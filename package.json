{"name": "clearmechanic.frontend", "version": "3.20", "private": true, "type": "module", "scripts": {"start": "vite", "build-uat": "tsc && vite build -c vite.config.uat.ts --mode uat", "build-local": "tsc && vite build -c vite.config.uat.ts --mode local", "build": "tsc && vite build -c vite.config.production.ts", "analyze": "npx vite-bundle-visualizer -c vite.config.production.ts", "serve": "vite preview -c vite.config.production.ts --port 3000", "check-updates": "ncu", "check-dependencies": "depcheck", "pretty": "prettier --write \"./**/*.{js,jsx,json,ts,tsx,css}\"", "postinstall": "node ./patch-react-grid-layout.cjs", "prepare": "node ./patch-react-grid-layout.cjs", "test": "vitest"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.16.14", "@mui/lab": "^5.0.0-alpha.172", "@mui/material": "^5.16.14", "@mui/styles": "^5.16.14", "@mui/x-date-pickers": "^6.19.3", "@react-hook/resize-observer": "^1.2.6", "@reduxjs/toolkit": "^1.8.2", "@sentry/react": "^8.54.0", "@tanstack/query-sync-storage-persister": "^4.24.10", "@tanstack/react-query": "^4.24.10", "@tanstack/react-query-devtools": "^4.24.12", "@tanstack/react-query-persist-client": "^4.24.10", "@tiptap/core": "^2.2.3", "@tiptap/extension-bold": "^2.2.2", "@tiptap/extension-bullet-list": "^2.2.2", "@tiptap/extension-character-count": "^2.2.2", "@tiptap/extension-code": "^2.2.2", "@tiptap/extension-code-block": "^2.2.2", "@tiptap/extension-document": "^2.2.2", "@tiptap/extension-heading": "^2.2.2", "@tiptap/extension-history": "^2.2.2", "@tiptap/extension-italic": "^2.2.2", "@tiptap/extension-link": "^2.2.2", "@tiptap/extension-list-item": "^2.2.2", "@tiptap/extension-ordered-list": "^2.2.2", "@tiptap/extension-paragraph": "^2.2.2", "@tiptap/extension-placeholder": "^2.2.2", "@tiptap/extension-strike": "^2.2.2", "@tiptap/extension-text": "^2.2.2", "@tiptap/extension-text-align": "^2.2.2", "@tiptap/extension-typography": "^2.2.2", "@tiptap/extension-underline": "^2.2.2", "@tiptap/pm": "^2.2.2", "@tiptap/react": "^2.2.2", "axios": "^1.6.7", "broadcast-channel": "^7.0.0", "clsx": "^1.2.1", "crypto-js": "^4.2.0", "date-fns": "^2.21.0", "framer-motion": "^4.0.0", "i18next": "^23.7.19", "interweave": "^13.1.0", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "luxon": "^3.4.4", "moment": "^2.30.1", "overlayscrollbars": "^2.8.1", "overlayscrollbars-react": "^0.5.6", "p-queue": "^7.4.1", "posthog-js": "^1.227.2", "prosemirror-state": "^1.4.3", "prosemirror-transform": "^1.8.0", "pubnub": "^7.6.0", "qs": "^6.11.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-full-screen": "^1.1.1", "react-google-recaptcha-v3": "^1.10.1", "react-grid-layout": "^1.4.4", "react-i18next": "^14.0.5", "react-lines-ellipsis": "^0.15.4", "react-loading-skeleton": "^3.4.0", "react-number-format": "^4.9.3", "react-redux": "^7.2.2", "react-resize-detector": "^7.1.2", "react-select": "^4.3.1", "react-text-mask": "^5.5.0", "react-transition-group": "^4.4.5", "react-virtuoso": "^4.6.3", "react-window": "^1.8.10", "redux": "^4.0.5", "redux-persist": "^6.0.0", "reselect": "^4.1.5", "simplebar-react": "^3.2.5", "use-async-memo": "^1.2.5", "use-debounce": "^8.0.4", "uuid": "^9.0.1", "webm-duration-fix": "^1.0.4", "zod": "^3.24.2"}, "eslintConfig": {"extends": ["react-app"], "rules": {"eqeqeq": "off", "no-template-curly-in-string": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@sentry/vite-plugin": "^3.1.2", "@types/crypto-js": "^4.2.2", "@types/luxon": "^3.4.2", "@types/preval.macro": "^3.0.2", "@types/pubnub": "^7.4.1", "@types/qs": "^6.9.11", "@types/react": "^18.2.55", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.19", "@types/react-grid-layout": "^1.3.5", "@types/react-lines-ellipsis": "^0.15.5", "@types/react-select": "^4.0.18", "@types/react-text-mask": "^5.4.14", "@types/react-window": "^1.8.8", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.7.0", "depcheck": "^1.4.7", "eslint": "^8.57.0", "eslint-config-react-app": "^7.0.1", "jsdom": "^25.0.0", "npm-check-updates": "^16.14.15", "prettier": "^2.8.8", "preval.macro": "^5.0.0", "react-router-dom": "^6.30.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-checker": "^0.6.4", "vite-plugin-html": "^3.2.2", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.1", "vitest": "^3.2.4"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.12.0"}}