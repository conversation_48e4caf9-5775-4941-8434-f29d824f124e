image: node:20

pipelines:
  pull-requests:
    '**':
      - step:
          name: Build and test
          caches:
            - node
          script:
            - npm install --force
            - npm run build
            - npm test
  branches:
    master:
      - step:
          name: Build and test
          caches:
            - node
          script:
            - npm install --force
            - npm run build
            - npm test

definitions:
  caches:
    node: ~/.npm
