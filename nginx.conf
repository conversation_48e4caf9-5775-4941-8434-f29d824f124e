map $cookie_sticky $set_cookie_sticky {
    default "";
    "" 'sticky=$STICKY_ID;Domain=$host;Path=/;Max-Age=30;SameSite=strict;Secure';
}

server {
    listen 80;
    listen [::]:80;
    server_name cmos_ui_2;
    server_tokens off;
    access_log off;
    gzip on;
    gzip_types application/javascript text/css;
    gzip_min_length 1000;

    root /usr/share/nginx/html; # keep that

    # Return 500 for api routes because if nginx gets a request with these paths then that means we have a problem (like ALB is misconfigured)
    location /api {
        default_type text/plain;
        return 500 "You are attempting to access Old API service via front-end server, is ALB/server configured wrong?\nIf you are a developer, please fix that.";
    }

    location /general-api {
        default_type text/plain;
        return 500 "You are attempting to access New API service via front-end server, is ALB/server configured wrong?\nIf you are a developer, please fix that.";
    }

    # Health-check
    location = /dashboard/__nginx_health/ready {
        default_type text/plain;
        return 200 "Healthy";
    }

    # Treat /dashboard as /
    location = /dashboard {
        rewrite /dashboard / last;
    }

    # Treat /dashboard/SOMETHING as /SOMETHING
    location ~ ^/dashboard/(.*) {
        rewrite ^/dashboard/(.*)$ /$1 last;
    }

    location ^~ /assets {
        add_header Set-Cookie $set_cookie_sticky;
        try_files $uri =404;
    }

    location / {
        # kill cache
        add_header Last-Modified $date_gmt;
        add_header Cache-Control 'no-store, no-cache';
        if_modified_since off;
        expires off;
        etag off;
        add_header Set-Cookie $set_cookie_sticky;
        try_files $uri $uri/ /index.html =404;
    }
}