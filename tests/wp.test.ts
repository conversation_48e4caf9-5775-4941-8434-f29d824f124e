import { DateTime } from 'luxon';
import moment from 'moment';
import { expect, test } from 'vitest';
import { UserScheduleDto } from '../src/api/common';
import { getRestrictedZones } from '../src/views/WorkshopPlanner/Planning';

test('getRestrictedZones: CMOS-3249 - problematic case', () => {
    const windows = [
        { from: '08:30:00', to: '13:00:00' },
        { from: '15:00:00', to: '18:30:00' },
    ];
    const schedule: UserScheduleDto = {
        monday: { windows },
        tuesday: { windows },
        wednesday: { windows },
        thursday: { windows },
        friday: { windows },
        saturday: { windows },
        sunday: { windows },
    };

    const zone = 'America/Monterrey';
    const from = DateTime.now()
        .setZone(zone)
        .set({ hour: 8, minute: 30, second: 0, millisecond: 0 });
    const to = from.set({ hour: 18, minute: 30, second: 0, millisecond: 0 });

    const zones = getRestrictedZones('any_value_here', schedule, 'America/Monterrey', from, to);

    expect(zones).toHaveLength(1);
    expect(zones[0].minutesDuration).toBe(120);

    const expectedRzStart = DateTime.now()
        .setZone(zone)
        .set({ hour: 13, minute: 0, second: 0, millisecond: 0 });
    expect(+moment(zones[0].timestamp).toDate()).toBe(expectedRzStart.toMillis());
});

test('getRestrictedZones: CMOS-3249 - temporary fix case', () => {
    const windows = [
        { from: '08:30:00', to: '13:00:00' },
        { from: '15:00:00', to: '18:30:00' },
    ];
    const schedule: UserScheduleDto = {
        monday: { windows },
        tuesday: { windows },
        wednesday: { windows },
        thursday: { windows },
        friday: { windows },
        saturday: { windows },
        sunday: { windows },
    };

    const zone = 'America/Monterrey';

    // NOTICE minute set to 0 in "from" - that is the difference with problematic case
    const from = DateTime.now()
        .setZone(zone)
        .set({ hour: 8, minute: 0, second: 0, millisecond: 0 });
    const to = from.set({ hour: 18, minute: 30, second: 0, millisecond: 0 });

    const zones = getRestrictedZones('any_value_here', schedule, 'America/Monterrey', from, to);

    expect(zones).toHaveLength(2);
    expect(zones[0].minutesDuration).toBe(30);
    expect(+moment(zones[0].timestamp).toDate()).toBe(from.toMillis());
    expect(zones[1].minutesDuration).toBe(120);
});
