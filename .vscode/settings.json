{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit", "source.sortMembers": "explicit"}, "prettier.configPath": ".prettierrc.json", "cSpell.words": ["Aftersales", "Appt", "clearmechanic", "clsx", "cmos", "datacontracts", "fontawesome", "luxon", "<PERSON><PERSON>", "NREUM", "Omnichannel", "persistor", "preval", "prose<PERSON><PERSON>r", "Prospection", "prospections", "Pubnub", "recaptcha", "reduxjs", "requestpasswordreset", "Roboto", "scrollbars", "<PERSON><PERSON><PERSON>", "Sendings", "SUBROUTES", "svgr", "tanstack", "tiptap", "UISYSTEM", "Validatable"], "cSpell.language": "en,es", "eslint.runtime": "node", "[typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.formatOnSave": false, "editor.insertSpaces": true, "editor.tabSize": 2, "editor.autoIndent": "keep", "diffEditor.ignoreTrimWhitespace": false, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}}}