{"requiresCompatibilities": ["FARGATE"], "family": "cmos-frontend-uat", "containerDefinitions": [{"name": "cmos-frontend-uat", "image": "078824135716.dkr.ecr.ca-central-1.amazonaws.com/cmos-services-frontend-uat", "cpu": 0, "links": [], "portMappings": [{"name": "cmos-frontend-uat-80-tcp", "containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [], "secrets": [], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/cmos-frontend-uat", "awslogs-region": "ca-central-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "healthCheck": {"command": ["CMD-SHELL", " curl --silent --fail localhost/dashboard/__nginx_health/ready"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 120}, "systemControls": []}], "volumes": [], "networkMode": "awsvpc", "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::078824135716:role/ecsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX"}, "tags": [{"key": "related_project", "value": "cmos"}]}