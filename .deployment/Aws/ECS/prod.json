{"requiresCompatibilities": ["FARGATE"], "family": "cmos-frontend-prod", "containerDefinitions": [{"name": "cmos-frontend-prod", "image": "078824135716.dkr.ecr.ca-central-1.amazonaws.com/cmos-services-frontend", "cpu": 0, "links": [], "portMappings": [{"name": "cmos-frontend-prod-80-tcp", "containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [], "secrets": [], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "healthCheck": {"command": ["CMD-SHELL", " curl --silent --fail localhost/dashboard/__nginx_health/ready"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 120}, "systemControls": []}], "volumes": [], "networkMode": "awsvpc", "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::078824135716:role/ecsTaskExecutionRole", "runtimePlatform": {"operatingSystemFamily": "LINUX"}, "tags": [{"key": "related_project", "value": "cmos"}]}