version: 0.2

phases:
    install:
        runtime-versions:
            nodejs: 20.11
        commands:
            - npm install -g npm@10.4.0
    pre_build:
        on-failure: ABORT
        commands:
            - ECR_HOST=078824135716.dkr.ecr.ca-central-1.amazonaws.com
            - REPOSITORY_URI=$ECR_HOST/cmos-services-frontend
            - IMAGE_TAG=latest
            - aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_HOST
            - aws ecr-public get-login-password --region us-east-1 | docker login --username AWS --password-stdin public.ecr.aws
            - npm install --force
    build:
        on-failure: ABORT
        commands:
            - npm run build
            - docker build -t $REPOSITORY_URI:latest -f ./Dockerfile .
    post_build:
        commands:
            - docker push $REPOSITORY_URI:latest
            # put image uri to imagedefinitions.json, it will be picked up by CodePipeline at deploy stage
            - printf '[{"name":"cmos-frontend-prod","imageUri":"%s"}]' $REPOSITORY_URI:latest > imagedefinitions.json
artifacts:
    files: imagedefinitions.json
