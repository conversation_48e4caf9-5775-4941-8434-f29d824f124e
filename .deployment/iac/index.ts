import * as dotenv from 'dotenv';
import * as pulumi from '@pulumi/pulumi';
import { createUATInfra } from './uat';
import { createPRODInfra } from './prod';

dotenv.config();

function createInfra() {
    const stack = pulumi.getStack();

    switch (stack) {
        case 'uat':
            return createUATInfra();
        case 'prod':
            return createPRODInfra();
        default:
            throw new Error('Unknown stack: ' + stack);
    }
}

const infra = createInfra();

export default infra;
