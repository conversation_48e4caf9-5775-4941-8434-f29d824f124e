import * as pulumi from '@pulumi/pulumi';

export const AWS_ENCRYPTION_KEY_ARN = 'arn:aws:kms:ca-central-1:078824135716:alias/aws/s3';

export const AWS_CODEBUILD_ROLE_ARN = 'arn:aws:iam::078824135716:role/cmos-services-code-builder';

export const AWS_ECR_HOST = '078824135716.dkr.ecr.ca-central-1.amazonaws.com';

export const AWS_CODEPIPELINE_ROLE_ARN =
    'arn:aws:iam::078824135716:role/service-role/cmos-codepipeline-role';

export const AWS_CODESTAR_CONNECTION_ARN =
    'arn:aws:codestar-connections:ca-central-1:078824135716:connection/68b2b93a-f42a-4ed6-b259-36251f3c200a';

export const BB_FRONTEND_REPOID = 'clearmechanic/clearmechanic.frontend';

export const BB_REPO_LOCATION =
    'https://<EMAIL>/clearmechanic/cmos.frontend.git';

export function requireEnv(env: string) {
    if (pulumi.getStack() !== env) {
        throw new Error(
            'Pulumi stack must be set to "' + env + '" when deploying environment "' + env + '"'
        );
    }
}
