{"version": 3, "checkpoint": {"stack": "organization/cmos.fe.iac/uat", "latest": {"manifest": {"time": "2025-07-30T18:19:04.5431261+10:00", "magic": "6a5f1cff8ef65523800fc5c9de7d17699a6e5b813cf7d2dab78054c0abf00cfc", "version": "v3.178.0"}, "secrets_providers": {"type": "passphrase", "state": {"salt": "v1:S+t6EPr/cVs=:v1:mn/XQeEIY1iRy3Ep:M1MbFhdeSnqGQz2RUtlavf2ByOiaEg=="}}, "resources": [{"urn": "urn:pulumi:uat::cmos.fe.iac::pulumi:pulumi:Stack::cmos.fe.iac-uat", "custom": false, "type": "pulumi:pulumi:<PERSON>ack", "outputs": {"default": {"codebuildProject": {"arn": "arn:aws:codebuild:ca-central-1:078824135716:project/iac-uat-frontend", "artifacts": {"artifactIdentifier": "", "bucketOwnerAccess": "", "encryptionDisabled": false, "location": "", "name": "iac-uat-frontend", "namespaceType": "", "overrideArtifactName": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}, "badgeEnabled": false, "badgeUrl": "", "buildBatchConfig": null, "buildTimeout": 60, "cache": {"location": "", "modes": null, "type": "NO_CACHE"}, "concurrentBuildLimit": 1, "description": "", "encryptionKey": "arn:aws:kms:ca-central-1:078824135716:alias/aws/s3", "environment": {"certificate": "", "computeType": "BUILD_GENERAL1_SMALL", "environmentVariables": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "ca-central-1"}, {"name": "AWS_ACCESS_KEY_ID", "type": "PARAMETER_STORE", "value": "/CodeBuild/cmos_services_deployer/access_key"}, {"name": "AWS_SECRET_ACCESS_KEY", "type": "PARAMETER_STORE", "value": "/CodeBuild/cmos_services_deployer/secret_key"}, {"name": "NODE_OPTIONS", "type": "PLAINTEXT", "value": "--max-old-space-size=6144"}], "fleet": null, "image": "aws/codebuild/standard:7.0", "imagePullCredentialsType": "CODEBUILD", "privilegedMode": true, "registryCredential": null, "type": "LINUX_CONTAINER"}, "fileSystemLocations": [], "id": "arn:aws:codebuild:ca-central-1:078824135716:project/iac-uat-frontend", "logsConfig": {"cloudwatchLogs": {"groupName": "", "status": "ENABLED", "streamName": ""}, "s3Logs": {"bucketOwnerAccess": "", "encryptionDisabled": false, "location": "", "status": "DISABLED"}}, "name": "iac-uat-frontend", "projectVisibility": "PRIVATE", "publicProjectAlias": "", "queuedTimeout": 480, "resourceAccessRole": "", "secondaryArtifacts": [], "secondarySourceVersions": [], "secondarySources": [], "serviceRole": "arn:aws:iam::078824135716:role/cmos-services-code-builder", "source": {"auth": null, "buildStatusConfig": null, "buildspec": "version: 0.2\nphases:\n  install:\n    runtime-versions:\n      nodejs: \"20.11\"\n    commands:\n      - npm install -g npm@10.4.0\n  pre_build:\n    on-failure: ABORT\n    commands:\n      - ECR_HOST=078824135716.dkr.ecr.ca-central-1.amazonaws.com\n      - REPOSITORY_URI=078824135716.dkr.ecr.ca-central-1.amazonaws.com/cmos-services-frontend-uat\n      - IMAGE_TAG=latest\n      - aws ecr get-login-password | docker login --username AWS\n        --password-stdin 078824135716.dkr.ecr.ca-central-1.amazonaws.com\n      - npm install --force\n  build:\n    on-failure: ABORT\n    commands:\n      - npm run build\n      - docker build -t $REPOSITORY_URI:$IMAGE_TAG -f ./Dockerfile .\n  post_build:\n    commands:\n      - docker push $REPOSITORY_URI:$IMAGE_TAG\n      - printf\n        '[{\"name\":\"cmos-frontend-uat\",\"imageUri\":\"$REPOSITORY_URI:$IMAGE_TAG\"}]'\n        > imagedefinitions.json\nartifacts:\n  files:\n    - imagedefinitions.json\n", "gitCloneDepth": 0, "gitSubmodulesConfig": null, "insecureSsl": false, "location": "", "reportBuildStatus": false, "type": "CODEPIPELINE"}, "sourceVersion": "", "tags": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "tagsAll": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "urn": "urn:pulumi:uat::cmos.fe.iac::aws:codebuild/project:Project::iac-uat-frontend", "vpcConfig": null}, "logGroup": {"arn": "arn:aws:logs:ca-central-1:078824135716:log-group:/aws/codebuild/iac-uat-frontend", "id": "/aws/codebuild/iac-uat-frontend", "kmsKeyId": "", "logGroupClass": "STANDARD", "name": "/aws/codebuild/iac-uat-frontend", "namePrefix": "", "retentionInDays": 3, "skipDestroy": false, "tags": {"related_codebuild_project": "iac-uat-frontend"}, "tagsAll": {"related_codebuild_project": "iac-uat-frontend"}, "urn": "urn:pulumi:uat::cmos.fe.iac::aws:cloudwatch/logGroup:LogGroup::loggroup./aws/codebuild/iac-uat-frontend"}, "pipeline": {"arn": "arn:aws:codepipeline:ca-central-1:078824135716:iac-uat-frontend", "artifactStores": [{"encryptionKey": null, "location": "codepipeline-ca-central-1-770400812731", "region": "", "type": "S3"}], "executionMode": "SUPERSEDED", "id": "iac-uat-frontend", "name": "iac-uat-frontend", "pipelineType": "V2", "roleArn": "arn:aws:iam::078824135716:role/service-role/cmos-codepipeline-role", "stages": [{"actions": [{"category": "Source", "configuration": {"BranchName": "master", "ConnectionArn": "arn:aws:codestar-connections:ca-central-1:078824135716:connection/68b2b93a-f42a-4ed6-b259-36251f3c200a", "DetectChanges": "true", "FullRepositoryId": "clearmechanic/clearmechanic.frontend", "OutputArtifactFormat": "CODEBUILD_CLONE_REF"}, "inputArtifacts": null, "name": "Source", "namespace": "", "outputArtifacts": ["SourceArtifact"], "owner": "AWS", "provider": "CodeStarSourceConnection", "region": "", "roleArn": "", "runOrder": 1, "timeoutInMinutes": 0, "version": "1"}], "beforeEntry": null, "name": "Source", "onFailure": null, "onSuccess": null}, {"actions": [{"category": "Build", "configuration": {"ProjectName": "iac-uat-frontend"}, "inputArtifacts": ["SourceArtifact"], "name": "Build", "namespace": "", "outputArtifacts": ["BuildOutput"], "owner": "AWS", "provider": "CodeBuild", "region": "", "roleArn": "", "runOrder": 1, "timeoutInMinutes": 0, "version": "1"}], "beforeEntry": null, "name": "Build", "onFailure": null, "onSuccess": null}, {"actions": [{"category": "Deploy", "configuration": {"ClusterName": "cmos-services-uat", "FileName": "imagedefinitions.json", "ServiceName": "cmos-frontend"}, "inputArtifacts": ["BuildOutput"], "name": "Deploy", "namespace": "", "outputArtifacts": null, "owner": "AWS", "provider": "ECS", "region": "", "roleArn": "", "runOrder": 1, "timeoutInMinutes": 0, "version": "1"}], "beforeEntry": null, "name": "Deploy", "onFailure": null, "onSuccess": null}], "tags": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "tagsAll": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "triggerAlls": [{"gitConfigurations": [{"pullRequests": [], "pushes": [{"branches": [{"excludes": [], "includes": ["master"]}], "filePaths": [], "tags": []}], "sourceActionName": "Source"}], "providerType": "CodeStarSourceConnection"}], "triggers": [], "urn": "urn:pulumi:uat::cmos.fe.iac::aws:codepipeline/pipeline:Pipeline::iac-uat-frontend", "variables": []}}}, "created": "2025-07-30T08:18:56.6941614Z", "modified": "2025-07-30T08:18:56.6941614Z"}, {"urn": "urn:pulumi:uat::cmos.fe.iac::pulumi:providers:aws::default_6_83_0", "custom": true, "id": "99c08077-56f7-4fef-bb01-179de1a44caa", "type": "pulumi:providers:aws", "inputs": {"__internal": {}, "region": "ca-central-1", "skipCredentialsValidation": "false", "skipRegionValidation": "true", "version": "6.83.0"}, "outputs": {"region": "ca-central-1", "skipCredentialsValidation": "false", "skipRegionValidation": "true", "version": "6.83.0"}, "created": "2025-07-30T08:18:59.963262Z", "modified": "2025-07-30T08:18:59.963262Z"}, {"urn": "urn:pulumi:uat::cmos.fe.iac::aws:codebuild/project:Project::iac-uat-frontend", "custom": true, "id": "arn:aws:codebuild:ca-central-1:078824135716:project/iac-uat-frontend", "type": "aws:codebuild/project:Project", "inputs": {"__defaults": ["badgeEnabled", "buildTimeout", "queuedTimeout"], "artifacts": {"__defaults": ["encryptionDisabled", "overrideArtifactName"], "encryptionDisabled": false, "overrideArtifactName": false, "type": "CODEPIPELINE"}, "badgeEnabled": false, "buildTimeout": 60, "cache": {"__defaults": [], "type": "NO_CACHE"}, "concurrentBuildLimit": 1, "encryptionKey": "arn:aws:kms:ca-central-1:078824135716:alias/aws/s3", "environment": {"__defaults": ["imagePullCredentialsType"], "computeType": "BUILD_GENERAL1_SMALL", "environmentVariables": [{"__defaults": [], "name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "ca-central-1"}, {"__defaults": [], "name": "AWS_ACCESS_KEY_ID", "type": "PARAMETER_STORE", "value": "/CodeBuild/cmos_services_deployer/access_key"}, {"__defaults": [], "name": "AWS_SECRET_ACCESS_KEY", "type": "PARAMETER_STORE", "value": "/CodeBuild/cmos_services_deployer/secret_key"}, {"__defaults": [], "name": "NODE_OPTIONS", "type": "PLAINTEXT", "value": "--max-old-space-size=6144"}], "image": "aws/codebuild/standard:7.0", "imagePullCredentialsType": "CODEBUILD", "privilegedMode": true, "type": "LINUX_CONTAINER"}, "logsConfig": {"__defaults": [], "cloudwatchLogs": {"__defaults": [], "status": "ENABLED"}, "s3Logs": {"__defaults": ["encryptionDisabled"], "encryptionDisabled": false, "status": "DISABLED"}}, "name": "iac-uat-frontend", "projectVisibility": "PRIVATE", "queuedTimeout": 480, "serviceRole": "arn:aws:iam::078824135716:role/cmos-services-code-builder", "source": {"__defaults": [], "buildspec": "version: 0.2\nphases:\n  install:\n    runtime-versions:\n      nodejs: \"20.11\"\n    commands:\n      - npm install -g npm@10.4.0\n  pre_build:\n    on-failure: ABORT\n    commands:\n      - ECR_HOST=078824135716.dkr.ecr.ca-central-1.amazonaws.com\n      - REPOSITORY_URI=078824135716.dkr.ecr.ca-central-1.amazonaws.com/cmos-services-frontend-uat\n      - IMAGE_TAG=latest\n      - aws ecr get-login-password | docker login --username AWS\n        --password-stdin 078824135716.dkr.ecr.ca-central-1.amazonaws.com\n      - npm install --force\n  build:\n    on-failure: ABORT\n    commands:\n      - npm run build\n      - docker build -t $REPOSITORY_URI:$IMAGE_TAG -f ./Dockerfile .\n  post_build:\n    commands:\n      - docker push $REPOSITORY_URI:$IMAGE_TAG\n      - printf\n        '[{\"name\":\"cmos-frontend-uat\",\"imageUri\":\"$REPOSITORY_URI:$IMAGE_TAG\"}]'\n        > imagedefinitions.json\nartifacts:\n  files:\n    - imagedefinitions.json\n", "type": "CODEPIPELINE"}, "tags": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "tagsAll": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}}, "outputs": {"arn": "arn:aws:codebuild:ca-central-1:078824135716:project/iac-uat-frontend", "artifacts": {"artifactIdentifier": "", "bucketOwnerAccess": "", "encryptionDisabled": false, "location": "", "name": "iac-uat-frontend", "namespaceType": "", "overrideArtifactName": false, "packaging": "NONE", "path": "", "type": "CODEPIPELINE"}, "badgeEnabled": false, "badgeUrl": "", "buildBatchConfig": null, "buildTimeout": 60, "cache": {"location": "", "modes": null, "type": "NO_CACHE"}, "concurrentBuildLimit": 1, "description": "", "encryptionKey": "arn:aws:kms:ca-central-1:078824135716:alias/aws/s3", "environment": {"certificate": "", "computeType": "BUILD_GENERAL1_SMALL", "environmentVariables": [{"name": "AWS_DEFAULT_REGION", "type": "PLAINTEXT", "value": "ca-central-1"}, {"name": "AWS_ACCESS_KEY_ID", "type": "PARAMETER_STORE", "value": "/CodeBuild/cmos_services_deployer/access_key"}, {"name": "AWS_SECRET_ACCESS_KEY", "type": "PARAMETER_STORE", "value": "/CodeBuild/cmos_services_deployer/secret_key"}, {"name": "NODE_OPTIONS", "type": "PLAINTEXT", "value": "--max-old-space-size=6144"}], "fleet": null, "image": "aws/codebuild/standard:7.0", "imagePullCredentialsType": "CODEBUILD", "privilegedMode": true, "registryCredential": null, "type": "LINUX_CONTAINER"}, "fileSystemLocations": [], "id": "arn:aws:codebuild:ca-central-1:078824135716:project/iac-uat-frontend", "logsConfig": {"cloudwatchLogs": {"groupName": "", "status": "ENABLED", "streamName": ""}, "s3Logs": {"bucketOwnerAccess": "", "encryptionDisabled": false, "location": "", "status": "DISABLED"}}, "name": "iac-uat-frontend", "projectVisibility": "PRIVATE", "publicProjectAlias": "", "queuedTimeout": 480, "resourceAccessRole": "", "secondaryArtifacts": [], "secondarySourceVersions": [], "secondarySources": [], "serviceRole": "arn:aws:iam::078824135716:role/cmos-services-code-builder", "source": {"auth": null, "buildStatusConfig": null, "buildspec": "version: 0.2\nphases:\n  install:\n    runtime-versions:\n      nodejs: \"20.11\"\n    commands:\n      - npm install -g npm@10.4.0\n  pre_build:\n    on-failure: ABORT\n    commands:\n      - ECR_HOST=078824135716.dkr.ecr.ca-central-1.amazonaws.com\n      - REPOSITORY_URI=078824135716.dkr.ecr.ca-central-1.amazonaws.com/cmos-services-frontend-uat\n      - IMAGE_TAG=latest\n      - aws ecr get-login-password | docker login --username AWS\n        --password-stdin 078824135716.dkr.ecr.ca-central-1.amazonaws.com\n      - npm install --force\n  build:\n    on-failure: ABORT\n    commands:\n      - npm run build\n      - docker build -t $REPOSITORY_URI:$IMAGE_TAG -f ./Dockerfile .\n  post_build:\n    commands:\n      - docker push $REPOSITORY_URI:$IMAGE_TAG\n      - printf\n        '[{\"name\":\"cmos-frontend-uat\",\"imageUri\":\"$REPOSITORY_URI:$IMAGE_TAG\"}]'\n        > imagedefinitions.json\nartifacts:\n  files:\n    - imagedefinitions.json\n", "gitCloneDepth": 0, "gitSubmodulesConfig": null, "insecureSsl": false, "location": "", "reportBuildStatus": false, "type": "CODEPIPELINE"}, "sourceVersion": "", "tags": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "tagsAll": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "vpcConfig": null}, "parent": "urn:pulumi:uat::cmos.fe.iac::pulumi:pulumi:Stack::cmos.fe.iac-uat", "provider": "urn:pulumi:uat::cmos.fe.iac::pulumi:providers:aws::default_6_83_0::99c08077-56f7-4fef-bb01-179de1a44caa", "propertyDependencies": {"artifacts": [], "cache": [], "concurrentBuildLimit": [], "encryptionKey": [], "environment": [], "logsConfig": [], "name": [], "projectVisibility": [], "serviceRole": [], "source": [], "tags": []}, "created": "2025-07-30T08:19:02.4454317Z", "modified": "2025-07-30T08:19:02.4454317Z"}, {"urn": "urn:pulumi:uat::cmos.fe.iac::aws:cloudwatch/logGroup:LogGroup::loggroup./aws/codebuild/iac-uat-frontend", "custom": true, "id": "/aws/codebuild/iac-uat-frontend", "type": "aws:cloudwatch/logGroup:LogGroup", "inputs": {"__defaults": ["<PERSON><PERSON><PERSON><PERSON>"], "name": "/aws/codebuild/iac-uat-frontend", "retentionInDays": 3, "skipDestroy": false, "tags": {"related_codebuild_project": "iac-uat-frontend"}, "tagsAll": {"related_codebuild_project": "iac-uat-frontend"}}, "outputs": {"arn": "arn:aws:logs:ca-central-1:078824135716:log-group:/aws/codebuild/iac-uat-frontend", "id": "/aws/codebuild/iac-uat-frontend", "kmsKeyId": "", "logGroupClass": "STANDARD", "name": "/aws/codebuild/iac-uat-frontend", "namePrefix": "", "retentionInDays": 3, "skipDestroy": false, "tags": {"related_codebuild_project": "iac-uat-frontend"}, "tagsAll": {"related_codebuild_project": "iac-uat-frontend"}}, "parent": "urn:pulumi:uat::cmos.fe.iac::pulumi:pulumi:Stack::cmos.fe.iac-uat", "provider": "urn:pulumi:uat::cmos.fe.iac::pulumi:providers:aws::default_6_83_0::99c08077-56f7-4fef-bb01-179de1a44caa", "propertyDependencies": {"name": [], "retentionInDays": [], "tags": []}, "created": "2025-07-30T08:19:02.8088336Z", "modified": "2025-07-30T08:19:02.8088336Z"}, {"urn": "urn:pulumi:uat::cmos.fe.iac::aws:codepipeline/pipeline:Pipeline::iac-uat-frontend", "custom": true, "id": "iac-uat-frontend", "type": "aws:codepipeline/pipeline:Pipeline", "inputs": {"__defaults": [], "artifactStores": [{"__defaults": [], "location": "codepipeline-ca-central-1-770400812731", "type": "S3"}], "executionMode": "SUPERSEDED", "name": "iac-uat-frontend", "pipelineType": "V2", "roleArn": "arn:aws:iam::078824135716:role/service-role/cmos-codepipeline-role", "stages": [{"__defaults": [], "actions": [{"__defaults": [], "category": "Source", "configuration": {"BranchName": "master", "ConnectionArn": "arn:aws:codestar-connections:ca-central-1:078824135716:connection/68b2b93a-f42a-4ed6-b259-36251f3c200a", "DetectChanges": "true", "FullRepositoryId": "clearmechanic/clearmechanic.frontend", "OutputArtifactFormat": "CODEBUILD_CLONE_REF"}, "name": "Source", "outputArtifacts": ["SourceArtifact"], "owner": "AWS", "provider": "CodeStarSourceConnection", "version": "1"}], "name": "Source"}, {"__defaults": [], "actions": [{"__defaults": [], "category": "Build", "configuration": {"ProjectName": "iac-uat-frontend"}, "inputArtifacts": ["SourceArtifact"], "name": "Build", "outputArtifacts": ["BuildOutput"], "owner": "AWS", "provider": "CodeBuild", "version": "1"}], "name": "Build"}, {"__defaults": [], "actions": [{"__defaults": [], "category": "Deploy", "configuration": {"ClusterName": "cmos-services-uat", "FileName": "imagedefinitions.json", "ServiceName": "cmos-frontend"}, "inputArtifacts": ["BuildOutput"], "name": "Deploy", "owner": "AWS", "provider": "ECS", "version": "1"}], "name": "Deploy"}], "tags": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "tagsAll": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}}, "outputs": {"arn": "arn:aws:codepipeline:ca-central-1:078824135716:iac-uat-frontend", "artifactStores": [{"encryptionKey": null, "location": "codepipeline-ca-central-1-770400812731", "region": "", "type": "S3"}], "executionMode": "SUPERSEDED", "id": "iac-uat-frontend", "name": "iac-uat-frontend", "pipelineType": "V2", "roleArn": "arn:aws:iam::078824135716:role/service-role/cmos-codepipeline-role", "stages": [{"actions": [{"category": "Source", "configuration": {"BranchName": "master", "ConnectionArn": "arn:aws:codestar-connections:ca-central-1:078824135716:connection/68b2b93a-f42a-4ed6-b259-36251f3c200a", "DetectChanges": "true", "FullRepositoryId": "clearmechanic/clearmechanic.frontend", "OutputArtifactFormat": "CODEBUILD_CLONE_REF"}, "inputArtifacts": null, "name": "Source", "namespace": "", "outputArtifacts": ["SourceArtifact"], "owner": "AWS", "provider": "CodeStarSourceConnection", "region": "", "roleArn": "", "runOrder": 1, "timeoutInMinutes": 0, "version": "1"}], "beforeEntry": null, "name": "Source", "onFailure": null, "onSuccess": null}, {"actions": [{"category": "Build", "configuration": {"ProjectName": "iac-uat-frontend"}, "inputArtifacts": ["SourceArtifact"], "name": "Build", "namespace": "", "outputArtifacts": ["BuildOutput"], "owner": "AWS", "provider": "CodeBuild", "region": "", "roleArn": "", "runOrder": 1, "timeoutInMinutes": 0, "version": "1"}], "beforeEntry": null, "name": "Build", "onFailure": null, "onSuccess": null}, {"actions": [{"category": "Deploy", "configuration": {"ClusterName": "cmos-services-uat", "FileName": "imagedefinitions.json", "ServiceName": "cmos-frontend"}, "inputArtifacts": ["BuildOutput"], "name": "Deploy", "namespace": "", "outputArtifacts": null, "owner": "AWS", "provider": "ECS", "region": "", "roleArn": "", "runOrder": 1, "timeoutInMinutes": 0, "version": "1"}], "beforeEntry": null, "name": "Deploy", "onFailure": null, "onSuccess": null}], "tags": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "tagsAll": {"cmos_env": "uat", "pulumi": "true", "related_project": "cmos"}, "triggerAlls": [{"gitConfigurations": [{"pullRequests": [], "pushes": [{"branches": [{"excludes": [], "includes": ["master"]}], "filePaths": [], "tags": []}], "sourceActionName": "Source"}], "providerType": "CodeStarSourceConnection"}], "triggers": [], "variables": []}, "parent": "urn:pulumi:uat::cmos.fe.iac::pulumi:pulumi:Stack::cmos.fe.iac-uat", "dependencies": ["urn:pulumi:uat::cmos.fe.iac::aws:codebuild/project:Project::iac-uat-frontend"], "provider": "urn:pulumi:uat::cmos.fe.iac::pulumi:providers:aws::default_6_83_0::99c08077-56f7-4fef-bb01-179de1a44caa", "propertyDependencies": {"artifactStores": [], "executionMode": [], "name": [], "pipelineType": [], "roleArn": [], "stages": ["urn:pulumi:uat::cmos.fe.iac::aws:codebuild/project:Project::iac-uat-frontend"], "tags": []}, "created": "2025-07-30T08:19:04.4756053Z", "modified": "2025-07-30T08:19:04.4756053Z"}], "metadata": {}}}}