{"kind": "update", "startTime": 1753951922, "message": "Add gitCloneDepth to CodePipeline configuration and update UAT attributes", "environment": {"exec.kind": "cli", "git.author": "<PERSON><PERSON>", "git.author.email": "<EMAIL>", "git.committer": "<PERSON><PERSON>", "git.committer.email": "<EMAIL>", "git.dirty": "false", "git.head": "81fe88c3d36e36a1edc1e223568d222a45a3b494", "git.headName": "refs/heads/master", "pulumi.arch": "amd64", "pulumi.env.PULUMI_CONFIG_PASSPHRASE": "set", "pulumi.os": "windows", "pulumi.version": "v3.178.0", "stack.environments": "[]", "updatePlan": "false", "vcs.kind": "bitbucket.org", "vcs.owner": "clearmechanic", "vcs.repo": "clearmechanic.frontend", "vcs.root": ".deployment/iac"}, "config": {"aws:region": "ca-central-1", "pulumi:tags": {"pulumi:template": "aws-typescript"}}, "version": 0, "result": "succeeded", "endTime": 1753951930, "resourceChanges": {"same": 3, "update": 1}}