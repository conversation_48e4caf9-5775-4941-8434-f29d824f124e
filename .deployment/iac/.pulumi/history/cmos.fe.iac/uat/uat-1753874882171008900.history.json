{"kind": "update", "startTime": 1753874873, "message": "Merged in feature/CMOS-4568 (pull request #2298)", "environment": {"exec.kind": "cli", "git.author": "<PERSON><PERSON>", "git.author.email": "<EMAIL>", "git.committer": "<PERSON><PERSON>", "git.committer.email": "<EMAIL>", "git.dirty": "true", "git.head": "c1a095372e4c1c639af2a9cd317030a832da5331", "git.headName": "refs/heads/master", "pulumi.arch": "amd64", "pulumi.env.PULUMI_CONFIG_PASSPHRASE": "set", "pulumi.os": "windows", "pulumi.version": "v3.178.0", "stack.environments": "[]", "updatePlan": "false", "vcs.kind": "bitbucket.org", "vcs.owner": "clearmechanic", "vcs.repo": "clearmechanic.frontend", "vcs.root": ".deployment/iac"}, "config": {"aws:region": "ca-central-1", "pulumi:tags": {"pulumi:template": "aws-typescript"}}, "version": 0, "result": "succeeded", "endTime": 1753874882, "resourceChanges": {"create": 2, "same": 2}}