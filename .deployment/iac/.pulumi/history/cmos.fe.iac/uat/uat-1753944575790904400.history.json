{"kind": "update", "startTime": 1753944568, "message": "revert package and lock", "environment": {"exec.kind": "cli", "git.author": "<PERSON><PERSON>", "git.author.email": "<EMAIL>", "git.committer": "<PERSON><PERSON>", "git.committer.email": "<EMAIL>", "git.dirty": "true", "git.head": "70be57b2c7923a509dda07446b1d4f26a79b2211", "git.headName": "refs/heads/master", "pulumi.arch": "amd64", "pulumi.env.PULUMI_CONFIG_PASSPHRASE": "set", "pulumi.os": "windows", "pulumi.version": "v3.178.0", "stack.environments": "[]", "updatePlan": "false", "vcs.kind": "bitbucket.org", "vcs.owner": "clearmechanic", "vcs.repo": "clearmechanic.frontend", "vcs.root": ".deployment/iac"}, "config": {"aws:region": "ca-central-1", "pulumi:tags": {"pulumi:template": "aws-typescript"}}, "version": 0, "result": "succeeded", "endTime": 1753944575, "resourceChanges": {"same": 3, "update": 1}}