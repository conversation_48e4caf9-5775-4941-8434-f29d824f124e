{"kind": "update", "startTime": 1753863535, "message": "CMOS-4568: review fixes", "environment": {"exec.kind": "cli", "git.author": "<PERSON><PERSON>", "git.author.email": "<EMAIL>", "git.committer": "<PERSON><PERSON>", "git.committer.email": "<EMAIL>", "git.dirty": "true", "git.head": "722d60f6d2d4bc122c32c05e2a554319fcd7eabf", "git.headName": "refs/heads/feature/CMOS-4568", "pulumi.arch": "amd64", "pulumi.env.PULUMI_CONFIG_PASSPHRASE": "set", "pulumi.os": "windows", "pulumi.version": "v3.178.0", "stack.environments": "[]", "updatePlan": "false", "vcs.kind": "bitbucket.org", "vcs.owner": "clearmechanic", "vcs.repo": "clearmechanic.frontend", "vcs.root": ".deployment/iac"}, "config": {"aws:region": "ca-central-1", "pulumi:tags": {"pulumi:template": "aws-typescript"}}, "version": 0, "result": "succeeded", "endTime": 1753863544, "resourceChanges": {"create": 4}}