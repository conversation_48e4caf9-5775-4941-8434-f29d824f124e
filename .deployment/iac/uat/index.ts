import { requireEnv } from '../config';
import { createInfrastructure } from '../builder/createInfrastructure';

export function createUATInfra() {
    requireEnv('uat');

    return createInfrastructure({
        env: 'uat',
        branchName: 'master',
        codeBuildProjectName: 'iac-uat-frontend',
        pipelineName: 'iac-uat-frontend',
        clusterName: 'cmos-services-uat',
        serviceName: 'cmos-frontend',
        ecrRepo: 'cmos-services-frontend-uat',
        ecsContainerName: 'cmos-frontend-uat',
        buildCommand: 'npm run build-uat',
    });
}
