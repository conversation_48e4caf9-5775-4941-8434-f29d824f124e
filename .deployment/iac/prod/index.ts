import { requireEnv } from '../config';
import { createInfrastructure } from '../builder/createInfrastructure';

export function createPRODInfra() {
    requireEnv('prod');

    return createInfrastructure({
        env: 'prod',
        branchName: 'prod',
        codeBuildProjectName: 'iac-prod-frontend',
        pipelineName: 'iac-prod-frontend',
        clusterName: 'cmos-services-prod',
        serviceName: 'cmos-frontend',
        ecrRepo: 'cmos-services-frontend',
        ecsContainerName: 'cmos-frontend-prod',
        buildCommand: 'npm run build',
    });
}
