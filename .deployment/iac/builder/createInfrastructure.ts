import * as aws from '@pulumi/aws';
import {
    AWS_CODEBUILD_ROLE_ARN,
    AWS_CODEPIPELINE_ROLE_ARN,
    AWS_CODESTAR_CONNECTION_ARN,
    AWS_ECR_HOST,
    BB_FRONTEND_REPOID,
    AWS_ENCRYPTION_KEY_ARN,
} from '../config';
import { createBuildSpec } from './buildspec';
import { Output } from '@pulumi/pulumi/output';

export type InfrastructureConfig = {
    env: 'uat' | 'prod';
    branchName: 'master' | 'prod';
    codeBuildProjectName: string;
    pipelineName: string;
    clusterName: string;
    serviceName: string;
    ecrRepo: string;
    ecsContainerName: string;
    buildCommand?: string;
};

export function createInfrastructure(config: InfrastructureConfig) {
    const {
        env,
        branchName,
        codeBuildProjectName,
        pipelineName,
        clusterName,
        serviceName,
        ecrRepo,
        ecsContainerName,
        buildCommand,
    } = config;

    const logGroupName = `/aws/codebuild/${codeBuildProjectName}`;
    const logGroup = new aws.cloudwatch.LogGroup(`loggroup.${logGroupName}`, {
        name: logGroupName,
        retentionInDays: 3,
        tags: {
            related_codebuild_project: codeBuildProjectName,
        },
    });

    const buildspec = createBuildSpec({
        ecrHost: AWS_ECR_HOST,
        ecrRepo,
        dockerfile: './Dockerfile',
        ecsContainerName,
        buildCommand,
    });

    const codebuildProject = createCodeBuildProject(codeBuildProjectName, env, buildspec);

    const pipeline = createCodePipeline({
        pipelineName,
        branchName,
        codebuildProjectName: codebuildProject.name,
        clusterName,
        serviceName,
        env,
    });

    return {
        codebuildProject,
        logGroup,
        pipeline,
    };
}

function createCodeBuildProject(projectName: string, env: string, buildspec: string) {
    return new aws.codebuild.Project(projectName, {
        name: projectName,
        serviceRole: AWS_CODEBUILD_ROLE_ARN,
        artifacts: {
            type: 'CODEPIPELINE',
        },
        cache: {
            type: 'NO_CACHE',
            modes: [],
        },
        concurrentBuildLimit: 1,
        projectVisibility: 'PRIVATE',
        environment: {
            computeType: 'BUILD_GENERAL1_MEDIUM',
            image: 'aws/codebuild/standard:7.0',
            type: 'LINUX_CONTAINER',
            privilegedMode: true,
            environmentVariables: [
                {
                    name: 'AWS_DEFAULT_REGION',
                    value: 'ca-central-1',
                    type: 'PLAINTEXT',
                },
                {
                    name: 'AWS_ACCESS_KEY_ID',
                    type: 'PARAMETER_STORE',
                    value: '/CodeBuild/cmos_services_deployer/access_key',
                },
                {
                    name: 'AWS_SECRET_ACCESS_KEY',
                    type: 'PARAMETER_STORE',
                    value: '/CodeBuild/cmos_services_deployer/secret_key',
                },
                {
                    name: 'NODE_OPTIONS',
                    value: '--max-old-space-size=6144',
                    type: 'PLAINTEXT',
                },
            ],
        },
        source: {
            type: 'CODEPIPELINE',
            buildspec,
        },
        encryptionKey: AWS_ENCRYPTION_KEY_ARN,
        logsConfig: {
            cloudwatchLogs: {
                status: 'ENABLED',
            },
            s3Logs: {
                status: 'DISABLED',
            },
        },
        tags: {
            related_project: 'cmos',
            cmos_env: env,
            pulumi: 'true',
        },
    });
}

function createCodePipeline(params: {
    pipelineName: string;
    branchName: string;
    codebuildProjectName: Output<string>;
    clusterName: string;
    serviceName: string;
    env: string;
}) {
    const { pipelineName, branchName, codebuildProjectName, clusterName, serviceName, env } =
        params;

    return new aws.codepipeline.Pipeline(pipelineName, {
        name: pipelineName,
        roleArn: AWS_CODEPIPELINE_ROLE_ARN,
        pipelineType: 'V2',
        executionMode: 'SUPERSEDED',
        artifactStores: [
            {
                location: 'codepipeline-ca-central-1-770400812731',
                type: 'S3',
            },
        ],
        stages: [
            {
                name: 'Source',
                actions: [
                    {
                        name: 'Source',
                        category: 'Source',
                        owner: 'AWS',
                        provider: 'CodeStarSourceConnection',
                        version: '1',
                        outputArtifacts: ['SourceArtifact'],
                        configuration: {
                            ConnectionArn: AWS_CODESTAR_CONNECTION_ARN,
                            FullRepositoryId: BB_FRONTEND_REPOID,
                            BranchName: branchName,
                            OutputArtifactFormat: 'CODEPIPELINE_DEFAULT',
                            DetectChanges: 'true',
                        },
                    },
                ],
            },
            {
                name: 'Build',
                actions: [
                    {
                        name: 'Build',
                        category: 'Build',
                        owner: 'AWS',
                        provider: 'CodeBuild',
                        version: '1',
                        inputArtifacts: ['SourceArtifact'],
                        outputArtifacts: ['BuildOutput'],
                        configuration: {
                            ProjectName: codebuildProjectName,
                        },
                    },
                ],
            },
            {
                name: 'Deploy',
                actions: [
                    {
                        name: 'Deploy',
                        category: 'Deploy',
                        owner: 'AWS',
                        provider: 'ECS',
                        version: '1',
                        inputArtifacts: ['BuildOutput'],
                        configuration: {
                            ClusterName: clusterName,
                            ServiceName: serviceName,
                            FileName: 'imagedefinitions.json',
                        },
                    },
                ],
            },
        ],
        tags: {
            related_project: 'cmos',
            cmos_env: env,
            pulumi: 'true',
        },
    });
}
