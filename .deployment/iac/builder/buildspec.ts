import * as YAML from 'yaml';

export type BuildspecOptions = {
    ecrHost: string;
    ecrRepo: string;
    dockerfile: string;
    ecsContainerName: string;
    buildCommand?: string;
};

export type Buildspec = {
    version: 0.2;
    'run-as'?: string;
    phases: Partial<Record<PhaseName, Phase>>;
    artifacts?: {
        files?: string | string[];
    };
    cache?: {
        paths?: string[];
    };
};

export type PhaseName = 'install' | 'pre_build' | 'build' | 'post_build';

export type OnFailure = 'ABORT' | 'CONTINUE' | 'RETRY' | `RETRY-${number}`;

export type Phase = {
    'run-as'?: string;
    'on-failure'?: OnFailure;
    'runtime-versions'?: Record<string, string>;
    commands: string[];
    finally?: string[];
};

export function buildspecToString(buildspec: Buildspec): string {
    return YAML.stringify(buildspec, { indent: 2 });
}

export function createBuildSpec({
    ecrHost,
    ecrRepo,
    dockerfile,
    ecsContainerName,
    buildCommand = 'npm run build',
}: BuildspecOptions): string {
    const buildspec: Buildspec = {
        version: 0.2,
        phases: {
            install: {
                'runtime-versions': {
                    nodejs: '20.11',
                },
                commands: ['npm install -g npm@10.4.0'],
            },
            pre_build: {
                'on-failure': 'ABORT',
                commands: [
                    `ECR_HOST=${ecrHost}`,
                    `REPOSITORY_URI=${ecrHost}/${ecrRepo}`,
                    `IMAGE_TAG=latest`,
                    `aws ecr get-login-password | docker login --username AWS --password-stdin ${ecrHost}`,
                    `npm install --force`,
                ],
            },
            build: {
                'on-failure': 'ABORT',
                commands: [
                    buildCommand,
                    `docker build -t $REPOSITORY_URI:$IMAGE_TAG -f ${dockerfile} .`,
                ],
            },
            post_build: {
                commands: [
                    `IMAGE_URI=$REPOSITORY_URI:$IMAGE_TAG`,
                    `docker push $IMAGE_URI`,
                    `echo "[{\\"name\\": \\"${ecsContainerName}\\", \\"imageUri\\": \\"$IMAGE_URI\\"}]" > imagedefinitions.json`,
                ],
            },
        },
        artifacts: {
            files: ['imagedefinitions.json'],
        },
    };

    return `${buildspecToString(buildspec)}`;
}
