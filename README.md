# Summary

That's a react-based frontend part for redesigned CMOS application.

# Development Rules

-   Build of source code from 'master' should contain no errors _(another words it should always be successful)_
-   Build of source code from 'master' should contain no warnings
    -   ![example](/readme_assets/build-warnings.png)
-   Using of just developed features should not lead to situation when dev console is full of errors, including React runtime warnings
    -   ![example](/readme_assets/console-warnings.png)
-   Pull requests using is highly desirable

# Technical Stack

Full list of used instruments and technologies:

-   Package manager - [NPM](https://www.npmjs.com/)
-   Bootstrapper - [Vite (v.5)](https://main.vitejs.dev/)
-   Code formatter - [Prettier (2.8)](https://prettier.io/)
-   Language - [Typescript (5.1.6)](https://www.typescriptlang.org/)
-   Framework - [React JS (18)](https://reactjs.org/)
-   UI Components library - [MUI (5)](https://mui.com/material-ui/)

# Available Scripts

In the project directory, you can run:

-   `npm install` - for dependencies installing
-   `npm run start` - for building in "development" mode
    -   _includes hot reload (in fact that's just a `webpack --watch` inside)_
    -   _no minification_
-   `npm run build` - for building in "production" mode
-   `npm run pretty` - for running code formatting

# Local Setup

##### Preconditions:

-   Installed backend part of CMOS application, hosted locally
    -   Recommended port: **54812** _(http://localhost:54812)_
-   Installed NodeJS
    -   Recommended version: **20.11.1**
-   Installed NPM
    -   Recommended version: **10.4.0**
-   Installed IDE
    -   Recommended: **VS Code**

##### Steps:

1. Clone repository - https://bitbucket.org/clearmechanic/clearmechanic.frontend/src/master/
2. Setup code formatting _(instruction is applicable only for VS Code)_
    1. Open root folder of project
    2. Open recommended extensions and install Prettier
        - ![example](/readme_assets/vs-code-recommendations.png)
    3. Make sure the Prettier is selected as default code formatter
        1. Open control pane (Ctrl+Shift+P)
        2. Select "Format Document With..."
        3. Select "Configure Default Formatter..."
        4. Select "Prettier - code formatter"
3. Install dependencies by `npm install`
4. Build project and start dev session by `npm run start`
5. Enjoy!

# Helpful links

-   [One of UAT Repair shops](https://uat.clearmechanic.com/Service)
-   [One of PROD Repair shops](https://demo.clearmechanic.com/Service)
-   [UAT Administration area](https://uat.clearmechanic.com/Administration)
-   [PROD Administration area](https://demo.clearmechanic.com/Administration)

# devcontainer (Experimental)

This repo supports devcontainer.
You need to install Devcontainer extension to use it (ms-vscode-remote.remote-containers).
Main benefits of devcontainer are:

-   You do not have to install right version of node/npm locally, it will installed in isolation in container.
-   You will avoid system-specific issues. For example, some build error only happen on Linux, but won't occur on Windows. Container simulates production environment.

## Things to keep in mind

-   Before using make sure that you have allocated enough memory and CPU to WSL. Dev container will use at least 1GB of memory so make sure to allocate at least 2-3 GB (+ whatever you will need for other running containers).
-   After launching the container git will take some time to re-index the repository. If it doesn't happen automatically, you can run `git status` to force it.
-   Typescript will take quite some time (up to a 2 minutes) to reindex the codebase. Autocompletion won't work for some time.
-   Typescript will re-initialize every time container is recreated. For example if you restart WSL. Essentially you will have to wait extra minute in the beginning of your work day :(
-   Reduced disk access speed - data stored on your Windows machine is slower to access from a devcontainer, so basically everything is slower, this can be worked around but right now it is a problem that will persist

## How to use

-   Install Devcontainer extension.
-   `Ctrl+Shift+P` opens the command menu.
-   Run `Dev Container: Clone Repository in Container Volume` to clone the repository inside the container. This is more performant due to faster I/O but it means the source code won't be accessible from Windows.
-   Run `Dev Container: Reopen in Container` to run container and mount source directory to it. This is slower because container will not have direct access to the source code. It will access it through WSL's abstraction layer. So basically just slower I/O operations which affects pretty much everything.
