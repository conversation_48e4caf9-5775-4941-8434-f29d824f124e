{"name": "DevContainer", "image": "node:20.11.1-bullseye", "runArgs": ["--network=host"], "customizations": {"vscode": {"extensions": ["esbenp.prettier-vscode", "jbockle.jbockle-format-files", "yoavbls.pretty-ts-errors", "dbaeumer.vscode-eslint", "streetsidesoftware.code-spell-checker", "aaron-bond.better-comments"]}}, "features": {"ghcr.io/devcontainers/features/node:1": {"version": "20.11.1", "npmVersion": "10.4.0"}}, "postCreateCommand": "chmod 700 /root/.ssh && chmod 600 /root/.ssh/* && npm install -g npm@10.4.0 && npm install --force", "forwardPorts": [3000], "containerEnv": {"VITE_DEV_SERVER_HOST": "0.0.0.0", "VITE_DEV_LOCALHOST": "host.docker.internal", "VITE_DEV_PROXY_CHANGE_ORIGIN": "false"}, "mounts": ["source=${localEnv:USERPROFILE}\\.ssh,target=/root/.ssh,type=bind,consistency=cached"]}