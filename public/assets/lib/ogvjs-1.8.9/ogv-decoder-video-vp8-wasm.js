
var OGVDecoderVideoVP8W = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(OGVDecoderVideoVP8W) {
  OGVDecoderVideoVP8W = OGVDecoderVideoVP8W || {};


var a;a||(a=typeof OGVDecoderVideoVP8W !== 'undefined' ? OGVDecoderVideoVP8W : {});var aa,m;a.ready=new Promise(function(b,c){aa=b;m=c});var ba=a,ca=Object.assign({},a),ha="object"==typeof window,n="function"==typeof importScripts,t="",x,y,A,fs,B,C;
if("object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node)t=n?require("path").dirname(t)+"/":__dirname+"/",C=()=>{B||(fs=require("fs"),B=require("path"))},x=function(b,c){C();b=B.normalize(b);return fs.readFileSync(b,c?void 0:"utf8")},A=b=>{b=x(b,!0);b.buffer||(b=new Uint8Array(b));return b},y=(b,c,e)=>{C();b=B.normalize(b);fs.readFile(b,function(d,f){d?e(d):c(f.buffer)})},1<process.argv.length&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),
process.on("unhandledRejection",function(b){throw b;}),a.inspect=function(){return"[Emscripten Module object]"};else if(ha||n)n?t=self.location.href:"undefined"!=typeof document&&document.currentScript&&(t=document.currentScript.src),_scriptDir&&(t=_scriptDir),0!==t.indexOf("blob:")?t=t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1):t="",x=b=>{var c=new XMLHttpRequest;c.open("GET",b,!1);c.send(null);return c.responseText},n&&(A=b=>{var c=new XMLHttpRequest;c.open("GET",b,!1);c.responseType="arraybuffer";
c.send(null);return new Uint8Array(c.response)}),y=(b,c,e)=>{var d=new XMLHttpRequest;d.open("GET",b,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?c(d.response):e()};d.onerror=e;d.send(null)};a.print||console.log.bind(console);var D=a.printErr||console.warn.bind(console);Object.assign(a,ca);ca=null;var ia=0,E;a.wasmBinary&&(E=a.wasmBinary);var noExitRuntime=a.noExitRuntime||!0;"object"!=typeof WebAssembly&&F("no native wasm support detected");
var G,ja=!1,ka,la;function ma(){var b=G.buffer;ka=b;a.HEAP8=new Int8Array(b);a.HEAP16=new Int16Array(b);a.HEAP32=new Int32Array(b);a.HEAPU8=la=new Uint8Array(b);a.HEAPU16=new Uint16Array(b);a.HEAPU32=new Uint32Array(b);a.HEAPF32=new Float32Array(b);a.HEAPF64=new Float64Array(b)}var na,oa=[],pa=[],qa=[];function ra(){var b=a.preRun.shift();oa.unshift(b)}var K=0,sa=null,L=null;a.preloadedImages={};a.preloadedAudios={};
function F(b){if(a.onAbort)a.onAbort(b);b="Aborted("+b+")";D(b);ja=!0;b=new WebAssembly.RuntimeError(b+". Build with -s ASSERTIONS=1 for more info.");m(b);throw b;}function ta(){return M.startsWith("data:application/octet-stream;base64,")}var M;M="ogv-decoder-video-vp8-wasm.wasm";if(!ta()){var ua=M;M=a.locateFile?a.locateFile(ua,t):t+ua}function va(){var b=M;try{if(b==M&&E)return new Uint8Array(E);if(A)return A(b);throw"both async and sync fetching of the wasm failed";}catch(c){F(c)}}
function wa(){if(!E&&(ha||n)){if("function"==typeof fetch&&!M.startsWith("file://"))return fetch(M,{credentials:"same-origin"}).then(function(b){if(!b.ok)throw"failed to load wasm binary file at '"+M+"'";return b.arrayBuffer()}).catch(function(){return va()});if(y)return new Promise(function(b,c){y(M,function(e){b(new Uint8Array(e))},c)})}return Promise.resolve().then(function(){return va()})}
function xa(b){for(;0<b.length;){var c=b.shift();if("function"==typeof c)c(a);else{var e=c.C;"number"==typeof e?void 0===c.A?N(e)():N(e)(c.A):e(void 0===c.A?null:c.A)}}}var O=[];function N(b){var c=O[b];c||(b>=O.length&&(O.length=b+1),O[b]=c=na.get(b));return c}
var Oa={e:function(){throw Infinity;},g:function(b,c,e){la.copyWithin(b,c,c+e)},f:function(b){var c=la.length;b>>>=0;if(2147483648<b)return!1;for(var e=1;4>=e;e*=2){var d=c*(1+.2/e);d=Math.min(d,b+100663296);var f=Math;d=Math.max(b,d);f=f.min.call(f,2147483648,d+(65536-d%65536)%65536);a:{try{G.grow(f-ka.byteLength+65535>>>16);ma();var g=1;break a}catch(l){}g=void 0}if(g)return!0}return!1},a:function(){return ia},d:Ja,i:Ka,j:La,h:Ma,c:Na,k:function(b,c,e,d,f,g,l,p,P,q,H,I,Q,R,da,ea){function fa(z,
h,u,ya,za,Aa,Ra,Sa,S){z.set(new Uint8Array(Ta,h,u*ya));var v,r;for(v=r=0;v<Aa;v++,r+=u)for(h=0;h<u;h++)z[r+h]=S;for(;v<Aa+Sa;v++,r+=u){for(h=0;h<za;h++)z[r+h]=S;for(h=za+Ra;h<u;h++)z[r+h]=S}for(;v<ya;v++,r+=u)for(h=0;h<u;h++)z[r+h]=S;return z}var Ta=G.buffer,k=a.videoFormat,Ba=(Q&-2)*P/l,Ca=(R&-2)*q/p,Da=H*P/l,Ea=I*q/p;H===k.cropWidth&&I===k.cropHeight&&(da=k.displayWidth,ea=k.displayHeight);for(var Fa=a.recycledFrames,w,Ga=p*c,Ha=q*d,Ia=q*g;0<Fa.length;){var J=Fa.shift();k=J.format;if(k.width===
l&&k.height===p&&k.chromaWidth===P&&k.chromaHeight===q&&k.cropLeft===Q&&k.cropTop===R&&k.cropWidth===H&&k.cropHeight===I&&k.displayWidth===da&&k.displayHeight===ea&&J.y.bytes.length===Ga&&J.u.bytes.length===Ha&&J.v.bytes.length===Ia){w=J;break}}w||(w={format:{width:l,height:p,chromaWidth:P,chromaHeight:q,cropLeft:Q,cropTop:R,cropWidth:H,cropHeight:I,displayWidth:da,displayHeight:ea},y:{bytes:new Uint8Array(Ga),stride:c},u:{bytes:new Uint8Array(Ha),stride:d},v:{bytes:new Uint8Array(Ia),stride:g}});
fa(w.y.bytes,b,c,p,Q,R,H,I,0);fa(w.u.bytes,e,d,q,Ba,Ca,Da,Ea,128);fa(w.v.bytes,f,g,q,Ba,Ca,Da,Ea,128);a.frameBuffer=w},b:function(b){ia=b}};
(function(){function b(f){a.asm=f.exports;G=a.asm.l;ma();na=a.asm.s;pa.unshift(a.asm.m);K--;a.monitorRunDependencies&&a.monitorRunDependencies(K);0==K&&(null!==sa&&(clearInterval(sa),sa=null),L&&(f=L,L=null,f()))}function c(f){b(f.instance)}function e(f){return wa().then(function(g){return WebAssembly.instantiate(g,d)}).then(function(g){return g}).then(f,function(g){D("failed to asynchronously prepare wasm: "+g);F(g)})}var d={a:Oa};K++;a.monitorRunDependencies&&a.monitorRunDependencies(K);if(a.instantiateWasm)try{return a.instantiateWasm(d,
b)}catch(f){return D("Module.instantiateWasm callback failed with error: "+f),!1}(function(){return E||"function"!=typeof WebAssembly.instantiateStreaming||ta()||M.startsWith("file://")||"function"!=typeof fetch?e(c):fetch(M,{credentials:"same-origin"}).then(function(f){return WebAssembly.instantiateStreaming(f,d).then(c,function(g){D("wasm streaming compile failed: "+g);D("falling back to ArrayBuffer instantiation");return e(c)})})})().catch(m);return{}})();
a.___wasm_call_ctors=function(){return(a.___wasm_call_ctors=a.asm.m).apply(null,arguments)};a._ogv_video_decoder_init=function(){return(a._ogv_video_decoder_init=a.asm.n).apply(null,arguments)};a._ogv_video_decoder_async=function(){return(a._ogv_video_decoder_async=a.asm.o).apply(null,arguments)};a._ogv_video_decoder_destroy=function(){return(a._ogv_video_decoder_destroy=a.asm.p).apply(null,arguments)};
a._ogv_video_decoder_process_header=function(){return(a._ogv_video_decoder_process_header=a.asm.q).apply(null,arguments)};a._ogv_video_decoder_process_frame=function(){return(a._ogv_video_decoder_process_frame=a.asm.r).apply(null,arguments)};a._malloc=function(){return(a._malloc=a.asm.t).apply(null,arguments)};a._free=function(){return(a._free=a.asm.u).apply(null,arguments)};
var T=a._setThrew=function(){return(T=a._setThrew=a.asm.v).apply(null,arguments)},U=a.stackSave=function(){return(U=a.stackSave=a.asm.w).apply(null,arguments)},V=a.stackRestore=function(){return(V=a.stackRestore=a.asm.x).apply(null,arguments)},Pa=a.dynCall_iiiij=function(){return(Pa=a.dynCall_iiiij=a.asm.y).apply(null,arguments)};function Na(b,c,e,d,f){var g=U();try{N(b)(c,e,d,f)}catch(l){V(g);if(l!==l+0)throw l;T(1,0)}}
function Ja(b,c,e){var d=U();try{return N(b)(c,e)}catch(f){V(d);if(f!==f+0)throw f;T(1,0)}}function Ka(b,c,e,d){var f=U();try{return N(b)(c,e,d)}catch(g){V(f);if(g!==g+0)throw g;T(1,0)}}function Ma(b,c){var e=U();try{N(b)(c)}catch(d){V(e);if(d!==d+0)throw d;T(1,0)}}function La(b,c,e,d,f,g){var l=U();try{return Pa(b,c,e,d,f,g)}catch(p){V(l);if(p!==p+0)throw p;T(1,0)}}var W;L=function Qa(){W||Ua();W||(L=Qa)};
function Ua(){function b(){if(!W&&(W=!0,a.calledRun=!0,!ja)){xa(pa);aa(a);if(a.onRuntimeInitialized)a.onRuntimeInitialized();if(a.postRun)for("function"==typeof a.postRun&&(a.postRun=[a.postRun]);a.postRun.length;){var c=a.postRun.shift();qa.unshift(c)}xa(qa)}}if(!(0<K)){if(a.preRun)for("function"==typeof a.preRun&&(a.preRun=[a.preRun]);a.preRun.length;)ra();xa(oa);0<K||(a.setStatus?(a.setStatus("Running..."),setTimeout(function(){setTimeout(function(){a.setStatus("")},1);b()},1)):b())}}a.run=Ua;
if(a.preInit)for("function"==typeof a.preInit&&(a.preInit=[a.preInit]);0<a.preInit.length;)a.preInit.pop()();Ua();var X,Va,Y;"undefined"===typeof performance||"undefined"===typeof performance.now?Y=Date.now:Y=performance.now.bind(performance);function Z(b){var c=Y();b=b();a.cpuTime+=Y()-c;return b}a.loadedMetadata=!!ba.videoFormat;a.videoFormat=ba.videoFormat||null;a.frameBuffer=null;a.cpuTime=0;Object.defineProperty(a,"processing",{get:function(){return!1}});
a.init=function(b){Z(function(){a._ogv_video_decoder_init()});b()};a.processHeader=function(b,c){var e=Z(function(){var d=b.byteLength;X&&Va>=d||(X&&a._free(X),Va=d,X=a._malloc(Va));var f=X;(new Uint8Array(G.buffer,f,d)).set(new Uint8Array(b));return a._ogv_video_decoder_process_header(f,d)});c(e)};a.B=[];
a.processFrame=function(b,c){function e(p){a._free(g);c(p)}var d=a._ogv_video_decoder_async(),f=b.byteLength,g=a._malloc(f);d&&a.B.push(e);var l=Z(function(){(new Uint8Array(G.buffer,g,f)).set(new Uint8Array(b));return a._ogv_video_decoder_process_frame(g,f)});d||e(l)};a.close=function(){};a.sync=function(){a._ogv_video_decoder_async()&&(a.B.push(function(){}),Z(function(){a._ogv_video_decoder_process_frame(0,0)}))};a.recycledFrames=[];
a.recycleFrame=function(b){var c=a.recycledFrames;c.push(b);16<c.length&&c.shift()};


  return OGVDecoderVideoVP8W.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDecoderVideoVP8W;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return OGVDecoderVideoVP8W; });
else if (typeof exports === 'object')
  exports["OGVDecoderVideoVP8W"] = OGVDecoderVideoVP8W;
