
var OGVDemuxerWebMW = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(OGVDemuxerWebMW) {
  OGVDemuxerWebMW = OGVDemuxerWebMW || {};


var a;a||(a=typeof OGVDemuxerWebMW !== 'undefined' ? OGVDemuxerWebMW : {});var h,k;a.ready=new Promise(function(b,c){h=b;k=c});var l=Object.assign({},a),n="object"==typeof window,p="function"==typeof importScripts,q="",r,t,u,fs,v,w;
if("object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node)q=p?require("path").dirname(q)+"/":__dirname+"/",w=()=>{v||(fs=require("fs"),v=require("path"))},r=function(b,c){w();b=v.normalize(b);return fs.readFileSync(b,c?void 0:"utf8")},u=b=>{b=r(b,!0);b.buffer||(b=new Uint8Array(b));return b},t=(b,c,d)=>{w();b=v.normalize(b);fs.readFile(b,function(e,f){e?d(e):c(f.buffer)})},1<process.argv.length&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),
process.on("unhandledRejection",function(b){throw b;}),a.inspect=function(){return"[Emscripten Module object]"};else if(n||p)p?q=self.location.href:"undefined"!=typeof document&&document.currentScript&&(q=document.currentScript.src),_scriptDir&&(q=_scriptDir),0!==q.indexOf("blob:")?q=q.substr(0,q.replace(/[?#].*/,"").lastIndexOf("/")+1):q="",r=b=>{var c=new XMLHttpRequest;c.open("GET",b,!1);c.send(null);return c.responseText},p&&(u=b=>{var c=new XMLHttpRequest;c.open("GET",b,!1);c.responseType="arraybuffer";
c.send(null);return new Uint8Array(c.response)}),t=(b,c,d)=>{var e=new XMLHttpRequest;e.open("GET",b,!0);e.responseType="arraybuffer";e.onload=()=>{200==e.status||0==e.status&&e.response?c(e.response):d()};e.onerror=d;e.send(null)};var aa=a.print||console.log.bind(console),A=a.printErr||console.warn.bind(console);Object.assign(a,l);l=null;var B;a.wasmBinary&&(B=a.wasmBinary);var noExitRuntime=a.noExitRuntime||!0;"object"!=typeof WebAssembly&&C("no native wasm support detected");
var D,E=!1,F="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;
function H(b,c,d){var e=c+d;for(d=c;b[d]&&!(d>=e);)++d;if(16<d-c&&b.buffer&&F)return F.decode(b.subarray(c,d));for(e="";c<d;){var f=b[c++];if(f&128){var g=b[c++]&63;if(192==(f&224))e+=String.fromCharCode((f&31)<<6|g);else{var m=b[c++]&63;f=224==(f&240)?(f&15)<<12|g<<6|m:(f&7)<<18|g<<12|m<<6|b[c++]&63;65536>f?e+=String.fromCharCode(f):(f-=65536,e+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else e+=String.fromCharCode(f)}return e}function I(b){return b?H(J,b,void 0):""}var K,J,L;
function M(){var b=D.buffer;K=b;a.HEAP8=new Int8Array(b);a.HEAP16=new Int16Array(b);a.HEAP32=L=new Int32Array(b);a.HEAPU8=J=new Uint8Array(b);a.HEAPU16=new Uint16Array(b);a.HEAPU32=new Uint32Array(b);a.HEAPF32=new Float32Array(b);a.HEAPF64=new Float64Array(b)}var N,ba=[],ca=[],da=[];function ea(){var b=a.preRun.shift();ba.unshift(b)}var O=0,P=null,Q=null;a.preloadedImages={};a.preloadedAudios={};
function C(b){if(a.onAbort)a.onAbort(b);b="Aborted("+b+")";A(b);E=!0;b=new WebAssembly.RuntimeError(b+". Build with -s ASSERTIONS=1 for more info.");k(b);throw b;}function fa(){return R.startsWith("data:application/octet-stream;base64,")}var R;R="ogv-demuxer-webm-wasm.wasm";if(!fa()){var ha=R;R=a.locateFile?a.locateFile(ha,q):q+ha}function ia(){var b=R;try{if(b==R&&B)return new Uint8Array(B);if(u)return u(b);throw"both async and sync fetching of the wasm failed";}catch(c){C(c)}}
function ja(){if(!B&&(n||p)){if("function"==typeof fetch&&!R.startsWith("file://"))return fetch(R,{credentials:"same-origin"}).then(function(b){if(!b.ok)throw"failed to load wasm binary file at '"+R+"'";return b.arrayBuffer()}).catch(function(){return ia()});if(t)return new Promise(function(b,c){t(R,function(d){b(new Uint8Array(d))},c)})}return Promise.resolve().then(function(){return ia()})}
function S(b){for(;0<b.length;){var c=b.shift();if("function"==typeof c)c(a);else{var d=c.B;"number"==typeof d?void 0===c.A?ka(d)():ka(d)(c.A):d(void 0===c.A?null:c.A)}}}var T=[];function ka(b){var c=T[b];c||(b>=T.length&&(T.length=b+1),T[b]=c=N.get(b));return c}
var la=[null,[],[]],ma={a:function(b,c,d,e){C("Assertion failed: "+I(b)+", at: "+[c?I(c):"unknown filename",d,e?I(e):"unknown function"])},f:function(){C("")},e:function(b,c,d){J.copyWithin(b,c,c+d)},d:function(b){var c=J.length;b>>>=0;if(2147483648<b)return!1;for(var d=1;4>=d;d*=2){var e=c*(1+.2/d);e=Math.min(e,b+100663296);var f=Math;e=Math.max(b,e);f=f.min.call(f,2147483648,e+(65536-e%65536)%65536);a:{try{D.grow(f-K.byteLength+65535>>>16);M();var g=1;break a}catch(m){}g=void 0}if(g)return!0}return!1},
b:function(b,c,d,e){for(var f=0,g=0;g<d;g++){var m=L[c>>2],G=L[c+4>>2];c+=8;for(var x=0;x<G;x++){var y=J[m+x],z=la[b];0===y||10===y?((1===b?aa:A)(H(z,0)),z.length=0):z.push(y)}f+=G}L[e>>2]=f;return 0},c:function(b,c,d,e){var f=D.buffer;a.audioPackets.push({data:f.slice?f.slice(b,b+c):(new Uint8Array(new Uint8Array(f,b,c))).buffer,timestamp:d,discardPadding:e})},j:function(b,c,d,e,f,g,m,G,x,y,z){a.videoFormat={width:b,height:c,chromaWidth:d,chromaHeight:e,cropLeft:G,cropTop:x,cropWidth:g,cropHeight:m,
displayWidth:y,displayHeight:z,fps:f}},h:function(b,c){function d(e){for(var f="",g=new Uint8Array(D.buffer);0!=g[e];e++)f+=String.fromCharCode(g[e]);return f}b&&(a.videoCodec=d(b));c&&(a.audioCodec=d(c));b=a._ogv_demuxer_media_duration();a.duration=0<=b?b:NaN;a.loadedMetadata=!0},i:function(b,c){if(a.onseek)a.onseek(b+4294967296*c)},g:function(b,c,d,e,f){var g=D.buffer;a.videoPackets.push({data:g.slice?g.slice(b,b+c):(new Uint8Array(new Uint8Array(g,b,c))).buffer,timestamp:d,keyframeTimestamp:e,
isKeyframe:!!f})}};
(function(){function b(f){a.asm=f.exports;D=a.asm.k;M();N=a.asm.w;ca.unshift(a.asm.l);O--;a.monitorRunDependencies&&a.monitorRunDependencies(O);0==O&&(null!==P&&(clearInterval(P),P=null),Q&&(f=Q,Q=null,f()))}function c(f){b(f.instance)}function d(f){return ja().then(function(g){return WebAssembly.instantiate(g,e)}).then(function(g){return g}).then(f,function(g){A("failed to asynchronously prepare wasm: "+g);C(g)})}var e={a:ma};O++;a.monitorRunDependencies&&a.monitorRunDependencies(O);if(a.instantiateWasm)try{return a.instantiateWasm(e,
b)}catch(f){return A("Module.instantiateWasm callback failed with error: "+f),!1}(function(){return B||"function"!=typeof WebAssembly.instantiateStreaming||fa()||R.startsWith("file://")||"function"!=typeof fetch?d(c):fetch(R,{credentials:"same-origin"}).then(function(f){return WebAssembly.instantiateStreaming(f,e).then(c,function(g){A("wasm streaming compile failed: "+g);A("falling back to ArrayBuffer instantiation");return d(c)})})})().catch(k);return{}})();
a.___wasm_call_ctors=function(){return(a.___wasm_call_ctors=a.asm.l).apply(null,arguments)};a._ogv_demuxer_init=function(){return(a._ogv_demuxer_init=a.asm.m).apply(null,arguments)};a._ogv_demuxer_receive_input=function(){return(a._ogv_demuxer_receive_input=a.asm.n).apply(null,arguments)};a._ogv_demuxer_process=function(){return(a._ogv_demuxer_process=a.asm.o).apply(null,arguments)};a._ogv_demuxer_destroy=function(){return(a._ogv_demuxer_destroy=a.asm.p).apply(null,arguments)};
a._ogv_demuxer_flush=function(){return(a._ogv_demuxer_flush=a.asm.q).apply(null,arguments)};a._ogv_demuxer_media_length=function(){return(a._ogv_demuxer_media_length=a.asm.r).apply(null,arguments)};a._ogv_demuxer_media_duration=function(){return(a._ogv_demuxer_media_duration=a.asm.s).apply(null,arguments)};a._ogv_demuxer_seekable=function(){return(a._ogv_demuxer_seekable=a.asm.t).apply(null,arguments)};
a._ogv_demuxer_keypoint_offset=function(){return(a._ogv_demuxer_keypoint_offset=a.asm.u).apply(null,arguments)};a._ogv_demuxer_seek_to_keypoint=function(){return(a._ogv_demuxer_seek_to_keypoint=a.asm.v).apply(null,arguments)};a._malloc=function(){return(a._malloc=a.asm.x).apply(null,arguments)};a._free=function(){return(a._free=a.asm.y).apply(null,arguments)};var U;Q=function na(){U||V();U||(Q=na)};
function V(){function b(){if(!U&&(U=!0,a.calledRun=!0,!E)){S(ca);h(a);if(a.onRuntimeInitialized)a.onRuntimeInitialized();if(a.postRun)for("function"==typeof a.postRun&&(a.postRun=[a.postRun]);a.postRun.length;){var c=a.postRun.shift();da.unshift(c)}S(da)}}if(!(0<O)){if(a.preRun)for("function"==typeof a.preRun&&(a.preRun=[a.preRun]);a.preRun.length;)ea();S(ba);0<O||(a.setStatus?(a.setStatus("Running..."),setTimeout(function(){setTimeout(function(){a.setStatus("")},1);b()},1)):b())}}a.run=V;
if(a.preInit)for("function"==typeof a.preInit&&(a.preInit=[a.preInit]);0<a.preInit.length;)a.preInit.pop()();V();var W,X,Y;"undefined"===typeof performance||"undefined"===typeof performance.now?Y=Date.now:Y=performance.now.bind(performance);function Z(b){var c=Y();b=b();c=Y()-c;a.cpuTime+=c;return b}a.loadedMetadata=!1;a.videoCodec=null;a.audioCodec=null;a.duration=NaN;a.onseek=null;a.cpuTime=0;a.audioPackets=[];Object.defineProperty(a,"hasAudio",{get:function(){return a.loadedMetadata&&a.audioCodec}});
Object.defineProperty(a,"audioReady",{get:function(){return 0<a.audioPackets.length}});Object.defineProperty(a,"audioTimestamp",{get:function(){return 0<a.audioPackets.length?a.audioPackets[0].timestamp:-1}});a.videoPackets=[];Object.defineProperty(a,"hasVideo",{get:function(){return a.loadedMetadata&&a.videoCodec}});Object.defineProperty(a,"frameReady",{get:function(){return 0<a.videoPackets.length}});
Object.defineProperty(a,"frameTimestamp",{get:function(){return 0<a.videoPackets.length?a.videoPackets[0].timestamp:-1}});Object.defineProperty(a,"keyframeTimestamp",{get:function(){return 0<a.videoPackets.length?a.videoPackets[0].keyframeTimestamp:-1}});Object.defineProperty(a,"nextKeyframeTimestamp",{get:function(){for(var b=0;b<a.videoPackets.length;b++){var c=a.videoPackets[b];if(c.isKeyframe)return c.timestamp}return-1}});Object.defineProperty(a,"processing",{get:function(){return!1}});
Object.defineProperty(a,"seekable",{get:function(){return!!a._ogv_demuxer_seekable()}});a.init=function(b){Z(function(){a._ogv_demuxer_init()});b()};a.receiveInput=function(b,c){Z(function(){var d=b.byteLength;W&&X>=d||(W&&a._free(W),X=d,W=a._malloc(X));var e=W;(new Uint8Array(D.buffer,e,d)).set(new Uint8Array(b));a._ogv_demuxer_receive_input(e,d)});c()};a.process=function(b){var c=Z(function(){return a._ogv_demuxer_process()});b(!!c)};
a.dequeueVideoPacket=function(b){if(a.videoPackets.length){var c=a.videoPackets.shift().data;b(c)}else b(null)};a.dequeueAudioPacket=function(b){if(a.audioPackets.length){var c=a.audioPackets.shift();b(c.data,c.discardPadding)}else b(null)};a.getKeypointOffset=function(b,c){var d=Z(function(){return a._ogv_demuxer_keypoint_offset(1E3*b)});c(d)};
a.seekToKeypoint=function(b,c){var d=Z(function(){return a._ogv_demuxer_seek_to_keypoint(1E3*b)});d&&(a.audioPackets.splice(0,a.audioPackets.length),a.videoPackets.splice(0,a.videoPackets.length));c(!!d)};a.flush=function(b){Z(function(){a.audioPackets.splice(0,a.audioPackets.length);a.videoPackets.splice(0,a.videoPackets.length);a._ogv_demuxer_flush()});b()};a.close=function(){};


  return OGVDemuxerWebMW.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDemuxerWebMW;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return OGVDemuxerWebMW; });
else if (typeof exports === 'object')
  exports["OGVDemuxerWebMW"] = OGVDemuxerWebMW;
