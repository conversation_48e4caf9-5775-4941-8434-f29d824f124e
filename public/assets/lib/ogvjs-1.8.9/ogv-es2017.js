/*! For license information please see ogv-es2017.js.LICENSE.txt */
!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ogvjs=t():e.ogvjs=t()}(globalThis,(function(){return(()=>{var e={318:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},848:e=>{window,e.exports=function(e){var t={};function r(i){if(t[i])return t[i].exports;var s=t[i]={i,l:!1,exports:{}};return e[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(i,s,function(t){return e[t]}.bind(null,s));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=1)}([function(e,t){function r(e,t){if(e<1||e!==Math.round(e))throw"Invalid channel count for BufferQueue";this.channels=e,this.bufferSize=t,this.flush()}r.prototype.flush=function(){this._buffers=[],this._pendingBuffer=this.createBuffer(this.bufferSize),this._pendingPos=0},r.prototype.sampleCount=function(){var e=0;return this._buffers.forEach((function(t){e+=t[0].length})),e},r.prototype.createBuffer=function(e){for(var t=[],i=0;i<this.channels;i++)t[i]=new Float32Array(e);return t},r.prototype.validate=function(e){if(e.length!==this.channels)return!1;for(var t,i=0;i<e.length;i++){var s=e[i];if(!(s instanceof Float32Array))return!1;if(0==i)t=s.length;else if(s.length!==t)return!1}return!0},r.prototype.appendBuffer=function(e){if(!this.validate(e))throw"Invalid audio buffer passed to BufferQueue.appendBuffer";for(var t=e[0].length,i=this.channels,s=this._pendingPos,d=this._pendingBuffer,h=this.bufferSize,u=0;u<t;u++){for(var c=0;c<i;c++)d[c][s]=e[c][u];++s==h&&(this._buffers.push(d),s=this._pendingPos=0,d=this._pendingBuffer=this.createBuffer(h))}this._pendingPos=s},r.prototype.prependBuffer=function(e){if(!this.validate(e))throw"Invalid audio buffer passed to BufferQueue.prependBuffer";var t=this._buffers.slice(0);t.push(this.trimBuffer(this._pendingBuffer,0,this._pendingPos)),this.flush(),this.appendBuffer(e);for(var i=0;i<t.length;i++)this.appendBuffer(t[i])},r.prototype.nextBuffer=function(){if(this._buffers.length)return this._buffers.shift();var e=this.trimBuffer(this._pendingBuffer,0,this._pendingPos);return this._pendingBuffer=this.createBuffer(this.bufferSize),this._pendingPos=0,e},r.prototype.trimBuffer=function(e,t,i){var s=e[0].length,d=t+Math.min(i,s);if(0==t&&d>=s)return e;for(var h=[],u=0;u<this.channels;u++)h[u]=e[u].subarray(t,d);return h},e.exports=r},function(e,t,i){!function(){i(0);var t=i(2),s=i(4);function n(e){this._options=e||{},this._backend=null,this._resampleFractional=0,this._resampleLastSampleData=void 0,this._tempoChanger=null}n.prototype.rate=0,n.prototype.targetRate=0,n.prototype.channels=0,n.prototype.bufferSize=0,Object.defineProperty(n.prototype,"bufferDuration",{get:function(){return this.targetRate?this.bufferSize/this.targetRate:0}}),Object.defineProperty(n.prototype,"bufferThreshold",{get:function(){return this._backend?this._backend.bufferThreshold/this.targetRate:0},set:function(e){if(!this._backend)throw"Invalid state: AudioFeeder cannot set bufferThreshold before init";this._backend.bufferThreshold=Math.round(e*this.targetRate)}}),Object.defineProperty(n.prototype,"playbackPosition",{get:function(){return this._backend?this.getPlaybackState().playbackPosition:0}}),Object.defineProperty(n.prototype,"outputPlaybackPosition",{get:function(){return this._backend?this.getPlaybackState().outputPlaybackPosition:0}}),Object.defineProperty(n.prototype,"durationBuffered",{get:function(){return this._backend?this.getPlaybackState().samplesQueued/this.targetRate:0}}),Object.defineProperty(n.prototype,"muted",{get:function(){if(this._backend)return this._backend.muted;throw"Invalid state: cannot get mute before init"},set:function(e){if(!this._backend)throw"Invalid state: cannot set mute before init";this._backend.muted=e}}),n.prototype.mute=function(){this.muted=!0},n.prototype.unmute=function(){this.muted=!1},Object.defineProperty(n.prototype,"volume",{get:function(){if(this._backend)return this._backend.volume;throw"Invalid state: cannot get volume before init"},set:function(e){if(!this._backend)throw"Invalid state: cannot set volume before init";this._backend.volume=e}}),Object.defineProperty(n.prototype,"tempo",{get:function(){if(this._tempoChanger)return this._tempoChanger.getTempo();throw"Invalid state: cannot get tempo before init"},set:function(e){if(!this._tempoChanger)throw"Invalid state: cannot set tempo before init";this._tempoChanger.setTempo(e)}}),n.prototype.init=function(e,i){if(this.channels=e,this.rate=i,this._options.backendFactory)this._backend=this._options.backendFactory(e,i,this._options);else{if(!t.isSupported())throw"No supported backend";this._backend=new t(e,i,this._options)}this.targetRate=this._backend.rate,this.bufferSize=this._backend.bufferSize,this._tempoChanger=s({sampleRate:this.targetRate,numChannels:e,tempo:1}),this._backend.onstarved=function(){this.onstarved&&this.onstarved()}.bind(this),this._backend.onbufferlow=function(){this.onbufferlow&&this.onbufferlow()}.bind(this)},n.prototype._resample=function(e){var t=this.rate,i=this.channels,s=this._backend.rate,d=this._backend.channels;if(t==s&&i==d)return e;var h,u=[],c=e[0].length,l=this._resampleFractional,f=c*s/t+l,_=Math.floor(f),p=f-_;h=t<s?function(e,i,d,h){for(var a=function(t){return t<0?d&&d.length+t>0?d[d.length+t]:e[0]:e[t]},u=0;u<i.length;u++){var c,f=(u+1-l)*t/s-1,_=Math.floor(f),p=Math.ceil(f);c=_==p?a(_):a(_)*(p-f)+a(p)*(f-_),i[u]=h*c}}:function(e,t,i,s){for(var d=0;d<t.length;d++)t[d]=s*e[d*e.length/t.length|0]};var m=1;d>i&&(m=Math.SQRT1_2);for(var V=0;V<d;V++){var g=V;V>=i&&(g=0);var v=e[g],b=new Float32Array(_);h(v,b,this._resampleLastSampleData?this._resampleLastSampleData[g]:void 0,m),u.push(b)}return this._resampleFractional=p,this._resampleLastSampleData=e,u},n.prototype.bufferData=function(e){if(!this._backend)throw"Invalid state: AudioFeeder cannot bufferData before init";var t=this._resample(e);t=this._tempoChanger.process(t),this._backend.appendBuffer(t)},n.prototype.getPlaybackState=function(){if(this._backend){var e=this._backend.getPlaybackState();return e.outputPlaybackPosition=e.playbackPosition,e.playbackPosition=this._tempoChanger.mapOutputToInputTime(e.outputPlaybackPosition),e}throw"Invalid state: AudioFeeder cannot getPlaybackState before init"},n.prototype.waitUntilReady=function(e){if(!this._backend)throw"Invalid state: AudioFeeder cannot waitUntilReady before init";this._backend.waitUntilReady(e)},n.prototype.start=function(){if(!this._backend)throw"Invalid state: AudioFeeder cannot start before init";this._backend.start()},n.prototype.stop=function(){if(!this._backend)throw"Invalid state: AudioFeeder cannot stop before init";this._backend.stop()},n.prototype.flush=function(){if(this._resampleFractional=0,this._resampleLastSampleData=void 0,!this._backend)throw"Invalid state: AudioFeeder cannot flush before init";this._tempoChanger.flush(this.durationBuffered),this._backend.flush()},n.prototype.close=function(){this._backend&&(this._backend.close(),this._backend=null)},n.prototype.onstarved=null,n.prototype.onbufferlow=null,n.isSupported=function(){return!!Float32Array&&t.isSupported()},n.initSharedAudioContext=function(){return t.isSupported()?t.initSharedAudioContext():null},e.exports=n}()},function(e,t,i){!function(){var t=window.AudioContext||window.webkitAudioContext,s=i(0),d=i(3);function o(e,t,i){var d=i.audioContext||o.initSharedAudioContext();if(this._context=d,this.output=i.output||d.destination,this.rate=d.sampleRate,this.channels=2,i.bufferSize&&(this.bufferSize=0|i.bufferSize),this.bufferThreshold=2*this.bufferSize,this._bufferQueue=new s(this.channels,this.bufferSize),this._playbackTimeAtBufferTail=d.currentTime,this._queuedTime=0,this._delayedTime=0,this._dropped=0,this._liveBuffer=this._bufferQueue.createBuffer(this.bufferSize),d.createScriptProcessor)this._node=d.createScriptProcessor(this.bufferSize,0,this.channels);else{if(!d.createJavaScriptNode)throw new Error("Bad version of web audio API?");this._node=d.createJavaScriptNode(this.bufferSize,0,this.channels)}}o.prototype.bufferSize=4096,o.prototype.bufferThreshold=8192,o.prototype._volume=1,Object.defineProperty(o.prototype,"volume",{get:function(){return this._volume},set:function(e){this._volume=+e}}),o.prototype._muted=!1,Object.defineProperty(o.prototype,"muted",{get:function(){return this._muted},set:function(e){this._muted=!!e}}),o.prototype._audioProcess=function(e){var t,i,s,h,u;u="number"==typeof e.playbackTime?e.playbackTime:this._context.currentTime+this.bufferSize/this.rate;var c=this._playbackTimeAtBufferTail;if(c<u&&(this._delayedTime+=u-c),this._bufferQueue.sampleCount()<this.bufferSize&&this.onstarved&&this.onstarved(),this._bufferQueue.sampleCount()<this.bufferSize){for(t=0;t<this.channels;t++)for(s=e.outputBuffer.getChannelData(t),h=0;h<this.bufferSize;h++)s[h]=0;this._dropped++}else{var l=this.muted?0:this.volume,f=this._bufferQueue.nextBuffer();if(f[0].length<this.bufferSize)throw"Audio buffer not expected length.";for(t=0;t<this.channels;t++)for(i=f[t],this._liveBuffer[t].set(f[t]),s=e.outputBuffer.getChannelData(t),h=0;h<i.length;h++)s[h]=i[h]*l;this._queuedTime+=this.bufferSize/this.rate,this._playbackTimeAtBufferTail=u+this.bufferSize/this.rate,this._bufferQueue.sampleCount()<Math.max(this.bufferSize,this.bufferThreshold)&&this.onbufferlow&&d(this.onbufferlow.bind(this))}},o.prototype._samplesQueued=function(){return this._bufferQueue.sampleCount()+Math.floor(this._timeAwaitingPlayback()*this.rate)},o.prototype._timeAwaitingPlayback=function(){return Math.max(0,this._playbackTimeAtBufferTail-this._context.currentTime)},o.prototype.getPlaybackState=function(){return{playbackPosition:this._queuedTime-this._timeAwaitingPlayback(),samplesQueued:this._samplesQueued(),dropped:this._dropped,delayed:this._delayedTime}},o.prototype.waitUntilReady=function(e){e()},o.prototype.appendBuffer=function(e){this._bufferQueue.appendBuffer(e)},o.prototype.start=function(){this._node.onaudioprocess=this._audioProcess.bind(this),this._node.connect(this.output),this._playbackTimeAtBufferTail=this._context.currentTime},o.prototype.stop=function(){if(this._node){var e=this._timeAwaitingPlayback();if(e>0){var t=Math.round(e*this.rate),i=this._liveBuffer?this._liveBuffer[0].length:0;t>i?(this._bufferQueue.prependBuffer(this._liveBuffer),this._bufferQueue.prependBuffer(this._bufferQueue.createBuffer(t-i))):this._bufferQueue.prependBuffer(this._bufferQueue.trimBuffer(this._liveBuffer,i-t,t)),this._playbackTimeAtBufferTail-=e}this._node.onaudioprocess=null,this._node.disconnect()}},o.prototype.flush=function(){this._bufferQueue.flush()},o.prototype.close=function(){this.stop(),this._context=null},o.prototype.onstarved=null,o.prototype.onbufferlow=null,o.isSupported=function(){return!!t},o.sharedAudioContext=null,o.initSharedAudioContext=function(){if(!o.sharedAudioContext&&o.isSupported()){var e,i=new t;if(i.createScriptProcessor)e=i.createScriptProcessor(1024,0,2);else{if(!i.createJavaScriptNode)throw new Error("Bad version of web audio API?");e=i.createJavaScriptNode(1024,0,2)}e.connect(i.destination),e.disconnect(),o.sharedAudioContext=i}return o.sharedAudioContext},e.exports=o}()},function(e,t){e.exports=function(){if(void 0!==window.setImmediate)return window.setImmediate;if(window&&window.postMessage){var e=[];return window.addEventListener("message",(function(t){if(t.source===window){var i=t.data;if("object"==typeof i&&i.nextTickBrowserPingMessage){var s=e.pop();s&&s()}}})),function(t){e.push(t),window.postMessage({nextTickBrowserPingMessage:!0},document.location.toString())}}return function(e){setTimeout(e,0)}}()},function(e,t,i){var s;window,s=function(){return function(e){var t={};function r(i){if(t[i])return t[i].exports;var s=t[i]={i,l:!1,exports:{}};return e[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(i,s,function(t){return e[t]}.bind(null,s));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=1)}([function(e,t){var i={float_array:function(e){return new Float32Array(e)},blit:function(e,t,i,s,d){i.set(e.subarray(t,t+d),s)}};e.exports=i},function(e,t,i){var s,d;s=i(0),d=i(2),e.exports=function(e){var t=(e=e||{}).sampleRate||44100,i=e.wsizeLog||11,h=e.tempo||1,u=(e.numChannels,Math.pow(2,50/1200)-1),c=1<<i,l=d(i),f=1<<i-2;f-=f%100;for(var _=s.float_array(c+f+5),p=s.float_array(c+f+5),m=f,V=f,g=s.float_array(c),v=0;v<c;v++)g[v]=.5*(1-Math.cos(2*Math.PI*v/c));var b=1+(c>>1),y=s.float_array(b),T=s.float_array(b),k=s.float_array(b),A=s.float_array(b),w=s.float_array(b),P=s.float_array(b),E=1+(b>>1),x=[0,0],R=[],F=[],O=[],S=[];for(v=0;v<2;v++)R.push(s.float_array(E)),F.push(s.float_array(E)),O.push(s.float_array(E)),S.push(s.float_array(b));var C=s.float_array(E),B=s.float_array(E),D=0,M=0,I=[{in_time:0,out_time:0,tempo:h}],L=0,j=0,W=1,X=0,H=0,N=0,z=0,Q={mapOutputToInputTime:function(e){for(var t=I.length-1;e<I[t].out_time&&t>0;)t--;var i=I[t];return i.in_time+i.tempo*(e-i.out_time)},flush:function(e){X=0,x=[0,0],j=0,z=0,N=0;for(var t=0;t<2;t++)for(var i=0;i<b;i++)S[t][i]=0;for(t=0;t<_.length;t++)_[t]=0;for(t=0;t<p.length;t++)p[t]=0;if(e){M=Math.max(0,M-e),D=Q.mapOutputToInputTime(M);for(var s=I.length-1;M<=I[s].out_time&&s>=0;)I.pop(),s--;I.push({in_time:D,out_time:M,tempo:h})}},getTempo:function(){return h},setTempo:function(e){m=V=f,e>=1?V=Math.round(m/e):m=Math.round(V*e),H=(1/e-1*V/m)*m,W=function(e,t){for(var i=e.length/t|0,s=0,d=0;d<i;d++)s+=e[d*t];return.9/s}(g,V),h=e;var t=I[I.length-1];t.out_time==M?t.tempo=e:I.push({in_time:D,out_time:M,tempo:e})}};Q.flush(0),Q.setTempo(h);var J=function(e,t,i){var s=Math.floor(i),d=s%2==1?-1:1;return Math.atan2(d*(t[s]-t[s+1]),d*(e[s]-e[s+1]))},U=function(e,t,i,s,d){var h=2*Math.PI/c*.5*(s+t)*m;return(function(e){return e-2*Math.PI*Math.round(e/(2*Math.PI))}(e-i-h)+h)*d},q=function(e,t,i,s,d,h){for(var l=e%2,f=1-l,_=S[f],p=x[f],m=R[f],V=F[f],g=O[f],v=S[l],b=1;b<v.length;b++)v[b]=t[b]*t[b]+i[b]*i[b];var y=R[l],T=x[l]=function(e,t){for(var i=0,s=0;s<e.length;s++)e[s]>i&&(i=e[s]);var d=1e-8*i,h=1,c=1;for(t[0]=1,s=2;s<e.length;s++){var l=s*u;if(e[s]>d&&e[s]>e[s-1]&&e[s]>=e[s+1]){var f=s+(e[s-1]-e[s+1])/(2*(e[s-1]-2*e[s]+e[s+1]));f-t[h-1]>l?(t[h++]=f,c=s):e[s]>e[c]&&(t[h-1]=f,c=s)}}return h}(v,y),k=F[l],A=O[l];if(0!=e&&0!=T){var w=0;for(N=0;N<T;N++){for(z=y[N];y[N]>m[w]&&w!=p;)++w;var P=w;w>0&&z-m[w-1]<m[w]-z&&(P=w-1);var E=z*u;if(Math.abs(m[P]-z)<E&&_[Math.round(m[P])]>.1*v[Math.round(z)]){var D=J(t,i,z),M=V[P]+g[P]+U(D,z,V[P],m[P],h)-D;k[N]=D,A[N]=M,C[N]=Math.cos(M),B[N]=Math.sin(M)}else k[N]=J(t,i,z),A[N]=0,C[N]=1,B[N]=0}y[T]=2*c;var I=y[P=0],L=y[P+1],j=C[P],W=B[P];for(b=1;b<t.length-1;b++){b>=I&&b-I>L-b&&(I=y[++P],L=y[P+1],j=C[P],W=B[P]);var X=t[b]*j-i[b]*W,H=t[b]*W+i[b]*j;t[b]=X,i[b]=H}}else for(var N=0;N<T;N++){var z=y[N];V[N]=g[N]=J(t,i,z)}},G=function(){var e=0|(X+=2*H);X-=e;for(var t=0;t<c;t++)l.m_re[t]=g[t]*_[t],l.m_im[t]=g[t]*_[m+t];s.blit(_,2*m,_,0,c-m),l.inplace(!1),l.unpack(y,T,k,A),q(L,y,T,0,0,1*V/m),q(L+1,k,A,0,0,1*(V+e)/m),s.blit(k,0,w,0,b),s.blit(A,0,P,0,b),l.repack(y,T,k,A),l.inplace(!0);var i=p.length;for(s.blit(p,j,p,0,i-j),t=i-j;t<i;t++)p[t]=0;var d=0,h=W;for(t=0;t<V;t++)Math.abs(2*l.m_re[t])>d&&(d=Math.abs(2*l.m_re[t]));for(t=0;t<c-V;t++)Math.abs(l.m_re[t+V+e]+l.m_im[t])>d&&(d=Math.abs(l.m_re[t+V+e]+l.m_im[t]));for(t=c-V;t<c;t++)Math.abs(2*l.m_im[t])>d&&(d=Math.abs(2*l.m_im[t]));var u=1/Math.floor(1*c/(2*V));for(h*d>u&&(h=u/d),t=0;t<c;t++)p[t]+=h*l.m_re[t],p[t+V+e]+=h*l.m_im[t];return L+=2,j=2*V+e};return Q.process=function(e){var i=e[0].length,d=e[0];if(e.length>1){d=s.float_array(e[0].length);for(var u=1/e.length,l=0;l<e.length;l++)for(var f=0;f<i;f++)d[f]+=u*e[l][f]}if(1==h){if(z+N>0){var g=z+N+i,v=[];for(l=0;l<e.length;l++){var b=s.float_array(g);s.blit(p,0,b,0,z),s.blit(_,0,b,z,N),s.blit(e[l],0,b,z+N,i),v.push(b)}Q.flush(0),i=g,e=v}return D+=i/t,M+=i/t,e}var y=N+i-(c-m),T=2*Math.floor(Math.max(0,y)/(2*m)),k=z+V*T+Math.floor(X+H*T);z>k&&(k=z);var A=s.float_array(k);s.blit(p,0,A,0,z);for(var w=0,P=z,E=0,x=0;;){var R=c+m-N;if(w+R>i){s.blit(d,w,_,N,i-w),N+=i-w,w=i;break}R<=0?N-=2*m:(s.blit(d,w,_,N,R),w+=R,N=c-m),x=G(),D+=2*m/t,M+=x/t,(E=P+x-k)<0&&(E=0),s.blit(p,0,A,P,x-E),P+=x}s.blit(p,x-E,p,0,E),z=E;var F=[];for(l=0;l<e.length;l++)F.push(A);return F},Q}},function(e,t,i){"use strict";var s=i(0);e.exports=function(e){for(var t=1<<e,i={m_logN:e,m_N:t,m_invN:1/t,m_re:s.float_array(t),m_im:s.float_array(t),m_revTgt:new Array(t)},d=0;d<t;d++){for(var h=d,u=0,c=0;c<e;c++)u<<=1,u|=1&h,h>>=1;i.m_revTgt[d]=u}i.twiddleRe=s.float_array(i.m_logN),i.twiddleIm=s.float_array(i.m_logN);for(var l=1,f=0;f<i.m_logN;f++){var _=2*l*Math.PI*i.m_invN;i.twiddleRe[f]=Math.cos(_),i.twiddleIm[f]=Math.sin(_),l<<=1}i.inplace=function(e){var t=i.m_re,s=i.m_im,d=i.m_N,h=i.m_logN,u=d>>1,c=d>>1,l=d;if(e)for(var f=1/d,_=0;_<d;_++)t[_]*=f,s[_]*=f;for(var p=0;p<h;p++){var m=i.twiddleRe[p],V=i.twiddleIm[p];e||(V*=-1);for(var g=0;g<d;){for(var v=g,b=g+c,y=1,T=0,k=0;k<u;k++){var A=t[v],w=s[v],P=t[b],E=s[b];t[v]=A+P,s[v]=w+E,P=A-P,E=w-E,t[b]=P*y-E*T,s[b]=P*T+E*y,v++,b++;var x=y;y=y*m-T*V,T=x*V+T*m}g+=l}u>>=1,c>>=1,l>>=1}for(var R,F,O=i.m_revTgt,S=0;S<d;S++)O[S]>S&&(F=t[R=O[S]],t[R]=t[S],t[S]=F,F=s[R],s[R]=s[S],s[S]=F)};var p=t>>1;return i.unpack=function(e,s,d,h){e[0]=i.m_re[0],d[0]=i.m_im[0],s[0]=h[0]=0,e[p]=i.m_re[p],d[p]=i.m_im[p],s[p]=h[p]=0;for(var u=1;u<p;u++)e[u]=(i.m_re[u]+i.m_re[t-u])/2,s[u]=(i.m_im[u]-i.m_im[t-u])/2,d[u]=(i.m_im[u]+i.m_im[t-u])/2,h[u]=(-i.m_re[u]+i.m_re[t-u])/2},i.repack=function(e,s,d,h){i.m_re[0]=e[0],i.m_im[0]=d[0],i.m_re[p]=e[p],i.m_im[p]=d[p];for(var u=1;u<p;u++)i.m_re[u]=e[u]-h[u],i.m_im[u]=s[u]+d[u],i.m_re[t-u]=e[u]+h[u],i.m_im[t-u]=-s[u]+d[u]},i}}])},e.exports=s()}])},893:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=class Bisector{constructor(e){this.lower=e.start,this.upper=e.end,this.onprocess=e.process,this.position=0,this.n=0}iterate(){return this.n++,this.position=Math.floor((this.lower+this.upper)/2),this.onprocess(this.lower,this.upper,this.position)}start(){return this.iterate(),this}left(){return this.upper=this.position,this.iterate()}right(){return this.lower=this.position,this.iterate()}};t.default=i},523:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=new class{hasTypedArrays(){return!!window.Uint32Array}hasWebAssembly(){return!!window.WebAssembly}hasWebAudio(){return!(!window.AudioContext&&!window.webkitAudioContext)}hasFlash(){return!1}hasAudio(){return this.hasWebAudio()}isBlacklisted(e){return!1}isSlow(){return!1}isTooSlow(){return!1}supported(e){return"OGVDecoder"===e?this.hasWebAssembly():"OGVPlayer"===e&&(this.supported("OGVDecoder")&&this.hasAudio())}};t.default=i},408:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(i(580));class OGVDecoderAudioProxy extends((0,d.default)({loadedMetadata:!1,audioFormat:null,audioBuffer:null,cpuTime:0})){init(e){this.proxy("init",[],e)}processHeader(e,t){this.proxy("processHeader",[e],t,[e])}processAudio(e,t){this.proxy("processAudio",[e],t,[e])}close(){this.terminate()}}var h=OGVDecoderAudioProxy;t.default=h},319:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(i(580));class OGVDecoderVideoProxy extends((0,d.default)({loadedMetadata:!1,videoFormat:null,frameBuffer:null,cpuTime:0})){init(e){this.proxy("init",[],e)}processHeader(e,t){this.proxy("processHeader",[e],t,[e])}processFrame(e,t){this.proxy("processFrame",[e],t,[e])}close(){this.terminate()}sync(){this.proxy("sync",[],(()=>{}))}recycleFrame(e){this.proxy("recycleFrame",[e],(()=>{}),[e.y.bytes.buffer,e.u.bytes.buffer,e.v.bytes.buffer])}}var h=OGVDecoderVideoProxy;t.default=h},445:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(i(539)),h="1.8.9-20220406232920-cb5f7ff",u={OGVDemuxerOggW:"ogv-demuxer-ogg-wasm.js",OGVDemuxerWebMW:"ogv-demuxer-webm-wasm.js",OGVDecoderAudioOpusW:"ogv-decoder-audio-opus-wasm.js",OGVDecoderAudioVorbisW:"ogv-decoder-audio-vorbis-wasm.js",OGVDecoderVideoTheoraW:"ogv-decoder-video-theora-wasm.js",OGVDecoderVideoVP8W:"ogv-decoder-video-vp8-wasm.js",OGVDecoderVideoVP8MTW:"ogv-decoder-video-vp8-mt-wasm.js",OGVDecoderVideoVP9W:"ogv-decoder-video-vp9-wasm.js",OGVDecoderVideoVP9SIMDW:"ogv-decoder-video-vp9-simd-wasm.js",OGVDecoderVideoVP9MTW:"ogv-decoder-video-vp9-mt-wasm.js",OGVDecoderVideoVP9SIMDMTW:"ogv-decoder-video-vp9-simd-mt-wasm.js",OGVDecoderVideoAV1W:"ogv-decoder-video-av1-wasm.js",OGVDecoderVideoAV1SIMDW:"ogv-decoder-video-av1-simd-wasm.js",OGVDecoderVideoAV1MTW:"ogv-decoder-video-av1-mt-wasm.js",OGVDecoderVideoAV1SIMDMTW:"ogv-decoder-video-av1-simd-mt-wasm.js"};var c=class OGVLoaderBase{constructor(){this.base=this.defaultBase()}defaultBase(){}wasmSupported(){return d.default.wasmSupported()}scriptForClass(e){return u[e]}urlForClass(e){var t=this.scriptForClass(e);if(t)return this.urlForScript(t);throw new Error("asked for URL for unknown class "+e)}urlForScript(e){if(e){var t=this.base;return void 0===t?t="":t+="/",t+e+"?version="+encodeURIComponent(h)}throw new Error("asked for URL for unknown script "+e)}loadClass(e,t,i){i=i||{};var s=this.getGlobal(),d=this.urlForClass(e),classWrapper=t=>((t=t||{}).locateFile=e=>"data:"===e.slice(0,5)?e:this.urlForScript(e),t.mainScriptUrlOrBlob=this.scriptForClass(e)+"?version="+encodeURIComponent(h),s[e](t));"function"==typeof s[e]?t(classWrapper):this.loadScript(d,(()=>{t(classWrapper)}))}};t.default=c},964:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(i(408)),h=s(i(319)),u=s(i(445)),c={audio:{proxy:d.default,worker:"ogv-worker-audio.js"},video:{proxy:h.default,worker:"ogv-worker-video.js"}},l={OGVDecoderAudioOpusW:"audio",OGVDecoderAudioVorbisW:"audio",OGVDecoderVideoTheoraW:"video",OGVDecoderVideoVP8W:"video",OGVDecoderVideoVP9W:"video",OGVDecoderVideoVP9SIMDW:"video",OGVDecoderVideoAV1W:"video",OGVDecoderVideoAV1SIMDW:"video"};class OGVLoaderWeb extends u.default{constructor(){super(),this.scriptStatus={},this.scriptCallbacks={}}getGlobal(){return window}defaultBase(){for(var e,t,i=document.querySelectorAll("script"),s=/^(?:|(.*)\/)ogv(?:-support|-es2017)?\.js(?:\?|#|$)/,d=0;d<i.length;d++)if((e=i[d].getAttribute("src"))&&(t=e.match(s)))return t[1]}loadClass(e,t,i){(i=i||{}).worker?this.workerProxy(e,t):super.loadClass(e,t,i)}loadScript(e,t){if("done"==this.scriptStatus[e])t();else if("loading"==this.scriptStatus[e])this.scriptCallbacks[e].push(t);else{this.scriptStatus[e]="loading",this.scriptCallbacks[e]=[t];var i=document.createElement("script"),done=t=>{var i=this.scriptCallbacks[e];delete this.scriptCallbacks[e],this.scriptStatus[e]="done",i.forEach((e=>{e()}))};i.addEventListener("load",done),i.addEventListener("error",done),i.src=e,document.querySelector("head").appendChild(i)}}workerProxy(e,t){var i=c[l[e]];if(!i)throw new Error("Requested worker for class with no proxy: "+e);var s,d=i.proxy,h=i.worker,u=this.urlForScript(this.scriptForClass(e)),_=this.urlForScript(h),p=function construct(t){return new d(s,e,t)};if(_.match(/^https?:|\/\//i)){var m,V,g,v,b,y=!1,T=!1;function completionCheck(){if(1==y&&1==T){var e=g+" "+v+"\nOGVLoader.base = "+JSON.stringify(f.base);try{b=new Blob([e],{type:"application/javascript"})}catch(t){window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder,(b=new BlobBuilder).append(e),b=b.getBlob()}s=new Worker(URL.createObjectURL(b)),t((function(e){return Promise.resolve(new p(e))}))}}(m=new XMLHttpRequest).open("GET",u,!0),m.onreadystatechange=function(){4==m.readyState&&200==m.status&&(g=m.responseText,y=!0,completionCheck())},m.send(),(V=new XMLHttpRequest).open("GET",_,!0),V.onreadystatechange=function(){4==V.readyState&&200==V.status&&(v=V.responseText,T=!0,completionCheck())},V.send()}else s=new Worker(_),t((function(e){return Promise.resolve(new p(e))}))}}var f=new OGVLoaderWeb,_=f;t.default=_},759:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(i(309)),h={MEDIA_ERR_ABORTED:1,MEDIA_ERR_NETWORK:2,MEDIA_ERR_DECODE:3,MEDIA_ERR_SRC_NOT_SUPPORTED:4};class OGVMediaError{constructor(e,t){this.code=e,this.message=t}}(0,d.default)(OGVMediaError,h),(0,d.default)(OGVMediaError.prototype,h);var u=OGVMediaError;t.default=u},278:(e,t)=>{"use strict";function split(e,t,i){var s=e.split(t,i).map((e=>function trim(e){return e.replace(/^\s+/,"").replace(/\s+$/,"")}(e)));if("number"==typeof i)for(;s.length<i;)s.push(null);return s}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=class OGVMediaType{constructor(e){e=String(e),this.major=null,this.minor=null,this.codecs=null;var t=split(e,";");if(t.length){var i=t.shift();if(i){var s=split(i,"/",2);this.major=s[0],this.minor=s[1]}for(var d in t){var h=t[d].match(/^codecs\s*=\s*"(.*?)"$/);if(h){this.codecs=split(h[1],",");break}}}}};t.default=i},869:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d,h=s(i(731)),u=s(i(936)),c=s(i(848)),l=s(i(964)),f=s(i(893)),_=s(i(309)),p=s(i(759)),m=s(i(278)),V=s(i(168)),g=s(i(625)),v=s(i(302)),b=(()=>{if("function"==typeof setImmediate)return setImmediate;var e=new MessageChannel,t=[];return e.port1.onmessage=e=>{t.shift()()},function nextTick(i){t.push(i),e.port2.postMessage({})}})(),y={NETWORK_EMPTY:0,NETWORK_IDLE:1,NETWORK_LOADING:2,NETWORK_NO_SOURCE:3,HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},T="INITIAL",k="SEEKING_END",A="LOADED",w="PRELOAD",P="READY",E="PLAYING",x="SEEKING",R="ERROR",F="NOT_SEEKING",O="BISECT_TO_TARGET",S="BISECT_TO_KEYPOINT",C="LINEAR_TO_TARGET",B="exact",D="fast";function OGVJSElement(){var e=document.createElement("ogvjs");return Object.setPrototypeOf?Object.setPrototypeOf(e,Object.getPrototypeOf(this)):e.__proto__=this.__proto__,e}d="undefined"==typeof performance||void 0===typeof performance.now?Date.now:performance.now.bind(performance),OGVJSElement.prototype=Object.create(HTMLElement.prototype,{});class OGVPlayer extends OGVJSElement{constructor(e){if(super(),(e=e||{}).base=e.base||l.default.base,this._options=e,this._instanceId="ogvjs"+ ++OGVPlayer.instanceCount,void 0!==e.worker?this._enableWorker=!!e.worker:this._enableWorker=!!window.Worker,!l.default.wasmSupported())throw new Error("WebAssembly not supported");this._enableThreading=!!e.threading,this._enableSIMD=!!e.simd,this._state=T,this._seekState=F,this._detectedType=null,this._canvas=document.createElement("canvas"),this._frameSink=null,this.className=this._instanceId,(0,_.default)(this,y),this._view=this._canvas,this._view.style.position="absolute",this._view.style.top="0",this._view.style.left="0",this._view.style.width="100%",this._view.style.height="100%",this._view.style.objectFit="contain",this.appendChild(this._view),this._startTime=d(),this._codec=null,this._audioInfo=null,this._videoInfo=null,this._actionQueue=[],this._audioFeeder=null,this._muted=!1,this._initialPlaybackPosition=0,this._initialPlaybackOffset=0,this._prebufferingAudio=!1,this._initialSeekTime=0,this._currentSrc="",this._crossOrigin=null,this._streamEnded=!1,this._mediaError=null,this._dataEnded=!1,this._byteLength=0,this._duration=null,this._lastSeenTimestamp=null,this._nextProcessingTimer,this._nextFrameTimer=null,this._loading=!1,this._started=!1,this._paused=!0,this._ended=!1,this._startedPlaybackInDocument=!1,this._stream=void 0,this._framesProcessed=0,this._targetPerFrameTime=1e3/60,this._actualPerFrameTime=0,this._totalFrameTime=0,this._totalFrameCount=0,this._playTime=0,this._bufferTime=0,this._drawingTime=0,this._proxyTime=0,this._totalJitter=0,this._droppedAudio=0,this._delayedAudio=0,this._lateFrames=0,this._poster="",this._thumbnail=null,this._frameEndTimestamp=0,this._audioEndTimestamp=0,this._decodedFrames=[],this._pendingFrames=[],this._lastFrameDecodeTime=0,this._lastFrameVideoCpuTime=0,this._lastFrameAudioCpuTime=0,this._lastFrameDemuxerCpuTime=0,this._lastFrameDrawingTime=0,this._lastFrameBufferTime=0,this._lastFrameProxyTime=0,this._lastVideoCpuTime=0,this._lastAudioCpuTime=0,this._lastDemuxerCpuTime=0,this._lastBufferTime=0,this._lastProxyTime=0,this._lastDrawingTime=0,this._lastFrameTimestamp=0,this._currentVideoCpuTime=0,this._lastTimeUpdate=0,this._timeUpdateInterval=250,this._seekTargetTime=0,this._bisectTargetTime=0,this._seekMode=null,this._lastSeekPosition=null,this._seekBisector=null,this._didSeek=null,this._depth=0,this._needProcessing=!1,this._pendingFrame=0,this._pendingAudio=0,this._framePipelineDepth=8,this._frameParallelism=this._enableThreading?Math.min(16,navigator.hardwareConcurrency)||1:0,this._audioPipelineDepth=12,this._videoInfo=null,this._audioInfo=null,this._width=0,this._height=0,this._volume=1,this._playbackRate=1,Object.defineProperties(this,{src:{get:function getSrc(){return this.getAttribute("src")||""},set:function setSrc(e){this.setAttribute("src",e),this._loading=!1,this._prepForLoad("interactive")}},buffered:{get:function getBuffered(){var e;return e=this._stream&&this._byteLength&&this._duration?this._stream.getBufferedRanges().map((e=>e.map((e=>e/this._stream.length*this._duration)))):[[0,0]],new V.default(e)}},seekable:{get:function getSeekable(){return this.duration<1/0&&this._stream&&this._stream.seekable&&this._codec&&this._codec.seekable?new V.default([[0,this._duration]]):new V.default([])}},currentTime:{get:function getCurrentTime(){return this._state==x?this._seekTargetTime:this._codec?this._state!=E||this._paused?this._initialPlaybackOffset:this._getPlaybackTime():this._initialSeekTime},set:function setCurrentTime(e){this._seek(e,B)}},duration:{get:function getDuration(){return this._codec&&this._codec.loadedMetadata?null!==this._duration?this._duration:1/0:NaN}},paused:{get:function getPaused(){return this._paused}},ended:{get:function getEnded(){return this._ended}},seeking:{get:function getSeeking(){return this._state==x}},muted:{get:function getMuted(){return this._muted},set:function setMuted(e){this._muted=e,this._audioFeeder?this._audioFeeder.muted=this._muted:this._started&&!this._muted&&this._codec&&this._codec.hasAudio&&(this._log("unmuting: switching from timer to audio clock"),this._initAudioFeeder(),this._startPlayback(this._audioEndTimestamp)),this._fireEventAsync("volumechange")}},poster:{get:function getPoster(){return this._poster},set:function setPoster(e){if(this._poster=e,!this._started){this._thumbnail&&this.removeChild(this._thumbnail);var t=new Image;t.crossOrigin=this.crossOrigin,t.src=this._poster,t.className="ogvjs-poster",t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.style.objectFit="contain",t.style.visibility="hidden",t.addEventListener("load",(()=>{this._thumbnail===t&&(OGVPlayer.styleManager.appendRule("."+this._instanceId,{width:t.naturalWidth+"px",height:t.naturalHeight+"px"}),t.style.visibility="visible")})),this._thumbnail=t,this.appendChild(t)}}},videoWidth:{get:function getVideoWidth(){return this._videoInfo?this._videoInfo.displayWidth:0}},videoHeight:{get:function getVideoHeight(){return this._videoInfo?this._videoInfo.displayHeight:0}},ogvjsVideoFrameRate:{get:function getOgvJsVideoFrameRate(){return this._videoInfo?0==this._videoInfo.fps?this._totalFrameCount/(this._totalFrameTime/1e3):this._videoInfo.fps:0}},ogvjsAudioChannels:{get:function getOgvJsAudioChannels(){return this._audioInfo?this._audioInfo.channels:0}},ogvjsAudioSampleRate:{get:function getOgvJsAudioChannels(){return this._audioInfo?this._audioInfo.rate:0}},width:{get:function getWidth(){return this._width},set:function setWidth(e){this._width=parseInt(e,10),this.style.width=this._width+"px"}},height:{get:function getHeight(){return this._height},set:function setHeight(e){this._height=parseInt(e,10),this.style.height=this._height+"px"}},autoplay:{get:function getAutoplay(){return!1},set:function setAutoplay(e){}},controls:{get:function getControls(){return!1},set:function setControls(e){}},loop:{get:function getLoop(){return!1},set:function setLoop(e){}},crossOrigin:{get:function getCrossOrigin(){return this._crossOrigin},set:function setCrossOrigin(e){switch(e){case null:this._crossOrigin=e,this.removeAttribute("crossorigin");break;default:e="anonymous";case"":case"anonymous":case"use-credentials":this._crossOrigin=e,this.setAttribute("crossorigin",e)}this._thumbnail&&(this._thumbnail.crossOrigin=e)}},currentSrc:{get:function getCurrentSrc(){return this._currentSrc}},defaultMuted:{get:function getDefaultMuted(){return!1}},defaultPlaybackRate:{get:function getDefaultPlaybackRate(){return 1}},error:{get:function getError(){return this._state===R?this._mediaError?this._mediaError:new p.default("unknown error occurred in media procesing"):null}},preload:{get:function getPreload(){return this.getAttribute("preload")||""},set:function setPreload(e){this.setAttribute("preload",e)}},readyState:{get:function getReadyState(){return this._stream&&this._codec&&this._codec.loadedMetadata?OGVPlayer.HAVE_ENOUGH_DATA:OGVPlayer.HAVE_NOTHING}},networkState:{get:function getNetworkState(){return this._stream?this._stream.waiting?OGVPlayer.NETWORK_LOADING:OGVPlayer.NETWORK_IDLE:this.readyState==OGVPlayer.HAVE_NOTHING?OGVPlayer.NETWORK_EMPTY:OGVPlayer.NETWORK_NO_SOURCE}},playbackRate:{get:function getPlaybackRate(){return this._playbackRate},set:function setPlaybackRate(e){var t=Number(e)||1;this._audioFeeder?this._audioFeeder.tempo=t:this._paused||(this._initialPlaybackOffset=this._getPlaybackTime(),this._initialPlaybackPosition=t*d()/1e3),this._playbackRate=t,this._fireEventAsync("ratechange")}},played:{get:function getPlayed(){return new V.default([[0,this.currentTime]])}},volume:{get:function getVolume(){return this._volume},set:function setVolume(e){this._volume=+e,this._audioFeeder&&(this._audioFeeder.volume=this._volume),this._fireEventAsync("volumechange")}}}),this.onframecallback=null,this.onloadstate=null,this.onprogress=null,this.onsuspend=null,this.onabort=null,this.onemptied=null,this.onstalled=null,this.onloadedmetadata=null,this.onloadeddata=null,this.oncanplay=null,this.oncanplaythrough=null,this.onplaying=null,this.onwaiting=null,this.onseeking=null,this.onseeked=null,this.onended=null,this.ondurationchange=null,this.ontimeupdate=null,this.onplay=null,this.onpause=null,this.onratechange=null,this.onresize=null,this.onvolumechange=null,this.onaudiofeedercreated=null}_time(e){var t=d();e();var i=d()-t;return this._lastFrameDecodeTime+=i,i}_log(e){var t=this._options;if(t.debug){var i=d()-this._startTime;t.debugFilter&&!e.match(t.debugFilter)||console.log("["+Math.round(10*i)/10+"ms] "+e)}}_fireEvent(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this._log("fireEvent "+e);var i,s="function"==typeof Event;for(var d in s?i=new CustomEvent(e):(i=document.createEvent("Event")).initEvent(e,!1,!1),t)t.hasOwnProperty(d)&&(i[d]=t[d]);var h=this.dispatchEvent(i);!s&&"resize"===e&&this.onresize&&h&&this.onresize.call(this,i)}_fireEventAsync(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this._log("fireEventAsync "+e),b((()=>{this._fireEvent(e,t)}))}static initSharedAudioContext(){var e=document.createElement("audio");e.src=v.default,e.play(),c.default.initSharedAudioContext()}_initAudioFeeder(){var e=this._options,t={bufferSize:8192};e.audioContext&&(t.audioContext=e.audioContext),e.audioDestination&&(t.output=e.audioDestination),e.audioBackendFactory&&(t.backendFactory=e.audioBackendFactory);var i=this._audioFeeder=new c.default(t);i.init(this._audioInfo.channels,this._audioInfo.rate),this.onaudiofeedercreated&&this.onaudiofeedercreated(this._audioFeeder),i.bufferThreshold=1,i.volume=this.volume,i.muted=this.muted,i.tempo=this.playbackRate,i.onbufferlow=()=>{this._log("onbufferlow"),this._stream&&(this._stream.buffering||this._stream.seeking)||this._pendingAudio||this._pingProcessing()},i.onstarved=()=>{this._dataEnded?this._log("onstarved: appear to have reached end of audio"):(this._log("onstarved: halting audio due to starvation"),this._stopPlayback(),this._prebufferingAudio=!0),this._isProcessing()||this._pingProcessing(0)}}_startPlayback(e){if(this._audioFeeder){this._audioFeeder.start();var t=this._audioFeeder.getPlaybackState();this._initialPlaybackPosition=t.playbackPosition}else this._initialPlaybackPosition=this._playbackRate*d()/1e3;void 0!==e&&(this._initialPlaybackOffset=e),this._prebufferingAudio=!1,this._log("continuing at "+this._initialPlaybackPosition+", "+this._initialPlaybackOffset)}_stopPlayback(){this._initialPlaybackOffset=this._getPlaybackTime(),this._log("pausing at "+this._initialPlaybackOffset),this._audioFeeder&&this._audioFeeder.stop()}_getPlaybackTime(e){return this._prebufferingAudio||this._paused?this._initialPlaybackOffset:(this._audioFeeder?(e=e||this._audioFeeder.getPlaybackState()).playbackPosition:this._playbackRate*d()/1e3)-this._initialPlaybackPosition+this._initialPlaybackOffset}_stopVideo(){this._log("STOPPING"),this._state=T,this._seekState=F,this._started=!1,this._ended=!1,this._frameEndTimestamp=0,this._audioEndTimestamp=0,this._lastFrameDecodeTime=0,this._prebufferingAudio=!1,this._actionQueue.splice(0,this._actionQueue.length),this._stream&&(this._stream.abort(),this._stream=null,this._streamEnded=!1),this._codec&&(this._codec.close(),this._codec=null,this._pendingFrame=0,this._pendingAudio=0,this._dataEnded=!1),this._videoInfo=null,this._audioInfo=null,this._audioFeeder&&(this._audioFeeder.close(),this._audioFeeder=null),this._nextProcessingTimer&&(clearTimeout(this._nextProcessingTimer),this._nextProcessingTimer=null),this._nextFrameTimer&&(clearTimeout(this._nextFrameTimer),this._nextFrameTimer=null),this._frameSink&&(this._frameSink.clear(),this._frameSink=null),this._decodedFrames&&(this._decodedFrames=[]),this._pendingFrames&&(this._pendingFrames=[]),this._initialSeekTime=0,this._initialPlaybackPosition=0,this._initialPlaybackOffset=0,this._duration=null}_doFrameComplete(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._startedPlaybackInDocument&&!document.body.contains(this)&&b((()=>{this.stop()}));var t=d(),i=t-this._lastFrameTimestamp,s=this._actualPerFrameTime-this._targetPerFrameTime;this._totalJitter+=Math.abs(s),this._playTime+=i;var h={cpuTime:this._lastFrameDecodeTime,drawingTime:this._drawingTime-this._lastFrameDrawingTime,bufferTime:this._bufferTime-this._lastFrameBufferTime,proxyTime:this._proxyTime-this._lastFrameProxyTime,demuxerTime:0,videoTime:0,audioTime:0,clockTime:this._actualPerFrameTime,late:e.dropped,dropped:e.dropped};function n(e){return Math.round(10*e)/10}this._codec&&(h.demuxerTime=this._codec.demuxerCpuTime-this._lastFrameDemuxerCpuTime,h.videoTime+=this._currentVideoCpuTime-this._lastFrameVideoCpuTime,h.audioTime+=this._codec.audioCpuTime-this._lastFrameAudioCpuTime),h.cpuTime+=h.demuxerTime,this._lastFrameDecodeTime=0,this._lastFrameTimestamp=t,this._codec?(this._lastFrameVideoCpuTime=this._currentVideoCpuTime,this._lastFrameAudioCpuTime=this._codec.audioCpuTime,this._lastFrameDemuxerCpuTime=this._codec.demuxerCpuTime):(this._lastFrameVideoCpuTime=0,this._lastFrameAudioCpuTime=0,this._lastFrameDemuxerCpuTime=0),this._lastFrameDrawingTime=this._drawingTime,this._lastFrameBufferTime=this._bufferTime,this._lastFrameProxyTime=this._proxyTime,this._log("drew frame "+e.frameEndTimestamp+": clock time "+n(i)+" (jitter "+n(s)+") cpu: "+n(h.cpuTime)+" (mux: "+n(h.demuxerTime)+" buf: "+n(h.bufferTime)+" draw: "+n(h.drawingTime)+" proxy: "+n(h.proxyTime)+") vid: "+n(h.videoTime)+" aud: "+n(h.audioTime)),this._fireEventAsync("framecallback",h),(!this._lastTimeUpdate||t-this._lastTimeUpdate>=this._timeUpdateInterval)&&(this._lastTimeUpdate=t,this._fireEventAsync("timeupdate")),this._codec&&e.yCbCrBuffer&&this._codec.recycleFrame(e.yCbCrBuffer)}_seekStream(e){this._stream.seeking&&this._stream.abort(),this._stream.buffering&&this._stream.abort(),this._streamEnded=!1,this._dataEnded=!1,this._ended=!1,this._stream.seek(e).then((()=>{this._readBytesAndWait()})).catch((e=>{this._onStreamError(e)}))}_onStreamError(e){"AbortError"===e.name?this._log("i/o promise canceled; ignoring"):(this._log("i/o error: "+e),this._mediaError=new p.default(p.default.MEDIA_ERR_NETWORK,String(e)),this._state=R,this._stopPlayback())}_seek(e,t){if(this._log("requested seek to "+e+", mode "+t),this.readyState==this.HAVE_NOTHING)return this._log("not yet loaded; saving seek position for later"),void(this._initialSeekTime=e);if(this._stream&&!this._stream.seekable)throw new Error("Cannot seek a non-seekable stream");if(this._codec&&!this._codec.seekable)throw new Error("Cannot seek in a non-seekable file");var prepForSeek=i=>{this._stream&&this._stream.buffering&&this._stream.abort(),this._stream&&this._stream.seeking&&this._stream.abort(),this._actionQueue.splice(0,this._actionQueue.length),this._stopPlayback(),this._prebufferingAudio=!1,this._audioFeeder&&this._audioFeeder.flush(),this._state=x,this._seekTargetTime=e,this._seekMode=t,this._codec?this._codec.flush(i):i()};prepForSeek((()=>{this._isProcessing()||this._pingProcessing(0)})),this._actionQueue.push((()=>{prepForSeek((()=>{this._doSeek(e)}))}))}_doSeek(e){this._streamEnded=!1,this._dataEnded=!1,this._ended=!1,this._state=x,this._seekTargetTime=e,this._lastSeekPosition=-1,this._decodedFrames=[],this._pendingFrames=[],this._pendingFrame=0,this._pendingAudio=0,this._didSeek=!1,this._codec.seekToKeypoint(e,(t=>{if(t)return this._seekState=C,this._fireEventAsync("seeking"),this._didSeek?void 0:void this._pingProcessing();this._codec.getKeypointOffset(e,(e=>{e>0?(this._seekState=C,this._seekStream(e)):(this._seekState=O,this._startBisection(this._seekTargetTime)),this._fireEventAsync("seeking")}))}))}_startBisection(e){var t=Math.max(0,this._stream.length-65536);this._bisectTargetTime=e,this._seekBisector=new f.default({start:0,end:t,process:(e,t,i)=>i!=this._lastSeekPosition&&(this._lastSeekPosition=i,this._codec.flush((()=>{this._seekStream(i)})),!0)}),this._seekBisector.start()}_continueSeekedPlayback(){this._seekState=F,this._state=P,this._frameEndTimestamp=this._codec.frameTimestamp,this._audioEndTimestamp=this._codec.audioTimestamp,this._codec.hasAudio?this._seekTargetTime=this._codec.audioTimestamp:this._seekTargetTime=this._codec.frameTimestamp,this._initialPlaybackOffset=this._seekTargetTime;var finishedSeeking=()=>{this._lastTimeUpdate=this._seekTargetTime,this._fireEventAsync("timeupdate"),this._fireEventAsync("seeked"),this._isProcessing()||this._pingProcessing()};if(this._codec.hasVideo&&this._decodedFrames.length){var e=this._decodedFrames.shift();this._drawFrame(e.yCbCrBuffer),finishedSeeking()}else{if(this._codec.hasVideo&&this._codec.frameReady)return this._codec.decodeFrame((e=>{e&&this._drawFrame(this._codec.frameBuffer),finishedSeeking()})),void this._codec.sync();finishedSeeking()}}_drawFrame(e){this._thumbnail&&(this.removeChild(this._thumbnail),this._thumbnail=null),this._frameSink.drawFrame(e)}_doProcessLinearSeeking(){var e;if(e=this._codec.hasVideo?this._targetPerFrameTime/1e3:1/256,this._codec.hasVideo){if(this._pendingFrame)return;if(!this._codec.frameReady)return void this._codec.process((e=>{e?this._pingProcessing():this._streamEnded?(this._log("stream ended during linear seeking on video"),this._dataEnded=!0,this._continueSeekedPlayback()):this._readBytesAndWait()}));if(this._seekMode===D&&this._codec.keyframeTimestamp==this._codec.frameTimestamp)return void this._continueSeekedPlayback();if(this._codec.frameTimestamp<=this._seekTargetTime){var t=this._codec.frameTimestamp;return this._pendingFrame++,this._pendingFrames.push({frameEndTimestamp:t}),this._decodedFrames.splice(0,this._decodedFrames.length),this._codec.decodeFrame((e=>{this._pendingFrame--,this._pendingFrames.shift(),this._decodedFrames.push({yCbCrBuffer:this._codec.frameBuffer,videoCpuTime:this._codec.videoCpuTime,frameEndTimestamp:t}),this._pingProcessing()})),void this._codec.sync()}if(!this._codec.hasAudio)return void this._continueSeekedPlayback()}if(this._codec.hasAudio){if(this._pendingAudio)return;return this._codec.audioReady?this._codec.audioTimestamp+e<this._seekTargetTime?void this._codec.decodeAudio((()=>{this._pingProcessing()})):void this._continueSeekedPlayback():void this._codec.process((e=>{e?this._pingProcessing():this._streamEnded?(this._log("stream ended during linear seeking on audio"),this._dataEnded=!0,this._continueSeekedPlayback()):this._readBytesAndWait()}))}}_doProcessBisectionSeek(){var e,t;if(this._codec.hasVideo)t=this._codec.frameTimestamp,e=this._targetPerFrameTime/1e3;else{if(!this._codec.hasAudio)throw new Error("Invalid seek state; no audio or video track available");t=this._codec.audioTimestamp,e=1/256}t<0?this._codec.process((e=>{if(e)this._pingProcessing();else if(this._streamEnded){if(this._log("stream ended during bisection seek"),!this._seekBisector.right())throw this._log("failed going back"),new Error("not sure what to do")}else this._readBytesAndWait()})):t-e/2>this._bisectTargetTime?this._seekBisector.left()||(this._log("close enough (left)"),this._seekTargetTime=t,this._continueSeekedPlayback()):t+e/2<this._bisectTargetTime?this._seekBisector.right()||(this._log("close enough (right)"),this._seekState=C,this._pingProcessing()):this._seekState==O&&this._codec.hasVideo&&this._codec.keyframeTimestamp<this._codec.frameTimestamp?(this._log("finding the keypoint now"),this._seekState=S,this._startBisection(this._codec.keyframeTimestamp)):(this._log("straight seeking now"),this._seekState=C,this._pingProcessing())}_setupVideo(){this._videoInfo.fps>0?this._targetPerFrameTime=1e3/this._videoInfo.fps:this._targetPerFrameTime=16.667,this._canvas.width=this._videoInfo.displayWidth,this._canvas.height=this._videoInfo.displayHeight,OGVPlayer.styleManager.appendRule("."+this._instanceId,{width:this._videoInfo.displayWidth+"px",height:this._videoInfo.displayHeight+"px"});var e={};void 0!==this._options.webGL&&(e.webGL=this._options.webGL),this._options.forceWebGL&&(e.webGL="required"),this._frameSink=h.default.attach(this._canvas,e)}_doProcessing(){if(this._didSeek&&(this._didSeek=!1),this._nextProcessingTimer=null,this._isProcessing(),this._depth>0)throw new Error("REENTRANCY FAIL: doProcessing recursing unexpectedly");var e=0;do{if(this._needProcessing=!1,this._depth++,this._doProcessingLoop(),this._depth--,this._needProcessing&&this._isProcessing())throw new Error("REENTRANCY FAIL: waiting on input or codec but asked to keep processing");++e>500&&(this._log("stuck in processing loop; breaking with timer"),this._needProcessing=0,this._pingProcessing(0))}while(this._needProcessing)}_doProcessingLoop(){if(this._actionQueue.length)this._actionQueue.shift()();else if(this._state==T)this._doProcessInitial();else if(this._state==k)this._doProcessSeekingEnd();else if(this._state==A)this._doProcessLoaded();else if(this._state==w)this._doProcessPreload();else if(this._state==P)this._doProcessReady();else if(this._state==x)this._doProcessSeeking();else if(this._state==E)this._doProcessPlay();else{if(this._state!=R)throw new Error("Unexpected OGVPlayer state "+this._state);this._doProcessError()}}_doProcessInitial(){if(this._codec.loadedMetadata){if(!this._codec.hasVideo&&!this._codec.hasAudio)throw new Error("No audio or video found, something is wrong");this._codec.hasAudio&&(this._audioInfo=this._codec.audioFormat),this._codec.hasVideo&&(this._videoInfo=this._codec.videoFormat,this._setupVideo()),isNaN(this._codec.duration)||(this._duration=this._codec.duration),null===this._duration&&this._stream.seekable&&"video/ogg"==this._detectedType?(this._state=k,this._lastSeenTimestamp=-1,this._codec.flush((()=>{this._seekStream(Math.max(0,this._stream.length-131072))}))):(this._state=A,this._pingProcessing())}else this._codec.process((e=>{if(e)this._pingProcessing();else{if(this._streamEnded)throw new Error("end of file before headers found");this._log("reading more cause we are out of data"),this._readBytesAndWait()}}))}_doProcessSeekingEnd(){this._codec.frameReady?(this._log("saw frame with "+this._codec.frameTimestamp),this._lastSeenTimestamp=Math.max(this._lastSeenTimestamp,this._codec.frameTimestamp),this._codec.discardFrame((()=>{this._pingProcessing()}))):this._codec.audioReady?(this._log("saw audio with "+this._codec.audioTimestamp),this._lastSeenTimestamp=Math.max(this._lastSeenTimestamp,this._codec.audioTimestamp),this._codec.discardAudio((()=>{this._pingProcessing()}))):this._codec.process((e=>{e?this._pingProcessing():this._stream.eof?(this._log("seek-duration: we are at the end: "+this._lastSeenTimestamp),this._lastSeenTimestamp>0&&(this._duration=this._lastSeenTimestamp),this._state=A,this._codec.flush((()=>{this._streamEnded=!1,this._dataEnded=!1,this._seekStream(0)}))):this._readBytesAndWait()}))}_doProcessLoaded(){this._state=w,this._fireEventAsync("loadedmetadata"),this._fireEventAsync("durationchange"),this._codec.hasVideo&&this._fireEventAsync("resize"),this._pingProcessing(0)}_doProcessPreload(){!this._codec.frameReady&&this._codec.hasVideo||!this._codec.audioReady&&this._codec.hasAudio?this._codec.process((e=>{e?this._pingProcessing():this._streamEnded?this._ended=!0:this._readBytesAndWait()})):(this._state=P,this._fireEventAsync("loadeddata"),this._pingProcessing())}_doProcessReady(){if(this._log("initial seek to "+this._initialSeekTime),this._initialSeekTime>0){var e=this._initialSeekTime;this._initialSeekTime=0,this._log("initial seek to "+e),this._doSeek(e)}else if(this._paused)this._log("paused while in ready");else{var finishStartPlaying=()=>{this._log("finishStartPlaying"),this._state=E,this._lastFrameTimestamp=d(),this._codec.hasAudio&&this._audioFeeder?this._prebufferingAudio=!0:this._startPlayback(),this._pingProcessing(0),this._fireEventAsync("play"),this._fireEventAsync("playing")};!this._codec.hasAudio||this._audioFeeder||this._muted?finishStartPlaying():(this._initAudioFeeder(),this._audioFeeder.waitUntilReady(finishStartPlaying))}}_doProcessSeeking(){if(this._seekState==F)throw new Error("seeking in invalid state (not seeking?)");if(this._seekState==O)this._doProcessBisectionSeek();else if(this._seekState==S)this._doProcessBisectionSeek();else{if(this._seekState!=C)throw new Error("Invalid seek state "+this._seekState);this._doProcessLinearSeeking()}}_doProcessPlay(){var e=this._codec;if(this._paused)this._log("paused during playback; stopping loop");else if((!e.hasAudio||e.audioReady||this._pendingAudio||this._dataEnded)&&(!e.hasVideo||e.frameReady||this._pendingFrame||this._decodedFrames.length||this._dataEnded)){var t,i,s,d=null,h=0,u=!1,c=0;if(e.hasAudio&&this._audioFeeder?(d=this._audioFeeder.getPlaybackState(),h=this._getPlaybackTime(d),u=this._dataEnded&&0==this._audioFeeder.durationBuffered,this._prebufferingAudio&&(this._audioFeeder.durationBuffered>=2*this._audioFeeder.bufferThreshold&&(!e.hasVideo||this._decodedFrames.length>=this._framePipelineDepth)||this._dataEnded)&&(this._log("prebuffering audio done; buffered to "+this._audioFeeder.durationBuffered),this._startPlayback(h),this._prebufferingAudio=!1),d.dropped!=this._droppedAudio&&this._log("dropped "+(d.dropped-this._droppedAudio)),d.delayed!=this._delayedAudio&&this._log("delayed "+(d.delayed-this._delayedAudio)),this._droppedAudio=d.dropped,this._delayedAudio=d.delayed,(t=this._audioFeeder.durationBuffered<=2*this._audioFeeder.bufferThreshold)&&(this._codec.audioReady?this._pendingAudio>=this._audioPipelineDepth&&(this._log("audio decode disabled: "+this._pendingAudio+" packets in flight"),t=!1):t=!1)):(h=this._getPlaybackTime(),t=this._codec.audioReady&&this._audioEndTimestamp<h),this._codec.hasVideo){i=this._decodedFrames.length>0,s=this._pendingFrame+this._decodedFrames.length<this._framePipelineDepth+this._frameParallelism&&this._codec.frameReady,i&&(c=1e3*(this._decodedFrames[0].frameEndTimestamp-h),this._actualPerFrameTime=this._targetPerFrameTime-c);var l=this._targetPerFrameTime;if(this._prebufferingAudio)s&&this._log("decoding a frame during prebuffering"),i=!1;else if(i&&this._dataEnded&&u)this._log("audio timeline ended? ready to draw frame");else if(i&&-c>=l){for(var f=-1,_=0;_<this._decodedFrames.length-1;_++)this._decodedFrames[_].frameEndTimestamp<h&&(f=_-1);if(f>=0)for(;f-- >=0;){this._lateFrames++;var p=this._decodedFrames.shift();this._log("skipping already-decoded late frame at "+p.frameEndTimestamp),c=1e3*(p.frameEndTimestamp-h),this._frameEndTimestamp=p.frameEndTimestamp,this._actualPerFrameTime=this._targetPerFrameTime-c,this._framesProcessed++,p.dropped=!0,this._doFrameComplete(p)}var m=this._codec.nextKeyframeTimestamp,V=m-this._targetPerFrameTime/1e3*(this._framePipelineDepth+this._pendingFrame);if(m>=0&&m!=this._codec.frameTimestamp&&h>=V){this._log("skipping late frame at "+this._decodedFrames[0].frameEndTimestamp+" vs "+h+", expect to see keyframe at "+m);for(var g=0;g<this._decodedFrames.length;g++){var v=this._decodedFrames[g];this._lateFrames++,this._framesProcessed++,this._frameEndTimestamp=v.frameEndTimestamp,c=1e3*(v.frameEndTimestamp-h),this._actualPerFrameTime=this._targetPerFrameTime-c,v.dropped=!0,this._doFrameComplete(v)}this._decodedFrames=[];for(var b=0;b<this._pendingFrames.length;b++){var y=this._pendingFrames[b];this._lateFrames++,this._framesProcessed++,this._frameEndTimestamp=y.frameEndTimestamp,c=1e3*(y.frameEndTimestamp-h),this._actualPerFrameTime=this._targetPerFrameTime-c,y.dropped=!0,this._doFrameComplete(y)}for(this._pendingFrames=[],this._pendingFrame=0;this._codec.frameReady&&this._codec.frameTimestamp<m;){var T={frameEndTimestamp:this._codec.frameTimestamp,dropped:!0};c=1e3*(T.frameEndTimestamp-h),this._actualPerFrameTime=this._targetPerFrameTime-c,this._lateFrames++,this._codec.discardFrame((()=>{})),this._framesProcessed++,this._doFrameComplete(T)}return void(this._isProcessing()||this._pingProcessing())}}else i&&c<=4||(i=!1)}if(s){this._log("play loop: ready to decode frame; thread depth: "+this._pendingFrame+", have buffered: "+this._decodedFrames.length),0==this._videoInfo.fps&&this._codec.frameTimestamp-this._frameEndTimestamp>0&&(this._targetPerFrameTime=1e3*(this._codec.frameTimestamp-this._frameEndTimestamp)),this._totalFrameTime+=this._targetPerFrameTime,this._totalFrameCount++;var k=this._frameEndTimestamp=this._codec.frameTimestamp;this._pendingFrame++,this._pendingFrames.push({frameEndTimestamp:k});var A=this._pendingFrames,w=!1,P=this._time((()=>{this._codec.decodeFrame((e=>{A===this._pendingFrames?(this._log("play loop callback: decoded frame"),this._pendingFrame--,this._pendingFrames.shift(),e?this._decodedFrames.push({yCbCrBuffer:this._codec.frameBuffer,videoCpuTime:this._codec.videoCpuTime,frameEndTimestamp:k}):this._log("Bad video packet or something"),this._codec.process((()=>{this._isProcessing()||this._pingProcessing(w?void 0:0)}))):this._log("play loop callback after flush, discarding")}))}));this._pendingFrame&&(w=!0,this._proxyTime+=P,this._pingProcessing(),this._dataEnded&&this._codec.sync())}else if(t){this._log("play loop: ready for audio; depth: "+this._pendingAudio),this._pendingAudio++;var E=this._codec.audioTimestamp,x=this._time((()=>{this._codec.decodeAudio((e=>{if(this._pendingAudio--,this._log("play loop callback: decoded audio"),this._audioEndTimestamp=E,e){var t=this._codec.audioBuffer;if(t&&(this._bufferTime+=this._time((()=>{this._audioFeeder&&this._audioFeeder.bufferData(t)})),!this._codec.hasVideo)){this._framesProcessed++;var i={frameEndTimestamp:this._audioEndTimestamp};this._doFrameComplete(i)}}this._isProcessing()||this._pingProcessing()}))}));this._pendingAudio&&(this._proxyTime+=x,this._codec.audioReady?this._pingProcessing():this._doProcessPlayDemux())}else if(i){this._log("play loop: ready to draw frame"),this._nextFrameTimer&&(clearTimeout(this._nextFrameTimer),this._nextFrameTimer=null),this._thumbnail&&(this.removeChild(this._thumbnail),this._thumbnail=null);var R=this._decodedFrames.shift();this._currentVideoCpuTime=R.videoCpuTime,this._drawingTime+=this._time((()=>{this._drawFrame(R.yCbCrBuffer)})),this._framesProcessed++,this._doFrameComplete(R),this._pingProcessing()}else if(!this._decodedFrames.length||this._nextFrameTimer||this._prebufferingAudio)if(this._dataEnded&&!(this._pendingAudio||this._pendingFrame||this._decodedFrames.length)){this._log("play loop: playback reached end of data "+[this._pendingAudio,this._pendingFrame,this._decodedFrames.length]);var F=0;this._codec.hasAudio&&this._audioFeeder&&(F=1e3*this._audioFeeder.durationBuffered),F>0?(this._log("play loop: ending pending "+F+" ms"),this._pingProcessing(Math.max(0,F))):(this._log("play loop: ENDING NOW: playback time "+this._getPlaybackTime()+"; frameEndTimestamp: "+this._frameEndTimestamp),this._stopPlayback(),this._prebufferingAudio=!1,this._initialPlaybackOffset=Math.max(this._audioEndTimestamp,this._frameEndTimestamp),this._ended=!0,this._paused=!0,this._fireEventAsync("pause"),this._fireEventAsync("ended"))}else this._prebufferingAudio&&(e.hasVideo&&!e.frameReady||e.hasAudio&&!e.audioReady)?(this._log("play loop: prebuffering demuxing"),this._doProcessPlayDemux()):this._log("play loop: waiting on async/timers");else{var O=c;this._log("play loop: setting a timer for drawing "+O),this._nextFrameTimer=setTimeout((()=>{this._nextFrameTimer=null,this._pingProcessing()}),O)}}else this._log("play loop: demuxing"),this._doProcessPlayDemux()}_doProcessPlayDemux(){var e=this._codec.frameReady,t=this._codec.audioReady;this._codec.process((i=>{this._codec.frameReady&&!e||this._codec.audioReady&&!t?(this._log("demuxer has packets"),this._pingProcessing()):i?(this._log("demuxer processing to find more packets"),this._pingProcessing()):(this._log("demuxer ran out of data"),this._streamEnded?(this._log("demuxer reached end of data stream"),this._dataEnded=!0,this._pingProcessing()):(this._log("demuxer loading more data"),this._readBytesAndWait()))}))}_doProcessError(){}_isProcessing(){return this._stream&&(this._stream.buffering||this._stream.seeking)||this._codec&&this._codec.processing}_readBytesAndWait(){if(this._stream.buffering||this._stream.seeking)this._log("readBytesAndWait during i/o");else{this._stream.read(32768).then((e=>{this._log("got input "+[e.byteLength]),e.byteLength&&this._actionQueue.push((()=>{this._codec.receiveInput(e,(()=>{this._pingProcessing()}))})),this._stream.eof&&(this._log("stream is at end!"),this._streamEnded=!0),this._isProcessing()||this._pingProcessing()})).catch((e=>{this._onStreamError(e)}))}}_pingProcessing(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;if(this._stream&&this._stream.waiting)this._log("waiting on input");else{this._nextProcessingTimer&&(this._log("canceling old processing timer"),clearTimeout(this._nextProcessingTimer),this._nextProcessingTimer=null);e>-1/256?this._nextProcessingTimer=setTimeout((()=>{this._pingProcessing()}),e):this._depth?this._needProcessing=!0:this._doProcessing()}}_startProcessingVideo(e){if(!this._started&&!this._codec){this._framesProcessed=0,this._bufferTime=0,this._drawingTime=0,this._proxyTime=0,this._started=!0,this._ended=!1;var t={base:this._options.base,worker:this._enableWorker,threading:this._enableThreading,simd:this._enableSIMD};this._detectedType&&(t.type=this._detectedType),this._codec=new g.default(t),this._lastVideoCpuTime=0,this._lastAudioCpuTime=0,this._lastDemuxerCpuTime=0,this._lastBufferTime=0,this._lastDrawingTime=0,this._lastProxyTime=0,this._lastFrameVideoCpuTime=0,this._lastFrameAudioCpuTime=0,this._lastFrameDemuxerCpuTime=0,this._lastFrameBufferTime=0,this._lastFrameProxyTime=0,this._lastFrameDrawingTime=0,this._currentVideoCpuTime=0,this._codec.onseek=e=>{this._didSeek=!0,this._stream&&this._seekStream(e)},this._codec.init((()=>{this._codec.receiveInput(e,(()=>{this._readBytesAndWait()}))}))}}_loadCodec(e){this._stream.read(1024).then((t=>{var i=new Uint8Array(t);i.length>4&&i[0]=="O".charCodeAt(0)&&i[1]=="g".charCodeAt(0)&&i[2]=="g".charCodeAt(0)&&i[3]=="S".charCodeAt(0)?this._detectedType="video/ogg":i.length>4&&26==i[0]&&69==i[1]&&223==i[2]&&163==i[3]?this._detectedType="video/webm":this._detectedType="video/ogg",e(t)}))}_prepForLoad(e){this._stopVideo();var doLoad=()=>{this._options.stream?this._stream=this._options.stream:this._stream=new u.default({url:this.src,cacheSize:16777216,progressive:!1}),this._stream.load().then((()=>{this._loading=!1,this._currentSrc=this.src,this._byteLength=this._stream.seekable?this._stream.length:0;var e=this._stream.headers["x-content-duration"];"string"==typeof e&&(this._duration=parseFloat(e)),this._loadCodec((e=>{this._startProcessingVideo(e)}))})).catch((e=>{this._onStreamError(e)}))};this._currentSrc="",this._loading=!0,this._actionQueue.push((()=>{e&&"none"===this.preload?this._loading=!1:doLoad()})),this._pingProcessing(0)}load(){this._prepForLoad()}canPlayType(e){var t=new m.default(e);function checkTypes(e){if(t.codecs){var i=0,s=0;return t.codecs.forEach((t=>{e.indexOf(t)>=0?i++:s++})),0===i||s>0?"":"probably"}return"maybe"}return"ogg"!==t.minor||"audio"!==t.major&&"video"!==t.major&&"application"!==t.major?"webm"!==t.minor||"audio"!==t.major&&"video"!==t.major?"":checkTypes(["vorbis","opus","vp8","vp9"]):checkTypes(["vorbis","opus","theora"])}play(){this._muted||this._options.audioContext||OGVPlayer.initSharedAudioContext(),this._paused&&(this._startedPlaybackInDocument=document.body.contains(this),this._paused=!1,this._state==x||(this._started&&this._codec&&this._codec.loadedMetadata?(this._ended&&this._stream&&this._byteLength?(this._log(".play() starting over after end"),this._seek(0)):this._log(".play() while already started"),this._state=P,this._isProcessing()||this._pingProcessing()):this._loading?this._log(".play() while loading"):(this._log(".play() before started"),this._stream||this.load())))}getPlaybackStats(){return{targetPerFrameTime:this._targetPerFrameTime,framesProcessed:this._framesProcessed,videoBytes:this._codec?this._codec.videoBytes:0,audioBytes:this._codec?this._codec.audioBytes:0,playTime:this._playTime,demuxingTime:this._codec?this._codec.demuxerCpuTime-this._lastDemuxerCpuTime:0,videoDecodingTime:this._codec?this._codec.videoCpuTime-this._lastVideoCpuTime:0,audioDecodingTime:this._codec?this._codec.audioCpuTime-this._lastAudioCpuTime:0,bufferTime:this._bufferTime-this._lastBufferTime,drawingTime:this._drawingTime-this._lastDrawingTime,proxyTime:this._proxyTime-this._lastProxyTime,droppedAudio:this._droppedAudio,delayedAudio:this._delayedAudio,jitter:this._totalJitter/this._framesProcessed,lateFrames:this._lateFrames}}resetPlaybackStats(){this._framesProcessed=0,this._playTime=0,this._codec&&(this._lastDemuxerCpuTime=this._codec.demuxerCpuTime,this._lastVideoCpuTime=this._codec.videoCpuTime,this._lastAudioCpuTime=this._codec.audioCpuTime,this._codec.videoBytes=0,this._codec.audioBytes=0),this._lastBufferTime=this._bufferTime,this._lastDrawingTime=this._drawingTime,this._lastProxyTime=this._proxyTime,this._totalJitter=0,this._totalFrameTime=0,this._totalFrameCount=0}getVideoFrameSink(){return this._frameSink}getCanvas(){return this._canvas}getVideo(){return null}pause(){this._paused||(this._nextProcessingTimer&&(clearTimeout(this._nextProcessingTimer),this._nextProcessingTimer=null),this._stopPlayback(),this._prebufferingAudio=!1,this._paused=!0,this._fireEvent("pause"))}stop(){this._stopVideo(),this._paused=!0}fastSeek(e){this._seek(+e,D)}}(0,_.default)(OGVPlayer,y),OGVPlayer.instanceCount=0,OGVPlayer.styleManager=new function StyleManager(){var e=document.createElement("style");e.type="text/css",e.textContent="ogvjs { display: inline-block; position: relative; -webkit-user-select: none; -webkit-tap-highlight-color: rgba(0,0,0,0); ",document.head.appendChild(e);var t=e.sheet;this.appendRule=function(e,i){var s=[];for(var d in i)i.hasOwnProperty(d)&&s.push(d+":"+i[d]);var h=e+"{"+s.join(";")+"}";t.insertRule(h,t.cssRules.length-1)}};var M=OGVPlayer;t.default=M},580:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function OGVProxyClass(e){return class{constructor(t,i,s){for(var d in s=s||{},this.worker=t,this.transferables=function(){var e=new ArrayBuffer(1024),i=new Uint8Array(e);try{return t.postMessage({action:"transferTest",bytes:i},[e]),!e.byteLength}catch(e){return!1}}(),e)e.hasOwnProperty(d)&&(this[d]=e[d]);this.processingQueue=0,Object.defineProperty(this,"processing",{get:function get(){return this.processingQueue>0}}),this.messageCount=0,this.pendingCallbacks={},this.worker.addEventListener("message",(e=>{this.handleMessage(e)})),this.proxy("construct",[i,s],(()=>{}))}proxy(e,t,i){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];if(!this.worker)throw'Tried to call "'+e+'" method on closed proxy object';var d="callback-"+ ++this.messageCount+"-"+e;i&&(this.pendingCallbacks[d]=i);var h={action:e,callbackId:d,args:t||[]};this.processingQueue++,this.transferables?this.worker.postMessage(h,s):this.worker.postMessage(h)}terminate(){this.worker&&(this.worker.terminate(),this.worker=null,this.processingQueue=0,this.pendingCallbacks={})}handleMessage(e){if(this.processingQueue--,"callback"===e.data.action){var t=e.data,i=t.callbackId,s=t.args,d=this.pendingCallbacks[i];if(t.props)for(var h in t.props)t.props.hasOwnProperty(h)&&(this[h]=t.props[h]);d&&(delete this.pendingCallbacks[i],d.apply(this,s))}}}};t.default=i},168:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=class OGVTimeRanges{constructor(e){this._ranges=e,this.length=e.length}start(e){if(e<0||e>this.length||e!==(0|e))throw new RangeError("Invalid index");return this._ranges[e][0]}end(e){if(e<0||e>this.length||e!==(0|e))throw new RangeError("Invalid index");return this._ranges[e][1]}};t.default=i},625:(e,t,i)=>{"use strict";var s=i(318);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(i(964));var h=class OGVWrapperCodec{constructor(e){return this.options=e||{},this.demuxer=null,this.videoDecoder=null,this.audioDecoder=null,this.flushIter=0,this.loadedMetadata=!1,this.processing=!1,Object.defineProperties(this,{duration:{get:function get(){return this.loadedMetadata?this.demuxer.duration:NaN}},hasAudio:{get:function get(){return this.loadedMetadata&&!!this.audioDecoder}},audioReady:{get:function get(){return this.hasAudio&&this.demuxer.audioReady}},audioTimestamp:{get:function get(){return this.demuxer.audioTimestamp}},audioFormat:{get:function get(){return this.hasAudio?this.audioDecoder.audioFormat:null}},audioBuffer:{get:function get(){return this.hasAudio?this.audioDecoder.audioBuffer:null}},hasVideo:{get:function get(){return this.loadedMetadata&&!!this.videoDecoder}},frameReady:{get:function get(){return this.hasVideo&&this.demuxer.frameReady}},frameTimestamp:{get:function get(){return this.demuxer.frameTimestamp}},keyframeTimestamp:{get:function get(){return this.demuxer.keyframeTimestamp}},nextKeyframeTimestamp:{get:function get(){return this.demuxer.nextKeyframeTimestamp}},videoFormat:{get:function get(){return this.hasVideo?this.videoDecoder.videoFormat:null}},frameBuffer:{get:function get(){return this.hasVideo?this.videoDecoder.frameBuffer:null}},seekable:{get:function get(){return this.demuxer.seekable}},demuxerCpuTime:{get:function get(){return this.demuxer?this.demuxer.cpuTime:0}},audioCpuTime:{get:function get(){return this.audioDecoder?this.audioDecoder.cpuTime:0}},videoCpuTime:{get:function get(){return this.videoDecoder?this.videoDecoder.cpuTime:0}}}),this.loadedDemuxerMetadata=!1,this.loadedAudioMetadata=!1,this.loadedVideoMetadata=!1,this.loadedAllMetadata=!1,this.onseek=null,this.videoBytes=0,this.audioBytes=0,this}flushSafe(e){var t=this.flushIter;return i=>{this.flushIter<=t&&e(i)}}init(e){var t;this.processing=!0,t="video/webm"===this.options.type||"audio/webm"===this.options.type?"OGVDemuxerWebMW":"OGVDemuxerOggW",d.default.loadClass(t,(t=>{t().then((t=>{this.demuxer=t,t.onseek=e=>{this.onseek&&this.onseek(e)},t.init((()=>{this.processing=!1,e()}))}))}))}close(){this.demuxer&&(this.demuxer.close(),this.demuxer=null),this.videoDecoder&&(this.videoDecoder.close(),this.videoDecoder=null),this.audioDecoder&&(this.audioDecoder.close(),this.audioDecoder=null)}receiveInput(e,t){this.demuxer.receiveInput(e,t)}process(e){if(this.processing)throw new Error("reentrancy fail on OGVWrapperCodec.process");this.processing=!0;var finish=t=>{this.processing=!1,e(t)},doProcessData=()=>{this.demuxer.process(finish)};this.demuxer.loadedMetadata&&!this.loadedDemuxerMetadata?this.loadAudioCodec((()=>{this.loadVideoCodec((()=>{this.loadedDemuxerMetadata=!0,this.loadedAudioMetadata=!this.audioDecoder,this.loadedVideoMetadata=!this.videoDecoder,this.loadedAllMetadata=this.loadedAudioMetadata&&this.loadedVideoMetadata,finish(!0)}))})):this.loadedDemuxerMetadata&&!this.loadedAudioMetadata?this.audioDecoder.loadedMetadata?(this.loadedAudioMetadata=!0,this.loadedAllMetadata=this.loadedAudioMetadata&&this.loadedVideoMetadata,finish(!0)):this.demuxer.audioReady?this.demuxer.dequeueAudioPacket(((e,t)=>{this.audioBytes+=e.byteLength,this.audioDecoder.processHeader(e,(e=>{finish(!0)}))})):doProcessData():this.loadedAudioMetadata&&!this.loadedVideoMetadata?this.videoDecoder.loadedMetadata?(this.loadedVideoMetadata=!0,this.loadedAllMetadata=this.loadedAudioMetadata&&this.loadedVideoMetadata,finish(!0)):this.demuxer.frameReady?(this.processing=!0,this.demuxer.dequeueVideoPacket((e=>{this.videoBytes+=e.byteLength,this.videoDecoder.processHeader(e,(()=>{finish(!0)}))}))):doProcessData():this.loadedVideoMetadata&&!this.loadedMetadata&&this.loadedAllMetadata?(this.loadedMetadata=!0,finish(!0)):!this.loadedMetadata||this.hasAudio&&!this.demuxer.audioReady||this.hasVideo&&!this.demuxer.frameReady?doProcessData():finish(!0)}decodeFrame(e){var t=this.flushSafe(e),i=this.frameTimestamp,s=this.keyframeTimestamp;this.demuxer.dequeueVideoPacket((e=>{this.videoBytes+=e.byteLength,this.videoDecoder.processFrame(e,(e=>{var d=this.videoDecoder.frameBuffer;d&&(d.timestamp=i,d.keyframeTimestamp=s),t(e)}))}))}decodeAudio(e){var t=this.flushSafe(e);this.demuxer.dequeueAudioPacket(((e,i)=>{this.audioBytes+=e.byteLength,this.audioDecoder.processAudio(e,(e=>{if(i){var s=this.audioDecoder.audioBuffer,d=[];for(var h of s){var u=Math.round(i*this.audioFormat.rate/1e9);u>0?d.push(h.subarray(0,h.length-Math.min(u,h.length))):d.push(h.subarray(Math.min(Math.abs(u),h.length),h.length))}this.audioDecoder.audioBuffer=d}return t(e)}))}))}discardFrame(e){this.demuxer.dequeueVideoPacket((t=>{this.videoBytes+=t.byteLength,e()}))}discardAudio(e){this.demuxer.dequeueAudioPacket(((t,i)=>{this.audioBytes+=t.byteLength,e()}))}flush(e){this.flushIter++,this.demuxer.flush(e)}sync(){this.videoDecoder&&this.videoDecoder.sync()}recycleFrame(e){this.videoDecoder&&this.videoDecoder.recycleFrame(e)}getKeypointOffset(e,t){this.demuxer.getKeypointOffset(e,t)}seekToKeypoint(e,t){this.demuxer.seekToKeypoint(e,this.flushSafe(t))}loadAudioCodec(e){if(this.demuxer.audioCodec){var t={vorbis:"OGVDecoderAudioVorbisW",opus:"OGVDecoderAudioOpusW"}[this.demuxer.audioCodec];this.processing=!0,d.default.loadClass(t,(t=>{var i={};this.demuxer.audioFormat&&(i.audioFormat=this.demuxer.audioFormat),t(i).then((t=>{this.audioDecoder=t,t.init((()=>{this.loadedAudioMetadata=t.loadedMetadata,this.processing=!1,e()}))}))}),{worker:this.options.worker})}else e()}loadVideoCodec(e){if(this.demuxer.videoCodec){var t=!!this.options.simd,i=!!this.options.threading,s={theora:"OGVDecoderVideoTheoraW",vp8:i?"OGVDecoderVideoVP8MTW":"OGVDecoderVideoVP8W",vp9:i?t?"OGVDecoderVideoVP9SIMDMTW":"OGVDecoderVideoVP9MTW":t?"OGVDecoderVideoVP9SIMDW":"OGVDecoderVideoVP9W",av1:i?t?"OGVDecoderVideoAV1SIMDMTW":"OGVDecoderVideoAV1MTW":t?"OGVDecoderVideoAV1SIMDW":"OGVDecoderVideoAV1W"}[this.demuxer.videoCodec];this.processing=!0,d.default.loadClass(s,(t=>{var s={};this.demuxer.videoFormat&&(s.videoFormat=this.demuxer.videoFormat),i&&delete window.ENVIRONMENT_IS_PTHREAD,t(s).then((t=>{this.videoDecoder=t,t.init((()=>{this.loadedVideoMetadata=t.loadedMetadata,this.processing=!1,e()}))}))}),{worker:this.options.worker&&!this.options.threading})}else e()}};t.default=h},539:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=new class WebAssemblyChecker{constructor(){this.tested=!1,this.testResult=void 0}wasmSupported(){if(!this.tested){try{"object"==typeof WebAssembly?this.testResult=function testSafariWebAssemblyBug(){var e=new Uint8Array([0,97,115,109,1,0,0,0,1,6,1,96,1,127,1,127,3,2,1,0,5,3,1,0,1,7,8,1,4,116,101,115,116,0,0,10,16,1,14,0,32,0,65,1,54,2,0,32,0,40,2,0,11]),t=new WebAssembly.Module(e);return 0!==new WebAssembly.Instance(t,{}).exports.test(4)}():this.testResult=!1}catch(e){console.log("Exception while testing WebAssembly",e),this.testResult=!1}this.tested=!0}return this.testResult}};t.default=i},309:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function extend(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])};t.default=i},431:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),d=function get(e,t,i){null===e&&(e=Function.prototype);var s=Object.getOwnPropertyDescriptor(e,t);if(void 0===s){var d=Object.getPrototypeOf(e);return null===d?void 0:get(d,t,i)}if("value"in s)return s.value;var h=s.get;return void 0!==h?h.call(i):void 0};function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var h=i(828),u="arraybuffer",c=function(e){function ArrayBufferBackend(){return _classCallCheck(this,ArrayBufferBackend),_possibleConstructorReturn(this,(ArrayBufferBackend.__proto__||Object.getPrototypeOf(ArrayBufferBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(ArrayBufferBackend,e),s(ArrayBufferBackend,[{key:"initXHR",value:function initXHR(){d(ArrayBufferBackend.prototype.__proto__||Object.getPrototypeOf(ArrayBufferBackend.prototype),"initXHR",this).call(this),this.xhr.responseType=u}},{key:"onXHRProgress",value:function onXHRProgress(){}},{key:"onXHRLoad",value:function onXHRLoad(){var e=this.xhr.response;this.bytesRead+=e.byteLength,this.emit("buffer",e),d(ArrayBufferBackend.prototype.__proto__||Object.getPrototypeOf(ArrayBufferBackend.prototype),"onXHRLoad",this).call(this)}}]),ArrayBufferBackend}(h);c.supported=function(){try{var e=new XMLHttpRequest;return e.responseType=u,e.responseType===u}catch(e){return!1}},e.exports=c},306:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();function getXHRLength(e){if(206==e.status)return function getXHRRangeTotal(e){var t=getXHRRangeMatches(e);return t?parseInt(t[3],10):-1}(e);var t=e.getResponseHeader("Content-Length");return null===t||""===t?-1:parseInt(t,10)}function getXHRRangeMatches(e){var t=e.getResponseHeader("Content-Range");return t&&t.match(/^bytes (\d+)-(\d+)\/(\d+)/)}var d=function(e){function Backend(e){var t=e.url,i=e.offset,s=e.length,d=e.cachever,h=void 0===d?0:d;!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Backend);var u=function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(Backend.__proto__||Object.getPrototypeOf(Backend)).call(this));return u.url=t,u.offset=i,u.length=s,u.cachever=h,u.loaded=!1,u.seekable=!1,u.headers={},u.eof=!1,u.bytesRead=0,u.xhr=new XMLHttpRequest,u}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(Backend,e),s(Backend,[{key:"load",value:function load(){var e=this;return new Promise((function(t,i){var s=null;e._onAbort=function(e){s(),i(e)};var d=function checkOpen(){if(2==e.xhr.readyState){if(206==e.xhr.status){var d=function getXHRRangeStart(e){var t=getXHRRangeMatches(e);return t?parseInt(t[1],10):0}(e.xhr);if(e.offset!=d)return console.log("Expected start at "+e.offset+" but got "+d+"; working around Safari range caching bug: https://bugs.webkit.org/show_bug.cgi?id=82672"),e.cachever++,e.emit("cachever"),e.abort(),s(),void e.load().then(t).catch(i);e.seekable=!0}e.xhr.status>=200&&e.xhr.status<300?(e.length=getXHRLength(e.xhr),e.headers=function getXHRHeaders(e){var t={};return e.getAllResponseHeaders().split(/\r?\n/).forEach((function(e){var i=e.split(/:\s*/,2);i.length>1&&(t[i[0].toLowerCase()]=i[1])})),t}(e.xhr),e.onXHRStart()):(s(),i(new Error("HTTP error "+e.xhr.status)))}},h=function checkError(){s(),i(new Error("network error"))},u=function checkBackendOpen(){s(),t()};s=function oncomplete(){e.xhr.removeEventListener("readystatechange",d),e.xhr.removeEventListener("error",h),e.off("open",u),e._onAbort=null},e.initXHR(),e.xhr.addEventListener("readystatechange",d),e.xhr.addEventListener("error",h),e.on("open",u),e.xhr.send()}))}},{key:"bufferToOffset",value:function bufferToOffset(e){return Promise.reject(new Error("abstract"))}},{key:"abort",value:function abort(){if(this.xhr.abort(),this._onAbort){var e=this._onAbort;this._onAbort=null;var t=new Error("Aborted");t.name="AbortError",e(t)}}},{key:"initXHR",value:function initXHR(){var e=this.url;this.cachever&&(e+="?buggy_cachever="+this.cachever),this.xhr.open("GET",e);var t=null;(this.offset||this.length)&&(t="bytes="+this.offset+"-"),this.length&&(t+=this.offset+this.length-1),null!==t&&this.xhr.setRequestHeader("Range",t)}},{key:"onXHRStart",value:function onXHRStart(){throw new Error("abstract")}}]),Backend}(i(566));e.exports=d},810:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),d=function get(e,t,i){null===e&&(e=Function.prototype);var s=Object.getOwnPropertyDescriptor(e,t);if(void 0===s){var d=Object.getPrototypeOf(e);return null===d?void 0:get(d,t,i)}if("value"in s)return s.value;var h=s.get;return void 0!==h?h.call(i):void 0};function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var h=function(e){function BinaryStringBackend(){return _classCallCheck(this,BinaryStringBackend),_possibleConstructorReturn(this,(BinaryStringBackend.__proto__||Object.getPrototypeOf(BinaryStringBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(BinaryStringBackend,e),s(BinaryStringBackend,[{key:"initXHR",value:function initXHR(){d(BinaryStringBackend.prototype.__proto__||Object.getPrototypeOf(BinaryStringBackend.prototype),"initXHR",this).call(this),this.xhr.responseType="text",this.xhr.overrideMimeType("text/plain; charset=x-user-defined")}},{key:"onXHRProgress",value:function onXHRProgress(){var e=this.xhr.responseText.slice(this.bytesRead);e.length>0&&(this.bytesRead+=e.length,this.emit("buffer",e))}},{key:"onXHRLoad",value:function onXHRLoad(){this.onXHRProgress(),d(BinaryStringBackend.prototype.__proto__||Object.getPrototypeOf(BinaryStringBackend.prototype),"onXHRLoad",this).call(this)}}]),BinaryStringBackend}(i(828));h.supported=function(){try{return!!(new XMLHttpRequest).overrideMimeType}catch(e){return!1}},e.exports=h},828:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),d=function get(e,t,i){null===e&&(e=Function.prototype);var s=Object.getOwnPropertyDescriptor(e,t);if(void 0===s){var d=Object.getPrototypeOf(e);return null===d?void 0:get(d,t,i)}if("value"in s)return s.value;var h=s.get;return void 0!==h?h.call(i):void 0};function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var h=function(e){function DownloadBackend(){return _classCallCheck(this,DownloadBackend),_possibleConstructorReturn(this,(DownloadBackend.__proto__||Object.getPrototypeOf(DownloadBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(DownloadBackend,e),s(DownloadBackend,[{key:"bufferToOffset",value:function bufferToOffset(e){var t=this;return new Promise((function(i,s){if(t.eof||t.offset>=e)i();else{var d=null;t._onAbort=function(e){d(),s(e)};var h=function checkBuffer(){t.offset>=e&&!t.eof&&(d(),i())},u=function checkDone(){d(),i()},c=function checkError(){d(),s(new Error("error streaming"))};d=function oncomplete(){t.buffering=!1,t.off("buffer",h),t.off("done",u),t.off("error",c),t._onAbort=null},t.buffering=!0,t.on("buffer",h),t.on("done",u),t.on("error",c)}}))}},{key:"initXHR",value:function initXHR(){d(DownloadBackend.prototype.__proto__||Object.getPrototypeOf(DownloadBackend.prototype),"initXHR",this).call(this)}},{key:"onXHRStart",value:function onXHRStart(){var e=this;this.xhr.addEventListener("progress",(function(){return e.onXHRProgress()})),this.xhr.addEventListener("error",(function(){return e.onXHRError()})),this.xhr.addEventListener("load",(function(){return e.onXHRLoad()})),this.emit("open")}},{key:"onXHRProgress",value:function onXHRProgress(){throw new Error("abstract")}},{key:"onXHRError",value:function onXHRError(){this.emit("error")}},{key:"onXHRLoad",value:function onXHRLoad(){this.eof=!0,this.emit("done")}}]),DownloadBackend}(i(306));e.exports=h},761:(e,t,i)=>{"use strict";var s=i(855),d=i(810),h=i(431);var u=null;e.exports=function instantiate(e){if(!1===e.progressive)return new h(e);if(u||(u=function autoselect(){return s.supported()?s:d.supported()?d:null}()),!u)throw new Error("No supported backend class");return new u(e)}},855:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}(),d=function get(e,t,i){null===e&&(e=Function.prototype);var s=Object.getOwnPropertyDescriptor(e,t);if(void 0===s){var d=Object.getPrototypeOf(e);return null===d?void 0:get(d,t,i)}if("value"in s)return s.value;var h=s.get;return void 0!==h?h.call(i):void 0};function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var h=i(828),u="moz-chunked-arraybuffer",c=function(e){function MozChunkedBackend(){return _classCallCheck(this,MozChunkedBackend),_possibleConstructorReturn(this,(MozChunkedBackend.__proto__||Object.getPrototypeOf(MozChunkedBackend)).apply(this,arguments))}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(MozChunkedBackend,e),s(MozChunkedBackend,[{key:"initXHR",value:function initXHR(){d(MozChunkedBackend.prototype.__proto__||Object.getPrototypeOf(MozChunkedBackend.prototype),"initXHR",this).call(this),this.xhr.responseType=u}},{key:"onXHRProgress",value:function onXHRProgress(){var e=this.xhr.response;this.bytesRead+=e.byteLength,this.emit("buffer",e)}}]),MozChunkedBackend}(h);c.supported=function(){try{var e=new XMLHttpRequest;return e.responseType=u,e.responseType===u}catch(e){return!1}},e.exports=c},503:e=>{"use strict";var t=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i=function(){function CacheItem(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.buffer,i=void 0===t?void 0:t,s=e.string,d=void 0===s?void 0:s,h=e.start,u=void 0===h?0:h,c=e.end,l=void 0===c?u+(i?i.byteLength:d?d.length:0):c,f=e.prev,_=void 0===f?null:f,p=e.next,m=void 0===p?null:p,V=e.eof,g=void 0!==V&&V,v=e.empty,b=void 0===v?!(i||d):v,y=e.timestamp,T=void 0===y?Date.now():y;_classCallCheck(this,CacheItem),this.start=u,this.end=l,this.prev=_,this.next=m,this.eof=g,this.empty=b,this.timestamp=T,this.buffer=i,this.string=d,Object.defineProperty(this,"length",{get:function get(){return this.end-this.start}})}return t(CacheItem,[{key:"contains",value:function contains(e){return e>=this.start&&(e<this.end||this.eof)}},{key:"readBytes",value:function readBytes(e,t,i){var s=t-this.start,d=i-t;if(this.buffer){var h=new Uint8Array(this.buffer,s,d);e.set(h)}else{if(!this.string)throw new Error("invalid state");for(var u=this.string,c=0;c<d;c++)e[c]=u.charCodeAt(s+c)}this.timestamp=Date.now()}},{key:"split",value:function split(e){if(!this.empty||!this.contains(e))throw new Error("invalid split");var t=new CacheItem({start:this.start,end:e}),i=new CacheItem({start:e,end:this.eof?e:this.end,eof:this.eof});return t.next=i,i.prev=t,[t,i]}},{key:"first",value:function first(e){for(var t=this;t;t=t.next)if(e(t))return t;return null}},{key:"last",value:function last(e){for(var last=null,t=this;t&&e(t);t=t.next)last=t;return last}}]),CacheItem}();e.exports=i},91:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var d=i(503),h=function(){function CachePool(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.cacheSize,i=void 0===t?0:t;_classCallCheck(this,CachePool);var s=new d({eof:!0});this.head=s,this.tail=s,this.readOffset=0,this.readCursor=s,this.writeOffset=0,this.writeCursor=s,this.cacheSize=i}return s(CachePool,[{key:"bytesReadable",value:function bytesReadable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0,t=this.readOffset,i=this.readCursor,s=i.last((function(i){return!i.empty&&i.start<=t+e}));return s?Math.min(e,s.end-t):0}},{key:"bytesWritable",value:function bytesWritable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0,t=this.writeOffset,i=this.writeCursor;if(i.eof)return e;var s=i.last((function(i){return i.empty&&i.start<=t+e}));return s?Math.min(e,s.end-t):0}},{key:"seekRead",value:function seekRead(e){var t=this.head.first((function(t){return t.contains(e)}));if(!t)throw new Error("read seek out of range");this.readOffset=e,this.readCursor=t}},{key:"seekWrite",value:function seekWrite(e){var t=this.head.first((function(t){return t.contains(e)}));if(!t)throw new Error("write seek out of range");this.writeOffset=e,this.writeCursor=t}},{key:"readBytes",value:function readBytes(e){for(var t=e.byteLength,i=this.bytesReadable(t),s=this.readOffset,d=s+i,h=s,u=this.readCursor;u&&!u.empty&&!(u.start>=d);u=u.next){var c=Math.min(d,u.end),l=e.subarray(h-s,c-s);u.readBytes(l,h,c),h=c}return this.readOffset=h,this.readCursor=this.readCursor.first((function(e){return e.contains(h)})),i}},{key:"write",value:function write(e){var t=this.bufferItem(e),i=this.writeCursor;if(!i.empty)throw new Error("write cursor not empty");if(!i.contains(t.end)&&i.end!==t.end)throw new Error("write cursor too small");i.start<t.start&&(this.split(i,t.start),i=this.writeCursor),(t.end<i.end||i.eof)&&(this.split(i,t.end),i=this.writeCursor),this.splice(i,i,t,t),this.writeOffset=t.end,this.writeCursor=t.next,this.gc()}},{key:"bufferItem",value:function bufferItem(e){if(e instanceof ArrayBuffer)return new d({start:this.writeOffset,end:this.writeOffset+e.byteLength,buffer:e});if("string"==typeof e)return new d({start:this.writeOffset,end:this.writeOffset+e.length,string:e});throw new Error("invalid input to write")}},{key:"split",value:function split(e,t){var i=e.split(t);this.splice(e,e,i[0],i[1])}},{key:"ranges",value:function ranges(){for(var ranges=[],e=this.head;e;e=e.next)if(!e.empty){var t=e;e=e.last((function(e){return!e.empty})),ranges.push([t.start,e.end])}return ranges}},{key:"gc",value:function gc(){for(var e=0,t=[],i=this.head;i;i=i.next)i.empty||(e+=i.length,(i.end<this.readOffset||i.start>this.readOffset+this.chunkSize)&&t.push(i));if(e>this.cacheSize){t.sort((function(e,t){return e.timestamp-t.timestamp}));for(var s=0;s<t.length;s++){var d=t[s];if(e<=this.cacheSize)break;this.remove(d),e-=d.length}}}},{key:"remove",value:function remove(e){var t=new d({start:e.start,end:e.end});this.splice(e,e,t,t),(e=t).prev&&e.prev.empty&&(e=this.consolidate(e.prev)),e.next&&e.next.empty&&!e.next.eof&&(e=this.consolidate(e)),0===e.start&&(this.head=e)}},{key:"consolidate",value:function consolidate(e){var t=e.last((function(e){return e.empty&&!e.eof})),i=new d({start:e.start,end:t.end});return this.splice(e,t,i,i),i}},{key:"splice",value:function splice(e,t,i,s){var d=this;if(e.start!==i.start)throw new Error("invalid splice head");if(!(t.end===s.end||t.eof&&s.eof))throw new Error("invalid splice tail");var h=e.prev,u=t.next;e.prev=null,t.next=null,h&&(h.next=i,i.prev=h),u&&(u.prev=s,s.next=u),e===this.head&&(this.head=i),t===this.tail&&(this.tail=s),this.readCursor=this.head.first((function(e){return e.contains(d.readOffset)})),this.writeCursor=this.head.first((function(e){return e.contains(d.writeOffset)}))}},{key:"eof",get:function get(){return this.readCursor.eof}}]),CachePool}();e.exports=h},814:(e,t,i)=>{"use strict";e.exports=i(91)},566:e=>{"use strict";var t=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();var i=function(){function TinyEvents(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,TinyEvents),this._e={}}return t(TinyEvents,[{key:"on",value:function on(e,t){(this._e[e]||(this._e[e]=[])).push(t)}},{key:"off",value:function off(e,t){var i=this._e[e]||[],s=i.indexOf(t);t>=0&&i.splice(s,1)}},{key:"emit",value:function emit(e,t){(this._e[e]||[]).slice().forEach((function(e){return e(t)}))}}]),TinyEvents}();e.exports=i},936:(e,t,i)=>{"use strict";var s=function(){function defineProperties(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}return function(e,t,i){return t&&defineProperties(e.prototype,t),i&&defineProperties(e,i),e}}();i(566);var d=i(814),h=i(761),u=function(){function StreamFile(e){var t=e.url,i=void 0===t?"":t,s=e.chunkSize,h=void 0===s?1048576:s,u=e.cacheSize,c=void 0===u?0:u,l=e.progressive,f=void 0===l||l;!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,StreamFile),this.length=-1,this.loaded=!1,this.loading=!1,this.seekable=!1,this.buffering=!1,this.seeking=!1,this.progressive=f,Object.defineProperties(this,{offset:{get:function get(){return this._cache.readOffset}},eof:{get:function get(){return this.length===this._cache.readOffset}}}),this.url=i,this.headers={},this._cache=new d({cacheSize:c}),this._backend=null,this._cachever=0,this._chunkSize=h}return s(StreamFile,[{key:"load",value:function load(){var e=this;return new Promise((function(t,i){if(e.loading)throw new Error("cannot load when loading");if(e.loaded)throw new Error("cannot load when loaded");e.loading=!0,e._openBackend().then((function(i){e.seekable=i.seekable,e.headers=i.headers,e.length=i.length,e.loaded=!0,e.loading=!1,t()})).catch((function(t){"AbortError"!==t.name&&(e.loading=!1),i(t)}))}))}},{key:"_openBackend",value:function _openBackend(){var e=this;return new Promise((function(t,i){if(e._backend)t(e._backend);else if(e.eof)i(new Error("cannot open at end of file"));else{var s=e._cache,d=e._chunkSize,u=s.bytesReadable(d),c=s.readOffset+u;if(s.seekWrite(c),e.length>=0&&c>=e.length)return void t(null);var l=e._clampToLength(s.writeOffset+s.bytesWritable(d))-s.writeOffset;if(0===l)t(null);else{var f=e._backend=new h({url:e.url,offset:e._cache.writeOffset,length:l,cachever:e._cachever,progressive:e.progressive}),_=null,p=function checkOpen(){f!==e._backend?(_(),i(new Error("invalid state"))):(f.on("buffer",(function(t){f===e._backend&&e._cache.write(t)})),f.on("done",(function(){f===e._backend&&(-1===e.length&&(e.length=e._backend.offset+e._backend.bytesRead),e._backend=null)})),t(f))},m=function checkError(t){f!==e._backend?i(new Error("invalid state")):(e._backend=null,i(t))};_=function oncomplete(){f.off("open",p),f.off("error",m)},f.on("open",p),f.on("error",m),f.on("cachever",(function(){e._cachever++})),f.load()}}}))}},{key:"_readAhead",value:function _readAhead(){var e=this;return new Promise((function(t,i){e._backend||e.eof?t():e._openBackend().then((function(){t()})).catch((function(e){i(e)}))}))}},{key:"seek",value:function seek(e){var t=this;return new Promise((function(i,s){if(!t.loaded||t.buffering||t.seeking)throw new Error("invalid state");if(e!==(0|e)||e<0)throw new Error("invalid input");if(t.length>=0&&e>t.length)throw new Error("seek past end of file");if(!t.seekable)throw new Error("seek on non-seekable stream");t._backend&&t.abort(),t._cache.seekRead(e),t._cache.seekWrite(e),t._readAhead().then(i).catch(s)}))}},{key:"read",value:function read(e){var t=this;return this.buffer(e).then((function(e){return t.readSync(e)}))}},{key:"readSync",value:function readSync(e){var t=this.bytesAvailable(e),i=new Uint8Array(t);if(this.readBytes(i)!==t)throw new Error("failed to read expected data");return i.buffer}},{key:"readBytes",value:function readBytes(e){if(!this.loaded||this.buffering||this.seeking)throw new Error("invalid state");if(!(e instanceof Uint8Array))throw new Error("invalid input");var t=this._cache.readBytes(e);return this._readAhead(),t}},{key:"buffer",value:function buffer(e){var t=this;return new Promise((function(i,s){if(!t.loaded||t.buffering||t.seeking)throw new Error("invalid state");if(e!==(0|e)||e<0)throw new Error("invalid input");var d=t._clampToLength(t.offset+e),h=d-t.offset,u=t.bytesAvailable(h);u>=h?i(u):(t.buffering=!0,t._openBackend().then((function(i){return i?i.bufferToOffset(d).then((function(){return t.buffering=!1,t.buffer(e)})):Promise.resolve(u)})).then((function(e){t.buffering=!1,i(e)})).catch((function(e){"AbortError"!==e.name&&(t.buffering=!1),s(e)})))}))}},{key:"bytesAvailable",value:function bytesAvailable(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1/0;return this._cache.bytesReadable(e)}},{key:"abort",value:function abort(){this.loading&&(this.loading=!1),this.buffering&&(this.buffering=!1),this.seeking&&(this.seeking=!1),this._backend&&(this._backend.abort(),this._backend=null)}},{key:"getBufferedRanges",value:function getBufferedRanges(){return this._cache.ranges()}},{key:"_clampToLength",value:function _clampToLength(e){return this.length<0?e:Math.min(this.length,e)}}]),StreamFile}();e.exports=u},302:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});const s="data:audio/mpeg;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU5LjE2LjEwMAAAAAAAAAAAAAAA//tQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAACAAAEEwCZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZ//////////////////////////////////////////////////////////////////8AAAAATGF2YzU5LjE4AAAAAAAAAAAAAAAAJAZAAAAAAAAABBMIw3vfAAAAAAAAAAAAAAAAAAAAAP/7kGQAD/AAAGkAAAAIAAANIAAAAQAAAaQAAAAgAAA0gAAABExBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVUxBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/7kmRAj/AAAGkAAAAIAAANIAAAAQAAAaQAAAAgAAA0gAAABFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVU="},826:e=>{e.exports={vertex:"precision mediump float;\n\nattribute vec2 aPosition;\nattribute vec2 aLumaPosition;\nattribute vec2 aChromaPosition;\nvarying vec2 vLumaPosition;\nvarying vec2 vChromaPosition;\nvoid main() {\n    gl_Position = vec4(aPosition, 0, 1);\n    vLumaPosition = aLumaPosition;\n    vChromaPosition = aChromaPosition;\n}\n",fragment:"// inspired by https://github.com/mbebenita/Broadway/blob/master/Player/canvas.js\n\nprecision mediump float;\n\nuniform sampler2D uTextureY;\nuniform sampler2D uTextureCb;\nuniform sampler2D uTextureCr;\nvarying vec2 vLumaPosition;\nvarying vec2 vChromaPosition;\nvoid main() {\n   // Y, Cb, and Cr planes are uploaded as ALPHA textures.\n   float fY = texture2D(uTextureY, vLumaPosition).w;\n   float fCb = texture2D(uTextureCb, vChromaPosition).w;\n   float fCr = texture2D(uTextureCr, vChromaPosition).w;\n\n   // Premultipy the Y...\n   float fYmul = fY * 1.1643828125;\n\n   // And convert that to RGB!\n   gl_FragColor = vec4(\n     fYmul + 1.59602734375 * fCr - 0.87078515625,\n     fYmul - 0.39176171875 * fCb - 0.81296875 * fCr + 0.52959375,\n     fYmul + 2.017234375   * fCb - 1.081390625,\n     1\n   );\n}\n",vertexStripe:"precision mediump float;\n\nattribute vec2 aPosition;\nattribute vec2 aTexturePosition;\nvarying vec2 vTexturePosition;\n\nvoid main() {\n    gl_Position = vec4(aPosition, 0, 1);\n    vTexturePosition = aTexturePosition;\n}\n",fragmentStripe:"// extra 'stripe' texture fiddling to work around IE 11's poor performance on gl.LUMINANCE and gl.ALPHA textures\n\nprecision mediump float;\n\nuniform sampler2D uStripe;\nuniform sampler2D uTexture;\nvarying vec2 vTexturePosition;\nvoid main() {\n   // Y, Cb, and Cr planes are mapped into a pseudo-RGBA texture\n   // so we can upload them without expanding the bytes on IE 11\n   // which doesn't allow LUMINANCE or ALPHA textures\n   // The stripe textures mark which channel to keep for each pixel.\n   // Each texture extraction will contain the relevant value in one\n   // channel only.\n\n   float fLuminance = dot(\n      texture2D(uStripe, vTexturePosition),\n      texture2D(uTexture, vTexturePosition)\n   );\n\n   gl_FragColor = vec4(0, 0, 0, fLuminance);\n}\n"}},487:e=>{!function(){"use strict";function FrameSink(e,t){throw new Error("abstract")}FrameSink.prototype.drawFrame=function(e){throw new Error("abstract")},FrameSink.prototype.clear=function(){throw new Error("abstract")},e.exports=FrameSink}()},926:(e,t,i)=>{!function(){"use strict";var t=i(487),s=i(627);function SoftwareFrameSink(e){var t=this,i=e.getContext("2d"),d=null,h=null,u=null;return t.drawFrame=function drawFrame(t){var c=t.format;e.width===c.displayWidth&&e.height===c.displayHeight||(e.width=c.displayWidth,e.height=c.displayHeight),null!==d&&d.width==c.width&&d.height==c.height||function initImageData(e,t){for(var s=(d=i.createImageData(e,t)).data,h=e*t*4,u=0;u<h;u+=4)s[u+3]=255}(c.width,c.height),s.convertYCbCr(t,d.data);var l,f=c.cropWidth!=c.displayWidth||c.cropHeight!=c.displayHeight;f?(h||function initResampleCanvas(e,t){(h=document.createElement("canvas")).width=e,h.height=t,u=h.getContext("2d")}(c.cropWidth,c.cropHeight),l=u):l=i,l.putImageData(d,-c.cropLeft,-c.cropTop,c.cropLeft,c.cropTop,c.cropWidth,c.cropHeight),f&&i.drawImage(h,0,0,c.displayWidth,c.displayHeight)},t.clear=function(){i.clearRect(0,0,e.width,e.height)},t}SoftwareFrameSink.prototype=Object.create(t.prototype),e.exports=SoftwareFrameSink}()},895:(e,t,i)=>{!function(){"use strict";var t=i(487),s=i(826);function WebGLFrameSink(e){var t,i,d=this,h=WebGLFrameSink.contextForCanvas(e);if(null===h)throw new Error("WebGL unavailable");function compileShader(e,t){var i=h.createShader(e);if(h.shaderSource(i,t),h.compileShader(i),!h.getShaderParameter(i,h.COMPILE_STATUS)){var s=h.getShaderInfoLog(i);throw h.deleteShader(i),new Error("GL shader compilation for "+e+" failed: "+s)}return i}var u,c,l,f,_,p,m,V,g,v,b=new Float32Array([-1,-1,1,-1,-1,1,-1,1,1,-1,1,1]),y={},T={},k={};function createOrReuseTexture(e,t){return y[e]&&!t||(y[e]=h.createTexture()),y[e]}function uploadTexture(e,t,i,s,d){var u=!y[e]||t,c=createOrReuseTexture(e,t);if(h.activeTexture(h.TEXTURE0),WebGLFrameSink.stripe){var l=!y[e+"_temp"]||t,f=createOrReuseTexture(e+"_temp",t);h.bindTexture(h.TEXTURE_2D,f),l?(h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_S,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_T,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MIN_FILTER,h.NEAREST),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MAG_FILTER,h.NEAREST),h.texImage2D(h.TEXTURE_2D,0,h.RGBA,i/4,s,0,h.RGBA,h.UNSIGNED_BYTE,d)):h.texSubImage2D(h.TEXTURE_2D,0,0,0,i/4,s,h.RGBA,h.UNSIGNED_BYTE,d);var _=y[e+"_stripe"],p=!_||t;p&&(_=createOrReuseTexture(e+"_stripe",t)),h.bindTexture(h.TEXTURE_2D,_),p&&(h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_S,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_T,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MIN_FILTER,h.NEAREST),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MAG_FILTER,h.NEAREST),h.texImage2D(h.TEXTURE_2D,0,h.RGBA,i,1,0,h.RGBA,h.UNSIGNED_BYTE,function buildStripe(e){if(k[e])return k[e];for(var t=e,i=new Uint32Array(t),s=0;s<t;s+=4)i[s]=255,i[s+1]=65280,i[s+2]=16711680,i[s+3]=4278190080;return k[e]=new Uint8Array(i.buffer)}(i)))}else h.bindTexture(h.TEXTURE_2D,c),u?(h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_S,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_T,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MIN_FILTER,h.LINEAR),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MAG_FILTER,h.LINEAR),h.texImage2D(h.TEXTURE_2D,0,h.ALPHA,i,s,0,h.ALPHA,h.UNSIGNED_BYTE,d)):h.texSubImage2D(h.TEXTURE_2D,0,0,0,i,s,h.ALPHA,h.UNSIGNED_BYTE,d)}function unpackTexture(e,t,s,d){var m=y[e];h.useProgram(i);var V=T[e];V&&!t||(h.activeTexture(h.TEXTURE0),h.bindTexture(h.TEXTURE_2D,m),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_S,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_T,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MIN_FILTER,h.LINEAR),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MAG_FILTER,h.LINEAR),h.texImage2D(h.TEXTURE_2D,0,h.RGBA,s,d,0,h.RGBA,h.UNSIGNED_BYTE,null),V=T[e]=h.createFramebuffer()),h.bindFramebuffer(h.FRAMEBUFFER,V),h.framebufferTexture2D(h.FRAMEBUFFER,h.COLOR_ATTACHMENT0,h.TEXTURE_2D,m,0);var g=y[e+"_temp"];h.activeTexture(h.TEXTURE1),h.bindTexture(h.TEXTURE_2D,g),h.uniform1i(p,1);var v=y[e+"_stripe"];h.activeTexture(h.TEXTURE2),h.bindTexture(h.TEXTURE_2D,v),h.uniform1i(_,2),h.bindBuffer(h.ARRAY_BUFFER,u),h.enableVertexAttribArray(c),h.vertexAttribPointer(c,2,h.FLOAT,!1,0,0),h.bindBuffer(h.ARRAY_BUFFER,l),h.enableVertexAttribArray(f),h.vertexAttribPointer(f,2,h.FLOAT,!1,0,0),h.viewport(0,0,s,d),h.drawArrays(h.TRIANGLES,0,b.length/2),h.bindFramebuffer(h.FRAMEBUFFER,null)}function attachTexture(e,i,s){h.activeTexture(i),h.bindTexture(h.TEXTURE_2D,y[e]),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_S,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_WRAP_T,h.CLAMP_TO_EDGE),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MIN_FILTER,h.LINEAR),h.texParameteri(h.TEXTURE_2D,h.TEXTURE_MAG_FILTER,h.LINEAR),h.uniform1i(h.getUniformLocation(t,e),s)}function initProgram(e,t){var i=compileShader(h.VERTEX_SHADER,e),s=compileShader(h.FRAGMENT_SHADER,t),d=h.createProgram();if(h.attachShader(d,i),h.attachShader(d,s),h.linkProgram(d),!h.getProgramParameter(d,h.LINK_STATUS)){var u=h.getProgramInfoLog(d);throw h.deleteProgram(d),new Error("GL program linking failed: "+u)}return d}return d.drawFrame=function(y){var T=y.format,k=!t||e.width!==T.displayWidth||e.height!==T.displayHeight;if(k&&(e.width=T.displayWidth,e.height=T.displayHeight,d.clear()),t||function init(){if(WebGLFrameSink.stripe){i=initProgram(s.vertexStripe,s.fragmentStripe),h.getAttribLocation(i,"aPosition"),l=h.createBuffer();var e=new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]);h.bindBuffer(h.ARRAY_BUFFER,l),h.bufferData(h.ARRAY_BUFFER,e,h.STATIC_DRAW),f=h.getAttribLocation(i,"aTexturePosition"),_=h.getUniformLocation(i,"uStripe"),p=h.getUniformLocation(i,"uTexture")}t=initProgram(s.vertex,s.fragment),u=h.createBuffer(),h.bindBuffer(h.ARRAY_BUFFER,u),h.bufferData(h.ARRAY_BUFFER,b,h.STATIC_DRAW),c=h.getAttribLocation(t,"aPosition"),m=h.createBuffer(),V=h.getAttribLocation(t,"aLumaPosition"),g=h.createBuffer(),v=h.getAttribLocation(t,"aChromaPosition")}(),k){var setupTexturePosition=function(e,t,i){var s=T.cropLeft/i,d=(T.cropLeft+T.cropWidth)/i,u=(T.cropTop+T.cropHeight)/T.height,c=T.cropTop/T.height,l=new Float32Array([s,u,d,u,s,c,s,c,d,u,d,c]);h.bindBuffer(h.ARRAY_BUFFER,e),h.bufferData(h.ARRAY_BUFFER,l,h.STATIC_DRAW)};setupTexturePosition(m,0,y.y.stride),setupTexturePosition(g,0,y.u.stride*T.width/T.chromaWidth)}uploadTexture("uTextureY",k,y.y.stride,T.height,y.y.bytes),uploadTexture("uTextureCb",k,y.u.stride,T.chromaHeight,y.u.bytes),uploadTexture("uTextureCr",k,y.v.stride,T.chromaHeight,y.v.bytes),WebGLFrameSink.stripe&&(unpackTexture("uTextureY",k,y.y.stride,T.height),unpackTexture("uTextureCb",k,y.u.stride,T.chromaHeight),unpackTexture("uTextureCr",k,y.v.stride,T.chromaHeight)),h.useProgram(t),h.viewport(0,0,e.width,e.height),attachTexture("uTextureY",h.TEXTURE0,0),attachTexture("uTextureCb",h.TEXTURE1,1),attachTexture("uTextureCr",h.TEXTURE2,2),h.bindBuffer(h.ARRAY_BUFFER,u),h.enableVertexAttribArray(c),h.vertexAttribPointer(c,2,h.FLOAT,!1,0,0),h.bindBuffer(h.ARRAY_BUFFER,m),h.enableVertexAttribArray(V),h.vertexAttribPointer(V,2,h.FLOAT,!1,0,0),h.bindBuffer(h.ARRAY_BUFFER,g),h.enableVertexAttribArray(v),h.vertexAttribPointer(v,2,h.FLOAT,!1,0,0),h.drawArrays(h.TRIANGLES,0,b.length/2)},d.clear=function(){h.viewport(0,0,e.width,e.height),h.clearColor(0,0,0,0),h.clear(h.COLOR_BUFFER_BIT)},d.clear(),d}WebGLFrameSink.stripe=!1,WebGLFrameSink.contextForCanvas=function(e){var t={preferLowPowerToHighPerformance:!0,powerPreference:"low-power",failIfMajorPerformanceCaveat:!0,preserveDrawingBuffer:!0};return e.getContext("webgl",t)||e.getContext("experimental-webgl",t)},WebGLFrameSink.isAvailable=function(){var e,t=document.createElement("canvas");t.width=1,t.height=1;try{e=WebGLFrameSink.contextForCanvas(t)}catch(e){return!1}if(e){var i=e.TEXTURE0,s=e.createTexture(),d=new Uint8Array(16),h=WebGLFrameSink.stripe?1:4,u=WebGLFrameSink.stripe?e.RGBA:e.ALPHA,c=WebGLFrameSink.stripe?e.NEAREST:e.LINEAR;return e.activeTexture(i),e.bindTexture(e.TEXTURE_2D,s),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,c),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,c),e.texImage2D(e.TEXTURE_2D,0,u,h,4,0,u,e.UNSIGNED_BYTE,d),!e.getError()}return!1},WebGLFrameSink.prototype=Object.create(t.prototype),e.exports=WebGLFrameSink}()},627:(e,t,i)=>{!function(){"use strict";var t=i(877);e.exports={convertYCbCr:function convertYCbCr(e,i){var s=0|e.format.width,d=0|e.format.height,h=0|t(e.format.width/e.format.chromaWidth),u=0|t(e.format.height/e.format.chromaHeight),c=e.y.bytes,l=e.u.bytes,f=e.v.bytes,_=0|e.y.stride,p=0|e.u.stride,m=0|e.v.stride,V=s<<2,g=0,v=0,b=0,y=0,T=0,k=0,A=0,w=0,P=0,E=0,x=0,R=0,F=0,O=0,S=0,C=0,B=0,D=0;if(1==h&&1==u)for(A=0,w=V,D=0,C=0;C<d;C+=2){for(b=(v=C*_|0)+_|0,y=D*p|0,T=D*m|0,S=0;S<s;S+=2)P=0|l[y++],R=(409*(E=0|f[T++])|0)-57088|0,F=(100*P|0)+(208*E|0)-34816|0,O=(516*P|0)-70912|0,x=298*c[v++]|0,i[A]=x+R>>8,i[A+1]=x-F>>8,i[A+2]=x+O>>8,A+=4,x=298*c[v++]|0,i[A]=x+R>>8,i[A+1]=x-F>>8,i[A+2]=x+O>>8,A+=4,x=298*c[b++]|0,i[w]=x+R>>8,i[w+1]=x-F>>8,i[w+2]=x+O>>8,w+=4,x=298*c[b++]|0,i[w]=x+R>>8,i[w+1]=x-F>>8,i[w+2]=x+O>>8,w+=4;A+=V,w+=V,D++}else for(k=0,C=0;C<d;C++)for(B=0,g=C*_|0,y=(D=C>>u)*p|0,T=D*m|0,S=0;S<s;S++)P=0|l[y+(B=S>>h)],R=(409*(E=0|f[T+B])|0)-57088|0,F=(100*P|0)+(208*E|0)-34816|0,O=(516*P|0)-70912|0,x=298*c[g++]|0,i[k]=x+R>>8,i[k+1]=x-F>>8,i[k+2]=x+O>>8,k+=4}}}()},877:e=>{!function(){"use strict";e.exports=function depower(e){for(var t=0,i=e>>1;0!=i;)i>>=1,t++;if(e!==1<<t)throw"chroma plane dimensions must be power of 2 ratio to luma plane dimensions; got "+e;return t}}()},731:(e,t,i)=>{!function(){"use strict";var t=i(487),s=i(926),d=i(895),h={FrameSink:t,SoftwareFrameSink:s,WebGLFrameSink:d,attach:function(e,t){return("webGL"in(t=t||{})?t.webGL:d.isAvailable())?new d(e,t):new s(e,t)}};e.exports=h}()}},t={};function __webpack_require__(i){var s=t[i];if(void 0!==s)return s.exports;var d=t[i]={exports:{}};return e[i](d,d.exports,__webpack_require__),d.exports}__webpack_require__.d=(e,t)=>{for(var i in t)__webpack_require__.o(t,i)&&!__webpack_require__.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return(()=>{"use strict";var e=i,t=__webpack_require__(318);Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"OGVCompat",{enumerable:!0,get:function get(){return s.default}}),Object.defineProperty(e,"OGVLoader",{enumerable:!0,get:function get(){return d.default}}),Object.defineProperty(e,"OGVMediaError",{enumerable:!0,get:function get(){return h.default}}),Object.defineProperty(e,"OGVMediaType",{enumerable:!0,get:function get(){return u.default}}),Object.defineProperty(e,"OGVPlayer",{enumerable:!0,get:function get(){return c.default}}),Object.defineProperty(e,"OGVTimeRanges",{enumerable:!0,get:function get(){return l.default}}),e.OGVVersion=void 0;var s=t(__webpack_require__(523)),d=t(__webpack_require__(964)),h=t(__webpack_require__(759)),u=t(__webpack_require__(278)),c=t(__webpack_require__(869)),l=t(__webpack_require__(168)),f="1.8.9-20220406232920-cb5f7ff";e.OGVVersion=f,"object"==typeof window&&(window.OGVCompat=s.default,window.OGVLoader=d.default,window.OGVMediaError=h.default,window.OGVMediaType=u.default,window.OGVTimeRanges=l.default,window.OGVPlayer=c.default,window.OGVVersion=f)})(),i})()}));