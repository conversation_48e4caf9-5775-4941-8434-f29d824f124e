
var OGVDecoderAudioVorbisW = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(OGVDecoderAudioVorbisW) {
  OGVDecoderAudioVorbisW = OGVDecoderAudioVorbisW || {};


var b;b||(b=typeof OGVDecoderAudioVorbisW !== 'undefined' ? OGVDecoderAudioVorbisW : {});var h,k;b.ready=new Promise(function(a,c){h=a;k=c});var m=b,n=Object.assign({},b),p=(a,c)=>{throw c;},q="object"==typeof window,r="function"==typeof importScripts,t="",u,v,w,fs,x,y;
if("object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node)t=r?require("path").dirname(t)+"/":__dirname+"/",y=()=>{x||(fs=require("fs"),x=require("path"))},u=function(a,c){y();a=x.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")},w=a=>{a=u(a,!0);a.buffer||(a=new Uint8Array(a));return a},v=(a,c,e)=>{y();a=x.normalize(a);fs.readFile(a,function(d,f){d?e(d):c(f.buffer)})},1<process.argv.length&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),
process.on("unhandledRejection",function(a){throw a;}),p=(a,c)=>{if(noExitRuntime)throw process.exitCode=a,c;c instanceof z||A("exiting due to exception: "+c);process.exit(a)},b.inspect=function(){return"[Emscripten Module object]"};else if(q||r)r?t=self.location.href:"undefined"!=typeof document&&document.currentScript&&(t=document.currentScript.src),_scriptDir&&(t=_scriptDir),0!==t.indexOf("blob:")?t=t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1):t="",u=a=>{var c=new XMLHttpRequest;c.open("GET",
a,!1);c.send(null);return c.responseText},r&&(w=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),v=(a,c,e)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?c(d.response):e()};d.onerror=e;d.send(null)};b.print||console.log.bind(console);var A=b.printErr||console.warn.bind(console);Object.assign(b,n);n=null;b.quit&&(p=b.quit);var B;
b.wasmBinary&&(B=b.wasmBinary);var noExitRuntime=b.noExitRuntime||!0;"object"!=typeof WebAssembly&&C("no native wasm support detected");var D,E=!1,F,G;function H(){var a=D.buffer;F=a;b.HEAP8=new Int8Array(a);b.HEAP16=new Int16Array(a);b.HEAP32=new Int32Array(a);b.HEAPU8=G=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAPU32=new Uint32Array(a);b.HEAPF32=new Float32Array(a);b.HEAPF64=new Float64Array(a)}var I,J=[],K=[],L=[];function aa(){var a=b.preRun.shift();J.unshift(a)}var M=0,N=null,O=null;
b.preloadedImages={};b.preloadedAudios={};function C(a){if(b.onAbort)b.onAbort(a);a="Aborted("+a+")";A(a);E=!0;a=new WebAssembly.RuntimeError(a+". Build with -s ASSERTIONS=1 for more info.");k(a);throw a;}function P(){return Q.startsWith("data:application/octet-stream;base64,")}var Q;Q="ogv-decoder-audio-vorbis-wasm.wasm";if(!P()){var R=Q;Q=b.locateFile?b.locateFile(R,t):t+R}
function ba(){var a=Q;try{if(a==Q&&B)return new Uint8Array(B);if(w)return w(a);throw"both async and sync fetching of the wasm failed";}catch(c){C(c)}}
function ca(){if(!B&&(q||r)){if("function"==typeof fetch&&!Q.startsWith("file://"))return fetch(Q,{credentials:"same-origin"}).then(function(a){if(!a.ok)throw"failed to load wasm binary file at '"+Q+"'";return a.arrayBuffer()}).catch(function(){return ba()});if(v)return new Promise(function(a,c){v(Q,function(e){a(new Uint8Array(e))},c)})}return Promise.resolve().then(function(){return ba()})}
function S(a){for(;0<a.length;){var c=a.shift();if("function"==typeof c)c(b);else{var e=c.s;"number"==typeof e?void 0===c.o?da(e)():da(e)(c.o):e(void 0===c.o?null:c.o)}}}var T=[];function da(a){var c=T[a];c||(a>=T.length&&(T.length=a+1),T[a]=c=I.get(a));return c}
var ea={b:function(a,c,e){G.copyWithin(a,c,c+e)},a:function(a){var c=G.length;a>>>=0;if(2147483648<a)return!1;for(var e=1;4>=e;e*=2){var d=c*(1+.2/e);d=Math.min(d,a+100663296);var f=Math;d=Math.max(a,d);f=f.min.call(f,2147483648,d+(65536-d%65536)%65536);a:{try{D.grow(f-F.byteLength+65535>>>16);H();var g=1;break a}catch(l){}g=void 0}if(g)return!0}return!1},c:function(a){if(!noExitRuntime){if(b.onExit)b.onExit(a);E=!0}p(a,new z(a))},d:function(a,c,e){var d=D.buffer,f=new Uint32Array(d,a,c),g=[];if(0!==
a)for(a=0;a<c;a++){var l=f[a];d.slice?(l=d.slice(l,l+4*e),l=new Float32Array(l)):(l=new Float32Array(d,l,e),l=new Float32Array(l));g.push(l)}b.audioBuffer=g},e:function(a,c){b.audioFormat={channels:a,rate:c};b.loadedMetadata=!0}};
(function(){function a(f){b.asm=f.exports;D=b.asm.f;H();I=b.asm.n;K.unshift(b.asm.g);M--;b.monitorRunDependencies&&b.monitorRunDependencies(M);0==M&&(null!==N&&(clearInterval(N),N=null),O&&(f=O,O=null,f()))}function c(f){a(f.instance)}function e(f){return ca().then(function(g){return WebAssembly.instantiate(g,d)}).then(function(g){return g}).then(f,function(g){A("failed to asynchronously prepare wasm: "+g);C(g)})}var d={a:ea};M++;b.monitorRunDependencies&&b.monitorRunDependencies(M);if(b.instantiateWasm)try{return b.instantiateWasm(d,
a)}catch(f){return A("Module.instantiateWasm callback failed with error: "+f),!1}(function(){return B||"function"!=typeof WebAssembly.instantiateStreaming||P()||Q.startsWith("file://")||"function"!=typeof fetch?e(c):fetch(Q,{credentials:"same-origin"}).then(function(f){return WebAssembly.instantiateStreaming(f,d).then(c,function(g){A("wasm streaming compile failed: "+g);A("falling back to ArrayBuffer instantiation");return e(c)})})})().catch(k);return{}})();
b.___wasm_call_ctors=function(){return(b.___wasm_call_ctors=b.asm.g).apply(null,arguments)};b._ogv_audio_decoder_init=function(){return(b._ogv_audio_decoder_init=b.asm.h).apply(null,arguments)};b._ogv_audio_decoder_process_header=function(){return(b._ogv_audio_decoder_process_header=b.asm.i).apply(null,arguments)};b._ogv_audio_decoder_process_audio=function(){return(b._ogv_audio_decoder_process_audio=b.asm.j).apply(null,arguments)};
b._ogv_audio_decoder_destroy=function(){return(b._ogv_audio_decoder_destroy=b.asm.k).apply(null,arguments)};b._malloc=function(){return(b._malloc=b.asm.l).apply(null,arguments)};b._free=function(){return(b._free=b.asm.m).apply(null,arguments)};var U;function z(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}O=function fa(){U||V();U||(O=fa)};
function V(){function a(){if(!U&&(U=!0,b.calledRun=!0,!E)){S(K);h(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();L.unshift(c)}S(L)}}if(!(0<M)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)aa();S(J);0<M||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},1);a()},1)):a())}}b.run=V;
if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();V();var W,X;function ha(a){if(W&&X>=a)return W;W&&b._free(W);X=a;return W=b._malloc(X)}var Y;"undefined"===typeof performance||"undefined"===typeof performance.now?Y=Date.now:Y=performance.now.bind(performance);function Z(a){var c=Y();a=a();b.cpuTime+=Y()-c;return a}b.loadedMetadata=!!m.audioFormat;b.audioFormat=m.audioFormat||null;b.audioBuffer=null;b.cpuTime=0;
Object.defineProperty(b,"processing",{get:function(){return!1}});b.init=function(a){Z(function(){b._ogv_audio_decoder_init()});a()};b.processHeader=function(a,c){var e=Z(function(){var d=a.byteLength,f=ha(d);(new Uint8Array(D.buffer,f,d)).set(new Uint8Array(a));return b._ogv_audio_decoder_process_header(f,d)});c(e)};b.processAudio=function(a,c){var e=Z(function(){var d=a.byteLength,f=ha(d);(new Uint8Array(D.buffer,f,d)).set(new Uint8Array(a));return b._ogv_audio_decoder_process_audio(f,d)});c(e)};
b.close=function(){};


  return OGVDecoderAudioVorbisW.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = OGVDecoderAudioVorbisW;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return OGVDecoderAudioVorbisW; });
else if (typeof exports === 'object')
  exports["OGVDecoderAudioVorbisW"] = OGVDecoderAudioVorbisW;
