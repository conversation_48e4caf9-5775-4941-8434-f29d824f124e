import { defineConfig, mergeConfig } from 'vite';
import baseConfig from './vite.config.base';

export default defineConfig((env) =>
    mergeConfig(
        baseConfig(env),
        defineConfig({
            build: {
                sourcemap: true,
                rollupOptions: {
                    output: {
                        experimentalMinChunkSize: 100_000,
                    },
                },
            },
        })
    )
);
