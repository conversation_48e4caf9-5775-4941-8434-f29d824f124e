{
    "extends": ["react-app"],
    "ignorePatterns": ["dist/**/*"],
    "rules": {
        "react/self-closing-comp": "warn",
        "prefer-const": "warn",
        "no-restricted-syntax": [
            "warn",
            {
                "message": "Please do not use makeStyles",
                "selector": "CallExpression[callee.name='makeStyles'], CallExpression[callee.name='withStyles']"
            }
        ]
    },
    "overrides": [
        {
            "files": ["*.ts", "*.tsx"], // Your TypeScript files extension

            "rules": {
                "@typescript-eslint/no-explicit-any": "warn",
                "@typescript-eslint/await-thenable": "warn",
                "@typescript-eslint/no-duplicate-enum-values": "warn",
                "@typescript-eslint/no-duplicate-type-constituents": "warn",
                "@typescript-eslint/no-empty-interface": "warn",

                "@typescript-eslint/no-unused-vars": [
                    "warn",
                    {
                        "args": "all",
                        "argsIgnorePattern": "^_",
                        "caughtErrors": "all",
                        "caughtErrorsIgnorePattern": "^_",
                        "destructuredArrayIgnorePattern": "^_",
                        "varsIgnorePattern": "^_",
                        "ignoreRestSiblings": true
                    }
                ]
            },

            "parserOptions": {
                "project": ["./tsconfig.eslint.json"] // Specify it only for TypeScript files
            }
        },
        {
            "files": ["*.tsx"],
            "rules": {
                "no-restricted-syntax": [
                    "warn",
                    {
                        "selector": "TSInterfaceDeclaration[id.name=/Props$/]",
                        "message": "Avoid using interfaces for react component props."
                    }
                ]
            }
        }
    ]
}
