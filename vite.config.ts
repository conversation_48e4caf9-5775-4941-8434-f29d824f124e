import { defineConfig, mergeConfig } from 'vite';
import baseConfig from './vite.config.base';

export default defineConfig((env) =>
    mergeConfig(
        baseConfig(env),
        defineConfig({
            server: {
                port: 3000,
                host: process.env.VITE_DEV_SERVER_HOST || 'localhost',
            },
            build: {
                emptyOutDir: true,
            },
        })
    )
);
