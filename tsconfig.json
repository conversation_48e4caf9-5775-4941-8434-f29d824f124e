{
    "compilerOptions": {
        "target": "ESNext",
        "lib": ["dom", "dom.iterable", "esnext"],
        "noImplicitAny": true,
        "noImplicitThis": true,
        "strictNullChecks": true,
        "allowJs": true,
        "skipLibCheck": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "strict": true,
        "forceConsistentCasingInFileNames": false, //Temp-Change: Original value true
        "noFallthroughCasesInSwitch": true,
        "module": "esnext",
        "moduleResolution": "node",
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",
        "types": ["vite/client" /*, "vite-plugin-svgr/client"*/],
        "baseUrl": "src"
    },
    "include": ["src"]
}
