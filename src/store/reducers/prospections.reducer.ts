import { BrandDto, VehicleModelDto } from 'api/Clients/Brands';
import { ProspectionsType } from 'store/actions/prospections.action';

export type ProspectionState = {
    brands: BrandDto[];
    isBrandsFetching: boolean;
    models: VehicleModelDto[];
    isModelsFetching: boolean;
    years: string[];
    isYearsFetching: boolean;
};

const initialState: ProspectionState = {
    brands: [],
    isBrandsFetching: false,
    models: [],
    isModelsFetching: false,
    years: [],
    isYearsFetching: false,
};

const prospectionsReducer = (state = initialState, action: { type: string; payload: any }) => {
    const { type, payload } = action;
    switch (type) {
        case ProspectionsType.FETCH_BRANDS_START:
            return { ...state, isBrandsFetching: true };
        case ProspectionsType.FETCH_BRANDS_SUCCESS:
            return {
                ...state,
                isBrandsFetching: false,
                brands: payload === null ? state.brands : (payload as BrandDto[]),
            };
        case ProspectionsType.FETCH_MODELS_START:
            return { ...state, isModelsFetching: true };
        case ProspectionsType.FETCH_MODELS_SUCCESS:
            return {
                ...state,
                isModelsFetching: false,
                models: payload === null ? state.models : (payload as VehicleModelDto[]),
            };
        case ProspectionsType.FETCH_YEARS_START:
            return { ...state, isYearsFetching: true };
        case ProspectionsType.FETCH_YEARS_SUCCESS:
            return {
                ...state,
                isYearsFetching: false,
                years: payload === null ? state.years : (payload as string[]),
            };
        default:
            return state;
    }
};

export default prospectionsReducer;
