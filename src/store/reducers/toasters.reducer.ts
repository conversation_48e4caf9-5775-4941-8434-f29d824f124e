import { NotificationData } from '../../common/components/NotificationPull/NotificationData';
import { ToastersType } from '../actions/toasters.action';

interface ToastersState {
    notifications: NotificationData[];
}

const initialState: ToastersState = {
    notifications: [],
};

const toastersReducer = (state = initialState, action: { type: string; payload: any }) => {
    switch (action.type) {
        case ToastersType.ADD_NEW_TOASTER:
            const newNotification = action.payload as NotificationData;
            if (!newNotification || !newNotification.key) return state;
            if (state.notifications.find((current) => current.key == newNotification.key))
                return state;
            const extendedNotifications = [newNotification, ...state.notifications];
            return { ...state, notifications: extendedNotifications };

        case ToastersType.REMOVE_TOASTER:
            const notificationKey = action.payload as string;
            const lostNotifications = state.notifications.filter((n) => n.key !== notificationKey);
            return { ...state, notifications: lostNotifications };

        default:
            return state;
    }
};

export default toastersReducer;
