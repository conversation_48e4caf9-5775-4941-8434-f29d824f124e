import { createSelector } from 'reselect';
import { RootState } from 'store';

const selectProspections = (state: RootState) => state.prospections;

export const selectBrands = createSelector(
    [selectProspections],
    (prospections) => prospections.brands
);
export const selectModels = createSelector(
    [selectProspections],
    (prospections) => prospections.models
);
export const selectYears = createSelector(
    [selectProspections],
    (prospections) => prospections.years
);
