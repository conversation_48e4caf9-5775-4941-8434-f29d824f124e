import CryptoJS from 'crypto-js';
import { createTransform } from 'redux-persist';

const secretKey = import.meta.env.VITE_SECRET_STORAGE_KEY;

export function createEncryptedTransform<T>(reducerKeys: string[]) {
    return createTransform<T, any>(
        (inboundState) => {
            try {
                const encrypted = CryptoJS.AES.encrypt(
                    JSON.stringify(inboundState),
                    secretKey
                ).toString();

                return encrypted;
            } catch (err) {
                console.error(`Encrypt error: ${reducerKeys.join(', ')}`, err);
                return inboundState;
            }
        },
        (outboundState) => {
            try {
                const bytes = CryptoJS.AES.decrypt(outboundState, secretKey);
                const decrypted = bytes.toString(CryptoJS.enc.Utf8);

                return JSON.parse(decrypted);
            } catch (err) {
                console.error(`Decrypt error: ${reducerKeys.join(', ')}`, err);
                return outboundState;
            }
        },
        { whitelist: reducerKeys }
    );
}
