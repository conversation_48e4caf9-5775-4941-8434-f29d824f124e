import BrandsAP<PERSON>, { BrandDto, VehicleModelDto } from 'api/Clients/Brands';
import store from 'store';
import { notifyLoadingError } from './toasters.action';

export const ProspectionsType = {
    FETCH_BRANDS_START: 'FETCH_BRANDS_START',
    FETCH_BRANDS_SUCCESS: 'FETCH_BRANDS_SUCCESS',
    FETCH_MODELS_START: 'FETCH_MODELS_START',
    FETCH_MODELS_SUCCESS: 'FETCH_MODELS_SUCCESS',
    FETCH_YEARS_START: 'FETCH_YEARS_START',
    FETCH_YEARS_SUCCESS: 'FETCH_YEARS_SUCCESS',
};

const getState = () => {
    return store.getState().prospections;
};

const fetchBrandsStart = () => ({
    type: ProspectionsType.FETCH_BRANDS_START,
});

const fetchBrandsSuccess = (brands: BrandDto[] | null) => ({
    type: ProspectionsType.FETCH_BRANDS_SUCCESS,
    payload: brands,
});

const fetchModelsStart = () => ({
    type: ProspectionsType.FETCH_MODELS_START,
});

const fetchModelsSuccess = (models: VehicleModelDto[] | null) => ({
    type: ProspectionsType.FETCH_MODELS_SUCCESS,
    payload: models,
});

const fetchYearsStart = () => ({
    type: ProspectionsType.FETCH_YEARS_START,
});

const fetchYearsSuccess = (models: string[] | null) => ({
    type: ProspectionsType.FETCH_YEARS_SUCCESS,
    payload: models,
});

export const fetchAllBrandsAsync = () => async (dispatch: any) => {
    dispatch(fetchBrandsStart());
    try {
        if (getState().brands?.length) {
            dispatch(fetchBrandsSuccess(null));
            return;
        }

        const response = await BrandsAPI.getBrands();
        dispatch(fetchBrandsSuccess(response));
    } catch (error) {
        console.error(error);
        dispatch(notifyLoadingError());
    }
};

export const fetchAllModelsAsync =
    (brandIds: number[], allModels: boolean) => async (dispatch: any) => {
        dispatch(fetchModelsStart());
        try {
            if (
                !allModels &&
                getState().models.length &&
                JSON.stringify(getState().brands.map((b) => b.id)) === JSON.stringify(brandIds)
            ) {
                dispatch(fetchModelsSuccess(null));
            }

            const response = await BrandsAPI.getModelsMultiplesBrands(brandIds, allModels);
            dispatch(fetchModelsSuccess(response));
        } catch (error) {
            console.error(error);
            dispatch(notifyLoadingError());
        }
    };

export const fetchAllYearsAsync =
    (modelIds: number[], allYears: boolean) => async (dispatch: any) => {
        dispatch(fetchYearsStart());
        try {
            if (
                !allYears &&
                getState().years.length &&
                JSON.stringify(getState().models.map((b) => b.id)) === JSON.stringify(modelIds)
            ) {
                dispatch(fetchModelsSuccess(null));
            }

            const response = await BrandsAPI.getYearsMultiplesModels(modelIds, allYears);
            dispatch(fetchYearsSuccess(response));
        } catch (_) {
            dispatch(notifyLoadingError());
        }
    };
