import i18next from 'i18next';
import { NotificationType } from '../../common/components/Notification/INotificationProps';
import { NotificationData } from '../../common/components/NotificationPull/NotificationData';

export const ToastersType = {
    ADD_NEW_TOASTER: 'ADD_NEW_TOASTER',
    REMOVE_TOASTER: 'REMOVE_TOASTER',
};

/**
 * @deprecated use useToasters hook instead
 */
export const setNewToaster = (newToaster: NotificationData) => {
    return {
        type: ToastersType.ADD_NEW_TOASTER,
        payload: newToaster,
    };
};

/**
 * @deprecated
 */
export const removeToaster = (toasterKey: string) => {
    return {
        type: ToastersType.REMOVE_TOASTER,
        payload: toasterKey,
    };
};

/**
 * @deprecated
 */
export const notifyLoadingError = () =>
    setNewToaster(
        new NotificationData(
            i18next.t('toasters.errorOccurredWhenLoading'),
            i18next.t('toasters.errorOccurred'),
            NotificationType.danger
        )
    );

/**
 * @deprecated
 */
export const notifySavingError = () =>
    setNewToaster(
        new NotificationData(
            i18next.t('toasters.errorOccurredWhenSaving'),
            i18next.t('toasters.errorOccurred'),
            NotificationType.danger
        )
    );
