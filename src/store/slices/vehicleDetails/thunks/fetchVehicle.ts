import { createAsyncThunk } from '@reduxjs/toolkit';
import { VehicleDetailsWithExtraResponse, VehiclesCrmApi } from 'api/customers';
import { isCmosError } from 'api/error';

type Arg = {
    id: string;
};

const fetchVehicleThunk = createAsyncThunk<VehicleDetailsWithExtraResponse, Arg>(
    'vehicleDetails/fetchVehicle',
    async ({ id }, { rejectWithValue }) => {
        try {
            const response = await VehiclesCrmApi.getVehicleDetailsWithExtra(id);
            return response;
        } catch (e: unknown) {
            if (isCmosError(e) && e.cmosCode === 'EntityNotFound') {
                return rejectWithValue('NOT_FOUND');
            }
            if (e instanceof Error) {
                return rejectWithValue(e.message);
            }
            throw e;
        }
    }
);

export default fetchVehicleThunk;
