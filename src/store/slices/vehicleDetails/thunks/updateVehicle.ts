import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    RecommendedServiceFrequency,
    UsageType,
    VehicleDetailsDto,
    VehicleDetailsUpdate,
    VehiclesCrmApi,
} from 'api/customers';
import { Future } from 'utils/future';

type Arg = {
    id: string;
    update: UpdateVehicleThunkUpdate;
};

export type UpdateVehicleThunkUpdate = {
    bdcAdvisor?: { id: string; name: string } | null;
    brand?: string; // empty string = no value
    model?: string; // empty string = no value
    year?: string; // empty string = no value
    plates?: string; // empty string = no value
    vin?: string; // empty string = no value
    mileage?: number | null;
    saleDate?: string | null;
    lastServiceDateWithMe?: string | null;
    lastServiceDateWithAnother?: string | null;
    usageType?: UsageType | null;
    recommendedServiceFrequency?: RecommendedServiceFrequency;
    soldByDealer?: boolean;
};

const updateVehicleThunk = createAsyncThunk<void, Arg>(
    'vehicleDetails/updateVehicle',
    async ({ id, update }) => {
        await DebouncedUpdateVehicleDetails.INSTANCE.update(id, {
            bdcAdvisorId: update.bdcAdvisor?.id,
            brand: update.brand,
            model: update.model,
            year: update.year,
            plates: update.plates,
            vin: update.vin,
            mileage: update.mileage,
            saleDate: update.saleDate,
            lastServiceDateWithMe: update.lastServiceDateWithMe,
            lastServiceDateWithAnother: update.lastServiceDateWithAnother,
            usageType: update.usageType,
            recommendedServiceFrequency: update.recommendedServiceFrequency,
            soldByDealer: update.soldByDealer,
        });
    }
);

export default updateVehicleThunk;

class DebouncedUpdateVehicleDetails {
    private readonly _update: Record<string, VehicleDetailsUpdate> = {};
    private readonly _futures: Record<string, Future<VehicleDetailsDto>> = {};
    private readonly _timeout: number;

    public static INSTANCE = new DebouncedUpdateVehicleDetails(1000);

    constructor(timeout: number) {
        this._timeout = timeout;
    }

    update(vehicleId: string, update: VehicleDetailsUpdate) {
        // update the pending update payload
        this._update[vehicleId] = {
            ...this._update[vehicleId],
            ...update,
        };

        if (this._futures[vehicleId]) {
            return this._futures[vehicleId].getPromise();
        } else {
            const future = new Future<VehicleDetailsDto>();
            this._futures[vehicleId] = future;

            setTimeout(() => {
                const update = this._update[vehicleId];
                delete this._update[vehicleId];

                const promise = VehiclesCrmApi.updateVehicleDetails(vehicleId, update);
                future.fromPromise(promise);
                promise.finally(() => {
                    if (this._futures[vehicleId] === future) delete this._futures[vehicleId];
                });
            }, this._timeout);

            return future.getPromise();
        }
    }
}
