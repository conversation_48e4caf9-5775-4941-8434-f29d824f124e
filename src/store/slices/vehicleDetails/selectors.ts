import { createSelector } from 'reselect';
import { RootState } from 'store';
import { State, VehicleState } from '.';

const selectSlice = (r: RootState) => r.vehicleDetails;

export function internalSelectVehicleDetails(state: State, id: string): VehicleState {
    const vehicleState: VehicleState | undefined = state.vehicles[id];
    if (!vehicleState) {
        return {
            details: null,
            isLoading: false,
            error: null,
            isNotFound: false,
            expandedContactSections: {},
        };
    }
    return vehicleState;
}

export const selectVehicleDetails = createSelector(
    [selectSlice, (_: RootState, id: string) => id],
    (state, id) => internalSelectVehicleDetails(state, id)
);

export const selectVehicleParams = createSelector(selectSlice, (s) => s.vehicleParams);

export const selectCustomerStatuses = createSelector(
    selectVehicleParams,
    (s) => s?.customerStatuses ?? []
);

export const selectRecommendedServiceFrequencyOptions = createSelector(
    selectVehicleParams,
    (s) => s?.recommendedServiceFrequencyOptions ?? []
);

export const selectProspectionPriorities = createSelector(
    selectVehicleParams,
    (s) => s?.prospectionPriorities ?? []
);

export const selectRetentionBands = createSelector(
    selectVehicleParams,
    (s) => s?.retentionBands ?? []
);
