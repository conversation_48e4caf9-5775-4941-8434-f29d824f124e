import { createSlice, PayloadAction, SerializedError } from '@reduxjs/toolkit';
import { VehicleAvailableParams, VehicleDetailsWithExtraResponse } from 'api/customers';
import { DateTime } from 'luxon';
import { internalSelectVehicleDetails } from './selectors';
import fetchVehicleThunk from './thunks/fetchVehicle';
import updateVehicleThunk from './thunks/updateVehicle';

export * from './selectors';

export { fetchVehicleThunk, updateVehicleThunk };

export type VehicleState = {
    details: VehicleDetailsWithExtraResponse | null;
    error: SerializedError | null;
    isLoading: boolean;
    isNotFound: boolean;
    expandedContactSections: Record<string, boolean>;
};

export type State = {
    vehicleId: string;
    vehicles: Record<string, VehicleState>;
    vehicleParams: VehicleAvailableParams | null;
};

const initialState: () => State = () => ({
    vehicles: {},
    vehicleId: '',
    vehicleParams: null,
});

const slice = createSlice({
    name: 'vehicleDetails',
    initialState: initialState(),

    reducers: {
        setVehicleParams: (state, action: PayloadAction<VehicleAvailableParams>) => {
            state.vehicleParams = action.payload;
        },

        toggleSectionExpanded: (
            state,
            action: PayloadAction<{ vehicleId: string; customerId: string }>
        ) => {
            const { vehicleId, customerId } = action.payload;
            const vehicle = internalSelectVehicleDetails(state, vehicleId);

            vehicle.expandedContactSections ??= {};
            vehicle.expandedContactSections[customerId] =
                !vehicle.expandedContactSections[customerId];

            state.vehicles[vehicleId] = vehicle;
        },
    },

    extraReducers(builder) {
        builder
            .addCase(fetchVehicleThunk.pending, (state, result) => {
                const id = result.meta.arg.id;
                state.vehicleId = id;
                const vehicle = internalSelectVehicleDetails(state, id);
                vehicle.isLoading = true;
                state.vehicles[id] = vehicle;
            })
            .addCase(fetchVehicleThunk.fulfilled, (state, result) => {
                const id = result.meta.arg.id;
                state.vehicleId = id;
                const vehicle = internalSelectVehicleDetails(state, id);
                state.vehicles[id] = vehicle;
                vehicle.isLoading = false;
                vehicle.error = null;
                vehicle.details = result.payload;
                const contacts = vehicle.details?.details?.contacts ?? [];
                const expandedContactSections: Record<string, boolean> = {};
                contacts.forEach((contact: { customerId: string }) => {
                    const previousExpandedState =
                        vehicle.expandedContactSections?.[contact.customerId];
                    expandedContactSections[contact.customerId] = previousExpandedState ?? true;
                });

                vehicle.expandedContactSections = expandedContactSections;
            })
            .addCase(fetchVehicleThunk.rejected, (state, result) => {
                const id = result.meta.arg.id;
                state.vehicleId = id;
                const vehicle = internalSelectVehicleDetails(state, id);
                vehicle.isLoading = false;
                vehicle.error = result.error;
                vehicle.isNotFound = result.payload === 'NOT_FOUND';
                if (
                    typeof result.payload === 'string' &&
                    result.payload !== 'NOT_FOUND' &&
                    vehicle.error.message === 'Rejected'
                ) {
                    vehicle.error.message = result.payload;
                }
                state.vehicles[id] = vehicle;
            });

        builder.addCase(updateVehicleThunk.pending, (state, action) => {
            const { id, update } = action.meta.arg;

            const vehicleState = state.vehicles[id];

            if (!vehicleState) {
                console.warn(
                    `handling updateVehicleThunk.pending failed: vehicle ${id} is not in the state`
                );
                return;
            }

            if (!vehicleState.details) {
                return;
            }

            const details = vehicleState.details.details;

            if (update.bdcAdvisor !== undefined) {
                details.bdcAdvisor = update.bdcAdvisor
                    ? { id: update.bdcAdvisor.id, name: update.bdcAdvisor.name }
                    : null;
            }

            if (update.brand !== undefined) details.brand = update.brand;
            if (update.model !== undefined) details.model = update.model;
            if (update.year !== undefined) details.year = update.year;
            if (update.plates !== undefined) details.plates = update.plates;
            if (update.vin !== undefined) details.vin = update.vin;
            if (update.mileage !== undefined) details.mileage = update.mileage;
            if (update.saleDate !== undefined)
                details.dateOfSale = update.saleDate
                    ? { date: update.saleDate, monthsSince: monthsSince(update.saleDate) }
                    : null;
            if (update.lastServiceDateWithMe !== undefined)
                details.lastServiceWithMe = update.lastServiceDateWithMe
                    ? {
                          date: update.lastServiceDateWithMe,
                          monthsSince: monthsSince(update.lastServiceDateWithMe),
                      }
                    : null;
            if (update.lastServiceDateWithAnother !== undefined)
                details.lastServiceWithAnother = update.lastServiceDateWithAnother
                    ? {
                          date: update.lastServiceDateWithAnother,
                          monthsSince: monthsSince(update.lastServiceDateWithAnother),
                      }
                    : null;

            if (details.lastServiceWithAnother && details.lastServiceWithMe) {
                details.lastService =
                    details.lastServiceWithMe.monthsSince >
                    details.lastServiceWithAnother.monthsSince
                        ? details.lastServiceWithAnother
                        : details.lastServiceWithMe;
            } else {
                details.lastService = details.lastServiceWithMe ?? details.lastServiceWithAnother;
            }

            if (update.usageType !== undefined) details.usageType = update.usageType;

            if (update.recommendedServiceFrequency !== undefined)
                details.recommendedServiceFrequency = {
                    km: update.recommendedServiceFrequency.km,
                    months: update.recommendedServiceFrequency.months,
                };

            if (update.soldByDealer !== undefined) details.soldByDealer = update.soldByDealer;
        });
    },
});

function monthsSince(date: string): number {
    const d = DateTime.fromISO(date);
    const now = DateTime.now();

    return (now.year - d.year) * 12 + (now.month - d.month);
}

const vehicleDetailsSliceReducer = slice.reducer;

export const vehicleDetailsActions = slice.actions;

export default vehicleDetailsSliceReducer;
