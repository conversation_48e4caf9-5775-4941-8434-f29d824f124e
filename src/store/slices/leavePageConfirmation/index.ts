import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { Report, ReportColumn } from 'api/Reports';

type State = {
    hasUnsavedChanges: boolean;
};

const initialState: State = {
    hasUnsavedChanges: false,
};

const slice = createSlice({
    name: 'leavePageConfirmation',
    initialState,
    reducers: {
        setHasUnsavedChanges: (state, action: PayloadAction<boolean>) => {
            state.hasUnsavedChanges = action.payload;
        },
    },
});

const leavePageConfirmationReducer = slice.reducer;
export const { setHasUnsavedChanges } = slice.actions;
export const leavePageConfirmationActions = slice.actions;
export default leavePageConfirmationReducer;
