import { createSelector } from 'reselect';
import { RootState, useAppSelector } from '../..';

export const selectGlobalSettingsSlice = (state: RootState) => state.globalSettings;

export const selectSettings = createSelector(selectGlobalSettingsSlice, (slice) => {
    if (slice.settings === null) {
        throw new Error('Do not use this selector outside of AppIntializationGuard');
    }

    return slice.settings;
});

export const selectIanaTz = createSelector(selectSettings, (s) => s.internationalization.ianaTzId);

export const selectCountryCode = createSelector(
    selectSettings,
    (s) => s.internationalization.countryPhoneCode
);

export function useTz() {
    return useAppSelector(selectIanaTz);
}

export const selectAutoOrderNumber = createSelector(
    selectSettings,
    (s) => s.repairShopSettings?.features.autoOrderNumber
);

export const selectFeatureFlags = createSelector(
    selectSettings,
    (settings) => settings.featureFlags
);

export const selectRepairShopFeatures = createSelector(
    selectSettings,
    (settings) => settings.repairShopSettings?.features
);

export const selectIsSpreadsheetSelected = createSelector(
    selectGlobalSettingsSlice,
    (slice) => slice.isSpreadsheetSelected
);

export const selectIsCustomizableBannerClosed = createSelector(
    selectGlobalSettingsSlice,
    (slice) => slice.isCustomizableBannerClosed
);

export const selectCurrentShopId = createSelector(selectSettings, (s) => {
    if (s.appMode === 'RepairShop') {
        return s.uid;
    }
    return null;
});

export const selectRepairShopIntegrationAccountName = createSelector(
    selectSettings,
    (settings) => settings.repairShopSettings?.integrationAccountName
);

export const selectRepairShopQuotesIntegrationType = createSelector(
    selectSettings,
    (settings) => settings.repairShopSettings?.quotesIntegrationType
);

export const selectIsEnterprise = createSelector(selectSettings, (s) => s.appMode === 'Enterprise');

export const selectEnableOnlinePayment = createSelector(
    selectSettings,
    (s) => s.repairShopSettings?.features.enableOnlinePayments
);

export const selectEnableManualPaymentRegistration = createSelector(
    selectSettings,
    (settings) => settings.repairShopSettings?.features.enableManualPaymentRegistration
);

export const selectSyncAppointmentsThirdParty = createSelector(
    [selectRepairShopFeatures],
    (repairShopFeatures) => repairShopFeatures?.syncAppointmentsThirdPartyEnabled ?? false
);

export const selectRepairShopCampaignsConfiguration = createSelector(
    [selectSettings],
    (settings) => settings.repairShopSettings?.features.campaigns
);

export const selectRepairShopWpConfiguration = createSelector([selectSettings], (settings) => ({
    enableWorkshopJobIntegration:
        settings.repairShopSettings?.features.enableWorkshopJobIntegration,
    integrationAccountName: settings.repairShopSettings?.integrationAccountName,
}));

export const selectWorkshopPlannerEnabled = createSelector(
    selectSettings,
    (settings) => settings.repairShopSettings?.features.enableWp ?? false
);
