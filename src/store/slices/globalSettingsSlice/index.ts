import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import GlobalSettingsAPI from 'api/GlobalSettings';
import { BroadcastChannel } from 'broadcast-channel';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { FeatureFlags } from 'datacontracts/GlobalSettings/RepairShopSettingsDto';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // defaults to localStorage for web
import store, { RootState } from 'store';
import { selectSettings } from './selectors';

export * from './selectors';

export interface GlobalSettingsState {
    settings: GlobalSettingsDto | null;
    loading: boolean;
    fatalInitializationError: {
        message?: string;
        httpStatus?: number;
    } | null;
    initialized: boolean;
    actualized: boolean;
    isSpreadsheetSelected?: boolean;
    isCustomizableBannerClosed?: boolean;
}

const initialState: GlobalSettingsState = {
    loading: false,
    initialized: false,
    settings: null,
    actualized: false,
    fatalInitializationError: null,
};

const slice = createSlice({
    name: 'globalSettings',
    initialState,
    reducers: {
        setLoading: (state: GlobalSettingsState, { payload }: PayloadAction<boolean>) => {
            state.loading = payload;
        },

        setFatalInitializationError: (
            state: GlobalSettingsState,
            {
                payload,
            }: PayloadAction<{
                message?: string;
                httpStatus?: number;
            }>
        ) => {
            state.fatalInitializationError = payload;
        },

        // NOTE (MB) setSettings should ONLY be called with global settings from the backend
        setSettings: (
            state: GlobalSettingsState,
            { payload }: PayloadAction<GlobalSettingsDto>
        ) => {
            state.settings = payload;
            state.initialized = true;
            state.actualized = true;
        },
        setIsSpreadsheetSelected: (
            state: GlobalSettingsState,
            { payload }: PayloadAction<boolean | undefined>
        ) => {
            state.isSpreadsheetSelected = payload;
        },
        setIsCustomizableBannerClosed: (
            state: GlobalSettingsState,
            { payload }: PayloadAction<boolean | undefined>
        ) => {
            state.isCustomizableBannerClosed = payload;
        },
        updateRsFeatureFlags: (
            state: GlobalSettingsState,
            { payload }: PayloadAction<Partial<FeatureFlags>>
        ) => {
            if (state.settings?.repairShopSettings) {
                state.settings.repairShopSettings.features = {
                    ...state.settings.repairShopSettings.features,
                    ...payload,
                };
            }
        },
    },
});

export const globalSettingsPersistentReducer = persistReducer(
    {
        key: 'globalSettings',
        storage,
        blacklist: ['loading', 'initialized', 'actualized', 'fatalInitializationError'],
    },
    slice.reducer
);

export const globalSettingsActions = slice.actions;

export const loadGlobalSettingsThunk = createAsyncThunk(
    'globalSettings/loadGlobalSettings',
    async (_, { dispatch, getState }) => {
        const settings = await GlobalSettingsAPI.get(
            window.location.hostname,
            selectSettings(getState() as RootState).appMode === 'Enterprise'
        );
        dispatch(slice.actions.setSettings(settings));
        globalSettingsChannel.postMessage(settings);
    }
);

const globalSettingsChannel = new BroadcastChannel<GlobalSettingsDto>('globalSettings-updated');

globalSettingsChannel.onmessage = (message: GlobalSettingsDto) => {
    store.dispatch(slice.actions.setSettings(message));
};
