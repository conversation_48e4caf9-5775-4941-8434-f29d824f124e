import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { TerminologyType } from 'api/Terminologies';
import { ensureTerminologies } from './thunks/ensureTerminologies';

export interface TerminologyState {
    list: TerminologyDto[];
}

export interface TerminologyDto {
    id?: string;
    alias?: string;
    originalTitle: string;
    type: TerminologyType;
}

const initialState: TerminologyState = {
    list: [],
};

const slice = createSlice({
    name: 'terminology',
    initialState,
    reducers: {
        update: (state: TerminologyState, { payload }: PayloadAction<TerminologyDto>) => {
            state.list = state.list.map((t) => (t.id === payload.id ? payload : t));
        },
    },
    extraReducers(builder) {
        builder.addCase(ensureTerminologies.fulfilled, (state, { payload }) => {
            state.list = payload.terminologies;
        });
    },
});

export const terminologiesReducer = slice.reducer;
export const terminologyActions = slice.actions;
