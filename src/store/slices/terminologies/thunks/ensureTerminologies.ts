import { createAsyncThunk } from '@reduxjs/toolkit';
import TerminologiesAPI from 'api/Terminologies';
import EnterpriseTerminologiesAPI from 'api/enterprise/terminologies';
import { TerminologyDto } from 'store/slices/terminologies';

type EnsureTerminologiesResultArg = {
    repairShopKey: string;
    enterpriseId: number;
    isEnterprise: boolean;
};

export type EnsureTerminologiesResult = {
    terminologies: TerminologyDto[];
};

export const ensureTerminologies = createAsyncThunk<
    EnsureTerminologiesResult,
    EnsureTerminologiesResultArg
>(
    'terminologies/ensureTerminologies',
    async (arg: { repairShopKey: string; enterpriseId: number; isEnterprise: boolean }) => {
        const terminologies = arg.isEnterprise
            ? await EnterpriseTerminologiesAPI.fetchEnterpriseTerminologies(arg.enterpriseId)
            : await TerminologiesAPI.fetchTerminologies(arg.repairShopKey);

        return { terminologies };
    }
);
