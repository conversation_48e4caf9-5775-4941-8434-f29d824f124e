import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    CustomAppointmentReasonDto,
    CustomAppointmentReasonUpdatedDto,
} from 'api/appointmentReasons';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { fetchCustomAppointmentReasonsThunk } from './thunks';

export * from './selectors';
export * from './thunks';

export const _INTERNAL_APPOINTMENT_REASONS_CACHE_ID = Math.random().toString(16).substring(2);

type AppointmentReasonsState = {
    custom: CustomAppointmentReasonDto[];
    fetchedAt: number;

    /**
     * Random string, if it does not match current CACHE_ID,
     * the cached data is no longer valid.
     */
    cacheId: string;
};

const initialState: AppointmentReasonsState = {
    custom: [],
    fetchedAt: 0,
    cacheId: _INTERNAL_APPOINTMENT_REASONS_CACHE_ID,
};

const slice = createSlice({
    name: 'appointmentReasons',
    initialState,
    reducers: {
        updateCustomReasonFromPubnubEvent: (
            state,
            action: PayloadAction<CustomAppointmentReasonUpdatedDto>
        ) => {
            const index = state.custom.findIndex((x) => x.id === action.payload.id);
            if (index !== -1) {
                state.custom[index].name = action.payload.name;
                state.custom[index].color = action.payload.color;
            }
        },
    },
    extraReducers(builder) {
        builder.addCase(fetchCustomAppointmentReasonsThunk.fulfilled, (state, action) => {
            if (action.payload.fetched) {
                state.cacheId = _INTERNAL_APPOINTMENT_REASONS_CACHE_ID;
                state.fetchedAt = Date.now();
                state.custom = action.payload.list;
            }
        });
    },
});

const reducer = slice.reducer;

export const appointmentReasonsActions = slice.actions;

const appointmentReasonsReducer = persistReducer(
    {
        key: 'appointmentReasons',
        storage,
    },
    reducer
);

export default appointmentReasonsReducer;
