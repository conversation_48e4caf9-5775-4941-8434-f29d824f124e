import { createAsyncThunk } from '@reduxjs/toolkit';
import AppointmentReasonsApi, { CustomAppointmentReasonDto } from 'api/appointmentReasons';
import { RootState } from 'store';
import { _INTERNAL_APPOINTMENT_REASONS_CACHE_ID } from '.';
import { internalSelectAppointmentReasonsSlice } from './selectors';

export const fetchCustomAppointmentReasonsThunk = createAsyncThunk<
    { list: CustomAppointmentReasonDto[]; fetched: boolean },
    { staleTime: number }
>('appointmentReasons/fetchAppointmentReasons', async (arg, { getState }) => {
    const state = internalSelectAppointmentReasonsSlice(getState() as RootState);

    const isStale =
        _INTERNAL_APPOINTMENT_REASONS_CACHE_ID !== state.cacheId ||
        state.fetchedAt < Date.now() - arg.staleTime;

    if (!isStale) {
        return {
            fetched: false,
            list: [],
        };
    }

    const appointmentReasons = await AppointmentReasonsApi.getCustomAppointmentReasons();
    return {
        fetched: true,
        list: appointmentReasons.customAppointmentReasons,
    };
});
