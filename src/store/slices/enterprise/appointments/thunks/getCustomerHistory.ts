import { createAsyncThunk } from '@reduxjs/toolkit';
import EnterpriseAppointmentsApi from 'api/enterprise/appointments';

export const getCustomerHistory = createAsyncThunk(
    'appointments/customerHistory',
    async (arg: { shopId: string; customerId: string; vehicleId: string }) => {
        const response = await EnterpriseAppointmentsApi.getCustomerHistory(
            arg.shopId,
            arg.customerId,
            arg.vehicleId
        );
        return response;
    }
);
