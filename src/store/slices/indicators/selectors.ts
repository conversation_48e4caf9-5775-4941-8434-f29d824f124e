import { createSelector } from 'reselect';
import { RootState } from 'store';

const selectSlice = (r: RootState) => r.indicators;

export const selectSelectedIndicators = createSelector(selectSlice, (s) => s.selectedIndicators);
export const selectAllIndicators = createSelector(selectSlice, (s) => s.allIndicators);
export const selectFrom = createSelector(selectSlice, (s) => s.from);
export const selectTo = createSelector(selectSlice, (s) => s.to);
