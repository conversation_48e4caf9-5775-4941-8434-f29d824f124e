import { createSelector } from 'reselect';
import { RootState } from 'store';

const conversationsState = (state: RootState) => state.conversations;

export const selectFilter = createSelector(conversationsState, (state) => state.persistentFilters);
export const selectConversations = createSelector(
    conversationsState,
    (state) => state.conversations
);
export const selectSelectedConversationId = createSelector(
    conversationsState,
    (state) => state.shownConversationsId
);

export const conversationsLoading = createSelector(conversationsState, (state) => state.isLoading);
