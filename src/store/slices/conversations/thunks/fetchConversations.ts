import { createAsyncThunk } from '@reduxjs/toolkit';
import WhatsApp from 'api/whatsapp';
import { RootState } from 'store';

const fetchConversations = createAsyncThunk(
    'conversations/fetchConversations',
    async (_, { getState }) => {
        const { paging, persistentFilters } = (getState() as RootState).conversations;

        const request = {
            page: paging.page,
            limit: paging.limit,
            term: persistentFilters.searchTerm,
            chatBotMode: persistentFilters.inbox === 'chatBot',
            advisorMode: persistentFilters.inbox === 'advisor',
            isActive: persistentFilters.active === 'active',
            isClosed: persistentFilters.active === 'closed',
        };
        const conversations = await WhatsApp.listFilteredConversations(request);

        return { conversations };
    }
);

export default fetchConversations;
