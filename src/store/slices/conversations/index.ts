import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
    ConversationGotControlFlowMessage,
    ConversationUpdatedMessage,
    isConversationUpdatedMessage,
} from 'api/whatsapp';
import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import _ from 'lodash';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { UserState } from '../user';
import fetchConversations from './thunks/fetchConversations';
export type ConversationInboxFilter = 'advisor' | 'chatBot' | 'both';
export type ConversationActiveFilter = 'active' | 'closed';

export type PersistentFilters = {
    inbox: ConversationInboxFilter;
    active: ConversationActiveFilter;
    showFilter: boolean;
    searchTerm: string;
};

export type Paging = {
    page: number;
    limit: number;
};

export type ConversationsState = {
    conversations: ConversationDto[];
    shownConversationsId: number | null;
    isLoading: boolean;
    paging: Paging;
    persistentFilters: PersistentFilters;
};

export type LostControlFlowEvent = {
    conversationsId: number;
};

const initialState: ConversationsState = {
    conversations: [],
    shownConversationsId: null,
    isLoading: false,
    paging: {
        page: 0,
        limit: 100,
    },
    persistentFilters: {
        inbox: 'both',
        active: 'active',
        showFilter: false,
        searchTerm: '',
    },
};
const slice = createSlice({
    name: 'conversations',
    initialState,
    reducers: {
        setShownConversationId: (state: ConversationsState, { payload }) => {
            state.shownConversationsId = payload;
        },

        setNextPage: (state: ConversationsState) => {
            if (state.paging.page === 0 && state.conversations.length < state.paging.limit) {
                return; //dont increse page if loaded amount less than limit of page TODO (AG) add isLast condition
            }
            state.paging.page += 1;
        },

        setFilters: (state: ConversationsState, { payload }: PayloadAction<PersistentFilters>) => {
            if (_.isEqual(state.persistentFilters, payload)) {
                return;
            }

            if (state.persistentFilters.showFilter === payload.showFilter) {
                state.conversations = []; //reset only for regular filters, not visibility
            }

            state.paging.page = 0;
            state.persistentFilters = payload;
        },

        resetState: (state: ConversationsState) => {
            return initialState;
        },

        handleLostControlFlow: (
            state: ConversationsState,
            { payload }: PayloadAction<LostControlFlowEvent>
        ) => {
            const conversationId = payload.conversationsId;
            state.conversations = state.conversations.map((c) =>
                c.conversationId === conversationId ? { ...c, currentControlFlow: false } : c
            );
        },

        handleConversationUpdate: (
            state: ConversationsState,
            {
                payload: { user, message },
            }: PayloadAction<{
                user: Exclude<UserState['user'], null>;
                message: ConversationUpdatedMessage | ConversationGotControlFlowMessage;
            }>
        ) => {
            //TODO (AG) move to common logic
            const userId = user.key || -1;
            const role = user.role || '';
            const jobTitle = user.jobTitle || '';
            const availableForJob = ['administrator', 'manager', 'owner'];
            const availableForUserRoles = ['master', 'masterervice'];

            const canSeeAllConversations =
                availableForUserRoles.includes(role.toLocaleLowerCase()) ||
                availableForJob.includes(jobTitle.toLocaleLowerCase());

            if (!canSeeAllConversations && message.inChargeUserId !== userId) {
                console.debug(
                    `cannot handle conversation, role: ${role}, jobTitle: ${jobTitle}, key: ${message.inChargeUserId}`
                );
                //return;
            }

            const conversation: ConversationDto = {
                conversationId: message.conversationId,
                customerPhoneNumber: message.customerPhoneNumber,
                customerFirstName: message.customerFirstName,
                customerLastName: message.customerLastName,
                lastInboundMessage: message.lastInboundMessage,
                lastSentMessage: message.lastSentMessage,
                chatBotMode: message.chatBotMode,
                currentControlFlow: message.currentControlFlow,
                unreadMessages: message.unreadMessages,
                lastMessageContent: message.lastMessageContent,
                repairOrderId: isConversationUpdatedMessage(message)
                    ? message.repairOrderId
                    : message.repairOrderID, // I hate this so much, is consistent naming convention too much to ask for?
                appointmentId: isConversationUpdatedMessage(message)
                    ? message.appointmentId
                    : message.appointmentID, // x2
                vehicleId: message.vehicleId,
                massSendingId: message.massSendingId,
                inChargeUserId: message.inChargeUserId,
                customerId: message.customerId,
            };

            if (
                message.conversationId == state.shownConversationsId &&
                message.unreadMessages > 0
            ) {
                message = { ...message, unreadMessages: 0 };
            }
            state.conversations = state.conversations
                .filter((c) => c.conversationId !== message.conversationId)
                .concat(conversation);
        },
    },
    extraReducers(builder) {
        builder.addCase(fetchConversations.fulfilled, (state, { payload }) => {
            if (payload === null) return;

            let conversationsToAdd = payload.conversations;
            if (state.conversations.length > 0) {
                conversationsToAdd = conversationsToAdd.filter((newItem) => {
                    const found = state.conversations.find(
                        (existingItem) => existingItem.conversationId === newItem.conversationId
                    );
                    return !found;
                });
            }

            state.conversations.push(...conversationsToAdd);
            state.isLoading = false;
        });

        builder.addCase(fetchConversations.pending, (state, { payload }) => {
            //state.isLoading = true; (AG) CMOS-1964, investigate
        });

        builder.addCase(fetchConversations.rejected, (state, { payload }) => {
            //state.isLoading = false; (AG) CMOS-1964, investigate
        });
    },
});

export const conversationsReducer = persistReducer(
    {
        key: 'conversations',
        storage,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        whitelist: <(keyof ConversationsState)[]>['persistentFilters'],
    },
    slice.reducer
);

export const {
    setShownConversationId,
    setNextPage,
    setFilters,
    resetState,
    handleConversationUpdate,
    handleLostControlFlow,
} = slice.actions;
