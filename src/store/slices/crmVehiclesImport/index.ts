import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

export * from './selectors';

type CrmVehiclesImportState = {
    initiatedImportIds: string[];
};

const initialState: CrmVehiclesImportState = {
    initiatedImportIds: [],
};

const slice = createSlice({
    name: 'crmVehiclesImport',
    initialState,
    reducers: {
        addInitiatedImport: (state, { payload }: PayloadAction<string>) => {
            if (!state.initiatedImportIds.includes(payload)) state.initiatedImportIds.push(payload);
        },
        removeInitiatedImport: (state, { payload }: PayloadAction<string>) => {
            state.initiatedImportIds = state.initiatedImportIds.filter((x) => x !== payload);
        },
    },
});

const reducer = slice.reducer;

export const crmVehiclesImportActions = slice.actions;

export const crmVehiclesImportReducer = persistReducer(
    { key: 'crmVehiclesImport', storage, whitelist: ['initiatedImportIds'] },
    reducer
);
