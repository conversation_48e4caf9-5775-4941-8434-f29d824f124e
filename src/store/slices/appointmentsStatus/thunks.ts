import { createAsyncThunk } from '@reduxjs/toolkit';
import AccountApi from 'api/account';
import { RootState } from 'store';
import { AppointmentsStatusState } from '.';
import { selectDisplayableAppointmentsStatusColumns, selectServiceAdvisorType } from './selectors';

export const fetchAppointmentsStatusUiStateThunk = createAsyncThunk(
    'appointments/fetchAppointmentsStatusUiState',
    async () => {
        const uiState = await AccountApi.getValue('appointments_status_ui_state');

        return getUiStateOrNull(uiState);
    }
);

export const toggleDisplayedStatusFieldThunk = createAsyncThunk(
    'appointments/toggleAppointmentsStatusDisplayedField',
    async ({ key, shown }: { key: string; shown: boolean }, { getState }) => {
        const displayedColumns = [
            ...selectDisplayableAppointmentsStatusColumns(getState() as RootState),
        ];
        const serviceAdvisorType = selectServiceAdvisorType(getState() as RootState);

        await AccountApi.setValue(
            'appointments_status_ui_state',
            JSON.stringify({ columns: displayedColumns, serviceAdvisorType })
        );
    }
);

export const toggleServiceAdvisorTypeThunk = createAsyncThunk(
    'appointments/toggleServiceAdvisorTypeThunk',
    async ({ key }: { key: string }, { getState }) => {
        const displayedColumns = [
            ...selectDisplayableAppointmentsStatusColumns(getState() as RootState),
        ];

        await AccountApi.setValue(
            'appointments_status_ui_state',
            JSON.stringify({ columns: displayedColumns, serviceAdvisorType: key })
        );
    }
);

function getUiStateOrNull(payload: string): AppointmentsStatusState | null {
    let parsed: unknown;

    try {
        parsed = JSON.parse(payload);
    } catch {
        return null;
    }

    if (isValidState(parsed)) {
        return parsed;
    }
    return null;
}

function isValidState(parsed: unknown): parsed is AppointmentsStatusState {
    if (parsed === null || typeof parsed !== 'object') return false;

    const obj = parsed as object;
    if (!Object.hasOwn(obj, key('columns'))) {
        return false;
    }

    if (!Object.hasOwn(obj, key('serviceAdvisorType'))) {
        return false;
    }

    const str = (obj as { serviceAdvisorType: unknown }).serviceAdvisorType;
    if (!(typeof str === 'string')) return false;

    const arr = (obj as { columns: unknown }).columns;
    if (!(arr instanceof Array)) return false;

    return arr.every((x) => typeof x === 'string');
}

function key(k: keyof AppointmentsStatusState): string {
    return k;
}
