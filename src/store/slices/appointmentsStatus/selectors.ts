import { createSelector } from 'reselect';
import { RootState } from 'store';
import { DEFAULT_SHOWN_COLUMNS } from '.';

const selectSlice = (state: RootState) => state.appointmentsStatus;

export const selectAppointments = createSelector(
    selectSlice,
    (appointmentsStatus) => appointmentsStatus.appointments
);

export const selectActiveAppointments = createSelector(
    selectSlice,
    (appointmentsStatus) => appointmentsStatus.activeAppointments
);

export const selectServiceAdvisorType = createSelector(
    selectSlice,
    (appointmentsStatus) => appointmentsStatus.serviceAdvisorType
);

export const selectDisplayableAppointmentsStatusColumns = createSelector(selectSlice, (s) => {
    const defaultColumns = Object.values(DEFAULT_SHOWN_COLUMNS);
    const displayableAppointmentsStatusColumns = defaultColumns.filter((x) =>
        s.columns.includes(x)
    );
    return displayableAppointmentsStatusColumns;
});
