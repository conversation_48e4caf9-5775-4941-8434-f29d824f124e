import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import {
    fetchAppointmentsStatusUiStateThunk,
    toggleDisplayedStatusFieldThunk,
    toggleServiceAdvisorTypeThunk,
} from './thunks';

export type AppointmentDto = {
    appointmentId: string;
    date: string;
    serviceAdvisorFullName: string;
    serviceAdvisorInitials: string;
    customer: string;
    model: string;
    plates: string;
    status: AppointmentStatus;
    orderNumber: string;
};

export type AppointmentStatus =
    | 'Unconfirmed'
    | 'Confirmed'
    | 'CustomerArrived'
    | 'CustomerDidNotArrive'
    | 'OrderCreated';

export type AppointmentsStatusState = {
    columns: string[];
    serviceAdvisorType: string;
    appointments: AppointmentDto[];
    activeAppointments: AppointmentDto[];
    appointmentInterval: number;
};

export const DEFAULT_SHOWN_COLUMNS = {
    APPOINTMENT_TIME: 'appointments_status_table_ui:appointment_time',
    SERVICE_ADVISOR: 'appointments_status_table_ui:service_advisor',
    CUSTOMER: 'appointments_status_table_ui:customer',
    MODEL: 'appointments_status_table_ui:model',
    PLATES: 'appointments_status_table_ui:plates',
    STATUS: 'appointments_status_table_ui:status',
};

export const SERVICE_ADVISOR_TYPES = {
    FULLNAME: 'full_name',
    INITIALS: 'initials',
};

export const DEFAULT_SHOWN_COLUMNS_LIST = Object.values(DEFAULT_SHOWN_COLUMNS);

const initialState: AppointmentsStatusState = {
    columns: [],
    serviceAdvisorType: '',
    appointments: [],
    activeAppointments: [],
    appointmentInterval: 0,
};

const appointmentsSlices = createSlice({
    name: 'appointmentsStatusState',
    initialState,
    reducers: {
        setAllAppointments: (state, { payload }: PayloadAction<AppointmentDto[]>) => {
            state.appointments = payload || [];
            state.activeAppointments = payload || [];
            state.appointmentInterval = 0;
        },
        setAppointments: (state, { payload }: PayloadAction<AppointmentDto[]>) => {
            state.appointments = payload || [];
            state.appointmentInterval = 0;
        },
        setNextActiveAppointments: (state, { payload }: PayloadAction<AppointmentDto[]>) => {
            let newAppointmentInterval = state.appointmentInterval + 1;
            const activeAppointments = payload.slice(
                newAppointmentInterval * 10 - 10,
                newAppointmentInterval * 10
            );
            if (payload[newAppointmentInterval * 10] == undefined) newAppointmentInterval = 0;

            state.activeAppointments = activeAppointments;
            state.appointmentInterval = newAppointmentInterval;
        },
        setAppointmentsStatusColumns: (state, { payload }: PayloadAction<string[]>) => {
            state.columns = payload || [];
        },
    },

    extraReducers(builder) {
        builder.addCase(fetchAppointmentsStatusUiStateThunk.fulfilled, (state, action) => {
            if (action.payload === null) {
                state.columns = [...Object.values(DEFAULT_SHOWN_COLUMNS)];
                state.serviceAdvisorType = 'full_name';
            } else {
                state.columns = action.payload.columns;
                state.serviceAdvisorType = action.payload.serviceAdvisorType;
            }
        });
        builder.addCase(toggleServiceAdvisorTypeThunk.pending, (state, action) => {
            const { key } = action.meta.arg;
            state.serviceAdvisorType = key;
        });

        builder.addCase(toggleDisplayedStatusFieldThunk.pending, (state, action) => {
            const { key, shown } = action.meta.arg;
            const idx = state.columns.indexOf(key);

            if (shown) {
                if (idx === -1) state.columns.push(key);
            } else {
                if (idx !== -1) state.columns.splice(idx, 1);
            }
        });
    },
});

export const appointmentsStatusReducer = persistReducer(
    {
        key: 'appointmentsStatus',
        storage,
        whitelist: <(keyof AppointmentsStatusState)[]>['columns'],
    },
    appointmentsSlices.reducer
);

export const appointmentsActions = appointmentsSlices.actions;
