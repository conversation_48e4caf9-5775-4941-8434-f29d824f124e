import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchAppointmentAttachments } from './thunks';
import { AppointmentAttachmentsDto } from '../../../api/appointments';

type AppointmentAttachmentsState = {
    appointmentAttachments: (File | AppointmentAttachmentsDto)[];
};

const initialState: AppointmentAttachmentsState = {
    appointmentAttachments: [],
};

const slice = createSlice({
    name: 'appointmentFiles',
    initialState: initialState,
    reducers: {
        addAppointmentAttachment(state, action: PayloadAction<File>) {
            state.appointmentAttachments.push(action.payload);
        },
        removeAppointmentAttachment(state, action: PayloadAction<number>) {
            state.appointmentAttachments.splice(action.payload, 1);
        },
        clearAppointmentAttachments(state) {
            state.appointmentAttachments = [];
        },
    },
    extraReducers: (builder) => {
        builder.addCase(fetchAppointmentAttachments.fulfilled, (state, { payload }) => {
            state.appointmentAttachments = payload.appointmentAttachmentUrls;
        });
    },
});

export const {
    addAppointmentAttachment,
    removeAppointmentAttachment,
    clearAppointmentAttachments,
} = slice.actions;

const appointmentAttachmentsReducer = slice.reducer;
export default appointmentAttachmentsReducer;
