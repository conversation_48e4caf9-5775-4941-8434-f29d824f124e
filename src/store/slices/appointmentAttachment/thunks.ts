import { createAsyncThunk } from '@reduxjs/toolkit';
import AppointmentsApi from '../../../api/appointments';

type DownloadRequest = {
    appointmentId: string;
};

export const fetchAppointmentAttachments = createAsyncThunk(
    'appointmentFiles/fetchAppointmentAttachments',
    async (payload: DownloadRequest) => {
        const { appointmentId } = payload;
        const appointmentAttachmentUrls = await AppointmentsApi.getAppointmentAttachments(
            appointmentId
        );

        return { appointmentAttachmentUrls };
    }
);
