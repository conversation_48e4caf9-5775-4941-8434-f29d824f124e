import { createSelector } from 'reselect';
import { RootState } from 'store';

const selectSlice = (r: RootState) => r.customReports;

export const selectPrimaryColumn = createSelector(selectSlice, (s) => s.primaryColumn);
export const selectSelectedColumns = createSelector(selectSlice, (s) => s.selectedColumns);
export const selectSelectedReport = createSelector(selectSlice, (s) => s.selectedReport);
export const selectFrom = createSelector(selectSlice, (s) => s.from);
export const selectTo = createSelector(selectSlice, (s) => s.to);
export const selectCustomColumnsCollapseState = createSelector(
    selectSlice,
    (s) => s.customColumnsCollapseState
);
export const selectPrimaryColumnsExpanded = createSelector(
    selectSlice,
    (s) => s.primaryColumnsExpanded
);
export const selectPreviewGridData = createSelector(selectSlice, (s) => s.previewGridData);
