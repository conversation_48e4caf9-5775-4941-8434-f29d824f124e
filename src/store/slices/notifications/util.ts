import { RealTimeNotificationDto, RealTimeNotificationType } from 'api/notifications';
import { CmosNotification } from '.';

export function convertApiNotificationsToState(
    notifications: RealTimeNotificationDto[]
): CmosNotification[] {
    // SANITY CHECK: in case if get a string response from the service which is possible is some very weird cases
    if (!(notifications instanceof Array))
        throw new Error('notifications parameters must be an array');

    const result: CmosNotification[] = [];

    for (const notification of notifications) {
        try {
            const cmosNotification = convertToCmosNotification(notification);
            result.push(cmosNotification);
        } catch (err) {
            console.error('failed to convert to CmosNotification type', notification, err);
            result.push({
                type: '__invalid',
                title: 'INVALID NOTIFICATION',
                body: 'This notification is invalid, please check console',
                id: notification.id,
                data: null,
                createdAt: notification.sentAt,
                read: notification.isRead,
            });
        }
    }

    return result;
}

type Validator = (value: any) => string | undefined;

const objectValidator: Validator = (v) => {
    if (typeof v !== 'object') return 'value must be an object';
    if (v === null) return 'value must not be null';
};

const emptyValidator: Validator = (v) => {
    if (v) return 'use correct validator for passed value';
};

const orderIdValidator: Validator = (v) => {
    const error = objectValidator(v);
    if (error) return error;
    if (typeof v.orderId !== 'number') return 'must have a property orderId of type number';
    if (typeof v.orderNumber !== 'string') return 'must have a property orderNumber of type string';
};

const appointmentIdValidator: Validator = (v) => {
    const error = objectValidator(v);
    if (error) return error;
    if (typeof v.appointmentId !== 'string')
        return 'must have a property appointmentId of type string';
};

const VALIDATORS: Record<RealTimeNotificationType, Validator> = {
    AppointmentAssigned: appointmentIdValidator,
    OrderCreated: orderIdValidator,
    InspectionItemApproved: orderIdValidator,
    InspectionItemDeclined: orderIdValidator,
    OrderPhaseModified: orderIdValidator,
    OrderAssigned: orderIdValidator,
    SurveyWithNegativeRating: orderIdValidator,
    PriorityLevelCount: orderIdValidator,
    PriorityLevelPercentage: orderIdValidator,
    InspectionItem: orderIdValidator,
    InspectionNotSent: orderIdValidator,
    NoInternalViews: orderIdValidator,
    HighMileageVehicle: orderIdValidator,
    ConversationQuotaLimitExceeded: emptyValidator,
    NewRedItem: orderIdValidator,
    PhaseSetback: orderIdValidator,
    PaymentReceived: orderIdValidator,
    JobAssigned: emptyValidator,
};

function convertToCmosNotification(notification: RealTimeNotificationDto): CmosNotification {
    const data = notification.args ? JSON.parse(notification.args) : null;
    const validate = VALIDATORS[notification.type];
    const error = validate(data);
    if (error) {
        throw new Error('invalid JSON schema: ' + error);
    }

    return {
        createdAt: notification.sentAt,
        title: notification.title,
        body: notification.body,
        id: notification.id,
        type: notification.type,
        read: notification.isRead,
        data,
    };
}
