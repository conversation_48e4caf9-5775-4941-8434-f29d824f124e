import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { RealTimeNotificationDto } from 'api/notifications';
import remove from 'lodash/remove';

import { DateTime } from 'luxon';
import fetchUnseenThunk from './thunks/fetchUnseen';
import markAsSeenThunk from './thunks/markAsSeen';
import { convertApiNotificationsToState } from './util';

export * from './selectors';

export { fetchUnseenThunk, markAsSeenThunk };

type OrderData = {
    orderId: number;
    orderNumber: string;
};
type OrderDataKeys =
    | 'OrderCreated'
    | 'InspectionItemApproved'
    | 'InspectionItemDeclined'
    | 'OrderPhaseModified'
    | 'OrderAssigned'
    | 'SurveyWithNegativeRating'
    | 'PriorityLevelCount'
    | 'PriorityLevelPercentage'
    | 'InspectionItem'
    | 'InspectionNotSent'
    | 'NoInternalViews'
    | 'HighMileageVehicle'
    | 'ConversationQuotaLimitExceeded'
    | 'NewRedItem'
    | 'PhaseSetback'
    | 'PaymentReceived'
    | 'JobAssigned';

export type RealTimeNotificationTypes = {
    AppointmentAssigned: {
        appointmentId: string;
    };
    __invalid: null;
} & Record<OrderDataKeys, OrderData>;

export type CmosNotification = ValueOf<{
    [K in keyof RealTimeNotificationTypes]: {
        id: number;
        type: K;
        data: RealTimeNotificationTypes[K];
        createdAt: string;
        title: string;
        body: string;
        read: boolean;
    };
}>; // TODO: other types

type State = {
    notifications: CmosNotification[];
    lastFetch: number;
    hasUnseen: boolean;
};

const initialState: State = {
    notifications: [],
    lastFetch: 0,
    hasUnseen: false,
};

const slice = createSlice({
    name: 'notifications',
    initialState,
    reducers: {
        markAsRead: (state, action: PayloadAction<number>) => {
            const notification = state.notifications.find((x) => x.id === action.payload);
            if (notification) notification.read = true;
        },

        ensureExists: (state, action: PayloadAction<RealTimeNotificationDto[]>) => {
            const cmosNotifications = convertApiNotificationsToState(action.payload);
            addNotifications(state.notifications, cmosNotifications);
        },
    },
    extraReducers: (builder) => {
        builder.addCase(markAsSeenThunk.pending, (state) => {
            state.hasUnseen = false;
        });

        builder.addCase(fetchUnseenThunk.fulfilled, (state, { payload }) => {
            state.hasUnseen = payload;
        });
    },
});

const reducer = slice.reducer;

export const notificationsActions = slice.actions;

export default reducer;

function addNotifications(notifications: CmosNotification[], newNotifications: CmosNotification[]) {
    if (newNotifications.length === 0) return notifications;
    remove(notifications, (x) => newNotifications.find((n) => n.id === x.id));
    notifications.push(...newNotifications);
    notifications.sort(
        (a, b) =>
            DateTime.fromISO(a.createdAt).toMillis() - DateTime.fromISO(b.createdAt).toMillis()
    );
}
