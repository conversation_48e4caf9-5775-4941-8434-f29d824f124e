import { createAsyncThunk } from '@reduxjs/toolkit';
import AccountApi from 'api/account';
import { RootState } from 'store';
import { OrdersStatusState } from '.';
import { selectDisplayableOrderStatusColumns } from './selectors';

export const fetchOrdersStatusUiStateThunk = createAsyncThunk(
    'orders/fetchOrdersStatusUiState',
    async () => {
        const uiState = await AccountApi.getValue('orders_status_ui_state');

        return getUiStateOrNull(uiState);
    }
);

export const toggleDisplayedStatusFieldThunk = createAsyncThunk(
    'orders/toggleOrdersStatusDisplayedField',
    async ({ key, shown }: { key: string; shown: boolean }, { getState }) => {
        const displayedColumns = [...selectDisplayableOrderStatusColumns(getState() as RootState)];

        await AccountApi.setValue(
            'orders_status_ui_state',
            JSON.stringify({ columns: displayedColumns })
        );
    }
);

function getUiStateOrNull(payload: string): OrdersStatusState | null {
    let parsed: unknown;

    try {
        parsed = JSON.parse(payload);
    } catch {
        return null;
    }

    if (isValidState(parsed)) {
        return parsed;
    }
    return null;
}

function isValidState(parsed: unknown): parsed is OrdersStatusState {
    if (parsed === null || typeof parsed !== 'object') return false;

    const obj = parsed as object;
    if (!Object.hasOwn(obj, key('columns'))) {
        return false;
    }

    const arr = (obj as { columns: unknown }).columns;
    if (!(arr instanceof Array)) return false;
    return arr.every((x) => typeof x === 'string');
}

function key(k: keyof OrdersStatusState): string {
    return k;
}
