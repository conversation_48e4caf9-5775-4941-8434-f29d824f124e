import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { OrderDto } from 'api/status';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { fetchOrdersStatusUiStateThunk, toggleDisplayedStatusFieldThunk } from './thunks';

export type OrdersStatusState = {
    columns: string[];
    orders: OrderDto[];
    activeOrders: OrderDto[];
    orderInterval: number;
};

export const DEFAULT_SHOWN_COLUMNS = {
    DELIVERY_TIME: 'orders_status_table_ui:delivery_time',
    TOWER: 'orders_status_table_ui:tower',
    ORDER_NUMBER: 'orders_status_table_ui:order_number',
    CUSTOMER: 'orders_status_table_ui:customer',
    MODEL: 'orders_status_table_ui:model',
    PLATES: 'orders_status_table_ui:plates',
    PHASE: 'orders_status_table_ui:phase',
};

export const DEFAULT_SHOWN_COLUMNS_LIST = Object.values(DEFAULT_SHOWN_COLUMNS);

const initialState: OrdersStatusState = {
    columns: [],
    orders: [],
    activeOrders: [],
    orderInterval: 0,
};

const ordersSlices = createSlice({
    name: 'ordersStatusState',
    initialState,
    reducers: {
        setAllOrders: (state, { payload }: PayloadAction<OrderDto[]>) => {
            state.orders = payload || [];
            state.activeOrders = payload || [];
            state.orderInterval = 0;
        },
        setOrders: (state, { payload }: PayloadAction<OrderDto[]>) => {
            state.orders = payload || [];
            state.orderInterval = 0;
        },
        setNextActiveOrders: (state, { payload }: PayloadAction<OrderDto[]>) => {
            let newOrderInterval = state.orderInterval + 1;
            const activeOrders = payload.slice(newOrderInterval * 10 - 10, newOrderInterval * 10);
            if (payload[newOrderInterval * 10] == undefined) newOrderInterval = 0;

            state.activeOrders = activeOrders;
            state.orderInterval = newOrderInterval;
        },
        setOrderStatusColumns: (state, { payload }: PayloadAction<string[]>) => {
            state.columns = payload || [];
        },
    },

    extraReducers(builder) {
        builder.addCase(fetchOrdersStatusUiStateThunk.fulfilled, (state, action) => {
            if (action.payload === null) {
                state.columns = [...Object.values(DEFAULT_SHOWN_COLUMNS)];
            } else {
                state.columns = action.payload.columns;
            }
        });
        builder.addCase(toggleDisplayedStatusFieldThunk.pending, (state, action) => {
            const { key, shown } = action.meta.arg;
            const idx = state.columns.indexOf(key);

            if (shown) {
                if (idx === -1) state.columns.push(key);
            } else {
                if (idx !== -1) state.columns.splice(idx, 1);
            }
        });
    },
});

export const ordersStatusReducer = persistReducer(
    {
        key: 'ordersStatus',
        storage,
        whitelist: ['columns'] as (keyof OrdersStatusState)[],
    },
    ordersSlices.reducer
);

export const ordersActions = ordersSlices.actions;
