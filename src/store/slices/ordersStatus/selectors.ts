import { createSelector } from 'reselect';
import { RootState } from 'store';
import { DEFAULT_SHOWN_COLUMNS } from '.';

const selectSlice = (state: RootState) => state.ordersStatus;

export const selectOrders = createSelector(selectSlice, (ordersStatus) => ordersStatus.orders);

export const selectActiveOrders = createSelector(
    selectSlice,
    (ordersStatus) => ordersStatus.activeOrders
);

export const selectDisplayableOrderStatusColumns = createSelector(selectSlice, (s) => {
    const defaultColumns = Object.values(DEFAULT_SHOWN_COLUMNS);
    const displayableOrderStatusColumns = defaultColumns.filter((x) => s.columns.includes(x));
    return displayableOrderStatusColumns;
});
