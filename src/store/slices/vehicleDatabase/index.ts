import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { VehicleDatabasePageParams, VehiclesPageParams } from 'api/customers';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { VehiclesColumnKey } from '../vehicles';

export * from './selectors';

type VehicleDatabaseState = {
    filters: Omit<VehicleDatabasePageParams, 'pageIdx'>;
    selectedColumns: Record<string, boolean>;
    /**
     * List of filters that are active i.e. have something selected in them.
     */
    activeFilters: Record<string, boolean>;
};

export const vehicleDatabaseFilterKeyMap: Partial<Record<string, keyof VehicleDatabasePageParams>> =
    {
        usageType: 'usageType',
        assignedBdcAdvisor: 'bdcAdvisor',
        inPma: 'inPma',
        isFirstOwner: 'isFirstOwner',
        year: 'year',
        vinStatus: 'vinStatus',
        prospectionPriority: 'prospectionPriority',
        brand: 'brand',
        model: 'model',
        customerStatus: 'customerStatus',
        lastActivity: 'lastActivity',
        recommendedServiceFrequency: 'recommendedFrequency',
        customerType: 'customerType',
    };

export const vehiclesFilterKeyMap: Partial<Record<VehiclesColumnKey, keyof VehiclesPageParams>> = {
    bdcAdvisor: 'bdcAdvisor',
    prospectionPriority: 'prospectionPriority',
};

export type ColumnGroup = 'Retention' | 'FollowUp' | 'GeographicLocation';

export type VehicleDatabaseColumn = {
    key: string;
    selectedByDefault: boolean;
    isFiltered: boolean;
    dataType:
        | 'boolean'
        | 'string'
        | 'prospectionPriority'
        | 'recommendedServiceFrequency'
        | 'date'
        | 'vinStatus';
    columnGroup: ColumnGroup;
    isAvailableWhenCrmDisabled?: true;
    width?: number;
};

export const DISPLAYED_COLUMNS: VehicleDatabaseColumn[] = [
    {
        key: 'vin',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        width: 170,
        isAvailableWhenCrmDisabled: true,
        selectedByDefault: true,
    },
    {
        key: 'brand',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'Retention',
        isAvailableWhenCrmDisabled: true,
        selectedByDefault: true,
    },
    {
        key: 'model',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'Retention',
        isAvailableWhenCrmDisabled: true,
        selectedByDefault: true,
    },
    {
        key: 'year',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'Retention',
        isAvailableWhenCrmDisabled: true,
        selectedByDefault: true,
    },
    {
        key: 'plates',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        isAvailableWhenCrmDisabled: true,
        selectedByDefault: true,
    },
    {
        key: 'vinStatus',
        isFiltered: true,
        dataType: 'vinStatus',
        columnGroup: 'Retention',
        selectedByDefault: true,
    },
    {
        key: 'prospectionPriority',
        isFiltered: true,
        dataType: 'prospectionPriority',
        columnGroup: 'Retention',
        selectedByDefault: true,
    },
    {
        key: 'retentionBand',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        selectedByDefault: true,
    },
    {
        key: 'soldByDealer',
        isFiltered: false,
        dataType: 'boolean',
        columnGroup: 'Retention',
        selectedByDefault: true,
    },
    {
        key: 'bacThatSold',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        selectedByDefault: true,
    },
    {
        key: 'inPma',
        isFiltered: true,
        dataType: 'boolean',
        columnGroup: 'Retention',
        selectedByDefault: true,
    },
    {
        key: 'saleDate',
        isFiltered: false,
        dataType: 'date',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'monthsFromSaleDate',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'lastServiceDate',
        isFiltered: false,
        dataType: 'date',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'monthsSinceLastService',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'lastServiceDateWithMe',
        isFiltered: false,
        dataType: 'date',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'monthsSinceLastServiceWithMe',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'lastServiceDateWithAnother',
        isFiltered: false,
        dataType: 'date',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'monthsSinceLastServiceWithAnother',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'Retention',
        selectedByDefault: false,
    },
    {
        key: 'customerType',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'usageType',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'mileage',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'FollowUp',
        isAvailableWhenCrmDisabled: true,
        selectedByDefault: true,
    },
    {
        key: 'customerStatus',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'lastActivity',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'lastActivityDate',
        isFiltered: false,
        dataType: 'date',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'monthsDaysSinceLastActivity',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'dateOfNextActivity',
        isFiltered: false,
        dataType: 'date',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'recommendedServiceFrequency',
        isFiltered: true,
        dataType: 'recommendedServiceFrequency',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'assignedBdcAdvisor',
        isFiltered: true,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'pendingCampaigns',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'FollowUp',
        selectedByDefault: false,
    },
    {
        key: 'isFirstOwner',
        isFiltered: true,
        dataType: 'boolean',
        columnGroup: 'GeographicLocation',
        selectedByDefault: false,
    },
    {
        key: 'neighborhood',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'GeographicLocation',
        selectedByDefault: false,
    },
    {
        key: 'districtOrMunicipality',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'GeographicLocation',
        selectedByDefault: false,
    },
    {
        key: 'zipCode',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'GeographicLocation',
        selectedByDefault: false,
    },
    {
        key: 'city',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'GeographicLocation',
        selectedByDefault: false,
    },
    {
        key: 'state',
        isFiltered: false,
        dataType: 'string',
        columnGroup: 'GeographicLocation',
        selectedByDefault: false,
    },
];

const initialState: VehicleDatabaseState = {
    filters: {
        brand: [],
        model: [],
        year: [],
        vinStatus: [],
        inPma: [],
        customerType: [],
        customerStatus: [],
        lastActivity: [],
        recommendedFrequency: [],
        bdcAdvisor: [],
        prospectionPriority: [],
        isFirstOwner: [],
        usageType: [],
        ids: [],
        autocompleteSearchValue: null,
    },
    activeFilters: {},
    selectedColumns: Object.fromEntries(
        DISPLAYED_COLUMNS.filter((x) => x.selectedByDefault).map((x) => [
            x.key,
            x.selectedByDefault,
        ])
    ),
};

const vehicleDatabaseSlices = createSlice({
    name: 'vehicleDatabase',
    initialState,
    reducers: {
        setFilters: (
            state,
            { payload }: PayloadAction<Omit<VehicleDatabasePageParams, 'pageIdx'>>
        ) => {
            state.filters = payload;
        },

        clearFiltersExcept: (
            state,
            { payload }: PayloadAction<keyof Omit<VehicleDatabasePageParams, 'pageIdx'>>
        ) => {
            const preservedValue = state.filters[payload];
            const preservedIds = state.filters.ids;

            state.filters = {
                ...initialState.filters,
                [payload]: preservedValue,
                ids: preservedIds,
            };
        },

        toggleColumnSelection: (state, { payload }: PayloadAction<string>) => {
            state.selectedColumns[payload] = !(state.selectedColumns[payload] ?? false);
        },

        setFilterIsActive: (
            state,
            { payload }: PayloadAction<{ key: string; active: boolean }>
        ) => {
            state.activeFilters[payload.key] = payload.active;
        },

        clearFiltersWhenCrmIsDisabled: (state) => {
            state.filters = {
                brand: state.filters.brand,
                model: state.filters.model,
                year: state.filters.year,
                vinStatus: [],
                inPma: [],
                customerType: [],
                customerStatus: [],
                lastActivity: [],
                recommendedFrequency: [],
                bdcAdvisor: [],
                prospectionPriority: [],
                isFirstOwner: [],
                usageType: [],
                ids: [],
                autocompleteSearchValue: null,
            };
        },
    },
});

export const vehicleDatabaseReducer = persistReducer(
    {
        key: 'vehicleDatabase',
        storage,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        whitelist: <(keyof VehicleDatabaseState)[]>['filters', 'selectedColumns', 'activeFilters'],
        version: 1,
    },
    vehicleDatabaseSlices.reducer
);

export const vehicleDatabaseActions = vehicleDatabaseSlices.actions;
