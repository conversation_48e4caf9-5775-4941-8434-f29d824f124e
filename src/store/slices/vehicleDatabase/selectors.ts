import { createSelector } from 'reselect';
import { RootState } from 'store';
import { DISPLAYED_COLUMNS } from '.';
import { selectSettings } from '../globalSettingsSlice';

const selectSlice = (state: RootState) => state.vehicleDatabase;

export const selectVehicleDatabaseFilters = createSelector(selectSlice, (s) => s.filters);

export const selectAvailableVehicleDatabaseColumns = createSelector(
    [selectSlice, selectSettings],
    (s, settings) => {
        const columns = DISPLAYED_COLUMNS.map((column) => ({
            ...column,
            selected: s.selectedColumns[column.key] ?? false,
        }));

        const crmEnabled = settings.repairShopSettings?.features.enableAftersalesCrm ?? false;

        return crmEnabled
            ? columns
            : columns.filter((column) => column.isAvailableWhenCrmDisabled === true);
    }
);

export const selectSelectedVehicleDatabaseColumns = createSelector(
    [selectSlice, selectSettings],
    (s, settings) => {
        const columns = DISPLAYED_COLUMNS.filter(
            (column) => s.selectedColumns[column.key] ?? false
        );

        if (columns.length === 0) return DISPLAYED_COLUMNS;

        const crmEnabled = settings.repairShopSettings?.features.enableAftersalesCrm ?? false;

        return crmEnabled
            ? columns
            : columns.filter((column) => column.isAvailableWhenCrmDisabled === true);
    }
);

export const selectActiveFilters = createSelector(selectSlice, (s) => s.activeFilters);
