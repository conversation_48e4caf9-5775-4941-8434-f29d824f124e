import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PhaseSetbackRulesDto } from 'api/phaseSetback';
import ensurePhaseSetbackRulesFetched from './thunks/ensurePhaseSetbackRulesFetched';

type PhaseSetbackState = {
    rules: PhaseSetbackRulesDto[];
};

const initialState: PhaseSetbackState = {
    rules: [],
};

const phaseSetbackSlice = createSlice({
    name: 'phaseSetback',
    initialState,
    reducers: {
        reset: (
            state: PhaseSetbackState,
            { payload }: PayloadAction<PhaseSetbackRulesDto | null>
        ) => {
            state.rules = payload ? [payload] : [];
        },
        setEnabled: (
            state: PhaseSetbackState,
            { payload }: PayloadAction<{ repairShopKey: string; enabled: boolean }>
        ) => {
            const rulesIndexToUpdate = state.rules.findIndex(
                (x) => x.repairShopKey === payload.repairShopKey
            );
            if (rulesIndexToUpdate >= 0) {
                state.rules[rulesIndexToUpdate] = {
                    ...state.rules[rulesIndexToUpdate],
                    enabled: payload.enabled,
                };
            }
        },
    },
    extraReducers(builder) {
        builder.addCase(ensurePhaseSetbackRulesFetched.fulfilled, (state, { payload }) => {
            state.rules = payload.rules;
        });
    },
});

export default phaseSetbackSlice;
