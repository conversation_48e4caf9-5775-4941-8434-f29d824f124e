import { createAsyncThunk } from '@reduxjs/toolkit';
import EnterprisePhaseSetbackApi from 'api/enterprise/phaseSetback';
import PhaseSetbackApi from 'api/phaseSetback';

const ensurePhaseSetbackRulesFetched = createAsyncThunk(
    'phaseSetback/ensurePhaseSetbackRulesFetched',
    async (arg: { repairShopKey: string; enterpriseId: number; isEnterprise: boolean }) => {
        const rules = arg.isEnterprise
            ? await EnterprisePhaseSetbackApi.getRules(arg.enterpriseId)
            : [await PhaseSetbackApi.getRules(arg.repairShopKey)];

        return { rules };
    }
);

export default ensurePhaseSetbackRulesFetched;
