import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { combineReducers } from 'redux';
import { persistStore } from 'redux-persist';
import ProspectionsReducer from './reducers/prospections.reducer';
import ToastersReducer from './reducers/toasters.reducer';
import { PhaseNotificationTemplatesReducer } from './slices/Settings/PhaseNotificationTemplate';
import { planningsSettingsReducer } from './slices/Settings/WorkshopPlanner/plannings';
import packagesSettingsReducer from './slices/Settings/packages';
import persistentTeamMemberSettingsFilterReducer from './slices/Settings/teamMembers';
import absencesReducer from './slices/absences';
import appointmentAttachmentsReducer from './slices/appointmentAttachment';
import appointmentReasonsReducer from './slices/appointmentReasons';
import appointmentsReducer from './slices/appointments';
import { appointmentsStatusReducer } from './slices/appointmentsStatus';
import conversationNotificationsReducer from './slices/chatButton';

import clientsSlice from './slices/clientsSlice';
import colorFieldsReducer from './slices/colorFields';
import { conversationsReducer } from './slices/conversations';
import { crmVehiclesImportReducer } from './slices/crmVehiclesImport';
import customReportReducer from './slices/customReport';
import downloaderReducer from './slices/downloader';
import enterpriseReducer from './slices/enterprise';
import persistentEnterpriseTeamMemberSettingsFilterReducer from './slices/enterprise/Settings/teamMembers';
import reportsReducer from './slices/enterprise/reports';
import enterpriseUsersReducer from './slices/enterprise/users';
import { globalSettingsPersistentReducer } from './slices/globalSettingsSlice';
import indicatorsReducer from './slices/indicators';
import leavePageConfirmationReducer from './slices/leavePageConfirmation';
import notificationsReducer from './slices/notifications';
import orderSlice from './slices/order/orderDetails';
import orderInspectionFormsSlice from './slices/order/orderInspectionFormsSlice';
import orderSpreadsheetSlice from './slices/order/spreadsheet';
import orderTypesReducer from './slices/orderTypes';
import { ordersReducer } from './slices/orders';
import { ordersJobsReducer } from './slices/ordersJobs';
import { ordersStatusReducer } from './slices/ordersStatus';
import phaseSetbackSlice from './slices/phaseSetback';
import { teamMembersReducer } from './slices/teamMembers';
import { terminologiesReducer } from './slices/terminologies';
import { uiReducer } from './slices/ui';
import { userReducer } from './slices/user';
import usersReducer from './slices/users';
import { vehicleDatabaseReducer } from './slices/vehicleDatabase';
import vehicleDetailsSliceReducer from './slices/vehicleDetails';
import { vehiclesReducer } from './slices/vehicles';
import { viewByCostReducer } from './slices/viewByCost';
import wpReducer from './slices/wp';

const rootReducer = combineReducers({
    absences: absencesReducer,
    appointmentAttachments: appointmentAttachmentsReducer,
    appointmentReasons: appointmentReasonsReducer,
    appointments: appointmentsReducer,
    appointmentsStatus: appointmentsStatusReducer,
    clients: clientsSlice.reducer,
    colorFields: colorFieldsReducer,
    conversations: conversationsReducer,
    conversationsNotifications: conversationNotificationsReducer,
    crmVehiclesImport: crmVehiclesImportReducer,
    customReports: customReportReducer,
    downloader: downloaderReducer,
    enterprise: enterpriseReducer,
    enterpriseTeamMemberSettingsFilter: persistentEnterpriseTeamMemberSettingsFilterReducer,
    enterpriseUsers: enterpriseUsersReducer,
    globalSettings: globalSettingsPersistentReducer,
    indicators: indicatorsReducer,
    leavePageConfirmation: leavePageConfirmationReducer,
    notifications: notificationsReducer,
    order: orderSlice.reducer,
    orderInspectionForms: orderInspectionFormsSlice.reducer,
    orders: ordersReducer,
    ordersJobs: ordersJobsReducer,
    orderSpreadsheet: orderSpreadsheetSlice.reducer,
    ordersStatus: ordersStatusReducer,
    orderTypes: orderTypesReducer,
    packagesSettings: packagesSettingsReducer,
    phaseNotificationTemplates: PhaseNotificationTemplatesReducer,
    phaseSetback: phaseSetbackSlice.reducer,
    planningsSettings: planningsSettingsReducer,
    prospections: ProspectionsReducer,
    reports: reportsReducer,
    teamMembers: teamMembersReducer,
    teamMemberSettingsFilters: persistentTeamMemberSettingsFilterReducer,
    terminologies: terminologiesReducer,
    toasters: ToastersReducer,
    ui: uiReducer,
    user: userReducer,
    users: usersReducer,
    vehicleDatabase: vehicleDatabaseReducer,
    vehicleDetails: vehicleDetailsSliceReducer,
    vehicles: vehiclesReducer,
    viewByCost: viewByCostReducer,
    wp: wpReducer,
});

const store = configureStore({
    reducer: rootReducer,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;

export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export * from './types';
export default store;
