import store from 'store';
import { selectUser } from 'store/slices/user';
import API from '../api/Authentication';

export class SessionLogic {
    /**
     * @deprecated
     */
    public static getUserId(): string | null {
        return selectUser(store.getState())?.key || null;
    }

    /**
     * @deprecated
     */
    public static getUserRole(): string | null {
        return selectUser(store.getState())?.role || null;
    }

    /**
     * @deprecated
     */
    public static getUserJobTitle(): string | null {
        return selectUser(store.getState())?.jobTitle || null;
    }

    public static async requestPasswordReset(email: string, captcha: string) {
        return API.requestPasswordReset(email, captcha);
    }

    public static async resetPassword(
        token: string,
        password: string,
        confirmPassword: string,
        captcha: string
    ) {
        return API.resetPassword(token, password, confirmPassword, captcha);
    }
}
