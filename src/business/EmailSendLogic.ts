import API from '../api/Email';

export class EmailSendLogic {
    public static async send(
        subdomain: string,
        isEnterprise: boolean,
        userDisplayName: string | null,
        from: string,
        text: string,
        isInactiveAccountEmail: boolean
    ) {
        return await API.send(
            isEnterprise ? null : subdomain,
            isEnterprise ? subdomain : null,
            userDisplayName,
            from,
            text,
            isInactiveAccountEmail
        );
    }
}
