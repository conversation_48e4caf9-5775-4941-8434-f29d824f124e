import {
    NumberAmountFormatOptions,
    numberAmountFormatter,
    numberPhoneFormatter,
} from '../common/FormatersHelper';
import { InternationalizationDto } from '../datacontracts/Interationalization/InternationalizationDto';

export class InternationalizationLogic {
    public static maxLengthPhone(settings: InternationalizationDto) {
        return settings.phoneNumberFormat.split('9').length - 1;
    }
    public static minLengthPhone(settings: InternationalizationDto) {
        return settings.phoneNumberFormat.replace(/(9\?)/g, '').split('9').length - 1;
    }

    public static numberToPhone(
        settings: InternationalizationDto,
        phoneNumber: string | null,
        phoneNumberFormat: string = ''
    ) {
        if (phoneNumber == null) return '-';
        if (!phoneNumberFormat) {
            const internationalization: InternationalizationDto = settings;
            phoneNumberFormat = internationalization.phoneNumberFormat;
        }
        return numberPhoneFormatter(phoneNumberFormat, phoneNumber);
    }

    public static numberToCurrency(
        settings: InternationalizationDto,
        amount?: number | null,
        options?: Partial<NumberAmountFormatOptions>
    ) {
        const formattedAmount = InternationalizationLogic.numberFormat(settings, amount, options);
        return settings.currency.replace('{0}', formattedAmount);
    }

    public static numberFormat(
        settings: InternationalizationDto,
        amount?: number | null,
        options?: Partial<NumberAmountFormatOptions>
    ) {
        return numberAmountFormatter(
            amount,
            settings.numberGroupSeparator,
            settings.numberDecimalSeparator,
            options
        );
    }
}
