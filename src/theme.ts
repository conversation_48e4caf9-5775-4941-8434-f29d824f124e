import { Theme, getLuminance, inputBaseClasses, outlinedInputClasses } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { makeStyles } from '@mui/styles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { isDark } from 'utils/colors';
import { Colors } from './common/styles/Colors';

declare module '@mui/styles/defaultTheme' {
    // eslint-disable-next-line @typescript-eslint/no-empty-interface
    interface DefaultTheme extends Theme {}
}

const HEADER_STYLES = {
    h1: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '34px',
        lineHeight: '41px',
    },
    h2: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '24px',
        lineHeight: '29px',
    },
    h3: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '21px',
        lineHeight: '25px',
    },
    h4: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '18px',
        lineHeight: '22px',
    },
    h5: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '14px',
        lineHeight: '17px',
    },
    h6: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '12px',
        lineHeight: '15px',
    },

    h1Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '34px',
        lineHeight: '41px',
    },
    h1Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '34px',
        lineHeight: '41px',
    },
    h2Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '24px',
        lineHeight: '29px',
    },
    h2Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '24px',
        lineHeight: '29px',
    },
    h3Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '21px',
        lineHeight: '25px',
    },
    h3Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '21px',
        lineHeight: '25px',
    },
    h4Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '18px',
        lineHeight: '22px',
    },
    h4Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '18px',
        lineHeight: '22px',
    },
    h5Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '14px',
        lineHeight: '17px',
    },
    h5Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '14px',
        lineHeight: '17px',
    },
    h6Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '12px',
        lineHeight: '15px',
    },
    h6Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: '12px',
        lineHeight: '15px',
    },
    h7Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '11px',
        lineHeight: '13px',
    },
    h7Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '11px',
        lineHeight: '13px',
    },
    h8Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '10px',
        lineHeight: '12px',
    },
    h8Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '10px',
        lineHeight: '12px',
    },
    h9Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '9px',
        lineHeight: '11px',
    },
    h9Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '9px',
        lineHeight: '11px',
    },
    h10Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '8px',
        lineHeight: '10px',
    },
    h19Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '8px',
        lineHeight: '10px',
    },
    h11Inter: {
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '7px',
        lineHeight: '8px',
    },
    h11Roboto: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '7px',
        lineHeight: '8px',
    },
};

// Update the Typography's variant prop options
declare module '@mui/material/Typography' {
    interface TypographyPropsVariantOverrides {
        h1Inter: true;
        h1Roboto: true;
        h2Inter: true;
        h2Roboto: true;
        h3Inter: true;
        h3Roboto: true;
        h4Inter: true;
        h4Roboto: true;
        h5Inter: true;
        h5Roboto: true;
        h6Inter: true;
        h6Roboto: true;
        h7Inter: true;
        h7Roboto: true;
        h8Inter: true;
        h8Roboto: true;
        h9Inter: true;
        h9Roboto: true;
        h10Inter: true;
        h10Roboto: true;
        h11Inter: true;
        h11Roboto: true;
    }
}

declare module '@mui/material/styles' {
    interface TypographyVariants {
        h1Inter: React.CSSProperties;
        h1Roboto: React.CSSProperties;
        h2Inter: React.CSSProperties;
        h2Roboto: React.CSSProperties;
        h3Inter: React.CSSProperties;
        h3Roboto: React.CSSProperties;
        h4Inter: React.CSSProperties;
        h4Roboto: React.CSSProperties;
        h5Inter: React.CSSProperties;
        h5Roboto: React.CSSProperties;
        h6Inter: React.CSSProperties;
        h6Roboto: React.CSSProperties;
        h7Inter: React.CSSProperties;
        h7Roboto: React.CSSProperties;
        h8Inter: React.CSSProperties;
        h8Roboto: React.CSSProperties;
        h9Inter: React.CSSProperties;
        h9Roboto: React.CSSProperties;
        h10Inter: React.CSSProperties;
        h10Roboto: React.CSSProperties;
        h11Inter: React.CSSProperties;
        h11Roboto: React.CSSProperties;
    }

    // allow configuration using `createTheme`
    interface TypographyVariantsOptions {
        h1Inter?: React.CSSProperties;
        h1Roboto?: React.CSSProperties;
        h2Inter?: React.CSSProperties;
        h2Roboto?: React.CSSProperties;
        h3Inter?: React.CSSProperties;
        h3Roboto?: React.CSSProperties;
        h4Inter?: React.CSSProperties;
        h4Roboto?: React.CSSProperties;
        h5Inter?: React.CSSProperties;
        h5Roboto?: React.CSSProperties;
        h6Inter?: React.CSSProperties;
        h6Roboto?: React.CSSProperties;
        h7Inter?: React.CSSProperties;
        h7Roboto?: React.CSSProperties;
        h8Inter?: React.CSSProperties;
        h8Roboto?: React.CSSProperties;
        h9Inter?: React.CSSProperties;
        h9Roboto?: React.CSSProperties;
        h10Inter?: React.CSSProperties;
        h10Roboto?: React.CSSProperties;
        h11Inter?: React.CSSProperties;
        h11Roboto?: React.CSSProperties;
    }

    interface BreakpointOverrides {
        xs: true;
        sm: true;
        md: true;
        lg: true;
        xl: true;
        ['2xl']: true;
        ['3xl']: true;
        ['4xl']: true;
        ['5xl']: true;
    }

    interface Palette {
        custom: { gray: string };
        neutral: {
            1: string;
            2: string;
            3: string;
            4: string;
            5: string;
            6: string;
            7: string;
            8: string;
            9: string;
        } & Palette['primary'];
    }

    interface PaletteOptions {
        custom: { gray: string };
        neutral?: {
            1: string;
            2: string;
            3: string;
            4: string;
            5: string;
            6: string;
            7: string;
            8: string;
            9: string;
        } & PaletteOptions['primary'];
    }
}

let cmosTheme = createTheme({});

const createColor = (color: string) =>
    cmosTheme.palette.augmentColor({
        color: { main: color, contrastText: isDark(color, 0.7) ? '#fff' : '#000' },
    });

cmosTheme = createTheme({
    palette: {
        background: {
            default: '#fafafa',
            paper: '#fff',
        },
        primary: {
            main: Colors.CM1,
            contrastText: Colors.White,
            dark: Colors.CM1_dark,
            light: Colors.CM2,
        },
        error: createColor(Colors.Error),
        warning: createColor(Colors.Warning),
        neutral: {
            light: Colors.Neutral3,
            main: Colors.Neutral3,
            dark: Colors.Neutral4,
            contrastText: Colors.Neutral6,
            1: Colors.White,
            2: Colors.Neutral2,
            3: Colors.Neutral3,
            4: Colors.Neutral4,
            5: Colors.Neutral5,
            6: Colors.Neutral6,
            7: Colors.Neutral7,
            8: Colors.Neutral8,
            9: Colors.Neutral9,
        },
        success: createColor(Colors.Success),
        info: createColor(Colors.CM1),
        custom: { gray: Colors.Grey },
        getContrastText(background) {
            return getLuminance(background) > 0.6 ? '#000' : '#fff';
        },
    },
    typography: {
        ...HEADER_STYLES,
        body1: {
            fontFamily: 'Inter, sans-serif',
            fontStyle: 'normal',
            fontWeight: 'normal',
            fontSize: '12px',
            lineHeight: '15px',
        },
    },

    components: {
        MuiAlert: {
            styleOverrides: {
                root: {
                    boxShadow: 'none',
                },
            },
            defaultProps: {
                variant: 'filled',
            },
        },

        MuiAutocomplete: {
            styleOverrides: {
                root: ({ theme }) => ({
                    [`& .${outlinedInputClasses.notchedOutline}`]: {
                        // borderColor: 'transparent',
                    },

                    [`& .${inputBaseClasses.input}`]: {
                        ...theme.typography.h6Roboto,
                        fontWeight: 'normal',
                    },
                }),
                paper: {
                    backgroundColor: 'var(--neutral1) !important',
                },
                noOptions: {
                    display: 'flex',
                    justifyContent: 'center',
                    color: 'var(--neutral7)',
                },
            },
        },

        MuiMenuItem: {
            styleOverrides: {
                root: ({ theme }) => ({
                    ...theme.typography.h6Roboto,
                    fontWeight: 'normal',
                }),
            },
        },

        MuiListItem: {
            styleOverrides: {
                root: ({ theme }) => ({
                    ...theme.typography.h6Roboto,
                    fontWeight: 'normal',
                }),
            },
        },

        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: 10,
                    boxShadow: '0 0 4px var(--neutral4)',
                },
            },
        },

        MuiCircularProgress: {
            defaultProps: {
                thickness: 2,
            },
            styleOverrides: {
                circle: {
                    strokeLinecap: 'round',
                },
            },
        },
    },
});

const generateVariables = (theme: Theme) => {
    const vars: Record<string, string> = {};

    vars['--neutral1'] = '#fff';
    vars['--neutral2'] = Colors.Neutral2;
    vars['--neutral3'] = Colors.Neutral3;
    vars['--neutral4'] = Colors.Neutral4;
    vars['--neutral5'] = Colors.Neutral5;
    vars['--neutral6'] = Colors.Neutral6;
    vars['--neutral7'] = Colors.Neutral7;
    vars['--neutral8'] = Colors.Neutral8;
    vars['--neutral9'] = Colors.Neutral9;
    vars['--cm1'] = theme.palette.primary.main;
    vars['--cm2'] = Colors.CM2;
    vars['--cm3'] = Colors.CM3;
    vars['--cm4'] = Colors.CM4;
    vars['--cm5'] = Colors.CM5;
    vars['--grey'] = Colors.Grey;
    vars['--grey5'] = Colors.Grey5;
    vars['--greyBlue'] = Colors.GrayBlue;
    vars['--success'] = Colors.Success;
    vars['--success-background'] = Colors.Success_background;

    vars['--yellow'] = Colors.Warning;
    vars['--danger'] = theme.palette.error.main;
    vars['--danger-background'] = Colors.Error_background;

    return vars;
};

export const useGlobalThemeCss = makeStyles((theme) => ({
    '@global': {
        ':root': generateVariables(theme),
        '.custom-scrollbar': scrollbarStyle(),
    },
}));

export default cmosTheme;
