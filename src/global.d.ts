declare type MergeTypes<A, B> = A & Omit<B, keyof A>;
declare type ValueOf<T> = T[keyof T];

// https://stackoverflow.com/questions/41980195/recursive-partialt-in-typescript
declare type RecursivePartial<T> = {
    [P in keyof T]?: T[P] extends (infer U)[]
        ? RecursivePartial<U>[]
        : T[P] extends object | undefined
        ? RecursivePartial<T[P]>
        : T[P];
};

interface ImportMeta {
    VITEST?: 'true';
}

declare const __BUILD_INFO__: {
    ID: string;
    TS: number;
    APP_VERSION: string;
    GIT_COMMIT: string;
};
