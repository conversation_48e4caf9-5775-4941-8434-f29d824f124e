/**
 * License note: original repo does not have LICENSE file but package.json indicates it is Apache 2.0.
 *
 * <AUTHOR> <<EMAIL>>
 * @license MIT AND Apache-2.0
 * @description requestIdleCallback polyfill, borrowed from https://github.com/pladaria/requestidlecallback-polyfill/blob/master/index.js
 */

window.requestIdleCallback =
    window.requestIdleCallback ||
    function (cb) {
        const start = Date.now();
        return setTimeout(function () {
            cb({
                didTimeout: false,
                timeRemaining: function () {
                    return Math.max(0, 50 - (Date.now() - start));
                },
            });
        }, 1);
    };

window.cancelIdleCallback =
    window.cancelIdleCallback ||
    function (id) {
        clearTimeout(id);
    };
