type RGB = [number, number, number];

export const getRelativeLuminance = (rgb: RGB) =>
    0.2126 * (rgb[0] / 255) + 0.7152 * (rgb[1] / 255) + 0.0722 * (rgb[2] / 255);

export function parseRgb(c: string): RGB {
    if (c.startsWith('#')) c = c.substring(1);
    if (c.length === 3) {
        const r = parseInt(c.substring(0, 1), 16),
            g = parseInt(c.substring(1, 2), 16),
            b = parseInt(c.substring(2, 3), 16);
        return [(r << 4) | r, (g << 4) | g, (b << 4) | b];
    } else {
        const r = parseInt(c.substring(0, 2), 16),
            g = parseInt(c.substring(2, 4), 16),
            b = parseInt(c.substring(4, 6), 16);
        return [r, g, b];
    }
}

export function isDark(rgbColor: string, luminanceThreshold: number = 0.5) {
    const rgb = parseRgb(rgbColor);
    const l = getRelativeLuminance(rgb);
    return l < luminanceThreshold;
}

export function isDarkOr(rgbColor: string, fallback: boolean, luminanceThreshold: number = 0.5) {
    try {
        return isDark(rgbColor, luminanceThreshold);
    } catch {
        return fallback;
    }
}

type HSL = {
    h: number;
    s: number;
    l: number;
};

function hslHueNormalize(v: number) {
    while (v < 0) {
        v += 365;
    }
    while (v > 365) {
        v -= 365;
    }
    return v;
}

/**
 * Calculates the distance between two hue values in the HSL color space.
 * @param a - The first hue value (0-365).
 * @param b - The second hue value (0-365).
 * @returns The distance between the two hue values.
 */
export function hslHueDistance(a: number, b: number): number {
    a = hslHueNormalize(a);
    b = hslHueNormalize(b);
    if (a === b) return 0;
    const distance = Math.abs(a - b);
    let sm: number, lg: number;
    if (a < b) {
        sm = a;
        lg = b;
    } else {
        sm = b;
        lg = a;
    }
    const distance2 = 365 - lg + sm;
    return Math.min(distance, distance2);
}

export function toHSL(rgb: RGB): HSL {
    // Make r, g, and b fractions of 1
    let [r, g, b] = rgb;
    r /= 255;
    g /= 255;
    b /= 255;

    // Find greatest and smallest channel values
    const cmin = Math.min(r, g, b),
        cmax = Math.max(r, g, b),
        delta = cmax - cmin;
    let h = 0,
        s = 0,
        l = 0;

    // Calculate hue
    // No difference
    if (delta === 0) h = 0;
    // Red is max
    else if (cmax === r) h = ((g - b) / delta) % 6;
    // Green is max
    else if (cmax === g) h = (b - r) / delta + 2;
    // Blue is max
    else h = (r - g) / delta + 4;

    h = Math.round(h * 60);

    // Make negative hues positive behind 360°
    if (h < 0) h += 360;

    // Calculate lightness
    l = (cmax + cmin) / 2;

    // Calculate saturation
    s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));

    // Multiply l and s by 100
    s = +(s * 100).toFixed(1);
    l = +(l * 100).toFixed(1);

    return { h, s, l };
}

export function hslToString(hsl: HSL): string {
    return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
}

export function blendRGB(a: HSL, b: HSL, blendFactor: number): HSL {
    if (blendFactor < 0) blendFactor = 0;
    else if (blendFactor > 1) blendFactor = 1;

    return {
        h: a.h * (1 - blendFactor) + b.h * blendFactor,
        s: a.s * (1 - blendFactor) + b.s * blendFactor,
        l: a.l * (1 - blendFactor) + b.l * blendFactor,
    };
}
