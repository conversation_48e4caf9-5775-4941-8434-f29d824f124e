// thx GPT-4o for this piece of typescript

type HasRequiredUndefined<T> = {
    [K in keyof T]-?: undefined extends T[K]
        ? {} extends Pick<T, K>
            ? never // optional with undefined — OK
            : K // required with undefined — flag it
        : never;
}[keyof T];

type NoExplicitUndefined<T> = HasRequiredUndefined<T> extends never ? T : never;

/**
 * Takes a plain object and returns its copy without properties set
 * to undefined. Can be useful when you need to remove properties
 * that are undefined so that Object.keys does not return them for example
 *
 * Will cause typescript error if passed object has a type with explicit (i.e. non-optional)
 * undefined props.
 * @param value an object that may contain properties with undefined
 * @returns
 */
export function removeUndefined<T extends object>(value: NoExplicitUndefined<T>): T {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const r = {} as any;

    for (const k in value) {
        const v = value[k];
        if (v !== undefined) {
            r[k] = v;
        }
    }

    return r;
}
