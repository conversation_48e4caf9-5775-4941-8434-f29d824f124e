/**
 * Splits a list of objects into batches of given size (or small)
 * @param batchSize size of the batch, must be 1 or greater
 */
export function batch<T>(list: T[], batchSize: number): T[][] {
    if (batchSize < 1) throw new Error(`batchSize must be 1 or greater, got ${batchSize}`);

    if (batchSize === 1) {
        // special case
        return list.map((x) => [x]);
    }

    let items: T[] = [];
    const batches: T[][] = [];

    for (const item of list) {
        if (items.length >= batchSize) {
            batches.push(items);
            items = [];
        }

        items.push(item);
    }

    if (items.length > 0) batches.push(items);

    return batches;
}

export function notUndefined<T>(value: T | undefined): value is T {
    return value !== undefined;
}
