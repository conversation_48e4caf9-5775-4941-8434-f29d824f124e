import useTrackedState, { ChangeDetector, UseTrackedState } from 'common/hooks/useTrackedState';
import { ComponentType, MutableRefObject, createContext, useContext, useEffect } from 'react';

export type WithTrackedStateProps<State extends object, Props> = Props & {
    trackedState: UseTrackedState<State>;
};

export type WithTrackedStateExternalProps<State extends {}, Props> = Props & {
    trackedStateRef?: MutableRefObject<UseTrackedState<State> | undefined>;
};

export type TrackedStateContextResult<State extends {}> = {
    Provider: ComponentType<React.PropsWithChildren<{}>>;
    useStateHook: () => UseTrackedState<State>;
    withState: <Props extends object>(
        component: ComponentType<WithTrackedStateProps<State, Props>>
    ) => ComponentType<WithTrackedStateExternalProps<State, Props>>;
};

/**
 * Creates new context that stores the tracked state of given type.
 * @param initialState function that returns default value of the state
 */
export default function createTrackedStateContext<State extends {}>(
    initialState: () => State,
    changeDetector?: ChangeDetector<State>
): TrackedStateContextResult<State> {
    const context = createContext<UseTrackedState<State> | null>(null);

    const Provider = ({ children }: React.PropsWithChildren<{}>) => {
        const state = useTrackedState<State>(initialState(), changeDetector);

        return <context.Provider value={state}>{children}</context.Provider>;
    };

    const useStateHook = () => {
        const ctx = useContext(context);

        if (ctx === null) throw new Error('invalid operation: tracked state context not found');

        return ctx;
    };

    const withState: TrackedStateContextResult<State>['withState'] = (Component) => {
        const Inner: ReturnType<TrackedStateContextResult<State>['withState']> = ({
            trackedStateRef,
            ...props
        }) => {
            const state = useStateHook();

            useEffect(() => {
                if (trackedStateRef) {
                    trackedStateRef.current = state;
                }
            }, [trackedStateRef, state]);

            // @ts-ignore
            return <Component trackedState={state} {...props} />;
        };

        return (props) => {
            return (
                <Provider>
                    <Inner {...props} />
                </Provider>
            );
        };
    };

    return {
        Provider,
        useStateHook,
        withState,
    };
}
