// One instance per tab
// Mimics https://github.com/enmasseio/timesync logic

const ENDPOINT = import.meta.env.VITE_GENERAL_API_BASE_URL + '/time';
const PROBES_PER_SYNC = 3; // How many request/response pairs each sync
const PROBE_DELAY = 100; // MS gap between probes
const RESYNC_INTERVAL_MS = 120_000; // Long-running tabs stay accurate

type Subscriber = () => void;

class UtcClock {
    private offset = 0;
    private readonly subs = new Set<Subscriber>();

    constructor() {
        this.sync().then(() => {
            setInterval(() => this.sync(), RESYNC_INTERVAL_MS);
        });
    }

    now = (): number => performance.now() + this.offset;

    // Subscribe to offset changes
    subscribe = (cb: Subscriber): (() => void) => {
        this.subs.add(cb);
        return () => this.subs.delete(cb);
    };

    get offsetMs() {
        return this.offset;
    }

    private async sync() {
        const diffs: number[] = [];

        for (let i = 0; i < PROBES_PER_SYNC; i++) {
            try {
                const perfSend = performance.now();
                const res = await fetch(ENDPOINT, { cache: 'no-store' });
                const serverUtc: number = await res.json();
                const perfReceive = performance.now();

                const oneWay = (perfReceive - perfSend) / 2;
                const perfAtServer = perfReceive - oneWay;
                diffs.push(serverUtc - perfAtServer);
            } catch {
                // Ignore
            }
            await new Promise((r) => setTimeout(r, PROBE_DELAY));
        }

        if (diffs.length) {
            diffs.sort((a, b) => a - b);
            this.offset = diffs[Math.floor(diffs.length / 2)]; // Median
            this.subs.forEach((cb) => cb());
        }
    }
}

// Singleton instance
export const utcClock = new UtcClock();
