import use<PERSON>orceRender from 'common/hooks/useForceRender';
import { ComponentType, createContext, useContext, useEffect, useMemo } from 'react';

export type LoadingEntry = {
    setLoading(loading: boolean): void;
    setError(err: any): void;
    dispose(): void;
};

interface LoadingScopeController {
    setLoading(key: string, value: boolean | undefined): void;
    setError(key: string, error: any): void;
}

const LoadingScopeControllerContext = createContext<LoadingScopeController | null>(null);

const dummyEntry: LoadingEntry = {
    dispose: () => {},
    setLoading: () => {},
    setError: () => {},
};

export function useLoadingEntry(name: string, defaultValue: boolean = true) {
    const controller = useContext(LoadingScopeControllerContext);

    const entry: LoadingEntry = useMemo(() => {
        if (!controller) return dummyEntry;

        return {
            dispose: () => controller.setLoading(name, undefined),
            setLoading: (loading) => controller.setLoading(name, loading),
            setError: (err) => controller.setError(name, err),
        };
    }, [controller, name]);

    useEffect(() => {
        entry.setLoading(defaultValue);
    }, [entry, defaultValue]);

    useEffect(() => () => entry.dispose(), [entry]);

    return entry;
}

class LoadingScopeControllerImpl implements LoadingScopeController {
    private readonly _entries: Record<string, { loading: boolean | undefined; error?: any }> = {};
    private readonly _forceRender: () => void;
    private _isLoading: boolean | undefined = undefined;

    constructor(forceRender: () => void) {
        this._forceRender = forceRender;
    }

    setLoading(key: string, value: boolean | undefined): void {
        if (typeof value === 'undefined') {
            this._getEntry(key).loading = undefined;
            delete this._entries[key];
        } else {
            this._getEntry(key).loading = value;
        }
        this._checkForChanges();
    }

    setError(key: string, error: any): void {
        const entry = this._getEntry(key);
        entry.error = error;
        entry.loading = false;
        this._checkForChanges();
    }

    get isLoading() {
        return this._isLoading;
    }

    private _getEntry(name: string) {
        if (!this._entries[name]) this._entries[name] = { loading: undefined };
        return this._entries[name];
    }

    private _checkForChanges() {
        let newLoading: boolean | undefined = undefined;

        if (Object.keys(this._entries).length) {
            const entries = Object.values(this._entries);
            newLoading = entries.some((x) => x.loading);
        }

        if (newLoading !== this._isLoading) {
            this._isLoading = newLoading;
            this._forceRender();
        }
    }
}

export type LoadingScopeProps = {
    children: (loading: boolean | undefined) => React.ReactNode;
};

export function LoadingScope({ children }: LoadingScopeProps) {
    const forceRender = useForceRender();
    const controller = useMemo(() => new LoadingScopeControllerImpl(forceRender), [forceRender]);

    return (
        <LoadingScopeControllerContext.Provider value={controller}>
            {children(controller.isLoading)}
        </LoadingScopeControllerContext.Provider>
    );
}

export type WithLoadingScope = {
    $isLoading: boolean | undefined;
};

export function withLoadingScope<T>(
    component: ComponentType<T & WithLoadingScope>
): ComponentType<T> {
    const Component = component;
    return (props: T) => (
        <LoadingScope>{(loading) => <Component $isLoading={loading} {...props} />}</LoadingScope>
    );
}
