// import fixWebmDuration from 'fix-webm-duration'; // required for recording audio
import nr from 'utils/nr';
import * as oggCompat from './oggCompat';

export { oggCompat };

/// #######################################
/// Ogg compatibility layer initialization
/// #######################################

let oggPlaybackStatus: 'supported' | 'unsupported' | 'supportedViaOgvCompatLayer' | 'pending' =
    'pending';

async function initOggPlayback() {
    if (oggCompat.isOggOpusSupported()) {
        oggPlaybackStatus = 'supported';
    } else {
        await oggCompat.loadOggCompat();

        if (oggCompat.isOGVPlayerSupported()) {
            try {
                oggPlaybackStatus = 'supportedViaOgvCompatLayer';
            } catch (e: unknown) {
                console.error('[audio] failed to load ogv.js compat layer', e);
                oggPlaybackStatus = 'unsupported';
            }
        } else {
            nr('ogvjsCompatFailed');
            oggPlaybackStatus = 'unsupported';
        }
    }
}

initOggPlayback();

export function createOggAudioElement(): HTMLMediaElement {
    switch (oggPlaybackStatus) {
        case 'supported': // ogg is supported
        case 'pending': // initOggPlayback is not yet finished (very unlikely case)
            return document.createElement('audio');

        case 'unsupported':
            // both ogg and ogv.js compatibility layer are not supported (very unlikely case)
            // we will still create audio element just so that we can use it later and then get an error when ogg playback inevitable fails
            return document.createElement('audio');

        case 'supportedViaOgvCompatLayer':
            return oggCompat.createOGVJSPlayer();

        default:
            throw new Error('unknown value for oggPlaybackStatus');
    }
}

// code below may or may not be useful in the future so I'll keep it
// https://imgur.com/a/BSIaTs4

/// ############################################
/// Audio recording - wrapper for MediaRecorder
/// ############################################

// const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// class AudioVolume {
//     private readonly _source: MediaStreamAudioSourceNode;
//     private readonly _analyser: AnalyserNode;
//     private readonly _array: Uint8Array;

//     constructor(stream: MediaStream) {
//         const context = new AudioContext();
//         this._source = context.createMediaStreamSource(stream);
//         this._analyser = context.createAnalyser();
//         this._source.connect(this._analyser);
//         this._array = new Uint8Array(this._analyser.fftSize);
//     }

//     dispose() {
//         this._source.disconnect(this._analyser);
//     }

//     /**
//      * Get peak volume since last time getPeakLevel was called.
//      * This should be called periodically.
//      */
//     getPeakLevel() {
//         this._analyser.getByteTimeDomainData(this._array);
//         return (
//             this._array.reduce((max, current) => Math.max(max, Math.abs(current - 127)), 0) / 128
//         );
//     }
// }

// export class AudioRecorder {
//     private _mr: MediaRecorder | undefined;
//     private _chunks: Blob[] = [];
//     private readonly _volume: AudioVolume;
//     private _start: number = 0;
//     private _duration: number = 0;

//     private constructor(mr: MediaRecorder, stream: MediaStream) {
//         this._mr = mr;
//         this._mr.addEventListener('dataavailable', (e) => {
//             console.log('dataavailable', e.data);
//             this._chunks.push(e.data);
//         });
//         this._volume = new AudioVolume(stream);
//     }

//     getVolume() {
//         return this._volume.getPeakLevel();
//     }

//     start() {
//         if (this._getMR().state === 'recording') return;
//         this._getMR().start();
//         this._chunks = [];
//         this._start = Date.now();
//         this._duration = 0;
//     }

//     async stop() {
//         if (this._getMR().state === 'inactive') return;
//         const promise = waitToFinishRecording(this._getMR());
//         this._duration += Date.now() - this._start;
//         await this._waitForWholeSeconds();
//         this._getMR().stop();
//         await promise;
//     }

//     private async _waitForWholeSeconds() {
//         const seconds = Math.ceil(this._duration / 1000);
//         const remaining = seconds * 1000 - this._duration;
//         await delay(remaining);
//         this._duration = seconds * 1000;
//     }

//     pause() {
//         this._getMR().pause();
//         this._duration += Date.now() - this._start;
//     }

//     resume() {
//         this._getMR().resume();
//         this._start = Date.now();
//     }

//     dispose() {
//         if (this._mr) {
//             if (this._mr.state !== 'inactive') {
//                 this._mr.stop();
//             }

//             this._mr.stream.getTracks().forEach((t) => t.stop());

//             this._mr = undefined;
//         }

//         this._chunks = [];
//         this._start = 0;
//         this._duration = 0;
//         this._volume.dispose();
//     }

//     async getBlob(): Promise<Blob> {
//         const blob = new Blob(this._chunks, { type: this._getMR().mimeType });
//         if (blob.type.startsWith('audio/webm')) {
//             const fixedBlob = await fixWebmDuration(blob, this._duration);
//             return fixedBlob;
//         }

//         return blob;
//     }

//     private _getMR(): MediaRecorder {
//         if (this._mr) return this._mr;
//         throw new Error('AudioRecorder has been disposed of and cannot be used anymore');
//     }

//     public static async create(): Promise<AudioRecorder> {
//         const [mr, stream] = await createMediaRecorder();

//         return new AudioRecorder(mr, stream);
//     }

//     public static getAudioFormat(): AudioFormat | undefined {
//         const mimeType = getSupportedAudioFormat();
//         if (!mimeType) return undefined;
//         if (mimeType.startsWith('audio/webm')) return 'webm';
//         if (mimeType.startsWith('audio/ogg')) return 'ogg';
//         if (mimeType.startsWith('audio/mp4')) return 'm4a';
//         if (mimeType.startsWith('audio/aac')) return 'aac';
//         if (mimeType.startsWith('audio/mp3')) return 'mp3';

//         return undefined;
//     }
// }

// export async function createMediaRecorder(): Promise<[MediaRecorder, MediaStream]> {
//     const stream = await navigator.mediaDevices.getUserMedia({
//         audio: {
//             sampleRate: 16000,
//             channelCount: 1,
//             autoGainControl: true,
//             echoCancellation: true,
//             noiseSuppression: true,
//         },
//     });

//     const mediaRecorder = new MediaRecorder(stream, {
//         mimeType: getSupportedAudioFormat(),
//         // audioBitsPerSecond: 32000,
//     });
//     return [mediaRecorder, stream];
// }

// /**
//  * Waits for MediaRecorder to stop.
//  * WARNING: if MediaRecorder is already stopped will wait *forever*
//  */
// function waitToFinishRecording(mr: MediaRecorder) {
//     return new Promise<void>((resolve) => {
//         const callback = () => {
//             mr.removeEventListener('stop', callback);
//             resolve();
//         };
//         mr.addEventListener('stop', callback);
//     });
// }

// /**
//  * Returns first supported audio format (for recording),
//  * returns undefined if browser cannot record any of the
//  * formats that CMOS supports.
//  */
// function getSupportedAudioFormat(): string | undefined {
//     const formats = [
//         'audio/ogg;codecs=opus',
//         'audio/ogg', // vorbis codec or any other codec
//         'audio/webm;codecs=opus',
//         'audio/webm', // other codecs
//         'audio/mp4', // Safari records mp4 with aac audio
//         'audio/mp3',
//         'audio/aac', // I don't think this is supported by any browsers, but our backend does support it
//     ];

//     for (const format of formats) {
//         if (MediaRecorder.isTypeSupported(format)) {
//             return format;
//         }
//     }

//     return undefined;
// }
