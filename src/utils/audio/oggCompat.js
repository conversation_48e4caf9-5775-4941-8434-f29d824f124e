/**
 * OGG compat layer based on ogv.js (using WebAssembly for real-time transcoding under the hood).
 *
 * Why not use typescript? -> ogv.js does not have Typescript typings so we're gonna have to make light wrapper for it.
 *
 * ┌──────────────────────────────────────┐  ┌──────────────────────────────────────┐
 * │  Ogg file on Safari,                 │  │                                      │
 * │  A silent symphony plays.            │  │  "How about no?"                     │
 * │  "Unsupported", it says.             │  │                                      │
 * │                                      │  │  -- some guy at Apple, apparently,   │
 * │  -- GPT-4, circa 2024                │  │  after being asked about OGG format  │
 * └──────────────────────────────────────┘  └──────────────────────────────────────┘
 *
 * # IMPORTANT
 * This library uses global variables, because it was not made for modern builders like esbuild/vite.
 * This should be fine, considering that it was developed at Wikipedia and folks there probably know
 * what they're doing.
 */

import nr from 'utils/nr';

// oh god, just ignore this line, just imagine it's not there
const BASE_PATH = '/dashboard/assets/lib/ogvjs-1.8.9'; // hard-coded path because we know for a fact that it will be there and totally not because I am just lazy to do something about it

/**
 * @returns {boolean} whether or not ogg-opus can be played
 */
export function isOggOpusSupported() {
    const audio = new Audio();
    // canPlayType returns "probably", "maybe" or "" which is hilarious and sad at the same time
    return audio.canPlayType('audio/ogg;codecs=opus') === 'probably';
}

/**
 * @returns {boolean} whether or not OGVPlayer can be used, if ogv.js has not been loaded yet will return false
 */
export function isOGVPlayerSupported() {
    return window.OGVCompat ? window.OGVCompat.supported('OGVPlayer') : false;
}

/**
 * @returns {Promise<void>}
 */
export async function loadOggCompat() {
    console.debug('[oggCompat] BASE_PATH', BASE_PATH);

    if (document.querySelector('script[data-ogv-compat-layer]')) {
        return;
    }

    if (window.localStorage && !window.localStorage.getItem('ogvjs')) {
        window.localStorage.setItem('ogvjs', 'yes');
        nr('ogvjsCompatLoaded');
    }

    const promise = new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.setAttribute('data-ogv-compat-layer', 'ogvjs');
        script.src = BASE_PATH + '/ogv-es2017.js';
        script.onload = () => {
            resolve();
        };
        script.onerror = (e) => {
            // failed to load ogv.js main script
            reject(e);
        };
        document.body.appendChild(script);
    });
    await promise;

    // NOTE (MB) ogv.js will load more dependencies as needed (encoder, worker and muxer)
}

/**
 * @returns {string} version of ogv.js installed
 */
export function getOGVCompatLayerVersion() {
    return window.OGVVersion || 'unknown';
}

/**
 * Creates new instance of OGVPlayer that can be used a substitute for HTMLMediaElement
 * for playing OGG format.
 *
 * Please note that returned value is not an instance of
 * HTMLMediaElement (so instanceof check will return false, for example), but it
 * will behave the same way.
 *
 * @returns {HTMLMediaElement} OGVJSPlayer instance that will behave like HTMLMediaElement (a polyfill if you will)
 */
export function createOGVJSPlayer() {
    if (!window.OGVPlayer) throw new Error('OGVPlayer is not loaded');
    return new window.OGVPlayer();
}
