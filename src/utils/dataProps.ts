type RemoveDataProps<T> = {
    [K in keyof T as K extends `data-${string}` ? never : K]: T[K];
};

export function splitDataProps<T extends object>(
    props: T
): [RemoveDataProps<T>, Record<string, string>] {
    const dataProps: Record<string, string> = {};
    // @ts-ignore
    const otherProps: RemoveDataProps<T> = {};

    for (const [key, value] of Object.entries(props)) {
        if (key.startsWith('data-')) {
            dataProps[key] = value as string;
        } else {
            // @ts-ignore
            otherProps[key] = value;
        }
    }

    return [otherProps, dataProps];
}

export type DataProps = Record<`data-${string}`, string>;
