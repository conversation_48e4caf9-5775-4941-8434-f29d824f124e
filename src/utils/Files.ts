export const formatBytes = (bytes: number): string => {
    if (!+bytes) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];

    let i = Math.floor(Math.log(bytes) / Math.log(k));
    if (i > sizes.length) i = sizes.length - 1;

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(0))} ${sizes[i]}`;
};

export const getFileExtension = (file: File | null): string | undefined => {
    return file?.name?.split('.')?.pop();
};
