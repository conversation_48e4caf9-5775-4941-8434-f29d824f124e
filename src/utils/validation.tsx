import useForceRender from 'common/hooks/useForceRender';
import React, { createContext, useCallback, useContext, useEffect, useRef } from 'react';

type Validator<T> = (value: T) => boolean | string;

export type UseValidator = {
    validate(): boolean;
    reset(): void;
    valid: boolean | undefined;
};

type UseManualValidationOptions = {
    /**
     * callback function that will be called when the error changes.
     * WILL NOT be called if valid is true.
     */
    onErrorChanged?: (error: string | undefined) => void;

    initialValidateOnChange?: boolean;

    defaultValidState?: boolean;
};

export function useValidation<T>(
    value: T,
    validator: Validator<T>,
    options: UseManualValidationOptions = {}
): UseValidator {
    const { initialValidateOnChange = false } = options;

    const fr = useForceRender();
    const stateRef = useRef({
        value,
        validateOnChange: initialValidateOnChange,
        valid: options.defaultValidState as ReturnType<Validator<T>> | undefined,
        validator,
        options,
    });

    const validate = useCallback(() => {
        stateRef.current.validateOnChange = true; // force-enable this on each validation call

        const validNew = stateRef.current.validator(stateRef.current.value);
        if (validNew !== stateRef.current.valid) {
            stateRef.current.valid = validNew;

            if (validNew !== true && stateRef.current.options.onErrorChanged) {
                stateRef.current.options.onErrorChanged(validNew === false ? undefined : validNew);
            }

            fr();
        }

        return stateRef.current.valid !== false;
    }, [fr]);

    const reset = useCallback(() => {
        stateRef.current.validateOnChange =
            stateRef.current.options.initialValidateOnChange ?? false;
        stateRef.current.valid = undefined;
    }, []);

    useEffect(() => {
        if (stateRef.current.value !== value) {
            stateRef.current.value = value;

            if (stateRef.current.validateOnChange) {
                validate();
            }
        }
    }, [value, validate]);

    // if true - valid
    // if undefined - undetermined
    // if anything else - invalid
    const valid =
        stateRef.current.valid === true
            ? true
            : stateRef.current.valid === undefined
            ? undefined
            : false;

    return {
        valid,
        validate,
        reset,
    };
}

export type RegisteredInputOptions = {
    onReset(): void;
    onValidate(): boolean;
};

export type ValidateAllResult = {
    valid: boolean | undefined;
    totalInputs: number;
    validInputs: number;
};

export interface ValidationContext {
    validateAll(): ValidateAllResult;
    resetAll(): void;
}

class ValidationContextImpl implements ValidationContext {
    private readonly _inputs: Record<string, () => RegisteredInputOptions> = {};

    registerInput(inputOptions: () => RegisteredInputOptions): string {
        const id = Math.random().toString().substring(2);
        this._inputs[id] = inputOptions;
        return id;
    }

    unregisterInput(id: string): void {
        delete this._inputs[id];
    }

    validateAll(): ValidateAllResult {
        const entries = Object.entries(this._inputs);

        const result: ValidateAllResult = {
            totalInputs: entries.length,
            validInputs: 0,
            valid: undefined,
        };

        if (result.totalInputs === 0) return result;

        for (const [_inputId, inputOptions] of entries) {
            try {
                const isValid = inputOptions().onValidate();
                if (isValid) result.validInputs++;
            } catch (e: unknown) {
                console.error('Error while trying to validate input', e);
            }
        }

        return result;
    }

    resetAll(): void {
        for (const id in this._inputs) {
            try {
                this._inputs[id]().onReset();
            } catch (e: unknown) {
                console.error('Error while trying to reset the input', e);
            }
        }
    }
}

const ValidationContextContext = createContext<ValidationContext | null>(null);

export class ValidationContextProvider extends React.Component<React.PropsWithChildren<{}>> {
    private readonly _validationContext: ValidationContext;

    constructor(props: Readonly<React.PropsWithChildren<{}>>) {
        super(props);
        this._validationContext = new ValidationContextImpl();
    }

    validateAll() {
        return this._validationContext.validateAll();
    }

    resetAll() {
        this._validationContext.resetAll();
    }

    render() {
        const { children } = this.props;

        return (
            <ValidationContextContext.Provider value={this._validationContext}>
                {children}
            </ValidationContextContext.Provider>
        );
    }
}

export function useValidationContextInput(options: RegisteredInputOptions) {
    const ctx = useContext(ValidationContextContext);
    if (!(ctx === null || ctx instanceof ValidationContextImpl))
        throw new Error('unsupported ValidationContext implementation');
    const optionsRef = useRef(options);

    useEffect(() => {
        if (!ctx) return;
        const inputId = ctx.registerInput(() => optionsRef.current);
        return () => {
            ctx.unregisterInput(inputId);
        };
    }, [ctx]);
}
