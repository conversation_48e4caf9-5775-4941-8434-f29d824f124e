import { createContext, useContext, useMemo } from 'react';
import { Subject } from 'utils/event';

export type ToasterUpdate = {
    title?: React.ReactNode;
    content?: React.ReactNode;
    color?: string;
    icon?: React.ReactNode;
    preset?: ToasterPreset;
    expiresAt?: number;
    createdAt?: number;
    isDetachable?: boolean;
    closeable?: boolean;
    data?: Record<string, unknown>;
    onClose?: () => void;
};

export type ToasterPreset =
    | 'success'
    | 'info'
    | 'danger'
    | 'warning'
    | 'none'
    | 'progress'
    | 'network';

export type ToasterData = {
    title?: React.ReactNode;
    content?: React.ReactNode;
    color?: string;
    icon?: React.ReactNode;
    count?: number;
    expiresAt: number;
    createdAt: number;
    preset: ToasterPreset;
    isDetached: boolean;
    isDetachable: boolean;
    closeable: boolean;
    onClose?: () => void;
    data: Record<string, unknown>;
};

function defaultToasterData(): ToasterData {
    return {
        expiresAt: -1,
        preset: 'none',
        isDetachable: false, // TODO change to false
        isDetached: false,
        createdAt: Date.now(),
        closeable: true,
        data: {},
    };
}

function applyUpdate(data: ToasterData, update: ToasterUpdate): ToasterData {
    return {
        ...data,
        ...update,
    };
}

export interface Toaster {
    readonly id: string;
    dismiss(): void;
    update(update: ToasterUpdate): void;
    getData(): ToasterData;
    incrementCounter(inc: number): void;
    subscribe(callback: (data: ToasterData) => void): () => void;
}

function isToasterEqual(a: Toaster, b: Toaster) {
    const aData = a.getData();
    const bData = b.getData();
    return (
        aData.title === bData.title &&
        aData.preset === bData.preset &&
        aData.color === bData.color &&
        aData.content === bData.content &&
        aData.icon === bData.icon
    );
}

type Callback = (toasters: Toaster[]) => void;

export interface ToasterController {
    forEach(fn: (toaster: Toaster) => void): unknown;
    createToaster(id?: string, update?: ToasterUpdate): Toaster;
    subscribeToChanges(callback: Callback): () => void;
    dismiss(id: string): void;
    update(id: string, update: ToasterUpdate): void;
}

const ToastersControllerContext = createContext<ToasterController | null>(null);

class ToasterControllerImpl implements ToasterController {
    private static ToasterImpl = class implements Toaster {
        private readonly _controller: ToasterControllerImpl;
        readonly id: string;
        private readonly _state: Subject<ToasterData>;

        constructor(id: string, controller: ToasterControllerImpl, state: ToasterData) {
            this.id = id;
            this._controller = controller;
            this._state = new Subject<ToasterData>(state);
        }
        subscribe(callback: (data: ToasterData) => void): () => void {
            return this._state.subscribe(callback);
        }

        incrementCounter(inc: number): void {
            this._state.update((s) => ({
                ...s,
                count: (s.count ?? 1) + inc,
            }));
        }

        dismiss(): void {
            this._controller.dismiss(this.id);
        }

        update(update: ToasterUpdate): void {
            this._state.update((value) => {
                return applyUpdate(value, update);
            });
        }

        getData(): ToasterData {
            return this._state.value;
        }
    };

    private readonly _subscribers: Callback[] = [];
    private readonly _toasters: Record<string, Toaster> = {};

    createToaster(id?: string, update?: ToasterUpdate): Toaster {
        id ??= Math.random().toString(36).substring(2);
        let data = defaultToasterData();
        if (update) data = applyUpdate(data, update);
        const toaster = new ToasterControllerImpl.ToasterImpl(id, this, data);

        if (this._toasters[id]) {
            if (update)
                this._toasters[id].update({
                    ...update,
                    createdAt: data.createdAt,
                    expiresAt: Math.max(data.expiresAt, toaster.getData().expiresAt),
                });
            return this._toasters[id];
        }

        const dup = this._findDuplicate(toaster);
        if (dup) {
            dup.incrementCounter(1);
            if (dup.getData().expiresAt < data.expiresAt)
                dup.update({
                    expiresAt: data.expiresAt,
                    createdAt: data.createdAt,
                });
            return dup;
        }
        this._toasters[id] = toaster;
        this._triggerChange();
        return toaster;
    }

    subscribeToChanges(callback: (toasters: Toaster[]) => void): () => void {
        if (!this._subscribers.includes(callback)) this._subscribers.push(callback);
        setTimeout(() => callback(this._getToasters()), 1);
        return () => this._unsub(callback);
    }

    forEach(fn: (toaster: Toaster) => void): void {
        this._getToasters().forEach(fn);
    }

    private _findDuplicate(t: Toaster): Toaster | undefined {
        for (const key in this._toasters) {
            if (isToasterEqual(t, this._toasters[key])) return this._toasters[key];
        }

        return;
    }

    private _unsub(callback: Callback) {
        const idx = this._subscribers.indexOf(callback);
        if (idx !== -1) this._subscribers.splice(idx, 1);
    }

    dismiss(id: string): void {
        if (!this._toasters[id]) return;

        const onClose = this._toasters[id].getData().onClose;

        if (onClose) {
            try {
                onClose();
            } catch (e: unknown) {
                console.error(`failed to call onClose callback on toaster ${id}`, e);
            }
        }

        delete this._toasters[id];
        this._triggerChange();
    }
    update(id: string, update: ToasterUpdate): void {
        const toaster = this._get(id);
        toaster.update(update);
    }

    private _triggerChange() {
        if (this._subscribers.length === 0) return;
        const toasters = this._getToasters();
        for (const callback of this._subscribers) callback(toasters);
    }

    private _getToasters() {
        return Object.values(this._toasters);
    }

    private _get(id: string): Toaster {
        const toaster = this._toasters[id];
        if (!toaster) throw new Error(`toaster with id = ${id} not found`);
        return toaster;
    }
}

export function ToastersControllerProvider({ children }: React.PropsWithChildren<{}>) {
    const controller = useMemo(() => new ToasterControllerImpl(), []);
    return (
        <ToastersControllerContext.Provider value={controller}>
            {children}
        </ToastersControllerContext.Provider>
    );
}

export function useToastersController() {
    const controller = useContext(ToastersControllerContext);
    if (controller === null) throw new Error('toasters controller not found');
    return controller;
}
