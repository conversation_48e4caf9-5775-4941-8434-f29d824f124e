import { createEvent, Unsubscribe } from './event';

export interface IFuture<T> {
    resolve(value: T): void;
    reject(error: unknown): void;
    fromPromise(promise: Promise<T>): void;
    getPromise(): Promise<T>;
    isFinished(): boolean;
}

export class Future<T = void> implements IFuture<T> {
    private static PENDING = 0;
    private static REJECTED = 1;
    private static FULFILLED = 2;

    private value?: T;
    private error?: unknown;
    private state = Future.PENDING;

    private readonly finished = createEvent<void>();

    resolve(value: T): void {
        if (this.state !== Future.PENDING) return;

        this.value = value;
        this.state = Future.FULFILLED;
        this.finished.publish();
    }

    reject(error: unknown): void {
        if (this.state !== Future.PENDING) return;

        this.error = error;
        this.state = Future.REJECTED;
        this.finished.publish();
    }

    fromPromise(promise: Promise<T>): void {
        promise.then(this.resolve.bind(this)).catch(this.reject.bind(this));
    }

    getPromise(): Promise<T> {
        return new Promise((resolve, reject) => {
            if (this.state === Future.PENDING) {
                let unsubscribe: Unsubscribe | undefined;

                const callback = () => {
                    if (this.state === Future.FULFILLED) {
                        resolve(this.value!);
                    } else if (this.state === Future.REJECTED) {
                        reject(this.error);
                    } else {
                        reject('internal error: invalid future state');
                    }

                    if (unsubscribe) {
                        unsubscribe();
                        unsubscribe = undefined;
                    }
                };

                unsubscribe = this.finished.subscribe(callback);
            } else if (this.state === Future.FULFILLED) {
                resolve(this.value!);
            } else if (this.state === Future.REJECTED) {
                reject(this.error);
            } else {
                reject('invalid future state');
            }
        });
    }

    isFinished(): boolean {
        return this.state !== Future.PENDING;
    }
}
