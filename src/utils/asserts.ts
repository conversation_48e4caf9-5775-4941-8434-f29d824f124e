import { useDebugValue, useEffect, useRef } from 'react';

let useDebugAssertionMustNotChange: (value: unknown) => void;

if (import.meta.env.DEV) {
    useDebugAssertionMustNotChange = (value: unknown) => {
        useDebugValue(value);
        const isFirst = useRef(true);
        useEffect(() => {
            if (isFirst.current) {
                isFirst.current = false;
            } else {
                console.error(`WARNING! value changed even though it shouldn't have: ${value}`);
            }
        }, [value]);
    };
} else {
    useDebugAssertionMustNotChange = () => {};
}

export { useDebugAssertionMustNotChange };
