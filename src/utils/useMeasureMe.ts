import { useEffect, useRef } from 'react';

export default function useMeasureMe(key: string) {
    const state = useRef({
        renders: 0,
    });
    state.current.renders++;

    useEffect(() => {
        const callback = () => {
            console.debug(`[useMeasureMe] key=${key} renders/s = ${state.current.renders}`);
            state.current.renders = 0;
        };

        const interval = setInterval(callback, 1000);
        return () => clearInterval(interval);
    }, [key]);
}
