import * as Sentry from '@sentry/react';
import React, { ErrorInfo } from 'react';
import FatalError from './FatalError';

type State = {
    error?: Error;
    errorInfo?: ErrorInfo;
};
type Props = React.PropsWithChildren<{}>;

class PassThrough extends React.Component<Props> {
    render(): React.ReactNode {
        const { children } = this.props;
        return <>{children}</>;
    }
}

/**
 * Final frontier of battle against errors (we've lost at this point).
 * On a serious note: if it gets here, something REALLY went wrong.
 */
class FatalErrorBoundaryImpl extends React.Component<Props, State> {
    constructor(props: Readonly<Props>) {
        super(props);
        this.state = {};
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
        Sentry.captureException(error, { data: errorInfo });
        this.setState({ error, errorInfo });
    }

    render(): React.ReactNode {
        const { error, errorInfo } = this.state;

        if (error && errorInfo) {
            return <FatalError errorInfo={errorInfo} error={error} />;
        }

        const { children } = this.props;

        return <>{children}</>;
    }
}

const enableFatalErrorBoundary = !new URLSearchParams(window.location.search).has(
    'debug.FatalErrorBoundary.disable'
);

export const FatalErrorBoundary = enableFatalErrorBoundary ? FatalErrorBoundaryImpl : PassThrough;
