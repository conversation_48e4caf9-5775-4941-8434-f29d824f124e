import { AxiosError, isAxiosError } from 'axios';
import { GeneralWPJobsErrorCode } from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { ENTERPRISE_ROUTES, ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Http } from 'services/Http';
import { isErrorResponse, Server } from 'services/Server';
import { useAppDispatch } from 'store';
import { userActions } from 'store/slices/user';
import nr from 'utils/nr';

export default function ErrorsReporting() {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    const isEnterprise = useIsEnterpriseRoute();

    const onApiError = useCallback(
        (err: AxiosError) => {
            if (err.code === AxiosError.ERR_CANCELED) {
                //user intentionally canceled the request
                return;
            }
            if (!err.response) {
                // some kind of network error
                toasters.networkError();
                return;
            } else if (err.response.status === 401) {
                nr('logout.401');
                dispatch(userActions.forceLogout());
                navigate({
                    pathname: isEnterprise ? ENTERPRISE_ROUTES.LOGIN : ROUTES.LOGIN,
                    // search: '?reason=401',
                });
                return;
            } else if (err.response.status === 402) {
                // Payment Required - shop is deactivated
                nr('shopDeactivatedRedirect');
                navigate(
                    isEnterprise
                        ? ENTERPRISE_ROUTES.ACCOUNT_DEACTIVATED
                        : ROUTES.ACCOUNT_DEACTIVATED
                );
                return;
            } else if (err.response.status === 403) {
                if (
                    isAxiosError(err) &&
                    isErrorResponse(err.response?.data) &&
                    err.response.data.code === GeneralWPJobsErrorCode.NoEditJobsPermission
                )
                    return;
                nr('logout.403');
                dispatch(userActions.forceLogout());
                navigate({
                    pathname: isEnterprise ? ENTERPRISE_ROUTES.LOGIN : ROUTES.LOGIN,
                    // search: '?reason=403',
                });
                return;
            } else if (err.response.status === 500) {
                toasters.danger(
                    'Internal server error: ' + err.message,
                    t('toasters.errorOccurred')
                );
            } else if (err.response.status === 504) {
                toasters.danger(
                    'HTTP504: Gateway timeout (service is unavailable)\n See console for more details',
                    t('toasters.errorOccurred')
                );
            } else if (err.response.status >= 500) {
                toasters.danger(
                    `HTTP error ${err.response.status}: ${err.message}`,
                    t('toasters.errorOccurred')
                );
            }
        },
        [toasters, dispatch, navigate, isEnterprise, t]
    );

    useEffect(() => {
        const unsub1 = Server.apiError.subscribe(onApiError);
        const unsub2 = Http.axiosError.subscribe(onApiError);
        return () => {
            unsub1();
            unsub2();
        };
    }, [onApiError]);

    return null;
}
