import { ErrorInfo, useState } from 'react';
import ProductLogo from 'views/Components/ProductLogo';
import styles from './FatalError.module.css';

type Props = {
    error: Error;
    errorInfo: ErrorInfo;
};

export default function FatalError({ error, errorInfo }: Props) {
    const [showErrorInfo, setShowErrorInfo] = useState(false);

    return (
        <section className={styles.root}>
            <header className={styles.logo}>
                <ProductLogo scale={2} fill="#000" />
            </header>
            <div className={styles.content}>
                <p>Something went wrong. Please try again.</p>
                <p>
                    <ul>
                        <li>
                            <a href="/dashboard/login">Go to login</a>
                        </li>
                        <li>
                            <a href="/dashboard/enterprise/login">Go to login (Enterprise)</a>
                        </li>
                        <li>
                            <a href="/dashboard/orders">Go to orders</a>
                        </li>
                        <li>
                            <a href="/dashboard/enterprise/orders">Go to orders (Enterprise)</a>
                        </li>
                    </ul>
                    <div className={styles.buttons}>
                        <button
                            onClick={(e) => {
                                window.location.reload();
                            }}
                        >
                            Refresh the page
                        </button>

                        <button
                            onClick={() => setShowErrorInfo((v) => !v)}
                            className={styles.secondaryBtn}
                        >
                            Error info
                        </button>
                    </div>
                </p>
                {showErrorInfo && (
                    <pre>
                        <b>Error</b>: {error?.toString()}
                        {errorInfo.componentStack}
                    </pre>
                )}
            </div>
        </section>
    );
}
