import ErrorBoundary from './ErrorBoundary';
import SimpleErrorDisplay from './SimpleErrorDisplay';

export type SimpleErrorBoundaryProps = React.PropsWithChildren<{}>;

export default function SimpleErrorBoundary({ children }: SimpleErrorBoundaryProps) {
    return (
        <ErrorBoundary renderError={(e, retry) => <SimpleErrorDisplay retry={retry} ctx={e} />}>
            {children}
        </ErrorBoundary>
    );
}
