import { Warning } from '@mui/icons-material';
import { styled, SxProps, Typography } from '@mui/material';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import ErrorBoundary from './ErrorBoundary';

export type SimpleErrorBoundaryProps = React.PropsWithChildren<{}>;

export default function SimpleErrorBoundary2({ children }: SimpleErrorBoundaryProps) {
    return (
        <ErrorBoundary
            renderError={(ctx, retry) => (
                <SimpleErrorDisplay2 onRetry={retry} errorCombo={ctx.combo} />
            )}
        >
            {children}
        </ErrorBoundary>
    );
}

type SimpleErrorDisplayProps = {
    onRetry: () => void;
    errorCombo?: number;
    message?: string;
    sx?: SxProps;
};

export function SimpleErrorDisplay2({ onRetry, errorCombo, message, sx }: SimpleErrorDisplayProps) {
    const { t } = useAppTranslation();

    return (
        <DivRoot
            sx={sx}
            onMouseDown={(e) => {
                e.stopPropagation();
                e.preventDefault();
            }}
        >
            <Warning color="error" />
            <Typography variant="h6">
                {t('commonLabels.somethingWentWrong')}
                {errorCombo && errorCombo > 1 ? ` (x${errorCombo})` : undefined}
            </Typography>
            {message && <Typography variant="body1">{message}</Typography>}
            <Button
                disableRipple
                sx={{
                    ':active': { background: 'var(--cm5)' },
                    paddingLeft: '6px !important',
                    paddingRight: '6px !important',
                }}
                cmosSize={'small'}
                onClick={() => onRetry()}
                cmosVariant={'typography'}
                label={t('commonLabels.tryAgain')}
            />
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    position: 'absolute',
    inset: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    gap: '6px',
    padding: '20px',
});
