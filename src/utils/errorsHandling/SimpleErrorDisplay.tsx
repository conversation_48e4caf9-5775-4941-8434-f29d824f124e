import { Box, Button, SxProps, Theme, styled } from '@mui/material';
import { isCmosError } from 'api/error';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { ErrorContext } from './ErrorBoundary';

type Props = {
    ctx: ErrorContext;
    retry?: () => void;
    sx?: SxProps<Theme>;
    alwaysShowError?: boolean;
};

export default function SimpleErrorDisplay({
    sx,
    ctx: { error, errorInfo, combo = 1 },
    retry,
}: Props) {
    const { t } = useAppTranslation();

    return (
        <DivRoot sx={sx}>
            <H3Header>
                {t('commonLabels.somethingWentWrong')}
                {combo > 1 ? <small>{` (x${combo})`}</small> : ''}
            </H3Header>

            <StyledPre>{getErrorMessage(error)}</StyledPre>

            <Box sx={{ gap: 1.25, display: 'flex', ml: 2 }}>
                {retry && <ErrorButton onClick={retry}>Retry</ErrorButton>}

                {combo > 3 && (
                    <ErrorButton onClick={() => window.location.reload()}>Refresh page</ErrorButton>
                )}
            </Box>
        </DivRoot>
    );
}

const StyledPre = styled('pre')(({ theme }) => ({
    padding: '8px 0 8px 16px',
    backgroundColor: rgba(theme.palette.error.main, 0.1),
}));

const DivRoot = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.common.white,
    padding: '1px 0px 20px 0px',
    borderRadius: 10,
    boxShadow: `0 0 0 2px ${theme.palette.error.light} inset`,
}));

const H3Header = styled('h3')(({ theme }) => ({
    color: theme.palette.neutral[9],
    marginLeft: 16,
}));

const ErrorButton = styled(Button)(({ theme }) => ({
    border: 'none',
    padding: 5,
    minHeight: 0,
    minWidth: 0,
    textTransform: 'none',
    lineHeight: 1,
}));

function getErrorMessage(error: unknown): string {
    if (isCmosError(error)) {
        return `${error.cmosMessage}\nHTTP ${error.response?.status ?? '???'}\ncode=${
            error.code
        }\ncmosCode=${error.cmosCode}`;
    }

    switch (typeof error) {
        case 'string':
            return error;
        case 'object':
            if (error === null) {
                return 'null';
            }
            if (Object.hasOwn(error, 'toString')) {
                return error.toString();
            }
            if (error instanceof Error) {
                return error.message;
            }
    }

    return 'Unknown error';
}
