import * as Sentry from '@sentry/react';
import isEqual from 'lodash/isEqual';
import React, { ErrorInfo } from 'react';

export type ErrorContext = {
    error: unknown;
    errorInfo?: ErrorInfo;
    combo?: number;
};

export type ErrorBoundaryProps = React.PropsWithChildren<{
    renderError: (ctx: ErrorContext, retry: () => void) => React.ReactNode;
}>;

type State = {
    ctx?: ErrorContext;
    retrying?: boolean;
};

export default class ErrorBoundary extends React.Component<ErrorBoundaryProps, State> {
    constructor(props: Readonly<ErrorBoundaryProps>) {
        super(props);
        this.state = {};
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
        Sentry.captureException(error, { data: errorInfo });
        this.setState((s) => {
            return {
                ctx: {
                    combo: (s.ctx?.combo ?? 0) + 1,
                    error,
                    errorInfo,
                },
                retrying: false,
            };
        });
    }

    shouldComponentUpdate(
        nextProps: Readonly<ErrorBoundaryProps>,
        nextState: Readonly<State>,
        _nextContext: unknown
    ): boolean {
        return (
            !isEqual(this.state.ctx, nextState.ctx) ||
            nextState.retrying !== this.state.retrying ||
            nextProps.children !== this.props.children
        );
    }

    render(): React.ReactNode {
        const { renderError, children } = this.props;
        const { ctx, retrying } = this.state;
        if (ctx && !retrying) {
            return renderError(ctx, this.retry.bind(this));
        } else {
            return <>{children}</>;
        }
    }

    private retry() {
        this.setState({ retrying: true });
    }
}
