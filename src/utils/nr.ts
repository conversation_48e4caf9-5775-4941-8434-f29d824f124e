/**
 * New relic utils.
 */

type NRAPI = {
    addPageAction(eventName: string, properties?: object): void;
    setApplicationVersion(version: string): void;
    setCustomAttribute(key: string, value: string): void;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const nrApi: NRAPI | undefined = (window as any).NREUM;

function nr(eventName: string, properties?: object) {
    if (nrApi) {
        try {
            nrApi.addPageAction(eventName, properties);
        } catch (e: unknown) {
            console.error(e);
        }
    } else console.debug(`[nr] ${eventName}`, properties);
}

export function setApplicationVersion(version: string) {
    try {
        nrApi?.setApplicationVersion(version);
    } catch (e: unknown) {
        console.error(e);
    }
}

export function setCustomAttribute(key: string, value: string) {
    try {
        nrApi?.setCustomAttribute(key, value);
    } catch (e: unknown) {
        console.error(e);
    }
}

export default nr;
