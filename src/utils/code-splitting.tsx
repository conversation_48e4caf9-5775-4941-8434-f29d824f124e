import React, { ComponentType } from 'react';

type ComponentKey2Key<T> = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [K in keyof T]: T[K] extends ComponentType<any> ? K : never;
};
type ValueOf<T> = T[keyof T];
type ComponentKeys<T> = ValueOf<ComponentKey2Key<T>>;

/**
 * Allows to create and use a separate "chunk" file that contains multiple components.
 * For example:
 *
 * chunk/main.ts:
 *  import Orders from 'views/Orders';
 *  export { Orders };
 * App.ts:
 *  const chunk = getChunk(() => import('./chunks/main'));
 *  const Orders = chunk.component('Orders');
 *
 * And now you can use Orders as if it's a normal component (don't forget
 * to add Suspense component at the top of the component tree)
 *
 * @param fn function that returns an object with components, this function MUST contain import("path/to/chunk") in its body to be picked up by webpack
 * @returns helper object
 */
export function getChunk<T>(fn: () => Promise<T>) {
    let chunk: Promise<T> | undefined = undefined;

    const getChunkPromise = () => {
        if (!chunk) chunk = fn();
        return chunk;
    };

    const cacheComponents: Partial<Record<ComponentKeys<T>, ComponentType<{}>>> = {};

    return {
        element: <K extends ComponentKeys<T>>(key: K) => {
            if (!cacheComponents[key]) {
                cacheComponents[key] = React.lazy(async () => {
                    const chunkNotNull = getChunkPromise();
                    const chunkResult: T = await chunkNotNull;
                    const component = chunkResult[key] as ComponentType<{}>;
                    return { default: component };
                });
            }

            const Component: React.ComponentType<{}> = cacheComponents[key]!;
            return <Component />;
        },

        load: () => {
            getChunkPromise();
        },
    };
}
