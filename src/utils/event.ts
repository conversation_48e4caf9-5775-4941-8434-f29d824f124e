import useForceRender from 'common/hooks/useForceRender';
import { useEffect, useMemo, useRef, useState } from 'react';

export type Unsubscribe = () => void;
export type EventCallback<T> = (event: T) => void;

export interface IBusClient<TEvent> {
    dispose(): void;
    setCallback(callback: EventCallback<TEvent>): void;
    publish(event: TEvent): void;
}

export interface IBus<TEvent> {
    register(): IBusClient<TEvent>;
}

class Bus<T> implements IBus<T> {
    private readonly _clients: Record<string, BusClient<T>> = {};

    register(): IBusClient<T> {
        const client = new BusClient<T>(this, () => {});
        this._clients[client.id] = client;
        return client;
    }

    _deregister(client: BusClient<T>) {
        delete this._clients[client.id];
    }

    _publish(from: string, event: T): void {
        for (const key in this._clients) {
            if (key === from) continue;
            this._clients[key].callback(event);
        }
    }
}

export function newBus<T>() {
    return new Bus<T>();
}

class BusClient<TEvent> implements IBusClient<TEvent> {
    private readonly _bus: Bus<TEvent>;
    public callback: EventCallback<TEvent>;
    public readonly id: string;

    constructor(bus: Bus<TEvent>, callback: EventCallback<TEvent>) {
        this._bus = bus;
        this.callback = callback;
        this.id = Math.random().toString(36).substring(2);
    }
    dispose(): void {
        this._bus._deregister(this);
    }
    setCallback(callback: EventCallback<TEvent>): void {
        this.callback = callback;
    }
    publish(event: TEvent): void {
        this._bus._publish(this.id, event);
    }
}

export function useBusSubscription<T>(
    bus: IBus<T> | null | undefined,
    callback: EventCallback<T>
): IBusClient<T> | null {
    const client = useMemo(() => (bus ? bus.register() : null), [bus]);
    useEffect(() => () => client?.dispose(), [client]);

    useEffect(() => {
        client?.setCallback(callback);
    }, [client, callback]);
    return client;
}

export interface IEvent<T> {
    subscribe(event: (value: T) => void): Unsubscribe;
    publish(event: T): void;
}

class EventImpl<T> implements IEvent<T> {
    private readonly _callbacks: EventCallback<T>[] = [];

    subscribe(callback: (value: T) => void): Unsubscribe {
        this._callbacks.push(callback);
        return () => {
            const idx = this._callbacks.indexOf(callback);
            if (idx !== -1) this._callbacks.splice(idx, 1);
        };
    }

    publish(event: T): void {
        for (const c of this._callbacks) {
            c(event);
        }
    }
}

export function createEvent<T = unknown>(): IEvent<T> {
    return new EventImpl();
}

export function useEventSubscription<T>(event: IEvent<T>, fn: EventCallback<T>) {
    const ref = useRef<EventCallback<T>>(fn);
    ref.current = fn;

    useEffect(() => {
        return event.subscribe((v) => ref.current(v));
    }, [event]);
}

export class Subject<T> {
    private _value: T;
    private readonly _event: IEvent<void> = createEvent();

    constructor(value: T) {
        this._value = value;
    }

    get value(): T {
        return this._value;
    }

    subscribe(callback: (value: T) => void, callImmediately: boolean = true): Unsubscribe {
        if (callImmediately) callback(this.value);
        return this._event.subscribe(() => callback(this.value));
    }

    set(value: T) {
        this._value = value;
        this._event.publish(undefined);
    }

    update(fn: (value: T) => T) {
        this.set(fn(this._value));
    }
}

export function useSubject<T>(subject: Subject<T>): T {
    const [value, setValue] = useState<T>(subject.value);

    useEffect(() => {
        return subject.subscribe(setValue);
    }, [subject]);

    return value;
}

export function useSubjectSelector<T, P>(subject: Subject<T>, selector: (value: T) => P): P {
    const state = useRef<{ value: P; initialized: boolean }>({
        value: undefined as never as P,
        initialized: false,
    });
    const fr = useForceRender();

    if (!state.current.initialized) {
        state.current.value = selector(subject.value);
        state.current.initialized = true;
    }

    useEffect(() => {
        return subject.subscribe((newValue) => {
            const newProjectedValue = selector(newValue);
            if (newProjectedValue !== state.current.value) {
                state.current.value = newProjectedValue;
                fr();
            }
        });
    }, [fr, selector, subject]);

    return state.current.value;
}
