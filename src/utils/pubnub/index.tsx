import camelCase from 'lodash/camelCase';
import isObject from 'lodash/isObject';
import sortBy from 'lodash/sortBy';
import transform from 'lodash/transform';
import Pubnub from 'pubnub';
import { createContext, useContext, useEffect, useMemo, useRef } from 'react';
import { usePubnubOptional } from './provider';
import { useDeepMemoStringArray } from './util';

type Entry = {
    counter: number;
    timeoutMs: number;
    unsubscribeDeadline: number;
};

function bookEntry(entries: Record<string, Entry>, name: string, timeoutMs: number): boolean {
    if (entries[name] === undefined) {
        entries[name] = {
            counter: 1,
            timeoutMs,
            unsubscribeDeadline: Number.MAX_SAFE_INTEGER,
        };
        return true;
    } else {
        const entry = entries[name];
        entry.counter += 1;
        return entry.counter === 1;
    }
}

function cancelEntry(entries: Record<string, Entry>, name: string): void {
    if (entries[name] === undefined) return;
    const entry = entries[name];
    if (entry.counter > 0) {
        entry.counter -= 1;
        entry.unsubscribeDeadline = Date.now() + entry.timeoutMs;
    }
}

type MessageWrapper<T = unknown> = {
    type: string;
    payload: T;
};

export type MessageEvent<T = unknown> = {
    event: Pubnub.MessageEvent;
    message: MessageWrapper<T>;
};
export type MessageCallback<T = unknown> = (message: MessageEvent<T>) => void;

function isMessageWrapper(o: object): o is MessageWrapper {
    if (o === null) return false;
    return typeof o === 'object' && typeof (o as { type: unknown }).type === 'string';
}

function replaceKeysDeep(obj: object, keysMap: (key: string) => string): object {
    return transform(obj, (result, value, key) => {
        const currentKey = keysMap(key);
        (result as any)[currentKey] = isObject(value) ? replaceKeysDeep(value, keysMap) : value;
    });
}

function toCamelCase(o: object) {
    if (o === null) return null;
    return replaceKeysDeep(o, camelCase);
}

class ChannelsController {
    private static _instance: ChannelsController | undefined;

    private _initialized = false;
    private readonly _pubnub: Pubnub;
    private readonly _channels: Record<string, Entry> = {};
    private readonly _channelGroups: Record<string, Entry> = {};
    private _unsubLoopTimeout: number | undefined;
    private _callbacks: Record<string, MessageCallback[]> = {};

    constructor(pubnub: Pubnub) {
        this._pubnub = pubnub;
        this._handleMessage = this._handleMessage.bind(this);
        this.init();
    }

    book(channels: string[], channelGroups: string[], timeoutMs: number = 0): () => void {
        if (channels.length + channelGroups.length === 0)
            throw new Error('At least one channel or channel group must be specified');
        const newChannels: string[] = [];
        const newChannelGroups: string[] = [];

        const subscribedChannels = this._pubnub.getSubscribedChannels();
        const subscribedChannelGroups = this._pubnub.getSubscribedChannelGroups();

        for (const channel of channels)
            if (bookEntry(this._channels, channel, timeoutMs))
                if (!subscribedChannels.includes(channel)) newChannels.push(channel);
        for (const cg of channelGroups)
            if (bookEntry(this._channelGroups, cg, timeoutMs))
                if (!subscribedChannelGroups.includes(cg)) newChannelGroups.push(cg);
        const channelsCopy = [...channels];
        const channelGroupsCopy = [...channelGroups];
        if (newChannelGroups.length + newChannels.length !== 0) {
            console.debug('[pubnub] subscribing', {
                newChannels,
                newChannelGroups,
                subscribedChannels,
                subscribedChannelGroups,
            });
            this._pubnub.subscribe({
                channels: newChannels,
                channelGroups: newChannelGroups,
            });
        }
        this._wakeUpLoop();
        return () => this._cancel(channelsCopy, channelGroupsCopy);
    }

    subscribe<T = unknown>(type: string, callback: MessageCallback<T>): () => void {
        if (this._callbacks[type] === undefined) {
            this._callbacks[type] = [callback as MessageCallback];
        } else {
            this._callbacks[type].push(callback as MessageCallback);
        }

        return () => this._unsubscribe(type, callback);
    }

    dispose() {
        console.debug('[pubnub] disposing');
        this._pubnub.removeListener({
            message: this._handleMessage,
        });
    }

    private _unsubscribe<T>(type: string, callback: MessageCallback<T>): void {
        if (this._callbacks[type]) {
            const idx = this._callbacks[type].indexOf(callback as MessageCallback);
            if (idx !== -1) {
                this._callbacks[type].splice(idx, 1);
            }
        }
    }

    private _cancel(channels: string[], channelGroups: string[]): void {
        for (const channel of channels) cancelEntry(this._channels, channel);
        for (const cg of channelGroups) cancelEntry(this._channelGroups, cg);
        this._wakeUpLoop();
    }

    private init() {
        if (this._initialized || !this._pubnub) return;
        console.debug('[pubnub] initializing channels controller');

        // unsub from all channels in 5s if no one reserves connection to them
        for (const channel of this._pubnub.getSubscribedChannels())
            this._channels[channel] = {
                counter: 0,
                unsubscribeDeadline: Date.now() + 10000,
                timeoutMs: 0,
            };
        for (const channel of this._pubnub.getSubscribedChannelGroups())
            this._channelGroups[channel] = {
                counter: 0,
                unsubscribeDeadline: Date.now() + 10000,
                timeoutMs: 0,
            };

        this._wakeUpLoop();
        console.debug('[pubnub] added listener');
        this._pubnub.addListener({
            message: this._handleMessage,
        });
        this._initialized = true;
    }

    private _event: any;

    private _handleMessage(event: Pubnub.MessageEvent) {
        if (event.timetoken === this._event) {
            console.warn('[pubnub] receving the same event twice');
        }
        this._event = event.timetoken;

        try {
            console.debug('[pubnub] message', event.message, event.timetoken);
            let messageObj: any;

            if (typeof event.message === 'object') {
                messageObj = toCamelCase(event.message);
            } else if (typeof event.message === 'string') {
                messageObj = toCamelCase(JSON.parse(event.message));
            } else {
                throw new Error(`unexpected message type: ${typeof event.message}`);
            }

            if (messageObj === null) throw new Error('unexpected message object: null');

            if (isMessageWrapper(messageObj)) {
                this._handleValidMessage({
                    event,
                    message: messageObj,
                });
            } else {
                throw new Error(`unexpected message structure`, messageObj);
            }
        } catch (e) {
            console.error('[pubnub] failed to parse message', e);
        }
    }

    private _handleValidMessage(wrappedEvent: MessageEvent<unknown>) {
        const callbacks = this._callbacks[wrappedEvent.message.type];
        if (callbacks === undefined) return;
        console.debug(
            `[pubnub] executing ${callbacks.length} callbacks for message type ${wrappedEvent.message.type}`
        );
        for (const callback of callbacks) {
            try {
                callback(wrappedEvent);
            } catch (e) {
                console.error(
                    `[pubnub] failed to handle message, type:${wrappedEvent.message.type}`,
                    wrappedEvent,
                    e
                );
            }
        }
    }

    private _wakeUpLoop() {
        console.debug('[pubnub] [unsub-loop] woke up');
        clearTimeout(this._unsubLoopTimeout);
        this._unsubLoopTimeout = this._startNextLoopIteration();
    }

    private _startNextLoopIteration(): number {
        const nextDeadline = this._getNextDeadline() ?? Date.now() + 600000; // wait for 600s if no deadlines are available
        const timeout = Math.max(0, nextDeadline - Date.now());
        console.debug('[pubnub] [unsub-loop] going to sleep for', timeout, 'ms');

        return setTimeout(() => {
            this._closeExpiredConnections();
        }, timeout) as unknown as number;
    }

    private _closeExpiredConnections() {
        const expiredChannels: string[] = [];
        const expiredChannelGroups: string[] = [];

        for (const channel of Object.keys(this._channels)) {
            const entry = this._channels[channel];
            if (entry.counter === 0 && entry.unsubscribeDeadline < Date.now()) {
                delete this._channels[channel];
                expiredChannels.push(channel);
            }
        }
        for (const channelGroup of Object.keys(this._channelGroups)) {
            const entry = this._channelGroups[channelGroup];
            if (entry.counter === 0 && entry.unsubscribeDeadline < Date.now()) {
                delete this._channelGroups[channelGroup];
                expiredChannelGroups.push(channelGroup);
            }
        }

        if (expiredChannelGroups.length + expiredChannels.length > 0) {
            console.debug('[pubnub] unsubscribing', { expiredChannelGroups, expiredChannels });
            this._pubnub.unsubscribe({
                channelGroups: expiredChannelGroups,
                channels: expiredChannels,
            });
        }
    }

    private _getNextDeadline(): number | undefined {
        let nextDeadline = Number.MAX_SAFE_INTEGER;
        for (const channel in this._channels)
            if (this._channels[channel].unsubscribeDeadline < nextDeadline)
                nextDeadline = this._channels[channel].unsubscribeDeadline;

        for (const cg in this._channelGroups)
            if (this._channelGroups[cg].unsubscribeDeadline < nextDeadline)
                nextDeadline = this._channelGroups[cg].unsubscribeDeadline;

        return nextDeadline === Number.MAX_SAFE_INTEGER ? undefined : nextDeadline;
    }
}

const ChannelsControllerContext = createContext<ChannelsController | null | undefined>(undefined);

export function ChannelsControllerProvider({ children }: React.PropsWithChildren<{}>) {
    const pn = usePubnubOptional();
    const controller = useMemo(() => {
        return pn ? new ChannelsController(pn) : null;
    }, [pn]);

    useEffect(() => () => controller?.dispose(), [controller]);

    return (
        <ChannelsControllerContext.Provider value={controller}>
            {children}
        </ChannelsControllerContext.Provider>
    );
}

type UsePubnubSubscriptionOptions = {
    channels?: string[];
    channelGroups?: string[];
    timeoutMs?: number;
    enabled?: boolean;
};

/**
 * Subscribe to the specified channels and channel groups.
 * If the subscription is already present, the timeout is increased.
 *
 * @param options Subscription options
 *
 * @returns Function to cancel the subscription.
 *
 * The function returned by `usePubnubSubscription` should be called
 * when the component is unmounted or when the subscription is not needed anymore.
 */
export function usePubnubSubscription({
    channels,
    channelGroups,
    timeoutMs,
    enabled = true,
}: UsePubnubSubscriptionOptions) {
    const controller = useContext(ChannelsControllerContext)!;
    const timeout = Math.max(timeoutMs ?? 0, 150); // set minimum timeout to avoid re-subscribing when input of the hook changes

    useEffect(() => {
        if (enabled && controller && (channels?.length ?? 0) + (channelGroups?.length ?? 0) !== 0) {
            const cancel = controller.book(channels ?? [], channelGroups ?? [], timeout);
            return cancel;
        }
    }, [channels, channelGroups, timeout, enabled, controller]);
}

export type UsePubnubListenerOptions = {
    types: string[];
    channels?: string[];
    channelGroups?: string[];
    listenerEnabled?: boolean;
    unsubscribeIfListenerDisabled?: boolean;
    unsubscribeTimeoutMs?: number;
} & (
    | {
          channels: string[];
          channelGroups?: string[];
      }
    | {
          channels?: string[];
          channelGroups: string[];
      }
);

function useTypes(types: string[]): string[] {
    const str = JSON.stringify(sortBy(types));

    return useMemo(() => JSON.parse(str), [str]);
}

export function usePubnubListener<T>(
    listener: MessageCallback<T>,
    options: UsePubnubListenerOptions
) {
    const controller = useContext(ChannelsControllerContext);

    useEffect(() => {
        // if controller is null that means pubnub has not
        // been initialized yet, if it is undefined - there is no PubnubProvider
        // and that is a problem
        if (controller === undefined) {
            console.warn('usePubnubListener is called without controller');
        }
    }, [controller]);

    const {
        unsubscribeIfListenerDisabled,
        listenerEnabled = true,
        unsubscribeTimeoutMs,
        channelGroups: _channelGroups,
        channels: _channels,
        types: typesArray,
    } = options ?? {};
    const types = useTypes(typesArray);

    const channelGroups = useDeepMemoStringArray(_channelGroups ?? []);
    const channels = useDeepMemoStringArray(_channels ?? []);

    usePubnubSubscription({
        channelGroups,
        channels,
        enabled: listenerEnabled || !unsubscribeIfListenerDisabled,
        timeoutMs: unsubscribeTimeoutMs,
    });

    const listenerRef = useRef<MessageCallback<T>>(listener);
    listenerRef.current = listener;

    useEffect(() => {
        if (listenerEnabled && controller) {
            const fn: MessageCallback<T> = (e) => listenerRef.current(e);
            console.debug(`[pubnub] subscribing to ${types}`);
            const callbacks = types.map((t) => controller.subscribe<T>(t, fn));
            return () => {
                console.debug(`[pubnub] unsubscribing from ${types}`);

                for (const cb of callbacks) {
                    cb();
                }
            };
        }
        return () => {};
    }, [listenerEnabled, types, controller]);
}
