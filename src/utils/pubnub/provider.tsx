import Pubnub from 'pubnub';
import { createContext, useContext } from 'react';

const PubnubContext = createContext<Pubnub | null>(null);

export function usePubnubOptional() {
    return useContext(PubnubContext);
}

export function PubnubProvider({
    children,
    client,
}: React.PropsWithChildren<{ client: Pubnub | null }>) {
    return <PubnubContext.Provider value={client}>{children}</PubnubContext.Provider>;
}
