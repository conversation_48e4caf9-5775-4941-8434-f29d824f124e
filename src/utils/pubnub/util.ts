import sortBy from 'lodash/sortBy';
import { useRef } from 'react';

function areArraysEqual(arr: string[], arr2: string[]) {
    if (arr.length !== arr2.length) return false;
    if (arr.length === 0) return true;

    const arrSorted = sortBy(arr);
    const arr2Sorted = sortBy(arr2);

    return arrSorted.every((x, i) => x === arr2Sorted[i]);
}

export function useDeepMemoStringArray(arr: string[]): string[] {
    const valueRef = useRef(arr);

    if (valueRef.current !== arr) {
        if (!areArraysEqual(arr, valueRef.current)) {
            valueRef.current = arr;
        }
    }

    return valueRef.current;
}
