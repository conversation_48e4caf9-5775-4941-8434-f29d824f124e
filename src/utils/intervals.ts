import { DateTime } from 'luxon';

export interface IInterval<T = DateTime> {
    start: T;
    end: T;
}

export type Minutes = number;

/**
 * Calculates a list intervals that are intersections of the given intervals and target
 * interval.
 * @param intervals intervas that need to be checked for intersections with the target interval
 * @param target target interval that will be checked against other intervals
 */
export function getIntersections(
    intervals: IInterval[],
    target: IInterval,
    minLength: number = -1
): IInterval[] {
    const intersections: IInterval[] = [];

    for (const i of intervals) {
        if (i.start <= target.end && i.end >= target.start) {
            const intersection: IInterval = {
                start: target.start > i.start ? target.start : i.start,
                end: target.end < i.end ? target.end : i.end,
            };
            if (
                minLength > 0 &&
                intersection.end.diff(intersection.start, 'minutes').minutes < minLength
            ) {
                continue;
            }

            if (+intersection.start === +intersection.end) continue;

            intersections.push(intersection);
        }
    }

    return intersections;
}
