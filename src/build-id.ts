import { setApplicationVersion } from 'utils/nr';

type BuildInfo = {
    BUILD_ID: string;
    BUILD_ISO_DATE: string;
    BUILD_TIMESTAMP?: number;
    APP_VERSION: string;
    GIT_COMMIT: string;
};

const info = __BUILD_INFO__;

const buildInfo: BuildInfo = {
    BUILD_ID: info.ID,
    BUILD_ISO_DATE: new Date(info.TS).toISOString(),
    BUILD_TIMESTAMP: info.TS,
    APP_VERSION: info.APP_VERSION,
    GIT_COMMIT: info.GIT_COMMIT,
};

console.debug('buildInfo', buildInfo);
setApplicationVersion(buildInfo.APP_VERSION);

export default buildInfo;
