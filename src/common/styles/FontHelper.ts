import { Colors } from './Colors';

/**
 * @deprecated use MUI theme object instead
 */
export const FontPrimary = (Header: Object, bold = false, color: string = Colors.Neutral8): any => {
    return {
        ...Header,
        color,
        fontFamily: 'Inter',
        fontWeight: bold ? 700 : 400,
    };
};

/**
 * @deprecated use MUI theme object instead
 */
export const FontSecondary = (
    Header: Object,
    bold = false,
    color: string = Colors.Neutral8
): any => {
    return {
        ...Header,
        color,
        fontFamily: 'Roboto',
        fontWeight: bold ? 700 : 400,
    };
};
