import { makeStyles } from '@mui/styles';
import { Colors } from './Colors';
import { FontPrimary, FontSecondary } from './FontHelper';
import { HeaderStyles } from './HeaderStyles';

export const useConfirmModalClasses = makeStyles((theme) => ({
    content: {
        width: 961,
        padding: '47px 86px 50px 86px',
    },
    title: {
        ...FontPrimary(HeaderStyles.H4_18px, true, Colors.Neutral7),
    },
    cancelBtn: {
        width: 164,
    },
    confirmBtn: {
        width: 227,
    },
    tooltipIcon: {
        width: 18,
        height: 18,
        display: 'float',
        background: Colors.Neutral4,
        alignItems: 'center',
        alignContent: 'center',
        borderRadius: '100%',
    },
    tooltipText: {
        ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Neutral7),
    },
}));
