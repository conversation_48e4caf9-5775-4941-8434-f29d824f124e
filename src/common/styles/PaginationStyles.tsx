import { makeStyles } from '@mui/styles';
import { Colors } from './Colors';

export const paginationStyles = makeStyles((theme) => ({
    root: {
        display: 'flex',
        justifyContent: 'center',
        width: '100%',
        margin: '30px 0 0 0',

        '& .MuiPagination-ul': {
            gap: 8,
        },
        '& .MuiPaginationItem-page:not(.Mui-selected)': {
            color: Colors.Neutral6,
        },
        '& .MuiPaginationItem-icon': {
            color: Colors.CM2,
        },
    },
}));
