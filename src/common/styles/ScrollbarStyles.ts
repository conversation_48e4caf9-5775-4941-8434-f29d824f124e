import { Colors } from './Colors';

const scrollbarSize = 6;

type ScrollbarOptions = {
    widerHorizontal?: boolean;
    size?: number;
    trackBackgroundColor?: string;
};

export const scrollbarStyle = (opts?: ScrollbarOptions) => ({
    '@supports ( -moz-appearance:none )': {
        scrollbarWidth: opts?.size && opts?.size > scrollbarSize ? 'auto' : 'thin',
    },

    '&::-webkit-scrollbar': {
        width: opts?.size ?? scrollbarSize,
        height: (opts?.size ?? scrollbarSize) * (opts?.widerHorizontal ? 2 : 1),
    },
    /* Track */
    '&::-webkit-scrollbar-track': {
        background: `${opts?.trackBackgroundColor ?? Colors.Neutral3} !important`,
        borderRadius: '50px',
    },

    /* Handle */
    '&::-webkit-scrollbar-thumb': {
        background: `${Colors.Neutral5} !important`,
        borderRadius: '50px',
        marginTop: '10%',
        marginBottom: '10%',

        '&:hover': {
            background: `${Colors.Neutral7} !important`,
        },
    },
});
