import Option from 'react-select/src/components/Option';
import { CheckBoxOption } from '../components/Inputs/Dropdown/SelectOptions/CheckBoxOption';
import { IconOption } from '../components/Inputs/Dropdown/SelectOptions/IconOption';
import { RadioOption } from '../components/Inputs/Dropdown/SelectOptions/RadioOption';
import { TextOption } from '../components/Inputs/Dropdown/SelectOptions/TextOption';
export enum OptionStyle {
    text,
    radio,
    checkbox,
    icons,
}

export const OptionElement = (style: OptionStyle): typeof Option => {
    if (style === OptionStyle.text) return TextOption;
    if (style === OptionStyle.radio) return RadioOption;
    if (style === OptionStyle.checkbox) return CheckBoxOption;
    if (style === OptionStyle.icons) return IconOption;
    else return TextOption;
};
