/**
 * @deprecated Use {@link Typography} instead
 */
export class HeaderStyles {
    public static readonly H1_34px = {
        fontStyle: 'normal',
        fontSize: '34px',
        lineHeight: '41.15px',
    };

    public static readonly H2_24px = {
        fontStyle: 'normal',
        fontSize: '24px',
        lineHeight: '29.05px',
    };
    public static readonly H3_21px = {
        fontStyle: 'normal',
        fontSize: '21px',
        lineHeight: '25.41px',
    };
    public static readonly H4_18px = {
        fontStyle: 'normal',
        fontSize: '18px',
        lineHeight: '21.78px',
    };
    public static readonly H5_14px = {
        fontStyle: 'normal',
        fontSize: '14px',
        lineHeight: '16.94px',
    };
    public static readonly H6_12px = {
        fontStyle: 'normal',
        fontSize: '12px',
        lineHeight: '14.52px',
    };
    public static readonly H7_11px = {
        fontStyle: 'normal',
        fontSize: '11px',
        lineHeight: '13.31px',
    };
    public static readonly H8_10px = {
        fontStyle: 'normal',
        fontSize: '10px',
        lineHeight: '12.1px',
    };
    public static readonly H9_9px = {
        fontStyle: 'normal',
        fontSize: '9px',
        lineHeight: '10.89px',
    };
    public static readonly H10_8px = {
        fontStyle: 'normal',
        fontSize: '8px',
        lineHeight: '9.68px',
    };
    public static readonly H11_7px = {
        fontStyle: 'normal',
        fontSize: '7px',
        lineHeight: '8.47px',
    };
    public static readonly H12_16px = {
        fontStyle: 'normal',
        fontSize: '16px',
        lineHeight: '19px',
    };
}
