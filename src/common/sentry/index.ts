import * as Sentry from '@sentry/react';
import 'overlayscrollbars/overlayscrollbars.css';
import { useEffect } from 'react';
import {
    createRoutesFromChildren,
    matchRoutes,
    useLocation,
    useNavigationType,
} from 'react-router-dom';

export const initSentry = () => {
    const getEnvironmentConfig = (): Sentry.BrowserOptions => {
        if (import.meta.env.VITE_NR_PROFILE === 'uat') {
            return {
                enabled: true,
                environment: 'Uat',
                sampleRate: 1,
                tracesSampleRate: 1,
            };
        } else if (import.meta.env.VITE_NR_PROFILE === 'production') {
            return {
                enabled: true,
                environment: 'Production',
                sampleRate: 0.1, // 10% of errors
                tracesSampleRate: 0.002, // 0.2% of transactions
            };
        } else {
            return {
                enabled: false,
            };
        }
    };

    Sentry.init({
        dsn: 'https://<EMAIL>/4508760571772928',
        release: import.meta.env.VITE_VERSION,
        integrations: [
            Sentry.captureConsoleIntegration({ levels: [/*'warn', */ 'error'] }),
            // See docs for support of different versions of variation of react router
            // https://docs.sentry.io/platforms/javascript/guides/react/configuration/integrations/react-router/
            Sentry.reactRouterV6BrowserTracingIntegration({
                useEffect,
                useLocation,
                useNavigationType,
                createRoutesFromChildren,
                matchRoutes,
            }),
        ],
        ...getEnvironmentConfig(),
    });
};

export default initSentry;
