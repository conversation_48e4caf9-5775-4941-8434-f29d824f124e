import { DateTime, IANAZone } from 'luxon';
import moment from 'moment';

export const GetTimePassed = (date: moment.MomentInput, translationFunc: Function) => {
    const today = moment();

    const seconds = today.diff(date, 'seconds');
    if (seconds < 2) {
        return translationFunc('timePassed.secondAgo');
    }
    if (seconds < 60) {
        return translationFunc('timePassed.xSecondsAgo', { seconds });
    }

    const minutes = today.diff(date, 'minutes');
    if (minutes < 2) {
        return translationFunc('timePassed.minuteAgo');
    }
    if (minutes < 60) {
        return translationFunc('timePassed.xMinutesAgo', { minutes });
    }

    const hours = today.diff(date, 'hours');
    if (hours < 2) {
        return translationFunc('timePassed.hourAgo');
    }
    if (hours < 24) {
        return translationFunc('timePassed.xHoursAgo', { hours });
    }

    const days = today.diff(date, 'days');
    if (days < 2) {
        return translationFunc('timePassed.dayAgo');
    }
    if (days <= 30) {
        return translationFunc('timePassed.xDaysAgo', { days });
    }

    return moment(date).format(translationFunc('dateFormats.longTimeAgo'));
};

export const GetOffsetLabel = (ianaName: string | undefined) => {
    const timeZone = new IANAZone(ianaName ?? '');
    if (!timeZone.isValid) {
        return '(INVALID TIMEZONE)';
    } else {
        const now = DateTime.now();
        const hrsOffset = timeZone.formatOffset(now.toMillis(), 'narrow');

        const timezoneShortName = timeZone.offsetName(now.toMillis(), {
            format: 'short',
            locale: 'en',
        });
        if (timezoneShortName.startsWith('GMT')) {
            return `(GMT${hrsOffset})`;
        } else {
            return `(GMT${hrsOffset} / ${timezoneShortName})`;
        }
    }
};
