export type FilterOption<T> = {
    id: FilterOptionId<T>;
    label: string;
};
export type FilterOptionId<T> = T;

type ArrayLengthMutationKeys = 'splice' | 'push' | 'pop' | 'shift' | 'unshift' | number;
type ArrayItems<T extends Array<any>> = T extends Array<infer TItems> ? TItems : never;
export type FixedLengthArray<T extends any[]> = Pick<
    T,
    Exclude<keyof T, ArrayLengthMutationKeys>
> & {
    [Symbol.iterator]: () => IterableIterator<ArrayItems<T>>;
};

export enum DayOfWeek {
    Sunday,
    Monday,
    Tuesday,
    Wednesday,
    Thursday,
    Friday,
    Saturday,
}
export interface ApiResult<T> {
    isSuccess: boolean;
    message?: string;
    data: T;
}
