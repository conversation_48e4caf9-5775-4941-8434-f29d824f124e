import { WhoAmIUser } from 'api/account';
import { ViewByCostOrderDto } from 'api/viewByCost';
import posthog, { PostHogConfig } from 'posthog-js';
import { PostHogProvider as PHProvider } from 'posthog-js/react';
import { useAppSelector } from 'store';
import { selectUser } from 'store/slices/user';

const postHogOptions: Partial<PostHogConfig> = {
    api_host: import.meta.env.VITE_POSTHOG_HOST,
    autocapture: false,
    rageclick: true,
    capture_pageview: false,
    capture_pageleave: false,
    ip: true,
};

export const PostHogProvider = ({ children }: React.PropsWithChildren<{}>) => {
    // Only initialize PostHog in production environment
    if (import.meta.env.VITE_NR_PROFILE !== 'production') return <>{children}</>;

    return (
        <PHProvider options={postHogOptions} apiKey={import.meta.env.VITE_POSTHOG_KEY}>
            {children}
        </PHProvider>
    );
};

export const usePostHog = () => {
    const user = useAppSelector(selectUser);

    const identifyPostHogUser = (user: Partial<WhoAmIUser> | null) => {
        if (import.meta.env.VITE_NR_PROFILE !== 'production') return;
        if (!user) return;
        posthog.identify(user.key, {
            email: user.name,
            name: user.displayName,
            jobTitle: user.jobTitle,
            role: user.role,
        });
    };

    const identifyPostHogCustomerFromRoData = (roData: ViewByCostOrderDto) => {
        if (import.meta.env.VITE_NR_PROFILE !== 'production') return;
        posthog.identify(roData.customerId ?? '', {
            email: roData.email ?? '',
            firstName: roData.firstName ?? '',
            lastName: roData.lastName ?? '',
            mobile: roData.mobilePhone ?? '',
        });
    };

    const registerPageView = (pageName: string) => {
        if (import.meta.env.VITE_NR_PROFILE !== 'production') return;
        posthog.capture('$pageview', { pageName });
    };

    const capturePosthogEvent = (eventName: string, properties?: Record<string, any>) => {
        if (import.meta.env.VITE_NR_PROFILE !== 'production') return;
        posthog.capture(eventName, properties);
    };

    const capturePosthogIdentifiedEvent = (eventName: string, properties?: Record<string, any>) => {
        if (import.meta.env.VITE_NR_PROFILE !== 'production') return;
        posthog.capture(eventName, {
            ...properties,
            distinctId: user!.key,
            userId: user!.id,
            email: user!.name,
            name: user!.displayName,
            jobTitle: user!.jobTitle,
            role: user!.role,
        });
    };

    return {
        identifyPostHogUser,
        identifyPostHogCustomerFromRoData,
        registerPageView,
        capturePosthogEvent,
        capturePosthogIdentifiedEvent,
    };
};
