import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import { Interweave } from 'interweave';
import { useMemo } from 'react';
import { Toaster, ToasterController, ToasterUpdate, useToastersController } from 'utils/toasters';

type ToasterFn = (
    text: string | React.ReactNode | null,
    title?: string | React.ReactNode,
    options?: ToasterOptions
) => Toaster;

type ToasterOptions = {
    duration?: number;
    id?: string;
    isDetachable?: boolean;
    closeable?: boolean;
    onClose?: () => void;
    data?: Record<string, unknown>;
    icon?: React.ReactNode;
};

export type UseToasters = {
    success: ToasterFn;
    warning: ToasterFn;
    danger: ToasterFn;
    info: ToasterFn;
    progress: ToasterFn;
    update(id: string, update: ToasterUpdate): void;
    dismiss(id: string): void;
    networkError(): void;
    forEach(fn: (toaster: Toaster) => void): void;
};

function NetworkErrorContent() {
    const { t } = useAppTranslation();

    return <Interweave content={t('toasters.networkErrorBody')} />;
}

const networkErrorContent = <NetworkErrorContent />;

function getExpiresAt(duration: number | undefined): number {
    duration ??= 3000;
    return Number.isFinite(duration) ? Date.now() + duration : -1;
}

export function createToastersFromController(
    controller: ToasterController,
    t: TFunction
): UseToasters {
    return {
        progress: (text, title, { id, duration: _, ...options } = {}) => {
            return controller.createToaster(id, {
                title,
                content: text,
                expiresAt: -1,
                preset: 'progress',
                ...options,
            });
        },
        success: (text, title, { id, duration, ...options } = {}) => {
            return controller.createToaster(id, {
                title,
                content: text,
                expiresAt: getExpiresAt(duration),
                preset: 'success',
                ...options,
            });
        },
        warning: (text, title, { id, ...options } = {}) => {
            return controller.createToaster(id, {
                title,
                content: text,
                expiresAt: getExpiresAt(options?.duration),
                preset: 'warning',
                ...options,
            });
        },
        info: (text, title, { id, duration, ...options } = {}) => {
            return controller.createToaster(id, {
                title,
                content: text,
                expiresAt: getExpiresAt(duration),
                preset: 'info',
                ...options,
            });
        },
        danger: (text, title, { id, duration, ...options } = {}) => {
            return controller.createToaster(id, {
                title,
                content: text,
                expiresAt: getExpiresAt(duration ?? 10000),
                preset: 'danger',
                ...options,
            });
        },
        update: (id, update) => controller.update(id, update),
        dismiss: (id) => controller.dismiss(id),
        networkError: () =>
            controller.createToaster('NetworkError', {
                title: t('toasters.networkErrorTitle'),
                preset: 'network',
                content: networkErrorContent,
                expiresAt: Date.now() + 10000,
            }),
        forEach: (fn) => {
            controller.forEach(fn);
        },
    };
}

export default function useToasters(): UseToasters {
    const controller = useToastersController();
    const { t } = useAppTranslation();

    return useMemo(() => createToastersFromController(controller, t), [controller, t]);
}
