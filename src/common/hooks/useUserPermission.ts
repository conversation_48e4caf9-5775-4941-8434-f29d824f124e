import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUserPermission } from 'store/slices/user/selectors';

export const useUserPermission = () => {
    const userPermission = useSelector(selectUserPermission);

    return useMemo(
        () => ({
            shouldReceiveUploadNotifications: userPermission.shouldReceiveUploadNotifications,
            allowEditEstimates: userPermission.allowEditEstimates,
            settingsAccess: userPermission.settingsAccess,
            allowShowEstimates: userPermission.allowShowEstimates,
            allowGenerateReports: userPermission.allowGenerateReports,
            allowSeeAppointments: userPermission.allowSeeAppointments,
            allowEditAppointments: userPermission.allowEditAppointments,
            allowSeeAllOrders: userPermission.allowSeeAllOrders,
            allowEditOrders: userPermission.allowEditOrders,
            allowSeeAllConversations: userPermission.allowSeeAllConversations,
            allowEditWorkshopPlanner: userPermission.allowEditWorkshopPlanner,
            allowSeeAllJobs: userPermission.allowSeeAllJobs,
            allowShowJobs: userPermission.allowShowJobs,
            allowManageJobs: userPermission.allowManageJobs,
            allowReopenOrders: userPermission.allowReopenOrders,
            allowEditVehicles: userPermission.allowEditVehicles,
            allowEditCustomers: userPermission.allowEditCustomers,
            allowSeeTasks: userPermission.allowSeeTasks,
            allowSeeAllTasks: userPermission.allowSeeAllTasks,
            allowEditTasks: userPermission.allowEditTasks,
            allowSendMassiveSendings: userPermission.allowSendMassiveSendings,
        }),
        [userPermission]
    );
};
