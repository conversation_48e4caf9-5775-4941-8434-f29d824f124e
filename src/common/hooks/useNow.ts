import moment from 'moment';
import { useEffect, useRef } from 'react';
import useForceRender from './useForceRender';

export default function useNow(
    granularity: moment.unitOfTime.StartOf = 'm',
    refreshInterval = 60000
) {
    const now = useRef(new Date());
    const fr = useForceRender();

    useEffect(() => {
        const interval = setInterval(() => {
            const newNow = moment();

            if (!newNow.isSame(now.current, granularity)) {
                now.current = newNow.toDate();
                fr();
            }
        }, refreshInterval);
        return () => clearInterval(interval);
    }, [fr, granularity, refreshInterval]);
    return now.current;
}
