import { useEffect, useState } from 'react';

export function useWindowDimension() {
    const [dimension, setDimension] = useState({
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
    });
    useEffect(() => {
        const debouncedResizeHandler = debounce(() => {
            setDimension({ windowWidth: window.innerWidth, windowHeight: window.innerHeight });
        }, 100);
        window.addEventListener('resize', debouncedResizeHandler);
        return () => window.removeEventListener('resize', debouncedResizeHandler);
    }, []);
    return dimension;
}

function debounce(fn: any, ms: number) {
    let timer: number | undefined;
    return (...args: any[]) => {
        clearTimeout(timer);
        timer = setTimeout((_: any) => {
            timer = undefined;
            fn.apply(window, args);
        }, ms);
    };
}
