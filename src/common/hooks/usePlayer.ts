import { useCallback, useEffect, useRef, useState } from 'react';

type UsePlayerProps = {
    element: HTMLMediaElement | null;
    playing: boolean;
    onPause?: () => void;
    onPlayError?: () => void;
    initiallyInLoadingState?: boolean;
};

export function usePlayer({
    element,
    playing,
    onPause,
    onPlayError,
    initiallyInLoadingState,
}: UsePlayerProps) {
    const [duration, setDuration] = useState(0);
    const [currentTime, setCurrentTime] = useState(0);
    const [loading, setLoading] = useState(initiallyInLoadingState ?? false);
    const [error, setError] = useState<MediaError | null>(null);

    const onPauseRef = useRef(onPause);
    onPauseRef.current = onPause;

    const onPlayErrorRef = useRef(onPlayError);
    onPlayErrorRef.current = onPlayError;

    const elementRef = useRef(element);
    elementRef.current = element;

    useEffect(() => {
        let intervalId: number = 0;
        if (playing && element) {
            intervalId = setInterval(() => {
                setCurrentTime(element.currentTime ?? 0);
            }, 1000 / 30);

            return () => clearInterval(intervalId);
        }
    }, [playing, element]);

    useEffect(() => {
        if (!element) return;

        if (playing && element.paused) {
            element.play().catch((err: DOMException) => {
                if (err.name !== 'AbortError') {
                    console.error(`Couldn't play media element: ${err.message}`);
                    onPlayErrorRef.current && onPlayErrorRef.current();
                }
            });
        } else {
            element.pause();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [playing, element]);

    useEffect(() => {
        if (!element) return;

        const handleEnd = () => {
            setCurrentTime(0);
            if (elementRef.current) {
                elementRef.current.currentTime = 0;
            }
            onPauseRef.current && onPauseRef.current();
        };

        const handleLoadedMetadata = () => {
            if (!element) {
                console.error('received loadedmetadata event but media element is null');
                return;
            }
            setDuration(element.duration);
        };

        const handleLoadStart = () => {
            setLoading(true);
        };

        const handleCanPlay = () => {
            setLoading(false);
        };

        const handleError = () => {
            setError(element.error);
            setLoading(false);
        };

        element.addEventListener('ended', handleEnd);
        element.addEventListener('loadedmetadata', handleLoadedMetadata);
        element.addEventListener('loadstart', handleLoadStart);
        element.addEventListener('canplay', handleCanPlay);
        element.addEventListener('error', handleError);

        if (element.readyState >= HTMLMediaElement.HAVE_METADATA) {
            handleLoadedMetadata();
        }

        return () => {
            element.removeEventListener('ended', handleEnd);
            element.removeEventListener('loadedmetadata', handleLoadedMetadata);
            element.removeEventListener('loadstart', handleLoadStart);
            element.removeEventListener('canplay', handleCanPlay);
            element.removeEventListener('error', handleError);

            element.pause();
        };
    }, [element]);

    const changeCurrentTime = useCallback(
        (newTime: number) => {
            if (element) {
                setCurrentTime(newTime);
                element.currentTime = newTime;
            }
        },
        [element]
    );

    return {
        currentTime,
        setCurrentTime: changeCurrentTime,
        duration: Number.isNaN(duration) ? currentTime : duration,
        loading,
        error,
    };
}
