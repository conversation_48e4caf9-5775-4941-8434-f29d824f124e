import { useRef } from 'react';
import useDocumentVisibleCallback from './useDocumentVisibleCallback';
import useInterval from './useInterval';

/**
 * Calls the provided function at the specified interval, but only
 * if the document is active.
 *
 * @param fn The function to call at the specified interval.
 * @param ms The number of milliseconds to wait between each
 *           call to the provided function.
 * @param callOnActivation Whether to call the provided function
 *                          when the document becomes active. Defaults
 *                          to `true`.
 */
export default function useIntervalIfDocumentActive(
    fn: () => void,
    ms: number,
    callOnActivation: boolean = true
) {
    const ref = useRef<boolean | null>(null);

    useDocumentVisibleCallback((active) => {
        if (ref.current !== null && active === ref.current) return;

        ref.current = active;
        if (active && callOnActivation) {
            fn();
        }
    });

    useInterval(() => {
        if (ref.current) fn();
    }, ms);
}
