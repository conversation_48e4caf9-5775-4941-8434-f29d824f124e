import { useEffect, useState } from 'react';

type UseLockedBodyOutput = [boolean, (locked: boolean) => void];

// https://stackoverflow.com/questions/13382516/getting-scroll-bar-width-using-javascript
function getScrollbarWidth() {
    // Creating invisible container
    const outer = document.createElement('div');
    outer.style.visibility = 'hidden';
    outer.style.overflow = 'scroll'; // forcing scrollbar to appear
    document.body.appendChild(outer);

    // Creating inner element and placing it in the container
    const inner = document.createElement('div');
    outer.appendChild(inner);

    // Calculating difference between container's full width and the child width
    const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;

    // Removing temporary elements from the DOM
    outer.parentNode!.removeChild(outer);

    return scrollbarWidth;
}

let __scrollbarWidth: number | undefined = undefined;
function getScrollbarWidthLazy() {
    if (__scrollbarWidth === undefined) __scrollbarWidth = getScrollbarWidth();
    return __scrollbarWidth;
}

type UseLockedBodyOptions = {
    rootId?: string;
    restoreOverflow?: boolean;
};

const lockedBodyData = {
    counter: 0,
    overflow: '',
    paddingRight: '',
    hasVerticalScroll: false,
    scrollbarWidth: 0,
};

// https://usehooks-ts.com/react-hook/use-locked-body
function useLockedBody(
    initialLocked = false,
    { rootId, restoreOverflow }: UseLockedBodyOptions = {}
): UseLockedBodyOutput {
    const [locked, setLocked] = useState(initialLocked);

    // Do the side effect before render
    useEffect(() => {
        if (!locked) {
            return;
        }

        lockedBodyData.counter++;

        if (lockedBodyData.counter === 1) {
            // Save initial body style
            lockedBodyData.overflow = document.body.style.overflow;
            lockedBodyData.paddingRight = document.body.style.paddingRight;

            const scrollbarWidth = getScrollbarWidthLazy();
            lockedBodyData.hasVerticalScroll = document.body.scrollHeight > window.innerHeight;

            // Lock body scroll
            document.body.style.overflow = 'hidden';

            if (lockedBodyData.hasVerticalScroll) {
                document.body.style.marginRight = `${scrollbarWidth}px`;
                document.body.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`);
            }

            // Get the scrollBar width
            const root = document.getElementById(rootId ?? 'root'); // or root
            lockedBodyData.scrollbarWidth = root ? root.offsetWidth - root.scrollWidth : 0;

            // Avoid width reflow
            if (lockedBodyData.scrollbarWidth) {
                document.body.style.paddingRight = `${lockedBodyData.scrollbarWidth}px`;
            }
        }

        return () => {
            lockedBodyData.counter--;
            if (lockedBodyData.counter > 0) return;

            const newOverflow = restoreOverflow ? lockedBodyData.overflow : 'initial';
            document.body.style.overflow = newOverflow;
            if (lockedBodyData.hasVerticalScroll) document.body.style.marginRight = '0px';

            if (lockedBodyData.scrollbarWidth) {
                document.body.style.paddingRight = lockedBodyData.paddingRight;
            }
        };
    }, [locked, rootId, restoreOverflow]);

    // Update state if initialValue changes
    useEffect(() => {
        if (locked !== initialLocked) {
            setLocked(initialLocked);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialLocked]);

    return [locked, setLocked];
}

export default useLockedBody;
