import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import useURLSearchParams from './useURLSearchParams';

type ParamSetter = (value: string | null, replace?: boolean) => void;

export default function useQueryParam(name: string): [string | null, ParamSetter] {
    const params = useURLSearchParams();
    const navigate = useNavigate();
    const value = params.get(name);
    const location = useLocation();

    const setter: ParamSetter = useCallback(
        (value: string | null, replace?: boolean) => {
            const currentParams = new URLSearchParams(window.location.search);
            if (value === null) currentParams.delete(name);
            else currentParams.set(name, value);
            navigate(
                {
                    pathname: location.pathname,
                    search: '?' + currentParams.toString(),
                },
                {
                    replace,
                }
            );
        },
        [location.pathname, name, navigate]
    );

    return [value, setter];
}
