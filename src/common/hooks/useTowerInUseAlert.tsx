import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Dispatch, useCallback, useMemo } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';

// TODO (MB) move to useToasters
const useTowerAlert = () => {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const createNotification = useCallback(
        (dispatch: Dispatch<any>) => {
            return (orderNumber: string) => {
                const notification: NotificationData = new NotificationData(
                    '',
                    t('towerAlert.towerInUse'),
                    NotificationType.warning
                );

                notification.bodyElement = () => (
                    <div>
                        {`${t('towerAlert.theTowerNumberIsBeingUsedByOrder')} `}
                        <strong>#{orderNumber}.</strong>
                    </div>
                );
                dispatch(setNewToaster(notification));
            };
        },
        [t]
    );

    return useMemo(
        () => ({
            showTowerInUseAlert: createNotification(dispatch),
        }),
        [createNotification, dispatch]
    );
};

export default useTowerAlert;
