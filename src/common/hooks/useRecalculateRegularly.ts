import { useEffect, useRef } from 'react';
import useForceRender from './useForceRender';

export default function useRecalculateRegularly<T>(fn: () => T, interval: number) {
    const fnRef = useRef<() => T>(fn);
    fnRef.current = fn;
    const fr = useForceRender();

    const value = useRef<{ v: T } | null>(null);
    if (value.current === null) value.current = { v: fn() };

    useEffect(() => {
        const intervalId = setInterval(() => {
            value.current = { v: fnRef.current() };
            fr();
        }, interval);
        return () => clearInterval(intervalId);
    }, [interval, fr]);

    return value.current.v;
}
