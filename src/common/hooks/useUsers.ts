import { UserListItem } from 'api/users';
import { useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectEnterpriseUsers } from 'store/slices/enterprise/users/selectors';
import { selectIsEnterprise } from 'store/slices/globalSettingsSlice';
import { selectUsers as selectShopUsers } from 'store/slices/users';

const selectUsers = createSelector(
    [selectShopUsers, selectEnterpriseUsers, selectIsEnterprise, (_, shopId?: string) => shopId],
    (shopUsers, enterpriseUsers, isEnterprise, shopId) => {
        let users: UserListItem[] = [];

        if (isEnterprise) {
            if (shopId) {
                const foundUsers = enterpriseUsers.find((x) => x.shopId === shopId)?.users;
                if (foundUsers) {
                    users = foundUsers;
                }
            } else {
                enterpriseUsers.forEach((x) => (users = [...users, ...x.users]));
            }
        } else {
            usersRecordToList(shopUsers, users);
        }

        return users;
    }
);

function usersRecordToList(users: Record<string, UserListItem>, list: UserListItem[]) {
    for (const id in users) {
        list.push(users[id]);
    }
}

export function useUsers(shopId?: string) {
    const users = useAppSelector((s) => selectUsers(s, shopId));

    return useMemo(() => [...users], [users]);
}
