import { debounce } from 'lodash';
import { useEffect, useRef } from 'react';
import useForceRender from './useForceRender';

const isElementInViewport = (el: HTMLElement) => {
    const rect = el.getBoundingClientRect();
    const css = window.getComputedStyle(el, null);
    const headerHeight = parseFloat(css.getPropertyValue('--header-height'));

    return (
        rect.top - headerHeight > -1 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
};

const useIsInBounds = (el?: HTMLElement | null) => {
    const inBoundsRef = useRef(true);
    const fr = useForceRender();

    useEffect(() => {
        if (!el) return () => {};

        const update = () => {
            const inBounds = isElementInViewport(el);
            if (inBounds !== inBoundsRef.current) {
                inBoundsRef.current = inBounds;
                fr();
            }
        };
        update();
        const callback = debounce(update, 10);

        document.addEventListener('scroll', callback);
        return () => document.removeEventListener('scroll', callback);
    }, [el, fr]);

    return inBoundsRef.current;
};

export default useIsInBounds;
