import { useCallback, useEffect, useState } from 'react';

export const useClickedOutside = (myRef: any) => {
    const [clickedOutside, setClickedOutside] = useState(true);

    const handleClickOutside = (e: any) => {
        if (!myRef.current.contains(e.target)) {
            console.debug('Click outside element: ', myRef.current);
            setClickedOutside(true);
        } else {
            console.debug('Click inside element: ', myRef.current);
            setClickedOutside(false);
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    });
    return [clickedOutside];
};

export function useClickedOutsideCallback(
    element: HTMLElement | undefined | null,
    callback: () => void
) {
    const callback2 = useCallback(
        (e: MouseEvent) => {
            if (element && e.target instanceof HTMLElement && !e.target.contains(element)) {
                callback();
            }
        },
        [element, callback]
    );

    useEffect(() => {
        if (!element) return () => {};
        document.addEventListener('mousedown', callback2);
        return () => document.removeEventListener('mousedown', callback2);
    }, [callback2]);
}
