import React, { useState } from 'react';
import { ChangeEvent } from '../components/Inputs/IInput';

/**
 * @deprecated don't be lazy and just use normal state, ok?
 */
export const useInputValue = <S = undefined>(
    value: S | (() => S)
): [S, (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | S) => void] => {
    const [state, setState] = useState<any>(value);
    const setStateHandler = (event: ChangeEvent | any) => {
        if (event?.target && event?.target?.value != state) {
            setState(event.target.value ?? '');
        } else if (event != state) {
            setState(event);
        }
    };

    return [state, setStateHandler];
};
