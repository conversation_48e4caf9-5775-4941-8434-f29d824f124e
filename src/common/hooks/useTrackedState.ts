import { useCallback, useMemo, useRef, useState } from 'react';

type Set<T> = T | ((old: T) => T);

function _set<T>(old: T, s: Set<T>): T {
    // @ts-ignore
    if (typeof s === 'function') return s(old);
    return s;
}

export type UseTrackedState<T> = {
    state: T;
    originalState: T;
    resetToOriginal: () => void;
    setOriginalState: (value: T) => void;
    changed: boolean;
    getChanges: () => Partial<T>;
    fieldChanged: (key: keyof T) => boolean;
    set: <K extends keyof T>(k: K, v: Set<T[K]>) => void;
    update: (update: Partial<T>) => void;
    resetToDefault: () => void;
};

type ChangedMap<T> = Partial<Record<keyof T, boolean>>;

function isSomethingChanged(map: Record<any, any>): boolean {
    for (const key in map) {
        if (map[key]) return true;
    }
    return false;
}

/**
 * Checks if something changed in an object based on its change map, but excludes some properties from the check.
 * @param changed a map of property names to boolean values indicating if property was changed
 * @param exceptions a list property names exempt from the check
 * @returns true or false depending on whether or not something changed
 */
export function isSomethingChangedWithExceptions<T>(
    changed: ChangedMap<T>,
    exceptions: (keyof T)[]
) {
    for (const key in changed) {
        if (exceptions.includes(key)) continue;
        if (changed[key]) return true;
    }
    return false;
}

export type ChangeDetector<T> = (originalState: T, state: T, changesMap: ChangedMap<T>) => boolean;

function defaultChangeDetector<T>(
    _originalState: T,
    _currentState: T,
    changes: Record<string, any>
): boolean {
    return isSomethingChanged(changes);
}

export default function useTrackedState<T extends {}>(
    defaultState: T,
    changeDetector: ChangeDetector<T> = defaultChangeDetector
): UseTrackedState<T> {
    const [fr, setFr] = useState(0);
    const originalRef = useRef<T>(defaultState);
    const newState = useRef<T>({ ...defaultState });
    const changedMap = useRef<Partial<Record<keyof T, boolean>>>({});

    const set = useCallback(<K extends keyof T>(k: K, v: Set<T[K]>) => {
        // @ts-ignore
        newState.current![k] = _set(newState.current![k], v);
        changedMap.current[k] = newState.current![k] !== originalRef.current[k];
        setFr((v) => v + 1);
    }, []);

    return useMemo(
        () => ({
            state: { ...(newState.current ?? originalRef.current) },
            originalState: originalRef.current,
            setOriginalState: (value: T) => {
                originalRef.current = value;
                changedMap.current = {};
                newState.current = { ...value };
                setFr((v) => v + 1);
            },
            resetToOriginal: () => {
                changedMap.current = {};
                newState.current = { ...originalRef.current };
                setFr((v) => v + 1);
            },
            getChanges: () => {
                const changedState: Partial<T> = {};

                for (const k in newState.current) {
                    if (changedMap.current[k]) changedState[k] = newState.current[k];
                }

                return changedState;
            },
            changed: changeDetector(originalRef.current, newState.current, changedMap.current),
            fieldChanged: (key) => changedMap.current[key] ?? false,
            set,
            update: (upd) => {
                for (const k in upd) {
                    // @ts-ignore
                    newState.current![k as keyof T] = upd[k];
                    changedMap.current[k] = newState.current![k] !== originalRef.current[k];
                }
                setFr((v) => v + 1);
            },
            resetToDefault: () => {
                newState.current = { ...defaultState };
                originalRef.current = { ...defaultState };
                setFr((v) => v + 1);
            },
        }),
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [fr]
    );
}
