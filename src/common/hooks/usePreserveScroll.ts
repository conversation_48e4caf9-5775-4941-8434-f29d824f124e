import { RefObject, useEffect, useLayoutEffect } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { uiActions } from 'store/slices/ui';
import { selectElementScroll } from '../../store/slices/ui/selectors';

/**
 * Saves the scroll position of a DOM node in Redux and restores it on mount.
 *
 * @param key   A unique key per view (e.g. route pathname)
 * @param ref   Ref to the scroll container (<ElementContainer/>)
 */
export const usePreserveScroll = (key: string, ref: RefObject<HTMLElement>) => {
    const dispatch = useAppDispatch();
    const saved = useAppSelector((state) => selectElementScroll(state, key));

    useLayoutEffect(() => {
        if (ref.current) {
            ref.current.scrollTop = saved;
        }
    }, [saved, ref]);

    /* ---------- Save on scroll ------------------------------------------- */
    useEffect(() => {
        const node = ref.current;
        if (!node) return;

        const onScroll = () => {
            // `requestAnimationFrame` avoids dispatch-spam
            requestAnimationFrame(() =>
                dispatch(uiActions.setElementScroll({ key, position: node.scrollTop }))
            );
        };

        node.addEventListener('scroll', onScroll);

        return () => node.removeEventListener('scroll', onScroll);
    }, [dispatch, key, ref]);
};
