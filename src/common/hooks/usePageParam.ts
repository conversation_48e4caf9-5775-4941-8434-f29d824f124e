import { useCallback } from 'react';
import useQueryParam from './useQueryParam';

type PageSetter = (page: number, replace?: boolean) => void;

export default function usePageParam(name: string = 'page'): [number, PageSetter] {
    const [pageStr, setPageStr] = useQueryParam(name);
    const page = pageStr === null || isNaN(+pageStr) ? 1 : +pageStr;

    const setter: PageSetter = useCallback(
        (value: number) => {
            value = Math.floor(Math.max(1, value));
            if (value === page) {
                return;
            }

            if (value === 1) {
                if (pageStr !== null && isNaN(+pageStr)) {
                    // we have some nonsense in page parameter
                    setPageStr(null, true); // replace current history entry
                } else if (pageStr !== null) {
                    // we have a value in URL but it's not 1
                    setPageStr(value + '');
                }
                // if pageStr is null and new value is 1 then we don't have to do anything
            } else {
                setPageStr(value + '');
            }
        },
        [name, setPageStr, page]
    );

    return [page, setter];
}
