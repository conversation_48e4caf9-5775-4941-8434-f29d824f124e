import { TFunction } from 'i18next';
import React, { createContext, useContext, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { TerminologyDto } from 'store/slices/terminologies';
import { selectTerminologies } from 'store/slices/terminologies/selectors';

type TerminologyParams = {
    localization: string;
    terminology: string;
    caseType: CaseTypeEnum;
};

enum CaseTypeEnum {
    Upper,
    Lower,
    Title,
    Sentence,
    Default,
}

// NOTE (MB) we will use context for TFunction to avoid creating one Proxy object per each useAppTranslation call
const TFunctionContext = createContext<TFunction | null>(null);

/**
 * Create `Proxy` object that intercepts translated text and replaces all terminologies appropriately
 * @param tFunction original t function from i18next
 * @param terminologies list of terminologies to replace
 * @returns `Proxy` object that intercepts translated text and replaces all terminologies appropriately
 */
function createTFunctionProxy(tFunction: TFunction, terminologies: TerminologyDto[]): TFunction {
    if ((tFunction as { _isProxy?: true })._isProxy === true) {
        return tFunction;
    }

    // https://caniuse.com/?search=Proxy - basically full support since 2016
    return new Proxy(tFunction, {
        get(target, p) {
            if (p === '_isProxy') return true;
            return (target as TFunction)[p as keyof TFunction];
        },

        apply(target, thisArg, argArray) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            let localization = target.apply(thisArg, argArray as any);

            const matchArray =
                localization
                    .match(/\[(Title|Upper|Lower|Sentence|Default):\w+\]/g)
                    ?.toString()
                    .split(',')
                    .map((x) => parseLocalizationParams(x)) ?? [];

            const swapArray =
                matchArray.map((x) => {
                    const swapValue = terminologies.find(
                        (t) => t.type.toLowerCase() === x.terminology.toLowerCase()
                    );

                    if (
                        swapValue?.alias !== undefined &&
                        swapValue?.alias !== null &&
                        swapValue?.alias !== ''
                    ) {
                        return {
                            term: swapValue?.alias,
                            omitCaseChanging: true,
                        };
                    } else {
                        return {
                            term: swapValue?.originalTitle,
                            omitCaseChanging: false,
                        };
                    }
                }) ?? [];

            matchArray.forEach((matchValue, index) => {
                const swapValue = swapArray[index];
                if (swapValue && swapValue.term) {
                    localization = localization.replace(
                        matchValue.localization,
                        swapValue.omitCaseChanging
                            ? swapValue.term
                            : changeCase(swapValue.term, matchValue.caseType)
                    );
                }
            });

            return localization;
        },
    });
}

/**
 * Fully transparent version of useTranslation that returns modified version
 * of t function for auto-replacing terminologies
 */
export function useAppTranslation(): ReturnType<typeof useTranslation> {
    const result = useTranslation();
    const t = useContext(TFunctionContext);
    if (t === null) throw new Error('TFunction replacement is missing');
    return {
        ...result,
        t,
    };
}

export function TerminologiesTFunctionReplacementProvider({
    children,
}: React.PropsWithChildren<{}>) {
    const terminologies = useSelector(selectTerminologies);
    const { t: tOriginal } = useTranslation();

    const t = useMemo(
        () => createTFunctionProxy(tOriginal, terminologies),
        [terminologies, tOriginal]
    );

    return <TFunctionContext.Provider value={t}>{children}</TFunctionContext.Provider>;
}

function parseLocalizationParams(localizationParams: string): TerminologyParams {
    const caseType =
        localizationParams
            .replace(/\[|\]/g, '')
            .match(/.+?(?=:)/)
            ?.toString() ?? '';

    const terminology = localizationParams.replace(/\[|\]/g, '').replace(caseType.concat(':'), '');

    return {
        caseType: isKeyofCaseType(caseType) ? CaseTypeEnum[caseType] : CaseTypeEnum.Default,
        terminology: terminology,
        localization: localizationParams,
    };
}

function isKeyofCaseType(value: string): value is keyof typeof CaseTypeEnum {
    // type assertion: necessary to do type check here
    return CaseTypeEnum[value as keyof typeof CaseTypeEnum] !== undefined;
}

function changeCase(swapValue: string, caseType: CaseTypeEnum): string {
    return caseType === CaseTypeEnum.Upper
        ? swapValue.toUpperCase()
        : caseType === CaseTypeEnum.Lower
        ? swapValue.toLowerCase()
        : caseType === CaseTypeEnum.Title
        ? //TODO find better formatting
          swapValue
              .split(' ')
              .map((v) => {
                  return v.length < 4 ? v : capitalize(v);
              })
              .join(' ')
        : caseType === CaseTypeEnum.Sentence
        ? capitalize(swapValue)
        : swapValue;
}

function capitalize(value: string): string {
    return value.charAt(0).toUpperCase().concat(value.toLowerCase().slice(1));
}
