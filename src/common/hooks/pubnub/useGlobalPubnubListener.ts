import { CustomAppointmentReasonUpdatedDto } from 'api/appointmentReasons';
import { OrderTypeDto } from 'api/orders';
import { PUBNUB_CHANNELS } from 'api/pubnub';
import { UserListItem } from 'api/users';
import { useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { appointmentReasonsActions } from 'store/slices/appointmentReasons';
import { enterpriseUsersAction } from 'store/slices/enterprise/users';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { orderTypesActions } from 'store/slices/orderTypes';
import {
    initUserState,
    selectUserAuthorizationStatus,
    useCurrentUserOptional,
} from 'store/slices/user';
import { usersActions } from 'store/slices/users';
import { usePubnubListener, usePubnubSubscription } from 'utils/pubnub';

const useGlobalPubnubListener = () => {
    const dispatch = useDispatch();
    const user = useCurrentUserOptional();
    const gs = useAppSelector(selectSettings);
    const hasAuthorizedUser = useAppSelector(selectUserAuthorizationStatus) === true;

    const channels = useMemo(() => {
        const list: string[] = [];

        if (user) {
            list.push(PUBNUB_CHANNELS.user(user.key));

            if (gs.appMode === 'RepairShop') {
                list.push(PUBNUB_CHANNELS.shop(gs.uid));
            }
        }

        return list;
    }, [gs.appMode, gs.uid, user]);

    usePubnubSubscription({ channels });

    usePubnubListener<unknown>(
        async () => {
            dispatch(initUserState());
        },
        {
            channels: [],
            types: ['user.updated'],
            listenerEnabled: !!user,
        }
    );

    usePubnubListener<UserListItem>(
        ({ message: { payload } }) => {
            dispatch(usersActions.update(payload));
            dispatch(enterpriseUsersAction.update(payload));
        },
        {
            listenerEnabled: hasAuthorizedUser,
            types: ['user.other.updated'],
            channels: [],
        }
    );

    usePubnubListener<CustomAppointmentReasonUpdatedDto>(
        ({ message: { payload } }) => {
            dispatch(appointmentReasonsActions.updateCustomReasonFromPubnubEvent(payload));
        },
        {
            listenerEnabled: hasAuthorizedUser,
            types: ['appointment.reasons.custom.updated'],
            channels: [],
        }
    );

    // order type real-time updates
    usePubnubListener<OrderTypeDto>(
        ({ message: { payload } }) => {
            dispatch(orderTypesActions.update(payload));
        },
        {
            listenerEnabled: hasAuthorizedUser,
            types: ['orderType.updated'],
            channels: [],
        }
    );
};

export default useGlobalPubnubListener;
