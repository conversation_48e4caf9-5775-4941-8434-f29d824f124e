import { ENTERPRISE_ROUTES, ROUTES } from 'common/constants';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';

export const useAppRoutes = () => {
    const { appMode } = useSelector(selectSettings);
    const isEnterprise = appMode === 'Enterprise';

    const appRoutes = {
        LOGIN: isEnterprise ? ENTERPRISE_ROUTES.LOGIN : ROUTES.LOGIN,
        ORDERS: isEnterprise ? ENTERPRISE_ROUTES.ORDERS : ROUTES.ORDERS,
        ORDERS_DETAIL: isEnterprise ? ENTERPRISE_ROUTES.ORDERS_DETAIL : ROUTES.ORDERS_DETAIL,
        REPORTS: isEnterprise ? ENTERPRISE_ROUTES.REPORTS : ROUTES.REPORTS,
    };

    return { appRoutes };
};
