import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
const TAG_LOG = '[QueryParam]';
export type TypeFilter = 'string' | 'date' | 'number' | 'custom';
function validNumber(value: number) {
    return !isNaN(value) && !isFinite(value);
}

function queryToProperty(value: string | null, config: ConfigQueryParam): any {
    if (!value) return;
    const { type, stringToCustomType } = config;
    try {
        if (!config.type || config.type === 'string') return value;
        else if (type === 'custom' && stringToCustomType) return stringToCustomType(value);
        else if (type === 'number') {
            const valueNumber = parseFloat(value);
            if (validNumber(valueNumber)) return parseFloat(value);
        } else if (type === 'date') {
            const miliseconds = parseInt(value);
            if (validNumber(miliseconds)) return new Date(miliseconds);
        } else {
            console.error('[Error] Value not typed in queryToProperty');
        }
    } catch (error) {
        console.error(
            TAG_LOG,
            '[Error]',
            'When trying to convert queryToPropery',
            'Config:',
            config,
            'Value:',
            value
        );
    }
    return;
}

function propertyToQuery(
    value: string | number | Date | any,
    config: ConfigQueryParam
): string | void {
    const { type, customTypeToString } = config;
    if (!value) return;
    try {
        if (!config.type || config.type === 'string') return value;
        else if (type === 'custom' && customTypeToString) return customTypeToString(value);
        else if (type === 'number') return value.toString();
        else if (type === 'date') return (value as Date).getMilliseconds().toString();
    } catch (error) {
        console.error(
            TAG_LOG,
            '[Error]',
            'When trying to convert propertyToQuery',
            'Config:',
            config,
            'Value:',
            value
        );
    }
    return;
}

function getURLParams() {
    const currentURLParams = window.location.search.substring(1);
    return new URLSearchParams(currentURLParams);
}
/**
 * Use this hook if you need to modify the value of the query parameters of the url and if you need the history of those changes to be saved.
 * <AUTHOR> Calles
 * @param names Specify an array of the names of the URL parameters that you want to handle with this hook
 * @param saveHistory by default the value is true, if you need to manually manage the way it is saved in the history you can specify it as false.
 * @returns Returns an array with the parameters state and the setState to edit them
 */
export const useQueryParams = <T>(
    configs: ConfigQueryParam[],
    saveHistory: boolean = true
): [T | null, Dispatch<SetStateAction<T | null>>] => {
    const [params, setQuery] = useState<T | null>(null);
    const navigate = useNavigate();
    const [loadEvent, setLoadEvent] = useState(false);
    useEffect(() => {
        console.debug(TAG_LOG, 'Init useQueryParams');
        const query = getURLParams();
        const currentParams: any = {};
        for (const config of configs) {
            const queryName = config.alias ?? config.name;
            if (query.has(queryName)) {
                const value = query.get(queryName);
                console.debug(TAG_LOG, 'Load Params', config.name, ' from URL:', value);
                currentParams[config.name] = queryToProperty(value, config);
            } else {
                console.debug(TAG_LOG, 'Load Params', config.name, ' from URL: Default value');
                currentParams[config.name] = config.default;
            }
        }

        setQuery(currentParams);
        setLoadEvent(true);
        /** Load Current Params Values */
    }, []);

    useEffect(() => {
        console.debug(TAG_LOG, 'Update Params useQueryParams');
        if (loadEvent) return setLoadEvent(false);
        /** Save changes in History */
        const urlParams = getURLParams();

        const lastParams: any = params ? params : {};
        for (const config of configs) {
            const value: string | void = propertyToQuery(lastParams[config.name], config);
            const queryName = config.alias ?? config.name;
            console.debug('[QueryParam]', queryName, value);
            if (value) {
                urlParams.set(queryName, value);
            } else {
                //urlParams.delete(queryName);
            }
        }
        setQuery(lastParams);
        navigate({ search: '?' + urlParams.toString() });
        console.debug('[QueryParam]', 'Values update: ', urlParams.toString());
    }, [params]);

    return [params, setQuery];
};

export interface ConfigQueryParam {
    name: string;
    alias?: string;
    required?: boolean;
    type?: TypeFilter;
    stringToCustomType?: (queryValue: string) => any;
    customTypeToString?: (value: any) => string;
    default?: any;
}
