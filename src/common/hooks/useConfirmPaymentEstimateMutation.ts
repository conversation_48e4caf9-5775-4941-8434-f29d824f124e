import { useMutation } from '@tanstack/react-query';
import ViewByCostApi, { ViewByCostEstimateItemDto } from '../../api/viewByCost/index';
import { useAppTranslation } from './useAppTranslation';
import { usePostHog } from './usePostHog';
import useToasters from './useToasters';

type ConfirmPaymentEstimateValues = {
    repairOrderId: string;
    items: ViewByCostEstimateItemDto[];
};

const useConfirmPaymentEstimateMutation = (
    onSuccess: (_paymentUrl: string) => void,
    isPayingNow: boolean = false,
    postHogEvent: string = '',
    postHogData: Record<string, any> = {}
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { capturePosthogEvent } = usePostHog();

    const mutation = useMutation(
        ({ repairOrderId, items }: ConfirmPaymentEstimateValues) =>
            ViewByCostApi.confirmPaymentEstimate(repairOrderId, items, isPayingNow),
        {
            onSuccess: (paymentUrl) => {
                toasters.success(t('syncEstimate.estimateSuccessfullySynchronized'), '');
                onSuccess && onSuccess(paymentUrl);
                capturePosthogEvent(postHogEvent, postHogData);
            },
            onError: () => {
                toasters.danger(t('syncEstimate.theEstimateWasNotSuccessfullySynchronized'), '');
                capturePosthogEvent(postHogEvent, { ...postHogData, error: true });
            },
        }
    );

    return mutation;
};

export default useConfirmPaymentEstimateMutation;
