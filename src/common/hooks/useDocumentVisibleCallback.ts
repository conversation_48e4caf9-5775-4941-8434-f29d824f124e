import { useEffect, useRef } from 'react';

type Callback = (active: boolean) => void;

export default function useDocumentVisibleCallback(callback: Callback) {
    const ref = useRef<Callback>(callback);
    ref.current = callback;

    useEffect(() => {
        if (document.hidden === undefined) {
            ref.current(true);
        } else {
            ref.current(!document.hidden);

            const callback = () => {
                ref.current(!document.hidden);
            };
            document.addEventListener('visibilitychange', callback);
            return () => document.removeEventListener('visibilitychange', callback);
        }
    }, []);
}
