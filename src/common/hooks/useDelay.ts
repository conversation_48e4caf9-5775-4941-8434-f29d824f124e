import { useState } from 'react';

export const useDelay = (delay: number = 1000): [boolean, (value: boolean) => void] => {
    const [bool, setBool] = useState(false);

    let timerId: ReturnType<typeof setTimeout> | null = null;

    function setDelay(value: boolean) {
        if (timerId !== null) clearTimeout(timerId);

        if (value) {
            timerId = setTimeout(() => {
                setBool(true);
            }, delay);
        } else {
            timerId = null;
            setBool(false);
        }
    }

    return [bool, setDelay];
};
