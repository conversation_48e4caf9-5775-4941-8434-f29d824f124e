import { useMutation } from '@tanstack/react-query';
import ViewByCostApi, { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from './useToasters';

type ConfirmEstimateValues = {
    repairOrderId: string;
    items: ViewByCostEstimateItemDto[];
};

const useConfirmEstimateMutation = (onSuccess?: () => void, onError?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const mutation = useMutation(
        ({ repairOrderId, items }: ConfirmEstimateValues) =>
            ViewByCostApi.confirmEstimate(repairOrderId, items),
        {
            onSuccess: () => {
                toasters.success(t('syncEstimate.estimateSuccessfullySynchronized'), '');
                onSuccess && onSuccess();
            },
            onError: () => {
                toasters.danger(t('syncEstimate.theEstimateWasNotSuccessfullySynchronized'), '');
                onError && onError();
            },
        }
    );

    return mutation;
};

export default useConfirmEstimateMutation;
