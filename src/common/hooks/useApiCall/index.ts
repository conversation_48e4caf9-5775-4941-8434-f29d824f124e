import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setNewToaster } from '../../../store/actions/toasters.action';
import { ApiCallStatus, IUseApiCallPropsOptions, compileErrorNotification } from './helpers';

type UseApiCallBehaviour = 'ContinueOld' | 'ForgetOld';

/**
 * @deprecated use react-query library
 */
export const useApiCall = (behaviour: UseApiCallBehaviour = 'ForgetOld') => {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const [apiCallStatus, setApiCallStatus] = useState<ApiCallStatus>('NotCalledYet');
    const promiseRef = useRef<Promise<any>>();

    const callApi = useCallback(
        async <TGoodResponse, TBadResponse>(
            callApiFunc: () => Promise<TGoodResponse>,
            options?: IUseApiCallPropsOptions<TBadResponse>
        ): Promise<TGoodResponse> => {
            const isLastPromise = () => {
                return promise === promiseRef.current;
            };

            const setStatusIfLastPromise = (status: ApiCallStatus) => {
                if (isLastPromise()) setApiCallStatus(status);
            };

            setApiCallStatus('Pending');
            const promise = callApiFunc();
            promiseRef.current = promise;

            return new Promise(async (resolve, reject) => {
                try {
                    const result = await promise;
                    if (isLastPromise() || behaviour === 'ContinueOld') {
                        resolve(result);
                    }
                } catch (error: unknown) {
                    const errorNotification = compileErrorNotification<TBadResponse>(
                        t,
                        error,
                        options
                    );
                    dispatch(setNewToaster(errorNotification));
                    setStatusIfLastPromise('Rejected');

                    if (isLastPromise()) {
                        reject(error || behaviour === 'ContinueOld');
                    }
                } finally {
                    setStatusIfLastPromise('Finished');
                }
            });
        },
        [dispatch, t, behaviour]
    );

    return {
        /**
         * @deprecated
         */
        callApi,
        apiCallStatus,
    };
};

export interface IDefaultErrorResponse {
    status: number;
    data: {
        message: string;
    };
}
