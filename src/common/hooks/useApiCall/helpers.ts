import {
    INotificationBaseProps,
    NotificationType,
} from '../../components/Notification/INotificationProps';
import { NotificationData } from '../../components/NotificationPull/NotificationData';

export interface IUseApiCallPropsOptions<TBadResponse> {
    selectErrorContent?: (errorResponse: TBadResponse) => Partial<INotificationBaseProps>;
}

export type ApiCallStatus = 'NotCalledYet' | 'Pending' | 'Finished' | 'Rejected';

export const compileErrorNotification = <TBadResponse>(
    translationFunc: (tKey: string) => string,
    error: unknown,
    options?: IUseApiCallPropsOptions<TBadResponse>
): NotificationData => {
    const defaultErrorContent = {
        body: translationFunc('toasters.errorOccurred'),
        title: translationFunc('toasters.errorOccurred'),
        type: NotificationType.danger,
    };

    const customErrorContent = options?.selectErrorContent?.(error as TBadResponse);
    const errorContent = {
        ...defaultErrorContent,
        ...customErrorContent,
    };

    const notification = new NotificationData(
        errorContent.body as string,
        errorContent.title as string,
        errorContent.type
    );

    return notification;
};
