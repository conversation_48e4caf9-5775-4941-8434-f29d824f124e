import { useMemo, useSyncExternalStore } from 'react';
import { utcClock } from '../../utils/utcClock';

/**
 * Returns useServerUtcNow() that any component can call.
 * Component re-renders only when the offset changes
 * (~ once a RESYNC_INTERVAL_MS)
 */
export function useServerUtcNow() {
    useSyncExternalStore(utcClock.subscribe, () => utcClock.offsetMs);
    return useMemo(() => utcClock.now, []);
}
