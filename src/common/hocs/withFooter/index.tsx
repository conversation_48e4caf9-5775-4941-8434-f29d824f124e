import Footer from '../../components/Footer';
import { useStyles } from './css';

/**
 * @deprecated HOCs are evil, don't use them
 */
export function withFooter<T extends JSX.IntrinsicAttributes>(
    WrappedComponent: React.ComponentType<T>
): React.ComponentType<T> {
    return (props: T) => {
        const styles = useStyles();

        return (
            <>
                <div className={styles.content}>
                    <WrappedComponent {...props} />
                </div>
                <Footer />
            </>
        );
    };
}
