export type OrderStatus =
    | 'Uploading'
    | 'LoadedPosted'
    | 'SentEmail'
    | 'SentSMS'
    | 'SentSMSAndEmail'
    | 'SentWA'
    | 'SentEmailAndWA'
    | 'SentSMSAndWA'
    | 'SentSMSEmailAndWA'
    | 'SentInspectionForm'
    | 'ApprovedByConsumer'
    | 'DeclinedByConsumer'
    | 'ApprovedByTeamMember'
    | 'DeclinedByTeamMember'
    | 'Closed'
    | 'UploadedFromDms'
    | 'UploadedCM1SAppointment'
    | 'UploadedFromOpenAPI'
    | 'ConvertedFromAppointment'
    | 'Reopened';

export enum WPOrderStatus {
    onQueue = 'onQueue',
    onHold = 'onHold',
    onProcess = 'inProgress',
    inWash = 'forWashing',
    onPreparationForDelivery = 'prepDelivery',
    readyForDelivery = 'readyForDelivery',
    done = 'delivered',
    qualityControl = 'qualityControl',
}
export const OrderStatusLabel = (status: OrderStatus) => `orders.status.${status}`;
