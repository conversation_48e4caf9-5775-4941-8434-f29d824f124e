export const GEN_SETTINGS_TABS = {
    LOCATION_INFO: 'locationinfo',
    TEAM_MEMBERS: 'teammembers',
    ORDERS: 'orders',
    ORDER_PDF: 'orderpdf',
    INSPECTION_FORMS: 'inspectionforms',
    APPOINTMENTS: 'appointments',
    SITE_FOR_APPOINTMENTS: 'siteforappointments',
    APPOINTMENT_REASONS: 'appointmentreasons',
    ORDER_TYPES: 'ordertypes',
    PHASES: 'phases',
    NOTIFICATIONS: 'notifications',
    TERMINOLOGIES: 'terminologies',
    IMPORT_CUSTOMERS: 'import-customers',
    PACKAGES: 'packages',
    CUSTOMIZABLE_FIELDS: 'fields',
};

export const REPORTS_TABS = {
    PREDEFINED_REPORTS: 'predefinedreports',
    CUSTOM_REPORTS: 'customreports',
};
export const CM_SETTINGS_TABS = {
    GENERAL: 'general',
    SIGNATURES: 'signatures',
    FOLLOW_UP: 'followup',
    CAMPAIGNS: 'campaigns',
};

export const SETTINGS_PROSPECTION_TABS = {
    GENERAL: 'general',
    MAINTENANCE_PROSPECTION: 'maintenance',
    PROSPECTION_EXCEPTION: 'exceptions',
    IMPORT_CUSTOMERS: 'import-customers',
    CALL_RESULTS: 'call-results',
};

export const SETTINGS_WORKSHOPPLANNER_TABS = {
    GENERAL: 'general',
    PLANNINGS: 'plannings',
};

export const VIEW_BY_COST_TABS = {
    QUOTE: 'quote',
    PHASE: 'phase',
    GENERAL_INFO: 'general-info',
};

// TODO (AP) Rename to SHOP_ROUTES
export const ROUTES = {
    BASE: '/',
    LOGIN: '/login',
    RESET_PASSWORD: '/reset-password',
    NEW_PASSWORD: '/new-password/:token',
    ORDERS: '/orders',
    ORDERS_DETAIL: '/order-details/:id',
    FOLLOW_UP: '/follow-up',
    REPORTS: {
        PATH: '/reports/:section',
        ROOT: '/reports',
        DEFAULT: `/reports/${REPORTS_TABS.PREDEFINED_REPORTS}`,
    },
    SURVEYS: '/surveys',
    MASSIVE_SENDING: '/massive-sending',
    MASS_TASKS: '/tasks/mass-tasks',
    INDICATORS: '/indicators',
    BDC_INDICATORS: '/bdc-indicators',
    VEHICLE_DATABASE: '/vehicle-database',
    VEHICLE_DETAILS: '/vehicle/:vehicleId',
    TASKS: '/tasks',
    VEHICLES: '/vehicles',
    APPOINTMENTS: {
        BASE: '/appointments',
        SUBROUTES: {
            NEW: '/new',
            APPOINTMENT: '/:appointmentId',
        },
    },
    SETTINGS: {
        GENERAL: {
            PATH: `/settings/general/:section`,
            DEFAULT: `/settings/general/${GEN_SETTINGS_TABS.LOCATION_INFO}`,
        },
        CM: {
            PATH: `/settings/cm/:section`,
            DEFAULT: `/settings/cm/${CM_SETTINGS_TABS.GENERAL}`,
        },
        PROSPECTIONS: {
            PATH: `/settings/prospections/:section`,
            DEFAULT: `/settings/prospections/${SETTINGS_PROSPECTION_TABS.GENERAL}`,
            IMPORT_CUSTOMERS: `/settings/prospections/${SETTINGS_PROSPECTION_TABS.IMPORT_CUSTOMERS}`,
        },
        WORKSHOP_PLANNER: {
            PATH: `/settings/workshop-planner/:section`,
            DEFAULT: `/settings/workshop-planner/${SETTINGS_WORKSHOPPLANNER_TABS.GENERAL}`,
        },
        AFTERSALES_CRM: {
            DEFAULT: `/settings/aftersales-crm`,
            PATH: `/settings/aftersales-crm/:section`,
        },
    },
    CONSUMERS: {
        BY_SYSTEM: '/consumer-inspection-form-by-system/:id',
        BY_PRIORITY: '/consumer-inspection-form-by-priority/:id',
        BY_ESTIMATE: '/consumer-inspection-form-by-estimate/:id',
    },
    UISYSTEM: '/ui-system',
    NOT_FOUND: '/not-found',
    ACCOUNT_DEACTIVATED: '/account-deactivated',
    HELP_EMAIL_SENT: '/help-email-sent',
    CONVERSATIONS: '/conversations/:conversationId?',
    WORKSHOP_PLANNER: '/workshop-planner',
    WORKSHOP_PLANNER_SINGLE_VIEW: '/workshop-planner/view',
    WORKSHOP_PLANNER_SINGLE_VIEW_TECHNICIANS: '/workshop-planner/view/technicians',
    WORKSHOP_PLANNER_SINGLE_VIEW_ADVISORS: '/workshop-planner/view/advisors',

    VIEW_BY_COST: '/view-by-cost/:orderId',
    CHECK_SERVICES: '/check-services',
};

export const ENTERPRISE_ROUTES = {
    BASE: '/enterprise',
    LOGIN: '/enterprise/login',

    ORDERS: '/enterprise/orders',
    ORDERS_DETAIL: '/enterprise/order-details/:id',
    REPORTS: {
        PATH: '/enterprise/reports/:section',
        ROOT: '/enterprise/reports',
        DEFAULT: `/enterprise/reports/${REPORTS_TABS.PREDEFINED_REPORTS}`,
    },
    INDICATORS: '/enterprise/indicators',
    APPOINTMENTS: '/enterprise/appointments',
    APPOINTMENTS_EDIT: '/enterprise/appointments/:num',
    APPOINTMENTS_NEW: '/enterprise/appointments/new',
    CONVERSATIONS: '/enterprise/conversations/:conversationId?',
    NOT_FOUND: '/enterprise/not-found',
    SETTINGS: {
        GENERAL: {
            PATH: `/enterprise/settings/general/:section`,
            DEFAULT: `/enterprise/settings/general/${GEN_SETTINGS_TABS.TEAM_MEMBERS}`,
        },
    },
    ACCOUNT_DEACTIVATED: '/enterprise/account-deactivated',
};

export const STATUS_PAGE_ROUTES = {
    BASE: '/status',
    ORDERS: '/status/orders',
    ORDERS_DETAIL: '/status/orders/:orderNumber',
    ORDERS_DETAIL_NOT_FOUND: '/status/orders/not-found',
    APPOINTMENTS: '/status/appointments',
    NOT_FOUND: '/status/not-found',
};
