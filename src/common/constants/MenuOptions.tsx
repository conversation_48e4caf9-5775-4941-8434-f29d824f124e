import { UserPermissionDto } from 'api/account';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import React from 'react';
import { generatePath } from 'react-router-dom';
import { JobTitle } from './JobTitle';
import { ENTERPRISE_ROUTES, ROUTES } from './RoutesDefinition';
export type MenuOptionTooltip = {
    title: string;
};

type IsVisibleFn = (glSettings: GlobalSettingsDto, permission: UserPermissionDto) => boolean;

// TODO (AP) Separate this class to 2 subclasses - CollapsableMenuOption & ClickableMenuOption
export type MenuOption = {
    key: string;
    label: string;
    active: boolean;
    getRedirectTo: (glSettings: GlobalSettingsDto) => string;
    subMenuOptions?: MenuSubOption[];
    isVisible: IsVisibleFn;
    disabledRedirectTo?: string;
    isDisabled?: (glSettings: GlobalSettingsDto) => boolean;
    showTooltip?: (glSettings: GlobalSettingsDto) => boolean;
    tooltip?: MenuOptionTooltip;
    shouldBeExpanded?: (jobTitle: JobTitle) => boolean;
    icon?: React.ReactNode;
};

export type MenuSubOption = {
    key: string;
    label: string | ((gs: GlobalSettingsDto) => string);
    active: boolean;
    getRedirectTo: (glSettings: GlobalSettingsDto) => string;
    isVisible: IsVisibleFn;
    disabledRedirectTo?: string;
    isDisabled?: (glSettings: GlobalSettingsDto) => boolean;
    showTooltip?: (glSettings: GlobalSettingsDto) => boolean;
    tooltip?: MenuOptionTooltip;
    icon?: React.ReactNode;
};

export const MenuOptions: MenuOption[] = [
    {
        active: false,
        label: 'titles.reports',
        getRedirectTo: (gs) =>
            gs.appMode === 'RepairShop'
                ? ROUTES.REPORTS.DEFAULT
                : ENTERPRISE_ROUTES.REPORTS.DEFAULT,
        key: 'Reports',
        isVisible: (_gs, userPermission) => userPermission.allowGenerateReports,
    },
    {
        label: 'titles.orders',
        getRedirectTo: (gs) =>
            gs.appMode === 'RepairShop' ? ROUTES.ORDERS : ENTERPRISE_ROUTES.ORDERS,
        active: false,
        key: 'Orders',
        isVisible: () => true,
    },
    {
        label: 'titles.appointments',
        getRedirectTo: (gs) =>
            gs.appMode === 'RepairShop' ? ROUTES.APPOINTMENTS.BASE : ENTERPRISE_ROUTES.APPOINTMENTS,
        active: true,
        key: 'Appointments',
        isVisible: (_gs, userPermission) => userPermission.allowSeeAppointments,
    },
    {
        label: 'titles.workshopPlanner',
        getRedirectTo: () => ROUTES.WORKSHOP_PLANNER,
        active: true,
        key: 'WorkshopPlanner',
        isVisible: (gs) => gs.appMode === 'RepairShop',
        isDisabled: (gs) => gs.repairShopSettings?.features.enableWp !== true,
        showTooltip: (gs) => gs.repairShopSettings?.features.enableWp !== true,
        tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
    },
    {
        active: false,
        label: 'titles.conversations',
        getRedirectTo: () => generatePath(ROUTES.CONVERSATIONS, {}),
        key: 'Conversation',
        isVisible: (gs) =>
            gs.appMode === 'RepairShop' &&
            gs.repairShopSettings?.features.sendWhatsappToConsumer === true,
    },
    {
        label: 'titles.conversations',
        getRedirectTo: () => generatePath(ENTERPRISE_ROUTES.CONVERSATIONS, {}),
        active: true,
        key: 'Conversation',
        isVisible: (gs) => gs.appMode === 'Enterprise',
    },
    {
        active: false,
        label: 'titles.surveys',
        getRedirectTo: () => ROUTES.SURVEYS,
        key: 'Surveys',
        //isVisible: (gs) => gs.appMode === 'RepairShop'
        isVisible: () => false,
    },
    {
        active: false,
        label: 'titles.massiveSending',
        getRedirectTo: () => ROUTES.MASSIVE_SENDING,
        key: 'MassiveSending',
        isVisible: (gs, userPermission) =>
            gs.appMode === 'RepairShop' &&
            gs.repairShopSettings?.features.sendWhatsappToConsumer === true &&
            userPermission.allowSendMassiveSendings,
        isDisabled: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
        showTooltip: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
        tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
    },
    {
        active: false,
        label: 'titles.indicators',
        getRedirectTo: (gs) =>
            gs.appMode === 'RepairShop' ? ROUTES.INDICATORS : ENTERPRISE_ROUTES.INDICATORS,
        key: 'Indicators',
        isVisible: () => true,
    },
    {
        active: false,
        label: 'titles.settings.settings',
        getRedirectTo: () => ROUTES.SETTINGS.GENERAL.DEFAULT,
        key: 'Settings',
        isVisible: (_gs, userPermission) => userPermission.settingsAccess,
        subMenuOptions: [
            {
                active: false,
                label: 'titles.settings.general',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop'
                        ? ROUTES.SETTINGS.GENERAL.DEFAULT
                        : ENTERPRISE_ROUTES.SETTINGS.GENERAL.DEFAULT,
                key: 'General',
                isVisible: () => true,
            },
            {
                active: false,
                label: 'titles.settings.cm',
                getRedirectTo: () => ROUTES.SETTINGS.CM.DEFAULT,
                key: 'ClearMechanic',
                isVisible: (gs) => gs.appMode === 'RepairShop',
            },
            {
                active: false,
                label: 'titles.settings.prospections',
                getRedirectTo: () => ROUTES.SETTINGS.PROSPECTIONS.DEFAULT,
                key: 'Prospections',
                isVisible: (gs) => gs.appMode === 'RepairShop',
                isDisabled: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
            {
                active: false,
                label: 'titles.settings.workshopPlanner',
                getRedirectTo: () => ROUTES.SETTINGS.WORKSHOP_PLANNER.DEFAULT,
                key: 'WorkshopPlanner',
                isVisible: (gs) => gs.appMode === 'RepairShop',
                isDisabled: (gs) => gs.repairShopSettings?.features.enableWp !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableWp !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
        ],
    },
];

export const AftersalesCrmMenuOptions: MenuOption[] = [
    {
        active: false,
        label: 'titles.analysis',
        getRedirectTo: () => ROUTES.SETTINGS.GENERAL.DEFAULT,
        key: 'Analysis',
        isVisible: () => true,
        subMenuOptions: [
            {
                active: false,
                label: (gs) =>
                    gs.repairShopSettings?.features.enableAftersalesCrm
                        ? 'titles.indicatorsWhenCrmEnabled'
                        : 'titles.indicators',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop' ? ROUTES.INDICATORS : ENTERPRISE_ROUTES.INDICATORS,
                key: 'Indicators',
                isVisible: () => true,
            },
            {
                active: false,
                label: 'titles.bdcIndicators',
                getRedirectTo: () => ROUTES.BDC_INDICATORS,
                key: 'BdcIndicators',
                isVisible: (gs) =>
                    gs.repairShopSettings?.features.enableAftersalesCrm === true &&
                    gs.appMode === 'RepairShop',
            },
            {
                active: false,
                label: 'titles.reports',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop'
                        ? ROUTES.REPORTS.DEFAULT
                        : ENTERPRISE_ROUTES.REPORTS.DEFAULT,
                key: 'Reports',
                isVisible: (_gs, userPermission) => userPermission.allowGenerateReports,
            },
        ],
    },
    {
        active: false,
        label: 'titles.workshop',
        getRedirectTo: () => ROUTES.SETTINGS.GENERAL.DEFAULT,
        key: 'Workshop',
        isVisible: () => true,
        shouldBeExpanded: (jobTitle) =>
            !(jobTitle === 'BdcAdvisor' || jobTitle === 'BdcSupervisor'),
        subMenuOptions: [
            {
                label: 'titles.appointments',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop'
                        ? ROUTES.APPOINTMENTS.BASE
                        : ENTERPRISE_ROUTES.APPOINTMENTS,
                active: true,
                key: 'Appointments',
                isVisible: (gs, userPermission) =>
                    gs.repairShopSettings?.features.enableAftersalesCrm !== true &&
                    userPermission.allowSeeAppointments,
            },
            {
                label: 'titles.orders',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop' ? ROUTES.ORDERS : ENTERPRISE_ROUTES.ORDERS,
                active: false,
                key: 'Orders',
                isVisible: () => true,
            },
            {
                label: 'titles.workshopPlanner',
                getRedirectTo: () => ROUTES.WORKSHOP_PLANNER,
                active: true,
                key: 'WorkshopPlanner',
                isVisible: (gs) => gs.appMode === 'RepairShop',
                isDisabled: (gs) => gs.repairShopSettings?.features.enableWp !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableWp !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
            {
                active: false,
                label: 'titles.conversations',
                getRedirectTo: () => generatePath(ROUTES.CONVERSATIONS, {}),
                key: 'Conversation',
                isVisible: (gs) =>
                    gs.appMode === 'RepairShop' &&
                    gs.repairShopSettings?.features.sendWhatsappToConsumer === true,
            },
            {
                label: 'titles.conversations',
                getRedirectTo: () => generatePath(ENTERPRISE_ROUTES.CONVERSATIONS, {}),
                active: true,
                key: 'Conversation',
                isVisible: (gs) => gs.appMode === 'Enterprise',
            },
            {
                active: false,
                label: 'titles.massiveSending',
                getRedirectTo: () => ROUTES.MASSIVE_SENDING,
                key: 'MassiveSending',
                isVisible: (gs, userPermission) =>
                    gs.appMode === 'RepairShop' &&
                    gs.repairShopSettings?.features.sendWhatsappToConsumer === true &&
                    gs.repairShopSettings?.features.enableAftersalesCrm !== true &&
                    userPermission.allowSendMassiveSendings,
                isDisabled: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
            {
                active: true,
                label: 'titles.vehicleDatabase',
                getRedirectTo: () => ROUTES.VEHICLE_DATABASE,
                key: 'VehicleDatabase',
                isVisible: (gs) =>
                    gs.repairShopSettings?.features.enableAftersalesCrm !== true &&
                    gs.appMode === 'RepairShop',
            },
        ],
    },
    {
        active: false,
        label: 'titles.aftersalesCrm',
        getRedirectTo: () => ROUTES.SETTINGS.GENERAL.DEFAULT,
        key: 'AftersalesCrm',
        isVisible: (gs) =>
            gs.repairShopSettings?.features.enableAftersalesCrm === true &&
            gs.appMode === 'RepairShop',
        shouldBeExpanded: (jobTitle) => jobTitle === 'BdcAdvisor' || jobTitle === 'BdcSupervisor',
        subMenuOptions: [
            {
                active: true,
                label: 'titles.vehicleDatabase',
                getRedirectTo: () => ROUTES.VEHICLE_DATABASE,
                key: 'VehicleDatabase',
                isVisible: () => true,
            },
            {
                active: true,
                label: 'titles.vehicles',
                getRedirectTo: () => ROUTES.VEHICLES,
                key: 'Vehicles',
                isVisible: () => true,
            },
            {
                active: true,
                label: 'titles.tasks',
                getRedirectTo: () => ROUTES.TASKS,
                key: 'Tasks',
                isVisible: () => true,
            },
            {
                label: 'titles.appointments',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop'
                        ? ROUTES.APPOINTMENTS.BASE
                        : ENTERPRISE_ROUTES.APPOINTMENTS,
                active: true,
                key: 'Appointments',
                isVisible: (_gs, userPermission) => userPermission.allowSeeAppointments,
            },
            {
                active: true,
                label: 'titles.massTasks',
                getRedirectTo: () => ROUTES.MASS_TASKS,
                key: 'MassTasks',
                isVisible: (gs, userPermission) =>
                    gs.appMode === 'RepairShop' && userPermission.allowManageMassTasks,
                icon: <CalendarIcon fill="currentColor" />,
            },
            {
                active: true,
                label: 'titles.massiveSending',
                getRedirectTo: () => ROUTES.MASSIVE_SENDING,
                key: 'MassiveSending',
                isVisible: (gs, userPermission) =>
                    gs.appMode === 'RepairShop' &&
                    gs.repairShopSettings?.features.sendWhatsappToConsumer === true &&
                    userPermission.allowSendMassiveSendings,
                isDisabled: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
        ],
    },

    {
        active: false,
        label: 'titles.surveys',
        getRedirectTo: () => ROUTES.SURVEYS,
        key: 'Surveys',
        //isVisible: (gs) => gs.appMode === 'RepairShop'
        isVisible: () => false,
    },

    {
        active: false,
        label: 'titles.settings.settings',
        getRedirectTo: () => ROUTES.SETTINGS.GENERAL.DEFAULT,
        key: 'Settings',
        isVisible: (_gs, userPermission) => userPermission.settingsAccess,
        subMenuOptions: [
            {
                active: false,
                label: 'titles.settings.general',
                getRedirectTo: (gs) =>
                    gs.appMode === 'RepairShop'
                        ? ROUTES.SETTINGS.GENERAL.DEFAULT
                        : ENTERPRISE_ROUTES.SETTINGS.GENERAL.DEFAULT,
                key: 'General',
                isVisible: () => true,
            },
            {
                active: false,
                label: 'titles.settings.cm',
                getRedirectTo: () => ROUTES.SETTINGS.CM.DEFAULT,
                key: 'ClearMechanic',
                isVisible: (gs) => gs.appMode === 'RepairShop',
            },
            {
                active: false,
                label: 'titles.settings.workshopPlanner',
                getRedirectTo: () => ROUTES.SETTINGS.WORKSHOP_PLANNER.DEFAULT,
                key: 'WorkshopPlanner',
                isVisible: (gs) => gs.appMode === 'RepairShop',
                isDisabled: (gs) => gs.repairShopSettings?.features.enableWp !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableWp !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
            {
                active: false,
                label: 'titles.settings.prospections',
                getRedirectTo: () => ROUTES.SETTINGS.PROSPECTIONS.DEFAULT,
                key: 'Prospections',
                isVisible: (gs) => gs.appMode === 'RepairShop',
                isDisabled: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                showTooltip: (gs) => gs.repairShopSettings?.features.enableAutoProspection !== true,
                tooltip: { title: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled' },
            },
            {
                active: true,
                label: 'titles.aftersalesCrm',
                getRedirectTo: () =>
                    generatePath(ROUTES.SETTINGS.AFTERSALES_CRM.PATH, { section: 'general' }),
                key: 'SettingsAftersalesCrm',
                isVisible: (gs) =>
                    gs.repairShopSettings?.features.enableAftersalesCrm === true &&
                    gs.appMode === 'RepairShop',
            },
        ],
    },
];

export default MenuOptions;
