export enum ReasonForPause {
    AssignmentofAnotherVehicle = 1,
    Lunch,
    WaitingForCustomerAuthorization,
    WaitingForServiceBay,
    WaitingForTools,
    WaitingForTechnicianReassignment,
    WaitingForParts,
    InWarrantyProcess,
    TOT,
    Other,
}

export const REASONS_FOR_PAUSE = [
    ReasonForPause.AssignmentofAnotherVehicle,
    ReasonForPause.Lunch,
    ReasonForPause.WaitingForCustomerAuthorization,
    ReasonForPause.WaitingForServiceBay,
    ReasonForPause.WaitingForTools,
    ReasonForPause.WaitingForTechnicianReassignment,
    ReasonForPause.WaitingForParts,
    ReasonForPause.InWarrantyProcess,
    ReasonForPause.TOT,
    ReasonForPause.Other,
];

export function convertReasonForPauseToLabel(reason: ReasonForPause) {
    switch (reason) {
        case ReasonForPause.AssignmentofAnotherVehicle:
            return 'assignmentofAnotherVehicle';
        case ReasonForPause.Lunch:
            return 'lunch';
        case ReasonForPause.WaitingForCustomerAuthorization:
            return 'waitingForCustomerAuthorization';
        case ReasonForPause.WaitingForServiceBay:
            return 'waitingForServiceBay';
        case ReasonForPause.WaitingForTools:
            return 'waitingForTools';
        case ReasonForPause.WaitingForTechnicianReassignment:
            return 'waitingForTechnicianReassignment';
        case ReasonForPause.WaitingForParts:
            return 'waitingForParts';
        case ReasonForPause.InWarrantyProcess:
            return 'inWarrantyProcess';
        case ReasonForPause.TOT:
            return 'tot';
        case ReasonForPause.Other:
            return 'other';
    }
}
