import React from 'react';
import {
    Box,
    Dialog,
    DialogContent,
    DialogTitle,
    IconButton,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from '@mui/material';

import { useQuery } from '@tanstack/react-query';
import { AppointmentAttachmentsInfoDto } from 'api/appointments';
import { styled } from '@mui/material/styles';

import { PageIcon } from '../Icons/PageIcon';

import { DownloadIcon } from '../Icons/DownloadIcon';
import { CloseIcon } from '../Icons/CloseIcon';
import i18n from 'i18next';
import { useAppTranslation } from '../../hooks/useAppTranslation';
import { scrollbarStyle } from '../../styles/ScrollbarStyles';
import { DateTime } from 'luxon';
import { useAppSelector } from 'store';
import { selectIanaTz } from 'store/slices/globalSettingsSlice';
import { OrderAttachmentDto } from 'api/orders';

type AttachedFilesModalProps = {
    id: string | number;
    fetchAttachments: () => Promise<AppointmentAttachmentsInfoDto[] | OrderAttachmentDto[]>;
    open: boolean;
    onClose: () => void;
};

const AttachedFilesModal = ({ id, fetchAttachments, open, onClose }: AttachedFilesModalProps) => {
    const { t } = useAppTranslation();
    const tzName = useAppSelector(selectIanaTz);

    const { data: attachments } = useQuery(['appointment', 'order', 'attachments', id], {
        queryFn: fetchAttachments,
        staleTime: 3000,
        cacheTime: Infinity,
        enabled: open,
    });

    const formatDate = (date: string) => {
        const lang = i18n.language;

        const format = lang.includes('en') ? 'MM/dd/yy hh:mm a' : 'dd/MM/yy hh:mm a';

        return DateTime.fromISO(date, { zone: 'utc' })
            .setZone(tzName)
            .setLocale(lang)
            .toFormat(format)
            .replace('AM', 'a.m.')
            .replace('PM', 'p.m.');
    };

    if (attachments === undefined) {
        return;
    }

    const handleDownload = (url: string) => {
        window.open(url, '_blank');
    };

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
            <Box sx={{ padding: '15px' }}>
                <DialogTitle>
                    <StyledTitle>{t('commonLabels.attachedFiles')}</StyledTitle>
                    <IconButton
                        aria-label="close"
                        onClick={onClose}
                        sx={{ position: 'absolute', right: 25, top: 25 }}
                    >
                        <CloseIcon fill={'var(--neutral7)'} />
                    </IconButton>
                </DialogTitle>
                <DialogContent>
                    <StyledFilesCount>
                        {attachments.length}{' '}
                        {attachments.length === 1
                            ? t('commonLabels.file').toLowerCase()
                            : t('commonLabels.files').toLowerCase()}
                    </StyledFilesCount>
                    <StyledTableContainer>
                        <StyledTable>
                            <StyledTableHead>
                                <TableRow sx={{ height: '60px' }}>
                                    <StyledHeaderTableCell sx={{ width: '6%' }} />
                                    <StyledHeaderTableCell sx={{ width: '45%' }}>
                                        {t('commonLabels.name').toUpperCase()}
                                    </StyledHeaderTableCell>
                                    <StyledHeaderTableCell sx={{ width: '17%' }}>
                                        {t('commonLabels.uploaded').toUpperCase()}
                                    </StyledHeaderTableCell>
                                    <StyledHeaderTableCell sx={{ width: '23%' }}>
                                        {t('commonLabels.uploadedDate').toUpperCase()}
                                    </StyledHeaderTableCell>
                                    <StyledHeaderTableCell />
                                </TableRow>
                            </StyledTableHead>
                            <ScrollArea>
                                <TableBody>
                                    {attachments.map((attachment) => (
                                        <TableRow key={attachment.id} sx={{ height: '80px' }}>
                                            <StyledTableCell
                                                sx={{ paddingTop: '15px', width: '6%' }}
                                            >
                                                <PageIcon fill={'var(--neutral7)'} />
                                            </StyledTableCell>
                                            <StyledFileNameTableCell sx={{ width: '45%' }}>
                                                <Box sx={{ width: '100%' }}>{attachment.name}</Box>
                                            </StyledFileNameTableCell>
                                            <StyledFileInfoTableCell sx={{ width: '17%' }}>
                                                <Box sx={{ width: '100%' }}>
                                                    {attachment.uploadedBy}
                                                </Box>
                                            </StyledFileInfoTableCell>
                                            <StyledFileInfoTableCell sx={{ width: '23%' }}>
                                                {formatDate(attachment.uploadDate)}
                                            </StyledFileInfoTableCell>
                                            <StyledTableCell>
                                                <StyledIconButton
                                                    onClick={() => handleDownload(attachment.url)}
                                                >
                                                    <DownloadIcon fill={'var(--neutral7)'} />
                                                </StyledIconButton>
                                            </StyledTableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </ScrollArea>
                        </StyledTable>
                    </StyledTableContainer>
                </DialogContent>
            </Box>
        </Dialog>
    );
};

const StyledTableContainer = styled(TableContainer)({
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    overflow: 'hidden',
    width: '100%',
});

const StyledTable = styled(Table)({
    tableLayout: 'fixed',
});

const StyledTableHead = styled(TableHead)(({ theme }) => ({
    ...theme.typography.h6,
    backgroundColor: '#f5f5f5',
}));

const StyledTableCell = styled(TableCell)({
    color: 'var(--neutral7)',
    fontWeight: 'bold',
    textAlign: 'left',
    padding: '12px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});

const StyledHeaderTableCell = styled(TableCell)(({ theme }) => ({
    color: 'var(--neutral7)',
    ...theme.typography.h6,
    fontWeight: 700,
    textAlign: 'left',
    padding: '12px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));

const StyledFileNameTableCell = styled(TableCell)(({ theme }) => ({
    color: 'var(--neutral7)',
    ...theme.typography.h5,
    fontWeight: 700,
    textAlign: 'left',
    padding: '12px',
    whiteSpace: 'none',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));

const StyledFileInfoTableCell = styled(TableCell)(({ theme }) => ({
    color: 'var(--neutral7)',
    ...theme.typography.h5,
    fontWeight: 400,
    textAlign: 'left',
    padding: '12px',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));

const StyledFilesCount = styled('div')(({ theme }) => ({
    color: 'var(--neutral7)',
    ...theme.typography.h7Roboto,
    fontWeight: 400,
    marginBottom: 20,
}));

const ScrollArea = styled('div')({
    width: '820px',
    maxHeight: '450px',
    overflowY: 'auto',
    overflowX: 'hidden',
    ...scrollbarStyle(),
});

const StyledTitle = styled('div')(({ theme }) => ({
    color: 'var(--neutral8)',
    ...theme.typography.h5,
    fontWeight: 700,
}));

const StyledIconButton = styled(IconButton)({
    color: '#757575',
    '&:hover': {
        color: '#000',
    },
});

export default AttachedFilesModal;
