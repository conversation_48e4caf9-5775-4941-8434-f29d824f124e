import { Step, StepConnector, StepLabel, styled, Theme } from '@mui/material';
import MuiStepper from '@mui/material/Stepper';
import { withStyles } from '@mui/styles';
import clsx from 'clsx';
import { Colors } from 'common/styles/Colors';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useEffect, useRef } from 'react';
import StepIcon from './Icon';
import useStyles from './css';

type StepperProps = {
    activeStep: number;
    steps: SimpleStep[];
    activeIcon?: React.ReactNode;
    vertical?: boolean;
};

export type SimpleStep = {
    label: string;
    id: number;
};

const Stepper = ({ activeStep, steps, activeIcon, vertical = false }: StepperProps) => {
    const classes = useStyles();
    const stepperRef = useRef<HTMLDivElement>(null);
    const activeStepRef = useRef<HTMLDivElement>(null);
    const StyledStepperConnector = withStyles((theme: Theme) => ({
        alternativeLabel: {
            top: 20,
        },
        active: {
            '& $line': {
                borderColor: Colors.CM1,
                [theme.breakpoints.up('sm')]: {
                    borderColor: Colors.Success,
                },
            },
        },
        completed: {
            '& $line': {
                borderColor: Colors.Success,
            },
        },
        line: {
            top: 0,
            borderColor: Colors.Transparent,
            borderTopWidth: 1,
            borderRadius: 1,
            marginRight: 16,
        },
    }))(StepConnector);

    useEffect(() => {
        if (vertical) {
            if (activeStepRef.current) {
                activeStepRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            if (stepperRef.current) {
                const stepWidth = stepperRef.current.clientWidth / steps.length; // Assuming 3 steps

                // Calculate the target scroll position based on the active step
                let targetScrollLeft = 0;
                if (activeStep > 3) {
                    targetScrollLeft =
                        stepWidth * activeStep - (stepperRef.current.clientWidth - stepWidth) / 2;
                }

                // Set the scroll position
                stepperRef.current.scrollLeft = targetScrollLeft;
            }
        }
    }, [activeStep, vertical, steps.length]);

    return (
        <OverlayScrollbarsComponent
            options={{
                scrollbars: { theme: 'os-theme-dark' },
            }}
        >
            <DivRoot ref={stepperRef}>
                <MuiStepper
                    className={classes.stepper}
                    alternativeLabel={!vertical}
                    activeStep={activeStep}
                    connector={<div />}
                    orientation={vertical ? 'vertical' : 'horizontal'}
                >
                    {steps.map(({ label, id }, index) => {
                        const isNeutralWeb = index > activeStep + 1;
                        const isNeutralMobile = index > activeStep;
                        return (
                            <Step key={`${id} - ${label}`} className={classes.step}>
                                <StepLabel
                                    StepIconComponent={(props) => {
                                        return StepIcon({ ...props, activeIcon });
                                    }}
                                    classes={{
                                        alternativeLabel: classes.labelAlternativeLabel,
                                        completed: classes.labelCompleted,
                                        label: classes.label,
                                        active: classes.activeLabel,
                                    }}
                                    ref={index === activeStep ? activeStepRef : null}
                                >
                                    {label}
                                </StepLabel>
                                {!vertical && index > 0 && (
                                    <StyledStepperConnector
                                        className={clsx(
                                            classes.connector,
                                            index === activeStep + 1
                                                ? classes.connectorActive
                                                : isNeutralWeb
                                                ? classes.connectorNeutral
                                                : classes.connectorCompleted
                                        )}
                                    />
                                )}
                                {vertical && index < steps.length - 1 && (
                                    <StyledStepperConnector
                                        className={clsx(
                                            classes.connector,
                                            index === activeStep
                                                ? classes.connectorActive
                                                : isNeutralMobile
                                                ? classes.connectorNeutral
                                                : classes.connectorCompleted
                                        )}
                                    />
                                )}
                            </Step>
                        );
                    })}
                </MuiStepper>
            </DivRoot>
        </OverlayScrollbarsComponent>
    );
};

const DivRoot = styled('div')({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingBottom: '20px',
});

export default Stepper;
