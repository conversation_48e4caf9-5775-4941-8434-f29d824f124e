import { StepIconProps } from '@mui/material/StepIcon';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import useStyles from './css';

// NOTE (MB) i don't know if "icon" prop is supposed to be left unused or not, leaving as is for now
const StepIcon = ({
    active,
    completed,
    activeIcon,
    icon,
}: StepIconProps & { activeIcon?: React.ReactNode }) => {
    const classes = useStyles();
    return (
        <div className={classes.root}>
            {completed ? (
                <CheckIcon fill={Colors.Success} size={IconSize.LL} />
            ) : active ? (
                activeIcon ? (
                    activeIcon
                ) : (
                    <div className={classes.activeCircle} />
                )
            ) : (
                <div className={classes.circle} />
            )}
        </div>
    );
};

export default StepIcon;
