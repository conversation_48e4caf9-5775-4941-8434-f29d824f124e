import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const useStyles = makeStyles((theme: Theme) => ({
    stepper: {
        backgroundColor: theme.palette.neutral[2],
        width: '200%',
        [theme.breakpoints.down('md')]: {
            padding: 0,
        },
    },
    step: {
        [theme.breakpoints.up('sm')]: {
            width: 180,
        },
    },
    labelAlternativeLabel: {
        marginRight: 16,
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[7]),
        '& .MuiStepLabel-active': {
            ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.primary.main),
        },
    },
    label: {
        width: '100%',
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[7]),
    },
    activeLabel: {
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.primary.main),
        color: `${theme.palette.primary.main} !important`,
        fontWeight: '700 !important' as any,
    },
    labelCompleted: {
        color: `${Colors.Success} !important`,
        fontWeight: '700 !important' as any,
    },
    connector: {
        width: 1,
        // height: 'calc(100vh / 20)',
        height: 70,
        [theme.breakpoints.up('sm')]: {
            width: 'auto',
            height: 1,
            marginRight: 30,
        },
    },
    connectorCompleted: {
        backgroundColor: Colors.Success,
    },
    connectorActive: {
        backgroundColor: theme.palette.primary.main,
    },
    connectorNeutral: {
        backgroundColor: theme.palette.neutral[7],
    },
}));

export default useStyles;
