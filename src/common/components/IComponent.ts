/**
 * @deprecated this interface is just some props that you can put on any element, we can just use react typing for that
 * for example React.HTMLAttributes<HTMLDivElement> includes all of those properties and also contains actual correct types
 * instead of "Function" as the callbacks
 */

export interface IComponent {
    location?: any;
    className?: any;
    // TODO (MB) This needs to be deprecated and we should use forwardRef instead
    // Because "ref" is not a prop: https://github.com/facebook/react/pull/5744
    /**
     * @deprecated this is a property and "ref" is not a property, remove this and just use forwarRef everywhere instead
     */
    ref?: any;

    // TODO make proper typings or maybe even remove some of that in favor of exisintg react typings
    // please don't be lazy like that
    id?: any;
    onFocus?: React.MouseEventHandler;
    onBlur?: React.MouseEventHandler;
    onMouseOut?: React.MouseEventHandler;
    onMouseLeave?: React.MouseEventHandler;
    onClick?: React.MouseEventHandler;
}
