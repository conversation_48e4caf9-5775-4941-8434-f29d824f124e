.header {
    height: var(--cmos-aps-header-size);
    position: sticky;
    top: 0;
    box-sizing: border-box;
    overflow: hidden;
    z-index: 3;
    display: flex;
    background-color: #fff;
    min-width: 100%;
}

.inner {
    border-collapse: collapse;
    height: 100%;
    box-sizing: border-box;
    align-items: stretch;
    border-bottom: 2px solid var(--cmos-aps-border-color);
    display: flex;
    align-items: stretch;
}

.inner > :last-child {
    margin-right: 6px;
}

.corner {
    width: var(--cmos-aps-timeline-size);
    min-width: var(--cmos-aps-timeline-size);
    height: 100%;
    border-right: 1px solid var(--cmos-aps-border-color);
    position: sticky;
    left: 0;
    top: 0;
    z-index: 4;
    background-color: #fff;
    box-sizing: border-box;
}

.cell {
    border: solid var(--cmos-aps-border-color);
    --width: var(--cmos-aps-column-size);
    width: var(--width);
    min-width: var(--width);
    max-width: var(--width);
    border-width: 0 1px 0 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

.cell:last-child {
    border-right-width: 0;
}
