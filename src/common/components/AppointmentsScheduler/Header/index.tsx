import clsx from 'clsx';
import React, { useContext, useEffect, useRef } from 'react';
import { AppointmentSchedulerInnerElementContext, KeyGetterFunction } from '../_util';
import styles from './css.module.css';

export type HeaderCellComponentProps<G> = {
    value: G;
};
export type HeaderCellComponent<G> = React.ComponentType<HeaderCellComponentProps<G>>;

export type AppointmentSchedulerHeaderProps<G> = {
    groups: G[];
    getGroupKey: KeyGetterFunction<G>;
    component: HeaderCellComponent<G>;
    CornerComponent?: React.ComponentType<{}>;
    classNameCell?: string;
};

export default function AppointmentSchedulerHeader<G>({
    groups,
    CornerComponent,
    getGroupKey,
    classNameCell,
    component: Component,
}: AppointmentSchedulerHeaderProps<G>) {
    const { element: innerElement } = useContext(AppointmentSchedulerInnerElementContext);
    const headerRef = useRef<HTMLElement | null>(null);

    useEffect(() => {
        if (!innerElement) return;

        const callback = () => {
            if (!headerRef.current) return;

            headerRef.current.scroll(innerElement.scrollLeft, 0);
        };

        innerElement.addEventListener('scroll', callback);
        return () => innerElement.removeEventListener('scroll', callback);
    }, [innerElement]);

    return (
        <header ref={headerRef} className={styles.header}>
            <div className={styles.corner}>{CornerComponent && <CornerComponent />}</div>
            <div className={styles.inner}>
                {groups.map((g) => {
                    return (
                        <div key={getGroupKey(g)} className={clsx(styles.cell, classNameCell)}>
                            <Component value={g} />
                        </div>
                    );
                })}
            </div>
        </header>
    );
}
