.container {
    width: var(--cmos-aps-timeline-size);
    min-width: var(--cmos-aps-timeline-size);
    position: sticky;
    display: block;
    left: 0;
    box-sizing: border-box;
    z-index: 2;

    border-right: 1px solid var(--cmos-aps-border-color);
    background-color: #fff;
    overflow: hidden;
}

.timezone {
    font-size: 12px;
    text-align: center;
    font-weight: 700;
    padding-top: 8px;
    padding-right: 10px;
    color: var(--neutral7);
}

.inner {
    position: relative;
}

.mark {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transform: translateY(calc(var(--pos) - 50%));
    box-sizing: border-box;
    --decor-size: 10px;
    padding-right: var(--decor-size);
    display: flex;
    justify-content: center;
}

.mark::after {
    content: ' ';
    display: block;
    height: 1px;
    background-color: var(--cmos-aps-border-color);
    top: 50%;
    transform: translateY(-1px);
    width: var(--decor-size);
    right: 0;
    position: absolute;
}
