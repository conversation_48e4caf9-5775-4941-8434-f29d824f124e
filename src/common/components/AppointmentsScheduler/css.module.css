.root {
    --cmos-aps-timeline-size: 40px;
    --cmos-aps-header-size: 70px;
    --cmos-aps-border-color: var(--neutral3);

    box-sizing: border-box;
    position: relative;
    overflow: auto;
    /**
     * this looks good, but causes weird visual behavior
     * when appointment popup opens prior to scrolling animation
     * and then "snaps" into its place after animation 
     */
    /* scroll-behavior: smooth; */
    /* border: 1px solid var(--cmos-aps-border-color); */
    height: 100%;
}

.inner {
    display: flex;
    align-items: stretch;
    width: fit-content;
    min-width: 100%;
}
