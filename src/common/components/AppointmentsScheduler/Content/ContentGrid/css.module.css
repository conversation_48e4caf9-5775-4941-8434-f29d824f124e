.contentGridContainer {
    position: relative;
}

.columns {
    position: absolute;
    pointer-events: none;
    inset: 0;
    display: flex;
    align-items: stretch;
    justify-items: stretch;
}

.column {
    flex-grow: 1;
}

.grid {
    /**
        add a height to the grid that will be smaller than the the actual height
        this is extremely scuffed, but necessary so that the grid element will have a scrollbar
        before the scheduler calculates the actual height grid is supposed to have 

        if you remove this, auto-scroll will stop working
        btw another option is to not render grid until we calculate the height, but that's more complicated
     */
    height: 10px;
}

/* ====== GRID ====== */

.gridChildren {
    z-index: 1;
    display: contents;
}

.list {
    display: flex;
    flex-wrap: wrap;
}

.item {
    width: var(--cmos-aps-cell-width);
    height: var(--cmos-aps-cell-height);
    overflow: hidden;
    box-sizing: border-box;
}

.item > * {
    display: block;
    border: solid var(--neutral4);
    border-width: 0 1px 1px 0;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    height: 100%;
}

.item:last-child > * {
    border-bottom-width: 0;
}
