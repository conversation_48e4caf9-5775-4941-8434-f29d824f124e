.root {
    display: flex;
    justify-items: stretch;
    min-width: calc(100% - var(--cmos-aps-timeline-size));
    position: relative;
    overflow: hidden;
    background-color: #fff;
}

.column {
    width: var(--cmos-aps-column-size);
    min-width: var(--cmos-aps-column-size);
    z-index: 1;
    pointer-events: none;
}

.cell {
    position: absolute;
    z-index: inherit;
    left: var(--offset);
    transform: translateY(var(--pos));
    width: var(--cmos-aps-column-size);
    height: var(--size);
}
