import { LanguagesEnum } from 'common/constants/Language';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { forwardRef } from 'react';
import { SMenuItem2 } from '../mui/SMenuItem';

const LanguageMenuItem = forwardRef(
    (
        {
            onClick,
            language,
            onClose,
        }: { onClick: () => void; language: string; onClose: () => void },
        ref?: React.ForwardedRef<HTMLLIElement>
    ) => {
        const { t } = useAppTranslation();

        const newLanguage =
            language === LanguagesEnum.SPANISH ? LanguagesEnum.ENGLISH : LanguagesEnum.SPANISH;

        return (
            <SMenuItem2 sx={{ minWidth: 120 }} onClick={() => onClick()} ref={ref} disableRipple>
                {t(`viewByCost.common.${newLanguage}`)}
            </SMenuItem2>
        );
    }
);

export default LanguageMenuItem;
