// import { Box, Grid, IconButton, Menu, styled } from '@mui/material';
import { Button, Divider } from '@mui/material';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import { LanguagesEnum } from 'common/constants/Language';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from 'common/styles/Colors';
import { useCallback, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import {
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import { DownIcon } from '../Icons/DownIcon';
import { UpIcon } from '../Icons/UpIcon';
import { StyledMenu } from '../Inputs/Menu/common';

type UnauthorizedHeaderBarProps = {
    titleSection: string;
    showLanguageOptions?: boolean;
};

const NavRoot = styled('nav')({
    boxSizing: 'border-box',
    display: 'grid',
    gridTemplateColumns: 'auto 1fr auto',
    backgroundColor: '#ffffff',
    borderBottom: '1px solid var(--neutral3)',
    overflow: 'hidden',
    paddingLeft: '5vw',
    paddingRight: '5vw',
    height: 'var(--header-height)',
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    zIndex: 3,
});

const TitleGroup = styled('header')({
    display: 'flex',
    alignItems: 'center',
});

const Title = styled('span')({
    fontFamily: 'Inter',
    fontSize: '14px',
    fontWeight: 'bold',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: '16.94px',
    letterSpacing: 'normal',
    color: '#899198',
});

const LanguageOption = styled('span')({
    fontFamily: 'Inter',
    fontSize: '12px',
    fontWeight: 'bold',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: '14.52px',
    letterSpacing: 'normal',
    color: '#0069FF',
});

const SmallMenuItemsContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const MenuLink = styled(Button)(({ theme }) => ({
    border: 'none',
    background: 'none',
    textTransform: 'none',
    padding: '12px 10px',
    borderRadius: 0,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    letterSpacing: 'normal',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    width: 150,
    textAlign: 'left',
    WebkitLineClamp: '2',
    WebkitBoxOrient: 'horizontal',
    WebkitBoxAlign: 'center',
    cursor: 'pointer',
    minHeight: 32,
    '&:hover': {
        color: theme.palette.primary.light,
        backgroundColor: rgba(theme.palette.primary.light, 0.1),
    },
}));

const UnauthorizedHeaderBar = ({
    titleSection,
    showLanguageOptions,
}: UnauthorizedHeaderBarProps) => {
    const { t, i18n } = useAppTranslation();
    const {
        defaultLanguage,
        hyperLinkedTextEnabled,
        hyperLinkedText,
        hyperLinkedUrl,
        documentHyperlinkedTextEnabled,
        documentHyperlinkedText,
        documentHyperlinkedUrl,
    } = useAppSelector(repairShopInfoSelector);

    const { repairOrderNumber, inspectionFormPdfLink } = useAppSelector(repairOrderDetailSelector);

    const documentHyperlinkUrl = useMemo(() => {
        if (!documentHyperlinkedUrl) {
            return '';
        }

        return `${documentHyperlinkedUrl}${repairOrderNumber}`;
    }, [documentHyperlinkedUrl, repairOrderNumber]);

    const [language, setLanguage] = useState<string>(defaultLanguage || 'en-US');

    const [showLanguageMenu, setShowLanguageMenu] = useState<HTMLElement | null>(null);

    const handleCloseMenuLanguage = () => {
        setShowLanguageMenu(null);
    };

    const handleEnglishLanguage = useCallback(
        (language: LanguagesEnum) => {
            setLanguage(language);
            i18n.changeLanguage(language);
            setShowLanguageMenu(null);
        },
        [i18n]
    );

    const idLanguageMenu = showLanguageMenu ? 'view-language-popper' : undefined;

    const handleDocumentHyperlinkUrl = useCallback(() => {
        if (documentHyperlinkedTextEnabled && documentHyperlinkUrl) {
            const link = document.createElement('a');
            link.href = documentHyperlinkUrl;
            link.target = '_blank';
            link.click();
        }
        setShowLanguageMenu(null);
    }, [documentHyperlinkedTextEnabled, documentHyperlinkUrl]);

    const handleHyperLinkedUrl = useCallback(() => {
        if (hyperLinkedTextEnabled && hyperLinkedUrl) {
            const link = document.createElement('a');
            link.href = hyperLinkedUrl;
            link.target = '_blank';
            link.click();
        }
        setShowLanguageMenu(null);
    }, [hyperLinkedTextEnabled, hyperLinkedUrl]);

    const handleInspectionFormPdfLink = useCallback(() => {
        if (inspectionFormPdfLink) {
            const link = document.createElement('a');
            link.href = inspectionFormPdfLink;
            link.target = '_blank';
            link.click();
        }
        setShowLanguageMenu(null);
    }, [inspectionFormPdfLink]);

    return (
        <>
            <NavRoot>
                <TitleGroup>
                    <Title> {titleSection} </Title>
                </TitleGroup>
                {showLanguageOptions && (
                    <Grid container alignItems="center" justifyContent="flex-end" wrap="nowrap">
                        <Box
                            sx={{ cursor: 'pointer' }}
                            onClick={(event: React.MouseEvent<HTMLDivElement>) =>
                                setShowLanguageMenu(event.currentTarget)
                            }
                        >
                            <LanguageOption> {t(`viewByCost.common.options`)} </LanguageOption>

                            <IconButton
                                aria-controls="simple-menu"
                                aria-haspopup="true"
                                size="small"
                            >
                                {showLanguageMenu ? (
                                    <UpIcon fill={Colors.CM1} />
                                ) : (
                                    <DownIcon fill={Colors.CM1} />
                                )}
                            </IconButton>
                        </Box>
                        <StyledMenu
                            id={idLanguageMenu}
                            anchorEl={showLanguageMenu}
                            keepMounted
                            open={Boolean(showLanguageMenu)}
                            anchorOrigin={{
                                vertical: 'bottom',
                                horizontal: 'right',
                            }}
                            transformOrigin={{
                                vertical: 'top',
                                horizontal: 'right',
                            }}
                            onClose={handleCloseMenuLanguage}
                            MenuListProps={{
                                disablePadding: true,
                            }}
                        >
                            <SmallMenuItemsContainer>
                                <MenuLink
                                    onClick={() => handleEnglishLanguage(LanguagesEnum.SPANISH)}
                                    sx={(theme) => ({
                                        color:
                                            language === LanguagesEnum.SPANISH
                                                ? theme.palette.primary.light
                                                : theme.palette.neutral[7],
                                    })}
                                >
                                    {t(`viewByCost.common.${LanguagesEnum.SPANISH}`)}
                                </MenuLink>
                            </SmallMenuItemsContainer>
                            <Divider />
                            <SmallMenuItemsContainer>
                                <MenuLink
                                    onClick={() => handleEnglishLanguage(LanguagesEnum.ENGLISH)}
                                    sx={(theme) => ({
                                        color:
                                            language === LanguagesEnum.ENGLISH
                                                ? theme.palette.primary.light
                                                : theme.palette.neutral[7],
                                    })}
                                >
                                    {t(`viewByCost.common.${LanguagesEnum.ENGLISH}`)}
                                </MenuLink>
                            </SmallMenuItemsContainer>
                            <Divider />
                            {documentHyperlinkedTextEnabled && (
                                <>
                                    <SmallMenuItemsContainer>
                                        <MenuLink onClick={handleDocumentHyperlinkUrl}>
                                            {documentHyperlinkedText}
                                        </MenuLink>
                                    </SmallMenuItemsContainer>
                                    <Divider />
                                </>
                            )}
                            {hyperLinkedTextEnabled && (
                                <>
                                    <SmallMenuItemsContainer>
                                        <MenuLink onClick={handleHyperLinkedUrl}>
                                            {hyperLinkedText}
                                        </MenuLink>
                                    </SmallMenuItemsContainer>
                                    <Divider />
                                </>
                            )}
                            <SmallMenuItemsContainer>
                                <MenuLink onClick={handleInspectionFormPdfLink}>
                                    {t('viewByCost.common.viewPdfOfTheOrder')}
                                </MenuLink>
                            </SmallMenuItemsContainer>
                        </StyledMenu>
                    </Grid>
                )}
            </NavRoot>
        </>
    );
};

export default UnauthorizedHeaderBar;
