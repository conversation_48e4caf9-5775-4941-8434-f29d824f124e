import IconButton from '@mui/material/IconButton';
import Slider from '@mui/material/Slider';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { SyntheticEvent, useEffect, useMemo, useRef, useState } from 'react';
import isFullScreenAvailable from 'utils/isFullScreenAvailable';
import { CompressIcon } from '../Icons/CompressIcon';
import { PauseIcon } from '../Icons/PauseIcon';
import { PlayIcon } from '../Icons/PlayIcon';
import { ProyectIcon } from '../Icons/ProyectIcon';
import useStyles from './css';

type VideoPlayerProps = {
    videoSource: string;
};

const fullscreenAvailable = isFullScreenAvailable('video');

const VideoPlayer = ({ videoSource }: VideoPlayerProps) => {
    const classes = useStyles();
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isFullScreen, setIsFullScreen] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    const duration = videoRef.current?.duration || 0;
    const timeElapsed = useMemo(() => (currentTime / duration) * 100, [currentTime, duration]);

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
    };

    const togglePlay = () => {
        if (videoRef.current?.paused) {
            videoRef.current?.play();
            setIsPlaying(true);
        } else {
            videoRef.current?.pause();
            setIsPlaying(false);
        }
    };

    const toggleFullScreen = () => {
        videoRef.current?.requestFullscreen();
    };

    const handleTimeUpdate = (event: SyntheticEvent<HTMLVideoElement>) => {
        if (duration === event.currentTarget.currentTime) {
            setIsPlaying(false);
            setCurrentTime(0);
        } else {
            setCurrentTime(event.currentTarget.currentTime);
        }
    };

    const handleSliderChange = (_event: Event, value: number | number[], _activeThumb: number) => {
        if (videoRef && videoRef.current) {
            const time = ((value as number) * duration) / 100;
            videoRef.current.currentTime = time;
            setCurrentTime(time);
        }
    };

    const handleExitFullscreen = () => {
        setIsFullScreen((old) => !old);
    };

    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.addEventListener('fullscreenchange', handleExitFullscreen);
            videoRef.current.addEventListener('fullscreenchange', handleExitFullscreen);
            videoRef.current.addEventListener('fullscreenchange', handleExitFullscreen);
        }

        return () => {
            if (videoRef.current) {
                videoRef.current.removeEventListener('fullscreenchange', handleExitFullscreen);
                videoRef.current.removeEventListener('fullscreenchange', handleExitFullscreen);
                videoRef.current.removeEventListener('fullscreenchange', handleExitFullscreen);
            }
        };
    }, []);

    return (
        <div className={classes.videoContainer}>
            <video
                className={classes.video}
                ref={videoRef}
                src={videoSource}
                onTimeUpdate={handleTimeUpdate}
            />
            <div className={classes.controls}>
                <div style={{ width: 'calc(100% - 12px)', padding: '0px 6px' }}>
                    <Slider
                        value={timeElapsed}
                        onChange={handleSliderChange}
                        style={{ color: Colors.White, padding: 0 }}
                    />
                </div>
                <div style={{ display: 'flex' }}>
                    <div style={{ width: '50%' }}>
                        <IconButton onClick={togglePlay} style={{ padding: 0 }} size="large">
                            {isPlaying ? (
                                <PauseIcon fill={Colors.White} size={isMobile ? 20 : IconSize.LL} />
                            ) : (
                                <PlayIcon fill={Colors.White} size={isMobile ? 20 : IconSize.LL} />
                            )}
                        </IconButton>
                        <span className={classes.timeElapsed}>
                            {formatTime(currentTime)}/{formatTime(duration)}
                        </span>
                    </div>
                    {fullscreenAvailable && (
                        <div style={{ width: '50%', display: 'flex', justifyContent: 'end' }}>
                            <IconButton
                                onClick={toggleFullScreen}
                                style={{ padding: 0 }}
                                size="large"
                            >
                                {isFullScreen ? (
                                    <CompressIcon
                                        fill={Colors.White}
                                        size={isMobile ? 20 : IconSize.LL}
                                    />
                                ) : (
                                    <ProyectIcon
                                        fill={Colors.White}
                                        size={isMobile ? 20 : IconSize.LL}
                                    />
                                )}
                            </IconButton>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default VideoPlayer;
