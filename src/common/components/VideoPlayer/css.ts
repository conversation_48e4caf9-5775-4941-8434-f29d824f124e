import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    videoContainer: {
        width: '100%',
        height: '100%',
        position: 'relative',
    },
    video: {
        width: '100%',
        height: '100%',
        borderRadius: '10px 10px 0px 0px',
        backgroundColor: '#000',
    },
    controls: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
    },
    timeElapsed: {
        ...theme.typography.h5Inter,
        fontWeight: 'bold',
        color: theme.palette.neutral[1],
    },
    timeBar: {
        flex: 1,
        height: 3,
        backgroundColor: theme.palette.neutral[4],
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
    },
    timeIndicator: {
        backgroundColor: theme.palette.primary.main,
        width: 15,
        height: 15,
        borderRadius: '50%',
        position: 'absolute',
    },
}));

export default useStyles;
