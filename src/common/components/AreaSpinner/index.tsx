import CircularProgress from '@mui/material/CircularProgress';
import clsx from 'clsx';
import styles from './styles.module.css';

interface AreaSpinnerProps {
    className?: string;
}

export const AreaSpinner = ({ className }: AreaSpinnerProps) => {
    return (
        <div className={clsx(styles.area, className)}>
            <CircularProgress size={53} />
        </div>
    );
};

export default AreaSpinner;
