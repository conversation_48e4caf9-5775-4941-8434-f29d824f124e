import { Button, IconButton, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useRef, useState } from 'react';
import { useInputValue } from '../../hooks/useInputValue';
import { Colors } from '../../styles/Colors';
import { FiltersIcon } from '../Icons/FiltersIcon';
import { GridMenuSubheader, StyledMenu } from '../Inputs/Menu/common';
import TextFormField from '../Inputs/TextField';

type ITableHeadInputFilterProps = {
    mainLabel: string;
    state?: string;
    onStateChanged: (state: string) => void;
};

export const TableHeadInputFilter = ({
    mainLabel,
    state,
    onStateChanged,
}: ITableHeadInputFilterProps) => {
    const menuButtonRef = useRef<HTMLButtonElement>(null);
    const { t } = useAppTranslation();

    const [input, setInput] = useInputValue<string>(state ?? '');
    const [showMenu, setShowMenu] = useState<boolean>(false);

    const handleApplyClicked = useCallback(() => {
        onStateChanged(input);
        setShowMenu(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [input]);

    return (
        <>
            <IconButton
                ref={menuButtonRef}
                onClick={() => setShowMenu(true)}
                aria-controls="simple-menu"
                aria-haspopup="true"
                disableFocusRipple
                size="small"
            >
                <FiltersIcon fill={Colors.Neutral7} />
            </IconButton>
            <StyledMenu
                anchorEl={menuButtonRef.current}
                keepMounted
                open={showMenu}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                onClose={() => setShowMenu(false)}
                MenuListProps={{
                    disablePadding: true,
                    subheader: (
                        <GridMenuSubheader container justifyContent="space-between">
                            <span>{mainLabel}</span>
                            <Button
                                sx={{
                                    textTransform: 'none',
                                    padding: 0,
                                    fontSize: '1em',
                                    minWidth: 50,
                                }}
                                variant="text"
                                onClick={handleApplyClicked}
                            >
                                {t('search.apply')}
                            </Button>
                        </GridMenuSubheader>
                    ),
                }}
            >
                <DivMenuItemsSearch>
                    <TextFormField
                        name={'search'}
                        placeholder={mainLabel}
                        type={'text'}
                        value={input}
                        onChange={setInput}
                        cmosVariant="roundedPrimary"
                        onEnterPress={handleApplyClicked}
                        enableEnterComplete={true}
                        onBlur={handleApplyClicked}
                    />
                </DivMenuItemsSearch>
            </StyledMenu>
        </>
    );
};

const DivMenuItemsSearch = styled('div')({
    padding: '10px 14px',
});

export default TableHeadInputFilter;
