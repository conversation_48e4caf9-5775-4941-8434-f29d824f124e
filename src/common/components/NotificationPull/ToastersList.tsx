import {
    DndContext,
    DragEndEvent,
    PointerSensor,
    useDraggable,
    useDroppable,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { CSS } from '@dnd-kit/utilities';
import { styled } from '@mui/material';
import clsx from 'clsx';
import { Colors } from 'common/styles/Colors';
import { ZLayer } from 'common/styles/ZLayer';
import React, { CSSProperties, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { useAppDispatch } from 'store';
import { removeToaster } from 'store/actions/toasters.action';
import { Toaster, ToasterPreset } from 'utils/toasters';
import { NetworkErrorIcon } from 'views/NetworkError';
import Notification from '../Notification';
import { NotificationType } from '../Notification/INotificationProps';
import { NotificationData } from './NotificationData';

export default function ToastersList({
    toasters,
    legacyNotifications,
}: {
    toasters: Toaster[];
    legacyNotifications: NotificationData[][];
}) {
    const dispatch = useAppDispatch();
    const [detached, setDetached] = useState<Record<string, { x: number; y: number }>>({});

    const sensors = useSensors(useSensor(PointerSensor));

    return (
        <DndContext modifiers={[restrictToWindowEdges]} sensors={sensors} onDragEnd={handleDragEnd}>
            <DroppableArea>
                {toasters
                    .map((toaster) => {
                        const data = toaster.getData();
                        if (data.expiresAt > 0 && data.expiresAt < Date.now() - 200) return null;

                        return (
                            <ToasterDisplay
                                key={toaster.id}
                                toaster={toaster}
                                position={detached[toaster.id]}
                            />
                        );
                    })
                    .filter(Boolean)}
                {legacyNotifications.map((l) => {
                    const first = l[0];
                    const last = l[l.length - 1];
                    return (
                        <DivItem key={last.key}>
                            <Notification
                                {...last}
                                expiresAt={first.expiresAt}
                                count={l.length}
                                onCloseNotification={() => {
                                    for (const n of l) {
                                        dispatch(removeToaster(n.key));
                                    }
                                }}
                            />
                        </DivItem>
                    );
                })}
            </DroppableArea>
        </DndContext>
    );

    function handleDragEnd(event: DragEndEvent) {
        if (event.over) {
            const id = event.active.id;
            if (typeof id === 'string' && event.over.id === 'left_bottom') {
                setDetached((d) => {
                    const newDetached = { ...d };
                    delete newDetached[id];
                    return newDetached;
                });
            }

            return;
        }

        // was dropped over nothing - detach toaster
        if (typeof event.active.id === 'string') {
            const id = event.active.id;
            if (!event.active.rect.current.translated) return;
            const pos = {
                x: event.active.rect.current.translated.left,
                y: event.active.rect.current.translated.top,
            };
            setDetached((d) => ({
                ...d,
                [id]: pos,
            }));
        }
    }
}

function ToasterDisplay({
    toaster,
    position,
}: {
    toaster: Toaster;
    position: { x: number; y: number } | undefined;
}) {
    const animateAppearanceRef = useRef(true);
    const animateAppearance = animateAppearanceRef.current;
    if (animateAppearanceRef.current) animateAppearanceRef.current = false;
    const [data, setData] = useState(toaster.getData());

    useEffect(() => {
        return toaster.subscribe(setData);
    }, [toaster]);

    const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
        id: toaster.id,
        disabled: !data.isDetachable,
    });

    const style: CSSProperties = {
        transform: transform
            ? CSS.Transform.toString({
                  ...transform,
                  scaleY: 1,
                  scaleX: 1,
              })
            : 'none',
        ...(position
            ? {
                  position: 'fixed',
                  top: position.y,
                  left: position.x,
                  zIndex: ZLayer.notification + 1,
              }
            : undefined),
    };

    const element = (
        <DivItem ref={setNodeRef} style={style}>
            <Notification
                animateAppearance={animateAppearance}
                closeable={data.closeable}
                draggable={data.isDetachable ? { listeners, attributes } : undefined}
                isDragging={isDragging}
                animationType={position || isDragging ? 'grow' : 'slide'}
                expiresAt={data.expiresAt}
                createdAt={data.createdAt}
                onCloseNotification={() => toaster.dismiss()}
                title={data.title}
                body={data.content}
                count={data.count ?? 1}
                type={toNotificationType(data.preset)}
                icon={data.icon}
                customTheme={
                    data.preset === 'network'
                        ? {
                              color: Colors.Neutral7,
                              Icon: NetworkErrorIcon,
                              IconColor: Colors.Neutral7,
                          }
                        : undefined
                }
            />
        </DivItem>
    );

    if (position) {
        return ReactDOM.createPortal(element, document.body);
    }

    return element;
}

const DivItem = styled('div')({
    marginBottom: 20,
    top: 0,
    zIndex: 100,
});

function DroppableArea({ children }: React.PropsWithChildren) {
    const { setNodeRef, isOver, over } = useDroppable({
        id: 'left_bottom',
    });

    return (
        <DivRoot
            className={clsx({
                over: isOver,
            })}
            ref={setNodeRef}
            children={children}
        />
    );
}

function toNotificationType(preset: ToasterPreset | undefined): NotificationType {
    switch (preset) {
        case 'danger':
            return NotificationType.danger;
        case 'success':
            return NotificationType.success;
        case 'warning':
            return NotificationType.warning;
        case 'info':
            return NotificationType.info;
        case 'progress':
            return NotificationType.progress;
        default:
            return NotificationType.custom;
    }
}

const DivRoot = styled('div')({
    position: 'fixed',
    display: 'flex',
    flexDirection: 'column-reverse',
    margin: '0 0 10px 10px',
    padding: '20px 0 0 20px',
    zIndex: 100,
    minHeight: 100,
    bottom: 0,
    width: 400,

    '&:not(.over):empty': {
        pointerEvents: 'none',
    },

    '&.over': {
        outline: '4px solid var(--cm3)',
        outlineOffset: -8,
    },
});
