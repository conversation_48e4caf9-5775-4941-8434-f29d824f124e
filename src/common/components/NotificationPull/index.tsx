import { styled } from '@mui/material';
import groupBy from 'lodash/groupBy';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Toaster, useToastersController } from 'utils/toasters';
import Notification from '../../../common/components/Notification';
import { selectNotifications } from '../../../store/selectors/toasters.selector';
import { ZLayer } from '../../styles/ZLayer';
import { NotificationData } from './NotificationData';
import ToastersList from './ToastersList';

/**
 * Every time you modify the value of the new Notification property, it will be added to the pull,
 * as long as the key property of the instance does not already exist in the pull. This means that elements that are already in the pull cannot be edited.
 * @param newNotification add new Notification in the pull.
 * @deprecated use Notifications component instead
 */
export function NotificationPull({ newNotification }: { newNotification?: NotificationData }) {
    const [notificationShow, setnotificationShow] = useState<NotificationData[]>([]);

    useEffect(() => {
        console.debug('New Notification Set: ', newNotification);
        if (!newNotification || !newNotification.key) return;
        if (notificationShow.find((current) => current.key === newNotification.key)) return;
        setnotificationShow([newNotification, ...notificationShow]);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [newNotification]);
    return (
        <DivContainer>
            <DivRoot>
                {notificationShow.map((notification) => (
                    <DivItem key={`${notification.key}-item`}>
                        <Notification
                            {...notification}
                            onCloseNotification={() => {
                                setnotificationShow((ant) => {
                                    const notificationUpdate = ant.filter(
                                        (n) => n.key !== notification.key
                                    );
                                    return [...notificationUpdate];
                                });
                            }}
                        />
                    </DivItem>
                ))}
            </DivRoot>
        </DivContainer>
    );
}

/*  The version of Notifications pull reworked with Redux */
const Notifications = () => {
    const controller = useToastersController();
    const [toasters, setToasters] = useState<Toaster[]>([]);
    useEffect(() => controller.subscribeToChanges(setToasters), [controller]);
    const legacyNotifications = useSelector(selectNotifications);
    const groupedNotifications = useMemo(
        () => Object.values(groupBy(legacyNotifications, getLegacyToasterKey)),
        [legacyNotifications]
    );

    return (
        <DivContainer id="toasters-container">
            <ToastersList legacyNotifications={groupedNotifications} toasters={toasters} />
        </DivContainer>
    );
};

function getLegacyToasterKey(notification: NotificationData): string {
    const key = JSON.stringify([
        notification.body,
        notification.title,
        notification.link,
        notification.type,
    ]);
    return key;
}

function getReactNodeKey(node: React.ReactNode): string {
    if (typeof node === 'string') return node;
    if (node === undefined || node === null) return '';
    return Math.random().toString();
}

function getToasterKey(toaster: Toaster): string {
    const data = toaster.getData();

    if (toaster.getData().preset === 'progress') return toaster.id;

    const key = JSON.stringify([
        data.color,
        getReactNodeKey(data.content),
        data.preset,
        getReactNodeKey(data.title),
    ]);
    return key;
}

function reduceToasters(toasters: Toaster[]): ToasterGroup[] {
    const mapping = groupBy(toasters, getToasterKey);
    const groups: ToasterGroup[] = [];

    for (const group of Object.values(mapping)) {
        groups.push({
            toasters: group,
            id: 'g-' + group.map((x) => x.id).join('-'),
        });
    }

    return groups;
}

export type ToasterGroup = {
    toasters: Toaster[];
    id: string;
};

const DivContainer = styled('div')({
    position: 'fixed',
    left: 0,
    width: '100vw',
    zIndex: ZLayer.notification,
    bottom: 0,
});

const DivItem = styled('div')({
    marginBottom: 20,
});

export default Notifications;

const DivRoot = styled('div')({
    display: 'flex',
    position: 'fixed',
    maxHeight: '100vh',
    flexDirection: 'column',
    zIndex: ZLayer.notification,
    justifyContent: 'flex-end',
    paddingLeft: 30,
    paddingRight: 30,
    bottom: 0,
});
