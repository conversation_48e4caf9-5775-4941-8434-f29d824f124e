import { Colors } from '../../styles/Colors';
import { IconProps } from '../Icons/Icon';
import { INotificationProps, NotificationType } from '../Notification/INotificationProps';
export class NotificationData implements INotificationProps {
    key: string;
    bodyElement?: (props: any) => JSX.Element;

    constructor(
        public body: string,
        public title: string,
        public type: NotificationType,
        key?: string
    ) {
        if (key != null) this.key = key;
        else {
            const random = `${type}-${new Date().getTime()}-${Math.random()}`;
            this.key = random;
        }
    }
    customTheme?: {
        Icon: (props: IconProps) => JSX.Element;
        IconColor: Colors;
        color: Colors;
    };
    expiresAt?: number;
    link?: { label: string; url: string };
    currentPosition: number = 0;
    onCloseNotification = () => {};
}
