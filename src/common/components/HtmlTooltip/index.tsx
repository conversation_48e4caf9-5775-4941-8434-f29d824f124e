import { Fade, PopperProps, styled } from '@mui/material';
import Tooltip, { TooltipProps } from '@mui/material/Tooltip';
import useResizeObserver from '@react-hook/resize-observer';
import React, { useRef, useState } from 'react';

// need to use some TS mumbo-jambo because PopperInstance is not available directly since it's not a direct dependency
type PopperInstanceRef = PopperProps['popperRef'] & {};
type RefOf<T> = T extends React.Ref<infer I> ? I : never;
type PopperInstance = RefOf<PopperInstanceRef>;

export const HtmlTooltip = React.forwardRef(({ title, ...props }: TooltipProps, ref) => {
    const popperRef = useRef<PopperInstance | null>(null);
    const [rootEl, setRootEl] = useState<HTMLElement | null>(null);

    // trigger popper update when content of the tooltip resizes
    useResizeObserver(rootEl, () => {
        popperRef.current?.update();
    });

    return (
        <Tooltip
            ref={ref}
            slots={{
                tooltip: StyledTooltip,
                transition: Fade,
            }}
            slotProps={{
                popper: {
                    popperRef: popperRef,
                },
                transition: {
                    timeout: 120,
                },
            }}
            title={
                React.isValidElement<{ ref?: React.Ref<HTMLElement> }>(title)
                    ? React.cloneElement(title, {
                          ref: setRootEl,
                      })
                    : title
            }
            {...props}
        />
    );
});

const StyledTooltip = styled('div')(({ theme }) => ({
    backgroundColor: '#FFF',
    color: 'rgba(0, 0, 0, 0.87)',
    fontSize: theme.typography.pxToRem(12),
    padding: 0,
    borderRadius: 16,
    boxShadow: '0 4px 20px 0 rgba(229, 231, 234, 0.84)',
}));
