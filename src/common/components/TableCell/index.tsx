import { styled } from '@mui/material';
import TableCellUI, { TableCellProps } from '@mui/material/TableCell';
import { CSSProperties } from 'react';

export interface CustomTableCellProps extends TableCellProps {
    customStyles?: CSSProperties;
}

const TableCell = styled(TableCellUI)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    padding: '10px',
    textAlign: 'left',
    color: theme.palette.neutral[7],
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,

    '@media (min-width: 1920px)': {
        paddingLeft: 16,
        paddingTop: 5,
        paddingBottom: 5,
    },
}));

export default TableCell;
