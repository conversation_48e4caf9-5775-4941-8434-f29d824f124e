import { Box, styled } from '@mui/material';
import { InfoIcon } from '../Icons/InfoIcon';

export type NoteProps = {
    children?: React.ReactNode;
    showInfoIcon?: boolean;
};

export default function Note({ children, showInfoIcon = true }: NoteProps) {
    return (
        <Box display="flex" alignItems="center" gap={0.5}>
            {showInfoIcon && (
                <DivIconContainer>
                    <InfoIcon size={14} fill={'var(--neutral1)'} />
                </DivIconContainer>
            )}
            <StyledSpan>{children}</StyledSpan>
        </Box>
    );
}

const StyledSpan = styled('span')(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[5],
    fontWeight: 400,
}));

const DivIconContainer = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[5],
    width: 14,
    height: 14,
    borderRadius: '50%',
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[1],
    fontWeight: 400,
}));
