import { styled } from '@mui/material';
import { TimeSpan } from 'api/utils/format';
import moment from 'moment';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import GridLayout, { ItemCallback, Layout, ReactGridLayoutProps } from 'react-grid-layout';
import { ItemReplacedCallback, ItemStartDraggingCallback, RenderItemFunction } from '..';
import { useSchedulerContext } from '../context';
import styles from './css.module.css';

const params = new URLSearchParams(window.location.search);
const doNotUseCSSTransforms = params.get('debug.doNotUseCSSTransforms') === '1';

export type GridCellBase = {
    key: string;
    static: boolean;
    groupKey: string;
    timestamp: moment.MomentInput;
    minutesDuration: number;
    isResizable?: boolean;
};

export type GridStaticCell = GridCellBase & {
    static: true;
};

export type GridCell<T> = GridCellBase & {
    data: T;
};

/**
 * IGridCanvasController allows you to modify the layout of the grid.
 * Please not that this will not modify the data from which the layout
 * is initially created. So please use with caution.
 */
export interface IGridCanvasController {
    replace(item: Layout, newItem: Layout): void;
}

class Controller implements IGridCanvasController {
    private readonly _setLayout: (m: (layout: Layout[]) => Layout[]) => void;

    constructor(setLayout: (m: (layout: Layout[]) => Layout[]) => void) {
        this._setLayout = setLayout;
    }
    replace(newItem: GridLayout.Layout): void {
        this._setLayout((l) => l.map((x) => (x.i === newItem.i ? newItem : x)));
    }
}

export type GridCanvasCellsProps<T, G> = {
    cells: GridCell<T>[];
    renderItem: RenderItemFunction<T>;
    vertical: boolean;

    /**
     * children elements placed before GridLayout element
     */
    children?: React.ReactNode;

    staticCells?: GridStaticCell[];
    onItemReplaced?: ItemReplacedCallback<T, G>;
    onItemStartDragging?: ItemStartDraggingCallback<T>;
    onItemStopDragging?: () => void;
    onItemStartResizing?: (item: T) => void;
    onItemStopResizing?: () => void;
    onItemResized?: (item: T, newPosition: TimeSpan, newDuration: TimeSpan) => void;
    controllerRef?: React.Ref<IGridCanvasController>;
    allowOverlap?: boolean;
    isDraggable?: boolean;
    isResizable?: boolean;
    snappingFactor?: number;

    /**
     * If set to true allow for draggable elements to be dropped into the scheduler.
     */
    isDroppable?: boolean;

    /**
     * Called when an item is dropped into the scheduler.
     */
    onDrop?: (positionTimestamp: number, group: G, event: Event) => void;
    droppedItemDurationInMinutes?: () => number | false;
    hideTitleFirstInterval?: boolean;
};

const getDefaultDroppedItemDuration = () => 60;

function _GridCanvasCells<T, G>({
    cells,
    renderItem,
    vertical,
    children,
    staticCells,
    onItemReplaced,
    onItemStartDragging,
    onItemStopDragging,
    onItemStartResizing,
    onItemStopResizing,
    onItemResized,
    snappingFactor = 1,
    allowOverlap = false,
    isDraggable = true,
    isResizable = true,
    droppedItemDurationInMinutes = getDefaultDroppedItemDuration,
    isDroppable = false,
    onDrop,
}: GridCanvasCellsProps<T, G>) {
    const { grouping, dimensions, interval } = useSchedulerContext();
    const { rowHeight, columnWidth, stepLengthInMinutes } = dimensions;
    const { groups } = grouping;

    const filteredCells = useMemo(
        () =>
            cells.filter((c) =>
                moment(c.timestamp).isBetween(interval.from, interval.to, undefined, '[]')
            ),
        [cells, interval.from, interval.to]
    );

    const from = moment(interval.from);
    const to = moment(interval.to);

    const timelineSegmentsCount = Math.ceil(to.diff(from, 's') / 60 / stepLengthInMinutes);
    const minutesPerUnit = 5;
    const unitsInHour = Math.ceil(60 / minutesPerUnit); // 1 unit = 5 minutes, this value MUST be integer and > 1
    const unitsInSegment = Math.ceil((stepLengthInMinutes / 60) * unitsInHour);
    const timelineSize = timelineSegmentsCount * (vertical ? rowHeight : columnWidth);
    const timelineSizeInUnits = timelineSegmentsCount * unitsInSegment;
    const actualRowHeight = vertical ? rowHeight / unitsInSegment : rowHeight;

    const rowsCount = vertical ? timelineSizeInUnits : groups.length;
    const columnsCount = vertical ? groups.length : timelineSizeInUnits;
    const gridWidth = vertical ? columnWidth * groups.length : timelineSize;

    const onDrag: ItemCallback = useCallback(
        (_layout, _old, newItem) => {
            if (onItemStartDragging) {
                const cell = cells.find((x) => x.key === newItem.i);
                if (cell) {
                    onItemStartDragging(cell.data);
                }
            }
        },
        [cells, onItemStartDragging]
    );

    const onDragStop = (
        _layout: Layout[],
        oldItem: Layout,
        newItem: Layout,
        _placeholder: Layout,
        _event: MouseEvent,
        _element: HTMLElement
    ) => {
        if (onItemStopDragging) {
            onItemStopDragging();
        }
        if (!onItemReplaced) return;
        if (oldItem.x === newItem.x && oldItem.y === newItem.y) {
            return;
        }

        const cell = cells.find((x) => x.key === newItem.i);
        if (!cell) return;
        const item = cell.data;

        const newTimelineCoord = vertical ? newItem.y : newItem.x;
        const newGroupIndex = vertical ? newItem.x : newItem.y;
        const oldTimelineCoord = vertical ? oldItem.y : oldItem.x;

        let newPosition = TimeSpan.fromParts(newTimelineCoord / unitsInHour);
        const newGroup = groups.length ? groups[newGroupIndex].data : (undefined as never);
        const oldPosition = TimeSpan.fromParts(oldTimelineCoord / unitsInHour);

        if (snappingFactor && snappingFactor > 0) {
            const integerSnappingFactor = Math.round(snappingFactor);
            const snappedPosition = new TimeSpan(
                Math.floor(newPosition.totalSeconds / 60 / integerSnappingFactor) *
                    integerSnappingFactor *
                    60
            );
            if (snappedPosition.totalSeconds !== newPosition.totalSeconds) {
                newPosition = snappedPosition;
                const newTimelineCoord = (snappedPosition.totalSeconds / 3600) * unitsInHour;
                controller.replace({
                    ...newItem,
                    ...(vertical ? { y: newTimelineCoord } : { x: newTimelineCoord }),
                });
            }
        }

        onItemReplaced(item, newPosition, newGroup, oldPosition, {
            cancel: () => {
                controller.replace(newItem);
            },
        });
    };

    const handleDrop: ReactGridLayoutProps['onDrop'] & {} = useCallback(
        (_layout, item, event) => {
            const timelineCoord = vertical ? item.y : item.x;
            const groupIndex = vertical ? item.x : item.y;

            const start = +from
                .clone()
                .add('m', timelineCoord * minutesPerUnit)
                .toDate();

            const group = groups[groupIndex];

            // TODO (MB) optimistically update layout
            onDrop?.(start, group.data as G, event);
        },
        [onDrop, from, groups, vertical]
    );

    const handleResizeStart: ItemCallback = useCallback(
        (_layout, _old, newItem) => {
            if (onItemStartResizing) {
                const cell = cells.find((x) => x.key === newItem.i);
                if (cell) {
                    onItemStartResizing(cell.data);
                }
            }
        },
        [cells, onItemStartResizing]
    );

    const handleResizeStop: ItemCallback = useCallback(
        (_layout, oldItem, newItem) => {
            if (onItemStopResizing) {
                onItemStopResizing();
            }

            if (!onItemResized) return;

            if (
                oldItem.x === newItem.x &&
                oldItem.y === newItem.y &&
                oldItem.w === newItem.w &&
                oldItem.h === newItem.h
            ) {
                return;
            }

            const cell = cells.find((x) => x.key === newItem.i);
            if (!cell) return;
            const item = cell.data;

            const newTimelineCoord = vertical ? newItem.y : newItem.x;
            const newTimelineSize = vertical ? newItem.h : newItem.w;

            const newPosition = TimeSpan.fromParts(newTimelineCoord / unitsInHour);
            const newDuration = TimeSpan.fromParts(newTimelineSize / unitsInHour);

            onItemResized(item, newPosition, newDuration);
        },
        [cells, onItemStopResizing, onItemResized, unitsInHour, vertical]
    );

    const calculatedLayout = useMemo(() => {
        const from = moment(interval.from);
        const to = moment(interval.to);
        const availableHandles = (vertical ? ['s', 'n'] : ['w', 'e']) as Array<
            's' | 'n' | 'w' | 'e'
        >;

        const cells = (filteredCells as GridCellBase[]).concat(staticCells ?? []).map((cell) => {
            const startDate = moment(cell.timestamp);
            const startInHrs = startDate.diff(from, 's') / 3600;
            const durationInHrs = cell.minutesDuration / 60;
            const maxDurationInHrs = to.diff(startDate, 'minute') / 60;
            const groupId = cell.groupKey;
            const timelineCoord = startInHrs * unitsInHour;
            const timelineSize = Math.min(durationInHrs, maxDurationInHrs) * unitsInHour;
            const groupIndex = groups.map((g) => g.key).indexOf(groupId);

            return {
                i: cell.key,
                static: cell.static,
                x: vertical ? groupIndex : timelineCoord,
                y: vertical ? timelineCoord : groupIndex,
                w: vertical ? 1 : timelineSize,
                h: vertical ? timelineSize : 1,
                isResizable: cell.isResizable,
                resizeHandles: cell.isResizable ? availableHandles : undefined,
            };
        });

        return cells;
    }, [filteredCells, unitsInHour, groups, interval.from, interval.to, staticCells, vertical]);

    const [layout, setLayout] = useState<Layout[]>(calculatedLayout);
    const controller = useMemo(() => new Controller(setLayout), [setLayout]);

    useEffect(() => {
        if (layout !== calculatedLayout) {
            setLayout(calculatedLayout);
        }
    }, [calculatedLayout, layout]);

    const onDropDragOver = useCallback(
        (
            _e: GridLayout.DragOverEvent
        ):
            | {
                  w?: number;
                  h?: number;
              }
            | false
            | undefined => {
            const duration = droppedItemDurationInMinutes();

            if (duration === false) {
                return false;
            }

            const timelineUnitSize = duration / minutesPerUnit;

            return {
                h: vertical ? timelineUnitSize : 1,
                w: vertical ? 1 : timelineUnitSize,
            };
        },
        [droppedItemDurationInMinutes, vertical]
    );

    return (
        <>
            {children}
            <GridLayout
                maxRows={rowsCount}
                cols={columnsCount}
                rowHeight={actualRowHeight}
                width={gridWidth}
                isBounded={false}
                compactType={null}
                preventCollision
                containerPadding={[0, 0]}
                margin={[0, 0]}
                onDragStop={onDragStop}
                onDrag={onDrag}
                onDrop={handleDrop}
                layout={layout}
                onLayoutChange={setLayout}
                useCSSTransforms={!doNotUseCSSTransforms}
                className={styles.gridLayout}
                allowOverlap={allowOverlap}
                isDraggable={isDraggable}
                isDroppable={isDroppable}
                onDropDragOver={onDropDragOver}
                isResizable={isResizable}
                onResizeStart={handleResizeStart}
                onResizeStop={handleResizeStop}
            >
                {staticCells &&
                    staticCells.map((cell) => {
                        return <div key={cell.key} />;
                    })}
                {filteredCells.map((cell) => {
                    return (
                        <DivItem className={styles.cell} key={cell.key}>
                            {renderItem(cell.data)}
                        </DivItem>
                    );
                })}
            </GridLayout>
        </>
    );
}

const DivItem = styled('div')({
    zIndex: 1,

    '& > .react-resizable-handle': {
        position: 'absolute',
    },

    '& > .react-resizable-handle.react-resizable-handle-w': {
        left: 0,
    },

    '& > .react-resizable-handle.react-resizable-handle-e': {
        right: 0,
    },

    '& > .react-resizable-handle.react-resizable-handle-n': {
        top: 0,
    },

    '& > .react-resizable-handle.react-resizable-handle-s': {
        bottom: 0,
    },

    '& > .react-resizable-handle.react-resizable-handle-w, & > .react-resizable-handle.react-resizable-handle-e':
        {
            top: 0,
            cursor: 'ew-resize',
            width: '10px',
            height: '100%',
        },

    '& > .react-resizable-handle.react-resizable-handle-n, & > .react-resizable-handle.react-resizable-handle-s':
        {
            left: 0,
            cursor: 'ns-resize',
            width: '100%',
            height: '10px',
        },
});

export const GridCanvasCells = memo(_GridCanvasCells) as typeof _GridCanvasCells;
