import { styled } from '@mui/material';
import moment from 'moment';
import { useEffect, useMemo, useRef } from 'react';
import { useSchedulerContext } from '../../context';

export type CursorProps = {
    vertical: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

export default function Cursor({ vertical, ...props }: CursorProps) {
    const {
        interval: { from, to },
        dimensions: { pixelsPerMinuteRatio },
    } = useSchedulerContext();

    const divElement = useRef<HTMLDivElement | null>(null);
    const div = useMemo(
        () => (
            <DivCursor
                {...props}
                ref={divElement}
                style={
                    vertical
                        ? {
                              top: 'var(--wp-scheduler-cursor-offset, 0px)',
                              left: 0,
                              height: 2,
                              width: '100%',
                          }
                        : {
                              top: 0,
                              left: 'var(--wp-scheduler-cursor-offset, 0px)',
                              height: '100%',
                              width: 2,
                          }
                }
            />
        ),
        [props, vertical]
    );
    useEffect(() => {
        const start = moment(from);
        const end = moment(to);

        const updateFn = () => {
            if (!divElement.current) return;
            const maxMinutes = end.diff(start, 's') / 60;
            const minutes = moment().diff(start, 's') / 60;

            if (minutes < 0 || minutes > maxMinutes) {
                divElement.current.style.display = 'none';
            } else {
                divElement.current.style.removeProperty('display');
                const offset = Math.max(
                    0,
                    Math.round(pixelsPerMinuteRatio * Math.min(minutes, maxMinutes)) // minus 2 because cursor width is 2px
                );

                divElement.current.style.setProperty('--wp-scheduler-cursor-offset', `${offset}px`);
                divElement.current.setAttribute('data-time', moment().toISOString());
            }
        };
        updateFn();
        const interval = setInterval(updateFn, 300);
        return () => clearInterval(interval);
    }, [from, pixelsPerMinuteRatio, to]);

    return div;
}

const DivCursor = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.primary.main,
    position: 'absolute',
    zIndex: 2,
    pointerEvents: 'none',
}));
