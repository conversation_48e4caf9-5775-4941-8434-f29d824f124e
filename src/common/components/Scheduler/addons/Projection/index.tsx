import moment from 'moment';
import { useSchedulerContext } from '../../context';

export type ScheduleProjectionItemProps<T> = {
    value: T;
    groupKey: string | null;
    left: number;
    top: number;
    width: number;
    height: number | string;
    outerContainer?: HTMLElement | null;
    vertical: boolean;
    singleView: boolean;
};

export type SchedulerProjectionProps<P> = {
    items: P[];
    getKey: (item: P) => string;
    getDuration: (item: P) => number;
    getStartsAt: (item: P) => number;
    getGroupKey: (item: P) => string;
    itemComponent: React.ComponentType<ScheduleProjectionItemProps<P>>;
    outerContainer?: HTMLElement | null;
    vertical: boolean;
    singleView: boolean;
};

export default function Projection<T>({
    items,
    itemComponent: Item,
    getStartsAt,
    getKey,
    getGroupKey,
    getDuration,
    outerContainer,
    vertical,
    singleView,
}: SchedulerProjectionProps<T>) {
    const { grouping, dimensions, interval } = useSchedulerContext();
    const groupKeys = grouping.groups.map((x) => x.key);
    const fromTs = +moment(interval.from).toDate();

    return (
        <>
            {items.map((x) => {
                const groupKey = getGroupKey(x);
                const durationSize = dimensions.pixelsPerMinuteRatio * getDuration(x);
                const startTimelinePosition =
                    (dimensions.pixelsPerMinuteRatio * (getStartsAt(x) - fromTs)) / 60000;
                const groupIndex = Math.max(0, groupKeys.indexOf(groupKey));
                const top = vertical ? startTimelinePosition : groupIndex * dimensions.rowHeight;
                const height = vertical ? durationSize : dimensions.rowHeight;
                const left = vertical ? groupIndex * dimensions.columnWidth : startTimelinePosition;
                const width = vertical ? dimensions.columnWidth : durationSize;

                return (
                    <Item
                        key={getKey(x)}
                        value={x}
                        groupKey={groupKey}
                        width={width}
                        height={height}
                        left={left}
                        top={top}
                        outerContainer={outerContainer}
                        vertical={vertical}
                        singleView={singleView}
                    />
                );
            })}
        </>
    );
}
