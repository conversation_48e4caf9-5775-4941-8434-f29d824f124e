.inner {
    max-width: 100%;
    width: 100%;
    max-height: var(--wps-inner-max-height);
}

.inner [data-overlayscrollbars-viewport] {
    position: relative;
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto 1fr;
}

.innerVerticalGrid [data-overlayscrollbars-viewport] {
    grid-template-columns: auto 1fr;
    grid-template-rows: 1fr auto;
}

.innerVerticalGrid.withoutStickyPart [data-overlayscrollbars-viewport] {
    grid-template-columns: auto;
    grid-template-rows: 1fr auto;
}

.inner :global(.os-scrollbar-horizontal) {
    width: calc(100% - var(--wps-side-width, 0px) - 5px);
    margin-left: var(--wps-side-width, 0px);
}
.stickyBottomRow {
    position: sticky;
    bottom: 0px;
    height: var(--wps-row-height);
    border-top: 1px solid var(--neutral3);
    background-color: var(--neutral1);
    grid-column: span 2;
    z-index: 3;
    display: flex;
}

.stickyBottomRow .sbrHeader {
    border-right: 1px solid var(--neutral3);
    width: var(--wps-side-width);
    min-width: var(--wps-side-width);
    height: 100%;
    left: 0px;
    position: sticky;
    background-color: var(--neutral1);
    z-index: 5;
}

.stickyBottomRow .sbrBody {
    width: var(--wps-body-width);
    max-width: var(--wps-body-width);
    min-width: var(--wps-body-width);
}

.sbcHeader {
    border-left: 1px solid var(--neutral3);
    border-bottom: 1px solid var(--neutral3);
    box-sizing: border-box;
    width: 100%;
    min-width: var(--wps-column-width);
    height: var(--wps-header-height);
    right: 0.5px;
    top: 0px;
    position: sticky;
    background-color: var(--neutral1);
    z-index: 5;
}

.sbcBody {
    width: 100%;
    min-width: var(--wps-column-width);
    height: var(--wps-body-height);
    max-height: var(--wps-body-height);
    min-height: var(--wps-body-height);
}

/* .body :global(.react-resizable-handle) {
    height: 100%;
    width: 8px;
    left: 0;
    top: 0;
    position: absolute;
    box-sizing: border-box;
    background-color: rgba(0, 0, 255, 0.2);
    cursor: w-resize;
}

.body :global(.react-resizable-handle-e) {
    right: 0;
    left: initial;
    cursor: e-resize;
} */
