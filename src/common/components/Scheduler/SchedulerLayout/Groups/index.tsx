import { styled } from '@mui/material';
import React, { useRef } from 'react';

function defaultRenderGroup<G>(g: G) {
    return `${g}`;
}

export type GroupsProps<G> = {
    vertical: boolean;
    hasExpandedLastGroup: boolean;
    renderGroup?: (g: G, v: boolean) => React.ReactNode;
    getGroupKey: (g: G) => string;
    groups: G[];
};

export default function Groups<G>({
    vertical,
    hasExpandedLastGroup,
    groups,
    getGroupKey,
    renderGroup = defaultRenderGroup,
}: GroupsProps<G>) {
    const containerRef = useRef<HTMLDivElement | null>(null);

    return (
        <DivGroups
            style={
                vertical
                    ? {
                          flexDirection: 'row',
                      }
                    : {
                          flexDirection: 'column',
                      }
            }
            ref={containerRef}
        >
            {groups.map((g) => {
                const groupKey = getGroupKey(g);
                return (
                    <Group<G>
                        key={groupKey}
                        groupKey={groupKey}
                        data={g}
                        renderGroup={renderGroup}
                        vertical={vertical}
                        hasExpandedLastGroup={hasExpandedLastGroup}
                    />
                );
            })}
        </DivGroups>
    );
}

const DivGroups = styled('div')({
    display: 'flex',
    justifyContent: 'stretch',
    alignItems: 'stretch',
    height: '100%',
});

type GroupProps<G> = {
    data: G;
    renderGroup: (g: G, v: boolean) => React.ReactNode;
    vertical: boolean;
    hasExpandedLastGroup: boolean;
    groupKey: string;
};

function _Group<G>({ data, renderGroup, vertical, hasExpandedLastGroup, groupKey }: GroupProps<G>) {
    return (
        <DivGroup
            data-group-key={groupKey}
            vertical={vertical}
            hasExpandedLastGroup={hasExpandedLastGroup}
            style={
                vertical
                    ? {
                          width: 'var(--wps-column-width)',
                          minWidth: 'var(--wps-column-width)',
                          textAlign: 'center',
                      }
                    : {
                          height: 'var(--wps-row-height)',
                      }
            }
        >
            {renderGroup(data, vertical)}
        </DivGroup>
    );
}

const Group = React.memo(_Group) as typeof _Group;

const DivGroup = styled('div')<{ vertical: boolean; hasExpandedLastGroup: boolean }>(
    ({ theme, vertical, hasExpandedLastGroup }) => ({
        boxSizing: 'border-box',
        backgroundColor: theme.palette.neutral[1],
        border: `solid ${theme.palette.custom.gray}`,
        borderWidth: '0 1px 1px 0',
        overflow: 'hidden',

        '&:last-child': {
            ...(vertical
                ? {
                      borderRight: 'none',
                      width: hasExpandedLastGroup ? '100% !important' : 'var(--wps-column-width)',
                  }
                : { borderBottom: 'none' }),
        },
    })
);
