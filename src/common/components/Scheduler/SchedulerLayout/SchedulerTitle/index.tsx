import { styled } from '@mui/material';

const SchedulerTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    fontSize: 16,
    color: theme.palette.neutral[7],
    height: 'var(--wps-header-height)',
    left: 0,
    top: 0,
    paddingRight: '30px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'left',
    position: 'absolute',
    minWidth: 'calc(var(--wps-side-width) + 10px)',
    maxWidth: 'calc(var(--wps-side-width) * 2 - 20px)',
    border: 'solid var(--neutral4)',
    borderWidth: '1px 0 0 1px',
    borderRadius: '10px 0 0 0',
    background: `linear-gradient(to right, ${theme.palette.neutral[1]} 90%, transparent)`,
    zIndex: 5,
    boxSizing: 'border-box',
    textAlign: 'left',
    paddingLeft: 13,
}));

export default SchedulerTitle;
