import { styled, useTheme } from '@mui/material';
import clsx from 'clsx';
import moment, { Moment } from 'moment';
import { OverlayScrollbars, PartialOptions } from 'overlayscrollbars';
import { useOverlayScrollbars } from 'overlayscrollbars-react';
import { CSSProperties, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import GridCanvas, { GridCanvasProps } from '../GridCanvas';
import {
    ScrollbarController,
    useSchedulerScrollbarController,
} from '../GridCanvas/scrollbarController';
import { useSchedulerContext } from '../context';
import Groups from './Groups';
import SchedulerTitle from './SchedulerTitle';
import Timeline from './Timeline';
import { SetOffset } from './common';
import styles from './css.module.css';

export type SchedulerLayoutProps<T, G> = Omit<GridCanvasProps<T, G>, '_setOffsetRef'> & {
    headerHeight: number;
    sideWidth: number;
    maxHeight?: number;
    title?: React.ReactNode;
    children?: React.ReactNode;
    renderTimeline?: (time: Moment) => React.ReactNode;

    groupingOptions: {
        getGroupKey: (g: G) => string;
        renderGroup?: (g: G, v: boolean) => React.ReactNode;
    };
    className?: string;
    rootRef: React.ForwardedRef<HTMLDivElement>;

    stickyPart?: {
        header?: React.ReactNode;
        content: React.ReactNode;
    };
};

const osOptions: PartialOptions = {
    // update: { debounce: [300, 2000] },
    scrollbars: { theme: 'os-theme-wps' },
    update: {
        ignoreMutation(_mutation) {
            // ignore all mutation to prevent excessive updates
            // IMPORTANT: we will have to manually update scrollbars
            return true;
        },
    },
};

export default function SchedulerLayout<T, G>({
    sideWidth,
    headerHeight,
    maxHeight,
    title,
    renderTimeline,
    showCursor,
    groupingOptions,
    children,
    className,
    rootRef,
    isDroppable,
    stickyPart,
    hideTitleFirstInterval,
    ...props
}: SchedulerLayoutProps<T, G>) {
    const theme = useTheme();
    const { grouping, dimensions, interval } = useSchedulerContext();
    const groups = useMemo(() => grouping.groups.map((g) => g.data), [grouping.groups]) as G[];
    const setHeaderOffsetRef = useRef<SetOffset | null>(null);
    const setSideOffsetRef = useRef<SetOffset | null>(null);
    const [isDropping, setDropping] = useState(false);

    const onScroll = useCallback(
        (e: Event) => {
            if (!(e.target instanceof HTMLElement)) {
                return;
            }

            if (props.vertical) {
                setSideOffsetRef.current?.(-e.target.scrollTop);
            } else {
                setHeaderOffsetRef.current?.(-e.target.scrollLeft);
            }
        },
        [props.vertical]
    );

    const [initializeOs] = useOverlayScrollbars({
        options: osOptions,
        events: {
            scroll(_instance, event) {
                onScroll(event);
            },
            initialized: (os) => {
                setOverlayScrollbars(os);
            },
            destroyed: () => setOverlayScrollbars(null),
        },
    });
    const [overlayScrollbars, setOverlayScrollbars] = useState<OverlayScrollbars | null>(null);

    const controllerProvider = useSchedulerScrollbarController();
    const controller = useMemo(() => {
        if (!overlayScrollbars) return null;
        const fromTimestamp = +moment(interval.from).toDate();
        const element = overlayScrollbars.elements().viewport;

        const controller = new ScrollbarController(
            element,
            fromTimestamp,
            dimensions.pixelsPerMinuteRatio,
            dimensions.sideWidth,
            props.vertical
        );

        return controller;
    }, [
        interval.from,
        overlayScrollbars,
        dimensions.pixelsPerMinuteRatio,
        dimensions.sideWidth,
        props.vertical,
    ]);

    useEffect(() => {
        controllerProvider?.setContext(controller);
    }, [controller, controllerProvider]);

    const skippedFirstRenderForElement = useRef<HTMLElement | null>(null);
    const fromTimestamp = +moment(interval.from).toDate();
    const toTimestamp = +moment(interval.to).toDate();

    useEffect(() => {
        if (!overlayScrollbars) return;
        const rootElement = overlayScrollbars.elements().target;

        // skip first render after root element changes
        if (skippedFirstRenderForElement.current !== rootElement) {
            skippedFirstRenderForElement.current = rootElement;
            return;
        }

        // assume that scrollbars might not be ready to be updated right away and just wait a bit
        setTimeout(() => {
            // if planning is huge updating scrollbar might actually take a while so wait for idle
            window.requestIdleCallback(() => {
                overlayScrollbars.update(true); // force update (that's what true means)
            });
        }, 50);
    }, [
        // update scrollbar whenever one of these changes
        overlayScrollbars,
        dimensions.sideWidth,
        dimensions.pixelsPerMinuteRatio,
        dimensions.rowHeight,
        dimensions.stepLengthInMinutes,
        dimensions.columnWidth,
        fromTimestamp,
        toTimestamp,
    ]);

    const rootEl = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        const { current: root } = rootEl;
        const viewport = root?.querySelector('[data-os-viewport]');

        if (viewport instanceof HTMLElement && root) {
            initializeOs({
                target: root,
                elements: {
                    viewport,
                },
            });
        }
    }, [initializeOs]);

    return (
        <SchedulerLayoutRoot
            title={title}
            vertical={props.vertical}
            headerHeight={headerHeight}
            sideWidth={sideWidth}
            maxHeight={maxHeight}
            className={className}
            rootRef={rootRef}
        >
            <TimelineWrapper
                style={
                    props.vertical
                        ? {
                              height: 'calc(var(--wps-inner-max-height))',
                              width: 'var(--wps-side-width)',
                              left: 0,
                              paddingTop: 'var(--wps-header-height)',
                              borderRight: `1px solid ${theme.palette.neutral[3]}`,
                          }
                        : {
                              height: 'var(--wps-header-height)',
                              width: '100%',
                              top: 0,
                              paddingLeft: 'var(--wps-side-width)',
                              borderBottom: `1px solid ${theme.palette.neutral[3]}`,
                          }
                }
            >
                <Timeline
                    vertical={props.vertical}
                    renderTimelineCell={renderTimeline}
                    setOffsetRef={props.vertical ? setSideOffsetRef : setHeaderOffsetRef}
                    hideTitleFirstInterval={hideTitleFirstInterval}
                />
            </TimelineWrapper>

            <div
                ref={rootEl}
                data-scroll-parent
                onDragStart={() => {
                    setDropping(true);
                }}
                onDragEnd={() => {
                    setDropping(false);
                }}
                className={clsx(
                    styles.inner,
                    props.vertical && styles.innerVerticalGrid,
                    props.vertical && !stickyPart && styles.withoutStickyPart,
                    isDropping && 'wps-dropping'
                )}
            >
                <div data-os-viewport>
                    <GroupsWrapper
                        style={
                            props.vertical
                                ? {
                                      top: 0,
                                      height: 'var(--wps-header-height)',
                                      minHeight: 'var(--wps-header-height)',
                                      width: '100%',
                                  }
                                : {
                                      left: 0,
                                      width: 'var(--wps-side-width)',
                                      minWidth: 'var(--wps-side-width)',
                                      height: '100%',
                                  }
                        }
                    >
                        {groupingOptions && groups.length && !grouping.hideGroups && (
                            <Groups
                                vertical={props.vertical}
                                groups={groups}
                                getGroupKey={groupingOptions.getGroupKey}
                                renderGroup={groupingOptions.renderGroup}
                                hasExpandedLastGroup={props.hasExpandedLastColumn}
                            />
                        )}
                    </GroupsWrapper>
                    {stickyPart && props.vertical && (
                        <div className={styles.sbcHeader}>{stickyPart.header}</div>
                    )}
                    <DivBody
                        style={
                            props.vertical
                                ? {
                                      minHeight: 'var(--wps-body-height)',
                                      maxHeight: 'var(--wps-body-height)',
                                  }
                                : {
                                      minWidth: 'var(--wps-body-width)',
                                      maxWidth: 'var(--wps-body-width)',
                                  }
                        }
                    >
                        {groups.length > 0 && (
                            <GridCanvas<T, G>
                                isDroppable={isDroppable}
                                showCursor={showCursor}
                                {...props}
                            >
                                {children}
                            </GridCanvas>
                        )}
                    </DivBody>

                    {stickyPart &&
                        (props.vertical ? (
                            <div className={styles.sbcBody}>{stickyPart.content}</div>
                        ) : (
                            <div className={styles.stickyBottomRow}>
                                <div className={styles.sbrHeader}>{stickyPart.header}</div>

                                <div className={styles.sbrBody}>{stickyPart.content}</div>
                            </div>
                        ))}
                </div>
            </div>
        </SchedulerLayoutRoot>
    );
}

const TimelineWrapper = styled('div')(({ theme }) => ({
    zIndex: 4,
    color: theme.palette.neutral[7],
    backgroundColor: theme.palette.neutral[1],
    position: 'sticky',
    overflow: 'hidden',
}));

const GroupsWrapper = styled('div')({
    zIndex: 3,
    position: 'sticky',
});

const DivBody = styled('div')({
    position: 'relative',
    overflow: 'hidden',
});

export const schedulerLayoutClasses = {
    layout: 'SchedulerLayout-root',
    title: 'SchedulerLayout-title',
};

type SchedulerLayoutRootProps = {
    headerHeight: number;
    maxHeight?: number;
    sideWidth: number;
    title?: React.ReactNode;
    vertical: boolean;
    children?: React.ReactNode;
    className?: string;
    rootRef: React.ForwardedRef<HTMLDivElement>;
};

function SchedulerLayoutRoot({
    headerHeight,
    maxHeight,
    sideWidth,
    children,
    title,
    vertical,
    className,
    rootRef,
}: SchedulerLayoutRootProps) {
    const { dimensions, grouping } = useSchedulerContext();

    const totalDurationInMinutes = dimensions.stepLengthInMinutes * dimensions.stepsCount;

    return (
        <DivRoot
            ref={rootRef}
            className={className}
            style={
                {
                    '--wps-header-height': `${
                        vertical && grouping.hideGroups ? 0 : headerHeight
                    }px`,
                    '--wps-side-width': `${!vertical && grouping.hideGroups ? 0 : sideWidth}px`,
                    '--wps-total-duration': totalDurationInMinutes,
                    '--wps-pixels-per-minute': `${dimensions.pixelsPerMinuteRatio}px`,
                    '--wps-row-height': `${dimensions.rowHeight}px`,
                    '--wps-column-width': `${dimensions.columnWidth}px`,
                    '--wps-viewport-size': `${
                        dimensions.pixelsPerMinuteRatio * totalDurationInMinutes
                    }px`,
                    '--wps-inner-max-height': maxHeight
                        ? `${vertical ? maxHeight : maxHeight - headerHeight}px`
                        : 'calc(100vh - 240px)',
                    '--wps-groups-count': Math.max(1, grouping.groups.length),
                    '--wps-body-width': vertical
                        ? 'calc(var(--wps-column-width) * var(--wps-groups-count))'
                        : 'calc(var(--wps-pixels-per-minute) * var(--wps-total-duration))',
                    '--wps-body-height': vertical
                        ? 'calc(var(--wps-pixels-per-minute) * var(--wps-total-duration))'
                        : 'calc(var(--wps-row-height) * var(--wps-groups-count))',
                } as CSSProperties
            }
        >
            {!vertical && (
                <SchedulerTitle className={schedulerLayoutClasses.title}>{title}</SchedulerTitle>
            )}
            <DivLayout
                className={schedulerLayoutClasses.layout}
                style={vertical ? { display: 'flex' } : undefined}
            >
                {children}
            </DivLayout>
        </DivRoot>
    );
}

const DivRoot = styled('div')(({ theme }) => ({
    position: 'relative',
    '&::after': {
        display: 'block',
        height: 1,
        width: '100%',
        content: '""',
        backgroundColor: theme.palette.neutral[3],
    },
}));

const DivLayout = styled('div')(({ theme }) => ({
    position: 'relative',
    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: '10px',
    overflow: 'auto',
    height: 'fit-content',
    backgroundColor: theme.palette.neutral[1],
}));
