import { styled } from '@mui/material';
import moment from 'moment';
import { MutableRefObject, useEffect, useMemo, useRef } from 'react';
import { useSchedulerContext } from '../../context';
import { SetOffset } from '../common';

const Time = styled('time')({
    opacity: 0.9,

    ':hover': {
        opacity: 1,
    },
});

function defaultRenderTimelineCell(time: moment.Moment) {
    return <Time>{time.format('HH:mm')}</Time>;
}

export type TimelineProps = {
    vertical: boolean;
    renderTimelineCell?: (time: moment.Moment) => React.ReactNode;
    setOffsetRef: MutableRefObject<SetOffset | null>;
    hideTitleFirstInterval?: boolean;
};

export default function Timeline({
    vertical,
    renderTimelineCell = defaultRenderTimelineCell,
    setOffsetRef,
    hideTitleFirstInterval = false,
}: TimelineProps) {
    const {
        interval,
        dimensions: { stepLengthInMinutes },
    } = useSchedulerContext();
    const cells = useMemo(() => {
        const cells: React.ReactNode[] = [];

        const cursor = moment(interval.from);

        let iterationsLeft = 300;
        while (cursor.isBefore(interval.to) && iterationsLeft > 0) {
            iterationsLeft--;

            if (hideTitleFirstInterval && !vertical && cursor.isSame(interval.from)) {
                cells.push(<TimelineCellWithHiddenTitle key={iterationsLeft} />);
                cursor.add(stepLengthInMinutes, 'minute');
                continue;
            }

            cells.push(
                <TimelineCell key={iterationsLeft} vertical={vertical}>
                    {renderTimelineCell(cursor.clone())}
                </TimelineCell>
            );
            cursor.add(stepLengthInMinutes, 'minute');
        }

        return cells;
    }, [interval, stepLengthInMinutes, hideTitleFirstInterval, renderTimelineCell, vertical]);

    const timelineRef = useRef<HTMLElement | null>(null);
    useEffect(() => {
        setOffsetRef.current = (offset) => {
            if (timelineRef.current)
                timelineRef.current.style.setProperty('--timeline-offset', `${offset}px`);
        };
    }, [setOffsetRef]);

    return (
        <StyledTimeline
            ref={timelineRef}
            style={
                vertical
                    ? {
                          width: 'var(--wps-side-width)',
                          transform:
                              'translateY(calc(var(--wps-row-height) / -2 + var(--timeline-offset)))',
                          flexDirection: 'column',
                      }
                    : {
                          height: 'var(--wps-header-height)',
                          transform:
                              'translateX(calc(var(--wps-column-width) / -2 + var(--timeline-offset)))',
                      }
            }
        >
            {cells}
        </StyledTimeline>
    );
}

const StyledTimeline = styled('header')({
    display: 'flex',
    '--timeline-offset': '0px',
});

type TimelineCellProps = {
    vertical: boolean;
    children: React.ReactNode;
};

function TimelineCell({ vertical, children }: TimelineCellProps) {
    return (
        <DivTimelineCell
            style={
                vertical
                    ? { width: '100%', height: 'var(--wps-row-height)' }
                    : { minWidth: 'var(--wps-column-width)', height: '100%' }
            }
        >
            {children}
        </DivTimelineCell>
    );
}

function TimelineCellWithHiddenTitle() {
    return <DivTimelineCell style={{ minWidth: 'var(--wps-column-width)', height: '100%' }} />;
}

const DivTimelineCell = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));
