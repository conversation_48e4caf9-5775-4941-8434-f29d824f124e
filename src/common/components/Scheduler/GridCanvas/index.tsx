import { styled } from '@mui/material';
import clsx from 'clsx';
import moment from 'moment';
import React, { useMemo } from 'react';
import { GridCanvasCells, GridCanvasCellsProps } from '../GridCanvasCells';
import Cursor from '../GridCanvasCells/Cursor';
import { useSchedulerContext } from '../context';
import GroupDecorations, { GroupDecorationsProps } from './GroupDecorations';
import styles from './css.module.css';
import { MousePositionCallback, useMouseTrackerProps } from './mouseTracker';

export type GridCanvasProps<T, G> = GridCanvasCellsProps<T, G> & {
    showCursor: boolean;
    hasExpandedLastColumn: boolean;
    onMouseMoved?: MousePositionCallback;
    GroupDecorationComponent?: GroupDecorationsProps<G>['GroupDecorationComponent'];
    children?: React.ReactNode;
};

export default function GridCanvas<T, G>({
    showCursor,
    hasExpandedLastColumn,
    onMouseMoved,
    GroupDecorationComponent,
    children,
    ...props
}: GridCanvasProps<T, G>) {
    return (
        <GridCanvasCells<T, G> {...props}>
            <GroupDecorations<G>
                GroupDecorationComponent={GroupDecorationComponent}
                vertical={props.vertical}
                hasExpandedLastColumn={hasExpandedLastColumn}
            />
            {showCursor && <Cursor vertical={props.vertical} />}
            <InnerGrid
                mouseTrackerCallback={onMouseMoved}
                vertical={props.vertical}
                hasExpandedLastColumn={hasExpandedLastColumn}
            />{' '}
            {children}
        </GridCanvasCells>
    );
}

type InnerGridProps = {
    mouseTrackerCallback?: MousePositionCallback;
    vertical: boolean;
    hasExpandedLastColumn: boolean;
};

function InnerGrid({ mouseTrackerCallback, vertical, hasExpandedLastColumn }: InnerGridProps) {
    const { dimensions, grouping, interval } = useSchedulerContext();
    const { rowHeight, columnWidth } = dimensions;
    const groupsCount = Math.max(grouping.groups.length, 1);
    const totalDuration = moment(interval.to).diff(interval.from, 's') / 60;
    const timelineSize = Math.ceil(totalDuration / dimensions.stepLengthInMinutes);
    const ts = +moment(interval.from).toDate();

    const rowsCount = vertical ? timelineSize : groupsCount;
    const tableWidth = vertical ? groupsCount : timelineSize;

    const rows = useMemo(() => {
        const rows = Array.from({ length: rowsCount }).map((_, idx) => {
            const cells: React.ReactNode[] = [];

            for (let i = 0; i < tableWidth; i++) {
                cells.push(
                    <td
                        data-cmos-scheduler-group-idx={vertical ? i : idx}
                        data-cmos-scheduler-ts={
                            ts + (vertical ? idx : i) * dimensions.stepLengthInMinutes * 60000
                        }
                        key={i}
                        className={clsx(
                            vertical &&
                                hasExpandedLastColumn &&
                                i === tableWidth - 1 &&
                                styles.expandedColumn
                        )}
                    />
                );
            }

            return <tr key={idx}>{cells}</tr>;
        });

        return rows;
    }, [
        rowsCount,
        tableWidth,
        ts,
        dimensions.stepLengthInMinutes,
        vertical,
        hasExpandedLastColumn,
    ]);

    const mouseTrackerProps = useMouseTrackerProps(
        dimensions.pixelsPerMinuteRatio,
        mouseTrackerCallback,
        vertical
    );

    return (
        <STable
            {...mouseTrackerProps}
            cellWidth={columnWidth}
            cellHeight={rowHeight}
            className={styles.gridTable}
        >
            <tbody>{rows}</tbody>
        </STable>
    );
}

const STable = styled('table')<{ cellWidth: number; cellHeight: number }>(
    ({ cellWidth, cellHeight }) => ({
        '--cell-width': `${cellWidth}px`,
        '--cell-height': `${cellHeight}px`,
    })
);
