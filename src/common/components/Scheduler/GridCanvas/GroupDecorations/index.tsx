import { styled } from '@mui/material';
import { CSSProperties, ComponentType } from 'react';
import { GroupDefinition, useSchedulerContext } from '../../context';

export type GroupDecorationsProps<G> = {
    GroupDecorationComponent?: ComponentType<GroupDecorationComponentProps<G>>;
    vertical: boolean;
    hasExpandedLastColumn: boolean;
};

export type GroupDecorationComponentProps<G> = {
    group: G;
    vertical: boolean;
};

export default function GroupDecorations<G>({
    GroupDecorationComponent,
    vertical,
    hasExpandedLastColumn,
}: GroupDecorationsProps<G>) {
    const { grouping } = useSchedulerContext();

    if (!GroupDecorationComponent) return null;

    return (
        <GroupDecorationsRoot
            style={
                vertical
                    ? {
                          height: 'var(--wps-viewport-size)',
                          width: 'var(--wps-column-width)',
                          top: '0px',
                      }
                    : {
                          height: 'var(--wps-row-height)',
                          width: 'var(--wps-viewport-size)',
                          left: '0px',
                      }
            }
        >
            {grouping.groups.map((g: GroupDefinition, i: number) => (
                <DivGroup
                    key={g.key}
                    style={
                        {
                            ...(vertical
                                ? {
                                      '--index': i,
                                      left: 'calc(var(--index) * var(--wps-column-width))',
                                      width:
                                          hasExpandedLastColumn && i === grouping.groups.length - 1
                                              ? 'calc(var(--wps-column-width) + 10px)'
                                              : 'var(--wps-column-width)',
                                      height: '100%',
                                      top: 0,
                                  }
                                : {
                                      '--index': i,
                                      left: 0,
                                      width: '100%',
                                      height: 'var(--wps-row-height)',
                                      top: 'calc(var(--index) * var(--wps-row-height))',
                                  }),
                        } as CSSProperties
                    }
                >
                    <GroupDecorationComponent group={g.data as G} vertical={vertical} />
                </DivGroup>
            ))}
        </GroupDecorationsRoot>
    );
}

const DivGroup = styled('div')({
    position: 'absolute',
});

const GroupDecorationsRoot = styled('div')({
    position: 'absolute',

    [`.wps-dropping &`]: {
        // make group decorations invisible when dragging and element inside the scheduler
        pointerEvents: 'none',
    },
});
