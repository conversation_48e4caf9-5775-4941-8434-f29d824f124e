import { useMemo } from 'react';

type MouseTrackerProps = {
    onMouseMove?: React.MouseEventHandler;
    onMouseOver?: React.MouseEventHandler;
    onMouseOut?: React.MouseEventHandler;
};

export type MousePositionCallback = (
    timestamp: number,
    groupIndex: number,
    event?: React.MouseEvent
) => void;

function preventExtraCalls(fn: MousePositionCallback): MousePositionCallback {
    let ts0: number, groupIndex0: number;

    return (ts, groupIndex, ev) => {
        if (ts === ts0 && groupIndex === groupIndex0) return;
        ts0 = ts;
        groupIndex0 = groupIndex;
        fn(ts, groupIndex, ev);
    };
}

export function useMouseTrackerProps(
    pixelsPerMinute: number,
    callback: MousePositionCallback | undefined,
    vertical: boolean,
    roundingToSeconds: number = 60 * 5
): MouseTrackerProps {
    const callbackWithoutExtraCalls = useMemo(
        () => (callback ? preventExtraCalls(callback) : undefined),
        [callback]
    );

    if (!callbackWithoutExtraCalls) return {};

    return {
        onMouseOut: (e) => {
            if (isSchedulerCellElement(e.target) && !isSchedulerCellElement(e.relatedTarget)) {
                callbackWithoutExtraCalls(-1, -1, e);
            }
        },
        onMouseMove: (e) => {
            if (!isSchedulerCellElement(e.target)) return;

            const groupIndex = +e.target.dataset.cmosSchedulerGroupIdx!;
            const tsStart = +e.target.dataset.cmosSchedulerTs!;
            const rect = e.target.getBoundingClientRect();
            const mouseCoord = vertical ? e.clientY - rect.top : e.clientX - rect.left;
            let ts = tsStart + (mouseCoord / pixelsPerMinute) * 60000;
            ts = Math.round(ts / 1000 / roundingToSeconds) * 1000 * roundingToSeconds;
            callbackWithoutExtraCalls!(ts, groupIndex);
        },
    };
}

export function isSchedulerCellElement(el: any): el is HTMLTableCellElement {
    return (
        el instanceof HTMLTableCellElement &&
        !!el.dataset.cmosSchedulerGroupIdx &&
        !!el.dataset.cmosSchedulerTs
    );
}
