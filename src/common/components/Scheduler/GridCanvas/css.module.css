.gridTable {
    border-collapse: collapse;
    margin: -1px;
    background-color: transparent;

    position: absolute;
    left: 0;
    top: 0;
    width: fit-content;
    height: fit-content;
}

.gridTable .expandedColumn {
    width: calc(var(--cell-width) + 10px);
}

.gridTable td {
    height: var(--cell-height);
    width: var(--cell-width);
    min-width: var(--cell-width);
    border: 1px solid var(--grey);
    box-sizing: border-box;
    padding: 0;
    background-color: transparent;
}

.gridTable tr:last-child td {
    border-bottom: none;
}

:global(.wps-dropping) .gridTable {
    pointer-events: none;
}
