import { createContext, useContext, useMemo } from 'react';
import { Subject, Unsubscribe } from 'utils/event';

type Origin = 'start' | 'center' | 'end';

type ScrollToElementOptions = {
    elementOrigin?: Origin;
    schedulerOrigin?: Origin;
    offset?: number;
    smooth?: boolean;
};

export class ScrollbarController {
    public readonly rootElement: HTMLElement;
    private _fromTimestamp: number = 0;
    private _pixelPerMinuteRatio: number = 0;
    private _sideWidth: number = 0;
    private _vertical: boolean = false;

    constructor(
        scrollbars: HTMLElement,
        fromTimestamp: number,
        pixelPerMinuteRatio: number,
        sideWidth: number,
        vertical: boolean
    ) {
        this._fromTimestamp = fromTimestamp;
        this.rootElement = scrollbars;
        this._pixelPerMinuteRatio = pixelPerMinuteRatio;
        this._sideWidth = sideWidth;
        this._vertical = vertical;
    }

    scrollToTimestamp(ts: number, origin: Origin = 'center', smooth: boolean = false): void {
        let position = ((ts - this._fromTimestamp) / 60000) * this._pixelPerMinuteRatio;
        const clientSize = this._vertical
            ? this.rootElement.clientHeight
            : this.rootElement.clientWidth;

        switch (origin) {
            case 'start':
                break;
            case 'center':
                position -= clientSize / 2;
                break;
            case 'end':
                position += clientSize / 2;
                break;
        }

        const top = !this._vertical ? position : undefined;
        const left = this._vertical ? position : undefined;

        this.scroll(top, left, smooth);
    }

    scroll(left?: number, top?: number, smooth: boolean = false): void {
        this.rootElement.scroll({ left, top, behavior: smooth ? 'smooth' : undefined });
    }

    getOffsetFromLeft(element: HTMLElement): number {
        const rect = element.getBoundingClientRect();
        const scrollbarsRect = this.rootElement.getBoundingClientRect();
        return rect.left - scrollbarsRect.left;
    }

    getOffsetFromTop(element: HTMLElement): number {
        const rect = element.getBoundingClientRect();
        const scrollbarsRect = this.rootElement.getBoundingClientRect();
        return rect.top - scrollbarsRect.top;
    }

    getAreaWidth() {
        return this.rootElement.getBoundingClientRect().width ?? 0;
    }

    getAreaHeight() {
        return this.rootElement.getBoundingClientRect().height ?? 0;
    }

    getSideWidth() {
        return this._sideWidth;
    }

    scrollToElement(element: HTMLElement, options: ScrollToElementOptions = {}): void {
        const rect = element.getBoundingClientRect();
        let offset = this._vertical
            ? this.rootElement.scrollTop + this.getOffsetFromTop(element)
            : this.rootElement.scrollLeft + this.getOffsetFromLeft(element);
        const clientSize = this._vertical
            ? this.rootElement.clientHeight
            : this.rootElement.clientWidth;
        const rectSize = this._vertical ? rect.height : rect.width;

        switch (options.schedulerOrigin ?? 'center') {
            case 'center':
                offset -= clientSize / 2;
                break;
            case 'end':
                offset -= clientSize;
                break;
            case 'start':
                break;
        }

        switch (options.elementOrigin ?? 'center') {
            case 'center':
                offset += rectSize / 2;
                break;
            case 'end':
                offset += rectSize;
                break;
            case 'start':
                break;
        }

        offset -= options.offset ?? 0;
        const top = !this._vertical ? offset : undefined;
        const left = this._vertical ? offset : undefined;

        this.scroll(top, left, options.smooth ?? false);
    }

    scrollToGroup(groupKey: string, options: ScrollToElementOptions = {}): void {
        const groupElement = this.rootElement.querySelector<HTMLElement>(
            `[data-group-key="${groupKey}"]`
        );
        if (groupElement) {
            const rect = groupElement.getBoundingClientRect();
            let offset = this.rootElement.scrollTop + this.getOffsetFromTop(groupElement);
            const clientHeight = this.rootElement.clientHeight;
            const rectHeight = rect.height;

            offset -= clientHeight / 2;
            offset += rectHeight / 2;
            offset -= options.offset ?? 0;

            this.scroll(undefined, offset, options.smooth ?? false);
        }
    }
}

class DynamicContext {
    private _subject = new Subject<ScrollbarController | null>(null);

    setContext(context: ScrollbarController | null) {
        this._subject.set(context);
    }

    subscribe(callback: (context: ScrollbarController | null) => void): Unsubscribe {
        return this._subject.subscribe(callback);
    }

    get scrollbarContext() {
        return this._subject.value;
    }
}

const SchedulerScrollbarControllerContext = createContext<DynamicContext | null>(null);

export function SchedulerScrollbarControllerProvider({ children }: React.PropsWithChildren<{}>) {
    const parentCtx = useSchedulerScrollbarController();
    const ctx = useMemo(() => parentCtx ?? new DynamicContext(), [parentCtx]);
    return (
        <SchedulerScrollbarControllerContext.Provider value={ctx}>
            {children}
        </SchedulerScrollbarControllerContext.Provider>
    );
}

export function useSchedulerScrollbarController() {
    return useContext(SchedulerScrollbarControllerContext);
}
