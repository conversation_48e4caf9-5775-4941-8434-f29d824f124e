import { createContext } from 'react';
import { Layout } from 'react-grid-layout';

type Unsubscribe = () => void;

type Callback = (item: Layout) => void;

export interface ISharedLayoutHub {
    subscribeDrag(key: string, callback: Callback): Unsubscribe;
    publishDragUpdate(key: string, item: Layout): void;
}
const SharedLayoutHubContext = createContext<ISharedLayoutHub | null>(null);

class SharedLayoutHub implements ISharedLayoutHub {
    private _drag: { callback: Callback; key: string }[] = [];

    publishDragUpdate(key: string, item: Layout) {
        for (const callback of this._drag) {
            if (callback.key === key) continue;
            try {
                callback.callback(item);
            } catch (err) {
                console.error('error occurred while calling drag callbacks');
            }
        }
    }

    subscribeDrag(key: string, callback: Callback): Unsubscribe {
        const entry = {
            callback,
            key,
        };
        this._drag.push(entry);

        return () => {
            this._drag.splice(this._drag.indexOf(entry));
        };
    }
}
