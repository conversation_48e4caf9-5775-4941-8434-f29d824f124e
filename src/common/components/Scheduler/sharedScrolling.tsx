import { createContext, useContext, useMemo } from 'react';
import { IBus, newBus } from 'utils/event';

const SharedScrollingContext = createContext<IBus<number> | null>(null);

export function useSharedScrolling() {
    return useContext(SharedScrollingContext);
}

export function SharedScrollingContextProvider({ children }: React.PropsWithChildren<{}>) {
    return (
        <SharedScrollingContext.Provider value={useMemo(() => newBus<number>(), [])}>
            {children}
        </SharedScrollingContext.Provider>
    );
}
