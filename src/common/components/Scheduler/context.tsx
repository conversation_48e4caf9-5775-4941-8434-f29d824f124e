import { MomentInput } from 'moment';
import { createContext, useCallback, useContext, useMemo } from 'react';
import { IEvent, createEvent } from 'utils/event';

export type SchedulerContextData = {
    interval: SchedulerInterval;
    grouping: GroupsState;
    dimensions: SchedulerDimensions;
};

export type SchedulerInterval = {
    from: MomentInput;
    to: MomentInput;
};

export type GroupsState = {
    groups: GroupDefinition[];
    hideGroups: boolean;
};

export type GroupDefinition = {
    key: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any;
};

export type SchedulerDimensions = {
    rowHeight: number;
    columnWidth: number;
    pixelsPerMinuteRatio: number;
    sideWidth: number;
    headerHeight: number;
    stepLengthInMinutes: number;
    stepsCount: number;
};

export const SchedulerContext = createContext<SchedulerContextData | null>(null);

export function useSchedulerContext(): SchedulerContextData {
    const data = useContext(SchedulerContext);
    if (data === null) throw new Error('cannot use useSchedulerContext hook outside of scheduler');
    return data;
}

export type MousePosition = {
    ts: number;
    groupIndex: number;
    mouseEvent?: React.MouseEvent;
};

const MouseTrackerContext = createContext<IEvent<MousePosition> | null>(null);

export function useSchedulerMouseTrackerEvent() {
    const event = useContext(MouseTrackerContext);
    if (event === null)
        throw new Error('MouseTrackerContext is only available within the scheduler component');
    return event;
}

export function useSchedulerMouseTrackerProvider() {
    const event = useMemo(() => createEvent<MousePosition>(), []);

    const Provider = useCallback(
        ({ children }: React.PropsWithChildren<{}>) => {
            return (
                <MouseTrackerContext.Provider value={event}>
                    {children}
                </MouseTrackerContext.Provider>
            );
        },
        [event]
    );

    return {
        event,
        Provider,
    };
}
