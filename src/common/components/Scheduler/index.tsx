import { TimeSpan } from 'api/utils/format';
import { Moment, MomentInput } from 'moment';
import { memo, useCallback, useMemo } from 'react';
import { GridCanvasProps } from './GridCanvas';
import { MousePositionCallback } from './GridCanvas/mouseTracker';
import { SchedulerScrollbarControllerProvider } from './GridCanvas/scrollbarController';
import SchedulerLayout, { SchedulerLayoutProps, schedulerLayoutClasses } from './SchedulerLayout';
import {
    GroupsState,
    SchedulerContext,
    SchedulerContextData,
    useSchedulerMouseTrackerProvider,
} from './context';

export { schedulerLayoutClasses };

export type RenderItemFunction<T> = (item: T) => React.ReactNode;

export type SchedulerItemBase = {
    groupKey: string;
    timestamp: MomentInput;
    minutesDuration: number;
};

export interface IDragAndDropActionCancel {
    cancel(): void;
}

export type ItemReplacedCallback<T, G> = (
    item: T,
    newPosition: TimeSpan,
    newGroup: G,
    oldPosition: TimeSpan,
    actionCancel: IDragAndDropActionCancel
) => void;

export type ItemStartDraggingCallback<T> = (item: T) => void;

export type SchedulerWithGroupsProps<T, G> = {
    from: MomentInput;
    to: MomentInput;
    items: T[];
    restrictedZones?: SchedulerItemBase[];
    title?: React.ReactNode;
    renderItem: RenderItemFunction<T>;
    renderHeader?: (time: Moment) => React.ReactNode;
    getItemStatic: (item: T) => boolean;
    getItemTimestamp: (item: T) => number;
    getItemMinutesDuration: (item: T) => number;
    rowHeight?: number;
    columnWidth?: number;
    stepLengthInMinutes?: number;
    stepsCount: number;
    sideWidth?: number;
    noDefaultRow?: boolean;
    GroupDecorationComponent?: GridCanvasProps<T, G>['GroupDecorationComponent'];
    groups: G[];
    hideGroups?: boolean;
    getItemKey: (item: T) => string;
    getItemIsResizable?: (item: T) => boolean;
    getGroupKey: (group: G) => string;
    renderGroup?: (group: G, vertical: boolean) => React.ReactNode;
    getItemGroupKey: (item: T) => string;
    headerHeight?: number;
    maxHeight?: number;
    className?: string;
    children?: React.ReactNode;
    schedulerRef: React.ForwardedRef<HTMLDivElement>;
    allowOverlap?: boolean;
    hideTitleFirstInterval?: boolean;
} & Pick<
    SchedulerLayoutProps<T, G>,
    | 'onDrop'
    | 'droppedItemDurationInMinutes'
    | 'isDraggable'
    | 'isDroppable'
    | 'stickyPart'
    | 'vertical'
    | 'onItemStartDragging'
    | 'onItemStopDragging'
    | 'onItemReplaced'
    | 'isResizable'
    | 'onItemStartResizing'
    | 'onItemStopResizing'
    | 'onItemResized'
>;

function SchedulerWithGroups<T, G>({
    // date an time stuff
    from,
    to,

    // data
    items,
    groups,

    // drag and drop stuff
    restrictedZones,
    getItemStatic,

    // rendering and visual stuff
    renderItem,
    renderHeader,
    GroupDecorationComponent,
    renderGroup,
    hideGroups,
    rowHeight = 100,
    stepLengthInMinutes = 30,
    stepsCount,
    columnWidth = 120,
    noDefaultRow,
    title,
    getItemTimestamp,
    getItemMinutesDuration,
    sideWidth = 130,
    getItemKey,
    getItemIsResizable,
    headerHeight,
    maxHeight,
    className,

    // keys
    getGroupKey,
    getItemGroupKey,

    // events
    onItemReplaced,
    onItemStartDragging,
    onItemStopDragging,

    children,
    schedulerRef,
    allowOverlap = false,

    ...props
}: SchedulerWithGroupsProps<T, G>) {
    const itemsData = useMemo(() => {
        return items.map((item) => ({
            key: getItemKey(item),
            static: getItemStatic(item),
            timestamp: getItemTimestamp(item),
            minutesDuration: getItemMinutesDuration(item),
            data: item,
            groupKey: getItemGroupKey(item),
            isResizable: getItemIsResizable ? getItemIsResizable(item) : props.isResizable,
        }));
    }, [
        items,
        getItemStatic,
        getItemTimestamp,
        getItemMinutesDuration,
        getItemGroupKey,
        getItemKey,
        getItemIsResizable,
        props.isResizable,
    ]);

    const groupingState: GroupsState = useMemo(() => {
        return {
            groups: groups.map((g) => ({
                key: getGroupKey(g),
                data: g,
            })),
            hideGroups: hideGroups ?? false,
        };
    }, [groups, getGroupKey, hideGroups]);

    const schedulerContext: SchedulerContextData = useMemo(
        () => ({
            interval: { from, to },
            dimensions: {
                rowHeight,
                stepLengthInMinutes,
                columnWidth,
                pixelsPerMinuteRatio:
                    (props.vertical ? rowHeight : columnWidth) / stepLengthInMinutes,
                stepsCount,
                sideWidth,
                headerHeight: headerHeight ?? 0,
            },
            grouping: groupingState,
        }),
        [
            from,
            to,
            groupingState,
            rowHeight,
            stepLengthInMinutes,
            columnWidth,
            stepsCount,
            sideWidth,
            headerHeight,
            props.vertical,
        ]
    );

    const { event, Provider } = useSchedulerMouseTrackerProvider();
    const onMouseMovedCallback = useCallback<MousePositionCallback>(
        (ts, groupIndex, mouseEvent) => {
            event.publish({
                ts,
                groupIndex,
                mouseEvent,
            });
        },
        [event]
    );

    return (
        <SchedulerContext.Provider value={schedulerContext}>
            <Provider>
                <SchedulerScrollbarControllerProvider>
                    <SchedulerLayout<T, G>
                        rootRef={schedulerRef}
                        cells={itemsData}
                        staticCells={restrictedZones?.map((z, i) => ({
                            key: 'static_' + i,
                            static: true,
                            groupKey: z.groupKey,
                            timestamp: z.timestamp,
                            minutesDuration: z.minutesDuration,
                            isResizable: false,
                        }))}
                        onMouseMoved={onMouseMovedCallback}
                        renderItem={renderItem}
                        renderTimeline={renderHeader}
                        showCursor
                        sideWidth={sideWidth}
                        headerHeight={headerHeight ?? 40}
                        maxHeight={maxHeight}
                        title={title}
                        GroupDecorationComponent={GroupDecorationComponent}
                        onItemReplaced={onItemReplaced}
                        onItemStartDragging={onItemStartDragging}
                        onItemStopDragging={onItemStopDragging}
                        groupingOptions={{
                            getGroupKey: getGroupKey,
                            renderGroup: renderGroup,
                        }}
                        className={className}
                        allowOverlap={allowOverlap}
                        hasExpandedLastColumn={!props.stickyPart && props.vertical}
                        {...props}
                    >
                        {children}
                    </SchedulerLayout>
                </SchedulerScrollbarControllerProvider>
            </Provider>
        </SchedulerContext.Provider>
    );
}

export default memo(SchedulerWithGroups) as typeof SchedulerWithGroups;

type SchedulerProps<T> = Omit<
    SchedulerWithGroupsProps<T, never>,
    'groups' | 'getGroupKey' | 'hideGroups' | 'getItemGroupKey' | 'renderGroup'
>;

const getDummyKey = () => '';
const renderNothing = () => null;

export function Scheduler<T>(props: SchedulerProps<T>) {
    return (
        <SchedulerWithGroups
            getItemGroupKey={getDummyKey}
            getGroupKey={getDummyKey}
            groups={[]}
            hideGroups
            renderGroup={renderNothing}
            {...props}
        />
    );
}
