import { SxProps, Theme, styled } from '@mui/material';
import { InfoIcon } from '../Icons/InfoIcon';

type InfoTextProps = {
    children?: React.ReactNode;
    sx?: SxProps<Theme>;
};

export default function InfoText({ children, sx }: InfoTextProps) {
    return (
        <StyledRoot sx={sx}>
            <StyledIconWrapperSpan>
                <InfoIcon size={14} fill="currentColor" />
            </StyledIconWrapperSpan>
            {children}
        </StyledRoot>
    );
}

const StyledRoot = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
}));

const StyledIconWrapperSpan = styled('span')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[5],
    color: 'white',
    borderRadius: 100,
    height: 14,
    width: 14,
    display: 'inline-block',
}));
