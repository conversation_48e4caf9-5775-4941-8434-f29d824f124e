import { numberPhoneFormatter } from 'common/FormatersHelper';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';

export default function PhoneNumberDisplay({ phoneNumber }: { phoneNumber: string }) {
    const { internationalization } = useAppSelector(selectSettings);

    const formatted = numberPhoneFormatter(internationalization.phoneNumberFormat, phoneNumber);

    return <>{formatted}</>;
}
