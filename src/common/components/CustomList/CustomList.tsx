import { List, listClasses, menuItemClasses, styled } from '@mui/material';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

export const CustomList = styled(List)(({ theme }) => ({
    [`& .${menuItemClasses.root}:not(:last-child)`]: {
        borderBottom: `1px solid ${theme.palette.neutral[3]}`,
    },

    [`& .${listClasses.root}`]: {
        borderRadius: 10,
    },

    ...scrollbarStyle(),
    overflow: 'auto',
}));
