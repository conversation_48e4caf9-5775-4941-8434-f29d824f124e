import { ExpandLess, ExpandMore } from '@mui/icons-material';
import { Interpolation, styled } from '@mui/material';
import React from 'react';
import { DropdownProps } from '../Inputs/Dropdown';

type FakeDropdownProps = React.HTMLAttributes<HTMLDivElement> & {
    open?: boolean;
    cmosVariant?: DropdownProps['cmosVariant'];
};

export default function FakeDropdown({ open, children, cmosVariant, ...props }: FakeDropdownProps) {
    return (
        <Root role="button" tabIndex={0} {...props}>
            <Content>{children}</Content>
            <IconContainer>{open ? <ExpandLess /> : <ExpandMore />}</IconContainer>
        </Root>
    );
}

const Root = styled('div')<{ cmosVariant?: DropdownProps['cmosVariant']; isFocused?: boolean }>(
    ({ theme, cmosVariant = 'default', isFocused = false }) => {
        const baseStyle: Interpolation<{}> = {
            display: 'flex',
            alignItems: 'stretch',
            '--dropdown-border-radius': '4px',
            '--dropdown-background-color': theme.palette.background.paper,
            '--dropdown-text-color': theme.palette.common.black,
            '--dropdown-font-weight': 'normal',

            borderRadius: 'var(--dropdown-border-radius) !important',
            backgroundColor: 'var(--dropdown-background-color) !important',
            border: '1px solid var(--dropdown-main-color) !important',

            color: 'var(--dropdown-text-color) !important',
            cursor: 'pointer !important',

            '&:hover': {
                borderColor: isFocused ? undefined : `${theme.palette.primary.main} !important`,
            },
        };

        const style = [baseStyle];

        switch (cmosVariant) {
            case 'default':
                style.push({
                    '--dropdown-main-color': theme.palette.neutral[5],
                });
                break;
        }

        return style;
    }
);

const IconContainer = styled('div')({});

const Content = styled('div')({
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    marginRight: 8,
    marginLeft: 8,
});
