import { makeStyles } from '@mui/styles';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

// essentially copy style from DropdownMulti
const useTagStyles = makeStyles((theme) => ({
    tag: {
        borderRadius: 10,
        backgroundColor: theme.palette.neutral[3],
        color: theme.palette.neutral[7],
        display: 'inline-flex',
        flexWrap: 'nowrap',
        maxWidth: '100%',
    },
    tagBody: {
        padding: '7px 5px 7px 15px',
        wordBreak: 'break-all',
        width: 'auto',
    },
    ellipsis: {
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        overflow: 'hidden',
    },
    small: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
    },
    medium: {
        ...FontSecondary(HeaderStyles.H5_14px, false, theme.palette.neutral[7]),
    },
    large: {
        ...FontSecondary(HeaderStyles.H4_18px, false, theme.palette.neutral[7]),
    },
    button: {
        margin: '0 3px 0 0',
        width: 28,
    },
}));

export default useTagStyles;
