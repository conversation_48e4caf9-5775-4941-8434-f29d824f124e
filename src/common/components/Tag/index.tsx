import { IconButton } from '@mui/material';
import clsx from 'clsx';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { forwardRef } from 'react';
import { CloseIcon } from '../Icons/CloseIcon';
import useTagStyles from './styles';

type TagSize = 'small' | 'medium' | 'large';

type TagProps = React.HTMLAttributes<HTMLDivElement> & {
    onClose?: () => void;
    size?: TagSize;
    ellipsis?: boolean;
};

const Tag = forwardRef<HTMLDivElement, TagProps>(
    ({ onClose, className, children, size = 'medium', ellipsis, ...props }, ref) => {
        const styles = useTagStyles();

        return (
            <div className={clsx(styles.tag, styles[size], className)} {...props}>
                <div className={clsx(styles.tagBody, ellipsis && styles.ellipsis)}>{children}</div>
                <IconButton
                    className={styles.button}
                    size="small"
                    onClick={() => (onClose ? onClose() : undefined)}
                >
                    <CloseIcon size={IconSize.S} fill={Colors.Neutral6} />
                </IconButton>
            </div>
        );
    }
);

export default Tag;
