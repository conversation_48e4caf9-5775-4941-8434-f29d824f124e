import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CommentsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.965 19.0001C6.81114 19.0033 6.66227 18.9454 6.55095 18.8392C6.43964 18.7329 6.37493 18.5869 6.371 18.4331V17.0241C5.75708 17.0375 5.16286 16.8071 4.71847 16.3833C4.27408 15.9595 4.01573 15.3769 4 14.7631V8.26307C4.01547 7.64876 4.27381 7.06564 4.71842 6.64146C5.16303 6.21728 5.75765 5.98664 6.372 6.00007H17.63C18.2438 5.98717 18.8378 6.21792 19.2819 6.64183C19.7261 7.06573 19.9843 7.64829 20 8.26207V14.7621C19.9845 15.376 19.7263 15.9588 19.2818 16.3826C18.8374 16.8064 18.243 17.0368 17.629 17.0231H10.68L7.264 18.9231C7.17276 18.9744 7.06968 19.0009 6.965 19.0001ZM7.483 13.4131C7.3553 13.4131 7.23283 13.4638 7.14253 13.5541C7.05223 13.6444 7.0015 13.7669 7.0015 13.8946C7.0015 14.0223 7.05223 14.1447 7.14253 14.235C7.23283 14.3253 7.3553 14.3761 7.483 14.3761H13.519C13.6467 14.3761 13.7692 14.3253 13.8595 14.235C13.9498 14.1447 14.0005 14.0223 14.0005 13.8946C14.0005 13.7669 13.9498 13.6444 13.8595 13.5541C13.7692 13.4638 13.6467 13.4131 13.519 13.4131H7.483ZM7.483 11.0051C7.3553 11.0051 7.23283 11.0558 7.14253 11.1461C7.05223 11.2364 7.0015 11.3589 7.0015 11.4866C7.0015 11.6143 7.05223 11.7367 7.14253 11.827C7.23283 11.9173 7.3553 11.9681 7.483 11.9681H17.02C17.1477 11.9681 17.2702 11.9173 17.3605 11.827C17.4508 11.7367 17.5015 11.6143 17.5015 11.4866C17.5015 11.3589 17.4508 11.2364 17.3605 11.1461C17.2702 11.0558 17.1477 11.0051 17.02 11.0051H7.483ZM7.483 8.59707C7.41792 8.5941 7.35292 8.60436 7.29192 8.62722C7.23091 8.65008 7.17518 8.68506 7.12808 8.73007C7.08097 8.77507 7.04348 8.82916 7.01787 8.88905C6.99226 8.94895 6.97905 9.01342 6.97905 9.07857C6.97905 9.14371 6.99226 9.20818 7.01787 9.26808C7.04348 9.32798 7.08097 9.38206 7.12808 9.42707C7.17518 9.47207 7.23091 9.50706 7.29192 9.52992C7.35292 9.55278 7.41792 9.56303 7.483 9.56007H17.02C17.1439 9.55442 17.2609 9.50122 17.3465 9.41154C17.4322 9.32186 17.4801 9.2026 17.4801 9.07857C17.4801 8.95453 17.4322 8.83527 17.3465 8.74559C17.2609 8.65591 17.1439 8.60271 17.02 8.59707H7.483Z"
                fill={fill}
            />
        </svg>
    );
};
