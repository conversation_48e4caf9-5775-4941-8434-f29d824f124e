import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const LogOutIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.1721 3.00001C12.0474 2.99962 11.9238 3.02389 11.8085 3.07144C11.6932 3.119 11.5884 3.18888 11.5002 3.27709C11.412 3.36529 11.3421 3.47006 11.2946 3.58538C11.247 3.7007 11.2227 3.82428 11.2231 3.94901V10.206C11.2231 10.458 11.3232 10.6996 11.5014 10.8778C11.6795 11.0559 11.9212 11.156 12.1731 11.156C12.4251 11.156 12.6667 11.0559 12.8449 10.8778C13.023 10.6996 13.1231 10.458 13.1231 10.206V3.94901C13.1235 3.82411 13.0992 3.70036 13.0515 3.58491C13.0038 3.46947 12.9338 3.3646 12.8453 3.27637C12.7569 3.18814 12.6519 3.1183 12.5364 3.07087C12.4208 3.02343 12.297 2.99935 12.1721 3.00001V3.00001ZM9.01412 6.66601C8.86188 6.67656 8.7144 6.72354 8.58412 6.80301C7.21578 7.59288 6.14631 8.8122 5.5416 10.2718C4.93689 11.7315 4.83073 13.3499 5.2396 14.876C5.64846 16.4022 6.54948 17.7507 7.80292 18.7126C9.05637 19.6744 10.5922 20.1958 12.1721 20.1958C13.7521 20.1958 15.2879 19.6744 16.5413 18.7126C17.7948 17.7507 18.6958 16.4022 19.1046 14.876C19.5135 13.3499 19.4073 11.7315 18.8026 10.2718C18.1979 8.8122 17.1285 7.59288 15.7601 6.80301C15.6517 6.73327 15.5302 6.68628 15.403 6.6649C15.2758 6.64352 15.1457 6.6482 15.0204 6.67865C14.8951 6.7091 14.7773 6.76469 14.6741 6.84205C14.5709 6.9194 14.4845 7.01691 14.4202 7.12865C14.3558 7.2404 14.3149 7.36405 14.2997 7.49211C14.2846 7.62017 14.2957 7.74996 14.3322 7.87362C14.3688 7.99728 14.4301 8.11222 14.5124 8.21147C14.5947 8.31073 14.6964 8.39222 14.8111 8.45101C15.8164 9.03187 16.602 9.92819 17.0461 11.001C17.4901 12.0738 17.5678 13.2631 17.2672 14.3845C16.9665 15.506 16.3042 16.4969 15.383 17.2036C14.4618 17.9104 13.3332 18.2934 12.1721 18.2934C11.0111 18.2934 9.88244 17.9104 8.96125 17.2036C8.04006 16.4969 7.37777 15.506 7.07709 14.3845C6.7764 13.2631 6.8541 12.0738 7.29816 11.001C7.74221 9.92819 8.5278 9.03187 9.53312 8.45101C9.7232 8.34693 9.87207 8.18111 9.95515 7.98096C10.0382 7.78081 10.0505 7.5583 9.99002 7.35021C9.92952 7.14213 9.79983 6.96091 9.62239 6.8365C9.44495 6.7121 9.23037 6.65196 9.01412 6.66601Z"
                fill={fill}
            />
        </svg>
    );
};
