import { createIconComponent } from './Icon';

const Drag<PERSON>nd<PERSON>ropI<PERSON> = createIconComponent(() => (
    <>
        <path d="M9.5 9C10.3284 9 11 8.32843 11 7.5C11 6.67157 10.3284 6 9.5 6C8.67157 6 8 6.67157 8 7.5C8 8.32843 8.67157 9 9.5 9Z" />
        <path d="M9.5 14C10.3284 14 11 13.3284 11 12.5C11 11.6716 10.3284 11 9.5 11C8.67157 11 8 11.6716 8 12.5C8 13.3284 8.67157 14 9.5 14Z" />
        <path d="M9.5 19C10.3284 19 11 18.3284 11 17.5C11 16.6716 10.3284 16 9.5 16C8.67157 16 8 16.6716 8 17.5C8 18.3284 8.67157 19 9.5 19Z" />
        <path d="M14.5 9C15.3284 9 16 8.32843 16 7.5C16 6.67157 15.3284 6 14.5 6C13.6716 6 13 6.67157 13 7.5C13 8.32843 13.6716 9 14.5 9Z" />
        <path d="M14.5 14C15.3284 14 16 13.3284 16 12.5C16 11.6716 15.3284 11 14.5 11C13.6716 11 13 11.6716 13 12.5C13 13.3284 13.6716 14 14.5 14Z" />
        <path d="M14.5 19C15.3284 19 16 18.3284 16 17.5C16 16.6716 15.3284 16 14.5 16C13.6716 16 13 16.6716 13 17.5C13 18.3284 13.6716 19 14.5 19Z" />
    </>
));

export default DragAndDropIcon;
