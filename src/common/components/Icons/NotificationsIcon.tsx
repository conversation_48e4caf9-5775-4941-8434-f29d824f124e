import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const NotificationsIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.0025 19C12.9396 19 13.6893 18.2344 13.6893 17.25H10.289C10.289 18.2344 11.0387 19 12.0025 19ZM17.759 14.9258C17.2503 14.3516 16.2597 13.5039 16.2597 10.6875C16.2597 8.58203 14.8138 6.88672 12.8325 6.44922V5.875C12.8325 5.41016 12.4577 5 12.0025 5C11.5206 5 11.1458 5.41016 11.1458 5.875V6.44922C9.16445 6.88672 7.71863 8.58203 7.71863 10.6875C7.71863 13.5039 6.72798 14.3516 6.21927 14.9258C6.05862 15.0898 5.9783 15.3086 6.00507 15.5C6.00507 15.9648 6.32637 16.375 6.86185 16.375H17.1164C17.6519 16.375 17.9732 15.9648 18 15.5C18 15.3086 17.9197 15.0898 17.759 14.9258Z"
                fill={fill}
            />
        </svg>
    );
};
