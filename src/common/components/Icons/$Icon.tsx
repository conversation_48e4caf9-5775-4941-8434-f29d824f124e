import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const $Icon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            className={className}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.0293 18.0359V17.3309C13.8337 17.2093 14.5706 16.8095 15.1129 16.2003C15.6553 15.5912 15.969 14.8109 16 13.9942C16 13.0115 15.5079 11.262 12.2285 10.5781C10.0647 10.1225 10.0647 9.28345 10.0647 9.00672C10.0647 7.94383 11.0698 7.45822 12.0013 7.45822C12.3716 7.44979 12.7366 7.54784 13.0532 7.74083C13.3699 7.93382 13.6251 8.21371 13.7888 8.54754C13.8621 8.71127 13.9767 8.85285 14.1214 8.95816C14.2661 9.06347 14.4357 9.12884 14.6133 9.14773C14.7933 9.17254 14.9766 9.15138 15.1463 9.08623C15.316 9.02107 15.4666 8.91401 15.5843 8.77493C15.694 8.64332 15.763 8.48239 15.783 8.31188C15.8029 8.14138 15.7729 7.96874 15.6965 7.81516C15.4411 7.28272 15.0691 6.81523 14.6084 6.44771C14.1478 6.08018 13.6103 5.82215 13.0363 5.69292V4.9526C13.0137 4.69258 12.8949 4.45051 12.7034 4.27417C12.5118 4.09782 12.2615 4 12.0018 4C11.742 4 11.4917 4.09782 11.3001 4.27417C11.1086 4.45051 10.9898 4.69258 10.9672 4.9526V5.65767C10.1634 5.77979 9.4272 6.17991 8.88553 6.78903C8.34386 7.39815 8.03068 8.17809 8 8.99438C8 9.97706 8.48855 11.7265 11.7715 12.4104C13.9397 12.8661 13.9397 13.7086 13.9397 13.9818C13.9397 15.0447 12.9345 15.5303 12.0031 15.5303C11.6315 15.5402 11.2649 15.4428 10.9467 15.2497C10.6285 15.0567 10.372 14.776 10.2077 14.441C10.082 14.2022 9.87221 14.0191 9.61933 13.9275C9.36644 13.8359 9.08864 13.8424 8.84026 13.9457C8.7191 13.9873 8.60795 14.0539 8.5139 14.1412C8.41985 14.2285 8.345 14.3346 8.29413 14.4527C8.24326 14.5708 8.21751 14.6982 8.21851 14.8269C8.21952 14.9555 8.24726 15.0826 8.29997 15.1998C8.55538 15.7323 8.92735 16.1998 9.38804 16.5673C9.84873 16.9348 10.3862 17.1928 10.9602 17.3221V18.0474C10.9828 18.3074 11.1016 18.5495 11.2931 18.7258C11.4846 18.9022 11.735 19 11.9947 19C12.2545 19 12.5048 18.9022 12.6963 18.7258C12.8879 18.5495 13.0066 18.3074 13.0293 18.0474V18.0359Z"
                fill={fill}
            />
        </svg>
    );
};
