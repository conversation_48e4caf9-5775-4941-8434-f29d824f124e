import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const InspectionIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.2861 18.9991H8.713C8.27417 19.0133 7.84745 18.8575 7.52636 18.5657C7.20526 18.2739 7.01598 17.87 7 17.4425V6.55579C7.01621 6.1285 7.20563 5.7249 7.52672 5.43344C7.84782 5.14199 8.27441 4.98645 8.713 5.00093H14.0684V8.98559C14.082 9.3387 14.2385 9.67219 14.5038 9.91317C14.769 10.1542 15.1214 10.283 15.4838 10.2716H19V17.4416C18.9843 17.8694 18.7949 18.2737 18.4736 18.5657C18.1523 18.8577 17.7252 19.0135 17.2861 18.9991ZM10.2457 15.2569C10.2013 15.2546 10.1569 15.2611 10.1153 15.2761C10.0736 15.2911 10.0355 15.3142 10.0032 15.344C9.97102 15.3738 9.94535 15.4096 9.92782 15.4494C9.91028 15.4892 9.90124 15.532 9.90124 15.5753C9.90124 15.6186 9.91028 15.6614 9.92782 15.7012C9.94535 15.741 9.97102 15.7768 10.0032 15.8066C10.0355 15.8364 10.0736 15.8595 10.1153 15.8745C10.1569 15.8895 10.2013 15.896 10.2457 15.8937H15.9022C15.9465 15.896 15.9909 15.8895 16.0326 15.8745C16.0743 15.8595 16.1124 15.8364 16.1446 15.8066C16.1768 15.7768 16.2025 15.741 16.22 15.7012C16.2376 15.6614 16.2466 15.6186 16.2466 15.5753C16.2466 15.532 16.2376 15.4892 16.22 15.4494C16.2025 15.4096 16.1768 15.3738 16.1446 15.344C16.1124 15.3142 16.0743 15.2911 16.0326 15.2761C15.9909 15.2611 15.9465 15.2546 15.9022 15.2569H10.2457ZM10.2457 13.3462C10.1589 13.3462 10.0756 13.3798 10.0143 13.4396C9.95289 13.4994 9.91841 13.5805 9.91841 13.6651C9.91841 13.7497 9.95289 13.8308 10.0143 13.8906C10.0756 13.9504 10.1589 13.984 10.2457 13.984H15.9022C15.989 13.984 16.0722 13.9504 16.1336 13.8906C16.195 13.8308 16.2295 13.7497 16.2295 13.6651C16.2295 13.5805 16.195 13.4994 16.1336 13.4396C16.0722 13.3798 15.989 13.3462 15.9022 13.3462H10.2457ZM18.4744 9.6295H15.4838C15.3943 9.63326 15.3049 9.61959 15.2209 9.5893C15.1368 9.55901 15.0598 9.5127 14.9943 9.45308C14.9288 9.39346 14.8762 9.32172 14.8395 9.24205C14.8028 9.16239 14.7828 9.0764 14.7806 8.9891V5.69315L18.4717 9.6251L18.4744 9.6295Z"
                fill={fill}
            />
        </svg>
    );
};
