import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PtIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.84 20.6339H7.564C7.35196 20.6339 7.14213 20.5908 6.94727 20.5072C6.75242 20.4236 6.5766 20.3012 6.4305 20.1475C6.2844 19.9939 6.17107 19.8121 6.09741 19.6133C6.02375 19.4144 5.99129 19.2027 6.002 18.9909V7.15692C5.99129 6.94515 6.02375 6.73341 6.09741 6.53458C6.17107 6.33575 6.2844 6.15397 6.4305 6.0003C6.5766 5.84662 6.75242 5.72426 6.94727 5.64065C7.14213 5.55703 7.35196 5.51392 7.564 5.51392H8.832V6.75792C8.82689 6.95306 8.89941 7.14226 9.03364 7.284C9.16788 7.42573 9.35286 7.50842 9.548 7.51392H13.858C14.0531 7.50842 14.2381 7.42573 14.3724 7.284C14.5066 7.14226 14.5791 6.95306 14.574 6.75792V5.51392H15.84C16.052 5.51392 16.2619 5.55703 16.4567 5.64065C16.6516 5.72426 16.8274 5.84662 16.9735 6.0003C17.1196 6.15397 17.2329 6.33575 17.3066 6.53458C17.3802 6.73341 17.4127 6.94515 17.402 7.15692V18.9909C17.4127 19.2027 17.3802 19.4144 17.3066 19.6133C17.2329 19.8121 17.1196 19.9939 16.9735 20.1475C16.8274 20.3012 16.6516 20.4236 16.4567 20.5072C16.2619 20.5908 16.052 20.6339 15.84 20.6339ZM8.426 16.1969C8.29127 16.1969 8.16205 16.2504 8.06679 16.3457C7.97152 16.441 7.918 16.5702 7.918 16.7049C7.918 16.8396 7.97152 16.9689 8.06679 17.0641C8.16205 17.1594 8.29127 17.2129 8.426 17.2129H15.07C15.2047 17.2129 15.3339 17.1594 15.4292 17.0641C15.5245 16.9689 15.578 16.8396 15.578 16.7049C15.578 16.5702 15.5245 16.441 15.4292 16.3457C15.3339 16.2504 15.2047 16.1969 15.07 16.1969H8.426ZM8.426 13.1479C8.29504 13.1537 8.17136 13.2098 8.08075 13.3046C7.99013 13.3993 7.93955 13.5253 7.93955 13.6564C7.93955 13.7875 7.99013 13.9135 8.08075 14.0083C8.17136 14.103 8.29504 14.1591 8.426 14.1649H15.07C15.201 14.1591 15.3246 14.103 15.4152 14.0083C15.5059 13.9135 15.5564 13.7875 15.5564 13.6564C15.5564 13.5253 15.5059 13.3993 15.4152 13.3046C15.3246 13.2098 15.201 13.1537 15.07 13.1479H8.426ZM8.426 10.0999C8.29127 10.0999 8.16205 10.1534 8.06679 10.2487C7.97152 10.344 7.918 10.4732 7.918 10.6079C7.918 10.7426 7.97152 10.8719 8.06679 10.9671C8.16205 11.0624 8.29127 11.1159 8.426 11.1159H15.07C15.2047 11.1159 15.3339 11.0624 15.4292 10.9671C15.5245 10.8719 15.578 10.7426 15.578 10.6079C15.578 10.4732 15.5245 10.344 15.4292 10.2487C15.3339 10.1534 15.2047 10.0999 15.07 10.0999H8.426ZM13.155 7.02692H10.277C10.1812 7.02753 10.0863 7.00873 9.99805 6.97167C9.90975 6.9346 9.82988 6.88003 9.76325 6.81125C9.69662 6.74247 9.64461 6.66091 9.61037 6.57148C9.57612 6.48205 9.56035 6.38661 9.564 6.29092C9.55853 6.09482 9.63011 5.90439 9.7634 5.76045C9.89668 5.61651 10.0811 5.53052 10.277 5.52092C10.2779 5.10054 10.4286 4.69424 10.702 4.37492C10.8341 4.24741 10.9911 4.14848 11.1632 4.08431C11.3352 4.02013 11.5186 3.99209 11.702 4.00192C11.8854 3.99228 12.0689 4.02051 12.2409 4.08485C12.413 4.1492 12.5699 4.24828 12.702 4.37592C12.9756 4.69465 13.1275 5.0999 13.131 5.51992H13.132C13.2308 5.51829 13.3288 5.5376 13.4196 5.57657C13.5104 5.61554 13.5919 5.67329 13.6588 5.74603C13.7257 5.81876 13.7764 5.90483 13.8076 5.99857C13.8389 6.09231 13.8499 6.19161 13.84 6.28992C13.8507 6.38488 13.8408 6.48103 13.8109 6.5718C13.781 6.66257 13.7319 6.7458 13.6668 6.8158C13.6018 6.8858 13.5223 6.94091 13.434 6.97734C13.3457 7.01378 13.2505 7.03069 13.155 7.02692Z"
                fill={fill}
            />
        </svg>
    );
};
