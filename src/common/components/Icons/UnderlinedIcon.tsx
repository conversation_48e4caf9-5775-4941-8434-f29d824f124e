import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const UnderlinedIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.258 4.66113H8.975V11.5121C8.975 12.4981 9.24133 13.2745 9.774 13.8411C10.3067 14.4078 11.0603 14.6911 12.035 14.6911C13.0097 14.6911 13.7577 14.4078 14.279 13.8411C14.8117 13.2745 15.078 12.4981 15.078 11.5121V4.66113H16.795V11.5461C16.795 12.9968 16.387 14.1358 15.571 14.9631C14.7663 15.7905 13.5877 16.2041 12.035 16.2041C10.4823 16.2041 9.298 15.7905 8.482 14.9631C7.666 14.1245 7.258 12.9911 7.258 11.5631V4.66113Z"
                fill={fill}
            />
            <path d="M6 18.4121H18.07V19.5001H6V18.4121Z" fill={fill} />
        </svg>
    );
};
