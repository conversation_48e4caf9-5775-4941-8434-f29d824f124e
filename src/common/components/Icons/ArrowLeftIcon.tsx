import SvgIcon from '@mui/material/SvgIcon';
import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

const ArrowLeftIcon: typeof Icon = ({
    fill = Colors.CM1,
    size = IconSize.M,
    className = '',
    style,
}: IconProps) => {
    return (
        <SvgIcon
            width={size}
            style={{ minWidth: size, ...style }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.54782 11.9679L9.54782 11.9679L9.54678 11.9691C9.41911 12.1161 9.35 12.31 9.35 12.509C9.35 12.708 9.41911 12.902 9.54678 13.0489L9.54677 13.049L9.54799 13.0503L13.4388 17.42L13.4388 17.42L13.44 17.4214C13.5716 17.5656 13.7521 17.65 13.944 17.65C14.1359 17.65 14.3163 17.5656 14.4479 17.4214L14.448 17.4214L14.4504 17.4187C14.5781 17.2717 14.6472 17.0777 14.6472 16.8787C14.6472 16.6797 14.5781 16.4858 14.4504 16.3388L14.4504 16.3388L14.4492 16.3374L11.0332 12.5016L14.4492 8.66576L14.4492 8.66576C14.5794 8.51954 14.65 8.32459 14.65 8.12445C14.65 7.92431 14.5794 7.72937 14.4492 7.58315C14.3185 7.43636 14.1372 7.35 13.944 7.35C13.7507 7.35 13.5695 7.43636 13.4388 7.58315L13.4386 7.58334L9.54782 11.9679Z"
                fill={fill}
                stroke={fill}
                strokeWidth="0.3"
            />
        </SvgIcon>
    );
};

export default ArrowLeftIcon;
