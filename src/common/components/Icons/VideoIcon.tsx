import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const VideoIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M14.576 8H13.232C10.481 8.038 9.032 8.012 5.937 8H4.988C4.72597 8 4.47467 8.10409 4.28938 8.28938C4.10409 8.47467 4 8.72597 4 8.988V15.611C4 15.873 4.10409 16.1243 4.28938 16.3096C4.47467 16.4949 4.72597 16.599 4.988 16.599H14.576C14.838 16.599 15.0893 16.4949 15.2746 16.3096C15.4599 16.1243 15.564 15.873 15.564 15.611V8.988C15.564 8.72597 15.4599 8.47467 15.2746 8.28938C15.0893 8.10409 14.838 8 14.576 8Z"
                fill={fill}
            />
            <path
                d="M19.6171 8.81108C19.5479 8.76505 19.4681 8.73726 19.3853 8.73027C19.3024 8.72327 19.2191 8.73731 19.1431 8.77107L16.4741 10.0171V14.5841L19.1431 15.8291L19.3431 15.8691C19.4741 15.8691 19.5998 15.817 19.6924 15.7244C19.7851 15.6317 19.8371 15.5061 19.8371 15.3751V9.22607C19.8378 9.14407 19.818 9.0632 19.7796 8.99075C19.7412 8.9183 19.6854 8.85656 19.6171 8.81108Z"
                fill={fill}
            />
        </svg>
    );
};
