import { CSSProperties, FC, forwardRef, memo } from 'react';
import { IconSize } from '../../styles/IconSize';
export type IconProps = {
    fill?: string;
    size?: IconSize | number;
    className?: string;
    style?: CSSProperties;
};

export declare function Icon(props: IconProps): JSX.Element;

export function createIconComponent(
    render: () => React.ReactNode,
    defaultViewBox: string = '0 0 24 24'
): FC<IconProps> {
    return memo(
        forwardRef<SVGSVGElement, IconProps>(
            ({ fill = 'currentColor', size = 24, className, style }, ref) => {
                return (
                    <svg
                        ref={ref}
                        width={size}
                        style={{ minWidth: size, ...style }}
                        height={size}
                        viewBox={defaultViewBox}
                        fill={fill}
                        stroke={fill}
                        className={className}
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        {render()}
                    </svg>
                );
            }
        )
    );
}
