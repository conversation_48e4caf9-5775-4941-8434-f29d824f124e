import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MoveLeftIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.661 6.27105L5.27801 11.699C5.19013 11.7795 5.11995 11.8774 5.07195 11.9865C5.02394 12.0955 4.99915 12.2134 4.99915 12.3325C4.99915 12.4517 5.02394 12.5696 5.07195 12.6786C5.11995 12.7877 5.19013 12.8856 5.27801 12.966L10.661 18.399C10.8212 18.5556 11.0317 18.6505 11.2551 18.667C11.4785 18.6834 11.7005 18.6204 11.882 18.489C11.9911 18.4158 12.0819 18.3186 12.1475 18.2048C12.2131 18.091 12.2517 17.9637 12.2604 17.8327C12.2691 17.7016 12.2476 17.5703 12.1976 17.4488C12.1476 17.3274 12.0704 17.219 11.972 17.132L8.08201 13.242H17.672C17.9119 13.242 18.142 13.1468 18.3116 12.9771C18.4812 12.8075 18.5765 12.5774 18.5765 12.3375C18.5765 12.0977 18.4812 11.8676 18.3116 11.698C18.142 11.5283 17.9119 11.433 17.672 11.433H8.08201L11.972 7.54305C12.0704 7.45609 12.1476 7.34771 12.1976 7.22626C12.2476 7.1048 12.2691 6.97349 12.2604 6.84244C12.2517 6.71138 12.2131 6.58406 12.1475 6.47027C12.0819 6.35648 11.9911 6.25925 11.882 6.18605C11.7011 6.05403 11.4794 5.99016 11.256 6.00571C11.0326 6.02126 10.8219 6.11524 10.661 6.27105Z"
                fill={fill}
            />
        </svg>
    );
};
