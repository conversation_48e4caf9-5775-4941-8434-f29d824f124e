import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SyncUpIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.99565 6.35194C6.86439 6.11562 6.91541 5.81781 7.12966 5.65296C8.52226 4.58147 10.2342 3.99754 12 4.00001C16.4184 4.00001 20 7.5816 20 12C20 13.3823 19.6493 14.6829 19.0325 15.8172C18.831 16.1877 18.3138 16.1633 18.1089 15.7947L16.4718 12.849C16.2602 12.4681 16.5356 12 16.9713 12H17.8286C18.1442 12 18.4027 11.7437 18.3746 11.4294C18.2803 10.3758 17.9261 9.35886 17.3397 8.47141C16.6481 7.42457 15.6639 6.60413 14.5097 6.1121C13.3555 5.62008 12.0822 5.47817 10.848 5.70402C9.85788 5.8852 8.92804 6.29664 8.13165 6.90086C7.83305 7.12741 7.38984 7.06164 7.20784 6.73398L6.99565 6.35194ZM17.0044 17.6481C17.1356 17.8844 17.0846 18.1822 16.8703 18.347C15.4777 19.4185 13.7658 20.0025 12 20C7.5816 20 4 16.4184 4 12C4 10.6177 4.35074 9.31706 4.96753 8.18279C5.16901 7.81226 5.68622 7.83665 5.8911 8.20531L7.52816 11.151C7.73983 11.5319 7.46443 12 7.02868 12H6.17138C5.85581 12 5.59733 12.2563 5.62544 12.5706C5.71966 13.6242 6.07391 14.6411 6.66027 15.5286C7.35194 16.5754 8.33605 17.3959 9.49026 17.8879C10.6445 18.3799 11.9178 18.5218 13.152 18.296C14.1421 18.1148 15.072 17.7034 15.8684 17.0991C16.167 16.8726 16.6102 16.9384 16.7922 17.266L17.0044 17.6481Z"
                fill={fill}
            />
        </svg>
    );
};
