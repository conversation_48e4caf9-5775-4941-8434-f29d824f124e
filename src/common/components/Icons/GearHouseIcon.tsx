import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const GearHouseIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.6694 11.9998H18.0001V17.9998C18.0001 18.5521 17.5524 18.9998 17.0001 18.9998H7.00011C6.44783 18.9998 6.00011 18.5521 6.00011 17.9998V11.9998H5.33084C4.86916 11.9998 4.65413 11.4276 5.00158 11.1236L11.3416 5.57604C11.7186 5.24614 12.2816 5.24614 12.6586 5.57604L18.9986 11.1236C19.3461 11.4276 19.1311 11.9998 18.6694 11.9998ZM14.2031 14.1718L14.707 14.453C14.7656 14.4882 14.789 14.5585 14.7773 14.6171C14.6367 15.0389 14.4257 15.4139 14.1328 15.7304C14.0859 15.7772 14.0156 15.7889 13.957 15.7538L13.4531 15.4725C13.2421 15.6483 13.0078 15.7889 12.7499 15.8827V16.4569C12.7499 16.5155 12.7031 16.5741 12.6328 16.5975C12.2226 16.6796 11.789 16.6913 11.3554 16.5975C11.2968 16.5741 11.2499 16.5155 11.2499 16.4569V15.8827C10.9804 15.7889 10.746 15.6483 10.5351 15.4725L10.0312 15.7538C9.9726 15.7889 9.90229 15.7772 9.85542 15.7304C9.57417 15.4139 9.35151 15.0389 9.2226 14.6171C9.19917 14.5585 9.2226 14.4882 9.2812 14.453L9.7851 14.1718C9.73823 13.8905 9.73823 13.621 9.7851 13.3397L9.2812 13.0585C9.2226 13.0233 9.19917 12.953 9.2226 12.8944C9.35151 12.4725 9.57417 12.0975 9.85542 11.7811C9.90229 11.7343 9.9726 11.7225 10.0312 11.7577L10.5351 12.0507C10.746 11.8632 10.9804 11.7225 11.2499 11.6288V11.0546C11.2499 10.996 11.2851 10.9374 11.3554 10.9257C11.7656 10.8319 12.1992 10.8202 12.6328 10.9257C12.7031 10.9374 12.7499 10.996 12.7499 11.0546V11.6288C13.0078 11.7225 13.2421 11.8632 13.4531 12.0389L13.957 11.7577C14.0156 11.7225 14.0859 11.7343 14.1328 11.7811C14.414 12.0975 14.6367 12.4725 14.7656 12.8944C14.789 12.953 14.7656 13.0233 14.707 13.0585L14.2031 13.3397C14.2617 13.621 14.2617 13.8905 14.2031 14.1718ZM11.0624 13.7499C11.0624 14.2772 11.4726 14.6874 11.9999 14.6874C12.5156 14.6874 12.9374 14.2772 12.9374 13.7499C12.9374 13.2343 12.5156 12.8124 11.9999 12.8124C11.4726 12.8124 11.0624 13.2343 11.0624 13.7499Z"
                fill={fill}
            />
        </svg>
    );
};
