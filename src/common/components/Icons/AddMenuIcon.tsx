import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AddMenuIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M14.6471 10.875C14.6471 10.3365 14.2105 9.9 13.6721 9.9H3.975C3.43652 9.9 3 10.3365 3 10.875C3 11.4135 3.43652 11.85 3.975 11.85H13.6721C14.2105 11.85 14.6471 11.4135 14.6471 10.875ZM14.6471 6.975C14.6471 6.43652 14.2105 6 13.6721 6H3.975C3.43652 6 3 6.43652 3 6.975C3 7.51348 3.43652 7.95 3.975 7.95H13.6721C14.2105 7.95 14.6471 7.51348 14.6471 6.975ZM3 14.775C3 15.3135 3.43652 15.75 3.975 15.75H9.78971C10.3282 15.75 10.7647 15.3135 10.7647 14.775C10.7647 14.2365 10.3282 13.8 9.78971 13.8H3.975C3.43652 13.8 3 14.2365 3 14.775Z"
                fill={fill}
            />
            <path
                d="M21 14.6668C21 14.4698 20.9235 14.2808 20.7873 14.1415C20.6511 14.0022 20.4665 13.9239 20.2739 13.9239L17.4908 13.9239L17.4908 11.0764C17.4908 10.8794 17.4143 10.6904 17.2782 10.5511C17.142 10.4118 16.9573 10.3335 16.7647 10.3335C16.5721 10.3335 16.3874 10.4118 16.2513 10.5511C16.1151 10.6904 16.0386 10.8794 16.0386 11.0764L16.0386 13.9239L13.2555 13.9239C13.063 13.9239 12.8783 14.0022 12.7421 14.1415C12.6059 14.2808 12.5294 14.4698 12.5294 14.6668C12.5294 14.8639 12.6059 15.0528 12.7421 15.1922C12.8783 15.3315 13.063 15.4098 13.2555 15.4098L16.0359 15.4098L16.0359 18.2572C16.0359 18.4543 16.1124 18.6432 16.2486 18.7826C16.3848 18.9219 16.5695 19.0002 16.7621 19.0002C16.9546 19.0002 17.1393 18.9219 17.2755 18.7826C17.4117 18.6432 17.4882 18.4543 17.4882 18.2572L17.4882 15.4125L20.2739 15.4125C20.3694 15.4125 20.4641 15.3932 20.5524 15.3557C20.6406 15.3182 20.7208 15.2632 20.7883 15.1939C20.8557 15.1246 20.9091 15.0424 20.9455 14.952C20.9818 14.8615 21.0003 14.7646 21 14.6668Z"
                fill={fill}
            />
        </svg>
    );
};
