import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AppointmentsMenuIcon: typeof Icon = ({
    fill = '#ACB7C0',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.5177 6.69973H15.6444V6.23593C15.6444 6.02184 15.5594 5.81652 15.408 5.66514C15.2566 5.51376 15.0513 5.42871 14.8372 5.42871C14.6231 5.42871 14.4178 5.51376 14.2664 5.66514C14.115 5.81652 14.03 6.02184 14.03 6.23593V6.6988H9.95596V6.23593C9.95596 6.02184 9.87092 5.81652 9.71953 5.66514C9.56815 5.51376 9.36283 5.42871 9.14875 5.42871C8.93466 5.42871 8.72934 5.51376 8.57796 5.66514C8.42658 5.81652 8.34153 6.02184 8.34153 6.23593V6.6988H7.46826C7.07915 6.70028 6.70641 6.85558 6.43135 7.13081C6.15629 7.40604 6.00123 7.77888 6 8.16799V8.91758H18V8.16799C17.9978 7.77684 17.8404 7.40255 17.5625 7.12727C17.2846 6.852 16.9088 6.69824 16.5177 6.69973Z"
                fill={fill}
            />
            <path
                d="M6.32143 9.875C6.13393 9.875 6 10.0273 6 10.1797V16.7812C6 17.4668 6.5625 18 7.28571 18H16.7143C17.4107 18 18 17.4668 18 16.7812V10.1797C18 10.0273 17.8393 9.875 17.6786 9.875H6.32143ZM15.2411 12.3125C15.3482 12.4395 15.3482 12.6426 15.2411 12.7441L11.4107 16.3496C11.2768 16.4766 11.0625 16.4766 10.9554 16.3496L8.73214 14.2422C8.625 14.1152 8.625 13.9375 8.73214 13.8105L9.50893 13.0996C9.61607 12.9727 9.83036 12.9727 9.96429 13.0996L11.1964 14.2676L14.0357 11.6016C14.1429 11.4746 14.3571 11.4746 14.4911 11.6016L15.2411 12.3125Z"
                fill={fill}
            />
        </svg>
    );
};
