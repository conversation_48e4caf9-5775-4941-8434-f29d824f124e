import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PersonSpeechBalloonIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M 9.3025 12.5 C 11.272 12.5 12.9039 10.8301 12.9039 8.75 C 12.9039 6.6992 11.272 5 9.3025 5 C 7.3048 5 5.7011 6.6992 5.7011 8.75 C 5.7011 10.8301 7.3048 12.5 9.3025 12.5 Z M 11.8066 13.4375 H 11.3283 C 10.7093 13.7598 10.034 13.9062 9.3025 13.9062 C 8.5709 13.9062 7.8675 13.7598 7.2485 13.4375 H 6.7702 C 4.6882 13.4375 3 15.2246 3 17.3926 V 18.5938 C 3 19.3848 3.5909 20 4.3505 20 H 14.2544 C 14.9859 20 15.6049 19.3848 15.6049 18.5938 V 17.3926 C 15.6049 15.2246 13.8886 13.4375 11.8066 13.4375 Z"
                fill={fill}
            />
            <path
                d="M 17.5027 4 C 15.5628 4 14.0054 5.4688 14.0054 7.25 C 14.0054 8.0312 14.2922 8.7344 14.7841 9.2969 C 14.6065 10.0938 14.0327 10.7812 14.0327 10.7969 C 14.0054 10.8281 13.9917 10.8906 14.0054 10.9375 C 14.0327 10.9844 14.06 11 14.1147 11 C 15.0163 11 15.6994 10.5156 16.0272 10.2031 C 16.4781 10.3906 16.9699 10.5 17.5027 10.5 C 19.4289 10.5 21 9.0469 21 7.25 C 21 5.4688 19.4289 4 17.5027 4 Z"
                fill={fill}
            />
        </svg>
    );
};
