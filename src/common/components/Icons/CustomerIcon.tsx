import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CustomerIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_36614_72574)">
                <rect width="18" height="18" rx="9" fill={fill} />
                <path
                    d="M10.4198 9.05348C11.0546 8.75258 11.568 8.24462 11.8757 7.61314C12.1834 6.98167 12.267 6.26428 12.1128 5.57897C11.9586 4.89366 11.5757 4.28123 11.0272 3.84243C10.4787 3.40362 9.79713 3.16455 9.09468 3.16455C8.39223 3.16455 7.7107 3.40362 7.16218 3.84243C6.61365 4.28123 6.2308 4.89366 6.07659 5.57897C5.92238 6.26428 6.00599 6.98167 6.31367 7.61314C6.62135 8.24462 7.13479 8.75258 7.76952 9.05348C6.46254 9.35362 5.296 10.0881 4.46048 11.137C3.62496 12.1859 2.79471 14.8323 4.46108 14.8323H13.7284C15.3948 14.8323 14.564 12.1862 13.7285 11.1373C12.8931 10.0885 11.7267 9.35389 10.4198 9.05348Z"
                    fill="white"
                />
            </g>
            <defs>
                <clipPath id="clip0_36614_72574">
                    <rect width="18" height="18" rx="9" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};
