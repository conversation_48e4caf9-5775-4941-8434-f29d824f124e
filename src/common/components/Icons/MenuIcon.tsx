import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MenuIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5.24188 7.06409H19.0589C19.1852 7.07596 19.3125 7.06134 19.4328 7.02116C19.5531 6.98098 19.6637 6.91612 19.7574 6.83074C19.8512 6.74537 19.9261 6.64135 19.9774 6.52535C20.0287 6.40934 20.0552 6.28392 20.0552 6.15709C20.0552 6.03027 20.0287 5.90484 19.9774 5.78884C19.9261 5.67284 19.8512 5.56882 19.7574 5.48344C19.6637 5.39807 19.5531 5.33321 19.4328 5.29303C19.3125 5.25285 19.1852 5.23822 19.0589 5.25009H5.24188C5.11561 5.23822 4.98826 5.25285 4.86797 5.29303C4.74768 5.33321 4.6371 5.39807 4.54332 5.48344C4.44954 5.56882 4.37462 5.67284 4.32335 5.78884C4.27209 5.90484 4.24561 6.03027 4.24561 6.15709C4.24561 6.28392 4.27209 6.40934 4.32335 6.52535C4.37462 6.64135 4.44954 6.74537 4.54332 6.83074C4.6371 6.91612 4.74768 6.98098 4.86797 7.02116C4.98826 7.06134 5.11561 7.07596 5.24188 7.06409Z"
                fill={fill}
                stroke="#0069FF"
                strokeWidth="0.5"
            />
            <path
                d="M19.0589 10.693H5.24188C5.11561 10.6811 4.98826 10.6957 4.86797 10.7359C4.74768 10.7761 4.6371 10.8409 4.54332 10.9263C4.44954 11.0117 4.37462 11.1157 4.32335 11.2317C4.27209 11.3477 4.24561 11.4731 4.24561 11.6C4.24561 11.7268 4.27209 11.8522 4.32335 11.9682C4.37462 12.0842 4.44954 12.1882 4.54332 12.2736C4.6371 12.359 4.74768 12.4238 4.86797 12.464C4.98826 12.5042 5.11561 12.5188 5.24188 12.507H19.0589C19.1852 12.5188 19.3125 12.5042 19.4328 12.464C19.5531 12.4238 19.6637 12.359 19.7574 12.2736C19.8512 12.1882 19.9261 12.0842 19.9774 11.9682C20.0287 11.8522 20.0552 11.7268 20.0552 11.6C20.0552 11.4731 20.0287 11.3477 19.9774 11.2317C19.9261 11.1157 19.8512 11.0117 19.7574 10.9263C19.6637 10.8409 19.5531 10.7761 19.4328 10.7359C19.3125 10.6957 19.1852 10.6811 19.0589 10.693Z"
                fill={fill}
                stroke="#0069FF"
                strokeWidth="0.5"
            />
            <path
                d="M19.0589 16.1349H5.24188C5.11561 16.123 4.98826 16.1376 4.86797 16.1778C4.74768 16.218 4.6371 16.2828 4.54332 16.3682C4.44954 16.4536 4.37462 16.5576 4.32335 16.6736C4.27209 16.7896 4.24561 16.915 4.24561 17.0419C4.24561 17.1687 4.27209 17.2941 4.32335 17.4101C4.37462 17.5261 4.44954 17.6301 4.54332 17.7155C4.6371 17.8009 4.74768 17.8657 4.86797 17.9059C4.98826 17.9461 5.11561 17.9607 5.24188 17.9489H19.0589C19.1852 17.9607 19.3125 17.9461 19.4328 17.9059C19.5531 17.8657 19.6637 17.8009 19.7574 17.7155C19.8512 17.6301 19.9261 17.5261 19.9774 17.4101C20.0287 17.2941 20.0552 17.1687 20.0552 17.0419C20.0552 16.915 20.0287 16.7896 19.9774 16.6736C19.9261 16.5576 19.8512 16.4536 19.7574 16.3682C19.6637 16.2828 19.5531 16.218 19.4328 16.1778C19.3125 16.1376 19.1852 16.123 19.0589 16.1349Z"
                fill={fill}
                stroke="#0069FF"
                strokeWidth="0.5"
            />
        </svg>
    );
};
