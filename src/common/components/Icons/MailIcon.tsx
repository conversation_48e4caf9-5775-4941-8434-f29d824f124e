import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MailIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M3.15306 7.72217C3.0463 7.91603 2.99347 8.13496 3.00006 8.35617V16.4022C2.99293 16.5599 3.02273 16.717 3.08706 16.8612L8.24706 11.7012L3.15306 7.72217Z"
                fill={fill}
            />
            <path
                d="M20.251 7.72217L15.157 11.7012L20.317 16.8612C20.367 16.7129 20.3962 16.5584 20.404 16.4022V8.35617C20.3999 8.13619 20.3477 7.91978 20.251 7.72217Z"
                fill={fill}
            />
            <path
                d="M10.871 12.3129C11.1058 12.5039 11.3993 12.6081 11.702 12.6081C12.0047 12.6081 12.2982 12.5039 12.533 12.3129L19.289 7.02187C19.21 7.00372 19.1289 6.99632 19.048 6.99987H4.35599C4.27501 6.99524 4.19379 7.00266 4.11499 7.02187L9.44999 11.2219L10.871 12.3129Z"
                fill={fill}
            />
            <path
                d="M13.232 13.2089C12.7925 13.546 12.2549 13.7304 11.701 13.7339C11.1459 13.7374 10.6061 13.5523 10.17 13.2089L9.16597 12.4219L3.91797 17.6699C4.06624 17.7199 4.22069 17.7491 4.37697 17.7569H19.048C19.2057 17.764 19.3628 17.7342 19.507 17.6699L14.26 12.4219L13.232 13.2089Z"
                fill={fill}
            />
        </svg>
    );
};
