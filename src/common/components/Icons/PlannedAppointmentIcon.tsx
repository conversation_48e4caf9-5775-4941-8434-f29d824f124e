import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PlannedAppointmentIcon: typeof Icon = ({
    fill = Colors.Grey5,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 15 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.3236 4.18746H9.77776V3.89758C9.77776 3.76377 9.7246 3.63545 9.62999 3.54083C9.53538 3.44622 9.40705 3.39307 9.27325 3.39307C9.13945 3.39307 9.01112 3.44622 8.91651 3.54083C8.82189 3.63545 8.76874 3.76377 8.76874 3.89758V4.18687H6.22248V3.89758C6.22248 3.76377 6.16932 3.63545 6.07471 3.54083C5.9801 3.44622 5.85177 3.39307 5.71797 3.39307C5.58416 3.39307 5.45584 3.44622 5.36122 3.54083C5.26661 3.63545 5.21346 3.76377 5.21346 3.89758V4.18687H4.66766C4.42447 4.1878 4.19151 4.28486 4.01959 4.45688C3.84768 4.6289 3.75077 4.86192 3.75 5.10512V5.57361H11.25V5.10512C11.2486 4.86065 11.1503 4.62671 10.9766 4.45467C10.8029 4.28262 10.568 4.18652 10.3236 4.18746Z"
                fill={fill}
            />
            <path
                d="M3.95089 6.17188C3.83371 6.17188 3.75 6.26709 3.75 6.3623V10.4883C3.75 10.9167 4.10156 11.25 4.55357 11.25H10.4464C10.8817 11.25 11.25 10.9167 11.25 10.4883V6.3623C11.25 6.26709 11.1496 6.17188 11.0491 6.17188H3.95089ZM9.52567 7.69531C9.59263 7.77466 9.59263 7.90161 9.52567 7.96509L7.1317 10.2185C7.04799 10.2979 6.91406 10.2979 6.8471 10.2185L5.45759 8.90137C5.39062 8.82202 5.39062 8.71094 5.45759 8.63159L5.94308 8.18726C6.01004 8.10791 6.14397 8.10791 6.22768 8.18726L6.99777 8.91724L8.77232 7.25098C8.83929 7.17163 8.97321 7.17163 9.05692 7.25098L9.52567 7.69531Z"
                fill={fill}
            />
        </svg>
    );
};
