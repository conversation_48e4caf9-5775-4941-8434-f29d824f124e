import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const RemoveOperationIcon: typeof Icon = ({ size = IconSize.M, className }: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.3477 4.78662C16.6289 4.78662 20.0977 8.25537 20.0977 12.5366C20.0977 16.8179 16.6289 20.2866 12.3477 20.2866C8.06641 20.2866 4.59766 16.8179 4.59766 12.5366C4.59766 8.25537 8.06641 4.78662 12.3477 4.78662ZM8.22266 13.7866H16.4727C16.6602 13.7866 16.8477 13.6304 16.8477 13.4116V11.6616C16.8477 11.4741 16.6602 11.2866 16.4727 11.2866H8.22266C8.00391 11.2866 7.84766 11.4741 7.84766 11.6616V13.4116C7.84766 13.6304 8.00391 13.7866 8.22266 13.7866Z"
                fill="#F15857"
            />
        </svg>
    );
};
