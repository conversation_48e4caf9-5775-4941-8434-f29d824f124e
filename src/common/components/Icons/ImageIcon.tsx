import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ImageIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.7891 19.1H5.61112C5.44579 19.0966 5.28858 19.0277 5.17401 18.9085C5.05945 18.7892 4.9969 18.6294 5.00012 18.464V17.798H5.01112V5.42702C5.04519 5.32051 5.10758 5.22526 5.19159 5.15146C5.27561 5.07766 5.37811 5.02808 5.48812 5.00802H17.5771V4.99902H17.7681C17.936 5.00086 18.0963 5.06855 18.2147 5.18751C18.3331 5.30647 18.4001 5.46718 18.4011 5.63502V18.464C18.4027 18.546 18.3881 18.6274 18.3582 18.7037C18.3284 18.7801 18.2837 18.8497 18.2269 18.9088C18.17 18.9679 18.1021 19.0152 18.027 19.048C17.9519 19.0808 17.8711 19.0985 17.7891 19.1ZM6.20112 6.24802V15.058H17.1781V6.24802H6.20112ZM15.4761 13.786H7.84112C7.79728 13.7857 7.75434 13.7736 7.71684 13.7509C7.67934 13.7282 7.64869 13.6957 7.62812 13.657C7.60582 13.618 7.59367 13.5741 7.5928 13.5292C7.59193 13.4843 7.60235 13.4398 7.62312 13.4L10.3071 8.95002C10.3281 8.91084 10.3592 8.878 10.3972 8.85496C10.4352 8.83191 10.4787 8.8195 10.5231 8.81902C10.5628 8.8191 10.6018 8.829 10.6367 8.84784C10.6716 8.86669 10.7013 8.89389 10.7231 8.92702L12.3601 11.22C12.3835 11.251 12.4137 11.2762 12.4484 11.2935C12.4831 11.3109 12.5213 11.3199 12.5601 11.32C12.5925 11.3199 12.6246 11.3132 12.6543 11.3003C12.6841 11.2874 12.7109 11.2686 12.7331 11.245L13.4091 10.564C13.4304 10.5412 13.456 10.5229 13.4846 10.5104C13.5131 10.4978 13.5439 10.4912 13.5751 10.491C13.6165 10.4919 13.6569 10.5031 13.6927 10.5238C13.7286 10.5444 13.7586 10.5737 13.7801 10.609L15.6801 13.379C15.7086 13.414 15.7265 13.4564 15.7318 13.5012C15.7371 13.546 15.7296 13.5913 15.7101 13.632C15.6909 13.6782 15.6584 13.7175 15.6167 13.7451C15.575 13.7727 15.5261 13.7863 15.4761 13.786ZM14.1671 9.47202C13.9724 9.4798 13.7797 9.42916 13.614 9.3266C13.4483 9.22403 13.317 9.07425 13.2371 8.89649C13.1571 8.71873 13.1322 8.52113 13.1654 8.32909C13.1987 8.13704 13.2886 7.95933 13.4237 7.81881C13.5588 7.67828 13.7328 7.58136 13.9233 7.5405C14.1139 7.49965 14.3123 7.51673 14.4931 7.58955C14.6739 7.66236 14.8288 7.78759 14.9379 7.94912C15.0469 8.11066 15.1052 8.30112 15.1051 8.49602C15.1099 8.74987 15.0139 8.99527 14.8381 9.17842C14.6622 9.36156 14.4209 9.46651 14.1671 9.47202Z"
                fill={fill}
            />
        </svg>
    );
};
