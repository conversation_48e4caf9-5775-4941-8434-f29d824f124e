import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const NaIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.01796 10.6241V16.0001H4.31396V7.99609H6.06596L9.77397 13.1801V7.99609H11.478V16.0001H9.83397L6.01796 10.6241Z"
                fill={fill}
            />
            <path
                d="M18.1562 14.6201H14.5802L14.0762 16.0001H12.2042L15.3002 7.99609H17.4362L20.5202 16.0001H18.6602L18.1562 14.6201ZM15.0362 13.1441H17.6882L16.3682 9.47209L15.0362 13.1441Z"
                fill={fill}
            />
        </svg>
    );
};
