/*<svg width="13" height="16" viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.40576 11.8569L0.405762 6.70769L6.40576 0.608398V4.42516C9.71952 4.42516 12.4058 8.30209 12.4058 14.266C12.4058 14.5606 12.3995 14.8526 12.3863 15.1434C11.9131 13.456 11.2359 11.9809 10.4042 10.8267C9.57258 9.67245 8.60774 8.86845 7.58069 8.47381L6.40576 8.24693V11.8569Z" fill="#467CFC"/>
</svg>*/
import { Colors } from '../../styles/Colors';
import { Icon, IconProps } from './Icon';

export const ReplyIcon: typeof Icon = ({ fill = Colors.CM2, className }: IconProps) => {
    return (
        <svg
            width={14}
            style={{ minWidth: 14 }}
            height={14}
            viewBox="0 0 14 14"
            className={className}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.40576 11.8569L0.405762 6.70769L6.40576 0.608398V4.42516C9.71952 4.42516 12.4058 8.30209 12.4058 14.266C12.4058 14.5606 12.3995 14.8526 12.3863 15.1434C11.9131 13.456 11.2359 11.9809 10.4042 10.8267C9.57258 9.67245 8.60774 8.86845 7.58069 8.47381L6.40576 8.24693V11.8569Z"
                fill={fill}
            />
        </svg>
    );
};
