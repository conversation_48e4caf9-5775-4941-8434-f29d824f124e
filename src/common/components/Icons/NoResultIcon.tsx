import { Icon } from './Icon';

export const NoResultIcon: typeof Icon = () => {
    return (
        <svg
            width="66"
            height="65"
            viewBox="0 0 66 65"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M64.7245 62.793C66.075 60.4821 64.4869 58.3039 63.5241 57.5036C58.0846 52.1642 46.628 40.8251 44.3172 38.1842C42.0064 35.5432 39.8781 35.8083 39.1029 36.271C37.9274 35.0205 35.5691 32.5647 35.5391 32.7447C35.5091 32.9248 33.951 34.4203 33.1757 35.1456C33.826 35.8333 35.419 37.4789 36.5895 38.5593C35.9292 40.9902 37.2897 42.6733 38.0525 43.211C43.9796 49.2131 56.2615 61.5925 57.9721 63.0931C59.5476 65.2313 63.0364 65.6815 64.7245 62.793Z"
                fill="#4A4D51"
            />
            <path
                d="M26.0469 6.56088C22.9083 5.53551 15.093 4.83526 8.94077 10.2372C11.4167 7.42368 16.706 2.17182 26.0469 6.56088Z"
                fill="#4A4D51"
            />
            <path
                d="M28.1632 31.8749C25.5195 33.1803 18.6651 35.1108 12.3966 32.3896C15.013 33.9404 20.4915 36.7127 28.1632 31.8749Z"
                fill="#4A4D51"
            />
            <circle
                cx="16.9185"
                cy="16.9185"
                r="17.9185"
                transform="matrix(-1 0 0 1 36.5898 2.17188)"
                stroke="#4A4D51"
                stroke-width="2"
            />
            <path
                d="M24.6216 14.1412C24.3965 13.9161 24.0913 13.7897 23.773 13.7897C23.4547 13.7897 23.1495 13.9161 22.9244 14.1412L19.6719 17.3937L16.4194 14.1412C16.1943 13.9161 15.889 13.7896 15.5707 13.7896C15.2524 13.7896 14.9472 13.9161 14.7221 14.1412C14.4971 14.3662 14.3706 14.6715 14.3706 14.9898C14.3706 15.3081 14.4971 15.6133 14.7221 15.8384L17.9746 19.0909L14.7221 22.3434C14.4971 22.5685 14.3706 22.8737 14.3706 23.192C14.3706 23.5103 14.4971 23.8156 14.7221 24.0406C14.9472 24.2657 15.2525 24.3922 15.5707 24.3922C15.889 24.3922 16.1943 24.2657 16.4194 24.0406L19.6688 20.7912L22.9213 24.0437C23.1464 24.2688 23.4516 24.3952 23.7699 24.3952C24.0882 24.3952 24.3935 24.2688 24.6185 24.0437C24.8436 23.8187 24.97 23.5134 24.97 23.1951C24.97 22.8768 24.8436 22.5716 24.6185 22.3465L21.3691 19.0971L24.6247 15.8415C24.7364 15.7298 24.825 15.5971 24.8853 15.4511C24.9456 15.3051 24.9765 15.1486 24.9762 14.9907C24.9759 14.8327 24.9445 14.6763 24.8836 14.5306C24.8228 14.3848 24.7337 14.2525 24.6216 14.1412Z"
                fill="#4A4D51"
            />
        </svg>
    );
};
