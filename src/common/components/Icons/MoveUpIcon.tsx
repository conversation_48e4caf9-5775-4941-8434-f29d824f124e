import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MoveUpIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5.29907 10.2462L11.2871 4.30821C11.3759 4.21095 11.484 4.13326 11.6045 4.08011C11.7251 4.02696 11.8553 3.99951 11.9871 3.99951C12.1188 3.99951 12.2491 4.02696 12.3696 4.08011C12.4901 4.13326 12.5982 4.21095 12.6871 4.30821L18.6751 10.2462C18.8479 10.4229 18.9527 10.655 18.971 10.9015C18.9893 11.1479 18.9199 11.3929 18.7751 11.5932C18.6944 11.714 18.587 11.8147 18.4613 11.8875C18.3355 11.9602 18.1947 12.0031 18.0497 12.0127C17.9047 12.0224 17.7595 11.9986 17.6252 11.9432C17.4909 11.8878 17.3711 11.8023 17.2751 11.6932L12.9841 7.40021V17.9802C12.9841 18.2454 12.8787 18.4998 12.6912 18.6873C12.5036 18.8749 12.2493 18.9802 11.9841 18.9802C11.7189 18.9802 11.4645 18.8749 11.277 18.6873C11.0894 18.4998 10.9841 18.2454 10.9841 17.9802V7.40021L6.69907 11.6932C6.60307 11.8023 6.48329 11.8878 6.34897 11.9432C6.21466 11.9986 6.06941 12.0224 5.92444 12.0127C5.77947 12.0031 5.63866 11.9602 5.51289 11.8875C5.38711 11.8147 5.27974 11.714 5.19907 11.5932C5.05423 11.3929 4.98482 11.1479 5.00312 10.9015C5.02142 10.655 5.12624 10.4229 5.29907 10.2462Z"
                fill={fill}
            />
        </svg>
    );
};
