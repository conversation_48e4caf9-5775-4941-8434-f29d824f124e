import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ListIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.3201 18.1331H8.05444V14.552H19.3201C19.505 14.5572 19.6804 14.6356 19.8079 14.7698C19.9353 14.9039 20.0045 15.0831 20.0002 15.2681V17.416C20.0048 17.6012 19.9358 17.7806 19.8083 17.915C19.6809 18.0494 19.5052 18.1278 19.3201 18.1331ZM6.67602 18.1331H3.67602C3.4907 18.1281 3.31492 18.0497 3.18725 17.9153C3.05958 17.7809 2.99079 17.6013 2.99536 17.416V15.2681C2.99106 15.0829 3.0601 14.9035 3.18774 14.7693C3.31538 14.6351 3.49088 14.557 3.67602 14.552H6.67602V18.1321V18.1331ZM19.3201 13.3572H8.05444V9.77515H19.3201C19.5054 9.78038 19.6813 9.8588 19.8088 9.99341C19.9363 10.128 20.0051 10.3078 20.0002 10.4932V12.6411C20.0045 12.8261 19.9353 13.0052 19.8079 13.1394C19.6804 13.2736 19.505 13.3519 19.3201 13.3572ZM6.67602 13.3572H3.67602C3.49088 13.3522 3.31538 13.2738 3.18774 13.1396C3.0601 13.0054 2.99106 12.8263 2.99536 12.6411V10.4932C2.99052 10.3077 3.05955 10.1278 3.18725 9.99316C3.31495 9.85853 3.49052 9.78012 3.67602 9.77515H6.67602V13.356V13.3572ZM19.3201 8.58105H8.05444V4.99902H19.3201C19.5049 5.00426 19.6805 5.08235 19.8079 5.21631C19.9353 5.35026 20.0043 5.52929 20.0002 5.71411V7.86304C20.0051 8.04837 19.9363 8.22819 19.8088 8.36279C19.6813 8.4974 19.5054 8.57582 19.3201 8.58105ZM6.67602 8.58105H3.67602C3.4907 8.57608 3.31492 8.4977 3.18725 8.36328C3.05958 8.22886 2.99079 8.04934 2.99536 7.86401V5.71509C2.99132 5.5301 3.06061 5.35102 3.18823 5.21704C3.31584 5.08306 3.49106 5.00498 3.67602 5H6.67602V8.58008V8.58105Z"
                fill={fill}
            />
        </svg>
    );
};
