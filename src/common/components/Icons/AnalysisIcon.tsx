import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AnalysisIcon: typeof Icon = ({ size = IconSize.M, className }: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.0449 20.8348H8.04492C7.24927 20.8348 6.48621 20.5187 5.9236 19.9561C5.36099 19.3935 5.04492 18.6304 5.04492 17.8348V8.34277C5.04492 7.54712 5.36099 6.78406 5.9236 6.22145C6.48621 5.65884 7.24927 5.34277 8.04492 5.34277H15.0449C15.8406 5.34277 16.6036 5.65884 17.1662 6.22145C17.7289 6.78406 18.0449 7.54712 18.0449 8.34277V17.8348C18.0449 18.6304 17.7289 19.3935 17.1662 19.9561C16.6036 20.5187 15.8406 20.8348 15.0449 20.8348ZM8.29892 13.6208C8.23797 13.6206 8.17758 13.6325 8.12127 13.6558C8.06496 13.6792 8.01386 13.7135 7.97092 13.7568C7.88421 13.8439 7.83553 13.9618 7.83553 14.0848C7.83553 14.2077 7.88421 14.3256 7.97092 14.4128L8.57092 15.0128L7.97092 15.6128C7.88421 15.6999 7.83553 15.8178 7.83553 15.9408C7.83553 16.0637 7.88421 16.1816 7.97092 16.2688C8.05793 16.3557 8.17591 16.4046 8.29892 16.4046C8.42193 16.4046 8.53991 16.3557 8.62692 16.2688L9.22692 15.6688L9.82692 16.2688C9.91419 16.3552 10.0321 16.4038 10.1549 16.4038C10.2778 16.4038 10.3957 16.3552 10.4829 16.2688C10.5696 16.1816 10.6183 16.0637 10.6183 15.9408C10.6183 15.8178 10.5696 15.6999 10.4829 15.6128L9.88292 15.0128L10.4829 14.4128C10.5664 14.3251 10.6124 14.2082 10.6109 14.0871C10.6094 13.966 10.5607 13.8503 10.475 13.7647C10.3894 13.679 10.2737 13.6303 10.1526 13.6288C10.0315 13.6273 9.91462 13.6733 9.82692 13.7568L9.22692 14.3568L8.62692 13.7568C8.58389 13.7136 8.53276 13.6794 8.47648 13.6561C8.42019 13.6327 8.35985 13.6207 8.29892 13.6208ZM12.0089 14.5428C11.8859 14.5428 11.7678 14.5917 11.6808 14.6787C11.5938 14.7657 11.5449 14.8837 11.5449 15.0068C11.5449 15.1298 11.5938 15.2479 11.6808 15.3349C11.7678 15.4219 11.8859 15.4708 12.0089 15.4708H14.7909C14.914 15.4708 15.032 15.4219 15.119 15.3349C15.206 15.2479 15.2549 15.1298 15.2549 15.0068C15.2549 14.8837 15.206 14.7657 15.119 14.6787C15.032 14.5917 14.914 14.5428 14.7909 14.5428H12.0089ZM8.29892 8.98277C8.17655 8.98277 8.05913 9.0311 7.97223 9.11726C7.88533 9.20341 7.83598 9.32041 7.83492 9.44277V11.3008C7.83492 11.4238 7.88381 11.5419 7.97082 11.6289C8.05784 11.7159 8.17586 11.7648 8.29892 11.7648H10.1529C10.276 11.7648 10.394 11.7159 10.481 11.6289C10.568 11.5419 10.6169 11.4238 10.6169 11.3008V9.44277C10.6169 9.31971 10.568 9.20169 10.481 9.11468C10.394 9.02766 10.276 8.97877 10.1529 8.97877L8.29892 8.98277ZM12.0099 9.91077C11.9472 9.90786 11.8846 9.91769 11.8259 9.93967C11.7671 9.96165 11.7134 9.99532 11.668 10.0386C11.6226 10.082 11.5865 10.1341 11.5618 10.1917C11.5371 10.2494 11.5244 10.3115 11.5244 10.3743C11.5244 10.437 11.5371 10.4991 11.5618 10.5568C11.5865 10.6145 11.6226 10.6666 11.668 10.7099C11.7134 10.7532 11.7671 10.7869 11.8259 10.8089C11.8846 10.8309 11.9472 10.8407 12.0099 10.8378H14.7909C14.8536 10.8407 14.9162 10.8309 14.975 10.8089C15.0338 10.7869 15.0875 10.7532 15.1328 10.7099C15.1782 10.6666 15.2144 10.6145 15.239 10.5568C15.2637 10.4991 15.2765 10.437 15.2765 10.3743C15.2765 10.3115 15.2637 10.2494 15.239 10.1917C15.2144 10.1341 15.1782 10.082 15.1328 10.0386C15.0875 9.99532 15.0338 9.96165 14.975 9.93967C14.9162 9.91769 14.8536 9.90786 14.7909 9.91077H12.0099ZM9.69092 10.8378H8.76292V9.91077H9.68992V10.8368L9.69092 10.8378Z"
                fill="#ACB7C0"
            />
            <path
                d="M16.1808 5.03078H13.3988C13.3988 4.78718 13.3508 4.54596 13.2576 4.3209C13.1644 4.09584 13.0277 3.89135 12.8555 3.7191C12.6832 3.54685 12.4787 3.41021 12.2537 3.31698C12.0286 3.22376 11.7874 3.17578 11.5438 3.17578C11.3002 3.17578 11.059 3.22376 10.8339 3.31698C10.6088 3.41021 10.4044 3.54685 10.2321 3.7191C10.0598 3.89135 9.92321 4.09584 9.82998 4.3209C9.73676 4.54596 9.68878 4.78718 9.68878 5.03078H6.90578C6.41397 5.03131 5.94245 5.22692 5.59468 5.57468C5.24692 5.92245 5.05131 6.39397 5.05078 6.88578V18.9438C5.05131 19.4356 5.24692 19.9071 5.59468 20.2549C5.94245 20.6026 6.41397 20.7983 6.90578 20.7988H16.1808C16.6726 20.7983 17.1441 20.6026 17.4919 20.2549C17.8396 19.9071 18.0353 19.4356 18.0358 18.9438V6.88578C18.0353 6.39397 17.8396 5.92245 17.4919 5.57468C17.1441 5.22692 16.6726 5.03131 16.1808 5.03078ZM17.1088 18.9438C17.1088 19.1899 17.011 19.4259 16.837 19.6C16.6629 19.774 16.4269 19.8718 16.1808 19.8718H6.90578C6.65966 19.8718 6.42362 19.774 6.24959 19.6C6.07555 19.4259 5.97778 19.1899 5.97778 18.9438V6.88578C5.97778 6.63966 6.07555 6.40362 6.24959 6.22959C6.42362 6.05555 6.65966 5.95778 6.90578 5.95778H7.83378V6.88578H15.2508V5.95878H16.1788C16.4249 5.95878 16.6609 6.05655 16.835 6.23059C17.009 6.40462 17.1068 6.64066 17.1068 6.88678L17.1088 18.9438Z"
                fill="#ACB7C0"
            />
        </svg>
    );
};
