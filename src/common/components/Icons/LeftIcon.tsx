import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const LeftIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.9439 7.2002C13.7049 7.2002 13.4838 7.30713 13.3267 7.48359L9.43558 11.8685L9.65997 12.0677L9.43349 11.8709C9.28074 12.0468 9.19995 12.2762 9.19995 12.5092C9.19995 12.7422 9.28072 12.9717 9.43348 13.1475L13.3267 17.52L13.3292 17.5227C13.4872 17.6959 13.707 17.8002 13.9439 17.8002C14.1809 17.8002 14.4007 17.6959 14.5588 17.5228L14.5636 17.5172C14.7163 17.3414 14.7971 17.1119 14.7971 16.8789C14.7971 16.6459 14.7164 16.4165 14.5636 16.2406L11.234 12.5018L14.5612 8.7657C14.7173 8.59037 14.8 8.35931 14.8 8.12465C14.8 7.88998 14.7173 7.65892 14.5612 7.48359C14.404 7.30713 14.1829 7.2002 13.9439 7.2002ZM14.0696 7.51634C14.0287 7.50569 13.9866 7.5002 13.9439 7.5002C13.7965 7.5002 13.655 7.56599 13.5508 7.68309L9.65997 12.0677C9.55738 12.1858 9.49995 12.3442 9.49995 12.5092C9.49995 12.6742 9.55738 12.8327 9.65997 12.9508L13.5508 17.3205C13.6086 17.3839 13.6774 17.4319 13.752 17.4623C13.6774 17.4319 13.6087 17.3839 13.5508 17.3205L9.66002 12.9508C9.55743 12.8327 9.5 12.6742 9.5 12.5092C9.5 12.3442 9.55743 12.1858 9.66002 12.0677L13.5508 7.68311C13.6551 7.566 13.7965 7.50021 13.944 7.50021C13.9866 7.50021 14.0287 7.5057 14.0696 7.51634Z"
                fill={fill}
            />
        </svg>
    );
};
