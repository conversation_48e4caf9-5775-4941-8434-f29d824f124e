import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SignalIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.793 7.00012C14.1802 6.98855 16.4912 7.83766 18.3004 9.39107C18.4401 9.50846 18.5732 9.63342 18.6992 9.76538C18.7879 9.8316 18.8608 9.91659 18.9127 10.0142C18.9645 10.1119 18.9941 10.2198 18.9992 10.3302C19.0043 10.4406 18.9849 10.5507 18.9422 10.6527C18.8996 10.7547 18.8349 10.8461 18.7527 10.9201C18.3774 11.2945 18.0021 11.1504 17.6662 10.8266C16.6035 9.76438 15.2567 9.02984 13.7867 8.71074C12.499 8.40986 11.1567 8.43186 9.87967 8.77479C8.60264 9.11771 7.43068 9.77088 6.46855 10.6759C6.22336 10.9151 5.89832 11.0557 5.55566 11.0708C4.89891 10.9772 4.80508 10.2445 5.3774 9.698C6.77078 8.3429 8.55151 7.45255 10.4738 7.14984C10.9805 7.06749 11.4955 7.03193 11.793 7.00012Z"
                fill={fill}
            />
            <path
                d="M12.4995 10.6422C13.822 10.7317 15.074 11.2703 16.0469 12.1684C16.4484 12.5165 16.7674 12.932 16.3546 13.4168C15.9418 13.9015 15.5102 13.6713 15.0946 13.3035C14.2638 12.536 13.1766 12.104 12.0443 12.0916C10.912 12.0791 9.8155 12.4871 8.968 13.2362C8.90889 13.2848 8.84885 13.3354 8.79443 13.3887C8.72938 13.4783 8.64447 13.5516 8.54633 13.6031C8.44819 13.6546 8.33947 13.6828 8.22861 13.6855C8.11776 13.6882 8.00777 13.6654 7.90722 13.6188C7.80666 13.5722 7.71825 13.503 7.64886 13.4168C7.29046 13.0256 7.48467 12.6232 7.82806 12.2817C8.45885 11.7131 9.19669 11.2754 9.99866 10.9939C10.8006 10.7125 11.6507 10.5929 12.4995 10.6422Z"
                fill={fill}
            />
            <path
                d="M13.3664 15.5965C13.3722 15.9601 13.2342 16.3113 12.9822 16.5741C12.7302 16.8368 12.3845 16.9899 12.0201 17.0002C11.6553 16.9931 11.3066 16.8493 11.0434 16.5973C10.7801 16.3453 10.6217 16.0037 10.5996 15.6405C10.6022 15.2768 10.747 14.9285 11.0032 14.6696C11.2594 14.4108 11.6067 14.2619 11.9713 14.2546C12.1522 14.2488 12.3323 14.2794 12.5011 14.3444C12.6699 14.4094 12.8239 14.5076 12.954 14.6331C13.084 14.7586 13.1874 14.9089 13.2581 15.075C13.3288 15.2412 13.3653 15.4198 13.3655 15.6003L13.3664 15.5965Z"
                fill={fill}
            />
        </svg>
    );
};
