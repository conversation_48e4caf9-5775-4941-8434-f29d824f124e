import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const EmojisIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.7812 4.96875C8.03516 4.96875 5 8.00391 5 11.75C5 15.4961 8.03516 18.5312 11.7812 18.5312C15.5273 18.5312 18.5625 15.4961 18.5625 11.75C18.5625 8.00391 15.5273 4.96875 11.7812 4.96875ZM11.7812 17.2188C8.74609 17.2188 6.3125 14.7852 6.3125 11.75C6.3125 8.74219 8.74609 6.28125 11.7812 6.28125C14.7891 6.28125 17.25 8.74219 17.25 11.75C17.25 14.7852 14.7891 17.2188 11.7812 17.2188ZM9.59375 11.3125C10.0586 11.3125 10.4688 10.9297 10.4688 10.4375C10.4688 9.97266 10.0586 9.5625 9.59375 9.5625C9.10156 9.5625 8.71875 9.97266 8.71875 10.4375C8.71875 10.9297 9.10156 11.3125 9.59375 11.3125ZM13.9688 11.3125C14.4336 11.3125 14.8438 10.9297 14.8438 10.4375C14.8438 9.97266 14.4336 9.5625 13.9688 9.5625C13.4766 9.5625 13.0938 9.97266 13.0938 10.4375C13.0938 10.9297 13.4766 11.3125 13.9688 11.3125ZM14.0781 13.3086C13.5039 13.9922 12.6562 14.375 11.7812 14.375C10.8789 14.375 10.0312 13.9922 9.48438 13.3086C9.23828 13.0352 8.82812 13.0078 8.55469 13.2266C8.28125 13.4453 8.22656 13.8828 8.47266 14.1562C9.29297 15.1406 10.4961 15.6875 11.7812 15.6875C13.0391 15.6875 14.2422 15.1406 15.0625 14.1562C15.3086 13.8828 15.2812 13.4453 14.9805 13.2266C14.707 13.0078 14.2969 13.0352 14.0781 13.3086Z"
                fill={fill}
            />
        </svg>
    );
};
