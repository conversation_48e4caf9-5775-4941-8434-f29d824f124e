import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MoveRightIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.916 18.399L18.3 12.966C18.3879 12.8855 18.458 12.7876 18.506 12.6786C18.5541 12.5695 18.5788 12.4516 18.5788 12.3325C18.5788 12.2133 18.5541 12.0955 18.506 11.9864C18.458 11.8773 18.3879 11.7795 18.3 11.699L12.917 6.27098C12.7567 6.11442 12.5463 6.01953 12.3229 6.00306C12.0995 5.98659 11.8774 6.04961 11.696 6.18098C11.5869 6.25418 11.4961 6.35141 11.4305 6.4652C11.3649 6.57899 11.3263 6.70631 11.3176 6.83737C11.3089 6.96843 11.3304 7.09973 11.3804 7.22119C11.4304 7.34265 11.5075 7.45102 11.606 7.53798L15.496 11.428H5.89998C5.6601 11.428 5.43003 11.5233 5.26041 11.6929C5.09078 11.8625 4.99548 12.0926 4.99548 12.3325C4.99548 12.5724 5.09078 12.8024 5.26041 12.9721C5.43003 13.1417 5.6601 13.237 5.89998 13.237H15.49L11.6 17.127C11.5017 17.214 11.4247 17.3224 11.3748 17.4439C11.325 17.5653 11.3035 17.6966 11.3122 17.8275C11.3209 17.9585 11.3595 18.0858 11.425 18.1996C11.4904 18.3134 11.5811 18.4107 11.69 18.484C11.8713 18.6173 12.0941 18.682 12.3186 18.6664C12.5431 18.6509 12.7548 18.5561 12.916 18.399Z"
                fill={fill}
            />
        </svg>
    );
};
