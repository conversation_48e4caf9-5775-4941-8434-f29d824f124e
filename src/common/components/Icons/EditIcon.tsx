import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const EditIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
    style,
}: IconProps) => {
    return (
        <svg
            width={size}
            className={className}
            style={{ minWidth: size, ...(style ?? {}) }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.3281 7.38184L8.39307 12.3168L10.8601 14.7818L15.7931 9.84884"
                fill={fill}
            />
            <path
                d="M7.57197 13.1479L10.039 15.6149L7.47197 16.3479C7.38747 16.3587 7.30162 16.35 7.22101 16.3224C7.14041 16.2949 7.06718 16.2492 7.00694 16.189C6.9467 16.1287 6.90106 16.0555 6.8735 15.9749C6.84595 15.8943 6.83722 15.8085 6.84797 15.7239L7.57197 13.1479Z"
                fill={fill}
            />
            <path
                d="M16.6269 9.03089C16.8038 8.87769 16.9456 8.68825 17.0428 8.47543C17.14 8.2626 17.1903 8.03136 17.1903 7.79739C17.1903 7.56342 17.14 7.33218 17.0428 7.11935C16.9456 6.90653 16.8038 6.71709 16.6269 6.56389C16.4737 6.38705 16.2843 6.24521 16.0714 6.14801C15.8586 6.0508 15.6274 6.00049 15.3934 6.00049C15.1594 6.00049 14.9282 6.0508 14.7154 6.14801C14.5025 6.24521 14.3131 6.38705 14.1599 6.56389L16.6269 9.03089Z"
                fill={fill}
            />
            <path
                d="M19.2281 19.108H6.14907C6.12925 19.1093 6.10937 19.1064 6.09077 19.0995C6.07216 19.0925 6.05525 19.0817 6.04116 19.0677C6.02706 19.0537 6.01611 19.0368 6.00903 19.0183C6.00194 18.9997 5.99889 18.9799 6.00007 18.96V18.098C5.99874 18.0781 6.00168 18.0582 6.0087 18.0395C6.01572 18.0208 6.02665 18.0038 6.04076 17.9897C6.05487 17.9756 6.07183 17.9647 6.09051 17.9577C6.10919 17.9507 6.12916 17.9477 6.14907 17.949H19.2291C19.249 17.9477 19.2689 17.9507 19.2876 17.9577C19.3063 17.9647 19.3233 17.9756 19.3374 17.9897C19.3515 18.0038 19.3624 18.0208 19.3694 18.0395C19.3764 18.0582 19.3794 18.0781 19.3781 18.098V18.96C19.3793 18.98 19.3762 18.9999 19.369 19.0185C19.3619 19.0372 19.3508 19.0541 19.3366 19.0681C19.3224 19.0821 19.3054 19.0929 19.2867 19.0998C19.268 19.1067 19.248 19.1095 19.2281 19.108Z"
                fill={fill}
            />
        </svg>
    );
};
