import { styled } from '@mui/material';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ProhibitionIcon: typeof Icon = ({
    fill = 'var(--cm2)',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <Svg
            className={className}
            minWidth={size}
            width={size}
            height={size}
            viewBox="0 0 10 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="5" cy="5.5" r="4.5" stroke={fill} />
            <line x1="1.49856" y1="8.30087" x2="8.35037" y2="2.54062" stroke={fill} />
        </Svg>
    );
};

const Svg = styled('svg')<{ minWidth: number }>(({ minWidth }) => ({
    minWidth: minWidth,
}));
