import { Colors } from 'common/styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ValetServiceIcon: typeof Icon = ({
    fill = Colors.CM1,
    size = IconSize.M,
    style,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size, ...style }}
            height={size}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10 8V10H8V8.5H6.5V7H5L3.87 5.87C3.595 5.955 3.305 6 3 6C2.20435 6 1.44129 5.68393 0.87868 5.12132C0.31607 4.55871 0 3.79565 0 3C0 2.20435 0.31607 1.44129 0.87868 0.87868C1.44129 0.31607 2.20435 0 3 0C3.79565 0 4.55871 0.31607 5.12132 0.87868C5.68393 1.44129 6 2.20435 6 3C6 3.305 5.955 3.595 5.87 3.87L10 8ZM2.5 1.5C2.23478 1.5 1.98043 1.60536 1.79289 1.79289C1.60536 1.98043 1.5 2.23478 1.5 2.5C1.5 2.76522 1.60536 3.01957 1.79289 3.20711C1.98043 3.39464 2.23478 3.5 2.5 3.5C2.76522 3.5 3.01957 3.39464 3.20711 3.20711C3.39464 3.01957 3.5 2.76522 3.5 2.5C3.5 2.23478 3.39464 1.98043 3.20711 1.79289C3.01957 1.60536 2.76522 1.5 2.5 1.5Z"
                fill={fill}
            />
        </svg>
    );
};
