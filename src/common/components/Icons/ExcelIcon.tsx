import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ExcelIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 39 42"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M2.21432 3.94759L26.0438 0.592478C26.1782 0.573457 26.3153 0.583162 26.4456 0.620936C26.576 0.658711 26.6966 0.723671 26.7992 0.811416C26.9019 0.89916 26.6819 0.620936 27.0406 1.1295C27.1262 1.37384 27.1262 2.17445 27.1262 2.30841V40.4819C27.1262 40.6157 27.0971 40.7479 27.0408 40.8696C26.9844 40.9913 26.9023 41.0997 26.7999 41.1874C26.6974 41.2751 26.5771 41.3401 26.447 41.378C26.3168 41.4159 26.18 41.4259 26.0457 41.4071L2.21242 38.052C1.76052 37.9886 1.347 37.7665 1.04785 37.4265C0.748696 37.0866 0.583989 36.6516 0.583984 36.2016V6.24585C0.583989 5.79578 0.585836 5.11213 1.04785 4.57309C1.53371 4.17755 1.76241 4.01105 2.21432 3.94759ZM4.37736 7.42047V34.5792L23.3347 37.2502V4.74946L4.37736 7.42047ZM29.022 34.0838H34.7092V7.91579H29.022V4.1775H36.6049C37.1077 4.1775 37.5899 4.37443 37.9454 4.72496C38.3009 5.07549 38.5007 5.55092 38.5007 6.04664V35.953C38.5007 36.4487 38.3009 36.9241 37.9454 37.2747C37.5899 37.6252 37.1077 37.8221 36.6049 37.8221H29.022V34.0838ZM16.1309 20.9998L21.439 28.4764H16.8892L13.8561 24.2035L10.8229 28.4764H6.2731L11.5812 20.9998L6.2731 13.5232H10.8229L13.8561 17.7961L16.8892 13.5232H21.439L16.1309 20.9998Z"
                fill={fill}
            />
        </svg>
    );
};
