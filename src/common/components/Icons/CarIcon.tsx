import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CarIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
    style,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size, ...style }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.775 11.2682L17.4542 10.4661L16.8708 9.03385C16.375 7.80208 15.1792 7 13.8375 7H10.1333C8.79167 7 7.59583 7.80208 7.1 9.03385L6.51667 10.4661L6.19583 11.2682C5.49583 11.526 5 12.1849 5 12.9583V14.3333C5 14.8203 5.175 15.2214 5.46667 15.5365V17.0833C5.46667 17.599 5.875 18 6.4 18H7.33333C7.82917 18 8.26667 17.599 8.26667 17.0833V16.1667H15.7333V17.0833C15.7333 17.599 16.1417 18 16.6667 18H17.6C18.0958 18 18.5333 17.599 18.5333 17.0833V15.5365C18.7958 15.2214 19 14.8203 19 14.3333V12.9583C19 12.1849 18.475 11.526 17.775 11.2682ZM8.82083 9.72135C9.05417 9.17708 9.55 8.83333 10.1333 8.83333H13.8375C14.4208 8.83333 14.9167 9.17708 15.15 9.72135L15.7333 11.125H8.26667L8.82083 9.72135ZM7.33333 14.3333C6.75 14.3333 6.4 13.9896 6.4 13.4167C6.4 12.8724 6.75 12.5 7.33333 12.5C7.8875 12.5 8.73333 13.3307 8.73333 13.875C8.73333 14.4193 7.8875 14.3333 7.33333 14.3333ZM16.6667 14.3333C16.0833 14.3333 15.2667 14.4193 15.2667 13.875C15.2667 13.3307 16.0833 12.5 16.6667 12.5C17.2208 12.5 17.6 12.8724 17.6 13.4167C17.6 13.9896 17.2208 14.3333 16.6667 14.3333Z"
                fill={fill}
            />
        </svg>
    );
};
