import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CmIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.7931 7.21716L17.1331 8.87616C16.9668 9.03863 16.7435 9.1296 16.5111 9.1296C16.2786 9.1296 16.0553 9.03863 15.8891 8.87616L15.1211 8.10816C14.9586 7.94188 14.8676 7.71864 14.8676 7.48616C14.8676 7.25369 14.9586 7.03044 15.1211 6.86416L16.7801 5.20516C16.1762 4.98192 15.521 4.93592 14.8919 5.07257C14.2627 5.20922 13.6857 5.52283 13.2288 5.97647C12.7719 6.43011 12.4541 7.00487 12.313 7.63305C12.1718 8.26123 12.2131 8.91667 12.4321 9.52216L5.42606 16.5272C5.291 16.6608 5.18378 16.8199 5.11061 16.9952C5.03743 17.1706 4.99976 17.3587 4.99976 17.5487C4.99976 17.7387 5.03743 17.9268 5.11061 18.1021C5.18378 18.2774 5.291 18.4365 5.42606 18.5702C5.55969 18.7052 5.71878 18.8124 5.89412 18.8856C6.06946 18.9588 6.25756 18.9965 6.44756 18.9965C6.63756 18.9965 6.82566 18.9588 7.001 18.8856C7.17634 18.8124 7.33543 18.7052 7.46906 18.5702L14.4591 11.5642C15.0651 11.7822 15.7206 11.8231 16.349 11.682C16.9774 11.541 17.5526 11.2239 18.0073 10.7678C18.4621 10.3117 18.7774 9.73557 18.9166 9.10676C19.0557 8.47795 19.0129 7.82251 18.7931 7.21716Z"
                fill={fill}
            />
        </svg>
    );
};
