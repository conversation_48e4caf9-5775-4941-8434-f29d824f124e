import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ProyectIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10 6C10 5.44772 9.55228 5 9 5H7H6C5.44772 5 5 5.44772 5 6V7V9C5 9.55228 5.44772 10 6 10C6.55228 10 7 9.55228 7 9L7 7.5C7 7.22386 7.22386 7 7.5 7L9 7C9.55228 7 10 6.55228 10 6Z"
                fill={fill}
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13 17C13 17.5523 13.4477 18 14 18L16 18L17 18C17.5523 18 18 17.5523 18 17L18 16L18 14C18 13.4477 17.5523 13 17 13C16.4477 13 16 13.4477 16 14L16 15.5C16 15.7761 15.7761 16 15.5 16L14 16C13.4477 16 13 16.4477 13 17Z"
                fill={fill}
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17 10C17.5523 10 18 9.55228 18 9L18 7L18 6C18 5.44772 17.5523 5 17 5L16 5L14 5C13.4477 5 13 5.44772 13 6C13 6.55228 13.4477 7 14 7L15.5 7C15.7761 7 16 7.22386 16 7.5L16 9C16 9.55228 16.4477 10 17 10Z"
                fill={fill}
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6 13C5.44772 13 5 13.4477 5 14L5 16L5 17C5 17.5523 5.44772 18 6 18L7 18L9 18C9.55228 18 10 17.5523 10 17C10 16.4477 9.55228 16 9 16L7.5 16C7.22386 16 7 15.7761 7 15.5L7 14C7 13.4477 6.55229 13 6 13Z"
                fill={fill}
            />
        </svg>
    );
};
