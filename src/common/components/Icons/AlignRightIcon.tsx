import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AlignRightIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.97302 9.25999C4.97302 9.45042 5.04867 9.63304 5.18332 9.76769C5.31797 9.90235 5.5006 9.97799 5.69102 9.97799H18.619C18.8094 9.97799 18.9921 9.90235 19.1267 9.76769C19.2614 9.63304 19.337 9.45042 19.337 9.25999C19.337 9.06957 19.2614 8.88694 19.1267 8.75229C18.9921 8.61764 18.8094 8.54199 18.619 8.54199H5.69102C5.5006 8.54199 5.31797 8.61764 5.18332 8.75229C5.04867 8.88694 4.97302 9.06957 4.97302 9.25999Z"
                fill={fill}
            />
            <path
                d="M9.28198 6.38695C9.28198 6.57737 9.35763 6.76 9.49228 6.89465C9.62693 7.0293 9.80956 7.10495 9.99998 7.10495H18.618C18.8084 7.10495 18.991 7.0293 19.1257 6.89465C19.2603 6.76 19.336 6.57737 19.336 6.38695C19.336 6.19652 19.2603 6.01389 19.1257 5.87924C18.991 5.74459 18.8084 5.66895 18.618 5.66895H9.99998C9.80956 5.66895 9.62693 5.74459 9.49228 5.87924C9.35763 6.01389 9.28198 6.19652 9.28198 6.38695Z"
                fill={fill}
            />
            <path
                d="M4.97302 15.0051C4.97302 15.1955 5.04867 15.3782 5.18332 15.5128C5.31797 15.6475 5.5006 15.7231 5.69102 15.7231H18.619C18.8094 15.7231 18.9921 15.6475 19.1267 15.5128C19.2614 15.3782 19.337 15.1955 19.337 15.0051C19.337 14.8147 19.2614 14.6321 19.1267 14.4974C18.9921 14.3628 18.8094 14.2871 18.619 14.2871H5.69102C5.5006 14.2871 5.31797 14.3628 5.18332 14.4974C5.04867 14.6321 4.97302 14.8147 4.97302 15.0051Z"
                fill={fill}
            />
            <path
                d="M9.28198 12.133C9.28198 12.3235 9.35763 12.5061 9.49228 12.6407C9.62693 12.7754 9.80956 12.851 9.99998 12.851H18.618C18.8084 12.851 18.991 12.7754 19.1257 12.6407C19.2603 12.5061 19.336 12.3235 19.336 12.133C19.336 11.9426 19.2603 11.76 19.1257 11.6253C18.991 11.4907 18.8084 11.415 18.618 11.415H9.99998C9.80956 11.415 9.62693 11.4907 9.49228 11.6253C9.35763 11.76 9.28198 11.9426 9.28198 12.133Z"
                fill={fill}
            />
            <path
                d="M9.28198 17.8782C9.28198 18.0686 9.35763 18.2512 9.49228 18.3859C9.62693 18.5205 9.80956 18.5962 9.99998 18.5962H18.618C18.8084 18.5962 18.991 18.5205 19.1257 18.3859C19.2603 18.2512 19.336 18.0686 19.336 17.8782C19.336 17.7839 19.3174 17.6905 19.2813 17.6034C19.2452 17.5163 19.1924 17.4371 19.1257 17.3705C19.059 17.3038 18.9799 17.2509 18.8927 17.2148C18.8056 17.1787 18.7123 17.1602 18.618 17.1602H9.99998C9.90569 17.1602 9.81233 17.1787 9.72522 17.2148C9.6381 17.2509 9.55895 17.3038 9.49228 17.3705C9.42561 17.4371 9.37272 17.5163 9.33664 17.6034C9.30055 17.6905 9.28198 17.7839 9.28198 17.8782Z"
                fill={fill}
            />
        </svg>
    );
};
