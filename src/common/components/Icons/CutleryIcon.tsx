import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CutleryIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5.90099 4.06601L11.063 10.121L8.88999 12.671L5.89999 9.16601C5.30908 8.4489 4.99035 7.54617 4.99999 6.61701C4.98988 5.68717 5.30964 4.78366 5.90099 4.06601ZM13.622 11.582L12.966 12.352L18.4 18.726L17.313 20.001L11.879 13.628L6.44399 20L5.35699 18.726L12.535 10.308C12.3455 9.49042 12.36 8.63877 12.5771 7.82807C12.7942 7.01738 13.2073 6.27249 13.78 5.65901C15.28 3.90001 17.349 3.47101 18.4 4.70401C19.451 5.93701 19.086 8.36201 17.585 10.121C16.35 11.566 14.742 12.113 13.622 11.582Z"
                fill={fill}
            />
        </svg>
    );
};
