import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AftersalesCrmMenuIcon: typeof Icon = ({ size = IconSize.M, className }: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10 12C11.875 12 13.4286 10.4414 13.4286 8.5C13.4286 6.58594 11.875 5 10 5C8.09821 5 6.57143 6.58594 6.57143 8.5C6.57143 10.4414 8.09821 12 10 12ZM12.3839 12.875H11.9286C11.3393 13.1758 10.6964 13.3125 10 13.3125C9.30357 13.3125 8.63393 13.1758 8.04464 12.875H7.58929C5.60714 12.875 4 14.543 4 16.5664V17.6875C4 18.4258 4.5625 19 5.28571 19H14.7143C15.4107 19 16 18.4258 16 17.6875V16.5664C16 14.543 14.3661 12.875 12.3839 12.875Z"
                fill="#ACB7C0"
            />
            <path
                d="M17.5027 4C15.5628 4 14.0054 5.46875 14.0054 7.25C14.0054 8.03125 14.2922 8.73438 14.7841 9.29688C14.6065 10.0938 14.0327 10.7812 14.0327 10.7969C14.0054 10.8281 13.9917 10.8906 14.0054 10.9375C14.0327 10.9844 14.06 11 14.1147 11C15.0163 11 15.6994 10.5156 16.0272 10.2031C16.4781 10.3906 16.9699 10.5 17.5027 10.5C19.4289 10.5 21 9.04688 21 7.25C21 5.46875 19.4289 4 17.5027 4Z"
                fill="#ACB7C0"
            />
        </svg>
    );
};
