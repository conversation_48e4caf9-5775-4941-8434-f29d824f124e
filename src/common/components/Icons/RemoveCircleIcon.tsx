import SvgIcon from '@mui/material/SvgIcon';
import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

const RemoveCircleIcon: typeof Icon = ({
    fill = Colors.CM1,
    size = IconSize.M,
    className = '',
    style,
}: IconProps) => {
    return (
        <SvgIcon
            style={{ width: size, height: size, ...style }}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12 4.25C7.71875 4.25 4.25 7.71875 4.25 12C4.25 16.2812 7.71875 19.75 12 19.75C16.2812 19.75 19.75 16.2812 19.75 12C19.75 7.71875 16.2812 4.25 12 4.25ZM7.875 13.25C7.65625 13.25 7.5 13.0938 7.5 12.875V11.125C7.5 10.9375 7.65625 10.75 7.875 10.75H16.125C16.3125 10.75 16.5 10.9375 16.5 11.125V12.875C16.5 13.0938 16.3125 13.25 16.125 13.25H7.875Z"
                fill={fill}
            />
        </SvgIcon>
    );
};

export default RemoveCircleIcon;
