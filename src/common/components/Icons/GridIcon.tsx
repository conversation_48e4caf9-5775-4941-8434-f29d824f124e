import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const GridIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.70001 8.408H7.70501C7.89066 8.408 8.06871 8.33425 8.19998 8.20297C8.33126 8.0717 8.40501 7.89365 8.40501 7.708V4.7C8.40501 4.51435 8.33126 4.3363 8.19998 4.20503C8.06871 4.07375 7.89066 4 7.70501 4H4.70001C4.51435 4 4.33631 4.07375 4.20503 4.20503C4.07376 4.3363 4.00001 4.51435 4.00001 4.7V7.705C3.99961 7.79718 4.01743 7.88853 4.05243 7.9738C4.08743 8.05907 4.13893 8.1366 4.20397 8.20191C4.26901 8.26723 4.34631 8.31906 4.43143 8.35443C4.51656 8.3898 4.60783 8.408 4.70001 8.408Z"
                fill={fill}
            />
            <path
                d="M10.21 8.408H13.215C13.4007 8.408 13.5787 8.33425 13.71 8.20297C13.8413 8.0717 13.915 7.89365 13.915 7.708V4.7C13.915 4.51435 13.8413 4.3363 13.71 4.20503C13.5787 4.07375 13.4007 4 13.215 4H10.21C10.0244 4 9.84632 4.07375 9.71504 4.20503C9.58377 4.3363 9.51002 4.51435 9.51002 4.7V7.705C9.50962 7.79718 9.52744 7.88853 9.56244 7.9738C9.59744 8.05907 9.64894 8.1366 9.71398 8.20191C9.77902 8.26723 9.85632 8.31906 9.94144 8.35443C10.0266 8.3898 10.1178 8.408 10.21 8.408Z"
                fill={fill}
            />
            <path
                d="M15.72 8.408H18.725C18.9107 8.408 19.0887 8.33425 19.22 8.20297C19.3513 8.0717 19.425 7.89365 19.425 7.708V4.7C19.425 4.51435 19.3513 4.3363 19.22 4.20503C19.0887 4.07375 18.9107 4 18.725 4H15.72C15.5344 4 15.3563 4.07375 15.2251 4.20503C15.0938 4.3363 15.02 4.51435 15.02 4.7V7.705C15.0196 7.79718 15.0374 7.88853 15.0724 7.9738C15.1074 8.05907 15.1589 8.1366 15.224 8.20191C15.289 8.26723 15.3663 8.31906 15.4515 8.35443C15.5366 8.3898 15.6278 8.408 15.72 8.408Z"
                fill={fill}
            />
            <path
                d="M4.70001 13.9178H7.70501C7.89066 13.9178 8.06871 13.844 8.19998 13.7127C8.33126 13.5815 8.40501 13.4034 8.40501 13.2178V10.2098C8.40501 10.0241 8.33126 9.84607 8.19998 9.71479C8.06871 9.58351 7.89066 9.50977 7.70501 9.50977H4.70001C4.51435 9.50977 4.33631 9.58351 4.20503 9.71479C4.07376 9.84607 4.00001 10.0241 4.00001 10.2098V13.2148C3.99961 13.3069 4.01743 13.3983 4.05243 13.4836C4.08743 13.5688 4.13893 13.6464 4.20397 13.7117C4.26901 13.777 4.34631 13.8288 4.43143 13.8642C4.51656 13.8996 4.60783 13.9178 4.70001 13.9178Z"
                fill={fill}
            />
            <path
                d="M10.21 13.9178H13.215C13.4007 13.9178 13.5787 13.844 13.71 13.7127C13.8413 13.5815 13.915 13.4034 13.915 13.2178V10.2098C13.915 10.0241 13.8413 9.84607 13.71 9.71479C13.5787 9.58351 13.4007 9.50977 13.215 9.50977H10.21C10.0244 9.50977 9.84632 9.58351 9.71504 9.71479C9.58377 9.84607 9.51002 10.0241 9.51002 10.2098V13.2148C9.50962 13.3069 9.52744 13.3983 9.56244 13.4836C9.59744 13.5688 9.64894 13.6464 9.71398 13.7117C9.77902 13.777 9.85632 13.8288 9.94144 13.8642C10.0266 13.8996 10.1178 13.9178 10.21 13.9178Z"
                fill={fill}
            />
            <path
                d="M15.72 13.9178H18.725C18.9107 13.9178 19.0887 13.844 19.22 13.7127C19.3513 13.5815 19.425 13.4034 19.425 13.2178V10.2098C19.425 10.0241 19.3513 9.84607 19.22 9.71479C19.0887 9.58351 18.9107 9.50977 18.725 9.50977H15.72C15.5344 9.50977 15.3563 9.58351 15.2251 9.71479C15.0938 9.84607 15.02 10.0241 15.02 10.2098V13.2148C15.0196 13.3069 15.0374 13.3983 15.0724 13.4836C15.1074 13.5688 15.1589 13.6464 15.224 13.7117C15.289 13.777 15.3663 13.8288 15.4515 13.8642C15.5366 13.8996 15.6278 13.9178 15.72 13.9178Z"
                fill={fill}
            />
            <path
                d="M4.70001 19.428H7.70501C7.89066 19.428 8.06871 19.3543 8.19998 19.223C8.33126 19.0917 8.40501 18.9137 8.40501 18.728V15.72C8.40501 15.5344 8.33126 15.3563 8.19998 15.225C8.06871 15.0938 7.89066 15.02 7.70501 15.02H4.70001C4.51435 15.02 4.33631 15.0938 4.20503 15.225C4.07376 15.3563 4.00001 15.5344 4.00001 15.72V18.725C3.99961 18.8172 4.01743 18.9085 4.05243 18.9938C4.08743 19.0791 4.13893 19.1566 4.20397 19.2219C4.26901 19.2873 4.34631 19.3391 4.43143 19.3745C4.51656 19.4098 4.60783 19.428 4.70001 19.428Z"
                fill={fill}
            />
            <path
                d="M10.21 19.428H13.215C13.4007 19.428 13.5787 19.3543 13.71 19.223C13.8413 19.0917 13.915 18.9137 13.915 18.728V15.72C13.915 15.5344 13.8413 15.3563 13.71 15.225C13.5787 15.0938 13.4007 15.02 13.215 15.02H10.21C10.0244 15.02 9.84632 15.0938 9.71504 15.225C9.58377 15.3563 9.51002 15.5344 9.51002 15.72V18.725C9.50962 18.8172 9.52744 18.9085 9.56244 18.9938C9.59744 19.0791 9.64894 19.1566 9.71398 19.2219C9.77902 19.2873 9.85632 19.3391 9.94144 19.3745C10.0266 19.4098 10.1178 19.428 10.21 19.428Z"
                fill={fill}
            />
            <path
                d="M15.72 19.428H18.725C18.9107 19.428 19.0887 19.3543 19.22 19.223C19.3513 19.0917 19.425 18.9137 19.425 18.728V15.72C19.425 15.5344 19.3513 15.3563 19.22 15.225C19.0887 15.0938 18.9107 15.02 18.725 15.02H15.72C15.5344 15.02 15.3563 15.0938 15.2251 15.225C15.0938 15.3563 15.02 15.5344 15.02 15.72V18.725C15.0196 18.8172 15.0374 18.9085 15.0724 18.9938C15.1074 19.0791 15.1589 19.1566 15.224 19.2219C15.289 19.2873 15.3663 19.3391 15.4515 19.3745C15.5366 19.4098 15.6278 19.428 15.72 19.428Z"
                fill={fill}
            />
        </svg>
    );
};
