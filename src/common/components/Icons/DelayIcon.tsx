import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DelayIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6.66675 6.34061V4.69562C6.66675 4.31143 6.34837 3.99997 5.95564 3.99997C5.5629 3.99997 5.24453 4.31143 5.24453 4.69562V7.82605C5.24453 7.85565 5.24642 7.88482 5.25009 7.91344C5.24648 7.94182 5.24463 7.97072 5.24463 8.00005C5.24463 8.38425 5.563 8.6957 5.95574 8.6957L9.15573 8.6957C9.54847 8.6957 9.86684 8.38425 9.86684 8.00005C9.86684 7.61585 9.54847 7.3044 9.15573 7.3044H7.69998C8.85345 6.32864 10.3556 5.73919 12 5.73919C15.6328 5.73919 18.5778 8.62013 18.5778 12.174C18.5778 15.7254 15.6368 18.6049 12.0073 18.6087L12.0001 18.6087C11.5599 18.6087 11.1304 18.5665 10.7156 18.4862C10.3303 18.4116 9.95619 18.6567 9.87997 19.0336C9.80375 19.4105 10.0543 19.7765 10.4396 19.851C10.9421 19.9483 11.4611 19.9994 11.9915 20L12 20C16.4183 20 20 16.4962 20 12.174C20 7.85174 16.4183 4.34789 12 4.34789C9.95161 4.34789 8.08159 5.10192 6.66675 6.34061ZM8.54117 18.4903C8.75966 18.1711 8.67221 17.739 8.34586 17.5253C7.62824 17.0553 7.01024 16.4507 6.52981 15.7487C6.31132 15.4294 5.86964 15.3439 5.54329 15.5576C5.21694 15.7714 5.1295 16.2034 5.34799 16.5227C5.93185 17.3758 6.68255 18.1102 7.55466 18.6814C7.88101 18.8951 8.32268 18.8096 8.54117 18.4903ZM4.98794 14.2479C5.37321 14.1734 5.62374 13.8074 5.54753 13.4305C5.46545 13.0246 5.42231 12.6046 5.42231 12.1739C5.42231 11.7897 5.10393 11.4783 4.7112 11.4783C4.31846 11.4783 4.00009 11.7897 4.00009 12.1739C4.00009 12.6957 4.05238 13.2062 4.15235 13.7005C4.22856 14.0774 4.60267 14.3225 4.98794 14.2479ZM11.1998 8.4348C11.1998 8.0506 11.5182 7.73915 11.9109 7.73915C12.3036 7.73915 12.622 8.0506 12.622 8.4348V11.7119L14.7248 13.769C15.0025 14.0407 15.0025 14.4811 14.7248 14.7528C14.4471 15.0245 13.9969 15.0245 13.7192 14.7528L11.4601 12.5429C11.2934 12.3798 11.1998 12.1586 11.1998 11.928V8.4348Z"
                fill={fill}
            />
        </svg>
    );
};
