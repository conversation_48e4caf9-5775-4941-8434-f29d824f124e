import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CalendarCheckmarkIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M 18.625 9 C 18.8125 9 19 8.8438 19 8.625 V 7.5 C 19 6.6875 18.3125 6 17.5 6 H 16 V 4.375 C 16 4.1875 15.8125 4 15.625 4 H 14.375 C 14.1563 4 14 4.1875 14 4.375 V 6 H 10 V 4.375 C 10 4.1875 9.8125 4 9.625 4 H 8.375 C 8.1563 4 8 4.1875 8 4.375 V 6 H 6.5 C 5.6563 6 5 6.6875 5 7.5 V 8.625 C 5 8.8438 5.1563 9 5.375 9 H 18.625 Z M 5.375 10 C 5.1563 10 5 10.1875 5 10.375 V 18.5 C 5 19.3438 5.6563 20 6.5 20 H 17.5 C 18.3125 20 19 19.3438 19 18.5 V 10.375 C 19 10.1875 18.8125 10 18.625 10 H 5.375 Z M 15.7813 13 C 15.9063 13.1563 15.9063 13.4063 15.7813 13.5313 L 11.3125 17.9688 C 11.1563 18.125 10.9063 18.125 10.7813 17.9688 L 8.1875 15.375 C 8.0625 15.2188 8.0625 15 8.1875 14.8438 L 9.0938 13.9688 C 9.2188 13.8125 9.4688 13.8125 9.625 13.9688 L 11.0625 15.4063 L 14.375 12.125 C 14.5 11.9688 14.75 11.9688 14.9063 12.125 L 15.7813 13 Z"
                fill={fill}
            />
        </svg>
    );
};
