import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DeleteIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
    style,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size, ...(style ?? {}) }}
            height={size}
            className={className}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.174 5.97102C18.3236 5.97724 18.4649 6.04101 18.5685 6.149C18.6721 6.257 18.73 6.40087 18.73 6.55052C18.73 6.70018 18.6721 6.84404 18.5685 6.95204C18.4649 7.06004 18.3236 7.12381 18.174 7.13002H18.166C18.135 7.13025 18.1052 7.14243 18.0828 7.16402C18.0605 7.18562 18.0473 7.21498 18.046 7.24602L17.629 18.515C17.6123 18.9134 17.4428 19.2899 17.1556 19.5665C16.8684 19.8431 16.4857 19.9983 16.087 20H8.66703C8.26869 19.9981 7.88638 19.8428 7.59945 19.5665C7.31252 19.2902 7.14299 18.914 7.12603 18.516L6.70903 7.24702C6.70775 7.21598 6.69457 7.18662 6.67223 7.16502C6.64989 7.14343 6.6201 7.13125 6.58903 7.13102H6.58003C6.5019 7.13427 6.42393 7.12169 6.35079 7.09403C6.27765 7.06637 6.21086 7.0242 6.15443 6.97007C6.09801 6.91594 6.05311 6.85096 6.02244 6.77903C5.99176 6.7071 5.97595 6.62972 5.97595 6.55152C5.97595 6.47333 5.99176 6.39594 6.02244 6.32402C6.05311 6.25209 6.09801 6.18711 6.15443 6.13298C6.21086 6.07884 6.27765 6.03668 6.35079 6.00902C6.42393 5.98136 6.5019 5.96878 6.58003 5.97202H9.40603C9.43887 5.9705 9.47048 5.95909 9.49671 5.93928C9.52295 5.91947 9.54258 5.89219 9.55303 5.86102L9.74203 5.19902C9.84882 4.85836 10.0594 4.55958 10.3443 4.34443C10.6292 4.12928 10.9742 4.00852 11.331 3.99902H13.422C13.7789 4.00852 14.1239 4.12928 14.4088 4.34443C14.6937 4.55958 14.9042 4.85836 15.011 5.19902L15.2 5.86102C15.2102 5.89234 15.2298 5.91978 15.2561 5.93962C15.2824 5.95947 15.3141 5.97078 15.347 5.97202L18.174 5.97102ZM11.913 9.21702V17.1C11.913 17.2231 11.9619 17.3411 12.0489 17.4281C12.136 17.5151 12.254 17.564 12.377 17.564C12.5001 17.564 12.6181 17.5151 12.7051 17.4281C12.7921 17.3411 12.841 17.2231 12.841 17.1V9.21702C12.841 9.09396 12.7921 8.97594 12.7051 8.88893C12.6181 8.80191 12.5001 8.75302 12.377 8.75302C12.254 8.75302 12.136 8.80191 12.0489 8.88893C11.9619 8.97594 11.913 9.09396 11.913 9.21702ZM9.36203 9.23102L9.59403 17.115C9.60187 17.2349 9.65591 17.3471 9.74479 17.428C9.83367 17.5088 9.95045 17.5521 10.0706 17.5486C10.1907 17.5451 10.3047 17.4951 10.3887 17.4092C10.4728 17.3233 10.5202 17.2082 10.521 17.088L10.29 9.20002C10.2905 9.13768 10.2783 9.07589 10.2544 9.01833C10.2304 8.96078 10.1951 8.90865 10.1505 8.86506C10.1059 8.82146 10.053 8.78729 9.99497 8.76459C9.9369 8.7419 9.87485 8.73113 9.81253 8.73295C9.75021 8.73476 9.6889 8.74912 9.63225 8.77516C9.5756 8.8012 9.52478 8.83839 9.48283 8.8845C9.44087 8.93062 9.40864 8.98472 9.38806 9.04357C9.36747 9.10242 9.35896 9.16481 9.36303 9.22702L9.36203 9.23102ZM14.462 9.20402L14.23 17.088C14.2309 17.2082 14.2783 17.3233 14.3623 17.4092C14.4463 17.4951 14.5604 17.5451 14.6805 17.5486C14.8006 17.5521 14.9174 17.5088 15.0063 17.428C15.0951 17.3471 15.1492 17.2349 15.157 17.115L15.389 9.23102C15.3882 9.11087 15.3408 8.99573 15.2567 8.90984C15.1727 8.82394 15.0587 8.77399 14.9386 8.77049C14.8184 8.76699 14.7017 8.81022 14.6128 8.89108C14.5239 8.97194 14.4699 9.08413 14.462 9.20402ZM10.843 5.97102H13.908C13.9219 5.97258 13.936 5.97055 13.9489 5.96513C13.9618 5.9597 13.9731 5.95106 13.9817 5.94004C13.9904 5.92901 13.996 5.91596 13.9982 5.90213C14.0003 5.8883 13.9989 5.87415 13.994 5.86102L13.894 5.51702C13.8563 5.41889 13.7917 5.33332 13.7078 5.27003C13.6238 5.20675 13.5238 5.16829 13.419 5.15902H11.331C11.2263 5.16829 11.1263 5.20675 11.0423 5.27003C10.9583 5.33332 10.8938 5.41889 10.856 5.51702L10.756 5.86102C10.751 5.87441 10.7495 5.88888 10.7518 5.903C10.7541 5.91712 10.76 5.93041 10.769 5.94153C10.778 5.95265 10.7898 5.96122 10.8031 5.96639C10.8164 5.97157 10.8309 5.97316 10.845 5.97102H10.843Z"
                fill={fill}
            />
        </svg>
    );
};
