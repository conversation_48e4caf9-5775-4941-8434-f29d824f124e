import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const OneTimeIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M 11.8261 5.3913 C 8.2723 5.3913 5.3913 8.2722 5.3913 11.8261 C 5.3913 15.3799 8.2723 18.2609 11.8261 18.2609 C 12.2103 18.2609 12.5218 18.5723 12.5218 18.9565 C 12.5218 19.3407 12.2103 19.6522 11.8261 19.6522 C 7.5039 19.6522 4 16.1483 4 11.8261 C 4 7.5039 7.5039 4 11.8261 4 C 16.1483 4 19.6522 7.5039 19.6522 11.8261 C 19.6522 12.2103 19.3407 12.5217 18.9565 12.5217 C 18.5724 12.5217 18.2609 12.2103 18.2609 11.8261 C 18.2609 8.2722 15.3799 5.3913 11.8261 5.3913 Z M 20 16.3478 C 20 14.345 18.3506 12.6957 16.3478 12.6957 C 14.3303 12.6957 12.6956 14.345 12.6956 16.3478 C 12.6956 18.3654 14.3303 20 16.3478 20 C 18.3506 20 20 18.3654 20 16.3478 Z M 15.9207 18.2917 C 15.8324 18.3801 15.6704 18.3801 15.582 18.2917 L 14.0505 16.7602 C 13.9621 16.6718 13.9621 16.5098 14.0505 16.4215 L 14.3892 16.0975 C 14.4775 15.9944 14.6248 15.9944 14.7132 16.0975 L 15.7587 17.1283 L 17.9677 14.9194 C 18.0561 14.8163 18.2033 14.8163 18.2917 14.9194 L 18.6304 15.2434 C 18.7188 15.3317 18.7188 15.4937 18.6304 15.5821 L 15.9207 18.2917 Z M 12.6088 8.087 C 12.6088 7.7028 12.2973 7.3913 11.9131 7.3913 C 11.5289 7.3913 11.2175 7.7028 11.2175 8.087 V 11.364 L 9.1604 13.4211 C 8.8887 13.6928 8.8887 14.1333 9.1604 14.4049 C 9.432 14.6766 9.8725 14.6766 10.1441 14.4049 L 12.3541 12.195 C 12.5172 12.0319 12.6088 11.8108 12.6088 11.5801 V 8.087 Z"
                fill={fill}
            />
        </svg>
    );
};
