import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const TrafficLightIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M8.24933 6.49925V5.75339C8.24933 5.55358 8.3287 5.36195 8.46998 5.22066C8.61127 5.07937 8.80289 5 9.00269 5H14.9973C15.1971 5 15.3887 5.07937 15.53 5.22066C15.6713 5.36195 15.7507 5.55358 15.7507 5.75339V6.50678H18C17.9743 7.13326 17.7384 7.73283 17.3304 8.20886C16.9223 8.68489 16.3658 9.00963 15.7507 9.13073V10.9981H18C17.9743 11.6245 17.7384 12.2241 17.3304 12.7001C16.9223 13.1762 16.3658 13.5009 15.7507 13.622V15.498H18C17.9743 16.1244 17.7384 16.724 17.3304 17.2C16.9223 17.6761 16.3658 18.0008 15.7507 18.1219V19.2466C15.7507 19.4464 15.6713 19.638 15.53 19.7793C15.3887 19.9206 15.1971 20 14.9973 20H8.99946C8.79966 20 8.60804 19.9206 8.46675 19.7793C8.32547 19.638 8.2461 19.4464 8.2461 19.2466V18.1219C7.63152 18.0002 7.07578 17.6751 6.66836 17.1992C6.26093 16.7232 6.02551 16.124 6 15.498H8.24933V13.6231C7.63398 13.5019 7.07738 13.177 6.66929 12.7008C6.26121 12.2246 6.02544 11.6247 6 10.9981H8.24933V9.12427C7.63398 9.00313 7.07738 8.67823 6.66929 8.20198C6.26121 7.72574 6.02544 7.12591 6 6.49925H8.24933ZM11.9978 18.4964C12.3447 18.4964 12.6808 18.3762 12.9489 18.1561C13.217 17.9361 13.4006 17.6299 13.4682 17.2897C13.5359 16.9495 13.4835 16.5964 13.32 16.2905C13.1565 15.9846 12.892 15.7448 12.5716 15.6121C12.2511 15.4793 11.8946 15.4618 11.5627 15.5625C11.2307 15.6632 10.944 15.8759 10.7513 16.1643C10.5586 16.4527 10.4719 16.799 10.5059 17.1442C10.5399 17.4893 10.6925 17.8121 10.9378 18.0573C11.077 18.1965 11.2422 18.307 11.4241 18.3823C11.606 18.4577 11.801 18.4964 11.9978 18.4964ZM11.9978 13.9976C12.3447 13.9976 12.6808 13.8774 12.9489 13.6573C13.217 13.4373 13.4006 13.1311 13.4682 12.7909C13.5359 12.4507 13.4835 12.0975 13.32 11.7916C13.1565 11.4857 12.892 11.246 12.5716 11.1133C12.2511 10.9805 11.8946 10.963 11.5627 11.0637C11.2307 11.1644 10.944 11.377 10.7513 11.6654C10.5586 11.9539 10.4719 12.3001 10.5059 12.6453C10.5399 12.9905 10.6925 13.3132 10.9378 13.5585C11.077 13.6977 11.2422 13.8082 11.4241 13.8835C11.606 13.9589 11.801 13.9976 11.9978 13.9976ZM11.9978 9.49882C12.3447 9.49882 12.6808 9.37855 12.9489 9.1585C13.217 8.93846 13.4006 8.63225 13.4682 8.29206C13.5359 7.95186 13.4835 7.59873 13.32 7.29283C13.1565 6.98693 12.892 6.74718 12.5716 6.61445C12.2511 6.48171 11.8946 6.46419 11.5627 6.56488C11.2307 6.66557 10.944 6.87823 10.7513 7.16663C10.5586 7.45504 10.4719 7.80133 10.5059 8.14652C10.5399 8.49171 10.6925 8.81443 10.9378 9.0597C11.077 9.19892 11.2422 9.30935 11.4241 9.38469C11.606 9.46004 11.801 9.49882 11.9978 9.49882Z"
                fill={fill}
            />
        </svg>
    );
};
