import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CashIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.339 12.561C15.339 13.2335 15.1396 13.8908 14.766 14.4499C14.3924 15.0091 13.8614 15.4449 13.2401 15.7022C12.6189 15.9595 11.9352 16.0269 11.2757 15.8957C10.6162 15.7645 10.0103 15.4407 9.53484 14.9652C9.05935 14.4897 8.73553 13.8839 8.60434 13.2243C8.47315 12.5648 8.54048 11.8812 8.79782 11.2599C9.05515 10.6386 9.49094 10.1076 10.0501 9.73401C10.6092 9.36042 11.2666 9.16101 11.939 9.16101C12.8402 9.16286 13.7039 9.52166 14.3411 10.1589C14.9784 10.7961 15.3372 11.6598 15.339 12.561ZM11.639 11.667C11.6344 11.6128 11.6409 11.5583 11.6581 11.5067C11.6753 11.4551 11.7028 11.4076 11.739 11.367C11.7709 11.3256 11.8125 11.2927 11.8601 11.2711C11.9077 11.2494 11.9598 11.2398 12.012 11.243C12.0704 11.2411 12.1284 11.2538 12.1807 11.2798C12.2331 11.3058 12.2782 11.3443 12.312 11.392C12.3782 11.4866 12.4132 11.5995 12.412 11.715C12.412 11.789 12.486 11.815 12.536 11.815H13.182C13.1986 11.816 13.2151 11.8135 13.2306 11.8077C13.2462 11.8018 13.2602 11.7927 13.272 11.781C13.2837 11.7692 13.2928 11.7552 13.2987 11.7396C13.3045 11.7241 13.307 11.7076 13.306 11.691C13.3005 11.4328 13.2038 11.1848 13.033 10.991C12.8597 10.7929 12.6228 10.6613 12.363 10.619V10.147C12.3614 10.121 12.3503 10.0965 12.3319 10.0781C12.3135 10.0597 12.289 10.0487 12.263 10.047H11.863C11.837 10.0487 11.8125 10.0597 11.7941 10.0781C11.7757 10.0965 11.7647 10.121 11.763 10.147V10.6C11.4966 10.635 11.2463 10.7473 11.043 10.923C10.8558 11.1245 10.7496 11.3882 10.745 11.6632C10.7404 11.9383 10.8376 12.2053 11.018 12.413C11.2904 12.6357 11.6021 12.8053 11.937 12.913C12.0699 12.9568 12.1896 13.0336 12.285 13.136C12.3231 13.18 12.3516 13.2314 12.3689 13.287C12.3861 13.3426 12.3916 13.4012 12.385 13.459C12.3933 13.5134 12.3887 13.5691 12.3716 13.6214C12.3544 13.6737 12.3252 13.7213 12.2862 13.7602C12.2473 13.7992 12.1997 13.8284 12.1474 13.8456C12.0951 13.8627 12.0394 13.8673 11.985 13.859C11.9161 13.8609 11.8476 13.8486 11.7836 13.823C11.7196 13.7974 11.6616 13.7589 11.613 13.71C11.5254 13.6048 11.4733 13.4746 11.464 13.338C11.464 13.264 11.39 13.238 11.34 13.238H10.7C10.6835 13.237 10.6669 13.2395 10.6514 13.2454C10.6359 13.2512 10.6218 13.2603 10.6101 13.2721C10.5983 13.2838 10.5892 13.2979 10.5834 13.3134C10.5775 13.3289 10.575 13.3455 10.576 13.362C10.5747 13.5056 10.6027 13.6479 10.6582 13.7803C10.7138 13.9126 10.7957 14.0323 10.899 14.132C11.1255 14.3362 11.4138 14.4587 11.718 14.48V14.9C11.7197 14.926 11.7307 14.9505 11.7491 14.9689C11.7675 14.9873 11.792 14.9984 11.818 15H12.218C12.244 14.9984 12.2685 14.9873 12.2869 14.9689C12.3053 14.9505 12.3164 14.926 12.318 14.9V14.453C12.5625 14.4179 12.7892 14.3045 12.964 14.13C13.0623 14.0329 13.1396 13.9167 13.1912 13.7886C13.2428 13.6604 13.2675 13.5231 13.264 13.385C13.2723 13.2464 13.2498 13.1076 13.1979 12.9787C13.146 12.8499 13.0661 12.7342 12.964 12.64C12.6932 12.4234 12.392 12.2478 12.07 12.119C11.9336 12.0676 11.8076 11.9921 11.698 11.896C11.665 11.891 11.64 11.792 11.64 11.668L11.639 11.667ZM21 8.11701V17.03C21.0005 17.1768 20.972 17.3223 20.9161 17.4581C20.8601 17.5938 20.7779 17.7172 20.674 17.821C20.5702 17.9249 20.4468 18.0071 20.3111 18.0631C20.1753 18.119 20.0298 18.1475 19.883 18.147H4.11701C3.97017 18.1475 3.82468 18.119 3.68893 18.0631C3.55317 18.0071 3.42982 17.9249 3.32599 17.821C3.22216 17.7172 3.1399 17.5938 3.08396 17.4581C3.02801 17.3223 2.99948 17.1768 3.00001 17.03V8.11701C2.99948 7.97017 3.02801 7.82469 3.08396 7.68893C3.1399 7.55317 3.22216 7.42982 3.32599 7.32599C3.42982 7.22216 3.55317 7.1399 3.68893 7.08396C3.82468 7.02801 3.97017 6.99948 4.11701 7.00001H19.883C20.0298 6.99948 20.1753 7.02801 20.3111 7.08396C20.4468 7.1399 20.5702 7.22216 20.674 7.32599C20.7779 7.42982 20.8601 7.55317 20.9161 7.68893C20.972 7.82469 21.0005 7.97017 21 8.11701ZM19.659 10.178C19.5696 10.1986 19.4777 10.207 19.386 10.203C19.1774 10.2033 18.9708 10.1623 18.7781 10.0825C18.5853 10.0027 18.4102 9.88565 18.2629 9.73801C18.1155 9.59036 17.9988 9.41506 17.9193 9.22218C17.8399 9.02929 17.7993 8.82261 17.8 8.61401C17.7958 8.51335 17.8042 8.41257 17.825 8.31401H6.25201C6.28081 8.43607 6.29756 8.56067 6.30201 8.68601C6.30267 8.89486 6.26202 9.10179 6.1824 9.29487C6.10278 9.48796 5.98576 9.66339 5.83807 9.81107C5.69039 9.95876 5.51496 10.0758 5.32187 10.1554C5.12879 10.235 4.92186 10.2757 4.71301 10.275C4.57841 10.2708 4.4445 10.254 4.31301 10.225V15.066C4.44423 15.0354 4.57829 15.0187 4.71301 15.016C4.97763 15.0185 5.23762 15.0856 5.47032 15.2117C5.70302 15.3377 5.90135 15.5187 6.04801 15.739C6.21828 15.9954 6.30684 16.2973 6.30201 16.605V16.805H17.847C17.8262 16.7064 17.8178 16.6057 17.822 16.505C17.8213 16.2962 17.862 16.0892 17.9416 15.8961C18.0212 15.7031 18.1383 15.5276 18.2859 15.3799C18.4336 15.2323 18.6091 15.1152 18.8021 15.0356C18.9952 14.956 19.2022 14.9153 19.411 14.916C19.5027 14.9108 19.5947 14.9193 19.684 14.941V10.178H19.659Z"
                fill={fill}
            />
        </svg>
    );
};
