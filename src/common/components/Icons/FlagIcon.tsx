import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const FlagIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5 5C5 4.44772 5.44772 4 6 4H12.3C12.4458 4.00044 12.5882 4.0443 12.709 4.126C12.8341 4.21079 12.9334 4.32844 12.996 4.466L13.2908 5.10429C13.4543 5.45832 13.8087 5.685 14.1986 5.685H18.226C18.3298 5.68582 18.4323 5.7082 18.527 5.75072C18.6216 5.79324 18.7064 5.85497 18.776 5.932C18.9249 6.09611 19.0063 6.31041 19.004 6.532V15.795C19.0063 16.0166 18.9249 16.2309 18.776 16.395C18.7064 16.472 18.6216 16.5338 18.527 16.5763C18.4323 16.6188 18.3298 16.6412 18.226 16.642H13.258C13.1122 16.6416 12.9698 16.5977 12.849 16.516C12.7239 16.4312 12.6245 16.3136 12.562 16.176L12.2671 15.5311C12.1044 15.1753 11.749 14.947 11.3577 14.947H7.556C7.00372 14.947 6.556 15.3947 6.556 15.947V19.222C6.556 19.6517 6.20768 20 5.778 20C5.34832 20 5 19.6517 5 19.222V5Z"
                fill={fill}
            />
        </svg>
    );
};
