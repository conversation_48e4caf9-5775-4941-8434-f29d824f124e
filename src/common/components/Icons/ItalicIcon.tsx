import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ItalicIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.383 6.00098H8.683V8.00998H11.931L9.453 16.71H6V18.719H14.7V16.715H11.457L13.935 8.01498H17.383V6.00098Z"
                fill={fill}
            />
        </svg>
    );
};
