import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PdfIcon: typeof Icon = ({ fill = '#EA3434', size = IconSize.M }: IconProps) => {
    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 22 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.09422 0C1.70246 0 0.574219 1.12824 0.574219 2.52V18.48C0.574219 19.8718 1.70246 21 3.09422 21H19.0542C20.446 21 21.5742 19.8718 21.5742 18.48V2.52C21.5742 1.12824 20.446 0 19.0542 0H3.09422ZM4.43306 13.2474C4.51832 13.3058 4.65953 13.335 4.8567 13.335C5.05386 13.335 5.19508 13.3058 5.28033 13.2474C5.36559 13.1891 5.41888 13.1148 5.4402 13.0245C5.46151 12.929 5.47217 12.8308 5.47217 12.73V11.6872H6.49529C6.7191 11.6872 6.94557 11.6474 7.17471 11.5678C7.40385 11.4829 7.61433 11.3608 7.80617 11.2016C8.00333 11.0371 8.16053 10.8328 8.27777 10.5887C8.40033 10.3445 8.46161 10.0606 8.46161 9.73689C8.46161 9.40786 8.40033 9.12128 8.27777 8.87716C8.16053 8.63305 8.00333 8.42872 7.80617 8.26421C7.61433 8.09439 7.40385 7.96968 7.17471 7.89007C6.9509 7.81046 6.72709 7.77067 6.50328 7.77067H4.86469C4.66753 7.77067 4.52631 7.79985 4.44106 7.85823C4.3558 7.9166 4.30251 7.99355 4.28119 8.08908C4.25988 8.1793 4.24922 8.27483 4.24922 8.37565V12.722C4.24922 12.8229 4.25988 12.9211 4.28119 13.0166C4.30251 13.1121 4.35313 13.1891 4.43306 13.2474ZM6.50328 10.4772H5.47217V8.98065H6.49529C6.6445 8.98065 6.77505 9.01249 6.88696 9.07617C7.00419 9.13986 7.09478 9.22742 7.15873 9.33887C7.22267 9.45031 7.25464 9.58033 7.25464 9.72892C7.25464 9.90405 7.21467 10.0473 7.13474 10.1588C7.06014 10.2702 6.96422 10.3525 6.84699 10.4056C6.73509 10.4533 6.62052 10.4772 6.50328 10.4772ZM9.17044 13.2474C9.2557 13.3058 9.39158 13.335 9.57809 13.335H11.0009C11.3952 13.335 11.7576 13.2634 12.0879 13.1201C12.4237 12.9768 12.7167 12.7804 12.9672 12.531C13.2176 12.2763 13.4121 11.9791 13.5507 11.6394C13.6892 11.2945 13.7585 10.923 13.7585 10.525C13.7585 10.1323 13.6892 9.76873 13.5507 9.43439C13.4121 9.10005 13.2176 8.80817 12.9672 8.55874C12.7167 8.30932 12.4263 8.11561 12.0959 7.97763C11.7655 7.83966 11.4085 7.77067 11.0249 7.77067H9.58608C9.38892 7.76535 9.24771 7.79189 9.16245 7.85027C9.07719 7.90865 9.0239 7.98559 9.00258 8.08112C8.98127 8.17134 8.97061 8.26687 8.97061 8.36769V12.722C8.97061 12.8282 8.98393 12.929 9.01058 13.0245C9.03722 13.1148 9.09051 13.1891 9.17044 13.2474ZM11.0249 12.1171H10.1856V8.98065H11.0169C11.3099 8.98065 11.5711 9.04964 11.8002 9.18762C12.0293 9.32029 12.2105 9.50604 12.3437 9.74485C12.4769 9.98366 12.5436 10.2543 12.5436 10.5568C12.5436 10.8593 12.4769 11.1273 12.3437 11.3608C12.2105 11.5943 12.0293 11.7801 11.8002 11.918C11.5711 12.0507 11.3126 12.1171 11.0249 12.1171ZM14.5737 13.2554C14.659 13.3085 14.7975 13.335 14.9893 13.335C15.1918 13.335 15.3331 13.3085 15.413 13.2554C15.4982 13.197 15.5515 13.1227 15.5728 13.0325C15.5942 12.937 15.6048 12.8388 15.6048 12.738V11.0583H17.2354C17.3473 11.0583 17.4432 11.0504 17.5232 11.0344C17.6084 11.0185 17.675 10.9761 17.723 10.9071C17.771 10.8328 17.7949 10.7107 17.7949 10.5409C17.7949 10.3658 17.771 10.241 17.723 10.1667C17.675 10.0925 17.6084 10.0473 17.5232 10.0314C17.4432 10.0155 17.35 10.0075 17.2434 10.0075H15.6048V8.98065H17.1787C17.2853 8.98065 17.3839 8.97269 17.4745 8.95677C17.5704 8.93554 17.6477 8.88247 17.7063 8.79756C17.7649 8.71265 17.7942 8.57467 17.7942 8.38362C17.7942 8.18195 17.7649 8.03866 17.7063 7.95376C17.653 7.86884 17.5811 7.81842 17.4905 7.8025C17.3999 7.78128 17.3013 7.77067 17.1947 7.77067H15.0133C14.7789 7.77067 14.6163 7.81312 14.5257 7.89803C14.4352 7.98294 14.3899 8.14215 14.3899 8.37565V12.73C14.3899 12.8308 14.3978 12.929 14.4138 13.0245C14.4352 13.1201 14.4884 13.197 14.5737 13.2554Z"
                fill={fill}
            />
        </svg>
    );
};
