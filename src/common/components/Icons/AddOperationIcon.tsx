import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AddOperationIcon: typeof Icon = ({ size = IconSize.M, className }: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.3477 5.53662V19.5366M5.34766 12.5366H19.3477"
                stroke="#0069FF"
                stroke-width="4"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </svg>
    );
};
