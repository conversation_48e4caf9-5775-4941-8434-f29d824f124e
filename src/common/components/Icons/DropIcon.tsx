import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DropIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.39999 8.893L11.3093 5.15957C11.6958 4.79049 12.3042 4.79049 12.6906 5.15957L16.6 8.893C17.5016 9.74856 18.122 10.8579 18.379 12.074C18.6297 13.2801 18.5004 14.5343 18.009 15.664C17.5067 16.8112 16.673 17.7821 15.615 18.452C14.5348 19.136 13.2825 19.4991 12.004 19.4991C10.7254 19.4991 9.4732 19.136 8.39299 18.452C7.33498 17.782 6.50131 16.8111 5.99899 15.664C5.50753 14.5343 5.37827 13.2801 5.62899 12.074C5.88373 10.8588 6.50128 9.74963 7.39999 8.893ZM12.691 7.11398C12.3044 6.74424 11.6954 6.74411 11.3087 7.11369L8.42499 9.87C7.957 10.3133 7.58292 10.8462 7.32499 11.437C7.20453 11.7141 7.1118 12.0017 7.04766 12.2953C6.92989 12.8346 7.38829 13.287 7.9403 13.287H16.0567C16.6087 13.287 17.0671 12.8346 16.9493 12.2953C16.8852 12.0017 16.7924 11.7141 16.672 11.437C16.4141 10.8462 16.04 10.3133 15.572 9.87L12.691 7.11398Z"
                fill={fill}
            />
        </svg>
    );
};
