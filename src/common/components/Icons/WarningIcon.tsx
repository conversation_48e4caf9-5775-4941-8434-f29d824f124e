import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const WarningIcon: typeof Icon = ({ fill = Colors.CM2, size = IconSize.M }: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.1298 3.76612C11.8751 3.76607 11.6232 3.81806 11.3894 3.91889C11.1556 4.01972 10.9449 4.16726 10.7702 4.35246C10.5954 4.53766 10.4604 4.75662 10.3734 4.99589C10.2863 5.23516 10.2491 5.48969 10.2639 5.74387L10.7306 13.6767C10.7514 14.0338 10.9079 14.3695 11.1681 14.6149C11.4283 14.8604 11.7725 14.9972 12.1302 14.9972C12.4879 14.9972 12.8321 14.8604 13.0923 14.6149C13.3525 14.3695 13.509 14.0338 13.5298 13.6767L13.9965 5.74387C14.0119 5.48956 13.9751 5.23479 13.8882 4.99528C13.8014 4.75576 13.6664 4.53659 13.4916 4.35127C13.3167 4.16595 13.1058 4.01841 12.8717 3.91777C12.6377 3.81714 12.3855 3.76552 12.1307 3.76612H12.1298Z"
                fill={fill}
            />
            <path
                d="M12.1298 16.359C12.4353 16.3593 12.7311 16.4655 12.967 16.6594C13.2029 16.8534 13.3643 17.1231 13.4237 17.4227C13.4831 17.7223 13.4368 18.0332 13.2927 18.3024C13.1486 18.5717 12.9156 18.7827 12.6334 18.8995C12.3512 19.0162 12.0372 19.0316 11.745 18.9429C11.4527 18.8542 11.2003 18.6669 11.0306 18.4129C10.8609 18.159 10.7846 17.8541 10.8144 17.5502C10.8443 17.2462 10.9787 16.962 11.1946 16.746C11.3161 16.6214 11.4616 16.5229 11.6224 16.4563C11.7832 16.3898 11.9558 16.3567 12.1298 16.359Z"
                fill={fill}
            />
        </svg>
    );
};
