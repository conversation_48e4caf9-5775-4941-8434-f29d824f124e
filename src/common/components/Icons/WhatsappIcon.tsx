import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const WhatsappIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.6797 6.37891C16.3184 4.98438 14.459 4.1875 12.4668 4.1875C8.41602 4.1875 5.0957 7.50781 5.0957 11.5586C5.0957 12.8867 5.46094 14.1484 6.0918 15.2441L5.0625 19.0625L8.94727 18.0664C10.043 18.6309 11.2383 18.9629 12.4668 18.9629C16.5508 18.9629 19.9375 15.6426 19.9375 11.5918C19.9375 9.59961 19.0742 7.77344 17.6797 6.37891ZM12.4668 17.7012C11.3711 17.7012 10.3086 17.4023 9.3457 16.8379L9.14648 16.7051L6.82227 17.3359L7.45312 15.0781L7.28711 14.8457C6.68945 13.8496 6.35742 12.7207 6.35742 11.5586C6.35742 8.20508 9.11328 5.44922 12.5 5.44922C14.127 5.44922 15.6543 6.08008 16.8164 7.24219C17.9785 8.4043 18.6758 9.93164 18.6758 11.5918C18.6758 14.9453 15.8535 17.7012 12.4668 17.7012ZM15.8535 13.1191C15.6543 13.0195 14.7578 12.5879 14.5918 12.5215C14.4258 12.4551 14.293 12.4219 14.1602 12.6211C14.0605 12.7871 13.6953 13.2188 13.5957 13.3516C13.4629 13.4512 13.3633 13.4844 13.1973 13.3848C12.1016 12.8535 11.4043 12.4219 10.6738 11.1934C10.4746 10.8613 10.873 10.8945 11.2051 10.1973C11.2715 10.0645 11.2383 9.96484 11.2051 9.86523C11.1719 9.76562 10.7734 8.86914 10.6406 8.50391C10.4746 8.13867 10.3418 8.17188 10.209 8.17188C10.1094 8.17188 9.97656 8.17188 9.87695 8.17188C9.74414 8.17188 9.54492 8.20508 9.37891 8.4043C9.21289 8.60352 8.74805 9.03516 8.74805 9.93164C8.74805 10.8613 9.37891 11.7246 9.47852 11.8574C9.57812 11.957 10.7734 13.8164 12.6328 14.6133C13.7949 15.1445 14.2598 15.1777 14.8574 15.0781C15.1895 15.0449 15.9199 14.6465 16.0859 14.2148C16.252 13.7832 16.252 13.418 16.1855 13.3516C16.1523 13.252 16.0195 13.2188 15.8535 13.1191Z"
                fill={fill}
            />
        </svg>
    );
};
