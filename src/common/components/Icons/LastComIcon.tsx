import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const LastComIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.965 19C6.81114 19.0032 6.66227 18.9454 6.55095 18.8391C6.43964 18.7329 6.37493 18.5868 6.371 18.433V17.024C5.75708 17.0374 5.16286 16.807 4.71847 16.3833C4.27408 15.9595 4.01573 15.3769 4 14.763V8.26301C4.01547 7.6487 4.27381 7.06558 4.71842 6.6414C5.16303 6.21722 5.75765 5.98658 6.372 6.00001H17.63C18.2438 5.98711 18.8378 6.21786 19.2819 6.64177C19.7261 7.06567 19.9843 7.64823 20 8.26201V14.762C19.9845 15.376 19.7263 15.9587 19.2818 16.3826C18.8374 16.8064 18.243 17.0367 17.629 17.023H10.68L7.264 18.923C7.17276 18.9743 7.06968 19.0009 6.965 19ZM8 12C8.55228 12 9 11.5523 9 11C9 10.4477 8.55228 10 8 10C7.44772 10 7 10.4477 7 11C7 11.5523 7.44772 12 8 12ZM13 11C13 11.5523 12.5523 12 12 12C11.4477 12 11 11.5523 11 11C11 10.4477 11.4477 10 12 10C12.5523 10 13 10.4477 13 11ZM16 12C16.5523 12 17 11.5523 17 11C17 10.4477 16.5523 10 16 10C15.4477 10 15 10.4477 15 11C15 11.5523 15.4477 12 16 12Z"
                fill={fill}
            />
        </svg>
    );
};
