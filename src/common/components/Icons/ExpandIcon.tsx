import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ExpandIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.65404 12.7522L5.54904 16.8582V14.1082C5.55254 13.9169 5.48848 13.7305 5.36813 13.5818C5.24778 13.4331 5.07884 13.3316 4.89104 13.2952C4.78007 13.2784 4.66677 13.2859 4.55898 13.3172C4.45119 13.3485 4.35149 13.4028 4.26675 13.4764C4.18202 13.55 4.11429 13.6411 4.06824 13.7435C4.02219 13.8458 3.99892 13.957 4.00004 14.0692V18.7162C4.00004 18.9217 4.08169 19.1189 4.22703 19.2642C4.37237 19.4095 4.5695 19.4912 4.77504 19.4912H9.38304C9.57431 19.4947 9.76069 19.4306 9.9094 19.3103C10.0581 19.1899 10.1596 19.021 10.196 18.8332C10.2129 18.7221 10.2053 18.6088 10.174 18.5009C10.1427 18.393 10.0882 18.2933 10.0145 18.2085C9.94081 18.1238 9.84955 18.0561 9.74708 18.0101C9.6446 17.9641 9.53335 17.9409 9.42104 17.9422H6.63304L10.7 13.8762C10.8402 13.7439 10.9289 13.5661 10.9501 13.3745C10.9714 13.1829 10.9238 12.99 10.816 12.8302C10.7533 12.7369 10.67 12.6592 10.5726 12.6031C10.4751 12.547 10.3661 12.5139 10.2539 12.5065C10.1417 12.4991 10.0293 12.5174 9.92534 12.5602C9.82135 12.603 9.72854 12.669 9.65404 12.7532V12.7522Z"
                fill={fill}
            />
            <path
                d="M14.069 4.00005C13.9567 3.99878 13.8454 4.02195 13.7429 4.06793C13.6404 4.11392 13.5492 4.18163 13.4755 4.26638C13.4018 4.35113 13.3474 4.45089 13.316 4.55875C13.2847 4.66661 13.2771 4.77999 13.294 4.89105C13.3304 5.07885 13.4319 5.24779 13.5806 5.36814C13.7293 5.48849 13.9157 5.55255 14.107 5.54905H16.857L12.791 9.61505C12.6385 9.74953 12.545 9.93852 12.5306 10.1413C12.5162 10.3441 12.5821 10.5444 12.714 10.699C12.7822 10.7872 12.8706 10.8578 12.9716 10.905C13.0727 10.9521 13.1835 10.9744 13.295 10.97C13.4991 10.9671 13.6939 10.8838 13.837 10.738L17.942 6.63305V9.38305C17.9385 9.57432 18.0025 9.7607 18.1229 9.90941C18.2432 10.0581 18.4122 10.1596 18.6 10.196C18.711 10.2129 18.8244 10.2053 18.9323 10.174C19.0401 10.1427 19.1399 10.0883 19.2246 10.0145C19.3094 9.94082 19.3771 9.84956 19.4231 9.74709C19.4691 9.64461 19.4922 9.53336 19.491 9.42105V4.77405C19.4907 4.56868 19.4089 4.37181 19.2636 4.22669C19.1183 4.08156 18.9213 4.00005 18.716 4.00005H14.069Z"
                fill={fill}
            />
        </svg>
    );
};
