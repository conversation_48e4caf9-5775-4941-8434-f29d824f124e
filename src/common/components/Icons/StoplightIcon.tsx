import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const StoplightIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M14.2489 10.8308V9.58768C14.2489 9.25466 14.3812 8.93528 14.6166 8.6998C14.8521 8.46432 15.1715 8.33203 15.5045 8.33203H25.4955C25.8285 8.33203 26.1479 8.46432 26.3834 8.6998C26.6188 8.93528 26.7511 9.25466 26.7511 9.58768V10.8433H30.5C30.4572 11.8875 30.0641 12.8868 29.384 13.6801C28.7039 14.4735 27.7764 15.0147 26.7511 15.2166V18.3288H30.5C30.4572 19.3729 30.0641 20.3722 29.384 21.1656C28.7039 21.959 27.7764 22.5002 26.7511 22.7021V25.8286H30.5C30.4572 26.8727 30.0641 27.872 29.384 28.6654C28.7039 29.4588 27.7764 30 26.7511 30.2019V32.0764C26.7511 32.4094 26.6188 32.7288 26.3834 32.9643C26.1479 33.1997 25.8285 33.332 25.4955 33.332H15.4991C15.1661 33.332 14.8467 33.1997 14.6113 32.9643C14.3758 32.7288 14.2435 32.4094 14.2435 32.0764V30.2019C13.2192 29.999 12.293 29.4573 11.6139 28.664C10.9349 27.8707 10.5425 26.872 10.5 25.8286H14.2489V22.7038C13.2233 22.5019 12.2956 21.9604 11.6155 21.1667C10.9354 20.373 10.5424 19.3732 10.5 18.3288H14.2489V15.2058C13.2233 15.0039 12.2956 14.4624 11.6155 13.6687C10.9354 12.8749 10.5424 11.8752 10.5 10.8308H14.2489ZM20.4964 30.8261C21.0745 30.8261 21.6347 30.6257 22.0815 30.2589C22.5284 29.8922 22.8343 29.3818 22.9471 28.8148C23.0598 28.2479 22.9725 27.6593 22.7 27.1495C22.4275 26.6396 21.9867 26.2401 21.4526 26.0188C20.9185 25.7976 20.3243 25.7684 19.7711 25.9362C19.2179 26.104 18.74 26.4585 18.4189 26.9391C18.0977 27.4198 17.9531 27.997 18.0098 28.5723C18.0665 29.1476 18.3208 29.6855 18.7296 30.0942C18.9616 30.3263 19.2371 30.5103 19.5402 30.6359C19.8434 30.7615 20.1683 30.8261 20.4964 30.8261V30.8261ZM20.4964 23.3281C21.0745 23.3281 21.6347 23.1276 22.0815 22.7609C22.5284 22.3942 22.8343 21.8838 22.9471 21.3168C23.0598 20.7498 22.9725 20.1613 22.7 19.6514C22.4275 19.1416 21.9867 18.742 21.4526 18.5208C20.9185 18.2996 20.3243 18.2704 19.7711 18.4382C19.2179 18.606 18.74 18.9604 18.4189 19.4411C18.0977 19.9218 17.9531 20.4989 18.0098 21.0743C18.0665 21.6496 18.3208 22.1874 18.7296 22.5962C18.9616 22.8282 19.2371 23.0123 19.5402 23.1379C19.8434 23.2635 20.1683 23.3281 20.4964 23.3281V23.3281ZM20.4964 15.8301C21.0745 15.8301 21.6347 15.6296 22.0815 15.2629C22.5284 14.8961 22.8343 14.3858 22.9471 13.8188C23.0598 13.2518 22.9725 12.6632 22.7 12.1534C22.4275 11.6436 21.9867 11.244 21.4526 11.0228C20.9185 10.8015 20.3243 10.7724 19.7711 10.9402C19.2179 11.108 18.74 11.4624 18.4189 11.9431C18.0977 12.4238 17.9531 13.0009 18.0098 13.5762C18.0665 14.1515 18.3208 14.6894 18.7296 15.0982C18.9616 15.3302 19.2371 15.5143 19.5402 15.6399C19.8434 15.7654 20.1683 15.8301 20.4964 15.8301Z"
                fill={fill}
            />
        </svg>
    );
};
