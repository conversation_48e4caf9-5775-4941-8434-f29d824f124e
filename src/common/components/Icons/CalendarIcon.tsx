import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CalendarIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.5177 6.70022H15.6444V6.23641C15.6444 6.02233 15.5594 5.81701 15.408 5.66563C15.2566 5.51424 15.0513 5.4292 14.8372 5.4292C14.6231 5.4292 14.4178 5.51424 14.2664 5.66563C14.115 5.81701 14.03 6.02233 14.03 6.23641V6.69929H9.95596V6.23641C9.95596 6.02233 9.87092 5.81701 9.71953 5.66563C9.56815 5.51424 9.36283 5.4292 9.14875 5.4292C8.93466 5.4292 8.72934 5.51424 8.57796 5.66563C8.42658 5.81701 8.34153 6.02233 8.34153 6.23641V6.69929H7.46826C7.07915 6.70077 6.70641 6.85606 6.43135 7.1313C6.15629 7.40653 6.00123 7.77937 6 8.16848V8.91807H18V8.16848C17.9978 7.77733 17.8404 7.40304 17.5625 7.12776C17.2846 6.85249 16.9088 6.69873 16.5177 6.70022Z"
                fill={fill}
            />
            <path
                d="M6 16.5303C6.00099 16.9196 6.15594 17.2926 6.43102 17.5681C6.70611 17.8435 7.07899 17.9989 7.46826 18.0004H16.5308C16.9198 17.9989 17.2924 17.8438 17.5674 17.5687C17.8424 17.2937 17.9976 16.9211 17.9991 16.5322V9.84863H6V16.5303ZM13.8707 12.3495C13.8707 12.3165 13.8772 12.2838 13.8898 12.2534C13.9024 12.2229 13.9209 12.1952 13.9442 12.1719C13.9676 12.1486 13.9952 12.1301 14.0257 12.1175C14.0562 12.1048 14.0888 12.0983 14.1218 12.0983H15.3258C15.3588 12.0983 15.3915 12.1048 15.4219 12.1175C15.4524 12.1301 15.4801 12.1486 15.5034 12.1719C15.5267 12.1952 15.5452 12.2229 15.5578 12.2534C15.5705 12.2838 15.5769 12.3165 15.5769 12.3495V13.1431C15.5769 13.1761 15.5705 13.2087 15.5578 13.2392C15.5452 13.2696 15.5267 13.2973 15.5034 13.3206C15.4801 13.344 15.4524 13.3625 15.4219 13.3751C15.3915 13.3877 15.3588 13.3942 15.3258 13.3942H14.1237C14.0571 13.3942 13.9932 13.3677 13.9461 13.3206C13.899 13.2735 13.8726 13.2097 13.8726 13.1431L13.8707 12.3495ZM13.8707 14.7041C13.8707 14.6711 13.8772 14.6385 13.8898 14.608C13.9024 14.5775 13.9209 14.5499 13.9442 14.5265C13.9676 14.5032 13.9952 14.4847 14.0257 14.4721C14.0562 14.4595 14.0888 14.453 14.1218 14.453H15.3258C15.3588 14.453 15.3915 14.4595 15.4219 14.4721C15.4524 14.4847 15.4801 14.5032 15.5034 14.5265C15.5267 14.5499 15.5452 14.5775 15.5578 14.608C15.5705 14.6385 15.5769 14.6711 15.5769 14.7041V15.4977C15.5769 15.5307 15.5705 15.5634 15.5578 15.5938C15.5452 15.6243 15.5267 15.652 15.5034 15.6753C15.4801 15.6986 15.4524 15.7171 15.4219 15.7297C15.3915 15.7423 15.3588 15.7488 15.3258 15.7488H14.1237C14.0571 15.7488 13.9932 15.7224 13.9461 15.6753C13.899 15.6282 13.8726 15.5643 13.8726 15.4977L13.8707 14.7041ZM11.1319 12.3495C11.1319 12.3165 11.1384 12.2838 11.151 12.2534C11.1636 12.2229 11.1821 12.1952 11.2054 12.1719C11.2287 12.1486 11.2564 12.1301 11.2869 12.1175C11.3174 12.1048 11.35 12.0983 11.383 12.0983H12.587C12.6536 12.0983 12.7175 12.1248 12.7646 12.1719C12.8117 12.219 12.8381 12.2829 12.8381 12.3495V13.1431C12.8381 13.2097 12.8117 13.2735 12.7646 13.3206C12.7175 13.3677 12.6536 13.3942 12.587 13.3942H11.3849C11.3519 13.3942 11.3192 13.3877 11.2888 13.3751C11.2583 13.3625 11.2306 13.344 11.2073 13.3206C11.184 13.2973 11.1655 13.2696 11.1529 13.2392C11.1402 13.2087 11.1338 13.1761 11.1338 13.1431L11.1319 12.3495ZM11.1319 14.7041C11.1319 14.6711 11.1384 14.6385 11.151 14.608C11.1636 14.5775 11.1821 14.5499 11.2054 14.5265C11.2287 14.5032 11.2564 14.4847 11.2869 14.4721C11.3174 14.4595 11.35 14.453 11.383 14.453H12.587C12.6536 14.453 12.7175 14.4794 12.7646 14.5265C12.8117 14.5736 12.8381 14.6375 12.8381 14.7041V15.4977C12.8381 15.5643 12.8117 15.6282 12.7646 15.6753C12.7175 15.7224 12.6536 15.7488 12.587 15.7488H11.3849C11.3519 15.7488 11.3192 15.7423 11.2888 15.7297C11.2583 15.7171 11.2306 15.6986 11.2073 15.6753C11.184 15.652 11.1655 15.6243 11.1529 15.5938C11.1402 15.5634 11.1338 15.5307 11.1338 15.4977L11.1319 14.7041ZM8.40806 12.3495C8.40806 12.2829 8.43451 12.219 8.48161 12.1719C8.5287 12.1248 8.59257 12.0983 8.65917 12.0983H9.8632C9.9298 12.0983 9.99367 12.1248 10.0408 12.1719C10.0879 12.219 10.1143 12.2829 10.1143 12.3495V13.1431C10.1143 13.2097 10.0879 13.2735 10.0408 13.3206C9.99367 13.3677 9.9298 13.3942 9.8632 13.3942H8.65917C8.59257 13.3942 8.5287 13.3677 8.48161 13.3206C8.43451 13.2735 8.40806 13.2097 8.40806 13.1431V12.3495ZM8.40806 14.7041C8.40806 14.6375 8.43451 14.5736 8.48161 14.5265C8.5287 14.4794 8.59257 14.453 8.65917 14.453H9.8632C9.9298 14.453 9.99367 14.4794 10.0408 14.5265C10.0879 14.5736 10.1143 14.6375 10.1143 14.7041V15.4977C10.1143 15.5643 10.0879 15.6282 10.0408 15.6753C9.99367 15.7224 9.9298 15.7488 9.8632 15.7488H8.65917C8.59257 15.7488 8.5287 15.7224 8.48161 15.6753C8.43451 15.6282 8.40806 15.5643 8.40806 15.4977V14.7041Z"
                fill={fill}
            />
        </svg>
    );
};
