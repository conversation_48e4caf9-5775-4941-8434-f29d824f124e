import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const Timer2Icon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.3333 4.99919C14.3333 5.64352 13.811 6.16585 13.1667 6.16585C13.1667 6.19822 13.1653 6.23028 13.1628 6.26198C14.2072 6.4366 15.1734 6.8425 16.0067 7.42524L16.9956 6.43635C17.4512 5.98074 18.1899 5.98074 18.6455 6.43635C19.1011 6.89197 19.1011 7.63066 18.6455 8.08627L17.6708 9.06104C18.507 10.2142 19 11.6325 19 13.1659C19 17.0318 15.866 20.1659 12 20.1659C8.13401 20.1659 5 17.0318 5 13.1659C5 9.69599 7.52465 6.8158 10.8372 6.26198C10.8347 6.23028 10.8333 6.19822 10.8333 6.16585C10.189 6.16585 9.66667 5.64352 9.66667 4.99919C9.66667 4.35485 10.189 3.83252 10.8333 3.83252H12H13.1667C13.811 3.83252 14.3333 4.35485 14.3333 4.99919ZM12.0019 8.49919C12.6454 8.50021 13.1667 9.02215 13.1667 9.66585V9.86501C14.5261 10.3455 15.5 11.6419 15.5 13.1659C15.5 15.0988 13.933 16.6659 12 16.6659C10.067 16.6659 8.5 15.0988 8.5 13.1659C8.5 11.6419 9.47394 10.3455 10.8333 9.86501V9.66585C10.8333 9.02193 11.355 8.49985 11.9988 8.49919C9.42201 8.49985 7.33333 10.5889 7.33333 13.1659C7.33333 15.7432 9.42267 17.8325 12 17.8325C14.5773 17.8325 16.6667 15.7432 16.6667 13.1659C16.6667 10.5892 14.5783 8.50021 12.0019 8.49919ZM12 11.9992C11.3557 11.9992 10.8333 12.5215 10.8333 13.1659C10.8333 13.8102 11.3557 14.3325 12 14.3325C12.6443 14.3325 13.1667 13.8102 13.1667 13.1659C13.1667 12.5215 12.6443 11.9992 12 11.9992Z"
                fill={fill}
            />
        </svg>
    );
};
