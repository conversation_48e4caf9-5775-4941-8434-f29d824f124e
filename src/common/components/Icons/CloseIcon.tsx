import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CloseIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.9497 7.05034C16.7247 6.82527 16.4194 6.69883 16.1011 6.69883C15.7828 6.69883 15.4776 6.82527 15.2525 7.05034L12 10.3029L8.74748 7.05034C8.52242 6.82527 8.21716 6.69883 7.89887 6.69883C7.58057 6.69883 7.27532 6.82527 7.05025 7.05034C6.82518 7.2754 6.69874 7.58066 6.69874 7.89895C6.69874 8.21724 6.82518 8.5225 7.05025 8.74757L10.3028 12.0001L7.05025 15.2526C6.82519 15.4777 6.69874 15.7829 6.69874 16.1012C6.69874 16.4195 6.82519 16.7248 7.05025 16.9498C7.27532 17.1749 7.58058 17.3013 7.89887 17.3013C8.21716 17.3013 8.52242 17.1749 8.74749 16.9498L11.9969 13.7004L15.2494 16.9529C15.4745 17.178 15.7798 17.3044 16.098 17.3044C16.4163 17.3044 16.7216 17.178 16.9467 16.9529C17.1717 16.7278 17.2982 16.4226 17.2982 16.1043C17.2982 15.786 17.1717 15.4808 16.9467 15.2557L13.6972 12.0063L16.9528 8.75066C17.0645 8.63896 17.1531 8.5063 17.2134 8.3603C17.2737 8.2143 17.3046 8.05783 17.3043 7.89986C17.3041 7.74189 17.2726 7.58553 17.2117 7.43975C17.1509 7.29397 17.0618 7.16163 16.9497 7.05034Z"
                fill={fill}
            />
        </svg>
    );
};
