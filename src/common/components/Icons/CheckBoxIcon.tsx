import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CheckBoxIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8 5C6.34315 5 5 6.34315 5 8V16C5 17.6569 6.34315 19 8 19H16C17.6569 19 19 17.6569 19 16V8C19 6.34315 17.6569 5 16 5H8ZM8 6C6.89543 6 6 6.89543 6 8V16C6 17.1046 6.89543 18 8 18H16C17.1046 18 18 17.1046 18 16V8C18 6.89543 17.1046 6 16 6H8Z"
                fill={fill}
            />
            <rect x="6" y="6" width="12" height="12" rx="2" fill="#7BABF9" fillOpacity="0.4" />
            <path
                d="M15.2464 9.00589C15.0946 9.02673 14.9542 9.10074 14.8485 9.21561L10.8573 13.375L9.08998 11.9138C9.02121 11.8568 8.94226 11.8144 8.85766 11.7889C8.77305 11.7635 8.68444 11.7556 8.59689 11.7657C8.50933 11.7757 8.42455 11.8035 8.34737 11.8475C8.27019 11.8915 8.20214 11.9508 8.14709 12.0221C8.09205 12.0933 8.05109 12.175 8.02655 12.2627C8.00201 12.3503 7.99438 12.442 8.00409 12.5327C8.01379 12.6234 8.04065 12.7112 8.08313 12.7911C8.1256 12.871 8.18286 12.9415 8.25163 12.9985L10.4867 14.8503C10.6164 14.9564 10.7794 15.0093 10.9444 14.9987C11.1094 14.988 11.2648 14.9146 11.3808 14.7926L15.7977 10.1918C15.8983 10.0902 15.9655 9.95847 15.9898 9.81519C16.0142 9.6719 15.9945 9.52429 15.9336 9.39321C15.8726 9.26213 15.7734 9.15419 15.65 9.08461C15.5267 9.01502 15.3853 8.98731 15.2459 9.00538L15.2464 9.00589Z"
                fill={fill}
            />
        </svg>
    );
};
