import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const BinnacleOutlineIcon: typeof Icon = ({
    fill = Colors.Grey5,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="path-1-outside-1_6_10967"
                maskUnits="userSpaceOnUse"
                x="5"
                y="3"
                width="14"
                height="17"
                fill="black"
            >
                <rect fill="white" x="5" y="3" width="14" height="17" />
                <path d="M17.0295 5.00014H16.2621V6.5248C16.2572 6.82692 16.133 7.11531 15.9157 7.32896C15.6984 7.54262 15.4051 7.66478 15.0979 7.6696H8.89363C8.58639 7.66478 8.29311 7.54262 8.07583 7.32896C7.85855 7.11531 7.73431 6.82692 7.72941 6.5248V5.00014H6.96631C6.83937 5 6.71366 5.02449 6.59635 5.07219C6.47905 5.11989 6.37247 5.18988 6.28272 5.27814C6.19296 5.3664 6.12179 5.4712 6.07327 5.58654C6.02476 5.70188 5.99986 5.8255 6 5.95032V18.0498C5.99986 18.1746 6.02476 18.2983 6.07327 18.4136C6.12179 18.5289 6.19296 18.6337 6.28272 18.722C6.37247 18.8103 6.47905 18.8802 6.59635 18.9279C6.71366 18.9757 6.83937 19.0001 6.96631 19H17.0337C17.1606 19.0001 17.2863 18.9757 17.4036 18.9279C17.5209 18.8802 17.6275 18.8103 17.7173 18.722C17.807 18.6337 17.8782 18.5289 17.9267 18.4136C17.9752 18.2983 18.0001 18.1746 18 18.0498V5.95032C18.0001 5.82515 17.9751 5.70119 17.9263 5.58558C17.8775 5.46996 17.806 5.36497 17.7158 5.27666C17.6256 5.18834 17.5185 5.11844 17.4007 5.07099C17.2829 5.02353 17.1568 4.99945 17.0295 5.00014ZM15.2493 16.0995H8.74122C8.64003 16.0995 8.54298 16.06 8.47143 15.9896C8.39987 15.9192 8.35968 15.8238 8.35968 15.7243C8.35968 15.6248 8.39987 15.5294 8.47143 15.459C8.54298 15.3887 8.64003 15.3491 8.74122 15.3491H15.2747C15.3749 15.3515 15.4705 15.3917 15.5414 15.4615C15.6123 15.5313 15.6533 15.6252 15.6557 15.7238C15.6518 15.8264 15.607 15.9234 15.5309 15.9937C15.4548 16.064 15.3536 16.102 15.2493 16.0995ZM15.2493 13.1751H8.74122C8.64003 13.1751 8.54298 13.1355 8.47143 13.0652C8.39987 12.9948 8.35968 12.8994 8.35968 12.7999C8.35968 12.7004 8.39987 12.6049 8.47143 12.5346C8.54298 12.4642 8.64003 12.4247 8.74122 12.4247H15.2747C15.3749 12.4271 15.4705 12.4673 15.5414 12.5371C15.6123 12.6068 15.6533 12.7007 15.6557 12.7993C15.6558 12.8507 15.6452 12.9016 15.6244 12.9487C15.6037 12.9959 15.5734 13.0384 15.5352 13.0735C15.4971 13.1087 15.4521 13.1357 15.4029 13.153C15.3537 13.1703 15.3014 13.1774 15.2493 13.174V13.1751ZM15.2493 10.2756H8.74122C8.64003 10.2756 8.54298 10.2361 8.47143 10.1657C8.39987 10.0953 8.35968 9.99991 8.35968 9.9004C8.35968 9.8009 8.39987 9.70547 8.47143 9.63511C8.54298 9.56475 8.64003 9.52522 8.74122 9.52522H15.2747C15.3749 9.5276 15.4705 9.56783 15.5414 9.63759C15.6123 9.70734 15.6533 9.80126 15.6557 9.89988C15.6515 10.0023 15.6066 10.099 15.5305 10.1691C15.4545 10.2392 15.3535 10.2771 15.2493 10.2745V10.2756ZM8.51261 6.52897V4.37466C8.51503 4.27604 8.55595 4.18212 8.62689 4.11237C8.69783 4.04261 8.79334 4.00238 8.89363 4H15.0979C15.1982 4.00238 15.2937 4.04261 15.3646 4.11237C15.4356 4.18212 15.4765 4.27604 15.4789 4.37466V6.5248C15.4765 6.62342 15.4356 6.71734 15.3646 6.7871C15.2937 6.85685 15.1982 6.89709 15.0979 6.89947H8.89363C8.84279 6.9025 8.79187 6.8949 8.74424 6.87714C8.69662 6.85939 8.65336 6.83189 8.61735 6.79648C8.58133 6.76106 8.55337 6.71853 8.53532 6.6717C8.51726 6.62487 8.50952 6.5748 8.51261 6.5248V6.52897Z" />
            </mask>
            <path
                d="M17.0295 5.00014H16.2621V6.5248C16.2572 6.82692 16.133 7.11531 15.9157 7.32896C15.6984 7.54262 15.4051 7.66478 15.0979 7.6696H8.89363C8.58639 7.66478 8.29311 7.54262 8.07583 7.32896C7.85855 7.11531 7.73431 6.82692 7.72941 6.5248V5.00014H6.96631C6.83937 5 6.71366 5.02449 6.59635 5.07219C6.47905 5.11989 6.37247 5.18988 6.28272 5.27814C6.19296 5.3664 6.12179 5.4712 6.07327 5.58654C6.02476 5.70188 5.99986 5.8255 6 5.95032V18.0498C5.99986 18.1746 6.02476 18.2983 6.07327 18.4136C6.12179 18.5289 6.19296 18.6337 6.28272 18.722C6.37247 18.8103 6.47905 18.8802 6.59635 18.9279C6.71366 18.9757 6.83937 19.0001 6.96631 19H17.0337C17.1606 19.0001 17.2863 18.9757 17.4036 18.9279C17.5209 18.8802 17.6275 18.8103 17.7173 18.722C17.807 18.6337 17.8782 18.5289 17.9267 18.4136C17.9752 18.2983 18.0001 18.1746 18 18.0498V5.95032C18.0001 5.82515 17.9751 5.70119 17.9263 5.58558C17.8775 5.46996 17.806 5.36497 17.7158 5.27666C17.6256 5.18834 17.5185 5.11844 17.4007 5.07099C17.2829 5.02353 17.1568 4.99945 17.0295 5.00014ZM15.2493 16.0995H8.74122C8.64003 16.0995 8.54298 16.06 8.47143 15.9896C8.39987 15.9192 8.35968 15.8238 8.35968 15.7243C8.35968 15.6248 8.39987 15.5294 8.47143 15.459C8.54298 15.3887 8.64003 15.3491 8.74122 15.3491H15.2747C15.3749 15.3515 15.4705 15.3917 15.5414 15.4615C15.6123 15.5313 15.6533 15.6252 15.6557 15.7238C15.6518 15.8264 15.607 15.9234 15.5309 15.9937C15.4548 16.064 15.3536 16.102 15.2493 16.0995ZM15.2493 13.1751H8.74122C8.64003 13.1751 8.54298 13.1355 8.47143 13.0652C8.39987 12.9948 8.35968 12.8994 8.35968 12.7999C8.35968 12.7004 8.39987 12.6049 8.47143 12.5346C8.54298 12.4642 8.64003 12.4247 8.74122 12.4247H15.2747C15.3749 12.4271 15.4705 12.4673 15.5414 12.5371C15.6123 12.6068 15.6533 12.7007 15.6557 12.7993C15.6558 12.8507 15.6452 12.9016 15.6244 12.9487C15.6037 12.9959 15.5734 13.0384 15.5352 13.0735C15.4971 13.1087 15.4521 13.1357 15.4029 13.153C15.3537 13.1703 15.3014 13.1774 15.2493 13.174V13.1751ZM15.2493 10.2756H8.74122C8.64003 10.2756 8.54298 10.2361 8.47143 10.1657C8.39987 10.0953 8.35968 9.99991 8.35968 9.9004C8.35968 9.8009 8.39987 9.70547 8.47143 9.63511C8.54298 9.56475 8.64003 9.52522 8.74122 9.52522H15.2747C15.3749 9.5276 15.4705 9.56783 15.5414 9.63759C15.6123 9.70734 15.6533 9.80126 15.6557 9.89988C15.6515 10.0023 15.6066 10.099 15.5305 10.1691C15.4545 10.2392 15.3535 10.2771 15.2493 10.2745V10.2756ZM8.51261 6.52897V4.37466C8.51503 4.27604 8.55595 4.18212 8.62689 4.11237C8.69783 4.04261 8.79334 4.00238 8.89363 4H15.0979C15.1982 4.00238 15.2937 4.04261 15.3646 4.11237C15.4356 4.18212 15.4765 4.27604 15.4789 4.37466V6.5248C15.4765 6.62342 15.4356 6.71734 15.3646 6.7871C15.2937 6.85685 15.1982 6.89709 15.0979 6.89947H8.89363C8.84279 6.9025 8.79187 6.8949 8.74424 6.87714C8.69662 6.85939 8.65336 6.83189 8.61735 6.79648C8.58133 6.76106 8.55337 6.71853 8.53532 6.6717C8.51726 6.62487 8.50952 6.5748 8.51261 6.5248V6.52897Z"
                stroke={fill}
                stroke-width="2"
                mask="url(#path-1-outside-1_6_10967)"
            />
        </svg>
    );
};
