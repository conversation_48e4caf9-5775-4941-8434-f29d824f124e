import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PlusInCircleIcon: typeof Icon = ({
    fill = '#4A4D51',
    size = IconSize.M,
}: IconProps) => {
    return (
        <svg
            width={size}
            height={size}
            style={{ minWidth: size }}
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="12.7773" cy="12.7402" r="11.5" fill="white" stroke="#C9CDD3" />
            <path
                d="M12.7778 5.74023C12.4596 5.74023 12.1544 5.86666 11.9294 6.09169C11.7044 6.31672 11.5779 6.6219 11.5779 6.94014V11.5403H6.97719C6.65897 11.5403 6.35379 11.6667 6.12877 11.8918C5.90376 12.1168 5.77734 12.422 5.77734 12.7402C5.77734 13.0584 5.90376 13.3637 6.12877 13.5887C6.35379 13.8137 6.65897 13.9402 6.97719 13.9402H11.5771V18.5403C11.5771 18.8586 11.7035 19.1638 11.9285 19.3888C12.1535 19.6138 12.4587 19.7402 12.7769 19.7402C13.0951 19.7402 13.4003 19.6138 13.6253 19.3888C13.8503 19.1638 13.9767 18.8586 13.9767 18.5403V13.9402H18.5775C18.8957 13.9402 19.2009 13.8137 19.4259 13.5887C19.6509 13.3637 19.7773 13.0584 19.7773 12.7402C19.7773 12.422 19.6509 12.1168 19.4259 11.8918C19.2009 11.6667 18.8957 11.5403 18.5775 11.5403H13.9776V6.94014C13.9776 6.6219 13.8512 6.31672 13.6262 6.09169C13.4012 5.86666 13.096 5.74023 12.7778 5.74023Z"
                fill={fill}
            />
            <defs>
                <rect
                    width="24"
                    height="24"
                    fill="white"
                    transform="translate(0.777344 0.740234)"
                />
            </defs>
        </svg>
    );
};
