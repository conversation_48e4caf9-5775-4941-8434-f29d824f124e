import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SearchIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill={fill}
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.36 15.7218C11.4125 15.7236 12.442 15.4138 13.3193 14.8315L16.1728 17.6745C16.2704 17.7759 16.3872 17.8569 16.5164 17.9128C16.6455 17.9687 16.7844 17.9983 16.9251 17.9999C17.0658 18.0016 17.2054 17.9752 17.3358 17.9224C17.4662 17.8696 17.5849 17.7914 17.6849 17.6923C17.7849 17.5932 17.8642 17.4751 17.9183 17.3451C17.9724 17.215 18.0002 17.0755 18 16.9346C17.9998 16.7937 17.9717 16.6542 17.9173 16.5243C17.8629 16.3944 17.7832 16.2765 17.683 16.1777L14.8146 13.3173C15.4012 12.441 15.7115 11.4084 15.7052 10.3534C15.7037 9.29316 15.3883 8.25712 14.799 7.37635C14.2096 6.49558 13.3727 5.80962 12.3941 5.40523C11.4154 5.00084 10.3391 4.89617 9.30106 5.10447C8.26305 5.31276 7.31002 5.82467 6.56248 6.57544C5.81495 7.32621 5.30649 8.28214 5.10141 9.32234C4.89632 10.3625 5.00381 11.4403 5.41029 12.4193C5.81677 13.3983 6.50399 14.2346 7.38503 14.8224C8.26608 15.4103 9.30138 15.7232 10.36 15.7218ZM10.36 7.10249C11.002 7.10249 11.6296 7.29315 12.1634 7.65037C12.6971 8.00759 13.1132 8.51532 13.3589 9.10935C13.6045 9.70339 13.6688 10.357 13.5436 10.9877C13.4183 11.6183 13.1092 12.1976 12.6552 12.6522C12.2013 13.1069 11.6229 13.4165 10.9933 13.5419C10.3636 13.6674 9.71097 13.603 9.11786 13.3569C8.52474 13.1109 8.0178 12.6942 7.66113 12.1596C7.30447 11.625 7.1141 10.9964 7.1141 10.3534C7.11541 9.49164 7.45781 8.66551 8.06625 8.05612C8.6747 7.44673 9.49955 7.1038 10.36 7.10249Z"
                fill={fill}
            />
        </svg>
    );
};
