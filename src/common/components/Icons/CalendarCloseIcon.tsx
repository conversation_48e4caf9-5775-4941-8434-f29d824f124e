import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CalendarCloseIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.5 1.625C8.05228 1.625 8.5 2.07272 8.5 2.625V3.625H15.5V2.625C15.5 2.07272 15.9477 1.625 16.5 1.625C17.0523 1.625 17.5 2.07272 17.5 2.625V3.64969C20.0267 3.90055 22 6.03233 22 8.625V17.625C22 20.3864 19.7614 22.625 17 22.625H7C4.23858 22.625 2 20.3864 2 17.625V8.625C2 6.03233 3.97334 3.90055 6.5 3.64969V2.625C6.5 2.07272 6.94772 1.625 7.5 1.625ZM4.17071 7.625H19.8293C19.4175 6.45981 18.3062 5.625 17 5.625H7C5.69378 5.625 4.58254 6.45981 4.17071 7.625ZM20 9.625H4V17.625C4 19.2819 5.34315 20.625 7 20.625H17C18.6569 20.625 20 19.2819 20 17.625V9.625ZM9.17157 12.2966C9.5621 11.906 10.1953 11.906 10.5858 12.2966L12 13.7108L13.4142 12.2966C13.8047 11.906 14.4379 11.906 14.8284 12.2966C15.219 12.6871 15.219 13.3203 14.8284 13.7108L13.4142 15.125L14.8284 16.5392C15.219 16.9297 15.219 17.5629 14.8284 17.9534C14.4379 18.344 13.8047 18.344 13.4142 17.9534L12 16.5392L10.5858 17.9534C10.1953 18.344 9.5621 18.344 9.17157 17.9534C8.78105 17.5629 8.78105 16.9297 9.17157 16.5392L10.5858 15.125L9.17157 13.7108C8.78105 13.3203 8.78105 12.6871 9.17157 12.2966Z"
                fill={fill}
            />
        </svg>
    );
};
