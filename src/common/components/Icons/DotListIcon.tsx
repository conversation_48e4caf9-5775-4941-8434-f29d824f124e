import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DotListIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5 11.826C5 11.7167 5.02152 11.6086 5.06333 11.5076C5.10514 11.4067 5.16643 11.3149 5.24369 11.2377C5.32095 11.1604 5.41266 11.0991 5.51361 11.0573C5.61455 11.0155 5.72274 10.994 5.832 10.994H5.982C6.09126 10.994 6.19945 11.0155 6.30039 11.0573C6.40134 11.0991 6.49306 11.1604 6.57031 11.2377C6.64757 11.3149 6.70886 11.4067 6.75067 11.5076C6.79248 11.6086 6.814 11.7167 6.814 11.826C6.814 12.0467 6.72634 12.2583 6.57031 12.4143C6.41428 12.5703 6.20266 12.658 5.982 12.658H5.833C5.72366 12.6581 5.61536 12.6367 5.5143 12.595C5.41324 12.5532 5.32141 12.4919 5.24404 12.4147C5.16668 12.3374 5.1053 12.2456 5.06343 12.1446C5.02155 12.0436 5 11.9353 5 11.826ZM5.832 7.665H5.982C6.09126 7.665 6.19945 7.64348 6.30039 7.60167C6.40134 7.55986 6.49306 7.49857 6.57031 7.42131C6.64757 7.34406 6.70886 7.25234 6.75067 7.15139C6.79248 7.05045 6.814 6.94226 6.814 6.833C6.81413 6.72366 6.79271 6.61536 6.75096 6.5143C6.7092 6.41324 6.64794 6.32141 6.57067 6.24404C6.4934 6.16668 6.40163 6.1053 6.30062 6.06343C6.19962 6.02155 6.09134 6 5.982 6H5.833C5.61234 6 5.40072 6.08766 5.24469 6.24369C5.08866 6.39972 5.001 6.61134 5.001 6.832C5.00087 6.94134 5.02229 7.04964 5.06404 7.1507C5.1058 7.25176 5.16706 7.3436 5.24433 7.42096C5.3216 7.49832 5.41337 7.5597 5.51438 7.60157C5.61538 7.64345 5.72366 7.665 5.833 7.665H5.832ZM5.832 17.652H5.982C6.20266 17.652 6.41428 17.5643 6.57031 17.4083C6.72634 17.2523 6.814 17.0407 6.814 16.82C6.814 16.5993 6.72634 16.3877 6.57031 16.2317C6.41428 16.0757 6.20266 15.988 5.982 15.988H5.833C5.61234 15.988 5.40072 16.0757 5.24469 16.2317C5.08866 16.3877 5.001 16.5993 5.001 16.82C5.001 17.0407 5.08866 17.2523 5.24469 17.4083C5.40072 17.5643 5.61234 17.652 5.833 17.652H5.832ZM9.732 12.658H18.055C18.2757 12.658 18.4873 12.5703 18.6433 12.4143C18.7993 12.2583 18.887 12.0467 18.887 11.826C18.887 11.7167 18.8655 11.6086 18.8237 11.5076C18.7819 11.4067 18.7206 11.3149 18.6433 11.2377C18.5661 11.1604 18.4743 11.0991 18.3734 11.0573C18.2725 11.0155 18.1643 10.994 18.055 10.994H9.731C9.62174 10.994 9.51355 11.0155 9.41261 11.0573C9.31166 11.0991 9.21994 11.1604 9.14269 11.2377C9.06543 11.3149 9.00414 11.4067 8.96233 11.5076C8.92052 11.6086 8.899 11.7167 8.899 11.826C8.899 11.9353 8.92052 12.0434 8.96233 12.1444C9.00414 12.2453 9.06543 12.3371 9.14269 12.4143C9.21994 12.4916 9.31166 12.5529 9.41261 12.5947C9.51355 12.6365 9.62174 12.658 9.731 12.658H9.732ZM9.732 7.664H18.055C18.2757 7.664 18.4873 7.57634 18.6433 7.42031C18.7993 7.26428 18.887 7.05266 18.887 6.832C18.887 6.72266 18.8654 6.61438 18.8236 6.51338C18.7817 6.41237 18.7203 6.3206 18.643 6.24333C18.5656 6.16606 18.4738 6.1048 18.3727 6.06305C18.2716 6.02129 18.1633 5.99987 18.054 6H9.731C9.62174 6 9.51355 6.02152 9.41261 6.06333C9.31166 6.10515 9.21994 6.16643 9.14269 6.24369C9.06543 6.32095 9.00414 6.41266 8.96233 6.51361C8.92052 6.61455 8.899 6.72274 8.899 6.832C8.89887 6.94134 8.92029 7.04964 8.96204 7.1507C9.0038 7.25176 9.06506 7.3436 9.14233 7.42096C9.2196 7.49832 9.31137 7.5597 9.41238 7.60157C9.51338 7.64345 9.62166 7.665 9.731 7.665L9.732 7.664ZM9.732 17.651H18.055C18.2757 17.651 18.4873 17.5633 18.6433 17.4073C18.7993 17.2513 18.887 17.0397 18.887 16.819C18.887 16.5983 18.7993 16.3867 18.6433 16.2307C18.4873 16.0747 18.2757 15.987 18.055 15.987H9.731C9.62174 15.987 9.51355 16.0085 9.41261 16.0503C9.31166 16.0921 9.21994 16.1534 9.14269 16.2307C9.06543 16.3079 9.00414 16.3997 8.96233 16.5006C8.92052 16.6016 8.899 16.7097 8.899 16.819C8.89887 16.9283 8.92029 17.0366 8.96204 17.1377C9.0038 17.2388 9.06506 17.3306 9.14233 17.408C9.2196 17.4853 9.31137 17.5467 9.41238 17.5886C9.51338 17.6304 9.62166 17.652 9.731 17.652L9.732 17.651Z"
                fill={fill}
            />
        </svg>
    );
};
