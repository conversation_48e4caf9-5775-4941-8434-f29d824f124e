import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AttachFileIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size, flexShrink: 0 }}
            height={size}
            viewBox="0 -3 24 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.4583 23.8346C10.7972 23.8346 9.38889 23.2569 8.23333 22.1013C7.07778 20.9457 6.5 19.5374 6.5 17.8763V6.5013C6.5 5.30964 6.92431 4.2895 7.77292 3.44089C8.62153 2.59227 9.64167 2.16797 10.8333 2.16797C12.025 2.16797 13.0451 2.59227 13.8937 3.44089C14.7424 4.2895 15.1667 5.30964 15.1667 6.5013V16.793C15.1667 17.5513 14.9049 18.1923 14.3812 18.7159C13.8576 19.2395 13.2167 19.5013 12.4583 19.5013C11.7 19.5013 11.059 19.2395 10.5354 18.7159C10.0118 18.1923 9.75 17.5513 9.75 16.793V6.5013H11.375V16.793C11.375 17.0999 11.4788 17.3572 11.6865 17.5648C11.8941 17.7725 12.1514 17.8763 12.4583 17.8763C12.7653 17.8763 13.0226 17.7725 13.2302 17.5648C13.4378 17.3572 13.5417 17.0999 13.5417 16.793V6.5013C13.5417 5.74297 13.2799 5.102 12.7562 4.57839C12.2326 4.05477 11.5917 3.79297 10.8333 3.79297C10.075 3.79297 9.43403 4.05477 8.91042 4.57839C8.38681 5.102 8.125 5.74297 8.125 6.5013V17.8763C8.125 19.068 8.54931 20.0881 9.39792 20.9367C10.2465 21.7853 11.2667 22.2096 12.4583 22.2096C13.65 22.2096 14.6701 21.7853 15.5187 20.9367C16.3674 20.0881 16.7917 19.068 16.7917 17.8763V6.5013H18.4167V17.8763C18.4167 19.5374 17.8389 20.9457 16.6833 22.1013C15.5278 23.2569 14.1194 23.8346 12.4583 23.8346Z"
                fill={fill}
            />
        </svg>
    );
};
