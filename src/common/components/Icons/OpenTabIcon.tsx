import { SvgIcon } from '@mui/material';

const OpenTabIcon = (props: any) => {
    const { color, ...otherProps } = props;
    return (
        <SvgIcon
            {...otherProps}
            style={{ width: 21, height: 21, ...props.style }}
            viewBox="0 0 21 21"
        >
            <path
                d="M4.40104 18.368C3.94271 18.368 3.55035 18.2048 3.22396 17.8784C2.89757 17.552 2.73438 17.1596 2.73438 16.7013V5.03465C2.73438 4.57631 2.89757 4.18395 3.22396 3.85756C3.55035 3.53118 3.94271 3.36798 4.40104 3.36798H10.2344V5.03465H4.40104V16.7013H16.0677V10.868H17.7344V16.7013C17.7344 17.1596 17.5712 17.552 17.2448 17.8784C16.9184 18.2048 16.526 18.368 16.0677 18.368H4.40104ZM8.31771 13.9513L7.15104 12.7846L14.901 5.03465H11.901V3.36798H17.7344V9.20131H16.0677V6.20131L8.31771 13.9513Z"
                fill="#0069FF"
            />
        </SvgIcon>
    );
};

export default OpenTabIcon;
