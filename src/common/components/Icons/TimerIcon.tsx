import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const TimerIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14 5.5C14 5.77614 13.7761 6 13.5 6L10.5 6C10.2239 6 10 5.77614 10 5.5C10 5.22386 10.2239 5 10.5 5H13.5C13.7761 5 14 5.22386 14 5.5ZM12 19C15.3137 19 18 16.3137 18 13C18 11.8426 17.6723 10.7618 17.1046 9.8452L17.3588 9.591C17.6517 9.29811 17.6517 8.82323 17.3588 8.53034C17.0659 8.23745 16.5911 8.23745 16.2982 8.53034L16.156 8.67247C15.0778 7.63668 13.6132 7 12 7C8.68629 7 6 9.68629 6 13C6 16.3137 8.68629 19 12 19ZM12.25 10C12.6642 10 13 10.3358 13 10.75V13.25C13 13.6642 12.6642 14 12.25 14C11.8358 14 11.5 13.6642 11.5 13.25V10.75C11.5 10.3358 11.8358 10 12.25 10Z"
                fill={fill}
            />
        </svg>
    );
};
