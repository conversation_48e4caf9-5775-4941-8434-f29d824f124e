import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DownloadIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.1826 18.0459H5.55205C5.25037 18.0459 4.96104 18.1657 4.74772 18.3791C4.5344 18.5924 4.41455 18.8817 4.41455 19.1834C4.41455 19.4851 4.5344 19.7744 4.74772 19.9877C4.96104 20.2011 5.25037 20.3209 5.55205 20.3209H19.1826C19.4842 20.3209 19.7736 20.2011 19.9869 19.9877C20.2002 19.7744 20.3201 19.4851 20.3201 19.1834C20.3201 18.8817 20.2002 18.5924 19.9869 18.3791C19.7736 18.1657 19.4842 18.0459 19.1826 18.0459Z"
                fill={fill}
            />
            <path
                d="M12.3672 4.4165C12.0661 4.4165 11.7773 4.53612 11.5644 4.74903C11.3515 4.96195 11.2318 5.25073 11.2318 5.55184V11.8915L9.99359 10.6587C9.7765 10.4717 9.49675 10.3737 9.21044 10.3843C8.92413 10.395 8.65243 10.5135 8.44984 10.7161C8.24725 10.9187 8.12875 11.1904 8.11809 11.4767C8.10744 11.763 8.20544 12.0428 8.39242 12.2598L11.5644 15.4373L11.5926 15.4568C11.6297 15.493 11.6695 15.5263 11.7118 15.5564L11.7573 15.59C11.8143 15.6244 11.8732 15.6555 11.9338 15.6832C11.9951 15.7051 12.0577 15.7232 12.1213 15.7373C12.2021 15.7576 12.285 15.7681 12.3683 15.7688C12.4485 15.7678 12.5284 15.7584 12.6066 15.7406C12.6733 15.7271 12.7389 15.7083 12.8027 15.6843C12.8633 15.6566 12.9222 15.6255 12.9793 15.5911L13.0248 15.5575C13.067 15.5274 13.1068 15.4941 13.1439 15.4578L13.1721 15.4383L16.3441 12.2609C16.5311 12.0438 16.6291 11.7641 16.6184 11.4778C16.6078 11.1915 16.4893 10.9198 16.2867 10.7172C16.0841 10.5146 15.8124 10.3961 15.5261 10.3854C15.2398 10.3748 14.96 10.4728 14.7429 10.6598L13.5025 11.8915V5.55184C13.5025 5.25073 13.3829 4.96195 13.17 4.74903C12.9571 4.53612 12.6683 4.4165 12.3672 4.4165Z"
                fill={fill}
            />
        </svg>
    );
};
