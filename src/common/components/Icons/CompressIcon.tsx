import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CompressIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M 18.625 10 C 18.8125 10 19 9.8438 19 9.625 V 8.375 C 19 8.1875 18.8125 8 18.625 8 H 16 V 5.375 C 16 5.1875 15.8125 5 15.625 5 H 14.375 C 14.1563 5 14 5.1875 14 5.375 V 9.25 C 14 9.6875 14.3125 10 14.75 10 H 18.625 Z M 10 9.25 V 5.375 C 10 5.1875 9.8125 5 9.625 5 H 8.375 C 8.1563 5 8 5.1875 8 5.375 V 8 H 5.375 C 5.1563 8 5 8.1875 5 8.375 V 9.625 C 5 9.8438 5.1563 10 5.375 10 H 9.25 C 9.6563 10 10 9.6875 10 9.25 Z M 10 18.625 V 14.75 C 10 14.3438 9.6563 14 9.25 14 H 5.375 C 5.1563 14 5 14.1875 5 14.375 V 15.625 C 5 15.8438 5.1563 16 5.375 16 H 8 V 18.625 C 8 18.8438 8.1563 19 8.375 19 H 9.625 C 9.8125 19 10 18.8438 10 18.625 Z M 16 18.625 V 16 H 18.625 C 18.8125 16 19 15.8438 19 15.625 V 14.375 C 19 14.1875 18.8125 14 18.625 14 H 14.75 C 14.3125 14 14 14.3438 14 14.75 V 18.625 C 14 18.8438 14.1563 19 14.375 19 H 15.625 C 15.8125 19 16 18.8438 16 18.625 Z"
                fill={fill}
            />
        </svg>
    );
};
