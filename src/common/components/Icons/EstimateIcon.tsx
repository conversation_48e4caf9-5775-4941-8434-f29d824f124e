import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const EstimateIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M25.0002 34.4333H13.3335C12.0074 34.4333 10.7356 33.9065 9.79796 32.9688C8.86028 32.0311 8.3335 30.7594 8.3335 29.4333V13.6133C8.3335 12.2872 8.86028 11.0154 9.79796 10.0777C10.7356 9.14007 12.0074 8.61328 13.3335 8.61328H25.0002C26.3262 8.61328 27.598 9.14007 28.5357 10.0777C29.4734 11.0154 30.0002 12.2872 30.0002 13.6133V29.4333C30.0002 30.7594 29.4734 32.0311 28.5357 32.9688C27.598 33.9065 26.3262 34.4333 25.0002 34.4333V34.4333ZM13.7568 22.4099C13.6552 22.4096 13.5546 22.4295 13.4607 22.4684C13.3669 22.5073 13.2817 22.5645 13.2102 22.6366C13.0656 22.7818 12.9845 22.9784 12.9845 23.1833C12.9845 23.3882 13.0656 23.5847 13.2102 23.7299L14.2102 24.7299L13.2102 25.7299C13.0656 25.8752 12.9845 26.0717 12.9845 26.2766C12.9845 26.4815 13.0656 26.6781 13.2102 26.8233C13.3552 26.9682 13.5518 27.0496 13.7568 27.0496C13.9619 27.0496 14.1585 26.9682 14.3035 26.8233L15.3035 25.8233L16.3035 26.8233C16.4489 26.9674 16.6454 27.0483 16.8502 27.0483C17.0549 27.0483 17.2514 26.9674 17.3968 26.8233C17.5414 26.6781 17.6225 26.4815 17.6225 26.2766C17.6225 26.0717 17.5414 25.8752 17.3968 25.7299L16.3968 24.7299L17.3968 23.7299C17.536 23.5838 17.6126 23.389 17.6101 23.1872C17.6076 22.9854 17.5264 22.7925 17.3837 22.6498C17.2409 22.5071 17.0481 22.4258 16.8463 22.4233C16.6444 22.4209 16.4497 22.4974 16.3035 22.6366L15.3035 23.6366L14.3035 22.6366C14.2318 22.5647 14.1466 22.5077 14.0528 22.4688C13.9589 22.4299 13.8584 22.4099 13.7568 22.4099V22.4099ZM19.9402 23.9466C19.7351 23.9466 19.5384 24.0281 19.3933 24.1731C19.2483 24.3181 19.1668 24.5148 19.1668 24.7199C19.1668 24.925 19.2483 25.1218 19.3933 25.2668C19.5384 25.4118 19.7351 25.4933 19.9402 25.4933H24.5768C24.7819 25.4933 24.9786 25.4118 25.1237 25.2668C25.2687 25.1218 25.3502 24.925 25.3502 24.7199C25.3502 24.5148 25.2687 24.3181 25.1237 24.1731C24.9786 24.0281 24.7819 23.9466 24.5768 23.9466H19.9402ZM13.7568 14.6799C13.5529 14.6799 13.3572 14.7605 13.2123 14.9041C13.0675 15.0477 12.9853 15.2427 12.9835 15.4466V18.5433C12.9835 18.7484 13.065 18.9451 13.21 19.0901C13.355 19.2351 13.5517 19.3166 13.7568 19.3166H16.8468C17.0519 19.3166 17.2486 19.2351 17.3937 19.0901C17.5387 18.9451 17.6202 18.7484 17.6202 18.5433V15.4466C17.6202 15.2415 17.5387 15.0448 17.3937 14.8998C17.2486 14.7548 17.0519 14.6733 16.8468 14.6733L13.7568 14.6799ZM19.9418 16.2266C19.8374 16.2218 19.733 16.2381 19.635 16.2748C19.5371 16.3114 19.4476 16.3675 19.3719 16.4397C19.2963 16.512 19.2361 16.5988 19.195 16.6949C19.1538 16.791 19.1326 16.8945 19.1326 16.9991C19.1326 17.1037 19.1538 17.2072 19.195 17.3033C19.2361 17.3995 19.2963 17.4863 19.3719 17.5585C19.4476 17.6307 19.5371 17.6868 19.635 17.7235C19.733 17.7601 19.8374 17.7765 19.9418 17.7716H24.5768C24.6813 17.7765 24.7857 17.7601 24.8836 17.7235C24.9816 17.6868 25.0711 17.6307 25.1467 17.5585C25.2224 17.4863 25.2826 17.3995 25.3237 17.3033C25.3648 17.2072 25.3861 17.1037 25.3861 16.9991C25.3861 16.8945 25.3648 16.791 25.3237 16.6949C25.2826 16.5988 25.2224 16.512 25.1467 16.4397C25.0711 16.3675 24.9816 16.3114 24.8836 16.2748C24.7857 16.2381 24.6813 16.2218 24.5768 16.2266H19.9418ZM16.0768 17.7716H14.5302V16.2266H16.0752V17.7699L16.0768 17.7716Z"
                fill={fill}
            />
            <path
                d="M26.8947 8.09167H22.2581C22.2581 7.68566 22.1781 7.28364 22.0227 6.90854C21.8674 6.53344 21.6396 6.19262 21.3525 5.90553C21.0654 5.61844 20.7246 5.39071 20.3495 5.23534C19.9744 5.07997 19.5724 5 19.1664 5C18.7604 5 18.3584 5.07997 17.9833 5.23534C17.6082 5.39071 17.2673 5.61844 16.9803 5.90553C16.6932 6.19262 16.4654 6.53344 16.3101 6.90854C16.1547 7.28364 16.0747 7.68566 16.0747 8.09167H11.4364C10.6167 8.09255 9.83084 8.41856 9.25123 8.99817C8.67162 9.57778 8.34561 10.3636 8.34473 11.1833V31.28C8.34561 32.0997 8.67162 32.8856 9.25123 33.4652C9.83084 34.0448 10.6167 34.3708 11.4364 34.3717H26.8947C27.7144 34.3708 28.5003 34.0448 29.0799 33.4652C29.6595 32.8856 29.9855 32.0997 29.9864 31.28V11.1833C29.9855 10.3636 29.6595 9.57778 29.0799 8.99817C28.5003 8.41856 27.7144 8.09255 26.8947 8.09167V8.09167ZM28.4414 31.28C28.4414 31.6902 28.2784 32.0836 27.9884 32.3737C27.6983 32.6637 27.3049 32.8267 26.8947 32.8267H11.4364C11.0262 32.8267 10.6328 32.6637 10.3427 32.3737C10.0527 32.0836 9.88973 31.6902 9.88973 31.28V11.1833C9.88973 10.7731 10.0527 10.3797 10.3427 10.0897C10.6328 9.79962 11.0262 9.63667 11.4364 9.63667H12.9831V11.1833H25.3447V9.63833H26.8914C27.3016 9.63833 27.695 9.80129 27.9851 10.0913C28.2751 10.3814 28.4381 10.7748 28.4381 11.185L28.4414 31.28Z"
                fill={fill}
            />
        </svg>
    );
};
