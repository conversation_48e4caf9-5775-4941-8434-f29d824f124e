import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CheckShieldIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.1919 5.23802C11.9021 5.08123 12.6379 5.08124 13.3481 5.23806L17.994 6.264C18.1481 6.29769 18.2861 6.38294 18.3852 6.50567C18.4843 6.6284 18.5385 6.78126 18.539 6.939V13.851C18.5383 14.5358 18.368 15.2098 18.0433 15.8127C17.7185 16.4156 17.2495 16.9287 16.678 17.306L13.3745 19.4943C12.705 19.9379 11.835 19.9379 11.1655 19.4943L7.862 17.306C7.29043 16.9289 6.8212 16.4161 6.49627 15.8134C6.17135 15.2106 6.00084 14.5367 6 13.852V6.939C6.00046 6.78126 6.05472 6.6284 6.15381 6.50567C6.2529 6.38294 6.3909 6.29769 6.545 6.264L11.1919 5.23802ZM15.8605 10.488C15.5891 10.218 15.1507 10.2178 14.8791 10.4875L12.2754 13.0731C12.0804 13.2667 11.7656 13.2667 11.5707 13.073L10.4425 11.9523C10.1717 11.6832 9.7344 11.6833 9.46361 11.9524C9.1904 12.224 9.19044 12.666 9.4637 12.9375L11.2195 14.6821C11.6094 15.0694 12.2388 15.0696 12.6288 14.6824L15.8599 11.4754C16.1339 11.2034 16.1342 10.7603 15.8605 10.488Z"
                fill={fill}
            />
        </svg>
    );
};
