import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AlignCenterIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            className={className}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.269 9.25999C19.269 9.45042 19.1934 9.63304 19.0587 9.76769C18.9241 9.90235 18.7415 9.97799 18.551 9.97799H5.62303C5.4326 9.97799 5.24998 9.90235 5.11533 9.76769C4.98068 9.63304 4.90503 9.45042 4.90503 9.25999C4.90503 9.06957 4.98068 8.88694 5.11533 8.75229C5.24998 8.61764 5.4326 8.54199 5.62303 8.54199H18.551C18.7415 8.54199 18.9241 8.61764 19.0587 8.75229C19.1934 8.88694 19.269 9.06957 19.269 9.25999Z"
                fill={fill}
            />
            <path
                d="M17.1151 6.38695C17.1151 6.57737 17.0394 6.76 16.9048 6.89465C16.7701 7.0293 16.5875 7.10495 16.3971 7.10495H7.77806C7.58763 7.10495 7.40501 7.0293 7.27036 6.89465C7.13571 6.76 7.06006 6.57737 7.06006 6.38695C7.06006 6.19652 7.13571 6.01389 7.27036 5.87924C7.40501 5.74459 7.58763 5.66895 7.77806 5.66895H16.3961C16.4904 5.66881 16.5839 5.68729 16.6711 5.72331C16.7584 5.75934 16.8376 5.8122 16.9044 5.87889C16.9712 5.94557 17.0242 6.02477 17.0603 6.11195C17.0965 6.19913 17.1151 6.29257 17.1151 6.38695Z"
                fill={fill}
            />
            <path
                d="M19.269 15.0051C19.269 15.1955 19.1934 15.3782 19.0587 15.5128C18.9241 15.6475 18.7415 15.7231 18.551 15.7231H5.62303C5.4326 15.7231 5.24998 15.6475 5.11533 15.5128C4.98068 15.3782 4.90503 15.1955 4.90503 15.0051C4.90503 14.8147 4.98068 14.6321 5.11533 14.4974C5.24998 14.3628 5.4326 14.2871 5.62303 14.2871H18.551C18.7415 14.2871 18.9241 14.3628 19.0587 14.4974C19.1934 14.6321 19.269 14.8147 19.269 15.0051Z"
                fill={fill}
            />
            <path
                d="M17.1151 12.133C17.1151 12.3235 17.0394 12.5061 16.9048 12.6407C16.7701 12.7754 16.5875 12.851 16.3971 12.851H7.77806C7.58763 12.851 7.40501 12.7754 7.27036 12.6407C7.13571 12.5061 7.06006 12.3235 7.06006 12.133C7.06006 11.9426 7.13571 11.76 7.27036 11.6253C7.40501 11.4907 7.58763 11.415 7.77806 11.415H16.3961C16.4904 11.4149 16.5839 11.4334 16.6711 11.4694C16.7584 11.5054 16.8376 11.5583 16.9044 11.625C16.9712 11.6917 17.0242 11.7709 17.0603 11.858C17.0965 11.9452 17.1151 12.0387 17.1151 12.133Z"
                fill={fill}
            />
            <path
                d="M17.1151 17.8782C17.1151 18.0686 17.0394 18.2512 16.9048 18.3859C16.7701 18.5205 16.5875 18.5962 16.3971 18.5962H7.77806C7.58763 18.5962 7.40501 18.5205 7.27036 18.3859C7.13571 18.2512 7.06006 18.0686 7.06006 17.8782C7.06006 17.7839 7.07863 17.6905 7.11471 17.6034C7.1508 17.5163 7.20368 17.4371 7.27036 17.3705C7.33703 17.3038 7.41618 17.2509 7.50329 17.2148C7.5904 17.1787 7.68377 17.1602 7.77806 17.1602H16.3961C16.4904 17.16 16.5839 17.1785 16.6711 17.2145C16.7584 17.2505 16.8376 17.3034 16.9044 17.3701C16.9712 17.4368 17.0242 17.516 17.0603 17.6032C17.0965 17.6903 17.1151 17.7838 17.1151 17.8782Z"
                fill={fill}
            />
        </svg>
    );
};
