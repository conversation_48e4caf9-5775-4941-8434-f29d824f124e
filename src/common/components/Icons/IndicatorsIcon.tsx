import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const IndicatorsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10 5H6C5.44772 5 5 5.44772 5 6V13C5 13.5523 5.44772 14 6 14H10C10.5523 14 11 13.5523 11 13V6C11 5.44772 10.5523 5 10 5Z"
                fill={fill}
            />
            <path
                d="M13 19H18C18.5523 19 19 18.5523 19 18V11C19 10.4477 18.5523 10 18 10H13C12.4477 10 12 10.4477 12 11V18C12 18.5523 12.4477 19 13 19Z"
                fill={fill}
            />
            <path
                d="M10 15H6C5.44772 15 5 15.4477 5 16V18C5 18.5523 5.44772 19 6 19H10C10.5523 19 11 18.5523 11 18V16C11 15.4477 10.5523 15 10 15Z"
                fill={fill}
            />
            <path
                d="M13 9H18C18.5523 9 19 8.55228 19 8V6C19 5.44772 18.5523 5 18 5L13 5C12.4477 5 12 5.44772 12 6V8C12 8.55228 12.4477 9 13 9Z"
                fill={fill}
            />
        </svg>
    );
};
