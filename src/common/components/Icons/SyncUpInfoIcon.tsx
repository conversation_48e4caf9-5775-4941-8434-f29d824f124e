import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SyncUpInfoIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 15 15"
            fill="none"
            className={className}
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M2.8225 2.23029C2.69124 1.99397 2.74217 1.69603 2.95706 1.53202C4.25845 0.538728 5.8543 -0.00228858 7.5 7.27699e-06C11.6422 7.27699e-06 15 3.35775 15 7.5C15 8.77557 14.6814 9.97696 14.12 11.0285C13.9213 11.4005 13.4043 11.3763 13.1994 11.0077L11.7218 8.34901C11.5102 7.96814 11.7856 7.5 12.2213 7.5H12.9286C13.2442 7.5 13.5028 7.24361 13.4729 6.92946C13.3798 5.95446 13.0491 5.01394 12.506 4.19195C11.8576 3.21054 10.9349 2.44137 9.85288 1.9801C8.77082 1.51882 7.57704 1.38578 6.41997 1.59752C5.50707 1.76457 4.6488 2.14043 3.91046 2.69168C3.61012 2.91592 3.16684 2.85024 2.98484 2.52258L2.8225 2.23029ZM12.1775 12.7697C12.3088 13.006 12.2578 13.304 12.0429 13.468C10.7415 14.4613 9.1457 15.0023 7.5 15C3.35775 15 0 11.6422 0 7.5C0 6.22443 0.318582 5.02304 0.880024 3.97154C1.07868 3.59949 1.59572 3.62365 1.8006 3.99231L3.27816 6.65099C3.48983 7.03186 3.21443 7.5 2.77868 7.5H2.07138C1.75581 7.5 1.49716 7.75639 1.52714 8.07054C1.62019 9.04554 1.95089 9.98606 2.494 10.8081C3.14244 11.7895 4.06505 12.5586 5.14712 13.0199C6.22918 13.4812 7.42296 13.6142 8.58003 13.4025C9.49293 13.2354 10.3512 12.8596 11.0895 12.3083C11.3899 12.0841 11.8332 12.1498 12.0152 12.4774L12.1775 12.7697Z"
                fill={fill}
                fill-opacity="0.7"
            />
            <path
                d="M7.60224 3.6875C7.50457 3.68748 7.40792 3.71384 7.31823 3.76498C7.22854 3.81611 7.1477 3.89093 7.08068 3.98486C7.01365 4.07878 6.96185 4.18982 6.92845 4.31116C6.89506 4.4325 6.88077 4.56158 6.88648 4.69049L7.06551 8.71349C7.07349 8.89459 7.13353 9.06481 7.23335 9.1893C7.33316 9.31379 7.4652 9.38314 7.60243 9.38314C7.73965 9.38314 7.87169 9.31379 7.97151 9.1893C8.07132 9.06481 8.13136 8.89459 8.13934 8.71349L8.31837 4.69049C8.32429 4.56152 8.31015 4.43232 8.27684 4.31085C8.24352 4.18939 8.19173 4.07823 8.12466 3.98425C8.05759 3.89027 7.97667 3.81545 7.88688 3.76441C7.79709 3.71338 7.70034 3.6872 7.60261 3.6875H7.60224Z"
                fill={fill}
                fill-opacity="0.7"
            />
            <path
                d="M7.35617 10.0743C7.47333 10.0744 7.58683 10.1283 7.67733 10.2266C7.76783 10.325 7.82974 10.4618 7.85252 10.6137C7.8753 10.7656 7.85753 10.9233 7.80225 11.0599C7.74697 11.1964 7.65758 11.3034 7.54933 11.3626C7.44107 11.4219 7.32063 11.4296 7.20852 11.3846C7.09641 11.3397 6.99957 11.2447 6.93448 11.1159C6.86939 10.9871 6.84009 10.8325 6.85155 10.6783C6.86302 10.5242 6.91455 10.3801 6.99738 10.2705C7.04398 10.2074 7.09982 10.1574 7.16151 10.1236C7.22319 10.0899 7.28942 10.0731 7.35617 10.0743Z"
                fill={fill}
                fill-opacity="0.7"
            />
        </svg>
    );
};
