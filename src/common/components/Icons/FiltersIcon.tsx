import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const FiltersIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.01303 17.4349C6.96983 17.443 6.92597 17.447 6.88203 17.4469H4.72003C4.5304 17.4469 4.34854 17.3716 4.21445 17.2375C4.08036 17.1034 4.00503 16.9215 4.00503 16.7319C4.00503 16.5423 4.08036 16.3604 4.21445 16.2263C4.34854 16.0922 4.5304 16.0169 4.72003 16.0169H6.88203C6.92588 16.0158 6.96974 16.0188 7.01303 16.0259C7.1599 15.6057 7.43355 15.2415 7.79623 14.9835C8.15891 14.7254 8.59274 14.5863 9.03784 14.5853C9.48294 14.5844 9.91738 14.7215 10.2812 14.978C10.645 15.2344 10.9203 15.5974 11.069 16.0169H19.13C19.3199 16.0169 19.502 16.0923 19.6363 16.2266C19.7706 16.3609 19.846 16.543 19.846 16.7329C19.846 16.9228 19.7706 17.1049 19.6363 17.2392C19.502 17.3735 19.3199 17.4489 19.13 17.4489H11.069C10.92 17.8683 10.6444 18.2311 10.2804 18.4873C9.91642 18.7434 9.48188 18.8803 9.03678 18.879C8.59168 18.8777 8.15795 18.7382 7.79546 18.4799C7.43297 18.2216 7.15958 17.8572 7.01303 17.4369V17.4349ZM12.78 13.1549H4.72003C4.626 13.1549 4.5329 13.1364 4.44603 13.1004C4.35916 13.0644 4.28023 13.0117 4.21374 12.9452C4.14725 12.8787 4.09451 12.7998 4.05853 12.7129C4.02255 12.626 4.00403 12.5329 4.00403 12.4389C4.00403 12.3449 4.02255 12.2518 4.05853 12.1649C4.09451 12.078 4.14725 11.9991 4.21374 11.9326C4.28023 11.8661 4.35916 11.8134 4.44603 11.7774C4.5329 11.7414 4.626 11.7229 4.72003 11.7229H12.781C12.9297 11.3044 13.2042 10.9423 13.567 10.6861C13.9298 10.43 14.363 10.2925 14.807 10.2925C15.2511 10.2925 15.6843 10.43 16.0471 10.6861C16.4098 10.9423 16.6844 11.3044 16.833 11.7229H19.133C19.3229 11.7229 19.505 11.7983 19.6393 11.9326C19.7736 12.0669 19.849 12.249 19.849 12.4389C19.849 12.6288 19.7736 12.8109 19.6393 12.9452C19.505 13.0795 19.3229 13.1549 19.133 13.1549H16.833C16.6844 13.5734 16.4098 13.9356 16.0471 14.1917C15.6843 14.4478 15.2511 14.5853 14.807 14.5853C14.363 14.5853 13.9298 14.4478 13.567 14.1917C13.2042 13.9356 12.9297 13.5734 12.781 13.1549H12.78ZM8.45803 8.86191H4.72003C4.626 8.86191 4.5329 8.84339 4.44603 8.80741C4.35916 8.77143 4.28023 8.71869 4.21374 8.6522C4.14725 8.58571 4.09451 8.50678 4.05853 8.41991C4.02255 8.33304 4.00403 8.23994 4.00403 8.14591C4.00403 8.05189 4.02255 7.95878 4.05853 7.87191C4.09451 7.78504 4.14725 7.70611 4.21374 7.63962C4.28023 7.57314 4.35916 7.5204 4.44603 7.48441C4.5329 7.44843 4.626 7.42991 4.72003 7.42991H8.45803C8.60666 7.01145 8.88122 6.64927 9.244 6.39315C9.60677 6.13702 10.04 5.99951 10.484 5.99951C10.9281 5.99951 11.3613 6.13702 11.7241 6.39315C12.0868 6.64927 12.3614 7.01145 12.51 7.42991H19.13C19.2241 7.42991 19.3172 7.44843 19.404 7.48441C19.4909 7.5204 19.5698 7.57314 19.6363 7.63962C19.7028 7.70611 19.7555 7.78504 19.7915 7.87191C19.8275 7.95878 19.846 8.05189 19.846 8.14591C19.846 8.23994 19.8275 8.33304 19.7915 8.41991C19.7555 8.50678 19.7028 8.58571 19.6363 8.6522C19.5698 8.71869 19.4909 8.77143 19.404 8.80741C19.3172 8.84339 19.2241 8.86191 19.13 8.86191H12.51C12.3614 9.28038 12.0868 9.64255 11.7241 9.89868C11.3613 10.1548 10.9281 10.2923 10.484 10.2923C10.04 10.2923 9.60677 10.1548 9.244 9.89868C8.88122 9.64255 8.60666 9.28038 8.45803 8.86191Z"
                fill={fill}
            />
        </svg>
    );
};
