import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const FolderOpenIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 18 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M16.1193 8.1875C16.101 8.05167 16.0811 7.91601 16.0595 7.78054L16.0114 7.47946C15.8425 6.42095 14.9294 5.64209 13.8575 5.64209H7.76978C7.544 4.83887 6.80616 4.25 5.93078 4.25H4.2121C3.09998 4.25 2.16324 5.08099 2.03064 6.18517L1.82628 7.88701C1.60679 9.71478 1.66344 11.5652 1.99431 13.3761C2.17133 14.3449 2.98158 15.0711 3.96392 15.1414L5.09943 15.2226C7.69872 15.4086 10.308 15.4086 12.9073 15.2226L14.1357 15.1347C14.7421 15.0913 15.2791 14.7833 15.6249 14.3195C16.0852 13.878 16.4474 13.3388 16.6817 12.7362L17.6188 10.3266C18.019 9.29737 17.2598 8.1875 16.1555 8.1875H16.1193ZM4.2121 5.375C3.66942 5.375 3.21232 5.7805 3.14762 6.31931L2.94325 8.02114C2.73722 9.73692 2.79039 11.4739 3.10099 13.1739C3.18576 13.6378 3.57378 13.9856 4.0442 14.0192L4.10871 14.0239L5.85777 9.50545C6.16526 8.71111 6.92942 8.1875 7.78119 8.1875H14.9834C14.9724 8.11089 14.9607 8.03434 14.9485 7.95786L14.9005 7.65678C14.8187 7.14423 14.3765 6.76709 13.8575 6.76709L7.32287 6.76709C6.98762 6.76709 6.71585 6.49532 6.71585 6.16007C6.71585 5.72649 6.36436 5.375 5.93078 5.375H4.2121Z"
                fill={fill}
            />
        </svg>
    );
};
