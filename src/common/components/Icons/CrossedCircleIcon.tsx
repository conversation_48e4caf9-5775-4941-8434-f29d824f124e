import { Icon, IconProps } from './Icon';

export const CrossedCircleIcon: typeof Icon = ({}: IconProps) => {
    return (
        <svg
            style={{ margin: 4.5 }}
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="6" cy="6" r="5.5" fill="white" stroke="#C9CDD3" />
            <line x1="1.67075" y1="9.62371" x2="9.67075" y2="2.62371" stroke="#C9CDD3" />
        </svg>
    );
};
