import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const VolumeIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.276 5.04687C13.1718 5.0056 13.0588 4.99184 12.9477 5.0069C12.8367 5.02196 12.7314 5.06534 12.642 5.13287L8.873 8.18287H5.6C5.44143 8.18469 5.28988 8.24849 5.17775 8.36062C5.06562 8.47275 5.00182 8.6243 5 8.78287V14.3689C5.00182 14.5274 5.06562 14.679 5.17775 14.7911C5.28988 14.9033 5.44143 14.9671 5.6 14.9689H8.89L12.659 18.0189C12.7732 18.1017 12.912 18.144 13.053 18.1389C13.1455 18.1286 13.237 18.1116 13.327 18.0879C13.4284 18.0424 13.5138 17.9674 13.5719 17.8727C13.6301 17.7779 13.6583 17.6679 13.653 17.5569V5.57787C13.6287 5.4687 13.5829 5.36549 13.5181 5.2743C13.4534 5.18311 13.3711 5.10579 13.276 5.04687Z"
                fill={fill}
            />
            <path
                d="M16.463 6.77801C16.4061 6.83218 16.3608 6.89734 16.3299 6.96954C16.2989 7.04173 16.283 7.11946 16.283 7.19801C16.283 7.27656 16.2989 7.35429 16.3299 7.42648C16.3608 7.49868 16.4061 7.56384 16.463 7.61801C17.4809 8.6767 18.0462 10.0904 18.039 11.559C18.0398 13.0264 17.4754 14.4378 16.463 15.5C16.3556 15.6135 16.2957 15.7638 16.2957 15.92C16.2957 16.0762 16.3556 16.2265 16.463 16.34C16.5168 16.3943 16.5808 16.4373 16.6514 16.4667C16.7219 16.4961 16.7975 16.5112 16.874 16.5112C16.9504 16.5112 17.026 16.4961 17.0966 16.4667C17.1671 16.4373 17.2311 16.3943 17.285 16.34C18.5272 15.0603 19.2165 13.3435 19.204 11.56C19.2085 9.77808 18.5204 8.06412 17.285 6.78001C17.1751 6.67284 17.0278 6.61268 16.8744 6.61231C16.7209 6.61193 16.5733 6.67137 16.463 6.77801Z"
                fill={fill}
            />
            <path
                d="M15.811 8.26809C15.756 8.21201 15.6905 8.16739 15.6182 8.1368C15.5459 8.1062 15.4682 8.09025 15.3897 8.08985C15.3112 8.08945 15.2334 8.10463 15.1608 8.13449C15.0882 8.16435 15.0222 8.20831 14.9667 8.26382C14.9112 8.31934 14.8672 8.38531 14.8374 8.45792C14.8075 8.53053 14.7923 8.60833 14.7927 8.68684C14.7931 8.76535 14.8091 8.84301 14.8397 8.91531C14.8703 8.98761 14.9149 9.05314 14.971 9.10809C15.6183 9.75918 15.9816 10.64 15.9816 11.5581C15.9816 12.4762 15.6183 13.357 14.971 14.0081C14.888 14.0902 14.8312 14.1951 14.8078 14.3094C14.7843 14.4238 14.7953 14.5425 14.8392 14.6507C14.8832 14.7588 14.9582 14.8515 15.0548 14.9171C15.1514 14.9827 15.2652 15.0181 15.382 15.0191C15.4585 15.0204 15.5346 15.0059 15.6053 14.9764C15.676 14.947 15.7399 14.9033 15.793 14.8481C16.2303 14.4153 16.5769 13.8997 16.8125 13.3313C17.048 12.763 17.1679 12.1533 17.165 11.5381C17.172 10.9298 17.0557 10.3264 16.8229 9.76439C16.5902 9.20235 16.246 8.69333 15.811 8.26809Z"
                fill={fill}
            />
        </svg>
    );
};
