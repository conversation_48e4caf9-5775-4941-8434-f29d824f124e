import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CloneIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.2837 16.9555C9.78663 16.9549 9.3101 16.7572 8.95863 16.4058C8.60717 16.0543 8.40948 15.5778 8.40894 15.0807V5.87577C8.40921 5.37854 8.60678 4.90175 8.95827 4.55006C9.30977 4.19837 9.78645 4.00054 10.2837 4H16.0797C16.7595 4.00047 17.4115 4.27 17.8931 4.74969L19.2503 6.10691C19.4886 6.34384 19.6775 6.62563 19.8062 6.93601C19.9348 7.24638 20.0007 7.57918 20 7.91517V15.0797C19.9997 15.5769 19.8022 16.0537 19.4507 16.4054C19.0992 16.7571 18.6225 16.9549 18.1253 16.9555H10.2837Z"
                fill={fill}
            />
            <path
                d="M15.0569 20.3645H6.87474C6.3777 20.3639 5.90116 20.1662 5.5497 19.8148C5.19823 19.4633 5.00054 18.9868 5 18.4897V7.57982C5.00054 7.08277 5.19823 6.60624 5.5497 6.25478C5.90116 5.90331 6.3777 5.70562 6.87474 5.70508H7.21533C7.35095 5.70508 7.48103 5.75896 7.57693 5.85486C7.67283 5.95076 7.72671 6.08084 7.72671 6.21646C7.72671 6.35209 7.67283 6.48217 7.57693 6.57807C7.48103 6.67397 7.35095 6.72785 7.21533 6.72785H6.87474C6.64895 6.72839 6.43256 6.81833 6.27291 6.97798C6.11325 7.13764 6.02331 7.35403 6.02277 7.57982V18.4897C6.02331 18.7155 6.11325 18.9319 6.27291 19.0916C6.43256 19.2512 6.64895 19.3412 6.87474 19.3417H15.0569C15.2828 19.3414 15.4993 19.2516 15.6591 19.0919C15.8188 18.9322 15.9086 18.7156 15.9089 18.4897V18.1492C15.9089 18.0135 15.9628 17.8835 16.0587 17.7875C16.1546 17.6916 16.2847 17.6378 16.4203 17.6378C16.5559 17.6378 16.686 17.6916 16.7819 17.7875C16.8778 17.8835 16.9317 18.0135 16.9317 18.1492V18.4897C16.9311 18.9868 16.7334 19.4633 16.382 19.8148C16.0305 20.1662 15.554 20.3639 15.0569 20.3645Z"
                fill={fill}
            />
        </svg>
    );
};
