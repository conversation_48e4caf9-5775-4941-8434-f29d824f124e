import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const TutorialsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.5351 19.072C11.2644 19.0718 10.0145 18.7504 8.90143 18.1376C7.78836 17.5248 6.84831 16.6405 6.16867 15.5669C5.48903 14.4934 5.09187 13.2654 5.01412 11.9971C4.93637 10.7289 5.18055 9.46161 5.72396 8.31306C6.26737 7.16451 7.09236 6.172 8.12223 5.42782C9.15211 4.68363 10.3534 4.21192 11.6145 4.05655C12.8756 3.90118 14.1555 4.0672 15.3352 4.53916C16.5149 5.01111 17.5561 5.7737 18.3621 6.756C18.4398 6.83526 18.5002 6.92983 18.5394 7.0337C18.5786 7.13758 18.5958 7.24847 18.5898 7.35934C18.5838 7.4702 18.5548 7.57861 18.5047 7.67766C18.4545 7.77671 18.3843 7.86423 18.2985 7.93467C18.2126 8.0051 18.1131 8.05689 18.0062 8.08676C17.8992 8.11662 17.7872 8.1239 17.6773 8.10813C17.5674 8.09236 17.462 8.05388 17.3678 7.99515C17.2736 7.93643 17.1926 7.85873 17.1301 7.767C16.3325 6.79493 15.2472 6.1009 14.0302 5.78477C12.8133 5.46864 11.5274 5.54671 10.3576 6.00774C9.18778 6.46877 8.19439 7.28899 7.52031 8.3504C6.84624 9.41181 6.52624 10.6597 6.60635 11.9145C6.68647 13.1693 7.16256 14.3664 7.96615 15.3334C8.76973 16.3005 9.85937 16.9877 11.0783 17.2963C12.2972 17.6048 13.5826 17.5187 14.7495 17.0503C15.9164 16.582 16.9046 15.7556 17.5721 14.69C17.6929 14.5307 17.869 14.4225 18.0656 14.3866C18.2623 14.3506 18.4652 14.3896 18.6346 14.4959C18.8039 14.6021 18.9273 14.7679 18.9805 14.9606C19.0337 15.1533 19.0129 15.3589 18.9221 15.537C18.2467 16.6214 17.3054 17.5154 16.1877 18.134C15.0699 18.7527 13.8126 19.0755 12.5351 19.072Z"
                fill={fill}
            />
            <path
                d="M15.8114 11.2058C15.8944 11.2474 15.9641 11.3112 16.0129 11.3902C16.0616 11.4691 16.0874 11.56 16.0874 11.6528C16.0874 11.7456 16.0616 11.8365 16.0129 11.9155C15.9641 11.9944 15.8944 12.0583 15.8114 12.0998L11.2274 14.3918C11.1512 14.43 11.0665 14.4481 10.9813 14.4443C10.8961 14.4405 10.8133 14.415 10.7408 14.3703C10.6682 14.3255 10.6083 14.2629 10.5668 14.1884C10.5252 14.1139 10.5034 14.0301 10.5034 13.9448V9.36082C10.5034 9.27556 10.5252 9.19171 10.5668 9.11725C10.6083 9.04279 10.6682 8.98019 10.7408 8.93539C10.8133 8.8906 10.8961 8.86511 10.9813 8.86133C11.0665 8.85755 11.1512 8.87562 11.2274 8.91382L15.8114 11.2058Z"
                fill={fill}
            />
        </svg>
    );
};
