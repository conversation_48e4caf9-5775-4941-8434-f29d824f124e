import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PhoneInCircle: typeof Icon = ({
    fill = '#36CE91',
    size = IconSize.S,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size, flexShrink: 0 }}
            height={size}
            viewBox="0 0 13 13"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="6.13281" cy="6.41113" r="5.5" fill={fill} stroke={fill} />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.08531 3.35519C5.01881 3.13319 4.95231 2.95519 4.73031 2.93369L4.30881 2.91169C3.86531 2.88919 3.13281 3.55519 3.13281 4.66469C3.15481 6.59519 6.03981 9.74619 7.94831 9.92369C9.07981 10.0347 9.79031 9.36869 9.81231 8.90319V8.50319C9.83431 8.28119 9.65681 8.19269 9.43531 8.10319C9.01381 7.94769 8.59231 7.74819 8.17031 7.59269C8.09279 7.575 8.01219 7.57583 7.93505 7.59512C7.8579 7.6144 7.78639 7.6516 7.72631 7.70369C7.58246 7.8177 7.44863 7.94383 7.32631 8.08069C7.21531 8.16969 7.10431 8.21369 6.99331 8.10269C6.03377 7.60791 5.27938 6.79068 4.86281 5.79469C4.84329 5.76916 4.82944 5.73976 4.82218 5.70845C4.81492 5.67715 4.81441 5.64465 4.82071 5.61314C4.827 5.58162 4.83994 5.55181 4.85867 5.5257C4.8774 5.49958 4.90148 5.47776 4.92931 5.46169C5.08481 5.32869 5.19581 5.21769 5.32931 5.08469C5.38911 5.03358 5.4348 4.968 5.46202 4.89421C5.48924 4.82042 5.49709 4.74088 5.48481 4.66319C5.35181 4.24319 5.21881 3.77719 5.08531 3.35519Z"
                fill="white"
            />
        </svg>
    );
};
