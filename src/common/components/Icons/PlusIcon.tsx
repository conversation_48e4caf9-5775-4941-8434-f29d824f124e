import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PlusIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className = '',
    style,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size, ...(style ?? {}) }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.0004 5C11.6822 5 11.377 5.12642 11.152 5.35145C10.927 5.57648 10.8006 5.88167 10.8006 6.19991V10.8001H6.19985C5.88163 10.8001 5.57644 10.9265 5.35143 11.1515C5.12641 11.3765 5 11.6817 5 12C5 12.3182 5.12641 12.6235 5.35143 12.8485C5.57644 13.0735 5.88163 13.1999 6.19985 13.1999H10.7997V17.8001C10.7997 18.1183 10.9261 18.4235 11.1511 18.6485C11.3761 18.8736 11.6813 19 11.9996 19C12.3178 19 12.623 18.8736 12.848 18.6485C13.073 18.4235 13.1994 18.1183 13.1994 17.8001V13.1999H17.8002C18.1184 13.1999 18.4236 13.0735 18.6486 12.8485C18.8736 12.6235 19 12.3182 19 12C19 11.6817 18.8736 11.3765 18.6486 11.1515C18.4236 10.9265 18.1184 10.8001 17.8002 10.8001H13.2003V6.19991C13.2003 5.88167 13.0739 5.57648 12.8489 5.35145C12.6239 5.12642 12.3187 5 12.0004 5Z"
                fill={fill}
            />
        </svg>
    );
};
