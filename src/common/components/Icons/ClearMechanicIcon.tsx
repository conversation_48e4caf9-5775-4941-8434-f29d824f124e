import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ClearMechanicIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.7931 7.21692L17.1331 8.87592C16.9668 9.03839 16.7435 9.12935 16.5111 9.12935C16.2786 9.12935 16.0553 9.03839 15.8891 8.87592L15.1211 8.10792C14.9586 7.94164 14.8676 7.71839 14.8676 7.48592C14.8676 7.25344 14.9586 7.03019 15.1211 6.86392L16.7801 5.20492C16.1762 4.98168 15.521 4.93567 14.8919 5.07232C14.2627 5.20897 13.6857 5.52259 13.2288 5.97623C12.7719 6.42986 12.4541 7.00462 12.313 7.63281C12.1718 8.26099 12.2131 8.91643 12.4321 9.52192L5.42606 16.5269C5.291 16.6605 5.18378 16.8196 5.11061 16.995C5.03743 17.1703 4.99976 17.3584 4.99976 17.5484C4.99976 17.7384 5.03743 17.9265 5.11061 18.1019C5.18378 18.2772 5.291 18.4363 5.42606 18.5699V18.5699C5.55969 18.705 5.71878 18.8122 5.89412 18.8854C6.06946 18.9585 6.25756 18.9962 6.44756 18.9962C6.63756 18.9962 6.82566 18.9585 7.001 18.8854C7.17634 18.8122 7.33543 18.705 7.46906 18.5699L14.4591 11.5639C15.0651 11.7819 15.7206 11.8228 16.349 11.6818C16.9774 11.5408 17.5526 11.2237 18.0073 10.7676C18.4621 10.3115 18.7774 9.73533 18.9166 9.10652C19.0557 8.4777 19.0129 7.82226 18.7931 7.21692V7.21692Z"
                fill={fill}
            />
        </svg>
    );
};
