import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CalendarIconTransparent: typeof Icon = ({
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.653 2.86357H16.2703V2.18354C16.2703 1.86964 16.1357 1.56861 15.896 1.34665C15.6563 1.12469 15.3312 1 14.9922 1C14.6533 1 14.3282 1.12469 14.0885 1.34665C13.8488 1.56861 13.7141 1.86964 13.7141 2.18354V2.8622H7.26361V2.18354C7.26361 1.86964 7.12895 1.56861 6.88926 1.34665C6.64957 1.12469 6.32449 1 5.98552 1C5.64655 1 5.32146 1.12469 5.08177 1.34665C4.84208 1.56861 4.70743 1.86964 4.70743 2.18354V2.8622H3.32474C2.70865 2.86437 2.11848 3.09207 1.68297 3.49561C1.24746 3.89916 1.00195 4.44582 1 5.01633V6.11538H20V5.01633C19.9965 4.44282 19.7474 3.89404 19.3073 3.49043C18.8673 3.08683 18.2723 2.86138 17.653 2.86357V2.86357Z"
                stroke="#5C6477"
            />
            <mask
                id="path-2-outside-1_121_1965"
                maskUnits="userSpaceOnUse"
                x="0.730713"
                y="5.84619"
                width="20"
                height="14"
                fill="black"
            >
                <rect fill="white" x="0.730713" y="5.84619" width="20" height="14" />
                <path d="M1.73071 16.6712C1.73216 17.2436 1.96001 17.7922 2.3645 18.1972C2.769 18.6022 3.31729 18.8307 3.8897 18.8329H17.2156C17.7875 18.8308 18.3354 18.6026 18.7398 18.1982C19.1442 17.7938 19.3724 17.2459 19.3746 16.6739V6.84619H1.73071V16.6712ZM13.3041 10.5235C13.3041 10.475 13.3136 10.427 13.3322 10.3822C13.3507 10.3374 13.3779 10.2967 13.4122 10.2624C13.4465 10.2281 13.4872 10.2009 13.532 10.1824C13.5768 10.1638 13.6248 10.1543 13.6733 10.1543H15.4438C15.4923 10.1543 15.5403 10.1638 15.5851 10.1824C15.6299 10.2009 15.6706 10.2281 15.7049 10.2624C15.7391 10.2967 15.7663 10.3374 15.7849 10.3822C15.8035 10.427 15.813 10.475 15.813 10.5235V11.6905C15.813 11.739 15.8035 11.787 15.7849 11.8318C15.7663 11.8766 15.7391 11.9173 15.7049 11.9516C15.6706 11.9859 15.6299 12.0131 15.5851 12.0316C15.5403 12.0502 15.4923 12.0597 15.4438 12.0597H13.6761C13.5781 12.0597 13.4842 12.0208 13.415 11.9516C13.3457 11.8823 13.3068 11.7884 13.3068 11.6905L13.3041 10.5235ZM13.3041 13.9859C13.3041 13.9374 13.3136 13.8894 13.3322 13.8446C13.3507 13.7998 13.3779 13.7591 13.4122 13.7248C13.4465 13.6905 13.4872 13.6633 13.532 13.6447C13.5768 13.6262 13.6248 13.6166 13.6733 13.6166H15.4438C15.4923 13.6166 15.5403 13.6262 15.5851 13.6447C15.6299 13.6633 15.6706 13.6905 15.7049 13.7248C15.7391 13.7591 15.7663 13.7998 15.7849 13.8446C15.8035 13.8894 15.813 13.9374 15.813 13.9859V15.1529C15.813 15.2014 15.8035 15.2494 15.7849 15.2942C15.7663 15.339 15.7391 15.3797 15.7049 15.414C15.6706 15.4482 15.6299 15.4755 15.5851 15.494C15.5403 15.5126 15.4923 15.5221 15.4438 15.5221H13.6761C13.5781 15.5221 13.4842 15.4832 13.415 15.414C13.3457 15.3447 13.3068 15.2508 13.3068 15.1529L13.3041 13.9859ZM9.27682 10.5235C9.27682 10.475 9.28637 10.427 9.30492 10.3822C9.32348 10.3374 9.35068 10.2967 9.38497 10.2624C9.41925 10.2281 9.45996 10.2009 9.50476 10.1824C9.54956 10.1638 9.59757 10.1543 9.64606 10.1543H11.4165C11.5144 10.1543 11.6084 10.1932 11.6776 10.2624C11.7469 10.3316 11.7858 10.4256 11.7858 10.5235V11.6905C11.7858 11.7884 11.7469 11.8823 11.6776 11.9516C11.6084 12.0208 11.5144 12.0597 11.4165 12.0597H9.64882C9.60033 12.0597 9.55231 12.0502 9.50751 12.0316C9.46272 12.0131 9.42201 11.9859 9.38772 11.9516C9.35343 11.9173 9.32624 11.8766 9.30768 11.8318C9.28912 11.787 9.27957 11.739 9.27957 11.6905L9.27682 10.5235ZM9.27682 13.9859C9.27682 13.9374 9.28637 13.8894 9.30492 13.8446C9.32348 13.7998 9.35068 13.7591 9.38497 13.7248C9.41925 13.6905 9.45996 13.6633 9.50476 13.6447C9.54956 13.6262 9.59757 13.6166 9.64606 13.6166H11.4165C11.5144 13.6166 11.6084 13.6555 11.6776 13.7248C11.7469 13.794 11.7858 13.888 11.7858 13.9859V15.1529C11.7858 15.2508 11.7469 15.3447 11.6776 15.414C11.6084 15.4832 11.5144 15.5221 11.4165 15.5221H9.64882C9.60033 15.5221 9.55231 15.5126 9.50751 15.494C9.46272 15.4755 9.42201 15.4482 9.38772 15.414C9.35343 15.3797 9.32624 15.339 9.30768 15.2942C9.28912 15.2494 9.27957 15.2014 9.27957 15.1529L9.27682 13.9859ZM5.27161 10.5235C5.27161 10.4256 5.31051 10.3316 5.37976 10.2624C5.44901 10.1932 5.54293 10.1543 5.64086 10.1543H7.4113C7.50923 10.1543 7.60315 10.1932 7.6724 10.2624C7.74165 10.3316 7.78055 10.4256 7.78055 10.5235V11.6905C7.78055 11.7884 7.74165 11.8823 7.6724 11.9516C7.60315 12.0208 7.50923 12.0597 7.4113 12.0597H5.64086C5.54293 12.0597 5.44901 12.0208 5.37976 11.9516C5.31051 11.8823 5.27161 11.7884 5.27161 11.6905V10.5235ZM5.27161 13.9859C5.27161 13.888 5.31051 13.794 5.37976 13.7248C5.44901 13.6555 5.54293 13.6166 5.64086 13.6166H7.4113C7.50923 13.6166 7.60315 13.6555 7.6724 13.7248C7.74165 13.794 7.78055 13.888 7.78055 13.9859V15.1529C7.78055 15.2508 7.74165 15.3447 7.6724 15.414C7.60315 15.4832 7.50923 15.5221 7.4113 15.5221H5.64086C5.54293 15.5221 5.44901 15.4832 5.37976 15.414C5.31051 15.3447 5.27161 15.2508 5.27161 15.1529V13.9859Z" />
            </mask>
            <path
                d="M1.73071 16.6712C1.73216 17.2436 1.96001 17.7922 2.3645 18.1972C2.769 18.6022 3.31729 18.8307 3.8897 18.8329H17.2156C17.7875 18.8308 18.3354 18.6026 18.7398 18.1982C19.1442 17.7938 19.3724 17.2459 19.3746 16.6739V6.84619H1.73071V16.6712ZM13.3041 10.5235C13.3041 10.475 13.3136 10.427 13.3322 10.3822C13.3507 10.3374 13.3779 10.2967 13.4122 10.2624C13.4465 10.2281 13.4872 10.2009 13.532 10.1824C13.5768 10.1638 13.6248 10.1543 13.6733 10.1543H15.4438C15.4923 10.1543 15.5403 10.1638 15.5851 10.1824C15.6299 10.2009 15.6706 10.2281 15.7049 10.2624C15.7391 10.2967 15.7663 10.3374 15.7849 10.3822C15.8035 10.427 15.813 10.475 15.813 10.5235V11.6905C15.813 11.739 15.8035 11.787 15.7849 11.8318C15.7663 11.8766 15.7391 11.9173 15.7049 11.9516C15.6706 11.9859 15.6299 12.0131 15.5851 12.0316C15.5403 12.0502 15.4923 12.0597 15.4438 12.0597H13.6761C13.5781 12.0597 13.4842 12.0208 13.415 11.9516C13.3457 11.8823 13.3068 11.7884 13.3068 11.6905L13.3041 10.5235ZM13.3041 13.9859C13.3041 13.9374 13.3136 13.8894 13.3322 13.8446C13.3507 13.7998 13.3779 13.7591 13.4122 13.7248C13.4465 13.6905 13.4872 13.6633 13.532 13.6447C13.5768 13.6262 13.6248 13.6166 13.6733 13.6166H15.4438C15.4923 13.6166 15.5403 13.6262 15.5851 13.6447C15.6299 13.6633 15.6706 13.6905 15.7049 13.7248C15.7391 13.7591 15.7663 13.7998 15.7849 13.8446C15.8035 13.8894 15.813 13.9374 15.813 13.9859V15.1529C15.813 15.2014 15.8035 15.2494 15.7849 15.2942C15.7663 15.339 15.7391 15.3797 15.7049 15.414C15.6706 15.4482 15.6299 15.4755 15.5851 15.494C15.5403 15.5126 15.4923 15.5221 15.4438 15.5221H13.6761C13.5781 15.5221 13.4842 15.4832 13.415 15.414C13.3457 15.3447 13.3068 15.2508 13.3068 15.1529L13.3041 13.9859ZM9.27682 10.5235C9.27682 10.475 9.28637 10.427 9.30492 10.3822C9.32348 10.3374 9.35068 10.2967 9.38497 10.2624C9.41925 10.2281 9.45996 10.2009 9.50476 10.1824C9.54956 10.1638 9.59757 10.1543 9.64606 10.1543H11.4165C11.5144 10.1543 11.6084 10.1932 11.6776 10.2624C11.7469 10.3316 11.7858 10.4256 11.7858 10.5235V11.6905C11.7858 11.7884 11.7469 11.8823 11.6776 11.9516C11.6084 12.0208 11.5144 12.0597 11.4165 12.0597H9.64882C9.60033 12.0597 9.55231 12.0502 9.50751 12.0316C9.46272 12.0131 9.42201 11.9859 9.38772 11.9516C9.35343 11.9173 9.32624 11.8766 9.30768 11.8318C9.28912 11.787 9.27957 11.739 9.27957 11.6905L9.27682 10.5235ZM9.27682 13.9859C9.27682 13.9374 9.28637 13.8894 9.30492 13.8446C9.32348 13.7998 9.35068 13.7591 9.38497 13.7248C9.41925 13.6905 9.45996 13.6633 9.50476 13.6447C9.54956 13.6262 9.59757 13.6166 9.64606 13.6166H11.4165C11.5144 13.6166 11.6084 13.6555 11.6776 13.7248C11.7469 13.794 11.7858 13.888 11.7858 13.9859V15.1529C11.7858 15.2508 11.7469 15.3447 11.6776 15.414C11.6084 15.4832 11.5144 15.5221 11.4165 15.5221H9.64882C9.60033 15.5221 9.55231 15.5126 9.50751 15.494C9.46272 15.4755 9.42201 15.4482 9.38772 15.414C9.35343 15.3797 9.32624 15.339 9.30768 15.2942C9.28912 15.2494 9.27957 15.2014 9.27957 15.1529L9.27682 13.9859ZM5.27161 10.5235C5.27161 10.4256 5.31051 10.3316 5.37976 10.2624C5.44901 10.1932 5.54293 10.1543 5.64086 10.1543H7.4113C7.50923 10.1543 7.60315 10.1932 7.6724 10.2624C7.74165 10.3316 7.78055 10.4256 7.78055 10.5235V11.6905C7.78055 11.7884 7.74165 11.8823 7.6724 11.9516C7.60315 12.0208 7.50923 12.0597 7.4113 12.0597H5.64086C5.54293 12.0597 5.44901 12.0208 5.37976 11.9516C5.31051 11.8823 5.27161 11.7884 5.27161 11.6905V10.5235ZM5.27161 13.9859C5.27161 13.888 5.31051 13.794 5.37976 13.7248C5.44901 13.6555 5.54293 13.6166 5.64086 13.6166H7.4113C7.50923 13.6166 7.60315 13.6555 7.6724 13.7248C7.74165 13.794 7.78055 13.888 7.78055 13.9859V15.1529C7.78055 15.2508 7.74165 15.3447 7.6724 15.414C7.60315 15.4832 7.50923 15.5221 7.4113 15.5221H5.64086C5.54293 15.5221 5.44901 15.4832 5.37976 15.414C5.31051 15.3447 5.27161 15.2508 5.27161 15.1529V13.9859Z"
                stroke="#5C6477"
                stroke-width="2"
                mask="url(#path-2-outside-1_121_1965)"
            />
        </svg>
    );
};
