import { styled } from '@mui/material';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const LightIcon: typeof Icon = ({
    fill = 'var(--cm2)',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <Svg
            className={className}
            minWidth={size}
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M8.49996 9.99984C9.05551 9.99984 9.52774 9.80539 9.91663 9.4165C10.3055 9.02761 10.5 8.55539 10.5 7.99984C10.5 7.44428 10.3055 6.97206 9.91663 6.58317C9.52774 6.19428 9.05551 5.99984 8.49996 5.99984C7.9444 5.99984 7.47218 6.19428 7.08329 6.58317C6.6944 6.97206 6.49996 7.44428 6.49996 7.99984C6.49996 8.55539 6.6944 9.02761 7.08329 9.4165C7.47218 9.80539 7.9444 9.99984 8.49996 9.99984ZM8.49996 11.3332C7.57774 11.3332 6.79163 11.0082 6.14163 10.3582C5.49163 9.70817 5.16663 8.92206 5.16663 7.99984C5.16663 7.07761 5.49163 6.2915 6.14163 5.6415C6.79163 4.9915 7.57774 4.6665 8.49996 4.6665C9.42218 4.6665 10.2083 4.9915 10.8583 5.6415C11.5083 6.2915 11.8333 7.07761 11.8333 7.99984C11.8333 8.92206 11.5083 9.70817 10.8583 10.3582C10.2083 11.0082 9.42218 11.3332 8.49996 11.3332ZM3.83329 8.6665H1.16663V7.33317H3.83329V8.6665ZM15.8333 8.6665H13.1666V7.33317H15.8333V8.6665ZM7.83329 3.33317V0.666504H9.16663V3.33317H7.83329ZM7.83329 15.3332V12.6665H9.16663V15.3332H7.83329ZM4.76663 5.1665L3.08329 3.54984L4.03329 2.5665L5.63329 4.23317L4.76663 5.1665ZM12.9666 13.4332L11.35 11.7498L12.2333 10.8332L13.9166 12.4498L12.9666 13.4332ZM11.3333 4.2665L12.95 2.58317L13.9333 3.53317L12.2666 5.13317L11.3333 4.2665ZM3.06663 12.4665L4.74996 10.8498L5.66663 11.7332L4.04996 13.4165L3.06663 12.4665Z"
                fill={fill}
            />
        </Svg>
    );
};

const Svg = styled('svg')<{ minWidth: number }>(({ minWidth }) => ({
    minWidth: minWidth,
}));
