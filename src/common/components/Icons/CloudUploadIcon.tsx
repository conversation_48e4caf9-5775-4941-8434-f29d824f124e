import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CloudUploadIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.882 6C13.0057 5.99883 14.0907 6.41088 14.9304 7.15766C15.7701 7.90443 16.306 8.9338 16.436 10.05C17.3739 10.1379 18.242 10.5837 18.86 11.2946C19.478 12.0056 19.7985 12.9272 19.755 13.8682C19.7115 14.8092 19.3074 15.6974 18.6264 16.3483C17.9455 16.9993 17.04 17.363 16.098 17.364H7.116C6.28959 17.364 5.49702 17.0357 4.91266 16.4514C4.32829 15.867 4 15.0744 4 14.248C4 13.4216 4.32829 12.629 4.91266 12.0447C5.49702 11.4603 6.28959 11.132 7.116 11.132H7.334C7.25654 10.4887 7.31634 9.83624 7.50947 9.21771C7.7026 8.59919 8.02467 8.02865 8.45443 7.5437C8.88419 7.05876 9.41188 6.67045 10.0027 6.40436C10.5935 6.13827 11.234 6.00044 11.882 6ZM11.882 9.849C11.8368 9.84606 11.7915 9.85273 11.7491 9.86859C11.7067 9.88445 11.6682 9.90918 11.636 9.94104L9.62 11.774C9.54778 11.8397 9.50426 11.9311 9.49883 12.0286C9.49341 12.126 9.52651 12.2218 9.591 12.295C9.6598 12.3623 9.75098 12.4018 9.84711 12.4061C9.94323 12.4103 10.0375 12.3789 10.112 12.318L11.512 11.041V14.982C11.5169 15.0759 11.5577 15.1643 11.6259 15.2291C11.694 15.2938 11.7845 15.3298 11.8785 15.3298C11.9725 15.3298 12.063 15.2938 12.1311 15.2291C12.1993 15.1643 12.2401 15.0759 12.245 14.982V11.04L13.645 12.317C13.7188 12.3795 13.8134 12.4118 13.91 12.4075C14.0066 12.4033 14.098 12.3627 14.166 12.294C14.2293 12.2202 14.2616 12.1248 14.2562 12.0277C14.2508 11.9306 14.2081 11.8394 14.137 11.773L12.128 9.94C12.0584 9.88324 11.9718 9.85158 11.882 9.85004V9.849Z"
                fill={fill}
            />
        </svg>
    );
};
