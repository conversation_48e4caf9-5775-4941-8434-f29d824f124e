import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PhotoIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className = '',
}: IconProps) => {
    return (
        <svg width={size} style={{ minWidth: size }} height={size} viewBox="0 0 24 24" fill="none">
            <path
                d="M10.4 6L9.361 8.078H6.454C6.26295 8.07761 6.0737 8.11494 5.89711 8.18787C5.72053 8.2608 5.56008 8.3679 5.42499 8.50299C5.28989 8.63809 5.18281 8.79851 5.10988 8.9751C5.03695 9.15168 4.99961 9.34093 5 9.53198V17.011C4.99961 17.202 5.03695 17.3913 5.10988 17.5679C5.18281 17.7445 5.28989 17.9049 5.42499 18.04C5.56008 18.1751 5.72053 18.2822 5.89711 18.3551C6.0737 18.428 6.26295 18.4654 6.454 18.465H18.5C18.6911 18.4654 18.8803 18.428 19.0569 18.3551C19.2335 18.2822 19.3939 18.1751 19.529 18.04C19.6641 17.9049 19.7712 17.7445 19.8441 17.5679C19.9171 17.3913 19.9544 17.202 19.954 17.011V9.53198C19.9544 9.34093 19.9171 9.15168 19.8441 8.9751C19.7712 8.79851 19.6641 8.63809 19.529 8.50299C19.3939 8.3679 19.2335 8.2608 19.0569 8.18787C18.8803 8.11494 18.6911 8.07761 18.5 8.078H15.6L14.561 6H10.4ZM12.478 9.73999C13.1766 9.73999 13.8594 9.94717 14.4403 10.3353C15.0211 10.7234 15.4738 11.275 15.7411 11.9203C16.0085 12.5657 16.0784 13.2759 15.9421 13.9611C15.8059 14.6462 15.4695 15.2756 14.9755 15.7695C14.4815 16.2635 13.8522 16.5999 13.1671 16.7361C12.4819 16.8724 11.7718 16.8025 11.1264 16.5352C10.481 16.2678 9.92935 15.8151 9.54125 15.2343C9.15315 14.6534 8.946 13.9705 8.946 13.272C8.94785 12.3354 9.32064 11.4378 9.98278 10.7755C10.6449 10.1131 11.5425 9.74009 12.479 9.73798L12.478 9.73999ZM12.478 10.987C12.0261 10.987 11.5843 11.121 11.2085 11.3721C10.8328 11.6231 10.5399 11.9801 10.3669 12.3976C10.194 12.8151 10.1487 13.2745 10.2369 13.7178C10.3251 14.161 10.5427 14.5682 10.8623 14.8878C11.1818 15.2073 11.589 15.425 12.0322 15.5131C12.4755 15.6013 12.9349 15.556 13.3524 15.3831C13.77 15.2101 14.1268 14.9173 14.3779 14.5415C14.629 14.1657 14.763 13.7239 14.763 13.272C14.7643 12.9716 14.7062 12.6739 14.5919 12.3961C14.4776 12.1182 14.3094 11.8658 14.0971 11.6533C13.8848 11.4408 13.6325 11.2724 13.3548 11.1579C13.077 11.0434 12.7794 10.985 12.479 10.986L12.478 10.987Z"
                fill={fill}
            />
        </svg>
    );
};
