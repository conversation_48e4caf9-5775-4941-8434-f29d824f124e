import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const InfoIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.004 4C12.3996 4 12.7863 4.1173 13.1152 4.33706C13.4441 4.55682 13.7004 4.86918 13.8518 5.23463C14.0032 5.60009 14.0428 6.00222 13.9656 6.39018C13.8884 6.77814 13.6979 7.13451 13.4182 7.41421C13.1385 7.69392 12.7822 7.8844 12.3942 7.96157C12.0062 8.03874 11.6041 7.99913 11.2387 7.84776C10.8732 7.69638 10.5609 7.44004 10.3411 7.11114C10.1213 6.78224 10.004 6.39556 10.004 6C10.004 5.46957 10.2147 4.96086 10.5898 4.58579C10.9649 4.21071 11.4736 4 12.004 4ZM12.004 9.339C12.4954 9.33972 12.9677 9.52867 13.324 9.867C13.4938 10.0317 13.6284 10.229 13.7198 10.4471C13.8113 10.6652 13.8576 10.8995 13.856 11.136V18.421C13.8575 18.6597 13.8102 18.8962 13.717 19.116C13.6238 19.3357 13.4867 19.5341 13.314 19.699C12.9562 20.0355 12.4824 20.2212 11.9912 20.2177C11.5 20.2142 11.029 20.0216 10.676 19.68C10.5088 19.5159 10.3762 19.3199 10.286 19.1037C10.1959 18.8874 10.15 18.6553 10.151 18.421V11.136C10.1498 10.8994 10.1965 10.665 10.2883 10.4469C10.3801 10.2288 10.515 10.0316 10.685 9.867C11.0415 9.53002 11.5135 9.34248 12.004 9.343V9.339Z"
                fill={fill}
            />
        </svg>
    );
};
