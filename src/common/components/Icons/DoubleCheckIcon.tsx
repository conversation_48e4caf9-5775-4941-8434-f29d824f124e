import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DoubleCheckIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.3619 8.26708C15.1944 8.09784 14.9668 8.00182 14.7287 8.00003C14.4906 7.99823 14.2615 8.09081 14.0914 8.2575L7.44925 14.8219L4.56343 11.8283C4.48211 11.7398 4.38391 11.6685 4.27462 11.6186C4.16532 11.5687 4.04714 11.5411 3.92704 11.5376C3.80693 11.534 3.68733 11.5545 3.57528 11.5979C3.46324 11.6413 3.361 11.7067 3.27461 11.7902C3.18822 11.8737 3.11942 11.9737 3.07226 12.0842C3.0251 12.1947 3.00053 12.3136 3.00001 12.4337C2.99948 12.5539 3.02301 12.6729 3.06921 12.7838C3.11541 12.8948 3.18334 12.9953 3.26899 13.0796L6.79066 16.7294C6.87403 16.8146 6.97348 16.8823 7.08324 16.9288C7.19301 16.9753 7.3109 16.9995 7.43009 17H7.43967C7.67555 16.9992 7.90171 16.9059 8.06952 16.7402L15.3523 9.53756C15.5214 9.36993 15.6172 9.14215 15.6188 8.90405C15.6204 8.66596 15.5276 8.43694 15.3607 8.26708H15.3619Z"
                fill={fill}
            />
            <path
                d="M12.1911 16.7256C12.2742 16.8105 12.3732 16.8782 12.4825 16.9246C12.5919 16.9711 12.7093 16.9954 12.8281 16.9963H12.8377C13.0736 16.9955 13.2997 16.9022 13.4675 16.7364L20.7528 9.54102C20.9041 9.37018 20.9849 9.14825 20.9789 8.9201C20.9728 8.69196 20.8804 8.47461 20.7202 8.31202C20.5601 8.14943 20.3441 8.05371 20.1161 8.04423C19.8881 8.03476 19.665 8.11222 19.4919 8.26096L12.8556 14.8229L12.6162 14.5774C12.5348 14.489 12.4366 14.4177 12.3274 14.3678C12.2181 14.3178 12.0999 14.2903 11.9798 14.2867C11.8597 14.2832 11.7401 14.3037 11.628 14.3471C11.516 14.3905 11.4137 14.4559 11.3273 14.5394C11.241 14.6229 11.1722 14.7229 11.125 14.8334C11.0778 14.9439 11.0533 15.0627 11.0527 15.1829C11.0522 15.3031 11.0757 15.4221 11.1219 15.533C11.1681 15.644 11.2361 15.7445 11.3217 15.8288L12.1911 16.7256Z"
                fill={fill}
            />
        </svg>
    );
};
