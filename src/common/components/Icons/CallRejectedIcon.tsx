import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CallRejectedIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.79901 6.88497C7.66962 6.44254 7.54023 6.08779 7.10828 6.04495L6.28816 6.0011C5.42524 5.95626 4 7.28355 4 9.49471C4.04281 13.3421 9.6562 19.6218 13.3696 19.9755C15.5712 20.1968 16.9536 18.8695 16.9964 17.9417V17.1446C17.0392 16.7021 16.6938 16.5258 16.2629 16.3474C15.4428 16.0375 14.6226 15.6399 13.8015 15.33C13.6507 15.2948 13.4939 15.2964 13.3438 15.3349C13.1937 15.3733 13.0545 15.4474 12.9376 15.5512C12.6577 15.7784 12.3974 16.0298 12.1594 16.3026C11.9434 16.4799 11.7274 16.5676 11.5114 16.3464C9.64444 15.3604 8.17662 13.7317 7.36609 11.7467C7.3281 11.6958 7.30115 11.6373 7.28702 11.5749C7.27289 11.5125 7.27192 11.4477 7.28417 11.3849C7.29641 11.3221 7.32159 11.2627 7.35803 11.2106C7.39447 11.1586 7.44133 11.1151 7.49548 11.0831C7.79804 10.818 8.01401 10.5968 8.27376 10.3317C8.3901 10.2299 8.47901 10.0992 8.53198 9.95214C8.58495 9.80507 8.60021 9.64655 8.57632 9.49172C8.31754 8.65469 8.05876 7.72598 7.79901 6.88497Z"
                fill={fill}
            />
            <path
                d="M19.1997 5.3003C19.0747 5.17525 18.9051 5.105 18.7282 5.105C18.5514 5.105 18.3818 5.17525 18.2567 5.3003L16.4497 7.10734L14.6427 5.30029C14.5176 5.17525 14.348 5.105 14.1712 5.105C13.9943 5.105 13.8247 5.17525 13.6997 5.30029C13.5747 5.42534 13.5044 5.59493 13.5044 5.77177C13.5044 5.94861 13.5747 6.11821 13.6997 6.24325L15.5068 8.05029L13.6997 9.85734C13.5747 9.98238 13.5044 10.152 13.5044 10.3288C13.5044 10.5057 13.5747 10.6752 13.6997 10.8003C13.8248 10.9253 13.9943 10.9956 14.1712 10.9956C14.348 10.9956 14.5176 10.9253 14.6427 10.8003L16.448 8.99496L18.255 10.802C18.3801 10.9271 18.5497 10.9973 18.7265 10.9973C18.9034 10.9973 19.0729 10.9271 19.198 10.802C19.323 10.677 19.3933 10.5074 19.3933 10.3305C19.3933 10.1537 19.323 9.9841 19.198 9.85905L17.3927 8.05372L19.2014 6.24497C19.2635 6.18291 19.3127 6.1092 19.3462 6.02809C19.3797 5.94697 19.3969 5.86004 19.3967 5.77227C19.3966 5.68451 19.3791 5.59764 19.3453 5.51665C19.3115 5.43565 19.262 5.36213 19.1997 5.3003Z"
                fill={fill}
            />
        </svg>
    );
};
