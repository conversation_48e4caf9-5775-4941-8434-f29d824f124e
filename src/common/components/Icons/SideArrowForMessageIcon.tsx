import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SideArrowForMessageIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className = '',
}: IconProps) => {
    return (
        <svg
            className={className}
            width={22}
            style={{ width: 22, height: 18 }}
            height={18}
            viewBox="0 0 22 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.55 17.5L0 0H22L11.55 17.5Z"
                fill={fill}
            />
        </svg>
    );
};
