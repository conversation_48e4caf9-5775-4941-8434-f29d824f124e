import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CopyIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.041 14.0578H8.70003C8.52669 14.0511 8.36267 13.9775 8.24239 13.8525C8.12212 13.7275 8.05493 13.5608 8.05493 13.3873C8.05493 13.2138 8.12212 13.0471 8.24239 12.9221C8.36267 12.7971 8.52669 12.7235 8.70003 12.7168H10.041C10.1313 12.7133 10.2213 12.7281 10.3057 12.7602C10.39 12.7923 10.4671 12.8412 10.5322 12.9038C10.5972 12.9664 10.649 13.0415 10.6843 13.1246C10.7197 13.2076 10.7379 13.297 10.7379 13.3873C10.7379 13.4776 10.7197 13.567 10.6843 13.6501C10.649 13.7331 10.5972 13.8082 10.5322 13.8708C10.4671 13.9335 10.39 13.9823 10.3057 14.0144C10.2213 14.0465 10.1313 14.0613 10.041 14.0578Z"
                fill={fill}
            />
            <path
                d="M12.723 11.3752H8.70003C8.52669 11.3685 8.36267 11.2949 8.24239 11.1699C8.12212 11.0449 8.05493 10.8782 8.05493 10.7047C8.05493 10.5312 8.12212 10.3645 8.24239 10.2395C8.36267 10.1145 8.52669 10.0409 8.70003 10.0342H12.723C12.8133 10.0307 12.9033 10.0455 12.9877 10.0776C13.072 10.1097 13.1491 10.1585 13.2142 10.2212C13.2792 10.2838 13.331 10.3589 13.3663 10.4419C13.4017 10.525 13.4199 10.6144 13.4199 10.7047C13.4199 10.795 13.4017 10.8844 13.3663 10.9674C13.331 11.0505 13.2792 11.1256 13.2142 11.1882C13.1491 11.2508 13.072 11.2997 12.9877 11.3318C12.9033 11.3639 12.8133 11.3787 12.723 11.3752Z"
                fill={fill}
            />
            <path
                d="M12.723 8.69306H8.70003C8.52669 8.68637 8.36267 8.6128 8.24239 8.48778C8.12212 8.36277 8.05493 8.19604 8.05493 8.02256C8.05493 7.84909 8.12212 7.68235 8.24239 7.55734C8.36267 7.43233 8.52669 7.35876 8.70003 7.35206H12.723C12.8133 7.34858 12.9033 7.36334 12.9877 7.39547C13.072 7.42759 13.1491 7.47642 13.2142 7.53902C13.2792 7.60163 13.331 7.67672 13.3663 7.75981C13.4017 7.8429 13.4199 7.93227 13.4199 8.02256C13.4199 8.11286 13.4017 8.20223 13.3663 8.28531C13.331 8.3684 13.2792 8.4435 13.2142 8.5061C13.1491 8.56871 13.072 8.61753 12.9877 8.64966C12.9033 8.68179 12.8133 8.69655 12.723 8.69306Z"
                fill={fill}
            />
            <path
                d="M18.0872 6.68192H16.0762V5.34092C16.0762 5.16296 16.0055 4.99229 15.8796 4.86645C15.7538 4.74062 15.5831 4.66992 15.4052 4.66992H6.01817C5.84021 4.66992 5.66954 4.74062 5.5437 4.86645C5.41786 4.99229 5.34717 5.16296 5.34717 5.34092V16.0699C5.34717 16.2479 5.41786 16.4186 5.5437 16.5444C5.66954 16.6702 5.84021 16.7409 6.01817 16.7409H7.35917V18.7529C7.35917 18.9309 7.42986 19.1016 7.5557 19.2274C7.68154 19.3532 7.85221 19.4239 8.03017 19.4239H18.0872C18.2651 19.4239 18.4358 19.3532 18.5616 19.2274C18.6875 19.1016 18.7582 18.9309 18.7582 18.7529V7.35192C18.7579 7.17414 18.6871 7.00372 18.5613 6.8781C18.4355 6.75248 18.265 6.68192 18.0872 6.68192ZM6.68717 6.01092H14.7342V15.3989H6.68817L6.68717 6.01092ZM17.4172 18.0809H8.70017V16.7399H15.4062C15.5841 16.7399 15.7548 16.6692 15.8806 16.5434C16.0065 16.4176 16.0772 16.2469 16.0772 16.0689V8.02292H17.4172V18.0809Z"
                fill={fill}
            />
        </svg>
    );
};
