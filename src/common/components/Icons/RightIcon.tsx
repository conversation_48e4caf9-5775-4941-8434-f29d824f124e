import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const RightIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.0561 17.8002C10.2951 17.8002 10.5162 17.6933 10.6733 17.5168L14.5644 13.1318L14.3401 12.9327L14.5665 13.1295C14.7193 12.9536 14.8001 12.7242 14.8001 12.4912C14.8001 12.2582 14.7193 12.0287 14.5665 11.8529L10.6733 7.4804L10.6708 7.47768C10.5128 7.30451 10.293 7.2002 10.0561 7.2002C9.81916 7.2002 9.59931 7.30445 9.44127 7.47762L9.43645 7.48317C9.28369 7.65901 9.20291 7.88848 9.20291 8.12147C9.20291 8.35445 9.28367 8.58394 9.43643 8.75978L12.766 12.4986L9.43887 16.2347C9.28276 16.41 9.20007 16.6411 9.20007 16.8757C9.20007 17.1104 9.28276 17.3415 9.43887 17.5168C9.59599 17.6933 9.81708 17.8002 10.0561 17.8002ZM14.5001 12.4912C14.5001 12.6562 14.4426 12.8146 14.3401 12.9327L10.4492 17.3173C10.345 17.4344 10.2036 17.5002 10.0561 17.5002C10.0377 17.5002 10.0193 17.4992 10.0011 17.4971C10.0193 17.4992 10.0376 17.5002 10.056 17.5002C10.2035 17.5002 10.3449 17.4344 10.4492 17.3173L14.34 12.9327C14.4426 12.8146 14.5 12.6562 14.5 12.4912C14.5 12.3262 14.4426 12.1677 14.34 12.0496L10.4492 7.6799C10.3744 7.59799 10.2815 7.54172 10.1813 7.51602C10.2816 7.54171 10.3745 7.59799 10.4492 7.67991L14.3401 12.0496C14.4426 12.1677 14.5001 12.3262 14.5001 12.4912Z"
                fill={fill}
            />
        </svg>
    );
};
