import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PageIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size, flexShrink: 0 }}
            height={size}
            viewBox="0 0 22 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.8333 8.45312V5H7.625C7.26042 5 7 5.2793 7 5.60938V17.3906C7 17.7461 7.26042 18 7.625 18H16.375C16.7135 18 17 17.7461 17 17.3906V9.0625H13.4583C13.0938 9.0625 12.8333 8.80859 12.8333 8.45312ZM17 8.09766C17 7.94531 16.9219 7.79297 16.8177 7.66602L14.2656 5.17773C14.1354 5.07617 13.9792 5 13.8229 5H13.6667V8.25H17V8.09766Z"
                fill={fill}
            />
        </svg>
    );
};
