import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PollsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15 20.659H8C7.20435 20.659 6.44129 20.3429 5.87868 19.7803C5.31607 19.2177 5 18.4546 5 17.659V8.16699C5 7.37134 5.31607 6.60828 5.87868 6.04567C6.44129 5.48306 7.20435 5.16699 8 5.16699H15C15.7956 5.16699 16.5587 5.48306 17.1213 6.04567C17.6839 6.60828 18 7.37134 18 8.16699V17.659C18 18.4546 17.6839 19.2177 17.1213 19.7803C16.5587 20.3429 15.7956 20.659 15 20.659ZM8.254 13.445C8.19304 13.4448 8.13266 13.4567 8.07635 13.4801C8.02004 13.5034 7.96893 13.5377 7.926 13.581C7.83929 13.6681 7.79061 13.7861 7.79061 13.909C7.79061 14.0319 7.83929 14.1499 7.926 14.237L8.526 14.837L7.926 15.437C7.83929 15.5241 7.79061 15.6421 7.79061 15.765C7.79061 15.8879 7.83929 16.0059 7.926 16.093C8.01301 16.1799 8.13099 16.2288 8.254 16.2288C8.37701 16.2288 8.49499 16.1799 8.582 16.093L9.182 15.493L9.782 16.093C9.86927 16.1795 9.98715 16.228 10.11 16.228C10.2329 16.228 10.3507 16.1795 10.438 16.093C10.5247 16.0059 10.5734 15.8879 10.5734 15.765C10.5734 15.6421 10.5247 15.5241 10.438 15.437L9.838 14.837L10.438 14.237C10.5215 14.1493 10.5674 14.0324 10.566 13.9113C10.5645 13.7902 10.5157 13.6745 10.4301 13.5889C10.3445 13.5033 10.2287 13.4545 10.1077 13.453C9.98657 13.4516 9.86969 13.4975 9.782 13.581L9.182 14.181L8.582 13.581C8.53897 13.5379 8.48784 13.5036 8.43156 13.4803C8.37527 13.457 8.31493 13.445 8.254 13.445ZM11.964 14.367C11.8409 14.367 11.7229 14.4159 11.6359 14.5029C11.5489 14.5899 11.5 14.7079 11.5 14.831C11.5 14.9541 11.5489 15.0721 11.6359 15.1591C11.7229 15.2461 11.8409 15.295 11.964 15.295H14.746C14.8691 15.295 14.9871 15.2461 15.0741 15.1591C15.1611 15.0721 15.21 14.9541 15.21 14.831C15.21 14.7079 15.1611 14.5899 15.0741 14.5029C14.9871 14.4159 14.8691 14.367 14.746 14.367H11.964ZM8.254 8.80699C8.13163 8.80699 8.01421 8.85532 7.92731 8.94148C7.84041 9.02763 7.79105 9.14463 7.79 9.26699V11.125C7.79 11.2481 7.83889 11.3661 7.9259 11.4531C8.01292 11.5401 8.13094 11.589 8.254 11.589H10.108C10.2311 11.589 10.3491 11.5401 10.4361 11.4531C10.5231 11.3661 10.572 11.2481 10.572 11.125V9.26699C10.572 9.14393 10.5231 9.02591 10.4361 8.93889C10.3491 8.85188 10.2311 8.80299 10.108 8.80299L8.254 8.80699ZM11.965 9.73499C11.9023 9.73208 11.8397 9.74191 11.7809 9.76389C11.7222 9.78587 11.6685 9.81954 11.6231 9.86287C11.5777 9.90619 11.5416 9.95828 11.5169 10.016C11.4922 10.0737 11.4795 10.1357 11.4795 10.1985C11.4795 10.2612 11.4922 10.3233 11.5169 10.381C11.5416 10.4387 11.5777 10.4908 11.6231 10.5341C11.6685 10.5774 11.7222 10.6111 11.7809 10.6331C11.8397 10.6551 11.9023 10.6649 11.965 10.662H14.746C14.8087 10.6649 14.8713 10.6551 14.9301 10.6331C14.9888 10.6111 15.0425 10.5774 15.0879 10.5341C15.1333 10.4908 15.1694 10.4387 15.1941 10.381C15.2188 10.3233 15.2315 10.2612 15.2315 10.1985C15.2315 10.1357 15.2188 10.0737 15.1941 10.016C15.1694 9.95828 15.1333 9.90619 15.0879 9.86287C15.0425 9.81954 14.9888 9.78587 14.9301 9.76389C14.8713 9.74191 14.8087 9.73208 14.746 9.73499H11.965ZM9.646 10.662H8.718V9.73499H9.645V10.661L9.646 10.662Z"
                fill={fill}
            />
            <path
                d="M16.1368 4.855H13.3548C13.3548 4.6114 13.3069 4.37018 13.2136 4.14512C13.1204 3.92006 12.9838 3.71557 12.8115 3.54332C12.6393 3.37106 12.4348 3.23443 12.2097 3.1412C11.9847 3.04798 11.7434 3 11.4998 3C11.2562 3 11.015 3.04798 10.79 3.1412C10.5649 3.23443 10.3604 3.37106 10.1882 3.54332C10.0159 3.71557 9.87926 3.92006 9.78604 4.14512C9.69282 4.37018 9.64484 4.6114 9.64484 4.855H6.86184C6.37002 4.85553 5.8985 5.05114 5.55074 5.3989C5.20297 5.74667 5.00737 6.21819 5.00684 6.71V18.768C5.00737 19.2598 5.20297 19.7313 5.55074 20.0791C5.8985 20.4269 6.37002 20.6225 6.86184 20.623H16.1368C16.6286 20.6225 17.1002 20.4269 17.4479 20.0791C17.7957 19.7313 17.9913 19.2598 17.9918 18.768V6.71C17.9913 6.21819 17.7957 5.74667 17.4479 5.3989C17.1002 5.05114 16.6286 4.85553 16.1368 4.855ZM17.0648 18.768C17.0648 19.0141 16.9671 19.2502 16.793 19.4242C16.619 19.5982 16.383 19.696 16.1368 19.696H6.86184C6.61572 19.696 6.37967 19.5982 6.20564 19.4242C6.03161 19.2502 5.93384 19.0141 5.93384 18.768V6.71C5.93384 6.46388 6.03161 6.22784 6.20564 6.0538C6.37967 5.87977 6.61572 5.782 6.86184 5.782H7.78984V6.71H15.2068V5.783H16.1348C16.381 5.783 16.617 5.88077 16.791 6.0548C16.9651 6.22884 17.0628 6.46488 17.0628 6.711L17.0648 18.768Z"
                fill={fill}
            />
        </svg>
    );
};
