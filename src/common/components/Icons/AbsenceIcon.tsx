import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AbsenceIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            className={className}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.4096 19H7.59037C7.16894 18.9994 6.76491 18.8271 6.46681 18.5208C6.16872 18.2145 6.00085 17.7991 6 17.3658V9.92137H19V17.3658C18.9991 17.7991 18.8313 18.2145 18.5332 18.5208C18.2351 18.8271 17.8311 18.9994 17.4096 19ZM12.3711 15.1762L14.0045 16.8557C14.1187 16.9668 14.2706 17.0277 14.4278 17.0254C14.585 17.0232 14.7352 16.9579 14.8463 16.8435C14.9574 16.7291 15.0207 16.5746 15.0227 16.4129C15.0248 16.2512 14.9653 16.0951 14.8571 15.9778L13.2238 14.2984L14.8582 12.6178C14.9142 12.5602 14.9586 12.4919 14.9889 12.4167C15.0192 12.3415 15.0348 12.2608 15.0348 12.1794C15.0348 12.098 15.0192 12.0174 14.9889 11.9422C14.9586 11.867 14.9142 11.7986 14.8582 11.7411C14.8022 11.6835 14.7357 11.6378 14.6626 11.6067C14.5894 11.5755 14.511 11.5595 14.4319 11.5595C14.3527 11.5595 14.2743 11.5755 14.2011 11.6067C14.128 11.6378 14.0615 11.6835 14.0055 11.7411L12.3711 13.4216L10.7367 11.7422C10.6227 11.6309 10.4709 11.5698 10.3136 11.5718C10.1564 11.5739 10.0062 11.639 9.89489 11.7533C9.78363 11.8675 9.72014 12.0219 9.71792 12.1836C9.71571 12.3453 9.77495 12.5015 9.88303 12.6189L11.5174 14.2984L9.88303 15.9789C9.76982 16.0953 9.70622 16.2532 9.70622 16.4179C9.70622 16.5825 9.76982 16.7404 9.88303 16.8568C9.99624 16.9732 10.1498 17.0386 10.3099 17.0386C10.47 17.0386 10.6235 16.9732 10.7367 16.8568L12.3701 15.1773L12.3711 15.1762ZM19 8.89005H6V8.05087C6.00028 7.61715 6.1679 7.20126 6.46606 6.89447C6.76422 6.58768 7.16857 6.41505 7.59037 6.41446H8.53643V5.89881C8.53643 5.78078 8.55904 5.6639 8.60297 5.55485C8.64689 5.4458 8.71128 5.34672 8.79245 5.26326C8.87362 5.17979 8.96998 5.11359 9.07603 5.06842C9.18208 5.02325 9.29575 5 9.41054 5C9.52533 5 9.639 5.02325 9.74505 5.06842C9.8511 5.11359 9.94746 5.17979 10.0286 5.26326C10.1098 5.34672 10.1742 5.4458 10.2181 5.55485C10.262 5.6639 10.2847 5.78078 10.2847 5.89881V6.41446H14.6982V5.89881C14.7061 5.66574 14.8017 5.44494 14.9649 5.28294C15.1281 5.12095 15.346 5.03041 15.5728 5.03041C15.7996 5.03041 16.0176 5.12095 16.1807 5.28294C16.3439 5.44494 16.4395 5.66574 16.4475 5.89881V6.41446H17.3925C17.6023 6.41345 17.8103 6.45494 18.0045 6.53657C18.1988 6.6182 18.3755 6.73837 18.5245 6.89023C18.6736 7.04208 18.7921 7.22264 18.8733 7.42159C18.9546 7.62055 18.9969 7.834 18.9979 8.04977V8.88895L19 8.89005Z"
                fill={fill}
            />
        </svg>
    );
};
