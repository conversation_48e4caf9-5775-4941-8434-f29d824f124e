import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const BrokenLinkIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.4122 14.8131L16.9282 14.3131C16.751 14.2624 16.5611 14.2824 16.3984 14.369C16.2358 14.4555 16.1131 14.6019 16.0562 14.7771C16.0063 14.9469 16.0227 15.1293 16.1019 15.2876C16.1811 15.4459 16.3173 15.5683 16.4832 15.6301L17.9672 16.1301C18.0535 16.1611 18.1453 16.174 18.2368 16.1681C18.3283 16.1623 18.4177 16.1378 18.4993 16.0961C18.581 16.0544 18.6533 15.9964 18.7118 15.9258C18.7702 15.8551 18.8136 15.7732 18.8392 15.6851C18.8687 15.5998 18.8811 15.5096 18.8757 15.4195C18.8702 15.3294 18.8469 15.2413 18.8072 15.1602C18.7675 15.0792 18.7122 15.0068 18.6444 14.9472C18.5766 14.8876 18.4977 14.8421 18.4122 14.8131Z"
                fill={fill}
            />
            <path
                d="M14.758 10.0642C14.6943 9.99819 14.6179 9.94569 14.5335 9.90982C14.449 9.87396 14.3582 9.85547 14.2665 9.85547C14.1747 9.85547 14.0839 9.87396 13.9995 9.90982C13.915 9.94569 13.8387 9.99819 13.775 10.0642L9.84197 14.0162C9.71762 14.152 9.65177 14.3313 9.6587 14.5153C9.66562 14.6993 9.74476 14.8732 9.87897 14.9992C10.009 15.1157 10.1774 15.1801 10.352 15.1801C10.5265 15.1801 10.6949 15.1157 10.825 14.9992L14.758 11.0472C14.883 10.9141 14.9526 10.7383 14.9526 10.5557C14.9526 10.3731 14.883 10.1973 14.758 10.0642Z"
                fill={fill}
            />
            <path
                d="M15.4069 16.738C15.3503 16.5634 15.2266 16.4184 15.0631 16.3349C14.8995 16.2515 14.7095 16.2364 14.5349 16.293C14.3603 16.3496 14.2153 16.4733 14.1318 16.6369C14.0483 16.8004 14.0333 16.9904 14.0899 17.165L14.5899 18.649C14.6152 18.7418 14.6594 18.8284 14.7197 18.9033C14.78 18.9783 14.855 19.04 14.9402 19.0847C15.0254 19.1293 15.1189 19.156 15.2148 19.1629C15.3108 19.1698 15.4071 19.1569 15.4978 19.1249C15.5885 19.0929 15.6717 19.0426 15.7421 18.9771C15.8125 18.9116 15.8686 18.8323 15.907 18.7441C15.9454 18.6559 15.9652 18.5607 15.9652 18.4645C15.9652 18.3683 15.9453 18.2732 15.9069 18.185L15.4069 16.738Z"
                fill={fill}
            />
            <path
                d="M9.17319 8.31981C9.19504 8.41312 9.23581 8.50096 9.29299 8.57788C9.35017 8.65479 9.42254 8.71915 9.5056 8.76695C9.58867 8.81476 9.68067 8.845 9.7759 8.8558C9.87113 8.8666 9.96756 8.85773 10.0592 8.82973C10.1509 8.80174 10.2358 8.75522 10.3088 8.69307C10.3817 8.63091 10.4411 8.55443 10.4833 8.46837C10.5255 8.38232 10.5496 8.28851 10.554 8.19278C10.5585 8.09704 10.5432 8.00141 10.5092 7.91181C10.5092 7.89281 10.4902 7.87481 10.4902 7.85581L9.99019 6.37181C9.93185 6.19838 9.80791 6.05469 9.64492 5.97152C9.48194 5.88834 9.29287 5.87229 9.11819 5.92681C8.94614 5.98707 8.8039 6.11129 8.72103 6.27367C8.63817 6.43606 8.62103 6.62412 8.67319 6.79881L9.17319 8.31981Z"
                fill={fill}
            />
            <path
                d="M6.16904 10.231L7.65304 10.731C7.82769 10.7876 8.01767 10.7725 8.18121 10.6891C8.34474 10.6056 8.46842 10.4606 8.52504 10.286C8.58167 10.1113 8.5666 9.92135 8.48314 9.75782C8.39969 9.59428 8.25469 9.4706 8.08004 9.41398L6.59604 8.93198C6.50971 8.90104 6.41796 8.88809 6.32644 8.89395C6.23491 8.8998 6.14556 8.92433 6.06387 8.96601C5.98218 9.0077 5.90989 9.06567 5.85145 9.13634C5.793 9.20702 5.74965 9.28891 5.72404 9.37698C5.66983 9.54924 5.68625 9.73598 5.7697 9.89613C5.85315 10.0563 5.9968 10.1767 6.16904 10.231Z"
                fill={fill}
            />
            <path
                d="M12.7911 9.08109L14.7571 7.11509C14.9504 6.91954 15.1804 6.76414 15.434 6.65782C15.6876 6.55149 15.9596 6.49635 16.2346 6.49555C16.5096 6.49476 16.782 6.54832 17.0361 6.65318C17.2903 6.75803 17.5212 6.9121 17.7157 7.10652C17.9101 7.30094 18.0642 7.53188 18.169 7.78606C18.2739 8.04023 18.3274 8.31263 18.3266 8.58759C18.3258 8.86254 18.2707 9.13462 18.1644 9.38819C18.058 9.64175 17.9026 9.87179 17.7071 10.0651L15.7411 12.0311C15.6289 12.1615 15.5686 12.3287 15.5718 12.5007C15.575 12.6727 15.6415 12.8375 15.7585 12.9637C15.8754 13.0898 16.0348 13.1685 16.2061 13.1846C16.3774 13.2007 16.5486 13.1532 16.6871 13.0511L16.7241 13.0141L18.6901 11.0481C19.3716 10.4256 19.7779 9.55794 19.8196 8.63592C19.8613 7.7139 19.5351 6.81306 18.9126 6.13159C18.2901 5.45011 17.4225 5.04382 16.5004 5.0021C15.5784 4.96037 14.6776 5.28662 13.9961 5.90909L13.7731 6.13209L11.8071 8.09609C11.6949 8.2265 11.6346 8.39367 11.6378 8.56569C11.641 8.73771 11.7075 8.90253 11.8245 9.02868C11.9414 9.15482 12.1008 9.23346 12.2721 9.24959C12.4434 9.26571 12.6146 9.21819 12.7531 9.11609C12.7721 9.11609 12.7721 9.09709 12.7901 9.07909L12.7911 9.08109Z"
                fill={fill}
            />
            <path
                d="M10.8249 18.9319L12.7959 16.9659C12.9203 16.8302 12.9861 16.6508 12.9792 16.4668C12.9722 16.2829 12.8931 16.109 12.7589 15.9829C12.6289 15.8665 12.4605 15.8021 12.2859 15.8021C12.1113 15.8021 11.9429 15.8665 11.8129 15.9829L9.8469 17.9489C9.65361 18.1445 9.42356 18.2999 9.17 18.4062C8.91643 18.5125 8.64435 18.5677 8.3694 18.5685C8.09444 18.5693 7.82205 18.5157 7.56787 18.4109C7.31369 18.306 7.08275 18.1519 6.88833 17.9575C6.69391 17.7631 6.53984 17.5321 6.43499 17.278C6.33014 17.0238 6.27657 16.7514 6.27736 16.4764C6.27816 16.2015 6.33331 15.9294 6.43963 15.6758C6.54595 15.4223 6.70135 15.1922 6.8969 14.9989L8.8629 13.0139C8.98725 12.8782 9.0531 12.6988 9.04618 12.5148C9.03925 12.3309 8.96011 12.157 8.8259 12.0309C8.69587 11.9145 8.52745 11.8501 8.3529 11.8501C8.17835 11.8501 8.00993 11.9145 7.8799 12.0309L5.9139 14.0159C5.31184 14.6776 4.9877 15.5457 5.00878 16.44C5.02985 17.3343 5.39452 18.1862 6.02708 18.8188C6.65964 19.4513 7.5115 19.816 8.40583 19.8371C9.30015 19.8581 10.1682 19.534 10.8299 18.9319H10.8249Z"
                fill={fill}
            />
        </svg>
    );
};
