import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PauseIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.615 17.307V6.69201C13.615 6.24313 13.7933 5.81264 14.1107 5.49523C14.4281 5.17783 14.8586 4.99951 15.3075 4.99951C15.7564 4.99951 16.1869 5.17783 16.5043 5.49523C16.8217 5.81264 17 6.24313 17 6.69201V17.307C17 17.5293 16.9562 17.7494 16.8712 17.9547C16.7861 18.16 16.6614 18.3466 16.5043 18.5038C16.3471 18.661 16.1605 18.7856 15.9552 18.8707C15.7498 18.9557 15.5298 18.9995 15.3075 18.9995C15.0852 18.9995 14.8652 18.9557 14.6598 18.8707C14.4545 18.7856 14.2679 18.661 14.1107 18.5038C13.9536 18.3466 13.8289 18.16 13.7438 17.9547C13.6588 17.7494 13.615 17.5293 13.615 17.307ZM6 17.307V6.69201C6 6.24313 6.17832 5.81264 6.49572 5.49523C6.81313 5.17783 7.24362 4.99951 7.6925 4.99951C8.14138 4.99951 8.57187 5.17783 8.88928 5.49523C9.20668 5.81264 9.385 6.24313 9.385 6.69201V17.307C9.385 17.5293 9.34122 17.7494 9.25617 17.9547C9.17111 18.16 9.04644 18.3466 8.88928 18.5038C8.73212 18.661 8.54554 18.7856 8.34019 18.8707C8.13485 18.9557 7.91476 18.9995 7.6925 18.9995C7.47024 18.9995 7.25015 18.9557 7.04481 18.8707C6.83946 18.7856 6.65288 18.661 6.49572 18.5038C6.33856 18.3466 6.21389 18.16 6.12883 17.9547C6.04378 17.7494 6 17.5293 6 17.307V17.307Z"
                fill={fill}
            />
        </svg>
    );
};
