import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const NumbersListIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.92302 12.85H18.174C18.3928 12.85 18.6027 12.7631 18.7574 12.6084C18.9121 12.4536 18.999 12.2438 18.999 12.025C18.999 11.8062 18.9121 11.5964 18.7574 11.4416C18.6027 11.2869 18.3928 11.2 18.174 11.2H9.92302C9.70422 11.2 9.49438 11.2869 9.33966 11.4416C9.18494 11.5964 9.09802 11.8062 9.09802 12.025C9.09802 12.2438 9.18494 12.4536 9.33966 12.6084C9.49438 12.7631 9.70422 12.85 9.92302 12.85ZM9.92302 7.899H18.174C18.3928 7.899 18.6027 7.81208 18.7574 7.65736C18.9121 7.50265 18.999 7.2928 18.999 7.074C18.9988 6.85537 18.9117 6.64578 18.757 6.49128C18.6023 6.33678 18.3927 6.25 18.174 6.25H9.92302C9.70422 6.25 9.49438 6.33692 9.33966 6.49164C9.18494 6.64635 9.09802 6.8562 9.09802 7.075C9.09802 7.2938 9.18494 7.50365 9.33966 7.65836C9.49438 7.81308 9.70422 7.9 9.92302 7.9V7.899ZM9.92302 17.799H18.174C18.3928 17.799 18.6027 17.7121 18.7574 17.5574C18.9121 17.4026 18.999 17.1928 18.999 16.974C18.999 16.7552 18.9121 16.5454 18.7574 16.3906C18.6027 16.2359 18.3928 16.149 18.174 16.149H9.92302C9.70422 16.149 9.49438 16.2359 9.33966 16.3906C9.18494 16.5454 9.09802 16.7552 9.09802 16.974C9.09776 17.0825 9.11891 17.19 9.16025 17.2903C9.20159 17.3907 9.26232 17.4818 9.33895 17.5587C9.41559 17.6355 9.50662 17.6964 9.60685 17.738C9.70707 17.7796 9.81452 17.801 9.92302 17.801V17.799Z"
                fill={fill}
            />
            <path
                d="M6.10796 9.0002C6.0813 9.0002 6.0573 8.99086 6.03596 8.9722C6.0173 8.95086 6.00796 8.92686 6.00796 8.9002V6.9962L5.45996 7.4162C5.4413 7.4322 5.4213 7.4402 5.39996 7.4402C5.37063 7.4402 5.34396 7.4242 5.31996 7.3922L5.08396 7.0842C5.07063 7.06286 5.06396 7.04286 5.06396 7.0242C5.06396 6.98686 5.07996 6.95886 5.11196 6.9402L6.03196 6.2282C6.0613 6.20953 6.0933 6.2002 6.12796 6.2002H6.62796C6.6573 6.2002 6.6813 6.20953 6.69996 6.2282C6.71863 6.24686 6.72796 6.27086 6.72796 6.3002V8.9002C6.72796 8.92686 6.71863 8.95086 6.69996 8.9722C6.6813 8.99086 6.6573 9.0002 6.62796 9.0002H6.10796Z"
                fill={fill}
            />
            <path
                d="M5.26796 13.0002C5.23863 13.0002 5.2133 12.9909 5.19196 12.9722C5.1733 12.9535 5.16396 12.9295 5.16396 12.9002V12.6162C5.16396 12.5362 5.1973 12.4709 5.26396 12.4202L5.67996 12.0122C5.92263 11.8255 6.10663 11.6802 6.23196 11.5762C6.3573 11.4722 6.45196 11.3775 6.51596 11.2922C6.57996 11.2069 6.61196 11.1255 6.61196 11.0482C6.61196 10.8375 6.50796 10.7322 6.29996 10.7322C6.19063 10.7322 6.10663 10.7655 6.04796 10.8322C5.9893 10.8962 5.95063 10.9775 5.93196 11.0762C5.91596 11.1375 5.87063 11.1682 5.79596 11.1682H5.29596C5.27196 11.1682 5.25196 11.1602 5.23596 11.1442C5.21996 11.1282 5.21196 11.1082 5.21196 11.0842C5.2173 10.9135 5.26396 10.7575 5.35196 10.6162C5.44263 10.4722 5.5693 10.3589 5.73196 10.2762C5.8973 10.1909 6.08663 10.1482 6.29996 10.1482C6.52396 10.1482 6.71596 10.1842 6.87596 10.2562C7.03596 10.3282 7.1573 10.4295 7.23996 10.5602C7.32263 10.6909 7.36397 10.8429 7.36397 11.0162C7.36397 11.2029 7.3053 11.3735 7.18797 11.5282C7.0733 11.6802 6.8973 11.8455 6.65996 12.0242L6.29196 12.3962H7.31997C7.3493 12.3962 7.3733 12.4055 7.39197 12.4242C7.4133 12.4429 7.42396 12.4669 7.42396 12.4962V12.9002C7.42396 12.9295 7.4133 12.9535 7.39197 12.9722C7.3733 12.9909 7.3493 13.0002 7.31997 13.0002H5.26796Z"
                fill={fill}
            />
            <path
                d="M6.29996 18.0402C6.0413 18.0402 5.82396 18.0002 5.64796 17.9202C5.47463 17.8402 5.3453 17.7415 5.25996 17.6242C5.1773 17.5042 5.1333 17.3842 5.12796 17.2642C5.12796 17.2402 5.13596 17.2202 5.15196 17.2042C5.17063 17.1882 5.19196 17.1802 5.21596 17.1802H5.73996C5.7693 17.1802 5.7933 17.1855 5.81196 17.1962C5.83063 17.2069 5.84796 17.2269 5.86396 17.2562C5.9173 17.3949 6.06263 17.4642 6.29996 17.4642C6.43596 17.4642 6.5413 17.4335 6.61596 17.3722C6.6933 17.3082 6.73196 17.2215 6.73196 17.1122C6.73196 16.8989 6.5933 16.7922 6.31596 16.7922H5.87196C5.8453 16.7922 5.8213 16.7829 5.79996 16.7642C5.7813 16.7429 5.77196 16.7189 5.77196 16.6922V16.4682C5.77196 16.4095 5.7933 16.3642 5.83596 16.3322L6.49196 15.7762H5.37996C5.3533 15.7762 5.3293 15.7669 5.30796 15.7482C5.2893 15.7269 5.27996 15.7029 5.27996 15.6762V15.3002C5.27996 15.2709 5.2893 15.2469 5.30796 15.2282C5.3293 15.2095 5.3533 15.2002 5.37996 15.2002H7.21996C7.2493 15.2002 7.2733 15.2095 7.29197 15.2282C7.3133 15.2469 7.32397 15.2709 7.32397 15.3002V15.6402C7.32397 15.6909 7.30263 15.7349 7.25996 15.7722L6.65196 16.3362L6.67996 16.3442C6.9253 16.3762 7.11863 16.4589 7.25996 16.5922C7.40396 16.7229 7.47597 16.9109 7.47597 17.1562C7.47597 17.3349 7.4253 17.4909 7.32397 17.6242C7.22263 17.7575 7.08263 17.8602 6.90396 17.9322C6.7253 18.0042 6.52396 18.0402 6.29996 18.0402Z"
                fill={fill}
            />
        </svg>
    );
};
