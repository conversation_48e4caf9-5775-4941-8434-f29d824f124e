import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DetailsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19 6.99902H5C3.89543 6.99902 3 7.89445 3 8.99902V15.999C3 17.1036 3.89543 17.999 5 17.999H19C20.1046 17.999 21 17.1036 21 15.999V8.99902C21 7.89445 20.1046 6.99902 19 6.99902Z"
                fill={fill}
            />
            <path d="M16.332 8H7V16.295H16.332V8Z" fill="white" />
            <path
                d="M19 7.658C18.3068 7.53683 17.2036 7.14527 16.5 7.15802C15.341 7.31802 12.579 7.65799 12.0745 7.15802H8.692C7.453 7.15802 7.318 7.37902 6 7.65802C5.5962 8.54605 5.1557 7.57637 5 8.5394L5.219 16.3874C5.219 17.2664 6.739 16.8724 7.619 16.8734H15.519C16.398 16.8734 17.993 17.2664 17.995 16.3874L18.167 8.9504C18.167 8.9504 20.381 9.40264 19 7.658ZM10.919 13.7024L9.408 15.2154C9.31245 15.3092 9.1839 15.3618 9.05 15.3618C8.9161 15.3618 8.78755 15.3092 8.692 15.2154L8.192 14.7154C8.09705 14.6205 8.04371 14.4917 8.04371 14.3574C8.04371 14.2231 8.09705 14.0943 8.192 13.9994C8.28695 13.9045 8.41572 13.8511 8.55 13.8511C8.68428 13.8511 8.81305 13.9045 8.908 13.9994L9.054 14.1504L10.204 12.9874C10.251 12.9404 10.3068 12.9031 10.3683 12.8776C10.4297 12.8522 10.4955 12.8391 10.562 12.8391C10.6285 12.8391 10.6943 12.8522 10.7557 12.8776C10.8172 12.9031 10.873 12.9404 10.92 12.9874C10.967 13.0344 11.0043 13.0902 11.0297 13.1516C11.0552 13.2131 11.0683 13.2789 11.0683 13.3454C11.0683 13.4119 11.0552 13.4777 11.0297 13.5391C11.0043 13.6006 10.967 13.6564 10.92 13.7034L10.919 13.7024ZM10.919 9.6674L9.408 11.1804C9.31245 11.2742 9.1839 11.3268 9.05 11.3268C8.9161 11.3268 8.78755 11.2742 8.692 11.1804L8.192 10.6804C8.09705 10.5855 8.04371 10.4567 8.04371 10.3224C8.04371 10.1881 8.09705 10.0593 8.192 9.9644C8.28695 9.86945 8.41572 9.81611 8.55 9.81611C8.68428 9.81611 8.81305 9.86945 8.908 9.9644L9.054 10.1154L10.208 8.9554C10.3029 8.86045 10.4317 8.80711 10.566 8.80711C10.7003 8.80711 10.8291 8.86045 10.924 8.9554C11.0189 9.05034 11.0723 9.17912 11.0723 9.3134C11.0723 9.44767 11.0189 9.57645 10.924 9.6714L10.919 9.6674ZM14.594 14.8574H12.579C12.4452 14.8574 12.3169 14.8042 12.2223 14.7096C12.1277 14.615 12.0745 14.4867 12.0745 14.3529C12.0745 14.2191 12.1277 14.0908 12.2223 13.9962C12.3169 13.9015 12.4452 13.8484 12.579 13.8484H14.595C14.7288 13.8484 14.8571 13.9015 14.9517 13.9962C15.0463 14.0908 15.0995 14.2191 15.0995 14.3529C15.0995 14.4867 15.0463 14.615 14.9517 14.7096C14.8571 14.8042 14.7288 14.8574 14.595 14.8574H14.594ZM14.594 10.8224H12.579C12.4452 10.8224 12.3169 10.7692 12.2223 10.6746C12.1277 10.58 12.0745 10.4517 12.0745 10.3179C12.0745 10.1841 12.1277 10.0558 12.2223 9.96116C12.3169 9.86655 12.4452 9.8134 12.579 9.8134H14.595C14.7288 9.8134 14.8571 9.86655 14.9517 9.96116C15.0463 10.0558 15.0995 10.1841 15.0995 10.3179C15.0995 10.4517 15.0463 10.58 14.9517 10.6746C14.8571 10.7692 14.7288 10.8224 14.595 10.8224H14.594Z"
                fill={fill}
            />
        </svg>
    );
};
