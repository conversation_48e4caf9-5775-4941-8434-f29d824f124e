import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ApIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5.094 17.6404H5.344V19.4064C5.34418 19.4389 5.35415 19.4706 5.37259 19.4973C5.39103 19.5241 5.4171 19.5447 5.44741 19.5565C5.47772 19.5682 5.51087 19.5706 5.54254 19.5633C5.57421 19.5559 5.60294 19.5392 5.625 19.5154L7.516 17.6244H13.532C13.8217 17.6228 14.099 17.507 14.3038 17.3022C14.5087 17.0974 14.6244 16.82 14.626 16.5304V11.4364C14.6244 11.1467 14.5087 10.8694 14.3038 10.6645C14.099 10.4597 13.8217 10.3439 13.532 10.3424H5.094C4.80434 10.3439 4.52699 10.4597 4.32217 10.6645C4.11734 10.8694 4.00158 11.1467 4 11.4364V16.5464C4.00158 16.836 4.11734 17.1134 4.32217 17.3182C4.52699 17.523 4.80434 17.6388 5.094 17.6404ZM11.22 13.4054C11.3343 13.4054 11.4461 13.4393 11.5411 13.5028C11.6362 13.5663 11.7103 13.6566 11.754 13.7622C11.7977 13.8678 11.8092 13.984 11.7869 14.0961C11.7646 14.2082 11.7095 14.3112 11.6287 14.3921C11.5479 14.4729 11.4449 14.528 11.3328 14.5503C11.2206 14.5726 11.1044 14.5611 10.9988 14.5174C10.8932 14.4736 10.8029 14.3995 10.7394 14.3045C10.6759 14.2094 10.642 14.0977 10.642 13.9834C10.6419 13.9075 10.6567 13.8324 10.6856 13.7622C10.7146 13.6921 10.7571 13.6284 10.8106 13.5747C10.8642 13.521 10.9279 13.4784 10.998 13.4494C11.068 13.4203 11.1431 13.4054 11.219 13.4054H11.22ZM9.314 13.4054C9.42832 13.4054 9.54007 13.4393 9.63512 13.5028C9.73017 13.5663 9.80426 13.6566 9.848 13.7622C9.89175 13.8678 9.9032 13.984 9.88089 14.0961C9.85859 14.2082 9.80354 14.3112 9.72271 14.3921C9.64187 14.4729 9.53888 14.528 9.42676 14.5503C9.31464 14.5726 9.19842 14.5611 9.09281 14.5174C8.98719 14.4736 8.89692 14.3995 8.83341 14.3045C8.7699 14.2094 8.736 14.0977 8.736 13.9834C8.73451 13.9071 8.74841 13.8314 8.77686 13.7607C8.80532 13.6899 8.84775 13.6257 8.90162 13.5717C8.95548 13.5177 9.01968 13.4752 9.09036 13.4466C9.16105 13.418 9.23677 13.404 9.313 13.4054H9.314ZM7.408 13.4054C7.5613 13.4054 7.70831 13.4663 7.81671 13.5747C7.9251 13.6831 7.986 13.8301 7.986 13.9834C7.986 14.1367 7.9251 14.2837 7.81671 14.3921C7.70831 14.5005 7.5613 14.5614 7.408 14.5614C7.25471 14.5614 7.10769 14.5005 6.99929 14.3921C6.8909 14.2837 6.83 14.1367 6.83 13.9834C6.83 13.8301 6.8909 13.6831 6.99929 13.5747C7.10769 13.4663 7.25471 13.4054 7.408 13.4054ZM19.22 6.09236V11.1864C19.2184 11.476 19.1027 11.7534 18.8978 11.9582C18.693 12.163 18.4157 12.2788 18.126 12.2804H17.876V14.0464C17.8758 14.0789 17.8659 14.1106 17.8474 14.1373C17.829 14.1641 17.8029 14.1847 17.7726 14.1965C17.7423 14.2082 17.7091 14.2106 17.6775 14.2033C17.6458 14.1959 17.6171 14.1792 17.595 14.1554L15.72 12.3004H15.486V11.4364C15.4857 10.9185 15.2799 10.4219 14.9137 10.0557C14.5475 9.68948 14.0509 9.48363 13.533 9.48336H8.594V6.09236C8.5961 5.80305 8.7121 5.52621 8.91687 5.32182C9.12163 5.11743 9.39868 5.00193 9.688 5.00036H18.111C18.2562 4.99659 18.4007 5.02213 18.5359 5.07546C18.671 5.12879 18.794 5.20881 18.8975 5.31074C19.001 5.41267 19.0829 5.53442 19.1383 5.66871C19.1938 5.80301 19.2215 5.9471 19.22 6.09236Z"
                fill={fill}
            />
        </svg>
    );
};
