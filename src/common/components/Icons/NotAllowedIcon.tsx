import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const NotAllowedIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 21 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.694 14.25C7.52115 14.25 4.94922 11.8996 4.94922 9C4.94922 6.10043 7.52115 3.75 10.694 3.75C13.8668 3.75 16.4387 6.10043 16.4387 9C16.4387 11.8996 13.8668 14.25 10.694 14.25ZM10.694 13.2C11.9129 13.2 13.0818 12.7575 13.9437 11.9698C14.8056 11.1822 15.2898 10.1139 15.2898 9C15.2898 7.88609 14.8056 6.8178 13.9437 6.03015C13.0818 5.2425 11.9129 4.8 10.694 4.8C9.4751 4.8 8.30614 5.2425 7.44425 6.03015C6.58237 6.8178 6.09817 7.88609 6.09817 9C6.09817 10.1139 6.58237 11.1822 7.44425 11.9698C8.30614 12.7575 9.4751 13.2 10.694 13.2ZM14.7153 6.375L7.44425 12.675C7.13023 12.4705 6.8964 11.912 6.67265 11.625L13.8536 5.5875C14.1676 5.79198 14.4916 6.08802 14.7153 6.375Z"
                fill={fill}
            />
        </svg>
    );
};
