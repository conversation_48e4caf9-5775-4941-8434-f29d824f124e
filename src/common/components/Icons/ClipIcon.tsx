import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ClipIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M5.23128 17.7946C6.02929 18.5677 7.09679 19 8.20789 19C9.31898 19 10.3865 18.5677 11.1845 17.7946L19.1221 10.0227C19.3999 9.7519 19.6208 9.42819 19.7715 9.07068C19.9223 8.71316 20 8.32908 20 7.94107C20 7.55306 19.9223 7.16897 19.7715 6.81146C19.6208 6.45394 19.3999 6.13023 19.1221 5.8594C18.5517 5.30814 17.7894 5 16.9961 5C16.2029 5 15.4406 5.30814 14.8702 5.8594L6.93205 13.6312C6.76596 13.7941 6.63401 13.9885 6.54394 14.203C6.45387 14.4175 6.40747 14.6477 6.40747 14.8804C6.40747 15.113 6.45387 15.3433 6.54394 15.5578C6.63401 15.7723 6.76596 15.9667 6.93205 16.1295C7.27502 16.4591 7.73222 16.6432 8.20789 16.6432C8.68355 16.6432 9.14075 16.4591 9.48372 16.1295L16.2889 9.46738C16.3647 9.39566 16.4197 9.30483 16.4483 9.20446C16.4769 9.10409 16.4779 8.99787 16.4512 8.89699C16.4232 8.79536 16.3688 8.70298 16.2935 8.62927C16.2181 8.55557 16.1246 8.50319 16.0223 8.47749C15.9196 8.45092 15.8117 8.45163 15.7093 8.47955C15.6069 8.50747 15.5135 8.56163 15.4385 8.63668L8.63282 15.2968C8.57736 15.3533 8.51119 15.3982 8.43819 15.4288C8.36518 15.4594 8.2868 15.4752 8.20763 15.4752C8.12845 15.4752 8.05007 15.4594 7.97707 15.4288C7.90406 15.3982 7.83789 15.3533 7.78243 15.2968C7.72497 15.2437 7.6791 15.1793 7.64774 15.1076C7.61637 15.036 7.60018 14.9586 7.60018 14.8804C7.60018 14.8022 7.61637 14.7248 7.64774 14.6531C7.6791 14.5815 7.72497 14.5171 7.78243 14.464L15.7185 6.69217C16.0582 6.35635 16.5166 6.16802 16.9943 6.16802C17.472 6.16802 17.9304 6.35635 18.2702 6.69217C18.4394 6.85322 18.5742 7.04699 18.6663 7.26173C18.7584 7.47646 18.8059 7.70767 18.8059 7.94133C18.8059 8.17498 18.7584 8.40619 18.6663 8.62092C18.5742 8.83566 18.4394 9.02943 18.2702 9.19048L10.3341 16.9618C9.76698 17.5194 9.00348 17.8319 8.20815 17.8319C7.41281 17.8319 6.64931 17.5194 6.08219 16.9618C5.80121 16.6928 5.57761 16.3696 5.42486 16.0119C5.2721 15.6541 5.19335 15.2691 5.19335 14.8801C5.19335 14.4911 5.2721 14.1061 5.42486 13.7484C5.57761 13.3906 5.80121 13.0675 6.08219 12.7985L12.8853 6.13682C12.9612 6.06515 13.0163 5.97435 13.045 5.87398C13.0736 5.7736 13.0747 5.66736 13.0481 5.56643C13.0202 5.46473 12.9658 5.37226 12.8904 5.29847C12.8151 5.22468 12.7215 5.17221 12.6193 5.14641C12.5164 5.1198 12.4084 5.12048 12.3059 5.1484C12.2034 5.17632 12.11 5.23051 12.0349 5.3056L5.23128 11.9657C4.84165 12.3445 4.53194 12.7976 4.32045 13.2982C4.10897 13.7988 4 14.3367 4 14.8801C4 15.4236 4.10897 15.9615 4.32045 16.4621C4.53194 16.9626 4.84165 17.4157 5.23128 17.7946Z"
                fill={fill}
            />
        </svg>
    );
};
