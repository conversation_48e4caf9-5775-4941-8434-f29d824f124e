import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const NumbersIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
    style,
}: IconProps) => {
    return (
        <svg
            width={size}
            className={className}
            style={{ minWidth: size, ...(style ?? {}) }}
            height={size}
            viewBox="-4 -4 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M8.70169 2.36364C8.81445 2.50776 9.01179 2.53659 9.23731 2.36364V4.03548H8.50436C8.33521 4.03548 8.16607 4.1796 8.16607 4.38137V5.2173C8.16607 5.39024 8.33521 5.56319 8.50436 5.56319H11.5771C11.7745 5.56319 11.9154 5.39024 11.9154 5.2173V4.38137C11.9154 4.1796 11.7745 4.03548 11.5771 4.03548H10.8724V0.374723C10.8724 0.172949 10.7032 0.0288248 10.5341 0.0288248H9.63198C9.54741 0.0288248 9.46284 0.0576497 9.40646 0.115299L8.16607 1.29712C8.0533 1.41242 8.02511 1.64302 8.16607 1.75831L8.70169 2.36364ZM7.85597 9.25277C7.85597 10.4634 8.81445 11.357 9.91389 11.2129C9.60379 11.4723 9.20912 11.4435 8.84264 11.3282C8.64531 11.2705 8.44797 11.357 8.39159 11.5588L8.13788 12.3947C8.0815 12.5676 8.16607 12.7406 8.33521 12.8271C8.56074 12.9135 8.89902 13 9.40646 13C11.0979 13 12 11.4435 12 9.97339C12 8.1286 10.8442 7.29268 9.77294 7.29268C8.53255 7.29268 7.85597 8.30155 7.85597 9.25277ZM10.4495 9.80044C10.4495 9.82927 10.4495 9.82927 10.4495 9.82927C10.4495 9.85809 10.3368 9.91574 10.0548 9.91574C9.40646 9.91574 9.35008 9.36807 9.35008 9.22395C9.35008 8.99335 9.49103 8.76275 9.71655 8.76275C10.0267 8.76275 10.4495 9.1663 10.4495 9.80044ZM4.95233 9.68514H3.62737V0.461197C3.62737 0.230599 3.40184 0 3.17632 0H2.27422C2.0205 0 1.82316 0.230599 1.82316 0.461197V9.68514H0.470012C0.0471518 9.68514 -0.150183 10.204 0.131724 10.4922L2.38698 12.7982C2.55612 12.9712 2.86622 12.9712 3.03536 12.7982L5.29062 10.4922C5.57253 10.204 5.37519 9.68514 4.95233 9.68514Z"
                fill={fill}
            />
        </svg>
    );
};
