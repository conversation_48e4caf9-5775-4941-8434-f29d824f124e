import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PowerIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.172 3.00001C12.0473 2.99962 11.9237 3.02389 11.8084 3.07144C11.693 3.119 11.5883 3.18888 11.5001 3.27709C11.4119 3.36529 11.342 3.47006 11.2944 3.58538C11.2469 3.7007 11.2226 3.82428 11.223 3.94901V10.206C11.223 10.458 11.3231 10.6996 11.5012 10.8778C11.6794 11.0559 11.921 11.156 12.173 11.156C12.425 11.156 12.6666 11.0559 12.8447 10.8778C13.0229 10.6996 13.123 10.458 13.123 10.206V3.94901C13.1234 3.82411 13.0991 3.70036 13.0514 3.58491C13.0037 3.46947 12.9336 3.3646 12.8452 3.27637C12.7568 3.18814 12.6518 3.1183 12.5362 3.07087C12.4207 3.02343 12.2969 2.99935 12.172 3.00001ZM9.014 6.66601C8.86175 6.67656 8.71428 6.72354 8.584 6.80301C7.21565 7.59288 6.14619 8.8122 5.54148 10.2718C4.93677 11.7315 4.83061 13.3499 5.23947 14.876C5.64833 16.4022 6.54936 17.7507 7.8028 18.7126C9.05624 19.6744 10.592 20.1958 12.172 20.1958C13.752 20.1958 15.2878 19.6744 16.5412 18.7126C17.7946 17.7507 18.6957 16.4022 19.1045 14.876C19.5134 13.3499 19.4072 11.7315 18.8025 10.2718C18.1978 8.8122 17.1283 7.59288 15.76 6.80301C15.6515 6.73327 15.53 6.68628 15.4029 6.6649C15.2757 6.64352 15.1455 6.6482 15.0202 6.67865C14.8949 6.7091 14.7771 6.76469 14.674 6.84205C14.5708 6.9194 14.4844 7.01691 14.4201 7.12865C14.3557 7.2404 14.3147 7.36405 14.2996 7.49211C14.2845 7.62017 14.2956 7.74996 14.3321 7.87362C14.3687 7.99728 14.43 8.11222 14.5123 8.21147C14.5946 8.31073 14.6962 8.39222 14.811 8.45101C15.8163 9.03187 16.6019 9.92819 17.046 11.001C17.49 12.0738 17.5677 13.2631 17.267 14.3845C16.9663 15.506 16.3041 16.4969 15.3829 17.2036C14.4617 17.9104 13.3331 18.2934 12.172 18.2934C11.0109 18.2934 9.88231 17.9104 8.96113 17.2036C8.03994 16.4969 7.37765 15.506 7.07696 14.3845C6.77627 13.2631 6.85398 12.0738 7.29804 11.001C7.74209 9.92819 8.52768 9.03187 9.533 8.45101C9.72307 8.34693 9.87195 8.18111 9.95502 7.98096C10.0381 7.78081 10.0504 7.5583 9.9899 7.35021C9.9294 7.14213 9.79971 6.96091 9.62227 6.8365C9.44482 6.7121 9.23025 6.65196 9.014 6.66601Z"
                fill={fill}
            />
        </svg>
    );
};
