import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CheckIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.7874 7.00998C17.5195 7.04528 17.2717 7.17069 17.0853 7.36533L10.0422 14.4128L6.92344 11.9371C6.80208 11.8405 6.66277 11.7686 6.51347 11.7255C6.36417 11.6825 6.2078 11.6691 6.0533 11.6861C5.89879 11.7031 5.74918 11.7503 5.61299 11.8248C5.4768 11.8993 5.35671 11.9998 5.25957 12.1205C5.16243 12.2412 5.09015 12.3797 5.04685 12.5282C5.00355 12.6766 4.99008 12.8321 5.00721 12.9857C5.02434 13.1394 5.07173 13.2881 5.14669 13.4236C5.22164 13.559 5.32268 13.6784 5.44405 13.775L9.38821 16.9126C9.61707 17.0925 9.90468 17.182 10.1959 17.164C10.487 17.1459 10.7612 17.0216 10.9659 16.8149L18.7603 9.01939C18.9377 8.84722 19.0563 8.62401 19.0993 8.38123C19.1423 8.13845 19.1076 7.88834 19 7.66624C18.8924 7.44415 18.7174 7.26126 18.4997 7.14335C18.282 7.02545 18.0325 6.97849 17.7865 7.00911L17.7874 7.00998Z"
                fill={fill}
            />
        </svg>
    );
};
