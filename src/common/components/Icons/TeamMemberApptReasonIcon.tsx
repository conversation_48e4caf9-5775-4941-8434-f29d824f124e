import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const TeamMemberApptReasonIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.0909 16.1818C17.0909 16.3456 17.0239 16.5027 16.9045 16.6185C16.7852 16.7343 16.6233 16.7993 16.4545 16.7993H7.54545C7.37668 16.7993 7.21482 16.7343 7.09548 16.6185C6.97614 16.5027 6.90909 16.3456 6.90909 16.1818V10.6241H5L11.5717 4.82679C11.6889 4.72334 11.8416 4.66602 12 4.66602C12.1584 4.66602 12.3111 4.72334 12.4283 4.82679L19 10.6241H17.0909V16.1818Z"
                fill={fill}
            />
        </svg>
    );
};
