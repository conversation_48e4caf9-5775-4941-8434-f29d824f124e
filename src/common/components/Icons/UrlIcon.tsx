import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const UrlIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.1619 5.09212C17.8164 4.74606 17.4061 4.47153 16.9544 4.28421C16.5027 4.0969 16.0185 4.00049 15.5294 4.00049C15.0404 4.00049 14.5562 4.0969 14.1045 4.28421C13.6528 4.47153 13.2425 4.74606 12.8969 5.09212L11.1789 6.79212C11.1009 6.87012 11.0391 6.96273 10.9968 7.06464C10.9546 7.16656 10.9329 7.2758 10.9329 7.38612C10.9329 7.49643 10.9546 7.60567 10.9968 7.70759C11.0391 7.8095 11.1009 7.90211 11.1789 7.98012C11.2569 8.05812 11.3496 8.12 11.4515 8.16221C11.5534 8.20443 11.6626 8.22616 11.7729 8.22616C11.8833 8.22616 11.9925 8.20443 12.0944 8.16221C12.1963 8.12 12.2889 8.05812 12.3669 7.98012L14.0669 6.28012C14.4533 5.90945 14.968 5.70248 15.5034 5.70248C16.0389 5.70248 16.5536 5.90945 16.9399 6.28012C17.3179 6.6627 17.5298 7.17882 17.5298 7.71662C17.5298 8.25441 17.3179 8.77053 16.9399 9.15312L14.1329 12.0101C13.7466 12.3808 13.2319 12.5878 12.6964 12.5878C12.161 12.5878 11.6463 12.3808 11.2599 12.0101C11.1021 11.8526 10.8882 11.7642 10.6652 11.7644C10.4423 11.7646 10.2285 11.8533 10.0709 12.0111C9.91341 12.1689 9.82501 12.3828 9.8252 12.6058C9.82538 12.8288 9.91414 13.0426 10.0719 13.2001C10.4175 13.5462 10.8278 13.8207 11.2795 14.008C11.7312 14.1953 12.2154 14.2917 12.7044 14.2917C13.1935 14.2917 13.6777 14.1953 14.1294 14.008C14.5811 13.8207 14.9914 13.5462 15.3369 13.2001L18.1779 10.3591C18.5254 10.0139 18.8008 9.60315 18.9882 9.15068C19.1757 8.69822 19.2715 8.21303 19.27 7.72327C19.2685 7.23351 19.1698 6.74892 18.9796 6.2976C18.7894 5.84628 18.5115 5.43721 18.1619 5.09412V5.09212Z"
                fill={fill}
            />
            <path
                d="M10.874 15.252L9.17397 16.97C8.98533 17.1588 8.76135 17.3086 8.51482 17.4108C8.2683 17.513 8.00406 17.5657 7.73718 17.5658C7.4703 17.5659 7.20602 17.5134 6.95943 17.4113C6.71283 17.3093 6.48875 17.1597 6.29997 16.971C6.1112 16.7824 5.96143 16.5584 5.85921 16.3119C5.757 16.0654 5.70434 15.8011 5.70425 15.5342C5.70415 15.2674 5.75663 15.0031 5.85867 14.7565C5.96071 14.5099 6.11033 14.2858 6.29897 14.097L9.13997 11.256C9.52256 10.8781 10.0387 10.6661 10.5765 10.6661C11.1143 10.6661 11.6304 10.8781 12.013 11.256C12.1705 11.4136 12.3842 11.5021 12.607 11.5021C12.8298 11.5021 13.0434 11.4136 13.201 11.256C13.3585 11.0985 13.447 10.8848 13.447 10.662C13.447 10.4392 13.3585 10.2256 13.201 10.068C12.8561 9.72091 12.4459 9.44542 11.9941 9.25743C11.5423 9.06944 11.0578 8.97266 10.5685 8.97266C10.0791 8.97266 9.59461 9.06944 9.14282 9.25743C8.69103 9.44542 8.28088 9.72091 7.93597 10.068L5.09597 12.897C4.74647 13.2419 4.46865 13.6525 4.27848 14.1052C4.08832 14.5579 3.98957 15.0437 3.98794 15.5347C3.9863 16.0257 4.0818 16.5121 4.26894 16.9661C4.45608 17.42 4.73117 17.8325 5.07836 18.1797C5.42555 18.5268 5.83798 18.8019 6.29192 18.9891C6.74586 19.1762 7.23233 19.2717 7.72333 19.2701C8.21433 19.2684 8.70015 19.1697 9.15283 18.9795C9.60551 18.7894 10.0161 18.5115 10.361 18.162L12.061 16.462C12.1409 16.3851 12.2046 16.2929 12.248 16.1908C12.2915 16.0888 12.3139 15.979 12.3139 15.868C12.3139 15.7571 12.2915 15.6473 12.248 15.5452C12.2046 15.4432 12.1409 15.3509 12.061 15.274C11.9846 15.1945 11.8932 15.1308 11.7922 15.0866C11.6911 15.0424 11.5823 15.0186 11.472 15.0166C11.3618 15.0145 11.2522 15.0343 11.1495 15.0747C11.0469 15.1151 10.9533 15.1754 10.874 15.252Z"
                fill={fill}
            />
        </svg>
    );
};
