import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const VehicleIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_36614_72565)">
                <rect width={'18'} height="18" rx="9" fill={fill} />
                <path
                    d="M13.8117 8.39015L13.5444 7.72188L13.0584 6.52853C12.6453 5.50226 11.649 4.83398 10.5311 4.83398H7.44491C6.32707 4.83398 5.33074 5.50226 4.91763 6.52853L4.43161 7.72188L4.1643 8.39015C3.58108 8.60495 3.16797 9.15389 3.16797 9.79829V10.9439C3.16797 11.3496 3.31377 11.6838 3.55678 11.9463V13.2351C3.55678 13.6647 3.89699 13.9989 4.33441 13.9989H5.11203C5.52515 13.9989 5.88966 13.6647 5.88966 13.2351V12.4714H12.1107V13.2351C12.1107 13.6647 12.4509 13.9989 12.8883 13.9989H13.6659C14.079 13.9989 14.4435 13.6647 14.4435 13.2351V11.9463C14.6623 11.6838 14.8324 11.3496 14.8324 10.9439V9.79829C14.8324 9.15389 14.3949 8.60495 13.8117 8.39015ZM6.35137 7.10134C6.54578 6.64787 6.95889 6.36146 7.44491 6.36146H10.5311C11.0171 6.36146 11.4302 6.64787 11.6247 7.10134L12.1107 8.27081H5.88966L6.35137 7.10134ZM5.11203 10.9439C4.62602 10.9439 4.33441 10.6575 4.33441 10.1802C4.33441 9.72669 4.62602 9.41642 5.11203 9.41642C5.57375 9.41642 6.27847 10.1086 6.27847 10.562C6.27847 11.0155 5.57375 10.9439 5.11203 10.9439ZM12.8883 10.9439C12.4023 10.9439 11.7219 11.0155 11.7219 10.562C11.7219 10.1086 12.4023 9.41642 12.8883 9.41642C13.35 9.41642 13.6659 9.72669 13.6659 10.1802C13.6659 10.6575 13.35 10.9439 12.8883 10.9439Z"
                    fill="white"
                />
            </g>
            <defs>
                <clipPath id="clip0_36614_72565">
                    <rect width="18" height="18" rx="9" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};
