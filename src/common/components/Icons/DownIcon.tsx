import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DownIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className = '',
    style,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size, ...(style ?? {}) }}
            className={className}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.4325 14.84L16.8171 10.9492C16.9342 10.8449 17 10.7035 17 10.556C17 10.4086 16.9342 10.2671 16.8171 10.1629C16.7 10.0586 16.5412 10 16.3755 10C16.2099 10 16.0511 10.0586 15.934 10.1629L11.9984 13.6677L8.06282 10.1629C7.94473 10.0603 7.78627 10.0028 7.62127 10.0028C7.45628 10.0028 7.29782 10.0603 7.17972 10.1629C7.0645 10.268 7 10.4091 7 10.556C7 10.7029 7.0645 10.844 7.17972 10.9492L11.5494 14.84C11.6675 14.9426 11.826 15 11.991 15C12.156 15 12.3144 14.9426 12.4325 14.84Z"
                fill={fill}
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.0166 11.173C17.193 11.0159 17.3 10.7948 17.3 10.5558C17.3 10.3168 17.193 10.0957 17.0166 9.9386C16.8412 9.78249 16.6102 9.69981 16.3755 9.69981C16.1408 9.69981 15.9098 9.78249 15.7344 9.9386L11.9984 13.2658L8.25953 9.93616C8.08369 9.78341 7.85421 9.70264 7.62122 9.70264C7.38823 9.70264 7.15877 9.78342 6.98292 9.93618L6.97738 9.941C6.80421 10.099 6.69995 10.3189 6.69995 10.5558C6.69995 10.7927 6.80427 11.0125 6.97744 11.1706L6.98015 11.173L11.3526 15.0663C11.5285 15.219 11.7579 15.2998 11.9909 15.2998C12.2239 15.2998 12.4534 15.219 12.6292 15.0663L12.4325 14.8398C12.3144 14.9424 12.1559 14.9998 11.9909 14.9998C11.8259 14.9998 11.6675 14.9424 11.5494 14.8398L7.17967 10.949C7.06445 10.8438 6.99995 10.7027 6.99995 10.5558C6.99995 10.4089 7.06445 10.2678 7.17967 10.1627C7.29777 10.0601 7.45623 10.0026 7.62122 10.0026C7.78622 10.0026 7.94468 10.0601 8.06278 10.1627L11.9984 13.6675L15.9339 10.1627C16.0511 10.0584 16.2099 9.99981 16.3755 9.99981C16.5411 9.99981 16.6999 10.0584 16.8171 10.1627C16.9342 10.2669 17 10.4084 17 10.5558C17 10.7033 16.9342 10.8447 16.8171 10.949L12.4325 14.8398L12.6316 15.0642L17.0166 11.173Z"
                fill={fill}
            />
        </svg>
    );
};
