import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const BlockedUserIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M21.064 19.7309C20.8828 19.7308 20.7056 19.678 20.554 19.5789L18.446 18.2099C18.4746 18.4706 18.4889 18.7326 18.489 18.9949H4.26497C4.26513 17.4857 4.74532 16.0157 5.63607 14.7974C6.52681 13.5792 7.78194 12.6758 9.21999 12.2179L2.428 7.80788C2.22551 7.66927 2.0852 7.45702 2.03713 7.21639C1.98906 6.97575 2.03704 6.72586 2.17073 6.5201C2.30443 6.31433 2.5133 6.16898 2.7527 6.11513C2.99211 6.06128 3.24306 6.10319 3.45198 6.23189L7.67299 8.97292C7.72029 9.629 7.94076 10.2608 8.31197 10.8038C8.68318 11.3468 9.19182 11.7817 9.78597 12.0639C9.60297 12.1059 9.41299 12.1569 9.21999 12.2179L18.447 18.2089C18.3368 17.1935 18.0065 16.2145 17.479 15.3399L21.579 18.0019C21.7489 18.1124 21.8785 18.2749 21.9486 18.4651C22.0186 18.6553 22.0253 18.8631 21.9676 19.0574C21.9099 19.2517 21.791 19.4222 21.6285 19.5434C21.466 19.6646 21.2687 19.73 21.066 19.7299L21.064 19.7309ZM17.474 15.3389L7.67397 8.97389C7.66797 8.88489 7.665 8.79791 7.665 8.71291C7.66415 8.04803 7.84178 7.39514 8.17946 6.8224C8.51714 6.24966 9.00243 5.77805 9.58461 5.45693C10.1668 5.1358 10.8245 4.97688 11.4891 4.99678C12.1537 5.01668 12.8007 5.2147 13.3626 5.57008C13.9246 5.92547 14.3808 6.42525 14.6836 7.01717C14.9864 7.60909 15.1246 8.27144 15.084 8.93507C15.0434 9.59871 14.8253 10.2393 14.4525 10.7898C14.0798 11.3404 13.5661 11.7807 12.965 12.0649C13.8971 12.2813 14.7763 12.6825 15.5506 13.2446C16.3249 13.8068 16.9786 14.5186 17.473 15.3379L17.474 15.3389Z"
                fill={fill}
            />
        </svg>
    );
};
