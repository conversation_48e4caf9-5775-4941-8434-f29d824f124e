import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { Icon, IconProps } from './Icon';

const PrintIcon: typeof Icon = ({ fill = Colors.CM2, size = IconSize.M }: IconProps) => {
    return (
        <svg
            width={size}
            height={size}
            viewBox="-4 -5 25 25"
            fill={fill}
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M3.24992 14.0416H0.874919C0.664956 14.0416 0.463592 13.9582 0.315126 13.8097C0.166659 13.6612 0.083252 13.4599 0.083252 13.2499V5.33325C0.083252 5.12329 0.166659 4.92193 0.315126 4.77346C0.463592 4.62499 0.664956 4.54158 0.874919 4.54158H3.24992V1.37492C3.24992 1.16496 3.33333 0.963592 3.48179 0.815126C3.63026 0.666659 3.83162 0.583252 4.04158 0.583252H11.9583C12.1682 0.583252 12.3696 0.666659 12.518 0.815126C12.6665 0.963592 12.7499 1.16496 12.7499 1.37492V4.54158H15.1249C15.3349 4.54158 15.5362 4.62499 15.6847 4.77346C15.8332 4.92193 15.9166 5.12329 15.9166 5.33325V13.2499C15.9166 13.4599 15.8332 13.6612 15.6847 13.8097C15.5362 13.9582 15.3349 14.0416 15.1249 14.0416H12.7499V15.6249C12.7499 15.8349 12.6665 16.0362 12.518 16.1847C12.3696 16.3332 12.1682 16.4166 11.9583 16.4166H4.04158C3.83162 16.4166 3.63026 16.3332 3.48179 16.1847C3.33333 16.0362 3.24992 15.8349 3.24992 15.6249V14.0416ZM3.24992 12.4583V11.6666C3.24992 11.4566 3.33333 11.2553 3.48179 11.1068C3.63026 10.9583 3.83162 10.8749 4.04158 10.8749H11.9583C12.1682 10.8749 12.3696 10.9583 12.518 11.1068C12.6665 11.2553 12.7499 11.4566 12.7499 11.6666V12.4583H14.3333V6.12492H1.66659V12.4583H3.24992ZM4.83325 2.16659V4.54158H11.1666V2.16659H4.83325ZM4.83325 12.4583V14.8333H11.1666V12.4583H4.83325ZM2.45825 6.91658H4.83325V8.49992H2.45825V6.91658Z"
                fill="#0069FF"
            />
        </svg>
    );
};

export default PrintIcon;
