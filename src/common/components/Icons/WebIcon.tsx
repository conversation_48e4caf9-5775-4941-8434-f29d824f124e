import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const WebIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 22 22"
            className={className}
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g id="web">
                <path
                    id="Vector"
                    d="M14.9954 12.834C15.0687 12.229 15.1237 11.624 15.1237 11.0007C15.1237 10.3773 15.0687 9.77232 14.9954 9.16732H18.0937C18.2404 9.75398 18.332 10.3682 18.332 11.0007C18.332 11.6332 18.2404 12.2473 18.0937 12.834H14.9954ZM13.3729 17.9307C13.9229 16.9132 14.3445 15.8132 14.6379 14.6673H17.342C16.462 16.1798 15.0595 17.3532 13.3729 17.9307ZM13.1437 12.834H8.8537C8.76203 12.229 8.70703 11.624 8.70703 11.0007C8.70703 10.3773 8.76203 9.76315 8.8537 9.16732H13.1437C13.2262 9.76315 13.2904 10.3773 13.2904 11.0007C13.2904 11.624 13.2262 12.229 13.1437 12.834ZM10.9987 18.2973C10.2379 17.1973 9.6237 15.9782 9.24786 14.6673H12.7495C12.3737 15.9782 11.7595 17.1973 10.9987 18.2973ZM7.33203 7.33398H4.65536C5.5262 5.81232 6.93786 4.63898 8.61536 4.07065C8.06536 5.08815 7.65286 6.18815 7.33203 7.33398ZM4.65536 14.6673H7.33203C7.65286 15.8132 8.06536 16.9132 8.61536 17.9307C6.93786 17.3532 5.5262 16.1798 4.65536 14.6673ZM3.9037 12.834C3.75703 12.2473 3.66536 11.6332 3.66536 11.0007C3.66536 10.3682 3.75703 9.75398 3.9037 9.16732H7.00203C6.9287 9.77232 6.8737 10.3773 6.8737 11.0007C6.8737 11.624 6.9287 12.229 7.00203 12.834H3.9037ZM10.9987 3.69482C11.7595 4.79482 12.3737 6.02315 12.7495 7.33398H9.24786C9.6237 6.02315 10.2379 4.79482 10.9987 3.69482ZM17.342 7.33398H14.6379C14.3445 6.18815 13.9229 5.08815 13.3729 4.07065C15.0595 4.64815 16.462 5.81232 17.342 7.33398ZM10.9987 1.83398C5.92953 1.83398 1.83203 5.95898 1.83203 11.0007C1.83203 13.4318 2.7978 15.7634 4.51689 17.4825C5.36809 18.3337 6.37861 19.0089 7.49077 19.4695C8.60292 19.9302 9.79491 20.1673 10.9987 20.1673C13.4298 20.1673 15.7614 19.2015 17.4805 17.4825C19.1996 15.7634 20.1654 13.4318 20.1654 11.0007C20.1654 9.79687 19.9283 8.60487 19.4676 7.49272C19.0069 6.38057 18.3317 5.37004 17.4805 4.51884C16.6293 3.66764 15.6188 2.99242 14.5066 2.53176C13.3945 2.07109 12.2025 1.83398 10.9987 1.83398Z"
                    fill={fill}
                />
            </g>
        </svg>
    );
};
