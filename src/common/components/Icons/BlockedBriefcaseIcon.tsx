import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const BlockedBriefcaseIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M21.1626 20C21.0004 20 20.8416 19.9552 20.7056 19.871L18.8364 18.7134C18.9666 18.7018 19.093 18.6657 19.2083 18.6071C19.3237 18.5485 19.4257 18.4686 19.5085 18.3721C19.6055 18.2554 19.6588 18.111 19.6597 17.962V17.3147L21.6171 18.5268C21.7691 18.6208 21.8852 18.7593 21.948 18.9214C22.0108 19.0836 22.0169 19.2608 21.9654 19.4265C21.9139 19.5922 21.8075 19.7376 21.6622 19.841C21.5169 19.9444 21.3405 20.0002 21.1592 20H21.1626ZM18.7365 18.7182H5.29537C5.18756 18.7234 5.07978 18.7077 4.9785 18.6722C4.87721 18.6366 4.78452 18.5818 4.70603 18.5111C4.62754 18.4405 4.56484 18.3554 4.52175 18.261C4.47866 18.1667 4.45607 18.0649 4.4553 17.962V9.80683L18.8373 18.7126C18.8028 18.7158 18.7692 18.7174 18.7365 18.7174V18.7182ZM19.6605 17.3147L5.27435 8.40329H8.66235V6.8011C8.66235 6.58863 8.75087 6.38488 8.90842 6.23465C9.06596 6.08441 9.27962 6 9.50242 6H14.5428C14.7656 6 14.9793 6.08441 15.1368 6.23465C15.2944 6.38488 15.3829 6.58863 15.3829 6.8011V8.40329H18.7365C18.9676 8.40238 19.1908 8.48416 19.3619 8.63247C19.533 8.78077 19.6395 8.98483 19.6605 9.20439V17.3147ZM10.3358 7.23529V8.40329H13.696V7.23529H10.3358ZM4.44771 9.80683L2.38282 8.52504C2.19589 8.40936 2.06481 8.22761 2.01842 8.01976C1.97202 7.81191 2.01411 7.59499 2.13542 7.41674C2.25674 7.23848 2.44737 7.11348 2.66534 7.06924C2.8833 7.02499 3.11077 7.06513 3.29769 7.18082L5.27435 8.40329C5.12076 8.40778 4.97137 8.45195 4.84206 8.53111C4.71274 8.61026 4.6084 8.7214 4.54016 8.85269C4.4843 8.96196 4.45552 9.08205 4.45612 9.20361V9.806L4.44771 9.80683Z"
                fill={fill}
            />
        </svg>
    );
};
