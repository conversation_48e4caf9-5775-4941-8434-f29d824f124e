import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const BoldIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.0459 18.9998H13.2465C16.303 18.9998 18.0573 17.367 18.0573 15.1337C18.0573 13.1365 16.6066 11.8816 14.9333 11.8074V11.6724C16.4514 11.3553 17.5175 10.2555 17.5175 8.67667C17.5175 6.5918 15.9117 5.18164 12.808 5.18164H7.0459V18.9998ZM10.3857 16.3077V13.0151H12.5111C13.7863 13.0151 14.569 13.6898 14.569 14.7491C14.569 15.7275 13.8943 16.3077 12.4504 16.3077H10.3857ZM10.3857 10.8627V7.83327H12.2884C13.4017 7.83327 14.1102 8.40678 14.1102 9.3244C14.1102 10.296 13.3275 10.8627 12.2345 10.8627H10.3857Z"
                fill={fill}
            />
        </svg>
    );
};
