import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PersonCheckmarkIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M 9.3025 11.5 C 11.272 11.5 12.9039 9.8301 12.9039 7.75 C 12.9039 5.6992 11.272 4 9.3025 4 C 7.3048 4 5.7011 5.6992 5.7011 7.75 C 5.7011 9.8301 7.3048 11.5 9.3025 11.5 Z M 11.8066 12.4375 H 11.3283 C 10.7093 12.7598 10.034 12.9062 9.3025 12.9062 C 8.5709 12.9062 7.8675 12.7598 7.2485 12.4375 H 6.7702 C 4.6882 12.4375 3 14.2246 3 16.3926 V 17.5938 C 3 18.3848 3.5909 19 4.3505 19 H 14.2544 C 14.9859 19 15.6049 18.3848 15.6049 17.5938 V 16.3926 C 15.6049 14.2246 13.8886 12.4375 11.8066 12.4375 Z M 20.8944 8.6875 L 20.1066 7.8672 C 19.9941 7.7207 19.769 7.7207 19.6565 7.8672 L 16.7022 10.9141 L 15.408 9.5664 C 15.2954 9.4199 15.0703 9.4199 14.9297 9.5664 L 14.1419 10.3867 C 14.0293 10.5039 14.0293 10.7383 14.1419 10.8848 L 16.449 13.2871 C 16.5897 13.4336 16.7866 13.4336 16.9273 13.2871 L 20.8944 9.1855 C 21.0352 9.0391 21.0352 8.834 20.8944 8.6875 Z"
                fill={fill}
            />
        </svg>
    );
};
