import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const TuneIcon: typeof Icon = ({
    fill = 'currentColor',
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.01291 17.7103C6.96971 17.7184 6.92585 17.7224 6.88191 17.7223H4.71991C4.53028 17.7223 4.34841 17.647 4.21432 17.5129C4.08024 17.3788 4.00491 17.1969 4.00491 17.0073C4.00491 16.8177 4.08024 16.6358 4.21432 16.5017C4.34841 16.3676 4.53028 16.2923 4.71991 16.2923H6.88191C6.92576 16.2912 6.96961 16.2942 7.01291 16.3013C7.15978 15.8811 7.43343 15.5169 7.79611 15.2589C8.15879 15.0008 8.59262 14.8617 9.03772 14.8607C9.48282 14.8597 9.91726 14.9969 10.2811 15.2533C10.6449 15.5098 10.9202 15.8728 11.0689 16.2923H19.1299C19.3198 16.2923 19.5019 16.3677 19.6362 16.502C19.7705 16.6363 19.8459 16.8184 19.8459 17.0083C19.8459 17.1982 19.7705 17.3803 19.6362 17.5146C19.5019 17.6489 19.3198 17.7243 19.1299 17.7243H11.0689C10.9199 18.1437 10.6443 18.5065 10.2803 18.7627C9.9163 19.0188 9.48176 19.1557 9.03666 19.1544C8.59155 19.1531 8.15783 19.0136 7.79534 18.7553C7.43285 18.497 7.15946 18.1326 7.01291 17.7123V17.7103ZM12.7799 13.4303H4.71991C4.62588 13.4303 4.53277 13.4118 4.4459 13.3758C4.35904 13.3398 4.2801 13.2871 4.21362 13.2206C4.14713 13.1541 4.09439 13.0752 4.05841 12.9883C4.02243 12.9014 4.00391 12.8083 4.00391 12.7143C4.00391 12.6203 4.02243 12.5272 4.05841 12.4403C4.09439 12.3534 4.14713 12.2745 4.21362 12.208C4.2801 12.1415 4.35904 12.0888 4.4459 12.0528C4.53277 12.0168 4.62588 11.9983 4.71991 11.9983H12.7809C12.9295 11.5798 13.2041 11.2177 13.5669 10.9615C13.9296 10.7054 14.3628 10.5679 14.8069 10.5679C15.251 10.5679 15.6842 10.7054 16.0469 10.9615C16.4097 11.2177 16.6843 11.5798 16.8329 11.9983H19.1329C19.3228 11.9983 19.5049 12.0737 19.6392 12.208C19.7735 12.3423 19.8489 12.5244 19.8489 12.7143C19.8489 12.9042 19.7735 13.0863 19.6392 13.2206C19.5049 13.3549 19.3228 13.4303 19.1329 13.4303H16.8329C16.6843 13.8488 16.4097 14.2109 16.0469 14.4671C15.6842 14.7232 15.251 14.8607 14.8069 14.8607C14.3628 14.8607 13.9296 14.7232 13.5669 14.4671C13.2041 14.2109 12.9295 13.8488 12.7809 13.4303H12.7799ZM8.45791 9.1373H4.71991C4.62588 9.1373 4.53277 9.11878 4.4459 9.0828C4.35904 9.04682 4.2801 8.99408 4.21362 8.92759C4.14713 8.8611 4.09439 8.78217 4.05841 8.6953C4.02243 8.60843 4.00391 8.51533 4.00391 8.4213C4.00391 8.32728 4.02243 8.23417 4.05841 8.1473C4.09439 8.06043 4.14713 7.9815 4.21362 7.91501C4.2801 7.84853 4.35904 7.79579 4.4459 7.7598C4.53277 7.72382 4.62588 7.7053 4.71991 7.7053H8.45791C8.60654 7.28684 8.8811 6.92466 9.24387 6.66854C9.60665 6.41241 10.0398 6.2749 10.4839 6.2749C10.928 6.2749 11.3612 6.41241 11.7239 6.66854C12.0867 6.92466 12.3613 7.28684 12.5099 7.7053H19.1299C19.2239 7.7053 19.317 7.72382 19.4039 7.7598C19.4908 7.79579 19.5697 7.84853 19.6362 7.91501C19.7027 7.9815 19.7554 8.06043 19.7914 8.1473C19.8274 8.23417 19.8459 8.32728 19.8459 8.4213C19.8459 8.51533 19.8274 8.60843 19.7914 8.6953C19.7554 8.78217 19.7027 8.8611 19.6362 8.92759C19.5697 8.99408 19.4908 9.04682 19.4039 9.0828C19.317 9.11878 19.2239 9.1373 19.1299 9.1373H12.5099C12.3613 9.55577 12.0867 9.91794 11.7239 10.1741C11.3612 10.4302 10.928 10.5677 10.4839 10.5677C10.0398 10.5677 9.60665 10.4302 9.24387 10.1741C8.8811 9.91794 8.60654 9.55577 8.45791 9.1373Z"
                fill={fill}
            />
        </svg>
    );
};
