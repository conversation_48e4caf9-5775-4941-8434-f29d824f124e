import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MicIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12 14.375C13.4492 14.375 14.625 13.2266 14.625 11.75V7.375C14.625 5.92578 13.4492 4.75 12 4.75C10.5234 4.75 9.375 5.92578 9.375 7.375V11.75C9.375 13.2266 10.5234 14.375 12 14.375ZM16.375 10H15.9375C15.6914 10 15.5 10.2188 15.5 10.4375V11.75C15.5 13.8008 13.7227 15.4414 11.6445 15.25C9.8125 15.0586 8.5 13.4453 8.5 11.6133V10.4375C8.5 10.2188 8.28125 10 8.0625 10H7.625C7.37891 10 7.1875 10.2188 7.1875 10.4375V11.5586C7.1875 13.9922 8.91016 16.1797 11.3438 16.5078V17.4375H9.8125C9.56641 17.4375 9.375 17.6562 9.375 17.875V18.3125C9.375 18.5586 9.56641 18.75 9.8125 18.75H14.1875C14.4062 18.75 14.625 18.5586 14.625 18.3125V17.875C14.625 17.6562 14.4062 17.4375 14.1875 17.4375H12.6562V16.5352C14.9805 16.207 16.8125 14.1836 16.8125 11.75V10.4375C16.8125 10.2188 16.5938 10 16.375 10Z"
                fill={fill}
            />
        </svg>
    );
};
