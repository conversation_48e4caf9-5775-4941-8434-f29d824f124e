import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CodeIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.8988 18.1031H10.7068C10.6079 18.0764 10.5154 18.0303 10.4346 17.9674C10.3538 17.9045 10.2864 17.826 10.2363 17.7367C10.1863 17.6473 10.1546 17.5488 10.1432 17.4471C10.1318 17.3453 10.1408 17.2423 10.1698 17.1441L13.2398 6.43805C13.3173 6.27754 13.4482 6.14898 13.6101 6.07439C13.7719 5.9998 13.9547 5.98384 14.1271 6.02923C14.2994 6.07463 14.4506 6.17855 14.5548 6.3232C14.6589 6.46784 14.7095 6.6442 14.6978 6.82205L11.6278 17.567C11.5845 17.7254 11.4889 17.8646 11.3566 17.9618C11.2242 18.0591 11.063 18.1089 10.8988 18.1031Z"
                fill={fill}
            />
            <path
                d="M8.59594 16.1853C8.75103 16.1835 8.90193 16.1348 9.02879 16.0455C9.15564 15.9563 9.2525 15.8307 9.30662 15.6853C9.36074 15.54 9.36957 15.3817 9.33197 15.2312C9.29436 15.0807 9.21207 14.9451 9.09594 14.8423L5.90994 11.9633L9.09494 9.08533C9.22064 8.94607 9.29102 8.76561 9.2928 8.57802C9.29458 8.39042 9.22763 8.20866 9.10459 8.06704C8.98155 7.92542 8.81093 7.83373 8.62492 7.80929C8.43892 7.78484 8.2504 7.82932 8.09494 7.93433L4.25994 11.3883C4.17838 11.4604 4.11308 11.549 4.06835 11.6482C4.02362 11.7474 4.00049 11.855 4.00049 11.9638C4.00049 12.0727 4.02362 12.1803 4.06835 12.2795C4.11308 12.3787 4.17838 12.4673 4.25994 12.5393L8.09694 15.9933C8.2332 16.1185 8.41195 16.1871 8.59694 16.1853H8.59594Z"
                fill={fill}
            />
            <path
                d="M16.2707 16.185C16.1156 16.1832 15.9647 16.1345 15.8379 16.0452C15.711 15.956 15.6142 15.8304 15.56 15.6851C15.5059 15.5397 15.4971 15.3814 15.5347 15.2309C15.5723 15.0804 15.6546 14.9449 15.7707 14.842L18.9557 11.964L15.7707 9.08605C15.6806 9.0252 15.6045 8.94593 15.5474 8.85348C15.4902 8.76103 15.4533 8.65749 15.4392 8.54973C15.425 8.44196 15.4339 8.33241 15.4652 8.22833C15.4965 8.12425 15.5496 8.028 15.6209 7.94594C15.6921 7.86389 15.78 7.79791 15.8787 7.75235C15.9774 7.70679 16.0846 7.68271 16.1933 7.68168C16.302 7.68064 16.4097 7.7027 16.5092 7.74637C16.6088 7.79005 16.6979 7.85436 16.7707 7.93505L20.6087 11.389C20.6903 11.4611 20.7556 11.5497 20.8003 11.6489C20.845 11.7481 20.8682 11.8557 20.8682 11.9645C20.8682 12.0734 20.845 12.181 20.8003 12.2802C20.7556 12.3794 20.6903 12.468 20.6087 12.54L16.7707 15.994C16.6343 16.1188 16.4556 16.1871 16.2707 16.185Z"
                fill={fill}
            />
        </svg>
    );
};
