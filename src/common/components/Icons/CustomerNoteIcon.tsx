import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const CustomerNoteIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.1183 11.049C11.6539 10.791 12.0872 10.3555 12.3468 9.81407C12.6065 9.27266 12.677 8.6576 12.5469 8.07004C12.4167 7.48248 12.0937 6.95741 11.6308 6.58119C11.1679 6.20497 10.5928 6 10 6C9.40723 6 8.8321 6.20497 8.36922 6.58119C7.90634 6.95741 7.58326 7.48248 7.45313 8.07004C7.32299 8.6576 7.39355 9.27266 7.65319 9.81407C7.91284 10.3555 8.34611 10.791 8.88174 11.049C7.77882 11.3063 6.79441 11.9361 6.08934 12.8353C5.38428 13.7346 5.00025 14.8503 5 16H15C14.9995 14.8503 14.6154 13.7348 13.9103 12.8356C13.2053 11.9363 12.2211 11.3065 11.1183 11.049Z"
                fill={fill}
            />
            <path
                d="M16.1961 12.8848C16.4408 12.6375 16.6447 12.3492 16.7976 12.0303C17.1058 11.3877 17.1893 10.6585 17.035 9.96192C16.8808 9.26531 16.4974 8.64122 15.9461 8.19318C15.3947 7.74501 14.7084 7.5 14 7.5C13.2916 7.5 12.6053 7.74501 12.0539 8.19318C11.5026 8.64122 11.1192 9.26531 10.965 9.96192C10.8107 10.6585 10.8942 11.3877 11.2024 12.0303C11.3553 12.3491 11.5591 12.6374 11.8037 12.8846C10.9835 13.2472 10.2584 13.8093 9.69586 14.5268C8.92135 15.5147 8.50027 16.7391 8.5 17.9999L8.49989 18.5H9H19H19.5002L19.5 17.9998C19.4995 16.7391 19.0783 15.5149 18.3038 14.5271C17.7413 13.8096 17.0163 13.2474 16.1961 12.8848Z"
                fill={fill}
                stroke="white"
            />
        </svg>
    );
};
