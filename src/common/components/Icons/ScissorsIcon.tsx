import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ScissorsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M7.5 5.25513C7.29059 5.43941 7.16208 5.69867 7.14224 5.97691C7.12241 6.25515 7.21284 6.53002 7.394 6.74214L10.523 10.4591L9.142 12.0521C8.8352 11.9432 8.51155 11.8894 8.186 11.8931C7.55587 11.8931 6.93989 12.08 6.41595 12.4301C5.89202 12.7802 5.48366 13.2777 5.24252 13.8599C5.00138 14.4421 4.93829 15.0827 5.06122 15.7007C5.18415 16.3187 5.48759 16.8864 5.93316 17.332C6.37873 17.7775 6.94642 18.081 7.56444 18.2039C8.18247 18.3268 8.82307 18.2638 9.40523 18.0226C9.9874 17.7815 10.485 17.3731 10.8351 16.8492C11.1851 16.3252 11.372 15.7093 11.372 15.0791C11.3585 14.4576 11.1749 13.8516 10.841 13.3271L11.9 12.1051L12.966 13.3261C12.6321 13.8506 12.4485 14.4566 12.435 15.0781C12.435 15.7083 12.6219 16.3242 12.9719 16.8482C13.322 17.3721 13.8196 17.7805 14.4018 18.0216C14.9839 18.2628 15.6245 18.3258 16.2426 18.2029C16.8606 18.08 17.4283 17.7765 17.8738 17.331C18.3194 16.8854 18.6229 16.3177 18.7458 15.6997C18.8687 15.0817 18.8056 14.4411 18.5645 13.8589C18.3233 13.2767 17.915 12.7792 17.391 12.4291C16.8671 12.079 16.2511 11.8921 15.621 11.8921C15.2955 11.8884 14.9718 11.9422 14.665 12.0511L13.284 10.4591L16.417 6.74214C16.5816 6.54909 16.6754 6.3057 16.683 6.05213C16.6898 5.89871 16.6596 5.74591 16.5948 5.60666C16.5301 5.4674 16.4327 5.3458 16.311 5.25214C16.098 5.07182 15.8226 4.98258 15.5443 5.00373C15.2661 5.02487 15.0073 5.1547 14.824 5.36514L11.9 8.81213L8.983 5.36514C8.79941 5.15631 8.54125 5.02782 8.26397 5.00725C7.98668 4.98669 7.71239 5.07568 7.5 5.25513ZM9.248 15.0791C9.248 15.2892 9.18572 15.4945 9.06902 15.6692C8.95233 15.8438 8.78647 15.9799 8.59241 16.0603C8.39836 16.1407 8.18482 16.1617 7.97882 16.1207C7.77281 16.0798 7.58358 15.9786 7.43505 15.8301C7.28653 15.6816 7.18538 15.4923 7.14441 15.2863C7.10343 15.0803 7.12446 14.8668 7.20484 14.6727C7.28522 14.4787 7.42134 14.3128 7.59599 14.1961C7.77063 14.0794 7.97596 14.0171 8.186 14.0171C8.32547 14.0171 8.46356 14.0446 8.59241 14.098C8.72126 14.1513 8.83833 14.2296 8.93695 14.3282C9.03556 14.4268 9.11379 14.5439 9.16716 14.6727C9.22053 14.8016 9.248 14.9397 9.248 15.0791ZM16.683 15.0791C16.683 15.2892 16.6207 15.4945 16.504 15.6692C16.3873 15.8438 16.2215 15.9799 16.0274 16.0603C15.8334 16.1407 15.6198 16.1617 15.4138 16.1207C15.2078 16.0798 15.0186 15.9786 14.8701 15.8301C14.7215 15.6816 14.6204 15.4923 14.5794 15.2863C14.5384 15.0803 14.5595 14.8668 14.6398 14.6727C14.7202 14.4787 14.8563 14.3128 15.031 14.1961C15.2056 14.0794 15.411 14.0171 15.621 14.0171C15.7605 14.0171 15.8986 14.0446 16.0274 14.098C16.1563 14.1513 16.2733 14.2296 16.3719 14.3282C16.4706 14.4268 16.5488 14.5439 16.6022 14.6727C16.6555 14.8016 16.683 14.9397 16.683 15.0791Z"
                fill={fill}
            />
        </svg>
    );
};
