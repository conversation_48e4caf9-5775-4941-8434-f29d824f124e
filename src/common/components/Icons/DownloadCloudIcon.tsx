import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DownloadCloudIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.1836 10.957C18.293 10.6562 18.375 10.3555 18.375 10C18.375 8.55078 17.1992 7.375 15.75 7.375C15.2031 7.375 14.6836 7.53906 14.2734 7.83984C13.5352 6.52734 12.1133 5.625 10.5 5.625C8.06641 5.625 6.125 7.59375 6.125 10C6.125 10.082 6.125 10.1641 6.125 10.2461C4.59375 10.7656 3.5 12.2422 3.5 13.9375C3.5 16.125 5.25 17.875 7.4375 17.875H17.5C19.4141 17.875 21 16.3164 21 14.375C21 12.707 19.7969 11.2852 18.1836 10.957ZM14.5469 13.3906L11.6758 16.2617C11.5117 16.4258 11.2109 16.4258 11.0469 16.2617L8.17578 13.3906C7.90234 13.1172 8.09375 12.625 8.47656 12.625H10.2812V9.5625C10.2812 9.34375 10.4727 9.125 10.7188 9.125H12.0312C12.25 9.125 12.4688 9.34375 12.4688 9.5625V12.625H14.2461C14.6289 12.625 14.8203 13.1172 14.5469 13.3906Z"
                fill={fill}
            />
        </svg>
    );
};
