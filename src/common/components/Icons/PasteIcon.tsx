import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PasteIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.16498 8.93996C8.99882 8.94919 8.84251 9.02169 8.72814 9.14257C8.61378 9.26346 8.55005 9.42355 8.55005 9.58996C8.55005 9.75638 8.61378 9.91647 8.72814 10.0374C8.84251 10.1582 8.99882 10.2307 9.16498 10.24H14.373C14.4614 10.2449 14.5498 10.2317 14.633 10.2012C14.7161 10.1708 14.7921 10.1237 14.8564 10.0629C14.9208 10.002 14.972 9.92872 15.007 9.8474C15.042 9.76609 15.0601 9.67849 15.0601 9.58996C15.0601 9.50144 15.042 9.41384 15.007 9.33253C14.972 9.25122 14.9208 9.1779 14.8564 9.11706C14.7921 9.05622 14.7161 9.00913 14.633 8.97868C14.5498 8.94823 14.4614 8.93506 14.373 8.93996H9.16498Z"
                fill={fill}
            />
            <path
                d="M14.373 11.5439H9.16498C8.99882 11.5532 8.84251 11.6257 8.72814 11.7466C8.61378 11.8674 8.55005 12.0275 8.55005 12.1939C8.55005 12.3604 8.61378 12.5205 8.72814 12.6413C8.84251 12.7622 8.99882 12.8347 9.16498 12.8439H14.373C14.5391 12.8347 14.6955 12.7622 14.8098 12.6413C14.9242 12.5205 14.9879 12.3604 14.9879 12.1939C14.9879 12.0275 14.9242 11.8674 14.8098 11.7466C14.6955 11.6257 14.5391 11.5532 14.373 11.5439Z"
                fill={fill}
            />
            <path
                d="M11.7691 14.1479H9.16913C9.00297 14.1572 8.84666 14.2297 8.73229 14.3506C8.61793 14.4714 8.5542 14.6315 8.5542 14.798C8.5542 14.9644 8.61793 15.1245 8.73229 15.2453C8.84666 15.3662 9.00297 15.4387 9.16913 15.4479H11.7691C11.9353 15.4387 12.0916 15.3662 12.206 15.2453C12.3203 15.1245 12.3841 14.9644 12.3841 14.798C12.3841 14.6315 12.3203 14.4714 12.206 14.3506C12.0916 14.2297 11.9353 14.1572 11.7691 14.1479Z"
                fill={fill}
            />
            <path
                d="M16.9772 5.68282H15.6772V5.03382C15.6772 4.94816 15.6603 4.86334 15.6274 4.78423C15.5946 4.70511 15.5464 4.63326 15.4858 4.57278C15.4251 4.5123 15.3531 4.46439 15.2739 4.43179C15.1947 4.3992 15.1098 4.38255 15.0242 4.38282H8.51016C8.3375 4.38282 8.17192 4.4514 8.04983 4.57349C7.92775 4.69558 7.85916 4.86116 7.85916 5.03382V5.68282H6.55916C6.47367 5.68308 6.38907 5.70018 6.31018 5.73313C6.2313 5.76609 6.15968 5.81427 6.09942 5.8749C6.03915 5.93554 5.99142 6.00745 5.95895 6.08654C5.92647 6.16562 5.9099 6.25032 5.91016 6.33582V18.0538C5.91016 18.2265 5.97875 18.3921 6.10083 18.5141C6.22292 18.6362 6.3885 18.7048 6.56116 18.7048H16.9772C17.1498 18.7048 17.3154 18.6362 17.4375 18.5141C17.5596 18.3921 17.6282 18.2265 17.6282 18.0538V6.33582C17.6284 6.25016 17.6118 6.16529 17.5792 6.08607C17.5466 6.00686 17.4987 5.93486 17.4382 5.8742C17.3777 5.81353 17.3059 5.7654 17.2267 5.73256C17.1476 5.69972 17.0628 5.68282 16.9772 5.68282ZM16.3262 17.4028H7.21016V6.98282H9.16516V5.68282H14.3732V6.98282H16.3262V17.4028Z"
                fill={fill}
            />
        </svg>
    );
};
