import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const LockIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.9375 10.875H16.2812V8.90625C16.2812 6.63672 14.3945 4.75 12.125 4.75C9.82812 4.75 7.96875 6.63672 7.96875 8.90625V10.875H7.3125C6.57422 10.875 6 11.4766 6 12.1875V17.4375C6 18.1758 6.57422 18.75 7.3125 18.75H16.9375C17.6484 18.75 18.25 18.1758 18.25 17.4375V12.1875C18.25 11.4766 17.6484 10.875 16.9375 10.875ZM14.0938 10.875H10.1562V8.90625C10.1562 7.83984 11.0312 6.9375 12.125 6.9375C13.1914 6.9375 14.0938 7.83984 14.0938 8.90625V10.875Z"
                fill={fill}
            />
        </svg>
    );
};
