import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SettingsIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.4999 12.938V11.558C19.4999 11.375 19.4272 11.1995 19.2978 11.0701C19.1684 10.9407 18.9929 10.868 18.8099 10.868C18.6669 10.8686 18.5274 10.8234 18.4118 10.739C18.2963 10.6546 18.2108 10.5355 18.1679 10.399C18.0425 9.99357 17.8749 9.60243 17.6679 9.23198C17.5973 9.10439 17.5702 8.95724 17.5907 8.81286C17.6113 8.66847 17.6784 8.53474 17.7819 8.43198L17.7999 8.40998C17.9234 8.28146 17.9923 8.11018 17.9923 7.93198C17.9923 7.75378 17.9234 7.58249 17.7999 7.45398L17.0439 6.68798C16.9154 6.56453 16.7441 6.49559 16.5659 6.49559C16.3877 6.49559 16.2164 6.56453 16.0879 6.68798L16.0669 6.70898C15.9642 6.81246 15.8304 6.87958 15.6861 6.90016C15.5417 6.92073 15.3945 6.89363 15.2669 6.82298C14.8965 6.61598 14.5053 6.4484 14.0999 6.32298C13.9651 6.28058 13.8471 6.19661 13.7629 6.0831C13.6786 5.96958 13.6324 5.83233 13.6309 5.69098C13.6309 5.60036 13.6131 5.51064 13.5784 5.42693C13.5437 5.34321 13.4929 5.26715 13.4288 5.20307C13.3648 5.139 13.2887 5.08818 13.205 5.0535C13.1213 5.01882 13.0315 5.00098 12.9409 5.00098H11.5579C11.3749 5.00098 11.1994 5.07367 11.07 5.20307C10.9406 5.33247 10.8679 5.50798 10.8679 5.69098C10.865 5.83415 10.8176 5.97287 10.7323 6.08791C10.6471 6.20294 10.5281 6.28859 10.3919 6.33298C10.0651 6.43703 9.74763 6.56849 9.44293 6.72598C9.31412 6.78654 9.16973 6.8058 9.02956 6.78109C8.88938 6.75638 8.76027 6.68893 8.65993 6.58798L8.58793 6.51598C8.45942 6.39253 8.28813 6.32359 8.10993 6.32359C7.93173 6.32359 7.76044 6.39253 7.63193 6.51598L6.68993 7.45498C6.56648 7.58349 6.49754 7.75478 6.49754 7.93298C6.49754 8.11118 6.56648 8.28246 6.68993 8.41098L6.71093 8.43198C6.81441 8.53474 6.88154 8.66847 6.90211 8.81286C6.92269 8.95724 6.89559 9.10439 6.82493 9.23198C6.61793 9.60243 6.45035 9.99357 6.32493 10.399C6.28253 10.5338 6.19857 10.6518 6.08505 10.7361C5.97153 10.8203 5.83428 10.8665 5.69293 10.868C5.50993 10.868 5.33443 10.9407 5.20503 11.0701C5.07563 11.1995 5.00293 11.375 5.00293 11.558V12.939C5.00293 13.122 5.07563 13.2975 5.20503 13.4269C5.33443 13.5563 5.50993 13.629 5.69293 13.629C5.83599 13.6284 5.97549 13.6736 6.09102 13.758C6.20654 13.8423 6.29201 13.9615 6.33493 14.098C6.46035 14.5034 6.62793 14.8945 6.83493 15.265C6.90559 15.3926 6.93269 15.5397 6.91211 15.6841C6.89154 15.8285 6.82441 15.9622 6.72093 16.065L6.69993 16.086C6.57648 16.2145 6.50754 16.3858 6.50754 16.564C6.50754 16.7422 6.57648 16.9135 6.69993 17.042L7.46993 17.812C7.59844 17.9354 7.76973 18.0044 7.94793 18.0044C8.12613 18.0044 8.29742 17.9354 8.42593 17.812L8.44693 17.791C8.5497 17.6875 8.68342 17.6204 8.82781 17.5998C8.97219 17.5792 9.11935 17.6063 9.24693 17.677C9.61739 17.884 10.0085 18.0516 10.4139 18.177C10.5457 18.2214 10.6604 18.3057 10.7421 18.4182C10.8238 18.5308 10.8685 18.6659 10.8699 18.805C10.8699 18.8956 10.8878 18.9853 10.9225 19.069C10.9571 19.1527 11.008 19.2288 11.072 19.2929C11.1361 19.357 11.2122 19.4078 11.2959 19.4425C11.3796 19.4771 11.4693 19.495 11.5599 19.495H12.9409C13.0315 19.495 13.1213 19.4771 13.205 19.4425C13.2887 19.4078 13.3648 19.357 13.4288 19.2929C13.4929 19.2288 13.5437 19.1527 13.5784 19.069C13.6131 18.9853 13.6309 18.8956 13.6309 18.805C13.6303 18.6619 13.6755 18.5224 13.7599 18.4069C13.8443 18.2914 13.9635 18.2059 14.0999 18.163C14.5053 18.0376 14.8965 17.87 15.2669 17.663C15.3945 17.5923 15.5417 17.5652 15.6861 17.5858C15.8304 17.6064 15.9642 17.6735 16.0669 17.777L16.0879 17.798C16.2164 17.9214 16.3877 17.9904 16.5659 17.9904C16.7441 17.9904 16.9154 17.9214 17.0439 17.798L17.9859 16.856C18.1094 16.7275 18.1783 16.5562 18.1783 16.378C18.1783 16.1998 18.1094 16.0285 17.9859 15.9L17.9139 15.828C17.8144 15.7289 17.7475 15.6018 17.7221 15.4637C17.6967 15.3255 17.7141 15.183 17.7719 15.055C17.9276 14.7488 18.057 14.43 18.1589 14.102C18.2042 13.9653 18.2911 13.8462 18.4075 13.7613C18.5239 13.6765 18.6639 13.6302 18.8079 13.629C18.8988 13.6292 18.9888 13.6116 19.0728 13.5769C19.1568 13.5423 19.2332 13.4914 19.2975 13.4272C19.3618 13.363 19.4128 13.2868 19.4475 13.2028C19.4822 13.1188 19.5001 13.0288 19.4999 12.938ZM13.6329 15.409C13.1978 15.6002 12.7277 15.699 12.2524 15.699C11.7771 15.699 11.307 15.6002 10.8719 15.409H10.8289C10.0627 15.0624 9.44833 14.4494 9.09993 13.684C9.09804 13.6714 9.09804 13.6586 9.09993 13.646C8.90869 13.2109 8.80995 12.7408 8.80995 12.2655C8.80995 11.7902 8.90869 11.3201 9.09993 10.885C9.09804 10.8724 9.09804 10.8596 9.09993 10.847C9.4499 10.0622 10.0784 9.43516 10.8639 9.08698L11.0739 8.99998C11.9042 8.69776 12.819 8.72749 13.6279 9.08298L13.6659 9.09998C14.4312 9.44744 15.0445 10.0607 15.3919 10.826C15.3938 10.8386 15.3938 10.8514 15.3919 10.864C15.7474 11.6729 15.7772 12.5877 15.4749 13.418C15.4496 13.4846 15.4219 13.5536 15.3919 13.625C15.052 14.4045 14.4379 15.0322 13.6659 15.389L13.6329 15.409Z"
                fill={fill}
            />
        </svg>
    );
};
