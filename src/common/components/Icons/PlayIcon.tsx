import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PlayIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.221 11.1999C17.4495 11.3314 17.6393 11.5209 17.7712 11.7491C17.9032 11.9773 17.9727 12.2363 17.9727 12.4999C17.9727 12.7636 17.9032 13.0225 17.7712 13.2508C17.6393 13.479 17.4495 13.6684 17.221 13.7999L7.74399 19.2149C7.51605 19.3451 7.25793 19.4132 6.99542 19.4124C6.73291 19.4116 6.47521 19.342 6.24807 19.2104C6.02092 19.0788 5.83229 18.8899 5.70101 18.6626C5.56974 18.4352 5.50042 18.1774 5.49999 17.9149V7.08492C5.50042 6.82241 5.56974 6.56461 5.70101 6.33729C5.83229 6.10996 6.02092 5.92106 6.24807 5.78947C6.47521 5.65788 6.73291 5.58821 6.99542 5.58741C7.25793 5.58661 7.51605 5.65472 7.74399 5.78492L17.221 11.1999Z"
                fill={fill}
            />
        </svg>
    );
};
