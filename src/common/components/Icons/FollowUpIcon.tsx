import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const FollowUpIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M17.1764 6.08356L10.0162 6.08215C9.85744 6.08215 9.7002 6.11342 9.5535 6.17419C9.4068 6.23495 9.2735 6.32402 9.16122 6.4363C9.04894 6.54858 8.95988 6.68187 8.89911 6.82857C8.83835 6.97528 8.80707 7.13251 8.80707 7.2913C8.80707 7.45009 8.83835 7.60732 8.89911 7.75402C8.95988 7.90072 9.04894 8.03402 9.16122 8.1463C9.2735 8.25858 9.4068 8.34764 9.5535 8.40841C9.7002 8.46917 9.85744 8.50045 10.0162 8.50045L14.2567 8.5153L6.44109 16.331C6.21427 16.5578 6.08684 16.8654 6.08684 17.1862C6.08684 17.507 6.21427 17.8146 6.44109 18.0414C6.66792 18.2683 6.97556 18.3957 7.29634 18.3957C7.61712 18.3957 7.92476 18.2683 8.15158 18.0414L15.9672 10.2258L15.9672 14.4529C15.9636 14.6154 15.9925 14.7769 16.0524 14.928C16.1122 15.0791 16.2018 15.2167 16.3157 15.3326C16.4296 15.4485 16.5656 15.5404 16.7157 15.6028C16.8657 15.6653 17.0268 15.697 17.1893 15.6962C17.3518 15.6953 17.5125 15.6619 17.6619 15.5979C17.8113 15.5339 17.9463 15.4406 18.059 15.3235C18.1717 15.2064 18.2598 15.0679 18.3181 14.9162C18.3764 14.7644 18.4036 14.6026 18.3983 14.4401L18.3983 7.30544C18.3993 6.98082 18.272 6.66895 18.044 6.43782C17.9319 6.32172 17.7969 6.23025 17.6475 6.16926C17.4981 6.10826 17.3377 6.07907 17.1764 6.08356Z"
                fill={fill}
            />
        </svg>
    );
};
