import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ClockFilledIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11.1111 8.44445C11.1111 7.95353 11.5091 7.55556 12 7.55556C12.4909 7.55556 12.8889 7.95352 12.8889 8.44444V11.1111L14.6667 11.1111C15.1576 11.1111 15.5556 11.5091 15.5556 12C15.5556 12.4909 15.1576 12.8889 14.6667 12.8889H12.1111C11.5964 12.8889 11.1725 12.5 11.1172 12H11.1111V8.44445Z"
                fill={fill}
            />
        </svg>
    );
};
