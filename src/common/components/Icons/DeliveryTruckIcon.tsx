import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DeliveryTruckIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 370 370"
            fill={fill}
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M260,89.038v0.001v150h-20v-72.808l-37.599-9.4l-26.514-81.063l-41.269-0.121l0.005-0.07H0v183.461h27.248
			c2.73,19.956,19.878,35.384,40.571,35.384c20.694,0,37.842-15.428,40.572-35.384h14.69c2.73,19.956,19.878,35.384,40.572,35.384
			c20.693,0,37.841-15.428,40.571-35.384H220h20h130v-0.833v-19.167v-150H260z M116.154,95.594l45.218,0.133l19.451,59.466h-64.669
			V95.594z M67.819,274.423c-11.558,0-20.961-9.403-20.961-20.961c0-1.229,0.128-2.427,0.332-3.598
			c0.056-0.326,0.114-0.651,0.185-0.971c0.077-0.346,0.17-0.687,0.265-1.026c0.082-0.296,0.16-0.593,0.255-0.883
			c0.111-0.34,0.24-0.67,0.368-1.002c0.109-0.284,0.211-0.571,0.332-0.849c0.129-0.296,0.278-0.581,0.419-0.869
			c0.149-0.303,0.289-0.611,0.452-0.906c0.073-0.132,0.159-0.255,0.235-0.386c1.169-2.003,2.649-3.799,4.385-5.308
			c0.012-0.01,0.023-0.021,0.034-0.031c0.436-0.377,0.889-0.733,1.354-1.073c0.04-0.029,0.078-0.06,0.118-0.089
			c0.448-0.323,0.911-0.626,1.384-0.914c0.067-0.041,0.134-0.083,0.202-0.123c0.453-0.269,0.918-0.52,1.392-0.755
			c0.103-0.051,0.207-0.101,0.312-0.15c0.449-0.214,0.906-0.412,1.371-0.594c0.152-0.059,0.306-0.113,0.459-0.168
			c0.43-0.157,0.865-0.304,1.306-0.433c0.221-0.064,0.447-0.116,0.67-0.173c0.391-0.1,0.781-0.199,1.179-0.276
			c0.332-0.063,0.67-0.106,1.008-0.154c0.308-0.044,0.614-0.099,0.925-0.129c0.665-0.064,1.336-0.102,2.018-0.102
			c0.681,0,1.353,0.038,2.018,0.102c0.313,0.031,0.619,0.085,0.928,0.13c0.336,0.048,0.674,0.09,1.005,0.154
			c0.4,0.078,0.793,0.177,1.186,0.277c0.221,0.056,0.444,0.108,0.662,0.171c0.446,0.13,0.884,0.278,1.318,0.437
			c0.149,0.054,0.298,0.106,0.446,0.163c0.47,0.183,0.931,0.384,1.385,0.6c0.099,0.047,0.197,0.094,0.295,0.142
			c0.479,0.238,0.95,0.491,1.409,0.763c0.061,0.037,0.122,0.074,0.183,0.111c0.48,0.292,0.949,0.599,1.403,0.927
			c0.031,0.023,0.061,0.047,0.093,0.069c2.378,1.736,4.381,3.965,5.861,6.538c0.052,0.09,0.111,0.175,0.162,0.266
			c0.173,0.312,0.322,0.639,0.48,0.961c0.132,0.271,0.272,0.536,0.392,0.813c0.126,0.29,0.233,0.59,0.346,0.886
			c0.122,0.32,0.247,0.639,0.354,0.966c0.097,0.298,0.178,0.603,0.262,0.906c0.092,0.333,0.183,0.666,0.259,1.006
			c0.072,0.324,0.13,0.653,0.188,0.982c0.203,1.169,0.331,2.365,0.331,3.592C88.781,265.02,79.378,274.423,67.819,274.423z
			 M163.654,274.423c-11.559,0-20.962-9.403-20.962-20.961c0-1.227,0.127-2.422,0.331-3.592c0.057-0.33,0.115-0.659,0.188-0.982
			c0.076-0.339,0.167-0.673,0.259-1.006c0.084-0.303,0.165-0.608,0.262-0.906c0.107-0.328,0.232-0.647,0.355-0.968
			c0.113-0.296,0.219-0.595,0.345-0.884c0.121-0.278,0.261-0.544,0.394-0.815c0.157-0.321,0.306-0.648,0.479-0.959
			c0.05-0.091,0.11-0.176,0.162-0.266c1.479-2.573,3.483-4.802,5.861-6.538c0.031-0.023,0.061-0.047,0.093-0.069
			c0.454-0.328,0.924-0.635,1.403-0.927c0.061-0.037,0.121-0.075,0.183-0.111c0.458-0.272,0.929-0.525,1.408-0.763
			c0.098-0.049,0.197-0.096,0.296-0.143c0.453-0.216,0.915-0.416,1.384-0.599c0.147-0.057,0.297-0.11,0.446-0.164
			c0.434-0.158,0.872-0.307,1.318-0.436c0.218-0.063,0.441-0.115,0.662-0.171c0.393-0.1,0.786-0.2,1.186-0.277
			c0.331-0.063,0.668-0.106,1.005-0.154c0.309-0.044,0.615-0.099,0.928-0.13c0.665-0.064,1.337-0.102,2.018-0.102
			c0.681,0,1.353,0.038,2.018,0.102c0.312,0.03,0.617,0.085,0.926,0.129c0.337,0.048,0.676,0.091,1.007,0.154
			c0.399,0.077,0.791,0.177,1.182,0.277c0.223,0.056,0.447,0.108,0.667,0.172c0.444,0.129,0.88,0.277,1.312,0.435
			c0.151,0.055,0.303,0.108,0.453,0.166c0.467,0.183,0.927,0.382,1.378,0.597c0.101,0.048,0.202,0.096,0.302,0.146
			c0.477,0.236,0.945,0.488,1.402,0.759c0.064,0.038,0.127,0.078,0.191,0.116c0.477,0.29,0.943,0.595,1.395,0.921
			c0.035,0.025,0.068,0.051,0.103,0.077c0.471,0.344,0.93,0.705,1.371,1.086c0.003,0.003,0.007,0.007,0.01,0.01
			c1.774,1.537,3.284,3.373,4.465,5.425c0.054,0.094,0.116,0.183,0.169,0.278c0.172,0.31,0.32,0.635,0.477,0.955
			c0.133,0.273,0.274,0.541,0.396,0.82c0.126,0.288,0.232,0.586,0.345,0.881c0.123,0.322,0.249,0.641,0.356,0.97
			c0.097,0.297,0.177,0.602,0.261,0.905c0.092,0.333,0.183,0.667,0.26,1.006c0.072,0.324,0.13,0.653,0.188,0.982
			c0.203,1.169,0.331,2.365,0.331,3.592C184.615,265.02,175.212,274.423,163.654,274.423z M220,239.04h-18.014
			c-5.249-13.904-17.852-24.217-33.042-26.186c-0.234-0.031-0.468-0.063-0.703-0.09c-0.302-0.034-0.606-0.063-0.911-0.09
			c-0.367-0.033-0.735-0.061-1.104-0.084c-0.233-0.015-0.465-0.031-0.698-0.041c-0.622-0.029-1.246-0.048-1.873-0.048
			c-0.627,0-1.251,0.019-1.873,0.048c-0.234,0.01-0.466,0.027-0.699,0.042c-0.369,0.023-0.737,0.051-1.104,0.084
			c-0.305,0.027-0.609,0.056-0.912,0.09c-0.234,0.027-0.467,0.059-0.7,0.089c-15.192,1.968-27.797,12.282-33.046,26.186h-19.169
			c-5.249-13.904-17.854-24.218-33.046-26.186c-0.233-0.031-0.466-0.063-0.7-0.089c-0.303-0.034-0.607-0.063-0.912-0.09
			c-0.367-0.033-0.734-0.061-1.104-0.084c-0.233-0.015-0.465-0.031-0.699-0.042c-0.622-0.029-1.246-0.048-1.873-0.048
			s-1.251,0.019-1.873,0.048c-0.233,0.01-0.465,0.027-0.698,0.041c-0.37,0.023-0.738,0.051-1.106,0.084
			c-0.304,0.027-0.607,0.056-0.909,0.09c-0.237,0.027-0.473,0.059-0.709,0.09c-15.188,1.97-27.789,12.283-33.038,26.185H20V95.578
			h76.154v79.615h97.23L220,181.847V239.04z M350,238.206h-70V109.04h25v50h20v-50h25V238.206z"
            />
        </svg>
    );
};
