import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SortIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M4 7C4 6.44772 4.44772 6 5 6H18.8311C19.3833 6 19.8311 6.44772 19.8311 7C19.8311 7.55228 19.3833 8 18.8311 8H5C4.44772 8 4 7.55228 4 7ZM4 11.746C4 11.1937 4.44772 10.746 5 10.746H13.813C14.3653 10.746 14.813 11.1937 14.813 11.746C14.813 12.2983 14.3653 12.746 13.813 12.746H5C4.44772 12.746 4 12.2983 4 11.746ZM4.99609 15.492C4.44381 15.492 3.99609 15.9397 3.99609 16.492C3.99609 17.0443 4.44381 17.492 4.99609 17.492H8.20703C8.75932 17.492 9.20703 17.0443 9.20703 16.492C9.20703 15.9397 8.75932 15.492 8.20703 15.492H4.99609Z"
                fill={fill}
            />
        </svg>
    );
};
