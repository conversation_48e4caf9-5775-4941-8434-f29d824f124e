import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const PhoneIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.905 5.88811C9.772 5.44411 9.639 5.0881 9.195 5.0451L8.352 5.00111C7.465 4.95611 6 6.2881 6 8.5071C6.044 12.3681 11.814 18.6701 15.631 19.0251C17.894 19.2471 19.315 17.9151 19.359 16.9841V16.1841C19.403 15.7401 19.048 15.5631 18.605 15.3841C17.762 15.0731 16.919 14.6741 16.075 14.3631C15.92 14.3277 15.7588 14.3294 15.6045 14.368C15.4502 14.4065 15.3072 14.4809 15.187 14.5851C14.8993 14.8131 14.6316 15.0654 14.387 15.3391C14.165 15.5171 13.943 15.6051 13.721 15.3831C11.8019 14.3936 10.2931 12.7591 9.46 10.7671C9.42095 10.716 9.39325 10.6573 9.37873 10.5946C9.36421 10.532 9.3632 10.467 9.37579 10.404C9.38838 10.341 9.41426 10.2814 9.45172 10.2291C9.48917 10.1769 9.53734 10.1332 9.593 10.1011C9.904 9.8351 10.126 9.6131 10.393 9.34711C10.5126 9.2449 10.604 9.11374 10.6584 8.96615C10.7129 8.81857 10.7286 8.65949 10.704 8.5041C10.438 7.6641 10.172 6.73211 9.905 5.88811Z"
                fill={fill}
            />
        </svg>
    );
};
