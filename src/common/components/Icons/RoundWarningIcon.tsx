import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const RoundWarningIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 51 51"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M28.3125 31.5251C28.3125 29.9783 27.0469 28.7126 25.5 28.7126C23.918 28.7126 22.6875 29.9783 22.6875 31.5251C22.6875 33.1072 23.918 34.3376 25.5 34.3376C27.0469 34.3376 28.3125 33.1072 28.3125 31.5251ZM23.0039 17.2517L23.4609 26.8142C23.4961 27.2361 23.8828 27.5876 24.3047 27.5876H26.6602C27.082 27.5876 27.4688 27.2361 27.5039 26.8142L27.9609 17.2517C27.9961 16.7595 27.6094 16.3376 27.1172 16.3376H23.8477C23.3555 16.3376 22.9688 16.7595 23.0039 17.2517Z"
                fill={fill}
            />
            <circle cx="25.5" cy="25.5876" r="24.5" stroke={fill} />
        </svg>
    );
};
