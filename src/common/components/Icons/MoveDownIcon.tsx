import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MoveDownIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.29298 13.558L12.161 19.377C12.248 19.4718 12.3538 19.5476 12.4716 19.5994C12.5895 19.6512 12.7168 19.6779 12.8455 19.6779C12.9742 19.6779 13.1015 19.6512 13.2193 19.5994C13.3372 19.5476 13.443 19.4718 13.53 19.377L19.399 13.558C19.5686 13.3851 19.6717 13.1578 19.69 12.9163C19.7083 12.6747 19.6407 12.4345 19.499 12.238C19.42 12.12 19.3151 12.0216 19.1922 11.9505C19.0693 11.8794 18.9317 11.8374 18.79 11.8277C18.6484 11.8181 18.5064 11.841 18.375 11.8948C18.2436 11.9486 18.1262 12.0318 18.032 12.138L13.826 16.344V5.978C13.826 5.71862 13.7229 5.46986 13.5395 5.28645C13.3561 5.10304 13.1074 5 12.848 5C12.5886 5 12.3398 5.10304 12.1564 5.28645C11.973 5.46986 11.87 5.71862 11.87 5.978V16.345L7.66198 12.14C7.56774 12.0338 7.4504 11.9506 7.31899 11.8968C7.18758 11.843 7.04559 11.8201 6.90392 11.8297C6.76226 11.8394 6.6247 11.8814 6.5018 11.9525C6.3789 12.0236 6.27394 12.122 6.19498 12.24C6.05358 12.4361 5.98588 12.6758 6.00381 12.9169C6.02173 13.1579 6.12413 13.385 6.29298 13.558Z"
                fill={fill}
            />
        </svg>
    );
};
