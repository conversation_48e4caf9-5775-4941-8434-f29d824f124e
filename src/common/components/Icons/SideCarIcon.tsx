import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const SideCarIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 26 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M22.1 6.07143H21.45L17.0219 0.948661C16.575 0.417411 15.6812 0.0379464 14.9906 0H6.29688C5.24062 0 4.26562 0.607143 3.85938 1.5558L1.95 6.18527C0.8125 6.45089 0 7.39955 0 8.5V12.75C0 13.0915 0.284375 13.3571 0.65 13.3571H2.6C2.6 15.4062 4.30625 17 6.5 17C8.65312 17 10.4 15.4062 10.4 13.3571H15.6C15.6 15.4062 17.3062 17 19.5 17C21.6531 17 23.4 15.4062 23.4 13.3571H25.35C25.675 13.3571 26 13.0915 26 12.75V9.71429C26 7.70312 24.2531 6.07143 22.1 6.07143ZM6.5 15.1786C5.40312 15.1786 4.55 14.3817 4.55 13.3571C4.55 12.3705 5.40312 11.5357 6.5 11.5357C7.55625 11.5357 8.45 12.3705 8.45 13.3571C8.45 14.3817 7.55625 15.1786 6.5 15.1786ZM9.425 6.07143H4.7125L6.29688 2.42857H9.425V6.07143ZM11.375 6.07143V2.42857H14.9906L18.1187 6.07143H11.375ZM19.5 15.1786C18.4031 15.1786 17.55 14.3817 17.55 13.3571C17.55 12.3705 18.4031 11.5357 19.5 11.5357C20.5562 11.5357 21.45 12.3705 21.45 13.3571C21.45 14.3817 20.5562 15.1786 19.5 15.1786Z"
                fill={fill}
            />
        </svg>
    );
};
