import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const JustifiedIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.737 9.25999C18.737 9.45042 18.6614 9.63304 18.5267 9.76769C18.3921 9.90235 18.2095 9.97799 18.019 9.97799H5.09105C4.90062 9.97799 4.718 9.90235 4.58334 9.76769C4.44869 9.63304 4.37305 9.45042 4.37305 9.25999C4.37305 9.06957 4.44869 8.88694 4.58334 8.75229C4.718 8.61764 4.90062 8.54199 5.09105 8.54199H18.019C18.2095 8.54199 18.3921 8.61764 18.5267 8.75229C18.6614 8.88694 18.737 9.06957 18.737 9.25999Z"
                fill={fill}
            />
            <path
                d="M18.737 6.38695C18.737 6.57737 18.6614 6.76 18.5267 6.89465C18.3921 7.0293 18.2095 7.10495 18.019 7.10495H5.09105C4.90062 7.10495 4.718 7.0293 4.58334 6.89465C4.44869 6.76 4.37305 6.57737 4.37305 6.38695C4.37305 6.19652 4.44869 6.01389 4.58334 5.87924C4.718 5.74459 4.90062 5.66895 5.09105 5.66895H18.019C18.2095 5.66895 18.3921 5.74459 18.5267 5.87924C18.6614 6.01389 18.737 6.19652 18.737 6.38695Z"
                fill={fill}
            />
            <path
                d="M18.737 15.0051C18.737 15.1955 18.6614 15.3782 18.5267 15.5128C18.3921 15.6475 18.2095 15.7231 18.019 15.7231H5.09105C4.90062 15.7231 4.718 15.6475 4.58334 15.5128C4.44869 15.3782 4.37305 15.1955 4.37305 15.0051C4.37305 14.8147 4.44869 14.6321 4.58334 14.4974C4.718 14.3628 4.90062 14.2871 5.09105 14.2871H18.019C18.2095 14.2871 18.3921 14.3628 18.5267 14.4974C18.6614 14.6321 18.737 14.8147 18.737 15.0051Z"
                fill={fill}
            />
            <path
                d="M18.737 12.133C18.737 12.3235 18.6614 12.5061 18.5267 12.6407C18.3921 12.7754 18.2095 12.851 18.019 12.851H5.09105C4.90062 12.851 4.718 12.7754 4.58334 12.6407C4.44869 12.5061 4.37305 12.3235 4.37305 12.133C4.37305 11.9426 4.44869 11.76 4.58334 11.6253C4.718 11.4907 4.90062 11.415 5.09105 11.415H18.019C18.2095 11.415 18.3921 11.4907 18.5267 11.6253C18.6614 11.76 18.737 11.9426 18.737 12.133Z"
                fill={fill}
            />
            <path
                d="M18.737 17.8782C18.737 18.0686 18.6614 18.2512 18.5267 18.3859C18.3921 18.5205 18.2095 18.5962 18.019 18.5962H5.09105C4.90062 18.5962 4.718 18.5205 4.58334 18.3859C4.44869 18.2512 4.37305 18.0686 4.37305 17.8782C4.37305 17.7839 4.39162 17.6905 4.4277 17.6034C4.46378 17.5163 4.51667 17.4371 4.58334 17.3705C4.65002 17.3038 4.72917 17.2509 4.81628 17.2148C4.90339 17.1787 4.99676 17.1602 5.09105 17.1602H18.019C18.1133 17.1602 18.2067 17.1787 18.2938 17.2148C18.3809 17.2509 18.4601 17.3038 18.5267 17.3705C18.5934 17.4371 18.6463 17.5163 18.6824 17.6034C18.7185 17.6905 18.737 17.7839 18.737 17.8782Z"
                fill={fill}
            />
        </svg>
    );
};
