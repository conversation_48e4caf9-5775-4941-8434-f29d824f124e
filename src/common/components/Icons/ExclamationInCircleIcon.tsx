import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const ExclamationInCircleIcon: typeof Icon = ({
    fill,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 51 51"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M28.5898 31.5527C28.5898 33.1348 27.3242 34.3652 25.7773 34.3652C24.1953 34.3652 22.9648 33.1348 22.9648 31.5527C22.9648 30.0059 24.1953 28.7402 25.7773 28.7402C27.3242 28.7402 28.5898 30.0059 28.5898 31.5527ZM23.2812 17.2793C23.2461 16.7871 23.6328 16.3652 24.125 16.3652H27.3945C27.8867 16.3652 28.2734 16.7871 28.2383 17.2793L27.7812 26.8418C27.7461 27.2637 27.3594 27.6152 26.9375 27.6152H24.582C24.1602 27.6152 23.7734 27.2637 23.7383 26.8418L23.2812 17.2793Z"
                fill={fill ?? '#0069FF'}
            />
            <circle
                xmlns="http://www.w3.org/2000/svg"
                cx="25.7773"
                cy="25.6152"
                r="24.5"
                stroke={fill ?? '#0069FF'}
            />
        </svg>
    );
};
