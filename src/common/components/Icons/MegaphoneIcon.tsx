import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const MegaphoneIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.9016 12.8839C16.8028 12.8832 16.7064 12.853 16.6244 12.7973C16.5425 12.7415 16.4786 12.6625 16.4407 12.5703C16.4029 12.478 16.3927 12.3765 16.4116 12.2785C16.4304 12.1804 16.4775 12.0901 16.5468 12.0189C16.7419 11.8209 16.8514 11.5528 16.8514 11.2734C16.8514 10.9939 16.7419 10.7258 16.5468 10.5278C16.4621 10.4315 16.4171 10.3062 16.4208 10.1774C16.4245 10.0486 16.4768 9.92617 16.5669 9.83508C16.657 9.74399 16.7782 9.69115 16.9055 9.68736C17.0329 9.68357 17.1569 9.72912 17.2522 9.81469C17.6337 10.2022 17.8478 10.7267 17.8478 11.2734C17.8478 11.8201 17.6337 12.3445 17.2522 12.732C17.1598 12.8275 17.0337 12.8821 16.9016 12.8839ZM18.7424 14.3497C19.5478 13.5329 20 12.4266 20 11.2734C20 10.1201 19.5478 9.01384 18.7424 8.197C18.6966 8.147 18.6411 8.10689 18.5795 8.07908C18.5179 8.05126 18.4514 8.03632 18.3839 8.03514C18.3165 8.03397 18.2495 8.04659 18.187 8.07225C18.1245 8.0979 18.0677 8.13606 18.0202 8.18444C17.9726 8.23282 17.9352 8.29041 17.9102 8.35376C17.8852 8.41712 17.8731 8.48492 17.8747 8.55311C17.8763 8.6213 17.8915 8.68846 17.9194 8.75056C17.9473 8.81267 17.9873 8.86843 18.037 8.91451C18.6547 9.54167 19.0014 10.3906 19.0014 11.2756C19.0014 12.1605 18.6547 13.0095 18.037 13.6366C17.9435 13.7312 17.8909 13.8594 17.8909 13.9932C17.8909 14.1269 17.9435 14.2552 18.037 14.3497C18.1306 14.4443 18.2574 14.4974 18.3897 14.4974C18.522 14.4974 18.6489 14.4443 18.7424 14.3497ZM9.12041 14.3497V8.1871H6.75056C6.51903 8.18708 6.2898 8.2335 6.07613 8.32367C5.86247 8.41383 5.66863 8.54595 5.50582 8.71237C5.34302 8.8788 5.2145 9.07621 5.12769 9.2932C5.04088 9.51018 4.99752 9.74242 5.00011 9.97646V12.6055C5.00068 12.9962 5.12913 13.3757 5.36543 13.6848C5.60173 13.994 5.93259 14.2154 6.30641 14.3145L6.756 17.5598C6.77169 17.6822 6.83119 17.7946 6.92325 17.8757C7.01531 17.9568 7.13356 18.001 7.25566 18H8.9974C9.06942 18.0007 9.14075 17.9857 9.20649 17.956C9.27223 17.9262 9.33084 17.8825 9.3783 17.8277C9.42576 17.7729 9.46094 17.7085 9.48144 17.6387C9.50194 17.5689 9.50727 17.4954 9.49707 17.4233L9.07687 14.3904L9.12041 14.3497ZM14.7364 6.26128C14.4811 6.1024 14.1895 6.01285 13.8899 6.00128C13.5903 5.98972 13.2928 6.05653 13.0263 6.19525L10.1165 7.7117V14.7888L13.0263 16.3075C13.2928 16.4447 13.5897 16.5107 13.8887 16.499C14.1876 16.4873 14.4786 16.3983 14.7338 16.2406C14.9891 16.0829 15.2001 15.8617 15.3467 15.5981C15.4933 15.3345 15.5707 15.0373 15.5714 14.7349V7.79203C15.5731 7.48934 15.4971 7.19138 15.3507 6.9273C15.2044 6.66322 14.9927 6.44207 14.7364 6.28549V6.26128Z"
                fill={fill}
            />
        </svg>
    );
};
