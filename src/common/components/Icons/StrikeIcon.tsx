import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const StrikeIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className = '',
}: IconProps) => {
    return (
        <svg width={size} style={{ minWidth: size }} height={size}>
            <g fillRule="evenodd">
                <path
                    d="M15.6 8.5c-.5-.7-1-1.1-1.3-1.3-.6-.4-1.3-.6-2-.6-2.7 0-2.8 1.7-2.8 2.1 0 1.6 1.8 2 3.2 2.3 4.4.9 4.6 2.8 4.6 3.9 0 1.4-.7 4.1-5 4.1A6.2 6.2 0 017 16.4l1.5-1.1c.4.6 1.6 2 3.7 2 1.6 0 2.5-.4 3-1.2.4-.8.3-2-.8-2.6-.7-.4-1.6-.7-2.9-1-1-.2-3.9-.8-3.9-3.6C7.6 6 10.3 5 12.4 5c2.9 0 4.2 1.6 4.7 2.4l-1.5 1.1z"
                    fill={fill}
                />
                <path d="M5 11h14a1 1 0 010 2H5a1 1 0 010-2z" fillRule="nonzero" fill={fill} />
            </g>
        </svg>
    );
};
