import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const UpIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6.70007 14.4442C6.70007 14.2052 6.80701 13.9841 6.98347 13.827L11.3684 9.93583L11.5675 10.1602L11.3708 9.93374C11.5466 9.78098 11.7761 9.7002 12.0091 9.7002C12.2421 9.7002 12.4716 9.78097 12.6474 9.93372L17.0199 13.827L17.0226 13.8294C17.1958 13.9875 17.3001 14.2073 17.3001 14.4442C17.3001 14.6811 17.1958 14.901 17.0226 15.059L17.0171 15.0638C16.8413 15.2166 16.6118 15.2974 16.3788 15.2974C16.1458 15.2974 15.9163 15.2166 15.7405 15.0638L12.0017 11.7342L8.26558 15.0614C8.09025 15.2175 7.85919 15.3002 7.62453 15.3002C7.38986 15.3002 7.1588 15.2175 6.98347 15.0614C6.80701 14.9043 6.70007 14.6832 6.70007 14.4442ZM7.50401 14.9897C7.54342 14.9966 7.58377 15.0002 7.62453 15.0002C7.79014 15.0002 7.94897 14.9416 8.06608 14.8373L12.0016 11.3325L12.0016 11.3325L8.06603 14.8373C7.94892 14.9416 7.79009 15.0002 7.62448 15.0002C7.58374 15.0002 7.54341 14.9966 7.50401 14.9897ZM16.3035 14.9934C16.3283 14.996 16.3535 14.9974 16.3788 14.9974C16.5438 14.9974 16.7022 14.9399 16.8203 14.8373C16.9355 14.7322 17 14.5911 17 14.4442C17 14.2973 16.9355 14.1562 16.8203 14.051L12.4506 10.1602C12.374 10.0937 12.2805 10.0462 12.1792 10.021C12.2805 10.0461 12.3741 10.0937 12.4506 10.1602L16.8204 14.051C16.9356 14.1562 17.0001 14.2973 17.0001 14.4442C17.0001 14.5911 16.9356 14.7322 16.8204 14.8373C16.7023 14.9399 16.5438 14.9974 16.3788 14.9974C16.3535 14.9974 16.3283 14.996 16.3035 14.9934Z"
                fill={fill}
            />
        </svg>
    );
};
