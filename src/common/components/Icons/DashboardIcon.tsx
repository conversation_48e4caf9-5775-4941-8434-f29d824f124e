import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const DashboardIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.99902 5H5.99902C5.44674 5 4.99902 5.44772 4.99902 6V13C4.99902 13.5523 5.44674 14 5.99902 14H9.99902C10.5513 14 10.999 13.5523 10.999 13V6C10.999 5.44772 10.5513 5 9.99902 5Z"
                fill={fill}
            />
            <path
                d="M12.999 19H17.999C18.5513 19 18.999 18.5523 18.999 18V11C18.999 10.4477 18.5513 10 17.999 10H12.999C12.4467 10 11.999 10.4477 11.999 11V18C11.999 18.5523 12.4467 19 12.999 19Z"
                fill={fill}
            />
            <path
                d="M9.99902 15H5.99902C5.44674 15 4.99902 15.4477 4.99902 16V18C4.99902 18.5523 5.44674 19 5.99902 19H9.99902C10.5513 19 10.999 18.5523 10.999 18V16C10.999 15.4477 10.5513 15 9.99902 15Z"
                fill={fill}
            />
            <path
                d="M12.999 9H17.999C18.5513 9 18.999 8.55228 18.999 8V6C18.999 5.44772 18.5513 5 17.999 5L12.999 5C12.4467 5 11.999 5.44772 11.999 6V8C11.999 8.55228 12.4467 9 12.999 9Z"
                fill={fill}
            />
        </svg>
    );
};
