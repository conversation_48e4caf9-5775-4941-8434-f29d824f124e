import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { Icon, IconProps } from './Icon';

export const AlignLeftIcon: typeof Icon = ({
    fill = Colors.CM2,
    size = IconSize.M,
    className,
}: IconProps) => {
    return (
        <svg
            className={className}
            width={size}
            style={{ minWidth: size }}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M18.8 8.74583C18.8 8.93626 18.7244 9.11888 18.5897 9.25353C18.4551 9.38819 18.2725 9.46383 18.082 9.46383H5.15404C4.96361 9.46383 4.78098 9.38819 4.64633 9.25353C4.51168 9.11888 4.43604 8.93626 4.43604 8.74583C4.43604 8.55541 4.51168 8.37278 4.64633 8.23813C4.78098 8.10348 4.96361 8.02783 5.15404 8.02783H18.082C18.2725 8.02783 18.4551 8.10348 18.5897 8.23813C18.7244 8.37278 18.8 8.55541 18.8 8.74583Z"
                fill={fill}
            />
            <path
                d="M14.491 5.87327C14.491 6.0637 14.4154 6.24633 14.2807 6.38098C14.1461 6.51563 13.9635 6.59127 13.773 6.59127H5.15404C4.96361 6.59127 4.78098 6.51563 4.64633 6.38098C4.51168 6.24633 4.43604 6.0637 4.43604 5.87327C4.43604 5.68285 4.51168 5.50022 4.64633 5.36557C4.78098 5.23092 4.96361 5.15527 5.15404 5.15527H13.772C13.8664 5.15514 13.9599 5.17362 14.0471 5.20964C14.1343 5.24567 14.2136 5.29853 14.2804 5.36522C14.3472 5.4319 14.4001 5.5111 14.4363 5.59828C14.4724 5.68545 14.491 5.7789 14.491 5.87327Z"
                fill={fill}
            />
            <path
                d="M18.8 14.4909C18.8 14.6814 18.7244 14.864 18.5897 14.9987C18.4551 15.1333 18.2725 15.209 18.082 15.209H5.15404C4.96361 15.209 4.78098 15.1333 4.64633 14.9987C4.51168 14.864 4.43604 14.6814 4.43604 14.4909C4.43604 14.3005 4.51168 14.1179 4.64633 13.9832C4.78098 13.8486 4.96361 13.7729 5.15404 13.7729H18.082C18.2725 13.7729 18.4551 13.8486 18.5897 13.9832C18.7244 14.1179 18.8 14.3005 18.8 14.4909Z"
                fill={fill}
            />
            <path
                d="M14.491 11.6189C14.491 11.8093 14.4154 11.9919 14.2807 12.1266C14.1461 12.2612 13.9635 12.3369 13.773 12.3369H5.15404C4.96361 12.3369 4.78098 12.2612 4.64633 12.1266C4.51168 11.9919 4.43604 11.8093 4.43604 11.6189C4.43604 11.4285 4.51168 11.2458 4.64633 11.1112C4.78098 10.9765 4.96361 10.9009 5.15404 10.9009H13.772C13.8664 10.9007 13.9599 10.9192 14.0471 10.9552C14.1343 10.9913 14.2136 11.0441 14.2804 11.1108C14.3472 11.1775 14.4001 11.2567 14.4363 11.3439C14.4724 11.4311 14.491 11.5245 14.491 11.6189Z"
                fill={fill}
            />
            <path
                d="M14.491 17.364C14.491 17.5544 14.4154 17.737 14.2807 17.8717C14.1461 18.0064 13.9635 18.082 13.773 18.082H5.15404C4.96361 18.082 4.78098 18.0064 4.64633 17.8717C4.51168 17.737 4.43604 17.5544 4.43604 17.364C4.43604 17.2697 4.45461 17.1763 4.49069 17.0892C4.52677 17.0021 4.57966 16.923 4.64633 16.8563C4.713 16.7896 4.79216 16.7367 4.87927 16.7006C4.96638 16.6646 5.05975 16.646 5.15404 16.646H13.772C13.8664 16.6459 13.9599 16.6643 14.0471 16.7004C14.1343 16.7364 14.2136 16.7893 14.2804 16.8559C14.3472 16.9226 14.4001 17.0018 14.4363 17.089C14.4724 17.1762 14.491 17.2696 14.491 17.364Z"
                fill={fill}
            />
        </svg>
    );
};
