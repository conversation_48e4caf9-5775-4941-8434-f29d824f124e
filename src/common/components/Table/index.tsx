/* eslint-disable @typescript-eslint/no-explicit-any */
import { TableContainer } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import {
    hasNextPage,
    hasPreviousPage,
    initialTableQuery,
    TableQuerySetting,
    totalPagesCount,
} from './helpers';
import { ApiSearch, TablePaginationQuery, TableProviderColumn, TableResponse } from './types';

const TableContext = createContext<any>(null);

export type ApiSearchQuery<T> = {
    queryKey: string[];
    queryFn: ApiSearch<T>;
};

export type TableProviderProps<T> = {
    children: React.ReactNode;
    apiSearch: ApiSearchQuery<T>;
    querySetting?: TableQuerySetting;
    tableEnhanced?: React.JSX.Element;
    columns: TableProviderColumn<T>[];
    startActive?: boolean;
    delay?: number;
};

export enum TableStatus {
    LOADING = 'loading',
    SHOW_DATA = 'showData',
    SHOW_EMPTY = 'showEmpty',
    SHOW_NOT_FOUND = 'showNotFound',
}

export const TableProvider = <T,>({
    children,
    columns,
    apiSearch,
    querySetting,
    startActive = true,
}: TableProviderProps<T>) => {
    const [query, setQuery] = useState<TablePaginationQuery>(initialTableQuery(querySetting));
    const [tableStatus, setTableStatus] = useState<TableStatus>(TableStatus.LOADING);
    const [active, setActive] = useState<boolean>(startActive);
    const [page, setPage] = useState<TableResponse<T>>({
        rows: [],
        totalCount: 0,
        hasAnyRecord: false,
    });

    const { refetch } = useQuery<TableResponse<T>, Error>({
        queryKey: [...apiSearch.queryKey, query.page, query.pageSize, query.searchValue],
        queryFn: () => apiSearch.queryFn(query),
        onSuccess: (page: TableResponse<T>) => {
            if (page) {
                setPage(page);
                showTableStatus(page.totalCount > 0, page.hasAnyRecord);
            }
        },
    });

    const setPageHasAnyRecord = useCallback((hasAnyRecord: boolean) => {
        setPage((prev) => ({ ...prev, hasAnyRecord }));
    }, []);

    const showTableStatus = useCallback(
        (hasData: boolean, hasAnyRecord: boolean) => {
            if (hasData) {
                setTableStatus(TableStatus.SHOW_DATA);
            } else if (hasAnyRecord) {
                setPageHasAnyRecord(true);
                setTableStatus(TableStatus.SHOW_NOT_FOUND);
            } else {
                setPageHasAnyRecord(false);
                setTableStatus(TableStatus.SHOW_EMPTY);
            }
        },
        [setPageHasAnyRecord]
    );

    const handleChangeQuery = useCallback((newValues: Partial<TablePaginationQuery>) => {
        setQuery((prev) => ({ ...prev, ...newValues }));
    }, []);

    useEffect(() => {
        setTableStatus(TableStatus.LOADING);
    }, [query.searchValue]);

    const handleToggleActive = useCallback(() => setActive((prev) => !prev), []);

    useEffect(() => {
        setActive(startActive);
    }, [startActive]);

    const contextValue = useMemo(() => {
        return {
            page,
            columns,
            query,
            totalPages: totalPagesCount(query, page.totalCount),
            handleChangeQuery,
            handleToggleActive,
            active,
            disable: !active,
            hasPreviousPage: hasPreviousPage(query),
            hasNextPage: hasNextPage(query, page.totalCount),
            refetch,
            tableStatus,
            isLoading: tableStatus === TableStatus.LOADING,
        };
    }, [page, columns, query, handleChangeQuery, handleToggleActive, active, refetch, tableStatus]);

    return (
        <TableContext.Provider value={contextValue}>
            <TableContainer>{children}</TableContainer>
        </TableContext.Provider>
    );
};

export type UseTable<T> = {
    page: TableResponse<T> & { isLoading: boolean };
    columns: TableProviderColumn<T>[];
    query: TablePaginationQuery;
    totalPages: number;
    handleChangeQuery: (_newQuery: Partial<TablePaginationQuery>) => void;
    handleToggleActive: () => void;
    refetch: () => void;
    active: boolean;
    disable: boolean;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
    isLoading: boolean;
    tableStatus: TableStatus;
};

export const useTable = <T,>(): UseTable<T> => useContext(TableContext);

export * from './common/TableBody';
export * from './common/TableHead';
export * from './common/TablePagination';
