/* eslint-disable @typescript-eslint/no-explicit-any */
import { TablePaginationQuery } from './types';

export type TableQuerySetting = {
    pageSize?: number;
    searchValue?: string;
};

export function initialTableQuery(setting?: TableQuerySetting): TablePaginationQuery {
    return {
        page: 0,
        pageSize: setting?.pageSize ?? 10,
        searchValue: setting?.searchValue ?? '',
    };
}

export function totalPagesCount(query: TablePaginationQuery, totalCount: number): number {
    return Math.ceil(totalCount / query.pageSize);
}

export function hasPreviousPage(query: TablePaginationQuery): boolean {
    return query.page > 0;
}

export function hasNextPage(query: TablePaginationQuery, totalCount: number): boolean {
    const totalPageCount = Math.ceil(totalCount / query.pageSize);
    return query.page + 1 < totalPageCount;
}

export function memoEqual(prevProps: unknown, nextProps: unknown) {
    return JSON.stringify(prevProps) === JSON.stringify(nextProps);
}
