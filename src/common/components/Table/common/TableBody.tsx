import {
    TableBody as MuiTableBody,
    TableBodyProps as MuiTableBodyProps,
    TableCell,
    TableRow,
    TableRowProps,
} from '@mui/material';
import { CustomTableCellProps } from 'common/components/TableCell';
import { memo, useMemo } from 'react';
import { TableStatus, useTable } from '..';
import { memoEqual } from '../helpers';
import { TableProviderColumn } from '../types';
import { TableSpinner } from './TableSpinner';
import { TableWithoutData, TableWithoutDataProps } from './TableWithoutData';

type TableBodyProps = Omit<MuiTableBodyProps, 'children'> & {
    tableRow?: Omit<TableRowProps, 'children'>;
    tableCell?: Omit<CustomTableCellProps, 'children'>;
    rowSize?: number;
    withoutDataConfig?: TableWithoutDataProps;
    notFoundConfig?: TableWithoutDataProps;
} & WithoutDataProps;

export const TableBody = memo(
    <T,>({
        tableRow,
        tableCell,
        rowSize = 16,
        notFoundConfig,
        withoutDataConfig,
        ...tableBody
    }: TableBodyProps) => {
        const { tableStatus, page, query } = useTable<T>();

        const { rows, fullHeight, emptyRowsHeight } = useMemo(() => {
            const rows = page && Array.isArray(page.rows) ? page.rows : [];
            const fullHeight = `${rowSize * query.pageSize}px`;
            const emptyRowsCount = query.pageSize - rows.length;
            const emptyRowsHeight = `${rowSize * Math.max(0, emptyRowsCount)}px`;

            return { rows, fullHeight, emptyRowsHeight };
        }, [page, query, rowSize]);

        const config = useMemo(() => {
            return tableStatus === TableStatus.SHOW_NOT_FOUND ? notFoundConfig : withoutDataConfig;
        }, [tableStatus, notFoundConfig, withoutDataConfig]);

        const renderContent = () => {
            switch (tableStatus) {
                case TableStatus.LOADING:
                    return <TableSpinner height={fullHeight} />;

                case TableStatus.SHOW_EMPTY:
                case TableStatus.SHOW_NOT_FOUND: {
                    if (!config) return null;

                    return <TableWithoutData {...config} />;
                }

                case TableStatus.SHOW_DATA:
                    return (
                        <MuiTableBody
                            {...tableBody}
                            sx={{
                                height: fullHeight,
                                ...(tableBody.sx || {}),
                            }}
                        >
                            {rows.map((row: T, index: number) => (
                                <MemoizedTableRow
                                    key={`row-${index}`}
                                    row={row}
                                    tableCell={tableCell}
                                    tableRow={tableRow}
                                />
                            ))}
                            {rows.length < query.pageSize && (
                                <EmptySpacerRow height={emptyRowsHeight} />
                            )}
                        </MuiTableBody>
                    );

                default:
                    return null;
            }
        };

        return renderContent();
    },
    (prevProps, nextProps) => memoEqual(prevProps, nextProps)
);
TableBody.displayName = 'TableBody';

type WithoutDataProps = {
    withoutDataConfig?: TableWithoutDataProps;
    notFoundConfig?: TableWithoutDataProps;
};

type MemoizedTableRowProps<T> = {
    row: T;
    tableCell?: Omit<CustomTableCellProps, 'children'>;
    tableRow?: Omit<TableRowProps, 'children'>;
};

const MemoizedTableRow = memo(
    <T,>({ row, tableCell, tableRow }: MemoizedTableRowProps<T>) => {
        const table = useTable<T>();
        return (
            <TableRow {...tableRow}>
                {table.columns.map(({ body }: TableProviderColumn<T>, colIndex: number) => (
                    <TableCell
                        key={colIndex}
                        {...tableCell}
                        sx={{
                            width: 'auto',
                            whiteSpace: 'nowrap',
                            ...(tableCell?.sx || {}),
                        }}
                    >
                        {body(row, table)}
                    </TableCell>
                ))}
            </TableRow>
        );
    },
    (prevProps, nextProps) => memoEqual(prevProps, nextProps)
);

const EmptySpacerRow = memo(({ height }: { height: string }) => <TableRow sx={{ height }} />);
