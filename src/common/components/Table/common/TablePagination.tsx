import { Pagination, PaginationProps } from '@mui/material';
import { useCallback, useMemo } from 'react';
import { useTable } from '..';

type TablePaginationProps = Omit<
    PaginationProps,
    'count' | 'page' | 'onChange' | 'hasPreviousPage' | 'hasNextPage'
>;

export const TablePagination = <T,>(props: TablePaginationProps) => {
    const { query, handleChangeQuery, totalPages, hasNextPage, hasPreviousPage, refetch } =
        useTable<T>();

    const currentPage = useMemo(() => query.page + 1, [query.page]);

    const changePageHandler = useCallback(
        (_: React.ChangeEvent<unknown>, page: number) => {
            const newPage = page - 1;
            if (query.page === newPage) {
                refetch();
            } else {
                handleChangeQuery({ page: newPage });
            }
        },
        [handleChangeQuery, query.page, refetch]
    );

    return (
        <Pagination
            count={totalPages}
            page={currentPage}
            onChange={changePageHandler}
            {...{ hasPreviousPage, hasNextPage }}
            {...props}
        />
    );
};
TablePagination.displayName = 'TablePagination';
