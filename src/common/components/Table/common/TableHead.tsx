import {
    TableHead as MuiTableHead,
    TableHeadProps as MuiTableHeadProps,
    TableRow,
    TableRowProps,
    TableSortLabelProps,
} from '@mui/material';
import TableCellUI, { TableCellProps } from '@mui/material/TableCell';
import { memo, useMemo } from 'react';
import { useTable } from '..';
import { memoEqual } from '../helpers';
import { HeadCell } from '../types';

type TableHeadProps = Omit<MuiTableHeadProps, 'children'> & {
    tableRow?: Omit<TableRowProps, 'children'>;
    tableSort?: Omit<TableSortLabelProps, 'children'>;
    tableCell?: Omit<TableCellProps, 'children'>;
};

export type TableHeadCellProps<T> = Omit<TableCellProps, 'children'> & {
    head: HeadCell<T>;
    tableSort?: Omit<TableSortLabelProps, 'children'>;
};

export const TableHead = memo(
    ({ tableRow, tableSort, tableCell, ...tableHead }: TableHeadProps) => {
        const { columns } = useTable();

        const memoizedColumns = useMemo(() => columns, [columns]);

        return (
            <MuiTableHead {...tableHead}>
                <TableRow {...tableRow}>
                    {memoizedColumns.map(({ head }) => (
                        <TableHeadCell
                            key={head.field}
                            {...tableCell}
                            head={head}
                            tableSort={tableSort}
                        />
                    ))}
                </TableRow>
            </MuiTableHead>
        );
    },
    (prevProps, nextProps) => memoEqual(prevProps, nextProps)
);
TableHead.displayName = 'TableHead';

const TableHeadCell = memo(
    <T,>({ head, tableSort, ...props }: TableHeadCellProps<T>) => {
        const table = useTable<T>();
        return <TableCellUI {...props}>{head.render(table)}</TableCellUI>;
    },
    (prevProps, nextProps) => memoEqual(prevProps, nextProps)
);
