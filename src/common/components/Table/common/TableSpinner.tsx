import { Box, TableBody, TableCell, TableRow } from '@mui/material';
import AreaSpinner from 'common/components/AreaSpinner';
import { useTable } from '..';

type TableSpinnerProps = {
    height: string;
};

export const TableSpinner = <T,>({ height }: TableSpinnerProps) => {
    const { columns } = useTable<T>();

    return (
        <TableBody sx={{ height, width: '100%' }}>
            <TableRow sx={{ height: '100%', width: '100%' }}>
                <TableCell
                    colSpan={columns.length}
                    sx={{
                        padding: 0,
                        height: '100%',
                        position: 'relative',
                    }}
                >
                    <SpinnerContent />
                </TableCell>
            </TableRow>
        </TableBody>
    );
};
TableSpinner.displayName = 'TableSpinner';

const SpinnerContent = () => (
    <Box
        sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
        }}
    >
        <AreaSpinner />
    </Box>
);
