import { Box, styled, TableBody, TableCell, TableRow } from '@mui/material';
import { useMemo } from 'react';
import { useTable } from '..';

export type TableWithoutDataProps = {
    height: string | number;
    image: string;
    title: string;
    description: string;
};

export const TableWithoutData = <T,>({
    height,
    image,
    title,
    description,
}: TableWithoutDataProps) => {
    const table = useTable<T>();

    const columnCount = useMemo(() => table.columns.length, [table.columns]);

    return (
        <TableBody>
            <TableRow>
                <TableCell
                    colSpan={columnCount}
                    sx={{
                        height,
                        border: 'none',
                        padding: 0,
                        verticalAlign: 'middle',
                    }}
                >
                    <CenteredContentWrapper disable={table.disable}>
                        <WithoutDataImage image={image} />
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                gap: '8px',
                                mt: 4,
                            }}
                        >
                            <WithoutDataTitle>{title}</WithoutDataTitle>
                            <WithoutDataDescription>{description}</WithoutDataDescription>
                        </Box>
                    </CenteredContentWrapper>
                </TableCell>
            </TableRow>
        </TableBody>
    );
};
TableWithoutData.displayName = 'TableWithoutData';

const CenteredContentWrapper = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'disable',
})<{ disable?: boolean }>(({ disable }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    opacity: disable ? 0.5 : 1,
}));

const WithoutDataImage = styled('div')<{ image: string }>(({ image }) => ({
    backgroundImage: `url(${image})`,
    height: 284,
    width: 405,
    backgroundRepeat: 'no-repeat',
}));

const WithoutDataTitle = styled('div')({
    fontSize: '18px',
    fontWeight: 700,
    color: '#0069FF',
});

const WithoutDataDescription = styled('div')({
    fontSize: '18px',
    fontWeight: 500,
    color: '#899198',
});
