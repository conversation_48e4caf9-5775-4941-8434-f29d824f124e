import { ReactNode } from 'react';
import { UseTable } from '.';

export type TableProviderColumn<T> = {
    head: HeadCell<T>;
    body: BodyCellRender<T>;
};

export type HeadCell<T> = {
    render: (_tableContext: UseTable<T>) => ReactNode;
    field: string;
};

export type BodyCellRender<T> = (_item: T, _tableContext: UseTable<T>) => ReactNode | string;

export type TablePaginationQuery = {
    page: number;
    pageSize: number;
    searchValue: string;
};

export type InputSearch = {
    fields: string[];
    value: string;
};

export type TableResponse<T> = {
    rows: T[];
    totalCount: number;
    hasAnyRecord: boolean;
};

export type ApiSearch<T> = (_query: TablePaginationQuery) => Promise<TableResponse<T>>;

export type TableSort = {
    field: string;
    value: 'asc' | 'desc';
};
