.root {
    position: relative;
}

.handle {
    position: absolute;
    height: 100%;
    top: 0;
    width: 20px;
    z-index: 1;
    cursor: col-resize;
    right: -10px;
}

.handle::after {
    content: '';
    display: block;
    width: 4px;
    height: 100%;
    margin-left: 8px;
}

.handle:hover::after,
.root:global(.ResizableColumn-resizing) .handle::after {
    background-color: var(--cm1);
}

.resizingBody {
    cursor: col-resize;
}

.resizingBody * {
    user-select: none !important;
}
