import clsx from 'clsx';
import { HTMLAttributes, PureComponent, ReactNode } from 'react';
import styles from './ResizableColumn.module.css';

export type ResizeEvent = {
    width: number;
    nativeEvent: MouseEvent;
};

export type ResizableProps = Omit<HTMLAttributes<HTMLDivElement>, 'onResize'> & {
    onResize?: (event: ResizeEvent) => void;
    maxWidth?: number;
    minWidth?: number;
    disabled?: boolean;
};

type ResizableState = {
    resizing: boolean;
};

export const resizableClasses = {
    handle: 'ResizableColumn-handle',
    resizing: 'ResizableColumn-resizing',
};

export class Resizable extends PureComponent<ResizableProps, ResizableState> {
    constructor(props: Readonly<ResizableProps>) {
        super(props);
        this.onHandleMouseDown = this.onHandleMouseDown.bind(this);

        this.state = {
            resizing: false,
        };
    }

    private startXPosition: number = 0;
    private startWidth: number = 0;
    private previousWidth: number = 0;
    element: HTMLDivElement | null = null;

    onHandleMouseDown(event: React.MouseEvent<HTMLDivElement>) {
        if (this.props.disabled) return;
        if (!this.element) return;
        this.startXPosition = event.clientX;
        this.startWidth = this.element.clientWidth;
        this.previousWidth = NaN;

        this.setState({
            resizing: true,
        });

        const onMouseMove = (e: MouseEvent) => {
            if (!this.element) return;

            const { minWidth, maxWidth } = this.props;

            const diff = e.clientX - this.startXPosition;
            let newWidth = Math.round(this.startWidth + diff);
            if (minWidth !== undefined && newWidth < minWidth) newWidth = minWidth;
            else if (maxWidth !== undefined && newWidth > maxWidth) newWidth = maxWidth;

            if (newWidth === this.previousWidth) return;

            if (this.props.onResize) {
                this.props.onResize({
                    nativeEvent: e,
                    width: newWidth,
                });
                this.previousWidth = newWidth;
            }
        };

        const onStopCallback = () => {
            this.setState({
                resizing: false,
            });

            document.body.classList.remove(styles.resizingBody);
            document.removeEventListener('mouseup', onStopCallback);
            document.removeEventListener('mouseleave', onStopCallback);
            document.removeEventListener('mousemove', onMouseMove);
        };

        document.body.classList.add(styles.resizingBody);
        document.addEventListener('mouseup', onStopCallback);
        document.addEventListener('mouseleave', onStopCallback);
        document.addEventListener('mousemove', onMouseMove);
    }

    componentDidUpdate(
        prevProps: Readonly<ResizableProps>,
        _prevState: Readonly<ResizableState>,
        _snapshot?: unknown
    ): void {
        if (
            prevProps.disabled !== this.props.disabled &&
            this.props.disabled &&
            this.state.resizing
        ) {
            this.setState({
                resizing: false,
            });
        }
    }

    render(): ReactNode {
        const { children, className, onResize: _, disabled, ...props } = this.props;

        return (
            <div
                ref={(el) => (this.element = el)}
                draggable="false"
                onDragStart={preventDefault}
                className={clsx(
                    styles.root,
                    this.state.resizing && resizableClasses.resizing,
                    className
                )}
                {...props}
            >
                {children}
                {!disabled && (
                    <div
                        className={`${styles.handle} ${resizableClasses.handle}`}
                        onMouseDown={this.onHandleMouseDown}
                    />
                )}
            </div>
        );
    }
}

const preventDefault = (e: { preventDefault(): void }) => e.preventDefault();
