import { Box, IconButton, styled } from '@mui/material';
import clsx from 'clsx';
import { motion, useAnimation } from 'framer-motion';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { Link } from 'react-router-dom';
import { isDarkOr } from 'utils/colors';
import { IconSize } from '../../styles/IconSize';
import { CheckIcon } from '../Icons/CheckIcon';
import { CloseIcon } from '../Icons/CloseIcon';
import DragAndDropIcon from '../Icons/DragAndDropIcon';
import { InfoIcon } from '../Icons/InfoIcon';
import { WarningIcon } from '../Icons/WarningIcon';
import { INotificationProps, NotificationType } from './INotificationProps';
import NotificationLine from './NotificationLine';
import ProgressIcon from './ProgressIcon';

const animationVariants = {
    slide: {
        initial: { x: -400 },
        appear: {
            x: 0,
            transition: {
                duration: 0.6,
            },
        },
        disappear: {
            x: -400,
            transition: {
                duration: 0.3,
            },
        },
    },
    grow: {
        initial: { scale: 0.8, opacity: 0 },
        appear: {
            scale: 1,
            opacity: 1,
            transition: {
                duration: 0.6,
            },
        },
        disappear: {
            scale: 0.8,
            opacity: 0,
            transition: {
                duration: 0.2,
            },
        },
    },
};

const Notification = ({
    title,
    body,
    bodyElement: BodyElement,
    type,
    expiresAt: expiresAtProp,
    createdAt: createdAtProp,
    animationType = 'slide',
    link,
    onCloseNotification,
    customTheme,
    count = 1,
    isDragging = false,
    draggable,
    closeable = true,
    animateAppearance = true,
    icon,
}: INotificationProps) => {
    const now = useMemo(() => Date.now(), []);
    const defaultExpiresAt = useMemo(() => Date.now() + (link ? 8 : 5) * 1000, [link]);
    const expiresAt = expiresAtProp ?? defaultExpiresAt;
    const countPrev = useRef<number>(count);
    const color = (() => {
        if (type === NotificationType.danger) return 'var(--danger)';
        if (type === NotificationType.warning) return 'var(--yellow)';
        if (type === NotificationType.success) return 'var(--success)';
        if (type === NotificationType.info) return 'var(--cm1)';
        if (type === NotificationType.custom && customTheme) return customTheme.color;
    })();
    const createdAt = createdAtProp ?? now;

    const controls = useAnimation();
    const counterControls = useAnimation();

    useEffect(() => {
        if (countPrev.current !== count) {
            counterControls.stop();
            counterControls.set({ scale: 1 });
            counterControls.start({
                scale: [1, 1.2, 1],
                rotate: [0, (Math.random() - 0.5) * 16, 0],
                transition: {
                    duration: 0.6,
                    repeat: 0,
                    times: [0, 0.3, 0.6],
                },
            });
            countPrev.current = count;
        }
    }, [count, counterControls, type]);

    useEffect(() => {
        if (animateAppearance) {
            controls.stop();
            controls.start('appear');
        }
    }, [controls, animateAppearance]);

    const close = useCallback(() => {
        controls.stop();
        controls.start('disappear').then(() => {
            onCloseNotification();
        });
    }, [onCloseNotification, controls]);

    useEffect(() => {
        if (expiresAt < 0) return () => {};

        const msRemaining = Math.max(expiresAt - Date.now(), 0);
        const t = setTimeout(close, msRemaining);
        return () => clearTimeout(t);
    }, [expiresAt, link, close]);

    return (
        <DivMainContainer
            initial={animateAppearance ? animationVariants[animationType].initial : undefined}
            variants={animationVariants[animationType]}
            animate={controls}
            color={color}
            className={clsx({ isDragging })}
        >
            {draggable && (
                <Box
                    sx={{
                        position: 'absolute',
                        left: -2,
                        top: 2,
                        zIndex: 1,
                        borderRadius: 100,
                        display: 'flex',
                        alignItems: 'end',
                        justifyContent: 'end',
                        padding: '2px',

                        '& *': {
                            cursor: 'grab',
                        },

                        '&:hover': {
                            '& > svg': {
                                fill: 'var(--cm1)',
                                stroke: 'var(--cm1)',
                            },
                        },
                    }}
                    {...draggable.attributes}
                    {...draggable.listeners}
                >
                    <DragAndDropIcon fill="var(--neutral8)" size={25} />
                </Box>
            )}
            <NotificationLine createdAt={createdAt} expiresAt={expiresAt} />
            <DivContent>
                {closeable && (
                    <DivCloseButtonContainer>
                        <IconButtonClose size="small" onClick={close}>
                            <CloseIcon fill={'var(--neutral5)'} size={IconSize.M} />
                        </IconButtonClose>
                    </DivCloseButtonContainer>
                )}
                <DivIconContainer color={color}>
                    {icon ? (
                        icon
                    ) : (
                        <>
                            {type === NotificationType.success && (
                                <CheckIcon fill={'var(--neutral1)'} size={IconSize.M} />
                            )}
                            {type === NotificationType.warning && (
                                <WarningIcon fill={'var(--neutral1)'} size={IconSize.M} />
                            )}
                            {type === NotificationType.danger && (
                                <CloseIcon fill={'var(--neutral1)'} size={IconSize.M} />
                            )}
                            {type === NotificationType.info && (
                                <InfoIcon fill={'var(--neutral1)'} size={IconSize.M} />
                            )}
                            {type === NotificationType.progress && <ProgressIcon />}
                            {type === NotificationType.custom && customTheme && (
                                <customTheme.Icon fill={customTheme.IconColor} size={IconSize.M} />
                            )}
                        </>
                    )}
                </DivIconContainer>
                <DivBody>
                    <DivTitleContent>
                        <SpanTitle>
                            {title}&nbsp;
                            {count > 1 && (
                                <DivCounter animate={counterControls}>x{count}</DivCounter>
                            )}
                        </SpanTitle>
                    </DivTitleContent>
                    {(body || BodyElement) && (
                        <SpanDescription>
                            {body}
                            {BodyElement ? <BodyElement /> : ''}
                        </SpanDescription>
                    )}
                    {link && <StyledLink to={link.url}>{link.label}</StyledLink>}
                </DivBody>
            </DivContent>
        </DivMainContainer>
    );
};

const DivMainContainer = styled(motion.div, {
    shouldForwardProp: (prop) => !['color'].includes(prop as string),
})<{ color: string | undefined }>(({ theme, color }) => ({
    width: 295,
    borderRadius: 11,
    overflow: 'hidden',
    background: theme.palette.neutral[1],
    boxShadow: '0 0 13px 0 rgba(201, 205, 211, 0.57) !important',
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    '--notification-color': color,
    '--notification-text-color':
        color && isDarkOr(color, false, 0.7) ? theme.palette.neutral[1] : '#000',
    transition: 'box-shadow .15s',

    '&.isDragging': {
        boxShadow: '0 0 16px 0 rgba(200, 200, 220, 1) !important',
    },
}));

const DivIconContainer = styled('div', {
    shouldForwardProp: (prop) => !['color'].includes(prop as string),
})<{ color: string | undefined }>(({ color }) => ({
    width: 24,
    height: 24,
    borderRadius: '50%',
    margin: '0 14px',
    backgroundColor: color,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
}));

const DivTitleContent = styled('div')({
    width: 237,
    minHeight: 14,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
});

const SpanTitle = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    paddingRight: 27,
    letterSpacing: 'normal',
    textAlign: 'left',
}));

const DivCounter = styled(motion.div)({
    backgroundColor: 'var(--notification-color)',
    color: 'var(--notification-text-color)',
    borderRadius: 10,
    padding: '2px 4px',
    display: 'inline-block',
});

const SpanDescription = styled('span')(({ theme }) => ({
    ...theme.typography.h6,
    color: theme.palette.neutral[7],
    fontWeight: 400,
    letterSpacing: 'normal',
    textAlign: 'left',
    marginTop: 6,
    marginRight: 12,
}));

const IconButtonClose = styled(IconButton)({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: 'none',
    cursor: 'pointer',
    backgroundColor: 'transparent',

    '&:focus': {
        outline: 'none',
    },
});

const DivCloseButtonContainer = styled('div')({
    position: 'absolute',
    top: 9,
    right: 15,
});

const DivBody = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    marginRight: 15,
});

const DivContent = styled('div')(({ theme }) => ({
    position: 'relative',
    paddingTop: 16,
    paddingBottom: 15,
    borderRadius: '0 11px 11px 0',
    backgroundColor: theme.palette.neutral[1],
    display: 'flex',
    alignItems: 'center',
}));

const StyledLink = styled(Link)(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.primary.main,
    fontWeight: 400,
    marginTop: 11,
    letterSpacing: 'normal',
    textAlign: 'left',
    cursor: 'pointer',
    textDecoration: 'none',
}));

export default Notification;
