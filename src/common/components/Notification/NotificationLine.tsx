import { styled } from '@mui/material';
import { motion, useAnimation } from 'framer-motion';
import { useEffect } from 'react';

type NotificationLineProps = {
    expiresAt: number;
    createdAt: number;
};

export default function NotificationLine({ expiresAt, createdAt }: NotificationLineProps) {
    const controls = useAnimation();

    useEffect(() => {
        if (expiresAt < 0) return;
        const totalDuration = expiresAt - createdAt;
        if (totalDuration < 0) {
            controls.set('expired');
        } else {
            controls.stop();
            // set scale according to how long we have left until expiration
            const remaining = 1 - (Date.now() - createdAt) / totalDuration;
            controls.set({
                scaleX: Math.max(0, remaining),
            });
            controls.start(
                { scaleX: 0 },
                { duration: (totalDuration * remaining) / 1000, ease: 'linear' }
            );
        }
    }, [expiresAt, createdAt, controls]);

    if (expiresAt < 0) return null;

    return (
        <DivRoot animate={controls} transition={{ duration: (expiresAt - Date.now()) / 1000 }} />
    );
}

const DivRoot = styled(motion.div)({
    height: 4,
    position: 'absolute',
    top: 0,
    zIndex: 1,
    left: 0,
    backgroundColor: 'var(--notification-color)',
    opacity: 0.5,
    width: '100%',
    transformOrigin: 'left',
});
