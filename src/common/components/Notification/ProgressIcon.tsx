import { styled } from '@mui/material';
import { IconSize } from 'common/styles/IconSize';
import { SyncUpIcon } from '../Icons/SyncUpIcon';

export default function ProgressIcon() {
    return <StyledSyncUpIcon size={IconSize.M} />;
}

const StyledSyncUpIcon = styled(SyncUpIcon)({
    rotating: {
        animation: '$rotate 1.8s linear infinite',
    },

    '@keyframes rotate': {
        '0%': {
            transform: 'rotate(0deg)',
        },
        '100%': {
            transform: 'rotate(360deg)',
        },
    },
});
