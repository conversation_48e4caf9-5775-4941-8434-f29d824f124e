import { DraggableAttributes } from '@dnd-kit/core';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { Colors } from '../../styles/Colors';
import { Icon } from '../Icons/Icon';

export enum NotificationType {
    danger = 'danger',
    warning = 'warning',
    success = 'success',
    info = 'info',
    custom = 'custom',
    progress = 'progress',
}
export interface INotificationProps extends INotificationBaseProps {
    bodyElement?: (props: any) => JSX.Element;
    /**
     * @deprecated
     */
    customTheme?: { Icon: typeof Icon; IconColor: Colors; color: Colors };
    icon?: React.ReactNode;
    expiresAt?: number;
    createdAt?: number;
    animationType?: 'slide' | 'grow';
    link?: {
        label: string;
        url: string;
    };
    isDragging?: boolean;
    onCloseNotification: () => void;
    count?: number;
    draggable?: {
        attributes: DraggableAttributes | undefined;
        listeners: SyntheticListenerMap | undefined;
    };
    closeable?: boolean;
    animateAppearance?: boolean;
}

export interface INotificationBaseProps {
    title?: React.ReactNode;
    body?: React.ReactNode;
    type: NotificationType;
}
