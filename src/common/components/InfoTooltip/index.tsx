import { Box, BoxProps, styled, SxProps, Theme, TooltipProps } from '@mui/material';
import { ComponentProps, ComponentType, forwardRef } from 'react';
import { IconProps } from '../Icons/Icon';
import { InfoIcon } from '../Icons/InfoIcon';
import ArrowTooltip from '../Tooltip';

type InfoTooltipProps = {
    text: string;
    position?: string;
    onClick?: React.MouseEventHandler;
    sx?: SxProps<Theme>;
    slotProps?: {
        internal?: ComponentProps<typeof InfoTooltipInternal>;
    };
    className?: string;
};

const InfoTooltip = ({
    position,
    text,
    onClick,
    sx,
    slotProps = {},
    className,
}: InfoTooltipProps) => {
    return (
        <ArrowTooltip
            sx={sx}
            content={text}
            position={(position || 'bottom') as TooltipProps['placement']}
        >
            <InfoTooltipInternal onClick={onClick} {...slotProps.internal} className={className} />
        </ArrowTooltip>
    );
};

export const InfoTooltipInternal = forwardRef(
    (
        {
            IconComponent = InfoIcon,
            className,
            ...props
        }: {
            IconComponent?: ComponentType<IconProps>;
        } & BoxProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        // TODO (MB) TS complains here, understand what is going on

        return (
            <BoxNoticeIconContainer
                // @ts-ignore
                ref={ref}
                className={className}
                {...props}
            >
                <IconComponent size={14} fill={'var(--neutral1)'} />
            </BoxNoticeIconContainer>
        );
    }
);

const BoxNoticeIconContainer = styled(Box)(({ theme }) => ({
    width: 14,
    height: 14,
    display: 'inline-block',
    borderRadius: '50%',
    backgroundColor: theme.palette.neutral[5],
    marginLeft: 5,
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[1],
    fontWeight: 400,

    '&:hover': {
        backgroundColor: theme.palette.primary.main,
    },
}));

export default InfoTooltip;
