import useResizeObserver from '@react-hook/resize-observer';
import useForceRender from 'common/hooks/useForceRender';
import { useCallback, useEffect, useRef, useState } from 'react';
import ArrowTooltip from '../Tooltip';

type TextOverflowTooltipProps<T extends HTMLElement> = {
    children: (ref: React.Ref<T>) => React.ReactElement<any, any>;
    text: string;
};

export default function TextOverflowTooltip<T extends HTMLElement>({
    children,
    text,
}: TextOverflowTooltipProps<T>) {
    const [el, setEl] = useState<T | null>(null);
    const fr = useForceRender();
    const isOverflowingRef = useRef(false);

    useResizeObserver(
        el,
        useCallback(
            (x) => {
                const val = x.target.scrollWidth > x.target.clientWidth;
                if (val !== isOverflowingRef.current) {
                    isOverflowingRef.current = val;
                    fr();
                }
            },
            [fr]
        )
    );

    useEffect(() => {
        if (!el) return;

        const val = el.scrollWidth > el.clientWidth;
        if (val !== isOverflowingRef.current) {
            isOverflowingRef.current = val;
            fr();
        }
    }, [fr, text, el]);

    return (
        <ArrowTooltip content={text} disabled={!isOverflowingRef.current}>
            {children(setEl)}
        </ArrowTooltip>
    );
}
