import { Tooltip, TooltipProps, styled, tooltipClasses } from '@mui/material';

type ArrowTooltipProps = Omit<TooltipProps, 'title' | 'placement' | 'content'> & {
    content?: TooltipProps['title'];
    position?: TooltipProps['placement'];
    disabled?: boolean;
};

const ArrowTooltip = ({
    content,
    children,
    position,
    disabled = false,
    ...props
}: ArrowTooltipProps) => {
    if (disabled) {
        return <>{children}</>;
    }

    return (
        <StyledTooltip placement={position} arrow title={content} {...props}>
            {children}
        </StyledTooltip>
    );
};

export default ArrowTooltip;

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#fff',
        color: theme.palette.neutral[7],
        filter: `drop-shadow(0px 0px 1px ${theme.palette.neutral[7]})`,
        borderRadius: 10,
        padding: '6px 7px',
        willChange: 'transform, opacity',
    },

    [`& .${tooltipClasses.arrow}::before`]: {
        backgroundColor: '#fff',
    },
}));
