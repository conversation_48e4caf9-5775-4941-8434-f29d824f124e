import { useCallback } from 'react';

type ScrollDetectorProps = React.HTMLAttributes<HTMLDivElement>;

function updateElement(el: HTMLElement) {
    const top = el.scrollTop === 0;
    const left = el.scrollLeft === 0;
    const bottom = el.scrollTop + el.clientHeight === el.scrollHeight;
    const right = el.scrollLeft + el.clientWidth === el.scrollWidth;
    if (top) el.setAttribute('data-top', '');
    else el.removeAttribute('data-top');

    if (left) el.setAttribute('data-left', '');
    else el.removeAttribute('data-left');

    if (right) el.setAttribute('data-right', '');
    else el.removeAttribute('data-right');

    if (bottom) el.setAttribute('data-bottom', '');
    else el.removeAttribute('data-bottom');
}

function updateElementRef(el: HTMLElement | null) {
    if (el) updateElement(el);
}

export default function ScrollDetector({ onScroll, ...props }: ScrollDetectorProps) {
    const onScrollCallback: React.UIEventHandler<HTMLDivElement> = useCallback(
        (e) => {
            const el = e.target as HTMLDivElement;
            updateElement(el);
            if (onScroll) onScroll(e);
        },
        [onScroll]
    );

    return <div ref={updateElementRef} onScroll={onScrollCallback} {...props} />;
}
