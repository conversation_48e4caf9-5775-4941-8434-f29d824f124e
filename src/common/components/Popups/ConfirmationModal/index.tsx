import { styled } from '@mui/material';
import Box from '@mui/material/Box';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';

export type ConfirmationModalProps = {
    open: boolean;
    children?: React.ReactNode;
    cancelCaptionButton?: string;
    confirmationCaptionButton?: string;
    isError?: boolean;

    onClose: () => void;
    onConfirmation: () => void;
};

// TODO (AP) Rework using ConfirmationPopup
const ConfirmationModal = ({
    children,
    cancelCaptionButton,
    confirmationCaptionButton,
    open,
    isError,
    onClose,
    onConfirmation,
}: ConfirmationModalProps) => {
    const { t } = useAppTranslation();

    return (
        <Modal open={open} boxComponent={isError ? DivError : undefined}>
            <Box style={{ position: 'relative' }}>
                <BoxClose onClick={onClose}>
                    <BoxIconClose>
                        <CloseIcon fill={'var(--neutral5)'} size={IconSize.M} />
                    </BoxIconClose>
                </BoxClose>
            </Box>
            <BoxContent>
                <DivBodyCaption>{children}</DivBodyCaption>
                <BoxOptions>
                    <Button
                        color={Colors.Neutral3}
                        cmosVariant={'filled'}
                        onClick={onClose}
                        label={cancelCaptionButton ?? t('commonLabels.cancel')}
                    />
                    <Button
                        color={Colors.Success}
                        cmosVariant={'filled'}
                        onClick={onConfirmation}
                        label={confirmationCaptionButton ?? t('commonLabels.confirm')}
                    />
                </BoxOptions>
            </BoxContent>
        </Modal>
    );
};

export default ConfirmationModal;

const DivBodyCaption = styled('div')(({ theme }) => ({
    ...theme.typography.h4Roboto,
    color: theme.palette.neutral[8],
    textAlign: 'center',
}));

const BoxContent = styled(Box)({
    width: 464,
    marginTop: 40,
    marginRight: 22,
    marginLeft: 22,
    marginBottom: 30,
    display: 'flex',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignItems: 'center',
});

const BoxClose = styled(Box)({
    position: 'absolute',
    top: 0,
    right: 0,
});

const BoxIconClose = styled(Box)({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: 'none',
    cursor: 'pointer',
    marginRight: 20,
    marginTop: 20,
    backgroundColor: 'transparent',

    '&:focus': {
        outline: 'none',
    },
});

const BoxOptions = styled(Box)({
    marginTop: 30,
    display: 'flex',

    '&>button': {
        width: 200,
    },

    '&>button:first-child': {
        marginRight: 16,
    },
});

const DivError = styled('div')(({ theme }) => ({
    border: `1px solid ${theme.palette.error}`,
}));
