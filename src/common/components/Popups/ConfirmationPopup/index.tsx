import { IconButton, styled, useTheme } from '@mui/material';
import { BroadcastIcon } from 'common/components/Icons/BroadcastIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { WarningIcon } from 'common/components/Icons/WarningIcon';
import { Colors } from 'common/styles/Colors';
import { IconSize } from '../../../styles/IconSize';
import { Button } from '../../Button';
import { CloseIcon } from '../../Icons/CloseIcon';
import { Modal } from '../../Modal';

type ConfirmationPopupProps = {
    open: boolean;
    title: string;
    body: React.ReactNode;
    cancel?: string;
    onCancel?: () => void;
    onClose: () => void;
    confirm?: string;
    onConfirm?: () => void;
    color: string;
    icon?: React.ReactNode;
    showCloseBtn?: boolean;
    isConfirmDisabled?: boolean;
    classes?: { title?: string; content?: string; options?: string; body?: string; box?: string };
    showLoader?: boolean;
    displayDivider?: boolean;
    actionsContent?: React.ReactNode;
};

const ConfirmationPopup = ({
    open,
    title,
    body,
    cancel,
    onCancel,
    onClose,
    confirm,
    onConfirm,
    icon,
    color,
    showCloseBtn = true,
    isConfirmDisabled = false,
    classes = {
        title: undefined,
        content: undefined,
        options: undefined,
        body: undefined,
        box: undefined,
    },
    showLoader = false,
    displayDivider = false,
    actionsContent = false,
}: ConfirmationPopupProps) => {
    return (
        <StyledModal
            color={color}
            open={open}
            boxComponent={DivBoxComponent}
            classes={classes?.box ? { box: classes.box } : undefined}
        >
            {showCloseBtn && (
                <DivButtonContainer>
                    <DivClose>
                        <IconButtonClose onClick={onClose} size="large">
                            <CloseIcon fill={'var(--neutral6)'} size={IconSize.M} />
                        </IconButtonClose>
                    </DivClose>
                </DivButtonContainer>
            )}
            <DivContent className={classes.content}>
                {icon && <DivIcon>{icon}</DivIcon>}
                <DivTitle className={classes.title}>{title}</DivTitle>
                {displayDivider && <Divider />}
                <DivBody className={classes.body}>{body}</DivBody>
                {actionsContent ? (
                    actionsContent
                ) : (
                    <DivOptions className={classes.options}>
                        {cancel && (
                            <Button
                                disabled={showLoader}
                                color={Colors.Neutral3}
                                cmosVariant={'filled'}
                                onClick={onCancel ? onCancel : onClose}
                                label={cancel}
                            />
                        )}
                        <Button
                            showLoader={showLoader}
                            disabled={isConfirmDisabled || showLoader}
                            color={color}
                            cmosVariant={'filled'}
                            onClick={onConfirm}
                            label={confirm}
                        />
                    </DivOptions>
                )}
            </DivContent>
        </StyledModal>
    );
};

type SpecificConfirmationPopupProps = Omit<ConfirmationPopupProps, 'Icon' | 'color'>;

export function WarningConfirmationPopup(props: SpecificConfirmationPopupProps) {
    return (
        <ConfirmationPopup
            icon={<WarningIcon fill={'var(--yellow)'} size={IconSize.M} />}
            color={Colors.Warning}
            {...props}
        />
    );
}

export function BroadcastConfirmationPopup(props: SpecificConfirmationPopupProps) {
    const theme = useTheme();

    return (
        <ConfirmationPopup
            icon={<BroadcastIcon fill={theme.palette.warning.main} size={IconSize.M} />}
            color={theme.palette.warning.main}
            {...props}
        />
    );
}

export function DeleteConfirmationPopup(props: SpecificConfirmationPopupProps) {
    return (
        <ConfirmationPopup
            icon={<DeleteIcon fill={'var(--danger)'} size={IconSize.M} />}
            color={Colors.Error}
            {...props}
        />
    );
}

export function CommonConfirmationPopup(props: ConfirmationPopupProps) {
    return <ConfirmationPopup {...props} />;
}

const Divider = styled('div')({
    width: '80%',
    borderBottom: '1px solid #E5E7EA',
    margin: '0 auto',
    marginBottom: '8px',
});

const StyledModal = styled(Modal, {
    shouldForwardProp: (prop) => !['color'].includes(prop as string),
})<{ color: string }>(({ color }) => ({
    '--confirmation-popup-color': color,
}));

const DivBoxComponent = styled('div')({
    border: '1px solid var(--confirmation-popup-color)',
    maxWidth: '78%',
});

const DivContent = styled('div')({
    width: 464,
    minHeight: 150,
    maxWidth: '100%',
    padding: '30px',
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    alignItems: 'center',
});

const IconButtonClose = styled(IconButton)({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: 'none',
    cursor: 'pointer',
    marginRight: 15,
    marginTop: 15,
    backgroundColor: 'transparent',

    '&:focus': {
        outline: 'none',
    },
});

const DivClose = styled('div')({
    position: 'absolute',
    top: 0,
    right: 0,
});

const DivTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[8],
    marginBottom: 16,
    textAlign: 'center',
}));

const DivIcon = styled('div')({
    display: 'flex',
    marginBottom: 16,
    width: 50,
    height: 50,
    border: '1px solid var(--confirmation-popup-color)',
    borderRadius: '50%',
    alignItems: 'center',
    justifyContent: 'center',
});

const DivBody = styled('div')(({ theme }) => ({
    textAlign: 'center',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    fontWeight: 400,

    '& b': {
        fontWeight: 700,
    },
}));

const DivOptions = styled('div')({
    marginTop: 30,
    display: 'flex',

    '&>button': {
        width: 200,
    },

    '&>button:nth-child(2)': {
        marginLeft: 16,
    },
});

const DivButtonContainer = styled('div')({ position: 'relative' });
