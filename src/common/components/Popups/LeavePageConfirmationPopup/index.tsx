import { WarningConfirmationPopup } from '../ConfirmationPopup';
import { useAppTranslation } from '../../../hooks/useAppTranslation';
import styles from './style.module.css';

export interface LeavePageConfirmationPopupProps {
    open: boolean;

    onClose: () => void;
    onConfirm: () => void;

    title?: string;
    body?: string;
    cancel?: string;
    confirm?: string;
}

export default function LeavePageConfirmationPopup({
    open,
    onClose,
    onConfirm,
    title,
    body,
    cancel,
    confirm,
}: LeavePageConfirmationPopupProps) {
    const { t } = useAppTranslation();

    return (
        <WarningConfirmationPopup
            open={open}
            title={title ?? t('commonLabels.blockLeavingConfirmation.title')}
            body={body ?? t('commonLabels.blockLeavingConfirmation.body')}
            cancel={cancel ?? t('commonLabels.blockLeavingConfirmation.cancelButton')}
            confirm={confirm ?? t('commonLabels.blockLeavingConfirmation.continueButton')}
            onConfirm={onConfirm}
            onClose={onClose}
            showCloseBtn={true}
            classes={{ content: styles.content, options: styles.options }}
        />
    );
}
