import { styled } from '@mui/material';
import Box from '@mui/material/Box';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';

export type CancelModalProps = {
    title?: string;
    text?: string;
    cancelCaptionButton?: string;
    confirmationCaptionButton?: string;
    open?: boolean;
    onClose?: () => void;
    onCancel?: () => void;
};

// TODO (AP) Rework using ConfirmationPopup
const CancelModal = ({
    text,
    cancelCaptionButton,
    confirmationCaptionButton,
    open,
    onClose,
    onCancel,
    title,
}: CancelModalProps) => {
    const { t } = useAppTranslation();

    const closeHandler = () => {
        if (onClose) {
            onClose();
        }
    };

    const cancelChangeHandler = () => {
        if (onCancel) {
            onCancel();
        }
    };

    return (
        <Modal open={open}>
            <Box style={{ position: 'relative' }}>
                <BoxClose onClick={closeHandler}>
                    <BoxIconClose>
                        <CloseIcon fill={'var(--neutral5)'} size={IconSize.M} />
                    </BoxIconClose>
                </BoxClose>
            </Box>
            <BoxContent>
                <BoxTitle>{title || t('cancelModal.doYouWantToCancel')}</BoxTitle>
                {text && <SpanText>{text}</SpanText>}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, marginTop: 2 }}>
                    <Button
                        w="md"
                        color={Colors.Neutral3}
                        cmosVariant={'filled'}
                        onClick={closeHandler}
                        label={cancelCaptionButton || t('cancelModal.goBack')}
                    />
                    <Button
                        w="md"
                        color={Colors.Success}
                        cmosVariant={'filled'}
                        onClick={cancelChangeHandler}
                        label={confirmationCaptionButton || t('cancelModal.yesCancel')}
                    />
                </Box>
            </BoxContent>
        </Modal>
    );
};

const BoxContent = styled(Box)({
    width: 464,
    maxHeight: 204,
    marginTop: 40,
    marginRight: 22,
    marginLeft: 22,
    marginBottom: 30,
    display: 'flex',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    alignItems: 'center',
});

const BoxClose = styled(Box)({
    position: 'absolute',
    top: 0,
    right: 0,
});

const BoxIconClose = styled(Box)({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: 'none',
    cursor: 'pointer',
    marginRight: 20,
    marginTop: 20,
    backgroundColor: 'transparent',

    '&:focus': {
        outline: 'none',
    },
});

const BoxTitle = styled(Box)(({ theme }) => ({
    ...theme.typography.h4Roboto,
    color: theme.palette.neutral[8],
}));

const SpanText = styled('span')(({ theme }) => ({
    marginTop: 10,
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 400,
}));

export default CancelModal;
