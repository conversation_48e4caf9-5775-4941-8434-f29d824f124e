import styled from '@mui/material/styles/styled';
import { Button } from 'common/components/Button';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { ChangeEvent, useState } from 'react';

export interface FileUploadButtonProps {
    label: string;
    onFileSelected?: (file: File | null) => void;
    onFileDelete?: () => void;
    accept?: string[];
    maxSize?: number;
}

export default function FileUploadButton({
    label,
    onFileSelected,
    onFileDelete,
    accept,
    maxSize = 25 * 1024 * 1024,
}: FileUploadButtonProps) {
    const [file, setFile] = useState<File | null>(null);
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
        const selectedFile =
            event.target.files && event.target.files.length > 0 ? event.target.files[0] : null;
        if (selectedFile) {
            if (!isValidFile(selectedFile, maxSize, accept)) {
                toasters.danger('', t('toasters.fileNotSupported'));
                return;
            }
            setFile(selectedFile);
            if (onFileSelected) onFileSelected(selectedFile);
        }
    };

    const handleFileDelete = () => {
        setFile(null);
        if (onFileDelete) onFileDelete();
    };

    return file ? (
        <FileContainer>
            <FileName>{file.name}</FileName>
            <Button
                cmosVariant={'typography'}
                iconPosition="right"
                color="var(--cm1)"
                Icon={DeleteIcon}
                onClick={handleFileDelete}
            />
        </FileContainer>
    ) : (
        <>
            <input
                type="file"
                id="contained-button-file"
                style={{ display: 'none' }}
                onChange={handleFileChange}
                accept={accept !== undefined ? accept.map((e) => '.' + e).join(',') : undefined}
            />
            <label htmlFor="contained-button-file">
                <AttachFileButton>{label}</AttachFileButton>
            </label>
        </>
    );
}

const isValidFile = (file: File, maxSize: number, accept?: string[]): boolean => {
    if (file.size > maxSize) return false;
    if (!accept) return true;
    const parts = file.name.split('.');
    const ext = parts[parts.length - 1];
    return accept.includes(ext.toLowerCase());
};

const AttachFileButton = styled('div')(({ theme }) => ({
    height: 30,
    paddingLeft: 12,
    paddingRight: 12,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: theme.palette.neutral[5],
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.palette.neutral[1],
    borderRadius: 30,
    cursor: 'pointer',
    ...theme.typography.h6Roboto,
    color: 'var(--cm2)',
    '&:hover': {
        borderColor: 'var(--cm1)',
        backgroundColor: 'var(--cm5)',
    },
}));

const FileContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    maxWidth: 150,
    marginRight: 10,
    width: 150,
});

const FileName = styled('div')(({ theme }) => ({
    maxWidth: 126,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    ...theme.typography.h6Roboto,
    color: 'var(--cm2)',
}));
