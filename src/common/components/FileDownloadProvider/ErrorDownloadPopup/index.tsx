import { Box } from '@mui/material';
import { OrderedReportProperty } from 'api/Reports';
import { ENTERPRISE_ROUTES, REPORTS_TABS, ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment/moment';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import {
    selectFrom,
    selectPrimaryColumn,
    selectSelectedColumns,
    selectSelectedReport,
    selectTo,
} from 'store/slices/customReport/selectors';
import { finishDownload, setDownloadFailed } from 'store/slices/downloader';
import { selectDownloadFailed } from 'store/slices/downloader/selectors';
import { initiateReportDownload } from 'store/slices/downloader/thunks/downloadReport';
import { selectFilters } from 'store/slices/enterprise/reports';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { WarningConfirmationPopup } from '../../Popups/ConfirmationPopup';

export default function ErrorDownloadPopup() {
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const downloadFailed = useAppSelector(selectDownloadFailed);
    const selectedReport = useSelector(selectSelectedReport);
    const selectedColumns = useSelector(selectSelectedColumns);
    const primaryColumn = useSelector(selectPrimaryColumn);
    const to = useSelector(selectTo);
    const from = useSelector(selectFrom);
    const appMode = useAppSelector(selectSettings).appMode;
    const { locations: selectedLocations } = useAppSelector(selectFilters);

    function handleReportDownload() {
        const requestData = {
            primaryPropertyId: primaryColumn?.id!,
            properties: selectedColumns.map(
                (c) =>
                    ({
                        id: c.id,
                        order: c.order,
                    } as OrderedReportProperty)
            ),
            reportName: selectedReport?.name,
            dateFrom: moment(from).format('YYYY-MM-DDTHH:mm:ss.SSS'),
            dateTo: moment(to).format('YYYY-MM-DDTHH:mm:ss.SSS'),
        };

        const isEnterprise = appMode === 'Enterprise';
        const shopIds = selectedLocations || [];

        dispatch(
            initiateReportDownload({
                requestData: requestData,
                isEnterprise: isEnterprise,
                fileName: selectedReport?.name!,
                shopIds: shopIds,
                isDebug: false,
                prodShopKey: '',
            })
        );
        dispatch(setDownloadFailed(false));
    }

    const customReportUrl = `${
        appMode === 'RepairShop' ? ROUTES.REPORTS.ROOT : ENTERPRISE_ROUTES.REPORTS.ROOT
    }/${REPORTS_TABS.CUSTOM_REPORTS}`;

    return (
        <WarningConfirmationPopup
            open={downloadFailed}
            title={t('reports.downloadError')}
            body={
                <>
                    <Box>{t('reports.errorOccurred')}</Box>
                    <Box>{t('reports.downloadingAgain')}</Box>
                </>
            }
            cancel={t('reports.cancel')}
            confirm={t('reports.tryAgain')}
            onConfirm={handleReportDownload}
            onClose={() => {
                dispatch(finishDownload());
            }}
            onCancel={() => {
                dispatch(finishDownload());
                navigate(customReportUrl);
            }}
        />
    );
}
