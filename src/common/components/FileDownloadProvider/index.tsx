import { DragIndicator } from '@mui/icons-material';
import { LinearProgress, linearProgressClasses, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import moment from 'moment';
import { useEffect, useState } from 'react';
import Draggable, { DraggableData, DraggableEvent } from 'react-draggable';
import { useAppDispatch, useAppSelector } from 'store';
import { cancelDownload, finishDownload } from 'store/slices/downloader';
import {
    selectDateFrom,
    selectDownloadLink,
    selectFileName,
    selectIsDownloading,
    selectLastIncludedDate,
} from 'store/slices/downloader/selectors';
import { CloseIcon } from '../Icons/CloseIcon';
import { DownloadIcon } from '../Icons/DownloadIcon';
import ErrorDownloadPopup from './ErrorDownloadPopup';

type Position = {
    x: number;
    y: number;
};

export default function FileDownloadProvider() {
    const isDownloading = useAppSelector(selectIsDownloading);
    const fileName = useAppSelector(selectFileName);
    const downloadLink = useAppSelector(selectDownloadLink);
    const lastIncludedDate = useAppSelector(selectLastIncludedDate);
    const dateFrom = useAppSelector(selectDateFrom);
    const toasters = useToasters();
    const dispatch = useAppDispatch();

    const { t } = useAppTranslation();

    const [position, setPosition] = useState<Position>({ x: 0, y: 0 });

    function decodeSlashes(text: string) {
        return text.replace(/&#x2F;/g, '/');
    }

    useEffect(() => {
        if (!lastIncludedDate || !dateFrom) return;
        const formattedDateFrom = moment(dateFrom, 'YYYY-MM-DD');
        const formattedLastIncludedDate = moment(lastIncludedDate, 'YYYY-MM-DD');
        if (formattedDateFrom.isBefore(formattedLastIncludedDate)) {
            const formattedDateFromString = formattedDateFrom.format('DD/MM/YY');
            const formattedLastIncludedDateString = formattedLastIncludedDate.format('DD/MM/YY');

            const warningMessage = t('reports.MoreOneMillionRowsError', {
                formattedDateFromString: formattedDateFromString,
                formattedLastIncludedDateString: formattedLastIncludedDateString,
            });
            const decodedMessage = decodeSlashes(warningMessage);

            toasters.warning(decodedMessage, t('reports.RowLimitExceeded'));
        }
    }, [dateFrom, lastIncludedDate, t]);

    useEffect(() => {
        if (downloadLink && downloadLink.trim() !== '') {
            const link = document.createElement('a');
            link.href = downloadLink;
            document.body.appendChild(link);
            dispatch(finishDownload());
            link.click();
            document.body.removeChild(link);
            toasters.success(fileName ?? '', t('reports.DownloadSuccess'));
        }
    }, [downloadLink]);

    function onDownloadCanceled() {
        toasters.danger('', t('reports.DownloadCancel'));
        dispatch(cancelDownload());
    }

    function onDragStop(e: DraggableEvent, data: DraggableData) {
        const { x, y } = data;
        setPosition({ x, y });
    }

    return isDownloading ? (
        <Draggable
            position={position}
            enableUserSelectHack={false}
            onStop={onDragStop}
            handle={'#modalDragHandler'}
        >
            <DivRoot style={{ zIndex: 9999 }}>
                <DivSide />
                <DivContainer>
                    <DivHeader>
                        <DivDndIconContainer id={'modalDragHandler'}>
                            <DragIndicator
                                style={{
                                    width: 16,
                                    height: 16,
                                    color: 'gray',
                                    cursor: 'grab',
                                }}
                            />
                        </DivDndIconContainer>
                        <DivCloseIconContainer>
                            <DivCloseIcon onClick={onDownloadCanceled}>
                                <CloseIcon size={20} fill={'#5C6477'} />
                            </DivCloseIcon>
                        </DivCloseIconContainer>
                    </DivHeader>
                    <DivContentContainer>
                        <DivMainIconContainer>
                            <DivMainIconBackground>
                                <DownloadIcon
                                    fill={'var(--neutral1)'}
                                    style={{ width: 24, height: 24 }}
                                />
                            </DivMainIconBackground>
                        </DivMainIconContainer>
                        <DivContent>
                            <DivTitle>{t('reports.DownloadingInProgress')}</DivTitle>
                            <DivText>{t('reports.YouCanContinueUsingOtherScreens')}</DivText>
                            <DivDownloadProgressBar>
                                <LinearProgress
                                    variant="indeterminate"
                                    sx={{
                                        backgroundColor: '#F6F6F6',
                                        [`& .${linearProgressClasses.bar}`]: {
                                            backgroundColor: '#36ce91',
                                        },
                                    }}
                                />
                            </DivDownloadProgressBar>
                        </DivContent>
                    </DivContentContainer>
                </DivContainer>
            </DivRoot>
        </Draggable>
    ) : (
        <ErrorDownloadPopup />
    );
}

const DivRoot = styled('div')(() => ({
    display: 'flex',
    zIndex: 3000,
    position: 'absolute',
    left: 20,
    bottom: 14,
    width: 282,
    height: 117,
    borderRadius: 10,
    boxShadow: '0px 4px 4px rgba(0,0,0,0.1)',
}));

const DivSide = styled('div')(() => ({
    alignSelf: 'flex-start',
    height: '100%',
    width: 5,
    backgroundColor: 'var(--cm1)',
    borderBottomLeftRadius: 10,
    borderTopLeftRadius: 10,
}));

const DivContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    width: 277,
});

const DivHeader = styled('div')(() => ({
    alignSelf: 'flex-start',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 17,
    width: '100%',
    backgroundColor: 'var(--neutral1) !important',
    borderTopRightRadius: 10,
}));

const DivDndIconContainer = styled('div')({
    marginLeft: 3,
});

const DivCloseIconContainer = styled('div')({
    marginRight: 8,
});

const DivCloseIcon = styled('div')({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
});

const DivContentContainer = styled('div')(() => ({
    display: 'flex',
    width: 277,
    height: 100,
    backgroundColor: 'var(--neutral1) !important',
    borderBottomRightRadius: 10,
}));

const DivMainIconContainer = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 14,
    paddingRight: 14,
    height: '100%',
});

const DivMainIconBackground = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: 28,
    height: 28,
    borderRadius: 14,
    marginBottom: 21,
    backgroundColor: 'var(--cm1)',
}));

const DivContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    width: 221,
    paddingBottom: 12,
    paddingRight: 16,
});

const DivTitle = styled('div')(() => ({
    marginBottom: 4,
    fontFamily: 'Roboto',
    fontSize: 12,
    fontWeight: 700,
}));

const DivText = styled('div')(() => ({
    marginBottom: 13,
    fontFamily: 'Roboto',
    fontSize: 12,
    fontWeight: 400,
}));

const DivDownloadProgressBar = styled('div')(() => ({
    display: 'block',
    width: 200,
    height: 6,
    borderRadius: 10,
}));
