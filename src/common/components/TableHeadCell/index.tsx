import TableCellUI, { TableCellProps } from '@mui/material/TableCell';
import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { memo } from 'react';
import { Colors } from '../../styles/Colors';

const useStyles = makeStyles((theme) => ({
    cell: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        padding: '16px 10px',
        backgroundColor: Colors.Neutral2,
        textTransform: 'uppercase',
        boxSizing: 'border-box',

        // eslint-disable-next-line no-useless-computed-key
        ['@media (min-width: 1920px)']: {
            paddingLeft: 16,
        },
        '& .MuiTableSortLabel-root.MuiTableSortLabel-active': {
            ...theme.typography.h6Inter,
            color: theme.palette.neutral[7],
        },
    },
}));

const TableHeadCell = memo(({ className, ...props }: TableCellProps) => {
    const style = useStyles();

    return <TableCellUI {...props} className={clsx(style.cell, className)} component="th" />;
});
TableHeadCell.displayName = 'TableHeadCell';

export default TableHeadCell;
