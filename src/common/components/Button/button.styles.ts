import { makeStyles } from '@mui/styles';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from '../../styles/Colors';
import { CmosButtonVariant } from './ButtonProps';

export type ButtonColors =
    | Colors.Success
    | Colors.Error
    | Colors.Warning
    | Colors.GrayBlue
    | Colors.Neutral3
    | Colors.CM1;

export const getTextColor = (theme: CmosButtonVariant, color: string) => {
    if (color === Colors.Neutral3) return Colors.Neutral6;
    if (theme === 'filled') return Colors.White;
    else return color;
};

const ButtonColorTheme = (theme: CmosButtonVariant, normal: ButtonColors, dark: Colors): any => {
    const light = normal === Colors.Neutral3 ? normal : rgba(normal, 0.2);

    return {
        color: getTextColor(theme, normal),
        backgroundColor: theme === 'filled' ? normal : Colors.Transparent,
        border:
            theme === 'stroke'
                ? `1px solid ${normal === Colors.Neutral3 ? dark : normal}`
                : `1px solid ${Colors.Transparent}`,
        '&:hover': {
            backgroundColor:
                theme === 'filled' ? dark : theme === 'stroke' ? light : Colors.Transparent,
        },
        '&:active': {
            backgroundColor:
                theme === 'filled' ? dark : theme === 'stroke' ? light : Colors.Transparent,
            border: theme !== 'typography' ? `solid 1px ${normal}` : undefined,
        },
    };
};

// NOTE (AP) That's done for optimizations purposes. Don't try rework that using function-based-styles
export const useButtonColorStyles = makeStyles((theme) => ({
    strokeSuccess: {
        ...ButtonColorTheme('stroke', Colors.Success, Colors.Success_dark),
    },
    strokeError: {
        ...ButtonColorTheme('stroke', Colors.Error, Colors.Error_dark),
    },
    strokeWarning: {
        ...ButtonColorTheme('stroke', Colors.Warning, Colors.Warning_dark),
    },
    strokeNeutral3: {
        ...ButtonColorTheme('stroke', Colors.Neutral3, Colors.Neutral5),
    },
    strokeCM1: {
        ...ButtonColorTheme('stroke', Colors.CM1, Colors.CM1_dark),
    },
    filledSuccess: {
        ...ButtonColorTheme('filled', Colors.Success, Colors.Success_dark),
    },
    filledError: {
        ...ButtonColorTheme('filled', Colors.Error, Colors.Error_dark),
    },
    filledWarning: {
        ...ButtonColorTheme('filled', Colors.Warning, Colors.Warning_dark),
    },
    filledNeutral3: {
        ...ButtonColorTheme('filled', Colors.Neutral3, Colors.Neutral4),
    },
    filledCM1: {
        ...ButtonColorTheme('filled', Colors.CM1, Colors.CM1_dark),
    },
    typography: {
        backgroundColor: 'transparent',
        '--fg': 'var(--color)',
        color: 'var(--fg)',
        '&:hover': {
            '--fg': 'color-mix(in srgb, var(--color), #000 20%)', // if it works in your browser - great, if it doesn't - no big deal, nobody is going to notice anyway
        },
    },
}));
