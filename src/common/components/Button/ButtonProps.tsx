import { ButtonProps as MuiButtonProps } from '@mui/material';
import { CSSProperties, ComponentType, MouseEventHandler } from 'react';
import { IconProps } from '../Icons/Icon';

type CmosButtonSize = 'small' | 'medium' | 'large';

export type CmosButtonVariant = 'stroke' | 'filled' | 'typography';

export type CmosButtonWidth = 'md' | 'sm' | 'lg';

export type ButtonProps = {
    label?: string;
    cmosVariant?: CmosButtonVariant;
    color?: 'warning' | 'neutral' | 'success' | 'primary' | string;
    cmosSize?: CmosButtonSize;
    blockMode?: boolean;
    disabled?: boolean;
    customStyles?: CSSProperties;
    type?: string;
    Icon?: ComponentType<IconProps>;
    iconPosition?: 'left' | 'right';
    showLoader?: boolean;
    onClick?: MouseEventHandler;
    w?: CmosButtonWidth | number;
    href?: string;
    buttonInnercustomStyles?: CSSProperties;
    children?: React.ReactNode;
    sx?: MuiButtonProps['sx'];
    disableRipple?: boolean;
};
