import { Button as Mui<PERSON>utton, styled } from '@mui/material';
import clsx from 'clsx';
import { SingleLineText } from 'common/styles/TextHelpers';
import React, { memo, useCallback } from 'react';
import { useHref, useNavigate } from 'react-router-dom';
import { splitDataProps } from 'utils/dataProps';
import { Colors } from '../../styles/Colors';
import { IconSize } from '../../styles/IconSize';
import { ButtonProps, CmosButtonWidth } from './ButtonProps';
import { useButtonColorStyles } from './button.styles';
import styles from './styles.module.css';

const BUTTON_WIDTHS: Record<CmosButtonWidth, number> = {
    md: 160,
    sm: 120,
    lg: 250,
};

const NoncachedButton = React.forwardRef(
    (
        {
            Icon,
            className,
            buttonInnercustomStyles = undefined,
            iconPosition = 'left',
            label,
            cmosVariant = 'filled',
            cmosSize = 'medium',
            blockMode = false,
            color = Colors.CM1,
            disabled,
            customStyles,
            type,
            showLoader,
            onClick,
            href,
            w,
            children,
            sx,
            disableRipple,
            ...props
        }: ButtonProps & { className?: string },
        ref: React.Ref<HTMLButtonElement>
    ) => {
        const colorStyles = useButtonColorStyles();
        // const textColor = getTextColor(theme, color);
        const isButtonIcon = (!label || label === '') && Icon;

        const sizeClass = (() => {
            switch (cmosSize) {
                case 'small':
                    return styles.small;
                case 'medium':
                    return styles.medium;
                default:
                    return styles.large;
            }
        })();

        const colorClass = (() => {
            switch (cmosVariant) {
                case 'filled':
                    switch (color) {
                        case Colors.CM1:
                        case 'primary':
                            return colorStyles.filledCM1;
                        case Colors.Success:
                        case 'success':
                            return colorStyles.filledSuccess;
                        case Colors.Error:
                        case 'error':
                            return colorStyles.filledError;
                        case Colors.Warning:
                        case 'warning':
                            return colorStyles.filledWarning;
                        case 'neutral':
                        default:
                            return colorStyles.filledNeutral3;
                    }
                case 'stroke':
                    switch (color) {
                        case Colors.CM1:
                            return colorStyles.strokeCM1;
                        case Colors.Success:
                            return colorStyles.strokeSuccess;
                        case Colors.Error:
                            return colorStyles.strokeError;
                        case Colors.Warning:
                            return colorStyles.strokeWarning;
                        default:
                            return colorStyles.strokeNeutral3;
                    }
                default:
                    return colorStyles.typography;
            }
        })();

        const navigate = useNavigate();
        const fullHref = useHref(href ?? '');
        const handlerClick = useCallback(
            (event: React.MouseEvent<HTMLButtonElement>) => {
                if (!disabled && type !== 'submit') {
                    event.preventDefault();
                    if (onClick) onClick(event);
                    if (href) {
                        navigate(href);
                    }
                }
            },
            [onClick, disabled, type, href, navigate]
        );

        return (
            <StyledButton
                sx={sx}
                href={href ? fullHref : undefined}
                ref={ref}
                style={
                    {
                        ...customStyles,
                        '--color': color,
                    } as React.CSSProperties
                }
                onClick={handlerClick}
                width={typeof w === 'string' ? BUTTON_WIDTHS[w] : w}
                isButtonIcon={!!isButtonIcon}
                isButtonBlock={blockMode && !!label}
                classes={{
                    root: clsx(className, sizeClass, colorClass, disabled && styles.disabled),
                }}
                disableRipple={disableRipple}
                {...splitDataProps(props)[1]}
            >
                <InnerCustom style={{ ...buttonInnercustomStyles }}>
                    {showLoader && <Loader />}
                    {iconPosition === 'left' && Icon && (
                        <Icon fill="currentColor" size={IconSize.M} />
                    )}
                    {children
                        ? children
                        : label && (
                              <Label
                                  needMarginLeft={iconPosition === 'left' && !!Icon && !!label}
                                  needMarginRight={iconPosition === 'right' && !!Icon && !!label}
                              >
                                  {label}
                              </Label>
                          )}
                    {iconPosition === 'right' && Icon && (
                        <Icon fill="currentColor" size={IconSize.M} />
                    )}
                </InnerCustom>
            </StyledButton>
        );
    }
);

const StyledButton = styled(MuiButton, {
    shouldForwardProp: (prop) =>
        !['width', 'isButtonIcon', 'isButtonBlock'].includes(prop as string),
})<{ width?: number; isButtonIcon: boolean; isButtonBlock: boolean }>(
    ({ width, isButtonIcon, isButtonBlock, theme }) => ({
        ...(width || isButtonBlock ? { width: width ? width : '100%' } : undefined),
        cursor: 'pointer',
        borderRadius: isButtonIcon ? '51%' : 51,
        minWidth: 24,
        textTransform: 'initial',
        color: theme.palette.neutral[1],
        ...SingleLineText(),
        ...theme.typography.h6Inter,

        '&svg': {
            width: IconSize.M,
        },

        '&:focus': {
            outline: 'none',
        },

        padding: '0.25em !important',
    })
);

const InnerCustom = styled('div')({
    display: 'flex',
    alignItems: 'center',
    lineHeight: '100%',
});

const Loader = styled('span')({
    'margin-right': '8px',
    width: '16px',
    height: '16px',
    border: '2px solid #F6F6F6',
    'border-bottom-color': '#7175FA',
    'border-radius': '50%',
    display: 'inline-block',
    'box-sizing': 'border-box',
    animation: 'rotation 1s linear infinite',

    '@keyframes rotation': {
        '0%': {
            transform: 'rotate(0deg)',
        },
        '100%': {
            transform: 'rotate(360deg)',
        },
    },
});

const Label = styled('div', {
    shouldForwardProp: (prop) => !['needMarginLeft', 'needMarginRight'].includes(prop as string),
})<{ needMarginLeft: boolean; needMarginRight: boolean }>(
    ({ needMarginLeft, needMarginRight }) => ({
        marginLeft: needMarginLeft ? 3 : 0,
        marginRight: needMarginRight ? 3 : 0,
        cursor: 'pointer',
    })
);

export const CachedButton = memo(NoncachedButton);

export { CachedButton as Button };
