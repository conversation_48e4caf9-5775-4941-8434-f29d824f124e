import { Button, IconButton, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useRef, useState } from 'react';
import { useInputValue } from '../../hooks/useInputValue';
import { Colors } from '../../styles/Colors';
import { FilterOption, FilterOptionId } from '../../types';
import { FiltersIcon } from '../Icons/FiltersIcon';
import Checkbox from '../Inputs/Checkbox';
import {
    DivMenuItem,
    DivMenuItemContainer,
    GridMenuSubheader,
    SpanMenuItemLabel,
    StyledMenu,
    StyledMenuItem,
} from '../Inputs/Menu/common';
import TextFormField from '../Inputs/TextField';

type ITableHeadAutocompleteFilterProps<T> = {
    mainLabel: string;
    options: FilterOption<T>[];
    selected: FilterOptionId<T>[];
    onSelectedChanged: (selected: FilterOptionId<T>[]) => void;
};

export const TableHeadAutocompleteFilter = <T extends string | number>({
    mainLabel,
    options,
    selected,
    onSelectedChanged,
}: ITableHeadAutocompleteFilterProps<T>) => {
    const menuButtonRef = useRef(null);
    const { t } = useAppTranslation();

    const [input, setInput] = useInputValue<string>('');
    const [showMenu, setShowMenu] = useState<boolean>(false);
    const [selectedOptions, setSelectedOptions] = useState<FilterOptionId<T>[]>(selected);

    const displayedOptions = useMemo(
        () =>
            options
                .filter((o) => o.label.toLowerCase().startsWith(input.toLowerCase()))
                .sort((l, r) => {
                    return l.label.localeCompare(r.label);
                }),
        [options, input]
    );

    const handleOptionClicked = (optionId: FilterOptionId<T>) => {
        if (!selectedOptions.some((s) => s === optionId)) {
            const result = [...selectedOptions, optionId];
            setSelectedOptions(result);
        } else {
            const result = selectedOptions.filter((o) => o !== optionId);
            setSelectedOptions(result);
        }
    };

    const handleApplyClicked = () => {
        onSelectedChanged(selectedOptions);
        setShowMenu(false);
    };

    return (
        <>
            <IconButton
                ref={menuButtonRef}
                onClick={() => setShowMenu(true)}
                aria-controls="simple-menu"
                aria-haspopup="true"
                size="small"
            >
                <FiltersIcon fill={Colors.Neutral7} />
            </IconButton>
            <StyledMenu
                anchorEl={menuButtonRef.current}
                keepMounted
                open={showMenu}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                onClose={() => setShowMenu(false)}
                MenuListProps={{
                    disablePadding: true,
                    subheader: (
                        <GridMenuSubheader container justifyContent="space-between">
                            <span>{mainLabel}</span>
                            <Button
                                sx={{
                                    textTransform: 'none',
                                    padding: 0,
                                    fontSize: '1em',
                                    minWidth: 50,
                                }}
                                variant="text"
                                onClick={handleApplyClicked}
                            >
                                {t('search.apply')}
                            </Button>
                        </GridMenuSubheader>
                    ),
                }}
            >
                <DivMenuItemsSearch>
                    <TextFormField
                        name={'search'}
                        placeholder={t('search.startTyping')}
                        type={'text'}
                        value={input}
                        onChange={setInput}
                        cmosVariant="roundedPrimary"
                    />
                </DivMenuItemsSearch>
                <DivMenuItemContainer
                    sx={{
                        padding: '10px 0',
                        maxHeight: 400,
                        overflowY: 'auto',
                    }}
                >
                    {displayedOptions.length ? (
                        displayedOptions.map((option) => {
                            const isSelected = selectedOptions.some((s) => s === option.id);
                            return (
                                <StyledMenuItem
                                    key={option.id}
                                    onClick={() => handleOptionClicked(option.id)}
                                    disableRipple
                                >
                                    <DivMenuItemContent>
                                        <StyledCheckbox
                                            onChange={() => {}}
                                            checked={isSelected}
                                            disabled
                                        />

                                        <SpanMenuItemLabel isSelected={isSelected}>
                                            {option.label}
                                        </SpanMenuItemLabel>
                                    </DivMenuItemContent>
                                </StyledMenuItem>
                            );
                        })
                    ) : (
                        <DivNoMenuItems>{t('search.nothingFound')}</DivNoMenuItems>
                    )}
                </DivMenuItemContainer>
            </StyledMenu>
        </>
    );
};

const DivMenuItemsSearch = styled('div')({
    padding: '10px 14px 6px 14px',
});

const DivMenuItemContent = styled(DivMenuItem)({
    display: 'flex',
    alignItems: 'center',
    gap: 10,
});

const StyledCheckbox = styled(Checkbox)({
    padding: 0,
});

const DivNoMenuItems = styled('div')(({ theme }) => ({
    paddingLeft: 14,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    fontWeight: 400,
}));

export default TableHeadAutocompleteFilter;
