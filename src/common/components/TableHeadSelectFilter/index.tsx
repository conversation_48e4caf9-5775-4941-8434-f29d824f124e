import { Button, IconButton, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useRef, useState } from 'react';
import { Colors } from '../../styles/Colors';
import { FilterOption, FilterOptionId } from '../../types';
import { FiltersIcon } from '../Icons/FiltersIcon';
import Checkbox from '../Inputs/Checkbox';
import {
    DivMenuItem,
    DivMenuItemContainer,
    GridMenuSubheader,
    SpanMenuItemLabel,
    StyledMenu,
    StyledMenuItem,
} from '../Inputs/Menu/common';

type ITableHeadSelectFilterProps<T extends string> = {
    mainLabel: string;
    options: FilterOption<T>[];
    selected: FilterOptionId<T>[];
    hideApplyButton?: boolean;
    onSelectedChanged: (selected: FilterOptionId<T>[]) => void;
};

export const TableHeadSelectFilter = <T extends string>({
    mainLabel,
    options,
    selected,
    hideApplyButton,
    onSelectedChanged,
}: ITableHeadSelectFilterProps<T>) => {
    const menuButtonRef = useRef(null);
    const { t } = useAppTranslation();

    const [showMenu, setShowMenu] = useState<boolean>(false);
    const [selectedOptions, setSelectedOptions] = useState<FilterOptionId<T>[]>(selected);

    const handleOptionClicked = (optionId: FilterOptionId<T>) => {
        if (!selectedOptions.some((s) => s === optionId)) {
            const result = [...selectedOptions, optionId];
            if (hideApplyButton) {
                onSelectedChanged(result);
            }

            setSelectedOptions(result);
        } else {
            const result = selectedOptions.filter((o) => o !== optionId);
            if (hideApplyButton) {
                onSelectedChanged(result);
            }

            setSelectedOptions(result);
        }
    };

    const handleApplyClicked = () => {
        onSelectedChanged(selectedOptions);
        setShowMenu(false);
    };

    return (
        <>
            <IconButton
                ref={menuButtonRef}
                onClick={() => setShowMenu(true)}
                aria-controls="simple-menu"
                aria-haspopup="true"
                size="small"
            >
                <FiltersIcon fill={Colors.Neutral7} />
            </IconButton>
            <StyledMenu
                slotProps={{ paper: { sx: { overflowY: 'hidden' } } }}
                anchorEl={menuButtonRef.current}
                keepMounted
                open={showMenu}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                onClose={() => setShowMenu(false)}
                MenuListProps={{
                    disablePadding: true,
                    subheader: (
                        <GridMenuSubheader
                            container
                            justifyContent="space-between"
                            alignItems="center"
                        >
                            <span>{mainLabel}</span>
                            {!hideApplyButton ? (
                                <Button
                                    sx={{
                                        textTransform: 'none',
                                        padding: 0,
                                        fontSize: '1em',
                                        minWidth: 50,
                                    }}
                                    variant="text"
                                    onClick={handleApplyClicked}
                                >
                                    {t('search.apply')}
                                </Button>
                            ) : null}
                        </GridMenuSubheader>
                    ),
                }}
            >
                <DivMenuItemContainer
                    sx={{
                        maxHeight: 340,
                        overflowY: 'auto',
                        ...scrollbarStyle(),
                    }}
                >
                    {options.map((option) => {
                        const isSelected = selectedOptions.some((s) => s === option.id);
                        return (
                            <StyledMenuItem
                                key={option.id}
                                onClick={() => handleOptionClicked(option.id)}
                                disableRipple
                            >
                                <DivMenuItem
                                    sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '10px',
                                    }}
                                >
                                    <StyledCheckbox
                                        onChange={() => {}}
                                        checked={isSelected}
                                        disabled
                                    />

                                    <SpanMenuItemLabel isSelected={isSelected}>
                                        {option.label}
                                    </SpanMenuItemLabel>
                                </DivMenuItem>
                            </StyledMenuItem>
                        );
                    })}
                </DivMenuItemContainer>
            </StyledMenu>
        </>
    );
};

const StyledCheckbox = styled(Checkbox)({
    padding: 0,
});

export default TableHeadSelectFilter;
