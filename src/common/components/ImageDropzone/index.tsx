import React, { useRef } from 'react';

import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ImageIcon } from '../Icons/ImageIcon';

export type ImageDropzoneProps = {
    imageUrl: string | undefined | null;
    emptyFileComponent: React.ReactNode;
    onChange: (file: File) => void;
    validationFunction?: (file: File) => boolean;
    dropzoneClass?: string;
    disableChange?: boolean;
    imageOverlay?: true | React.ReactNode;
};

const ImageDropzone = ({
    imageUrl,
    emptyFileComponent,
    onChange,
    validationFunction,
    dropzoneClass,
    disableChange,
    imageOverlay,
}: ImageDropzoneProps) => {
    const imageRef = useRef<HTMLInputElement>(null);

    const showOpenFileDialog = () => {
        if (disableChange) return;
        if (imageRef && imageRef.current) {
            imageRef.current.click();
        }
    };

    const dragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    const dragEnter = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    const dragLeave = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    const handleFileDrop = async (e: React.DragEvent<HTMLDivElement>) => {
        e.stopPropagation();
        e.preventDefault();
        if (disableChange) return;

        if (imageRef.current) {
            const files = e.dataTransfer.files;
            imageRef.current.files = files;
            handleFileSelected(files);
        }
    };

    const handleChangeFile = (e: React.FormEvent<HTMLInputElement>) => {
        e.stopPropagation();
        e.preventDefault();
        if (disableChange) return;

        const files = imageRef.current?.files;
        handleFileSelected(files);
    };

    const handleFileSelected = (files: FileList | null | undefined) => {
        if (files && files.length > 0) {
            const file = files[0];
            if (typeof validationFunction === 'function' && validationFunction) {
                const validated = validationFunction(file);
                if (!validated) return;
            }
            if (onChange) onChange(file);
        }
    };

    return (
        <div>
            <StyledInput
                ref={imageRef!}
                type="file"
                accept="image/jpeg, image/jpg, image/png"
                onChange={handleChangeFile}
            />
            <DivBackgroundPicture
                className={dropzoneClass}
                onClick={showOpenFileDialog}
                style={{ backgroundImage: imageUrl ? `url(${imageUrl})` : undefined }}
                onDragOver={dragOver}
                onDragEnter={dragEnter}
                onDragLeave={dragLeave}
                onDrop={handleFileDrop}
            >
                {!imageUrl && emptyFileComponent}
                {imageUrl && imageOverlay && (
                    <DivOverlay>
                        {imageOverlay === true ? <DefaultOverlay /> : imageOverlay}
                    </DivOverlay>
                )}
            </DivBackgroundPicture>
        </div>
    );
};

function DefaultOverlay() {
    const { t } = useAppTranslation();

    return (
        <>
            <ImageIcon fill="currentColor" />
            {t('commonLabels.upload.changeImage')}
        </>
    );
}

const StyledInput = styled('input')({
    display: 'none',
});

const DivBackgroundPicture = styled('div')({
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    position: 'relative',
    overflow: 'hidden',
    cursor: 'pointer',

    '&:hover > $overlay': {
        opacity: 1,
    },
});

const DivOverlay = styled('div')(({ theme }) => ({
    display: 'flex',
    opacity: 0,
    transition: '.2s opacity',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    inset: 0,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    userSelect: 'none',
    ...theme.typography.h6Inter,
}));

export default ImageDropzone;
