import { Paper, PaperProps, styled, useForkRef } from '@mui/material';
import { useOverlayScrollbars } from 'overlayscrollbars-react';
import React, { forwardRef, useCallback, useEffect } from 'react';

const PaperWithOverlayScrollbars = forwardRef(
    (props: PaperProps, ref: React.ForwardedRef<HTMLDivElement>) => {
        const [init, instance] = useOverlayScrollbars();
        const initWrapper = useCallback(
            (el: HTMLElement | null) => {
                instance()?.destroy();
                if (el) init(el);
            },
            [instance, init]
        );
        const innerRef = useForkRef(ref, initWrapper);

        useEffect(
            () => () => {
                instance()?.destroy();
            },
            [instance]
        );

        return <StyledPaper ref={innerRef} {...props} />;
    }
);

const StyledPaper = styled(Paper)({
    boxShadow: 'none',
    borderRadius: 10,
    border: '1px solid var(--neutral4)',

    thead: {
        tr: {
            'th:first-child': {
                borderLeft: 'none',
            },
        },

        'tr:first-child': {
            th: {
                borderTop: 'none',
            },

            'th:first-child': {
                'border-top-left-radius': '10px',
            },

            'th:last-child': {
                'border-top-right-radius': '10px',
            },
        },
    },

    tbody: {
        tr: {
            'td:first-child': {
                borderLeft: 'none',
            },
        },

        'tr:last-child': {
            td: {
                borderBottom: 'none',
            },

            'td:first-child': {
                'border-bottom-left-radius': '10px',
            },

            'td:last-child': {
                'border-bottom-right-radius': '10px',
            },
        },
    },
});

export default PaperWithOverlayScrollbars;
