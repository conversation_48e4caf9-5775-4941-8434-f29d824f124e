import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { Colors } from '../../styles/Colors';
import { UrlIcon } from '../Icons/UrlIcon';
import { TextArea } from '../Inputs';
import Dropdown from '../Inputs/Dropdown';
import TextFormField from '../Inputs/TextField';
import styles from './style.module.css';

const SendSmsModalBody = ({
    title = 'communication.sendSmsWithNote',
    desc = 'communication.sendSmsWithNoteDesc',
    onClose,
}: any) => {
    const [number] = useState(null);
    const [message] = useState('');
    const [dropdownSelected] = useState(null);
    const { t } = useAppTranslation();

    const dropdownOptions = [
        { label: 'Atajo 1', value: 'atajo1' },
        { label: 'Atajo 2', value: 'atajo2' },
        { label: 'Atajo 3', value: 'atajo3' },
        { label: 'Atajo 4', value: 'atajo4' },
    ];
    return (
        <Grid
            container
            spacing={0}
            direction="column"
            alignItems="center"
            justifyContent="center"
            style={{ minHeight: '100vh' }}
        >
            <Grid item xs={8}>
                <Card className={styles.card}>
                    <Grid container spacing={1}>
                        <Grid item xs={12}>
                            <div className={styles.modalTitle}>
                                <span>{t(title)}</span>
                            </div>
                        </Grid>
                        <Grid item xs={12}>
                            <div className={styles.modalDesc}>
                                <span>{t(desc)}</span>
                            </div>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={2}>
                                    <span className={styles.label}>{t('commonLabels.number')}</span>
                                </Grid>
                                <Grid item xs={4}>
                                    <TextFormField
                                        placeholder={t('commonLabels.number')}
                                        type={'text'}
                                        onChange={() => {}}
                                        name={'number'}
                                        value={number}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={2}>
                                    <span className={styles.label}>
                                        {t('commonLabels.message')}
                                    </span>
                                </Grid>
                                <Grid item xs={8}>
                                    <Grid container spacing={1} alignItems="center">
                                        <Grid item xs={3}>
                                            <Button
                                                variant="outlined"
                                                className={styles.attachFileBtn}
                                            >
                                                <span>{t('commonLabels.attachFile')}</span>
                                            </Button>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <Dropdown
                                                name={'shortcuts'}
                                                onChange={() => {}}
                                                placeholder={t('commonLabels.shortcuts')}
                                                options={dropdownOptions}
                                                value={dropdownSelected}
                                            />
                                        </Grid>
                                        <Grid item xs={3}>
                                            <span
                                                className={styles.insertLink}
                                                onClick={() => {
                                                    // TODO: Do something here? IDK
                                                }}
                                            >
                                                {t('commonLabels.insertLink')}{' '}
                                                <UrlIcon
                                                    fill={Colors.CM3}
                                                    className={styles.UrlIcon}
                                                />
                                            </span>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={2} />
                                <Grid item xs={8}>
                                    <TextArea
                                        placeholder={t('commonLabels.message')}
                                        onChange={() => {}}
                                        name={'message'}
                                        value={message}
                                        orientation={'row'}
                                        rows={6}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                        <Grid item xs={11}>
                            <Grid container spacing={1} justifyContent="flex-end">
                                <Grid item xs={3}>
                                    <Button
                                        variant="contained"
                                        className={styles.cancelBtn}
                                        onClick={() => (onClose ? onClose() : undefined)}
                                    >
                                        <span>{t('commonLabels.cancel')}</span>
                                    </Button>
                                </Grid>
                                <Grid item xs={3}>
                                    <Button
                                        variant="contained"
                                        className={styles.addCommentBtn}
                                        onClick={() => (onClose ? onClose() : undefined)}
                                    >
                                        <span>{t('commonLabels.addComment')}</span>
                                    </Button>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Card>
            </Grid>
        </Grid>
    );
};
export default SendSmsModalBody;
