.card {
    width: 834px;
    height: 357px;
    padding: 11px 5px 39px 43px;
    border-radius: 24px;
}

.modalTitle {
    margin: 20px 158px 9px 0 !important;
    font-family: inter;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.21;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
}

.modalDesc {
    margin: 0px 39px 18px 0;
    font-family: proxima-nova;
    font-size: 11px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.27;
    letter-spacing: normal;
    text-align: left;
    color: #2d3135;
}

.label {
    margin: 24px 5px 29px 0;
    font-family: proxima-nova;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.17;
    letter-spacing: normal;
    text-align: left;
    color: #4a4d51;
}

.attachFileBtn {
    border: solid 1px #0069ff !important;
    border-radius: 30px !important;
    width: 100% !important;
    height: 32px !important;
    font-family: proxima-nova !important;
    font-size: 12px !important;
    font-weight: bold !important;
    font-stretch: normal !important;
    font-style: normal !important;
    line-height: 1.17 !important;
    letter-spacing: normal !important;
    text-align: left !important;
    color: #467cfc !important;
    text-transform: none !important;
}

.attachFileBtn:hover {
    background-color: #ebf2ff !important;
}

.insertLink {
    margin: 35px 0.5px 124px 16px;
    font-family: proxima-nova;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.17;
    letter-spacing: normal;
    text-align: left;
    color: #6797f5;
    cursor: pointer;
}

.insertLinkIcon {
    height: 10.5px;
    width: 10.5px;
}

.cancelBtn {
    width: 100% !important;
    height: 32px !important;
    margin: 24px 13px 0 23px !important;
    padding: 9px 0 8px !important;
    border-radius: 20px !important;
    background-color: #efefef !important;
    font-size: 12px !important;
    font-family: proxima-nova !important;
    font-weight: 500 !important;
    font-stretch: normal !important;
    font-style: normal !important;
    line-height: 1.17 !important;
    letter-spacing: normal !important;
    text-align: center !important;
    color: #676767 !important;
    text-transform: none !important;
}

.cancelBtn:hover {
    background-color: #efefef !important;
}

.addCommentBtn {
    width: 100% !important;
    height: 32px !important;
    margin: 24px 13px 0 23px !important;
    padding: 9px 0 8px !important;
    border-radius: 20px !important;
    background-color: #0069ff !important;
    font-size: 12px !important;
    font-family: proxima-nova !important;
    font-weight: 500 !important;
    font-stretch: normal !important;
    font-style: normal !important;
    line-height: 1.17 !important;
    letter-spacing: normal !important;
    text-align: center !important;
    color: #ffffff !important;
    text-transform: none !important;
}

.addCommentBtn:hover {
    background-color: #0069ff !important;
}
