import styled from '@mui/material/styles/styled';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useMemo } from 'react';
import { Link } from '../../../common/components/Link';

const StyledFooter = styled('div')(({ theme }) => ({
    ...theme.typography.h7Roboto,
    fontWeight: 'normal',
    letterSpacing: 'normal',
    display: 'flex',
    justifyContent: 'center',
    minHeight: 120,
    color: 'var(--neutral6)',
}));

const TextContainer = styled('div')({
    paddingTop: 53,
});

const StyledLink = styled(Link)(({ theme }) => ({
    fontWeight: 'bold',
    color: 'var(--neutral6) !important',
    ...theme.typography.h7Roboto,
}));

const Footer = (props: { className?: string }): JSX.Element => {
    const { className } = props;
    const { t } = useAppTranslation();
    const year = useMemo(() => moment(new Date()).format('yyyy'), []);
    return (
        <StyledFooter className={className}>
            <TextContainer>
                {`© ${year} ${t('commonLabels.footer')}`}{' '}
                <StyledLink to="https://clearmechanic.com" baseURL="">
                    ClearMechanic.com
                </StyledLink>
            </TextContainer>
        </StyledFooter>
    );
};

export default Footer;
