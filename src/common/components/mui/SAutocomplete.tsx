import {
    Autocomplete,
    AutocompleteProps,
    AutocompleteValue,
    InputAdornment,
    styled,
} from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { HTMLAttributes, forwardRef, useState } from 'react';
import { SearchIcon } from '../Icons/SearchIcon';
import STextField from './STextField';

type SAutocompleteProps<T, Multiple extends boolean> = MergeTypes<
    {
        loading?: boolean;
        value: Multiple extends true ? T[] : T | null;
        getOptionSelected?: (value: Multiple extends true ? T[] : T) => boolean;
        options: T[];
        placeholder?: string;
    },
    MergeTypes<
        Partial<Pick<AutocompleteProps<T, Multiple, true, false>, 'renderInput'>>,
        AutocompleteProps<T, Multiple, true, false>
    >
>;

export default function SAutocomplete<Value, Multiple extends boolean = false>({
    placeholder,
    loading,
    noOptionsText: noOptionsTextProp,
    value,
    renderInput,
    ...props
}: SAutocompleteProps<Value, Multiple>) {
    const [open, setOpen] = useState(false);
    const { t } = useAppTranslation();
    const noOptionsText = loading
        ? t('commonLabels.loading') + '...'
        : noOptionsTextProp ?? t('commonLabels.noDataSelector');

    return (
        <StyledAutocomplete<Value, Multiple, true>
            forcePopupIcon={false}
            disableClearable
            value={value as AutocompleteValue<Value, Multiple, true, false>}
            renderInput={
                renderInput ??
                ((params) => (
                    <STextField
                        {...params}
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                                <InputAdornment position="end">
                                    <SearchIcon />
                                </InputAdornment>
                            ),
                        }}
                        fullWidth
                        variant="outlined"
                        placeholder={placeholder}
                    />
                ))
            }
            open={open}
            openOnFocus
            noOptionsText={noOptionsText}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            ListboxComponent={OSWrapper}
            {...props}
        />
    );
}

const StyledAutocomplete = styled(Autocomplete)(({ theme, disabled }) => ({
    '& :first-child': {
        marginTop: 0,
        marginBottom: 0,
    },

    '& fieldset': {
        ...(!disabled ? { borderColor: `${theme.palette.neutral[5]}` } : undefined),
    },

    '&:hover fieldset': {
        ...(!disabled ? { borderColor: `${theme.palette.primary.main} !important` } : undefined),
        ...(!disabled
            ? { backgroundColor: `${rgba(theme.palette.primary.main, 0.2)} !important` }
            : undefined),
    },

    '&.Mui-focused fieldset': {
        borderColor: `${theme.palette.primary.main} !important`,
        backgroundColor: `transparent !important`,
    },
})) as typeof Autocomplete;

const OSWrapper = forwardRef<HTMLUListElement, HTMLAttributes<HTMLUListElement>>(
    (props, fwdRef) => {
        return (
            <StyledOverlayScrollbarsComponent
                element="ul"
                ref={(ref) => {
                    if (!fwdRef) return;
                    const element = ref?.getElement() ?? null;
                    if (typeof fwdRef === 'function') {
                        fwdRef(element);
                    } else {
                        fwdRef.current = element;
                    }
                }}
                {...props}
            />
        );
    }
);

const StyledOverlayScrollbarsComponent = styled(OverlayScrollbarsComponent)({
    '.os-scrollbar-vertical': {
        margin: '4px 0',
    },
}) as typeof OverlayScrollbarsComponent;
