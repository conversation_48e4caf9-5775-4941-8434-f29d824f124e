import { MenuItem, styled } from '@mui/material';
import { rgba } from 'common/styles/ColorHelpers';

const SMenuItem = styled(MenuItem)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    flexWrap: 'wrap',
    fontWeight: 'normal',
    padding: '8px 14px',

    '&:hover': {
        color: theme.palette.primary.light,
        backgroundColor: rgba(theme.palette.primary.light, 0.1),
    },
})) as typeof MenuItem;

export const SMenuItem2 = styled(SMenuItem)(({ theme }) => ({
    padding: '6px 10px',
    minHeight: 32,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
}));

export default SMenuItem;
