import { forwardRef } from 'react';
import SSelect, { SSelectProps } from './SSelect';

export type SSelectMultiProps<T> = Omit<
    SSelectProps<T[]>,
    'multiple' | 'value' | 'renderValue' | 'ref'
> & {
    value?: T[];
    renderValue?: (value: T[]) => React.ReactNode;
    ref?: React.Ref<HTMLDivElement>;
};

const SSelectMulti = forwardRef<HTMLDivElement, SSelectMultiProps<unknown>>(
    ({ renderValue, ...props }, ref) => {
        return (
            <SSelect<unknown[]>
                ref={ref}
                displayEmpty
                {...props}
                multiple
                renderValue={renderValue}
            />
        );
    }
) as <T>(props: SSelectMultiProps<T>) => JSX.Element;

export default SSelectMulti;
