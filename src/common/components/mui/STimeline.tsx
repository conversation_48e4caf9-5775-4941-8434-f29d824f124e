import {
    Timeline,
    timelineContentClasses,
    timelineDotClasses,
    timelineItemClasses,
    timelineSeparatorClasses,
} from '@mui/lab';
import { styled } from '@mui/material';

const STimeline = styled(Timeline)({
    margin: 0,

    [`& .${timelineDotClasses.root}`]: {
        margin: 0,
        boxShadow: 'none',
        // transform: 'translateY(3px)',
    },

    [`& .${timelineItemClasses.root}`]: {
        minHeight: 30,
    },

    [`& .${timelineContentClasses.root}`]: {
        paddingTop: 0,
    },

    [`& .${timelineSeparatorClasses.root}`]: {
        transform: 'translateY(3px)',
    },
});

export default STimeline;
