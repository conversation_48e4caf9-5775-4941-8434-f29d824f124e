import { ExpandMore } from '@mui/icons-material';
import {
    Paper,
    Select,
    SelectProps,
    listClasses,
    menuItemClasses,
    outlinedInputClasses,
    selectClasses,
    styled,
} from '@mui/material';
import clsx from 'clsx';
import { rgba } from 'common/styles/ColorHelpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { ComponentProps, ComponentType, HTMLAttributes, forwardRef } from 'react';

/**
 * Styled Select for all selects
 */
const SelectBase = styled(Select)(({ theme }) => ({
    minWidth: 80,
    '--select-height': '32px',
    height: 'var(--select-height)',
    padding: 0,
    overflow: 'hidden',
    '--background-color': '#fff',
    backgroundColor: 'var(--background-color)',

    [`& .${selectClasses.icon}`]: {
        backgroundColor: 'var(--background-color)',
        top: 'calc(50% - 12px)',
    },

    [`& .${selectClasses.root}`]: {
        ...theme.typography.h6Inter,
        display: 'flex',
        alignItems: 'center',
        paddingTop: 5,
        fontWeight: 'initial',
        paddingBottom: 5,
        height: '100%',
        '&:focus': {
            backgroundColor: 'transparent',
        },
    },

    [`& .${selectClasses.select}`]: {
        ...theme.typography.h6Roboto,
        verticalAlign: 'baseline',
        lineHeight: 'var(--select-height)',
        paddingTop: 0,
        paddingBottom: 0,
        height: 'var(--select-height)',
        fontWeight: 'normal',
    },

    [`& .${selectClasses.nativeInput}`]: {
        height: 'var(--select-height)',
    },

    [`& .${outlinedInputClasses.input}`]: {
        display: 'flex',
        alignItems: 'center',
    },

    [`& .${outlinedInputClasses.notchedOutline}`]: {
        top: '0px',
        '& > legend': {
            display: 'none',
        },
    },

    [`&:hover:not(.Mui-disabled) .${outlinedInputClasses.notchedOutline}`]: {
        borderColor: 'var(--cm1)',
    },
})) as ComponentType<SelectProps>; // NOTE (MB) type assertion is necessary because otherwise TS complains for no reason, Trust Me™

const SelectCustomPaper = styled(Paper)(({ theme }) => ({
    position: 'fixed',

    overflow: 'hidden', // normally hiding scrollbar will break it, but we use custom scrollbar so it's fine

    ...scrollbarStyle(),
    [`& .${menuItemClasses.root}:hover`]: {
        backgroundColor: rgba(theme.palette.primary.main, 0.03),
    },
    '& .Mui-selected': {
        color: theme.palette.primary.main,
        fontWeight: 'bold',
        backgroundColor: rgba(theme.palette.primary.main, 0.08),
        '&:hover': {
            backgroundColor: rgba(theme.palette.primary.main, 0.12),
        },
    },
    '& .MuiTouchRipple-child': {
        backgroundColor: theme.palette.primary.main,
    },

    [`& .${listClasses.root}`]: {
        maxHeight: '80vh',
        overflowY: 'auto',
        ...scrollbarStyle(),
    },
}));

const SelectCustomPaperWithBorders = styled(SelectCustomPaper)(({ theme }) => ({
    '& .MuiMenuItem-root': {
        borderBottom: `1px solid ${theme.palette.neutral[3]}`,

        '&:last-child': {
            borderBottom: 'none',
        },
    },
}));

export type SSelectProps<T> = Omit<
    React.PropsWithoutRef<SelectProps<T>>,
    'color' | 'variant' | 'slotProps'
> & { ref?: React.Ref<HTMLDivElement> | undefined } & {
    isInvalid?: boolean;

    /**
     * if set to true, border is added between menu items of the
     * select component
     */
    menuBorders?: boolean;

    slotProps?: SelectProps<T>['slotProps'] & {
        placeholder?: ComponentProps<typeof Placeholder>;
    };
};

const SSelectBase = forwardRef<
    HTMLDivElement,
    SSelectProps<unknown> & { innerComponent: ComponentType<SelectProps> }
>(
    (
        {
            innerComponent: Inner,
            isInvalid,
            className,
            menuBorders,
            placeholder,
            slotProps = {},
            value,
            MenuProps,
            ...props
        },
        ref
    ) => {
        const { placeholder: placeholderProps, ...otherSlotProps } = slotProps;

        return (
            <Inner
                ref={ref}
                aria-invalid={isInvalid}
                color="primary"
                variant="outlined"
                IconComponent={ExpandMore}
                displayEmpty
                renderValue={
                    placeholder && !value
                        ? () => (
                              <Placeholder className="SSelect-placeholder" {...placeholderProps}>
                                  {placeholder}
                              </Placeholder>
                          )
                        : undefined
                }
                value={value}
                MenuProps={{
                    slots: {
                        paper: menuBorders ? SelectCustomPaperWithBorders : SelectCustomPaper,
                    },
                    elevation: 0,
                    transitionDuration: 100,
                    ...MenuProps,
                    MenuListProps: {
                        component: OSWrapper,
                        ...MenuProps?.MenuListProps,
                    },
                }}
                slotProps={otherSlotProps}
                className={clsx(isInvalid && 'invalid', className)}
                {...props}
            />
        );
    }
);

const OSWrapper = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>((props, fwdRef) => {
    return (
        <StyledOverlayScrollbarsComponent
            ref={(ref) => {
                if (!fwdRef) return;
                const element = ref?.getElement() ?? null;
                if (typeof fwdRef === 'function') {
                    fwdRef(element);
                } else {
                    fwdRef.current = element;
                }
            }}
            {...props}
        />
    );
});

const StyledOverlayScrollbarsComponent = styled(OverlayScrollbarsComponent)({
    '.os-scrollbar-vertical': {
        margin: '4px 0',
    },
}) as typeof OverlayScrollbarsComponent;

const Placeholder = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
    fontSize: 12,
}));

function createSelectComponent(inputComponent: React.ComponentType<SelectProps>) {
    return forwardRef<HTMLDivElement, SSelectProps<unknown>>(({ ...props }, ref) => {
        return <SSelectBase ref={ref} innerComponent={inputComponent} {...props} />;
    }) as <T>(props: SSelectProps<T>) => JSX.Element;
}

const SSelectRounded = styled(SelectBase)(({ theme, disabled }) => ({
    borderRadius: 20,
    '& > fieldset': {
        ...(!disabled ? { borderColor: `${theme.palette.neutral[5]} !important` } : undefined),

        // hide legend that usually contains label of the input (but in CMOS label is separate from Select)
        top: 0,
        '& > legend': {
            display: 'none',
        },
    },

    '&:hover > fieldset': {
        ...(!disabled ? { borderColor: `${theme.palette.primary.main} !important` } : undefined),
        ...(!disabled
            ? { backgroundColor: `${rgba(theme.palette.primary.main, 0.2)} !important` }
            : undefined),
    },

    '&.Mui-focused > fieldset': {
        borderColor: `${theme.palette.primary.main} !important`,
        backgroundColor: 'transparent !important',
    },

    '&.invalid > fieldset': {
        borderColor: `var(--danger) !important`,
    },

    [`& > .${selectClasses.select}`]: {
        color: theme.palette.primary.main,
        fontWeight: 'bold',
    },
}));

const SSelect = createSelectComponent(SSelectRounded);

export const SSelectRect = createSelectComponent(SelectBase);

export default SSelect;

export const SSelectInput = createSelectComponent(
    styled(SelectBase)({
        flexGrow: 1,
        '&.invalid > fieldset': {
            borderColor: `var(--danger) !important`,
        },
    })
);

export const SSelectGrey = createSelectComponent(
    styled(SelectBase)({
        flexGrow: 1,
        '&.invalid > fieldset': {
            borderColor: `var(--danger) !important`,
        },

        '&, & > .MuiSelect-icon': {
            backgroundColor: 'var(--neutral2)',
        },
    })
);

export const SSelectTinted = createSelectComponent(
    styled(SSelectRounded)(({ theme }) => ({
        '--background-color': 'var(--neutral1)',
    }))
);
