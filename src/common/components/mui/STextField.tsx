import { TextField, styled } from '@mui/material';

type STextFieldProps = {};

const STextField = styled(TextField)<STextFieldProps>(({ theme }) => ({
    '--border-color': theme.palette.neutral[4],
    boxSizing: 'border-box',
    '& .MuiInputBase-root': {
        height: 36,
        paddingTop: 0,
        paddingBottom: 0,
    },
    '& fieldset': {
        borderRadius: 100,
    },
    '& input': {
        padding: 0,
    },
}));

export default STextField;
