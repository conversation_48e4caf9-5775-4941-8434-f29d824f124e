import {
    CSSInterpolation,
    TextField as MuiTextField,
    TextFieldProps as MuiTextFieldProps,
    TextFieldVariants,
    Theme,
    inputBaseClasses,
    outlinedInputClasses,
    styled,
} from '@mui/material';
import { rgba } from 'common/styles/ColorHelpers';

export type CmosTextFieldVariant = 'grey' | 'roundedPrimary' | 'roundedGrey';

export type TextFieldProps<
    Value = unknown,
    Variant extends TextFieldVariants = TextFieldVariants
> = Omit<MuiTextFieldProps<Variant>, 'value'> & { value?: Value } & CmosSpecificTextFieldProps;

type CmosSpecificTextFieldProps = {
    cmosVariant?: CmosTextFieldVariant;
    fullWidth?: boolean;
};

/**
 * Wrapper around MUI TextField with some custom properties for CMOS-specific styling.
 */
const TextField = styled(MuiTextField, {
    shouldForwardProp: (prop) => !['cmosVariant', 'fullWidth'].includes(prop as string),
})<CmosSpecificTextFieldProps>(
    ({ theme, cmosVariant, size, disabled, multiline = false, fullWidth = false }) => {
        const heightValue = size === 'medium' ? 40 : 32;

        const base: CSSInterpolation = {
            '--input-background-color': theme.palette.neutral[1],

            // this var must be set to default border color, do not redefine this on :hover/:active etc
            '--input-border-color': theme.palette.neutral[5],

            width: fullWidth ? '100%' : undefined,
            height: multiline ? undefined : heightValue,
            minHeight: multiline ? heightValue : undefined,

            '&[aria-invalid=true]': {
                [`& .${outlinedInputClasses.notchedOutline}`]: {
                    borderColor: 'var(--danger)',
                },
            },

            [`& .${inputBaseClasses.input}`]: {
                paddingTop: 0,
                paddingBottom: 0,
                height: '100%',
                ...theme.typography.h6Roboto,
                fontWeight: 'normal',

                '&::placeholder': {
                    opacity: 0.75,
                },
                '&:-webkit-autofill, :-webkit-autofill:hover, :-webkit-autofill:focus, :-webkit-autofill:active':
                    {
                        '-webkit-box-shadow': '0 0 0 30px white inset !important',
                    },
            },

            [`& .${outlinedInputClasses.notchedOutline}`]: {
                borderColor: 'var(--input-border-color)',
                top: '0px',

                '& > legend': {
                    display: 'none',
                },
            },

            [`&:hover .${outlinedInputClasses.notchedOutline}`]: {
                ...(!disabled
                    ? { borderColor: `${theme.palette.primary.main} !important` }
                    : undefined),
            },

            [`& .${inputBaseClasses.root}`]: {
                height: '100%',
                backgroundColor: 'var(--input-background-color)',
            },

            [`&:hover .${inputBaseClasses.root}`]: {
                ...(!disabled
                    ? { backgroundColor: 'var(--input-background-hover-color)' }
                    : { backgroundColor: undefined }),
            },

            '.Mui-focused': {
                backgroundColor: `transparent !important`,
            },

            // input is readonly
            '&:has(input[readonly])': {
                '& input': {
                    cursor: 'default',
                },

                [`& .${outlinedInputClasses.notchedOutline}`]: {
                    // force border to 1px and default color
                    borderWidth: '1px !important',
                    borderColor: 'var(--input-border-color) !important',
                },
            },
        };

        return [base, getCmosVariantStyle(theme, cmosVariant)];
    }
);

const ROUNDED_CSS: CSSInterpolation = {
    '--input-main-color': 'black',
    '--input-highlight-color': 'var(--input-main-color)',
    '--input-text-color': 'var(--input-main-color)',

    [`& .${inputBaseClasses.input}`]: {
        color: 'var(--input-text-color)',
    },

    [`& .${inputBaseClasses.root}`]: {
        borderRadius: 50,
    },
};

function getCmosVariantStyle(
    theme: Theme,
    variant: CmosTextFieldVariant | undefined
): CSSInterpolation {
    if (variant === undefined) return {};

    switch (variant) {
        case 'grey':
            return {
                '--input-background-color': theme.palette.neutral[2],
                '--input-text-color': theme.palette.common.black,
            };
        case 'roundedPrimary':
            return [
                ROUNDED_CSS,
                {
                    '--input-main-color': theme.palette.primary.main,
                    '--input-background-hover-color': rgba(theme.palette.primary.main, 0.2),
                },
            ];
        case 'roundedGrey':
            return [
                ROUNDED_CSS,
                {
                    '--input-main-color': theme.palette.neutral[5],
                    '--input-highlight-color': theme.palette.primary.main,
                    '--input-text-color': theme.palette.common.black,
                    '--input-background-hover-color': rgba(theme.palette.primary.main, 0.2),
                },
            ];
    }
}

export default TextField;
