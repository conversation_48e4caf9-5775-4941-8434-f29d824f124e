import { Menu, styled } from '@mui/material';

const SMenu = styled(Menu)<{ borders?: boolean }>(({ theme, borders }) => ({
    '& .MuiPaper-root': {
        borderRadius: 8,
        boxShadow: `0 2px 6px -1px ${theme.palette.neutral[4]}`,
    },

    '& .MuiMenuItem-root': {
        borderBottom: borders ? `1px solid ${theme.palette.neutral[3]}` : undefined,

        '&:last-child': {
            borderBottom: borders ? 'none' : undefined,
        },
    },
}));

export default SMenu;
