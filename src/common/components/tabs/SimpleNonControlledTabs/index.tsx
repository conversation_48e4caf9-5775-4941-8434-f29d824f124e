import { useState } from 'react';
import SimpleTabs, { SimpleTabsProps } from '../SimpleTabs';

export default function SimpleNonControlledTabs({ onTabSelected, ...props }: SimpleTabsProps) {
    const [selected, setSelected] = useState<string>();

    return (
        <SimpleTabs
            onTabSelected={(value) => {
                setSelected(value);
                if (onTabSelected) onTabSelected(value);
            }}
            selected={selected}
            {...props}
        />
    );
}
