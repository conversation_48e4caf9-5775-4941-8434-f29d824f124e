import { TabsProps } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ChangeEvent, useCallback } from 'react';
import Tab, { LinkTab } from '../Tab';
import Tabs from '../Tabs';

export type SimpleTab = {
    key: string;
    label: React.ReactNode;
    href?: string;
    disabled?: boolean;
    disabledTooltipTitle?: string;
    onTabClick?: () => void;
};

export type SimpleTabsProps = {
    tabs: SimpleTab[];
    selected?: string;
    onTabSelected?: (selectedTab: string) => void;
    disabled?: boolean;
    sx?: {
        height?: number;
        gap?: number;
        overflow?: 'visible' | 'hidden';
    };
    className?: string;
};

export default function SimpleTabs({
    tabs,
    sx,
    selected,
    onTabSelected,
    disabled: allDisabled,
    className,
}: SimpleTabsProps) {
    const { t } = useAppTranslation();
    const tabsElements = tabs.map(
        ({ key, label, href, disabled, disabledTooltipTitle, onTabClick }) => {
            const TabComponent = href ? LinkTab : Tab;

            if (allDisabled && !disabled) {
                disabled = true;
            }

            const handleClick = () => {
                if (onTabSelected) onTabSelected(key);
                if (onTabClick) onTabClick();
            };

            return disabled && disabledTooltipTitle ? (
                <ArrowTooltip
                    content={t(disabledTooltipTitle || '')}
                    position="top"
                    disabled={!disabled}
                >
                    <span>
                        <TabComponent
                            key={key}
                            to={href ?? '#'}
                            value={key}
                            label={label}
                            disabled={disabled}
                            disableRipple
                            onClick={handleClick}
                        />
                    </span>
                </ArrowTooltip>
            ) : (
                <TabComponent
                    key={key}
                    disableRipple
                    to={href ?? '#'}
                    value={key}
                    label={label}
                    disabled={disabled}
                    onClick={handleClick}
                />
            );
        }
    );
    return (
        <Tabs
            _height={sx?.height}
            style={
                {
                    '--gap': sx?.gap ? `${sx.gap}px` : undefined,
                    '--overflow': sx?.overflow || 'hidden',
                } as React.CSSProperties
            }
            onChange={
                useCallback(
                    (_event: ChangeEvent<HTMLButtonElement>, selected: any) => {
                        if (onTabSelected) onTabSelected(selected);
                    },
                    [onTabSelected]
                ) as TabsProps['onChange']
            }
            value={selected}
            className={className}
        >
            {tabsElements}
        </Tabs>
    );
}
