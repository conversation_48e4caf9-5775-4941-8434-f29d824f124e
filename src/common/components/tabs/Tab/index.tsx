import { Tab as MuiTab, TabProps as MuiTabProps, styled } from '@mui/material';
import { NavLink } from 'react-router-dom';

const STab = styled(MuiTab)(({ theme }) => ({
    textTransform: 'initial',
    padding: '10px 2px',
    minWidth: 30,
    height: 'var(--tabs-height)',
    minHeight: 'initial',
    color: theme.palette.neutral[7],
    opacity: 1,
    ...theme.typography.h5Roboto,
    fontWeight: '500',
    borderRadius: 5,
    position: 'relative',

    '& .STab-overlay': {
        position: 'absolute',
        inset: 0,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        pointerEvents: 'none',
        visibility: 'hidden',
        fontWeight: '700',
    },

    '&::after': {
        content: '" "',
        display: 'block',
        position: 'absolute',
        left: 3,
        right: 3,
        height: 2,
        bottom: 0,
        transition: 'transform 0.25s',
        transformOrigin: 'bottom',
    },

    '&:active': {
        backgroundColor: 'var(--neutral3) !important',
    },

    '&.Mui-selected': {
        color: 'transparent',

        '& .STab-overlay': {
            visibility: 'visible',
            color: theme.palette.primary.main,
        },

        '&::after': {
            backgroundColor: 'var(--cm1)',
            borderRadius: 2,
            transform: 'scaleY(160%)',
        },
    },

    '&:hover:not(.Mui-selected)::after': {
        content: '" "',
        backgroundColor: theme.palette.neutral[7],
        display: 'block',
        position: 'absolute',
        left: 3,
        right: 3,
        height: 2,
        bottom: 0,
    },

    '&:hover': {
        background: 'var(--neutral2)',
        color: `${theme.palette.primary.main} !important`,
    },
})) as typeof MuiTab;

export default function Tab({ label, ...props }: MuiTabProps) {
    return (
        <STab
            label={
                <>
                    {label}
                    <span className="STab-overlay">{label}</span>
                </>
            }
            {...props}
        />
    );
}

export function LinkTab({ label, ...props }: MuiTabProps<typeof NavLink>) {
    return (
        <STab
            component={NavLink}
            label={
                <>
                    {label}
                    <span className="STab-overlay">{label}</span>
                </>
            }
            {...props}
        />
    );
}
