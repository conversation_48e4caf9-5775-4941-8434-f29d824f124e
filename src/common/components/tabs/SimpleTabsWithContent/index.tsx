import { useRef } from 'react';
import SimpleTabs, { SimpleTab, SimpleTabsProps } from '../SimpleTabs';

export type TabWithContent = SimpleTab & {
    content: React.ReactNode;
};

export type SimpleTabsWithContentProps = Omit<
    SimpleTabsProps,
    'tabs' | 'selected' | 'onTabSelected'
> & {
    tabs: TabWithContent[];
    selected: string;
    onTabSelected?: (selected: string) => void;
    children?: (content: React.ReactNode, selected: string) => React.ReactNode;
    renderLayout?: RenderLayoutFn;
    keepMounted?: boolean;
};

type RenderLayoutFn = (layout: {
    content: React.ReactNode;
    tabs: React.ReactNode;
    selected: string;
}) => React.ReactElement;

const defaultLayout: RenderLayoutFn = (v) => (
    <>
        {v.tabs}
        {v.content}
    </>
);

const directMap: (content: React.ReactNode, selected: string) => React.ReactNode = (c) => c;

export default function SimpleTabsWithContent({
    tabs,
    selected,
    onTabSelected,
    children,
    renderLayout,
    keepMounted = false,
    ...props
}: SimpleTabsWithContentProps) {
    const content = useContent(tabs, selected, keepMounted);

    const renderedContent = (children ?? directMap)(content, selected);

    return (renderLayout ?? defaultLayout)({
        tabs: (
            <SimpleTabs {...props} tabs={tabs} onTabSelected={onTabSelected} selected={selected} />
        ),
        content: renderedContent,
        selected,
    });
}

function useContent(tabs: TabWithContent[], selected: string, keepMounted: boolean) {
    const activeTab = tabs.find((t) => t.key === selected);

    const seen = useRef<Set<string>>(new Set());

    if (keepMounted) {
        if (!seen.current.has(selected)) {
            seen.current.add(selected);
        }

        const elements: React.ReactElement[] = [];

        seen.current.forEach((key) => {
            const tab = tabs.find((x) => x.key === key);
            if (tab) {
                elements.push(
                    <div
                        key={tab.key}
                        style={{ display: tab.key === selected ? 'contents' : 'none' }}
                    >
                        {tab.content}
                    </div>
                );
            }
        });

        return <>{elements}</>;
    } else {
        return activeTab?.content;
    }
}
