import { TabContext } from '@mui/lab';
import React, { useState } from 'react';

export type StatefulTabContextProps = {
    initialValue?: any;
    children?: (setTab: (tab: any) => void, tab: any) => React.ReactNode;
};

export default function StatefulTabContext({ initialValue, children }: StatefulTabContextProps) {
    const [tab, setTab] = useState(initialValue);
    return <TabContext value={tab}>{children && children(setTab, tab)}</TabContext>;
}
