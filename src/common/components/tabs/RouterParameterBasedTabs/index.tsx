import { useCallback, useEffect, useMemo, useRef } from 'react';
import { generatePath, useNavigate, useParams } from 'react-router-dom';
import { removeUndefined } from 'utils/object';
import SimpleTabsWithContent, {
    SimpleTabsWithContentProps,
    TabWithContent,
} from '../SimpleTabsWithContent';

export type RouterParameterBasedTabsProps = Omit<
    SimpleTabsWithContentProps,
    'selected' | 'onTabSelected' | 'tabs'
> & {
    urlPattern: string;
    parameterName: string;
    tabs: RouterTab[];
};

export type RouterTab = {
    value: string;
    content: React.ReactNode;
    label: string;
    disabled?: boolean;
    disabledTooltipTitle?: string;
};

export default function RouterParameterBasedTabs({
    urlPattern,
    parameterName,
    tabs,
    ...props
}: RouterParameterBasedTabsProps) {
    const params = useParams<Record<string, string>>();
    const value = params[parameterName];
    const navigate = useNavigate();

    const { [parameterName]: baseParameter, ...otherParams } = params;

    const tabsWithContent: TabWithContent[] = useMemo(() => {
        try {
            return tabs.map((t) => ({
                content: t.content,
                key: t.value,
                label: t.label,
                href: generatePath(urlPattern, {
                    ...removeUndefined(otherParams),
                    [parameterName]: t.value,
                }),
                disabled: t.disabled,
                disabledTooltipTitle: t.disabledTooltipTitle,
            }));
        } catch (e) {
            console.error(e);
            return [];
        }
    }, [tabs, urlPattern, otherParams, parameterName]);

    const handleTabChange = useCallback(
        (value: string, replace: boolean = false) => {
            navigate(
                {
                    pathname: generatePath(urlPattern, { [parameterName]: value }),
                },
                { replace }
            );
        },
        [navigate, parameterName, urlPattern]
    );

    const autoCorrected = useRef(false);
    useEffect(() => {
        if (!autoCorrected.current && value && tabs.find((x) => x.value === value) === undefined) {
            autoCorrected.current = true;
            if (tabs.length > 0) {
                handleTabChange(tabs[0].value, true);
            }
        }
    }, [tabs, value, handleTabChange]);

    return (
        <SimpleTabsWithContent
            tabs={tabsWithContent}
            selected={value ?? ''}
            onTabSelected={handleTabChange}
            {...props}
        />
    );
}
