import {
    Tabs as MuiTabs,
    TabsProps as MuiTabsProps,
    styled,
    TabScrollButton,
    TabScrollButtonProps,
} from '@mui/material';
import { forwardRef } from 'react';

export type TabsProps = MuiTabsProps & {
    _height?: number;
};

export default function Tabs({ _height, style, ...props }: TabsProps) {
    return (
        <MuiTabsStyled
            variant="scrollable"
            scrollButtons="auto"
            style={
                { '--tabs-height': `${_height ?? 30}px`, ...(style ?? {}) } as React.CSSProperties
            }
            TabIndicatorProps={{
                style: { display: 'none', transition: 'none' },
            }}
            ScrollButtonComponent={CustomScrollButton}
            {...props}
        />
    );
}
const CustomScrollButton = forwardRef<HTMLButtonElement, TabScrollButtonProps>((props, ref) => {
    return <StyledTabScrollButton disableRipple {...props} ref={ref} />;
});

const StyledTabScrollButton = styled(TabScrollButton)({
    ':hover': {
        backgroundColor: 'var(--neutral3)',
    },

    ':active': {
        backgroundColor: 'var(--neutral4)',
    },
});

const MuiTabsStyled = styled(MuiTabs)(({ theme }) => ({
    '& .MuiTabs-flexContainer': {
        gap: 'var(--gap)',
    },

    '& .MuiTabs-indicator': {
        backgroundColor: theme.palette.primary.main,
        height: 3,
        borderRadius: 2,
        bottom: 0,
    },

    '& .MuiTabs-scrollButtons': {
        color: theme.palette.primary.main,
    },

    '& .MuiTabs-scrollButtons svg': {
        width: 25,
        height: 25,
    },
    '& .MuiTab-root': {
        padding: 5,
        overflow: 'var(--overflow)',
    },

    '--gap': '30px',
    '--overflow': 'hidden',
    height: 'var(--tabs-height)',
    minHeight: 'var(--tabs-height)',
}));
