import { styled } from '@mui/material';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { rgba } from '../../styles/ColorHelpers';

export type PickerProps = {
    value: any;
    label: string;
    selected: boolean;
    disabled: boolean;
    onChange: Function;
};
export function Picker({
    label,
    value,
    selected = false,
    disabled = false,
    onChange = () => {},
}: PickerProps) {
    const [classState, setClassState] = useState('default');

    useEffect(() => {
        if (selected) setClassState('selected');
        else setClassState('default');
    }, [disabled, selected]);
    return (
        <DivButton
            className={clsx(classState, disabled ? 'deactive' : '')}
            onClick={() => {
                onChange(value, label);
            }}
        >
            <div>{label}</div>
        </DivButton>
    );
}

const DivButton = styled('div')(({ theme }) => ({
    width: 59,
    height: 25,
    borderRadius: 5,
    display: 'flex',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    ...theme.typography.h5Inter,

    '&.default': {
        background: theme.palette.neutral[3],
        color: theme.palette.neutral[6],
        border: `1px solid ${theme.palette.neutral[3]}`,
    },

    '&.selected': {
        background: theme.palette.primary.main,
        color: theme.palette.neutral[1],
        border: `1px solid ${theme.palette.neutral[3]}`,
    },

    '&.deactive': {
        opacity: 0.5,
    },

    '&.default:hover, &.selected:hover': {
        background: rgba(theme.palette.primary.main, 0.2),
        color: theme.palette.neutral[6],
        border: `1px solid ${theme.palette.neutral[3]}`,
    },

    '&.default:active, &.selected:active': {
        background: rgba(theme.palette.primary.main, 0.2),
        border: `1px solid ${rgba(theme.palette.primary.main, 0.2)}`,
        color: theme.palette.neutral[6],
    },
}));
