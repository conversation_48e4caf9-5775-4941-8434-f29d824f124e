import { styled } from '@mui/material';

export const MarkedText = ({ text, markText }: { text: string; markText: string }) => {
    text ??= 'No label found';
    const lower = text.toLowerCase();
    const indexFrom = lower.indexOf(markText.toLowerCase());
    const title = text.length > 32 ? text : undefined;
    if (indexFrom > -1) {
        return (
            <span title={title}>
                {text.substring(0, indexFrom)}
                <StyledSpan>{text.substring(indexFrom, indexFrom + markText.length)}</StyledSpan>
                {text.substring(indexFrom + markText.length, text.length)}
            </span>
        );
    } else {
        return <span title={title}>{text}</span>;
    }
};

const StyledSpan = styled('span')({
    backgroundColor: 'var(--cm5)',
});
