import { styled } from '@mui/material';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import HeaderBar from 'views/HeaderBar';
import SideMenu from 'views/HeaderBar/SideMenu';
import FileDownloadProvider from '../FileDownloadProvider';

type LayoutProps = {
    children?: React.ReactNode;
    noPadding?: boolean;
};

/**
 * base layout for the application with header, side bar and any custom content inside, has padding by default
 */
export default function Layout({ children, noPadding }: LayoutProps) {
    const location = useLocation();

    const [sidebarOpen, setSidebarOpen] = useState(false);

    useEffect(() => {
        setSidebarOpen(false);
    }, [location.pathname, location.search]);

    return (
        <>
            <FileDownloadProvider />
            <HeaderBar onOpenSideBar={() => setSidebarOpen(true)}>
                <LayoutBody noPadding={noPadding ?? false}>{children}</LayoutBody>
            </HeaderBar>
            <SideMenu open={sidebarOpen} onClose={() => setSidebarOpen(false)} />
        </>
    );
}

const LayoutBody = styled('div', {
    shouldForwardProp: (prop) => prop !== 'noPadding',
})<{ noPadding: boolean }>(({ noPadding }) => ({
    width: '100%',
    padding: noPadding ? '0px' : '33px 0px 0 0px',
    marginTop: 'var(--header-height)',
}));
