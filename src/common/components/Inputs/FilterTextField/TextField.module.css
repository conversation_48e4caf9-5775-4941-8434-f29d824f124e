.container {
    width: 100%;
    min-width: 130px;
    display: flex;
}

.orientationRow {
    flex-direction: row;
}
.orientationColumn {
    flex-direction: column;
}

.errorIcon {
    width: 3px;
    height: 12.4px;
    position: absolute;
    right: 12.4px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url(../../../assets/images/exclamation_icon.png);
}

.input:hover {
    border: solid 1px #8e8e8e;
}
.input:focus {
    outline: none;
    border: solid 1px #83a8ff;
}
.input:active {
    border: solid 1px #467cfc;
}
.icon {
    position: absolute;
    right: 14px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: none;
    background-color: transparent;
}
.icon:focus {
    outline: none;
}
.showPasswordBtn {
    width: 18.4px;
    height: 11.2px;
    background-image: url(../../../assets/images/visibleEyeIcon.png);
}

.hidePasswordBtn {
    width: 18px;
    height: 17px;
    background-size: cover;
    background-image: url(../../../assets/images/hiddenEyeIcon.png);
}

.inputContainer {
    position: relative;
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
}
.input {
    padding: 0px 13px;
    height: calc(100% - 2px);
    width: calc(100% - 26px);
    border-radius: 16px;
    border: solid 1px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    opacity: 0.8;
    font-family: proxima-nova;
    font-size: 12px;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #26292b;
}
.label {
    height: 15px;
    font-family: proxima-nova;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.17;
    letter-spacing: normal;
    text-align: left;
    color: #4a4d51;
    margin: 12px 0;
}

.requiredDot {
    width: 8px;
    height: 8px;
    position: absolute;
    top: -2px;
    right: -2px;
    border-radius: 50%;
    background-color: #467cfc;
}
.requiredLabel {
    font-family: proxima-nova;
    font-size: 13px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.08;
    letter-spacing: normal;
    color: #6797f5;
}
.optionalLabel {
    font-weight: 300;
}

.invalidInput {
    border: solid 1px #f15857;
}
.invalidInput:hover {
    border: solid 1px #f15857;
}
.invalidInput:focus {
    border: solid 1px #f15857;
}
.invalidInput:active {
    border: solid 1px #f15857;
}
