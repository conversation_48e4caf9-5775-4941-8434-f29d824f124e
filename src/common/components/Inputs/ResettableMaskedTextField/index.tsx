import { TextFieldProps } from 'common/components/mui';
import React, { CSSProperties, useCallback, useEffect, useState } from 'react';
import MaskedInput from 'react-text-mask';
import InputWrapper from '../InputWrapper';
import { ResettableMaskedTextInput } from './ResettableMaskedTextInput';

type ResettableMaskedTextFormFieldProps = Omit<
    TextFieldProps,
    'IconRight' | 'onIconRightClick' | 'value'
> & {
    mask: Array<string | RegExp> | false;
    maskPlaceholderChar?: string;
    showMask?: boolean;
    value: string;
    wrapperRef?: React.Ref<HTMLDivElement>;
    onSave: (value: string) => Promise<void>;
    placeholder?: string;
    name?: string;
    required?: boolean;
    alignItems?: CSSProperties['alignItems'];
    isInvalid?: boolean;
    showValidationIndicators?: boolean;
    maxLength?: number;
    minLength?: number;
    onChange?: TextFieldProps['onChange'];
    onEdit?: (value: string) => string;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    onValidate?: (value: string) => boolean;
    disabled?: boolean;
    showEditButton?: boolean;
    block?: boolean;
};

const ResettableMaskedTextFormField = React.forwardRef(
    (
        {
            label,
            required,
            onValidate,
            alignItems = 'center',
            block = true,
            wrapperRef,
            isInvalid,
            disabled,
            ...props
        }: ResettableMaskedTextFormFieldProps,
        ref: React.ForwardedRef<MaskedInput>
    ) => {
        const [isInvalidComputed, setIsInvalidComputed] = useState(false);

        const validate = useCallback(
            (value: string) => {
                if (onValidate) {
                    const isInvalid = onValidate(value);
                    setIsInvalidComputed(isInvalid);
                }
            },
            [onValidate]
        );

        useEffect(() => {
            if (isInvalid === undefined) return;
            setIsInvalidComputed(isInvalid);
        }, [isInvalid]);

        return (
            <InputWrapper
                ref={wrapperRef}
                fullWidth={block}
                isInvalid={isInvalidComputed}
                label={label}
                disabled={disabled}
            >
                <ResettableMaskedTextInput
                    ref={ref as any}
                    isInvalid={isInvalidComputed}
                    isRequired={required}
                    onValidate={validate}
                    disabled={disabled}
                    {...props}
                />
            </InputWrapper>
        );
    }
);

export default ResettableMaskedTextFormField;
