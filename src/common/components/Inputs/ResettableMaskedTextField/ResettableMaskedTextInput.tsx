import { Box, CircularProgress, IconButton, InputAdornment, styled } from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { handleCommonKeyDowns } from 'common/components/Inputs/utils';
import { TextField, TextFieldProps } from 'common/components/mui';
import { Colors } from 'common/styles/Colors';
import React, { ChangeEvent, useCallback, useRef, useState } from 'react';
import MaskedInput from 'react-text-mask';

type ResettableMaskedTextInputProps = Omit<
    TextFieldProps,
    'IconRight' | 'onIconRightClick' | 'value'
> & {
    mask: Array<string | RegExp> | false;
    maskPlaceholderChar?: string;
    showMask?: boolean;
    onSave: (value: string) => Promise<void>;
    onEdit?: (value: string) => string;
    onValidate?: (value: string) => void;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    readonly?: boolean;
    isRequired?: boolean;
    isInvalid?: boolean;
    value?: string;
    showEditButton?: boolean;
} & TextFieldProps<string> &
    Pick<TextFieldProps['InputProps'] & {}, 'endAdornment' | 'startAdornment'>;
export const ResettableMaskedTextInput = React.forwardRef(
    (
        {
            mask,
            maskPlaceholderChar,
            showMask,
            value,
            onSave,
            onBlur,
            onChange,
            onEdit,
            onValidate,
            onKeyDown,
            isRequired,
            readonly,
            disabled,
            size = 'medium',
            showEditButton,
            isInvalid,
            id,

            ...textFieldProps
        }: ResettableMaskedTextInputProps,
        ref: React.ForwardedRef<MaskedInput>
    ) => {
        const [newValue, setNewValue] = useState('');
        const [isEditing, setEditing] = useState(false);
        const [isLoading, setLoading] = useState(false);

        const cancelButtonHover = useRef(false);
        const cancelSave = useRef(false);
        const canSave =
            !cancelSave.current && !isInvalid && (isRequired ? newValue.trim() !== '' : true);

        const saveCallback = useCallback(async () => {
            setEditing(false);
            if (!canSave) return;

            if (newValue === value) return;
            setLoading(true);
            try {
                await onSave(newValue);
            } finally {
                setLoading(false);
            }
        }, [onSave, newValue, value, canSave]);

        const startEditing = () => {
            setEditing(true);
            setNewValue(value || '');
            cancelSave.current = false;
        };

        const onIconClickCallback = () => {
            if (isEditing) {
                saveCallback();
            } else {
                startEditing();
            }
        };

        const cancelCallback = useCallback(() => {
            setEditing(false);
            cancelSave.current = true;
            cancelButtonHover.current = false;
            if (value && onValidate) onValidate(value);
        }, [setEditing, cancelSave, value, onValidate]);

        const onChangeCallback = useCallback(
            (e: ChangeEvent<HTMLInputElement>) => {
                if (onValidate) onValidate(e.target.value);
                if (isEditing) {
                    if (onEdit) {
                        const editableValue = onEdit(e.target.value);
                        setNewValue(editableValue);
                    } else {
                        setNewValue(e.target.value);
                    }
                }
                if (onChange) {
                    onChange(e);
                }
            },
            [onValidate, onEdit, onChange, isEditing]
        );

        const onBlurCallback = useCallback(
            (e: React.FocusEvent<HTMLInputElement>) => {
                if (onBlur) {
                    onBlur(e);
                }
                if (!cancelButtonHover.current) saveCallback();
            },
            [onBlur, saveCallback]
        );

        const handleMouseEnter = () => {
            cancelButtonHover.current = true;
        };

        const handleMouseLeave = () => {
            cancelButtonHover.current = false;
        };

        const endAdornment = showEditButton ? (
            <InputAdornment position="end">
                {!isInvalid && (
                    <IconButton
                        sx={
                            size === 'small'
                                ? { height: '32px', width: '32px', marginRight: -1.5 }
                                : { height: '40px', width: '40px', marginRight: -1.5 }
                        }
                        onClick={onIconClickCallback}
                    >
                        {isEditing ? <CheckIcon /> : <EditIcon />}
                    </IconButton>
                )}
            </InputAdornment>
        ) : (
            isEditing && (
                <InputAdornment position="end">
                    {!isInvalid && (
                        <IconButton
                            sx={
                                size === 'small'
                                    ? { height: '32px', width: '32px', marginRight: -1.5 }
                                    : { height: '40px', width: '40px', marginRight: -1.5 }
                            }
                            onClick={onIconClickCallback}
                        >
                            <CheckIcon />
                        </IconButton>
                    )}
                </InputAdornment>
            )
        );

        return (
            <>
                <MaskedInput
                    id={id}
                    ref={ref}
                    mask={mask}
                    placeholderChar={maskPlaceholderChar || '\u2000'}
                    value={(isEditing ? newValue : value) ?? ''}
                    disabled={disabled}
                    readOnly={readonly}
                    showMask={showMask}
                    // onChange and onBlur must be passed to MaskedInput directly instead of TextField
                    onChange={onChangeCallback}
                    onBlur={onBlurCallback}
                    render={(ref, props) => (
                        <TextField
                            fullWidth
                            onClick={() => !isEditing && startEditing()}
                            InputProps={{
                                readOnly: !isEditing,
                                endAdornment: endAdornment,
                                error: isInvalid,
                            }}
                            onKeyDown={handleCommonKeyDowns({
                                onKeyDown,
                                onEnterPress: () => saveCallback(),
                                onEscPress: () => cancelCallback(),
                            })}
                            onFocus={() => {
                                !isEditing && startEditing();
                            }}
                            size={size}
                            inputRef={ref}
                            {...textFieldProps}
                            {...props}
                        />
                    )}
                />
                {isEditing &&
                    (size === 'small' ? (
                        <StyledIconButton
                            size="small"
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                            onClick={() => cancelCallback()}
                        >
                            <CloseIcon fill={Colors.Neutral6} />
                        </StyledIconButton>
                    ) : (
                        <IconButton
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                            onClick={() => cancelCallback()}
                        >
                            <CloseIcon fill={Colors.Neutral6} />
                        </IconButton>
                    ))}
                {isLoading && (
                    <Box marginLeft={1} display={'flex'} alignItems={'center'}>
                        <CircularProgress size={20} thickness={4} />
                    </Box>
                )}
            </>
        );
    }
);
const StyledIconButton = styled(IconButton)({
    padding: '0 4px',
});
