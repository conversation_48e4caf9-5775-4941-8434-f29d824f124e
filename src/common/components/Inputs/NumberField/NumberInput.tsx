import { InputBaseComponentProps } from '@mui/material';
import { TextField, TextFieldProps } from 'common/components/mui';
import React, { forwardRef, memo } from 'react';
import NumberFormat, { NumberFormatPropsBase } from 'react-number-format';
import { useValidateTextFieldValue } from '../utils';

type FormatProps = NumberFormatPropsBase<JSX.IntrinsicElements['input']>;

export type NumberInputProps = Omit<TextFieldProps, 'value' | 'defaultValue'> & {
    onEnterPress?: React.MouseEventHandler;
    onEscPress?: React.MouseEventHandler;
    enableEnterComplete?: boolean;
    showLoader?: boolean;
    template?: string;
    formatProps?: FormatProps;
    value?: string | null | number;

    /**
     * Max length of the input value (including minus sign and comma)
     */
    maxLength?: number;
    minLength?: number;

    /**
     * If set to true, input will not allow value that do not fit `maxLength` requirement.
     */
    disallowInvalidInputValue?: boolean;
    isInvalid?: boolean;
    readonly?: boolean;
    dataTestId?: string;
} & Pick<
        FormatProps,
        | 'onValueChange'
        | 'decimalScale'
        | 'decimalSeparator'
        | 'fixedDecimalScale'
        | 'thousandSeparator'
    > &
    Pick<TextFieldProps['InputProps'] & {}, 'endAdornment' | 'startAdornment'>;

const NumberInput = ({
    formatProps,
    enableEnterComplete,
    showLoader,
    onEnterPress,
    onEscPress,
    template = '',
    value,

    onValueChange,
    decimalScale,
    decimalSeparator,
    fixedDecimalScale,
    thousandSeparator,

    maxLength,
    minLength,
    disallowInvalidInputValue = false,

    isInvalid,
    readonly,

    endAdornment,
    startAdornment,

    InputProps,
    dataTestId,
    ...props
}: NumberInputProps) => {
    const matches = template.match(/(.*)\{0\}(.*)/);
    const prefix = (matches && matches[1]) ?? '';
    const suffix = (matches && matches[2]) ?? '';

    const formatPropsExt = {
        prefix,
        suffix,
        onValueChange,
        decimalScale,
        decimalSeparator,
        fixedDecimalScale,
        thousandSeparator,
        maxLength: undefined as number | undefined,
        minLength: undefined as number | undefined,
        ...(formatProps ?? {}),
    };

    if (disallowInvalidInputValue) {
        formatPropsExt.maxLength = maxLength;
    }

    const stringValue = valueToString(value, formatPropsExt.decimalSeparator);
    const isValid = useValidateTextFieldValue(stringValue ?? '', { maxLength, minLength });
    const isInvalidExt = isInvalid || !isValid;

    return (
        <TextField
            value={stringValue}
            inputProps={{ _formatProps: formatPropsExt, _dataTestId: dataTestId }}
            error={isInvalidExt}
            InputProps={{
                inputComponent: CustomInputComponent,
                endAdornment,
                startAdornment,
                readOnly: readonly,
                ...InputProps,
            }}
            data-test-id={dataTestId}
            {...props}
        />
    );
};

export default memo(NumberInput);

const CustomInputComponent = forwardRef(
    (
        { _formatProps, _dataTestId, defaultValue, value, ...props }: InputBaseComponentProps,
        ref: React.ForwardedRef<HTMLInputElement>
    ) => {
        const formatProps = _formatProps as NumberFormatPropsBase<JSX.IntrinsicElements['input']>;

        return (
            <NumberFormat
                data-test-id={`${_dataTestId}__input`}
                getInputRef={ref}
                {...formatProps}
                value={value as number | string | null}
                defaultValue={defaultValue as number | string}
                isAllowed={(x) => !x.floatValue || Math.abs(x.floatValue) < 100000000}
                {...props}
            />
        );
    }
);

function valueToString(
    v: string | null | undefined | number,
    decimalSeparator: string | undefined
): string | undefined {
    if (v === undefined) return undefined;

    if (typeof v === 'number') {
        const defaultStringNumber = v + '';
        if (decimalSeparator) {
            return defaultStringNumber.replace('.', decimalSeparator);
        }
        return defaultStringNumber;
    }

    if (!v) return '';
    return v;
}
