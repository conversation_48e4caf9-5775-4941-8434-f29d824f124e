import { useId } from 'react';
import InputWrapper, { InputWrapperProps } from '../InputWrapper';
import NumberInput, { NumberInputProps } from './NumberInput';

export type NumberFormFieldProps = Omit<NumberInputProps, 'classes'> &
    Pick<
        InputWrapperProps,
        'label' | 'fullWidth' | 'isRequired' | 'showValidationIndicators' | 'isInvalid'
    > & {
        slotProps?: {
            inputWrapper?: Partial<InputWrapperProps>;
        };
        classes?: (NumberInputProps['classes'] & {}) & (InputWrapperProps['classes'] & {});
    };

const NumberFormField = ({
    label,
    fullWidth,
    isRequired,
    showValidationIndicators,
    slotProps = {},
    classes = {},
    disabled,
    id: idProp,
    ...props
}: NumberFormFieldProps) => {
    const { label: labelClass, ...numberInputClasses } = classes;
    const randomId = useId();
    const id = idProp ?? randomId;

    return (
        <InputWrapper
            fullWidth={fullWidth}
            label={label}
            isRequired={isRequired}
            showValidationIndicators={showValidationIndicators}
            classes={{ label: labelClass }}
            disabled={disabled}
            htmlFor={id}
            {...slotProps.inputWrapper}
        >
            <NumberInput
                fullWidth
                disabled={disabled}
                classes={numberInputClasses}
                id={id}
                {...props}
            />
        </InputWrapper>
    );
};

export { NumberInput as NumberField, NumberFormField };
