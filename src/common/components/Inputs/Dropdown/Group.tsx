import { styled } from '@mui/material';
import { components } from 'react-select';

export const Group: typeof components.Group = (props) => {
    return (
        <div {...props}>
            <DivGroup>
                <span>{props.label}</span>
            </DivGroup>
            <div>{props.children}</div>
        </div>
    );
};

const DivGroup = styled('div')(({ theme }) => ({
    ...theme.typography.h7Inter,
    cursor: 'default',
    display: 'flex',
    height: 30,
    paddingBottom: 8,
    paddingTop: 9,
    paddingLeft: 12,
    paddingRight: 12,
    alignItems: 'center',
    boxSizing: 'border-box',
    backgroundColor: theme.palette.neutral[3],
    fontWeight: 400,
    color: theme.palette.neutral[7],
}));
