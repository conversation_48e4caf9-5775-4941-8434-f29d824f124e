import { CSSObject } from '@emotion/react';
import { IconProps } from 'common/components/Icons/Icon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import React, { ComponentType, ForwardedRef, forwardRef } from 'react';
import Select, { CommonProps, Props as SelectProps, ValueType, components } from 'react-select';
import Creatable from 'react-select/creatable';
import InputWrapper, { InputWrapperProps } from '../InputWrapper';
import { handleCommonKeyDowns } from '../utils';
import { GroupData, OptionData } from './DataOption';
import { Group } from './Group';
import {
    DropdownControlComponent,
    DropdownIndicator,
    DropdownLoadingIndicator,
    DropdownMenuComponent,
    DropdownMenuListComponent,
    DropdownOptionComponent,
    DropdownPlaceholder,
    DropdownSelectContainer,
    DropdownValueContainerComponent,
    SingleValue,
} from './dropdown-components';
import './scuffed-portal.css';
import { DROPDOWN_SHARED_PROPS_KEY } from './util';

type ImplementationProps<Value, Multiple extends boolean> = SelectProps<
    OptionData<Value>,
    Multiple
>;

type ImplementationComponents<Value, Multiple extends boolean> = ImplementationProps<
    Value,
    Multiple
>['components'] & {};

export type { ImplementationComponents as DropdownImplementationComponents };

export type CmosDropdownVariant =
    | 'grey'
    | 'lightGray'
    | 'roundedGrey'
    | 'roundedPrimary'
    | 'roundedPrimaryFilled'
    | 'transparent'
    | 'default';

export type CustomHeaderMenuComponent<Value, Multiple extends boolean> = ComponentType<
    CommonProps<OptionData<Value>, Multiple>
>;
export type CustomFooterMenuComponent<Value, Multiple extends boolean> = ComponentType<
    CommonProps<OptionData<Value>, Multiple>
>;
export type DropdownProps<Value = any, Multiple extends boolean = false> = Pick<
    InputWrapperProps,
    | 'label'
    | 'isRequired'
    | 'isInvalid'
    | 'showValidationIndicators'
    | 'disabled'
    | 'fullWidth'
    | 'tooltipText'
> & {
    id?: string;
    value?: ValueType<OptionData<Value>, Multiple>;
    multiple?: Multiple;
    cmosVariant?: CmosDropdownVariant;
    showLoader?: boolean;
    CustomDropdownIndicator?: ImplementationComponents<Value, Multiple>['DropdownIndicator'];
    CustomMenu?: ImplementationComponents<Value, Multiple>['MenuList'];
    CustomHeaderMenu?: CustomHeaderMenuComponent<Value, Multiple>;
    CustomFooterMenu?: CustomFooterMenuComponent<Value, Multiple>;
    CustomOption?: ImplementationComponents<Value, Multiple>['Option'];
    CustomGroup?: typeof components.Group;
    options?: ReadonlyArray<OptionData<Value> | GroupData<Value>>;
    creatable?: boolean;
    onInputChange?: (newInputValue: string) => void;
    extraProps?: Record<string, any>;
    showValueCounterAfter?: number;
    onClose?: () => void;
    onOpen?: () => void;
    size?: 'small' | 'medium';
    onEnterPress?: React.KeyboardEventHandler;
    autoFocus?: boolean;
    openMenuOnFocus?: boolean;
    /**
     * height and width of the icons, only applicable if
     * icons are being displayed
     */
    iconSize?: number;

    getOptionValue?: ImplementationProps<Value, Multiple>['getOptionValue'];
    showDisabledStyles?: boolean;

    /**
     * @deprecated TODO (MB) CMOS-1993 remove
     */
    Icon?: ComponentType<IconProps>;
    /**
     * @deprecated TODO (MB) CMOS-1993 remove
     */
    optionStyle?: OptionStyle;
    /**
     * @deprecated use slotProps instead
     */
    classes?: { label?: string };

    styles?: {
        control?: CSSObject;
        menu?: CSSObject;
        menuList?: CSSObject;
        option?: CSSObject;
        indicatorsContainer?: CSSObject;
    };

    slotProps?: {
        inputWrapper?: Partial<InputWrapperProps>;
    };

    /**
     * If set to true, the dropdown's options will be displayed as disabled (but selecting will still work).
     */
    displayOptionsAsDisabled?: boolean;

    dataTestId?: string;

    showNoOptionsMessage?: boolean;
} & Pick<
        ImplementationProps<Value, Multiple>,
        | 'onChange'
        | 'onBlur'
        | 'isClearable'
        | 'isSearchable'
        | 'filterOption'
        | 'name'
        | 'menuIsOpen'
        | 'menuPlacement'
        | 'menuPosition'
        | 'closeMenuOnSelect'
        | 'placeholder'
    >;

const NoopComponent: React.ComponentType<any> = () => <></>;

const Dropdown = forwardRef(DropdownInner) as <Value = any, Multiple extends boolean = false>(
    props: DropdownProps<Value, Multiple> & { ref?: ForwardedRef<HTMLSelectElement> }
) => ReturnType<typeof DropdownInner>;

function DropdownInner<Value = any, Multiple extends boolean = false>(
    {
        id,
        label,
        tooltipText,
        isInvalid,
        isRequired,
        showValidationIndicators,
        disabled = false,
        CustomMenu,
        CustomHeaderMenu,
        CustomFooterMenu,
        CustomOption,
        CustomDropdownIndicator,
        CustomGroup,
        creatable = false,
        options,
        onChange,
        onBlur,
        isSearchable = false,
        isClearable = false,
        onInputChange,
        fullWidth = true,
        extraProps,
        filterOption,
        name,
        showValueCounterAfter,
        onClose,
        onOpen,
        value,
        size = 'small',
        displayOptionsAsDisabled,
        showDisabledStyles = false,

        classes = {},
        Icon,
        styles = {},
        menuIsOpen,
        menuPlacement,
        menuPosition = 'fixed',
        closeMenuOnSelect,
        placeholder,
        optionStyle = OptionStyle.text,
        iconSize,
        slotProps = {},
        showLoader,
        //loaderRightPosition = 34,
        onEnterPress,
        autoFocus,
        getOptionValue,
        cmosVariant,
        multiple,
        openMenuOnFocus,
        dataTestId,
        showNoOptionsMessage = true,
    }: DropdownProps<Value, Multiple>,
    ref: ForwardedRef<HTMLSelectElement>
) {
    const { t } = useAppTranslation();

    const handleChange: typeof onChange = (newValue, action) => {
        console.debug(newValue, action);
        if (onChange) onChange(newValue, action);
    };

    const handleBlur: typeof onBlur = (e) => {
        if (onBlur) onBlur(e);
    };

    const componentsConfig: ImplementationComponents<Value, Multiple> = {
        Group: CustomGroup ?? Group,
        SingleValue: SingleValue,
        MultiValue: NoopComponent,
        Menu: DropdownMenuComponent,
        MenuList: DropdownMenuListComponent,
        Option: DropdownOptionComponent,
        Control: DropdownControlComponent,
        ValueContainer: DropdownValueContainerComponent,
        Placeholder: DropdownPlaceholder,
        LoadingIndicator: DropdownLoadingIndicator,
        DropdownIndicator: DropdownIndicator,
        SelectContainer: DropdownSelectContainer,
    };

    const handleInputChange = (newValue: string) => {
        if (onInputChange) onInputChange(newValue);
    };

    const inputWrapperProps = {
        isRequired,
        isInvalid,
        showValidationIndicators,
        fullWidth,
        disabled,
        label,
        tooltipText,
    };

    const sharedProps: DropdownSharedProps<Value, Multiple> = {
        CustomHeaderMenu,
        CustomFooterMenu,
        CustomMenu,
        CustomOption,
        cmosVariant,
        showValueCounterAfter,
        optionStyle,
        iconSize,
        Icon,
        CustomDropdownIndicator,
        displayOptionsAsDisabled,
    };

    const selectProps: Partial<ImplementationProps<Value, Multiple>> = {
        'data-test-id': dataTestId,
        getOptionValue:
            getOptionValue ??
            ((x) => {
                switch (typeof x) {
                    case 'string':
                    case 'number':
                        // string and number are supported
                        return x;
                    case 'boolean':
                    case 'bigint':
                    case 'symbol':
                        return `${x}`;
                    case 'undefined':
                        // normally undefined enabled uncontrolled mode, dropdown always works in controlled mode so must replace undefined with empty string
                        return '';
                    case 'object':
                    case 'function':
                        if (x === null) return '';
                        // NOTE (MB) react-select does the same thing so it's fine
                        // https://github.com/JedWatson/react-select/blob/06e34882638d1526b9f5a1238bb567a3e9460ce5/packages/react-select/src/builtins.ts#L10
                        return x as unknown as string;
                }
            }),
        id,
        ref,
        filterOption,
        isClearable,
        isSearchable: creatable || isSearchable,
        name,
        components: componentsConfig,
        menuIsOpen,
        isDisabled: disabled,
        onInputChange: handleInputChange,
        optionStyle,
        noOptionsMessage: showNoOptionsMessage ? () => t('commonLabels.noDataSelector') : () => '',
        placeholder,
        menuPlacement,
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        menuPortalTarget: menuPosition === 'fixed' ? scuffedPortalContainer.get() : null,
        menuPosition,
        hideSelectedOptions: false,
        isLoading: showLoader,
        isMulti: multiple,
        onMenuOpen: onOpen,
        onMenuClose: onClose,
        onChange: handleChange,
        onBlur: handleBlur,
        onKeyDown: handleCommonKeyDowns({ onEnterPress }),
        closeMenuOnSelect: closeMenuOnSelect ?? !multiple,
        menuShouldBlockScroll: menuPosition === 'fixed',
        captureMenuScroll: true,
        options,
        value: value ?? (multiple ? [] : null),
        openMenuOnFocus,
        styles: {
            container: (base) => ({
                ...base,
                width: '100%',
            }),
            indicatorSeparator: () => ({ display: 'none' }),

            control: (base) => ({
                ...base,
                height: size === 'small' ? 32 : 40,
                minHeight: size === 'small' ? 32 : 40,
                ...styles.control,
            }),
            indicatorsContainer: (base) => ({
                ...base,
                height: size === 'small' ? 32 : 40,
                ...styles.indicatorsContainer,
                '& > svg, & > div > svg': {
                    color: disabled && showDisabledStyles ? 'rgba(0, 0, 0, 0.38)' : 'currentColor',
                },
            }),
            menuPortal: (base) => ({
                ...base,
                zIndex: 1310,
            }),
            input(base, _props) {
                return {
                    ...base,
                    margin: 0,
                    padding: 0,
                };
            },
            menu: (base) => ({ ...base, borderRadius: 10, ...styles.menu }),
            menuList: (base) => ({ ...base, ...styles.menuList }),
            noOptionsMessage: (base) => ({
                ...base,
                display: showNoOptionsMessage ? 'block' : 'none',
            }),
            valueContainer: (base) => ({
                ...base,
                paddingLeft: '14px',
                flexWrap: 'nowrap',
                fontWeight: 'normal',
                '& > span, & > div > span ': {
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    color: disabled && showDisabledStyles ? 'rgba(0, 0, 0, 0.38)' : 'currentColor',
                },
            }),
            option: (base) => ({ ...styles.option }),
        },

        ...extraProps,
        [DROPDOWN_SHARED_PROPS_KEY]: sharedProps,
    };
    return (
        <InputWrapper
            classes={{ label: classes.label }}
            {...inputWrapperProps}
            {...slotProps.inputWrapper}
        >
            {creatable ? (
                <Creatable
                    ref={(el) => {
                        if (el && autoFocus) el.focus();
                    }}
                    {...selectProps}
                />
            ) : (
                <Select
                    ref={(el) => {
                        if (el && autoFocus) el.focus();
                    }}
                    {...selectProps}
                />
            )}
        </InputWrapper>
    );
}

export type DropdownSharedProps<Value, Multiple extends boolean> = Pick<
    DropdownProps<Value, Multiple>,
    | 'CustomHeaderMenu'
    | 'CustomFooterMenu'
    | 'CustomMenu'
    | 'CustomOption'
    | 'cmosVariant'
    | 'showValueCounterAfter'
    | 'optionStyle'
    | 'iconSize'
    | 'Icon'
    | 'CustomDropdownIndicator'
    | 'displayOptionsAsDisabled'
>;

// this module creates a portal container for all fixed dropdowns
// that is invisible when empty and visible when contains any elements inside of it
// it's scuffed (hence the name) and I am not proud of it, but it works
module scuffedPortalContainer {
    let portalContainer: HTMLElement | null = null;

    // I kinda hate this, but it works, so....
    export function get(): HTMLElement {
        if (!portalContainer) {
            portalContainer = document.createElement('div');
            portalContainer.id = 'scuffed-portal';

            document.body.appendChild(portalContainer);
        }

        return portalContainer;
    }
}

export default Dropdown;
