import {
    Box,
    CSSInterpolation,
    CircularProgress,
    List,
    Theme,
    listClasses,
    menuItemClasses,
    styled,
    useForkRef,
} from '@mui/material';
import {
    GroupTypeBase,
    IndicatorProps,
    MenuListComponentProps,
    MenuProps,
    OptionProps,
    ValueContainerProps,
    components,
} from 'react-select';
import { CmosDropdownVariant } from '.';
import { getSharedProps } from './util';

import { ExpandMore } from '@mui/icons-material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { CheckCircleIcon } from 'common/components/Icons/CheckCircleIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { UncheckedCircleIcon } from 'common/components/Icons/UncheckedCircleIcon';
import { SMenuItem } from 'common/components/mui';
import { rgba } from 'common/styles/ColorHelpers';
import { IconSize } from 'common/styles/IconSize';
import { OptionStyle } from 'common/styles/OptionStyle';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import React, { Children, useEffect, useRef } from 'react';
import { useDebugAssertionMustNotChange } from 'utils/asserts';
import { OptionData } from './DataOption';

export const DropdownSelectContainer: typeof components.SelectContainer = (props) => {
    return (
        <components.SelectContainer
            {...props}
            innerProps={
                {
                    'data-test-id': props.selectProps['data-test-id'],
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                } as any
            }
        />
    );
};

module control {
    const ROUNDED_CSS: CSSInterpolation = {
        '--dropdown-border-radius': '100px',
    };

    function getVariantCss(
        theme: Theme,
        cmosVariant: CmosDropdownVariant | undefined
    ): CSSInterpolation {
        switch (cmosVariant) {
            case 'roundedPrimary':
                return [
                    ROUNDED_CSS,
                    {
                        '--dropdown-main-color': theme.palette.primary.main,
                        '--dropdown-text-color': theme.palette.primary.main,
                        '--dropdown-font-weight': 'bold',
                        '--dropdown-background-hover-color': rgba(theme.palette.primary.main, 0.2),
                    },
                ];
            case 'roundedPrimaryFilled':
                return [
                    ROUNDED_CSS,
                    {
                        '--dropdown-main-color': theme.palette.primary.main,
                        '--dropdown-background-color': 'var(--cm5)',
                        '--dropdown-text-color': theme.palette.primary.main,
                        '--dropdown-font-weight': 'bold',
                        '--dropdown-background-hover-color': rgba(theme.palette.primary.main, 0.2),
                    },
                ];
            case 'roundedGrey':
                return [
                    ROUNDED_CSS,
                    {
                        '--dropdown-background-color': theme.palette.neutral[2],
                        '--dropdown-main-color': theme.palette.neutral[5],
                        '--dropdown-background-hover-color': rgba(theme.palette.primary.main, 0.2),
                    },
                ];
            case 'transparent':
                return {
                    '--dropdown-background-color': 'transparent',
                    borderColor: 'transparent !important',
                };
            case 'default':
                return {
                    '--dropdown-main-color': theme.palette.neutral[5],
                };
            case 'grey':
            default: //TODO (AP) That's actually should be "Same as 'default'" case, not the "Same as 'gray'"
                return {
                    '--dropdown-background-color': theme.palette.neutral[2],
                    '--dropdown-main-color': theme.palette.neutral[5],
                };
            case 'lightGray':
                return [
                    {
                        '--dropdown-main-color': theme.palette.neutral[6],
                        '--dropdown-text-color': theme.palette.neutral[8],
                    },
                ];
        }
    }

    const RootContainer = styled('div', {
        shouldForwardProp: (prop) => {
            return !['cmosVariant', 'isFocused', 'sx'].includes(prop as string);
        },
    })<{ cmosVariant?: CmosDropdownVariant; isFocused: boolean }>(
        ({ theme, cmosVariant, isFocused }) => {
            const base: CSSInterpolation = {
                display: 'flex',
                alignItems: 'stretch',
                '--dropdown-border-radius': '4px',
                '--dropdown-background-color': theme.palette.background.paper,
                '--dropdown-text-color': theme.palette.common.black,
                '--dropdown-font-weight': 'normal',

                borderRadius: 'var(--dropdown-border-radius) !important',
                backgroundColor: 'var(--dropdown-background-color) !important',
                border: '1px solid var(--dropdown-main-color) !important',

                color: 'var(--dropdown-text-color) !important',
                cursor: 'pointer !important',
                height: '100%',

                '&:hover': {
                    borderColor: isFocused ? undefined : `${theme.palette.primary.main} !important`,
                    backgroundColor: 'var(--dropdown-background-hover-color) !important',
                },
            };

            return [
                base,
                getVariantCss(theme, cmosVariant),
                isFocused
                    ? {
                          boxShadow: `0 0 0 1px ${theme.palette.primary.main} inset !important`,
                          borderColor: `${theme.palette.primary.main} !important`,
                      }
                    : undefined,
            ];
        }
    );

    export const DropdownControlComponent: typeof components.Control = (props) => {
        const { children, selectProps, innerProps, innerRef, getStyles, isFocused } = props;
        const { cmosVariant } = getSharedProps(selectProps);
        return (
            <RootContainer
                ref={innerRef}
                isFocused={isFocused}
                sx={getStyles('control', props)}
                {...innerProps}
                cmosVariant={cmosVariant}
            >
                {children}
            </RootContainer>
        );
    };
}

module menu {
    export const DropdownMenuComponent = <Value, IsMulti extends boolean>({
        innerRef,
        ...props
    }: MenuProps<OptionData<Value>, IsMulti, GroupTypeBase<OptionData<Value>>>) => {
        const ref = useRef<HTMLElement | null>();
        const forkRef = useForkRef(innerRef, ref);

        useEffect(() => {
            const el = ref.current;
            if (!el) return;
            el.style.transform = 'scale(0.95)';
            el.style.opacity = '0';
            setTimeout(() => {
                el.style.transition = '.1s';
                el.style.opacity = '1';
                el.style.transform = 'scale(1)';
            }, 0);
        }, []);

        return <components.Menu innerRef={forkRef} {...props} />;
    };
}

module valueContainer {
    const Root = styled('div')(({ theme }) => ({
        overflow: 'hidden',
        alignItems: 'center',
        flex: 1,
        paddingLeft: 12,
        marginRight: 4,
        height: '100%',
        display: 'grid',
        gridTemplateRows: '0px 1fr',
        whiteSpace: 'nowrap',
        ...theme.typography.h6Roboto,
    }));

    export const DropdownValueContainerComponent = <Value, Multiple extends boolean>(
        props: ValueContainerProps<OptionData<Value>, Multiple, GroupTypeBase<OptionData<Value>>>
    ) => {
        const { selectProps, getValue, children } = props;

        useDebugAssertionMustNotChange(selectProps.isMulti);

        // for single value dropdowns use default value container
        // NOTE this will cause weird focus issue if dropdown changes from multiple to single or the other way around
        // but that is not supposed to happen normally
        if (!selectProps.isMulti) {
            if (selectProps.isDisabled) {
                return (
                    <div style={{ opacity: 0.6 }}>
                        <components.ValueContainer {...props} />
                    </div>
                );
            }

            return <components.ValueContainer {...props} />;
        }

        const value = getValue();
        const items: Readonly<{ label: string }[]> = value instanceof Array ? value : [value];
        const { showValueCounterAfter } = getSharedProps(selectProps);

        const displayedValues =
            showValueCounterAfter && items.length > showValueCounterAfter
                ? items.slice(0, showValueCounterAfter)
                : items;
        const showCounter = displayedValues.length < items.length;

        let valueLabel: React.ReactNode;

        if (items.length === 0) {
            valueLabel = undefined;
        } else {
            let valuesLabelStr = displayedValues.map((x) => x.label).join(', ');
            if (valuesLabelStr && showCounter && showValueCounterAfter)
                valuesLabelStr += ` +${items.length - showValueCounterAfter}`;
            valueLabel = valuesLabelStr;
        }

        let placeholder: React.ReactNode = undefined;

        if (selectProps.placeholder && !selectProps.inputValue && items.length === 0) {
            placeholder = selectProps.placeholder;
        }

        const childrenArray = Children.toArray(children);

        // NOTE (MB) react-select always passed two children components to ValueContainer
        // last one is always the "dummy" input. Dummy input must be displayed, otherwise open on focus/click won't work properly
        const dummyInputElement = childrenArray[childrenArray.length - 1];

        return (
            <Root
                style={selectProps.isDisabled ? { opacity: 0.6 } : undefined}
                sx={props.getStyles('valueContainer', props)}
            >
                {valueLabel && <span>{valueLabel}</span>}
                &nbsp;
                {dummyInputElement}
                {placeholder}
            </Root>
        );
    };
}

module menuList {
    const CustomList = styled(List)(({ theme }) => ({
        [`& .${menuItemClasses.root}:not(:last-child)`]: {
            borderBottom: `1px solid ${theme.palette.neutral[3]}`,
        },

        [`& .${listClasses.root}`]: {
            borderRadius: 10,
        },

        paddingBottom: 0,
        ...scrollbarStyle(),
        overflow: 'auto',

        // backgroundColor: theme.palette.neutral[2], // matches our design system but looks kinda ugly
    }));

    const CustomFooterMenuContainer = styled('div')({
        position: 'sticky',
        bottom: 0,
        borderRadius: 10,
        height: '100%',
        background: 'white',
        padding: '10px 12px',
    });

    export const DropdownMenuListComponent = <Value, IsMulti extends boolean>(
        props: MenuListComponentProps<OptionData<Value>, IsMulti, GroupTypeBase<OptionData<Value>>>
    ): JSX.Element => {
        const { children, innerRef, selectProps, maxHeight, ...otherProps } = props;
        const { CustomHeaderMenu, CustomMenu, CustomFooterMenu } = getSharedProps<Value, IsMulti>(
            selectProps
        );

        if (CustomMenu) return <CustomMenu {...props} />;

        return (
            <CustomList sx={{ maxHeight }} ref={innerRef}>
                {CustomHeaderMenu && <CustomHeaderMenu selectProps={selectProps} {...otherProps} />}
                {children}
                {CustomFooterMenu && (
                    <CustomFooterMenuContainer>
                        <CustomFooterMenu selectProps={selectProps} {...otherProps} />
                    </CustomFooterMenuContainer>
                )}
            </CustomList>
        );
    };
}

module option {
    const StartAdornment = styled('div')({
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start',
        marginRight: 4,
        width: 24,
    });

    const DropdownMenuItem = styled(SMenuItem)({
        overflow: 'hidden',
        textWrap: 'wrap',
        whiteSpace: 'wrap',
    }) as typeof SMenuItem;

    export const DropdownOptionComponent = <Value, IsMulti extends boolean>(
        props: OptionProps<OptionData<Value>, IsMulti, GroupTypeBase<OptionData<Value>>>
    ) => {
        const { children, innerProps, isDisabled, isSelected, selectProps, innerRef, data } = props;
        const {
            CustomOption,
            optionStyle,
            iconSize,
            displayOptionsAsDisabled = false,
        } = getSharedProps<Value, IsMulti>(selectProps);

        if (CustomOption) return <CustomOption {...props} />;

        const optionData = data as OptionData<Value>;

        let startAdornment: React.ReactNode = undefined;

        const disableOption = !isSelected && displayOptionsAsDisabled;
        const style = disableOption ? { opacity: 0.5 } : undefined;

        if (optionStyle) {
            switch (optionStyle) {
                case OptionStyle.radio:
                    startAdornment = (
                        <StartAdornment>
                            {isSelected ? (
                                <CheckCircleIcon style={style} />
                            ) : (
                                <UncheckedCircleIcon style={style} />
                            )}
                        </StartAdornment>
                    );
                    break;
                case OptionStyle.checkbox:
                    startAdornment = (
                        <StartAdornment>
                            {isSelected ? (
                                <CheckBoxIcon style={style} />
                            ) : (
                                <UncheckBoxIcon style={style} />
                            )}
                        </StartAdornment>
                    );
                    break;
                case OptionStyle.icons:
                    const IconComponent = optionData.icon;
                    const color = optionData.color ?? undefined;
                    startAdornment = (
                        <StartAdornment
                            style={iconSize ? { width: iconSize, height: iconSize } : undefined}
                        >
                            {IconComponent && (
                                <IconComponent style={style} size={18} fill={color} />
                            )}
                        </StartAdornment>
                    );
                    break;
            }
        }

        return (
            <DropdownMenuItem
                sx={props.getStyles('option', props)}
                ref={innerRef}
                disabled={isDisabled}
                component="div"
                selected={isSelected}
                style={disableOption ? { pointerEvents: 'none' } : undefined}
                {...innerProps}
            >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {startAdornment}
                    <div style={style}>{children}</div>
                </Box>
            </DropdownMenuItem>
        );
    };
}

module placeholder {
    const Placeholder = styled('span')<{ focused: boolean; disabled: boolean }>(
        ({ focused, disabled, theme }) => ({
            ...theme.typography.h6Roboto,
            fontWeight: 'normal',
            opacity: 0.75,
        })
    );

    export const DropdownPlaceholder: typeof components.Placeholder = ({
        isDisabled,
        innerProps,
        isFocused,
        selectProps,
    }) => {
        return (
            <Placeholder disabled={isDisabled} focused={isFocused} {...innerProps}>
                {selectProps.placeholder}
            </Placeholder>
        );
    };
}

module indicator {
    export const DropdownLoadingIndicator: typeof components.LoadingIndicator = (_props) => {
        return <CircularProgress sx={{ marginRight: 0.75 }} size={16} />;
    };

    export const DropdownIndicator = <Value, IsMulti extends boolean>(
        props: IndicatorProps<OptionData<Value>, IsMulti>
    ) => {
        const { CustomDropdownIndicator } = getSharedProps<Value, IsMulti>(props.selectProps);

        if (CustomDropdownIndicator) return <CustomDropdownIndicator {...props} />;

        return (
            <ExpandMore
                style={{ transform: `rotate(${props.selectProps.menuIsOpen ? '180deg' : '0deg'})` }}
                sx={{
                    transition: 'transform .3s',
                    margin: '0 5px',
                }}
            />
        );
    };
}

module singleValue {
    const Root = styled('div')(({ theme }) => ({
        overflow: 'hidden',
        maxWidth: '97%',
        display: 'flex',
        alignItems: 'center',
        color: 'var(--dropdown-text-color)',
        ...theme.typography.h6Roboto,
        fontWeight: 'var(--dropdown-font-weight)',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
    }));

    export const SingleValue = ({ selectProps, getValue, hasValue, data, ...props }: any) => {
        const arrayValue = hasValue ? getValue() : [];
        const { multiple, optionStyle, formatOptionLabel } = selectProps;
        const value = multiple ? arrayValue : arrayValue[0];

        return (
            <Root>
                {!multiple && hasValue && optionStyle === OptionStyle.icons && value?.icon ? (
                    <Box display={'flex'} marginRight={1}>
                        <value.icon size={IconSize.S} fill={value?.color} />
                    </Box>
                ) : null}
                <span>
                    {formatOptionLabel
                        ? formatOptionLabel()
                        : multiple && Array.isArray(value)
                        ? value.map(({ label }: any) => label).join(', ')
                        : props.children}
                </span>
            </Root>
        );
    };
}

export const DropdownValueContainerComponent = valueContainer.DropdownValueContainerComponent;
export const DropdownControlComponent = control.DropdownControlComponent;
export const DropdownOptionComponent = option.DropdownOptionComponent;
export const DropdownMenuListComponent = menuList.DropdownMenuListComponent;
export const DropdownMenuComponent = menu.DropdownMenuComponent;
export const DropdownPlaceholder = placeholder.DropdownPlaceholder;

export const DropdownLoadingIndicator = indicator.DropdownLoadingIndicator;
export const DropdownIndicator = indicator.DropdownIndicator;
export const SingleValue = singleValue.SingleValue;
