import { DropdownSharedProps } from '.';

export const DROPDOWN_SHARED_PROPS_KEY = '_sharedProps';

export function getSharedProps<Value, Multiple extends boolean>(
    props: Record<string, any>
): DropdownSharedProps<Value, Multiple> {
    const value = props[DROPDOWN_SHARED_PROPS_KEY];
    if (typeof value !== 'object' || value === null)
        throw new Error('failed to get _sharedProps, _sharedProps prop must be passed to Dropdown');

    return value;
}
