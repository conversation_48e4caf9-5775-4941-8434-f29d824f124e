import { Divider } from '@mui/material';
import { Colors } from '../../../../styles/Colors';
import { FontPrimary } from '../../../../styles/FontHelper';
import { HeaderStyles } from '../../../../styles/HeaderStyles';
export default function WrapperOption({ children, isDivider, title, isTitle }: any) {
    if (isDivider) {
        return <Divider style={{ margin: '4px 0' }} />;
    } else if (isTitle) {
        return (
            <span
                style={{
                    ...FontPrimary(HeaderStyles.H6_12px, true, Colors.Neutral7),
                    display: 'flex',
                    alignItems: 'center',
                    borderBottom: 'solid 0.0px #e5e7ea',
                    color: Colors.Neutral7,
                    fontWeight: 500,
                    padding: '2.5px 0 2.5px 0',
                }}
            >
                <strong>{title}</strong>
            </span>
        );
    } else {
        return children;
    }
}
