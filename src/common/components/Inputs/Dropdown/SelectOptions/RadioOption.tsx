import clsx from 'clsx';
import { useState } from 'react';
import Option from 'react-select/src/components/Option';
import { IconSize } from '../../../../styles/IconSize';
import { CheckCircleIcon } from '../../../Icons/CheckCircleIcon';
import { UncheckedCircleIcon } from '../../../Icons/UncheckedCircleIcon';
import { MarkedText } from '../../../MarkedText';
import WrapperOption from './WrapperOption';
import { DivContainer, DivLabel, DivRoot } from './common';

const SelectedIcon = <CheckCircleIcon size={IconSize.M} />;

const UnSelectedIcon = <UncheckedCircleIcon size={IconSize.M} />;

export const RadioOption: typeof Option = ({
    innerRef,
    innerProps,
    label,
    selectProps,
    options,
    isSelected,
    ...props
}) => {
    const { inputValue } = selectProps;
    const [Icon, setIcon] = useState(isSelected ? SelectedIcon : UnSelectedIcon);

    const onMouseOver = () => setIcon(SelectedIcon);
    const onMouseOut = () => (!isSelected ? setIcon(UnSelectedIcon) : '');
    return (
        <WrapperOption {...props.data}>
            <DivRoot
                ref={innerRef}
                {...innerProps}
                className={clsx(isSelected ? 'isSelected' : '', 'boldSelected')}
                onMouseOver={onMouseOver}
                onMouseOut={onMouseOut}
            >
                <DivContainer>
                    {Icon}
                    <DivLabel>
                        <MarkedText text={label} markText={inputValue ?? ''} />
                    </DivLabel>
                </DivContainer>
            </DivRoot>
        </WrapperOption>
    );
};
