import clsx from 'clsx';
import { useEffect, useState } from 'react';
import Option from 'react-select/src/components/Option';
import { IconSize } from '../../../../styles/IconSize';
import { CheckBoxIcon } from '../../../Icons/CheckBoxIcon';
import { UncheckBoxIcon } from '../../../Icons/UncheckBoxIcon';
import { MarkedText } from '../../../MarkedText';
import WrapperOption from './WrapperOption';
import { DivContainer, DivLabel, DivRoot } from './common';

const SelectedIcon = <CheckBoxIcon size={IconSize.M} />;

const UnSelectedIcon = <UncheckBoxIcon size={IconSize.M} />;

export const CheckBoxOption: typeof Option = ({
    innerRef,
    innerProps,
    label,
    selectProps,
    options,
    isSelected,
    isDisabled,
    ...props
}) => {
    const { inputValue } = selectProps;
    const [Icon, setIcon] = useState(isSelected ? SelectedIcon : UnSelectedIcon);

    const onMouseOver = () => setIcon(SelectedIcon);
    const onMouseOut = () => (!isSelected ? setIcon(UnSelectedIcon) : '');

    useEffect(() => {
        setIcon(isSelected ? SelectedIcon : UnSelectedIcon);
    }, [isSelected]);

    return (
        <>
            {isDisabled ? (
                <WrapperOption {...props}>
                    <DivRoot
                        ref={innerRef}
                        {...innerProps}
                        className={isSelected ? 'isSelected' : undefined}
                    >
                        <DivContainer>
                            <DivLabel>
                                <MarkedText text={label} markText={inputValue ?? ''} />
                            </DivLabel>
                        </DivContainer>
                    </DivRoot>
                </WrapperOption>
            ) : (
                <WrapperOption {...props.data}>
                    <DivRoot
                        ref={innerRef}
                        {...innerProps}
                        className={clsx(isSelected && 'isSelected', 'boldSelected')}
                        onMouseOver={onMouseOver}
                        onMouseOut={onMouseOut}
                    >
                        <DivContainer>
                            {Icon}
                            <DivLabel>
                                <MarkedText text={label} markText={inputValue ?? ''} />
                            </DivLabel>
                        </DivContainer>
                    </DivRoot>
                </WrapperOption>
            )}
        </>
    );
};
