import Option from 'react-select/src/components/Option';
import { MarkedText } from '../../../MarkedText';
import WrapperOption from './WrapperOption';
import { DivContainer, DivLabel, DivRoot } from './common';

export const TextOption: typeof Option = (props) => {
    const { innerRef, innerProps, label, selectProps, isSelected } = props;
    const { inputValue } = selectProps;
    return (
        <WrapperOption {...props}>
            <DivRoot
                ref={innerRef}
                {...innerProps}
                className={isSelected ? 'isSelected' : undefined}
            >
                <DivContainer>
                    <DivLabel>
                        <MarkedText text={label} markText={inputValue ?? ''} />
                    </DivLabel>
                </DivContainer>
            </DivRoot>
        </WrapperOption>
    );
};
