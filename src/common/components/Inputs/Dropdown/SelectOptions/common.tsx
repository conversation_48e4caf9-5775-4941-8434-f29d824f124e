import { styled } from '@mui/material';

export const DivRoot = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    color: theme.palette.neutral[7],

    '&.isSelected': {
        color: theme.palette.primary.main,
    },

    '&.isSelected.boldSelected': {
        fontWeight: 700,
    },

    '&:hover': {
        background: 'var(--cm5)',
    },

    '&:last-child > $container': {
        borderBottom: 'none',
    },
}));

export const DivContainer = styled('div')(({ theme }) => ({
    cursor: 'pointer',
    marginLeft: 16,
    minHeight: 38,
    display: 'flex',
    alignItems: 'center',
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
}));

export const DivLabel = styled('div')({
    paddingLeft: 14,
    marginTop: 3,
    marginBottom: 3,
    height: 'calc(100% - 6px)',
    boxSizing: 'border-box',
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
});
