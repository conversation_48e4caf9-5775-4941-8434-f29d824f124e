import Option from 'react-select/src/components/Option';
import { IconSize } from '../../../../styles/IconSize';
import { MarkedText } from '../../../MarkedText';
import WrapperOption from './WrapperOption';
import { DivContainer, DivLabel, DivRoot } from './common';

export const IconOption: typeof Option = ({
    innerRef,
    innerProps,
    selectProps,
    data,
    options,
    isSelected,
    ...props
}) => {
    const { label, icon: Icon, color } = data;
    const { inputValue } = selectProps;
    return (
        <WrapperOption {...props}>
            <DivRoot
                ref={innerRef}
                {...innerProps}
                className={isSelected ? 'isSelected' : undefined}
            >
                <DivContainer>
                    {Icon ? <Icon size={IconSize.S} fill={color ? color : 'var(--cm1)'} /> : ''}
                    <DivLabel>
                        <MarkedText text={label} markText={inputValue ?? ''} />
                    </DivLabel>
                </DivContainer>
            </DivRoot>
        </WrapperOption>
    );
};
