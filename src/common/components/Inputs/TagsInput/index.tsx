import { Close } from '@mui/icons-material';
import { Chip, styled, SxProps, Theme, useForkRef } from '@mui/material';
import React, { forwardRef, useCallback, useRef, useState } from 'react';

type TagsInputProps = {
    onAdded: (value: string) => void;
    children?: React.ReactNode;
    disabled?: boolean;
    slotProps?: {
        input?: React.InputHTMLAttributes<HTMLInputElement>;
    };
    sx?: SxProps<Theme>;
    inputRef?: React.Ref<HTMLInputElement>;
};

const Root = styled('div')(({ theme }) => ({
    border: '1px solid var(--neutral4)',
    borderRadius: 5,
    padding: '5px 7px',
    display: 'flex',
    flexWrap: 'wrap',
    gap: 4,
    minHeight: 60,
    position: 'relative',
    '--input-row-height': '25px',

    '&:hover': {
        borderColor: 'var(--cm1)',
    },

    '&[aria-disabled=true]': {
        backgroundColor: 'var(--neutral2)',
        borderColor: 'var(--neutral3)',
    },

    '&:has(input:focus)': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 1px ${theme.palette.primary.main}`,
    },
}));

const Input = styled('input')(({ theme }) => ({
    border: 'none',
    outline: 'none !important',
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    height: 'var(--input-row-height)',
    backgroundColor: 'transparent',
}));

const TagsInput = forwardRef(
    (
        { children, onAdded, disabled, sx, slotProps = {}, inputRef: inputRefProp }: TagsInputProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const inputRef = useRef<HTMLInputElement | null>(null);
        const inputForkRef = useForkRef(inputRef, inputRefProp);
        const [inputValue, setInputValue] = useState('');

        const handleKeyDown = useCallback(
            (e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') {
                    if (e.target instanceof HTMLInputElement) {
                        const newTag = e.target.value.trim();
                        if (newTag) {
                            onAdded(newTag);
                            setInputValue('');
                        }
                    }
                }
            },
            [onAdded]
        );

        const handleRootClick = useCallback(
            (e: React.MouseEvent<HTMLElement>) => {
                if (disabled) return;

                if (e.target === e.currentTarget) {
                    if (inputRef.current) {
                        inputRef.current.focus();
                    }
                }
            },
            [disabled]
        );

        return (
            <Root
                ref={ref}
                onClick={handleRootClick}
                role="combobox"
                aria-disabled={disabled}
                sx={sx}
            >
                {children}
                <Input
                    ref={inputForkRef}
                    value={inputValue}
                    disabled={disabled}
                    onChange={(e) => setInputValue(e.target.value)}
                    {...slotProps.input}
                    onKeyDown={combineEventHandlers(handleKeyDown, slotProps.input?.onKeyDown)}
                />
            </Root>
        );
    }
);

function combineEventHandlers<Args extends unknown[]>(
    ...handlers: (((...args: Args) => void) | null | undefined)[]
): (...args: Args) => void {
    return (...args: Args) => {
        handlers.forEach((h) => {
            if (h) h(...args);
        });
    };
}

export default TagsInput;

export type TagProps = {
    label?: string;
    onRemove: () => void;
    className?: string;
};

const SChip = styled(Chip)(({ theme }) => ({
    height: 'var(--input-row-height, 25px)',
    backgroundColor: 'var(--neutral3)',
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));

export function Tag({ label, onRemove, className }: TagProps) {
    return <SChip className={className} deleteIcon={<Close />} label={label} onDelete={onRemove} />;
}
