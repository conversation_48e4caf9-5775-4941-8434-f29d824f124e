import {
    CircularProgress,
    IconButton,
    InputAdornment,
    inputBaseClasses,
    outlinedInputClasses,
    styled,
} from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { TextField } from 'common/components/mui';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import { TextFormFieldProps } from '../TextField';

export type DragAndDropEditableTextFieldProps = Pick<TextFormFieldProps, 'onChange'> & {
    isDraggable?: boolean;
    value?: string;
    onSaveClick?: React.MouseEventHandler;
    dragAndDropProps?: DraggableProvidedDragHandleProps;
    showLoader?: boolean;
    disabled?: boolean;
    isEditMode?: boolean;
};

const TransparentTextField = styled(TextField)(({ theme, disabled }) => ({
    flexGrow: 1,
    height: 40,

    [`& .${inputBaseClasses.root}`]: {
        border: 'none',
        background: 'transparent',
    },

    [`& .${outlinedInputClasses.notchedOutline}`]: {
        border: 'none',
    },
}));

const Container = styled('div')<{ focused: boolean }>(({ theme, focused }) => ({
    display: 'flex',
    alignItems: 'center',
    background: theme.palette.background.paper,
    width: '100%',
    borderRadius: 10,
    border: `1px solid ${focused ? theme.palette.primary.main : theme.palette.neutral[5]}`,
    boxShadow: focused ? `0 0 0 2px ${rgba(theme.palette.primary.main, 0.3)}` : undefined,
    '&:hover': {
        backgroundColor: theme.palette.neutral[3],
    },
}));

const DragHandle = styled('div')({
    padding: 5,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
});

export function DragAndDropEditableTextField({
    isEditMode = false,
    disabled = false,
    isDraggable = true,
    onSaveClick,
    dragAndDropProps,
    value = '',
    onChange,
    showLoader,
}: DragAndDropEditableTextFieldProps) {
    return (
        <Container focused={isEditMode}>
            <DragHandle
                {...dragAndDropProps}
                sx={{ visibility: !isDraggable ? 'hidden' : undefined }}
            >
                <DragAndDropIcon size={IconSize.M} fill={Colors.Neutral7} />
            </DragHandle>
            <TransparentTextField
                disabled={disabled}
                onChange={onChange}
                value={value}
                InputProps={{
                    readOnly: !isEditMode,
                    endAdornment: (
                        <InputAdornment position="end">
                            {showLoader ? (
                                <CircularProgress size={16} />
                            ) : isEditMode ? (
                                <IconButton onClick={onSaveClick} size="small">
                                    <CheckIcon />
                                </IconButton>
                            ) : undefined}
                        </InputAdornment>
                    ),
                }}
            />
        </Container>
    );
}
