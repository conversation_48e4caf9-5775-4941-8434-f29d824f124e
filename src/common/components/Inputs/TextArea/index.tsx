import clsx from 'clsx';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React from 'react';
import { InputOrientation } from '../input.styles';
import styles from './styles.module.css';

export type TextAreaProps = MergeTypes<
    {
        value: string; //override
        textAreaClassName?: string;
        labelClassName?: string;
        label?: string;
        isRequired?: boolean;
        hideLabel?: boolean;
        orientation?: InputOrientation;
        showValidationIndicators?: boolean;
        isInvalid?: boolean;
    },
    React.TextareaHTMLAttributes<HTMLTextAreaElement>
>;

export const TextArea = React.forwardRef(
    (
        {
            label,
            rows,
            className,
            orientation,
            hideLabel,
            isInvalid,
            value,
            name,
            isRequired,
            showValidationIndicators,
            labelClassName,
            textAreaClassName,
            style,
            ...props
        }: TextAreaProps,
        ref: React.ForwardedRef<HTMLTextAreaElement>
    ) => {
        const { t } = useAppTranslation();

        return (
            <div
                style={style}
                className={clsx(
                    styles.container,
                    orientation === 'row' ? styles.orientationRow : styles.orientationColumn,
                    className
                )}
            >
                {!hideLabel && (
                    <p
                        className={
                            labelClassName ? `${styles.label} ${labelClassName}` : styles.label
                        }
                    >
                        {label}
                        {showValidationIndicators &&
                            (isRequired ? (
                                <span className={styles.requiredLabel}> *</span>
                            ) : (
                                <span className={styles.optionalLabel}>
                                    {' '}
                                    {t('commonLabels.optional')}
                                </span>
                            ))}
                    </p>
                )}
                <div className={styles.inputContainer}>
                    <textarea
                        className={clsx(
                            styles.input,
                            isInvalid && styles.invalidInput,
                            textAreaClassName
                        )}
                        rows={rows}
                        name={name}
                        value={value ?? ''}
                        ref={ref}
                        {...props}
                    />
                    {isInvalid && <span className={styles.errorIcon} />}
                    {showValidationIndicators && isRequired && (
                        <div className={styles.requiredDot} />
                    )}
                </div>
            </div>
        );
    }
);
