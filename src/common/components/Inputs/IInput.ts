import React, { ChangeEvent as ChangeEventReact, ComponentType } from 'react';
import { InputTheme } from '../../styles/InputTheme';
import { IComponent } from '../IComponent';
import { IconProps } from '../Icons/Icon';
import { InputSize } from './InputSize';
import { InputValidation } from './InputValidation';
export interface ChangeEvent extends ChangeEventReact<HTMLInputElement> {
    isValid: boolean;
}

export type ChangeEventHandler = (event: React.ChangeEvent<HTMLInputElement>) => void;

// TODO (MB) rewrite this mess and use typings from react
/**
 * @deprecated do not use this type - it's too generic, many components use it but do not actually make use of all props in here which only creates confusion
 */
export interface IInput<TValue = any, TElement = HTMLInputElement> extends IComponent {
    IconRight?: ComponentType<IconProps>;
    IconLeft?: ComponentType<IconProps>;
    onIconRightClick?: React.MouseEventHandler;
    onIconLeftClick?: React.MouseEventHandler;
    CustomIconRight?: JSX.Element;
    CustomIconLeft?: JSX.Element;
    label?: string;
    readonly?: boolean;
    placeholder?: string;
    autoComplete?: string | undefined;
    onChange?: React.ChangeEventHandler<TElement>;
    onKeyDown?: React.KeyboardEventHandler<TElement>;

    value?: TValue;
    name?: string;
    orientation?: 'row' | 'column';
    // TODO (MB) switch to type `number | 'small' | 'medium' | 'auto'` here, InputSize is unnecessary
    size?: InputSize;
    hideLabel?: boolean;
    validation?: boolean; // Validation flag
    enableValidation?: boolean; //Show color state validation
    isRequired?: boolean; // enable required validation, label indicator and input indicator.
    showValidationIndicators?: boolean; // enable required or optional indicators
    state?: InputValidation;
    disabled?: boolean;
    theme?: InputTheme;

    /**
     * @deprecated
     */
    debug?: boolean;
}
