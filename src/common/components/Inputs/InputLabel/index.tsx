import { styled } from '@mui/material';
import React from 'react';

const InputLabel = React.forwardRef<HTMLLabelElement, React.HTMLAttributes<HTMLLabelElement>>(
    ({ className, ...props }, ref) => {
        return <StyledLabel className={className} ref={ref} {...props} />;
    }
);

const StyledLabel = styled('label')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
}));

export default InputLabel;
