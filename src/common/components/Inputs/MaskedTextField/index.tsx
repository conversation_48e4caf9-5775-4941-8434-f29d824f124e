import { CircularProgress } from '@mui/material';
import { IconProps } from 'common/components/Icons/Icon';
import { TextField, TextFieldProps } from 'common/components/mui';
import { Colors } from 'common/styles/Colors';
import React, { ComponentType, forwardRef } from 'react';
import MaskedInput from 'react-text-mask';
import { DataProps, splitDataProps } from 'utils/dataProps';
import { handleCommonKeyDowns } from '../utils';

export type MaskedTextFieldProps = {
    mask: Array<string | RegExp> | false;
    maskPlaceholderChar?: string;
    showMask?: boolean;
    showLoader?: boolean;
    readonly?: boolean;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    /**
     * @deprecated
     */
    IconRight?: ComponentType<IconProps>;
    inputRef?: React.Ref<HTMLInputElement>;

    slotProps?: {
        textField?: Partial<TextFieldProps>;
    };
    isInvalid?: boolean;
} & Pick<TextFieldProps['InputProps'] & {}, 'endAdornment' | 'startAdornment'> &
    Pick<
        TextFieldProps<string>,
        | 'onChange'
        | 'onBlur'
        | 'onFocus'
        | 'disabled'
        | 'id'
        | 'value'
        | 'onKeyDown'
        | 'name'
        | 'fullWidth'
        | 'cmosVariant'
        | 'placeholder'
    > &
    DataProps;

const MaskedTextField = forwardRef(
    (
        {
            // component specific props
            maskPlaceholderChar,
            mask,
            showMask,
            showLoader,
            IconRight,
            readonly,
            onEnterPress,
            onEscPress,
            // TextField props
            placeholder,
            endAdornment,
            startAdornment,
            onChange,
            onBlur,
            onFocus,
            value,
            disabled,
            id,
            onKeyDown,
            inputRef,
            name,
            cmosVariant,
            fullWidth, // TODO implement
            slotProps = {},
            isInvalid,
            ...otherProps
        }: MaskedTextFieldProps,
        forwardedRef: React.ForwardedRef<MaskedInput>
    ) => {
        const [, dataProps] = splitDataProps(otherProps);

        return (
            <MaskedInput
                id={id}
                ref={forwardedRef}
                mask={mask}
                placeholderChar={maskPlaceholderChar || '\u2000'}
                value={value ?? ''}
                disabled={disabled}
                readOnly={readonly}
                showMask={showMask}
                name={name}
                // onChange and onBlur must be passed to MaskedInput directly instead of TextField
                onChange={onChange}
                onBlur={onBlur}
                onFocus={onFocus}
                onKeyDown={handleCommonKeyDowns({ onEnterPress, onEscPress, onKeyDown })}
                placeholder={placeholder}
                render={(ref, props) => (
                    <TextField
                        InputProps={{
                            error: isInvalid,
                            startAdornment:
                                startAdornment ?? (IconRight && <IconRight fill={Colors.CM1} />),
                            endAdornment: showLoader ? (
                                <CircularProgress
                                    size={16}
                                    thickness={4}
                                    style={{ color: '#467CFC' }}
                                />
                            ) : (
                                endAdornment
                            ),
                        }}
                        fullWidth
                        cmosVariant={cmosVariant}
                        inputRef={(element) => {
                            ref(element);
                            if (inputRef) {
                                if (typeof inputRef === 'function') inputRef(element);
                                else (inputRef as { current: HTMLElement }).current = element;
                            }
                        }}
                        {...dataProps}
                        {...slotProps.textField}
                        {...props}
                    />
                )}
            />
        );
    }
);

export default MaskedTextField;
