import { FormLabel, FormLabelProps, formLabelClasses, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { memo } from 'react';
import InfoTooltip from '../InfoTooltip';

export type InputLabelProps = React.PropsWithChildren<{
    isRequired?: boolean;
    isInvalid?: boolean;
    className?: string;
    showValidationIndicators?: boolean;
    tooltipText?: string;
    htmlFor?: string;
    sx?: FormLabelProps['sx'];
    disabled?: boolean;
}>;

const CustomFormLabel = styled(FormLabel, {
    shouldForwardProp: (prop) => !['isInvalid'].includes(prop as string),
})<{ isInvalid: boolean }>(({ theme, isInvalid }) => ({
    ...theme.typography.h6Inter,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    textWrap: 'nowrap',
    color: isInvalid ? 'var(--danger)' : theme.palette.neutral[8],
    display: 'flex',
    alignItems: 'center',
    margin: '5px 0',
    height: '16px',

    [`&.${formLabelClasses.focused}`]: {
        // text color should remain unchanged when focused
        color: isInvalid ? 'var(--danger)' : theme.palette.neutral[8],
    },
}));

const OptionLabel = styled('span')({
    fontWeight: 300,
});

const RequiredLabel = styled('span')(({ theme }) => ({
    position: 'relative',
    top: '-4px',
    width: '8px',
    borderRadius: '32px',
    height: '8px',
    color: theme.palette.primary.light,
    zIndex: 2,
}));

export const InputLabel = memo(
    ({
        children,
        isRequired = false,
        isInvalid = false,
        className,
        showValidationIndicators = false,
        tooltipText,
        htmlFor,
        sx,
        disabled,
    }: InputLabelProps) => {
        const { t } = useAppTranslation();

        if (!children) return null;

        return (
            <CustomFormLabel
                htmlFor={htmlFor}
                sx={sx}
                isInvalid={isInvalid}
                className={className}
                disabled={disabled}
            >
                {children}
                {showValidationIndicators &&
                    (isRequired ? (
                        <RequiredLabel>&nbsp;*</RequiredLabel>
                    ) : (
                        <OptionLabel>&nbsp;{t('commonLabels.optional')}</OptionLabel>
                    ))}
                {tooltipText && (
                    <InfoTooltip onClick={(e) => e.preventDefault()} text={tooltipText} />
                )}
            </CustomFormLabel>
        );
    }
);
