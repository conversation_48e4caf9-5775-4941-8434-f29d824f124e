import { CSSObject } from '@emotion/react';
import { useTheme } from '@mui/material';
import { CmosTextFieldVariant } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import Select, { Props as SelectProps, ValueType, components } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { OptionElement, OptionStyle } from '../../../styles/OptionStyle';
import { OptionData } from '../Dropdown/DataOption';
import InputWrapper, { InputWrapperProps } from '../InputWrapper';

type ImplementationProps<Value> = SelectProps<OptionData<Value>, true>;

export type DropdownMultiProps<Value = unknown> = Pick<
    InputWrapperProps,
    'label' | 'isRequired' | 'isInvalid' | 'showValidationIndicators' | 'disabled' | 'fullWidth'
> & {
    className?: string;
    value?: ValueType<OptionData<Value>, true>;
    cmosVariant?: CmosTextFieldVariant;
    options: OptionData<Value>[];
    onCreate?: (option: OptionData<null>) => void;
    onSelect?: (option: OptionData<Value>) => void;
    onRemove?: (option: OptionData<Value>) => void;
    onInputChange?: (input: string) => void;
    onClose?: () => void;
    creatable?: boolean;
    placeholder?: string;

    CustomMenu?: typeof components.MenuList;
    styles?: {
        menu?: CSSObject;
        menuList?: CSSObject;
        control?: CSSObject;
    };

    slotProps?: {
        inputWrapper?: Partial<InputWrapperProps>;
    };
} & Pick<
        ImplementationProps<Value>,
        'onChange' | 'onBlur' | 'filterOption' | 'name' | 'menuIsOpen' | 'placeholder'
    >;

export function DropdownMulti<Value = unknown>({
    options,
    creatable,
    onCreate,
    onSelect,
    onInputChange,
    onRemove,
    className,
    slotProps = {},
    cmosVariant,
    CustomMenu,
    menuIsOpen,
    placeholder,
    filterOption,
    value,
    onClose,

    // InputWrapper
    showValidationIndicators,
    isInvalid,
    isRequired,
    label,
    disabled,
    fullWidth,
    styles = {},
}: DropdownMultiProps<Value>) {
    const optionStyle = OptionStyle.text;
    const theme = useTheme();

    // const {
    //     options,
    //     placeholder,
    //     theme = InputTheme.rounded,
    //     styles,
    //     size = InputSize.Auto,
    //     validation,
    //     enableValidation,
    //     disabled,
    //     value,
    //     isRequired,
    //     multiple,
    //     onInputChange,
    //     onCreate,
    //     onSelect,
    //     onRemove,
    //     onClose,
    //     filterOption,
    //     name,
    //     menuIsOpen,
    //     backgroundColorMenu = Colors.Neutral2,
    //     optionStyle = OptionStyle.text,
    //     creatable,
    //     className,
    // } = props;

    // useEffect(() => {
    //     const fullMatch = optionsOrDefault.find(
    //         (option) => (option.label ?? '').toUpperCase() === inputValue.toUpperCase()
    //     );

    //     if (!fullMatch && inputValue.length > 0 && creatable) {
    //         const option = { label: inputValue, value: null };
    //         setNewOption(option);
    //         setAllOptions([option, ...optionsOrDefault.map((o) => ({ ...o }))]);
    //     } else {
    //         setAllOptions(optionsOrDefault);
    //         setNewOption(null);
    //     }
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [options, inputValue, creatable]);

    const handleChange: ImplementationProps<Value>['onChange'] = (value, action) => {
        if (action.action === 'create-option') {
            const [addedOption] = value.slice(-1);
            onCreate && onCreate({ value: null, label: addedOption.label });
        } else if (action.action === 'pop-value' || action.action === 'remove-value') {
            onRemove && onRemove(action.removedValue);
        } else if (action.action === 'select-option') {
            onSelect && onSelect(action.option!);
        }
    };

    const CustomMenuListElement: typeof components.MenuList = (props) => {
        if (CustomMenu) {
            return (
                <components.MenuList {...props}>
                    <CustomMenu {...props} />
                </components.MenuList>
            );
        } else {
            return (
                <>
                    <components.MenuList {...props}>
                        {/*CustomHeaderMenu ?
            <CustomHeaderMenu {...props} /> : ""*/}
                        {props.children}
                    </components.MenuList>
                </>
            );
        }
    };

    const CustomMenuElement = components.Menu;

    const CustomIconElement: typeof components.IndicatorsContainer = (props) => {
        return <div />;
    };

    const CustomNoOptionsMessage: typeof components.NoOptionsMessage = (props) => {
        const { t } = useAppTranslation();
        return (
            <>
                <components.NoOptionsMessage {...props}>
                    {t('commonLabels.noMatchesFound')}
                </components.NoOptionsMessage>
            </>
        );
    };

    const componentsConfig = {
        Option: OptionElement(optionStyle),
        IndicatorsContainer: CustomIconElement,
        MenuList: CustomMenuListElement,
        Menu: CustomMenuElement,
        NoOptionsMessage: CustomNoOptionsMessage,
    };

    let selectElement: JSX.Element;

    const selectStyles: ImplementationProps<Value>['styles'] = {
        container: (base) => ({
            ...base,
            width: '100%',
            //maxHeight: 450,
        }),
        control: (base, { selectProps, hasValue }) => {
            return {
                ...base,
                cursor: 'pointer',
                ...styles.control,
            };
        },
        indicatorSeparator: (base) => ({
            ...base,
            display: 'none',
        }),
        // control: (base, { selectProps, hasValue }) => {
        //     const { menuIsOpen } = selectProps;
        //     return {
        //         ...base,
        //         minHeight: 49,
        //         maxHeight: 450,
        //         overflowY: 'auto',
        //         ...scrollbarStyle(),
        //         ...inputBoxStyle({
        //             theme,
        //             size,
        //         }),
        //         borderColor: `${getColorByState(
        //             InputValidation.default,
        //             menuIsOpen ? Colors.CM1 : undefined
        //         )}!important`,
        //         background: `${!menuIsOpen ? Colors.Neutral2 : Colors.White} !important`,
        //         cursor: 'pointer',
        //         ...styles.control,
        //     };
        // },
        // menu: (base) => ({
        //     border: `solid 1px ${Colors.Neutral5}`,
        //     boxShadow: 'none',
        //     backgroundColor: Colors.Neutral2,
        //     borderRadius: `${isRounded(theme) ? 16 : 10}px`,
        //     marginTop: 5,
        //     marginBottom: 5,
        //     zIndex: ZLayer.menu,

        //     ...(styles ? styles.menu : {}),
        //     width: '100%',
        //     position: 'absolute',
        // }),
        // input: (base) => ({
        //     ...base,
        // }),
        // menuList: (base) => ({
        //     // '&::-webkit-scrollbar-track': {
        //     //     background: `${Colors.Neutral3}!important`,
        //     //     borderRadius: '50px',
        //     //     marginTop: 11,
        //     //     marginBottom: 16,
        //     // },

        //     paddingRight: 6,
        //     marginRight: 6,
        //     boxSizing: 'border-box',
        //     overflowY: 'auto',
        //     maxHeight: '60vh',
        //     ...scrollbarStyle(),
        // }),
        // placeholder: (base) => ({
        //     ...base,
        //     ...FontSecondary(
        //         HeaderStyles.H7_11px,
        //         false,
        //         isRounded(theme) ? Colors.CM1 : Colors.Neutral7
        //     ),
        // }),
        // noOptionsMessage: (base) => ({
        //     ...base,
        //     ...FontPrimary(HeaderStyles.H6_12px, true, Colors.CM2),
        //     letterSpacing: 'normal',
        // }),
        // valueContainer: (base) => ({
        //     ...base,
        //     padding: '11px 10px 7px 10px !important',
        //     gap: 5,
        // }),
        multiValue: (base) => {
            return {
                ...base,
                background: theme.palette.neutral[3],
                borderRadius: 15,
            };
        },
        multiValueLabel: (base) => ({
            ...base,
            ...theme.typography.h6Inter,
            color: theme.palette.neutral[7],
            fontWeight: 'normal',
            padding: '6px',
            paddingLeft: '9px',
        }),
        multiValueRemove: (base) => ({
            ...base,
            color: theme.palette.neutral[6],
            width: 24,
            '& svg': {
                height: '20px!important',
                width: '20px!important',
            },
            ':hover': {
                backgroundColor: theme.palette.neutral[4],
                borderRadius: '0 15px 15px 0',
                color: 'white',
            },
        }),
    };

    if (creatable) {
        selectElement = (
            <CreatableSelect
                isMulti={true}
                onChange={handleChange}
                onMenuClose={onClose}
                filterOption={filterOption}
                allowCreateWhileLoading={true}
                defaultValue={value}
                isDisabled={disabled}
                components={componentsConfig}
                options={options}
                menuIsOpen={menuIsOpen}
                optionStyle={optionStyle}
                value={value}
                placeholder={placeholder}
                styles={selectStyles}
                multiple
            />
        );
    } else {
        selectElement = (
            <Select
                isMulti={true}
                onChange={handleChange}
                onMenuClose={onClose}
                filterOption={filterOption}
                allowCreateWhileLoading={true}
                defaultValue={value}
                isDisabled={disabled}
                components={componentsConfig}
                options={options}
                menuIsOpen={menuIsOpen}
                optionStyle={optionStyle}
                value={value}
                placeholder={placeholder}
                styles={selectStyles}
                multiple
            />
        );
    }

    return (
        <InputWrapper
            className={className}
            isInvalid={isInvalid}
            isRequired={isRequired}
            label={label}
            disabled={disabled}
            fullWidth={fullWidth}
            showValidationIndicators={showValidationIndicators}
            {...slotProps.inputWrapper}
        >
            {selectElement}
        </InputWrapper>
    );
}
export default DropdownMulti;
