import { Radio, styled } from '@mui/material';
import { Icon } from 'common/components/Icons/Icon';
import { Colors } from 'common/styles/Colors';
import { memo, useCallback } from 'react';
import { IconSize } from '../../../styles/IconSize';
import { CheckCircleIcon } from '../../Icons/CheckCircleIcon';
import { UncheckedCircleIcon } from '../../Icons/UncheckedCircleIcon';

export type IRadioButtonProps = {
    checked?: boolean;
    onChange?: (checked: boolean) => void;
    name?: string;
    value?: boolean;
    disabled?: boolean;
    className?: string;
    label?: string;
    iconRight?: typeof Icon;
    description?: string;
};

const NoncachedRadioButton = ({
    checked,
    onChange,
    name,
    value: valueProp, // value is here for compatibility, since we use it somewhere but basically useless here
    disabled,
    className,
    label,
    iconRight: IconRight,
    description,
}: IRadioButtonProps) => {
    const mainColor = disabled ? 'var(--neutral6)' : 'var(--cm1)';
    const isChecked = checked ?? valueProp ?? false;
    const onChangeCallback = useCallback(
        () => (onChange ? onChange(!isChecked) : undefined),
        [isChecked, onChange]
    );

    return (
        <DivContainer disabled={!!disabled}>
            <DivRadioContainer disabled={!!disabled}>
                <StyledRadio
                    disabled={disabled}
                    checked={isChecked}
                    onChange={onChangeCallback}
                    name={name}
                    className={className}
                    color="default"
                    inputProps={{
                        'aria-label': name,
                    }}
                    checkedIcon={<CheckCircleIcon fill={mainColor} size={IconSize.M} />}
                    icon={<UncheckedCircleIcon fill={mainColor} size={IconSize.M} />}
                />
                {label && (
                    <SpanLabel selected={!!checked && !disabled} onClick={onChangeCallback}>
                        {label}
                    </SpanLabel>
                )}
                {IconRight && <IconRight fill={Colors.CM1} />}
            </DivRadioContainer>
            {description && <SpanDescription>{description}</SpanDescription>}
        </DivContainer>
    );
};

export const CachedRadioButton = memo(
    NoncachedRadioButton,
    (prevProps: IRadioButtonProps, nextProps: IRadioButtonProps) => {
        if (prevProps.checked !== nextProps.checked) return false;
        if (prevProps.onChange !== nextProps.onChange) return false;
        if (prevProps.name !== nextProps.name) return false;
        if (prevProps.value !== nextProps.value) return false;
        if (prevProps.disabled !== nextProps.disabled) return false;
        if (prevProps.className !== nextProps.className) return false;
        if (prevProps.label !== nextProps.label) return false;
        if (prevProps.description !== nextProps.description) return false;
        return true;
    }
);

const DivContainer = styled('div', {
    shouldForwardProp: (prop) => !['disabled'].includes(prop as string),
})<{ disabled: boolean }>(({ disabled }) => ({
    display: 'flex',
    flexDirection: 'column',

    ...(disabled ? { pointerEvents: 'none', opacity: 0.5 } : undefined),
}));

const DivRadioContainer = styled('div', {
    shouldForwardProp: (prop) => !['disabled'].includes(prop as string),
})<{ disabled: boolean }>(({ disabled }) => ({
    display: 'flex',
    alignItems: 'center',

    ...(disabled ? { pointerEvents: 'none', opacity: 0.5 } : undefined),
}));

const StyledRadio = styled(Radio)({
    padding: 1,
});

const SpanLabel = styled('label', {
    shouldForwardProp: (prop) => !['selected'].includes(prop as string),
})<{ selected: boolean }>(({ theme, selected }) => ({
    ...theme.typography.h6Inter,
    color: selected ? theme.palette.primary.main : theme.palette.neutral[6],
    fontWeight: 400,
    cursor: 'pointer',
    textAlign: 'left',
    marginLeft: '2px',
}));

const SpanDescription = styled('span')(({ theme }) => ({
    marginLeft: '28px',
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[6],
    fontWeight: 400,
}));

export { CachedRadioButton as RadioButton };
