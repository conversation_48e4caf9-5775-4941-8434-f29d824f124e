// export function formatNumber(num: number) {
//     return num.toLocaleString('en-US', {
//         minimumIntegerDigits: 2,
//         useGrouping: false,
//     });
// }

// export function parseTime(s: string): [number, number] | null {
//     const parts = s.split(':');
//     const h = +parts[0];
//     const m = parts.length > 1 ? +parts[1] : 0;

//     if (isNaN(h) || isNaN(m)) {
//         return null;
//     }

//     return [h, m];
// }
