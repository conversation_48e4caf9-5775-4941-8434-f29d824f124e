import { AccessTime } from '@mui/icons-material';
import { IconButton, InputAdornment, Popper, useForkRef } from '@mui/material';
import { multiSectionDigitalClockSectionClasses } from '@mui/x-date-pickers';
import { CmosTextFieldVariant } from 'common/components/mui';
import { ZLayer } from 'common/styles/ZLayer';
import { DateTime } from 'luxon';
import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import MaskedTextField, { MaskedTextFieldProps } from '../MaskedTextField';
import { handleCommonKeyDowns } from '../utils';
import TimePickerMenu from './TimePickerMenu';

export type TimeFieldOwnProps = {
    value: [number, number] | null;
    onChange?: (value: [number, number]) => void;
    keepOpen?: boolean;
    disablePast?: boolean;
    disableInput?: boolean;
    closeAfterSelect?: boolean;
    cmosVariant?: CmosTextFieldVariant;
};

export type TimeFieldProps = MergeTypes<TimeFieldOwnProps, Omit<MaskedTextFieldProps, 'mask'>>;

export const TimeField = React.forwardRef(
    (
        {
            value: externalValue,
            onChange,
            keepOpen = false,
            disablePast = false,
            disableInput = false,
            closeAfterSelect,
            onKeyDown,
            disabled,
            ...props
        }: TimeFieldProps,
        forwardedRef: React.ForwardedRef<HTMLDivElement>
    ) => {
        const inputRef = useRef<HTMLInputElement | null>(null);
        const paperRef = useRef<HTMLDivElement | null>(null);
        const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
        const [open, setOpen] = useState(false);

        // string value of the input while in edit mode
        const [inputValue, setInputValue] = useState('');
        // last valid value parsed from inputValue
        const internalValue = useInternalValue(inputValue);

        // input value that will be displayed depending on "open"
        const displayedInputValue = open ? inputValue : getInputValueFromTuple(externalValue);

        // popup picker value that will be displayed depending on "open"
        const displayedDateValue = open ? internalValue : getDateFromTimeTuple(externalValue);

        const closePopper = useCallback(() => {
            if (!stateRef.current.open) return;

            setOpen(false);
            stateRef.current.open = false;

            if (stateRef.current.onChange) {
                const newValue = getTimeTupleFromDate(stateRef.current.internalValue);
                // sanity check: newValue should always be not null here
                if (newValue !== null) {
                    stateRef.current.onChange(newValue);
                }
            }
        }, []);

        const stateRef = useRef({
            open,
            externalValue,
            internalValue,
            onChange,
            closePopper,
        });
        stateRef.current.open = open;
        stateRef.current.externalValue = externalValue;
        stateRef.current.internalValue = internalValue;
        stateRef.current.onChange = onChange;
        stateRef.current.closePopper = closePopper;

        // effect for closing the picker on click outside
        useEffect(() => {
            // if time picker is open and user clicks anywhere other than time picker or popper, close time picker
            if (!open) return () => {};

            const callback = (e: MouseEvent) => {
                if (!(e.target instanceof Node)) return;
                if (paperRef.current?.contains(e.target) || anchorEl?.contains(e.target)) return;
                stateRef.current.closePopper();
            };

            document.body.addEventListener('mousedown', callback);

            return () => document.body.removeEventListener('mousedown', callback);
        }, [anchorEl, open]);

        const openPopper = useCallback(() => {
            if (stateRef.current.open || disabled) return;

            setOpen(true);
            stateRef.current.open = true;
            setInputValue(
                getInputValueFromDate(getDateFromTimeTuple(stateRef.current.externalValue))
            );
        }, [disabled]);

        const handleFocus = useCallback(
            (e: React.FocusEvent<HTMLInputElement>) => {
                if (!stateRef.current.open) {
                    openPopper();
                }
            },
            [openPopper]
        );

        const handleBlur = useCallback(
            (e: React.FocusEvent<HTMLInputElement>) => {
                if (!e.relatedTarget) {
                    // just lost focus
                    return;
                }

                if (paperRef.current && !paperRef.current.contains(e.relatedTarget)) {
                    // focused on something outside of the time picker
                    closePopper();
                    return;
                }
            },
            [closePopper]
        );

        const handleMenuValueChange = useCallback(
            (value: Date | null) => {
                const newInputValue = getInputValueFromDate(value);
                setInputValue(newInputValue);

                const newInternalValue = parsePartialInput(newInputValue);
                if (newInternalValue !== 'invalid') {
                    stateRef.current.internalValue = newInternalValue;
                }

                closePopper();
            },
            [closePopper]
        );

        const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
            setInputValue(e.target.value);

            if (stateRef.current.onChange) {
                const newValue = getTimeTupleFromDate(stateRef.current.internalValue);
                // sanity check: newValue should always be not null here
                if (newValue !== null) {
                    stateRef.current.onChange(newValue);
                }
            }
        }, []);

        const handlePaper = useCallback((paper: HTMLDivElement | null) => {
            // this ref callback assumes that once paper appeared it means popup is open

            if (!paper) return;
            if (!stateRef.current.open) return;
            if (stateRef.current.externalValue !== null) return;

            const sections = paper.querySelectorAll(
                `.${multiSectionDigitalClockSectionClasses.root}`
            );
            const hoursSection: Element | null = sections.item(0);
            const minutesSection: Element | null = sections.item(1);

            const focusOn = getNearest15min();

            if (hoursSection) {
                const hours = focusOn.hour();
                const child = hoursSection.children.item(hours);
                if (child) {
                    child.scrollIntoView();
                }
            }

            if (minutesSection) {
                const minutes = focusOn.minute();
                const child = minutesSection.children.item(minutes);
                if (child) {
                    child.scrollIntoView();
                }
            }
        }, []);

        const paperForkedRef = useForkRef(paperRef, handlePaper);
        const rootRef = useForkRef(setAnchorEl, forwardedRef);

        return (
            <>
                <MaskedTextField
                    data-test-closeAfterSelect={closeAfterSelect ? 'true' : undefined}
                    slotProps={{
                        textField: {
                            ref: rootRef,
                            autoComplete: 'off',
                        },
                    }}
                    inputRef={inputRef}
                    endAdornment={
                        <InputAdornment position="end">
                            <IconButton
                                size="small"
                                sx={{ marginRight: -1.5 }}
                                onClick={openPopper}
                                disabled={disabled}
                            >
                                <AccessTime />
                            </IconButton>
                        </InputAdornment>
                    }
                    value={displayedInputValue}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    readonly={disableInput}
                    mask={[/\d/, /\d/, ':', /\d/, /\d/]}
                    maskPlaceholderChar="_"
                    onKeyDown={handleCommonKeyDowns({
                        onKeyDown,
                        onEnterPress: closePopper,
                    })}
                    disabled={disabled}
                    {...props}
                />

                <Popper
                    style={{ zIndex: ZLayer.menu }}
                    popperOptions={{ placement: 'bottom-start' }}
                    open={open || keepOpen}
                    anchorEl={anchorEl}
                    keepMounted={false} // keep this false or handlePaper will break
                >
                    <TimePickerMenu
                        ref={paperForkedRef}
                        onChange={handleMenuValueChange}
                        value={displayedDateValue}
                        disablePast={disablePast}
                        disabled={disabled || disableInput}
                        anchorWidth={anchorEl?.offsetWidth}
                    />
                </Popper>
            </>
        );
    }
);

function getNearest15min(): moment.Moment {
    const now = moment();
    const roundInterval: number = 15;
    const remainder: number = roundInterval - (now.minutes() % roundInterval);

    return now.add(remainder, 'minutes');
}

function getDateFromTimeTuple(tuple: null | [number, number]): Date | null {
    if (tuple === null) return null;

    return DateTime.fromObject({ hour: tuple[0], minute: tuple[1] }).toJSDate();
}

function getTimeTupleFromDate(tuple: null | Date): [number, number] | null {
    if (tuple === null) return null;

    return [tuple.getHours(), tuple.getMinutes()];
}

function parsePartialInput(s: string): Date | null | 'invalid' {
    if (s === '__:__') return null;

    if (/\d{2}:\d{2}/.test(s)) {
        const [h, m] = s.split(':').map(Number);
        const d = new Date();
        d.setHours(h, m, 0, 0);
        return d;
    }

    return 'invalid';
}

function useInternalValue(inputValue: string): Date | null {
    const lastValidValue = useRef<Date | null>(null);
    const parsedValue = parsePartialInput(inputValue);
    if (parsedValue !== 'invalid') {
        lastValidValue.current = parsedValue;
    }

    return lastValidValue.current;
}

function getInputValueFromDate(date: Date | null): string {
    if (date === null) return '';
    return DateTime.fromJSDate(date).toFormat('HH:mm');
}

function getInputValueFromTuple(tuple: null | [number, number]): string {
    if (tuple === null) return '';
    return DateTime.fromObject({ hour: tuple[0], minute: tuple[1] }).toFormat('HH:mm');
}
