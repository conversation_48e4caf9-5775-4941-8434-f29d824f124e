import { styled } from '@mui/material';
import { useEffect, useRef } from 'react';

export type TimePickerMenuItemProps = {
    selected: boolean;
    value: number;
    onSelected: (value: number) => void;
    disabled?: boolean;
    isScrolledTo: boolean;
    isShown: boolean;
};

export default function TimePickerMenuItem({
    selected,
    value,
    onSelected,
    disabled,
    isScrolledTo,
    isShown,
}: TimePickerMenuItemProps) {
    const handleOnSelect = () => onSelected(value);
    const itemRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (isShown && isScrolledTo && itemRef && itemRef.current) {
            itemRef.current.scrollIntoView({
                block: 'nearest',
                inline: 'nearest',
            });
        }
    }, [isShown, isScrolledTo]);

    return (
        <DivRoot onClick={!disabled ? handleOnSelect : undefined} selected={selected} ref={itemRef}>
            {value + ''}
        </DivRoot>
    );
}

const DivRoot = styled('div', {
    shouldForwardProp: (prop) => !['selected'].includes(prop as string),
})<{ selected: boolean }>(({ theme, selected }) => ({
    cursor: 'pointer',
    height: 38,
    display: 'flex',
    alignItems: 'center',
    marginRight: 12,
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
    color: selected ? theme.palette.primary.main : 'initial',

    '&:hover': {
        backgroundColor: 'var(--cm5)',
    },

    '&$disabled': {
        cursor: 'default',
        opacity: '0.5',

        '&:hover': {
            backgroundColor: theme.palette.neutral[1],
        },
    },
}));
