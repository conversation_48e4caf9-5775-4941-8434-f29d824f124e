import { Paper, styled } from '@mui/material';
import { DigitalClock } from '@mui/x-date-pickers';
import { PickerSelectionState } from '@mui/x-date-pickers/internals';
import { useQuery } from '@tanstack/react-query';
import OrdersApi from 'api/orders';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import moment from 'moment';
import { OverlayScrollbars } from 'overlayscrollbars';
import 'overlayscrollbars/overlayscrollbars.css';
import { forwardRef, useEffect, useMemo, useRef } from 'react';
import { LocalizationProvider } from '../../Calendar';

export type TimePickerMenuProps = {
    value: Date | null;
    onChange?: (value: Date, selectionState?: PickerSelectionState) => void;
    disablePast?: boolean;
    disabled?: boolean;
    disableInput?: boolean;
    anchorWidth?: number;
};

const TimePickerMenu = forwardRef<HTMLDivElement, TimePickerMenuProps>(
    ({ value, onChange, disablePast, disabled, disableInput, anchorWidth }, ref) => {
        const timeInterval = useTimeInterval();

        const clockRef = useRef<HTMLDivElement>(null);
        const scrollbarInstance = useRef<OverlayScrollbars | null>(null);
        const currentHour = moment().hour();

        const availableSlotTime = useMemo(() => {
            if (!timeInterval) return null;

            const targetTime = value ? moment(value) : moment();

            // Round minutes to the nearest available interval for scrolling
            return targetTime
                .minute(Math.round(targetTime.minute() / timeInterval) * timeInterval)
                .seconds(0)
                .toDate();
        }, [timeInterval, value]);

        useEffect(() => {
            if (!clockRef.current) return;

            scrollbarInstance.current = OverlayScrollbars(clockRef.current, {});

            return () => {
                scrollbarInstance.current?.destroy();
                scrollbarInstance.current = null;
            };
        }, []);

        // Automatically scrolls the DigitalClock to the nearest current hour when opened
        useEffect(() => {
            if (availableSlotTime && clockRef.current && scrollbarInstance.current) {
                const viewport = scrollbarInstance.current.elements().viewport;
                if (!viewport) return;

                const allItems = viewport.querySelectorAll('[role="option"]');
                const scrollToTime = moment(availableSlotTime).format('HH:mm');
                const targetItem = Array.from(allItems).find(
                    (item) => item.textContent?.trim() === scrollToTime
                );

                if (targetItem) {
                    const targetPosition = (targetItem as HTMLElement).offsetTop;
                    viewport.scrollTo({
                        top: targetPosition - viewport.clientHeight / 2.5,
                        behavior: 'smooth',
                    });
                }
            }
        }, [availableSlotTime]);

        return (
            <StyledPaper ref={ref} anchorWidth={anchorWidth}>
                <LocalizationProvider>
                    <StyledDigitalClock
                        value={value || currentHour}
                        ref={clockRef}
                        onChange={onChange}
                        autoFocus={false}
                        disablePast={disablePast}
                        disabled={disabled || disableInput}
                        ampm={false}
                        timeStep={timeInterval ? timeInterval : 15}
                    />
                </LocalizationProvider>
            </StyledPaper>
        );
    }
);

const StyledDigitalClock = styled(DigitalClock)({
    display: 'flex',

    '& .MuiDigitalClock-list': { width: '100%' },
    '& .MuiDigitalClock-item': {
        margin: 0,
        height: 38,
        borderTop: '1px solid var(--neutral3)',

        ':hover': {
            backgroundColor: 'var(--cm5) !important',
        },

        '&.Mui-selected': {
            color: 'var(--cm1)',
            backgroundColor: 'initial',
            fontWeight: 'bold',
        },
    },
});

const StyledPaper = styled(Paper)<{ anchorWidth?: number }>(({ anchorWidth }) => ({
    marginTop: 5,
    border: '1px solid var(--neutral4)',
    boxShadow: 'none',
    overflow: 'hidden',
    width: anchorWidth ? `${anchorWidth}px` : 'auto',
}));

export default TimePickerMenu;

// TODO: I don't like that we are calling API from a common component
// I REALLY don't like that, but we have to fix this fast
function useTimeInterval() {
    const isEnterprise = useIsEnterpriseRoute();

    const { data: shopTimeInterval } = useQuery(['orders', 'time-interval'], {
        queryFn: OrdersApi.getTimeInterval,
        cacheTime: Infinity,
        staleTime: Infinity,
        enabled: !isEnterprise,
    });

    return isEnterprise ? 15 : shopTimeInterval;
}
