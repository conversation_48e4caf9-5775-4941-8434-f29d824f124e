import { useCallback, useEffect, useState } from 'react';

function parseTime(v: string): number {
    const parts = v.trim().split(':').map(Number);
    let m = 0;
    if (parts.length >= 2) m += isNaN(parts[1]) ? 0 : parts[1];
    if (parts.length >= 1) m += isNaN(parts[0]) ? 0 : parts[0] * 60;
    return m;
}

function totalMinutesToString(v: number) {
    const h = Math.floor(v / 60);
    const m = v - h * 60;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
}

type StateData = {
    open: boolean;
    displayedValue: number;
    inputValue: string;
    firstRender: boolean;
    originalValue: number;
};

function hoursAndMinuteChanged(v: number, v2: number) {
    return Math.floor(v / 60) !== Math.floor(v2 / 60) && Math.floor(v % 60) !== Math.floor(v2 % 60);
}

export default function useTimePickerState(currentValue: number) {
    //const firstRender = useRef<boolean>(true);
    const [state, setState] = useState<StateData>({
        open: false,
        displayedValue: currentValue,
        inputValue: currentValue ? totalMinutesToString(currentValue) : '',
        firstRender: true,
        originalValue: currentValue,
    });

    useEffect(() => {
        // if (firstRender.current) {
        //     firstRender.current = false;
        //     return;
        // }
        const displayedString = totalMinutesToString(currentValue);
        setState((s) => ({
            ...s,
            displayedValue: currentValue,
            inputValue: displayedString,
            originalValue: currentValue,
        }));
    }, [currentValue]);

    return {
        open: useCallback(() => {
            setState((s) => ({
                ...s,
                open: true,
                displayedValue: currentValue,
                originalValue: currentValue,
                inputValue: totalMinutesToString(currentValue),
            }));
        }, [currentValue]),
        isOpen: state.open,
        close: useCallback(() => {
            setState((s) => ({
                ...s,
                open: false,
                originalValue: currentValue,
            }));
        }, [currentValue]),
        displayedValue: state.displayedValue,
        setDisplayedValue: useCallback((v: number) => {
            v = Math.max(0, Math.min(23 * 60 + 59, v));
            setState((s) => ({
                ...s,
                displayedValue: v,
                inputValue: totalMinutesToString(v),
            }));
        }, []),
        inputString: state.inputValue,
        setInputString: useCallback((v: string) => {
            setState((s) => ({
                ...s,
                displayedValue: Math.max(0, Math.min(23 * 60 + 59, parseTime(v))),
                inputValue: v,
            }));
        }, []),
        normalizeInputValue: useCallback(() => {
            setState((s) => ({
                ...s,
                inputValue: totalMinutesToString(s.displayedValue),
            }));
        }, []),
        valueWasSet: hoursAndMinuteChanged(state.displayedValue, state.originalValue),
    };
}
