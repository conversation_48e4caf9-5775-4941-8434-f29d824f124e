import {
    Box,
    CircularProgress,
    IconButton,
    InputAdornment,
    inputAdornmentClasses,
    inputBaseClasses,
    styled,
} from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { handleCommonKeyDowns } from 'common/components/Inputs/utils';
import { TextField, TextFieldProps } from 'common/components/mui';
import { Colors } from 'common/styles/Colors';
import React, { ChangeEvent, useCallback, useRef, useState, useEffect } from 'react';

type ResettableTextInputWithCustomControlProps = Omit<
    TextFieldProps,
    'IconRight' | 'onIconRightClick' | 'value'
> & {
    control: React.ReactElement<any, any>;
    setStartEditingFn: (callback: () => void) => void;
    onSave: (value: string) => Promise<boolean>;
    onEdit?: (value: string) => string;
    onValidate?: (value: string) => void;
    formatValue?: string;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    hasCharacterCounter?: boolean;
    isRequired?: boolean;
    isInvalid?: boolean;
    value?: string;
    maxLength?: number;
    formatting?: (value: string) => string;
    disabled?: boolean;
};

export default function ResettableTextInputWithCustomControl({
    control,
    setStartEditingFn,
    value,
    onSave,
    onEdit,
    onKeyDown,
    onEnterPress,
    onEscPress,
    onValidate,
    hasCharacterCounter,
    isRequired,
    size = 'medium',
    isInvalid,
    maxLength,
    formatting,
    disabled = false,
    ...props
}: ResettableTextInputWithCustomControlProps) {
    const [newValue, setNewValue] = useState('');
    const [isEditing, setIsEditing] = useState(false);
    const [isLoading, setLoading] = useState(false);
    const [isLengthInvalid, setIsLengthInvalid] = useState(false);
    const [isSaveInvalid, setIsSaveInvalid] = useState(false);

    const cancelButtonHover = useRef(false);
    const cancelSave = useRef(false);
    const canSave =
        !cancelSave.current &&
        !isInvalid &&
        !isLengthInvalid &&
        !isSaveInvalid &&
        (isRequired ? newValue.trim() !== '' : true);

    const saveCallback = useCallback(async () => {
        if (!canSave) return;
        if (newValue === value) {
            setIsEditing(false);
            return;
        }
        setLoading(true);
        try {
            const isSuccess = await onSave(newValue);
            if (isSuccess) {
                setIsEditing(false);
            } else {
                setIsSaveInvalid(true);
            }
        } catch {
            setIsSaveInvalid(true);
        } finally {
            setLoading(false);
        }
    }, [onSave, newValue, value, canSave]);

    const onIconClickCallback = () => {
        if (disabled) return;
        saveCallback();
    };

    const cancelCallback = useCallback(() => {
        setIsEditing(false);
        cancelSave.current = true;
        cancelButtonHover.current = false;
        if (value && onValidate) onValidate(value);
    }, [setIsEditing, cancelSave, value, onValidate]);

    const onChangeCallback = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            if (onValidate) onValidate(e.target.value);
            let v = e.target.value;
            if (onEdit) {
                v = onEdit(v);
            }
            setIsSaveInvalid(false);
            setIsLengthInvalid(
                typeof maxLength == 'number' && maxLength >= 0 && v.length > maxLength
            );
            setNewValue(v);
        },
        [onValidate, isEditing, onEdit, maxLength]
    );

    const startEditing = useCallback(() => {
        if (disabled) return;

        setIsLengthInvalid(false);
        setIsSaveInvalid(false);
        setIsEditing(true);
        setNewValue(value || '');
        cancelSave.current = false;
    }, [setIsEditing, value, disabled]);

    useEffect(() => {
        setStartEditingFn(startEditing);
    }, [setIsEditing, value, disabled]);

    const handleMouseEnter = () => {
        cancelButtonHover.current = true;
    };

    const handleMouseLeave = () => {
        cancelButtonHover.current = false;
    };

    const numberToString = (n: number) => {
        return (Math.floor(n / 10) % 10).toFixed() + (n % 10).toFixed();
    };

    const endAdornment = isEditing && (
        <InputAdornment position="end">
            {hasCharacterCounter && maxLength && isEditing && (
                <StyledSpan>
                    {numberToString(newValue?.length)}/{numberToString(maxLength)}
                </StyledSpan>
            )}
            {!isInvalid && !isLengthInvalid && !isSaveInvalid && (
                <IconButton
                    disabled={!!disabled}
                    sx={
                        size === 'small'
                            ? { height: '32px', width: '32px', marginRight: -1.5 }
                            : { height: '40px', width: '40px', marginRight: -1.5 }
                    }
                    onClick={onIconClickCallback}
                >
                    <CheckIcon />
                </IconButton>
            )}
        </InputAdornment>
    );

    const handleKeyDown: typeof onKeyDown = (e) => {
        if (e.key === 'Enter' && props.multiline) {
            return;
        }
        if (onKeyDown) onKeyDown(e);
    };

    return (
        <>
            {!isEditing && control}
            {isEditing && (
                <>
                    <StyledTextField
                        fullWidth
                        InputProps={{
                            readOnly: !isEditing,
                            endAdornment: endAdornment,
                            error: isInvalid || isLengthInvalid || isSaveInvalid,
                        }}
                        onChange={onChangeCallback}
                        value={
                            (isEditing ? newValue : formatting ? formatting(value ?? '') : value) ??
                            ''
                        }
                        onKeyDown={handleCommonKeyDowns({
                            onKeyDown: handleKeyDown,
                            onEnterPress: props.multiline
                                ? undefined
                                : (e) => {
                                      if (onEnterPress) onEnterPress(e);
                                      saveCallback();
                                  },
                            onEscPress: (e) => {
                                if (onEscPress) onEscPress(e);
                                cancelCallback();
                            },
                        })}
                        size={size}
                        disabled={disabled}
                        {...props}
                    />
                    {size === 'small' ? (
                        <PaddedIconButton
                            size="small"
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                            onClick={() => cancelCallback()}
                        >
                            <CloseIcon fill={Colors.Neutral6} />
                        </PaddedIconButton>
                    ) : (
                        <AlignedIconButton
                            onMouseEnter={handleMouseEnter}
                            onMouseLeave={handleMouseLeave}
                            onClick={() => cancelCallback()}
                        >
                            <CloseIcon fill={Colors.Neutral6} />
                        </AlignedIconButton>
                    )}
                </>
            )}
            {isLoading && (
                <Box marginLeft={1} display={'flex'} alignItems={'center'}>
                    <CircularProgress size={20} thickness={4} />
                </Box>
            )}
        </>
    );
}

const StyledTextField = styled(TextField)(({ multiline, size }) => ({
    [`& .${inputAdornmentClasses.root}`]: {
        alignSelf: multiline ? 'end' : 'start',
        marginTop: multiline
            ? size === 'medium'
                ? '4.5px'
                : '7.5px'
            : size === 'medium'
            ? '20px'
            : '16px',
    },
}));

const AlignedIconButton = styled(IconButton)({
    alignSelf: 'start',
});

const PaddedIconButton = styled(AlignedIconButton)({
    padding: '3px 3px',
});

const StyledSpan = styled('span')({
    fontSize: 10,
    textAlign: 'right',
    width: 30,
});
