import { Colors } from '../../styles/Colors';
import { FontSecondary } from '../../styles/FontHelper';
import { HeaderStyles } from '../../styles/HeaderStyles';
import { InputTheme, isRounded } from '../../styles/InputTheme';
import { InputSize } from './InputSize';
import { InputValidation } from './InputValidation';

export const getColorByState = (state: InputValidation, def: Colors = Colors.Neutral8) => {
    if (state === InputValidation.error) return Colors.Error;
    else if (state === InputValidation.success) return Colors.Success;
    else return def;
};

export const borderColor = (normal: Colors, focus: Colors, hover: Colors) => ({
    border: `solid 1px ${normal}`,
    ...borderEvent(focus, hover),
});
export const borderEvent = (focus: Colors, hover: Colors) => ({
    '&:focus': {
        outline: 'none',
        border: `solid 1px ${focus}`,
    },
    '&:active': {
        border: `solid 1px ${focus}`,
    },
    '&:hover': {
        border: `1px solid ${hover}`,
    },
});

export const getBackgraundColor = (theme: InputTheme) => {
    if (theme === InputTheme.rectangle_grey || theme === InputTheme.rounded_grey)
        return Colors.Neutral2;
    else return Colors.White;
};
export type InputOrientation = 'row' | 'column';
export interface InputStylesProp {
    theme: InputTheme;
    size: InputSize;
}
export interface DisableableStylesProp {
    disabled?: boolean;
}

export const inputBoxStyle = ({ size, theme }: InputStylesProp) => ({
    ...borderColor(Colors.Neutral4, Colors.CM4, Colors.CM2),
    boxShadow: '',
    backgroundColor: getBackgraundColor(theme),
    borderRadius: `${isRounded(theme) ? 16 : 5}px`,
    /* Header / 5 / Roboto Regular 14px */
    /* Neutrales/9 */
    ...FontSecondary(
        size == InputSize.S
            ? isRounded(theme)
                ? HeaderStyles.H6_12px
                : HeaderStyles.H7_11px
            : HeaderStyles.H5_14px,
        false,
        Colors.Black
    ),
    '&.error': {
        ...borderColor(Colors.Error, Colors.Error, Colors.Error),
    },
    '&.success': {
        ...borderColor(Colors.Success, Colors.Success, Colors.Success),
    },
});
