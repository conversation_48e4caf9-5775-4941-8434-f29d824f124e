.container {
    width: 100%;
    display: flex;
}

.orientationRow {
    flex-direction: row;
}

.orientationColumn {
    flex-direction: column;
}

.errorIcon {
    width: 3px;
    height: 12.4px;
    position: absolute;
    right: 12.4px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-image: url(../../../assets/images/exclamation_icon.png);
}

.inputContainer {
    position: relative;
    height: 38px;
    width: 100%;
}

.requiredDot {
    width: 8px;
    height: 8px;
    position: absolute;
    top: -2px;
    right: -2px;
    border-radius: 50%;
    background-color: #467cfc;
}

.requiredLabel {
    font-family: proxima-nova;
    font-size: 13px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.08;
    letter-spacing: normal;
    color: #6797f5;
}

.optionalLabel {
    font-weight: 300;
}

.label {
    height: 15px;
    font-family: proxima-nova;
    font-size: 12px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.17;
    letter-spacing: normal;
    text-align: left;
    color: #4a4d51;
    margin: 12px 0;
}

.invalidInput {
    border: solid 1px #f15857;
}

.invalidInput:hover {
    border: solid 1px #f15857;
}

.invalidInput:focus {
    border: solid 1px #f15857;
}

.invalidInput:active {
    border: solid 1px #f15857;
}
