import Select from 'react-select';
import { Colors } from '../../../styles/Colors';
import { FontPrimary } from '../../../styles/FontHelper';
import { HeaderStyles } from '../../../styles/HeaderStyles';
import { ChangeEvent } from '../IInput';
import { ISelector } from '../ISelector';
import styles from './Selector.module.css';

// TODO is not using, may be deleted
const Selector = ({
    placeholder,
    className,
    orientation: labelOrientation,
    isRequired,
    options,
    onChange,
    value,
}: ISelector) => {
    const changeSelector = (event: ChangeEvent) => {
        if (onChange) onChange(event);
    };

    return (
        <div
            className={` ${className || ''} ${styles.container} ${
                labelOrientation === 'row' ? styles.orientationRow : styles.orientationColumn
            }`}
        >
            <div className={styles.inputContainer}>
                <Select
                    defaultValue={value}
                    value={value}
                    onChange={changeSelector}
                    options={options}
                    placeholder={placeholder}
                    styles={{
                        indicatorSeparator: (base: any) => ({
                            ...base,
                            display: 'none',
                        }),
                        dropdownIndicator: (base: any) => ({
                            ...base,
                            padding: '0px 8px',
                        }),
                        menu: (base) => ({
                            ...base,
                            borderRadius: 4,
                            border: 'solid 1px rgba(0, 0, 0, 0.1)',
                            backgroundColor: Colors.Neutral2,
                        }),
                        placeholder: (base: any) => ({
                            ...base,
                            ...FontPrimary(HeaderStyles.H6_12px),
                            fontStretch: 'normal',
                            opacity: 0.8,
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                            color: Colors.Neutral7,
                        }),
                        option: (base, { data }) => ({
                            ...FontPrimary(HeaderStyles.H7_11px),
                            display: 'flex',
                            alignItems: 'center',
                            height: 29,
                            borderBottom: `solid 0.5px #e5e7ea`,
                            opacity: 0.8,
                            fontStretch: 'normal',
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                            paddingLeft: 11,
                            paddingRight: 11,
                            ':hover': {
                                backgroundColor: Colors.CM5,
                            },
                        }),
                        control: (base: any) => ({
                            ...base,
                            height: 32,
                            minHeight: 32,
                            paddingLeft: 3,
                            borderRadius: 16,
                            border: 'solid 1px rgba(0, 0, 0, 0.1)',
                            backgroundColor: Colors.White,
                        }),
                        singleValue: (base, { data }) => ({
                            ...base,
                            ...FontPrimary(HeaderStyles.H7_11px),
                            fontStretch: 'normal',
                            opacity: 0.8,
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                            color: Colors.CM2,
                        }),
                        input: (base: any) => ({
                            ...base,
                            ...FontPrimary(HeaderStyles.H7_11px),
                            fontStretch: 'normal',
                            opacity: 0.8,
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                        }),
                    }}
                />
                {isRequired && <div className={styles.requiredDot} />}
            </div>
        </div>
    );
};

export default Selector;
