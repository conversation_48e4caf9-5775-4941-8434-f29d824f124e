/* eslint-disable @typescript-eslint/no-unused-vars */
import { exposeUnmaskedValue } from 'common/FormatersHelper';
import { forwardRef } from 'react';
import InputWrapper, { InputWrapperProps } from '../InputWrapper';
import MaskedTextField, { MaskedTextFieldProps } from '../MaskedTextField';
import { useValidateTextFieldValue } from '../utils';

export type MaskedTextFormFieldProps = MaskedTextFieldProps & {
    maxLength?: number;
    minLength?: number;
    slotProps?: {
        inputWrapper?: Partial<InputWrapperProps>;
    };
} & Pick<
        InputWrapperProps,
        'isRequired' | 'isInvalid' | 'label' | 'showValidationIndicators' | 'hideRoundIndicator'
    >;

const MaskedTextFormField = forwardRef<HTMLDivElement, MaskedTextFormFieldProps>(
    (
        {
            // own props
            maxLength,
            minLength,
            slotProps = {},
            onBlur,

            // InputWrapper
            isRequired,
            isInvalid,
            label,
            showValidationIndicators,
            hideRoundIndicator,
            disabled,

            // pass to child component
            ...childProps
        },
        ref
    ) => {
        const { value, mask, fullWidth } = childProps;

        const unmaskedValue = exposeUnmaskedValue(value ?? '', mask);

        const { valid: isValid, validate } = useValidateTextFieldValue(unmaskedValue, {
            minLength,
            maxLength,
            isRequired,
        });

        const isInvalidExt = isInvalid || isValid === false;
        const inputWrapperProps = {
            isInvalid: isInvalidExt,
            showValidationIndicators,
            label,
            isRequired,
            hideRoundIndicator,
            fullWidth,
            disabled,
        };

        function handleBlur(e: React.FocusEvent<HTMLInputElement>) {
            validate();
            if (onBlur) {
                onBlur(e);
            }
        }

        return (
            <InputWrapper {...inputWrapperProps} {...slotProps.inputWrapper} ref={ref}>
                <MaskedTextField
                    disabled={disabled}
                    onBlur={handleBlur}
                    isInvalid={isInvalidExt}
                    {...childProps}
                />
            </InputWrapper>
        );
    }
);
MaskedTextFormField.displayName = 'MaskedTextFormField';

export default MaskedTextFormField;
