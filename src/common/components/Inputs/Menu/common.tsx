import { Grid, Menu, MenuItem, styled } from '@mui/material';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

export const StyledMenu = styled(Menu)(({ theme }) => ({
    '& .MuiMenu-paper': {
        border: `solid 1px ${theme.palette.neutral[4]}`,
    },
}));

export const GridMenuSubheader = styled(Grid)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[3],
    padding: '9px 12px',

    ...theme.typography.h7Inter,
    color: theme.palette.neutral[7],
    fontWeight: 400,
}));

export const DivMenuItemContainer = styled('div')({
    padding: '10px 0',
    minWidth: 220,
    overflowY: 'auto',
    ...scrollbarStyle(),
});

export const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
    padding: '0 14px',

    '&:not(:last-child) $menuItemContent': {
        borderBottom: `solid 1px ${theme.palette.neutral[3]}`,
    },

    '&:hover': {
        backgroundColor: 'var(--cm5)',
    },
}));

export const DivMenuItem = styled('div')({
    padding: '4px 0',
    width: '100%',
    minWidth: 140,
});

export const SpanMenuItemLabel = styled('span', {
    shouldForwardProp: (prop) => !['isSelected'].includes(prop as string),
})<{ isSelected: boolean }>(({ theme, isSelected }) => ({
    ...theme.typography.h6Inter,
    color: isSelected ? theme.palette.primary.main : theme.palette.neutral[7],
    fontWeight: isSelected ? 700 : 400,
}));
