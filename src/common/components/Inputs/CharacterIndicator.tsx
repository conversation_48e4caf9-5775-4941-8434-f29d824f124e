import { styled, SxProps, Theme } from '@mui/material';

type CharacterIndicatorProps = {
    charactersCount: number;
    charactersLimit?: number;
    sx?: SxProps<Theme>;
};

function CharacterIndicator({
    charactersCount,
    charactersLimit = 100,
    sx,
}: CharacterIndicatorProps) {
    return <StyledSpan sx={sx}>{`${charactersCount}/${charactersLimit}`}</StyledSpan>;
}

const StyledSpan = styled('span')({
    fontSize: 10,
    textAlign: 'right',
    width: 40,
    position: 'absolute',
    bottom: 1,
    right: 10,
});

export default CharacterIndicator;
