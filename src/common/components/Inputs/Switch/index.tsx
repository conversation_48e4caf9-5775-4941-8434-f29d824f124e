import { styled } from '@mui/material';
import MUISwitch, { switchClasses, SwitchProps } from '@mui/material/Switch';
import { forwardRef } from 'react';

const StyledSwitch = styled(MUISwitch)(({ theme }) => ({
    [`& .${switchClasses.switchBase}`]: {
        color: theme.palette.neutral[9],

        [`&.${switchClasses.checked}`]: {
            color: theme.palette.neutral[9],

            [`& + .${switchClasses.track}`]: {
                backgroundColor: theme.palette.primary.main,
                opacity: 1,
            },
        },
    },

    [`&.${switchClasses.track}`]: {
        backgroundColor: `${theme.palette.neutral[5]} !important`,
        opacity: '1 !important',
    },

    [`& .${switchClasses.thumb}`]: {
        opacity: 1,
        boxShadow: '0px 0px 4px rgba(140, 140, 140, 0.9)',
        backgroundColor: theme.palette.neutral[1],
    },

    [`& .${switchClasses.track}`]: {
        opacity: 1,
        // height: 14,
        // width: 32,
        backgroundColor: theme.palette.neutral[5],
        [`&.${switchClasses.checked}`]: {
            backgroundColor: theme.palette.primary.main,
        },
    },

    [`& .${switchClasses.switchBase}.${switchClasses.disabled} + .${switchClasses.track}`]: {
        opacity: '0.5 !important',
    },

    [`&.${switchClasses.sizeMedium}`]: {
        height: '34px',
        padding: '11px',
        width: '56px',

        [`& .${switchClasses.thumb}`]: {
            width: 16,
            height: 16,
        },

        [`& .${switchClasses.track}`]: {
            height: 12,
        },
    },
}));
export const Switch = forwardRef<HTMLButtonElement, SwitchProps>((props, ref) => (
    <StyledSwitch ref={ref} size="medium" {...props} />
));
