import { useValidation } from 'utils/validation';

type HandleCommonKeyDownsParam = {
    onKeyDown?: React.KeyboardEventHandler;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    preventEnter?: boolean;
};

export function handleCommonKeyDowns({
    onKeyDown,
    onEnterPress,
    onEscPress,
    preventEnter,
}: HandleCommonKeyDownsParam): React.KeyboardEventHandler {
    return (e) => {
        if (onKeyDown) {
            onKeyDown(e);
        }
        if (e.key === 'Enter') {
            if (preventEnter) {
                e.preventDefault();
                e.stopPropagation();
            }
            if (onEnterPress) {
                onEnterPress(e);
            }
        }

        if (e.key === 'Escape' && onEscPress) {
            onEscPress(e);
        }
    };
}

export function useValidateTextFieldValue(
    value: string,
    {
        maxLength = Infinity,
        minLength = 0,
        isRequired = false,
        initialValidateOnChange,
    }: {
        maxLength?: number;
        minLength?: number;
        isRequired?: boolean;
        initialValidateOnChange?: boolean;
    }
) {
    return useValidation(
        value,
        (v) => v.length <= maxLength && v.length >= minLength && (!isRequired || v.trim() !== ''),
        { initialValidateOnChange }
    );
}
