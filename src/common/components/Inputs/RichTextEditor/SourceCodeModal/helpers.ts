// Quick way to format html https://stackoverflow.com/a/60338028
export function formatHtml(html: string) {
    var tab = '\t';
    var result = '';
    var indent = '';

    html.split(/>\s*</).forEach((element) => {
        if (element.match(/^\/\w/)) {
            indent = indent.substring(tab.length);
        }

        result += indent + '<' + element + '>\r\n';

        if (element.match(/^<?\w[^>]*[^\/]$/) && !element.startsWith('input')) {
            indent += tab;
        }
    });

    return result.substring(1, result.length - 3);
}
