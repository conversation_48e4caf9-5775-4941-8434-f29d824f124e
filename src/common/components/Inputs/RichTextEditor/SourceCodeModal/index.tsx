import { Box, IconButton, styled } from '@mui/material';
import { Editor } from '@tiptap/react';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useState } from 'react';
import { TextArea } from '../../TextArea';
import { formatHtml } from './helpers';

export type SourceCodeModalProps = {
    editor: Editor;
    title?: string;
    onClose: () => void;
    onSourceChanged: () => void;
};

export default function SourceCodeModal({
    editor,
    title,
    onClose,
    onSourceChanged,
}: SourceCodeModalProps) {
    const [html, setHtml] = useState('');
    const { t } = useAppTranslation();

    useEffect(() => {
        setHtml(formatHtml(editor.getHTML()));
    }, [editor]);

    const onSave = () => {
        editor.chain().setContent(html).run();
        onSourceChanged();
        onClose();
    };

    return (
        <Modal open>
            <DivRoot>
                <Box display="flex" justifyContent="space-between" marginBottom={2}>
                    <StyledHeader>{title ?? t('richTextEditor.sourceCodeTitle')}</StyledHeader>
                    <IconButton onClick={onClose} size="large">
                        <CloseIcon fill={'var(--neutral5)'} />
                    </IconButton>
                </Box>
                <StyledTextArea
                    hideLabel
                    value={html}
                    onChange={(e) => setHtml(e.target.value)}
                    name="textAreaHtml"
                />
                <Box display="flex" gap={0.5} marginTop={2} justifyContent="end">
                    <StyledButton
                        label={t('commonLabels.cancel')}
                        cmosVariant={'filled'}
                        color={'var(--neutral3)'}
                        onClick={onClose}
                    />

                    <StyledButton
                        label={t('commonLabels.save')}
                        cmosVariant={'filled'}
                        color={'var(--cm1)'}
                        onClick={onSave}
                    />
                </Box>
            </DivRoot>
        </Modal>
    );
}

const StyledTextArea = styled(TextArea)({
    '& textarea': {
        width: '600px !important',
        height: '400px !important',
        overflowY: 'scroll',
        ...scrollbarStyle(),
    },
});

const DivRoot = styled('div')({
    padding: 20,
});

const StyledHeader = styled('h4')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[8],
    marginTop: 12,
}));

const StyledButton = styled(Button)({
    width: 160,
});
