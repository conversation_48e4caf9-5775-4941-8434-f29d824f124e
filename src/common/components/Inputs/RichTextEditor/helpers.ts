import { Extension } from '@tiptap/core';

export function isFirefox() {
    return navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
}

function getSelectedText(): string {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return '';
    return sel.getRangeAt(0).cloneContents().textContent ?? '';
}

async function requestPermissions(permission: string) {
    if (navigator.permissions) {
        const result = await navigator.permissions.query({
            name: permission as PermissionName,
        });
        if (result.state !== 'granted' && result.state !== 'prompt') {
            throw new Error('Permissions denied');
        }
    }
}

// does not work in Firefox
export async function getClipboardContent() {
    await requestPermissions('clipboard-read');
    return await navigator.clipboard.readText();
}

export async function copyToClipboard() {
    if (!isFirefox()) {
        // Firefox does not need permission
        try {
            await requestPermissions('clipboard-write');
        } catch {}
    }
    const text = getSelectedText();
    navigator.clipboard.writeText(text);
}

// extension that disabled Enter key
export const DisableNL = Extension.create({
    name: 'DisableNL',
    addKeyboardShortcuts() {
        return {
            Enter: () => true,
        };
    },
});
