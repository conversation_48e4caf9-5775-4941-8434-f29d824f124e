import { Box, Popover, styled } from '@mui/material';
import { Editor } from '@tiptap/react';
import { Button } from 'common/components/Button';
import { BrokenLinkIcon } from 'common/components/Icons/BrokenLinkIcon';
import { UrlIcon } from 'common/components/Icons/UrlIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useRef, useState } from 'react';
import Checkbox from '../../Checkbox';
import TextFormField from '../../TextField';
import TextEditorIcon from '../TextEditorIcon';
import { normalizeHref } from './helpers';
import styles from './styles.module.css';

type CreateLinkButtonProps = {
    editor: Editor;
};

export default function CreateLinkButton({ editor }: CreateLinkButtonProps) {
    const hasLink = editor.isActive('link');
    const [open, setOpen] = useState(false);
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const [isNewTab, setNewTab] = useState(false);
    const [href, setHref] = useState('');
    const ranges = useRef<Range[]>([]);

    const { t } = useAppTranslation();

    // NOTE (MB) When we input the URL we lose the selected area that supposed to become a link so
    // we need to save the selection and then when we have our URL ready restore
    // it back (i.e. select the same range again)

    const saveSelection = () => {
        const sel = window.getSelection();
        ranges.current = [];
        if (!sel) return;
        for (let i = 0; i < sel.rangeCount; i++) {
            ranges.current.push(sel.getRangeAt(i));
        }
    };

    const restoreSelection = () => {
        const sel = window.getSelection();
        if (!sel) return;
        sel.removeAllRanges();
        for (const r of ranges.current) {
            sel.addRange(r);
        }
    };

    const addLink = useCallback(() => {
        if (href.trim() !== '') {
            restoreSelection();
            editor
                .chain()
                .focus()
                .extendMarkRange('link')
                .setLink({
                    href: normalizeHref(href),
                    target: isNewTab ? '_blank' : undefined,
                })
                .run();
        }
        setOpen(false);
    }, [editor, href, isNewTab]);

    const onEditorIconPress = useCallback(() => {
        setOpen(true);
        if (editor.isActive('link')) {
            const attrs = editor.getAttributes('link');
            setHref(attrs.href);
            setNewTab(attrs.target === '_blank');
        } else {
            setHref('');
            setNewTab(true);
        }
    }, [editor]);

    return (
        <>
            <TextEditorIcon
                hint={hasLink ? t('richTextEditor.editLink') : t('richTextEditor.insertLink')}
                ref={setAnchorEl}
                iconComponent={UrlIcon}
                onClicked={onEditorIconPress}
                checked={hasLink}
            />
            <TextEditorIcon
                hint={t('richTextEditor.removeLink')}
                iconComponent={BrokenLinkIcon}
                onClicked={() => {
                    if (hasLink) {
                        editor.chain().focus().extendMarkRange('link').unsetLink().run();
                    }
                }}
            />
            {anchorEl && (
                <Popover
                    transitionDuration={100}
                    open={open}
                    onClose={() => setOpen(false)}
                    anchorEl={anchorEl}
                    anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
                    classes={{ paper: styles.container }}
                >
                    <TextFormField
                        value={href}
                        onChange={(e) => setHref(e.target.value)}
                        name="url"
                        onFocus={saveSelection}
                        onEnterPress={addLink}
                        label={t('richTextEditor.url')}
                        placeholder={t('richTextEditor.urlPlaceholder')}
                    />
                    <Box marginTop={1} display="flex" alignItems="center">
                        <Checkbox
                            className={styles.checkbox}
                            checked={isNewTab}
                            onChange={(e) => {
                                setNewTab(e.target.checked);
                            }}
                        />
                        <SpanLabel>{t('richTextEditor.newLink')}</SpanLabel>
                    </Box>
                    <Button
                        disabled={href.trim() === ''}
                        className={styles.button}
                        onClick={addLink}
                        label={t('richTextEditor.insertLink')}
                        cmosVariant={'filled'}
                        color={'var(--cm1)'}
                    />
                </Popover>
            )}
        </>
    );
}

const SpanLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
}));
