import { styled } from '@mui/material';
import { Editor, EditorContent } from '@tiptap/react';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import React, { useCallback, useRef } from 'react';

type RichTextEditorContentProps = {
    editor: Editor;
    isFullscreen: boolean;
    className?: string;
    onBlur?: () => void;
};

export default function RichTextEditorContent({
    editor,
    isFullscreen,
    className,
    onBlur,
}: RichTextEditorContentProps) {
    const anchorHint = useRef<HTMLDivElement | null>(null);

    const onMouseLeave = useCallback(() => {
        if (anchorHint.current) {
            anchorHint.current.style.display = 'none';
        }
    }, []);

    const onMouseMove = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;
        if (anchorHint.current === el) return;
        // NOTE (MB) I don't know how to make a floating hint on top of the link using tiptap framework
        // that's why we have this little hack
        if (el instanceof HTMLAnchorElement) {
            if (anchorHint.current) {
                if (anchorHint.current.innerText !== el.href) {
                    anchorHint.current.innerText = el.href ?? '';
                }
                anchorHint.current.style.display = 'block';
                anchorHint.current.style.top = `${e.clientY}px`;
                anchorHint.current.style.left = `${e.clientX}px`;
            }
        } else {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    return (
        <>
            <StyledEditorContent
                onMouseMove={onMouseMove}
                onMouseLeave={onMouseLeave}
                onBlur={onBlur}
                className={className}
                editor={editor}
                isFullscreen={isFullscreen}
            />
            <DivAnchorHint ref={anchorHint} />
        </>
    );
}

const StyledEditorContent = styled(EditorContent, {
    shouldForwardProp: (prop) => !['isFullscreen'].includes(prop as string),
})<{ isFullscreen: boolean }>(({ theme, isFullscreen }) => ({
    cursor: 'text',
    minHeight: 65,
    backgroundColor: theme.palette.neutral[1],
    border: `1px solid ${theme.palette.neutral[3]}`,
    borderRadius: 10,
    overflowY: 'scroll',
    ...scrollbarStyle(),

    '& > .ProseMirror': {
        padding: '1px 15px', // prevent margin callapse https://stackoverflow.com/questions/19718634/how-to-disable-margin-collapsing
    },

    '& > .ProseMirror:focus': {
        outline: 'none',
    },

    '& > .ProseMirror:not(:focus)': {
        '& > :first-child::before': {
            ...theme.typography.h6Inter,
            color: theme.palette.neutral[5],
            content: 'attr(data-placeholder)',
            position: 'absolute',
            pointerEvents: 'none',
        },
    },

    maxHeight: isFullscreen ? 'initial' : 300,

    ...(isFullscreen
        ? {
              height: '70vh',
              borderColor: theme.palette.neutral[4],
          }
        : undefined),
}));

const DivAnchorHint = styled('div')(({ theme }) => ({
    background: 'rgba(0,0,0,0.4)',
    borderRadius: 100,
    padding: 3,
    position: 'fixed',
    transform: 'translate(-50%, 10px)',
    pointEvents: 'none',
    display: 'none',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[2],
    fontWeight: 400,
}));
