import { Box, Skeleton } from '@mui/material';
import useForceRender from 'common/hooks/useForceRender';
import React, { ComponentProps, useEffect } from 'react';

const getTextEditor = () => import('./RichTextEditor');
const ActualComponent = React.lazy(getTextEditor);
let richTextEditorLoaded = false;

type RichTextEditor = Awaited<ReturnType<typeof getTextEditor>>['default'];
type RichTextEditorProps = ComponentProps<RichTextEditor>;

function RichTextEditorSkeleton() {
    const ICON_SIZE = 28;

    return (
        <Box sx={{ cursor: 'progress' }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE * 6,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
                <Skeleton
                    sx={{
                        height: ICON_SIZE,
                        width: ICON_SIZE,
                        marginBottom: '5px',
                        transform: 'none',
                    }}
                />
            </Box>
            <Skeleton sx={{ height: 62, transform: 'none', marginTop: 1 }} />
        </Box>
    );
}

export default function RichTextEditor(props: RichTextEditorProps) {
    const fr = useForceRender();
    useEffect(() => {
        if (!richTextEditorLoaded) {
            getTextEditor().then(() => {
                richTextEditorLoaded = true;
                fr();
            });
        }
    }, [fr]);

    if (!richTextEditorLoaded) {
        return <RichTextEditorSkeleton />;
    }

    return <ActualComponent {...props} />;
}
