import { styled } from '@mui/material';
import { Editor } from '@tiptap/core';
import Bold from '@tiptap/extension-bold';
import BulletList from '@tiptap/extension-bullet-list';
import CharacterCount from '@tiptap/extension-character-count';
import Code from '@tiptap/extension-code';
import CodeBlock from '@tiptap/extension-code-block';
import DocumentEditor from '@tiptap/extension-document';
import Heading from '@tiptap/extension-heading';
import History from '@tiptap/extension-history';
import Italic from '@tiptap/extension-italic';
import Link from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import Paragraph from '@tiptap/extension-paragraph';
import Placeholder from '@tiptap/extension-placeholder';
import Strike from '@tiptap/extension-strike';
import TextEditor from '@tiptap/extension-text';
import TextAlign from '@tiptap/extension-text-align';
import Typography from '@tiptap/extension-typography';
import Underline from '@tiptap/extension-underline';
import { BubbleMenu, Extensions, useEditor } from '@tiptap/react';
import clsx from 'clsx';
import { AlignCenterIcon } from 'common/components/Icons/AlignCenterIcon';
import { AlignLeftIcon } from 'common/components/Icons/AlignLeftIcon';
import { AlignRightIcon } from 'common/components/Icons/AlignRightIcon';
import { BoldIcon } from 'common/components/Icons/BoldIcon';
import { CodeIcon } from 'common/components/Icons/CodeIcon';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import { DotListIcon } from 'common/components/Icons/DotListIcon';
import { ExpandIcon } from 'common/components/Icons/ExpandIcon';
import { ItalicIcon } from 'common/components/Icons/ItalicIcon';
import { JustifiedIcon } from 'common/components/Icons/JustifiedIcon';
import { MoveLeftIcon } from 'common/components/Icons/MoveLeftIcon';
import { MoveRightIcon } from 'common/components/Icons/MoveRightIcon';
import { NumbersListIcon } from 'common/components/Icons/NumbersListIcon';
import { PasteIcon } from 'common/components/Icons/PasteIcon';
import { ScissorsIcon } from 'common/components/Icons/ScissorsIcon';
import { StrikeIcon } from 'common/components/Icons/StrikeIcon';
import { UnderlinedIcon } from 'common/components/Icons/UnderlinedIcon';
import React, { useEffect, useState } from 'react';
import CreateLinkButtons from './CreateLinkButtons';
import RichTextEditorContent from './RichTextEditorContent';
import SourceCodeModal from './SourceCodeModal';
import TextEditorIcon from './TextEditorIcon';
import TypographyDropdown from './TypographyDropdown';
import { DisableNL, copyToClipboard, getClipboardContent, isFirefox } from './helpers';
import styles from './styles.module.css';

type RichTextEditorClasses = {
    toolbar?: string;
    content?: string;
};

type ChangeCallbackBehavior = 'onUpdate' | 'onBlur';

export type HtmlChangeSource = 'onBlur' | 'onUpdate' | 'sourceChanged';

export type TextEditorButtons =
    | 'bold'
    | 'italic'
    | 'underline'
    | 'strike'
    | 'alignment'
    | 'bulletList'
    | 'orderedList'
    | 'typography'
    | 'link'
    | 'clipboard'
    | 'history'
    | 'code'
    | 'sourceCode'
    | 'expand';

type RichTextEditorProps = {
    html: string;
    placeholder?: string;
    onHtmlChange: (newHtml: string, source: HtmlChangeSource, editor: Editor) => void;
    onLengthChange?: (length: number) => void;
    onBlur?: () => void;
    editorButtons?: TextEditorButtons[];
    readOnly?: boolean;
    className?: string;
    classes?: RichTextEditorClasses;
    sourceCodePopupTitle?: string;
    changeCallbackBehavior?: ChangeCallbackBehavior;
    maxTextLength?: number;
    editorRef?: React.MutableRefObject<Editor | null> | React.RefCallback<Editor | null>;
    modifyExtensions?: (extensions: Extensions) => Extensions;
    customButtons?: React.ReactNode[];

    // special options (that you probable don't need in most cases)

    /**
     * @description when set to true multiple consecutive spaces are removed from resulting HTML value (only when setContent is called directly)
     */
    _collapseWhitespace?: boolean;

    /**
     * @description disables Enter press (and newline as well). New line can still be inserted via Ctrl+V
     */
    _disableEnter?: boolean;
};

export default function RichTextEditor({
    html,
    placeholder,
    onHtmlChange,
    editorButtons = [
        'bold',
        'italic',
        'underline',
        'alignment',
        'bulletList',
        'orderedList',
        'typography',
        'link',
        'clipboard',
        'history',
        'code',
        'sourceCode',
        'expand',
    ],
    readOnly,
    className,
    classes,
    changeCallbackBehavior = 'onBlur',
    sourceCodePopupTitle,
    maxTextLength,
    editorRef,
    modifyExtensions,
    onBlur,
    customButtons,
    _collapseWhitespace,
    _disableEnter,
}: RichTextEditorProps) {
    const [sourceCodeOpen, setSourceCodeOpen] = useState(false);
    const [isFullscreen, setFullscreen] = useState(false);

    let extensions: Extensions = [TextEditor, Paragraph, History];

    if (_disableEnter) {
        extensions.unshift(
            DocumentEditor.configure({
                content: 'block',
            })
        );
        extensions.push(DisableNL);
    } else {
        extensions.unshift(DocumentEditor);
    }
    if (editorButtons.includes('bold')) {
        extensions.push(Bold);
    }
    if (editorButtons.includes('italic')) {
        extensions.push(Italic);
    }
    if (editorButtons.includes('underline')) {
        extensions.push(Underline);
    }
    if (editorButtons.includes('strike')) {
        extensions.push(Strike);
    }
    if (editorButtons.includes('typography') || editorButtons.includes('alignment')) {
        extensions.push(Typography);
        if (editorButtons.includes('typography')) extensions.push(Heading);
    }
    if (editorButtons.includes('alignment')) {
        extensions.push(
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            })
        );
    }
    if (editorButtons.includes('bulletList') || editorButtons.includes('orderedList')) {
        extensions.push(ListItem);
    }
    if (editorButtons.includes('bulletList')) {
        extensions.push(BulletList);
    }
    if (editorButtons.includes('orderedList')) {
        extensions.push(OrderedList);
    }
    if (editorButtons.includes('link')) {
        extensions.push(
            Link.configure({
                openOnClick: false,
                HTMLAttributes: {
                    rel: 'noopener',
                },
            })
        );
    }
    if (editorButtons.includes('code')) {
        extensions.push(Code);
        extensions.push(CodeBlock);
    }
    if (placeholder) {
        extensions.push(
            Placeholder.configure({
                placeholder: placeholder,
            })
        );
    }
    if (modifyExtensions) {
        extensions = modifyExtensions(extensions);
    }

    if (maxTextLength) {
        extensions.push(
            CharacterCount.configure({
                limit: maxTextLength,
            })
        );
    }

    const editor = useEditor(
        {
            extensions,
            content: html,
            parseOptions: {
                preserveWhitespace: !_collapseWhitespace,
            },
            onUpdate: (props) => {
                if (changeCallbackBehavior !== 'onUpdate') return;
                const h = props.editor.getHTML();
                onHtmlChange(h, 'onUpdate', props.editor);
            },
            editable: !readOnly,
        },
        [changeCallbackBehavior, readOnly]
    );
    useEffect(() => {
        if (editorRef === null || editorRef === undefined) return;
        if (typeof editorRef === 'object') editorRef.current = editor;
        else editorRef(editor);
    }, [editor, editorRef]);

    const showButton = (value: TextEditorButtons) => editorButtons.includes(value);
    const showGroup = (values: TextEditorButtons[]) =>
        editorButtons.some((b) => values.includes(b));

    useEffect(() => {
        if (!editor || editor.isDestroyed) return;
        const current = editor.getHTML();
        if (current !== html) {
            editor.commands.setContent(html, undefined, {
                preserveWhitespace: !_collapseWhitespace,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [html, editor]);
    useEffect(() => {
        if (!editor || changeCallbackBehavior !== 'onBlur') return () => {};

        const callback = () => {
            if (onHtmlChange) {
                onHtmlChange(editor.getHTML(), 'onBlur', editor);
            }
        };

        editor.on('blur', callback);
        return () => editor.off('blur', callback);
    }, [editor, onHtmlChange, changeCallbackBehavior, maxTextLength]);

    //NOTE (AS) Hack to make editor react on placeholder change. Taken from there: https://github.com/ueberdosis/tiptap/issues/1935
    useEffect(() => {
        if (editor !== null && placeholder !== '') {
            editor.extensionManager.extensions.filter(
                (extension) => extension.name === 'placeholder'
            )[0].options['placeholder'] = placeholder;
            editor.view.dispatch(editor.state.tr);
        }
    }, [placeholder]);

    // NOTE (MB) editor.isDestroyed is a hack: https://github.com/ueberdosis/tiptap/issues/1451#issuecomment-875096103
    if (editor === null || editor.isDestroyed) return null;

    return (
        <div className={clsx(isFullscreen && styles.fullscreen, className)}>
            <DivToolbar className={classes?.toolbar}>
                {showGroup(['bold', 'italic', 'underline']) ? (
                    <DivButtonGroup>
                        {showButton('bold') ? (
                            <TextEditorIcon
                                hint="Ctrl+B"
                                onClicked={() => editor.chain().focus().toggleBold().run()}
                                checked={editor.isActive('bold')}
                                iconComponent={BoldIcon}
                            />
                        ) : null}

                        {showButton('italic') ? (
                            <TextEditorIcon
                                hint="Ctrl+I"
                                onClicked={() => editor.chain().focus().toggleItalic().run()}
                                checked={editor.isActive('italic')}
                                iconComponent={ItalicIcon}
                            />
                        ) : null}

                        {showButton('underline') ? (
                            <TextEditorIcon
                                hint="Ctrl+U"
                                onClicked={() => editor.chain().focus().toggleUnderline().run()}
                                checked={editor.isActive('underline')}
                                iconComponent={UnderlinedIcon}
                            />
                        ) : null}

                        {showButton('strike') ? (
                            <TextEditorIcon
                                hint="Ctrl+Shift+X"
                                onClicked={() => editor.chain().focus().toggleStrike().run()}
                                checked={editor.isActive('strike')}
                                iconComponent={StrikeIcon}
                            />
                        ) : null}
                    </DivButtonGroup>
                ) : null}
                {showGroup(['alignment']) ? (
                    <DivButtonGroup>
                        <TextEditorIcon
                            onClicked={() => editor.chain().focus().setTextAlign('left').run()}
                            checked={editor.isActive({ textAlign: 'left' })}
                            iconComponent={AlignLeftIcon}
                        />
                        <TextEditorIcon
                            onClicked={() => editor.chain().focus().setTextAlign('center').run()}
                            checked={editor.isActive({ textAlign: 'center' })}
                            iconComponent={AlignCenterIcon}
                        />
                        <TextEditorIcon
                            onClicked={() => editor.chain().focus().setTextAlign('justify').run()}
                            checked={editor.isActive({ textAlign: 'justify' })}
                            iconComponent={JustifiedIcon}
                        />
                        <TextEditorIcon
                            onClicked={() => editor.chain().focus().setTextAlign('right').run()}
                            checked={editor.isActive({ textAlign: 'right' })}
                            iconComponent={AlignRightIcon}
                        />
                    </DivButtonGroup>
                ) : null}
                {showGroup(['bulletList', 'orderedList']) ? (
                    <DivButtonGroup>
                        {showButton('bulletList') ? (
                            <TextEditorIcon
                                onClicked={() => editor.chain().focus().toggleBulletList().run()}
                                checked={editor.isActive('bulletList')}
                                iconComponent={DotListIcon}
                            />
                        ) : null}

                        {showButton('orderedList') ? (
                            <TextEditorIcon
                                onClicked={() => editor.chain().focus().toggleOrderedList().run()}
                                checked={editor.isActive('orderedList')}
                                iconComponent={NumbersListIcon}
                            />
                        ) : null}
                    </DivButtonGroup>
                ) : null}
                {showButton('typography') ? (
                    <DivButtonGroup>
                        <TypographyDropdown editor={editor} />
                    </DivButtonGroup>
                ) : null}
                {showButton('link') ? (
                    <DivButtonGroup>
                        <CreateLinkButtons editor={editor} />
                    </DivButtonGroup>
                ) : null}
                {showGroup(['clipboard']) ? (
                    <DivButtonGroup>
                        <TextEditorIcon
                            hint="Ctrl+X"
                            iconComponent={ScissorsIcon}
                            onClicked={() => {
                                copyToClipboard().then(() =>
                                    editor.chain().focus().deleteSelection().run()
                                );
                            }}
                        />
                        <TextEditorIcon
                            hint="Ctrl+C"
                            iconComponent={CopyIcon}
                            onClicked={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                copyToClipboard();
                            }}
                        />

                        {/* Firefox does not support pasting from the clipboard */}
                        {!isFirefox() && (
                            <TextEditorIcon
                                hint="Ctrl+V"
                                iconComponent={PasteIcon}
                                onClicked={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    if (isFirefox()) {
                                        document.execCommand('paste');
                                    } else {
                                        getClipboardContent().then((content) =>
                                            editor.chain().focus().insertContent(content).run()
                                        );
                                    }
                                }}
                            />
                        )}
                    </DivButtonGroup>
                ) : null}
                {showGroup(['history', 'code', 'expand']) ? (
                    <DivButtonGroup>
                        {showButton('history') ? (
                            <TextEditorIcon
                                hint="Ctrl+Z"
                                disabled={!editor.can().undo()}
                                iconComponent={MoveLeftIcon}
                                onClicked={() => editor.commands.undo()}
                            />
                        ) : null}

                        {showButton('history') ? (
                            <TextEditorIcon
                                hint="Ctrl+Shift+Z"
                                disabled={!editor.can().redo()}
                                iconComponent={MoveRightIcon}
                                onClicked={() => editor.commands.redo()}
                            />
                        ) : null}

                        {showButton('code') ? (
                            <TextEditorIcon
                                iconComponent={CodeIcon}
                                onClicked={() => setSourceCodeOpen(true)}
                            />
                        ) : null}

                        {showButton('expand') ? (
                            <TextEditorIcon
                                checked={isFullscreen}
                                iconComponent={ExpandIcon}
                                onClicked={() => setFullscreen(!isFullscreen)}
                            />
                        ) : null}
                    </DivButtonGroup>
                ) : null}
                {customButtons?.length && <DivButtonGroup>{customButtons}</DivButtonGroup>}
            </DivToolbar>
            <RichTextEditorContent
                className={classes?.content}
                editor={editor}
                isFullscreen={isFullscreen}
                onBlur={onBlur}
            />
            {sourceCodeOpen && showButton('sourceCode') && (
                <SourceCodeModal
                    title={sourceCodePopupTitle}
                    onClose={() => setSourceCodeOpen(false)}
                    onSourceChanged={() => {
                        if (onHtmlChange) onHtmlChange(editor.getHTML(), 'sourceChanged', editor);
                    }}
                    editor={editor}
                />
            )}
            {editor && !editor.isDestroyed && showButton('link') && (
                <BubbleMenu tippyOptions={{ duration: 100, animateFill: true }} editor={editor}>
                    <DivBubble>
                        <CreateLinkButtons editor={editor} />
                    </DivBubble>
                </BubbleMenu>
            )}
        </div>
    );
}

const DivToolbar = styled('div')(({ theme }) => ({
    border: `1px solid ${theme.palette.neutral[3]}`,
    borderRadius: 10,
    display: 'flex',
    gap: 6,
    backgroundColor: theme.palette.neutral[1],
    padding: 6,
    marginBottom: 10,
    flexWrap: 'wrap',
    userSelect: 'none',
}));

const DivButtonGroup = styled('div')(({ theme }) => ({
    display: 'flex',
    '&:not(:last-child)': {
        borderRight: `1px solid ${theme.palette.neutral[4]}`,
        paddingRight: 2,
    },
    alignItems: 'center',
    gap: 6,
}));

const DivBubble = styled('div')(({ theme }) => ({
    display: 'flex',
    backgroundColor: theme.palette.neutral[1],
    borderRadius: 6,
    boxShadow: `0 3px 6px ${theme.palette.neutral[4]}`,
}));
