import { Popover, styled } from '@mui/material';
import { Level } from '@tiptap/extension-heading';
import { Editor } from '@tiptap/react';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { getTypographyType } from './helpers';
import styles from './styles.module.css';

type TypographyDropdownProps = {
    editor: Editor;
};

export default function TypographyDropdown({ editor }: TypographyDropdownProps) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState(false);
    const { t } = useAppTranslation();

    const setHeading = (level: Level) => {
        editor.chain().focus().setHeading({ level }).run();
        setOpen(false);
    };
    const setParagraph = () => {
        editor.chain().focus().setParagraph().run();
        setOpen(false);
    };

    const typographyType = getTypographyType(editor);
    const text =
        typographyType === null
            ? t('richTextEditor.multiple')
            : typographyType === 'p'
            ? t('richTextEditor.p')
            : t('richTextEditor.h', { level: typographyType });

    return (
        <>
            <DivDropdown onClick={() => setOpen(true)} ref={setAnchorEl}>
                <span>{text}</span>
                <DownIcon />
            </DivDropdown>
            <Popover
                transitionDuration={150}
                classes={{
                    paper: styles.list,
                }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    horizontal: 'left',
                    vertical: 'top',
                }}
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: 0,
                            boxShadow: 'none',
                            border: '1px solid var(--neutral3)',
                            borderTop: 'none',
                        },
                    },
                }}
                open={Boolean(open && anchorEl)}
                onClose={() => setOpen(false)}
                anchorEl={anchorEl}
            >
                <ul>
                    <LiItem key="p" onClick={setParagraph}>
                        <p>{t('richTextEditor.p')}</p>
                    </LiItem>
                    <LiItem onClick={() => setHeading(1)}>
                        <h1>{t('richTextEditor.h', { level: 1 })}</h1>
                    </LiItem>
                    <LiItem onClick={() => setHeading(2)}>
                        <h2>{t('richTextEditor.h', { level: 2 })}</h2>
                    </LiItem>
                    <LiItem onClick={() => setHeading(3)}>
                        <h3>{t('richTextEditor.h', { level: 3 })}</h3>
                    </LiItem>
                    <LiItem onClick={() => setHeading(4)}>
                        <h4>{t('richTextEditor.h', { level: 4 })}</h4>
                    </LiItem>
                    <LiItem onClick={() => setHeading(5)}>
                        <h5>{t('richTextEditor.h', { level: 5 })}</h5>
                    </LiItem>
                    <LiItem onClick={() => setHeading(6)}>
                        <h6>{t('richTextEditor.h', { level: 6 })}</h6>
                    </LiItem>
                </ul>
            </Popover>
        </>
    );
}

const LiItem = styled('li')(({ theme }) => ({
    padding: 5,
    cursor: 'pointer',
    color: theme.palette.neutral[7],
    ...theme.typography.h5Inter,
    '&:hover': {
        backgroundColor: 'rgba(0,0,0,0.02)',
        color: theme.palette.primary.light,
    },
    '&:focus': {
        backgroundColor: 'rgba(0,0,0,0.07)',
        outline: '0 0 1px solid black       ',
    },
    '& h4, h5, h6': {
        margin: '2px 0',
    },
    '& h1, h2, h3': {
        margin: '8px 0',
    },
    '& p': {
        margin: '6px 0',
    },
}));

const DivDropdown = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[7],
    fontWeight: 400,
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    padding: '0 6px',
    borderRadius: '5px',
    width: 135,
    justifyContent: 'space-between',
    userSelect: 'none',

    '&:hover': {
        backgroundColor: 'rgba(0,0,0,0.05)',
    },
}));
