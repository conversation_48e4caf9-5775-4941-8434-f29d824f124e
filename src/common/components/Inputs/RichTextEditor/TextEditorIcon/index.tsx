import { styled } from '@mui/material';
import { IconProps } from 'common/components/Icons/Icon';
import React, { ComponentType } from 'react';

type TextEditorIconProps = {
    checked?: boolean;
    onClicked: (e: React.MouseEvent<HTMLButtonElement>) => void;
    iconComponent: ComponentType<IconProps>;
    disabled?: boolean;
    hint?: string;
};

const TextEditorIcon = React.forwardRef(
    (
        { checked, iconComponent: IconComponent, onClicked, disabled, hint }: TextEditorIconProps,
        ref: React.Ref<HTMLButtonElement>
    ) => {
        return (
            <>
                <StyledButton
                    title={hint}
                    disabled={disabled}
                    onClick={onClicked}
                    ref={ref}
                    checked={!!checked}
                >
                    <IconComponent fill={checked ? 'var(--cm1)' : 'var(--neutral7)'} />
                </StyledButton>
            </>
        );
    }
);

const StyledButton = styled('button', {
    shouldForwardProp: (prop) => !['checked'].includes(prop as string),
})<{ checked: boolean }>(({ checked }) => ({
    all: 'initial',
    display: 'flex',
    height: 28,
    width: 28,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    cursor: 'pointer',
    backgroundColor: checked ? 'rgba(211, 224, 255, 0.67)' : 'initial',

    '& > input': {
        border: 'none',
        height: 0,
        width: 0,
        padding: 0,
        margin: 0,
    },

    '&:hover': {
        backgroundColor: 'rgba(0,0,0,0.03)',
    },

    '&:disabled': {
        backgroundColor: 'transparent',
        cursor: 'not-allowed',
        opacity: 0.5,
    },
}));

export default TextEditorIcon;
