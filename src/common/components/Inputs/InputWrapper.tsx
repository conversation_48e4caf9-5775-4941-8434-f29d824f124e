import { FormControl, FormControlProps, styled, useForkRef } from '@mui/material';
import React, { ForwardedRef, forwardRef, useEffect, useRef } from 'react';
import { InputLabel, InputLabelProps } from './InputLabel';

export type InputWrapperOrientation = 'column' | 'row';

export type InputWrapperProps = FormControlProps & {
    children?: React.ReactNode;
    label?: React.ReactNode;
    classes?: {
        label?: string;
    };
    isRequired?: boolean;
    disabled?: boolean;
    orientation?: InputWrapperOrientation;
    readonly?: boolean;
    className?: string;
    isInvalid?: boolean;
    showValidationIndicators?: boolean;

    rightAdornment?: React.ReactNode;
    hideRoundIndicator?: boolean;
    tooltipText?: string;
    htmlFor?: string;

    /**
     * If set to true, the wrapper element will shake side to side when isInvalid set to true, only happens after first render.
     */
    shakeOnInvalid?: boolean;

    slotProps?: {
        label?: Partial<InputLabelProps>;
    };
};

const InputContainer = styled('div')({
    position: 'relative',
    width: '100%',
    display: 'flex',
    justifyContent: 'stretch',
});

const Control = styled(FormControl)({
    display: 'block',
    margin: 'initial',
});

const RequiredIndicator = styled('div')(({ theme }) => ({
    height: 8,
    width: 8,
    borderRadius: 100,
    background: theme.palette.primary.main,
    position: 'absolute',
    right: -4,
    top: -4,
}));

const RightAdornment = styled('div')({
    height: '100%',
    position: 'absolute',
    right: 8,
    top: 0,
    display: 'flex',
    alignItems: 'center',
});

/**
 * InputWrapper is a component that provides a label and puts its children elements under that label.
 * Essentially it's just a FormControl from MUI.
 */
const InputWrapper = forwardRef(
    (
        {
            readonly,
            disabled = false,
            isRequired = false,
            hideRoundIndicator,
            orientation = 'column',
            children,
            label,
            classes,
            showValidationIndicators,
            fullWidth = false,
            rightAdornment,
            tooltipText,
            isInvalid,
            htmlFor,
            slotProps = {},
            shakeOnInvalid = false,
            ...props
        }: InputWrapperProps,
        ref: ForwardedRef<HTMLDivElement>
    ) => {
        const shakeOnInvalidRef = useShakeOnInvalid(shakeOnInvalid && isInvalid === true);
        const forkedRef = useForkRef(shakeOnInvalidRef, ref);

        return (
            <Control
                ref={forkedRef}
                error={isInvalid}
                fullWidth={fullWidth}
                disabled={disabled}
                {...props}
            >
                <InputLabel
                    className={classes?.label}
                    showValidationIndicators={showValidationIndicators}
                    isRequired={isRequired}
                    tooltipText={tooltipText}
                    htmlFor={htmlFor}
                    {...slotProps.label}
                >
                    {label}
                </InputLabel>

                <InputContainer>
                    {children}
                    {showValidationIndicators && isRequired && !hideRoundIndicator && (
                        <RequiredIndicator />
                    )}
                    {rightAdornment && <RightAdornment>{rightAdornment}</RightAdornment>}
                </InputContainer>
            </Control>
        );
    }
);
export default InputWrapper;

const SHAKE_AMPLITUDE = 3;

function useShakeOnInvalid(shouldShake: boolean): React.Ref<HTMLDivElement | null> {
    const rootRef = useRef<HTMLDivElement | null>(null);
    const animation = useRef<Animation | null>(null);
    const isFirstUpdateRef = useRef(true);

    useEffect(() => {
        if (!shouldShake || !rootRef.current) return;

        if (isFirstUpdateRef.current) {
            isFirstUpdateRef.current = false;
            return;
        }

        animation.current?.cancel();
        animation.current = rootRef.current.animate(
            [
                {
                    transform: `translateX(${SHAKE_AMPLITUDE}px)`,
                },
                {
                    transform: `translateX(${-SHAKE_AMPLITUDE}px)`,
                },
                {
                    transform: `translateX(${SHAKE_AMPLITUDE}px)`,
                },
                {
                    transform: `translateX(${0}px)`,
                },
            ],
            { duration: 200 }
        );
        animation.current.addEventListener('finish', () => (animation.current = null));
    }, [shouldShake]);

    return rootRef;
}
