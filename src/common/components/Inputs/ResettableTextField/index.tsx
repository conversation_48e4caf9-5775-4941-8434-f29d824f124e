import { TextFieldProps } from 'common/components/mui';
import React, { forwardRef, useCallback, useEffect, useState } from 'react';
import InputWrapper from '../InputWrapper';
import ResettableTextInput from './ResettableTextInput';

export type ResettableTextFormFieldProps = Omit<
    TextFieldProps,
    'IconRight' | 'onIconRightClick' | 'value'
> & {
    value: string;
    wrapperRef?: React.Ref<HTMLDivElement>;
    onSave: (value: string) => Promise<void>;
    placeholder?: string;
    name?: string;
    required?: boolean;
    isInvalid?: boolean;
    showValidationIndicators?: boolean;
    maxLength?: number;
    onChange?: TextFieldProps['onChange'];
    onEdit?: (value: string) => string;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    onValidate?: (value: string) => boolean;
    hasCharacterCounter?: boolean;
    disabled?: boolean;
    showEditButton?: boolean;
    block?: boolean;
    formatting?: (value: string) => string;
};

const ResettableTextFormField = forwardRef(
    (
        {
            label,
            required,
            block = true,
            wrapperRef,
            onValidate,
            isInvalid,
            formatting,
            disabled,
            ...props
        }: ResettableTextFormFieldProps,
        ref: React.Ref<HTMLDivElement>
    ) => {
        const [isInvalidComputed, setIsInvalidComputed] = useState(false);

        const validate = useCallback(
            (value: string) => {
                if (onValidate) {
                    const isInvalid = onValidate(value);
                    setIsInvalidComputed(isInvalid);
                }
            },
            [onValidate]
        );

        useEffect(() => {
            if (isInvalid === undefined) return;
            setIsInvalidComputed(isInvalid);
        }, [isInvalid]);

        return (
            <InputWrapper
                ref={wrapperRef}
                fullWidth={block}
                isInvalid={isInvalidComputed}
                label={label}
                disabled={disabled}
            >
                <ResettableTextInput
                    ref={ref}
                    isInvalid={isInvalidComputed}
                    isRequired={required}
                    onValidate={validate}
                    formatting={formatting}
                    disabled={disabled}
                    {...props}
                />
            </InputWrapper>
        );
    }
);

export default ResettableTextFormField;
