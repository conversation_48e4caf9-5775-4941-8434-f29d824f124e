import {
    Box,
    CircularProgress,
    IconButton,
    InputAdornment,
    inputAdornmentClasses,
    styled,
} from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { handleCommonKeyDowns } from 'common/components/Inputs/utils';
import { TextField, TextFieldProps } from 'common/components/mui';
import { Colors } from 'common/styles/Colors';
import React, { ChangeEvent, useCallback, useRef, useState } from 'react';

type ResettableTextInputProps = Omit<TextFieldProps, 'IconRight' | 'onIconRightClick' | 'value'> & {
    onSave: (value: string) => Promise<void>;
    onEdit?: (value: string) => string;
    onValidate?: (value: string) => void;
    formatValue?: string;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    hasCharacterCounter?: boolean;
    isRequired?: boolean;
    isInvalid?: boolean;
    value?: string;
    showEditButton?: boolean;
    maxLength?: number;
    formatting?: (value: string) => string;
    disabled?: boolean;
};

export default function ResettableTextInput({
    value,
    onSave,
    onChange,
    onBlur,
    onFocus,
    onEdit,
    onKeyDown,
    onEnterPress,
    onEscPress,
    onValidate,
    hasCharacterCounter,
    isRequired,
    size = 'medium',
    showEditButton,
    isInvalid,
    maxLength,
    formatting,
    disabled = false,
    ...props
}: ResettableTextInputProps) {
    const [newValue, setNewValue] = useState('');
    const [isEditing, setEditing] = useState(false);
    const [isLoading, setLoading] = useState(false);

    const cancelButtonHover = useRef(false);
    const cancelSave = useRef(false);
    const canSave =
        !cancelSave.current && !isInvalid && (isRequired ? newValue.trim() !== '' : true);

    const saveCallback = useCallback(async () => {
        if (!canSave) return;
        setEditing(false);
        if (newValue === value) return;
        setLoading(true);
        try {
            await onSave(newValue);
        } finally {
            setLoading(false);
        }
    }, [onSave, newValue, value, canSave]);

    const startEditing = useCallback(() => {
        if (disabled) return;

        setEditing(true);
        setNewValue(value || '');
        cancelSave.current = false;
    }, [setEditing, value, disabled]);

    const onIconClickCallback = () => {
        if (disabled) return;
        if (isEditing) {
            saveCallback();
        } else {
            startEditing();
        }
    };

    const cancelCallback = useCallback(() => {
        setEditing(false);
        cancelSave.current = true;
        cancelButtonHover.current = false;
        if (value && onValidate) onValidate(value);
    }, [setEditing, cancelSave, value, onValidate]);

    const onChangeCallback = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            if (onValidate) onValidate(e.target.value);
            if (isEditing) {
                let v = e.target.value;
                if (onEdit) {
                    v = onEdit(v);
                }
                if (typeof maxLength == 'number' && maxLength >= 0 && v.length > maxLength) {
                    v = v.substring(0, maxLength);
                }
                setNewValue(v);
            }
            if (onChange) {
                onChange(e);
            }
        },
        [onValidate, isEditing, onChange, onEdit, maxLength]
    );

    const onBlurCallback = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            if (onBlur) {
                onBlur(e);
            }
            if (!cancelButtonHover.current) saveCallback();
        },
        [onBlur, saveCallback]
    );

    const onFocusCallback = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            if (onFocus) {
                onFocus(e);
            }
            if (!isEditing) startEditing();
        },
        [onFocus, isEditing, startEditing]
    );

    const handleMouseEnter = () => {
        cancelButtonHover.current = true;
    };

    const handleMouseLeave = () => {
        cancelButtonHover.current = false;
    };

    const numberToString = (n: number) => {
        return (Math.floor(n / 10) % 10).toFixed() + (n % 10).toFixed();
    };

    const endAdornment = showEditButton ? (
        <InputAdornment position="end">
            {!isInvalid && (
                <IconButton
                    disabled={disabled}
                    sx={
                        size === 'small'
                            ? { height: '32px', width: '32px', marginRight: -1.5 }
                            : { height: '40px', width: '40px', marginRight: -1.5 }
                    }
                    onClick={onIconClickCallback}
                >
                    {isEditing ? (
                        <CheckIcon fill={disabled ? 'var(--cm4)' : 'var(--cm2)'} />
                    ) : (
                        <EditIcon fill={disabled ? 'var(--cm4)' : 'var(--cm2)'} />
                    )}
                </IconButton>
            )}
            {hasCharacterCounter && maxLength && isEditing && (
                <StyledSpan>
                    {numberToString(newValue?.length)}/{numberToString(maxLength - 1)}
                </StyledSpan>
            )}
        </InputAdornment>
    ) : (
        isEditing && (
            <InputAdornment position="end">
                {!isInvalid && (
                    <IconButton
                        sx={
                            size === 'small'
                                ? { height: '32px', width: '32px', marginRight: -1.5 }
                                : { height: '40px', width: '40px', marginRight: -1.5 }
                        }
                        onClick={onIconClickCallback}
                    >
                        <CheckIcon />
                    </IconButton>
                )}
                {hasCharacterCounter && maxLength && isEditing && (
                    <StyledSpan>
                        {numberToString(newValue?.length)}/{numberToString(maxLength - 1)}
                    </StyledSpan>
                )}
            </InputAdornment>
        )
    );

    const handleKeyDown: typeof onKeyDown = (e) => {
        if (e.key === 'Enter' && props.multiline) {
            return;
        }
        if (onKeyDown) onKeyDown(e);
    };

    return (
        <>
            <StyledTextField
                fullWidth
                onClick={() => !isEditing && startEditing()}
                InputProps={{
                    readOnly: !isEditing,
                    endAdornment: endAdornment,
                    error: isInvalid,
                }}
                onChange={onChangeCallback}
                onBlur={onBlurCallback}
                value={(isEditing ? newValue : formatting ? formatting(value ?? '') : value) ?? ''}
                onKeyDown={handleCommonKeyDowns({
                    onKeyDown: handleKeyDown,
                    onEnterPress: props.multiline
                        ? undefined
                        : (e) => {
                              if (onEnterPress) onEnterPress(e);
                              saveCallback();
                          },
                    onEscPress: (e) => {
                        if (onEscPress) onEscPress(e);
                        cancelCallback();
                    },
                })}
                onFocus={onFocusCallback}
                size={size}
                disabled={disabled}
                {...props}
            />
            {isEditing &&
                (size === 'small' ? (
                    <PaddedIconButton
                        size="small"
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                        onClick={() => cancelCallback()}
                    >
                        <CloseIcon fill={Colors.Neutral6} />
                    </PaddedIconButton>
                ) : (
                    <AlignedIconButton
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                        onClick={() => cancelCallback()}
                    >
                        <CloseIcon fill={Colors.Neutral6} />
                    </AlignedIconButton>
                ))}
            {isLoading && (
                <Box marginLeft={1} display={'flex'} alignItems={'center'}>
                    <CircularProgress size={20} thickness={4} />
                </Box>
            )}
        </>
    );
}

const StyledTextField = styled(TextField)(({ multiline, size }) => ({
    [`& .${inputAdornmentClasses.root}`]: {
        alignSelf: 'start',
        marginTop: multiline
            ? size === 'medium'
                ? '4.5px'
                : '7.5px'
            : size === 'medium'
            ? '20px'
            : '16px',
    },
}));

const AlignedIconButton = styled(IconButton)({
    alignSelf: 'start',
});

const PaddedIconButton = styled(AlignedIconButton)({
    padding: '0 4px',
});

const StyledSpan = styled('span')({
    fontSize: 10,
    textAlign: 'right',
    width: 30,
    marginRight: -10,
    marginTop: 26,
    marginLeft: -20,
});
