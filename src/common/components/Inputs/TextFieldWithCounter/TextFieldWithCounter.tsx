import { InputAdornment, inputAdornmentClasses, styled } from '@mui/material';
import { handleCommonKeyDowns } from 'common/components/Inputs/utils';
import { TextField } from 'common/components/Inputs';
import React, { ChangeEvent, useCallback, useRef, useState, useEffect, KeyboardEvent } from 'react';

type TextFieldWithCounterProps = {
    onSave?: (value: string) => void;
    onCanSave?: (canSave: boolean) => void;
    onEdit?: (value: string) => string;
    onValidate?: (value: string) => void;
    label?: string;
    placeholder?: string;
    formatValue?: string;
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    hasCharacterCounter?: boolean;
    isRequired?: boolean;
    isInvalid?: boolean;
    value?: string;
    showEditButton?: boolean;
    maxLength?: number;
    formatting?: (value: string) => string;
    disabled?: boolean;
    multiline?: boolean;
    rows?: number;
};

export default function TextFieldWithCounter({
    value,
    onSave,
    onCanSave,
    onEdit,
    onEnterPress,
    onEscPress,
    onValidate,
    hasCharacterCounter,
    isRequired,
    showEditButton,
    isInvalid,
    maxLength,
    formatting,
    disabled = false,
    ...props
}: TextFieldWithCounterProps) {
    const [newValue, setNewValue] = useState('');
    const [isEditing, setEditing] = useState(false);
    const [isLengthInvalid, setIsLengthInvalid] = useState(false);

    const cancelButtonHover = useRef(false);
    const cancelSave = useRef(false);
    const canSave =
        !cancelSave.current && !isLengthInvalid && (isRequired ? newValue.trim() !== '' : true);

    const saveCallback = useCallback(async () => {
        if (!canSave) return;
        setEditing(false);
        if (newValue === value) return;
        if (onSave) onSave(newValue);
    }, [onSave, newValue, value, canSave]);

    const startEditing = useCallback(() => {
        if (disabled) return;

        setEditing(true);
        setNewValue(value || '');
        cancelSave.current = false;
    }, [setEditing, value, disabled]);

    const cancelCallback = useCallback(() => {
        setEditing(false);
        cancelSave.current = true;
        cancelButtonHover.current = false;
        if (value && onValidate) onValidate(value);
    }, [setEditing, cancelSave, value, onValidate]);

    const onChangeCallback = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            if (onValidate) onValidate(e.target.value);
            if (isEditing) {
                let v = e.target.value;
                if (onEdit) {
                    v = onEdit(v);
                }
                setIsLengthInvalid(
                    typeof maxLength == 'number' && maxLength >= 0 && v.length > maxLength
                );
                setNewValue(v);
            }
        },
        [onValidate, isEditing, onEdit, maxLength]
    );

    const onBlurCallback = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            if (!cancelButtonHover.current) saveCallback();
        },
        [saveCallback]
    );

    const onFocusCallback = useCallback(
        (e: React.FocusEvent<HTMLInputElement>) => {
            if (!isEditing) startEditing();
        },
        [isEditing, startEditing]
    );

    useEffect(() => {
        if (onCanSave) onCanSave(canSave);
    }, [canSave]);

    const endAdornment = (
        <InputAdornment position="end">
            {hasCharacterCounter && maxLength && isEditing && (
                <StyledSpan>
                    {newValue?.length}/{maxLength}
                </StyledSpan>
            )}
        </InputAdornment>
    );

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && props.multiline) {
            return;
        }
    };

    return (
        <>
            <StyledTextField
                fullWidth
                onClick={() => !isEditing && startEditing()}
                InputProps={{
                    readOnly: !isEditing,
                    endAdornment: endAdornment,
                    error: isInvalid || isLengthInvalid,
                }}
                onChange={onChangeCallback}
                onBlur={onBlurCallback}
                value={(isEditing ? newValue : formatting ? formatting(value ?? '') : value) ?? ''}
                onKeyDown={handleCommonKeyDowns({
                    onKeyDown: handleKeyDown,
                    onEnterPress: props.multiline
                        ? undefined
                        : (e) => {
                              if (onEnterPress) onEnterPress(e);
                              saveCallback();
                          },
                    onEscPress: (e) => {
                        if (onEscPress) onEscPress(e);
                        cancelCallback();
                    },
                })}
                onFocus={onFocusCallback}
                disabled={disabled}
                {...props}
            />
        </>
    );
}

const StyledTextField = styled(TextField)(({ multiline, size }) => ({
    [`& .${inputAdornmentClasses.root}`]: {
        alignSelf: multiline ? 'end' : 'start',
        marginTop: multiline
            ? size === 'medium'
                ? '4.5px'
                : '7.5px'
            : size === 'medium'
            ? '20px'
            : '16px',
    },
}));

const StyledSpan = styled('span')({
    fontSize: 10,
    textAlign: 'right',
    width: 40,
});
