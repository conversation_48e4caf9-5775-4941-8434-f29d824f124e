import { IconButton } from '@mui/material';
import { useState } from 'react';
import { Colors } from '../../../styles/Colors';
import { HideIcon } from '../../Icons/HideIcon';
import { ShowIcon } from '../../Icons/ShowIcon';
import TextFormField, { TextFormFieldProps } from '../TextField';

function PasswordField(props: TextFormFieldProps) {
    const [showPassword, setShowPassword] = useState(false);

    return (
        <TextFormField
            type={showPassword ? 'text' : 'password'}
            endAdornment={
                <IconButton size="small" onClick={() => setShowPassword((x) => !x)}>
                    {showPassword ? (
                        <ShowIcon fill={Colors.CM1} />
                    ) : (
                        <HideIcon fill={Colors.Neutral6} />
                    )}
                </IconButton>
            }
            {...props}
        />
    );
}

export default PasswordField;
