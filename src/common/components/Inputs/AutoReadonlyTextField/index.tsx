import { IconButton } from '@mui/material';
import { CheckIcon } from '../../Icons/CheckIcon';
import { EditIcon } from '../../Icons/EditIcon';
import TextFormField, { TextFormFieldProps } from '../TextField';

export type AutoReadonlyTextFieldProps = TextFormFieldProps & {
    onKeyEnterPress?: React.KeyboardEventHandler<HTMLInputElement>;
    onEditClick?: () => void;
    onSaveClick: () => void;
};

const AutoReadonlyTextField = ({
    onKeyEnterPress,
    onEditClick,
    onSaveClick,
    readonly,
    ...props
}: AutoReadonlyTextFieldProps) => {
    return (
        <TextFormField
            onEnterPress={onKeyEnterPress}
            readonly={readonly}
            endAdornment={
                !readonly ? (
                    <IconButton size="small" onClick={() => onSaveClick && onSaveClick()}>
                        <CheckIcon />
                    </IconButton>
                ) : (
                    <IconButton size="small" onClick={() => onEditClick && onEditClick()}>
                        <EditIcon fill={'var(--neutral6)'} />
                    </IconButton>
                )
            }
            {...props}
        />
    );
};

export default AutoReadonlyTextField;
