import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React from 'react';
import { TextArea, TextAreaProps } from '../TextArea';

type LimitedTextAreaProps = TextAreaProps & {
    maxLength: number;
    rootClassName?: string;
    counterClassName?: string;
    isInvalid?: boolean;
    limitText?: boolean;
    shortCounter?: boolean;
};

export const LimitedTextArea = React.forwardRef(
    (
        {
            maxLength,
            value = '',
            counterClassName,
            rootClassName,
            isInvalid,
            limitText = false,
            shortCounter = false,
            ...props
        }: LimitedTextAreaProps,
        ref: React.ForwardedRef<HTMLTextAreaElement>
    ) => {
        const { t } = useAppTranslation();

        return (
            <DivRoot className={rootClassName}>
                <TextArea
                    {...props}
                    value={value}
                    isInvalid={isInvalid}
                    ref={ref}
                    maxLength={limitText ? maxLength : undefined}
                />

                <DivLabel className={counterClassName}>
                    {value.length}/{maxLength} {!shortCounter && t('commonLabels.maxCharacters')}
                </DivLabel>
            </DivRoot>
        );
    }
);

const DivRoot = styled('div')({
    width: '100%',
    position: 'relative',
});

const DivLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h7Inter,
    fontWeight: 400,
    color: theme.palette.neutral[5],
    marginTop: 5,
}));
