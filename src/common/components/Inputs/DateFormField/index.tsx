import InputWrapper from '../InputWrapper';

import React, { forwardRef } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useValidation } from 'utils/validation';
import DateField, { DateFieldProps } from '../DateField';

export type DateFormFieldProps = MergeTypes<
    {
        fullWidth?: boolean;
        label?: string;
        isRequired?: boolean;
        isInvalid?: boolean;
        showValidationIndicators?: boolean;
    },
    DateFieldProps
>;

const SINGLE_M_REGEX = /(?<!M)M(?!M)/g;
const SINGLE_D_REGEX = /(?<!D)D(?!D)/g;
const SINGLE_d_REGEX = /(?<!d)d(?!d)/g;

function fixFormat(fmt: string): string {
    fmt = fmt
        .replaceAll('D', 'd')
        .replaceAll('Y', 'y')
        .replace('yyyy', 'yy')
        .replace(SINGLE_M_REGEX, 'MM')
        .replace(SINGLE_D_REGEX, 'dd')
        .replace(SINGLE_d_REGEX, 'dd');
    return fmt;
}

const DateFormField = forwardRef(
    (
        {
            fullWidth,
            label,
            isRequired = false,
            isInvalid,
            showValidationIndicators,
            value,
            format,
            disabled,

            ...props
        }: DateFormFieldProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const dateValid = useDateValidation(value, isRequired);
        const isInvalidExt = isInvalid || !dateValid;

        const { internationalization } = useAppSelector(selectSettings);

        format ??= fixFormat(internationalization.dateFormat);

        return (
            <InputWrapper
                disabled={disabled}
                ref={ref}
                showValidationIndicators={showValidationIndicators}
                isRequired={isRequired}
                label={label}
                fullWidth={fullWidth}
                isInvalid={isInvalidExt}
            >
                <DateField
                    {...props}
                    format={format}
                    fullWidth
                    isInvalid={isInvalidExt}
                    value={value}
                    disabled={disabled}
                />
            </InputWrapper>
        );
    }
);

export default DateFormField;

function useDateValidation(date: Date | null | undefined, isRequired: boolean) {
    return useValidation(date, (v) => (isRequired ? !!v : true)).valid !== false;
}
