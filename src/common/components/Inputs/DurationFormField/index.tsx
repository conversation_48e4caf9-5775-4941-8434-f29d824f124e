import { DurationField } from '../DurationField';
import InputWrapper from '../InputWrapper';

export type DurationFormFieldProps = {
    value: number;
    label: string;
    name: string;
    disabled?: boolean;
    onChange?: (duration: number) => void;
    isInvalid?: boolean;
    isRequired?: boolean;
    disableInput?: boolean;
    hideValidationIndicators?: boolean;
    showValidationIndicators?: boolean;
};

export default function DurationFormField({
    name,
    value,
    label,
    onChange,
    disabled,
    isInvalid,
    isRequired,
    disableInput,
    hideValidationIndicators,
    showValidationIndicators,
}: DurationFormFieldProps) {
    const inputWrapperProps = {
        isRequired,
        isInvalid,
        disableInput,
        hideValidationIndicators,
        showValidationIndicators,
        disabled,
        label,
    };

    return (
        <InputWrapper {...inputWrapperProps}>
            <DurationField
                value={value}
                onChange={onChange}
                name={name}
                disabled={disabled}
                isInvalid={isInvalid}
                disableInput={disableInput}
            />
        </InputWrapper>
    );
}
