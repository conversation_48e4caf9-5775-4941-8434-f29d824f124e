import { useAppTranslation } from 'common/hooks/useAppTranslation';
import Select from 'react-select';
import { Colors } from '../../../styles/Colors';
import { FontPrimary } from '../../../styles/FontHelper';
import { HeaderStyles } from '../../../styles/HeaderStyles';
import { ChangeEvent } from '../IInput';
import { ISelector } from '../ISelector';
import styles from './Selector.module.css';

const Selector = ({
    label,
    placeholder,
    className,
    orientation,
    isRequired,
    options,
    onChange,
    name,
    value,
    showValidationIndicators,
    hideLabel,
}: ISelector) => {
    const { t } = useAppTranslation();

    const changeSelector = (event: ChangeEvent) => {
        if (onChange) onChange(event);
    };

    return (
        <div
            className={` ${className || ''} ${styles.container} ${
                orientation === 'row' ? styles.orientationRow : styles.orientationColumn
            }`}
        >
            {!hideLabel && (
                <p className={styles.label}>
                    {label}
                    {showValidationIndicators &&
                        (isRequired ? (
                            <span className={styles.requiredLabel}> *</span>
                        ) : (
                            <span className={styles.optionalLabel}>
                                {' '}
                                {t('commonLabels.optional')}
                            </span>
                        ))}
                </p>
            )}

            <div className={styles.inputContainer}>
                <Select
                    defaultValue={value}
                    value={value}
                    onChange={changeSelector}
                    options={options}
                    placeholder={placeholder}
                    styles={{
                        indicatorSeparator: (base) => ({
                            ...base,
                            display: 'none',
                        }),
                        dropdownIndicator: (base) => ({
                            ...base,
                            padding: '0px 8px',
                        }),
                        menu: (base) => ({
                            ...base,
                            borderRadius: 4,
                            border: 'solid 1px rgba(0, 0, 0, 0.1)',
                            backgroundColor: Colors.Neutral2,
                        }),
                        placeholder: (base) => ({
                            ...base,
                            ...FontPrimary(HeaderStyles.H6_12px, false, Colors.Neutral7),
                            fontStretch: 'normal',
                            lineHeight: 1.09,
                        }),
                        option: (base, { data }) => ({
                            ...FontPrimary(HeaderStyles.H7_11px),
                            display: 'flex',
                            alignItems: 'center',
                            height: 29,
                            borderBottom: `solid 0.5px ${Colors.Neutral3}`,
                            fontStretch: 'normal',
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                            paddingLeft: 11,
                            paddingRight: 11,
                            ':hover': {
                                backgroundColor: Colors.CM5,
                            },
                        }),
                        control: (base) => ({
                            ...base,
                            height: 32,
                            minHeight: 32,
                            paddingLeft: 3,
                            borderRadius: 4,
                            border: `solid 1px ${Colors.White}`,
                            backgroundColor: Colors.Neutral2,
                        }),
                        singleValue: (base, { data }) => ({
                            ...base,
                            ...FontPrimary(HeaderStyles.H7_11px, false, Colors.CM2),
                            fontStretch: 'normal',
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                        }),
                        input: (base) => ({
                            ...base,
                            ...FontPrimary(HeaderStyles.H7_11px, false, Colors.CM2),
                            fontStretch: 'normal',
                            lineHeight: 1.09,
                            letterSpacing: 'normal',
                            color: Colors.CM2,
                        }),
                    }}
                />
                {isRequired && <div className={styles.requiredDot} />}
            </div>
        </div>
    );
};

export default Selector;
