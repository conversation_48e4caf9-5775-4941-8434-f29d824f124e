import { CircularProgress, InputAdornment, InputProps } from '@mui/material';
import { TextField, TextFieldProps } from 'common/components/mui';
import React, { forwardRef, useCallback, useId } from 'react';
import { useValidationContextInput } from 'utils/validation';
import InputWrapper, { InputWrapperProps } from '../InputWrapper';
import { handleCommonKeyDowns, useValidateTextFieldValue } from '../utils';

export type TextFormFieldProps<TextValue extends string = string> = Pick<
    InputWrapperProps,
    'label' | 'isRequired' | 'showValidationIndicators'
> &
    TextFieldProps<TextValue | null> & {
        showLoader?: boolean;
        wrapperRef?: React.Ref<HTMLDivElement>;
        inputWrapperClasses?: InputWrapperProps['classes'] & { self?: string };
        block?: boolean;

        /**
         * if set to true, TextFormField will be displayed as invalid,
         * if set to false or undefined, default behavior is used to determine if
         * it's invalid or not
         */
        isInvalid?: boolean;

        maxLength?: number;
        minLength?: number;
        onChange?: TextFieldProps['onChange'];
        onEnterPress?: React.KeyboardEventHandler;
        onEscPress?: React.KeyboardEventHandler;

        slotProps?: {
            inputWrapper?: Partial<InputWrapperProps>;
        };

        slots?: {
            appendChildren?: React.ReactNode;
        };

        endAdornment?: InputProps['endAdornment'];
        startAdornment?: InputProps['startAdornment'];

        /**
         * if set to true calls stopPropagation and preventDefault on keydown event when key is 'Enter'
         */
        enableEnterComplete?: boolean;
        readonly?: boolean;
        dataTestId?: string;
    };

const TextFormField = forwardRef(
    (
        {
            showLoader,
            minLength = 0,
            maxLength = Infinity,
            block = true,
            isInvalid,
            inputWrapperClasses,
            wrapperRef,

            // InputWrapper props
            label,
            isRequired = false,
            showValidationIndicators,

            // TextField props
            value,
            disabled,
            endAdornment,
            startAdornment,
            onChange,
            onEnterPress,
            onKeyDown,
            onEscPress,
            readonly,
            enableEnterComplete = false,
            InputProps,
            id,
            dataTestId,
            onBlur,
            slotProps = {},

            inputProps,
            slots = {},
            ...textFieldProps
        }: TextFormFieldProps,
        ref: React.Ref<HTMLDivElement>
    ) => {
        const { self: wrapperClass, ...otherWrapperClasses } = inputWrapperClasses ?? {};
        const { valid, validate, reset } = useValidateTextFieldValue(value ?? '', {
            minLength,
            maxLength,
            isRequired,
        });
        const isInvalidExt = !disabled && (isInvalid || valid === false);
        const randomId = useId();

        useValidationContextInput({
            onReset: reset,
            onValidate: validate,
        });

        const handleBlur = useCallback(
            (e: React.FocusEvent<HTMLInputElement>) => {
                validate();
                if (onBlur) {
                    onBlur(e);
                }
            },
            [onBlur, validate]
        );

        return (
            <InputWrapper
                ref={wrapperRef}
                className={wrapperClass}
                classes={otherWrapperClasses}
                label={label}
                isRequired={isRequired}
                isInvalid={isInvalidExt}
                disabled={disabled}
                showValidationIndicators={showValidationIndicators}
                htmlFor={id ?? randomId}
                fullWidth={block}
                {...slotProps.inputWrapper}
            >
                <TextField
                    ref={ref}
                    data-test-id={dataTestId}
                    fullWidth
                    value={value}
                    disabled={disabled}
                    onChange={onChange}
                    id={id ?? randomId}
                    onBlur={handleBlur}
                    InputProps={{
                        error: isInvalidExt,
                        endAdornment: showLoader ? (
                            <InputAdornment position="end">
                                <CircularProgress thickness={4} size={16} />
                            </InputAdornment>
                        ) : (
                            endAdornment
                        ),
                        startAdornment,
                        ...InputProps,
                    }}
                    inputProps={{
                        readOnly: readonly,
                        // important: DO NOT change postfix or it might break auto-tests
                        'data-test-id': dataTestId ? `${dataTestId}__input` : undefined,
                        ...inputProps,
                    }}
                    onKeyDown={handleCommonKeyDowns({
                        onKeyDown,
                        onEnterPress,
                        onEscPress,
                        preventEnter: enableEnterComplete,
                    })}
                    {...textFieldProps}
                />
                {slots.appendChildren}
            </InputWrapper>
        );
    }
);

export { TextFormField as TextField, TextFormField };

export default TextFormField;
