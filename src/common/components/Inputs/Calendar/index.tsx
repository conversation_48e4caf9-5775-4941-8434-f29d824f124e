import { styled } from '@mui/material';
import { yearCalendarClasses } from '@mui/x-date-pickers';
import { DateCalendar, DateCalendarProps } from '@mui/x-date-pickers/DateCalendar';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { forwardRef } from 'react';
import LocalizationProvider from './LocalizationProvider';

export { LocalizationProvider };

const CmosCalendar = styled(DateCalendar)({
    [`& .${yearCalendarClasses.root}`]: {
        ...scrollbarStyle({ size: 8 }),
    },
}) as <T>(props: DateCalendarProps<T> & React.RefAttributes<HTMLDivElement>) => JSX.Element;

const CalendarWrapper = forwardRef<HTMLDivElement, DateCalendarProps<Date>>((props, ref) => (
    <LocalizationProvider>
        <CmosCalendar<Date> ref={ref} dayOfWeekFormatter={(x) => x} {...props} />
    </LocalizationProvider>
));

export default CalendarWrapper;
