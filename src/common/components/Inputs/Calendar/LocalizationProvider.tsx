import { LocalizationProvider as MuiLocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Locale } from 'date-fns';
import { enUS, es } from 'date-fns/locale';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';

export default function LocalizationProvider({ children }: React.PropsWithChildren<{}>) {
    const { internationalization } = useAppSelector(selectSettings);
    const { t } = useAppTranslation();

    return (
        <MuiLocalizationProvider
            dateAdapter={AdapterDateFns}
            adapterLocale={getLocale(internationalization.language)}
            dateFormats={{
                weekdayShort: 'iiiiii',
            }}
            localeText={{
                clearButtonLabel: t('commonLabels.clear'),
            }}
        >
            {children}
        </MuiLocalizationProvider>
    );
}

const enUSButStartsOnMonday: typeof enUS = {
    ...enUS,
    options: {
        ...enUS.options,
        weekStartsOn: 1,
    },
};

function getLocale(language: string): Locale {
    switch (language) {
        case 'es':
            return es;
        default:
            return enUSButStartsOnMonday;
    }
}
