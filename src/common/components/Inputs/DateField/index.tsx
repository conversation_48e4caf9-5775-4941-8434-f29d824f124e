import { CalendarMonth } from '@mui/icons-material';
import { buttonClasses, inputAdornmentClasses, inputBaseClasses, styled } from '@mui/material';
import { yearCalendarClasses } from '@mui/x-date-pickers';
import { DatePickerProps, DatePicker as MuiDatePicker } from '@mui/x-date-pickers/DatePicker';
import { TextField, TextFieldProps } from 'common/components/mui';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import React from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { LocalizationProvider } from '../Calendar';
import { handleCommonKeyDowns } from '../utils';

export type DatePickerVariant = 'grey' | 'rounded' | 'default';

export type DateFieldProps = MergeTypes<
    {
        placeholder?: string;
        fullWidth?: boolean;
        enableEnterComplete?: boolean;
        enableTextInput?: boolean;
        onEnterPress?: React.KeyboardEventHandler<HTMLInputElement>;
        clearable?: boolean;
        isInvalid?: boolean;
        variant?: DatePickerVariant;
    } & Pick<TextFieldProps, 'onBlur' | 'onFocus'>,
    DatePickerProps<Date>
>;

const TEXT_FIELD_COMPONENTS: Record<
    DatePickerVariant,
    React.ComponentType<Omit<TextFieldProps, 'variant' | 'cmosVariant'>>
> = {
    default: TextField,
    grey: (props) => <TextField cmosVariant="grey" {...props} />,
    rounded: (props) => <TextField cmosVariant="roundedPrimary" {...props} />,
};

/**
 * Customized MUI DatePicker.
 *
 * WILL NOT work with tooltip.
 */
export const DateField = React.forwardRef(
    (
        {
            name,
            disableFuture,
            shouldDisableDate,
            placeholder,
            clearable = false,
            fullWidth,
            label,

            variant,
            onEnterPress,
            format,
            enableTextInput,
            enableEnterComplete,
            isInvalid = false,

            // TextFieldProps
            onBlur,
            onFocus,
            value,
            ...props
        }: DateFieldProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const { internationalization } = useAppSelector(selectSettings);
        const dateFormat = getCorrectDateFormat(internationalization.dateFormat, format);

        return (
            <LocalizationProvider>
                <StyledMuiDatePicker
                    ref={ref}
                    value={value}
                    slotProps={{
                        textField: {
                            error: isInvalid,
                            onBlur,
                            onFocus,
                            fullWidth: true,
                            onKeyDown: handleCommonKeyDowns({
                                onEnterPress: onEnterPress,
                            }),
                        },
                        layout: {
                            sx: {
                                [`& .${yearCalendarClasses.root}`]: {
                                    ...scrollbarStyle(),
                                },
                            },
                        },
                        actionBar: {
                            actions: clearable ? ['clear'] : [],
                            sx: {
                                [`& .${buttonClasses.root}`]: {
                                    textTransform: 'none',
                                    borderRadius: 10,
                                },
                            },
                        },
                        openPickerIcon: {
                            sx: {
                                fill: 'var(--cm2)',
                            },
                        },
                    }}
                    slots={{
                        textField: TEXT_FIELD_COMPONENTS[variant ?? 'default'],
                        openPickerIcon: CalendarMonth,
                    }}
                    format={dateFormat}
                    disableFuture={disableFuture || false}
                    {...props}
                />
            </LocalizationProvider>
        );
    }
);

const StyledMuiDatePicker = styled(MuiDatePicker)({
    height: 32,

    [`& .${inputBaseClasses.readOnly} .${inputAdornmentClasses.positionEnd}`]: {
        opacity: 0.5,
    },
}) as typeof MuiDatePicker;

export default DateField;

function getCorrectDateFormat(settingsFormat: string, propValue: string | undefined): string {
    // NOTE (MB) this regex matches stand-alone letter D without any other Ds next to it
    settingsFormat = settingsFormat.replace(/(?<!D)D{1,2}(?!D)/g, 'dd').replace('yyyy', 'yy');
    return propValue === undefined ? settingsFormat : propValue.replace('{date}', settingsFormat);
}
