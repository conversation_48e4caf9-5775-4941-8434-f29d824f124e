import { forwardRef } from 'react';
import InputWrapper, { InputWrapperProps } from '../InputWrapper';
import { TimeField, TimeFieldProps } from '../TimeField';

export type TimeFormFieldProps = MergeTypes<
    {
        slotProps?: {
            inputWrapper?: Partial<InputWrapperProps>;
        };
    },
    TimeFieldProps
> & {
    timeFieldRef?: React.Ref<React.ComponentRef<typeof TimeField>>;
} & Pick<InputWrapperProps, 'label' | 'isInvalid' | 'isRequired' | 'showValidationIndicators'>;

/**
 * TimeField wrapped in InputWrapper with some of the wrapper's props exposed.
 * You can modify inaccessible TimeField props via slotProps. TimeField reference is available via timeFieldRef
 */
const TimeFormField = forwardRef(
    (
        {
            slotProps = {},

            // InputWrapper props
            label,
            isInvalid,
            isRequired,
            showValidationIndicators,

            disabled,
            timeFieldRef,
            fullWidth,

            ...timeFieldProps
        }: TimeFormFieldProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const inputWrapperProps = {
            label,
            isInvalid,
            isRequired,
            showValidationIndicators,
            disabled,
            fullWidth,
            ...slotProps.inputWrapper,
        };

        return (
            <InputWrapper ref={ref} {...inputWrapperProps}>
                <TimeField
                    ref={timeFieldRef}
                    fullWidth
                    disabled={disabled}
                    // slotProps={timeFieldSlotProps}
                    {...timeFieldProps}
                />
            </InputWrapper>
        );
    }
);

export default TimeFormField;
