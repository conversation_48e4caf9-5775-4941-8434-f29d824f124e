import {
    Checkbox as CheckboxMUI,
    CheckboxProps as MUICheckboxProps,
    useTheme,
} from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import React, { useMemo } from 'react';

export type CheckboxProps = MUICheckboxProps & {
    uncheckedIconColor?: string;
    id?: string;
};

export const Checkbox = React.forwardRef(
    (
        { uncheckedIconColor, ...props }: CheckboxProps,
        ref: React.ForwardedRef<HTMLButtonElement>
    ) => {
        const theme = useTheme();

        const size = useMemo(() => props.style?.width ?? 24, [props.style]);

        return (
            <CheckboxMUI
                {...props}
                ref={ref}
                color="default"
                checkedIcon={<CheckBoxIcon fill={theme.palette.primary.main} size={+size} />}
                icon={
                    <UncheckBoxIcon
                        fill={uncheckedIconColor ?? theme.palette.neutral[6]}
                        size={+size}
                    />
                }
                style={{
                    ...props.style,
                    opacity: props.disabled ? 0.5 : 1,
                    padding: props.size === 'small' ? '3px' : undefined,
                }}
            />
        );
    }
);

export default Checkbox;
