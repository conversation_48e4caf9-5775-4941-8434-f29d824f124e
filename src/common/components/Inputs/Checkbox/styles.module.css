/* The container */
.container {
    display: block;
    position: relative;
    padding-left: 21px;
    height: 16px;
    margin-bottom: 12px;
    margin-top: 12px;
    cursor: pointer;
    font-family: Proxima-nova;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.17;
    letter-spacing: normal;
    text-align: left;
    color: #899198;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    align-items: center;
    display: flex;
}

/* Hide the browser's default checkbox */
.container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Create a custom checkbox */
.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 14px;
    width: 14px;
    border-radius: 3px;
    border: solid 1px #acb7c0;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
    border: solid 1px #acb7c0;
}

.container:hover .unchecked ~ .checkmark:after {
    display: block;
    border: solid #cdd2d6;
    border-width: 0 2px 2px 0;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
    border: solid 1px #0069ff;
    background-color: #e6eeff;
}

.container input:checked ~ .label {
    color: #467cfc;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
    content: '';
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
    display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
    left: 4.2px;
    top: 2.3px;
    width: 3.4px;
    height: 5.7px;
    border: solid #467cfc;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
