import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { numberPhoneUnformatter, phoneFormatRegexMask } from 'common/FormatersHelper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import MaskedTextFormField, { MaskedTextFormFieldProps } from '../MaskedTextFormField';

type PropsOverride = {
    onChange?: (phone: string) => void;
    phoneType?: 'mobile' | 'landline'; // NOTE (MB) I do not know if format of landline is any different from
    // a mobile phone
};

export type PhoneFieldProps = Omit<
    MaskedTextFormFieldProps,
    keyof PropsOverride | 'mask' | 'label'
> &
    PropsOverride;

export default function PhoneField({
    value,
    onChange,
    phoneType = 'mobile',
    ...props
}: PhoneFieldProps) {
    const { internationalization } = useAppSelector(selectSettings);
    const maxLengthPhone = InternationalizationLogic.maxLengthPhone(internationalization);
    const oldValue = useRef<string | null | undefined>(undefined);
    const [displayValue, setDisplayValue] = useState(value);

    const { t } = useAppTranslation();

    const onFocus = useCallback(() => {
        oldValue.current = value === null ? undefined : value;
    }, [value]);

    const onBlur = useCallback(() => {
        if (displayValue !== value) {
            const withoutFormat = numberPhoneUnformatter(displayValue);
            if (onChange) onChange(withoutFormat);
        }
    }, [value, displayValue, onChange]);

    const onChangeCallback = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setDisplayValue(e.target.value);
    }, []);

    const onEsc = useCallback(() => {
        if (oldValue.current && oldValue.current !== displayValue) {
            setDisplayValue(displayValue);
        }
    }, [displayValue]);

    const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' || e.key === 'Escape') {
            (e.target as HTMLInputElement).blur();
        }
    };

    useEffect(() => {
        if (value !== displayValue) {
            setDisplayValue(value);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    return (
        <MaskedTextFormField
            label={phoneType === 'mobile' ? t('commonLabels.mobile') : t('commonLabels.phone')}
            name="phone"
            maxLength={maxLengthPhone}
            value={displayValue}
            onFocus={onFocus}
            onEscPress={onEsc}
            onChange={onChangeCallback}
            onKeyDown={onKeyDown}
            onBlur={onBlur}
            mask={phoneFormatRegexMask(internationalization.phoneNumberFormat)}
            {...props}
        />
    );
}
