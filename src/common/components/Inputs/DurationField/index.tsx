import { Popper, useForkRef } from '@mui/material';
import { CmosTextFieldVariant } from 'common/components/mui';
import { ZLayer } from 'common/styles/ZLayer';
import { DateTime } from 'luxon';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import MaskedTextField, { MaskedTextFieldProps } from '../MaskedTextField';
import TimePickerMenu from '../TimeField/TimePickerMenu';
import { handleCommonKeyDowns } from '../utils';

export type DurationFieldOwnProps = {
    value: number | null;
    onChange?: (value: number) => void;
    keepOpen?: boolean;
    disablePast?: boolean;
    disableInput?: boolean;
    closeAfterSelect?: boolean;
    cmosVariant?: CmosTextFieldVariant;
};

export type DurationFieldProps = MergeTypes<
    DurationFieldOwnProps,
    Omit<MaskedTextFieldProps, 'mask'>
>;

export const DurationField = React.forwardRef(
    (
        {
            value: externalValue,
            onChange,
            keepOpen = false,
            disablePast = false,
            disableInput = false,
            closeAfterSelect,
            onKeyDown,
            ...props
        }: DurationFieldProps,
        forwardedRef: React.ForwardedRef<HTMLDivElement>
    ) => {
        const inputRef = useRef<HTMLInputElement | null>(null);
        const paperRef = useRef<HTMLDivElement | null>(null);
        const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
        const [open, setOpen] = useState(false);

        // string value of the input while in edit mode
        const [inputValue, setInputValue] = useState('');
        // last valid value parsed from inputValue
        const internalValue = useInternalValue(inputValue);

        // input value that will be displayed depending on "open"
        const displayedInputValue = open ? inputValue : getInputValueFromSeconds(externalValue);

        // popup picker value that will be displayed depending on "open"
        const displayedDateValue = getDateFromMinutes(open ? internalValue : externalValue);

        const closePopper = useCallback(() => {
            if (!stateRef.current.open) {
                return;
            }

            setOpen(false);
            stateRef.current.open = false;

            if (stateRef.current.onChange) {
                const newValue = stateRef.current.internalValue;
                // sanity check: newValue should always be not null here
                if (newValue !== null) {
                    stateRef.current.onChange(newValue);
                }
            }
        }, []);

        const stateRef = useRef({
            open,
            externalValue,
            internalValue,
            onChange,
            closePopper,
        });
        stateRef.current.open = open;
        stateRef.current.externalValue = externalValue;
        stateRef.current.internalValue = internalValue;
        stateRef.current.onChange = onChange;
        stateRef.current.closePopper = closePopper;

        // effect for closing the picker on click outside
        useEffect(() => {
            // if time picker is open and user clicks anywhere other than time picker or popper, close time picker
            if (!open) return () => {};

            const callback = (e: MouseEvent) => {
                if (!(e.target instanceof Node)) return;
                if (paperRef.current?.contains(e.target) || anchorEl?.contains(e.target)) return;
                stateRef.current.closePopper();
            };

            document.body.addEventListener('mousedown', callback);

            return () => document.body.removeEventListener('mousedown', callback);
        }, [anchorEl, open]);

        const openPopper = useCallback(() => {
            if (stateRef.current.open) return;

            setOpen(true);
            stateRef.current.open = true;
            setInputValue(getInputValueFromSeconds(stateRef.current.externalValue));
        }, []);

        const handleFocus = useCallback(
            (e: React.FocusEvent<HTMLInputElement>) => {
                openPopper();
            },
            [openPopper]
        );

        const handleBlur = useCallback(
            (e: React.FocusEvent<HTMLInputElement>) => {
                if (!e.relatedTarget) {
                    // just lost focus
                    closePopper();
                    return;
                }

                if (paperRef.current && !paperRef.current.contains(e.relatedTarget)) {
                    // focused on something outside of the time picker
                    closePopper();
                    return;
                }
            },
            [closePopper]
        );

        const handleMenuValueChange = useCallback(
            (value: Date | null) => {
                const newInputValue = getInputValueFromDate(value);
                setInputValue(newInputValue);

                const newInternalValue = parsePartialInput(newInputValue);
                if (newInternalValue !== 'invalid') {
                    stateRef.current.internalValue = newInternalValue;
                }

                closePopper();
            },
            [closePopper]
        );

        const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
            setInputValue(e.target.value);

            if (stateRef.current.onChange) {
                const newValue = stateRef.current.internalValue;
                // sanity check: newValue should always be not null here
                if (newValue !== null) {
                    stateRef.current.onChange(newValue);
                }
            }
        }, []);

        const rootRef = useForkRef(setAnchorEl, forwardedRef);

        return (
            <>
                <MaskedTextField
                    slotProps={{
                        textField: {
                            ref: rootRef,
                        },
                    }}
                    inputRef={inputRef}
                    value={displayedInputValue}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    readonly={disableInput}
                    mask={[/\d/, /\d/, ':', /\d/, /\d/]}
                    maskPlaceholderChar="_"
                    onKeyDown={handleCommonKeyDowns({
                        onKeyDown,
                        onEnterPress: closePopper,
                    })}
                    {...props}
                />

                <Popper
                    style={{ zIndex: ZLayer.menu }}
                    popperOptions={{ placement: 'bottom-start' }}
                    open={open || keepOpen}
                    anchorEl={anchorEl}
                    keepMounted={false} // keep this false or handlePaper will break
                >
                    <TimePickerMenu
                        ref={paperRef}
                        onChange={handleMenuValueChange}
                        // do not display value out of range in the menu
                        value={displayedDateValue === 'outOfRange' ? null : displayedDateValue}
                        disablePast={disablePast}
                        disableInput={disableInput}
                        anchorWidth={anchorEl?.offsetWidth}
                    />
                </Popper>
            </>
        );
    }
);

function getDateFromMinutes(totalMinutes: number | null): Date | null | 'outOfRange' {
    if (totalMinutes === null) return null;

    if (totalMinutes >= 24 * 60) {
        return 'outOfRange';
    }

    return DateTime.now()
        .set({ millisecond: 0, second: 0, minute: 0, hour: 0 })
        .plus({ minutes: totalMinutes })
        .toJSDate();
}

function parsePartialInput(s: string): number | null | 'invalid' {
    if (s === '__:__' || s === '') return null;

    if (/\d{2}:\d{2}/.test(s)) {
        const [h, m] = s.split(':').map(Number);
        return h * 60 + m;
    }

    return 'invalid';
}

function useInternalValue(inputValue: string): number | null {
    const lastValidValue = useRef<number | null>(null);
    const parsedValue = parsePartialInput(inputValue);
    if (parsedValue !== 'invalid') {
        lastValidValue.current = parsedValue;
    }

    return lastValidValue.current;
}

function getInputValueFromSeconds(totalMinutes: number | null): string {
    if (totalMinutes === null) return '';
    const hours = Math.floor(totalMinutes / 60);
    const minutes = Math.floor(totalMinutes - hours * 60);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

function getInputValueFromDate(date: Date | null): string {
    if (date === null) return '';

    return DateTime.fromJSDate(date).toFormat('HH:mm');
}
