import styled from '@mui/material/styles/styled';

export interface LinkProps {
    children?: JSX.Element | string;
    to: string;
    baseURL?: string;
    disabled?: boolean;
    className?: string;
    onClick?: Function;
}

export const Link = ({
    children,
    className,
    to,
    disabled,
    onClick,
    baseURL = '/dashboard',
    ...props
}: LinkProps): JSX.Element => {
    const url = disabled ? '#' : to;
    const onClickHandle = (e: any) => {
        if (disabled) return e.preventDefault();
        else if (onClick) return onClick(e);
        else return e;
    };
    return (
        <StyledLink
            className={className}
            disabled={disabled}
            {...props}
            href={baseURL + url}
            onClick={onClickHandle}
        >
            {children}
        </StyledLink>
    );
};

const StyledLink = styled('a')<{ disabled?: boolean }>(({ theme, disabled }) => ({
    ...theme.typography.h4Roboto,
    fontWeight: 'bold',
    textAlign: 'right',
    textDecoration: 'none',
    color: disabled ? 'var(--neutral4)' : 'var(--cm1)',
    '&:focus': {
        outline: 'none',
    },
    '&:hover': {
        textDecoration: disabled ? 'none' : 'underline',
    },
    '&:visited': {
        color: disabled ? 'var(--neutral4)' : 'var(--cm3)',
    },
}));
