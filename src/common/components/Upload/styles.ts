import { makeStyles } from '@mui/styles';
import { rgba } from 'common/styles/ColorHelpers';

const useUploadStyle = makeStyles((theme) => ({
    input: {
        display: 'none',
    },
    button: {
        padding: '0 2em',
        position: 'relative',
        left: '1px',
    },
    root: {
        width: '100%',
        gap: 10,
    },
    container: {
        borderRadius: '10px 20px 20px 10px',
        border: `1px solid ${theme.palette.neutral[4]}`,
        height: '32px', // is we don't set height explicitly it will have weird vertical padding of 1px
    },
    text: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        margin: '0 0 0 1em',
    },
    textUploaded: {
        color: theme.palette.primary.main,
    },
    fileUploaded: {
        backgroundColor: rgba(theme.palette.primary.main, 0.1),
        borderColor: theme.palette.primary.main,
    },
    uploading: {
        '& *': {
            cursor: 'wait !important',
        },
    },
}));

export default useUploadStyle;
