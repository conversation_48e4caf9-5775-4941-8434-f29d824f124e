import { Box } from '@mui/material';
import clsx from 'clsx';
import { Colors } from 'common/styles/Colors';
import { ChangeEvent, useCallback, useEffect, useMemo, useRef } from 'react';
import { Button } from '../Button';
import { DeleteIcon } from '../Icons/DeleteIcon';
import { Icon } from '../Icons/Icon';
import useUploadStyle from './styles';

export type UploadProps = {
    file: File | null;
    onFileReady?: (file: File | null) => void;
    accept: string;
    id?: string;

    buttonLabel: string;
    uploadedButtonLabel: string;

    text: string;
    uploadedText: string;

    isUploading?: boolean;
    isFileUploaded?: boolean;
    onDelete?: () => void;
    deleteDisabled?: boolean;
    icon?: typeof Icon;
    disabled?: boolean;
};

export default function Upload({
    id: idProp,
    buttonLabel,
    uploadedButtonLabel,
    text,
    uploadedText,
    onDelete,
    deleteDisabled,
    file,
    isFileUploaded,
    icon,
    onFileReady,
    isUploading,
    disabled,
    ...props
}: UploadProps) {
    const styles = useUploadStyle();
    const inputRef = useRef<HTMLInputElement | null>(null);
    const id = useMemo(
        () => idProp ?? `fileUpload${Math.random().toString(16).substring(2)}`,
        [idProp]
    );

    const onChangeCallback = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            if (isUploading) return;
            if (onFileReady)
                onFileReady(
                    !e.target.files || e.target.files.length === 0 ? null : e.target.files[0]
                );
        },
        [onFileReady, isUploading]
    );

    // reset input when file is set to null
    useEffect(() => {
        if (file === null && inputRef.current) inputRef.current.value = '';
    }, [file]);

    return (
        <Box className={styles.root} display="flex" gap={0.5}>
            <Box
                flexGrow={1}
                display="flex"
                className={clsx(styles.container, isFileUploaded && styles.fileUploaded)}
                alignItems="center"
            >
                <Box
                    flexGrow={1}
                    className={clsx(styles.text, isFileUploaded && styles.textUploaded)}
                >
                    {isFileUploaded ? uploadedText : text}
                </Box>
                <label htmlFor={id} className={isUploading ? styles.uploading : undefined}>
                    <Button
                        disabled={disabled}
                        Icon={isUploading ? undefined : icon}
                        showLoader={isUploading}
                        className={styles.button}
                        cmosVariant={isFileUploaded ? 'stroke' : 'filled'}
                        onClick={(e) => {
                            // This feels like a hack
                            inputRef.current?.click();
                        }}
                        label={isFileUploaded ? uploadedButtonLabel : buttonLabel}
                    />
                    <input
                        disabled={disabled}
                        onChange={onChangeCallback}
                        className={styles.input}
                        id={id}
                        ref={inputRef}
                        type="file"
                        {...props}
                    />
                </label>
            </Box>

            {onDelete && (
                <Button
                    disabled={deleteDisabled}
                    onClick={onDelete}
                    color={Colors.Error}
                    cmosVariant={'stroke'}
                    Icon={DeleteIcon}
                />
            )}
        </Box>
    );
}
