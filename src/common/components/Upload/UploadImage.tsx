import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ImageIcon } from '../Icons/ImageIcon';
import Upload, { UploadProps } from './Upload';

type PropsOverwrite = {
    accept?: string;
    buttonLabel?: string;
    uploadedButtonLabel?: string;
    text?: string;
    uploadedText?: string;
};

export type UploadImageProps = Omit<UploadProps, keyof PropsOverwrite> & PropsOverwrite;

export default function UploadImage({
    text,
    uploadedText,
    buttonLabel,
    uploadedButtonLabel,
    accept,
    ...props
}: UploadImageProps) {
    const { t } = useAppTranslation();

    return (
        <Upload
            icon={ImageIcon}
            uploadedButtonLabel={uploadedButtonLabel ?? t('commonLabels.upload.changeImage')}
            buttonLabel={buttonLabel ?? t('commonLabels.upload.uploadImage')}
            text={text ?? t('commonLabels.upload.noAttachedImage')}
            uploadedText={uploadedText ?? t('commonLabels.upload.uploadedImage')}
            accept={accept ?? 'image/*'}
            {...props}
        />
    );
}
