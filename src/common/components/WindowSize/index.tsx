import { useEffect, useState } from 'react';

export function WindowSize() {
    const [size, setSize] = useState({ w: 0, h: 0 });

    useEffect(() => {
        const callback = () => {
            setSize({ w: window.innerWidth, h: window.innerHeight });
        };

        callback();

        window.addEventListener('resize', callback);

        return () => window.removeEventListener('resize', callback);
    }, []);

    return (
        <>
            {size.w}×{size.h}
        </>
    );
}
