import { CSSProperties } from 'react';

export type BoxShadowProps = {
    children: any;
    style?: CSSProperties;
};

export function BoxShadow({ children, style }: BoxShadowProps) {
    return (
        <div
            style={{
                width: 'fit-content',
                boxShadow: 'rgb(229 231 234 / 56%) 5px 0px 10px 5px',
                overflow: 'hidden',
                margin: 20,
                borderRadius: 10,
                ...style,
            }}
        >
            {children}
        </div>
    );
}
