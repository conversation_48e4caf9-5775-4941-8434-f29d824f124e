import { Modal as MUIModal, ModalProps as MuiModalProps, styled } from '@mui/material';
import clsx from 'clsx';
import useLockedBody from 'common/hooks/useLockedBody';
import React, { HTMLAttributes, forwardRef, useCallback, useRef, useState } from 'react';
import { DataProps, splitDataProps } from 'utils/dataProps';
import { Colors } from '../../../common/styles/Colors';
import styles from './Modal.module.css';
import ModalTransition from './ModalTransition';

type ModalProps = {
    open?: boolean;
    children: React.ReactNode;
    onClose?: () => void;
    width?: string;
    background?: Colors;
    classes?: {
        box: string;
    };
    boxComponent?: React.ComponentType<HTMLAttributes<HTMLDivElement>> | 'div';
    className?: string;
} & Pick<MuiModalProps, 'onTransitionEnter' | 'onTransitionExited'> &
    DataProps;

export const Modal = forwardRef(
    (
        {
            open = false,
            onClose,
            children,
            background = Colors.White,
            width,
            classes,
            className,
            boxComponent: BoxComponent = 'div',
            onTransitionEnter,
            onTransitionExited,
            ...props
        }: ModalProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const openRef = useRef(open);
        openRef.current = open;
        const [actuallyOpen, setActuallyOpen] = useState(open);

        const handleTransitionEnter = useCallback(() => {
            if (openRef.current) {
                setActuallyOpen(true);
            }
            if (onTransitionEnter) onTransitionEnter();
        }, [onTransitionEnter]);

        const handleTransitionExited = useCallback(() => {
            if (!openRef.current) {
                setActuallyOpen(false);
            }
            if (onTransitionExited) onTransitionExited();
        }, [onTransitionExited]);

        useLockedBody(actuallyOpen);

        const transitionDuration = 150;

        return (
            <StyledMUIModal
                ref={ref}
                disableScrollLock
                open={open}
                onClose={onClose}
                className={clsx(styles.modal, className)}
                closeAfterTransition
                slotProps={{
                    backdrop: {
                        transitionDuration,
                    },
                }}
                onTransitionEnter={handleTransitionEnter}
                onTransitionExited={handleTransitionExited}
                {...splitDataProps(props)[1]}
            >
                <ModalTransition in={open} timeout={transitionDuration}>
                    <BoxComponent
                        className={clsx(classes?.box, styles.box)}
                        style={{ background, width }}
                    >
                        {children}
                    </BoxComponent>
                </ModalTransition>
            </StyledMUIModal>
        );
    }
);

const StyledMUIModal = styled(MUIModal)({
    right: 'var(--scrollbar-width, 0px)',
});
