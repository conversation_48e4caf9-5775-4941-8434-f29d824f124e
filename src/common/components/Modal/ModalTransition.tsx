import React from 'react';
import { Transition } from 'react-transition-group';
import { TransitionProps, TransitionStatus } from 'react-transition-group/Transition';

// default css props
const defaultStyle = {
    transform: 'translate3d(0, 10px, 0)',
    transitionOrigin: 'bottom center',
    opacity: 0,
};

// custom css props in 4 state: entering, entered, exiting, exited
const animationStyles: Partial<Record<TransitionStatus, React.CSSProperties>> = {
    entered: {
        opacity: 1,
        transform: 'none',
        willChange: 'unset',
    },
};

const ModalTransition = React.forwardRef(
    (
        {
            children,
            style: styleProp,
            timeout,
            in: inProp,
            onEnter,
            onExited,
            ...other
        }: {
            children: React.ReactElement;
            style?: React.CSSProperties;
            timeout: number;
            in: boolean;
            onEnter?: TransitionProps['onEnter'];
            onExited?: TransitionProps['onExited'];
        },
        ref: React.ForwardedRef<HTMLElement>
    ) => {
        const style = {
            ...styleProp,
            ...(React.isValidElement(children)
                ? (children.props as { style?: React.CSSProperties }).style
                : {}),
        };

        return (
            <Transition<undefined>
                timeout={timeout}
                appear={true}
                in={inProp}
                onEnter={onEnter}
                onExited={onExited}
                {...other}
            >
                {(state, childProps) => {
                    return React.cloneElement(children, {
                        style: {
                            ...defaultStyle,
                            transition: 'all ' + timeout + 'ms ease',
                            transitionProperty: 'transform, opacity',
                            willChange: 'transform, opacity',
                            ...animationStyles[state],
                            ...style,
                        },
                        ref: ref,
                        ...childProps,
                    });
                }}
            </Transition>
        );
    }
);

export default ModalTransition;
