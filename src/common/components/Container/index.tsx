import { styled } from '@mui/material';
import React from 'react';

export type ContainerProps = React.HTMLAttributes<HTMLDivElement> & {
    w?: number | string;
};

export default function Container({ className, w = 80, ...props }: ContainerProps) {
    return <DivContainer style={{ width: typeof w === 'string' ? `${w}` : `${w}%` }} {...props} />;
}

const DivContainer = styled('div')({
    margin: '0 auto',
});
