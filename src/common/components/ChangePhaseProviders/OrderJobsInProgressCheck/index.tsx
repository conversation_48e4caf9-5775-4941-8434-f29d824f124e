import { styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { WpOrdersApi } from 'api/workshopPlanner';
import { JobInProgress } from 'api/workshopPlanner/orders';
import { isAxiosError } from 'axios';
import { CalendarIconTransparent } from 'common/components/Icons/CalendarIconTransparent';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import useToasters from 'common/hooks/useToasters';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import { Trans } from 'react-i18next';
import { isErrorResponse } from 'services/Server';
import { useAppSelector } from 'store';
import { selectRepairShopFeatures } from 'store/slices/globalSettingsSlice';
import { selectUser, selectUserPermission } from 'store/slices/user';
import styles from './styles.module.css';

const OrderJobsInProgressCheckContext = createContext<{
    checkOrderJobsInProgress: (
        changePhaseData: ChangePhaseData,
        onChange: () => void,
        onCancel?: () => void
    ) => void;
} | null>(null);

export function useOrderJobsInProgressCheck() {
    const ctx = useContext(OrderJobsInProgressCheckContext);
    if (!ctx) throw new Error('OrderJobsInProgressCheckContext is not available');
    return ctx;
}

type ChangePhaseData = {
    destinationPhaseId: number;
    orderId: string;
    orderNumber: string;
    excludedJobIds?: string[];
};

export function OrderJobsInProgressCheckProvider({ children }: React.PropsWithChildren<{}>) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const fr = useForceRender();
    const { enableWp } = useAppSelector(selectRepairShopFeatures) ?? { enableWp: false };
    const { allowManageJobs } = useAppSelector(selectUserPermission);
    const { key: userId } = useAppSelector(selectUser);

    const phaseChangeContext = useRef<{
        closedPhase: boolean;
        orderId: string;
        orderNumber: string;
        jobsInProgress: JobInProgress[];
        canManageJobs: boolean;
        onChange: () => void;
        onCancel?: () => void;
        excludedJobIds?: string[];
    } | null>(null);

    const ctx = useMemo(
        () => ({
            checkOrderJobsInProgress: async (
                changePhaseData: ChangePhaseData,
                onChange: () => void,
                onCancel?: () => void
            ) => {
                if (!enableWp) {
                    onChange();
                    return;
                }

                try {
                    const jobsInProgress = await WpOrdersApi.getOrderJobsInProgress(
                        changePhaseData.orderId
                    );
                    if (jobsInProgress.length === 0) {
                        onChange();
                    } else {
                        phaseChangeContext.current = {
                            closedPhase: changePhaseData.destinationPhaseId === Phases.Closed,
                            orderId: changePhaseData.orderId,
                            orderNumber: changePhaseData.orderNumber,
                            jobsInProgress,
                            canManageJobs:
                                allowManageJobs ||
                                jobsInProgress.every((x) => x.technicianId === userId),
                            onChange,
                            onCancel,
                            excludedJobIds: changePhaseData.excludedJobIds,
                        };
                        fr();
                    }
                } catch (_) {
                    toasters.danger(
                        t('toasters.errorOccurredWhenLoading'),
                        t('toasters.errorOccurred')
                    );
                    onChange();
                }
            },
        }),
        [t, toasters, fr, enableWp, allowManageJobs, userId]
    );

    const handleConfirm = useCallback(() => {
        if (phaseChangeContext.current) {
            phaseChangeContext.current.onChange();
            phaseChangeContext.current = null;
            fr();
        }
    }, [fr]);

    const handleCancel = useCallback(() => {
        if (phaseChangeContext.current) {
            if (phaseChangeContext.current.closedPhase) {
                phaseChangeContext.current.onCancel?.();
            } else {
                phaseChangeContext.current.onChange();
            }
            phaseChangeContext.current = null;
            fr();
        }
    }, [fr]);
    return (
        <OrderJobsInProgressCheckContext.Provider value={ctx}>
            <JobsInProgressPopup
                closedPhase={phaseChangeContext.current?.closedPhase ?? false}
                open={phaseChangeContext.current !== null}
                excludedJobIds={phaseChangeContext.current?.excludedJobIds ?? []}
                orderId={phaseChangeContext.current?.orderId ?? ''}
                orderNumber={phaseChangeContext.current?.orderNumber ?? ''}
                jobsInProgress={phaseChangeContext.current?.jobsInProgress ?? []}
                isConfirmDisabled={!phaseChangeContext.current?.canManageJobs}
                onConfirm={handleConfirm}
                onCancel={handleCancel}
            />
            {children}
        </OrderJobsInProgressCheckContext.Provider>
    );
}

export enum GeneralWPJobsErrorCode {
    NoEditJobsPermission = 'General.WP.Jobs.NoEditJobsPermission',
    NoStandardOperationSpecified = 'General.WP.Jobs.NoStandardOperationSpecified',
    MandatoryFieldNotSpecified = 'General.WP.Jobs.MandatoryFieldNotSpecified',
    PendingSignature = 'General.WP.Jobs.PendingSignature',
}

type JobsInProgressPopupProps = {
    closedPhase: boolean;
    open: boolean;
    isConfirmDisabled: boolean;
    orderId: string;
    orderNumber: string;
    jobsInProgress: JobInProgress[];
    excludedJobIds: string[];
    onConfirm: () => void;
    onCancel: () => void;
};

function JobsInProgressPopup({
    closedPhase,
    open,
    isConfirmDisabled,
    excludedJobIds,
    orderId,
    orderNumber,
    jobsInProgress,
    onConfirm,
    onCancel,
}: JobsInProgressPopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const stopJobsMutation = useMutation(
        async () => WpOrdersApi.stopOrderJobsInProgress(orderId, excludedJobIds),
        {
            onSuccess() {
                onConfirm();
            },
            onError(err) {
                if (isAxiosError(err) && isErrorResponse(err.response?.data)) {
                    const response = err.response.data;
                    if (response.code === GeneralWPJobsErrorCode.NoEditJobsPermission) {
                        toasters.danger(
                            t('jobsInProgressPopup.noEditJobsPermission'),
                            t('toasters.errorOccurred')
                        );
                    } else if (
                        response.code === GeneralWPJobsErrorCode.NoStandardOperationSpecified
                    ) {
                        toasters.danger(
                            t('jobsInProgressPopup.noStandardOperationSpecified'),
                            t('toasters.errorOccurred')
                        );
                    } else if (
                        response.code === GeneralWPJobsErrorCode.MandatoryFieldNotSpecified
                    ) {
                        toasters.danger(
                            t('jobsInProgressPopup.mandatoryFieldNotSpecified'),
                            t('toasters.errorOccurred')
                        );
                    } else if (response.code === GeneralWPJobsErrorCode.PendingSignature) {
                        toasters.danger(
                            t('jobsInProgressPopup.pendingSignature'),
                            t('toasters.errorOccurred')
                        );
                    } else {
                        toasters.danger(
                            t('toasters.errorOccurredWhenSaving'),
                            t('toasters.errorOccurred')
                        );
                    }
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
        }
    );

    return (
        <WarningConfirmationPopup
            open={open}
            showLoader={stopJobsMutation.isLoading}
            title={t('jobsInProgressPopup.title')}
            body={
                <DivBody>
                    <Divider />
                    <DivTextWrapper>
                        <PMainText>
                            {t(
                                `jobsInProgressPopup.${
                                    closedPhase ? 'bodyClose' : 'bodyChangePhase'
                                }`,
                                { orderNumber }
                            )}
                        </PMainText>
                        <DivJobsList>
                            {jobsInProgress.map((x, i) => (
                                <JobInProgressItem index={i} startedAt={x.startedAt} />
                            ))}
                        </DivJobsList>
                    </DivTextWrapper>
                </DivBody>
            }
            cancel={
                closedPhase ? t('commonLabels.cancel') : t('jobsInProgressPopup.continueAnyway')
            }
            confirm={
                closedPhase
                    ? t('jobsInProgressPopup.finishAndClose')
                    : t('jobsInProgressPopup.finishAndChangePhase')
            }
            isConfirmDisabled={isConfirmDisabled}
            onConfirm={() => stopJobsMutation.mutateAsync()}
            onCancel={() => (closedPhase ? onCancel() : onConfirm())}
            showCloseBtn={false}
            onClose={() => {}}
            classes={{ box: styles.box, options: styles.options, title: styles.title }}
        />
    );
}

type JobInProgressItemProps = {
    index: number;
    startedAt: string;
};

function JobInProgressItem({ index, startedAt }: JobInProgressItemProps) {
    const { t } = useAppTranslation();

    return (
        <DivRoot key={index}>
            <CalendarIconTransparent />
            <DivJobWrapper>
                <Trans
                    t={t}
                    i18nKey="jobsInProgressPopup.scheduledJob"
                    values={{
                        index: index + 1,
                        time: moment
                            .utc(startedAt)
                            .local()
                            .format(t('jobsInProgressPopup.dateFormat')),
                    }}
                />
            </DivJobWrapper>
        </DivRoot>
    );
}

const Divider = styled('div')({
    height: 1,
    width: '50%',
    borderTop: `solid #E5E7EA 1px`,
});

const DivBody = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '0px 9px',

    ...theme.typography.h5Inter,
    fontWeight: 400,
}));

const PMainText = styled('p')({
    textAlign: 'left',
});

const DivJobsList = styled('div')({
    maxHeight: '147px',
    overflowY: 'auto',
    ...scrollbarStyle(),
});

const DivJobWrapper = styled('div')({ textAlign: 'left', lineHeight: '20px', paddingLeft: '9px' });

const DivTextWrapper = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
});

const DivRoot = styled('div')({
    display: 'flex',
    paddingBottom: '9px',
});
