import { BroadcastConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import React, { createContext, useCallback, useContext, useMemo, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectPhaseSetbackRules } from 'store/slices/phaseSetback/selectors';
import { LimitedTextArea } from '../../Inputs';
import styles from './styles.module.css';

const PhaseSetbackCheckContext = createContext<{
    checkPhasesIds: (
        changePhaseData: ChangePhaseData,
        onChange: (reason?: string) => void,
        onCancel?: () => void
    ) => void;
} | null>(null);

export function usePhaseSetbackCheck() {
    const ctx = useContext(PhaseSetbackCheckContext);
    if (!ctx) throw new Error('PhaseSetbackReasonPopupContext is not available');
    return ctx;
}

type ChangePhaseData = {
    originPhaseId?: number;
    destinationPhaseId: number;
    repairShopKey?: string;
};

export function PhaseSetbackCheckProvider({ children }: React.PropsWithChildren<{}>) {
    const phaseChangeContext = useRef<{
        onChange: (reason?: string) => void;
        onCancel?: () => void;
    } | null>(null);

    const phaseSetbackRules = useAppSelector(selectPhaseSetbackRules);

    const fr = useForceRender();

    const ctx = useMemo(
        () => ({
            checkPhasesIds: (
                changePhaseData: ChangePhaseData,
                onChange: (reason?: string) => void,
                onCancel?: () => void
            ) => {
                const { originPhaseId, destinationPhaseId, repairShopKey } = changePhaseData;

                if (!originPhaseId || originPhaseId === destinationPhaseId) {
                    onChange();
                    return;
                }

                const shopRules =
                    (repairShopKey
                        ? phaseSetbackRules.find((x) => x.repairShopKey === repairShopKey)
                        : phaseSetbackRules[0]) ?? null;

                if (
                    shopRules &&
                    shopRules.enabled &&
                    shopRules.rules.some(
                        (x) =>
                            x.originPhaseId === originPhaseId &&
                            x.destinationPhaseId === destinationPhaseId
                    )
                ) {
                    phaseChangeContext.current = { onChange, onCancel };
                    fr();
                } else {
                    onChange();
                }
            },
        }),
        [phaseSetbackRules, fr]
    );

    const handleConfirm = useCallback(
        (reason: string) => {
            if (phaseChangeContext.current) {
                phaseChangeContext.current.onChange(reason);
                phaseChangeContext.current = null;
                fr();
            }
        },
        [fr]
    );

    const handleCancel = useCallback(() => {
        if (phaseChangeContext.current) {
            phaseChangeContext.current.onCancel?.();
            phaseChangeContext.current = null;
            fr();
        }
    }, [fr]);

    const handleClose = useCallback(() => {
        if (phaseChangeContext.current) {
            phaseChangeContext.current = null;
            fr();
        }
    }, [fr]);

    return (
        <PhaseSetbackCheckContext.Provider value={ctx}>
            <ReasonQuestionPopup
                open={phaseChangeContext.current !== null}
                onConfirm={handleConfirm}
                onClose={handleClose}
                onCancel={handleCancel}
            />
            {children}
        </PhaseSetbackCheckContext.Provider>
    );
}

type ReasonQuestionPopupProps = {
    open: boolean;
    onClose: () => void;
    onConfirm: (reason: string) => void;
    onCancel: () => void;
};

function ReasonQuestionPopup({ open, onClose, onConfirm, onCancel }: ReasonQuestionPopupProps) {
    const { t } = useAppTranslation();
    const [text, setText] = useState('');

    const closePopup = () => {
        setText('');
        onClose();
    };

    return (
        <BroadcastConfirmationPopup
            open={open}
            title={t('phaseSetbackPopup.title')}
            body={
                <LimitedTextArea
                    maxLength={60}
                    value={text}
                    placeholder={t('phaseSetbackPopup.placeholder')}
                    name={'text'}
                    onChange={(e) => setText(e.target.value)}
                    hideLabel
                    limitText
                    shortCounter
                    textAreaClassName={styles.textArea}
                    counterClassName={styles.counter}
                />
            }
            cancel={t('commonLabels.goBack')}
            confirm={t('commonLabels.continue')}
            onConfirm={() => {
                onConfirm(text);
                closePopup();
            }}
            onClose={() => {
                closePopup();
            }}
            onCancel={() => {
                onCancel();
                closePopup();
            }}
            isConfirmDisabled={text.length === 0}
            classes={{ body: styles.body }}
        />
    );
}
