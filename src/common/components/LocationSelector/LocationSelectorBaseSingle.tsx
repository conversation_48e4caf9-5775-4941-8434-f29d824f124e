import { LocationDto } from 'api/enterprise';
import { useCallback, useMemo } from 'react';
import LocationSelectorBase, { LocationSelectorBaseProps } from './LocationSelectorBase';

export type LocationSelectorBaseSingleProps = MergeTypes<
    {
        value?: string | null;
        onChange?: (value: string, location: LocationDto) => void;
    },
    Omit<LocationSelectorBaseProps, 'disableAllOption'>
>;

export function LocationSelectorBaseSingle({
    onChange,
    value,
    ...props
}: LocationSelectorBaseSingleProps) {
    const valueArr = useMemo(() => (value ? [value] : undefined), [value]);
    const onChangeCallback = useCallback(
        (values: string[], locations: LocationDto[]) => {
            // new value will be at the end of the array
            if (values.length === 0) return; // disallow deselect
            onChange && onChange(values[values.length - 1], locations[values.length - 1]);
        },
        [onChange]
    );
    return (
        <LocationSelectorBase
            closeOnSelect
            disableAllOption
            value={valueArr}
            onChange={onChangeCallback}
            {...props}
        />
    );
}
