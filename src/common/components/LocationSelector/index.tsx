import { useMediaQuery } from '@mui/material';
import LocationSelectorBase, { LocationSelectorBaseProps } from './LocationSelectorBase';

export type LocationSelectorProps = Omit<
    LocationSelectorBaseProps,
    'style' | 'numericPlaceholderAfterCount'
>;

const LocationSelector = (props: LocationSelectorProps) => {
    const shorter = useMediaQuery('(max-width:1300px)');

    return (
        <LocationSelectorBase
            numericPlaceholderAfterCount={0}
            style={{ width: shorter ? 120 : 200 }}
            {...props}
        />
    );
};

export default LocationSelector;
