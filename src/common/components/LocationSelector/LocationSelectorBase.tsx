import {
    Autocomplete,
    AutocompleteProps,
    Backdrop,
    Grow,
    MenuItem,
    Popper,
    PopperProps,
    TextField,
    UseAutocompleteProps,
    styled,
} from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { CSSProperties, HTMLAttributes, useCallback, useMemo, useRef, useState } from 'react';
import { useLocations } from 'store/slices/enterprise/locations';
import { LocationDto } from '../../../api/enterprise';
import { rgba } from '../../styles/ColorHelpers';
import { scrollbarStyle } from '../../styles/ScrollbarStyles';
import { CheckBoxIcon } from '../Icons/CheckBoxIcon';
import { DownIcon } from '../Icons/DownIcon';
import { UncheckBoxIcon } from '../Icons/UncheckBoxIcon';
import Tooltip from '../Tooltip';

const Input = styled(TextField)(({ theme }) => ({
    minWidth: 100,
    cursor: 'pointer',

    '& > .MuiInputBase-root': {
        ...theme.typography.h6Inter,
        color: theme.palette.primary.main,
        display: 'flex',
        alignItems: 'center',
        paddingTop: 5,
        paddingBottom: 5,
        height: 32,
        padding: '0 7px !important',
        borderRadius: 20,
    },
    '& .MuiInputBase-input': {
        cursor: 'pointer',
        '&::placeholder': {
            fontWeight: 'bold',
            color: 'var(--cm1)',
            opacity: 1,
        },
    },
}));

const SAutocomplete = styled(Autocomplete)({
    cursor: 'pointer',

    '& .MuiFormControl-root': {
        margin: 0,
    },
    '& .MuiOutlinedInput-notchedOutline': {
        borderColor: 'var(--cm1)',
    },
    '& .MuiInputBase-root:hover fieldset': {
        borderColor: 'var(--cm1)',
    },
    '& .MuiInputBase-root.Mui-disabled': {
        '& fieldset': {
            borderColor: 'var(--neutral4)',
        },
        '& input': {
            cursor: 'default',
        },
    },
}) as typeof Autocomplete;

type ImplProps = AutocompleteProps<LocationDto | null, true, true, false>;

export type LocationSelectorBaseProps = Pick<ImplProps, 'sx' | 'slotProps'> & {
    value?: string[];
    onChange: (values: string[], locations: LocationDto[]) => void;
    style?: CSSProperties;
    className?: string;
    /**
     * if true disables "All locations" options
     */
    disableAllOption?: boolean;
    disableSelectNewOption?: boolean;
    closeOnSelect?: boolean;
    noValuePlaceholder?: string;
    filterFn?: (location: LocationDto) => void;

    /**
     * function for converting LocationDto to string representation
     */
    getLocationLabel?: (location: LocationDto) => string;
    getLocationTooltip?: (location: LocationDto) => string;
    disabled?: boolean;
    useOptionsDividers?: boolean;
    customPopperComponent?: React.ComponentType<PopperProps>;

    /**
     * will switch to numeric representation of selected locations after
     * number of locations is more than this value
     *
     * example of numeric representation: 4 locations
     * example of non-numeric representation: John's shop +3
     */
    numericPlaceholderAfterCount: number;
};

const getLocationName = (location: LocationDto): string => location.name;

export default function LocationSelectorBase({
    value,
    onChange,
    className,
    style,
    sx,
    slotProps,
    disableAllOption,
    disableSelectNewOption,
    closeOnSelect,
    noValuePlaceholder,
    filterFn,
    disabled,
    getLocationLabel = getLocationName,
    getLocationTooltip,
    useOptionsDividers,
    customPopperComponent,
    numericPlaceholderAfterCount,
}: LocationSelectorBaseProps) {
    const { t } = useAppTranslation();
    const locations = useLocations();
    const locationOptions = useMemo(() => {
        const options = locations
            ? locations
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .filter(filterFn ?? (() => true))
                  .map((b) => ({ label: b.name, value: b.repairShopId, disabled: false }))
            : [];

        if (!disableAllOption)
            options.unshift({
                label: t('settings.prospections.maintenance.form.selectSpecificBrands'),
                value: 'all',
                disabled: true,
            });

        return options;
    }, [locations, t, disableAllOption, filterFn]);
    const selectedLocations = useMemo(() => {
        if (value?.length === 1 && value.findIndex((b) => b === 'all') !== -1) {
            return locationOptions;
        }

        const options = (value ?? []).map((id) => {
            const location = locationOptions.find((x) => x.value === id);
            if (location) return location;
            const unfilteredLocation = locations.find((x) => x.repairShopId === id);
            if (unfilteredLocation) {
                return {
                    label: unfilteredLocation.name,
                    value: unfilteredLocation.repairShopId,
                };
            } else {
                return {
                    label: id,
                    value: id,
                };
            }
        });

        return options;
    }, [value, locationOptions, locations]);

    const [focus, setFocus] = useState(false);
    const placeholder = useMemo(() => {
        if (focus) return t('commonLabels.search') + '...';
        if (selectedLocations.length === 0)
            return noValuePlaceholder ?? t('locations.allLocations');

        if (selectedLocations.length > numericPlaceholderAfterCount) {
            return t('locations.locationsWithCount', { count: selectedLocations.length });
        } else {
            const location = locations.find((x) => x.repairShopId === selectedLocations[0].value);
            let label = location ? getLocationLabel(location) : selectedLocations[0].value;
            if (selectedLocations.length > 1) {
                label += ` +${selectedLocations.length - 1}`;
            }
            return label;
        }
    }, [
        t,
        selectedLocations,
        locations,
        focus,
        getLocationLabel,
        noValuePlaceholder,
        numericPlaceholderAfterCount,
    ]);

    const handleChange: UseAutocompleteProps<LocationDto | null, true, true, false>['onChange'] = (
        _event,
        newValue,
        reason,
        details
    ) => {
        if (newValue.length > selectedLocations.length && disableSelectNewOption) {
            return;
        }
        const isAllLocationOption = details?.option === null;

        if (reason === 'selectOption' && isAllLocationOption) {
            onChange([], []);
        } else {
            const locations = newValue.filter((x): x is LocationDto => x !== null);
            onChange(
                locations.map((x) => x!.repairShopId),
                locations
            );
        }
        return;
    };

    const values = useMemo(
        () =>
            selectedLocations.length === 0
                ? disableAllOption
                    ? []
                    : [null]
                : selectedLocations
                      .map((y) => locations?.find((x) => x.repairShopId === y.value))
                      .filter((x): x is LocationDto => !!x),
        [selectedLocations, locations, disableAllOption]
    );
    const elRef = useRef<HTMLElement>();

    return (
        <>
            <SAutocomplete<LocationDto | null, true, true>
                ref={elRef}
                sx={sx}
                slotProps={slotProps}
                disabled={disabled}
                className={className}
                style={style}
                popupIcon={<DownIcon style={{ backgroundColor: 'transparent' }} />}
                value={values}
                noOptionsText={t('commonLabels.noDataSelector')}
                getOptionLabel={(x) => (x ? x.name : '')}
                onChange={handleChange}
                options={disableAllOption ? locations ?? [] : [null, ...(locations ?? [])]}
                disableCloseOnSelect={!closeOnSelect}
                disableClearable
                renderTags={renderNothing}
                onFocus={() => setFocus(true)}
                onBlur={() => setFocus(false)}
                onClose={() => {
                    if (closeOnSelect && elRef.current) {
                        elRef.current.getElementsByTagName('input')[0].blur();
                    }
                }}
                openOnFocus
                multiple
                PopperComponent={customPopperComponent || PopperComponent}
                renderInput={(params) => {
                    return (
                        <Input
                            {...params}
                            placeholder={placeholder}
                            variant="outlined"
                            margin="normal"
                        />
                    );
                }}
                renderOption={useCallback(
                    (props: HTMLAttributes<HTMLLIElement>, o: LocationDto | null) => {
                        if (!o)
                            return (
                                <MenuItem
                                    className={useOptionsDividers ? 'useDivider' : undefined}
                                    {...props}
                                >
                                    {selectedLocations.length === 0 ? (
                                        <CheckBoxIcon />
                                    ) : (
                                        <UncheckBoxIcon />
                                    )}
                                    {t('locations.allLocations')}
                                </MenuItem>
                            );
                        return (
                            <Tooltip
                                content={getLocationTooltip?.(o) ?? ''}
                                disabled={!getLocationTooltip}
                                position="top"
                            >
                                <MenuItem
                                    className={useOptionsDividers ? 'useDivider' : undefined}
                                    {...props}
                                >
                                    {!disableAllOption &&
                                        (selectedLocations.some(
                                            (x) => x.value === o.repairShopId
                                        ) ? (
                                            <CheckBoxIcon />
                                        ) : (
                                            <UncheckBoxIcon />
                                        ))}
                                    {getLocationLabel(o)}
                                </MenuItem>
                            </Tooltip>
                        );
                    },
                    [
                        useOptionsDividers,
                        selectedLocations,
                        t,
                        getLocationTooltip,
                        disableAllOption,
                        getLocationLabel,
                    ]
                )}
            />
        </>
    );
}

const renderNothing = () => null;

const StyledPopperComponent = styled(Popper)(({ theme }) => ({
    '& .MuiPaper-root': {
        boxShadow: 'none',
        border: '1px solid var(--neutral5)',
        '& .MuiAutocomplete-option': {
            padding: '4px 8px',
            minHeight: 32,
            '& > div': {
                height: 38,
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                margin: '0 16px',
            },

            '&:hover': {
                backgroundColor: rgba(theme.palette.primary.main, 0.03),
            },
            '& .useDivider': {
                borderBottom: `1px solid ${theme.palette.neutral[3]}`,
            },
            '&[aria-selected=true]': {
                backgroundColor: rgba(theme.palette.primary.main, 0.08),
                '&:hover': {
                    backgroundColor: rgba(theme.palette.primary.main, 0.12),
                },
            },
        },
    },
    '& .MuiAutocomplete-listbox': {
        ...scrollbarStyle(),
    },
    '& .MuiAutocomplete-noOptions': {
        color: 'var(--cm1)',
        fontWeight: 'bold',
        textAlign: 'center',
    },
}));

const PopperComponent: React.ComponentType<PopperProps> = ({
    children,
    className,
    open,
    ...props
}) => {
    return (
        <Backdrop style={{ zIndex: 3, backgroundColor: 'transparent' }} open={open}>
            <StyledPopperComponent transition open={open} className={className} {...props}>
                {({ TransitionProps }) => (
                    <Grow {...TransitionProps}>{children as React.ReactElement}</Grow>
                )}
            </StyledPopperComponent>
        </Backdrop>
    );
};
