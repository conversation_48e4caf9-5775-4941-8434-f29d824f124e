import { Backdrop, Grow, Popper, PopperProps, autocompleteClasses, styled } from '@mui/material';
import { rgba } from 'common/styles/ColorHelpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { LocationSelectorProps } from '.';
import LocationSelectorBase from './LocationSelectorBase';

const PopperComponent: React.ComponentType<PopperProps> = ({
    children,
    className,
    open,
    ...props
}) => {
    // useLockedBody();
    return (
        <Backdrop style={{ zIndex: 3, backgroundColor: 'transparent' }} open={open}>
            <PopperRoot transition open={open} {...props} className={className}>
                {({ TransitionProps }) => (
                    <Grow {...TransitionProps}>{children as React.ReactElement}</Grow>
                )}
            </PopperRoot>
        </Backdrop>
    );
};

const PopperRoot = styled(Popper)(({ theme }) => ({
    '& .MuiPaper-root': {
        boxShadow: 'none',
        border: `1px solid ${theme.palette.neutral[5]}`,
        backgroundColor: theme.palette.neutral[2],
        borderRadius: '15px',
        //borderColor: rgba(theme.palette.primary.main, 1),

        '& .MuiAutocomplete-option': {
            padding: 0,
            '& > div': {
                height: 38,
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                margin: '0 16px',
            },

            '& .useDivider': {
                borderBottom: `1px solid ${theme.palette.neutral[3]}`,
            },

            '&:hover': {
                backgroundColor: rgba(theme.palette.primary.main, 0.03),
            },
            '&[aria-selected=true]': {
                backgroundColor: rgba(theme.palette.primary.main, 0.08),
                '&:hover': {
                    backgroundColor: rgba(theme.palette.primary.main, 0.12),
                },
            },
        },
    },
    '& .MuiAutocomplete-listbox': {
        ...scrollbarStyle(),
    },

    '& .MuiAutocomplete-noOptions': {
        color: theme.palette.primary.main,
        fontWeight: 'bold',
        textAlign: 'center',
    },
}));

function ConversationLocationSelector(props: LocationSelectorProps) {
    return (
        <LocationSelectorBase
            numericPlaceholderAfterCount={0}
            style={{ width: 200 }}
            slotProps={{
                popper: {
                    sx: (theme) => ({
                        [`& .${autocompleteClasses.option}`]: {
                            borderBottom: `1px solid ${theme.palette.neutral[3]}`,
                        },
                    }),
                },
                paper: {
                    sx: {
                        backgroundColor: 'var(--neutral1) !important',
                    },
                },
            }}
            // customPopperComponent={PopperComponent}
            {...props}
        />
    );
}

export default ConversationLocationSelector;
