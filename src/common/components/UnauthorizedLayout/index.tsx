import { styled } from '@mui/material';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import React from 'react';
import backgroundViewByCost from '../../../assets/images/backgroundViewByCost.png';
import UnauthorizedHeaderBar from '../UnauthorizedHeaderBar';

type UnauthorizedLayoutProps = React.PropsWithChildren<{
    noPadding?: boolean;
    titleSection?: string;
}>;

const Body = styled('main', {
    shouldForwardProp: (prop) => !['noPadding'].includes(prop as string),
})<{ noPadding: boolean }>(({ theme, noPadding }) => ({
    width: '100%',
    minHeight: 'calc(100vh - var(--header-height,0px))',
    padding: noPadding ? '0px' : '17px 12px 0 12px',
    marginTop: 'var(--header-height,0px)',
    backgroundColor: '#fafafa',
    ...scrollbarStyle(),
    '&::-webkit-scrollbar': {
        width: 0,
    },
    '&::-webkit-scrollbar-track': {
        background: `transparent !important`,
    },
    [theme.breakpoints.up('sm')]: {
        backgroundImage: `url(${backgroundViewByCost})`,
        backgroundPosition: 'bottom right',
        backgroundSize: '250px',
        backgroundRepeat: 'no-repeat',
    },
}));

const UnauthorizedLayout = ({
    children,
    noPadding = false,
    titleSection,
}: UnauthorizedLayoutProps) => {
    return (
        <Body noPadding={noPadding}>
            <UnauthorizedHeaderBar titleSection={titleSection ?? ''} showLanguageOptions />

            {children}
        </Body>
    );
};

export default UnauthorizedLayout;
