export const numberPhoneFormatter = (numberFormat: string, phoneNumber: number | string) => {
    phoneNumber = numberPhoneUnformatter(phoneNumber.toString());
    let result = '';
    if (!numberFormat) return phoneNumber.toString();
    let numberIndex = 0;
    const numberDigits = phoneNumber.split('');
    for (const index in numberFormat.split('')) {
        if (numberFormat[index] == '9') {
            if (numberDigits[numberIndex]) {
                result = `${result}${numberDigits[numberIndex]}`;
                numberIndex++;
            }
        } else {
            result = `${result}${numberFormat[index]}`;
        }
    }
    return result.replace('?', ''); //Character in format number of the countries AE, DE, BR
};

export const numberPhoneUnformatter = (phoneNumber: number | string | undefined | null) => {
    if (!phoneNumber) return '';
    // eslint-disable-next-line no-useless-escape
    return phoneNumber.toString().replace(/[\/\(\)\-\s\+\*]/g, '');
};

export const phoneFormatRegexMask = (phoneFormat: string) => {
    phoneFormat = phoneFormat.replace(/\?/, '');
    const mask = [];
    for (const format of phoneFormat.split('')) {
        if (format == '9') {
            mask.push(/\d/);
        } else {
            mask.push(format);
        }
    }
    return mask;
};

export const exposeUnmaskedValue = (
    maskedValue: string,
    mask: (string | RegExp)[] | false
): string => {
    if (mask) {
        return (
            mask
                .map((value, index) => ({ value, index }))
                .filter(({ value }) => typeof value !== 'string')
                .map(({ index }) => maskedValue.charAt(index))
                //.filter((value) => value !== ' ')
                .join('')
        );
    }

    return '';
};

export type NumberAmountFormatOptions = {
    allowZero: boolean;
    requireDecimals: boolean;
    fractionDigits: number;
};
export const numberAmountFormatter = (
    amount: number | null | undefined,
    numberGroupSeparator: string,
    numberDecimalSeparator: string,
    options?: Partial<NumberAmountFormatOptions>
) => {
    const formatOptions: NumberAmountFormatOptions = {
        allowZero: false,
        requireDecimals: false,
        fractionDigits: 2,
        ...options,
    };

    if (amount === undefined) return '-';
    if (amount == null) return '-';
    if (Number.isNaN(amount)) return '-';
    if (amount === 0 && !formatOptions.allowZero) return '-';

    const amountParts = amount.toFixed(formatOptions.fractionDigits).split('.');
    const integerPart = amountParts[0].replace(/\B(?=(\d{3})+(?!\d))/g, numberGroupSeparator);
    const decimals = amountParts[1];
    if (!formatOptions.requireDecimals) {
        return integerPart;
    } else {
        return `${integerPart}${numberDecimalSeparator}${decimals}`;
    }
};

export const stringToTitleCase = (str?: string | null) => {
    if (!str) return '';
    if (str.trim() == '') return '';
    return str.replace(/\w\S*/g, function (txt) {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
};
