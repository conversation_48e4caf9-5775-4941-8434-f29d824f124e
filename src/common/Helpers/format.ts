export const formatTime = (seconds: number): string => {
    if (Number.isNaN(seconds) || Number.isFinite(seconds) === false) {
        return '--:--';
    }

    const toString = (value: number): string => {
        const str = value.toString();
        return str.length > 1 ? str : `0${str}`;
    };

    const minutes = Math.floor(seconds / 60);
    const restSeconds = Math.floor(seconds % 60);
    return `${toString(minutes)}:${toString(restSeconds)}`;
};

/**
 * Performs simple email validation.
 */
export function isValidEmail(email: string): boolean {
    return /^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email);
}
