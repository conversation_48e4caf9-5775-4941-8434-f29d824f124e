import { AppointmentDto } from 'api/appointments';
import moment from 'moment';

(window as any).moment = moment;

type LegacyVehicleData = {
    vehiclePlates?: string | null;
    vehicleColor?: string | null;
    vehicleYear?: string | null;
    vehicleModel?: string | null;

    vehicleMake?: string | null;
    vehicleVIN?: string | null;
};

type VehicleData = {
    vehiclePlates?: string | null;
    vehicleColor?: string | null;
    vehicleYear?: string | null;
    vehicleModel?: string | null;
    vehicleBrand?: string | null;
    vehicleVin?: string | null;
};

const isLegacyVehicleData = (v: VehicleData | LegacyVehicleData): v is LegacyVehicleData =>
    typeof (v as LegacyVehicleData).vehicleMake === 'string';

function isAppointmentDto(
    v: LegacyVehicleData | VehicleData | AppointmentDto | null
): v is AppointmentDto {
    if (v === null) return false;
    // those 2 check should be enough
    return (
        typeof (v as AppointmentDto).id === 'string' &&
        typeof (v as AppointmentDto).customer === 'object'
    );
}

export const getVehicleFormatted = (
    appointment?: LegacyVehicleData | VehicleData | AppointmentDto | null
): string => {
    if (!appointment) return '';
    if (isAppointmentDto(appointment)) {
        return [
            appointment.vehicle?.plates ?? '--',
            appointment.vehicle?.vin,
            appointment.vehicle?.brand,
            appointment.vehicle?.model,
            appointment.vehicle?.year,
            appointment.vehicle?.color,
        ]
            .filter(Boolean)
            .join(', ');
    }

    return [
        appointment.vehiclePlates ?? '--',
        isLegacyVehicleData(appointment) ? appointment.vehicleVIN : appointment.vehicleVin,
        isLegacyVehicleData(appointment) ? appointment.vehicleMake : appointment.vehicleBrand,
        appointment.vehicleModel,
        appointment.vehicleYear,
        appointment.vehicleColor,
    ]
        .filter(Boolean)
        .join(', ');
};

export const capitalizeFirstLetter = (value: string, locale: string = navigator.language) => {
    return value.charAt(0).toLocaleUpperCase(locale) + value.slice(1);
};

export const splitCamelCaseString = (input: string): string => {
    const flat = input.replace(/([a-z])([A-Z])/g, '$1 $2');
    return flat;
};

export const normalizeAccent = (value: string) => {
    return value
        .normalize('NFD') //Characters like 'é' are split into their base characters and their combining marks, 'e' and '´'.
        .replace(/[\u0300-\u036f]/g, '') //Remove all diacritic marks from the string.
        .toLowerCase();
};
