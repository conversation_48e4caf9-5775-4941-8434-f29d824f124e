import i18n from 'i18next';
import { DateTime, Settings } from 'luxon';
import moment from 'moment';
import 'moment/dist/locale/es';
import { getI18n, initReactI18next } from 'react-i18next';
import { get, onUpdate } from './translations';

export const whiteListLanguages = ['es', 'en'];

if (import.meta.env.DEV) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).DateTime = DateTime;
}
Settings.throwOnInvalid = true;

declare module 'luxon' {
    interface TSSettings {
        throwOnInvalid: true;
    }
}

const changeLanguage = (lang: string) => {
    moment.locale(lang);
    Settings.defaultLocale = lang;
    i18n.changeLanguage(lang);
    document.documentElement.setAttribute('lang', lang);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
(window as any).changeLanguage = changeLanguage;

export const initI18n = (lang: string) => {
    console.debug('[i18n] language loaded:', lang);

    document.documentElement.setAttribute('lang', lang);
    Settings.defaultLocale = lang;

    // capitalize months' names in spanish
    const langMoment = lang === 'br' ? 'pt-br' : lang;

    //moment.updateLocale also sets a locale for some reason. So it should be called before actual moment.locale.
    moment.updateLocale('es', {
        months: [
            'Enero',
            'Febrero',
            'Marzo',
            'Abril',
            'Mayo',
            'Junio',
            'Julio',
            'Agosto',
            'Septiembre',
            'Octubre',
            'Noviembre',
            'Diciembre',
        ],
        monthsShort: [
            'ene',
            'feb',
            'mar',
            'abr',
            'may',
            'jun',
            'jul',
            'ago',
            'sep',
            'oct',
            'nov',
            'dic',
        ],
    });

    moment.locale(langMoment);

    if (i18n.isInitialized) {
        i18n.changeLanguage(lang);
    } else {
        const { en, es } = get();

        i18n
            //.use(LanguageDetector)
            .use(initReactI18next)
            .init({
                pluralSeparator: '_',
                resources: {
                    es: {
                        translation: es,
                    },
                    en: {
                        translation: en,
                    },
                },
                supportedLngs: whiteListLanguages,
                // detection: { // not needed unless we enable LanguageDetector above
                //     order: ['path'],
                //     lookupFromPathIndex: 0,
                //     checkWhitelist: true,
                // },
                fallbackLng: lang,
            });

        onUpdate((translations) => {
            const { en, es } = translations;
            const reactI18N = getI18n();
            reactI18N.removeResourceBundle('en', 'translation');
            reactI18N.removeResourceBundle('es', 'translation');
            reactI18N.addResourceBundle('en', 'translation', en, true, true);
            reactI18N.addResourceBundle('es', 'translation', es, true, true);
            reactI18N.emit('languageChanged', reactI18N.language);
        });
    }
};

export default i18n;
