// ! WARNING !
// This file uses manual HMR, therefore we should be careful when editing it.
// If you make a change that changes the exports, the code might break in unexpected ways.

import en from 'translations/en.json';
import es from 'translations/es.json';

type LanguageKey = 'en' | 'es';
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Translations = Record<LanguageKey, Record<string, any>>;

let translations: Translations = { en, es };

// ! IMPORTANT !
// DO NOT remove this function, if you remove it, hot reload will break
// if you have to remove/rename it make sure to modify the code inside
// if (import.meta.hot) statement below
export function get(): Translations {
    return translations;
}

export let updateCallback: ((translations: Translations) => void) | undefined = undefined;

export function onUpdate(callback: (translations: Translations) => void) {
    updateCallback = callback;
}

if (import.meta.hot) {
    updateCallback = import.meta.hot.data.updateCallback;

    import.meta.hot.dispose((data) => {
        data.updateCallback = updateCallback;
    });

    import.meta.hot.accept((newModule) => {
        if (newModule === undefined) {
            // Syntax error occurred in the new module
            return;
        }
        const newTranslations: Translations = newModule.get();
        translations = newTranslations;

        if (updateCallback) {
            updateCallback(translations);
        }
    });
}
