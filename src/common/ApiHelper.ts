/**
 *
 * @returns Subdomain in window.location if subdomain include 'localhost' subdomain is VITE_LOCAL_SUBDOMAIN
 */
export const getSubdomain = () => {
    //TODO: edit this subdomain to change the subdomain you want to debug locally
    return isLocalhost() ? import.meta.env.VITE_LOCAL_SUBDOMAIN ?? '' : getSubdomainInLocation();
};

export const isLocalhost = () => getSubdomainInLocation().includes('localhost');

const getSubdomainInLocation = () => window.location.href.split('/')[2].split('.')[0];
