export enum PassInvalidKeys {
    Uppercase = 'uppercase',
    Lowercase = 'lowercase',
    Digits = 'digits',
    Special = 'special',
    Length = 'length',
}

/**
 *
 * @param password
 * @returns empty array if no errors or ErrorType
 */
export const checkInvalidPassword = (password: string): PassInvalidKeys[] => {
    const lowerscase = /(?=.*[a-z])/;
    const uppercase = /(?=.*[A-Z])/;
    const digits = /(?=.*\d)/;
    // eslint-disable-next-line no-useless-escape
    const special = /(?=.*[$@$!%&_\-:;<>=\~`'"/[\]{}()*+?.,\\^$|#\s])/;
    const invalids = [];
    if (password.length < 8) {
        invalids.push(PassInvalidKeys.Length);
    }
    if (!lowerscase.test(password)) {
        invalids.push(PassInvalidKeys.Lowercase);
    }
    if (!uppercase.test(password)) {
        invalids.push(PassInvalidKeys.Uppercase);
    }
    if (!digits.test(password)) {
        invalids.push(PassInvalidKeys.Digits);
    }
    if (!special.test(password)) {
        invalids.push(PassInvalidKeys.Special);
    }
    return invalids;
};
