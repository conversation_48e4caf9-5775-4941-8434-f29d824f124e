import { Box, Grid, styled } from '@mui/material';
import { TemplateCategory } from 'api/whatsapp';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { InfoTooltipInternal } from 'common/components/InfoTooltip';
import { RadioButton } from 'common/components/Inputs';
import InputWrapper from 'common/components/Inputs/InputWrapper';

import { NumberFormField } from 'common/components/Inputs/NumberField';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import { useEffect, useState } from 'react';
import {
    MuteNotificationsEditState,
    SILENCE_MODE_TIME_PERIODS,
    SilenceModeTimePeriod,
} from './util';

type MessageTypeTabProps = {
    preferences: MuteNotificationsEditState;
    setPreferences: (
        change: (preferences: MuteNotificationsEditState) => MuteNotificationsEditState
    ) => void;
};

const ALL_TEMPLATE_CATEGORIES: ReadonlyArray<TemplateCategory> = [
    'All',
    'AppointmentConfirmation',
    'AppointmentReminder',
    'SentDuringOrder',
    'Survey',
    'NextServiceReminder',
    'FollowUpMessages',
    'MassSendingMessages',
];

const IMAGES_EN: Record<Exclude<TemplateCategory, 'All'>, () => Promise<typeof import('*.png')>> = {
    AppointmentConfirmation: () =>
        import('assets/images/mutedPreferences/en/AppointmentConfirmation.png'),
    AppointmentReminder: () => import('assets/images/mutedPreferences/en/AppointmentReminder.png'),
    SentDuringOrder: () => import('assets/images/mutedPreferences/en/SentDuringOrder.png'),
    Survey: () => import('assets/images/mutedPreferences/en/Survey.png'),
    NextServiceReminder: () => import('assets/images/mutedPreferences/en/NextServiceReminder.png'),
    FollowUpMessages: () => import('assets/images/mutedPreferences/en/FollowUpMessages.png'),
    MassSendingMessages: () => import('assets/images/mutedPreferences/en/MassSendingMessages.png'),
};

const IMAGES_ES: Record<Exclude<TemplateCategory, 'All'>, () => Promise<typeof import('*.png')>> = {
    AppointmentConfirmation: () =>
        import('assets/images/mutedPreferences/es/AppointmentConfirmation.png'),
    AppointmentReminder: () => import('assets/images/mutedPreferences/es/AppointmentReminder.png'),
    SentDuringOrder: () => import('assets/images/mutedPreferences/es/SentDuringOrder.png'),
    Survey: () => import('assets/images/mutedPreferences/es/Survey.png'),
    NextServiceReminder: () => import('assets/images/mutedPreferences/es/NextServiceReminder.png'),
    FollowUpMessages: () => import('assets/images/mutedPreferences/es/FollowUpMessages.png'),
    MassSendingMessages: () => import('assets/images/mutedPreferences/es/MassSendingMessages.png'),
};

function getLabel(t: TFunction, value: TemplateCategory): string {
    return t(`conversations.mutePreferences.templateCategories.${value}`);
}

const DurationValueField = styled(NumberFormField)({
    textAlign: 'right',
});

const ErrorText = styled('p')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.error.main,
}));

export default function MessageTypeTab({ preferences, setPreferences }: MessageTypeTabProps) {
    const { t } = useAppTranslation();
    const isSingularPeriodOptions = preferences.typeOfMessage.silenceDurationParts.value === 1;
    const isSilenceDurationInvalid =
        preferences.typeOfMessage.silenceDurationParts.value <= 0 &&
        preferences.typeOfMessage.silenceMode === 'Custom';

    return (
        <Grid container spacing={6}>
            <Grid item xs={12} md={6}>
                <InputWrapper
                    tooltipText={t('conversations.mutePreferences.popup.templateCategoriesTooltip')}
                    label={t('conversations.mutePreferences.templateCategories.label')}
                >
                    <SSelectInput
                        fullWidth
                        multiple
                        value={preferences.typeOfMessage.templateCategories}
                        onChange={(event) => {
                            let value = event.target.value as TemplateCategory[];
                            if (value[value.length - 1] === 'All') value = ['All'];
                            else if (value.includes('All'))
                                value = value.filter((x) => x !== 'All');
                            setPreferences((preferences) => ({
                                ...preferences,
                                typeOfMessage: {
                                    ...preferences.typeOfMessage,
                                    templateCategories: value,
                                },
                            }));
                        }}
                        renderValue={(value) =>
                            (value as TemplateCategory[]).map((x) => getLabel(t, x)).join(', ') ||
                            t('conversations.mutePreferences.templateCategories.placeholder')
                        }
                    >
                        {ALL_TEMPLATE_CATEGORIES.map((value) => {
                            return (
                                <SMenuItem value={value} key={value}>
                                    {preferences.typeOfMessage.templateCategories.includes(
                                        value
                                    ) ? (
                                        <CheckBoxIcon />
                                    ) : (
                                        <UncheckBoxIcon />
                                    )}
                                    {getLabel(t, value)}
                                    {value !== 'All' && <ImageTooltip templateCategory={value} />}
                                </SMenuItem>
                            );
                        })}
                    </SSelectInput>
                </InputWrapper>
            </Grid>
            <Grid item xs={12} md={6}>
                <InputWrapper
                    tooltipText={t('conversations.mutePreferences.popup.silenceDurationTooltip')}
                    label={t('conversations.mutePreferences.popup.silenceDuration')}
                >
                    <div>
                        <Box sx={{ display: 'flex', gridGap: '16px', alignItems: 'center' }}>
                            <RadioButton
                                onChange={() =>
                                    setPreferences((p) => ({
                                        ...p,
                                        typeOfMessage: {
                                            ...p.typeOfMessage,
                                            silenceMode: 'Always',
                                        },
                                    }))
                                }
                                checked={preferences.typeOfMessage.silenceMode === 'Always'}
                                label={t('conversations.mutePreferences.popup.always')}
                                name="silence-mode-always"
                            />
                            <RadioButton
                                onChange={() =>
                                    setPreferences((p) => ({
                                        ...p,
                                        typeOfMessage: {
                                            ...p.typeOfMessage,
                                            silenceMode: 'Custom',
                                        },
                                    }))
                                }
                                checked={preferences.typeOfMessage.silenceMode === 'Custom'}
                                label={t('conversations.mutePreferences.popup.custom')}
                                name="silence-mode-always"
                            />
                            <Box sx={{ maxWidth: 70 }}>
                                <DurationValueField
                                    value={preferences.typeOfMessage.silenceDurationParts.value}
                                    //size={InputSize.S}
                                    disabled={preferences.typeOfMessage.silenceMode !== 'Custom'}
                                    showValidationIndicators={
                                        preferences.typeOfMessage.silenceDurationParts.value !== 0
                                    }
                                    // enableValidation={
                                    //     preferences.typeOfMessage.silenceDurationParts.value === 0
                                    // }
                                    onValueChange={(x) => {
                                        if (
                                            x.floatValue &&
                                            (x.floatValue > 9999 || x.floatValue < 0)
                                        ) {
                                            setPreferences((x) => ({
                                                ...x,
                                            }));
                                            return;
                                        }
                                        setPreferences((p) => ({
                                            ...p,
                                            typeOfMessage: {
                                                ...p.typeOfMessage,
                                                silenceDurationParts: {
                                                    ...p.typeOfMessage.silenceDurationParts,
                                                    value: Math.round(x.floatValue ?? 0),
                                                },
                                            },
                                        }));
                                    }}
                                />
                            </Box>
                            <SSelectInput
                                disabled={preferences.typeOfMessage.silenceMode !== 'Custom'}
                                onChange={(e) =>
                                    setPreferences((p) => ({
                                        ...p,
                                        typeOfMessage: {
                                            ...p.typeOfMessage,
                                            silenceDurationParts: {
                                                ...p.typeOfMessage.silenceDurationParts,
                                                period: e.target.value as SilenceModeTimePeriod,
                                            },
                                        },
                                    }))
                                }
                                value={preferences.typeOfMessage.silenceDurationParts.period}
                            >
                                {SILENCE_MODE_TIME_PERIODS.map((value) => (
                                    <SMenuItem value={value} key={value}>
                                        {t(
                                            `commonLabels.timePeriods.${value}${
                                                isSingularPeriodOptions ? '-singular' : ''
                                            }`
                                        )}
                                    </SMenuItem>
                                ))}
                            </SSelectInput>
                        </Box>
                        {isSilenceDurationInvalid && (
                            <ErrorText>
                                {t('conversations.mutePreferences.popup.invalidSilenceDuration')}
                            </ErrorText>
                        )}
                    </div>
                </InputWrapper>
            </Grid>
        </Grid>
    );
}

type ImageTooltipProps = {
    templateCategory: Exclude<TemplateCategory, 'All'>;
};

const Img = styled('img')({
    pointerEvents: 'none',
    position: 'fixed',
    // maxHeight: '50vh',
    // maxWidth: '40vw',
    overflow: 'hidden',
    borderRadius: '10px',
    boxShadow: '0 0 10px var(--neutral5)',
    zIndex: 2,
    transform: 'translateY(-35%)',
    left: 0,
    opacity: 0,
    transition: 'opacity .3s',
});

function ImageTooltip({ templateCategory }: ImageTooltipProps) {
    const {
        i18n: { language },
    } = useAppTranslation();
    const images = language.startsWith('es') ? IMAGES_ES : IMAGES_EN;
    const imagePromise = images[templateCategory];

    const [show, setShow] = useState(false);
    const [url, setUrl] = useState<string | null>(null);
    const [anchor, setAnchor] = useState<HTMLElement | null>();
    const [imgRef, setImgRef] = useState<HTMLImageElement | null>(null);

    useEffect(() => {
        if (!anchor || !imgRef) return;

        const callback = () => {
            const position = anchor.getBoundingClientRect().right + 15;
            imgRef.style.opacity = '1';
            imgRef.style.left = `${position}px`;
            imgRef.style.maxWidth = `${Math.max(window.innerWidth - position - 15, 200)}px`;
            // imgRef.style.minWidth = `${Math.max(0, (window.innerWidth - position) * 0.8)}px`;
        };

        callback();
        document.addEventListener('resize', callback);

        return () => {
            document.removeEventListener('resize', callback);
        };
    }, [anchor, imgRef]);

    useEffect(() => {
        if (show && !url) {
            imagePromise().then((v) => setUrl(v.default));
        }
    }, [imagePromise, url, show]);

    return (
        <>
            <InfoTooltipInternal
                ref={setAnchor}
                onMouseEnter={() => setShow(true)}
                onMouseLeave={() => setShow(false)}
            />
            {show && url && <Img ref={setImgRef} src={url} />}
        </>
    );
}
