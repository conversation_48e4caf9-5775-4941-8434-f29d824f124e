import { Box, CheckboxProps, FormControlLabel, Grid, styled } from '@mui/material';
import { Checkbox } from 'common/components/Inputs';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import TagsInput, { Tag } from 'common/components/Inputs/TagsInput';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { MuteNotificationsEditState } from './util';

type ExceptionsTabProps = {
    preferences: MuteNotificationsEditState;
    setPreferences: (
        change: (preferences: MuteNotificationsEditState) => MuteNotificationsEditState
    ) => void;
};

const NoteText = styled('p')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
}));

const PREDEFINED_DAYS = [5, 10, 15, 30];

export default function ExceptionsTab({ preferences, setPreferences }: ExceptionsTabProps) {
    const { t } = useAppTranslation();

    return (
        <>
            <InputLabel /*theme={InputTheme.rectangle}*/>
                {t('conversations.mutePreferences.popup.exceptionsLabel')}
            </InputLabel>

            <Grid container spacing={6}>
                <Grid item xs={12} md={6}>
                    <CheckboxItem
                        checked={preferences.exceptions.businessCustomers}
                        label={t('conversations.mutePreferences.popup.businessCustomers')}
                        onChange={(e) =>
                            setPreferences((preferences) => ({
                                ...preferences,
                                exceptions: {
                                    ...preferences.exceptions,
                                    businessCustomers: e.target.checked,
                                },
                            }))
                        }
                        tooltip={t('conversations.mutePreferences.popup.businessCustomersTooltip')}
                    />
                    <CheckboxItem
                        checked={preferences.exceptions.ordersWithoutActivity}
                        label={t('conversations.mutePreferences.popup.ordersWithoutActivity')}
                        onChange={(e) =>
                            setPreferences((preferences) => ({
                                ...preferences,
                                exceptions: {
                                    ...preferences.exceptions,
                                    ordersWithoutActivity: e.target.checked,
                                },
                            }))
                        }
                        tooltip={t(
                            'conversations.mutePreferences.popup.ordersWithoutActivityTooltip'
                        )}
                    >
                        <Box sx={{ display: 'flex', gridGap: '8px' }}>
                            <InputLabel /*theme={InputTheme.rectangle}*/>
                                {t('conversations.mutePreferences.popup.timeWithoutActivity')}
                            </InputLabel>
                            <SSelectInput
                                disabled={!preferences.exceptions.ordersWithoutActivity}
                                value={preferences.exceptions.ordersWithoutActivityIntervalInDays}
                                onChange={(e) =>
                                    setPreferences((preferences) => ({
                                        ...preferences,
                                        exceptions: {
                                            ...preferences.exceptions,
                                            ordersWithoutActivityIntervalInDays: e.target
                                                .value as number,
                                        },
                                    }))
                                }
                            >
                                {!PREDEFINED_DAYS.includes(
                                    preferences.exceptions.ordersWithoutActivityIntervalInDays
                                ) && (
                                    <SMenuItem
                                        value={
                                            preferences.exceptions
                                                .ordersWithoutActivityIntervalInDays
                                        }
                                    >
                                        {preferences.exceptions.ordersWithoutActivityIntervalInDays}{' '}
                                        {t('commonLabels.timePeriods.d')}
                                    </SMenuItem>
                                )}
                                {PREDEFINED_DAYS.map((days) => (
                                    <SMenuItem value={days} key={days}>
                                        {days} {t('commonLabels.timePeriods.d')}
                                    </SMenuItem>
                                ))}
                            </SSelectInput>
                        </Box>
                    </CheckboxItem>
                </Grid>
                <Grid item xs={12} md={6}>
                    <CheckboxItem
                        checked={preferences.exceptions.keywordsEnabled}
                        label={t('conversations.mutePreferences.popup.keywords')}
                        tooltip={t('conversations.mutePreferences.popup.keywordsTooltip')}
                        onChange={(e) =>
                            setPreferences((preferences) => ({
                                ...preferences,
                                exceptions: {
                                    ...preferences.exceptions,
                                    keywordsEnabled: e.target.checked,
                                },
                            }))
                        }
                    >
                        <KeywordsInput
                            disabled={!preferences.exceptions.keywordsEnabled}
                            value={preferences.exceptions.keywords}
                            onChange={(e) =>
                                setPreferences((preferences) => ({
                                    ...preferences,
                                    exceptions: { ...preferences.exceptions, keywords: e },
                                }))
                            }
                        />
                        <NoteText>
                            <strong>{t('commonLabels.note')}:</strong>{' '}
                            {t('conversations.mutePreferences.popup.keywordsNote')}
                        </NoteText>
                    </CheckboxItem>
                </Grid>
            </Grid>
        </>
    );
}

type CheckboxItemProps = {
    checked: boolean;
    onChange: CheckboxProps['onChange'];
    label: string;
    tooltip?: string;
    children?: React.ReactNode;
};

const SCheckbox = styled(Checkbox)({
    padding: 1,
});

const CheckboxRoot = styled('div')({});

const CheckboxLabel = styled(InputLabel)({
    fontWeight: 'normal',
    userSelect: 'none',
});

const CheckboxContent = styled('div')({
    paddingLeft: 25,
});

const SFormControlLabel = styled(FormControlLabel)({
    marginRight: 0,
    marginLeft: 0,
});

function CheckboxItem({ checked, onChange, label, children, tooltip }: CheckboxItemProps) {
    return (
        <CheckboxRoot>
            <SFormControlLabel
                label={
                    <CheckboxLabel
                        /*theme={InputTheme.rectangle}*/
                        tooltipText={tooltip}
                    >
                        {label}
                    </CheckboxLabel>
                }
                control={<SCheckbox checked={checked} onChange={onChange} />}
            />

            {children && <CheckboxContent>{children}</CheckboxContent>}
        </CheckboxRoot>
    );
}

type KeywordInputProps = {
    value: string[];
    onChange: (newValue: string[]) => void;
    disabled: boolean;
};

const WarningText = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.warning.main,
}));

function KeywordsInput({ value, onChange, disabled }: KeywordInputProps) {
    const { t } = useAppTranslation();
    const reachedLimit = value.length >= 20;

    return (
        <>
            <TagsInput
                disabled={disabled || reachedLimit}
                onAdded={(item) => {
                    item = item.trim().replaceAll(/[ ]{2,}/g, ' ');
                    if (!value.some((x) => x.trim().toLowerCase() === item.toLowerCase())) {
                        onChange([...value, item]);
                    }
                }}
            >
                {value.map((tag) => (
                    <Tag
                        onRemove={() => {
                            onChange(value.filter((x) => x !== tag));
                        }}
                        key={tag}
                        label={tag}
                    />
                ))}
            </TagsInput>
            {reachedLimit && (
                <WarningText>
                    {t('conversations.mutePreferences.popup.keywordsLimitWarning')}
                </WarningText>
            )}
        </>
    );
}
