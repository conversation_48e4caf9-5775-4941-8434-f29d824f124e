import { useMutation, useQuery } from '@tanstack/react-query';
import { TimeSpan } from 'api/utils/format';
import WhatsAppApi, { MuteNotificationsPreferences } from 'api/whatsapp';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import xor from 'lodash/xor';
import { isErrorResponse } from 'services/Server';
import { useCurrentUser } from 'store/slices/user';

export function useMutePreferencesQuery(enabled: boolean) {
    const userId = useCurrentUser().key;
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const mutePreferencesQuery = useQuery(['conversations', 'mute-preferences', userId], {
        queryFn: () => WhatsAppApi.getMuteNotifications(),
        enabled,
        refetchInterval: false,
        refetchOnReconnect: true,
        refetchOnWindowFocus: false,
    });

    const saveMutation = useMutation(WhatsAppApi.updateMuteNotifications, {
        onError: (error) => {
            if (isErrorResponse(error)) {
                toasters.danger(error.message, t('toasters.errorOccurred'));
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        },
    });

    return {
        query: mutePreferencesQuery,
        saveMutation,
    };
}

export type SilenceModeTimePeriod = 'd' | 'w' | 'M' | 'y';
export const SILENCE_MODE_TIME_PERIODS = Object.freeze([
    'd',
    'w',
    'M',
    'y',
] as SilenceModeTimePeriod[]);

export type MuteNotificationsEditState = {
    exceptions: Omit<
        MuteNotificationsPreferences['exceptions'],
        'ordersWithoutActivityInterval'
    > & {
        ordersWithoutActivityIntervalInDays: number;
    };
    typeOfMessage: Omit<MuteNotificationsPreferences['typeOfMessage'], 'silenceDuration'> & {
        silenceDurationParts: {
            value: number;
            period: SilenceModeTimePeriod;
        };
    };
};

export function mutePreferencesToEditState(
    state: MuteNotificationsPreferences
): MuteNotificationsEditState {
    const [value, period] = timeSpanToParts(state.typeOfMessage.silenceDuration);
    const { ordersWithoutActivityInterval, ...exceptions } = state.exceptions;
    return {
        exceptions: {
            ...exceptions,
            ordersWithoutActivityIntervalInDays: TimeSpan.fromString(ordersWithoutActivityInterval)
                .days,
        },
        typeOfMessage: {
            ...state.typeOfMessage,
            silenceDurationParts: {
                value,
                period,
            },
        },
    };
}

export function editStateToMutePreferences(
    state: MuteNotificationsEditState
): MuteNotificationsPreferences {
    const { value, period } = state.typeOfMessage.silenceDurationParts;
    const { ordersWithoutActivityIntervalInDays, ...exceptions } = state.exceptions;
    return {
        exceptions: {
            ...exceptions,
            ordersWithoutActivityInterval: new TimeSpan(
                ordersWithoutActivityIntervalInDays * 24 * 60 * 60
            ).toString(),
        },
        typeOfMessage: {
            silenceDuration: partsToTimeSpan(value, period),
            templateCategories: state.typeOfMessage.templateCategories,
            silenceMode: state.typeOfMessage.silenceMode,
        },
    };
}

const SECONDS_PER_DAY = 24 * 60 * 60;
const SECOND_MULTIPLIERS: Record<SilenceModeTimePeriod, number> = {
    d: 1 * SECONDS_PER_DAY,
    w: 7 * SECONDS_PER_DAY,
    M: 30 * SECONDS_PER_DAY,
    y: 365 * SECONDS_PER_DAY,
};

function timeSpanToParts(timespan: string): [number, SilenceModeTimePeriod] {
    try {
        const ts = TimeSpan.fromString(timespan);
        const seconds = ts.totalSeconds;

        const gt0AndInt = (x: number) => x > 0 && Math.floor(x) === x;

        const years = seconds / SECOND_MULTIPLIERS.y;
        if (gt0AndInt(years)) return [years, 'y'];

        const months = seconds / SECOND_MULTIPLIERS.M;
        if (gt0AndInt(months)) return [months, 'M'];

        const weeks = seconds / SECOND_MULTIPLIERS.w;
        if (gt0AndInt(weeks)) return [weeks, 'w'];

        return [Math.floor(seconds / SECOND_MULTIPLIERS.d), 'd'];
    } catch {
        return [0, 'd'];
    }
}

function partsToTimeSpan(value: number, period: SilenceModeTimePeriod): string {
    if (value < 0) throw new Error('value cannot be negative');
    return new TimeSpan(value * SECOND_MULTIPLIERS[period]).toString();
}

export const DEFAULT_MUTE_NOTIFICATIONS_STATE: MuteNotificationsEditState = {
    typeOfMessage: {
        templateCategories: [],
        silenceMode: 'Always',
        silenceDurationParts: {
            value: 0,
            period: 'd',
        },
    },
    exceptions: {
        keywordsEnabled: false,
        keywords: [],
        businessCustomers: false,
        ordersWithoutActivity: false,
        ordersWithoutActivityIntervalInDays: 0,
    },
};

export function isMuteNotificationsEditStateValid(state: MuteNotificationsEditState): boolean {
    const silenceDurationValid =
        state.typeOfMessage.silenceMode !== 'Custom' ||
        state.typeOfMessage.silenceDurationParts.value > 0;

    return silenceDurationValid;
}

export function stateEqualToPreferences(
    editData: MuteNotificationsEditState,
    data: MuteNotificationsPreferences | undefined
): boolean {
    if (!data) return false;

    let silenceDurationEqual = false;

    if (data.typeOfMessage.silenceMode !== editData.typeOfMessage.silenceMode) {
        silenceDurationEqual = false;
    } else if (data.typeOfMessage.silenceMode === 'Always') {
        silenceDurationEqual = true;
    } else {
        silenceDurationEqual =
            data.typeOfMessage.silenceDuration ===
            partsToTimeSpan(
                editData.typeOfMessage.silenceDurationParts.value,
                editData.typeOfMessage.silenceDurationParts.period
            );
    }

    return (
        data.exceptions.businessCustomers === editData.exceptions.businessCustomers &&
        data.exceptions.ordersWithoutActivity === editData.exceptions.ordersWithoutActivity &&
        TimeSpan.fromString(data.exceptions.ordersWithoutActivityInterval).days ===
            editData.exceptions.ordersWithoutActivityIntervalInDays &&
        data.exceptions.keywordsEnabled === editData.exceptions.keywordsEnabled &&
        xor(data.exceptions.keywords, editData.exceptions.keywords).length === 0 &&
        xor(data.typeOfMessage.templateCategories, editData.typeOfMessage.templateCategories)
            .length === 0 &&
        silenceDurationEqual
    );
}
