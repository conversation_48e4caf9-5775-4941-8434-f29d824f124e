import { styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import CancelModal from 'common/components/Popups/CancelModal';
import SimpleTabsWithContent from 'common/components/tabs/SimpleTabsWithContent';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';
import ExceptionsTab from './ExceptionsTab';
import MessageTypeTab from './MessageTypeTab';
import {
    DEFAULT_MUTE_NOTIFICATIONS_STATE,
    MuteNotificationsEditState,
    editStateToMutePreferences,
    isMuteNotificationsEditStateValid,
    mutePreferencesToEditState,
    stateEqualToPreferences,
    useMutePreferencesQuery,
} from './util';

type MutePreferencesPopupProps = {
    open: boolean;
    onClose: () => void;
};

const ModalBoxComponent = styled('div')({
    minWidth: 960,
    maxWidth: 960,
    minHeight: 300,
    padding: '40px 60px',
    boxSizing: 'border-box',
});

const Header = styled('section')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
});

const HeaderText = styled('h4')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[8],
    margin: 0,
}));

const ButtonsContainer = styled('div')({
    display: 'flex',
    gap: '8px',
});

const TabWrapper = styled('div')({
    marginTop: 18,
});

type Tab = 'messageType' | 'exceptions';

export default function MutePreferencesPopup({ open, onClose }: MutePreferencesPopupProps) {
    const { t } = useAppTranslation();
    const [selectedTab, setSelectedTab] = useState<Tab>('messageType');
    const [exitConfirmationPopupOpen, setExitConfirmationPopupOpen] = useState(false);
    const [editData, setEditData] = useState<MuteNotificationsEditState>(
        DEFAULT_MUTE_NOTIFICATIONS_STATE
    );

    const { query, saveMutation } = useMutePreferencesQuery(open);
    const hasChanges = !stateEqualToPreferences(editData, query.data);
    const isInvalid = !isMuteNotificationsEditStateValid(editData);

    useEffect(() => {
        if (open) {
            if (query.data) setEditData(mutePreferencesToEditState(query.data));
        }
    }, [query.data, open]);

    useEffect(() => {
        if (!open) {
            setSelectedTab('messageType');
        }
    }, [open]);

    const resetState = useCallback(() => {
        setEditData(DEFAULT_MUTE_NOTIFICATIONS_STATE);
    }, []);

    const closePopup = useCallback(() => {
        setExitConfirmationPopupOpen(false);
        resetState();
        onClose();
    }, [onClose, resetState]);

    const requestClosePopup = useCallback(() => {
        if (hasChanges && !query.isLoading) {
            setExitConfirmationPopupOpen(true);
        } else {
            closePopup();
        }
    }, [closePopup, hasChanges, query.isLoading]);

    const savePreferences = async () => {
        try {
            const preferences = editStateToMutePreferences(editData);
            await saveMutation.mutateAsync(preferences);
            closePopup();
        } catch {
            // ignored (error is handled in mutation hook)
        }
    };

    return (
        <>
            <Modal boxComponent={ModalBoxComponent} open={open} onClose={requestClosePopup}>
                <Header>
                    <HeaderText>{t('conversations.mutePreferences.popup.title')}</HeaderText>

                    <ButtonsContainer>
                        <Button
                            label={t('commonLabels.cancel')}
                            w="md"
                            color={Colors.Neutral3}
                            onClick={requestClosePopup}
                            disabled={saveMutation.isLoading}
                        />
                        <Button
                            label={t('commonLabels.save')}
                            w="md"
                            color={Colors.Success}
                            onClick={savePreferences}
                            disabled={
                                saveMutation.isLoading ||
                                query.isLoading ||
                                !hasChanges ||
                                isInvalid
                            }
                            showLoader={saveMutation.isLoading}
                        />
                    </ButtonsContainer>
                </Header>
                <SimpleTabsWithContent
                    selected={selectedTab}
                    onTabSelected={setSelectedTab as (x: string) => void}
                    tabs={[
                        {
                            key: 'messageType',
                            label: t('conversations.mutePreferences.popup.messageType'),
                            content: (
                                <TabWrapper>
                                    <MessageTypeTab
                                        preferences={editData}
                                        setPreferences={setEditData}
                                    />
                                </TabWrapper>
                            ),
                        },
                        {
                            key: 'exceptions',
                            label: t('conversations.mutePreferences.popup.exceptions'),
                            content: (
                                <TabWrapper>
                                    <ExceptionsTab
                                        preferences={editData}
                                        setPreferences={setEditData}
                                    />
                                </TabWrapper>
                            ),
                        },
                    ]}
                />
            </Modal>
            <CancelModal
                title={t('conversations.mutePreferences.popup.cancel')}
                open={exitConfirmationPopupOpen && open}
                onClose={() => setExitConfirmationPopupOpen(false)}
                onCancel={closePopup}
            />
        </>
    );
}
