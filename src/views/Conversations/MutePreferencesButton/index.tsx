import { VolumeUp } from '@mui/icons-material';
import { IconButton, styled } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { useState } from 'react';
import MutePreferencesPopup from './MutePreferencesPopup';

const SIconButton = styled(IconButton)(({ theme }) => ({
    color: 'var(--cm1)',
    border: '1px solid var(--cm1)',

    '&:hover': {
        backgroundColor: rgba(theme.palette.primary.main, 0.2),
    },
}));

export default function MutePreferencesButton() {
    const { t } = useAppTranslation();
    const [popupOpen, setPopupOpen] = useState(false);

    return (
        <>
            <ArrowTooltip content={t('conversations.mutePreferences.tooltip')}>
                <SIconButton
                    onClick={() => {
                        setPopupOpen(true);
                    }}
                    size="small"
                >
                    <VolumeUp />
                </SIconButton>
            </ArrowTooltip>
            <MutePreferencesPopup open={popupOpen} onClose={() => setPopupOpen(false)} />
        </>
    );
}
