import { styled } from '@mui/material';
import { MessageDto } from 'api/whatsapp';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { useResizeDetector } from 'react-resize-detector';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import DateLabel from './DateLabel';
import Message from './Message';

type MessagesProps = {
    messages: MessageDto[];
    onScrollbarWidthChanged: (width: number) => void;
};

export default function Messages({ messages, onScrollbarWidthChanged }: MessagesProps) {
    const { internationalization } = useAppSelector(selectSettings);
    const { width, ref: messagesRef } = useResizeDetector<HTMLDivElement>();
    const messagesEndRef = useRef<HTMLDivElement | null>(null);
    const [scrollBarWidth, setScrollBarWidth] = useState(0);

    useEffect(() => {
        if (messagesRef?.current) {
            setScrollBarWidth(messagesRef.current.offsetWidth - messagesRef.current.clientWidth);
        }
    }, [width, messagesRef]);

    useEffect(() => {
        onScrollbarWidthChanged(scrollBarWidth);
    }, [scrollBarWidth, onScrollbarWidthChanged]);

    const atTheBottom = messagesRef.current
        ? messagesRef.current.scrollTop + messagesRef.current.offsetHeight ===
          messagesRef.current.scrollHeight
        : false;

    useEffect(() => {
        if (atTheBottom) {
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [messages]);

    const groups = useMemo(() => {
        // group messages by date
        // then reverse the list of date groups because date groups are displayed in a flexbox with column-reverse direction
        return groupMessages(messages, internationalization.ianaTzId).toReversed();
    }, [messages, internationalization.ianaTzId]);

    const onScroll = useCallback(() => {}, []);

    return (
        <StyledMessagesContainer onScroll={onScroll} ref={messagesRef}>
            {groups.map(({ date, timeGroups }) => (
                <StyledDateGroup key={date}>
                    <DateLabel date={date} />
                    {timeGroups.map((timeGroup) => (
                        <StyledTimeGroupRow
                            inbound={timeGroup.isInbound}
                            key={`${timeGroup.messages[0].sentAt} ${timeGroup.user?.id ?? 'null'} ${
                                timeGroup.isInbound
                            }`}
                        >
                            <div className="timeGroupColumn">
                                <div className="messageGroup">
                                    {timeGroup.messages.map((message) => (
                                        <Message key={message.id} message={message} />
                                    ))}
                                </div>
                                <StyledTimeLabel>
                                    {timeGroup.time}{' '}
                                    {timeGroup.user ? (
                                        <Trans
                                            i18nKey="conversations.sentBy"
                                            values={{
                                                name:
                                                    timeGroup.user.displayName ||
                                                    timeGroup.user.name,
                                            }}
                                            components={{ bold: <strong /> }}
                                        />
                                    ) : undefined}
                                </StyledTimeLabel>
                            </div>
                        </StyledTimeGroupRow>
                    ))}
                </StyledDateGroup>
            ))}
            <div ref={messagesEndRef} />
        </StyledMessagesContainer>
    );
}

const StyledDateGroup = styled('div')({
    position: 'relative',
});

const StyledMessagesContainer = styled('div')({
    paddingTop: 16,
    paddingLeft: 32,
    paddingRight: 32,
    height: '100%',
    ...scrollbarStyle({ size: 12 }),
    // flexGrow: 1,
    // height: '400px',
    overflow: 'auto',
    display: 'flex',
    // justifyContent: 'left',
    flexDirection: 'column-reverse',
});

const StyledTimeLabel = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[6],
    ...theme.typography.h8Roboto,
    marginTop: 8,
    marginBottom: 20,
}));

const StyledTimeGroupRow = styled('div', {
    shouldForwardProp: (prop) => !['inbound'].includes(prop as string),
})<{ inbound: boolean }>(({ theme, inbound }) => ({
    display: 'flex',
    justifyContent: inbound ? 'flex-start' : 'flex-end',

    '& .timeGroupColumn': {
        display: 'flex',
        flexDirection: 'column',

        '& .messageGroup': {
            display: 'flex',
            alignItems: inbound ? 'flex-start' : 'flex-end',
            flexDirection: 'column',
            rowGap: 3,

            '& .message': {
                paddingLeft: 14,
                paddingRight: 14,
                paddingTop: 8,
                paddingBottom: 8,
                backgroundColor: '#EAFFD7',
                maxWidth: 413,
                borderRadius: 20,
                whiteSpace: 'break-spaces',
                ...theme.typography.h6Roboto,
                fontWeight: 'normal',
                color: theme.palette.neutral[9],
            },
        },
    },
    '&.inbound': {
        justifyContent: 'flex-start',

        '& .timeGroupColumn': {
            '& .messageGroup': {
                alignItems: 'flex-start',

                '& .message': {
                    backgroundColor: '#F6F6F6',
                },
            },
        },
    },
}));

type MessagesTimeGroup = {
    time: string;
    user: null | { id: string; name: string; displayName: string };
    isInbound: boolean;

    messages: MessageDto[];
};

type MessageDateGroup = {
    date: string;
    timeGroups: MessagesTimeGroup[];
};

function groupMessages(messages: MessageDto[], tz: string) {
    const groups: MessageDateGroup[] = [];
    let group: MessageDateGroup | undefined;

    const sortedMessages = [...messages];
    sortedMessages.sort(
        (a, b) => DateTime.fromISO(a.sentAt).toMillis() - DateTime.fromISO(b.sentAt).toMillis()
    );

    for (const message of sortedMessages) {
        const dtObject = DateTime.fromISO(message.sentAt);
        const date = dtObject.toFormat('yyyy-MM-dd');
        const time = dtObject.toFormat('hh:mm a');

        if (group) {
            if (group.date === date) {
                const lastTimeGroup = group.timeGroups[group.timeGroups.length - 1];
                if (
                    lastTimeGroup.time === time &&
                    message.user?.id === lastTimeGroup.user?.id &&
                    message.isInbound === lastTimeGroup.isInbound
                ) {
                    lastTimeGroup.messages.push(message);
                } else {
                    group.timeGroups.push({
                        user: message.user,
                        isInbound: message.isInbound,
                        time,
                        messages: [message],
                    });
                }

                continue;
            } else {
                groups.push(group);
            }
        }

        group = {
            date,
            timeGroups: [
                {
                    time,
                    user: message.user,
                    isInbound: message.isInbound,
                    messages: [message],
                },
            ],
        };
    }

    if (group) {
        groups.push(group);
    }

    return groups;
}
