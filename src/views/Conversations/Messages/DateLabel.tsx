import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import moment from 'moment';

type DateLabelProps = {
    date: string;
};

const Root = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    marginBottom: 16,
    position: 'sticky',
    zIndex: 1,
    top: 0,
});

const Label = styled('div')(({ theme }) => ({
    paddingLeft: 8,
    paddingRight: 8,
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: 'var(--cm5)',
    color: theme.palette.primary.light,
    ...theme.typography.h8Roboto,
    fontWeight: 'bold',
    borderRadius: 20,
    textTransform: 'uppercase',
}));

const getDay = (date: string, t: TFunction) => {
    const m = moment(date);
    if (m.isSame(moment(), 'day')) return t('commonLabels.today');
    if (m.isSame(moment().subtract(1, 'day'), 'day')) return t('commonLabels.yesterday');
    return m.format(t('conversations.dateFormat'));
};

export default function DateLabel({ date }: DateLabelProps) {
    const { t } = useAppTranslation();
    const dateStr = getDay(date, t);

    return (
        <Root>
            <Label>{dateStr}</Label>
        </Root>
    );
}
