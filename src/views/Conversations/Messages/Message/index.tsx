import { ErrorOutline } from '@mui/icons-material';
import { Box, IconButton, Slider, sliderClasses, styled } from '@mui/material';
import { TimeSpan } from 'api/utils/format';
import { MessageDto } from 'api/whatsapp';
import { formatTime } from 'common/Helpers';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { PdfIcon } from 'common/components/Icons/PdfIcon';
import { PlayIcon } from 'common/components/Icons/PlayIcon';
import { usePlayer } from 'common/hooks/usePlayer';
import { Interweave } from 'interweave';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createOggAudioElement } from 'utils/audio';
import { createEvent } from 'utils/event';

type MessageProps = {
    message: MessageDto;
};

const Message = memo(({ message }: MessageProps) => {
    const content = useMemo(() => {
        if (message.type === 'button') {
            return message.content;
        }

        if (message.type === 'file')
            if (message.content.includes('.pdf'))
                return (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                        <PdfIcon fill={'#EA3434'} size={21} />
                        <StyledLink href={message.contentUrl} target="_blank" rel="noreferrer">
                            <Interweave noWrap content={message.content} />
                        </StyledLink>
                    </div>
                );
            else return <Interweave noWrap content={message.content} />;

        if (
            message.type === 'text' ||
            message.type === 'template' ||
            message.type === 'text_template'
        ) {
            return <Interweave noWrap content={message.content} />;
        }

        if (message.type === 'audio') {
            return <MessageAudioPlayer message={message} />;
        }

        return <StyledErrorMessage>Unknown message type "{message.type}"</StyledErrorMessage>;
    }, [message]);

    return (
        <Root
            data-test-id={`whatsapp-message-${message.id}`}
            inbound={message.isInbound}
            id={`wa-message-${message.id}`}
        >
            {content}
        </Root>
    );
});

const Root = styled('div', {
    shouldForwardProp: (prop) => !['inbound'].includes(prop as string),
})<{ inbound: boolean }>(({ theme, inbound }) => ({
    paddingLeft: 14,
    paddingRight: 14,
    paddingTop: 8,
    paddingBottom: 8,
    backgroundColor: inbound ? '#F6F6F6' : '#EAFFD7',
    maxWidth: 413,
    borderRadius: 20,
    whiteSpace: 'break-spaces',
    color: 'var(--neutral9)',
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    overflowWrap: 'break-word',
}));

export default Message;

const StyledLink = styled('a')({
    '&:active': {
        color: '#0000ff',
    },
});

const StyledErrorMessage = styled('span')({
    fontWeight: 'bold',
});

const messagePlayingEvent = createEvent<number>();

function MessageAudioPlayer({ message }: { message: MessageDto }) {
    if (message.audio === null)
        return <StyledErrorMessage>Invalid audio message: no audio info</StyledErrorMessage>;

    if (!message.audio.url)
        return <StyledErrorMessage>Invalid audio message: no URL</StyledErrorMessage>;

    return (
        <OggPlayer
            messageId={message.id}
            url={message.audio.url}
            duration={message.audio.duration}
        />
    );
}

/**
 * Component displaying audio message in ogg format (uses compat layer if necessary) in the conversation.
 */
function OggPlayer({
    url,
    messageId,
    duration: expectedDuration,
}: {
    url: string;
    messageId: number;
    duration: string;
}) {
    const [isPlaying, setPlaying] = useState(false);
    const loadedRef = useRef(false);
    const element = useOggAudioElement(url);
    const {
        currentTime,
        setCurrentTime,
        loading: isLoading,
        duration,
        error,
    } = usePlayer({
        element,
        initiallyInLoadingState: true,
        playing: isPlaying,
        onPause: () => setPlaying(false),
        onPlayError: () => setPlaying(false),
    });

    const durationString = useMemo(() => {
        if (currentTime) {
            return formatTime(currentTime);
        }

        if (duration) {
            return formatTime(duration);
        }

        try {
            const ts = TimeSpan.fromString(expectedDuration);
            if (ts.totalSeconds > 0) {
                return formatTime(ts.totalSeconds);
            }
        } catch {
            // ignore
        }

        return '??:??';
    }, [expectedDuration, duration, currentTime]);

    useEffect(() => {
        if (!isPlaying) return;

        const unsubscribe = messagePlayingEvent.subscribe((id) => {
            if (id !== messageId && isPlaying) {
                setPlaying(false);
            }
        });
        return () => {
            unsubscribe();
        };
    }, [messageId, isPlaying]);

    const toggle = useCallback(() => {
        if (isPlaying) {
            setPlaying(false);
        } else {
            setPlaying(true);
            messagePlayingEvent.publish(messageId); // notify other messages that this one is playing audio and others must be paused
        }
    }, [isPlaying, messageId]);

    const onBecomeVisible = useCallback(
        (visible: boolean) => {
            if (visible) {
                if (!loadedRef.current) {
                    loadedRef.current = true;
                    element.load();
                }
            }
        },
        [element]
    );

    return (
        <MessageContainer onBecomeVisible={onBecomeVisible} data-test-class="ogg-player">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PlayButton
                    sx={{
                        color: error ? 'var(--danger) !important' : 'var(--cm1)',
                    }}
                    disableRipple
                    disabled={!!error}
                    onClick={toggle}
                >
                    {error ? (
                        <ErrorOutline />
                    ) : isPlaying ? (
                        <PauseIcon fill="currentColor" />
                    ) : (
                        <PlayIcon fill="currentColor" />
                    )}
                </PlayButton>
                <StyledSlider
                    isLoading={isLoading}
                    step={duration / 200}
                    disabled={Number.isNaN(duration) || duration === 0}
                    max={duration}
                    value={currentTime}
                    onChange={(_, v) => setCurrentTime(v as number)}
                />

                <StyledDuration>{durationString}</StyledDuration>
            </Box>
            {error && <StyledError>{error.message}</StyledError>}
        </MessageContainer>
    );
}

const StyledSlider = styled(Slider, {
    shouldForwardProp: (prop) => !['isLoading'].includes(prop as string),
})<{ isLoading: boolean }>(({ isLoading }) => ({
    width: 200,
    [`& .${sliderClasses.thumb}`]: {
        transition: 'box-shadow .2s',
        backgroundColor: 'white',
        boxShadow: 'none',
        border: '1px solid var(--cm1)',
        height: 16,
        width: 16,

        ':hover': {
            boxShadow: '0 0 0 1px var(--cm1)',
        },
    },
    [`& .${sliderClasses.track}`]: {
        transition: 'none !important',
        height: 9,
    },

    [`& .${sliderClasses.rail}`]: {
        backgroundColor: 'var(--neutral4)',
        opacity: 1,
        height: 9,
        overflow: 'hidden',

        '::after': {
            content: '""',
            // backgroundColor: 'var(--cm1)',
            height: '100%',
            width: '50%',
            backgroundImage: 'linear-gradient(45deg, transparent, var(--cm1), transparent)',
            display: 'none',
            opacity: 0.5,

            ...(isLoading
                ? {
                      animation: 'loading 1s infinite',
                      display: 'block',
                  }
                : {}),
        },
    },

    '@keyframes loading': {
        '0%': {
            transform: 'translateX(0%)',
        },

        '100%': {
            transform: 'translateX(200%)',
        },
    },
}));

const StyledDuration = styled('span')(({ theme }) => ({
    ...theme.typography.h7Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[9],
    display: 'inline-block',
    minWidth: 28,
}));

const StyledError = styled('div')(({ theme }) => ({
    paddingLeft: 8,
    color: theme.palette.error.main,
    fontWeight: 500,
}));

const PlayButton = styled(IconButton)(({ theme }) => ({
    padding: 4,

    ':hover': {
        backgroundColor: theme.palette.action.hover,
    },

    ':active': {
        backgroundColor: theme.palette.action.focus,
    },
}));

function useOggAudioElement(url: string) {
    const element = useMemo(() => {
        const audioElement = createOggAudioElement();
        audioElement.preload = 'none';
        audioElement.src = url;

        return audioElement;
    }, [url]);

    if (element.parentElement === null) {
        element.style.visibility = 'hidden';
        document.body.appendChild(element);
    }

    useEffect(
        () => () => {
            element.remove();
        },
        [element]
    );

    return element;
}

type MessageContainerProps = React.HTMLAttributes<HTMLDivElement> & {
    onBecomeVisible: (visible: boolean) => void;
};

function MessageContainer({ onBecomeVisible, ...props }: MessageContainerProps) {
    const [element, setElement] = useState<HTMLElement | null>(null);

    const onBecomeVisibleRef = useRef(onBecomeVisible);
    onBecomeVisibleRef.current = onBecomeVisible;

    useEffect(() => {
        if (!element) return;

        const parent = getScrollParent(element) ?? window;

        const handler = onVisibilityChange(element, (visible) => {
            onBecomeVisibleRef.current(visible);
        });

        parent.addEventListener('scroll', handler);
        handler();

        return () => {
            parent.removeEventListener('scroll', handler);
        };
    }, [element, onBecomeVisible]);

    return <div ref={setElement} {...props} />;
}

function getScrollParent(node: Node | null) {
    if (node == null) {
        return null;
    }

    if (!(node instanceof HTMLElement)) return null;

    if (node.scrollHeight - node.clientHeight > 10) {
        return node;
    } else {
        return getScrollParent(node.parentNode);
    }
}

function onVisibilityChange(el: HTMLElement, callback: (visible: boolean) => void) {
    let oldVisible: boolean | undefined = undefined;
    return function () {
        const visible = isElementInViewport(el);
        if (visible !== oldVisible) {
            oldVisible = visible;
            callback(visible);
        }
    };
}

function isElementInViewport(el: HTMLElement): boolean {
    const rect = el.getBoundingClientRect();
    const css = window.getComputedStyle(el, null);
    const headerHeight = parseFloat(css.getPropertyValue('--header-height'));

    return (
        rect.top - headerHeight > -1 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}
