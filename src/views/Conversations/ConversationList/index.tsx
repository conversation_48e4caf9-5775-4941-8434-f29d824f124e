import { styled } from '@mui/material';
import AreaSpinner from 'common/components/AreaSpinner';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Virtuoso } from 'react-virtuoso';
import { setNextPage } from 'store/slices/conversations';
import { conversationsLoading } from 'store/slices/conversations/selectors';
import fetchConversations from 'store/slices/conversations/thunks/fetchConversations';
import { Conversation, ConversationInput } from '../Conversation';

type ConversationListProps = {
    onSelectConversation: (conversationId: number | null) => void;
    selectedConversationId: number | null;
    conversations: ConversationInput[];
};

export const ConversationList = ({
    onSelectConversation,
    selectedConversationId,
    conversations,
}: ConversationListProps) => {
    const isLoading = useSelector(conversationsLoading);
    const dispatch = useDispatch();

    const orderedConversations = useMemo(() => {
        return conversations.slice().sort((a, b) => {
            const maxA = moment.max(moment(a.lastInboundMessage), moment(a.lastSentMessage));
            const maxB = moment.max(moment(b.lastInboundMessage), moment(b.lastSentMessage));
            return maxB.diff(maxA);
        });
    }, [conversations]);

    const endReached = useCallback(() => {
        dispatch(setNextPage());
        dispatch(fetchConversations());
    }, [dispatch]);

    if (isLoading) {
        return <AreaSpinner />;
    }

    return (
        <StyledVirtuoso
            data={orderedConversations}
            endReached={endReached}
            itemContent={(_index, conversation) => {
                return (
                    <Conversation
                        conversation={conversation}
                        key={conversation.conversationId}
                        isSelected={conversation.conversationId === selectedConversationId}
                        onSelect={() => onSelectConversation(conversation.conversationId)}
                    />
                );
            }}
        />
    );
};

const StyledVirtuoso = styled(Virtuoso)(({ theme }) => ({
    ...scrollbarStyle(),
    width: 420,
    minWidth: 420,
    borderRightStyle: 'solid',
    borderRightWidth: 1,
    borderRightColor: theme.palette.neutral[3],
    overflowY: 'auto',
})) as typeof Virtuoso;

export default ConversationList;
