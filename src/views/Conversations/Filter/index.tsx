import { styled } from '@mui/material';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/conversations';
import { selectFilter } from 'store/slices/conversations/selectors';
import fetchConversations from 'store/slices/conversations/thunks/fetchConversations';
import MutePreferencesButton from '../MutePreferencesButton';
import { FilterButton } from './FilterButton';
import { useStyles } from './css';

type FilterProps = {
    shouldResetFilter: boolean;
};

export const Filter = ({ shouldResetFilter }: FilterProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const conversationFilter = useAppSelector(selectFilter);
    const showFilters = useMemo(
        () => conversationFilter.showFilter,
        [conversationFilter.showFilter]
    );
    const setShowFilters = (value: boolean) => {
        dispatch(setFilters({ ...conversationFilter, showFilter: value }));
    };

    const onFilterChanged = useCallback(() => {
        dispatch(fetchConversations());
    }, [dispatch]);

    const onFilterChangedDebounced = useMemo(
        () => debounce(onFilterChanged, 250),
        [onFilterChanged]
    );

    useEffect(() => {
        onFilterChangedDebounced();
    }, [
        conversationFilter.active,
        conversationFilter.inbox,
        conversationFilter.searchTerm,
        onFilterChangedDebounced,
    ]);

    useEffect(() => {
        if (shouldResetFilter) {
            dispatch(
                setFilters({
                    inbox: 'both',
                    active: 'active',
                    showFilter: false,
                    searchTerm: '',
                })
            );
        }
    }, [dispatch, shouldResetFilter]);

    const bothValue = 'both';
    const advisorValue = 'advisor';
    const chatBotValue = 'chatBot';

    const trayOptions = useMemo<OptionData[]>(
        () => [
            { label: t('conversations.filter.bothInboxes'), value: bothValue },
            { label: t('conversations.filter.advisorInbox'), value: advisorValue },
            { label: t('conversations.filter.chatbotInbox'), value: chatBotValue },
        ],
        [t]
    );

    const trayFilter = useMemo(() => {
        const selected =
            trayOptions.find((opt) => opt.value === conversationFilter.inbox) || trayOptions[0];
        return selected;
    }, [trayOptions, conversationFilter]);

    const setTrayFilter = (value: OptionData) => {
        dispatch(setFilters({ ...conversationFilter, inbox: value.value }));
    };

    const activeValue = 'active';
    const closedValue = 'closed';

    const activeOptions = useMemo<OptionData[]>(
        () => [
            { label: t('conversations.filter.active'), value: activeValue },
            { label: t('conversations.filter.closed'), value: closedValue },
        ],
        [t]
    );

    const activeFilter = useMemo(() => {
        const selected =
            activeOptions.find((opt) => opt.value === conversationFilter.active) ||
            activeOptions[0];

        return selected;
    }, [activeOptions, conversationFilter]);

    const setActiveFilter = (value: OptionData) => {
        dispatch(setFilters({ ...conversationFilter, active: value.value }));
    };

    return (
        <Root>
            {showFilters && (
                <>
                    <Dropdown
                        name={'tray'}
                        slotProps={{
                            inputWrapper: {
                                className: styles.dropdown,
                            },
                        }}
                        cmosVariant="roundedPrimary"
                        options={trayOptions}
                        value={trayFilter}
                        onChange={(option) => option && setTrayFilter(option)}
                        isSearchable={false}
                    />
                    {trayFilter.value === advisorValue && (
                        <Dropdown
                            name={'active'}
                            cmosVariant="roundedPrimary"
                            slotProps={{
                                inputWrapper: {
                                    className: styles.dropdown,
                                },
                            }}
                            options={activeOptions}
                            value={activeFilter}
                            onChange={(option) => option && setActiveFilter(option)}
                            isSearchable={false}
                        />
                    )}
                    <HideFiltersButton onClick={() => setShowFilters(false)}>
                        {t('conversations.filter.hideFilters')}
                    </HideFiltersButton>
                </>
            )}
            {!showFilters && <FilterButton onClick={() => setShowFilters(true)} />}
            <MutePreferencesButton />
        </Root>
    );
};

const Root = styled('div')({
    marginLeft: 20,
    paddingLeft: 20,
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    gap: 20,

    '::before': {
        content: '""',
        display: 'block',
        width: 1,
        backgroundColor: 'var(--neutral4)',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
    },
});

const HideFiltersButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.primary.main,
    margin: '0 16px',
    cursor: 'pointer',
    fontWeight: 'normal',
}));

export default Filter;
