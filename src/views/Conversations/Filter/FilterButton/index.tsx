import { IconButton, IconButtonProps, styled, useTheme } from '@mui/material';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';

type FilterButtonProps = {
    onClick: IconButtonProps['onClick'];
};

const StyledIconButton = styled(IconButton)(({ theme }) => ({
    border: `1px solid ${theme.palette.primary.main}`,

    '&:hover': {
        backgroundColor: rgba(theme.palette.primary.main, 0.2),
    },
}));

export const FilterButton = ({ onClick }: FilterButtonProps) => {
    const { t } = useAppTranslation();
    const theme = useTheme();

    return (
        <>
            <ArrowTooltip content={t('conversations.filter.tooltip')}>
                <StyledIconButton onClick={onClick} size="small">
                    <FiltersIcon fill={theme.palette.primary.main} />
                </StyledIconButton>
            </ArrowTooltip>
        </>
    );
};
