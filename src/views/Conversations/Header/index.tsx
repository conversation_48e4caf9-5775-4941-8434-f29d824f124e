import { styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import PhoneCallAPI from 'api/PhoneCall';
import { default as ArrowTooltip, default as Tooltip } from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo, useState } from 'react';
import { getErrorResponse, hasCode } from 'services/Server';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { useCurrentUser } from 'store/slices/user';
import { Button } from '../../../common/components/Button';
import { PhoneIcon } from '../../../common/components/Icons/PhoneIcon';
import { Colors } from '../../../common/styles/Colors';
import { ConversationDto } from '../../../datacontracts/WhatsApp/ConversationDto';
import ConnectingCall from '../../Components/ConnectingCall';
import IsConversationMuted from '../IsConversationMuted';
import { getConsumerName } from '../logic';

type HeaderProps = {
    conversation: ConversationDto;
    isDetailsOpened: boolean;
    toggleDetails: () => void;
};

const Root = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 41,
    paddingTop: 20,
    paddingBottom: 20,
    paddingRight: 45,
    height: 52,
    width: '100%',
    boxSizing: 'border-box',
});

const Buttons = styled('div')({
    display: 'flex',
    columnGap: 16,
});

const CalloutButton = styled(Button)({
    maxWidth: 32,
});

const DetailsButton = styled(Button)({
    width: 180,
});

const Name = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: Colors.Grey5,
}));

export const Header = ({ conversation, isDetailsOpened, toggleDetails }: HeaderProps) => {
    const { t } = useAppTranslation();
    const [isConnectingCallOpened, setIsConnectingCallOpened] = useState(false);
    const { repairShopSettings } = useAppSelector(selectSettings);
    const toasters = useToasters();
    const user = useCurrentUser();
    const callMutation = useMutation(PhoneCallAPI.makeCall, {
        onMutate: () => {
            setIsConnectingCallOpened(true);
        },
        onError: () => {
            toasters.danger(
                t('conversations.errorMessages.failedToCall'),
                t('toasters.errorOccurred')
            );
        },
    });
    const errorMessage = useMemo(() => {
        const errorResponse = getErrorResponse(callMutation.error);

        if (errorResponse && hasCode(errorResponse, 'General.Users.PhoneNumberIsMissing')) {
            return t('conversations.errorMessages.phoneNumberIsMissing');
        }

        return t('conversations.errorMessages.failedToCall');
    }, [callMutation.error, t]);

    const callClickHandler = () => {
        callMutation.mutate({
            callTo: conversation.customerPhoneNumber,
            orderId: conversation.repairOrderId,
            customerId: conversation.customerId,
        });
    };

    const phoneCallSettings = useMemo(() => {
        if (repairShopSettings) {
            return {
                enablePhoneCalls: repairShopSettings.features.phoneCalls,
            };
        }
        return {
            enablePhoneCalls: false,
        };
    }, [repairShopSettings]);

    return (
        <>
            <Root>
                <div>
                    <Name>{getConsumerName(conversation)}</Name>
                    <IsConversationMuted conversationId={conversation.conversationId} />
                </div>
                <Buttons>
                    {phoneCallSettings.enablePhoneCalls && (
                        <ArrowTooltip
                            content={t('orders.preview.masterPhoneCallTooltip')}
                            disabled={user.role !== 'Master'}
                        >
                            <Tooltip
                                position="bottom"
                                content={t('orders.preview.phoneCallTooltip')}
                            >
                                <div>
                                    <CalloutButton
                                        cmosVariant={'stroke'}
                                        cmosSize={'medium'}
                                        iconPosition="right"
                                        color={Colors.CM1}
                                        Icon={PhoneIcon}
                                        onClick={callClickHandler}
                                        disabled={user.role === 'Master'}
                                    />
                                </div>
                            </Tooltip>
                        </ArrowTooltip>
                    )}
                    {conversation.customerId && (
                        <DetailsButton
                            cmosVariant={'stroke'}
                            cmosSize={'medium'}
                            color={Colors.CM1}
                            label={
                                isDetailsOpened
                                    ? t('conversations.hideCustomerDetail')
                                    : t('conversations.showCustomerDetail')
                            }
                            onClick={toggleDetails}
                        />
                    )}
                </Buttons>
            </Root>
            <ConnectingCall
                open={isConnectingCallOpened}
                isLoading={callMutation.isLoading}
                errorMessage={errorMessage}
                callFailed={callMutation.isError}
                onClose={() => setIsConnectingCallOpened(false)}
            />
        </>
    );
};

export default Header;
