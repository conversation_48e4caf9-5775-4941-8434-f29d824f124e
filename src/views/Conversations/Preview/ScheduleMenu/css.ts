import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';

export const useStyles = makeStyles((theme: Theme) => ({
    bottom: {
        backgroundColor: Colors.Neutral2,
        paddingLeft: 24,
        paddingRight: 24,
        paddingTop: 14,
        paddingBottom: 14,
    },
    scheduleList: {
        listStyleType: 'none',
        margin: 0,
        padding: '15px 19.5px',
        '& li': {
            cursor: 'pointer',
            ...theme.typography.h6Roboto,
            padding: 8,
            fontWeight: 'normal',
            color: theme.palette.neutral[7],
            borderBottom: `1px solid ${theme.palette.neutral[3]}`,
            '&:last-child': {
                border: 'none',
            },
        },
    },
    followUpForm: {
        borderTop: `1px solid ${theme.palette.neutral[2]}`,
        padding: '18px 25px',
        backgroundColor: '#FFFFFF',
    },
    followUpFormContainer: {
        position: 'absolute',
        bottom: 0,
        width: '100%',
        height: '100%',
        left: 0,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-end',
    },
    followUpFormBackdrop: {
        flexGrow: 1,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
    },
}));
