import { Collapse, styled } from '@mui/material';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';

export const CollapsibleEstimate = ({
    title,
    total,
    estimateQuantity,
    priority,
    isHide,
    children,
    padded = false,
}: {
    title: string;
    total: string;
    estimateQuantity: number;
    priority: string;
    isHide: boolean;
    children?: JSX.Element | string;
    padded?: boolean;
}) => {
    const [isCollapsed, setIsCollapsed] = useState(isHide);

    return (
        <DivRoot>
            <DivTitleSection
                onClick={() => {
                    setIsCollapsed(!isCollapsed);
                }}
            >
                <DivIconWrapper className={isCollapsed ? 'collapsed' : undefined}>
                    <UpIcon fill={Colors.CM1} size={IconSize.M} />
                </DivIconWrapper>
                <DivEstimateTitleLayout>
                    <div>
                        <span>{title}</span>&#160;(
                        <SpanEstimateTotalTitle>{total}</SpanEstimateTotalTitle>)
                    </div>
                    <DivPriority className={priority === 'Med' ? 'medEstimate' : 'urgentEstimate'}>
                        {estimateQuantity}
                    </DivPriority>
                </DivEstimateTitleLayout>
            </DivTitleSection>
            <Collapse timeout={200} in={isCollapsed}>
                <DivContentWrapper className={padded ? 'padded' : undefined}>
                    {children}
                </DivContentWrapper>
            </Collapse>
        </DivRoot>
    );
};

const SpanEstimateTotalTitle = styled('span')({
    color: '#467cfc',
});

const DivPriority = styled('div')({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 20,
    height: 20,
    margin: '0px 2px 0 2px!important',
    borderRadius: '50%',
    fontFamily: 'inter',
    fontSize: 10,
    fontWeight: 'bold',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: 1.2,
    letterSpacing: 'normal',
    textAlign: 'center',
    color: '#ffffff',
    '&.medEstimate': {
        backgroundColor: '#ffc626',
    },
    '&.urgentEstimate': {
        backgroundColor: '#f15857',
    },
});

const DivEstimateTitleLayout = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr auto',
    flex: 1,
    alignItems: 'center',
});

export const CollapsibleSection = ({
    title,
    isCollapse = false,
    children,
}: {
    title: React.ReactNode;
    isCollapse?: boolean;
    children?: React.ReactNode;
}) => {
    const [isCollapsed, setIsCollapsed] = useState(isCollapse);
    return (
        <DivRoot>
            <DivTitleSection role="button" onClick={() => setIsCollapsed(!isCollapsed)}>
                <DivIconWrapper className={isCollapsed ? 'collapsed' : undefined}>
                    <UpIcon fill={Colors.CM1} size={IconSize.M} />
                </DivIconWrapper>
                {title}
            </DivTitleSection>
            <Collapse timeout={200} in={isCollapsed}>
                <DivContentWrapper>{children}</DivContentWrapper>
            </Collapse>
        </DivRoot>
    );
};

const DivRoot = styled('div')(({ theme }) => ({
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,

    ':last-child': {
        borderBottom: 'none',
    },
}));

const DivTitleSection = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    padding: '18px 24px',
    textAlign: 'left',
    cursor: 'pointer',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],

    ':hover': {
        color: theme.palette.neutral[9],
    },

    ':active': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

const DivIconWrapper = styled('div')({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 24,
    height: 24,
    marginRight: 10,
    transition: 'transform .2s',

    '&.collapsed': {
        transform: 'rotate(180deg)',
    },
});

const DivContentWrapper = styled('div')({
    padding: '8px 24px',
});
