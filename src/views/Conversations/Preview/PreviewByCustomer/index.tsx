import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import styled from '@mui/styles/styled';
import { useQuery } from '@tanstack/react-query';
import PhoneCallAPI from 'api/PhoneCall';
import CustomersApi, { CustomerPreviewDto } from 'api/customers';
import { getErrorMessage } from 'api/error';
import AreaSpinner from 'common/components/AreaSpinner';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Colors } from 'common/styles/Colors';
import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import { useEffect, useState } from 'react';
import { SimpleErrorDisplay2 } from 'utils/errorsHandling/SimpleErrorBoundary2';
import { getCustomerName } from '../../logic';
import CustomerDetails from '../CustomerDetails';
import { ScheduleMenu } from '../ScheduleMenu';

type PreviewByCustomerProps = {
    enabled: boolean;
    conversation: ConversationDto;
    onClose: () => void;
};

export const PreviewByCustomer = ({ conversation, onClose, enabled }: PreviewByCustomerProps) => {
    const [customerData, setCustomerData] = useState<CustomerPreviewDto | null>(null);

    const {
        data: customerDataFromBackend,
        isFetching,
        error,
        refetch,
    } = useQuery({
        enabled,
        queryKey: ['customers', 'details', conversation.customerId],
        queryFn: () => {
            return CustomersApi.getCustomerPreview(conversation.customerId || '');
        },
    });

    useEffect(() => {
        if (!customerDataFromBackend) return;
        setCustomerData(customerDataFromBackend);
    }, [customerDataFromBackend]);

    const { data: phoneCallsLog } = useQuery(
        ['phone-calls', 'log', conversation.customerId],
        () => {
            return PhoneCallAPI.getLog(conversation.customerId || '');
        }
    );

    if (isFetching) {
        return (
            <StyledPreview>
                <StyledSpinner />
            </StyledPreview>
        );
    }

    if (!customerData || error) {
        const errorMessage = getErrorMessage(error);

        return (
            <StyledPreview>
                <SimpleErrorDisplay2 message={errorMessage} onRetry={refetch} />
            </StyledPreview>
        );
    }

    return (
        <StyledPreview>
            <StyledFirstHeader>
                <StyledHeaderContainer>
                    <StyledHeaderContent>
                        <StyledCustomerName>{getCustomerName(customerData)} </StyledCustomerName>
                    </StyledHeaderContent>
                    <div>
                        <IconButton size="small" onClick={onClose}>
                            <CloseIcon fill={Colors.White} />
                        </IconButton>
                    </div>
                </StyledHeaderContainer>
            </StyledFirstHeader>
            <StyledScrollable>
                <CustomerDetails customer={customerData} phoneCallsLog={phoneCallsLog || null} />
            </StyledScrollable>

            <StyledBottom>
                <Grid container spacing={1}>
                    <Grid item xs={12}>
                        <ScheduleMenu
                            customer={
                                customerData
                                    ? {
                                          customerId: customerData.customerId,
                                          firstName: customerData.firstName,
                                          lastName: customerData.lastName,
                                          mobile: customerData.mobile,
                                          landline: customerData.landline,
                                          taxIdentification: customerData.taxIdentification,
                                          email: customerData.email,
                                          businessName: customerData.businessName,
                                      }
                                    : undefined
                            }
                            vehicle={
                                customerData?.vehicle
                                    ? {
                                          vehicleId: customerData.vehicle.vehicleId,
                                          brand: customerData.vehicle.brand,
                                          model: customerData.vehicle.model,
                                          plates: customerData.vehicle.plates,
                                          vin: customerData.vehicle.vin,
                                          year: customerData.vehicle.year,
                                          color: customerData.vehicle.color,
                                      }
                                    : undefined
                            }
                            hasFollowUp={customerData.maintenanceInfo?.hasFollowUp}
                            onFollowUpSaved={(followUpDate) => {
                                setCustomerData({
                                    ...customerData,
                                    maintenanceInfo: customerData.maintenanceInfo
                                        ? {
                                              ...customerData.maintenanceInfo,
                                              hasFollowUp: true,
                                              nextServiceDate: followUpDate.toString(),
                                          }
                                        : null,
                                });
                            }}
                        />
                    </Grid>
                </Grid>
            </StyledBottom>
        </StyledPreview>
    );
};

const StyledPreview = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '0px 4px 16px 0px rgba(130, 130, 130, 0.25)',
    backgroundColor: theme.palette.neutral[1],
    width: 430,
    zIndex: 1,
}));

const StyledSpinner = styled(AreaSpinner)({
    paddingBottom: 56,
});

const StyledFirstHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    backgroundColor: theme.palette.neutral[8],
    paddingLeft: 24,
    paddingRight: 16,
    paddingTop: 10,
    paddingBottom: 8,
}));

const StyledHeaderContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
});
const StyledHeaderContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    rowGap: 5,
});
const StyledCustomerName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    fontSize: 18,
    lineHeight: '22px',
}));

const StyledScrollable = styled('div')({
    overflowY: 'auto',
    height: 0,
    flexGrow: 1,
});

const StyledBottom = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[2],
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 14,
    paddingBottom: 14,
}));
