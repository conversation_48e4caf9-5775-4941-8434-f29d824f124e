import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import { styled } from '@mui/material/styles';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import {
    IPreviewItem,
    IRepairOrderPreviewResponse,
} from 'datacontracts/Order/IOrderPreviewResponse';
import moment from 'moment';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { CollapsibleEstimate, CollapsibleSection } from '../Collapsible';

type OrderPreviewDetailsProps = {
    order: IRepairOrderPreviewResponse | undefined;
    twoColumnCustomerInformation?: boolean;
};

const OrderDetails = ({ order }: OrderPreviewDetailsProps) => {
    const { t } = useAppTranslation();
    const { internationalization } = useSelector(selectSettings);

    const getTotalEstimateWithSubitems = (items: IPreviewItem[] | undefined) => {
        return (
            items?.reduce((acc, current) => {
                const subitemsTotal =
                    current.subitems.reduce((acc, current) => acc + (current.totalCost ?? 0), 0) ??
                    0;
                return acc + (current.totalCost ?? 0) + subitemsTotal;
            }, 0) ?? 0
        );
    };

    const urgentEstimatesTotal = getTotalEstimateWithSubitems(order?.redItems);
    const medEstimatesTotal = getTotalEstimateWithSubitems(order?.yellowItems);

    const vehicleInformation = useMemo(() => {
        const brandModel = [order?.make, order?.model].filter((value) => Boolean(value)).join(' ');
        const vehicleInformation = [brandModel, order?.year]
            .filter((value) => Boolean(value))
            .join(', ');
        return vehicleInformation || '--';
    }, [order]);

    const ticketAverage = useMemo(() => {
        const redItems =
            order?.redItems.reduce((prev, current) => prev + (current.totalCost ?? 0), 0) ?? 0;
        const yellowItems =
            order?.yellowItems.reduce((prev, current) => prev + (current.totalCost ?? 0), 0) ?? 0;

        return (
            (redItems + yellowItems) /
            ((order?.redItems.length || 0) + (order?.yellowItems.length || 0))
        );
    }, [order]);

    return (
        <>
            <CollapsibleSection title={t('orders.preview.customerInformation')} isCollapse>
                <Grid container spacing={1} direction="row" justifyContent="center">
                    <Grid item xs={6}>
                        <Grid container direction="column" spacing={1}>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <CustomerInfoLabel>
                                        {t('commonLabels.mobile')}
                                    </CustomerInfoLabel>
                                </Grid>
                                <Grid item>
                                    <CustomerInfoValue>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            order?.mobilePhone || null
                                        )}
                                    </CustomerInfoValue>
                                </Grid>
                            </Grid>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <CustomerInfoLabel>{t('commonLabels.email')}</CustomerInfoLabel>
                                </Grid>
                                <Grid item>
                                    <CustomerInfoValue>{order?.email || '--'}</CustomerInfoValue>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={6}>
                        <Grid container direction="column" spacing={1}>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <CustomerInfoLabel>{t('commonLabels.phone')}</CustomerInfoLabel>
                                </Grid>
                                <Grid item>
                                    <CustomerInfoValue>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            order?.landlinePhone || null
                                        )}
                                    </CustomerInfoValue>
                                </Grid>
                            </Grid>
                            <Grid container item>
                                <Grid item xs={5}>
                                    <CustomerInfoLabel>
                                        {t('commonLabels.businessName')}
                                    </CustomerInfoLabel>
                                </Grid>
                                <Grid item style={{ marginLeft: 5 }}>
                                    <CustomerInfoValue>
                                        {order?.businessName || '--'}
                                    </CustomerInfoValue>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.vehicleInformation')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <CustomerInfoLabel>{t('commonLabels.vehicle')}</CustomerInfoLabel>
                            </Grid>
                            <Grid item>
                                <CustomerInfoValue>{vehicleInformation}</CustomerInfoValue>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <CustomerInfoLabel>{t('commonLabels.vin')}</CustomerInfoLabel>
                            </Grid>
                            <Grid item>
                                <CustomerInfoValue>{order?.vin || '--'}</CustomerInfoValue>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                        <CustomerInfoLabel>
                                            {t('commonLabels.plates')}
                                        </CustomerInfoLabel>
                                    </Grid>
                                    <Grid item xs={6}>
                                        <CustomerInfoValue>
                                            {order?.plates || '--'}
                                        </CustomerInfoValue>
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={6}>
                                <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                        <CustomerInfoLabel>
                                            {t('commonLabels.averageTicket')}
                                        </CustomerInfoLabel>
                                    </Grid>
                                    <Grid item xs={6}>
                                        <CustomerInfoValue>
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                ticketAverage
                                            )}
                                        </CustomerInfoValue>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection
                title={
                    <div style={{ display: 'flex' }}>
                        <Title>{t('orders.preview.inspectionItems')}</Title>-
                        <TitleLink href={order?.inspectionLink || '#'} style={{ marginLeft: 5 }}>
                            {order?.inspectionLink || '--'}
                        </TitleLink>
                    </div>
                }
            >
                <CollapsibleEstimate
                    padded
                    title={`${t('orders.preview.businessOpportunity')} - ${t(
                        'commonLabels.priorities.urgent'
                    )}`}
                    total={InternationalizationLogic.numberToCurrency(
                        internationalization,
                        urgentEstimatesTotal
                    )}
                    estimateQuantity={order?.redItems ? order?.redItems.length : 0}
                    priority={'Urgent'}
                    isHide={false}
                >
                    {order?.redItems && <BusinessOpportunityList items={order.redItems} />}
                </CollapsibleEstimate>

                <CollapsibleEstimate
                    padded
                    title={`${t('orders.preview.businessOpportunity')} - ${t(
                        'commonLabels.priorities.suggested'
                    )}`}
                    total={InternationalizationLogic.numberToCurrency(
                        internationalization,
                        medEstimatesTotal
                    )}
                    estimateQuantity={order?.yellowItems ? order?.yellowItems.length : 0}
                    priority={'Med'}
                    isHide={true}
                >
                    {order?.yellowItems && <BusinessOpportunityList items={order.yellowItems} />}
                </CollapsibleEstimate>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.maintenanceInformation')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <CustomerInfoLabel>
                                    {t(
                                        `commonLabels.${
                                            order?.hasFollowUp ? 'followUpDate' : 'nextServiceDate'
                                        }`
                                    )}
                                </CustomerInfoLabel>
                            </Grid>
                            <Grid item>
                                <CustomerInfoValue>
                                    {order?.nextServiceDate
                                        ? moment(new Date(order.nextServiceDate)).format(
                                              t('dateFormats.longDate')
                                          )
                                        : '--'}
                                </CustomerInfoValue>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <CustomerInfoLabel>
                                    {t('commonLabels.calculatedMileage')}
                                </CustomerInfoLabel>
                            </Grid>
                            <Grid item>
                                <CustomerInfoValue>
                                    {order?.calculatedMileage || '--'}
                                </CustomerInfoValue>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <CustomerInfoLabel>
                                    {t('commonLabels.serviceAdvisor')}
                                </CustomerInfoLabel>
                            </Grid>
                            <Grid item>
                                <CustomerInfoValue>
                                    {order?.serviceAdvisor?.name || '--'}{' '}
                                </CustomerInfoValue>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>
        </>
    );
};

const BusinessOpportunityList = ({ items }: { items: IPreviewItem[] }) => {
    const { internationalization } = useSelector(selectSettings);
    return (
        <div>
            {items.map((estimate, index) => (
                <div key={index}>
                    <EstimateRow>
                        <span>- {estimate.repairName}</span>
                        <span>
                            {InternationalizationLogic.numberToCurrency(
                                internationalization,
                                estimate.totalCost
                            )}
                        </span>
                    </EstimateRow>
                    {estimate.subitems.length > 0
                        ? estimate.subitems.map((subItem, index) => (
                              <SubItemRow key={index}>
                                  <span>* {subItem.name}</span>
                                  <span>
                                      {InternationalizationLogic.numberToCurrency(
                                          internationalization,
                                          subItem.totalCost
                                      )}
                                  </span>
                              </SubItemRow>
                          ))
                        : null}

                    {index + 1 !== items.length ? <Divider /> : null}
                </div>
            ))}
        </div>
    );
};

const CustomerInfoLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--greyBlue)',
    textAlign: 'left',
}));

const CustomerInfoValue = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    width: 0,
    flexGrow: 1,
    display: 'flex',
    alignItems: 'flex-end',
    textAlign: 'left',
    '& > span': {
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
    },
}));

const EstimateRow = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 5,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
}));

const SubItemRow = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '3px 5px 5px 5px',
    ...theme.typography.h8Inter,
    color: theme.palette.neutral[6],
}));

const Title = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--greyBlue)',
    marginRight: 5,
}));

const TitleLink = styled(Link)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    color: theme.palette.primary.light,
}));

export default OrderDetails;
