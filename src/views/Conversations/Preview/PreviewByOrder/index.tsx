import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton/IconButton';
import { useQuery } from '@tanstack/react-query';
import CustomersApi, { CustomerDetailsDto, VehicleListItemDto } from 'api/customers';
import { getErrorMessage } from 'api/error';
import OrderAPI from 'api/Order';
import { isAxiosError } from 'axios';
import AreaSpinner from 'common/components/AreaSpinner';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IRepairOrderPreviewResponse } from 'datacontracts/Order/IOrderPreviewResponse';
import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import moment from 'moment';
import { useState } from 'react';
import { generatePath, useNavigate } from 'react-router-dom';
import { SimpleErrorDisplay2 } from 'utils/errorsHandling/SimpleErrorBoundary2';
import { getConsumerName } from '../../logic';
import PreviewDetail from '../OrderDetails';
import { ScheduleMenu } from '../ScheduleMenu';

type PreviewByOrderProps = {
    conversation: ConversationDto;
    onClose: () => void;
    enabled: boolean;
};

const StyledPreview = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '0px 4px 16px 0px rgba(130, 130, 130, 0.25)',
    backgroundColor: theme.palette.neutral[1],
    width: 430,
    zIndex: 1,
}));

const StyledSpinner = styled(AreaSpinner)({
    paddingBottom: 56,
});

const StyledFirstHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    backgroundColor: theme.palette.neutral[8],
    paddingLeft: 24,
    paddingRight: 16,
    paddingTop: 10,
    paddingBottom: 8,
}));

const StyledHeaderContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
});
const StyledHeaderContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    rowGap: 5,
});
const StyledCustomerName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    fontSize: 18,
    lineHeight: '22px',
}));
const StyledOrderDate = styled('span')(({ theme }) => ({
    ...theme.typography.h7Roboto,
    color: theme.palette.neutral[1],
}));

const StyledScrollable = styled('div')({
    overflowY: 'auto',
    height: 0,
    flexGrow: 1,
});

const StyledBottom = styled('div')({
    backgroundColor: Colors.Neutral2,
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 14,
    paddingBottom: 14,
});

export const PreviewByOrder = ({ conversation, onClose, enabled }: PreviewByOrderProps) => {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const [data, setData] = useState<IRepairOrderPreviewResponse | null>(null);
    const [customerDetails, setCustomerDetails] = useState<CustomerDetailsDto | null>(null);
    const [vehicleDetails, setVehicleDetails] = useState<VehicleListItemDto | null>(null);

    const seeOrderDetails = async () => {
        if (data) {
            navigate(generatePath(ROUTES.ORDERS_DETAIL, { id: data.repairOrderId + '' }));
        }
    };

    const { isLoading, error, refetch } = useQuery({
        queryKey: ['customers', 'details', conversation.repairOrderId],
        queryFn: async () => {
            if (!conversation.repairOrderId)
                throw new Error('conversation does not have order key is not set');
            const orderId = await OrderAPI.getIdByKey(conversation.repairOrderId);
            const orderPreviewData = await OrderAPI.preview(orderId);

            let customer: CustomerDetailsDto | null = null;

            try {
                customer = await CustomersApi.details(
                    orderPreviewData.customerId
                        ? { customerId: orderPreviewData.customerId }
                        : {
                              firstName: orderPreviewData.lastName || '',
                              lastName: orderPreviewData.lastName || '',
                          }
                );
            } catch (error: unknown) {
                if (!(isAxiosError(error) && error.response?.status === 404)) {
                    // non 404 error is not handled here
                    throw error;
                }

                // 404 error is not a problem, we can continue
            }
            return { customer, orderPreviewData };
        },
        cacheTime: 0,
        staleTime: 0,
        enabled: !!conversation.repairOrderId && enabled,
        onSuccess: ({ customer, orderPreviewData }): void => {
            setCustomerDetails(customer);
            setData(orderPreviewData);

            if (customer && customer.linkedVehicles && customer.linkedVehicles.length) {
                setVehicleDetails(
                    customer.linkedVehicles.find(
                        (v) =>
                            v.plates === orderPreviewData.plates &&
                            v.brand === orderPreviewData.make &&
                            v.year ===
                                `${orderPreviewData.year !== null ? orderPreviewData.year : ''}` &&
                            v.model === orderPreviewData.model &&
                            v.vin === orderPreviewData.vin
                    ) ?? null
                );
            }
        },
    });

    if (isLoading) {
        return (
            <StyledPreview>
                <StyledSpinner />
            </StyledPreview>
        );
    }

    if (!data || error) {
        const errorMessage = getErrorMessage(error);

        return (
            <StyledPreview>
                <SimpleErrorDisplay2 message={errorMessage} onRetry={refetch} />
            </StyledPreview>
        );
    }

    return (
        <StyledPreview>
            <StyledFirstHeader>
                <StyledHeaderContainer>
                    <StyledHeaderContent>
                        <StyledCustomerName>{`${getConsumerName(conversation)} - ${t(
                            'conversations.orderNumber',
                            {
                                number: data.repairOrderNumber,
                            }
                        )}`}</StyledCustomerName>
                        <StyledOrderDate>
                            {moment(data.uploadTime).format(t('dateFormats.longDate'))}
                        </StyledOrderDate>
                    </StyledHeaderContent>
                    <div>
                        <IconButton size="small" onClick={onClose}>
                            <CloseIcon fill={Colors.White} />
                        </IconButton>
                    </div>
                </StyledHeaderContainer>
            </StyledFirstHeader>
            <StyledScrollable>
                <PreviewDetail order={data} twoColumnCustomerInformation />
            </StyledScrollable>

            <StyledBottom>
                <Grid container spacing={1}>
                    <Grid item xs={6}>
                        <Button
                            blockMode
                            cmosVariant={'stroke'}
                            color={Colors.CM1}
                            label={t('orders.preview.seeOrderDetail')}
                            onClick={() => seeOrderDetails()}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <ScheduleMenu
                            customer={
                                customerDetails
                                    ? {
                                          customerId: customerDetails.customerId,
                                          firstName: customerDetails.firstName,
                                          lastName: customerDetails.lastName,
                                          mobile: customerDetails.mobile,
                                          landline: customerDetails.landline,
                                          taxIdentification: customerDetails.taxIdentification,
                                          email: customerDetails.email,
                                          businessName: customerDetails.businessName,
                                      }
                                    : undefined
                            }
                            vehicle={
                                vehicleDetails
                                    ? {
                                          vehicleId: vehicleDetails.id,
                                          brand: vehicleDetails.brand,
                                          model: vehicleDetails.model,
                                          plates: vehicleDetails.plates,
                                          vin: vehicleDetails.vin,
                                          year: vehicleDetails.year,
                                          color: vehicleDetails.color,
                                      }
                                    : undefined
                            }
                            hasFollowUp={data.hasFollowUp}
                            onFollowUpSaved={(followUpDate) => {
                                setData({
                                    ...data,
                                    hasFollowUp: true,
                                    nextServiceDate: followUpDate,
                                });
                            }}
                        />
                    </Grid>
                </Grid>
            </StyledBottom>
        </StyledPreview>
    );
};
