import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import { PreviewByCustomer } from './PreviewByCustomer';
import { PreviewByOrder } from './PreviewByOrder';

type PreviewProps = {
    enabled: boolean;
    conversation: ConversationDto;
    onClose: () => void;
};

export const Preview = ({ conversation, onClose, enabled }: PreviewProps) => {
    return (
        <>
            {conversation.repairOrderId ? (
                <PreviewByOrder enabled={enabled} conversation={conversation} onClose={onClose} />
            ) : conversation.customerId ? (
                <PreviewByCustomer
                    enabled={enabled}
                    conversation={conversation}
                    onClose={onClose}
                />
            ) : undefined}
        </>
    );
};

export default Preview;
