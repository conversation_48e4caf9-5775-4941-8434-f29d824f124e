import { styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { ButtonProps } from 'common/components/Button/ButtonProps';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';

export type SendButtonProps = Pick<ButtonProps, 'onClick' | 'showLoader' | 'disabled'>;

const StyledButton = styled(Button)(({ theme }) => ({
    height: '40px !important',
    width: '110px !important',
    fontSize: '16px !important',
}));

export default function SendButton(props: SendButtonProps) {
    const { t } = useAppTranslation();

    return (
        <StyledButton
            cmosVariant="filled"
            color={Colors.Success}
            label={t('conversations.send')}
            {...props}
        />
    );
}
