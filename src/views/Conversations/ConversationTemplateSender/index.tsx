import { ExpandMore } from '@mui/icons-material';
import { Box, Card, Popover, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { EnterpriseWhatsappApi } from 'api/enterprise';
import { getErrorMessage } from 'api/error';
import WhatsAppApi from 'api/whatsapp';
import { Button } from 'common/components/Button';
import { InspectionIcon } from 'common/components/Icons/InspectionIcon';
import { SMenuItem } from 'common/components/mui';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { useRef, useState } from 'react';

export type PredefinedTemplateData = {
    preview: string;
    templateName: string;
};

export default function ConversationTemplateSender({
    hasAppointmentId,
    conversationId,
    onTemplateSelected,
    shopId,
    title,
}: {
    hasAppointmentId: boolean;
    conversationId: number;
    onTemplateSelected: (data: PredefinedTemplateData) => void;
    shopId: string | null;
    title: string;
}) {
    const toasters = useToasters();
    const isEnterprise = useIsEnterpriseRoute();
    const { t } = useAppTranslation();
    const buttonRef = useRef<HTMLButtonElement | null>(null);
    const [showPopover, setShowPopover] = useState(false);

    const { data: templatePreviews } = useQuery({
        queryKey: ['whats-app-predefined-templates'],
        queryFn: () => {
            try {
                if (isEnterprise) {
                    if (!shopId) throw new Error('shopId is null');
                    return EnterpriseWhatsappApi.getPredefinedTemplate(conversationId, shopId);
                } else {
                    return WhatsAppApi.getPredefinedTemplate(conversationId);
                }
            } catch (e: unknown) {
                toasters.danger(
                    getErrorMessage(e),
                    t('toasters.errorOccurred') + ': failed to get preview of predefined templates'
                );
                throw e;
            }
        },
    });

    function sendPredefinedTemplate(templateName: string) {
        onTemplateSelected({
            preview: templatePreviews?.previews[templateName] ?? '',
            templateName,
        });
    }

    return (
        <DivRoot>
            <PDescription>{title}</PDescription>
            <Button w={420} ref={buttonRef} onClick={() => setShowPopover(true)}>
                <ExpandMore />
                {t('conversations.sendWhatsAppTemplate')}
            </Button>
            <Popover
                anchorEl={buttonRef.current}
                open={showPopover}
                onClose={() => setShowPopover(false)}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
                transformOrigin={{ vertical: 40 * 4 + 8, horizontal: 'center' }}
                transitionDuration={150}
            >
                <Card sx={{ width: 420 }}>
                    <Box
                        sx={{
                            color: 'neutral.8',
                            height: 40,
                            backgroundColor: 'neutral.3',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '6px',
                            p: 2,
                            fontWeight: 'bold',
                        }}
                    >
                        <InspectionIcon fill="currentColor" />
                        {t('conversations.whatsAppTemplates')}
                    </Box>
                    <TemplatePreview
                        text={
                            templatePreviews
                                ? templatePreviews.previews['cm_followup_after_giving_an_estimate']
                                : undefined
                        }
                    >
                        <StyledItem
                            onClick={() =>
                                sendPredefinedTemplate('cm_followup_after_giving_an_estimate')
                            }
                        >
                            {t('conversations.closedWindowTemplates.followUpAfterEstimate')}
                        </StyledItem>
                    </TemplatePreview>
                    <TemplatePreview
                        text={
                            templatePreviews
                                ? templatePreviews.previews[
                                      'cm_followup_after_appointment_no_show'
                                  ] ??
                                  templatePreviews.previews[
                                      'cm_followup_after_appointment_no_show_no_plates'
                                  ]
                                : undefined
                        }
                    >
                        <StyledItem
                            disabled={!hasAppointmentId}
                            onClick={() =>
                                sendPredefinedTemplate(
                                    templatePreviews?.previews[
                                        'cm_followup_after_appointment_no_show'
                                    ]
                                        ? 'cm_followup_after_appointment_no_show'
                                        : 'cm_followup_after_appointment_no_show_no_plates'
                                )
                            }
                        >
                            {t(
                                'conversations.closedWindowTemplates.followUpAfterAppointmentNoShow'
                            )}
                        </StyledItem>
                    </TemplatePreview>

                    <TemplatePreview
                        text={
                            templatePreviews
                                ? templatePreviews.previews[
                                      'cm_followup_for_not_answering_a_customer_question'
                                  ]
                                : undefined
                        }
                    >
                        <StyledItem
                            onClick={() =>
                                sendPredefinedTemplate(
                                    'cm_followup_for_not_answering_a_customer_question'
                                )
                            }
                        >
                            {t('conversations.closedWindowTemplates.followUpNotAnsweringQuestion')}
                        </StyledItem>
                    </TemplatePreview>
                </Card>
            </Popover>
        </DivRoot>
    );
}

const StyledItem = styled(SMenuItem)({
    height: 40,

    ':hover': {
        fontWeight: '500',
        color: 'var(--cm1)',
    },

    '&:not(:last-child)': {
        borderBottom: '1px solid var(--neutral3)',
    },
});

const DivRoot = styled('div')({
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
});

const PDescription = styled('p')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[6],
    textAlign: 'center',
}));

function TemplatePreview({ text, children }: { text?: string; children: JSX.Element }) {
    return (
        <ArrowTooltip
            content={text ? <DivTemplatePreview>{text}</DivTemplatePreview> : undefined}
            disabled={!text}
            position="top-end"
            arrow={false}
            enterDelay={500}
            disableInteractive
        >
            {children}
        </ArrowTooltip>
    );
}

const DivTemplatePreview = styled('div')(({ theme }) => ({
    whiteSpace: 'pre-wrap',
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));
