import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { getErrorMessage } from 'api/error';
import WhatsAppApi from 'api/whatsapp';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';

const Root = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    display: 'inline-block',
    fontWeight: 'normal',
}));

const ErrorText = styled(Root)(({ theme }) => ({
    color: theme.palette.error.main,
    cursor: 'pointer',
}));

type IsConversationMutedProps = {
    conversationId: number;
};

export default function IsConversationMuted({ conversationId }: IsConversationMutedProps) {
    const { t } = useAppTranslation();
    const { data, isError, error, isLoading, refetch } = useQuery({
        queryKey: ['conversation', conversationId, 'state'],
        queryFn: () => WhatsAppApi.getConversationMuteState(conversationId),
    });

    if (isError) {
        const errorMessage = getErrorMessage(error);
        return (
            <ArrowTooltip content={t('conversations.muteState.errorTooltip')}>
                <ErrorText role="button" onClick={() => refetch()}>
                    Error: {errorMessage}
                </ErrorText>
            </ArrowTooltip>
        );
    }

    if (!data) return null;
    if (!data.isMuted) return null;

    const dateFmt = t('conversations.muteState.dateFmt');
    let when: string;
    if (data.mutedUntil === null) {
        when = t('conversations.muteState.always');
    } else {
        when = t('conversations.muteState.until', {
            value: DateTime.fromISO(data.mutedUntil).toFormat(dateFmt),
        });
    }

    return (
        <Root>
            {t('conversations.muteState.template', {
                when,
            })}
        </Root>
    );
}
