import { ButtonProps, IconButton, styled } from '@mui/material';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { InspectionIcon } from 'common/components/Icons/InspectionIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { Interweave } from 'interweave';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { forwardRef } from 'react';

export type MessageAreaProps = {
    textValue: string;
    onTextValueChange: (value: string) => void;

    onTemplateModeToggled?: (enabled: boolean) => void;
    templateModeEnabled?: boolean;
    templateContent?: string;
};

const StyledRoot = styled('div')(({ theme }) => ({
    minHeight: 90,
    maxHeight: 250,
    width: '100%',
    boxSizing: 'border-box',
    borderRadius: 5,
    borderWidth: 1,
    borderColor: theme.palette.neutral[3],
    borderStyle: 'solid',
    display: 'flex',
    overflow: 'auto',
    position: 'relative',
    ...scrollbarStyle(),

    '&.restrictions': {
        backgroundColor: theme.palette.neutral[2],
    },

    ':has(textarea:focus)': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 1px ${theme.palette.primary.main} inset`,
    },
}));

export const Scroller = styled(OverlayScrollbarsComponent)({
    paddingTop: 14,
    paddingRight: 9,
    paddingBottom: 14,
    paddingLeft: 16,
    width: '100%',

    '& .os-scrollbar-vertical': {
        marginRight: 2,
        marginTop: 4,
        marginBottom: 4,
    },
});
Scroller.displayName = 'MessageArea.Scroller';

export const Root = forwardRef(
    (
        { children, ...props }: React.HTMLAttributes<HTMLDivElement>,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        return (
            <StyledRoot ref={ref} {...props}>
                {children}
            </StyledRoot>
        );
    }
);
Root.displayName = 'MessageArea.Root';

const StyledMessageAreaUseTemplateButton = styled(Button)({
    width: 116,
    position: 'absolute',
    top: 4,
    right: 16,
    height: '32px !important',

    '&:not(.isActive)': {
        color: 'var(--neutral6)',
    },

    '&:disabled': {
        opacity: 0.3,
    },
});

export type MessageAreaUseTemplateButtonProps = Pick<ButtonProps, 'disabled' | 'onClick'> & {
    isActive: boolean;
};

const UseTemplateButton = forwardRef(
    (
        { onClick, disabled, isActive }: MessageAreaUseTemplateButtonProps,
        ref: React.ForwardedRef<HTMLButtonElement>
    ) => {
        const { t } = useAppTranslation();

        return (
            <StyledMessageAreaUseTemplateButton
                ref={ref}
                cmosVariant={'typography'}
                cmosSize={'small'}
                onClick={onClick}
                disabled={disabled}
                className={clsx({ isActive: isActive })}
            >
                <InspectionIcon fill="currentColor" />
                {t('conversations.useTemplate')}
            </StyledMessageAreaUseTemplateButton>
        );
    }
);
UseTemplateButton.displayName = 'MessageArea.UseTemplateButton';

export type MessageAreaCloseButtonProps = Pick<ButtonProps, 'disabled' | 'onClick'>;

const CloseButton = forwardRef(
    (
        { onClick, disabled }: MessageAreaCloseButtonProps,
        ref: React.ForwardedRef<HTMLButtonElement>
    ) => {
        return (
            <IconButton
                sx={{ position: 'absolute', right: 16 }}
                ref={ref}
                onClick={onClick}
                disabled={disabled}
            >
                <CloseIcon />
            </IconButton>
        );
    }
);
CloseButton.displayName = 'MessageArea.CloseButton';

const Warning = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[6],
}));
Warning.displayName = 'MessageArea.Warning';

const StyledTemplatePreview = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[6],
    whiteSpace: 'break-spaces',
}));

export type TemplatePreviewProps = {
    content: string;
};

export const TemplatePreview = forwardRef(
    ({ content }: TemplatePreviewProps, ref: React.ForwardedRef<HTMLDivElement>) => {
        return (
            <StyledTemplatePreview ref={ref}>
                <Interweave content={content} />
            </StyledTemplatePreview>
        );
    }
);
TemplatePreview.displayName = 'MessageArea.TemplatePreview';

export const MessageArea = {
    Root,
    UseTemplateButton,
    CloseButton,
    Warning,
    TemplatePreview,
    Scroller,
};
