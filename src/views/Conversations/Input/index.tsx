import { TextField, inputBaseClasses, outlinedInputClasses, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { EnterpriseWhatsappApi } from 'api/enterprise';
import { getErrorMessage } from 'api/error';
import { default as WhatsApp, default as WhatsAppApi } from 'api/whatsapp';
import clsx from 'clsx';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import { useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import ConversationTemplateSender, { PredefinedTemplateData } from '../ConversationTemplateSender';
import FastMessages from '../FastMessages/FastMessages';
import { allowSendMessages, isClosedTimeWindow, noContext, notCurrentControlFlow } from '../logic';
import { MessageArea } from '../MessageArea';
import SendButton from '../SendButton';
import Sender from '../Sender/Sender';

export type InputProps = {
    conversation: Pick<
        ConversationDto,
        | 'conversationId'
        | 'chatBotMode'
        | 'appointmentId'
        | 'repairOrderId'
        | 'massSendingId'
        | 'vehicleId'
        | 'lastInboundMessage'
        | 'currentControlFlow'
    >;
    rightOffset: number;
    onMessageCreated: () => void;
    shopId?: string;
};

const debugOverrideConversationState = new URLSearchParams(window.location.search).get(
    'debug.overrideConversationState'
);
const debugCanSendMessages =
    new URLSearchParams(window.location.search).get('debug.canSendMessages') === 'true';

const Input = ({ conversation, rightOffset, onMessageCreated, shopId }: InputProps) => {
    const { t } = useAppTranslation();
    const isEnterprise = useIsEnterpriseRoute();
    const toasters = useToasters();
    const gs = useAppSelector(selectSettings);

    const [text, setText] = useState('');
    const [mode, setMode] = useState<'text' | 'template' | 'predefined-template'>('text');
    const [templateText, setTemplateText] = useState('');
    const [predefinedTemplateName, setPredefinedTemplateName] = useState('');

    const sendTextMessageMutation = useMutation({
        mutationFn: async (text: string) => {
            // NOTE (MB) both enterprise and shop are the same here
            await WhatsApp.sendTextMessage(conversation.conversationId, text);
            onMessageCreated();
        },
        onError(error) {
            toasters.danger(getErrorMessage(error), t('toasters.errorOccurred'));
        },
    });

    const sendOrderTemplateMessageMutation = useMutation({
        mutationFn: async (text: string) => {
            if (!conversation.repairOrderId) {
                console.error('cannot send a template, orderKey is not set');
                return;
            }
            // NOTE (MB) both enterprise and shop are the same here
            await WhatsApp.sendTemplateMessage(
                conversation.conversationId,
                conversation.repairOrderId,
                text
            );
            onMessageCreated();
        },
        onError(error) {
            toasters.danger(getErrorMessage(error), t('toasters.errorOccurred'));
        },
    });

    const sendPredefinedTemplateMutation = useMutation({
        mutationFn: async (templateName: string) => {
            if (isEnterprise) {
                if (!shopId) {
                    throw new Error('shopId is not defined');
                }
                await EnterpriseWhatsappApi.sendPredefinedTemplate(
                    conversation.conversationId,
                    shopId,
                    templateName
                );
            } else {
                await WhatsAppApi.sendPredefinedTemplate(conversation.conversationId, templateName);
            }
        },
        onError(error) {
            toasters.danger(getErrorMessage(error), t('toasters.errorOccurred'));
        },
    });

    function handlePredefinedTemplateSelected(data: PredefinedTemplateData) {
        if (!data.preview) return;
        setMode('predefined-template');
        setTemplateText(data.preview);
        setPredefinedTemplateName(data.templateName);
    }

    const fastMessages = useMemo(
        () => [
            t('conversations.fastMessages.welcome'),
            t('conversations.fastMessages.service'),
            t('conversations.fastMessages.pleasure'),
        ],
        [t]
    );

    const templates = useMemo<OptionData[]>(
        () =>
            conversation.repairOrderId
                ? [
                      {
                          value: 'cm_consumer_numbers',
                          label: t('conversations.templates.cm_consumer_numbers'),
                      },
                  ]
                : [],
        [t, conversation.repairOrderId]
    );

    const [selectedTemplate, setSelectedTemplate] = useState<OptionData | null>(null);

    const canSendText = allowSendMessages(conversation, gs) || debugCanSendMessages;
    const canSendTemplate = !!conversation.repairOrderId || debugCanSendMessages;

    useEffect(() => {
        if (!canSendTemplate) setMode('text');
    }, [canSendTemplate]);

    const { data: orderTemplatePreview } = useQuery({
        queryKey: [
            'template-preview',
            selectedTemplate?.value,
            conversation.conversationId,
            conversation.repairOrderId,
        ],
        enabled: !!conversation.repairOrderId && !!selectedTemplate,
        queryFn: () =>
            WhatsApp.previewTemplate(
                conversation.conversationId,
                conversation.repairOrderId!,
                selectedTemplate!.value
            ),
    });

    useEffect(() => {
        setTemplateText(orderTemplatePreview || '');
    }, [orderTemplatePreview]);

    const handleSend = async () => {
        if (
            sendTextMessageMutation.isLoading ||
            sendOrderTemplateMessageMutation.isLoading ||
            sendPredefinedTemplateMutation.isLoading
        )
            return;
        switch (mode) {
            case 'text':
                if (text.length > 0) {
                    await sendTextMessageMutation.mutateAsync(text);
                    setText('');
                }
                break;
            case 'template':
                if (selectedTemplate) {
                    await sendOrderTemplateMessageMutation.mutateAsync(selectedTemplate.value);
                    setMode('text');
                    setSelectedTemplate(null);
                }
                break;
            case 'predefined-template':
                if (predefinedTemplateName) {
                    await sendPredefinedTemplateMutation.mutateAsync(predefinedTemplateName);
                    setMode('text');
                    setPredefinedTemplateName('');
                    setTemplateText('');
                }
                break;
        }
    };

    const isSendButtonDisabled =
        sendOrderTemplateMessageMutation.isLoading ||
        sendTextMessageMutation.isLoading ||
        sendPredefinedTemplateMutation.isLoading ||
        (mode === 'text' && (text.length === 0 || !canSendText)) ||
        (mode === 'template' && !selectedTemplate);

    function handleUseTemplate() {
        setMode((prev) => (prev === 'text' && canSendTemplate ? 'template' : 'text'));
    }

    function handleCloseMessageAreaClick() {
        if (mode === 'predefined-template') {
            setMode('text');
            setPredefinedTemplateName('');
            setTemplateText('');
        }
    }

    const state:
        | 'noContext'
        | 'notCurrentControlFlow'
        | 'isChatBotMode'
        | 'isClosedTimeWindow'
        | 'normal' = useMemo(() => {
        if (debugOverrideConversationState) {
            switch (debugOverrideConversationState) {
                case 'noContext':
                    return 'noContext';
                case 'notCurrentControlFlow':
                    return 'notCurrentControlFlow';
                case 'isChatBotMode':
                    return 'isChatBotMode';
                case 'isClosedTimeWindow':
                    return 'isClosedTimeWindow';
                case 'normal':
                    return 'normal';
            }
        }

        if (noContext(conversation)) return 'noContext';
        if (notCurrentControlFlow(conversation)) return 'notCurrentControlFlow';
        if (isClosedTimeWindow(conversation)) return 'isClosedTimeWindow';
        return 'normal';
    }, [conversation]);

    const warningText = useMemo(() => {
        switch (state) {
            case 'noContext':
                return t('conversations.noContextWarning');
            case 'notCurrentControlFlow':
                return t('conversations.notCurrentControlFlowWarning');
            case 'isChatBotMode':
                return t('conversations.chatBotModeWarning');
            case 'isClosedTimeWindow':
                return t('conversations.closedWindowTimeWarning');
        }
    }, [state, t]);

    return (
        <Root style={{ paddingRight: 24 + rightOffset }}>
            <Sender.Layout>
                {(state === 'isClosedTimeWindow' || state === 'isChatBotMode') &&
                mode !== 'predefined-template' ? (
                    <Sender.Overlay>
                        <ConversationTemplateSender
                            title={
                                state === 'isChatBotMode'
                                    ? t('conversations.chatBotModeWarning')
                                    : t('conversations.closedWindowTimeWarning')
                            }
                            conversationId={conversation.conversationId}
                            hasAppointmentId={!!conversation.appointmentId}
                            onTemplateSelected={handlePredefinedTemplateSelected}
                            shopId={shopId ?? null}
                        />
                    </Sender.Overlay>
                ) : (
                    <>
                        <Sender.Message>
                            <MessageArea.Root className={clsx(!canSendText && 'restrictions')}>
                                <MessageArea.Scroller>
                                    {mode === 'text' && canSendText && (
                                        <MessageTextField
                                            multiline
                                            value={text}
                                            onChange={(e) => setText(e.target.value)}
                                            placeholder={t('conversations.textAreaPlaceholder')}
                                            minRows={3.2}
                                            disabled={sendTextMessageMutation.isLoading}
                                        />
                                    )}
                                    {mode === 'text' && !canSendText && (
                                        <MessageArea.Warning>{warningText}</MessageArea.Warning>
                                    )}
                                    {(mode === 'template' || mode === 'predefined-template') && (
                                        <MessageArea.TemplatePreview content={templateText} />
                                    )}
                                </MessageArea.Scroller>

                                {mode !== 'predefined-template' ? (
                                    <MessageArea.UseTemplateButton
                                        disabled={templates.length === 0}
                                        isActive={mode === 'template'}
                                        onClick={handleUseTemplate}
                                    />
                                ) : (
                                    <MessageArea.CloseButton
                                        onClick={handleCloseMessageAreaClick}
                                    />
                                )}
                            </MessageArea.Root>
                        </Sender.Message>
                        <Sender.Send>
                            <SendButton
                                onClick={handleSend}
                                showLoader={
                                    sendTextMessageMutation.isLoading ||
                                    sendOrderTemplateMessageMutation.isLoading
                                }
                                disabled={isSendButtonDisabled}
                            />
                        </Sender.Send>
                    </>
                )}
                <Sender.Top>
                    {mode === 'template' && templates.length > 0 && (
                        <Dropdown
                            name={'templates'}
                            cmosVariant="roundedGrey"
                            options={templates}
                            value={selectedTemplate}
                            onChange={(option) => setSelectedTemplate(option)}
                            slotProps={{
                                inputWrapper: {
                                    sx: {
                                        width: 320,
                                    },
                                },
                            }}
                            isSearchable={false}
                            placeholder={t('conversations.selectTemplate')}
                        />
                    )}
                    {mode === 'text' && canSendText && (
                        <FastMessages.Root>
                            {fastMessages.map((text) => (
                                <FastMessages.Button
                                    key={text}
                                    onClick={() => sendTextMessageMutation.mutate(text)}
                                    text={text}
                                    disabled={sendTextMessageMutation.isLoading}
                                />
                            ))}
                        </FastMessages.Root>
                    )}
                </Sender.Top>
            </Sender.Layout>
        </Root>
    );
};

const Root = styled('div')(({ theme }) => ({
    paddingTop: 10,
    paddingLeft: 32,
    paddingBottom: 23,
    backgroundColor: theme.palette.neutral[1],
}));

const MessageTextField = styled(TextField)({
    width: '100%',
    padding: 0,
    [`& .${inputBaseClasses.root}`]: {
        padding: 0,
    },

    [`& .${outlinedInputClasses.notchedOutline}`]: {
        border: 'none',
    },
});

export default Input;
