import { Box, styled } from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { MicIcon } from 'common/components/Icons/MicIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import moment from 'moment';
import { memo, useEffect, useRef, useState } from 'react';
import LinesEllipsis from 'react-lines-ellipsis';
import { useResizeDetector } from 'react-resize-detector';
import { getConsumerName } from '../logic';

/**
 * Type that contains enough information for Conversation
 * to be displayed
 */
export type ConversationInput = {
    conversationId: number;
    customerFirstName: string;
    customerLastName: string;
    customerPhoneNumber: string;
    lastInboundMessage: string;
    lastSentMessage: string;
    lastMessageContent: string;
    unreadMessages: number;
    chatBotMode: boolean;
    shopName?: string;
};

type ConversationProps = {
    conversation: ConversationInput;
    isSelected: boolean;
    onSelect: () => void;
};

export function Conversation({ conversation, isSelected, onSelect }: ConversationProps) {
    const message = useRef<HTMLDivElement>(null);
    const { t } = useAppTranslation();

    const getDate = () => {
        const inboundDate = moment(conversation.lastInboundMessage);
        const sentDate = moment(conversation.lastSentMessage);

        let maxDate = moment();

        if (!inboundDate.isValid()) maxDate = sentDate;
        else if (!sentDate.isValid()) maxDate = inboundDate;
        else maxDate = moment.max(inboundDate, sentDate);

        const isToday = maxDate.isSame(new Date(), 'day');

        if (isToday) return maxDate.format('h:mm A');

        return maxDate.format(t('conversations.dateFormat'));
    };

    const hasUnreadMessages = conversation.unreadMessages > 0;

    return (
        <DivConversationContainer
            className={isSelected ? 'isSelected' : undefined}
            onClick={onSelect}
        >
            <DivConsumerAndMessage>
                <DivConsumer>{getConsumerName(conversation)}</DivConsumer>
                <DivShopName>{conversation.shopName}</DivShopName>
                <DivMessage ref={message}>
                    <ConversationLastMessageContent
                        content={conversation.lastMessageContent || ''}
                    />
                </DivMessage>
            </DivConsumerAndMessage>
            <DivBadge>
                {hasUnreadMessages ? (
                    <StyledCircle>{conversation.unreadMessages}</StyledCircle>
                ) : (
                    <CheckIcon fill={Colors.Neutral5} size={IconSize.M} />
                )}
            </DivBadge>
            <DivDate highlighted={hasUnreadMessages}>{getDate()}</DivDate>
        </DivConversationContainer>
    );
}

function ConversationLastMessageContent({ content }: { content: string }) {
    const { t } = useAppTranslation();

    if (content.startsWith('!audio_message!')) {
        return (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <MicIcon fill="currentColor" />
                <span>{t('conversations.voiceMessage')}</span>
            </Box>
        );
    }

    return (
        <RefreshableEllipsis
            text={content}
            maxLine={2}
            ellipsis="..."
            trimRight
            basedOn="letters"
        />
    );
}

const DivConsumer = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[8],
    ...theme.typography.h5Roboto,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    minHeight: 17,
}));

const DivMessage = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[6],
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    marginTop: 5,
}));

const DivShopName = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[6],
    ...theme.typography.h9Roboto,
    fontWeight: 'normal',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    marginTop: 3,
    marginBottom: 5,
    minHeight: 11,
}));

const DivConsumerAndMessage = styled('div')({
    marginTop: 2,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    width: 0,
});

const DivDate = styled('div', {
    shouldForwardProp: (prop) => prop !== 'highlighted',
})<{ highlighted: boolean }>(({ theme, highlighted }) => ({
    ...theme.typography.h9Roboto,
    color: highlighted ? theme.palette.primary.main : theme.palette.neutral[5],
    position: 'absolute',
    right: 24,
}));

const DivConversationContainer = styled('div')(({ theme }) => ({
    paddingTop: 16,
    paddingRight: 24,
    paddingBottom: 13,
    paddingLeft: 32,
    borderBottomColor: theme.palette.neutral[3],
    borderBottomWidth: 1,
    borderBottomStyle: 'solid',
    display: 'flex',
    height: 90,
    boxSizing: 'border-box',
    position: 'relative',
    cursor: 'pointer',
    transition: 'background-color .2s',

    '&.isSelected': {
        backgroundColor: 'var(--cm5)',
        cursor: 'default',

        ':hover': {
            backgroundColor: 'var(--cm5)',
        },

        ':active': {
            backgroundColor: 'var(--cm5)',
        },
    },

    ':hover': {
        backgroundColor: theme.palette.action.hover,
    },

    ':active': {
        backgroundColor: theme.palette.action.focus,
    },

    '&:last-child': {
        borderBottomStyle: 'none',
    },
}));

const StyledCircle = styled('span')(({ theme }) => ({
    borderRadius: 12,
    backgroundColor: theme.palette.primary.light,
    color: theme.palette.neutral[1],
    ...theme.typography.h6Inter,
    height: 24,
    minWidth: 24,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 4.5,
    paddingRight: 4.5,
    boxSizing: 'border-box',
}));

const DivBadge = styled('div')({
    paddingLeft: 8,
    paddingTop: 30,
});

const RefreshableEllipsis = ({ ...props }: LinesEllipsis.ReactLinesEllipsisProps) => {
    const { width, ref } = useResizeDetector();
    const [show, setShow] = useState(true);

    useEffect(() => {
        if (width) {
            setShow(false);
            setTimeout(() => {
                setShow(true);
            });
        }
    }, [width]);

    return (
        <Box sx={{ width: '100%' }} ref={ref}>
            {show && <LinesEllipsisMemo {...props} />}
        </Box>
    );
};

const LinesEllipsisMemo = memo(LinesEllipsis);

export default Conversation;
