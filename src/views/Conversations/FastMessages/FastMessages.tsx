import { styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { ButtonProps } from 'common/components/Button/ButtonProps';
import { useEffect, useRef } from 'react';

const Root = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    columnGap: 16,
});

const StyledFastMessageButton = styled(Button)(({ theme }) => ({
    width: 'initial',
    paddingLeft: 16,
    paddingRight: 16,
    ...theme.typography.h6Roboto,
    fontWeight: '500 !important',

    '&:hover': {
        backgroundColor: 'white !important',
        fontWeight: 'bold !important',
    },
}));

export type FastMessageButtonProps = Pick<ButtonProps, 'onClick' | 'disabled'> & { text: string };

export function FastMessageButton({ text, onClick, disabled }: FastMessageButtonProps) {
    const buttonRef = useRef<HTMLButtonElement>(null);

    useEffect(() => {
        // explicitly set width on mount to avoid resizing if font weight changes
        window.requestAnimationFrame(() => {
            const el = buttonRef.current;
            if (!el) return;
            el.style.removeProperty('width');
            el.style.width = el.getBoundingClientRect().width * 1.1 + 'px';
        });
    }, [text]);

    return (
        <StyledFastMessageButton
            ref={buttonRef}
            cmosVariant="stroke"
            onClick={onClick}
            disabled={disabled}
        >
            {text}
        </StyledFastMessageButton>
    );
}

const FastMessages = {
    Root,
    Button: FastMessageButton,
};

export default FastMessages;
