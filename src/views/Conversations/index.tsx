import { styled } from '@mui/material';
import { PUBNUB_CHANNELS } from 'api/pubnub';
import Instruction from 'common/components/Instruction';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { motion } from 'framer-motion';
import { ComponentProps, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useAppSelector } from 'store';
import { setShownConversationId } from 'store/slices/conversations';
import {
    selectConversations,
    selectSelectedConversationId,
} from 'store/slices/conversations/selectors';
import readConversation from 'store/slices/conversations/thunks/readConversation';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { usePubnubListener } from 'utils/pubnub';
import { CustomHeaderContent } from 'views/HeaderBar';
import {
    ConversationMessageAddedMessage,
    MessageDto,
    default as WhatsApp,
    default as WhatsAppAPI,
} from '../../api/whatsapp';
import AreaSpinner from '../../common/components/AreaSpinner';
import { useApiCall } from '../../common/hooks';
import ConversationList from './ConversationList';
import Filter from './Filter';
import Header from './Header';
import Input from './Input';
import Messages from './Messages';
import Preview from './Preview';
import useShopConversationsPubnubListener from './pubnub';
import SearchField from './SearchField';

const PREVIEW_WIDTH = 430;

export default function Conversations() {
    const { conversationId: conversationIdParam } = useParams<{ conversationId?: string }>();
    const conversationId = conversationIdParam != null ? parseInt(conversationIdParam) : null;
    const conversations = useSelector(selectConversations);
    const shownConversationId = useSelector(selectSelectedConversationId);
    const settings = useAppSelector(selectSettings);
    const dispatch = useDispatch();
    const { callApi: callListMessagesApi, apiCallStatus: listMessagesCallStatus } = useApiCall();

    const { t } = useAppTranslation();

    useShopConversationsPubnubListener(settings);

    const [selectedConversationId, setSelectedConversationId] = useState<number | null>(
        conversationId
    );
    const [messages, setMessages] = useState<MessageDto[]>([]);
    const [messagesScrollBarWidth, setMessagesScrollBarWidth] = useState(0);
    const [detailsVisibility, setDetailsVisibility] = useState<
        'visible' | 'hidden' | 'immediatelyHidden'
    >('hidden');

    useDocumentTitle(t('titles.conversations'));

    useEffect(() => setSelectedConversationId(conversationId), [conversationId]);

    useEffect(() => {
        (async () => {
            dispatch(setShownConversationId(null));
            if (selectedConversationId) {
                const result = await callListMessagesApi(
                    () => WhatsApp.listMessages(selectedConversationId),
                    {
                        selectErrorContent: () => ({
                            body: t('toasters.errorOccurredWhenLoading'),
                        }),
                    }
                );

                setMessages(result);
                dispatch(setShownConversationId(selectedConversationId));
            }
        })();
        return () => {
            dispatch(setShownConversationId(null));
        };
    }, [dispatch, t, callListMessagesApi, selectedConversationId]);

    useEffect(() => {
        const shownConversation = conversations.find(
            (c) => c.conversationId === shownConversationId
        );
        if (shownConversation && shownConversation.unreadMessages > 0) {
            dispatch(readConversation(shownConversation.conversationId));
        }
    }, [conversations, dispatch, shownConversationId]);

    usePubnubListener<ConversationMessageAddedMessage>(
        (_message) => {
            const event = _message.message.payload;
            if (event.conversationId === shownConversationId) {
                const message: MessageDto = {
                    id: event.messageId,
                    content: event.contents,
                    contentUrl: event.contentUrl,
                    isInbound: event.isInbound,
                    sentAt: event.timestamp,
                    user: event.sentBy
                        ? {
                              id: event.sentBy.id,
                              name: event.sentBy.name,
                              displayName: event.sentBy.displayName,
                          }
                        : null,
                    type: event.type,
                    audio: event.audio
                        ? { url: event.audio.url, duration: event.audio.duration }
                        : null,
                };
                setMessages((prev) => {
                    const index = prev.findIndex((x) => x.id === message.id);
                    if (index === -1) return prev.concat(message);
                    const newList = [...prev];
                    newList[index] = message;
                    return newList;
                });
                dispatch(readConversation(shownConversationId));
            }
        },
        {
            unsubscribeTimeoutMs: 60000,
            channels: [PUBNUB_CHANNELS.shopConversations(settings.uid)],
            types: ['message.added'],
            listenerEnabled: settings.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    useEffect(() => setDetailsVisibility('immediatelyHidden'), [selectedConversationId]);

    const selectedConversation = conversations.find(
        (c) => c.conversationId === selectedConversationId
    );

    const onMessageCreated = () => {
        if (selectedConversation) {
            WhatsAppAPI.listMessages(selectedConversation.conversationId).then((response) => {
                setMessages(response);
            });
        }
    };

    return (
        <Root>
            <CustomHeaderContent>
                <Filter shouldResetFilter={!!conversationId} />
            </CustomHeaderContent>
            <Area>
                <LeftContainer>
                    <SearchField />
                    <RepairShopConversations
                        onSelectConversation={setSelectedConversationId}
                        selectedConversationId={selectedConversationId}
                    />
                </LeftContainer>
                {selectedConversation != null ? (
                    <RightContainer>
                        <Header
                            conversation={selectedConversation}
                            isDetailsOpened={detailsVisibility === 'visible'}
                            toggleDetails={() =>
                                setDetailsVisibility((prev) =>
                                    prev === 'visible' ? 'hidden' : 'visible'
                                )
                            }
                        />
                        <CenterArea>
                            <MessagesArea>
                                {listMessagesCallStatus !== 'Finished' && <SAreaSpinner />}
                                {listMessagesCallStatus === 'Finished' && (
                                    <>
                                        <Messages
                                            messages={messages}
                                            onScrollbarWidthChanged={setMessagesScrollBarWidth}
                                        />
                                        <Input
                                            onMessageCreated={onMessageCreated}
                                            conversation={selectedConversation}
                                            rightOffset={messagesScrollBarWidth}
                                        />
                                    </>
                                )}
                            </MessagesArea>
                            <motion.div
                                animate={detailsVisibility}
                                variants={{
                                    visible: { width: 430 },
                                    hidden: { width: 0 },
                                    immediatelyHidden: {
                                        width: 0,
                                        transition: { duration: 0 },
                                    },
                                }}
                                transition={{ duration: 0.15, ease: 'easeOut' }}
                            />
                            <PreviewWrapper>
                                <MotionPreview
                                    animate={detailsVisibility}
                                    variants={{
                                        visible: { translateX: 0 },
                                        hidden: { translateX: PREVIEW_WIDTH },
                                        immediatelyHidden: {
                                            translateX: PREVIEW_WIDTH,
                                            transition: { duration: 0 },
                                        },
                                    }}
                                    transition={{ duration: 0.15, ease: 'easeOut' }}
                                >
                                    <Preview
                                        enabled={detailsVisibility === 'visible'}
                                        conversation={selectedConversation}
                                        onClose={() => setDetailsVisibility('hidden')}
                                    />
                                </MotionPreview>
                            </PreviewWrapper>
                        </CenterArea>
                    </RightContainer>
                ) : (
                    <Instruction mainText={t('conversations.instruction')} />
                )}
            </Area>
        </Root>
    );
}

function RepairShopConversations(
    props: Omit<ComponentProps<typeof ConversationList>, 'conversations'>
) {
    const conversations = useSelector(selectConversations);

    return <ConversationList {...props} conversations={conversations} />;
}

const Root = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    height: 'calc(100vh - var(--header-height))',
});

const RightContainer = styled('div')({
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
});

const LeftContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    width: 420,
    minWidth: 420,
});

const Area = styled('div')({
    display: 'flex',
    height: 0,
    flexGrow: 1,
});

const CenterArea = styled('div')(({ theme }) => ({
    height: 0,
    flexGrow: 1,
    display: 'flex',
    position: 'relative',
    backgroundColor: theme.palette.neutral[1],
}));

const PreviewWrapper = styled('div')({
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    overflowX: 'hidden',
});

const MotionPreview = motion(
    styled('div')({
        display: 'flex',
        height: '100%',
    })
);

const SAreaSpinner = styled(AreaSpinner)({ paddingBottom: 56 });

const MessagesArea = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    width: 0,
    flexGrow: 1,
    zIndex: 1,
});
