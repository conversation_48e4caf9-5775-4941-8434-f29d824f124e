import { PUBNUB_CHANNELS } from 'api/pubnub';
import { ConversationGotControlFlowMessage, ConversationUpdatedMessage } from 'api/whatsapp';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { useDispatch } from 'react-redux';
import {
    LostControlFlowEvent,
    handleConversationUpdate,
    handleLostControlFlow,
    resetState,
} from 'store/slices/conversations';
import fetchConversations from 'store/slices/conversations/thunks/fetchConversations';
import { useCurrentUser } from 'store/slices/user';

import { usePubnubListener } from 'utils/pubnub';

function useShopConversationsPubnubListener(settings: GlobalSettingsDto | null) {
    const dispatch = useDispatch();
    const user = useCurrentUser();

    usePubnubListener<ConversationUpdatedMessage | ConversationGotControlFlowMessage>(
        (_message) => {
            dispatch(handleConversationUpdate({ user, message: _message.message.payload }));
        },
        {
            channels: [PUBNUB_CHANNELS.shopConversations(settings?.uid || '')],
            types: ['conversation.updated', 'conversation.gotcontrolflow'],
            listenerEnabled: settings?.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    usePubnubListener<LostControlFlowEvent>(
        (_message) => {
            dispatch(handleLostControlFlow(_message.message.payload));
        },
        {
            channels: [PUBNUB_CHANNELS.shopConversations(settings?.uid || '')],
            types: ['conversation.lostcontrolflow'],
            listenerEnabled: settings?.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    usePubnubListener<unknown>(
        async () => {
            dispatch(resetState());
            dispatch(fetchConversations());
        },
        {
            channels: [PUBNUB_CHANNELS.user(user.key)],
            types: ['user.updated'],
            unsubscribeIfListenerDisabled: true,
        }
    );

    //#endregion
}

export default useShopConversationsPubnubListener;
