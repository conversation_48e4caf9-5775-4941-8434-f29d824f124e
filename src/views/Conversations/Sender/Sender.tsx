import { styled } from '@mui/material';

const Layout = styled('div', { name: 'SenderLayout' })({
    display: 'grid',
    gridTemplateColumns: '1fr auto',
    gridTemplateAreas: '"top top" "message send"',
    gap: '10px',
    position: 'relative',
    minHeight: '90px',
});

const Sender = {
    Layout,
    Top: styled('div', { name: 'SenderTop' })({
        gridArea: 'top',
        minHeight: 48,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',

        '&:empty': {
            display: 'none',
        },
    }),
    Send: styled('div', { name: 'SenderSend' })({ gridArea: 'send' }),
    Message: styled('div', { name: 'SenderMessage' })({ gridArea: 'message' }),
    Overlay: styled('div', { name: 'SenderOverlay' })({
        inset: 0,
        backgroundColor: '#fff',
        position: 'absolute',
        zIndex: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        padding: '10px 0',
    }),
};

export default Sender;
