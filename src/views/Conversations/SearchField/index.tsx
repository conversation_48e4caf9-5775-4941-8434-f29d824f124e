import { InputAdornment } from '@mui/material';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/conversations';
import { selectFilter } from 'store/slices/conversations/selectors';
import styles from './styles.module.css';

const SearchField = () => {
    const dispatch = useAppDispatch();
    const conversationFilter = useAppSelector(selectFilter);
    const { t } = useAppTranslation();

    const setSearchTerm = (value: string) => {
        dispatch(setFilters({ ...conversationFilter, searchTerm: value }));
    };

    return (
        <TextFormField
            cmosVariant="roundedGrey"
            name={'search'}
            value={conversationFilter.searchTerm}
            placeholder={t('conversations.filter.searchPlaceholder')}
            endAdornment={
                <InputAdornment position="end">
                    <SearchIcon />
                </InputAdornment>
            }
            onChange={(e) => setSearchTerm(e.target.value)}
            inputWrapperClasses={{ self: styles.search }}
        />
    );
};

export default SearchField;
