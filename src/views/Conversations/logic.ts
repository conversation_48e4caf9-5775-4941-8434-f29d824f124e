import { CustomerPreviewDto } from 'api/customers';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import moment from 'moment';

// NOTE (MB) we are using inline types here to allow other types (other than ConversationDto) to be passed, this
// allows us to use these functions for both enterprise and repair shop contracts since they are similar enough

export function allowSendMessages(
    conversation: {
        // for isChatBotMode
        chatBotMode: boolean;

        // for notCurrentControlFlow
        currentControlFlow: boolean;

        // for isClosedTimeWindow
        lastInboundMessage: string;

        // for noContext
        repairOrderId: string | null;
        appointmentId: string | null;
        vehicleId: string | null;
        massSendingId: string | null;
    },
    gs: GlobalSettingsDto
) {
    if (isClosedTimeWindow(conversation)) return false;
    if (notCurrentControlFlow(conversation)) return false;
    if (noContext(conversation)) return false;

    return true;
}

export function isChatBotMode(conversation: { chatBotMode: boolean }) {
    return conversation.chatBotMode;
}

export function isClosedTimeWindow(conversation: { lastInboundMessage: string }) {
    return !isOpenWindowTime(conversation);
}

export function notCurrentControlFlow(conversation: { currentControlFlow: boolean }) {
    return !conversation.currentControlFlow;
}

export function hasContext(conversation: {
    repairOrderId: string | null;
    appointmentId: string | null;
    vehicleId: string | null;
    massSendingId: string | null;
}) {
    return (
        !!conversation.repairOrderId ||
        !!conversation.appointmentId ||
        !!conversation.vehicleId ||
        !!conversation.massSendingId
    );
}
export function noContext(conversation: {
    repairOrderId: string | null;
    appointmentId: string | null;
    vehicleId: string | null;
    massSendingId: string | null;
}) {
    return !hasContext(conversation);
}

function isOpenWindowTime(conversation: { lastInboundMessage: string }) {
    return moment().diff(moment(conversation.lastInboundMessage), 'hours') <= 24;
}

export function getConsumerName(conversation: {
    customerFirstName: string;
    customerLastName: string;
    customerPhoneNumber: string;
}) {
    let consumer = `${conversation.customerFirstName ?? ''} ${
        conversation.customerLastName ?? ''
    }`.trim();
    if (!consumer.length) consumer = '+' + conversation.customerPhoneNumber;
    return consumer;
}

export function getCustomerName(customer: CustomerPreviewDto) {
    let result = `${customer.firstName ?? ''} ${customer.lastName ?? ''}`.trim();
    if (!result.length) result = '+' + customer.mobile;
    return result;
}
