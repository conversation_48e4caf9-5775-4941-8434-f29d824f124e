import { styled } from '@mui/material';
import { jobTitleLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useAppSelector } from 'store';
import { selectedDate } from 'store/slices/wp/plannings';
import { TechnicianCapacityMetrics, TechnicianHours } from '../../helpers';

type TechnicianCapacityTooltipProps = {
    techniciansHours: TechnicianHours[];
    technicianCapacityMetrics: TechnicianCapacityMetrics;
};

function TechnicianCapacityTooltip({
    techniciansHours,
    technicianCapacityMetrics,
}: TechnicianCapacityTooltipProps) {
    const { t } = useAppTranslation();
    const workDays = 1;
    const zeroRegExp = /.00|0$/;
    const date = useAppSelector(selectedDate);
    const dateString = date.toFormat(t('dateFormats.short'));

    const TotalMetrics = () => {
        return (
            <FormulaValues>
                <li>
                    {t('workshopPlanner.technicianCapacityTooltip.technicians') + ': '}
                    {techniciansHours.length}
                </li>
                <li>
                    {t('workshopPlanner.technicianCapacityTooltip.averageAvailableHours') + ': '}

                    {technicianCapacityMetrics.averageAvailableHoursPerTechnician
                        .toFixed(2)
                        .replace(zeroRegExp, '')}
                </li>
                <li>
                    {t('workshopPlanner.technicianCapacityTooltip.prevWeekTechnicalProductivity') +
                        ': '}
                    {technicianCapacityMetrics.prevWeekTechniciansProductivity
                        .toFixed(2)
                        .replace(zeroRegExp, '') + '%'}
                </li>
                <li>
                    {t('workshopPlanner.technicianCapacityTooltip.assignedHours') + ': '}
                    {technicianCapacityMetrics.assignedHoursForPeriod}
                </li>
            </FormulaValues>
        );
    };

    return (
        <DivRoot>
            <Section>
                <DivTitle>{t('workshopPlanner.technicianCapacityTooltip.inputData')}</DivTitle>
                <InputDataContainer>
                    <div>
                        {t('workshopPlanner.technicianCapacityTooltip.period') + ': ' + dateString}
                    </div>
                    <div>
                        {t('workshopPlanner.technicianCapacityTooltip.workDays') + ': ' + workDays}
                    </div>
                </InputDataContainer>
            </Section>
            <Section>
                <DivTitle>
                    {t(
                        'workshopPlanner.technicianCapacityTooltip.availableActualTechnicalCapacityTitle',
                        {
                            hours: technicianCapacityMetrics.availableActualTechnicalCapacityHours,
                        }
                    )}
                </DivTitle>
                <FormulaWithPreLine>
                    {`${t(
                        'workshopPlanner.technicianCapacityTooltip.availableActualTechnicalCapacity'
                    )} = ${t('workshopPlanner.technicianCapacityTooltip.technicians')} x ${t(
                        'workshopPlanner.technicianCapacityTooltip.averageAvailableHoursWithNewLine'
                    )} x ${t('workshopPlanner.technicianCapacityTooltip.workDays')} x ${t(
                        'workshopPlanner.technicianCapacityTooltip.prevWeekTechnicalProductivity'
                    )}
                    - ${t('workshopPlanner.technicianCapacityTooltip.assignedHours')}`}
                </FormulaWithPreLine>
                <TotalMetrics />
            </Section>
            <Section>
                <DivTitle>
                    {t('workshopPlanner.technicianCapacityTooltip.actualTechnicalCapacityUsed') +
                        ': ' +
                        technicianCapacityMetrics.technicalProductivityUsed +
                        '%'}
                </DivTitle>
                <FormulaWithPreLine>
                    {`${t(
                        'workshopPlanner.technicianCapacityTooltip.actualTechnicalCapacityUsed'
                    )} = (${t('workshopPlanner.technicianCapacityTooltip.assignedHours')} x 100) /
                    (${t('workshopPlanner.technicianCapacityTooltip.technicians')} x ${t(
                        'workshopPlanner.technicianCapacityTooltip.averageAvailableHours'
                    )} x ${t(
                        'workshopPlanner.technicianCapacityTooltip.prevWeekTechnicalProductivityWithNewLine'
                    )})`}
                </FormulaWithPreLine>
                <TotalMetrics />
            </Section>
            <Section>
                <DivTitle>
                    {t('workshopPlanner.technicianCapacityTooltip.actualTechnicalCapacityTitle', {
                        hours: technicianCapacityMetrics.actualTechnicalCapacity,
                    })}
                </DivTitle>
                <Formula>
                    {t('workshopPlanner.technicianCapacityTooltip.actualTechnicalCapacity') +
                        ' = ' +
                        t('workshopPlanner.technicianCapacityTooltip.technicians') +
                        ' x ' +
                        t('workshopPlanner.technicianCapacityTooltip.averageAvailableHours') +
                        ' x ' +
                        t('workshopPlanner.technicianCapacityTooltip.workDays') +
                        ' x ' +
                        t('workshopPlanner.technicianCapacityTooltip.technicalProductivity')}
                </Formula>
                <FormulaValues>
                    <li>
                        {t('workshopPlanner.technicianCapacityTooltip.technicians') + ': '}
                        {techniciansHours.length}
                    </li>
                    <li>
                        {t('workshopPlanner.technicianCapacityTooltip.averageAvailableHours') +
                            ': '}

                        {technicianCapacityMetrics.averageAvailableHoursPerTechnician
                            .toFixed(2)
                            .replace(zeroRegExp, '')}
                    </li>
                    <li>
                        {t('workshopPlanner.technicianCapacityTooltip.technicalProductivity') +
                            ': '}
                        {technicianCapacityMetrics.techniciansProductivity + '%'}
                    </li>
                </FormulaValues>
            </Section>
            <Section>
                <DivTitle>
                    {t('workshopPlanner.technicianCapacityTooltip.technicianProductivity', {
                        percentage: technicianCapacityMetrics.techniciansProductivity,
                    })}
                </DivTitle>
                <Formula>
                    {t('workshopPlanner.technicianCapacityTooltip.technicalProductivity') +
                        ': (' +
                        t('workshopPlanner.technicianCapacityTooltip.productiveHours') +
                        ' / ' +
                        t('workshopPlanner.technicianCapacityTooltip.workedHours') +
                        ') x 100'}
                </Formula>
                {techniciansHours.map((x) => {
                    const roundedProductiveHours = x.productiveHours
                        .toFixed(2)
                        .replace(zeroRegExp, '');
                    const roundedWorkedHours = x.workedHours.toFixed(2).replace(zeroRegExp, '');

                    return (
                        <Technician key={x.id}>
                            {t(jobTitleLabel(x.job)) + ' ' + x.name + ': '}
                            <BoldValue>
                                {(x.workedHours && x.productiveHours
                                    ? ((x.productiveHours / x.workedHours) * 100).toFixed(0)
                                    : 0) + '%'}
                            </BoldValue>
                            <TechniciansMetrics>
                                <li>
                                    {t('workshopPlanner.technicianCapacityTooltip.availableHours') +
                                        ': ' +
                                        x.totalAvailableHours.toFixed(2).replace(zeroRegExp, '')}
                                </li>
                                <li>
                                    {t('workshopPlanner.technicianCapacityTooltip.workedHours') +
                                        ': ' +
                                        roundedWorkedHours}
                                </li>
                                <li>
                                    {t(
                                        'workshopPlanner.technicianCapacityTooltip.productiveHours'
                                    ) +
                                        ': ' +
                                        roundedProductiveHours +
                                        ' (' +
                                        t(
                                            'workshopPlanner.technicianCapacityTooltip.completedJobs'
                                        ) +
                                        ')'}
                                </li>
                                <li>
                                    {t(
                                        'workshopPlanner.technicianCapacityTooltip.technicalProductivity'
                                    ) +
                                        ': (' +
                                        roundedProductiveHours +
                                        ' / ' +
                                        roundedWorkedHours +
                                        ') x 100 = ' +
                                        (x.workedHours && x.productiveHours
                                            ? ((x.productiveHours / x.workedHours) * 100).toFixed(0)
                                            : 0) +
                                        '%'}
                                </li>
                            </TechniciansMetrics>
                        </Technician>
                    );
                })}
            </Section>
        </DivRoot>
    );
}

const DivRoot = styled('div')(({ theme }) => ({
    padding: '19px 10px 14px 25px',
    maxWidth: '600px',
    maxHeight: 'calc(100vh - 70px)',
    overflowY: 'auto',
    boxSizing: 'border-box',

    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 22,

    ...theme.typography.h5Inter,
    fontWeight: 300,
    lineHeight: '22px',
    color: theme.palette.neutral[1],

    ...scrollbarStyle(),
}));

const DivTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontSize: 16,
}));

const Formula = styled('div')({
    fontStyle: 'italic',
});

const FormulaWithPreLine = styled(Formula)({
    whiteSpace: 'pre-line',
});

const Technician = styled('div')({
    paddingLeft: 5,
    marginBottom: 5,
});

const TechniciansMetrics = styled('ul')({
    margin: 0,
});

const FormulaValues = styled(TechniciansMetrics)({
    paddingLeft: '30px',
});

const BoldValue = styled('span')({
    fontWeight: 600,
});

const Section = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
});

const InputDataContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
});

export default TechnicianCapacityTooltip;
