import { Fade, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { StyledTooltip } from '../../common/TooltipComponents';
import { useTechnicianCapacityContext } from '../context';
import Indicator from '../Indicator';
import TechnicianCapacityTooltip from './TechnicianCapacityTooltip';

const TechnicianCapacityIndicator = () => {
    const { t } = useAppTranslation();

    const maxTechnicianCapacity =
        useAppSelector(selectSettings).repairShopSettings?.features.maximumTechnicianCapacity ?? 80;

    const technicianCapacity = useTechnicianCapacityContext();

    return (
        <>
            {technicianCapacity && (
                <StyledTooltip
                    arrow
                    placement="bottom-start"
                    title={
                        <TechnicianCapacityTooltip
                            techniciansHours={technicianCapacity.techniciansHours}
                            technicianCapacityMetrics={technicianCapacity.technicianCapacityMetrics}
                        />
                    }
                    TransitionComponent={Fade}
                    TransitionProps={{ timeout: 100 }}
                >
                    <DivRoot>
                        <Indicator
                            title={t('workshopPlanner.indicators.capacity')}
                            value={
                                technicianCapacity.technicianCapacityMetrics
                                    .technicalProductivityUsed
                            }
                            maxValue={maxTechnicianCapacity}
                            indicateOverload
                        />
                    </DivRoot>
                </StyledTooltip>
            )}
        </>
    );
};

const DivRoot = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    gap: 2,

    position: 'relative',
    minHeight: 33,
    paddingLeft: 16,
    marginLeft: 16,

    '::before': {
        content: '""',
        display: 'block',
        width: 1,
        backgroundColor: 'var(--neutral4)',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
    },
});

export default TechnicianCapacityIndicator;
