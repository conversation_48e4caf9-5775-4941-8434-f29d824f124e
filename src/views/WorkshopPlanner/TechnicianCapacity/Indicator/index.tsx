import { styled } from '@mui/material';
import { useMemo } from 'react';

type IndicatorProps = {
    title: string;
    value: number;
    maxValue?: number;
    indicateOverload?: boolean;
};

export default function Indicator({
    title,
    value,
    maxValue,
    indicateOverload = false,
}: IndicatorProps) {
    const zeroRegExp = /.00|0$/;

    const segments = useMemo(() => {
        const segmentsCount = 5;
        const percentagesPerSegment = 100 / segmentsCount;
        const overloadedSegmentIndex =
            maxValue !== undefined && value > maxValue
                ? Math.floor(maxValue / percentagesPerSegment)
                : segmentsCount;
        const segmentsProps: SegmentProps[] = [];

        for (let i = 0; i < segmentsCount; ++i) {
            const segmentIsOverloaded = indicateOverload ? i >= overloadedSegmentIndex : false;
            let percentageRemains = value - i * percentagesPerSegment;
            percentageRemains =
                percentageRemains < 0
                    ? 0
                    : percentageRemains > percentagesPerSegment
                    ? percentagesPerSegment
                    : percentageRemains;
            const segmentPercentage = Math.floor((percentageRemains / percentagesPerSegment) * 100);
            segmentsProps.push({
                percentage: segmentPercentage,
                isOverloaded: segmentIsOverloaded,
            });
        }

        return segmentsProps;
    }, [value, maxValue, indicateOverload]);

    return (
        <>
            <DivTitle>{title}</DivTitle>
            <DivValue>{value.toFixed(2).replace(zeroRegExp, '') + '%'}</DivValue>
            <DivSegmentsContainer>
                {segments.map((x, i) => (
                    <Segment
                        key={`segment-${i}`}
                        percentage={x.percentage}
                        isOverloaded={x.isOverloaded}
                    />
                ))}
            </DivSegmentsContainer>
        </>
    );
}

type SegmentProps = {
    percentage: number;
    isOverloaded: boolean;
};

function Segment({ percentage, isOverloaded }: SegmentProps) {
    return (
        <DivSegment>
            <DivSegmentProgress percentage={percentage} isOverloaded={isOverloaded} />
        </DivSegment>
    );
}

const DivTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h8Inter,
    color: theme.palette.neutral[8],
}));

const DivValue = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 400,
    textAlign: 'left',
}));

const DivSegmentsContainer = styled('div')({
    display: 'flex',
    gap: 2,
});

const DivSegment = styled('div')(({ theme }) => ({
    border: `solid 1px ${theme.palette.neutral[5]}`,
    borderRadius: 2,
    width: 12,
    height: 8,
}));

const DivSegmentProgress = styled('div', {
    shouldForwardProp: (prop) => {
        return !['percentage', 'isOverloaded'].includes(prop as string);
    },
})<{ percentage: number; isOverloaded: boolean }>(({ percentage, isOverloaded, theme }) => ({
    height: '100%',
    width: `${percentage}%`,
    backgroundColor: isOverloaded ? theme.palette.error.main : theme.palette.success.main,
}));
