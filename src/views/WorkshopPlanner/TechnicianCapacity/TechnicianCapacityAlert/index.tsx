import { styled } from '@mui/material';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { createContext, useContext, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useTechnicianCapacityContext } from '../context';
import { canAddNewHours, NewHours } from '../helpers';
import styles from './style.module.css';

interface ITechnicianCapacityAlertContext {
    checkTechnicianCapacity(callback: ConfirmAddingJobCallback, newHours?: NewHours): void;
}

type ConfirmAddingJobCallback = () => void;

const TechnicianCapacityAlertContext = createContext<ITechnicianCapacityAlertContext | null>(null);

export function useTechnicianCapacityAlert() {
    const ctx = useContext(TechnicianCapacityAlertContext);
    if (!ctx) throw new Error('cannot use TechnicianCapacityAlertContext');
    return ctx;
}

export function TechnicianCapacityAlertProvider({ children }: React.PropsWithChildren<{}>) {
    const { t } = useAppTranslation();

    const technicianCapacity = useTechnicianCapacityContext();

    const {
        showTechnicianCapacity,
        maximumTechnicianCapacity,
        allowSchedulingWithMaximumCapacity,
    } = useAppSelector(selectSettings).repairShopSettings?.features ?? {
        showTechnicianCapacity: false,
        maximumTechnicianCapacity: 80,
    };

    const [openPopup, setOpenPopup] = useState(false);
    const [confirmCallback, setConfirmCallback] = useState<ConfirmAddingJobCallback | null>(null);

    const ctx = useMemo(
        () => ({
            checkTechnicianCapacity: (callback: ConfirmAddingJobCallback, newHours?: NewHours) => {
                if (
                    !technicianCapacity ||
                    !showTechnicianCapacity ||
                    (technicianCapacity.technicianCapacityMetrics.technicalProductivityUsed <
                        maximumTechnicianCapacity &&
                        (!newHours ||
                            canAddNewHours(
                                technicianCapacity.techniciansHours,
                                maximumTechnicianCapacity,
                                newHours,
                                technicianCapacity.technicianCapacityMetrics
                                    .prevWeekTechniciansProductivity,
                                technicianCapacity.technicianCapacityMetrics
                                    .averageAvailableHoursPerTechnician
                            )))
                ) {
                    callback();
                    return;
                }

                if (allowSchedulingWithMaximumCapacity) {
                    setConfirmCallback(() => callback);
                    setOpenPopup(true);
                }
            },
        }),
        [
            technicianCapacity,
            showTechnicianCapacity,
            maximumTechnicianCapacity,
            allowSchedulingWithMaximumCapacity,
        ]
    );

    const closePopup = () => {
        setConfirmCallback(null);
        setOpenPopup(false);
    };

    return (
        <>
            <TechnicianCapacityAlertContext.Provider value={ctx}>
                {children}
            </TechnicianCapacityAlertContext.Provider>
            <WarningConfirmationPopup
                showCloseBtn={false}
                open={openPopup}
                title={t('workshopPlanner.technicianCapacityAlert.title')}
                body={
                    <Trans
                        i18nKey={'workshopPlanner.technicianCapacityAlert.body'}
                        values={{
                            capacity:
                                technicianCapacity?.technicianCapacityMetrics
                                    .technicalProductivityUsed ?? 0,
                            remain: 100 - maximumTechnicianCapacity,
                        }}
                        components={{
                            1: <AlertBody />,
                            2: <AlertBodySpan />,
                        }}
                    />
                }
                cancel={t('workshopPlanner.technicianCapacityAlert.cancel')}
                confirm={t('workshopPlanner.technicianCapacityAlert.confirm')}
                onConfirm={() => {
                    confirmCallback?.();
                    closePopup();
                }}
                onClose={closePopup}
                onCancel={closePopup}
                classes={{ title: styles.title, content: styles.content }}
            />
        </>
    );
}

const AlertBody = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: 8,
});

const AlertBodySpan = styled('span')({
    display: 'inline-block',

    fontSize: '14px',
    lineHeight: '22px',
});
