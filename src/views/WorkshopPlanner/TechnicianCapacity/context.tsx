import { createContext, useContext, useMemo } from 'react';
import {
    TechnicianCapacityMetrics,
    TechnicianHours,
    useTechnicianCapacityMetrics,
    useTechniciansHours,
} from './helpers';

type TechnicianCapacityData = {
    techniciansHours: TechnicianHours[];
    technicianCapacityMetrics: TechnicianCapacityMetrics;
};

const TechnicianCapacityContext = createContext<TechnicianCapacityData | null>(null);

export function useTechnicianCapacityContext() {
    return useContext(TechnicianCapacityContext);
}

export function TechnicianCapacityProvider({ children }: React.PropsWithChildren<{}>) {
    const techniciansHours = useTechniciansHours();
    const technicianCapacityMetrics = useTechnicianCapacityMetrics(techniciansHours);

    const ctx = useMemo(() => {
        return { techniciansHours, technicianCapacityMetrics };
    }, [techniciansHours, technicianCapacityMetrics]);

    return (
        <TechnicianCapacityContext.Provider value={ctx}>
            {children}
        </TechnicianCapacityContext.Provider>
    );
}
