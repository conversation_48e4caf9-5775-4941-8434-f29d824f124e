import { useQuery } from '@tanstack/react-query';
import { getScheduleForDate, UserScheduleDto } from 'api/common';
import { AbsenceDto, UserListItem } from 'api/users';
import { TimeSpan } from 'api/utils/format';
import WpApi from 'api/workshopPlanner';
import { JobTitle } from 'common/constants';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectDateString, selectUsersWithBlocksByJob } from 'store/slices/wp/plannings';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import { useTechnicianCapacityContext } from './context';

export type TechnicianCapacityMetrics = {
    techniciansProductivity: number;
    technicalProductivityUsed: number;
    availableActualTechnicalCapacityHours: number;
    actualTechnicalCapacity: number;
    averageAvailableHoursPerTechnician: number;
    assignedHoursForPeriod: number;
    prevWeekTechniciansProductivity: number;
};

export type TechnicianHours = {
    id: string;
    name: string;
    job: JobTitle;
    totalAvailableHours: number;
    workedHours: number;
    assignedHours: number;
    productiveHours: number;
};

export type NewHours = {
    hours: number;
    userId: string;
};

const selectTechnicianCapacitySettings = createSelector(
    [selectSettings, selectShopWpSettings],
    (settings, WPsettings) => ({
        calcCapacity:
            settings.repairShopSettings?.features.showTechnicianCapacity ||
            WPsettings?.showWorkshopUtilizationMetric ||
            false,
        completedJobsType: WPsettings?.completedJobsType ?? 'ActualTimeSpent',
    })
);

export function useTechniciansHours(): TechnicianHours[] {
    const { calcCapacity, completedJobsType } = useAppSelector(selectTechnicianCapacitySettings);
    const [completedJobs, setCompletedJobs] = useState(-1);

    const usersWithBlocks = useAppSelector((r) => selectUsersWithBlocksByJob(r, 'Technician'));
    const dateString = useAppSelector(selectDateString);

    const date = useMemo(
        () =>
            DateTime.fromISO(dateString, { zone: 'local' }).set({
                millisecond: 0,
                second: 0,
                minute: 0,
                hour: 0,
            }),
        [dateString]
    );

    // NOTE (AK) if user is on page and enough time is passed
    // check if there are some new completed jobs
    // for recalculating technicians hours
    useEffect(() => {
        if (calcCapacity && completedJobsType === 'ScheduledTime') {
            const updateFn = () => {
                const now = moment();
                const completedJobs = usersWithBlocks.reduce(
                    (allCompletedBlocks, ub) =>
                        ub.blocks.reduce(
                            (userCompletedBlocks, b) =>
                                moment(b.startsAt + b.duration * 60000).isBefore(now)
                                    ? userCompletedBlocks + 1
                                    : userCompletedBlocks,
                            allCompletedBlocks
                        ),
                    0
                );
                setCompletedJobs(completedJobs);
            };

            const interval = setInterval(updateFn, 60000);
            return () => clearInterval(interval);
        }
    }, [calcCapacity, completedJobsType, usersWithBlocks]);

    const techniciansHours = useMemo(() => {
        if (!calcCapacity) {
            return [];
        }

        const secondsPerHour = 3600;
        const minutesPerHour = 60;

        const getSeconds = (to: string, from: string) => {
            return TimeSpan.fromString(to).totalSeconds - TimeSpan.fromString(from).totalSeconds;
        };

        const getWorkedHours = (schedule: UserScheduleDto.DaySchedule) =>
            schedule.windows.reduce(
                (allSeconds, window) => allSeconds + getSeconds(window.to, window.from),
                0
            ) / secondsPerHour;
        const getWorkedHoursExcludingAbsences = (
            schedule: UserScheduleDto.DaySchedule,
            absences: AbsenceDto[]
        ) => {
            if (absences.some((x) => x.roundedToDays)) {
                return 0;
            }

            let workedSeconds = 0;

            for (const window of schedule.windows) {
                const workStartsSeconds = TimeSpan.fromString(window.from).totalSeconds;
                const workEndsSeconds = TimeSpan.fromString(window.to).totalSeconds;

                workedSeconds += workEndsSeconds - workStartsSeconds;

                for (const absence of absences) {
                    const absenceStartsDate = DateTime.fromISO(absence.startsAt);
                    const absenceEndsDate = DateTime.fromISO(absence.endsAt);

                    const absenceStartsSeconds = TimeSpan.fromParts(
                        absenceStartsDate.hour,
                        absenceStartsDate.minute,
                        absenceStartsDate.second
                    ).totalSeconds;
                    const absenceEndsSeconds = TimeSpan.fromParts(
                        absenceEndsDate.hour,
                        absenceEndsDate.minute,
                        absenceEndsDate.second
                    ).totalSeconds;

                    if (
                        absenceStartsSeconds < workEndsSeconds &&
                        absenceEndsSeconds > workStartsSeconds
                    ) {
                        const intersectionStartsSeconds =
                            absenceStartsSeconds > workStartsSeconds
                                ? absenceStartsSeconds
                                : workStartsSeconds;
                        const intersectionEndsSeconds =
                            absenceEndsSeconds < workEndsSeconds
                                ? absenceEndsSeconds
                                : workEndsSeconds;

                        workedSeconds -= intersectionEndsSeconds - intersectionStartsSeconds;
                    }
                }
            }

            return workedSeconds / secondsPerHour;
        };

        const now = moment();
        const techniciansHours = usersWithBlocks.map((ub) => {
            const schedule = getScheduleForDate(ub.user.schedule, date);
            return {
                id: ub.user.key,
                name: ub.user.name,
                job: ub.user.job as JobTitle,
                totalAvailableHours: getWorkedHours(schedule),
                assignedHours:
                    ub.blocks.reduce((allSeconds, block) => allSeconds + block.duration, 0) /
                    minutesPerHour,
                productiveHours:
                    completedJobs === 0
                        ? 0
                        : ub.blocks.reduce((allDuration, block) => {
                              let blockDuration = 0;

                              if (completedJobsType === 'ActualTimeSpent') {
                                  blockDuration = block.accumulatedDurationInSeconds ?? 0;
                              } else {
                                  blockDuration = moment(
                                      block.startsAt + block.duration * 60000
                                  ).isBefore(now)
                                      ? block.duration
                                      : 0;
                              }

                              return allDuration + blockDuration;
                          }, 0) /
                          (completedJobsType === 'ActualTimeSpent'
                              ? secondsPerHour
                              : minutesPerHour),
                workedHours: getWorkedHoursExcludingAbsences(schedule, ub.absences),
            };
        });

        return techniciansHours;
    }, [calcCapacity, usersWithBlocks, date, completedJobsType, completedJobs]);

    return techniciansHours;
}

// Consider optimizing or splitting calculations
export function useTechnicianCapacityMetrics(
    techniciansHours: TechnicianHours[]
): TechnicianCapacityMetrics {
    const { calcCapacity } = useAppSelector(selectTechnicianCapacitySettings);
    const dateString = useAppSelector(selectDateString);
    const date = useMemo(
        () =>
            DateTime.fromISO(dateString, { zone: 'local' }).set({
                millisecond: 0,
                second: 0,
                minute: 0,
                hour: 0,
            }),
        [dateString]
    );

    const usersWithBlocks = useAppSelector((r) => selectUsersWithBlocksByJob(r, 'Technician'));
    const users = useMemo(() => usersWithBlocks.map((ub) => ub.user), [usersWithBlocks]);

    const techniciansProductivitySum = techniciansHours.reduce(
        (techniciansProductivitySum, technicianHours) =>
            techniciansProductivitySum +
            (technicianHours.workedHours
                ? technicianHours.productiveHours / technicianHours.workedHours
                : 0),
        0
    );

    // Actual Technical Capacity
    const techniciansProductivity = techniciansHours.length
        ? Math.round((techniciansProductivitySum / techniciansHours.length) * 100)
        : 0;

    const prevWeekStart = useMemo(() => date.minus({ days: 7 }).startOf('day'), [date]);
    const prevWeekEnd = useMemo(() => date.minus({ days: 1 }).endOf('day'), [date]);
    const userIds = useMemo(() => users.map((user) => user.key), [users]);

    const { data: prevWeekUserBlocks } = useQuery(
        ['prevWeekSchedule', prevWeekStart, prevWeekEnd],
        () => WpApi.getSchedule(prevWeekStart.toISO(), prevWeekEnd.toISO(), userIds),
        {
            cacheTime: Infinity,
            enabled: calcCapacity && !!prevWeekStart && !!prevWeekEnd,
        }
    );

    return useMemo(() => {
        if (!calcCapacity) {
            return {
                techniciansProductivity: 0,
                technicalProductivityUsed: 0,
                availableActualTechnicalCapacityHours: 0,
                actualTechnicalCapacity: 0,
                averageAvailableHoursPerTechnician: 0,
                assignedHoursForPeriod: 0,
                prevWeekTechniciansProductivity: 0,
            };
        }

        if (!prevWeekUserBlocks) {
            return {
                techniciansProductivity,
                technicalProductivityUsed: 0,
                availableActualTechnicalCapacityHours: 0,
                actualTechnicalCapacity: 0,
                averageAvailableHoursPerTechnician: 0,
                assignedHoursForPeriod: 0,
                prevWeekTechniciansProductivity: 0,
            };
        }

        const prevWeekTechniciansProductivity = calcPrevWeekTechniciansProductivity(
            prevWeekStart,
            users,
            prevWeekUserBlocks
        );

        const workDays = 1;
        const totalTechnicians = techniciansHours.length;
        const totalAvailableHours = techniciansHours.reduce(
            (sum, tech) => sum + tech.totalAvailableHours,
            0
        );
        const averageAvailableHoursPerTechnician = totalTechnicians
            ? totalAvailableHours / totalTechnicians
            : 0;
        const assignedHoursForPeriod = techniciansHours.reduce(
            (sum, tech) => sum + tech.assignedHours,
            0
        );

        // Actual Technical Capacity used =
        // (Assigned hours of the period x 100) / (Technicians x Average available hours per Technician x Technical Productivity of the previous week)
        const technicalProductivityUsed =
            totalTechnicians &&
            averageAvailableHoursPerTechnician &&
            prevWeekTechniciansProductivity
                ? Math.round(
                      (assignedHoursForPeriod * 100) /
                          (totalTechnicians *
                              averageAvailableHoursPerTechnician *
                              (prevWeekTechniciansProductivity / 100))
                  )
                : 0;

        // Available Actual Technical Capacity =
        // Technicians x Average available hours per Technician x Work days x Technical Productivity of the previous week - Assigned hours for the period
        const availableActualTechnicalCapacityHours = Math.round(
            (totalTechnicians *
                averageAvailableHoursPerTechnician *
                workDays *
                prevWeekTechniciansProductivity) /
                100 -
                assignedHoursForPeriod
        );

        // Actual Technical Capacity =
        // Total technicians x Available hours x Work days x Technical productivity (percentage)
        const actualTechnicalCapacity = Math.round(
            (totalTechnicians *
                averageAvailableHoursPerTechnician *
                workDays *
                techniciansProductivity) /
                100
        );

        return {
            techniciansProductivity,
            technicalProductivityUsed,
            availableActualTechnicalCapacityHours,
            actualTechnicalCapacity,
            averageAvailableHoursPerTechnician,
            assignedHoursForPeriod,
            prevWeekTechniciansProductivity,
        };
    }, [
        calcCapacity,
        prevWeekUserBlocks,
        prevWeekStart,
        users,
        techniciansHours,
        techniciansProductivity,
    ]);
}

export function useIsSchedulingAllowed(newHours?: NewHours) {
    const technicianCapacity = useTechnicianCapacityContext();

    const {
        showTechnicianCapacity,
        maximumTechnicianCapacity,
        allowSchedulingWithMaximumCapacity,
    } = useAppSelector(selectSettings).repairShopSettings?.features ?? {
        showTechnicianCapacity: false,
        maximumTechnicianCapacity: 80,
        allowSchedulingWithMaximumCapacity: false,
    };

    return (
        !technicianCapacity ||
        !showTechnicianCapacity ||
        allowSchedulingWithMaximumCapacity ||
        (technicianCapacity.technicianCapacityMetrics.technicalProductivityUsed <
            maximumTechnicianCapacity &&
            (!newHours ||
                canAddNewHours(
                    technicianCapacity.techniciansHours,
                    maximumTechnicianCapacity,
                    newHours,
                    technicianCapacity.technicianCapacityMetrics.prevWeekTechniciansProductivity,
                    technicianCapacity.technicianCapacityMetrics.averageAvailableHoursPerTechnician
                )))
    );
}

export function canAddNewHours(
    techniciansHours: TechnicianHours[],
    maximumTechnicianCapacity: number,
    { hours, userId }: NewHours,
    prevWeekTechniciansProductivity: number,
    averageAvailableHoursPerTechnician: number
) {
    if (techniciansHours.every((x) => x.id !== userId)) {
        return true;
    }

    const totalTechnicians = techniciansHours.length;

    const assignedHoursForPeriod = techniciansHours.reduce(
        (sum, tech) => sum + tech.assignedHours,
        0
    );

    const technicalProductivityUsed =
        totalTechnicians && averageAvailableHoursPerTechnician && prevWeekTechniciansProductivity
            ? Math.round(
                  (assignedHoursForPeriod + hours * 100) /
                      (totalTechnicians *
                          averageAvailableHoursPerTechnician *
                          prevWeekTechniciansProductivity)
              )
            : 0;

    return technicalProductivityUsed < maximumTechnicianCapacity;
}

function calcPrevWeekTechniciansProductivity(
    prevWeekStart: DateTime,
    users: UserListItem[],
    prevWeekUserBlocks: WpApi.PlanningScheduleDto
): number {
    const secondsPerHour = 3600;

    const getHours = (to: string, from: string) => {
        return TimeSpan.fromString(to).totalSeconds - TimeSpan.fromString(from).totalSeconds;
    };

    const getWorkedHours = (schedule: UserScheduleDto.DaySchedule) =>
        schedule.windows.reduce(
            (allSeconds, window) => allSeconds + getHours(window.to, window.from),
            0
        ) / secondsPerHour;

    const prevWeekDates = Array.from({ length: 7 }, (_, i) => prevWeekStart.plus({ days: i }));

    // 1. Calculate the productivity of each technician for each day of the previous week
    const dailyProductivityPerTechnician = prevWeekDates.map((date) =>
        users.map((user) => {
            const schedule = getScheduleForDate(user.schedule, date);
            const workedHours = getWorkedHours(schedule);

            const userBlocks =
                prevWeekUserBlocks?.schedules.find((s) => s.userId === user.key)?.blocks ?? [];

            const productiveHours =
                userBlocks
                    .filter((block) => DateTime.fromSeconds(block.startsAt).hasSame(date, 'day'))
                    .reduce((sum, block) => sum + (block.accumulatedDurationInSeconds ?? 0), 0) /
                3600;

            // Technician's daily productivity
            return workedHours ? Math.round(productiveHours / workedHours) : 0;
        })
    );

    // 2. Calculate the average productivity of all technicians for each day
    const dailyAvgProductivity = dailyProductivityPerTechnician.map((dayProductivity) => {
        const total = dayProductivity.reduce((acc, val) => acc + val, 0);
        return dayProductivity.length ? total / dayProductivity.length : 0;
    });

    // 3. Calculate the weekly average productivity
    const weeklyProductivity =
        dailyAvgProductivity.length > 0
            ? (dailyAvgProductivity.reduce((acc, val) => acc + val, 0) /
                  dailyAvgProductivity.length) *
              100
            : 0;

    return weeklyProductivity;
}
