import { Fade, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { StyledTooltip } from 'views/WorkshopPlanner/common/TooltipComponents';
import Indicator from '../Indicator';
import { useTechnicianCapacityContext } from '../context';
import WorkshopUtilizationIndicatorTooltip from './WorkshopUtilizationIndicatorTooltip';

type WorkshopUtilizationIndicatorProps = {
    productiveWorkspace: number;
};

export default function WorkshopUtilizationIndicator({
    productiveWorkspace,
}: WorkshopUtilizationIndicatorProps) {
    const { t } = useAppTranslation();

    const technicianCapacity = useTechnicianCapacityContext();

    // using in Potential Capacity formula
    // now it is just 1, but in future may be something else
    const workDays = 1;

    const potentialCapacity = technicianCapacity
        ? productiveWorkspace *
          technicianCapacity.technicianCapacityMetrics.averageAvailableHoursPerTechnician *
          workDays
        : 0;

    const workshopUtilization =
        technicianCapacity && potentialCapacity
            ? (technicianCapacity.technicianCapacityMetrics.actualTechnicalCapacity /
                  potentialCapacity) *
              100
            : 0;

    return (
        <>
            {technicianCapacity && (
                <StyledTooltip
                    arrow
                    placement="bottom-start"
                    title={
                        <WorkshopUtilizationIndicatorTooltip
                            potentialCapacity={potentialCapacity}
                            productiveWorkspaces={productiveWorkspace}
                            averageAvailableHoursPerTechnician={
                                technicianCapacity.technicianCapacityMetrics
                                    .averageAvailableHoursPerTechnician
                            }
                            workshopUtilization={workshopUtilization}
                            actualTechnicalCapacity={
                                technicianCapacity.technicianCapacityMetrics.actualTechnicalCapacity
                            }
                        />
                    }
                    TransitionComponent={Fade}
                    TransitionProps={{ timeout: 100 }}
                >
                    <DivRoot>
                        <Indicator
                            title={t('workshopPlanner.indicators.utilization')}
                            value={Math.round(workshopUtilization)}
                        />
                    </DivRoot>
                </StyledTooltip>
            )}
        </>
    );
}

const DivRoot = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    gap: 2,

    position: 'relative',
    minHeight: 33,
    paddingLeft: 16,
    marginLeft: 16,

    '::before': {
        content: '""',
        display: 'block',
        width: 1,
        backgroundColor: 'var(--neutral4)',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
    },
});
