import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppSelector } from 'store';
import { selectedDate } from 'store/slices/wp/plannings';

type WorkshopUtilizationIndicatorTooltipProps = {
    potentialCapacity: number;
    productiveWorkspaces: number;
    averageAvailableHoursPerTechnician: number;
    workshopUtilization: number;
    actualTechnicalCapacity: number;
};

export default function WorkshopUtilizationIndicatorTooltip({
    potentialCapacity,
    productiveWorkspaces,
    averageAvailableHoursPerTechnician,
    workshopUtilization,
    actualTechnicalCapacity,
}: WorkshopUtilizationIndicatorTooltipProps) {
    const { t } = useAppTranslation();
    const date = useAppSelector(selectedDate);
    const dateString = date.toFormat(t('dateFormats.short'));
    const zeroRegExp = /.00|0$/;

    // using in Potential Capacity formula
    // now it is just 1, but in future may be something else
    const workDays = 1;

    return (
        <DivRoot>
            <Section>
                <DivTitle>{t('workshopPlanner.workshopUtilizationTooltip.inputData')}</DivTitle>
                <div>
                    <div>{`${t(
                        'workshopPlanner.workshopUtilizationTooltip.period'
                    )}: ${dateString}`}</div>
                    <div>{`${t(
                        'workshopPlanner.workshopUtilizationTooltip.workDays'
                    )}: ${workDays}`}</div>
                </div>
            </Section>
            <Section>
                <DivTitle>
                    {t('workshopPlanner.workshopUtilizationTooltip.potentialCapacityTitle', {
                        hours: potentialCapacity.toFixed(2).replace(zeroRegExp, ''),
                    })}
                </DivTitle>
                <Formula>
                    {`${t('workshopPlanner.workshopUtilizationTooltip.potentialCapacity')} = ${t(
                        'workshopPlanner.workshopUtilizationTooltip.productiveWorkspaces'
                    )} x ${t(
                        'workshopPlanner.workshopUtilizationTooltip.averageAvailableHours'
                    )} x ${t('workshopPlanner.workshopUtilizationTooltip.workDays')}`}
                </Formula>
                <StyledList>
                    <li>
                        {`${t(
                            'workshopPlanner.workshopUtilizationTooltip.productiveWorkspaces'
                        )}: ${productiveWorkspaces}`}
                    </li>
                    <li>
                        {`${t(
                            'workshopPlanner.workshopUtilizationTooltip.averageAvailableHours'
                        )}: ${averageAvailableHoursPerTechnician
                            .toFixed(2)
                            .replace(zeroRegExp, '')} ${t(
                            'workshopPlanner.workshopUtilizationTooltip.hours'
                        )}`}
                    </li>
                </StyledList>
            </Section>
            <Section>
                <DivTitle>
                    {`${t(
                        'workshopPlanner.workshopUtilizationTooltip.workshopUtilization'
                    )}: ${Math.round(workshopUtilization)}%`}
                </DivTitle>
                <Formula>
                    {`${t('workshopPlanner.workshopUtilizationTooltip.workshopUtilization')} = ${t(
                        'workshopPlanner.workshopUtilizationTooltip.actualTechnicalCapacity'
                    )} / ${t(
                        'workshopPlanner.workshopUtilizationTooltip.potentialCapacity'
                    )} x 100`}
                </Formula>
                <StyledList>
                    <li>
                        {`${t(
                            'workshopPlanner.workshopUtilizationTooltip.actualTechnicalCapacity'
                        )}: ${actualTechnicalCapacity} ${t(
                            'workshopPlanner.workshopUtilizationTooltip.hours'
                        )}`}
                    </li>
                    <li>
                        {`${t(
                            'workshopPlanner.workshopUtilizationTooltip.potentialCapacity'
                        )}: ${potentialCapacity.toFixed(2).replace(zeroRegExp, '')} ${t(
                            'workshopPlanner.workshopUtilizationTooltip.hours'
                        )}`}
                    </li>
                </StyledList>
            </Section>
        </DivRoot>
    );
}

const DivRoot = styled('div')(({ theme }) => ({
    padding: '19px 10px 19px 25px',
    maxWidth: '600px',
    maxHeight: 'calc(100vh - 70px)',
    overflowY: 'auto',
    boxSizing: 'border-box',

    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 22,

    ...theme.typography.h5Inter,
    fontWeight: 300,
    lineHeight: '22px',
    color: theme.palette.neutral[1],
}));

const Section = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
});

const DivTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontSize: 16,
}));

const Formula = styled('div')({
    fontStyle: 'italic',
});

const StyledList = styled('ul')({
    margin: 0,
    paddingLeft: 25,
});
