import { styled } from '@mui/material';
import { useMemo } from 'react';
import { parseRgb, toHSL } from 'utils/colors';

export default function LegendItem({
    children,
    color,
}: React.PropsWithChildren<{ color: string }>) {
    const requiresBorder = useMemo(() => {
        try {
            const hsl = toHSL(parseRgb(color));
            return hsl.l > 90;
        } catch {
            return false;
        }
    }, [color]);

    return (
        <DivRoot>
            <span
                style={{
                    backgroundColor: color,
                    height: 3,
                    width: 16,
                    display: 'flex',
                    border: requiresBorder ? '1px solid var(--neutral4)' : undefined,
                    boxSizing: 'content-box',
                    marginLeft: requiresBorder ? 0 : 1,
                }}
            />
            <span>{children}</span>
        </DivRoot>
    );
}

const DivRoot = styled('div')(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: '18px 1fr',
    columnGap: '6px',
    alignItems: 'center',
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));
