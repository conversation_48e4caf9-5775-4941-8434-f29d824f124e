import { styled } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { createSelector } from 'reselect';
import { RootState, useAppSelector } from 'store';
import { selectCustomAppointmentReasons } from 'store/slices/appointmentReasons';
import { selectColorFieldsInSection } from 'store/slices/colorFields/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectOrderTypes } from 'store/slices/orderTypes';
import { selectUsers } from 'store/slices/users';
import { selectPlanningUsers } from 'store/slices/wp/plannings';
import LegendItem from './LegendItem';

const maxDisplayedItems = 3;

export default function TechniciansLegend() {
    const { t } = useAppTranslation();

    const { isLoading: colorFieldsIsLoading } = useAppSelector((state) =>
        selectColorFieldsInSection(state, 'WebJobInfo')
    );

    const { allColors, displayedColors, customFieldColors, colorType } = useAppSelector((state) =>
        selectColors(state)
    );

    if (
        colorFieldsIsLoading ||
        (!allColors && !customFieldColors) ||
        (allColors.length === 0 && customFieldColors.length === 0)
    )
        return null;

    return (
        <DivRoot>
            <ArrowTooltip
                position="top"
                content={
                    <DivTooltipRoot>
                        <TooltipHeader>
                            {t(`workshopPlanner.technicians.legend.${colorType}`)}
                        </TooltipHeader>

                        <OverlayScrollbarsComponent style={{ maxHeight: 200 }}>
                            <DivLegendTooltipList>
                                {allColors.map((item, i) => (
                                    <LegendItem
                                        key={`${i}_${item.color}_${item.title}`}
                                        color={item.color}
                                    >
                                        {item.title}
                                    </LegendItem>
                                ))}
                            </DivLegendTooltipList>
                        </OverlayScrollbarsComponent>
                    </DivTooltipRoot>
                }
            >
                <DivLegend>
                    {displayedColors.map((item, i) => (
                        <DivLegendItemContainer>
                            <LegendItem key={`${i}_${item.color}_${item.title}`} color={item.color}>
                                {item.title}
                            </LegendItem>
                        </DivLegendItemContainer>
                    ))}
                </DivLegend>
            </ArrowTooltip>
            <DivLegend>
                {customFieldColors.map((item, i) => (
                    <DivLegendItemContainer paddingTop={displayedColors.length > 0}>
                        <LegendItem key={`${i}_${item.color}_${item.title}`} color={item.color}>
                            {item.title}
                        </LegendItem>
                    </DivLegendItemContainer>
                ))}
            </DivLegend>
        </DivRoot>
    );
}

type LegendItemData = {
    title: string;
    color: string;
};

const selectColors = createSelector([(s: RootState) => s], (state) => {
    const data: LegendItemData[] = [];
    const customFieldColors: LegendItemData[] = [];

    const { colorField } = selectColorFieldsInSection(state, 'WebJobInfo');

    if (colorField?.options) {
        for (const option of colorField.options) {
            customFieldColors.push({
                title: option.name,
                color: option.color,
            });
        }
    }

    const colorType = selectSettings(state).repairShopSettings?.showJobBlockColor;
    if (!colorType || colorType === 'NoColor')
        return {
            colorType,
            allColors: [],
            displayedColors: [],
            customFieldColors,
        };

    switch (colorType) {
        case 'ByOrderType':
            const orderTypes = selectOrderTypes(state);
            for (const orderType of orderTypes) {
                if (orderType.color) {
                    data.push({
                        title: orderType.name,
                        color: orderType.color,
                    });
                }
            }
            break;
        case 'ByServiceAdvisor':
            const users = selectUsers(state);
            const userIds = selectPlanningUsers(state);

            for (const userId of userIds) {
                const user = users[userId];
                if (!user || !user.color) continue;
                data.push({
                    title: user.name,
                    color: user.color,
                });
            }
            break;
        case 'ByReasonForAppointment':
            const reasons = selectCustomAppointmentReasons(state);
            for (const reason of reasons) {
                if (reason.color) {
                    data.push({
                        color: reason.color,
                        title: reason.name,
                    });
                }
            }
    }

    return {
        allColors: data,
        displayedColors: data.length > maxDisplayedItems ? data.slice(0, maxDisplayedItems) : data,
        customFieldColors,
        colorType,
    };
});

const DivRoot = styled('div')({
    maxWidth: '70%',
    display: 'table',
    margin: '0 0 12px 12px',
});

const DivLegend = styled('div')({
    display: 'table-row',
});

const DivLegendItemContainer = styled('div', {
    shouldForwardProp: (prop) => prop !== 'paddingTop',
})<{ paddingTop?: boolean }>(({ paddingTop = false }) => ({
    display: 'table-cell',
    paddingRight: '10px',
    paddingTop: paddingTop ? '10px' : '0',
}));

const DivTooltipRoot = styled('div')({
    minWidth: 180,
    maxWidth: 400,
    padding: '0 0 5px 10px',
});

const DivLegendTooltipList = styled('div')({
    '& > *': {
        marginBottom: '5px',
    },
});

const TooltipHeader = styled('header')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    marginBottom: 8,
    marginTop: 8,
}));
