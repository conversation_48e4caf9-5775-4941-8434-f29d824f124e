import React, { createContext, useCallback, useContext, useMemo, useState } from 'react';
import WpAdvisorsConvertOrderModal from '.';
import { useOrderPopup } from '../OrderPopup/context';

interface IConvertOrderModalContext {
    open(appointmentId: string): void;
}

const ConvertOrderModalContext = createContext<IConvertOrderModalContext | null>(null);

export function useConvertAppointmentModalOptional() {
    return useContext(ConvertOrderModalContext);
}

export function ConvertAppointmentModalProvider({ children }: React.PropsWithChildren<{}>) {
    const [appointmentId, setAppointmentId] = useState('');
    const ctx = useMemo(() => ({ open: setAppointmentId }), []);
    const orderPopup = useOrderPopup();

    const onAppointmentConverted = useCallback(
        async (orderNumber: string) => {
            orderPopup.open(orderNumber);
            setAppointmentId('');
        },
        [orderPopup]
    );

    return (
        <>
            <WpAdvisorsConvertOrderModal
                open={!!appointmentId}
                appointmentId={appointmentId}
                onClose={() => setAppointmentId('')}
                onAppointmentConverted={onAppointmentConverted}
            />
            <ConvertOrderModalContext.Provider value={ctx}>
                {children}
            </ConvertOrderModalContext.Provider>
        </>
    );
}
