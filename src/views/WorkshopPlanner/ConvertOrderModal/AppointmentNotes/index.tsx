import { styled, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { useMutation } from '@tanstack/react-query';
import AppointmentNotesApi, { AppointmentNoteDto, AppointmentNoteType } from 'api/appointmentNotes';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import AppointmentNoteForm from 'views/Appointments/common/AppointmentNotesForm';
import NoteEditor from 'views/Appointments/common/AppointmentNotesForm/NoteEditor';

type AppointmentNoteFormProps = {
    appointmentId: string;
    notesType: AppointmentNoteType;
    disabled: boolean;
    notes?: AppointmentNoteDto[];
    onChange?: (notes: AppointmentNoteDto[]) => void;
};

const AppointmentNotes = ({
    notes = [],
    appointmentId,
    notesType,
    disabled,
    onChange,
}: AppointmentNoteFormProps) => {
    const { t } = useAppTranslation();
    const [noteId, setNoteId] = useState<string>();

    const { addNoteMutate } = useAddNoteMutation((data) => {
        onChange && onChange([...notes, data]);
    });

    const { updateNoteMutate } = useUpdateNoteMutation((data) => {
        onChange && onChange(notes.map((x) => (x.id === data.id ? data : x)));
    });

    const { deleteNoteMutate } = useDeleteNoteMutation(() => {
        onChange && onChange(notes.filter((x) => x.id !== noteId));
        setNoteId(undefined);
    });

    const handleSave = (note: string) => {
        addNoteMutate({ note, appointmentId, noteType: notesType });
    };

    const handleEdit = (note: AppointmentNoteDto) => {
        if (appointmentId) {
            updateNoteMutate({
                note: note.note,
                appointmentNoteId: note.id,
            });
        } else {
            const index = notes.findIndex((n) => n.id === note.id);
            if (index >= 0) {
                onChange &&
                    onChange(notes.map((x) => (x.id === note.id ? { ...x, note: note.note } : x)));
            }
        }
    };

    const handleDelete = (note: AppointmentNoteDto) => {
        if (appointmentId) {
            setNoteId(note.id);
            deleteNoteMutate({ appointmentNoteId: note.id });
        } else {
            onChange && onChange(notes.filter((x) => x.id !== note.id));
        }
    };

    const filteredNotes = notes.filter((x) => x.type === notesType);

    return (
        <Box component="div">
            <TypographyLabel>
                {notesType === 'ForCustomer'
                    ? t('appointments.notesVisibleForCustomer')
                    : t('appointments.internalNotes')}
            </TypographyLabel>
            {filteredNotes.map((note, index) => (
                <BoxNoteEditorWrapper key={`${note.id}-${index}`} component="div">
                    •
                    <NoteEditor
                        appointmentNote={note}
                        editSection="WorkshopPlanner"
                        disabled={disabled}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                </BoxNoteEditorWrapper>
            ))}
            <BoxAppointmentNoteFormWrapper>
                <AppointmentNoteForm onClickSave={handleSave} disabled={disabled} />
            </BoxAppointmentNoteFormWrapper>
        </Box>
    );
};

const useAddNoteMutation = (onSuccess?: (data: AppointmentNoteDto) => void) => {
    const { mutate: addNoteMutate } = useMutation(
        (body: { note: string; appointmentId: string; noteType: AppointmentNoteType }) =>
            AppointmentNotesApi.addNote(
                body.appointmentId,
                body.note,
                body.noteType,
                'WorkshopPlanner'
            ),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { addNoteMutate };
};

const useUpdateNoteMutation = (onSuccess?: (data: AppointmentNoteDto) => void) => {
    const { mutate: updateNoteMutate } = useMutation(
        (body: { note: string; appointmentNoteId: string }) =>
            AppointmentNotesApi.updateNote(body.appointmentNoteId, body.note),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { updateNoteMutate };
};

const useDeleteNoteMutation = (onSuccess?: () => void) => {
    const { mutate: deleteNoteMutate } = useMutation(
        (body: { appointmentNoteId: string }) =>
            AppointmentNotesApi.deleteNote(body.appointmentNoteId),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );
    return { deleteNoteMutate };
};

const TypographyLabel = styled(Typography)(({ theme }) => ({
    fontFamily: 'Inter',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: 700,
    lineHeight: 'normal',
    color: theme.palette.neutral[8],
    marginBottom: '5px',
}));

const BoxNoteEditorWrapper = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    paddingLeft: '8px',
});

const BoxAppointmentNoteFormWrapper = styled(Box)({
    paddingTop: '3px',
});

export default AppointmentNotes;
