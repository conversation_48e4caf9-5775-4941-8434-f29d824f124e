import { Box, Divider, Grid, IconButton, Skeleton, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import AppointmentNotesApi, { AppointmentNoteDto } from 'api/appointmentNotes';
import AppointmentsApi from 'api/appointments';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { phoneFormatRegexMask } from 'common/FormatersHelper';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { PollsIcon } from 'common/components/Icons/PollsIcon';
import { TextField } from 'common/components/Inputs';
import MaskedTextFormField from 'common/components/Inputs/MaskedTextFormField';
import { Modal } from 'common/components/Modal';
import SimpleTabsWithContent from 'common/components/tabs/SimpleTabsWithContent';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { textOverFlow } from 'common/styles/TextHelpers';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { hasSubCode, isErrorResponse } from 'services/Server';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import BrandPicker from 'views/Components/BrandPicker';
import ModelPicker from 'views/Components/ModelPicker';
import YearPicker from 'views/Components/YearPicker';
import { NewOrderDataPopup } from 'views/Orders/NewOrderPopup';
import { NewOrderData } from 'views/Orders/NewOrderPopup/NewOrderDataPopup';
import { useTechnicianCapacityAlert } from '../TechnicianCapacity/TechnicianCapacityAlert';
import { useIsSchedulingAllowed } from '../TechnicianCapacity/helpers';
import { useScheduleAppointmentAltPopup } from '../schedule-popups/ScheduleAppointmentWorkAltPopup/context';
import AppointmentNotes from './AppointmentNotes';
import AppointmentScheduleTimeline from './AppointmentScheduleTimeline';
import { useCreateOrderMutation } from './helper';

export type WpAdvisorsConvertOrderModalProps = {
    open: boolean;
    appointmentId: string;
    onClose: () => void;
    onAppointmentConverted: (
        orderNumber: string,
        orderId: number | string,
        converted: boolean
    ) => void;
};

export type AppointmentCustomerInfo = {
    customerFirstName: string;
    customerLastName: string;
    customerMobile: string;
    vehicleMake: string;
    vehicleModel: string;
    vehicleYear: string;
    vehiclePlates: string;
    observations: string;
};

const defaultCustomerInfo: () => AppointmentCustomerInfo = () => ({
    customerFirstName: '',
    customerLastName: '',
    customerMobile: '',
    vehicleMake: '',
    vehicleModel: '',
    vehiclePlates: '',
    vehicleYear: '',
    observations: '',
});

const HeaderContainer = styled('header')(({ theme }) => ({
    padding: '25px 70px 0 70px',
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
    position: 'relative',
}));

const HeaderText = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
    marginBottom: 18,
}));

const TabContentContainer = styled('div')({
    padding: '20px 70px 20px 70px',
});

const BoxComponent = styled('div')({
    minWidth: 840,
    minHeight: 460,
    overflow: 'hidden',
});

function ModalSkeleton() {
    return (
        <>
            <Box sx={{ ml: '70px', mr: '70px' }}>
                <Skeleton
                    sx={{
                        mt: 3,
                        mb: 2,
                        height: 40,
                        display: 'block',
                    }}
                />
                <Box sx={{ display: 'flex', gap: 2 }}>
                    <Skeleton width={130} height={20} />
                    <Skeleton width={130} height={20} />
                </Box>
            </Box>
            <Divider sx={{ mt: '4px', mb: 2 }} />
            <Box sx={{ ml: '70px', mr: '70px' }}>
                <Skeleton width={330} sx={{ mb: 1 }} />
                <Skeleton width={210} />
                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        columnGap: 2,
                        mt: 2,
                    }}
                >
                    <Skeleton width="100%" height={32} />
                    <Skeleton width="100%" height={32} />
                    <Skeleton width="100%" height={32} />
                    <Skeleton width="100%" height={32} />
                </Box>
                <Skeleton width="100%" height={100} sx={{ mt: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 4 }}>
                    <Skeleton
                        width={160}
                        height="38px"
                        sx={{ transform: 'none', borderRadius: '100px' }}
                    />
                    <Skeleton
                        width={160}
                        height="38px"
                        sx={{ transform: 'none', borderRadius: '100px' }}
                    />
                </Box>
            </Box>
        </>
    );
}

const WpAdvisorsConvertOrderModal = ({
    open,
    appointmentId,
    onAppointmentConverted,
    onClose,
}: WpAdvisorsConvertOrderModalProps) => {
    const { t } = useAppTranslation();

    const [selectedTab, setSelectedTab] = useState('details');

    const [customerInfo, setCustomerInfo] = useState<AppointmentCustomerInfo>(defaultCustomerInfo);
    const { data: appointment, isLoading } = useAppointmentQuery(appointmentId);

    useEffect(() => {
        if (!open) {
            setSelectedTab('details');
        }
    }, [open]);

    return (
        <>
            <Modal boxComponent={BoxComponent} open={open} onClose={onClose}>
                {isLoading ? (
                    <ModalSkeleton />
                ) : (
                    <SimpleTabsWithContent
                        selected={selectedTab}
                        onTabSelected={setSelectedTab}
                        tabs={[
                            {
                                label: t('workshopPlanner.convertOrder.appointmentDetailTab'),
                                key: 'details',
                                content: (
                                    <AppointmentDetailsTab
                                        onClose={onClose}
                                        appointmentId={appointmentId}
                                        open={open}
                                        onCustomerInfoUpdated={setCustomerInfo}
                                        onAppointmentConverted={onAppointmentConverted}
                                    />
                                ),
                            },
                            {
                                label: t('workshopPlanner.convertOrder.timelineTab'),
                                key: 'timeline',
                                content: <TimelineTab appointmentId={appointmentId} />,
                            },
                        ]}
                        renderLayout={({ tabs, content }) => (
                            <>
                                <HeaderContainer>
                                    <HeaderText>
                                        <span>{`${t('workshopPlanner.convertOrder.appointment')} #${
                                            appointment?.number
                                        } - ${customerInfo?.customerFirstName || ''} ${
                                            customerInfo?.customerLastName || ''
                                        }`}</span>
                                    </HeaderText>
                                    <IconButton
                                        onClick={onClose}
                                        sx={{ position: 'absolute', right: 10, top: 10 }}
                                    >
                                        <CloseIcon />
                                    </IconButton>
                                    {tabs}
                                </HeaderContainer>

                                <TabContentContainer>{content}</TabContentContainer>
                            </>
                        )}
                    />
                )}
            </Modal>
        </>
    );
};

const Caption = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));

const CaptionBold = styled(Caption)({
    fontWeight: 'bold',
});

type AppointmentDetailsTabProps = {
    appointmentId: string;
    open: boolean;
    onClose: () => void;
    onCustomerInfoUpdated: (value: AppointmentCustomerInfo) => void;
} & Pick<WpAdvisorsConvertOrderModalProps, 'onAppointmentConverted'>;

function AppointmentDetailsTab({
    appointmentId,
    open,
    onAppointmentConverted,
    onClose,
    onCustomerInfoUpdated,
}: AppointmentDetailsTabProps) {
    const permissions = useSelector(selectUserPermission);
    const canUpdateCustomer = permissions.allowEditCustomers;
    const canUpdateVehicle = permissions.allowEditVehicles;
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const maxLengthPhone = InternationalizationLogic.maxLengthPhone(internationalization);
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const scheduleAppointmentAltPopup = useScheduleAppointmentAltPopup();
    const tzName = internationalization.ianaTzId;
    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed();

    const [orderNumberPopupOpen, setOrderNumberPopupOpen] = useState(false);
    const [customerInfo, setCustomerInfo] = useState<AppointmentCustomerInfo>(defaultCustomerInfo);

    const { data: appointment } = useAppointmentQuery(appointmentId, open);
    const hasOrder = Boolean(appointment?.order?.number);
    const [appointmentNotes, setAppointmentNotes] = useState<AppointmentNoteDto[]>([]);

    const { data, refetch } = useQuery(['appointmentNotes', appointmentId], () =>
        AppointmentNotesApi.getNotes(appointmentId)
    );

    const handleAppointmentNotesChange = (notes: AppointmentNoteDto[]) => {
        setAppointmentNotes(notes);
        refetch();
    };

    useEffect(() => {
        if (data) {
            setAppointmentNotes(data);
        }
    }, [data]);

    useEffect(() => {
        if (!appointment) {
            return;
        }
        const newCustomerInfo: AppointmentCustomerInfo = {
            customerFirstName: appointment.customer.firstName,
            customerLastName: appointment.customer.lastName,
            customerMobile: appointment.customer.mobile,
            vehicleMake: appointment.vehicle?.brand ?? '',
            vehicleModel: appointment.vehicle?.model ?? '',
            vehiclePlates: appointment.vehicle?.plates ?? '',
            vehicleYear: appointment.vehicle?.year ?? '',
            observations: appointment.observations,
        };
        setCustomerInfo(newCustomerInfo);
        onCustomerInfoUpdated(newCustomerInfo);
    }, [appointment, onCustomerInfoUpdated]);

    const createOrderMutation = useCreateOrderMutation((r) => {
        onAppointmentConverted(r.orderNumber, r.orderId, true);
        setOrderNumberPopupOpen(false);
    });

    const updateAppointmentMutation = useMutation(async () => {
        if (appointment) {
            if (
                customerInfo.customerFirstName === appointment.customer.firstName &&
                customerInfo.customerLastName === appointment.customer.lastName &&
                customerInfo.customerMobile === appointment.customer.mobile &&
                customerInfo.vehicleMake === (appointment.vehicle?.brand ?? '') &&
                customerInfo.vehicleModel === (appointment.vehicle?.model ?? '') &&
                customerInfo.vehiclePlates === (appointment.vehicle?.plates ?? '') &&
                customerInfo.vehicleYear === (appointment.vehicle?.year ?? '') &&
                customerInfo.observations === appointment.observations
            ) {
                // nothing changed
                return;
            }
        }

        const a = await AppointmentsApi.get(appointmentId);
        try {
            await AppointmentsApi.update(appointmentId, {
                declinedItemReasonsForView: a.declinedItemReasonsForView,
                reasons: a.reasons.map((x) => x.id),
                customReasons: a.customReasons || [],
                promisedAt: a.promisedAt,
                startsAt: a.startsAt,
                observations: customerInfo.observations,
                duration: a.durationInMinutes,
                serviceAdvisorId: a.serviceAdvisor.id,
                originId: a.origin?.id ?? null,
                withValetService: a.withValetService,
                customerUpdateData: canUpdateCustomer
                    ? {
                          customer: {
                              ...a.customer,
                              firstName: customerInfo.customerFirstName,
                              lastName: customerInfo.customerLastName,
                              mobile: customerInfo.customerMobile,
                          },
                      }
                    : null,
                vehicleUpdateData: canUpdateVehicle
                    ? {
                          vehicle: a.vehicle
                              ? {
                                    ...a.vehicle,
                                    brand: customerInfo.vehicleMake,
                                    model: customerInfo.vehicleModel,
                                    year: customerInfo.vehicleYear,
                                    plates: customerInfo.vehiclePlates,
                                }
                              : null,
                      }
                    : null,
            });
        } catch (e) {
            if (isErrorResponse(e)) {
                if (hasSubCode(e, 'ValidationError', 'InvalidMobileNumberError')) {
                    toasters.danger(
                        t('appointments.invalidMobileNumberError'),
                        t('toasters.errorOccurred')
                    );
                } else {
                    toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                }
            }
        }
    });

    const onConvertToOrder = async (orderNumber?: string | null) => {
        if (!appointment) return;

        if (appointment.order?.number) {
            onAppointmentConverted(appointment.order.number, appointment.id, false);
        } else {
            if (!repairShopSettings?.features.autoOrderNumber && !orderNumber) {
                setOrderNumberPopupOpen(true);
                return;
            }

            await updateAppointmentMutation.mutateAsync();
            await createOrderMutation.mutateAsync({
                id: appointment.id,
                orderNumber: orderNumber,
            });
        }
    };

    const onOrderData = ({ number }: NewOrderData) => onConvertToOrder(number);

    const handleAddJobClick = () => {
        if (!permissions.allowEditWorkshopPlanner || hasOrder) {
            console.error('cannot open scheduleAppointmentAltPopup', {
                hasOrder,
                userPermission: permissions,
            });
            return;
        }

        let ts: number | undefined = undefined;
        try {
            ts = appointment
                ? DateTime.fromFormat(
                      `${appointment.startsAt.date} ${appointment.startsAt.time}`,
                      'yyyy-MM-dd hh:mm:ss',
                      { zone: tzName }
                  )
                      .set({ hour: 0, minute: 0, second: 0 })
                      .toMillis()
                : undefined;
        } catch (e: unknown) {
            console.error('cannot get timestamp from appointment date', e);
        }

        technicianCapacityAlertPopup.checkTechnicianCapacity(() => {
            scheduleAppointmentAltPopup.open(appointmentId, ts);
            onClose();
        });
    };

    return (
        <>
            <Box sx={{ display: 'flex', gap: 2 }}>
                <Parameter label={`${t('workshopPlanner.convertOrder.appointment')}: `}>
                    {appointment
                        ? moment(
                              `${appointment.startsAt.date} ${appointment.startsAt.time}`
                          ).format('hh:mm a')
                        : '--'}
                </Parameter>
                <Parameter label={`${t('workshopPlanner.convertOrder.serviceAdvisor')}: `}>
                    {appointment?.serviceAdvisor.name}
                </Parameter>
            </Box>
            <Box sx={{ mt: 1 }}>
                <Parameter label={`${t('workshopPlanner.convertOrder.reasonForAppointment')}: `}>
                    {textOverFlow(appointment?.reasons.map((r) => r.name).join(', ') ?? '', 900)}
                </Parameter>
            </Box>
            <Grid container spacing={5}>
                <Grid item xs={6}>
                    <Grid container spacing={1} alignItems="flex-end">
                        <Grid item xs={6}>
                            <TextField
                                cmosVariant="grey"
                                label={t('commonLabels.name')}
                                name="firstName"
                                value={customerInfo?.customerFirstName || ''}
                                isRequired
                                disabled={
                                    !permissions.allowEditWorkshopPlanner ||
                                    !canUpdateCustomer ||
                                    hasOrder
                                }
                                showValidationIndicators
                                onChange={(e) => {
                                    if (!e.target.value) {
                                        toasters.warning(
                                            t('appointments.step3.theNameCannotBeEmpty'),
                                            t('appointments.step3.theClientCouldNotBeEdited')
                                        );
                                    }
                                    setCustomerInfo((old) => ({
                                        ...old,
                                        customerFirstName: e.target.value ?? '',
                                    }));
                                }}
                                placeholder={t('orderDetails.firstNamePlaceholder')}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <TextField
                                cmosVariant="grey"
                                name="lastName"
                                value={customerInfo?.customerLastName || ''}
                                disabled={
                                    !permissions.allowEditWorkshopPlanner ||
                                    !canUpdateCustomer ||
                                    hasOrder
                                }
                                onChange={(e) => {
                                    setCustomerInfo((old) => ({
                                        ...old,
                                        customerLastName: e.target.value,
                                    }));
                                }}
                                placeholder={t('orderDetails.lastNamePlaceholder')}
                            />
                        </Grid>
                    </Grid>
                    <div style={{ marginTop: 11 }}>
                        <MaskedTextFormField
                            cmosVariant="grey"
                            label={t('commonLabels.mobile')}
                            name="mobile"
                            isRequired
                            disabled={
                                !permissions.allowEditWorkshopPlanner ||
                                !canUpdateCustomer ||
                                hasOrder
                            }
                            showValidationIndicators
                            maxLength={maxLengthPhone}
                            value={customerInfo?.customerMobile || ''}
                            onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '');
                                if (!Boolean(value)) {
                                    toasters.warning(
                                        t('appointments.step3.theMobileCannotBeEmpty'),
                                        t('appointments.step3.theClientCouldNotBeEdited')
                                    );
                                }
                                setCustomerInfo((old) => ({
                                    ...old,
                                    customerMobile: value,
                                }));
                            }}
                            mask={phoneFormatRegexMask(internationalization.phoneNumberFormat)}
                            placeholder={t('orderDetails.mobilePlaceholder')}
                            showMask={!!customerInfo?.customerMobile?.length}
                        />
                    </div>
                </Grid>
                <Grid item xs={6}>
                    <Grid container spacing={1}>
                        <Grid item xs={4}>
                            <BrandPicker
                                value={customerInfo?.vehicleMake ?? undefined}
                                onChange={(brandName) => {
                                    setCustomerInfo((old) => ({
                                        ...old,
                                        vehicleMake: brandName ?? '',
                                    }));
                                }}
                                hideIcons
                                disabled={
                                    !permissions.allowEditWorkshopPlanner ||
                                    !canUpdateVehicle ||
                                    hasOrder
                                }
                                cmosVariant="grey"
                                name="vehicle-brand"
                                label={t('workshopPlanner.convertOrder.vehicle')}
                                placeholder={t('appointments.step1.brand')}
                            />
                        </Grid>
                        <Grid item xs={4}>
                            <ModelPicker
                                brandName={customerInfo?.vehicleMake ?? undefined}
                                cmosVariant="grey"
                                name="vehicle-model"
                                label=" "
                                disabled={
                                    !permissions.allowEditWorkshopPlanner ||
                                    !canUpdateVehicle ||
                                    hasOrder
                                }
                                placeholder={t('appointments.step1.model')}
                                value={customerInfo?.vehicleModel}
                                onChange={(modelName) => {
                                    setCustomerInfo((old) => ({
                                        ...old,
                                        vehicleModel: modelName ?? '',
                                    }));
                                }}
                            />
                        </Grid>
                        <Grid item xs={4}>
                            <YearPicker
                                cmosVariant="grey"
                                name="vehicle-year"
                                disabled={
                                    !permissions.allowEditWorkshopPlanner ||
                                    !canUpdateVehicle ||
                                    hasOrder
                                }
                                modelName={customerInfo?.vehicleModel ?? undefined}
                                brandName={customerInfo?.vehicleMake ?? undefined}
                                label=" "
                                placeholder={t('appointments.step1.year')}
                                value={customerInfo?.vehicleYear}
                                onChange={(y) => {
                                    setCustomerInfo((old) => ({
                                        ...old,
                                        vehicleYear: y ?? '',
                                    }));
                                }}
                            />
                        </Grid>
                    </Grid>
                    <div style={{ marginTop: 11 }}>
                        <TextField
                            name="vehicle-plate"
                            cmosVariant="grey"
                            label={t('appointments.step1.plates')}
                            placeholder={t('appointments.step1.plates')}
                            disabled={
                                !permissions.allowEditWorkshopPlanner ||
                                !canUpdateVehicle ||
                                hasOrder
                            }
                            value={customerInfo?.vehiclePlates || ''}
                            onChange={(event) => {
                                setCustomerInfo((old) => ({
                                    ...old,
                                    vehiclePlates: event.target.value ?? '',
                                }));
                            }}
                        />
                    </div>
                </Grid>
            </Grid>
            <DivNotesContainer>
                <AppointmentNotes
                    appointmentId={appointmentId}
                    notesType="ForCustomer"
                    disabled={!permissions.allowEditWorkshopPlanner || hasOrder}
                    notes={appointmentNotes}
                    onChange={handleAppointmentNotesChange}
                />
                <AppointmentNotes
                    appointmentId={appointmentId}
                    notesType="ForInternal"
                    disabled={!permissions.allowEditWorkshopPlanner || hasOrder}
                    notes={appointmentNotes}
                    onChange={handleAppointmentNotesChange}
                />
            </DivNotesContainer>
            <Box sx={{ display: 'flex', gap: 5, mt: 4, justifyContent: 'center' }}>
                <Button
                    cmosVariant={'stroke'}
                    w={170}
                    iconPosition="right"
                    Icon={hasOrder ? EditIcon : PollsIcon}
                    disabled={
                        !permissions.allowEditWorkshopPlanner ||
                        !Boolean(customerInfo?.customerFirstName) ||
                        !Boolean(customerInfo?.customerMobile)
                    }
                    label={t(
                        `workshopPlanner.convertOrder.${hasOrder ? 'editOrder' : 'convertToOrder'}`
                    )}
                    onClick={() => onConvertToOrder()}
                />

                <Button
                    cmosVariant="stroke"
                    w={170}
                    disabled={
                        hasOrder || !permissions.allowEditWorkshopPlanner || !isSchedulingAllowed
                    }
                    label={t('workshopPlanner.convertOrder.addJob')}
                    onClick={handleAddJobClick}
                />
            </Box>
            <NewOrderDataPopup
                open={open && orderNumberPopupOpen}
                loading={createOrderMutation.isLoading}
                onClose={() => setOrderNumberPopupOpen(false)}
                onOrderData={onOrderData}
            />
        </>
    );
}

type ParameterProps = {
    label: React.ReactNode;
    children?: React.ReactNode;
};

function Parameter({ label, children }: ParameterProps) {
    return (
        <div>
            <CaptionBold>{label}</CaptionBold>
            <Caption>{children}</Caption>
        </div>
    );
}

function TimelineTab({ appointmentId }: { appointmentId: string }) {
    return <AppointmentScheduleTimeline appointmentId={appointmentId} />;
}

function useAppointmentQuery(appointmentId: string, enabled: boolean = true) {
    return useQuery({
        queryKey: ['appointment', appointmentId],
        queryFn: () => AppointmentsApi.get(appointmentId),
        enabled: !!appointmentId && enabled,
        staleTime: 100,
        cacheTime: 20000,
    });
}

const DivNotesContainer = styled('div')({
    marginTop: '16px',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
});

export default WpAdvisorsConvertOrderModal;
