import { useMutation } from '@tanstack/react-query';
import AppointmentAPI, { AppointmentConvertedResponse } from 'api/Appointment';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';

export const useCreateOrderMutation = (
    onSuccess?: (data: AppointmentConvertedResponse) => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const mutation = useMutation(
        (appointment: { id: string; orderNumber?: string | null }) =>
            AppointmentAPI.convertAppointment(appointment.id, appointment.orderNumber),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );
    return mutation;
};
