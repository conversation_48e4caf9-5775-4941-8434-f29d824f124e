import {
    TimelineConnector,
    TimelineContent,
    TimelineDot,
    TimelineItem,
    TimelineSeparator,
    timelineContentClasses,
    timelineItemClasses,
} from '@mui/lab';
import { Box, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { WpAppointmentsApi } from 'api/workshopPlanner';
import { PREDEFINED_PLANNINGS } from 'api/workshopPlanner/plannings';
import ArrowTooltip from 'common/components/Tooltip';
import { STimeline } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime, Duration } from 'luxon';
import { useMemo } from 'react';
import { Trans } from 'react-i18next';
import Skeleton from 'react-loading-skeleton';
import { useAppSelector } from 'store';
import { selectUsers } from 'store/slices/users';
import { selectPlannings } from 'store/slices/wp/plannings';

type AppointmentScheduleTimelineProps = {
    appointmentId: string;
};

const TimelineIsEmpty = styled('p')(({ theme }) => ({
    color: theme.palette.neutral[7],
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
}));

export default function AppointmentScheduleTimeline({
    appointmentId,
}: AppointmentScheduleTimelineProps) {
    const { data, isLoading } = useQuery(
        ['wp', 'appointment', appointmentId, 'schedule-timeline'],
        () => WpAppointmentsApi.getTimeline(appointmentId),
        {
            staleTime: 5000,
            cacheTime: 120000,
        }
    );
    const { t } = useAppTranslation();
    const blocks = useMemo(() => data?.blocks ?? [], [data]);
    const users = useAppSelector(selectUsers);
    const planningsList = useAppSelector(selectPlannings);

    return (
        <div>
            {isLoading && (
                <>
                    <ItemSkeleton />
                    <ItemSkeleton />
                    <ItemSkeleton />
                    <ItemSkeleton />
                </>
            )}
            {blocks.length === 0 && !isLoading && (
                <TimelineIsEmpty>
                    {t('workshopPlanner.orderPopup.noJobsInTimeline')}
                </TimelineIsEmpty>
            )}
            <STimeline
                sx={{
                    [`& .${timelineItemClasses.missingOppositeContent}::before`]: {
                        display: 'none',
                    },

                    [`& .${timelineContentClasses.root}`]: (theme) => ({
                        ...theme.typography.h5Inter,
                    }),
                }}
            >
                {blocks.map((x, idx) => {
                    const user = users[x.userId];
                    const planning = planningsList.list.find((p) => p.id === x.planningId);
                    const planningName = planning
                        ? planning.readonly && planning.name === PREDEFINED_PLANNINGS.TECHNICIANS
                            ? t('workshopPlanner.technicians.title')
                            : planning.name
                        : 'UNKNOWN';
                    const startsAt = DateTime.fromISO(x.startsAt);
                    const endsAt = startsAt.plus({ minute: x.duration });
                    const isLast = idx === blocks.length - 1;

                    return (
                        <TimelineItem key={x.startsAt}>
                            <TimelineSeparator>
                                <TimelineDot color="primary" />
                                {!isLast && <TimelineConnector sx={{ bgcolor: 'var(--cm1)' }} />}
                            </TimelineSeparator>
                            <TimelineContent>
                                <Trans
                                    i18nKey="workshopPlanner.orderPopup.timelineTemplate"
                                    values={{
                                        user: user?.name ?? 'USER UNKNOWN',
                                        planning: planningName,
                                    }}
                                />
                                &nbsp;&mdash;&nbsp;
                                <ArrowTooltip content={toActuallyHuman(endsAt.diff(startsAt))}>
                                    <span style={{ cursor: 'default' }}>
                                        <time dateTime={startsAt.toISO()}>
                                            {startsAt.toFormat('dd/MM/yy HH:mm')}
                                        </time>{' '}
                                        -{' '}
                                        <time dateTime={endsAt.toISO()}>
                                            {endsAt.hasSame(startsAt, 'day')
                                                ? endsAt.toFormat('HH:mm')
                                                : endsAt.toFormat('dd/MM/yy HH:mm')}
                                        </time>
                                    </span>
                                </ArrowTooltip>
                            </TimelineContent>
                        </TimelineItem>
                    );
                })}
            </STimeline>
        </div>
    );
}

function toActuallyHuman(d: Duration) {
    let millis = d.toMillis();
    if (millis === 0) return Duration.fromObject({ minutes: 0 }).toHuman();
    const days = Math.floor(millis / 86400000);
    millis -= days * 86400000;
    const hours = Math.floor(millis / 3600000);
    millis -= hours * 3600000;
    const minutes = Math.floor(millis / 60000);
    millis -= minutes * 60000;
    const seconds = Math.floor(millis / 1000);
    millis -= seconds * 1000;
    return Duration.fromObject({
        days: days > 0 ? days : undefined,
        hours: hours > 0 ? hours : undefined,
        minutes: minutes > 0 ? minutes : undefined,
        seconds: seconds > 0 ? seconds : undefined,
        milliseconds: millis > 0 ? millis : undefined,
    }).toHuman();
}

const ItemSkeleton = () => (
    <>
        <Box display="flex" gap={0.5} marginBottom={1}>
            <Skeleton circle width={15} height={15} />
            <Skeleton width={450} height={10} />
        </Box>
    </>
);
