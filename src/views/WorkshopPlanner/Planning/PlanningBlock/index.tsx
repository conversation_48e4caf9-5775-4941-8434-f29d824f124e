import { useMutation } from '@tanstack/react-query';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectUserPermission, useCurrentUser } from 'store/slices/user';
import {
    BlockView,
    JobTimelineAction,
    selectBlock,
    selectBlockIsHidden,
    selectDoesUserHaveMultiplePlannings,
    selectPlanning,
    setJobStatusThunk,
} from 'store/slices/wp/plannings';
import OrderBlock from 'views/WorkshopPlanner/common/OrderBlock';
import PlannedOrderBlock from 'views/WorkshopPlanner/common/PlannedOrderBlock';
import ThreeDotBlockMenu, {
    ThreeDotMenuAction,
} from 'views/WorkshopPlanner/common/ThreeDotBlockMenu';
import { useSelectedBlockAttributesSetter } from 'views/WorkshopPlanner/common/selectedBlockContext';
import { useScheduleAppointmentAltPopup } from 'views/WorkshopPlanner/schedule-popups/ScheduleAppointmentWorkAltPopup/context';
import { useScheduleOrderAltPopup } from 'views/WorkshopPlanner/schedule-popups/ScheduleOrderWorkAltPopup/context';
import ChangeJobStatusPopup from '../ChangeJobStatusPopup';
import DeleteBlockPopup from '../DeleteBlockPopup';
import { useEditBlockPopupContext } from '../EditBlockPopup/context';
import { useMoreJobInfoPopup } from '../MoreJobInfoPopup/context';
import ReassignBlockPopup from '../ReassignBlockPopup';
import { useRequestPasswordPopupContext } from '../RequestPasswordPopup/context';

export type FilteredBlockView = BlockView & { isFiltered?: boolean };

type Props = {
    block: FilteredBlockView;
    onDoubleClick?: (block: FilteredBlockView) => void;
    planningId: number;
    ignoreHiddenFlag?: boolean;
    outerContainer: HTMLElement | null;
    isDragging?: boolean;
    disableTooltip?: boolean;
    vertical: boolean;
    singleView: boolean;
};

function PlanningBlock({
    block,
    onDoubleClick,
    planningId,
    ignoreHiddenFlag,
    outerContainer,
    isDragging = false,
    disableTooltip = false,
    vertical,
    singleView,
}: Props) {
    const [action, setAction] = useState<ThreeDotMenuAction>();
    const [ref, setRef] = useState<HTMLElement | null>(null);
    const [tooltipPosition, setTooltipPosition] = useState<'top' | 'bottom' | 'right' | 'left'>(
        'top'
    );
    const [tooltipIsOpened, setTooltipIsOpened] = useState(false);
    const [threeDotMenuIsOpened, setThreeDotMenuIsOpened] = useState(false);
    const editBlockPopup = useEditBlockPopupContext();
    const isHiddenState = useAppSelector((r) => selectBlockIsHidden(r, block.id));
    const { allowEditWorkshopPlanner } = useAppSelector(selectUserPermission);
    const id = planningId !== undefined ? getBlockElementGlobalId(planningId, block.id) : undefined;
    const isHidden = isHiddenState && !ignoreHiddenFlag;
    const belongsToMultiplePlannings = useAppSelector((r) =>
        selectDoesUserHaveMultiplePlannings(r, block.user.id)
    );
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const user = useCurrentUser();
    const planning = useAppSelector((r) => selectPlanning(r, block.planningId));
    const planningName = useMemo(() => {
        if (block.type !== 'order' && block.type !== 'appointment') return undefined;
        if (!belongsToMultiplePlannings && block.planningId === planningId) return undefined;
        if (planning?.isTechnicians) return t('workshopPlanner.technicians.title');
        return planning?.name;
    }, [planning, t, belongsToMultiplePlannings, block, planningId]);
    const schedulerContext = useSchedulerContext();
    const scheduleOrderBlockPopup = useScheduleOrderAltPopup();
    const scheduleAppointmentAltPopup = useScheduleAppointmentAltPopup();
    const requestPasswordPopup = useRequestPasswordPopupContext();

    const setPauseStatusMutation = useMutation(async (userKey: string) => {
        await dispatch(
            setJobStatusThunk({
                jobId: block.id,
                action: JobTimelineAction.Paused,
                newPhaseId: null,
                pauseReason: null,
                userKey: userKey,
            })
        );
    });

    const currentBlockAttributes = useMemo(() => {
        if (block.type === 'order') {
            return {
                orderNumber: block.order.number,
                appointmentNumber: null,
            };
        } else {
            return {
                orderNumber:
                    block.appointment.orderNumber && block.appointment.orderNumber.length > 0
                        ? block.appointment.orderNumber
                        : null,
                appointmentNumber: block.appointment.number,
            };
        }
    }, [block]);

    const moreJobInfoPopup = useMoreJobInfoPopup();
    const { blockState } = useAppSelector((r) => {
        const blockState = selectBlock(r, block.id);
        return {
            blockState,
        };
    });

    const openMoreInfo = useCallback(() => {
        if (blockState) moreJobInfoPopup.open(blockState.id);
    }, [blockState, moreJobInfoPopup]);

    const handleStopJobValidationErrors = (errors: string[]) => {
        if (errors.indexOf('General.WP.Jobs.NoStandardOperationSpecified') >= 0) {
            onBlockAction({ actionType: 'edit', withoutPreview: false });
        } else if (errors.indexOf('General.WP.Jobs.MandatoryFieldNotSpecified') >= 0) {
            openMoreInfo();
        }
    };

    const onBlockAction = useCallback(
        ({
            actionType,
            withoutPreview,
        }: {
            actionType: ThreeDotMenuAction;
            withoutPreview: boolean;
        }) => {
            switch (actionType) {
                case 'edit': {
                    setAction(undefined);
                    if (block.type === 'appointment') {
                        editBlockPopup.open({
                            blockId: block.id,
                            withoutPreview,
                            anchorElement: withoutPreview ? ref ?? undefined : undefined,
                        });
                    } else {
                        openMoreInfo();
                    }
                    break;
                }
                case 'duplicate': {
                    setAction(undefined);

                    if (block.type === 'order') {
                        scheduleOrderBlockPopup.open(
                            block.order.number,
                            block.startsAt + block.duration * 60000,
                            block.duration,
                            block.user.id,
                            block.planningId,
                            true
                        );
                    } else {
                        scheduleAppointmentAltPopup.open(
                            block.appointment.id,
                            block.startsAt + block.duration * 60000,
                            block.duration,
                            block.user.id,
                            block.planningId,
                            true
                        );
                    }
                    break;
                }
                case 'pause': {
                    if (user.permission.allowManageJobs || user.key === block.user.id) {
                        setPauseStatusMutation.mutate(user.key);
                    } else {
                        setAction(undefined);
                        requestPasswordPopup.open({
                            onSuccess: (userKey) => {
                                setPauseStatusMutation.mutate(userKey);
                            },
                            user: block.user,
                            buttonTitle: t('workshopPlanner.requestPasswordBlock.pauseJob'),
                            closeOnSuccess: true,
                            anchorElement: ref ?? undefined,
                        });
                    }
                    break;
                }
                default: {
                    setAction(actionType);
                    break;
                }
            }
        },
        [
            editBlockPopup,
            scheduleOrderBlockPopup,
            scheduleAppointmentAltPopup,
            requestPasswordPopup,
            block,
            user,
            setPauseStatusMutation,
            t,
            ref,
            openMoreInfo,
        ]
    );

    const setSelectedBlock = useSelectedBlockAttributesSetter();

    const onOpenTooltip = useCallback(() => {
        if (threeDotMenuIsOpened || isDragging) return;
        setSelectedBlock({
            orderNumber: currentBlockAttributes.orderNumber,
            appointmentNumber: currentBlockAttributes.appointmentNumber,
        });
        setTooltipIsOpened(true);
    }, [setSelectedBlock, threeDotMenuIsOpened, isDragging, currentBlockAttributes]);

    const onCloseTooltip = useCallback(() => {
        setSelectedBlock(null);
        setTooltipIsOpened(false);
    }, [setSelectedBlock]);

    useEffect(() => {
        onCloseTooltip();
    }, [isDragging, onCloseTooltip]);

    const updateTooltipPosition = useCallback(() => {
        if (!outerContainer || !ref) {
            return;
        }

        const outerRect = outerContainer.getBoundingClientRect();
        const innerRect = ref.getBoundingClientRect();

        let tooltipRowsCount;
        if (block.type === 'order') {
            tooltipRowsCount = 5;
            tooltipRowsCount += block.order.type ? 1 : 0;
            tooltipRowsCount += block.order.promisedAt ? 1 : 0;
            tooltipRowsCount += block.order.inCharge ? 1 : 0;
            tooltipRowsCount +=
                block.order.appointmentReasons.length > 0
                    ? 1 + block.order.appointmentReasons.length
                    : 0;
            tooltipRowsCount += block.order.notes.length > 0 ? 1 + block.order.notes.length : 0;
        } else {
            tooltipRowsCount = 7;
            tooltipRowsCount +=
                block.appointment.reasons.length > 0 ? 1 + block.appointment.reasons.length : 0;
            tooltipRowsCount +=
                block.appointment.notes.length > 0 ? 1 + block.appointment.notes.length : 0;
        }

        const pixelsPerRow = 24;
        const tooltipHeight = tooltipRowsCount * pixelsPerRow;

        if (innerRect.top > tooltipHeight && innerRect.top - outerRect.top - 40 > tooltipHeight) {
            setTooltipPosition('top');
        } else if (document.documentElement.clientHeight - innerRect.bottom > tooltipHeight) {
            setTooltipPosition('bottom');
        } else if (document.documentElement.clientWidth - innerRect.right > innerRect.left) {
            setTooltipPosition('right');
        } else {
            setTooltipPosition('left');
        }
    }, [outerContainer, ref, block]);

    useEffect(() => {
        if (tooltipIsOpened && outerContainer && ref) {
            updateTooltipPosition();
        }
    }, [updateTooltipPosition, outerContainer, ref, tooltipIsOpened]);

    const formattedDuration = useMemo(() => {
        const minutesPerHour = 60;
        const hours = Math.floor(block.duration / minutesPerHour);
        const minutes = block.duration % minutesPerHour;

        const formatNumber = (n: number) => (n < 10 ? '0' + n : n);

        if (hours === 0) {
            return formatNumber(minutes) + ' min';
        } else {
            return (
                formatNumber(hours) +
                ':' +
                formatNumber(minutes) +
                (hours === 1 && minutes === 0 ? ' hr' : ' hrs')
            );
        }
    }, [block.duration]);

    let blockNode: React.ReactNode;

    if (block.type === 'order') {
        blockNode = (
            <OrderBlock
                ref={setRef}
                id={id}
                style={{ visibility: isHidden ? 'hidden' : 'visible', height: '100%' }}
                data-planning-id={planningId}
                data-block-id={id}
                isChanged={block.changed}
                tooltipIsOpened={
                    !disableTooltip && tooltipIsOpened && !threeDotMenuIsOpened && !isDragging
                }
                isDragging={isDragging ?? false}
                tooltipPosition={tooltipPosition}
                onDoubleClick={() => {
                    if (onDoubleClick) {
                        onCloseTooltip();
                        onDoubleClick(block);
                    }
                }}
                onOpenTooltip={onOpenTooltip}
                onCloseTooltip={onCloseTooltip}
                orderId={block.order.id}
                orderNumber={block.order.number}
                firstName={block.order.customer?.firstName ?? ''}
                lastName={block.order.customer?.lastName ?? ''}
                model={block.order.vehicle?.model ?? ''}
                plates={block.order.vehicle?.plates ?? ''}
                startsAtDate={DateTime.fromMillis(block.startsAt).toISO()}
                deliveryDate={block.order.promisedAt}
                phase={block.order.phase}
                tower={block.order.tower ?? ''}
                towerColor={{
                    orderTypeKey: block.order.type?.id,
                    appointmentReasonColor: block.order.appointmentReasonColor,
                    userIdOrKey: block.order.inCharge?.id,
                    colorFieldValue: block.order.colorFieldValue,
                }}
                orderType={block.order.type?.name}
                planningName={planningName}
                availableVerticalSpace={schedulerContext.dimensions.rowHeight}
                disabled={!allowEditWorkshopPlanner}
                duration={formattedDuration}
                inChargeName={block.order.inCharge?.name ?? null}
                appointmentReasons={block.order.appointmentReasons}
                notes={block.order.notes}
                orderReasons={block.order.orderReasons}
                jobDescriptions={block.order.jobDescriptions}
                vertical={vertical}
                singleView={singleView}
                height={
                    vertical
                        ? schedulerContext.dimensions.pixelsPerMinuteRatio * block.duration
                        : schedulerContext.dimensions.rowHeight
                }
                hasRedItems={block.order.hasRedItems}
                templateId={block.order.templateId}
                jobStatus={block.status}
                accumulatedDurationInSeconds={block.accumulatedDurationInSeconds}
                lastStart={block.lastStart}
                isFiltered={block.isFiltered ?? false}
            >
                {allowEditWorkshopPlanner && (
                    <ThreeDotBlockMenu
                        autoHide
                        blockId={block.id}
                        orderNumber={block.order.number}
                        orderId={block.order.id}
                        onOpen={() => {
                            setThreeDotMenuIsOpened(true);
                            onCloseTooltip();
                        }}
                        onClose={() => {
                            setThreeDotMenuIsOpened(false);
                        }}
                        onAction={onBlockAction}
                        alignCenter={block.duration <= 10}
                    />
                )}
            </OrderBlock>
        );
    } else if (block.type === 'appointment') {
        blockNode = (
            <PlannedOrderBlock
                ref={setRef}
                id={id}
                style={{ visibility: isHidden ? 'hidden' : 'visible', height: '100%' }}
                data-planning-id={planningId}
                data-block-id={id}
                tooltipIsOpened={
                    !disableTooltip && tooltipIsOpened && !threeDotMenuIsOpened && !isDragging
                }
                isDragging={isDragging ?? false}
                tooltipPosition={tooltipPosition}
                onDoubleClick={() => onDoubleClick && onDoubleClick(block)}
                onOpenTooltip={onOpenTooltip}
                onCloseTooltip={onCloseTooltip}
                appointmentId={block.appointment.id}
                appointmentNumber={block.appointment.number}
                orderNumber={block.appointment.orderNumber}
                status={block.appointment.status}
                firstName={block.appointment.customer.firstName}
                lastName={block.appointment.customer.lastName}
                model={block.appointment.vehicle?.model}
                plates={block.appointment.vehicle?.plates}
                duration={formattedDuration}
                appointmentTime={block.appointment.startTime}
                serviceAdvisor={block.appointment.serviceAdvisor}
                appointmentReasons={block.appointment.reasons}
                notes={block.appointment.notes}
                isNew={block.new}
                isChanged={block.changed}
                planningName={planningName}
                availableVerticalSpace={schedulerContext.dimensions.rowHeight}
                disabled={!allowEditWorkshopPlanner}
                color={{
                    appointmentReasonId:
                        block.appointment.reasons.length > 0
                            ? block.appointment.reasons[0].id
                            : undefined, // TODO CMOS-3123
                    userIdOrKey: block.appointment.serviceAdvisor.id,
                }}
                vertical={vertical}
                singleView={singleView}
                height={
                    vertical
                        ? schedulerContext.dimensions.pixelsPerMinuteRatio * block.duration
                        : schedulerContext.dimensions.rowHeight
                }
                jobDescriptions={block.appointment.jobDescriptions}
                isFiltered={block.isFiltered ?? false}
            >
                {allowEditWorkshopPlanner && (
                    <ThreeDotBlockMenu
                        autoHide
                        blockId={block.id}
                        onOpen={() => {
                            setThreeDotMenuIsOpened(true);
                            onCloseTooltip();
                        }}
                        onClose={() => {
                            setThreeDotMenuIsOpened(false);
                        }}
                        onAction={onBlockAction}
                        alignCenter={block.duration <= 10}
                    />
                )}
            </PlannedOrderBlock>
        );
    }

    return (
        <>
            {blockNode}
            {action === 'delete' && (
                <DeleteBlockPopup
                    onClose={() => setAction(undefined)}
                    open
                    blockId={block.id}
                    anchorEl={ref}
                />
            )}
            {action === 'reassign' && (
                <ReassignBlockPopup
                    onClose={() => setAction(undefined)}
                    open
                    blockId={block.id}
                    anchorEl={ref}
                />
            )}
            {action === 'start' && block.type === 'order' && (
                <ChangeJobStatusPopup
                    action={JobTimelineAction.Started}
                    onClose={() => setAction(undefined)}
                    open
                    block={block}
                    orderKey={block.order.id}
                    orderPhaseId={block.order.phase.id}
                    orderPhaseChangedAt={block.order.phaseChangedAt}
                    orderPhaseChangedByUserDisplayName={block.order.phaseChangedByUserDisplayName}
                    currentPhase={block.order.phase}
                    orderNumber={block.order.number}
                />
            )}
            {action === 'stop' && block.type === 'order' && (
                <ChangeJobStatusPopup
                    action={JobTimelineAction.Stopped}
                    onClose={() => setAction(undefined)}
                    open
                    block={block}
                    orderKey={block.order.id}
                    orderPhaseId={block.order.phase.id}
                    orderPhaseChangedAt={block.order.phaseChangedAt}
                    orderPhaseChangedByUserDisplayName={block.order.phaseChangedByUserDisplayName}
                    currentPhase={block.order.phase}
                    onValidationErrors={handleStopJobValidationErrors}
                    orderNumber={block.order.number}
                />
            )}
            {action === 'pauseWithOrder' && block.type === 'order' && (
                <ChangeJobStatusPopup
                    action={JobTimelineAction.Paused}
                    onClose={() => setAction(undefined)}
                    open
                    block={block}
                    orderKey={block.order.id}
                    orderPhaseId={block.order.phase.id}
                    orderPhaseChangedAt={block.order.phaseChangedAt}
                    orderPhaseChangedByUserDisplayName={block.order.phaseChangedByUserDisplayName}
                    currentPhase={block.order.phase}
                    orderNumber={block.order.number}
                    anchorEl={ref}
                />
            )}
            {action === 'resume' && block.type === 'order' && (
                <ChangeJobStatusPopup
                    action={JobTimelineAction.Resumed}
                    onClose={() => setAction(undefined)}
                    open
                    block={block}
                    orderKey={block.order.id}
                    orderPhaseId={block.order.phase.id}
                    orderPhaseChangedAt={block.order.phaseChangedAt}
                    orderPhaseChangedByUserDisplayName={block.order.phaseChangedByUserDisplayName}
                    currentPhase={block.order.phase}
                    orderNumber={block.order.number}
                />
            )}
        </>
    );
}

export default memo(PlanningBlock);

export function getBlockElementGlobalId(planningId: number, blockId: string): string {
    return `wp-planning-${planningId}_block-${blockId}`;
}

function getBlockClass(blockId: string): string {
    return `wp-block-${blockId}`;
}

export function getBlockElement(blockId: string, planningId?: number): HTMLElement | null {
    if (planningId) return document.getElementById(getBlockElementGlobalId(planningId, blockId));

    return document.querySelector('.' + getBlockClass(blockId));
}
