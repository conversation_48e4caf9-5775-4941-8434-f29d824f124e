import { styled } from '@mui/material';
import WpApi from 'api/workshopPlanner';
import clsx from 'clsx';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import Tooltip from 'common/components/Tooltip';
import { useEffect, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectUser } from 'store/slices/users';
import { selectUserSchedule } from 'store/slices/wp/plannings';
import { usePlanningId } from '..';
import { useUserAbsence } from './util';

type TeamMemberHeaderProps = {
    userId: string;
    vertical: boolean;
    showTeamMemberSpecialty: boolean;
    userSpecialty: string | null;
};

const Root = styled('div')(({ theme }) => ({
    width: '100%',
    height: '100%',
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: 4,

    boxShadow: `0 0 0 3px transparent inset`,
    transition: 'box-shadow 1.5s',

    '&.focused': {
        boxShadow: `0 0 0 3px ${theme.palette.primary.main} inset`,
    },

    '&.absent': {
        borderColor: theme.palette.neutral[4],
        '& > span': {
            opacity: 0.3,
        },
    },

    '&.TeamMemberHeader-sm': {
        '& .TeamMemberDisplayedName': {
            fontSize: '11px',
            lineHeight: '12px',
        },
    },

    '@media (max-height: 760px)': {
        gap: 0,
    },
}));

const Title = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 400,
    color: theme.palette.neutral[9],

    [theme.breakpoints.down('lg')]: {
        fontSize: '11px',
        lineHeight: '12px',
    },
}));

const Specialty = styled('span')<{ vertical?: boolean }>(({ theme, vertical }) => ({
    ...theme.typography.h8Inter,
    fontWeight: 400,
    color: theme.palette.neutral[9],
    whiteSpace: 'nowrap',
    ...(vertical && {
        maxWidth: '100%',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        paddingLeft: 5,
        paddingRight: 5,
    }),
}));

export default function TeamMemberHeader({
    userId,
    vertical,
    showTeamMemberSpecialty,
    userSpecialty,
}: TeamMemberHeaderProps) {
    const user: Readonly<WpApi.User> | undefined = useAppSelector((r) => {
        const u = selectUser(r, userId);
        if (u) {
            const { id, key, ...rest } = u;
            return { id: key, ...rest };
        }
        return undefined;
    });
    const { absences } = useAppSelector((r) => selectUserSchedule(r, userId));
    const planningId = usePlanningId();
    const displayedName = user?.name ?? userId;
    const isAbsent = useUserAbsence(user, absences);
    const {
        dimensions: { rowHeight },
    } = useSchedulerContext();

    const maxSpecialtyLength = 20;

    function truncateText(text: string, maxLength: number) {
        return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
    }

    const truncatedSpecialty = vertical
        ? userSpecialty
        : userSpecialty && truncateText(userSpecialty, maxSpecialtyLength);
    const specialtyRef = useRef<HTMLSpanElement | null>(null);
    const [isOverflowed, setIsOverflowed] = useState(false);

    useEffect(() => {
        if (vertical) {
            specialtyRef.current &&
                setIsOverflowed(
                    specialtyRef.current.scrollWidth > specialtyRef.current.offsetWidth
                );
        } else {
            userSpecialty && setIsOverflowed(userSpecialty.length > maxSpecialtyLength);
        }
    }, [userSpecialty, vertical]);

    return (
        <Root
            data-test-id={`planning-user-row-${planningId}-${userId})`}
            // warning - do not change this id - it is used for scrolling to this element
            id={`planning-user-row-${planningId}-${userId})`}
            className={clsx(
                isAbsent ? 'absent' : undefined,
                !vertical && rowHeight <= 60 ? 'TeamMemberHeader-sm' : undefined
            )}
            style={
                vertical
                    ? { alignItems: 'center' }
                    : {
                          paddingLeft: 13,
                          alignItems: 'start',
                      }
            }
        >
            <Title className="TeamMemberDisplayedName">{displayedName}</Title>
            {showTeamMemberSpecialty &&
                userSpecialty &&
                (isOverflowed ? (
                    <Tooltip content={userSpecialty} position={vertical ? 'bottom' : 'right'}>
                        <Specialty vertical={vertical} ref={specialtyRef}>
                            {truncatedSpecialty}
                        </Specialty>
                    </Tooltip>
                ) : (
                    <Specialty vertical={vertical} ref={specialtyRef}>
                        {truncatedSpecialty}
                    </Specialty>
                ))}
        </Root>
    );
}
