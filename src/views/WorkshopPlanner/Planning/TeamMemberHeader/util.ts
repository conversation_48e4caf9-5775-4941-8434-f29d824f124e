import { getScheduleForDate, UserScheduleDto } from 'api/common';
import { AbsenceDto } from 'api/users';
import { TimeSpan } from 'api/utils/format';
import WpApi from 'api/workshopPlanner';
import useNow from 'common/hooks/useNow';
import { DateTime } from 'luxon';
import { useAppSelector } from 'store';
import { selectIanaTz } from 'store/slices/globalSettingsSlice';

/**
 * @param user user for which to find an activity
 * @returns absence or presence of user
 */
export function useUserAbsence(
    user: Readonly<WpApi.User> | undefined,
    absences: AbsenceDto[]
): Boolean {
    const now = DateTime.fromJSDate(useNow());
    const tzName = useAppSelector(selectIanaTz);
    const daySchedule = user ? getScheduleForDate(user.schedule, now) : null;

    if (
        daySchedule &&
        (daySchedule.windows.length === 0 ||
            scheduleDoesNotWorkRightNow(tzName, daySchedule, now) ||
            hasAbsenceRightNow(now, absences))
    ) {
        return true;
    }

    return false;
}

function scheduleDoesNotWorkRightNow(
    tzName: string,
    schedule: UserScheduleDto.DaySchedule,
    now: DateTime
): boolean {
    const nowTz = now.setZone(tzName);
    const timeInSeconds = nowTz.hour * 3600 + nowTz.minute * 60 + nowTz.second;
    return !schedule.windows.some((w) => {
        const fromInSeconds = TimeSpan.fromString(w.from).totalSeconds;
        const toInSeconds = TimeSpan.fromString(w.to).totalSeconds;
        return timeInSeconds >= fromInSeconds && timeInSeconds <= toInSeconds;
    });
}

function hasAbsenceRightNow(
    now: DateTime,
    absences: { startsAt: string; endsAt: string }[]
): boolean {
    return absences.some((absence) => {
        const start = DateTime.fromISO(absence.startsAt);
        const end = DateTime.fromISO(absence.endsAt);
        return now >= start && now < end;
    });
}
