import { Box, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { Button } from 'common/components/Button';
import { useOrderJobsInProgressCheck } from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { usePhaseSetbackCheck } from 'common/components/ChangePhaseProviders/PhaseSetbackCheck';
import {
    CommonConfirmationPopup,
    WarningConfirmationPopup,
} from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import { useCallback, useState } from 'react';
import { Trans } from 'react-i18next';
import { useAppDispatch, useAppSelector } from 'store';
import { useCurrentUser } from 'store/slices/user';
import {
    BlockView,
    JobTimelineAction,
    loadScheduleThunk,
    setJobStatusThunk,
} from 'store/slices/wp/plannings';
import PhaseSetbackApi from '../../../../api/phaseSetback';
import WpApi from '../../../../api/workshopPlanner';
import { ButtonProps } from '../../../../common/components/Button/ButtonProps';
import { TrafficLightIcon } from '../../../../common/components/Icons/TrafficLightIcon';
import { Modal } from '../../../../common/components/Modal';
import { ReasonForPause } from '../../../../common/constants/ReasonForPause';
import { IconSize } from '../../../../common/styles/IconSize';
import { isErrorResponse } from '../../../../services/Server';
import {
    selectIanaTz,
    selectRepairShopWpConfiguration,
} from '../../../../store/slices/globalSettingsSlice';
import { loadAllPausedOrdersThunk } from '../../../../store/slices/wp/paused';
import PauseReasonPicker from '../../../Components/PauseReasonPicker';
import PhasePicker from '../../../Components/PhasePicker';
import BlockPopupBase, { BlockPopupBaseProps } from '../BlockPopupBase';
import RequestPasswordPopup from '../RequestPasswordPopup';

type ChangeJobStatusPopupProps = BlockPopupBaseProps & {
    block: BlockView;
    orderKey: string;
    orderPhaseId: number;
    orderPhaseChangedByUserDisplayName: string | null;
    orderPhaseChangedAt: string | null;
    currentPhase: {
        id: number;
        name: string;
    };
    action: JobTimelineAction;
    orderNumber: string;
    onValidationErrors?: (errors: string[]) => void;
};

export default function ChangeJobStatusPopup({
    block,
    currentPhase,
    orderKey,
    orderNumber,
    orderPhaseId,
    orderPhaseChangedByUserDisplayName,
    orderPhaseChangedAt,
    action,
    onValidationErrors,
    ...props
}: ChangeJobStatusPopupProps) {
    const { t } = useAppTranslation();
    const ianaTz = useAppSelector(selectIanaTz);
    const user = useCurrentUser();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const [userKey, setUserKey] = useState<string>(user.key);
    const [selectedPhase, setSelectedPhase] = useState<number | null>();
    const [showPhaseSelectorPopup, setShowPhaseSelectorPopup] = useState<boolean>(false);
    const [incompleteInformationErrors, setIncompleteInformationErrors] = useState<string[]>([]);
    const [isPendingSignaturePopup, setIsPendingSignaturePopup] = useState<boolean>(false);
    const [preSelectedReasonForPause, setPreSelectedReasonForPause] =
        useState<ReasonForPause | null>(null);
    const [selectedReasonForPause, setSelectedReasonForPause] = useState<ReasonForPause | null>(
        null
    );

    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const [requestPassword, setRequestPassword] = useState<boolean>(
        !user.permission.allowManageJobs && block.user.id !== user.key
    );
    const onPasswordVerified = (key: string) => {
        setRequestPassword(false);
        setUserKey(key);
    };

    const actionTitle = function (): string {
        switch (action) {
            case JobTimelineAction.Started:
                return t('workshopPlanner.changeJobStatusPopup.actionStarted');
            case JobTimelineAction.Paused:
                return t('workshopPlanner.changeJobStatusPopup.actionPaused');
            case JobTimelineAction.Resumed:
                return t('workshopPlanner.changeJobStatusPopup.actionResumed');
            case JobTimelineAction.Stopped:
                return t('workshopPlanner.changeJobStatusPopup.actionStopped');
        }
    };

    const setJobStatusMutation = useMutation({
        mutationFn: async () => {
            if (action === JobTimelineAction.Paused && !selectedReasonForPause) {
                throw new Error('cannot pause the order, pause reason is not set');
            }

            const result = await dispatch(
                setJobStatusThunk({
                    jobId: block.id,
                    action: action,
                    newPhaseId: currentPhase?.id !== selectedPhase ? selectedPhase : null,
                    pauseReason: selectedReasonForPause,
                    userKey: userKey,
                })
            );
            if (result.meta.requestStatus === 'rejected') throw result.payload;

            return result.payload as WpApi.SetJobStatusResponseDto;
        },
        onSuccess: (data) => {
            if (enableWorkshopJobIntegration && integrationAccountName) {
                if (!data.thirdPartyError) {
                    toasters.success(
                        t('workshopPlanner.orderPopup.setActionIntegrationSuccess.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.orderPopup.setActionIntegrationSuccess.title')
                    );
                } else {
                    toasters.danger(
                        t('workshopPlanner.orderPopup.setActionIntegrationError.text', {
                            errorMessage: data.thirdPartyError,
                        }),
                        t('workshopPlanner.orderPopup.setActionIntegrationError.title', {
                            integrationAccountName,
                        })
                    );
                }
            }

            if (action === JobTimelineAction.Paused) {
                // sub-optimal but whatever
                dispatch(loadAllPausedOrdersThunk());
                dispatch(loadScheduleThunk({}));
                props.onClose();
            } else if (data.errors && data.errors.length > 0) {
                setIncompleteInformationErrors(data.errors);
                setIsPendingSignaturePopup(
                    data.errors.length === 1 &&
                        data.errors[0] === 'General.WP.Jobs.PendingSignature'
                );
            } else {
                props.onClose();
            }
        },
        onError: (err) => {
            if (isErrorResponse(err)) {
                if (err.code === 'General.WP.TechnicianNotAssigned') {
                    toasters.danger(
                        t('workshopPlanner.orderPopup.technicianNotAssignedError'),
                        t('toasters.errorOccurred')
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            } else {
                console.warn('unknown error occurred while updating the entry: ', err);
            }
        },
    });

    const onSelectPhase = async function () {
        setShowPhaseSelectorPopup(true);
    };

    const onBack = async function () {
        setShowPhaseSelectorPopup(false);
        setSelectedPhase(null);
    };

    const onClose = function () {
        props.onClose();
        setShowPhaseSelectorPopup(false);
    };

    const changeJobStatus = useCallback(
        async (phaseSetbackReason?: string) => {
            await setJobStatusMutation.mutateAsync();

            if (phaseSetbackReason) {
                try {
                    if (currentPhase?.id != null && selectedPhase != null) {
                        await PhaseSetbackApi.savePhaseSetback({
                            orderKey,
                            originPhaseId: currentPhase.id,
                            destinationPhaseId: selectedPhase,
                            reason: phaseSetbackReason,
                        });
                    }
                } catch {
                    toasters.danger('', t('toasters.errorOccurredWhenSaving'));
                }
            }
        },
        [setJobStatusMutation, currentPhase, selectedPhase, toasters, t, orderKey]
    );

    const getPhaseName = (phaseName: string | null | undefined) =>
        !!phaseName && ['noPhase', 'closedOrder'].includes(phaseName)
            ? t(`phases.${phaseName}`)
            : phaseName;

    const hidePhaseIds = (): number[] => {
        const arr = [];
        if (action !== JobTimelineAction.Stopped) {
            arr.push(-2);
        }
        if (!!currentPhase) {
            arr.push(currentPhase!.id);
        }
        return arr;
    };

    return requestPassword ? (
        <RequestPasswordPopup
            onSuccess={onPasswordVerified}
            user={block.user}
            buttonTitle={t('workshopPlanner.requestPasswordBlock.startJob')}
            closeOnSuccess={false}
            {...props}
        />
    ) : (
        <>
            <CommonConfirmationPopup
                icon={<TrafficLightIcon fill={Colors.CM1} size={IconSize.M} />}
                color={Colors.CM1}
                open={
                    props.open &&
                    orderPhaseId !== -2 &&
                    !showPhaseSelectorPopup &&
                    (action !== JobTimelineAction.Paused || selectedReasonForPause != null)
                }
                title={t('workshopPlanner.changeJobStatusPopup.title')}
                body={
                    <Trans
                        i18nKey="workshopPlanner.changeJobStatusPopup.body"
                        values={{
                            action: actionTitle(),
                            currentPhase: getPhaseName(currentPhase?.name),
                        }}
                        components={{ bold: <strong /> }}
                    />
                }
                onClose={onClose}
                actionsContent={
                    <Box
                        sx={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-between',
                            marginTop: '30px',
                            '&>button': {
                                width: 190,
                            },
                        }}
                    >
                        <Button
                            disabled={setJobStatusMutation.isLoading}
                            color={Colors.Neutral3}
                            cmosVariant={'filled'}
                            label={t('workshopPlanner.changeJobStatusPopup.cancelButton')}
                            onClick={() => changeJobStatus()}
                        />
                        <Button
                            showLoader={setJobStatusMutation.isLoading}
                            disabled={setJobStatusMutation.isLoading}
                            color={Colors.CM1}
                            cmosVariant={'filled'}
                            onClick={onSelectPhase}
                            label={t('workshopPlanner.changeJobStatusPopup.continueButton')}
                        />
                    </Box>
                }
            />

            <Modal
                open={
                    props.open &&
                    showPhaseSelectorPopup &&
                    (action !== JobTimelineAction.Paused || selectedReasonForPause != null)
                }
            >
                <PhaseSelectorPopupContent>
                    <PhaseSelectorPopupTitle>
                        {t('workshopPlanner.changeJobPhasePopup.title')}
                    </PhaseSelectorPopupTitle>
                    <PhaseSelectorPopupDropdownContainer>
                        <PhasePicker
                            name="phase"
                            placeholder={t('workshopPlanner.changeJobPhasePopup.dropdownLabel')}
                            phaseId={selectedPhase ?? null}
                            disabled={false}
                            onChange={(phaseId) => setSelectedPhase(phaseId)}
                            hidePhaseIds={hidePhaseIds()}
                        />
                    </PhaseSelectorPopupDropdownContainer>
                    <PhaseSelectorPopupButtonsContainer>
                        <PhaseSelectorPopupButton
                            disabled={setJobStatusMutation.isLoading}
                            color={Colors.Neutral3}
                            cmosVariant={'filled'}
                            onClick={onBack}
                            label={t('workshopPlanner.changeJobPhasePopup.cancelButton')}
                        />
                        <ChangePhaseButton
                            orderId={orderKey}
                            orderNumber={orderNumber}
                            jobId={block.id}
                            showLoader={setJobStatusMutation.isLoading}
                            disabled={setJobStatusMutation.isLoading || selectedPhase == null}
                            color={Colors.CM1}
                            cmosVariant={'filled'}
                            label={t('workshopPlanner.changeJobPhasePopup.continueButton')}
                            currentPhaseId={currentPhase.id}
                            selectedPhaseId={selectedPhase!}
                            onClick={changeJobStatus}
                            sx={{ width: 147 }}
                        />
                    </PhaseSelectorPopupButtonsContainer>
                </PhaseSelectorPopupContent>
            </Modal>

            <WarningConfirmationPopup
                open={incompleteInformationErrors.length > 0}
                title={
                    isPendingSignaturePopup
                        ? t('workshopPlanner.stopJobValidationPopup.pendingSignatureTitle')
                        : t('workshopPlanner.stopJobValidationPopup.incompleteInformationTitle')
                }
                body={
                    <StopJobValidationPopupBody>
                        <StopJobValidationPopupDivider />
                        {t('workshopPlanner.stopJobValidationPopup.body') + ':'}
                        <ul>
                            {incompleteInformationErrors.map((e) => {
                                if (e === 'General.WP.Jobs.MandatoryFieldNotSpecified')
                                    return (
                                        <li>
                                            {t(
                                                'workshopPlanner.stopJobValidationPopup.mandatoryFieldNotSpecified'
                                            )}
                                        </li>
                                    );
                                else if (e === 'General.WP.Jobs.NoStandardOperationSpecified')
                                    return (
                                        <li>
                                            {t(
                                                'workshopPlanner.stopJobValidationPopup.noStandardOperationSpecified'
                                            )}
                                        </li>
                                    );
                                else if (e === 'General.WP.Jobs.PendingSignature')
                                    return (
                                        <li>
                                            {t(
                                                'workshopPlanner.stopJobValidationPopup.pendingSignature'
                                            )}
                                        </li>
                                    );
                                else {
                                    return <li>{e}</li>;
                                }
                            })}
                        </ul>
                    </StopJobValidationPopupBody>
                }
                confirm={
                    isPendingSignaturePopup
                        ? t('workshopPlanner.stopJobValidationPopup.ok')
                        : t('workshopPlanner.stopJobValidationPopup.completeInformation')
                }
                onConfirm={() => {
                    onValidationErrors!(incompleteInformationErrors);
                    setIncompleteInformationErrors([]);
                }}
                onClose={() => {
                    setIncompleteInformationErrors([]);
                }}
            />
            <BlockPopupBase
                {...props}
                open={
                    props.open &&
                    action === JobTimelineAction.Paused &&
                    orderPhaseId !== -2 &&
                    selectedReasonForPause === null
                }
            >
                <AreYouSure>{t('workshopPlanner.pauseJobBlock.selectPauseReason')}</AreYouSure>
                <Box sx={{ marginBottom: '10px' }}>
                    <PauseReasonPicker
                        reason={preSelectedReasonForPause}
                        onChange={(pauseReason) => {
                            setPreSelectedReasonForPause(pauseReason);
                        }}
                    />
                </Box>
                <Box display="flex" alignItems="center" justifyContent="center" gap={4}>
                    <Button
                        w={120}
                        cmosSize={'medium'}
                        onClick={props.onClose}
                        label={t('commonLabels.cancel')}
                        color={Colors.Neutral3}
                    />
                    <Button
                        w={120}
                        cmosSize={'medium'}
                        onClick={() => setSelectedReasonForPause(preSelectedReasonForPause)}
                        disabled={preSelectedReasonForPause == null}
                        color={Colors.CM1}
                        label={t('commonLabels.confirm')}
                    />
                </Box>
            </BlockPopupBase>
            <WarningConfirmationPopup
                open={props.open && orderPhaseId === -2}
                title={t('workshopPlanner.closedOrderWarning.title')}
                body={
                    <div>
                        <p>
                            {t('workshopPlanner.closedOrderWarning.body1', {
                                userDisplayName: orderPhaseChangedByUserDisplayName,
                                formattedDateTime:
                                    orderPhaseChangedAt &&
                                    DateTime.fromISO(orderPhaseChangedAt, {
                                        zone: 'utc',
                                    })
                                        .setZone(ianaTz)
                                        .toFormat(t('dateFormats.luxon.shortRightSlashDivider')),
                            })}
                        </p>
                        <p>{t('workshopPlanner.closedOrderWarning.body2')}</p>
                    </div>
                }
                showCloseBtn={false}
                displayDivider={true}
                onClose={props.onClose}
                confirm={t('workshopPlanner.closedOrderWarning.button')}
                onConfirm={props.onClose}
            />
        </>
    );
}

type ChangePhaseButtonProps = {
    orderId: string;
    orderNumber: string;
    jobId: string;
    currentPhaseId: number;
    selectedPhaseId: number;
    onClick: (reason?: string) => void;
} & Omit<ButtonProps, 'onClick'>;

function ChangePhaseButton({
    orderId,
    orderNumber,
    jobId,
    currentPhaseId,
    selectedPhaseId,
    onClick,
    showLoader,
    ...props
}: ChangePhaseButtonProps) {
    const [loading, setLoading] = useState(false);
    const phaseSetbackCheck = usePhaseSetbackCheck();
    const orderJobsInProgressCheck = useOrderJobsInProgressCheck();

    return (
        <Button
            {...props}
            showLoader={showLoader || loading}
            onClick={() => {
                if (currentPhaseId !== selectedPhaseId) {
                    setLoading(true);
                    phaseSetbackCheck.checkPhasesIds(
                        { originPhaseId: currentPhaseId, destinationPhaseId: selectedPhaseId },
                        (reason) =>
                            orderJobsInProgressCheck.checkOrderJobsInProgress(
                                {
                                    destinationPhaseId: selectedPhaseId,
                                    orderId,
                                    orderNumber,
                                    excludedJobIds: [jobId],
                                },
                                () => {
                                    onClick(reason);
                                    setLoading(false);
                                },
                                () => setLoading(false)
                            ),
                        () => setLoading(false)
                    );
                } else {
                    onClick();
                }
            }}
        />
    );
}

const PhaseSelectorPopupContent = styled('div')({
    width: 413,
    padding: '40px 30px 30px',
});

const PhaseSelectorPopupTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    width: 400,
    color: theme.palette.neutral[8],
    textAlign: 'left',
    marginBottom: 30,
}));

const PhaseSelectorPopupDropdownContainer = styled('div')({
    marginBottom: 30,
});

const PhaseSelectorPopupButtonsContainer = styled('div')({
    display: 'flex',
    justifyContent: 'space-around',
});

const PhaseSelectorPopupButton = styled(Button)({
    width: 147,
});

const StopJobValidationPopupDivider = styled('div')({
    width: 270,
    borderBottom: '2px solid #E5E7EA',
    margin: '0 auto',
    marginBottom: '15px',
});

const StopJobValidationPopupBody = styled('div')(({ theme }) => ({
    ...theme.typography.body1,
    textAlign: 'left',
    '& ul': { paddingLeft: 25, marginTop: 5 },
    '& ul li + li': {
        marginTop: 5,
    },
}));

const AreYouSure = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    width: 400,
    color: theme.palette.neutral[7],
    textAlign: 'center',
    margin: '0 0 10px 0',
    boxSizing: 'border-box',
    padding: '0 60px',
}));
