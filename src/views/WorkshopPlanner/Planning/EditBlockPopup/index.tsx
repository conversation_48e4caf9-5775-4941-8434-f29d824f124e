import { Box, Grid, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import WpApi, { StandardOperationDto } from 'api/workshopPlanner';
import { isAxiosError } from 'axios';
import { Button } from 'common/components/Button';
import DateF<PERSON><PERSON>ield from 'common/components/Inputs/DateFormField';
import Duration<PERSON><PERSON><PERSON>ield from 'common/components/Inputs/DurationFormField';
import TimeF<PERSON><PERSON>ield from 'common/components/Inputs/TimeFormField';
import { useSchedulerScrollbarController } from 'common/components/Scheduler/GridCanvas/scrollbarController';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import useLockedBody from 'common/hooks/useLockedBody';
import useToasters from 'common/hooks/useToasters';
import isEqual from 'lodash/isEqual';
import { DateTime } from 'luxon';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { createSelector } from 'reselect';
import { hasCode } from 'services/Server';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { selectRepairShopWpConfiguration } from 'store/slices/globalSettingsSlice';
import { selectUsers } from 'store/slices/users';
import {
    block,
    planningsActions,
    selectBlock,
    selectEditingBlockDurationExceedsBy,
    selectEditingStatePlanningId,
    selectEditingStateTimestamp,
    selectIsEditingStateInvalid,
} from 'store/slices/wp/plannings';
import updateBlockThunk from 'store/slices/wp/plannings/thunks/updateBlock';
import { isDateValid } from 'utils';
import UTInput from 'views/WorkshopPlanner/common/UTInput';
import DraggableBlockPopupBase, {
    DraggableBlockPopupBaseProps,
} from '../BlockPopupBase/DraggableBlockPopupBase';

const ExceedsText = styled('span')(({ theme }) => ({
    marginTop: 10,
    color: 'var(--danger)',
    wordWrap: 'break-word',
    maxWidth: 360,
    display: 'inline-flex',
    ...theme.typography.h6Inter,
}));

type EditBlockPopupProps = DraggableBlockPopupBaseProps & {
    blockId: string;
    planningId: number;
    /**
     * if set to true, does not dispatches updates to update preview
     */
    withoutPreview: boolean;
    orderTypeId?: string;
};

const selectAll = createSelector(
    [(r: RootState) => r, (_, planningId: number) => planningId],
    (r, planningId) => {
        const selectedPlanningId = selectEditingStatePlanningId(r);
        if (selectedPlanningId !== planningId) return null;
        return {
            ts: selectEditingStateTimestamp(r),
            validationState: selectIsEditingStateInvalid(r),
            exceedsBy: selectEditingBlockDurationExceedsBy(r),
        };
    }
);

function EditBlockPopup({
    blockId,
    planningId,
    withoutPreview,
    orderTypeId,
    ...props
}: EditBlockPopupProps) {
    const { t } = useAppTranslation();
    useLockedBody(props.open);
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { block, userName } = useAppSelector((r) => {
        const block = selectBlock(r, blockId);
        let userName: string | null = null;
        if (block) {
            const users = selectUsers(r);
            const user = users[block.userId];
            if (user) userName = user.name;
        }

        return {
            block,
            userName,
        };
    }, isEqual);
    const state = useAppSelector(
        useCallback((r) => selectAll(r, planningId), [planningId]),
        isEqual
    );

    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const { setDate, setTime, setDuration, state: internalState } = useEditBlockState(block);

    const [standardOperations, setStandardOperations] = useState<StandardOperationDto[]>([]);
    const [isLoaded, setIsLoaded] = useState(false);

    useQuery({
        queryKey: ['blockId', block?.id],
        queryFn: async () => await WpApi.getOrderBlockStandardOperations(block?.id!),
        enabled: props.open && !!blockId,
        onSuccess(data) {
            setStandardOperations(
                !data || data.length === 0
                    ? [{ operationCode: '', standardTime: 0, operationCodeDescription: '' }]
                    : data
            );
            setIsLoaded(true);
        },
    });

    const hasOverlap = state?.validationState.hasOverlap ?? false;
    const durationIsInvalid =
        (state?.validationState.durationIsInvalid ?? false) && (state?.exceedsBy ?? 0) > 0;

    const isFormInvalid = durationIsInvalid || hasOverlap;

    const timestampFromState = state?.ts ?? 0;

    const updateBlockMutation = useMutation(
        async () => {
            const result = await dispatch(
                updateBlockThunk({
                    id: blockId,
                    duration: internalState.duration,
                    startsAt: internalState.startsAt.set({ millisecond: 0, second: 0 }).toMillis(),
                    timestamp: Date.now(),
                    // if there is no preview then there is no need to do optimistic updates
                    disableOptimisticUpdate: withoutPreview,
                    standardOperations: standardOperations,
                })
            );
            if (result.meta.requestStatus === 'rejected') throw result.payload;

            return result.payload as WpApi.UpdateBlockResponseDto;
        },
        {
            onSuccess: (data) => {
                if (enableWorkshopJobIntegration && integrationAccountName) {
                    if (!data.thirdPartyError) {
                        toasters.success(
                            t('workshopPlanner.orderPopup.setActionIntegrationSuccess.text', {
                                integrationAccountName,
                            }),
                            t('workshopPlanner.orderPopup.setActionIntegrationSuccess.title')
                        );
                    } else {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.setActionIntegrationError.text', {
                                errorMessage: data.thirdPartyError,
                            }),
                            t('workshopPlanner.orderPopup.setActionIntegrationError.title', {
                                integrationAccountName,
                            })
                        );
                    }
                }

                toasters.success(
                    t('workshopPlanner.editBlock.successNotif.text'),
                    t('workshopPlanner.editBlock.successNotif.title')
                );

                props.onClose();
            },
            onError: (err) => {
                if (isAxiosError(err) && err.response) {
                    if (hasCode(err.response.data, 'General.WP.BlockOverlapAbsence')) {
                        toasters.danger(
                            t('absences.blockOverlapAbsenceErrorBody'),
                            t('absences.blockOverlapAbsenceErrorTitle')
                        );
                    } else if (hasCode(err.response.data, 'General.WP.Schedule.Overlap')) {
                        toasters.danger(
                            t('workshopPlanner.reassignBlock.teamMemberBusyNotif.text'),
                            t('workshopPlanner.reassignBlock.teamMemberBusyNotif.title')
                        );
                    } else if (hasCode(err.response.data, 'General.WP.OutsideOfUserSchedule')) {
                        toasters.danger(
                            t('workshopPlanner.editBlock.outsideOfSchedule.text'),
                            t('workshopPlanner.editBlock.outsideOfSchedule.title')
                        );
                    } else if (hasCode(err.response.data, 'General.WP.Schedule.TooLarge')) {
                        toasters.danger(
                            t('workshopPlanner.editBlock.tooLarge.text'),
                            t('workshopPlanner.editBlock.tooLarge.title')
                        );
                    } else {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                } else {
                    toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                }
            },
        }
    );

    useEffect(() => {
        if (!props.open || !block || internalState.duration <= 0 || withoutPreview) return;
        dispatch(
            planningsActions.setEditingPreview({
                blockId,
                ts: internalState.startsAt.toMillis(),
                duration: internalState.duration,
                planningId,
            })
        );
    }, [internalState, planningId, blockId, props.open, dispatch, block, withoutPreview]);

    useEffect(() => {
        if (!props.open || withoutPreview) return;
        return () => {
            dispatch(planningsActions.removeEditingPreview());
        };
    }, [dispatch, props.open, withoutPreview]);

    const schedulerScrollbarControllerProvider = useSchedulerScrollbarController();

    const blockOffsetXRef = useRef(0);
    useEffect(() => {
        const schedulerScrollbarController = schedulerScrollbarControllerProvider?.scrollbarContext;
        if (!props.open || !schedulerScrollbarController || !props.anchorEl) return () => {};
        const anchorEl = props.anchorEl;
        blockOffsetXRef.current = schedulerScrollbarController.getOffsetFromLeft(anchorEl);
    }, [props.open, schedulerScrollbarControllerProvider, props.anchorEl]);

    useEffect(() => {
        const schedulerScrollbarController = schedulerScrollbarControllerProvider?.scrollbarContext;

        if (
            !props.open ||
            !props.anchorEl ||
            !schedulerScrollbarController ||
            timestampFromState === 0
        )
            return;
        schedulerScrollbarController.scrollToElement(props.anchorEl, {
            offset: blockOffsetXRef.current,
            elementOrigin: 'start',
            schedulerOrigin: 'start',
            smooth: true,
        });
    }, [timestampFromState, props.open, props.anchorEl, schedulerScrollbarControllerProvider]);

    const scrollRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
    }, [standardOperations]);

    if (!isLoaded) return null;
    return (
        <DraggableBlockPopupBase
            disableCloseOnClickOutside
            showCloseButton={block?.type === 'order'}
            {...props}
        >
            <Grid container spacing={1} style={{ width: 440 }}>
                <Grid item xs={6}>
                    <DateFormField
                        isRequired
                        showValidationIndicators
                        label={t('workshopPlanner.editBlock.startDate')}
                        enableEnterComplete
                        value={internalState.startsAt.toJSDate()}
                        onChange={(v) => {
                            if (v && isDateValid(v)) setDate(v);
                        }}
                    />
                </Grid>

                <Grid item xs={6}>
                    <TimeFormField
                        isRequired
                        showValidationIndicators
                        value={[internalState.startsAt.hour, internalState.startsAt.minute]}
                        isInvalid={hasOverlap}
                        onChange={setTime}
                        label={t('workshopPlanner.editBlock.startHour')}
                    />
                </Grid>

                <Grid item xs={6}>
                    <DurationFormField
                        isRequired
                        showValidationIndicators
                        isInvalid={durationIsInvalid}
                        value={internalState.duration}
                        onChange={setDuration}
                        label={t('commonLabels.duration')}
                        name="duration"
                    />
                </Grid>
                <Grid item xs={6}>
                    <UTInput
                        isInvalid={durationIsInvalid}
                        durationInSeconds={internalState.duration * 60}
                        onChange={(v) => {
                            setDuration(Math.floor(v / 60));
                        }}
                    />
                </Grid>
            </Grid>
            {Boolean(state?.exceedsBy) && (
                <ExceedsText>{t('workshopPlanner.schedulePopup.exceedsDuration')}</ExceedsText>
            )}
            {block?.isUnassigned && (
                <DivUnassignedBlockEditingHint>
                    <Trans
                        i18nKey="workshopPlanner.schedulePopup.unassignedHint"
                        components={{
                            bold: <b />,
                        }}
                        values={{ user: userName }}
                    />
                </DivUnassignedBlockEditingHint>
            )}
            <Box
                marginTop={2}
                marginLeft={0}
                display="flex"
                alignItems="center"
                justifyContent="center"
                gap={1}
            >
                <Button
                    w={140}
                    cmosSize={'small'}
                    onClick={props.onClose}
                    disabled={updateBlockMutation.isLoading}
                    label={t('commonLabels.cancel')}
                    color={'var(--neutral3)'}
                />
                <Button
                    w={140}
                    cmosSize={'small'}
                    showLoader={updateBlockMutation.isLoading}
                    onClick={() => {
                        updateBlockMutation.mutate();
                    }}
                    disabled={updateBlockMutation.isLoading || isFormInvalid}
                    label={t('commonLabels.save')}
                />
            </Box>
        </DraggableBlockPopupBase>
    );
}

const DivUnassignedBlockEditingHint = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    marginTop: 10,
    marginBottom: 10,
}));

/**
 * Custom hook for managing the state of a block, including its start time, duration, and methods for setting the duration, date, and time.
 *
 * This implementation uses a ref to store the state of the block. This helps with issues caused by regular useState not updating immediately
 * (which causes unnecessary re-renders and some weird bugs)
 */
function useEditBlockState(block: block.State | null) {
    const fr = useForceRender();
    const stateRef = useRef<{
        startsAt: DateTime;
        duration: number;
    }>({
        startsAt: DateTime.now(),
        duration: 0,
    });

    const blockRef = useRef<block.State | null>(null);

    if (blockRef.current !== block && block !== null) {
        blockRef.current = block;
        stateRef.current = {
            startsAt: DateTime.fromMillis(block.startsAt),
            duration: block.duration,
        };
    }

    return {
        state: stateRef.current,
        setDuration: useCallback(
            (value: number) => {
                stateRef.current = {
                    ...stateRef.current,
                    duration: value,
                };
                fr();
            },
            [fr]
        ),
        setDate: useCallback(
            (value: Date) => {
                stateRef.current = {
                    ...stateRef.current,
                    startsAt: stateRef.current.startsAt.set({
                        day: value.getDate(),
                        month: value.getMonth() + 1,
                        year: value.getFullYear(),
                    }),
                };
                fr();
            },
            [fr]
        ),
        setTime: useCallback(
            (value: [number, number]) => {
                stateRef.current = {
                    ...stateRef.current,
                    startsAt: stateRef.current.startsAt.set({
                        hour: value[0],
                        minute: value[1],
                    }),
                };
                fr();
            },
            [fr]
        ),
    };
}

export default memo(EditBlockPopup);
