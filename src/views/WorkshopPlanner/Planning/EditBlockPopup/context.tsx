import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import EditBlockPopup from '.';
import { usePlanningId } from '..';

interface IEditBlockPopupContext {
    /**
     * @param blockId id of the block to edit
     * @param withoutPreview if set to true, will disable block editing preview
     */
    open(parameters: {
        blockId: string;
        withoutPreview?: boolean;
        anchorElement?: HTMLElement;
    }): void;
    setAnchorEl(element: HTMLElement | null): void;
}

const EditBlockPopupContext = createContext<IEditBlockPopupContext | null>(null);

export function useEditBlockPopupContext() {
    const ctx = useContext(EditBlockPopupContext);
    if (!ctx) throw new Error('EditBlockPopupContext is not available');
    return ctx;
}

export default function EditBlockPopupContextProvider({ children }: React.PropsWithChildren<{}>) {
    const [editState, setEditState] = useState<{
        blockId: string | null;
        withoutPreview: boolean;
        anchorEl: HTMLElement | null;
    }>({
        blockId: null,
        withoutPreview: false,
        anchorEl: null,
    });
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const planningId = usePlanningId();
    const ctx: IEditBlockPopupContext = useMemo(
        () => ({
            open: (parameters) => {
                setEditState({
                    blockId: parameters.blockId,
                    withoutPreview: parameters.withoutPreview ?? false,
                    anchorEl: parameters.anchorElement ?? null,
                });
            },
            setAnchorEl,
        }),
        []
    );
    const onClose = useCallback(
        () =>
            setEditState({
                blockId: null,
                withoutPreview: false,
                anchorEl: null,
            }),
        []
    );

    return (
        <EditBlockPopupContext.Provider value={ctx}>
            <EditBlockPopup
                anchorEl={editState.anchorEl ?? anchorEl}
                onClose={onClose}
                planningId={planningId}
                open={!!editState.blockId && !!planningId}
                blockId={editState.blockId ?? ''}
                withoutPreview={editState.withoutPreview}
            />
            {children}
        </EditBlockPopupContext.Provider>
    );
}
