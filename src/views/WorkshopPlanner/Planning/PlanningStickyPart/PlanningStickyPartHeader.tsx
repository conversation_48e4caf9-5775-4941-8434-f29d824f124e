import { styled } from '@mui/material';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import theme from 'theme';

const DivRowHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[9],
    fontWeight: 'normal',
    display: 'flex',
    alignItems: 'center',
    background: theme.palette.neutral[1],
    height: '100%',
}));

const StyledClockIcon = styled(ClockIcon)(({ theme }) => ({
    color: theme.palette.error.main,
    marginRight: 2,
}));

export type PlanningStickyPartHeaderProps = {
    vertical: boolean;
};

export default function PlanningStickyPartHeader({ vertical }: PlanningStickyPartHeaderProps) {
    const { t } = useAppTranslation();
    return (
        <DivRowHeader
            style={
                vertical
                    ? {
                          justifyContent: 'center',
                          borderLeft: `3px solid ${theme.palette.custom.gray}`,
                      }
                    : {
                          paddingLeft: 11,
                          borderTop: `3px solid ${theme.palette.custom.gray}`,
                      }
            }
        >
            <StyledClockIcon size={18} />
            {t('workshopPlanner.didNotArrive.title')}
        </DivRowHeader>
    );
}
