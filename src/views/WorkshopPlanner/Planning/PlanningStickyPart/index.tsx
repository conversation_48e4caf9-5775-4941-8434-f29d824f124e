import { styled } from '@mui/material';
import Cursor from 'common/components/Scheduler/GridCanvasCells/Cursor';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import moment from 'moment';
import React, { CSSProperties, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { RootState, useAppSelector } from 'store';
import { BlockView, selectUnassignedPlanningBlocks } from 'store/slices/wp/plannings';
import {
    selectFilteredAppointmentIds,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import theme from 'theme';
import FakeGrid from 'views/WorkshopPlanner/common/FakeGrid';
import { usePlanningId } from '..';
import PlanningBlock, { FilteredBlockView } from '../PlanningBlock';
import { DroppingBlockContext } from '../droppingBlockContext';
import PlanningStickyPartHeader from './PlanningStickyPartHeader';

export { PlanningStickyPartHeader };

export type PlanningStickyPartProps = {
    vertical: boolean;
    singleView: boolean;
};

export default function PlanningStickyPart({ vertical, singleView }: PlanningStickyPartProps) {
    return (
        <DivRoot
            style={
                vertical
                    ? {
                          width: '100%',
                          borderLeft: `3px solid ${theme.palette.custom.gray}`,
                      }
                    : {
                          borderTop: `3px solid ${theme.palette.custom.gray}`,
                      }
            }
        >
            <FakeGrid vertical={vertical} />
            <DidNotArriveItems vertical={vertical} singleView={singleView} />
            <Cursor vertical={vertical} />
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    position: 'relative',
    height: '100%',
});

type DidNotArriveItemsProps = {
    vertical: boolean;
    singleView: boolean;
};

function DidNotArriveItems({ vertical, singleView }: DidNotArriveItemsProps) {
    const planningId = usePlanningId();
    const selector = useCallback(
        (r: RootState) => {
            const filteredOrderIds = selectFilteredOrderIds(r);
            const filteredAppointmentIds = selectFilteredAppointmentIds(r);

            return selectUnassignedPlanningBlocks(r, planningId).map((x) =>
                x.type === 'order'
                    ? {
                          ...x,
                          isFiltered:
                              filteredOrderIds.includes(x.order.id) ||
                              (!!x.order.appointmentId &&
                                  filteredAppointmentIds.includes(x.order.appointmentId)),
                      }
                    : {
                          ...x,
                          isFiltered:
                              filteredAppointmentIds.includes(x.appointment.id) ||
                              (!!x.appointment.orderId &&
                                  filteredOrderIds.includes(x.appointment.orderId)),
                      }
            );
        },
        [planningId]
    );
    const blocks = useAppSelector(selector);
    const ctx = useSchedulerContext();
    const droppingBlockCtx = useContext(DroppingBlockContext);

    const fromTimestamp = +moment(ctx.interval.from).toDate();

    const [isDragging, setIsDragging] = useState(false);

    const blockGroups = useMemo(() => {
        const groups: FilteredBlockView[][] = [];

        for (let i = 0; i < blocks.length; i++) {
            const a = blocks[i];
            if (i === 0) {
                groups.push([a]);
            } else {
                const b = blocks[i - 1];
                if (!doesOverlap(a, b)) {
                    groups.push([a]);
                } else {
                    groups[groups.length - 1].push(a);
                }
            }
        }

        return groups;
    }, [blocks]);

    useEffect(() => {
        const callback = () => {
            setIsDragging(false);
        };

        document.addEventListener('dragend', callback);
        return () => document.removeEventListener('dragend', callback);
    }, []);

    return (
        <>
            {blockGroups.map((group) => {
                return (
                    <React.Fragment key={group.map((b) => b.id).join('_')}>
                        {group.map((block, index) => {
                            const nextBlock = index >= group.length - 1 ? null : group[index + 1];
                            const availableTime = nextBlock
                                ? nextBlock.startsAt - block.startsAt
                                : block.duration * 60000;
                            const blockStartCoord =
                                ((block.startsAt - fromTimestamp) / 60000) *
                                ctx.dimensions.pixelsPerMinuteRatio;
                            const blockSize = block.duration * ctx.dimensions.pixelsPerMinuteRatio;

                            return (
                                <DivBlockOuter
                                    key={block.id}
                                    unselectable="on"
                                    draggable="true"
                                    style={
                                        {
                                            ...(vertical
                                                ? {
                                                      '--planning-block-menu-offset':
                                                          ctx.dimensions.sideWidth +
                                                          ctx.dimensions.columnWidth *
                                                              blockGroups.length +
                                                          4,
                                                      top: `${blockStartCoord}px`,
                                                      left: 0,
                                                      height: `${blockSize}px`,
                                                      width: '100%',
                                                  }
                                                : {
                                                      '--planning-block-menu-offset':
                                                          (availableTime / 60000) *
                                                              ctx.dimensions.pixelsPerMinuteRatio +
                                                          4,
                                                      top: 0,
                                                      left: `${blockStartCoord}px`,
                                                      width: `${blockSize}px`,
                                                      height: '100%',
                                                  }),
                                        } as CSSProperties
                                    }
                                    onDragStart={() => {
                                        setIsDragging(true);
                                        droppingBlockCtx?.set(block.id);
                                    }}
                                    data-block-id={block.id}
                                >
                                    <PlanningBlock
                                        block={block}
                                        planningId={planningId}
                                        outerContainer={null}
                                        disableTooltip={isDragging}
                                        vertical={vertical}
                                        singleView={singleView}
                                    />
                                </DivBlockOuter>
                            );
                        })}
                    </React.Fragment>
                );
            })}
        </>
    );
}

const DivBlockOuter = styled('div')({
    position: 'absolute',
});

function doesOverlap(a: BlockView, b: BlockView): boolean {
    const aEnd = a.startsAt + a.duration * 60000;
    const bEnd = b.startsAt + b.duration * 60000;

    return a.startsAt < bEnd && b.startsAt < aEnd;
}
