import { styled } from '@mui/styles';
import { LightIcon } from 'common/components/Icons/LightIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppSelector } from 'store';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';

const HighlightsAnnotation = () => {
    const { t } = useAppTranslation();
    const wpSettings = useAppSelector(selectShopWpSettings);

    return (
        <DivLightsLegendsContainer>
            {wpSettings?.urgentFlashingIndicationEnabled && (
                <DivLightLegendWrapper>
                    <LightIcon fill="var(--yellow)" />
                    {wpSettings?.urgentFlashingIndicationExplanatoryText}
                </DivLightLegendWrapper>
            )}

            <DivLightLegendWrapper>
                <LightIcon fill="#FA966B" />
                {t('workshopPlanner.redHighlightAnnotation')}
            </DivLightLegendWrapper>
        </DivLightsLegendsContainer>
    );
};

export default HighlightsAnnotation;

const DivLightsLegendsContainer = styled('div')({
    display: 'flex',
    justifyContent: 'flex-start',
});

const DivLightLegendWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    marginLeft: '10px',
    color: theme.palette.neutral[7],
}));
