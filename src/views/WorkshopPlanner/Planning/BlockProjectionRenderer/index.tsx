import { styled } from '@mui/material';
import { ScheduleProjectionItemProps } from 'common/components/Scheduler/addons/Projection';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { useRef } from 'react';
import { BlockProjection } from 'store/slices/wp/plannings';
import AbsenceBlock from 'views/Components/AbsenceBlock';
import { useEditAbsencePopup } from 'views/absence/EditAbsence/context';
import { PreviewProjection } from '../PreviewProjection';
import EditBlockProjection from './EditBlockProjection';

const Root = styled('div')({
    position: 'absolute',
    boxSizing: 'border-box',
});

export default function BlockProjectionRenderer({
    value,
    top,
    left,
    width,
    height,
    outerContainer,
    vertical,
    singleView,
}: ScheduleProjectionItemProps<BlockProjection>) {
    let inner: React.ReactNode;
    let zIndex = 1;

    const anchorElRef = useRef<HTMLDivElement | null>(null);
    const {
        dimensions: { rowHeight },
    } = useSchedulerContext();
    const editAbsencePopup = useEditAbsencePopup();

    if (value.type === 'editPreview') {
        inner = (
            <EditBlockProjection
                projection={value}
                outerContainer={outerContainer}
                vertical={vertical}
                singleView={singleView}
            />
        );
        zIndex = 2;
    } else if (value.type === 'preview') {
        inner = (
            <PreviewProjection
                duration={value.duration}
                requestedDuration={value.requestedDuration}
                vertical={vertical}
            />
        );
        zIndex = 2;
    } else if (value.type === 'absence') {
        const editAbsence = () => {
            if (anchorElRef.current) {
                editAbsencePopup.open(value.absence, anchorElRef.current);
            } else {
                console.warn('no anchor element found for the popup');
            }
        };

        inner = (
            <AbsenceBlock
                availableVerticalSpace={rowHeight}
                absence={value.absence}
                leftOffset={140}
                topOffset={65}
                vertical={vertical}
                onClick={editAbsence}
            />
        );
    }

    return (
        <Root
            data-test-id={`planningProjection-${value.type}`}
            data-value-type={value.type}
            style={{
                width,
                height,
                zIndex,
                transform: `translate(${left}px, ${top}px)`,
            }}
            ref={anchorElRef}
        >
            {inner}
        </Root>
    );
}
