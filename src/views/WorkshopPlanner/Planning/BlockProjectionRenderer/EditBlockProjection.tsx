import { styled } from '@mui/material';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { rgba } from 'common/styles/ColorHelpers';
import { useCallback, useMemo } from 'react';
import { useAppSelector } from 'store';
import { BlockProjection, selectEditingIntersection } from 'store/slices/wp/plannings';
import { usePlanningId } from '..';
import { useEditBlockPopupContext } from '../EditBlockPopup/context';
import PlanningBlock from '../PlanningBlock';

type EditBlockProjectionProps = {
    projection: BlockProjection & { type: 'editPreview' };
    outerContainer?: HTMLElement | null;
    vertical: boolean;
    singleView: boolean;
};

const DivRoot = styled('div')({
    borderRadius: 5,
    outline: '2px solid var(--cm2)',
    outlineOffset: -1,
    height: '100%',
});

const DivIntersection = styled('div')(({ theme }) => ({
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    backgroundImage: `repeating-linear-gradient(45deg, ${rgba(
        theme.palette.error.main,
        0.6
    )}, ${rgba(theme.palette.error.main, 0.6)} 20px, ${rgba(
        theme.palette.error.main,
        0.2
    )} 20px, ${rgba(theme.palette.error.main, 0.1)} 40px)`,
}));

export function getBlockEditProjectionId(blockId: string, planningId: number): string {
    return `WP(planning=${planningId}_blockId=${blockId})`;
}

export default function EditBlockProjection({
    projection,
    outerContainer,
    vertical,
    singleView,
}: EditBlockProjectionProps) {
    const intersections = useAppSelector(selectEditingIntersection);
    const { dimensions } = useSchedulerContext();
    const editBlockPopup = useEditBlockPopupContext();
    const planningId = usePlanningId();
    const setEditBlockAnchorElement = useCallback(
        (el: HTMLDivElement) => {
            editBlockPopup.setAnchorEl(el);
        },
        [editBlockPopup]
    );

    const renderedIntersections = useMemo(
        () =>
            intersections.map((b) => {
                const startPosition =
                    ((b.start.toMillis() - projection.startsAt) / 60000) *
                    dimensions.pixelsPerMinuteRatio;
                const durationSize =
                    dimensions.pixelsPerMinuteRatio *
                    ((b.end.toMillis() - b.start.toMillis()) / 60000);

                return (
                    <DivIntersection
                        key={b.end.toMillis()}
                        style={{
                            transform: vertical
                                ? `translateY(${startPosition}px)`
                                : `translateX(${startPosition}px)`,
                            width: vertical ? 'calc(var(--wps-column-width) - 6px)' : durationSize,
                            height: vertical ? durationSize : 'calc(var(--wps-row-height) - 6px)',
                        }}
                    />
                );
            }),
        [intersections, projection.startsAt, dimensions.pixelsPerMinuteRatio, vertical]
    );

    return (
        <DivRoot ref={setEditBlockAnchorElement}>
            <PlanningBlock
                ignoreHiddenFlag
                block={projection.block}
                planningId={planningId}
                outerContainer={outerContainer ?? null}
                vertical={vertical}
                singleView={singleView}
            />
            {renderedIntersections}
        </DivRoot>
    );
}
