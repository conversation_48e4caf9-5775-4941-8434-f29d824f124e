import { Fullscreen } from '@mui/icons-material';
import { IconButton, styled } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useNavigate } from 'react-router-dom';
import TeamMembersSelector from 'views/WorkshopPlanner/Filter/TeamMembersSelector';
import TechniciansLegend from 'views/WorkshopPlanner/TechnicianCapacity/TechniciansLegend';
import HighlightsAnnotation from '../HighlightsAnnotation';

type PlanningHeaderProps = {
    planningId: number;
    planningType: 'Advisors' | 'Technicians' | 'Custom';
    isSingleView?: boolean;
    showLegend?: boolean;
    onLoadingChange?: (isLoading: boolean) => void;
};

export default function PlanningHeader({
    planningId,
    isSingleView = false,
    planningType,
    showLegend = false,
}: PlanningHeaderProps) {
    const { t } = useAppTranslation();
    const navigate = useNavigate();

    if (isSingleView) return;

    const Legend = () => {
        switch (planningType) {
            case 'Technicians':
                return <TechniciansLegend />;
            case 'Advisors':
                return (
                    <SchedulerHeader>
                        <TextWrapper>
                            <GreenLine />
                            <HeaderText style={{ marginRight: 12 }}>
                                {t('workshopPlanner.appointmentConfirmedOrOrderCreated')}
                            </HeaderText>
                            <RedLine />
                            <HeaderText>{t('workshopPlanner.customerDidNotArrive')}</HeaderText>
                        </TextWrapper>
                    </SchedulerHeader>
                );
            default:
                return null;
        }
    };

    return (
        <Root>
            <div>
                {planningType !== 'Advisors' && <HighlightsAnnotation />}
                {showLegend && <Legend />}
            </div>

            <FilterWithSingleView>
                <TeamMembersSelector planningId={planningId} />
                <ArrowTooltip content={t('commonLabels.project')}>
                    <IconButton
                        size="small"
                        sx={{ padding: 0, ml: '7px', color: '#0069FF' }}
                        onClick={() => {
                            navigate(
                                planningType === 'Advisors'
                                    ? ROUTES.WORKSHOP_PLANNER_SINGLE_VIEW_ADVISORS
                                    : planningType === 'Technicians'
                                    ? ROUTES.WORKSHOP_PLANNER_SINGLE_VIEW_TECHNICIANS
                                    : `${ROUTES.WORKSHOP_PLANNER_SINGLE_VIEW}?planning=${planningId}`
                            );
                        }}
                    >
                        <Fullscreen />
                    </IconButton>
                </ArrowTooltip>
            </FilterWithSingleView>
        </Root>
    );
}

const Root = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'start',
    marginBottom: 5,
    marginTop: 10,
});

const FilterWithSingleView = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 5,
});

const SchedulerHeader = styled('div')({
    display: 'flex',
    width: '100%',
    justifyContent: 'flex-end',
    marginBottom: 10,
});

const HeaderText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));

const TextWrapper = styled('div')({ display: 'flex', alignItems: 'center' });

const GreenLine = styled('div')({ borderTop: '2px solid #36CE91', width: 16, marginRight: 5 });

const RedLine = styled('div')({ borderTop: '2px solid #F15857', width: 16, marginRight: 5 });
