import { styled } from '@mui/material';
import { useSchedulerScrollbarController } from 'common/components/Scheduler/GridCanvas/scrollbarController';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { Duration } from 'luxon';
import { useEffect, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectBlockPreviewTime } from 'store/slices/wp/plannings';

const Root = styled('div')(({ theme }) => ({
    backgroundColor: rgba(theme.palette.primary.main, 0.1),
    borderRadius: 10,
    border: `2px dashed ${theme.palette.primary.main}`,
    boxSizing: 'border-box',
    display: 'flex',
    justifyContent: 'left',
    alignItems: 'start',
    color: theme.palette.primary.main,
    ...theme.typography.h6Inter,
    overflow: 'hidden',
    position: 'relative',
}));

const BlockContentWrapper = styled('div')({
    margin: 10,
});

const OverflowAppendix = styled('div')(({ theme }) => ({
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: 500,
    backgroundImage: `repeating-linear-gradient(45deg, ${rgba(
        theme.palette.error.main,
        0.6
    )}, ${rgba('var(--danger)', 0.6)} 20px, ${rgba(theme.palette.error.main, 0.2)} 20px, ${rgba(
        theme.palette.error.main,
        0.1
    )} 40px)`,
}));

type PreviewProjectionProps = {
    duration: number;
    requestedDuration: number;
    vertical: boolean;
};

export function PreviewProjection({
    duration,
    requestedDuration,
    vertical,
}: PreviewProjectionProps) {
    const ctx = useSchedulerContext();
    const fullSize = ctx.dimensions.pixelsPerMinuteRatio * requestedDuration;
    const allowedSize = ctx.dimensions.pixelsPerMinuteRatio * duration;
    const { t } = useAppTranslation();
    const [rootEl, setRootEl] = useState<HTMLDivElement | null>(null);
    const scrollOffsetRef = useRef<number>(-1);
    const scrollControllerProvider = useSchedulerScrollbarController();
    const { time, disableAutomaticInitialPosition } = useAppSelector(selectBlockPreviewTime);

    useEffect(() => {
        const scrollController = scrollControllerProvider?.scrollbarContext;
        if (!scrollController || !rootEl) return;
        scrollOffsetRef.current = -1;

        if (disableAutomaticInitialPosition) {
            scrollOffsetRef.current = vertical
                ? ctx.dimensions.headerHeight
                : ctx.dimensions.sideWidth;
        } else {
            const update = () => {
                window.requestAnimationFrame(() => {
                    scrollOffsetRef.current = vertical
                        ? scrollController.getOffsetFromTop(rootEl)
                        : scrollController.getOffsetFromLeft(rootEl);
                });
            };

            // for some reason scrollController.getOffsetFromLeft returns nonsensical values sometime like -31231243245
            // probably by the time we call it rootEl is not positioned in the dom or something, idk
            //
            // if I was smarter and cared more I would have probably debugged the crap out of this
            // and figured out when exactly we need to call update to get proper value,
            // but why do that when we can just call it multiple times and hope it works
            update();
            setTimeout(update, 100);
            setTimeout(update, 300);
        }
    }, [
        scrollControllerProvider?.scrollbarContext,
        rootEl,
        ctx.dimensions.sideWidth,
        ctx.dimensions.headerHeight,
        disableAutomaticInitialPosition,
        vertical,
    ]);

    useEffect(() => {
        const scrollController = scrollControllerProvider?.scrollbarContext;

        if (scrollController && rootEl && scrollOffsetRef.current >= 0) {
            let offset = scrollOffsetRef.current;

            if (disableAutomaticInitialPosition) {
                const areaSize = vertical
                    ? scrollController.getAreaHeight()
                    : scrollController.getAreaWidth();
                offset += (areaSize - scrollOffsetRef.current) / 4;
            }

            scrollController.scrollToElement(rootEl, {
                elementOrigin: 'start',
                schedulerOrigin: 'start',
                offset,
                smooth: true,
            });
        }
    }, [
        time,
        disableAutomaticInitialPosition,
        scrollControllerProvider?.scrollbarContext,
        rootEl,
        ctx.dimensions.sideWidth,
        vertical,
    ]);

    return (
        <Root
            ref={setRootEl}
            style={{
                visibility: requestedDuration > 0 ? 'visible' : 'hidden',
                ...(vertical
                    ? { height: fullSize, minHeight: fullSize, maxHeight: fullSize, width: '90%' }
                    : { width: fullSize, minWidth: fullSize, maxWidth: fullSize, height: '90%' }),
            }}
        >
            <BlockContentWrapper>
                {t('workshopPlanner.schedulePopup.preview')} <br />
                {Duration.fromDurationLike({ minutes: requestedDuration }).toHuman({})}
                {fullSize > allowedSize && (
                    // subtract 2px to account for the border
                    <OverflowAppendix
                        style={{
                            transform: vertical
                                ? `translateY(${allowedSize - 2}px)`
                                : `translateX(${allowedSize - 2}px)`,
                        }}
                    />
                )}
            </BlockContentWrapper>
        </Root>
    );
}
