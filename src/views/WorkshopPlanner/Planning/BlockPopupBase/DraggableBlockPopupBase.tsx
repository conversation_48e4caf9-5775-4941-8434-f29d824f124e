import { Backdrop, IconButton, Paper, styled } from '@mui/material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import useLockedBody from 'common/hooks/useLockedBody';
import { rgba } from 'common/styles/ColorHelpers';
import { IconSize } from 'common/styles/IconSize';
import { useCallback, useEffect, useMemo, useState } from 'react';
import ReactDOM, { flushSync } from 'react-dom';
import Draggable, { ControlPosition } from 'react-draggable';
import { BlockPopupBaseProps } from '.';

export type DraggableBlockPopupBaseProps = Omit<
    BlockPopupBaseProps,
    'onPositionUpdated' | 'actionsRef'
>;

export default function DraggableBlockPopupBase({
    children,
    open,
    anchorEl,
    onClose,
    disableCloseOnClickOutside,
    showCloseButton = false,
    slotProps,
}: DraggableBlockPopupBaseProps) {
    const [position, setPosition] = useState<ControlPosition>({
        x: 0,
        y: 0,
    });
    const [isDragged, setIsDragged] = useState(false);
    const [popoverRef, setPopoverRef] = useState<HTMLDivElement | null>(null);
    const draggableId = useMemo(
        () => `draggable-schedule-popup-handle-${Math.random().toString(36).substring(2)}`,
        []
    );
    useLockedBody(open);

    const updatePosition = useCallback(() => {
        if (!popoverRef || !anchorEl || isDragged) {
            return;
        }

        const rect = anchorEl.getBoundingClientRect();
        let popoverRect = popoverRef.getBoundingClientRect();
        const pos = {
            y: rect.top - popoverRect.height - 15,
            x: rect.left + rect.width / 2 - popoverRect.width / 2,
        };

        if (pos.x < 0) {
            pos.x = 0;
        } else if (pos.x + popoverRect.width > document.documentElement.clientWidth) {
            pos.x = document.documentElement.clientWidth - popoverRect.width;
        }

        popoverRect = popoverRef.getBoundingClientRect();
        if (popoverRect.top < 0) {
            pos.y += popoverRect.height + rect.height + 15 * 2;
        }

        setPosition(pos);
        popoverRef.style.transform = `translate(${pos.x}px, ${pos.y}px)`;
    }, [popoverRef, anchorEl, isDragged]);

    useEffect(() => {
        if (open && anchorEl) {
            updatePosition();
        }
    }, [updatePosition, open, anchorEl]);

    useEffect(() => {
        if (!popoverRef || isDragged) return;

        const observer = new ResizeObserver(() => {
            updatePosition();
        });

        observer.observe(popoverRef);
        return () => observer.disconnect();
    }, [popoverRef, updatePosition, isDragged]);

    const handleClose = () => {
        setIsDragged(false);
        onClose();
    };

    return ReactDOM.createPortal(
        <SBackdrop
            onClick={(e) => {
                if (e.target === e.currentTarget) {
                    // close only if use clicked on backdrop directly (propagated click event does not count)
                    if (!disableCloseOnClickOutside) handleClose();
                }
            }}
            invisible
            open={open && !!anchorEl}
        >
            {open && (
                <Draggable
                    position={position}
                    enableUserSelectHack={false}
                    onDrag={(_, { x, y }) => {
                        setIsDragged(true);
                        // NOTE (MB) prevents popup from lagging behind on low-end devices but hurts overall performance while dragging
                        // i.e. this is the "lesser evil" but it's still pretty bad (if you have 2010 laptop)
                        flushSync(() => setPosition({ x, y }));
                    }}
                    bounds="body"
                    handle={'#' + draggableId}
                >
                    <StyledPaper
                        ref={setPopoverRef}
                        role="dialog"
                        elevation={0}
                        onClick={(e) => e.stopPropagation()}
                        {...slotProps?.paper}
                    >
                        <Header id={draggableId}>
                            {showCloseButton && (
                                <CloseButton onClick={handleClose} size="small">
                                    <CloseIcon fill="var(--neutral6)" size={IconSize.M} />
                                </CloseButton>
                            )}
                        </Header>
                        <Main>{children}</Main>
                    </StyledPaper>
                </Draggable>
            )}
        </SBackdrop>,
        document.body
    );
}

const SBackdrop = styled(Backdrop)({
    left: 0,
    top: 0,
    position: 'fixed',
    height: '100vh',
    zIndex: 100,
});

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: 0,
    boxShadow: '0 4px 10px -2px var(--neutral4)',
    borderRadius: 10,
    marginBottom: 10,
    overflow: 'initial',
    position: 'fixed',
    left: 0,
    top: 0,
    zIndex: 1,

    '&.react-draggable-dragging': {
        outline: `4px solid ${rgba(theme.palette.neutral[9], 0.5)}`,
        userSelect: 'none !important',
    },
}));

const Header = styled('header')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[3],
    padding: '15px 30px',
    position: 'relative',
    paddingRight: 60,
    cursor: 'move',
    borderRadius: '10px 10px 0 0',
}));

const CloseButton = styled(IconButton)({
    position: 'absolute',
    top: 0,
    right: 8,
});

const Main = styled('main')({
    padding: '0 19px 19px 19px',
});
