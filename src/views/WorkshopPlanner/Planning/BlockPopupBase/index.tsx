import { Backdrop, IconButton, Paper, styled } from '@mui/material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import useLockedBody from 'common/hooks/useLockedBody';
import { IconSize } from 'common/styles/IconSize';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

export type BlockPopupBaseActions = {
    updatePosition: () => void;
};

export type BlockPopupBaseProps = React.PropsWithChildren<{
    anchorEl?: HTMLElement | null;
    open: boolean;
    onClose: () => void;
    actionsRef?: React.MutableRefObject<BlockPopupBaseActions | null>;
    onPositionUpdated?: () => void;
    disableCloseOnClickOutside?: boolean;
    showCloseButton?: boolean;
    slotProps?: {
        paper?: React.HTMLAttributes<HTMLElement>;
    };
}>;

const SBackdrop = styled(Backdrop)({
    left: 0,
    top: 0,
    position: 'fixed',
    height: '100vh',
    zIndex: 100,
});

const Chevron = styled('div')({
    position: 'absolute',
    height: 20,
    width: 20,
    backgroundColor: '#fff',
    bottom: -10,
    left: '50%',
    border: '1px solid var(--neutral3)',
    borderWidth: '0 1px 1px 0',
});

export default function BlockPopupBase({
    children,
    open,
    anchorEl,
    onClose,
    actionsRef,
    onPositionUpdated,
    disableCloseOnClickOutside,
    showCloseButton = false,
    slotProps,
}: BlockPopupBaseProps) {
    const [position, setPosition] = useState({
        top: 0,
        left: 0,
    });
    const [popoverRef, setPopoverRef] = useState<HTMLDivElement | null>(null);
    const [chevronRef, setChevronRef] = useState<HTMLDivElement | null>(null);
    const onPositionUpdatedRef = useRef(onPositionUpdated);
    onPositionUpdatedRef.current = onPositionUpdated;
    useLockedBody(open);

    const updatePosition = useCallback(() => {
        if (!popoverRef || !anchorEl) {
            return;
        }

        const rect = anchorEl.getBoundingClientRect();
        let popoverRect = popoverRef.getBoundingClientRect();
        let topChevron = false;
        const pos = {
            top: rect.top - popoverRect.height - 15,
            left: rect.left + rect.width / 2 - popoverRect.width / 2,
        };
        let chevronOffset = 0;
        if (pos.left < 0) {
            chevronOffset = pos.left;
            pos.left = 0;
        } else if (pos.left + popoverRect.width > document.documentElement.clientWidth) {
            chevronOffset = pos.left + popoverRect.width - document.documentElement.clientWidth;
            pos.left = document.documentElement.clientWidth - popoverRect.width;
        }
        const oldTransform = popoverRef.style.transform;
        popoverRef.style.transform = `translate(${pos.left}px, ${pos.top}px)`;
        popoverRect = popoverRef.getBoundingClientRect();
        popoverRef.style.transform = oldTransform;
        if (popoverRect.top < 0) {
            topChevron = true;
            pos.top += popoverRect.height + rect.height + 15 * 2;
        }
        setPosition(pos);
        if (topChevron) {
            popoverRef.classList.add('top-chevron');
        } else {
            popoverRef.classList.remove('top-chevron');
        }
        if (chevronRef) {
            chevronRef.style.transform = `translate(calc(${chevronOffset}px - 50%), ${
                topChevron ? -1 : 1
            }px) rotate(${topChevron ? -135 : 45}deg)`;
        }
        if (onPositionUpdatedRef.current) onPositionUpdatedRef.current();
    }, [popoverRef, chevronRef, anchorEl]);

    useEffect(() => {
        if (open && anchorEl) {
            updatePosition();
        }
    }, [updatePosition, open, anchorEl]);

    if (actionsRef) {
        actionsRef.current = { updatePosition };
    }

    useEffect(() => {
        if (!popoverRef) return;

        const observer = new ResizeObserver(() => {
            updatePosition();
        });

        observer.observe(popoverRef);
        return () => observer.disconnect();
    }, [popoverRef, updatePosition]);

    return ReactDOM.createPortal(
        <SBackdrop
            onClick={(e) => {
                if (e.target === e.currentTarget) {
                    // close only if use clicked on backdrop directly (propagated click event does not count)
                    if (!disableCloseOnClickOutside) onClose();
                }
            }}
            invisible
            open={open && !!anchorEl}
        >
            <StyledPaper
                ref={setPopoverRef}
                style={{
                    transform: `translate(${position.left}px, ${position.top}px)`,
                    ...(slotProps?.paper?.style && { ...slotProps?.paper?.style }),
                }}
                role="dialog"
                elevation={0}
            >
                {showCloseButton && (
                    <CloseButton onClick={onClose} size="small">
                        <CloseIcon fill="var(--neutral6)" size={IconSize.L} />
                    </CloseButton>
                )}
                <Chevron ref={setChevronRef} className="chevron" />
                {children}
            </StyledPaper>
        </SBackdrop>,
        document.body
    );
}

const CloseButton = styled(IconButton)({
    position: 'absolute',
    top: 0,
    right: 8,
});

const StyledPaper = styled(Paper)({
    padding: 19,
    boxShadow: '0 4px 10px -2px var(--neutral4)',
    borderRadius: 10,
    marginBottom: 10,
    overflow: 'initial',
    position: 'fixed',
    left: 0,
    top: 0,
    zIndex: 1,

    '&.top-chevron .chevron': {
        top: -10,
        bottom: 'initial',
    },
});
