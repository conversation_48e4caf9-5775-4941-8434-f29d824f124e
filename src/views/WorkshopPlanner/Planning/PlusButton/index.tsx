import { styled } from '@mui/material';
import clsx from 'clsx';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { isSchedulerCellElement } from 'common/components/Scheduler/GridCanvas/mouseTracker';
import {
    MousePosition,
    useSchedulerContext,
    useSchedulerMouseTrackerEvent,
} from 'common/components/Scheduler/context';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import moment from 'moment';
import { createContext, useContext, useMemo, useRef, useState } from 'react';
import { IEvent, createEvent, useEventSubscription } from 'utils/event';
import { useTechnicianCapacityAlert } from 'views/WorkshopPlanner/TechnicianCapacity/TechnicianCapacityAlert';
import { useIsSchedulingAllowed } from 'views/WorkshopPlanner/TechnicianCapacity/helpers';
import { useScheduleAppointmentPopup } from 'views/WorkshopPlanner/schedule-popups/ScheduleAppointmentWorkPopup/context';
import { useScheduleOrderPopup } from 'views/WorkshopPlanner/schedule-popups/ScheduleOrderWorkPopup/context';
import { useScheduleAbsencePopup } from 'views/absence/ScheduleAbsence/context';

type PlusButtonProps = {
    planningId: number;
    vertical: boolean;
};

export default function PlusButton({ planningId, vertical }: PlusButtonProps) {
    const rootRef = useRef<HTMLDivElement | null>(null);
    const event = useSchedulerMouseTrackerEvent();
    const { dimensions, interval, grouping } = useSchedulerContext();
    const [state, setState] = useState({
        userId: '',
        ts: 0,
    });
    const hint = useMemo(() => DateTime.fromMillis(state.ts).toFormat('HH:mm'), [state.ts]);
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const { t } = useAppTranslation();
    const id = useMemo(() => Math.floor(Math.random() * 1000000), []);
    const context = useContext(PlusButtonActivationEventContext);
    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed();

    useEventSubscription(context, (activeId) => {
        if (activeId !== id) {
            rootRef.current!.style.display = 'none';
        }
    });

    const callback = useMemo(
        () => (pos: MousePosition) => {
            const groupIndex = pos.groupIndex;
            if (groupIndex === -1) {
                if (
                    pos.mouseEvent &&
                    pos.mouseEvent.relatedTarget instanceof Node &&
                    rootRef.current?.contains(pos.mouseEvent.relatedTarget)
                )
                    return;
                rootRef.current!.style.display = 'none';
                return;
            }
            const rootElement = rootRef.current;

            if (!rootElement) return;

            const distanceInMinutes = (pos.ts - +moment(interval.from).toDate()) / 60000;
            const offset = distanceInMinutes * dimensions.pixelsPerMinuteRatio;

            if (rootElement.style.display !== 'block' && context) {
                context.publish(id);
            }
            rootElement.style.display = 'block';
            if (vertical) {
                rootElement.style.setProperty('--x-pos', groupIndex + '');
                rootElement.style.setProperty('--y-pos', `${offset}px`);
            } else {
                rootElement.style.setProperty('--y-pos', groupIndex + '');
                rootElement.style.setProperty('--x-pos', `${offset}px`);
            }

            const userId = grouping.groups[groupIndex].data as string;
            setState({
                ts: pos.ts,
                userId,
            });
        },
        [dimensions.pixelsPerMinuteRatio, interval.from, grouping.groups, context, id, vertical]
    );
    useEventSubscription(event, callback);
    const [hovered, setHovered] = useState(false);
    const scheduleAppointmentPopup = useScheduleAppointmentPopup();
    const scheduleOrderPopup = useScheduleOrderPopup();
    const scheduleAbsencePopup = useScheduleAbsencePopup();

    const addOrderWork = () => {
        technicianCapacityAlertPopup.checkTechnicianCapacity(() => {
            scheduleOrderPopup.open(state.ts, state.userId, planningId, rootRef.current!);
            setAnchorEl(null);
        });
    };

    const addAppointmentWork = () => {
        technicianCapacityAlertPopup.checkTechnicianCapacity(() => {
            scheduleAppointmentPopup.open(state.ts, state.userId, planningId, rootRef.current!);
            setAnchorEl(null);
        });
    };

    const addAbsence = () => {
        scheduleAbsencePopup.open(state.ts, state.userId, rootRef.current!);
        setAnchorEl(null);
    };

    return (
        <>
            <DivRoot
                ref={rootRef}
                onMouseOut={(e) => {
                    if (
                        e.relatedTarget instanceof Node &&
                        !rootRef.current!.contains(e.relatedTarget) &&
                        !isSchedulerCellElement(e.relatedTarget) &&
                        !anchorEl
                    ) {
                        rootRef.current!.style.display = 'none';
                    }
                }}
                className={clsx(hovered && 'hovered')}
                style={
                    vertical
                        ? {
                              transform:
                                  'translate(var(--x-pos) * var(--wps-column-width), calc(var(--y-pos)))',
                              height: 0,
                              width: 'var(--wps-column-width)',
                          }
                        : {
                              transform:
                                  'translate(var(--x-pos), calc(var(--y-pos) * var(--wps-row-height)))',
                              height: 'var(--wps-row-height)',
                              width: 0,
                          }
                }
            >
                <DivInner
                    style={
                        vertical
                            ? {
                                  width: '96%',
                                  height: 4,
                              }
                            : {
                                  width: 4,
                                  height: '96%',
                              }
                    }
                >
                    <SpanHint
                        style={
                            vertical
                                ? {
                                      transform: 'translateY(calc(-50% + 1px))',
                                  }
                                : { transform: 'translateX(calc(-50% + 1px))' }
                        }
                    >
                        {hint}
                    </SpanHint>
                    <StyledBtn
                        onClick={(e) => setAnchorEl(e.currentTarget)}
                        onMouseOver={() => setHovered(true)}
                        onMouseOut={() => setHovered(false)}
                        style={
                            vertical
                                ? {
                                      left: '50%',
                                      width: 'clamp(16px, 26px, calc(var(--wps-column-width) / 3))',
                                      height: 'clamp(16px, 26px, calc(var(--wps-column-width) / 3))',
                                      transform: 'translate(-50%, calc(-10 / 26 * 100%))',
                                  }
                                : {
                                      top: '50%',
                                      width: 'clamp(16px, 26px, calc(var(--wps-row-height) / 3))',
                                      height: 'clamp(16px, 26px, calc(var(--wps-row-height) / 3))',
                                      transform: 'translate(calc(-10 / 26 * 100%), -50%)',
                                  }
                        }
                    >
                        <PlusIcon size={16} fill="currentColor" />
                    </StyledBtn>
                </DivInner>
            </DivRoot>
            {anchorEl && (
                <SMenu
                    borders
                    anchorOrigin={{ horizontal: 'right', vertical: 'center' }}
                    transformOrigin={{ horizontal: 'left', vertical: 'center' }}
                    id="scheduler-add-work-menu"
                    anchorEl={anchorEl}
                    onClose={() => {
                        setAnchorEl(null);
                    }}
                    transitionDuration={150}
                    open={!!anchorEl}
                    keepMounted
                >
                    {isSchedulingAllowed && (
                        <>
                            <SMenuItem2 onClick={addAppointmentWork}>
                                <PlusIcon size={24} fill="currentColor" />
                                {t('workshopPlanner.add.appointment')}
                            </SMenuItem2>
                            <SMenuItem2 onClick={addOrderWork}>
                                <PlusIcon size={24} fill="currentColor" />
                                {t('workshopPlanner.add.order')}
                            </SMenuItem2>
                        </>
                    )}
                    <SMenuItem2 onClick={addAbsence}>
                        <PlusIcon size={24} fill="currentColor" />
                        {t('workshopPlanner.add.absence')}
                    </SMenuItem2>
                </SMenu>
            )}
        </>
    );
}

const PlusButtonActivationEventContext = createContext<IEvent<number>>(createEvent());

const DivRoot = styled('div')({
    display: 'none',
    opacity: 0.6,
    position: 'absolute',
    left: 0,
    top: 0,
    transition: 'opacity 0.2s ease-in-out',

    '&.with-animation': {
        transition: 'all 0.2s ease-in-out',
    },

    '&.hovered': {
        opacity: 1,
    },
});

const DivInner = styled('div')({
    backgroundColor: 'var(--cm1)',
    borderRadius: 4,
    transform: 'translate(-50%, 2%)',
    position: 'relative',
});

const StyledBtn = styled('button')({
    all: 'initial',
    backgroundColor: 'var(--cm1)',
    color: '#fff',
    position: 'absolute',
    borderRadius: 100,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.3s ease-in-out',
    cursor: 'pointer',

    '&:hover': {
        backgroundColor: 'var(--cm2)',
        boxShadow: '0 0 0 2px var(--cm2)',
    },

    '&:active': {
        scale: 0.97,
    },
});

const SpanHint = styled('span')(({ theme }) => ({
    position: 'absolute',
    ...theme.typography.h7Inter,
    display: 'inline-block',
    padding: 2,
    borderRadius: 6,
    backgroundColor: 'var(--neutral1)',
    boxShadow: '0 1px 4px 0 var(--neutral4)',
}));
