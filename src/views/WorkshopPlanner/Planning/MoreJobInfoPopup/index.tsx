import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import FieldsApi from 'api/fields';
import { Modal } from 'common/components/Modal';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useMemo } from 'react';
import { useAppSelector } from 'store';
import { selectBlock } from 'store/slices/wp/plannings';
import JobTimeline from './JobTimeline';
import MoreJobInfoCustomFields from './MoreJobInfoCustomFields';
import MoreJobInfoHeader from './MoreJobInfoHeader';
import MoreJobInfoPredefinedFields from './MoreJobInfoPredefinedFields';
import TechnicianSignature from './TechnicianSignature';

export type MoreJobInfoPopupProps = {
    onClose: () => void;
    blockId: string;
};

export function MoreJobInfoPopup({ onClose, blockId }: MoreJobInfoPopupProps) {
    const block = useAppSelector((r) => selectBlock(r, blockId));

    if (!block) {
        throw new Error("can't find block for MoreJobInfoPopup");
    }

    if (block.type !== 'order') {
        throw new Error("MoreJobInfoPopup works only with orders' jobs");
    }

    const { data } = useQuery(['job', 'customFieldsData', block.id], () =>
        FieldsApi.getJobValues(block.id)
    );

    const predefinedFields = useMemo(
        () => (data ? data.fields.filter((x) => x.type === 'Predefined') : null),
        [data]
    );
    const customFields = useMemo(
        () => (data ? data.fields.filter((x) => x.type !== 'Predefined') : null),
        [data]
    );

    return (
        <Modal open onClose={onClose}>
            <MoreJobInfoHeader orderId={block.orderId} onClose={onClose} />
            <DivContent>
                {predefinedFields && (
                    <MoreJobInfoPredefinedFields block={block} fields={predefinedFields} />
                )}
                {customFields && (
                    <MoreJobInfoCustomFields blockId={block.id} fields={customFields} />
                )}
                <JobTimeline blockId={block.id} status={block.status} />
                <TechnicianSignature orderId={block.orderId} scheduleEntryId={block.id} />
            </DivContent>
        </Modal>
    );
}

const DivContent = styled('div')({
    maxWidth: '962px',
    minWidth: '962px',
    minHeight: 'calc(100vh - 90px)',
    maxHeight: 'calc(100vh - 90px)',
    ...scrollbarStyle(),
    overflowY: 'auto',
    padding: '0px 90px 0px 90px',
});
