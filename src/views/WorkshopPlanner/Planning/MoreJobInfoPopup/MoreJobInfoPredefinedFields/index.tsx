import { Box, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { FieldValueWithName, PREDEFINED_FIELDS } from 'api/fields';
import OrdersApi from 'api/orders';
import WpApi, { StandardOperationDto } from 'api/workshopPlanner';
import { isAxiosError } from 'axios';
import { TextField } from 'common/components/Inputs';
import DateFormField from 'common/components/Inputs/DateFormField';
import DurationFormField from 'common/components/Inputs/DurationFormField';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { DateTime } from 'luxon';
import { useEffect, useMemo, useState } from 'react';
import { hasCode } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { selectRepairShopWpConfiguration, selectSettings } from 'store/slices/globalSettingsSlice';
import { useUser } from 'store/slices/users';
import { selectWpOrder } from 'store/slices/wp/orders';
import { selectPlanning } from 'store/slices/wp/plannings';
import { State } from 'store/slices/wp/plannings/state/block';
import updateBlockThunk from 'store/slices/wp/plannings/thunks/updateBlock';
import { isDateValid } from 'utils';
import UTInput from 'views/WorkshopPlanner/common/UTInput';
import { isStandardOperationsAllowed } from 'views/WorkshopPlanner/helpers';
import SroOperationCodeFields from './SroOperationCodeFields';

export type JobInfoPredefinedFieldsProps = {
    block: State;
    fields: FieldValueWithName[];
};

export default function MoreJobInfoPredefinedFields({
    block,
    fields,
}: JobInfoPredefinedFieldsProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const user = useUser(block.userId);
    const planning = useAppSelector((r) => selectPlanning(r, block.planningId));
    const { data: jobDescriptionData } = useQuery(['wp', 'job', 'description', block.id], () =>
        WpApi.getJobDescription(block.id)
    );

    const planningName = planning?.isTechnicians
        ? t('workshopPlanner.technicians.title')
        : planning?.name;

    const globalSettings = useAppSelector(selectSettings);

    const [invalidTime, setInvalidTime] = useState(false);
    const handleSuccess = () => {
        if (invalidTime) {
            setInvalidTime(false);
        }
    };
    const handleError = () => {
        setInvalidTime(true);
    };

    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const updateBlockMutation = useMutation(
        async ({
            startsAt,
            duration,
            standardOperations,
        }: {
            startsAt: DateTime;
            duration: number;
            standardOperations?: StandardOperationDto[];
        }) => {
            const result = await dispatch(
                updateBlockThunk({
                    id: block.id,
                    duration: duration,
                    startsAt: startsAt.set({ millisecond: 0, second: 0 }).toMillis(),
                    standardOperations: standardOperations,
                    timestamp: Date.now(),
                    // if there is no preview then there is no need to do optimistic updates
                    disableOptimisticUpdate: true,
                })
            );
            if (result.meta.requestStatus === 'rejected') throw result.payload;
        },
        {
            onSuccess: (_data, { standardOperations }) => {
                if (enableWorkshopJobIntegration && integrationAccountName) {
                    toasters.success(
                        t('workshopPlanner.orderPopup.setActionIntegrationSuccess.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.orderPopup.setActionIntegrationSuccess.title')
                    );
                } else {
                    toasters.success(
                        t('workshopPlanner.editBlock.successNotif.text'),
                        t('workshopPlanner.editBlock.successNotif.title')
                    );
                }

                if (standardOperations) {
                    setStandardOperations(standardOperations);
                }

                handleSuccess();
            },
            onError: (err) => {
                if (isAxiosError(err) && err.response) {
                    if (enableWorkshopJobIntegration && integrationAccountName) {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.setActionIntegrationError.text', {
                                errorMessage: err.message,
                            }),
                            t('workshopPlanner.orderPopup.setActionIntegrationError.title', {
                                integrationAccountName,
                            })
                        );
                    } else if (
                        !(
                            hasCode(err.response.data, 'General.WP.BlockOverlapAbsence') ||
                            hasCode(err.response.data, 'General.WP.Schedule.Overlap') ||
                            hasCode(err.response.data, 'General.WP.OutsideOfUserSchedule') ||
                            hasCode(err.response.data, 'General.WP.Schedule.TooLarge')
                        )
                    ) {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                } else {
                    toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                }

                handleError();
            },
        }
    );

    const standardOperationOrderTypes = useMemo(() => {
        try {
            return JSON.parse(globalSettings.standardOperationOrderTypes) ?? [];
        } catch (error) {
            console.error('Error parsing standardOperationOrderTypes:', error);
            return [];
        }
    }, [globalSettings.standardOperationOrderTypes]);

    const orderId = block?.type === 'order' ? block.orderId : null;

    const order = useAppSelector((state) =>
        orderId && state.wp ? selectWpOrder(state, orderId) : null
    );

    const canUseStandardOperations = isStandardOperationsAllowed(
        !!order,
        !order?.type,
        globalSettings.enableStandardOperations,
        standardOperationOrderTypes,
        order?.type?.key
    );

    const [standardOperations, setStandardOperations] = useState<StandardOperationDto[]>([]);

    const { data: standardOperationsData } = useQuery(
        ['blockId', 'standardOperations', block.id],
        () => WpApi.getOrderBlockStandardOperations(block.id)
    );

    const { data: timeInterval } = useQuery(['orders', 'time-interval'], {
        queryFn: OrdersApi.getTimeInterval,
        cacheTime: Infinity,
        staleTime: Infinity,
    });

    useEffect(() => {
        if (standardOperationsData) {
            setStandardOperations(
                standardOperationsData.length === 0
                    ? [
                          {
                              operationCode: '0',
                              standardTime: timeInterval ?? 15,
                              operationCodeDescription: '',
                          },
                      ]
                    : standardOperationsData
            );
        }
    }, [standardOperationsData, timeInterval]);

    function isDuplicateOperationCode(
        standardOperations: StandardOperationDto[],
        currentCode: string,
        currentIndex: number
    ) {
        return (
            Array.isArray(standardOperations) &&
            standardOperations.some(
                (item, idx) =>
                    item.operationCode.trim() === currentCode.trim() && idx !== currentIndex
            )
        );
    }

    const calculateScheduledDuration = (fields: StandardOperationDto[]) => {
        return fields.reduce((total, field) => total + (field.standardTime ?? 0), 0);
    };

    const duration = useMemo(
        () =>
            canUseStandardOperations && standardOperations
                ? calculateScheduledDuration(standardOperations)
                : block.duration,
        [canUseStandardOperations, standardOperations, block.duration]
    );

    const handleOperationCodeChange = (index: number, newCode: string) => {
        if (standardOperations[index].operationCode === newCode) return;

        if (isDuplicateOperationCode(standardOperations, newCode, index)) {
            toasters.danger(
                t('workshopPlanner.editBlock.duplicateOperationCode.text'),
                t('workshopPlanner.editBlock.duplicateOperationCode.title')
            );
            return;
        }

        const updated = [...standardOperations];
        updated[index] = { ...updated[index], operationCode: newCode };

        updateBlockMutation.mutateAsync({
            startsAt,
            duration: duration,
            standardOperations: updated,
        });
    };

    const handleStandardTimeChange = (index: number, newTime: number) => {
        if (newTime === 0 || standardOperations[index].standardTime === newTime) return;

        const updated = [...standardOperations];
        updated[index] = { ...updated[index], standardTime: newTime };
        updateBlockMutation.mutateAsync({
            startsAt,
            duration: calculateScheduledDuration(updated),
            standardOperations: updated,
        });
    };

    const handleOperationCodeDescriptionChange = (index: number, newDescription: string) => {
        if (standardOperations[index].operationCodeDescription === newDescription) return;

        updateBlockMutation.mutateAsync({
            startsAt,
            duration: block.duration,
            standardOperations: standardOperations.map((item, ind) =>
                ind === index ? { ...item, operationCodeDescription: newDescription } : item
            ),
        });
    };

    const addOperationField = () => {
        if (!standardOperations) return;

        const newOperationCode = standardOperations
            .map((x) => +x.operationCode)
            .filter((x) => !isNaN(x))
            .toSorted()
            .reduce((nextCode, x) => (x === nextCode ? nextCode + 1 : nextCode), 0);

        const updated: StandardOperationDto[] = [
            ...standardOperations,
            {
                operationCode: newOperationCode + '',
                standardTime: 15,
                operationCodeDescription: '',
            },
        ];

        updateBlockMutation.mutateAsync({
            startsAt,
            duration: calculateScheduledDuration(updated),
            standardOperations: updated,
        });
    };

    const removeOperationField = (index: number) => {
        if (!standardOperations) return;

        const updated = standardOperations.filter((_, ind) => ind !== index);

        updateBlockMutation.mutateAsync({
            startsAt,
            duration: calculateScheduledDuration(updated),
            standardOperations: updated,
        });
    };

    const startsAt = DateTime.fromMillis(block.startsAt);

    const updateDate = (date: Date | null) => {
        if (!date) return;

        const newDay = date.getDate();
        const newMonth = date.getMonth() + 1;
        const newYear = date.getFullYear();

        if (newDay === startsAt.day && newMonth === startsAt.month && newYear === startsAt.year) {
            return;
        }

        const newStartsAt = startsAt.set({
            day: date.getDate(),
            month: date.getMonth() + 1,
            year: date.getFullYear(),
        });

        updateBlockMutation.mutateAsync({
            startsAt: newStartsAt,
            duration: block.duration,
        });
    };

    const updateTime = (time: [number, number]) => {
        if (time[0] === startsAt.hour && time[1] === startsAt.minute) return;

        const newStartsAt = startsAt.set({
            hour: time[0],
            minute: time[1],
        });

        updateBlockMutation.mutateAsync({
            startsAt: newStartsAt,
            duration: block.duration,
        });
    };

    const updateDuration = (duration: number) => {
        if (duration === 0 || duration === block.duration) return;

        updateBlockMutation.mutateAsync({
            startsAt,
            duration,
        });
    };

    const updateUTs = (uts: number) => {
        const duration = Math.floor(uts / 60);
        if (duration === 0 || duration === block.duration) return;

        updateBlockMutation.mutateAsync({
            startsAt,
            duration,
        });
    };

    const isEmployeeIdHidden = useMemo(
        () => fields.every((x) => x.name !== PREDEFINED_FIELDS.JOB_EMPLOYEE_ID),
        [fields]
    );

    return (
        <DivFieldsContainer>
            <PlanningFieldsWrapper columns={isEmployeeIdHidden ? 2 : 3}>
                <TextField
                    name="teamMember"
                    disabled
                    cmosVariant="grey"
                    label={t('workshopPlanner.moreJobInfoPopup.teamMember')}
                    value={user?.name}
                    slotProps={{
                        inputWrapper: {
                            slotProps: { label: { sx: { color: 'var(--neutral8) !important' } } },
                        },
                    }}
                />
                {!isEmployeeIdHidden && (
                    <TextField
                        name="employeeId"
                        disabled
                        cmosVariant="grey"
                        label={t('workshopPlanner.moreJobInfoPopup.employeeId')}
                        value={user?.dmsIdNumber}
                        slotProps={{
                            inputWrapper: {
                                slotProps: {
                                    label: { sx: { color: 'var(--neutral8) !important' } },
                                },
                            },
                        }}
                    />
                )}
                <TextField
                    name="planning"
                    disabled
                    cmosVariant="grey"
                    label={t('workshopPlanner.moreJobInfoPopup.planning')}
                    value={planningName}
                    slotProps={{
                        inputWrapper: {
                            slotProps: { label: { sx: { color: 'var(--neutral8) !important' } } },
                        },
                    }}
                />
            </PlanningFieldsWrapper>
            <TimeFieldsWrapper>
                <DateFormField
                    key={`invalid-date-${invalidTime}`}
                    isRequired
                    label={t('workshopPlanner.moreJobInfoPopup.startDate')}
                    enableEnterComplete
                    value={startsAt.toJSDate()}
                    onChange={(v) => {
                        if (v && isDateValid(v)) {
                            updateDate(v);
                        }
                    }}
                />
                <TimeFormField
                    isRequired
                    value={[startsAt.hour, startsAt.minute]}
                    onChange={updateTime}
                    label={t('workshopPlanner.editBlock.startHour')}
                />
                <DurationFormField
                    isRequired
                    disabled={canUseStandardOperations}
                    value={duration}
                    onChange={updateDuration}
                    label={t('commonLabels.duration')}
                    name="duration"
                />
                <UTInput
                    disabled={canUseStandardOperations}
                    durationInSeconds={block.duration * 60}
                    onChange={updateUTs}
                />
            </TimeFieldsWrapper>

            {invalidTime && (
                <ExceedsText>{t('workshopPlanner.schedulePopup.exceedsDuration')}</ExceedsText>
            )}

            {block.type === 'order' &&
                canUseStandardOperations &&
                standardOperations &&
                standardOperations.length > 0 &&
                standardOperations.map((field, index) => (
                    <SroOperationCodeFields
                        index={index}
                        field={field}
                        orderId={block.orderId}
                        standardOperations={standardOperations}
                        onOperationCodeChange={handleOperationCodeChange}
                        onStandardTimeChange={handleStandardTimeChange}
                        onOperationCodeDescriptionChange={handleOperationCodeDescriptionChange}
                        onAddOperationField={addOperationField}
                        onRemoveOperationField={removeOperationField}
                    />
                ))}
            <DivJobDescriptionContainer>
                <b>{t('workshopPlanner.moreJobInfoPopup.jobDescription')}</b>
                <UlJobDescriptionList>
                    {jobDescriptionData &&
                        jobDescriptionData.jobDescription.map((value, index) => (
                            <li key={index}>{value}</li>
                        ))}
                </UlJobDescriptionList>
            </DivJobDescriptionContainer>
        </DivFieldsContainer>
    );
}

const PlanningFieldsWrapper = styled(Box, {
    shouldForwardProp: (prop) => !['columns'].includes(prop as string),
})<{ columns: number }>(({ columns }) => ({
    display: 'grid',
    gridTemplateColumns: '1fr '.repeat(columns),
    columnGap: '24px',
}));

const TimeFieldsWrapper = styled(Box)({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr 1fr',
    columnGap: '24px',
    paddingTop: '10px',
});

const ExceedsText = styled('span')(({ theme }) => ({
    marginTop: '10px',
    color: 'var(--danger)',
    wordWrap: 'break-word',
    display: 'inline-flex',
    ...theme.typography.h6Inter,
}));

const DivFieldsContainer = styled('div')({
    paddingTop: 20,
});

const DivJobDescriptionContainer = styled('div')(({ theme }) => ({
    paddingTop: 20,
    color: theme.palette.neutral[8],
}));

const UlJobDescriptionList = styled('ul')({
    marginTop: '6px',
});
