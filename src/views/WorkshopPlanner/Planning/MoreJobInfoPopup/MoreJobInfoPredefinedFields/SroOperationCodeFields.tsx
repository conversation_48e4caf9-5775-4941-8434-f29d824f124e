import { Box, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import WpApi, { StandardOperationDto } from 'api/workshopPlanner';
import { AddOperationIcon } from 'common/components/Icons/AddOperationIcon';
import { RemoveOperationIcon } from 'common/components/Icons/RemoveOperationIcon';
import DurationFormField from 'common/components/Inputs/DurationFormField';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import ResettableTextInput from 'common/components/Inputs/ResettableTextField/ResettableTextInput';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback } from 'react';
import { useAppSelector } from 'store';
import {
    selectRepairShopIntegrationAccountName,
    selectSettings,
} from 'store/slices/globalSettingsSlice';

export type SroOperationCodeFieldsProps = {
    index: number;
    field: StandardOperationDto;
    orderId: string;
    standardOperations: StandardOperationDto[];
    onOperationCodeChange: (index: number, newCode: string) => void;
    onStandardTimeChange: (index: number, newTime: number) => void;
    onOperationCodeDescriptionChange: (index: number, newDescription: string) => void;
    onAddOperationField: () => void;
    onRemoveOperationField: (index: number) => void;
};

export default function SroOperationCodeFields({
    index,
    field,
    orderId,
    standardOperations,
    onOperationCodeChange,
    onStandardTimeChange,
    onOperationCodeDescriptionChange,
    onAddOperationField,
    onRemoveOperationField,
}: SroOperationCodeFieldsProps) {
    const { t } = useAppTranslation();

    const sroValidationIntegration =
        useAppSelector(selectSettings).repairShopSettings?.features.sroValidationIntegration;

    const { mutateAsync: sroMutate, isLoading: isLoadingSro } = useSroMutation((data) => {
        onStandardTimeChange(index, hoursToMinutes(data.atdSROTime));
        onOperationCodeDescriptionChange(index, data.sroDescription);
    });

    const handleOperationCodeChange = useCallback(
        async (operationCode: string) => {
            if (
                sroValidationIntegration &&
                Array.isArray(standardOperations) &&
                !standardOperations.some((x) => x.operationCode === operationCode)
            ) {
                onOperationCodeChange(index, operationCode);
                await sroMutate({ orderId, sro: operationCode });
            } else if (!sroValidationIntegration) {
                onOperationCodeChange(index, operationCode);
            }
        },
        [
            sroValidationIntegration,
            orderId,
            sroMutate,
            onOperationCodeChange,
            index,
            standardOperations,
        ]
    );

    return (
        <StandardOperationsFieldsWrapper>
            <MainOperationsFieldsWrapper>
                <InputWrapper
                    label={t('workshopPlanner.schedulePopup.operationCode')}
                    isRequired
                    showValidationIndicators
                >
                    <ResettableTextInput
                        isRequired
                        value={field.operationCode}
                        disabled={isLoadingSro}
                        placeholder={t('workshopPlanner.schedulePopup.operationCodePlaceholder')}
                        onSave={async (v) => handleOperationCodeChange(v)}
                        maxLength={50}
                        size="small"
                    />
                </InputWrapper>
                <DurationFormField
                    isRequired
                    showValidationIndicators
                    label={t('workshopPlanner.schedulePopup.standardTime')}
                    name={`standardTime-${index}`}
                    value={field.standardTime ?? 0}
                    onChange={(value) => onStandardTimeChange(index, value)}
                />
            </MainOperationsFieldsWrapper>
            <InputWrapper label={t('workshopPlanner.schedulePopup.operationCodeDescription')}>
                <ResettableTextInput
                    value={field.operationCodeDescription}
                    placeholder={t(
                        'workshopPlanner.schedulePopup.operationCodeDescriptionPlaceholder'
                    )}
                    onSave={async (v) => onOperationCodeDescriptionChange(index, v)}
                    maxLength={1000}
                    size="small"
                />
                {standardOperations && (
                    <ActionButtons
                        rightOffset={
                            standardOperations.length > 1 && index === standardOperations.length - 1
                                ? -52
                                : -25
                        }
                    >
                        {index === standardOperations.length - 1 && (
                            <AddButton onClick={onAddOperationField}>
                                <AddOperationIcon />
                            </AddButton>
                        )}
                        {standardOperations.length > 1 && (
                            <RemoveButton onClick={() => onRemoveOperationField(index)}>
                                <RemoveOperationIcon />
                            </RemoveButton>
                        )}
                    </ActionButtons>
                )}
            </InputWrapper>
        </StandardOperationsFieldsWrapper>
    );
}

const useSroMutation = (
    onSuccess?: (_data: WpApi.GetSroOperationDto) => void,
    onError?: () => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dmsName = useAppSelector(selectRepairShopIntegrationAccountName);
    const mutation = useMutation(
        (params: { orderId: string; sro: string }) => WpApi.getSroOperation(params),
        {
            onSuccess: (data) => {
                toasters.success(
                    t('workshopPlanner.schedulePopup.sroValidation.success.description', {
                        dmsName,
                    }),
                    t('workshopPlanner.schedulePopup.sroValidation.success.title')
                );
                onSuccess && onSuccess(data);
            },
            onError: (errorMessage) => {
                toasters.danger(
                    t('workshopPlanner.schedulePopup.sroValidation.error.description', {
                        errorMessage,
                    }),
                    t('workshopPlanner.schedulePopup.sroValidation.error.title', {
                        dmsName,
                    })
                );
                onError && onError();
            },
        }
    );

    return mutation;
};

const hoursToMinutes = (hours: number): number => {
    return Math.round(hours * 60);
};

const StandardOperationsFieldsWrapper = styled(Box)({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    columnGap: '24px',
    alignItems: 'start',
    paddingTop: '10px',
});

const MainOperationsFieldsWrapper = styled(Box)({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    columnGap: '24px',
    alignItems: 'center',
});

const AddButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    backgroundColor: 'var(--primary)',
    color: '#fff',
    padding: 1,
    borderRadius: 4,
    cursor: 'pointer',
}));

const RemoveButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    backgroundColor: 'var(--error)',
    color: '#fff',
    padding: 1,
    marginRight: -4,
    borderRadius: 4,
    cursor: 'pointer',
}));

const ActionButtons = styled('div')<{ rightOffset: number }>(({ rightOffset }) => ({
    position: 'absolute',
    right: rightOffset,
    top: '2px',
    display: 'flex',
    alignItems: 'center',
}));
