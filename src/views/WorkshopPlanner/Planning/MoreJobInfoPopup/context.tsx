import { createContext, useContext, useMemo, useState } from 'react';
import { MoreJobInfoPopup } from '.';

interface IMoreJobInfoPopup {
    open: (blockId: string, onClose?: () => void) => void;
}

const MoreJobInfoPopupContext = createContext<IMoreJobInfoPopup | null>(null);

export function useMoreJobInfoPopup() {
    const ctx = useContext(MoreJobInfoPopupContext);

    if (ctx == null) {
        throw Error('MoreJobInfoPopupContext is not available');
    }

    return ctx;
}

export function MoreJobInfoPopupProvider({ children }: React.PropsWithChildren) {
    const [blockId, setBlockId] = useState<string | null>(null);
    const [onClose, setOnClose] = useState<(() => void) | null>(null);

    const handleClose = () => {
        setBlockId(null);
        onClose?.();
        setOnClose(null);
    };

    const ctx: IMoreJobInfoPopup = useMemo(
        () => ({
            open: (blockId, onClose) => {
                setBlockId(blockId);
                onClose && setOnClose(() => onClose);
            },
        }),
        []
    );

    return (
        <MoreJobInfoPopupContext.Provider value={ctx}>
            {blockId && <MoreJobInfoPopup blockId={blockId} onClose={handleClose} />}
            {children}
        </MoreJobInfoPopupContext.Provider>
    );
}
