import {
    TimelineConnector,
    TimelineContent,
    timelineContentClasses,
    TimelineDot,
    TimelineItem,
    timelineItemClasses,
    TimelineSeparator,
} from '@mui/lab';
import { Box, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import WpApi from 'api/workshopPlanner';
import { STimeline } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import Skeleton from 'react-loading-skeleton';

export type JobTimelineProps = {
    blockId: string;
    status: WpApi.JobStatus;
};

export default function JobTimeline({ blockId, status }: JobTimelineProps) {
    const { t } = useAppTranslation();

    const { data, isLoading } = useQuery(['job', 'timeline', blockId], () =>
        WpApi.getJobTimeline(blockId)
    );

    const timeline = useMemo(() => (data ? data.timeline : []), [data]);

    return (
        <DivTimelineContainer>
            <DivLabel>{t('workshopPlanner.moreJobInfoPopup.timeline')}</DivLabel>
            <div>
                <b>{t('workshopPlanner.moreJobInfoPopup.realDuration')}</b>
                {` ${data && status === 'Stopped' ? data.realDuration : '--'} hrs`}
            </div>
            {isLoading && (
                <>
                    <ItemSkeleton />
                    <ItemSkeleton />
                    <ItemSkeleton />
                    <ItemSkeleton />
                </>
            )}
            <STimeline
                sx={{
                    [`& .${timelineItemClasses.missingOppositeContent}::before`]: {
                        display: 'none',
                    },

                    [`& .${timelineContentClasses.root}`]: (theme) => ({
                        ...theme.typography.h5Inter,
                        color: theme.palette.neutral[8],
                    }),

                    paddingTop: '10px',
                }}
            >
                {timeline.map((x, idx) => {
                    const isLast = idx === timeline.length - 1;

                    return (
                        <TimelineItem key={x.dateTimeStampInfo}>
                            <TimelineSeparator>
                                <TimelineDot color="primary" sx={{ padding: '3px !important' }} />
                                {!isLast && <TimelineConnector sx={{ bgcolor: 'var(--cm1)' }} />}
                            </TimelineSeparator>
                            <TimelineContent sx={{ fontSize: '12px !important' }}>
                                <SpanTimelineItem>
                                    {x.description}
                                    {x.dateTimeStampInfo}
                                </SpanTimelineItem>
                            </TimelineContent>
                        </TimelineItem>
                    );
                })}
            </STimeline>
        </DivTimelineContainer>
    );
}

function ItemSkeleton() {
    return (
        <>
            <Box display="flex" gap={0.5} marginBottom={1}>
                <Skeleton circle width={15} height={15} />
                <Skeleton width={450} height={10} />
            </Box>
        </>
    );
}

const DivTimelineContainer = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[8],
}));

const DivLabel = styled('div')({
    fontSize: 14,
    fontWeight: 'bold',

    padding: '20px 0px',
});

const SpanTimelineItem = styled('span')({
    fontWeight: 'normal',
});
