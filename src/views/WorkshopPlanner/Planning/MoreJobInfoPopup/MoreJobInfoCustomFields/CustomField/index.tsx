import { styled } from '@mui/material';
import FieldsApi, { CustomizableFieldValue, FieldType } from 'api/fields';
import { TimeSpan } from 'api/utils/format';
import { $Icon } from 'common/components/Icons/$Icon';
import { IconProps } from 'common/components/Icons/Icon';
import DateField from 'common/components/Inputs/DateField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import NumberInput from 'common/components/Inputs/NumberField/NumberInput';
import ResettableTextInput from 'common/components/Inputs/ResettableTextField/ResettableTextInput';
import { TimeField } from 'common/components/Inputs/TimeField';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import { DateTime } from 'luxon';
import { ComponentType, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { planningsActions } from 'store/slices/wp/plannings';
import theme from 'theme';
import { isDateValid } from 'utils';
import ErrorBoundary from 'utils/errorsHandling/ErrorBoundary';

export type CustomFieldProps = {
    blockId: string;
    field: CustomizableFieldValue;
    onFieldSave: (value: string, id: string) => void;
};

// (AK) CustomField looks like CustomField of LocationInfo and OrderDetails (it may be general for any section with custom fields)
// there is sense to make it more common (at least the most common stuff), because copy-paste is not cool
// but i don't have time and i am lazy :P, so now i can only cry about it \(ToT)/
export default function CustomField({ blockId, field, onFieldSave }: CustomFieldProps) {
    const dispatch = useAppDispatch();
    const Component = INPUT_COMPONENTS[field.type];

    async function onSave(value: string, extraChanges?: CustomInputExtraChanges) {
        onFieldSave(value, field.id);
        await FieldsApi.setJobValue(blockId, field.id, value);

        if (extraChanges?.color !== undefined) {
            dispatch(planningsActions.setColorFieldValue({ blockId, color: extraChanges.color }));
        }
    }

    return (
        <InputWrapper sx={{ paddingBottom: '10px' }} label={field.name}>
            <FieldErrorBoundary>
                {Component && (
                    <Component
                        id={field.id}
                        value={field.value}
                        placeholder={field.name}
                        onSave={onSave}
                        field={field}
                    />
                )}
            </FieldErrorBoundary>
        </InputWrapper>
    );
}

type CustomInputProps = {
    id: string;
    value: string;
    onSave: (value: string, extraChanges?: CustomInputExtraChanges) => void;
    placeholder: string;
    field: CustomizableFieldValue;
};

// (SV) Properties for updating states in planning
export type CustomInputExtraChanges = {
    color?: string | null;
};

function ShortTextInput(props: CustomInputProps) {
    return <FreeTextInput multiline={false} {...props} />;
}

function LongTextInput(props: CustomInputProps) {
    return <FreeTextInput multiline {...props} />;
}

function FreeTextInput({
    value,
    onSave,
    placeholder,
    multiline,
}: CustomInputProps & { multiline: boolean }) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLDivElement>(null);

    return (
        <ResettableTextInput
            inputRef={ref}
            value={value}
            onSave={async (v) => {
                onSave(v);
            }}
            showEditButton
            placeholder={`${t('commonLabels.enter')} ${placeholder}`}
            maxLength={1000}
            size="small"
            multiline={multiline}
        />
    );
}

namespace numericInput {
    type NumericInputProps = CustomInputProps & {
        precision?: number;
        icon?: ComponentType<IconProps>;
    };

    export function NumericInput({
        value,
        onSave,
        placeholder,
        precision = 6,
        icon: Icon,
    }: NumericInputProps) {
        const { t } = useAppTranslation();
        const ref = useRef<HTMLDivElement>(null);
        const [internalValue, setInternalValue] = useState<number | null>(null);
        const [focus, setFocus] = useState(false);

        const parsedValue = useMemo(() => parseStringValue(value, precision), [value, precision]);

        useEffect(() => {
            if (focus) {
                setInternalValue(parsedValue);
            }
        }, [parsedValue, focus]);

        return (
            <NumberInput
                inputRef={ref}
                placeholder={`${t('commonLabels.enter')} ${placeholder}`}
                InputProps={{
                    endAdornment: Icon && <Icon />,
                }}
                cmosVariant="grey"
                value={focus ? internalValue : parsedValue}
                onBlur={() => {
                    setFocus(false);
                    onSave(internalValue === null ? '' : internalValue.toString());
                }}
                onFocus={() => setFocus(true)}
                onValueChange={(v) => setInternalValue(v.floatValue ?? null)}
                fullWidth
            />
        );
    }

    function parseStringValue(value: string, precision: number): number | null {
        value = value.trim();

        if (value === null || value === undefined) return null;

        let num = Number.parseFloat(value);
        if (!Number.isNaN(num)) {
            return roundNum(num, precision);
        }

        value = value.replaceAll(/[^\d]/g, '');
        if (value === '') return null;
        num = Number.parseFloat(value);
        return Number.isNaN(num) ? null : roundNum(num, precision);
    }

    function roundNum(num: number, precision: number): number {
        const d = 10 ** precision;
        return Math.floor(num * d) / d;
    }
}

namespace currencyInput {
    const selectCurrencySymbol = createSelector(selectSettings, (s) =>
        s.internationalization.currency.replace('{0}', '').trim()
    );

    const CustomCurrency = styled('div')({
        fontWeight: 'bold',
        color: 'var(--neutral7)',
        display: 'flex',
        justifyContent: 'center',
        verticalAlign: 'middle',
        lineHeight: '24px',
        height: 24,
        width: 24,
    });

    function normalizeSymbol(symbol: string): string {
        if (symbol === 'USD') return '$';

        return symbol;
    }

    function Icon() {
        const symbol: string = normalizeSymbol(useAppSelector(selectCurrencySymbol));

        const scale = 1.5 / Math.sqrt(Math.max(1, symbol.length));

        if (symbol !== '$')
            return (
                <CustomCurrency style={{ transform: `scale(${scale})` }}>{symbol}</CustomCurrency>
            );

        // eslint-disable-next-line react/jsx-pascal-case
        return <$Icon fill={theme.palette.neutral[7]} />;
    }

    export function CurrencyInput({ id, ...props }: CustomInputProps) {
        return <numericInput.NumericInput {...props} id={id} precision={2} icon={Icon} />;
    }
}

function DateInput({ value, onSave }: CustomInputProps) {
    const ref = useRef<HTMLInputElement>(null);
    const date = useMemo(() => {
        try {
            return DateTime.fromFormat(value, 'yyyy-MM-dd').set({
                millisecond: 0,
                second: 0,
                minute: 0,
                hour: 0,
            });
        } catch (_err: unknown) {
            return null;
        }
    }, [value]);
    const jsDate = date?.toJSDate() ?? null;

    const onDateChange = useCallback(
        (d: Date | null) => {
            if (!d) d = new Date();
            if (!isDateValid(d)) return false;

            const stringValue = DateTime.fromJSDate(d).toFormat('yyyy-MM-dd');
            onSave(stringValue);
        },
        [onSave]
    );

    return (
        <DateField inputRef={ref} variant="grey" fullWidth value={jsDate} onChange={onDateChange} />
    );
}

namespace timeInput {
    export function TimeInput({ field, value, onSave }: CustomInputProps) {
        const { t } = useAppTranslation();
        const ref = useRef<HTMLInputElement>(null);
        const parsedValue = useMemo(() => parseTimeValue(value), [value]);

        const onValueChange = useCallback(
            ([h, m]: [number, number]) => {
                onSave(TimeSpan.fromParts(h, m).toString());
            },
            [onSave]
        );

        return (
            <TimeField
                ref={ref}
                cmosVariant="grey"
                fullWidth
                value={parsedValue}
                onChange={onValueChange}
                placeholder={`${t('commonLabels.select')} ${field.name}`}
            />
        );
    }

    function parseTimeValue(value: string): [number, number] | null {
        value = value.trim();
        if (value === '') return null;

        if (/^\d{2}:\d{2}(:\d{2})?$/.test(value)) {
            const parts = value.split(':').map(Number);
            const h = Math.min(23, parts[0]);
            const m = Math.min(59, parts[1]);
            return [h, m];
        }

        return null;
    }
}

type DropDownOption = { name: string };

function SelectInput(props: CustomInputProps) {
    return <InternalMultiSelectInput isMulti={false} {...props} />;
}

type MultiSelectFieldValue = {
    value: DropDownOption[];
    text?: string | null;
};

function MultiSelectInput(props: CustomInputProps) {
    return <InternalMultiSelectInput isMulti {...props} />;
}

function InternalMultiSelectInput({
    value: valueProp,
    onSave,
    field,
    isMulti,
}: CustomInputProps & { isMulti: boolean }) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLSelectElement>(null);

    const parsedValue: MultiSelectFieldValue = useMemo(() => {
        try {
            const { v: _, ...value } = valueProp
                ? (JSON.parse(valueProp) as MultiSelectFieldValue & { v?: unknown })
                : { v: 0, value: [] };
            return value;
        } catch {
            return { value: [] };
        }
    }, [valueProp]);

    const hasValueInExtraText = useRef(!!parsedValue.text);
    const hasExtraText = hasValueInExtraText.current || field.hasExtraText === true;

    // calculate dropdown options
    const options = useMemo(() => {
        const settingsOptions = (field.options ?? []).map((o) => ({
            label: o.name,
            value: o.name,
            color: o.color,
        }));
        //Merge options taken from settings with options stored as value for a specific location field to add options that are deleted in settings.
        const mergedOptions = parsedValue.value.reduce((accumulator, locationItem) => {
            if (!accumulator.some((settingsItem) => settingsItem.value === locationItem.name)) {
                accumulator.push({
                    value: locationItem.name,
                    label: locationItem.name,
                    color: null,
                });
            }
            return accumulator;
        }, settingsOptions);

        return mergedOptions.sort((l, r) => {
            return l.label.localeCompare(r.label);
        });
    }, [field, parsedValue.value]);

    const selectedValues = useMemo(() => parsedValue.value.map((x) => x.name), [parsedValue.value]);

    const selectedOptions = useMemo(() => {
        return options.filter((o) => selectedValues.includes(o.value));
    }, [options, selectedValues]);

    const handleSelectChange = (
        valuesArg: ReadonlyArray<OptionData<string>> | OptionData<string> | null
    ) => {
        const isArray = valuesArg instanceof Array;
        let fieldValue: MultiSelectFieldValue;

        if (isArray) {
            if (!isMulti) throw new Error('unreachable code');
            // SV In the current implementation, only non-multiple choice fields can have a color.
            fieldValue = {
                value: valuesArg.map((x) => ({ name: x.value })),
                text: parsedValue.text || undefined,
            };
        } else {
            if (isMulti) throw new Error('unreachable code');
            fieldValue = {
                value: valuesArg ? [{ name: valuesArg.value }] : [],
                text: parsedValue.text || undefined,
            };
        }

        onSave(JSON.stringify(fieldValue), {
            color: !isArray ? valuesArg?.value ?? null : undefined,
        });
    };

    const handleExtraTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value: MultiSelectFieldValue = {
            value: parsedValue.value,
            text: e.target.value,
        };
        onSave(JSON.stringify(value));
    };

    return (
        <MultiSelectContainer>
            <Dropdown
                multiple={isMulti}
                showValueCounterAfter={3}
                ref={ref}
                cmosVariant="grey"
                placeholder={t('customizableFields.placeholders.select') + ' ' + field.name}
                options={options}
                value={selectedOptions}
                onChange={handleSelectChange}
                styles={{
                    indicatorsContainer: undefined,
                }}
                optionStyle={OptionStyle.checkbox}
            />
            {hasExtraText && (
                <TextField
                    value={parsedValue.text ?? ''}
                    onChange={handleExtraTextChange}
                    placeholder={`${t('commonLabels.enter')} ${field.name}`}
                />
            )}
        </MultiSelectContainer>
    );
}

const MultiSelectContainer = styled('div')({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 6,
});

const INPUT_COMPONENTS: Partial<Record<FieldType, ComponentType<CustomInputProps>>> = {
    ShortText: ShortTextInput,
    LongText: LongTextInput,
    Date: DateInput,
    Numeric: numericInput.NumericInput,
    Currency: currencyInput.CurrencyInput,
    Time: timeInput.TimeInput,
    Select: SelectInput,
    MultiSelect: MultiSelectInput,
};

function FieldErrorBoundary({ children }: React.PropsWithChildren<{}>) {
    return (
        <ErrorBoundary
            renderError={({ error }) => {
                let errorMessage: string = 'unknown';
                if (error instanceof Error) {
                    errorMessage = error.message;
                } else if (typeof error === 'string') {
                    errorMessage = error;
                }
                console.error(error);
                return (
                    <ErrorMessage>
                        <small>
                            <strong>CUSTOM FIELD ERROR</strong>
                        </small>
                        <br />
                        {errorMessage}
                    </ErrorMessage>
                );
            }}
        >
            {children}
        </ErrorBoundary>
    );
}

const ErrorMessage = styled('div')({
    border: '1px solid var(--danger)',
    padding: '8px',
    width: '100%',
});
