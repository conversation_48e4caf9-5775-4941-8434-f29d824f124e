import { CustomizableFieldValue, FieldValueWithName } from 'api/fields';
import { useState } from 'react';
import CustomField from './CustomField';

export type MoreJobInfoCustomFieldsProps = {
    blockId: string;
    fields: FieldValueWithName[];
};

export default function MoreJobInfoCustomFields({
    blockId,
    fields: initialFields,
}: MoreJobInfoCustomFieldsProps) {
    const [fields, setFields] = useState<FieldValueWithName[]>(initialFields);

    const handleSave = (value: string, id: string) => {
        setFields(fields.map((x) => (x.id === id ? { ...x, value: value } : x)));
    };

    return (
        <div>
            {fields.map((x) => (
                <CustomField
                    blockId={blockId}
                    field={x as CustomizableFieldValue}
                    onFieldSave={handleSave}
                />
            ))}
        </div>
    );
}
