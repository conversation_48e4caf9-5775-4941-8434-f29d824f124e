import { Box, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import SignaturesApi from 'api/Signatures';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

export type TechnicianSignatureProps = {
    orderId: string;
    scheduleEntryId: string;
};

export default function TechnicianSignature({
    orderId,
    scheduleEntryId,
}: TechnicianSignatureProps) {
    const { t } = useAppTranslation();

    const { data: url } = useQuery(
        ['wp', 'technician-signature-url', orderId, scheduleEntryId],
        () => SignaturesApi.getTechnicianSignatureUrl(orderId, scheduleEntryId)
    );

    return (
        <div>
            {url !== undefined && (
                <Box sx={{ marginBottom: '24px' }}>
                    <SInputLabel>
                        {t('workshopPlanner.moreJobInfoPopup.technicianSignature')}
                    </SInputLabel>
                    <DivSignatureContainer>
                        {url.length ? (
                            <ImgSignature alt="signature" src={url} />
                        ) : (
                            <DivPlaceholder>
                                {t(
                                    'workshopPlanner.moreJobInfoPopup.technicianSignaturePlaceholder'
                                )}
                            </DivPlaceholder>
                        )}
                    </DivSignatureContainer>
                </Box>
            )}
        </div>
    );
}

const SInputLabel = styled(InputLabel)({
    pointerEvents: 'none',
    fontSize: 14,
    lineHeight: '20px !important',
});

const DivSignatureContainer = styled('div')(({ theme }) => ({
    pointerEvents: 'none',
    opacity: 0.5,

    position: 'relative',
    border: 'solid 1px #C9CDD3',
    borderRadius: 5,
    textAlign: 'center',
    padding: 5,
    height: 94,
    width: 296,
    background: theme.palette.neutral[2],
}));

const DivPlaceholder = styled('div')({
    textAlign: 'left',
    paddingLeft: 5,
});

const ImgSignature = styled('img')({
    height: '100%',
    borderRadius: 5,
});
