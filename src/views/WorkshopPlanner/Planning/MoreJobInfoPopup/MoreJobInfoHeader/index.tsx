import { IconButton, styled, Typography } from '@mui/material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IconSize } from 'common/styles/IconSize';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectWpOrderViews } from 'store/slices/wp/orders';

export type MoreJobInfoHeaderProps = {
    orderId: string;
    onClose: () => void;
};

const selectData = createSelector(
    [selectWpOrderViews, (_, orderId: string) => orderId],
    (orders, orderId) => {
        const order = orders.find((x) => x.id === orderId);

        if (order) {
            return {
                orderNumber: order.number,
                customerName: `${order.customer.firstName} ${order.customer.lastName}`.trim(),
            };
        }

        return { orderNumber: '', customerName: '' };
    }
);

export default function MoreJobInfoHeader({ orderId, onClose }: MoreJobInfoHeaderProps) {
    const { t } = useAppTranslation();

    const { orderNumber, customerName } = useAppSelector((r) => selectData(r, orderId));

    return (
        <DivHeaderContainer>
            <header>
                <TypographyHeader variant="h4">
                    {t('workshopPlanner.moreJobInfoPopup.order', {
                        orderNumber,
                    })}
                    {' – ' + customerName}
                </TypographyHeader>
                <CloseButton onClick={onClose} size="large">
                    <CloseIcon size={IconSize.L} fill={'var(--neutral6)'} />
                </CloseButton>
                <DivPopupTitle>{t('workshopPlanner.moreJobInfoPopup.title')}</DivPopupTitle>
            </header>
        </DivHeaderContainer>
    );
}

const DivHeaderContainer = styled('div')(({ theme }) => ({
    padding: '30px 90px 5px 90px',
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
    height: 'fit-content',
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'end',
    borderRadius: '24px 24px 0 0',
    position: 'relative',
}));

const TypographyHeader = styled(Typography)(({ theme }) => ({
    height: '1.4em',
    display: 'flex',
    gap: 15,
    color: theme.palette.neutral[8],
}));

const CloseButton = styled(IconButton)(({ theme }) => ({
    position: 'absolute',
    top: 0,
    right: 0,
    color: theme.palette.neutral[7],
}));

const DivPopupTitle = styled('div')(({ theme }) => ({
    marginTop: 10,
    color: theme.palette.neutral[7],
    ...theme.typography.h5Inter,
}));
