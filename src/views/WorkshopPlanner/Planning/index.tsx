import { Pagination, paginationClasses, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { UserScheduleDto, getScheduleForDate } from 'api/common';
import { ShopScheduleApi } from 'api/settings';
import { TimeSpan } from 'api/utils/format';
import SchedulerWithGroups, {
    ItemReplacedCallback,
    SchedulerItemBase,
    schedulerLayoutClasses,
} from 'common/components/Scheduler';
import { GroupDecorationComponentProps } from 'common/components/Scheduler/GridCanvas/GroupDecorations';
import {
    SchedulerScrollbarControllerProvider,
    ScrollbarController,
    useSchedulerScrollbarController,
} from 'common/components/Scheduler/GridCanvas/scrollbarController';
import Projection from 'common/components/Scheduler/addons/Projection';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { DateTime, Duration } from 'luxon';
import moment from 'moment';
import React, {
    createContext,
    startTransition,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import Skeleton from 'react-loading-skeleton';
import { useSelector, useStore } from 'react-redux';
import { Subject } from 'utils/event';

import SpecialtiesApi from 'api/Specialties';
import useQueryParam from 'common/hooks/useQueryParam';
import { createSelector } from 'reselect';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { selectIanaTz, selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import {
    BlockProjection,
    BlockView,
    moveBlockThunk,
    selectBlock,
    selectBlockPreviewTime,
    selectPlanning,
    selectPlanningDisplayData,
    selectPlanningHasUnassignedBlocks,
    selectProjections,
} from 'store/slices/wp/plannings';
import ensureAppointmentIsPresentThunk from 'store/slices/wp/plannings/thunks/ensureAppointmentIsPresent';
import updateBlockThunk from 'store/slices/wp/plannings/thunks/updateBlock';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import {
    selectFilteredAppointmentIds,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import { useConvertAppointmentModalOptional } from '../ConvertOrderModal/context';
import { useOrderPopupOptional } from '../OrderPopup/context';
import { isStandardOperationsAllowed } from '../helpers';
import BlockProjectionRenderer from './BlockProjectionRenderer';
import EditBlockPopupContextProvider from './EditBlockPopup/context';
import { MoreJobInfoPopupProvider } from './MoreJobInfoPopup/context';
import PlanningBlock, { FilteredBlockView } from './PlanningBlock';
import PlanningStickyPart, { PlanningStickyPartHeader } from './PlanningStickyPart';
import PlusButton from './PlusButton';
import RequestPasswordPopupContextProvider from './RequestPasswordPopup/context';
import TeamMemberHeader from './TeamMemberHeader';
import { DroppingBlockContext } from './droppingBlockContext';

type PlanningProps = {
    planningId: number;
    vertical?: boolean;
    singleView?: boolean;

    /**
     * Selected date in the format "yyyy-MM-dd"
     */
    dateString: string;

    /**
     * Max height of the planning (including the header)
     */
    maxHeight?: number;

    availableWidth?: number;

    /**
     * Max number of rows per page, if not set all rows will be displayed without pagination
     */
    pagination?: number;

    /**
     * Options for auto sizing columns
     */
    autoSizeRowHeight?: boolean;

    /**
     * If set to true and maxHeight is set, the rows will be auto sized
     */
    autoSizeColumnWidth?: {
        availableWidth: number;
    };

    showUnassignedRow: boolean;

    disablePlusButton?: boolean;
    headerHeight?: number;
    borderless?: boolean;
    intervalMinutes?: number;
    customInterval?: {
        from: DateTime;
        to: DateTime;
    };
};

type ResizableBlock = FilteredBlockView & { isResizable?: boolean };

export const PlanningSkeleton = () => (
    <Skeleton
        style={{ borderRadius: 10, width: '100%', display: 'block' }}
        height={250}
        width="100%"
    />
);

const selectAll = createSelector(
    [(r: RootState) => r, (_, planningId: number) => planningId],
    (r, planningId) => {
        const filteredOrderIds = selectFilteredOrderIds(r);
        const filteredAppointmentIds = selectFilteredAppointmentIds(r);

        const blocks = selectPlanningDisplayData(r, planningId).map((x) =>
            x.type === 'order'
                ? {
                      ...x,
                      isFiltered:
                          filteredOrderIds.includes(x.order.id) ||
                          (!!x.order.appointmentId &&
                              filteredAppointmentIds.includes(x.order.appointmentId)),
                  }
                : {
                      ...x,
                      isFiltered:
                          filteredAppointmentIds.includes(x.appointment.id) ||
                          (!!x.appointment.orderId &&
                              filteredOrderIds.includes(x.appointment.orderId)),
                  }
        );

        return {
            planning: selectPlanning(r, planningId),
            blocks,
            hasUnassigned: selectPlanningHasUnassignedBlocks(r, planningId),
        };
    }
);

export const PLANNING_PAGINATION_HEIGHT = 40;

export default function Planning({
    dateString: selectedDate,
    planningId,
    vertical = false,
    singleView = false,
    autoSizeRowHeight = false,
    autoSizeColumnWidth,
    maxHeight,
    borderless = false,
    pagination,
    intervalMinutes = 30,
    showUnassignedRow: showUnassignedRowSettingsValue,
    customInterval,
    disablePlusButton,
}: PlanningProps) {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const [schedulerRef, setSchedulerRef] = useState<HTMLDivElement | null>(null);

    const userPermission = useSelector(selectUserPermission);
    const { allowEditWorkshopPlanner } = userPermission;

    const defaultInterval = usePlanningInterval(selectedDate);
    const { from, to } = customInterval ?? defaultInterval;

    const [queryUserKey] = useQueryParam('assignedTo');
    const [queryDate] = useQueryParam('startDate');
    const [dateToAutoScroll, setDateToAutoScroll] = useState<DateTime | null>(null);
    const [userKeyAutoScroll, setUserKeyAutoScroll] = useState<string | null>(null);

    useEffect(() => {
        if (!queryDate) return;

        const parsed = DateTime.fromISO(queryDate);

        if (parsed.isValid) {
            setDateToAutoScroll(parsed);
        }

        if (!queryUserKey) return;
        setUserKeyAutoScroll(queryUserKey);
    }, [queryDate, queryUserKey]);

    const [page, setPage] = useState(1);
    const [movingBlockId, setMovingBlockId] = useState<string | null>(null);
    const { planning, blocks, userIds, users, pagesCount, hasUnassigned } = usePlanningData(
        planningId,
        page,
        pagination
    );

    const appointments = useAppSelector((r) => r.wp.plannings.appointments);

    useEffect(() => {
        const missingIds = new Set<string>();

        for (const block of blocks) {
            if (
                block.type === 'order' &&
                block.order.appointmentId &&
                !appointments[block.order.appointmentId]
            ) {
                missingIds.add(block.order.appointmentId);
            }
        }

        missingIds.forEach((id) => {
            dispatch(ensureAppointmentIsPresentThunk({ id }));
        });
    }, [blocks, appointments, dispatch]);

    const firstFilteredBlock = useMemo(() => {
        const firstFilteredBlock = blocks.find((x) => x.isFiltered);

        return firstFilteredBlock
            ? {
                  time: firstFilteredBlock.startsAt,
                  userKey: firstFilteredBlock.user.id,
              }
            : null;
    }, [blocks]);

    const showUnassignedRow = showUnassignedRowSettingsValue || hasUnassigned;

    const wpSettings = useAppSelector(selectShopWpSettings);
    const showTeamMemberSpecialty = wpSettings?.showTeamMemberSpecialty || false;

    const { data: specialties = [] } = useQuery(
        ['specialties'],
        () => SpecialtiesApi.getSpecialties(),
        { staleTime: 1000, cacheTime: Infinity }
    );

    const renderGroup = useCallback(
        (g: string, v: boolean) => (
            <TeamMemberHeader
                userId={g}
                vertical={v}
                showTeamMemberSpecialty={showTeamMemberSpecialty}
                userSpecialty={
                    specialties.find((s) => s.id === users.find((u) => u.key === g)?.specialtyId)
                        ?.name ?? null
                }
            />
        ),
        [showTeamMemberSpecialty, specialties, users]
    );

    const onItemStartDragging = useCallback((item: BlockView) => {
        setMovingBlockId(item.id);
    }, []);

    const onItemStopDragging = useCallback(() => {
        setMovingBlockId(null);
    }, []);

    const onItemReplaced: ItemReplacedCallback<BlockView, string> = useCallback(
        (item, newPositionRelativeToFrom, userId) => {
            const newPosition = new TimeSpan(
                from.minute * 60 + from.hour * 3600 + newPositionRelativeToFrom.totalSeconds
            );
            dispatch(
                moveBlockThunk({
                    startsAt: DateTime.fromMillis(item.startsAt)
                        .set({
                            hour: newPosition.hours,
                            minute: newPosition.minutes,
                            second: 0,
                            millisecond: 0,
                        })
                        .toMillis(),
                    id: item.id,
                    userId,
                    timestamp: Date.now(),
                })
            );
        },
        [dispatch, from]
    );

    const handleItemResized = useCallback(
        (item: BlockView, newPositionRelativeToFrom: TimeSpan, newDuration: TimeSpan) => {
            const newPosition = new TimeSpan(
                from.minute * 60 + from.hour * 3600 + newPositionRelativeToFrom.totalSeconds
            );
            dispatch(
                updateBlockThunk({
                    id: item.id,
                    startsAt: DateTime.fromMillis(item.startsAt)
                        .set({
                            hour: newPosition.hours,
                            minute: newPosition.minutes,
                            second: 0,
                            millisecond: 0,
                        })
                        .toMillis(),
                    duration: newDuration.totalSeconds / 60,
                    timestamp: Date.now(),
                })
            );
        },
        [dispatch, from]
    );

    const handleItemStartResizing = useCallback((item: BlockView) => {
        setMovingBlockId(item.id);
    }, []);

    const handleItemStopResizing = useCallback(() => setMovingBlockId(null), []);

    const orderPopup = useOrderPopupOptional();
    const convertModal = useConvertAppointmentModalOptional();

    const onBlockDoubleClick = useCallback(
        (block: BlockView) => {
            if (block.type === 'order') {
                orderPopup?.open(block.order.number);
            } else if (block.type === 'appointment') {
                convertModal?.open(block.appointment.id);
            }
        },
        [orderPopup, convertModal]
    );

    const droppingBlockSubject = useMemo(() => new Subject<string | null>(null), []);
    const onDrop = useCallback(
        (positionTimestamp: number, userId: string, _event: Event) => {
            const blockId = droppingBlockSubject.value;
            if (!blockId) return;

            dispatch(
                moveBlockThunk({
                    id: blockId,
                    userId,
                    startsAt: positionTimestamp,
                    timestamp: Date.now(),
                })
            );
        },
        [droppingBlockSubject, dispatch]
    );

    const renderItem = useCallback(
        (item: FilteredBlockView) => {
            return planning ? (
                <PlanningBlock
                    planningId={planning.id}
                    onDoubleClick={onBlockDoubleClick}
                    block={item}
                    outerContainer={schedulerRef}
                    isDragging={movingBlockId === item.id}
                    vertical={vertical}
                    singleView={singleView}
                />
            ) : null;
        },
        [planning, onBlockDoubleClick, schedulerRef, movingBlockId, vertical, singleView]
    );

    const getItemStatic = useCallback(
        (item: BlockView): boolean => item.new || !allowEditWorkshopPlanner,
        [allowEditWorkshopPlanner]
    );

    const store = useStore();
    const getDroppingItemDuration = useCallback((): number | false => {
        if (!droppingBlockSubject.value) return false;

        const block = selectBlock(store.getState(), droppingBlockSubject.value);
        if (!block) return false;

        return block.duration;
    }, [droppingBlockSubject, store]);

    if (userIds.length === 0) return null;

    const planningName = planning?.isTechnicians
        ? t('workshopPlanner.technicians.title')
        : planning?.name;

    const SchedulerComponent: typeof SchedulerWithGroups = borderless
        ? BorderlessSchedulerWithGroups
        : SchedulerWithGroups;

    if (pagination && pagesCount > 1 && maxHeight) {
        maxHeight -= PLANNING_PAGINATION_HEIGHT;
    }

    // calculation various sizes
    const headerHeight = vertical ? 65 : 40;
    const sideWidth = vertical ? 90 : 130;
    const defaultRowHeight = 100;
    const defaultColumnWidth = 120;

    const contentAreaHeight = maxHeight
        ? Math.max(maxHeight - headerHeight, defaultRowHeight)
        : undefined;
    const contentAreaWidth = autoSizeColumnWidth
        ? Math.max(autoSizeColumnWidth.availableWidth - sideWidth, defaultColumnWidth)
        : undefined;

    // NOTE (MB) we could just use ResizeObserver inside Scheduler which will make "auto-size columns" a built-in feature
    // but that would make Schedule even more complicated
    // if in the future we have a greater need for auto-sizing rows/columns we can do that, for now though - this will do
    const stepsCount = Math.ceil(to.diff(from).shiftTo('minutes').minutes / intervalMinutes);
    const groupsCount = userIds.length + (showUnassignedRow ? 1 : 0);

    const columnWidth = contentAreaWidth
        ? vertical
            ? Math.max(10, contentAreaWidth / groupsCount)
            : contentAreaWidth / stepsCount
        : defaultColumnWidth;

    const minRowsCountToAutoSize = 7;
    const rowHeight =
        autoSizeRowHeight &&
        contentAreaHeight &&
        (vertical || groupsCount >= minRowsCountToAutoSize)
            ? vertical
                ? contentAreaHeight / stepsCount
                : Math.max(10, contentAreaHeight / groupsCount)
            : defaultRowHeight;

    return (
        <>
            <PlanningIdContext.Provider value={planningId}>
                <DroppingBlockContext.Provider value={droppingBlockSubject}>
                    <SchedulerScrollbarControllerProvider>
                        <PlanningAutoScroll
                            users={users}
                            selectedDate={from}
                            selectedTime={dateToAutoScroll}
                            selectedUserKey={userKeyAutoScroll}
                            firstFilteredBlockTime={firstFilteredBlock?.time}
                            firstFilteredBlockUserKey={firstFilteredBlock?.userKey}
                        />
                        <MoreJobInfoPopupProvider>
                            <EditBlockPopupContextProvider>
                                <RequestPasswordPopupContextProvider>
                                    <RestrictedZoneProvider
                                        from={from}
                                        to={to}
                                        planningId={planningId}
                                    >
                                        {(restrictedZones) => (
                                            <SchedulerComponent<ResizableBlock, string>
                                                schedulerRef={setSchedulerRef}
                                                getItemKey={getKey}
                                                getItemIsResizable={getItemIsResizable}
                                                title={planningName}
                                                allowOverlap
                                                vertical={vertical}
                                                from={from.toMillis()}
                                                to={to.toMillis()}
                                                items={blocks}
                                                renderItem={renderItem}
                                                maxHeight={maxHeight}
                                                stepLengthInMinutes={intervalMinutes}
                                                stepsCount={stepsCount}
                                                columnWidth={columnWidth}
                                                restrictedZones={restrictedZones}
                                                getItemStatic={getItemStatic}
                                                getItemTimestamp={getStartsAt}
                                                getItemMinutesDuration={getDuration}
                                                renderGroup={renderGroup}
                                                groups={userIds}
                                                getGroupKey={getGroupKey}
                                                getItemGroupKey={getItemGroupKey}
                                                onItemReplaced={onItemReplaced}
                                                onItemStartDragging={onItemStartDragging}
                                                onItemStopDragging={onItemStopDragging}
                                                GroupDecorationComponent={
                                                    PlanningGroupDecorationComponent
                                                }
                                                headerHeight={headerHeight}
                                                sideWidth={sideWidth}
                                                rowHeight={rowHeight}
                                                isDroppable
                                                isResizable
                                                onItemStartResizing={handleItemStartResizing}
                                                onItemStopResizing={handleItemStopResizing}
                                                onItemResized={handleItemResized}
                                                droppedItemDurationInMinutes={
                                                    getDroppingItemDuration
                                                }
                                                onDrop={onDrop}
                                                hideTitleFirstInterval
                                                stickyPart={
                                                    showUnassignedRow
                                                        ? {
                                                              header: (
                                                                  <PlanningStickyPartHeader
                                                                      vertical={vertical}
                                                                  />
                                                              ),
                                                              content: (
                                                                  <PlanningStickyPart
                                                                      vertical={vertical}
                                                                      singleView={singleView}
                                                                  />
                                                              ),
                                                          }
                                                        : undefined
                                                }
                                            >
                                                <Projections
                                                    blocks={blocks}
                                                    customUserIds={userIds}
                                                    outerContainer={schedulerRef}
                                                    vertical={vertical}
                                                    singleView={singleView}
                                                />
                                                {allowEditWorkshopPlanner && !disablePlusButton && (
                                                    <PlusButton
                                                        vertical={vertical}
                                                        planningId={planningId}
                                                    />
                                                )}
                                            </SchedulerComponent>
                                        )}
                                    </RestrictedZoneProvider>
                                </RequestPasswordPopupContextProvider>
                            </EditBlockPopupContextProvider>
                        </MoreJobInfoPopupProvider>
                    </SchedulerScrollbarControllerProvider>
                </DroppingBlockContext.Provider>
            </PlanningIdContext.Provider>
            {pagination && pagesCount > 1 && (
                <Pagination
                    sx={{
                        paddingTop: '7px',
                        [`& .${paginationClasses.ul}`]: {
                            justifyContent: 'center',
                        },
                        height: PLANNING_PAGINATION_HEIGHT,
                        position: 'relative',

                        '::after': {
                            display: 'block',
                            left: 0,
                            right: 0,
                            top: -1,
                            background: 'var(--neutral3)',
                            content: '" "',
                            position: 'absolute',
                            height: '1px',
                        },
                    }}
                    size="small"
                    onChange={(_, page) => {
                        setPage(page);
                    }}
                    color="primary"
                    count={pagesCount}
                    page={page}
                />
            )}
        </>
    );
}

const BorderlessSchedulerWithGroups = styled(SchedulerWithGroups)({
    [`& .${schedulerLayoutClasses.layout}`]: {
        border: 'none',
        borderRadius: 0,
    },
    [`& .${schedulerLayoutClasses.title}`]: {
        borderLeft: 'none',
        borderTop: 'none',
        height: 'calc(var(--wps-header-height) - 1px)',
    },
}) as typeof SchedulerWithGroups;

function usePlanningData(planningId: number, page: number, pageSize: number | undefined) {
    pageSize = pageSize ? Math.max(1, pageSize) : undefined;
    page = Math.max(0, page);

    const globalSettings = useAppSelector(selectSettings);
    const {
        blocks: allBlocks,
        planning,
        hasUnassigned,
    } = useAppSelector(
        useCallback((r) => selectAll(r, planningId), [planningId]),
        isEqual
    );

    const standardOperationOrderTypes = useMemo(() => {
        try {
            return JSON.parse(globalSettings.standardOperationOrderTypes) ?? [];
        } catch (error) {
            console.error('Error parsing standardOperationOrderTypes:', error);
            return [];
        }
    }, [globalSettings.standardOperationOrderTypes]);

    if (planning?.isAdvisors) throw new Error('planning component cannot render advisors planning');

    const allDisplayedUsers = planning?.users;
    const { users, blocks } = useMemo(() => {
        if (!allDisplayedUsers)
            return {
                users: [],
                blocks: [],
            };

        if (pageSize !== undefined) {
            const users = allDisplayedUsers.slice((page - 1) * pageSize, page * pageSize);
            const userIds = users.map((x) => x.key);
            return {
                users,
                blocks: allBlocks
                    .filter((x) => userIds.includes(x.user.id))
                    .map((x) => ({
                        ...x,
                        isResizable: isBlockResizable(
                            x,
                            globalSettings.enableStandardOperations,
                            standardOperationOrderTypes
                        ),
                    })),
            };
        } else {
            return {
                users: allDisplayedUsers,
                blocks: allBlocks.map((x) => ({
                    ...x,
                    isResizable: isBlockResizable(
                        x,
                        globalSettings.enableStandardOperations,
                        standardOperationOrderTypes
                    ),
                })),
            };
        }
    }, [
        allDisplayedUsers,
        page,
        pageSize,
        allBlocks,
        standardOperationOrderTypes,
        globalSettings.enableStandardOperations,
    ]);
    const userIds = useMemo(() => users.map((u) => u.key), [users]);

    return {
        userIds,
        blocks,
        planning,
        users,
        pagesCount:
            pageSize === undefined
                ? 1
                : Math.ceil((allDisplayedUsers?.length ?? 0) / (pageSize ?? 1)),
        hasUnassigned,
    };
}

function isBlockResizable(
    block: BlockView,
    enableStandardOperations: boolean,
    standardOperationOrderTypes: string[]
) {
    if (block.type !== 'order') {
        return true;
    }

    const canUseStandardOperations = isStandardOperationsAllowed(
        !!block.order,
        !block.orderTypeId,
        enableStandardOperations,
        standardOperationOrderTypes,
        block.orderTypeId
    );

    return !canUseStandardOperations;
}

export function PlanningAutoScroll({
    users,
    selectedDate,
    selectedTime,
    selectedUserKey,
    firstFilteredBlockTime,
    firstFilteredBlockUserKey,
}: {
    users: { schedule: UserScheduleDto }[];
    selectedDate: DateTime;
    selectedTime?: DateTime | null;
    selectedUserKey?: string | null;
    firstFilteredBlockTime?: number | null;
    firstFilteredBlockUserKey?: string | null;
}) {
    // TODO (MB) this should probably be fetched once at the initialization step
    const { data: shopSchedule, refetch: refetchShopSchedule } = useQuery(
        ['wp', 'shop-schedule'],
        async () => {
            const response = await ShopScheduleApi.get();
            return response;
        },
        {
            enabled: false,
        }
    );
    const ctxProvider = useSchedulerScrollbarController();
    if (!ctxProvider)
        throw new Error('invalid operation: useSchedulerScrollbarController not found');

    const wasScrolled = useRef(false);
    const { time } = useAppSelector(selectBlockPreviewTime);

    // NOTE (AK) Handle case, when there is preview block with start time
    if (time) {
        wasScrolled.current = true;
    }

    const firstFilteredBlockTimeRef = useRef<number | null>(null);
    const firstFilteredBlockUserKeyRef = useRef<string | null>(null);

    firstFilteredBlockTimeRef.current = firstFilteredBlockTime ?? null;
    firstFilteredBlockUserKeyRef.current = firstFilteredBlockUserKey ?? null;

    const scrollToUserTime = useCallback(
        (
            scrollController: ScrollbarController,
            selectedTime: DateTime,
            selectedUserKey: string
        ) => {
            scrollController.scrollToGroup(selectedUserKey, {
                smooth: true,
            });
            setTimeout(() => {
                scrollController.scrollToTimestamp(selectedTime.toMillis(), 'start', true);
            }, 1000);
        },
        []
    );

    useEffect(() => {
        return ctxProvider.subscribe((scrollController) => {
            if (wasScrolled.current) return;
            if (!scrollController) return;

            if (firstFilteredBlockTimeRef.current && firstFilteredBlockUserKeyRef.current) {
                scrollToUserTime(
                    scrollController,
                    DateTime.fromMillis(firstFilteredBlockTimeRef.current),
                    firstFilteredBlockUserKeyRef.current
                );
                wasScrolled.current = true;
                return;
            }
            if (selectedTime && selectedUserKey) {
                scrollToUserTime(scrollController, selectedTime, selectedUserKey);
                wasScrolled.current = true;
                return;
            }

            const isToday = selectedDate.hasSame(DateTime.now(), 'day');
            if (isToday) {
                scrollController.scrollToTimestamp(Date.now(), 'center');
            } else {
                const earliestStartTime = getEarliestTimestamp(users, selectedDate);
                if (earliestStartTime !== null) {
                    scrollController.scrollToTimestamp(earliestStartTime, 'start');
                } else if (shopSchedule) {
                    const schedule = shopSchedule.find(
                        (x) => (x.dayNumber === 0 ? 7 : x.dayNumber) === selectedDate.weekday
                    );
                    const startTime = schedule ? schedule.opening : '08:00:00';
                    const shopStart = selectedDate
                        .set({ millisecond: 0, second: 0, minute: 0, hour: 0 })
                        .plus({
                            second: TimeSpan.fromString(startTime).totalSeconds,
                        });
                    scrollController.scrollToTimestamp(shopStart.toMillis(), 'start');
                } else {
                    refetchShopSchedule();
                    return;
                }
            }

            wasScrolled.current = true;
        });
    }, [
        ctxProvider,
        selectedDate,
        users,
        shopSchedule,
        refetchShopSchedule,
        selectedTime,
        selectedUserKey,
        scrollToUserTime,
    ]);

    useEffect(() => {
        const scrollController = ctxProvider?.scrollbarContext;

        if (scrollController && firstFilteredBlockTime && firstFilteredBlockUserKey) {
            scrollToUserTime(
                scrollController,
                DateTime.fromMillis(firstFilteredBlockTime),
                firstFilteredBlockUserKey
            );
        }
    }, [firstFilteredBlockTime, firstFilteredBlockUserKey, ctxProvider, scrollToUserTime]);

    return null;
}

function getEarliestTimestamp(
    users: { schedule: UserScheduleDto }[],
    selectedDate: DateTime
): number | null {
    const schedules = users.map((x) => getScheduleForDate(x.schedule, selectedDate));

    let earliestTimeOfDayInMillis = Number.MAX_SAFE_INTEGER;

    for (const schedule of schedules) {
        if (schedule.windows.length === 0) continue;
        const firstWindow = schedule.windows[0];
        const startTime = Duration.fromISOTime(firstWindow.from);
        const startTimeInMillis = startTime.toMillis();
        if (startTimeInMillis < earliestTimeOfDayInMillis) {
            earliestTimeOfDayInMillis = startTimeInMillis;
        }
    }

    if (earliestTimeOfDayInMillis === Number.MAX_SAFE_INTEGER) {
        return null;
    }

    earliestTimeOfDayInMillis = Math.max(0, earliestTimeOfDayInMillis - 60 * 60 * 1000);

    return (
        selectedDate.set({ millisecond: 0, second: 0, minute: 0, hour: 0 }).toMillis() +
        earliestTimeOfDayInMillis
    );
}

const getGroupKey = (g: string) => g;
const getKey = (b: BlockView) => b.id;
const getItemIsResizable = (b: ResizableBlock) => b.isResizable ?? false;
const getItemGroupKey = (b: BlockView) => b.user.id;
const getDuration = (b: BlockView) => b.duration;
const getStartsAt = (b: BlockView) => b.startsAt;

function usePlanningInterval(dateString: string) {
    return useMemo(() => {
        const from = DateTime.fromISO(dateString, { zone: 'local' }).set({
            millisecond: 0,
            second: 0,
            minute: 0,
            hour: 0,
        });
        const to = from.plus({ day: 1 });

        return {
            dateString,
            from,
            to,
        };
    }, [dateString]);
}

const PlanningIdContext = createContext<number | null>(null);

export function usePlanningId(): number {
    const id = useContext(PlanningIdContext);
    if (id === null)
        throw new Error('usePlanningId cannot be called outside of Planning component');
    return id;
}

type ProjectionsProps = {
    blocks: FilteredBlockView[];
    customUserIds?: string[];
    outerContainer: HTMLElement | null;
    vertical: boolean;
    singleView: boolean;
};

function Projections({
    blocks,
    customUserIds,
    outerContainer,
    vertical,
    singleView,
}: ProjectionsProps) {
    const planningId = usePlanningId();
    const projections = useAppSelector(
        (r) => selectProjections(r, blocks, planningId, customUserIds),
        isEqual
    );

    return (
        <Projection<BlockProjection>
            items={projections}
            getKey={getPKey}
            getGroupKey={getPGroupKey}
            getDuration={getPDuration}
            getStartsAt={getPStartsAt}
            itemComponent={BlockProjectionRenderer}
            outerContainer={outerContainer}
            vertical={vertical}
            singleView={singleView}
        />
    );
}

const getPKey = (p: BlockProjection) => {
    if (p.type === 'absence') {
        return `${p.absence.id}-${p.userId}`;
    }
    return p.type;
};
const getPDuration = (p: BlockProjection) => p.duration;
const getPStartsAt = (p: BlockProjection) => p.startsAt;
const getPGroupKey = (p: BlockProjection) => {
    if (p.type === 'editPreview') return p.block.user.id;
    return p.userId;
};

function PlanningGroupDecorationComponent({
    group,
    vertical,
}: GroupDecorationComponentProps<string>) {
    const zones = useContext(RestrictedZonesContext);
    const userZones = useMemo(() => zones.filter((x) => x.groupKey === group), [zones, group]);

    return (
        <>
            {userZones.map((x) => (
                <RestrictedZonePlaceholder
                    key={x.timestamp as number}
                    zone={x}
                    vertical={vertical}
                />
            ))}
        </>
    );
}

type RestrictedZonePlaceholderProps = {
    zone: SchedulerItemBase;
    vertical: boolean;
};

function RestrictedZonePlaceholder({ zone, vertical }: RestrictedZonePlaceholderProps) {
    const ctx = useSchedulerContext();
    const timelineCoord =
        moment(zone.timestamp).diff(moment(ctx.interval.from), 'm') *
        ctx.dimensions.pixelsPerMinuteRatio;
    const timelineIntervalSize = ctx.dimensions.pixelsPerMinuteRatio * zone.minutesDuration;
    return (
        <ZoneBlock
            data-minutes={zone.minutesDuration}
            style={
                vertical
                    ? { top: timelineCoord, width: '100%', height: timelineIntervalSize }
                    : { left: timelineCoord, width: timelineIntervalSize, height: '100%' }
            }
        />
    );
}

const ZoneBlock = styled('div')({
    position: 'absolute',
    background: 'var(--neutral3)',
});

const RestrictedZonesContext = createContext<SchedulerItemBase[]>([]);

function RestrictedZoneProvider({
    planningId,
    children,
    from,
    to,
}: {
    planningId: number;
    children: (rzs: SchedulerItemBase[]) => React.ReactNode;
    from: DateTime;
    to: DateTime;
}) {
    const planning = useAppSelector(
        useCallback((r) => selectPlanning(r, planningId), [planningId]),
        isEqual
    );
    if (planning?.isAdvisors) throw new Error('planning component cannot render advisors planning');

    const users = useMemo(() => planning?.users ?? [], [planning?.users]);
    const ianaTz = useAppSelector(selectIanaTz);

    const [restrictedZones, setRestrictedZones] = useState<SchedulerItemBase[]>([]);

    useEffect(() => {
        window.requestIdleCallback(() => {
            // NOTE (MB) getAllRestrictedZones is a (relatively) expensive function call
            const zones = getAllRestrictedZones(users, ianaTz, from, to);
            startTransition(() => {
                setRestrictedZones(zones);
            });
        });
    }, [ianaTz, from, to, users]);

    return (
        <RestrictedZonesContext.Provider value={restrictedZones}>
            {children(restrictedZones)}
        </RestrictedZonesContext.Provider>
    );
}

function getAllRestrictedZones(
    users: { key: string; schedule: UserScheduleDto }[],
    timezone: string,
    from: DateTime,
    to: DateTime
): SchedulerItemBase[] {
    return users.map((x) => getRestrictedZones(x.key, x.schedule, timezone, from, to)).flat();
}

/**
 *
 * @param schedule schedule of the user
 */
export function getRestrictedZones(
    groupKey: string,
    schedule: UserScheduleDto,
    scheduleTimezone: string,
    from: DateTime,
    to: DateTime
): SchedulerItemBase[] {
    const fromLocal = from.setZone(scheduleTimezone);
    const toLocal = to.setZone(scheduleTimezone);
    const rzs: SchedulerItemBase[] = [];

    let startOfPeriod = fromLocal;

    // iterate over (potentially) multiple days in the provided interval [from, to)
    while (startOfPeriod < toLocal) {
        const daySchedule = getScheduleForDate(schedule, startOfPeriod);
        mustNotOverlap(daySchedule.windows);
        const midnight = startOfPeriod.set({ millisecond: 0, second: 0, minute: 0, hour: 0 });
        const nextDayMidnight = midnight.plus({ day: 1 });
        const endOfPeriod = toLocal.hasSame(startOfPeriod, 'day') ? toLocal : nextDayMidnight;

        if (daySchedule.windows.length === 0) {
            // whole day is an RZ
            rzs.push({
                groupKey,
                minutesDuration: endOfPeriod.diff(startOfPeriod, 'minutes').minutes,
                timestamp: startOfPeriod.toMillis(),
            });
        } else {
            let cursor = startOfPeriod;

            for (let i = 0; i < daySchedule.windows.length; i++) {
                const w = daySchedule.windows[i];
                const startsAt = midnight.plus(Duration.fromISOTime(w.from));
                const endsAt = midnight.plus(Duration.fromISOTime(w.to));

                const rzDuration = startsAt.diff(cursor, 'minute').as('minutes');
                if (rzDuration <= 0) {
                    cursor = endsAt;
                    continue;
                }

                rzs.push({
                    groupKey,
                    minutesDuration: rzDuration,
                    timestamp: cursor.toMillis(),
                });
                cursor = endsAt;
            }

            const lastWindowEnd = midnight.plus(
                Duration.fromISOTime(daySchedule.windows[daySchedule.windows.length - 1].to)
            );
            let endOfDayOrPeriod = midnight.plus({ day: 1 });
            if (endOfDayOrPeriod > to) endOfDayOrPeriod = to;
            const closingRzDuration = endOfDayOrPeriod.diff(lastWindowEnd).as('minutes');
            if (closingRzDuration >= 1) {
                rzs.push({
                    groupKey,
                    minutesDuration: closingRzDuration,
                    timestamp: lastWindowEnd.toMillis(),
                });
            }
        }

        startOfPeriod = endOfPeriod;
    }

    return rzs;
}

function mustNotOverlap(windows: UserScheduleDto.ScheduleWindow[]): void | never {
    if (windows.length <= 1) return;

    for (let i = 1; i < windows.length; i++) {
        const w = windows[i];
        const prev = windows[i - 1];
        const start = Duration.fromISOTime(w.from);
        const prevEnd = Duration.fromISOTime(prev.to);
        if (prevEnd > start) throw new Error('user schedule contains overlapping periods');
    }
}
