import { Box, Divider, IconButton, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { RoundWarningIcon } from 'common/components/Icons/RoundWarningIcon';
import { TextFormField } from 'common/components/Inputs';
import PasswordField from 'common/components/Inputs/PasswordField';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import { useAppDispatch } from 'store';
import { verifyUserPasswordThunk } from 'store/slices/wp/plannings';
import { BlockPopupBaseProps } from '../BlockPopupBase';
import { useRequestPasswordModalStyles } from './css';
type RequestPasswordPopupProps = BlockPopupBaseProps & {
    onSuccess: (userKey: string) => void;
    user: {
        id: string;
        name: string;
        userName: string;
    };
    buttonTitle: string;
    closeOnSuccess: boolean;
};

const ContentBlock = styled('div')(({ theme }) => ({
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: 500,
    paddingTop: '25px',
}));

const BodyBlock = styled('div')(({ theme }) => ({
    border: '1px solid #FFC626',
    borderRadius: '16px',
}));

const ModalHeader = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'end',
    flexDirection: 'row',
}));

const Message = styled('p')(({ theme }) => ({
    width: '365px',
    height: '30px',
    fontSize: '12px',
    lineHeight: '15px',
    textAlign: 'center',
    marginLeft: '55px',
    marginRight: '55px',
}));

export default function RequestPasswordPopup({
    onSuccess,
    user,
    buttonTitle,
    closeOnSuccess,
    ...props
}: RequestPasswordPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const styles = useRequestPasswordModalStyles();
    const toasters = useToasters();
    const [password, setPassword] = useState('');
    const onEnterKeyPressHelper = () => {
        verifyLoginMutation.mutate();
    };

    const verifyLoginMutation = useMutation({
        mutationFn: async () => {
            const result = await dispatch(
                verifyUserPasswordThunk({
                    userName: user.userName,
                    password: password,
                })
            );
            if (result.meta.requestStatus === 'rejected') throw result.payload;

            if (result.payload) {
                onSuccess(result.payload as string);
            } else {
                throw result.payload;
            }
        },
        onSuccess: () => {
            if (closeOnSuccess) {
                setPassword('');
                props.onClose();
            }
        },
        onError: () => {
            toasters.danger(
                t('workshopPlanner.requestPasswordBlock.pleaseTryAgain'),
                t('workshopPlanner.requestPasswordBlock.incorrectPassword')
            );
        },
    });

    return (
        <Modal open={props.open} onClose={props.onClose}>
            <BodyBlock>
                <ModalHeader>
                    <IconButton onClick={props.onClose} sx={{ top: 10, right: 10 }} size="small">
                        <CloseIcon size={30} />
                    </IconButton>
                </ModalHeader>
                <ContentBlock onMouseDown={(e) => e.stopPropagation()}>
                    <Box>
                        <RoundWarningIcon style={{ margin: 5 }} size={51} fill={Colors.Warning} />
                    </Box>
                    <Box
                        sx={{
                            textAlign: 'center',
                            lineHeight: 1,
                            marginLeft: '68px',
                            marginRight: '68px',
                        }}
                    >
                        <h2>
                            {t('workshopPlanner.requestPasswordBlock.assignToMessage', {
                                name: user.name,
                            })}
                        </h2>
                    </Box>
                    <Divider
                        sx={{
                            width: '271px',
                            height: '0px',
                            border: '1px solid #E5E7EA',
                            transform: 'rotate(-0.21deg)',
                        }}
                    />
                    <Message>
                        {t('workshopPlanner.requestPasswordBlock.requestPasswordMessage', {
                            name: user.name,
                            buttonTitle: buttonTitle,
                        })}
                    </Message>
                    <Box sx={{ minWidth: 'min(360px, 92%)', alignSelf: 'center' }}>
                        <TextFormField
                            dataTestId="email"
                            size="medium"
                            label={t('login.emailLabel')}
                            type={'text'}
                            inputWrapperClasses={{ self: styles.emailLabel }}
                            value={user.userName}
                            isRequired={true}
                            disabled={true}
                            inputProps={{
                                style: {
                                    backgroundColor: Colors.Neutral2,
                                    WebkitTextFillColor: Colors.Black,
                                },
                            }}
                        />
                        <PasswordField
                            dataTestId="password"
                            size="medium"
                            label={t('login.passwordLabel')}
                            placeholder={t('login.passwordPlaceholderLabel')}
                            onChange={(e) => setPassword(e.target.value)}
                            value={password}
                            isRequired={true}
                            isInvalid={verifyLoginMutation.isError}
                            onEnterPress={onEnterKeyPressHelper}
                            enableEnterComplete={true}
                        />
                    </Box>
                    <div style={{ marginTop: '23px', marginBottom: '37px' }}>
                        <Button
                            w={150}
                            cmosSize={'medium'}
                            onClick={() => verifyLoginMutation.mutate()}
                            disabled={verifyLoginMutation.isLoading}
                            label={buttonTitle}
                            color={Colors.Warning}
                            showLoader={verifyLoginMutation.isLoading}
                        />
                    </div>
                </ContentBlock>
            </BodyBlock>
        </Modal>
    );
}
