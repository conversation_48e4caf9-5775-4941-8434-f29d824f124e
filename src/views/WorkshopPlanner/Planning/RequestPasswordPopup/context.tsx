import { createContext, use<PERSON>allback, useContext, useMemo, useState } from 'react';
import RequestPasswordPopup from '.';

interface IRequestPasswordPopupContext {
    open(parameters: {
        onSuccess: (userKey: string) => void;
        user: {
            id: string;
            name: string;
            userName: string;
        };
        buttonTitle: string;
        closeOnSuccess: boolean;
        anchorElement?: HTMLElement;
    }): void;
}

const RequestPasswordPopupContext = createContext<IRequestPasswordPopupContext | null>(null);

export function useRequestPasswordPopupContext() {
    const ctx = useContext(RequestPasswordPopupContext);
    if (!ctx) throw new Error('RequestPasswordPopupContext is not available');
    return ctx;
}

export default function RequestPasswordPopupContextProvider({
    children,
}: React.PropsWithChildren<{}>) {
    const [editState, setEditState] = useState<{
        onSuccess: (userKey: string) => void;
        user: {
            id: string;
            name: string;
            userName: string;
        };
        buttonTitle: string;
        closeOnSuccess: boolean;
        anchorEl: HTMLElement | null;
    }>({
        onSuccess: () => {},
        user: {
            id: '',
            name: '',
            userName: '',
        },
        buttonTitle: '',
        closeOnSuccess: false,
        anchorEl: null,
    });
    const ctx: IRequestPasswordPopupContext = useMemo(
        () => ({
            open: (parameters) => {
                setEditState({
                    onSuccess: parameters.onSuccess,
                    user: parameters.user,
                    buttonTitle: parameters.buttonTitle,
                    closeOnSuccess: parameters.closeOnSuccess,
                    anchorEl: parameters.anchorElement ?? null,
                });
            },
        }),
        []
    );
    const onClose = useCallback(
        () =>
            setEditState({
                onSuccess: () => {},
                user: {
                    id: '',
                    name: '',
                    userName: '',
                },
                buttonTitle: '',
                closeOnSuccess: false,
                anchorEl: null,
            }),
        []
    );

    return (
        <RequestPasswordPopupContext.Provider value={ctx}>
            <RequestPasswordPopup
                anchorEl={editState.anchorEl}
                onClose={onClose}
                onSuccess={editState.onSuccess}
                user={editState.user}
                buttonTitle={editState.buttonTitle}
                closeOnSuccess={editState.closeOnSuccess}
                open={!!editState.anchorEl}
            />
            {children}
        </RequestPasswordPopupContext.Provider>
    );
}
