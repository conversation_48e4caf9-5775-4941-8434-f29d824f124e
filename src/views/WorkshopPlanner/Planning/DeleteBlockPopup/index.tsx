import { Box, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useAppDispatch, useAppSelector } from 'store';
import { selectRepairShopWpConfiguration } from 'store/slices/globalSettingsSlice/selectors';
import { deleteBlockThunk } from 'store/slices/wp/plannings';
import BlockPopupBase, { BlockPopupBaseProps } from '../BlockPopupBase';

type DeleteBlockPopupProps = BlockPopupBaseProps & {
    blockId: string;
};

const AreYouSure = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    width: 310,
    color: theme.palette.neutral[7],
    textAlign: 'center',
    margin: '0 0 10px 0',
    boxSizing: 'border-box',
    padding: '0 60px',
}));

export default function DeleteBlockPopup({ blockId, ...props }: DeleteBlockPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const deleteBlockMutation = useMutation(
        async () => {
            const result = await dispatch(deleteBlockThunk({ id: blockId }));
            if (result.meta.requestStatus === 'rejected') throw result.payload;
        },
        {
            onSuccess: () => {
                if (enableWorkshopJobIntegration) {
                    toasters.success(
                        t('workshopPlanner.deleteBlock.successIntegrationNotif.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.deleteBlock.successIntegrationNotif.title')
                    );
                } else {
                    toasters.success(
                        t('workshopPlanner.deleteBlock.successNotif.text'),
                        t('workshopPlanner.deleteBlock.successNotif.title')
                    );
                }
                props.onClose();
            },
            onError: (error: any) => {
                if (enableWorkshopJobIntegration) {
                    toasters.danger(
                        t('workshopPlanner.deleteBlock.errorIntegrationNotif.text', {
                            errorMessage: error?.message,
                        }),
                        t('workshopPlanner.deleteBlock.errorIntegrationNotif.title', {
                            integrationAccountName,
                        })
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
        }
    );

    return (
        <BlockPopupBase {...props}>
            <AreYouSure>{t('workshopPlanner.deleteBlock.text')}</AreYouSure>
            <Box display="flex" alignItems="center" justifyContent="center" gap={0.5}>
                <Button
                    w={120}
                    cmosSize={'small'}
                    onClick={props.onClose}
                    disabled={deleteBlockMutation.isLoading}
                    label={t('commonLabels.cancel')}
                    color={Colors.Neutral3}
                />
                <Button
                    w={120}
                    cmosSize={'small'}
                    showLoader={deleteBlockMutation.isLoading}
                    onClick={() => deleteBlockMutation.mutate()}
                    disabled={deleteBlockMutation.isLoading}
                    color={Colors.Error}
                    label={t('workshopPlanner.deleteBlock.yes')}
                />
            </Box>
        </BlockPopupBase>
    );
}
