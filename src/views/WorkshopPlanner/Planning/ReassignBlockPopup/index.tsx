import { Box, LinearProgress, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import WpApi from 'api/workshopPlanner';
import { Button } from 'common/components/Button';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useEffect, useRef, useState } from 'react';
import { hasCode, isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { selectUser } from 'store/slices/users';
import { selectBlock, selectPlanning } from 'store/slices/wp/plannings';
import reassignBlockThunk from 'store/slices/wp/plannings/thunks/reassignBlock';
import UserSelect from 'views/Components/UserSelect/UserSelect';
import { usePlanningId } from '..';
import BlockPopupBase, { BlockPopupBaseProps } from '../BlockPopupBase';

type ReassignBlockPopupProps = BlockPopupBaseProps & {
    blockId: string;
};

const Progress = styled(LinearProgress)({
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,

    // ugly hack to prevent loading bar from appearing outside
    // of the popup without adding overflow: hidden to the
    // popup itself (because overflow must be "initial")
    height: 20,
    borderRadius: 100,
    clipPath: 'inset(0 0 16px 0)',
});

export default function ReassignBlockPopup({ blockId, ...props }: ReassignBlockPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const block = useAppSelector((r) => selectBlock(r, blockId));
    const [userId, setUserId] = useState('');
    const selectedUser = useAppSelector((r) => selectUser(r, userId));
    useEffect(() => {
        if (block?.userId && props.open) setUserId(block.userId);
    }, [block?.userId, props.open]);
    const planningId = usePlanningId();
    const planning = useAppSelector((r) => selectPlanning(r, planningId));
    const planningUserIds = planning?.allUsers.map((x) => x.key) ?? [];
    const validationPausedRef = useRef(false); // when set to true does not creat notifications if validation fails

    useEffect(() => {
        // unpause validation if paused when user changes
        validationPausedRef.current = false;
    }, [userId]);

    const { data, isFetching } = useQuery(
        ['wp', 'user-schedule-available', userId, block?.startsAt, block?.duration],
        () => {
            if (!block) throw new Error('block is null');
            const startsAtSeconds = Math.floor(Date.now() / 1000);
            const endsAtSeconds = Math.floor(block.startsAt / 1000) + block.duration * 60;
            const duration = Math.floor((endsAtSeconds - startsAtSeconds) / 60);
            return WpApi.isAvailable(userId, startsAtSeconds, duration);
        },
        {
            enabled: !!block && userId !== block.userId && props.open,
            staleTime: 0,
            cacheTime: 0,
            refetchInterval: 40000,
            onSuccess: (response) => {
                if (validationPausedRef.current) return;
                if (!response.available) {
                    if (response.reason === 'OverlapAbsence') {
                        toasters.danger(
                            t('absences.blockOverlapAbsenceErrorBody'),
                            t('absences.blockOverlapAbsenceErrorTitle')
                        );
                    } else {
                        toasters.danger(
                            t('workshopPlanner.reassignBlock.teamMemberBusyNotif.text'),
                            t('workshopPlanner.reassignBlock.teamMemberBusyNotif.title')
                        );
                    }
                }
            },
        }
    );
    const canBeSaved =
        data?.available === true && !!block && userId !== block.userId && !isFetching;

    const reassignBlockMutation = useMutation(
        async () => {
            const result = await dispatch(reassignBlockThunk({ id: blockId, userId }));
            if (result.meta.requestStatus === 'rejected') throw result.payload;
        },
        {
            onSuccess: () => {
                // pause validation to avoid an error notification right after
                // state is successfully updated
                validationPausedRef.current = true;
                toasters.success(
                    t('workshopPlanner.reassignBlock.successNotif.text', {
                        user: selectedUser?.name ?? 'UNKNOWN',
                    }),
                    t('workshopPlanner.reassignBlock.successNotif.title')
                );
                props.onClose();
            },
            onError: (err) => {
                if (isErrorResponse(err)) {
                    if (hasCode(err, 'General.WP.BlockOverlapAbsence')) {
                        toasters.danger(
                            t('absences.blockOverlapAbsenceErrorBody'),
                            t('absences.blockOverlapAbsenceErrorTitle')
                        );
                    } else {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                } else {
                    toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                }
            },
        }
    );

    return (
        <BlockPopupBase {...props}>
            {isFetching && <Progress />}
            <InputWrapper label={t('workshopPlanner.reassignBlock.reassignTo')}>
                <Box minWidth={260} display="flex" paddingBottom={1}>
                    <UserSelect
                        isInvalid={!data?.available && !isFetching && userId !== block?.userId}
                        onChange={setUserId}
                        userId={userId}
                        userIds={planningUserIds}
                    />
                </Box>
            </InputWrapper>
            <Box display="flex" alignItems="center" justifyContent="center" gap={0.5}>
                <Button
                    w={120}
                    cmosSize={'small'}
                    onClick={props.onClose}
                    disabled={reassignBlockMutation.isLoading}
                    label={t('commonLabels.cancel')}
                    color={Colors.Neutral3}
                />
                <Button
                    w={120}
                    cmosSize={'small'}
                    showLoader={reassignBlockMutation.isLoading}
                    onClick={() => reassignBlockMutation.mutate()}
                    disabled={!canBeSaved || reassignBlockMutation.isLoading}
                    label={t('commonLabels.save')}
                />
            </Box>
        </BlockPopupBase>
    );
}
