import { FullscreenExit } from '@mui/icons-material';
import { Box, styled, Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { PUBNUB_CHANNELS } from 'api/pubnub';
import WpApi, { WpPhasesApi } from 'api/workshopPlanner';
import { OrderUpdatedEvent } from 'api/workshopPlanner/orders';
import { PREDEFINED_PLANNINGS } from 'api/workshopPlanner/plannings';
import { OrderJobsInProgressCheckProvider } from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { PhaseSetbackCheckProvider } from 'common/components/ChangePhaseProviders/PhaseSetbackCheck';
import ArrowTooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { capitalizeFirstLetter } from 'common/Helpers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useForceRender from 'common/hooks/useForceRender';
import useURLSearchParams from 'common/hooks/useURLSearchParams';
import debounce from 'lodash/debounce';
import { DateTime, Duration } from 'luxon';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { useStore } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectWpOrder, wpOrdersActions } from 'store/slices/wp/orders';
import { loadPlanningsThunk, selectPlanning, selectPlannings } from 'store/slices/wp/plannings';
import { useWpShopSchedule } from 'store/slices/wp/plannings/hooks';
import { fetchShopWpSettingsThunk } from 'store/slices/wp/settings';
import SimpleErrorDisplay from 'utils/errorsHandling/SimpleErrorDisplay';
import { usePubnubListener } from 'utils/pubnub';
import { EditAbsencePopupProvider } from 'views/absence/EditAbsence/context';
import { ScheduleAbsencePopupProvider } from 'views/absence/ScheduleAbsence/context';
import { useHeaderLoading } from 'views/HeaderBar';
import { CustomHeaderButtons } from 'views/HeaderBar/HeaderButtons';
import WorkshopPlannerAdvisors, { WorkshopPlannerAdvisorsProps } from '../Advisors';
import { orderBlockClasses } from '../common/OrderBlock';
import { useScheduleFetcher, useWorkshopPlannerAppointmentsFetcher } from '../helpers';
import Planning from '../Planning';
import { ScheduleAppointmentPopupAltProvider } from '../schedule-popups/ScheduleAppointmentWorkAltPopup/context';
import { ScheduleAppointmentPopupProvider } from '../schedule-popups/ScheduleAppointmentWorkPopup/context';
import { ScheduleOrderPopupAltProvider } from '../schedule-popups/ScheduleOrderWorkAltPopup/context';
import { ScheduleOrderPopupProvider } from '../schedule-popups/ScheduleOrderWorkPopup/context';
import { TechnicianCapacityAlertProvider } from '../TechnicianCapacity/TechnicianCapacityAlert';

export default function SingleView() {
    // CMOS-2632: temporary solution - use current date for now
    const dateString = useCurrentDate();
    const isReady = useSingleViewDataFetcher(dateString);
    const planningId = usePlanningId();
    useOrdersListener();

    if (planningId === undefined) {
        return (
            <DivErrorLayout>
                <SimpleErrorDisplay ctx={{ error: 'Invalid planning ID provided' }} />
            </DivErrorLayout>
        );
    }

    return <SingleViewInternal dateString={dateString} isReady={isReady} planningId={planningId} />;
}

export function TechniciansSingleView() {
    useOrdersListener();
    return (
        <PredefinedPlanningSingleView predefinedPlanningName={PREDEFINED_PLANNINGS.TECHNICIANS} />
    );
}

export function AdvisorsSingleView() {
    return <PredefinedPlanningSingleView predefinedPlanningName={PREDEFINED_PLANNINGS.ADVISORS} />;
}

function PredefinedPlanningSingleView({
    predefinedPlanningName,
}: {
    predefinedPlanningName: string;
}) {
    // CMOS-2632: temporary solution - use current date for now
    const dateString = useCurrentDate();
    const isReady = useSingleViewDataFetcher(dateString);
    const { list: plannings } = useAppSelector(selectPlannings);

    if (!isReady) {
        return null;
    }

    const planning = plannings.find((x) => x.readonly && x.name === predefinedPlanningName);

    if (planning) {
        return (
            <SingleViewInternal
                dateString={dateString}
                isReady={isReady}
                planningId={planning.id}
            />
        );
    }

    return (
        <DivErrorLayout>
            <SimpleErrorDisplay ctx={{ error: 'Could not find' }} />
        </DivErrorLayout>
    );
}

const getContentAreaHeight = () => {
    const css = window.getComputedStyle(document.body, null);
    const headerHeight = parseFloat(css.getPropertyValue('--header-height'));

    return window.innerHeight - (Number.isNaN(headerHeight) ? 52 : headerHeight);
};
const getContentAreaWidth = () => window.innerWidth;

function SingleViewInternal({
    planningId,
    isReady,
    dateString,
}: {
    planningId: number;
    isReady: boolean;
    dateString: string;
}) {
    const dispatch = useAppDispatch();
    const navigate = useNavigate();
    const { t } = useAppTranslation();
    const { data } = useQuery(['wp', 'display-parameters'], {
        queryFn: () => WpApi.getDisplayParameters(),
        staleTime: 1000,
        cacheTime: Infinity,
    });
    const showUnassignedRow = data?.showUnassignedRow ?? false;

    useHeaderLoading(!isReady);

    const planning = useAppSelector((r) => selectPlanning(r, planningId));
    const planningName = planning?.isAdvisors
        ? t('workshopPlanner.advisors')
        : planning?.isTechnicians
        ? t('workshopPlanner.technicians.title')
        : planning?.name ?? 'unknown';

    const dateFormattedString = capitalizeFirstLetter(
        DateTime.fromFormat(dateString, 'yyyy-MM-dd').toFormat(
            t('dateFormats.longDateWithDayLuxon')
        )
    );
    const customInterval = useShopActiveHours(dateString);
    const isCustomIntervalInvalid = customInterval.to < customInterval.from;
    useDocumentTitle(`${t('titles.workshopPlanner')} – ${planningName} / ${dateFormattedString}`);

    const airportScreenView =
        useAppSelector(selectSettings).repairShopSettings?.features.airportScreenView ?? 'Vertical';

    const [maxHeight, setMaxHeight] = useState(getContentAreaHeight());
    const [availableWidth, setAvailableWidth] = useState(getContentAreaWidth());

    useEffect(() => {
        dispatch(fetchShopWpSettingsThunk());

        // NOTE (MB) this will not trigger if header height changes suddenly
        const callback = debounce(() => {
            setMaxHeight(getContentAreaHeight());
            setAvailableWidth(getContentAreaWidth());
        }, 150);
        window.addEventListener('resize', callback);

        return () => {
            window.removeEventListener('resize', callback);
        };
    }, [dispatch]);

    const customHeaderButtons = (
        <CustomHeaderButtons>
            <DivCloseArea>
                <ArrowTooltip
                    position="left"
                    slotProps={{
                        popper: {
                            modifiers: [
                                {
                                    name: 'offset',
                                    options: {
                                        offset: [0, -26],
                                    },
                                },
                            ],
                        },
                    }}
                    content={t('commonLabels.stopProjecting')}
                >
                    <CloseButton
                        to={ROUTES.WORKSHOP_PLANNER}
                        onClick={() => {
                            navigate(ROUTES.WORKSHOP_PLANNER);
                        }}
                    >
                        <FullscreenExit sx={{ height: '34px', width: '34px' }} />
                    </CloseButton>
                </ArrowTooltip>
            </DivCloseArea>
        </CustomHeaderButtons>
    );

    const wrap = (element: React.ReactElement) => (
        <>
            {customHeaderButtons}
            <CustomPageLayout>
                <TechnicianCapacityAlertProvider>
                    <ScheduleAppointmentPopupAltProvider>
                        <ScheduleAppointmentPopupProvider>
                            <ScheduleOrderPopupAltProvider>
                                <ScheduleOrderPopupProvider>
                                    <ScheduleAbsencePopupProvider>
                                        <EditAbsencePopupProvider>
                                            <PhaseSetbackCheckProvider>
                                                <OrderJobsInProgressCheckProvider>
                                                    {element}
                                                </OrderJobsInProgressCheckProvider>
                                            </PhaseSetbackCheckProvider>
                                        </EditAbsencePopupProvider>
                                    </ScheduleAbsencePopupProvider>
                                </ScheduleOrderPopupProvider>
                            </ScheduleOrderPopupAltProvider>
                        </ScheduleAppointmentPopupProvider>
                    </ScheduleAppointmentPopupAltProvider>
                </TechnicianCapacityAlertProvider>
            </CustomPageLayout>
        </>
    );

    if (isCustomIntervalInvalid) {
        return (
            <>
                {customHeaderButtons}
                <InvalidShopScheduleWarning />
            </>
        );
    }

    if (planning?.isAdvisors) {
        return wrap(
            <WorkshopPlannerAdvisorsInner
                dateString={dateString}
                showUnassignedRow={showUnassignedRow}
                borderless
                autoSizeRowHeight
                hideLegend
                maxHeight={maxHeight}
                pagination={20}
                autoSizeColumnWidth={{ availableWidth: availableWidth }}
                customInterval={customInterval}
                vertical={airportScreenView === 'Vertical'}
                singleView
            />
        );
    }

    if (!planning && isReady) {
        return (
            <DivErrorLayout>
                <SimpleErrorDisplay ctx={{ error: 'Planning not found' }} />
            </DivErrorLayout>
        );
    }

    return wrap(
        <Planning
            disablePlusButton
            showUnassignedRow={showUnassignedRow}
            dateString={dateString}
            maxHeight={maxHeight}
            autoSizeRowHeight
            autoSizeColumnWidth={{ availableWidth: availableWidth }}
            planningId={planningId}
            borderless
            pagination={20}
            intervalMinutes={60}
            customInterval={customInterval}
            vertical={airportScreenView === 'Vertical'}
            singleView
        />
    );
}

function WorkshopPlannerAdvisorsInner(props: WorkshopPlannerAdvisorsProps) {
    useWorkshopPlannerAppointmentsFetcher(props.dateString);

    return <WorkshopPlannerAdvisors {...props} />;
}

function useCurrentDate(): string {
    const fr = useForceRender();

    const ref = useRef('');

    if (ref.current === '') {
        ref.current = DateTime.now().toFormat('yyyy-MM-dd');
    }

    useEffect(() => {
        const interval = setInterval(() => {
            const now = DateTime.now().toFormat('yyyy-MM-dd');
            if (now !== ref.current) {
                ref.current = now;
                fr();
            }
        }, 60000);

        return () => clearInterval(interval);
    }, [fr]);

    return ref.current;
}

function usePlanningId(): number | undefined {
    const params = useURLSearchParams();
    const planningParam = params.get('planning');

    const planningId = +(planningParam ?? '');

    return Number.isNaN(planningId) ? undefined : planningId;
}

function useOrdersListener() {
    const dispatch = useAppDispatch();
    const store = useStore();
    const { uid, appMode } = useAppSelector(selectSettings);

    const { data: phases } = useQuery(['wp', 'phases'], WpPhasesApi.getPhases);

    usePubnubListener<OrderUpdatedEvent>(
        async (_message) => {
            if (phases === undefined) return;

            const order = selectWpOrder(
                store.getState() as RootState,
                _message.message.payload.orderKey
            );

            if (order) {
                const phase = phases.find((p) => p.id === _message.message.payload.phaseId);
                dispatch(wpOrdersActions.update({ id: _message.message.payload.orderKey, phase }));
            }
        },
        {
            channels: [PUBNUB_CHANNELS.orders(uid)],
            types: ['order.updated'],
            listenerEnabled: appMode === 'RepairShop',
        }
    );
}

function useShopActiveHours(date: string): {
    from: DateTime;
    to: DateTime;
    isLoading: boolean;
} {
    const { data } = useWpShopSchedule();

    return useMemo(() => {
        const day = DateTime.fromFormat(date, 'yyyy-MM-dd');

        if (!data) {
            return {
                from: day,
                to: day.plus({ day: 1 }),
                isLoading: true,
            };
        } else {
            const daySchedule = data.find(
                (x) => (x.dayNumber === 0 ? 7 : x.dayNumber) === day.weekday
            );

            if (!daySchedule) {
                // that's a bug
                throw new Error('no day schedule found');
            }

            const start = Duration.fromISOTime(daySchedule.opening);
            const end = Duration.fromISOTime(daySchedule.closing);

            const activeHours = {
                from: day.plus(start),
                to: day.plus(end),
                isLoading: false,
            };

            // temporary fix for CMOS-3249: scheduler component does not support ranges that are not full hours
            // so we have round it up or down to a full hour
            // for example: 08:30-18:30 will become 08:00-19:00
            if (activeHours.from.minute) {
                activeHours.from = activeHours.from.minus({ minutes: activeHours.from.minute });
            }
            if (activeHours.to.minute) {
                activeHours.to = activeHours.to.plus({ minutes: 60 - activeHours.to.minute });
            }

            return activeHours;
        }
    }, [date, data]);
}

const DivCloseArea = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
});

const CloseButton = styled(Link)(({ theme }) => ({
    color: theme.palette.neutral[6],
    borderRadius: 0,
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    height: '100%',
    padding: '0 16px',
    display: 'flex',
    alignItems: 'center',

    ':hover': {
        color: theme.palette.neutral[9],
    },

    ':active': {
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
    },
}));

const CustomPageLayout = styled('div')({
    height: 'calc(100vh - var(--header-height))',
    overflowY: 'hidden',
    backgroundColor: '#fff',

    // custom style for all order block components
    [`& .${orderBlockClasses.root}`]: {
        [`& .${orderBlockClasses.promiseLabel}`]: {
            display: 'none',
        },

        [`& .${orderBlockClasses.promiseValue}`]: {
            fontWeight: 'bold',
        },
    },
});

function useSingleViewDataFetcher(dateString: string) {
    useScheduleFetcher(dateString);
    const [arePlanningsLoaded, setArePlanningsLoaded] = useState(false);

    const dispatch = useAppDispatch();

    useEffect(() => {
        // NOTE (MB) we do not use useQuery here because we have no need to store in the cache or anywhere else
        // we ONLY need isLoading
        dispatch(loadPlanningsThunk()).then(() => {
            setArePlanningsLoaded(true);
        });
    }, [dispatch]);

    return arePlanningsLoaded;
}

const DivErrorLayout = styled('div')({
    padding: '40px',
});

function InvalidShopScheduleWarning() {
    const { t } = useAppTranslation();
    return (
        <Box sx={{ p: 5, fontSize: '1.5rem' }}>
            <Typography component="header" variant="h1Inter" sx={{ fontSize: '1.5em', my: 5 }}>
                {t('workshopPlanner.invalidSchedule.title')}
            </Typography>
            <p>
                <Typography
                    color="neutral.8"
                    variant="body1"
                    sx={{ fontSize: '1em', whiteSpace: 'pre' }}
                >
                    {t('workshopPlanner.invalidSchedule.text')}
                </Typography>
            </p>
            <p>
                <Typography
                    color="neutral.8"
                    variant="body1"
                    sx={{ fontSize: '1em', whiteSpace: 'pre' }}
                >
                    <Trans
                        i18nKey="workshopPlanner.invalidSchedule.settings"
                        components={{
                            1: (
                                <SettingsLink
                                    to={ROUTES.SETTINGS.GENERAL.PATH.replace(
                                        ':section',
                                        'appointments'
                                    )}
                                />
                            ),
                        }}
                    />
                </Typography>
            </p>
            <p>
                <Typography
                    color="neutral.8"
                    variant="body1"
                    sx={{ fontSize: '1em', whiteSpace: 'pre' }}
                >
                    {t('workshopPlanner.invalidSchedule.bugReport')}
                </Typography>
            </p>
        </Box>
    );
}

const SettingsLink = styled(Link)({
    fontWeight: 'bold',
    color: 'var(--cm2)',
    textDecoration: 'none',

    ':hover': {
        color: 'var(--cm1)',
        textDecoration: 'underline',
    },
});
