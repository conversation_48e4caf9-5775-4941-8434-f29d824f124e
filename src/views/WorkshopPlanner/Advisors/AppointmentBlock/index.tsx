import { styled } from '@mui/material';
import { AppointmentNoteDto } from 'api/appointments';
import clsx from 'clsx';
import { PlannedAppointmentIcon } from 'common/components/Icons/PlannedAppointmentIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectWpAppointment, types } from 'store/slices/wp/plannings';
import {
    selectFilteredAppointmentIds,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import AppointmentDetailsTooltip from 'views/WorkshopPlanner/common/AppointmentDetailsTooltip';
import {
    useIsBlockSelected,
    useSelectedBlockAttributesSetter,
} from 'views/WorkshopPlanner/common/selectedBlockContext';
import { StyledTooltip } from '../../common/TooltipComponents';

type AppointmentBlockProps = {
    appointment: types.AppointmentBlock;
    onClick?: React.MouseEventHandler;
    availableVerticalSpace?: number;
    outerContainer?: HTMLElement | null;
    className?: string;
};

type AppointmentBlockBreakpoint = 'md' | 'sm';

function getAppointmentBlockBreakpoint(height: number): AppointmentBlockBreakpoint {
    if (height < 40) {
        return 'sm';
    }

    return 'md';
}

const AppointmentBlock = ({
    appointment,
    onClick,
    availableVerticalSpace = Infinity,
    outerContainer,
    className,
}: AppointmentBlockProps) => {
    const { t } = useAppTranslation();
    const {
        id,
        number,
        customerFirstName,
        customerLastName,
        vehicleModel,
        vehiclePlates,
        startDate,
        status,
        orderNumber,
        orderId,
        userServiceDisplayName,
        isPlanned,
    } = appointment;

    let statusText: string = '';
    let color: string = '#FFFFFF';
    switch (status) {
        case AppointmentStatusEnum.OrderCreated:
            statusText = `${t('workshopPlanner.ORDER')} #${orderNumber}`;
            color = '#36CE91';
            break;
        case AppointmentStatusEnum.Confirmed:
            statusText = t('workshopPlanner.APPOINTMENT_CONFIRMED');
            color = '#36CE91';
            break;
        case AppointmentStatusEnum.Unconfirmed:
            statusText = t('workshopPlanner.APPOINTMENT_UNCONFIRMED');
            break;
        case AppointmentStatusEnum.CustomerDidNotArrive:
            statusText = t('workshopPlanner.DID_NOT_ARRIVE');
            color = '#F15857';
            break;
        default:
            statusText = '';
            break;
    }

    const [tooltipIsOpened, setTooltipIsOpened] = useState(false);
    const [tooltipPosition, setTooltipPosition] = useState<'top' | 'bottom' | 'right' | 'left'>(
        'top'
    );
    const blockRef = useRef<HTMLDivElement | null>(null);
    const isBlockSelected = useIsBlockSelected(
        (state) =>
            state.appointmentNumber === appointment.number ||
            (!!state.orderNumber && state.orderNumber === appointment.orderNumber)
    );
    const formattedDate = useMemo(() => moment(startDate).format('hh:mm a'), [startDate]);
    const wpAppointment = useAppSelector((r) => selectWpAppointment(r, id));
    const breakpoint = getAppointmentBlockBreakpoint(availableVerticalSpace);

    const filteredAppointmentIds = useAppSelector(selectFilteredAppointmentIds);
    const filteredOrderIds = useAppSelector(selectFilteredOrderIds);

    const boundaryNumberOfDigits = 5;
    const maximumNumberOfDigits =
        useAppSelector(selectSettings).repairShopSettings?.features
            .maximumNumberOfDigitsToDisplayForOrderAndAppointment ?? boundaryNumberOfDigits;
    const numberModified = number.slice(-maximumNumberOfDigits);

    const setSelectedBlock = useSelectedBlockAttributesSetter();

    const onOpenTooltip = useCallback(() => {
        setSelectedBlock({
            orderNumber: orderNumber && orderNumber.length > 0 ? orderNumber : null,
            appointmentNumber: number,
        });
        setTooltipIsOpened(true);
    }, [number, orderNumber, setSelectedBlock]);

    const onCloseTooltip = useCallback(() => {
        setSelectedBlock(null);
        setTooltipIsOpened(false);
    }, [setSelectedBlock]);

    const updateTooltipPosition = useCallback(() => {
        if (!outerContainer || !blockRef.current) {
            return;
        }

        const outerRect = outerContainer.getBoundingClientRect();
        const innerRect = blockRef.current.getBoundingClientRect();

        let tooltipRowsCount = 6;
        if (wpAppointment) {
            tooltipRowsCount +=
                wpAppointment.reasons.length > 0 ? 1 + wpAppointment.reasons.length : 0;
            tooltipRowsCount += wpAppointment.notes.length > 0 ? 1 + wpAppointment.notes.length : 0;
        }

        const pixelsPerRow = 24;
        const tooltipHeight = tooltipRowsCount * pixelsPerRow;

        if (innerRect.top > tooltipHeight && innerRect.top - outerRect.top - 40 > tooltipHeight) {
            setTooltipPosition('top');
        } else if (document.documentElement.clientHeight - innerRect.bottom > tooltipHeight) {
            setTooltipPosition('bottom');
        } else if (document.documentElement.clientWidth - innerRect.right > innerRect.left) {
            setTooltipPosition('right');
        } else {
            setTooltipPosition('left');
        }
    }, [outerContainer, blockRef, wpAppointment]);

    useEffect(() => {
        if (tooltipIsOpened && outerContainer && blockRef.current) {
            updateTooltipPosition();
        }
    }, [updateTooltipPosition, outerContainer, blockRef, tooltipIsOpened]);

    return (
        <StyledTooltip
            placement={tooltipPosition}
            arrow
            title={
                <AppointmentDetailsTooltip
                    appointmentNumber={number}
                    customerFirstName={customerFirstName ?? ''}
                    customerLastName={customerLastName ?? ''}
                    model={vehicleModel ?? ''}
                    plates={vehiclePlates ?? ''}
                    status={statusText}
                    duration={null}
                    appointmentTime={formattedDate}
                    serviceAdvisorName={userServiceDisplayName}
                    appointmentReasons={wpAppointment ? wpAppointment.reasons : []}
                    notes={
                        wpAppointment
                            ? wpAppointment.notes.map(
                                  (x): AppointmentNoteDto => ({
                                      note: x.note,
                                      type: x.type,
                                  })
                              )
                            : []
                    }
                    jobDescriptions={
                        wpAppointment?.blockJobDescriptions
                            ? wpAppointment.blockJobDescriptions.flatMap(
                                  (x) => x?.jobDescriptions ?? []
                              )
                            : []
                    }
                />
            }
            onOpen={() => {
                onOpenTooltip();
            }}
            onClose={() => {
                onCloseTooltip();
            }}
            open={tooltipIsOpened}
        >
            <DivAppointmentContainer
                ref={blockRef}
                style={{}}
                className={clsx(
                    className,
                    !tooltipIsOpened && isBlockSelected && 'AppointmentBlock-highlighted',
                    (filteredAppointmentIds.includes(id) ||
                        (orderId && filteredOrderIds.includes(orderId))) &&
                        'AppointmentBlock-filter-match'
                )}
            >
                <DivAppointment
                    style={{
                        background: tooltipIsOpened
                            ? '#E6EEFE'
                            : status === AppointmentStatusEnum.CustomerDidNotArrive
                            ? Colors.Neutral3
                            : Colors.White,
                    }}
                    onClick={onClick}
                    className={`AppointmentBlock-${breakpoint}`}
                >
                    <DivLine
                        style={{
                            background: color,
                            height: breakpoint === 'sm' ? 2 : 4,
                        }}
                    />
                    <DivAppointmentContent>
                        <DivCustomerName
                            sx={{
                                textDecorationLine:
                                    status === AppointmentStatusEnum.CustomerDidNotArrive
                                        ? 'line-through'
                                        : 'none',
                            }}
                        >
                            <SpanAppointmentNumber>{numberModified}</SpanAppointmentNumber>
                            <SpanCustomerName>
                                &nbsp;-&nbsp;{customerLastName}&nbsp;{customerFirstName}
                            </SpanCustomerName>
                        </DivCustomerName>
                        <DivVehicleInfo sx={{ marginTop: breakpoint === 'sm' ? undefined : '5px' }}>
                            {vehicleModel || '--'} - {vehiclePlates || '--'}
                        </DivVehicleInfo>
                        {status !== AppointmentStatusEnum.CustomerDidNotArrive && (
                            <DivVehicleInfo>
                                {t('workshopPlanner.appointment')}: {formattedDate}
                            </DivVehicleInfo>
                        )}
                        <DivRow>
                            {isPlanned ? (
                                <DivIconWrapper>
                                    <PlannedAppointmentIcon size={20} />
                                </DivIconWrapper>
                            ) : undefined}
                            <DivStatus>{statusText}</DivStatus>
                        </DivRow>
                    </DivAppointmentContent>
                </DivAppointment>
            </DivAppointmentContainer>
        </StyledTooltip>
    );
};

export default AppointmentBlock;

const DivRow = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const DivAppointment = styled('div')(({ theme }) => ({
    overflow: 'hidden',
    width: '100%',
    background: theme.palette.neutral[1],
    borderRadius: 5,
    height: '100%',

    '&.AppointmentBlock-sm': {
        fontSize: '9px',
    },
}));

const DivAppointmentContent = styled('div')({
    width: '100%',
    padding: '2px 5px',
    boxSizing: 'border-box',
    lineHeight: '15px',
});

const DivAppointmentContainer = styled('div')(({ theme }) => ({
    overflow: 'hidden',
    width: '100%',
    background: theme.palette.neutral[1],
    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: 5,
    height: '96%',
    transition: '0.17s box-shadow',

    '&.AppointmentBlock-highlighted': {
        boxShadow: `5px 5px 10px 5px #AEC5FE`,
    },

    '&.AppointmentBlock-filter-match': {
        outline: `3px solid ${theme.palette.primary.main}`,
        outlineOffset: '-3px',
    },
}));

const DivLine = styled('div')({
    width: '100%',
    height: 4,
});

const DivCustomerName = styled('div')(({ theme }) => ({
    display: 'flex',
    fontSize: '1em',
    color: theme.palette.neutral[8],
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    userSelect: 'none',
}));

const SpanAppointmentNumber = styled('span')({
    fontWeight: 'bold',
    maxWidth: 'min(100px, calc(100% - 10px))',
    textDecoration: 'inherit',
    direction: 'rtl',
    textAlign: 'left',
    flexShrink: 0,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',

    '::after': {
        content: "'#'",
        display: 'inline-block',
    },
});

const SpanCustomerName = styled('span')({
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    flexGrow: 1,
    textAlign: 'left',
});

const DivVehicleInfo = styled('div')(({ theme }) => ({
    fontWeight: 'normal',
    color: theme.palette.neutral[8],
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
}));

const DivStatus = styled('div')(({ theme }) => ({
    fontWeight: 'bold',
    color: theme.palette.neutral[8],
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
}));

const DivIconWrapper = styled('div')({
    marginLeft: -3,
    marginTop: -3,
    marginBottom: -6,
    marginRight: -1,
});
