import { styled } from '@mui/material';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import theme from 'theme';

const DivHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[9],
    fontWeight: 'normal',
    display: 'flex',
    alignItems: 'center',
    background: theme.palette.neutral[1],
    height: '100%',
}));

const StyledClockIcon = styled(ClockIcon)(({ theme }) => ({
    color: theme.palette.error.main,
    marginRight: 2,
}));

export type AdvisorsStickyPartHeaderProps = {
    vertical: boolean;
};

export default function AdvisorsStickyPartHeader({ vertical }: AdvisorsStickyPartHeaderProps) {
    const { t } = useAppTranslation();

    return (
        <DivHeader
            style={
                vertical
                    ? {
                          justifyContent: 'center',
                          borderLeft: `3px solid ${theme.palette.custom.gray}`,
                      }
                    : {
                          paddingLeft: 11,
                          borderTop: `3px solid ${theme.palette.custom.gray}`,
                      }
            }
        >
            <StyledClockIcon size={18} />
            {t('workshopPlanner.didNotArrive.title')}
        </DivHeader>
    );
}
