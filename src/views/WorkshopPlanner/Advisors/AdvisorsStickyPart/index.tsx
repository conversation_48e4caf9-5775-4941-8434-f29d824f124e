import { styled } from '@mui/material';
import Cursor from 'common/components/Scheduler/GridCanvasCells/Cursor';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useMemo } from 'react';
import { types } from 'store/slices/wp/plannings';
import theme from 'theme';
import { useConvertAppointmentModalOptional } from 'views/WorkshopPlanner/ConvertOrderModal/context';
import FakeGrid from 'views/WorkshopPlanner/common/FakeGrid';
import AppointmentBlock from '../AppointmentBlock';
import AdvisorsStickyPartHeader from './AdvisorsStickyPartHeader';

export { AdvisorsStickyPartHeader };

export type AdvisorsStickyPartProps = {
    appointments: types.AppointmentBlock[];
    vertical: boolean;
};

export default function AdvisorsStickyPart({ appointments, vertical }: AdvisorsStickyPartProps) {
    const ctx = useSchedulerContext();
    const fromTimestamp = +moment(ctx.interval.from).toDate();

    const groupedAppointments = useMemo(() => {
        const groups: types.AppointmentBlock[][] = [];

        const sortedAppointments = appointments.toSorted(
            (a, b) =>
                DateTime.fromISO(a.startDate).toMillis() - DateTime.fromISO(b.startDate).toMillis()
        );

        for (let i = 0; i < sortedAppointments.length; i++) {
            const a = sortedAppointments[i];
            if (groups.length === 0) {
                groups.push([a]);
            } else {
                const lastGroup = groups[groups.length - 1];
                const lastBlock = lastGroup[lastGroup.length - 1];

                if (DateTime.fromISO(lastBlock.endDate) > DateTime.fromISO(a.startDate)) {
                    lastGroup.push(a);
                } else {
                    groups.push([a]);
                }
            }
        }

        return groups.map((g) => ({
            appointments: g,
            start: g[0].startDate,
            end: g[g.length - 1].endDate,
        }));
    }, [appointments]);

    const convertAppointmentModal = useConvertAppointmentModalOptional();

    return (
        <DivRoot
            style={
                vertical
                    ? {
                          borderLeft: `3px solid ${theme.palette.custom.gray}`,
                      }
                    : {
                          borderTop: `3px solid ${theme.palette.custom.gray}`,
                      }
            }
        >
            <FakeGrid vertical={vertical} />
            <Cursor vertical={vertical} />

            {groupedAppointments.map((group, i) => {
                const key = `${i}_${group.start}_${group.end}`;
                const groupStartTimestamp = DateTime.fromISO(group.start).toMillis();
                const groupEndTimestamp = DateTime.fromISO(group.end).toMillis();

                const timelineCoord =
                    ((groupStartTimestamp - fromTimestamp) / 60000) *
                    ctx.dimensions.pixelsPerMinuteRatio;
                const size =
                    ((groupEndTimestamp - groupStartTimestamp) / 60000) *
                    ctx.dimensions.pixelsPerMinuteRatio;

                let lastItemPosition = -1;

                return (
                    <DivItem
                        style={{
                            ...(vertical
                                ? {
                                      top: timelineCoord,
                                      left: 0,
                                      height: size,
                                      width: '100%',
                                  }
                                : {
                                      top: 0,
                                      left: timelineCoord,
                                      height: '100%',
                                      width: size,
                                  }),
                        }}
                        key={key}
                    >
                        {group.appointments.map((a) => {
                            const startTimestamp = DateTime.fromISO(a.startDate).toMillis();
                            const endTimestamp = DateTime.fromISO(a.endDate).toMillis();

                            let timelineCoord =
                                ((startTimestamp - groupStartTimestamp) / 60000) *
                                ctx.dimensions.pixelsPerMinuteRatio;
                            let size =
                                ((endTimestamp - startTimestamp) / 60000) *
                                ctx.dimensions.pixelsPerMinuteRatio;

                            const minVisibleSpace = 40;

                            if (
                                lastItemPosition !== -1 &&
                                timelineCoord - lastItemPosition < minVisibleSpace
                            ) {
                                const diff = minVisibleSpace - (timelineCoord - lastItemPosition);
                                timelineCoord += diff;
                                size = Math.max(size - diff, minVisibleSpace);
                            }

                            lastItemPosition = timelineCoord;

                            return (
                                <DivItem
                                    style={{
                                        ...(vertical
                                            ? {
                                                  top: timelineCoord,
                                                  left: 0,
                                                  height: size,
                                                  width: '100%',
                                              }
                                            : {
                                                  top: 0,
                                                  left: timelineCoord,
                                                  height: '100%',
                                                  width: size,
                                              }),
                                    }}
                                >
                                    <StyledAppointmentBlock
                                        onClick={() => convertAppointmentModal?.open(a.id)}
                                        appointment={a}
                                        outerContainer={null}
                                    />
                                </DivItem>
                            );
                        })}
                    </DivItem>
                );
            })}
        </DivRoot>
    );
}

const StyledAppointmentBlock = styled(AppointmentBlock)({
    cursor: 'pointer',
});

const DivRoot = styled('div')({
    position: 'relative',
    height: '100%',
});

const DivItem = styled('div')({
    position: 'absolute',
});
