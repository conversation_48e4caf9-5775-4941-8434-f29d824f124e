import SchedulerWithGroups, { schedulerLayoutClasses } from 'common/components/Scheduler';
import moment from 'moment';
import { useCallback, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';

import { Box, Pagination, paginationClasses, styled } from '@mui/material';
import { UserScheduleDto } from 'api/common';
import { TimeSpan } from 'api/utils/format';
import { SchedulerScrollbarControllerProvider } from 'common/components/Scheduler/GridCanvas/scrollbarController';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import { DateTime } from 'luxon';
import Skeleton from 'react-loading-skeleton';
import { selectAdvisorsPlanning, selectAppointmentsBlocks, types } from 'store/slices/wp/plannings';
import {
    selectFilteredAppointmentIds,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import { useRepairShopAppointmentSettings } from 'views/Appointments/common';
import { useConvertAppointmentModalOptional } from '../ConvertOrderModal/context';
import { PLANNING_PAGINATION_HEIGHT, PlanningAutoScroll, PlanningSkeleton } from '../Planning';
import PlanningHeader from '../Planning/PlanningHeader';
import {
    UserLikeObject,
    UserScheduleDecorationComponent,
    WorkshopPlannerCurrentDayContext,
} from '../helpers';
import Absences from './Absences';
import AdvisorsStickyPart, { AdvisorsStickyPartHeader } from './AdvisorsStickyPart';
import AppointmentBlock from './AppointmentBlock';
import ServiceAdvisorHeader from './ServiceAdvisorHeader';

function isAbsent(schedule: UserScheduleDto, utcOffsetInMinutes: number) {
    const today = moment.utc().utcOffset(utcOffsetInMinutes);
    const timeInSeconds = today.hour() * 3600 + today.minute() * 60;
    const daySchedule = [
        schedule.sunday,
        schedule.monday,
        schedule.tuesday,
        schedule.wednesday,
        schedule.thursday,
        schedule.friday,
        schedule.saturday,
    ][today.day()];
    return !daySchedule.windows.some((w) => {
        return (
            timeInSeconds >= TimeSpan.fromString(w.from).totalSeconds &&
            timeInSeconds <= TimeSpan.fromString(w.to).totalSeconds
        );
    });
}

export function AdvisorsSkeleton() {
    return (
        <div>
            <Box
                display="flex"
                width="100%"
                flexDirection="column"
                marginBottom="7px"
                alignItems="end"
            >
                <Skeleton height={15} width={400} />
            </Box>
            <PlanningSkeleton />
        </div>
    );
}

const getItemKey = (u: types.AppointmentBlock) => u.id;
const getGroupKey = (u: AdvisorData) => u.id;
const getItemGroupKey = (a: types.AppointmentBlock) => a.userServiceAdvisorId;
const getItemMinutesDuration = (a: types.AppointmentBlock) =>
    moment(a.endDate).diff(a.startDate, 'm');
const getItemStatic = (_a: types.AppointmentBlock) => false;
const getItemTimestamp = (a: types.AppointmentBlock) => DateTime.fromISO(a.startDate).toMillis();

type AdvisorData = UserLikeObject & {
    color: string;
    id: string;
};

const renderGroup = (model: AdvisorData, vertical: boolean) => {
    return (
        <ServiceAdvisorHeader text={model.displayName} color={model.color} vertical={vertical} />
    );
};

export type WorkshopPlannerAdvisorsProps = {
    /**
     * Selected date in the format "yyyy-MM-dd"
     */
    dateString: string;
    borderless?: boolean;
    maxHeight?: number;
    autoSizeRowHeight?: boolean;
    hideLegend?: boolean;
    pagination?: number;

    /**
     * If set to true and maxHeight is set, the rows will be auto sized
     */
    autoSizeColumnWidth?: {
        availableWidth: number;
    };

    customInterval?: {
        from: DateTime;
        to: DateTime;
    };

    showUnassignedRow?: boolean;
    vertical?: boolean;
    singleView?: boolean;
};

const WorkshopPlannerAdvisors = ({
    dateString: selectedDate,
    borderless = false,
    maxHeight,
    autoSizeRowHeight = false,
    hideLegend = false,
    pagination,
    customInterval,
    showUnassignedRow = false,
    autoSizeColumnWidth,
    vertical = false,
    singleView = false,
}: WorkshopPlannerAdvisorsProps) => {
    const { t } = useAppTranslation();
    const { selectedAdvisors, planning } = useAppSelector(selectAdvisorsPlanning);
    const appointmentSettings = useRepairShopAppointmentSettings();
    const intervalMinutes = appointmentSettings?.duration ?? 30;
    const startOfDay = useMemo(
        () =>
            DateTime.fromFormat(selectedDate, 'yyyy-MM-dd').set({
                millisecond: 0,
                second: 0,
                minute: 0,
                hour: 0,
            }),
        [selectedDate]
    );

    const defaultInterval = {
        from: startOfDay,
        to: startOfDay.plus({ day: 1 }),
    };

    const { from, to } = customInterval ?? defaultInterval;

    const [schedulerRef, setSchedulerRef] = useState<HTMLDivElement | null>(null);

    const allAppointments = useAppSelector(selectAppointmentsBlocks);
    const [didNotArriveAppointment, appointments] = useMemo(() => {
        if (!showUnassignedRow) {
            return [[], allAppointments];
        }

        const didNotArrive: types.AppointmentBlock[] = [];
        const other: types.AppointmentBlock[] = [];

        for (const appointment of allAppointments) {
            if (appointment.status === AppointmentStatusEnum.CustomerDidNotArrive) {
                didNotArrive.push(appointment);
            } else {
                other.push(appointment);
            }
        }

        return [didNotArrive, other];
    }, [allAppointments, showUnassignedRow]);

    const timeZoneOffset = useAppSelector(selectSettings).internationalization.timeZoneOffset;

    const [page, setPage] = useState(1);
    const { advisorsData, pagesCount, displayedAdvisorIds } = useMemo(() => {
        const pageSize = pagination === undefined ? undefined : Math.max(1, pagination);

        let list: AdvisorData[] = selectedAdvisors
            .map(({ schedule, ...a }) => ({
                ...a,
                schedule,
            }))
            .map((a) => ({
                id: a.key,
                color: a.color ?? '#fff',
                displayName: a.name,
                absent: isAbsent(a.schedule, timeZoneOffset),
                schedule: a.schedule,
            }));

        const totalCount = list.length;

        if (pageSize !== undefined) {
            list = list.slice((page - 1) * pageSize, page * pageSize);
        }

        const pagesCount = pageSize === undefined ? 1 : Math.ceil(totalCount / pageSize);
        return {
            advisorsData: list,
            displayedAdvisorIds: list.map((x) => x.id),
            pagesCount,
        };
    }, [selectedAdvisors, page, pagination, timeZoneOffset]);
    const filteredAppointments = useMemo(
        () =>
            appointments?.filter((x) => advisorsData.some((y) => y.id === x.userServiceAdvisorId)),
        [advisorsData, appointments]
    );

    const filteredAppointmentIds = useAppSelector(selectFilteredAppointmentIds);
    const filteredOrderIds = useAppSelector(selectFilteredOrderIds);

    const firstFilteredBlock = useMemo(() => {
        if (filteredAppointmentIds.length === 0 && filteredOrderIds.length === 0) return null;

        const firstFilteredBlock = allAppointments.find(
            (x) =>
                filteredAppointmentIds.includes(x.id) ||
                (x.orderId && filteredOrderIds.includes(x.orderId))
        );

        return firstFilteredBlock
            ? {
                  time: DateTime.fromISO(firstFilteredBlock.startDate).toMillis(),
                  userKey: firstFilteredBlock.userServiceAdvisorId,
              }
            : null;
    }, [filteredAppointmentIds, filteredOrderIds, allAppointments]);

    const currentDay = useMemo(() => DateTime.fromISO(selectedDate).toJSDate(), [selectedDate]);
    const SchedulerComponent: typeof SchedulerWithGroups = borderless
        ? BorderlessSchedulerWithGroups
        : SchedulerWithGroups;

    if (pagination && pagesCount > 1 && maxHeight) {
        maxHeight -= PLANNING_PAGINATION_HEIGHT;
    }

    // calculation various sizes
    const headerHeight = vertical ? 65 : 40;
    const sideWidth = vertical ? 90 : 130;
    const defaultRowHeight = 100;
    const defaultColumnWidth = 120;

    const contentAreaHeight = maxHeight
        ? Math.max(maxHeight - headerHeight, defaultRowHeight)
        : undefined;
    const contentAreaWidth = autoSizeColumnWidth
        ? Math.max(autoSizeColumnWidth.availableWidth - sideWidth, defaultColumnWidth)
        : undefined;

    // NOTE (MB) we could just use ResizeObserver inside Scheduler which will make "auto-size columns" a built-in feature
    // but that would make Schedule even more complicated
    // if in the future we have a greater need for auto-sizing rows/columns we can do that, for now though - this will do
    const stepsCount = Math.ceil(to.diff(from).shiftTo('minutes').minutes / intervalMinutes);
    const groupsCount = advisorsData.length + (showUnassignedRow ? 1 : 0);

    const columnWidth = contentAreaWidth
        ? vertical
            ? Math.max(10, contentAreaWidth / groupsCount)
            : intervalMinutes < 30
            ? 100
            : contentAreaWidth / stepsCount
        : defaultColumnWidth;

    const minRowsCountToAutoSize = 7;
    const rowHeight =
        autoSizeRowHeight &&
        contentAreaHeight &&
        (vertical || groupsCount >= minRowsCountToAutoSize)
            ? vertical
                ? intervalMinutes < 30
                    ? 40
                    : contentAreaHeight / stepsCount
                : Math.max(10, contentAreaHeight / groupsCount)
            : defaultRowHeight;

    const convertAppointmentModal = useConvertAppointmentModalOptional();

    const renderAppointment = useCallback(
        (model: types.AppointmentBlock) => {
            return (
                <AppointmentBlock
                    onClick={() => convertAppointmentModal?.open(model.id)}
                    appointment={model}
                    availableVerticalSpace={rowHeight}
                    outerContainer={schedulerRef}
                />
            );
        },
        [convertAppointmentModal, rowHeight, schedulerRef]
    );

    return (
        <div>
            <PlanningHeader
                planningId={planning.id}
                planningType="Advisors"
                showLegend={!hideLegend}
                isSingleView={singleView}
            />
            {advisorsData.length !== 0 && (
                <WorkshopPlannerCurrentDayContext.Provider value={currentDay}>
                    <SchedulerScrollbarControllerProvider>
                        <PlanningAutoScroll
                            selectedDate={startOfDay}
                            users={advisorsData}
                            firstFilteredBlockTime={firstFilteredBlock?.time}
                            firstFilteredBlockUserKey={firstFilteredBlock?.userKey}
                        />
                        <SchedulerComponent<types.AppointmentBlock, AdvisorData>
                            schedulerRef={setSchedulerRef}
                            title={t('workshopPlanner.advisors')}
                            vertical={vertical}
                            GroupDecorationComponent={UserScheduleDecorationComponent}
                            rowHeight={rowHeight}
                            sideWidth={sideWidth}
                            columnWidth={columnWidth}
                            headerHeight={headerHeight}
                            maxHeight={maxHeight}
                            renderGroup={renderGroup}
                            renderItem={renderAppointment}
                            stepLengthInMinutes={intervalMinutes}
                            stepsCount={stepsCount}
                            from={from.toMillis()}
                            to={to.toMillis()}
                            items={filteredAppointments ?? []}
                            groups={advisorsData}
                            getGroupKey={getGroupKey}
                            getItemKey={getItemKey}
                            getItemGroupKey={getItemGroupKey}
                            getItemMinutesDuration={getItemMinutesDuration}
                            getItemStatic={getItemStatic}
                            getItemTimestamp={getItemTimestamp}
                            allowOverlap
                            isDraggable={false}
                            hideTitleFirstInterval
                            stickyPart={
                                showUnassignedRow
                                    ? {
                                          header: <AdvisorsStickyPartHeader vertical={vertical} />,
                                          content: (
                                              <AdvisorsStickyPart
                                                  appointments={didNotArriveAppointment}
                                                  vertical={vertical}
                                              />
                                          ),
                                      }
                                    : undefined
                            }
                        >
                            <Absences
                                filterUserIds={displayedAdvisorIds}
                                vertical={vertical}
                                singleView={singleView}
                            />
                        </SchedulerComponent>
                    </SchedulerScrollbarControllerProvider>
                </WorkshopPlannerCurrentDayContext.Provider>
            )}
            {pagesCount > 1 && (
                <Pagination
                    sx={{
                        paddingTop: '7px',
                        [`& .${paginationClasses.ul}`]: {
                            justifyContent: 'center',
                        },
                        height: PLANNING_PAGINATION_HEIGHT,

                        '::after': {
                            display: 'block',
                            left: 0,
                            right: 0,
                            top: -1,
                            background: 'var(--neutral3)',
                            content: '" "',
                            position: 'absolute',
                            height: '1px',
                        },
                    }}
                    size="small"
                    color="primary"
                    page={page}
                    onChange={(_, page) => setPage(page)}
                    count={pagesCount}
                />
            )}
        </div>
    );
};

const BorderlessSchedulerWithGroups = styled(SchedulerWithGroups)({
    [`& .${schedulerLayoutClasses.layout}`]: {
        border: 'none',
        borderRadius: 0,
    },
    [`& .${schedulerLayoutClasses.title}`]: {
        borderLeft: 'none',
        borderTop: 'none',
        height: 'calc(var(--wps-header-height) - 1px)',
    },
}) as typeof SchedulerWithGroups;

export default WorkshopPlannerAdvisors;
