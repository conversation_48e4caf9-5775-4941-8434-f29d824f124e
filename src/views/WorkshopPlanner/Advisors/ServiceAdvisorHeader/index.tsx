import { styled } from '@mui/material';

type ServiceAdvisorHeaderProps = {
    color: string;
    text: string;
    vertical: boolean;
};

const ServiceAdvisorHeader = ({ color, text, vertical }: ServiceAdvisorHeaderProps) => {
    return (
        <DivRoot style={vertical ? { justifyContent: 'center' } : {}}>
            <DivCircle style={{ background: color }} />
            {text}
        </DivRoot>
    );
};

const DivRoot = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[9],
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    background: 'var(--neutral1)',
}));

const DivCircle = styled('div')({
    width: 6,
    height: 6,
    borderRadius: '50%',
    marginRight: 8,
    marginLeft: 11,
});

export default ServiceAdvisorHeader;
