import { styled } from '@mui/material';
import { AbsenceDto } from 'api/users';
import Projection, {
    ScheduleProjectionItemProps,
} from 'common/components/Scheduler/addons/Projection';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import { DateTime } from 'luxon';
import { useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectAbsences } from 'store/slices/absences/selectors';
import { useEditAbsencePopup } from 'views/absence/EditAbsence/context';
import AbsenceBlock from 'views/Components/AbsenceBlock';

export type AbsencesProps = {
    filterUserIds: string[];
    vertical: boolean;
    singleView: boolean;
};

export default function Absences({ filterUserIds, vertical, singleView }: AbsencesProps) {
    const absences = useAbsences(filterUserIds);

    return (
        <Projection<AbsenceData>
            items={absences}
            getDuration={getPDuration}
            getGroupKey={getPGroupKey}
            getKey={getPKey}
            getStartsAt={getPStartsAt}
            itemComponent={AbsenceProjectionItem}
            vertical={vertical}
            singleView={singleView}
        />
    );
}

function AbsenceProjectionItem({
    value,
    height,
    width,
    left,
    top,
    vertical,
}: ScheduleProjectionItemProps<AbsenceData>) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const {
        dimensions: { rowHeight },
    } = useSchedulerContext();

    const editAbsencePopup = useEditAbsencePopup();

    const editAbsence = () => {
        editAbsencePopup.open(value.absence, anchorEl!);
    };

    return (
        <DivProjectionItem
            onClick={(e) => {
                setAnchorEl(e.currentTarget);
            }}
            style={{
                height,
                width,
                transform: `translate(${left}px, ${top}px)`,
            }}
        >
            <AbsenceBlock
                availableVerticalSpace={rowHeight}
                absence={value.absence}
                leftOffset={140}
                onClick={editAbsence}
                vertical={vertical}
            />
        </DivProjectionItem>
    );
}

const DivProjectionItem = styled('div')({
    position: 'absolute',
    left: 0,
    top: 0,
});

type AbsenceData = {
    userId: string;
    startsAt: number;
    duration: number;
    absence: AbsenceDto;
};

function useAbsences(selectedAdvisors: string[]) {
    const absences = useAppSelector(selectAbsences);

    return useMemo(() => {
        const data: AbsenceData[] = [];

        for (const absence of absences) {
            if (absence.userId !== null && !selectedAdvisors.includes(absence.userId)) continue;

            const startsAtDt = DateTime.fromISO(absence.startsAt);
            const endsAt = DateTime.fromISO(absence.endsAt);
            const duration = endsAt.diff(startsAtDt).as('minutes');
            const startsAt = startsAtDt.toMillis();
            const applicableAdvisors = absence.userId ? [absence.userId] : selectedAdvisors;
            for (const userId of applicableAdvisors) {
                data.push({
                    userId,
                    startsAt,
                    duration,
                    absence,
                });
            }
        }

        return data;
    }, [absences, selectedAdvisors]);
}

const getPKey = (p: AbsenceData) => `${p.absence.id}-${p.userId}`;
const getPDuration = (p: AbsenceData) => p.duration;
const getPStartsAt = (p: AbsenceData) => p.startsAt;
const getPGroupKey = (p: AbsenceData) => p.userId;
