import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { UserScheduleDto } from 'api/common';
import { PUBNUB_CHANNELS, PUBNUB_MESSAGE_TYPES } from 'api/pubnub';
import { AbsenceDto } from 'api/users';
import { WpPhasesApi } from 'api/workshopPlanner';
import { OrderUpdatedEvent } from 'api/workshopPlanner/orders';
import { GroupDecorationComponentProps } from 'common/components/Scheduler/GridCanvas/GroupDecorations';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import useIntervalIfDocumentActive from 'common/hooks/useIntervalIfDocumentActive';
import { TFunction } from 'i18next';
import { DateTime } from 'luxon';
import moment from 'moment';
import { createContext, useCallback, useContext, useEffect, useMemo, useRef } from 'react';
import { useSelector, useStore } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { absencesActions } from 'store/slices/absences';
import { selectIanaTz, selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import {
    ensureOrderIsPresentThunk,
    selectWpOrder,
    selectWpOrderViewsWithoutPaused,
    wpOrdersActions,
} from 'store/slices/wp/orders';
import { loadAllPausedOrdersThunk, selectFilteredPausedOrders } from 'store/slices/wp/paused';
import {
    BlockUpdatedEvent,
    BlocksUnassignedEvent,
    loadScheduleThunk,
    planningsActions,
    selectPlanningsWithBlockIds,
} from 'store/slices/wp/plannings';
import ensureAppointmentIsPresentThunk from 'store/slices/wp/plannings/thunks/ensureAppointmentIsPresent';
import loadWorkshopPlannerAppointmentsThunk from 'store/slices/wp/plannings/thunks/loadAppointments';
import {
    selectFilterApplied,
    selectFilteredAppointmentIds,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import { usePubnubListener, usePubnubSubscription } from 'utils/pubnub';
import { getRestrictedZones } from './Planning';

export const renderPromisedDate = (t: TFunction, date: string | null | undefined): string => {
    if (date === null || date === undefined) return '--';

    switch (moment(date).format('DD/MM/YY')) {
        case moment().format('DD/MM/YY'):
            return `${t('commonLabels.today')}, ${moment(date).format('HH:mma')}`;
        case moment().add(1, 'days').format('DD/MM/YY'):
            return `${t('commonLabels.tomorrow')}, ${moment(date).format('HH:mma')}`;
        default:
            return moment(date).format('DD/MM/YY, HH:mma');
    }
};

type WeekLikeObject<T> = {
    sunday: T;
    monday: T;
    tuesday: T;
    wednesday: T;
    thursday: T;
    friday: T;
    saturday: T;
};

export function getValueForDay<T>(v: WeekLikeObject<T>, day: number): T {
    return [v.sunday, v.monday, v.tuesday, v.wednesday, v.thursday, v.friday, v.saturday][day];
}

export type UserLikeObject = {
    displayName: string;
    absent: boolean;
    schedule: UserScheduleDto;
};

export const WorkshopPlannerCurrentDayContext = createContext<Date>(new Date());

export function UserScheduleDecorationComponent({
    group,
    vertical,
}: GroupDecorationComponentProps<UserLikeObject>) {
    const day = useContext(WorkshopPlannerCurrentDayContext);
    const ctx = useSchedulerContext();
    const schedulerFrom = moment(ctx.interval.from);
    const current = getValueForDay(group.schedule, day.getDay());

    const ianaTz = useSelector(selectIanaTz);
    const from = DateTime.fromJSDate(day).set({ millisecond: 0, second: 0, minute: 0, hour: 0 });
    const to = from.plus({ day: 1 });
    const rzs = useMemo(
        () => getRestrictedZones('', group.schedule, ianaTz, from, to),
        [from, to, ianaTz, group.schedule]
    );

    if (current === undefined) {
        return null;
    }

    const technicianIntervals = rzs.map((w) => {
        const start = moment(w.timestamp);
        return {
            start: +start.toDate(),
            duration: w.minutesDuration,
        };
    });

    return [...technicianIntervals].map(({ start, duration }, index) => {
        const startDate = moment(start);
        const secondsFrom = startDate.diff(schedulerFrom, 's');
        const timelineCoord = (secondsFrom / 60) * ctx.dimensions.pixelsPerMinuteRatio;
        const timelineIntervalSize = duration * ctx.dimensions.pixelsPerMinuteRatio;

        return (
            <DivRestrictedZone
                key={`${start}_${duration}_${index}`}
                style={
                    vertical
                        ? {
                              top: `${timelineCoord}px`,
                              height: `${timelineIntervalSize}px`,
                              width: '100%',
                          }
                        : {
                              left: `${timelineCoord}px`,
                              width: `${timelineIntervalSize}px`,
                              height: '100%',
                          }
                }
            />
        );
    });
}

const DivRestrictedZone = styled('div')({
    position: 'absolute',
    background: 'var(--neutral3)',
});

/**
 * Hooks that fetches WP schedule and subscribes to pubnub events.
 */
export function useScheduleFetcher(dateString: string) {
    const dispatch = useAppDispatch();
    const dateRef = useRef(dateString);
    dateRef.current = dateString;

    const loadSchedule = useCallback(() => {
        dispatch(loadScheduleThunk({ date: dateRef.current }));
    }, [dispatch]);

    // refetch periodically every 5 min just in case
    useIntervalIfDocumentActive(loadSchedule, 300000, false);

    useEffect(() => {
        // refetch when date changes
        dispatch(loadScheduleThunk({ date: dateString }));
    }, [dateString, dispatch]);

    const { uid: shopId } = useAppSelector(selectSettings);

    // subscribe to absence and block updates channel
    usePubnubSubscription({
        channels: [PUBNUB_CHANNELS.wpBlocks(shopId), PUBNUB_CHANNELS.absences(shopId)],
    });

    // real-time block updates
    usePubnubListener<BlockUpdatedEvent | string>(
        ({ message: { payload, type } }) => {
            if (type === PUBNUB_MESSAGE_TYPES.wp.block.deleted && typeof payload === 'string') {
                // handling wp.block.deleted
                dispatch(planningsActions.deleteBlockById(payload));
            } else if (
                type === PUBNUB_MESSAGE_TYPES.wp.block.updated &&
                typeof payload === 'object' &&
                payload
            ) {
                // handling wp.block.updated
                if (payload.type === 'Order') {
                    dispatch(ensureOrderIsPresentThunk({ id: payload.orderId }));
                } else if (payload.type === 'PlannedOrder') {
                    dispatch(ensureAppointmentIsPresentThunk({ id: payload.appointmentId }));
                }
                dispatch(planningsActions.onBlockUpdated(payload));
            }
        },
        {
            channels: [],
            types: [PUBNUB_MESSAGE_TYPES.wp.block.updated, PUBNUB_MESSAGE_TYPES.wp.block.deleted],
            listenerEnabled: !!shopId,
        }
    );

    usePubnubListener<BlocksUnassignedEvent>(
        ({ message: { payload } }) => {
            dispatch(planningsActions.unassignBlocks(payload.blockIds));
        },
        {
            channels: [],
            types: ['wp.block.unassigned_multiple'],
            listenerEnabled: !!shopId,
        }
    );

    // real-time absence updates
    usePubnubListener<AbsenceDto>(
        ({ message }) => {
            dispatch(absencesActions.addAbsence(message.payload));
        },
        {
            channels: [],
            types: ['absence.changed'],
            listenerEnabled: !!shopId,
        }
    );

    return { loadSchedule };
}

export function useWorkshopPlannerAppointmentsFetcher(dateString: string) {
    const dispatch = useAppDispatch();
    const dateRef = useRef(dateString);
    dateRef.current = dateString;

    useEffect(() => {
        dispatch(loadWorkshopPlannerAppointmentsThunk(dateString));
    }, [dispatch, dateString]);

    const loadAppointments = useCallback(
        () => dispatch(loadWorkshopPlannerAppointmentsThunk(dateRef.current)),
        [dispatch]
    );

    useIntervalIfDocumentActive(loadAppointments, 300000, false);
}

export function isStandardOperationsAllowed(
    isOrderSelected: boolean,
    isOrderHasNoType: boolean,
    enableStandardOperations: boolean,
    standardOperationOrderTypes?: string[],
    orderTypeId?: string
): boolean {
    const isAllOrderTypesAllowed =
        !!standardOperationOrderTypes && standardOperationOrderTypes.length === 0;

    const isOrderTypeAllowed =
        isAllOrderTypesAllowed ||
        (isAllOrderTypesAllowed && isOrderHasNoType) ||
        (orderTypeId && standardOperationOrderTypes?.includes(orderTypeId));

    const canUseStandardOperations =
        enableStandardOperations &&
        (isAllOrderTypesAllowed || (isOrderTypeAllowed && isOrderSelected));

    return canUseStandardOperations || false;
}

export function usePhases() {
    const dispatch = useAppDispatch();
    const store = useStore();
    const settings = useAppSelector(selectSettings);

    const { data, isLoading } = useQuery(['wp', 'phases'], WpPhasesApi.getPhases);

    const { phases, phasesLoaded } = useMemo(
        () => ({ phases: data ?? [], phasesLoaded: !!data && !isLoading }),
        [data, isLoading]
    );

    usePubnubListener<OrderUpdatedEvent>(
        async (_message) => {
            const order = selectWpOrder(
                store.getState() as RootState,
                _message.message.payload.orderKey
            );

            if (order) {
                const phase = phases.find((p) => p.id === _message.message.payload.phaseId);
                dispatch(wpOrdersActions.update({ id: _message.message.payload.orderKey, phase }));

                dispatch(loadAllPausedOrdersThunk());
                dispatch(loadScheduleThunk({}));
            }
        },
        {
            channels: [PUBNUB_CHANNELS.orders(settings.uid)],
            types: ['order.updated'],
            listenerEnabled: settings.appMode === 'RepairShop',
        }
    );

    return { phases, phasesLoaded };
}

export function useTabsCounters() {
    const filterApplied = useAppSelector(selectFilterApplied);
    const planningsWithBlocks = useAppSelector(selectPlanningsWithBlockIds);
    const filteredOrderIds = useAppSelector(selectFilteredOrderIds);
    const filteredAppointmentIds = useAppSelector(selectFilteredAppointmentIds);

    const planningsCounters: Record<string, number> = useMemo(() => {
        const result: Record<string, number> = {};
        for (const p of planningsWithBlocks) {
            result[p.planningId] = p.blocks.reduce((acc, block) => {
                if (!filterApplied) {
                    return 0;
                }

                return (
                    acc +
                    ((block.orderId && filteredOrderIds.includes(block.orderId)) ||
                    (block.appointmentId && filteredAppointmentIds.includes(block.appointmentId))
                        ? 1
                        : 0)
                );
            }, 0);
        }

        return result;
    }, [filterApplied, planningsWithBlocks, filteredOrderIds, filteredAppointmentIds]);

    const phasesOrders = useAppSelector(selectWpOrderViewsWithoutPaused);
    const phasesOrdersCounter = useMemo(() => {
        return phasesOrders.reduce((acc, order) => {
            if (!filterApplied) {
                return 0;
            }

            return (
                acc +
                (filteredOrderIds.includes(order.id) ||
                (order.appointmentId && filteredAppointmentIds.includes(order.appointmentId))
                    ? 1
                    : 0)
            );
        }, 0);
    }, [filterApplied, phasesOrders, filteredOrderIds, filteredAppointmentIds]);

    const { pausedOrders } = useAppSelector(selectFilteredPausedOrders);
    const pausedOrdersCounter = useMemo(() => {
        return pausedOrders.reduce((acc, order) => {
            if (!filterApplied) {
                return 0;
            }

            return (
                acc +
                (filteredOrderIds.includes(order.id) ||
                (order.appointmentId && filteredAppointmentIds.includes(order.appointmentId))
                    ? 1
                    : 0)
            );
        }, 0);
    }, [filterApplied, pausedOrders, filteredOrderIds, filteredAppointmentIds]);

    return { planningsCounters, phasesOrdersCounter, pausedOrdersCounter, filterApplied };
}
