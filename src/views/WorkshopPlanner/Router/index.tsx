import { ROUTES } from 'common/constants/RoutesDefinition';
import { Navigate, Route, Routes } from 'react-router-dom';
import WorkshopPlanner from '..';

const WorkshopPlannerRouter = () => {
    return (
        <Routes>
            <Route path="/" element={<WorkshopPlanner />} />
            <Route path="*" element={<Navigate replace to={ROUTES.WORKSHOP_PLANNER} />} />
        </Routes>
    );
};

export default WorkshopPlannerRouter;
