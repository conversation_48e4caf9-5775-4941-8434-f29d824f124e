import { JobDescriptionDto } from 'api/workshopPlanner';
import { OrderReasonInfo } from 'api/workshopPlanner/orders';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AdditionalNoteInfoDto } from 'datacontracts/AdditionalNoteDto';
import moment from 'moment';
import { useMemo } from 'react';
import {
    DetailsList,
    DetailsRow,
    OverflowContainer,
    StyledGridContainer,
    StyledGridTitle,
    StyledTitle,
} from '../TooltipComponents';
import TowerNumber, { TowerColorOptions } from '../TowerNumber';

type OrderDetailsTooltipProps = {
    tower: string;
    towerColor: TowerColorOptions;
    orderNumber: string;
    customerFirstName: string;
    customerLastName: string;
    model: string;
    plates: string;
    orderType?: string;
    deliveryDate: string | null;
    phase: string;
    duration?: string;
    serviceAdvisorName: string | null;
    notes: AdditionalNoteInfoDto[];
    orderReasons: OrderReasonInfo[];
    jobDescriptions: JobDescriptionDto[];
};

const OrderDetailsTooltip = ({
    tower,
    towerColor,
    orderNumber,
    customerFirstName,
    customerLastName,
    model,
    plates,
    orderType,
    deliveryDate,
    phase,
    duration,
    serviceAdvisorName,
    notes,
    orderReasons,
    jobDescriptions,
}: OrderDetailsTooltipProps) => {
    const { t } = useAppTranslation();

    const formattedDate = useMemo(
        () => moment(deliveryDate).format('DD/MM/YY, hh:mm a'),
        [deliveryDate]
    );

    const customerNotes = useMemo(() => {
        const filteredNotes = notes ? notes.filter((x) => x.type === 'ForCustomer') : [];
        return filteredNotes;
    }, [notes]);

    const internalNotes = useMemo(() => {
        const filteredNotes = notes ? notes.filter((x) => x.type === 'ForInternal') : [];
        return filteredNotes;
    }, [notes]);

    const customerOrderReasons = useMemo(() => {
        return orderReasons ? orderReasons.filter((x) => x.isFromCustomer) : [];
    }, [orderReasons]);

    const internalOrderReasons = useMemo(() => {
        return orderReasons ? orderReasons.filter((x) => !x.isFromCustomer) : [];
    }, [orderReasons]);

    return (
        <OverflowContainer>
            <StyledGridContainer container onMouseDown={(e) => e.stopPropagation()}>
                <StyledGridTitle container>
                    <TowerNumber {...towerColor} towerNumber={tower} orderNumber={orderNumber} />
                    <StyledTitle>
                        {makeTitle(orderNumber, customerFirstName, customerLastName, model, plates)}
                    </StyledTitle>
                </StyledGridTitle>
                {orderType && (
                    <DetailsRow title={t('commonLabels.orderType')} details={orderType} />
                )}
                {deliveryDate && (
                    <DetailsRow
                        title={t('workshopPlanner.detailsTooltip.promiseOfDelivery')}
                        details={formattedDate}
                    />
                )}
                <DetailsRow title={t('workshopPlanner.detailsTooltip.phase')} details={phase} />
                {duration && (
                    <DetailsRow
                        title={t('workshopPlanner.detailsTooltip.duration')}
                        details={duration}
                    />
                )}
                {serviceAdvisorName && (
                    <DetailsRow
                        title={t('workshopPlanner.detailsTooltip.serviceAdvisor')}
                        details={serviceAdvisorName}
                    />
                )}
                {jobDescriptions && jobDescriptions.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.jobDescription')}
                        details={(jobDescriptions ?? []).map((x) => x.name)}
                    />
                )}
                {customerOrderReasons.length > 0 && (
                    <DetailsList
                        title={t('orderDetails.orderReasons.customerReasonsForVisit')}
                        details={customerOrderReasons.map((x) => x.text)}
                    />
                )}
                {internalOrderReasons.length > 0 && (
                    <DetailsList
                        title={t('orderDetails.orderReasons.workshopReasonsForVisit')}
                        details={internalOrderReasons.map((x) => x.text)}
                    />
                )}
                {customerNotes.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.visibleToCustomerNotes')}
                        details={customerNotes.map((x) => x.text)}
                    />
                )}
                {internalNotes.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.internalNotes')}
                        details={internalNotes.map((x) => x.text)}
                    />
                )}
            </StyledGridContainer>
        </OverflowContainer>
    );
};

const makeTitle = (
    orderNumber: string,
    firstName: string,
    lastName: string,
    model: string,
    plates: string
) => {
    let title = '#' + orderNumber;

    if (firstName || lastName) {
        title += ' - ' + (firstName ? firstName + ' ' : '') + lastName;
    }

    if (model || plates) {
        title += ' - ' + (model ? model + ', ' : '') + plates;
    }

    return title;
};

export default OrderDetailsTooltip;
