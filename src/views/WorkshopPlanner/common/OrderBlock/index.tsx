import { styled, useTheme } from '@mui/material';
import useResizeObserver from '@react-hook/resize-observer';
import { DetailedAppointmentReasonDto } from 'api/appointments';
import WpApi, { JobDescriptionDto } from 'api/workshopPlanner';
import { OrderReasonInfo } from 'api/workshopPlanner/orders';
import clsx from 'clsx';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import { Colors } from 'common/styles/Colors';
import { AdditionalNoteInfoDto } from 'datacontracts/AdditionalNoteDto';
import { TFunction } from 'i18next';
import { CSSProperties, forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import { isDark } from 'utils/colors';
import { renderPromisedDate } from 'views/WorkshopPlanner/helpers';
import OrderDetailsTooltip from '../OrderDetailsTooltip';
import { StatusLabel } from '../StatusLabel';
import { StyledTooltip } from '../TooltipComponents';
import TowerNumber, { TowerColorOptions } from '../TowerNumber';
import { useIsBlockSelected } from '../selectedBlockContext';
import { useBlockStyle } from '../utils';
import { isScheduleEntryInvalid, isScheduleEntryWarned } from './helpers';

export type OrderBlockProps = MergeTypes<
    {
        orderId: string;
        orderNumber: string;
        firstName: string;
        lastName: string;
        model: string;
        plates: string;
        orderType?: string;
        startsAtDate: string;
        deliveryDate: string | null;
        phase: { id: number; name: string };
        selected?: boolean;
        tooltipIsOpened: boolean;
        isDragging: boolean;
        tooltipPosition?: 'top' | 'bottom' | 'right' | 'left';
        onClick?: React.HTMLAttributes<HTMLDivElement>['onClick'];
        onDoubleClick?: React.HTMLAttributes<HTMLDivElement>['onClick'];
        onOpenTooltip?: () => void;
        onCloseTooltip?: () => void;
        isNew?: boolean;
        isChanged?: boolean;
        isStatic?: boolean;
        children?: React.ReactNode;
        planningName?: string;
        availableVerticalSpace?: number;
        disabled?: boolean;
        duration: string;
        inChargeName: string | null;
        appointmentReasons: DetailedAppointmentReasonDto[];
        notes: AdditionalNoteInfoDto[];
        orderReasons: OrderReasonInfo[];
        tower: string;
        towerColor: TowerColorOptions;
        vertical: boolean;
        singleView: boolean;
        height: number;
        hasRedItems: boolean;
        templateId: number | null;
        jobDescriptions: JobDescriptionDto[];
        jobStatus: WpApi.JobStatus;
        accumulatedDurationInSeconds: number;
        lastStart: number | null;
        isFiltered: boolean;
    },
    React.HTMLAttributes<HTMLDivElement>
>;

export const orderBlockClasses = {
    root: 'OrderBlock-root',
    phase: 'OrderBlock-phase',
    promise: 'OrderBlock-promise',
    promiseLabel: 'OrderBlock-promiseLabel',
    promiseValue: 'OrderBlock-promiseValue',

    orderType: 'OrderBlock-orderType',
    vehicle: 'OrderBlock-vehicle',
    order: 'OrderBlock-order',
    tower: 'OrderBlock-tower',
};

function getOrderBlockBreakpoint(height: number): OrderBlockBreakpoint {
    if (height <= 60) {
        return 'sm';
    }

    if (height <= 120) {
        return 'md';
    }

    return 'lg';
}

type OrderBlockBreakpoint = 'lg' | 'md' | 'sm';

const OrderBlock = forwardRef(
    (
        {
            orderId,
            tower,
            towerColor,
            orderNumber,
            firstName,
            lastName,
            model,
            plates,
            orderType,
            startsAtDate,
            deliveryDate,
            phase,
            color,
            tooltipIsOpened,
            isDragging,
            tooltipPosition = 'top',
            onClick,
            onDoubleClick,
            onOpenTooltip,
            onCloseTooltip,
            selected,
            isNew,
            isChanged,
            isStatic,
            children,
            planningName,
            availableVerticalSpace = Infinity,
            disabled = false,
            duration,
            inChargeName,
            appointmentReasons,
            notes,
            orderReasons,
            vertical,
            singleView,
            height,
            templateId,
            hasRedItems,
            jobDescriptions,
            jobStatus,
            accumulatedDurationInSeconds,
            lastStart,
            isFiltered,
            ...props
        }: OrderBlockProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const theme = useTheme();
        const invalid = isScheduleEntryInvalid(phase.id, startsAtDate, deliveryDate);

        const { t } = useAppTranslation();
        const isBlockSelected = useIsBlockSelected((state) => state.orderNumber === orderNumber);

        const boundaryNumberOfDigits = 5;

        const maximumNumberOfDigits =
            useAppSelector(selectSettings).repairShopSettings?.features
                .maximumNumberOfDigitsToDisplayForOrderAndAppointment ?? boundaryNumberOfDigits;

        const towerNumberMaxLength =
            useAppSelector(selectSettings).repairShopSettings?.features
                .maximumNumberOfDigitsToDisplayForTowerNumber ?? boundaryNumberOfDigits;

        const orderNumberModified = orderNumber.slice(-maximumNumberOfDigits);

        const breakpoint = getOrderBlockBreakpoint(availableVerticalSpace);
        const vehicleInfoString = [model.trim(), plates.trim()].filter(Boolean).join(' - ') || '--';

        const wpSettings = useAppSelector(selectShopWpSettings);
        const isWarned =
            wpSettings === null
                ? false
                : isScheduleEntryWarned(
                      wpSettings.urgentFlashingIndicationEnabled,
                      templateId,
                      wpSettings.urgentFlashingIndicationTemplateId,
                      hasRedItems
                  );

        const style = useBlockStyle({
            ...towerColor,
        });

        const canShowMoreThanOneRows = !vertical || height / availableVerticalSpace >= 0.45;

        const [root, setRoot] = useState<HTMLDivElement | null>(null);
        const [hiddenDiv, setHiddenDiv] = useState<HTMLDivElement | null>(null);
        const overflowingRef = useRef(false);
        const fr = useForceRender();

        useResizeObserver(
            root,
            useCallback(() => {
                if (!hiddenDiv) return;

                // NOTE (AK) actually instead of root div, hidden div is using here.
                // So it can both check if there is no place for current text, and it should be just truncated,
                // and check if there is enough place and text should be adjusted.
                const overflowing = hiddenDiv.scrollHeight > hiddenDiv.clientHeight;

                if (overflowing !== overflowingRef.current) {
                    overflowingRef.current = overflowing;
                    fr();
                }
            }, [fr, hiddenDiv])
        );

        useEffect(() => {
            if (!hiddenDiv) return;

            const overflowing = hiddenDiv.scrollHeight > hiddenDiv.clientHeight;

            if (overflowing !== overflowingRef.current) {
                overflowingRef.current = overflowing;
                fr();
            }
        }, [fr, hiddenDiv]);

        return (
            <div ref={ref} {...props}>
                <StyledTooltip
                    placement={tooltipPosition}
                    arrow
                    title={
                        <OrderDetailsTooltip
                            tower={tower}
                            towerColor={towerColor}
                            orderNumber={orderNumber}
                            customerFirstName={firstName}
                            customerLastName={lastName}
                            model={model}
                            plates={plates}
                            orderType={orderType}
                            deliveryDate={deliveryDate}
                            phase={getPhaseLocalization(t, phase.name)}
                            duration={duration}
                            serviceAdvisorName={inChargeName}
                            notes={notes}
                            orderReasons={orderReasons}
                            jobDescriptions={jobDescriptions}
                        />
                    }
                    onOpen={() => {
                        onOpenTooltip?.();
                    }}
                    onClose={() => {
                        onCloseTooltip?.();
                    }}
                    open={tooltipIsOpened}
                >
                    <DivRoot
                        data-new={isNew ? 'true' : 'false'}
                        adjustText={vertical && !overflowingRef.current}
                        onClick={onClick}
                        onDoubleClick={onDoubleClick}
                        className={clsx(
                            selected && 'OrderBlock-selected',
                            disabled && 'OrderBlock-disabled',
                            !tooltipIsOpened && invalid && 'OrderBlock-invalid',
                            !tooltipIsOpened && isWarned && 'OrderBlock-warning',
                            (isNew || isChanged) && 'OrderBlock-hasIndicator',
                            isStatic && 'OrderBlock-static',
                            `OrderBlock-${breakpoint}`,
                            !tooltipIsOpened &&
                                !isDragging &&
                                isBlockSelected &&
                                'OrderBlock-highlighted',
                            `OrderBlock-${vertical ? 'vertical' : 'horizontal'}`,
                            `OrderBlock-${singleView ? 'singleView' : 'normalView'}`,
                            isFiltered && 'OrderBlock-filter-match'
                        )}
                        style={
                            {
                                '--ob-tower-color': color ?? undefined,
                                '--ob-tower-color-text':
                                    color && isDark(color) ? theme.palette.neutral[8] : '#000',
                                '--ob-available-vertical-space': Number.isFinite(
                                    availableVerticalSpace
                                )
                                    ? `${availableVerticalSpace}`
                                    : '100',
                                ...style,
                            } as CSSProperties
                        }
                        ref={setRoot}
                    >
                        {vertical && (
                            <DivHiddenText ref={setHiddenDiv}>
                                <InnerText
                                    towerColor={towerColor}
                                    tower={tower}
                                    orderNumber={orderNumber}
                                    singleView={singleView}
                                    firstName={firstName}
                                    lastName={lastName}
                                    vertical={vertical}
                                    deliveryDate={deliveryDate}
                                    phase={phase}
                                    planningName={planningName}
                                    towerNumberMaxLength={towerNumberMaxLength}
                                    orderNumberModified={orderNumberModified}
                                    vehicleInfoString={vehicleInfoString}
                                    canShowMoreThanOneRows={canShowMoreThanOneRows}
                                    jobStatus={jobStatus}
                                    accumulatedDurationInSeconds={accumulatedDurationInSeconds}
                                    lastStart={lastStart}
                                />
                            </DivHiddenText>
                        )}
                        <InnerText
                            towerColor={towerColor}
                            tower={tower}
                            orderNumber={orderNumber}
                            singleView={singleView}
                            firstName={firstName}
                            lastName={lastName}
                            vertical={vertical}
                            deliveryDate={deliveryDate}
                            phase={phase}
                            planningName={planningName}
                            towerNumberMaxLength={towerNumberMaxLength}
                            orderNumberModified={orderNumberModified}
                            vehicleInfoString={vehicleInfoString}
                            canShowMoreThanOneRows={canShowMoreThanOneRows}
                            jobStatus={jobStatus}
                            accumulatedDurationInSeconds={accumulatedDurationInSeconds}
                            lastStart={lastStart}
                        />
                        {children}
                    </DivRoot>
                </StyledTooltip>
            </div>
        );
    }
);

type InnerTextProps = Pick<
    OrderBlockProps,
    | 'towerColor'
    | 'tower'
    | 'orderNumber'
    | 'singleView'
    | 'firstName'
    | 'lastName'
    | 'vertical'
    | 'deliveryDate'
    | 'phase'
    | 'planningName'
    | 'jobStatus'
    | 'accumulatedDurationInSeconds'
    | 'lastStart'
> & {
    towerNumberMaxLength: number;
    orderNumberModified: string;
    vehicleInfoString: string;
    canShowMoreThanOneRows: boolean;
};

function InnerText({
    towerColor,
    tower,
    orderNumber,
    singleView,
    firstName,
    lastName,
    vertical,
    deliveryDate,
    towerNumberMaxLength,
    orderNumberModified,
    vehicleInfoString,
    jobStatus,
    accumulatedDurationInSeconds,
    lastStart,
}: InnerTextProps) {
    const { t } = useAppTranslation();

    return (
        <>
            <TowerNumber
                {...towerColor}
                maximumNumberOfDigits={towerNumberMaxLength}
                towerNumber={tower}
                orderNumber={orderNumber}
                displayMode={singleView ? 'single-view' : 'min-content'}
            />
            <span className={orderBlockClasses.order}>
                <strong>#{orderNumberModified}</strong>{' '}
                {!singleView && (
                    <>
                        - {firstName} {lastName}
                    </>
                )}
            </span>
            {vertical ? <>{'- '}</> : <br />}
            <span className={orderBlockClasses.vehicle}>{vehicleInfoString}</span>
            {!singleView && (
                <>
                    <br />
                    <span className={orderBlockClasses.promise}>
                        <span className={orderBlockClasses.promiseLabel}>
                            {t('commonLabels.promise')}:&nbsp;
                        </span>
                        <span className={orderBlockClasses.promiseValue}>
                            {renderPromisedDate(t, deliveryDate)}
                        </span>
                    </span>
                </>
            )}
            {/* {canShowMoreThanOneRows && (
                <>
                    <br />
                    <SpanPhase>
                        {singleView
                            ? getPhaseLocalization(t, phase.name)
                            : [getPhaseLocalization(t, phase.name), planningName]
                                  .filter(Boolean)
                                  .join(' / ')}
                    </SpanPhase>
                </>
            )} */}
            <StatusLabel
                status={jobStatus}
                accumulatedDurationInSeconds={accumulatedDurationInSeconds}
                lastStart={lastStart}
            />
        </>
    );
}

export const getPhaseLocalization = (t: TFunction, phaseName: string) =>
    (phaseName
        ? ['noPhase', 'closedOrder'].includes(phaseName)
            ? t(`workshopPlanner.phases.${phaseName}`)
            : phaseName
        : ''
    ).toUpperCase();

export default OrderBlock;

const DivRoot = styled('div', {
    shouldForwardProp: (prop) => prop !== 'adjustText',
})<{ adjustText: boolean }>(({ theme, adjustText }) => ({
    ...theme.typography.body1,
    padding: '0.33em 0.1667em 0.33em 0.25em', // by default 1em = 12px
    lineHeight: '15px',
    margin: 1,
    whiteSpace: adjustText ? 'normal' : 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',

    borderRadius: 5,
    border: `1px solid ${theme.palette.neutral[4]}`,

    backgroundColor: '#fff',
    color: 'var(--text-color, var(--neutral8))',
    transition: '0.17s box-shadow',

    position: 'relative',
    boxSizing: 'border-box',

    '&:hover': {
        boxShadow: '0 3px 5px -2px' + theme.palette.neutral[4],
        backgroundColor: '#E6EEFE',
    },

    '& .TowerNumber': {
        display: 'inline-block',
        marginRight: '4px',
    },

    '&.OrderBlock-vertical': {
        width: 'calc(var(--wps-column-width) - 6px)',
        height: '100%',
    },

    '&.OrderBlock-horizontal': {
        height: 'calc(var(--wps-row-height) - 6px)',
    },

    '&.OrderBlock-hasIndicator': {
        '&::after': {
            content: '" "',
            display: 'block',
            height: 5,
            width: 5,
            borderRadius: 1000,
            position: 'absolute',
            top: 5,
            right: 5,
            backgroundColor: theme.palette.primary.main,
        },
    },

    '&.OrderBlock-static': {
        background:
            'repeating-linear-gradient(45deg, #eaeaea, #eaeaea 2px, #ffffff 2px, #ffffff 6px)',
        cursor: 'default',
        boxShadow: 'none !important',
    },

    '&.OrderBlock-invalid': {
        backgroundColor: Colors.Error_background,
        animation: 'invalidEffect 500ms linear alternate infinite',
    },

    '&.OrderBlock-warning': {
        backgroundColor: 'rgba(255, 198, 38, 0.4)',
        animation: 'warningEffect 500ms linear alternate infinite',
    },

    '&.OrderBlock-selected': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 1px ${theme.palette.primary.main}`,
        '&:hover': {
            boxShadow: `0 0 0 2px ${theme.palette.primary.main}`,
        },
    },

    '&.OrderBlock-highlighted': {
        boxShadow: `5px 5px 10px 5px #AEC5FE`,
    },

    '&.OrderBlock-sm': {
        lineHeight: 'calc(11px * var(--ob-available-vertical-space) / 50)',
        fontSize: 'calc(9px * var(--ob-available-vertical-space) / 50)',
        margin: 0,
        boxShadow: 'none',
        transition: 'border-color 0.17s',

        ':hover': {
            borderColor: 'var(--cm4)',
        },

        '& .TowerNumber': {
            height: '14px',
        },

        '&.OrderBlock-singleView .TowerNumber': {
            height: '0.9em',
        },

        '&.OrderBlock-vertical': {
            width: 'var(--wps-column-width)',
        },

        '&.OrderBlock-horizontal': {
            height: 'var(--wps-row-height)',
        },
    },
    '&.OrderBlock-md': {},
    '&.OrderBlock-lg': {
        fontSize: '14px',

        display: 'flex',
        flexDirection: 'column',
    },

    '&.OrderBlock-disabled': {
        cursor: 'default',

        ':hover': {
            boxShadow: 'none',
        },
    },

    '&.OrderBlock-filter-match': {
        outline: `3px solid ${theme.palette.primary.main}`,
        outlineOffset: '-3px',
    },

    '@keyframes invalidEffect': {
        '0%': {
            backgroundColor: 'var(--block-color, #fff)',
        },
        '40%': {
            backgroundColor: 'var(--block-color, #fff)',
        },
        '100%': {
            backgroundColor: `var(--blinking-color, ${Colors.Error_background})`,
        },
    },

    '@keyframes warningEffect': {
        '0%': {
            backgroundColor: 'var(--block-color, #fff)',
        },
        '40%': {
            backgroundColor: 'var(--block-color, #fff)',
        },
        '100%': {
            backgroundColor: `var(--blinking-color, 'rgba(255, 198, 38, 0.4)'`,
        },
    },
}));

const DivHiddenText = styled('div')({
    visibility: 'hidden',
    position: 'absolute',
    whiteSpace: 'normal',
    height: '100%',
});
