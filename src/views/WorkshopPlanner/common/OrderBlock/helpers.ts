import { Phases } from 'common/constants';
import moment from 'moment';

export function isScheduleEntryInvalid(
    phaseId: number,
    startsAtDate: string,
    deliveryDate: string | null
): boolean {
    const now = moment();

    // The Order block is programmed to start at a certain time, but the current time (the blue vertical line) is already passed the programmed start time,
    // and the Order has status "WITHOUT PHASE"
    if (moment(startsAtDate).isBefore(now) && phaseId === Phases.NoPhase) {
        return true;
    }

    // The Order block shows that it has a delivery promise for a specific day and time, but the timeline shows that that specific time and date has already passed
    if (moment(deliveryDate).isBefore(now) && phaseId !== Phases.Closed) {
        return true;
    }

    return false;
}

export function isScheduleEntryWarned(
    checkRedItems: boolean,
    orderTemplateId: number | null,
    templateId: number | null,
    hasRedItems: boolean
) {
    if (orderTemplateId === null || templateId === null) return false;
    return checkRedItems && hasRedItems && orderTemplateId === templateId;
}
