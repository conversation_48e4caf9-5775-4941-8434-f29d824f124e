import { MoreVertOutlined } from '@mui/icons-material';
import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import WpApi from 'api/workshopPlanner';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import RoundPauseIcon from 'common/components/Icons/RoundPauseIcon';
import RoundStartIcon from 'common/components/Icons/RoundStartIcon';
import RoundStopIcon from 'common/components/Icons/RoundStopIcon';
import TransferIcon from 'common/components/Icons/TransferIcon';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useNow from 'common/hooks/useNow';
import { rgba } from 'common/styles/ColorHelpers';
import { useCallback, useState } from 'react';
import { createSelector } from 'reselect';
import { RootState, useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import { selectUsers } from 'store/slices/users';

import { block, selectBlock } from 'store/slices/wp/plannings';
import { useOrderPopupOptional } from 'views/WorkshopPlanner/OrderPopup/context';
export type ThreeDotMenuAction =
    | 'reassign'
    | 'delete'
    | 'edit'
    | 'duplicate'
    | 'start'
    | 'stop'
    | 'pause'
    | 'pauseWithOrder'
    | 'resume';
type ThreeDotBlockMenuProps = {
    blockId: string;
    onAction: (request: { actionType: ThreeDotMenuAction; withoutPreview: boolean }) => void;
    onOpen?: () => void;
    onClose?: () => void;
    autoHide?: boolean;
    alignCenter?: boolean;
    orderNumber?: string;
    orderId?: string;
};

type BlockData = {
    block: block.State;
    canBeReassigned: boolean;
    isUnassigned: boolean;
    user: {
        id: string;
        name: string;
    };
};

const selectBlockData = createSelector(
    [(r: RootState) => r, (_, id: string, now: Date) => [id, now] as [string, Date]],
    (r, [id, now]): BlockData | null => {
        const block = selectBlock(r, id);
        const users = selectUsers(r);

        if (!block) {
            return null;
        }

        const user = users[block.userId];
        if (!user) {
            console.error('cannot find user with id ' + block.userId + ' in selectBlockData');
            return null;
        }

        const ts = +now;
        const endsAt = block.startsAt + block.duration * 60000;
        const canBeReassigned =
            !block.isUnassigned && block.startsAt <= ts && endsAt > ts && endsAt - ts > 60000;

        return {
            block,
            canBeReassigned,
            isUnassigned: block.isUnassigned,
            user: {
                id: user.key,
                name: user.name,
            },
        };
    }
);

export default function ThreeDotBlockMenu({
    blockId,
    orderNumber,
    orderId,
    onAction,
    onOpen,
    onClose,
    autoHide,
    alignCenter,
}: ThreeDotBlockMenuProps) {
    const now = useNow('minute');
    const blockData = useAppSelector((r) => selectBlockData(r, blockId, now));
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState(false);
    const { t } = useAppTranslation();
    const { allowEditWorkshopPlanner } = useAppSelector(selectUserPermission);

    const handleClose = useCallback(() => {
        setOpen(false);
        if (onClose) onClose();
    }, [onClose]);

    const { data } = useQuery({
        queryKey: ['blockId', blockId],
        queryFn: async () => await WpApi.getJobsTransitions(blockId, orderId!, blockData!.user.id),
        enabled: open && blockData != null && blockData.block.type === 'order',
    });

    const orderPopup = useOrderPopupOptional();
    if (!blockData) return null;

    return (
        <>
            <StyledButton
                ref={setAnchorEl}
                onDoubleClick={stopPropagation}
                onClick={(e) => {
                    e.stopPropagation();
                    if (onOpen) onOpen();
                    setOpen(true);
                }}
                onMouseOver={(e) => e.stopPropagation()}
                className={`${autoHide && !open ? 'DotMenu-autoHide' : undefined} ${
                    alignCenter && 'DotMenu-alignCenter'
                }`}
            >
                <MoreVertOutlined fontSize="small" />
            </StyledButton>
            <SMenu
                borders
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                open={open}
                onClose={handleClose}
                onMouseDown={stopPropagation} // prevents dragging of the block when dragging menu
                onMouseOver={(e) => e.stopPropagation()}
                anchorEl={anchorEl}
            >
                {blockData.canBeReassigned && (
                    <SMenuItem2
                        onClick={() => {
                            onAction({
                                actionType: 'reassign',
                                withoutPreview: blockData.isUnassigned,
                            });
                            handleClose();
                        }}
                    >
                        <TransferIcon style={{ marginRight: 5 }} size={24} />
                        {t('workshopPlanner.3dot.reassignJob')}
                    </SMenuItem2>
                )}
                <SMenuItem2
                    onClick={() => {
                        onAction({
                            actionType: 'edit',
                            withoutPreview: blockData.isUnassigned,
                        });
                        handleClose();
                    }}
                >
                    <EditIcon style={{ marginRight: 5 }} size={24} fill="currentColor" />
                    {t('workshopPlanner.3dot.seeOrEditJob')}
                </SMenuItem2>

                {blockData.block.type === 'order' && allowEditWorkshopPlanner && (
                    <div>
                        {blockData?.block.status === 'NotStarted' && (
                            <SMenuItem2
                                disabled={!data?.canBeStarted}
                                onClick={() => {
                                    onAction({
                                        actionType: 'start',
                                        withoutPreview: false,
                                    });

                                    handleClose();
                                }}
                            >
                                <RoundStartIcon
                                    style={{ marginRight: 5 }}
                                    size={24}
                                    fill="currentColor"
                                />
                                {t('workshopPlanner.3dot.startJob')}
                            </SMenuItem2>
                        )}
                        {(blockData?.block.status === 'Paused' ||
                            blockData?.block.status === 'Stopped') && (
                            <SMenuItem2
                                disabled={!data?.canBeStarted}
                                onClick={() => {
                                    onAction({
                                        actionType: 'resume',
                                        withoutPreview: false,
                                    });
                                    handleClose();
                                }}
                            >
                                <RoundStartIcon
                                    style={{ marginRight: 5 }}
                                    size={24}
                                    fill="currentColor"
                                />
                                {t('workshopPlanner.3dot.resumeJob')}
                            </SMenuItem2>
                        )}
                        {blockData?.block.status === 'InProgress' && (
                            <SMenuItem2
                                onClick={async () => {
                                    if (orderId != null && data?.canBePausedWithoutOrder) {
                                        onAction({
                                            actionType: 'pause',
                                            withoutPreview: false,
                                        });
                                    } else {
                                        onAction({
                                            actionType: 'pauseWithOrder',
                                            withoutPreview: false,
                                        });
                                    }

                                    handleClose();
                                }}
                            >
                                <RoundPauseIcon
                                    style={{ marginRight: 5 }}
                                    size={24}
                                    fill="currentColor"
                                />
                                {t('workshopPlanner.3dot.pauseJob')}
                            </SMenuItem2>
                        )}
                        {(blockData.block.status === 'InProgress' ||
                            blockData.block.status === 'Paused') && (
                            <SMenuItem2
                                onClick={() => {
                                    onAction({
                                        actionType: 'stop',
                                        withoutPreview: false,
                                    });
                                    handleClose();
                                }}
                            >
                                <RoundStopIcon
                                    style={{ marginRight: 5 }}
                                    size={24}
                                    fill="currentColor"
                                />
                                {t('workshopPlanner.3dot.stopJob')}
                            </SMenuItem2>
                        )}
                    </div>
                )}
                <SMenuItem2
                    onClick={() => {
                        onAction({
                            actionType: 'duplicate',
                            withoutPreview: blockData.isUnassigned,
                        });
                        handleClose();
                    }}
                >
                    <CopyIcon style={{ marginRight: 5 }} size={24} fill="currentColor" />
                    {t('workshopPlanner.3dot.duplicateJob')}
                </SMenuItem2>
                {blockData.block.type === 'order' && orderPopup && (
                    <SMenuItem2
                        onClick={() => {
                            orderNumber && orderPopup.open(orderNumber);
                        }}
                    >
                        <CarIcon style={{ marginRight: 5 }} size={24} fill="currentColor" />
                        {t('workshopPlanner.3dot.seeOrEditOrder')}
                    </SMenuItem2>
                )}
                <RedMenuItem
                    onClick={() => {
                        onAction({
                            actionType: 'delete',
                            withoutPreview: blockData.isUnassigned,
                        });
                        handleClose();
                    }}
                >
                    <DeleteIcon style={{ marginRight: 5 }} size={24} fill="currentColor" />
                    {t('workshopPlanner.3dot.deleteJob')}
                </RedMenuItem>
            </SMenu>
        </>
    );
}

const stopPropagation = (e: { stopPropagation: () => void }) => e.stopPropagation();

const StyledButton = styled('button')(({ theme }) => ({
    all: 'initial',
    boxSizing: 'border-box',
    backgroundColor: '#fff',
    position: 'absolute',
    top: 2,
    left: 'calc(var(--planning-block-menu-offset, 100%) - var(--size) - 10px)',
    borderRadius: '50%',
    '--size': '24px',
    height: 'var(--size)',
    width: 'var(--size)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: '.3s',
    border: `1px solid ${theme.palette.neutral[4]}`,

    '&:hover': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 2px ${theme.palette.primary.main}`,
    },

    '&.DotMenu-autoHide': {
        opacity: 0,

        // show if direct parent is hovered
        '*:hover > &': {
            opacity: 1,
        },
    },

    '&.DotMenu-alignCenter': {
        left: 'calc(var(--planning-block-menu-offset, 50%) - var(--size) / 2)',
    },
}));

const RedMenuItem = styled(SMenuItem2)(({ theme }) => ({
    color: 'var(--danger)',

    '&:hover': {
        color: 'var(--danger)',
        backgroundColor: rgba('var(--danger)', 0.1),
    },
}));
