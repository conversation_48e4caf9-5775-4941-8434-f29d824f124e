import { AppointmentNoteDto, DetailedAppointmentReasonDto } from 'api/appointments';
import { JobDescriptionDto } from 'api/workshopPlanner';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import { useMemo } from 'react';
import {
    DetailsList,
    DetailsRow,
    OverflowContainer,
    StyledGridContainer,
    StyledGridTitle,
    StyledTitle,
    appointmentReasonToString,
} from '../TooltipComponents';

type PlannedOrderDetailsTooltipProps = {
    appointmentNumber: string;
    customerFirstName: string;
    customerLastName: string;
    model: string;
    plates: string;
    status: string;
    duration: string | null;
    appointmentTime: string;
    serviceAdvisorName: string;
    appointmentReasons: DetailedAppointmentReasonDto[];
    notes: AppointmentNoteDto[];
    jobDescriptions: JobDescriptionDto[];
};

const AppointmentDetailsTooltip = ({
    appointmentNumber,
    customerFirstName,
    customerLastName,
    model,
    plates,
    status,
    duration,
    appointmentTime,
    serviceAdvisorName,
    appointmentReasons,
    notes,
    jobDescriptions,
}: PlannedOrderDetailsTooltipProps) => {
    const { t } = useAppTranslation();

    const customerNotes = useMemo(() => {
        const filteredNotes = notes ? notes.filter((x) => x.type === 'ForCustomer') : [];
        return filteredNotes;
    }, [notes]);

    const internalNotes = useMemo(() => {
        const filteredNotes = notes ? notes.filter((x) => x.type === 'ForInternal') : [];
        return filteredNotes;
    }, [notes]);

    return (
        <OverflowContainer>
            <StyledGridContainer container onMouseDown={(e) => e.stopPropagation()}>
                <StyledGridTitle container>
                    <StyledTitle>
                        {makeTitle(
                            t,
                            appointmentNumber,
                            customerFirstName,
                            customerLastName,
                            model,
                            plates
                        )}
                    </StyledTitle>
                </StyledGridTitle>
                {status.length > 0 && (
                    <DetailsRow
                        title={t('workshopPlanner.detailsTooltip.status')}
                        details={status}
                    />
                )}
                {duration && (
                    <DetailsRow
                        title={t('workshopPlanner.detailsTooltip.duration')}
                        details={duration}
                    />
                )}
                <DetailsRow
                    title={t('workshopPlanner.detailsTooltip.appointmentTime')}
                    details={appointmentTime}
                />
                <DetailsRow
                    title={t('workshopPlanner.detailsTooltip.serviceAdvisor')}
                    details={serviceAdvisorName}
                />
                {appointmentReasons.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.appointmentReason')}
                        details={appointmentReasons.map(appointmentReasonToString)}
                    />
                )}
                {jobDescriptions && jobDescriptions.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.jobDescription')}
                        details={(jobDescriptions ?? []).map((x) => x.name)}
                    />
                )}
                {customerNotes.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.visibleToCustomerNotes')}
                        details={customerNotes.map((x) => x.note)}
                    />
                )}
                {internalNotes.length > 0 && (
                    <DetailsList
                        title={t('workshopPlanner.detailsTooltip.internalNotes')}
                        details={internalNotes.map((x) => x.note)}
                    />
                )}
            </StyledGridContainer>
        </OverflowContainer>
    );
};

const makeTitle = (
    t: TFunction,
    appointmentNumber: string,
    firstName: string,
    lastName: string,
    model: string,
    plates: string
) => {
    let title = `${t('workshopPlanner.detailsTooltip.appointmentTitle')} #` + appointmentNumber;

    if (firstName || lastName) {
        title += ' - ' + (firstName ? firstName + ' ' : '') + lastName;
    }

    if (model || plates) {
        title += ' - ' + (model ? model + ', ' : '') + plates;
    }

    return title;
};

export default AppointmentDetailsTooltip;
