import { Fade, Tooltip, TooltipProps, styled, tooltipClasses } from '@mui/material';
import Grid, { GridProps } from '@mui/material/Grid';
import { DetailedAppointmentReasonDto } from 'api/appointments';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

const StyledList = styled('ul')({
    margin: 0,
    maxWidth: 500,
    listStyleType: 'disc',
    paddingLeft: 25,
});

export const StyledGridContainer = styled(({ ...props }: GridProps) => (
    <Grid container {...props} />
))({
    padding: '14px 23px 14px 23px',
    fontFamily: 'Inter',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
});

export const OverflowContainer = styled('div')({
    maxHeight: '100vh',
    overflowY: 'auto',
    overflowX: 'hidden',
    ...scrollbarStyle(),
});

export const StyledGridTitle = styled(({ ...props }: GridProps) => <Grid item {...props} />)({
    fontSize: 18,
    alignItems: 'center',
    gap: 4,
    maxWidth: 500,
    flexWrap: 'nowrap',
});

const StyledGridItemRow = styled(({ ...props }: GridProps) => <Grid item {...props} />)({
    fontSize: 16,
    lineHeight: '24px',
    alignItems: 'center',
    maxWidth: 500,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
});

const StyledGridItemList = styled(({ ...props }: GridProps) => <Grid item {...props} />)({
    fontSize: 16,
    lineHeight: '24px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
});

export const StyledTitle = styled('span')({
    fontWeight: 'bold',
    textAlign: 'left',
    lineHeight: '24px',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
});

type DetailsRowProps = {
    title: string;
    details?: string;
};

export const DetailsRow = ({ title, details }: DetailsRowProps) => {
    return (
        <StyledGridItemRow>
            <StyledTitle>{title + ': '}</StyledTitle>
            {details && <span>{details}</span>}
        </StyledGridItemRow>
    );
};

type DetailsListProps = {
    title: string;
    details: string[];
};

export const DetailsList = ({ title, details }: DetailsListProps) => {
    return (
        <StyledGridItemList>
            <StyledTitle>{title + ': '}</StyledTitle>
            <StyledList>
                {details.map((d, i) => (
                    <li key={`${i}_${d}`}>{d}</li>
                ))}
            </StyledList>
        </StyledGridItemList>
    );
};

export const appointmentReasonToString = (reason: DetailedAppointmentReasonDto) =>
    !!reason.detail ? reason.name + ' - ' + reason.detail : reason.name;

export const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip
        TransitionProps={{ timeout: 100 }}
        TransitionComponent={Fade}
        {...props}
        classes={{ popper: className }}
    />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#5c6477',
        maxWidth: 'fit-content',
    },

    [`& .${tooltipClasses.arrow}::before`]: {
        backgroundColor: '#5c6477',
    },
}));
