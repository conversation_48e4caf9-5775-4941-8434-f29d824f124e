import { styled } from '@mui/material';
import { useSchedulerContext } from 'common/components/Scheduler/context';
import moment from 'moment';

type FakeGridFakeGridProps = {
    vertical: boolean;
};

export default function FakeGrid({ vertical }: FakeGridFakeGridProps) {
    const { interval, dimensions } = useSchedulerContext();
    const from = moment(interval.from);
    const to = moment(interval.to);
    const count = Math.ceil(to.diff(from, 's') / 60 / dimensions.stepLengthInMinutes);

    const elements: React.ReactElement[] = [];

    for (let i = 0; i < count; i++) {
        const element = vertical ? (
            <tr key={i}>
                <td />
            </tr>
        ) : (
            <td key={i} />
        );

        elements.push(element);
    }

    return (
        <FakeGridTable vertical={vertical}>
            <tbody>{vertical ? elements : <tr>{elements}</tr>}</tbody>
        </FakeGridTable>
    );
}

const FakeGridTable = styled('table')<{ vertical: boolean }>(({ theme, vertical }) => ({
    borderCollapse: 'collapse',
    position: 'absolute',
    zIndex: -1,

    ...(vertical
        ? {
              width: '100%',
              left: 0,
              top: '-0.5px', // ok, i've just reused this solution

              tr: {
                  borderBottom: `1px solid ${theme.palette.custom.gray}`,
                  height: 'var(--wps-row-height)',
                  minHeight: 'var(--wps-row-height)',
                  maxHeight: 'var(--wps-row-height)',
                  padding: 0,
              },

              'tr:first-child': {
                  borderTop: 'none',
              },
          }
        : {
              height: '100%',
              left: '-0.5px', // don't ask me, I don't know why, but this helps
              top: 0,

              td: {
                  borderRight: `1px solid ${theme.palette.custom.gray}`,
                  width: 'var(--wps-column-width)',
                  minWidth: 'var(--wps-column-width)',
                  maxWidth: 'var(--wps-column-width)',
                  padding: 0,
              },

              'td:first-child': {
                  borderLeft: 'none',
              },
          }),
}));
