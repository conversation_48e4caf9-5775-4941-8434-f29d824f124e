import { styled } from '@mui/material';
import { Colors } from 'common/styles/Colors';
import { CSSProperties, useMemo } from 'react';
import { isDark } from 'utils/colors';

export type TowerNumberInnerProps = MergeTypes<
    {
        towerNumber?: string | null;
        orderNumber?: string;
        color?: string | null;
        isResponsive?: boolean;
        displayMode?: 'min-content' | 'ellipsis' | 'min-expanded' | 'max-expanded' | 'single-view';
        maximumNumberOfDigits?: number;
    },
    React.HTMLAttributes<HTMLDivElement>
>;

export default function TowerNumberInner({
    towerNumber,
    orderNumber,
    color,
    isResponsive,
    displayMode = 'min-content',
    maximumNumberOfDigits,
    style,
    ...props
}: TowerNumberInnerProps) {
    const textColor = useMemo(
        () => (color && isDark(color, 0.6) ? Colors.White : Colors.Black0),
        [color]
    );

    const number = towerNumber
        ? towerNumber
        : orderNumber
        ? orderNumber.slice(maximumNumberOfDigits ? -maximumNumberOfDigits : -4)
        : undefined;

    if (!number) return null;

    const fontSize =
        displayMode === 'single-view'
            ? '0.9em'
            : !(displayMode === 'min-expanded' || displayMode === 'max-expanded') &&
              number.length >= 4
            ? 10
            : displayMode === 'min-expanded' || displayMode === 'max-expanded'
            ? 14
            : undefined;

    return (
        <DivRoot className={`TowerNumber dm-${displayMode}`}>
            <StyledTower
                textColor={textColor}
                backgroundColor={color}
                className={`dm-${displayMode}`}
                style={{
                    ...style,
                    fontSize,
                }}
                isResponsive={!!isResponsive}
                {...props}
            >
                <StyledTextContainer>
                    {maximumNumberOfDigits && number.length > maximumNumberOfDigits
                        ? number.slice(-maximumNumberOfDigits)
                        : number}
                </StyledTextContainer>
            </StyledTower>
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    position: 'relative',
    height: 24,
    flexShrink: '0',
    maxWidth: 150,

    '&.dm-min-content': {
        minWidth: 30,
    },

    '&.dm-ellipsis': {
        width: 30,
    },

    '&.dm-min-expanded': {
        width: 60,
        height: 30,
    },

    '&.dm-max-expanded': {
        width: 113,
        height: 30,
    },

    '&.dm-single-view': {
        height: '0.9em',
        minWidth: 30,
    },
});

const StyledTower = styled('div')<{
    backgroundColor: string | null | undefined;
    textColor: Colors.White | Colors.Black0;
    isResponsive: boolean;
    style: CSSProperties | undefined;
}>(({ theme, backgroundColor, textColor, isResponsive }) => ({
    height: '100%',
    width: '100%',

    borderRadius: 15,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: backgroundColor || 'var(--neutral3)',
    cursor: 'default',
    color: textColor,
    transition: '.25s background-color, .25s color',
    ...theme.typography.h6Inter,

    '&.dm-min-content': {
        padding: '0px 10px 0px 10px',
    },

    '&.dm-ellipsis': {},

    '&.dm-min-expanded': {},

    '&.dm-max-expanded': {},

    '&.dm-single-view': {
        paddingTop: '0.2em',
        marginTop: '-0.1em',
        lineHeight: '0.9em',
    },

    ...(isResponsive
        ? {
              [theme.breakpoints.up('3xl')]: {
                  width: 56,
                  height: 36.12,
                  fontSize: '18px',
                  lineHeight: '21.09px',
              },
              [theme.breakpoints.up('4xl')]: {
                  width: 72.97,
                  height: 51.17,
                  fontSize: '24px',
                  lineHeight: '28.13px',
              },
              [theme.breakpoints.up('5xl')]: {
                  width: 149,
                  height: 75.82,
                  fontSize: '42px',
                  lineHeight: '49.22px',
              },
          }
        : undefined),
}));

const StyledTextContainer = styled('div')({
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});
