import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useDynamicColor } from '../utils';
import TowerNumberInner, { TowerNumberInnerProps } from './TowerNumberInner';

type TowerNumberProps = TowerColorOptions & Omit<TowerNumberInnerProps, 'color'>;

export default function TowerNumber({
    orderTypeKey,
    userIdOrKey,
    appointmentReasonId,
    appointmentReasonColor,
    ...props
}: TowerNumberProps) {
    const color = useTowerColor({
        appointmentReasonId,
        userIdOrKey,
        orderTypeKey,
        appointmentReasonColor,
    });
    return <TowerNumberInner color={color} {...props} />;
}

export type TowerColorOptions = {
    orderTypeKey: string | null | undefined;
    userIdOrKey: string | number | null | undefined;
    appointmentReasonId?: string | null | undefined;
    appointmentReasonColor?: string | null | undefined;
    colorFieldValue?: string | null | undefined;
};

const selectTowerColorType = createSelector(
    selectSettings,
    (s) => s.repairShopSettings?.showTowerColor
);

function useTowerColor({
    appointmentReasonId,
    orderTypeKey,
    userIdOrKey,
    appointmentReasonColor,
    colorFieldValue,
}: TowerColorOptions): string | null {
    const towerColorType = useAppSelector(selectTowerColorType);
    const isEnterprise = useIsEnterpriseRoute();

    const color = useDynamicColor(
        {
            appointmentReasonId,
            orderTypeKey,
            userIdOrKey,
            appointmentReasonColor,
            colorFieldValue: colorFieldValue,
        },
        towerColorType
    );

    return isEnterprise ? null : color;
}
