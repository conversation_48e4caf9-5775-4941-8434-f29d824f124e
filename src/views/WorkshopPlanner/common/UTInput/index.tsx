import { TextFormField, TextFormFieldProps } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';

type UTInputProps = {
    durationInSeconds: number;
    onChange?: (value: number) => void;
    disabled?: boolean;
    isInvalid?: boolean;
    readonly?: boolean;
    onBlur?: TextFormFieldProps['onBlur'];
};

export default function UTInput({
    durationInSeconds,
    onChange,
    isInvalid,
    disabled,
    readonly,
}: UTInputProps) {
    const utHr = useAppSelector(selectSettings).repairShopSettings?.features.uTsEqualTo ?? 100;
    const { t } = useAppTranslation();
    const value = Math.floor((durationInSeconds / 3600) * utHr);
    const [rawValue, setRawValue] = useState('');
    const formattedValue = `${value} UT's`;
    const [focus, setFocus] = useState(false);

    return (
        <TextFormField
            isInvalid={isInvalid}
            disabled={disabled}
            onFocus={() => {
                setFocus(true);
                setRawValue(value + '');
            }}
            onBlur={(e) => {
                const v = +e.target.value;
                if (!isNaN(v) && onChange) {
                    onChange((v / utHr) * 3600);
                }
                setFocus(false);
            }}
            name="ut"
            onChange={(e) => setRawValue(e.target.value)}
            value={focus ? rawValue : formattedValue}
            label={t('workshopPlanner.utLabel')}
            placeholder="0 UT's"
            readonly={readonly}
        />
    );
}
