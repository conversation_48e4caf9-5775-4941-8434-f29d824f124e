import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined';
import PauseCircleOutlinedIcon from '@mui/icons-material/PauseCircleOutlined';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import { styled } from '@mui/material';
import WpApi from 'api/workshopPlanner';
import { DateTime } from 'luxon';
import { useEffect, useMemo, useState } from 'react';
import theme from 'theme';
import { useServerUtcNow } from '../../../../common/hooks/useServerUtcNow';

type StatusLabelProps = {
    status: WpApi.JobStatus;
    accumulatedDurationInSeconds: number;
    lastStart: number | null;
};

export const StatusLabel = function StatusLabel({
    status,
    accumulatedDurationInSeconds,
    lastStart,
}: StatusLabelProps) {
    const [elapsed, setElapsed] = useState<number>(accumulatedDurationInSeconds);
    const serverUtcNow = useServerUtcNow();

    useEffect(() => {
        let timerId: number | undefined;

        if (status === 'InProgress') {
            const start = !lastStart
                ? DateTime.fromMillis(serverUtcNow())
                : DateTime.fromSeconds(lastStart, { zone: 'utc' });

            const update = () => {
                let secondsSinceStart = DateTime.fromMillis(serverUtcNow()).diff(
                    start,
                    'seconds'
                ).seconds;
                if (secondsSinceStart < 0) secondsSinceStart = 0;
                setElapsed(Math.floor(accumulatedDurationInSeconds + secondsSinceStart));
            };

            update();
            timerId = setInterval(update, 1000);
        } else {
            setElapsed(accumulatedDurationInSeconds);
        }

        return () => {
            if (timerId) clearInterval(timerId);
        };
    }, [status, accumulatedDurationInSeconds, lastStart, serverUtcNow]);

    const statusIcon = useMemo(() => convertJobStatusToIcon(status), [status]);

    return (
        <Root>
            {statusIcon}
            <DurationLabel>{status !== 'NotStarted' && formatTime(elapsed)}</DurationLabel>
        </Root>
    );
};

function formatTime(totalSeconds: number): string {
    const hours = Math.floor(totalSeconds / 3600).toString();
    const minutes = Math.floor((totalSeconds % 3600) / 60)
        .toString()
        .padStart(2, '0');
    const seconds = (totalSeconds % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
}

function convertJobStatusToIcon(status: WpApi.JobStatus) {
    switch (status) {
        case 'NotStarted':
            return <PlayCircleOutlineIcon color={'info'} />;
        case 'InProgress':
            return <></>;
        case 'Paused':
            return <PauseCircleOutlinedIcon color={'info'} />;
        case 'Stopped':
            return <CheckCircleOutlinedIcon color={'info'} />;
    }
}

const Root = styled('div')({
    display: 'flex',
    justifyContent: 'start',
    gap: '5px',
    padding: '5px 0 0 3px',
});

const DurationLabel = styled('div')({
    ...theme.typography.h6Inter,
    color: theme.palette.primary.light,
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    height: '24px',
});
