import { UserListItem } from 'api/users';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Colors } from 'common/styles/Colors';
import { useMemo } from 'react';
import { createSelector } from 'reselect';
import { RootState, useAppSelector } from 'store';
import { selectCustomAppointmentReasons } from 'store/slices/appointmentReasons';
import { selectColorFieldsInSection } from 'store/slices/colorFields/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectOrderTypes } from 'store/slices/orderTypes';
import { selectUsers } from 'store/slices/users';
import { hslHueDistance, hslToString, isDark, parseRgb, toHSL } from 'utils/colors';

const selectBlockColorType = createSelector(selectSettings, (s) => {
    const type = s.repairShopSettings?.showJobBlockColor;
    if (type === 'NoColor') return undefined;
    return type;
});

export function useBlockStyle(options: DynamicColorOptions):
    | {
          color: string | undefined;
          '--text-color': string | undefined;
          '--block-color': string;
          '--blinking-color': string;
          backgroundColor: string;
      }
    | undefined {
    const colorType = useAppSelector(selectBlockColorType);
    const color = useDynamicColor(options, colorType);

    return useMemo(() => {
        if (!color) {
            return undefined;
        }
        const textColor = isDark(color) ? '#fff' : undefined;
        return {
            backgroundColor: color,
            '--block-color': color,
            color: textColor,
            '--text-color': textColor,
            '--blinking-color': getBlinkingColor(color),
        };
    }, [color]);
}

function getBlinkingColor(color: string | null): string {
    if (!color) {
        return Colors.Error_background;
    }

    try {
        const hsl = toHSL(parseRgb(color));
        if (hslHueDistance(0, hsl.h) < 10) {
            return hslToString({ ...hsl, l: hsl.l + (hsl.l > 50 ? -25 : 25) });
        } else {
            const bHSL = toHSL(parseRgb(Colors.Error_background));
            bHSL.l = Math.min(hsl.l * 1.5, 80);
            return hslToString(bHSL);
        }
    } catch {
        return Colors.Error_background;
    }
}

type AnyColorType = 'ByServiceAdvisor' | 'ByOrderType' | 'ByReasonForAppointment';

export function useDynamicColor(
    options: DynamicColorOptions,
    colorType: AnyColorType | undefined
): string | null {
    const isEnterprise = useIsEnterpriseRoute();

    return useAppSelector((r) => {
        // NOTE (MB) enterprise does not support
        if (isEnterprise) return null;

        if (!colorType) return null;

        return selectBlockColor(r, colorType, options);
    });
}

export type DynamicColorOptions = {
    userIdOrKey?: string | number | null;
    orderTypeKey?: string | null;
    appointmentReasonId?: string | null;
    appointmentReasonColor?: string | null;
    colorFieldValue?: string | null;
};

function selectBlockColor(
    state: RootState,
    colorType: AnyColorType | undefined,
    {
        userIdOrKey,
        orderTypeKey,
        appointmentReasonId,
        appointmentReasonColor,
        colorFieldValue,
    }: DynamicColorOptions
): string | null {
    const { colorField } = selectColorFieldsInSection(state, 'WebJobInfo');

    const colorFromCustomField = colorField?.options.find((x) => x.name === colorFieldValue)?.color;

    if (colorFromCustomField) {
        return colorFromCustomField;
    }

    switch (colorType) {
        case 'ByServiceAdvisor':
            if (userIdOrKey) {
                const user = getUser(state, userIdOrKey);
                if (user) {
                    return user.color;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        case 'ByOrderType':
            if (orderTypeKey) {
                const orderTypes = selectOrderTypes(state);
                const orderType = orderTypes.find((x) => x.key === orderTypeKey);
                if (orderType) {
                    return orderType.color;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        case 'ByReasonForAppointment':
            if (appointmentReasonColor) {
                return appointmentReasonColor;
            }

            const reasons = selectCustomAppointmentReasons(state);
            const reason = reasons.find((x) => x.id === appointmentReasonId);
            if (reason) {
                return reason.color;
            } else {
                return null;
            }
        default:
            return null;
    }
}

function getUser(state: RootState, userKey: string | number): UserListItem | undefined {
    const users = selectUsers(state);

    if (typeof userKey === 'string') {
        return users[userKey];
    }

    let user: UserListItem | undefined = undefined;
    for (const key in users) {
        if (users[key].id === userKey) {
            user = users[key];
            break;
        }
    }

    return user;
}
