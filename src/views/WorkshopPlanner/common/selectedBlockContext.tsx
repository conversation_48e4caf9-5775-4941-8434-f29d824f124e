import { createContext, useCallback, useContext } from 'react';
import { Subject, useSubjectSelector } from 'utils/event';

type SelectedBlockAttributes = {
    orderNumber: string | null;
    appointmentNumber: string | null;
};

const SelectedBlockContext = createContext<Subject<SelectedBlockAttributes | null>>(
    new Subject(null)
);

export function useIsBlockSelected(condition: (state: SelectedBlockAttributes) => boolean) {
    const subject = useContext(SelectedBlockContext);

    return useSubjectSelector(subject, (state) => {
        if (!state) return false;
        return condition(state);
    });
}

export function useSelectedBlockAttributesSetter(): (
    value: SelectedBlockAttributes | null
) => void {
    const subject = useContext(SelectedBlockContext);
    return useCallback(
        (value: SelectedBlockAttributes | null) => {
            subject.set(value);
        },
        [subject]
    );
}
