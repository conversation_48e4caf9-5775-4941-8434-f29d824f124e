import { styled } from '@mui/material';
import useResizeObserver from '@react-hook/resize-observer';
import {
    AppointmentNoteDto,
    AppointmentStatus,
    BasicServiceAdvisorDto,
    DetailedAppointmentReasonDto,
    LocalDateObject,
} from 'api/appointments';
import { JobDescriptionDto } from 'api/workshopPlanner';
import clsx from 'clsx';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import { TFunction } from 'i18next';
import moment from 'moment';
import {
    CSSProperties,
    forwardRef,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import AppointmentDetailsTooltip from '../AppointmentDetailsTooltip';
import { StyledTooltip } from '../TooltipComponents';
import { useIsBlockSelected } from '../selectedBlockContext';
import { DynamicColorOptions, useBlockStyle } from '../utils';

export type PlannedOrderBlockProps = MergeTypes<
    {
        appointmentId: string;
        appointmentNumber: string;
        orderNumber: string | null;
        status: AppointmentStatus;
        firstName: string;
        lastName?: string;
        model?: string;
        plates?: string;
        duration: string;
        appointmentTime: LocalDateObject;
        serviceAdvisor: BasicServiceAdvisorDto;
        appointmentReasons: DetailedAppointmentReasonDto[];
        notes: AppointmentNoteDto[];
        selected?: boolean;
        tooltipIsOpened: boolean;
        isDragging: boolean;
        tooltipPosition: 'top' | 'bottom' | 'right' | 'left';
        onClick?: React.HTMLAttributes<HTMLDivElement>['onClick'];
        onDoubleClick?: React.HTMLAttributes<HTMLDivElement>['onClick'];
        onOpenTooltip: () => void;
        onCloseTooltip: () => void;
        isNew?: boolean;
        isChanged?: boolean;
        isStatic?: boolean;
        children?: React.ReactNode;
        planningName?: string;
        availableVerticalSpace?: number;
        disabled?: boolean;
        color: DynamicColorOptions;
        vertical: boolean;
        singleView: boolean;
        height: number;
        jobDescriptions: JobDescriptionDto[];
        isFiltered: boolean;
    },
    React.HTMLAttributes<HTMLDivElement>
>;

export function mapStatus(
    t: TFunction,
    status: AppointmentStatus,
    orderNumber: string | null
): string {
    switch (status) {
        case 'Unconfirmed':
            return 'workshopPlanner.APPOINTMENT_UNCONFIRMED';
        case 'CustomerDidNotArrive':
            return 'workshopPlanner.DID_NOT_ARRIVE';
        case 'Confirmed':
            return 'workshopPlanner.APPOINTMENT_CONFIRMED';
        case 'OrderCreated':
            return `${t('workshopPlanner.ORDER')} #${orderNumber}`;
        default:
            return '';
    }
}

type PlannedOrderBlockBreakpoint = 'sm' | 'md' | 'lg';

function getPlannerOrderBlockBreakpoint(height: number): PlannedOrderBlockBreakpoint {
    if (height <= 60) {
        return 'sm';
    }

    if (height <= 120) {
        return 'md';
    }

    return 'lg';
}

const PlannedOrderBlock = forwardRef(
    (
        {
            appointmentId,
            appointmentNumber,
            orderNumber,
            status,
            firstName,
            lastName,
            model,
            plates,
            duration,
            appointmentTime,
            serviceAdvisor,
            appointmentReasons,
            notes,
            tooltipIsOpened,
            isDragging,
            tooltipPosition,
            onClick,
            onDoubleClick,
            onOpenTooltip,
            onCloseTooltip,
            selected,
            isNew,
            isChanged,
            isStatic,
            children,
            planningName,
            availableVerticalSpace = Infinity,
            disabled = false,
            color,
            vertical,
            singleView,
            height,
            jobDescriptions,
            isFiltered,
            ...props
        }: PlannedOrderBlockProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const { t } = useAppTranslation();
        const mappedStatus = useMemo(
            () => t(mapStatus(t, status, orderNumber)).toUpperCase(),
            [t, status, orderNumber]
        );
        const formattedDate = useMemo(
            () => moment(`${appointmentTime.date} ${appointmentTime.time}`).format('hh:mm a'),
            [appointmentTime]
        );
        const isBlockSelected = useIsBlockSelected(
            (state) =>
                state.appointmentNumber === appointmentNumber ||
                (!!state.orderNumber && state.orderNumber === orderNumber)
        );

        const boundaryNumberOfDigits = 5;
        const maximumNumberOfDigits =
            useAppSelector(selectSettings).repairShopSettings?.features
                .maximumNumberOfDigitsToDisplayForOrderAndAppointment ?? boundaryNumberOfDigits;
        const appointmentNumberModified = appointmentNumber.slice(-maximumNumberOfDigits);

        const style = useBlockStyle(color);

        const canShowMoreThanOneRows = !vertical || height / availableVerticalSpace >= 0.45;

        const [root, setRoot] = useState<HTMLDivElement | null>(null);
        const [hiddenDiv, setHiddenDiv] = useState<HTMLDivElement | null>(null);
        const overflowingRef = useRef(false);
        const fr = useForceRender();

        useResizeObserver(
            root,
            useCallback(() => {
                if (!hiddenDiv) return;

                // NOTE (AK) actually instead of root div, hidden div is using here.
                // So it can both check if there is no place for current text, and it should be just truncated,
                // and check if there is enough place and text should be adjusted.
                const overflowing = hiddenDiv.scrollHeight > hiddenDiv.clientHeight;

                if (overflowing !== overflowingRef.current) {
                    overflowingRef.current = overflowing;
                    fr();
                }
            }, [fr, hiddenDiv])
        );

        useEffect(() => {
            if (!hiddenDiv) return;

            const overflowing = hiddenDiv.scrollHeight > hiddenDiv.clientHeight;

            if (overflowing !== overflowingRef.current) {
                overflowingRef.current = overflowing;
                fr();
            }
        }, [fr, hiddenDiv]);

        return (
            <div ref={ref} {...props}>
                <StyledTooltip
                    placement={tooltipPosition}
                    arrow
                    title={
                        <AppointmentDetailsTooltip
                            appointmentNumber={appointmentNumber}
                            customerFirstName={firstName}
                            customerLastName={lastName ?? ''}
                            model={model ?? ''}
                            plates={plates ?? ''}
                            status={mappedStatus}
                            duration={duration}
                            appointmentTime={formattedDate}
                            serviceAdvisorName={serviceAdvisor.name}
                            appointmentReasons={appointmentReasons}
                            notes={notes}
                            jobDescriptions={jobDescriptions}
                        />
                    }
                    onOpen={() => {
                        onOpenTooltip();
                    }}
                    onClose={() => {
                        onCloseTooltip();
                    }}
                    open={tooltipIsOpened}
                >
                    <DivRoot
                        data-new={isNew ? 'true' : 'false'}
                        adjustText={vertical && !overflowingRef.current}
                        onClick={onClick}
                        onDoubleClick={onDoubleClick}
                        style={
                            {
                                '--pob-available-vertical-space': `${availableVerticalSpace}`,
                                ...style,
                            } as CSSProperties
                        }
                        className={clsx(
                            `PlannedOrderBlock-${getPlannerOrderBlockBreakpoint(
                                availableVerticalSpace
                            )}`,
                            disabled && 'PlannedOrderBlock-disabled',
                            selected && 'PlannedOrderBlock-selected',
                            (isNew || isChanged) && 'PlannedOrderBlock-hasIndicator',
                            isStatic && 'PlannedOrderBlock-static',
                            !tooltipIsOpened &&
                                !isDragging &&
                                isBlockSelected &&
                                'PlannedOrderBlock-highlighted',
                            `PlannedOrderBlock-${vertical ? 'vertical' : 'horizontal'}`,
                            isFiltered && `PlannedOrderBlock-filter-match`
                        )}
                        ref={setRoot}
                    >
                        {vertical && (
                            <DivHiddenText ref={setHiddenDiv}>
                                <InnerText
                                    vertical={vertical}
                                    singleView={singleView}
                                    firstName={firstName}
                                    lastName={lastName}
                                    model={model}
                                    plates={plates}
                                    planningName={planningName}
                                    appointmentNumberModified={appointmentNumberModified}
                                    canShowMoreThanOneRows={canShowMoreThanOneRows}
                                    mappedStatus={mappedStatus}
                                />
                            </DivHiddenText>
                        )}
                        <InnerText
                            vertical={vertical}
                            singleView={singleView}
                            firstName={firstName}
                            lastName={lastName}
                            model={model}
                            plates={plates}
                            planningName={planningName}
                            appointmentNumberModified={appointmentNumberModified}
                            canShowMoreThanOneRows={canShowMoreThanOneRows}
                            mappedStatus={mappedStatus}
                        />
                        {children}
                    </DivRoot>
                </StyledTooltip>
            </div>
        );
    }
);

type InnerTextProps = Pick<
    PlannedOrderBlockProps,
    'vertical' | 'singleView' | 'firstName' | 'lastName' | 'model' | 'plates' | 'planningName'
> & {
    appointmentNumberModified: string;
    canShowMoreThanOneRows: boolean;
    mappedStatus: string;
};

function InnerText({
    vertical,
    singleView,
    firstName,
    lastName,
    model,
    plates,
    planningName,
    appointmentNumberModified,
    canShowMoreThanOneRows,
    mappedStatus,
}: InnerTextProps) {
    return (
        <>
            <DivCalendarIconWrapper vertical={vertical}>
                <CalendarIcon size={15} fill="currentColor" className="CalendarIcon" />
            </DivCalendarIconWrapper>
            <strong>#{appointmentNumberModified}</strong>{' '}
            {!singleView && (
                <>
                    - {firstName} {lastName}
                </>
            )}
            {vertical ? <>{'- '}</> : <br />}
            <span>
                {model} - {plates}
            </span>{' '}
            {canShowMoreThanOneRows && (
                <>
                    <br />
                    <strong>
                        {singleView
                            ? mappedStatus
                            : [mappedStatus, planningName].filter(Boolean).join(' / ')}
                    </strong>
                </>
            )}
        </>
    );
}

export default PlannedOrderBlock;

const DivRoot = styled('div', {
    shouldForwardProp: (prop) => prop !== 'adjustText',
})<{ adjustText: boolean }>(({ theme, adjustText }) => ({
    ...theme.typography.body1,
    padding: '0.33em 0.1667em 0.33em 0.25em', // by default 1em = 12px
    lineHeight: '15px',
    margin: 1,
    whiteSpace: adjustText ? 'normal' : 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',

    borderRadius: 5,
    border: `1px solid ${theme.palette.neutral[4]}`,

    backgroundColor: '#fff',
    transition: '0.17s box-shadow',

    position: 'relative',
    boxSizing: 'border-box',

    '&:hover': {
        boxShadow: '0 3px 5px -2px' + theme.palette.neutral[4],
        backgroundColor: '#E6EEFE',
    },

    '& .CalendarIcon': {
        marginBottom: '-2px',
    },

    '&.PlannedOrderBlock-vertical': {
        width: 'calc(var(--wps-column-width) - 10px)',
        height: '100%',
    },

    '&.PlannedOrderBlock-horizontal': {
        height: 'calc(var(--wps-row-height) - 10px)',
    },

    '&.PlannedOrderBlock-hasIndicator': {
        '&::after': {
            content: '" "',
            display: 'block',
            height: 5,
            width: 5,
            borderRadius: 1000,
            position: 'absolute',
            top: 5,
            right: 5,
            backgroundColor: theme.palette.primary.main,
        },
    },

    '&.PlannedOrderBlock-static': {
        background:
            'repeating-linear-gradient(45deg, #eaeaea, #eaeaea 2px, #ffffff 2px, #ffffff 6px)',
        cursor: 'default',
        boxShadow: 'none !important',
    },

    '&.PlannedOrderBlock-highlighted': {
        boxShadow: `5px 5px 10px 5px #AEC5FE`,
    },

    '&.PlannedOrderBlock-selected': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 1px ${theme.palette.primary.main}`,
        '&:hover': {
            boxShadow: `0 0 0 2px ${theme.palette.primary.main}`,
        },
    },

    '&.PlannedOrderBlock-sm': {
        lineHeight: 'calc(11px * var(--pob-available-vertical-space) / 50)',
        fontSize: 'calc(9px * var(--pob-available-vertical-space) / 50)',
        margin: 0,
        boxShadow: 'none',
        transition: 'border-color 0.17s',

        ':hover': {
            borderColor: 'var(--cm4)',
        },

        '& .CalendarIcon': {
            height: 'calc(12px * var(--pob-available-vertical-space) / 50)',
            width: 'calc(12px * var(--pob-available-vertical-space) / 50)',
        },

        '&.PlannedOrderBlock-vertical': {
            width: 'var(--wps-column-width)',
        },

        '&.PlannedOrderBlock-horizontal': {
            height: 'var(--wps-row-height)',
        },
    },
    '&.PlannedOrderBlock-md': {},

    '&.PlannedOrderBlock-disabled': {
        cursor: 'default',

        ':hover': {
            boxShadow: 'none',
        },
    },

    '&.PlannedOrderBlock-filter-match': {
        outline: `3px solid ${theme.palette.primary.main}`,
        outlineOffset: '-3px',
    },
}));

const DivCalendarIconWrapper = styled('div')<{ vertical: boolean }>({
    display: 'inline-block',
    alignItems: 'center',
});

const DivHiddenText = styled('div')({
    visibility: 'hidden',
    position: 'absolute',
    whiteSpace: 'normal',
    height: '100%',
});
