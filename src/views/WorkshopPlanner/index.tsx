import { Badge, CircularProgress, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import WpApi from 'api/workshopPlanner';
import { PREDEFINED_PLANNINGS } from 'api/workshopPlanner/plannings';
import { OrderJobsInProgressCheckProvider } from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { PhaseSetbackCheckProvider } from 'common/components/ChangePhaseProviders/PhaseSetbackCheck';
import SimpleTabsWithContent from 'common/components/tabs/SimpleTabsWithContent';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useQueryParam from 'common/hooks/useQueryParam';
import { TFunction } from 'i18next';
import isEqual from 'lodash/isEqual';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { loadLatestOrdersThunk } from 'store/slices/wp/orders';
import { loadAllPausedOrdersThunk } from 'store/slices/wp/paused';
import {
    loadPlanningsThunk,
    planning,
    selectDateString,
    selectPlannings,
} from 'store/slices/wp/plannings';
import { fetchShopWpSettingsThunk } from 'store/slices/wp/settings';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import SimpleErrorBoundary from 'utils/errorsHandling/SimpleErrorBoundary';
import { WidePageLayout } from 'views/Components/Page';
import { StickyTabsWrapper } from 'views/Components/StickyTabsWrapper';
import { CustomHeaderContent, useHeaderLoading } from 'views/HeaderBar';
import { EditAbsencePopupProvider } from 'views/absence/EditAbsence/context';
import { ScheduleAbsencePopupProvider } from '../absence/ScheduleAbsence/context';
import WorkshopPlannerAdvisors from './Advisors';
import { ConvertAppointmentModalProvider } from './ConvertOrderModal/context';
import WorkshopPlannerFilter from './Filter';
import { OrderPopupProvider } from './OrderPopup/context';
import { WorkshopPlannerPausedOrders } from './PausedOrders';
import { WorkshopPlannerPhases } from './Phases';
import Planning from './Planning';
import PlanningHeader from './Planning/PlanningHeader';
import { PlanningsTabsProvider } from './PlanningsTabs/context';
import { TechnicianCapacityAlertProvider } from './TechnicianCapacity/TechnicianCapacityAlert';
import TechnicianCapacityIndicator from './TechnicianCapacity/TechnicianCapacityIndicator';
import WorkshopUtilizationIndicator from './TechnicianCapacity/WorkshopUtilizationIndicator';
import { TechnicianCapacityProvider } from './TechnicianCapacity/context';
import {
    usePhases,
    useScheduleFetcher,
    useTabsCounters,
    useWorkshopPlannerAppointmentsFetcher,
} from './helpers';
import { ScheduleAppointmentPopupAltProvider } from './schedule-popups/ScheduleAppointmentWorkAltPopup/context';
import { ScheduleAppointmentPopupProvider } from './schedule-popups/ScheduleAppointmentWorkPopup/context';
import { ScheduleOrderPopupAltProvider } from './schedule-popups/ScheduleOrderWorkAltPopup/context';
import { ScheduleOrderPopupProvider } from './schedule-popups/ScheduleOrderWorkPopup/context';

const selectIndicatorsSettings = createSelector(
    [selectSettings, selectShopWpSettings],
    (globalSettings, shopWpSettings) => ({
        showTechnicianCapacity: globalSettings.repairShopSettings?.features.showTechnicianCapacity,
        showUtilizationMetrics: shopWpSettings?.showWorkshopUtilizationMetric,
        productiveWorkspace: shopWpSettings?.productiveWorkspace ?? 1,
    })
);

export default function WorkshopPlanner() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    useDocumentTitle(t('titles.workshopPlanner'));

    const [queryPlanningId, setQueryPlanningId] = useQueryParam('planning');
    const [_, setAssignedToQueryParam] = useQueryParam('assignedTo');
    const [__, setStartDateQueryParam] = useQueryParam('startDate');

    const { data } = useQuery(['wp', 'display-parameters'], {
        queryFn: () => WpApi.getDisplayParameters(),
        staleTime: 1000,
        cacheTime: Infinity,
    });
    const showUnassignedRow = data?.showUnassignedRow ?? false;

    useEffect(() => {
        dispatch(fetchShopWpSettingsThunk());
        dispatch(loadPlanningsThunk());
        dispatch(loadLatestOrdersThunk());
        dispatch(loadAllPausedOrdersThunk());
    }, [dispatch]);

    const { list: plannings, loaded: planningsLoaded } = useAppSelector(selectPlannings, isEqual);

    const { planningsCounters, phasesOrdersCounter, pausedOrdersCounter, filterApplied } =
        useTabsCounters();

    useHeaderLoading(!planningsLoaded);
    const { showTechnicianCapacity, showUtilizationMetrics, productiveWorkspace } =
        useAppSelector(selectIndicatorsSettings);

    const dateString = useAppSelector(selectDateString);
    const { loadSchedule } = useScheduleFetcher(dateString);
    useWorkshopPlannerAppointmentsFetcher(dateString);
    const { phases, phasesLoaded } = usePhases();

    function getPlanningName(planningName: string, t: TFunction) {
        switch (planningName) {
            case PREDEFINED_PLANNINGS.TECHNICIANS:
                return t('workshopPlanner.technicians.title');
            case PREDEFINED_PLANNINGS.ADVISORS:
                return t('workshopPlanner.advisors');
            default:
                return planningName;
        }
    }

    const [section, setSection] = useState('phases');

    const tabs = useMemo(
        () =>
            planningsLoaded
                ? [
                      ...plannings.map((x) => ({
                          key: `planning-${x.id}`,
                          label: (
                              <TabLabel
                                  tabName={getPlanningName(x.name, t)}
                                  filterCount={planningsCounters[x.id]}
                                  filterApplied={filterApplied}
                              />
                          ),
                          content: (
                              <SimpleErrorBoundary key={x.id}>
                                  {renderPlanning(x, dateString, showUnassignedRow)}
                              </SimpleErrorBoundary>
                          ),
                      })),
                      {
                          key: 'phases',
                          label: (
                              <TabLabel
                                  tabName={t('workshopPlanner.phases.phasesTitle')}
                                  filterCount={phasesOrdersCounter}
                                  filterApplied={filterApplied}
                              />
                          ),
                          content: (
                              <WorkshopPlannerPhases phases={phases} phasesLoaded={phasesLoaded} />
                          ),
                      },
                      {
                          key: 'pausedOrders',
                          label: (
                              <TabLabel
                                  tabName={t('workshopPlanner.pausedOrders.paused')}
                                  filterCount={pausedOrdersCounter}
                                  filterApplied={filterApplied}
                              />
                          ),
                          content: <WorkshopPlannerPausedOrders />,
                      },
                  ]
                : [],
        [
            planningsLoaded,
            plannings,
            dateString,
            showUnassignedRow,
            t,
            phases,
            phasesLoaded,
            planningsCounters,
            phasesOrdersCounter,
            pausedOrdersCounter,
            filterApplied,
        ]
    );

    const sectionsAreInitialized = useRef(false);

    useEffect(() => {
        if (sectionsAreInitialized.current || !planningsLoaded || plannings.length === 0) return;

        if (queryPlanningId && !isNaN(Number(queryPlanningId))) {
            const techniciansPlanning = plannings.find((x) => x.id === Number(queryPlanningId));
            if (techniciansPlanning) {
                setSection(`planning-${techniciansPlanning.id}`);
                sectionsAreInitialized.current = true;
                return;
            }
        }

        setSection(`planning-${plannings[0].id}`);
        sectionsAreInitialized.current = true;
    }, [planningsLoaded, plannings, queryPlanningId]);

    const selectedPlanningIdRef = useRef<number | null>(null);

    useEffect(() => {
        if (section === 'phases' || section === 'pausedOrders') {
            selectedPlanningIdRef.current = null;
        }

        const parsedId = Number(section.slice(9));
        selectedPlanningIdRef.current = isNaN(parsedId) ? null : parsedId;
    }, [section]);

    const handleSetPlanningId = useCallback((newPlanningId: number) => {
        if (newPlanningId !== selectedPlanningIdRef.current) {
            setSection(`planning-${newPlanningId}`);
            return true;
        } else {
            return false;
        }
    }, []);

    const handleTabSelection = (newSection: string) => {
        setQueryPlanningId(null);

        // CMOS-4814 p.1: prevent scrolling when changing the tab (values becomes unrelated to the new planning)
        setAssignedToQueryParam(null);
        setStartDateQueryParam(null);

        setSection(newSection);
    };

    return (
        <WidePageLayout>
            <TechnicianCapacityProvider>
                <CustomHeaderContent>
                    {showTechnicianCapacity && <TechnicianCapacityIndicator />}
                    {showUtilizationMetrics && (
                        <WorkshopUtilizationIndicator productiveWorkspace={productiveWorkspace} />
                    )}
                    <WorkshopPlannerFilter />
                </CustomHeaderContent>

                <TechnicianCapacityAlertProvider>
                    <PlanningsTabsProvider setPlanningId={handleSetPlanningId}>
                        <ScheduleAppointmentPopupAltProvider>
                            <ScheduleAppointmentPopupProvider>
                                <ScheduleOrderPopupAltProvider>
                                    <ScheduleOrderPopupProvider>
                                        <PhaseSetbackCheckProvider>
                                            <OrderJobsInProgressCheckProvider>
                                                <OrderPopupProvider onClosed={loadSchedule}>
                                                    <ConvertAppointmentModalProvider>
                                                        <ScheduleAbsencePopupProvider>
                                                            <EditAbsencePopupProvider>
                                                                {planningsLoaded ? (
                                                                    <SimpleTabsWithContent
                                                                        sx={{
                                                                            height: 46,
                                                                            overflow: 'visible',
                                                                        }}
                                                                        tabs={tabs}
                                                                        selected={section}
                                                                        onTabSelected={
                                                                            handleTabSelection
                                                                        }
                                                                        renderLayout={({
                                                                            tabs,
                                                                            content,
                                                                        }) => (
                                                                            <>
                                                                                <StickyTabsWrapper
                                                                                    alwaysDetached
                                                                                    innerWidth="min(1800px, 90vw)"
                                                                                >
                                                                                    <DivHeader>
                                                                                        {tabs}
                                                                                    </DivHeader>
                                                                                </StickyTabsWrapper>
                                                                                {content}
                                                                            </>
                                                                        )}
                                                                    />
                                                                ) : (
                                                                    <Center>
                                                                        <CircularProgress />
                                                                    </Center>
                                                                )}
                                                            </EditAbsencePopupProvider>
                                                        </ScheduleAbsencePopupProvider>
                                                    </ConvertAppointmentModalProvider>
                                                </OrderPopupProvider>
                                            </OrderJobsInProgressCheckProvider>
                                        </PhaseSetbackCheckProvider>
                                    </ScheduleOrderPopupProvider>
                                </ScheduleOrderPopupAltProvider>
                            </ScheduleAppointmentPopupProvider>
                        </ScheduleAppointmentPopupAltProvider>
                    </PlanningsTabsProvider>
                </TechnicianCapacityAlertProvider>
            </TechnicianCapacityProvider>
        </WidePageLayout>
    );
}

const DivHeader = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr auto',
    height: '100%',
    alignItems: 'center',
});

const Center = styled('div')({
    position: 'absolute',
    inset: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,.5)',
});

type TabLabelProps = {
    tabName: string;
    filterCount: number;
    filterApplied: boolean;
};
function TabLabel({ tabName, filterCount, filterApplied }: TabLabelProps) {
    const StyledTabLabel = styled('div')({
        overflow: 'hidden',
        maxWidth: '360px',
    });

    const StyledBadge = styled(Badge)(({ theme }) => ({
        '& .MuiBadge-badge': {
            padding: '0 4px',
            height: '16px',
            minWidth: '16px',
            right: -8,
            ...theme.typography.h8Inter,
            fontWeight: 700,
        },
    }));

    return (
        <StyledBadge color="primary" badgeContent={filterCount} showZero={filterApplied}>
            <StyledTabLabel>{tabName} </StyledTabLabel>
        </StyledBadge>
    );
}

function renderPlanning(planning: planning.State, dateString: string, showUnassignedRow: boolean) {
    if (planning.readonly) {
        switch (planning.name) {
            case PREDEFINED_PLANNINGS.TECHNICIANS:
                return (
                    <div>
                        <PlanningHeader
                            planningId={planning.id}
                            planningType="Technicians"
                            showLegend
                        />
                        <Planning
                            key={planning.id}
                            showUnassignedRow={showUnassignedRow}
                            dateString={dateString}
                            planningId={planning.id}
                        />
                    </div>
                );
            case PREDEFINED_PLANNINGS.ADVISORS:
                return (
                    <WorkshopPlannerAdvisors
                        key={planning.id}
                        showUnassignedRow={showUnassignedRow}
                        dateString={dateString}
                    />
                );
            default:
                return (
                    <div key={planning.id}>
                        Invalid planning object: <code>{JSON.stringify(planning)}</code>
                    </div>
                );
        }
    }
    return (
        <div>
            <PlanningHeader planningId={planning.id} planningType="Custom" />
            <Planning
                key={planning.id}
                showUnassignedRow={showUnassignedRow}
                planningId={planning.id}
                dateString={dateString}
            />
        </div>
    );
}
