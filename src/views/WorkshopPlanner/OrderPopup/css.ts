import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

const useOrderPopupStyles = makeStyles((theme) => ({
    content: {
        padding: '10px 35px 10px 20px',
        overflow: 'auto',
        ...scrollbarStyle(),
        margin: '0 15px',
        scrollBehavior: 'smooth',
        boxSizing: 'border-box',
    },
    headerContainer: {
        padding: '30px 60px 0 60px',
        borderBottom: `1px solid ${theme.palette.neutral[3]}`,
        height: 'fit-content',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'end',
        borderRadius: '24px 24px 0 0',
    },
    header: {
        height: '1.4em',
        display: 'flex',
        gap: 15,
        color: theme.palette.neutral[8],
    },
    tabs: {
        margin: '10px 0 0 0',
    },
    saveArea: {
        padding: '10px 0px',
        display: 'flex',
        justifyContent: 'space-between',
    },
    root: {
        width: 'clamp(400px, 1200px, 100vw)',
        boxSizing: 'border-box',
        minWidth: 400,
        maxWidth: '100vw',
        height: 800,
        maxHeight: '95vh',
        borderRadius: 24,
        position: 'relative',
        display: 'grid',
        gridTemplateRows: 'auto 1fr',
    },
    closeBtn: {
        position: 'absolute',
        top: 10,
        right: 10,
        color: theme.palette.neutral[7],
    },
    subHeader: {
        ...FontPrimary(HeaderStyles.H12_16px, true, Colors.Black0),
    },
    body: {
        overflowY: 'scroll',
        ...scrollbarStyle(),
        paddingRight: 16,
        maxHeight: 'calc(100% - 181px)',
    },
}));

export default useOrderPopupStyles;
