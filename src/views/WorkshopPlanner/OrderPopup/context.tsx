import useQueryParam from 'common/hooks/useQueryParam';
import React, { createContext, useContext, useMemo } from 'react';
import OrderPopup from '.';

interface IOrderPopupContext {
    open(orderNumber: string): void;
}

const OrderPopupContext = createContext<IOrderPopupContext | null>(null);

export function useOrderPopup() {
    const ctx = useContext(OrderPopupContext);
    if (ctx === null) throw new Error('OrderPopupContext is not available');
    return ctx;
}

export function useOrderPopupOptional() {
    return useContext(OrderPopupContext);
}

type OrderPopupProviderProps = React.PropsWithChildren<{
    onClosed?: () => void;
}>;

export function OrderPopupProvider({ children, onClosed }: OrderPopupProviderProps) {
    const [orderNumber, setOrderNumber] = useQueryParam('op.num');
    const ctx: IOrderPopupContext = useMemo(
        () => ({
            open: (num) => setOrderNumber(num, true),
        }),
        [setOrderNumber]
    );

    return (
        <OrderPopupContext.Provider value={ctx}>
            {orderNumber && (
                <OrderPopup
                    onSaved={() => {
                        setOrderNumber(null, true);
                        if (onClosed) onClosed();
                    }}
                    onClose={() => {
                        setOrderNumber(null, true);
                        if (onClosed) onClosed();
                    }}
                    orderNumber={orderNumber}
                />
            )}
            {children}
        </OrderPopupContext.Provider>
    );
}
