import DateFormField from 'common/components/Inputs/DateFormField';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import moment from 'moment';
import { useCallback, useMemo } from 'react';
import useDateAndTimePickersStyles from './css';

type DateAndTimePickersProps = MergeTypes<
    {
        value: string | null;
        onChange: (value: string) => void;
        label: string;
        disabled: boolean;
    },
    React.HTMLAttributes<HTMLDivElement>
>;

export default function DateAndTimePickers({
    value,
    label,
    onChange,
    disabled,
    ...props
}: DateAndTimePickersProps) {
    const m = useMemo(() => (value ? moment(value) : null), [value]);

    const onTimeChange = useCallback(
        (tuple: [number, number]) => {
            onChange((m?.clone() ?? moment()).hour(tuple[0]).minute(tuple[1]).toISOString());
        },
        [onChange, m]
    );
    const onDateChange = useCallback(
        (date: Date | null) => {
            onChange(
                moment(date)
                    .second(0)
                    .hour(m?.hour() ?? 0)
                    .minute(m?.minute() ?? 0)
                    .toISOString()
            );
        },
        [onChange, m]
    );
    const styles = useDateAndTimePickersStyles();

    return (
        <div {...props}>
            <InputLabel isRequired={false}>{label}</InputLabel>
            <div className={styles.root}>
                <DateFormField
                    name="deliveryDate"
                    variant="grey"
                    value={m?.toDate()}
                    disabled={disabled}
                    onChange={onDateChange}
                />
                <TimeFormField
                    name="deliveryDate"
                    cmosVariant="grey"
                    disabled={disabled}
                    value={m ? [m.hour(), m.minute()] : null}
                    onChange={onTimeChange}
                />
            </div>
        </div>
    );
}
