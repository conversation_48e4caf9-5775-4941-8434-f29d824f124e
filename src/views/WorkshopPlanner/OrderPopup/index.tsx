import { Box, Grid, IconButton, styled, Typography } from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import WpOrdersApi, { WpOrderInfo, WpUpdateOrder } from 'api/workshopPlanner/orders';
import WpScheduleApi from 'api/workshopPlanner/schedule';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { PlayIcon } from 'common/components/Icons/PlayIcon';
import { Modal } from 'common/components/Modal';
import { TabPanel } from 'common/components/tabs';
import SimpleTabs from 'common/components/tabs/SimpleTabs';
import StatefulTabContext from 'common/components/tabs/StatefulTabContext';
import { ReasonForPause } from 'common/constants/ReasonForPause';
import useLockedBody from 'common/hooks/useLockedBody';
import useToasters from 'common/hooks/useToasters';
import useTowerAlert from 'common/hooks/useTowerInUseAlert';
import { Colors } from 'common/styles/Colors';
import { createRef, memo, PropsWithChildren, useCallback, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import { useSelector } from 'react-redux';
import {
    ErrorResponse,
    getErrorResponse,
    hasCode,
    hasSubCode,
    isErrorResponse,
} from 'services/Server';
import { useAppDispatch } from 'store';

import { UserPermissionDto } from 'api/account';
import PhaseSetbackApi from 'api/phaseSetback';
import { ButtonProps } from 'common/components/Button/ButtonProps';
import { useOrderJobsInProgressCheck } from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { usePhaseSetbackCheck } from 'common/components/ChangePhaseProviders/PhaseSetbackCheck';
import OpenTabIcon from 'common/components/Icons/OpenTabIcon';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import Tooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { generatePath } from 'react-router-dom';
import { selectUserPermission } from 'store/slices/user/selectors';
import { wpOrdersActions } from 'store/slices/wp/orders';
import { loadAllPausedOrdersThunk } from 'store/slices/wp/paused';
import { loadScheduleThunk } from 'store/slices/wp/plannings';
import PauseReasonPicker from 'views/Components/PauseReasonPicker';
import PhasePicker from 'views/Components/PhasePicker';
import { useTechnicianCapacityAlert } from '../TechnicianCapacity/TechnicianCapacityAlert';
import { useIsSchedulingAllowed } from '../TechnicianCapacity/helpers';
import TowerNumber from '../common/TowerNumber';
import { useScheduleOrderAltPopup } from '../schedule-popups/ScheduleOrderWorkAltPopup/context';
import OrderInformation from './OrderInformation';
import OrderScheduleTimeline from './OrderScheduleTimeline';
import useOrderPopupStyles from './css';
import useOrderState, { applyUpdate, OrderPopupStateProvider } from './state';

export type OrderPopupProps = {
    orderNumber: string;
    onClose: () => void;
    onSaved: () => void;
};

const queryKey = (num: string) => ['wp', 'order', num];

export default function OrderPopup({ orderNumber, onClose, onSaved }: OrderPopupProps) {
    const { t } = useAppTranslation();
    const userPermission = useSelector(selectUserPermission);
    const styles = useOrderPopupStyles();
    const toasters = useToasters();
    const scheduleOrderBlockPopup = useScheduleOrderAltPopup();
    const [initialPhaseId, setInitialPhaseId] = useState<number>(-1);
    useLockedBody(true);

    // order data query and state
    const { trackedState, state, set, changed, getUpdateRequest, reset } = useOrderState();
    const fieldChanged = trackedState.fieldChanged;

    const { data } = useQuery(queryKey(orderNumber), () => WpOrdersApi.getOrder(orderNumber), {
        onSuccess: (d) => {
            reset(d);
            setInitialPhaseId(d.phase.id);
        },
    });

    const [isTowerInUse, setIsTowerInUse] = useState(false);

    const saveMutation = useSaveOrderMutation(
        orderNumber,
        getUpdateRequest,
        () => setIsTowerInUse(false),
        (err) => {
            if (err.code === 'General.WP.TowerNumberConflict') {
                setIsTowerInUse(true);
                set('tower', '');
            } else if (hasSubCode(err, 'ValidationError', 'InvalidMobileNumberError')) {
                toasters.danger(
                    t('appointments.invalidMobileNumberError'),
                    t('toasters.errorOccurred')
                );
            }
        }
    );
    const pauseOrderMutation = usePauseOrderMutation(orderNumber, state.pauseReason!);
    const resumeOrderMutation = useResumeOrderMutation(orderNumber);

    const save = useCallback(
        async (phaseSetbackReason?: string) => {
            const savePromise = saveMutation.mutateAsync({
                orderKey: data!.key,
                initialPhaseId,
                phaseIdToSave: state.phaseId,
                phaseSetbackReason,
            });

            if (fieldChanged('pauseReason')) {
                if (state.pauseReason) {
                    await pauseOrderMutation.mutateAsync();
                } else {
                    await resumeOrderMutation.mutateAsync();
                }
            }

            await savePromise;
            onSaved();
        },
        [
            saveMutation,
            onSaved,
            pauseOrderMutation,
            resumeOrderMutation,
            state.pauseReason,
            state.phaseId,
            fieldChanged,
            data,
            initialPhaseId,
        ]
    );

    const addJob = useCallback(
        async (phaseSetbackReason?: string) => {
            if (fieldChanged('pauseReason')) {
                throw new Error('Can not add job for paused order');
            }

            await saveMutation.mutateAsync({
                orderKey: data!.key,
                initialPhaseId,
                phaseIdToSave: state.phaseId,
                phaseSetbackReason,
            });

            scheduleOrderBlockPopup.open(orderNumber);
            onClose();
        },
        [
            saveMutation,
            scheduleOrderBlockPopup,
            fieldChanged,
            onClose,
            orderNumber,
            data,
            state.phaseId,
            initialPhaseId,
        ]
    );

    const bottomRef = createRef<HTMLDivElement>();

    const scrollToBottomOfDiv = useCallback(() => {
        bottomRef!.current!.scrollTo!({
            behavior: 'smooth',
        });
        console.debug(bottomRef);
    }, [bottomRef]);

    const extraText = data ? `${data.customer.firstName} ${data.customer.lastName}`.trim() : '';

    const orderDetailUrl = data?.id
        ? generatePath('/dashboard/' + ROUTES.ORDERS_DETAIL, { id: data.id })
        : undefined;

    const canSaveChanges = changed && userPermission.allowEditWorkshopPlanner;

    return (
        <OrderPopupStateProvider value={trackedState}>
            <Modal open classes={{ box: styles.root }} onClose={onClose}>
                <StatefulTabContext initialValue="order">
                    {(setTab, tab) => (
                        <>
                            <div className={styles.headerContainer}>
                                <header>
                                    <Typography variant="h4" className={styles.header}>
                                        {t('workshopPlanner.orderPopup.title', { orderNumber })}
                                        {extraText === '' ? '' : ' – ' + extraText}
                                        <TowerNumber
                                            orderTypeKey={state.orderTypeId}
                                            appointmentReasonColor={state.appointmentReasonColor}
                                            userIdOrKey={state.inChargeId}
                                            towerNumber={state.tower}
                                        />
                                    </Typography>
                                    <IconButton
                                        className={styles.closeBtn}
                                        onClick={onClose}
                                        size="large"
                                    >
                                        <CloseIcon size={30} />
                                    </IconButton>
                                    <div className={styles.tabs}>
                                        <SimpleTabs
                                            onTabSelected={setTab}
                                            selected={tab}
                                            sx={{ gap: 15 }}
                                            tabs={[
                                                {
                                                    key: 'order',
                                                    label: t(
                                                        'workshopPlanner.orderPopup.quickOrderInfo'
                                                    ),
                                                },
                                                {
                                                    key: 'timeline',
                                                    label: t('workshopPlanner.orderPopup.timeline'),
                                                },
                                                {
                                                    key: 'detail',
                                                    label: (
                                                        <OrderDetailTab>
                                                            {t(
                                                                'workshopPlanner.orderPopup.orderDetail'
                                                            )}
                                                            <OpenTabIcon />
                                                        </OrderDetailTab>
                                                    ),
                                                    onTabClick: () => {
                                                        if (orderDetailUrl && !changed) {
                                                            window.open(orderDetailUrl, '_blank');
                                                            setTab('order');
                                                        }
                                                    },
                                                },
                                            ]}
                                        />
                                    </div>
                                </header>
                            </div>

                            <div className={styles.content} ref={bottomRef}>
                                <TabPanel style={{ paddingTop: 5 }} value="order">
                                    {!data && <OrderTabSkeleton />}
                                    {data && (
                                        <Box
                                            display="flex"
                                            flexDirection="column"
                                            gap={2}
                                            alignItems="stretch"
                                        >
                                            <OrderInformation
                                                orderId={data.id}
                                                isTowerInUse={isTowerInUse}
                                                appointment={data?.appointment}
                                            />
                                            <div>
                                                <span className={styles.subHeader}>
                                                    {t('workshopPlanner.orderPopup.phase')}
                                                </span>
                                                <DivWithPaddingTop>
                                                    <PhasePicker
                                                        name="orderType"
                                                        phaseId={state.phaseId}
                                                        disabled={
                                                            !userPermission.allowEditWorkshopPlanner
                                                        }
                                                        onChange={(phaseId) =>
                                                            set('phaseId', phaseId)
                                                        }
                                                        onOpen={() => {
                                                            scrollToBottomOfDiv();
                                                        }}
                                                    />
                                                </DivWithPaddingTop>
                                            </div>
                                            <div>
                                                <span className={styles.subHeader}>
                                                    {t('workshopPlanner.orderPopup.pauseOrder')}
                                                </span>
                                                <DivWithPaddingTop>
                                                    <Tooltip
                                                        content={t(
                                                            'workshopPlanner.orderPopup.orderMustBeAssigned'
                                                        )}
                                                        disabled={state.assignedTo !== null}
                                                    >
                                                        <div>
                                                            <PauseReasonPicker
                                                                reason={state.pauseReason}
                                                                disabled={
                                                                    state.assignedTo === null ||
                                                                    !userPermission.allowEditWorkshopPlanner
                                                                }
                                                                onChange={(pauseReason) => {
                                                                    if (
                                                                        pauseReason === null &&
                                                                        state.isPaused &&
                                                                        state.pauseReason !== null
                                                                    ) {
                                                                        set('isPaused', false);
                                                                    }
                                                                    set('pauseReason', pauseReason);
                                                                }}
                                                                onOpen={() => {
                                                                    scrollToBottomOfDiv();
                                                                }}
                                                            />
                                                        </div>
                                                    </Tooltip>
                                                </DivWithPaddingTop>
                                            </div>
                                            <div className={styles.saveArea}>
                                                <Box sx={{ display: 'flex', gap: 1 }}>
                                                    <Button
                                                        w="md"
                                                        disabled={
                                                            state.pauseReason === null ||
                                                            !userPermission.allowEditWorkshopPlanner
                                                        }
                                                        cmosVariant={'stroke'}
                                                        onClick={() => {
                                                            if (
                                                                state.isPaused &&
                                                                state.pauseReason !== null
                                                            ) {
                                                                set('pauseReason', null);
                                                            }
                                                            set('isPaused', !state.isPaused);
                                                        }}
                                                        label={
                                                            state.isPaused
                                                                ? t('commonLabels.resume')
                                                                : t('commonLabels.pause')
                                                        }
                                                        Icon={state.isPaused ? PlayIcon : PauseIcon}
                                                        iconPosition="right"
                                                    />
                                                    <AddJobButton
                                                        userPermission={userPermission}
                                                        disabled={fieldChanged('pauseReason')}
                                                        initialPhaseId={initialPhaseId}
                                                        phaseIdToSave={state.phaseId}
                                                        onAddJob={addJob}
                                                        onCancel={() =>
                                                            set('phaseId', initialPhaseId)
                                                        }
                                                        showLoader={saveMutation.isLoading}
                                                        orderId={data.key}
                                                        orderNumber={orderNumber}
                                                    >
                                                        {t('workshopPlanner.orderPopup.addJob')}
                                                    </AddJobButton>
                                                </Box>

                                                <Box gap={2} display="flex" flexDirection="row">
                                                    <Button
                                                        w="md"
                                                        color={Colors.Neutral3}
                                                        cmosVariant={'filled'}
                                                        onClick={() => onClose()}
                                                        label={t('commonLabels.cancel')}
                                                    />
                                                    <SaveButton
                                                        initialPhaseId={initialPhaseId}
                                                        phaseIdToSave={state.phaseId}
                                                        disabled={!canSaveChanges}
                                                        showLoader={saveMutation.isLoading}
                                                        w="md"
                                                        cmosVariant={'filled'}
                                                        onSave={save}
                                                        onCancel={() => {
                                                            set('phaseId', initialPhaseId);
                                                        }}
                                                        label={t('commonLabels.save')}
                                                        orderId={data.key}
                                                        orderNumber={orderNumber}
                                                    />
                                                </Box>
                                            </div>
                                        </Box>
                                    )}
                                </TabPanel>
                                <TabPanel style={{ paddingTop: 5 }} value="timeline">
                                    <OrderScheduleTimeline orderNumber={orderNumber} />
                                </TabPanel>
                                <TabPanel style={{ paddingTop: 5 }} value="detail">
                                    <WarningConfirmationPopup
                                        open={canSaveChanges}
                                        title={t(
                                            'workshopPlanner.orderPopup.orderDetailPopup.title'
                                        )}
                                        body={
                                            <Box>
                                                {t(
                                                    'workshopPlanner.orderPopup.orderDetailPopup.body'
                                                )}
                                            </Box>
                                        }
                                        confirm={t(
                                            'workshopPlanner.orderPopup.orderDetailPopup.saveChanges'
                                        )}
                                        onConfirm={() => {
                                            save();
                                            if (orderDetailUrl) {
                                                window.open(orderDetailUrl, '_blank');
                                            }
                                        }}
                                        onClose={() => {
                                            setTab('order');
                                        }}
                                    />
                                </TabPanel>
                            </div>
                        </>
                    )}
                </StatefulTabContext>
                <div id="last-element" />
            </Modal>
        </OrderPopupStateProvider>
    );
}

type AddJobButtonProps = PropsWithChildren<
    {
        orderId: string;
        orderNumber: string;
        userPermission: UserPermissionDto;
        initialPhaseId: number;
        phaseIdToSave: number;
        onAddJob: (phaseSetbackReason?: string) => void;
        onCancel: () => void;
    } & Pick<ButtonProps, 'showLoader' | 'disabled'>
>;

const AddJobButton = ({
    orderId,
    orderNumber,
    userPermission,
    initialPhaseId,
    phaseIdToSave,
    onAddJob,
    onCancel,
    showLoader,
    disabled,
    children,
}: AddJobButtonProps) => {
    const phaseSetbackCheck = usePhaseSetbackCheck();
    const orderJobsInProgressCheck = useOrderJobsInProgressCheck();
    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed();

    const [loading, setLoading] = useState(false);

    return (
        <Button
            sx={{ minWidth: '160px !important' }}
            cmosVariant="stroke"
            disabled={disabled || !userPermission.allowEditWorkshopPlanner || !isSchedulingAllowed}
            showLoader={showLoader || loading}
            onClick={() => {
                if (!userPermission.allowEditWorkshopPlanner) {
                    console.error('cannot open scheduleOrderBlockPopup', { userPermission });
                    return;
                }

                technicianCapacityAlertPopup.checkTechnicianCapacity(() => {
                    if (initialPhaseId !== phaseIdToSave) {
                        setLoading(true);
                        phaseSetbackCheck.checkPhasesIds(
                            { originPhaseId: initialPhaseId, destinationPhaseId: phaseIdToSave },
                            (reason) =>
                                orderJobsInProgressCheck.checkOrderJobsInProgress(
                                    { destinationPhaseId: phaseIdToSave, orderId, orderNumber },
                                    () => {
                                        onAddJob(reason);
                                        setLoading(false);
                                    },
                                    () => {
                                        onCancel();
                                        setLoading(false);
                                    }
                                ),
                            () => {
                                onCancel();
                                setLoading(false);
                            }
                        );
                    } else {
                        onAddJob();
                    }
                });
            }}
        >
            {children}
        </Button>
    );
};

type SaveButtonProps = {
    orderId: string;
    orderNumber: string;
    initialPhaseId: number;
    phaseIdToSave: number;
    onSave: (phaseSetbackReason?: string) => void;
    onCancel: () => void;
} & Omit<ButtonProps, 'onClick'>;

const SaveButton = ({
    orderId,
    orderNumber,
    initialPhaseId,
    phaseIdToSave,
    onSave,
    onCancel,
    showLoader,
    ...props
}: SaveButtonProps) => {
    const [loading, setLoading] = useState(false);
    const phaseSetbackCheck = usePhaseSetbackCheck();
    const orderJobsInProgressCheck = useOrderJobsInProgressCheck();

    return (
        <Button
            {...props}
            showLoader={showLoader || loading}
            onClick={() => {
                if (initialPhaseId !== phaseIdToSave) {
                    setLoading(true);
                    phaseSetbackCheck.checkPhasesIds(
                        { originPhaseId: initialPhaseId, destinationPhaseId: phaseIdToSave },
                        (reason) => {
                            orderJobsInProgressCheck.checkOrderJobsInProgress(
                                { destinationPhaseId: phaseIdToSave, orderId, orderNumber },
                                () => {
                                    onSave(reason);
                                    setLoading(false);
                                },
                                () => {
                                    onCancel();
                                    setLoading(false);
                                }
                            );
                        },
                        () => {
                            onCancel();
                            setLoading(false);
                        }
                    );
                } else {
                    onSave();
                }
            }}
        />
    );
};

const OrderTabSkeleton = memo(
    () => (
        <div>
            <Grid container spacing={4}>
                <Grid item md={9}>
                    <Box display="flex" gap={1.875}>
                        <div>
                            <Skeleton width={60} height={10} />
                            <Skeleton height={25} width={220} />
                        </div>
                        <div>
                            <Skeleton width={70} height={10} />
                            <Skeleton height={25} width={150} />
                        </div>
                    </Box>
                </Grid>
                <Grid item md={3}>
                    <Skeleton height={25} width={200} />
                </Grid>

                <Grid item md={3}>
                    <Skeleton width={100} height={10} />
                    <Skeleton height={25} />
                </Grid>
                <Grid item md={2}>
                    <Skeleton width={100} height={10} />
                    <Skeleton height={25} />
                </Grid>

                <Grid item md={2}>
                    <Skeleton style={{ marginTop: 21 }} height={25} />
                </Grid>

                <Grid item md={2}>
                    <Skeleton style={{ marginTop: 21 }} height={25} />
                </Grid>
                <Grid item md={3}>
                    <Skeleton width={100} height={10} />

                    <Skeleton height={25} />
                </Grid>
                <Grid item md={3}>
                    <Skeleton width={100} height={10} />
                    <Skeleton height={25} />
                </Grid>
                <Grid item md={6}>
                    <Skeleton width={100} height={10} />
                    <Skeleton height={25} />
                </Grid>
                <Grid item md={3}>
                    <Skeleton width={100} height={10} />
                    <Skeleton height={25} />
                </Grid>
            </Grid>
            <div style={{ height: 40 }} />
            <Skeleton style={{ marginTop: 30, marginBottom: 5 }} width={150} height={16} />
            <Skeleton height={30} />
            <Skeleton style={{ marginTop: 30, marginBottom: 5 }} width={150} height={16} />
            <Skeleton height={30} />
        </div>
    ),
    () => false
);

function useSaveOrderMutation(
    orderNumber: string,
    getUpdateRequest: () => WpUpdateOrder,
    onSaved: () => void,
    onError?: (err: ErrorResponse) => void
) {
    const queryClient = useQueryClient();
    const toasters = useToasters();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const { showTowerInUseAlert } = useTowerAlert();

    return useMutation(
        async ({
            orderKey,
            initialPhaseId,
            phaseIdToSave,
            phaseSetbackReason,
        }: {
            orderKey: string;
            initialPhaseId: number;
            phaseIdToSave: number;
            phaseSetbackReason?: string;
        }) => {
            const result = await WpOrdersApi.updateOrder(orderNumber, getUpdateRequest());

            if (phaseSetbackReason) {
                try {
                    await PhaseSetbackApi.savePhaseSetback({
                        orderKey,
                        originPhaseId: initialPhaseId,
                        destinationPhaseId: phaseIdToSave,
                        reason: phaseSetbackReason,
                    });
                } catch {
                    toasters.danger('', t('toasters.errorOccurredWhenSaving'));
                }
            }

            return result;
        },
        {
            onSuccess(result) {
                const key = queryKey(orderNumber);
                const order = queryClient.getQueryData(key) as WpOrderInfo;
                queryClient.setQueryData(key, applyUpdate(result.orderInfo, order));
                dispatch(wpOrdersActions.onUpdated(result.orderInfo));
                onSaved();
            },
            onError: (err) => {
                const response = getErrorResponse(err);

                if (response) {
                    if (hasCode(response, 'General.WP.TowerNumberConflict')) {
                        showTowerInUseAlert(response.meta.takenBy.number);
                        onError && onError(response);
                    } else {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                } else {
                    console.warn('unknown error occurred while updating the entry: ', err);
                }
            },
        }
    );
}

function usePauseOrderMutation(orderNumber: string, pauseReason: ReasonForPause | null) {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();

    return useMutation(
        async () => {
            if (!pauseReason) throw new Error('cannot pause the order, pause reason is not set');
            await WpScheduleApi.pauseOrder(orderNumber, pauseReason);
        },
        {
            onSuccess: () => {
                // sub-optimal but whatever
                dispatch(loadAllPausedOrdersThunk());
                dispatch(loadScheduleThunk({}));
            },
            onError: (err) => {
                if (isErrorResponse(err)) {
                    if (err.code === 'General.WP.TechnicianNotAssigned') {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.technicianNotAssignedError'),
                            t('toasters.errorOccurred')
                        );
                    } else {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                } else {
                    console.warn('unknown error occurred while updating the entry: ', err);
                }
            },
        }
    );
}

function useResumeOrderMutation(orderNumber: string) {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();

    return useMutation(
        async () => {
            return await WpScheduleApi.resumeOrder(orderNumber);
        },
        {
            onSuccess: () => {
                // sub-optimal but whatever
                dispatch(loadAllPausedOrdersThunk());
                dispatch(loadScheduleThunk({}));
            },
            onError: (err) => {
                if (isErrorResponse(err)) {
                    if (err.code === 'General.WP.TechnicianNotAssigned') {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.technicianNotAssignedError'),
                            t('toasters.errorOccurred')
                        );
                    } else if (err.code === 'General.WP.UserScheduleOccupied') {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.canNotResumeNow'),
                            t('toasters.errorOccurred')
                        );
                    } else if (err.code === 'General.WP.StartPointOutsideSchedule') {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.startPointIsOutOfSchedule'),
                            t('toasters.errorOccurred')
                        );
                    } else {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                } else {
                    console.warn('unknown error occurred while updating the entry: ', err);
                }
            },
        }
    );
}

const DivWithPaddingTop = styled('div')({
    paddingTop: 10,
});

const OrderDetailTab = styled('span')({
    display: 'flex',
    alignItems: 'center',
    gap: 13,
});
