import {
    TimelineConnector,
    TimelineContent,
    timelineContentClasses,
    TimelineDot,
    TimelineItem,
    timelineItemClasses,
    TimelineSeparator,
} from '@mui/lab';
import { Box, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import WpOrdersApi from 'api/workshopPlanner/orders';
import ArrowTooltip from 'common/components/Tooltip';
import { STimeline } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import Skeleton from 'react-loading-skeleton';

type OrderScheduleTimelineProps = {
    orderNumber: string;
};

const TimelineIsEmpty = styled('p')(({ theme }) => ({
    color: theme.palette.neutral[7],
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
}));

const TimestampInfo = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
}));

export default function OrderScheduleTimeline({ orderNumber }: OrderScheduleTimelineProps) {
    const { data, isLoading } = useQuery(
        ['wp', 'order', orderNumber, 'schedule-timeline'],
        () => WpOrdersApi.getTimeline(orderNumber),
        {
            staleTime: 5000,
            cacheTime: 120000,
        }
    );
    const { t } = useAppTranslation();
    const timeline = useMemo(() => data ?? [], [data]);

    return (
        <div>
            {isLoading && (
                <>
                    <ItemSkeleton />
                    <ItemSkeleton />
                    <ItemSkeleton />
                    <ItemSkeleton />
                </>
            )}
            {timeline.length === 0 && !isLoading && (
                <TimelineIsEmpty>
                    {t('workshopPlanner.orderPopup.noJobsInTimeline')}
                </TimelineIsEmpty>
            )}
            <STimeline
                sx={{
                    [`& .${timelineItemClasses.missingOppositeContent}::before`]: {
                        display: 'none',
                    },

                    [`& .${timelineContentClasses.root}`]: (theme) => ({
                        ...theme.typography.h5Inter,
                        color: 'var(--neutral8)',
                    }),
                }}
            >
                {timeline.map((block, idx) => {
                    const isLast = idx === timeline.length - 1;

                    return (
                        <TimelineItem key={block.dateTimeStampInfo}>
                            <TimelineSeparator>
                                <TimelineDot color="primary" />
                                {!isLast && <TimelineConnector sx={{ bgcolor: 'var(--cm1)' }} />}
                            </TimelineSeparator>
                            <TimelineContent>
                                <>{block.description}</>
                                <ArrowTooltip content={block.tooltipInfo}>
                                    <span style={{ cursor: 'default' }}>
                                        <TimestampInfo>{block.dateTimeStampInfo}</TimestampInfo>
                                    </span>
                                </ArrowTooltip>
                            </TimelineContent>
                        </TimelineItem>
                    );
                })}
            </STimeline>
        </div>
    );
}

const ItemSkeleton = () => (
    <>
        <Box display="flex" gap={0.5} marginBottom={1}>
            <Skeleton circle width={15} height={15} />
            <Skeleton width={450} height={10} />
        </Box>
    </>
);
