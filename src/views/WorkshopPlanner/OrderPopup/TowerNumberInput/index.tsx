import { TextFormField, TextFormFieldProps } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback } from 'react';

type TowerNumberInputProps = MergeTypes<
    {
        value: string | null;
        isValid?: boolean;
        onChange: (tower: string | null) => void;
    },
    TextFormFieldProps
>;

export default function TowerNumberInput({
    value,
    isValid,
    onChange,
    ...props
}: TowerNumberInputProps) {
    const onChangeCallback = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const newValue = e.target.value.trim();
            onChange(newValue === '' ? null : newValue);
        },
        [onChange]
    );
    const { t } = useAppTranslation();

    return (
        <TextFormField
            name="tower"
            placeholder={t('workshopPlanner.orderPopup.tower')}
            label={t('workshopPlanner.orderPopup.tower')}
            value={value ?? ''}
            isInvalid={!isValid}
            onChange={onChangeCallback}
            {...props}
        />
    );
}
