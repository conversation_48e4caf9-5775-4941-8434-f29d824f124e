import { OrderReasonDto } from 'api/orders';
import { WpOrderInfo, WpOrderUpdated, WpUpdateOrder } from 'api/workshopPlanner/orders';
import { ReasonForPause } from 'common/constants/ReasonForPause';
import useTrackedState, { UseTrackedState } from 'common/hooks/useTrackedState';
import { AdditionalNoteInfoDto } from 'datacontracts/AdditionalNoteDto';
import { createContext, useContext } from 'react';

export type State = {
    firstName: string;
    lastName: string;
    mobile: string;

    brand: string;
    model: string;
    year: string;
    plates: string;

    tower: string | null;
    orderTypeId: string | null;
    orderTypeName: string | null;

    inChargeId: string | null;
    inChargeColor: string | null;
    assignedTo: string | null;
    deliveryDate: string | null;
    notes: AdditionalNoteInfoDto[];

    phaseId: number;

    appointmentReasonColor: string | null;

    pauseReason: ReasonForPause | null;
    isPaused: boolean;
    orderReasons: OrderReasonDto[];
};

export type StateUpdate = {
    type: 'update';
    state: Partial<State>;
};

function getDefaultState(): State {
    return {
        firstName: '',
        lastName: '',
        mobile: '',

        brand: '',
        model: '',
        year: '',
        plates: '',

        tower: null,
        orderTypeId: null,
        orderTypeName: null,

        inChargeId: null,
        inChargeColor: null,
        assignedTo: null,
        deliveryDate: '',
        notes: [],

        phaseId: -1,

        appointmentReasonColor: null,

        pauseReason: null,
        isPaused: false,
        orderReasons: [],
    };
}

export function getStateFromOrderInfo(info?: WpOrderInfo): State {
    if (!info) return getDefaultState();

    return {
        firstName: info.customer.firstName,
        lastName: info.customer.lastName,
        mobile: info.customer.mobile,

        brand: info.vehicle.brand,
        model: info.vehicle.model,
        year: info.vehicle.year,
        plates: info.vehicle.plates,

        tower: info.tower,
        orderTypeId: info.orderType?.key ?? null,
        orderTypeName: info.orderType?.name ?? null,

        inChargeId: info.inCharge?.id ?? null,
        inChargeColor: info.inCharge?.color ?? null,
        assignedTo: info.assignedTo?.id ?? null,
        deliveryDate: info.deliveryDate ?? '',
        notes: info.notes,

        phaseId: info.phase.id,

        appointmentReasonColor: info.appointmentReasonColor,

        pauseReason: info.reasonForPause,
        isPaused: info.pauseDate !== null,
        orderReasons: info.orderReasons,
    };
}

export default function useOrderPopupStateSource() {
    const trackedState = useTrackedState<State>(getDefaultState());

    const { state, originalState, update, setOriginalState, set, changed, getChanges } =
        trackedState;

    return {
        trackedState,
        changed,
        originalState,
        set,
        update,
        state,
        reset: (order?: WpOrderInfo) => {
            setOriginalState(getStateFromOrderInfo(order));
        },
        getUpdateRequest(): WpUpdateOrder {
            const { tower, assignedTo, inChargeId, orderTypeId, phaseId, ...changes } =
                getChanges();
            const request: WpUpdateOrder = {
                inChargeUserId: inChargeId,
                assignedToUserId: assignedTo,
                tower,
                orderTypeId,
                deliveryDate: changes.deliveryDate ? changes.deliveryDate : undefined,
                phaseId,
            };

            if (changes.firstName || changes.lastName || changes.mobile) {
                request.customer = {
                    firstName: changes.firstName,
                    lastName: changes.lastName,
                    mobile: changes.mobile,
                };
            }

            if (changes.brand || changes.model || changes.year || changes.plates) {
                request.vehicle = {
                    brand: changes.brand,
                    model: changes.model,
                    year: changes.year,
                    plates: changes.plates,
                };
            }

            return request;
        },
    };
}

const OrderPopupTrackedStateContext = createContext<UseTrackedState<State> | null>(null);

export const OrderPopupStateProvider = OrderPopupTrackedStateContext.Provider;

export function useOrderPopupState() {
    const state = useContext(OrderPopupTrackedStateContext);
    if (!state) throw new Error('OrderPopupTrackedStateContext is not available');
    return state;
}

export function applyUpdate(
    {
        tower,
        inCharge,
        assignedTo,
        vehicle,
        customer,
        orderType,
        deliveryDate,
        reasonForPause,
        pauseDate,
        phase,
    }: WpOrderUpdated,
    order: WpOrderInfo
): WpOrderInfo {
    return {
        ...order,
        tower,
        inCharge,
        assignedTo,
        vehicle,
        customer,
        orderType,
        deliveryDate,
        reasonForPause,
        pauseDate,
        phase,
    };
}
