import { Box, Divider, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import AdditionalNotesAPI from 'api/AdditionalNotes';
import { OrderReasonDto } from 'api/orders/_common';
import { AppointmentInfo } from 'api/workshopPlanner/orders';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { phoneFormatRegexMask } from 'common/FormatersHelper';
import { TextField } from 'common/components/Inputs';
import MaskedTextFormField from 'common/components/Inputs/MaskedTextFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AdditionalNoteDto } from 'datacontracts/AdditionalNoteDto';
import { UserBaseDto } from 'datacontracts/UserBaseDto';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import BrandPicker from 'views/Components/BrandPicker';
import ModelPicker from 'views/Components/ModelPicker';
import OrderTypePicker from 'views/Components/OrderTypePicker';
import TeamMemberPicker from 'views/Components/TeamMemberPicker';
import YearPicker from 'views/Components/YearPicker';
import OrderReasons from 'views/OrderDetail/OrderDetailsSection/OrderInfoSection/OrderNotes/OrderReasons';
import DateAndTimePickers from '../DateAndTimePickers';
import TowerNumberInput from '../TowerNumberInput';
import { useOrderPopupState } from '../state';
import AdditionalNotes from './AdditionalNotes';
import useOrderInformationStyles from './css';

type OrderInformationProps = {
    orderId: number;
    appointment?: AppointmentInfo | null;
    isTowerInUse?: boolean;
};

const CssGrid = styled('section')({
    display: 'grid',
    gridTemplateColumns: '1fr 2fr 1fr',
    gap: '10px 47px',
    gridTemplateAreas: `
        "topLeft topLeft deliveryDate" 
        "customer vehicle users" 
        "customer vehicle users"
        "notes notes notes"`,
});

export default function OrderInformation({
    orderId,
    appointment,
    isTowerInUse,
}: OrderInformationProps) {
    const { t } = useAppTranslation();
    const { state, update, set } = useOrderPopupState();
    const userPermission = useAppSelector(selectUserPermission);
    const styles = useOrderInformationStyles();
    const [additionalNotes, setAdditionalNotes] = useState<AdditionalNoteDto[]>([]);
    const [internalOrderReasons, setInternalOrderReasons] = useState<OrderReasonDto[]>([]);
    const [customerOrderReasons, setCustomerOrderReasons] = useState<OrderReasonDto[]>([]);

    const handleAssignedToChange = useCallback(
        (user: UserBaseDto | null) => {
            set('assignedTo', user?.userKey ?? null);
        },
        [set]
    );

    const { data, refetch } = useQuery(['order', orderId, 'additionalNotes'], () =>
        AdditionalNotesAPI.getNotes(orderId)
    );

    useEffect(() => {
        if (data) {
            setAdditionalNotes(data);
        }
    }, [data]);

    const handleAdditionalNotesChange = (notes: AdditionalNoteDto[]) => {
        setAdditionalNotes(notes);
        refetch();
    };

    useMemo(() => {
        const internalReasons: OrderReasonDto[] = [];
        const customerReasons: OrderReasonDto[] = [];

        if (state.orderReasons.length > 0) {
            state.orderReasons.map((r) => {
                if (r.isFromCustomer) customerReasons.push(r);
                else internalReasons.push(r);
            });
            setInternalOrderReasons(internalReasons);
            setCustomerOrderReasons(customerReasons);
        }
    }, [state.orderReasons]);

    return (
        <>
            <CssGrid>
                <div style={{ gridArea: 'topLeft' }}>
                    {!!appointment && (
                        <div className={styles.appointmentInfo}>
                            <Box
                                sx={{
                                    whiteSpace: 'nowrap',
                                    marginBottom: '10px',
                                    color: 'var(--neutral8)',
                                }}
                            >
                                <b>{t('workshopPlanner.orderPopup.appointment')}</b>:{' '}
                                {appointment.startDate
                                    ? moment(
                                          `${appointment.startDate.date} ${appointment.startDate.time}`
                                      ).format('hh:mm a')
                                    : '--'}
                            </Box>
                        </div>
                    )}
                    <Box display="flex" gap={1.25}>
                        <OrderTypePicker
                            fullWidth={false}
                            placeholder={t('workshopPlanner.orderPopup.orderType')}
                            label={t('workshopPlanner.orderPopup.orderType')}
                            name="orderType"
                            disabled={!userPermission.allowEditWorkshopPlanner}
                            orderTypeId={state.orderTypeId}
                            onChange={(ot) => {
                                set('orderTypeId', ot?.key ?? null);
                                set('orderTypeName', ot?.name ?? null);
                            }}
                        />
                        <TowerNumberInput
                            fullWidth={false}
                            cmosVariant="grey"
                            value={state.tower}
                            disabled={!userPermission.allowEditWorkshopPlanner}
                            isValid={!isTowerInUse}
                            onChange={(v) => set('tower', v)}
                        />
                    </Box>
                </div>
                <Box
                    sx={{
                        marginTop: '25px',
                        width: '277px',
                        display: 'flex',
                        flexDirection: 'row-reverse',
                    }}
                >
                    <DateAndTimePickers
                        style={{ gridArea: 'deliveryDate', marginLeft: '-22px' }}
                        label={t('workshopPlanner.orderPopup.deliveryDate')}
                        disabled={!userPermission.allowEditWorkshopPlanner}
                        onChange={(value) => set('deliveryDate', value)}
                        value={state.deliveryDate ?? null}
                    />
                </Box>
                <CustomerInfo />
                <VehicleInfo />
                <Box
                    gridArea="users"
                    display="flex"
                    gap={'10px'}
                    alignItems="stretch"
                    flexDirection="column"
                >
                    <TeamMemberPicker
                        userId={state.inChargeId}
                        disabled={!userPermission.allowEditWorkshopPlanner}
                        label={t('orderDetails.inCharge')}
                        placeholder={t('orderDetails.inCharge')}
                        onChange={(u) =>
                            update({
                                inChargeColor: u?.color ?? null,
                                inChargeId: u?.userKey ?? null,
                            })
                        }
                        name="inChargeUser"
                    />

                    <TeamMemberPicker
                        userId={state.assignedTo}
                        isRequired
                        disabled={!userPermission.allowEditWorkshopPlanner}
                        label={t('commonLabels.assignedTo')}
                        placeholder={t('commonLabels.assignedTo')}
                        onChange={handleAssignedToChange}
                        name="assignedToUser"
                    />
                </Box>
            </CssGrid>
            <Divider />

            {customerOrderReasons && orderId && (
                <OrderReasons reasons={customerOrderReasons} orderId={orderId} isCustomer={true} />
            )}
            {internalOrderReasons && orderId && (
                <OrderReasons
                    reasons={internalOrderReasons}
                    orderId={orderId}
                    onChange={(reasons: OrderReasonDto[]) => {
                        setInternalOrderReasons(reasons);
                    }}
                    isCustomer={false}
                />
            )}

            <Divider />
            <DivAdditionalNotesContainer>
                <AdditionalNotes
                    orderId={orderId}
                    notesType="ForCustomer"
                    notes={additionalNotes}
                    disabled={!userPermission.allowEditWorkshopPlanner}
                    onChange={handleAdditionalNotesChange}
                />
                <AdditionalNotes
                    orderId={orderId}
                    notesType="ForInternal"
                    notes={additionalNotes}
                    disabled={!userPermission.allowEditWorkshopPlanner}
                    onChange={handleAdditionalNotesChange}
                />
            </DivAdditionalNotesContainer>
            <Divider />
        </>
    );
}

function VehicleInfo() {
    const userPermissions = useAppSelector(selectUserPermission);
    const disabled =
        !userPermissions.allowEditWorkshopPlanner || !userPermissions.allowEditVehicles;
    const { state, set, update } = useOrderPopupState();
    const { t } = useAppTranslation();
    const styles = useOrderInformationStyles();

    return (
        <Box
            gridArea="vehicle"
            display="flex"
            alignItems="stretch"
            gap={'10px'}
            flexDirection="column"
        >
            <div className={styles.customer}>
                <BrandPicker
                    cmosVariant="grey"
                    onChange={(v) => update({ brand: v ?? '', model: '', year: '' })}
                    placeholder={t('commonLabels.brand')}
                    label={t('commonLabels.vehicle')}
                    name="brand"
                    disabled={disabled}
                    value={state.brand}
                />
                <ModelPicker
                    cmosVariant="grey"
                    placeholder={t('commonLabels.model')}
                    name="model"
                    brandName={state.brand}
                    disabled={disabled}
                    value={state.model}
                    onChange={(v) => update({ model: v ?? '', year: '' })}
                />
                <YearPicker
                    cmosVariant="grey"
                    placeholder={t('commonLabels.year')}
                    name="year"
                    brandName={state.brand}
                    modelName={state.model}
                    disabled={disabled}
                    value={state.year}
                    onChange={(v) => set('year', v ?? '')}
                />
            </div>
            <TextField
                cmosVariant="grey"
                name="plates"
                disabled={disabled}
                label={t('workshopPlanner.orderPopup.plates')}
                value={state.plates}
                onChange={(v) => set('plates', v.target.value.toUpperCase())}
                placeholder={t('orderDetails.platesPlaceholder')}
            />
        </Box>
    );
}

function CustomerInfo() {
    const userPermissions = useAppSelector(selectUserPermission);
    const disabled =
        !userPermissions.allowEditWorkshopPlanner || !userPermissions.allowEditCustomers;
    const { state, set } = useOrderPopupState();

    const { t } = useAppTranslation();
    const styles = useOrderInformationStyles();
    const { internationalization } = useAppSelector(selectSettings);
    const maxLengthPhone = InternationalizationLogic.maxLengthPhone(internationalization);

    return (
        <Box
            gridArea="customer"
            display="flex"
            alignItems="stretch"
            gap={'10px'}
            flexDirection="column"
        >
            <Box className={styles.flexGrow1} display="flex" alignItems="end" gap={0.75}>
                <TextField
                    name="firstName"
                    disabled={disabled}
                    cmosVariant="grey"
                    placeholder={t('orderDetails.firstNamePlaceholder')}
                    label={t('workshopPlanner.orderPopup.customerName')}
                    value={state.firstName}
                    onChange={(e) => set('firstName', e.target.value)}
                />
                <TextField
                    name="lastName"
                    cmosVariant="grey"
                    disabled={disabled}
                    value={state.lastName}
                    onChange={(e) => set('lastName', e.target.value)}
                    placeholder={t('orderDetails.lastNamePlaceholder')}
                />
            </Box>
            <MaskedTextFormField
                name="mobile"
                cmosVariant="grey"
                maxLength={maxLengthPhone}
                disabled={disabled}
                mask={phoneFormatRegexMask(internationalization.phoneNumberFormat)}
                label={t('workshopPlanner.orderPopup.mobile')}
                value={state.mobile}
                onChange={(e) => {
                    set('mobile', e.target.value.replace(/[^\d]/g, ''));
                }}
                placeholder={t('orderDetails.mobilePlaceholder')}
            />
        </Box>
    );
}

const DivAdditionalNotesContainer = styled('div')({
    marginTop: '16px',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
});
