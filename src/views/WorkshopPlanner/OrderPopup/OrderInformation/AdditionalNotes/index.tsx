import { styled, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { useMutation } from '@tanstack/react-query';
import AdditionalNotesAPI from 'api/AdditionalNotes';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AdditionalNoteDto, AdditionalNoteType } from 'datacontracts/AdditionalNoteDto';
import { useState } from 'react';
import AdditionalNoteForm from 'views/OrderDetail/common/AdditionalNotesForm';
import Note from 'views/OrderDetail/common/AdditionalNotesForm/Note';

type AdditionalNoteFormProps = {
    orderId: number;
    notesType: AdditionalNoteType;
    notes: AdditionalNoteDto[];
    disabled: boolean;
    onChange: (notes: AdditionalNoteDto[]) => void;
};

const AdditionalNotes = ({
    notes = [],
    orderId,
    notesType,
    disabled,
    onChange,
}: AdditionalNoteFormProps) => {
    const { t } = useAppTranslation();
    const [noteId, setNoteId] = useState<number>();

    const { addNoteMutate } = useAddNoteMutation((data) => {
        onChange && onChange([...notes, data]);
    });

    const { updateNoteMutate } = useUpdateNoteMutation((data) => {
        onChange &&
            onChange(notes.map((x) => (x.additionalNoteId === data.additionalNoteId ? data : x)));
    });

    const { deleteNoteMutate } = useDeleteNoteMutation(() => {
        onChange && onChange(notes.filter((x) => x.additionalNoteId !== noteId));
        setNoteId(undefined);
    });

    const handleSave = (text: string) => {
        addNoteMutate({ text, orderId, noteType: notesType });
    };

    const handleEdit = (note: AdditionalNoteDto) => {
        updateNoteMutate({
            text: note.text,
            additionalNoteId: note.additionalNoteId,
        });
    };

    const handleDelete = (note: AdditionalNoteDto) => {
        setNoteId(note.additionalNoteId);
        deleteNoteMutate({ additionalNoteId: note.additionalNoteId });
    };

    const filteredNotes = notes.filter((x) => x.type === notesType);

    return (
        <Box component="div">
            <TypographyLabel>
                {notesType === 'ForCustomer'
                    ? t('orderDetails.additionalNotes.notesForCustomer')
                    : t('orderDetails.additionalNotes.notesForInternal')}
            </TypographyLabel>
            {filteredNotes.map((note, index) => (
                <BoxNoteWrapper key={`${note.additionalNoteId}-${index}`} component="div">
                    •
                    <BoxNoteWrapper key={`${note.additionalNoteId}-${index}`} component="div">
                        <Note
                            additionalNote={note}
                            editSection="WorkshopPlanner"
                            disabled={disabled}
                            onEdit={handleEdit}
                            onDelete={handleDelete}
                        />
                    </BoxNoteWrapper>
                </BoxNoteWrapper>
            ))}
            <BoxAdditionalNoteFormWrapper>
                <AdditionalNoteForm onClickSave={handleSave} disabled={disabled} />
            </BoxAdditionalNoteFormWrapper>
        </Box>
    );
};

const useAddNoteMutation = (onSuccess?: (data: AdditionalNoteDto) => void) => {
    const { mutate: addNoteMutate } = useMutation(
        (body: { text: string; orderId: number; noteType: AdditionalNoteType }) =>
            AdditionalNotesAPI.addNote(body.orderId, body.text, body.noteType, 'WorkshopPlanner'),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { addNoteMutate };
};

const useUpdateNoteMutation = (onSuccess?: (data: AdditionalNoteDto) => void) => {
    const { mutate: updateNoteMutate } = useMutation(
        (body: { text: string; additionalNoteId: number }) =>
            AdditionalNotesAPI.updateNote(body.additionalNoteId, body.text),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { updateNoteMutate };
};

const useDeleteNoteMutation = (onSuccess?: () => void) => {
    const { mutate: deleteNoteMutate } = useMutation(
        (body: { additionalNoteId: number }) =>
            AdditionalNotesAPI.deleteNote(body.additionalNoteId),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );
    return { deleteNoteMutate };
};

const TypographyLabel = styled(Typography)(({ theme }) => ({
    fontFamily: 'Inter',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: 700,
    lineHeight: 'normal',
    color: theme.palette.neutral[8],
    marginBottom: '5px',
}));

const BoxNoteWrapper = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    paddingLeft: '8px',
});

const BoxAdditionalNoteFormWrapper = styled(Box)({
    paddingTop: '3px',
});

export default AdditionalNotes;
