import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

const useOrderInformationStyles = makeStyles((theme) => ({
    content: {
        padding: '50px 60px',
        overflow: 'hidden',
    },
    header: {
        height: '1.2em',
        display: 'flex',
        gap: 15,
    },
    saveArea: {
        padding: '10px 0px',
        display: 'flex',
        justifyContent: 'space-between',
    },
    root: {
        width: 'clamp(400px, 1200px, 100vw)',
        boxSizing: 'border-box',
        minWidth: 400,
        maxWidth: '100vw',
        height: '80vh',
        display: 'grid',
        gridTemplateRows: '1fr auto',
        borderRadius: 0,
    },
    appointmentInfo: {
        display: 'flex',
        gap: 15,
        ...theme.typography.h6Roboto,
        fontWeight: 'initial',
    },
    flexGrow1: {
        '& *': {
            flexGrow: 1,
        },
    },
    customer: {
        display: 'grid',
        gridTemplateColumns: 'repeat(3, minmax(0, 1fr))',
        alignItems: 'end',
        gap: 10,
    },
    subHeader: {
        ...FontPrimary(HeaderStyles.H12_16px, true, Colors.Black0),
    },
    body: {
        overflowY: 'scroll',
        ...scrollbarStyle(),
        paddingRight: 16,
        maxHeight: 'calc(100% - 181px)',
    },
}));

export default useOrderInformationStyles;
