import { Box, TableCell, TableRow, styled, useTheme } from '@mui/material';
import WpOrdersApi from 'api/workshopPlanner/orders';
import ShopWPSettingsApi from 'api/workshopPlanner/shopWPSettings';
import { Phases } from 'common/constants';
import { ReasonForPause } from 'common/constants/ReasonForPause';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import React, { useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { PausedOrderView } from 'store/slices/wp/paused/selectors';
import PauseReasonPicker from 'views/Components/PauseReasonPicker';
import TowerNumber from 'views/WorkshopPlanner/common/TowerNumber';

export type PausedOrdersTableRowProps = {
    order: PausedOrderView;
    settings: ShopWPSettingsApi.ShopWpSettingsDto;
    requestOrderPopup: (orderNumber: string) => void;
};

export function PausedOrdersTableRow({
    order,
    settings,
    requestOrderPopup,
}: PausedOrdersTableRowProps) {
    const theme = useTheme();
    const { t } = useAppTranslation();

    const boundaryNumberOfDigits = 5;
    const maximumNumberOfDigits =
        useAppSelector(selectSettings).repairShopSettings?.features
            .maximumNumberOfDigitsToDisplayForTowerNumber ?? boundaryNumberOfDigits;

    const now = moment();
    const pausedDays = Math.round(now.diff(order.pausedAt, 'd', true));
    const deliveryDateIsExpired =
        settings.highlightExpiredDeliveryDateOrders && moment(order.promisedAt).isBefore(now);
    const preventivePausedDaysIsExpired =
        settings.highlightExpiredPausedDaysOrders &&
        pausedDays >= settings.preventiveExpiredPausedDays;
    const urgentPausedDaysIsExpired =
        settings.highlightExpiredPausedDaysOrders && pausedDays >= settings.urgentExpiredPausedDays;

    const expiredDeliveryDateColor = 'rgba(241, 88, 87, 0.2)';
    const expiredPreventivePausedDaysColor = 'rgba(255, 198, 38, 0.2)';
    const expiredUrgentPausedDaysColor = 'rgba(250, 150, 107, 0.2)';

    const blinkerColor1 = deliveryDateIsExpired
        ? expiredDeliveryDateColor
        : urgentPausedDaysIsExpired
        ? expiredUrgentPausedDaysColor
        : preventivePausedDaysIsExpired
        ? expiredPreventivePausedDaysColor
        : theme.palette.neutral[1];

    const blinkerColor2 = urgentPausedDaysIsExpired
        ? expiredUrgentPausedDaysColor
        : preventivePausedDaysIsExpired
        ? expiredPreventivePausedDaysColor
        : deliveryDateIsExpired
        ? expiredDeliveryDateColor
        : theme.palette.neutral[1];

    const { callApi } = useApiCall();
    const [reasonForPauseState, setReasonForPauseState] = useState<ReasonForPause | null>(
        order.pauseReason
    );

    const renderDaysOnPause = (pausedDays: number) => {
        return `${pausedDays} ${
            pausedDays === 1
                ? t('workshopPlanner.pausedOrders.day')
                : t('workshopPlanner.pausedOrders.days')
        }`;
    };

    const updateReasonForPause = async (reasonForPause: ReasonForPause) => {
        await callApi(
            async () => WpOrdersApi.updateOrder(order.number, { reasonForPause: reasonForPause }),
            {
                selectErrorContent: () => {
                    setReasonForPauseState(reasonForPauseState);
                    return { body: t('toasters.errorOccurredWhenSaving') };
                },
            }
        );

        setReasonForPauseState(reasonForPause);
    };

    return (
        <StyledTableRow
            className={
                deliveryDateIsExpired || preventivePausedDaysIsExpired || urgentPausedDaysIsExpired
                    ? 'blink'
                    : undefined
            }
            style={
                {
                    '--blinker-color-1': blinkerColor1,
                    '--blinker-color-2': blinkerColor2,
                } as React.CSSProperties
            }
            onClick={() => requestOrderPopup(order.number)}
        >
            <StyledTowerNumberCell component="td" scope="row">
                <Box display={'flex'} alignItems={'center'} justifyContent={'center'}>
                    <TowerNumber
                        orderTypeKey={order.type?.key}
                        userIdOrKey={order.inCharge?.id}
                        appointmentReasonColor={order.appointmentReasonColor}
                        towerNumber={order.tower}
                        orderNumber={order.number}
                        displayMode={
                            maximumNumberOfDigits <= boundaryNumberOfDigits
                                ? 'min-expanded'
                                : 'max-expanded'
                        }
                        maximumNumberOfDigits={maximumNumberOfDigits}
                    />
                </Box>
            </StyledTowerNumberCell>
            <StyledTableCell component="td" scope="row">
                {`#${order.number}`}
            </StyledTableCell>
            <StyledTableCell component="td" scope="row">
                {order.phase.id === Phases.NoPhase || order.phase.id === Phases.Closed
                    ? t(`workshopPlanner.phases.${order.phase.name}`)
                    : order.phase.name}
            </StyledTableCell>
            <StyledTableCell component="td" scope="row">
                {order.assignedTo?.name ?? '--'}
            </StyledTableCell>
            <StyledTableCell
                sx={{ paddingLeft: '25px' }}
                component="td"
                scope="row"
                onClick={(e) => e.stopPropagation()}
            >
                <Box sx={{ width: '70%' }}>
                    <PauseReasonPicker
                        cmosVariant="transparent"
                        name="reasonForPause"
                        reason={reasonForPauseState}
                        onChange={(e: ReasonForPause | null) => {
                            if (e !== null) {
                                updateReasonForPause(e);
                            }
                        }}
                        styles={{
                            control: {
                                '& > :first-child': {
                                    paddingLeft: '10px',

                                    '& > :first-child': {
                                        ...theme.typography.h5Roboto,
                                        color: theme.palette.neutral[7],
                                        fontWeight: 'normal',
                                    },
                                },
                                '& > :last-child': {
                                    color: theme.palette.primary.main,
                                },
                            },
                        }}
                        isClearable={false}
                    />
                </Box>
            </StyledTableCell>
            <StyledTableCell component="td" scope="row">
                {order.promisedAt
                    ? `${moment(order.promisedAt).format('DD/MM/YY, HH:mm')} hrs`
                    : '--'}
            </StyledTableCell>
            <StyledTableCell component="td" scope="row">
                {renderDaysOnPause(pausedDays)}
            </StyledTableCell>
        </StyledTableRow>
    );
}

const StyledTableRow = styled(TableRow)(({ theme }) => ({
    '&:hover': {
        backgroundColor: 'rgba(211, 224, 255, 0.27)',
        cursor: 'pointer',
    },

    '&.blink': {
        animation: 'blinker 2s linear normal infinite',
    },
    '@keyframes blinker': {
        '0%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '10%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '25%': {
            backgroundColor: 'var(--blinker-color-1)',
        },
        '40%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '50%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '60%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '75%': {
            backgroundColor: 'var(--blinker-color-2)',
        },
        '90%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '100%': {
            backgroundColor: theme.palette.neutral[1],
        },
    },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    height: 40,
    padding: '0 0 0 35px',
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
}));

const StyledTowerNumberCell = styled(StyledTableCell)(() => ({
    padding: '0px',
}));
