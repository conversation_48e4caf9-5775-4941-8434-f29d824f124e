import { TableCell, TableHead, TableRow, styled } from '@mui/material';
import Box from '@mui/material/Box';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import PauseReasonTableFilterCell from './PauseReasonTableFilterCell';
import PhaseTableFilterCell from './PhaseTableFilterCell';
import TechnicanTableFilterCell from './TechnicanTableFilterCell';

export function PausedOrdersTableHeader() {
    const { t } = useAppTranslation();

    return (
        <StyledTableHead>
            <TableRow>
                <StyledTableCell
                    component="td"
                    scope="row"
                    sx={{ paddingLeft: 0, textAlign: 'center' }}
                >
                    {t('workshopPlanner.pausedOrders.tableHeaders.tower')}
                </StyledTableCell>
                <StyledTableCell component="td" scope="row">
                    {t('workshopPlanner.pausedOrders.tableHeaders.order')}
                </StyledTableCell>
                <StyledTableCell component="td" scope="row">
                    {t('workshopPlanner.pausedOrders.tableHeaders.phase')}
                    <PhaseTableFilterCell />
                </StyledTableCell>
                <StyledTableCell component="td" scope="row">
                    <Box sx={{ display: 'flex', alignItems: 'center', flexDirection: 'row' }}>
                        {t('workshopPlanner.pausedOrders.tableHeaders.latestTechnician')}
                        <TechnicanTableFilterCell />
                    </Box>
                </StyledTableCell>
                <StyledTableCell component="td" scope="row">
                    {t('workshopPlanner.pausedOrders.tableHeaders.reasonForPause')}
                    <PauseReasonTableFilterCell />
                </StyledTableCell>
                <StyledTableCell component="td" scope="row">
                    {t('workshopPlanner.pausedOrders.tableHeaders.promiseDate')}
                </StyledTableCell>
                <StyledTableCell component="td" scope="row">
                    {t('workshopPlanner.pausedOrders.tableHeaders.daysOnPause')}
                </StyledTableCell>
            </TableRow>
        </StyledTableHead>
    );
}

const StyledTableHead = styled(TableHead)({
    display: 'table',
    width: '100%',
    tableLayout: 'fixed',
});

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    padding: '6px 0 5px 35px',
    backgroundColor: '#F6F6F6',
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));
