import { useAppDispatch, useAppSelector } from '../../../../store';
import { TableHeadSelectFilter } from '../../../../common/components';
import { useMemo } from 'react';
import { createSelector } from 'reselect';
import { selectUsers } from '../../../../store/slices/users';
import { UserListItem } from '../../../../api/users';
import { pausedOrdersActions, selectFilters } from '../../../../store/slices/wp/paused';
import { useAppTranslation } from '../../../../common/hooks/useAppTranslation';

const selectTechnicians = createSelector([selectUsers], (users) => {
    const usersWithJobTitle: UserListItem[] = [];

    for (const id in users) {
        if (users[id].job === 'Technician') {
            usersWithJobTitle.push(users[id]);
        }
    }

    return usersWithJobTitle;
});

export default function TechnicanTableFilterCell() {
    const dispatch = useAppDispatch();
    const technicians = useAppSelector(selectTechnicians);
    const { t } = useAppTranslation();
    const filters = useAppSelector(selectFilters);

    const options = useMemo(
        () => technicians.map((u) => ({ id: u.key, label: u.name })),
        [technicians]
    );

    return (
        <TableHeadSelectFilter
            options={options}
            onSelectedChanged={(userIds) =>
                dispatch(pausedOrdersActions.setOrderFilters({ userIds: userIds }))
            }
            mainLabel={t('workshopPlanner.pausedOrders.tableHeaders.latestTechnician')}
            selected={filters.userIds}
        />
    );
}
