import { useMemo } from 'react';
import { usePhases } from 'views/WorkshopPlanner/helpers';
import { TableHeadSelectFilter } from '../../../../common/components';
import { Phases } from '../../../../common/constants';
import { useAppTranslation } from '../../../../common/hooks/useAppTranslation';
import { useAppDispatch, useAppSelector } from '../../../../store';
import { pausedOrdersActions, selectFilters } from '../../../../store/slices/wp/paused';

export default function PhaseTableFilterCell() {
    const dispatch = useAppDispatch();
    const filters = useAppSelector(selectFilters);
    const { t } = useAppTranslation();
    const { phases } = usePhases();

    const optionsDistinct = useMemo(() => {
        return (phases ?? [])
            .filter((value) => value.id !== Phases.Closed)
            .map((value) => {
                if (value.id === Phases.NoPhase) {
                    return {
                        id: Phases.NoPhase.toString(),
                        label: t('orderDetails.noPhase'),
                    };
                } else
                    return {
                        id: value.id.toString(),
                        label: value.name,
                    };
            });
    }, [t, phases]);

    return (
        <TableHeadSelectFilter
            options={optionsDistinct}
            onSelectedChanged={(phaseIds) =>
                dispatch(pausedOrdersActions.setOrderFilters({ phaseIds: phaseIds }))
            }
            mainLabel={t('workshopPlanner.pausedOrders.filters.filterByPhase')}
            selected={filters.phaseIds}
        />
    );
}
