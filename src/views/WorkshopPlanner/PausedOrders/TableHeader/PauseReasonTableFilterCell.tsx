import { useAppDispatch, useAppSelector } from '../../../../store';
import { useAppTranslation } from '../../../../common/hooks/useAppTranslation';
import { useMemo } from 'react';
import { TableHeadSelectFilter } from '../../../../common/components';
import { pausedOrdersActions, selectFilters } from '../../../../store/slices/wp/paused';
import {
    convertReasonForPauseToLabel,
    ReasonForPause,
    REASONS_FOR_PAUSE,
} from '../../../../common/constants/ReasonForPause';

function getTranslationKey(reason: ReasonForPause): string {
    return `status.orders.reasonsForPause.${convertReasonForPauseToLabel(reason)}`;
}

export default function PauseReasonTableFilterCell() {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const filters = useAppSelector(selectFilters);

    const options = useMemo(
        () =>
            REASONS_FOR_PAUSE.map((reason) => ({
                id: reason + '',
                label: t(getTranslationKey(reason)),
            })),
        [t]
    );

    return (
        <TableHeadSelectFilter
            options={options}
            onSelectedChanged={(pauseReasonIds) =>
                dispatch(pausedOrdersActions.setOrderFilters({ pauseReasonIds: pauseReasonIds }))
            }
            mainLabel={t('workshopPlanner.pausedOrders.filters.filterByReasonForPause')}
            selected={filters.pauseReasonIds}
        />
    );
}
