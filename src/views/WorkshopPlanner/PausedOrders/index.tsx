import { Table, TableBody, styled } from '@mui/material';
import ShopWPSettingsApi from 'api/workshopPlanner/shopWPSettings';
import { LightIcon } from 'common/components/Icons/LightIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectFilteredPausedOrders } from 'store/slices/wp/paused';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import {
    selectFilterApplied,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import { useHeaderLoading } from 'views/HeaderBar';
import OrderPopup from '../OrderPopup';
import { PausedOrdersTableHeader } from './TableHeader';
import { PausedOrdersTableRow } from './TableRow';

const getDefaultShopWPSettings = (): ShopWPSettingsApi.ShopWpSettingsDto => {
    return {
        highlightExpiredDeliveryDateOrders: false,
        highlightExpiredPausedDaysOrders: false,
        preventiveExpiredPausedDays: 2,
        urgentExpiredPausedDays: 5,
        urgentFlashingIndicationEnabled: false,
        urgentFlashingIndicationTemplateId: null,
        urgentFlashingIndicationExplanatoryText: '',
        showTeamMemberSpecialty: false,
        defaultScheduledDurationMinutes: 60,
        showWorkshopUtilizationMetric: false,
        productiveWorkspace: 1,
        completedJobsType: 'ActualTimeSpent',
    };
};

export const WorkshopPlannerPausedOrders = () => {
    const { pausedOrders, showLoader } = useAppSelector(selectFilteredPausedOrders);
    const { t } = useAppTranslation();
    const wpSettings = useAppSelector(selectShopWpSettings);

    const [pausedOrdersSettings, setPausedOrdersSettings] =
        useState<ShopWPSettingsApi.ShopWpSettingsDto>(getDefaultShopWPSettings);

    useEffect(() => {
        if (wpSettings) {
            setPausedOrdersSettings(wpSettings);
        }
    }, [wpSettings]);
    // we can use phases loaded as an indicator that orders were loaded

    const [orderPopupNumber, setOrderPopupNumber] = useState<string>();

    const filteredOrderIds = useAppSelector(selectFilteredOrderIds);
    const filterApplied = useAppSelector(selectFilterApplied);
    const filteredOrders = useMemo(() => {
        return filterApplied
            ? pausedOrders.filter((order) => filteredOrderIds.includes(order.id))
            : pausedOrders;
    }, [filterApplied, filteredOrderIds, pausedOrders]);

    useHeaderLoading(showLoader);

    return (
        <>
            {orderPopupNumber && (
                <OrderPopup
                    onClose={() => setOrderPopupNumber(undefined)}
                    onSaved={() => setOrderPopupNumber(undefined)}
                    orderNumber={orderPopupNumber}
                />
            )}
            <div>
                <DivLightsLegendsContainer>
                    <DivLightLegendWrapper>
                        <StyledLightIcon fill="var(--yellow)" />
                        {t('workshopPlanner.pausedOrders.lightsLegends.expiredPausedDays', {
                            days: pausedOrdersSettings.preventiveExpiredPausedDays,
                        })}
                    </DivLightLegendWrapper>
                    <DivLightLegendWrapper>
                        <StyledLightIcon fill="#FA966B" />
                        {t('workshopPlanner.pausedOrders.lightsLegends.expiredPausedDays', {
                            days: pausedOrdersSettings.urgentExpiredPausedDays,
                        })}
                    </DivLightLegendWrapper>
                    <DivLightLegendWrapper>
                        <StyledLightIcon fill="var(--danger)" />
                        {t('workshopPlanner.pausedOrders.lightsLegends.expiredDeliveryDay')}
                    </DivLightLegendWrapper>
                </DivLightsLegendsContainer>
                <DivContainer>
                    <DivHeader>
                        <SpanHeaderText>
                            <b>{t('workshopPlanner.pausedOrders.paused')}</b>
                            {` - ${filteredOrders.length} ${
                                filteredOrders.length === 1
                                    ? t('workshopPlanner.pausedOrders.order')
                                    : t('workshopPlanner.pausedOrders.orders')
                            }`}
                        </SpanHeaderText>
                    </DivHeader>
                    <Table sx={{ width: '100%' }} stickyHeader aria-label="sticky table">
                        <PausedOrdersTableHeader />
                        <StyledTableBody>
                            {filteredOrders.length === 0 ? (
                                <tr>
                                    <td colSpan={7}>
                                        <PNoPausedOrders>
                                            {t('workshopPlanner.pausedOrders.noPausedOrders')}
                                        </PNoPausedOrders>
                                    </td>
                                </tr>
                            ) : (
                                filteredOrders.map((po) => (
                                    <PausedOrdersTableRow
                                        key={po.number}
                                        order={po}
                                        settings={pausedOrdersSettings}
                                        requestOrderPopup={setOrderPopupNumber}
                                    />
                                ))
                            )}
                        </StyledTableBody>
                    </Table>
                </DivContainer>
            </div>
        </>
    );
};

const DivLightsLegendsContainer = styled('div')({
    display: 'flex',
    justifyContent: 'flex-end',
    paddingTop: '24px',
});

const DivLightLegendWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    marginLeft: '16px',
    color: theme.palette.neutral[7],
}));

const StyledLightIcon = styled(LightIcon)({
    marginRight: '-4px',
});

const DivContainer = styled('section')(({ theme }) => ({
    width: '100%',
    height: 'fit-content',
    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: 12,
}));

const DivHeader = styled('header')(({ theme }) => ({
    height: 35,
    backgroundColor: '#EBEBEB',
    color: theme.palette.neutral[7],
    borderRadius: '12px 12px 0 0',
    display: 'flex',
    alignItems: 'center',
}));

const SpanHeaderText = styled('span')(({ theme }) => ({
    marginLeft: 21,
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
}));

const PNoPausedOrders = styled('p')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    margin: '20px 0 20px 16px',
}));

const StyledTableBody = styled(TableBody)({
    display: 'block',
    maxHeight: 'calc(100vh - 270px)',
    overflowY: 'auto',
    ...scrollbarStyle(),

    '& tr': {
        display: 'table',
        width: '100%',
        tableLayout: 'fixed',
    },
});
