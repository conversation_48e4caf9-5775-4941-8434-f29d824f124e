import { styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { PhaseDto } from 'api/orders';
import PhaseSetbackApi from 'api/phaseSetback';
import WpOrdersApi from 'api/workshopPlanner/orders';
import { useOrderJobsInProgressCheck } from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { usePhaseSetbackCheck } from 'common/components/ChangePhaseProviders/PhaseSetbackCheck';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useWindowDimension } from 'common/hooks/useWindowDimension';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useMemo, useState } from 'react';
import { DragDropContext, DropResult } from 'react-beautiful-dnd';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectUserPermission } from 'store/slices/user/selectors';
import {
    OrderView,
    selectWpOrderViewsWithoutPaused,
    wpOrdersActions,
} from 'store/slices/wp/orders';
import {
    selectFilterApplied,
    selectFilteredOrderIds,
} from 'store/slices/wp/unifiedSearchFilter/selectors';
import { useHeaderLoading } from 'views/HeaderBar';
import { useOrderPopup } from '../OrderPopup/context';
import { Phase } from './Phase';

type PhaseChangeState = {
    originPhase: PhaseDto;
    destinationPhase: PhaseDto;
    order: OrderView;
};

type WorkshopPlannerPhasesProps = {
    phases: PhaseDto[];
    phasesLoaded: boolean;
};

export function WorkshopPlannerPhases({ phases, phasesLoaded }: WorkshopPlannerPhasesProps) {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const update = useMutation(
        async ({
            phaseChange,
            reason,
        }: {
            phaseChange: PhaseChangeState | null;
            reason?: string;
        }) => {
            if (!phaseChange) return;

            dispatch(
                wpOrdersActions.update({
                    id: phaseChange.order.id,
                    phase: phaseChange.destinationPhase,
                })
            );

            await WpOrdersApi.updateOrder(phaseChange.order.number, {
                phaseId: phaseChange.destinationPhase.id,
            });

            if (reason) {
                try {
                    await PhaseSetbackApi.savePhaseSetback({
                        orderKey: phaseChange.order.id,
                        originPhaseId: phaseChange.originPhase.id,
                        destinationPhaseId: phaseChange.destinationPhase.id,
                        reason,
                    });
                } catch {
                    toasters.danger('', t('toasters.errorOccurredWhenSaving'));
                }
            }
        },
        {
            onError: (_, { phaseChange }) => {
                dispatch(
                    wpOrdersActions.update({
                        id: phaseChange!.order.id,
                        phase: phaseChange!.originPhase,
                    })
                );

                toasters.danger('', t('toasters.errorOccurredWhenSaving'));
            },
        }
    );

    return (
        <WorkshopPlannerPhasesInner
            phases={phases}
            phasesLoaded={phasesLoaded}
            onChange={(phaseChange, reason) => update.mutate({ phaseChange, reason })}
        />
    );
}

type WorkshopPlannerPhasesInnerProps = {
    phases: PhaseDto[];
    phasesLoaded: boolean;
    onChange: (phaseChange: PhaseChangeState, reason?: string) => void;
};

function WorkshopPlannerPhasesInner({
    phases,
    phasesLoaded,
    onChange,
}: WorkshopPlannerPhasesInnerProps) {
    const { t } = useAppTranslation();
    const userPermission = useAppSelector(selectUserPermission);
    const maximumNumberOfDigits =
        useAppSelector(selectSettings).repairShopSettings?.features
            .maximumNumberOfDigitsToDisplayForTowerNumber ?? 5;
    const orders = useAppSelector(selectWpOrderViewsWithoutPaused);
    const window = useWindowDimension();
    const phaseSetbackCheck = usePhaseSetbackCheck();
    const orderJobsInProgress = useOrderJobsInProgressCheck();
    const [loading, setLoading] = useState(false);
    const filteredOrderIds = useAppSelector(selectFilteredOrderIds);
    const filterApplied = useAppSelector(selectFilterApplied);

    const filteredOrders = useMemo(() => {
        return filterApplied
            ? orders.filter((order) => filteredOrderIds.includes(order.id))
            : orders;
    }, [filterApplied, filteredOrderIds, orders]);

    useHeaderLoading(loading);

    const findPhase = (droppableId: string): PhaseDto => {
        const phaseId = Number.parseInt(droppableId);

        const phase = phases.find((p) => p.id === phaseId);

        return phase !== undefined
            ? { id: phase.id, name: phase.name, order: phase.order }
            : phaseId === Phases.NoPhase
            ? { id: Phases.NoPhase, name: 'noPhase', order: 0 }
            : { id: Phases.Closed, name: 'closedOrder', order: Number.MAX_SAFE_INTEGER };
    };

    const onDragEnd = async (result: DropResult) => {
        if (!result.destination || !userPermission.allowEditWorkshopPlanner) return;

        const { source, destination, draggableId } = result;

        if (source.droppableId !== destination.droppableId) {
            const order = filteredOrders.find((order) => order.number === draggableId);

            if (order) {
                setLoading(true);
                const destinationPhase = findPhase(destination.droppableId);
                const originPhase = findPhase(source.droppableId);
                phaseSetbackCheck.checkPhasesIds(
                    { originPhaseId: originPhase.id, destinationPhaseId: destinationPhase.id },
                    (reason) =>
                        orderJobsInProgress.checkOrderJobsInProgress(
                            {
                                destinationPhaseId: destinationPhase.id,
                                orderId: order.id,
                                orderNumber: order.number,
                            },
                            () => {
                                onChange({ originPhase, destinationPhase, order }, reason);
                                setLoading(false);
                            },
                            () => setLoading(false)
                        ),
                    () => setLoading(false)
                );
            }
        }
    };

    const orderPopup = useOrderPopup();

    useHeaderLoading(!phasesLoaded);

    if (!phasesLoaded) {
        return null;
    }

    return (
        <>
            <DragDropContext onDragEnd={onDragEnd}>
                <DivRoot phasesLength={phases.length}>
                    <DivHeader>
                        <SpanHeaderText>{t('workshopPlanner.phases.phasesTitle')}</SpanHeaderText>
                    </DivHeader>
                    <DivBody
                        style={{
                            '--gridTemplateColumns': `repeat(${phases.length}, ${
                                phases.length < 5
                                    ? `calc(165px * ${window.windowWidth} / 1280)`
                                    : `${100 / Math.min(7, phases.length)}%`
                            })`,
                        }}
                    >
                        <Phase
                            requestOrderPopup={orderPopup.open}
                            phase={{ id: -1, name: 'noPhase', order: 0 }}
                            orders={filteredOrders.filter((o) => o.phase.id === Phases.NoPhase)}
                            maximumNumberOfDigits={maximumNumberOfDigits}
                        />
                        {phases
                            .filter((x) => x.id !== Phases.NoPhase && x.id !== Phases.Closed)
                            .map((phase) => (
                                <Phase
                                    key={phase.id}
                                    requestOrderPopup={orderPopup.open}
                                    phase={{ id: phase.id, name: phase.name, order: phase.order }}
                                    orders={filteredOrders.filter((o) => o.phase.id === phase.id)}
                                    maximumNumberOfDigits={maximumNumberOfDigits}
                                />
                            ))}
                        <Phase
                            requestOrderPopup={orderPopup.open}
                            phase={{
                                id: Phases.Closed,
                                name: 'closedOrder',
                                order: Number.MAX_SAFE_INTEGER,
                            }}
                            orders={filteredOrders.filter((o) => o.phase.id === Phases.Closed)}
                            maximumNumberOfDigits={maximumNumberOfDigits}
                        />
                    </DivBody>
                </DivRoot>
            </DragDropContext>
        </>
    );
}

const DivRoot = styled('div', { shouldForwardProp: (prop) => prop !== 'phasesLength' })<{
    phasesLength: number;
}>(({ phasesLength }) => ({
    width: `${phasesLength >= 5 ? '100%' : 'fit-content'}`,
}));

const DivBody = styled(OverlayScrollbarsComponent)({
    border: '1px solid #C9CDD3',
    borderTop: 'none',
    borderRadius: '0 0 12px 12px',

    '[data-overlayscrollbars-contents]': {
        height: 242,
        display: 'grid',
        gridTemplateRows: '100%',
        gridTemplateColumns: 'var(--gridTemplateColumns)',
    },

    '.os-scrollbar-horizontal': {
        margin: '0 5px',
    },
});

const DivHeader = styled('header')(({ theme }) => ({
    height: 35,
    backgroundColor: '#EBEBEB',
    color: theme.palette.neutral[7],
    border: '1px solid #C9CDD3',
    borderBottom: 'none',
    borderRadius: '12px 12px 0 0',
    display: 'flex',
    alignItems: 'center',
}));

const SpanHeaderText = styled('span')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    marginLeft: 21,
    fontWeight: 'bold',
}));
