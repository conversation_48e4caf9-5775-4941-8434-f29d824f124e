import { Tooltip, styled } from '@mui/material';
import { WpPhaseInfo } from 'api/workshopPlanner/orders';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { Droppable } from 'react-beautiful-dnd';
import { OrderView } from 'store/slices/wp/orders';
import { Tower } from '../Tower';

type PhaseProps = {
    phase: WpPhaseInfo;
    orders: OrderView[];
    maximumNumberOfDigits: number;
    requestOrderPopup: (orderNumber: string) => void;
};

export const Phase = ({ phase, orders, maximumNumberOfDigits, requestOrderPopup }: PhaseProps) => {
    const { t } = useAppTranslation();

    const boundaryNumberOfDigits = 5;

    const sortedOrders = orders.sort((a, b) => {
        if (a.tower !== null && b.tower !== null) {
            return parseInt(a.tower) - parseInt(b.tower);
        } else if (a.tower !== null) {
            return -1;
        } else if (b.tower !== null) {
            return 1;
        } else if (a.number <= b.number) {
            return -1;
        } else return 1;
    });

    return (
        <>
            <Droppable droppableId={phase.id.toString()} key={phase.id}>
                {(provided) => (
                    <DivRoot data-test-id={`phase${phase.id}`} id={`phase${phase.id}`}>
                        {phase.name === 'noPhase' || phase.name === 'closedOrder' ? (
                            <Tooltip
                                title={
                                    phase.name === 'noPhase'
                                        ? t('workshopPlanner.phases.noPhaseDescription')
                                        : t('workshopPlanner.phases.closedOrderDescription')
                                }
                            >
                                <DivHeader>
                                    <SpanPhaseName>
                                        {t(`workshopPlanner.phases.${phase.name}`)}
                                    </SpanPhaseName>
                                </DivHeader>
                            </Tooltip>
                        ) : (
                            <DivHeader>
                                <SpanPhaseName>{phase.name}</SpanPhaseName>
                            </DivHeader>
                        )}
                        <PhaseContent {...provided.droppableProps} ref={provided.innerRef}>
                            <DibConesContainer
                                isMinExpanded={maximumNumberOfDigits <= boundaryNumberOfDigits}
                            >
                                {sortedOrders.map((order, index) => (
                                    <Tower
                                        key={order.number}
                                        onRequestOrderPopup={() => requestOrderPopup(order.number)}
                                        order={order}
                                        index={index}
                                        displayMode={
                                            maximumNumberOfDigits <= boundaryNumberOfDigits
                                                ? 'min-expanded'
                                                : 'max-expanded'
                                        }
                                        maximumNumberOfDigits={maximumNumberOfDigits}
                                    />
                                ))}
                            </DibConesContainer>
                        </PhaseContent>
                    </DivRoot>
                )}
            </Droppable>
        </>
    );
};

const DivRoot = styled('div')({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    minHeight: 250,
    maxHeight: 'calc(100vh - 200px)',

    '&:last-child': {
        borderRight: 'none',
    },

    '&:last-child > div': {
        borderRight: 'none',
    },
});

const DibConesContainer = styled('div')<{ isMinExpanded: boolean }>(({ isMinExpanded }) => ({
    // display: 'grid',
    // justifyContent: 'center',
    // rowGap: 20,
    // columnGap: 30,
    // gridTemplateColumns: `repeat(auto-fill, ${isMinExpanded ? 60 : 120}px)`,
    // display: 'flex',
    // flexWrap: 'wrap',

    '& > *': {
        margin: '8px',
        display: 'inline-block',
    },

    '& [data-rbd-placeholder-context-id]': {
        backgroundColor: 'red',
    },
}));

const DivHeader = styled('div')({
    minHeight: 35,
    backgroundColor: '#F6F6F6',
    alignContent: 'center',
    borderRight: '1px solid #C9CDD3',
});

const SpanPhaseName = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[7],
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
    margin: '5px 10px 6px 10px',
    display: 'block',
    textAlign: 'center',
}));

const PhaseContent = styled('div')({
    flexGrow: 1,
    padding: '12px 12px 0 12px',
    overflowY: 'auto',
    borderRight: '1px solid var(--neutral4)',
    ...scrollbarStyle(),
});
