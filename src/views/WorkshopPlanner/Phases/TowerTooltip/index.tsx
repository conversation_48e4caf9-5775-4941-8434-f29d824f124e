import { styled } from '@mui/material';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { memo, useMemo } from 'react';
import { OrderView } from 'store/slices/wp/orders';
import { renderPromisedDate } from 'views/WorkshopPlanner/helpers';

type ConeTooltipProps = {
    order: OrderView;
};

const TowerTooltipData = ({ order }: ConeTooltipProps) => {
    const { t } = useAppTranslation();

    const formattedCustomerData = useMemo(() => {
        const customerName = [order.customer.firstName?.trim(), order.customer.lastName?.trim()]
            .filter(Boolean)
            .join(' ');

        return (
            <>
                #{order.number}
                {customerName && <> - {customerName}</>}
            </>
        );
    }, [order.number, order.customer.firstName, order.customer.lastName]);

    const formattedVehicleData = useMemo(
        () => [order.vehicle.model.trim(), order.vehicle.plates.trim()].filter(Boolean).join(' - '),
        [order.vehicle.model, order.vehicle.plates]
    );

    const formattedOrderTypeData = useMemo(() => order.type?.name, [order.type?.name]);

    const formattedPromiseData = useMemo(
        () =>
            order.promisedAt && (
                <>
                    {t('workshopPlanner.phases.promiseLabel')}
                    {renderPromisedDate(t, order.promisedAt)}
                </>
            ),
        [t, order.promisedAt]
    );

    const formattedPhaseData = useMemo(
        () =>
            (order.phase.id === Phases.NoPhase || order.phase.id === Phases.Closed
                ? t(`workshopPlanner.phases.${order.phase.name}`)
                : order.phase.name
            ).toUpperCase(),
        [t, order.phase.id, order.phase.name]
    );

    return (
        <StyledTooltipData>
            <BoldRow>{formattedCustomerData}</BoldRow>
            <div>{formattedVehicleData}</div>
            <div>{formattedOrderTypeData}</div>
            <BoldRow>{formattedPromiseData}</BoldRow>
            <div>{formattedPhaseData}</div>
        </StyledTooltipData>
    );
};

const StyledTooltipData = styled('div')({
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: 500,
    lineHeight: '24px',
    padding: '14px 24px',
});

const BoldRow = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    fontWeight: 'bold',
    lineHeight: '24px',
}));

export default memo(TowerTooltipData);
