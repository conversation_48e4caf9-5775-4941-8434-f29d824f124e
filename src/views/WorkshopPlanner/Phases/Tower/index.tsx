import { styled } from '@mui/material';
import { useState } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { OrderView } from 'store/slices/wp/orders';
import { StyledTooltip } from 'views/WorkshopPlanner/common/TooltipComponents';
import TowerNumber from 'views/WorkshopPlanner/common/TowerNumber';
import TowerTooltipData from '../TowerTooltip';

type ConeProps = {
    order: OrderView;
    index: number;
    displayMode?: 'min-content' | 'ellipsis' | 'min-expanded' | 'max-expanded';
    maximumNumberOfDigits?: number;
    onRequestOrderPopup: () => void;
};

export const Tower = ({
    order,
    index,
    displayMode = 'ellipsis',
    maximumNumberOfDigits,
    onRequestOrderPopup,
}: ConeProps) => {
    const [tooltipIsOpen, setTooltipIsOpen] = useState<boolean>(false);

    const handleTooltip = (value: boolean) => {
        setTimeout(() => setTooltipIsOpen(value), 200);
    };

    return (
        <Draggable key={`order-${order.number}`} draggableId={order.number} index={index}>
            {(provided, snapshot) => (
                <StyledTooltip
                    placement={'bottom'}
                    arrow
                    title={<TowerTooltipData order={order} />}
                    open={tooltipIsOpen}
                >
                    <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        onClick={() => {
                            onRequestOrderPopup();
                        }}
                        style={{
                            ...provided.draggableProps.style,
                        }}
                    >
                        <StyledTowerNumber
                            onMouseEnter={() => handleTooltip(true)}
                            onMouseLeave={() => handleTooltip(false)}
                            towerNumber={order.tower}
                            orderNumber={order.number}
                            orderTypeKey={order.type?.key}
                            appointmentReasonColor={order.appointmentReasonColor}
                            userIdOrKey={order.inCharge?.id}
                            displayMode={displayMode}
                            maximumNumberOfDigits={maximumNumberOfDigits}
                            isResponsive
                            isDragging={snapshot.isDragging}
                            style={{
                                opacity: snapshot.isDragging ? '0.5' : '1',
                                cursor: snapshot.isDragging ? 'grab' : 'pointer',
                            }}
                        />
                    </div>
                </StyledTooltip>
            )}
        </Draggable>
    );
};

const StyledTowerNumber = styled(TowerNumber)<{ isDragging: boolean }>(({ isDragging }) => ({
    position: 'absolute',
    transition: '0.2s',
    opacity: isDragging ? '0.5' : '1',
    cursor: isDragging ? 'grab' : 'pointer',
    '-webkit-font-smoothing': 'subpixel-antialiased',
    backfaceVisibility: 'hidden',

    '&:hover': {
        transform: 'scale(1.1)',
    },
}));
