import { createContext, useContext, useMemo } from 'react';

type PlanningsTabsContextData = {
    setPlanningId: (planningId: number) => boolean;
};

const PlanningsTabsContext = createContext<PlanningsTabsContextData | null>(null);

export function usePlanningsTabs() {
    return useContext(PlanningsTabsContext);
}

type PlanningsTabsProviderProps = React.PropsWithChildren<{
    setPlanningId: (planningId: number) => boolean;
}>;

export function PlanningsTabsProvider({ setPlanningId, children }: PlanningsTabsProviderProps) {
    const ctx = useMemo(
        () => ({
            setPlanningId: (newPlanningId: number) => setPlanningId(newPlanningId),
        }),
        [setPlanningId]
    );

    return <PlanningsTabsContext.Provider value={ctx}> {children}</PlanningsTabsContext.Provider>;
}
