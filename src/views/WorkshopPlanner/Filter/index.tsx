import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { Box, IconButton, styled } from '@mui/material';
import DateFormField from 'common/components/Inputs/DateFormField';
import useQueryParam from 'common/hooks/useQueryParam';
import debounce from 'lodash/debounce';
import isEqual from 'lodash/isEqual';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectFeatureFlags } from 'store/slices/globalSettingsSlice';
import { planningsActions, selectDateString, selectPlannings } from 'store/slices/wp/plannings';
import { isDateValid } from 'utils';
import UnifiedSearchFilter from './UnifiedSearchFilter';

const StyledDateFormField = styled(DateFormField)({
    width: 115,
    minWidth: 115,

    '& .MuiInputBase-input': {
        fontWeight: 'bold',
    },
});

const WorkshopPlannerFilter = () => {
    const dispatch = useAppDispatch();
    const [queryDate] = useQueryParam('startDate');

    useEffect(() => {
        if (!queryDate) return;

        const date = DateTime.fromISO(queryDate);

        if (date.isValid) {
            dispatch(planningsActions.setSelectedDate(date.toFormat('yyyy-MM-dd')));
        }
    }, [dispatch, queryDate]);

    const currentDate = useAppSelector(selectDateString);
    const currentDateJs = useMemo(() => DateTime.fromISO(currentDate).toJSDate(), [currentDate]);
    const { loaded } = useAppSelector(selectPlannings, isEqual);
    const { state, layoutOverflow } = useLayoutOverflow();

    const nextPlanning = () => {
        layoutOverflow?.setOffset(state.offset + 1);
    };

    const previousPlanning = () => {
        layoutOverflow?.setOffset(state.offset - 1);
    };

    const hasPrevPlanning = state.offset > 0;
    const hasNextPlanning = state.doNotFitCount > 0;
    const showToggleButtons = hasNextPlanning || hasPrevPlanning;

    const { workshopPlannerFilterFeature } = useAppSelector(selectFeatureFlags);

    return (
        <DivRoot>
            <Box sx={{ display: 'flex' }}>
                {loaded && (
                    <StyledDateFormField
                        name="date"
                        enableEnterComplete
                        variant="rounded"
                        value={currentDateJs}
                        onChange={(date) => {
                            if (date && isDateValid(date)) {
                                dispatch(
                                    planningsActions.setSelectedDate(
                                        DateTime.fromJSDate(date).toFormat('yyyy-MM-dd')
                                    )
                                );
                            }
                        }}
                    />
                )}
            </Box>

            <Box
                sx={{
                    display: 'flex',
                }}
            >
                {showToggleButtons && (
                    <>
                        <StyledIconButton
                            size="small"
                            disabled={!hasPrevPlanning}
                            onClick={() => previousPlanning()}
                        >
                            <ChevronLeft />
                        </StyledIconButton>

                        <StyledIconButton
                            size="small"
                            disabled={!hasNextPlanning}
                            onClick={() => nextPlanning()}
                        >
                            <ChevronRight />
                        </StyledIconButton>
                    </>
                )}
            </Box>
            <Box
                sx={{
                    width: '250px',
                }}
            >
                {workshopPlannerFilterFeature && <UnifiedSearchFilter />}
            </Box>
        </DivRoot>
    );
};

const StyledIconButton = styled(IconButton)({
    color: 'var(--cm1)',

    ':disabled': {
        color: 'var(--cm4)',
    },
});

const DivRoot = styled('div')({
    display: 'grid',
    gridTemplateColumns: '115px 1fr auto',
    position: 'relative',
    margin: '0 16px',
    padding: '0 16px',
    width: 'calc(100% - 32px)',
    overflow: 'hidden',
    minHeight: 33, // 1px bigger than height of the dropdowns to avoid weird visual bug

    '::before, ::after': {
        content: '""',
        display: 'block',
        width: 1,
        backgroundColor: 'var(--neutral4)',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
    },

    '::after': {
        left: 'initial',
        right: 0,
    },
});

export default WorkshopPlannerFilter;

function useLayoutOverflow() {
    const [layoutOverflow, setLayoutOverflow] = useState<LayoutOverflow | null>(null);
    const [state, setState] = useState<LayoutOverflowState>({
        totalCount: 0,
        doNotFitCount: 0,
        offset: 0,
    });

    useEffect(
        () => () => {
            layoutOverflow?.destroy();
        },
        [layoutOverflow]
    );

    const ref = useCallback((element: HTMLElement | null) => {
        if (element) {
            setLayoutOverflow(new LayoutOverflow(element, setState));
        } else {
            setLayoutOverflow(null);
        }
    }, []);

    return {
        ref,
        state,
        layoutOverflow,
    };
}

type LayoutOverflowState = {
    offset: number;
    totalCount: number;
    doNotFitCount: number;
};

/**
 * Utility class that handles layout overflow and hides elements that do not fit the root element.
 *
 * This utility make the following assumptions:
 *  * Root element must be a flex box with children element going horizontally left to right
 *  * Children elements must not be wider than the parent element
 *  * Change of `opacity` and `display` properties in children elements should not cause any layout changes (for example MutationObserver or ResizeObserver observing child element and changing it somehow might interfere)
 */
class LayoutOverflow {
    private readonly _rootElement: HTMLElement;
    private readonly _observer: ResizeObserver;
    private readonly _stateCallback: (state: LayoutOverflowState) => void;
    private _state: Readonly<LayoutOverflowState> = {
        offset: 0,
        totalCount: 0,
        doNotFitCount: 0,
    };

    /**
     * @param element rootElement to observer
     * @param stateCallback callback called whenever internal state changes
     */
    constructor(element: HTMLElement, stateCallback: (state: LayoutOverflowState) => void) {
        this._rootElement = element;
        this._observer = new ResizeObserver(
            debounce(() => {
                this.update();
            }, 250)
        );
        this._observer.observe(this._rootElement);
        this._stateCallback = stateCallback;
        this.update();
    }

    /**
     * Sets the number of child elements that should be hidden in the beginning.
     * @param offset offset (number of elements to hide in the beginning)
     */
    setOffset(offset: number) {
        this._setOffset(offset);
        this.update();
    }

    /**
     * The same as `setOffset` but does not trigger state callback.
     */
    private _setOffset(offset: number) {
        if (offset < 0) offset = 0;
        if (offset === this._state.offset) return;

        const oldOffset = this._state.offset;

        const children = this._getChildren().slice(0, Math.max(offset, oldOffset)); // get affected children
        for (let i = 0; i < children.length; i++) {
            if (i < offset) {
                children[i].style.display = 'none';
            } else {
                children[i].style.removeProperty('display');
            }
        }

        // NOTE (MB) state must be updated at the very end
        this._state = {
            ...this._state,
            offset,
        };
    }

    /**
     * Traverses list of children elements and performs the following steps:
     *  1. Hides elements that do not fit
     *  2. If there are no elements that do not fit and offset is more than 0 then shows as many elements as possible that were previously hidden due to offset (i.e. sets the offset to lowest possible number)
     */
    update() {
        const start = performance.now();

        this._update();

        if (this._state.doNotFitCount === 0 && this._state.offset > 0) {
            const children = this._getChildren();
            if (children.length > 0) {
                const lastChild = children[children.length - 1];

                let iteration = 0; // to prevent infinite loop in case if I (or someone else) makes a lil "oopsie"

                while (!this._overflows(lastChild) && this._state.offset > 0 && iteration++ < 200) {
                    this._setOffset(this._state.offset - 1);
                }

                iteration = 0;

                while (this._overflows(lastChild) && iteration++ < 200) {
                    this._setOffset(this._state.offset + 1);
                }
            }
        }

        const took = performance.now() - start;
        if (took > 10) {
            console.warn('layout update in LayoutOverflow took ' + took + 'ms');
        }

        this._stateCallback(this._state);
    }

    /**
     * Traverses the list of all children elements and hides those that either do not fit or are hidden due to offset.
     */
    private _update() {
        const allChildren = this._getChildren();
        if (allChildren.length === 0) return;
        const children = allChildren.slice(this._state.offset);

        for (let i = 0; i < allChildren.length; i++) {
            const child = allChildren[i];

            if (i < this._state.offset) {
                // hide offset children
                child.style.display = 'none';
                child.style.removeProperty('opacity');
            } else if (child.dataset.overflowed === 'true') {
                // show hidden children, but make them transparent to let us calculate layout
                child.style.removeProperty('display');
                child.style.opacity = '0';
            }
        }

        let doNotFitCount = 0;

        // hide children that do not fit
        for (let i = 0; i < children.length; i++) {
            const child = children[i];
            const overflows = this._overflows(child) && i !== 0; // first child should never be marked as "overflown"
            child.dataset.overflowed = overflows ? 'true' : 'false';
            if (overflows) {
                child.style.display = 'none';
                doNotFitCount++;
            }
            child.style.removeProperty('opacity');
        }

        this._state = {
            ...this._state,
            totalCount: children.length,
            doNotFitCount,
        };
    }

    private _overflows(childElement: HTMLElement): boolean {
        const childOffsetRight = childElement.offsetLeft + childElement.clientWidth;
        return childOffsetRight > this._rootElement.offsetWidth;
    }

    private _getChildren() {
        return [...this._rootElement.childNodes].filter(
            (x): x is HTMLElement => x instanceof HTMLElement
        );
    }

    destroy() {
        this._observer.disconnect();
    }
}
