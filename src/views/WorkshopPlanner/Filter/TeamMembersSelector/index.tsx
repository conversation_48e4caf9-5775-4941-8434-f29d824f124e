import { SelectChangeEvent, styled } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { SMenuItem } from 'common/components/mui';
import SSelectMulti from 'common/components/mui/SSelectMulti';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useQueryParam from 'common/hooks/useQueryParam';
import { useCallback, useEffect, useMemo, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import { useAppDispatch, useAppSelector } from 'store';
import { planningsActions, selectPlanning } from 'store/slices/wp/plannings';

const TeamMembersSelectorSkeleton = styled(Skeleton)({
    width: 170,
    borderRadius: 20,
    height: 32,
    display: 'inline-block',
});

type TeamMembersSelectorProps = {
    planningId: number;
    loading?: boolean;
};

const ElipsisContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 5,
    minWidth: 0,
});

const ElipsisText = styled('div')({
    maxWidth: 120,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});

const ElipsisTextWithNumber = styled(ElipsisText)({
    maxWidth: 95,
});

const NoDataLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.primary.light,
    display: 'block',
    padding: '0 10px',
}));

export default function TeamMembersSelector({ planningId, loading }: TeamMembersSelectorProps) {
    const [tooltipOpen, setTooltipOpen] = useState(false);
    const [queryUserId] = useQueryParam('assignedTo');

    const planning = useAppSelector(
        useCallback((r) => selectPlanning(r, planningId), [planningId])
    );

    const users = useMemo(() => planning?.allUsers ?? [], [planning]);
    const selectedUsers = useMemo(() => planning?.users ?? [], [planning]);
    const dispatch = useAppDispatch();
    const selectedUserIds = useMemo(() => selectedUsers.map((x) => x.key), [selectedUsers]);
    const { t } = useAppTranslation();

    const planningName = planning?.isTechnicians
        ? t('workshopPlanner.technicians.title')
        : planning?.isAdvisors
        ? t('workshopPlanner.advisors')
        : planning?.name;

    const renderValue = useCallback(
        (value: string[]) => {
            if (selectedUsers.length === 0) {
                return <span>{planningName}</span>;
            }

            return (
                <>
                    <ElipsisContainer>
                        <ElipsisTextWithNumber>{planningName}</ElipsisTextWithNumber>
                        <span>{`+${value.length}`}</span>
                    </ElipsisContainer>
                </>
            );
        },
        [selectedUsers, planningName]
    );

    const onChange = useCallback(
        (e: SelectChangeEvent<string[]>) => {
            if (e.target.value instanceof Array) {
                const newValue = e.target.value;
                dispatch(planningsActions.setSelectedUsers({ planningId, userIds: newValue }));
            }
        },
        [dispatch, planningId]
    );

    useEffect(() => {
        if (!queryUserId) return;
        dispatch(planningsActions.setSelectedUser({ planningId, userId: queryUserId }));
    }, [dispatch, planningId, queryUserId]);

    if (loading) {
        return <TeamMembersSelectorSkeleton />;
    }

    return (
        <ArrowTooltip content={planningName} open={tooltipOpen}>
            <SSelectMulti<string>
                renderValue={renderValue}
                style={{ width: 170, minWidth: 170 }}
                onChange={onChange}
                value={selectedUserIds}
                onMouseEnter={() => setTooltipOpen(true)}
                onMouseLeave={() => setTooltipOpen(false)}
                onOpen={() => setTooltipOpen(false)}
            >
                {users.map((user) => (
                    <SMenuItem key={user.key} value={user.key}>
                        {selectedUserIds.includes(user.key) ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                        {user.name}
                    </SMenuItem>
                ))}
                {users.length === 0 && (
                    <NoDataLabel>{t('commonLabels.noDataSelector')}</NoDataLabel>
                )}
            </SSelectMulti>
        </ArrowTooltip>
    );
}
