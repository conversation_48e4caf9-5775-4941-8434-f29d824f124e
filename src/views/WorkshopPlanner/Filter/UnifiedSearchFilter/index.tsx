import {
    Autocomplete,
    AutocompleteInputChangeReason,
    Box,
    IconButton,
    ListItem,
    Paper,
} from '@mui/material';
import { styled } from '@mui/styles';
import { PhaseDto } from 'api/orders';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { TextField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Interweave } from 'interweave';
import { forwardRef, SyntheticEvent, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectWpOrders } from 'store/slices/wp/orders';
import { selectWpAppointments } from 'store/slices/wp/plannings';
import { unifiedSearchFilterActions } from 'store/slices/wp/unifiedSearchFilter';
import { getPhaseName } from 'views/OrderDetail/util';

type UnifiedSearchItem = {
    id: string;
    orderNumber: string;
    appointmentNumber: string;
    customer?: string;
    vin?: string;
    plates?: string;
    vehicle?: string;
    phase?: PhaseDto;
    type: 'order' | 'appointment';
    filteredBy: 'appointmentNumber' | 'orderNumber' | 'vin' | 'plates';
};

const UnifiedSearchFilter = () => {
    const { t } = useAppTranslation();

    const [open, setOpen] = useState(false);
    const [focused, setFocused] = useState(false);
    const [search, setSearch] = useState('');

    const allAppointments = useAppSelector(selectWpAppointments);
    const allOrders = useAppSelector(selectWpOrders);

    const dispatch = useAppDispatch();

    const handleInputChange = (
        _event: SyntheticEvent<Element, Event>,
        newInputValue: string,
        reason: AutocompleteInputChangeReason
    ) => {
        if (reason === 'reset') return;

        setSearch(newInputValue);
        setOpen(newInputValue.length > 0);
    };

    const handleEnterPress = (_event: React.KeyboardEvent) => {
        applyFilters(search);
        setOpen(false);
    };

    const handleValueChange = (
        _e: SyntheticEvent<Element, Event>,
        value: UnifiedSearchItem | null
    ) => {
        const search = value?.orderNumber || value?.appointmentNumber || '';
        setSearch(search);
        applyFilters(value ?? search);
        setOpen(false);
    };

    const handleClear = () => {
        setSearch('');
        applyFilters('');
        setOpen(false);
    };

    const applyFilters = (value: UnifiedSearchItem | string) => {
        const filterOption = (option: UnifiedSearchItem) =>
            (typeof value === 'string' && value.length > 0) ||
            (typeof value !== 'string' &&
                (value.appointmentNumber.toLowerCase() === option.appointmentNumber.toLowerCase() ||
                    value.orderNumber.toLowerCase() === option.orderNumber.toLowerCase()));

        console.log(value);

        dispatch(
            unifiedSearchFilterActions.setAppointmentIds(
                options.filter((o) => filterOption(o) && o.type === 'appointment').map((o) => o.id)
            )
        );

        dispatch(
            unifiedSearchFilterActions.setOrderIds(
                options.filter((o) => filterOption(o) && o.type === 'order').map((o) => o.id)
            )
        );

        dispatch(unifiedSearchFilterActions.setFilterApplied(!!value));
    };

    const options = useMemo(() => {
        const orderOptions = Object.values(allOrders)
            .map((order) => {
                let filteredBy = '';
                const appointmentNumber = order.appointmentId
                    ? allAppointments[order.appointmentId]?.number
                    : null;

                if (order.number.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'orderNumber';
                } else if (appointmentNumber?.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'appointmentNumber';
                } else if (order.vehicle?.vin.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'vin';
                } else if (order.vehicle?.plates.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'plates';
                }

                if (!filteredBy) return null;

                return {
                    id: order.id,
                    customer: `${order.customer.firstName} ${order.customer.lastName}`,
                    vin: order.vehicle?.vin || '',
                    plates: order.vehicle?.plates || '',
                    vehicle: order.vehicle?.model || '',
                    appointmentNumber: appointmentNumber ?? '',
                    orderNumber: order.number,
                    phase: order.phase,
                    type: 'order' as const,
                    filteredBy,
                };
            })
            .filter(Boolean) as UnifiedSearchItem[];

        const appointmentOptions = Object.values(allAppointments)
            .filter((x) => !x.orderId || !allOrders[x.orderId])
            .map((appointment) => {
                let filteredBy = '';

                if (appointment.orderNumber?.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'orderNumber';
                } else if (appointment.number.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'appointmentNumber';
                } else if (appointment.vehicle?.vin.toLowerCase().includes(search.toLowerCase())) {
                    filteredBy = 'vin';
                } else if (
                    appointment.vehicle?.plates.toLowerCase().includes(search.toLowerCase())
                ) {
                    filteredBy = 'plates';
                }

                if (!filteredBy) return null;

                return {
                    id: appointment.id,
                    customer: `${appointment.customer.firstName} ${appointment.customer.lastName}`,
                    vin: appointment.vehicle?.vin || '',
                    plates: appointment.vehicle?.plates || '',
                    vehicle: appointment.vehicle?.model || '',
                    appointmentNumber: appointment.number,
                    orderNumber: appointment.orderNumber ?? '',
                    phase: undefined,
                    type: 'appointment' as const,
                    filteredBy,
                };
            })
            .filter(Boolean) as UnifiedSearchItem[];

        return [...appointmentOptions, ...orderOptions];
    }, [search, allAppointments, allOrders]);

    return (
        <Autocomplete
            noOptionsText={t('workshopPlanner.unifiedSearch.noResults')}
            options={options}
            open={focused && open}
            clearIcon={null}
            onBlur={() => setFocused(false)}
            onFocus={() => setFocused(true)}
            inputValue={search}
            renderInput={(params) => {
                return (
                    <StyledTextField
                        {...params}
                        placeholder={t('workshopPlanner.unifiedSearch.searchPlaceholder')}
                        showValidationIndicators={true}
                        isRequired={false}
                        cmosVariant="roundedGrey"
                        onEnterPress={handleEnterPress}
                        enableEnterComplete={true}
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: search ? (
                                <StyledIconButton onClick={handleClear}>
                                    <CloseIcon fill={'var(--cm1)'} />
                                </StyledIconButton>
                            ) : (
                                <SearchIconWrapper>
                                    <SearchIcon fill={'var(--neutral6)'} />
                                </SearchIconWrapper>
                            ),
                        }}
                    />
                );
            }}
            onInputChange={handleInputChange}
            onChange={handleValueChange}
            forcePopupIcon={false}
            renderOption={(props, item) => (
                <ListItem {...props}>
                    <AutocompleteSearchItem item={item} inputValue={search} />
                </ListItem>
            )}
            PaperComponent={PaperComponent}
            filterOptions={(options) => options}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            getOptionLabel={(option) => {
                return option.id;
            }}
        />
    );
};

function selectionText(defaultValue: string | undefined, inputValue: string): string {
    defaultValue = defaultValue || '';
    const normalizedInputValue = inputValue.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    const regexp = new RegExp(normalizedInputValue, 'i');

    const startIndex = defaultValue.search(regexp);
    const lastIndex = startIndex + inputValue.length;

    const beforeBold = defaultValue.slice(0, startIndex);
    const boldText = defaultValue.slice(startIndex, lastIndex);
    const afterBold = defaultValue.slice(lastIndex);

    return boldText ? `${beforeBold}${`<b>${boldText}</b>`}${afterBold}` : '';
}

type AutocompleteSearchItemProps = {
    item: UnifiedSearchItem;
    inputValue: string;
};

function AutocompleteSearchItem({ item, inputValue }: AutocompleteSearchItemProps) {
    const { t } = useAppTranslation();
    const selectedText =
        inputValue &&
        (item.filteredBy === 'orderNumber'
            ? selectionText(item.orderNumber, inputValue)
            : item.filteredBy === 'appointmentNumber'
            ? selectionText(item.appointmentNumber, inputValue)
            : item.filteredBy === 'vin'
            ? selectionText(item.vin, inputValue)
            : item.filteredBy === 'plates'
            ? selectionText(item.plates, inputValue)
            : '');

    const selectedHtmlText = <Interweave content={selectedText ?? ''} />;

    return (
        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', width: '100%', flexBasis: '85%' }}>
                <Box sx={{ display: 'flex', alignContent: 'center', flexWrap: 'wrap' }}>
                    <CarIcon fill={'var(--cm1'} />
                </Box>

                <Box padding={'10px 5px'}>
                    <Title>
                        {t('workshopPlanner.unifiedSearch.orderNumber')}
                        {item.filteredBy === 'orderNumber'
                            ? selectedHtmlText
                            : item.orderNumber
                            ? item.orderNumber
                            : ' --'}
                    </Title>

                    <Title>
                        {' / '}
                        {t('workshopPlanner.unifiedSearch.appointmentNumber')}
                        {item.filteredBy === 'appointmentNumber'
                            ? selectedHtmlText
                            : item.appointmentNumber
                            ? item.appointmentNumber
                            : ' --'}
                    </Title>

                    {item.customer && (
                        <Title>
                            {' / '}
                            {t('workshopPlanner.unifiedSearch.customer')}
                            {item.customer}
                        </Title>
                    )}
                    {item.plates && (
                        <>
                            <Title>
                                {' / '}
                                {t('workshopPlanner.unifiedSearch.plates')}
                                {item.filteredBy === 'plates' ? selectedHtmlText : item.plates}
                            </Title>
                        </>
                    )}
                    {item.vin && (
                        <>
                            <Title>
                                {' / '}
                                {t('workshopPlanner.unifiedSearch.vin')}
                                {item.filteredBy === 'vin' ? selectedHtmlText : item.vin}
                            </Title>
                        </>
                    )}
                    {item.vehicle && (
                        <>
                            <Title>
                                {' / '}
                                {t('workshopPlanner.unifiedSearch.vehicle')}
                                {item.vehicle}
                            </Title>
                        </>
                    )}

                    {item.phase && (
                        <>
                            <Title>
                                {' / '}
                                {t('workshopPlanner.unifiedSearch.phase')}
                                {getPhaseName(item.phase, t)}
                            </Title>
                        </>
                    )}
                </Box>
            </Box>
        </Box>
    );
}
const Title = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[8],
}));

const PaperComponent = forwardRef(
    ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>, _ref) => {
        const { t } = useAppTranslation();

        return (
            <Paper {...props} sx={{ position: 'absolute', right: 0, width: 550 }}>
                <HeaderItem>
                    <SelectOrderLabel>
                        {t('workshopPlanner.unifiedSearch.results')}
                    </SelectOrderLabel>
                </HeaderItem>

                {children}
            </Paper>
        );
    }
);

const HeaderItem = styled(ListItem)({
    flexDirection: 'column',
    justifyContent: 'start',
    alignItems: 'start',
    paddingBottom: 0,
});

const SelectOrderLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
    marginTop: 5,
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
    '& .MuiOutlinedInput-root .MuiAutocomplete-input': {
        color: theme.palette.primary.main,
        padding: '0 0 0 5px',
        '&::placeholder': {
            color: 'var(--neutral6)',
            opacity: 1,
        },
    },
}));

const SearchIconWrapper = styled('div')({
    position: 'absolute',
    right: '10px',
    top: '18px',
    transform: 'translateY(-50%)',
    pointerEvents: 'none',
});

const StyledIconButton = styled(IconButton)({
    position: 'absolute',
    right: '2px',
    top: '16px',
    transform: 'translateY(-50%)',
});

export default UnifiedSearchFilter;
