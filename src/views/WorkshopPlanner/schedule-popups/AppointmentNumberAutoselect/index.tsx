import { ListItem, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import AppointmentsApi, { AppointmentListItem } from 'api/appointments';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import { MarkedText } from 'common/components/MarkedText';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';

type AppointmentNumberAutoselectProps = {
    value?: AppointmentListItem;
    onChange: (value: AppointmentListItem) => void;
    children?: React.ReactNode;
};

export default function AppointmentNumberAutoselect({
    value,
    onChange,
    children,
}: AppointmentNumberAutoselectProps) {
    const { t } = useAppTranslation();
    const [search, setSearch] = useState('');
    const [debouncedSearch] = useDebounce(search, 200);
    const { data, isLoading } = useQuery(
        ['appointments', 'search', debouncedSearch],
        () => AppointmentsApi.search(debouncedSearch),
        {
            keepPreviousData: true,
        }
    );

    useEffect(() => {
        setSearch(value?.number ?? '');
    }, [value]);

    const options = useMemo(() => {
        if (!data) return [];
        if (!value) return data;
        if (data.find((x) => x.id === value.id)) return data;
        return [value, ...data];
    }, [data, value]);

    return (
        <div>
            <InputLabel isRequired showValidationIndicators>
                {t('workshopPlanner.schedulePopup.selectAppointment')}
            </InputLabel>
            {children}
            <SAutocomplete<AppointmentListItem>
                style={{ width: 300 }}
                options={options}
                loading={isLoading}
                getOptionLabel={(a) => a.number}
                autoComplete
                value={value ?? null}
                inputValue={search}
                inputMode="search"
                placeholder={t('workshopPlanner.schedulePopup.selectAppointmentPlaceholder')}
                onChange={(_, value) => onChange(value)}
                onInputChange={(_, v, reason) => {
                    if (reason !== 'reset') setSearch(v);
                }}
                getOptionSelected={(a) => a.id === value?.id}
                renderOption={(props, a) => (
                    <ListItem {...props}>
                        <AppointmentItem appointment={a} searchQuery={search} />
                    </ListItem>
                )}
            />
        </div>
    );
}

type AppointmentItemProps = {
    appointment: AppointmentListItem;
    searchQuery: string;
};
const AppointmentNumber = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
}));
const AppointmentDescription = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[7],
}));

function AppointmentItem({ searchQuery, appointment }: AppointmentItemProps) {
    return (
        <div>
            <AppointmentNumber>
                <MarkedText text={appointment.number} markText={searchQuery} />
            </AppointmentNumber>
            <br />
            <AppointmentDescription>
                {appointment.customer.firstName} {appointment.customer.lastName} /{' '}
                {appointment.vehicle?.model} {appointment.vehicle?.plates}
            </AppointmentDescription>
        </div>
    );
}
