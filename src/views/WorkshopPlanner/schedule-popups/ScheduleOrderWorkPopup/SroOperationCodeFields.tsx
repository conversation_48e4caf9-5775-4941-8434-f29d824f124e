import { Box } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import WpApi, { StandardOperationDto } from 'api/workshopPlanner';
import { AddOperationIcon } from 'common/components/Icons/AddOperationIcon';
import { RemoveOperationIcon } from 'common/components/Icons/RemoveOperationIcon';
import { TextField } from 'common/components/Inputs';
import DurationForm<PERSON>ield from 'common/components/Inputs/DurationFormField';
import ResettableTextFormField from 'common/components/Inputs/ResettableTextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback } from 'react';
import { useAppSelector } from 'store';
import { selectRepairShopIntegrationAccountName } from 'store/slices/globalSettingsSlice';
import { ActionButtons, AddButton, isDuplicateOperationCode, RemoveButton } from '.';

type SroOperationCodeFieldsProps = {
    id: number;
    standardOperations: StandardOperationDto[];
    orderId?: string;
    field: StandardOperationDto;
    addOperationField: () => void;
    removeOperationField: (_index: number) => void;
    handleOperationCodeChange: (_id: number, _operationCode: string) => void;
    handleStandardTimeChange: (_id: number, _standardTime: number) => void;
    handleOperationCodeDescriptionChange: (_id: number, _description: string) => void;
    width?: {
        operationCode: string;
        standardTime: string;
        operationCodeDescription: string;
    };
    isEditBlock?: boolean;
};
export const SroOperationCodeFields = ({
    id,
    standardOperations,
    orderId,
    field,
    addOperationField,
    removeOperationField,
    handleOperationCodeChange,
    handleStandardTimeChange,
    handleOperationCodeDescriptionChange,
    width,
    isEditBlock = false,
}: SroOperationCodeFieldsProps) => {
    const { t } = useAppTranslation();

    const { mutateAsync: sroMutate, isLoading: isLoadingSro } = useSroMutation((data) => {
        handleStandardTimeChange(id, hoursToMinutes(data.atdSROTime));
        handleOperationCodeDescriptionChange(id, data.sroDescription);
    });

    const handleChangeOperationCode = useCallback(
        async (operationCode: string) => {
            if (
                Array.isArray(standardOperations) &&
                !standardOperations.some((x) => x.operationCode === operationCode)
            ) {
                handleOperationCodeChange(id, operationCode);

                if (orderId) await sroMutate({ orderId, sro: operationCode });
            }
        },
        [orderId, sroMutate, handleOperationCodeChange, id, standardOperations]
    );

    return (
        <>
            <Box
                sx={{
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'flex-end',
                    marginTop: '8px',
                }}
            >
                <Box sx={{ width: width?.operationCode }}>
                    <ResettableTextFormField
                        required
                        isInvalid={isDuplicateOperationCode(
                            standardOperations,
                            field.operationCode,
                            id
                        )}
                        disabled={isLoadingSro || !orderId}
                        showValidationIndicators
                        name={`operationCode-${id}`}
                        label={t('workshopPlanner.schedulePopup.operationCode')}
                        placeholder={t('workshopPlanner.schedulePopup.operationCodePlaceholder')}
                        value={field.operationCode}
                        maxLength={50}
                        onSave={handleChangeOperationCode}
                        size="small"
                    />
                </Box>
                <Box sx={{ width: width?.standardTime }}>
                    <DurationFormField
                        isRequired
                        disabled
                        showValidationIndicators={true}
                        hideValidationIndicators={false}
                        label={t('workshopPlanner.schedulePopup.standardTime')}
                        name={`standardTime-${id}`}
                        value={field.standardTime ?? 0}
                    />
                </Box>
                {!isEditBlock && (
                    <Box sx={{ width: width?.operationCodeDescription }}>
                        <TextField
                            disabled
                            name={`operationCodeDescription-${id}`}
                            label={t('workshopPlanner.schedulePopup.operationCodeDescription')}
                            maxLength={100}
                            placeholder={t(
                                'workshopPlanner.schedulePopup.operationCodeDescriptionPlaceholder'
                            )}
                            value={field.operationCodeDescription}
                        />
                    </Box>
                )}
                <ActionButtons>
                    {id === standardOperations.length - 1 && (
                        <AddButton onClick={addOperationField}>
                            <AddOperationIcon />
                        </AddButton>
                    )}
                    {standardOperations.length > 1 && (
                        <RemoveButton onClick={() => removeOperationField(id)}>
                            <RemoveOperationIcon />
                        </RemoveButton>
                    )}
                </ActionButtons>
            </Box>
            {isEditBlock && (
                <Box sx={{ width: width?.operationCodeDescription }}>
                    <TextField
                        disabled
                        name={`operationCodeDescription-${id}`}
                        label={t('workshopPlanner.schedulePopup.operationCodeDescription')}
                        maxLength={100}
                        placeholder={t(
                            'workshopPlanner.schedulePopup.operationCodeDescriptionPlaceholder'
                        )}
                        value={field.operationCodeDescription}
                    />
                </Box>
            )}
        </>
    );
};

const useSroMutation = (
    onSuccess?: (_data: WpApi.GetSroOperationDto) => void,
    onError?: () => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dmsName = useAppSelector(selectRepairShopIntegrationAccountName);
    const mutation = useMutation(
        (params: { orderId: string; sro: string }) => WpApi.getSroOperation(params),
        {
            onSuccess: (data) => {
                toasters.success(
                    t('workshopPlanner.schedulePopup.sroValidation.success.description', {
                        dmsName,
                    }),
                    t('workshopPlanner.schedulePopup.sroValidation.success.title')
                );
                onSuccess && onSuccess(data);
            },
            onError: (errorMessage) => {
                toasters.danger(
                    t('workshopPlanner.schedulePopup.sroValidation.error.description', {
                        errorMessage,
                    }),
                    t('workshopPlanner.schedulePopup.sroValidation.error.title', {
                        dmsName,
                    })
                );
                onError && onError();
            },
        }
    );

    return mutation;
};

const hoursToMinutes = (hours: number): number => {
    return Math.round(hours * 60);
};
