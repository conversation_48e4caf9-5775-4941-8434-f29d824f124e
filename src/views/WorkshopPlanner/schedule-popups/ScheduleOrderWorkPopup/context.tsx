import useForceRender from 'common/hooks/useForceRender';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import ScheduleOrderWorkPopup from '.';

interface IScheduleOrderPopupContext {
    open(ts: number, userId: string, planningId: number, element: HTMLElement): void;
}

const ScheduleOrderPopupContext = createContext<IScheduleOrderPopupContext | null>(null);

export function useScheduleOrderPopup() {
    const ctx = useContext(ScheduleOrderPopupContext);
    if (!ctx) throw new Error('cannot use ScheduleOrderPopupContext');
    return ctx;
}

export function ScheduleOrderPopupProvider({ children }: React.PropsWithChildren<{}>) {
    const state = useRef({
        ts: 0,
        anchorEl: null as HTMLElement | null,
        userId: '',
        planningId: -1,
    });
    const fr = useForceRender();
    const ctx: IScheduleOrderPopupContext = useMemo(
        () => ({
            open: (ts, userId, planningId, element) => {
                state.current.anchorEl = element;
                state.current.ts = ts;
                state.current.planningId = planningId;
                state.current.userId = userId;
                fr();
            },
        }),
        [fr]
    );

    const close = useCallback(() => {
        state.current.anchorEl = null;
        fr();
    }, [fr]);

    return (
        <ScheduleOrderPopupContext.Provider value={ctx}>
            <ScheduleOrderWorkPopup
                planningId={state.current.planningId}
                ts={state.current.ts}
                onClose={close}
                userId={state.current.userId}
                anchorEl={state.current.anchorEl ?? undefined}
                open={!!state.current.anchorEl}
            />
            {children}
        </ScheduleOrderPopupContext.Provider>
    );
}
