import { Divider } from '@mui/material';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/styles';
import { useQuery } from '@tanstack/react-query';
import { OrderReasonDto, OrderSimpleSearchItemDto } from 'api/orders';
import WpOrdersApi from 'api/workshopPlanner/orders';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useMemo } from 'react';
import Skeleton from 'react-loading-skeleton';
import theme from 'theme';

type OrderDetailsProps = {
    number: string | undefined;
    setOrder: (order: OrderSimpleSearchItemDto | undefined) => void;
    maxWidth?: number | string;
};

const StyledProp = styled(Box)(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[8],
    lineHeight: '1.55em',
}));

const StyledContainer = styled('div')({
    display: 'flex',
    flexGrow: 1,
    flexWrap: 'nowrap',
    flexDirection: 'column',
    fontColor: '#4A4D51',
});

const StyledList = styled('ul')({
    display: 'flex',
    flexDirection: 'column',
    listStyle: 'none',
    paddingLeft: '25px',
    margin: 0,
});

const StyledListItem = styled('li')({
    position: 'relative',
    paddingLeft: '15px',
    '&::before': {
        content: '"•"',
        position: 'absolute',
        left: 0,
        color: 'black',
    },
});

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[8],
}));

const StyledBox = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    fontFamily: 'Inter',
    fontColor: '#4A4D51',
});

export default function OrderDetails({ number, setOrder, maxWidth }: OrderDetailsProps) {
    const { t } = useAppTranslation();
    const { data, isLoading } = useQuery(
        ['wp', 'order', number],
        () => WpOrdersApi.getOrder(number!),
        {
            enabled: !!number,
            staleTime: 10000,
            cacheTime: Infinity,
        }
    );

    const reasonsList = useMemo(() => {
        let internalReasons: OrderReasonDto[] = [];
        let customerReasons: OrderReasonDto[] = [];

        if (data?.orderReasons) {
            data.orderReasons.map((r) => {
                if (r.isFromCustomer) customerReasons.push(r);
                else internalReasons.push(r);
            });
        }
        return { customerReasons, internalReasons };
    }, [data]);

    const customerNotes = useMemo(
        () => (data?.notes ? data.notes.filter((x) => x.type === 'ForCustomer') : []),
        [data]
    );

    const internalNotes = useMemo(
        () => (data?.notes ? data.notes.filter((x) => x.type === 'ForInternal') : []),
        [data]
    );

    if (!number) return null;

    if (isLoading || !data) {
        return (
            <Grid container spacing={1} style={{ height: 106 }}>
                <Grid item>
                    <Skeleton width={300} />
                    <Skeleton width={200} height={12} />
                    <Skeleton width={100} height={12} />
                    <Skeleton width={160} height={12} />
                    <Skeleton width={230} height={12} />
                </Grid>
                <Grid item>
                    <Skeleton width={230} height={12} />
                </Grid>
            </Grid>
        );
    }

    const handleChangeOrderClick = () => {
        setOrder(undefined);
    };

    return (
        <Box maxWidth={maxWidth}>
            <Box display="flex" flexDirection="row">
                <StyledContainer>
                    <Title>
                        <b>
                            {t('commonLabels.order')} #{data.number}{' '}
                        </b>
                        <Button
                            customStyles={{
                                background: 'none',
                                border: 'none',
                                color: 'var(--cm1)',
                                textDecoration: 'underline',
                                cursor: 'pointer',
                                padding: 0,
                                height: 15,
                                font: 'inherit',
                                ...theme.typography.h8Inter,
                            }}
                            onClick={handleChangeOrderClick}
                        >
                            {t('workshopPlanner.schedulePopup.changeOrder')}
                        </Button>
                    </Title>
                    <Box sx={{ display: 'flex' }}>
                        <StyledProp sx={{ flexBasis: '50%' }}>
                            <b>{t('workshopPlanner.schedulePopup.customerName')}: </b>
                            {data.customer?.firstName || data.customer?.lastName
                                ? `${data.customer.firstName || ''} ${
                                      data.customer.lastName || ''
                                  }`.trim()
                                : '--'}
                        </StyledProp>
                        <StyledProp>
                            <b>{t('workshopPlanner.schedulePopup.orderDateAndTime')}: </b>
                            {data.deliveryDate
                                ? DateTime.fromISO(data.deliveryDate).toFormat('dd/MM/yy - HH:mm') +
                                  ' hrs.'
                                : '--'}
                        </StyledProp>
                    </Box>

                    <StyledProp>
                        <b>{t('commonLabels.vehicle')}: </b>
                        {[data.vehicle?.brand, data.vehicle?.model, data.vehicle?.year]
                            .filter(Boolean)
                            .join(' ') || '--'}
                        {data.vehicle?.plates ? `, ${data.vehicle.plates}` : ''}
                    </StyledProp>

                    <StyledProp>
                        <b>{t('commonLabels.vin')}: </b>
                        {data.vehicle?.vin || '--'}
                    </StyledProp>
                </StyledContainer>
            </Box>
            <Divider style={{ margin: '8px 0' }} />{' '}
            <OverlayScrollbarsComponent style={{ maxHeight: '120px' }}>
                <StyledBox>
                    <StyledProp>
                        {isLoading && !!data ? (
                            <Skeleton width={200} />
                        ) : (
                            <>
                                <StyledProp>
                                    <b>{t('orderDetails.orderReasons.customerReasonsForVisit')}:</b>
                                    {reasonsList.customerReasons.length ? (
                                        <StyledList>
                                            {reasonsList.customerReasons.map((reason, index) => (
                                                <StyledListItem key={index}>
                                                    {reason.text}
                                                </StyledListItem>
                                            ))}
                                        </StyledList>
                                    ) : (
                                        ` ${t(
                                            'orderDetails.orderReasons.noCustomerReasonsForVisit'
                                        )}`
                                    )}
                                </StyledProp>
                                <StyledProp>
                                    <b>{t('orderDetails.orderReasons.workshopReasonsForVisit')}:</b>
                                    {reasonsList.internalReasons.length ? (
                                        <StyledList>
                                            {reasonsList.internalReasons.map((reason, index) => (
                                                <StyledListItem key={index}>
                                                    {reason.text}
                                                </StyledListItem>
                                            ))}
                                        </StyledList>
                                    ) : (
                                        ` ${t(
                                            'orderDetails.orderReasons.noWorkshopReasonsForVisit'
                                        )}`
                                    )}
                                </StyledProp>
                            </>
                        )}
                    </StyledProp>
                </StyledBox>
            </OverlayScrollbarsComponent>
            <Divider style={{ margin: '8px 0' }} />
            <OverlayScrollbarsComponent style={{ maxHeight: '120px' }}>
                <StyledBox>
                    <StyledProp>
                        <b>{t('orderDetails.additionalNotes.notesForCustomer')}:</b>
                        {customerNotes.filter((note) => note.text.trim().length > 0).length ? (
                            <StyledList>
                                {customerNotes.map((note, index) => (
                                    <StyledListItem key={index}>{note.text}</StyledListItem>
                                ))}
                            </StyledList>
                        ) : (
                            ` ${t('orderDetails.additionalNotes.noNotesForCustomer')}`
                        )}
                    </StyledProp>
                    <StyledProp>
                        <b>{t('orderDetails.additionalNotes.notesForInternal')}:</b>
                        {internalNotes.filter((note) => note.text.trim().length > 0).length ? (
                            <StyledList>
                                {internalNotes.map((note, index) => (
                                    <StyledListItem key={index}>{note.text}</StyledListItem>
                                ))}
                            </StyledList>
                        ) : (
                            ` ${t('orderDetails.additionalNotes.noInternalNotes')}`
                        )}
                    </StyledProp>
                </StyledBox>
            </OverlayScrollbarsComponent>
            <Divider style={{ margin: '8px 0 8px 0' }} />
        </Box>
    );
}
