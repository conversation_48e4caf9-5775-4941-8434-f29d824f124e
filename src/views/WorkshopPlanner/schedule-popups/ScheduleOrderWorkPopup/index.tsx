import { Box, Divider, Grid, styled, useMediaQuery } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { isCmosError } from 'api/error';
import OrdersApi, { OrderSimpleSearchItemDto } from 'api/orders';
import { UserListItem } from 'api/users';
import { Button } from 'common/components/Button';
import DateFormField from 'common/components/Inputs/DateFormField';
import DurationForm<PERSON>ield from 'common/components/Inputs/DurationFormField';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectUser } from 'store/slices/users';
import { ensureOrderIsPresentThunk } from 'store/slices/wp/orders';
import {
    createOrderBlockThunk,
    loadScheduleThunk,
    planningsActions,
    selectPlanning,
} from 'store/slices/wp/plannings';
import { useMaxAvailableTimeInMinutes } from 'store/util/wp';
import { isDateValid } from 'utils';
import UTInput from 'views/WorkshopPlanner/common/UTInput';
import { useTechnicianCapacityAlert } from 'views/WorkshopPlanner/TechnicianCapacity/TechnicianCapacityAlert';
import LatestSelector from '../LatestSelector';
import OrderNumberAutocomplete from '../OrderNumberAutocomplete';
import SchedulePopupLayout, { SchedulePopupLayoutProps } from '../SchedulePopupLayout';
import OrderDetails from './OrderDetails';

import { JobDescriptionDto, StandardOperationDto, WpOrdersApi } from 'api/workshopPlanner';
import { AddOperationIcon } from 'common/components/Icons/AddOperationIcon';
import { RemoveOperationIcon } from 'common/components/Icons/RemoveOperationIcon';
import { TextField } from 'common/components/Inputs';
import { JobTitle, jobTitleLabel } from 'common/constants';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { TFunction } from 'i18next';
import { isErrorResponse } from 'services/Server';
import { selectRepairShopWpConfiguration, selectSettings } from 'store/slices/globalSettingsSlice';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import { isStandardOperationsAllowed } from 'views/WorkshopPlanner/helpers';
import { useIsSchedulingAllowed } from 'views/WorkshopPlanner/TechnicianCapacity/helpers';
import OrderDetailsTooltip from '../../common/OrderDetailsTooltip';
import JobDescriptionsAutocomplete from '../JobDescriptionAutocomplete';
import JobDescriptionsList from '../JobDescriptionList';
import { SroOperationCodeFields } from './SroOperationCodeFields';

type ScheduleOrderWorkPopupProps = Omit<SchedulePopupLayoutProps, 'title' | 'slotProps'> & {
    userId: string;
    planningId: number;
};

const ErrorText = styled('p')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--danger)',
}));

const JobDescriptionText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    marginBottom: '6px',
    marginTop: '17px',
    color: 'var(--neutral8)',
}));

const StandardOperationsSection = styled(Box)({
    maxHeight: 198,
    width: '100%',
    overflowY: 'auto',
    paddingLeft: '8px',
    scrollbarGutter: 'stable',
    ...scrollbarStyle(),
});

export const ActionButtons = styled(Box)({
    width: '8%',
    display: 'flex',
    alignItems: 'center',
    gap: 0,
});

export const AddButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    backgroundColor: 'var(--primary)',
    color: '#fff',
    padding: 2,
    borderRadius: 4,
    cursor: 'pointer',
}));

export const RemoveButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    backgroundColor: 'var(--error)',
    color: '#fff',
    padding: 2,
    marginRight: -4,
    borderRadius: 4,
    cursor: 'pointer',
}));

const DEFAULT_SCHEDULED_DURATION_MINUTES = 60;

const getPhaseLocalization = (t: TFunction, phaseName: string) =>
    (phaseName
        ? ['noPhase', 'closedOrder'].includes(phaseName)
            ? t(`workshopPlanner.phases.${phaseName}`)
            : phaseName
        : ''
    ).toUpperCase();

export default function ScheduleOrderWorkPopup({
    userId,
    planningId,
    onClose,
    ts: initialTs,
    ...props
}: ScheduleOrderWorkPopupProps) {
    const { t } = useAppTranslation();
    const planning = useAppSelector((r) => selectPlanning(r, planningId));
    const user: UserListItem | null = useAppSelector((r) => selectUser(r, userId) ?? null);
    const wpSettings = useAppSelector(selectShopWpSettings);
    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const { data: latestOrders } = useQuery(
        ['orders', 'latest-numbers'],
        () => OrdersApi.getLatest(),
        {
            cacheTime: Infinity,
            staleTime: 10000,
            enabled: props.open,
        }
    );

    const { data: ordersTooltipInfo } = useQuery(
        ['orders', 'latest-order-info'],
        () => WpOrdersApi.getLatestOrdersInfo(),
        {
            cacheTime: Infinity,
            staleTime: 10000,
            enabled: props.open,
        }
    );

    const [order, setOrder] = useState<OrderSimpleSearchItemDto>();
    const [duration, setDuration] = useState(0);
    const [selectedInspectionItems, setSelectedInspectionItems] = useState<JobDescriptionDto[]>([]);
    const [date, setDate] = useState(new Date());
    const [time, setTime] = useState<[number, number]>([0, 0]);

    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed({ hours: duration / 60, userId });

    const [isSaving, setIsSaving] = useState<boolean>(false);

    const dt = useMemo(() => {
        const dt = DateTime.fromJSDate(date).set({
            hour: time[0],
            minute: time[1],
        });
        return dt;
    }, [date, time]);
    const ts = dt.toMillis();
    const maxDuration = useMaxAvailableTimeInMinutes(user, dt);

    useEffect(() => {
        const dt = DateTime.fromMillis(initialTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
    }, [initialTs]);

    const handleClosed = useCallback(() => {
        const dt = DateTime.fromMillis(initialTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
        setIsSaving(false);
    }, [initialTs]);

    const globalSettings = useAppSelector(selectSettings);

    const standardOperationOrderTypes = useMemo(() => {
        try {
            return JSON.parse(globalSettings.standardOperationOrderTypes) ?? [];
        } catch (error) {
            console.error('Error parsing standardOperationOrderTypes:', error);
            return [];
        }
    }, [globalSettings.standardOperationOrderTypes]);

    const sroValidationIntegration = useMemo(
        () => !!globalSettings.repairShopSettings?.features.sroValidationIntegration,
        [globalSettings]
    );

    const [canUseStandardOperations, setCanUseStandardOperations] = useState<boolean>(false);

    useEffect(() => {
        setCanUseStandardOperations(
            isStandardOperationsAllowed(
                !!order,
                !order?.type,
                globalSettings.enableStandardOperations,
                standardOperationOrderTypes,
                order?.type?.key
            )
        );
    }, [order, globalSettings.enableStandardOperations, standardOperationOrderTypes]);

    const [standardOperations, setStandardOperations] = useState<StandardOperationDto[]>([]);

    const addOperationField = () => {
        setStandardOperations((prev) => [...prev, { operationCode: '', standardTime: 0 }]);
    };

    const removeOperationField = (index: number) => {
        setStandardOperations((prev) => {
            const updated = prev.filter((field, ind) => ind !== index);
            setDuration(calculateScheduledDuration(updated));
            return updated;
        });
    };

    const handleOperationCodeChange = (index: number, newCode: string) => {
        setStandardOperations((prev) =>
            prev.map((item, ind) => (ind === index ? { ...item, operationCode: newCode } : item))
        );
    };

    const handleStandardTimeChange = (index: number, newTime: number) => {
        setStandardOperations((prev) => {
            const updated = prev.map((item, ind) =>
                ind === index ? { ...item, standardTime: newTime } : item
            );

            setDuration(calculateScheduledDuration(updated));
            return updated;
        });
    };

    const handleOperationCodeDescriptionChange = (index: number, newDescription: string) => {
        setStandardOperations((prev) =>
            prev.map((item, ind) =>
                ind === index ? { ...item, operationCodeDescription: newDescription } : item
            )
        );
    };

    const calculateScheduledDuration = (fields: StandardOperationDto[]) => {
        return fields.reduce((total, field) => total + (field.standardTime ?? 0), 0);
    };

    const allOperationCodesFilled = canUseStandardOperations
        ? standardOperations.every(
              (field) => field.operationCode.trim() !== '' && field.standardTime > 0
          )
        : true;

    const allOperationCodesDescriptionFilled = canUseStandardOperations
        ? standardOperations.every(
              (field) =>
                  !field.operationCodeDescription || field.operationCodeDescription.length <= 100
          )
        : true;

    const valid =
        allOperationCodesFilled &&
        allOperationCodesDescriptionFilled &&
        duration >= 1 &&
        duration <= maxDuration &&
        !!order;
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const createBlock = useMutation(
        async () => {
            if (!valid) return;

            const result = await dispatch(
                createOrderBlockThunk({
                    userId,
                    orderId: order.id,
                    duration,
                    position: ts,
                    planningId,
                    standardOperations: canUseStandardOperations ? standardOperations : undefined,
                    jobDescriptionRecords: selectedInspectionItems,
                })
            );

            if (result.meta.requestStatus === 'rejected') {
                if (result.meta.rejectedWithValue) {
                    const e = result.payload;
                    if (isCmosError(e)) {
                        console.error('[wp>planned order scheduling]', e);
                    } else {
                        console.error('[wp>planned order scheduling] non cmos error', e);
                    }
                    throw e;
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                    throw new Error('unknown error');
                }
            }
        },
        {
            onSuccess: () => {
                if (!order) {
                    console.error('appointmentItem is not set');
                } else {
                    dispatch(ensureOrderIsPresentThunk({ id: order.id }));
                }
                dispatch(planningsActions.removePreview());
                dispatch(loadScheduleThunk({}));
                toasters.success(
                    t('workshopPlanner.schedulePopup.orderCreated.text', {
                        orderNumber: order?.number,
                    }),
                    t('workshopPlanner.schedulePopup.orderCreated.title')
                );
                if (enableWorkshopJobIntegration && integrationAccountName) {
                    toasters.success(
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.title')
                    );
                }
                onClose();
            },
            onError: (error) => {
                if (isErrorResponse(error)) {
                    if (enableWorkshopJobIntegration && integrationAccountName) {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.text', {
                                errorMessage: error.message,
                            }),
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.title', {
                                integrationAccountName,
                            })
                        );
                    }
                }
            },
        }
    );

    useEffect(() => {
        if (props.open) {
            dispatch(
                planningsActions.setPreview({
                    userId,
                    duration,
                    ts,
                    maxDuration,
                })
            );
        }
    }, [props.open, dispatch, duration, userId, ts, maxDuration]);

    useEffect(() => {
        setSelectedInspectionItems([]);
    }, [order?.number]);

    useEffect(() => {
        if (props.open) {
            setOrder(undefined);
        }
    }, [props.open]);

    useEffect(() => {
        if (props.open) {
            const duration = canUseStandardOperations
                ? 0
                : wpSettings?.defaultScheduledDurationMinutes ?? DEFAULT_SCHEDULED_DURATION_MINUTES;

            setDuration(duration);
            setStandardOperations([
                { operationCode: '', standardTime: 0, operationCodeDescription: '' },
            ]);
        } else {
            dispatch(planningsActions.removePreview());
        }
    }, [canUseStandardOperations, props.open, dispatch, wpSettings]);

    const scrollRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
    }, [standardOperations]);

    const isSmall = useMediaQuery('(max-height: 820px)');
    const popupStyles = canUseStandardOperations
        ? {
              main: {
                  style: {
                      paddingRight: 0,
                      ...(isSmall
                          ? {
                                maxHeight: '90vh',
                                overflow: 'auto',
                            }
                          : {}),
                  },
              },
          }
        : {};

    return (
        <SchedulePopupLayout
            slotProps={{
                main: popupStyles.main,
            }}
            circleColor={user?.color}
            ts={ts}
            onClose={onClose}
            onClosed={handleClosed}
            title={t('workshopPlanner.schedulePopup.orderTitle', {
                userPosition: user?.job ? t(jobTitleLabel(user?.job as JobTitle)) : user?.job,
                userName: user?.name,
            })}
            {...props}
        >
            {!order && (
                <>
                    <OrderNumberAutocomplete value={order} onChange={setOrder}>
                        <LatestSelector<OrderSimpleSearchItemDto>
                            value={order}
                            options={latestOrders ?? []}
                            onSelected={setOrder}
                            getLabel={(x) => '#' + x.number}
                            tooltip={(x) => {
                                const orderInfo = ordersTooltipInfo?.find(
                                    (order) => order.id === x.id
                                );
                                if (!orderInfo) {
                                    return <></>;
                                }
                                return (
                                    <OrderDetailsTooltip
                                        tower={orderInfo.tower}
                                        towerColor={orderInfo.towerColor}
                                        orderNumber={orderInfo.orderNumber}
                                        customerFirstName={orderInfo.customerFirstName || ''}
                                        customerLastName={orderInfo.customerLastName || ''}
                                        model={orderInfo.model || ''}
                                        plates={orderInfo.plates || ''}
                                        orderType={orderInfo.orderType || ''}
                                        deliveryDate={orderInfo.deliveryDate || null}
                                        phase={getPhaseLocalization(t, orderInfo.phase)}
                                        serviceAdvisorName={orderInfo.serviceAdvisorName || ''}
                                        notes={orderInfo.notes || []}
                                        orderReasons={orderInfo.orderReasons}
                                        jobDescriptions={orderInfo.jobDescriptions}
                                    />
                                );
                            }}
                        />
                    </OrderNumberAutocomplete>
                    <Divider style={{ margin: '8px 0' }} />{' '}
                </>
            )}
            <OrderDetails
                number={order?.number}
                setOrder={setOrder}
                maxWidth={canUseStandardOperations ? '90%' : undefined}
            />
            <Grid container spacing={1}>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <DateFormField
                        label={t('workshopPlanner.editBlock.startDate')}
                        value={date}
                        onChange={(v) => {
                            if (v && isDateValid(v)) setDate(v);
                        }}
                        isRequired
                        showValidationIndicators
                    />
                </Grid>

                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <TimeFormField
                        value={time}
                        onChange={setTime}
                        label={t('workshopPlanner.editBlock.startHour')}
                        isRequired
                        showValidationIndicators
                    />
                </Grid>
                {canUseStandardOperations && (
                    <StandardOperationsSection ref={scrollRef}>
                        {standardOperations.map((field, index) => (
                            <>
                                {sroValidationIntegration ? (
                                    <SroOperationCodeFields
                                        key={index}
                                        id={index}
                                        {...{
                                            standardOperations,
                                            orderId: order?.id,
                                            field,
                                            addOperationField,
                                            removeOperationField,
                                            handleOperationCodeChange,
                                            handleStandardTimeChange,
                                            handleOperationCodeDescriptionChange,
                                            width: {
                                                operationCode: '21.88%',
                                                standardTime: '21.88%',
                                                operationCodeDescription: '350.5px',
                                            },
                                        }}
                                    />
                                ) : (
                                    <Box
                                        key={index}
                                        sx={{
                                            display: 'flex',
                                            gap: '8px',
                                            alignItems: 'flex-end',
                                            marginTop: '8px',
                                        }}
                                    >
                                        <Box sx={{ width: '21.88%' }}>
                                            <TextField
                                                isRequired={true}
                                                isInvalid={isDuplicateOperationCode(
                                                    standardOperations,
                                                    field.operationCode,
                                                    index
                                                )}
                                                showValidationIndicators
                                                name={`operationCode-${index}`}
                                                label={t(
                                                    'workshopPlanner.schedulePopup.operationCode'
                                                )}
                                                placeholder={t(
                                                    'workshopPlanner.schedulePopup.operationCodePlaceholder'
                                                )}
                                                value={field.operationCode}
                                                onChange={(e) =>
                                                    handleOperationCodeChange(index, e.target.value)
                                                }
                                                maxLength={50}
                                            />
                                        </Box>
                                        <Box sx={{ width: '21.88%' }}>
                                            <DurationFormField
                                                isRequired={true}
                                                showValidationIndicators={true}
                                                hideValidationIndicators={false}
                                                label={t(
                                                    'workshopPlanner.schedulePopup.standardTime'
                                                )}
                                                name={`standardTime-${index}`}
                                                value={field.standardTime ?? 0}
                                                onChange={(value) =>
                                                    handleStandardTimeChange(index, value)
                                                }
                                            />
                                        </Box>
                                        <Box sx={{ width: '351.09px' }}>
                                            <TextField
                                                name={`operationCodeDescription-${index}`}
                                                label={t(
                                                    'workshopPlanner.schedulePopup.operationCodeDescription'
                                                )}
                                                maxLength={100}
                                                placeholder={t(
                                                    'workshopPlanner.schedulePopup.operationCodeDescriptionPlaceholder'
                                                )}
                                                value={field.operationCodeDescription}
                                                onChange={(e) =>
                                                    handleOperationCodeDescriptionChange(
                                                        index,
                                                        e.target.value
                                                    )
                                                }
                                            />
                                        </Box>
                                        <ActionButtons>
                                            {index === standardOperations.length - 1 && (
                                                <AddButton onClick={addOperationField}>
                                                    <AddOperationIcon />
                                                </AddButton>
                                            )}
                                            {standardOperations.length > 1 && (
                                                <RemoveButton
                                                    onClick={() => removeOperationField(index)}
                                                >
                                                    <RemoveOperationIcon />
                                                </RemoveButton>
                                            )}
                                        </ActionButtons>
                                    </Box>
                                )}
                            </>
                        ))}
                    </StandardOperationsSection>
                )}
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <DurationFormField
                        isRequired
                        showValidationIndicators
                        value={duration}
                        label={t('workshopPlanner.schedulePopup.duration')}
                        name={'duration'}
                        onChange={setDuration}
                        disabled={canUseStandardOperations}
                    />
                </Grid>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <UTInput
                        disabled={!planning?.isTechnicians || canUseStandardOperations}
                        durationInSeconds={duration * 60}
                        onChange={(v) => setDuration(Math.floor(v / 60))}
                    />
                </Grid>
            </Grid>
            {props.open && !createBlock.isLoading && duration > maxDuration ? (
                <ErrorText>{t('workshopPlanner.schedulePopup.exceedsDuration')}</ErrorText>
            ) : (
                <></>
            )}
            <Box>
                <JobDescriptionText>
                    {t('workshopPlanner.schedulePopup.jobDescription')}
                </JobDescriptionText>
                <Box display={'flex'} flexDirection={'column'} marginLeft={3}>
                    <Box width={canUseStandardOperations ? '89.5%' : '100%'}>
                        {selectedInspectionItems.length > 0 && (
                            <JobDescriptionsList
                                onRemoveJobDescription={(inspectionItemId) => {
                                    setSelectedInspectionItems((prevItems) =>
                                        prevItems.filter((item) => item.id !== inspectionItemId)
                                    );
                                }}
                                selectedJobDescriptions={selectedInspectionItems}
                            />
                        )}
                        <JobDescriptionsAutocomplete
                            cacheKey={order?.number ? order?.number : ''}
                            getJobDescriptions={() =>
                                order?.number ? WpOrdersApi.getInspectionItems(order?.number) : []
                            }
                            excludeItems={selectedInspectionItems}
                            onChange={(inspectionItem) => {
                                setSelectedInspectionItems((prevItems) => [
                                    ...prevItems,
                                    inspectionItem,
                                ]);
                            }}
                        />
                    </Box>
                </Box>
            </Box>
            <Box
                display="flex"
                justifyContent="end"
                gap={1.25}
                marginTop={3}
                marginRight={canUseStandardOperations ? 10 : 0}
            >
                <Button
                    onClick={onClose}
                    w="sm"
                    label={t('commonLabels.cancel')}
                    color={Colors.Neutral3}
                />
                <Button
                    disabled={!valid || !isSchedulingAllowed || isSaving}
                    onClick={() => {
                        setIsSaving(true);
                        technicianCapacityAlertPopup.checkTechnicianCapacity(createBlock.mutate, {
                            hours: duration / 60,
                            userId: userId,
                        });
                    }}
                    showLoader={createBlock.isLoading}
                    w="sm"
                    label={t('commonLabels.save')}
                />
            </Box>
        </SchedulePopupLayout>
    );
}

export function isDuplicateOperationCode(
    standardOperations: StandardOperationDto[],
    currentCode: string,
    currentIndex: number
) {
    return (
        Array.isArray(standardOperations) &&
        standardOperations.some(
            (item, idx) => item.operationCode.trim() === currentCode.trim() && idx !== currentIndex
        )
    );
}
