export function scrollToUserRow(planningId: number, userId: string) {
    const element = document.getElementById(`planning-user-row-${planningId}-${userId})`);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setTimeout(() => {
            element.classList.add('focused');
            setTimeout(() => {
                element.classList.remove('focused');
            }, 1500);
        }, 500);
    }
}
