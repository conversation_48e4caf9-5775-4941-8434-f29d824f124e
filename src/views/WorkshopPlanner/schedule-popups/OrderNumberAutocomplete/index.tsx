import { ListItem, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import OrdersApi, { OrderSimpleSearchItemDto } from 'api/orders';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import { MarkedText } from 'common/components/MarkedText';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';

type AppointmentNumberAutocompleteProps = {
    value?: OrderSimpleSearchItemDto;
    onChange: (value: OrderSimpleSearchItemDto) => void;
    children?: React.ReactNode;
};

export default function AppointmentNumberAutocomplete({
    value,
    onChange,
    children,
}: AppointmentNumberAutocompleteProps) {
    const { t } = useAppTranslation();
    const [search, setSearch] = useState('');
    const [debouncedSearch] = useDebounce(search, 200);
    const { data, isLoading } = useQuery(
        ['orders', 'simple-search', debouncedSearch],
        () => OrdersApi.simpleSearch(debouncedSearch),
        {
            keepPreviousData: true,
        }
    );
    const options = useMemo(() => {
        if (!data) return [];
        if (!value) return data;
        if (data.find((x) => x.id === value.id)) return data;
        return [value, ...data];
    }, [data, value]);

    useEffect(() => {
        setSearch(value?.number ?? '');
    }, [value]);

    return (
        <div>
            <InputLabel isRequired showValidationIndicators>
                {t('workshopPlanner.schedulePopup.selectOrder')}
            </InputLabel>
            {children}
            <SAutocomplete<OrderSimpleSearchItemDto>
                style={{ width: 300 }}
                options={options}
                loading={isLoading}
                getOptionLabel={(a) => a!.number}
                autoComplete
                value={value ?? null}
                inputValue={search}
                inputMode="search"
                placeholder={t('workshopPlanner.schedulePopup.selectOrderPlaceholder')}
                onChange={(_, value) => onChange(value)}
                onInputChange={(_, v, reason) => {
                    if (reason !== 'reset') setSearch(v);
                }}
                getOptionSelected={(a) => a.id === value?.id}
                renderOption={(props, o) => (
                    <ListItem {...props}>
                        <OrderItem order={o} searchQuery={search} />
                    </ListItem>
                )}
            />
        </div>
    );
}

type OrderItemProps = {
    order: OrderSimpleSearchItemDto;
    searchQuery: string;
};
const Number = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
}));
const Description = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[7],
}));

function OrderItem({ searchQuery, order }: OrderItemProps) {
    return (
        <div>
            <Number>
                <MarkedText text={order.number} markText={searchQuery} />
            </Number>
            <br />
            <Description>
                {order.customer.firstName} {order.customer.lastName} / {order.vehicle.model}{' '}
                {order.vehicle.plates}
            </Description>
        </div>
    );
}
