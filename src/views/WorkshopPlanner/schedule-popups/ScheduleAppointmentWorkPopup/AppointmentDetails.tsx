import { Divider, Grid, styled } from '@mui/material';
import Box from '@mui/material/Box';
import { useQuery } from '@tanstack/react-query';
import AppointmentsApi, { AppointmentListItem } from 'api/appointments';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useMemo } from 'react';
import Skeleton from 'react-loading-skeleton';
import theme from 'theme';

type AppointmentDetailsProps = {
    id: string | undefined;
    setAppointment: (appointment: AppointmentListItem | undefined) => void;
};

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[8],
}));

const Prop = styled('div')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[8],
    lineHeight: '1.55em',
}));

const StyledList = styled('ul')({
    display: 'flex',
    flexDirection: 'column',
    listStyle: 'none',
    paddingLeft: '25px',
    margin: 0,
});

const StyledListItem = styled('li')({
    position: 'relative',
    paddingLeft: '15px',
    '&::before': {
        content: '"•"',
        position: 'absolute',
        left: 0,
        color: 'black',
    },
});

export default function AppointmentDetails({ id, setAppointment }: AppointmentDetailsProps) {
    const { t } = useAppTranslation();
    const { data, isLoading } = useQuery(['appointment', id], () => AppointmentsApi.get(id!), {
        enabled: !!id,
        staleTime: 10000,
        cacheTime: Infinity,
    });

    const reasonsList = useMemo(() => {
        const reasons = data?.reasons.map((x) => x.name) || [];
        const customReasons =
            data?.customReasons.map((x) => `${x.name}${x.details ? ` - ${x.details.name}` : ''}`) ||
            [];
        return [...reasons, ...customReasons];
    }, [data]);

    const customerNotes = useMemo(
        () => (data?.notes ? data.notes.filter((x) => x.type === 'ForCustomer') : []),
        [data]
    );

    const internalNotes = useMemo(
        () => (data?.notes ? data.notes.filter((x) => x.type === 'ForInternal') : []),
        [data]
    );

    if (!id) return null;

    if (isLoading || !data) {
        return (
            <Grid container spacing={1} style={{ height: 106 }}>
                <Grid item>
                    <Skeleton width={300} />
                    <Skeleton width={200} height={12} />
                    <Skeleton width={100} height={12} />
                    <Skeleton width={160} height={12} />
                    <Skeleton width={230} height={12} />
                </Grid>
                <Grid item>
                    <Skeleton width={230} height={12} />
                </Grid>
            </Grid>
        );
    }

    const handleChangeAppointmentClick = () => {
        setAppointment(undefined);
    };

    return (
        <>
            <Box display="flex" flexDirection="row">
                <Box flexGrow="1">
                    <Title>
                        <strong>
                            {t('commonLabels.appointment')} #{data.number}
                        </strong>{' '}
                        <Button
                            customStyles={{
                                background: 'none',
                                border: 'none',
                                color: 'var(--cm1)',
                                textDecoration: 'underline',
                                cursor: 'pointer',
                                padding: 0,
                                height: 15,
                                font: 'inherit',
                                ...theme.typography.h8Inter,
                            }}
                            onClick={handleChangeAppointmentClick}
                        >
                            {t('workshopPlanner.schedulePopup.changeAppointment')}
                        </Button>
                    </Title>
                    <Box sx={{ display: 'flex' }}>
                        <Prop sx={{ flexBasis: '50%' }}>
                            <b>{t('workshopPlanner.schedulePopup.customerName')}: </b>
                            {data.customer?.firstName || data.customer?.lastName
                                ? `${data.customer.firstName || ''} ${
                                      data.customer.lastName || ''
                                  }`.trim()
                                : '--'}
                        </Prop>

                        <Prop>
                            <b>{t('workshopPlanner.schedulePopup.appointmentDateAndTime')}: </b>
                            {DateTime.fromFormat(
                                `${data.startsAt.date} ${data.startsAt.time}`,
                                'yyyy-MM-dd HH:mm:ss'
                            ).toFormat('dd/MM/yy - HH:mm') + ' hrs.'}
                        </Prop>
                    </Box>
                    <Prop>
                        <b>{t('commonLabels.vehicle')}: </b>
                        {[data.vehicle?.brand, data.vehicle?.model, data.vehicle?.year]
                            .filter(Boolean)
                            .join(' ') || '--'}
                        {data.vehicle?.plates ? `, ${data.vehicle.plates}` : ''}
                    </Prop>
                    <Prop>
                        <b>{t('commonLabels.vin')}: </b>
                        {data.vehicle?.vin || '--'}
                    </Prop>
                </Box>
            </Box>
            <Divider style={{ margin: '8px 0' }} />
            <OverlayScrollbarsComponent style={{ maxHeight: '120px' }}>
                <Prop>
                    <b>{t('workshopPlanner.orderPopup.reasonForAppointment')}: </b>
                    <>
                        {reasonsList.length ? (
                            <StyledList>
                                {reasonsList.map((reason, index) => (
                                    <StyledListItem key={index}>{reason}</StyledListItem>
                                ))}
                            </StyledList>
                        ) : (
                            ' --'
                        )}
                    </>
                </Prop>
            </OverlayScrollbarsComponent>
            <Divider style={{ margin: '8px 0' }} />
            <OverlayScrollbarsComponent style={{ maxHeight: '120px' }}>
                <Prop style={{ whiteSpace: 'pre-wrap' }}>
                    <b>{t('appointments.notesVisibleForCustomer')}:</b>
                    {customerNotes.filter((note) => note.note.trim().length > 0).length ? (
                        <StyledList>
                            {customerNotes.map((note, index) => (
                                <StyledListItem key={index}>{note.note}</StyledListItem>
                            ))}
                        </StyledList>
                    ) : (
                        ` ${t('appointments.noNotesForCustomer')}`
                    )}
                </Prop>
                <Prop style={{ whiteSpace: 'pre-wrap' }}>
                    <b>{t('appointments.internalNotes')}:</b>
                    {internalNotes.filter((note) => note.note.trim().length > 0).length ? (
                        <StyledList>
                            {internalNotes.map((note, index) => (
                                <StyledListItem key={index}>{note.note}</StyledListItem>
                            ))}
                        </StyledList>
                    ) : (
                        ` ${t('appointments.noInternalNotes')}`
                    )}
                </Prop>
            </OverlayScrollbarsComponent>
            <Divider style={{ margin: '8px 0' }} />
        </>
    );
}
