import { Box, Divider, Grid, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import AppointmentsApi, { AppointmentListItem, AppointmentStatus } from 'api/appointments';
import { isCmosError } from 'api/error';
import { UserListItem } from 'api/users';
import { JobDescriptionDto } from 'api/workshopPlanner';
import WpAppointmentsApi from 'api/workshopPlanner/appointments';
import { Button } from 'common/components/Button';
import DateFormField from 'common/components/Inputs/DateFormField';
import DurationFormField from 'common/components/Inputs/DurationFormField';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { JobTitle, jobTitleLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { TFunction } from 'i18next';
import { DateTime } from 'luxon';
import moment from 'moment/moment';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { selectRepairShopWpConfiguration } from 'store/slices/globalSettingsSlice';
import { selectUser } from 'store/slices/users';
import {
    createPlannedBlockThunk,
    loadScheduleThunk,
    planningsActions,
    selectPlanning,
} from 'store/slices/wp/plannings';
import { selectShopWpSettings } from 'store/slices/wp/settings/selectors';
import { useMaxAvailableTimeInMinutes } from 'store/util/wp';
import { isDateValid } from 'utils';
import UTInput from 'views/WorkshopPlanner/common/UTInput';
import { useIsSchedulingAllowed } from 'views/WorkshopPlanner/TechnicianCapacity/helpers';
import { useTechnicianCapacityAlert } from 'views/WorkshopPlanner/TechnicianCapacity/TechnicianCapacityAlert';
import AppointmentDetailsTooltip from '../../common/AppointmentDetailsTooltip';
import AppointmentNumberAutoselect from '../AppointmentNumberAutoselect';
import JobDescriptionsAutocomplete from '../JobDescriptionAutocomplete';
import JobDescriptionsList from '../JobDescriptionList';
import LatestSelector from '../LatestSelector';
import SchedulePopupLayout, { SchedulePopupLayoutProps } from '../SchedulePopupLayout';
import AppointmentDetails from './AppointmentDetails';

type ScheduleAppointmentWorkAltPopupProps = Omit<
    SchedulePopupLayoutProps,
    'title' | 'circleColor'
> & {
    userId: string;
    planningId: number;
};

const ErrorText = styled('p')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--danger)',
}));

const JobDescriptionText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    marginBottom: '6px',
    marginTop: '17px',
    color: 'var(--neutral8)',
}));

const DEFAULT_SCHEDULED_DURATION_MINUTES = 60;

export function mapStatus(
    t: TFunction,
    status: AppointmentStatus,
    orderNumber: string | undefined
): string {
    switch (status) {
        case 'Unconfirmed':
            return 'workshopPlanner.APPOINTMENT_UNCONFIRMED';
        case 'CustomerDidNotArrive':
            return 'workshopPlanner.DID_NOT_ARRIVE';
        case 'Confirmed':
            return 'workshopPlanner.APPOINTMENT_CONFIRMED';
        case 'OrderCreated':
            return `${t('workshopPlanner.ORDER')} #${orderNumber ?? ''}`;
        default:
            return '';
    }
}

export default function ScheduleAppointmentWorkAltPopup({
    userId,
    planningId,
    onClose,
    ts: initialTs,
    ...props
}: ScheduleAppointmentWorkAltPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const planning = useAppSelector((r) => selectPlanning(r, planningId));
    const user: UserListItem | null = useAppSelector((r) => selectUser(r, userId) ?? null);
    const wpSettings = useAppSelector(selectShopWpSettings);

    const [appointmentItem, setAppointmentItem] = useState<AppointmentListItem>();
    const [duration, setDuration] = useState(0);

    const [date, setDate] = useState(new Date());
    const [time, setTime] = useState<[number, number]>([0, 0]);
    const [selectedReasons, setSelectedReasons] = useState<JobDescriptionDto[]>([]);

    const [isSaving, setIsSaving] = useState<boolean>(false);

    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed({ hours: duration / 60, userId });

    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const dt = useMemo(() => {
        const dt = DateTime.fromJSDate(date).set({
            hour: time[0],
            minute: time[1],
        });
        return dt;
    }, [date, time]);
    const ts = dt.toMillis();
    const maxDuration = useMaxAvailableTimeInMinutes(user, dt);

    useEffect(() => {
        const dt = DateTime.fromMillis(initialTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
    }, [initialTs]);

    const handleClosed = useCallback(() => {
        const dt = DateTime.fromMillis(initialTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
        setIsSaving(false);
    }, [initialTs]);

    const { data: latestAppointments } = useQuery(
        ['appointments', 'latest-numbers'],
        AppointmentsApi.getLatest,
        {
            cacheTime: Infinity,
            staleTime: 10000,
            enabled: props.open,
        }
    );

    const valid = duration >= 1 && duration <= maxDuration && !!appointmentItem;

    const createBlock = useMutation(
        async () => {
            if (!valid) return;

            const result = await dispatch(
                createPlannedBlockThunk({
                    userId,
                    appointmentId: appointmentItem.id,
                    duration,
                    position: ts,
                    planningId,
                    jobDescriptionRecords: selectedReasons,
                })
            );

            if (result.meta.requestStatus === 'rejected') {
                if (result.meta.rejectedWithValue) {
                    const e = result.payload;
                    if (isCmosError(e)) {
                        console.error('[wp>planned order scheduling]', e);
                    } else {
                        console.error('[wp>planned order scheduling] non cmos error', e);
                    }
                    throw e;
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                    throw new Error('unknown error');
                }
            }
        },
        {
            onSuccess: () => {
                if (!appointmentItem) {
                    console.error('appointmentItem is not set');
                } else {
                    dispatch(planningsActions.addAppointmentInfoFromSearchItem(appointmentItem));
                }
                dispatch(planningsActions.removePreview());
                dispatch(loadScheduleThunk({}));
                toasters.success(
                    t('workshopPlanner.schedulePopup.appointmentCreated.text', {
                        appointmentNumber: appointmentItem?.number,
                    }),
                    t('workshopPlanner.schedulePopup.appointmentCreated.title')
                );
                if (enableWorkshopJobIntegration && integrationAccountName) {
                    toasters.success(
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.title')
                    );
                }
                onClose();
            },
            onError: (error) => {
                if (isErrorResponse(error)) {
                    if (enableWorkshopJobIntegration && integrationAccountName) {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.text', {
                                errorMessage: error.message,
                            }),
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.title', {
                                integrationAccountName,
                            })
                        );
                    }
                }
            },
        }
    );

    useEffect(() => {
        setSelectedReasons([]);
    }, [appointmentItem?.id]);

    useEffect(() => {
        if (props.open) {
            dispatch(
                planningsActions.setPreview({
                    userId,
                    duration,
                    ts,
                    maxDuration,
                })
            );
        }
    }, [props.open, dispatch, duration, userId, ts, maxDuration]);

    useEffect(() => {
        if (props.open) {
            setDuration(
                wpSettings?.defaultScheduledDurationMinutes ?? DEFAULT_SCHEDULED_DURATION_MINUTES
            );
            setAppointmentItem(undefined);
        } else {
            dispatch(planningsActions.removePreview());
        }
    }, [props.open, dispatch, wpSettings]);

    return (
        <SchedulePopupLayout
            circleColor={user?.color}
            ts={ts}
            onClose={onClose}
            onClosed={handleClosed}
            title={t('workshopPlanner.schedulePopup.appointmentTitle', {
                userPosition: user?.job ? t(jobTitleLabel(user?.job as JobTitle)) : user?.job,
                userName: user?.name,
            })}
            {...props}
        >
            {!appointmentItem && (
                <>
                    <AppointmentNumberAutoselect
                        value={appointmentItem}
                        onChange={setAppointmentItem}
                    >
                        <LatestSelector<AppointmentListItem>
                            value={appointmentItem}
                            options={latestAppointments ?? []}
                            onSelected={setAppointmentItem}
                            getLabel={(x) => '#' + x.number}
                            tooltip={(x) => {
                                return (
                                    <AppointmentDetailsTooltip
                                        appointmentNumber={x.number}
                                        customerFirstName={x.customer.firstName ?? ''}
                                        customerLastName={x.customer.lastName ?? ''}
                                        model={x.vehicle?.model ?? ''}
                                        plates={x.vehicle?.plates ?? ''}
                                        status={t(mapStatus(t, x.status, x.order?.number))}
                                        duration={null}
                                        appointmentTime={moment(
                                            `${x.startsAt.date} ${x.startsAt.time}`
                                        ).format('hh:mm a')}
                                        serviceAdvisorName={x.serviceAdvisor.name}
                                        appointmentReasons={x.reasons ? x.reasons : []}
                                        notes={x.notes}
                                        jobDescriptions={
                                            x.blockJobDescriptions
                                                ? x.blockJobDescriptions.flatMap(
                                                      (x) => x.jobDescriptions
                                                  )
                                                : []
                                        }
                                    />
                                );
                            }}
                        />
                    </AppointmentNumberAutoselect>
                    <Divider style={{ margin: '8px 0' }} />
                </>
            )}

            <AppointmentDetails id={appointmentItem?.id} setAppointment={setAppointmentItem} />
            <Grid container spacing={2}>
                <Grid item xs={3}>
                    <DateFormField
                        label={t('workshopPlanner.editBlock.startDate')}
                        enableEnterComplete
                        value={date}
                        onChange={(v) => {
                            if (v && isDateValid(v)) setDate(v);
                        }}
                    />
                </Grid>

                <Grid item xs={3}>
                    <TimeFormField
                        value={time}
                        // isInvalid={hasOverlap}
                        onChange={setTime}
                        label={t('workshopPlanner.editBlock.startHour')}
                    />
                </Grid>
                <Grid item xs={3}>
                    <DurationFormField
                        isRequired
                        value={duration}
                        label={t('workshopPlanner.schedulePopup.duration')}
                        name={'duration'}
                        onChange={setDuration}
                    />
                </Grid>
                <Grid item xs={3}>
                    <UTInput
                        disabled={!planning?.isTechnicians}
                        durationInSeconds={duration * 60}
                        onChange={(v) => setDuration(Math.floor(v / 60))}
                    />
                </Grid>
            </Grid>
            {props.open && !createBlock.isLoading && duration > maxDuration ? (
                <ErrorText>{t('workshopPlanner.schedulePopup.exceedsDuration')}</ErrorText>
            ) : (
                <></>
            )}

            <Box marginBottom={2}>
                <JobDescriptionText>
                    {t('workshopPlanner.schedulePopup.jobDescription')}
                </JobDescriptionText>
                <Box display={'flex'} flexDirection={'column'} alignItems={'flex-end'}>
                    <Box width={'97%'}>
                        {selectedReasons.length > 0 && (
                            <JobDescriptionsList
                                onRemoveJobDescription={(reasonId) => {
                                    setSelectedReasons((prevItems) =>
                                        prevItems.filter((item) => item.id !== reasonId)
                                    );
                                }}
                                selectedJobDescriptions={selectedReasons}
                            />
                        )}
                        <JobDescriptionsAutocomplete
                            cacheKey={appointmentItem?.number ? appointmentItem?.id : ''}
                            getJobDescriptions={() =>
                                appointmentItem?.id
                                    ? WpAppointmentsApi.getReasons(appointmentItem?.id)
                                    : []
                            }
                            excludeItems={selectedReasons}
                            onChange={(inspectionItem) => {
                                setSelectedReasons((prevItems) => [...prevItems, inspectionItem]);
                            }}
                        />
                    </Box>
                </Box>
            </Box>

            <Box display="flex" justifyContent="end" gap={1.25} marginTop={2}>
                <Button
                    onClick={onClose}
                    w="sm"
                    label={t('commonLabels.cancel')}
                    color={Colors.Neutral3}
                />
                <Button
                    disabled={!valid || !isSchedulingAllowed || isSaving}
                    onClick={() => {
                        setIsSaving(true);
                        technicianCapacityAlertPopup.checkTechnicianCapacity(createBlock.mutate, {
                            hours: duration / 60,
                            userId: userId,
                        });
                    }}
                    showLoader={createBlock.isLoading}
                    w="sm"
                    label={t('commonLabels.save')}
                />
            </Box>
        </SchedulePopupLayout>
    );
}
