import useF<PERSON><PERSON>Render from 'common/hooks/useForceRender';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import ScheduleAppointmentWorkPopup from '.';

interface IScheduleAppointmentPopupContext {
    open(ts: number, userId: string, planningId: number, element: HTMLElement): void;
}

const ScheduleAppointmentPopupContext = createContext<IScheduleAppointmentPopupContext | null>(
    null
);

export function useScheduleAppointmentPopup() {
    const ctx = useContext(ScheduleAppointmentPopupContext);
    if (!ctx) throw new Error('cannot use ScheduleAppointmentPopupContext');
    return ctx;
}

export function ScheduleAppointmentPopupProvider({ children }: React.PropsWithChildren<{}>) {
    const state = useRef({
        ts: 0,
        anchorEl: null as HTMLElement | null,
        userId: '',
        planningId: -1,
    });
    const fr = useForceRender();
    const ctx: IScheduleAppointmentPopupContext = useMemo(
        () => ({
            open: (ts, userId, planningId, element) => {
                state.current.anchorEl = element;
                state.current.ts = ts;
                state.current.planningId = planningId;
                state.current.userId = userId;
                fr();
            },
        }),
        [fr]
    );

    const close = useCallback(() => {
        state.current.anchorEl = null;
        fr();
    }, [fr]);

    return (
        <ScheduleAppointmentPopupContext.Provider value={ctx}>
            <ScheduleAppointmentWorkPopup
                planningId={state.current.planningId}
                ts={state.current.ts}
                onClose={close}
                userId={state.current.userId}
                anchorEl={state.current.anchorEl ?? undefined}
                open={!!state.current.anchorEl}
            />
            {children}
        </ScheduleAppointmentPopupContext.Provider>
    );
}
