import useF<PERSON><PERSON><PERSON><PERSON> from 'common/hooks/useForceRender';
import { DateTime } from 'luxon';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import ScheduleOrderWorkAltPopup from '.';

interface IScheduleOrderPopupContext {
    open(
        orderNumber: string,
        ts?: number,
        duration?: number,
        userId?: string,
        planningId?: number,
        fitInitialBlock?: boolean
    ): void;
}

const ScheduleOrderPopupContext = createContext<IScheduleOrderPopupContext | null>(null);

export function useScheduleOrderAltPopup() {
    const ctx = useContext(ScheduleOrderPopupContext);
    if (!ctx) throw new Error('cannot use ScheduleOrderPopupContext');
    return ctx;
}

export function ScheduleOrderPopupAltProvider({ children }: React.PropsWithChildren<{}>) {
    const state = useRef<{
        orderNumber: string;
        ts: number;
        duration: number;
        userId: string | null;
        planningId: number | null;
        fitInitialBlock: boolean;
    }>({
        orderNumber: '',
        ts: 0,
        duration: 0,
        userId: null,
        planningId: null,
        fitInitialBlock: false,
    });
    const fr = useForceRender();
    const ctx: IScheduleOrderPopupContext = useMemo(
        () => ({
            open: (orderNumber, ts, duration, userId, planningId, fitInitialBlock) => {
                state.current.orderNumber = orderNumber;
                state.current.ts =
                    ts ??
                    DateTime.now()
                        .set({ millisecond: 0, second: 0, minute: 0, hour: 0 })
                        .toMillis();
                state.current.duration = duration ?? 0;
                state.current.userId = userId ?? null;
                state.current.planningId = planningId ?? null;
                state.current.fitInitialBlock = fitInitialBlock ?? false;

                fr();
            },
        }),
        [fr]
    );

    const close = useCallback(() => {
        state.current.orderNumber = '';
        fr();
    }, [fr]);

    return (
        <ScheduleOrderPopupContext.Provider value={ctx}>
            <ScheduleOrderWorkAltPopup
                orderNumber={state.current.orderNumber}
                ts={state.current.ts}
                duration={state.current.duration}
                userId={state.current.userId}
                planningId={state.current.planningId}
                fitInitialBlock={state.current.fitInitialBlock}
                onClose={close}
                open={!!state.current.orderNumber}
            />
            {children}
        </ScheduleOrderPopupContext.Provider>
    );
}
