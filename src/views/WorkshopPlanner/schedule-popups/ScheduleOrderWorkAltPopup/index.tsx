import { Box, Grid, styled, useMediaQuery } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { isCmosError } from 'api/error';
import { UserListItem } from 'api/users';
import { JobDescriptionDto, StandardOperationDto, WpOrdersApi } from 'api/workshopPlanner';
import { Button } from 'common/components/Button';
import { AddOperationIcon } from 'common/components/Icons/AddOperationIcon';
import { RemoveOperationIcon } from 'common/components/Icons/RemoveOperationIcon';
import DateFormField from 'common/components/Inputs/DateFormField';
import DurationFormField from 'common/components/Inputs/DurationFormField';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import TextField from 'common/components/Inputs/TextField';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { selectRepairShopWpConfiguration, selectSettings } from 'store/slices/globalSettingsSlice';
import { selectUser } from 'store/slices/users';
import { wpOrdersActions } from 'store/slices/wp/orders';
import {
    createOrderBlockThunk,
    loadScheduleThunk,
    planningsActions,
    selectPlanning,
    selectPlanningUsers,
    selectPlannings,
    selectUserWithBlocks,
} from 'store/slices/wp/plannings';
import { calcMostSuitableTsInMillis, useMaxAvailableTimeInMinutes } from 'store/util/wp';
import { isDateValid } from 'utils';
import UserSelect from 'views/Components/UserSelect/UserSelect';
import { usePlanningsTabs } from 'views/WorkshopPlanner/PlanningsTabs/context';
import { useTechnicianCapacityAlert } from 'views/WorkshopPlanner/TechnicianCapacity/TechnicianCapacityAlert';
import { useIsSchedulingAllowed } from 'views/WorkshopPlanner/TechnicianCapacity/helpers';
import UTInput from 'views/WorkshopPlanner/common/UTInput';
import { isStandardOperationsAllowed } from 'views/WorkshopPlanner/helpers';
import JobDescriptionsAutocomplete from '../JobDescriptionAutocomplete';
import JobDescriptionsList from '../JobDescriptionList';
import { SroOperationCodeFields } from '../ScheduleOrderWorkPopup/SroOperationCodeFields';
import SchedulePopupLayout, { SchedulePopupLayoutProps } from '../SchedulePopupLayout';
import { scrollToUserRow } from '../helpers';
import OrderDetails from './OrderDetails';

type ScheduleOrderWorkAltPopupProps = Omit<
    SchedulePopupLayoutProps,
    'title' | 'circleColor' | 'slotProps'
> & {
    orderNumber: string;
    duration: number;
    userId: string | null;
    planningId: number | null;
    fitInitialBlock: boolean;
};

const ErrorText = styled('p')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--danger)',
}));

const JobDescriptionText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    marginBottom: '6px',
    marginTop: '17px',
    color: 'var(--neutral8)',
}));

const StandardOperationsSection = styled(Box)({
    maxHeight: 198,
    width: '100%',
    overflowY: 'auto',
    paddingLeft: '8px',
    scrollbarGutter: 'stable',
    ...scrollbarStyle(),
});

const ActionButtons = styled('div')({
    width: '8%',
    display: 'flex',
    alignItems: 'center',
    gap: 0,
});

const AddButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    backgroundColor: 'var(--primary)',
    color: '#fff',
    padding: 2,
    borderRadius: 4,
    cursor: 'pointer',
}));

const RemoveButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    backgroundColor: 'var(--error)',
    color: '#fff',
    padding: 2,
    marginRight: -4,
    borderRadius: 4,
    cursor: 'pointer',
}));

export default function ScheduleOrderWorkAltPopup({
    onClose,
    ts: initialTs,
    orderNumber,
    duration: initialDuration,
    userId: initialUserId,
    planningId: initialPlanningId,
    fitInitialBlock,
    ...props
}: ScheduleOrderWorkAltPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const [planningId, setPlanningId] = useState<number | null>(null);
    const [userId, setUserId] = useState<string | null>(null);

    const planningUserIds = useAppSelector(selectPlanningUsers);
    const planning = useAppSelector((r) => (planningId ? selectPlanning(r, planningId) : null));
    const user: UserListItem | null = useAppSelector((r) =>
        userId ? selectUser(r, userId) ?? null : null
    );
    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const [duration, setDuration] = useState(0);
    const [date, setDate] = useState(new Date());
    const [time, setTime] = useState<[number, number]>([0, 0]);
    const [selectedInspectionItems, setSelectedInspectionItems] = useState<JobDescriptionDto[]>([]);
    const [mostSuitableTs, setMostSuitableTs] = useState(-1);

    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed(
        userId ? { hours: duration / 60, userId } : undefined
    );

    const dt = useMemo(() => {
        const dt = DateTime.fromJSDate(date).set({
            hour: time[0],
            minute: time[1],
        });
        return dt;
    }, [date, time]);
    const ts = dt.toMillis();
    const maxDuration = useMaxAvailableTimeInMinutes(user, dt);
    const userWithBlocks = useAppSelector((r) => selectUserWithBlocks(r, initialUserId ?? null));

    const planningsTabs = usePlanningsTabs();

    const globalSettings = useAppSelector(selectSettings);

    const sroValidationIntegration = useMemo(
        () => !!globalSettings.repairShopSettings?.features.sroValidationIntegration,
        [globalSettings]
    );

    const standardOperationOrderTypes = useMemo(() => {
        try {
            return JSON.parse(globalSettings.standardOperationOrderTypes) ?? [];
        } catch (error) {
            console.error('Error parsing standardOperationOrderTypes:', error);
            return [];
        }
    }, [globalSettings.standardOperationOrderTypes]);

    const { data: order } = useQuery(
        ['wp', 'order', orderNumber],
        () => WpOrdersApi.getOrder(orderNumber),
        {
            enabled: !!orderNumber,
            staleTime: 10000,
            cacheTime: Infinity,
        }
    );

    const [canUseStandardOperations, setCanUseStandardOperations] = useState<boolean>(false);

    useEffect(() => {
        setCanUseStandardOperations(
            isStandardOperationsAllowed(
                !!order,
                !order?.orderType,
                globalSettings.enableStandardOperations,
                standardOperationOrderTypes,
                order?.orderType?.key
            )
        );
    }, [order, globalSettings.enableStandardOperations, standardOperationOrderTypes]);

    const [standardOperations, setStandardOperations] = useState<StandardOperationDto[]>([]);

    const addOperationField = () => {
        setStandardOperations((prev) => [...prev, { operationCode: '', standardTime: 0 }]);
    };

    const removeOperationField = (index: number) => {
        setStandardOperations((prev) => {
            const updated = prev.filter((_, ind) => ind !== index);
            setDuration(calculateScheduledDuration(updated));
            return updated;
        });
    };

    const handleOperationCodeChange = (index: number, newCode: string) => {
        setStandardOperations((prev) =>
            prev.map((item, ind) => (ind === index ? { ...item, operationCode: newCode } : item))
        );
    };

    const handleStandardTimeChange = (index: number, newTime: number) => {
        setStandardOperations((prev) => {
            const updated = prev.map((item, ind) =>
                ind === index ? { ...item, standardTime: newTime } : item
            );

            setDuration(calculateScheduledDuration(updated));
            return updated;
        });
    };

    const handleOperationCodeDescriptionChange = (index: number, newDescription: string) => {
        setStandardOperations((prev) =>
            prev.map((item, ind) =>
                ind === index ? { ...item, operationCodeDescription: newDescription } : item
            )
        );
    };

    const calculateScheduledDuration = (fields: StandardOperationDto[]) => {
        return fields.reduce((total, field) => total + (field.standardTime ?? 0), 0);
    };

    function isDuplicateOperationCode(
        standardOperations: StandardOperationDto[],
        currentCode: string,
        currentIndex: number
    ) {
        return (
            Array.isArray(standardOperations) &&
            standardOperations.some(
                (item, idx) =>
                    item.operationCode.trim() === currentCode.trim() && idx !== currentIndex
            )
        );
    }

    const allOperationCodesFilled = canUseStandardOperations
        ? standardOperations.every(
              (field) => field.operationCode.trim() !== '' && field.standardTime > 0
          )
        : true;

    const allOperationCodesDescriptionFilled = canUseStandardOperations
        ? standardOperations.every(
              (field) =>
                  !field.operationCodeDescription || field.operationCodeDescription.length <= 100
          )
        : true;

    useEffect(() => {
        if (!props.open) {
            return;
        }

        if (initialUserId && userWithBlocks && fitInitialBlock) {
            const mostSuitableTs = calcMostSuitableTsInMillis(
                initialUserId,
                initialDuration,
                initialTs,
                userWithBlocks
            );

            setMostSuitableTs(mostSuitableTs);
        } else {
            setMostSuitableTs(initialTs);
        }
    }, [initialUserId, initialDuration, initialTs, fitInitialBlock, props.open, userWithBlocks]);

    useEffect(() => {
        const dt = DateTime.fromMillis(mostSuitableTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
    }, [mostSuitableTs]);

    useEffect(() => {
        if (props.open) {
            setDuration(initialDuration);
            setUserId(initialUserId);
            setPlanningId(initialPlanningId);
            setStandardOperations([
                { operationCode: '', standardTime: 0, operationCodeDescription: '' },
            ]);
        }
    }, [props.open, initialUserId, initialPlanningId, initialDuration]);

    const handleClosed = useCallback(() => {
        const dt = DateTime.fromMillis(mostSuitableTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
    }, [mostSuitableTs]);

    const valid = !!(
        allOperationCodesFilled &&
        allOperationCodesDescriptionFilled &&
        duration >= 1 &&
        duration <= maxDuration &&
        planningId &&
        userId
    );

    const createBlock = useMutation(
        async () => {
            if (!valid) return;
            if (!userId) throw new Error('cannot create order block without userId');
            if (!planningId) throw new Error('cannot create order block without userId');
            if (!order) throw new Error('cannot create order block without loaded order');

            const result = await dispatch(
                createOrderBlockThunk({
                    userId,
                    orderId: order.key,
                    duration,
                    position: ts,
                    planningId,
                    jobDescriptionRecords: selectedInspectionItems,
                    standardOperations: canUseStandardOperations ? standardOperations : undefined,
                })
            );

            if (result.meta.requestStatus === 'rejected') {
                if (result.meta.rejectedWithValue) {
                    const e = result.payload;
                    if (isCmosError(e)) {
                        console.error('[wp>order scheduling]', e);
                    } else {
                        console.error('[wp>order scheduling] non cmos error', e);
                    }
                    throw e;
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                    throw new Error('unknown error');
                }
            }
        },
        {
            onSuccess: () => {
                // TODO 2397 return this
                if (!order) {
                    console.error('order is not set');
                } else {
                    dispatch(
                        wpOrdersActions.add({
                            id: order.key,
                            appointmentId: order.appointment?.id ?? null,
                            number: order.number,
                            customer: {
                                id: order.customer.id,
                                firstName: order.customer.firstName,
                                lastName: order.customer.lastName,
                            },
                            vehicle: {
                                id: order.vehicle.id,
                                model: order.vehicle.model,
                                plates: order.vehicle.plates,
                                brand: order.vehicle.brand,
                                year: order.vehicle.year,
                                vin: order.vehicle.vin,
                            },
                            type: order.orderType,
                            tower: order.tower ?? '',
                            inChargeId: order.inCharge?.id ?? null,
                            assignedId: order.assignedTo?.id ?? null,
                            promisedAt: order.deliveryDate,
                            phase: order.phase,
                            phaseChangedAt:
                                order.phasesHistory.length > 0
                                    ? order.phasesHistory.at(-1)!.changeDate
                                    : null,
                            phaseChangedByUserDisplayName:
                                order.phasesHistory.length > 0
                                    ? order.phasesHistory.at(-1)!.changedByUser
                                    : null,
                            notes: order.notes,
                            orderReasons: order.orderReasons,
                            appointmentReasonColor: order.appointmentReasonColor,
                            colorFieldValue: order.colorFieldValue,
                            hasRedItems: order.hasRedItems,
                            inspectionFormTemplateId: order.inspectionFormTemplateId,
                            jobDescriptions: order.jobDescriptions,
                        })
                    );
                }
                dispatch(planningsActions.removePreview());
                dispatch(loadScheduleThunk({}));
                // TODO 2397 return this
                toasters.success(
                    t('workshopPlanner.schedulePopup.orderCreated.text', {
                        orderNumber: order?.number,
                    }),
                    t('workshopPlanner.schedulePopup.orderCreated.title')
                );
                if (enableWorkshopJobIntegration && integrationAccountName) {
                    toasters.success(
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.title')
                    );
                }
                onClose();
            },
            onError: (error) => {
                if (isErrorResponse(error)) {
                    if (enableWorkshopJobIntegration && integrationAccountName) {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.text', {
                                errorMessage: error.message,
                            }),
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.title', {
                                integrationAccountName,
                            })
                        );
                    }
                }
            },
        }
    );

    useEffect(() => {
        if (props.open && userId && planningId) {
            const initialDt = DateTime.fromMillis(mostSuitableTs);
            const dt = DateTime.fromMillis(ts);
            if (
                ts >= 0 &&
                !(
                    dt.hour === 0 &&
                    dt.minute === 0 &&
                    initialDt.year === dt.year &&
                    initialDt.month === dt.month &&
                    initialDt.day === dt.day
                )
            ) {
                dispatch(
                    planningsActions.setPreview({
                        userId,
                        duration,
                        ts,
                        maxDuration,
                        disableAutomaticInitialPosition: true,
                    })
                );
            }
        }
    }, [props.open, dispatch, duration, userId, mostSuitableTs, ts, maxDuration, planningId]);

    useEffect(() => {
        if (!props.open) {
            setDuration(0);
            setPlanningId(null);
            setUserId(null);
            setTime([0, 0]);
            setMostSuitableTs(-1);
            dispatch(planningsActions.removePreview());
        }
    }, [props.open, dispatch]);

    const customerName = order
        ? `${order.customer.firstName} ${order.customer.lastName}`.trim()
        : '';
    const orderTitle = order ? `#${order.number} - ${customerName}` : '';
    const exceedsMaxDuration = duration > maxDuration && !(time[0] === 0 && time[1] === 0);

    useEffect(() => {
        setSelectedInspectionItems([]);
    }, [order?.number]);

    useEffect(() => {
        if (!planningId || !userId) return;

        const planningIsChanged = planningsTabs?.setPlanningId(planningId);

        setTimeout(
            () => {
                scrollToUserRow(planningId, userId);
            },
            planningIsChanged ? 1000 : 500
        );
    }, [planningId, userId, planningsTabs]);

    const scrollRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
    }, [standardOperations]);

    const isSmall = useMediaQuery('(max-height: 820px)');
    const popupStyles = canUseStandardOperations
        ? {
              main: {
                  style: {
                      paddingRight: 0,
                      ...(isSmall
                          ? {
                                maxHeight: ' 90vh',
                                overflow: 'auto',
                            }
                          : {}),
                  },
              },
          }
        : {};

    return (
        <SchedulePopupLayout
            slotProps={{
                main: popupStyles.main,
            }}
            hideCircle
            dataTestId="scheduleOrderBlockPopup-alt"
            ts={ts}
            onClose={onClose}
            title={t('workshopPlanner.schedulePopup.orderAltTitle', {
                order: orderTitle,
            })}
            onClosed={handleClosed}
            {...props}
        >
            <OrderDetails
                number={orderNumber}
                maxWidth={canUseStandardOperations ? '90%' : undefined}
            />
            <Grid container columnSpacing={1} rowSpacing={1}>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '50%' }}>
                    <InputWrapper
                        isRequired
                        showValidationIndicators
                        label={t('workshopPlanner.schedulePopup.teamMember')}
                    >
                        <UserSelect
                            userId={userId}
                            userIds={planningUserIds}
                            onChange={setUserId}
                            dataTestId="selectUser"
                            placeholder={t('workshopPlanner.schedulePopup.teamMemberPlaceholder')}
                            fullWidth
                        />
                    </InputWrapper>
                </Grid>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '50%' }}>
                    <PlanningSelect
                        userId={userId}
                        planningId={planningId}
                        onSelected={setPlanningId}
                    />
                </Grid>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <DateFormField
                        label={t('workshopPlanner.editBlock.startDate')}
                        enableEnterComplete
                        value={date}
                        onChange={(v) => {
                            if (v && isDateValid(v)) setDate(v);
                        }}
                        isRequired
                        showValidationIndicators
                    />
                </Grid>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <TimeFormField
                        value={time}
                        onChange={setTime}
                        label={t('workshopPlanner.editBlock.startHour')}
                        isRequired
                        showValidationIndicators
                    />
                </Grid>
                {canUseStandardOperations && (
                    <StandardOperationsSection ref={scrollRef}>
                        {standardOperations.map((field, index) => (
                            <>
                                {sroValidationIntegration ? (
                                    <SroOperationCodeFields
                                        key={index}
                                        id={index}
                                        {...{
                                            standardOperations,
                                            orderId: order?.key,
                                            field,
                                            addOperationField,
                                            removeOperationField,
                                            handleOperationCodeChange,
                                            handleStandardTimeChange,
                                            handleOperationCodeDescriptionChange,
                                            width: {
                                                operationCode: '21.88%',
                                                standardTime: '21.88%',
                                                operationCodeDescription: '350.5px',
                                            },
                                        }}
                                    />
                                ) : (
                                    <Box
                                        key={index}
                                        sx={{
                                            display: 'flex',
                                            gap: '8px',
                                            alignItems: 'flex-end',
                                            paddingTop: '8px',
                                        }}
                                    >
                                        <Box sx={{ width: '21.88%' }}>
                                            <TextField
                                                isRequired={true}
                                                isInvalid={isDuplicateOperationCode(
                                                    standardOperations,
                                                    field.operationCode,
                                                    index
                                                )}
                                                showValidationIndicators
                                                name={`operationCode-${index}`}
                                                label={t(
                                                    'workshopPlanner.schedulePopup.operationCode'
                                                )}
                                                placeholder={t(
                                                    'workshopPlanner.schedulePopup.operationCodePlaceholder'
                                                )}
                                                value={field.operationCode}
                                                onChange={(e) =>
                                                    handleOperationCodeChange(index, e.target.value)
                                                }
                                                maxLength={50}
                                            />
                                        </Box>
                                        <Box sx={{ width: '21.88%' }}>
                                            <DurationFormField
                                                isRequired={true}
                                                showValidationIndicators={true}
                                                hideValidationIndicators={false}
                                                label={t(
                                                    'workshopPlanner.schedulePopup.standardTime'
                                                )}
                                                name={`standardTime-${index}`}
                                                value={field.standardTime ?? 0}
                                                onChange={(value) =>
                                                    handleStandardTimeChange(index, value)
                                                }
                                            />
                                        </Box>
                                        <Box sx={{ width: '351px' }}>
                                            <TextField
                                                name={`operationCodeDescription-${index}`}
                                                label={t(
                                                    'workshopPlanner.schedulePopup.operationCodeDescription'
                                                )}
                                                placeholder={t(
                                                    'workshopPlanner.schedulePopup.operationCodeDescriptionPlaceholder'
                                                )}
                                                value={field.operationCodeDescription}
                                                onChange={(e) =>
                                                    handleOperationCodeDescriptionChange(
                                                        index,
                                                        e.target.value
                                                    )
                                                }
                                            />
                                        </Box>
                                        <ActionButtons>
                                            {index === standardOperations.length - 1 && (
                                                <AddButton onClick={addOperationField}>
                                                    <AddOperationIcon />
                                                </AddButton>
                                            )}
                                            {standardOperations.length > 1 && (
                                                <RemoveButton
                                                    onClick={() => removeOperationField(index)}
                                                >
                                                    <RemoveOperationIcon />
                                                </RemoveButton>
                                            )}
                                        </ActionButtons>
                                    </Box>
                                )}
                            </>
                        ))}
                    </StandardOperationsSection>
                )}
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <DurationFormField
                        isRequired
                        value={duration}
                        label={t('workshopPlanner.schedulePopup.duration')}
                        name={'duration'}
                        onChange={setDuration}
                        showValidationIndicators
                        disabled={canUseStandardOperations}
                    />
                </Grid>
                <Grid item sx={{ width: canUseStandardOperations ? '45%' : '25%' }}>
                    <UTInput
                        disabled={!planning?.isTechnicians || canUseStandardOperations}
                        durationInSeconds={duration * 60}
                        onChange={(v) => setDuration(Math.floor(v / 60))}
                    />
                </Grid>
            </Grid>
            {props.open && !createBlock.isLoading && exceedsMaxDuration ? (
                <ErrorText>{t('workshopPlanner.schedulePopup.exceedsDuration')}</ErrorText>
            ) : (
                <></>
            )}
            <Box marginBottom={2} width={canUseStandardOperations ? '89.5%' : '100%'}>
                <JobDescriptionText>
                    {t('workshopPlanner.schedulePopup.jobDescription')}
                </JobDescriptionText>
                <Box display={'flex'} flexDirection={'column'} alignItems={'flex-end'}>
                    <Box width={'97%'}>
                        {selectedInspectionItems.length > 0 && (
                            <JobDescriptionsList
                                onRemoveJobDescription={(inspectionItemId) => {
                                    setSelectedInspectionItems((prevItems) =>
                                        prevItems.filter((item) => item.id !== inspectionItemId)
                                    );
                                }}
                                selectedJobDescriptions={selectedInspectionItems}
                            />
                        )}
                        <JobDescriptionsAutocomplete
                            cacheKey={order?.number ? order?.number : ''}
                            getJobDescriptions={() =>
                                order?.number ? WpOrdersApi.getInspectionItems(order?.number) : []
                            }
                            excludeItems={selectedInspectionItems}
                            onChange={(inspectionItem) => {
                                setSelectedInspectionItems((prevItems) => [
                                    ...prevItems,
                                    inspectionItem,
                                ]);
                            }}
                        />
                    </Box>
                </Box>
            </Box>
            <Box
                display="flex"
                justifyContent="end"
                gap={1.25}
                marginTop={2}
                marginRight={canUseStandardOperations ? 10 : 0}
            >
                <Button
                    onClick={onClose}
                    w="sm"
                    label={t('commonLabels.cancel')}
                    color={Colors.Neutral3}
                />
                <Button
                    disabled={!valid || !isSchedulingAllowed}
                    onClick={() =>
                        technicianCapacityAlertPopup.checkTechnicianCapacity(createBlock.mutate, {
                            hours: duration / 60,
                            userId: userId!,
                        })
                    }
                    showLoader={createBlock.isLoading}
                    w="sm"
                    label={t('commonLabels.save')}
                />
            </Box>
        </SchedulePopupLayout>
    );
}

type PlanningSelectProps = {
    planningId: number | null;
    onSelected: (planningId: number | null) => void;
    userId: string | null;
};

function PlanningSelect({ planningId, onSelected, userId }: PlanningSelectProps) {
    const { t } = useAppTranslation();
    const { list: plannings } = useAppSelector(selectPlannings);
    const availablePlannings = useMemo(() => {
        const list = userId
            ? plannings
                  .filter((x) => x.userIds.includes(userId))
                  .map((x) => ({
                      ...x,
                      name: x.readonly
                          ? t(`settings.workshopPlannerSettings.planning.${x.name}`)
                          : x.name,
                  }))
            : [];
        list.sort((a, b) => a.name.localeCompare(b.name));
        return list;
    }, [plannings, userId, t]);
    const disabled = userId === null;

    useEffect(() => {
        if (planningId && availablePlannings.every((x) => x.id !== planningId)) {
            onSelected(null);
        }
    }, [availablePlannings, onSelected, planningId]);

    return (
        <InputWrapper
            isRequired
            disabled={disabled}
            showValidationIndicators
            label={t('workshopPlanner.schedulePopup.planning')}
        >
            <SSelectInput
                data-test-id="selectPlanning"
                placeholder={t('workshopPlanner.schedulePopup.planningPlaceholder')}
                fullWidth
                disabled={disabled}
                onChange={(e) => onSelected(Number(e.target.value))}
                value={planningId}
            >
                {availablePlannings.map((x) => (
                    <SMenuItem value={x.id} key={x.id}>
                        {x.name}
                    </SMenuItem>
                ))}
            </SSelectInput>
        </InputWrapper>
    );
}
