import { Box, Divider, Grid, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import AppointmentsApi from 'api/appointments';
import WpOrdersApi from 'api/workshopPlanner/orders';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useMemo } from 'react';
import Skeleton from 'react-loading-skeleton';

type OrderDetailsProps = {
    number: string | undefined;
    maxWidth?: string | number;
};

const Prop = styled('div')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[8],
    lineHeight: '1.55em',
}));

const StyledList = styled('ul')({
    display: 'flex',
    flexDirection: 'column',
    listStyle: 'none',
    paddingLeft: '25px',
    margin: 0,
});

const StyledListItem = styled('li')({
    position: 'relative',
    paddingLeft: '15px',
    '&::before': {
        content: '"•"',
        position: 'absolute',
        left: 0,
        color: 'black',
    },
});

export default function OrderDetails({ number, maxWidth }: OrderDetailsProps) {
    const { t } = useAppTranslation();
    const { data, isLoading } = useQuery(
        ['wp', 'order', number],
        () => WpOrdersApi.getOrder(number!),
        {
            enabled: !!number,
            staleTime: 10000,
            cacheTime: Infinity,
        }
    );
    const { data: appointmentData, isLoading: isAppointmentDataLoading } = useQuery(
        ['appointment', data?.appointment?.id],
        () => AppointmentsApi.get(data!.appointment!.id),
        {
            enabled: !!data?.appointment,
            cacheTime: Infinity,
            staleTime: 10000,
        }
    );

    const reasonsList = useMemo(() => {
        const reasons = appointmentData?.reasons.map((x) => x.name) || [];
        const customReasons =
            appointmentData?.customReasons.map(
                (x) => `${x.name}${x.details ? ` - ${x.details.name}` : ''}`
            ) || [];
        return [...reasons, ...customReasons];
    }, [appointmentData]);

    const customerNotes = useMemo(
        () => (data?.notes ? data.notes.filter((x) => x.type === 'ForCustomer') : []),
        [data]
    );

    const internalNotes = useMemo(
        () => (data?.notes ? data.notes.filter((x) => x.type === 'ForInternal') : []),
        [data]
    );

    if (!number) return null;

    if (isLoading || !data) {
        return (
            <Grid container spacing={1} style={{ height: 106 }}>
                <Grid item>
                    <Skeleton width={300} />
                    <Skeleton width={200} height={12} />
                    <Skeleton width={100} height={12} />
                    <Skeleton width={160} height={12} />
                    <Skeleton width={230} height={12} />
                </Grid>
                <Grid item>
                    <Skeleton width={230} height={12} />
                </Grid>
            </Grid>
        );
    }

    return (
        <Box display="flex" flexDirection="column" maxWidth={maxWidth}>
            <Box display="flex" flexDirection="row" gap={2}>
                <Box flexBasis="50%">
                    <Prop>
                        <b>{t('commonLabels.vehicle')}: </b>
                        {[data.vehicle?.brand, data.vehicle?.model, data.vehicle?.year]
                            .filter(Boolean)
                            .join(' ') || '--'}
                        {data.vehicle?.plates ? `, ${data.vehicle.plates}` : ''}
                    </Prop>

                    <Prop>
                        <b>{t('commonLabels.vin')}: </b>
                        {data.vehicle?.vin || '--'}
                    </Prop>
                </Box>
                <Box>
                    <Prop>
                        <b>{t('workshopPlanner.schedulePopup.orderDateAndTime')}: </b>
                        {data.deliveryDate
                            ? DateTime.fromISO(data.deliveryDate).toFormat('dd/MM/yy - HH:mm') +
                              ' hrs.'
                            : '--'}
                    </Prop>
                </Box>
            </Box>
            <Divider style={{ margin: '8px 0' }} />
            <OverlayScrollbarsComponent style={{ maxHeight: '120px' }}>
                <Prop>
                    <b>{t('workshopPlanner.orderPopup.reasonForAppointment')}:</b>
                    {isAppointmentDataLoading && !!data.appointment ? (
                        <Skeleton width={200} />
                    ) : (
                        <>
                            {reasonsList.length ? (
                                <StyledList>
                                    {reasonsList.map((reason, index) => (
                                        <StyledListItem key={index}>{reason}</StyledListItem>
                                    ))}
                                </StyledList>
                            ) : (
                                ' --'
                            )}
                        </>
                    )}
                </Prop>
            </OverlayScrollbarsComponent>
            <Divider style={{ margin: '8px 0' }} />
            <OverlayScrollbarsComponent style={{ maxHeight: '120px' }}>
                <Prop>
                    <b>{t('orderDetails.additionalNotes.notesForCustomer')}:</b>
                    {customerNotes.filter((note) => note.text.trim().length > 0).length ? (
                        <StyledList>
                            {customerNotes.map((note, index) => (
                                <StyledListItem key={index}>{note.text}</StyledListItem>
                            ))}
                        </StyledList>
                    ) : (
                        ` ${t('orderDetails.additionalNotes.noNotesForCustomer')}`
                    )}
                </Prop>
                <Prop>
                    <b>{t('orderDetails.additionalNotes.notesForInternal')}:</b>
                    {internalNotes.filter((note) => note.text.trim().length > 0).length ? (
                        <StyledList>
                            {internalNotes.map((note, index) => (
                                <StyledListItem key={index}>{note.text}</StyledListItem>
                            ))}
                        </StyledList>
                    ) : (
                        ` ${t('orderDetails.additionalNotes.noInternalNotes')}`
                    )}
                </Prop>
            </OverlayScrollbarsComponent>
            <Divider style={{ margin: '8px 0 8px 0' }} />
        </Box>
    );
}
