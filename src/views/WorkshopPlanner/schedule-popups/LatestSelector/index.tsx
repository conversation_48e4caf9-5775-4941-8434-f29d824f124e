import {
    Box,
    Chip,
    Fade,
    styled,
    Tooltip,
    tooltipClasses,
    TooltipProps,
    useMediaQuery,
} from '@mui/material';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import uniqBy from 'lodash/uniqBy';
import { ReactNode } from 'react';

type LatestSelectorProps<T> = {
    options: T[];
    getLabel: (value: T) => string;
    value?: T;
    onSelected: (value: T) => void;
    maxLength?: number;
    tooltip?: (value: T) => ReactNode;
};

const SelectableChip = styled(Chip)<{ selected: boolean }>(({ theme, selected }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.primary.light,
    padding: 0,
    '--bg': selected ? 'var(--cm5)' : '#fff',
    backgroundColor: 'var(--bg)',
    border: `1px solid ${selected ? theme.palette.primary.main : theme.palette.neutral[4]}`,
    height: 24,
    cursor: 'pointer',
    '&:hover': {
        borderColor: theme.palette.primary.main,
        boxShadow: `0 0 0 1px ${theme.palette.primary.main}`,
    },
    '&:focus, &:hover': {
        backgroundColor: 'var(--bg)',
    },
}));

export default function LatestSelector<T>({
    value,
    onSelected,
    getLabel,
    options,
    maxLength = 9,
    tooltip,
}: LatestSelectorProps<T>) {
    const isSmall = useMediaQuery('(max-width: 1440px)');
    if (options.length === 0) return null;
    const filteredOptions = uniqBy(options, getLabel);

    return (
        <>
            <InputWrapper
                slotProps={{
                    label: {
                        sx: {
                            fontWeight: 'normal',
                        },
                    },
                }}
            >
                <Box
                    display="flex"
                    gap={0.75}
                    paddingRight={10}
                    marginRight={1}
                    marginBottom={1}
                    {...(isSmall && { flexWrap: 'wrap' })}
                >
                    {filteredOptions.map((x) => {
                        let label = getLabel(x);
                        const selected = value ? getLabel(x) === getLabel(value) : false;
                        label =
                            label.length > maxLength
                                ? `..${label.substring(label.length - maxLength, label.length)}`
                                : label;
                        return tooltip ? (
                            <StyledTooltip title={tooltip(x)}>
                                <SelectableChip
                                    aria-selected={selected}
                                    onClick={() => onSelected(x)}
                                    selected={selected}
                                    key={label}
                                    label={label}
                                />
                            </StyledTooltip>
                        ) : (
                            <SelectableChip
                                aria-selected={selected}
                                onClick={() => onSelected(x)}
                                selected={selected}
                                key={label}
                                label={label}
                            />
                        );
                    })}
                </Box>
            </InputWrapper>
        </>
    );
}

export const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip
        TransitionProps={{ timeout: 100 }}
        TransitionComponent={Fade}
        {...props}
        classes={{ popper: className }}
    />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#5c6477',
        maxWidth: 'fit-content',
    },

    [`& .${tooltipClasses.arrow}::before`]: {
        backgroundColor: '#5c6477',
    },
}));
