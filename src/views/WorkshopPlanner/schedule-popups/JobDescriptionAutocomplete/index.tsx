import { Autocomplete, Box, CircularProgress, ListItemButton } from '@mui/material';
import TextField from '@mui/material/TextField';
import { useQuery } from '@tanstack/react-query';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { useCallback, useMemo, useState } from 'react';

import { useAutoCompleteCommonStyles } from '../../../Components/common';
import { JobDescriptionDto } from 'api/workshopPlanner';
import theme from 'theme';

type JobDescriptionsAutocompleteProps = {
    cacheKey: string;
    getJobDescriptions: () => Promise<JobDescriptionDto[]> | [];
    disabled?: boolean;
    excludeItems: JobDescriptionDto[];
    onChange: (item: JobDescriptionDto) => void;
};

type JobDescriptionsAutocompleteElement =
    | {
          new: false;
          jobDescription: JobDescriptionDto;
      }
    | {
          new: true;
          name: string;
      };

const JobDescriptionsAutocomplete = ({
    disabled,
    excludeItems,
    cacheKey,
    onChange,
    getJobDescriptions,
}: JobDescriptionsAutocompleteProps) => {
    const { t } = useAppTranslation();
    const commonStyles = useAutoCompleteCommonStyles();
    const [open, setOpen] = useState<boolean>(false);
    const [searchQuery, setSearchQuery] = useState<string>('');

    const { data: allJobDescriptionsData, isLoading: jobDescriptionsAreLoading } = useQuery(
        ['scheduleJob', 'jobDescription', cacheKey],
        getJobDescriptions,
        {
            cacheTime: 30000,
            enabled: open,
            staleTime: 30000,
        }
    );

    const allJobDescriptions = useMemo(
        () => allJobDescriptionsData || [],
        [allJobDescriptionsData]
    );

    const filteredJobDescriptions = useMemo(() => {
        if (!searchQuery) return allJobDescriptions;
        return allJobDescriptions.filter((item) =>
            item.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }, [allJobDescriptions, searchQuery]);

    const jobDescriptionOptions: JobDescriptionsAutocompleteElement[] = useMemo(() => {
        const filteredItems = filteredJobDescriptions.filter(
            (item) => !excludeItems?.some((excludedItem) => excludedItem.id === item.id)
        );

        if (searchQuery.length && filteredItems.length === 0) {
            return [
                {
                    new: true,
                    name: searchQuery,
                },
            ];
        }

        return filteredItems.map((item) => ({
            new: false,
            jobDescription: item,
        }));
    }, [filteredJobDescriptions, searchQuery, excludeItems]);

    return (
        <Box>
            <Autocomplete<JobDescriptionsAutocompleteElement>
                id="job-descriptsion-autocomplete"
                autoComplete
                autoHighlight
                disabled={disabled}
                open={open && !disabled}
                options={jobDescriptionsAreLoading ? [] : jobDescriptionOptions}
                inputValue={searchQuery}
                value={null}
                classes={{ paper: commonStyles.paperAutoComplete }}
                loading={jobDescriptionsAreLoading}
                loadingText={<CircularProgress style={{ color: 'var(--cm2)' }} size={20} />}
                noOptionsText={t('commonLabels.noDataSelector')}
                renderInput={useCallback(
                    (params) => (
                        <form autoComplete={'new-password'}>
                            <TextField
                                {...params}
                                fullWidth
                                placeholder={t('commonLabels.selectJobDescription')}
                                InputProps={{
                                    ...params.InputProps,
                                    type: 'text',
                                    className: commonStyles.root,
                                    classes: {
                                        focused: commonStyles.inputFocused,
                                    },
                                }}
                            />
                        </form>
                    ),
                    [commonStyles]
                )}
                filterOptions={(options) => options}
                renderOption={(props, option) => {
                    if (option.new) {
                        return (
                            <ListItemButton
                                disableRipple
                                component="li"
                                sx={{
                                    ...theme.typography.body1,
                                }}
                                {...props}
                            >
                                <PlusIcon size={15} style={{ marginRight: 2 }} />
                                <div>{option.name}</div>
                            </ListItemButton>
                        );
                    } else {
                        return (
                            <ListItemButton
                                disableRipple
                                component="li"
                                sx={{
                                    ...theme.typography.body1,
                                }}
                                {...props}
                            >
                                {option.jobDescription.name}
                            </ListItemButton>
                        );
                    }
                }}
                isOptionEqualToValue={(_option, _value) => false}
                getOptionLabel={(option) => (option.new ? option.name : option.jobDescription.name)}
                onChange={(_e, value) => {
                    if (!value) return;
                    if (value.new) {
                        onChange &&
                            onChange({
                                id: crypto.randomUUID(),
                                name: value.name,
                            });
                    } else {
                        onChange && onChange(value.jobDescription);
                    }
                    setSearchQuery('');
                }}
                onInputChange={(_e, value) => {
                    setSearchQuery(value);
                }}
                onOpen={() => setOpen(true)}
                onClose={() => setOpen(false)}
            />
        </Box>
    );
};

export default JobDescriptionsAutocomplete;
