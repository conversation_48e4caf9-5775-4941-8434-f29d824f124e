import { Backdrop, IconButton, Paper, styled } from '@mui/material';
import { CSSProperties } from '@mui/styles';
import { PropsWithTestId } from 'common/components';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { rgba } from 'common/styles/ColorHelpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM, { flushSync } from 'react-dom';
import Draggable, { ControlPosition } from 'react-draggable';

export type SchedulePopupLayoutProps = PropsWithTestId<{
    title: string;
    ts: number;
    anchorEl?: HTMLElement;
    onClose: () => void;
    onClosed?: () => void;
    children?: React.ReactNode;
    open: boolean;
    circleColor?: string | null;
    hideCircle?: boolean;
    slotProps?: {
        root?: React.HTMLAttributes<HTMLElement>;
        paper?: React.HTMLAttributes<HTMLElement>;
        main?: React.HTMLAttributes<HTMLElement>;
    };
}>;

const Root = styled('div')<{ slotProps?: CSSProperties }>(({ theme, slotProps }) => ({
    position: 'fixed',
    left: 0,
    top: 0,
    outline: `4px solid transparent`,
    transition: 'outline .15s',
    '&.react-draggable-dragging': {
        outline: `4px solid ${rgba(theme.palette.neutral[9], 0.5)}`,
        userSelect: 'none !important',
    },
    borderRadius: 10,
    maxWidth: 'min(80vw, 820px)',
    ...(slotProps?.root ? { ...slotProps.root } : {}),
}));

const SPaper = styled(Paper)<{ slotProps?: CSSProperties }>(({ slotProps }) => ({
    padding: 0,
    overflow: 'hidden',
    borderRadius: 10,
    ...(slotProps?.sPaper ? { ...slotProps.sPaper } : {}),
}));

const Header = styled('header')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[3],
    padding: '15px 30px',
    position: 'relative',
    paddingRight: 60,
    cursor: 'move',
}));

const Main = styled('main')<{ slotProps?: CSSProperties }>(({ slotProps }) => ({
    padding: '10px 30px 20px 30px',
    minWidth: 500,
    ...scrollbarStyle(),
    ...(slotProps?.main ? { ...slotProps.main } : {}),
}));

const CloseIconButton = styled(IconButton)({
    position: 'absolute',
    right: 10,
    top: 10,
    padding: 5,
});

const Title = styled('h1')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[8],
    margin: 0,
}));

export default function SchedulePopupLayout({
    open,
    title,
    anchorEl,
    onClose,
    onClosed,
    ts,
    children,
    circleColor,
    dataTestId,
    hideCircle = false,
    slotProps,
}: SchedulePopupLayoutProps) {
    const draggableId = useMemo(
        () => `draggable-schedule-popup-handle-${Math.random().toString(36).substring(2)}`,
        []
    );
    const [position, setPosition] = useState<ControlPosition>({ x: 0, y: 0 });

    const ref = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (open && ref.current) {
            setPosition(positionPopup(ref.current, anchorEl));
        }
    }, [open, ref, anchorEl]);

    return ReactDOM.createPortal(
        <Backdrop
            onClick={onClose}
            invisible
            style={{ zIndex: 1000 }}
            open={open}
            onTransitionEnd={() => {
                if (!open) {
                    onClosed?.();
                }
            }}
        >
            {open && (
                <Draggable
                    position={position}
                    enableUserSelectHack={false}
                    onDrag={(_, { x, y }) => {
                        // NOTE (MB) prevents popup from lagging behind on low-end devices but hurts overall performance while dragging
                        // i.e. this is the "lesser evil" but it's still pretty bad (if you have 2010 laptop)
                        flushSync(() => setPosition({ x, y }));
                    }}
                    bounds="body"
                    handle={'#' + draggableId}
                >
                    <Root
                        data-test-id={dataTestId}
                        ref={ref}
                        onClick={(e) => e.stopPropagation()}
                        {...slotProps?.root}
                    >
                        <SPaper {...slotProps?.paper}>
                            <Header id={draggableId}>
                                <Title>
                                    {title}
                                    {!hideCircle && (
                                        <ColorCircle
                                            style={{
                                                backgroundColor: circleColor ? circleColor : '#fff',
                                            }}
                                        />
                                    )}
                                </Title>

                                <CloseIconButton onClick={onClose} size="large">
                                    <CloseIcon fill="currentColor" />
                                </CloseIconButton>
                            </Header>
                            <Main {...slotProps?.main}>{children}</Main>
                        </SPaper>
                    </Root>
                </Draggable>
            )}
        </Backdrop>,
        document.body
    );
}

function createBlueprint(el: HTMLElement) {
    const rect = el.getBoundingClientRect();
    const b = document.createElement('div');
    b.style.position = 'fixed';
    b.style.top = '0px';
    b.style.left = '0px';
    b.style.width = `${rect.width}px`;
    b.style.height = `${rect.height}px`;
    b.style.transform = `translate(${rect.left}px, ${rect.top}px)`;
    b.style.pointerEvents = 'none';
    document.body.appendChild(b);
    return b;
}

function positionPopup(
    popupOriginal: HTMLElement,
    anchor: HTMLElement | undefined
): { x: number; y: number } {
    if (!anchor) {
        // position in the center
        const rect = popupOriginal.getBoundingClientRect();
        return {
            x: window.innerWidth / 2 - rect.width / 2,
            y: window.innerHeight / 2 - rect.height / 2,
        };
    }

    // create a "blueprint" element that is just a rectangle with the same exact position and size
    // this is to avoid problems with css transition (if it's set)
    const popup = createBlueprint(popupOriginal);

    let popupRect = popup.getBoundingClientRect();
    const { height, width } = popupRect;
    const anchorRect = anchor.getBoundingClientRect();
    // position it at the top
    let x: number = -1,
        y: number = -1;

    popup.style.transform = '';
    const set = (x_: number, y_: number) => {
        x = x_;
        y = y_;
        popup.style.transform = `translate(${x}px, ${y}px)`;
    };

    // position at the top
    let valX = anchorRect.right;
    set(anchorRect.right, anchorRect.top - height);
    popupRect = popup.getBoundingClientRect();

    // case when there is not enough space on the right
    if (popupRect.right > window.innerWidth - 20) {
        valX = anchorRect.left - width;
        if (valX < 0) {
            valX = window.innerWidth / 2 - width / 2;
            set(valX, anchorRect.top - height - 20);
            popupRect = popup.getBoundingClientRect();
        } else {
            set(valX, anchorRect.bottom - height - 20);
            popupRect = popup.getBoundingClientRect();
        }
    }

    // case when the window went beyond the screen from above
    if (popupRect.top < 0) {
        set(valX, anchorRect.bottom);
        popupRect = popup.getBoundingClientRect();

        if (anchorRect.left - width > 0) {
            set(valX, anchorRect.top);
            popupRect = popup.getBoundingClientRect();
        }
    }

    // case when the window goes beyond the screen from the bottom
    if (popupRect.bottom > window.innerHeight) {
        // doesn't fit at the bottom, move up a bit
        const valY = anchorRect.top - (popupRect.bottom - window.innerHeight + 20);
        set(anchorRect.left - width, valY);
        popupRect = popup.getBoundingClientRect();

        // doesn't fit at the left, put it at the edge of the screen
        if (popupRect.left < 0) {
            // leave part of the duration visible and display it on the right
            if (anchorRect.left + width + 100 < window.innerWidth - 20) {
                set(anchorRect.left + 100, valY);
            } else {
                // case when there are also places on the right, we display on the left overlapping the duration
                set(50, valY);
            }
        }
    }

    popupOriginal.style.transform = `translate(${x}px, ${y}px)`;
    popup.remove(); // removes the popup's blueprint, not popup itself (see start of this function)
    return { x, y };
}

const ColorCircle = styled('div')({
    borderRadius: 100,
    height: 16,
    width: 16,
    marginLeft: 8,
    position: 'relative',
    top: 3,
    display: 'inline-block',
});
