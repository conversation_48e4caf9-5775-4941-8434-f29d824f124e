import use<PERSON><PERSON><PERSON><PERSON><PERSON> from 'common/hooks/useForceRender';
import { DateTime } from 'luxon';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import ScheduleAppointmentWorkAltPopup from '.';

interface IScheduleAppointmentPopupContext {
    open(
        appointmentId: string,
        ts?: number,
        duration?: number,
        userId?: string,
        planningId?: number,
        fitInitialBlock?: boolean
    ): void;
}

const ScheduleAppointmentPopupContext = createContext<IScheduleAppointmentPopupContext | null>(
    null
);

export function useScheduleAppointmentAltPopup() {
    const ctx = useContext(ScheduleAppointmentPopupContext);
    if (!ctx) throw new Error('cannot use ScheduleAppointmentPopupContext');
    return ctx;
}

export function ScheduleAppointmentPopupAltProvider({ children }: React.PropsWithChildren<{}>) {
    const state = useRef<{
        appointmentId: string;
        ts: number;
        duration: number;
        userId: string | null;
        planningId: number | null;
        fitInitialBlock: boolean;
    }>({
        appointmentId: '',
        ts: 0,
        duration: 0,
        userId: null,
        planningId: null,
        fitInitialBlock: false,
    });
    const fr = useForceRender();
    const ctx: IScheduleAppointmentPopupContext = useMemo(
        () => ({
            open: (appointmentId, ts, duration, userId, planningId, fitInitialBlock) => {
                state.current.appointmentId = appointmentId;
                state.current.ts =
                    ts ??
                    DateTime.now()
                        .set({ millisecond: 0, second: 0, minute: 0, hour: 0 })
                        .toMillis();
                state.current.duration = duration ?? 0;
                state.current.userId = userId ?? null;
                state.current.planningId = planningId ?? null;
                state.current.fitInitialBlock = fitInitialBlock ?? false;

                fr();
            },
        }),
        [fr]
    );

    const close = useCallback(() => {
        state.current.appointmentId = '';
        fr();
    }, [fr]);

    return (
        <ScheduleAppointmentPopupContext.Provider value={ctx}>
            <ScheduleAppointmentWorkAltPopup
                appointmentId={state.current.appointmentId}
                ts={state.current.ts}
                duration={state.current.duration}
                userId={state.current.userId}
                planningId={state.current.planningId}
                fitInitialBlock={state.current.fitInitialBlock}
                onClose={close}
                open={!!state.current.appointmentId}
            />
            {children}
        </ScheduleAppointmentPopupContext.Provider>
    );
}
