import { Box, Grid, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import AppointmentsApi, {
    joinAppointmentReasonsToDetailedAppointmentReasons,
} from 'api/appointments';
import { isCmosError } from 'api/error';
import { UserListItem } from 'api/users';
import { Button } from 'common/components/Button';
import DateForm<PERSON>ield from 'common/components/Inputs/DateFormField';
import DurationForm<PERSON>ield from 'common/components/Inputs/DurationFormField';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { selectUser } from 'store/slices/users';
import {
    createPlannedBlockThunk,
    loadScheduleThunk,
    planningsActions,
    selectPlanning,
    selectPlannings,
    selectPlanningUsers,
    selectUserWithBlocks,
} from 'store/slices/wp/plannings';
import { calcMostSuitableTsInMillis, useMaxAvailableTimeInMinutes } from 'store/util/wp';
import { isDateValid } from 'utils';
import UTInput from 'views/WorkshopPlanner/common/UTInput';
import { useTechnicianCapacityAlert } from 'views/WorkshopPlanner/TechnicianCapacity/TechnicianCapacityAlert';
import SchedulePopupLayout, { SchedulePopupLayoutProps } from '../SchedulePopupLayout';
import AppointmentDetails from './AppointmentDetails';

import { JobDescriptionDto } from 'api/workshopPlanner';
import WpAppointmentsApi from 'api/workshopPlanner/appointments';
import { isErrorResponse } from 'services/Server';
import { selectRepairShopWpConfiguration } from 'store/slices/globalSettingsSlice';
import UserSelect from 'views/Components/UserSelect/UserSelect';
import { usePlanningsTabs } from 'views/WorkshopPlanner/PlanningsTabs/context';
import { useIsSchedulingAllowed } from 'views/WorkshopPlanner/TechnicianCapacity/helpers';
import { scrollToUserRow } from '../helpers';
import JobDescriptionsAutocomplete from '../JobDescriptionAutocomplete';
import JobDescriptionsList from '../JobDescriptionList';

type ScheduleAppointmentWorkAltPopupProps = Omit<
    SchedulePopupLayoutProps,
    'title' | 'circleColor'
> & {
    appointmentId: string;
    duration: number;
    userId: string | null;
    planningId: number | null;
    fitInitialBlock: boolean;
};

const ErrorText = styled('p')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--danger)',
}));

const JobDescriptionText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    marginBottom: '6px',
    marginTop: '17px',
    color: 'var(--neutral8)',
}));

export default function ScheduleAppointmentWorkAltPopup({
    onClose,
    ts: initialTs,
    appointmentId,
    duration: initialDuration,
    userId: initialUserId,
    planningId: initialPlanningId,
    fitInitialBlock,
    ...props
}: ScheduleAppointmentWorkAltPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const [planningId, setPlanningId] = useState<number | null>(null);
    const [userId, setUserId] = useState<string | null>(null);

    const planningUserIds = useAppSelector(selectPlanningUsers);
    const planning = useAppSelector((r) => (planningId ? selectPlanning(r, planningId) : null));
    const user: UserListItem | null = useAppSelector((r) =>
        userId ? selectUser(r, userId) ?? null : null
    );

    const [duration, setDuration] = useState(0);
    const [date, setDate] = useState(new Date());
    const [time, setTime] = useState<[number, number]>([0, 0]);
    const [selectedReasons, setSelectedReasons] = useState<JobDescriptionDto[]>([]);
    const [mostSuitableTs, setMostSuitableTs] = useState(-1);

    const technicianCapacityAlertPopup = useTechnicianCapacityAlert();
    const isSchedulingAllowed = useIsSchedulingAllowed(
        userId ? { hours: duration / 60, userId } : undefined
    );

    const { enableWorkshopJobIntegration, integrationAccountName } = useAppSelector(
        selectRepairShopWpConfiguration
    );

    const dt = useMemo(() => {
        const dt = DateTime.fromJSDate(date).set({
            hour: time[0],
            minute: time[1],
        });
        return dt;
    }, [date, time]);
    const ts = dt.toMillis();
    const maxDuration = useMaxAvailableTimeInMinutes(user, dt);
    const userWithBlocks = useAppSelector((r) => selectUserWithBlocks(r, initialUserId ?? null));

    const planningsTabs = usePlanningsTabs();

    useEffect(() => {
        if (!props.open) {
            return;
        }

        if (initialUserId && userWithBlocks && fitInitialBlock) {
            const mostSuitableTs = calcMostSuitableTsInMillis(
                initialUserId,
                initialDuration,
                initialTs,
                userWithBlocks
            );

            setMostSuitableTs(mostSuitableTs);
        } else {
            setMostSuitableTs(initialTs);
        }
        // TODO investigate why is this warning here, updating list of deps can cause some issues, so do not touch unless you know what is going on
    }, [initialUserId, initialDuration, initialTs, fitInitialBlock, props.open]);

    const { data: appointment } = useQuery(
        ['appointment', appointmentId],
        () => AppointmentsApi.get(appointmentId),
        {
            enabled: !!appointmentId,
            staleTime: 1000,
            cacheTime: 20000,
        }
    );

    const customerName = appointment
        ? `${appointment.customer.firstName} ${appointment.customer.lastName}`.trim()
        : '';
    const appointmentTitle = appointment ? `#${appointment.number} - ${customerName}` : '';

    useEffect(() => {
        const dt = DateTime.fromMillis(mostSuitableTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
    }, [mostSuitableTs]);

    const handleClosed = useCallback(() => {
        const dt = DateTime.fromMillis(mostSuitableTs).set({ second: 0, millisecond: 0 });
        setDate(dt.toJSDate());
        setTime([dt.hour, dt.minute]);
    }, [mostSuitableTs]);

    const valid = !!(duration >= 1 && duration <= maxDuration && planningId && userId);

    const createBlock = useMutation(
        async () => {
            if (!valid) return;
            if (!userId) throw new Error('cannot create planned order block without userId');
            if (!planningId) throw new Error('cannot create planned order block without userId');

            const result = await dispatch(
                createPlannedBlockThunk({
                    userId,
                    appointmentId,
                    duration,
                    position: ts,
                    planningId,
                    jobDescriptionRecords: selectedReasons,
                })
            );

            if (result.meta.requestStatus === 'rejected') {
                if (result.meta.rejectedWithValue) {
                    const e = result.payload;
                    if (isCmosError(e)) {
                        console.error('[wp>planned order scheduling]', e);
                    } else {
                        console.error('[wp>planned order scheduling] non cmos error', e);
                    }
                    throw e;
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                    throw new Error('unknown error');
                }
            }
        },
        {
            onSuccess: () => {
                if (!appointment) {
                    console.error('appointment is not set');
                } else {
                    dispatch(
                        planningsActions.addAppointment({
                            id: appointment.id,
                            status: appointment.status,
                            number: appointment.number,
                            orderNumber: appointment.order?.number ?? null,
                            orderId: appointment.order?.id ?? null,
                            startTime: appointment.startsAt,
                            serviceAdvisor: appointment.serviceAdvisor,
                            reasons: joinAppointmentReasonsToDetailedAppointmentReasons(
                                appointment.reasons,
                                appointment.customReasons
                            ),
                            notes: appointment.notes ?? [],
                            customer: {
                                id: appointment.customer.id,
                                firstName: appointment.customer.firstName,
                                lastName: appointment.customer.lastName,
                            },
                            vehicle: null,
                            blockJobDescriptions: appointment.jobDescriptions,
                        })
                    );
                }
                dispatch(planningsActions.removePreview());
                dispatch(loadScheduleThunk({}));
                toasters.success(
                    t('workshopPlanner.schedulePopup.appointmentCreated.text', {
                        appointmentNumber: appointment?.number,
                    }),
                    t('workshopPlanner.schedulePopup.appointmentCreated.title')
                );
                if (enableWorkshopJobIntegration && integrationAccountName) {
                    toasters.success(
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.text', {
                            integrationAccountName,
                        }),
                        t('workshopPlanner.orderPopup.jobCreatedIntegrationSuccess.title')
                    );
                }
                onClose();
            },
            onError: (error) => {
                if (isErrorResponse(error)) {
                    if (enableWorkshopJobIntegration && integrationAccountName) {
                        toasters.danger(
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.text', {
                                errorMessage: error.message,
                            }),
                            t('workshopPlanner.orderPopup.jobCreatedIntegrationError.title', {
                                integrationAccountName,
                            })
                        );
                    }
                }
            },
        }
    );

    useEffect(() => {
        if (props.open && userId && planningId) {
            const initialDt = DateTime.fromMillis(mostSuitableTs);
            const dt = DateTime.fromMillis(ts);
            if (
                ts >= 0 &&
                !(
                    dt.hour === 0 &&
                    dt.minute === 0 &&
                    initialDt.year === dt.year &&
                    initialDt.month === dt.month &&
                    initialDt.day === dt.day
                )
            ) {
                dispatch(
                    planningsActions.setPreview({
                        userId,
                        duration,
                        ts,
                        maxDuration,
                        disableAutomaticInitialPosition: true,
                    })
                );
            }
        }
    }, [props.open, dispatch, duration, userId, mostSuitableTs, ts, maxDuration, planningId]);

    useEffect(() => {
        setSelectedReasons([]);
    }, [appointment?.id]);

    useEffect(() => {
        if (props.open) {
            setDuration(initialDuration);
            setPlanningId(initialPlanningId);
            setUserId(initialUserId);
        } else {
            dispatch(planningsActions.removePreview());
            setDuration(0);
            setPlanningId(null);
            setUserId(null);
            setMostSuitableTs(-1);
        }
    }, [props.open, initialDuration, initialPlanningId, initialUserId, dispatch]);

    useEffect(() => {
        if (!planningId || !userId) return;

        const planningIsChanged = planningsTabs?.setPlanningId(planningId);

        setTimeout(
            () => {
                scrollToUserRow(planningId, userId);
            },
            planningIsChanged ? 1000 : 500
        );
    }, [planningId, userId, planningsTabs]);

    return (
        <SchedulePopupLayout
            dataTestId="scheduleAppointmentBlockPopup-alt"
            hideCircle
            ts={ts}
            onClose={onClose}
            onClosed={handleClosed}
            title={t('workshopPlanner.schedulePopup.appointmentAltTitle', {
                appointment: appointmentTitle,
            })}
            {...props}
        >
            <AppointmentDetails id={appointmentId} />
            <Grid container columnSpacing={2} rowSpacing={3}>
                <Grid item md={6}>
                    <InputWrapper
                        isRequired
                        showValidationIndicators
                        label={t('workshopPlanner.schedulePopup.teamMember')}
                    >
                        <UserSelect
                            userId={userId}
                            userIds={planningUserIds}
                            onChange={setUserId}
                            dataTestId="selectUser"
                            placeholder={t('workshopPlanner.schedulePopup.teamMemberPlaceholder')}
                            fullWidth
                        />
                    </InputWrapper>
                </Grid>
                <Grid item md={6}>
                    <PlanningSelect
                        userId={userId}
                        planningId={planningId}
                        onSelected={setPlanningId}
                    />
                </Grid>
                <Grid item md={3}>
                    <DateFormField
                        label={t('workshopPlanner.editBlock.startDate')}
                        enableEnterComplete
                        value={date}
                        onChange={(v) => {
                            if (v && isDateValid(v)) setDate(v);
                        }}
                    />
                </Grid>
                <Grid item md={3}>
                    <TimeFormField
                        value={time}
                        // isInvalid={hasOverlap}
                        onChange={setTime}
                        label={t('workshopPlanner.editBlock.startHour')}
                    />
                </Grid>
                <Grid item md={3}>
                    <DurationFormField
                        isRequired
                        value={duration}
                        label={t('workshopPlanner.schedulePopup.duration')}
                        name={'duration'}
                        onChange={setDuration}
                    />
                </Grid>
                <Grid item md={3}>
                    <UTInput
                        disabled={!planning?.isTechnicians}
                        durationInSeconds={duration * 60}
                        onChange={(v) => setDuration(Math.floor(v / 60))}
                    />
                </Grid>
            </Grid>
            {props.open && !createBlock.isLoading && duration > maxDuration ? (
                <ErrorText>{t('workshopPlanner.schedulePopup.exceedsDuration')}</ErrorText>
            ) : (
                <></>
            )}
            <Box marginBottom={4}>
                <JobDescriptionText>
                    {t('workshopPlanner.schedulePopup.jobDescription')}
                </JobDescriptionText>
                <Box display={'flex'} flexDirection={'column'} alignItems={'flex-end'}>
                    <Box width={'97%'}>
                        {selectedReasons.length > 0 && (
                            <JobDescriptionsList
                                onRemoveJobDescription={(reasonId) => {
                                    setSelectedReasons((prevItems) =>
                                        prevItems.filter((item) => item.id !== reasonId)
                                    );
                                }}
                                selectedJobDescriptions={selectedReasons}
                            />
                        )}
                        <JobDescriptionsAutocomplete
                            cacheKey={appointment?.id ? appointment?.id : ''}
                            getJobDescriptions={() =>
                                appointment?.id ? WpAppointmentsApi.getReasons(appointment?.id) : []
                            }
                            excludeItems={selectedReasons}
                            onChange={(inspectionItem) => {
                                setSelectedReasons((prevItems) => [...prevItems, inspectionItem]);
                            }}
                        />
                    </Box>
                </Box>
            </Box>
            <Box display="flex" justifyContent="end" gap={1.25} marginTop={2}>
                <Button
                    onClick={onClose}
                    w="sm"
                    label={t('commonLabels.cancel')}
                    color={Colors.Neutral3}
                />
                <Button
                    disabled={!valid || !isSchedulingAllowed}
                    onClick={() =>
                        technicianCapacityAlertPopup.checkTechnicianCapacity(createBlock.mutate, {
                            hours: duration / 60,
                            userId: userId!,
                        })
                    }
                    showLoader={createBlock.isLoading}
                    w="sm"
                    label={t('commonLabels.save')}
                />
            </Box>
        </SchedulePopupLayout>
    );
}

type PlanningSelectProps = {
    planningId: number | null;
    onSelected: (planningId: number | null) => void;
    userId: string | null;
};

function PlanningSelect({ planningId, onSelected, userId }: PlanningSelectProps) {
    const { t } = useAppTranslation();
    const { list: plannings } = useSelector(selectPlannings);
    const availablePlannings = useMemo(() => {
        const list = userId
            ? plannings
                  .filter((x) => x.userIds.includes(userId))
                  .map((x) => ({
                      ...x,
                      name: x.readonly
                          ? t(`settings.workshopPlannerSettings.planning.${x.name}`)
                          : x.name,
                  }))
            : [];
        list.sort((a, b) => a.name.localeCompare(b.name));
        return list;
    }, [plannings, userId, t]);
    const disabled = userId === null;

    useEffect(() => {
        if (planningId && availablePlannings.every((x) => x.id !== planningId)) {
            onSelected(null);
        }
    }, [availablePlannings, onSelected, planningId]);

    return (
        <InputWrapper
            isRequired
            disabled={disabled}
            showValidationIndicators
            label={t('workshopPlanner.schedulePopup.planning')}
        >
            <SSelectInput
                data-test-id="selectPlanning"
                placeholder={t('workshopPlanner.schedulePopup.planningPlaceholder')}
                fullWidth
                disabled={disabled}
                onChange={(e) => onSelected(Number(e.target.value))}
                value={planningId}
            >
                {availablePlannings.map((x) => (
                    <SMenuItem value={x.id} key={x.id}>
                        {x.name}
                    </SMenuItem>
                ))}
            </SSelectInput>
        </InputWrapper>
    );
}
