import { styled } from '@mui/material';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';

import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { JobDescriptionDto } from 'api/workshopPlanner';
import Box from '@mui/material/Box';
import { IconSize } from '../../../../common/styles/IconSize';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';

type JobDescriptionsListProps = {
    onRemoveJobDescription: (jobDescriptionId: string) => void;
    selectedJobDescriptions: JobDescriptionDto[];
};

const JobDescriptionCaption = styled('span')(({ theme }) => ({
    ...theme.typography.body1,
    color: 'black',
    position: 'relative',
    paddingLeft: '12px',
    '&::before': {
        content: '"•"',
        position: 'absolute',
        left: 0,
        top: '44%',
        transform: 'translateY(calc(-50% + 1px))',
        borderRadius: '50%',
    },
}));

export default function JobDescriptionsList({
    onRemoveJobDescription,
    selectedJobDescriptions,
}: JobDescriptionsListProps) {
    return (
        <OverlayScrollbarsComponent
            style={{
                maxHeight: '165px',
                overflowY: 'auto',
                marginBottom: '10px',
                marginLeft: '1px',
                boxSizing: 'border-box',
            }}
        >
            {selectedJobDescriptions.map((jobDescription) => (
                <Box
                    key={jobDescription.id}
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        height: '18px',
                    }}
                >
                    <JobDescriptionCaption>{jobDescription.name}</JobDescriptionCaption>
                    <Box
                        sx={{
                            flexShrink: 0,
                            textAlign: 'right',
                            marginRight: '13px',
                            width: '18px',
                            height: '18px',
                            borderRadius: '50%',
                            boxSizing: 'border-box',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            '&:hover': {
                                backgroundColor: '#E0E0E0',
                            },
                        }}
                        onClick={() => onRemoveJobDescription(jobDescription.id)}
                    >
                        <RemoveCircleIcon fill={'#F15857'} size={IconSize.S} />
                    </Box>
                </Box>
            ))}
        </OverlayScrollbarsComponent>
    );
}
