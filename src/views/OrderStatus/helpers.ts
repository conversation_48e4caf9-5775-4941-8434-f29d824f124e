import use<PERSON><PERSON><PERSON><PERSON><PERSON> from 'common/hooks/useForceRender';
import moment from 'moment';
import { useEffect, useRef } from 'react';
import { DEFAULT_SHOWN_COLUMNS } from 'store/slices/ordersStatus';

const getCurrentDate = () => {
    return moment().format('YYYY-MM-DD');
};

export const useCurrentDate = () => {
    const fr = useForceRender();
    const date = useRef<string | undefined>(undefined);
    if (!date.current) {
        date.current = getCurrentDate();
    }

    useEffect(() => {
        const interval = setInterval(() => {
            const newDate = getCurrentDate();
            if (date.current != newDate) {
                date.current = newDate;
                fr();
            }
        }, 60000);

        return () => clearInterval(interval);
    }, [fr]);
};

export const DEFAULT_COLUMNS_TRANSLATION_KEYS: Record<string, string> = {
    [DEFAULT_SHOWN_COLUMNS.DELIVERY_TIME]: 'status.orders.headers.deliveryTime',
    [DEFAULT_SHOWN_COLUMNS.TOWER]: 'status.orders.headers.tower',
    [DEFAULT_SHOWN_COLUMNS.ORDER_NUMBER]: 'status.orders.headers.#order',
    [DEFAULT_SHOWN_COLUMNS.CUSTOMER]: 'status.orders.headers.customer',
    [DEFAULT_SHOWN_COLUMNS.MODEL]: 'status.orders.headers.model',
    [DEFAULT_SHOWN_COLUMNS.PLATES]: 'status.orders.headers.plates',
    [DEFAULT_SHOWN_COLUMNS.PHASE]: 'status.orders.headers.phase',
};
