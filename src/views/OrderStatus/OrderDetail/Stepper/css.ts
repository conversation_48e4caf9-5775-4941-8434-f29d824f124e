import { StepConnector, StepLabel } from '@mui/material';
import { makeStyles, withStyles } from '@mui/styles';
import BackGroundLoginTransparent from '../../../../assets/images/backgroundLoginTransparent.png';
import { Colors } from '../../../../common/styles/Colors';

export const useStyles = makeStyles((theme) => {
    return {
        stepperContainer: {
            backgroundImage: `url(${BackGroundLoginTransparent})`,
            backgroundPosition: 'right',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            height: '100%',
            width: '100%',
            backgroundColor: theme.palette.neutral[2],
            display: 'flex',
            justifyContent: 'space-around',
            [theme.breakpoints.down(541)]: {
                alignItems: 'baseline',
            },
            [theme.breakpoints.down(350)]: {
                backgroundPosition: 'unset',
            },
        },
        stepper: {
            backgroundColor: 'transparent !important',
            width: '75%',
        },
        statusIcon: {
            fontSize: '60px !important',
        },
        statusText: {
            fontWeight: 700,
            fontSize: '24px',
            marginBottom: '1rem',
        },
        pollButton: {
            backgroundColor: Colors.Success,
            color: theme.palette.neutral[1],
            borderRadius: '50%',
            marginTop: '2em',
        },
        doneDiv: {
            color: Colors.Success,
            textAlign: 'center',
            [theme.breakpoints.down(541)]: {
                paddingTop: '0.8em',
            },
        },
        onHoldDiv: {
            color: Colors.Warning,
            textAlign: 'center',
        },
    };
});

export const CustomizedConnector = withStyles({
    active: {
        '& $line': {
            borderColor: Colors.CM1,
        },
    },
    completed: {
        '& $line': {
            borderColor: Colors.Success,
        },
    },
    line: {
        borderColor: Colors.Neutral7,
        borderRadius: 1,
        borderTopWidth: 3,
        textAlign: 'center',
    },
})(StepConnector);

export const CustomizedStepLabels = withStyles({
    root: {
        fontWeight: 700,
    },
    active: {
        color: `${Colors.CM1} !important`,
        fontWeight: 700,
        fontSize: '1.1rem',
    },
    completed: {
        color: `${Colors.Success} !important`,
    },
})(StepLabel);

export const iconStyles = makeStyles((theme) => ({
    root: {
        color: theme.palette.neutral[7],
        display: 'flex',
        alignItems: 'center',
    },
    active: {
        color: theme.palette.primary.main,
    },
    completed: {
        color: Colors.Success,
    },
    circle: {
        width: 20,
        height: 20,
        borderRadius: '50%',
        border: 'solid',
        borderColor: theme.palette.neutral[7],
    },
}));
