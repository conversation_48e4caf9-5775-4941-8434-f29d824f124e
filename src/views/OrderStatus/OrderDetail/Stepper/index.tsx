import { DoneOutlined, DriveEtaOutlined, PauseOutlined } from '@mui/icons-material';
import Check from '@mui/icons-material/Check';
import { Button, Grid, Step, Stepper } from '@mui/material';
import { StepIconProps } from '@mui/material/StepIcon';
import clsx from 'clsx';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import DealershipAPI from '../../../../api/status/Dealerships/Dealership';
import { WPOrderStatus } from '../../../../common/constants';
import { Colors } from '../../../../common/styles/Colors';
import StatusSteps from '../../../../datacontracts/Status/Orders/StatusSteps';
import StatusStepsLabels from '../../../../datacontracts/Status/Orders/StatusStepsLabels';
import { CustomizedConnector, CustomizedStepLabels, iconStyles, useStyles } from './css';

const StatusStepper = ({
    repairOrderStatus,
    organizationId,
}: {
    repairOrderStatus: string;
    organizationId: string;
}) => {
    const [activeStep, setActiveStep] = useState(0);
    const [windowWidth, setWindowWidth] = useState(0);
    const [dealerPoll, setDealerPoll] = useState('');
    const classes = useStyles();
    const { t } = useAppTranslation();
    useEffect(() => {
        const fetchData = async () => {
            const fetchPoll = await DealershipAPI.getDealerPoll(organizationId);
            return fetchPoll;
        };
        const findActiveStepIndex = (statusString: string): number => {
            if (repairOrderStatus === WPOrderStatus.qualityControl)
                statusString = WPOrderStatus.onProcess;
            return StatusSteps.findIndex((e) => e === statusString);
        };
        const activeIndex = findActiveStepIndex(repairOrderStatus);
        setActiveStep(activeIndex);
        setWindowWidth(window.innerWidth);
        if (repairOrderStatus === WPOrderStatus.done) {
            fetchData().then((data) => {
                setDealerPoll(data);
            });
        }
    }, [repairOrderStatus, organizationId]);
    function CustomizedIcons(props: StepIconProps) {
        const iconClasses = iconStyles();
        const { active, completed } = props;
        return (
            <div
                className={clsx(iconClasses.root, {
                    [iconClasses.active]: active,
                })}
            >
                {completed ? (
                    <Check className={iconClasses.completed} />
                ) : (
                    <>
                        {repairOrderStatus === WPOrderStatus.readyForDelivery ? (
                            <DriveEtaOutlined className={iconClasses.completed} />
                        ) : (
                            <>
                                {active ? (
                                    <DriveEtaOutlined className={iconClasses.active} />
                                ) : (
                                    <div className={iconClasses.circle} />
                                )}
                            </>
                        )}
                    </>
                )}
            </div>
        );
    }

    return (
        <Grid className={classes.stepperContainer} alignItems="center">
            {repairOrderStatus === WPOrderStatus.done ? (
                <Grid item className={classes.doneDiv}>
                    <Grid item>
                        <DoneOutlined className={classes.statusIcon} />
                    </Grid>
                    <Grid item className={classes.statusText}>
                        {t('orderStatus.detail.delivered')}
                    </Grid>
                    <Grid item>
                        {dealerPoll !== '' && (
                            <Button
                                style={{
                                    backgroundColor: Colors.Success,
                                    color: Colors.White,
                                    borderRadius: '51px',
                                    padding: '11px 16px',
                                    textTransform: 'none',
                                    marginTop: '2em',
                                }}
                                href={dealerPoll}
                            >
                                {t('orderStatus.detail.answerPoll')}
                            </Button>
                        )}
                    </Grid>
                </Grid>
            ) : (
                <>
                    {repairOrderStatus === WPOrderStatus.onHold ? (
                        <Grid item className={classes.onHoldDiv}>
                            <Grid item>
                                <PauseOutlined className={classes.statusIcon} />
                            </Grid>
                            <Grid item className={classes.statusText}>
                                {t('orderStatus.detail.authPending')}
                            </Grid>
                        </Grid>
                    ) : (
                        <Stepper
                            activeStep={activeStep}
                            alternativeLabel={windowWidth <= 541 ? false : true}
                            connector={<CustomizedConnector />}
                            className={classes.stepper}
                            orientation={windowWidth <= 541 ? 'vertical' : 'horizontal'}
                        >
                            {StatusStepsLabels.map((label) => (
                                <Step key={label}>
                                    <CustomizedStepLabels StepIconComponent={CustomizedIcons}>
                                        <strong>{label}</strong>
                                    </CustomizedStepLabels>
                                </Step>
                            ))}
                        </Stepper>
                    )}
                </>
            )}
        </Grid>
    );
};

export default StatusStepper;
