import { Backdrop, CircularProgress } from '@mui/material';
import OrderDetailAPI from 'api/status/Orders/OrderDetail';
import { STATUS_PAGE_ROUTES, WPOrderStatus } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Header from './Header';
import StatusStepper from './Stepper';
import styles from './styles.module.css';

const OrderStatusDetail = (props: any): JSX.Element => {
    const [repairOrder, setRepairOrder] = useState<any>({});
    const [loading, setLoading] = useState(true);
    const [organizationId, setOrganizationId] = useState<string>('');
    const orderNumber = useParams<{ orderNumber: string }>().orderNumber ?? '';
    const navigate = useNavigate();
    const { t } = useAppTranslation();

    const fetchData = async () => {
        const search = props.location.search;
        const params = new URLSearchParams(search);
        const organizationId = params.get('organizationId');
        setOrganizationId(organizationId || '');
        const fetchedRepairOrder = await OrderDetailAPI.getOrderDetail(
            orderNumber!,
            organizationId
        );
        return fetchedRepairOrder;
    };

    useEffect(() => {
        fetchData().then((fetchedRepairOrder) => {
            if (Object.keys(fetchedRepairOrder).length === 0) {
                setLoading(false);
                navigate({
                    pathname: STATUS_PAGE_ROUTES.ORDERS_DETAIL_NOT_FOUND,
                    search: '?orderNumber=' + orderNumber,
                });
            }
            setRepairOrder(fetchedRepairOrder);
            setLoading(false);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (
        <>
            <Header repairOrder={repairOrder} />
            <StatusStepper repairOrderStatus={repairOrder.status} organizationId={organizationId} />
            {repairOrder.status === WPOrderStatus.readyForDelivery && (
                <div className={styles.readyForDeliveryDiv}>
                    {t('orderStatus.detail.vehicleReady')}
                    <br />
                    {t('orderStatus.detail.holdingForDelivery')}
                </div>
            )}
            {repairOrder.status === WPOrderStatus.onHold && (
                <div className={styles.waitForAuthDiv}>
                    {t('orderStatus.detail.estimateQuestions')}
                </div>
            )}
            <Backdrop open={loading} className={styles.backDrop}>
                <CircularProgress />
            </Backdrop>
        </>
    );
};

export default OrderStatusDetail;
