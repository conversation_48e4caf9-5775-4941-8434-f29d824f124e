import { ChangeHistoryOutlined } from '@mui/icons-material';
import { Grid, Typography } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
// Interfaces
import IOrderDetailsStatusResponse from '../../../../datacontracts/Status/Orders/IOrderDetailsResponse';
import useStyles from './css';

const Header = ({ repairOrder }: { repairOrder: IOrderDetailsStatusResponse }) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    return (
        <>
            <Grid
                container
                spacing={3}
                direction="row"
                className={classes.headerContainer}
                alignItems="flex-end"
            >
                <Grid container item sm={12}>
                    <span>
                        {t('orderStatus.detail.orderNumber')}
                        <strong>{repairOrder.orderNumber}</strong>
                    </span>
                </Grid>
                <Grid container item sm={12}>
                    <Grid item sm={4}>
                        <span>
                            {t('orderStatus.detail.promiseDate')}
                            <br />
                            <strong>{repairOrder.promiseDate}</strong>
                        </span>
                    </Grid>
                    <Grid container item sm={4}>
                        <Grid item>
                            <span>
                                {repairOrder.model}
                                <br />
                                <strong>{repairOrder.licensePlate}</strong>
                            </span>
                        </Grid>
                    </Grid>
                    <Grid container item sm={4}>
                        <div className={classes.towerDiv}>
                            <Grid item md={12}>
                                {t('orderStatus.detail.tower')}
                            </Grid>
                            <Grid item md={12}>
                                <span className={classes.root}>
                                    <ChangeHistoryOutlined
                                        className={classes.icon}
                                        style={{ color: repairOrder.towerColor }}
                                    />
                                    <Typography component="span" className={classes.towerNumber}>
                                        {repairOrder.towerNumber}
                                    </Typography>
                                </span>
                            </Grid>
                        </div>
                    </Grid>
                </Grid>
            </Grid>
        </>
    );
};

export default Header;
