import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme) => {
    return {
        root: {
            position: 'relative',
            display: 'inline-flex',
            justifyContent: 'center',
            alignItems: 'center',
        },
        towerDiv: {
            display: 'flex',
            alignItems: 'center',
            alignContent: 'flex-start',
            flexDirection: 'column',
            [theme.breakpoints.down(600)]: {
                flexDirection: 'row',
            },
        },
        icon: {
            fontSize: '4.5em',
        },
        towerNumber: {
            position: 'absolute',
            lineHeight: 1,
            fontWeight: 'bold',
            top: '3em',
            fontSize: '0.8em',
        },
        headerContainer: {
            padding: '1rem 2rem',
            backgroundColor: theme.palette.neutral[1],
            fontWeight: 400,
            [theme.breakpoints.up(770)]: {
                padding: '1rem 2rem',
            },
        },
    };
});

export default useStyles;
