import { Grid, styled, Table, TableBody } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import AppointmentSettingsAPI from 'api/AppointmentSettings';
import StatusApi, { OrderDto } from 'api/status';
import AreaSpinner from 'common/components/AreaSpinner';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useRecalculateRegularly from 'common/hooks/useRecalculateRegularly';
import useToasters from 'common/hooks/useToasters';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { useCallback, useEffect, useState } from 'react';
import { FullScreen, useFullScreenHandle } from 'react-full-screen';
import { useAppDispatch, useAppSelector } from 'store';
import { ordersActions } from 'store/slices/ordersStatus';
import { selectActiveOrders, selectOrders } from 'store/slices/ordersStatus/selectors';
import StatusHeaderBar from './StatusHeaderBar';
import OrdersStatusTableHeaders from './TableHeaders';
import { OrdersStatusTableRow } from './TableRow';

export const OrdersStatus = () => {
    const [isFullScreen, setIsFullScreen] = useState(false);
    // Full screen handlers
    const fullScreenHandle = useFullScreenHandle();
    const handleFullScreenChange = (fullScreen: boolean) => {
        setIsFullScreen(fullScreen);
    };

    const orders = useAppSelector(selectOrders);
    const activeOrders = useAppSelector(selectActiveOrders);
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const [openingTime, setOpeningTime] = useState('08:00');
    const [closingTime, setClosingTime] = useState('19:00');
    const dispatch = useAppDispatch();

    const statusScreenEnabled = useRecalculateRegularly(() => {
        const currentDate = moment();
        const beforeTime = currentDate
            .clone()
            .hour(+openingTime.split(':')[0])
            .minute(+openingTime.split(':')[1])
            .second(0)
            .millisecond(0);
        const afterTime = beforeTime
            .clone()
            .hour(+closingTime.split(':')[0])
            .minute(+closingTime.split(':')[1]);
        return currentDate.isBetween(beforeTime, afterTime);
    }, 10000);

    const setNextActiveOrders = useCallback(
        (value: OrderDto[]) => {
            dispatch(ordersActions.setNextActiveOrders(value));
        },
        [dispatch]
    );

    // Automatically turns pages at a specified interval
    useEffect(() => {
        if (statusScreenEnabled && isFullScreen) {
            setNextActiveOrders(orders);
            const interval = setInterval(() => {
                setNextActiveOrders(orders);
            }, 10000);
            return () => clearInterval(interval);
        }
    }, [statusScreenEnabled, orders, isFullScreen, setNextActiveOrders]);

    useQuery(['appointment', 'settings'], () => AppointmentSettingsAPI.getAppointmentSettings(), {
        onSuccess: (response) => {
            const dayConfig = response.workingDays.find(
                (day) => day.dayNumber === moment().weekday()
            );
            setOpeningTime(dayConfig!.opening);
            setClosingTime(dayConfig!.closing);
        },
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), t('toasters.errorOccurred'));
        },
        cacheTime: Infinity,
        staleTime: 120000,
    });

    const { data, isFetching } = useQuery(['status', 'orders'], () => StatusApi.getOrders(), {
        refetchInterval: 60000, // 1 minute,
        onSuccess: (response) => {
            if (isFullScreen) {
                dispatch(ordersActions.setOrders(response));
            } else {
                dispatch(ordersActions.setAllOrders(response));
            }
        },
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), t('toasters.errorOccurred'));
        },
        enabled: statusScreenEnabled,
    });

    useEffect(() => {
        if (!data) return;

        if (isFullScreen) {
            dispatch(ordersActions.setOrders(data));
        } else {
            dispatch(ordersActions.setAllOrders(data));
        }
    }, [data, dispatch, isFullScreen]);

    return (
        // @ts-ignore TODO CMOS-1993 remove and fix
        <StyledFullScreen handle={fullScreenHandle}>
            <Grid container justifyContent="center">
                <Grid item xs={11}>
                    <StatusHeaderBar
                        fullScreenHandle={fullScreenHandle}
                        isFullScreen={isFullScreen}
                        handleFullScreenChange={handleFullScreenChange}
                    />
                </Grid>
                <Grid item xs={11}>
                    <StyledTableContainer>
                        <Table stickyHeader aria-label="sticky table">
                            <OrdersStatusTableHeaders />
                            <TableBody>
                                {activeOrders?.map((order: OrderDto) => (
                                    <OrdersStatusTableRow
                                        order={order}
                                        key={'row_' + order.orderNumber}
                                    />
                                ))}
                            </TableBody>
                        </Table>
                    </StyledTableContainer>
                    {isFetching && <StyledSpinner />}
                </Grid>
            </Grid>
        </StyledFullScreen>
    );
};

const StyledFullScreen = styled(FullScreen)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[2],
}));

const StyledTableContainer = styled('div')(({ theme }) => ({
    width: '100%',
    //Header + 10 rows
    maxHeight: 562,

    [theme.breakpoints.up('3xl')]: {
        maxHeight: 704,
    },
    [theme.breakpoints.up('4xl')]: {
        maxHeight: 1000,
    },
    [theme.breakpoints.up('5xl')]: {
        maxHeight: 1480,
    },
    overflowY: 'auto',
    border: '1px solid #dbdcdd',
    borderRadius: 12,
    ...scrollbarStyle(),
}));

const StyledSpinner = styled(AreaSpinner)({
    addingTop: 10,
});
