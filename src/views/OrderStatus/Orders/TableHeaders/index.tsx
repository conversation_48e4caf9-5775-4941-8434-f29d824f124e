import { styled, TableCell, TableHead, TableRow } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import { useAppSelector } from 'store';
import { selectDisplayableOrderStatusColumns } from 'store/slices/ordersStatus/selectors';
import theme from 'theme';
import { DEFAULT_COLUMNS_TRANSLATION_KEYS } from 'views/OrderStatus/helpers';

const OrdersStatusTableHeaders = () => {
    const { t } = useAppTranslation();

    const columns = useAppSelector(selectDisplayableOrderStatusColumns);
    const font = useMemo(() => {
        switch (columns.length) {
            case 3:
            case 4:
                return theme.typography.h2Inter;
            case 5:
            case 6:
                return theme.typography.h4Inter;
            case 7:
            default:
                return theme.typography.h5Inter;
        }
    }, [columns]);

    const displayedColumns = useMemo(
        () =>
            columns.map((x) => {
                return (
                    <StyledTableCell key={x} sx={{ ...font }} component="td" scope="row">
                        {t(DEFAULT_COLUMNS_TRANSLATION_KEYS[x])}
                    </StyledTableCell>
                );
            }),
        [columns, font]
    );

    return (
        <TableHead>
            <TableRow>{displayedColumns}</TableRow>
        </TableHead>
    );
};

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    height: 60,
    padding: '0 0 0 30px',
    backgroundColor: theme.palette.neutral[2],
    color: theme.palette.neutral[7],
    fontWeight: 700,
    textTransform: 'uppercase',

    [theme.breakpoints.up('3xl')]: {
        height: 88,
        fontSize: '18px',
        lineHeight: '21.78px',
    },
    [theme.breakpoints.up('4xl')]: {
        height: 125,
        fontSize: '24px',
        lineHeight: '29.05px',
    },
    [theme.breakpoints.up('5xl')]: {
        height: 185,
        fontSize: '42px',
        lineHeight: '50.83px',
    },
}));

export default OrdersStatusTableHeaders;
