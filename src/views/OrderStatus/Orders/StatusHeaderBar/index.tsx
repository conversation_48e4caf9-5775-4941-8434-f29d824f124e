import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { IconButton, styled, Typography } from '@mui/material';
import { CompressIcon } from 'common/components/Icons/CompressIcon';
import { ProyectIcon } from 'common/components/Icons/ProyectIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { FullScreenHandle } from 'react-full-screen';
import { useNavigate } from 'react-router-dom';
import { Http } from 'services/Http';
import isFullScreenAvailable from 'utils/isFullScreenAvailable';
import DefaultLogo from '../../../../assets/images/default-logo.png';
import { useAppSelector } from '../../../../store';
import { selectSettings } from '../../../../store/slices/globalSettingsSlice/selectors';
import { ColumnFilters } from '../ColumnFilters';

export type StatusHeaderBarProps = {
    fullScreenHandle: FullScreenHandle;
    isFullScreen: boolean;
    handleFullScreenChange: Function;
};

const fullscreenAvailable = isFullScreenAvailable('div');

const StatusHeaderBar = ({
    fullScreenHandle,
    isFullScreen,
    handleFullScreenChange,
}: StatusHeaderBarProps) => {
    const format = 'hh:mm A';
    const [currentTime, setCurrentTime] = useState(moment().format(format));
    const [isFS, setIsFS] = useState(isFullScreen);
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const backButtonContainerRef = useRef(null);

    const { repairShopSettings } = useAppSelector(selectSettings);
    const accountName = repairShopSettings!.accountName;
    const urlLogo = repairShopSettings!.consumerLogoPath?.replace('~', Http.HOST);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(moment().format(format));
        }, 1000);

        return () => clearInterval(interval);
    }, [format]);

    useEffect(() => {
        handleFullScreenChange(isFS);
    }, [isFS, handleFullScreenChange]);

    const handleBack = () => {
        navigate(ROUTES.ORDERS);
    };

    return (
        <>
            <ArrowTooltip
                content={t('status.orders.goBackTooltip')}
                PopperProps={{ container: backButtonContainerRef.current }}
            >
                <StyledIconContainer isFullScreen={isFullScreen} ref={backButtonContainerRef}>
                    <IconButton size="small" onClick={handleBack}>
                        <ArrowBackIcon />
                    </IconButton>
                </StyledIconContainer>
            </ArrowTooltip>
            <StyledHeaderContainer isFullScreen={isFullScreen}>
                <StyledContentContainer>
                    <StyledImage alt={accountName} src={urlLogo || DefaultLogo} />
                    <StyledLine />
                    <StyledTypography>{accountName}</StyledTypography>
                </StyledContentContainer>
                <StyledContentContainer>
                    <StyledClockContainer> {currentTime} </StyledClockContainer>
                    {fullscreenAvailable && (
                        <IconButton
                            onClick={() => {
                                isFS ? fullScreenHandle.exit() : fullScreenHandle.enter();
                                setIsFS((isFS) => !isFS);
                            }}
                            size="medium"
                        >
                            {isFS ? (
                                <StyledCompressIcon fill={Colors.GrayBlue} size={IconSize.L} />
                            ) : (
                                <StyledProyectIcon fill={Colors.GrayBlue} size={IconSize.L} />
                            )}
                        </IconButton>
                    )}

                    <ColumnFilters />
                </StyledContentContainer>
            </StyledHeaderContainer>
        </>
    );
};

const StyledHeaderContainer = styled('div')<{ isFullScreen: boolean }>(({ isFullScreen }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fafafa',
    padding: isFullScreen ? '25px 0 25px 0' : '10px 0 15px 0',
}));

const StyledIconContainer = styled('div')<{ isFullScreen: boolean }>(({ isFullScreen }) => ({
    padding: isFullScreen ? '45px 7px 0 7px' : '15px 7px 0 7px',
    width: '50px',
}));

const StyledContentContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const StyledImage = styled('img')(({ theme }) => ({
    width: 75,
    height: 75,
    objectFit: 'cover',
    borderRadius: '50%',

    [theme.breakpoints.up('3xl')]: {
        width: 120,
        height: 120,
    },
    [theme.breakpoints.up('4xl')]: {
        width: 132,
        height: 132,
    },
    [theme.breakpoints.up('5xl')]: {
        width: 230,
        height: 230,
    },
}));

const StyledTypography = styled(Typography)(({ theme }) => ({
    ...theme.typography.h4Inter,
    fontWeight: 'bold',
    color: Colors.GrayBlue,

    [theme.breakpoints.up('3xl')]: {
        fontSize: '28px',
        lineHeight: '33.89px',
    },
    [theme.breakpoints.up('4xl')]: {
        fontSize: '32px',
        lineHeight: '38.73px',
    },
    [theme.breakpoints.up('5xl')]: {
        fontSize: '50px',
        lineHeight: '60.51px',
    },
}));

const StyledLine = styled('div')(({ theme }) => ({
    borderLeft: '1px solid #CAC4D0',
    height: 64,
    marginLeft: 20,
    marginRight: 10,

    [theme.breakpoints.up('3xl')]: {
        height: 130,
    },
    [theme.breakpoints.up('4xl')]: {
        height: 130,
    },
    [theme.breakpoints.up('5xl')]: {
        height: 230,
    },
}));

const StyledClockContainer = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    fontWeight: 700,
    color: Colors.GrayBlue,
    marginRight: '12px',

    [theme.breakpoints.up('3xl')]: {
        fontSize: '28px',
        lineHeight: '33.89px',
    },
    [theme.breakpoints.up('4xl')]: {
        fontSize: '32px',
        lineHeight: '38.73px',
    },
    [theme.breakpoints.up('5xl')]: {
        fontSize: '50px',
        lineHeight: '60.51px',
    },
}));

const StyledCompressIcon = styled(CompressIcon)(({ theme }) => ({
    [theme.breakpoints.up('3xl')]: {
        width: 42,
        height: 42,
    },
    [theme.breakpoints.up('4xl')]: {
        width: 46,
        height: 46,
    },
    [theme.breakpoints.up('5xl')]: {
        width: 63,
        height: 63,
    },
}));

const StyledProyectIcon = styled(ProyectIcon)(({ theme }) => ({
    [theme.breakpoints.up('3xl')]: {
        width: 42,
        height: 42,
    },
    [theme.breakpoints.up('4xl')]: {
        width: 46,
        height: 46,
    },
    [theme.breakpoints.up('5xl')]: {
        width: 63,
        height: 63,
    },
}));

export default StatusHeaderBar;
