import { styled } from '@mui/material';
import { OrderDto } from 'api/status';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { convertReasonForPauseToLabel } from 'common/constants/ReasonForPause';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IconSize } from 'common/styles/IconSize';
import theme from 'theme';

export type PhaseColumnProps = {
    order: OrderDto;
};

export const PhaseColumn = ({ order }: PhaseColumnProps) => {
    const phase = getPhaseFromOrder(order);
    const { t } = useAppTranslation();

    return (
        <PhaseCell>
            {renderPhaseIcon(phase)}
            <div>{t(phase)}</div>
        </PhaseCell>
    );
};

function getPhaseFromOrder(order: OrderDto): string {
    if (order.pauseReason)
        return `status.orders.reasonsForPause.${convertReasonForPauseToLabel(order.pauseReason)}`;
    if (order.phase == 'noPhase') return 'status.orders.noPhase';

    return order.phase;
}

function renderPhaseIcon(phase: string): JSX.Element {
    if (phase.startsWith('status.orders.reasonsForPause'))
        return <StyledPauseIcon fill={theme.palette.primary.light} size={IconSize.M} />;
    if (phase == 'status.orders.noPhase')
        return <StyledClockIcon fill={theme.palette.primary.light} size={IconSize.M} />;

    return <StyledCarIcon fill={theme.palette.primary.light} size={IconSize.M} />;
}

const PhaseCell = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 4,
});

const StyledPauseIcon = styled(PauseIcon)(({ theme }) => ({
    width: 24,
    height: 24,
    [theme.breakpoints.up('3xl')]: {
        width: 36,
        height: 36,
    },
    [theme.breakpoints.up('4xl')]: {
        width: 40,
        height: 40,
    },
    [theme.breakpoints.up('5xl')]: {
        width: 57,
        height: 57,
    },
}));

const StyledClockIcon = styled(ClockIcon)(({ theme }) => ({
    width: 24,
    height: 24,
    [theme.breakpoints.up('3xl')]: {
        width: 36,
        height: 36,
    },
    [theme.breakpoints.up('4xl')]: {
        width: 40,
        height: 40,
    },
    [theme.breakpoints.up('5xl')]: {
        width: 57,
        height: 57,
    },
}));

const StyledCarIcon = styled(CarIcon)(({ theme }) => ({
    width: 24,
    height: 24,
    [theme.breakpoints.up('3xl')]: {
        width: 36,
        height: 36,
    },
    [theme.breakpoints.up('4xl')]: {
        width: 40,
        height: 40,
    },
    [theme.breakpoints.up('5xl')]: {
        width: 57,
        height: 57,
    },
}));
