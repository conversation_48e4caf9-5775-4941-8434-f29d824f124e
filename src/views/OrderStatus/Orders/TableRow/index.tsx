import { styled, TableCell, TableRow, useTheme } from '@mui/material';
import { OrderDto } from 'api/status';
import moment from 'moment';
import { CSSProperties, useMemo } from 'react';
import { useAppSelector } from 'store';
import { DEFAULT_SHOWN_COLUMNS } from 'store/slices/ordersStatus';
import { selectDisplayableOrderStatusColumns } from 'store/slices/ordersStatus/selectors';
import TowerNumber from 'views/WorkshopPlanner/common/TowerNumber';
import { PhaseColumn } from './PhaseColumn';

export type OrdersStatusTableRowProps = {
    order: OrderDto;
};

export const OrdersStatusTableRow = ({ order }: OrdersStatusTableRowProps) => {
    const theme = useTheme();

    const isOutdated = moment(order.promisedDate).isBefore(moment());
    const columns = useAppSelector(selectDisplayableOrderStatusColumns);
    const font = useMemo(() => {
        switch (columns.length) {
            case 3:
            case 4:
                return theme.typography.h2Inter;
            case 5:
            case 6:
                return theme.typography.h4Inter;
            case 7:
            default:
                return theme.typography.h5Inter;
        }
    }, [columns]);

    const columnsElement = useMemo(
        () =>
            columns.map((x) => (
                <StyledTableCell key={x} sx={{ ...font }} component="td" scope="row">
                    {renderColumn(x, order, font)}
                </StyledTableCell>
            )),
        [columns]
    );

    return isOutdated ? (
        <StyledTableRowOutdated>{columnsElement}</StyledTableRowOutdated>
    ) : (
        <StyledTableRow>{columnsElement}</StyledTableRow>
    );
};

function renderColumn(key: string, order: OrderDto, font: CSSProperties) {
    switch (key) {
        case DEFAULT_SHOWN_COLUMNS.DELIVERY_TIME:
            return moment(order.promisedDate).format('hh:mm A');
        case DEFAULT_SHOWN_COLUMNS.TOWER:
            return (
                <TowerNumber
                    isResponsive
                    towerNumber={order.towerNumber}
                    orderTypeKey={order.orderType?.id}
                    appointmentReasonColor={order.appointmentReasonColor}
                    userIdOrKey={order.inChargeUser?.id}
                    style={{ ...font }}
                />
            );
        case DEFAULT_SHOWN_COLUMNS.ORDER_NUMBER:
            return order.orderNumber;
        case DEFAULT_SHOWN_COLUMNS.CUSTOMER:
            return order.customer;
        case DEFAULT_SHOWN_COLUMNS.MODEL:
            return order.model;
        case DEFAULT_SHOWN_COLUMNS.PLATES:
            return order.plates;
        case DEFAULT_SHOWN_COLUMNS.PHASE:
            return <PhaseColumn order={order} />;
    }
}

const StyledTableRow = styled(TableRow)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
}));

const StyledTableRowOutdated = styled(TableRow)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
    animation: '$blinker 500ms linear alternate infinite',
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    height: 50,
    padding: '0 0 0 30px',
    color: theme.palette.neutral[7],
    fontWeight: 700,

    [theme.breakpoints.up('3xl')]: {
        height: 88,
        fontSize: '18px',
        lineHeight: '21.09px',
    },
    [theme.breakpoints.up('4xl')]: {
        height: 125,
        fontSize: '24px',
        lineHeight: '28.13px',
    },
    [theme.breakpoints.up('5xl')]: {
        height: 185,
        fontSize: '42px',
        lineHeight: '49.22px',
    },
}));
