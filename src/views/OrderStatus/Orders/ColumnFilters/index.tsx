import { I<PERSON><PERSON><PERSON>on, Menu, MenuItem, styled, useTheme } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import ArrowTooltip from 'common/components/Tooltip';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { t } from 'i18next';
import { useEffect, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { DEFAULT_SHOWN_COLUMNS } from 'store/slices/ordersStatus';
import { selectDisplayableOrderStatusColumns } from 'store/slices/ordersStatus/selectors';
import {
    fetchOrdersStatusUiStateThunk,
    toggleDisplayedStatusFieldThunk,
} from 'store/slices/ordersStatus/thunks';
import { DEFAULT_COLUMNS_TRANSLATION_KEYS } from '../../helpers';

const ColumnsMenu = styled(Menu)({
    '& > .MuiPaper-root': {
        borderRadius: 10,
        padding: '0 6px',
        backgroundColor: 'var(--neutral2)',
    },
    '& .MuiMenu-list': {
        minWidth: 200,
        maxWidth: 300,
        maxHeight: 'min(80vh, 360px)',
        ...scrollbarStyle(),
        overflowY: 'auto',
        overflowX: 'hidden',

        '& .MuiMenuItem-root:not(:last-child)': {
            borderBottom: '1px solid var(--neutral3)',
        },
    },
});

export const ColumnFilters = () => {
    const isEnterprise = useIsEnterpriseRoute();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const defaultColumns = Object.values(DEFAULT_SHOWN_COLUMNS);
    const columns = useAppSelector(selectDisplayableOrderStatusColumns);
    const buttonRef = useRef(null);
    const filtersContainerRef = useRef(null);

    const dispatch = useAppDispatch();
    const theme = useTheme();

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.target as HTMLElement);
    };

    useEffect(() => {
        if (!isEnterprise) dispatch(fetchOrdersStatusUiStateThunk());
    }, [dispatch, isEnterprise]);

    if (isEnterprise) return null;

    const toggle = (key: string, shown: boolean) => {
        dispatch(toggleDisplayedStatusFieldThunk({ key, shown }));
    };

    return (
        <>
            <ArrowTooltip
                content={t('status.orders.selectColumnsTooltip')}
                disableHoverListener={!!anchorEl}
                PopperProps={{ container: filtersContainerRef.current }}
            >
                <div ref={filtersContainerRef}>
                    <IconButton ref={buttonRef} size="medium" onClick={handleClick}>
                        <FiltersIcon
                            size={IconSize.L}
                            fill={anchorEl ? theme.palette.primary.light : Colors.GrayBlue}
                        />
                    </IconButton>
                    <ColumnsMenu
                        anchorOrigin={{
                            horizontal: 'right',
                            vertical: 'bottom',
                        }}
                        transformOrigin={{
                            horizontal: 'right',
                            vertical: 'top',
                        }}
                        onClose={() => setAnchorEl(null)}
                        open={!!anchorEl}
                        anchorEl={anchorEl}
                        container={buttonRef.current}
                    >
                        {defaultColumns?.map((x) => {
                            const selected = columns.includes(x);
                            const disabled = columns.length === 3 && selected;
                            return (
                                <MenuItem
                                    disabled={disabled}
                                    key={x}
                                    onClick={() => toggle(x, !selected)}
                                >
                                    {selected ? <CheckBoxIcon /> : <UncheckBoxIcon />}{' '}
                                    {translate(x)}
                                </MenuItem>
                            );
                        })}
                    </ColumnsMenu>
                </div>
            </ArrowTooltip>
        </>
    );
};

function translate(key: string): string {
    if (Object.values(DEFAULT_SHOWN_COLUMNS).includes(key)) {
        return DEFAULT_COLUMNS_TRANSLATION_KEYS[key]
            ? t(DEFAULT_COLUMNS_TRANSLATION_KEYS[key])
            : key;
    }
    return key;
}
