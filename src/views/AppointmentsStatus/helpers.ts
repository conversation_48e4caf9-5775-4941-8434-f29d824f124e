import useForceRender from 'common/hooks/useForceRender';
import moment from 'moment';
import { useEffect, useRef } from 'react';
import { DEFAULT_SHOWN_COLUMNS } from 'store/slices/appointmentsStatus';

export function useIsCurrentTimeBetween(fromInMinutes: number, toInMinutes: number) {
    const isBetween = useRef(false);
    const fr = useForceRender();

    useEffect(() => {
        const update = () => {
            const now = moment();
            const midnight = now.clone().hour(0).minute(0).second(0).milliseconds(0);
            const start = midnight.clone().add(fromInMinutes, 'minute');
            const end = now.clone().add(toInMinutes, 'minute');
            const isBetweenValue = now.isBetween(start, end);
            if (isBetweenValue !== isBetween.current) {
                isBetween.current = isBetweenValue;
                fr();
            }
        };
        update();
        const interval = setInterval(update, 60000);
        return () => clearInterval(interval);
    }, [fr, fromInMinutes, toInMinutes]);

    return isBetween.current;
}

function getCurrentDate() {
    return moment().format('YYYY-MM-DD');
}

export function useCurrentDate() {
    const fr = useForceRender();
    const date = useRef<string | undefined>(undefined);
    if (!date.current) date.current = getCurrentDate();

    // very inefficient but whatever
    useEffect(() => {
        const interval = setInterval(() => {
            const newDate = getCurrentDate();

            if (date.current != newDate) {
                date.current = newDate;
                fr();
            }
        }, 60000);
        return () => clearInterval(interval);
    }, [fr]);

    return date.current;
}

export const DEFAULT_COLUMNS_TRANSLATION_KEYS: Record<string, string> = {
    [DEFAULT_SHOWN_COLUMNS.APPOINTMENT_TIME]: 'status.appointments.headers.appointmentTime',
    [DEFAULT_SHOWN_COLUMNS.SERVICE_ADVISOR]: 'status.appointments.headers.serviceAdvisor',
    [DEFAULT_SHOWN_COLUMNS.CUSTOMER]: 'status.appointments.headers.customer',
    [DEFAULT_SHOWN_COLUMNS.MODEL]: 'status.appointments.headers.model',
    [DEFAULT_SHOWN_COLUMNS.PLATES]: 'status.appointments.headers.plates',
    [DEFAULT_SHOWN_COLUMNS.STATUS]: 'status.appointments.headers.status',
};
