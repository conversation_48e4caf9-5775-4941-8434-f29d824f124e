import { styled, TableCell, TableRow, useTheme } from '@mui/material';
import { AppointmentStatus } from 'api/status';
import { DelayIcon } from 'common/components/Icons/DelayIcon';
import { InspectionIcon } from 'common/components/Icons/InspectionIcon';
import { OneTimeIcon } from 'common/components/Icons/OneTimeIcon';
import { XMarkIcon } from 'common/components/Icons/XMarkIcon';
import { stringToTitleCase } from 'common/FormatersHelper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import moment from 'moment';
import { useMemo } from 'react';
import { useAppSelector } from 'store';
import { DEFAULT_SHOWN_COLUMNS, SERVICE_ADVISOR_TYPES } from 'store/slices/appointmentsStatus';
import {
    selectDisplayableAppointmentsStatusColumns,
    selectServiceAdvisorType,
} from 'store/slices/appointmentsStatus/selectors';

export type AppointmentData = {
    date: string;
    customer: string;
    model: string;
    serviceAdvisorFullName: string;
    serviceAdvisorInitials: string;
    plates: string;
    orderNumber?: string;
    key: string;
    status: AppointmentStatus;
};

export type AppointmentsStatusTableRowProps = {
    appointment: AppointmentData;
};

export const AppointmentsStatusTableRow = ({ appointment }: AppointmentsStatusTableRowProps) => {
    const { t } = useAppTranslation();
    const theme = useTheme();

    const isOutdated =
        moment(appointment.date).isBefore(moment()) && !Boolean(appointment.orderNumber);

    const isDidNotArrive = appointment.status === 'CustomerDidNotArrive';

    const status = getStatusFromAppointment(appointment);
    const columns = useAppSelector(selectDisplayableAppointmentsStatusColumns);
    const selectedServiceAdvisorType = useAppSelector(selectServiceAdvisorType);

    const font = useMemo(() => {
        switch (columns.length) {
            case 3:
            case 4:
                return theme.typography.h2Inter;
            case 5:
                return theme.typography.h4Inter;
            case 6:
            default:
                return theme.typography.h5Inter;
        }
    }, [columns]);

    function renderColumn(key: string) {
        switch (key) {
            case DEFAULT_SHOWN_COLUMNS.APPOINTMENT_TIME:
                return moment(appointment.date).format('hh:mm A');
            case DEFAULT_SHOWN_COLUMNS.SERVICE_ADVISOR:
                return selectedServiceAdvisorType === SERVICE_ADVISOR_TYPES.FULLNAME
                    ? stringToTitleCase(appointment.serviceAdvisorFullName)
                    : appointment.serviceAdvisorInitials;
            case DEFAULT_SHOWN_COLUMNS.CUSTOMER:
                return stringToTitleCase(appointment.customer);
            case DEFAULT_SHOWN_COLUMNS.MODEL:
                return stringToTitleCase(appointment.model);
            case DEFAULT_SHOWN_COLUMNS.PLATES:
                return appointment.plates;
            case DEFAULT_SHOWN_COLUMNS.STATUS:
                return (
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                        {renderStatusIcon(status)}
                        <div>{`${t(status)}${
                            appointment.orderNumber ? cropOrderNumber(appointment.orderNumber) : ''
                        }`}</div>
                    </div>
                );
        }
    }

    const columnsElement = useMemo(
        () =>
            columns.map((x) => (
                <StyledTableCell key={x} sx={{ ...font }} component="td" scope="row">
                    {renderColumn(x)}
                </StyledTableCell>
            )),
        [columns]
    );

    return isOutdated || isDidNotArrive ? (
        <StyledTableRowBlinking>{columnsElement}</StyledTableRowBlinking>
    ) : (
        <StyledTableRow>{columnsElement}</StyledTableRow>
    );
};

function getStatusFromAppointment(appointment: AppointmentData) {
    if (appointment.orderNumber) return 'status.appointments.status.order#';

    if (appointment.status === 'OrderCreated') return 'status.appointments.status.orderCreated';

    if (appointment.status === 'CustomerDidNotArrive')
        return 'status.appointments.status.customerDidNotArrive';
    if (appointment.status === 'Confirmed') return 'status.appointments.status.confirmed';

    return 'status.appointments.status.unconfirmed';
}

function renderStatusIcon(status: string) {
    if (status == 'status.appointments.status.order#')
        return <InspectionIcon fill={Colors.GrayBlue} size={IconSize.M} />;
    if (status == 'status.appointments.status.orderCreated')
        return <InspectionIcon fill={Colors.GrayBlue} size={IconSize.M} />;
    if (status == 'status.appointments.status.confirmed')
        return <OneTimeIcon fill={Colors.GrayBlue} size={IconSize.M} />;
    if (status == 'status.appointments.status.unconfirmed')
        return <DelayIcon fill={Colors.GrayBlue} size={IconSize.M} />;
    if (status == 'status.appointments.status.customerDidNotArrive')
        return <XMarkIcon fill={Colors.GrayBlue} size={IconSize.M} />;

    return <XMarkIcon fill={Colors.GrayBlue} size={IconSize.M} />;
}

function cropOrderNumber(orderNumber: string) {
    return orderNumber.length > 10 ? `...${orderNumber.slice(-10)}` : orderNumber;
}

const StyledTableRow = styled(TableRow)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
}));

const StyledTableRowBlinking = styled(TableRow)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
    animation: 'blinker 500ms linear alternate infinite',
    '@keyframes blinker': {
        '0%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '40%': {
            backgroundColor: theme.palette.neutral[1],
        },
        '100%': {
            backgroundColor: Colors.Error_background,
        },
    },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
    height: 50,
    padding: '0 0 0 30px',
    color: theme.palette.neutral[7],
    fontWeight: 700,

    [theme.breakpoints.up('3xl')]: {
        height: 88,
        fontSize: '18px',
        lineHeight: '21.09px',
    },
    [theme.breakpoints.up('4xl')]: {
        height: 125,
        fontSize: '24px',
        lineHeight: '28.13px',
    },
    [theme.breakpoints.up('5xl')]: {
        height: 185,
        fontSize: '42px',
        lineHeight: '49.22px',
    },
}));
