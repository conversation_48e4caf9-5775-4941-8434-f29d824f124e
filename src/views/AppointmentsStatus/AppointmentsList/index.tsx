import { Grid, LinearProgress, Table, TableBody, styled } from '@mui/material';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useCallback, useState } from 'react';
import { FullScreen, useFullScreenHandle } from 'react-full-screen';
import StatusHeaderBar from '../StatusHeaderBar';
import TableHeaders from '../TableHeaders';
import { AppointmentData, AppointmentsStatusTableRow } from '../TableRow';

export type AppointmentListProps = {
    appointments: AppointmentData[];
    showLoader?: boolean;
    onFullscreen?: (fullscreen: boolean) => void;
};

const ProgressContainer = styled('div')({
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
});

export default function AppointmentList({
    onFullscreen,
    appointments,
    showLoader,
}: AppointmentListProps) {
    const [isFullScreen, setIsFullScreen] = useState(false);
    const fullScreenHandle = useFullScreenHandle();
    const handleFullScreenChange = useCallback(
        (f: boolean) => {
            setIsFullScreen(f);
            if (onFullscreen) onFullscreen(f);
        },
        [onFullscreen]
    );

    return (
        // @ts-ignore CMOS-1993 remove and fix this
        <StyledFullScreen handle={fullScreenHandle}>
            <ProgressContainer style={{ opacity: showLoader ? 1 : 0 }}>
                <LinearProgress variant="indeterminate" />
            </ProgressContainer>

            <Grid container justifyContent="center" style={{ marginTop: 10 }}>
                <Grid item xs={11}>
                    <StatusHeaderBar
                        fullScreenHandle={fullScreenHandle}
                        isFullScreen={isFullScreen}
                        handleFullScreenChange={handleFullScreenChange}
                    />
                </Grid>
                <Grid item xs={11}>
                    <StyledTableContainer>
                        <Table stickyHeader aria-label="sticky table">
                            <TableHeaders />
                            <TableBody>
                                {appointments.map((appointment) => (
                                    <AppointmentsStatusTableRow
                                        appointment={appointment}
                                        key={'row_' + appointment.key}
                                    />
                                ))}
                            </TableBody>
                        </Table>
                    </StyledTableContainer>
                </Grid>
            </Grid>
        </StyledFullScreen>
    );
}

const StyledFullScreen = styled(FullScreen)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[2],
}));

const StyledTableContainer = styled('div')(({ theme }) => ({
    width: '100%',
    //Header + 10 rows
    maxHeight: 562,

    [theme.breakpoints.up('3xl')]: {
        maxHeight: 704,
    },
    [theme.breakpoints.up('4xl')]: {
        maxHeight: 1000,
    },
    [theme.breakpoints.up('5xl')]: {
        maxHeight: 1480,
    },
    overflowY: 'auto',
    border: '1px solid #dbdcdd',
    borderRadius: 12,
    ...scrollbarStyle(),
}));
