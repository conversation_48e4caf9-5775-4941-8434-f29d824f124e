import { Icon<PERSON>utton, Menu, MenuItem, styled, useTheme } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { RadioButton } from 'common/components/Inputs';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { DEFAULT_SHOWN_COLUMNS, SERVICE_ADVISOR_TYPES } from 'store/slices/appointmentsStatus';
import {
    selectDisplayableAppointmentsStatusColumns,
    selectServiceAdvisorType,
} from 'store/slices/appointmentsStatus/selectors';
import {
    fetchAppointmentsStatusUiStateThunk,
    toggleDisplayedStatusFieldThunk,
    toggleServiceAdvisorTypeThunk,
} from 'store/slices/appointmentsStatus/thunks';
import { DEFAULT_COLUMNS_TRANSLATION_KEYS } from '../helpers';

const ColumnsMenu = styled(Menu)({
    '& > .MuiPaper-root': {
        borderRadius: 10,
        padding: '0 6px',
        backgroundColor: 'var(--neutral2)',
    },
    '& .MuiMenu-list': {
        minWidth: 215,
        maxWidth: 300,
        maxHeight: 'min(80vh, 400px)',
        ...scrollbarStyle(),
        overflowY: 'auto',
        overflowX: 'hidden',

        '& .MuiMenuItem-root': {
            paddingLeft: '8px',
        },

        '& .MuiMenuItem-root:not(:last-child)': {
            borderBottom: '1px solid var(--neutral3)',
        },
    },
});

const ColumnsLabel = styled('div')(({ theme }) => ({
    padding: '10px 0 10px 12px',
    textAlign: 'left',
    color: theme.palette.grey[700],
    ...theme.typography.h5Inter,
}));

export const ColumnFilters = () => {
    const isEnterprise = useIsEnterpriseRoute();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const defaultColumns = Object.values(DEFAULT_SHOWN_COLUMNS);
    const columns = useAppSelector(selectDisplayableAppointmentsStatusColumns);
    const serviceAdvisorTypes = Object.values(SERVICE_ADVISOR_TYPES);
    const selectedServiceAdvisorType = useAppSelector(selectServiceAdvisorType);
    const buttonRef = useRef(null);
    const filtersContainerRef = useRef(null);

    const dispatch = useAppDispatch();
    const theme = useTheme();
    const { t } = useAppTranslation();

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.target as HTMLElement);
    };

    useEffect(() => {
        if (!isEnterprise) dispatch(fetchAppointmentsStatusUiStateThunk());
    }, [dispatch, isEnterprise]);

    if (isEnterprise) return null;

    const toggleColumns = (key: string, shown: boolean) => {
        dispatch(toggleDisplayedStatusFieldThunk({ key, shown }));
    };

    const toggleServiceAdvisorTypes = (key: string) => {
        dispatch(toggleServiceAdvisorTypeThunk({ key }));
    };

    function translateColumns(key: string): string {
        if (Object.values(DEFAULT_SHOWN_COLUMNS).includes(key)) {
            return DEFAULT_COLUMNS_TRANSLATION_KEYS[key]
                ? t(DEFAULT_COLUMNS_TRANSLATION_KEYS[key])
                : key;
        }
        return key;
    }

    function translateServiceAdvisorTypes(key: string): string {
        switch (key) {
            case SERVICE_ADVISOR_TYPES.INITIALS:
                return t('status.appointments.headers.initials');
            case SERVICE_ADVISOR_TYPES.FULLNAME:
            default:
                return t('status.appointments.headers.fullName');
        }
    }

    return (
        <>
            <ArrowTooltip
                content={t('status.appointments.selectColumnsTooltip')}
                disableHoverListener={!!anchorEl}
                PopperProps={{ container: filtersContainerRef.current }}
            >
                <div ref={filtersContainerRef}>
                    <IconButton ref={buttonRef} size="medium" onClick={handleClick}>
                        <FiltersIcon
                            size={IconSize.L}
                            fill={anchorEl ? theme.palette.primary.light : Colors.GrayBlue}
                        />
                    </IconButton>
                    <ColumnsMenu
                        anchorOrigin={{
                            horizontal: 'right',
                            vertical: 'bottom',
                        }}
                        transformOrigin={{
                            horizontal: 'right',
                            vertical: 'top',
                        }}
                        onClose={() => setAnchorEl(null)}
                        open={!!anchorEl}
                        anchorEl={anchorEl}
                        container={buttonRef.current}
                    >
                        <>
                            <ColumnsLabel>{t('status.appointments.columns')}</ColumnsLabel>
                            {defaultColumns?.map((x) => {
                                const selected = columns.includes(x);
                                const disabled = columns.length === 3 && selected;
                                return (
                                    <MenuItem
                                        disabled={disabled}
                                        key={x}
                                        onClick={() => toggleColumns(x, !selected)}
                                    >
                                        {selected ? <CheckBoxIcon /> : <UncheckBoxIcon />}{' '}
                                        {translateColumns(x)}
                                    </MenuItem>
                                );
                            })}
                            <ColumnsLabel>{t('status.appointments.showAdvisorName')}</ColumnsLabel>
                            {serviceAdvisorTypes.map((x) => {
                                const checked = x === selectedServiceAdvisorType;
                                const disabled = !columns.includes(
                                    DEFAULT_SHOWN_COLUMNS.SERVICE_ADVISOR
                                );
                                return (
                                    <MenuItem
                                        disabled={disabled}
                                        key={x}
                                        onClick={() => toggleServiceAdvisorTypes(x)}
                                    >
                                        <RadioButton checked={checked} />
                                        {translateServiceAdvisorTypes(x)}
                                    </MenuItem>
                                );
                            })}
                        </>
                    </ColumnsMenu>
                </div>
            </ArrowTooltip>
        </>
    );
};
