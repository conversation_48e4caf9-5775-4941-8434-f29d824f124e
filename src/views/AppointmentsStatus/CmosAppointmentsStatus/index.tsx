import { useQuery } from '@tanstack/react-query';
import AppointmentSettingsAPI from 'api/AppointmentSettings';
import StatusApi, { AppointmentDto } from 'api/status';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';

import { appointmentsActions } from 'store/slices/appointmentsStatus';
import {
    selectActiveAppointments,
    selectAppointments,
} from 'store/slices/appointmentsStatus/selectors';
import AppointmentList from '../AppointmentsList';
import { AppointmentData } from '../TableRow';

export const CmosAppointmentsStatus = () => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dispatch = useDispatch();

    const [isFullScreen, setIsFullScreen] = useState(false);
    const [openingTime, setOpeningTime] = useState('08:00');
    const [closingTime, setClosingTime] = useState('19:00');

    const appointments = useAppSelector(selectAppointments);
    const activeAppointments = useAppSelector(selectActiveAppointments);

    const currentDate = moment().second(0).millisecond(0);
    const enabledQueryStartTime = useMemo(
        () =>
            currentDate.clone().hour(+openingTime.split(':')[0]).minute(+openingTime.split(':')[1]),
        [currentDate, openingTime]
    );

    const queryFromTime = useCallback(
        () => moment().second(0).millisecond(0).add(moment.duration(-2, 'hours')),
        []
    );

    const enabledQueryEndTime = useMemo(
        () =>
            currentDate.clone().hour(+closingTime.split(':')[0]).minute(+closingTime.split(':')[1]),
        [currentDate, closingTime]
    );
    const statusScreenEnabled = moment().isBetween(enabledQueryStartTime, enabledQueryEndTime);

    const setNextActiveAppointments = useCallback(
        (appointments: AppointmentDto[]) => {
            dispatch(
                appointmentsActions.setNextActiveAppointments(
                    appointments.filter(
                        (a) => moment(a.date) < moment().add(moment.duration(1, 'hours'))
                    )
                )
            );
        },
        [dispatch]
    );

    // Automatically turns pages at a specified interval
    useEffect(() => {
        if (statusScreenEnabled && isFullScreen) {
            setNextActiveAppointments(appointments);

            const interval = setInterval(() => {
                setNextActiveAppointments(appointments);
            }, 10000);
            return () => clearInterval(interval);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [statusScreenEnabled, appointments, isFullScreen]);

    useQuery(['appointment', 'settings'], () => AppointmentSettingsAPI.getAppointmentSettings(), {
        onSuccess: (response) => {
            const dayConfig = response.workingDays.find(
                (day) => day.dayNumber === moment(new Date()).weekday()
            );
            setOpeningTime(dayConfig!.opening);
            setClosingTime(dayConfig!.closing);
        },
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), t('toasters.errorOccurred'));
        },
        cacheTime: Infinity,
        staleTime: 120000,
    });

    const { data, isInitialLoading } = useQuery(
        ['status', 'appointments', `${queryFromTime()}`, `${enabledQueryEndTime}`],
        () => StatusApi.getAppointments(queryFromTime(), enabledQueryEndTime),
        {
            refetchInterval: 60000, // 1 minute,
            onSuccess: (response) => {
                if (isFullScreen) {
                    dispatch(appointmentsActions.setAppointments(response));
                } else {
                    dispatch(appointmentsActions.setAllAppointments(response));
                }
            },
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            // enabled: statusScreenEnabled,
            keepPreviousData: true,
        }
    );
    const showLoader = isInitialLoading || data === undefined;

    useEffect(() => {
        if (!data) return;

        if (isFullScreen) {
            dispatch(appointmentsActions.setAppointments(data));
        } else {
            dispatch(appointmentsActions.setAllAppointments(data));
        }
    }, [data, isFullScreen]);

    const appointmentsData: AppointmentData[] = useMemo(
        () =>
            ((activeAppointments as AppointmentDto[]) ?? []).map((a) => ({
                date: a.date,
                customer: a.customer,
                model: a.model,
                serviceAdvisorFullName: a.serviceAdvisorFullName,
                serviceAdvisorInitials: a.serviceAdvisorInitials,
                plates: a.plates,
                orderNumber: a.orderNumber,
                key: a.appointmentId,
                status: a.status,
            })),
        [activeAppointments]
    );

    return (
        <AppointmentList
            onFullscreen={setIsFullScreen}
            showLoader={showLoader}
            appointments={appointmentsData}
        />
    );
};
