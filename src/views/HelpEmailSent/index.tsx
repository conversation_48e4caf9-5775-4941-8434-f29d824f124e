import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { withFooter } from '../../common/hocs/withFooter';
import { Colors } from '../../common/styles/Colors';
import { FontPrimary } from '../../common/styles/FontHelper';
import { HeaderStyles } from '../../common/styles/HeaderStyles';
import styles from './styles.module.css';

const HelpEmailSent = () => {
    const { t } = useAppTranslation();

    return (
        <div className={styles.mainContainer}>
            <div className={styles.image} />
            <span
                className={styles.great}
                style={{
                    ...FontPrimary(HeaderStyles.H1_34px, true, Colors.CM1),
                }}
            >
                {t('helpEmailSent.great')}
            </span>
            <span
                className={styles.info}
                style={{
                    ...FontPrimary(HeaderStyles.H3_21px, false, Colors.Neutral7),
                }}
            >
                {t('helpEmailSent.notified')}
            </span>
        </div>
    );
};

export default withFooter(HelpEmailSent);
