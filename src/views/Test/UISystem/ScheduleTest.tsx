import moment from 'moment';
import { CSSProperties } from 'react';

const NAMES = Array.from({ length: 100 }).map((_, v) => `User #${v + 1}`);

type Item = {
    name: string;
    color: string;
    key: string;
    start: string;
    durationMinutes: number;
};

const items: Item[] = [];
const dts: [number, number][] = [
    [9, 60],
    [10, 60],
    [11, 120],
    [14, 180],
];
const groups = [1, 2, 3, 4, 5, 6, 7];

for (const name of NAMES) {
    for (const [hr, dur] of dts) {
        items.push({
            key: Math.random().toString(),
            name,
            color: `rgb(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255})`,
            start: moment().hour(hr).minute(0).second(0).toISOString(),
            durationMinutes: dur,
        });
    }
}

export default function ScheduleTest() {
    return (
        <div style={{ margin: '40px 0' }}>
            <h2>Scheduler test</h2>
            <div
                style={
                    {
                        height: '100px',
                        margin: '50px',
                    } as CSSProperties
                }
            />
        </div>
    );
}
