import { Grid } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { CircleIcon } from '../../../common/components/Icons/CircleIcon';
import { UserIcon } from '../../../common/components/Icons/UserIcon';
import { SelecteOption } from '../../../common/components/Inputs/Dropdown/DataOption';
import { InputValidation } from '../../../common/components/Inputs/InputValidation';
import { useDropDownValue } from '../../../common/hooks/useDropDownValue';
import { Colors } from '../../../common/styles/Colors';
import { useStyles } from './styles';

const DropDownTest = ({ setThemeInput, themeInput }: any) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [inputValidation, setInputValidation] = useState(InputValidation.default);
    /*const optionsGropups = [
        {
            label: "Group 1",
            options: [
                { label: "Group 1, option 1", value: "value_1" },
                { label: "Group 1, option 2", value: "value_2" }
            ]
        },
        { label: "A root option", value: "value_3" },
        { label: "Another root option", value: "value_4" }
    ];

    const optionsDividers = [
        { label: t("communication.sendMessage"), value: "sms", desc: "Enviar SMS con nota", index: 0 },
        { label: t("communication.sendWaSmsEmail"), value: "smsEmailWa", desc: "Enviar SMS con nota", index: 7 },
        { isDivider: true },
        { isTitle: true, title: "WhatsApp" },
        { label: t("communication.sendWa"), value: "sms", desc: "Enviar SMS con nota", index: 1 },
        { isDivider: true },
        { isTitle: true, title: "SMS" },
        { label: t("communication.sendSms"), value: "email", desc: "Enviar SMS con nota", index: 2, modalTitle: "communication.sendSms" },
        { label: t("communication.sendSmsWithNote"), value: "wa", desc: "Enviar SMS con nota", index: 3, modalTitle: "communication.sendSmsWithNote" },
        { isDivider: true },
        { isTitle: true, title: "E-mail" },
        { label: t("communication.sendEmail"), value: "smsEmail", desc: "Enviar SMS con nota", index: 4, modalTitle: "communication.sendEmail" },
        { label: t("communication.sendEmailWithNote"), value: "smsWa", desc: "Enviar SMS con nota", index: 5, modalTitle: "communication.sendEmailWithNote" },
    ];*/

    const optionsList: SelecteOption[] = [
        {
            label: t('communication.sendMessage'),
            value: 'sms',
            icon: CircleIcon,
            color: Colors.Error,
        },
        {
            label: t('communication.sendWaSmsEmail'),
            value: 'smsEmailWa',
            icon: CircleIcon,
            color: 'green',
        },
        {
            label: t('communication.sendWa'),
            value: 'smswa',
            icon: CircleIcon,
            color: Colors.CM1,
        },
        {
            label: t('communication.sendSms'),
            value: 'email',
            icon: CircleIcon,
            color: 'orange',
        },
        {
            label: t('communication.sendSmsWithNote'),
            value: 'wa',
            icon: CircleIcon,
            color: Colors.Error,
        },
        {
            label: t('communication.sendEmail'),
            value: 'smsEmail',
            icon: CircleIcon,
            color: 'yellow',
        },
        {
            label: t('communication.sendEmailWithNote'),
            value: 'smsWa',
            icon: CircleIcon,
            color: Colors.Error,
        },
        { label: 'Option A', value: 'a', icon: CircleIcon, color: 'blue' },
        { label: 'Option B', value: 'b', icon: CircleIcon, color: 'orange' },
        { label: 'Option C', value: 'c', icon: CircleIcon, color: 'brown' },
        { label: 'Option D', value: 'd', icon: UserIcon, color: Colors.Error },
        { label: 'Option E', value: 'e', icon: UserIcon, color: Colors.Error },
    ];
    const [options, setOptions] = useState([...optionsList]);
    const [vDrop1, setvDrop1] = useDropDownValue([]);
    const [vDrop2, setvDrop2] = useDropDownValue([]);
    const [vDrop3, setvDrop3] = useDropDownValue([]);
    const [vDrop4, setvDrop4] = useDropDownValue([]);
    const [vDropMulti, setvDropMulti] = useDropDownValue([]);
    const [vDropIcon, setvDropIcon] = useDropDownValue([]);
    const [enabled, setEnabled] = useState('on');
    return (
        <Grid container alignContent={'center'} style={{ paddingTop: '100px' }}>
            {/* <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>DropDowns</span>
            </Grid>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <select
                    onChange={(event: any) => setThemeInput(event.target.value)}
                    value={themeInput}
                >
                    <option value={InputTheme.rectangle}>Regtangular Fondo Blanco</option>
                    <option value={InputTheme.rectangle_grey}>Regtangular Fondo Gris</option>
                    <option value={InputTheme.rounded}>Redondo</option>
                </select>
            </Grid>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <select
                    onChange={(event: any) => setInputValidation(event.target.value)}
                    value={inputValidation}
                >
                    <option value={InputValidation.default}>default</option>
                    <option value={InputValidation.error}>error</option>
                    <option value={InputValidation.success}>success</option>
                </select>
            </Grid>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <select onChange={(event: any) => setEnabled(event.target.value)} value={enabled}>
                    <option value={'on'}>Enabled</option>
                    <option value={'off'}>Disabled</option>
                </select>
            </Grid>
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={3} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>Default</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>Required</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>Optional</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>multiple</span>
                </Grid>
                <Grid item xs={1}></Grid>
                <Grid item xs={2} className={styles.inputContainer}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        options={options}
                        
                        label={'Dropdown text'}
                        disabled={enabled == 'off'}
                        value={vDrop1}
                        isRequired={true}
                        onChange={setvDrop1}
                        isInvalid={!(inputValidation == InputValidation.success)}
                    />
                </Grid>
                <Grid item xs={1}></Grid>
                <Grid item xs={2} className={styles.inputContainer}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        options={options}
                        label={'Dropdown Radio'}
                        optionStyle={OptionStyle.radio}
                        disabled={enabled == 'off'}
                        isRequired={true}
                        showValidationIndicators={true}
                        value={vDrop2}
                        onChange={setvDrop2}
                        isInvalid={!(inputValidation == InputValidation.success)}
                    />
                </Grid>
                <Grid item xs={1}></Grid>
                <Grid item xs={2} className={styles.inputContainer}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        options={options}
                        label={'Dropdown checkbox'}
                        optionStyle={OptionStyle.checkbox}
                        disabled={enabled == 'off'}
                        isRequired={false}
                        showValidationIndicators={true}
                        value={vDrop3}
                        onChange={setvDrop3}
                        isInvalid={!(inputValidation == InputValidation.success)}
                    />
                </Grid>

                <Grid item xs={2} className={styles.inputContainer}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        options={options}
                        label={'Dropdown checkbox'}
                        optionStyle={OptionStyle.radio}
                        disabled={enabled == 'off'}
                        isRequired={false}
                        showValidationIndicators={true}
                        value={vDrop4}
                        onChange={setvDrop4}
                        multiple={true}
                        isInvalid={!(inputValidation == InputValidation.success)}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}></Grid>
                <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>Open Menu</span>
                </Grid>
                <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>
                        Icon Color Selector
                    </span>
                </Grid>
                <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
                    <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>
                        Custom Dropdown
                    </span>
                </Grid>
                <Grid
                    item
                    xs={3}
                    className={styles.inputContainer}
                    style={{ textAlign: 'center' }}
                ></Grid>

                <Grid item xs={3} className={styles.inputContainer}></Grid>
                <Grid item xs={2} className={styles.inputContainer} style={{ marginBottom: 200 }}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        options={options}
                        label={'Open Menu Selector'}
                        optionStyle={OptionStyle.radio}
                        disabled={enabled == 'off'}
                        isRequired={false}
                        showValidationIndicators={true}
                        value={vDropMulti}
                        onChange={setvDropMulti}
                        menuIsOpen={true}
                        isInvalid={!(inputValidation == InputValidation.success)}
                    />
                </Grid>
                <Grid item xs={2} className={styles.inputContainer} style={{ marginBottom: 200 }}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        options={options}
                        label={'Selector Icon'}
                        optionStyle={OptionStyle.icons}
                        disabled={enabled == 'off'}
                        isRequired={false}
                        showValidationIndicators={true}
                        value={vDropIcon}
                        onChange={setvDropIcon}
                        isInvalid={!(inputValidation == InputValidation.success)}
                    />
                </Grid>
                <Grid item xs={2} className={styles.inputContainer}>
                    <Dropdown
                        name={'campo1'}
                        theme={themeInput}
                        label={'Selector Icon'}
                        disabled={enabled == 'off'}
                        isRequired={false}
                        showValidationIndicators={true}
                        isInvalid={!(inputValidation == InputValidation.success)}
                        CustomMenu={(props: any) => {
                            return (
                                <>
                                    <h4
                                        style={{
                                            width: '100%',
                                            textAlign: 'center',
                                        }}
                                    >
                                        Example custom Menu component
                                    </h4>
                                    <ul>
                                        <li>asdasd</li>
                                        <li>asdasd</li>
                                        <li>asdasd</li>
                                        <li>asdasd</li>
                                        <li>asdasd</li>
                                    </ul>
                                </>
                            );
                        }}
                    ></Dropdown>
                </Grid>
                <Grid item xs={2} className={styles.inputContainer}>
                    <DropdownMulti
                        name={'campo1'}
                        theme={themeInput}
                        label={'Selector Icon'}
                        disabled={enabled == 'off'}
                        isRequired={false}
                        showValidationIndicators={true}
                        isInvalid={!(inputValidation == InputValidation.success)}
                        options={options}
                        onCreate={(option) => {
                            console.log('create-option', option);
                            setOptions([...options, option]);
                        }}
                    />
                </Grid>
            </Grid> */}
        </Grid>
    );
};
export default DropDownTest;
