import AppointmentScheduler from 'common/components/AppointmentsScheduler';
import moment from 'moment';
import { useState } from 'react';
import { useInputValue } from '../../../common/hooks/useInputValue';
import { InputTheme } from '../../../common/styles/InputTheme';
import HeaderBar from '../../../views/HeaderBar';
import ButtonTest from './ButtonTest';
import DropDownTest from './DropDownTest';
import { IconsTest } from './IconsTest';
import { LinkTest } from './LinkTest';
import NotificationTest from './NotificationTest';
import { NumberFieldTest } from './NumberFieldTest';
import PickerTest from './PickerTest';
import RichTextEditorTest from './RichTextEditorTest';
import ScheduleTest from './ScheduleTest';
import { TextFieldLoaderTest } from './TextFieldLoaderTest';
import { TextFieldTest } from './TextFieldTest';

const UISystem = () => {
    const [themeInput, setThemeInput] = useInputValue(InputTheme.rectangle);

    const arr = Array.from({ length: 20 }).map((_, i) => i + '');
    const nowRounded = +moment().millisecond(0).second(0).minute(0).toDate();
    const [error, setError] = useState(false);

    if (error) {
        throw new Error('test error');
    }

    return (
        <div className={`fullSize`}>
            <HeaderBar />

            <button
                style={{ marginTop: 100 }}
                onClick={(e) => {
                    setError(true);
                }}
            >
                Cause an error (test for error boundary component)
            </button>

            <div style={{ padding: 10, height: '70vh' }}>
                <AppointmentScheduler<string, string>
                    range={[0, 24 * 60]}
                    stopsInterval={30}
                    sizes={{
                        columnWidth: 160,
                        headerHeight: 50,
                        timelineWidth: 120,
                        pixelsPerMinute: 1.6,
                    }}
                    groups={arr}
                    items={arr}
                    showTimezone={false}
                    components={{
                        HeaderCellComponent: ({ value }) => <>Header {value}</>,
                        ItemComponent: ({ value }) => <>Item {value}</>,
                        StopComponent: ({ ts }) => <span>{moment(ts).format('hh:mm')}</span>,
                        ContentGridColumnComponent: () => <span>Column</span>,
                    }}
                    getGroupKey={(g) => g}
                    getItemGroupKey={(i) => i}
                    getItemKey={(i) => i}
                    getItemData={(i) => ({
                        ts: nowRounded,
                        sizeInMinutes: 10 * +i,
                    })}
                />
            </div>
            <TextFieldTest />
            <TextFieldLoaderTest />
            <NumberFieldTest />
            <DropDownTest setThemeInput={setThemeInput} themeInput={themeInput} />
            {/* <DatePickerTest setThemeInput={setThemeInput} themeInput={themeInput} /> */}
            <PickerTest />
            <ButtonTest />
            <NotificationTest />
            <LinkTest />
            <RichTextEditorTest />
            <IconsTest />
            <ScheduleTest />
        </div>
    );
};
export default UISystem;
