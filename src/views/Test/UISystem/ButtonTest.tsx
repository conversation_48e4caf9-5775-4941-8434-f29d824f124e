import { Grid } from '@mui/material';
import { useState } from 'react';
import { Button } from '../../../common/components/Button';
import { ButtonColors } from '../../../common/components/Button/button.styles';
import { CopyIcon } from '../../../common/components/Icons/CopyIcon';
import { PlusIcon } from '../../../common/components/Icons/PlusIcon';
import { UserIcon } from '../../../common/components/Icons/UserIcon';
import { Colors } from '../../../common/styles/Colors';
import { FontPrimary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';
import { useStyles } from './styles';

const ButtonTest = () => {
    const styles = useStyles();
    const [size, setSize] = useState<'large' | 'medium' | 'small'>('medium');
    const [color, setColor] = useState<ButtonColors>(Colors.CM1);
    return (
        <Grid container alignContent={'center'} style={{ paddingTop: '100px' }}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>Button Styles</span>
            </Grid>

            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <select onChange={(event: any) => setSize(event.target.value)} value={size}>
                    <option value={'large'}>Large</option>
                    <option value={'medium'}>Medium</option>
                    <option value={'small'}>Small</option>
                </select>
            </Grid>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <select onChange={(event: any) => setColor(event.target.value)} value={color}>
                    <option value={Colors.CM1}>Primary - CM1</option>
                    <option value={Colors.Neutral3}>Gray - Neutral 3</option>
                    <option value={Colors.Error}>Error - Occasionals red</option>
                    <option value={Colors.Success}>Success - Occasionals green</option>
                </select>
            </Grid>
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={3} className={styles.inputContainer} />
                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>Typography</span>
                </Grid>

                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>Stroke</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>Filled</span>
                </Grid>
            </Grid>
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>No Icon</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={size}
                        color={color}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>

                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'stroke'}
                        cmosSize={size}
                        color={color}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={size}
                        color={color}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
            </Grid>
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>Icon Left</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={size}
                        color={color}
                        Icon={CopyIcon}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'stroke'}
                        cmosSize={size}
                        color={color}
                        Icon={UserIcon}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={size}
                        color={color}
                        Icon={PlusIcon}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
            </Grid>
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>Icon Right</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={size}
                        iconPosition="right"
                        color={color}
                        Icon={CopyIcon}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'stroke'}
                        cmosSize={size}
                        iconPosition="right"
                        color={color}
                        Icon={UserIcon}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={size}
                        iconPosition="right"
                        color={color}
                        Icon={PlusIcon}
                        label={'Button text example'}
                        onClick={() => {}}
                    />
                </Grid>
            </Grid>
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={3} className={styles.inputContainer}>
                    <span style={{ ...FontPrimary(HeaderStyles.H4_18px, true) }}>No Text</span>
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={size}
                        iconPosition="right"
                        color={color}
                        Icon={CopyIcon}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'stroke'}
                        cmosSize={size}
                        iconPosition="right"
                        color={color}
                        Icon={UserIcon}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={3} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={size}
                        iconPosition="right"
                        color={color}
                        Icon={PlusIcon}
                        onClick={() => {}}
                    />
                </Grid>
                <Grid item xs={2} className={styles.inputContainer} />
            </Grid>
        </Grid>
    );
};
export default ButtonTest;
