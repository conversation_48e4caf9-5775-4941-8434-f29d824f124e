import Grid from '@mui/material/Grid';
import { $Icon } from 'common/components/Icons/$Icon';
import { AbsenceIcon } from 'common/components/Icons/AbsenceIcon';
import { AddMenuIcon } from 'common/components/Icons/AddMenuIcon';
import { AlignCenterIcon } from 'common/components/Icons/AlignCenterIcon';
import { AlignLeftIcon } from 'common/components/Icons/AlignLeftIcon';
import { AlignRightIcon } from 'common/components/Icons/AlignRightIcon';
import { ApIcon } from 'common/components/Icons/ApIcon';
import { BinnacleIcon } from 'common/components/Icons/BinnacleIcon';
import { BlockedBriefcaseIcon } from 'common/components/Icons/BlockedBriefcaseIcon';
import { BlockedUserIcon } from 'common/components/Icons/BlockedUserIcon';
import { BoldIcon } from 'common/components/Icons/BoldIcon';
import { BrokenLinkIcon } from 'common/components/Icons/BrokenLinkIcon';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { CallRejectedIcon } from 'common/components/Icons/CallRejectedIcon';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { CashIcon } from 'common/components/Icons/CashIcon';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { CheckCircleIcon } from 'common/components/Icons/CheckCircleIcon';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CheckShieldIcon } from 'common/components/Icons/CheckShieldIcon';
import { CircleIcon } from 'common/components/Icons/CircleIcon';
import { ClipIcon } from 'common/components/Icons/ClipIcon';
import { ClockFilledIcon } from 'common/components/Icons/ClockFilledIcon';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { CloneIcon } from 'common/components/Icons/CloneIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CloudUploadIcon } from 'common/components/Icons/CloudUploadIcon';
import { CmIcon } from 'common/components/Icons/CmIcon';
import { CodeIcon } from 'common/components/Icons/CodeIcon';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import { CutleryIcon } from 'common/components/Icons/CutleryIcon';
import { DashboardIcon } from 'common/components/Icons/DashboardIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { DetailsIcon } from 'common/components/Icons/DetailsIcon';
import { DotListIcon } from 'common/components/Icons/DotListIcon';
import { DoubleCheckIcon } from 'common/components/Icons/DoubleCheckIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { DropIcon } from 'common/components/Icons/DropIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { EmojisIcon } from 'common/components/Icons/EmojisIcon';
import { ExpandIcon } from 'common/components/Icons/ExpandIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { FlagIcon } from 'common/components/Icons/FlagIcon';
import { FollowUpIcon } from 'common/components/Icons/FollowUpIcon';
import { GearHouseIcon } from 'common/components/Icons/GearHouseIcon';
import { GridIcon } from 'common/components/Icons/GridIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { ImageIcon } from 'common/components/Icons/ImageIcon';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { InspectionIcon } from 'common/components/Icons/InspectionIcon';
import { ItalicIcon } from 'common/components/Icons/ItalicIcon';
import { JustifiedIcon } from 'common/components/Icons/JustifiedIcon';
import { LeftIcon } from 'common/components/Icons/LeftIcon';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { ListIcon } from 'common/components/Icons/ListIcon';
import { LockIcon } from 'common/components/Icons/LockIcon';
import { MailIcon } from 'common/components/Icons/MailIcon';
import { MegaphoneIcon } from 'common/components/Icons/MegaphoneIcon';
import { MenuIcon } from 'common/components/Icons/MenuIcon';
import { MicIcon } from 'common/components/Icons/MicIcon';
import { MoveDownIcon } from 'common/components/Icons/MoveDownIcon';
import { MoveLeftIcon } from 'common/components/Icons/MoveLeftIcon';
import { MoveRightIcon } from 'common/components/Icons/MoveRightIcon';
import { MoveUpIcon } from 'common/components/Icons/MoveUpIcon';
import { NaIcon } from 'common/components/Icons/NaIcon';
import { NotificationsIcon } from 'common/components/Icons/NotificationsIcon';
import { NumbersListIcon } from 'common/components/Icons/NumbersListIcon';
import { OptionsIcon } from 'common/components/Icons/OptionsIcon';
import { PasteIcon } from 'common/components/Icons/PasteIcon';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { PhotoIcon } from 'common/components/Icons/PhotoIcon';
import { PlannedAppointmentIcon } from 'common/components/Icons/PlannedAppointmentIcon';
import { PlayIcon } from 'common/components/Icons/PlayIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { PollsIcon } from 'common/components/Icons/PollsIcon';
import { PowerIcon } from 'common/components/Icons/PowerIcon';
import PrintIcon from 'common/components/Icons/PrintIcon';
import { ProyectIcon } from 'common/components/Icons/ProyectIcon';
import { PtIcon } from 'common/components/Icons/PtIcon';
import { RightIcon } from 'common/components/Icons/RightIcon';
import { ScissorsIcon } from 'common/components/Icons/ScissorsIcon';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { SettingsIcon } from 'common/components/Icons/SettingsIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { SignalIcon } from 'common/components/Icons/SignalIcon';
import { SortIcon } from 'common/components/Icons/SortIcon';
import { Timer2Icon } from 'common/components/Icons/Timer2Icon';
import { TimerIcon } from 'common/components/Icons/TimerIcon';
import { TrafficLightIcon } from 'common/components/Icons/TrafficLightIcon';
import { TutorialsIcon } from 'common/components/Icons/TutorialsIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { UncheckedCircleIcon } from 'common/components/Icons/UncheckedCircleIcon';
import { UnderlinedIcon } from 'common/components/Icons/UnderlinedIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { UrlIcon } from 'common/components/Icons/UrlIcon';
import { UserIcon } from 'common/components/Icons/UserIcon';
import { VideoIcon } from 'common/components/Icons/VideoIcon';
import { VolumeIcon } from 'common/components/Icons/VolumeIcon';
import { WarningIcon } from 'common/components/Icons/WarningIcon';
import { WhatsappIcon } from 'common/components/Icons/WhatsappIcon';
import { useInputValue } from 'common/hooks/useInputValue';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { IconSize } from 'common/styles/IconSize';
import { useStyles } from './styles';

export const icons = [
    { name: 'Absence', component: AbsenceIcon },
    { name: 'AddMenu', component: AddMenuIcon },
    { name: 'AlignCenter', component: AlignCenterIcon },
    { name: 'AlignLeft', component: AlignLeftIcon },
    { name: 'AlignRight', component: AlignRightIcon },
    { name: 'Ap', component: ApIcon },
    { name: 'Binnacle', component: BinnacleIcon },
    { name: 'BlockedBriefcase', component: BlockedBriefcaseIcon },
    { name: 'BlockedUser', component: BlockedUserIcon },
    { name: 'Bold', component: BoldIcon },
    { name: 'BrokenLink', component: BrokenLinkIcon },
    { name: 'Calendar', component: CalendarIcon },
    { name: 'CallRejected', component: CallRejectedIcon },
    { name: 'Car', component: CarIcon },
    { name: 'Cash', component: CashIcon },
    { name: 'Circle', component: CircleIcon },
    { name: 'CheckBox', component: CheckBoxIcon },
    { name: 'CheckCircle', component: CheckCircleIcon },
    { name: 'Check', component: CheckIcon },
    { name: 'CheckShield', component: CheckShieldIcon },
    { name: 'Clip', component: ClipIcon },
    { name: 'ClockFilled', component: ClockFilledIcon },
    { name: 'Clock', component: ClockIcon },
    { name: 'Clone', component: CloneIcon },
    { name: 'Close', component: CloseIcon },
    { name: 'CloudUpload', component: CloudUploadIcon },
    { name: 'Cm', component: CmIcon },
    { name: 'Code', component: CodeIcon },
    { name: 'Comments', component: CommentsIcon },
    { name: 'Copy', component: CopyIcon },
    { name: 'Cutlery', component: CutleryIcon },
    { name: 'Dashboard', component: DashboardIcon },
    { name: 'Delete', component: DeleteIcon },
    { name: 'Details', component: DetailsIcon },
    { name: 'DotList', component: DotListIcon },
    { name: 'DoubleCheck', component: DoubleCheckIcon },
    { name: 'Down', component: DownIcon },
    { name: 'Download', component: DownloadIcon },
    { name: 'Drop', component: DropIcon },
    { name: 'DragAndDrop', component: DragAndDropIcon },
    { name: 'Edit', component: EditIcon },
    { name: 'Emojis', component: EmojisIcon },
    { name: 'Expand', component: ExpandIcon },
    { name: 'Filters', component: FiltersIcon },
    { name: 'Flag', component: FlagIcon },
    { name: 'FollowUp', component: FollowUpIcon },
    { name: 'GearHouse', component: GearHouseIcon },
    { name: 'Grid', component: GridIcon },
    { name: 'Hide', component: HideIcon },
    { name: '$Icon', component: $Icon },
    { name: 'Image', component: ImageIcon },
    { name: 'Info', component: InfoIcon },
    { name: 'Inspection', component: InspectionIcon },
    { name: 'Italic', component: ItalicIcon },
    { name: 'Justified', component: JustifiedIcon },
    { name: 'Left', component: LeftIcon },
    { name: 'Less', component: LessIcon },
    { name: 'List', component: ListIcon },
    { name: 'Lock', component: LockIcon },
    { name: 'Mail', component: MailIcon },
    { name: 'Megaphone', component: MegaphoneIcon },
    { name: 'Menu', component: MenuIcon },
    { name: 'Mic', component: MicIcon },
    { name: 'MoveDown', component: MoveDownIcon },
    { name: 'MoveLeft', component: MoveLeftIcon },
    { name: 'MoveRight', component: MoveRightIcon },
    { name: 'MoveUp', component: MoveUpIcon },
    { name: 'Na', component: NaIcon },
    { name: 'Notifications', component: NotificationsIcon },
    { name: 'NumbersList', component: NumbersListIcon },
    { name: 'Options', component: OptionsIcon },
    { name: 'Paste', component: PasteIcon },
    { name: 'Pause', component: PauseIcon },
    { name: 'Phone', component: PhoneIcon },
    { name: 'Photo', component: PhotoIcon },
    { name: 'Planned', component: PlannedAppointmentIcon },
    { name: 'Play', component: PlayIcon },
    { name: 'Plus', component: PlusIcon },
    { name: 'Polls', component: PollsIcon },
    { name: 'Print', component: PrintIcon },
    { name: 'Power', component: PowerIcon },
    { name: 'Proyect', component: ProyectIcon },
    { name: 'Pt', component: PtIcon },
    { name: 'Right', component: RightIcon },
    { name: 'Scissors', component: ScissorsIcon },
    { name: 'Search', component: SearchIcon },
    { name: 'Settings', component: SettingsIcon },
    { name: 'Show', component: ShowIcon },
    { name: 'Signal', component: SignalIcon },
    { name: 'Sort', component: SortIcon },
    { name: 'Timer2', component: Timer2Icon },
    { name: 'Timer', component: TimerIcon },
    { name: 'TrafficLight', component: TrafficLightIcon },
    { name: 'Tutorials', component: TutorialsIcon },
    { name: 'UncheckBox', component: UncheckBoxIcon },
    { name: 'UncheckedCircle', component: UncheckedCircleIcon },
    { name: 'Underlined', component: UnderlinedIcon },
    { name: 'Up', component: UpIcon },
    { name: 'Url', component: UrlIcon },
    { name: 'User', component: UserIcon },
    { name: 'Video', component: VideoIcon },
    { name: 'Volume', component: VolumeIcon },
    { name: 'Warning', component: WarningIcon },
    { name: 'Whatsapp', component: WhatsappIcon },
];

export function IconsTest() {
    const styles = useStyles();

    const [iconSize, setIconSize] = useInputValue(IconSize.M);
    return (
        <Grid container alignContent={'center'} className={styles.section}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>Iconos</span>
            </Grid>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <select onChange={(event: any) => setIconSize(event.target.value)} value={iconSize}>
                    <option value={IconSize.XL}>Extra Large</option>
                    <option value={IconSize.L}>Large</option>
                    <option value={IconSize.M}>Medium</option>
                    <option value={IconSize.S}>Small</option>
                </select>
            </Grid>
            {icons.map(({ name, component: Icon }: any) => (
                <Grid item xs={2} className={styles.iconContainer} key={`key-icon-${name}`}>
                    <Grid item xs={8}>
                        {name}
                    </Grid>
                    <Grid item xs={4}>
                        <Icon size={iconSize} />
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
}
