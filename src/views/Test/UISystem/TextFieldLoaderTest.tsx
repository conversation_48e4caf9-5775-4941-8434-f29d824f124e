import Grid from '@mui/material/Grid';
import { UserIcon } from '../../../common/components/Icons/UserIcon';
import TextField from '../../../common/components/Inputs/TextField';
import { FontPrimary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';
import { useStyles } from './styles';

export function TextFieldLoaderTest() {
    const styles = useStyles();

    return (
        <Grid container spacing={2} alignContent={'center'} className={styles.section}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H3_21px) }}>TextField loader</span>
            </Grid>
            <Grid item xs={4} className={styles.inputContainer} />
            <Grid item xs={2} className={styles.section} style={{ textAlign: 'center' }}>
                <TextField
                    label={'Campo de texto'}
                    name={'textfield.normal'}
                    value={'Texto de ejemplo'}
                    showLoader={true}
                    endAdornment={<UserIcon />}
                />
            </Grid>
            <Grid item xs={2} className={styles.section} style={{ textAlign: 'center' }}>
                <TextField
                    label={'Campo de texto'}
                    name={'textfield.normal'}
                    value={'Texto de ejemplo'}
                    showLoader={true}
                />
            </Grid>
        </Grid>
    );
}
