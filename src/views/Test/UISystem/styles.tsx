import { makeStyles } from '@mui/styles';
import { Colors } from '../../../common/styles/Colors';
export const useStyles = makeStyles((theme) => ({
    section: { marginTop: '30px' },
    iconContainer: {
        padding: 20,
        textAlign: 'center',
        justifyContent: 'space-evenly',

        display: 'flex',
        borderStyle: 'solid',
        borderWidth: '1px',
        borderColor: Colors.Neutral4,
    },
    inputContainer: {
        padding: 20,
        flexDirection: 'column',
        display: 'flex',
        alignItems: 'center',
    },
}));
