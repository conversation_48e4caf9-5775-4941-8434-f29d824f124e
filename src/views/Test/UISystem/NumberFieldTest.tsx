import { useState } from 'react';
import { InputSize } from '../../../common/components/Inputs/InputSize';
import { useDropDownValue } from '../../../common/hooks/useDropDownValue';
import { InputTheme } from '../../../common/styles/InputTheme';
import { icons } from './IconsTest';
import { useStyles } from './styles';

export const NumberFieldTest = () => {
    const styles = useStyles();
    const [stateInput, setStateInput] = useDropDownValue({
        value: 'normal',
        label: 'Normal',
    });
    const [requiredInput, setRequiredInput] = useDropDownValue({
        value: 'normal',
        label: 'Normal',
    });
    const [themeInputDropdown, setThemeInput] = useDropDownValue({
        value: InputTheme.rectangle,
        label: 'Rectangle',
    });
    const [size, setSize] = useDropDownValue<any>({
        value: InputSize.M,
        label: 'Medium (40px)',
    });
    const [customIconDropdown, setCustomIconDropdown] = useDropDownValue<any>({
        value: icons[0].component,
        label: icons[0].name,
        icon: icons[0].component,
    });

    const [template, setTemplate] = useState('${0}$$');
    const [thousandSeparator, setThousandSeparator] = useState(',');
    const [decimalSeparator, setDecimalSeparator] = useState('.');
    const [decimalScale, setDecimalScale] = useState(2);
    const [value, setValue] = useState<number | null>(111222333.44);

    return (
        <>
            {/* <Grid
                container
                alignContent={'center'}
                style={{ paddingTop: '100px', position: 'relative' }}
            >
                <Grid container alignContent={'center'} className={styles.section}>
                    <Grid item xs={12} style={{ textAlign: 'center' }}>
                        <span style={{ ...FontPrimary(HeaderStyles.H3_21px) }}>CurrencyField</span>
                    </Grid>

                    <Grid item xs={1} style={{ textAlign: 'center' }}></Grid>
                    <Grid item xs={2} style={{ textAlign: 'center' }}>
                        <Dropdown
                            onChange={(event: any) => setSize(event)}
                            label="Size Input:"
                            name="custom-icon-notification"
                            value={size}
                            options={[
                                { value: InputSize.M, label: 'Medium (40px)' },
                                { value: InputSize.S, label: 'Small (32px)' },
                            ]}
                        />
                    </Grid>
                    <Grid item xs={1} style={{ textAlign: 'center' }}></Grid>
                    <Grid item xs={2} style={{ textAlign: 'center' }}>
                        <Dropdown
                            onChange={(event: any) => {
                                const customIcon = icons.find(
                                    (i) => i.name == event.value
                                )?.component;
                                if (customIcon) {
                                    setCustomIconDropdown(event);
                                }
                            }}
                            label="Select Icon"
                            name="custom-icon-notification"
                            value={customIconDropdown}
                            optionStyle={OptionStyle.icons}
                            options={icons.map((icon) => ({
                                icon: icon.component,
                                label: icon.name,
                                value: icon.name,
                            }))}
                        ></Dropdown>
                    </Grid>
                    <Grid item xs={1} style={{ textAlign: 'center' }}></Grid>
                    <Grid item xs={2} style={{ textAlign: 'center' }}>
                        <Dropdown
                            onChange={(event: any) => setThemeInput(event)}
                            label="Theme:"
                            name="custom-icon-notification"
                            value={themeInputDropdown}
                            options={[
                                {
                                    value: InputTheme.rectangle,
                                    label: 'Regtangular Blanco',
                                },
                                {
                                    value: InputTheme.rectangle_grey,
                                    label: 'Regtangular Gris',
                                },
                                { value: InputTheme.rounded, label: 'Redondo' },
                            ]}
                        />
                    </Grid>
                    <Grid item xs={1} style={{ textAlign: 'center' }}></Grid>

                    <Grid item xs={2} style={{ textAlign: 'center' }}>
                        <Dropdown
                            onChange={(event: any) => setRequiredInput(event)}
                            label="Required / Optional:"
                            name="custom-icon-notification"
                            value={requiredInput}
                            options={[
                                { value: 'normal', label: 'Unspecified' },
                                { value: 'required', label: 'Required' },
                                { value: 'optional', label: 'optional' },
                            ]}
                        />
                    </Grid>
                    <Grid item xs={1} style={{ textAlign: 'center' }}></Grid>
                    <Grid item xs={2} style={{ textAlign: 'center' }}>
                        <Dropdown
                            onChange={(event: any) => setStateInput(event)}
                            label="Validation State:"
                            name="custom-icon-notification"
                            value={stateInput}
                            options={[
                                { value: 'normal', label: 'Normal' },
                                { value: 'error', label: 'Error' },
                                { value: 'success', label: 'Success' },
                            ]}
                        />
                    </Grid>
                    <Grid item xs={4} style={{ textAlign: 'center' }}></Grid>

                    <Grid container xs={12} justifyContent="center">
                        <div style={{ width: 200 }}>
                            <TextField
                                label={'Currency template'}
                                name={'template'}
                                value={template}
                                onChange={(e) => setTemplate(e.target.value)}
                            ></TextField>
                        </div>
                        <div style={{ width: 200, marginLeft: 8 }}>
                            <TextField
                                label={'Thousand separator'}
                                name={'thousandSeparator'}
                                value={thousandSeparator}
                                onChange={(e) => setThousandSeparator(e.target.value)}
                            ></TextField>
                        </div>
                        <div style={{ width: 200, marginLeft: 8 }}>
                            <TextField
                                label={'Decimal separator'}
                                name={'decimalSeparator'}
                                value={decimalSeparator}
                                onChange={(e) => setDecimalSeparator(e.target.value)}
                            ></TextField>
                        </div>
                        <div style={{ width: 200, marginLeft: 8 }}>
                            <NumberField
                                label={'Decimal scale'}
                                name={'decimalScale'}
                                value={decimalScale}
                                onValueChange={(values) => setDecimalScale(values.floatValue ?? 0)}
                            ></NumberField>
                        </div>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'currencyfield.normal'}
                            value={value}
                            placeholder={'placeholder'}
                            isInvalid={!(stateInput.value == 'success')}
                            isRequired={requiredInput.value == 'required'}
                            showValidationIndicators={requiredInput.value != 'normal'}
                            IconLeft={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'currencyfield.normal'}
                            value={value}
                            isInvalid={!(stateInput.value == 'success')}
                            showValidationIndicators={requiredInput.value != 'normal'}
                            isRequired={requiredInput.value == 'required'}
                            IconLeft={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'textfield.danger'}
                            value={value}
                            isInvalid={!(stateInput.value == 'success')}
                            showValidationIndicators={requiredInput.value != 'normal'}
                            isRequired={requiredInput.value == 'required'}
                            IconRight={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'textfield.success'}
                            value={value}
                            isInvalid={!(stateInput.value == 'success')}
                            isRequired={requiredInput.value == 'required'}
                            showValidationIndicators={requiredInput.value != 'normal'}
                            IconRight={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                </Grid>
                <Grid container alignContent={'center'} className={styles.section}>
                    <Grid item xs={12} style={{ textAlign: 'center' }}>
                        <span style={{ ...FontPrimary(HeaderStyles.H3_21px) }}>
                            Disabled CurrencyField
                        </span>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'currencyfield.normal'}
                            value={value}
                            placeholder={'placeholder'}
                            showValidationIndicators={true}
                            IconRight={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            disabled={true}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'currencyfield.normal'}
                            value={value}
                            showValidationIndicators={true}
                            IconRight={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            disabled={true}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'textfield.danger'}
                            isInvalid={!false}
                            value={value}
                            showValidationIndicators={true}
                            IconRight={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            disabled={true}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={3} className={styles.inputContainer}>
                        <NumberField
                            theme={themeInputDropdown.value}
                            label={'Currency field'}
                            name={'textfield.success'}
                            isInvalid={!true}
                            value={value}
                            showValidationIndicators={true}
                            IconRight={
                                icons.find((i) => i.name == customIconDropdown.value)?.component
                            }
                            size={parseInt(size.value)}
                            disabled={true}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                </Grid>
                <Grid container spacing={2} alignContent={'center'} className={styles.section}>
                    <Grid item xs={4} className={styles.inputContainer}></Grid>
                    <Grid item xs={2} className={styles.section} style={{ textAlign: 'center' }}>
                        <NumberField
                            label={'Currency field'}
                            name={'currencyfield.normal'}
                            value={value}
                            showLoader={true}
                            IconRight={UserIcon}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                    <Grid item xs={2} className={styles.section} style={{ textAlign: 'center' }}>
                        <NumberField
                            label={'Currency field'}
                            name={'currencyfield.normal'}
                            value={value}
                            showLoader={true}
                            thousandSeparator={thousandSeparator}
                            decimalSeparator={decimalSeparator}
                            onValueChange={(values) => setValue(values.floatValue ?? null)}
                            template={template}
                            decimalScale={decimalScale}
                        ></NumberField>
                    </Grid>
                </Grid>
            </Grid> */}
        </>
    );
};
