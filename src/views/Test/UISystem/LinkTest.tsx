import Grid from '@mui/material/Grid';
import { useEffect, useState } from 'react';
import { Link } from '../../../common/components/Link';
import { FontPrimary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';
import { useStyles } from './styles';

export function LinkTest() {
    const styles = useStyles();
    const [url, setUrl] = useState('');
    const renewLink = () => {
        const length = 10;
        var result = '';
        var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-';
        var charactersLength = characters.length;
        for (var i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        setUrl(`#${result}`);
    };

    useEffect(() => {
        renewLink();
    }, []);
    return (
        <Grid container alignContent={'center'} className={styles.section}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>Links</span>
            </Grid>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <button onClick={renewLink}>Renew Link visited</button>
            </Grid>

            <Grid item xs={6} className={styles.section} style={{ textAlign: 'center' }}>
                <Link to={url}>Text with hiperlink</Link>
            </Grid>
            <Grid item xs={6} className={styles.section} style={{ textAlign: 'center' }}>
                <Link to={`${url}2`} disabled={true}>
                    Text with hiperlink
                </Link>
            </Grid>
        </Grid>
    );
}
