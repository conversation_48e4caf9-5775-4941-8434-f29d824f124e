import { Grid, IconProps } from '@mui/material';
import useToasters from 'common/hooks/useToasters';
import { ComponentType, useState } from 'react';
import { ToastersControllerProvider } from 'utils/toasters';
import { Button } from '../../../common/components/Button';
import { EditIcon } from '../../../common/components/Icons/EditIcon';
import Dropdown from '../../../common/components/Inputs/Dropdown';
import TextField from '../../../common/components/Inputs/TextField';
import { NotificationType } from '../../../common/components/Notification/INotificationProps';
import Notifications from '../../../common/components/NotificationPull';
import { NotificationData } from '../../../common/components/NotificationPull/NotificationData';
import { useInputValue } from '../../../common/hooks/useInputValue';
import { Colors } from '../../../common/styles/Colors';
import { FontPrimary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';
import { OptionStyle } from '../../../common/styles/OptionStyle';
import { icons } from './IconsTest';
import { useStyles } from './styles';

const NotificationTest = () => {
    const styles = useStyles();
    const [color, setColor] = useState<NotificationType>(NotificationType.danger);
    const [colorSelect, setColorSelect] = useState<any>();
    const [customTheme, setCustomTheme] = useState({
        Icon: EditIcon as ComponentType<IconProps>,
        color: Colors.Neutral6,
        IconColor: Colors.White,
    });
    const [bodyNotification, setBodyNotification] = useInputValue(
        'Large body test example, you can edit this text in this input'
    );
    const [titleNotification, setTitleNotification] = useInputValue(
        'Large title, you can edit this text in this input'
    );
    const toasters = useToasters();

    return (
        <Grid container alignContent={'center'} style={{ paddingTop: '100px' }}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>Notification Styles</span>
            </Grid>
            <Grid
                item
                xs={4}
                style={{
                    textAlign: 'center',
                    display: 'flex',
                    justifyContent: 'center',
                }}
            >
                <div style={{ width: '40%', textAlign: 'center' }}>
                    <TextField
                        label={'Title Notification: '}
                        name="body"
                        value={titleNotification}
                        onChange={(data) => setTitleNotification(data)}
                    />
                </div>
            </Grid>
            <Grid
                item
                xs={4}
                style={{
                    textAlign: 'center',
                    display: 'flex',
                    justifyContent: 'center',
                }}
            >
                <div style={{ width: '40%', textAlign: 'center' }}>
                    <TextField
                        label={'Body Notification: '}
                        name="body"
                        value={bodyNotification}
                        onChange={(data) => setBodyNotification(data)}
                    />
                </div>
            </Grid>
            <Grid
                item
                xs={4}
                style={{
                    textAlign: 'center',
                    display: 'flex',
                    justifyContent: 'center',
                }}
            >
                <div style={{ width: '40%', textAlign: 'center' }}>
                    <Dropdown
                        name={'Style'}
                        label="Select Notification type:"
                        onChange={(event: any) => {
                            setColor(event.value);
                            setColorSelect(event);
                        }}
                        value={colorSelect}
                        options={[
                            { value: NotificationType.danger, label: 'Danger' },
                            {
                                value: NotificationType.warning,
                                label: 'Warning',
                            },
                            {
                                value: NotificationType.success,
                                label: 'Success',
                            },
                            { value: NotificationType.info, label: 'Info' },
                            { value: NotificationType.progress, label: 'Progress' },
                        ]}
                    />
                </div>
            </Grid>

            {color == NotificationType.custom && (
                <>
                    <Grid item xs={5} style={{ textAlign: 'center' }} />
                    <Grid item xs={2} style={{ textAlign: 'center' }}>
                        <Dropdown
                            onChange={(event) => {
                                if (event === null) return;
                                const customIcon = icons.find(
                                    (i) => i.name == event.value
                                )?.component;
                                setCustomTheme({
                                    ...customTheme,
                                    Icon: customIcon ?? EditIcon,
                                });
                            }}
                            label="Select Icon"
                            name="custom-icon-notification"
                            optionStyle={OptionStyle.icons}
                            options={icons.map((icon) => ({
                                icon: icon.component,
                                label: icon.name,
                                value: icon.name,
                            }))}
                        />
                    </Grid>
                </>
            )}
            <Grid container alignContent={'center'} className={styles.section}>
                <Grid item xs={5} className={styles.inputContainer} />
                <Grid item xs={2} className={styles.inputContainer}>
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        iconPosition="right"
                        label="Show Notification"
                        color={Colors.Neutral3}
                        onClick={() => {
                            const notification = new NotificationData(
                                bodyNotification,
                                titleNotification,
                                color
                            );
                            /*notification.bodyElement = () => (
                            <div>
                                <ul style={{display: 'block', margin:0, padding: 0, listStyle:"none"}}>
                                    <li>Aasdas das dasd asd adas</li>
                                    <li>Aasdas das dasd asd adas</li>
                                    <li>Aasdas das dasd asd adas</li>
                                    <li>Aasdas das dasd asd adas</li>
                                    <li>Aasdas das dasd asd adas</li>
                                </ul>
                            </div>);*/
                            switch (color) {
                                case NotificationType.danger:
                                    toasters.danger(bodyNotification, titleNotification);
                                    break;

                                case NotificationType.success:
                                    toasters.success(bodyNotification, titleNotification);
                                    break;

                                case NotificationType.warning:
                                    toasters.warning(bodyNotification, titleNotification);
                                    break;

                                case NotificationType.info:
                                    toasters.info(bodyNotification, titleNotification);
                                    break;

                                case NotificationType.progress:
                                    const toaster = toasters.progress(
                                        bodyNotification,
                                        titleNotification,
                                        {
                                            id: 'progress' + Math.random(),
                                            duration: 10000,
                                        }
                                    );
                                    setTimeout(() => toaster.dismiss(), 5000);
                                    break;
                            }
                        }}
                    />
                </Grid>
                <Grid item xs={5} className={styles.inputContainer} />
            </Grid>

            <Notifications />
        </Grid>
    );
};

export default function _NotificationTest() {
    return (
        <ToastersControllerProvider>
            <NotificationTest />
        </ToastersControllerProvider>
    );
}
