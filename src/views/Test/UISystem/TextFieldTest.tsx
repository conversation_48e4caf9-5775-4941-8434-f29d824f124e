import Grid from '@mui/material/Grid';
import { InputSize } from '../../../common/components/Inputs/InputSize';
import { useDelay } from '../../../common/hooks/useDelay';
import { useDropDownValue } from '../../../common/hooks/useDropDownValue';
import { InputTheme } from '../../../common/styles/InputTheme';
import { icons } from './IconsTest';
import { useStyles } from './styles';

export const TextFieldTest = () => {
    const styles = useStyles();
    const [stateInput, setStateInput] = useDropDownValue({
        value: 'normal',
        label: 'Normal',
    });
    const [requiredInput, setRequiredInput] = useDropDownValue({
        value: 'normal',
        label: 'Normal',
    });
    const [themeInputDropdown, setThemeInput] = useDropDownValue({
        value: InputTheme.rectangle,
        label: 'Rectangle',
    });
    const [size, setSize] = useDropDownValue<any>({
        value: InputSize.M,
        label: 'Medium (40px)',
    });
    const [customIconDropdown, setCustomIconDropdown] = useDropDownValue<any>({
        value: icons[0].component,
        label: icons[0].name,
        icon: icons[0].component,
    });
    const [backdropSpinnerOpen, setBackdropSpinnerOpen] = useDelay();
    const showBackdropSpinner = () => {
        setBackdropSpinnerOpen(true);
        setTimeout(() => {
            setBackdropSpinnerOpen(false);
        }, 6000);
    };

    return (
        <>
            {/***
             *
             *
             * INPUTS
             *
             */}
            <Grid
                container
                alignContent={'center'}
                style={{ paddingTop: '100px', position: 'relative' }}
            />
        </>
    );
};
