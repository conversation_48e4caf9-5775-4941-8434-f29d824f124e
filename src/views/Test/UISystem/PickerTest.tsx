import { Grid } from '@mui/material';
import { useState } from 'react';
import { Picker } from '../../../common/components/Picker';
import { FontPrimary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';

const PickerTest = () => {
    const [picker, setPicker] = useState<any>(0);

    return (
        <Grid container alignContent={'center'} style={{ paddingTop: '100px' }}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>Pickers</span>
            </Grid>
            <Grid item xs={12} alignContent={'center'}>
                <Grid
                    container
                    alignContent={'center'}
                    alignItems="center"
                    style={{ justifyContent: 'center' }}
                >
                    <div
                        style={{
                            width: '60%',
                            display: 'flex',
                            justifyContent: 'space-evenly',
                        }}
                    >
                        <div />
                        <Picker
                            label="00:00"
                            value={0}
                            onChange={() => setPicker(0)}
                            selected={picker == 0}
                            disabled={false}
                        />
                        <Picker
                            label="00:15"
                            value={1}
                            onChange={() => setPicker(1)}
                            selected={picker == 1}
                            disabled={false}
                        />
                        <Picker
                            label="00:30"
                            value={2}
                            onChange={() => setPicker(2)}
                            selected={picker == 2}
                            disabled={false}
                        />
                        <Picker
                            label="00:45"
                            value={3}
                            onChange={() => setPicker(3)}
                            selected={picker == 3}
                            disabled={false}
                        />
                        <Picker
                            label="01:00"
                            value={4}
                            onChange={() => setPicker(4)}
                            selected={picker == 4}
                            disabled={false}
                        />
                        <Picker
                            label="01:15"
                            value={5}
                            disabled={true}
                            selected={false}
                            onChange={() => {}}
                        />
                        <Picker
                            label="01:30"
                            value={6}
                            disabled={true}
                            selected={false}
                            onChange={() => {}}
                        />
                    </div>
                </Grid>
            </Grid>
        </Grid>
    );
};
export default PickerTest;
