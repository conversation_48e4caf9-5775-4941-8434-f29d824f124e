// TODO it is commented. I am not sure, but it looks like it can be deleted

// const DatePickerTest = ({ setThemeInput, themeInput }: any) => {
//     const styles = useStyles();
//     const [inputValidation, setInputValidation] = useState(InputValidation.default);
//     const [from, setFrom] = useState<Date | null>();
//     const [time, setTime] = useState<any>();

//     const [enabled, setEnabled] = useState('on');
//     return (
//         <Grid container alignContent={'center'} style={{ paddingTop: '100px' }}>
//             <Grid item xs={12} style={{ textAlign: 'center' }}>
//                 <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>
//                     Time Picker and Calendar
//                 </span>
//             </Grid>
//             <Grid item xs={12} style={{ textAlign: 'center' }}>
//                 <select
//                     onChange={(event: any) => setThemeInput(event.target.value)}
//                     value={themeInput}
//                 >
//                     <option value={InputTheme.rectangle}>Regtangular Fondo Blanco</option>
//                     <option value={InputTheme.rectangle_grey}>Regtangular Fondo Gris</option>
//                     <option value={InputTheme.rounded}>Redondo</option>
//                 </select>
//             </Grid>
//             <Grid item xs={12} style={{ textAlign: 'center' }}>
//                 <select
//                     onChange={(event: any) => setInputValidation(event.target.value)}
//                     value={inputValidation}
//                 >
//                     <option value={InputValidation.default}>default</option>
//                     <option value={InputValidation.error}>error</option>
//                     <option value={InputValidation.success}>success</option>
//                 </select>
//             </Grid>
//             <Grid item xs={12} style={{ textAlign: 'center' }}>
//                 <select onChange={(event: any) => setEnabled(event.target.value)} value={enabled}>
//                     <option value={'on'}>Enabled</option>
//                     <option value={'off'}>Disabled</option>
//                 </select>
//             </Grid>
//             <Grid container alignContent={'center'} className={styles.section}>
//                 <Grid item xs={3} className={styles.inputContainer}></Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
//                     <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>
//                         Icon Color Selector
//                     </span>
//                 </Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
//                     <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>
//                         Style Custom Drop component
//                     </span>
//                 </Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
//                     <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>Input Time</span>
//                 </Grid>
//                 <Grid item xs={3} className={styles.inputContainer}></Grid>

//                 <Grid item xs={3} className={styles.inputContainer}></Grid>
//                 <Grid item xs={2} className={styles.inputContainer}>
//                     <TimeFormField
//                         name={'campo1'}
//                         theme={themeInput}
//                         label={'Selector Icon'}
//                         disabled={enabled == 'off'}
//                         isRequired={false}
//                         showValidationIndicators={true}
//                         isInvalid={!(inputValidation == InputValidation.success)}
//                         value={time}
//                         onChange={(value: any) => {
//                             setTime(value);
//                         }}
//                     ></TimePicker>
//                 </Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ marginBottom: 200 }}>
//                     <DatePicker
//                         name={'name'}
//                         label="Date Selector"
//                         value={from}
//                         onChange={setFrom}
//                         disabled={enabled == 'off'}
//                         /*onBlur={()=>setDirtyFilters(true)}*/
//                     />
//                 </Grid>
//                 <Grid
//                     item
//                     xs={2}
//                     className={styles.inputContainer}
//                     style={{ marginBottom: 200 }}
//                 ></Grid>
//                 <Grid item xs={2} className={styles.inputContainer}></Grid>
//             </Grid>
//             <Grid container alignContent={'center'} className={styles.section}>
//                 <Grid item xs={3} className={styles.inputContainer}></Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
//                     <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>
//                         Icon Color Selector
//                     </span>
//                 </Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
//                     <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>
//                         Style Custom Drop component
//                     </span>
//                 </Grid>
//                 <Grid item xs={2} className={styles.inputContainer} style={{ textAlign: 'center' }}>
//                     <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true) }}>Input Time</span>
//                 </Grid>
//                 <Grid item xs={3} className={styles.inputContainer}></Grid>

//                 <Grid item xs={12} style={{ textAlign: 'center' }}>
//                     <div
//                         style={{
//                             display: 'flex',
//                             flexDirection: 'column',
//                             alignItems: 'center',
//                         }}
//                     >
//                         <BoxShadow>
//                             <Calendar
//                                 value={from}
//                                 onChange={setFrom}
//                                 /*onBlur={()=>setDirtyFilters(true)}*/
//                             />
//                         </BoxShadow>
//                     </div>
//                 </Grid>
//             </Grid>
//         </Grid>
//     );
// };
//
// export default DatePickerTest;
