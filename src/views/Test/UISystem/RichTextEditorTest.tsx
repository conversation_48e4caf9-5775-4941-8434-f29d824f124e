import { Grid } from '@mui/material';
import { Checkbox } from 'common/components/Inputs';
import RichTextEditor from 'common/components/Inputs/RichTextEditor';
import { HtmlChangeSource } from 'common/components/Inputs/RichTextEditor/RichTextEditor';
import { formatHtml } from 'common/components/Inputs/RichTextEditor/SourceCodeModal/helpers';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { useRef, useState } from 'react';

export default function RichTextEditorTest() {
    const [html, setHtml] = useState(`
    <h1>Header 1</h1>
    <h2>Header 2</h2>
    <h3>Header 3</h3>
    <h4>Header 4</h4>
    <h5>Header 5</h5>
    <h6>Header 6</h6>

    <p>This is just text, <b>and this is bold</b> and <i>this is italic</i> and we also have <u>undelined text</u>

    <p>My TODO list</p>
    <ul>
        <li>Buy groceries</li>
        <li>Make the world a better place</li>
        <li>I don't know what's next</li>
        <li>I don't plan that far ahead</li>
    </ul>

    <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ">Something very interesting</a>
    `);

    const [lastChangedSource, setLastChangedSource] = useState<HtmlChangeSource | null>(null);
    const [callbackOnBlur, setCallbackOnBlur] = useState(false);
    const counter = useRef(0);
    counter.current++;

    return (
        <Grid container style={{ marginTop: 20 }}>
            <Grid item xs={12} style={{ textAlign: 'center' }}>
                <span style={{ ...FontPrimary(HeaderStyles.H1_34px) }}>RichTextEditor</span>
            </Grid>
            <Grid item xs={6}>
                <RichTextEditor
                    changeCallbackBehavior={callbackOnBlur ? 'onBlur' : 'onUpdate'}
                    html={html}
                    onHtmlChange={(v, s) => {
                        setHtml(v);
                        setLastChangedSource(s);
                    }}
                />
            </Grid>
            <Grid item xs={6}>
                <Checkbox
                    value={callbackOnBlur}
                    onChange={(e) => setCallbackOnBlur(e.target.checked)}
                />
                <span>Call update callback on blur</span>
                <pre>
                    Render counter: {counter.current}
                    {'\n'}
                    Last changed source:
                    {lastChangedSource}
                    {'\n'}
                    HTML:
                    {formatHtml(html)}
                </pre>
            </Grid>
        </Grid>
    );
}
