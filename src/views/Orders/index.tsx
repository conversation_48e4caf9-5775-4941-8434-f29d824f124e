import { Box, CircularProgress, Pagination } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import { useMutation, useQuery } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import ReportsAPI from 'api/Reports';
import FieldsApi, { MultipleOrdersFields } from 'api/fields';
import { PUBNUB_CHANNELS, PUBNUB_MESSAGE_TYPES } from 'api/pubnub';
import { OrderUpdatedEvent } from 'api/workshopPlanner/orders';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import { ProyectIcon } from 'common/components/Icons/ProyectIcon';
import Tooltip from 'common/components/Tooltip';
import { STATUS_PAGE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useURLSearchParams from 'common/hooks/useURLSearchParams';
import { Colors } from 'common/styles/Colors';
import { CSSProperties, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { ordersActions, selectOrderFilters } from 'store/slices/orders';
import { selectOrdersJobs } from 'store/slices/ordersJobs/selectors';
import { fetchOrderJobsThunk } from 'store/slices/ordersJobs/thunks';
import { BlockUpdatedEvent } from 'store/slices/wp/plannings';
import { usePubnubListener } from 'utils/pubnub';
import { WidePageLayout } from 'views/Components/Page';
import { IRepairOrderListRequest } from '../../datacontracts/Order/IOrderListRequest';
import { IRepairOrder } from '../../datacontracts/Order/IOrderListResponse';
import { useOrdersJobsTable } from './CellComponents/OrdersJobs/hooks';
import ColumnFilters from './ColumnFilters';
import OrderTableFilters from './OrderTableFilters';
import OrdersTable from './OrdersTable';
import OrderPreview from './Preview/OrderPreview';
import { useStyles } from './css';
import { combineParamsAndFilters, IOrderPageParams } from './types';

const Orders = (): JSX.Element => {
    const styles = useStyles();
    const navigate = useNavigate();
    const { t } = useAppTranslation();
    useDocumentTitle(t('titles.orders'));

    const { enabledOrdersJobs } = useOrdersJobsTable();
    const isEnterprise = useIsEnterpriseRoute();
    const pageSize = 10;
    const dispatch = useAppDispatch();
    const settings = useAppSelector(selectSettings);
    const ordersJobs = useAppSelector(selectOrdersJobs);

    const [pageParams, setPageParams] = usePageParams();
    const [selectedOrder, setSelectedOrder] = useState<IRepairOrder | null>(null);

    const requestModel = useMemo((): IRepairOrderListRequest => {
        return {
            ...pageParams,
            TmIds: pageParams.TmIds?.join(','),
            From: pageParams.From,
            To: pageParams.To,
            Est:
                pageParams.YEst && !pageParams.NEst
                    ? true
                    : pageParams.NEst && !pageParams.YEst
                    ? false
                    : undefined,
            MRows: pageSize,
            //Because server starts counting from 0
            PIdx: pageParams.PIdx - 1,
            TmTyp: pageParams.TmTyp,
        };
    }, [pageParams]);

    const queryKey = useMemo(() => ['orders', pageParamsToString(pageParams)], [pageParams]);
    const { data, isFetching, isPreviousData, isInitialLoading, refetch } = useQuery(
        queryKey,
        async () => {
            const result = await OrderAPI.list(requestModel);

            let fields: MultipleOrdersFields | null = null;

            if (result.ordersGrid.data.length > 0) {
                if (!isEnterprise) {
                    try {
                        fields = await FieldsApi.getFieldsForMultipleOrders(
                            result.ordersGrid.data.map((x) => x.repairOrderId)
                        );
                    } catch {
                        console.error('failed to get order fields');
                    }
                }

                dispatch(ordersActions.setOrderFields(fields));
            } else {
                dispatch(ordersActions.setOrderFields({}));
            }

            return {
                ...result,
                fields,
            };
        },
        {
            cacheTime: 60000,
            keepPreviousData: true,
            staleTime: pageParams.PIdx === 1 ? 0 : 10000, // always refresh first page from backend, but for other pages limit to 1 request per 10 seconds
        }
    );

    // show loader when first page is loading or when any other pages is loading and the data of the previous page is supposed to be show on screen
    // when data is loading in background isInitialLoading=false, isPreviousData=false, isFetching=true
    // when first page is loading: isInitialLoading=true, isPreviousData=false, isFetching=true
    // when second (or any other) page is loading: isInitialLoading=false, isPreviousData=true, isFetching=true
    const showLoader = isInitialLoading || (isPreviousData && isFetching);
    // display no orders while next page is loading
    const orders = useMemo(
        () => (showLoader ? [] : data?.ordersGrid.data ?? []),
        [data, showLoader]
    );
    const totalOrders = data?.ordersGrid.pagerData.totalItemsCount ?? 0;
    const numberOfPages = data?.ordersGrid.pagerData.pageCount;

    const downloadHandler = useMutation(async () => {
        await ReportsAPI.orders(requestModel);
    });

    usePubnubListener<OrderUpdatedEvent>(
        async (_message) => {
            const order = orders.find(
                (x) => x.repairOrderKey === _message.message.payload.orderKey
            );
            if (order) {
                refetch();
            }
        },
        {
            channels: [PUBNUB_CHANNELS.orders(settings.uid)],
            types: ['order.updated'],
            listenerEnabled: settings.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    usePubnubListener<BlockUpdatedEvent | string>(
        ({ message: { payload, type } }) => {
            if (type === PUBNUB_MESSAGE_TYPES.wp.block.deleted && typeof payload === 'string') {
                const deletedBlock = ordersJobs
                    .map((order) => ({
                        orderId: order.orderId,
                        job: order.jobs.find((job) => job.jobId === payload),
                    }))
                    .find((x) => x.job !== undefined);
                // NOTE (SV) Refetch only if the deleted job is present in one of the orders displayed in the table
                if (deletedBlock) {
                    dispatch(
                        fetchOrderJobsThunk({
                            orderId: deletedBlock.orderId,
                            isRealtimeUpdate: true,
                        })
                    );
                }
            } else if (
                type === PUBNUB_MESSAGE_TYPES.wp.block.updated &&
                typeof payload === 'object' &&
                payload &&
                payload.type === 'Order'
            ) {
                const updatedOrder = orders.find((x) => x.repairOrderKey === payload.orderId);
                // NOTE (SV) Refetch only if the updated job orderId is equal to one of the orders displayed in the table
                if (updatedOrder) {
                    dispatch(
                        fetchOrderJobsThunk({
                            orderId: updatedOrder.repairOrderKey,
                            isRealtimeUpdate: true,
                        })
                    );
                }
            }
        },
        {
            channels: [PUBNUB_CHANNELS.wpBlocks(settings.uid)],
            types: [PUBNUB_MESSAGE_TYPES.wp.block.updated, PUBNUB_MESSAGE_TYPES.wp.block.deleted],
            listenerEnabled: enabledOrdersJobs,
        }
    );

    // TODO separate this into a component
    const counterElement = useMemo(
        () => (
            <Box display="flex" alignItems="center" gap={0.5} justifyContent="end">
                <span className={styles.resultLabel}>
                    <strong>{t('commonLabels.result')}:</strong> {totalOrders} {t('orders.orders')}
                </span>
                <Tooltip
                    position="top"
                    content={t('orders.downloadOrderTooltip')}
                    sx={{
                        whiteSpace: 'nowrap',
                    }}
                >
                    <IconButton
                        disabled={downloadHandler.isLoading}
                        className={styles.downloadIcon}
                        onClick={() => downloadHandler.mutate()}
                        size="small"
                    >
                        {downloadHandler.isLoading ? (
                            <CircularProgress size={16} style={{ margin: 4 }} />
                        ) : (
                            <DownloadIcon fill={Colors.Neutral6} />
                        )}
                    </IconButton>
                </Tooltip>
                {!isEnterprise && (
                    <Tooltip content={t('commonLabels.project')} position="top">
                        <IconButton
                            href={'/dashboard' + STATUS_PAGE_ROUTES.ORDERS}
                            size="small"
                            onClick={(e) => {
                                e.preventDefault();
                                navigate(STATUS_PAGE_ROUTES.ORDERS);
                            }}
                        >
                            <ProyectIcon fill={Colors.Neutral6} />
                        </IconButton>
                    </Tooltip>
                )}
                <ColumnFilters />
            </Box>
        ),
        [t, styles, navigate, totalOrders, downloadHandler, isEnterprise]
    );

    return (
        <>
            <WidePageLayout style={{ '--margin-top': '22px' } as CSSProperties}>
                <OrderTableFilters
                    filters={pageParams}
                    onFiltersChange={(newFilters) => {
                        setPageParams({
                            ...combineParamsAndFilters(pageParams, newFilters),
                            PIdx: 0,
                        });
                    }}
                    counterElement={counterElement}
                />
                <OrdersTable
                    showLoader={showLoader}
                    orders={orders}
                    selectOrder={setSelectedOrder}
                    updatePageParams={setPageParams}
                    pageParams={pageParams}
                />
                <Box display="flex" justifyContent="center">
                    <Pagination
                        disabled={showLoader}
                        count={numberOfPages}
                        page={pageParams.PIdx}
                        onChange={(_, i) => setPageParams({ ...pageParams, PIdx: i })}
                        color="primary"
                        size={'small'}
                    />
                </Box>
            </WidePageLayout>
            {selectedOrder && (
                <OrderPreview
                    orderFromList={selectedOrder}
                    onClose={() => setSelectedOrder(null)}
                />
            )}
        </>
    );
};

function pageParamsToString(p: IOrderPageParams): string {
    return JSON.stringify([
        p.Ron,
        p.AStatus,
        p.OTypes,
        p.Vin,
        p.Plts,
        p.CName,
        p.TmIds,
        p.TmTyp,
        p.From,
        p.To,
        p.OrdBy,
        p.PIdx,
        p.AAlerts,
        p.RAlerts,
        p.YEst,
        p.NEst,
        p.RShops,
        p.Phas,
        p.Id,
    ]);
}

export default Orders;

function usePageParams(): [IOrderPageParams, setParams: (params: IOrderPageParams) => void] {
    const dispatch = useAppDispatch();
    const persistentParams = useAppSelector(selectOrderFilters);
    const navigate = useNavigate();
    const urlParams = useURLSearchParams();
    const page = parsePageParams(urlParams.get('page'));
    const params: IOrderPageParams = useMemo(
        () => ({ ...persistentParams, PIdx: page }),
        [persistentParams, page]
    );

    const initRef = useRef(false);
    useEffect(() => {
        if (initRef.current) return;

        initRef.current = true;

        const newUrlParams = new URLSearchParams(urlParams);
        if (newUrlParams.has('PIdx')) {
            newUrlParams.set('page', newUrlParams.get('PIdx')!);
            newUrlParams.delete('PIdx');
        }

        for (const legacyKey of [
            'Ron',
            'AStatus',
            'OTypes',
            'Vin',
            'Plts',
            'CName',
            'TmIds',
            'TmTyp',
            'From',
            'To',
            'OrdBy',
        ]) {
            if (newUrlParams.has(legacyKey)) {
                newUrlParams.delete(legacyKey);
            }
        }

        if (newUrlParams.toString() !== urlParams.toString()) {
            navigate(
                { search: newUrlParams.size > 0 ? `?${newUrlParams.toString()}` : undefined },
                { replace: true }
            );
        }
    }, [urlParams, params, navigate]);

    const setParams = useCallback(
        ({ PIdx, ...params }: IOrderPageParams) => {
            dispatch(ordersActions.setFilters(params));

            const newUrlParams = new URLSearchParams();

            if (PIdx && PIdx > 1 && Number.isInteger(PIdx)) {
                newUrlParams.set('page', PIdx.toString());
            }

            navigate(
                {
                    search: newUrlParams.size > 0 ? `?${newUrlParams.toString()}` : undefined,
                },
                {
                    replace: true,
                }
            );
        },
        [dispatch, navigate]
    );

    return [params, setParams];
}

function parsePageParams(v: string | null): number {
    if (v === null) return 1;

    const page = +v;

    if (Number.isNaN(v)) {
        return 1;
    }

    if (!Number.isInteger(page) || page < 1) return 1;

    return page;
}
