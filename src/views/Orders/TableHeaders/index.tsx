import { Grid, styled } from '@mui/material';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { PREDEFINED_FIELDS_LIST } from 'api/fields';
import {
    TableHeadAutocompleteFilter,
    TableHeadCell,
    TableHeadSelectFilter,
} from 'common/components';
import { OrderByType } from 'common/constants';
import { OrdersTableColumns, labelHeader } from 'common/constants/OrdersTableColumns';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { FilterOption } from 'common/types';
import {
    forwardRef,
    useCallback,
    useEffect,
    useImperativeHandle,
    useLayoutEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import {
    DEFAULT_SHOWN_COLUMNS,
    selectJobsColumnsWidth,
    selectOrderColumnsWidth,
    setColumnsWidth,
} from 'store/slices/orders';
import { selectExpandedOrders } from '../../../store/slices/ordersJobs/selectors';
import { useOrdersJobsTable } from '../CellComponents/OrdersJobs/hooks';
import { useDisplayedOrderColumns } from '../helpers';
import { IOrderPageParams } from '../types';
import PhaseHeader from './Headers/PhaseHeader';
import TableHeaderUpload from './Headers/TableHeaderUpload';

type TableHeadersProps = {
    pageParams: IOrderPageParams;
    onPageParamsChange: (filters: IOrderPageParams) => void;
};

const PreventTextOverflow = styled('div')({
    textTransform: 'none',
    display: '-webkit-box',
    '-webkit-line-clamp': '2',
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
    maxWidth: 200,
    width: 'max-content',
});

const PreventTextOverflowPreserveTextTransform = styled(PreventTextOverflow)({
    textTransform: 'inherit',
    maxWidth: 250,
    '-webkit-line-clamp': '3',
});

function TableHeaders({ pageParams, onPageParamsChange }: TableHeadersProps) {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const settings = useAppSelector(selectSettings);
    const displayedColumns = useDisplayedOrderColumns();
    const isEnterprise = useIsEnterpriseRoute();
    const { renderOrdersJobsHeader, enabledOrdersJobs } = useOrdersJobsTable();
    const headerRowRef = useRef<HTMLTableRowElement | null>(null);
    const savedWidths = useAppSelector(selectOrderColumnsWidth);
    const fillerRef = useRef<FillerHeadCellHandle | null>(null);
    const expandedOrders = useAppSelector(selectExpandedOrders);
    const jobsColumnsWidth = useAppSelector(selectJobsColumnsWidth);

    // CMOS-4814 p4: To achieve matching between columns of two tables we have to store the Order table and Jobs table column widths to make our tables "responsive"
    const measureColumns = useCallback(() => {
        if (!headerRowRef.current) return;

        const columns = (
            Array.from(headerRowRef.current.children) as HTMLTableCellElement[]
        ).filter((c) => c.id !== 'filler');

        const ordersTableColumnsWidths = columns.map((c) =>
            Math.round(c.getBoundingClientRect().width)
        );

        const changed =
            ordersTableColumnsWidths.length !== savedWidths.length ||
            ordersTableColumnsWidths.some((w, i) => w !== savedWidths[i]);

        // CMOS-4814 p4: Calculating filler column width
        const container = document.getElementById('orders_table');
        if (container && columns.length) {
            const jobsColumnsWidthsWithPadding =
                jobsColumnsWidth.reduce((acc, w) => acc + w, 0) + 100; // CMOS-4814 p4: 100 is a Jobs table left padding
            const ordersTableWidth = container.getBoundingClientRect().width;
            // CMOS-4814 p4: On specific displays with specific number of columns selected, Jobs table may be wider than Orders headers selected and vice versa
            const targetWidth = Math.max(jobsColumnsWidthsWithPadding, ordersTableWidth);
            // CMOS-4814 p4: left here just in case
            // console.log('DEBUG-JCW:' + jobsColumnsWidthsWithPadding);
            // console.log('DEBUG-OTW:' + ordersTableWidth);
            // console.log('DEBUG-MAX:' + targetWidth);

            const columnsWidth = ordersTableColumnsWidths.reduce((acc, w) => acc + w, 0);
            if (targetWidth > columnsWidth) {
                const delta = targetWidth - columnsWidth;
                fillerRef.current?.setWidth(delta);
                // console.log('DEBUG-DELTA:' + delta);
            } else fillerRef.current?.setWidth(0);
        }
        // console.log('DEBUG-___________________________');

        if (changed) {
            dispatch(setColumnsWidth(ordersTableColumnsWidths));
        }
    }, [savedWidths, jobsColumnsWidth, dispatch]);

    // CMOS-4814 p4: Measuring after the DOM was built
    useLayoutEffect(measureColumns, [
        displayedColumns,
        enabledOrdersJobs,
        expandedOrders,
        measureColumns,
    ]);

    // CMOS-4814 p4: Running reMeasuring on window resize / zooming
    useEffect(() => {
        window.addEventListener('resize', measureColumns);
        return () => window.removeEventListener('resize', measureColumns);
    }, [measureColumns]);

    const repairShopsOptions = useMemo<FilterOption<number>[]>(() => {
        //small optimization
        if (isEnterprise) {
            return settings.enterpriseSettings!.shops.map((s) => ({
                id: s.id,
                label: s.name,
            }));
        } else return [];
    }, [isEnterprise, settings.enterpriseSettings]);

    const alertStatusesOptions = useMemo(
        () => [
            { id: 'Active', label: t('orders.alertStatuses.active') },
            { id: 'Resolved', label: t('orders.alertStatuses.resolved') },
        ],
        [t]
    );

    const estimateStatusesOptions = useMemo(
        () => [
            { id: 'Yes', label: t('orders.estimateStatuses.yes') },
            { id: 'No', label: t('orders.estimateStatuses.no') },
        ],
        [t]
    );

    const onSetOrderByFilter = useCallback(
        (order: OrderByType) => {
            onPageParamsChange({
                ...pageParams,
                OrdBy: order,
            });
        },
        [onPageParamsChange, pageParams]
    );

    // IMPORTANT: If you want to add new column here - make sure you set the EXPLICIT fixed width and maxWidth on it, otherwise you can break the layout
    return (
        <TableHead>
            <TableRow ref={headerRowRef}>
                {enabledOrdersJobs && renderOrdersJobsHeader()}
                {displayedColumns.map((header) => {
                    if (header.type === 'enterprise_account') {
                        return (
                            <TableHeadCell
                                key="enterprise_account"
                                style={{ width: '180px', maxWidth: '180px' }}
                            >
                                <Grid
                                    container
                                    alignItems="center"
                                    justifyContent="flex-start"
                                    wrap="nowrap"
                                >
                                    <HeaderTitle>
                                        {t(labelHeader(OrdersTableColumns.ACCOUNT))}
                                    </HeaderTitle>
                                    <TableHeadAutocompleteFilter
                                        mainLabel={t(labelHeader(OrdersTableColumns.ACCOUNT))}
                                        options={repairShopsOptions}
                                        selected={pageParams.RShops}
                                        onSelectedChanged={(selected) => {
                                            onPageParamsChange({
                                                ...pageParams,
                                                RShops: selected,
                                            });
                                        }}
                                    />
                                </Grid>
                            </TableHeadCell>
                        );
                    } else if (header.type === 'order_table_ui') {
                        if (header.key === DEFAULT_SHOWN_COLUMNS.INSPECTION) {
                            return (
                                <TableHeadCell
                                    key={header.key}
                                    style={{ width: '170px', maxWidth: '170px' }}
                                >
                                    <Grid
                                        container
                                        alignItems="center"
                                        justifyContent="flex-start"
                                        wrap="nowrap"
                                    >
                                        <HeaderTitle>
                                            {t(labelHeader(OrdersTableColumns.INSPECTION))}
                                        </HeaderTitle>
                                        <TableHeadSelectFilter
                                            hideApplyButton
                                            mainLabel={t('orders.alertStatuses.title')}
                                            options={alertStatusesOptions}
                                            selected={[
                                                ...(pageParams.AAlerts === true ? ['Active'] : []),
                                                ...(pageParams.RAlerts === true
                                                    ? ['Resolved']
                                                    : []),
                                            ]}
                                            onSelectedChanged={(selected) => {
                                                onPageParamsChange({
                                                    ...pageParams,
                                                    AAlerts: selected.includes('Active')
                                                        ? true
                                                        : undefined,
                                                    RAlerts: selected.includes('Resolved')
                                                        ? true
                                                        : undefined,
                                                });
                                            }}
                                        />
                                    </Grid>
                                </TableHeadCell>
                            );
                        } else if (header.key === DEFAULT_SHOWN_COLUMNS.ESTIMATE) {
                            return (
                                <TableHeadCell
                                    key={header.key}
                                    style={{ width: '170px', maxWidth: '170px' }}
                                >
                                    <Grid
                                        container
                                        alignItems="center"
                                        justifyContent="flex-start"
                                        wrap="nowrap"
                                    >
                                        <HeaderTitle>
                                            {t(labelHeader(OrdersTableColumns.ESTIMATE))}
                                        </HeaderTitle>
                                        <TableHeadSelectFilter
                                            hideApplyButton
                                            mainLabel={t('orders.estimateStatuses.title')}
                                            options={estimateStatusesOptions}
                                            selected={[
                                                ...(pageParams.YEst === true ? ['Yes'] : []),
                                                ...(pageParams.NEst === true ? ['No'] : []),
                                            ]}
                                            onSelectedChanged={(selected) => {
                                                onPageParamsChange({
                                                    ...pageParams,
                                                    YEst: selected.includes('Yes')
                                                        ? true
                                                        : undefined,
                                                    NEst: selected.includes('No')
                                                        ? true
                                                        : undefined,
                                                });
                                            }}
                                        />
                                    </Grid>
                                </TableHeadCell>
                            );
                        } else if (header.key === DEFAULT_SHOWN_COLUMNS.UPLOADED) {
                            return (
                                <TableHeaderUpload
                                    key={header.key}
                                    initialOrderBy={pageParams.OrdBy}
                                    onSetOrderByFilter={onSetOrderByFilter}
                                    style={{ width: 170, maxWidth: 170 }}
                                />
                            );
                        } else if (header.key === DEFAULT_SHOWN_COLUMNS.ORDER_NUMBER) {
                            return (
                                <TableHeadCell
                                    key={header.key}
                                    style={{ width: 196, maxWidth: 196 }}
                                >
                                    {t(header.labelTranslationKey)}
                                </TableHeadCell>
                            );
                        } else if (header.key === DEFAULT_SHOWN_COLUMNS.COMMUNICATION) {
                            return (
                                <TableHeadCell
                                    key={header.key}
                                    style={{ width: 330, maxWidth: 330 }}
                                >
                                    {t(header.labelTranslationKey)}
                                </TableHeadCell>
                            );
                        } else if (header.key === DEFAULT_SHOWN_COLUMNS.PHASE) {
                            return (
                                <PhaseHeader
                                    key={`header_${header.key}`}
                                    label={header.labelTranslationKey}
                                    selected={pageParams.Phas || ''}
                                    onSetPhasesByFilter={(phases) => {
                                        onPageParamsChange({
                                            ...pageParams,
                                            Phas: phases,
                                        });
                                    }}
                                    style={{ width: 180, maxWidth: 180 }}
                                />
                            );
                        } else if (header.key === DEFAULT_SHOWN_COLUMNS.TEAM) {
                            return (
                                <TableHeadCell key={header.key} style={{ width: 80, maxWidth: 80 }}>
                                    {t(header.labelTranslationKey)}
                                </TableHeadCell>
                            );
                        } else {
                            return (
                                <TableHeadCell
                                    key={header.key}
                                    style={{ width: 308, maxWidth: 308 }}
                                >
                                    {t(header.labelTranslationKey)}
                                </TableHeadCell>
                            );
                        }
                    } else {
                        const name = header.name;
                        if (PREDEFINED_FIELDS_LIST.includes(header.key)) {
                            const translationKey = `settings.customizableFields.predefined.${header.key.replaceAll(
                                '.',
                                '_'
                            )}`;
                            //lookup the translation for name value this field,
                            // if it doesn't exist then display as is
                            const name =
                                t(translationKey) !== translationKey
                                    ? t(translationKey)
                                    : header.key;
                            const width = Math.min(140 + name.length * 8, 270);
                            return (
                                <TableHeadCell
                                    key={header.key}
                                    style={{ width: width, maxWidth: width }}
                                >
                                    <PreventTextOverflowPreserveTextTransform>
                                        {name}
                                    </PreventTextOverflowPreserveTextTransform>
                                </TableHeadCell>
                            );
                        }

                        return (
                            <TableHeadCell key={header.key} style={{ width: 120, maxWidth: 120 }}>
                                <PreventTextOverflow>{name}</PreventTextOverflow>
                            </TableHeadCell>
                        );
                    }
                })}
                {/* CMOS-4814 p4: Need here to fill the empty space till the table right edge if there is not enough columns (table has "table-layout: fixed" and it's vital for this feature) */}
                <FillerHeadCell ref={fillerRef} />
            </TableRow>
        </TableHead>
    );
}

type FillerHeadCellHandle = {
    setWidth: (w: number) => void;
};

// CMOS-4814 p4: Using useState in the parent TableHeaders component brings infinite rerenders so we have to encapsulate it
const FillerHeadCell = forwardRef<FillerHeadCellHandle>((_, ref) => {
    const [width, setWidth] = useState<number>(0);

    // CMOS-4814 p4: Exposing the setter to the parent so only this cell will be rerendered
    useImperativeHandle(
        ref,
        () => ({
            setWidth: (w: number) => setWidth(w),
        }),
        []
    );

    return <TableHeadCell id="filler" key="filler" style={{ width, maxWidth: width }} />;
});

const HeaderTitle = styled('span')({
    whiteSpace: 'nowrap',
});

export default TableHeaders;
