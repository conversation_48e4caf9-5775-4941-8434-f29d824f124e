import { KeyboardArrowDown, KeyboardArrowUp } from '@mui/icons-material';
import { Box, styled, TableCellProps } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import TableHeadCell from 'common/components/TableHeadCell';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { OrderByType, OrderByTypeLabel } from 'common/constants/OrderByType';
import { labelHeader, OrdersTableColumns } from 'common/constants/OrdersTableColumns';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import React, { useState } from 'react';

const Label = styled('span')(({ theme, 'aria-selected': selected }) => ({
    ...theme.typography.h6Roboto,
    display: 'inline-block',
    marginLeft: 5,
    color: selected ? theme.palette.primary.main : 'inherit',

    '.selected &': {
        color: theme.palette.primary.main,
    },
}));

const CustomMenuItem = styled(SMenuItem2)({
    minWidth: 110,
    marginTop: 0,
    marginBottom: 0,
});

const MenuItemOrderBy = React.forwardRef(
    (
        { onClick, type, orderBy }: { onClick: Function; type: OrderByType; orderBy: OrderByType },
        ref?: React.ForwardedRef<HTMLLIElement>
    ) => {
        const { t } = useAppTranslation();

        const isDesc = type === OrderByType.UpdatedDesc || type === OrderByType.UploadedDesc;
        return (
            <CustomMenuItem
                className={type === orderBy ? 'selected' : undefined}
                onClick={() => onClick(type)}
                ref={ref}
                disableRipple
            >
                {isDesc ? <KeyboardArrowDown /> : <KeyboardArrowUp />}
                <Label>{t(OrderByTypeLabel(type))}</Label>
            </CustomMenuItem>
        );
    }
);

function TableHeaderUpload({
    initialOrderBy,
    onSetOrderByFilter,
    ...props
}: {
    initialOrderBy: OrderByType;
    onSetOrderByFilter: (type: OrderByType) => void;
} & TableCellProps) {
    const { t } = useAppTranslation();

    const [orderBy, setOrderBy] = useState<OrderByType>(initialOrderBy);
    const [showMenuOrderBy, setShowMenuOrderBy] = useState<any>(null);
    const handleLastCommMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setShowMenuOrderBy(event.currentTarget);
    };
    const handleCloseMenuOrderBy = () => {
        setShowMenuOrderBy(null);
    };

    const handleSelectType = (type: OrderByType) => {
        setOrderBy(type);
        onSetOrderByFilter(type);
        setShowMenuOrderBy(null);
    };

    const idLastCommMenu = showMenuOrderBy ? 'uploaded-orderBy-popper' : undefined;
    const isDesc = orderBy === OrderByType.UpdatedDesc || orderBy === OrderByType.UploadedDesc;
    return (
        <TableHeadCell {...props}>
            <Box alignItems="center" justifyContent="start" display="flex">
                {orderBy == 'UploadedAsc' || orderBy == 'UploadedDesc'
                    ? t(labelHeader(OrdersTableColumns.UPLOADED))
                    : t(labelHeader(OrdersTableColumns.UPDATED))}
                <IconButton
                    onClick={handleLastCommMenuClick}
                    aria-controls="simple-menu"
                    aria-haspopup="true"
                    size="small"
                >
                    {isDesc ? (
                        <DownIcon fill={Colors.Neutral7} />
                    ) : (
                        <UpIcon fill={Colors.Neutral7} />
                    )}
                </IconButton>
                <SMenu
                    borders
                    id={idLastCommMenu}
                    anchorEl={showMenuOrderBy}
                    keepMounted
                    open={Boolean(showMenuOrderBy)}
                    anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center',
                    }}
                    transformOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                    }}
                    onClose={handleCloseMenuOrderBy}
                >
                    <MenuItemOrderBy
                        orderBy={orderBy}
                        onClick={handleSelectType}
                        type={OrderByType.UploadedAsc}
                    />
                    <MenuItemOrderBy
                        orderBy={orderBy}
                        onClick={handleSelectType}
                        type={OrderByType.UploadedDesc}
                    />
                    <MenuItemOrderBy
                        orderBy={orderBy}
                        onClick={handleSelectType}
                        type={OrderByType.UpdatedAsc}
                    />
                    <MenuItemOrderBy
                        orderBy={orderBy}
                        onClick={handleSelectType}
                        type={OrderByType.UpdatedDesc}
                    />
                </SMenu>
            </Box>
        </TableHeadCell>
    );
}

export default TableHeaderUpload;
