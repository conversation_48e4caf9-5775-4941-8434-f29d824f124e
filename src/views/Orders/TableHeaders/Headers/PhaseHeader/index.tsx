import { Grid, TableCellProps } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import WpPhasesApi from 'api/workshopPlanner/phases';
import { TableHeadCell, TableHeadSelectFilter } from 'common/components';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { chain } from 'lodash';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectIsEnterprise } from 'store/slices/globalSettingsSlice';
import { useStyles } from './css';

type PhaseHeaderProps = {
    label: string;
    selected: string;
    onSetPhasesByFilter: (phases: string) => void;
};

function PhaseHeader({
    label,
    selected,
    onSetPhasesByFilter,
    ...props
}: PhaseHeaderProps & TableCellProps) {
    const { t } = useAppTranslation();
    const styles = useStyles();
    const isEnterprise = useSelector(selectIsEnterprise);

    const queryKey = isEnterprise ? ['enterprise', 'phases'] : ['phases'];
    const queryFn = isEnterprise
        ? () => EnterpriseOrdersApi.phases.getAll()
        : () => WpPhasesApi.getPhases();

    const { data } = useQuery(queryKey, queryFn);
    const phases = useMemo(() => data ?? [], [data]);

    const optionsDistinct = useMemo(() => {
        return chain(phases)
            .groupBy((p) => p.name)
            .map((value, key) => {
                if (value[0].id === Phases.Closed) {
                    return {
                        id: Phases.Closed.toString(),
                        label: t('orderDetails.closedOrder'),
                    };
                } else if (value[0].id === Phases.NoPhase) {
                    return {
                        id: Phases.NoPhase.toString(),
                        label: t('orderDetails.noPhase'),
                    };
                } else
                    return {
                        id: value
                            .map((p) => {
                                return p.id;
                            })
                            .join(','),
                        label: key,
                    };
            })
            .value();
    }, [t, phases]);

    return (
        <TableHeadCell {...props}>
            <Grid container alignItems="center" justifyContent="flex-start" wrap="nowrap">
                <span className={styles.headerTitle}>{t(label)}</span>
                <TableHeadSelectFilter
                    hideApplyButton={false}
                    mainLabel={t('orders.phaseFilterPopupTitle')}
                    options={optionsDistinct}
                    selected={selected.split(',')}
                    onSelectedChanged={(selected) => {
                        onSetPhasesByFilter(selected.join());
                    }}
                />
            </Grid>
        </TableHeadCell>
    );
}

export default PhaseHeader;
