import { OrderByType, TeamMemberType } from '../../common/constants';

export interface IOrderPageFilters {
    Ron?: string;
    AStatus?: string;
    OTypes?: string[];
    Vin?: string;
    Plts?: string;
    CName?: string;
    TmIds?: number[];
    TmTyp?: TeamMemberType;
    From?: string;
    To?: string;
    Phas?: string;
    Id?: string;
}

export function getDefaultOrderFilters(): IOrderPageFilters {
    return {};
}

export interface IOrderPageParams extends IOrderPageFilters {
    OrdBy: OrderByType;
    PIdx: number;
    AAlerts?: boolean;
    RAlerts?: boolean;
    YEst?: boolean;
    NEst?: boolean;
    RShops: number[];
}

export function combineParamsAndFilters(
    pageParams: IOrderPageParams,
    filters: IOrderPageFilters
): IOrderPageParams {
    const { OrdBy, PIdx, <PERSON>lerts, <PERSON>lerts, <PERSON><PERSON>t, NEst, RShops } = pageParams;
    return {
        ...filters,
        OrdBy,
        PIdx,
        AAlerts,
        RAlerts,
        YEst,
        NEst,
        RShops,
    };
}
