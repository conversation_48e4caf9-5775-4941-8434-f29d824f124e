import { styled } from '@mui/material';
import { TableCell } from 'common/components';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import TowerNumber from 'views/WorkshopPlanner/common/TowerNumber';
import usePhaseStyles from './css';

type PhaseProps = {
    order: IRepairOrder;
};

const Inner = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 8,
});

export default function Phase({ order }: PhaseProps) {
    const styles = usePhaseStyles();
    const { t } = useAppTranslation();

    let phaseName = order.phase.name;
    if (order.phase.id < 0) phaseName = t(`orderDetails.${order.phase.name}`);

    return (
        <TableCell className={styles.cell} component="td">
            <Inner>
                <TowerNumber
                    towerNumber={order.tower}
                    orderTypeKey={order.orderType?.id}
                    appointmentReasonColor={order.appointmentReasonColor}
                    userIdOrKey={order.inChargeUser?.key}
                    displayMode="ellipsis"
                />
                <span className={styles.name}>{phaseName}</span>
            </Inner>
        </TableCell>
    );
}
