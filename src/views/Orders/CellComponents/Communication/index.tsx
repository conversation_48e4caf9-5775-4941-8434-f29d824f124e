import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { GetTimePassed } from 'common/DateTimeHelpers';
import { ImageIcon } from 'common/components/Icons/ImageIcon';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import TableCell from 'common/components/TableCell';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import { memo } from 'react';

export const Communication = memo(({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const mediasCount = repairOrder.photoCount + repairOrder.videoCount;
    const { t } = useAppTranslation();

    // NOTE (AP) That's beautiful workaround implemented for CMM-349
    return (
        <TableCell component="td" scope="row">
            <StyledLastAction>
                <div>{t(`orders.status.${repairOrder.inspectionFormStatus}`)}</div>
                <small>{GetTimePassed(repairOrder.lastCommunicationTime, t)}</small>
            </StyledLastAction>
            <StyledCommunicationItemContainer>
                <StyledCommunicationItem>
                    {mediasCount > 0 && (
                        <Grid container alignItems="center" wrap="nowrap">
                            <ImageIcon fill={Colors.Neutral6} />
                            <span>{mediasCount}</span>
                        </Grid>
                    )}
                </StyledCommunicationItem>
                <StyledCommunicationItem>
                    {repairOrder.phoneCallsCount > 0 && (
                        <Grid container alignItems="center" wrap="nowrap">
                            <PhoneIcon fill={Colors.Neutral6} />
                            <span>{repairOrder.phoneCallsCount || 0}</span>
                        </Grid>
                    )}
                </StyledCommunicationItem>
                <StyledCommunicationItem>
                    {repairOrder.consumerViews > 0 && (
                        <Grid container alignItems="center" wrap="nowrap">
                            <ShowIcon fill={Colors.Neutral6} />
                            <span>{repairOrder.consumerViews || 0}</span>
                        </Grid>
                    )}
                </StyledCommunicationItem>
            </StyledCommunicationItemContainer>
        </TableCell>
    );
});

const StyledLastAction = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[7],
    fontWeight: 'bold',
    whiteSpace: 'nowrap',
    paddingLeft: 5,
    ...theme.typography.h6Inter,
    '& > small': {
        ...theme.typography.h7Roboto,
    },
}));

const StyledCommunicationItem = styled('div')({
    width: 50,
    paddingRight: 5,
    fontSize: 11,
});

const StyledCommunicationItemContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'start',
    paddingTop: 3,
});

export default Communication;
