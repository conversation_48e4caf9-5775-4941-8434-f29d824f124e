import Grid from '@mui/material/Grid';
import Tooltip from '@mui/material/Tooltip';
import clsx from 'clsx';
import { UserIcon } from 'common/components/Icons/UserIcon';
import TableCell from 'common/components/TableCell';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import { memo } from 'react';
import { useStyles } from './css';

export const TeamMembers = memo(({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const styles = useStyles();
    const { t } = useAppTranslation();

    return (
        <TableCell component="td" scope="row" className={styles.cell}>
            <Grid container justifyContent="center">
                <Grid
                    item
                    xs={5}
                    className={clsx(
                        styles.initials,
                        styles.inChargeInitials,
                        repairOrder.inChargeUser && styles.pointer
                    )}
                >
                    {repairOrder.inChargeUser ? (
                        <Tooltip
                            title={`${t('commonLabels.inCharge')}: ${
                                repairOrder.inChargeUser.name
                            }`}
                        >
                            <span>{repairOrder.inChargeUser.initials}</span>
                        </Tooltip>
                    ) : (
                        <UserIcon fill={Colors.White} size={IconSize.S} />
                    )}
                </Grid>
                <Grid
                    item
                    xs={5}
                    className={clsx([
                        styles.initials,
                        styles.assignedToInitials,
                        repairOrder.assignedToDisplayName && styles.pointer,
                    ])}
                >
                    {repairOrder.assignedToDisplayName ? (
                        <Tooltip
                            title={`${t('commonLabels.assignedTo')}: ${
                                repairOrder.assignedToDisplayName
                            }`}
                        >
                            <span>{repairOrder.assignedToInitials}</span>
                        </Tooltip>
                    ) : (
                        <UserIcon fill={Colors.CM1} size={IconSize.S} />
                    )}
                </Grid>
            </Grid>
        </TableCell>
    );
});

export default TeamMembers;
