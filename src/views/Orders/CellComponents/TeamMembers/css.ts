import { makeStyles } from '@mui/styles';
import { Colors } from '../../../../common/styles/Colors';

export const useStyles = makeStyles((theme) => ({
    inChargeInitialsLabel: {
        width: 10,
        height: 10,
    },
    pointer: {
        cursor: 'pointer',
    },
    cell: {
        minWidth: 50,
        width: 50,
    },
    initials: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontWeight: 'bold',
        fontStretch: 'normal',
        fontStyle: 'normal',
        letterSpacing: 0.32,
        textAlign: 'center',
        height: 18,
        width: 24,
        fontSize: 9,
        fontFamily: 'inter',
    },
    inChargeInitials: {
        borderRadius: '5px 0 0 5px',
        margin: '0 1px 0 0',
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.neutral[1],
    },
    assignedToInitials: {
        borderRadius: '0 5px 5px 0',
        backgroundColor: Colors.CM4,
        margin: '0 0 0 1px',
        color: theme.palette.primary.main,
    },
}));
