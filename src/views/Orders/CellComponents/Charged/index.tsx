import TableCell from 'common/components/TableCell';
import ArrowTooltip from 'common/components/Tooltip';
import { OrderByType } from 'common/constants/OrderByType';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import moment from 'moment';
import { memo } from 'react';
import { useStyles } from './css';

export const Charged = memo(
    ({ repairOrder, orderBy }: { repairOrder: IRepairOrder; orderBy: OrderByType }) => {
        const styles = useStyles();
        const { t } = useAppTranslation();

        const date =
            orderBy == 'UploadedAsc' || orderBy == 'UploadedDesc'
                ? repairOrder.uploadTime
                : repairOrder.lastUpdateTime;

        const isToday = (date: Date) => {
            const today = moment();
            const targetDate = moment(date);
            return (
                targetDate.date() === today.date() &&
                targetDate.month() === today.month() &&
                targetDate.year() === today.year()
            );
        };

        const format = isToday(date) ? t('dateFormats.timeOfToday') : t('dateFormats.dayOfMonth');

        const formattedDate = moment(date).format(format);

        return (
            <TableCell component="td" scope="row">
                <ArrowTooltip content={moment(date).format('L LTS [GMT]Z')}>
                    <span className={styles.uploadedTimeLabel}>{formattedDate}</span>
                </ArrowTooltip>
            </TableCell>
        );
    }
);

export default Charged;
