import { Box, styled } from '@mui/material';
import Badge from '@mui/material/Badge';
import { HtmlTooltip } from 'common/components/HtmlTooltip';
import { NotificationsIcon } from 'common/components/Icons/NotificationsIcon';
import TableCell from 'common/components/TableCell';
import useForceRender from 'common/hooks/useForceRender';
import { Colors } from 'common/styles/Colors';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import React, { useState } from 'react';
import AlertTooltip from '../../AlertsTooltip';

export const Inspection = ({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const [showTooltip, setShowTooltip] = useState(false);
    // const styles = useStyles();
    const forceRender = useForceRender();

    return (
        <TableCell component="td" scope="row" sx={{ minWidth: 83 }}>
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 30px)' }}>
                <Priority
                    style={{
                        backgroundColor: '#36ce91',
                    }}
                >
                    {repairOrder.green}
                </Priority>
                <Priority
                    style={{
                        backgroundColor: '#ffc626',
                    }}
                >
                    {repairOrder.yellow}
                </Priority>
                <Priority
                    style={{
                        backgroundColor: '#f15857',
                    }}
                >
                    {repairOrder.red}
                </Priority>
                {repairOrder.activeAlerts > 0 ? (
                    <Priority
                        sx={{ padding: 0 }}
                        onMouseEnter={() => setShowTooltip(true)}
                        onMouseLeave={() => setShowTooltip(false)}
                    >
                        <HtmlTooltip
                            placement="right"
                            title={
                                <React.Fragment>
                                    <AlertTooltip
                                        orderId={repairOrder.repairOrderId}
                                        forceRender={forceRender}
                                    />
                                </React.Fragment>
                            }
                        >
                            <Badge
                                overlap="rectangular"
                                badgeContent={repairOrder.activeAlerts}
                                color="error"
                                variant="dot"
                                sx={{ '& .MuiBadge-badge': { top: 6, right: 7 } }}
                            >
                                <NotificationsIcon
                                    fill={showTooltip ? Colors.CM2 : Colors.Neutral6}
                                />
                            </Badge>
                        </HtmlTooltip>
                    </Priority>
                ) : null}
            </Box>
        </TableCell>
    );
};

const Priority = styled('div')(({ theme }) => ({
    width: 22,
    height: 22,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '50%',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[1],
    textAlign: 'center',
}));

export default Inspection;
