import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';
import { SingleLineText } from '../../../../common/styles/TextHelpers';

export const useStyles = makeStyles((theme) => ({
    container: {
        display: 'flex',
        gap: 8,
        alignItems: 'center',
    },
    iconContainer: {
        minWidth: 32,
        //don't kill me for that (AP)
        minHeight: 35,
        userSelect: 'none',
    },
    labelsContainer: {
        width: '100%',
        maxWidth: 150,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
    },
    labelContainer: {
        ...SingleLineText(),
    },
    customerLabel: {
        ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[7]),
    },
    vehicleLabel: {
        ...FontSecondary(HeaderStyles.H7_11px, false, theme.palette.neutral[7]),
    },
}));
