import { useEffect, useState } from 'react';
import TableCell from '../../../../common/components/TableCell';
import { IRepairOrder } from '../../../../datacontracts/Order/IOrderListResponse';
import { useStyles } from './css';

const DefaultIcon = import.meta.env.VITE_BASE_URL + '/assets/images/no-make-icon.png';

export const CustomerVehicle = ({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const [iconUrl, setIconUrl] = useState<string>(DefaultIcon);
    const styles = useStyles();

    useEffect(() => {
        if (repairOrder.make && repairOrder.make.toLowerCase() !== 'default') {
            const isHighResolution = 'devicePixelRatio' in window && window.devicePixelRatio === 2;
            const resolutionURL = isHighResolution ? '@2x' : '';
            const makeURL = repairOrder.make
                .trim()
                .toLowerCase()
                //think that about spaces inside of make statement
                .replaceAll(' ', '_');
            setIconUrl(
                `${import.meta.env.VITE_AWS_BRANDS_LOGO_URL}/${makeURL}_32x32${resolutionURL}.png`
            );
        }
    }, [repairOrder]);

    const vehicleInfo = (
        <>
            {`${repairOrder.model ?? ''} ${repairOrder.year ?? ''}`.trim()}
            <div style={{ marginTop: 4 }}>{repairOrder.plates}</div>
        </>
    );

    return (
        <TableCell component="td" scope="row">
            <div className={styles.container}>
                <div className={styles.iconContainer}>
                    <img src={iconUrl} width="32" alt="" />
                </div>
                <div className={styles.labelsContainer}>
                    <div className={styles.labelContainer}>
                        <span className={styles.customerLabel}>
                            {repairOrder.consumerFirstName} {repairOrder.consumerLastName}
                        </span>
                    </div>
                    <div className={styles.labelContainer}>
                        <span className={styles.vehicleLabel}>{vehicleInfo}</span>
                    </div>
                </div>
            </div>
        </TableCell>
    );
};

export default CustomerVehicle;
