import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';
import { SingleLineText } from '../../../../common/styles/TextHelpers';

export const useStyles = makeStyles((theme) => ({
    labelContainer: {
        maxWidth: 100,
    },
    primaryLabel: {
        ...SingleLineText(),
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
    },
    secondaryLabel: {
        ...SingleLineText(),
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
        fontStyle: 'italic',
    },
}));
