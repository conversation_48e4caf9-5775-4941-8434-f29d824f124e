import Grid from '@mui/material/Grid';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import TableCell from '../../../../common/components/TableCell';
import { IRepairOrder } from '../../../../datacontracts/Order/IOrderListResponse';
import { useStyles } from './css';

export const OrderType = ({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const styles = useStyles();
    const { repairShopSettings } = useSelector(selectSettings);

    const orderType = repairOrder.orderType?.name;

    const additionalStatus = repairOrder.additionalStatus;

    return (
        <TableCell component="td" scope="row">
            <Grid
                container
                justifyContent="flex-start"
                alignItems="center"
                className={styles.labelContainer}
            >
                {repairShopSettings?.features.orderType ? (
                    orderType ? (
                        <span className={styles.primaryLabel}>{orderType}</span>
                    ) : (
                        <span className={styles.secondaryLabel}>{additionalStatus}</span>
                    )
                ) : additionalStatus ? (
                    <span className={styles.primaryLabel}>{additionalStatus}</span>
                ) : (
                    <span className={styles.secondaryLabel}>{orderType}</span>
                )}
            </Grid>
        </TableCell>
    );
};

export default OrderType;
