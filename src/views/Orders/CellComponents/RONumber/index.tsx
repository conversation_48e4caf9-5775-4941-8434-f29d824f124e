import { styled } from '@mui/material';
import { useAppRoutes } from 'common/hooks/useAppRoutes';
import { Link } from 'react-router-dom';
import TableCell from '../../../../common/components/TableCell';
import { IRepairOrder } from '../../../../datacontracts/Order/IOrderListResponse';
import { replaceUrl } from '../../../../utils';
import Tooltip from '../../../../common/components/Tooltip';
import { selectSettings } from '../../../../store/slices/globalSettingsSlice';
import { useSelector } from 'react-redux';

const RoNumberStyled = styled('span')({
    display: 'inline-block',
    padding: '6px 0',
    fontFamily: 'inter',
    fontSize: 12,
    fontWeight: 'bold',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: 1.17,
    letterSpacing: 'normal',
    textAlign: 'left',
    direction: 'rtl',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    color: 'var(--cm3)',
    '&:hover': {
        textDecoration: 'underline',
    },
});

const StyledCutRowLink = styled(Link)({
    display: 'block',
});

const cutOrderNumber = (orderName: string, maxCharacters: number) => {
    const lastPart = orderName.slice(-maxCharacters);
    return lastPart + '...'; // Потому что direction: 'rtl'
};

export const RONumber = ({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const { appRoutes } = useAppRoutes();
    const { repairShopSettings } = useSelector(selectSettings);

    const cutOrderNumbers = repairShopSettings?.features.showCutOrderNumber ?? false;

    return (
        <TableCell align="left" component="td" scope="row">
            <StyledCutRowLink
                data-test-id="ro-number-link"
                to={replaceUrl(appRoutes.ORDERS_DETAIL, { id: repairOrder.repairOrderId })}
            >
                <Tooltip
                    content={repairOrder.repairOrderNumber}
                    position={'bottom'}
                    disabled={
                        (repairOrder.repairOrderNumber.length <= 8 && cutOrderNumbers) ||
                        repairOrder.repairOrderNumber.length <= 30
                    }
                >
                    <RoNumberStyled>
                        {cutOrderNumbers && repairOrder.repairOrderNumber.length > 8
                            ? cutOrderNumber(repairOrder.repairOrderNumber, 8)
                            : repairOrder.repairOrderNumber.length > 30
                            ? cutOrderNumber(repairOrder.repairOrderNumber, 30)
                            : repairOrder.repairOrderNumber}
                    </RoNumberStyled>
                </Tooltip>
            </StyledCutRowLink>
        </TableCell>
    );
};

export default RONumber;
