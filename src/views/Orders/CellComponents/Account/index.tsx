import { Grid } from '@mui/material';
import TableCell from '../../../../common/components/TableCell';
import { IRepairOrder } from '../../../../datacontracts/Order/IOrderListResponse';
import { useStyles } from './css';

export const Account = ({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const styles = useStyles();
    return (
        <TableCell key={'Cell_Account_row_' + repairOrder.repairOrderId} component="td" scope="row">
            <Grid
                container
                justifyContent="flex-start"
                alignItems="center"
                className={styles.labelContainer}
            >
                <span className={styles.label}>{repairOrder.repairShopName}</span>
            </Grid>
        </TableCell>
    );
};

export default Account;
