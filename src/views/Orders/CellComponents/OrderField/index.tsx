import { styled } from '@mui/material';
import { PREDEFINED_FIELDS, PREDEFINED_FIELDS_LIST } from 'api/fields';
import { TimeSpan } from 'api/utils/format';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { TableCell } from 'common/components';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import { TFunction } from 'i18next';
import moment from 'moment';
import { memo } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectOrdersFields } from 'store/slices/orders/selectors';

type OrderFieldProps = {
    order: IRepairOrder;
    field: string;
};

type SelectValue = {
    name: string;
};

export default function OrderField({ order, field }: OrderFieldProps) {
    if (PREDEFINED_FIELDS_LIST.includes(field)) {
        return (
            <TableCell align="left" component="td" scope="row">
                <PredefinedOrderFieldValue order={order} field={field} />
            </TableCell>
        );
    }

    return <CustomFieldCell orderId={order.repairOrderId} fieldId={field} />;
}

const PredefinedOrderFieldValueInner = styled('div')({
    maxHeight: 'calc(var(--orders-table-row-height) - 12px)',
    overflow: 'hidden',
});

const PredefinedOrderFieldValue = memo(
    ({ field, order }: { field: string; order: IRepairOrder }) => {
        const { t } = useAppTranslation();
        const value = getFieldValue(t, order, field);

        if (value === undefined) return null;

        return <PredefinedOrderFieldValueInner>{value}</PredefinedOrderFieldValueInner>;
    }
);

function getFieldValue(t: TFunction, order: IRepairOrder, field: string): React.ReactNode {
    switch (field) {
        case PREDEFINED_FIELDS.CUSTOMER_EMAIL:
            return order.consumerEmail || '--';
        case PREDEFINED_FIELDS.CUSTOMER_BUSINESS_NAME:
            return order.businessName || '--';
        case PREDEFINED_FIELDS.CUSTOMER_ID_DOCUMENT:
            if (order.idDocTypeName) {
                return (
                    <span>
                        <strong>{order.idDocTypeName}</strong>: {order.idDocNumber || '--'}
                    </span>
                );
            }
            return order.idDocNumber || '--';
        case PREDEFINED_FIELDS.CUSTOMER_LANDLINE:
            return order.consumerLandlinePhoneNumber || '--';
        case PREDEFINED_FIELDS.CUSTOMER_MOBILE:
            return order.consumerMobilePhoneNumber || '--';
        case PREDEFINED_FIELDS.CUSTOMER_PAYMENT_METHOD:
            return t(`orders.paymentMethodTypes.${order.paymentMethod}`);
        case PREDEFINED_FIELDS.VEHICLE_VIN:
            return order.vin || '--';
        case PREDEFINED_FIELDS.VEHICLE_MILEAGE:
            return order.mileage === 0 ? '--' : order.mileage + '';
        case PREDEFINED_FIELDS.ORDER_TYPE:
            return order.orderType?.name || '--';
    }
}

const CustomFieldValueInner = styled('div')({
    overflowY: 'hidden',
    maxHeight: 'calc(var(--orders-table-row-height) - 9px)',
    boxSizing: 'border-box',
});

function CustomFieldCell({ fieldId, orderId }: { fieldId: string; orderId: number }) {
    const fields = useAppSelector(selectOrdersFields);
    const { value, type } = (fields[orderId] ?? {})[fieldId] ?? { value: '', type: 'ShortText' };

    let renderedValue: React.ReactNode = value || '--';

    switch (type) {
        case 'Currency':
            renderedValue = <MoneyValue value={value} />;
            break;
        case 'Numeric':
            renderedValue = <NumericValue value={value} />;
            break;
        case 'Time':
            renderedValue = <TimeValue value={value} />;
            break;
        case 'Date':
            renderedValue = <DateValue value={value} />;
            break;
        case 'Select':
        case 'MultiSelect':
            try {
                const parsed = JSON.parse(value);
                if (typeof parsed === 'object' && parsed !== null && parsed.value !== null) {
                    renderedValue = parsed.value.map((x: SelectValue) => x.name).join(', ') || '--';
                }
            } catch {
                //keep going using value variable as renderedValue
            }
    }

    return (
        <TableCell
            style={{ paddingTop: 4, paddingBottom: 4 }}
            align="left"
            component="td"
            scope="row"
        >
            <CustomFieldValueInner>{renderedValue}</CustomFieldValueInner>
        </TableCell>
    );
}

function NumericValue({ value }: { value: string }) {
    const { internationalization } = useAppSelector(selectSettings);

    return (
        <>
            {value && !Number.isNaN(+value)
                ? InternationalizationLogic.numberFormat(internationalization, +value)
                : value || '--'}
        </>
    );
}

function MoneyValue({ value }: { value: string }) {
    const { internationalization } = useAppSelector(selectSettings);

    return (
        <>
            {value && !Number.isNaN(+value)
                ? InternationalizationLogic.numberToCurrency(internationalization, +value)
                : value || '--'}
        </>
    );
}

function DateValue({ value }: { value: string }) {
    const dateFormat = useAppSelector(selectSettings).internationalization.dateFormat;
    let formatted: string = '--';

    if (value) {
        try {
            formatted = moment(value).format(dateFormat);
        } catch {
            formatted = value;
        }
    }

    return <>{formatted}</>;
}

function TimeValue({ value }: { value: string }) {
    let formatted = '--';

    if (value) {
        try {
            formatted = TimeSpan.fromString(value).toShortString();
        } catch {
            formatted = value;
        }
    }

    return <>{formatted}</>;
}
