import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    cell: {
        minWidth: 120,
        gap: 11,
    },

    estimate: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
    },

    estimateLabel: {
        ...FontSecondary(HeaderStyles.H9_9px, false, theme.palette.neutral[7]),
    },

    badge: {
        '& .MuiBadge-badge': { top: 7, right: 4 },
    },
}));
