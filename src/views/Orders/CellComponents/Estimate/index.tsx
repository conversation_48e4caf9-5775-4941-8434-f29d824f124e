import { Badge, BadgeProps, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { HtmlTooltip } from 'common/components/HtmlTooltip';
import { CashIcon } from 'common/components/Icons/CashIcon';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import TableCell from 'common/components/TableCell';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import { memo, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import EstimateTooltip from '../../EstimateTooltip';
import { useStyles } from './css';

export const Estimate = memo(({ repairOrder }: { repairOrder: IRepairOrder }) => {
    const { internationalization, repairShopSettings } = useSelector(selectSettings);

    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;

    const roundAwaitFromZero = useCallback(
        (value: number): number => {
            return repairShopSettings?.features.enableRemoveDecimals ? Math.round(value) : value;
        },
        [repairShopSettings]
    );

    const styles = useStyles();
    return (
        <TableCell component="td" scope="row">
            <Grid
                container
                alignItems="center"
                justifyContent="flex-start"
                className={styles.cell}
                wrap="nowrap"
            >
                <Grid item xs={'auto'}>
                    {repairOrder.yellow + repairOrder.red > 0 ? (
                        <HtmlTooltip
                            placement="right"
                            title={
                                <EstimateTooltip
                                    estimatesCount={
                                        repairOrder.red + repairOrder.yellow + repairOrder.green
                                    }
                                    orderId={repairOrder.repairOrderId}
                                />
                            }
                        >
                            {repairOrder.inspectionFormStatus === 'ApprovedByConsumer' ||
                            repairOrder.inspectionFormStatus === 'ApprovedByTeamMember' ? (
                                <StyledBadge
                                    color="success"
                                    variant="standard"
                                    className={styles.badge}
                                    overlap="rectangular"
                                    badgeContent={<CheckIcon fill={Colors.White} size={10} />}
                                >
                                    <CashIcon fill={Colors.CM2} size={IconSize.L} />
                                </StyledBadge>
                            ) : repairOrder.inspectionFormStatus === 'DeclinedByConsumer' ||
                              repairOrder.inspectionFormStatus === 'DeclinedByTeamMember' ? (
                                <StyledBadge
                                    color="error"
                                    variant="standard"
                                    className={styles.badge}
                                    overlap="rectangular"
                                    badgeContent={<CloseIcon fill={Colors.White} size={10} />}
                                >
                                    <CashIcon fill={Colors.CM2} size={IconSize.L} />
                                </StyledBadge>
                            ) : (
                                <div>
                                    <CashIcon fill={Colors.CM2} size={IconSize.L} />
                                </div>
                            )}
                        </HtmlTooltip>
                    ) : (
                        <CashIcon fill={Colors.CM2} size={IconSize.L} />
                    )}
                </Grid>
                <Grid item xs={'auto'}>
                    <Grid container>
                        <Grid item xs={12}>
                            <span className={styles.estimate}>
                                {InternationalizationLogic.numberToCurrency(
                                    internationalization,
                                    roundAwaitFromZero(repairOrder.yellowRedTotalCostWithTaxes),
                                    { requireDecimals: requireDecimals }
                                )}
                            </span>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        </TableCell>
    );
});

export default Estimate;

const StyledBadge = styled(Badge)<BadgeProps>({
    '& .MuiBadge-badge': {
        padding: 0,
        minWidth: 'auto',
        width: 10,
        height: 10,
    },
});
