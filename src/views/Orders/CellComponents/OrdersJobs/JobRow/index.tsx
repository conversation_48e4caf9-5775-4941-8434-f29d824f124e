import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined';
import PauseCircleOutlinedIcon from '@mui/icons-material/PauseCircleOutlined';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import { styled, TableRow, useTheme } from '@mui/material';
import { BinnacleOutlineIcon } from 'common/components/Icons/BinnacleOutlineIcon';
import TableCell from 'common/components/TableCell';
import { ROUTES } from 'common/constants';
import { DateTime } from 'luxon';
import { useNavigate } from 'react-router-dom';
import { Job, ordersJobsActions } from 'store/slices/ordersJobs';
import theme from 'theme';
import { FontSecondary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';
import { useAppDispatch, useAppSelector } from '../../../../../store';
import { selectIanaTz } from '../../../../../store/slices/globalSettingsSlice';
import { JobProgressLabel } from '../JobProgressLabel';

type JobRowProps = {
    job: Job;
};

export const JobRow = ({ job }: JobRowProps) => {
    const theme = useTheme();
    const navigate = useNavigate();
    const ianaTz = useAppSelector(selectIanaTz);
    const dispatch = useAppDispatch();

    if (!job) return;

    const redirectWP = async () => {
        // Need to preserve expanded jobs and scroll position
        // https://clearmechanic.atlassian.net/browse/CMOS-4814 req. 1
        dispatch(ordersJobsActions.setPreserveJobs(true));

        const path = `${ROUTES.WORKSHOP_PLANNER}?startDate=${job.scheduledStartDate}&assignedTo=${job.assignedUserId}&planning=${job.planningId}`;
        navigate(path);
    };

    return (
        <>
            <StyledTableRow sx={{ height: '60px' }} key={job.jobId} onClick={redirectWP}>
                <StyledCell width={340}>
                    <DescriptionContainer>
                        {!!job.description && (
                            <BinnacleOutlineIcon size={32} fill={theme.palette.primary.light} />
                        )}
                        {job.description}
                    </DescriptionContainer>
                </StyledCell>
                <StatusCell width={230}>{convertJobStatusToIcon(job)}</StatusCell>
                <BradSimmonsDesignCell width={180}>{job.assignedUserName}</BradSimmonsDesignCell>
                <BradSimmonsDesignCell width={200}>{`${convertDateToLocal(
                    job.scheduledStartDate,
                    ianaTz,
                    'HH:mm'
                )} - ${convertDateToLocal(
                    job.scheduledStopDate,
                    ianaTz,
                    'HH:mm'
                )}`}</BradSimmonsDesignCell>
                <StyledCell width={350}>
                    <PauseContainer>
                        {job.pauseDate ? (
                            <span>
                                {convertDateToLocal(job.pauseDate, ianaTz, 'dd MMM yyyy - HH:mm')}
                            </span>
                        ) : (
                            <span>--</span>
                        )}
                    </PauseContainer>
                </StyledCell>
            </StyledTableRow>
        </>
    );
};

function convertDateToLocal(date: string, ianaTz: string, format: string) {
    return DateTime.fromISO(date, {
        zone: 'utc',
    })
        .setZone(ianaTz)
        .toFormat(format);
}

function convertJobStatusToIcon(job: Job) {
    switch (job.status) {
        case 'NotStarted':
            return <PlayCircleOutlineIcon color={'info'} />;
        case 'InProgress':
            return (
                <JobProgressLabel
                    status={job.status}
                    lastStart={job.lastStart}
                    accumulatedDurationInSeconds={job.accumulatedDurationInSeconds}
                />
            );
        case 'Paused':
            return <PauseCircleOutlinedIcon color={'info'} />;
        case 'Stopped':
            return <CheckCircleOutlinedIcon color={'info'} />;
    }
}

const DescriptionContainer = styled('div')({
    ...theme.typography.h7Roboto,
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    gap: '11px',
});

const PauseContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
});

const StyledCell = styled(TableCell)({
    ...theme.typography.h7Roboto,
});

//https://clearmechanic.atlassian.net/browse/CMOS-4223?focusedCommentId=199440
const BradSimmonsDesignCell = styled(TableCell)({
    ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
});

const StatusCell = styled(StyledCell)({
    ...theme.typography.h7Roboto,
    height: '60px',
    display: 'flex',
    padding: '16px 50px 16px 10px !important',
});

const StyledTableRow = styled(TableRow)({
    '&:last-child > *': { borderBottom: 'none' },
    '&:hover': {
        cursor: 'pointer',
        backgroundColor: 'rgba(211, 224, 255, 0.27)',
    },
});
