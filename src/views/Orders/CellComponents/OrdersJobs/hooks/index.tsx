import TableHeadCell from 'common/components/TableHeadCell';
import { useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectIsEnterprise, selectWorkshopPlannerEnabled } from 'store/slices/globalSettingsSlice';
import { ordersJobsActions } from '../../../../../store/slices/ordersJobs';
import { selectExpandedOrders } from '../../../../../store/slices/ordersJobs/selectors';
import { ExpandedTable } from '../ExpandedTable';
import { JobsToggle } from '../JobsToggle';

export const useOrdersJobsTable = () => {
    const dispatch = useAppDispatch();
    const isEnterprise = useAppSelector(selectIsEnterprise);
    const enableWorkshopPlanner = useAppSelector(selectWorkshopPlannerEnabled);
    const expandedOrders = useAppSelector(selectExpandedOrders);
    const enabledOrdersJobs = useMemo(
        () => !isEnterprise && enableWorkshopPlanner,
        [enableWorkshopPlanner, isEnterprise]
    );

    const renderHeadCell = () => <TableHeadCell style={{ width: 98, maxWidth: 98 }} />;

    const renderToggle = (orderId: string, jobsCount: number) => (
        <JobsToggle orderId={orderId} jobsCountInitial={jobsCount} />
    );

    const renderTable = (orderId: string, jobsCount: number) => (
        <ExpandedTable orderId={orderId} jobsCount={jobsCount} />
    );

    useEffect(() => {
        return () => {
            dispatch(ordersJobsActions.clearOrderJobs());
        };
    }, [dispatch]);

    return {
        renderOrdersJobsHeader: renderHeadCell,
        renderOrdersJobsToggle: renderToggle,
        renderOrdersJobsTable: renderTable,
        isExpanded: (orderId: string) => !!expandedOrders[orderId],
        enabledOrdersJobs,
    };
};
