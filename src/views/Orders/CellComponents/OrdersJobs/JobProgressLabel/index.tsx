import { styled } from '@mui/material';
import { DateTime } from 'luxon';
import { useEffect, useState } from 'react';
import theme from 'theme';
import WpApi from '../../../../../api/workshopPlanner';
import { useServerUtcNow } from '../../../../../common/hooks/useServerUtcNow';

type JobProgressLabelProps = {
    status: WpApi.JobStatus;
    accumulatedDurationInSeconds: number;
    lastStart: string | null;
};

export const JobProgressLabel = ({
    status,
    lastStart,
    accumulatedDurationInSeconds,
}: JobProgressLabelProps) => {
    const [elapsed, setElapsed] = useState<number>(accumulatedDurationInSeconds);
    const serverUtcNow = useServerUtcNow();

    useEffect(() => {
        let timerId: number | undefined;

        if (status === 'InProgress') {
            const start = !lastStart
                ? DateTime.fromMillis(serverUtcNow())
                : DateTime.fromISO(lastStart, { zone: 'utc' });

            const update = () => {
                let secondsSinceStart = DateTime.fromMillis(serverUtcNow()).diff(
                    start,
                    'seconds'
                ).seconds;
                if (secondsSinceStart < 0) secondsSinceStart = 0;
                setElapsed(Math.floor(accumulatedDurationInSeconds + secondsSinceStart));
            };

            update();
            timerId = setInterval(update, 1000);
        } else {
            setElapsed(accumulatedDurationInSeconds);
        }

        return () => {
            if (timerId) clearInterval(timerId);
        };
    }, [status, accumulatedDurationInSeconds, lastStart, serverUtcNow]);

    return <InProgressStatusLabel>{formatTime(elapsed)}</InProgressStatusLabel>;
};

function formatTime(totalSeconds: number): string {
    const hours = Math.floor(totalSeconds / 3600).toString();
    const minutes = Math.floor((totalSeconds % 3600) / 60)
        .toString()
        .padStart(2, '0');
    const seconds = (totalSeconds % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
}

const InProgressStatusLabel = styled('div')({
    ...theme.typography.h6Inter,
    color: theme.palette.primary.light,
    fontWeight: 'bold',
    display: 'flex',
    width: '100px',
});
