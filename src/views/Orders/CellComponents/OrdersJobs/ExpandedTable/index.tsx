import { Collapse, Skeleton, styled, Table, TableBody, TableHead, TableRow } from '@mui/material';
import TableCell from 'common/components/TableCell';
import TableHeadCell from 'common/components/TableHeadCell';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectExpandedOrders, selectJobsByOrderId } from 'store/slices/ordersJobs/selectors';
import { selectOrderColumnsWidth, setJobsColumnsWidth } from '../../../../../store/slices/orders';
import { JobRow } from '../JobRow';

type JobHistoryRowProps = {
    orderId: string;
    jobsCount: number;
};

export const ExpandedTable = ({ orderId, jobsCount }: JobHistoryRowProps) => {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const orderJobs = useAppSelector((r) => selectJobsByOrderId(r, orderId));
    const expandedOrders = useAppSelector(selectExpandedOrders);
    const orderColumnsWidth = useAppSelector(selectOrderColumnsWidth);

    // CMOS-4814 p4: Main width calculation algorithm
    const getWidth = (outerTableWidths: number[], innerTableWidths: number[]): number[] => {
        const result: number[] = [];
        let i = 1; // where we start in the outer columns

        for (const target of innerTableWidths) {
            // if we've run out of outer columns, just fall back to target
            if (i >= outerTableWidths.length) {
                result.push(target);
                continue;
            }

            // take at least one column
            let columnWidth = outerTableWidths[i];
            let j = i + 1;

            // keep adding columns while we’re still short of the requested width
            while (columnWidth < target && j < outerTableWidths.length) {
                columnWidth += outerTableWidths[j];
                j += 1;
            }

            result.push(columnWidth); // columnWidth ≥ target here
            i = j; // next inner cell starts where we stopped
        }

        return result;
    };

    // CMOS-4814 p4: Storing default minimum widths here to calculate correct widths to match outer Orders table columns
    const columnWidths = useMemo(() => {
        const jobTableMinWidths = [340, 160, 250, 200, 170];
        return getWidth(orderColumnsWidth, jobTableMinWidths);
    }, [orderColumnsWidth]);

    // CMOS-4814 p4: Dispatching calculated widths so we can calculate the filler cell required width in Orders table headers
    useEffect(() => {
        dispatch(setJobsColumnsWidth(columnWidths));
    }, [columnWidths, dispatch]);

    // CMOS-4814 p4:
    // "table-layout: fixed" use first-row widths, but only as a minimum.
    // If the table itself is wider than the sum of those widths, the browser must
    // redistribute the surplus across all columns (CSS 2.1 §17.5.2.1 step 7).
    // When we hide a couple of Orders columns the parent <td> shrinks, yet the
    // nested table is still 100 % wide — so the extra pixels are spread back onto the
    // columns and first job column header becomes "columnWidths[0] + x" px instead of "columnWidths[0]" px
    // despite having correct value in "width" CSS attribute.
    const tableWidth = useMemo(() => columnWidths.reduce((acc, w) => acc + w, 0), [columnWidths]);

    return (
        <TableRow>
            <TableCell style={{ padding: 0 }} colSpan={8}>
                <Collapse in={expandedOrders[orderId] === true} unmountOnExit>
                    <RootContainer>
                        <FixedTable aria-label="jobs" sx={{ width: `${tableWidth}px` }}>
                            <TableHead>
                                <TableRow>
                                    <StyledTableHeadCell style={{ width: columnWidths[0] }}>
                                        {t('orders.jobHistoryTableHeaders.jobDescription')}
                                    </StyledTableHeadCell>
                                    <StatusHeadCell style={{ width: columnWidths[1] }}>
                                        {t('orders.jobHistoryTableHeaders.status')}
                                    </StatusHeadCell>
                                    <StyledTableHeadCell style={{ width: columnWidths[2] }}>
                                        {t('orders.jobHistoryTableHeaders.assigned')}
                                    </StyledTableHeadCell>
                                    <StyledTableHeadCell style={{ width: columnWidths[3] }}>
                                        {t('orders.jobHistoryTableHeaders.scheduled')}
                                    </StyledTableHeadCell>
                                    <StyledTableHeadCell style={{ width: columnWidths[4] }}>
                                        {t('orders.jobHistoryTableHeaders.pause')}
                                    </StyledTableHeadCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {orderJobs?.isLoading
                                    ? Array.from({ length: jobsCount }, (_, i) => (
                                          <TableRow key={i}>
                                              <SkeletonTableCell colSpan={5}>
                                                  <Skeleton
                                                      variant="rounded"
                                                      height={40}
                                                      animation="pulse"
                                                      width="100%"
                                                  />
                                              </SkeletonTableCell>
                                          </TableRow>
                                      ))
                                    : orderJobs?.jobs.map((job) => (
                                          <JobRow key={job.jobId} job={job} />
                                      ))}
                            </TableBody>
                        </FixedTable>
                    </RootContainer>
                </Collapse>
            </TableCell>
        </TableRow>
    );
};

const RootContainer = styled('div')({
    padding: '25px 0px 25px 100px',
});

const FixedTable = styled(Table)({
    tableLayout: 'fixed',
    borderCollapse: 'unset',
});

const StyledTableHeadCell = styled(TableHeadCell)(({ theme }) => ({
    backgroundColor: `${theme.palette.common.white} !important`,
    borderBottom: 'none',
}));

const StatusHeadCell = styled(StyledTableHeadCell)(({ theme }) => ({
    backgroundColor: `${theme.palette.common.white} !important`,
    borderBottom: 'none',
    padding: '16px 50px 16px 10px !important',
}));

const SkeletonTableCell = styled(TableCell)({
    borderBottom: 'none',
    padding: '10px 0 !important',
});
