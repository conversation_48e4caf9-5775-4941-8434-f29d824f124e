import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { IconButton, styled, useTheme } from '@mui/material';
import { BinnacleOutlineIcon } from 'common/components/Icons/BinnacleOutlineIcon';
import { useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectIsOrderExpanded, selectJobsByOrderId } from 'store/slices/ordersJobs/selectors';
import { fetchOrderJobsThunk } from 'store/slices/ordersJobs/thunks';
import theme from 'theme';
import { ordersJobsActions } from '../../../../../store/slices/ordersJobs';

type JobsToggleProps = {
    orderId: string;
    jobsCountInitial: number;
};

export const JobsToggle = ({ orderId, jobsCountInitial }: JobsToggleProps) => {
    const dispatch = useAppDispatch();
    const theme = useTheme();
    const orderJobs = useAppSelector((r) => selectJobsByOrderId(r, orderId));
    const isExpanded = useAppSelector((s) => selectIsOrderExpanded(s, orderId));

    const jobsCount = useMemo(() => {
        if (!orderJobs || orderJobs.isLoading) return jobsCountInitial;

        return orderJobs.jobs.length;
    }, [orderJobs, jobsCountInitial]);

    useEffect(() => {
        if (isExpanded)
            dispatch(
                fetchOrderJobsThunk({
                    orderId: orderId,
                })
            );
    }, [orderId, isExpanded, dispatch]);

    const toggleOrder = () => {
        dispatch(ordersJobsActions.setOrderExpanded({ orderId, isExpanded: !isExpanded }));
    };

    return (
        <ToggleContainer onClick={toggleOrder}>
            <IconContainer>
                <BinnacleOutlineIcon size={32} fill={theme.palette.primary.light} />
                <JobsCountBadge>
                    <JobsCountText>{jobsCount}</JobsCountText>
                </JobsCountBadge>
            </IconContainer>
            <IconButton aria-label="expand row" size="small" disableRipple>
                {isExpanded ? <ExpandMoreIcon /> : <KeyboardArrowRightIcon />}
            </IconButton>
        </ToggleContainer>
    );
};

const ToggleContainer = styled(IconButton)({
    display: 'flex',
});

const IconContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
});

const JobsCountBadge = styled('div')({
    position: 'absolute',
    right: 0,
    top: '3px',
    borderRadius: '50%',
    width: '16px',
    height: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.palette.primary.light,
});

const JobsCountText = styled('span')({
    ...theme.typography.h7Roboto,
    fontWeight: 'bold',
    color: theme.palette.common.white,
});
