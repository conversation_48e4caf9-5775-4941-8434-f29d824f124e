import { Box, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import TeamMemberAPI from 'api/TeamMember';
import { EnterpriseOrderTypesApi } from 'api/enterprise/orders';
import { OrderSimpleSearchItemDto, OrderTypesApi } from 'api/orders';
import { Button } from 'common/components/Button';
import DateFormField from 'common/components/Inputs/DateFormField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import TextField from 'common/components/Inputs/TextField';
import { TeamMemberType } from 'common/constants';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { rgba } from 'common/styles/ColorHelpers';
import { OptionStyle } from 'common/styles/OptionStyle';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { UserShortDto } from 'datacontracts/UserShortDto';
import groupBy from 'lodash/groupBy';
import isEqual from 'lodash/isEqual';
import map from 'lodash/map';
import { DateTime } from 'luxon';
import 'moment/locale/es';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useAsyncMemo } from 'use-async-memo';
import { isDateValid } from 'utils';
import CreateOrderButton from '../CreateOrderButton/CreateOrderButton';
import OrdersAutocomplete from '../OrdersAutocomplete';
import TeamMemberFilterMenu from '../TeamMemberFilterMenuBody';
import { IOrderPageFilters, getDefaultOrderFilters } from '../types';

type OrderTableFiltersProps = {
    filters: IOrderPageFilters;
    onFiltersChange: (filters: IOrderPageFilters) => void;
    counterElement?: React.ReactNode;
};

const OrderTableFilters = ({
    filters: activeFilters,
    onFiltersChange,
    counterElement,
}: OrderTableFiltersProps) => {
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const userPermission = useSelector(selectUserPermission);
    const [repairTypeOptions, setRepairTypeOptions] = useState<OptionData<string>[]>([]);
    const teamMembers = useAsyncMemo<UserShortDto[]>(() => TeamMemberAPI.list(), [], []);
    const [filters, setFilters] = useState<IOrderPageFilters>(activeFilters);

    const filtersChanged = useMemo(() => {
        return !isEqualFilters(activeFilters, filters, true);
    }, [filters, activeFilters]);

    const filtersTouched = useMemo(() => {
        return !isEqualFilters(filters, getDefaultOrderFilters(), false);
    }, [filters]);

    const [showAdditionalFilters, setShowAdditionalFilters] = useState(
        getShowAdditionalFiltersByDefault(filters)
    );
    const settings = useSelector(selectSettings);
    const orderTypeFilterMode = useMemo<OrderTypeFilterMode>(
        () => getOrderTypeFilterMode(settings),
        [settings]
    );

    const selectedOrderTypes = useMemo(() => {
        if (!filters.OTypes) return [];
        return repairTypeOptions.filter((ro) => filters.OTypes?.includes(ro.value));
    }, [repairTypeOptions, filters]);

    const { from, to } = useMemo(() => {
        let from: Date | null, to: Date | null;

        try {
            from = filters.From ? DateTime.fromFormat(filters.From, 'ddMMyy').toJSDate() : null;
        } catch {
            from = null;
        }

        try {
            to = filters.To ? DateTime.fromFormat(filters.To, 'ddMMyy').toJSDate() : null;
        } catch {
            to = null;
        }

        return {
            from,
            to,
        };
    }, [filters.From, filters.To]);
    const setFrom = useCallback((value: Date | null) => {
        setFilters((x) => ({
            ...x,
            From:
                value && isDateValid(value)
                    ? DateTime.fromJSDate(value).toFormat('ddMMyy')
                    : undefined,
        }));
    }, []);
    const setTo = useCallback((value: Date | null) => {
        setFilters((x) => ({
            ...x,
            To:
                value && isDateValid(value)
                    ? DateTime.fromJSDate(value).toFormat('ddMMyy')
                    : undefined,
        }));
    }, []);

    const isEnterprise = useIsEnterpriseRoute();
    const fetchOrderTypes = async () => {
        const response = await callApi(
            () => (isEnterprise ? EnterpriseOrderTypesApi.getFullList() : OrderTypesApi.getList()),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }
        );

        if (response && response.length) {
            if (isEnterprise) {
                setRepairTypeOptions(
                    map(
                        groupBy(response, (g) => g.name),
                        (value, key: string) => {
                            return {
                                label: key,
                                value: map(value, (t) => t.id).join(','),
                            };
                        }
                    )
                );
            } else {
                setRepairTypeOptions(
                    response.map((type) => ({
                        value: type.id + '',
                        label: type.name,
                    }))
                );
            }
        }
    };

    const handleRemoveFilters = () => {
        const defaultFilters = getDefaultOrderFilters();
        setFilters(defaultFilters);
        onFiltersChange(defaultFilters);
    };

    const applyFiltersChange = () => {
        const newFilters = { ...filters, Id: '' };
        setFilters(newFilters);
        onFiltersChange(newFilters);
    };

    const handleAutocompleteSelected = (value: OrderSimpleSearchItemDto | null) => {
        setFilters((x) => ({
            ...x,
            From: undefined,
            To: undefined,

            Id: value?.id || '',
            Ron: value?.number ? value.number : undefined,
            Vin: value?.vehicle?.vin ? value.vehicle.vin : undefined,
            Plts: value?.vehicle?.plates ? value.vehicle.plates : undefined,
            CName: value?.customer
                ? `${value.customer.firstName} ${value.customer.lastName}`
                : undefined,
        }));
    };

    useEffect(() => {
        //This effect should happen only when autocomplete option is selected.
        //It shouldn't fire applying filters when id is cleared, because it happens when "Remove filters" is clicked.
        if (filters.Id) onFiltersChange(filters);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [filters.Id]);

    useEffect(() => {
        fetchOrderTypes();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <DivContainer>
            <Box display="flex" gap={1.5} flexWrap="wrap" gridArea="filters">
                <DivItem>
                    <OrdersAutocomplete
                        type={'number'}
                        placeholder={t('orders.filters.RO#')}
                        onChange={handleAutocompleteSelected}
                        inputValue={filters.Ron}
                        onInputChange={(_e, value, _reason) => {
                            setFilters((x) => ({ ...x, Ron: value === '' ? undefined : value }));
                        }}
                        onInputEnterPress={applyFiltersChange}
                    />
                </DivItem>
                {!showAdditionalFilters ? (
                    <Box onClick={() => setShowAdditionalFilters(true)} sx={{ width: 610 }}>
                        <StyledTextField
                            name="filters"
                            placeholder={t('orders.filters.filter')}
                            type="text"
                            showValidationIndicators={true}
                            isRequired={false}
                            cmosVariant="roundedGrey"
                        />
                    </Box>
                ) : (
                    <>
                        {orderTypeFilterMode === 'DropDown' ? (
                            <DivItem>
                                <Dropdown
                                    multiple
                                    name={'orderType'}
                                    placeholder={t('orders.filters.orderType')}
                                    cmosVariant="roundedGrey"
                                    options={repairTypeOptions}
                                    optionStyle={OptionStyle.checkbox}
                                    value={selectedOrderTypes}
                                    onChange={(options) =>
                                        setFilters((x) => ({
                                            ...x,
                                            OTypes: options.map((o) => o.value),
                                        }))
                                    }
                                    closeMenuOnSelect={false}
                                />
                            </DivItem>
                        ) : orderTypeFilterMode === 'Input' ? (
                            <DivItem>
                                <StyledTextField
                                    name={'orderType'}
                                    placeholder={t('orders.filters.orderType')}
                                    type={'text'}
                                    showValidationIndicators={true}
                                    isRequired={false}
                                    value={filters.AStatus}
                                    onChange={(e) =>
                                        setFilters((x) => ({ ...x, AStatus: e.target.value }))
                                    }
                                    cmosVariant="roundedGrey"
                                    onEnterPress={applyFiltersChange}
                                    enableEnterComplete={true}
                                />
                            </DivItem>
                        ) : undefined}

                        <DivItem>
                            <OrdersAutocomplete
                                type={'vin'}
                                placeholder={t('orders.filters.vin')}
                                onChange={handleAutocompleteSelected}
                                inputValue={filters.Vin}
                                onInputChange={(_e, value, _reason) => {
                                    const vin = value.trim();
                                    setFilters((x) => ({ ...x, Vin: vin ? vin : undefined }));
                                }}
                                transformText={toUppercase}
                                onInputEnterPress={applyFiltersChange}
                            />
                        </DivItem>
                        <DivItem>
                            <OrdersAutocomplete
                                type={'plates'}
                                placeholder={t('orders.filters.plates')}
                                onChange={handleAutocompleteSelected}
                                inputValue={filters.Plts}
                                onInputChange={(_e, value, _reason) => {
                                    setFilters((x) => ({ ...x, Plts: value ? value : undefined }));
                                }}
                                transformText={toUppercase}
                                onInputEnterPress={applyFiltersChange}
                            />
                        </DivItem>
                        <DivItem>
                            <OrdersAutocomplete
                                type={'customer'}
                                placeholder={t('orders.filters.customerName')}
                                onChange={handleAutocompleteSelected}
                                inputValue={filters.CName}
                                onInputChange={(_e, value, _reason) => {
                                    setFilters((x) => ({
                                        ...filters,
                                        CName: value === '' ? undefined : value,
                                    }));
                                }}
                                onInputEnterPress={applyFiltersChange}
                            />
                        </DivItem>
                        {userPermission.allowSeeAllOrders ? (
                            <div>
                                <TeamMemberFilterMenu
                                    filters={filters}
                                    setFilters={setFilters}
                                    teamMembers={teamMembers}
                                />
                            </div>
                        ) : undefined}
                        <DivHideFiltersLabel>
                            <HideFiltersButton
                                cmosVariant={'typography'}
                                onClick={() => setShowAdditionalFilters(false)}
                            >
                                {t('orders.filters.hideFilters')}
                            </HideFiltersButton>
                        </DivHideFiltersLabel>
                    </>
                )}
            </Box>
            {isEnterprise ? null : (
                <Box style={{ display: 'flex', justifyContent: 'end', gridArea: 'create' }}>
                    <Box style={{ width: 125 }}>
                        <CreateOrderButton />
                    </Box>
                </Box>
            )}
            <Grid container style={{ gap: 12, gridArea: 'applyFilters' }}>
                <Grid item sx={{ width: 195 }}>
                    <DateFormField
                        variant="rounded"
                        clearable
                        format={`'${t('orders.filters.start')}': {date}`}
                        name="from"
                        enableTextInput
                        value={from}
                        onChange={setFrom}
                        onEnterPress={applyFiltersChange}
                        enableEnterComplete
                        onBlur={applyFiltersChange}
                    />
                </Grid>
                <Grid item sx={{ width: 195 }}>
                    <DateFormField
                        variant="rounded"
                        clearable
                        format={`'${t('orders.filters.end')}': {date}`}
                        name="to"
                        enableTextInput
                        value={to}
                        onChange={setTo}
                        onEnterPress={applyFiltersChange}
                        enableEnterComplete={true}
                        onBlur={applyFiltersChange}
                    />
                </Grid>
                <Grid item sx={{ width: 195 }}>
                    <Button
                        blockMode
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        label={t('orders.filters.search')}
                        onClick={applyFiltersChange}
                        disabled={!filtersChanged}
                    />
                </Grid>
                <Grid item sx={{ width: 125 }}>
                    <StyledTypographyButton
                        blockMode
                        cmosVariant={'typography'}
                        cmosSize={'medium'}
                        label={t('orders.filters.newSearch')}
                        onClick={handleRemoveFilters}
                        disabled={!filtersTouched}
                    />
                </Grid>
            </Grid>
            <div style={{ gridArea: 'counters' }}>{counterElement}</div>
        </DivContainer>
    );
};

const toUppercase = (v: string) => v.toUpperCase();

function isEqualFilters(
    a: IOrderPageFilters,
    b: IOrderPageFilters,
    trueForChangedFalseForTouched: boolean
): boolean {
    const allowedProps: (keyof IOrderPageFilters)[] = [
        'Ron',
        'AStatus',
        'OTypes',
        'Vin',
        'Plts',
        'CName',
        'From',
        'To',
        'Phas',
        'Id',
    ];

    // check the 'TmIds', 'TmTyp' filters only when changes are made in corresponding filter fields
    // need to skip the case when the TeamMember field was just opened and closed
    // also skip the case when we just change radio button, but there are no selected team members
    if (trueForChangedFalseForTouched) {
        if (
            a.TmIds !== undefined ||
            a.TmTyp !== undefined ||
            !(b.TmIds instanceof Array) ||
            b.TmIds.length > 0 ||
            b.TmTyp === undefined
        ) {
            allowedProps.push('TmIds', 'TmTyp');
        }
    }
    // same for filters touching
    else if (
        !(a.TmIds instanceof Array) ||
        a.TmIds.length > 0 ||
        a.TmTyp !== TeamMemberType.AssignedTo ||
        b.TmIds !== undefined ||
        b.TmTyp !== undefined
    ) {
        allowedProps.push('TmIds', 'TmTyp');
    }

    function normalize(filters: IOrderPageFilters): IOrderPageFilters {
        return Object.fromEntries(
            Object.entries(filters).filter(([key, value]) => {
                if (!allowedProps.includes(key as keyof IOrderPageFilters)) return false;
                if (value === undefined) return false;
                if (value instanceof Array && value.length === 0) return false;
                return true;
            })
        );
    }

    a = normalize(a);
    b = normalize(b);
    return isEqual(a, b);
}

const DivContainer = styled('div')(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: 'auto auto',
    gridTemplateAreas: '"filters create" "applyFilters counters"',
    gap: '10px',
    [theme.breakpoints.down('md')]: {
        gridTemplateColumns: 'auto',
        gridTemplateRows: 'auto auto auto auto',
        gridTemplateAreas: '"create" "filters" "applyFilters" "counters"',
    },
}));

const DivHideFiltersLabel = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    color: 'var(--cm2)',
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    textAlign: 'right',
    cursor: 'pointer',
}));

const StyledTypographyButton = styled(Button)(({ theme }) => ({
    '&:hover': {
        backgroundColor: rgba(theme.palette.primary.light, 0.2),
    },
}));

const HideFiltersButton = styled(StyledTypographyButton)({
    width: 125,
});

const DivItem = styled('div')({
    width: 195,
});

const StyledTextField = styled(TextField)(({ theme }) => ({
    '& .MuiInputBase-input': {
        '&::placeholder': {
            color: 'var(--neutral6)',
            opacity: 1,
        },
    },
}));

export default OrderTableFilters;

export type OrderTypeFilterMode = 'DropDown' | 'Input' | 'Nothing';

const getShowAdditionalFiltersByDefault = (filters: IOrderPageFilters) =>
    !!filters.AStatus ||
    (!!filters.OTypes && !!filters.OTypes.length) ||
    !!filters.Vin ||
    !!filters.Plts ||
    !!filters.CName ||
    !!filters.TmTyp;

const getOrderTypeFilterMode = (settings: GlobalSettingsDto): OrderTypeFilterMode => {
    const isEnterprise = settings.appMode === 'Enterprise';
    if (isEnterprise) {
        try {
            const roTypeEnabled = settings.enterpriseSettings!.shops.some((r) => r.enableOrderType);
            if (roTypeEnabled) {
                return 'DropDown';
            }
        } catch {}
        return 'Nothing';
    } else {
        if (settings.repairShopSettings?.features.orderType) {
            return 'DropDown';
        } else {
            return 'Input';
        }
    }
};
