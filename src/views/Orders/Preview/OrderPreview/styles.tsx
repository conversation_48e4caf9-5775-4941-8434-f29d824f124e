import { makeStyles } from '@mui/styles';
import { Colors } from '../../../../common/styles/Colors';
import { FontPrimary, FontSecondary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';

export const useOrderPreviewStyles = makeStyles((theme) => ({
    mainContainer: {
        width: '428px',
        height: '100%',
        backgroundColor: Colors.White,
        boxShadow: '0 8px 20px 0 rgba(99, 104, 110, 0.25)',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
    },
    contextualMenuIcon: {
        right: 26,
        position: 'absolute',
    },
    marginTop: {
        marginTop: 9,
    },

    carInfo: {
        padding: '24px 0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.Neutral2,
        width: '100%',
        '& .column': {
            paddingLeft: 24,
            '& .row': {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start',
            },
        },
    },
    customerName: {
        ...FontPrimary(HeaderStyles.H4_18px, true, Colors.Black),
        minHeight: 18,
        textAlign: 'center',
    },
    customerCar: {
        ...FontPrimary(HeaderStyles.H5_14px, false, Colors.Black),
        minHeight: '17px!important',
        minWidth: 10,
        letterSpacing: 'normal',
        textAlign: 'center',
    },
    mediaNumber: {
        ...FontPrimary(HeaderStyles.H5_14px, false, Colors.Neutral6),
        margin: '0 7.3px 0 7.3px',
        textAlign: 'center',
    },
    inspectionFormStatus: {
        ...FontPrimary(HeaderStyles.H5_14px, false, Colors.Neutral6),
        margin: '8px 0px 0 0px',
        textAlign: 'center',
    },
    inspectionFormSent: {
        ...FontPrimary(HeaderStyles.H7_11px, false, Colors.Neutral6),
        margin: '8px 0px 0 0px',
        textAlign: 'center',
    },
    buttonContainer: {
        display: 'flex',
        columnGap: 32,
    },
    button: {
        width: 165,
        margin: '13px 0 0 0!important',
    },
    commentIcon: {
        paddingRight: 10,
    },
    expandMoreIcon: {
        width: 17.2,
        height: 12.3,
        margin: '4px 0 3.7px 39.4px',
    },

    collapsable: {
        paddingBottom: 72,
    },
    callLog: {
        right: '445px',
        bottom: '90px',
    },
    callCustomerButton: {
        backgroundColor: '#0069FF',
        '&:hover': {
            backgroundColor: '#3b50d5',
        },
    },
    tooltipContainer: {
        display: 'flex',
        flexDirection: 'column',
        padding: '8px 40px',
    },
    status: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
    },
    updateTime: {
        ...FontSecondary(HeaderStyles.H7_11px, false, Colors.Neutral7),
    },
}));
