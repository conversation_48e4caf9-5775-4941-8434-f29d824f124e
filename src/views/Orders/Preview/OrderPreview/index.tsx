import Grid from '@mui/material/Grid';
import Modal from '@mui/material/Modal';
//import styles from "./styles.module.css";
import { PhoneCallLogic } from 'business/PhoneCallLogic';
import { Button } from 'common/components/Button';
import { ImageIcon } from 'common/components/Icons/ImageIcon';
import { motion } from 'framer-motion';
import { useMemo, useState } from 'react';
import { generatePath, useNavigate, useParams } from 'react-router-dom';
import { selectSettings } from 'store/slices/globalSettingsSlice';
/** ICON INPORT */
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { VideoIcon } from 'common/components/Icons/VideoIcon';
/** COMPONENTS */
import { IconButton, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { GetTimePassed } from 'common/DateTimeHelpers';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { LastComIcon } from 'common/components/Icons/LastComIcon';
import SendSmsModalBody from 'common/components/SendSmsModalBody';
import Tooltip from 'common/components/Tooltip';
import { ENTERPRISE_ROUTES, OrderStatusLabel, ROUTES } from 'common/constants';
import { useAppRoutes } from 'common/hooks/useAppRoutes';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useLockedBody from 'common/hooks/useLockedBody';
import { Colors } from 'common/styles/Colors';
import { SingleLineText } from 'common/styles/TextHelpers';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useSelector } from 'react-redux';
import { useAppSelector } from 'store';
import { selectConversations } from 'store/slices/conversations/selectors';
import { selectConversations as selectEnterpriseConversations } from 'store/slices/enterprise/conversationsSlice/selectors';
import { replaceUrl } from 'utils';
import TowerNumber from 'views/WorkshopPlanner/common/TowerNumber';
import CallLog from '../../../CallLog';
import CallCustomer from '../../../Components/CallCustomer';
import OrderPreviewDetails from '../../../Components/OrderPreviewDetails';
import SendMessage from '../../../Components/SendMessage';
import { IOrderPreviewProps } from './IOrderPreviewProps';
import { useOrderPreviewStyles } from './styles';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';

const HeaderContainer = styled('div')(({ theme }) => ({
    width: '100%',
    height: 52,
    paddingLeft: 24,
    backgroundColor: theme.palette.neutral[8],
    color: theme.palette.neutral[1],
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
}));

const DivHeaderInner = styled('div')({
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    maxWidth: 356,
});

const OrderPreview = ({ orderFromList, onClose }: IOrderPreviewProps) => {
    const { RO: ROStr } = useParams<{ RO: string }>();
    const isEnterprise = useIsEnterpriseRoute();
    const RO = +(ROStr ?? '');
    const styles = useOrderPreviewStyles();
    const { t } = useAppTranslation();
    const { appRoutes } = useAppRoutes();
    const { repairShopSettings, enterpriseSettings } = useAppSelector(selectSettings);
    /*const [anchorElSms, setAnchorElSms] = useState(null);*/
    const [sendSMSModal, setSendSMSModal] = useState(false);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [sendSMSModalTitle, setSendSMSModalTitle] = useState('communication.sendSms');
    const [sendSMSModalDesc /*, setSendSMSModalDesc*/] = useState(
        'communication.sendSmsWithNoteDesc'
    );

    const { data: phoneCallsData, refetch: fetchPhoneCalls } = useQuery(
        ['order', orderFromList?.repairOrderId ?? RO, 'phone-calls'],
        () => PhoneCallLogic.list(orderFromList?.repairOrderId ?? RO),
        {
            cacheTime: 3600000,
            staleTime: 500,
        }
    );
    const phoneCalls = phoneCallsData ?? [];
    const { data: order } = useQuery(
        ['order', orderFromList?.repairOrderId ?? RO, 'preview'],
        () => OrderAPI.preview(orderFromList?.repairOrderId ?? RO),
        {
            cacheTime: 3600000,
            staleTime: 500,
        }
    );

    const [isCallLogOpened, setIsCallLogOpened] = useState(false);
    const conversations = useSelector(selectConversations);
    const enterpriseConversations = useSelector(selectEnterpriseConversations);
    const { internationalization } = useSelector(selectSettings);

    useLockedBody(true);

    const navigate = useNavigate();

    const seeOrderDetails = () => {
        navigate(replaceUrl(appRoutes.ORDERS_DETAIL, { id: orderFromList.repairOrderId }));
    };

    const getCustomerName = () => {
        const firstName = order?.firstName ?? '';
        const lastName = order?.lastName ?? '';
        let name = firstName;
        if (firstName.length || lastName.length) name += ' ';
        name += lastName;
        if (name.length > 25) name = name.substring(0, 25) + '…';
        return name;
    };

    const customerName = getCustomerName();

    const handleViewConversationClick = () => {
        const phoneNumber = internationalization.countryPhoneCode + order?.mobilePhone;

        const conversationId = isEnterprise
            ? enterpriseConversations.find((c) => c.customerPhoneNumber === phoneNumber)
                  ?.conversationId
            : conversations.find((c) => c.customerPhoneNumber === phoneNumber)?.conversationId;

        const route = isEnterprise ? ENTERPRISE_ROUTES.CONVERSATIONS : ROUTES.CONVERSATIONS;

        navigate(generatePath(route, { conversationId }));
    };

    const sendSettings = useMemo(() => {
        if (order) {
            if (repairShopSettings) {
                return {
                    sendWhatsAppToCustomer: repairShopSettings.features.sendWhatsappToConsumer,
                    sendSmsToCustomer: repairShopSettings.features.sms,
                    enablePhoneCalls: repairShopSettings.features.phoneCalls,
                };
            } else if (enterpriseSettings) {
                const shop = enterpriseSettings.shops.find((s) => s.id === order.repairShopId);
                if (shop) {
                    return {
                        sendWhatsAppToCustomer: shop.sendWhatsAppToCustomer,
                        sendSmsToCustomer: shop.sendSmsToCustomer,
                        enablePhoneCalls: shop.enablePhoneCalls,
                    };
                }
            }
        }

        return {
            sendWhatsAppToCustomer: false,
            sendSmsToCustomer: false,
            enablePhoneCalls: false,
        };
    }, [order, repairShopSettings, enterpriseSettings]);

    return (
        <>
            <DivOverlayContainer onClick={() => (onClose ? onClose() : undefined)}>
                <motion.div
                    initial={{ x: 430 }}
                    animate={{ x: 0 }}
                    transition={{ type: 'easeInOut' }}
                    className={styles.mainContainer}
                    onClick={(event) => event.stopPropagation()}
                >
                    {order && (
                        <HeaderContainer>
                            <DivHeaderInner>
                                <SpanHeaderText style={{ flexGrow: 2 }}>
                                    {t('orderDetails.RO#')}
                                    {order?.repairOrderNumber}
                                </SpanHeaderText>
                                {order.towerNumber.trim() && (
                                    <>
                                        <Hyphen>-</Hyphen>
                                        <TowerNumber
                                            towerNumber={order?.towerNumber ?? ' '}
                                            orderTypeKey={order.orderType?.id}
                                            userIdOrKey={order.serviceAdvisor?.key}
                                            appointmentReasonColor={order.appointmentReasonColor}
                                            style={{ minWidth: 30 }}
                                        />
                                    </>
                                )}
                                <Hyphen>-</Hyphen>
                                <SpanHeaderText style={{ flexGrow: 1 }}>
                                    {order?.phaseName === 'noPhase' ||
                                    order?.phaseName === 'closedOrder'
                                        ? t(`orderDetails.${order?.phaseName}`)
                                        : order?.phaseName}
                                </SpanHeaderText>
                            </DivHeaderInner>
                            <IconButton
                                onClick={() => (onClose ? onClose() : undefined)}
                                size="large"
                                sx={{ color: 'inherit', opacity: 0.75 }}
                            >
                                <CloseIcon fill="currentColor" />
                            </IconButton>
                        </HeaderContainer>
                    )}

                    <DivDetailsContainer>
                        <OverlayScrollbarsComponent>
                            <div className={styles.carInfo}>
                                <Grid
                                    container
                                    justifyContent="center"
                                    alignItems="center"
                                    alignContent="center"
                                >
                                    <Grid item xs={12}>
                                        <Grid container spacing={1} justifyContent="center">
                                            <Grid item xs="auto">
                                                <span className={styles.customerName}>
                                                    {customerName}
                                                </span>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                    <Grid item xs={12} style={{ marginTop: 1 }}>
                                        <Grid container spacing={1} justifyContent="center">
                                            <Grid item xs="auto">
                                                <div className={styles.customerCar}>{`${
                                                    order?.make ?? ''
                                                } ${order?.model ?? ''} ${order?.year ?? ''}${
                                                    order?.plates ? `, ${order.plates}` : ''
                                                }`}</div>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                    <Grid
                                        item
                                        xs={12}
                                        style={{
                                            marginTop: 13,
                                            marginBottom: 7,
                                            height: 24,
                                        }}
                                    >
                                        <Grid container spacing={1} justifyContent="center">
                                            <Grid item xs={'auto'}>
                                                <Grid
                                                    container
                                                    spacing={0}
                                                    justifyContent="center"
                                                    alignItems="center"
                                                >
                                                    <VideoIcon fill={Colors.Neutral6} />
                                                    <span className={styles.mediaNumber}>
                                                        {order?.videoCount}
                                                    </span>
                                                </Grid>
                                            </Grid>
                                            <Grid item xs={'auto'}>
                                                <Grid
                                                    container
                                                    spacing={0}
                                                    justifyContent="center"
                                                    alignItems="center"
                                                >
                                                    <ImageIcon fill={Colors.Neutral6} />
                                                    <span className={styles.mediaNumber}>
                                                        {order?.photoCount}
                                                    </span>
                                                </Grid>
                                            </Grid>
                                            {(repairShopSettings?.features.phoneCalls ||
                                                (!repairShopSettings?.features.phoneCalls &&
                                                    order &&
                                                    order.phoneCallsCount > 0)) && (
                                                <Grid item xs={'auto'}>
                                                    <Grid
                                                        container
                                                        spacing={0}
                                                        justifyContent="center"
                                                        alignItems="center"
                                                    >
                                                        <PhoneIcon fill={Colors.Neutral6} />
                                                        <span className={styles.mediaNumber}>
                                                            {order?.phoneCallsCount}
                                                        </span>
                                                    </Grid>
                                                </Grid>
                                            )}
                                            <Grid item xs={'auto'}>
                                                <Grid
                                                    container
                                                    spacing={0}
                                                    justifyContent="center"
                                                    alignItems="center"
                                                >
                                                    <ShowIcon fill={Colors.Neutral6} />
                                                    <span className={styles.mediaNumber}>
                                                        {order?.consumerViews}
                                                    </span>
                                                </Grid>
                                            </Grid>
                                            <Grid item xs={'auto'}>
                                                <Grid
                                                    container
                                                    spacing={0}
                                                    justifyContent="center"
                                                    alignItems="center"
                                                >
                                                    <Tooltip
                                                        position="left"
                                                        content={
                                                            <div
                                                                className={styles.tooltipContainer}
                                                            >
                                                                <span className={styles.status}>
                                                                    {order &&
                                                                    order.inspectionFormStatus
                                                                        ? t(
                                                                              OrderStatusLabel(
                                                                                  order.inspectionFormStatus
                                                                              )
                                                                          )
                                                                        : ''}
                                                                </span>
                                                                <span className={styles.updateTime}>
                                                                    {order?.lastCommunicationTime &&
                                                                        GetTimePassed(
                                                                            order.lastCommunicationTime,
                                                                            t
                                                                        )}
                                                                </span>
                                                            </div>
                                                        }
                                                    >
                                                        <LastComIcon fill={Colors.Neutral6} />
                                                    </Tooltip>
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Grid container justifyContent="center">
                                            <div className={styles.buttonContainer}>
                                                <div className={styles.button}>
                                                    <Button
                                                        blockMode
                                                        cmosVariant={'stroke'}
                                                        color={Colors.CM1}
                                                        label={t('orders.preview.seeOrderDetail')}
                                                        onClick={() => seeOrderDetails()}
                                                    />
                                                </div>
                                                <div className={styles.button}>
                                                    <Button
                                                        blockMode
                                                        cmosVariant={'stroke'}
                                                        color={Colors.CM1}
                                                        label={t('orders.preview.viewConversation')}
                                                        onClick={handleViewConversationClick}
                                                    />
                                                </div>
                                            </div>
                                        </Grid>
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Grid container justifyContent="center">
                                            <div
                                                className={styles.buttonContainer}
                                                style={{
                                                    paddingTop: 10,
                                                }}
                                            >
                                                {sendSettings.enablePhoneCalls && (
                                                    <CallCustomer
                                                        canCallLandline={!!order?.landlinePhone}
                                                        canCallMobile={!!order?.mobilePhone}
                                                        repairOrderId={
                                                            orderFromList?.repairOrderId ?? RO
                                                        }
                                                        viewCallLogEnabled={!!phoneCalls.length}
                                                        onViewCallLog={() =>
                                                            setIsCallLogOpened(true)
                                                        }
                                                        onCall={fetchPhoneCalls}
                                                    />
                                                )}
                                                <SendMessage
                                                    canSendEmail={!!order?.email}
                                                    canSendWhatsApp={
                                                        !!order?.mobilePhone &&
                                                        sendSettings.sendWhatsAppToCustomer
                                                    }
                                                    canSendSms={
                                                        !!order?.mobilePhone &&
                                                        sendSettings.sendSmsToCustomer
                                                    }
                                                    repairOrderId={
                                                        orderFromList?.repairOrderId ?? RO
                                                    }
                                                    email={order?.email ?? ''}
                                                    number={order?.mobilePhone ?? ''}
                                                    inspectionLink={order?.inspectionLink ?? ''}
                                                />
                                            </div>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </div>

                            <div className={styles.collapsable}>
                                <OrderPreviewDetails order={order} />
                            </div>
                        </OverlayScrollbarsComponent>
                    </DivDetailsContainer>

                    <div>
                        {
                            <Modal
                                open={sendSMSModal}
                                onClose={() => setSendSMSModal(false)}
                                children={
                                    <SendSmsModalBody
                                        title={sendSMSModalTitle}
                                        desc={sendSMSModalDesc}
                                        onClose={() => setSendSMSModal(false)}
                                    />
                                }
                            />
                        }
                    </div>
                </motion.div>
            </DivOverlayContainer>
            <CallLog
                open={isCallLogOpened}
                onClose={() => setIsCallLogOpened(false)}
                phoneCalls={phoneCalls}
                className={styles.callLog}
            />
        </>
    );
};

export default OrderPreview;

const DivDetailsContainer = styled('div')({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    overflowY: 'auto',
    overflowX: 'hidden',
});

const DivOverlayContainer = styled('div')({
    width: '100%',
    height: '100vh',
    backgroundColor: 'rgba(0,0,0,0.36)',
    position: 'fixed',
    top: 0,
    zIndex: 7,
    display: 'flex',
    justifyContent: 'flex-end',
});

const SpanHeaderText = styled('span')(({ theme }) => ({
    ...theme.typography.h4Inter,
    ...SingleLineText(),
    textAlign: 'left',
}));

const Hyphen = styled('span')(({ theme }) => ({
    ...theme.typography.h4Inter,
    textAlign: 'left',
    margin: '0 5px',
}));
