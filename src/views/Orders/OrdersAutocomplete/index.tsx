import DescriptionIcon from '@mui/icons-material/Description';
import {
    Autocomplete,
    AutocompleteInputChangeReason,
    Box,
    ListItem,
    Paper,
    styled,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import OrdersApi, { OrderSimpleSearchItemDto } from 'api/orders';
import { TextField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Interweave } from 'interweave';
import { DateTime } from 'luxon';
import { SyntheticEvent, forwardRef, useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectIanaTz } from 'store/slices/globalSettingsSlice';
import { useDebounce } from 'use-debounce';
import { getPhaseName } from 'views/OrderDetail/util';

type OrdersAutocompleteProps = {
    type: string;
    placeholder: string;
    inputValue: string | undefined;
    onChange: (value: OrderSimpleSearchItemDto | null) => void;
    onInputChange: (
        event: React.SyntheticEvent,
        value: string,
        reason: AutocompleteInputChangeReason
    ) => void;
    onInputEnterPress: React.KeyboardEventHandler;
    transformText?: (text: string) => string;
};

const noTransform = (v: string) => v;

const searchLimit = 100;

export default function OrdersAutocomplete({
    type,
    placeholder,
    inputValue,
    onInputChange,
    onInputEnterPress,
    onChange,
    transformText = noTransform,
}: OrdersAutocompleteProps) {
    const { t } = useAppTranslation();
    const [open, setOpen] = useState(false);
    const [search, setSearch] = useState(inputValue);
    const [debouncedSearch] = useDebounce(search, 300);

    const isEnterprise = useIsEnterpriseRoute();
    const queryKey = isEnterprise
        ? ['enterprise', 'orders', 'search', debouncedSearch]
        : ['orders', 'search', debouncedSearch];
    const queryFn = isEnterprise
        ? () => EnterpriseOrdersApi.orders.search(type, debouncedSearch!, searchLimit)
        : () => OrdersApi.search(type, debouncedSearch!, searchLimit);

    const { data } = useQuery(queryKey, queryFn, {
        enabled: debouncedSearch !== '' && debouncedSearch !== undefined,
        keepPreviousData: false,
    });

    useEffect(() => {
        if (!inputValue) setSearch(inputValue);
    }, [inputValue]);

    const options = useMemo(() => {
        if (!data) return [];
        return data;
    }, [data]);

    const handleInputChange = (
        event: SyntheticEvent<Element, Event>,
        newInputValue: string,
        reason: AutocompleteInputChangeReason
    ) => {
        if (reason === 'reset') return;

        newInputValue = transformText(newInputValue);

        onInputChange(event, newInputValue, reason);
        setSearch(newInputValue);
        setOpen(newInputValue.length > 0);
    };

    const handleOnFocus = () => {
        if (search && search.length > 0) {
            setOpen(true);
        } else {
            setOpen(false);
        }
    };

    const handleValueChange = (
        _e: SyntheticEvent<Element, Event>,
        value: OrderSimpleSearchItemDto | null
    ) => {
        onChange(value);
        if (type === 'number') setSearch(value?.number ?? '');
        if (type === 'vin') setSearch(value?.vehicle.vin ?? '');
        if (type === 'plates') setSearch(value?.vehicle.plates ?? '');
        if (type === 'customer')
            setSearch(`${value?.customer.firstName} ${value?.customer.lastName}`);
        setOpen(false);
    };

    return (
        <Autocomplete
            noOptionsText={t('orders.searchAutocomplete.noOptions')}
            disablePortal
            clearOnBlur={false}
            options={options}
            inputValue={(open ? search : inputValue) ?? ''}
            renderInput={(params) => {
                return (
                    <StyledTextField
                        {...params}
                        placeholder={placeholder}
                        showValidationIndicators={true}
                        isRequired={false}
                        cmosVariant="roundedGrey"
                        onEnterPress={onInputEnterPress}
                        enableEnterComplete={true}
                    />
                );
            }}
            onInputChange={handleInputChange}
            onChange={handleValueChange}
            open={open}
            onFocus={handleOnFocus}
            onBlur={() => setOpen(false)}
            forcePopupIcon={false}
            getOptionLabel={(option) => {
                return option.number;
            }}
            renderOption={(props, o) => (
                <ListItem {...props}>
                    <OrderItem order={o} type={type} inputValue={inputValue} />
                </ListItem>
            )}
            PaperComponent={PaperComponent}
            filterOptions={(options) => options}
            isOptionEqualToValue={(option, value) => option.id === value.id}
        />
    );
}

type OrderItemProps = {
    order: OrderSimpleSearchItemDto;
    type: string;
    inputValue: string | undefined;
};

const Title = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[8],
}));

const Light = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[6],
}));

function selectionText(defaultValue: string, inputValue: string): string {
    const normalizedInputValue = inputValue.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    const regexp = new RegExp(normalizedInputValue, 'i');

    const startIndex = defaultValue.search(regexp);
    const lastIndex = startIndex + inputValue.length;

    return (
        defaultValue.slice(0, startIndex) +
        '<b>' +
        defaultValue.slice(startIndex, lastIndex) +
        '</b>' +
        defaultValue.slice(lastIndex)
    );
}

function formatOrderDate(createdAt: string, tzName: string): string {
    const date = DateTime.fromISO(createdAt, { zone: 'utc' }).setZone(tzName);
    const now = DateTime.now().setZone(tzName);

    if (date.hasSame(now, 'day')) {
        return date.toFormat('HH:mm');
    } else if (date.hasSame(now, 'year')) {
        return date.toFormat('d MMM');
    } else {
        return date.toFormat('dd/MM/yy');
    }
}

function OrderItem({ order, type, inputValue }: OrderItemProps) {
    const { t } = useAppTranslation();
    const tzName = useAppSelector(selectIanaTz);

    const formattedDate = useMemo(() => {
        if (!order.createdAt) return '';
        return formatOrderDate(order.createdAt, tzName);
    }, [order.createdAt, tzName]);

    const selectedText =
        inputValue &&
        (type === 'number'
            ? selectionText(order.number, inputValue)
            : type === 'vin'
            ? selectionText(order.vehicle.vin, inputValue)
            : type === 'plates'
            ? selectionText(order.vehicle.plates, inputValue)
            : type === 'customer'
            ? selectionText(order.customer.firstName + ' ' + order.customer.lastName, inputValue)
            : '');

    const selectedHtmlText = <Interweave content={selectedText ?? ''} />;

    return (
        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', width: '100%', flexBasis: '85%' }}>
                <Box sx={{ display: 'flex', alignContent: 'center', flexWrap: 'wrap' }}>
                    <DescriptionIcon color="primary" fontSize="small" />
                </Box>

                <Box padding={'10px 5px'}>
                    <Title>
                        {t('orders.searchAutocomplete.RO#')}
                        {': '}
                        {type === 'number' ? selectedHtmlText : order.number}
                    </Title>
                    {(order.customer.firstName || order.customer.lastName) && (
                        <Title>
                            {' / '}
                            {t('orders.searchAutocomplete.customer')}:{' '}
                            {type === 'customer'
                                ? selectedHtmlText
                                : order.customer.firstName + ' ' + order.customer.lastName}
                        </Title>
                    )}
                    {order.vehicle.plates && (
                        <>
                            <Title>
                                {' / '}
                                {t('orders.searchAutocomplete.plates')}:
                            </Title>{' '}
                            {type === 'plates' ? selectedHtmlText : order.vehicle.plates}
                        </>
                    )}
                    {order.vehicle.vin && (
                        <>
                            <Title>
                                {' / '}
                                {t('orders.searchAutocomplete.vin')}:
                            </Title>{' '}
                            {type === 'vin' ? selectedHtmlText : order.vehicle.vin}
                        </>
                    )}
                    {order.phase.name && (
                        <>
                            <Title>
                                {' / '}
                                {t('orders.searchAutocomplete.phase')}:
                            </Title>{' '}
                            <Light>{getPhaseName(order.phase, t)}</Light>
                        </>
                    )}
                </Box>
            </Box>
            <Box sx={{ paddingTop: '10px' }}>
                {order.createdAt && <Title>{formattedDate}</Title>}
            </Box>
        </Box>
    );
}

const PaperComponent = forwardRef(
    ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>, _ref) => {
        const { t } = useAppTranslation();

        return (
            <Paper {...props} sx={{ width: 550 }}>
                <HeaderItem>
                    <SelectOrderLabel>{t('orders.searchAutocomplete.orders')}</SelectOrderLabel>
                </HeaderItem>

                {children}
            </Paper>
        );
    }
);

const HeaderItem = styled(ListItem)({
    flexDirection: 'column',
    justifyContent: 'start',
    alignItems: 'start',
    paddingBottom: 0,
});

const SelectOrderLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
    marginTop: 5,
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
    '& .MuiOutlinedInput-root .MuiAutocomplete-input': {
        padding: '0 0 0 5px',
        '&::placeholder': {
            color: 'var(--neutral6)',
            opacity: 1,
        },
    },
}));
