import { useMutation } from '@tanstack/react-query';
import OrderAPI, { CreateEmptyOrderResponse } from 'api/Order';
import axios from 'axios';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback } from 'react';
import NewOrderDataPopup, { NewOrderData } from './NewOrderDataPopup';

type NewOrderPopupProps = {
    onOrderCreated: (response: CreateEmptyOrderResponse, variables: string) => void;
    onClose: () => void;
    open: boolean;
};

export default function NewOrderPopup({ open, onOrderCreated, onClose }: NewOrderPopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const createOrderMutation = useMutation((number: string) => OrderAPI.createEmpty(number), {
        onSuccess(data, variables, context) {
            onOrderCreated(data, variables);
            onClose();
        },

        onError(error, _orderNumber, _context) {
            if (axios.isAxiosError(error)) {
                if (error.response?.status === 409) {
                    // order number is taken
                    toasters.danger(
                        t('appointments.repairOrderExists'),
                        t('toasters.errorOccurredWhenSaving')
                    );
                }
            }
        },
    });

    const onOrderData = useCallback(
        async (data: NewOrderData) => {
            await createOrderMutation.mutateAsync(data.number);
        },
        [createOrderMutation]
    );

    return (
        <NewOrderDataPopup
            open={open}
            loading={createOrderMutation.isLoading}
            onClose={onClose}
            onOrderData={onOrderData}
        />
    );
}
