import { Box, Grid } from '@mui/material';
import { AppointmentConvertedResponse } from 'api/Appointment';
import { Button } from 'common/components/Button';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';
import useNewOrderPopupStyles from './css';

export type NewOrderData = {
    number: string;
};

type NewOrderDataPopupProps = {
    onOrderData: (
        data: NewOrderData
    ) => Promise<void> | Promise<AppointmentConvertedResponse | undefined>;
    onClose: () => void;
    open: boolean;
    loading?: boolean;
};

export default function NewOrderDataPopup({
    open,
    onOrderData,
    onClose,
    loading,
}: NewOrderDataPopupProps) {
    const [number, setNumber] = useState('');
    const { t } = useAppTranslation();
    const styles = useNewOrderPopupStyles();
    const [isInvalid, setIsInvalid] = useState(false);
    const [showHeplerText, setShowHeplerText] = useState(false);
    const [showNumberLimitWarning, setShowNumberLimitWarning] = useState(false);
    const numberLimit = 50;

    const valid = number.trim() !== '';

    const submit = () => {
        if (number.length <= numberLimit) {
            onOrderData({ number }).catch((err) => {
                setIsInvalid(true);
                if (err.response?.status === 409) setShowHeplerText(true);
            });
        } else {
            setShowNumberLimitWarning(true);
        }
    };

    const clearHelperText = () => {
        setIsInvalid(false);
        setShowHeplerText(false);
    };

    useEffect(() => {
        if (!open) {
            setNumber('');
            setIsInvalid(false);
            setShowHeplerText(false);
        }
    }, [open]);

    return (
        <Modal open={open}>
            <div className={styles.root}>
                <Grid container className={styles.headerContainer}>
                    <Box display="flex" width="100%" justifyContent="space-between">
                        <h4 className={styles.header}>{t('newOrder.title')}</h4>
                        <Box display="flex" gap={3}>
                            <Button
                                className={styles.button}
                                onClick={useCallback(() => onClose(), [onClose])}
                                label={t('commonLabels.cancel')}
                                cmosVariant={'filled'}
                                color={Colors.Neutral3}
                            />

                            <Button
                                className={styles.button}
                                showLoader={loading}
                                disabled={loading || !valid}
                                onClick={submit}
                                label={t('newOrder.createOrder')}
                                cmosVariant={'filled'}
                                color={Colors.CM1}
                            />
                        </Box>
                    </Box>
                </Grid>
                <Grid container>
                    <Grid md={6} item>
                        <TextFormField
                            error={isInvalid}
                            isRequired
                            showValidationIndicators
                            name="order-number"
                            label={t('newOrder.number')}
                            placeholder={t('newOrder.numberPlaceholder')}
                            value={number}
                            maxLength={numberLimit}
                            isInvalid={isInvalid}
                            helperText={showHeplerText && t('appointments.repairOrderExists')}
                            inputProps={{ style: { height: '32px' } }}
                            onChange={(e) =>
                                setNumber(e.target.value.replace(/[\s]/g, '').toUpperCase())
                            }
                            onClick={() => {
                                setShowHeplerText(false);
                                clearHelperText();
                            }}
                            FormHelperTextProps={{
                                sx: {
                                    marginLeft: '0',
                                },
                            }}
                        />
                    </Grid>
                </Grid>
            </div>
            <WarningConfirmationPopup
                open={showNumberLimitWarning}
                title={t('newOrder.numberLimitWarning.title')}
                body={<Box>{t('newOrder.numberLimitWarning.body')}</Box>}
                confirm={t('commonLabels.tryAgain')}
                onConfirm={() => {
                    setShowNumberLimitWarning(false);
                }}
                onClose={() => {
                    setShowNumberLimitWarning(false);
                }}
            />
        </Modal>
    );
}
