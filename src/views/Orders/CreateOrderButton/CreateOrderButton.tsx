import { Box, styled, Typography } from '@mui/material';
import OrderAPI, { CreateEmptyOrderResponse } from 'api/Order';
import { ActivityLogLogic } from 'business/ActivityLogLogic';
import { Button } from 'common/components/Button';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import Tooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { generatePath, useNavigate } from 'react-router-dom';
import { createSelector } from 'reselect';
import {
    selectRepairShopIntegrationAccountName,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission, useCurrentUser } from 'store/slices/user/selectors';
import nr from 'utils/nr';
import NewOrderPopup from '../NewOrderPopup';

const autoRepairOrderNumberSelector = createSelector(
    selectSettings,
    (s) => s.repairShopSettings?.features.autoOrderNumber
);

const showOrderCreateValidationModalSelect = createSelector(
    selectSettings,
    (s) => s.repairShopSettings?.features.vehicleReceptionIntegrationEnabled
);

export default function CreateOrderButton() {
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const { displayName: userName } = useCurrentUser();

    const autoNumber = useSelector(autoRepairOrderNumberSelector);
    const userPermission = useSelector(selectUserPermission);
    const showOrderCreateValidationModal = useSelector(showOrderCreateValidationModalSelect);
    const accountName = useSelector(selectRepairShopIntegrationAccountName);

    const [loading, setLoading] = useState(false);
    const [popupOpen, setPopupOpen] = useState(false);
    const [openValidation, setOpenValidation] = useState(false);
    const navigate = useNavigate();

    const onOrderCreated = useCallback(
        async ({ id }: CreateEmptyOrderResponse, orderNumber?: string) => {
            if (showOrderCreateValidationModal) {
                await callApi(() =>
                    ActivityLogLogic.save(
                        id,
                        t('orderDetails.activityLog.orderCreation', {
                            orderNumber,
                            userName,
                            date: DateTime.now().toFormat(t('dateFormats.short')),
                            time: DateTime.now().toFormat(t('dateFormats.timeOfToday')),
                        }),
                        true
                    )
                );
            }
            nr('order.createdEmpty');
            navigate({ pathname: generatePath(ROUTES.ORDERS_DETAIL, { id }) });
        },
        [navigate, callApi, t, userName, showOrderCreateValidationModal]
    );

    const onClose = useCallback(() => setPopupOpen(false), []);

    const handleOpenValidation = useCallback(() => setOpenValidation((prev) => !prev), []);

    const handleOpenOrderPopUp = useCallback(async () => {
        setOpenValidation(false);

        if (autoNumber) {
            setLoading(true);
            try {
                const response = await callApi(() => OrderAPI.createEmpty());
                onOrderCreated(response);
            } finally {
                setLoading(false);
            }
        } else {
            setPopupOpen(true);
        }
    }, [autoNumber, callApi, onOrderCreated, setOpenValidation]);

    const onClick = useCallback(() => {
        if (showOrderCreateValidationModal) {
            handleOpenValidation();
        } else {
            handleOpenOrderPopUp();
        }
    }, [showOrderCreateValidationModal, handleOpenValidation, handleOpenOrderPopUp]);

    return (
        <>
            <Tooltip content={t('orders.createOrderTooltip')}>
                <div>
                    <Button
                        blockMode
                        label={t('orders.createOrder')}
                        cmosVariant={'filled'}
                        color={Colors.CM1}
                        showLoader={loading}
                        disabled={loading || !userPermission.allowEditOrders}
                        onClick={onClick}
                    />
                </div>
            </Tooltip>
            <NewOrderPopup onClose={onClose} onOrderCreated={onOrderCreated} open={popupOpen} />
            <WarningConfirmationPopup
                open={openValidation}
                title={t('inspectionForms.orderConfirmationModal.title')}
                cancel={t('inspectionForms.orderConfirmationModal.cancelButton')}
                confirm={t('inspectionForms.orderConfirmationModal.confirmButton')}
                body={
                    <Box display="flex" gap="16px" flexDirection="column">
                        <Message>
                            {t('inspectionForms.orderConfirmationModal.message', { accountName })}
                        </Message>
                        <Message>{t('inspectionForms.orderConfirmationModal.question')}</Message>
                    </Box>
                }
                onConfirm={handleOpenOrderPopUp}
                onClose={handleOpenValidation}
            />
        </>
    );
}

const Message = styled(Typography)(({ theme }) => ({
    ...theme.typography.h10Roboto,
    color: '#6A6E72',
    fontSize: 12,
    fontWeight: 400,
    lineHeight: '16px',
}));
