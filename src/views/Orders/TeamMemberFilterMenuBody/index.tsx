import { Box, styled } from '@mui/material';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import MenuItem from '@mui/material/MenuItem';
import Radio from '@mui/material/Radio';
import { CheckCircleIcon } from 'common/components/Icons/CheckCircleIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckedCircleIcon } from 'common/components/Icons/UncheckedCircleIcon';
import Dropdown, { DropdownImplementationComponents } from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { TeamMemberType } from 'common/constants/TeamMemberType';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useInputValue } from 'common/hooks/useInputValue';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { UserShortDto } from 'datacontracts/UserShortDto';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, {
    ComponentProps,
    Dispatch,
    SetStateAction,
    useCallback,
    useEffect,
    useState,
} from 'react';
import { GroupTypeBase, MenuListComponentProps } from 'react-select';
import { IOrderPageFilters } from '../types';

type TeamMemberFilterDTO = {
    members: number[];
    type: TeamMemberType;
};

const TeamMemberCategoryLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],

    '.selected-category &': {
        //fontWeight: 'bold',
        color: theme.palette.primary.main,
    },
}));

const SDivider = styled(Divider)({
    margin: '0 !important',
});

const StyledMembersList = styled('div')({
    overflow: 'hidden',
    position: 'relative',
    zIndex: 1,

    '.os-scrollbar-vertical': {
        marginBottom: 5,
    },
});

const AnimatedDownIcon = styled(DownIcon)({
    transform: 'rotate(0deg)',
    transition: 'transform 0.3s',
});

const SelectedText = styled('span')({
    backgroundColor: '#e1eaff',
});

const MemberInitialsContainer = styled('div')({
    width: '24px!important',
    height: '24px!important',
    backgroundColor: '#6a6e72',
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    display: 'flex',
});

const MemberInitials = styled('span')(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[1],
    fontWeight: 700,
    textAlign: 'center',
}));

const TeamFilterName = styled('span')(({ theme }) => ({
    width: 135,
    height: 14,
    margin: '1px 0 0 10.8px',
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',

    '&.selected': {
        //fontWeight: 'bold',
        color: theme.palette.primary.main,
    },
}));

const markSelectedText = (text: string, teamNamesFilter: string) => {
    const lower = text.toLowerCase();
    const indexFrom = lower.indexOf(teamNamesFilter.toLowerCase());
    if (indexFrom > -1) {
        return (
            <>
                {text.substring(0, indexFrom)}
                <SelectedText>
                    {text.substring(indexFrom, indexFrom + teamNamesFilter.length)}
                </SelectedText>
                {text.substring(indexFrom + teamNamesFilter.length, text.length)}
            </>
        );
    } else {
        return text;
    }
};

function FilteredTeamMember({
    team,
    isSelected,
    handleTeamMemberSelected,
    teamNamesFilter,
}: {
    teamNamesFilter: string;
    team: UserShortDto;
    isSelected?: boolean;
    handleTeamMemberSelected: (member: UserShortDto) => void;
}) {
    return (
        <Grid item xs={11}>
            <MenuItem onClick={() => handleTeamMemberSelected(team)}>
                <MemberInitialsContainer>
                    <MemberInitials>{team.initials}</MemberInitials>
                </MemberInitialsContainer>
                <TeamFilterName className={isSelected ? 'selected' : undefined}>
                    {markSelectedText(team.displayName, teamNamesFilter)}
                </TeamFilterName>
            </MenuItem>
        </Grid>
    );
}

function CustomMenu({
    teamMembers,
    teamNamesFilter,
    preSelectedMembers,
    preSelectedType,
    setPreselectedMembers,
    setPreSelectedType,
    ...props
}: MenuListComponentProps<
    OptionData<number | null>,
    true,
    GroupTypeBase<OptionData<number | null>>
> & {
    teamMembers: UserShortDto[];
    teamNamesFilter: string;
    preSelectedMembers: UserShortDto[];
    preSelectedType: TeamMemberType;
    setPreselectedMembers: Dispatch<SetStateAction<UserShortDto[]>>;
    setPreSelectedType: Dispatch<SetStateAction<TeamMemberType>>;
}) {
    const { t } = useAppTranslation();
    const [teamMembersOrderAlphabetically, setTeamMembersOrderAlphabetically] = useState(true);

    const handleAlphabeticallyOrderClick = useCallback(() => {
        setTeamMembersOrderAlphabetically((x) => !x);
    }, []);

    const handleTeamMemberSelected = useCallback(
        (member: UserShortDto) => {
            const { userId } = member;
            if (preSelectedMembers.find((m) => m.userId === userId)) {
                setPreselectedMembers(preSelectedMembers.filter((m) => m.userId !== userId));
            } else {
                setPreselectedMembers([...preSelectedMembers, member]);
            }
        },
        [preSelectedMembers, setPreselectedMembers]
    );

    const filteredTeamMembers = teamMembers.filter((member: UserShortDto) => {
        const filterText = member.displayName
            .toLowerCase()
            .includes(teamNamesFilter.toLocaleLowerCase());
        const isSelected = preSelectedMembers.includes(member);
        return filterText && !isSelected;
    });

    return (
        <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Grid container justifyContent="center">
                <Grid item xs={11}>
                    <Grid container>
                        <Grid item xs={6}>
                            <MenuItem
                                disableGutters
                                className={
                                    preSelectedType === TeamMemberType.Interacted
                                        ? 'selected-category'
                                        : undefined
                                }
                                onClick={() => setPreSelectedType(TeamMemberType.Interacted)}
                            >
                                <Radio
                                    disableRipple
                                    checked={preSelectedType === TeamMemberType.Interacted}
                                    //onChange={handleTeamMemberRadioChange}
                                    value={TeamMemberType.Interacted}
                                    name="radio-team-member"
                                    color="default"
                                    inputProps={{
                                        'aria-label': TeamMemberType.Interacted,
                                    }}
                                    checkedIcon={
                                        <CheckCircleIcon fill={Colors.CM1} size={IconSize.M} />
                                    }
                                    icon={
                                        <UncheckedCircleIcon
                                            fill={Colors.Neutral6}
                                            size={IconSize.M}
                                        />
                                    }
                                />
                                <TeamMemberCategoryLabel>
                                    {t('orders.filters.memberInCharge')}
                                </TeamMemberCategoryLabel>
                            </MenuItem>
                        </Grid>
                        <Grid item xs={6}>
                            <MenuItem
                                disableGutters
                                className={
                                    preSelectedType === TeamMemberType.AssignedTo
                                        ? 'selected-category'
                                        : undefined
                                }
                                onClick={() => setPreSelectedType(TeamMemberType.AssignedTo)}
                            >
                                <Radio
                                    disableRipple
                                    checked={preSelectedType === TeamMemberType.AssignedTo}
                                    //onChange={handleTeamMemberRadioChange}
                                    value={TeamMemberType.AssignedTo}
                                    name="radio-team-member"
                                    color="default"
                                    inputProps={{
                                        'aria-label': TeamMemberType.AssignedTo,
                                    }}
                                    checkedIcon={
                                        <CheckCircleIcon fill={Colors.CM1} size={IconSize.M} />
                                    }
                                    icon={
                                        <UncheckedCircleIcon
                                            fill={Colors.Neutral6}
                                            size={IconSize.M}
                                        />
                                    }
                                />

                                <TeamMemberCategoryLabel>
                                    {t('orders.assignedMember')}
                                </TeamMemberCategoryLabel>
                            </MenuItem>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>

            <StyledMembersList>
                <OverlayScrollbarsComponent style={{ maxHeight: 200 }}>
                    <SDivider />
                    {preSelectedMembers.map((team) => {
                        return (
                            <>
                                <FilteredTeamMember
                                    handleTeamMemberSelected={handleTeamMemberSelected}
                                    teamNamesFilter={teamNamesFilter}
                                    team={team}
                                    key={team.userId}
                                    isSelected
                                />
                                <SDivider />
                            </>
                        );
                    })}
                    <MenuItem onClick={handleAlphabeticallyOrderClick}>
                        <OrderAlphabeticalLabel>
                            {t('orders.orderAlphabetically')}
                        </OrderAlphabeticalLabel>
                        <AnimatedDownIcon
                            fill={Colors.CM2}
                            style={{
                                transform: `rotate(${teamMembersOrderAlphabetically ? 180 : 0}deg)`,
                            }}
                        />
                    </MenuItem>
                    {teamMembers.length > 0 && <SDivider />}
                    {filteredTeamMembers
                        .sort((a, b) => {
                            if (teamMembersOrderAlphabetically) {
                                return a.displayName.toLowerCase() > b.displayName.toLowerCase()
                                    ? 1
                                    : -1;
                            } else {
                                return a.displayName.toLowerCase() < b.displayName.toLowerCase()
                                    ? 1
                                    : -1;
                            }
                        })
                        .map((team: UserShortDto, idx) => {
                            const member = (
                                <FilteredTeamMember
                                    handleTeamMemberSelected={handleTeamMemberSelected}
                                    teamNamesFilter={teamNamesFilter}
                                    team={team}
                                    key={team.userId}
                                />
                            );

                            if (idx !== filteredTeamMembers.length - 1) {
                                return (
                                    <React.Fragment key={team.userId}>
                                        {member}
                                        <SDivider key={`divider-${idx}`} />
                                    </React.Fragment>
                                );
                            }

                            return member;
                        })}
                </OverlayScrollbarsComponent>
            </StyledMembersList>
        </Box>
    );
}

const OrderAlphabeticalLabel = styled('span')(({ theme }) => ({
    width: 131,
    height: 14,
    margin: '5px 10.3px 5px 0',
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
}));

const TeamMemberFilterMenu = ({
    teamMembers,
    filters,
    setFilters,
}: {
    teamMembers: UserShortDto[];
    filters: IOrderPageFilters;
    setFilters: (filters: IOrderPageFilters) => void;
}) => {
    const { t } = useAppTranslation();
    const [preSelectedMembers, setPreselectedMembers] = useState<UserShortDto[]>([]);
    const [preSelectedType, setPreSelectedType] = useState<TeamMemberType>(
        'AssignedTo' as TeamMemberType
    );
    const [teamNamesFilter, setTeamNamesFilter] = useInputValue('');
    const [showTeamMembersMenu, setShowTeamMembersMenu] = useState(false);

    useEffect(() => {
        initFilter();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [teamMembers, filters.TmIds]);

    useEffect(() => {
        /** RESET PRESELECTED */
        if (showTeamMembersMenu) {
            initFilter();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showTeamMembersMenu]);

    const initFilter = () => {
        const initSelectedMembers = teamMembers.filter((member) =>
            filters.TmIds && filters.TmIds.length > 0
                ? filters.TmIds.includes(member.userId ?? 0)
                : false
        );
        setPreselectedMembers(initSelectedMembers);
        setPreSelectedType(filters.TmTyp ?? TeamMemberType.AssignedTo);
    };

    const handleTeamMembersMenuClose = () => {
        const value: TeamMemberFilterDTO = {
            members: preSelectedMembers.map((member) => member.userId ?? 0),
            type: preSelectedType,
        };

        setFilters({ ...filters, TmIds: value.members, TmTyp: value.type });
        setShowTeamMembersMenu(false);
    };

    const customMenuData: CustomMenuData = {
        teamMembers,
        teamNamesFilter,
        setPreselectedMembers,
        setPreSelectedType,
        preSelectedMembers,
        preSelectedType,
    };

    return (
        <Dropdown
            name={'campo1'}
            cmosVariant="roundedGrey"
            isRequired={false}
            showValidationIndicators={true}
            options={[{ label: '', value: -1 }]}
            multiple={true}
            value={preSelectedMembers.map((member) => ({
                label: member.displayName,
                value: member.userId,
            }))}
            onClose={handleTeamMembersMenuClose}
            isSearchable
            filterOption={(_candidate, input) => {
                setTeamNamesFilter(input);
                return true;
            }}
            Icon={FiltersIcon}
            styles={{
                menu: { minWidth: 360 },
                control: { minWidth: 195 },
                menuList: {
                    marginRight: 0,
                    overflowX: 'hidden',
                    minHeight: 250,
                },
            }}
            placeholder={t('orders.filters.teamMember')}
            CustomMenu={CustomerMenuComponent}
            extraProps={{
                // pass this to CustomMenuComponent (see below)
                [CUSTOM_MENU_DATA_PROPS]: customMenuData,
            }}
        />
    );
};

const CUSTOM_MENU_DATA_PROPS = '__TM_CUSTOM_MENU_DATA__';

type CustomMenuData = {
    teamMembers: UserShortDto[];
    teamNamesFilter: string;
    preSelectedType: TeamMemberType;
    preSelectedMembers: UserShortDto[];
    setPreselectedMembers: Dispatch<SetStateAction<UserShortDto[]>>;
    setPreSelectedType: Dispatch<SetStateAction<TeamMemberType>>;
};

function CustomerMenuComponent(
    props: ComponentProps<DropdownImplementationComponents<number | null, true>['MenuList'] & {}>
) {
    const { selectProps } = props;

    const {
        teamMembers,
        teamNamesFilter,
        preSelectedMembers,
        preSelectedType,
        setPreSelectedType,
        setPreselectedMembers,
    } = selectProps[CUSTOM_MENU_DATA_PROPS] as CustomMenuData;

    return (
        <CustomMenu
            teamMembers={teamMembers}
            teamNamesFilter={teamNamesFilter}
            preSelectedMembers={preSelectedMembers}
            preSelectedType={preSelectedType}
            setPreselectedMembers={setPreselectedMembers}
            setPreSelectedType={setPreSelectedType}
            {...props}
        />
    );
}

export default TeamMemberFilterMenu;
