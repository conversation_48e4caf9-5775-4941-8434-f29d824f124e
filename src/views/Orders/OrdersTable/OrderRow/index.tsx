import { TableCell, TableRow, styled } from '@mui/material';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import { useAppSelector } from 'store';
import { DEFAULT_SHOWN_COLUMNS } from 'store/slices/orders';
import { selectDisplayedOrderColumns } from 'store/slices/orders/selectors';
import theme from 'theme';
import {
    Account,
    Charged,
    Communication,
    CustomerVehicle,
    Estimate,
    Inspection,
    RONumber,
    TeamMembers,
} from 'views/Orders/CellComponents';
import OrderField from 'views/Orders/CellComponents/OrderField';
import { useOrdersJobsTable } from 'views/Orders/CellComponents/OrdersJobs/hooks';
import Phase from 'views/Orders/CellComponents/Phase';
import { IOrderPageParams } from 'views/Orders/types';

type OrderRowProps = {
    order: IRepairOrder;
    selectOrder: (order: IRepairOrder) => void;
    pageParams: IOrderPageParams;
    hasSpaceAfter: boolean;
};

const OrderRowInternal = styled(TableRow, {
    shouldForwardProp: (prop) => !['hasSpaceAfter'].includes(prop as string),
})<{ hasSpaceAfter: boolean }>(({ hasSpaceAfter }) => ({
    height: 'var(--orders-table-row-height)',

    '& td': {
        maxHeight: 'var(--orders-table-row-height) !important',
    },

    '&:hover': {
        backgroundColor: 'rgba(211, 224, 255, 0.27)',
        cursor: 'pointer',
    },
    '&:focus': {
        outline: `2px solid var(--cm3)`,
        outlineOffset: -2,
    },
    ...(!hasSpaceAfter
        ? {
              borderRadius: '0 0 10px 10px',
          }
        : undefined),
}));

const ToggleTableCell = styled(TableCell)({
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
});

export default function OrderRow({ order, selectOrder, pageParams, hasSpaceAfter }: OrderRowProps) {
    const columns = useAppSelector(selectDisplayedOrderColumns);

    const { renderOrdersJobsToggle, renderOrdersJobsTable, enabledOrdersJobs } =
        useOrdersJobsTable();

    return (
        <>
            <OrderRowInternal
                hasSpaceAfter={hasSpaceAfter}
                tabIndex={0}
                onClick={(e) => {
                    const sel = window.getSelection();
                    if (
                        sel &&
                        sel.toString() &&
                        e.target instanceof HTMLElement &&
                        e.target.contains(sel.anchorNode)
                    ) {
                        return;
                    }

                    // NOTE (SV) Condition for correct operation of the job history switch
                    const firstCell = e.currentTarget.querySelector('td');
                    if (
                        firstCell &&
                        (e.target instanceof SVGElement || e.target instanceof HTMLElement) &&
                        e.target.closest('td') === firstCell
                    ) {
                        return;
                    }

                    selectOrder(order);
                }}
                onKeyDown={(e) => ['Enter', ' '].includes(e.key) && selectOrder(order)}
            >
                {enabledOrdersJobs && (
                    <ToggleTableCell align="left" component="td" scope="row">
                        {order.jobsCount !== 0 &&
                            renderOrdersJobsToggle(order.repairOrderKey, order.jobsCount)}
                    </ToggleTableCell>
                )}
                {columns.map((key) => renderColumn(key, pageParams, order))}
                {/* CMOS-4814 p4: Need here to fill the empty space till the table right edge if there is not enough columns (table is fixed) */}
                <TableCell style={{ borderBottom: '1px solid #EFEFEF' }} />
            </OrderRowInternal>
            {enabledOrdersJobs &&
                order.jobsCount !== 0 &&
                renderOrdersJobsTable(order.repairOrderKey, order.jobsCount)}
        </>
    );
}

function renderColumn(key: string, pageParams: IOrderPageParams, order: IRepairOrder) {
    switch (key) {
        case DEFAULT_SHOWN_COLUMNS.TEAM:
            return <TeamMembers key={key} repairOrder={order} />;
        case DEFAULT_SHOWN_COLUMNS.ORDER_NUMBER:
            return <RONumber key={key} repairOrder={order} />;
        case DEFAULT_SHOWN_COLUMNS.CUSTOMER_VEHICLE:
            return <CustomerVehicle key={key} repairOrder={order} />;
        case DEFAULT_SHOWN_COLUMNS.UPLOADED:
            return <Charged key={key} orderBy={pageParams.OrdBy} repairOrder={order} />;
        case DEFAULT_SHOWN_COLUMNS.INSPECTION:
            return <Inspection repairOrder={order} key={key} />;
        case DEFAULT_SHOWN_COLUMNS.ESTIMATE:
            return <Estimate key={key} repairOrder={order} />;
        case DEFAULT_SHOWN_COLUMNS.COMMUNICATION:
            return <Communication key={key} repairOrder={order} />;
        case 'enterprise_account':
            return <Account key={key} repairOrder={order} />;
        case DEFAULT_SHOWN_COLUMNS.PHASE:
            return <Phase key={key} order={order} />;
        default:
            return <OrderField key={key} order={order} field={key} />;
    }
}
