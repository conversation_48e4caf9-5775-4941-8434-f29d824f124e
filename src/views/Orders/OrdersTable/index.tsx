import { CircularProgress, Table, TableBody, styled } from '@mui/material';
import { IRepairOrder } from 'datacontracts/Order/IOrderListResponse';
import { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { useStore } from 'react-redux';
import { useLocation } from 'react-router-dom';
import PageContent from 'views/Components/Page';
import { usePreserveScroll } from '../../../common/hooks/usePreserveScroll';
import { useAppDispatch } from '../../../store';
import { ordersJobsActions } from '../../../store/slices/ordersJobs';
import { selectPreserveJobs } from '../../../store/slices/ordersJobs/selectors';
import TableHeaders from '../TableHeaders';
import { IOrderPageParams } from '../types';
import OrderRow from './OrderRow';
import useOrdersTableStyles from './css';

type OrderTableProps = {
    orders: IRepairOrder[];
    showLoader: boolean;
    pageParams: IOrderPageParams;
    updatePageParams: (params: IOrderPageParams) => void;
    selectOrder: (order: IRepairOrder) => void;
};

const SPageContent = styled(PageContent)({
    overflowX: 'auto',
    margin: '20px 0',
});

const Center = styled('div')({
    position: 'absolute',
    inset: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,.5)',
});

function OrdersTable({
    orders,
    showLoader,
    pageParams,
    updatePageParams,
    selectOrder,
}: OrderTableProps) {
    const dispatch = useAppDispatch();
    const styles = useOrdersTableStyles();
    const location = useLocation();
    const containerRef = useRef<HTMLDivElement>(null);
    const store = useStore();
    // restore / save automatically
    usePreserveScroll(location.pathname + containerRef.current?.id, containerRef);

    useEffect(() => {
        return () => {
            const preserveJobs = selectPreserveJobs(store.getState());
            if (preserveJobs) {
                dispatch(ordersJobsActions.setPreserveJobs(false));
            } else {
                dispatch(ordersJobsActions.clearOrderJobs());
                dispatch(ordersJobsActions.resetAllOrdersExpanded());
            }
        };
    }, [dispatch, store]);

    const inner = useMemo(() => {
        const rows =
            orders && !showLoader
                ? orders.map((order: IRepairOrder, index) => (
                      <OrderRow
                          key={order.repairOrderNumber}
                          pageParams={pageParams}
                          selectOrder={selectOrder}
                          order={order}
                          hasSpaceAfter={index < 9} // only last row should have hasSpaceAfter=false
                      />
                  ))
                : [];

        return rows;
    }, [orders, pageParams, selectOrder, showLoader]);

    const onPageParamsChange = useCallback(
        (params: IOrderPageParams) => updatePageParams({ ...pageParams, ...params, PIdx: 1 }),
        [updatePageParams, pageParams]
    );

    return (
        <SPageContent className={styles.root} ref={containerRef} id={'orders_table'}>
            <Table className={styles.table} stickyHeader aria-label="orders table">
                <TableHeaders pageParams={pageParams} onPageParamsChange={onPageParamsChange} />
                <TableBody>{inner}</TableBody>
            </Table>
            {showLoader && (
                <Center>
                    <CircularProgress />
                </Center>
            )}
        </SPageContent>
    );
}

export default memo(OrdersTable);
