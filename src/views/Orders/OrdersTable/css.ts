import { makeStyles } from '@mui/styles';

const useOrdersTableStyles = makeStyles((theme) => ({
    root: {
        '--orders-table-row-height': '80px',
        [theme.breakpoints.up('lg')]: {
            height: `max(70vh, ${9 * 80 + 65}px)`,
            overflow: 'auto !important',
        },

        // minHeight: 'calc(var(--orders-table-row-height) * 10 + 63px)',
    },
    table: {
        width: '100%',
        tableLayout: 'fixed', // CMOS-4814 p4: DO NOT CHANGE, I don't know how to implement this requirement without fixed layout, see https://www.w3.org/TR/2003/WD-CSS21-20030915/tables.html#width-layout
    },
}));

export default useOrdersTableStyles;
