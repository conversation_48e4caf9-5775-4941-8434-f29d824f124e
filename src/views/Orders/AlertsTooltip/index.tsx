import { Box, styled } from '@mui/material';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import { useQuery } from '@tanstack/react-query';
import AlertAPI from 'api/Alert';
import AreaSpinner from 'common/components/AreaSpinner';
import { WarningIcon } from 'common/components/Icons/WarningIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useMemo } from 'react';
import styles from './styles.module.css';

const AlertTooltip = ({ orderId, forceRender }: { orderId: number; forceRender: () => void }) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const { isLoading: isLoadingAlerts, data: alertListResponse } = useQuery(
        ['orders', orderId, 'inspection', 'alerts'],
        () => AlertAPI.list(orderId),
        {
            onSuccess: () => {
                // A useForceRender hook was used because the tooltip was displayed in the wrong place
                // This occurred because the render started before the data was loaded
                // UseForceRender allows to re-render the tooltip in the correct place, after data was loaded
                forceRender();
            },
            onError: () => {
                forceRender();
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            cacheTime: 60000,
        }
    );

    const alertElements = useMemo(
        () =>
            alertListResponse?.alerts.map((alert) => (
                <AlertText key={alert.id}>{alert.text}</AlertText>
            )),
        [alertListResponse]
    );

    return (
        <Card
            onClick={(e) => e.stopPropagation()}
            sx={{ boxShadow: '0 4px 20px 0 rgba(229, 231, 234, 0.84)', padding: 0 }}
        >
            <OverlayScrollbarsComponent
                style={{ maxHeight: '250px', minWidth: 200, maxWidth: 'min(600px, 40vw)' }}
            >
                <CardContent sx={{ pt: 0 }}>
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            position: 'sticky',
                            top: 0,
                            pt: 2,
                            pb: 1,
                            bgcolor: 'neutral.1',
                        }}
                    >
                        <div className={styles.alertIcon}>
                            <WarningIcon fill={Colors.Warning} size={IconSize.M} />
                        </div>
                        <span className={styles.alertTitle}>{t('orders.alerts')}</span>
                    </Box>

                    {isLoadingAlerts && (
                        <SpinnerContainer>
                            <AreaSpinner />
                        </SpinnerContainer>
                    )}
                    <AlertsList>
                        {alertElements}
                        {alertElements?.length === 0 && (
                            <AlertText>{t('commonLabels.noDataSelector')}</AlertText>
                        )}
                    </AlertsList>
                </CardContent>
            </OverlayScrollbarsComponent>
        </Card>
    );
};

const SpinnerContainer = styled('div')({
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '20px',
});

const AlertsList = styled('ul')({
    listStyle: 'none',
    padding: 0,
    width: '100%',
    margin: 0,
});

const AlertText = styled('li')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    lineHeight: '22px',

    '&:not(:last-child)': {
        borderBottom: '1px solid var(--neutral3)',
    },
}));

export default AlertTooltip;
