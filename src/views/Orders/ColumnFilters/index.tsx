import { I<PERSON><PERSON><PERSON>on, Menu, MenuItem, styled } from '@mui/material';
import { PREDEFINED_FIELDS_LIST } from 'api/fields';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectWorkshopPlannerEnabled } from 'store/slices/globalSettingsSlice';
import {
    DEFAULT_SHOWN_COLUMNS,
    fetchOrdersUiStateThunk,
    toggleDisplayedFieldThunk,
} from 'store/slices/orders';
import {
    selectDisplayableOrderColumns,
    selectDisplayedOrderColumns,
} from 'store/slices/orders/selectors';
import Tooltip from '../../../common/components/Tooltip';
import { DEFAULT_COLUMNS_TRANSLATION_KEYS } from '../helpers';

const ColumnsMenu = styled(Menu)({
    '& > .MuiPaper-root': {
        borderRadius: 10,
        padding: '0 6px',
        backgroundColor: 'var(--neutral2)',
    },
    '& .MuiMenu-list': {
        minWidth: 200,
        maxWidth: 300,
        maxHeight: 'min(80vh, 360px)',
        ...scrollbarStyle(),
        overflowY: 'auto',
        overflowX: 'hidden',

        '& .MuiMenuItem-root:not(:last-child)': {
            borderBottom: '1px solid var(--neutral3)',
        },
    },
});

export default function ColumnFilters() {
    const isEnterprise = useIsEnterpriseRoute();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const displayedColumns = useAppSelector(selectDisplayedOrderColumns);
    const columns = useAppSelector(selectDisplayableOrderColumns);
    const enableWp = useAppSelector(selectWorkshopPlannerEnabled);

    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.target as HTMLElement);
    };

    useEffect(() => {
        if (!isEnterprise) dispatch(fetchOrdersUiStateThunk());
    }, [dispatch, isEnterprise]);

    if (isEnterprise) return null;

    const translate = (key: string) => {
        if (PREDEFINED_FIELDS_LIST.includes(key))
            return t(`settings.customizableFields.predefined.${key.replaceAll('.', '_')}`);
        if (Object.values(DEFAULT_SHOWN_COLUMNS).includes(key))
            return DEFAULT_COLUMNS_TRANSLATION_KEYS[key]
                ? t(DEFAULT_COLUMNS_TRANSLATION_KEYS[key])
                : key;
        return key;
    };

    const toggle = (key: string, shown: boolean, hideTeamColumn: boolean) => {
        dispatch(toggleDisplayedFieldThunk({ key, shown, hideTeamColumn }));
    };

    return (
        <>
            <Tooltip position="top" content={t('orders.columnFiltersTooltip')}>
                <IconButton size="small" onClick={handleClick}>
                    <FiltersIcon fill={anchorEl ? 'var(--cm1)' : 'currentColor'} />
                </IconButton>
            </Tooltip>
            <ColumnsMenu
                anchorOrigin={{
                    horizontal: 'right',
                    vertical: 'bottom',
                }}
                transformOrigin={{
                    horizontal: 'right',
                    vertical: 'top',
                }}
                onClose={() => setAnchorEl(null)}
                open={!!anchorEl}
                anchorEl={anchorEl}
            >
                {columns.map((x) => {
                    const selected = displayedColumns.includes(x.key);
                    const disabled = displayedColumns.length === 1 && selected;

                    return (
                        <MenuItem
                            disabled={disabled}
                            key={x.key}
                            onClick={() => toggle(x.key, !selected, enableWp)}
                        >
                            {selected ? <CheckBoxIcon /> : <UncheckBoxIcon />} {translate(x.name)}
                        </MenuItem>
                    );
                })}
            </ColumnsMenu>
        </>
    );
}
