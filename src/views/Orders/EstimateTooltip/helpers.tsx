import { IEstimateItem } from '../../../datacontracts/Order/IOrderEstimateDataResponse';

export interface IEstimateRow {
    repairName: string;
    cost?: number;
    isPriorityUrgent: boolean;
    status?: 'Approved' | 'Declined';
}

export const mapEstimate = (i: IEstimateItem): IEstimateRow => ({
    repairName: i.repairName,
    cost: i.totalCostWithDiscount,
    isPriorityUrgent: i.priority === 'Urgent',
    status:
        i?.approveStatus === 'ApprovedByConsumer' || i?.approveStatus === 'ApprovedByTeamMember'
            ? 'Approved'
            : i?.approveStatus === 'DeclinedByConsumer' ||
              i?.approveStatus === 'DeclinedByTeamMember'
            ? 'Declined'
            : undefined,
});

export const sortEstimates = (i1: IEstimateRow, i2: IEstimateRow) => {
    if (i1.isPriorityUrgent !== i2.isPriorityUrgent) {
        return i1.isPriorityUrgent ? -1 : 1;
    }
    if (i1.status !== i2.status) {
        return i1.status === 'Approved'
            ? -1
            : i1.status === undefined
            ? 1
            : i2.status === 'Approved'
            ? 1
            : -1;
    }
    if (i1.cost !== i2.cost) {
        return (i1.cost ?? 0) > (i2.cost ?? 0) ? -1 : 1;
    }

    return 0;
};
