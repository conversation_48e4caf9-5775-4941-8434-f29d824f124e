import { Box, Skeleton, styled } from '@mui/material';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Grid from '@mui/material/Grid';
import useResizeObserver from '@react-hook/resize-observer';
import { useQuery } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { $Icon as Icon } from 'common/components/Icons/$Icon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { IconSize } from 'common/styles/IconSize';
import { SingleLineText } from 'common/styles/TextHelpers';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, { forwardRef, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import theme from 'theme';
import { mapEstimate, sortEstimates } from './helpers';

function roundAwayFromZero(
    value: number | null | undefined,
    enableRemoveDecimals: boolean | undefined
): number | null | undefined {
    return enableRemoveDecimals
        ? value === null || value === undefined
            ? null
            : Math.round(value)
        : value;
}

const EstimateTooltip = forwardRef(
    (
        { orderId, estimatesCount }: { orderId: number; estimatesCount: number },
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const { t } = useAppTranslation();
        const { internationalization, repairShopSettings } = useSelector(selectSettings);

        const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;
        const taxPercentage = repairShopSettings?.taxPercentage;

        const { data, isLoading } = useQuery(
            ['order', orderId, 'estimateData'],
            () => OrderAPI.getEstimateData(orderId),
            {
                cacheTime: 60000,
                staleTime: 100,
            }
        );

        const estimates = useMemo(
            () => (data?.items ?? []).map(mapEstimate).sort(sortEstimates),
            [data]
        );
        const totalApproved = useMemo(() => {
            return estimates
                .filter((e) => e.status === 'Approved')
                .map((e) => e.cost ?? 0)
                .reduce((sum, value) => sum + value, 0);
        }, [estimates]);
        const totalRejected = useMemo(() => {
            return estimates
                .filter((e) => e.status === 'Declined')
                .map((e) => e.cost ?? 0)
                .reduce((sum, value) => sum + value, 0);
        }, [estimates]);

        const totalCost = data?.totalCost;
        const totalCostWithTaxesAndDiscount = data?.totalCostWithTaxesAndDiscount;
        const discount =
            data?.discountType === 'Currency'
                ? roundAwayFromZero(
                      data?.discount,
                      repairShopSettings?.features.enableRemoveDecimals
                  )
                : data?.discount;
        const discountType = useMemo(() => data?.discountType, [data]);

        const showDiscountedForm =
            !!data?.totalCostWithTaxesAndDiscount &&
            data?.totalCost !== data?.totalCostWithTaxesAndDiscount;
        const showDiscount = discount ? discount > 0 : false;
        const showTaxes = taxPercentage ? taxPercentage > 0 : false;

        return (
            <StyledCard
                data-test-id={`estimates-popup`}
                data-order-id={orderId}
                ref={ref}
                onClick={(e) => e.stopPropagation()}
            >
                <StyledCardContent>
                    <Grid container>
                        <Grid container sx={{ padding: '16px 0 0 16px' }}>
                            <Grid container alignItems="center" style={{ gap: 6 }}>
                                <DivEstimateIconWrapper>
                                    <Icon fill={Colors.CM3} size={IconSize.M} />
                                </DivEstimateIconWrapper>
                                <span style={{ ...FontSecondary(HeaderStyles.H5_14px, true) }}>
                                    {t('orders.estimatesPopover.estimate')}
                                </span>
                            </Grid>
                            <OverlayScrollbarsComponent
                                style={{ paddingRight: '24px', width: 'calc(100% - 12px)' }}
                            >
                                <AnimatedHeight>
                                    {isLoading && <EstimateListSkeleton count={estimatesCount} />}
                                    {estimates.map((estimate, index) => {
                                        return (
                                            <GridItemRow key={index}>
                                                <DivItemRowLabel>
                                                    <DivItemRowPriorityIndicator
                                                        style={{
                                                            backgroundColor:
                                                                estimate.isPriorityUrgent
                                                                    ? Colors.Error
                                                                    : Colors.Warning,
                                                        }}
                                                    />
                                                    <DivItemRowRepairName>
                                                        <span
                                                            style={{
                                                                ...FontSecondary(
                                                                    HeaderStyles.H6_12px,
                                                                    false,
                                                                    Colors.Neutral7
                                                                ),
                                                            }}
                                                        >
                                                            {estimate.repairName}:
                                                        </span>
                                                    </DivItemRowRepairName>
                                                    {estimate.status && (
                                                        <DivApproveStatus
                                                            className={`status-${estimate.status}`}
                                                        >
                                                            {estimate.status === 'Approved'
                                                                ? t(
                                                                      'orders.estimatesPopover.approved'
                                                                  )
                                                                : estimate.status === 'Declined'
                                                                ? t(
                                                                      'orders.estimatesPopover.rejected'
                                                                  )
                                                                : undefined}
                                                        </DivApproveStatus>
                                                    )}
                                                </DivItemRowLabel>
                                                <span
                                                    style={{
                                                        ...FontSecondary(
                                                            HeaderStyles.H6_12px,
                                                            false,
                                                            estimate.status === 'Approved'
                                                                ? Colors.Success
                                                                : estimate.status === 'Declined'
                                                                ? Colors.Error
                                                                : Colors.Neutral7
                                                        ),
                                                    }}
                                                >
                                                    {InternationalizationLogic.numberToCurrency(
                                                        internationalization,
                                                        roundAwayFromZero(
                                                            estimate.cost ?? 0,
                                                            repairShopSettings?.features
                                                                .enableRemoveDecimals
                                                        ),
                                                        { requireDecimals: requireDecimals }
                                                    )}
                                                </span>
                                            </GridItemRow>
                                        );
                                    })}
                                </AnimatedHeight>
                            </OverlayScrollbarsComponent>
                        </Grid>
                        {totalApproved > 0 && (
                            <DivRow
                                sx={{
                                    padding: '7px 24px 7px 16px',
                                    backgroundColor: Colors.Success_background,
                                }}
                            >
                                <span
                                    style={{
                                        ...FontPrimary(HeaderStyles.H6_12px, true, Colors.Success),
                                    }}
                                >
                                    {t('orders.estimatesPopover.totalApproved')}:
                                </span>
                                <span
                                    style={{
                                        ...FontSecondary(
                                            HeaderStyles.H5_14px,
                                            true,
                                            Colors.Success
                                        ),
                                    }}
                                >
                                    {InternationalizationLogic.numberToCurrency(
                                        internationalization,
                                        totalApproved,
                                        {
                                            requireDecimals: requireDecimals,
                                        }
                                    )}
                                </span>
                            </DivRow>
                        )}
                        {totalRejected > 0 && (
                            <DivRow
                                sx={{
                                    padding: '7px 24px 7px 16px',
                                    backgroundColor: Colors.Error_background,
                                }}
                            >
                                <span
                                    style={{
                                        ...FontPrimary(HeaderStyles.H6_12px, true, Colors.Error),
                                    }}
                                >
                                    {t('orders.estimatesPopover.totalRejected')}:
                                </span>
                                <span
                                    style={{
                                        ...FontSecondary(HeaderStyles.H5_14px, true, Colors.Error),
                                    }}
                                >
                                    {InternationalizationLogic.numberToCurrency(
                                        internationalization,
                                        totalRejected,
                                        {
                                            requireDecimals: requireDecimals,
                                        }
                                    )}
                                </span>
                            </DivRow>
                        )}
                        <DivTotalSection>
                            <span
                                style={{
                                    ...FontPrimary(HeaderStyles.H6_12px, true, Colors.White),
                                    marginRight: '5px',
                                    textWrap: 'nowrap',
                                }}
                            >
                                {t('orders.estimatesPopover.totalEstimated')}:
                            </span>
                            <div style={{ display: 'flex', gap: '4px' }}>
                                {showDiscountedForm ? (
                                    <>
                                        <span
                                            style={{
                                                ...FontSecondary(
                                                    HeaderStyles.H5_14px,
                                                    true,
                                                    Colors.White
                                                ),
                                                textDecorationLine: 'line-through',
                                                textDecorationThickness: '2px',
                                            }}
                                        >
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                totalCost,
                                                {
                                                    requireDecimals: requireDecimals,
                                                }
                                            )}
                                        </span>
                                        {showDiscount && (
                                            <span
                                                style={{
                                                    ...FontSecondary(
                                                        HeaderStyles.H5_14px,
                                                        true,
                                                        Colors.White
                                                    ),
                                                    color: theme.palette.success.light,
                                                }}
                                            >
                                                {discountType === 'Currency'
                                                    ? ` -$${discount}`
                                                    : ` -${discount}%`}
                                            </span>
                                        )}
                                        {showTaxes && (
                                            <span
                                                style={{
                                                    ...FontSecondary(
                                                        HeaderStyles.H5_14px,
                                                        true,
                                                        Colors.White
                                                    ),
                                                    color: theme.palette.success.light,
                                                }}
                                            >
                                                {` +${taxPercentage}%`}
                                            </span>
                                        )}
                                        <span
                                            style={{
                                                ...FontSecondary(
                                                    HeaderStyles.H5_14px,
                                                    true,
                                                    Colors.White
                                                ),
                                            }}
                                        >
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                totalCostWithTaxesAndDiscount,
                                                {
                                                    requireDecimals: requireDecimals,
                                                }
                                            )}
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        <span
                                            style={{
                                                ...FontSecondary(
                                                    HeaderStyles.H5_14px,
                                                    true,
                                                    Colors.White
                                                ),
                                            }}
                                        >
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                totalCost,
                                                {
                                                    requireDecimals: requireDecimals,
                                                }
                                            )}
                                        </span>
                                    </>
                                )}
                            </div>
                        </DivTotalSection>
                    </Grid>
                </StyledCardContent>
            </StyledCard>
        );
    }
);

const SKELETON_SIZES = [300, 200, 150, 250];

function EstimateListSkeleton({ count }: { count: number }) {
    const skeleton: JSX.Element[] = [];

    for (let i = 0; i < count && i < 5; i++) {
        skeleton.push(<Skeleton key={i} width={SKELETON_SIZES[i % SKELETON_SIZES.length]} />);
    }

    return (
        <Box
            sx={{
                display: 'flex',
                alignItems: 'start',
                flexDirection: 'column',
                fontWeight: '500',
                fontSize: 12,
                gap: '4px',
                padding: '8px 0 8px 0',
            }}
        >
            {skeleton}
        </Box>
    );
}

export default EstimateTooltip;

const StyledCard = styled(Card)({
    boxShadow: '0 4px 20px 0 rgba(229, 231, 234, 0.84) !important',
    width: 370,
});

const StyledCardContent = styled(CardContent)({
    padding: '0 !important',
});

const DivRow = styled('div')({
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
});

const DivTotalSection = styled(DivRow)(({ theme }) => ({
    padding: '12px 24px 12px 16px',
    backgroundColor: theme.palette.neutral[8],
}));

const DivEstimateIconWrapper = styled('div')({
    display: 'flex',
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    border: 'solid 1px var(--cm3)',
});

const GridItemRow = styled('div')({
    height: 30,
    minHeight: 30,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',

    '&:not(:last-child)': {
        borderBottom: 'solid 1px var(--neutral3)',
    },
});

const DivItemRowLabel = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const DivApproveStatus = styled('div')(({ theme }) => ({
    borderRadius: '5px',
    padding: '4px',
    marginLeft: 4,
    ...theme.typography.h8Roboto,

    '&.status-Approved': {
        backgroundColor: 'var(--success-background)',
        color: 'var(--success)',
    },
    '&.status-Declined': {
        backgroundColor: 'var(--danger-background)',
        color: 'var(--danger)',
    },
}));

const DivItemRowPriorityIndicator = styled('div')({
    width: 8,
    height: 8,
    borderRadius: '50%',
    marginRight: 8,
});

const DivItemRowRepairName = styled('div')({
    maxWidth: 160,
    ...SingleLineText(),
});

const animationsDisabled = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

const AnimatedHeight = animationsDisabled
    ? ({ children }: React.PropsWithChildren<{}>) => <>{children}</>
    : AnimatedHeightImpl;

function AnimatedHeightImpl({ children }: React.PropsWithChildren<{}>) {
    const innerRef = useRef<HTMLDivElement | null>(null);
    const outerRef = useRef<HTMLDivElement | null>(null);
    const tweenDispose = useRef<(() => void) | null>(null);

    useResizeObserver(innerRef, () => {
        if (tweenDispose.current) {
            tweenDispose.current();
            tweenDispose.current = null;
        }

        if (!innerRef.current || !outerRef.current) return;

        const innerRect = innerRef.current.getBoundingClientRect();
        const outerRect = outerRef.current.getBoundingClientRect();

        const maxHeight = Math.min(200, window.innerHeight * 0.6);

        tweenDispose.current = tween(
            outerRect.height,
            Math.min(innerRect.height, maxHeight) + 10,
            150,
            (h) => {
                if (outerRef.current) {
                    outerRef.current.style.height = `${h}px`;
                }
            }
        );
    });

    return (
        <DivRows ref={outerRef}>
            <div ref={innerRef}>{children}</div>
        </DivRows>
    );
}

const DivRows = styled('div')({
    padding: '8px 0 0 8px',
    maxHeight: 'min(60vh, 200px)',
    display: 'flex',
    flexDirection: 'column',
});

// https://easings.net/#easeOutQuint
function easeOutQuint(x: number): number {
    return 1 - Math.pow(1 - x, 5);
}

function tween(
    from: number,
    to: number,
    duration: number,
    update: (val: number) => void,
    callback?: () => void,
    timingFunction: (x: number) => number = easeOutQuint
): () => void {
    const start = performance.now();
    let stopFlag = false;

    function step(timestamp: number) {
        if (stopFlag) return;

        const progress = Math.min(timestamp - start, duration) / duration;
        const tProgress = timingFunction(progress);
        const val = from + (to - from) * tProgress;

        update(val);

        if (progress < 1) {
            window.requestAnimationFrame(step);
        } else {
            callback?.();
        }
    }

    window.requestAnimationFrame(step);
    return () => {
        stopFlag = true;
    };
}
