import { makeStyles } from '@mui/styles';

export const useStyles = makeStyles((theme) => ({
    body: {
        paddingLeft: 30,
        paddingRight: 30,
        width: 'calc(100% - var(--header-height))',

        // eslint-disable-next-line no-useless-computed-key
        ['@media (min-width: 1920px)']: {
            paddingLeft: 80,
            paddingRight: 80,
            width: 'calc(100% - 160px)',
        },
    },

    downloadIcon: {
        cursor: 'pointer',

        '&>svg': {
            display: 'flex',
        },
    },
    resultLabel: {
        height: 15,
        margin: '0 8px 0 0',
        fontFamily: 'Inter',
        fontSize: 12,
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: 1.17,
        letterSpacing: 'normal',
        textAlign: 'end',
        color: theme.palette.neutral[7],
    },
}));
