import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import moment from 'moment';
import { useMemo } from 'react';
import { useAppSelector } from 'store';
import { DEFAULT_SHOWN_COLUMNS } from 'store/slices/orders';
import { selectDisplayedOrderColumnsExt } from 'store/slices/orders/selectors';
import { NotificationType } from '../../common/components/Notification/INotificationProps';
import { NotificationData } from '../../common/components/NotificationPull/NotificationData';

export const handleApiError = async (
    error: any,
    setError: (_: NotificationData) => void
): Promise<void> => {
    if (error.status === 401) {
        setError(new NotificationData('Not logged', 'user is not logged', NotificationType.danger));
    }
    setError(new NotificationData('Request Error', error.data, NotificationType.danger));
};

const DATE_IN_URL_FORMAT = 'DDMMYY';

export const toUrlFormatString = (date: Date | null): string =>
    date ? moment(date).format(DATE_IN_URL_FORMAT) : '';

export type DisplayedOrderColumnInfo =
    | {
          type: 'order_table_ui';
          labelTranslationKey: string;
          key: string;
      }
    | {
          type: 'order_field';
          name: string;
          key: string;
      }
    | {
          key: string;
          type: 'enterprise_account';
      };

export const DEFAULT_COLUMNS_TRANSLATION_KEYS: Record<string, string> = {
    [DEFAULT_SHOWN_COLUMNS.TEAM]: 'orders.tableHeaders.team',
    [DEFAULT_SHOWN_COLUMNS.ORDER_NUMBER]: 'orders.tableHeaders.RO#',
    [DEFAULT_SHOWN_COLUMNS.CUSTOMER_VEHICLE]: 'orders.tableHeaders.customerVehicle',
    [DEFAULT_SHOWN_COLUMNS.UPLOADED]: 'orders.tableHeaders.charged',
    [DEFAULT_SHOWN_COLUMNS.INSPECTION]: 'orders.tableHeaders.inspection',
    [DEFAULT_SHOWN_COLUMNS.ESTIMATE]: 'orders.tableHeaders.estimate',
    [DEFAULT_SHOWN_COLUMNS.COMMUNICATION]: 'orders.tableHeaders.communication',
    [DEFAULT_SHOWN_COLUMNS.PHASE]: 'orders.tableHeaders.phase',
};

export function useDisplayedOrderColumns(): DisplayedOrderColumnInfo[] {
    const isEnterprise = useIsEnterpriseRoute();
    const selectedColumns = useAppSelector(selectDisplayedOrderColumnsExt);

    return useMemo(() => {
        if (isEnterprise) {
            const defaultFields: DisplayedOrderColumnInfo[] = Object.values(
                DEFAULT_SHOWN_COLUMNS
            ).map((x) => ({
                type: 'order_table_ui',
                key: x,
                labelTranslationKey:
                    DEFAULT_COLUMNS_TRANSLATION_KEYS[x] ?? 'unknown translation key',
            }));

            return [
                ...defaultFields,
                { type: 'enterprise_account', key: 'order.tableHeaders.enterpriseAccount' },
            ];
        } else {
            return selectedColumns.map(({ key, name }) => {
                if (DEFAULT_COLUMNS_TRANSLATION_KEYS[key]) {
                    return {
                        type: 'order_table_ui',
                        key,
                        labelTranslationKey: DEFAULT_COLUMNS_TRANSLATION_KEYS[key],
                    };
                } else {
                    return {
                        type: 'order_field',
                        name,
                        key,
                    };
                }
            });
        }
    }, [isEnterprise, selectedColumns]);
}
