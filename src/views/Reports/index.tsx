import { useMemo } from 'react';
import RouterParameterBasedTabs, {
    RouterTab,
} from '../../common/components/tabs/RouterParameterBasedTabs';

import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { StickyTabsWrapper } from 'views/Components/StickyTabsWrapper';
import { ENTERPRISE_ROUTES, REPORTS_TABS, ROUTES } from '../../common/constants';
import useDocumentTitle from '../../common/hooks/useDocumentTitle';
import { useAppSelector } from '../../store';
import { selectSettings } from '../../store/slices/globalSettingsSlice';
import CustomReportsTab from './CustomReports';
import PredefinedReportsTab from './PredefinedReports';

const TabsContainer = styled('div')({
    marginLeft: 'auto',
    marginRight: 'auto',
    width: '91%',
});

export default function Reports() {
    const { t } = useAppTranslation();
    const gs = useAppSelector(selectSettings);
    const routerTabs: RouterTab[] = useMemo(
        () => [
            {
                value: REPORTS_TABS.PREDEFINED_REPORTS,
                label: t('reports.predefinedReports'),
                content: <PredefinedReportsTab />,
            },
            {
                value: REPORTS_TABS.CUSTOM_REPORTS,
                label: t('reports.customizableReports'),
                content: <CustomReportsTab />,
            },
        ],
        [t]
    );

    useDocumentTitle(t('titles.reports'));
    if (!gs.enableCustomReport) {
        return <PredefinedReportsTab />;
    }

    return (
        <RouterParameterBasedTabs
            urlPattern={
                gs.appMode === 'RepairShop' ? ROUTES.REPORTS.PATH : ENTERPRISE_ROUTES.REPORTS.PATH
            }
            sx={{ height: 46 }}
            parameterName="section"
            tabs={routerTabs}
            renderLayout={(v) => (
                <>
                    <TabsContainer>
                        <StickyTabsWrapper alwaysDetached>{v.tabs}</StickyTabsWrapper>
                    </TabsContainer>
                    {v.content}
                </>
            )}
        />
    );
}
