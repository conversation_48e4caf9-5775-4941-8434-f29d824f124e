import { Box, Grid } from '@mui/material';
import { Button } from 'common/components/Button';
import InfoTooltip from 'common/components/InfoTooltip';
import { Checkbox } from 'common/components/Inputs';
import DateFormField from 'common/components/Inputs/DateFormField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import moment from 'moment';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { notifyLoadingError, setNewToaster } from 'store/actions/toasters.action';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import PageContent, { WidePageLayout } from 'views/Components/Page';

import InvalidDatesModal from '../InvalidDatesModal';
import LargeReportModal from '../LargeReportModal';
import { useStyles } from './css';
import {
    DateType,
    formatDate,
    generateReportByOption,
    getDropdownOptions,
    largeReportApiMap,
} from './helpers';

//TODO: Checkbox component with span inside?
const getCheckboxLabelFont = (condition: boolean) => {
    const color = condition ? Colors.CM2 : Colors.Neutral6;
    return FontSecondary(HeaderStyles.H6_12px, false, color);
};

export default function PredefinedReportsTab() {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const gs = useAppSelector(selectSettings);

    const reportsOptions = useMemo(() => getDropdownOptions(t, gs), [t, gs]);
    const [from, setFrom] = useState<Date | null>(null);
    const [to, setTo] = useState<Date | null>(null);
    const [type, setType] = useState<OptionData | null>(null);
    const [dateType, setDateType] = useState<DateType>(DateType.UploadTime);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [largeReportOpen, setLargeReportOpen] = useState(false);
    const { callApi: callApiSendEmail } = useApiCall();
    const { callApi: callApiCheckReport } = useApiCall();
    const [isInvalidDatesVisible, setIsInvalidDatesVisible] = useState(false);

    const datesAreValid = () =>
        type?.value !== 'executiveIntelligenceReportOption' ||
        !from ||
        !to ||
        moment(from).isSame(moment(to), 'year');

    useEffect(() => {
        if (
            (!type || type.value !== 'activityReportEstimatesOption') &&
            dateType === DateType.ApprovalTime
        ) {
            setDateType(DateType.UploadTime);
        }
    }, [type, dateType]);

    const onCheckboxChange = (e: ChangeEvent<HTMLInputElement>, value: DateType) => {
        e.preventDefault();
        setDateType(value);
    };

    const getParams = () => {
        const fromParam = formatDate(from);
        const toParam = formatDate(to);
        return { from: fromParam, to: toParam, dType: dateType };
    };

    const createReport = async () => {
        if (!datesAreValid()) {
            setIsInvalidDatesVisible(true);
            return;
        }
        setIsLoading(true);
        try {
            if (await checkReportIsLarge()) setLargeReportOpen(true);
            else await doCreateReport();
        } finally {
            setIsLoading(false);
        }
    };

    const checkReportIsLarge = async () => {
        const checkLengthApi =
            largeReportApiMap[type?.value as keyof typeof largeReportApiMap]?.checkLengthApi;
        if (!!checkLengthApi)
            return await callApiCheckReport(() => checkLengthApi(getParams()), {
                selectErrorContent: () => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            });

        return false;
    };

    const doCreateReport = async () => {
        const params = getParams();
        try {
            await generateReportByOption(type?.value, params.from, params.to, params.dType);
        } catch (e) {
            dispatch(notifyLoadingError());
        }
    };

    const sendEmailReport = async (email: string) => {
        const emailApi = largeReportApiMap[type?.value as keyof typeof largeReportApiMap]?.emailApi;
        if (!!emailApi) {
            const params = getParams();
            callApiSendEmail(
                () =>
                    emailApi({
                        emailAddresses: email,
                        dType: params.dType,
                        from: params.from,
                        to: params.to,
                    }),
                {
                    selectErrorContent: () => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );
            dispatch(
                setNewToaster(
                    new NotificationData(
                        t('reports.emailSentBody'),
                        t('reports.emailSentTitle'),
                        NotificationType.success
                    )
                )
            );
            setLargeReportOpen(false);
        }
    };

    return (
        <>
            <WidePageLayout marginTop>
                <PageContent paddedX paddedY>
                    <div className={styles.root}>
                        <Box display="flex" flexWrap="wrap" flexDirection="row" style={{ gap: 12 }}>
                            <Box className={styles.datePicker}>
                                <DateFormField
                                    name="from"
                                    variant="rounded"
                                    enableTextInput
                                    value={from}
                                    onChange={setFrom}
                                    enableEnterComplete={true}
                                />
                            </Box>
                            <Box className={styles.datePicker}>
                                <DateFormField
                                    name="to"
                                    variant="rounded"
                                    enableTextInput
                                    value={to}
                                    onChange={setTo}
                                    enableEnterComplete={true}
                                />
                            </Box>
                            <Box className={styles.dropDown}>
                                <Dropdown
                                    name={'typeDropdown'}
                                    value={type}
                                    onChange={setType}
                                    placeholder={t('reports.reportType')}
                                    cmosVariant="roundedGrey"
                                    options={reportsOptions}
                                    isRequired={true}
                                    menuPosition="fixed"
                                    styles={{
                                        menu: {
                                            overflow: 'hidden',
                                        },
                                        menuList: {
                                            maxHeight: '20vh',
                                            paddingRight: '3%',
                                            marginRight: '3%',
                                        },
                                    }}
                                />
                            </Box>
                            <Box
                                display="flex"
                                flexDirection="row"
                                alignItems="center"
                                flexBasis="100%"
                                style={{ gap: 35 }}
                            >
                                <Grid
                                    container
                                    direction="row"
                                    alignItems="center"
                                    style={{ width: 'auto' }}
                                >
                                    <Checkbox
                                        onChange={(e) => onCheckboxChange(e, DateType.UploadTime)}
                                        checked={dateType === DateType.UploadTime}
                                    />
                                    <span
                                        style={{
                                            ...getCheckboxLabelFont(
                                                dateType === DateType.UploadTime
                                            ),
                                        }}
                                    >
                                        {t('reports.uploadDate')}
                                    </span>
                                    <InfoTooltip text={t('reports.uploadTooltipText')} />
                                </Grid>
                                <Box
                                    display="flex"
                                    flexDirection="row"
                                    alignItems="center"
                                    width="auto"
                                >
                                    <Checkbox
                                        onChange={(e) =>
                                            onCheckboxChange(e, DateType.LastUpdatedTime)
                                        }
                                        checked={dateType === DateType.LastUpdatedTime}
                                    />
                                    <span
                                        style={{
                                            ...getCheckboxLabelFont(
                                                dateType === DateType.LastUpdatedTime
                                            ),
                                        }}
                                    >
                                        {t('reports.updateDate')}
                                    </span>
                                    <InfoTooltip text={t('reports.updateTooltipText')} />
                                </Box>
                                {type && type.value === 'activityReportEstimatesOption' && (
                                    <Grid
                                        container
                                        direction="row"
                                        alignItems="center"
                                        style={{ width: 'auto' }}
                                    >
                                        <Checkbox
                                            onChange={(e) =>
                                                onCheckboxChange(e, DateType.ApprovalTime)
                                            }
                                            checked={dateType === DateType.ApprovalTime}
                                        />
                                        <span
                                            style={{
                                                ...FontSecondary(
                                                    HeaderStyles.H6_12px,
                                                    false,
                                                    Colors.CM2
                                                ),
                                            }}
                                        >
                                            {t('reports.approvalDate')}
                                        </span>
                                        <InfoTooltip text={t('reports.approvalTooltipText')} />
                                    </Grid>
                                )}
                            </Box>
                        </Box>
                        <Box display="flex" justifyContent="flex-end">
                            <Button
                                cmosVariant={'filled'}
                                cmosSize={'medium'}
                                className={styles.createButton}
                                label={t('reports.createReport')}
                                onClick={createReport}
                                disabled={type === null || isLoading}
                                showLoader={isLoading}
                            />
                        </Box>
                    </div>
                </PageContent>
            </WidePageLayout>
            {largeReportOpen && (
                <LargeReportModal
                    onSubmit={sendEmailReport}
                    onClose={() => setLargeReportOpen(false)}
                />
            )}
            {isInvalidDatesVisible && (
                <InvalidDatesModal
                    from={moment(from).toString()}
                    to={moment(to).toString()}
                    onClose={() => setIsInvalidDatesVisible(false)}
                />
            )}
        </>
    );
}
