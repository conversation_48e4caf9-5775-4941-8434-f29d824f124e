import ReportEmailAPI from 'api/ReportEmail';
import ReportsAP<PERSON> from 'api/Reports';
import { GroupData, OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { OrderByType } from 'common/constants/OrderByType';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { IRepairOrderListRequest } from 'datacontracts/Order/IOrderListRequest';
import { TFunction } from 'i18next';
import moment from 'moment';

const DATE_IN_URL_FORMAT = 'DDMMYY';
export const formatDateString = (date: string | undefined): string =>
    date ? moment(date).format(DATE_IN_URL_FORMAT) : '';

export const formatDate = (date: Date | null): string =>
    date ? moment(date).format(DATE_IN_URL_FORMAT) : '';

export enum DateType {
    UploadTime = 'UploadTime',
    LastUpdatedTime = 'LastUpdatedTime',
    ApprovalTime = 'ApprovalTime',
}

export const getDropdownOptions = (t: TFunction, gs: GlobalSettingsDto): GroupData[] => {
    const forAll: GroupData = {
        label: t('reports.forAll'),
        options: [
            {
                label: t('reports.activityReportInspectionFormsOption'),
                value: 'activityReportInspectionFormsOption',
            },
            {
                label: t('reports.activityReportFollowUpLettersOption'),
                value: 'activityReportFollowUpLettersOption',
            },
            {
                label: t('reports.activityReportDmsOption'),
                value: 'activityReportDmsOption',
            },
            {
                label: t('reports.activityReportPartsOption'),
                value: 'activityReportPartsOption',
            },
            {
                label: t('reports.activityReportEstimatesOption'),
                value: 'activityReportEstimatesOption',
            },
            gs.appMode === 'RepairShop'
                ? {
                      label: t('reports.activityReportSurveysOption'),
                      value: 'activityReportSurveysOption',
                  }
                : undefined,
            gs.appMode === 'RepairShop'
                ? {
                      label: t('reports.activityReportWorkshopPlanner'),
                      value: 'activityReportWorkshopPlanner',
                  }
                : undefined,
        ].filter((x): x is OptionData<string> => !!x),
    };

    const options: GroupData[] = [
        {
            label: t('reports.forGeneralManager'),
            options: [
                {
                    label: t('reports.executiveIntelligenceReportOption'),
                    value: 'executiveIntelligenceReportOption',
                },
                {
                    label: t('reports.auditedProcessesOption'),
                    value: 'auditedProcessesOption',
                },
            ],
        },
        {
            label: t('reports.forAftersalesManager'),
            options: [
                {
                    label: t('reports.inspectionPatternsReportOption'),
                    value: 'inspectionPatternsReportOption',
                },
                {
                    label: t('reports.financialKpisReportOption'),
                    value: 'financialKpisReportOption',
                },
                {
                    label: t('reports.operationalKpisReportOption'),
                    value: 'operationalKpisReportOption',
                },
            ],
        },
        forAll,
    ];

    return options;
};

export const generateReportByOption = async (
    optionValue: string,
    fromParam: string,
    toParam: string,
    dateType: DateType
): Promise<void> => {
    if (optionValue === 'executiveIntelligenceReportOption')
        await ReportsAPI.executiveIntelligenceReport({
            from: fromParam,
            to: toParam,
            dType: dateType,
        });
    else if (optionValue === 'auditedProcessesOption')
        await ReportsAPI.auditedProcessesReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'inspectionPatternsReportOption')
        await ReportsAPI.inspectionPatternsReport({
            from: fromParam,
            to: toParam,
            dType: dateType,
        });
    else if (optionValue === 'financialKpisReportOption')
        await ReportsAPI.financialKPIReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'operationalKpisReportOption')
        await ReportsAPI.operationalKPIReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'activityReportInspectionFormsOption') {
        const orderBy =
            dateType === DateType.UploadTime ? OrderByType.UploadedDesc : OrderByType.UpdatedDesc;
        const filters: IRepairOrderListRequest = {
            From: fromParam,
            To: toParam,
            OrdBy: orderBy,
            RShops: [],
            PIdx: 1,
        };
        await ReportsAPI.orders(filters);
    } else if (optionValue === 'activityReportFollowUpLettersOption')
        await ReportsAPI.followUpReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'activityReportDmsOption') {
        await ReportsAPI.dmsReport({ from: fromParam, to: toParam, dType: dateType });
    } else if (optionValue === 'activityReportPartsOption')
        await ReportsAPI.partsReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'activityReportEstimatesOption')
        await ReportsAPI.estimatesReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'activityReportSurveysOption')
        await ReportsAPI.surveysReport({ from: fromParam, to: toParam, dType: dateType });
    else if (optionValue === 'activityReportWorkshopPlanner')
        await ReportsAPI.workshopPlannerReport({ from: fromParam, to: toParam, dType: dateType });
};

export const largeReportApiMap = {
    activityReportDmsOption: {
        emailApi: ReportEmailAPI.reportForDms,
        checkLengthApi: ReportsAPI.checkReportIsLarge.bind(null, 'DMS'),
    },
    activityReportInspectionFormsOption: {
        emailApi: ReportEmailAPI.reportForOrders,
        checkLengthApi: ReportsAPI.checkReportIsLarge.bind(null, 'Orders'),
    },
    inspectionPatternsReportOption: {
        emailApi: ReportEmailAPI.reportForPatterns,
        checkLengthApi: ReportsAPI.checkReportIsLarge.bind(null, 'Patterns'),
    },
    activityReportEstimatesOption: {
        emailApi: ReportEmailAPI.reportForEstimates,
        checkLengthApi: ReportsAPI.checkReportIsLarge.bind(null, 'Estimates'),
    },
};
