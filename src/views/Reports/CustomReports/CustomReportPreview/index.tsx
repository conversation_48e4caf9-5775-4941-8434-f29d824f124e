import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';

import {
    Box,
    Paper,
    styled,
    Table,
    TableBody,
    TableContainer,
    TableRow,
    Typography,
} from '@mui/material';
import TableCell from '@mui/material/TableCell';

import TableHead from '@mui/material/TableHead';
import { useQuery } from '@tanstack/react-query';
import ReportsAPI, {
    CellData,
    CustomReportColumnType,
    OrderedReportProperty,
    ReportColumn,
} from 'api/Reports';
import Tooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { TFunction } from 'i18next';
import moment from 'moment';
import { OverlayScrollbarsComponent, OverlayScrollbarsComponentRef } from 'overlayscrollbars-react';
import { useAppDispatch, useAppSelector } from 'store';
import { customReportActions } from 'store/slices/customReport';
import {
    selectFrom,
    selectPreviewGridData,
    selectPrimaryColumn,
    selectSelectedColumns,
    selectTo,
} from 'store/slices/customReport/selectors';
import { selectFilters } from 'store/slices/enterprise/reports';
import { useDebounce } from 'use-debounce';

const DEBUG_PRODUCTION_SHOP_KEY = 'caecda63-252e-4ebc-b188-980856eec249'; // QA Automation team production account https://smokenew.clearmechanic.com
const USE_PRODUCTION_DATABASE = false;

export type TableColumn = {
    id: string;
    type?: CustomReportColumnType;
    label: ReactNode;
};

function getUniqueIndexForCell(rowIndex: number, colIndex: number) {
    return rowIndex * 100000 + colIndex;
}

function getResultString(numberOfResults: number | undefined, t: TFunction) {
    if (!numberOfResults) return `0 ${t('reports.results')}`;
    if (numberOfResults === 1) return `${numberOfResults} ${t('reports.result')}`;
    if (numberOfResults < 20) return `${numberOfResults} ${t('reports.results')}`;
    return `20+ ${t('reports.results')}`;
}

function getTextAlign(columnType?: CustomReportColumnType): 'left' | 'center' | 'right' {
    if (columnType === undefined) return 'left';

    switch (columnType) {
        case CustomReportColumnType.String:
            return 'left';
        case CustomReportColumnType.Numeric:
        case CustomReportColumnType.Date:
        case CustomReportColumnType.TimeFromDate:
        case CustomReportColumnType.TimeMinutes:
        case CustomReportColumnType.TimeSpan:
        case CustomReportColumnType.TimeSpanDays:
        case CustomReportColumnType.TimeSpanDaysToSeconds:
        case CustomReportColumnType.DateAndTime:
        case CustomReportColumnType.TotalHoursAndMinutesFromTimespan:
            return 'center';
        case CustomReportColumnType.Currency:
        case CustomReportColumnType.Percentage:
        case CustomReportColumnType.Boolean:
            return 'right';
        default:
            return 'left';
    }
}

export default function CustomReportPreview() {
    usePreviewDataFetcher();

    const { t } = useAppTranslation();
    const selectedColumns = useAppSelector(selectSelectedColumns);
    const primaryColumn = useAppSelector(selectPrimaryColumn);
    const previewGridData = useAppSelector(selectPreviewGridData);

    const previewGridColumns: TableColumn[] = useMemo(() => {
        function createPreviewGridColumn(reportColumn: ReportColumn): TableColumn {
            const countResults = (id: string) =>
                previewGridData?.filter(
                    (entry: Record<string, string>) => entry[id] !== null && entry[id] !== ''
                );
            const keyExists = (id: string) =>
                previewGridData?.some((obj) => obj.hasOwnProperty(id));

            const numberOfResults = countResults(reportColumn.id);
            const shouldShowResults = numberOfResults !== undefined && keyExists(reportColumn.id);
            return {
                id: reportColumn.id,
                type: reportColumn.columnType,
                label: (
                    <>
                        {reportColumn.name?.toUpperCase()}
                        <br />
                        {shouldShowResults && (
                            <SpanTotalResultText>
                                {getResultString(numberOfResults?.length, t)}
                            </SpanTotalResultText>
                        )}
                    </>
                ),
            };
        }

        const updatedColumns: TableColumn[] = structuredClone(selectedColumns)
            .sort((a, b) => a.order - b.order)
            .map(createPreviewGridColumn);

        if (primaryColumn) {
            updatedColumns.unshift(createPreviewGridColumn(primaryColumn));
        }
        return updatedColumns;
    }, [selectedColumns, primaryColumn, previewGridData, t]);

    const [showTooltip, setShowTooltip] = useState<boolean>(false);
    const tableContainerRef = useRef<OverlayScrollbarsComponentRef | null>(null);
    const prevColumnsLength = useRef(previewGridColumns.length);
    const elementsRefs = useRef<(HTMLSpanElement | null)[]>([]);

    useEffect(() => {
        if (previewGridColumns.length > prevColumnsLength.current) {
            const scrollElement = tableContainerRef.current!.osInstance()?.elements().viewport;
            if (scrollElement) {
                scrollElement.scrollLeft = scrollElement.scrollWidth;
            }
        }
        prevColumnsLength.current = previewGridColumns.length;
    }, [previewGridColumns.length]);

    function onMouseEnterItem(rowIndex: number, colIndex: number) {
        const offsetWidth =
            elementsRefs.current[getUniqueIndexForCell(rowIndex, colIndex)]?.offsetWidth ?? 0;
        const scrollWidth =
            elementsRefs.current[getUniqueIndexForCell(rowIndex, colIndex)]?.scrollWidth ?? 0;

        if (offsetWidth < scrollWidth) {
            setShowTooltip(true);
        }
    }

    function handleRefAssignment(ref: HTMLSpanElement | null, rowIndex: number, colIndex: number) {
        elementsRefs.current[getUniqueIndexForCell(rowIndex, colIndex)] = ref;
    }

    const getCellValue: React.FC = (item: Record<string, string>, column: TableColumn) => {
        switch (column.type) {
            case CustomReportColumnType.Link:
                return (
                    <a href={item[column.id]} target="_blank" rel="noreferrer">
                        {item[column.id]}
                    </a>
                );
            case CustomReportColumnType.OrderPhase:
                const phaseName = item[column.id];
                if (['noPhase', 'closedOrder'].includes(phaseName))
                    return <>${t(`phases.${phaseName}`)}</>;
                return <>{phaseName}</>;
            default:
                return <>{item[column.id]}</>;
        }
    };

    return (
        <Box style={{ height: '100%' }}>
            <TableContainerStyles component={Paper}>
                <OverlayScrollbarsComponent
                    ref={tableContainerRef}
                    style={{
                        boxSizing: 'border-box',
                        borderRadius: '12px 12px 12px 12px',
                        height: '100%',
                        width: '100%',
                    }}
                >
                    <Box>
                        <Table stickyHeader aria-label="sticky table">
                            <TableHead>
                                <TableRow>
                                    {previewGridColumns.map((column: TableColumn) => (
                                        <TableCellHeader key={column.id}>
                                            <DivTableHeaderText>
                                                <>{column.label}</>
                                            </DivTableHeaderText>
                                        </TableCellHeader>
                                    ))}
                                </TableRow>
                            </TableHead>

                            <TableBody>
                                {previewGridData.map(
                                    (item: Record<string, string>, rowIndex: number) => (
                                        <TableRow key={rowIndex} id={`$GridList-${rowIndex}`} hover>
                                            {previewGridColumns.map(
                                                (column: TableColumn, colIndex: number) => (
                                                    <TableCell
                                                        key={colIndex}
                                                        sx={{
                                                            width: 180,
                                                            minWidth: 180,
                                                            maxWidth: 180,
                                                            textAlign: getTextAlign(column.type),
                                                            borderRight:
                                                                '1px solid var(--neutral3)',
                                                        }}
                                                    >
                                                        <Tooltip
                                                            content={
                                                                showTooltip ? item[column.id] : ''
                                                            }
                                                            position="bottom"
                                                        >
                                                            <StyledTypography
                                                                onMouseEnter={() =>
                                                                    onMouseEnterItem(
                                                                        rowIndex,
                                                                        colIndex
                                                                    )
                                                                }
                                                                onMouseLeave={() =>
                                                                    setShowTooltip(false)
                                                                }
                                                                ref={(ref) =>
                                                                    handleRefAssignment(
                                                                        ref,
                                                                        rowIndex,
                                                                        colIndex
                                                                    )
                                                                }
                                                            >
                                                                {getCellValue(item, column)}
                                                            </StyledTypography>
                                                        </Tooltip>
                                                    </TableCell>
                                                )
                                            )}
                                        </TableRow>
                                    )
                                )}
                            </TableBody>
                        </Table>
                    </Box>
                </OverlayScrollbarsComponent>
            </TableContainerStyles>
        </Box>
    );
}

/**
 * Hook that fetches and stores the custom report preview data.
 *
 * It memoizes the dependencies of the API call and uses the `useDebounce` hook to
 * debounce the API call by 700ms. It also memoizes the transformed data and stores
 * it in the Redux store. The hook is disabled if the primary column is not set.
 *
 * @returns {void} Nothing is returned from this hook.
 */
function usePreviewDataFetcher() {
    const dispatch = useAppDispatch();
    const isEnterprise = useIsEnterpriseRoute();
    const fromDate = useAppSelector(selectFrom);
    const toDate = useAppSelector(selectTo);
    const from = useMemo(() => (fromDate ? new Date(fromDate) : null), [fromDate]);
    const to = useMemo(() => (toDate ? new Date(toDate) : null), [toDate]);
    const primaryColumn = useAppSelector(selectPrimaryColumn);
    const selectedColumns = useAppSelector(selectSelectedColumns);
    const { locations: selectedLocations } = useAppSelector(selectFilters);
    const selectedColumnsIds = useMemo(() => {
        return selectedColumns.map((col) => col.id);
    }, [selectedColumns]);

    const debouncedPreviewDependenciesMemo = useMemo(
        () => ({
            selectedColumnsIds: selectedColumnsIds,
            selectedLocations: selectedLocations,
            primaryColumn,
            from,
            to,
        }),
        [selectedColumnsIds, selectedLocations, primaryColumn, from, to]
    );

    const [debouncedPreviewDependencies] = useDebounce(debouncedPreviewDependenciesMemo, 700);

    const { data: previewData } = useQuery(
        ['previewData', debouncedPreviewDependencies],
        () => {
            if (from === null || to === null) {
                throw new Error(
                    'The date range is not valid. Please provide both a start and end date.'
                );
            }
            if (USE_PRODUCTION_DATABASE) {
                return ReportsAPI.getCustomReportTestPreview({
                    shopKey: DEBUG_PRODUCTION_SHOP_KEY,
                    dto: {
                        primaryPropertyId: primaryColumn?.id!,
                        properties: selectedColumns.map(
                            (c) =>
                                ({
                                    order: c.order,
                                    id: c.id,
                                } satisfies OrderedReportProperty)
                        ),
                        dateFrom: moment(from).format('YYYY-MM-DDTHH:mm:ss.SSS'),
                        dateTo: moment(to).format('YYYY-MM-DDTHH:mm:ss.SSS'),
                    },
                });
            } else {
                const requestData = {
                    primaryPropertyId: primaryColumn?.id!,
                    properties: selectedColumns.map(
                        (c) =>
                            ({
                                order: c.order,
                                id: c.id,
                            } as OrderedReportProperty)
                    ),
                    dateFrom: moment(from).format('YYYY-MM-DDTHH:mm:ss.SSS'),
                    dateTo: moment(to).format('YYYY-MM-DDTHH:mm:ss.SSS'),
                };
                const shopIds = selectedLocations || [];
                return !isEnterprise
                    ? ReportsAPI.getCustomReportPreview(requestData)
                    : ReportsAPI.getEnterpriseCustomReportPreviewRequest({
                          ...requestData,
                          shopIds,
                      });
            }
        },
        {
            enabled: primaryColumn?.id !== undefined,
        }
    );

    useEffect(() => {
        if (!previewData) return;
        const properties = previewData.properties.sort(
            (s1: OrderedReportProperty, s2: OrderedReportProperty) => s1.order! - s2.order!
        );
        const transformedData = previewData.dataTable.map((row) =>
            transformPreviewRowData(row, properties)
        );

        dispatch(customReportActions.setPreviewGridData(transformedData));
    }, [dispatch, previewData]);
}

function transformPreviewRowData(row: CellData[], properties: OrderedReportProperty[]) {
    const rowData: Record<string, string> = {};
    row.forEach((cell, index) => {
        const propertyId = properties[index].id;
        rowData[propertyId] = cell.value ?? '';
    });
    return rowData;
}

const DivTableHeaderText = styled('div')(() => ({
    color: '#5C6477',
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: 700,
    overflowWrap: 'anywhere',
}));

const TableCellHeader = styled(TableCell)(() => ({
    backgroundColor: '#F6F6F6',
    width: 180,
    minWidth: 180,
    maxWidth: 180,
    textAlign: 'center',
    borderRight: '1px solid var(--neutral3)',
}));

const TableContainerStyles = styled(TableContainer)(() => ({
    border: '1px solid #DBDCDD',
    boxSizing: 'border-box',
    borderRadius: '12px 12px 12px 12px',
    height: '100%',
    width: '100%',
})) as typeof TableContainer;

const SpanTotalResultText = styled('span')(() => ({
    fontSize: 11,
    color: 'var(--neutral6)',
    fontWeight: 400,
}));

const StyledTypography = styled(Typography)(() => ({
    fontFamily: 'Roboto',
    color: 'var(--neutral7)',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));
