import React, { useState } from 'react';

import { Draggable } from 'react-beautiful-dnd';
import { DragIndicator } from '@mui/icons-material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Box, Button, styled } from '@mui/material';

type DraggableColumnProps = {
    draggableId: string;
    index: number;
    children: React.ReactNode;
    onDeleteClick: () => void;
};

const getActions = (onDeleteClick: () => void) => {
    return (
        <StyledButton onClick={onDeleteClick}>
            <CloseIcon fill={'#5C6477'} />
        </StyledButton>
    );
};

export default function DraggableColumn({
    draggableId,
    index,
    children,
    onDeleteClick,
    ...otherProps
}: DraggableColumnProps) {
    const [isHovered, setIsHovered] = useState<boolean>(false);

    return (
        <Draggable key={draggableId} draggableId={draggableId} index={index} {...otherProps}>
            {(provided) => (
                <div
                    ref={provided.innerRef}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                >
                    <DivMainBox>
                        <DivInnerContainer>
                            <Box
                                display={'flex'}
                                alignItems={'center'}
                                zIndex={1}
                                width={'100%'}
                                fontFamily={'Roboto'}
                            >
                                {<DragIndicator sx={{ cursor: 'grab' }} />}
                                <DivEllipsis>{children}</DivEllipsis>
                                <DivJustifyEnd>
                                    {isHovered && getActions(onDeleteClick)}
                                </DivJustifyEnd>
                            </Box>
                        </DivInnerContainer>
                    </DivMainBox>
                </div>
            )}
        </Draggable>
    );
}

const DivMainBox = styled('div')(() => ({
    boxSizing: 'border-box',
    width: '100%',
    '&:hover': {
        backgroundColor: '#EAE9FF!important',
    },
}));

const DivEllipsis = styled('div')({
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    width: 181,
});

const DivInnerContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    height: '30px!important',
    padding: 0,
    gap: 0,
    width: '100%',
    backgroundColor: 'white',
    position: 'relative',
    '&:hover': {
        '&::after': {
            content: '""',
            position: 'absolute',
            top: -2,
            left: -20,
            width: '106%',
            height: '112%',
            right: 0,
            bottom: 0,
            backgroundColor: '#EAE9FF',
        },
    },
});

const DivJustifyEnd = styled('div')({
    marginLeft: 'auto',
    minWidth: 'fit-content',
    alignSelf: 'baseline',
    marginTop: -6,
});

const StyledButton = styled(Button)({
    '&.MuiButton-root': {
        minWidth: 0,
        height: 30,
        marginRight: 10,
        marginTop: 5,
        padding: '0 !important',
    },
});
