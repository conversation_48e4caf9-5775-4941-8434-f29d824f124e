import React, { useState } from 'react';

import { styled } from '@mui/material';
import TextField from 'common/components/Inputs/TextField';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import useToasters from 'common/hooks/useToasters';
import { Report } from 'api/Reports';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

type SaveReportModalProps = {
    open: boolean;
    onClose: () => void;
    selectedReport: Report | null;
    checkNameIsInvalid: (reportName: string) => boolean;
    onConfirm: (reportName: string) => void;
};

export default function SaveReportModal({
    open,
    onClose,
    checkNameIsInvalid,
    selectedReport,
    onConfirm,
}: SaveReportModalProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [reportName, setReportName] = useState<string>('');
    const [cancelSaveModalOpen, setCancelSaveModalOpen] = useState<boolean>(false);
    const [nameIsInvalid, setNameIsInvalid] = useState<boolean>(false);

    function handleConfirm() {
        onConfirm(reportName);
    }

    function handleClose() {
        setNameIsInvalid(false);
        setReportName('');
        setCancelSaveModalOpen(false);
        onClose();
    }

    function handleClickGoBack() {
        if (reportName.length < 1) {
            handleClose();
            return;
        }
        setCancelSaveModalOpen(true);
    }

    function handleOnInputChange(event: React.ChangeEvent<{ value: string }>) {
        const reportName = event.target.value;
        const isInvalid = checkNameIsInvalid(reportName);
        setNameIsInvalid(isInvalid);
        if (isInvalid) {
            toasters.danger(t('reports.nameAlreadyExisting'), t('reports.reportTheSameName'));
        }
        setReportName(reportName);
    }

    const CancelSaveReportModalContent: React.FC = () => {
        return (
            <div>
                <DivModalCancelConfirmationHeader>
                    {selectedReport
                        ? t('reports.cancelCustomReportEditing')
                        : t('reports.cancelCustomReportCreation')}
                </DivModalCancelConfirmationHeader>
                <DivModalCancelConfirmationText>
                    {selectedReport
                        ? t('reports.unsavedChangesWillBeLost')
                        : t('reports.cancelCreationText')}
                </DivModalCancelConfirmationText>
            </div>
        );
    };

    return (
        <Modal open={open} onClose={handleClickGoBack}>
            <DivModalPaper>
                <DivModalHeaderContainer>
                    <DivModalHeader>{t('reports.saveCustomReport')}</DivModalHeader>
                    <DivModalActions>
                        <Button
                            sx={{ width: 164 }}
                            color={'var(--neutral3)'}
                            onClick={handleClickGoBack}
                        >
                            {t('reports.cancel')}
                        </Button>

                        <Button
                            sx={{ width: 164 }}
                            color={'#36CE91'}
                            disabled={
                                reportName.length < 1 || nameIsInvalid || reportName.length > 180
                            }
                            onClick={handleConfirm}
                        >
                            {t('reports.saveReport')}
                        </Button>
                    </DivModalActions>
                </DivModalHeaderContainer>
                <DivModalContent>
                    <DivLabel>{t('reports.name')}</DivLabel>
                    <TextField
                        name={'reports.email'}
                        sx={{ width: 476 }}
                        value={reportName}
                        maxLength={180}
                        isInvalid={nameIsInvalid || reportName.length > 180}
                        showValidationIndicators={true}
                        placeholder={t('reports.enterTheNameOfTheCustomReport')}
                        onChange={handleOnInputChange}
                    />

                    <DivInfoContent>
                        {nameIsInvalid
                            ? t(
                                  'reports.thereIsAlreadyAReportWithTheSameNamePleaseWriteAnotherName'
                              )
                            : t('reports.thisReportWillBeSavedForEasyFutureReference')}
                    </DivInfoContent>
                </DivModalContent>
                <ConfirmationModal
                    open={cancelSaveModalOpen}
                    onClose={() => setCancelSaveModalOpen(false)}
                    onConfirmation={handleClose}
                    confirmationCaptionButton={t('reports.yesCancel')}
                    cancelCaptionButton={t('reports.goBack')}
                >
                    <CancelSaveReportModalContent />
                </ConfirmationModal>
            </DivModalPaper>
        </Modal>
    );
}

const DivModalHeader = styled('div')(() => ({
    color: 'var(--neutral9)',
    fontFamily: 'Inter',
    fontSize: 18,
    fontWeight: 700,
}));

const DivModalHeaderContainer = styled('div')(() => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 31,
}));

const DivModalPaper = styled('div')(() => ({
    borderRadius: '10px',
    padding: '33px 64px 37px 49px',
    width: 791,
    height: 'auto',
    '&:focus:not(:focus-visible)': {
        outline: 'none',
    },
    '&:focus-visible': {
        outline: 'none',
    },
}));

const DivModalContent = styled('div')(() => ({}));

const DivInfoContent = styled('div')(() => ({
    marginTop: 10,
    display: 'flex',
    gap: 5,
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: '11px',
    fontStyle: 'normal',
    fontWeight: 400,
    lineHeight: 'normal',
    color: '#6A6E72',
}));

const DivModalActions = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'center',
    gap: 12,
}));

const DivModalCancelConfirmationHeader = styled('div')(() => ({
    color: 'var(--neutral8)',
    fontFamily: 'Inter',
    fontSize: 18,
    fontWeight: 700,
}));

const DivModalCancelConfirmationText = styled('div')(() => ({
    color: 'var(--neutral7)',
    fontFamily: 'Roboto',
    fontSize: 11,
    fontWeight: 400,
    marginTop: 17,
}));

const DivLabel = styled('div')(() => ({
    color: 'var(--neutral7)',
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: 700,
    marginBottom: 7,
    '::after': {
        content: "'*'",
        color: 'var(--cm3)',
        marginLeft: '4px',
    },
}));
