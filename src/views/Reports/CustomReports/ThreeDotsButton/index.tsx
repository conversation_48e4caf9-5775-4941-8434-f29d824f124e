import { MoreVert } from '@mui/icons-material';
import {
    IconButton,
    List,
    ListItemText,
    listItemTextClasses,
    Popover,
    styled,
} from '@mui/material';
import { Report } from 'api/Reports';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { useBlocker } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import {
    resetReportChanges,
    setPrimaryColumn,
    setSelectedColumns,
} from 'store/slices/customReport';
import {
    selectPrimaryColumn,
    selectSelectedColumns,
    selectSelectedReport,
} from 'store/slices/customReport/selectors';
import { setHasUnsavedChanges } from 'store/slices/leavePageConfirmation';
import LeavePageConfirmationPopup from '../../../../common/components/Popups/LeavePageConfirmationPopup';
import SaveReportModal from './SaveReportModal';

type ThreeDotsButtonProps = {
    onClickNewReport: () => void;
    saveCustomReport: (name?: string) => void;
    reports: Report[];
};

export default function ThreeDotsButton({
    onClickNewReport,
    saveCustomReport,
    reports,
}: ThreeDotsButtonProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const selectedReport = useAppSelector(selectSelectedReport);
    const selectedColumns = useAppSelector(selectSelectedColumns);
    const primaryColumn = useAppSelector(selectPrimaryColumn);
    const primaryColumnIsSelected = useAppSelector((r) => !!selectPrimaryColumn(r));

    const [threeDotsButtonAnchorEl, setThreeDotsButtonAnchorEl] = useState<SVGSVGElement | null>(
        null
    );
    const threeDotsOpen = Boolean(threeDotsButtonAnchorEl);

    const [cancelEditReportModalIsOpen, setCancelEditReportModalIsOpen] = useState<boolean>(false);
    const [saveReportModalIsOpen, setSaveReportModalIsOpen] = useState<boolean>(false);
    const [isColumnStateChanged, setIsColumnStateChanged] = useState<boolean | null>(null);
    const [showBlockLeavingConfirmation, setShowBlockLeavingConfirmation] =
        useState<boolean>(false);

    useEffect(() => {
        const isChanged = calcIsColumnStateChanged();
        setIsColumnStateChanged(isChanged);
        dispatch(setHasUnsavedChanges(isChanged));

        if (isChanged) {
            window.onbeforeunload = () => true;
        } else {
            window.onbeforeunload = null;
        }
        return () => {
            window.onbeforeunload = null;
        };
    }, [calcIsColumnStateChanged(), selectedColumns]);

    const blocker = useBlocker(isColumnStateChanged === true);
    if (blocker.state === 'blocked' && !showBlockLeavingConfirmation) {
        setShowBlockLeavingConfirmation(true);
    }

    function handleThreeDotSaveClick() {
        if (selectedReport) {
            saveCustomReport();
        } else {
            setSaveReportModalIsOpen(true);
        }
    }

    function nameAlreadyExist(reportName: string) {
        return reports
            .flatMap((x) => x.name.toLowerCase().trim())
            .includes(reportName.toLowerCase().trim());
    }

    function calcIsColumnStateChanged(): boolean {
        const initialStateColumns =
            reports
                .find((report) => report.id === selectedReport?.id)
                ?.columns.filter((item) => item.order !== 0)
                .map(({ isPrimary, name, ...rest }) => rest) ?? [];

        const currentStateColumns = selectedColumns.map(({ isPrimary, name, ...rest }) => rest);

        const initialPrimaryColumn = reports
            .find((report) => report.id === selectedReport?.id)
            ?.columns.find((item) => item.order === 0);

        if (initialPrimaryColumn && initialPrimaryColumn.id !== primaryColumn?.id) {
            return true;
        }

        if (initialStateColumns.length !== currentStateColumns.length) {
            return true;
        }

        const sortedInitialStateColumns = [...initialStateColumns].sort(
            (a, b) => a.order - b.order
        );
        const sortedCurrentStateColumns = [...currentStateColumns].sort(
            (a, b) => a.order - b.order
        );

        return sortedInitialStateColumns.some(
            (col, index) => JSON.stringify(col) !== JSON.stringify(sortedCurrentStateColumns[index])
        );
    }

    function onSaveCustomReport(name: string) {
        setSaveReportModalIsOpen(false);
        setThreeDotsButtonAnchorEl(null);
        saveCustomReport(name);
    }

    function handleMoreVertClick(event: React.MouseEvent<SVGSVGElement>) {
        setThreeDotsButtonAnchorEl(threeDotsButtonAnchorEl ? null : event.currentTarget);
    }

    function handleThreeDotCancelClick() {
        setCancelEditReportModalIsOpen(true);
        setThreeDotsButtonAnchorEl(null);
    }

    function cancelThreeDtoCancel() {
        if (selectedReport) {
            const initialStateColumns = structuredClone(reports).find(
                (report) => report.id === selectedReport?.id
            )?.columns;
            dispatch(setSelectedColumns(initialStateColumns?.filter((col) => col.order !== 0)!));
            dispatch(setPrimaryColumn(initialStateColumns?.find((col) => col.order === 0)!));
        } else {
            dispatch(setPrimaryColumn(null));
            dispatch(setSelectedColumns([]));
        }
    }

    return (
        <>
            <IconButton disabled={!primaryColumnIsSelected}>
                <MoreVert
                    sx={{ marginTop: -0.5, width: '100%' }}
                    onClick={(e: React.MouseEvent<SVGSVGElement>) => {
                        handleMoreVertClick(e);
                    }}
                />
            </IconButton>
            <Popover
                sx={{
                    transform: 'translate(-32px, 10px)',
                }}
                slotProps={{
                    paper: {
                        sx: {
                            display: 'flex',
                            flexDirection: 'column',
                            width: 154,
                        },
                    },
                }}
                open={threeDotsOpen}
                anchorEl={threeDotsButtonAnchorEl}
                onClose={() => setThreeDotsButtonAnchorEl(null)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
                <StyledList>
                    <StyledListItem
                        onClick={handleThreeDotSaveClick}
                        disabled={!isColumnStateChanged}
                    >
                        <StyledListItemText>
                            {selectedReport ? t('reports.saveChanges') : t('reports.saveReport')}
                        </StyledListItemText>
                    </StyledListItem>
                    <StyledListItem onClick={onClickNewReport}>
                        <StyledListItemText>{t('reports.newReport')}</StyledListItemText>
                    </StyledListItem>
                    <StyledListItem
                        onClick={handleThreeDotCancelClick}
                        disabled={!isColumnStateChanged}
                    >
                        <StyledListItemText>
                            {selectedReport
                                ? t('reports.cancelEdition')
                                : t('reports.cancelCreation')}
                        </StyledListItemText>
                    </StyledListItem>
                </StyledList>

                <SaveReportModal
                    open={saveReportModalIsOpen}
                    onClose={() => {
                        setSaveReportModalIsOpen(false);
                    }}
                    selectedReport={selectedReport}
                    onConfirm={(name: string) => onSaveCustomReport(name)}
                    checkNameIsInvalid={(stringName: string) => nameAlreadyExist(stringName)}
                />
            </Popover>
            <ConfirmationModal
                onConfirmation={() => {
                    setCancelEditReportModalIsOpen(false);
                    cancelThreeDtoCancel();
                    if (!selectedReport) onClickNewReport();
                }}
                confirmationCaptionButton={t('reports.yesCancel')}
                cancelCaptionButton={t('reports.goBack')}
                onClose={() => setCancelEditReportModalIsOpen(false)}
                open={cancelEditReportModalIsOpen}
            >
                {selectedReport
                    ? t('reports.cancelCustomReportEdition')
                    : t('reports.cancelCustomReportCreation')}
                <CancelText>
                    {selectedReport
                        ? t('reports.ifYouCancel')
                        : t('reports.ifYouCancelYouWillLoseTheLatestChangesYouMade')}
                </CancelText>
            </ConfirmationModal>
            <LeavePageConfirmationPopup
                open={showBlockLeavingConfirmation}
                onConfirm={() => {
                    setShowBlockLeavingConfirmation(false);
                    dispatch(resetReportChanges());
                    blocker.proceed?.();
                }}
                onClose={() => {
                    setShowBlockLeavingConfirmation(false);
                    blocker.reset?.();
                }}
            />
        </>
    );
}

const StyledList = styled(List)({
    padding: 0,
});

const CancelText = styled('div')({
    marginTop: 10,
    marginBottom: -10,
    fontSize: 11,
    fontWeight: 400,
    fontFamily: 'Roboto',
    color: 'var(--neutral7)',
});

const StyledListItem = styled(SMenuItem2)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'bold',
    padding: '8px 16px',
}));

const StyledListItemText = styled(ListItemText)(({ theme }) => ({
    [`& .${listItemTextClasses.primary}`]: {
        fontSize: 14,
        fontFamily: 'Inter',
    },
}));
