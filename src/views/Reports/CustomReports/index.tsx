import { Box, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import ReportsAPI, {
    CreateCustomReportRequest,
    CustomReportColumnType,
    OrderedReportProperty,
    Report,
    ReportColumn,
    UpdateCustomReportRequest,
} from 'api/Reports';
import { Button } from 'common/components/Button';
import DateField from 'common/components/Inputs/DateField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { useSelector, useStore } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import {
    setFrom,
    setPrimaryColumn,
    setSelectedColumns,
    setSelectedReport,
    setTo,
} from 'store/slices/customReport';
import {
    selectPrimaryColumn,
    selectSelectedColumns,
    selectSelectedReport,
} from 'store/slices/customReport/selectors';
import { selectIsDownloading } from 'store/slices/downloader/selectors';
import { initiateReportDownload } from 'store/slices/downloader/thunks/downloadReport';
import { selectFilters } from 'store/slices/enterprise/reports';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import theme from 'theme';
import { WidePageLayout } from 'views/Components/Page';
import { useHeaderLoading } from 'views/HeaderBar';
import CustomReportAutocomplete from '../../Components/CustomReportAutocomplete';
import CustomReportColumns from './CustomReportColumns';
import CustomReportDataProvider, { useCustomReportData } from './CustomReportDataProvider';
import CustomReportLocationSelector from './CustomReportLocationSelector';
import CustomReportPreview from './CustomReportPreview';
import { PleaseSelectColumnsSvg } from './PleaseSelectColumnsSvg';
import ThreeDotsButton from './ThreeDotsButton';

const DEBUG_PRODUCTION_SHOP_KEY = 'caecda63-252e-4ebc-b188-980856eec249'; // QA Automation team production account https://smokenew.clearmechanic.com
const USE_PRODUCTION_DATABASE = false;

export default function CustomReportsTab() {
    return (
        <CustomReportDataProvider>
            <CustomReportTabInner />
        </CustomReportDataProvider>
    );
}

function CustomReportTabInner() {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const dispatch = useAppDispatch();
    const isEnterprise = useIsEnterpriseRoute();

    const store = useStore();
    const selectedReport = useSelector(selectSelectedReport); // selected saved reports
    const primaryColumnIsSelected = useAppSelector((r) => !!selectPrimaryColumn(r));
    const { locations: selectedLocations } = useAppSelector(selectFilters);
    const isDownloading = useAppSelector(selectIsDownloading);
    const fileDateFormat = useAppSelector((r) =>
        selectSettings(r).internationalization.language === 'en' ? 'MM-DD-YYYY' : 'DD-MM-YYYY'
    );

    const { columnGroups, from, to } = useCustomReportData();

    //
    // Everything related to saved reports
    //
    const [_reports, setReports] = useState<Report[]>([]); // saved reports
    const { data: reportsData, isLoading: reportsLoading } = useQuery({
        queryKey: ['saved-reports'],
        queryFn: () =>
            isEnterprise
                ? ReportsAPI.getEnterpriseSavedCustomReports()
                : ReportsAPI.getSavedCustomReports(),
    });
    useHeaderLoading(reportsLoading);
    useEffect(() => {
        if (reportsData) {
            setReports(reportsData);
        }
    }, [reportsData]);
    // to fill column names from ColumnGroups to SavedReport
    const reports = useMemo((): Report[] => {
        return _reports.map((report) => {
            return {
                ...report,
                columns: report.columns
                    .map((column) => {
                        const matchingColumn = columnGroups
                            ?.flatMap((group) => group.columns)
                            ?.find((x) => x.id === column.id);

                        if (matchingColumn) {
                            return {
                                ...column,
                                name: matchingColumn.name,
                                columnType: matchingColumn.columnType,
                            };
                        } else {
                            return null;
                        }
                    })
                    .filter((column): column is ReportColumn => column !== null),
            };
        });
    }, [columnGroups, _reports]);
    // useEffect(() => {
    //     if (!columnGroups) return;
    //     const reportsWithFilledColumnInfo = fillColumnNames();
    //     setReports(reportsWithFilledColumnInfo);
    // }, [columnGroups, fillColumnNames]);
    function getReportFileName(startDate: Date, endDate: Date, reportName?: string) {
        const startDateFormatted = moment(startDate).format(fileDateFormat);
        const endDateFormatted = moment(endDate).format(fileDateFormat);
        const reportNameString = reportName ? ` ${reportName} -` : '';
        return `${t(
            'reports.CustomReportFileName'
        )} - ${reportNameString} ${startDateFormatted} - ${endDateFormatted}.xlsx`;
    }
    const reportNameMutation = useMutation(async (updatedReport: Report) => {
        const isSuccess = isEnterprise
            ? await ReportsAPI.renameEnterpriseCustomReport(updatedReport.id, updatedReport.name)
            : await ReportsAPI.renameCustomReport(updatedReport.id, updatedReport.name);
        if (isSuccess) {
            const updatedReports = reports.map((report) => {
                if (report.id === updatedReport.id) {
                    return { ...report, name: updatedReport.name };
                }
                return report;
            });
            setReports(updatedReports);
            if (updatedReport.id === selectedReport?.id) {
                dispatch(setSelectedReport(updatedReport));
            }
        }
    });

    const downloadReportMutation = useMutation(async () => {
        if (from === null || to === null) {
            throw new Error(
                'The date range is not valid. Please provide both a start and end date.'
            );
        }

        const reportName = selectedReport?.name;
        const fileName = getReportFileName(from, to, reportName);
        const shopIds = selectedLocations || [];
        const selectedColumns = selectSelectedColumns(store.getState());
        const primaryColumn = selectPrimaryColumn(store.getState());
        const requestData = {
            primaryPropertyId: primaryColumn?.id!,
            properties: selectedColumns.map(
                (c) =>
                    ({
                        id: c.id,
                        order: c.order,
                    } as OrderedReportProperty)
            ),
            reportName: reportName,
            dateFrom: moment(from).format('YYYY-MM-DDTHH:mm:ss.SSS'),
            dateTo: moment(to).format('YYYY-MM-DDTHH:mm:ss.SSS'),
        };

        dispatch(
            initiateReportDownload({
                requestData: requestData,
                isEnterprise: isEnterprise,
                fileName: fileName,
                shopIds: shopIds,
                isDebug: USE_PRODUCTION_DATABASE,
                prodShopKey: DEBUG_PRODUCTION_SHOP_KEY,
            })
        );
    });

    async function onDeleteReport(report: Report) {
        const isSuccess = isEnterprise
            ? await ReportsAPI.deleteEnterpriseCustomReport(report.id)
            : await ReportsAPI.deleteCustomReport(report.id);
        if (isSuccess) {
            const updatedReports = reports.filter((item) => item.id !== report.id);
            if (report.id === selectedReport?.id) {
                dispatch(setSelectedReport(null));
            }
            setReports(updatedReports);
            toasters.success(report.name, t('reports.reportDeleted'));
        }
    }

    function onClearSelectedReport() {
        dispatch(setPrimaryColumn(null));
        dispatch(setSelectedColumns([]));
        dispatch(setSelectedReport(null));
    }

    function onSelectReport(report: Report | undefined) {
        if (report) dispatch(setSelectedReport(report));

        const primaryColumnInfo = reports
            .find((x) => x.id === report?.id)
            ?.columns.find((x) => x.isPrimary)!;

        const primaryColumnName = columnGroups
            ?.flatMap((x) => x.columns)
            .find((col) => col.id === primaryColumnInfo.id)?.name!;

        const primaryColumn: ReportColumn = {
            id: primaryColumnInfo?.id,
            order: primaryColumnInfo?.order,
            isPrimary: true,
            name: primaryColumnName,
            columnType: primaryColumnInfo.columnType,
        };
        dispatch(setPrimaryColumn(primaryColumn));

        const selectedColumnInfo = reports
            .find((x) => x.id === report?.id)
            ?.columns.filter((x) => !x.isPrimary)!;

        const selectedColumns: ReportColumn[] = selectedColumnInfo?.map((column) => {
            return {
                id: column?.id,
                order: column?.order,
                isPrimary: false,
                name: column.name,
                columnType: column.columnType,
            };
        });

        dispatch(setSelectedColumns(selectedColumns ?? []));
    }

    function onSaveReport(reportName?: string) {
        if (selectedReport) {
            updateCustomReport.mutate();
        } else {
            createCustomReport.mutate(reportName!);
        }
    }

    const createCustomReport = useMutation(async (reportName: string) => {
        const selectedColumns = selectSelectedColumns(store.getState());
        const primaryColumn = selectPrimaryColumn(store.getState());
        const request: CreateCustomReportRequest = {
            name: reportName,
            primaryPropertyId: primaryColumn!.id,
            columns: selectedColumns.map(
                (c) =>
                    ({
                        id: c.id,
                        order: c.order,
                        name: c.name,
                        columnType: c.columnType,
                        isPrimary: c.isPrimary,
                    } satisfies ReportColumn)
            ),
        };

        const createReport = isEnterprise
            ? ReportsAPI.createEnterpriseCustomReport
            : ReportsAPI.createCustomReport;

        const newReportId = await createReport(request);

        if (newReportId) {
            const newReport: Report = {
                id: newReportId,
                name: reportName,
                columns: [
                    ...request.columns,
                    {
                        id: primaryColumn!.id,
                        order: 0,
                        isPrimary: true,
                        name: primaryColumn!.name,
                        columnType: CustomReportColumnType.String,
                    },
                ],
            };
            const newReports = [...reports, newReport];
            setReports(newReports);
            dispatch(setSelectedReport(newReport));
            toasters.success(newReport.name, t('reports.savedReport'));
        }
    });
    const updateCustomReport = useMutation(async () => {
        const selectedColumns = selectSelectedColumns(store.getState());
        const primaryColumn = selectPrimaryColumn(store.getState());
        const request: UpdateCustomReportRequest = {
            id: selectedReport?.id!,
            primaryPropertyId: primaryColumn!.id,
            columns: selectedColumns.map(
                (c) =>
                    ({
                        id: c.id,
                        order: c.order,
                        name: c.name,
                        columnType: c.columnType,
                        isPrimary: c.isPrimary,
                    } satisfies ReportColumn)
            ),
        };
        const updatedReport = isEnterprise
            ? await ReportsAPI.updateEnterpriseCustomReport(request)
            : await ReportsAPI.updateCustomReport(request);
        if (updatedReport) {
            const updatedReports = reports.map((report) => {
                if (report.id === updatedReport.id) {
                    return {
                        ...report,
                        columns: [
                            ...request.columns,
                            {
                                id: primaryColumn!.id,
                                order: 0,
                                isPrimary: true,
                                name: primaryColumn!.name,
                                columnType: CustomReportColumnType.String,
                            },
                        ],
                    };
                }
                return report;
            });

            setReports(updatedReports);
            toasters.success(updatedReport.name, t('reports.updatedReport'));
        }
    });

    return (
        <>
            <WidePageLayout marginTop>
                <DivInputRoot>
                    <DivInputContainer>
                        <Box display="flex" flexWrap="wrap" flexDirection="row" gap="12px">
                            <DateField
                                name="from"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '& fieldset': {
                                            borderColor: 'var(--cm1)',
                                        },
                                    },
                                    width: 160,
                                }}
                                format={`'${t('reports.start')}': dd/MM/yy`}
                                variant="rounded"
                                enableTextInput
                                value={from}
                                onChange={(newFrom) =>
                                    dispatch(setFrom(newFrom ? newFrom.toISOString() : null))
                                }
                                enableEnterComplete={true}
                            />
                            <DateField
                                name="to"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '& fieldset': {
                                            borderColor: 'var(--cm1)',
                                        },
                                    },
                                    width: 160,
                                }}
                                format={`'${t('reports.end')}': dd/MM/yy`}
                                variant="rounded"
                                enableTextInput
                                value={to}
                                onChange={(newTo) =>
                                    dispatch(setTo(newTo ? newTo.toISOString() : null))
                                }
                                enableEnterComplete={true}
                            />
                            {isEnterprise && (
                                <Box sx={{ width: 'auto', minWidth: 250 }}>
                                    <CustomReportLocationSelector />
                                </Box>
                            )}
                            <Box sx={{ width: 'auto', minWidth: 350 }}>
                                <CustomReportAutocomplete
                                    disabled={reportsLoading}
                                    savedReports={reports}
                                    onSave={(updatedReport) =>
                                        reportNameMutation.mutate(updatedReport)
                                    }
                                    onDelete={(report) => onDeleteReport(report)}
                                    onSelect={(report) => {
                                        onSelectReport(report);
                                    }}
                                    onClear={onClearSelectedReport}
                                />
                            </Box>
                        </Box>
                        <Box display="flex" justifyContent="flex-end">
                            <Button
                                sx={{ width: 164 }}
                                cmosVariant={'filled'}
                                cmosSize={'medium'}
                                label={t('reports.downloadReport')}
                                onClick={() => downloadReportMutation.mutate()}
                                disabled={isDownloading || !primaryColumnIsSelected}
                                showLoader={isDownloading}
                            />

                            <ThreeDotsButton
                                reports={reports}
                                onClickNewReport={onClearSelectedReport}
                                saveCustomReport={(name?: string) => onSaveReport(name)}
                            />
                        </Box>
                    </DivInputContainer>
                </DivInputRoot>

                <DivLayout>
                    <CustomReportColumns />
                    <Box>
                        <DivInformativeBoxContainer>
                            <Trans i18nKey="reports.informativebox" t={t} />
                        </DivInformativeBoxContainer>
                        <DivPreviewContainer>
                            {!primaryColumnIsSelected ? (
                                <DivEmptyPreviewBox>
                                    <PleaseSelectColumnsSvg />
                                    <DivEmptyPreviewBoxTitle>
                                        {t('reports.createReportImage')}
                                    </DivEmptyPreviewBoxTitle>
                                    <DivEmptyPreviewBoxCaption>
                                        {t('reports.pleaseSelectColumnsImage')}
                                    </DivEmptyPreviewBoxCaption>
                                </DivEmptyPreviewBox>
                            ) : (
                                <CustomReportPreview />
                            )}
                        </DivPreviewContainer>
                    </Box>
                </DivLayout>
            </WidePageLayout>
        </>
    );
}

const DivInputContainer = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'space-between',
}));

const DivInputRoot = styled('div')(() => ({
    minHeight: 90,
    padding: '28px 12px 23px 15px',

    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: 12,

    backgroundColor: 'white',
}));

const DivInformativeBoxContainer = styled('div')(() => ({
    fontFamily: 'Inter',
    fontSize: 12,
    borderRadius: 12,
    padding: '9px 0 9px 19px',
    color: 'var(--cm1)',
    backgroundColor: '#0069FF1A',
}));

const DivEmptyPreviewBox = styled('div')(() => ({
    display: 'flex',
    flexDirection: 'column',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    gap: 8,

    [theme.breakpoints.down(1600)]: {
        transform: 'scale(0.7)',
    },

    [theme.breakpoints.down(1536)]: {
        transform: 'scale(0.5)',
    },
}));

const DivEmptyPreviewBoxTitle = styled('div')(() => ({
    color: 'var(--cm1)',
    fontFamily: 'Inter',
    marginTop: 37,
    fontSize: 34,
    fontWeight: 700,
}));

const DivEmptyPreviewBoxCaption = styled('div')(() => ({
    color: 'var(--neutral7)',
    fontFamily: 'Inter',
    marginTop: 13,
    fontSize: 18,
}));

const DivPreviewContainer = styled('div')(() => ({
    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: 12,

    backgroundColor: `${theme.palette.primary.contrastText}`,

    height: 'calc(100vh - 302px)',
    marginTop: 8,
}));

const DivLayout = styled('div')(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: '340px minmax(0, 1fr)',
    marginTop: 8,
    gap: '8px',

    [theme.breakpoints.down(1000)]: {
        gridTemplateColumns: '1fr',
        gridTemplateRows: 'minmax(0, 400px) 1fr',
    },
}));
