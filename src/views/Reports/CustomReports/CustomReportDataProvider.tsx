import { useQuery } from '@tanstack/react-query';
import ReportsAPI, { CustomReportColumnType, ReportColumnGroup } from 'api/Reports';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { createContext, useContext, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectFrom, selectTo, selectPrimaryColumn } from 'store/slices/customReport/selectors';
import { selectFilters } from 'store/slices/enterprise/reports';
import { useHeaderLoading } from 'views/HeaderBar';

export type CustomReportData = {
    columnGroups: ReportColumnGroup[];
    isLoading: boolean;
    isFetched: boolean;
    from: Date | null;
    to: Date | null;
};

const CustomReportDataContext = createContext<CustomReportData | null>(null);

export function useCustomReportData(): CustomReportData {
    const context = useContext(CustomReportDataContext);
    if (!context)
        throw new Error('useCustomReportData must be used within a CustomReportDataProvider');
    return context;
}

/**
 * Provides a context with the custom report data
 * that cannot be derived using selectors.
 */
export default function CustomReportDataProvider({ children }: React.PropsWithChildren) {
    const { t } = useAppTranslation();
    const isEnterprise = useIsEnterpriseRoute();

    const { locations: selectedLocations } = useAppSelector(selectFilters);
    const fromDate = useAppSelector(selectFrom);
    const toDate = useAppSelector(selectTo);
    const primaryColumn = useAppSelector(selectPrimaryColumn);

    const from = useMemo(() => (fromDate ? new Date(fromDate) : null), [fromDate]);
    const to = useMemo(() => (toDate ? new Date(toDate) : null), [toDate]);

    const [isFetchedOnce, setIsFetchedOnce] = useState(false);

    const { data: columnGroupsData, isLoading } = useQuery({
        keepPreviousData: true,
        queryKey: ['custom-report-columns', from, to, primaryColumn],
        queryFn: async () => {
            if (from === null || to === null) {
                throw new Error(
                    'The date range is not valid. Please provide both a start and end date.'
                );
            }
            const requestParams = {
                dateFrom: from.toISOString(),
                dateTo: to.toISOString(),
                primaryPropertyId: primaryColumn?.id,
            };
            const result = !isEnterprise
                ? await ReportsAPI.getCustomReportColumns(requestParams)
                : await ReportsAPI.getEnterpriseCustomReportColumns(selectedLocations);
            if (!isFetchedOnce) {
                setIsFetchedOnce(true);
            }
            return result;
        },
    });
    useHeaderLoading(isLoading);

    const columnGroups: ReportColumnGroup[] = useMemo(() => {
        if (!columnGroupsData) return [];
        const handleSurveysColumnName = (name: string) => {
            const answerMatch = name.match(/^SurveysAnswer_(\d+)$/);
            if (answerMatch) {
                return `${t('reports.Answer')} ${answerMatch[1]}`;
            }

            const questionMatch = name.match(/^SurveysQuestion_(\d+)$/);
            if (questionMatch) {
                return `${t('reports.Question')} ${questionMatch[1]}`;
            }

            return name;
        };
        const handleChecklistsColumnName = (name: string) => {
            const checklistMatch = name.match(/^ChecklistNumber_(\d+)$/);
            if (checklistMatch) {
                return `${t('reports.ChecklistNumber')}${checklistMatch[1]}`;
            }

            const itemsInChecklistMatch = name.match(/^ItemsInChecklistNumber_(\d+)$/);
            if (itemsInChecklistMatch) {
                return `${t('reports.ItemsInChecklistNumber')}${itemsInChecklistMatch[1]}`;
            }

            return name;
        };

        // translation
        return columnGroupsData.map((group) => ({
            ...group,
            name: t('reports.' + group.name),
            columns: group.columns.map((column) => {
                let translatedName = t('reports.' + column.name, { defaultValue: column.name });

                switch (column.columnType) {
                    case CustomReportColumnType.TimeInPhase:
                        translatedName = t('reports.timeInPhase', { phaseName: translatedName });
                        break;
                    case CustomReportColumnType.Survey:
                        translatedName = handleSurveysColumnName(column.name);
                        break;
                    case CustomReportColumnType.Checklist:
                        translatedName = handleChecklistsColumnName(column.name);
                        break;
                    default:
                        break;
                }

                return {
                    ...column,
                    name: translatedName,
                };
            }),
        }));
    }, [columnGroupsData, t]);

    const customReportData: CustomReportData = useMemo(
        () => ({ columnGroups, from, to, isLoading, isFetched: isFetchedOnce }),
        [columnGroups, from, to, isLoading, isFetchedOnce]
    );

    if (!isFetchedOnce) return null;

    return (
        <CustomReportDataContext.Provider value={customReportData}>
            {children}
        </CustomReportDataContext.Provider>
    );
}
