import { Box, Collapse, formControlLabelClasses, styled, svgIconClasses } from '@mui/material';
import FormControlLabel from '@mui/material/FormControlLabel';
import React, { useMemo, useRef, useState } from 'react';

import { ExpandLess, ExpandMore } from '@mui/icons-material';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import { ReportColumn, ReportColumnGroup } from 'api/Reports';
import InfoTooltip from 'common/components/InfoTooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useSelector } from 'react-redux';
import { useAppDispatch } from 'store';
import {
    setPrimaryColumn,
    setPrimaryColumnsExpanded,
    setSelectedColumns,
} from 'store/slices/customReport';
import {
    selectPrimaryColumn,
    selectPrimaryColumnsExpanded,
    selectSelectedColumns,
} from 'store/slices/customReport/selectors';
import { WarningConfirmationPopup } from '../../../../../common/components/Popups/ConfirmationPopup';

type CustomReportPrimaryColumnsProps = {
    columnGroups: ReportColumnGroup[];
    columnSearchQuery: string;
};

export default function CustomReportPrimaryColumns({
    columnGroups,
    columnSearchQuery,
}: CustomReportPrimaryColumnsProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const selectedColumns = useSelector(selectSelectedColumns);
    const primaryColumn = useSelector(selectPrimaryColumn);
    const isPrimaryExpanded = useSelector(selectPrimaryColumnsExpanded);

    const technicianPrimaryColumnId = 'TechnicianPropertyId';
    const productivityColumnsIds = [
        'WorkshopPlannerAvailableHoursPropertyId',
        'WorkshopPlannerHoursPresentPropertyId',
        'WorkshopPlannerProductiveHoursPropertyId',
        'WorkshopPlannerUnproductiveHoursPropertyId',
        'WorkshopPlannerPercentProductivityPropertyId',
    ];
    const secondaryColumnByPrimaryId: Record<string, string> = {
        OrderIdPropertyId: 'OrderIdPropertyId',
        OrderByClosingDatePropertyId: 'OrderIdPropertyId',
        AppointmentIdPropertyId: 'AppointmentIdPropertyId',
        ServiceAdvisorPropertyId: 'OrdersServiceAdvisorPropertyId',
        TechnicianPropertyId: 'TechnicianPropertyId',
        LocationPropertyId: 'LocationPropertyId',
    };

    // CMOS-3979 requirements point 7
    const [primaryColumnChangeModalIsOpen, setPrimaryColumnChangeModalIsOpen] =
        useState<boolean>(false);
    const prevPrimaryColumn = useRef<ReportColumn | null>(null);
    const nextPrimaryColumn = useRef<ReportColumn | null>(null);

    function handlePrimaryColumnCheck(key: string) {
        nextPrimaryColumn.current = columnGroups
            .flatMap((g) => g.columns)
            .find((c) => c.id === key)!;

        if (key !== prevPrimaryColumn.current?.id && prevPrimaryColumn.current !== null) {
            setPrimaryColumnChangeModalIsOpen(true);
        } else {
            changePrimaryColumn(key);
        }
    }

    function changePrimaryColumn(key: string) {
        if (primaryColumnChangeModalIsOpen) setPrimaryColumnChangeModalIsOpen(false);

        let updatedSelectedColumns = structuredClone(selectedColumns);
        const selectedColumn = columnGroups
            .flatMap((group) => group.columns)
            .find((column) => column.id === key)!;

        const columnCurrentIndex = updatedSelectedColumns.findIndex((col) => col.id === key);
        if (columnCurrentIndex !== -1) {
            updatedSelectedColumns.splice(columnCurrentIndex, 1);
        }

        // Uncheck Productivity columns if not Technician primary column was selected
        if (key !== technicianPrimaryColumnId) {
            updatedSelectedColumns = updatedSelectedColumns.filter(
                (column) => !productivityColumnsIds.includes(column.id)
            );
        }

        // CMOS-3979 requirements point 7
        if (prevPrimaryColumn.current !== null) {
            const secondaryColumnId = secondaryColumnByPrimaryId[prevPrimaryColumn.current.id];
            const existingSecondaryColumnIndex = updatedSelectedColumns.findIndex(
                (c) => c.id === secondaryColumnId
            );
            if (existingSecondaryColumnIndex !== -1) {
                const [existing] = updatedSelectedColumns.splice(existingSecondaryColumnIndex, 1);
                updatedSelectedColumns.unshift(existing);
            } else {
                const secondaryColumn = columnGroups
                    .flatMap((group) => group.columns)
                    .find((column) => column.id === secondaryColumnId);
                updatedSelectedColumns.unshift(secondaryColumn!);
            }
        }

        updatedSelectedColumns.sort((a, b) => a.order - b.order);
        updatedSelectedColumns.forEach((column, index) => {
            column.order = index + 1;
        });

        dispatch(setSelectedColumns(updatedSelectedColumns));
        dispatch(setPrimaryColumn(selectedColumn));

        prevPrimaryColumn.current = selectedColumn;
    }

    const filteredColumns = useMemo(() => {
        return columnGroups.flatMap((group) => {
            let columns = group.columns.filter(
                (column) =>
                    column.isPrimary &&
                    column.name.toLowerCase().includes(columnSearchQuery.toLowerCase())
            );
            columns = columns.map((column) => {
                let translatedName = column.name;
                switch (column.id) {
                    case 'OrderIdPropertyId':
                        translatedName = t('reports.cr_OrderNumberByOpeningDate');
                        break;
                    case 'OrderByClosingDatePropertyId':
                        translatedName = t('reports.cr_OrderNumberByClosingDate');
                        break;
                    default:
                        break;
                }

                return {
                    ...column,
                    name: translatedName,
                };
            });
            return columns;
        });
    }, [columnGroups, columnSearchQuery]);

    const filteredPrimaryColumnsExist = useMemo(() => {
        return (
            columnGroups &&
            columnGroups.some((group) =>
                group.columns.some(
                    (column) =>
                        column.isPrimary &&
                        column.name.toLowerCase().includes(columnSearchQuery.toLowerCase())
                )
            )
        );
    }, [columnGroups, columnSearchQuery]);

    const tooltipTexts: Record<string, string> = {
        OrderIdPropertyId: t('reports.OrderByOpeningDateTooltip'),
        OrderByClosingDatePropertyId: t('reports.OrderByClosingDateTooltip'),
    };

    function handleCollapse() {
        dispatch(setPrimaryColumnsExpanded(!isPrimaryExpanded));
    }

    return (
        <DivColumnsContent>
            <DivExpandableTitleContainer
                onClick={handleCollapse}
                sx={{ marginBottom: !isPrimaryExpanded ? '3px' : '0px' }}
            >
                {filteredPrimaryColumnsExist && (
                    <>
                        <SpanTitleLeftContainer>
                            {' '}
                            {t('reports.primarycolumn')}
                            {isPrimaryExpanded && (
                                <InfoTooltip
                                    position={'right'}
                                    text={t('reports.primarycolumntooltip')}
                                />
                            )}
                            {!isPrimaryExpanded && (
                                <SpanGroupColumnsContainer>
                                    {columnGroups
                                        ?.flatMap((group) => group.columns)
                                        ?.filter((x) => x.isPrimary)
                                        .map((obj) => obj.name)
                                        .join(', ')}
                                </SpanGroupColumnsContainer>
                            )}
                        </SpanTitleLeftContainer>
                        {!isPrimaryExpanded ? <ExpandMore /> : <ExpandLess />}
                    </>
                )}
            </DivExpandableTitleContainer>

            <Collapse in={isPrimaryExpanded}>
                <RadioGroup
                    sx={{
                        padding: '10px 10px 0px 10px!important',
                        paddingTop: '0!important',
                        paddingLeft: '5px!important',
                    }}
                    defaultValue={-1}
                    value={primaryColumn?.id ?? -1}
                    aria-label={'Primary column'}
                    name={'Primary column'}
                    onChange={(event) => handlePrimaryColumnCheck(event.target.value)}
                >
                    {filteredColumns.map((primaryColumn, columnIndex, columnsArray) => (
                        <React.Fragment key={columnIndex}>
                            <FormControlLabelStyled
                                value={primaryColumn.id}
                                control={<RadioStyled />}
                                label={
                                    <Box display="flex" alignItems="center">
                                        {primaryColumn.name}
                                        {tooltipTexts[primaryColumn.id] && (
                                            <InfoTooltip
                                                position={'right'}
                                                text={tooltipTexts[primaryColumn.id]}
                                            />
                                        )}
                                    </Box>
                                }
                            />
                        </React.Fragment>
                    ))}
                </RadioGroup>
            </Collapse>
            <WarningConfirmationPopup
                body={t('reports.primaryColumnChangedPopupBody', {
                    prevColumnName: prevPrimaryColumn.current?.name,
                    nextColumnName: nextPrimaryColumn.current?.name,
                })}
                title={t('reports.primaryColumnChangedPopupTitle')}
                open={primaryColumnChangeModalIsOpen}
                showCloseBtn={false}
                displayDivider={true}
                onClose={() => changePrimaryColumn(nextPrimaryColumn.current!.id)}
                confirm={'Ok'}
                onConfirm={() => changePrimaryColumn(nextPrimaryColumn.current!.id)}
            />
        </DivColumnsContent>
    );
}

const DivColumnsContent = styled('div')({
    fontFamily: 'Roboto!important',
    fontSize: 12,
});

const DivExpandableTitleContainer = styled('div')(() => ({
    display: 'flex',
    fontWeight: 700,
    color: 'var(--neutral7)',
    margin: '0px 18px 3px 4px',
    padding: '4px 8px',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
    borderRadius: '6px',

    '&:hover': {
        color: 'var(--neutral9)',
        backgroundColor: 'rgba(0,0,0,0.04)',
    },
}));

const SpanTitleLeftContainer = styled('span')(() => ({
    whiteSpace: 'nowrap',
    display: 'flex',
    marginRight: -5,
}));

const SpanGroupColumnsContainer = styled('span')(() => ({
    fontFamily: 'Roboto',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: 100,
    color: 'var(--neutral6)',
    display: 'block',
    fontWeight: 'normal',
    paddingLeft: 10,
    marginRight: 'auto',
    userSelect: 'none',
}));

const RadioStyled = styled(Radio)(() => ({
    color: 'var(--cm1)',
    fontFamily: 'Roboto',
    position: 'relative',
    [`& .${svgIconClasses.root}`]: {
        height: 22,
        width: 16.81,
    },
    '&.Mui-checked': {
        '&, & + .MuiFormControlLabel-label': {
            color: 'var(--cm1)',
        },
    },
}));

const FormControlLabelStyled = styled(FormControlLabel)(() => ({
    height: '35px',
    [`& .${formControlLabelClasses.label}`]: {
        fontFamily: 'Roboto',
    },
}));
