import { ExpandLess, ExpandMore } from '@mui/icons-material';
import {
    Box,
    Collapse,
    formControlLabelClasses,
    IconButton,
    InputAdornment,
    inputBaseClasses,
    styled,
    TextField,
    textFieldClasses,
} from '@mui/material';
import FormControlLabel from '@mui/material/FormControlLabel';
import { ReportColumn, ReportColumnGroup } from 'api/Reports';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import Checkbox from 'common/components/Inputs/Checkbox';
import isEqual from 'lodash/isEqual';
import { OverlayScrollbars } from 'overlayscrollbars';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useStore } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { setCustomColumnsCollapseState, setSelectedColumns } from 'store/slices/customReport';
import {
    selectCustomColumnsCollapseState,
    selectPrimaryColumn,
    selectSelectedColumns,
} from 'store/slices/customReport/selectors';
import InfoTooltip from '../../../../common/components/InfoTooltip';
import { useAppTranslation } from '../../../../common/hooks/useAppTranslation';
import { useCustomReportData } from '../CustomReportDataProvider';
import SelectedCustomReportColumns from '../SelectedCustomReportColumns';
import CustomReportPrimaryColumns from './CustomReportPrimaryColumns';

type CustomReportColumnsProps = CustomReportColumnGroupsProps & {};

function areColumnsEqual(arr1: ReportColumn[], arr2: ReportColumn[]): boolean {
    if (arr1.length !== arr2.length) {
        return false;
    }

    const sortById = (a: ReportColumn, b: ReportColumn) => (a.id > b.id ? 1 : -1);

    const sortedArr1 = [...arr1].sort(sortById);
    const sortedArr2 = [...arr2].sort(sortById);

    return isEqual(sortedArr1, sortedArr2);
}

export default function CustomReportColumns() {
    const { t } = useAppTranslation();

    const [columnSearchQuery, setColumnSearchQuery] = useState('');

    const store = useStore();
    const dispatch = useAppDispatch();
    const primaryColumnIsSelected = useAppSelector((r) => !!selectPrimaryColumn(r));

    const { columnGroups, isFetched, isLoading } = useCustomReportData();

    useEffect(() => {
        if (isLoading || !isFetched) return;

        // every time column groups change, update selected columns
        // to make sure only columns that exist in the new column groups are selected
        const selectedColumns = selectSelectedColumns(store.getState());
        const selectedColumnsClone = structuredClone(selectedColumns);
        const updatedSelectedColumns = selectedColumnsClone.filter((selectedColumn) =>
            columnGroups.some((group) =>
                group.columns.some((column) => column.id === selectedColumn.id)
            )
        );

        if (!areColumnsEqual(updatedSelectedColumns, selectedColumns)) {
            dispatch(setSelectedColumns(updatedSelectedColumns));
        }
    }, [columnGroups, dispatch, isFetched, isLoading, store]);

    const columnsContainerRef = useRef<HTMLDivElement | null>(null);
    const columnsScrollPosition = useRef<number>(0);

    function saveColumnsScrollPosition() {
        if (!columnGroups) return;
        if (columnsContainerRef.current) {
            columnsScrollPosition.current = columnsContainerRef.current.scrollTop;
        }
    }

    function searchInputResultNotFound(): boolean {
        if (columnGroups) {
            const primaryColumnsResultNotFound =
                columnGroups.filter((group) =>
                    group.columns
                        .filter((x) => x.isPrimary)
                        .some((column) =>
                            column.name.toLowerCase().includes(columnSearchQuery.toLowerCase())
                        )
                ).length === 0;
            const columnsResultNotFound =
                columnGroups.filter((group) =>
                    group.columns.some((column) =>
                        column.name.toLowerCase().includes(columnSearchQuery.toLowerCase())
                    )
                ).length === 0;
            return (
                columnSearchQuery !== '' && primaryColumnsResultNotFound && columnsResultNotFound
            );
        }
        return false;
    }

    useEffect(() => {
        const overlayScrollbarsInstance = OverlayScrollbars(columnsContainerRef.current!, {});
        return () => {
            overlayScrollbarsInstance.destroy();
        };
    }, []);

    return (
        <DivColumnsContainer>
            <DivColumnsHeader>{t('reports.columnstitle')}</DivColumnsHeader>
            <DivSearcherContainer>
                <TextFiledStyled
                    placeholder={t('reports.searchcolumn')}
                    onReset={() => setColumnSearchQuery('')}
                    value={columnSearchQuery}
                    onChange={(e) => setColumnSearchQuery(e.target.value)}
                    InputProps={{
                        disableUnderline: true,
                        type: 'text',
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton>
                                    <SearchIcon />
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                />
            </DivSearcherContainer>
            {searchInputResultNotFound() && <ColumnNotFoundContent />}
            <DivScrollColumnsArea ref={columnsContainerRef} onScroll={saveColumnsScrollPosition}>
                <Box>
                    {!searchInputResultNotFound() && (
                        <>
                            {primaryColumnIsSelected && (
                                <DivTitleContainer>
                                    {t('reports.selectedcolumnstitle')}
                                    <InfoTooltip
                                        position={'right'}
                                        text={t('reports.selectedcolumnstitletooltip')}
                                    />
                                </DivTitleContainer>
                            )}
                            <SelectedCustomReportColumns />
                        </>
                    )}

                    <CustomReportColumnGroups
                        columnGroups={columnGroups || []}
                        columnSearchQuery={columnSearchQuery}
                    />
                </Box>
            </DivScrollColumnsArea>
        </DivColumnsContainer>
    );
}

const TextFiledStyled = styled(TextField)(() => ({
    [`& .${inputBaseClasses.root}`]: {
        height: 32,
        borderRadius: 20,
        paddingRight: '0px!important',
    },
}));

const DivSearcherContainer = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'center',
    marginTop: '11px',
    marginBottom: '17px',
    [`& .${textFieldClasses.root}`]: {
        width: '80%',
    },
}));

const ColumnNotFoundContent: React.FC = () => {
    const { t } = useAppTranslation();

    return (
        <DivColumnNotFoundContainer>
            <SearchIcon fill={'#919CA5'} size={79} style={{ height: 50, width: 50 }} />
            <DivColumnNotFoundCaptionBold>
                {t('reports.columnNotFound')}
            </DivColumnNotFoundCaptionBold>
            <DivColumnNotFoundCaption>
                {t('reports.tryAgainWithADifferentColumn')}
            </DivColumnNotFoundCaption>
        </DivColumnNotFoundContainer>
    );
};

const DivColumnNotFoundCaption = styled('div')(() => ({
    fontWeight: 400,
    fontFamily: 'Roboto',
    fontSize: 12,
    color: '#919CA5',
}));

const DivColumnNotFoundContainer = styled('div')(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 12,
    marginTop: 20,
}));

const DivColumnNotFoundCaptionBold = styled('div')(() => ({
    fontWeight: 700,
    textAlign: 'center',
    marginTop: -10,
    fontFamily: 'Inter',
    fontSize: 18,
    color: 'var(--cm1)',
}));

const DivColumnsContainer = styled('div')(({ theme }) => ({
    height: '100%',
    flexBasis: '17%',

    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: 12,

    backgroundColor: `${theme.palette.primary.contrastText}`,

    [`& .${formControlLabelClasses.root}`]: {
        margin: '-7px 15px 0 0',
        width: 'fit-content',
    },
    [`& .${formControlLabelClasses.label}`]: {
        color: 'var(--neutral8)',
    },
}));

const DivColumnsHeader = styled('div')(({ theme }) => ({
    fontWeight: 700,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,

    display: 'flex',
    alignItems: 'center',
    height: 44,
    color: theme.palette.neutral[7],
    justifyContent: 'center',
    backgroundColor: '#F6F6F6',
}));

const DivScrollColumnsArea = styled('div')(() => ({
    height: 'calc(100vh - 380px)',
    paddingLeft: 16,
    overflowY: 'auto',
    position: 'relative',
}));

type CustomReportColumnGroupsProps = {
    columnGroups: ReportColumnGroup[];
    columnSearchQuery: string;
};

function CustomReportColumnGroups({ columnGroups, columnSearchQuery }: CustomReportColumnsProps) {
    const filteredColumnGroups = columnGroups
        .filter((group) =>
            group.columns.some((column) =>
                column.name.toLowerCase().includes(columnSearchQuery.toLowerCase())
            )
        )
        .sort((a, b) => a.order - b.order);

    return (
        <>
            <CustomReportPrimaryColumns
                columnGroups={columnGroups || []}
                columnSearchQuery={columnSearchQuery}
            />

            {filteredColumnGroups
                .filter((group) => !group.columns.every((column) => column.isPrimary))
                .map((group) => (
                    <ColumnGroup
                        key={group.id}
                        group={group}
                        columnSearchQuery={columnSearchQuery}
                    />
                ))}
        </>
    );
}

/**
 * Renders a column group, which is a collapsible container
 * that displays all the columns (checkboxes) in that group.
 * The group is collapsible and the user can select/deselect columns
 * by clicking on the checkboxes.
 *
 * @param {ReportColumnGroup} group - the group to render
 * @param {string} columnSearchQuery - the string to search for in the columns
 */
function ColumnGroup({
    group,
    columnSearchQuery,
}: {
    group: ReportColumnGroup;
    columnSearchQuery: string;
}) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const store = useStore();
    const isExpanded = useAppSelector(
        (r) => selectCustomColumnsCollapseState(r)[group.id] ?? false
    );
    const selectedColumns = useAppSelector(selectSelectedColumns);
    const technicianPrimaryColumnId = 'TechnicianPropertyId';
    const primaryColumnData = useAppSelector((r) => {
        const c = selectPrimaryColumn(r);
        return {
            isSelected: !!c,
            isTechnicianPrimaryColumnId: c?.id === technicianPrimaryColumnId,
            matchingGroupColumnIndex: c ? group.columns.findIndex((x) => x.id === c.id) : -1,
        };
    }, isEqual);

    const filteredColumns = useMemo(() => {
        return group.columns
            .filter((column) => column.name.toLowerCase().includes(columnSearchQuery.toLowerCase()))
            .sort((a, b) => a.order - b.order);
    }, [columnSearchQuery, group.columns]);

    // an array of bools that each indicate if a column at that position is selected
    // kinda of an overkill as far as optimizations go but not too far imo
    const isColumnSelectedArray = useAppSelector((r) => {
        const selectedColumns = selectSelectedColumns(r);

        const isSelected = [];
        for (let i = 0; i < filteredColumns.length; i++) {
            isSelected.push(selectedColumns.some((x) => x.id === filteredColumns[i].id));
        }

        return isSelected;
    }, isEqual);

    const productivityColumnsIds = [
        'WorkshopPlannerAvailableHoursPropertyId',
        'WorkshopPlannerHoursPresentPropertyId',
        'WorkshopPlannerProductiveHoursPropertyId',
        'WorkshopPlannerUnproductiveHoursPropertyId',
        'WorkshopPlannerPercentProductivityPropertyId',
    ];
    function notCompatibleWithPrimaryColumn(columnId: string): boolean {
        if (!primaryColumnData.isTechnicianPrimaryColumnId) {
            if (productivityColumnsIds.includes(columnId)) return true;
        }

        return false;
    }

    const dependentColumns = [
        {
            columnId: 'OrdersOriginPhasePropertyId',
            dependentFromColumnId: 'OrdersPhaseSetbackPropertyId',
        },
        {
            columnId: 'OrdersDestinationPhasePropertyId',
            dependentFromColumnId: 'OrdersPhaseSetbackPropertyId',
        },
        {
            columnId: 'OrdersPhaseSetbackReasonPropertyId',
            dependentFromColumnId: 'OrdersPhaseSetbackPropertyId',
        },
    ];
    function compatibleWithSelectedColumns(columnId: string): boolean {
        const dependentColumn = dependentColumns.find((x) => x.columnId === columnId);
        if (!!dependentColumn) {
            return selectedColumns.some((x) => x.id === dependentColumn.dependentFromColumnId);
        }

        return true;
    }

    function handleCollapse() {
        dispatch(
            setCustomColumnsCollapseState({
                groupId: group.id,
                isExpanded: !isExpanded,
            })
        );
    }

    function handleCheckColumn(column: ReportColumn) {
        const updatedSelectedColumns = structuredClone(selectSelectedColumns(store.getState()));

        const columnCurrentIndex = updatedSelectedColumns.findIndex((col) => col.id === column.id);
        if (columnCurrentIndex !== -1) {
            updatedSelectedColumns.splice(columnCurrentIndex, 1);
            uncheckDependentColumns(column, updatedSelectedColumns);
        } else {
            const maxOrder = Math.max(...updatedSelectedColumns.map((c) => c.order));
            updatedSelectedColumns.push({
                id: column.id,
                name: column.name,
                order: maxOrder + 1,
                isPrimary: column.isPrimary,
                columnType: column.columnType,
            });
        }

        updatedSelectedColumns.sort((a, b) => a.order - b.order);
        updatedSelectedColumns.forEach((column, index) => {
            column.order = index + 1;
        });
        dispatch(setSelectedColumns(updatedSelectedColumns));
    }

    function uncheckDependentColumns(column: ReportColumn, updatedSelectedColumns: ReportColumn[]) {
        const columnsToReset = dependentColumns.filter(
            (x) => x.dependentFromColumnId === column.id
        );
        for (const columnToReset of columnsToReset) {
            const columnCurrentIndex = updatedSelectedColumns.findIndex(
                (col) => col.id === columnToReset.columnId
            );
            if (columnCurrentIndex !== -1) {
                updatedSelectedColumns.splice(columnCurrentIndex, 1);
            }
        }
    }

    const tooltipTexts: Record<string, string> = {
        OrdersTimeFromUploadedToClosedPropertyId: t(
            'reports.OrdersTimeFromUploadedToClosedPropertyTooltip'
        ),
        WorkshopPlannerTimeOnPausePropertyId: t('reports.WorkshopPlannerTimeOnPauseTooltip'),
        WorkshopPlannerDurationPropertyId: t('reports.WorkshopPlannerDurationTooltip'),
        WorkshopPlannerPhasePropertyId: t('reports.WorkshopPlannerCurrentPhaseTooltip'),
        WorkshopPlannerJobNumberPropertyId: t('reports.WorkshopPlannerJobNumberTooltip'),
        WorkshopPlannerJobRealDurationPropertyId: t(
            'reports.WorkshopPlannerJobRealDurationTooltip'
        ),
    };

    return (
        <DivColumnsContent key={group.id}>
            <ClickableDivTitleContainer
                role="button"
                onClick={handleCollapse}
                style={{ marginBottom: !isExpanded ? '3px' : '0px' }}
            >
                <SpanGroupNameContainer>{group.name}</SpanGroupNameContainer>
                {!isExpanded && (
                    <SpanGroupColumnsContainer>
                        {group.columns.map((obj) => obj.name).join(', ')}
                    </SpanGroupColumnsContainer>
                )}
                {!isExpanded ? <ExpandMore /> : <ExpandLess />}
            </ClickableDivTitleContainer>
            <Collapse in={isExpanded}>
                {filteredColumns.map((column, columnIndex, arr) => (
                    <React.Fragment key={column.id}>
                        <Box marginTop={1} display="flex" alignItems="center">
                            <FormControlLabelStyled
                                disabled={
                                    !primaryColumnData.isSelected ||
                                    columnIndex === primaryColumnData.matchingGroupColumnIndex ||
                                    notCompatibleWithPrimaryColumn(column.id) ||
                                    !compatibleWithSelectedColumns(column.id)
                                }
                                checked={
                                    isColumnSelectedArray[columnIndex] ||
                                    columnIndex === primaryColumnData.matchingGroupColumnIndex
                                }
                                onChange={() => handleCheckColumn(column)}
                                control={<StyledCheckbox uncheckedIconColor="var(--cm1)" />}
                                label={
                                    <Box display="flex" alignItems="center">
                                        {column.name}
                                        {tooltipTexts[column.id] && (
                                            <InfoTooltip
                                                position={'right'}
                                                text={tooltipTexts[column.id]}
                                            />
                                        )}
                                    </Box>
                                }
                            />
                        </Box>
                    </React.Fragment>
                ))}
            </Collapse>
        </DivColumnsContent>
    );
}

const DivColumnsContent = styled('div')({
    fontFamily: 'Roboto!important',
    fontSize: 12,
});

const DivTitleContainer = styled('div')({
    display: 'flex',
    margin: '0px 18px 3px 4px',
    padding: '4px 8px',
    fontWeight: 700,
    fontFamily: 'Inter',
    color: 'var(--neutral7)',
    alignItems: 'center',
    justifyContent: 'space-between',
});

const ClickableDivTitleContainer = styled(DivTitleContainer)({
    cursor: 'pointer',
    borderRadius: '6px',

    '&:hover': {
        backgroundColor: 'rgba(0,0,0,0.04)',
    },
});

const StyledCheckbox = styled(Checkbox)({
    paddingBottom: 3,
    paddingTop: 0,
    paddingRight: 8,
    paddingLeft: 12,
    '&:hover': {
        backgroundColor: 'transparent',
    },
});

const FormControlLabelStyled = styled(FormControlLabel)(() => ({
    [`& .${formControlLabelClasses.label}`]: {
        fontFamily: 'Roboto',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        width: 190,
    },

    [`&:hover .${formControlLabelClasses.label}`]: {
        textDecoration: 'underline',
        fontWeight: '500',
    },
}));

const SpanGroupNameContainer = styled('span')(() => ({
    whiteSpace: 'nowrap',
}));

const SpanGroupColumnsContainer = styled('span')(() => ({
    fontFamily: 'Roboto',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: 150,
    color: 'var(--neutral6)',
    display: 'block',
    fontWeight: 'normal',
    paddingLeft: 10,
    marginRight: 'auto',
    userSelect: 'none',
}));
