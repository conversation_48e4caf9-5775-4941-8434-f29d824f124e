import { autocompleteClasses } from '@mui/material';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectFilters, setFilters } from 'store/slices/enterprise/reports';
import LocationSelectorBase, {
    LocationSelectorBaseProps,
} from '../../../../common/components/LocationSelector/LocationSelectorBase';

export type LocationSelectorProps = Omit<
    LocationSelectorBaseProps,
    'style' | 'numericPlaceholderAfterCount'
>;

const selectLocations = createSelector(selectFilters, (f) => f.locations);

const CustomReportLocationSelector = () => {
    const selectedLocations = useAppSelector(selectLocations);
    const dispatch = useAppDispatch();

    const handleOnChange = (locations: string[]) => {
        dispatch(
            setFilters({
                locations: locations,
            })
        );
    };

    return (
        <LocationSelectorBase
            value={selectedLocations}
            numericPlaceholderAfterCount={0}
            sx={{
                width: 250,
                [`& .${autocompleteClasses.inputRoot}`]: {
                    '&:hover': {
                        backgroundColor: '#cce1ff',
                    },
                },
            }}
            disableSelectNewOption={selectedLocations.length >= 30}
            onChange={handleOnChange}
        />
    );
};

export default CustomReportLocationSelector;
