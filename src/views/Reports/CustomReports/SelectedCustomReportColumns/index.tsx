import { Box, Grid, styled } from '@mui/material';
import React from 'react';

import { ReportColumn } from 'api/Reports';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DragDropContext, Droppable, DropResult } from 'react-beautiful-dnd';
import DraggableColumn from '../DraggableColumn';
import { useAppDispatch } from 'store';
import { setSelectedColumns } from 'store/slices/customReport';
import { useSelector } from 'react-redux';
import { selectPrimaryColumn, selectSelectedColumns } from 'store/slices/customReport/selectors';

function getReorderedColumns(
    columns: ReportColumn[],
    prevOrder: number,
    newOrder: number,
    movedColumnId: string
) {
    const result = Array.from(columns);
    const positionsMoved = newOrder - prevOrder;
    const isMovedDown = positionsMoved > 0;
    const affectedFieldIds = isMovedDown
        ? columns
              .filter(
                  (f) => f.id === movedColumnId || (f.order! <= newOrder && f.order! >= prevOrder)
              )
              .map((f) => f.id)
        : columns
              .filter(
                  (f) => f.id === movedColumnId || (f.order! >= newOrder && f.order! <= prevOrder)
              )
              .map((f) => f.id);

    result.forEach((f) => {
        if (!affectedFieldIds.includes(f.id!)) return;
        if (f.id === movedColumnId) {
            f.order = newOrder;
            return;
        }
        f.order = isMovedDown ? f.order! - 1 : f.order! + 1;
    });

    return result;
}

export default function SelectedCustomReportColumns() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const selectedColumns = useSelector(selectSelectedColumns);
    const primaryColumn = useSelector(selectPrimaryColumn);

    const primaryColumnToComponent = (column: ReportColumn | null) => {
        let columnName;
        switch (column?.id) {
            case 'OrderIdPropertyId':
                columnName = t('reports.cr_OrderNumberByOpeningDate');
                break;
            case 'OrderByClosingDatePropertyId':
                columnName = t('reports.cr_OrderNumberByClosingDate');
                break;
            default:
                columnName = column?.name;
                break;
        }

        return (
            columnName && (
                <SpanPrimaryColumnContainer>
                    {' '}
                    {columnName}{' '}
                    <Box component={'span'} color={'var(--neutral6)'}>
                        {' '}
                        {'(' + t('reports.primary') + ')'}
                    </Box>
                </SpanPrimaryColumnContainer>
            )
        );
    };

    const mapItemsToComponents = (items: ReportColumn[]) => {
        return items
            .sort((i1, i2) => i1.order! - i2.order!)
            .map((column) => (
                <div key={column.id.toString()}>
                    <DraggableColumn
                        draggableId={column.id.toString()}
                        index={column.order - 1}
                        onDeleteClick={() => onDeleteClick(column.id!)}
                    >
                        {column.name}
                    </DraggableColumn>
                </div>
            ));
    };

    function onDeleteClick(id: string) {
        const columnIndex = selectedColumns.findIndex((column) => column.id === id);

        if (columnIndex !== -1) {
            const updatedSelectedColumns = structuredClone(selectedColumns);
            updatedSelectedColumns.splice(columnIndex, 1);
            updatedSelectedColumns.sort((a, b) => a.order - b.order);
            updatedSelectedColumns.forEach((column, index) => {
                column.order = index + 1;
            });

            dispatch(setSelectedColumns(updatedSelectedColumns));
        }
    }

    function dragEndHandler(result: DropResult) {
        const { destination, source, draggableId } = result;
        if (!destination) return;
        if (destination.droppableId !== source.droppableId) return;
        if (destination.droppableId === source.droppableId && destination.index === source.index)
            return;
        const prevOrder = source.index + 1;
        const newOrder = destination.index + 1;

        const reorderedColumns = getReorderedColumns(
            structuredClone(selectedColumns),
            prevOrder,
            newOrder,
            draggableId
        );

        dispatch(setSelectedColumns(reorderedColumns));
    }

    return (
        <DragDropContext onDragEnd={dragEndHandler}>
            <Droppable droppableId={'drop_selected_columns_id'}>
                {(provided) => (
                    <Box style={{ width: '100%' }}>
                        <Box marginLeft="10px">
                            <Grid
                                container
                                justifyContent="center"
                                sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    paddingTop: 0,
                                    marginBottom: '16px',
                                }}
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                            >
                                {primaryColumnToComponent(primaryColumn)}
                                {mapItemsToComponents(structuredClone(selectedColumns))}
                                {provided.placeholder}
                            </Grid>
                        </Box>
                    </Box>
                )}
            </Droppable>
        </DragDropContext>
    );
}

const SpanPrimaryColumnContainer = styled('span')(() => ({
    marginLeft: 6,
    marginBottom: 5,
    fontFamily: 'Roboto',
}));
