import { Button } from 'common/components/Button';
import TextForm<PERSON>ield from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { emailRegex } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { useStyles } from './css';

interface LargeReportModalProps {
    onSubmit: (email: string) => Promise<void>;
    onClose: () => void;
}

export const LargeReportModal = ({ onSubmit, onClose }: LargeReportModalProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [email, setEmail] = useState('');
    const [pending, setPending] = useState(false);
    const dispatch = useAppDispatch();

    const handleSubmitClick = async () => {
        if (emailRegex.test(email)) {
            setPending(true);
            try {
                await onSubmit(email);
            } finally {
                setPending(false);
            }
        } else {
            dispatch(
                setNewToaster(
                    new NotificationData(
                        t('reports.largeReport.invalidEmailBody'),
                        t('reports.largeReport.invalidEmailTitle'),
                        NotificationType.warning
                    )
                )
            );
        }
    };

    return (
        <Modal open onClose={onClose}>
            <div className={styles.largeReport}>
                <div className={styles.title}>{t('reports.largeReport.title')}</div>
                <div className={styles.text}>{t('reports.largeReport.text')}</div>
                <div className={styles.emailArea}>
                    <div className={styles.emailCaption}>
                        {t('reports.largeReport.emailCaption')}
                    </div>
                    <TextFormField
                        cmosVariant="grey"
                        name="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder={t('reports.largeReport.placeholder')}
                        inputWrapperClasses={{ self: styles.emailField }}
                    />
                </div>
                <div className={styles.buttons}>
                    <Button
                        color={Colors.Neutral3}
                        cmosVariant={'filled'}
                        label={t('commonLabels.cancel')}
                        onClick={onClose}
                        className={styles.button}
                    />
                    <Button
                        color={Colors.CM1}
                        cmosVariant={'filled'}
                        label={t('reports.largeReport.submit')}
                        onClick={handleSubmitClick}
                        className={styles.button}
                        disabled={pending}
                        showLoader={pending}
                    />
                </div>
            </div>
        </Modal>
    );
};

export default LargeReportModal;
