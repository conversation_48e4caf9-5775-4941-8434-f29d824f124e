import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    largeReport: {
        display: 'flex',
        flexDirection: 'column',
        paddingTop: 34,
        paddingBottom: 30,
        paddingLeft: 40,
        paddingRight: 35,
        width: 834,
        boxSizing: 'border-box',
    },
    title: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[8]),
        lineHeight: '17px',
        marginBottom: 16,
    },
    text: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
        lineHeight: '14px',
        marginBottom: 16,
        paddingBottom: 7,
        whiteSpace: 'pre',
    },
    emailArea: {
        display: 'flex',
        columnGap: 16,
        alignItems: 'center',
        marginBottom: 27,
    },
    emailCaption: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
        marginRight: 44,
    },
    emailField: {
        width: 320,
    },
    buttons: {
        display: 'flex',
        justifyContent: 'flex-end',
        columnGap: 16,
    },
    button: {
        width: 'initial',
        minWidth: 160,
    },
}));
