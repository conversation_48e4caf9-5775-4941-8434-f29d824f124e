import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useStyles } from './css';

interface InvalidDatesModalProps {
    from: string;
    to: string;
    onClose: () => void;
}

export const InvalidDatesModal = ({ from, to, onClose }: InvalidDatesModalProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const fromFormatted = from ? moment(from).format(t('reports.invalidDates.dateFormat')) : '';
    const toFormatted = to ? moment(to).format(t('reports.invalidDates.dateFormat')) : '';

    return (
        <Modal open classes={{ box: styles.invalidDatesModal }}>
            <div className={styles.title}>{t('reports.invalidDates.title')}</div>
            <div className={styles.body}>
                <div className={styles.bodyTop}>{t('reports.invalidDates.body1')}</div>
                <div className={styles.bodyTop}>
                    {t('reports.invalidDates.body2', { from: fromFormatted, to: toFormatted })}
                </div>
                <div className={styles.bodyBottom}>{t('reports.invalidDates.body3')}</div>
            </div>
            <div className={styles.buttonArea}>
                <Button
                    cmosVariant={'filled'}
                    cmosSize={'medium'}
                    className={styles.close}
                    label={t('reports.invalidDates.close')}
                    onClick={onClose}
                />
            </div>
        </Modal>
    );
};

export default InvalidDatesModal;
