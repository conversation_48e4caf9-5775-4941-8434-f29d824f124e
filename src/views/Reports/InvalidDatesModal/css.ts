import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    invalidDatesModal: {
        paddingTop: 34,
        paddingBottom: 30,
        paddingLeft: 40,
        paddingRight: 40,
    },
    title: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[8]),
        marginBottom: 16,
        lineHeight: '17px',
    },
    body: {
        minHeight: 51,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
    },
    bodyTop: {
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
    },
    bodyBottom: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
    },
    buttonArea: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: 24,
    },
    close: {
        minWidth: 160,
        width: 'initial',
    },
}));
