import { ArrowBack } from '@mui/icons-material';
import { Box, styled, Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { VehicleDetailsDto, VehiclesCrmApi } from 'api/customers';
import { getErrorMessage } from 'api/error';
import { Button } from 'common/components/Button';
import { isUuid } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import {
    fetchVehicleThunk,
    selectVehicleDetails,
    vehicleDetailsActions,
} from 'store/slices/vehicleDetails';
import { DocumentTitle } from 'views/HeaderBar/context';
import VehicleDetailsCenter from './VehicleDetailsCenter';
import VehicleDetailsLeft from './VehicleDetailsLeft';
import { fetchVehicleFieldsUIState } from './VehicleDetailsLeft/fields';
import VehicleDetailsRight from './VehicleDetailsRight';
import { VehicleDetailsRightColumnProvider } from './VehicleDetailsRight/VehicleDetailsRightColumnContext';

export default function VehicleDetails() {
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();

    const isValidUuid = useMemo(() => isUuid(vehicleId), [vehicleId]);

    const is404 = useAppSelector((r) => selectVehicleDetails(r, vehicleId).isNotFound);

    if (is404 || !isValidUuid) {
        return <VehicleDetails404 />;
    }

    return (
        <DivLayout>
            <VehicleDetailsFetcher vehicleId={vehicleId} />
            <CustomVehicleDetailsHeader vehicleId={vehicleId} />
            <StyledOverlayScrollbars options={{ scrollbars: { autoHide: 'move' } }}>
                <VehicleDetailsLeft vehicleId={vehicleId} />
            </StyledOverlayScrollbars>
            <StyledOverlayScrollbars options={{ scrollbars: { autoHide: 'move' } }}>
                <VehicleDetailsCenter vehicleId={vehicleId} />
            </StyledOverlayScrollbars>
            <StyledOverlayScrollbars options={{ scrollbars: { autoHide: 'move' } }}>
                <VehicleDetailsRightColumnProvider>
                    <VehicleDetailsRight />
                </VehicleDetailsRightColumnProvider>
            </StyledOverlayScrollbars>
        </DivLayout>
    );
}

const StyledOverlayScrollbars = styled(OverlayScrollbarsComponent)({
    padding: '0 18px 0 4px',
    height: 'calc(100vh - var(--header-height, 0px))',
    position: 'relative',

    '.os-scrollbar-vertical': {
        '--os-handle-perpendicular-size': '60%',
    },
});

const DivLayout = styled('div')({
    '--left-column-size': '350px',
    '--right-column-size': '350px',

    display: 'grid',
    gridTemplateColumns: 'var(--left-column-size) 1fr var(--right-column-size)',
});

function CustomVehicleDetailsHeader({ vehicleId }: { vehicleId: string }) {
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));
    const { t } = useAppTranslation();

    const title = useMemo((): DocumentTitle => {
        if (!details) return '';

        const title = `${details.details.brand || '-'} ${details.details.model || '-'} ${
            details.details.year || '-'
        }, ${details.details.plates || '-'} / ${details.details.vin || '-'}`;

        return {
            title: `${t('afterSalesCrm.vehicleDetails.title')}: ${title}`,
            titleJsx: <VehicleDetailsHeaderText details={details.details} />,
        };
    }, [details, t]);

    useDocumentTitle(title);

    return null;
}

function VehicleDetailsHeaderText({ details }: { details: VehicleDetailsDto }) {
    const { t } = useAppTranslation();
    let text = `${details.brand || '-'} ${details.model || '-'} ${details.year || '-'}`;

    text += `, ${details.plates || '-'}`;

    return (
        <>
            {t('afterSalesCrm.vehicleDetails.title')}:&nbsp;
            <span style={{ color: 'var(--neutral9)' }}>{text}</span>
            <span style={{ fontWeight: 'normal' }}> / {details.vin || '-'}</span>
        </>
    );
}

function VehicleDetails404() {
    const { t } = useAppTranslation();
    const navigate = useNavigate();

    return (
        <Box sx={{ mx: '400px', mt: '40px' }}>
            <Button onClick={() => navigate(-1)} color="neutral" cmosVariant="stroke">
                <ArrowBack />
                {t('commonLabels.goBack')}
            </Button>
            <Typography sx={{ display: 'block', mb: 3, mt: 2 }} variant="h1Inter">
                {t('afterSalesCrm.vehicleDetails.notFound')}
            </Typography>
            <Box sx={{ mt: 3, maxWidth: '75ch' }}>
                {t('afterSalesCrm.vehicleDetails.notFoundDescription')}
            </Box>
        </Box>
    );
}

function VehicleDetailsFetcher({ vehicleId }: { vehicleId: string }) {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const { data: vehicleParams } = useQuery({
        queryKey: ['crm', 'vehicle-params'],
        cacheTime: Infinity,
        staleTime: 500,
        queryFn: VehiclesCrmApi.getAvailableVehicleParams,
    });

    useEffect(() => {
        if (vehicleParams) {
            dispatch(vehicleDetailsActions.setVehicleParams(vehicleParams));
        }
    }, [dispatch, vehicleParams]);

    useQuery({
        queryFn: async () => {
            const fetchUIState = fetchVehicleFieldsUIState();
            const result = await dispatch(fetchVehicleThunk({ id: vehicleId }));

            if (result.meta.requestStatus === 'rejected') {
                toasters.danger(getErrorMessage(result.payload), t('toasters.errorOccurred'));
            }

            await fetchUIState;
        },
        enabled: Boolean(vehicleId && isUuid(vehicleId)),
    });

    return null;
}
