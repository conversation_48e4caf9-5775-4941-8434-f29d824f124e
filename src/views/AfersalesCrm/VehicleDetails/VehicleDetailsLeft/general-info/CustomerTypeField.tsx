import { TextFormField } from 'common/components/Inputs';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function CustomerTypeField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.customerType);
    const { t } = useAppTranslation();

    return (
        <InputWrapper label={label} disabled={disabled}>
            <ReadonlyFieldTooltip
                disabled={disabled}
                text={t('afterSalesCrm.vehicleDetails.readonlyField.customerType')}
            >
                <TextFormField
                    disabled={disabled}
                    readonly
                    cmosVariant="grey"
                    value={value ? t(`afterSalesCrm.vehicleDetails.customerType.${value}`) : '--'}
                />
            </ReadonlyFieldTooltip>
        </InputWrapper>
    );
}
