import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { useMemo } from 'react';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function MonthsDaysSinceLastActivityField({
    label,
    disabled,
}: VehicleDetailsFieldProps) {
    const lastActivityDate = useVehicleDetailsSelector((v) => v.lastActivityDate);

    const { t } = useAppTranslation();

    const text = useMemo(() => {
        if (!lastActivityDate) return '--';

        const diff = DateTime.fromISO(lastActivityDate).diffNow().negate().rescale();

        const months = diff.years * 12 + diff.months;

        if (months >= 1) {
            return `${months} ${
                months === 1
                    ? t('commonLabels.timePeriods.M-singular')
                    : t('commonLabels.timePeriods.M')
            }`;
        }

        const days = diff.weeks * 7 + diff.days;

        if (days >= 1) {
            return `${days} ${
                days === 1
                    ? t('commonLabels.timePeriods.d-singular')
                    : t('commonLabels.timePeriods.d')
            }`;
        }

        return '--';
    }, [t, lastActivityDate]);

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.monthsDaysSinceLastActivity')}
        >
            <TextFormField
                disabled={disabled}
                cmosVariant="grey"
                tabIndex={-1}
                readonly
                label={label}
                value={text}
            />
        </ReadonlyFieldTooltip>
    );
}
