import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SSelectGrey } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import UserSelectByJobTitle from 'views/Components/UserSelect/UserSelectByJobTitle';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function AssignedBdcAdvisorField({ label, disabled }: VehicleDetailsFieldProps) {
    const bdcAdvisorId = useVehicleDetailsSelector((v) => v.bdcAdvisor);
    const update = useVehicleUpdater();
    const { t } = useAppTranslation();

    return (
        <InputWrapper label={label} disabled={disabled}>
            <UserSelectByJobTitle
                disabled={disabled}
                SelectComponent={SSelectGrey}
                userId={bdcAdvisorId ? bdcAdvisorId.id : null}
                jobTitle="BdcAdvisor"
                placeholder={t('afterSalesCrm.vehicleDetails.fields.assignedBdcAdvisorPlaceholder')}
                showEmail
                fullWidth
                onChange={(_, user) =>
                    update({ bdcAdvisor: user ? { id: user.key, name: user.name } : null })
                }
            />
        </InputWrapper>
    );
}
