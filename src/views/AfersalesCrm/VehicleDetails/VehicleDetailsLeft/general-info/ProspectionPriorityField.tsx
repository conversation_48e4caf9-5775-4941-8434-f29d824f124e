import { TextFormField } from 'common/components/Inputs';
import { useTranslation } from 'react-i18next';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function ProspectionPriorityField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.prospectionPriority);
    const { t } = useTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.prospectionPriority')}
        >
            <TextFormField
                disabled={disabled}
                cmosVariant="grey"
                readonly
                label={label}
                value={value?.name || '--'}
                placeholder={label}
            />
        </ReadonlyFieldTooltip>
    );
}
