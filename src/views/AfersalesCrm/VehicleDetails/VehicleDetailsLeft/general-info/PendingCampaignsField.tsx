import { Box } from '@mui/material';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { useContext } from 'react';
import { useAppSelector } from 'store';
import { selectVehicleDetails } from 'store/slices/vehicleDetails';
import { VehicleDetailsFieldProps } from '..';
import { VehicleIdContext } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function PendingCampaignsField({ label, disabled }: VehicleDetailsFieldProps) {
    const vehicleId = useContext(VehicleIdContext);
    const { t } = useAppTranslation();

    const campaigns = useAppSelector((r) => selectVehicleDetails(r, vehicleId).details?.campaigns);

    return (
        <InputWrapper label={label} disabled={disabled}>
            <ReadonlyFieldTooltip
                disabled={disabled}
                text={t('afterSalesCrm.vehicleDetails.readonlyField.pendingCampaigns')}
            >
                <Box
                    sx={{
                        borderRadius: 1,
                        border: '1px solid var(--neutral4)',
                        padding: '8px 12px',
                        backgroundColor: 'var(--neutral2)',
                        width: '100%',
                        color: disabled ? 'var(--neutral5)' : 'var(--neutral7)',
                        minHeight: 75,
                    }}
                >
                    {(!campaigns || campaigns.length === 0) && '--'}
                    {campaigns?.map((c) => (
                        <React.Fragment key={c.id}>
                            <strong>{c.name}</strong> <br />
                            Code: {c.code} <br />
                            Start date: {c.startDate} <br />
                            End date: {c.startDate} <br />
                        </React.Fragment>
                    ))}
                </Box>
            </ReadonlyFieldTooltip>
        </InputWrapper>
    );
}
