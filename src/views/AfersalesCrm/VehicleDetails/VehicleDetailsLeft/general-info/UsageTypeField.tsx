import { usageTypeSchema } from 'api/customers';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SMenuItem } from 'common/components/mui';
import { SSelectGrey } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function UsageTypeField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.usageType);
    const update = useVehicleUpdater();
    const { t } = useAppTranslation();

    return (
        <InputWrapper label={label} disabled={disabled}>
            <SSelectGrey<string | null>
                disabled={disabled}
                placeholder={t(`afterSalesCrm.vehicleDetails.fields.usageTypePlaceholder`)}
                onChange={(e) => update({ usageType: usageTypeSchema.parse(e.target.value) })}
                value={value}
            >
                {Object.values(usageTypeSchema.Values).map((value) => (
                    <SMenuItem key={value} value={value}>
                        {t(`afterSalesCrm.vehicleDetails.usageType.${value}`)}
                    </SMenuItem>
                ))}
            </SSelectGrey>
        </InputWrapper>
    );
}
