import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function VinStatusField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.vinStatus);
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.vinStatus')}
        >
            <TextFormField
                disabled={disabled}
                readonly
                cmosVariant="grey"
                label={label}
                value={value ? t(`afterSalesCrm.vehicleDetails.vinStatus.${value}`) : '--'}
            />
        </ReadonlyFieldTooltip>
    );
}
