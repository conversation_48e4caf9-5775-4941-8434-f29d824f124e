import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function RetentionBandField({ label, disabled }: VehicleDetailsFieldProps) {
    const vehicleBands = useVehicleDetailsSelector((v) => v.retentionBands);
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.retentionBand')}
        >
            <TextFormField
                disabled={disabled}
                cmosVariant="grey"
                readonly
                label={label}
                value={vehicleBands.map((x) => x.name).join(', ')}
                placeholder={'--'}
            />
        </ReadonlyFieldTooltip>
    );
}
