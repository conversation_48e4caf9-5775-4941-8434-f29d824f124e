import DateFormField from 'common/components/Inputs/DateFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function NextActivityDateField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) =>
        v.nextActivityDate ? DateTime.fromISO(v.nextActivityDate) : null
    );
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.nextActivityDate')}
        >
            <div>
                <DateFormField
                    disabled={disabled}
                    readOnly
                    variant="grey"
                    value={value ? value.toJSDate() : null}
                    label={label}
                />
            </div>
        </ReadonlyFieldTooltip>
    );
}
