import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function LastActivityDateField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) =>
        v.lastActivityDate ? DateTime.fromISO(v.lastActivityDate) : null
    );
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.lastActivityDate')}
        >
            <TextFormField
                disabled={disabled}
                cmosVariant="grey"
                readonly
                value={value ? value.toFormat('dd/MM/yy') : '--'}
                label={label}
            />
        </ReadonlyFieldTooltip>
    );
}
