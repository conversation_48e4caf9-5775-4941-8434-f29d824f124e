import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function CustomerStatusField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.customerStatus);
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.customerStatus')}
        >
            <TextFormField
                disabled={disabled}
                readonly
                cmosVariant="grey"
                placeholder={t('afterSalesCrm.vehicleDetails.fields.customerStatusPlaceholder')}
                label={label}
                value={value?.name || null}
            />
        </ReadonlyFieldTooltip>
    );
}
