import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function LastActivityField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.lastActivity);
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.lastActivity')}
        >
            <TextFormField
                disabled={disabled}
                label={label}
                cmosVariant="grey"
                placeholder={t('afterSalesCrm.vehicleDetails.fields.lastActivityPlaceholder')}
                readonly
                value={value?.name || ''}
            />
        </ReadonlyFieldTooltip>
    );
}
