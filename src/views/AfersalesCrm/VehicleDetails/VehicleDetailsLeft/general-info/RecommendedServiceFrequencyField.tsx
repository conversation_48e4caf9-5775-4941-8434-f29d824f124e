import { RecommendedServiceFrequency } from 'api/customers';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SMenuItem } from 'common/components/mui';
import { SSelectGrey } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppSelector } from 'store';
import { selectRecommendedServiceFrequencyOptions } from 'store/slices/vehicleDetails';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

function toId(value: RecommendedServiceFrequency): string {
    if (value.km === null && value.months === null) return '';
    return `${value.km}:${value.months}`;
}

function fromId(id: string): RecommendedServiceFrequency {
    if (id === '')
        return {
            km: null,
            months: null,
        };

    const parts = id.split(':');

    if (parts.length !== 2)
        return {
            km: null,
            months: null,
        };

    function parse(v: string): number | null {
        if (!v) return null;
        const n = +v;
        if (Number.isNaN(n)) {
            return null;
        }
        return n;
    }

    const km = parse(parts[0]);
    const months = parse(parts[1]);

    return {
        km,
        months,
    };
}

export default function RecommendedServiceFrequencyField({
    label,
    disabled,
}: VehicleDetailsFieldProps) {
    const options = useAppSelector(selectRecommendedServiceFrequencyOptions);
    const update = useVehicleUpdater();
    const { t } = useAppTranslation();
    const value = useVehicleDetailsSelector((v) => v.recommendedServiceFrequency);

    return (
        <InputWrapper label={label} disabled={disabled}>
            <SSelectGrey<string>
                placeholder={t(
                    'afterSalesCrm.vehicleDetails.fields.recommendedServiceFrequencyPlaceholder'
                )}
                disabled={disabled}
                onChange={(e) => {
                    const id = e.target.value;
                    const option = fromId(id);
                    if (option) update({ recommendedServiceFrequency: option });
                }}
                {...(value.km || value.months
                    ? {
                          renderValue(value) {
                              if (value) {
                                  const option = fromId(value);
                                  return t('afterSalesCrm.values.recommendedServiceFrequency', {
                                      km: option.km,
                                      months: option.months,
                                  });
                              }
                          },
                      }
                    : undefined)}
                value={toId(value)}
            >
                {options.map((option) => (
                    <SMenuItem key={toId(option)} value={toId(option)}>
                        {t('afterSalesCrm.values.recommendedServiceFrequency', {
                            km: option.km,
                            months: option.months,
                        })}
                    </SMenuItem>
                ))}
            </SSelectGrey>
        </InputWrapper>
    );
}
