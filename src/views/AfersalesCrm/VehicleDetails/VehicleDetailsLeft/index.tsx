import {
    closestCenter,
    DndContext,
    DragEndEvent,
    DragOverlay,
    DragStartEvent,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import styled from '@emotion/styled';
import { Box, Skeleton } from '@mui/material';
import clsx from 'clsx';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ComponentType, memo, useContext, useMemo, useState } from 'react';
import ReactDOM from 'react-dom';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import { selectVehicleDetails } from 'store/slices/vehicleDetails';
import SimpleErrorBoundary2 from 'utils/errorsHandling/SimpleErrorBoundary2';
import VehicleDetailsCollapsibleSection from '../VehicleDetailsCollapsibleSection';
import { useVehicleFieldsUIState, VehicleDetailsField, VehicleIdContext } from './fields';
import AssignedBdcAdvisorField from './general-info/AssignedBdcAdvisorField';
import CustomerStatusField from './general-info/CustomerStatusField';
import CustomerTypeField from './general-info/CustomerTypeField';
import LastActivityDateField from './general-info/LastActivityDateField';
import LastActivityField from './general-info/LastActivityField';
import MonthsDaysSinceLastActivityField from './general-info/MonthsDaysSinceLastActivityField';
import NextActivityDateField from './general-info/NextActivityDateField';
import PendingCampaignsField from './general-info/PendingCampaignsField';
import ProspectionPriorityField from './general-info/ProspectionPriorityField';
import RecommendedServiceFrequencyField from './general-info/RecommendedServiceFrequencyField';
import RetentionBandField from './general-info/RetentionBandField';
import UsageTypeField from './general-info/UsageTypeField';
import VinStatusField from './general-info/VinStatusField';
import BacThatSoldField from './sales-and-service-info/BacThatSoldField';
import InPmaField from './sales-and-service-info/InPmaField';
import SoldByDealerField from './sales-and-service-info/SoldByDealerField';
import BrandField from './vehicle-info/BrandField';
import LastServiceDateField from './vehicle-info/LastServiceDateField';
import LastServiceDateWithMeField from './vehicle-info/LastServiceDateWithMeField';
import LastServiceDateWithOtherField from './vehicle-info/LastServiceDateWithOtherField';
import MileageField from './vehicle-info/MileageField';
import ModelField from './vehicle-info/ModelField';
import MonthsFromDateOfSaleField from './vehicle-info/MonthsFromDateOfSaleField';
import MonthsSinceLastServiceDateField from './vehicle-info/MonthsSinceLastServiceDateField';
import MonthsSinceLastServiceDateWithMeField from './vehicle-info/MonthsSinceLastServiceDateWithMeField';
import MonthsSinceLastServiceDateWithOtherField from './vehicle-info/MonthsSinceLastServiceDateWithOtherField';
import PlatesField from './vehicle-info/PlatesField';
import SaleDateField from './vehicle-info/SaleDateField';
import VinField from './vehicle-info/VinField';
import YearField from './vehicle-info/YearField';

export type VehicleDetailsFieldProps = {
    label: string;
    fieldKey: string;
    disabled: boolean;
};

const FIELD_COMPONENTS: Record<string, ComponentType<VehicleDetailsFieldProps>> = {
    assignedBdcAdvisor: AssignedBdcAdvisorField,
    customerStatus: CustomerStatusField,
    vinStatus: VinStatusField,
    customerType: CustomerTypeField,
    prospectionPriority: ProspectionPriorityField,
    retentionBand: RetentionBandField,
    pendingCampaigns: PendingCampaignsField,
    lastActivity: LastActivityField,
    lastActivityDate: LastActivityDateField,
    monthsDaysSinceLastActivity: MonthsDaysSinceLastActivityField,
    nextActivityDate: NextActivityDateField,

    brand: BrandField,
    model: ModelField,
    year: YearField,
    plates: PlatesField,
    vin: VinField,
    mileage: MileageField,
    saleDate: SaleDateField,
    monthsFromDateOfSale: MonthsFromDateOfSaleField,
    lastServiceDate: LastServiceDateField,
    monthsSinceLastService: MonthsSinceLastServiceDateField,
    lastServiceDateWithMe: LastServiceDateWithMeField,
    monthsSinceLastServiceWithMe: MonthsSinceLastServiceDateWithMeField,
    lastServiceDateWithAnother: LastServiceDateWithOtherField,
    monthsSinceLastServiceWithAnother: MonthsSinceLastServiceDateWithOtherField,
    usageType: UsageTypeField,
    recommendedServiceFrequency: RecommendedServiceFrequencyField,

    soldByDealer: SoldByDealerField,
    bacThatSold: BacThatSoldField,
    inPma: InPmaField,
};

for (const key in FIELD_COMPONENTS) {
    FIELD_COMPONENTS[key] = memo(FIELD_COMPONENTS[key]);
}

export default function VehicleDetailsLeft({ vehicleId }: { vehicleId: string }) {
    const { t } = useAppTranslation();

    const allowEditVehicles = useAppSelector((r) => selectUserPermission(r).allowEditVehicles);
    const [uiState, updateSection] = useVehicleFieldsUIState();

    const fields = uiState.data.sections;

    return (
        <SimpleErrorBoundary2>
            <Box sx={{ display: 'flex', gap: 1, pt: 2, pb: 4, flexDirection: 'column' }}>
                <VehicleIdContext.Provider value={vehicleId}>
                    {fields.map((section) => {
                        return (
                            <VehicleDetailsCollapsibleSection
                                key={section.key}
                                initialOpen={section.key === 'generalInfo'}
                                title={t(`afterSalesCrm.vehicleDetails.sections.${section.key}`)}
                            >
                                <VehicleDetailsLeftSection
                                    allowEditVehicles={allowEditVehicles}
                                    fields={section.fields}
                                    onFieldOrderChanged={(fields) =>
                                        updateSection(section.key, fields)
                                    }
                                />
                            </VehicleDetailsCollapsibleSection>
                        );
                    })}
                </VehicleIdContext.Provider>
            </Box>
        </SimpleErrorBoundary2>
    );
}

// some random numbers for skeleton
const PREGENERATED_RNG = [
    0.45219, 0.73581, 0.14529, 0.96911, 0.8322, 0.1937, 0.61959, 0.45055, 0.06039, 0.92233, 0.48758,
    0.76676, 0.29349, 0.8197, 0.83851, 0.84118, 0.91123, 0.32765, 0.50668, 0.80497, 0.63338,
];

function getWidth(field: string): string {
    const num = PREGENERATED_RNG[cyrb53(field) % PREGENERATED_RNG.length];
    return `${70 + 30 * num}%`;
}

function VehicleDetailsLeftSection({
    fields: fieldsList,
    onFieldOrderChanged,
    allowEditVehicles,
}: {
    allowEditVehicles: boolean;
    fields: VehicleDetailsField[];
    onFieldOrderChanged: (fields: VehicleDetailsField[]) => void;
}) {
    const { t } = useAppTranslation();
    const vehicleId = useContext(VehicleIdContext);
    const [fields, setFields] = useState(fieldsList);
    const fieldIds = useMemo(() => fields.map((x) => x.key), [fields]);
    const [activeFieldId, setActiveFieldId] = useState('');

    const hasResponse = useAppSelector((r) => !!selectVehicleDetails(r, vehicleId).details);

    if (!hasResponse) {
        return (
            <Box sx={{ padding: '20px 30px', display: 'flex', flexDirection: 'column', gap: 3.5 }}>
                {fields.map((f) => (
                    <Skeleton key={f.key} height={24} width={getWidth(f.key)} />
                ))}
            </Box>
        );
    }

    return (
        <DivSectionInner>
            <DndContext
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                collisionDetection={closestCenter}
            >
                <SortableContext items={fieldIds} strategy={verticalListSortingStrategy}>
                    {ReactDOM.createPortal(
                        <DragOverlay style={{ zIndex: 1000 }}>
                            <DivOverlay>
                                {activeFieldId &&
                                    (() => {
                                        const Component = FIELD_COMPONENTS[activeFieldId];
                                        if (Component)
                                            return (
                                                <Component
                                                    disabled={!allowEditVehicles}
                                                    fieldKey={activeFieldId}
                                                    label={t(
                                                        `afterSalesCrm.vehicleDetails.fields.${activeFieldId}`
                                                    )}
                                                />
                                            );
                                    })()}
                            </DivOverlay>
                        </DragOverlay>,
                        document.body
                    )}
                    {fields.map((field) => {
                        return (
                            <FieldSortable
                                key={field.key}
                                fieldKey={field.key}
                                allowEditVehicles={allowEditVehicles}
                                label={t(`afterSalesCrm.vehicleDetails.fields.${field.key}`)}
                            />
                        );
                    })}
                </SortableContext>
            </DndContext>
        </DivSectionInner>
    );

    function handleDragStart(event: DragStartEvent) {
        if (typeof event.active.id === 'string' && FIELD_COMPONENTS[event.active.id])
            setActiveFieldId(event.active.id);
    }

    function handleDragEnd(event: DragEndEvent) {
        const { active, over } = event;
        setActiveFieldId('');

        if (over && active.id !== over.id) {
            const oldIndex = fields.findIndex((x) => x.key === active.id);
            const newIndex = fields.findIndex((x) => x.key === over.id);
            if (!(oldIndex === -1 || newIndex === -1)) {
                const newFields = arrayMove(fields, oldIndex, newIndex);
                setFields(newFields);
                onFieldOrderChanged(newFields);
            }
        }
    }
}

const DivSectionInner = styled('div')({
    padding: '0 30px',

    '& > *': {
        marginBottom: '10px',

        '&:last-child': {
            marginBottom: '16px',
        },
    },
});

function cyrb53(str: string, seed = 0) {
    let h1 = 0xdeadbeef ^ seed,
        h2 = 0x41c6ce57 ^ seed;
    for (let i = 0, ch; i < str.length; i++) {
        ch = str.charCodeAt(i);
        h1 = Math.imul(h1 ^ ch, 2654435761);
        h2 = Math.imul(h2 ^ ch, 1597334677);
    }
    h1 = Math.imul(h1 ^ (h1 >>> 16), 2246822507);
    h1 ^= Math.imul(h2 ^ (h2 >>> 13), 3266489909);
    h2 = Math.imul(h2 ^ (h2 >>> 16), 2246822507);
    h2 ^= Math.imul(h1 ^ (h1 >>> 13), 3266489909);

    return 4294967296 * (2097151 & h2) + (h1 >>> 0);
}

function FieldSortable({
    fieldKey,
    label,
    allowEditVehicles,
}: {
    fieldKey: string;
    label: string;
    allowEditVehicles: boolean;
}) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
        id: fieldKey,
    });

    const style = {
        transform: CSS.Transform.toString(
            transform ? { y: transform.y, x: 0, scaleX: 1, scaleY: 1 } : null
        ),
        zIndex: isDragging ? 2 : undefined,
        transition,
    };

    const Component = FIELD_COMPONENTS[fieldKey];

    return (
        <DivFieldSortableRoot
            className={clsx({
                dragging: isDragging,
            })}
            ref={setNodeRef}
            style={style}
            {...attributes}
        >
            <DivFieldSortableHandle className="vd-field-handle" {...listeners}>
                <DragAndDropIcon fill="var(--neutral7)" size={26} />
            </DivFieldSortableHandle>
            {Component && (
                <Component fieldKey={fieldKey} label={label} disabled={!allowEditVehicles} />
            )}
        </DivFieldSortableRoot>
    );
}

const DivOverlay = styled('div')({
    position: 'relative',
    cursor: 'grabbing',

    '&::after': {
        content: '""',
        display: 'block',
        position: 'absolute',
        top: -8,
        right: -8,
        bottom: -8,
        left: -28,
        zIndex: -1,
        backgroundColor: 'var(--neutral3)',
        boxShadow: '0 4px 12px -5px var(--neutral8)',
        borderRadius: 8,
        cursor: 'grabbing',
    },
});

const DivFieldSortableRoot = styled('div')({
    backgroundColor: 'var(--neutral1)',
    position: 'relative',

    '&.dragging': {
        '& > *': {
            visibility: 'hidden',
        },

        backgroundColor: 'var(--neutral2)',
        outline: '2px dashed var(--neutral4)',
        borderRadius: 10,
    },

    '& .vd-field-handle': {
        opacity: 0,
    },

    '&:hover .vd-field-handle': {
        opacity: 1,
    },
});

const DivFieldSortableHandle = styled('div')({
    position: 'absolute',
    right: '100%',
    top: 21,
    bottom: 0,
    display: 'flex',
    alignItems: 'center',
    cursor: 'grab',
});
