import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function InPmaField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.inPma);
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.inPma')}
        >
            <TextFormField
                disabled={disabled}
                label={label}
                value={value === null ? '--' : t(`commonLabels.${value ? 'yes' : 'no'}`)}
                cmosVariant="grey"
                readonly
            />
        </ReadonlyFieldTooltip>
    );
}
