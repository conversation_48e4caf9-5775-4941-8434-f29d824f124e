import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function BacThatSoldField({ label, disabled }: VehicleDetailsFieldProps) {
    const bacThatSold = useVehicleDetailsSelector((v) => v.bacThatSold);
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip text={t('afterSalesCrm.vehicleDetails.readonlyField.bacThatSold')}>
            <TextFormField
                disabled={disabled}
                cmosVariant="grey"
                readonly
                placeholder={label}
                label={label}
                value={bacThatSold || '--'}
            />
        </ReadonlyFieldTooltip>
    );
}
