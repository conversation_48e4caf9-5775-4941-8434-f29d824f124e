import { SelectChangeEvent } from '@mui/material';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SMenuItem } from 'common/components/mui';
import { SSelectGrey } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function SoldByDealerField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) => v.soldByDealer);
    const { t } = useAppTranslation();
    const update = useVehicleUpdater();

    function handleChange(e: SelectChangeEvent<string>) {
        let value: boolean;

        if (e.target.value === 'true') value = true;
        else if (e.target.value === 'false') value = false;
        else throw new Error('unexpected input value for boolean input ' + e.target.value);

        update({ soldByDealer: value });
    }

    return (
        <InputWrapper label={label} disabled={disabled}>
            <SSelectGrey
                disabled={disabled}
                placeholder={t('afterSalesCrm.vehicleDetails.fields.soldByDealerPlaceholder')}
                value={value === null ? '' : `${value}`}
                onChange={handleChange}
            >
                <SMenuItem value="true">{t('commonLabels.yes')}</SMenuItem>
                <SMenuItem value="false">{t('commonLabels.no')}</SMenuItem>
            </SSelectGrey>
        </InputWrapper>
    );
}
