import InputWrapper, { InputWrapperProps } from 'common/components/Inputs/InputWrapper';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { forwardRef, useMemo } from 'react';

const MonthsSinceField = forwardRef(
    (
        {
            label,
            value,
            disabled,
            ...inputWrapperProps
        }: {
            label: string;
            value: number | null;
            disabled: boolean;
        } & Pick<
            InputWrapperProps,
            'onMouseEnter' | 'onMouseLeave' | 'onFocus' | 'onBlur' | 'aria-describedby'
        >,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const { t } = useAppTranslation();

        const text = useMemo(() => {
            if (value === null) return '--';

            if (value === 1) {
                return `1 ${t('commonLabels.timePeriods.M-singular')}`;
            }

            return `${value} ${t('commonLabels.timePeriods.M')}`;
        }, [t, value]);

        return (
            <InputWrapper disabled={disabled} ref={ref} label={label} {...inputWrapperProps}>
                <TextField
                    disabled
                    InputProps={{
                        readOnly: true,
                    }}
                    fullWidth
                    cmosVariant="grey"
                    value={text}
                />
            </InputWrapper>
        );
    }
);

export default MonthsSinceField;
