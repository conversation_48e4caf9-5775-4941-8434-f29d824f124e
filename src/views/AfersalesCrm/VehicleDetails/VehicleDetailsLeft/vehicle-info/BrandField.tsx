import InputWrapper from 'common/components/Inputs/InputWrapper';
import BrandPicker from 'views/Components/BrandPicker';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function BrandField({ label, disabled }: VehicleDetailsFieldProps) {
    const brand = useVehicleDetailsSelector((v) => v.brand);
    const update = useVehicleUpdater();

    return (
        <InputWrapper label={label}>
            <BrandPicker
                disabled={disabled}
                value={brand}
                onChange={(brand) => update({ brand: brand || '', model: '' })}
            />
        </InputWrapper>
    );
}
