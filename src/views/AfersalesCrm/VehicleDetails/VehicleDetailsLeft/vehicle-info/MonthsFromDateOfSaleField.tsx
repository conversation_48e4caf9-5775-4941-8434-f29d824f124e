import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import MonthsSinceField from '../MonthsSinceField';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function MonthsFromDateOfSaleField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) =>
        v.dateOfSale ? v.dateOfSale.monthsSince : null
    );
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            text={t('afterSalesCrm.vehicleDetails.readonlyField.monthsFromDateOfSale')}
        >
            <MonthsSinceField disabled={disabled} value={value} label={label} />
        </ReadonlyFieldTooltip>
    );
}
