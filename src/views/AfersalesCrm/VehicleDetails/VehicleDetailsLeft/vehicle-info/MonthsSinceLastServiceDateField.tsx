import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import MonthsSinceField from '../MonthsSinceField';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function MonthsSinceLastServiceDateField({
    label,
    disabled,
}: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) =>
        v.lastService ? v.lastService.monthsSince : null
    );
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.monthsSinceLastService')}
        >
            <MonthsSinceField disabled={disabled} label={label} value={value} />
        </ReadonlyFieldTooltip>
    );
}
