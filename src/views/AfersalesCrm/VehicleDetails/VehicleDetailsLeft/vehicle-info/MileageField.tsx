import { NumberField } from 'common/components/Inputs/NumberField';
import { useState } from 'react';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';
import InputWrapperWithLoader from '../InputWrapperWithLoader';

export default function MileageField({ label, disabled }: VehicleDetailsFieldProps) {
    const mileage = useVehicleDetailsSelector((v) => v.mileage);
    const update = useVehicleUpdater();
    const [pendingCount, setPendingCount] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const [value, setValue] = useState(mileage);

    return (
        <InputWrapperWithLoader
            disabled={disabled}
            isPending={pendingCount > 0}
            isFocused={isFocused}
            htmlFor="vd-mileage"
            label={label}
        >
            <NumberField
                disabled={disabled}
                placeholder={label}
                cmosVariant="grey"
                id="vd-mileage"
                fullWidth
                value={value}
                onValueChange={({ floatValue }) => setValue(floatValue ?? null)}
                onFocus={handleFocus}
                onBlur={handleBlur}
                formatProps={{ thousandSeparator: true }}
            />
        </InputWrapperWithLoader>
    );

    async function handleBlur() {
        if (value === mileage) return;

        setPendingCount((x) => x + 1);
        try {
            await update({
                mileage: value,
            });
        } finally {
            setPendingCount((x) => x - 1);
        }
    }

    function handleFocus() {
        setValue(mileage);
        setIsFocused(true);
    }
}
