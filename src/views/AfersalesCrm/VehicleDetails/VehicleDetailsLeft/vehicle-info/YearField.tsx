import InputWrapper from 'common/components/Inputs/InputWrapper';
import YearPicker from 'views/Components/YearPicker';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function YearField({ label, disabled }: VehicleDetailsFieldProps) {
    const year = useVehicleDetailsSelector((v) => v.year);

    const update = useVehicleUpdater();

    return (
        <InputWrapper label={label} disabled={disabled}>
            <YearPicker
                disabled={disabled}
                disableApi
                brandName=""
                modelName=""
                value={year}
                onChange={(year) => update({ year: year || '' })}
            />
        </InputWrapper>
    );
}
