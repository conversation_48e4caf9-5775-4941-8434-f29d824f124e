import DateFormField from 'common/components/Inputs/DateFormField';
import { DateTime } from 'luxon';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function LastServiceDateWithMeField({ label, disabled }: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) =>
        v.lastServiceWithMe
            ? DateTime.fromFormat(v.lastServiceWithMe.date, 'yyyy-MM-dd').toJSDate()
            : null
    );
    const update = useVehicleUpdater();

    return (
        <DateFormField
            disabled={disabled}
            placeholder={label}
            fullWidth
            variant="grey"
            label={label}
            value={value}
            onChange={(value) => {
                update({
                    lastServiceDateWithMe: value
                        ? DateTime.fromJSDate(value).toFormat('yyyy-MM-dd')
                        : null,
                });
            }}
        />
    );
}
