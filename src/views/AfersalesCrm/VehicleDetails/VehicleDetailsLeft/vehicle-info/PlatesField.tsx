import { VehiclesCrmApi } from 'api/customers';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useContext, useEffect, useState } from 'react';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater, VehicleIdContext } from '../fields';
import InputWrapperWithLoader from '../InputWrapperWithLoader';

export default function PlatesField({ label, disabled }: VehicleDetailsFieldProps) {
    const plates = useVehicleDetailsSelector((v) => v.plates);
    const update = useVehicleUpdater();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const vehicleId = useContext(VehicleIdContext);

    const [invalid, setInvalid] = useState(false);
    const [pendingCount, setPendingCount] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const [value, setValue] = useState(plates);

    useEffect(() => {
        setValue(plates);
    }, [plates]);

    return (
        <InputWrapperWithLoader
            isPending={pendingCount > 0}
            isFocused={isFocused}
            htmlFor="vd-plates"
            label={label}
            disabled={disabled}
        >
            <TextField
                placeholder={t('afterSalesCrm.vehicleDetails.fields.platesPlaceholder')}
                cmosVariant="grey"
                id="vd-plates"
                fullWidth
                aria-invalid={invalid ? 'true' : 'false'}
                value={value}
                onChange={(e) => setValue(e.target.value.toUpperCase())}
                onFocus={handleFocus}
                onBlur={handleBlur}
                disabled={disabled}
            />
        </InputWrapperWithLoader>
    );

    async function handleBlur() {
        setIsFocused(false);

        if (value === plates) {
            return;
        }

        setPendingCount((x) => x + 1);

        try {
            const exists = await VehiclesCrmApi.checkVehicleExists({
                plates: value,
                notWithId: vehicleId,
            });

            setInvalid(exists);

            if (exists) {
                toasters.danger(
                    t('afterSalesCrm.vehicleDetails.fields.platesAlreadyRegistered.text'),
                    t('afterSalesCrm.vehicleDetails.fields.platesAlreadyRegistered.title')
                );
                return;
            }

            await update({
                plates: value,
            });
        } finally {
            setPendingCount((x) => x - 1);
        }
    }

    function handleFocus() {
        setValue(plates);
        setIsFocused(true);
        setInvalid(false);
    }
}
