import DateFormField from 'common/components/Inputs/DateFormField';
import { DateTime } from 'luxon';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function SaleDateField({ label, disabled }: VehicleDetailsFieldProps) {
    const saleDate = useVehicleDetailsSelector((v) =>
        v.dateOfSale ? DateTime.fromFormat(v.dateOfSale.date, 'yyyy-MM-dd').toJSDate() : null
    );
    const update = useVehicleUpdater();

    return (
        <DateFormField
            disabled={disabled}
            placeholder={label}
            fullWidth
            variant="grey"
            label={label}
            value={saleDate}
            onChange={(value) => {
                update({
                    saleDate: value ? DateTime.fromJSDate(value).toFormat('yyyy-MM-dd') : null,
                });
            }}
        />
    );
}
