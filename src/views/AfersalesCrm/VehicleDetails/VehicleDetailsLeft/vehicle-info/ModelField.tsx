import InputWrapper from 'common/components/Inputs/InputWrapper';
import ModelPicker from 'views/Components/ModelPicker';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater } from '../fields';

export default function ModelField({ label, disabled }: VehicleDetailsFieldProps) {
    const { brand, model } = useVehicleDetailsSelector((v) => ({
        model: v.model,
        brand: v.brand,
    }));

    const update = useVehicleUpdater();

    return (
        <InputWrapper label={label} disabled={disabled}>
            <ModelPicker
                disabled={disabled}
                brandName={brand}
                value={model}
                onChange={(_, model) => {
                    update({ model: model ? model.name : '' });
                }}
            />
        </InputWrapper>
    );
}
