import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import MonthsSinceField from '../MonthsSinceField';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function MonthsSinceLastServiceDateWithOtherField({
    label,
    disabled,
}: VehicleDetailsFieldProps) {
    const value = useVehicleDetailsSelector((v) =>
        v.lastServiceWithAnother ? v.lastServiceWithAnother.monthsSince : null
    );
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.monthsSinceLastServiceWithAnother')}
        >
            <MonthsSinceField disabled={disabled} label={label} value={value} />
        </ReadonlyFieldTooltip>
    );
}
