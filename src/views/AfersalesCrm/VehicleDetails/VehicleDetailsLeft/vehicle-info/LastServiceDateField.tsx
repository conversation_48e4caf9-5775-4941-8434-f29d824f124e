import DateFormField from 'common/components/Inputs/DateFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector } from '../fields';
import ReadonlyFieldTooltip from '../ReadonlyFieldTooltip';

export default function LastServiceDateField({ label, disabled }: VehicleDetailsFieldProps) {
    const lastServiceDate = useVehicleDetailsSelector((v) =>
        v.lastService ? DateTime.fromFormat(v.lastService.date, 'yyyy-MM-dd').toJSDate() : null
    );
    const { t } = useAppTranslation();

    return (
        <ReadonlyFieldTooltip
            disabled={disabled}
            text={t('afterSalesCrm.vehicleDetails.readonlyField.lastServiceDate')}
        >
            <div>
                <DateFormField
                    disabled={disabled}
                    placeholder={label}
                    fullWidth
                    variant="grey"
                    label={label}
                    value={lastServiceDate}
                    readOnly
                />
            </div>
        </ReadonlyFieldTooltip>
    );
}
