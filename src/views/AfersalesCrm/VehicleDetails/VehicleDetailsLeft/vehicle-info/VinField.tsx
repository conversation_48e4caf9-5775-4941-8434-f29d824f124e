import { VehiclesCrmApi } from 'api/customers';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useContext, useEffect, useState } from 'react';
import { VehicleDetailsFieldProps } from '..';
import { useVehicleDetailsSelector, useVehicleUpdater, VehicleIdContext } from '../fields';
import InputWrapperWithLoader from '../InputWrapperWithLoader';

export default function VinField({ label, disabled }: VehicleDetailsFieldProps) {
    const vin = useVehicleDetailsSelector((v) => v.vin);
    const update = useVehicleUpdater();
    const { t } = useAppTranslation();
    const vehicleId = useContext(VehicleIdContext);
    const [pendingCount, setPendingCount] = useState(0);
    const [isFocused, setIsFocused] = useState(false);
    const [invalid, setInvalid] = useState(false);
    const [value, setValue] = useState(vin);
    const toasters = useToasters();

    useEffect(() => {
        setValue(vin);
    }, [vin]);

    return (
        <InputWrapperWithLoader
            disabled={disabled}
            isPending={pendingCount > 0}
            isFocused={isFocused}
            htmlFor="vd-vin"
            label={label}
        >
            <TextField
                disabled={disabled}
                placeholder={t('afterSalesCrm.vehicleDetails.fields.vinPlaceholder')}
                cmosVariant="grey"
                id="vd-vin"
                fullWidth
                aria-invalid={invalid ? 'true' : 'false'}
                value={value}
                onChange={(e) => {
                    setValue(e.target.value.toUpperCase());
                    if (invalid) setInvalid(false);
                }}
                onFocus={handleFocus}
                onBlur={handleBlur}
            />
        </InputWrapperWithLoader>
    );

    async function handleBlur() {
        setIsFocused(false);

        if (value === vin) {
            return;
        }

        setPendingCount((x) => x + 1);

        try {
            const exists = await VehiclesCrmApi.checkVehicleExists({
                vin: value,
                notWithId: vehicleId,
            });

            setInvalid(exists);

            if (exists) {
                toasters.danger(
                    t('afterSalesCrm.vehicleDetails.fields.vinAlreadyRegistered.text'),
                    t('afterSalesCrm.vehicleDetails.fields.vinAlreadyRegistered.title')
                );
                return;
            }

            await update({
                vin: value,
            });
        } finally {
            setPendingCount((x) => x - 1);
        }
    }

    function handleFocus() {
        setValue(vin);
        setIsFocused(true);
        setInvalid(false);
    }
}
