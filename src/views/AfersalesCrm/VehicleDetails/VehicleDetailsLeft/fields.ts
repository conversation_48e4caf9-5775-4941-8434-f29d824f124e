import { useQuery, useQueryClient } from '@tanstack/react-query';
import AccountApi from 'api/account';
import { VehicleDetailsDto } from 'api/customers';
import debounce from 'lodash/debounce';
import queryClient from 'query-client';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectVehicleDetails, updateVehicleThunk } from 'store/slices/vehicleDetails';
import { UpdateVehicleThunkUpdate } from 'store/slices/vehicleDetails/thunks/updateVehicle';
import { z } from 'zod';

const fieldSchema = z.object({
    key: z.string(),
});

const vehicleDetailsSectionSchema = z.object({
    key: z.string(),
    fields: z.array(fieldSchema),
});

export type VehicleDetailsField = z.infer<typeof fieldSchema>;

export type VehicleDetailsSection = z.infer<typeof vehicleDetailsSectionSchema>;

export const DEFAULT_FIELDS: VehicleDetailsSection[] = [
    {
        key: 'generalInfo',
        fields: [
            { key: 'assignedBdcAdvisor' },
            { key: 'customerStatus' },
            { key: 'vinStatus' },
            { key: 'customerType' },
            { key: 'prospectionPriority' },
            { key: 'retentionBand' },
            { key: 'pendingCampaigns' },
            { key: 'lastActivity' },
            { key: 'lastActivityDate' },
            { key: 'monthsDaysSinceLastActivity' },
            { key: 'nextActivityDate' },
        ],
    },
    {
        key: 'vehicleInfo',
        fields: [
            { key: 'brand' },
            { key: 'model' },
            { key: 'year' },
            { key: 'plates' },
            { key: 'vin' },
            { key: 'mileage' },
            { key: 'saleDate' },
            { key: 'monthsFromDateOfSale' },
            { key: 'lastServiceDate' },
            { key: 'monthsSinceLastService' },
            { key: 'lastServiceDateWithMe' },
            { key: 'monthsSinceLastServiceWithMe' },
            { key: 'lastServiceDateWithAnother' },
            { key: 'monthsSinceLastServiceWithAnother' },
            { key: 'usageType' },
            { key: 'recommendedServiceFrequency' },
        ],
    },
    {
        key: 'salesAndServiceInfo',
        fields: [{ key: 'soldByDealer' }, { key: 'bacThatSold' }, { key: 'inPma' }],
    },
];

const VehicleFieldsUIStateSchema = z.object({
    ver: z.literal('1'),
    data: z.object({
        sections: z.array(vehicleDetailsSectionSchema),
    }),
});

export type VehicleFieldsUIState = z.infer<typeof VehicleFieldsUIStateSchema>;

const DEFAULT_STATE: VehicleFieldsUIState = {
    ver: '1',
    data: {
        sections: DEFAULT_FIELDS,
    },
};

const queryKey = ['vehicle-details', 'fields-ui-state'];
const accountStorageKey = 'crm.vd.fields_ui';

const debouncedAccountSetValue = debounce(AccountApi.setValue, 500);

export function useVehicleFieldsUIState(): [
    state: VehicleFieldsUIState,
    updateSection: (sectionKey: string, fields: VehicleDetailsField[]) => void
] {
    const { data } = useQuery({
        queryKey,
        queryFn: () => AccountApi.getValue(accountStorageKey),
        cacheTime: Infinity,
        staleTime: 2000,
    });

    const state = useMemo(() => parseVehicleFieldsUIState(data || ''), [data]);
    const stateRef = useRef(state);
    stateRef.current = state;

    const queryClient = useQueryClient();

    const updateSection = useCallback(
        (sectionKey: string, fields: VehicleDetailsField[]) => {
            const newState: VehicleFieldsUIState = {
                ver: '1',
                data: {
                    sections: [...stateRef.current.data.sections],
                },
            };

            const section = newState.data.sections.find((x) => x.key === sectionKey);

            if (section) {
                section.fields = fields;
                const newJSONState = JSON.stringify(newState);
                debouncedAccountSetValue(accountStorageKey, newJSONState);
                queryClient.setQueryData(queryKey, newJSONState);
            }
        },
        [queryClient]
    );

    return [state, updateSection];
}

export async function fetchVehicleFieldsUIState() {
    try {
        await queryClient.fetchQuery({
            queryKey,
            queryFn: () => AccountApi.getValue(accountStorageKey),
            cacheTime: Infinity,
            staleTime: 5000,
        });
    } catch (e) {
        console.error('failed to fetch fields ui state', e);
    }
}

export function parseVehicleFieldsUIState(value: string): VehicleFieldsUIState {
    if (!value) return DEFAULT_STATE;

    let o: unknown;

    try {
        o = JSON.parse(value);
    } catch {
        return DEFAULT_STATE;
    }

    const result = VehicleFieldsUIStateSchema.safeParse(o);

    if (result.success) {
        for (const section of result.data.data.sections) {
            const defaultSection = DEFAULT_FIELDS.find((x) => x.key === section.key);
            if (!defaultSection) continue;
            const missingFields = defaultSection.fields.filter(
                (f) => !section.fields.some((x) => x.key === f.key)
            );
            section.fields.push(...missingFields);
        }

        const missingSections = DEFAULT_FIELDS.filter(
            (s) => !result.data.data.sections.some((x) => x.key === s.key)
        );

        result.data.data.sections.push(...missingSections);

        return result.data;
    }

    return DEFAULT_STATE;
}

export const VehicleIdContext = createContext<string>('');

export function useVehicleDetailsSelector<T>(selector: (vehicle: VehicleDetailsDto) => T): T {
    const vehicleId = useContext(VehicleIdContext);

    const result = useAppSelector((r) => {
        const vehicle = selectVehicleDetails(r, vehicleId);
        if (!vehicle.details)
            throw new Error('cannot use useVehicleDetailsSelector when vehicle is not fetched');

        return selector(vehicle.details.details);
    });

    return result;
}

export function useVehicleUpdater() {
    const vehicleId = useContext(VehicleIdContext);
    const dispatch = useAppDispatch();

    const update = useCallback(
        async (payload: UpdateVehicleThunkUpdate) => {
            await dispatch(
                updateVehicleThunk({
                    id: vehicleId,
                    update: payload,
                })
            );
        },
        [dispatch, vehicleId]
    );

    return update;
}
