import { CircularProgress, IconButton, styled } from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import InputWrapper, { InputWrapperProps } from 'common/components/Inputs/InputWrapper';
import { ForwardedRef, forwardRef } from 'react';

export type InputWrapperWithLoaderState = 'idle' | 'pending' | 'active';

const InputWrapperWithLoader = forwardRef(
    (
        {
            children,
            isFocused,
            isPending,
            ...props
        }: InputWrapperProps & { isFocused: boolean; isPending: boolean },
        ref: ForwardedRef<HTMLDivElement>
    ) => {
        return (
            <InputWrapper ref={ref} {...props}>
                {children}
                {(isFocused || isPending) && (
                    <RightAdornment>
                        {isPending ? (
                            <CircularProgress size={20} thickness={3} />
                        ) : (
                            <IconButton sx={{ p: 0 }}>
                                <CheckIcon />
                            </IconButton>
                        )}
                    </RightAdornment>
                )}
            </InputWrapper>
        );
    }
);

const RightAdornment = styled('div')({
    height: 32,
    width: 28,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'end',
});

export default InputWrapperWithLoader;
