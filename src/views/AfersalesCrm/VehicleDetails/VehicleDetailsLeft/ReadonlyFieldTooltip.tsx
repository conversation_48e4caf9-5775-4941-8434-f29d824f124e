import { Box } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React from 'react';

export default function ReadonlyFieldTooltip({
    text,
    children,
    disabled = false,
}: {
    text: string;
    children: React.ReactElement;
    disabled?: boolean;
}) {
    const { t } = useAppTranslation();

    return (
        <ArrowTooltip
            position="right"
            disabled={disabled}
            content={
                <Box
                    sx={(theme) => ({
                        color: theme.palette.neutral[8],
                        ...theme.typography.h7Inter,
                        p: '3px',
                    })}
                >
                    <strong style={{ fontWeight: '700' }}>
                        {t('afterSalesCrm.vehicleDetails.readonlyField.header')}
                    </strong>
                    <br />
                    <span style={{ fontWeight: 'normal' }}>{text}</span>
                </Box>
            }
        >
            {children}
        </ArrowTooltip>
    );
}
