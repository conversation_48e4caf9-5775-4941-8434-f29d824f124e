import { ExpandLess, ExpandMore } from '@mui/icons-material';
import { ButtonBase, Collapse, styled } from '@mui/material';
import React, { useState } from 'react';

type VehicleDetailsCollapsibleSectionProps = React.PropsWithChildren<{
    title: string;
    initialOpen?: boolean;
}>;

export default function VehicleDetailsCollapsibleSection({
    title,
    children,
    initialOpen = false,
}: VehicleDetailsCollapsibleSectionProps) {
    const [open, setOpen] = useState(initialOpen);

    return (
        <DivRoot>
            <DivCollapseHeader
                data-open={open}
                component="div"
                role="button"
                onClick={() => setOpen((x) => !x)}
            >
                <Title>{title}</Title>
                {open ? <ExpandLess /> : <ExpandMore />}
            </DivCollapseHeader>
            <Collapse in={open}>{children}</Collapse>
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    borderRadius: 10,
    backgroundColor: 'var(--neutral1)',
    overflow: 'hidden',
});

const DivCollapseHeader = styled(ButtonBase)({
    backgroundColor: 'var(--neutral3)',
    padding: '8px 30px',
    display: 'grid',
    gridTemplateColumns: '1fr 30px',
    cursor: 'pointer',

    '&:hover': {
        color: 'var(--neutral9)',
    },

    outline: '1px solid transparent',
    outlineOffset: -1,
    transition: 'outline-color 0.1s',
    borderRadius: '10px',

    '&[data-open=true]': {
        borderRadius: '10px 10px 0 0',
    },

    '&:active': {
        outlineColor: 'var(--neutral5)',
    },

    '& .MuiTouchRipple-rippleVisible': {
        animationDuration: '100ms',
    },

    '& .MuiTouchRipple-root': {
        opacity: 0.3,
    },
}) as typeof ButtonBase;

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--neutral8)',
    verticalAlign: 'center',
    height: '100%',
    lineHeight: '2',
}));
