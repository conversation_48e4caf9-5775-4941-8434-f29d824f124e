import { useEffect, useMemo, useRef, useState } from 'react';
import {
    Box,
    Button,
    CircularProgress,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    MenuItem,
    Select,
    styled,
    TextField,
    Typography,
} from '@mui/material';
import { Close, ExpandMore } from '@mui/icons-material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import CustomersApi, { VehicleContactEditDto } from 'api/customers';
import MaskedTextFormField from 'common/components/Inputs/MaskedTextFormField';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { numberPhoneUnformatter, phoneFormatRegexMask } from 'common/FormatersHelper';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useMutation, useQuery } from '@tanstack/react-query';
import useToasters from 'common/hooks/useToasters';
import { fetchVehicleThunk, selectVehicleDetails } from 'store/slices/vehicleDetails';
import { useParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material/Select/SelectInput';
import { useDebounce } from 'use-debounce';
import { useVehicleDetailsRightColumnContext } from '../VehicleDetailsRightColumnContext';

type ContactFormData = {
    customerId: string | null;
    firstName: string;
    lastName: string;
    phone: string;
    mobile: string;
    email: string;
    isFirstOwner: boolean | null;
    businessName: string;
    taxId: string;
    street: string;
    neighborhood: string;
    municipality: string;
    zipCode: string;
    city: string;
    state: string;
    taxEmail: string;
};

export default function EditVehicleContact() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const [formData, setFormData] = useState<ContactFormData>({
        customerId: null,
        firstName: '',
        lastName: '',
        phone: '',
        mobile: '',
        email: '',
        isFirstOwner: null,
        businessName: '',
        taxId: '',
        street: '',
        neighborhood: '',
        municipality: '',
        zipCode: '',
        city: '',
        state: '',
        taxEmail: '',
    });
    const [errors, setErrors] = useState<Partial<Record<keyof ContactFormData, boolean>>>({});
    const scrollDivRef = useRef(null);
    const toasters = useToasters();
    const { setRightPanelMode, editingContact, setEditingContact } =
        useVehicleDetailsRightColumnContext();
    const [debouncedMobile] = useDebounce(formData.mobile, 300);
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));

    const { internationalization } = useAppSelector(selectSettings);
    const maxLengthPhone = InternationalizationLogic.maxLengthPhone(internationalization);
    const phoneMask = phoneFormatRegexMask(internationalization.phoneNumberFormat);

    const { data, isLoading: isLoadingContact } = useQuery(
        ['vehicleContactDetails', vehicleId, editingContact],
        () => {
            if (!editingContact || !vehicleId) return null;
            return CustomersApi.getVehicleContactDetails({
                customerId: editingContact.customerId,
                vehicleId: vehicleId,
            });
        },
        {
            enabled: !!editingContact && !!vehicleId,
        }
    );

    useEffect(() => {
        if (data && editingContact?.customerId) {
            setFormData({
                customerId: editingContact.customerId,
                firstName: data.firstName || '',
                lastName: data.lastName || '',
                phone: data.landline || '',
                mobile: data.mobile || '',
                email: data.email || '',
                isFirstOwner: data.isFirstOwner,
                businessName: data.businessName || '',
                taxId: data.taxIdentification || '',
                street: data.street || '',
                neighborhood: data.neighborhood || '',
                municipality: data.district || '',
                zipCode: data.zipCode || '',
                city: data.city || '',
                state: data.state || '',
                taxEmail: data.taxEmail || '',
            });
        }
    }, [data, editingContact?.customerId]);

    // Check if this mobile number already exists in vehicle contacts
    useEffect(() => {
        const checkDuplicate = async () => {
            const sanitizedMobile = debouncedMobile?.replace(/\D/g, '');
            if (
                !sanitizedMobile ||
                sanitizedMobile.length < 2 ||
                sanitizedMobile === data?.mobile
            ) {
                return;
            }

            // Check in current vehicle contacts first
            const contacts = details?.details?.contacts ?? [];
            const existsInContacts = contacts.some(
                (c) => c.mobile?.replace(/\D/g, '') === sanitizedMobile
            );
            if (existsInContacts) {
                toasters.danger(
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorMessageLocalAlreadyExist'),
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorTitleLocalAlreadyExist')
                );
                setErrors((prev) => ({ ...prev, mobile: true }));
                return;
            }

            // Check number among all customers
            const userName = await CustomersApi.checkDuplicateMobile(sanitizedMobile);

            if (userName) {
                toasters.danger(
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorMessageAlreadyExist', {
                        name: userName,
                    }),
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorTitleAlreadyExist')
                );
                setErrors((prev) => ({ ...prev, mobile: true }));
            } else {
                setErrors((prev) => ({ ...prev, mobile: false }));
            }
        };

        void checkDuplicate();
    }, [debouncedMobile, details?.details?.contacts, toasters, data?.mobile, t]);

    const updateContactMutation = useMutation({
        mutationFn: (data: VehicleContactEditDto) => {
            if (!editingContact?.customerId) {
                throw new Error('Contact ID is required');
            }
            return CustomersApi.updateVehicleContact(editingContact?.customerId, data);
        },
        onSuccess: () => {
            dispatch(fetchVehicleThunk({ id: vehicleId }));
            setEditingContact(null);
            setRightPanelMode(null);
            toasters.success(
                t('afterSalesCrm.vehicleDetails.contactDetails.updateSuccessBody'),
                t('afterSalesCrm.vehicleDetails.contactDetails.updateSuccessTitle')
            );
        },
        onError: () => {
            toasters.danger(
                t('afterSalesCrm.vehicleDetails.contactDetails.errorMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.errorTitle')
            );
        },
    });

    const handleChange =
        (field: keyof ContactFormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            setFormData({
                ...formData,
                [field]: e.target.value,
            });

            // Clear error when field is edited
            if (errors[field]) {
                setErrors({
                    ...errors,
                    [field]: false,
                });
            }
        };

    const handleFirstOwnerChange = (event: SelectChangeEvent) => {
        const value = event.target.value;
        setFormData({
            ...formData,
            isFirstOwner: value === '' ? null : value === 'true',
        });
    };

    const validateForm = (): boolean => {
        const newErrors: Partial<Record<keyof ContactFormData, boolean>> = {};

        if (!formData.firstName.trim()) newErrors.firstName = true;
        if (!formData.mobile) newErrors.mobile = true;

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const onSubmit = async () => {
        if (!validateForm() || !editingContact) return;

        try {
            const contactData: VehicleContactEditDto = {
                firstName: formData.firstName,
                lastName: formData.lastName,
                landline: formData.phone,
                mobile: numberPhoneUnformatter(formData.mobile),
                email: formData.email,
                isFirstOwner: formData.isFirstOwner,
                businessName: formData.businessName,
                taxIdentification: formData.taxId,
                street: formData.street,
                neighborhood: formData.neighborhood,
                district: formData.municipality,
                zipCode: formData.zipCode,
                city: formData.city,
                state: formData.state,
                taxEmail: formData.taxEmail,
            };

            updateContactMutation.mutate(contactData);
        } catch (error) {
            console.error('Error preparing contact data:', error);
        }
    };

    // Check if required fields are filled
    const isFormValid = useMemo(() => {
        return (
            formData.firstName.trim() !== '' &&
            formData.mobile.trim() !== '' &&
            !errors.mobile &&
            !updateContactMutation.isLoading &&
            !isLoadingContact
        );
    }, [
        formData.firstName,
        formData.mobile,
        errors.mobile,
        updateContactMutation.isLoading,
        isLoadingContact,
    ]);

    return (
        <Container>
            <Header>
                <Typography variant="h6">
                    {t('afterSalesCrm.vehicleDetails.contactDetails.editContact')}
                </Typography>
                <IconButton
                    onClick={() => {
                        setRightPanelMode(null);
                        setEditingContact(null);
                    }}
                    size="small"
                >
                    <Close sx={{ color: 'white' }} />
                </IconButton>
            </Header>

            <Content ref={scrollDivRef}>
                {isLoadingContact ? (
                    <LoadingContainer>
                        <CircularProgress size={40} />
                    </LoadingContainer>
                ) : (
                    <>
                        <FormSection>
                            <SectionTitle>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.generalInfo')}
                            </SectionTitle>

                            <Grid container spacing={1}>
                                <Grid item xs={12}>
                                    <FieldLabel sx={{ marginBottom: '-4px' }}>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.name')}{' '}
                                        <Box component="span" sx={{ color: 'blue' }}>
                                            *
                                        </Box>
                                    </FieldLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.name'
                                        )}
                                        value={formData.firstName}
                                        onChange={handleChange('firstName')}
                                        error={!!errors.firstName}
                                        size="small"
                                        disabled={updateContactMutation.isLoading}
                                        inputProps={{
                                            maxLength: 250,
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={6}>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.lastName'
                                        )}
                                        value={formData.lastName}
                                        onChange={handleChange('lastName')}
                                        error={!!errors.lastName}
                                        size="small"
                                        disabled={updateContactMutation.isLoading}
                                        inputProps={{
                                            maxLength: 250,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.phone')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.phone'
                                        )}
                                        value={formData.phone}
                                        onChange={handleChange('phone')}
                                        size="small"
                                        disabled={updateContactMutation.isLoading}
                                        inputProps={{
                                            maxLength: 250,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.mobile')}{' '}
                                        <Box component="span" sx={{ color: 'blue' }}>
                                            *
                                        </Box>
                                    </FieldLabel>
                                    <MobileTextFieldContainer>
                                        <MaskedTextFormField
                                            fullWidth
                                            placeholder={t(
                                                'afterSalesCrm.vehicleDetails.contactDetails.placeholder.mobile'
                                            )}
                                            value={formData.mobile}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                if (value !== formData.mobile) {
                                                    setFormData({
                                                        ...formData,
                                                        mobile: value,
                                                    });

                                                    if (errors.mobile) {
                                                        setErrors({
                                                            ...errors,
                                                            mobile: false,
                                                        });
                                                    }
                                                }
                                            }}
                                            isInvalid={!!errors.mobile}
                                            disabled={updateContactMutation.isLoading}
                                            mask={phoneMask}
                                            maxLength={maxLengthPhone}
                                        />
                                    </MobileTextFieldContainer>
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.email')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.email'
                                        )}
                                        value={formData.email}
                                        onChange={handleChange('email')}
                                        size="small"
                                        disabled={updateContactMutation.isLoading}
                                        inputProps={{
                                            maxLength: 254,
                                        }}
                                    />
                                </Grid>

                                {data?.isOwner && (
                                    <Grid item xs={12}>
                                        <FieldLabel>
                                            {t(
                                                'afterSalesCrm.vehicleDetails.contactDetails.firstOwner'
                                            )}
                                        </FieldLabel>
                                        <FormControl fullWidth size="small">
                                            <Select
                                                value={
                                                    formData.isFirstOwner === null
                                                        ? ''
                                                        : String(formData.isFirstOwner)
                                                }
                                                onChange={handleFirstOwnerChange}
                                                displayEmpty
                                                sx={{
                                                    height: '30px',
                                                    backgroundColor: 'var(--neutral2)',

                                                    '& .MuiSelect-icon': {
                                                        top: '50%',
                                                        transform: 'translateY(-50%)',
                                                    },
                                                    '& .MuiSelect-select': {
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                    },
                                                    '&:hover .MuiOutlinedInput-notchedOutline': {
                                                        borderColor: 'var(--cm2)',
                                                    },
                                                }}
                                                IconComponent={(props) => (
                                                    <InputAdornment position="end" {...props}>
                                                        <ExpandMore fill="var(--neutral6)" />
                                                    </InputAdornment>
                                                )}
                                                renderValue={(selected) => {
                                                    if (selected === '') {
                                                        return (
                                                            <SelectYesNoStyled>
                                                                {t(
                                                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.selectYesOrNo'
                                                                )}
                                                            </SelectYesNoStyled>
                                                        );
                                                    }
                                                    return selected === 'true'
                                                        ? t('afterSalesCrm.values.Yes')
                                                        : t('afterSalesCrm.values.No');
                                                }}
                                                disabled={updateContactMutation.isLoading}
                                            >
                                                <MenuItem value="true">
                                                    {t('afterSalesCrm.values.Yes')}
                                                </MenuItem>
                                                <MenuItem value="false">
                                                    {t('afterSalesCrm.values.No')}
                                                </MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                )}
                            </Grid>
                        </FormSection>

                        <FormSection>
                            <SectionTitle>
                                {t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.contactAssociateWith'
                                )}
                            </SectionTitle>

                            {data?.linkedVehicles && data.linkedVehicles.length > 0 && (
                                <LinkedVehiclesContainer>
                                    {data.linkedVehicles.map((vehicle) => (
                                        <VehicleCard key={vehicle.vin}>
                                            <VehicleInfo>
                                                {`${vehicle.brand} ${vehicle.model} ${vehicle.year}, ${vehicle.registrationPlate}`}
                                                <br />
                                                {vehicle.vin}
                                            </VehicleInfo>
                                            {vehicle.isOwner && (
                                                <OwnerChip>
                                                    {t('afterSalesCrm.vehicleDetails.owner')}
                                                </OwnerChip>
                                            )}
                                        </VehicleCard>
                                    ))}
                                </LinkedVehiclesContainer>
                            )}
                        </FormSection>

                        <FormSection>
                            <SectionTitle>
                                {t('afterSalesCrm.vehicleDetails.billingInformation')}
                            </SectionTitle>

                            <Grid container spacing={1}>
                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.businessName'
                                        )}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.businessName'
                                        )}
                                        value={formData.businessName}
                                        onChange={handleChange('businessName')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.taxId')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.taxId'
                                        )}
                                        value={formData.taxId}
                                        onChange={handleChange('taxId')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.street')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.street'
                                        )}
                                        value={formData.street}
                                        onChange={handleChange('street')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.neighborhood'
                                        )}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.neighborhood'
                                        )}
                                        value={formData.neighborhood}
                                        onChange={handleChange('neighborhood')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.municipality'
                                        )}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.municipality'
                                        )}
                                        value={formData.municipality}
                                        onChange={handleChange('municipality')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.zipCode')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.zipCode'
                                        )}
                                        value={formData.zipCode}
                                        onChange={handleChange('zipCode')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.city')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.city'
                                        )}
                                        value={formData.city}
                                        onChange={handleChange('city')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.state')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.state'
                                        )}
                                        value={formData.state}
                                        onChange={handleChange('state')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FieldLabel>
                                        {t('afterSalesCrm.vehicleDetails.contactDetails.taxEmail')}
                                    </FieldLabel>
                                    <TextFieldStyled
                                        fullWidth
                                        placeholder={t(
                                            'afterSalesCrm.vehicleDetails.contactDetails.placeholder.taxEmail'
                                        )}
                                        value={formData.taxEmail}
                                        onChange={handleChange('taxEmail')}
                                        size="small"
                                        inputProps={{
                                            maxLength: 255,
                                        }}
                                    />
                                </Grid>
                            </Grid>
                        </FormSection>
                        <AddButtonContainer>
                            <SubmitButtonContainer>
                                <Button
                                    onClick={onSubmit}
                                    variant="contained"
                                    fullWidth
                                    disabled={!isFormValid}
                                    sx={{
                                        backgroundColor: 'var(--cm1)',
                                        borderRadius: '51px',
                                        width: '80%',
                                        textTransform: 'none',
                                        '&.Mui-disabled': {
                                            backgroundColor: 'var(--cm1)',
                                            opacity: 0.5,
                                            color: 'white',
                                            cursor: 'not-allowed',
                                        },
                                    }}
                                >
                                    {updateContactMutation.isLoading ? (
                                        <CircularProgress size={24} color="inherit" />
                                    ) : (
                                        t('afterSalesCrm.vehicleDetails.contactDetails.saveChanges')
                                    )}
                                </Button>
                            </SubmitButtonContainer>
                        </AddButtonContainer>
                    </>
                )}
            </Content>
        </Container>
    );
}

const LoadingContainer = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '50%',
    width: '100%',
});

const Container = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: 'white',
});

const AddButtonContainer = styled('div')({
    position: 'absolute',
    bottom: 0,
    left: 0,
    backgroundColor: 'var(--neutral3)',
    width: '95%',
    display: 'flex',
    justifyContent: 'center',
});

const Header = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '16px',
    backgroundColor: 'var(--neutral8)',
    color: 'white',
});

const MobileTextFieldContainer = styled('div')({
    '& .MuiOutlinedInput-root': {
        height: '30px',
        backgroundColor: 'var(--neutral2)!important',
        '&:hover': {
            backgroundColor: 'var(--neutral2)!important',
        },
    },
    '& input::placeholder': {
        color: 'gray',
        opacity: 1,
    },
    '& .Mui-focused': {
        backgroundColor: 'var(--neutral2)!important',
    },
    '& .MuiInputBase-root': {
        backgroundColor: 'var(--neutral2)!important',
    },
    '& .MuiOutlinedInput-root.Mui-focused': {
        backgroundColor: 'var(--neutral2) !important',
    },
});

const Content = styled('div')({
    flex: 1,
    padding: '16px',
    paddingRight: '6px',
    paddingBottom: '60px',
    overflowY: 'auto',
    scrollbarGutter: 'stable',
});

const FormSection = styled('div')({
    padding: '0 16px 16px 16px',
});

const SectionTitle = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 700,
    marginBottom: '3px',
    color: 'var(--neutral8)',
}));

const SelectYesNoStyled = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    color: 'var(--neutral7)',
    opacity: 0.6,
}));

const FieldLabel = styled(Typography)(({ theme }) => ({
    ...theme.typography.h9Inter,
    fontWeight: 400,
    marginBottom: '4px',
    color: 'var(--neutral8)',
}));

const SubmitButtonContainer = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    width: '90%',
    marginTop: '12px',
    marginBottom: '12px',
});

const TextFieldStyled = styled(TextField)({
    paddingTop: '0!important',
    '& .MuiOutlinedInput-root': {
        height: '30px',
        backgroundColor: 'var(--neutral2)',

        '&:hover fieldset': {
            borderColor: 'var(--cm2)',
        },
    },
});

const LinkedVehiclesContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    marginTop: '8px',
});

const VehicleCard = styled('div')({
    display: 'flex',
    position: 'relative',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '12px 16px',
    borderTop: '1px solid #C9CDD380',
    borderLeft: '1px solid #C9CDD380',
    borderRight: '1px solid #C9CDD380',

    '&:first-child': {
        borderTop: '1px solid #C9CDD380',
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
    },
    '&:last-child': {
        borderBottom: '1px solid #C9CDD380',
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
    },
});

const VehicleInfo = styled(Typography)(({ theme }) => ({
    ...theme.typography.h8Inter,
    color: 'var(--neutral8)',
    lineHeight: '1.4',
}));

const OwnerChip = styled('div')({
    backgroundColor: 'var(--neutral8)',
    position: 'absolute',
    top: 0,
    right: 0,
    color: 'var(--neutral2)',
    padding: '2px 3px',
    fontSize: '9px',
    fontWeight: 500,
    borderTopRightRadius: 5,
});
