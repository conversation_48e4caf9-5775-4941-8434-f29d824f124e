import { Box, Button, Modal, Typography, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMutation } from '@tanstack/react-query';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchVehicleThunk, selectVehicleDetails } from 'store/slices/vehicleDetails';
import { useParams } from 'react-router-dom';
import useToasters from 'common/hooks/useToasters';
import CustomersApi from 'api/customers';

import { Close } from '@mui/icons-material';
import { useVehicleDetailsRightColumnContext } from '../VehicleDetailsRightColumnContext';
import { ExclamationInCircleIcon } from 'common/components/Icons/ExclamationInCircleIcon';

type EstablishOwnerModalProps = {
    open: boolean;
};

export default function EstablishOwnerModal({ open }: EstablishOwnerModalProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));
    const { setRightPanelMode, editingContact, setEditingContact } =
        useVehicleDetailsRightColumnContext();

    const toasters = useToasters();

    const establishOwnerMutation = useMutation({
        mutationFn: () => {
            if (!editingContact?.customerId) {
                throw new Error('Missing customerId');
            }
            return CustomersApi.updateVehicleOwner(vehicleId, editingContact?.customerId);
        },
        onSuccess: () => {
            dispatch(fetchVehicleThunk({ id: vehicleId }));
            toasters.success(
                t('afterSalesCrm.vehicleDetails.contactDetails.ownerSuccessMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.ownerSuccessTitle')
            );
            setRightPanelMode(null);
            setEditingContact(null);
        },
        onError: () => {
            toasters.danger(
                t('afterSalesCrm.vehicleDetails.contactDetails.errorMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.errorTitle')
            );
            setRightPanelMode(null);
            setEditingContact(null);
        },
    });

    const handleClose = () => {
        setRightPanelMode(null);
        setEditingContact(null);
    };

    const handleEstablishOwner = () => {
        establishOwnerMutation.mutate();
    };

    const { brand, model, year, plates } = details?.details || {};

    const mainParts = [brand, model, year].filter(Boolean).join(' ');
    const vehicleInfo = [mainParts, plates].filter(Boolean).join(', ');

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="establish-owner-modal"
            aria-describedby="modal-to-establish-vehicle-owner"
        >
            <ModalContainer>
                <CloseButton onClick={handleClose}>
                    <Close />
                </CloseButton>

                <IconContainer>
                    <ExclamationInCircleIcon size={51} />
                </IconContainer>

                <Box sx={{ display: 'flex', justifyContent: 'center', mb: '12px' }}>
                    <Typography variant="h4Roboto" sx={{ fontWeight: 700, color: '#26292B' }}>
                        {t('afterSalesCrm.vehicleDetails.contactDetails.establishAsOwner')}
                    </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Box
                        sx={{
                            textAlign: 'center',
                            borderBottom: '1px solid var(--neutral3)',
                            width: '80%',
                            marginBottom: 2,
                        }}
                    />
                </Box>

                <Typography
                    variant="body1"
                    align="center"
                    sx={{ mb: 3, color: 'var(--grey5)', weight: 300 }}
                >
                    {editingContact?.firstName} {editingContact?.lastName}{' '}
                    {t('afterSalesCrm.vehicleDetails.contactDetails.willBeAssociatedAsOwnerOf')}{' '}
                    {vehicleInfo}.
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <UpdateButton
                        variant="contained"
                        onClick={handleEstablishOwner}
                        disabled={establishOwnerMutation.isLoading}
                        fullWidth
                        sx={{
                            color: '#F6F6F6',
                            fontWeight: '700!important',
                            fontFamily: 'Inter',
                            fontSize: 12,
                        }}
                    >
                        {t('afterSalesCrm.vehicleDetails.contactDetails.update')}
                    </UpdateButton>
                </Box>
            </ModalContainer>
        </Modal>
    );
}

const ModalContainer = styled(Box)(() => ({
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 450,
    backgroundColor: 'white',
    borderRadius: 8,
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
    padding: '50px 38px 38px 45px',
    outline: 'none',
}));

const IconContainer = styled(Box)({
    display: 'flex',
    justifyContent: 'center',
    marginBottom: 16,
});

const UpdateButton = styled(Button)({
    color: 'white',
    width: '150px',
    height: '37px',
    borderRadius: 24,
    padding: '10px 16px',
    textTransform: 'none',
    backgroundColor: 'var(--cm1)',
    fontWeight: 500,

    '&.Mui-disabled': {
        backgroundColor: 'var(--cm1)',
        opacity: 0.5,
        color: 'white',
    },
});

const CloseButton = styled(Box)({
    position: 'absolute',
    top: 12,
    right: 12,
    cursor: 'pointer',
    color: 'var(--neutral6)',
    '&:hover': {
        color: 'var(--neutral8)',
    },
});
