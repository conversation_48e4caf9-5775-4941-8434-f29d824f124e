import { useEffect, useMemo, useRef, useState } from 'react';
import {
    Box,
    Button,
    CircularProgress,
    Grid,
    IconButton,
    styled,
    TextField,
    Typography,
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import CustomersApi, { ContactSearchData, VehicleContactCreateDto } from 'api/customers';
import ContactAutocomplete from './ContactAutocomplete';
import MaskedTextFormField from 'common/components/Inputs/MaskedTextFormField';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { numberPhoneUnformatter, phoneFormatRegexMask } from 'common/FormatersHelper';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useMutation } from '@tanstack/react-query';
import useToasters from 'common/hooks/useToasters';
import { fetchVehicleThunk, selectVehicleDetails } from 'store/slices/vehicleDetails';
import { useParams } from 'react-router-dom';

import { useDebounce } from 'use-debounce';
import { useVehicleDetailsRightColumnContext } from '../VehicleDetailsRightColumnContext';

type ContactFormData = {
    customerId: string | null;
    firstName: string;
    lastName: string;
    phone: string;
    mobile: string;
    email: string;
    businessName: string;
    taxId: string;
    street: string;
    neighborhood: string;
    municipality: string;
    zipCode: string;
    city: string;
    state: string;
    taxEmail: string;
};

export default function AddVehicleContact() {
    const { t } = useAppTranslation();
    const [formData, setFormData] = useState<ContactFormData>({
        customerId: null,
        firstName: '',
        lastName: '',
        phone: '',
        mobile: '',
        email: '',
        businessName: '',
        taxId: '',
        street: '',
        neighborhood: '',
        municipality: '',
        zipCode: '',
        city: '',
        state: '',
        taxEmail: '',
    });
    const [errors, setErrors] = useState<Partial<Record<keyof ContactFormData, boolean>>>({});
    const scrollDivRef = useRef(null);
    const toasters = useToasters();
    const dispatch = useAppDispatch();
    const { setRightPanelMode } = useVehicleDetailsRightColumnContext();

    const { internationalization } = useAppSelector(selectSettings);
    const maxLengthPhone = InternationalizationLogic.maxLengthPhone(internationalization);
    const phoneMask = phoneFormatRegexMask(internationalization.phoneNumberFormat);
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));
    const { mobile, customerId } = formData;
    const [debouncedValues] = useDebounce({ mobile, customerId }, 300);

    const createContactMutation = useMutation({
        mutationFn: (data: VehicleContactCreateDto) => CustomersApi.createVehicleContact(data),
        onSuccess: () => {
            dispatch(fetchVehicleThunk({ id: vehicleId }));
            toasters.success(
                t('afterSalesCrm.vehicleDetails.contactDetails.successMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.successTitle')
            );
            setRightPanelMode(null);
        },
        onError: () => {
            toasters.danger(
                t('afterSalesCrm.vehicleDetails.contactDetails.errorMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.errorTitle')
            );
        },
    });

    // Check if this mobile number already exists in vehicle contacts
    useEffect(() => {
        const checkDuplicate = async () => {
            const sanitizedMobile = debouncedValues.mobile?.replace(/\D/g, '');
            if (
                !sanitizedMobile ||
                sanitizedMobile.length < 2 ||
                debouncedValues.customerId != null
            ) {
                return;
            }

            // Check in current vehicle contacts first
            const contacts = details?.details?.contacts ?? [];
            const existsInContacts = contacts.some(
                (c) => c.mobile?.replace(/\D/g, '') === sanitizedMobile
            );
            if (existsInContacts) {
                toasters.danger(
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorMessageLocalAlreadyExist'),
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorTitleLocalAlreadyExist')
                );
                setErrors((prev) => ({ ...prev, mobile: true }));
                return;
            }

            // Check number among all customers
            const userName = await CustomersApi.checkDuplicateMobile(sanitizedMobile);

            if (userName) {
                toasters.danger(
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorMessageAlreadyExist', {
                        name: userName,
                    }),
                    t('afterSalesCrm.vehicleDetails.contactDetails.errorTitleAlreadyExist')
                );
                setErrors((prev) => ({ ...prev, mobile: true }));
            } else {
                setErrors((prev) => ({ ...prev, mobile: false }));
            }
        };

        void checkDuplicate();
    }, [
        debouncedValues.mobile,
        debouncedValues.customerId,
        toasters,
        t,
        details?.details?.contacts,
    ]);

    const handleChange =
        (field: keyof ContactFormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            setFormData({
                ...formData,
                [field]: e.target.value,
            });

            // Clear error when field is edited
            if (errors[field]) {
                setErrors({
                    ...errors,
                    [field]: false,
                });
            }
        };

    const validateForm = (): boolean => {
        const newErrors: Partial<Record<keyof ContactFormData, boolean>> = {};

        if (!formData.firstName.trim()) newErrors.firstName = true;
        if (!formData.mobile) newErrors.mobile = true;

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const onSubmit = async () => {
        if (!validateForm()) return;

        try {
            const contactData: VehicleContactCreateDto = {
                vehicleId: vehicleId,
                customerId: formData.customerId,
                firstName: formData.firstName,
                lastName: formData.lastName,
                landline: formData.phone,
                mobile: numberPhoneUnformatter(formData.mobile),
                email: formData.email,
                businessName: formData.businessName,
                taxIdentification: formData.taxId,
                street: formData.street,
                neighborhood: formData.neighborhood,
                district: formData.municipality,
                zipCode: formData.zipCode,
                city: formData.city,
                state: formData.state,
                taxEmail: formData.taxEmail,
            };

            createContactMutation.mutate(contactData);
        } catch (error) {
            console.error('Error preparing contact data:', error);
        }
    };

    const handleContactSelect = (contact: ContactSearchData) => {
        setFormData({
            customerId: contact.customerId,
            firstName: contact.firstName || '',
            lastName: contact.lastName || '',
            phone: contact.landline || '',
            mobile: contact.mobile || '',
            email: contact.email || '',
            businessName: contact.businessName || '',
            taxId: contact.taxIdentification || '',
            street: contact.street || '',
            neighborhood: contact.neighborhood || '',
            municipality: contact.district || '',
            zipCode: contact.zipCode || '',
            city: contact.city || '',
            state: contact.state || '',
            taxEmail: contact.taxEmail || '',
        });

        setErrors({});
    };

    const handleClearAutocomplete = () => {
        setFormData({
            customerId: null,
            firstName: '',
            lastName: '',
            phone: '',
            mobile: '',
            email: '',
            businessName: '',
            taxId: '',
            street: '',
            neighborhood: '',
            municipality: '',
            zipCode: '',
            city: '',
            state: '',
            taxEmail: '',
        });
    };

    // Check if required fields are filled
    const isFormValid = useMemo(() => {
        return (
            formData.firstName.trim() !== '' &&
            formData.mobile.trim() !== '' &&
            !errors.mobile &&
            !createContactMutation.isLoading
        );
    }, [formData.firstName, formData.mobile, errors.mobile, createContactMutation.isLoading]);

    return (
        <Container>
            <Header>
                <Typography variant="h6">
                    {t('afterSalesCrm.vehicleDetails.contactDetails.addContact')}
                </Typography>
                <IconButton
                    onClick={() => {
                        setRightPanelMode(null);
                    }}
                    size="small"
                >
                    <Close sx={{ color: 'white' }} />
                </IconButton>
            </Header>

            <Content ref={scrollDivRef}>
                <SearchBox>
                    <ContactAutocomplete
                        onContactSelect={handleContactSelect}
                        scrollDivRef={scrollDivRef}
                        onClear={handleClearAutocomplete}
                    />
                </SearchBox>

                <FormSection>
                    <SectionTitle>
                        {t('afterSalesCrm.vehicleDetails.contactDetails.generalInfo')}
                    </SectionTitle>

                    <Grid container spacing={1}>
                        <Grid item xs={12}>
                            <FieldLabel sx={{ marginBottom: '-4px' }}>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.name')}{' '}
                                <Box component="span" sx={{ color: 'blue' }}>
                                    *
                                </Box>
                            </FieldLabel>
                        </Grid>
                        <Grid item xs={6}>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.name'
                                )}
                                value={formData.firstName}
                                onChange={handleChange('firstName')}
                                error={!!errors.firstName}
                                size="small"
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                inputProps={{
                                    maxLength: 250,
                                }}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.lastName'
                                )}
                                value={formData.lastName}
                                onChange={handleChange('lastName')}
                                error={!!errors.lastName}
                                size="small"
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                inputProps={{
                                    maxLength: 250,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.phone')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.phone'
                                )}
                                value={formData.phone}
                                onChange={handleChange('phone')}
                                size="small"
                                editable={formData.customerId != null}
                                inputProps={{
                                    maxLength: 250,
                                }}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.mobile')}{' '}
                                <Box component="span" sx={{ color: 'blue' }}>
                                    *
                                </Box>
                            </FieldLabel>
                            <MobileTextFieldContainer>
                                <MaskedTextFormField
                                    fullWidth
                                    placeholder={t(
                                        'afterSalesCrm.vehicleDetails.contactDetails.placeholder.mobile'
                                    )}
                                    value={formData.mobile}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        if (value !== formData.mobile) {
                                            setFormData({
                                                ...formData,
                                                mobile: value,
                                            });

                                            if (errors.mobile) {
                                                setErrors({
                                                    ...errors,
                                                    mobile: false,
                                                });
                                            }
                                        }
                                    }}
                                    isInvalid={!!errors.mobile}
                                    maxLength={maxLengthPhone}
                                    disabled={
                                        createContactMutation.isLoading ||
                                        formData.customerId != null
                                    }
                                    mask={phoneMask}
                                />
                            </MobileTextFieldContainer>
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.email')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.email'
                                )}
                                value={formData.email}
                                onChange={handleChange('email')}
                                size="small"
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                inputProps={{
                                    maxLength: 254,
                                }}
                            />
                        </Grid>
                    </Grid>
                </FormSection>

                <FormSection>
                    <SectionTitle>
                        {t('afterSalesCrm.vehicleDetails.contactDetails.associateContactWith')}
                    </SectionTitle>

                    <Grid container spacing={1}>
                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.vehicle')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                value={
                                    details?.details.brand +
                                    ' ' +
                                    details?.details.model +
                                    ' ' +
                                    details?.details.year +
                                    ', ' +
                                    details?.details.plates +
                                    '/' +
                                    details?.details.vin
                                }
                                editable
                                disabled
                                size="small"
                            />
                        </Grid>
                    </Grid>
                </FormSection>

                <FormSection>
                    <SectionTitle>
                        {t('afterSalesCrm.vehicleDetails.billingInformation')}
                    </SectionTitle>

                    <Grid container spacing={1}>
                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.businessName')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.businessName'
                                )}
                                value={formData.businessName}
                                onChange={handleChange('businessName')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.taxId')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.taxId'
                                )}
                                value={formData.taxId}
                                onChange={handleChange('taxId')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.street')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.street'
                                )}
                                value={formData.street}
                                onChange={handleChange('street')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.neighborhood')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.neighborhood'
                                )}
                                value={formData.neighborhood}
                                onChange={handleChange('neighborhood')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.municipality')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.municipality'
                                )}
                                value={formData.municipality}
                                onChange={handleChange('municipality')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.zipCode')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.zipCode'
                                )}
                                value={formData.zipCode}
                                onChange={handleChange('zipCode')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.city')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.city'
                                )}
                                value={formData.city}
                                onChange={handleChange('city')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.state')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.state'
                                )}
                                value={formData.state}
                                onChange={handleChange('state')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <FieldLabel>
                                {t('afterSalesCrm.vehicleDetails.contactDetails.taxEmail')}
                            </FieldLabel>
                            <TextFieldStyled
                                fullWidth
                                placeholder={t(
                                    'afterSalesCrm.vehicleDetails.contactDetails.placeholder.taxEmail'
                                )}
                                value={formData.taxEmail}
                                onChange={handleChange('taxEmail')}
                                editable={formData.customerId != null}
                                disabled={
                                    createContactMutation.isLoading || formData.customerId != null
                                }
                                size="small"
                                inputProps={{
                                    maxLength: 255,
                                }}
                            />
                        </Grid>
                    </Grid>
                </FormSection>
                <AddButtonContainer>
                    <SubmitButtonContainer>
                        <SubmitButton
                            onClick={onSubmit}
                            variant="contained"
                            fullWidth
                            disabled={!isFormValid}
                        >
                            {createContactMutation.isLoading ? (
                                <CircularProgress size={24} color="inherit" />
                            ) : (
                                t('afterSalesCrm.vehicleDetails.contactDetails.addContact')
                            )}
                        </SubmitButton>
                    </SubmitButtonContainer>
                </AddButtonContainer>
            </Content>
        </Container>
    );
}

const Container = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: 'white',
});

const AddButtonContainer = styled('div')({
    position: 'absolute',
    bottom: 0,
    left: 0,
    backgroundColor: 'var(--neutral3)',
    width: '95%',
    display: 'flex',
    justifyContent: 'center',
});

const Header = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '16px',
    backgroundColor: 'var(--neutral8)',
    color: 'white',
});

const MobileTextFieldContainer = styled('div')({
    '& .MuiOutlinedInput-root': {
        height: '30px',
        backgroundColor: 'var(--neutral2)!important',
        '&:hover': {
            backgroundColor: 'var(--neutral2)!important',
        },
    },
    '& input::placeholder': {
        color: 'gray',
        opacity: 1,
    },
    '& .Mui-focused': {
        backgroundColor: 'var(--neutral2)!important',
    },
    '& .MuiInputBase-root': {
        backgroundColor: 'var(--neutral2)!important',
    },
    '& .MuiOutlinedInput-root.Mui-focused': {
        backgroundColor: 'var(--neutral2) !important',
    },
});

const Content = styled('div')({
    flex: 1,
    padding: '16px',
    paddingRight: '6px',
    paddingBottom: '60px',
    overflowY: 'auto',
    scrollbarGutter: 'stable',
});

const SearchBox = styled('div')({
    marginBottom: '15px',
    padding: '0 16px',
});

const FormSection = styled('div')({
    padding: '0 16px 16px 16px',
});

const SectionTitle = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 700,
    marginBottom: '3px',
    color: 'var(--neutral8)',
}));

const FieldLabel = styled(Typography)(({ theme }) => ({
    ...theme.typography.h9Inter,
    fontWeight: 400,
    marginBottom: '4px',
    color: 'var(--neutral8)',
}));

const SubmitButtonContainer = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    width: '90%',
    marginTop: '12px',
    marginBottom: '12px',
});

const TextFieldStyled = styled(TextField, {
    shouldForwardProp: (prop) => prop !== 'editable',
})<{ editable?: boolean }>(({ editable }) => ({
    paddingTop: '0!important',
    '& .MuiOutlinedInput-root': {
        height: '30px',
        backgroundColor: 'var(--neutral2)',

        '&:hover fieldset': {
            borderColor: editable ? 'none' : 'var(--cm2)',
        },
    },
}));

const SubmitButton = styled(Button)(() => ({
    backgroundColor: 'var(--cm1)',
    borderRadius: '51px',
    width: '80%',
    textTransform: 'none',
    '&.Mui-disabled': {
        backgroundColor: 'var(--cm1)',
        opacity: 0.5,
        color: 'white',
        cursor: 'not-allowed',
    },
}));
