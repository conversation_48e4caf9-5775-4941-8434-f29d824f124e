import {
    Autocomplete,
    AutocompleteInputChangeReason,
    Box,
    Button,
    CircularProgress,
    InputAdornment,
    ListItem,
    Paper,
    Popper,
    PopperProps,
    styled,
    Typography,
} from '@mui/material';
import { PopperChildrenProps } from '@mui/material/Popper/BasePopper.types';
import { AutocompleteChangeReason } from '@mui/material/useAutocomplete/useAutocomplete';
import { useQuery } from '@tanstack/react-query';
import { TextField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Interweave } from 'interweave';
import React, { forwardRef, SyntheticEvent, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { useDebounce } from 'use-debounce';
import CustomersApi, { ContactSearchData } from 'api/customers';
import { ExpandMore } from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectVehicleDetails } from 'store/slices/vehicleDetails';

type ContactAutocompleteProps = {
    onContactSelect: (contact: ContactSearchData) => void;
    onClear: () => void;
    scrollDivRef?: React.RefObject<HTMLDivElement>;
};

type ContactSearchDataWithAssociationStatus = ContactSearchData & {
    isAlreadyAssociated: boolean;
};

export default function ContactAutocomplete({
    onContactSelect,
    onClear,
    scrollDivRef,
}: ContactAutocompleteProps) {
    const { t } = useAppTranslation();
    const [open, setOpen] = useState(false);
    const [search, setSearch] = useState('');
    const [debouncedSearch] = useDebounce(search, 300);
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));

    const queryKey = ['contacts', 'search', debouncedSearch];
    const queryFn = () => CustomersApi.searchVehicleContact(debouncedSearch);

    const { data, isFetching } = useQuery(queryKey, queryFn, {
        enabled: debouncedSearch !== '' && debouncedSearch !== undefined,
        keepPreviousData: false,
    });

    const options = useMemo(() => {
        if (!data) return [];

        return data.map((contact) => {
            const isAlreadyAssociated =
                details?.details?.contacts?.some(
                    (existingContact) => existingContact.customerId === contact.customerId
                ) || false;

            return {
                ...contact,
                isAlreadyAssociated,
            } as ContactSearchDataWithAssociationStatus;
        });
    }, [data, details]);

    const setParentOverflow = (hidden: boolean) => {
        if (scrollDivRef?.current) {
            scrollDivRef.current.style.overflow = hidden ? 'hidden' : 'auto';
        }
    };

    const handleInputChange = (
        e: SyntheticEvent<Element, Event>,
        newInputValue: string,
        reason: AutocompleteInputChangeReason
    ) => {
        if (reason === 'reset') return;

        if (reason === 'clear') {
            e.stopPropagation();
            setSearch('');
            onClear();
        }

        setSearch(newInputValue);
        const shouldOpen = newInputValue.length > 0;
        setOpen(shouldOpen);
        setParentOverflow(shouldOpen);
    };

    const handleOnFocus = () => {
        const shouldOpen = !!search && search.length > 0;
        setOpen(shouldOpen);
        setParentOverflow(shouldOpen);
    };

    const handleValueChange = (
        _e: SyntheticEvent<Element, Event>,
        value: ContactSearchDataWithAssociationStatus | null,
        reason: AutocompleteChangeReason
    ) => {
        if (reason === 'clear') {
            return;
        }

        if (value && !value.isAlreadyAssociated) {
            const { isAlreadyAssociated, ...originalContact } = value;
            onContactSelect(originalContact);
            setSearch(`${value.firstName} ${value.lastName} / ${value.mobile}`);
        }

        setOpen(false);
        setParentOverflow(false);
    };

    return (
        <Autocomplete
            noOptionsText={
                isFetching ? (
                    <CircularProgress size={24} color="inherit" />
                ) : (
                    <NoResultsContainer>
                        <Typography
                            variant="h6Roboto"
                            sx={{ fontWeight: 'bold', color: 'var(--neutral8)' }}
                        >
                            {t('afterSalesCrm.vehicleDetails.contactDetails.noContactsFound')}
                        </Typography>
                        <Typography variant="h8Roboto" sx={{ color: 'var(--neutral8)' }}>
                            {t('afterSalesCrm.vehicleDetails.contactDetails.tryAgainOrFillFields')}
                        </Typography>
                    </NoResultsContainer>
                )
            }
            disablePortal
            sx={{
                width: '100%',
            }}
            clearOnBlur={false}
            options={options}
            inputValue={search}
            clearIcon={
                <ClearButton>{t('afterSalesCrm.vehicleDetails.contactDetails.clear')}</ClearButton>
            }
            clearText={''}
            renderInput={(params) => (
                <StyledTextField
                    {...params}
                    search={search}
                    placeholder={t(
                        'afterSalesCrm.vehicleDetails.contactDetails.searchContactPlaceholder'
                    )}
                    showValidationIndicators={false}
                    isRequired={false}
                    cmosVariant="roundedGrey"
                    enableEnterComplete={true}
                    InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <>
                                {params.InputProps.endAdornment}
                                <InputAdornment position="end">
                                    <ExpandMore fill={'var(--neutral6)'} />
                                </InputAdornment>{' '}
                            </>
                        ),
                    }}
                />
            )}
            onInputChange={handleInputChange}
            onChange={handleValueChange}
            open={open}
            onFocus={handleOnFocus}
            onBlur={() => {
                setOpen(false);
                setParentOverflow(false);
            }}
            forcePopupIcon={false}
            getOptionLabel={(option) => `${option.firstName} ${option.lastName} / ${option.mobile}`}
            renderOption={(props, contact) => {
                if (contact.isAlreadyAssociated) {
                    return (
                        <ListItem
                            {...props}
                            sx={{
                                opacity: 0.6,
                                cursor: 'not-allowed',
                                pointerEvents: 'none',
                                backgroundColor: 'var(--neutral2)',
                            }}
                        >
                            <ContactItem contact={contact} inputValue={search} />
                        </ListItem>
                    );
                }

                return (
                    <ListItem {...props}>
                        <ContactItem contact={contact} inputValue={search} />
                    </ListItem>
                );
            }}
            PopperComponent={PopperComponent}
            PaperComponent={PaperComponent}
            filterOptions={(options) => options}
            isOptionEqualToValue={(option, value) => option.customerId === value.customerId}
            getOptionDisabled={(option) => option.isAlreadyAssociated === true}
        />
    );
}

type ContactItemProps = {
    contact: ContactSearchDataWithAssociationStatus;
    inputValue: string | undefined;
};

const Title = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[8],
}));

const AlreadyAssociatedText = styled(Typography)(({ theme }) => ({
    ...theme.typography.h11Roboto,
    fontWeight: 'bold',
    color: theme.palette.neutral[8],
    lineHeight: '100%',
    letterSpacing: '0%',
}));

function selectionText(defaultValue: string, inputValue: string): string {
    const normalizedInputValue = inputValue.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    const regexp = new RegExp(normalizedInputValue, 'i');

    const startIndex = defaultValue.search(regexp);
    if (startIndex === -1) {
        return defaultValue;
    }
    const lastIndex = startIndex + inputValue.length;

    return (
        defaultValue.slice(0, startIndex) +
        '<b>' +
        defaultValue.slice(startIndex, lastIndex) +
        '</b>' +
        defaultValue.slice(lastIndex)
    );
}

function ContactItem({ contact, inputValue }: ContactItemProps) {
    const { t } = useAppTranslation();

    const fullName = `${contact.firstName} ${contact.lastName}`;
    const selectedTextName = selectionText(fullName, inputValue ?? '');
    const selectedTextMobile = contact.mobile
        ? selectionText(contact.mobile, inputValue ?? '')
        : '-';

    const selectedHtmlTextName = <Interweave content={selectedTextName} />;
    const selectedHtmlTextMobile = <Interweave content={selectedTextMobile} />;

    return (
        <Box sx={{ display: 'flex', width: '100%', flexDirection: 'column' }}>
            <Box
                sx={{
                    display: 'flex',
                    width: '100%',
                    justifyContent: 'space-between',
                }}
            >
                <Box sx={{ display: 'flex', width: '100%', flexBasis: '100%' }}>
                    <Box
                        padding={'10px 5px'}
                        sx={{
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            maxWidth: '90%',
                        }}
                    >
                        <Title>
                            {selectedHtmlTextName}
                            {' / '}
                            {selectedHtmlTextMobile}
                        </Title>
                    </Box>
                </Box>
            </Box>

            {contact.isAlreadyAssociated && (
                <AlreadyAssociatedText>
                    {t('afterSalesCrm.vehicleDetails.contactDetails.contactAlreadyAssociated')}
                </AlreadyAssociatedText>
            )}
        </Box>
    );
}

const PopperComponent = forwardRef<HTMLDivElement, PopperProps>(({ children, ...props }, ref) => {
    return createPortal(
        <Popper
            {...props}
            ref={ref}
            placement="bottom-start"
            modifiers={[
                {
                    name: 'offset',
                    options: {
                        offset: [0, 5],
                    },
                },
            ]}
        >
            {typeof children === 'function' ? children(props as PopperChildrenProps) : children}
        </Popper>,
        document.body
    );
});

const StyledPaper = styled(Paper)(() => ({
    width: '100%',
    borderRadius: '5px',
    border: '1px solid var(--cm1)',
}));

const PaperComponent = forwardRef(
    ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>, _ref) => {
        return <StyledPaper {...props}>{children}</StyledPaper>;
    }
);

const StyledTextField = styled(TextField, {
    shouldForwardProp: (prop) => prop !== 'search',
})<{ search: string }>(({ theme, search }) => ({
    '& .MuiAutocomplete-clearIndicator': {
        marginRight: '20px',
        visibility: 'visible',
    },
    '& .MuiOutlinedInput-root .MuiAutocomplete-input': {
        padding: '0 0 0 5px',
        paddingRight: search === '' ? '10px' : '40px',
        '&::placeholder': {
            ...theme.typography.h6Inter,
            fontWeight: 'normal',
            color: 'var(--neutral6)',
            opacity: 1,
        },
    },
    '& .MuiOutlinedInput-root': {
        borderRadius: '5px',
        paddingRight: '8px!important',
        backgroundColor: '#fafafa!important',
    },
    width: '100%',
}));

const NoResultsContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    borderRadius: '8px',
    margin: '10px',
    backgroundColor: 'var(--neutral1)',
});

const ClearButton = styled(Button)(() => ({
    fontFamily: 'Inter',
    fontSize: '10px',
    textTransform: 'none',
    textDecoration: 'underline',
    minWidth: 'auto',
    padding: 0,
    '&:hover': {
        backgroundColor: 'transparent',
        textDecoration: 'underline',
    },
}));
