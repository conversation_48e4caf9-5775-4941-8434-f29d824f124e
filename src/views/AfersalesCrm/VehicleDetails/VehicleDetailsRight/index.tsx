import { Box, Modal, styled } from '@mui/material';
import VehicleContactDetails from './VehicleContactDetails';
import AddVehicleContact from './AddVehicleContact';
import EditVehicleContact from './EditVehicleContact';
import { useVehicleDetailsRightColumnContext } from './VehicleDetailsRightColumnContext';
import RemoveContactVehicleAssociationModal from './RemoveContactVehicleAssociationModal';
import EstablishOwnerModal from './EstablishOwnerModal';

export default function VehicleDetailsRight() {
    const { rightPanelMode, setRightPanelMode, setEditingContact } =
        useVehicleDetailsRightColumnContext();

    const handleCancelAddContact = () => {
        setRightPanelMode(null);
    };

    const handleCancelEditContact = () => {
        setEditingContact(null);
        setRightPanelMode(null);
    };

    return (
        <Container>
            <VehicleContactDetails />

            <Modal
                open={rightPanelMode === 'addContact'}
                onClose={handleCancelAddContact}
                aria-labelledby="add-contact-modal"
                aria-describedby="modal-to-add-new-contact"
            >
                <ModalContent>
                    <AddVehicleContact />
                </ModalContent>
            </Modal>

            <Modal
                open={rightPanelMode === 'editContact'}
                onClose={handleCancelEditContact}
                aria-labelledby="edit-contact-modal"
                aria-describedby="modal-to-edit-contact"
            >
                <ModalContent>
                    <EditVehicleContact />
                </ModalContent>
            </Modal>

            <EstablishOwnerModal open={rightPanelMode === 'establishOwner'} />
            <RemoveContactVehicleAssociationModal
                open={rightPanelMode === 'removeContactAssociation'}
            />
        </Container>
    );
}

const Container = styled(Box)({
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
});

const ModalContent = styled(Box)({
    position: 'absolute',
    right: 0,
    top: 0,
    width: '350px',
    height: '100vh',
    outline: 'none',
    boxShadow: '-4px 0 10px rgba(0, 0, 0, 0.1)',
});
