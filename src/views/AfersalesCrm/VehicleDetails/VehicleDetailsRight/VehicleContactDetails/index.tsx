import { <PERSON>, <PERSON><PERSON>, Card, CardContent, IconButton, styled, Typography } from '@mui/material';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import { PhoneInCircle } from 'common/components/Icons/PhoneInCircle';
import { PlusInCircleIcon } from 'common/components/Icons/PlusInCircleIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import { selectVehicleDetails } from 'store/slices/vehicleDetails';
import { useVehicleDetailsRightColumnContext } from '../VehicleDetailsRightColumnContext';
import EmptyContactList from './emptyContactList.png';
import VehicleContactDetailsCollapsibleSection from './VehicleContactDetailsCollapsibleSection';

export default function VehicleContactDetails() {
    const { t } = useAppTranslation();
    const userPermission = useSelector(selectUserPermission);
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { details, expandedContactSections } = useAppSelector((r) =>
        selectVehicleDetails(r, vehicleId)
    );

    const copyEmailText = t('afterSalesCrm.vehicleDetails.copyEmail');
    const emailCopiedText = t('afterSalesCrm.vehicleDetails.emailCopied');
    const { setRightPanelMode } = useVehicleDetailsRightColumnContext();
    const [emailTooltip, setEmailTooltip] = useState<string | JSX.Element>(copyEmailText);

    const ownerId = details?.details.ownerId;

    const sortedContacts = useMemo(() => {
        if (!details) return [];

        const list = details.details.contacts || [];

        const index = list.findIndex((x) => x.customerId === details.details.ownerId);

        if (index === -1 || index === 0) return list;

        // move owner to first position
        return [list[index]]
            .concat(list.slice(0, index))
            .concat(list.slice(index + 1, list.length));
    }, [details]);

    const handleCopyEmail = (email: string) => {
        navigator.clipboard.writeText(email);
        setEmailTooltip(<span style={{ fontWeight: 'bold' }}>{emailCopiedText}</span>);
        setTimeout(() => {
            setEmailTooltip(copyEmailText);
        }, 1000);
    };

    const handleCallContact = (mobile: string) => {};

    const handleAddContact = () => {
        setRightPanelMode('addContact');
    };

    return (
        <Container>
            <SectionHeader>
                <Typography variant="h5" sx={{ fontWeight: 700, color: 'var(--neutral8)' }}>
                    {t('afterSalesCrm.vehicleDetails.contacts')}
                </Typography>
                <ArrowTooltip
                    position="right"
                    content={t('afterSalesCrm.vehicleDetails.contactDetails.addContact')}
                >
                    <AddButton
                        size="small"
                        disabled={!userPermission.allowEditCustomers}
                        onClick={handleAddContact}
                    >
                        <PlusInCircleIcon
                            fill={userPermission.allowEditCustomers ? '#4A4D51' : '#a2a3a5'}
                        />
                    </AddButton>
                </ArrowTooltip>
            </SectionHeader>

            {sortedContacts.length === 0 ? (
                <NoContactsMessage>
                    <img
                        src={EmptyContactList}
                        alt="No contacts illustration"
                        style={{ width: '350px' }}
                    />
                    <Typography
                        variant="h6"
                        align="center"
                        sx={{ fontWeight: 700, color: 'var(--cm1)' }}
                    >
                        {t('afterSalesCrm.vehicleDetails.createOrAssociateContact')}
                    </Typography>
                    <Typography variant="h8Inter" align="center" sx={{ color: 'var(--neutral6)' }}>
                        {t('afterSalesCrm.vehicleDetails.noAssociatedContacts')}
                    </Typography>
                </NoContactsMessage>
            ) : (
                <ContactsContainer>
                    {sortedContacts.map((contact) => {
                        const isExpanded = expandedContactSections?.[contact.customerId] ?? true;

                        return (
                            <ContactCard key={contact.customerId}>
                                <VehicleContactDetailsCollapsibleSection
                                    contact={contact}
                                    initialOpen={isExpanded}
                                >
                                    <CardContent>
                                        <ArrowTooltip
                                            position="top"
                                            content={contact.firstName + ' ' + contact.lastName}
                                        >
                                            <Typography
                                                variant="h6"
                                                noWrap
                                                sx={{
                                                    color: 'var(--neutral8)',
                                                    width: 'fit-content',
                                                    textOverflow: 'ellipsis',
                                                    overflow: 'hidden',
                                                }}
                                            >
                                                {contact.firstName} {contact.lastName}
                                            </Typography>
                                        </ArrowTooltip>
                                        {contact.mobile && (
                                            <Box sx={{ marginBottom: '-4px' }}>
                                                <Typography
                                                    variant="h8Inter"
                                                    sx={{ color: 'var(--neutral8)' }}
                                                >
                                                    {contact.mobile}
                                                </Typography>
                                                <ArrowTooltip
                                                    position="right"
                                                    content={t(
                                                        'afterSalesCrm.vehicleDetails.callContact'
                                                    )}
                                                >
                                                    <IconButton
                                                        size="small"
                                                        onClick={() =>
                                                            handleCallContact(contact.mobile!)
                                                        }
                                                    >
                                                        <PhoneInCircle />
                                                    </IconButton>
                                                </ArrowTooltip>
                                            </Box>
                                        )}

                                        {contact.email && (
                                            <Box sx={{ marginBottom: '-2px' }}>
                                                <Typography
                                                    variant="h8Inter"
                                                    sx={{ color: 'var(--neutral8)' }}
                                                >
                                                    {contact.email}
                                                </Typography>
                                                <ArrowTooltip
                                                    position="right"
                                                    content={emailTooltip}
                                                >
                                                    <IconButton
                                                        sx={{ padding: '2px!important' }}
                                                        size="small"
                                                        onClick={() =>
                                                            handleCopyEmail(contact.email!)
                                                        }
                                                    >
                                                        <CopyIcon size={22} />
                                                    </IconButton>
                                                </ArrowTooltip>
                                            </Box>
                                        )}

                                        {contact.customerId === ownerId && (
                                            <ContactField>
                                                <Typography
                                                    variant="h8Inter"
                                                    sx={{ color: 'var(--neutral8)' }}
                                                >
                                                    {t('afterSalesCrm.vehicleDetails.firstOwner')}:{' '}
                                                    {contact.firstOwner != null
                                                        ? t(
                                                              `afterSalesCrm.values.${
                                                                  contact.firstOwner ? 'Yes' : 'No'
                                                              }`
                                                          )
                                                        : '-'}
                                                </Typography>
                                            </ContactField>
                                        )}
                                        {(contact.taxIdentification ||
                                            contact.businessName ||
                                            contact.district ||
                                            contact.street ||
                                            contact.neighborhood ||
                                            contact.city ||
                                            contact.state ||
                                            contact.zipCode) && (
                                            <BillingSection>
                                                <Box sx={{ marginBottom: '2px' }}>
                                                    <Typography
                                                        variant="h6"
                                                        sx={{ color: 'var(--neutral8)' }}
                                                    >
                                                        {t(
                                                            'afterSalesCrm.vehicleDetails.billingInformation'
                                                        )}
                                                    </Typography>
                                                </Box>

                                                {contact.taxIdentification && (
                                                    <ContactField>
                                                        <Typography
                                                            variant="h8Inter"
                                                            sx={{ color: 'var(--neutral8)' }}
                                                        >
                                                            {contact.taxIdentification}
                                                        </Typography>
                                                    </ContactField>
                                                )}

                                                {contact.businessName && (
                                                    <ContactField>
                                                        <Typography
                                                            variant="h8Inter"
                                                            sx={{ color: 'var(--neutral8)' }}
                                                        >
                                                            {contact.businessName}
                                                        </Typography>
                                                    </ContactField>
                                                )}

                                                {(contact.district ||
                                                    contact.street ||
                                                    contact.neighborhood ||
                                                    contact.city ||
                                                    contact.state ||
                                                    contact.zipCode) && (
                                                    <>
                                                        {contact.street && (
                                                            <ContactField>
                                                                <Typography
                                                                    variant="h8Inter"
                                                                    sx={{
                                                                        color: 'var(--neutral8)',
                                                                    }}
                                                                >
                                                                    {contact.street}{' '}
                                                                </Typography>
                                                            </ContactField>
                                                        )}

                                                        {contact.neighborhood && (
                                                            <ContactField>
                                                                <Typography
                                                                    variant="h8Inter"
                                                                    sx={{
                                                                        color: 'var(--neutral8)',
                                                                    }}
                                                                >
                                                                    {contact.neighborhood}{' '}
                                                                </Typography>
                                                            </ContactField>
                                                        )}

                                                        {contact.district && (
                                                            <ContactField>
                                                                <Typography
                                                                    variant="h8Inter"
                                                                    sx={{
                                                                        color: 'var(--neutral8)',
                                                                    }}
                                                                >
                                                                    {contact.district}{' '}
                                                                </Typography>
                                                            </ContactField>
                                                        )}

                                                        {contact.zipCode && (
                                                            <ContactField>
                                                                <Typography
                                                                    variant="h8Inter"
                                                                    sx={{
                                                                        color: 'var(--neutral8)',
                                                                    }}
                                                                >
                                                                    {contact.zipCode}{' '}
                                                                </Typography>
                                                            </ContactField>
                                                        )}

                                                        {(contact.city || contact.state) && (
                                                            <ContactField>
                                                                <Typography
                                                                    variant="h8Inter"
                                                                    sx={{
                                                                        color: 'var(--neutral8)',
                                                                    }}
                                                                >
                                                                    {contact.city}, {contact.state}{' '}
                                                                </Typography>
                                                            </ContactField>
                                                        )}
                                                    </>
                                                )}
                                            </BillingSection>
                                        )}
                                    </CardContent>
                                </VehicleContactDetailsCollapsibleSection>
                            </ContactCard>
                        );
                    })}
                </ContactsContainer>
            )}
        </Container>
    );
}

const Container = styled('div')({
    height: '100%',
    padding: '16px',
    display: 'flex',
    flexDirection: 'column',
});

const SectionHeader = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '6px',
});

const AddButton = styled(Button)({
    width: '24px',
    height: '24px',
    minWidth: '24px',
    minHeight: '24px',
    borderRadius: '50%',
    backgroundColor: 'var(--neutral2)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&:hover svg circle': {
        fill: '#E8F0FE',
        stroke: '#007AFF',
    },
    '&:hover svg path': {
        fill: '#007AFF',
    },
});

const ContactsContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    flex: 1,
});

const ContactCard = styled(Card)({
    borderRadius: '10px',
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    position: 'relative',
});

const ContactField = styled('div')({
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: '5px',
});

const BillingSection = styled('div')({
    marginTop: '16px',
});

const NoContactsMessage = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: '100%',
    gap: '16px',
    padding: '24px',
    textAlign: 'center',
});
