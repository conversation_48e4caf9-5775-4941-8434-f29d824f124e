import { Edit, ExpandLess, ExpandMore, MoreVert } from '@mui/icons-material';
import { Box, ButtonBase, Collapse, IconButton, MenuItem, styled } from '@mui/material';
import { VehicleContactDetails } from 'api/customers';
import SMenu from 'common/components/mui/SMenu';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import { selectVehicleDetails, vehicleDetailsActions } from 'store/slices/vehicleDetails';
import { useVehicleDetailsRightColumnContext } from '../VehicleDetailsRightColumnContext';

type VehicleDetailsCollapsibleSectionProps = React.PropsWithChildren<{
    initialOpen?: boolean;
    contact: VehicleContactDetails;
}>;

export default function VehicleContactDetailsCollapsibleSection({
    children,
    initialOpen,
    contact,
}: VehicleDetailsCollapsibleSectionProps) {
    const [open, setOpen] = useState(initialOpen);
    const [hover, setHover] = useState(false);
    const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { setRightPanelMode, setEditingContact } = useVehicleDetailsRightColumnContext();
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));
    const allowEditCustomers = useAppSelector((r) => selectUserPermission(r).allowEditCustomers);

    const ownerId = details?.details.ownerId;

    const getContactTitle = (): string => {
        const roleTitle =
            contact.customerId === ownerId
                ? t('afterSalesCrm.vehicleDetails.owner')
                : t('afterSalesCrm.vehicleDetails.contact');
        return `${roleTitle}: ${contact.firstName} ${contact.lastName}`;
    };

    const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation();
        setMenuAnchorEl(event.currentTarget);
    };

    const handleEditClick = (event: React.MouseEvent<HTMLElement>) => {
        event.stopPropagation();
        setRightPanelMode('editContact');
        setEditingContact(contact);
    };

    const handleMenuClose = () => {
        setMenuAnchorEl(null);
    };

    const handleToggle = () => {
        const newOpenState = !open;
        setOpen(newOpenState);

        dispatch(
            vehicleDetailsActions.toggleSectionExpanded({
                vehicleId,
                customerId: contact.customerId,
            })
        );
    };

    return (
        <DivRoot
            onMouseEnter={() => setHover(true)}
            onMouseLeave={() => {
                setHover(false);
                handleMenuClose();
            }}
        >
            <DivCollapseHeader
                data-open={open}
                component="div"
                role="button"
                onClick={handleToggle}
            >
                <Title>{getContactTitle()}</Title>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {hover && allowEditCustomers && (
                        <>
                            <IconButton
                                sx={{ padding: '2px!important' }}
                                size="small"
                                onClick={handleEditClick}
                            >
                                <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                                sx={{ padding: '2px!important' }}
                                size="small"
                                onClick={handleMenuOpen}
                            >
                                <MoreVert fontSize="small" />
                            </IconButton>
                        </>
                    )}
                    {open ? <ExpandLess /> : <ExpandMore />}
                </Box>
            </DivCollapseHeader>
            <Collapse in={open}>{children}</Collapse>

            <SMenu
                anchorEl={menuAnchorEl}
                open={Boolean(menuAnchorEl)}
                onClose={handleMenuClose}
                sx={{
                    '& .MuiPaper-root': {
                        backgroundColor: 'var(--neutral2)',
                        color: 'var(--neutral8)',
                        fontFamily: 'Inter',
                        fontSize: 10,
                        fontWeight: 400,
                        borderRadius: 1,
                    },
                    '& .MuiMenuItem-root:hover': {
                        backgroundColor: 'var(--cm5)',
                    },
                }}
            >
                <MenuItem
                    key="setAsOwner"
                    disabled={contact.customerId === ownerId}
                    onClick={() => {
                        setEditingContact(contact);
                        setRightPanelMode('establishOwner');
                        handleMenuClose();
                    }}
                >
                    {t('afterSalesCrm.vehicleDetails.setAsOwner')}
                </MenuItem>
                <MenuItem
                    key="removeAssociation"
                    onClick={() => {
                        setEditingContact(contact);
                        setRightPanelMode('removeContactAssociation');
                        handleMenuClose();
                    }}
                >
                    {t('afterSalesCrm.vehicleDetails.removeAssociation')}
                </MenuItem>
            </SMenu>
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    borderRadius: 10,
    backgroundColor: 'var(--neutral1)',
    overflow: 'hidden',
});

const DivCollapseHeader = styled(ButtonBase)({
    backgroundColor: 'var(--neutral3)',
    padding: '8px 10px 8px 20px',
    display: 'grid',
    gridTemplateColumns: '1fr auto',
    cursor: 'pointer',
    alignItems: 'center',

    '&:hover': {
        color: 'var(--neutral9)',
    },

    outline: '1px solid transparent',
    outlineOffset: -1,
    transition: 'outline-color 0.1s',
    borderRadius: '10px',

    '&[data-open=true]': {
        borderRadius: '10px 10px 0 0',
    },

    '&:active': {
        outlineColor: 'var(--neutral5)',
    },

    '& .MuiTouchRipple-rippleVisible': {
        animationDuration: '100ms',
    },

    '& .MuiTouchRipple-root': {
        opacity: 0.3,
    },
}) as typeof ButtonBase;

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--neutral8)',
    verticalAlign: 'center',
    height: '100%',
    lineHeight: '2',
    width: '190px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));
