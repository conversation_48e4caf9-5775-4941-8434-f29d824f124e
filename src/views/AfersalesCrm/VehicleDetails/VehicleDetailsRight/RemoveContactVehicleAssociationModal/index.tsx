import { Box, Button, Input, Modal, Typography, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMutation } from '@tanstack/react-query';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchVehicleThunk, selectVehicleDetails } from 'store/slices/vehicleDetails';
import { useParams } from 'react-router-dom';
import useToasters from 'common/hooks/useToasters';
import CustomersApi from 'api/customers';

import { Close } from '@mui/icons-material';
import { useVehicleDetailsRightColumnContext } from '../VehicleDetailsRightColumnContext';
import { ExclamationInCircleIcon } from 'common/components/Icons/ExclamationInCircleIcon';
import { useState } from 'react';
import { TextField } from 'common/components/Inputs';

type RemoveContactVehicleAssociationModalProps = {
    open: boolean;
};

export default function RemoveContactVehicleAssociationModal({
    open,
}: RemoveContactVehicleAssociationModalProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { vehicleId = '' } = useParams<{ vehicleId: string }>();
    const { details } = useAppSelector((r) => selectVehicleDetails(r, vehicleId));
    const { setRightPanelMode, editingContact, setEditingContact } =
        useVehicleDetailsRightColumnContext();

    const toasters = useToasters();
    const [reasonText, setReasonText] = useState('');

    const removeAssociationMutation = useMutation({
        mutationFn: () => {
            if (!editingContact?.customerId) {
                throw new Error('Missing customerId');
            }

            return CustomersApi.removeContactVehicleAssociation(
                vehicleId,
                editingContact?.customerId,
                reasonText
            );
        },
        onSuccess: () => {
            dispatch(fetchVehicleThunk({ id: vehicleId }));
            toasters.success(
                t('afterSalesCrm.vehicleDetails.contactDetails.removeSuccessMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.removeSuccessTitle')
            );
            setRightPanelMode(null);
            setEditingContact(null);
            setReasonText('');
        },
        onError: () => {
            toasters.danger(
                t('afterSalesCrm.vehicleDetails.contactDetails.errorMessage'),
                t('afterSalesCrm.vehicleDetails.contactDetails.errorTitle')
            );
            setRightPanelMode(null);
            setEditingContact(null);
            setReasonText('');
        },
    });

    const handleClose = () => {
        setRightPanelMode(null);
        setEditingContact(null);
    };

    const handleRemoveAssociation = () => {
        removeAssociationMutation.mutate();
    };

    const { brand, model, year, plates } = details?.details || {};

    const mainParts = [brand, model, year].filter(Boolean).join(' ');
    const vehicleInfo = [mainParts, plates].filter(Boolean).join(', ');

    const trimmedReason = reasonText.trim();
    const isReasonTooShort = trimmedReason.length < 5;
    const containsAlphanumeric = /[a-zA-Z0-9]/.test(trimmedReason);
    const isReasonTooLong = reasonText.length > 250;

    const isValidReason = !isReasonTooLong && !isReasonTooShort && containsAlphanumeric;

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="remove-association-modal"
            aria-describedby="modal-to-remove-contact-vehicle-association"
        >
            <ModalContainer>
                <CloseButton onClick={handleClose}>
                    <Close />
                </CloseButton>

                <IconContainer>
                    <ExclamationInCircleIcon size={51} />
                </IconContainer>

                <Typography
                    variant="h4"
                    align="center"
                    sx={{ fontWeight: 700, mb: 2, color: 'var(--neutral9)' }}
                >
                    {t('afterSalesCrm.vehicleDetails.contactDetails.removeAssociationTitle')}
                </Typography>

                <Typography variant="body1" align="center" sx={{ mb: 2, color: 'var(--neutral9)' }}>
                    {editingContact?.firstName} {editingContact?.lastName}{' '}
                    {t('afterSalesCrm.vehicleDetails.contactDetails.willBeUnlinkedFrom')}{' '}
                    {vehicleInfo}.
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Box width={360}>
                        <Typography variant="h9Inter" sx={{ color: 'var(--neutral9)' }}>
                            {t(
                                'afterSalesCrm.vehicleDetails.contactDetails.reasonForDisassociation'
                            )}
                        </Typography>
                        <TextField
                            placeholder={t(
                                'afterSalesCrm.vehicleDetails.contactDetails.confirmationPlaceholder'
                            )}
                            value={reasonText}
                            onChange={(e) => setReasonText(e.target.value)}
                            fullWidth
                            sx={{
                                marginTop: 1,
                                marginBottom: 2,
                                height: 60,
                                padding: '0',
                                '& .MuiInputBase-input::placeholder': {
                                    fontFamily: 'Inter',
                                    fontSize: '10px',
                                    color: 'var(--neutral6)',
                                    lineHeight: '100%',
                                    opacity: 1,
                                },
                                '& .MuiInputBase-input': {
                                    fontFamily: 'Inter',
                                    fontSize: '10px',
                                    color: 'var(--neutral9)',
                                    lineHeight: '100%',
                                    opacity: 1,
                                    padding: '7px',
                                },
                                '& .MuiInputBase-root': {
                                    padding: '7px',
                                },
                                '& .MuiOutlinedInput-root': {
                                    '& fieldset': {
                                        borderColor: isReasonTooLong ? 'red' : 'var(--neutral4)',
                                    },
                                    '&:hover fieldset': {
                                        borderColor: isReasonTooLong ? 'red' : 'var(--neutral6)',
                                    },
                                    '&.Mui-focused fieldset': {
                                        borderColor: isReasonTooLong ? 'red' : 'var(--cm1)',
                                    },
                                },
                            }}
                            inputProps={{
                                maxLength: 1000,
                                style: {
                                    height: 50,
                                    overflowY: 'auto',
                                    padding: '0',
                                },
                            }}
                            multiline
                            rows={1}
                            helperText={`${reasonText.length}/250`}
                            FormHelperTextProps={{
                                sx: {
                                    fontFamily: 'Inter',
                                    fontSize: '10px',
                                    color: 'var(--neutral6)',
                                    position: 'absolute',
                                    bottom: '10px',
                                    right: '20px',
                                    textAlign: 'right',
                                    marginRight: 0,
                                    marginLeft: 'auto',
                                },
                            }}
                        />
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <UpdateButton
                        variant="contained"
                        onClick={handleRemoveAssociation}
                        disabled={removeAssociationMutation.isLoading || !isValidReason}
                        fullWidth
                        sx={{
                            color: '#F6F6F6',
                            fontWeight: '700!important',
                            fontFamily: 'Inter',
                            fontSize: 12,
                        }}
                    >
                        {t('afterSalesCrm.vehicleDetails.contactDetails.remove')}
                    </UpdateButton>
                </Box>
            </ModalContainer>
        </Modal>
    );
}

const ModalContainer = styled(Box)(() => ({
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 500,
    backgroundColor: 'white',
    borderRadius: 8,
    boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
    padding: '50px 38px 38px 45px',
    outline: 'none',
}));

const IconContainer = styled(Box)({
    display: 'flex',
    justifyContent: 'center',
    marginBottom: 16,
});

const UpdateButton = styled(Button)({
    color: 'white',
    width: '180px',
    height: '37px',
    borderRadius: 24,
    padding: '10px 16px',
    textTransform: 'none',
    backgroundColor: 'var(--cm1)',
    fontWeight: 500,

    '&.Mui-disabled': {
        backgroundColor: 'var(--cm1)',
        opacity: 0.5,
        color: 'white',
    },
});

const CloseButton = styled(Box)({
    position: 'absolute',
    top: 12,
    right: 12,
    cursor: 'pointer',
    color: 'var(--neutral6)',
    '&:hover': {
        color: 'var(--neutral8)',
    },
});
