import React, { createContext, useContext, useState, ReactNode } from 'react';
import { VehicleContactDetails } from '../../../../api/customers';

export type RightPanelMode =
    | null
    | 'addContact'
    | 'editContact'
    | 'establishOwner'
    | 'removeContactAssociation';

type VehicleDetailsRightColumnContextType = {
    rightPanelMode: RightPanelMode;
    setRightPanelMode: (mode: RightPanelMode) => void;
    editingContact: VehicleContactDetails | null;
    setEditingContact: (contact: VehicleContactDetails | null) => void;
};

const VehicleDetailsRightColumnContext = createContext<
    VehicleDetailsRightColumnContextType | undefined
>(undefined);

export const useVehicleDetailsRightColumnContext = () => {
    const context = useContext(VehicleDetailsRightColumnContext);
    if (!context) {
        throw new Error(
            'useVehicleDetailsRightColumnContext must be used within a VehicleDetailsRightColumnProvider'
        );
    }
    return context;
};

export const VehicleDetailsRightColumnProvider = ({ children }: { children: ReactNode }) => {
    const [rightPanelMode, setRightPanelMode] = useState<RightPanelMode | null>(null);
    const [editingContact, setEditingContact] = useState<VehicleContactDetails | null>(null);

    return (
        <VehicleDetailsRightColumnContext.Provider
            value={{ rightPanelMode, setRightPanelMode, editingContact, setEditingContact }}
        >
            {children}
        </VehicleDetailsRightColumnContext.Provider>
    );
};
