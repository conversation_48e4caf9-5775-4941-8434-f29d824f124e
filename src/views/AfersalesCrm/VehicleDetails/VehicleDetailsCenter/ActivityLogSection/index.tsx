import { useQuery } from '@tanstack/react-query';
import CustomersApi from 'api/customers';

export default function ActivityLogSection({ vehicleId }: { vehicleId: string }) {
    const { data } = useQuery({
        queryKey: ['vehicle', 'activity-log', vehicleId],
        enabled: !!vehicleId,
        queryFn: () => CustomersApi.getVehicleActivityLog(vehicleId),
    });

    return (
        <>
            <p>Activity log for vehicle {vehicleId} will be here</p>
            <pre>{JSON.stringify(data, null, 1)}</pre>
        </>
    );
}
