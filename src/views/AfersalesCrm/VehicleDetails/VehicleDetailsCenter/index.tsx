import { Refresh } from '@mui/icons-material';
import { <PERSON>b<PERSON><PERSON>x<PERSON>, TabList, TabPanel } from '@mui/lab';
import { Alert, Box, IconButton, styled, Tab, tabsClasses } from '@mui/material';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useQueryParam from 'common/hooks/useQueryParam';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchVehicleThunk, selectVehicleDetails } from 'store/slices/vehicleDetails';
import { useHeaderLoading } from 'views/HeaderBar';
import VehicleActivitiesSection from './ActivitiesSection';
import ActivityLogSection from './ActivityLogSection';
import OrdersSection from './OrdersSection';
import WhatsAppSection from './WhatsAppSection';

const selectVehicleDetailsMeta = createSelector(
    selectVehicleDetails,
    ({ details, error, isLoading }) => ({
        isLoading,
        isFallbackFromCache: details?.isFallbackFromCache,
        error,
    })
);

function VehicleDetailsError({ vehicleId }: { vehicleId: string }) {
    const { error, isLoading, isFallbackFromCache } = useAppSelector((r) =>
        selectVehicleDetailsMeta(r, vehicleId)
    );
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    useHeaderLoading(isLoading);

    function onRetry() {
        dispatch(fetchVehicleThunk({ id: vehicleId }));
    }

    const children = [];

    if (error) {
        children.push(
            <Alert
                action={
                    <Button
                        sx={{ color: 'var(--neutral3) !important' }}
                        cmosVariant="typography"
                        onClick={onRetry}
                    >
                        <Refresh sx={{ mr: 0.5 }} /> {t('commonLabels.refresh')}
                    </Button>
                }
                severity="error"
            >
                {error.message}
            </Alert>
        );
    }

    if (isFallbackFromCache) {
        children.push(
            <Alert
                severity="warning"
                action={
                    <IconButton onClick={onRetry}>
                        <Refresh />
                    </IconButton>
                }
            >
                {t('afterSalesCrm.vehicleDetails.fallbackFromCacheWarning')}
            </Alert>
        );
    }

    if (children.length === 0) return null;

    return <Box sx={{ '& > *': { marginTop: '4px' } }}>{children}</Box>;
}

export default function VehicleDetailsCenter({ vehicleId }: { vehicleId: string }) {
    const [tab, setTab] = useQueryParam('tab');
    const { t } = useAppTranslation();

    return (
        <TabContext value={tab || 'activities'}>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    minHeight: '100%',
                }}
            >
                <StyledHeader>
                    <VehicleDetailsError vehicleId={vehicleId} />

                    <StyledTabs onChange={(_, value) => setTab(value, true)}>
                        <StyledTab
                            disableRipple
                            value="activities"
                            label={t('afterSalesCrm.vehicleDetails.tabs.activities')}
                        />
                        <StyledTab
                            disableRipple
                            value="whatsapp"
                            label={t('afterSalesCrm.vehicleDetails.tabs.whatsapp')}
                        />
                        <StyledTab
                            disableRipple
                            value="orders"
                            label={t('afterSalesCrm.vehicleDetails.tabs.orders')}
                        />
                        <StyledTab
                            disableRipple
                            value="activityLog"
                            label={t('afterSalesCrm.vehicleDetails.tabs.activityLog')}
                        />
                    </StyledTabs>
                </StyledHeader>

                <DivContent>
                    <StyledTabPanel value="activities">
                        <VehicleActivitiesSection vehicleId={vehicleId} />
                    </StyledTabPanel>
                    <StyledTabPanel value="whatsapp">
                        <WhatsAppSection vehicleId={vehicleId} />
                    </StyledTabPanel>
                    <StyledTabPanel value="orders">
                        <OrdersSection vehicleId={vehicleId} />
                    </StyledTabPanel>
                    <StyledTabPanel value="activityLog">
                        <ActivityLogSection vehicleId={vehicleId} />
                    </StyledTabPanel>
                </DivContent>
            </Box>
        </TabContext>
    );
}

const StyledTabPanel = styled(TabPanel)({ padding: 0 });

const StyledHeader = styled('header')({
    position: 'sticky',
    top: 0,
    backgroundColor: 'var(--neutral2)',
});

const StyledTabs = styled(TabList)({
    '--tab-height': '40px',

    height: 'var(--tab-height)',
    minHeight: 'var(--tab-height)',

    [`& .${tabsClasses.flexContainer}`]: {
        gap: '10px',
    },

    '.MuiTabs-indicator': {
        display: 'none',
    },
});

const StyledTab = styled(Tab)({
    textTransform: 'none',
    height: 'var(--tab-height)',
    minHeight: 'var(--tab-height)',
    padding: '0 8px',
    minWidth: 0,
    position: 'relative',
    justifyContent: 'flex-end',
    paddingBottom: '5px',

    '&::after': {
        display: 'block',
        content: '" "',
        position: 'absolute',
        height: 2,
        backgroundColor: 'var(--neutral9)',
        left: 4,
        right: 4,
        bottom: 0,
        transition: 'transform 0.15s',
        transform: 'scaleY(0)',
        transformOrigin: 'bottom',
    },

    '&.Mui-selected': {
        color: 'var(--neutral9)',
        fontWeight: '700',

        '&::after': {
            transform: 'scaleY(1)',
        },
    },

    '&:hover': {
        color: 'var(--neutral8)',
    },
});

const DivContent = styled('div')({
    backgroundColor: 'var(--neutral1)',
    border: '1px solid var(--neutral4)',
    flexGrow: '1',
});
