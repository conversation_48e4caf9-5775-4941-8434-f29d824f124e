import {
    Autocomplete,
    AutocompleteInputChangeReason,
    Box,
    InputAdornment,
    ListItem,
    Paper,
    Popper,
    PopperProps,
    styled,
} from '@mui/material';
import { PopperChildrenProps } from '@mui/material/Popper/BasePopper.types';
import { AutocompleteChangeReason } from '@mui/material/useAutocomplete/useAutocomplete';
import { useQuery } from '@tanstack/react-query';
import { VehiclesCrmApi, VehicleSearchBarData } from 'api/customers';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { TextField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Interweave } from 'interweave';
import React, { forwardRef, SyntheticEvent, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { useSelector } from 'react-redux';
import { useAppDispatch } from 'store';
import { useDebounce } from 'use-debounce';
import { selectVehiclesFilters, vehiclesActions } from 'store/slices/vehicles';

export default function VehicleAutocomplete() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const filters = useSelector(selectVehiclesFilters);
    const [open, setOpen] = useState(false);
    const [search, setSearch] = useState(filters.autocompleteSearchValue ?? '');
    const [debouncedSearch] = useDebounce(search, 300);
    const DEFAULT_GUID = '00000000-0000-0000-0000-000000000000';
    const queryKey = ['vehicles', 'search', debouncedSearch];
    const queryFn = () => VehiclesCrmApi.searchVehiclesByContactNumber(debouncedSearch);

    const { data, isLoading } = useQuery(queryKey, queryFn, {
        enabled: debouncedSearch !== '' && debouncedSearch !== undefined,
        keepPreviousData: false,
    });

    const options = useMemo(() => {
        if (!data) return [];
        return data;
    }, [data]);

    const handleInputChange = (
        _: SyntheticEvent<Element, Event>,
        newInputValue: string,
        reason: AutocompleteInputChangeReason
    ) => {
        if (reason === 'reset') return;

        if (reason === 'clear' || newInputValue.length === 0) {
            setSearch('');
            dispatch(
                vehiclesActions.setFilters({
                    ...filters,
                    ids: [],
                    autocompleteSearchValue: null,
                })
            );
        }

        setSearch(newInputValue);
        setOpen(newInputValue.length > 0);
    };

    const handleOnFocus = () => {
        if (search && search.length > 0) {
            setOpen(true);
        } else {
            setOpen(false);
        }
    };

    const handleValueChange = (
        _e: SyntheticEvent<Element, Event>,
        value: VehicleSearchBarData | null,
        reason: AutocompleteChangeReason
    ) => {
        dispatch(
            vehiclesActions.setFilters({
                ...filters,
                ids: value ? [value.id] : [],
                autocompleteSearchValue: search,
            })
        );
        setOpen(false);
        if (reason === 'clear') {
            return;
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Enter' && search.length > 0 && !isLoading) {
            const vehicleIds =
                options.length > 0 ? options.map((vehicle) => vehicle.id) : [DEFAULT_GUID];

            dispatch(
                vehiclesActions.setFilters({
                    ...filters,
                    ids: vehicleIds,
                    autocompleteSearchValue: search,
                })
            );

            setOpen(false);
        }
    };

    return (
        <Autocomplete
            noOptionsText={t('afterSalesCrm.searchBar.noResults')}
            disablePortal
            clearIcon={null}
            sx={{ width: '245px' }}
            clearOnBlur={true}
            options={options}
            inputValue={search}
            renderInput={(params) => {
                return (
                    <StyledTextField
                        {...params}
                        placeholder={t('afterSalesCrm.searchBar.placeholderVehicles')}
                        showValidationIndicators={true}
                        isRequired={false}
                        cmosVariant="roundedGrey"
                        enableEnterComplete={true}
                        onKeyDown={handleKeyDown}
                        InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                                <>
                                    <SearchIconWrapper>
                                        <SearchIcon fill={'var(--neutral6)'} />
                                    </SearchIconWrapper>
                                </>
                            ),
                        }}
                    />
                );
            }}
            onInputChange={handleInputChange}
            onChange={handleValueChange}
            open={open}
            onFocus={handleOnFocus}
            onBlur={() => setOpen(false)}
            forcePopupIcon={false}
            getOptionLabel={(option) => {
                return option.vin;
            }}
            renderOption={(props, v) => (
                <ListItem {...props}>
                    <VehicleItem vehicle={v} inputValue={search} />
                </ListItem>
            )}
            PopperComponent={PopperComponent}
            PaperComponent={PaperComponent}
            filterOptions={(options) => options}
            onKeyDown={handleKeyDown}
        />
    );
}

type VehicleItemProps = {
    vehicle: VehicleSearchBarData;
    inputValue: string | undefined;
};

const Title = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[8],
}));

function selectionText(defaultValue: string, inputValue: string): string {
    const normalizedInputValue = inputValue.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    const regexp = new RegExp(normalizedInputValue, 'i');

    const startIndex = defaultValue.search(regexp);
    if (startIndex === -1) {
        return defaultValue;
    }
    const lastIndex = startIndex + inputValue.length;

    return (
        defaultValue.slice(0, startIndex) +
        '<b>' +
        defaultValue.slice(startIndex, lastIndex) +
        '</b>' +
        defaultValue.slice(lastIndex)
    );
}

function VehicleItem({ vehicle, inputValue }: VehicleItemProps) {
    const { t } = useAppTranslation();

    const selectedTextVin = vehicle.vin ? selectionText(vehicle.vin, inputValue ?? '') : '-';
    const selectedHtmlTextVin = <Interweave content={selectedTextVin} />;

    const selectedTextPlates = vehicle.plates
        ? selectionText(vehicle.plates, inputValue ?? '')
        : '-';
    const selectedHtmlTextPlates = <Interweave content={selectedTextPlates} />;

    const selectedTextMobile = vehicle.mobile
        ? selectionText(vehicle.mobile, inputValue ?? '')
        : '-';
    const selectedHtmlTextMobile = <Interweave content={selectedTextMobile} />;

    const hasFullName = vehicle.contactFirstName || vehicle.contactLastName;
    const fullName = hasFullName
        ? `${vehicle.contactFirstName ?? ''} ${vehicle.contactLastName ?? ''}`.trim()
        : '-';
    const selectedTextFullName = selectionText(fullName, inputValue ?? '');
    const selectedHtmlTextFullName = <Interweave content={selectedTextFullName} />;

    return (
        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', width: '100%', flexBasis: '85%' }}>
                <Box sx={{ display: 'flex', alignContent: 'center', flexWrap: 'wrap' }}>
                    <CarIcon fill={'var(--cm1'} />
                </Box>
                <Box padding={'10px 5px'}>
                    <Title>
                        {vehicle.isOwner
                            ? t('afterSalesCrm.searchBar.owner')
                            : t('afterSalesCrm.searchBar.contact')}
                        {': '} {selectedHtmlTextFullName}
                        {' / '}
                        {t('commonLabels.mobile')} {': '}
                        {selectedHtmlTextMobile}
                        {' / '}
                        {t('afterSalesCrm.searchBar.plate')}
                        {': '}
                        {selectedHtmlTextPlates}
                        {' / '}
                        {t('afterSalesCrm.searchBar.vin')}
                        {': '}
                        {selectedHtmlTextVin}
                        {' / '}
                        {t('afterSalesCrm.searchBar.vehicle')}
                        {': '}
                        {vehicle.brand || '-'} {vehicle.model || '-'} {vehicle.year || '-'}
                    </Title>
                </Box>
            </Box>
        </Box>
    );
}

const PopperComponent = forwardRef<HTMLDivElement, PopperProps>(({ children, ...props }, ref) => {
    return createPortal(
        <Popper {...props} ref={ref} placement="bottom-start">
            {typeof children === 'function' ? children(props as PopperChildrenProps) : children}
        </Popper>,
        document.body
    );
});

const PaperComponent = forwardRef(
    ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>, _ref) => {
        const { t } = useAppTranslation();

        return (
            <Paper {...props} sx={{ width: 850 }}>
                <HeaderItem>
                    <SelectVehicleLabel>{t('afterSalesCrm.searchBar.label')}</SelectVehicleLabel>
                </HeaderItem>

                {children}
            </Paper>
        );
    }
);

const SearchIconWrapper = styled('div')({
    position: 'absolute',
    right: '10px',
    top: '18px',
    transform: 'translateY(-50%)',
    pointerEvents: 'none',
});

const HeaderItem = styled(ListItem)({
    flexDirection: 'column',
    justifyContent: 'start',
    alignItems: 'start',
    paddingBottom: 0,
});

const SelectVehicleLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
    marginTop: 5,
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
    '& .MuiOutlinedInput-root .MuiAutocomplete-input': {
        padding: '0 30px 0 5px',
        '&::placeholder': {
            ...theme.typography.h6Inter,
            fontWeight: 'normal',
            color: 'var(--neutral6)',
            opacity: 1,
        },
    },
    '& .MuiOutlinedInput-root': {
        backgroundColor: '#fafafa!important',
        '&:hover': {
            borderColor: '#0069FF',
            backgroundColor: '#e1ecfb!important',
        },
    },

    width: '300px',
}));
