import { Autocomplete, Box, Checkbox, ListItem, styled } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { TuneIcon } from 'common/components/Icons/TuneIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { TextField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import * as React from 'react';
import { Children, forwardRef, useEffect, useMemo, useState } from 'react';
import { FixedSizeList, ListChildComponentProps } from 'react-window';

export type HeaderAutocompleteFilterProps<T> = {
    mainLabel: string;
    options: FilterOption<T>[] | null;
    selected: FilterOptionId<T>[];
    onSelectedChanged: (selected: FilterOptionId<T>[]) => void;
    allOptionLabel: string;
    noOptionsText?: string;
    disableSearch: boolean;
    renderValue?: (value: T) => string;
};

export type FilterOption<T> = {
    id: FilterOptionId<T>;
    label: string;
    labelImage?: string;
    subLabelText?: string;
    isSelectAllOption?: boolean;
};

export type FilterOptionId<T> = T;

const renderRow = (props: ListChildComponentProps) => {
    const { data, index, style } = props;
    const option = data[index];
    return (
        <div style={{ ...style, top: style.top as number }} key={option.key}>
            {option}
        </div>
    );
};

const CustomScrollbarWrapper = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(function CustomScrollbarWrapper(props, ref) {
    return (
        <div
            ref={ref}
            {...props}
            style={{
                ...props.style,
                ...scrollbarStyle(),
            }}
            className="custom-scrollbar"
        />
    );
});

const ListboxComponent = forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLElement> & { mainLabel?: string; showHeader?: boolean }
>(function ListboxComponent(props, ref) {
    const { children, mainLabel, showHeader, ...other } = props;
    const itemData = Children.toArray(children);
    const itemCount = itemData.length;
    const itemSize = 40;
    const itemCountMin = Math.min(8, itemCount);
    const padding = itemCount < 8 ? 7 : -25;
    const height = itemSize * itemCountMin + padding;

    return (
        <Box
            ref={ref}
            {...other}
            sx={{
                overflow: 'hidden!important',
                padding: '0px!important',
            }}
        >
            {showHeader && (
                <Box
                    sx={{
                        padding: '8px 16px',
                        color: 'var(--neutral8)',
                        typography: 'h6Inter',
                        backgroundColor: '#F3F3F3',
                        fontWeight: '400',
                        lineHeight: '26px',
                        height: '41px',
                    }}
                >
                    {mainLabel}
                </Box>
            )}
            <FixedSizeList
                height={height - 5}
                width="100%"
                itemSize={itemSize}
                itemCount={itemCount}
                itemData={itemData}
                overscanCount={5}
                outerElementType={CustomScrollbarWrapper}
            >
                {renderRow}
            </FixedSizeList>
        </Box>
    );
});

export function HeaderAutocompleteFilter<T extends string | number>({
    mainLabel,
    options,
    selected,
    onSelectedChanged,
    allOptionLabel,
    noOptionsText,
    disableSearch = false,
    renderValue,
}: HeaderAutocompleteFilterProps<T>) {
    const { t } = useAppTranslation();
    const [selectedOptions, setSelectedOptions] = useState<FilterOptionId<T>[]>(selected);
    const [inputValue, setInputValue] = useState('');
    const [isFocused, setIsFocused] = useState(false);

    const allOptions = useMemo(() => {
        if (!options) return null;

        if (disableSearch) {
            return [...options];
        }

        return [
            {
                id: 'allOptions' as FilterOptionId<T>,
                label: allOptionLabel,
                isSelectAllOption: true,
            },
            ...options,
        ];
    }, [options, allOptionLabel, disableSearch]);

    const filteredOptions = useMemo(() => {
        if (!allOptions) return [];

        if (disableSearch) return allOptions;

        const searchText = inputValue.toLowerCase().trim();

        return allOptions.filter((option) => {
            if (searchText && option.isSelectAllOption) return false;
            return option.isSelectAllOption || option.label.toLowerCase().includes(searchText);
        });
    }, [allOptions, inputValue, disableSearch]);

    // Unselect options that are no longer available among current options
    useEffect(() => {
        if (!options) return;
        const validSelectedOptions = selectedOptions.filter((option) =>
            options.some((opt) => opt.id === option)
        );

        if (validSelectedOptions.length !== selectedOptions.length) {
            setSelectedOptions(validSelectedOptions);
            onSelectedChanged(validSelectedOptions);
        }
    }, [options, selectedOptions, onSelectedChanged]);

    const handleChange = (_: React.SyntheticEvent, newValue: FilterOption<T>[]) => {
        const newSelected = newValue
            .filter((option) => !option.isSelectAllOption)
            .map((option) => option.id);

        if (newValue.some((option) => option.isSelectAllOption)) {
            setSelectedOptions([]);
        } else {
            setSelectedOptions(newSelected);
        }
    };

    const displayData = useMemo(() => {
        if (isFocused) return { label: '', image: null };

        if (selectedOptions.length === 0) {
            return { label: t(`afterSalesCrm.columns.${mainLabel}`), image: null };
        }

        const firstOption = options?.find((opt) => opt.id === selectedOptions[0]);
        if (!firstOption) return { label: '', image: null };

        const translatedLabel = t(`afterSalesCrm.values.${firstOption.label}`, {
            defaultValue: firstOption.label,
        });

        if (selectedOptions.length > 1) {
            return {
                label: `${translatedLabel} +${selectedOptions.length - 1}`,
                image: firstOption.labelImage ?? null,
            };
        }

        return {
            label: translatedLabel,
            image: firstOption.labelImage ?? null,
        };
    }, [selectedOptions, options, t, isFocused, mainLabel]);

    const handleFocus = () => {
        setIsFocused(true);
        setInputValue('');
    };

    const handleBlur = () => {
        setIsFocused(false);
    };

    return (
        <Autocomplete
            multiple
            disableListWrap
            ListboxComponent={ListboxComponent}
            ListboxProps={
                {
                    mainLabel: t(`afterSalesCrm.columns.${mainLabel}`),
                    showHeader: !disableSearch,
                } as any
            }
            options={filteredOptions}
            value={selectedOptions
                .map((id) => options?.find((opt) => opt.id === id))
                .filter((opt): opt is FilterOption<T> => opt !== undefined)}
            open={isFocused}
            disableClearable
            renderTags={(_) => null}
            openOnFocus
            noOptionsText={noOptionsText}
            onBlur={handleBlur}
            onFocus={handleFocus}
            renderInput={(params) => (
                <Box sx={{ position: 'relative', width: '160px' }}>
                    {(!disableSearch || selectedOptions.length > 0) && (
                        <PlaceholderBox hasSelection={selectedOptions.length > 0}>
                            {displayData.image && (
                                <img
                                    src={displayData.image}
                                    alt=""
                                    style={{ width: 20, height: 20, flexShrink: 0 }}
                                />
                            )}
                            <EllipsisText>{displayData.label}</EllipsisText>
                        </PlaceholderBox>
                    )}
                    <StyledTextField
                        {...params}
                        variant="outlined"
                        sx={{ backgroundColor: '#fff' }}
                        size="small"
                        onKeyDown={(e) => {
                            if (e.key === 'Backspace') {
                                e.stopPropagation();
                            }
                        }}
                        placeholder={isFocused && !disableSearch ? t('commonLabels.search') : ''}
                        showValidationIndicators={false}
                        onChange={(e) => !disableSearch && setInputValue(e.target.value)}
                        cmosVariant="roundedGrey"
                        hasSelection={selectedOptions.length > 0}
                        disableSearch={disableSearch}
                        isFocused={isFocused}
                        inputProps={{
                            ...params.inputProps,
                            value:
                                disableSearch && (selectedOptions.length === 0 || isFocused)
                                    ? t(`afterSalesCrm.columns.${mainLabel}`)
                                    : inputValue,
                            readOnly: disableSearch,
                            style: {
                                cursor: disableSearch ? 'pointer' : 'text',
                                userSelect: disableSearch ? 'none' : 'auto',
                                color: disableSearch ? 'var(--neutral6)' : undefined,
                            },
                            onFocus: (e) => {
                                if (disableSearch) {
                                    const input = e.target as HTMLInputElement;
                                    requestAnimationFrame(() => {
                                        input.setSelectionRange(0, 0);
                                    });
                                }
                            },
                            onMouseDown: (e) => {
                                if (disableSearch) {
                                    e.preventDefault();
                                }
                            },
                        }}
                    />
                </Box>
            )}
            popupIcon={<TuneIcon />}
            componentsProps={{
                popupIndicator: { sx: { transform: 'none', transition: 'none' } },
                popper: {
                    placement: 'bottom-start',
                    modifiers: [
                        {
                            name: 'offset',
                            options: { offset: [0, 5] },
                        },
                    ],
                    sx: {
                        minWidth: '220px',
                        transform: 'translateY(5px)',
                        mt: '5px',
                        '.MuiAutocomplete-noOptions': {
                            fontSize: '12px',
                            fontFamily: 'Roboto',
                            color: 'var(--neutral8)',
                            backgroundColor: '#FAFAFA',
                        },
                    },
                },
            }}
            renderOption={(props, option, { selected, index }) => {
                const isAllOptionChecked = option.isSelectAllOption && selectedOptions.length === 0;
                const isChecked = option.isSelectAllOption ? isAllOptionChecked : selected;

                return (
                    <StyledListItem {...props} isLast={index === filteredOptions.length - 1}>
                        <Checkbox
                            checked={isChecked}
                            icon={<UncheckBoxIcon fill="var(--cm1)" />}
                            checkedIcon={<CheckBoxIcon fill="var(--cm1)" />}
                            sx={{ padding: 0 }}
                        />
                        <LabelContainer>
                            <LabelRow>
                                {option.labelImage && (
                                    <img src={option.labelImage} alt={option.label} />
                                )}
                                {t(
                                    option.isSelectAllOption
                                        ? option.label
                                        : `afterSalesCrm.values.${option.label}`,
                                    { defaultValue: option.label }
                                )}
                            </LabelRow>
                            {option.subLabelText && <SubLabel>{option.subLabelText}</SubLabel>}
                        </LabelContainer>
                    </StyledListItem>
                );
            }}
            onChange={handleChange}
            disableCloseOnSelect
            filterOptions={(x) => x}
            onClose={(_, reason) => {
                if (reason === 'toggleInput') {
                    return;
                }
                setInputValue('');
                onSelectedChanged(selectedOptions);
                setIsFocused(false);
            }}
        />
    );
}

export const StyledTextField = styled(TextField, {
    shouldForwardProp: (prop) => prop !== 'hasSelection' && prop !== 'isFocused',
})<{ hasSelection: boolean; isFocused: boolean; disableSearch: boolean }>(
    ({ hasSelection, isFocused, disableSearch }) => ({
        '& .MuiOutlinedInput-root': {
            borderRadius: '5px',
            width: '160px',
            backgroundColor: '#fafafa!important',
            border: '1px solid #c9cdd3',
            // paddingRight: '32px', // why was this here?
            height: '40px',
            cursor: disableSearch ? 'pointer' : 'text',
            '&:hover': {
                borderColor: '#0069FF',
                backgroundColor: '#e1ecfb!important',
            },
        },
        '& .MuiOutlinedInput-root .MuiAutocomplete-input': {
            '&::placeholder': {
                fontFamily: 'Inter',
                fontSize: '12px',
                fontWeight: 'normal',
                opacity: 1,
                color: hasSelection || isFocused ? 'var(--neutral8)' : 'var(--neutral6)',
            },
        },
        '& .MuiAutocomplete-inputRoot': {
            paddingTop: '3px!important',
        },
        '& .MuiOutlinedInput-notchedOutline': {
            border: 'none',
        },
        position: 'relative',
    })
);

const LabelRow = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    whiteSpace: 'pre',
});

const EllipsisText = styled('span')({
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    display: 'block',
});

const StyledListItem = styled(ListItem, {
    shouldForwardProp: (prop) => prop !== 'isLast',
})<{ isLast: boolean }>(({ isLast }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    height: 42,
    backgroundColor: 'transparent !important',
    color: 'var(--neutral8)',
    position: 'relative',
    '&.Mui-focused': {
        backgroundColor: '#e1ecfb!important',
        color: 'var(--cm1)!important',
    },
    '&::after': {
        content: isLast ? 'none' : '""',
        position: 'absolute',
        bottom: 0,
        left: '10px',
        width: '90%',
        height: '1px',
        backgroundColor: '#E0E0E0',
    },
}));

const PlaceholderBox = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'hasSelection',
})<{ hasSelection: boolean }>(({ hasSelection }) => ({
    position: 'absolute',
    left: '12px',
    top: '50%',
    transform: 'translateY(-50%)',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    pointerEvents: 'none',
    color: hasSelection ? 'var(--neutral8)' : 'var(--neutral6)',
    fontSize: '12px',
    zIndex: 1,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    width: 'calc(100% - 50px)',
}));

const LabelContainer = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    alignItems: 'center',
    fontWeight: '400',
}));

const SubLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h11Inter,
    color: 'var(--neutral6)',
    fontSize: '8px',
}));

export default React.memo(HeaderAutocompleteFilter) as typeof HeaderAutocompleteFilter;
