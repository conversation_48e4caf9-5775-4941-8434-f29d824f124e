import { InputAdornment, Paper, Popover, styled, useMediaQuery } from '@mui/material';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import VehicleAutocomplete from '../VehicleAutocomplete';

import Box from '@mui/material/Box';
import { useQuery } from '@tanstack/react-query';
import CustomersApi, { UsageType, usageTypeSchema } from 'api/customers';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import UsersApi from 'api/users';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { TuneIcon } from 'common/components/Icons/TuneIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { SMenuItem } from 'common/components/mui';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { shallowEqual, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands } from 'store/slices/clientsSlice/selectors';
import ensureBrandsFetched from 'store/slices/clientsSlice/thunks/ensureBrandsFetched';
import { selectUserPermission } from 'store/slices/user';
import { selectVehiclesFilters, vehiclesActions } from 'store/slices/vehicles';
import CreateNewCustomerPopup from 'views/Components/CreateNewCustomerPopup';
import MultipleBrandPicker from 'views/Components/MultipleBrandPicker';
import MultipleModelPicker from 'views/Components/MultipleModelPicker';
import MultipleYearPicker from 'views/Components/MultipleYearPicker';
import { z } from 'zod';
import HeaderAutocompleteFilter, {
    FilterOption,
    StyledTextField,
} from '../HeaderAutocompleteFilter';

const Root = styled('div')(({ theme }) => ({
    '&::after': {
        right: 0,
        left: 'initial',
    },
    justifyContent: 'right',
    gap: 10,
    position: 'relative',
    margin: '0 15px',
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItem: 'center',
    color: theme.palette.neutral[5],
    alignItems: 'center',
}));

export default function Header() {
    const { t } = useAppTranslation();

    return (
        <Root>
            <Box sx={{ display: 'flex', gap: 1, width: '87%', justifyContent: 'end' }}>
                <VehicleAutocomplete />
                <Box sx={{ display: 'flex', gap: 1 }}>
                    <FiltersByVehicle />
                    <Filters />
                </Box>
            </Box>
            <NewVehicleButton />
        </Root>
    );
}

function NewVehicleButton() {
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const { t } = useAppTranslation();
    const allowEditVehicles = useAppSelector((r) => selectUserPermission(r).allowEditVehicles);

    return (
        <>
            <Button
                disabled={!allowEditVehicles}
                onClick={() => setOpen(true)}
                sx={{ width: 140 }}
                label={t('afterSalesCrm.newVehicle')}
            />
            <CustomersSearchApiProvider mode="shop">
                <CreateNewCustomerPopup
                    open={open}
                    vehicleIsRequired
                    onCustomerAndVehicleCreated={(event) => {
                        setOpen(true);
                        if (event.vehicle) navigate('/vehicle/' + event.vehicle.id);
                    }}
                    onClose={() => setOpen(false)}
                />
            </CustomersSearchApiProvider>
        </>
    );
}

function FiltersByVehicle() {
    const { t } = useAppTranslation();
    const isSmall = useMediaQuery('(max-width: 1366px)');

    const brands = useAppSelector(selectBrands);
    const dispatch = useAppDispatch();

    const { data: brandsWithModels } = useQuery(
        ['brands-with-models', 'vehicle-database'],
        CustomersApi.getAllBrandsAndModels,
        {
            cacheTime: Infinity,
            staleTime: 60000,
        }
    );

    const filters = useSelector(selectVehiclesFilters, shallowEqual);

    const modelsMemo = useMemo(() => {
        if (!brandsWithModels) return null;

        return brandsWithModels
            .filter((brand) => filters.brand.includes(brand.name))
            .flatMap((brand) => brand.models.map((x) => ({ model: x, brand: brand.name })))
            .map((m) => ({
                id: JSON.stringify(m),
                label: m.model,
            }));
    }, [brandsWithModels, filters]);

    const yearsMemo = useMemo(() => {
        const currentYear = new Date().getFullYear();
        return Array.from({ length: currentYear - 1950 + 2 }, (_, i) => {
            const year = currentYear + 1 - i;
            return { id: year.toString(), label: year.toString() };
        });
    }, []);

    useEffect(() => {
        dispatch(ensureBrandsFetched());
    }, [dispatch]);

    const handleBrandsChange = useCallback(
        (selected: string[]) => {
            dispatch(
                vehiclesActions.setFilters({
                    brand: selected,
                })
            );
        },
        [dispatch]
    );

    const handleModelsChange = useCallback(
        (selected: string[]) => {
            const schema = z.object({
                brand: z.string(),
                model: z.string(),
            });
            const objects = selected.map((x) => schema.parse(JSON.parse(x)));

            const newValue: { name: string; models: string[] }[] = [];

            for (const { brand, model } of objects) {
                let brandEntry = newValue.find((x) => x.name === brand);
                if (!brandEntry) {
                    brandEntry = {
                        name: brand,
                        models: [],
                    };
                    newValue.push(brandEntry);
                }
                brandEntry.models.push(model);
            }

            dispatch(
                vehiclesActions.setFilters({
                    model: newValue,
                })
            );
        },
        [dispatch]
    );

    const handleYearsChange = useCallback(
        (selected: string[]) => {
            dispatch(
                vehiclesActions.setFilters({
                    year: selected,
                })
            );
        },
        [dispatch]
    );

    const selectedBrands = useMemo<string[]>(() => {
        return filters.brand || [];
    }, [filters.brand]);

    const optionBrands = useMemo<FilterOption<string>[] | null>(() => {
        if (brands.length === 0) return null;
        return brands.map((brand) => ({
            id: brand.name,
            label: brand.hasLogo ? brand.name : `         ${brand.name}`,
            labelImage: brand.hasLogo ? brand.brandUrl : undefined,
        }));
    }, [brands]);

    const selectedModels = useMemo(() => {
        return filters.model.flatMap((x) =>
            x.models.map((m) => JSON.stringify({ model: m, brand: x.name }))
        );
    }, [filters.model]);

    if (isSmall) {
        return (
            <CombinedVehicleFilters
                onChange={(brands, models, years) => {
                    dispatch(
                        vehiclesActions.setFilters({
                            brand: brands,
                            model: models.filter((x) => brands.includes(x.name)),
                            year: years,
                        })
                    );
                }}
                brands={filters.brand}
                models={filters.model}
                years={filters.year}
            />
        );
    } else {
        return (
            <>
                <HeaderAutocompleteFilter<string>
                    mainLabel="brand"
                    options={optionBrands}
                    selected={selectedBrands}
                    onSelectedChanged={handleBrandsChange}
                    allOptionLabel={`         ${t('afterSalesCrm.filters.allBrand')}`}
                    noOptionsText={t('afterSalesCrm.filters.noBrandsFound')}
                    disableSearch={false}
                />
                <HeaderAutocompleteFilter
                    mainLabel="model"
                    options={modelsMemo}
                    selected={selectedModels}
                    onSelectedChanged={handleModelsChange}
                    allOptionLabel={t('afterSalesCrm.filters.allModel')}
                    noOptionsText={t('afterSalesCrm.filters.noModelsFound')}
                    disableSearch={false}
                />
                <HeaderAutocompleteFilter<string>
                    mainLabel="year"
                    options={yearsMemo}
                    selected={filters.year}
                    onSelectedChanged={handleYearsChange}
                    allOptionLabel={t('afterSalesCrm.filters.allYear')}
                    noOptionsText={t('afterSalesCrm.filters.noYearsFound')}
                    disableSearch={false}
                />
            </>
        );
    }
}

function Filters() {
    const { t } = useAppTranslation();
    const isSmall = useMediaQuery('(max-width: 1600px)');

    const dispatch = useAppDispatch();
    const filters = useSelector(selectVehiclesFilters, shallowEqual);

    const { data: bdcAdvisors } = useQuery(
        ['bdc-advisors'],
        () => UsersApi.getBdcAdvisor().then((response) => response),
        {
            cacheTime: Infinity,
            staleTime: 60000,
        }
    );

    const handleUsageTypeChange = useCallback(
        (selected: string[]) => {
            dispatch(
                vehiclesActions.setFilters({
                    usageType: selected as UsageType[],
                })
            );
        },
        [dispatch]
    );

    const handleBdcAdvisorChange = useCallback(
        (selected: string[]) => {
            dispatch(
                vehiclesActions.setFilters({
                    bdcAdvisor: selected,
                })
            );
        },
        [dispatch]
    );

    const bdcAdvisorOptions = useMemo(() => {
        if (!bdcAdvisors) return null;
        return bdcAdvisors.map((advisor) => ({
            id: advisor.key,
            label: advisor.displayName,
            subLabelText: advisor.userName,
        }));
    }, [bdcAdvisors]);

    const usageTypeOptions = useMemo(
        () =>
            usageTypeSchema.options.map((usageType) => ({
                id: usageType,
                label: t(`afterSalesCrm.vehicleDetails.usageType.${usageType}`, {
                    defaultValue: usageType,
                }),
            })),
        [t]
    );

    if (isSmall) {
        return (
            <CombinedFilters
                usageTypeOptions={usageTypeOptions}
                assignedBdcAdvisorOptions={bdcAdvisorOptions ?? []}
                usageType={filters.usageType}
                assignedBdcAdvisor={filters.bdcAdvisor}
                onChange={(usageTypes, bdcAdvisors) => {
                    dispatch(
                        vehiclesActions.setFilters({
                            usageType: usageTypes,
                            bdcAdvisor: bdcAdvisors,
                        })
                    );
                }}
            />
        );
    } else {
        return (
            <>
                <HeaderAutocompleteFilter<UsageType>
                    mainLabel="usageType"
                    options={usageTypeOptions}
                    selected={filters.usageType}
                    onSelectedChanged={handleUsageTypeChange}
                    allOptionLabel={t('afterSalesCrm.filters.allUsageType')}
                    disableSearch={true}
                />
                <HeaderAutocompleteFilter<string>
                    mainLabel="assignedBdcAdvisor"
                    options={bdcAdvisorOptions}
                    selected={filters.bdcAdvisor}
                    onSelectedChanged={handleBdcAdvisorChange}
                    allOptionLabel={t('afterSalesCrm.filters.allAssignedBdcAdvisor')}
                    noOptionsText={t('afterSalesCrm.filters.noBdcAdvisorsFound')}
                    disableSearch={false}
                />
            </>
        );
    }
}

function CombinedVehicleFilters({
    brands,
    models,
    years,
    onChange,
}: {
    brands: string[];
    models: { name: string; models: string[] }[];
    years: string[];
    onChange: (
        brands: string[],
        models: { name: string; models: string[] }[],
        years: string[]
    ) => void;
}) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLDivElement | null>(null);
    const [open, setOpen] = useState(false);
    const [brandsValue, setBrandsValue] = useState(brands);
    const [modelsValue, setModelsValue] = useState(models);
    const [yearsValue, setYearsValue] = useState(years);

    const hasChanges = useMemo(() => {
        if (brands.length !== brandsValue.length) return true;
        if (models.length !== modelsValue.length) return true;
        if (years.length !== yearsValue.length) return true;

        if (!isEqual(brands.toSorted(), brandsValue.toSorted())) return true;
        if (!isEqual(years.toSorted(), yearsValue.toSorted())) return true;

        const flatModels = [...new Set(modelsValue.flatMap((x) => x.models))].toSorted();
        const flatModelsValue = [...new Set(models.flatMap((x) => x.models))].toSorted();

        if (flatModels.length !== flatModelsValue.length) return true;

        if (!isEqual(flatModels, flatModelsValue)) return true;

        return false;
    }, [brands, brandsValue, models, modelsValue, years, yearsValue]);

    return (
        <>
            <StyledTextField
                sx={{
                    '& .MuiInputBase-root': { cursor: 'pointer !important' },
                    '& input': { cursor: 'pointer !important' },
                }}
                ref={ref}
                onClick={handleOpen}
                readonly
                placeholder={t('afterSalesCrm.filters.filtersByVehicle')}
                endAdornment={
                    <InputAdornment position="end">
                        <TuneIcon />
                    </InputAdornment>
                }
                hasSelection={false}
                isFocused={false}
                disableSearch={false}
            />
            <Popover
                TransitionProps={{
                    timeout: 0,
                }}
                slots={{
                    paper: StyledPaper,
                }}
                onClose={handleClose}
                anchorEl={ref.current}
                open={open}
                anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
            >
                <CombinedFiltersHeader>
                    {t('afterSalesCrm.filters.selectFilters')}
                    <Button
                        disabled={!hasChanges}
                        sx={{ position: 'absolute', right: 8, top: 4, fontWeight: 'normal' }}
                        cmosVariant="typography"
                        onClick={handleApply}
                    >
                        {t('commonLabels.apply')}
                    </Button>
                </CombinedFiltersHeader>

                <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <MultipleBrandPicker
                        disablePlaceholderHint
                        showAllBrandsOption
                        cmosVariant="grey"
                        value={brandsValue}
                        onChange={handleBrandsChange}
                    />

                    <MultipleModelPicker
                        cmosVariant="grey"
                        filterBrands={brandsValue}
                        value={modelsValue}
                        onChange={setModelsValue}
                        disabled={brandsValue.length === 0}
                    />

                    <MultipleYearPicker
                        cmosVariant="grey"
                        value={yearsValue}
                        onChange={setYearsValue}
                        disablePlaceholderHint
                    />
                </Box>
            </Popover>
        </>
    );

    function handleBrandsChange(brands: string[]) {
        setBrandsValue(brands);
        setModelsValue((v) => v.filter((m) => brands.includes(m.name)));
    }

    function handleOpen() {
        setOpen(true);
        setBrandsValue(brands);
        setModelsValue(models);
        setYearsValue(years);
    }

    function handleClose() {
        setOpen(false);
    }

    function handleApply() {
        if (!hasChanges) return;
        onChange(brandsValue, modelsValue, yearsValue);
        setOpen(false); // closing and reopening also resets internal state
    }
}

function CombinedFilters({
    usageTypeOptions,
    assignedBdcAdvisorOptions,
    usageType,
    assignedBdcAdvisor,
    onChange,
}: {
    usageTypeOptions: FilterOption<UsageType>[];
    assignedBdcAdvisorOptions: FilterOption<string>[];
    usageType: UsageType[];
    assignedBdcAdvisor: string[];
    onChange: (usageTypes: UsageType[], bdcAdvisors: string[]) => void;
}) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLDivElement | null>(null);

    const [open, setOpen] = useState(false);
    const [usageTypeValue, setUsageTypeValue] = useState(usageType);
    const [assignedBdcAdvisorValue, setAssignedBdcAdvisorValue] = useState(assignedBdcAdvisor);

    const hasChanges = useMemo(() => {
        if (usageType.length !== usageTypeValue.length) return true;
        if (assignedBdcAdvisor.length !== assignedBdcAdvisorValue.length) return true;

        if (!isEqual(usageType.toSorted(), usageTypeValue.toSorted())) return true;

        if (!isEqual(assignedBdcAdvisor.toSorted(), assignedBdcAdvisorValue.toSorted()))
            return true;

        return false;
    }, [usageType, usageTypeValue, assignedBdcAdvisor, assignedBdcAdvisorValue]);

    return (
        <>
            <StyledTextField
                sx={{
                    '& .MuiInputBase-root': { cursor: 'pointer !important' },
                    '& input': { cursor: 'pointer !important' },
                }}
                ref={ref}
                onClick={handleOpen}
                readonly
                placeholder={t('afterSalesCrm.filters.filters')}
                endAdornment={
                    <InputAdornment position="end">
                        <TuneIcon />
                    </InputAdornment>
                }
                hasSelection={false}
                isFocused={false}
                disableSearch={false}
            />
            <Popover
                TransitionProps={{
                    timeout: 0,
                }}
                slots={{
                    paper: StyledPaper,
                }}
                onClose={handleClose}
                anchorEl={ref.current}
                open={open}
                anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
            >
                <CombinedFiltersHeader>
                    {t('afterSalesCrm.filters.selectFilters')}
                    <Button
                        disabled={!hasChanges}
                        sx={{ position: 'absolute', right: 8, top: 4, fontWeight: 'normal' }}
                        cmosVariant="typography"
                        onClick={handleApply}
                    >
                        {t('commonLabels.apply')}
                    </Button>
                </CombinedFiltersHeader>
                <OverlayScrollbarsComponent style={{ maxHeight: 500, minHeight: 200 }}>
                    <CombinedFiltersSubHeader>
                        {t('afterSalesCrm.columns.usageType')}
                    </CombinedFiltersSubHeader>
                    <Ul>
                        {usageTypeOptions.map(({ id, label }) => {
                            const isSelected = usageTypeValue.includes(id);

                            return (
                                <StyledListItem onClick={() => toggleUsageType(id)} key={id}>
                                    {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                                    {label}
                                </StyledListItem>
                            );
                        })}
                    </Ul>
                    <CombinedFiltersSubHeader>
                        {t('afterSalesCrm.columns.bdcAdvisor')}
                    </CombinedFiltersSubHeader>
                    <Ul>
                        {assignedBdcAdvisorOptions.map(({ id, label, subLabelText }) => {
                            const isSelected = assignedBdcAdvisorValue.includes(id);

                            return (
                                <StyledListItem
                                    onClick={() => toggleAssignedBdcAdvisor(id)}
                                    key={id}
                                >
                                    {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                                    <div>
                                        {label}
                                        {subLabelText && <SubLabel>{subLabelText}</SubLabel>}
                                    </div>
                                </StyledListItem>
                            );
                        })}
                    </Ul>
                </OverlayScrollbarsComponent>
            </Popover>
        </>
    );

    function handleClose() {
        setOpen(false);
    }

    function handleOpen() {
        setOpen(true);
        setUsageTypeValue(usageType);
        setAssignedBdcAdvisorValue(assignedBdcAdvisor);
    }

    function toggleUsageType(usageType: UsageType) {
        setUsageTypeValue((v) =>
            v.includes(usageType) ? v.filter((x) => x !== usageType) : [...v, usageType]
        );
    }

    function toggleAssignedBdcAdvisor(bdcAdvisorId: string) {
        setAssignedBdcAdvisorValue((v) =>
            v.includes(bdcAdvisorId) ? v.filter((x) => x !== bdcAdvisorId) : [...v, bdcAdvisorId]
        );
    }

    function handleApply() {
        if (!hasChanges) return;
        const usageTypeNew = usageTypeValue.toSorted();
        const assignedBdcAdvisorNew = assignedBdcAdvisorValue.toSorted();
        onChange(usageTypeNew, assignedBdcAdvisorNew);
        setOpen(false); // closing and reopening also resets internal state
    }
}

const Ul = styled('ul')({ margin: 0, padding: 0 });

const StyledPaper = styled(Paper)({
    borderRadius: 16,
    backgroundColor: 'var(--neutral2)',
    position: 'fixed',
    width: 250,
    maxWidth: 300,
    overflow: 'hidden',
});

const CombinedFiltersHeader = styled('div')({
    backgroundColor: 'var(--neutral3)',
    padding: '12px 15px',
    color: 'var(--neutral8)',
    borderRadius: '16px 16px 0 0',
});

const CombinedFiltersSubHeader = styled('div')({
    fontWeight: '600',
    margin: '5px 0 5px 16px',
});

const StyledListItem = styled(SMenuItem)({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    height: 42,
    color: 'var(--neutral8)',
    position: 'relative',

    '&::after': {
        content: '" "',
        display: 'block',
        bottom: 0,
        right: 16,
        left: 16,
        height: 1,
        backgroundColor: 'var(--neutral3)',
        position: 'absolute',
    },

    '&:last-child::after': {
        display: 'none',
    },

    '&:hover': {
        backgroundColor: '#e1ecfb',
        color: 'var(--cm1)',
    },
});

const SubLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h11Inter,
    color: 'var(--neutral6)',
    fontSize: '9px',
    display: 'block',
}));
