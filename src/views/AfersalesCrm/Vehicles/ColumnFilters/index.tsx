import { I<PERSON><PERSON>utton, Menu, MenuItem, styled } from '@mui/material';
import Box from '@mui/material/Box';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { vehiclesFilterKeyMap } from 'store/slices/vehicleDatabase';
import {
    selectVehiclesColumns,
    selectVehiclesFilters,
    vehiclesActions,
    VehiclesColumn,
    VehiclesColumnKey,
} from 'store/slices/vehicles';

export default function ColumnFilters() {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const dispatch = useAppDispatch();
    const filters = useSelector(selectVehiclesFilters);

    const columns = useAppSelector(selectVehiclesColumns);

    const { t } = useAppTranslation();

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.target as HTMLElement);
    };

    const isAllSelected = useMemo(() => columns.every((x) => x.selected), [columns]);

    const toggle = useCallback(
        (key: VehiclesColumnKey) => {
            const paramKey = vehiclesFilterKeyMap[
                key as keyof typeof vehiclesFilterKeyMap
            ] as string;

            if (isAllSelected) {
                columns
                    .filter((x) => x.key !== key)
                    .forEach((x) => {
                        dispatch(vehiclesActions.toggleColumnSelection(x.key));
                    });
            } else {
                dispatch(vehiclesActions.toggleColumnSelection(key));
                if (paramKey) {
                    dispatch(vehiclesActions.setFilters({ ...filters, [paramKey]: [] }));
                }
            }
        },
        [dispatch, filters, isAllSelected, columns]
    );

    const handleAllColumnsToggle = () => {
        if (isAllSelected) {
            return;
        } else {
            columns.forEach((x) => {
                if (!x.selected) {
                    dispatch(vehiclesActions.toggleColumnSelection(x.key));
                }
            });
        }
    };

    const shouldShowColumnChecked = (column: VehiclesColumn) => {
        if (isAllSelected) {
            return false;
        }
        return column.selected;
    };

    return (
        <>
            <IconButton size="small" onClick={handleClick}>
                <FiltersIcon fill={anchorEl ? 'var(--cm1)' : 'currentColor'} />
            </IconButton>
            <ColumnsMenu
                anchorOrigin={{
                    horizontal: 'right',
                    vertical: 'bottom',
                }}
                transformOrigin={{
                    horizontal: 'right',
                    vertical: 'top',
                }}
                onClose={() => setAnchorEl(null)}
                open={!!anchorEl}
                anchorEl={anchorEl}
            >
                <div>
                    <StickyHeader>{t('afterSalesCrm.filterMenu.columnsTitle')}</StickyHeader>

                    <StyledMenuItem onClick={handleAllColumnsToggle}>
                        {isAllSelected ? (
                            <CheckBoxIcon fill="var(--cm1)" />
                        ) : (
                            <UncheckBoxIcon fill="var(--cm1)" />
                        )}
                        <Box sx={{ marginLeft: '5px' }}>
                            {t('afterSalesCrm.filterMenu.allColumns')}
                        </Box>
                    </StyledMenuItem>
                    {columns.map((column) => (
                        <StyledMenuItem key={column.key} onClick={() => toggle(column.key)}>
                            {shouldShowColumnChecked(column) ? (
                                <CheckBoxIcon fill="var(--cm1)" />
                            ) : (
                                <UncheckBoxIcon fill="var(--cm1)" />
                            )}
                            <Box sx={{ marginLeft: '5px' }}>
                                {t(`afterSalesCrm.columns.${column.name}`)}
                            </Box>
                        </StyledMenuItem>
                    ))}
                </div>
            </ColumnsMenu>
        </>
    );
}

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    color: 'var(--neutral8)',
    '&:hover': {
        color: 'var(--cm1)',
    },
}));

const StickyHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    position: 'sticky',
    top: 0,
    backgroundColor: '#F3F3F3',
    padding: '8px 16px',
    color: 'var(--neutral8)',
    zIndex: 1,
}));

const ColumnsMenu = styled(Menu)({
    '& > .MuiPaper-root': {
        borderRadius: 10,
        backgroundColor: 'var(--neutral2)',
    },
    '& .MuiMenu-list': {
        minWidth: 200,
        maxWidth: 300,
        maxHeight: 'min(80vh, 360px)',
        ...scrollbarStyle(),
        overflowY: 'auto',
        overflowX: 'hidden',

        '& .MuiMenuItem-root:not(:last-child)': {
            '&::after': {
                content: '""',
                display: 'block',
                width: '90%',
                margin: '0 auto',
                borderBottom: '1px solid var(--neutral3)',
                position: 'absolute',
                bottom: 0,
            },
        },
    },
    '& .MuiList-root': {
        padding: 0,
    },
});
