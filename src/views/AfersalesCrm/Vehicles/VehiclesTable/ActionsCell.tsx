import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

import { VehiclesData } from 'api/customers';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { CustomerNoteIcon } from 'common/components/Icons/CustomerNoteIcon';
import { WhatsappIcon } from 'common/components/Icons/WhatsappIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import ActionButton from './ActionButton';
import ActionWrapper from './ActionWrapper';
import AppointmentButton from './AppointmentButton';
import ButtonText from './ButtonText';
import CallButton from './CallButton';

export type ActionsCellProps = {
    row: VehiclesData;
};

export const ActionsCell = ({ row }: ActionsCellProps) => {
    const { t } = useAppTranslation();

    const settings = useAppSelector(selectSettings);

    const {
        phoneCalls = false,
        whatsAppChat = false,
        sendWhatsappToConsumer = false,
    } = settings.repairShopSettings?.features ?? {};
    const showWhatsAppButton = whatsAppChat && sendWhatsappToConsumer;
    const showCallButton = phoneCalls;

    return (
        <Container>
            {showCallButton && <CallButton contacts={row.contacts} vehicleId={row.id} />}

            {showWhatsAppButton && (
                <ActionWrapper>
                    <ArrowTooltip
                        position="top"
                        content={t('afterSalesCrm.columns.whatsappTooltip')}
                    >
                        <ActionButton
                            disabled={!row.contacts.some((x) => !!x.phone)}
                            onClick={() => {
                                alert('Work in progress\nTrabajo en progreso');
                            }}
                        >
                            <WhatsappIcon size={24} fill="currentColor" />
                        </ActionButton>
                    </ArrowTooltip>
                    <ButtonText>{t('afterSalesCrm.columns.whatsapp')}</ButtonText>
                </ActionWrapper>
            )}

            <ActionWrapper>
                <ArrowTooltip position="top" content={t('afterSalesCrm.columns.noteTooltip')}>
                    <ActionButton
                        onClick={() => {
                            alert('Work in progress\nTrabajo en progreso');
                        }}
                    >
                        <CustomerNoteIcon size={24} fill="currentColor" />
                    </ActionButton>
                </ArrowTooltip>
                <ButtonText>{t('afterSalesCrm.columns.note')}</ButtonText>
            </ActionWrapper>

            <ActionWrapper>
                <ArrowTooltip position="top" content={t('afterSalesCrm.columns.taskTooltip')}>
                    <ActionButton
                        onClick={() => {
                            alert('Work in progress\nTrabajo en progreso');
                        }}
                    >
                        <CalendarIcon size={24} fill="currentColor" />
                    </ActionButton>
                </ArrowTooltip>
                <ButtonText>{t('afterSalesCrm.columns.task')}</ButtonText>
            </ActionWrapper>

            <AppointmentButton contacts={row.contacts} vehicleId={row.id} />
        </Container>
    );
};

const Container = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    gap: '10px 16px',
});

export default ActionsCell;
