import {
    Grid,
    styled,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@mui/material';
import Box from '@mui/material/Box';
import {
    Activity,
    ContactInfo,
    PriorityInfo,
    ServiceInfo,
    VehicleRowData,
    VehiclesData,
    VehiclesDataResponse,
} from 'api/customers';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { NoResultIcon } from 'common/components/Icons/NoResultIcon';
import PaperWithOverlayScrollbars from 'common/components/PaperWithOverlayScrollbars';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment/moment';
import React, { forwardRef } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectVehicleParams } from 'store/slices/vehicleDetails';
import {
    selectVehiclesColumns,
    selectVehiclesFilters,
    vehiclesActions,
} from 'store/slices/vehicles';
import TableHeadSelectFilter from '../../TableHeadSelectFilter';
import ActionsCell from './ActionsCell';
import ActivityCell from './ActivityCell';
import ContactCell from './ContactCell';
import NextActivityCell from './NextActivityCell';
import ProspectionPriorityCell from './ProspectionPriorityCell';
import ServiceCell from './ServiceCell';
import VehicleCell from './VehicleCell';
import ViewDetailsButton from './ViewDetailsButton';

type VehiclesTableProps = {
    vehiclesData?: VehiclesDataResponse;
};

export const VehiclesTable = React.memo(function VehiclesTable({
    vehiclesData,
}: VehiclesTableProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    const vehicleParams = useAppSelector(selectVehicleParams);

    const columns = useAppSelector(selectVehiclesColumns).filter((c) => c.selected);

    const local = useAppSelector((state) => selectSettings(state).internationalization).language;

    const filters = useSelector(selectVehiclesFilters);

    const getProspectionPrioritiesOptions = () => {
        return (
            vehicleParams?.prospectionPriorities.map((p) => ({
                id: p.id,
                label: p.name,
            })) ?? null
        );
    };

    function setFilters(selected: (string | boolean | number)[]) {
        const filterValue = selected as string[];
        dispatch(
            vehiclesActions.setFilters({
                ...filters,
                prospectionPriority: filterValue,
            })
        );
    }

    const formatDate = (date: string | null) => {
        if (!date) return '-';

        const momentDate = moment(date);
        if (local === 'es') {
            const month = momentDate.format('MMM').toLowerCase();
            return `${momentDate.format('D')} ${month} ${momentDate.format('YYYY')}`;
        }
        return momentDate.format('MMM D, YYYY');
    };

    return (
        <TableContainer
            component={PaperWithOverlayScrollbars}
            sx={{
                height: '75vh',
                '@media (max-height: 600px)': {
                    height: '63vh',
                },
                '@media (min-height: 601px) and (max-height: 800px)': {
                    height: '70vh',
                },
                '@media (min-height: 801px) and (max-height: 900px)': {
                    height: '75vh',
                },
                '@media (min-height: 901px)': {
                    height: '79vh',
                },
                // ...scrollbarStyle({ size: 10 }),
                borderCollapse: 'collapse',
            }}
        >
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        {columns.map((column) =>
                            !column.isFiltered ? (
                                <HeaderCell key={column.key} width={column.width}>
                                    {t(`afterSalesCrm.columns.${column.name}`)}
                                </HeaderCell>
                            ) : (
                                <HeaderCell key={column.key} width={column.width}>
                                    <Grid
                                        container
                                        alignItems="center"
                                        justifyContent="flex-start"
                                        wrap="nowrap"
                                    >
                                        <TableHeadSelectFilter
                                            mainLabel={column.name}
                                            options={getProspectionPrioritiesOptions()}
                                            selected={filters.prospectionPriority}
                                            onSelectedChanged={(selected, _) => {
                                                setFilters(selected);
                                            }}
                                            allOptionLabel={t(
                                                'afterSalesCrm.filters.allProspectionPriority'
                                            )}
                                        />
                                    </Grid>
                                </HeaderCell>
                            )
                        )}
                    </TableRow>
                </TableHead>
                <TableBody>
                    {vehiclesData?.vehiclesListIsEmpty && (
                        <TableRow>
                            <TableCell colSpan={columns.length} sx={{ border: 'none' }}>
                                <CenteredBox>
                                    <NoResultBox gapSize={10}>
                                        <CarIcon fill={'var(--cm1)'} size={100} />
                                        <Typography variant="h4Inter">
                                            {t('afterSalesCrm.noVehiclesFoundTitle')}
                                        </Typography>
                                        <Typography
                                            variant="h5Inter"
                                            sx={{ fontWeight: 400, fontSize: '18px' }}
                                        >
                                            {t('afterSalesCrm.noVehiclesFoundText')}
                                        </Typography>
                                    </NoResultBox>
                                </CenteredBox>
                            </TableCell>
                        </TableRow>
                    )}
                    {vehiclesData?.totalCount === 0 && !vehiclesData?.vehiclesListIsEmpty && (
                        <TableRow>
                            <TableCell colSpan={columns.length} sx={{ border: 'none' }}>
                                <CenteredBox>
                                    <NoResultBox>
                                        <NoResultIcon />
                                        <Typography variant="h5Inter" sx={{ fontSize: '18px' }}>
                                            {t('afterSalesCrm.noMatchesFound')}
                                        </Typography>
                                    </NoResultBox>
                                </CenteredBox>
                            </TableCell>
                        </TableRow>
                    )}
                    {vehiclesData?.vehiclesDatabaseData.map((row, rowIndex) => (
                        <StyledTableRow key={rowIndex}>
                            {columns.map(({ key, width, dataType }, index) => {
                                const cellValue = row[key as keyof VehiclesData] ?? '-';

                                const Component =
                                    dataType === 'actions'
                                        ? VehicleTableCellSuppressClickPropagation
                                        : VehicleTableCell;

                                return (
                                    <Component key={`${rowIndex}-${key}`} width={width}>
                                        <>
                                            {(() => {
                                                if (cellValue === '-') return '-';

                                                switch (dataType) {
                                                    case 'boolean':
                                                        return cellValue
                                                            ? t('afterSalesCrm.values.Yes')
                                                            : t('afterSalesCrm.values.No');
                                                    case 'vehicle':
                                                        return (
                                                            <VehicleCell
                                                                local={local}
                                                                data={cellValue as VehicleRowData}
                                                            />
                                                        );
                                                    case 'contacts':
                                                        return (
                                                            <ContactCell
                                                                data={cellValue as ContactInfo[]}
                                                            />
                                                        );
                                                    case 'activity':
                                                        return (
                                                            <ActivityCell
                                                                local={local}
                                                                data={cellValue as Activity}
                                                            />
                                                        );
                                                    case 'nextActivity':
                                                        return (
                                                            <NextActivityCell
                                                                local={local}
                                                                date={cellValue as string | null}
                                                            />
                                                        );
                                                    case 'service':
                                                        return (
                                                            <ServiceCell
                                                                local={local}
                                                                data={cellValue as ServiceInfo}
                                                            />
                                                        );
                                                    case 'prospectionPriority':
                                                        return (
                                                            <ProspectionPriorityCell
                                                                data={cellValue as PriorityInfo}
                                                            />
                                                        );
                                                    case 'date':
                                                        return formatDate(
                                                            cellValue as string | null
                                                        );
                                                    case 'actions':
                                                        return <ActionsCell row={row} />;
                                                    default:
                                                        return cellValue === ''
                                                            ? '-'
                                                            : t(
                                                                  `afterSalesCrm.values.${String(
                                                                      cellValue
                                                                  )}`,
                                                                  {
                                                                      defaultValue:
                                                                          String(cellValue),
                                                                  }
                                                              );
                                                }
                                            })()}

                                            {!['contacts', 'service'].includes(dataType) && (
                                                <ViewDetailsButton vehicleId={row.id} />
                                            )}
                                        </>
                                    </Component>
                                );
                            })}
                        </StyledTableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );
});

const HeaderCell = styled(TableCell)(({ theme, width }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 700,
    color: 'var(--neutral7)',
    padding: '16px 16px',
    backgroundColor: 'var(--neutral2)',
    textTransform: 'uppercase',
    boxSizing: 'border-box',
    minWidth: width || 150,
    height: 50,
}));

const VehicleTableCell = styled(TableCell, {
    shouldForwardProp: (prop) => prop !== 'width',
})<{ width?: number }>(({ theme, width }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    color: 'var(--neutral8)',
    position: 'relative',
    minWidth: width || 120,
    height: 50,
    verticalAlign: 'top',
    paddingBottom: '30px',

    '& .hover-button': {
        position: 'absolute',
        width: '105px',
        bottom: '12px',
        left: '70px',
        transform: 'translateX(-50%)',
        display: 'none',
        zIndex: 1,
        borderRadius: '51px',
        backgroundColor: '#ffffff!important',
        color: '#007bff!important',
        border: '1px solid #007bff!important',
        padding: '9px 16px !important',
        fontSize: '12px',
        cursor: 'pointer',
        fontWeight: 700,
        lineHeight: '12px',
        letterSpacing: '0',
        textAlign: 'center',
        verticalAlign: 'middle',
        '&:hover': {
            backgroundColor: '#007bff!important',
            color: '#ffffff!important',
        },
    },

    '&:hover .hover-button': {
        display: 'inline-block',
    },
}));

const VehicleTableCellSuppressClickPropagation = forwardRef(
    (
        { onClick, ...props }: React.ComponentProps<typeof VehicleTableCell>,
        ref: React.ForwardedRef<HTMLTableCellElement>
    ) => {
        return <VehicleTableCell ref={ref} {...props} onClick={handleClick} />;

        function handleClick(e: React.MouseEvent<HTMLTableCellElement>) {
            e.stopPropagation();

            document.dispatchEvent(new CustomEvent('cmos:crm:vehicles:clickSuppressed'));

            if (onClick) onClick(e);
        }
    }
);

const StyledTableRow = styled(TableRow)({
    height: 50,
    '&:hover': {
        cursor: 'pointer',
        '& > td': {
            backgroundColor: 'var(--cm5)',
        },
    },
});

const CenteredBox = styled(Box)({
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 10,
    pointerEvents: 'none',
});

const NoResultBox = styled(Box)<{ gapSize?: number }>(({ gapSize = 35 }) => ({
    color: 'var(--neutral9)',
    fontWeight: 700,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: `${gapSize}px`,
}));
