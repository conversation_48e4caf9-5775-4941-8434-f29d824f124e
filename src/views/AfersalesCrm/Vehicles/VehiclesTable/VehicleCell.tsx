import { Box, Link, styled, Typography } from '@mui/material';
import { VehicleRowData } from 'api/customers';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { generatePath } from 'react-router-dom';

export type VehicleCellProps = {
    data: VehicleRowData;
    local: string;
};

export const VehicleCell = ({ data, local }: VehicleCellProps) => {
    const { t } = useAppTranslation();
    const { brand, model, year, plate, vin, mileage, usageType, lastOrder } = data;

    const handleOrderClick = (event: React.MouseEvent<HTMLAnchorElement>, orderId: string) => {
        event.stopPropagation();
        const path = generatePath('/dashboard/' + ROUTES.ORDERS_DETAIL, { id: orderId });
        window.open(path, '_blank');
    };

    const formatDate = (date: string | undefined) => {
        if (!date) return '-';

        const momentDate = moment(date);
        if (local === 'es') {
            const month = momentDate.format('MMM').toLowerCase();
            return `${momentDate.format('D')} ${month} ${momentDate.format('YYYY')}`;
        }
        return momentDate.format('MMM D, YYYY');
    };

    return (
        <Container>
            <Row>
                <Typography variant="h6">
                    {brand} {model} {year},<span style={{ fontWeight: 400 }}>{` ${plate}`}</span>
                </Typography>
            </Row>

            <Row>
                <Typography variant="h6" sx={{ fontWeight: '400' }}>
                    {vin}
                </Typography>
            </Row>

            <Row>
                <Typography variant="h8Inter">
                    {t('afterSalesCrm.vehicleDetailsCell.mileage')}
                    {': '} {mileage != null ? mileage.toLocaleString() : '-'}
                </Typography>
            </Row>

            <Row>
                <Typography variant="h8Inter">
                    {t('afterSalesCrm.vehicleDetailsCell.usageType')}
                    {': '} {usageType != null ? t(`afterSalesCrm.values.${usageType}`) : '-'}
                </Typography>
            </Row>

            <Row>
                <Typography variant="h8Inter">
                    {t('afterSalesCrm.vehicleDetailsCell.lastOrder')}
                    {': '}
                    {lastOrder?.orderId && lastOrder?.orderNumber ? (
                        <>
                            <OrderLink onClick={(e) => handleOrderClick(e, lastOrder.orderId)}>
                                {lastOrder.orderNumber}
                            </OrderLink>
                            {lastOrder.orderDate ? ` (${formatDate(lastOrder.orderDate)})` : null}
                        </>
                    ) : (
                        '-'
                    )}
                </Typography>
            </Row>
        </Container>
    );
};

const Container = styled(Box)({
    width: '200px',
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
});

const Row = styled(Box)({
    display: 'flex',
    alignItems: 'center',
});

const OrderLink = styled(Link)({
    fontWeight: 'bold',
    cursor: 'pointer',
    textDecoration: 'underline',
    color: 'var(--cm1)',
    '&:hover': {
        textDecoration: 'none',
    },
});

export default VehicleCell;
