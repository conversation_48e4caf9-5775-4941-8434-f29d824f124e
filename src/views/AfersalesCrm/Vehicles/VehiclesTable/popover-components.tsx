import { styled } from '@mui/material';

export const PopperBody = styled('div')(({ theme }) => ({
    backgroundColor: 'var(--neutral1)',
    border: '1px solid var(--neutral4)',
    boxShadow: '0 2px 6px var(--neutral4)',
    borderRadius: 4,
    minWidth: 150,
    maxWidth: 220,
    transform: 'translateY(10px)',
    position: 'relative',
    transition: 'opacity .1s',
    ...theme.typography.h8Inter,

    '@media (min-width: 1400px)': {
        fontSize: theme.typography.h7Inter.fontSize,
    },

    '&::before': {
        content: '" "',
        display: 'block',
        border: '1px solid var(--neutral4)',
        height: 8,
        width: 8,
        backgroundColor: 'var(--neutral1)',
        position: 'absolute',
        transform: 'translate(8px, -4px) rotate(45deg)',
        clipPath: 'polygon(0 0, 100% 0, 0 100%)',
        zIndex: 200,
    },

    '[data-popper-placement="bottom-end"] &': {
        transform: 'translate(5px, 10px)',

        '&::before': {
            right: 20,
        },
    },

    '[data-popper-placement="top-end"] &': { visibility: 'hidden' },
    '[data-popper-placement="top-start"] &': { visibility: 'hidden' },
    '[data-popper-reference-hidden] &': { visibility: 'hidden' },
}));

export const PopperHeader = styled('span')({
    marginLeft: 16,
    marginRight: 8,
    marginTop: 8,
    display: 'block',
    font: 'inherit',
    fontWeight: 'bold',
});

export const PopperButton = styled('button')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    border: 'none',
    backgroundColor: 'transparent',
    width: '100%',
    textAlign: 'left',
    padding: '0 8px 0 12px',
    cursor: 'pointer',
    fontWeight: 'normal',
    transition: 'background-color 0.2s',
    minHeight: '2em',
    font: 'inherit',

    ':hover': {
        // color: 'var(--cm1)',
        backgroundColor: 'var(--cm5)',
    },
}));
