import { Popper, styled } from '@mui/material';
import { ContactInfo } from 'api/customers';
import { AppointmentsMenuIcon } from 'common/components/Icons/AppointmentsMenuIcon';
import { UserIcon } from 'common/components/Icons/UserIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment/moment';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectUserPermission } from 'store/slices/user';
import ActionButton from './ActionButton';
import ActionWrapper from './ActionWrapper';
import ButtonText from './ButtonText';
import { PopperBody, PopperButton, PopperHeader } from './popover-components';

export default function AppointmentButton({
    vehicleId,
    contacts,
}: {
    vehicleId: string;
    contacts: ContactInfo[];
}) {
    const { t } = useAppTranslation();
    const buttonRef = useRef<HTMLButtonElement | null>(null);
    const menuRef = useRef<HTMLDivElement | null>(null);
    const userPermission = useSelector(selectUserPermission);

    const [open, setOpen] = useState(false);

    useEffect(() => {
        if (!open) return;

        const callback = (event: MouseEvent | Event) => {
            if (!menuRef.current) return;
            if (!(event.target instanceof Node)) return;
            if (menuRef.current.contains(event.target)) {
                return;
            }

            setOpen(false);
            document.removeEventListener('click', callback);
            document.removeEventListener('cmos:crm:vehicles:clickSuppressed', callback);
        };

        window.requestAnimationFrame(() => {
            document.addEventListener('click', callback);
            document.addEventListener('cmos:crm:vehicles:clickSuppressed', callback);
        });

        return () => {
            document.removeEventListener('click', callback);
            document.removeEventListener('cmos:crm:vehicles:clickSuppressed', callback);
        };
    }, [open]);

    function createAppointment(contact: ContactInfo) {
        const queryParams = new URLSearchParams({
            vehicleId: vehicleId,
            customerId: contact.customerId,
            mode: 'day',
            date: moment().add(1, 'day').format('YYYY-MM-DD'),
        });

        const path = `/dashboard${ROUTES.APPOINTMENTS.BASE}`;
        const fullPath = `${path}?${queryParams.toString()}`;

        window.open(fullPath, '_blank');

        setOpen(false);
    }

    function handleClick() {
        if (contacts.length === 0) return;

        if (contacts.length === 1) {
            createAppointment(contacts[0]);
        } else {
            setOpen(true);
        }
    }

    return (
        <ActionWrapper>
            <ArrowTooltip
                position="top"
                content={open ? null : t('afterSalesCrm.appointmentButton.hint')}
            >
                <ActionButton
                    disabled={contacts.length === 0 || !userPermission.allowEditAppointments}
                    onClick={(e) => {
                        e.stopPropagation();
                        handleClick();
                    }}
                    ref={buttonRef}
                >
                    <AppointmentsMenuIcon size={24} fill="currentColor" />
                </ActionButton>
            </ArrowTooltip>
            <ButtonText>{t('afterSalesCrm.columns.appt')}</ButtonText>
            <Popper
                popperOptions={{
                    placement: 'bottom-start',
                    strategy: 'fixed',
                }}
                anchorEl={buttonRef.current}
                open={open}
            >
                <PopperBody ref={menuRef} onClick={(e) => e.stopPropagation()}>
                    <ContactsUl>
                        {[...contacts]
                            .sort((a, b) => (a.type === 0 ? -1 : 1))
                            .map((contact) => (
                                <li key={contact.customerId}>
                                    <PopperHeader>
                                        {contact.type === 0
                                            ? `${t('afterSalesCrm.vehicleDetails.owner')}:`
                                            : `${t('afterSalesCrm.vehicleDetails.contact')}:`}{' '}
                                    </PopperHeader>
                                    <PopperButton
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            createAppointment(contact);
                                        }}
                                    >
                                        <UserIcon size={14} fill="var(--cm1)" />
                                        {contact.name}
                                    </PopperButton>
                                </li>
                            ))}
                    </ContactsUl>
                </PopperBody>
            </Popper>
        </ActionWrapper>
    );
}

const ContactsUl = styled('ul')({ listStyle: 'none', padding: 0, margin: '0 0 8px 0' });
