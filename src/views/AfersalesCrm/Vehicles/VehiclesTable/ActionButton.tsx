import { Button, styled } from '@mui/material';

const ActionButton = styled(Button)({
    width: '24px',
    height: '24px',
    minWidth: '24px',
    minHeight: '24px',
    borderRadius: '50%',
    backgroundColor: 'var(--neutral2)',
    border: '1px solid var(--neutral4)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    color: 'var(--neutral8)',

    '&:hover': {
        backgroundColor: 'var(--cm5)',
        borderColor: 'var(--cm1)',
        color: 'var(--cm1)',
    },

    '&:disabled': {
        color: 'var(--neutral8)',
        border: '1px solid var(--neutral4)',
        opacity: 0.5,
    },
});

export default ActionButton;
