import { Box, Typography, styled } from '@mui/material';
import { PriorityInfo } from 'api/customers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { isDark } from 'utils/colors';

export type ProspectionPriorityCellProps = {
    data: PriorityInfo;
};

export const ProspectionPriorityCell = ({ data }: ProspectionPriorityCellProps) => {
    const { t } = useAppTranslation();
    const { priority, monthsSinceSold, monthsSinceServiced, recommendedServiceFrequency } = data;

    return (
        <Container>
            <PriorityContainer>
                {priority ? (
                    <PriorityBadge color={priority.color}>{priority.name}</PriorityBadge>
                ) : (
                    <PriorityBadge color="#899198">
                        {t('afterSalesCrm.values.noPriority')}
                    </PriorityBadge>
                )}
            </PriorityContainer>

            <InfoContainer>
                <Row>
                    <Value variant="h8Inter">
                        {' '}
                        {t('afterSalesCrm.columns.monthsSinceSold', {
                            months: monthsSinceSold ?? '-',
                        })}
                    </Value>
                </Row>

                <Row>
                    <Value variant="h8Inter">
                        {' '}
                        {t('afterSalesCrm.columns.monthsSinceServiced', {
                            months: monthsSinceServiced ?? '-',
                        })}
                    </Value>
                </Row>

                <Row>
                    <Value variant="h8Inter">{recommendedServiceFrequency}</Value>
                </Row>
            </InfoContainer>
        </Container>
    );
};

const Container = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    width: '152px',
});

const PriorityContainer = styled(Box)({
    display: 'flex',
    justifyContent: 'flex-start',
});

const PriorityBadge = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'color',
})<{ color: string }>(({ color }) => ({
    display: 'inline-block',
    padding: '4px 8px',
    borderRadius: '4px',
    backgroundColor: color,
    color: isDark(color, 0.7) ? '#fff' : 'var(--neutral8)',
    fontWeight: 'bold',
    fontSize: '12px',
}));

const InfoContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    gap: '3px',
});

const Row = styled(Box)({
    display: 'flex',
    alignItems: 'flex-start',
    gap: '8px',
});

const Value = styled(Typography)({
    fontWeight: 400,
});

export default ProspectionPriorityCell;
