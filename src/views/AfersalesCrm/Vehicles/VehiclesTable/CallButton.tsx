import { Popper, styled } from '@mui/material';
import { ContactInfo } from 'api/customers';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { numberPhoneFormatter } from 'common/FormatersHelper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useCallOverlay } from '../CallOverlay';
import ActionButton from './ActionButton';
import ActionWrapper from './ActionWrapper';
import ButtonText from './ButtonText';
import { PopperBody, PopperButton, PopperHeader } from './popover-components';

export default function CallButton({
    vehicleId,
    contacts,
}: {
    vehicleId: string;
    contacts: ContactInfo[];
}) {
    const { t } = useAppTranslation();
    const buttonRef = useRef<HTMLButtonElement | null>(null);
    const menuRef = useRef<HTMLDivElement | null>(null);
    const { internationalization } = useAppSelector(selectSettings);

    const [open, setOpen] = useState(false);
    const callOverlay = useCallOverlay();

    useEffect(() => {
        if (!open) return;

        const callback = (event: MouseEvent | Event) => {
            if (!menuRef.current) return;
            if (!(event.target instanceof Node)) return;
            if (menuRef.current.contains(event.target)) {
                return;
            }

            setOpen(false);
            document.removeEventListener('click', callback);
            document.removeEventListener('cmos:crm:vehicles:clickSuppressed', callback);
        };

        window.requestAnimationFrame(() => {
            document.addEventListener('click', callback);
            document.addEventListener('cmos:crm:vehicles:clickSuppressed', callback);
        });

        return () => {
            document.removeEventListener('click', callback);
            document.removeEventListener('cmos:crm:vehicles:clickSuppressed', callback);
        };
    }, [open]);

    return (
        <ActionWrapper>
            <ArrowTooltip position="top" content={open ? null : t('afterSalesCrm.callButton.hint')}>
                <ActionButton
                    disabled={contacts.length === 0 || !callOverlay.canCall}
                    onClick={handleClick}
                    ref={buttonRef}
                >
                    <PhoneIcon size={24} fill="currentColor" />
                </ActionButton>
            </ArrowTooltip>
            <ButtonText>{t('afterSalesCrm.columns.call')}</ButtonText>
            <Popper
                popperOptions={{
                    placement: 'bottom-start',
                    strategy: 'fixed',
                }}
                anchorEl={buttonRef.current}
                open={open}
            >
                <PopperBody onClick={(e) => e.stopPropagation()} ref={menuRef}>
                    <ContactsUl>
                        {contacts
                            .filter((x) => x.phone !== null)
                            .map((contact) => (
                                <li key={contact.phone}>
                                    <PopperHeader>{contact.name}</PopperHeader>
                                    <PopperButton onClick={() => callContact(contact)}>
                                        <PhoneIcon size={16} />
                                        {t('afterSalesCrm.callButton.call', {
                                            number: numberPhoneFormatter(
                                                internationalization.phoneNumberFormat,
                                                contact.phone!
                                            ),
                                        })}
                                    </PopperButton>
                                </li>
                            ))}
                    </ContactsUl>
                </PopperBody>
            </Popper>
        </ActionWrapper>
    );

    function callContact(contact: ContactInfo) {
        if (!callOverlay.canCall || !contact.phone) return;
        callOverlay.call({
            callTo: contact.phone,
            vehicleId,
        });
        setOpen(false);
    }

    function handleClick() {
        if (contacts.filter((x) => x.phone).length === 1) {
            const contact = contacts.find((x) => !!x.phone)!;
            callContact(contact);
        } else {
            setOpen(true);
        }
    }
}

const ContactsUl = styled('ul')({ listStyle: 'none', padding: 0, margin: '0 0 8px 0' });
