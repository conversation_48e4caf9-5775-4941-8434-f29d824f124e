import { Box, Typography, styled } from '@mui/material';
import { ServiceInfo } from 'api/customers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';

export type ServiceCellProps = {
    data: ServiceInfo;
    local: string;
};

export const ServiceCell = ({ data, local }: ServiceCellProps) => {
    const { t } = useAppTranslation();
    const { lastAtMyDealership, lastAtOtherDealership, nextService } = data;

    const formatDate = (date: string | null) => {
        if (!date) return '-';

        const momentDate = moment(date);
        if (local === 'es') {
            const month = momentDate.format('MMM').toLowerCase();
            return `${momentDate.format('D')} ${month} ${momentDate.format('YYYY')}`;
        }
        return momentDate.format('MMM D, YYYY');
    };
    return (
        <Container>
            <Row>
                <Label variant="h6Inter">{t('afterSalesCrm.columns.lastAtMyDealership')}:</Label>
                <Value variant="h8Inter">{formatDate(lastAtMyDealership)}</Value>
            </Row>

            <Row>
                <Label variant="h6Inter">{t('afterSalesCrm.columns.lastAtOtherDealership')}:</Label>
                <Value variant="h8Inter">{formatDate(lastAtOtherDealership)}</Value>
            </Row>

            <Row>
                <Label variant="h6Inter">{t('afterSalesCrm.columns.nextService')}:</Label>
                <Value variant="h8Inter">{formatDate(nextService)}</Value>
            </Row>
        </Container>
    );
};

const Container = styled(Box)({
    width: '152px',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
});

const Row = styled(Box)({
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: '3px',
});

const Label = styled(Typography)({
    fontWeight: 400,
});

const Value = styled(Typography)({
    fontWeight: 400,
});

export default ServiceCell;
