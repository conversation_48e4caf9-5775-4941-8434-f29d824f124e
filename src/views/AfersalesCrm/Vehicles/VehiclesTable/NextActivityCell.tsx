import { Box, Typography, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment/moment';

export type NextActivityCellProps = {
    date: string | null;
    local: string;
};

export const NextActivityCell = ({ date, local }: NextActivityCellProps) => {
    const { t } = useAppTranslation();

    const formatDate = (date: string | null) => {
        if (!date) return '-';

        const momentDate = moment(date);
        if (local === 'es') {
            const month = momentDate.format('MMM').toLowerCase();
            return `${momentDate.format('D')} ${month} ${momentDate.format('YYYY')}`;
        }
        return momentDate.format('MMM D, YYYY');
    };

    return (
        <Container>
            <Label variant="h6Inter">{t('afterSalesCrm.columns.nextActivity')}:</Label>
            <Value variant="h8Inter">{formatDate(date)}</Value>
        </Container>
    );
};

const Container = styled(Box)({
    width: '130px',
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
});

const Label = styled(Typography)({
    fontWeight: 400,
});

const Value = styled(Typography)({
    fontWeight: 400,
});

export default NextActivityCell;
