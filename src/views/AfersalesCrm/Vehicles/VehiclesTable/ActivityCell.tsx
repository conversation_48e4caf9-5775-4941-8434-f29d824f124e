import { Box, styled, Typography } from '@mui/material';
import { Activity } from 'api/customers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';

export type ActivityCellProps = {
    data: Activity;
    local: string;
};

export const ActivityCell = ({ data, local }: ActivityCellProps) => {
    const { t } = useAppTranslation();
    const { customerStatus, activity, lastActivityDate } = data;

    const formatDate = (date: string | null) => {
        if (!date) return '-';

        const momentDate = moment(date);
        if (local === 'es') {
            const month = momentDate.format('MMM').toLowerCase();
            return `${momentDate.format('D')} ${month} ${momentDate.format('YYYY')}`;
        }
        return momentDate.format('MMM D, YYYY');
    };

    return (
        <Container>
            <Row>
                <Label variant="h6Inter">{t('afterSalesCrm.columns.customerStatus')}:</Label>
                <Value variant="h8Inter">{customerStatus ?? '-'}</Value>
            </Row>

            <Row>
                <Label variant="h6Inter">{t('afterSalesCrm.columns.lastActivity')}:</Label>
                <Value variant="h8Inter">{activity}</Value>
                <Value variant="h8Inter">{formatDate(lastActivityDate)}</Value>
            </Row>
        </Container>
    );
};

const Container = styled(Box)({
    width: '130px',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
});

const Row = styled(Box)({
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: '3px',
});

const Label = styled(Typography)({
    fontWeight: 400,
});

const Value = styled(Typography)({
    fontWeight: 400,
});

export default ActivityCell;
