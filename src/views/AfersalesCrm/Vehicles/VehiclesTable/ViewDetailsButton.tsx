import React from 'react';
import { useNavigate } from 'react-router-dom';
import Tooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

type ViewDetailsButtonProps = {
    vehicleId: string;
};

export const ViewDetailsButton = ({ vehicleId }: ViewDetailsButtonProps) => {
    const navigate = useNavigate();
    const { t } = useAppTranslation();

    return (
        <Tooltip
            disableInteractive
            content={t('afterSalesCrm.viewDetailsButton.tooltip')}
            position="top"
            PopperProps={{
                modifiers: [
                    {
                        name: 'offset',
                        options: {
                            offset: [0, -4],
                        },
                    },
                ],
            }}
        >
            <button
                className="hover-button"
                onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/vehicle/${vehicleId}`);
                }}
            >
                {t('afterSalesCrm.viewDetailsButton.label')}
            </button>
        </Tooltip>
    );
};

export default ViewDetailsButton;
