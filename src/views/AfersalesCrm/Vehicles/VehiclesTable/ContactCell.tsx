import { Box, styled, Typography } from '@mui/material';
import { ContactInfo, ContactType } from 'api/customers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';

export type ContactCellProps = {
    data: ContactInfo[];
};

export const ContactCell = ({ data }: ContactCellProps) => {
    const { t } = useAppTranslation();

    const owner = useMemo(() => data.find((contact) => contact.type === ContactType.Owner), [data]);
    const contact = useMemo(
        () => data.find((contact) => contact.type === ContactType.Contact),
        [data]
    );

    return (
        <Container>
            {/* Owner Info */}
            <ContactRow>
                <StyledLabel variant="h6Inter">
                    {`${t('afterSalesCrm.columns.owner')}: ${owner?.name ?? '-'}`}
                </StyledLabel>
                {owner && (
                    <ContactDetails>
                        {owner.phone && (
                            <div>{`${t('afterSalesCrm.columns.phone')}: ${owner.phone}`}</div>
                        )}
                        {owner.email && (
                            <div>{`${t('afterSalesCrm.columns.email')}: ${owner.email}`}</div>
                        )}
                    </ContactDetails>
                )}
            </ContactRow>

            {/* Contact Info */}
            <ContactRow>
                <StyledLabel variant="h6Inter">
                    {`${t('afterSalesCrm.columns.contact')}: ${contact?.name ?? '-'}`}
                </StyledLabel>
                {contact && (
                    <ContactDetails>
                        {contact.phone && (
                            <div>{`${t('afterSalesCrm.columns.phone')}: ${contact.phone}`}</div>
                        )}
                        {contact.email && (
                            <div>{`${t('afterSalesCrm.columns.email')}: ${contact.email}`}</div>
                        )}
                    </ContactDetails>
                )}
            </ContactRow>
        </Container>
    );
};

const Container = styled(Box)({
    width: '200px',
    display: 'flex',
    flexDirection: 'column',
    gap: '25px',
});

const ContactRow = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
});

const ContactDetails = styled(Box)(({ theme }) => ({
    ...theme.typography.h8Inter,
    color: 'var(--neutral8)',
    display: 'flex',
    flexDirection: 'column',
    gap: '2px',
}));

const StyledLabel = styled(Typography)({
    fontWeight: 400,
    color: 'var(--neutral8)',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});

export default ContactCell;
