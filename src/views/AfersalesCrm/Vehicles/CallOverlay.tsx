import { Divider, IconButton, keyframes, styled, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { getErrorMessage } from 'api/error';
import PhoneCallAPI, { PhoneCallRequest } from 'api/PhoneCall';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useStore } from 'react-redux';
import { Store } from 'redux';
import { RootState } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectUser } from 'store/slices/user';

interface CallOverlayController {
    call(req: PhoneCallRequest): Promise<void>;
    get canCall(): boolean;
}

const CallOverlayContext = createContext<CallOverlayController | null>(null);

export function useCallOverlay() {
    const controller = useContext(CallOverlayContext);

    if (!controller) throw new Error('cannot find CallOverlayContext');

    return controller;
}

class CallOverlayControllerImpl implements CallOverlayController {
    private readonly _store: Store;
    private readonly _call: (req: PhoneCallRequest) => Promise<void>;

    constructor(store: Store, call: (req: PhoneCallRequest) => Promise<void>) {
        this._store = store;
        this._call = call;
    }

    get canCall(): boolean {
        const rootState = this._store.getState() as RootState;
        const shopSettings = selectSettings(rootState);
        if (shopSettings.appMode === 'Enterprise') {
            // not supported yet
            return false;
        } else {
            if (!shopSettings.repairShopSettings!.features.phoneCalls) {
                return false;
            }
        }

        const user = selectUser(rootState);
        if (!user) return false;

        if (!user.canPerformCalls) return false;

        return true;
    }

    async call(req: PhoneCallRequest): Promise<void> {
        if (!this.canCall) throw new Error('current user cannot call');

        await this._call(req);
    }
}

export default function CallOverlay({ children }: React.PropsWithChildren) {
    const { t } = useAppTranslation();

    const [request, setRequest] = useState<PhoneCallRequest | null>(null);

    const callMutation = useMutation({
        mutationFn: async (req: PhoneCallRequest) => {
            setRequest(req);
            const delayPromise = new Promise((resolve) => setTimeout(resolve, 2500));

            // note: calling might take a second or 2, to avoid users being
            // annoyed that they are getting no call we are going to display
            // loader for a few seconds more
            await Promise.all([delayPromise, PhoneCallAPI.makeCall(req)]);
        },
    });
    const callMutationRef = useRef(callMutation);
    callMutationRef.current = callMutation;

    const store = useStore();

    const controller = useMemo(
        () =>
            new CallOverlayControllerImpl(store, (req) => callMutationRef.current.mutateAsync(req)),
        [store]
    );

    useEffect(() => {
        if (!request) {
            callMutation.reset();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [request]);

    return (
        <>
            <CallOverlayContext.Provider value={controller}>{children}</CallOverlayContext.Provider>
            <Modal open={!!request}>
                <Container>
                    <IconWrapper
                        className={clsx({
                            calling: callMutation.isLoading,
                        })}
                    >
                        <PhoneIcon
                            size={32}
                            fill={callMutation.error ? 'var(--danger)' : 'var(--success)'}
                        />
                    </IconWrapper>
                    <Typography variant="h4Inter">{t('orders.calls.dialing')}</Typography>
                    <Divider sx={{ width: '100%' }} />
                    <Typography sx={{ color: 'var(--neutral7)' }}>
                        {callMutation.error ? (
                            <>
                                <strong>ERROR:</strong> {getErrorMessage(callMutation.error)}
                            </>
                        ) : (
                            t('orders.calls.yourPhoneWillRing')
                        )}
                    </Typography>
                    <Button
                        showLoader={callMutation.isLoading}
                        disabled={callMutation.isLoading}
                        onClick={handleCloseClick}
                        sx={{ width: '100%' }}
                        color="success"
                    >
                        {callMutation.isLoading ? '' : t('commonLabels.ok')}
                    </Button>

                    <IconButton
                        onClick={handleCloseClick}
                        sx={{ position: 'absolute', top: 12, right: 12 }}
                    >
                        <CloseIcon />
                    </IconButton>
                </Container>
            </Modal>
        </>
    );

    function handleCloseClick() {
        setRequest(null);
    }
}

const Container = styled('div')({
    padding: 30,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 15,
    width: 280,
    position: 'relative',
});

const pulseAnimation = keyframes({
    '0%': {
        boxShadow: '0 0 4px var(--success)',
    },
    '50%': {
        boxShadow: '0 0 20px var(--success)',
    },
    '100%': {
        boxShadow: '0 0 4px var(--success)',
    },
});

const IconWrapper = styled('div')({
    position: 'relative',
    padding: 8,
    display: 'inline-flex',
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    width: 48,
    border: '1px solid var(--neutral3)',

    '&.calling': {
        '&::after': {
            borderColor: 'transparent',
            inset: 0,
            borderRadius: 100,
            content: '" "',
            display: 'block',
            position: 'absolute',
            animation: `${pulseAnimation} 1s ease-in-out infinite`,
        },
    },
});
