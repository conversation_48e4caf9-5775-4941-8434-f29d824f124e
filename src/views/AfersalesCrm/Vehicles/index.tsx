import { Pagination, Typography } from '@mui/material';
import { CSSProperties, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { WidePageLayout } from '../../Components/Page';
import ColumnFilters from './ColumnFilters';

import useURLSearchParams from 'common/hooks/useURLSearchParams';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';

import Box from '@mui/material/Box';
import { useQuery } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useSelector } from 'react-redux';
import { CustomHeaderContent, useHeaderLoading } from '../../HeaderBar';

import { VehiclesCrmApi, VehiclesPageParams } from 'api/customers';
import { vehicleDetailsActions } from 'store/slices/vehicleDetails';
import { selectVehiclesFilters, vehiclesActions } from 'store/slices/vehicles';
import CallOverlay from './CallOverlay';
import Header from './Header';
import { VehiclesTable } from './VehiclesTable';

export default function Vehicles() {
    const [pageParams, setPageParams] = usePageParams();
    const filters = useSelector(selectVehiclesFilters);
    const { t } = useAppTranslation();
    useDocumentTitle(t('titles.vehicles'));
    const pageSize = 20;
    const [isLoading, setLoading] = useState(false);
    useHeaderLoading(isLoading);

    const { data, isInitialLoading, isPreviousData, isFetching } = useQuery(
        ['vehicles', filters, pageParams.pageIdx, pageSize],
        async () => {
            const requestData = {
                ...filters,
                pageIdx: pageParams.pageIdx - 1,
            } as VehiclesPageParams;
            return await VehiclesCrmApi.getVehiclesData(requestData);
        },
        {
            cacheTime: 1000,
            keepPreviousData: true,
        }
    );

    useEffect(() => {
        setLoading(isInitialLoading || (isPreviousData && isFetching));
    }, [isInitialLoading, isPreviousData, isFetching]);

    useEffect(() => {
        if (data?.totalPages && pageParams.pageIdx > data.totalPages) {
            setPageParams({ ...pageParams, pageIdx: 1 });
        }
    }, [data?.totalPages, pageParams, setPageParams]);

    return (
        <CallOverlay>
            <CustomHeaderContent>
                <Header />
            </CustomHeaderContent>
            <VehicleDetailsFetcher />
            <WidePageLayout style={{ '--margin-top': '22px' } as CSSProperties}>
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        marginBottom: '16px',
                        alignItems: 'center',
                    }}
                >
                    <Box>
                        <Typography variant="h6Inter" sx={{ color: 'var(--neutral8)' }}>
                            {t('afterSalesCrm.totalVehiclesResult')}
                        </Typography>
                        <Typography variant="h7Inter" sx={{ color: 'var(--neutral8)' }}>
                            {(data?.totalCount ?? 0) + t('afterSalesCrm.totalVehicles')}
                        </Typography>
                    </Box>
                    <ColumnFilters />
                </Box>

                <VehiclesTable vehiclesData={data} />
                <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: '16px' }}>
                    <Pagination
                        count={data?.totalPages}
                        page={pageParams.pageIdx}
                        onChange={(_, i) => setPageParams({ ...pageParams, pageIdx: i })}
                        color="primary"
                        size={'small'}
                    />
                </Box>
            </WidePageLayout>
        </CallOverlay>
    );
}

function usePageParams(): [VehiclesPageParams, setParams: (params: VehiclesPageParams) => void] {
    const dispatch = useAppDispatch();
    const persistentParams = useAppSelector(selectVehiclesFilters);
    const navigate = useNavigate();
    const urlParams = useURLSearchParams();
    const page = parsePageParams(urlParams.get('page'));
    const params: VehiclesPageParams = useMemo(
        () => ({ ...persistentParams, pageIdx: page }),
        [persistentParams, page]
    );

    const initRef = useRef(false);
    useEffect(() => {
        if (initRef.current) return;

        initRef.current = true;

        const newUrlParams = new URLSearchParams(urlParams);

        if (newUrlParams.toString() !== urlParams.toString()) {
            navigate(
                { search: newUrlParams.size > 0 ? `?${newUrlParams.toString()}` : undefined },
                { replace: true }
            );
        }
    }, [urlParams, params, navigate]);

    const setParams = useCallback(
        ({ pageIdx, ...params }: VehiclesPageParams) => {
            dispatch(vehiclesActions.setFilters(params));

            const newUrlParams = new URLSearchParams();

            if (pageIdx && pageIdx > 1 && Number.isInteger(pageIdx)) {
                newUrlParams.set('page', pageIdx.toString());
            }

            navigate(
                {
                    search: newUrlParams.size > 0 ? `?${newUrlParams.toString()}` : undefined,
                },
                {
                    replace: true,
                }
            );
        },
        [dispatch, navigate]
    );

    return [params, setParams];
}

function parsePageParams(v: string | null): number {
    if (v === null) return 1;

    const page = +v;

    if (Number.isNaN(v)) {
        return 1;
    }

    if (!Number.isInteger(page) || page < 1) return 1;

    return page;
}

function VehicleDetailsFetcher() {
    const dispatch = useAppDispatch();

    const { data: vehicleParams } = useQuery({
        queryKey: ['crm', 'vehicle-params'],
        cacheTime: Infinity,
        staleTime: 500,
        queryFn: VehiclesCrmApi.getAvailableVehicleParams,
    });

    useEffect(() => {
        if (vehicleParams) {
            dispatch(vehicleDetailsActions.setVehicleParams(vehicleParams));
        }
    }, [dispatch, vehicleParams]);

    return null;
}
