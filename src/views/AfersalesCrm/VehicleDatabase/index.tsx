import { Pagination } from '@mui/material';
import useURLSearchParams from 'common/hooks/useURLSearchParams';
import { CSSProperties, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { selectVehicleDatabaseFilters, vehicleDatabaseActions } from 'store/slices/vehicleDatabase';
import { WidePageLayout } from '../../Components/Page';
import ColumnFilters from './ColumnFilters';

import Box from '@mui/material/Box';
import { useQuery } from '@tanstack/react-query';
import {
    RecommendedServiceFrequency,
    VehicleDatabasePageParams,
    VehiclesCrmApi,
} from 'api/customers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useSelector } from 'react-redux';
import { selectVehicleParams, vehicleDetailsActions } from 'store/slices/vehicleDetails';
import { CustomHeaderContent, useHeaderLoading } from '../../HeaderBar';
import Header from './Header';
import VehicleDatabaseTable from './VehicleDatabaseTable';

export default function VehicleDatabase() {
    const [pageParams, setPageParams] = usePageParams();
    const filters = useSelector(selectVehicleDatabaseFilters);
    const { t } = useAppTranslation();
    useDocumentTitle(t('titles.vehicleDatabase'));
    const [isLoading, setLoading] = useState(false);
    useHeaderLoading(isLoading);
    const vehicleParams = useAppSelector(selectVehicleParams);

    const { data, isInitialLoading, isPreviousData, isFetching } = useQuery(
        ['vehicleDatabase', filters, pageParams.pageIdx, vehicleParams],
        async () => {
            if (!vehicleParams) return null;

            const requestData: VehicleDatabasePageParams = {
                ...filters,
                pageIdx: pageParams.pageIdx - 1,
            };

            const recommendedFrequencyOptions: RecommendedServiceFrequency[] =
                filters.recommendedFrequency.flatMap(
                    (km) =>
                        vehicleParams?.recommendedServiceFrequencyOptions.find(
                            (opt) => opt.km === Number(km)
                        ) ?? []
                );

            return VehiclesCrmApi.getVehicleDatabaseData(requestData, recommendedFrequencyOptions);
        },
        {
            cacheTime: 1000,
            keepPreviousData: true,
        }
    );

    useEffect(() => {
        setLoading(isInitialLoading || (isPreviousData && isFetching));
    }, [isInitialLoading, isPreviousData, isFetching]);

    useEffect(() => {
        if (data?.totalPages && pageParams.pageIdx > data.totalPages) {
            setPageParams({ ...pageParams, pageIdx: 1 });
        }
    }, [data?.totalPages, pageParams, setPageParams]);

    return (
        <>
            <CustomHeaderContent>
                <Header />
            </CustomHeaderContent>
            <VehicleDetailsFetcher />
            <WidePageLayout style={{ '--margin-top': '22px' } as CSSProperties}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
                    <ColumnFilters />
                </Box>

                <VehicleDatabaseTable vehicleDatabaseData={data || undefined} />
                <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: '16px' }}>
                    <Pagination
                        count={data?.totalPages}
                        page={pageParams.pageIdx}
                        onChange={(_, i) => setPageParams({ ...pageParams, pageIdx: i })}
                        color="primary"
                        size={'small'}
                    />
                </Box>
            </WidePageLayout>
        </>
    );
}

function usePageParams(): [
    VehicleDatabasePageParams,
    setParams: (params: VehicleDatabasePageParams) => void
] {
    const dispatch = useAppDispatch();
    const persistentParams = useAppSelector(selectVehicleDatabaseFilters);
    const navigate = useNavigate();
    const urlParams = useURLSearchParams();
    const page = parsePageParams(urlParams.get('page'));
    const params: VehicleDatabasePageParams = useMemo(
        () => ({ ...persistentParams, pageIdx: page }),
        [persistentParams, page]
    ) as VehicleDatabasePageParams;

    const initRef = useRef(false);
    useEffect(() => {
        if (initRef.current) return;

        initRef.current = true;

        const newUrlParams = new URLSearchParams(urlParams);

        if (newUrlParams.toString() !== urlParams.toString()) {
            navigate(
                { search: newUrlParams.size > 0 ? `?${newUrlParams.toString()}` : undefined },
                { replace: true }
            );
        }
    }, [urlParams, params, navigate]);

    const setParams = useCallback(
        ({ pageIdx, ...params }: VehicleDatabasePageParams) => {
            dispatch(vehicleDatabaseActions.setFilters(params));

            const newUrlParams = new URLSearchParams();

            if (pageIdx && pageIdx > 1 && Number.isInteger(pageIdx)) {
                newUrlParams.set('page', pageIdx.toString());
            }

            navigate(
                {
                    search: newUrlParams.size > 0 ? `?${newUrlParams.toString()}` : undefined,
                },
                {
                    replace: true,
                }
            );
        },
        [dispatch, navigate]
    );

    return [params, setParams];
}

function parsePageParams(v: string | null): number {
    if (v === null) return 1;

    const page = +v;

    if (Number.isNaN(v)) {
        return 1;
    }

    if (!Number.isInteger(page) || page < 1) return 1;

    return page;
}

function VehicleDetailsFetcher() {
    const dispatch = useAppDispatch();

    const { data: vehicleParams } = useQuery({
        queryKey: ['crm', 'vehicle-params'],
        cacheTime: Infinity,
        staleTime: 500,
        queryFn: VehiclesCrmApi.getAvailableVehicleParams,
    });

    useEffect(() => {
        if (vehicleParams) {
            dispatch(vehicleDetailsActions.setVehicleParams(vehicleParams));
        }
    }, [dispatch, vehicleParams]);

    return null;
}
