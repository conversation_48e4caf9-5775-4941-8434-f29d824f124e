import { Grid, IconButton, Menu, MenuItem, styled, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { VehicleDatabasePageParams } from 'api/customers';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import {
    ColumnGroup,
    selectActiveFilters,
    selectAvailableVehicleDatabaseColumns,
    selectVehicleDatabaseFilters,
    vehicleDatabaseActions,
    vehicleDatabaseFilterKeyMap,
} from 'store/slices/vehicleDatabase';

const ColumnsMenu = styled(Menu)({
    '& > .MuiPaper-root': {
        borderRadius: 10,
        backgroundColor: 'var(--neutral2)',
    },
    '& .MuiMenu-list': {
        minWidth: 200,
        maxWidth: 300,
        maxHeight: 'min(80vh, 360px)',
        ...scrollbarStyle(),
        overflowY: 'auto',
        overflowX: 'hidden',

        '& .MuiMenuItem-root:not(:last-child)': {
            '&::after': {
                content: '""',
                display: 'block',
                width: '90%',
                margin: '0 auto',
                borderBottom: '1px solid var(--neutral3)',
                position: 'absolute',
                bottom: 0,
            },
        },
    },
    '& .MuiList-root': {
        padding: 0,
    },
});

export default function ColumnFilters() {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const dispatch = useAppDispatch();
    const filters = useSelector(selectVehicleDatabaseFilters);

    const enableAftersalesCrm =
        useAppSelector((r) => r.globalSettings.settings)?.repairShopSettings?.features
            .enableAftersalesCrm ?? false;
    const columns = useAppSelector(selectAvailableVehicleDatabaseColumns);
    const activeFilters = useAppSelector(selectActiveFilters);

    useEffect(() => {
        if (!enableAftersalesCrm) {
            dispatch(vehicleDatabaseActions.clearFiltersWhenCrmIsDisabled());
        }
    }, [dispatch, enableAftersalesCrm]);

    const { t } = useAppTranslation();

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.target as HTMLElement);
    };

    const isAllSelected = useMemo(() => columns.every((x) => x.selected), [columns]);

    function toggle(key: string) {
        const paramKey = vehicleDatabaseFilterKeyMap[key] as string;

        if (isAllSelected) {
            dispatch(
                vehicleDatabaseActions.clearFiltersExcept(
                    paramKey as keyof Omit<VehicleDatabasePageParams, 'pageIdx'>
                )
            );
            columns
                .filter((x) => x.key !== key)
                .forEach((x) => {
                    dispatch(vehicleDatabaseActions.toggleColumnSelection(x.key));
                });
        } else {
            dispatch(vehicleDatabaseActions.toggleColumnSelection(key));
            dispatch(vehicleDatabaseActions.setFilters({ ...filters, [paramKey]: [] }));
            dispatch(
                vehicleDatabaseActions.setFilterIsActive({
                    key,
                    active: false,
                })
            );
        }
    }

    const handleAllColumnsToggle = () => {
        if (isAllSelected) {
            return;
        } else {
            columns.forEach((x) => {
                if (!x.selected) {
                    dispatch(vehicleDatabaseActions.toggleColumnSelection(x.key));
                }
            });
        }
    };

    function shouldShowColumnChecked(column: (typeof columns)[0]) {
        if (isAllSelected) {
            return false;
        }
        return column.selected;
    }

    // Group columns by columnGroup (Retention, FollowUp, GeographicLocation)
    const groupedColumns = useMemo(
        () =>
            columns.reduce<Record<ColumnGroup, (typeof columns)[number][]>>((acc, column) => {
                if (!acc[column.columnGroup]) {
                    acc[column.columnGroup] = [];
                }
                acc[column.columnGroup].push(column);
                return acc;
            }, {} as Record<ColumnGroup, (typeof columns)[number][]>),
        [columns]
    );

    return (
        <>
            <IconButton size="small" onClick={handleClick}>
                <FiltersIcon fill={anchorEl ? 'var(--cm1)' : 'currentColor'} />
            </IconButton>
            <ColumnsMenu
                anchorOrigin={{
                    horizontal: 'right',
                    vertical: 'bottom',
                }}
                transformOrigin={{
                    horizontal: 'right',
                    vertical: 'top',
                }}
                onClose={() => setAnchorEl(null)}
                open={!!anchorEl}
                anchorEl={anchorEl}
            >
                {!enableAftersalesCrm ? (
                    <Box>
                        <StyledMenuItem onClick={handleAllColumnsToggle}>
                            {isAllSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}{' '}
                            <Box
                                sx={{
                                    marginLeft: '5px',
                                    color: 'var(--neutral8)',
                                }}
                            >
                                {t('afterSalesCrm.filterMenu.allColumns')}
                            </Box>
                        </StyledMenuItem>
                        {columns.map((column) => (
                            <StyledMenuItem key={column.key} onClick={() => toggle(column.key)}>
                                {shouldShowColumnChecked(column) ? (
                                    <CheckBoxIcon />
                                ) : (
                                    <UncheckBoxIcon />
                                )}{' '}
                                <Box
                                    sx={{
                                        marginLeft: '5px',
                                        color: activeFilters[column.key]
                                            ? 'var(--cm1)'
                                            : 'var(--neutral8)',
                                    }}
                                >
                                    {t(`afterSalesCrm.columns.${column.key}`)}
                                </Box>
                            </StyledMenuItem>
                        ))}
                    </Box>
                ) : (
                    Object.entries(groupedColumns).map(([groupName, groupColumns]) => (
                        <Box key={groupName}>
                            <Grid
                                container
                                justifyContent="space-between"
                                alignItems="center"
                                sx={{ padding: '8px 12px', backgroundColor: '#F3F3F3' }}
                            >
                                <Typography sx={{ color: 'var(--neutral8)' }}>
                                    {t(`afterSalesCrm.filterMenu.${groupName}`)}
                                </Typography>
                            </Grid>
                            {groupName === 'Retention' && (
                                <StyledMenuItem onClick={handleAllColumnsToggle}>
                                    {isAllSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}{' '}
                                    <Box
                                        sx={{
                                            marginLeft: '5px',
                                            color: 'var(--neutral8)',
                                        }}
                                    >
                                        {t('afterSalesCrm.filterMenu.allColumns')}
                                    </Box>
                                </StyledMenuItem>
                            )}
                            {groupColumns.map((column) => (
                                <StyledMenuItem key={column.key} onClick={() => toggle(column.key)}>
                                    {shouldShowColumnChecked(column) ? (
                                        <CheckBoxIcon />
                                    ) : (
                                        <UncheckBoxIcon />
                                    )}{' '}
                                    <Box
                                        sx={{
                                            marginLeft: '5px',
                                            color: activeFilters[column.key]
                                                ? 'var(--cm1)'
                                                : 'var(--neutral8)',
                                        }}
                                    >
                                        {t(`afterSalesCrm.columns.${column.key}`)}
                                    </Box>
                                </StyledMenuItem>
                            ))}
                        </Box>
                    ))
                )}
            </ColumnsMenu>
        </>
    );
}

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    color: 'var(--neutral8)',
    '&:hover': {
        color: 'var(--cm1)',
    },
}));
