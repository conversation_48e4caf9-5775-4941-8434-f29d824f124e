import React, { useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

import {
    Box,
    Grid,
    styled,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from '@mui/material';

import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';

import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands } from 'store/slices/clientsSlice/selectors';
import ensureBrandsFetched from 'store/slices/clientsSlice/thunks/ensureBrandsFetched';
import {
    selectSelectedVehicleDatabaseColumns,
    selectVehicleDatabaseFilters,
    vehicleDatabaseActions,
    vehicleDatabaseFilterKeyMap,
} from 'store/slices/vehicleDatabase';

import TableHeadSelectFilter, { FilterOption } from '../../TableHeadSelectFilter';

import CustomersApi, {
    PriorityType,
    RecommendedServiceFrequency,
    usageTypeSchema,
    VehicleDatabaseData,
    VehicleDatabaseDataResponse,
    VehicleDatabasePageParams,
    vinStatusSchema,
} from 'api/customers';
import UsersApi from 'api/users';

import { CarIcon } from 'common/components/Icons/CarIcon';
import { NoResultIcon } from 'common/components/Icons/NoResultIcon';

import PaperWithOverlayScrollbars from 'common/components/PaperWithOverlayScrollbars';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import moment from 'moment/moment';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectVehicleParams } from 'store/slices/vehicleDetails';
import { isDark } from 'utils/colors';

const getAllOptionLabel = (key: string, t: (key: string) => string): string => {
    switch (key) {
        case 'vinStatus':
            return t('afterSalesCrm.filters.allVinStatus');
        case 'prospectionPriority':
            return t('afterSalesCrm.filters.allProspectionPriority');
        case 'usageType':
            return t('afterSalesCrm.filters.allUsageType');
        case 'customerStatus':
            return t('afterSalesCrm.filters.allCustomerStatus');
        case 'brand':
            return `           ${t('afterSalesCrm.filters.allBrand')}`;

        case 'model':
            return t('afterSalesCrm.filters.allModel');
        case 'year':
            return t('afterSalesCrm.filters.allYear');
        case 'assignedBdcAdvisor':
            return t('afterSalesCrm.filters.allAssignedBdcAdvisor');
        case 'lastActivity':
            return t('afterSalesCrm.filters.allLastActivity');
        default:
            return t('afterSalesCrm.filters.all');
    }
};
type VehicleDatabaseTableProps = {
    vehicleDatabaseData?: VehicleDatabaseDataResponse;
};

const VehicleDatabaseTable = React.memo(function VehicleDatabaseTable({
    vehicleDatabaseData,
}: VehicleDatabaseTableProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const navigate = useNavigate();
    const filters = useSelector(selectVehicleDatabaseFilters);
    const brands = useAppSelector(selectBrands);
    const vehicleParams = useAppSelector(selectVehicleParams);
    const { internationalization, repairShopSettings } = useAppSelector(selectSettings);
    const columns = useAppSelector(selectSelectedVehicleDatabaseColumns);

    const formatDate = (date: string | null) => {
        if (!date) return '-';

        const momentDate = moment(date);
        if (internationalization.language === 'es') {
            const month = momentDate.format('MMM').toLowerCase();
            return `${momentDate.format('D')} ${month} ${momentDate.format('YYYY')}`;
        }
        return momentDate.format('MMM D, YYYY');
    };

    const { data: bdcAdvisors } = useQuery(
        ['bdc-advisors'],
        () => UsersApi.getBdcAdvisor().then((response) => response),
        {
            cacheTime: Infinity,
            staleTime: 60000,
        }
    );

    const { data: brandsWithModels } = useQuery(
        ['brands-with-models', 'vehicle-database'],
        CustomersApi.getAllBrandsAndModels,
        {
            cacheTime: Infinity,
            staleTime: 60000,
        }
    );

    useEffect(() => {
        dispatch(ensureBrandsFetched());
    }, [dispatch]);

    const modelsMemo = useMemo(() => {
        if (!brandsWithModels) return null;

        return brandsWithModels
            .filter((brand) => filters.brand.includes(brand.name))
            .flatMap((brand) => brand.models)
            .filter((modelName, index, self) => self.indexOf(modelName) === index) // remove duplicates
            .map((modelName) => ({
                id: modelName,
                label: modelName,
            }));
    }, [brandsWithModels, filters.brand]);

    const brandsMemo = useMemo(() => {
        if (brands.length === 0) return null;
        return brands.map(({ name, brandUrl, hasLogo }) => ({
            id: name,
            label: hasLogo ? name : `           ${name}`,
            labelImage: hasLogo ? brandUrl : undefined,
        }));
    }, [brands]);

    function getOptions(key: string): FilterOption<string | boolean | number>[] | null {
        function getProspectionPrioritiesOptions() {
            return (
                vehicleParams?.prospectionPriorities.map((p) => ({
                    id: p.id,
                    label: p.name,
                })) ?? null
            );
        }

        function getCustomerStatusOptions() {
            return (
                vehicleParams?.customerStatuses.map((p) => ({
                    id: p.id,
                    label: p.name,
                })) ?? null
            );
        }

        function getRecommendedServiceFrequencyOptions() {
            return (
                vehicleParams?.recommendedServiceFrequencyOptions
                    .filter((p) => p.km != null)
                    .map((p) => ({
                        // Yes, I use kilometers as ID for dropdown. Because I didn’t come up with anything else.
                        // If you want, you can replace kilometers for months, the meaning will not change.
                        id: p.km as number,
                        label: t('afterSalesCrm.values.recommendedServiceFrequency', {
                            km: p.km ?? 0,
                            months: p.months,
                        }),
                    })) ?? null
            );
        }

        function getLastActivityOptions() {
            if (!vehicleParams?.customerStatuses) return null;

            return vehicleParams.customerStatuses
                .filter((status) => filters.customerStatus.includes(status.id))
                .flatMap((status) => status.activities)
                .map((activity) => ({
                    id: activity.id,
                    label: activity.name,
                }));
        }

        switch (key) {
            case 'prospectionPriority':
                return getProspectionPrioritiesOptions();

            case 'customerStatus':
                return getCustomerStatusOptions();

            case 'recommendedServiceFrequency':
                return getRecommendedServiceFrequencyOptions();

            case 'usageType':
                return mapEnumToOptions(usageTypeSchema.Enum, t);

            case 'customerType':
                return mapEnumToOptions(vinStatusSchema.Enum, t);

            case 'vinStatus':
                return mapEnumToOptions(vinStatusSchema.Enum, t);

            case 'inPma':
            case 'isFirstOwner':
                return [
                    { id: true, label: 'Yes' },
                    { id: false, label: 'No' },
                ];

            case 'brand':
                return brandsMemo;

            case 'model':
                return modelsMemo;

            case 'year': {
                const currentYear = new Date().getFullYear();
                return Array.from({ length: currentYear - 1950 + 2 }, (_, i) => {
                    const year = currentYear + 1 - i;
                    return { id: year.toString(), label: year.toString() };
                });
            }

            case 'assignedBdcAdvisor':
                if (!bdcAdvisors) return null;
                return bdcAdvisors.map((advisor) => ({
                    id: advisor.key,
                    label: advisor.displayName,
                    subLabelText: advisor.userName,
                }));

            case 'lastActivity':
                return getLastActivityOptions();
            default:
                return [];
        }
    }

    function setFilters(selected: (string | boolean | number)[], key: string) {
        let filterValue: string[] | boolean[] | number[];
        const paramKey = vehicleDatabaseFilterKeyMap[key];
        if (!paramKey) return;

        switch (key) {
            case 'usageType':
            case 'assignedBdcAdvisor':
                filterValue = selected as string[];
                break;
            case 'inPma':
            case 'isFirstOwner':
                filterValue = selected as boolean[];
                break;
            case 'year':
            case 'vinStatus':
            case 'prospectionPriority':
            case 'brand':
            case 'model':
            case 'customerStatus':
            case 'lastActivity':
            case 'recommendedServiceFrequency':
            case 'customerType':
                filterValue = selected as number[];
                break;
            default:
                return;
        }

        dispatch(
            vehicleDatabaseActions.setFilters({
                ...filters,
                [paramKey]: filterValue,
            })
        );
    }

    return (
        <TableContainer
            sx={{
                height: '75vh',
                '@media (max-height: 600px)': {
                    height: '63vh',
                },
                '@media (min-height: 601px) and (max-height: 800px)': {
                    height: '70vh',
                },
                '@media (min-height: 801px) and (max-height: 900px)': {
                    height: '75vh',
                },
                '@media (min-height: 901px)': {
                    height: '79vh',
                },
            }}
            component={PaperWithOverlayScrollbars}
        >
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        {columns.map((column) =>
                            !column.isFiltered ? (
                                <HeaderCell key={column.key} width={column.width}>
                                    {t(`afterSalesCrm.columns.${column.key}`)}
                                </HeaderCell>
                            ) : (
                                <HeaderCell key={column.key} width={column.width}>
                                    <Grid
                                        container
                                        alignItems="center"
                                        justifyContent="flex-start"
                                        wrap="nowrap"
                                    >
                                        <TableHeadSelectFilter
                                            mainLabel={column.key}
                                            options={getOptions(column.key)}
                                            selected={getSelected(column.key, filters)}
                                            onSelectedChanged={(selected, isPartiallySelected) => {
                                                setFilters(selected, column.key);
                                                dispatch(
                                                    vehicleDatabaseActions.setFilterIsActive({
                                                        key: column.key,
                                                        active: isPartiallySelected,
                                                    })
                                                );
                                            }}
                                            allOptionLabel={getAllOptionLabel(column.key, t)}
                                        />
                                    </Grid>
                                </HeaderCell>
                            )
                        )}
                    </TableRow>
                </TableHead>
                <TableBody>
                    {vehicleDatabaseData?.vehiclesListIsEmpty && (
                        <TableRow>
                            <TableCell colSpan={columns.length} sx={{ border: 'none' }}>
                                <CenteredBox>
                                    <NoResultBox gapSize={10}>
                                        <CarIcon fill={'var(--cm1)'} size={100} />
                                        <Typography variant="h4Inter">
                                            {t('afterSalesCrm.noVehiclesFoundTitle')}
                                        </Typography>
                                        <Typography variant="h5Inter" sx={{ fontWeight: 400 }}>
                                            {t('afterSalesCrm.noVehiclesFoundText')}
                                        </Typography>
                                    </NoResultBox>
                                </CenteredBox>
                            </TableCell>
                        </TableRow>
                    )}
                    {vehicleDatabaseData?.totalCount === 0 &&
                        !vehicleDatabaseData?.vehiclesListIsEmpty && (
                            <TableRow>
                                <TableCell colSpan={columns.length} sx={{ border: 'none' }}>
                                    <CenteredBox>
                                        <NoResultBox>
                                            <NoResultIcon />
                                            <Typography variant="h5Inter" sx={{ fontSize: '18px' }}>
                                                {t('afterSalesCrm.noMatchesFound')}
                                            </Typography>
                                        </NoResultBox>
                                    </CenteredBox>
                                </TableCell>
                            </TableRow>
                        )}
                    {vehicleDatabaseData?.vehiclesDatabaseData.map((row, rowIndex) => {
                        const rowElement = (
                            <StyledTableRow
                                onClick={
                                    repairShopSettings?.features.enableAftersalesCrm === true
                                        ? () => navigate(`/vehicle/${row.id}`)
                                        : undefined
                                }
                                key={rowIndex}
                            >
                                {columns.map(({ key, width, dataType }) => {
                                    const cellValue = row[key as keyof VehicleDatabaseData] ?? '-';
                                    return (
                                        <VehicleTableCell key={`${rowIndex}-${key}`} width={width}>
                                            {(() => {
                                                if (dataType === 'prospectionPriority') {
                                                    if (cellValue === '-')
                                                        return (
                                                            <PriorityStyled color="#899198">
                                                                {t(
                                                                    'afterSalesCrm.values.noPriority'
                                                                )}
                                                            </PriorityStyled>
                                                        );
                                                    const value = cellValue as PriorityType;
                                                    return (
                                                        <PriorityStyled color={value.color}>
                                                            {value.name}
                                                        </PriorityStyled>
                                                    );
                                                }

                                                if (cellValue === '-') return '-';
                                                switch (dataType) {
                                                    case 'boolean':
                                                        return cellValue
                                                            ? t('afterSalesCrm.values.Yes')
                                                            : t('afterSalesCrm.values.No');
                                                    case 'date':
                                                        return formatDate(
                                                            cellValue as string | null
                                                        );
                                                    case 'vinStatus':
                                                        return t(
                                                            `afterSalesCrm.values.${cellValue}`
                                                        );
                                                    case 'recommendedServiceFrequency': {
                                                        const value =
                                                            cellValue as RecommendedServiceFrequency;
                                                        if (
                                                            value.km === null &&
                                                            value.months === null
                                                        )
                                                            return '-';
                                                        return t(
                                                            'afterSalesCrm.values.recommendedServiceFrequency',
                                                            {
                                                                km: value.km ?? '-',
                                                                months: value.months ?? '-',
                                                            }
                                                        );
                                                    }
                                                    default:
                                                        return cellValue === ''
                                                            ? '-'
                                                            : t(
                                                                  `afterSalesCrm.values.${cellValue}`,
                                                                  {
                                                                      defaultValue:
                                                                          String(cellValue),
                                                                  }
                                                              );
                                                }
                                            })()}
                                        </VehicleTableCell>
                                    );
                                })}
                            </StyledTableRow>
                        );

                        return rowElement;
                    })}
                </TableBody>
            </Table>
        </TableContainer>
    );
});

function mapEnumToOptions<T extends Record<string, string | number>>(enumObj: T, t: TFunction) {
    return Object.entries(enumObj)
        .filter(([key]) => isNaN(Number(key)))
        .map(([label, id]) => ({
            id,
            label: t(`afterSalesCrm.filters.${label}`, { defaultValue: label }),
        }));
}

function getSelected(key: string, filters: Omit<VehicleDatabasePageParams, 'pageIdx'>) {
    switch (key) {
        case 'brand':
            return filters.brand;
        case 'model':
            return filters.model;
        case 'year':
            return filters.year;
        case 'vinStatus':
            return filters.vinStatus;
        case 'prospectionPriority':
            return filters.prospectionPriority;
        case 'inPma':
            return filters.inPma;
        case 'customerType':
            return filters.customerType;
        case 'usageType':
            return filters.usageType;
        case 'customerStatus':
            return filters.customerStatus;
        case 'lastActivity':
            return filters.lastActivity;
        case 'recommendedServiceFrequency':
            return filters.recommendedFrequency;
        case 'assignedBdcAdvisor':
            return filters.bdcAdvisor;
        case 'isFirstOwner':
            return filters.isFirstOwner;
        default:
            return [];
    }
}

const HeaderCell = styled(TableCell)(({ theme, width }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 700,
    color: 'var(--neutral7)',
    padding: '16px 10px',
    backgroundColor: 'var(--neutral2)',
    textTransform: 'uppercase',
    boxSizing: 'border-box',
    borderTop: '1px solid #DBDCDD',
    borderLeft: '1px solid #DBDCDD',
    minWidth: width || 120,
    height: 50,
}));

const VehicleTableCell = styled(TableCell)<{ width?: number }>(({ theme, width }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 400,
    color: 'var(--neutral8)',
    borderLeft: '1px solid #DBDCDD',
    position: 'relative',
    minWidth: width || 120,
    height: 50,
}));

const StyledTableRow = styled(TableRow)({
    height: 50,
    '&:hover': {
        backgroundColor: 'var(--cm5)',
        cursor: 'pointer',
        '& > td': {
            backgroundColor: 'var(--cm5)',
        },
    },
});

const PriorityStyled = styled(Box, {
    shouldForwardProp: (prop) => prop !== 'color',
})<{ color: string }>(({ color }) => ({
    display: 'inline-block',
    padding: '4px 8px',
    borderRadius: '4px',
    backgroundColor: color,
    color: isDark(color, 0.7) ? '#fff' : 'var(--neutral8)',
    fontWeight: 'bold',
}));

const CenteredBox = styled(Box)({
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 10,
    pointerEvents: 'none',
});

const NoResultBox = styled(Box)<{ gapSize?: number }>(({ gapSize = 35 }) => ({
    color: 'var(--neutral9)',
    fontWeight: 700,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: `${gapSize}px`,
}));

export default VehicleDatabaseTable;
