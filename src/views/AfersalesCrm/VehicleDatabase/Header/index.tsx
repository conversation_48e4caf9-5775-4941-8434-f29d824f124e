import { styled } from '@mui/material';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import CreateNewCustomerPopup from 'views/Components/CreateNewCustomerPopup';
import VehicleAutocomplete from '../VehicleAutocomplete';

const Root = styled('div')(({ theme }) => ({
    '&::after': {
        right: 0,
        left: 'initial',
    },
    justifyContent: 'right',
    gap: 10,
    position: 'relative',
    margin: '0 15px',
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItem: 'center',
    color: theme.palette.neutral[5],
    alignItems: 'center',
}));

export default function Header() {
    const { t } = useAppTranslation();

    return (
        <Root>
            <VehicleAutocomplete />
            <NewVehicleButton />
        </Root>
    );
}

function NewVehicleButton() {
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const { t } = useAppTranslation();
    const allowEditVehicles = useAppSelector((r) => selectUserPermission(r).allowEditVehicles);

    return (
        <>
            <Button
                disabled={!allowEditVehicles}
                onClick={() => setOpen(true)}
                sx={{ width: 140 }}
                label={t('afterSalesCrm.newVehicle')}
            />
            <CustomersSearchApiProvider mode="shop">
                <CreateNewCustomerPopup
                    open={open}
                    vehicleIsRequired
                    onCustomerAndVehicleCreated={(event) => {
                        setOpen(true);
                        if (event.vehicle) navigate('/vehicle/' + event.vehicle.id);
                    }}
                    onClose={() => setOpen(false)}
                />
            </CustomersSearchApiProvider>
        </>
    );
}
