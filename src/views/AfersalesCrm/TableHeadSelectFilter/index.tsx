import { Checkbox, IconButton, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import * as React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { FixedSizeList as List } from 'react-window';
import Box from '@mui/material/Box';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import {
    GridMenuSubheader,
    StyledMenu,
    StyledMenuItem,
} from 'common/components/Inputs/Menu/common';

export type TableHeadSelectFilterProps<T extends string | boolean | number> = {
    mainLabel: string;
    options: FilterOption<T>[] | null;
    selected: FilterOptionId<T>[];
    onSelectedChanged: (selected: FilterOptionId<T>[], isPartiallySelected: boolean) => void;
    allOptionLabel: string;
};

export type FilterOption<T> = {
    id: FilterOptionId<T>;
    label: string;
    labelImage?: string;
    subLabelText?: string;
    isSelectAllOption?: boolean;
};

export type FilterOptionId<T> = T;

const MenuItemContent = styled('div')<{ isLast?: boolean }>(({ isLast }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: 10,
    borderBottom: isLast ? 'none' : 'solid 1px #EFEFEF',
    width: '100%',
    paddingBottom: '8px',
    marginTop: '11px',
    minWidth: '140px',
}));

const MenuItemsContainer = styled('div')({
    maxHeight: 540,
});

const MenuItemLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: '#6A6E72',
    whiteSpace: 'pre',
    fontWeight: 400,
    '.MuiMenuItem-root:hover &': {
        color: 'var(--cm1)',
    },
}));

const LabelContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
});

const SubLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h11Inter,
    color: 'var(--neutral6)',
    fontSize: '8px',
}));

export const TableHeadSelectFilter = <T extends string | boolean | number>({
    mainLabel,
    options,
    selected,
    onSelectedChanged,
    allOptionLabel,
}: TableHeadSelectFilterProps<T>) => {
    const menuButtonRef = useRef(null);
    const { t } = useAppTranslation();

    const [showMenu, setShowMenu] = useState<boolean>(false);
    const [selectedOptions, setSelectedOptions] = useState<FilterOptionId<T>[]>(selected);

    const allOptions = useMemo(() => {
        if (!options) return null;
        return [
            {
                id: 'allOptions' as FilterOptionId<T>,
                label: allOptionLabel,
                isSelectAllOption: true,
            },
            ...options,
        ];
    }, [options, allOptionLabel]);

    const isAllSelected = useMemo(
        () =>
            options &&
            options.length > 0 &&
            options.every((option) => selectedOptions.includes(option.id)),
        [options, selectedOptions]
    );

    const isPartiallySelected = useMemo(
        () => selectedOptions.length > 0 && !isAllSelected,
        [selectedOptions.length, isAllSelected]
    );

    // Unselect options that are no longer available among current options
    useEffect(() => {
        if (!options) return;
        const validSelectedOptions = selectedOptions.filter((option) =>
            options.some((opt) => opt.id === option)
        );

        if (validSelectedOptions.length !== selectedOptions.length) {
            setSelectedOptions(validSelectedOptions);
            onSelectedChanged(validSelectedOptions, isPartiallySelected);
        }
    }, [options, selectedOptions, onSelectedChanged, isPartiallySelected]);

    const handleOptionClicked = (optionId: FilterOptionId<T>, isAllOption?: boolean) => {
        if (isAllOption) {
            setSelectedOptions([]);
            return;
        }

        if (!selectedOptions.some((s) => s === optionId)) {
            const result = [...selectedOptions, optionId];
            setSelectedOptions(result);
        } else {
            const result = selectedOptions.filter((o) => o !== optionId);
            setSelectedOptions(result);
        }
    };

    const onClose = () => {
        onSelectedChanged(selectedOptions, isPartiallySelected);
        setShowMenu(false);
    };

    return (
        <>
            <Box
                sx={{
                    color: isPartiallySelected ? 'var(--cm1)' : 'var(--neutral7)',
                }}
            >
                {t(`afterSalesCrm.columns.${mainLabel}`)}{' '}
            </Box>
            <IconButton
                ref={menuButtonRef}
                onClick={() => setShowMenu(true)}
                aria-controls="simple-menu"
                aria-haspopup="true"
                size="small"
            >
                <FiltersIcon fill={isPartiallySelected ? 'var(--cm1)' : 'var(--neutral7)'} />
            </IconButton>
            <StyledMenu
                anchorEl={menuButtonRef.current}
                keepMounted
                open={showMenu}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                onClose={onClose}
                MenuListProps={{
                    disablePadding: true,
                    subheader: (
                        <GridMenuSubheader
                            container
                            justifyContent="space-between"
                            alignItems="center"
                        >
                            <Box>{t(`afterSalesCrm.columns.${mainLabel}`)}</Box>
                        </GridMenuSubheader>
                    ),
                }}
            >
                <MenuItemsContainer>
                    <List
                        height={
                            (allOptions?.length ?? 0) * 42 > 500
                                ? 500
                                : (allOptions?.length ?? 0) * 42 + 1
                        }
                        itemCount={allOptions?.length ?? 0}
                        itemSize={42}
                        width="280px"
                    >
                        {({ index, style }) => {
                            if (!allOptions) return null;
                            const option = allOptions[index];
                            const isSelected = option.isSelectAllOption
                                ? selectedOptions.length === 0
                                : selectedOptions.some((s) => s === option.id);

                            return (
                                <StyledMenuItem
                                    key={String(option.id)}
                                    onClick={() =>
                                        handleOptionClicked(option.id, option.isSelectAllOption)
                                    }
                                    disableRipple
                                    sx={style}
                                >
                                    <MenuItemContent
                                        isLast={index === (allOptions?.length ?? 0) - 1}
                                    >
                                        <Checkbox
                                            checked={isSelected}
                                            readOnly
                                            icon={<UncheckBoxIcon fill="var(--cm1)" />}
                                            checkedIcon={<CheckBoxIcon fill="var(--cm1)" />}
                                            sx={{ padding: 0 }}
                                        />
                                        {option.labelImage && (
                                            <img src={option.labelImage} alt={option.label} />
                                        )}
                                        <LabelContainer>
                                            <MenuItemLabel>
                                                {t(
                                                    option.isSelectAllOption
                                                        ? option.label
                                                        : `afterSalesCrm.values.${option.label}`,
                                                    { defaultValue: option.label }
                                                )}
                                            </MenuItemLabel>
                                            {option.subLabelText && (
                                                <SubLabel>{option.subLabelText}</SubLabel>
                                            )}
                                        </LabelContainer>
                                    </MenuItemContent>
                                </StyledMenuItem>
                            );
                        }}
                    </List>
                </MenuItemsContainer>
            </StyledMenu>
        </>
    );
};
export default TableHeadSelectFilter;
