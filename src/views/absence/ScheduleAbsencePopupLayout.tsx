import { Backdrop, IconButton, Paper } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { useEffect, useMemo, useState } from 'react';
import ReactDOM from 'react-dom';
import Draggable, { ControlPosition } from 'react-draggable';

export type ScheduleAbsencePopupLayoutProps = {
    anchorEl?: HTMLElement;
    onClose: () => void;
    children?: React.ReactNode;
    open: boolean;
    onDelete?: () => void;
    userName: string;
};

const useStyles = makeStyles((theme) => ({
    root: {
        position: 'fixed',
        left: 0,
        top: 0,
        outline: `4px solid transparent`,
        transition: 'outline .15s',
        '&.react-draggable-dragging': {
            outline: `4px solid ${rgba(theme.palette.neutral[9], 0.5)}`,
            userSelect: 'none !important',
        },
        borderRadius: 10,
        maxWidth: 'min(80vw, 790px)',
        height: 'auto',
        width: 590,
        minHeight: 435,
    },
    paper: {
        padding: 0,
        overflowY: 'visible',
        borderRadius: 10,
    },
    header: {
        backgroundColor: theme.palette.neutral[3],
        padding: '15px 30px',
        position: 'relative',
        paddingRight: 60,
        cursor: 'move',
        borderRadius: '10px 10px 0 0',
    },
    buttons: {
        display: 'flex',
        alignItems: 'center',
        gap: 5,
        position: 'absolute',
        right: 10,
        top: 5,
        padding: 5,
    },
    title: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[8],
        margin: 0,
    },
    subtitle: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[6],
    },

    main: {
        padding: '10px 30px 20px 30px',
        minWidth: 500,
    },
}));

export default function ScheduleAbsencePopupLayout({
    open,
    anchorEl,
    onClose,
    children,
    onDelete,
    userName,
}: ScheduleAbsencePopupLayoutProps) {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const draggableId = useMemo(
        () => `draggable-schedule-popup-handle-${Math.random().toString(36).substring(2)}`,
        []
    );
    const [position, setPosition] = useState<ControlPosition>({ x: 0, y: 0 });

    const [ref, setRef] = useState<HTMLDivElement | null>(null);

    useEffect(() => {
        if (open && ref && anchorEl && document.body.contains(anchorEl)) {
            setPosition(positionPopup(ref, anchorEl));
        }
    }, [open, ref, anchorEl]);

    return ReactDOM.createPortal(
        <Backdrop onClick={onClose} invisible style={{ zIndex: 1000 }} open={open}>
            {open && (
                <Draggable
                    position={position}
                    enableUserSelectHack={false}
                    onDrag={(_, { x, y }) => setPosition({ x, y })}
                    bounds="body"
                    handle={'#' + draggableId}
                >
                    <div className={styles.root} ref={setRef} onClick={(e) => e.stopPropagation()}>
                        <Paper className={styles.paper}>
                            <header className={styles.header} id={draggableId}>
                                <h1 className={styles.title}>{t('absences.createPopup.title')}</h1>
                                <span className={styles.subtitle}>
                                    {t('absences.createPopup.personWhoSchedules', {
                                        user: userName,
                                    })}
                                </span>

                                <div className={styles.buttons}>
                                    {onDelete && (
                                        <IconButton size="medium" onClick={onDelete}>
                                            <DeleteIcon fill="currentColor" />
                                        </IconButton>
                                    )}
                                    <IconButton size="medium" onClick={onClose}>
                                        <CloseIcon fill="currentColor" />
                                    </IconButton>
                                </div>
                            </header>
                            <main className={styles.main}>{children}</main>
                        </Paper>
                    </div>
                </Draggable>
            )}
        </Backdrop>,
        document.body
    );
}

function createBlueprint(el: HTMLElement) {
    const rect = el.getBoundingClientRect();
    const b = document.createElement('div');
    b.style.position = 'fixed';
    b.style.top = '0px';
    b.style.left = '0px';
    b.style.width = `${rect.width}px`;
    b.style.height = `${rect.height}px`;
    b.style.transform = `translate(${rect.left}px, ${rect.top}px)`;
    b.style.pointerEvents = 'none';
    document.body.appendChild(b);
    return b;
}

function positionPopup(popupOriginal: HTMLElement, anchor: HTMLElement) {
    // create a "blueprint" element that is just a rectangle with the same exact position and size
    // this is to avoid problems with css transition (if it's set)
    const popup = createBlueprint(popupOriginal);

    let popupRect = popup.getBoundingClientRect();
    const { height } = popupRect;
    const anchorRect = anchor.getBoundingClientRect();
    // position it at the top
    let x: number = -1,
        y: number = -1;

    popup.style.transform = '';
    const set = (x_: number, y_: number) => {
        x = x_;
        y = y_;
        popup.style.transform = `translate(${x}px, ${y}px)`;
    };

    // # First find Y position
    // position at the top
    set(anchorRect.right, anchorRect.top - height);

    popupRect = popup.getBoundingClientRect();
    if (popupRect.top < 0) {
        // doesn't fit at the top, move to bottom
        set(anchorRect.left, anchorRect.bottom);
        popupRect = popup.getBoundingClientRect();
        if (popupRect.bottom > window.innerHeight) {
            // doesn't fit at the bottom, move up a bit
            set(anchorRect.left, anchorRect.bottom - (popupRect.bottom - window.innerHeight));
        }
    }

    // # Now find X position
    if (popupRect.right > window.innerWidth) {
        set(x - (popupRect.right - window.innerWidth) - 30, y);
    }

    if (x < 0) set(0, y);
    if (y < 0) set(x, 0);

    const adjLeft = Math.max(0, anchorRect.left);
    const adjRight = Math.min(window.innerWidth, anchorRect.right);
    const outOfBounds = adjLeft !== anchorRect.left || adjRight !== anchorRect.right;
    if (outOfBounds) {
        set(window.innerWidth / 2 - popupRect.width / 2, y);
    }

    popupOriginal.style.transform = `translate(${x}px, ${y}px)`;
    popup.remove(); // removes the popup's blueprint, not popup itself (see start of this function)
    return { x, y };
}
