import { AbsenceDto } from 'api/users';
import useForceRender from 'common/hooks/useForceRender';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import EditAbsencePopup from '.';

interface IEditAbsencePopupContext {
    open(absence: AbsenceDto, element: HTMLElement): void;
}

const EditAbsencePopupContext = createContext<IEditAbsencePopupContext | null>(null);

export function useEditAbsencePopup() {
    const ctx = useContext(EditAbsencePopupContext);
    if (!ctx) throw new Error('cannot use EditAbsencePopupContext');
    return ctx;
}

export function EditAbsencePopupProvider({ children }: React.PropsWithChildren<{}>) {
    const state = useRef({
        absence: null as AbsenceDto | null,
        anchorEl: null as HTMLElement | null,
    });
    const fr = useForceRender();
    const ctx: IEditAbsencePopupContext = useMemo(
        () => ({
            open: (absence, element) => {
                state.current.anchorEl = element;
                state.current.absence = absence;
                fr();
            },
        }),
        [fr]
    );

    const close = useCallback(() => {
        state.current.anchorEl = null;
        fr();
    }, [fr]);

    return (
        <EditAbsencePopupContext.Provider value={ctx}>
            <EditAbsencePopup
                absence={state.current.absence!}
                onClose={close}
                anchorEl={state.current.anchorEl ?? undefined}
                open={!!state.current.anchorEl}
            />
            {children}
        </EditAbsencePopupContext.Provider>
    );
}
