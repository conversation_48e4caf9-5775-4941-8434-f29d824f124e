import { Box, Grid, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { AbsenceDto, AbsencesApi, UpdateAbsenceDto } from 'api/users';
import { Button } from 'common/components/Button';
import { <PERSON><PERSON>rea, TextField } from 'common/components/Inputs';
import DateF<PERSON>Field from 'common/components/Inputs/DateFormField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { Switch } from 'common/components/Inputs/Switch';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import {
    DeleteConfirmationPopup,
    WarningConfirmationPopup,
} from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { absencesActions } from 'store/slices/absences';
import { selectActiveUser } from 'store/slices/users';
import { isDateValid } from 'utils';
import ScheduleAbsencePopupLayout from '../ScheduleAbsencePopupLayout';

type EditAbsencePopupProps = {
    absence: AbsenceDto;
    onClose: () => void;
    anchorEl?: HTMLElement;
    open: boolean;
};

export default function EditAbsencePopup({
    absence,
    onClose,
    open,
    ...props
}: EditAbsencePopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const [isDeletePopupOpen, setIsDeletePopupOpen] = useState<boolean>(false);

    const [reason, setReason] = useState<string | null>(absence?.reason);
    const [notes, setNotes] = useState<string | null>(absence?.notes);
    const [isAllDayMode, setIsAllDayMode] = useState<boolean>(absence?.roundedToDays ?? false);
    const [startDate, setStartDate] = useState<Date>(new Date());
    const [endDate, setEndDate] = useState<Date>(new Date());
    const [startTime, setStartTime] = useState<[number, number]>([0, 0]);
    const [endTime, setEndTime] = useState<[number, number]>([0, 0]);

    const [teamMemberId, setTeamMemberId] = useState<string | null>(absence?.userId);
    const [collisionsCount, setCollisionsCount] = useState<number>(0);
    const users = useAppSelector(selectActiveUser);
    const dispatch = useDispatch();
    const [cancelEditAbsenceModalIsOpen, setCancelEditAbsenceModalIsOpen] =
        useState<boolean>(false);

    useEffect(() => {
        setTeamMemberId(absence?.userId);
    }, [absence?.userId]);

    useEffect(() => {
        setNotes(absence?.notes);
    }, [absence?.notes]);

    useEffect(() => {
        setReason(absence?.reason);
    }, [absence?.reason]);

    useEffect(() => {
        setIsAllDayMode(absence?.roundedToDays ?? false);
    }, [absence?.roundedToDays]);

    useEffect(() => {
        setInitialDates();
    }, [absence?.startsAt, absence?.endsAt]);

    const setInitialDates = () => {
        if (!absence?.startsAt) return;

        const startsAtDt = DateTime.fromISO(absence.startsAt).toJSDate();
        const endsAt = DateTime.fromISO(absence.endsAt).toJSDate();
        if (absence.roundedToDays) {
            endsAt.setDate(endsAt.getDate() - 1);
        }

        setStartDate(startsAtDt);
        setStartTime([startsAtDt.getHours(), startsAtDt.getMinutes()]);

        setEndDate(endsAt);
        setEndTime([endsAt.getHours(), endsAt.getMinutes()]);
    };

    const userOptions = useMemo(() => {
        const options: OptionData<string | null>[] = users.map((user) => ({
            label: user.name,
            value: user.key,
        }));

        options.unshift({
            label: t('commonLabels.all'),
            value: null,
        });

        return options;
    }, [t, users]);

    const teamMemberValue = useMemo(() => {
        return userOptions.find((o) => o.value == teamMemberId);
    }, [userOptions, teamMemberId]);

    const isStartDateValid = startDate <= endDate;
    const isEndDateValid = endDate >= startDate;

    const isStartTimeValid =
        startTime[0] < endTime[0] || (startTime[0] === endTime[0] && startTime[1] < endTime[1]);

    const isEndTimeValid =
        endTime[0] > startTime[0] || (startTime[0] === endTime[0] && endTime[1] > startTime[1]);

    const areDatesChanged = useMemo((): boolean => {
        if (!absence) return false;
        if (!isDateValid(startDate)) return false;
        if (!isDateValid(endDate)) return false;

        const initialStartDateTime = DateTime.fromISO(absence.startsAt);
        const startDateTIme = DateTime.fromJSDate(startDate);

        const initialEndDateTime = DateTime.fromISO(absence.endsAt);
        const endDateTime = DateTime.fromJSDate(endDate);

        if (
            initialStartDateTime.year !== startDateTIme.year ||
            initialStartDateTime.month !== startDateTIme.month ||
            initialStartDateTime.day !== startDateTIme.day ||
            initialStartDateTime.hour !== startTime[0] ||
            initialStartDateTime.minute !== startTime[1] ||
            (!isAllDayMode &&
                (initialEndDateTime.hour !== endTime[0] ||
                    initialEndDateTime.minute !== endTime[1]))
        )
            return true;

        if (isAllDayMode) {
            const initialEndDateTimeMinusDay = initialEndDateTime.minus({ day: 1 });
            if (
                initialEndDateTimeMinusDay.year !== endDateTime.year ||
                initialEndDateTimeMinusDay.month !== endDateTime.month ||
                initialEndDateTimeMinusDay.day !== endDateTime.day
            )
                return true;
        }

        return false;
    }, [absence, endDate, endTime, isAllDayMode, startDate, startTime]);

    const isModified =
        absence &&
        (absence.reason !== reason ||
            absence.notes !== notes ||
            absence.userId !== teamMemberId ||
            absence.roundedToDays !== isAllDayMode ||
            areDatesChanged);

    const canEdit =
        isModified &&
        !!reason &&
        (teamMemberId === null || teamMemberValue != null) &&
        (isAllDayMode
            ? endDate >= startDate
            : endTime[0] > startTime[0] ||
              (endTime[0] == startTime[0] && endTime[1] > startTime[1]));

    const validateUpdateAbsence = useMutation(
        async () => {
            return AbsencesApi.checkSchedule(
                teamMemberId,
                getStartDateAsString(),
                calcAbsenceDurationInMinutes(),
                isAllDayMode
            );
        },
        {
            onSuccess: (count) => {
                if (count > 0) {
                    setCollisionsCount(count);
                } else {
                    updateAbsence.mutate();
                }
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const updateAbsence = useMutation(
        async () => {
            const absenceDto: UpdateAbsenceDto = {
                startsAt: getStartDateAsString(),
                durationInMinutes: calcAbsenceDurationInMinutes(),
                roundedToDays: isAllDayMode,
                notes: notes,
                reason: reason!,
                userId: teamMemberId,
            };

            return AbsencesApi.update(absence.id, absenceDto);
        },
        {
            onSuccess: (absence) => {
                if (absence) {
                    dispatch(absencesActions.addAbsence(absence));

                    toasters.success(
                        t('absences.editPopup.updatedAbsencesBody'),
                        t('absences.editPopup.updatedAbsencesTitle')
                    );
                    clearDataAndClose();
                }
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const deleteAbsence = useMutation(
        async () => {
            return AbsencesApi.delete_(absence.id);
        },
        {
            onSuccess: () => {
                dispatch(absencesActions.removeAbsence(absence.id));

                toasters.success(
                    t('absences.deleteModal.notificationBody'),
                    t('absences.deleteModal.notificationTitle')
                );

                clearDataAndClose();
            },
        }
    );

    const getStartDateAsString = (): string => {
        let startDateMoment = moment(startDate);
        if (isAllDayMode) {
            startDateMoment = startDateMoment.set({ hours: 0, minutes: 0, second: 0 });
        } else {
            startDateMoment.set({ hours: startTime[0], minutes: startTime[1], second: 0 });
        }
        return startDateMoment.toISOString();
    };
    const calcAbsenceDurationInMinutes = (): number => {
        const startDateMoment = moment(startDate);
        let endDateMoment = moment(endDate);
        if (isAllDayMode) {
            startDateMoment.set({ hours: 0, minutes: 0, second: 0 });

            endDateMoment.set({ hours: 0, minutes: 0, second: 0 });
            endDateMoment.add(1, 'days');
        } else {
            endDateMoment = moment(startDate); //to use the same day
            startDateMoment.set({ hours: startTime[0], minutes: startTime[1], second: 0 });
            endDateMoment.set({ hours: endTime[0], minutes: endTime[1], second: 0 });
        }

        const duration = moment.duration(endDateMoment.diff(startDateMoment));
        const diffInMinutes = duration.asMinutes();

        return diffInMinutes;
    };

    function onAbsenceModalClose() {
        if (isModified) setCancelEditAbsenceModalIsOpen(true);
        else clearDataAndClose();
    }

    const clearDataAndClose = () => {
        setIsAllDayMode(absence.roundedToDays);
        setReason(absence.reason);
        setNotes(absence.notes);
        setTeamMemberId(absence.userId);
        setIsDeletePopupOpen(false);
        setInitialDates();

        onClose();
    };

    return (
        <ScheduleAbsencePopupLayout
            open={open}
            onClose={onAbsenceModalClose}
            onDelete={() => {
                setIsDeletePopupOpen(true);
            }}
            userName={absence?.createByUserName || ''}
            {...props}
        >
            <Grid container spacing={3}>
                <Grid item xs={6}>
                    <Dropdown
                        name="teamMemberPicker"
                        cmosVariant="grey"
                        options={userOptions}
                        label={t('absences.editPopup.teamMemberLabel')}
                        placeholder={t('absences.editPopup.selectTeamMember')}
                        isRequired={true}
                        showValidationIndicators={true}
                        value={teamMemberValue}
                        onChange={(option: any) => {
                            setTeamMemberId(option.value);
                        }}
                    />
                </Grid>
                <Grid item xs={6}>
                    <StyledTextField>
                        <TextField
                            isRequired={true}
                            showValidationIndicators
                            name="name"
                            label={t('absences.editPopup.reasonLabel')}
                            placeholder={t('absences.editPopup.reasonPlaceholder')}
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            maxLength={50}
                            cmosVariant="grey"
                        />
                    </StyledTextField>
                </Grid>
                <Grid item xs={12}>
                    <Box>
                        <Switch
                            checked={isAllDayMode}
                            onChange={(_: any, checked: boolean) => setIsAllDayMode(checked)}
                            name="checkedA"
                        />
                        <StyledSwitchLabel>{t('absences.editPopup.allDayLabel')}</StyledSwitchLabel>
                    </Box>
                </Grid>

                <Grid item xs={6}>
                    <DateFormField
                        disablePast
                        isRequired
                        isInvalid={!isStartDateValid}
                        showValidationIndicators
                        name="delivery-date"
                        enableEnterComplete
                        label={t(
                            isAllDayMode
                                ? 'absences.editPopup.startDateLabel'
                                : 'absences.editPopup.dateLabel'
                        )}
                        placeholder={t('appointments.selectADate')}
                        onChange={(date) => {
                            if (date) setStartDate(date);
                        }}
                        value={startDate}
                    />
                </Grid>

                <Grid item xs={6}>
                    {isAllDayMode ? (
                        <DateFormField
                            disablePast
                            isRequired
                            isInvalid={!isEndDateValid}
                            showValidationIndicators
                            name="delivery-date"
                            enableEnterComplete
                            label={t('absences.editPopup.endDateLabel')}
                            placeholder={t('appointments.selectADate')}
                            onChange={(date) => {
                                if (date) setEndDate(date);
                            }}
                            value={endDate}
                        />
                    ) : (
                        <Grid container spacing={2}>
                            <Grid item xs={6}>
                                <TimeFormField
                                    isRequired={true}
                                    isInvalid={!isStartTimeValid}
                                    showValidationIndicators
                                    label={t('absences.editPopup.startTimeLabel')}
                                    name="monday-open"
                                    value={startTime}
                                    onChange={setStartTime}
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <TimeFormField
                                    isRequired={true}
                                    isInvalid={!isEndTimeValid}
                                    showValidationIndicators
                                    label={t('absences.editPopup.endTimeLabel')}
                                    name="monday-open"
                                    value={endTime}
                                    onChange={setEndTime}
                                />
                            </Grid>
                        </Grid>
                    )}
                </Grid>

                <Grid item xs={12}>
                    <TextArea
                        name="name"
                        label={t('absences.editPopup.notesLabel')}
                        placeholder={t('absences.editPopup.notesPlaceholder')}
                        rows={3}
                        value={notes || ''}
                        onChange={(e) => setNotes(e.target.value)}
                        maxLength={250}
                    />
                </Grid>

                <Box display="flex" width="100%" justifyContent="flex-end">
                    <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="space-between"
                        gap={1.25}
                        style={{ padding: 12 }}
                    >
                        <StyledButton
                            onClick={onAbsenceModalClose}
                            label={t('absences.editPopup.cancelButton')}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            cmosSize={'medium'}
                        />
                        <StyledButton
                            onClick={() => validateUpdateAbsence.mutate()}
                            showLoader={updateAbsence.isLoading}
                            label={t('absences.editPopup.saveButton')}
                            cmosVariant={'filled'}
                            cmosSize={'medium'}
                            color={Colors.CM1}
                            disabled={!canEdit}
                        />
                    </Box>
                </Box>
            </Grid>
            <DeleteConfirmationPopup
                open={isDeletePopupOpen}
                title={t('absences.deleteModal.title')}
                body={<div>{t('absences.deleteModal.body')}</div>}
                cancel={t('absences.deleteModal.cancelButton')}
                confirm={t('absences.deleteModal.confirmButton')}
                onConfirm={() => deleteAbsence.mutate()}
                onClose={() => {
                    setIsDeletePopupOpen(false);
                }}
            />
            <ConfirmationModal
                onConfirmation={() => {
                    setCancelEditAbsenceModalIsOpen(false);
                    clearDataAndClose();
                }}
                confirmationCaptionButton={t('absences.continueWithSchedule.yesCancel')}
                cancelCaptionButton={t('absences.continueWithSchedule.goBack')}
                onClose={() => setCancelEditAbsenceModalIsOpen(false)}
                open={cancelEditAbsenceModalIsOpen}
            >
                <CancelText>{t('absences.continueWithSchedule.cancelEditText')}</CancelText>
            </ConfirmationModal>
            <WarningConfirmationPopup
                open={collisionsCount > 0}
                title={t('absences.continueWithSchedule.title')}
                body={
                    <Trans
                        i18nKey="absences.continueWithSchedule.body"
                        values={{ count: collisionsCount }}
                        components={{ 1: <b />, 2: <br /> }}
                    />
                }
                cancel={t('absences.continueWithSchedule.cancelButton')}
                confirm={t('absences.continueWithSchedule.saveButton')}
                onConfirm={() => {
                    setCollisionsCount(0);
                    updateAbsence.mutate();
                }}
                onClose={() => {
                    setCollisionsCount(0);
                }}
                showCloseBtn={false}
            />
        </ScheduleAbsencePopupLayout>
    );
}
const CancelText = styled('div')({
    width: '90%',
    marginLeft: 'auto',
    marginRight: 'auto',
    fontSize: 18,
    fontWeight: 700,
    fontFamily: 'Inter',
    color: 'var(--neutral9)',
});

const StyledButton = styled(Button)({
    width: 130,
    padding: '11px 16px',
});

const StyledTextField = styled('div')(({ theme }) => ({
    ...theme.typography.h11Roboto,
    fontSize: 12,
}));

const StyledSwitchLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h11Inter,
    fontSize: 12,
    fontWeight: 700,
}));
