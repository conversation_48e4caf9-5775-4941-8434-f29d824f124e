import useForceRender from 'common/hooks/useForceRender';
import { createContext, useCallback, useContext, useMemo, useRef } from 'react';
import ScheduleAbsencePopup from './index';

interface IScheduleAbsencePopupContext {
    open(ts: number, userId: string | null, element: HTMLElement): void;
}

const ScheduleAbsencePopupContext = createContext<IScheduleAbsencePopupContext | null>(null);

export function useScheduleAbsencePopup() {
    const ctx = useContext(ScheduleAbsencePopupContext);
    if (!ctx) throw new Error('cannot use ScheduleAppointmentPopupContext');
    return ctx;
}

export function ScheduleAbsencePopupProvider({ children }: React.PropsWithChildren<{}>) {
    const state = useRef({
        ts: 0,
        anchorEl: null as HTMLElement | null,
        userId: '' as string | null,
    });
    const fr = useForceRender();
    const ctx: IScheduleAbsencePopupContext = useMemo(
        () => ({
            open: (ts, userId, element) => {
                state.current.anchorEl = element;
                state.current.ts = ts;
                state.current.userId = userId;
                fr();
            },
        }),
        [fr]
    );

    const close = useCallback(() => {
        state.current.anchorEl = null;
        fr();
    }, [fr]);

    return (
        <ScheduleAbsencePopupContext.Provider value={ctx}>
            <ScheduleAbsencePopup
                ts={state.current.ts}
                onClose={close}
                userId={state.current.userId}
                anchorEl={state.current.anchorEl ?? undefined}
                open={!!state.current.anchorEl}
            />
            {children}
        </ScheduleAbsencePopupContext.Provider>
    );
}
