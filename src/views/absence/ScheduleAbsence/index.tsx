import { Box, Grid, styled, Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useMutation } from '@tanstack/react-query';
import { AbsencesApi, CreateAbsenceDto } from 'api/users';
import { Button } from 'common/components/Button';
import { Text<PERSON>rea, TextField } from 'common/components/Inputs';
import DateFormField from 'common/components/Inputs/DateFormField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { Switch } from 'common/components/Inputs/Switch';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { absencesActions } from 'store/slices/absences';
import { useCurrentUser } from 'store/slices/user';
import { selectActiveUser } from 'store/slices/users';
import ScheduleAbsencePopupLayout from '../ScheduleAbsencePopupLayout';

const useStyles = makeStyles((theme: Theme) => ({
    button: {
        width: 130,
        padding: '11px 16px',
    },
    textField: {
        ...theme.typography.h11Roboto,
        fontSize: 12,
    },
    switchLabel: {
        ...theme.typography.h11Inter,
        fontSize: 12,
        fontWeight: 700,
    },
}));

type ScheduleAbsencePopupProps = {
    ts: number;
    userId: string | null;
    onClose: () => void;
    anchorEl?: HTMLElement;
    open: boolean;
};

export default function ScheduleAbsencePopup({
    ts,
    userId,
    onClose,
    open,
    ...props
}: ScheduleAbsencePopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [reason, setReason] = useState<string | null>(null);
    const [notes, setNotes] = useState<string | null>(null);
    const [isAllDayMode, setIsAllDayMode] = useState<boolean>(false);
    const [startDate, setStartDate] = useState<Date>(new Date());
    const [endDate, setEndDate] = useState<Date>(new Date());
    const [startTime, setStartTime] = useState<[number, number]>([0, 0]);
    const [endTime, setEndTime] = useState<[number, number]>([0, 0]);
    const [teamMemberId, setTeamMemeberId] = useState<string | null>(userId);
    const [collisionsCount, setCollisionsCount] = useState<number>(0);
    const users = useAppSelector(selectActiveUser);
    const dispatch = useDispatch();
    const styles = useStyles();
    const user = useCurrentUser();
    const [cancelEditAbsenceModalIsOpen, setCancelEditAbsenceModalIsOpen] =
        useState<boolean>(false);
    const initialValues = useRef({
        startDate: new Date(),
        endDate: new Date(),
        startTime: [0, 0],
        endTime: [0, 0],
        teamMemberId: userId,
    });

    useEffect(() => {
        setTeamMemeberId(userId);
        initialValues.current = {
            ...initialValues.current,
            teamMemberId: userId,
        };
    }, [userId]);

    useEffect(() => {
        const dt = DateTime.fromMillis(ts).set({ second: 0, millisecond: 0 });
        const jsDate = dt.toJSDate();
        setStartDate(jsDate);
        setEndDate(jsDate);
        setStartTime([dt.hour, dt.minute]);
        const endHours = dt.hour < 23 ? dt.hour + 1 : dt.hour;
        setEndTime([endHours, dt.minute]);
        initialValues.current = {
            ...initialValues.current,
            startDate: jsDate,
            endDate: jsDate,
            startTime: [dt.hour, dt.minute],
            endTime: [endHours, dt.minute],
        };
    }, [ts]);

    const userOptions = useMemo(() => {
        const options: OptionData<string | null>[] = users.map((user) => ({
            label: user.name,
            value: user.key,
        }));

        options.unshift({
            label: t('commonLabels.all'),
            value: null,
        });

        return options;
    }, [t, users]);

    const teamMemberValue = useMemo(() => {
        return userOptions.find((o) => o.value == teamMemberId);
    }, [userOptions, teamMemberId]);

    const isStartDateValid = startDate <= endDate;
    const isEndDateValid = endDate >= startDate;

    const isStartTimeValid =
        startTime[0] < endTime[0] || (startTime[0] === endTime[0] && startTime[1] < endTime[1]);

    const isEndTimeValid =
        endTime[0] > startTime[0] || (startTime[0] === endTime[0] && endTime[1] > startTime[1]);

    const canCreate =
        !!reason &&
        (teamMemberId === null || teamMemberValue != null) &&
        (isAllDayMode
            ? endDate >= startDate
            : endTime[0] > startTime[0] ||
              (endTime[0] == startTime[0] && endTime[1] > startTime[1]));

    const validateCreateAbsence = useMutation(
        async () => {
            return AbsencesApi.checkSchedule(
                teamMemberId,
                startDateAsString,
                absenceDurationInMinutes,
                isAllDayMode
            );
        },
        {
            onSuccess: (count) => {
                if (count > 0) {
                    setCollisionsCount(count);
                } else {
                    createAbsence.mutate();
                }
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const createAbsence = useMutation(
        async () => {
            const absenceDto: CreateAbsenceDto = {
                startsAt: startDateAsString,
                durationInMinutes: absenceDurationInMinutes,
                roundedToDays: isAllDayMode,
                notes: notes,
                reason: reason!,
                userId: teamMemberId,
            };

            return AbsencesApi.create(absenceDto);
        },
        {
            onSuccess: (absence) => {
                if (absence) {
                    dispatch(absencesActions.addAbsence(absence));

                    toasters.success(
                        t('absences.createPopup.createdAbsencesBody', {
                            reason: absence.reason,
                        }),
                        t('absences.createPopup.createdAbsencesTitle')
                    );
                    clearDataAndClose();
                }
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const startDateAsString = (() => {
        let startDateMoment = moment(startDate);
        if (isAllDayMode) {
            startDateMoment = startDateMoment.set({ hours: 0, minutes: 0, second: 0 });
        } else {
            startDateMoment.set({ hours: startTime[0], minutes: startTime[1], second: 0 });
        }
        return startDateMoment.toISOString();
    })();
    const absenceDurationInMinutes = ((): number => {
        const startDateMoment = moment(startDate);
        let endDateMoment = moment(endDate);
        if (isAllDayMode) {
            startDateMoment.set({ hours: 0, minutes: 0, second: 0 });

            endDateMoment.set({ hours: 0, minutes: 0, second: 0 });
            endDateMoment.add(1, 'days');
        } else {
            endDateMoment = moment(startDate); //to use the same day

            startDateMoment.set({
                hours: startTime[0],
                minutes: startTime[1],
                second: 0,
            });
            endDateMoment.set({
                hours: endTime[0],
                minutes: endTime[1],
                second: 0,
            });
        }

        const duration = moment.duration(endDateMoment.diff(startDateMoment));
        const diffInMinutes = duration.asMinutes();

        return diffInMinutes;
    })();

    function onAbsenceModalClose() {
        const hasChanged =
            (reason !== null && reason !== '') ||
            (notes !== null && notes !== '') ||
            isAllDayMode ||
            startDate.getTime() !== initialValues.current.startDate.getTime() ||
            endDate.getTime() !== initialValues.current.endDate.getTime() ||
            startTime[0] !== initialValues.current.startTime[0] ||
            startTime[1] !== initialValues.current.startTime[1] ||
            endTime[0] !== initialValues.current.endTime[0] ||
            endTime[1] !== initialValues.current.endTime[1] ||
            teamMemberId !== initialValues.current.teamMemberId;

        if (hasChanged) setCancelEditAbsenceModalIsOpen(true);
        else clearDataAndClose();
    }

    const clearDataAndClose = () => {
        setIsAllDayMode(false);
        setReason(null);
        setNotes(null);
        setTeamMemeberId(userId);

        onClose();
    };

    return (
        <ScheduleAbsencePopupLayout
            userName={user.displayName}
            open={open}
            onClose={onAbsenceModalClose}
            {...props}
        >
            <Grid container spacing={3}>
                <Grid item xs={6}>
                    <Dropdown
                        name="teamMemberPicker"
                        cmosVariant="grey"
                        options={userOptions}
                        label={t('absences.createPopup.teamMemberLabel')}
                        placeholder={t('absences.createPopup.selectTeamMember')}
                        isRequired={true}
                        showValidationIndicators={true}
                        value={teamMemberValue}
                        onChange={(option: any) => {
                            setTeamMemeberId(option.value);
                        }}
                    />
                </Grid>
                <Grid item xs={6}>
                    <StyledTextField>
                        <TextField
                            isRequired={true}
                            showValidationIndicators
                            name="name"
                            label={t('absences.createPopup.reasonLabel')}
                            placeholder={t('absences.createPopup.reasonPlaceholder')}
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                            maxLength={50}
                            cmosVariant="grey"
                        />
                    </StyledTextField>
                </Grid>
                <Grid item xs={12}>
                    <Box>
                        <Switch
                            checked={isAllDayMode}
                            onChange={(_: any, checked: boolean) => setIsAllDayMode(checked)}
                            name="checkedA"
                        />
                        <StyledSwitchLabel>
                            {t('absences.createPopup.allDayLabel')}
                        </StyledSwitchLabel>
                    </Box>
                </Grid>

                <Grid item xs={6}>
                    <DateFormField
                        disablePast
                        isRequired
                        isInvalid={!isStartDateValid}
                        showValidationIndicators
                        name="delivery-date"
                        enableEnterComplete
                        label={t(
                            isAllDayMode
                                ? 'absences.createPopup.startDateLabel'
                                : 'absences.createPopup.dateLabel'
                        )}
                        placeholder={t('appointments.selectADate')}
                        onChange={(date) => {
                            if (date) setStartDate(date);
                        }}
                        value={startDate}
                    />
                </Grid>

                <Grid item xs={6}>
                    {isAllDayMode ? (
                        <DateFormField
                            disablePast
                            isRequired
                            isInvalid={!isEndDateValid}
                            showValidationIndicators
                            name="delivery-date"
                            enableEnterComplete
                            label={t('absences.createPopup.endDateLabel')}
                            placeholder={t('appointments.selectADate')}
                            onChange={(date) => {
                                if (date) setEndDate(date);
                            }}
                            value={endDate}
                        />
                    ) : (
                        <Grid container spacing={2}>
                            <Grid item xs={6}>
                                <TimeFormField
                                    isRequired={true}
                                    isInvalid={!isStartTimeValid}
                                    showValidationIndicators
                                    label={t('absences.createPopup.startTimeLabel')}
                                    name="monday-open"
                                    value={startTime}
                                    onChange={setStartTime}
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <TimeFormField
                                    isRequired={true}
                                    isInvalid={!isEndTimeValid}
                                    showValidationIndicators
                                    label={t('absences.createPopup.endTimeLabel')}
                                    name="monday-open"
                                    value={endTime}
                                    onChange={setEndTime}
                                />
                            </Grid>
                        </Grid>
                    )}
                </Grid>

                <Grid item xs={12}>
                    <TextArea
                        name="name"
                        label={t('absences.createPopup.notesLabel')}
                        placeholder={t('absences.createPopup.notesPlaceholder')}
                        rows={3}
                        value={notes || ''}
                        onChange={(e) => setNotes(e.target.value)}
                        maxLength={250}
                    />
                </Grid>

                <Box display="flex" width="100%" justifyContent="flex-end">
                    <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="space-between"
                        gap={1.25}
                        style={{ padding: 12 }}
                    >
                        <StyledButton
                            onClick={onAbsenceModalClose}
                            label={t('absences.createPopup.cancelButton')}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            cmosSize={'medium'}
                        />
                        <StyledButton
                            onClick={() => validateCreateAbsence.mutate()}
                            showLoader={createAbsence.isLoading || validateCreateAbsence.isLoading}
                            label={t('absences.createPopup.createButton')}
                            cmosVariant={'filled'}
                            cmosSize={'medium'}
                            color={Colors.CM1}
                            disabled={
                                !canCreate ||
                                createAbsence.isLoading ||
                                validateCreateAbsence.isLoading
                            }
                        />
                    </Box>
                </Box>
            </Grid>
            <WarningConfirmationPopup
                open={collisionsCount > 0}
                title={t('absences.continueWithSchedule.title')}
                body={
                    <Trans
                        i18nKey="absences.continueWithSchedule.body"
                        values={{ count: collisionsCount }}
                        components={{ 1: <b />, 2: <br /> }}
                    />
                }
                cancel={t('absences.continueWithSchedule.cancelButton')}
                confirm={t('absences.continueWithSchedule.saveButton')}
                onConfirm={() => {
                    setCollisionsCount(0);
                    createAbsence.mutate();
                }}
                onClose={() => {
                    setCollisionsCount(0);
                }}
                showCloseBtn={false}
            />
            <ConfirmationModal
                onConfirmation={() => {
                    setCancelEditAbsenceModalIsOpen(false);
                    clearDataAndClose();
                }}
                confirmationCaptionButton={t('absences.continueWithSchedule.yesCancel')}
                cancelCaptionButton={t('absences.continueWithSchedule.goBack')}
                onClose={() => setCancelEditAbsenceModalIsOpen(false)}
                open={cancelEditAbsenceModalIsOpen}
            >
                <CancelText>{t('absences.continueWithSchedule.cancelText')}</CancelText>
            </ConfirmationModal>
        </ScheduleAbsencePopupLayout>
    );
}

const CancelText = styled('div')({
    width: '90%',
    marginLeft: 'auto',
    marginRight: 'auto',
    fontSize: 18,
    fontWeight: 700,
    fontFamily: 'Inter',
    color: 'var(--neutral9)',
});

const StyledButton = styled(Button)({
    width: 130,
    padding: '11px 16px',
});

const StyledTextField = styled('div')(({ theme }) => ({
    ...theme.typography.h11Roboto,
    fontSize: 12,
}));

const StyledSwitchLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h11Inter,
    fontSize: 12,
    fontWeight: 700,
}));
