import { autocompleteClasses } from '@mui/material';
import LocationSelectorBase, {
    LocationSelectorBaseProps,
} from 'common/components/LocationSelector/LocationSelectorBase';

export type LocationSelectorProps = Omit<
    LocationSelectorBaseProps,
    'style' | 'numericPlaceholderAfterCount'
>;

const IndicatorsLocationSelector = (props: LocationSelectorProps) => {
    return (
        <LocationSelectorBase
            numericPlaceholderAfterCount={0}
            sx={{
                width: 250,
                [`& .${autocompleteClasses.inputRoot}`]: {
                    '&:hover': {
                        backgroundColor: '#cce1ff',
                    },
                },
            }}
            {...props}
        />
    );
};

export default IndicatorsLocationSelector;
