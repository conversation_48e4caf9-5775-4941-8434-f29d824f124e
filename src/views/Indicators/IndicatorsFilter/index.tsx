import { Button, IconButton, Menu, MenuItem, menuItemClasses, styled } from '@mui/material';
import IndicatorsAPI, { IndicatorInfo } from 'api/Indicators';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { FiltersIcon } from 'common/components/Icons/FiltersIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch } from 'store';
import { setAllIndicators, setSelectedIndicators } from 'store/slices/indicators';
import { selectSelectedIndicators } from 'store/slices/indicators/selectors';

export default function IndicatorsFilter() {
    const isEnterprise = useIsEnterpriseRoute();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [localSelectedIndicators, setLocalSelectedIndicators] = useState<IndicatorInfo[]>(
        useSelector(selectSelectedIndicators)
    );
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const indicatorsInfo = useMemo((): IndicatorInfo[] => {
        const indicators = !isEnterprise
            ? IndicatorsAPI.getIndicatorsInfo()
            : IndicatorsAPI.getEnterpriseIndicatorsInfo();
        // translation
        const result = indicators.map((indicator) => ({
            type: indicator.type,
            valueType: indicator.valueType,
            name: t('indicators.titles.' + indicator.name),
            group: indicator.group,
            tooltip: indicator.tooltip ? t('indicators.tooltips.' + indicator.tooltip) : undefined,
            tableColumns: !!indicator.tableColumns
                ? indicator.tableColumns!.map((col) => {
                      return {
                          valueType: col.valueType,
                          name: t('indicators.columns.' + indicator.name + '.' + col.name),
                          tooltip: !!col.tooltip
                              ? t(
                                    'indicators.columns.tooltips.' +
                                        indicator.name +
                                        '.' +
                                        col.tooltip
                                )
                              : undefined,
                      };
                  })
                : undefined,
        }));
        dispatch(setAllIndicators(result));
        return result;
    }, [t]);

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(e.target as HTMLElement);
    };

    const toggle = (indicator: IndicatorInfo, selected: boolean) => {
        if (selected) {
            setLocalSelectedIndicators([...localSelectedIndicators, indicator]);
        } else {
            setLocalSelectedIndicators(
                localSelectedIndicators.filter((x) => x.type !== indicator.type)
            );
        }
    };

    const handleApply = () => {
        const list = structuredClone(localSelectedIndicators);
        list.sort((a, b) => {
            const indicatorIds = indicatorsInfo.map((x) => x.type);
            return indicatorIds.indexOf(a.type) - indicatorIds.indexOf(b.type);
        });
        dispatch(setSelectedIndicators(list));
    };

    return (
        <>
            <IconButton size="small" onClick={handleClick}>
                <FiltersIcon fill={anchorEl ? 'var(--cm1)' : 'currentColor'} />
            </IconButton>
            <IndicatorsMenu
                anchorOrigin={{
                    horizontal: 'right',
                    vertical: 'bottom',
                }}
                transformOrigin={{
                    horizontal: 'right',
                    vertical: 'top',
                }}
                onClose={() => setAnchorEl(null)}
                open={!!anchorEl}
                anchorEl={anchorEl}
            >
                <IndicatorsMenuHeader>
                    <div className="title">{t('indicators.filterTitle')}</div>
                    <StyledTypographyButton onClick={handleApply} disabled={false}>
                        {t('indicators.apply')}
                    </StyledTypographyButton>
                </IndicatorsMenuHeader>
                {indicatorsInfo.map((x, i) => {
                    const selected = localSelectedIndicators.map((x) => x.type).includes(x.type);

                    return (
                        <MenuItem
                            disabled={false}
                            key={x.type}
                            onClick={() => toggle(x, !selected)}
                        >
                            {selected ? <CheckBoxIcon /> : <UncheckBoxIcon />} {x.name}
                        </MenuItem>
                    );
                })}
            </IndicatorsMenu>
        </>
    );
}

const IndicatorsMenu = styled(Menu)(({ theme }) => ({
    '& > .MuiPaper-root': {
        borderRadius: 10,
        backgroundColor: 'var(--neutral2)',
    },
    '& .MuiMenu-list': {
        width: 306,
        maxHeight: 'min(80vh, 500px)',
        ...scrollbarStyle(),
        overflowY: 'auto',
        overflowX: 'hidden',

        [`& .${menuItemClasses.root}`]: {
            height: 28,
            fontFamily: 'Inter',
            fontSize: 12,
            fontWeight: 400,
            color: 'var(--neutral6)',
            '&:hover': {
                backgroundColor: 'rgba(0, 105, 255, 0.05)',
                color: `${theme.palette.primary.main}`,
            },
        },
    },
}));

const IndicatorsMenuHeader = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 38,
    margin: '-8px 0 0',
    paddingLeft: 20,
    backgroundColor: 'rgba(166, 166, 166, 0.25)',
    '& > .title': {
        fontFamily: 'Inter',
        fontSize: 12,
        fontWeight: 400,
        color: 'var(--neutral7)',
    },
});

const StyledTypographyButton = styled(Button)(({ theme }) => ({
    width: 'auto',
    height: 'auto',
    padding: 0,
    fontFamily: 'Inter',
    fontSize: 12,
    fontWeight: 700,
    textTransform: 'none',
    color: `${theme.palette.primary.main}`,
    '&:hover': {
        backgroundColor: 'transparent',
    },
}));
