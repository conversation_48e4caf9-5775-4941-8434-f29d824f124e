import { Box, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import IndicatorsAPI, {
    Indicator,
    IndicatorData,
    IndicatorGroup,
    IndicatorInfo,
    IndicatorValue,
    IndicatorValueType,
    IndicatorsGroup,
} from 'api/Indicators';
import InfoTooltip from 'common/components/InfoTooltip';
import DateFormField from 'common/components/Inputs/DateFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { selectFilters, setFilters } from 'store/slices/enterprise/indicators';
import { setFrom, setTo } from 'store/slices/indicators';
import {
    selectAllIndicators,
    selectFrom,
    selectSelectedIndicators,
    selectTo,
} from 'store/slices/indicators/selectors';
import theme from 'theme';
import { useDebounce } from 'use-debounce';
import { PageLayout } from 'views/Components/Page';
import { useHeaderLoading } from 'views/HeaderBar';
import useDocumentTitle from '../../common/hooks/useDocumentTitle';
import IndicatorsFilter from './IndicatorsFilter';
import IndicatorsLocationSelector from './IndicatorsLocationSelector';
import { selectFeatureFlags } from 'store/slices/globalSettingsSlice';

export default function Indicators() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const isEnterprise = useIsEnterpriseRoute();
    const toasters = useToasters();
    const enableAftersalesCrm =
        useAppSelector((r) => r.globalSettings.settings)?.repairShopSettings?.features
            .enableAftersalesCrm ?? false;
    const { aftersalesCrmFeature } = useAppSelector(selectFeatureFlags);
    const titleKey = !aftersalesCrmFeature
        ? 'titles.indicators'
        : enableAftersalesCrm
        ? 'titles.indicatorsWhenCrmEnabled'
        : 'titles.indicators';

    useDocumentTitle(t(titleKey));

    const from = new Date(useSelector(selectFrom) || new Date());
    const to = new Date(useSelector(selectTo) || new Date());
    const selectedIndicators = useSelector(selectSelectedIndicators);
    const allIndicators = useSelector(selectAllIndicators);
    const { locations: selectedLocations } = useAppSelector(selectFilters);

    const [indicators, setIndicators] = useState<IndicatorsGroup[]>([]);
    const [fromLocal, setFromLocal] = useState<Date | null>(from);
    const [toLocal, setToLocal] = useState<Date | null>(to);
    const [isFromOpened, setIsFromOpened] = useState<boolean>(false);
    const [isToOpened, setIsToOpened] = useState<boolean>(false);

    const selectedIndicatorsTypes = useMemo(() => {
        const indicators = selectedIndicators.length > 0 ? selectedIndicators : allIndicators;
        return indicators.map((col) => col.type);
    }, [selectedIndicators, allIndicators]);
    const debouncedIndicatorsDataDependenciesMemo = useMemo(
        () => ({
            selectedIndicatorsIds: selectedIndicatorsTypes,
            selectedLocations,
            from,
            to,
        }),
        [selectedIndicatorsTypes, selectedLocations, from, to]
    );
    const debouncedDependencies = useDebounce(debouncedIndicatorsDataDependenciesMemo, 700);
    const { data: indicatorsData, isFetching } = useQuery(
        ['indicatorsData', debouncedDependencies],
        () => {
            if (!(selectedIndicatorsTypes?.length > 0)) return;

            if (from === null || to === null) {
                throw new Error(
                    'The date range is not valid. Please provide both a start and end date.'
                );
            }

            const currentDate = new Date(new Date().toDateString());
            if (from > to || from > currentDate || to > currentDate) {
                // validation and reset to default
                toasters.danger(
                    t('indicators.dateRangeErrorText'),
                    t('indicators.dateRangeErrorTitle')
                );

                const fromDate = new Date(currentDate);
                fromDate.setDate(currentDate.getDate() - 6);
                dispatch(setFrom(fromDate?.toISOString()));
                dispatch(setTo(currentDate?.toISOString()));
                return;
            }

            const requestData = {
                types: selectedIndicatorsTypes,
                dateFrom: moment(from).format('YYYY-MM-DDTHH:mm:ss.SSS'),
                dateTo: moment(to).format('YYYY-MM-DDTHH:mm:ss.SSS'),
            };
            const shopIds: string[] = selectedLocations || [];
            return !isEnterprise
                ? IndicatorsAPI.getIndicatorsData(requestData)
                : IndicatorsAPI.getEnterpriseIndicatorsData({
                      ...requestData,
                      shopIds,
                  });
        }
    );
    useHeaderLoading(isFetching);

    useEffect(() => {
        if (selectedIndicatorsTypes.length === 0) {
            setIndicators([]);
        }
    }, [selectedIndicatorsTypes.length]);

    useEffect(() => {
        if (!indicatorsData) return;
        const list: IndicatorsGroup[] = [];
        let currentGroupKey: IndicatorGroup | undefined = undefined;
        let currentGroup: IndicatorsGroup;
        const indicators = selectedIndicators.length > 0 ? selectedIndicators : allIndicators;
        indicators.forEach((info: IndicatorInfo) => {
            if (currentGroupKey !== info.group || !info.group) {
                currentGroupKey = info.group;
                currentGroup = { indicators: [], type: !info.group ? 'table' : 'item' };
                list.push(currentGroup);
            }
            const data: IndicatorData = indicatorsData.find((x) => x.type === info.type)!;
            currentGroup.indicators.push({ info, data });
        });
        setIndicators(list);
    }, [indicatorsData]);

    const addThousandsSeparator = function (value: string): string {
        return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    };

    const formatValue = function (
        indValue: IndicatorValue | null,
        type: IndicatorValueType
    ): string {
        if (!indValue || indValue?.value === null) return t('indicators.notAvailable');
        switch (type) {
            case IndicatorValueType.String:
                return indValue.value.toString();
            case IndicatorValueType.Count:
                return addThousandsSeparator(indValue.value.toString());
            case IndicatorValueType.Money:
                return indValue.format!.replace('{0}', indValue.value.toString());
            case IndicatorValueType.Percent:
                return `${indValue.value.toString()}%`;
            case IndicatorValueType.Days:
                return `${indValue.value.toString()} ${
                    Number(indValue.value) === 1 ? t('indicators.day') : t('indicators.days')
                }`;
        }
    };

    const cellAlignment = function (type: IndicatorValueType): string | undefined {
        switch (type) {
            case IndicatorValueType.String:
                return 'align-left';
            case IndicatorValueType.Money:
            case IndicatorValueType.Percent:
                return 'align-right';
            default:
                return undefined;
        }
    };

    const fromChangedHandler = function (newFrom: Date | null) {
        setFromLocal(newFrom);
        if (isFromOpened) {
            dispatch(setFrom(newFrom?.toISOString()));
        }
    };

    const applyFromHandler = function () {
        dispatch(setFrom(fromLocal?.toISOString()));
    };

    const toChangedHandler = function (newTo: Date | null) {
        setToLocal(newTo);
        if (isToOpened) {
            dispatch(setTo(newTo?.toISOString()));
        }
    };

    const applyToHandler = function () {
        dispatch(setTo(toLocal?.toISOString()));
    };

    return (
        <>
            <IndicatorsPageLayout marginTop>
                <SelectorsContainer>
                    <Box display="flex" flexWrap="wrap" flexDirection="row" gap="12px">
                        <Box sx={{ width: 160 }}>
                            <DateFormField
                                name="from"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '& fieldset': {
                                            borderColor: 'var(--cm1)',
                                        },
                                    },
                                }}
                                format={`'${t('indicators.start')}': ${t('dateFormats.short')}`}
                                variant="rounded"
                                enableTextInput
                                value={from}
                                maxDate={to}
                                onChange={fromChangedHandler}
                                onBlur={applyFromHandler}
                                onEnterPress={applyFromHandler}
                                onOpen={() => setIsFromOpened(true)}
                                onClose={() => setIsFromOpened(false)}
                                enableEnterComplete={true}
                                disableFuture={true}
                            />
                        </Box>
                        <Box sx={{ width: 160 }}>
                            <DateFormField
                                name="to"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '& fieldset': {
                                            borderColor: 'var(--cm1)',
                                        },
                                    },
                                }}
                                format={`'${t('indicators.end')}': ${t('dateFormats.short')}`}
                                variant="rounded"
                                enableTextInput
                                value={to}
                                minDate={from}
                                onChange={toChangedHandler}
                                onBlur={applyToHandler}
                                onEnterPress={applyToHandler}
                                onOpen={() => setIsToOpened(true)}
                                onClose={() => setIsToOpened(false)}
                                enableEnterComplete={true}
                                disableFuture={true}
                            />
                        </Box>
                        {isEnterprise && (
                            <Box sx={{ width: 'auto', minWidth: 250 }}>
                                <IndicatorsLocationSelector
                                    value={selectedLocations}
                                    disableSelectNewOption={selectedLocations.length >= 30}
                                    onChange={(locations) => {
                                        dispatch(
                                            setFilters({
                                                locations: locations,
                                            })
                                        );
                                    }}
                                />
                            </Box>
                        )}
                    </Box>
                    <IndicatorsFilter />
                </SelectorsContainer>

                {indicators.map((indicatorsGroup: IndicatorsGroup) => {
                    if (indicatorsGroup.type === 'item') {
                        return (
                            <IndicatorsContainer>
                                {indicatorsGroup.indicators.map((indicator: Indicator) => {
                                    return (
                                        <IndicatorContainer>
                                            <IndicatorTitleContainer>
                                                <IndicatorTitle>
                                                    {indicator.info.name}
                                                </IndicatorTitle>
                                                {!!indicator.info.tooltip && (
                                                    <ItemIndicatorInfoTooltip
                                                        text={indicator.info.tooltip!}
                                                    />
                                                )}
                                            </IndicatorTitleContainer>
                                            <IndicatorValueComponent>
                                                {formatValue(
                                                    indicator.data?.value,
                                                    indicator.info.valueType
                                                )}
                                            </IndicatorValueComponent>
                                        </IndicatorContainer>
                                    );
                                })}
                            </IndicatorsContainer>
                        );
                    }
                    if (indicatorsGroup.type === 'table') {
                        const table = indicatorsGroup.indicators.at(0)!;
                        return (
                            <TableContainer>
                                <TableTitleContainer>
                                    <TableTitle>{table.info.name}</TableTitle>
                                    {!!table.info.tooltip && (
                                        <InfoTooltip text={table.info.tooltip!} />
                                    )}
                                </TableTitleContainer>
                                <Table>
                                    <thead>
                                        <TableRow className="table-header">
                                            {table.info.tableColumns!.map((column, idx) => (
                                                <TableHeaderCell
                                                    className={
                                                        idx === 0 ? 'first-column' : undefined
                                                    }
                                                >
                                                    {column.name}
                                                    {!!column.tooltip && (
                                                        <InfoTooltip text={column.tooltip!} />
                                                    )}
                                                </TableHeaderCell>
                                            ))}
                                        </TableRow>
                                    </thead>
                                </Table>
                                <TableScroll>
                                    <Table>
                                        <tbody>
                                            {!!table.data?.table ? (
                                                table.data.table.map((row: object[]) => (
                                                    <TableRow>
                                                        {row.map((cell: object, idx) => (
                                                            <TableCell
                                                                className={`${cellAlignment(
                                                                    table.info.tableColumns![idx]
                                                                        .valueType
                                                                )}
                                                             ${
                                                                 idx === 0
                                                                     ? 'first-column'
                                                                     : undefined
                                                             }`}
                                                            >
                                                                {formatValue(
                                                                    cell as IndicatorValue,
                                                                    table.info.tableColumns![idx]
                                                                        .valueType
                                                                )}
                                                            </TableCell>
                                                        ))}
                                                    </TableRow>
                                                ))
                                            ) : (
                                                <></>
                                            )}
                                        </tbody>
                                    </Table>
                                </TableScroll>
                            </TableContainer>
                        );
                    }
                })}
            </IndicatorsPageLayout>
        </>
    );
}

const IndicatorsPageLayout = styled(PageLayout)(() => ({
    //width: 1158
}));

const SelectorsContainer = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    //marginBottom: 30,
}));

const IndicatorsContainer = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    gap: 20,
    marginTop: 30,
}));

const IndicatorContainer = styled('div')(() => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    height: 119,
    width: '100%',
    padding: '20px 0',

    backgroundColor: 'white',
    borderRadius: 12,
    boxShadow: '0px 5px 16px 0px rgba(198, 198, 198, 0.09)',
}));

const IndicatorTitleContainer = styled('div')(() => ({
    display: 'flex',
    alignItems: 'flex-start',
    padding: '0 18px',
}));
const IndicatorTitle = styled('div')(() => ({
    fontFamily: 'Roboto',
    fontSize: 18,
    lineHeight: '18px',
    fontWeight: 700,
    color: theme.palette.neutral[9],
    marginRight: 5,
}));
const IndicatorValueComponent = styled('div')(() => ({
    display: 'flex',
    padding: '0 22px',
    color: 'var(--cm1)',
    fontFamily: 'Inter',
    fontSize: 26,
    lineHeight: '30px',
    fontWeight: 700,
}));

const TableContainer = styled('div')(() => ({
    marginTop: 30,
    padding: '36px 53px',
    backgroundColor: 'white',
    borderRadius: 12,
    boxShadow: '0px 5px 16px 0px rgba(198, 198, 198, 0.09)',
}));
const TableTitleContainer = styled('div')(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
}));
const TableTitle = styled('div')(() => ({
    fontFamily: 'Roboto',
    fontSize: 18,
    lineHeight: '18px',
    fontWeight: 700,
    color: theme.palette.neutral[9],
    marginRight: 5,
}));
const Table = styled('table')(() => ({
    width: '100%',
    tableLayout: 'fixed',
    borderCollapse: 'collapse',
    marginTop: 30,
}));
const TableScroll = styled('div')(() => ({
    overflowY: 'scroll',
    maxHeight: 200,
    paddingRight: 10,
    marginRight: -16,

    [`& > table`]: {
        marginTop: -1,
    },

    ...scrollbarStyle(),
}));
const TableRow = styled('tr')(() => ({
    height: 39,
    [`&.table-header`]: {
        backgroundColor: 'rgba(0, 105, 255, 0.20)',
        height: 56,
    },
}));
const TableCell = styled('td')(() => ({
    border: '1px solid #cccccc',
    padding: '0 22px',
    color: theme.palette.neutral[9],
    fontFamily: 'Roboto',
    fontSize: 16,
    lineHeight: '16px',
    fontWeight: 400,
    textAlign: 'center',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    [`&.first-column`]: {
        width: '25%',
    },
    [`&.align-left`]: {
        textAlign: 'left',
    },
    [`&.align-right`]: {
        textAlign: 'right',
    },
}));
const TableHeaderCell = styled(TableCell)(() => ({
    height: 39,
    fontWeight: 500,
    whiteSpace: 'normal',
}));
const ItemIndicatorInfoTooltip = styled(InfoTooltip)(() => ({
    marginTop: '2px',
}));
