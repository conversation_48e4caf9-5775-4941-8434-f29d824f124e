import { MenuItem } from '@mui/material';
import { SSelect } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/appointments';
import { selectFilters } from 'store/slices/appointments/selectors';

export default function ModeSelector() {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const { mode } = useAppSelector(selectFilters);

    return (
        <SSelect
            value={mode}
            onChange={(e) => dispatch(setFilters({ mode: e.target.value as any }))}
        >
            <MenuItem value="week">{t('appointments.week')}</MenuItem>
            <MenuItem value="day">{t('appointments.day')}</MenuItem>
        </SSelect>
    );
}
