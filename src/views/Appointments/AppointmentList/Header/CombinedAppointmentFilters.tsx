import { ExpandLess, ExpandMore } from '@mui/icons-material';
import {
    InputAdornment,
    OutlinedInput,
    Popover,
    outlinedInputClasses,
    styled,
} from '@mui/material';
import { AppointmentStatus } from 'api/appointments';
import { Button } from 'common/components/Button';
import { Checkbox, RadioButton } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import isEqual from 'lodash/isEqual';
import { useCallback, useId, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectFilters, setFilters } from 'store/slices/appointments';
import { useRepairShopServiceAdvisors } from 'views/Appointments/common';
import { APPOINTMENTS_STATUS_TRANSLATION_KEYS } from './AppointmentStatusSelector';

export default function CombinedAppointmentFilters() {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState(false);
    const filters = useSelector(selectFilters);
    const dispatch = useDispatch();

    const [state, setState] = useState({
        mode: filters.mode,
        statuses: filters.statuses as (AppointmentStatus | '__all')[],
        advisors: filters.advisors,
    });

    const applyFilters = () => {
        dispatch(
            setFilters({
                mode: state.mode,
                advisors: state.advisors.toSorted(),
                statuses: (
                    state.statuses.filter((x) => x !== '__all') as AppointmentStatus[]
                ).toSorted(),
            })
        );
    };

    const resetState = () => {
        setState({
            mode: filters.mode,
            statuses: filters.statuses as (AppointmentStatus | '__all')[],
            advisors: filters.advisors,
        });
    };

    const somethingChanged = useMemo(
        () =>
            !(
                state.mode === filters.mode &&
                isEqual(state.statuses.toSorted(), filters.statuses.toSorted()) &&
                isEqual(state.advisors.toSorted(), filters.advisors.toSorted())
            ),
        [state, filters]
    );

    const { t } = useAppTranslation();

    return (
        <>
            <StyledOutlinedInput
                ref={setAnchorEl}
                readOnly
                onClick={() => setOpen(true)}
                inputComponent={FakeDropdownInputComponent}
                notched
                placeholder={t('appointments.filters.filters')}
                endAdornment={
                    <InputAdornment sx={{ color: 'currentcolor' }} position="end">
                        {open ? <ExpandLess /> : <ExpandMore />}
                    </InputAdornment>
                }
            />
            <Popover
                transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                onClose={() => {
                    setOpen(false);
                }}
                TransitionProps={{
                    onTransitionEnd: () => {
                        if (!open) {
                            resetState();
                        }
                    },
                }}
                anchorEl={anchorEl}
                open={open}
                slotProps={{
                    paper: {
                        sx: (theme) => ({
                            width: 200,
                            minHeight: 100,
                            boxShadow: 'none',
                            border: `1px solid ${theme.palette.neutral[4]}`,
                            borderRadius: 2,
                        }),
                    },
                }}
            >
                <PopupHeader>
                    <span>{t('appointments.filters.selectFilters')}</span>
                    <Button
                        disabled={!somethingChanged}
                        sx={{ fontWeight: 'normal !important' }}
                        cmosVariant="typography"
                        cmosSize="small"
                        onClick={() => {
                            applyFilters();
                            setOpen(false);
                        }}
                    >
                        {t('appointments.filters.apply')}
                    </Button>
                </PopupHeader>
                <StyledContent>
                    <ViewSection
                        mode={state.mode}
                        onChange={(mode) => setState((x) => ({ ...x, mode }))}
                    />
                    <StatusSection
                        statuses={state.statuses}
                        onChange={(statuses) => setState((x) => ({ ...x, statuses }))}
                    />
                    <AdvisorsSection
                        advisors={state.advisors}
                        onChange={(advisors) => setState((x) => ({ ...x, advisors }))}
                    />
                </StyledContent>
            </Popover>
        </>
    );
}

const StyledOutlinedInput = styled(OutlinedInput)(({ theme }) => ({
    cursor: 'pointer',
    height: 32,
    borderRadius: 16,
    width: 115,
    minWidth: 80,
    color: `${theme.palette.primary.main} !important`,

    [`& .${outlinedInputClasses.notchedOutline}`]: {
        border: `1px solid ${theme.palette.primary.main}`,
    },

    [`:hover:not(.Mui-focused)`]: {
        [`& .${outlinedInputClasses.notchedOutline}`]: {
            border: `1px solid ${theme.palette.primary.main}`,
        },
    },
}));

const StyledContent = styled('div')({
    padding: '8px 16px 8px 16px',
    overflow: 'auto',
    ...scrollbarStyle(),
    maxHeight: 'min(75vh, 600px)',
});

const SectionHeader = styled('header')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    marginBottom: 8,
}));

const Section = styled('section')(({ theme }) => ({
    marginBottom: 8,
}));

const FakeDropdownInputComponent = styled('input')(({ theme }) => ({
    cursor: 'inherit',
    padding: '0 0 0 16px !important',
    height: '100% !important',

    '::placeholder': {
        fontWeight: 'bold',
        opacity: '1 !important',
    },
}));

const PopupHeader = styled('header')(({ theme }) => ({
    background: theme.palette.neutral[3],
    padding: '8px 16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
}));

function ViewSection({
    mode,
    onChange,
}: {
    mode: 'week' | 'day';
    onChange: (mode: 'week' | 'day') => void;
}) {
    const { t } = useAppTranslation();

    return (
        <>
            <SectionHeader>{t('appointments.filters.view')}</SectionHeader>
            <Section>
                <RadioButton
                    label={t('appointments.day')}
                    checked={mode === 'day'}
                    onChange={(checked) => {
                        if (checked) {
                            onChange('day');
                        }
                    }}
                />
                <RadioButton
                    label={t('appointments.week')}
                    checked={mode === 'week'}
                    onChange={(checked) => {
                        if (checked) {
                            onChange('week');
                        }
                    }}
                />
            </Section>
        </>
    );
}

function StatusSection({
    statuses,
    onChange,
}: {
    statuses: (AppointmentStatus | '__all')[];
    onChange: (statuses: (AppointmentStatus | '__all')[]) => void;
}) {
    const { t } = useAppTranslation();
    const id = useId();

    const setStatus = useCallback(
        (status: AppointmentStatus, selected: boolean) => {
            if (selected) {
                onChange([...statuses.filter((x) => x !== status), status]);
            } else {
                onChange(statuses.filter((x) => x !== status));
            }
        },
        [onChange, statuses]
    );

    return (
        <>
            <SectionHeader>{t('appointments.filters.status')}</SectionHeader>

            <div>
                <StyledCheckbox
                    id={'_' + id + '_all'}
                    onChange={(_, newChecked) => {
                        if (newChecked) {
                            onChange([]);
                        }
                    }}
                    checked={statuses.length === 0}
                />
                <StyledLabel htmlFor={'_' + id + '_all'} active={statuses.length === 0}>
                    {t('appointments.allAppointments')}
                </StyledLabel>
            </div>

            <Section>
                {Object.entries(APPOINTMENTS_STATUS_TRANSLATION_KEYS).map(([value, tKey]) => {
                    const checked = statuses.includes(value as AppointmentStatus);
                    const checkboxName = '_' + id + value;

                    return (
                        <div>
                            <StyledCheckbox
                                id={checkboxName}
                                onChange={(_, newChecked) =>
                                    setStatus(value as AppointmentStatus, newChecked)
                                }
                                key={value}
                                checked={checked}
                            />
                            <StyledLabel htmlFor={checkboxName} active={checked}>
                                {t(tKey)}
                            </StyledLabel>
                        </div>
                    );
                })}
            </Section>
        </>
    );
}

const StyledCheckbox = styled(Checkbox)({
    padding: 0,
});

const StyledLabel = styled('label', {
    shouldForwardProp: (prop) => prop !== 'active',
})<{ active: boolean }>(({ theme, active }) => ({
    display: 'inline-flex',
    marginLeft: 5,
    color: active ? theme.palette.primary.main : theme.palette.neutral[6],
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    cursor: 'pointer',
    alignItems: 'center',
}));

function AdvisorsSection({
    advisors,
    onChange,
}: {
    advisors: string[];
    onChange: (advisors: string[]) => void;
}) {
    const { t } = useAppTranslation();
    const id = useId();
    const allAdvisors = useRepairShopServiceAdvisors();
    const sortedAdvisors = useMemo(
        () => allAdvisors.toSorted((a, b) => a.name.localeCompare(b.name)),
        [allAdvisors]
    );

    const setAdvisor = useCallback(
        (advisor: string, selected: boolean) => {
            if (selected) {
                if (!advisors.includes(advisor)) {
                    onChange([...advisors, advisor]);
                }
            } else {
                onChange(advisors.filter((x) => x !== advisor));
            }
        },
        [onChange, advisors]
    );

    return (
        <>
            <SectionHeader>{t('appointments.filters.advisors')}</SectionHeader>
            <div>
                <StyledCheckbox
                    id={'_' + id + '_all'}
                    onChange={(_, newChecked) => {
                        if (newChecked) {
                            onChange([]);
                        }
                    }}
                    checked={advisors.length === 0}
                />
                <StyledLabel htmlFor={'_' + id + '_all'} active={advisors.length === 0}>
                    <svg
                        style={{ marginRight: 6 }}
                        width="9"
                        height="9"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx="6" cy="6" r="5.5" fill="white" stroke="#C9CDD3" />
                        <line
                            x1="1.67075"
                            y1="9.62371"
                            x2="9.67075"
                            y2="2.62371"
                            stroke="#C9CDD3"
                        />
                    </svg>
                    <span>{t('appointments.allAdvisors')}</span>
                </StyledLabel>
            </div>
            {sortedAdvisors.map((x) => {
                const checkboxName = '_' + id + x.id;
                const checked = advisors.includes(x.id);

                return (
                    <div>
                        <StyledCheckbox
                            id={checkboxName}
                            onChange={(_, newChecked) => setAdvisor(x.id, newChecked)}
                            key={x.id}
                            checked={checked}
                        />
                        <StyledLabel htmlFor={checkboxName} active={checked}>
                            <Circle sx={{ backgroundColor: x.color ?? '#aaa' }} />
                            <span>{x.name}</span>
                        </StyledLabel>
                    </div>
                );
            })}
        </>
    );
}

const Circle = styled('div')({
    height: 9,
    width: 9,
    borderRadius: 6,
    marginRight: 6,
});
