import { useMediaQuery } from '@mui/material';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useSelector } from 'react-redux';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useRepairShopAppointmentSettings } from 'views/Appointments/common';
import useQueryParam from 'common/hooks/useQueryParam';

export default function CreateAppointmentButton() {
    const { t } = useAppTranslation();
    const appointmentSettings = useRepairShopAppointmentSettings();
    const userPermission = useSelector(selectUserPermission);
    const synchronizeAppointmentsEnabled =
        appointmentSettings?.synchronizeAppointmentsEnabled ?? true;

    const isSmall = useMediaQuery('(max-width: 900px)');

    const link = useNewAppointmentLink();

    return (
        <Button
            href={link}
            cmosVariant={'filled'}
            disabled={synchronizeAppointmentsEnabled || !userPermission.allowEditAppointments}
            label={isSmall ? undefined : t('appointments.newAppointment')}
            Icon={isSmall ? PlusIcon : undefined}
            sx={{
                minWidth: '32px !important',
                width: isSmall ? undefined : '170px',
            }}
        />
    );
}

const useNewAppointmentLink = (): string => {
    const [vehicleIdParam] = useQueryParam('vehicleId');
    const [customerIdParam] = useQueryParam('customerId');
    const searchParams = new URLSearchParams({});

    if (vehicleIdParam) {
        searchParams.set('vehicleId', vehicleIdParam);
    }
    if (customerIdParam) {
        searchParams.set('customerId', customerIdParam);
    }

    return `${ROUTES.APPOINTMENTS.BASE}${
        ROUTES.APPOINTMENTS.SUBROUTES.NEW
    }?${searchParams.toString()}`;
};
