import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useNow from 'common/hooks/useNow';
import moment from 'moment';
import { useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { setFilters } from 'store/slices/appointments';
import { selectFilters } from 'store/slices/appointments/selectors';

export default function BackToNowButton() {
    const { mode, date } = useAppSelector(selectFilters);
    const { t } = useAppTranslation();
    const dispatch = useDispatch();

    const returnToNow = useCallback(
        () =>
            dispatch(
                setFilters({
                    date: moment().format('YYYY-MM-DD'),
                })
            ),
        [dispatch]
    );

    const nowDate = useNow('day');
    const shown = useMemo(() => {
        const now = moment(nowDate);

        if (mode === 'week') {
            return (
                now.startOf('isoWeek').format('YYYY-MM-DD') !==
                moment(date).startOf('isoWeek').format('YYYY-MM-DD')
            );
        } else {
            return !now.isSame(date, 'd');
        }
    }, [mode, date, nowDate]);

    if (!shown) return null;

    return (
        // empty div is necessary to avoid width of the button collapsing when there is not enough space
        <div>
            <Button
                label={t('appointments.backToToday')}
                cmosVariant="stroke"
                onClick={returnToNow}
            />
        </div>
    );
}
