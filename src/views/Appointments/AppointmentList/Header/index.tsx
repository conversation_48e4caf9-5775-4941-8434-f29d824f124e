import { styled } from '@mui/material';
import RefreshAppointmentsButton from 'views/Appointments/RefreshAppointmentsButton';
import { useRepairShopAppointmentSettings } from 'views/Appointments/common';
import AppointmentsFilters from './AppointmentFilters';
import BackToNowButton from './BackToNowButton';
import CreateAppointmentButton from './CreateAppointmentButton';
import DateSelector from './DateSelector';
import PreviewButton from './PreviewButton';

const Root = styled('div')(({ theme }) => ({
    '&::after, &::before': {
        width: 1,
        backgroundColor: theme.palette.neutral[5],
        content: '""',
        display: 'block',
        left: 0,
        height: 30,
        top: '50%',
        transform: 'translateY(-50%)',
        position: 'absolute',
    },
    '&::after': {
        right: 0,
        left: 'initial',
    },
    gap: 10,
    position: 'relative',
    margin: '0 30px',
    padding: '0 30px',
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItem: 'center',
    color: theme.palette.neutral[5],
    alignItems: 'center',
}));

const Filler = styled('div')({
    flexGrow: 1,
});

type HeaderProps = {
    onRefetch: () => void;
    activeSuccessSync: () => void;
};

export default function Header({ onRefetch, activeSuccessSync }: HeaderProps) {
    const appointmentSettings = useRepairShopAppointmentSettings();
    const synchronizeAppointmentsEnabled =
        appointmentSettings?.synchronizeAppointmentsEnabled ?? true;

    return (
        <Root>
            <DateSelector />
            <BackToNowButton />

            <PreviewButton />
            <Filler />
            <AppointmentsFilters />
            {synchronizeAppointmentsEnabled && (
                <RefreshAppointmentsButton
                    onRefreshed={() => onRefetch()}
                    {...{ activeSuccessSync }}
                />
            )}
            <CreateAppointmentButton />
        </Root>
    );
}
