import { MenuItem, useMediaQuery } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { SSelectMulti } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/appointments';
import { selectFilters } from 'store/slices/appointments/selectors';

export type AppointmentStatusMenu =
    | 'Unconfirmed'
    | 'Confirmed'
    | 'OrderCreated'
    | 'CustomerDidNotArrive';

export const APPOINTMENTS_STATUS_TRANSLATION_KEYS: Record<AppointmentStatusMenu, string> = {
    Confirmed: 'appointments.status.confirmed',
    Unconfirmed: 'appointments.status.unconfirmed',
    CustomerDidNotArrive: 'appointments.status.customerDidNotArrive',
    OrderCreated: 'appointments.status.orderCreated',
};

export default function AppointmentStatusSelector() {
    const { statuses } = useAppSelector(selectFilters);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const shorter = useMediaQuery('(max-width:1400px)');

    const setStatuses = useCallback(
        (value: (AppointmentStatusMenu | '__all')[]) => {
            if (value.length > 0 && value[value.length - 1] === '__all') {
                dispatch(
                    setFilters({
                        statuses: [],
                    })
                );
            } else {
                dispatch(
                    setFilters({
                        statuses: value.filter((x) => x !== '__all') as AppointmentStatusMenu[],
                    })
                );
            }
        },
        [dispatch]
    );

    return (
        <SSelectMulti
            style={{ width: shorter ? 120 : 200 }}
            value={statuses.length === 0 ? ['__all'] : statuses}
            displayEmpty
            renderValue={(x) => {
                const s = x as AppointmentStatusMenu[];
                if (s.length === 0 || (s.length === 1 && (s[0] as string) === '__all'))
                    return t('appointments.allAppointments');
                const first = t(APPOINTMENTS_STATUS_TRANSLATION_KEYS[s[0]]);
                if (s.length > 1) return `${first} +${s.length - 1}`;
                return first;
            }}
            onChange={(e) =>
                setStatuses(
                    (e.target.value instanceof Array ? e.target.value : []) as (
                        | AppointmentStatusMenu
                        | '__all'
                    )[]
                )
            }
        >
            <MenuItem value="__all">
                {statuses.length === 0 ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                {t('appointments.allAppointments')}
            </MenuItem>
            {Object.entries(APPOINTMENTS_STATUS_TRANSLATION_KEYS).map(([value, tKey]) => (
                <MenuItem key={value} value={value}>
                    {statuses.includes(value as AppointmentStatusMenu) ? (
                        <CheckBoxIcon />
                    ) : (
                        <UncheckBoxIcon />
                    )}
                    {t(tKey)}
                </MenuItem>
            ))}
        </SSelectMulti>
    );
}
