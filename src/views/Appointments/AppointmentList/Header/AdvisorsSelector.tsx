import {
    Backdrop,
    Grow,
    InputAdornment,
    ListItem,
    Popper,
    PopperProps,
    TextField,
    UseAutocompleteProps,
    styled,
    useMediaQuery,
} from '@mui/material';
import { ServiceAdvisorDto } from 'api/appointments';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { CircleIcon } from 'common/components/Icons/CircleIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { HTMLAttributes, useCallback, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/appointments';
import { selectFilters } from 'store/slices/appointments/selectors';
import { useRepairShopServiceAdvisors } from 'views/Appointments/common';

const Input = styled(TextField)(({ theme }) => ({
    minWidth: 100,
    cursor: 'pointer',

    '& > .MuiInputBase-root': {
        ...theme.typography.h6Inter,
        cursor: 'pointer',
        color: theme.palette.primary.main,
        display: 'flex',
        alignItems: 'center',
        paddingTop: 5,
        paddingBottom: 5,
        height: 32,
        padding: '0 7px !important',
        borderRadius: 20,
    },
    '& .MuiInputBase-input': {
        cursor: 'pointer',
        '&::placeholder': {
            color: 'var(--cm1)',
            fontWeight: 'bold',
            opacity: 1,
        },
    },
}));

const CustomAutocomplete = styled(SAutocomplete)({
    cursor: 'pointer',

    '& .MuiFormControl-root': {
        margin: 0,
    },
}) as typeof SAutocomplete;

export default function AdvisorsSelector() {
    const { advisors: selectedAdvisors } = useAppSelector(selectFilters);
    const advisors = useRepairShopServiceAdvisors();
    const { t } = useAppTranslation();
    const shorter = useMediaQuery('(max-width:1300px)');

    const dispatch = useAppDispatch();

    const handleChange: UseAutocompleteProps<
        ServiceAdvisorDto | null,
        true,
        true,
        false
    >['onChange'] = (event, newValue, reason, details) => {
        const isAllAdvisorsOption = details?.option === null;

        if (reason === 'selectOption' && isAllAdvisorsOption) {
            dispatch(
                setFilters({
                    advisors: [],
                })
            );
        } else {
            dispatch(
                setFilters({
                    advisors: newValue.filter((x) => x !== null).map((x) => x!.id),
                })
            );
        }
        return;
    };

    const value = useMemo(
        () =>
            selectedAdvisors.length === 0
                ? [null]
                : selectedAdvisors
                      .map((id) => advisors?.find((x) => x.id === id))
                      .filter((x): x is ServiceAdvisorDto => !!x),
        [selectedAdvisors, advisors]
    );
    const [focus, setFocus] = useState(false);
    const placeholder = useMemo(() => {
        if (focus) return t('commonLabels.search') + '...';
        if (selectedAdvisors.length === 0) return t('appointments.allAdvisors');

        const advisor =
            advisors?.find((x) => x.id === selectedAdvisors[0])?.name ?? selectedAdvisors[0];
        if (selectedAdvisors.length > 1) return `${advisor} +${selectedAdvisors.length - 1}`;
        return advisor;
    }, [t, selectedAdvisors, advisors, focus]);

    return (
        <>
            <CustomAutocomplete<ServiceAdvisorDto | null, true>
                style={{ width: shorter ? 120 : 200 }}
                value={value}
                noOptionsText={t('commonLabels.noDataSelector')}
                getOptionLabel={(x) => (x ? x.name : '')}
                onChange={handleChange}
                renderInput={({ InputProps, ...params }) => {
                    return (
                        <Input
                            {...params}
                            placeholder={placeholder}
                            variant="outlined"
                            margin="normal"
                            InputProps={{
                                ...InputProps,
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <DownIcon
                                            style={{
                                                backgroundColor: '#fff',
                                                transform: `rotate(${focus ? '180deg' : '0deg'})`,
                                            }}
                                        />
                                    </InputAdornment>
                                ),
                            }}
                        />
                    );
                }}
                renderOption={useCallback(
                    (props: HTMLAttributes<HTMLLIElement>, o: ServiceAdvisorDto | null) => {
                        if (!o)
                            return (
                                <ListItem {...props}>
                                    {selectedAdvisors.length === 0 ? (
                                        <CheckBoxIcon />
                                    ) : (
                                        <UncheckBoxIcon />
                                    )}
                                    <svg
                                        style={{ margin: 4.5 }}
                                        width="12"
                                        height="12"
                                        viewBox="0 0 12 12"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <circle
                                            cx="6"
                                            cy="6"
                                            r="5.5"
                                            fill="white"
                                            stroke="#C9CDD3"
                                        />
                                        <line
                                            x1="1.67075"
                                            y1="9.62371"
                                            x2="9.67075"
                                            y2="2.62371"
                                            stroke="#C9CDD3"
                                        />
                                    </svg>
                                    {t('appointments.allAdvisors')}
                                </ListItem>
                            );
                        return (
                            <ListItem {...props}>
                                {selectedAdvisors.includes(o.id) ? (
                                    <CheckBoxIcon />
                                ) : (
                                    <UncheckBoxIcon />
                                )}
                                <CircleIcon size={20} fill={o.color ?? '#aaa'} /> {o.name}
                            </ListItem>
                        );
                    },
                    [t, selectedAdvisors]
                )}
                options={[null, ...(advisors ?? [])]}
                disableCloseOnSelect
                disableClearable
                renderTags={(_) => null}
                onFocus={() => setFocus(true)}
                onBlur={() => setFocus(false)}
                openOnFocus
                multiple
                PopperComponent={PopperComponent}
            />
        </>
    );
}

const PopperComponent: React.ComponentType<PopperProps> = ({
    children,
    className,
    open,
    ...props
}) => {
    return (
        <Backdrop style={{ zIndex: 3, backgroundColor: 'transparent' }} open={open}>
            <StyledPopper transition {...props} className={className} open={open}>
                {({ TransitionProps }) => (
                    <Grow {...TransitionProps}>{children as React.ReactElement<any, any>}</Grow>
                )}
            </StyledPopper>
        </Backdrop>
    );
};

const StyledPopper = styled(Popper)(({ theme }) => ({
    '& .MuiPaper-root': {
        boxShadow: 'none',
        border: '1px solid var(--neutral5)',
        '& .MuiAutocomplete-option': {
            '&:hover': {
                backgroundColor: rgba(theme.palette.primary.main, 0.03),
            },
            '&[aria-selected=true]': {
                backgroundColor: rgba(theme.palette.primary.main, 0.08),
                '&:hover': {
                    backgroundColor: rgba(theme.palette.primary.main, 0.12),
                },
            },
        },
    },
    '& .MuiAutocomplete-listbox': {
        ...scrollbarStyle(),
    },
    '& .MuiAutocomplete-noOptions': {
        color: 'var(--cm1)',
        fontWeight: 'bold',
        textAlign: 'center',
    },
}));
