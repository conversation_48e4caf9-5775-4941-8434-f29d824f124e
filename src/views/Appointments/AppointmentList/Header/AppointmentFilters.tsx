import { useMediaQuery } from '@mui/material';
import AdvisorsSelector from './AdvisorsSelector';
import AppointmentStatusSelector from './AppointmentStatusSelector';
import CombinedAppointmentFilters from './CombinedAppointmentFilters';
import ModeSelector from './ModeSelector';

export default function AppointmentsFilters() {
    const isSmall = useMediaQuery('(max-width: 1440px)');

    const body = (
        <>
            <ModeSelector />
            <AppointmentStatusSelector />
            <AdvisorsSelector />
        </>
    );

    if (isSmall) {
        return <CombinedAppointmentFilters />;
    }

    return body;
}
