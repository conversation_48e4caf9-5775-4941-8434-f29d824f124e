import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectFilters } from 'store/slices/appointments/selectors';
import { CustomHeaderContent, useHeaderLoading } from 'views/HeaderBar';
import DayScheduler from '../Scheduler/DayScheduler';
import WeekScheduler from '../Scheduler/WeekScheduler';
import { Appointment } from '../Scheduler/_common';
import {
    useAppointmentsQuery,
    useRepairShopAppointmentSettings,
    useRepairShopServiceAdvisors,
} from '../common';
import Header from './Header';

import { styled } from '@mui/material';
import { legacyAppointmentStatusNotNew } from 'api/appointments';
import { PermissionsGate } from 'views/Components/AuthorizationGate';
import { EditAbsencePopupProvider } from 'views/absence/EditAbsence/context';
import { ScheduleAbsencePopupProvider } from 'views/absence/ScheduleAbsence/context';
import { AppointmentPopupProvider } from '../Scheduler/AppointmentPopup';
import { ScheduleAppointmentActionsMenuContextProvider } from '../Scheduler/ScheduleAppointmentActionsMenu';
import { setFilters } from 'store/slices/appointments';
import { useAppDispatch } from 'store';
import useQueryParam from 'common/hooks/useQueryParam';

const defaultIntervalMilliseconds = 120000;

function AppointmentListInner() {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    useApplyQueryFilters();

    const appointmentSettings = useRepairShopAppointmentSettings();
    const appointmentDuration = appointmentSettings
        ? Math.max(5, appointmentSettings.duration)
        : 30;
    const { date, advisors: selectedAdvisors, statuses, mode } = useSelector(selectFilters);
    const [refetchInterval, setRefetchInterval] = useState<number>(defaultIntervalMilliseconds);

    useDocumentTitle(t('titles.appointments'));

    const advisors = useRepairShopServiceAdvisors();

    const {
        appointments: data,
        absences: allAbsences,
        isFetching,
        refetch,
        activeSuccessSync,
    } = useAppointmentsQuery(date, {
        refetchInterval,
        onSuccess: (data) => {
            calculateRefetchInterval(data.synchronizationData?.lastSynchronizationTime);
        },
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), '');
        },
    });
    useHeaderLoading(isFetching);

    const absences = useMemo(() => {
        if (selectedAdvisors.length === 0) {
            const ids = advisors.map((x) => x.id);
            return allAbsences.filter((x) => x.userId === null || ids.includes(x.userId));
        }

        return allAbsences.filter((x) => x.userId === null || selectedAdvisors.includes(x.userId));
    }, [selectedAdvisors, advisors, allAbsences]);

    const appointments: Appointment[] = useMemo(() => {
        let list = (data ?? []).map((a) => ({
            number: a.number,
            orderNumber: a.orderNumber,
            repairShopId: a.repairShopId,
            status: a.status,
            customerId: a.customerId,
            customerFirstName: a.customerFirstName,
            customerLastName: a.customerLastName,
            customerEmail: a.customerEmail,
            customerMobile: a.customerMobile,
            userServiceDisplayName: a.userServiceDisplayName,
            userServiceAdvisorColor: a.userServiceAdvisorColor,
            userServiceId: a.userServiceAdvisorId,
            duration: a.duration,
            vehiclePlates: a.vehiclePlates,
            vehicleVIN: a.vehicleVIN,
            vehicleMake: a.vehicleMake,
            vehicleModel: a.vehicleModel,
            vehicleYear: a.vehicleYear,
            vehicleColor: a.vehicleColor,
            jobType: a.jobType,
            id: a.id ?? '',
            startDate: a.startDate + '',
            endDate: a.endDate + '',
            isThirdPartyCreated: a.isThirdPartyCreated,
            isThirdPartyUpdated: a.isThirdPartyUpdated,
            isThirdPartyDeleted: a.isThirdPartyDeleted,
            isFromAppointmentSite: a.isFromAppointmentSite,
            hasConflict: a.hasConflict,
            withValetService: a.withValetService,
        }));

        if (selectedAdvisors.length) {
            list = list.filter((x) => selectedAdvisors.includes(x.userServiceId));
        }

        if (statuses.length) {
            list = list.filter((x) => statuses.includes(legacyAppointmentStatusNotNew(x.status)));
        }

        if (mode === 'day') {
            const start = moment(date);
            list = list.filter((x) => moment(x.startDate).isSame(start, 'd'));
        }

        return list;
    }, [data, selectedAdvisors, statuses, mode, date]);

    const calculateRefetchInterval = (lastSyncTimeStr: string) => {
        const syncTimeMilliseconds = 5000;

        let refetchInterval;
        if (lastSyncTimeStr === undefined || !appointmentSettings?.synchronizeAppointmentsEnabled) {
            refetchInterval = defaultIntervalMilliseconds;
        } else {
            //We assume that next sync moment happens at last + 2min + syncTime, where syncTime is expected time for synchronizing to be completed.
            const nextSyncTime = moment
                .utc(lastSyncTimeStr)
                .add(defaultIntervalMilliseconds + syncTimeMilliseconds, 'milliseconds');
            const now = moment.utc();
            const diffMilliseconds = nextSyncTime.diff(now);

            if (diffMilliseconds > 0) {
                refetchInterval = diffMilliseconds;
            }
            //Difference <= 0 means that synchronizing takes longer than expected, so wait a bit more and retry.
            //But no longer than for 1 minute, after that we assume that sync job is down and don't spam the backend with requests.
            else if (diffMilliseconds > -60000) {
                refetchInterval = syncTimeMilliseconds;
            } else {
                refetchInterval = defaultIntervalMilliseconds;
            }
        }
        setRefetchInterval(refetchInterval);
    };

    return (
        <>
            <CustomHeaderContent>
                <Header onRefetch={refetch} {...{ activeSuccessSync }} />
            </CustomHeaderContent>
            <Main>
                <AppointmentPopupProvider>
                    <ScheduleAbsencePopupProvider>
                        <EditAbsencePopupProvider>
                            <ScheduleAppointmentActionsMenuContextProvider>
                                {mode === 'week' ? (
                                    <WeekScheduler
                                        stopsInterval={appointmentDuration}
                                        appointments={appointments}
                                        dateReference={date}
                                        absences={absences}
                                    />
                                ) : (
                                    <DayScheduler
                                        stopsInterval={appointmentDuration}
                                        appointments={appointments}
                                        absences={absences}
                                    />
                                )}
                            </ScheduleAppointmentActionsMenuContextProvider>
                        </EditAbsencePopupProvider>
                    </ScheduleAbsencePopupProvider>
                </AppointmentPopupProvider>
            </Main>
        </>
    );
}

const Main = styled('main')(({ theme }) => ({
    position: 'relative',
    paddingLeft: 28,
    paddingRight: 44,
    backgroundColor: theme.palette.neutral[1],
    height: 'calc(100vh - var(--header-height))',

    '@media (max-width: 900px)': {
        paddingLeft: 0,
        paddingRight: 0,
    },
}));

const useApplyQueryFilters = () => {
    const [modeParam] = useQueryParam('mode');
    const [dayParam] = useQueryParam('date');
    const dispatch = useAppDispatch();

    useEffect(() => {
        const hasMode = !!modeParam;
        const hasDay = !!dayParam;

        if (!hasMode && !hasDay) return;
        dispatch(
            setFilters({
                ...(hasMode && { mode: modeParam as 'week' | 'day' }),
                ...(hasDay && { date: dayParam }),
            })
        );
    }, [modeParam, dayParam, dispatch]);
};

export default function AppointmentList() {
    const { t } = useAppTranslation();

    return (
        <PermissionsGate
            predicate="allowSeeAppointments"
            text={t('appointments.accessDeniedText')}
            hint={t('appointments.accessDeniedHint')}
        >
            <AppointmentListInner />
        </PermissionsGate>
    );
}
