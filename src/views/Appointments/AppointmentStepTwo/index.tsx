import Grid from '@mui/material/Grid';
import AppointmentReasonAPI from 'api/AppointmentReason';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { isEqual } from 'lodash';
import { memo, useEffect, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    addAppointmentReason,
    removeAppointmentReason,
    removeDeclinedItemReason,
    setAppointmentReason,
    updateAppointmentData,
} from 'store/slices/appointments';
import { selectEditData } from 'store/slices/appointments/selectors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import ReasonAutocomplete from 'views/Components/ReasonAutocomplete';
import {
    AppointmentSection,
    StepHeader,
    useRepairShopAppointmentReasons,
    useRepairShopCustomAppointmentReasons,
} from '../common';
import AddCustomReason from './AddCustomReason';
import AppointmentDeclinedItemReasonsList from './AppointmentDeclinedItemReasonsList';
import AppointmentReasonsList from './AppointmentReasonsList';
import AppointmentsReasonModal from './AppointmentsReasonModal';
import CustomAppointmentReasonsList from './CustomAppointmentReasonsList';

const selectIsDisabled =
    appointmentEditorStateSelectors.createStep2DisabledSelector(selectEditData);
const selectStepTwoState = createSelector(
    selectEditData,
    selectIsDisabled,
    (
        { appointmentData: { id, reasons, customReasons, declinedItemReasonsForView } },
        isDisabled
    ) => ({
        isNew: !id,
        reasons,
        customReasons,
        declinedItemReasonsForView: declinedItemReasonsForView,
        isDisabled,
    })
);

function AppointmentStepTwo() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { isNew, reasons, customReasons, declinedItemReasonsForView, isDisabled } =
        useAppSelector(selectStepTwoState, isEqual);

    const [showInspectionModal, setShowInspectionModal] = useState<boolean>(false);
    const { add } = useRepairShopAppointmentReasons(false);
    const { isCustomApptReasonEnabled } = useRepairShopCustomAppointmentReasons();

    useEffect(() => {
        // When custom reasons are enabled and this is an existing appointment,
        // initialize reasons based on declinedItemReasonsForView
        if (isCustomApptReasonEnabled && !isNew && declinedItemReasonsForView) {
            const reasonsFromDeclineItems = declinedItemReasonsForView.map(({ id, name }) => ({
                id,
                name,
            }));
            dispatch(setAppointmentReason(reasonsFromDeclineItems));
        }
    }, [isCustomApptReasonEnabled, isNew, declinedItemReasonsForView, dispatch]);

    return (
        <>
            <AppointmentSection>
                <StepHeader
                    number={2}
                    title={t('appointments.step2.selectTheReasonForTheAppointment')}
                    isRequired={true}
                />
            </AppointmentSection>

            <AppointmentSection>
                {isCustomApptReasonEnabled ? (
                    <>
                        {declinedItemReasonsForView && declinedItemReasonsForView.length > 0 && (
                            <AppointmentDeclinedItemReasonsList
                                selectedReasons={declinedItemReasonsForView}
                                onRemoveReason={(id) => {
                                    dispatch(removeDeclinedItemReason(id));
                                }}
                            />
                        )}
                        <CustomAppointmentReasonsList
                            selectedReasons={customReasons}
                            disabled={isDisabled}
                        />
                        <AddCustomReason
                            disabled={isDisabled}
                            customReasons={customReasons}
                            hasCustomReasons={!!declinedItemReasonsForView?.length}
                        />
                    </>
                ) : (
                    <AppointmentReasonsList
                        selectedReasons={reasons}
                        onRemoveReason={(id) => dispatch(removeAppointmentReason(id))}
                    />
                )}
            </AppointmentSection>
            <AppointmentSection>
                {!isCustomApptReasonEnabled && (
                    <Grid container spacing={1}>
                        <Grid item xs={5}>
                            <ReasonAutocomplete
                                cacheKey="default"
                                searchReasons={AppointmentReasonAPI.getFilterReasons}
                                getFrequentReasons={AppointmentReasonAPI.getFrequentReasons}
                                createReason={AppointmentReasonAPI.createReason}
                                disabled={isDisabled}
                                excludeItems={reasons}
                                onChange={(reason) => {
                                    add(reason);
                                    dispatch(
                                        updateAppointmentData({
                                            appointment: {
                                                reasons: [...reasons, reason],
                                            },
                                        })
                                    );
                                }}
                            />
                        </Grid>
                        <Grid item xs={2} style={{ display: 'flex', alignItems: 'end' }}>
                            <Button
                                blockMode
                                cmosVariant={'filled'}
                                label={t('appointments.step2.seeAll')}
                                disabled={isDisabled}
                                onClick={() => setShowInspectionModal(true)}
                            />
                        </Grid>
                    </Grid>
                )}
            </AppointmentSection>
            <AppointmentsReasonModal
                open={showInspectionModal}
                onClose={() => setShowInspectionModal(false)}
                onSelected={(values) => dispatch(addAppointmentReason(values))}
            />
        </>
    );
}

export default memo(AppointmentStepTwo);
