import { Grid, styled } from '@mui/material';
import { CustomAppointmentReasonDto } from 'api/appointments';
import { useMemo } from 'react';
import { useAppDispatch } from 'store';
import {
    editCustomAppointmentReason,
    removeCustomAppointmentReason,
} from 'store/slices/appointments';
import CustomAppointmentReasonPicker from '../CustomAppointmentReasonPicker';

type CustomAppointmentReasonsListProps = {
    selectedReasons: CustomAppointmentReasonDto[];
    disabled: boolean;
};

const ReasonRow = styled(Grid)(({ theme }) => ({
    marginBottom: 15,
}));

type CustomAppointmentReasonItem = {
    reason: CustomAppointmentReasonDto;
    index: number;
};

export default function CustomAppointmentReasonsList({
    selectedReasons,
    disabled,
}: CustomAppointmentReasonsListProps) {
    const dispatch = useAppDispatch();

    const { customer, workshop } = useMemo(() => {
        const customer: CustomAppointmentReasonItem[] = [];
        const workshop: CustomAppointmentReasonItem[] = [];

        selectedReasons.forEach((reason, index) => {
            if (reason.isFromCustomer) {
                customer.push({ reason, index });
            } else {
                workshop.push({ reason, index });
            }
        });

        return {
            customer,
            workshop,
        };
    }, [selectedReasons]);

    const render = ({ reason, index }: CustomAppointmentReasonItem, indexInGroup: number) => {
        return (
            <ReasonRow key={`${reason.id}-${index}`} container spacing={0} alignItems="center">
                <CustomAppointmentReasonPicker
                    // index refers to index in the flat list of reasons (i.e. not grouped by type: workshop/customer)
                    index={index}
                    selectedReason={reason}
                    isDisabled={disabled}
                    onRemoveReason={(id) => {
                        dispatch(removeCustomAppointmentReason(id));
                    }}
                    onChange={(reason) =>
                        dispatch(editCustomAppointmentReason({ ...reason, index }))
                    }
                    hideLabel={indexInGroup !== 0}
                />
            </ReasonRow>
        );
    };

    return (
        <>
            {customer.map(render)}
            {workshop.map(render)}
        </>
    );
}
