import { Grid, styled } from '@mui/material';
import { AppointmentReasonDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import { Colors } from 'common/styles/Colors';

type AppointmentReasonsListProps = {
    onRemoveReason: (reasonId: string) => void;
    selectedReasons: AppointmentReasonDto[];
    isCustomApptReasonEnabled?: boolean;
};

const ReasonCaption = styled('span')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[8],
}));

export default function AppointmentReasonsList({
    onRemoveReason,
    selectedReasons,
}: AppointmentReasonsListProps) {
    return (
        <>
            {selectedReasons.map((reason) => {
                return (
                    <Grid key={reason.id} container spacing={0} alignItems="center">
                        <ReasonCaption>{reason.name}</ReasonCaption>
                        <Button
                            cmosVariant={'typography'}
                            color={Colors.Error}
                            Icon={RemoveCircleIcon}
                            onClick={() => onRemoveReason(reason.id)}
                        />
                    </Grid>
                );
            })}
        </>
    );
}
