import { styled } from '@mui/material';
import { DeclinedItemReasonForViewDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';

type AppointmentDeclinedItemReasonsListProps = {
    onRemoveReason: (reasonId: string) => void;
    selectedReasons: DeclinedItemReasonForViewDto[];
    isCustomApptReasonEnabled?: boolean;
};

export default function AppointmentDeclinedItemReasonsList({
    onRemoveReason,
    selectedReasons,
}: AppointmentDeclinedItemReasonsListProps) {
    const { t } = useAppTranslation();

    return (
        <>
            <Label>{t('appointments.step2.reasonForTheAppointment')}</Label>

            {selectedReasons.map((reason) => {
                return (
                    <div key={reason.id}>
                        <ReasonContainer>
                            <ReasonCaption>{reason.name}</ReasonCaption>
                        </ReasonContainer>

                        <Button
                            cmosVariant={'typography'}
                            color={Colors.Error}
                            Icon={RemoveCircleIcon}
                            onClick={() => onRemoveReason(reason.id)}
                        />
                    </div>
                );
            })}
        </>
    );
}

const ReasonCaption = styled('span')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.common.black,
}));

const Label = styled('span')(({ theme }) => ({
    ...theme.typography.h6,
    color: theme.palette.neutral[8],
    marginBottom: 5,
    display: 'flex',
}));

const ReasonContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    width: 300,
    height: 40,
    alignItems: 'center',
    '--input-border-radius': '4px',
    '--input-background-color': theme.palette.neutral[2],
    '--input-text-color': theme.palette.common.black,
    '--input-main-color': theme.palette.neutral[5],
    borderRadius: 'var(--input-border-radius) !important',
    backgroundColor: 'var(--input-background-color) !important',
    border: '1px solid var(--input-main-color) !important',
    color: 'var(--input-text-color) !important',
    padding: '2px 8px',
    marginBottom: 8,
}));
