import { Grid } from '@mui/material';
import { CustomAppointmentReasonDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import { useAppDispatch } from 'store';
import { addCustomAppointmentReason } from 'store/slices/appointments';
import CustomAppointmentReasonPicker from '../CustomAppointmentReasonPicker';

type AddCustomReasonsProps = {
    disabled: boolean;
    customReasons: CustomAppointmentReasonDto[];
    hasCustomReasons: boolean;
};

const AddCustomReason = ({
    disabled = true,
    customReasons,
    hasCustomReasons,
}: AddCustomReasonsProps) => {
    const [addCustomReason, setAddCustomReason] = useState(false);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const showPicker = useMemo(() => {
        return addCustomReason || (!customReasons.length && !hasCustomReasons);
    }, [hasCustomReasons, addCustomReason, customReasons, customReasons.length]);
    return (
        <Grid container gap={2}>
            {/* Shows picker for empty custom appointment reason */}
            {showPicker && (
                <Grid container spacing={0} alignItems="center">
                    <CustomAppointmentReasonPicker
                        isDisabled={disabled}
                        hideDelete={!hasCustomReasons && !customReasons.length}
                        onChange={(reason) => {
                            dispatch(addCustomAppointmentReason(reason));
                            setAddCustomReason(false);
                        }}
                        onRemoveReason={() => {
                            setAddCustomReason(false);
                        }}
                    />
                </Grid>
            )}
            <Grid item xs={4.5}>
                <Button
                    blockMode
                    cmosVariant={'stroke'}
                    label={t('appointments.step2.addReasonForAppointment')}
                    Icon={PlusIcon}
                    iconPosition="left"
                    customStyles={{ borderRadius: 5 }}
                    disabled={disabled || showPicker}
                    onClick={() => {
                        setAddCustomReason(true);
                    }}
                />
            </Grid>
        </Grid>
    );
};

export default AddCustomReason;
