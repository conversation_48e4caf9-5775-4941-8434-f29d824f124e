import { Box, Typography, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import { AppointmentReasonDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import FilterTextField from 'common/components/Inputs/FilterTextField';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectEditData } from 'store/slices/appointments/selectors';
import { useRepairShopAppointmentReasons } from 'views/Appointments/common';
import ReasonsGrid from './ReasonsGrid';
import { ReasonsContext } from './context';

type AppointmentsReasonModalProps = {
    open: boolean;
    onClose: () => void;
    onSelected: (data: AppointmentReasonDto[]) => void;
    title?: string;
    description?: string;
};

const selectNecessaryData = createSelector(selectEditData, (data) => ({
    selectedReasons: data.appointmentData.reasons,
}));

const ModalElement = styled('div')(({ theme }) => ({
    width: '1020px',
    padding: '36px 42px 42px 42px',
    borderRadius: '24px',
    backgroundColor: theme.palette.neutral[1],
    position: 'relative',
}));

const AppointmentsReasonModal = ({
    open,
    onClose: onCloseProp,
    onSelected,
    title = 'appointments.step2.reasonsForTheAppointment',
    description = 'appointments.step2.listOfAllExistingReasonsForTheAppointment',
}: AppointmentsReasonModalProps): JSX.Element => {
    const { t } = useAppTranslation();
    const [searchReasonItem, setSearchReasonItem] = useState('');
    const { selectedReasons } = useAppSelector(selectNecessaryData);
    const { reasons } = useRepairShopAppointmentReasons(open);
    const ctx = useMemo(() => new ReasonsContext(reasons), [reasons]);

    useEffect(() => {
        ctx.setSelectedReasons(selectedReasons);
    }, [ctx, selectedReasons]);

    const onClose = useCallback(() => {
        const ids = ctx.getSelectedReasonIds();
        onSelected(reasons.filter((x) => ids.includes(x.id)));
        onCloseProp();
    }, [onCloseProp, ctx, onSelected, reasons]);

    return (
        <Modal open={open}>
            <ModalElement>
                <IconButton
                    onClick={() => onClose()}
                    size="small"
                    sx={{ position: 'absolute', right: 42, top: 36 }}
                >
                    <CloseIcon />
                </IconButton>
                <Grid
                    container
                    justifyContent="space-between"
                    alignItems="center"
                    style={{ marginBottom: 35 }}
                >
                    <Grid item xs={12}>
                        <Grid container>
                            <Grid item>
                                <Typography component="h2" variant="h5Inter" sx={{ mb: 1 }}>
                                    {t(title)}
                                </Typography>
                                <Typography
                                    component="p"
                                    variant="h6Inter"
                                    sx={{ fontWeight: 'normal', mb: 2, fontSize: 11 }}
                                >
                                    {t(description)}
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid container>
                            <Grid item xs={6}>
                                <FilterTextField
                                    name="filter"
                                    placeholder={t('appointments.step2.search')}
                                    value={searchReasonItem}
                                    onChange={(e) => setSearchReasonItem(e.target.value)}
                                />
                            </Grid>
                            <Grid item xs={6} justifyContent="flex-end" style={{ display: 'flex' }}>
                                <Grid item xs={6}>
                                    <Button
                                        blockMode
                                        label={t('appointments.step2.save')}
                                        cmosVariant={'filled'}
                                        color={Colors.Success}
                                        onClick={() => {
                                            onClose();
                                        }}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
                <Box sx={{ overflow: 'auto', height: 420 }}>
                    <ReasonsGrid reasons={reasons} search={searchReasonItem} ctx={ctx} />
                </Box>
            </ModalElement>
        </Modal>
    );
};

export default AppointmentsReasonModal;
