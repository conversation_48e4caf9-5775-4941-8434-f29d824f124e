import { styled } from '@mui/material';
import { AppointmentReasonDto } from 'api/appointments';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useCallback, useMemo } from 'react';
import { GroupedVirtuoso } from 'react-virtuoso';
import { AppointmentReasonItem, LetterHeader } from './AppointmentReasonItem';
import { ReasonsContext } from './context';
import { useReasonsGrouping } from './helpers';

export type ReasonsGridProps = {
    reasons: AppointmentReasonDto[];
    search: string;
    ctx: ReasonsContext;
};

const SVirtuoso = styled(GroupedVirtuoso)({
    ...scrollbarStyle(),
});

export default function ReasonsGrid({ reasons, search, ctx }: ReasonsGridProps) {
    const subGroups = search ? 1 : 4;
    const filteredReasons = useMemo(() => {
        if (search) {
            return reasons.filter((reason) =>
                reason.name.toLowerCase().includes(search.toLowerCase())
            );
        }

        return reasons;
    }, [reasons, search]);
    const { groups } = useReasonsGrouping(filteredReasons, search ? 1 : 4);

    const itemContentFn = useCallback(
        (index: number, groupIndex: number) => {
            const reason = groups[groupIndex].reasons[index - groups[groupIndex].startIndex];
            if (!reason || reason.length === 0) return null;
            return (
                <div
                    style={{
                        display: 'grid',
                        gridTemplateColumns: Array.from({ length: subGroups })
                            .map(() => '1fr')
                            .join(' '),
                    }}
                >
                    <AppointmentReasonItem ctx={ctx} reason={reason[0]} searchMode={false} />
                    {reason.length >= 2 && (
                        <AppointmentReasonItem ctx={ctx} reason={reason[1]} searchMode={false} />
                    )}
                    {reason.length >= 3 && (
                        <AppointmentReasonItem ctx={ctx} reason={reason[2]} searchMode={false} />
                    )}
                    {reason.length >= 4 && (
                        <AppointmentReasonItem ctx={ctx} reason={reason[3]} searchMode={false} />
                    )}
                </div>
            );
        },
        [ctx, groups, subGroups]
    );

    return (
        <SVirtuoso
            groupCounts={groups.map((x) => x.reasons.length)}
            groupContent={(index) => <LetterHeader>{groups[index].letter}</LetterHeader>}
            itemContent={itemContentFn}
        />
    );
}
