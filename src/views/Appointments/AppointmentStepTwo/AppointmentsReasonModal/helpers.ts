import { AppointmentReasonDto } from 'api/appointments';
import chunk from 'lodash/chunk';
import { useMemo } from 'react';

export function useReasonsGrouping(reasons: AppointmentReasonDto[], subGroups: number) {
    return useMemo(() => calculateGrouping(reasons, subGroups), [reasons, subGroups]);
}

const OTHER_LETTER = '&';

export type ReasonsGroup = {
    letter: string;
    reasons: AppointmentReasonDto[][];
    startIndex: number;
};

function calculateGrouping(reasons: AppointmentReasonDto[], subGroups: number) {
    const letters: Record<string, AppointmentReasonDto[]> = {};
    for (const reason of reasons) {
        const firstLetter = reason.name.charAt(0).normalize().toUpperCase();
        if (/[a-zA-Z]/.test(firstLetter)) {
            if (!letters[firstLetter]) {
                letters[firstLetter] = [];
            }
            letters[firstLetter].push(reason);
        } else if (/\d/.test(firstLetter)) {
            if (!letters['0-9']) {
                letters['0-9'] = [];
            }
            letters['0-9'].push(reason);
        } else {
            if (!letters[OTHER_LETTER]) {
                letters[OTHER_LETTER] = [];
            }
            letters[OTHER_LETTER].push(reason);
        }
    }

    const groups: ReasonsGroup[] = [];

    let startIndex = 0;
    for (const letter in letters) {
        const reasons = chunk(letters[letter], subGroups);
        groups.push({
            reasons,
            letter,
            startIndex,
        });
        startIndex += reasons.length;
    }

    return {
        groups,
    };
}
