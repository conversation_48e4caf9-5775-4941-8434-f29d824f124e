import { CustomAppointmentReasonDto } from 'api/appointmentReasons';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';

type CustomApptReasonSelectorProps = {
    selectedId: string | undefined | null;
    onSelected: (selected: CustomAppointmentReasonDto) => void;
    isDisabled: boolean;
    customAppointmentReasons: CustomAppointmentReasonDto[];
    isFromCustomer: boolean;
    hideLabel: boolean;
};

export default function CustomApptReasonSelector({
    selectedId,
    onSelected,
    isDisabled,
    customAppointmentReasons,
    isFromCustomer,
    hideLabel,
}: CustomApptReasonSelectorProps) {
    const { t } = useAppTranslation();

    const options = useMemo(
        () =>
            (customAppointmentReasons ?? []).map((o) => ({
                label: o.name,
                value: o.id,
            })),
        [customAppointmentReasons]
    );
    const selectedOption = useMemo(
        () => options.find((o) => o.value === selectedId),
        [options, selectedId]
    );

    return (
        <Dropdown
            size="medium"
            name="customAppointmentReasons"
            cmosVariant="grey"
            options={options}
            value={selectedOption}
            onChange={(o: OptionData<string> | null) => {
                if (o === null) return;
                const newSelected = customAppointmentReasons?.find((x) => x.id === o.value);
                if (newSelected) {
                    onSelected(newSelected);
                }
            }}
            label={
                hideLabel
                    ? undefined
                    : isFromCustomer
                    ? t('appointments.step2.customerReasonForAppointment')
                    : t('appointments.step2.workshopReasonForAppointment')
            }
            placeholder={t('appointments.step2.selectTheReasonForTheAppointment')}
            disabled={isDisabled}
        />
    );
}
