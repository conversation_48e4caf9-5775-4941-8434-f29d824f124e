import { styled } from '@mui/styles';
import { CustomAppointmentReasonDto as CustomAppointmentReasonDto_ListItem } from 'api/appointmentReasons';
import { CustomAppointmentReasonDetailDto, CustomAppointmentReasonDto } from 'api/appointments';

import { IconButton } from '@mui/material';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import { useMemo } from 'react';
import { useRepairShopCustomAppointmentReasons } from 'views/Appointments/common';
import CustomApptReasonDetailsSelector from './CustomApptReasonDetailsSelector';
import CustomApptReasonSelector from './CustomApptReasonSelector';
import { createSelector } from 'reselect';
import { selectEditData } from 'store/slices/appointments';
import { useAppSelector } from 'store';
import { isEqual } from 'lodash';

const selectVehicleSectionData = createSelector(
    selectEditData,
    ({ appointmentData: { vehicleBrand, vehicleModel, vehicleYear } }) => ({
        vehicleBrand,
        vehicleModel,
        vehicleYear,
    })
);

type CustomAppointmentReasonPickerProps = {
    isDisabled: boolean;
    onChange: (reason: CustomAppointmentReasonDto) => void;
    onRemoveReason: (id: string) => void;
    hideDelete?: boolean;
    selectedReason?: CustomAppointmentReasonDto;
    index?: number;
    hideLabel?: boolean;
};

const CustomAppointmentReasonPicker = ({
    isDisabled,
    onRemoveReason,
    selectedReason,
    hideDelete = false,
    onChange,
    index,
    hideLabel = true,
}: CustomAppointmentReasonPickerProps) => {
    const { vehicleBrand, vehicleModel, vehicleYear } = useAppSelector(
        selectVehicleSectionData,
        isEqual
    );

    const { customAppointmentReasons } = useRepairShopCustomAppointmentReasons(
        vehicleBrand,
        vehicleModel,
        vehicleYear
    );

    const details = useMemo(() => {
        if (!selectedReason) return [];
        return (
            customAppointmentReasons?.find((x) => x.id === selectedReason.id)
                ?.customAppointmentReasonDetails ?? []
        ).map((x) => {
            const details: CustomAppointmentReasonDetailDto = {
                id: x.id,
                name: x.appointmentReasonDetail,
            };
            return details;
        });
    }, [selectedReason, customAppointmentReasons]);

    const selectedReasonDetails = useMemo(() => {
        if (!selectedReason) return undefined;
        return details?.find((x) => x.id === selectedReason.details?.id);
    }, [selectedReason, details]);

    const handleCustomAppointmentReasonChange = (reason: CustomAppointmentReasonDto_ListItem) => {
        const newSelectedReason: CustomAppointmentReasonDto = {
            id: reason.id,
            name: reason.name,
            details: null, // this is left null because sub-reason aka detail is not selected
            isFromCustomer: null, // TODO CMOS-3123 understand what's up with this prop
        };
        onChange(newSelectedReason);
    };

    const handleDetailChange = (details: CustomAppointmentReasonDetailDto) => {
        if (!selectedReason) return;
        const newSelectedReason: CustomAppointmentReasonDto = {
            ...selectedReason,
            details,
        };
        onChange(newSelectedReason);
    };

    const isFromCustomer = selectedReason?.isFromCustomer === true;

    return (
        <Root>
            <DropdownWrapper>
                <CustomApptReasonSelector
                    selectedId={selectedReason?.id}
                    onSelected={handleCustomAppointmentReasonChange}
                    isDisabled={isDisabled}
                    customAppointmentReasons={customAppointmentReasons}
                    isFromCustomer={isFromCustomer}
                    hideLabel={hideLabel}
                />
            </DropdownWrapper>
            {details?.length > 0 && (
                <DropdownWrapper>
                    <CustomApptReasonDetailsSelector
                        selected={selectedReasonDetails}
                        onSelected={handleDetailChange}
                        isDisabled={isDisabled}
                        reasonDetails={details}
                        hideLabel={hideLabel}
                    />
                </DropdownWrapper>
            )}
            {!hideDelete && index !== 0 && (
                <CustomIconButton
                    onClick={() => {
                        onRemoveReason && onRemoveReason(selectedReason?.id ?? '');
                    }}
                >
                    <RemoveCircleIcon fill="currentColor" />
                </CustomIconButton>
            )}
        </Root>
    );
};

export default CustomAppointmentReasonPicker;

const Root = styled('div')({
    display: 'flex',
    gap: '8px',
});

const DropdownWrapper = styled('div')({
    width: '300px',
});

const CustomIconButton = styled(IconButton)({
    color: 'var(--danger)',
    alignSelf: 'end',
    padding: '5px',
    marginBottom: '3px',
});
