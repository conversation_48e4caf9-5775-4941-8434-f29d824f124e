import { CustomAppointmentReasonDetailDto } from 'api/appointments';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';

type CustomApptReasonDetailsSelectorProps = {
    selected: CustomAppointmentReasonDetailDto | undefined | null;
    onSelected?: (selected: CustomAppointmentReasonDetailDto) => void;
    isDisabled: boolean;
    reasonDetails: CustomAppointmentReasonDetailDto[];
    hideLabel: boolean;
};

export default function CustomApptReasonDetailsSelector({
    selected,
    onSelected,
    isDisabled,
    reasonDetails,
    hideLabel,
}: CustomApptReasonDetailsSelectorProps) {
    const { t } = useAppTranslation();

    const options = useMemo(
        () =>
            (reasonDetails ?? []).map((o) => ({
                label: o.name,
                value: o.id,
            })),
        [reasonDetails]
    );
    const selectedOption = useMemo(
        () => options.find((o) => o.value === selected?.id),
        [options, selected]
    );

    return (
        <Dropdown
            size="medium"
            name="customAppointmentReasonDetails"
            cmosVariant="grey"
            options={options}
            value={selectedOption}
            onChange={(o: OptionData<string> | null) => {
                if (o === null) return;
                const newSelected = reasonDetails?.find((x) => x.id === o.value) ?? {
                    id: o.value,
                    name: o.label,
                };
                onSelected && onSelected(newSelected);
            }}
            label={hideLabel ? undefined : t('appointments.step2.reasonDetailsForTheAppointment')}
            placeholder={t('appointments.step2.selectTheReasonDetailsForTheAppointment')}
            disabled={isDisabled}
        />
    );
}
