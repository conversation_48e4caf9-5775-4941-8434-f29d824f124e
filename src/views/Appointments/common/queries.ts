import { QueryKey, UseQueryOptions, useQuery, useQueryClient } from '@tanstack/react-query';
import AppointmentAPI from 'api/Appointment';
import AppointmentReasonAPI from 'api/AppointmentReason';
import AppointmentSettingsAPI from 'api/AppointmentSettings';
import AppointmentReasonsApi from 'api/appointmentReasons';
import AppointmentsApi, {
    AppointmentOriginDto,
    AppointmentReasonDto,
    AppointmentScheduleWindowsDto,
} from 'api/appointments';
import { EnterpriseAppointmentsApi } from 'api/enterprise';
import { PUBNUB_CHANNELS } from 'api/pubnub';
import { AbsenceDto } from 'api/users';
import useToasters from 'common/hooks/useToasters';
import { GetAppointmentsResponse } from 'datacontracts/Scheduler/GetAppointmentsResponse';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { absencesActions } from 'store/slices/absences';
import { selectAbsences } from 'store/slices/absences/selectors';
import loadAbsencesThunk from 'store/slices/absences/thunks/loadAbsences';
import { setFetchedAppointments } from 'store/slices/appointments';
import { selectAppointments, selectFilters } from 'store/slices/appointments/selectors';
import {
    selectRepairShopIntegrationAccountName,
    selectSettings,
} from 'store/slices/globalSettingsSlice';
import { usePubnubListener } from 'utils/pubnub';

const APPOINTMENT_REASONS_QUERY_KEY = ['appointments', 'reasons'];
const CUSTOM_APPT_REASONS_QUERY_KEY = ['settings', 'customAppointmentReasons'];

/**
 * a query hook for repair shop appointment reasons
 */
function useAppointmentReasons(
    queryKey: QueryKey,
    getAllReasons: () => Promise<AppointmentReasonDto[]>,
    enabled: boolean = true
) {
    const query = useQuery(queryKey, () => getAllReasons().then((r) => r.filter((x) => x.name)), {
        cacheTime: Infinity,
        staleTime: 10000,
        enabled,
    });

    return {
        reasons: query.data ?? [],
        ...useAppointmentReasonsQueryModifiers(queryKey),
    };
}

export function useRepairShopAppointmentReasons(enabled: boolean = true) {
    return useAppointmentReasons(
        APPOINTMENT_REASONS_QUERY_KEY,
        AppointmentReasonAPI.getReasons,
        enabled
    );
}

export function useEnterpriseAppointmentReasons(shopId: string | null, enabled: boolean = true) {
    const queryKey = useMemo(
        () => ['enterprise', `shop=${shopId}`, 'appointments', 'reasons'],
        [shopId]
    );
    const get = useCallback(() => EnterpriseAppointmentsApi.reasons.getAll(shopId!), [shopId]);
    return useAppointmentReasons(queryKey, get, !!shopId && enabled);
}

type UseAppointmentOriginsQueryOptions = {
    getOrigins: () => Promise<AppointmentOriginDto[]>;
    enabled: boolean;
    cacheKey: string;
};

function useAppointmentOriginsQuery({
    cacheKey,
    enabled,
    getOrigins,
}: UseAppointmentOriginsQueryOptions) {
    return useQuery(['appointments', 'origins', { cacheKey }], getOrigins, {
        enabled,
        cacheTime: Infinity,
        staleTime: 1000,
    });
}

export function useRepairShopAppointmentOrigins() {
    return useAppointmentOriginsQuery({
        cacheKey: 'default',
        getOrigins: AppointmentsApi.getOrigins,
        enabled: true,
    });
}

export function useEnterpriseAppointmentOrigins(shopId: string | null) {
    return useAppointmentOriginsQuery({
        cacheKey: shopId ?? '',
        getOrigins: () => EnterpriseAppointmentsApi.getOrigins(shopId!),
        enabled: !!shopId,
    });
}

/**
 * a query hook for query modifiers of repair shop appointment reasons
 */
function useAppointmentReasonsQueryModifiers(queryKey: QueryKey) {
    const queryClient = useQueryClient();

    return useMemo(
        () => ({
            add: (reason: AppointmentReasonDto) => {
                const data = queryClient.getQueryData(queryKey) as AppointmentReasonDto[];
                if (!data) return false;
                if (data.some((x) => x.id === reason.id)) return;

                // 1 element was added and now we have to re-create a list with potentially thousands of reasons
                // god I love react
                queryClient.setQueryData(APPOINTMENT_REASONS_QUERY_KEY, [...data, reason]);
                return true;
            },
            remove: (id: string) => {
                const data = queryClient.getQueryData(
                    APPOINTMENT_REASONS_QUERY_KEY
                ) as AppointmentReasonDto[];
                if (!data) return false;
                if (!data.some((x) => x.id === id)) return;
                queryClient.setQueryData(
                    APPOINTMENT_REASONS_QUERY_KEY,
                    data.filter((x) => x.id !== id)
                );
                return true;
            },
        }),
        [queryClient, queryKey]
    );
}

export const APPOINTMENT_SETTINGS_QUERY_KEY: QueryKey = ['AppointmentSettingsDto'];

export function useRepairShopAppointmentSettings(staleTime: number = 10000) {
    const { data } = useQuery(
        APPOINTMENT_SETTINGS_QUERY_KEY,
        () => AppointmentSettingsAPI.getAppointmentSettings(),
        {
            cacheTime: Infinity,
            staleTime,
        }
    );

    return data;
}

function getAppointmentsQueryKey(date: moment.Moment) {
    const monday = moment(date).startOf('isoWeek');
    return ['appointments', 'week', monday.format('YYYY-MM-DD')];
}

export function useAppointmentsQuery(
    date: moment.MomentInput,
    {
        onSuccess,
        ...options
    }: Omit<UseQueryOptions<GetAppointmentsResponse>, 'queryKey' | 'queryFn' | 'meta'> = {}
) {
    const monday = moment(date).startOf('isoWeek');
    const dateStr = monday.format('YYYY-MM-DD');
    const queryKey = getAppointmentsQueryKey(monday);
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useTranslation();

    const [successSync, setSuccessSync] = useState(false);

    const { refetch, isFetching, isFetched } = useQuery(
        queryKey as QueryKey,
        async () => {
            const startDate = monday;
            const endDate = startDate.clone().add(7, 'd').add(-1, 's');

            return AppointmentAPI.getAppointments(startDate.toDate(), endDate.toDate());
        },
        {
            onSuccess: (data: GetAppointmentsResponse): void => {
                dispatch(
                    setFetchedAppointments({
                        date: dateStr,
                        appointments: data.data.map((x) => ({
                            ...x,
                            startDate: convertToUTC(x.startDate),
                            endDate: convertToUTC(x.endDate),
                        })),
                    })
                );
                if (onSuccess) {
                    onSuccess(data);

                    if (successSync) {
                        toasters.success(
                            t('appointments.get3rdParty.success.description', {
                                integratedAccountName,
                            }),
                            t('appointments.get3rdParty.success.title')
                        );

                        setSuccessSync(false);
                    }
                }
            },
            staleTime: 100,
            cacheTime: 120000,
            meta: {
                noPersist: true, // disable persistency for this query
            },
            ...options,
        }
    );

    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);
    const appointments = useAppSelector((r: RootState) => selectAppointments(r, dateStr));

    useEffect(() => {
        const startDate = DateTime.fromFormat(dateStr, 'yyyy-MM-dd');
        const endDate = startDate.plus({ days: 7 });
        dispatch(
            loadAbsencesThunk({
                from: startDate.toISO(),
                to: endDate.toISO(),
            })
        );
    }, [dateStr, dispatch]);

    const absences = useAppSelector(selectAbsences);

    const { uid } = useAppSelector(selectSettings);

    usePubnubListener<AbsenceDto>(
        ({ message }) => {
            dispatch(absencesActions.addAbsence(message.payload));
        },
        {
            channels: [PUBNUB_CHANNELS.absences(uid)],
            types: ['absence.changed'],
            listenerEnabled: !!uid,
        }
    );

    return {
        appointments,
        refetch,
        absences,
        isFetching: isFetching && !isFetched,
        activeSuccessSync: () => setSuccessSync(true),
    };
}

export function useAppointmentsQueryController() {
    const queryClient = useQueryClient();
    const { date } = useAppSelector(selectFilters);
    const day = moment(date).format('YYYY-MM-DD');

    return useMemo(
        () => ({
            refetchWeek: () => {
                queryClient.refetchQueries(getAppointmentsQueryKey(moment(day).startOf('isoWeek')));
            },
        }),
        [queryClient, day]
    );
}

type UserScheduleWindowsOptions = {
    cacheKey: string;
    date: string;
    serviceAdvisorId: string | null;
    getWindows: (
        date: string,
        serviceAdvisorId: string | null
    ) => Promise<AppointmentScheduleWindowsDto>;
    enabled: boolean;
};

function useScheduleWindows({
    cacheKey,
    getWindows,
    serviceAdvisorId,
    date,
    enabled,
}: UserScheduleWindowsOptions) {
    return useQuery(
        ['appointments', 'schedule-windows', { cacheKey, serviceAdvisorId, date }],
        () => getWindows(date, serviceAdvisorId),
        {
            keepPreviousData: true,
            enabled,
        }
    );
}

export function useRepairShopAvailableSchedules(date: string, serviceAdvisorId: string | null) {
    return useScheduleWindows({
        date,
        serviceAdvisorId,
        cacheKey: 'default',
        getWindows: AppointmentsApi.getScheduleWindows,
        enabled: true,
    });
}

export function useCustomAppointmentReasons(
    vehicleBrand?: string | null | undefined,
    vehicleModel?: string | null | undefined,
    vehicleYear?: string | null | undefined
) {
    const { data } = useQuery(
        [CUSTOM_APPT_REASONS_QUERY_KEY, vehicleBrand, vehicleModel, vehicleYear],
        () =>
            AppointmentReasonsApi.getCustomAppointmentReasons(
                vehicleBrand,
                vehicleModel,
                vehicleYear
            ),
        {
            cacheTime: 120000,
            refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
            refetchInterval: 120000,
        }
    );
    return data;
}

export function useRepairShopCustomAppointmentReasons(
    vehicleBrand?: string | null | undefined,
    vehicleModel?: string | null | undefined,
    vehicleYear?: string | null | undefined
) {
    const data = useCustomAppointmentReasons(vehicleBrand, vehicleModel, vehicleYear);
    const customAppointmentReasonsData = useMemo(() => {
        return data?.customAppointmentReasons ?? [];
    }, [data]);
    const isCustomApptReasonEnabled = useMemo(
        () => data?.isCustomApptReasonEnabled ?? false,
        [data]
    );
    return {
        isCustomApptReasonEnabled,
        customAppointmentReasons: customAppointmentReasonsData,
    };
}

function convertToUTC(dateString: string): string {
    if (!dateString) return dateString;

    if (dateString.endsWith('Z')) return dateString;

    return dateString + 'Z';
}
