import { Box, IconButton, InputAdornment } from '@mui/material';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import MuiTooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { AppointmentNoteDto, AppointmentNoteSection } from 'api/appointmentNotes';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { ProhibitionIcon } from 'common/components/Icons/ProhibitionIcon';
import { TextFormField } from 'common/components/Inputs';
import { JobTitle, jobTitleLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IconSize } from 'common/styles/IconSize';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { useCallback, useMemo, useRef, useState } from 'react';
import CharacterIndicator from '../../../../common/components/Inputs/CharacterIndicator';
import ThreeDotMenu from './ThreeDotMenu';

type NoteEditorProps = {
    appointmentNote: AppointmentNoteDto;
    editSection: AppointmentNoteSection;
    disabled?: boolean;
    onEdit?: (value: AppointmentNoteDto) => void;
    onDelete?: (value: AppointmentNoteDto) => void;
};

const NoteEditor = ({
    appointmentNote,
    editSection,
    disabled,
    onDelete,
    onEdit,
}: NoteEditorProps) => {
    const { t } = useAppTranslation();

    const [show, setShow] = useState<boolean>(false);
    const [isEdition, setIsEdition] = useState<boolean>(false);
    const [note, setNote] = useState<string>(appointmentNote.note);
    const anchorHint = useRef<HTMLDivElement | null>(null);

    const showActions = useMemo(
        () => !appointmentNote.isFromCustomer && appointmentNote.section === editSection && show,
        [appointmentNote.isFromCustomer, appointmentNote.section, editSection, show]
    );

    const handleSave = () => {
        if (note.trim().length) {
            setIsEdition(false);
            onEdit && onEdit({ ...appointmentNote, note });
        }
    };

    const handleDelete = () => {
        setIsEdition(false);
        onDelete && onDelete(appointmentNote);
    };

    const handleMouseMove = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;
        if (anchorHint.current === el) {
            // NOTE (AK) little hack for floating hint copied from RichTextEditorContent
            return;
        }

        if (el instanceof HTMLDivElement) {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'flex';
                anchorHint.current.style.top = `${e.clientY}px`;
                anchorHint.current.style.left = `${e.clientX}px`;
            }
        } else {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    const handleMouseOut = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;
        if (anchorHint.current === el) {
            // NOTE (AK) little hack for floating hint copied from RichTextEditorContent
            return;
        }

        if (el instanceof HTMLDivElement) {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    return isEdition ? (
        <Grid container spacing={1} display={'flex'} alignItems={'center'}>
            <Grid item xs={11}>
                <TextFormField
                    name="appointment-note"
                    label=""
                    maxLength={100}
                    placeholder={t('appointments.appointmentNotes.enterNote')}
                    value={note}
                    onChange={(event) => {
                        if (event.target.value.length <= 100) {
                            setNote(event.target.value);
                        }
                    }}
                    onEnterPress={() => {
                        handleSave();
                    }}
                    enableEnterComplete
                    multiline
                    endAdornment={
                        <InputAdornment position="end">
                            <IconButton
                                size="small"
                                onClick={() => {
                                    handleSave();
                                }}
                            >
                                <CheckIcon size={IconSize.M} />
                            </IconButton>
                            <CharacterIndicator charactersCount={note.length} />
                        </InputAdornment>
                    }
                />
            </Grid>
            <Grid item xs={1}>
                <Button
                    color="#899198"
                    cmosVariant={'typography'}
                    Icon={CloseIcon}
                    label=""
                    onClick={() => {
                        setIsEdition(false);
                    }}
                />
            </Grid>
        </Grid>
    ) : (
        <>
            <Tooltip
                title={<NotePopupContent appointmentNote={appointmentNote} />}
                placement="left"
            >
                <DivNoteWrapper
                    onMouseMove={(e) =>
                        !disabled &&
                        (appointmentNote.isFromCustomer ||
                            appointmentNote.section !== editSection) &&
                        handleMouseMove(e)
                    }
                    onMouseOver={() =>
                        !disabled &&
                        !appointmentNote.isFromCustomer &&
                        appointmentNote.section === editSection &&
                        setShow(true)
                    }
                    onMouseOut={(e) => {
                        !disabled &&
                            !appointmentNote.isFromCustomer &&
                            appointmentNote.section === editSection &&
                            setShow(false);
                        !disabled &&
                            (appointmentNote.isFromCustomer ||
                                appointmentNote.section !== editSection) &&
                            handleMouseOut(e);
                    }}
                >
                    <Typography
                        component="div"
                        sx={(theme) => ({
                            ...theme.typography.h6Roboto,
                            fontWeight: 400,
                            color: theme.palette.neutral[7],
                            inlineSize: 'auto',
                            overflowWrap: 'break-word',
                            maxWidth: '100%',
                        })}
                    >
                        {appointmentNote.note}
                    </Typography>
                    {showActions && (
                        <ThreeDotMenu
                            onClose={() => setShow(false)}
                            onEdit={() => setIsEdition(true)}
                            onDelete={handleDelete}
                        />
                    )}
                </DivNoteWrapper>
            </Tooltip>
            <DivMouseHint ref={anchorHint}>
                <ProhibitionIcon fill="var(--neutral5)" size={10} />
                {t('appointments.noteCannotBeEdited')}
            </DivMouseHint>
        </>
    );
};

type NotePopupContentProps = {
    appointmentNote: AppointmentNoteDto;
};

const NotePopupContent = ({ appointmentNote }: NotePopupContentProps) => {
    const { t } = useAppTranslation();

    const reversedHistory = useMemo(
        () =>
            appointmentNote.historyNotes && appointmentNote.historyNotes.length > 1
                ? appointmentNote.historyNotes.toReversed()
                : [],
        [appointmentNote.historyNotes]
    );

    return (
        <Box
            component="div"
            sx={(theme) => ({
                maxWidth: '330px',
                minHeight: '30px',
                maxHeight: '99vh',
                padding: '8px',
                background: theme.palette.neutral[1],
                overflowY: 'auto',
                ...scrollbarStyle(),
            })}
        >
            {reversedHistory.length <= 1 ? (
                <HistoryItem
                    isFirst
                    isListItem={false}
                    highlighted={false}
                    isFromCustomer={appointmentNote.isFromCustomer}
                    userJob={appointmentNote.user?.job}
                    userName={appointmentNote.user?.name}
                    createdAt={appointmentNote.createdAt}
                />
            ) : (
                <>
                    <HistoryItem
                        isFirst={false}
                        isListItem={false}
                        highlighted
                        isFromCustomer={appointmentNote.isFromCustomer}
                        userJob={appointmentNote.user?.job}
                        userName={appointmentNote.user?.name}
                        createdAt={reversedHistory[0].createdAt}
                        note={appointmentNote.note}
                    />
                    <Typography
                        sx={(theme) => ({
                            fontFamily: 'Roboto',
                            fontSize: '12px',
                            fontStyle: 'normal',
                            fontWeight: 700,
                            lineHeight: 'normal',
                            color: theme.palette.neutral[7],
                        })}
                    >
                        {t('appointments.noteActivityLog')}
                    </Typography>
                    <List sx={{ listStyleType: 'none', padding: '2px 8px 2px 0px' }}>
                        {reversedHistory.length > 0 &&
                            reversedHistory
                                .slice(1, reversedHistory.length)
                                .map((historyNote, index) => (
                                    <ListItem
                                        key={historyNote.id}
                                        sx={(theme) => ({
                                            display: 'list-item',
                                            padding: '2px 0px',
                                            color: theme.palette.neutral[7],
                                        })}
                                    >
                                        <HistoryItem
                                            isFirst={index === reversedHistory.length - 2}
                                            isListItem
                                            highlighted={false}
                                            isFromCustomer={appointmentNote.isFromCustomer}
                                            userJob={appointmentNote.user?.job}
                                            userName={historyNote.user?.name}
                                            createdAt={historyNote.createdAt}
                                            note={historyNote.note}
                                        />
                                    </ListItem>
                                ))}
                    </List>
                </>
            )}
        </Box>
    );
};

type HistoryItemProps = {
    isFirst: boolean;
    isListItem: boolean;
    highlighted: boolean;
    isFromCustomer: boolean;
    userJob: JobTitle | '' | undefined;
    userName: string | undefined;
    createdAt: Date;
    note?: string;
};

const HistoryItem = ({
    isFirst,
    isListItem,
    highlighted,
    isFromCustomer,
    userJob,
    userName,
    createdAt,
    note,
}: HistoryItemProps) => {
    const { t } = useAppTranslation();

    return (
        <Typography
            sx={(theme) => ({
                fontFamily: 'Roboto',
                fontSize: '12px',
                fontStyle: 'normal',
                fontWeight: 400,
                lineHeight: 'normal',
                color: theme.palette.neutral[7],
                marginBottom: highlighted ? '20px' : 'none',
            })}
        >
            <SpanBold bold={highlighted}>
                {isListItem && '- '}
                {`${t(`appointments.appointmentNotes.${isFirst ? 'addedBy' : 'modifiedBy'}`)} ${
                    isFromCustomer
                        ? t('appointments.appointmentNotes.customer')
                        : `${userJob ? t(jobTitleLabel(userJob)) : ''} ${userName}`
                } ${t('appointments.appointmentNotes.on')} ${moment(createdAt).format(
                    t('dateFormats.shortDate')
                )} ${t('appointments.appointmentNotes.at')} ${moment(createdAt).format(
                    t('dateFormats.time')
                )}`}
            </SpanBold>
            {note && `: "${note}"`}
        </Typography>
    );
};

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: theme.palette.neutral[1],
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.neutral[1],
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const DivNoteWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    padding: '8px 4px',
    borderRadius: '4px',
    width: '100%',

    '&:hover': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

const DivMouseHint = styled('div')(({ theme }) => ({
    minWidth: 'max-content',
    zIndex: 10,
    padding: 3,
    position: 'fixed',
    transform: 'translate(15px, 0px)',
    pointEvents: 'none',
    display: 'none',
    gap: '2px',
    ...theme.typography.h9Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[5],
}));

const SpanBold = styled('span')<{ bold: boolean }>(({ bold }) => ({
    fontWeight: bold ? 700 : 'inherit',
}));

export default NoteEditor;
