import Box from '@mui/material/Box';
import MuiButton from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';
import CharacterIndicator from '../../../../common/components/Inputs/CharacterIndicator';

type AppointmentNoteFormProps = {
    disabled?: boolean;
    onClickSave?: (value: string) => void;
};

const AppointmentNoteForm = ({ disabled, onClickSave }: AppointmentNoteFormProps) => {
    const { t } = useAppTranslation();
    const [note, setNote] = useState<string>('');
    const [inputMode, setInputMode] = useState<boolean>(false);

    const handleSave = () => {
        if (note.trim().length) {
            onClickSave && onClickSave(note.trim());
            setInputMode(false);
            setNote('');
        }
    };

    return (
        <Grid container spacing={0}>
            {inputMode ? (
                <Grid item xs={12}>
                    <Grid container spacing={1} display={'flex'} alignItems={'center'}>
                        <Grid item xs={11}>
                            <TextFormField
                                name="appointment-note"
                                label=""
                                maxLength={100}
                                placeholder={t('appointments.appointmentNotes.enterNote')}
                                value={note}
                                onChange={(event) => {
                                    if (event.target.value.length <= 100) {
                                        setNote(event.target.value);
                                    }
                                }}
                                onEnterPress={() => {
                                    handleSave();
                                }}
                                enableEnterComplete
                                multiline
                                endAdornment={
                                    <InputAdornment position="end">
                                        <IconButton
                                            size="small"
                                            onClick={() => {
                                                handleSave();
                                            }}
                                        >
                                            <CheckIcon size={IconSize.M} />
                                        </IconButton>
                                        <CharacterIndicator charactersCount={note.length} />
                                    </InputAdornment>
                                }
                            />
                        </Grid>
                        <Grid item xs={1}>
                            <Button
                                color="#899198"
                                cmosVariant={'typography'}
                                Icon={CloseIcon}
                                label=""
                                onClick={() => {
                                    setInputMode(false);
                                }}
                            />
                        </Grid>
                    </Grid>
                </Grid>
            ) : (
                <Grid item xs={12}>
                    <MuiButton
                        sx={(theme) => ({
                            border: `1px solid ${theme.palette.neutral[4]}`,
                            borderRadius: '4px !important',
                            background: theme.palette.neutral[2],
                            width: '100%',
                            height: 32,
                        })}
                        onClick={() => {
                            setInputMode(true);
                        }}
                        disabled={disabled}
                    >
                        <Box
                            component="div"
                            sx={{
                                display: 'block',
                                width: '100%',
                            }}
                        >
                            <Typography
                                component="span"
                                sx={(theme) => ({
                                    ...theme.typography.h6Inter,
                                    fontWeight: 'normal',
                                    color: theme.palette.neutral[7],
                                    textTransform: 'none',
                                    float: 'left',
                                    height: 24,
                                    display: 'flex',
                                    alignItems: 'center',
                                })}
                            >
                                {t('appointments.appointmentNotes.addNote')}
                            </Typography>
                            <Box
                                component="span"
                                sx={{
                                    float: 'right',
                                    height: 24,
                                }}
                            >
                                <PlusIcon fill="var(--neutral7)" />
                            </Box>
                        </Box>
                    </MuiButton>
                </Grid>
            )}
        </Grid>
    );
};

export default AppointmentNoteForm;
