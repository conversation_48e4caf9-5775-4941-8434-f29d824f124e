import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

type StepHeaderProps = {
    title: string;
    number: number;
    isRequired?: boolean;
};

const Step = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.primary.main,
    textTransform: 'uppercase',
    marginRight: 10,
}));
const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[8],
    lineHeight: '22px',
}));
const Root = styled('div')({
    display: 'flex',
    gap: 5,
    alignItems: 'center',
    marginBottom: 22,
});

const RequiredLabel = styled('span')(({ theme }) => ({
    position: 'relative',
    top: -10,
    width: 8,
    height: 8,
    borderRadius: '32px',
    color: theme.palette.primary.light,
    zIndex: 2,
}));

export default function StepHeader({ number, title, isRequired }: StepHeaderProps) {
    const { t } = useAppTranslation();

    return (
        <Root>
            <Step>{t('commonLabels.step', { number })}</Step>
            <Title>{title}</Title>
            {isRequired && <RequiredLabel>*</RequiredLabel>}
        </Root>
    );
}
