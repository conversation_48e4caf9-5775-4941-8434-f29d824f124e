import HistoryIcon from '@mui/icons-material/History';
import { IconButton, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { DeleteIcon } from 'common/components/Icons';
import CancelModal from 'common/components/Popups/CancelModal';
import { default as ArrowTooltip, default as Tooltip } from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Colors } from 'common/styles/Colors';
import { useCallback, useState } from 'react';
import { useAppSelector } from 'store';
import { selectEditData as selectRepairShopEditData } from 'store/slices/appointments';
import { selectEditData as selectEnterpriseEditData } from 'store/slices/enterprise/appointments';
import CustomerHistory from 'views/Appointments/CustomerHistory';
import FileAttachmentButton from '../FileAttachmentButton';

const Title = styled('h1')(({ theme }) => ({
    margin: 0,
    ...theme.typography.h4Inter,
    color: '#000000',
}));
const SubTitle = styled('h1')(({ theme }) => ({
    margin: 0,
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));

type AppointmentEditHeaderProps = {
    modified: boolean;
    valid?: boolean;
    onCancel: () => void;
    onSave: () => void;
    onDelete?: () => void;
    isSaving?: boolean;
};

export default function AppointmentEditHeader({
    modified,
    onCancel,
    onDelete,
    onSave,
    valid = true,
    isSaving = false,
}: AppointmentEditHeaderProps) {
    const isEnterprise = useIsEnterpriseRoute();
    const { t } = useAppTranslation();
    const [openCancelModal, setOpenCancelModal] = useState(false);

    const enabledCustomerHistory = useAppSelector((r) => {
        if (isEnterprise) {
            const { customer, vehicle } = selectEnterpriseEditData(r);
            return !!customer && !!vehicle;
        } else {
            const { customer, vehicle } = selectRepairShopEditData(r);
            return !!customer && !!vehicle;
        }
    });
    const [detailsVisible, setDetailsVisible] = useState(false);

    return (
        <>
            <CancelModal
                open={openCancelModal}
                title={t('appointments.cancelAppointmentEditing')}
                onCancel={onCancel}
                onClose={() => setOpenCancelModal(false)}
            />
            <DivRoot>
                <div style={{ flexGrow: 1 }}>
                    <Title>{t('appointments.editAppointment')}</Title>
                    <SubTitle>
                        {t('appointments.requiredFields')}
                        <span style={{ color: Colors.CM1 }}> *</span>
                    </SubTitle>
                </div>
                <DivButtons>
                    <FileAttachmentButton />
                    {onDelete && (
                        <Tooltip
                            position="top"
                            content={t('appointments.deleteAppointmentTooltip')}
                        >
                            <IconButton
                                size="large"
                                style={{ padding: 0 }}
                                onClick={() => onDelete()}
                            >
                                <DeleteIcon fill={Colors.Neutral5} />
                            </IconButton>
                        </Tooltip>
                    )}

                    <ArrowTooltip content={t('appointments.customerHistory.showCustomerHistory')}>
                        <IconButton
                            size="large"
                            onClick={() => setDetailsVisible((x) => !x)}
                            disabled={!enabledCustomerHistory}
                            style={{ padding: 0 }}
                        >
                            <HistoryIcon color={!enabledCustomerHistory ? undefined : 'primary'} />
                        </IconButton>
                    </ArrowTooltip>
                    <Button
                        w={200}
                        disabled={isSaving}
                        cmosVariant={'filled'}
                        color={Colors.Neutral3}
                        label={t('appointments.cancel')}
                        blockMode
                        onClick={() => {
                            if (modified) {
                                setOpenCancelModal(true);
                            } else {
                                onCancel();
                            }
                        }}
                    />
                    <Button
                        w={200}
                        cmosVariant={'filled'}
                        color={Colors.Success}
                        label={t('appointments.saveChanges')}
                        blockMode
                        disabled={!valid || isSaving || !modified}
                        showLoader={isSaving}
                        onClick={useCallback(() => onSave(), [onSave])}
                    />
                </DivButtons>
            </DivRoot>

            <PreviewSheet data-open={detailsVisible}>
                <CustomerHistory
                    onClose={() => {
                        setDetailsVisible(false);
                    }}
                />
            </PreviewSheet>
        </>
    );
}

const DivRoot = styled('div')({
    display: 'flex',
});

const DivButtons = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 8,
});

const PreviewSheet = styled('div')({
    zIndex: 3,
    display: 'flex',
    position: 'fixed',
    top: 'var(--header-height)',
    bottom: 0,
    right: 0,
    transition: 'transform 0.2s',

    '&[data-open=false]': {
        transform: 'translateX(100%)',
    },
});
