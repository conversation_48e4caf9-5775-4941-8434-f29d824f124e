import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IconButton, styled } from '@mui/material';

import Box from '@mui/material/Box';
import { selectAppointmentAttachments } from 'store/slices/appointmentAttachment/selectors';
import {
    clearAppointmentAttachments,
    removeAppointmentAttachment,
} from 'store/slices/appointmentAttachment';
import AppointmentsApi, { AppointmentAttachmentsDto } from 'api/appointments';
import { fetchAppointmentAttachments } from 'store/slices/appointmentAttachment/thunks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import { DeleteIcon } from 'common/components/Icons';
import { PageIcon } from 'common/components/Icons/PageIcon';
import useToasters from 'common/hooks/useToasters';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import Tooltip from 'common/components/Tooltip';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';

const FileListRoot = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    marginTop: '20px',
});

const ScrollArea = styled('div')({
    maxHeight: '150px',
    overflow: 'auto',
    ...scrollbarStyle(),
});

const FileName = styled('span')(({ theme }) => ({
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    ...theme.typography.h5,
    fontWeight: '400',
    color: 'var(--neutral7)',
    textDecoration: 'underline',
}));

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h5,
    color: 'var(--neutral7)',
}));

const NoAttachedFilesText = styled('div')(({ theme }) => ({
    ...theme.typography.h5,
    fontWeight: '400',
    color: 'var(--neutral8)',
}));

type AppointmentFilesProps = {
    appointmentId: string | undefined;
};

export default function AppointmentFiles({ appointmentId }: AppointmentFilesProps) {
    const dispatch = useDispatch();
    const files = useSelector(selectAppointmentAttachments);
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const [openConfirmationModal, setOpenConfirmationModal] = useState<boolean>(false);
    const [fileToRemove, setFileToRemove] = useState<{
        file: File | AppointmentAttachmentsDto;
        index: number;
    } | null>(null);

    useEffect(() => {
        dispatch(clearAppointmentAttachments());
    }, [dispatch]);

    useEffect(() => {
        if (appointmentId) {
            dispatch(
                fetchAppointmentAttachments({
                    appointmentId: appointmentId,
                })
            );
        }
    }, [appointmentId, dispatch]);

    const confirmRemoveFile = (file: File | AppointmentAttachmentsDto, index: number) => {
        setFileToRemove({ file, index });
        setOpenConfirmationModal(true);
    };

    const handleDownload = (file: File | AppointmentAttachmentsDto) => {
        const a = document.createElement('a');
        if (file instanceof File) {
            const url = URL.createObjectURL(file);
            const a = document.createElement('a');
            a.href = url;
            a.download = file.name;
            a.click();
            URL.revokeObjectURL(url);
        } else {
            a.href = file.url;
            a.download = file.name;
            a.click();
        }
    };

    const handleRemove = async () => {
        if (fileToRemove) {
            const { file, index } = fileToRemove;
            if (!(file instanceof File)) {
                await AppointmentsApi.deleteAttachment(file.id);
            }
            dispatch(removeAppointmentAttachment(index));

            toasters.success(file.name, t('appointments.appointmentFiles.fileDeleted'));
            setFileToRemove(null);
        }
        setOpenConfirmationModal(false);
    };

    return (
        <FileListRoot>
            <Title>{t('appointments.appointmentFiles.attachedFiles')}</Title>
            <ScrollArea>
                {files.length > 0 ? (
                    files.map((file, index) => (
                        <Box key={index}>
                            <Box display={'flex'} alignItems={'center'}>
                                <Box marginTop={'-2px'}>
                                    <PageIcon fill={'var(--neutral7)'} />
                                </Box>

                                <FileName>{file.name}</FileName>

                                <Tooltip content={t('appointments.appointmentFiles.downloadFile')}>
                                    <IconButton onClick={() => handleDownload(file)} size="small">
                                        <DownloadIcon fill={'var(--neutral7)'} />
                                    </IconButton>
                                </Tooltip>

                                <Tooltip content={t('appointments.appointmentFiles.deleteFile')}>
                                    <IconButton
                                        onClick={() => confirmRemoveFile(file, index)}
                                        size="small"
                                    >
                                        <DeleteIcon fill={'var(--neutral7)'} />
                                    </IconButton>
                                </Tooltip>
                            </Box>
                        </Box>
                    ))
                ) : (
                    <NoAttachedFilesText>
                        {t('appointments.appointmentFiles.noAttachedFiles')}
                    </NoAttachedFilesText>
                )}
            </ScrollArea>
            <DeleteConfirmationPopup
                open={openConfirmationModal}
                title={t('appointments.appointmentFiles.deleteFileQuestion')}
                body={t('appointments.appointmentFiles.areYouSureDelete')}
                cancel={t('appointments.appointmentFiles.noDelete')}
                confirm={t('appointments.appointmentFiles.deleteFile')}
                onClose={() => setOpenConfirmationModal(false)}
                onConfirm={handleRemove}
            />
        </FileListRoot>
    );
}
