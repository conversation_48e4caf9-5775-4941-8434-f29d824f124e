import { IconButton } from '@mui/material';
import { AttachFileIcon } from 'common/components/Icons/AttachFileIcon';
import Tooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addAppointmentAttachment } from 'store/slices/appointmentAttachment';
import { selectAppointmentAttachments } from 'store/slices/appointmentAttachment/selectors';
import { selectIsEnterprise } from 'store/slices/globalSettingsSlice';

const allowedExtensions = [
    'doc',
    'docx',
    'pdf',
    'txt',
    'rtf',
    'odt',
    'xls',
    'xlsx',
    'csv',
    'ods',
    'ppt',
    'pptx',
    'odp',
    'html',
    'htm',
    'xml',
    'mp4',
    'mkv',
    'avi',
    'mov',
    'wmv',
    'flv',
    'webm',
    '3gp',
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'tiff',
    'tif',
    'svg',
];

const FileAttachmentButton = () => {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const files = useSelector(selectAppointmentAttachments);
    const isEnterprise = useSelector(selectIsEnterprise);
    const attachFileRef = useRef<HTMLInputElement>(null);
    const toasters = useToasters();

    const handleAttachFile = () => {
        if (attachFileRef.current) attachFileRef.current.click();
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (!event.target.files || !event.target.files[0]) return;

        const file = event.target.files[0];
        const fileExtension = file.name.split('.').pop()?.toLowerCase();

        if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
            toasters.danger('Invalid file type');
            return;
        }

        const maxTotalSize = 10 * 1024 * 1024; // 10 MB
        const currentTotalSize = files.reduce(
            (totalSize, attachment) => totalSize + (attachment.size || 0),
            0
        );
        const newTotalSize = currentTotalSize + (file.size || 0);

        if (newTotalSize > maxTotalSize) {
            toasters.danger(
                t('appointments.appointmentFiles.fileSizeExceeded10Mb'),
                t('appointments.appointmentFiles.fileSizeExceeded')
            );
            return;
        }

        dispatch(addAppointmentAttachment(file));
    };

    return (
        !isEnterprise && (
            <Tooltip content={t('appointments.appointmentFiles.attachFile')}>
                <IconButton onClick={handleAttachFile} size="large" style={{ padding: 0 }}>
                    <input
                        ref={attachFileRef}
                        type="file"
                        style={{ display: 'none' }}
                        onChange={handleFileChange}
                        accept={allowedExtensions.map((ext) => `.${ext}`).join(', ')}
                    />
                    <AttachFileIcon fill={'var(--cm1)'} />
                </IconButton>
            </Tooltip>
        )
    );
};

export default FileAttachmentButton;
