import { useQuery } from '@tanstack/react-query';
import AppointmentsApi, { ServiceAdvisorDto } from 'api/appointments';
import { EnterpriseAppointmentsApi } from 'api/enterprise';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import isEqual from 'lodash/isEqual';
import { useCallback, useMemo } from 'react';
import { useStore } from 'react-redux';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectEditData } from 'store/slices/appointments/selectors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import { useValidation } from 'utils/validation';
import { useRepairShopAppointmentSettings } from './queries';

export function useEnterpriseServiceAdvisors(shopId: string | null) {
    return useServiceAdvisors({
        cacheKey: shopId ?? '',
        getAdvisors: () => EnterpriseAppointmentsApi.getServiceAdvisors(shopId!),
        enabled: !!shopId,
    });
}

export function useRepairShopServiceAdvisors() {
    return useServiceAdvisors({
        cacheKey: 'default',
        getAdvisors: AppointmentsApi.getServiceAdvisors,
        enabled: true,
    });
}

type UseServiceAdvisors = {
    cacheKey: string;
    getAdvisors: () => Promise<ServiceAdvisorDto[]>;
    enabled: boolean;
};

function useServiceAdvisors({ cacheKey, getAdvisors, enabled }: UseServiceAdvisors) {
    const { data } = useQuery(['users', 'advisors', { cacheKey }], getAdvisors, {
        cacheTime: Infinity,
        staleTime: 1000,
        enabled,
    });

    //Remove all in-active service advisors
    return useMemo(() => data?.filter((s) => s.isActive) ?? [], [data]);
}

export function useAppointmentEditDataGetter() {
    const store = useStore();
    return useCallback(() => selectEditData(store.getState()), [store]);
}

const selectIsAppointmentDataFlags = createSelector(
    selectEditData,
    ({ appointmentData: appointment, vehicle, customer }) => {
        return {
            vehicleModified: vehicle
                ? appointment.vehiclePlates !== vehicle.plates ||
                  appointment.vehicleModel !== vehicle.model ||
                  appointment.vehicleBrand !== vehicle.brand ||
                  appointment.vehicleVin !== vehicle.vin ||
                  appointment.vehicleYear !== vehicle.year ||
                  appointment.vehicleColor !== vehicle.color
                : false,
            customerModified: customer
                ? appointment.customerFirstName !== customer.firstName ||
                  appointment.customerLastName !== customer.lastName ||
                  appointment.customerMobile !== customer.mobile ||
                  appointment.customerEmail !== customer.email ||
                  appointment.customerTaxId !== customer.taxIdentification
                : false,
            step1Modified: !!(
                appointment.customerFirstName ||
                appointment.customerLastName ||
                appointment.customerEmail ||
                appointment.customerTaxId ||
                appointment.vehiclePlates ||
                appointment.vehicleVin ||
                appointment.vehicleBrand ||
                appointment.vehicleModel ||
                appointment.vehicleYear ||
                appointment.vehicleColor
            ),
            step2Disabled: !(appointment.customerFirstName && appointment.customerMobile),
            step3Disabled: !(appointment.reasons.length > 0),
        };
    }
);

export function useAppointmentDataFlags() {
    return useAppSelector(selectIsAppointmentDataFlags, isEqual);
}

export function useIsAppointmentDataValid(
    isAppointmentNumberInUse: boolean,
    isNumberRequired?: boolean
) {
    const settings = useRepairShopAppointmentSettings();

    const selector = useMemo(
        () =>
            appointmentEditorStateSelectors.createAppointmentDataValidSelector(
                selectEditData,
                isNumberRequired !== undefined
                    ? !isNumberRequired
                    : !settings?.activateAutomaticAppointmentNumber,
                isAppointmentNumberInUse
            ),
        [settings, isNumberRequired, isAppointmentNumberInUse]
    );
    const isValid = useAppSelector(selector);
    return isValid;
}

export function useEditAppointmentData() {
    return useAppSelector(
        appointmentEditorStateSelectors.createEditAppointmentDataSelector(false),
        isEqual
    );
}

export function useEnterpriseEditAppointmentData() {
    return useAppSelector(
        appointmentEditorStateSelectors.createEnterpriseEditAppointmentDataSelector(false),
        isEqual
    );
}

export function useNameValidator(name: string) {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    return useValidation(name, (name) => (name.trim() ? true : 'name must be set'), {
        onErrorChanged() {
            toasters.warning(
                t('appointments.step3.theNameCannotBeEmpty'),
                t('appointments.step3.theClientCouldNotBeEdited')
            );
        },
        defaultValidState: true,
    });
}
