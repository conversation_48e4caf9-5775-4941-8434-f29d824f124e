import HistoryIcon from '@mui/icons-material/History';
import { IconButton, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import CancelModal from 'common/components/Popups/CancelModal';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useState } from 'react';
import FileAttachmentButton from '../FileAttachmentButton';

type AppointmentNewHeaderProps = {
    modified: boolean;
    valid: boolean;
    onCancel: () => void;
    onSave: () => void;
    loading?: boolean;
    enabledCustomerHistory: boolean;
    toggleCustomerHistory: () => void;
};

export default function AppointmentNewHeader({
    onCancel,
    modified,
    onSave,
    valid,
    loading,
    enabledCustomerHistory,
    toggleCustomerHistory,
}: AppointmentNewHeaderProps) {
    const { t } = useAppTranslation();
    const [openCancelModal, setOpenCancelModal] = useState(false);

    return (
        <>
            <CancelModal
                open={openCancelModal}
                title={t('appointments.cancelNewAppointmentRegister')}
                onCancel={onCancel}
                onClose={() => setOpenCancelModal(false)}
            />
            <DivRoot>
                <Title>{t('appointments.newAppointment')}</Title>

                <DivButtons>
                    <FileAttachmentButton />
                    <ArrowTooltip content={t('appointments.customerHistory.showCustomerHistory')}>
                        <IconButton
                            size="large"
                            onClick={toggleCustomerHistory}
                            disabled={!enabledCustomerHistory}
                            style={{ padding: 0 }}
                        >
                            <HistoryIcon
                                fontSize="medium"
                                color={!enabledCustomerHistory ? undefined : 'primary'}
                            />
                        </IconButton>
                    </ArrowTooltip>
                    <Button
                        w={200}
                        disabled={loading}
                        cmosVariant={'filled'}
                        color={Colors.Neutral3}
                        label={t('appointments.cancel')}
                        blockMode
                        onClick={() => {
                            if (modified) {
                                setOpenCancelModal(true);
                            } else {
                                onCancel();
                            }
                        }}
                    />
                    <Button
                        w={200}
                        cmosVariant={'filled'}
                        color={Colors.Success}
                        label={t('appointments.createAppointment')}
                        blockMode
                        disabled={!valid || loading}
                        showLoader={loading}
                        onClick={useCallback(() => onSave(), [onSave])}
                    />
                </DivButtons>
            </DivRoot>
        </>
    );
}

const Title = styled('h1')(({ theme }) => ({
    margin: 0,
    ...theme.typography.h4Inter,
    color: '#000000',
    flexGrow: 1,
}));

const DivRoot = styled('div')({
    display: 'flex',
});

const DivButtons = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 8,
});
