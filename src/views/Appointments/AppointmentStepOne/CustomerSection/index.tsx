import { Grid } from '@mui/material';
import CustomersAPI, { CustomerSearchItemDto } from 'api/Clients/Customers';
import CustomersApi from 'api/customers';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import { isValidEmail, isValidMobileNumber } from 'common/Helpers';
import PhoneField from 'common/components/Inputs/PhoneField';
import TextF<PERSON><PERSON>ield from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { isEqual } from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    setCustomerFromSearchItem,
    setNewCustomerAndVehicle,
    updateAppointmentData,
} from 'store/slices/appointments';
import { selectEditData } from 'store/slices/appointments/selectors';
import { selectCountryCode } from 'store/slices/globalSettingsSlice';
import { selectUserPermission } from 'store/slices/user';
import { useNameValidator } from 'views/Appointments/common';
import { AppointmentSection } from 'views/Appointments/common/basic-components';
import CreateNewCustomerPopupCustomApi, {
    CustomerCreatedEvent,
} from 'views/Components/CreateNewCustomerPopup/CreateNewCustomerPopupCustomApi';
import CustomerAutocomplete from 'views/Components/CustomerAutocomplete';

const selectCustomerSectionData = createSelector(
    selectEditData,
    ({
        appointmentData: {
            customerEmail,
            customerFirstName,
            customerLastName,
            customerId,
            customerMobile,
            customerTaxId,
        },
        customer,
        initialCustomer,
    }) => ({
        customerEmail,
        customerFirstName,
        customerLastName,
        customerId,
        customerMobile,
        customerTaxId,
        customer,
        initialCustomer,
    })
);

export default function CustomerSection() {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { customer, initialCustomer, ...appointment } = useAppSelector(
        selectCustomerSectionData,
        isEqual
    );
    const customerSelected = !!customer;
    const [isMobileValid, setIsMobileValid] = useState(true);
    const allowEditCustomers = useAppSelector((r) => selectUserPermission(r).allowEditCustomers);

    const countryCode = useAppSelector(selectCountryCode);

    const onSelectCustomer = useCallback(
        (c: CustomerSearchItemDto) => {
            dispatch(setCustomerFromSearchItem(c));
        },
        [dispatch]
    );

    // VALIDATION
    const nameValidation = useNameValidator(appointment.customerFirstName);
    const validEmail = !appointment.customerEmail || isValidEmail(appointment.customerEmail);

    // CREATE CUSTOMER POPUP
    const [createCustomerPopupOpen, setCreateCustomerPopupOpen] = useState(false);
    const onCustomerAndVehicleCreated = useCallback(
        (event: CustomerCreatedEvent) => {
            dispatch(
                setNewCustomerAndVehicle({
                    customer: event.customer,
                    vehicle: event.vehicle || undefined,
                })
            );
        },
        [dispatch]
    );

    useEffect(() => {
        if (appointment.customerMobile !== '') {
            validateMobile();
        }
    }, [appointment.customerMobile]);

    const validateMobile = async () => {
        if (!appointment.customerMobile.trim()) {
            toasters.warning(
                t('appointments.step3.theMobileCannotBeEmpty'),
                t('appointments.step3.theClientCouldNotBeEdited')
            );
            setIsMobileValid(false);
            return;
        }

        if (!customer) {
            throw new Error('customer is not defined');
        }

        if (!isValidMobileNumber(countryCode, appointment.customerMobile)) {
            setIsMobileValid(false);
            return;
        }

        try {
            const isDuplicatedMobile = await CustomersApi.isDuplicatedMobile(
                customer.id,
                appointment.customerMobile
            );
            if (isDuplicatedMobile) {
                toasters.warning(
                    t('appointments.step3.theMobileIsRegistered'),
                    t('appointments.step3.theClientCouldNotBeEdited')
                );
                setIsMobileValid(false);
                return;
            }
        } catch {
            // no-op
        }

        setIsMobileValid(true);
    };

    return (
        <CustomersSearchApiProvider mode="shop">
            {createCustomerPopupOpen && (
                <CreateNewCustomerPopupCustomApi
                    createNewVehicle={CustomersApi.createVehicle}
                    createNewCustomer={CustomersAPI.createCustomer}
                    open={createCustomerPopupOpen}
                    onClose={() => setCreateCustomerPopupOpen(false)}
                    onCustomerAndVehicleCreated={onCustomerAndVehicleCreated}
                />
            )}
            <AppointmentSection>
                <CustomerAutocomplete
                    value={customer?.id}
                    onSelect={onSelectCustomer}
                    onOpenCreatePopup={() => setCreateCustomerPopupOpen(true)}
                    initiallySelected={initialCustomer}
                />
            </AppointmentSection>
            <AppointmentSection>
                <Grid container spacing={1} alignItems="flex-end">
                    <Grid item xs={12} md={2}>
                        <TextFormField
                            name="customer-name"
                            cmosVariant="grey"
                            label={t('appointments.step1.customerName')}
                            placeholder={t('appointments.step1.name')}
                            isRequired
                            showValidationIndicators
                            disabled={!customerSelected || !allowEditCustomers}
                            isInvalid={!nameValidation.valid}
                            value={appointment.customerFirstName || ''}
                            onBlur={nameValidation.validate}
                            onChange={(event) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { customerFirstName: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextFormField
                            name="customer-lastName"
                            cmosVariant="grey"
                            label={t('appointments.step1.lastNames')}
                            placeholder={t('appointments.step1.lastNames')}
                            showValidationIndicators
                            disabled={!customerSelected || !allowEditCustomers}
                            value={appointment.customerLastName || ''}
                            onChange={(event) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { customerLastName: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <PhoneField
                            name="customer-mobile"
                            isInvalid={!isMobileValid}
                            placeholder={t('appointments.step1.mobile')}
                            disabled={!customerSelected || !allowEditCustomers}
                            showValidationIndicators
                            isRequired
                            value={appointment.customerMobile}
                            onChange={(mobile) => {
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { customerMobile: mobile },
                                    })
                                );
                            }}
                            cmosVariant="grey"
                        />
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <TextFormField
                            name="customer-email"
                            cmosVariant="grey"
                            label={t('appointments.step1.email')}
                            placeholder={t('appointments.step1.email')}
                            disabled={!customerSelected || !allowEditCustomers}
                            value={appointment.customerEmail || ''}
                            isInvalid={!validEmail}
                            onChange={(event) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { customerEmail: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextFormField
                            name="customer-taxIdentification"
                            cmosVariant="grey"
                            label={t('appointments.step1.taxIdentification')}
                            placeholder={t('appointments.step1.taxIdentification')}
                            disabled={!customerSelected || !allowEditCustomers}
                            value={appointment.customerTaxId || ''}
                            onChange={(event) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { customerTaxId: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                </Grid>
            </AppointmentSection>
        </CustomersSearchApiProvider>
    );
}
