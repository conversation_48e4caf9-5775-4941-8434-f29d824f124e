import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { memo } from 'react';
import StepHeader from '../common/StepHeader';
import CustomerSection from './CustomerSection';
import VehicleSection from './VehicleSection';

type AppointmentStepOneProps = {
    repairShopKey?: string;
};

function AppointmentStepOne({ repairShopKey }: AppointmentStepOneProps) {
    const { t } = useAppTranslation();

    return (
        <>
            <StepHeader title={t('appointments.step1.selectACustomer')} number={1} />
            <CustomerSection />
            <VehicleSection repairShopKey={repairShopKey} />
        </>
    );
}

export default memo(AppointmentStepOne);
