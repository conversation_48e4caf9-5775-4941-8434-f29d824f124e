import { Grid } from '@mui/material';
import CustomersApi, { VehicleDetailsDto, VehicleListItemDto } from 'api/customers';
import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { isEqual } from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    getCustomerHistory,
    removeVehicle,
    setNewVehicle,
    setVehicleFromListItem,
    updateAppointmentData,
} from 'store/slices/appointments';
import { selectEditData } from 'store/slices/appointments/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectUserPermission } from 'store/slices/user';
import { AppointmentSection } from 'views/Appointments/common/basic-components';
import BrandPicker from 'views/Components/Autocomplete/BrandPicker';
import ModelPicker from 'views/Components/Autocomplete/ModelPicker';
import YearPicker from 'views/Components/Autocomplete/YearPicker';
import CreateNewVehiclePopupCustomApi from 'views/Components/CreateNewVehiclePopup/CreateNewVehiclePopupCustomApi';
import CustomerVehiclesAutocompleteCustomApi, {
    useVehiclesQueryModifiers,
} from 'views/Components/CustomerVehiclesAutocomplete/CustomerVehiclesAutocompleteCustomApi';

const selectVehicleSectionData = createSelector(
    selectEditData,
    ({
        vehicle,
        customer,
        appointmentData: {
            vehicleBrand,
            vehicleColor,
            vehicleId,
            vehicleModel,
            vehiclePlates,
            vehicleVin,
            vehicleYear,
        },
        initialVehicle,
    }) => ({
        vehicleBrand,
        vehicleColor,
        vehicleId,
        vehicleModel,
        vehiclePlates,
        vehicleVin,
        vehicleYear,
        vehicle,
        customerId: customer?.id,
        initialVehicle,
    })
);

export default function VehicleSection({ repairShopKey }: { repairShopKey?: string }) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const allowEditVehicles = useAppSelector((r) => selectUserPermission(r).allowEditVehicles);

    const settings = useAppSelector(selectSettings);

    const { vehicle, customerId, initialVehicle, ...appointment } = useAppSelector(
        selectVehicleSectionData,
        isEqual
    );
    const vehicleSelected = !!vehicle;

    const [createVehiclePopupOpen, setCreateVehiclePopupOpen] = useState(false);

    const { add: addVehicle } = useVehiclesQueryModifiers();

    useEffect(() => {
        if (customerId && vehicle) {
            dispatch(
                getCustomerHistory({
                    shopKey: settings.uid,
                    customerId: customerId,
                    vehicleId: vehicle.id,
                })
            );
        }
    }, [settings.uid, customerId, vehicle, dispatch]);

    const onVehicleCreated = useCallback(
        (v: VehicleDetailsDto) => {
            dispatch(setNewVehicle(v));
            if (customerId) addVehicle(customerId, v);
        },
        [dispatch, customerId, addVehicle]
    );

    const onVehicleSelected = useCallback(
        (v?: VehicleListItemDto) => {
            if (v) {
                dispatch(setVehicleFromListItem(v));
            } else {
                dispatch(removeVehicle());
            }
        },
        [dispatch]
    );

    const onVehicleAutoSelected = useCallback(
        (v: VehicleListItemDto) => {
            dispatch(setVehicleFromListItem(v));
        },
        [dispatch]
    );

    return (
        <>
            <CreateNewVehiclePopupCustomApi
                createNewVehicle={CustomersApi.createVehicle}
                open={createVehiclePopupOpen}
                onClose={() => setCreateVehiclePopupOpen(false)}
                onVehicleCreated={onVehicleCreated}
                customerId={customerId ?? null}
            />
            <AppointmentSection>
                <CustomerVehiclesAutocompleteCustomApi
                    cacheKey="default"
                    getVehicles={CustomersApi.getCustomerVehicles}
                    customerId={customerId}
                    value={vehicle?.id}
                    onSelect={onVehicleSelected}
                    onAutoSelect={onVehicleAutoSelected}
                    onOpenPopup={() => setCreateVehiclePopupOpen(true)}
                    initialValue={initialVehicle}
                />
            </AppointmentSection>
            <AppointmentSection>
                <Grid container spacing={1}>
                    <Grid item xs={12} md={2}>
                        <TextFormField
                            name="vehicle-plate"
                            cmosVariant="grey"
                            label={t('appointments.step1.plates')}
                            placeholder={t('appointments.step1.plates')}
                            disabled={!vehicleSelected || !allowEditVehicles}
                            value={appointment.vehiclePlates || ''}
                            onChange={(event) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { vehiclePlates: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextFormField
                            name="vehicle-vin"
                            cmosVariant="grey"
                            label={t('commonLabels.vin')}
                            placeholder={t('commonLabels.vin')}
                            disabled={!vehicleSelected || !allowEditVehicles}
                            value={appointment.vehicleVin || ''}
                            onChange={(event) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { vehicleVin: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <BrandPicker
                            disabled={!vehicleSelected || !allowEditVehicles}
                            value={appointment.vehicleBrand ?? undefined}
                            enterpriseRepairShopKey={repairShopKey}
                            onChange={(brandName) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: {
                                            vehicleBrand: brandName,
                                            vehicleModel: '',
                                            vehicleYear: '',
                                        },
                                    })
                                )
                            }
                            label={t('appointments.step1.brand')}
                            placeholder={t('appointments.step1.brand')}
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <ModelPicker
                            brandName={appointment.vehicleBrand ?? undefined}
                            label={t('appointments.step1.model')}
                            placeholder={t('appointments.step1.model')}
                            disabled={!vehicleSelected || !allowEditVehicles}
                            value={appointment.vehicleModel}
                            onChange={(modelName) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: {
                                            vehicleModel: modelName,
                                            vehicleYear: '',
                                        },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <YearPicker
                            modelName={appointment.vehicleModel ?? undefined}
                            brandName={appointment.vehicleBrand ?? undefined}
                            label={t('appointments.step1.year')}
                            placeholder={t('appointments.step1.year')}
                            disabled={!vehicleSelected || !allowEditVehicles}
                            value={appointment.vehicleYear}
                            onChange={(y) =>
                                dispatch(
                                    updateAppointmentData({
                                        appointment: {
                                            vehicleYear: y,
                                        },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextFormField
                            name="vehicle-color"
                            cmosVariant="grey"
                            label={t('appointments.step1.color')}
                            placeholder={t('appointments.step1.color')}
                            disabled={!vehicleSelected || !allowEditVehicles}
                            value={appointment.vehicleColor || ''}
                            onChange={(event) => {
                                if (vehicle)
                                    dispatch(
                                        updateAppointmentData({
                                            appointment: {
                                                vehicleColor: event.target.value,
                                            },
                                        })
                                    );
                            }}
                        />
                    </Grid>
                </Grid>
            </AppointmentSection>
        </>
    );
}
