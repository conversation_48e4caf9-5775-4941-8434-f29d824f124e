import { Box, Grid, styled } from '@mui/material';
import Typography from '@mui/material/Typography';
import { ScheduleWindow } from 'api/appointments';
import { BoxShadow } from 'common/components/Box/BoxShadow';
import { CircleIconWithBorder } from 'common/components/Icons';
import Calendar from 'common/components/Inputs/Calendar';
import Dropdown from 'common/components/Inputs/Dropdown';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { OptionStyle } from 'common/styles/OptionStyle';
import { isEqual } from 'lodash';
import moment from 'moment';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { appointmentsActions, fetchSchedule } from 'store/slices/appointments';
import { selectEditData, selectScheduleWindows } from 'store/slices/appointments/selectors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import { useRepairShopAppointmentSettings, useRepairShopServiceAdvisors } from '../common';
import StepHeader from '../common/StepHeader';
import AppointmentIntervals from './AppointmentIntervals';
import { capitalizeFirstLetter } from './helper';

const selectIsDisabled =
    appointmentEditorStateSelectors.createStep3DisabledSelector(selectEditData);
const selectStepThreeState = createSelector(
    selectEditData,
    selectIsDisabled,
    ({ appointmentData: { serviceAdvisorId, startsAt, id }, originalData }, isDisabled) => ({
        serviceAdvisorId,
        startsAt,
        isEditing: !!id,
        isDisabled,
        originalData,
    })
);

const AppointmentStepThree = () => {
    const { t } = useAppTranslation();
    const { isDisabled, isEditing, ...appointment } = useAppSelector(selectStepThreeState, isEqual);
    const dispatch = useAppDispatch();
    const advisors = useRepairShopServiceAdvisors();
    const settings = useRepairShopAppointmentSettings();
    const options = useMemo(
        () => [
            {
                label: t('appointments.step3.anyServiceAdvisor'),
                value: null,
                icon: CircleIconWithBorder,
            },

            ...(advisors ?? []).map((x) => ({
                value: x.id,
                label: x.name,
                icon: CircleIconWithBorder,
                color: x.color,
            })),
        ],
        [advisors, t]
    );
    const selectedAdvisorId = appointment.serviceAdvisorId;
    const selectedAdvisorOption = useMemo(
        () => options.find((x) => x.value === selectedAdvisorId),
        [selectedAdvisorId, options]
    );

    const localTime = useMemo(
        () => moment(`${appointment.startsAt.date} ${appointment.startsAt.time}`),
        [appointment.startsAt]
    );
    const setStrDate = (date: string) =>
        dispatch(appointmentsActions.setAppointmentDate(moment(date).format('YYYY-MM-DD')));

    const {
        hasAvailability,
        windows: availableSchedules,
        hasAvailableSchedule,
    } = useAppSelector(selectScheduleWindows);
    const appointmentDate = appointment.startsAt;

    useEffect(() => {
        dispatch(
            fetchSchedule({
                date: appointmentDate.date,
                advisorId: selectedAdvisorId,
            })
        );
    }, [appointmentDate.date, selectedAdvisorId, dispatch]);

    const selectSchedule = (schedule: ScheduleWindow | undefined) => {
        if (!schedule) return;
        dispatch(appointmentsActions.setScheduleWindow(schedule));
    };

    const daysOff = useMemo(
        () => settings?.workingDays.filter((day) => !day.active).map((day) => day.dayNumber),
        [settings]
    );
    const disableDaysOff = useCallback(
        (date: Date | null): boolean => {
            if (!date || !daysOff) {
                return false;
            }

            return daysOff.findIndex((day) => day === date.getDay()) !== -1 || !!isDisabled;
        },
        [daysOff, isDisabled]
    );

    return (
        <>
            <Grid container spacing={0}>
                <Grid item xs={12} md={6}>
                    <StepHeader
                        number={3}
                        title={t('appointments.step3.selectTheTimeAndDateOfTheAppointment')}
                    />
                </Grid>
                <Grid
                    item
                    xs={12}
                    md={6}
                    style={{ display: 'flex', alignItems: 'end', marginBottom: '22px' }}
                >
                    <SelectedDate>{`${capitalizeFirstLetter(
                        localTime.format(t('dateFormats.longDateWithDay'))
                    )} / ${
                        appointmentDate.time === '00:00:00' ? '--' : appointmentDate.time
                    }`}</SelectedDate>
                </Grid>
            </Grid>
            <Box display={'flex'} sx={{ marginTop: 4, gap: 6 }}>
                <Box>
                    <Caption>
                        {t('appointments.step3.selectTheDate')}{' '}
                        <span style={{ color: Colors.CM1 }}>*</span>
                    </Caption>
                    <BoxShadow style={{ margin: 0 }}>
                        <Calendar
                            disablePast
                            disabled={isDisabled}
                            value={localTime.toDate()}
                            shouldDisableDate={disableDaysOff}
                            onChange={(date) => {
                                if (date) {
                                    setStrDate(
                                        moment(new Date(date)).format('YYYY-MM-DDT00:00:00')
                                    );
                                }
                            }}
                        />
                    </BoxShadow>
                </Box>

                <Box>
                    <Grid container spacing={0}>
                        <Grid item xs={8}>
                            <Caption>
                                {`${t('appointments.step3.selectTheServiceAdvisorInCharge')} `}
                                <span style={{ color: Colors.CM1 }}>*</span>
                            </Caption>
                            <Dropdown
                                styles={{
                                    menuList: {
                                        maxHeight: '210px',
                                    },
                                }}
                                name="users"
                                cmosVariant="roundedPrimary"
                                optionStyle={OptionStyle.icons}
                                isSearchable
                                options={options}
                                disabled={isDisabled}
                                value={selectedAdvisorOption}
                                onChange={(event) => {
                                    dispatch(appointmentsActions.setAdvisor(event?.value ?? null));
                                }}
                            />
                        </Grid>
                        <Grid container spacing={0} direction="column" style={{ marginTop: 30 }}>
                            {(hasAvailability || isEditing) && (
                                <Grid item xs>
                                    <Caption>
                                        {`${t('appointments.step3.selectTime')} `}
                                        <span style={{ color: Colors.CM1 }}>*</span>
                                    </Caption>
                                    <AppointmentIntervals
                                        originalAppointmentDate={appointment.originalData?.startsAt}
                                        windows={availableSchedules}
                                        onWindowChange={selectSchedule}
                                        appointmentDate={appointmentDate}
                                    />
                                </Grid>
                            )}
                            {!hasAvailableSchedule && !hasAvailability && (
                                <Caption>{t('appointments.step3.thereIsNoAvailability')}</Caption>
                            )}
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </>
    );
};

const SelectedDate = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.primary.main,
}));

const Caption = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    marginBottom: 15,
}));

export default memo(AppointmentStepThree);
