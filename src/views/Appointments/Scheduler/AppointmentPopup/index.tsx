import TurnedInIcon from '@mui/icons-material/TurnedIn';
import {
    Box,
    CircularProgress,
    Divider,
    Grid,
    IconButton,
    LinearProgress,
    Popover,
    styled,
    Typography,
    useTheme,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentAPI from 'api/Appointment';
import AppointmentsApi from 'api/appointments';
import clsx from 'clsx';
import { getVehicleFormatted } from 'common/Helpers';
import AttachedFilesModal from 'common/components/AttachedFiles';
import { Button } from 'common/components/Button';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CustomerIcon } from 'common/components/Icons/CustomerIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { FolderOpenIcon } from 'common/components/Icons/FolderOpenIcon';
import { ValetServiceIcon } from 'common/components/Icons/ValetServiceIcon';
import { WebIcon } from 'common/components/Icons/WebIcon';
import { default as ArrowTooltip, default as Tooltip } from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import useQueryParam from 'common/hooks/useQueryParam';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import { Duration } from 'luxon';
import { useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { generatePath, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters, updateAppointmentStatus } from 'store/slices/appointments';
import { selectDate } from 'store/slices/appointments/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import {
    useAppointmentsQueryController,
    useRepairShopAppointmentSettings,
    useRepairShopCustomAppointmentReasons,
} from 'views/Appointments/common';
import { NewOrderDataPopup } from 'views/Orders/NewOrderPopup';
import { NewOrderData } from 'views/Orders/NewOrderPopup/NewOrderDataPopup';
import DeleteAppointmentModal from '../DeleteAppointmentModal';
import { getStatusText } from '../_common';
import { useAppointmentDetails } from '../_hooks';
import OmniChannelParameter from './OmniChannelParameter';
import Parameter from './Parameter';
import { AppointmentPopupContext, IAppointmentPopupContext } from './context';

export type OnAppointmentDeleteRequestedHandler = (appointmentData: {
    id: string;
    number: string;
}) => void;

export type AppointmentPopupProps = {
    id?: string;
    onClose: () => void;
    anchorEl?: HTMLElement | null;
    onOrderCreated: (orderId: number) => void;
    onCustomerDidNotArrive?: () => void;
    onDeleted: () => void;
    autoSwitchDate?: boolean;
};

export default function AppointmentPopup({
    onClose,
    id,
    onOrderCreated,
    onDeleted,
    onCustomerDidNotArrive,
    anchorEl,
    autoSwitchDate,
}: AppointmentPopupProps) {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    const appointmentSettings = useRepairShopAppointmentSettings();
    const synchronizeAppointmentsEnabled =
        appointmentSettings?.synchronizeAppointmentsEnabled ?? true;
    const userPermission = useSelector(selectUserPermission);
    const selectedDate = useSelector(selectDate);
    const autoOrderNumber =
        useAppSelector(selectSettings).repairShopSettings?.features.autoOrderNumber;

    const [showOrderInfoPopup, setShowOrderInfoPopup] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showAttachedFileModal, setAttachedFileModal] = useState(false);

    const { isCustomApptReasonEnabled } = useRepairShopCustomAppointmentReasons();

    const enabled = !!id;
    const { data, isInitialLoading, isFetching } = useAppointmentDetails(
        id,
        enabled,
        (fetchedData) => {
            if (!autoSwitchDate) return;

            if (selectedDate !== fetchedData.startsAt.date) {
                dispatch(setFilters({ date: fetchedData.startsAt.date }));
            }
        }
    );

    const handleOrderData = async (data: NewOrderData) => handleOrderConvertation(data.number);
    const convertToOrder = useMutation(
        (orderNumber?: string) => AppointmentAPI.convertAppointment(id!, orderNumber),
        {
            onSuccess: (response) => {
                if (response) onOrderCreated(response.orderId);
            },
        }
    );

    const handleOrderConvertation = async (orderNumber?: string) => {
        if (autoOrderNumber === undefined)
            throw new Error('autoOrderNumber feature flag is not available');
        if (autoOrderNumber) {
            return convertToOrder.mutateAsync(undefined);
        } else {
            if (orderNumber) {
                return await convertToOrder.mutateAsync(orderNumber);
            }
            setShowOrderInfoPopup(true);
            return undefined;
        }
    };

    const markAsDidNotArrive = useMutation(
        async () => {
            await AppointmentsApi.updateStatus(id!, 'CustomerDidNotArrive');
        },
        {
            onSuccess: () => {
                dispatch(
                    updateAppointmentStatus({
                        id: id!,
                        status: AppointmentStatusEnum.CustomerDidNotArrive,
                    })
                );
                if (onCustomerDidNotArrive) onCustomerDidNotArrive();
            },
        }
    );

    const customerNotes = useMemo(
        () => (data ? data.notes?.filter((x) => x.type === 'ForCustomer') : []),
        [data]
    );

    const internalNotes = useMemo(
        () => (data ? data.notes?.filter((x) => x.type === 'ForInternal') : []),
        [data]
    );

    if (!anchorEl) return null;

    let body: React.ReactNode;

    if (data && !isInitialLoading) {
        body = (
            <>
                {isFetching ? (
                    <LinearProgress
                        style={{
                            height: 5,
                            opacity: 0.5,
                        }}
                    />
                ) : (
                    <ColorStripe bgColor={data.serviceAdvisor.color || '#fff'} />
                )}

                <Header>
                    {data.appointmentSource === 'AppointmentSite' && data.hasConflict !== null ? (
                        <Box
                            component="div"
                            display="flex"
                            alignItems="center"
                            sx={{ padding: '15px 0px 0px 15px' }}
                        >
                            <Tooltip
                                content={t('appointments.theServiceAdvisorAlreadyHasAnAppointment')}
                                position="top"
                            >
                                <Box component="div">
                                    <WebIcon size={22} fill="#F15857" />
                                </Box>
                            </Tooltip>
                            <Typography
                                component="span"
                                sx={(theme) => ({
                                    ...theme.typography.h5Roboto,
                                    color: theme.palette.neutral[7],
                                    textTransform: 'uppercase',
                                    marginLeft: '3px',
                                })}
                            >
                                {getStatusText(data.status, t)}
                            </Typography>
                        </Box>
                    ) : (
                        <Status>{getStatusText(data.status, t)}</Status>
                    )}

                    <HeaderAction>
                        {data.containsAttachments && (
                            <ArrowTooltip content={t('commonLabels.attachedFiles')}>
                                <IconButton
                                    style={{ width: 11, height: 14 }}
                                    onClick={() => setAttachedFileModal(true)}
                                    size="large"
                                >
                                    <FolderOpenIcon fill={theme.palette.neutral[5]} />
                                </IconButton>
                            </ArrowTooltip>
                        )}
                        {data.status !== 'OrderCreated' && (
                            <>
                                <ArrowTooltip content={t('appointments.delete')}>
                                    <IconButton
                                        style={{ width: 11, height: 14 }}
                                        onClick={() => setShowDeleteModal(true)}
                                        disabled={
                                            (synchronizeAppointmentsEnabled &&
                                                data.appointmentSource !== 'AppointmentSite') ||
                                            !userPermission.allowEditAppointments
                                        }
                                        size="large"
                                    >
                                        <DeleteIcon fill={theme.palette.neutral[5]} />
                                    </IconButton>
                                </ArrowTooltip>
                                <ArrowTooltip content={t('appointments.editAppointment')}>
                                    <IconButton
                                        style={{ width: 11, height: 14 }}
                                        onClick={() =>
                                            navigate(`${ROUTES.APPOINTMENTS.BASE}/${data.number}`)
                                        }
                                        disabled={
                                            (synchronizeAppointmentsEnabled &&
                                                data.appointmentSource !== 'AppointmentSite') ||
                                            !userPermission.allowEditAppointments
                                        }
                                        size="large"
                                    >
                                        <EditIcon fill={theme.palette.neutral[5]} />
                                    </IconButton>
                                </ArrowTooltip>{' '}
                            </>
                        )}
                        <IconButton
                            style={{ width: 11, height: 14 }}
                            onClick={() => onClose()}
                            size="large"
                        >
                            <CloseIcon fill={theme.palette.neutral[5]} />
                        </IconButton>
                    </HeaderAction>
                </Header>
                {data.appointmentSource === 'AppointmentSite' && (
                    <Grid container sx={{ padding: '0px 16px', marginTop: '-5px' }}>
                        <Grid item xs={7}>
                            <Parameter
                                label={t(
                                    'appointments.appointmentScheduledFromSiteForAppointments'
                                )}
                                value=""
                            />
                        </Grid>
                    </Grid>
                )}

                <Summary>
                    {id && (
                        <OmniChannelParameter
                            appointmentId={id}
                            onClose={onClose}
                            onDeleted={onDeleted}
                            isThirdPartyUpdated={data.isThirdPartyUpdated ?? false}
                            isThirdPartyCreated={data.isThirdPartyCreated ?? false}
                        />
                    )}
                    <Grid container>
                        <Grid item xs={6}>
                            <Parameter
                                label={t('appointments.appointmentNumber')}
                                value={data.number}
                            />
                        </Grid>

                        <Grid item xs={6}>
                            <Parameter
                                label={t('appointments.orderNumber')}
                                value={data.order?.number || '--'}
                            />
                        </Grid>
                    </Grid>
                    <Parameter
                        icon={
                            <RoundedIcon style={{ color: 'var(--neutral5)' }}>
                                <CustomerIcon fill="currentColor" size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.customerName')}
                        value={`${data.customer.firstName ?? ''} ${
                            data.customer.lastName ?? ''
                        }`.trim()}
                    />
                    <Parameter
                        label={t('appointments.e-mail')}
                        value={data.customer.email || '--'}
                    />
                    <Parameter
                        label={t('appointments.mobile')}
                        value={data.customer.mobile || '--'}
                    />
                    <Divider />
                    {appointmentSettings?.valetServiceEnabled && data.withValetService && (
                        <>
                            <Parameter
                                icon={
                                    <RoundedIcon>
                                        <ValetServiceIcon
                                            fill={theme.palette.neutral[1]}
                                            style={{ marginLeft: 4, marginTop: 3.5 }}
                                            size={24}
                                        />
                                    </RoundedIcon>
                                }
                                label={''}
                                value={t('appointments.valetService')}
                            />
                            <Divider />
                        </>
                    )}
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <CarIcon size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.vehicle')}
                        value={[
                            data.vehicle?.plates ?? '--',
                            data.vehicle?.vin,
                            data.vehicle?.brand,
                            data.vehicle?.model,
                            data.vehicle?.year,
                            data.vehicle?.color,
                        ]
                            .filter(Boolean)
                            .join(', ')}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <ClockIcon size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.duration')}
                        value={`${Duration.fromISOTime(data.startsAt.time).toFormat(
                            'hh:mm'
                        )} - ${Duration.fromISOTime(data.startsAt.time)
                            .plus({ minutes: data.durationInMinutes })
                            .toFormat('hh:mm')}`}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <CustomerIcon fill={theme.palette.neutral[5]} size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.serviceAdvisor')}
                        value={data.serviceAdvisor.name}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <TurnedInIcon sx={{ height: 18, width: 18, padding: '2px' }} />
                            </RoundedIcon>
                        }
                        label={t('appointments.reasonForTheAppointment')}
                        value={
                            <ReasonsUl>
                                {isCustomApptReasonEnabled ? (
                                    <>
                                        {data.declinedItemReasonsForView &&
                                            data.declinedItemReasonsForView.map((r) => (
                                                <li key={r.id}>{r.name}</li>
                                            ))}
                                        {data.customReasons.map((r) => (
                                            <li key={r.id}>
                                                {r.name}
                                                {r.details ? ` - ${r.details.name}` : ''}
                                            </li>
                                        ))}
                                    </>
                                ) : (
                                    <>
                                        {data.reasons.map((r) => (
                                            <li key={r.id}>{r.name}</li>
                                        ))}
                                    </>
                                )}
                            </ReasonsUl>
                        }
                    />
                    <Parameter
                        label={t('appointments.notesVisibleForCustomer')}
                        value={
                            customerNotes && customerNotes.length ? (
                                <NotesList>
                                    {customerNotes.map((note, index) => (
                                        <div key={index}>{note.note}</div>
                                    ))}
                                </NotesList>
                            ) : (
                                '--'
                            )
                        }
                    />
                    <Parameter
                        label={t('appointments.internalNotes')}
                        value={
                            internalNotes && internalNotes.length ? (
                                <NotesList>
                                    {internalNotes.map((note, index) => (
                                        <div key={index}>{note.note}</div>
                                    ))}
                                </NotesList>
                            ) : (
                                '--'
                            )
                        }
                    />
                </Summary>

                <Actions>
                    {[
                        'Confirmed',
                        'Unconfirmed',
                        'CustomerArrived',
                        'CustomerDidNotArrive',
                    ].includes(data.status) ? (
                        <Button
                            onClick={() => handleOrderConvertation(undefined)}
                            showLoader={convertToOrder.isLoading}
                            label={t('appointments.convertToOrder')}
                            cmosVariant={'filled'}
                            disabled={
                                !userPermission.allowEditAppointments ||
                                !userPermission.allowEditOrders
                            }
                        />
                    ) : null}
                    {['Confirmed', 'Unconfirmed', 'CustomerArrived'].includes(data.status) ? (
                        <Button
                            onClick={() => markAsDidNotArrive.mutate()}
                            showLoader={markAsDidNotArrive.isLoading}
                            label={t('appointments.customerDidNotArrive')}
                            cmosVariant={'stroke'}
                            disabled={!userPermission.allowEditAppointments}
                        />
                    ) : null}
                </Actions>
            </>
        );
    } else {
        body = (
            <Box display="flex" justifyContent="center" padding={10}>
                <CircularProgress size={25} />
            </Box>
        );
    }

    return (
        <>
            <Popover
                disableScrollLock
                elevation={3}
                classes={{ paper: clsx(AppointmentPopupRoot) }}
                anchorOrigin={{ horizontal: 'center', vertical: 'center' }}
                transformOrigin={{ horizontal: 'center', vertical: 'top' }}
                onClose={onClose}
                anchorEl={anchorEl}
                open={enabled && !!anchorEl}
                style={{ zIndex: 7 }}
            >
                {body}
            </Popover>
            <NewOrderDataPopup
                open={showOrderInfoPopup}
                onClose={() => setShowOrderInfoPopup(false)}
                onOrderData={handleOrderData}
                loading={convertToOrder.isLoading}
            />
            <DeleteAppointmentModal
                open={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onDeleted={() => {
                    setShowDeleteModal(false);
                    onDeleted();
                }}
                appointmentData={
                    data
                        ? {
                              id: data.id,
                              customer:
                                  `${data.customer.firstName} ${data.customer.lastName}`.trim(),
                              vehicle: getVehicleFormatted(data),
                              date: data.startsAt,
                          }
                        : undefined
                }
            />
            <AttachedFilesModal
                id={id!}
                open={showAttachedFileModal}
                onClose={() => setAttachedFileModal(false)}
                fetchAttachments={() => AppointmentsApi.getAppointmentAttachmentsInfo(id!)}
            />
        </>
    );
}

const RoundedIcon = styled('div')({
    height: 18,
    width: 18,
    color: 'var(--neutral1)',
    backgroundColor: 'var(--neutral5)',
    borderRadius: 10,
});

const Header = styled('header')({
    position: 'relative',
    height: 40,
    display: 'flex',
    justifyContent: 'space-between',
});

const HeaderAction = styled('div')({
    marginTop: 12,
    marginRight: 10,
    justifyContent: 'flex-end',
    display: 'flex',
});

const Status = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[7],
    display: 'inline-block',
    padding: '17px 0 0 40px',
    textTransform: 'uppercase',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
}));

const ReasonsUl = styled('ul')({
    paddingLeft: 0,
    margin: '5px 15px',
});

const NotesList = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: 5,
});

const AppointmentPopupRoot = styled('div')(({ theme }) => ({
    backgroundColor: '#fff',
    overflow: 'hidden',
    position: 'relative',
    width: 310,
    padding: 0,
    borderRadius: 8,
    border: `1px solid ${theme.palette.neutral[3]}`,
}));

const Actions = styled('footer')({
    display: 'flex',
    flexDirection: 'column',
    gap: 6,
    alignItems: 'stretch',
    margin: 8,
});

const Summary = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 12,
    padding: '10px 16px',
    maxWidth: 308,
    maxHeight: 430,
    overflow: 'auto',
    scrollBehavior: 'smooth',
    ...scrollbarStyle(),
});

const ColorStripe = styled('div')<{ bgColor?: string }>(({ bgColor }) => ({
    height: 5,
    backgroundColor: bgColor || '#fff',
}));

export function AppointmentPopupProvider({ children }: React.PropsWithChildren<{}>) {
    const [id, setId] = useQueryParam('ap.id');
    const idRef = useRef(id);
    idRef.current = id;
    const anchors = useRef<Record<string, HTMLElement | null>>({});
    const anchorEl = anchors.current[id ?? ''] ?? null;
    const forceRender = useForceRender();
    const navigate = useNavigate();
    const onClose = () => {
        setId(null, true);
    };

    const ctx: IAppointmentPopupContext = useMemo(
        () => ({
            open(id) {
                setId(id, true);
            },
            setAnchor(id_, element) {
                anchors.current[id_] = element;
                if (id_ === idRef.current) {
                    forceRender();
                }
            },
        }),
        [setId, forceRender]
    );
    const { refetchWeek } = useAppointmentsQueryController();

    return (
        <>
            <AppointmentPopupContext.Provider value={ctx}>
                <AppointmentPopup
                    autoSwitchDate
                    anchorEl={anchorEl}
                    onClose={onClose}
                    id={id ?? undefined}
                    onOrderCreated={(orderId) =>
                        navigate(generatePath(ROUTES.ORDERS_DETAIL, { id: orderId }))
                    }
                    onDeleted={() => {
                        refetchWeek();
                    }}
                    onCustomerDidNotArrive={onClose}
                />
                {children}
            </AppointmentPopupContext.Provider>
        </>
    );
}
