import { createContext, useContext } from 'react';

export interface IAppointmentPopupContext {
    open(id: string): void;
    setAnchor(id: string, element: HTMLElement | null): void;
}

export const AppointmentPopupContext = createContext<IAppointmentPopupContext | null>(null);

export function useAppointmentPopupContext() {
    const ctx = useContext(AppointmentPopupContext);
    if (!ctx) throw new Error('AppointmentPopupContext is not available');
    return ctx;
}
