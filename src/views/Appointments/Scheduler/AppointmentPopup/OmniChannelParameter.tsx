import { IconButton, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentsApi, { OmnichannelSyncType } from 'api/appointments';
import { SyncUpInfoIcon } from 'common/components/Icons/SyncUpInfoIcon';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import Tooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { FeatureFlags } from 'datacontracts/GlobalSettings/RepairShopSettingsDto';
import { useMemo, useState } from 'react';
import { isErrorResponse } from 'services/Server';
import { useAppSelector } from 'store';
import { selectSettings, selectSyncAppointmentsThirdParty } from 'store/slices/globalSettingsSlice';
import Parameter from './Parameter';

type OmniChannelParameterProps = {
    isThirdPartyCreated: boolean;
    isThirdPartyUpdated: boolean;
    appointmentId: string;
    onClose: () => void;
    onDeleted: () => void;
};

const TextContent = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    padding: '0px 10px',
}));

const OMNICHANNEL_CREATE = 'omnichannelCreate';

export default function OmniChannelParameter({
    isThirdPartyCreated,
    isThirdPartyUpdated,
    appointmentId,
    onClose,
    onDeleted,
}: OmniChannelParameterProps) {
    const { t } = useAppTranslation();
    const [isWarningModalOpen, setIsWarningModalOpen] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>('');
    const toasters = useToasters();

    const { isOmnichannelAppointmentsEnabled } = useAppSelector(selectSettings).repairShopSettings
        ?.features as FeatureFlags;

    const isSyncAppointmentsThirdPartyEnabled = useAppSelector(selectSyncAppointmentsThirdParty);

    const omnichannelStatus = useMemo(() => {
        if (!isThirdPartyCreated) {
            return t('appointments.omnichannelModal.appointmentNotCreatedInYourIntegratedSoftware');
        }

        return t('appointments.omnichannelModal.appointmentNotUpdatedInYourIntegratedSoftware');
    }, [t, isThirdPartyCreated]);

    const omnichannelSynced = useMemo(() => {
        if (!isThirdPartyCreated) {
            return t('appointments.appointmentCreatedInYourIntegratedSoftware');
        }

        return t('appointments.appointmentUpdatedInYourIntegratedSoftware');
    }, [isThirdPartyCreated, t]);

    const syncOmnichannel = useMutation(
        async () => {
            let syncType: OmnichannelSyncType = 'UpdateAppointment';

            if (!isThirdPartyCreated) {
                syncType = 'CreateAppointment';
            }
            await AppointmentsApi.syncOmnichannel(
                appointmentId,
                syncType,
                isSyncAppointmentsThirdPartyEnabled
            );
        },
        {
            onSuccess: () => {
                toasters.success(omnichannelSynced);
                onClose();
                onDeleted();
            },
            onError: (error) => {
                if (isErrorResponse(error)) {
                    setIsWarningModalOpen(true);
                    setErrorMessage(error.message);
                    toasters.danger(omnichannelStatus);
                }
            },
            onSettled: () => {
                toasters.dismiss(OMNICHANNEL_CREATE);
            },
        }
    );
    const showSyncInfoValidation =
        (isOmnichannelAppointmentsEnabled || isSyncAppointmentsThirdPartyEnabled) &&
        (!isSyncAppointmentsThirdPartyEnabled
            ? !isThirdPartyCreated || !isThirdPartyUpdated
            : !isThirdPartyCreated);

    if (!showSyncInfoValidation) return null;

    return (
        <>
            <Parameter
                icon={
                    <Tooltip
                        content={t(
                            'appointments.omnichannelModal.synchronizeWithYourIntegratedSoftware'
                        )}
                        position="top"
                    >
                        <IconButton
                            size="small"
                            onClick={() => {
                                syncOmnichannel.mutate();
                                toasters.progress(
                                    t(
                                        'appointments.omnichannelModal.weAreUpdatingTheAppointmentInYourIntegratedSoftware'
                                    ),
                                    t('appointments.omnichannelModal.oneMomentPlease'),
                                    { id: OMNICHANNEL_CREATE }
                                );
                            }}
                        >
                            <SyncUpInfoIcon fill={Colors.Neutral7} size={18} />
                        </IconButton>
                    </Tooltip>
                }
                label={omnichannelStatus}
                value=""
            />
            <WarningConfirmationPopup
                open={isWarningModalOpen}
                title={omnichannelStatus}
                body={
                    <TextContent>
                        {t('appointments.omnichannelModal.reasonForErrorInYourIntegratedSoftware')}:
                        "{errorMessage}"
                    </TextContent>
                }
                cancel={t('appointments.omnichannelModal.cancel')}
                confirm={t('appointments.omnichannelModal.retry')}
                onConfirm={() => {
                    setIsWarningModalOpen(false);
                    syncOmnichannel.mutate();
                    toasters.progress(
                        t(
                            'appointments.omnichannelModal.weAreUpdatingTheAppointmentInYourIntegratedSoftware'
                        ),
                        t('appointments.omnichannelModal.oneMomentPlease'),
                        { id: OMNICHANNEL_CREATE }
                    );
                }}
                onClose={() => {
                    setIsWarningModalOpen(false);
                }}
            />
        </>
    );
}
