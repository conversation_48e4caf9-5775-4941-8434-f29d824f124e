import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { SyncUpInfoIcon } from 'common/components/Icons/SyncUpInfoIcon';
import { ValetServiceIcon } from 'common/components/Icons/ValetServiceIcon';
import { WebIcon } from 'common/components/Icons/WebIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { FeatureFlags } from 'datacontracts/GlobalSettings/RepairShopSettingsDto';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import moment from 'moment';
import { forwardRef } from 'react';
import { useAppSelector } from 'store';
import {
    selectSettings,
    selectSyncAppointmentsThirdParty,
} from 'store/slices/globalSettingsSlice/selectors';
import theme from 'theme';
import { Appointment, getStatusText } from '../../_common';

export type AppointmentCardProps = {
    appointment: Appointment;
    valetServiceEnabled: boolean;
    enableTooltip?: boolean;
    onClick: () => void;
};

const AppointmentCard = forwardRef(
    (
        { appointment, onClick, valetServiceEnabled }: AppointmentCardProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const { t } = useAppTranslation();
        const { isOmnichannelAppointmentsEnabled } = useAppSelector(selectSettings)
            .repairShopSettings?.features as FeatureFlags;

        const isSyncAppointmentsThirdPartyEnabled = useAppSelector(
            selectSyncAppointmentsThirdParty
        );

        const dateFormat = (date: Date): string => {
            return moment(date).format('HH:mm');
        };

        // const restProps = { ...otherProps, style, data, children };
        const isNotArrived = appointment.status === AppointmentStatusEnum.CustomerDidNotArrive;

        const showSyncInfoValidation =
            (isOmnichannelAppointmentsEnabled || isSyncAppointmentsThirdPartyEnabled) &&
            (!isSyncAppointmentsThirdPartyEnabled
                ? !appointment.isThirdPartyCreated || !appointment.isThirdPartyUpdated
                : !appointment.isThirdPartyCreated);

        return (
            <CardRoot component="div" ref={ref} id={appointment.id} onClick={onClick}>
                <Header style={{ background: appointment?.userServiceAdvisorColor ?? '#fff' }} />
                <Box
                    component="div"
                    // className={`${classes.content} ${isNotArrived ? classes.contentNotArrive : ''}`}
                    sx={
                        appointment.hasConflict
                            ? {
                                  height: '100%',
                                  padding: '5px 8px 5px 8px',
                                  backgroundColor: '#FCDEDD',
                                  animation: 'blinker 500ms linear alternate infinite',
                                  '@keyframes blinker': {
                                      '50%': { backgroundColor: 'transparent' },
                                  },
                              }
                            : {
                                  height: '100%',
                                  padding: '5px 8px 5px 8px',
                                  background: !isNotArrived ? '#FFFFFF' : '#EFEFEF',
                              }
                    }
                >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        {valetServiceEnabled && appointment.withValetService && (
                            <ValetServiceIcon
                                fill={theme.palette.primary.main}
                                size={10}
                                style={{ marginRight: 3 }}
                            />
                        )}
                        {showSyncInfoValidation && (
                            <SyncUpInfoIcon fill={Colors.Neutral7} size={15} />
                        )}
                        {isNotArrived ? (
                            <CustomerNotArrived>
                                {`${appointment.customerFirstName} ${
                                    appointment.customerLastName ?? ''
                                }`}
                            </CustomerNotArrived>
                        ) : (
                            <CustomerTitle>
                                {`${appointment.customerFirstName} ${
                                    appointment.customerLastName ?? ''
                                }`}
                            </CustomerTitle>
                        )}

                        <PlatesTitle>{appointment.vehiclePlates || '--'}</PlatesTitle>
                    </div>
                    <Box component="div" sx={{ display: 'flex', alignItems: 'center' }}>
                        <HoursText>
                            {dateFormat(new Date(appointment.startDate)) +
                                ' - ' +
                                dateFormat(new Date(appointment.endDate))}
                        </HoursText>
                        {appointment.isFromAppointmentSite && (
                            <Box
                                component="div"
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'right',
                                    width: '50%',
                                    marginTop: '-5px',
                                }}
                            >
                                <WebIcon
                                    size={22}
                                    fill={appointment.hasConflict != null ? '#F15857' : '#0069FF'}
                                />
                            </Box>
                        )}
                    </Box>
                    <div style={{ display: 'flex' }}>
                        <PersonChargeText style={{ width: '50%' }}>
                            {appointment.userServiceDisplayName}
                        </PersonChargeText>
                        <PersonChargeText
                            style={{ display: 'flex', justifyContent: 'end', width: '50%' }}
                        >
                            {getStatusText(appointment.status, t)}
                        </PersonChargeText>
                    </div>
                </Box>
            </CardRoot>
        );
    }
);

const CardRoot = styled(Box)({
    border: '1px solid #C9CDD3',
    boxSizing: 'border-box',
    borderRadius: 4,
    position: 'relative',
    zIndex: 2,
    cursor: 'pointer',
    overflow: 'hidden',
    transition: '.25s',
    height: '100%',
    '&:hover': {
        border: '1px solid var(--cm2)',
    },
});

const Header = styled('div')({
    width: '100%',
    height: 4,
});

const CustomerTitle = styled(Typography)({
    width: '70%',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    fontFamily: 'Inter',
    fontStyle: 'normal',
    fontWeight: 'bold',
    fontSize: 12,
    lineHeight: '15px',
    color: '#6A6E72',
});

const CustomerNotArrived = styled(CustomerTitle)({
    textDecoration: 'line-through',
});

const PlatesTitle = styled(Typography)({
    fontFamily: 'Inter',
    fontWeight: 'bold',
    fontSize: 7,
    lineHeight: '8px',
    color: '#6A6E72',
    display: 'flex',
    justifyContent: 'end',
    width: '30%',
});

const HoursText = styled(Typography)({
    fontFamily: 'Inter',
    fontSize: 9,
    lineHeight: '11px',
    color: '#6A6E72',
    whiteSpace: 'nowrap',
    width: '50%',
});

const PersonChargeText = styled(Typography)({
    fontFamily: 'Inter',
    fontSize: 7,
    lineHeight: '8px',
    color: '#6A6E72',
    marginTop: 1,
});

export default AppointmentCard;
