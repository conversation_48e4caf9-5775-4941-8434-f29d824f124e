import { styled } from '@mui/styles';
import { AbsenceDto } from 'api/users';
import { useAppointmentSchedulerContext } from 'common/components/AppointmentsScheduler';
import { ItemComponentProps } from 'common/components/AppointmentsScheduler/Content';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { NotAllowedIcon } from 'common/components/Icons/NotAllowedIcon';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { CSSProperties, Fragment, useRef, useState } from 'react';
import { useAppSelector } from 'store';
import { selectFilters } from 'store/slices/appointments/selectors';
import AbsenceBlock from 'views/Components/AbsenceBlock';
import { useEditAbsencePopup } from 'views/absence/EditAbsence/context';
import { useScheduleAbsencePopup } from 'views/absence/ScheduleAbsence/context';
import { AbsenceGroup } from '../_common';
import { useNow } from '../_hooks';

function getWidth(index: number, totalCount: number): number {
    return 100 - ((index / totalCount) * 100 * 2) / 3;
}

export default function AbsenceItem({ value }: ItemComponentProps<AbsenceGroup>) {
    const { absences, start, end } = value;
    const firstAbsence = absences[0];
    const { t } = useAppTranslation();
    const now = useNow();
    const scheduleAbsencePopup = useScheduleAbsencePopup();
    const editAbsencePopup = useEditAbsencePopup();
    const mode = useAppSelector((r) => selectFilters(r).mode);
    const { pixelsPerMinute } = useAppointmentSchedulerContext();

    const [menuContext, setMenuContext] = useState<{
        x: number;
        y: number;
        absence: AbsenceDto;
    } | null>(null);

    const rootRef = useRef<HTMLTableCellElement | null>(null);

    // all absences in the group will belong to the same service advisor in "day" mode
    const advisorId = mode === 'day' ? firstAbsence.originalAbsence.userId : undefined;
    const dateFormmated = start.toFormat('yyyyMMddHHmm');
    const isInPast = end < DateTime.fromJSDate(now);

    const handleOnClick = (event: React.MouseEvent, absence: AbsenceDto) => {
        setMenuContext({ x: event.clientX, y: event.clientY, absence });
    };

    const editAbsence = () => {
        if (!menuContext) throw new Error("Edit btn can't be clicked when no absence selected");

        editAbsencePopup.open(menuContext.absence, rootRef.current!);
        setMenuContext(null);
    };

    const createAppointment = () => {
        window.location.href =
            '/dashboard' +
            `${ROUTES.APPOINTMENTS.BASE}${ROUTES.APPOINTMENTS.SUBROUTES.NEW}?date=${dateFormmated}`;
    };

    const addAbsence = () => {
        scheduleAbsencePopup.open(start.toMillis(), advisorId || '', rootRef.current!);
        setMenuContext(null);
    };

    return (
        <RootContainer
            ref={rootRef}
            style={{ '--total-count': value.absences.length } as CSSProperties}
        >
            {value.absences.map((s, idx) => (
                <Fragment key={s.originalAbsence.id}>
                    <AbsenceItemWrapper
                        style={
                            {
                                '--index': idx,
                                '--absence-offset': s.start.diff(value.start).as('seconds') / 60,
                                '--absence-duration': s.duration,
                                width: `${getWidth(idx, value.absences.length)}%`,
                            } as CSSProperties
                        }
                    >
                        <AbsenceBlock
                            vertical
                            pixelPerMinute={pixelsPerMinute}
                            absence={s.originalAbsence}
                            topOffset={3}
                            overrideDuration={s.duration}
                            onClick={(e) => handleOnClick(e, s.originalAbsence)}
                        />
                    </AbsenceItemWrapper>
                    <SMenu
                        borders
                        anchorReference="anchorPosition"
                        anchorPosition={{ top: menuContext?.y ?? 0, left: menuContext?.x ?? 0 }}
                        anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'left',
                        }}
                        transformOrigin={{ horizontal: 'left', vertical: 'center' }}
                        id="absense-menu"
                        onClose={() => {
                            setMenuContext(null);
                        }}
                        transitionDuration={150}
                        open={!!menuContext}
                    >
                        <SMenuItem2 onClick={editAbsence}>
                            <CalendarIcon size={24} fill="currentColor" />
                            {t('appointments.editAbsence')}
                        </SMenuItem2>
                        {!isInPast && (
                            <>
                                <SMenuItem2 onClick={createAppointment}>
                                    <CalendarIcon size={24} fill="currentColor" />
                                    {t('appointments.scheduleNewAppointment')}
                                </SMenuItem2>
                                <SMenuItem2 onClick={addAbsence}>
                                    <NotAllowedIcon size={24} fill="currentColor" />
                                    {t('appointments.scheduleAbsence')}
                                </SMenuItem2>
                            </>
                        )}
                    </SMenu>
                </Fragment>
            ))}
        </RootContainer>
    );
}

const RootContainer = styled('div')({
    height: '100%',
    position: 'relative',
});

const AbsenceItemWrapper = styled('div')({
    boxSizing: 'border-box',
    position: 'absolute',
    left: 'calc(100% / var(--total-count) * var(--index) * 2 / 3)',
    top: 'calc(var(--cmos-aps-pixels-per-minute) * var(--absence-offset))',
    height: 'calc(var(--cmos-aps-pixels-per-minute) * var(--absence-duration))',
});
