import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import { useMutation } from '@tanstack/react-query';
import AppointmentsApi from 'api/appointments';
import { ItemComponent } from 'common/components/AppointmentsScheduler/Content';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { NotAllowedIcon } from 'common/components/Icons/NotAllowedIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectFilters } from 'store/slices/appointments/selectors';
import {
    useAppointmentsQueryController,
    useRepairShopAppointmentSettings,
} from 'views/Appointments/common';
import { useScheduleAbsencePopup } from 'views/absence/ScheduleAbsence/context';
import { useAppointmentPopupContext } from '../AppointmentPopup/context';
import { Appointment, AppointmentsGroup } from '../_common';
import { useNow } from '../_hooks';
import AppointmentCard from './AppointmentCard';

const selectMode = createSelector(selectFilters, (s) => s.mode);

const AppointmentItem: ItemComponent<AppointmentsGroup> = memo(({ value: appointmentGroup }) => {
    const mode = useAppSelector(selectMode);
    const appointments = appointmentGroup.list;
    const firstAppointment = appointments[0];

    // all appointments in the group will belong to the same service advisor in "day" mode
    const advisorId = mode === 'day' ? firstAppointment.userServiceId : undefined;
    const now = useNow();
    const date = moment(firstAppointment.startDate);
    const dateFormmated = date.format('YYYYMMDDHHmm');
    const isInPast = date.isBefore(now, 'm');

    const settings = useRepairShopAppointmentSettings();
    const valetServiceEnabled = settings?.valetServiceEnabled ?? false;

    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const { t } = useAppTranslation();

    const scheduleAbsencePopup = useScheduleAbsencePopup();
    const addAbsence = () => {
        scheduleAbsencePopup.open(date.valueOf(), advisorId || '', rootRef.current!);
        setAnchorEl(null);
    };
    const rootRef = useRef<HTMLTableCellElement | null>(null);

    let body: React.ReactNode;

    if (appointments.length === 1) {
        body = (
            <AppointmentSubItem
                appointment={firstAppointment}
                valetServiceEnabled={valetServiceEnabled}
            />
        );
    } else {
        body = (
            <Root>
                {appointments.map((a, idx) => (
                    <SubItem
                        key={a.id}
                        style={{ left: `${(50 / (appointments.length - 1)) * idx}%` }}
                    >
                        {/* TODO: This is temporally */}
                        <AppointmentSubItem
                            appointment={a}
                            hideWebIcon={appointments.length > 2}
                            valetServiceEnabled={valetServiceEnabled}
                        />
                    </SubItem>
                ))}
            </Root>
        );
    }

    return (
        <Wrapper>
            {body}

            {!isInPast && (
                <>
                    <PlusButton
                        className="plsBtn"
                        ref={rootRef}
                        onClick={(e) => setAnchorEl(e.currentTarget)}
                    >
                        <PlusIcon size={24} fill="currentColor" />
                    </PlusButton>
                    <SMenu
                        borders
                        anchorOrigin={{ horizontal: 'left', vertical: 'center' }}
                        transformOrigin={{ horizontal: 'left', vertical: 'center' }}
                        id="appointmet-plus-btn-menu"
                        anchorEl={anchorEl}
                        onClose={() => {
                            setAnchorEl(null);
                        }}
                        transitionDuration={150}
                        open={!!anchorEl}
                    >
                        <SMenuItem2>
                            <CalendarIcon size={24} fill="currentColor" />
                            {t('appointments.scheduleNewAppointment')}

                            <NavLink
                                style={{ width: 100, height: '40px', position: 'absolute' }}
                                draggable="false"
                                to={
                                    `${ROUTES.APPOINTMENTS.BASE}${ROUTES.APPOINTMENTS.SUBROUTES.NEW}?date=${dateFormmated}` +
                                    (advisorId ? `&advisor=${advisorId}` : '')
                                }
                            />
                        </SMenuItem2>
                        <SMenuItem2 onClick={addAbsence}>
                            <NotAllowedIcon size={24} fill="currentColor" />
                            {t('appointments.scheduleAbsence')}
                        </SMenuItem2>
                    </SMenu>
                </>
            )}
        </Wrapper>
    );
});

type AppointmentSubItemProps = {
    appointment: Appointment;
    valetServiceEnabled: boolean;
    hideWebIcon?: boolean;
};

function AppointmentSubItem({
    appointment,
    hideWebIcon,
    valetServiceEnabled,
}: AppointmentSubItemProps) {
    const navigate = useNavigate();
    const { t } = useAppTranslation();
    const appointmentPopup = useAppointmentPopupContext();
    const { refetchWeek } = useAppointmentsQueryController();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [resolveConflict, setResolveConflict] = useState(false);

    const conflictModalOpen = useMemo(
        () =>
            Boolean(appointment.isFromAppointmentSite) &&
            Boolean(appointment.hasConflict) &&
            resolveConflict,
        [appointment, resolveConflict]
    );

    useEffect(() => {
        appointmentPopup.setAnchor(appointment.id, anchorEl);
        return () => appointmentPopup.setAnchor(appointment.id, null);
    }, [appointmentPopup, anchorEl, appointment.id]);

    const handleClick = () => {
        appointmentPopup.open(appointment.id);
        setResolveConflict(true);
    };

    const resolveConflictMutation = useMutation(
        async () => {
            await AppointmentsApi.resolveConflict(appointment.id);
        },
        {
            onSuccess: () => {
                refetchWeek();
            },
            onSettled: () => {
                setResolveConflict(false);
            },
        }
    );

    return (
        <>
            <AppointmentCard
                ref={setAnchorEl}
                appointment={{
                    ...appointment,
                    isFromAppointmentSite: appointment.isFromAppointmentSite && !hideWebIcon,
                }}
                onClick={handleClick}
                valetServiceEnabled={valetServiceEnabled}
            />
            <WarningConfirmationPopup
                open={conflictModalOpen}
                title={t('appointments.conflictModal.title')}
                cancel={t('appointments.conflictModal.keepAppointment')}
                confirm={t('appointments.conflictModal.modifySchedule')}
                body={
                    <Box
                        component="div"
                        sx={{
                            padding: '0px 44px',
                            color: '#6A6E72',
                            textAlign: 'center',
                            fontFamily: 'Roboto',
                            fontSize: '12px',
                            fontStyle: 'normal',
                            fontWeight: '400',
                            lineHeight: '20px',
                        }}
                    >{`${t('appointments.conflictModal.theServiceAdvisor')} ${
                        appointment.userServiceDisplayName
                    } ${t('appointments.conflictModal.alreadyHasAnAppointmentAtSameTime')}`}</Box>
                }
                onConfirm={() => {
                    navigate(`${ROUTES.APPOINTMENTS.BASE}/${appointment.id}`);
                    setResolveConflict(false);
                }}
                onCancel={() => {
                    resolveConflictMutation.mutate();
                }}
                onClose={() => {
                    setResolveConflict(false);
                }}
            />
        </>
    );
}

const Root = styled('ul')({
    position: 'relative',
    height: '100%',
    listStyle: 'none',
    margin: 0,
});

const SubItem = styled('li')({
    position: 'absolute',
    left: 0,
    width: '50%',
    height: '100%',
});

const Wrapper = styled('div')({
    position: 'relative',
    height: '100%',

    '&:hover .plsBtn': {
        scale: '1',
        visibility: 'visible',
    },
});

const PlusButton = styled('div')({
    all: 'initial',
    backgroundColor: 'var(--cm1)',
    display: 'block',
    zIndex: 2,
    height: 24,
    width: 24,
    borderRadius: 12,
    position: 'absolute',
    right: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#fff',
    cursor: 'pointer',
    scale: '0',
    visibility: 'hidden',
    transition: 'scale .15s',
    transformOrigin: 'center',
    '&:hover': {
        backgroundColor: 'var(--cm2)',
    },
});

export default AppointmentItem;
