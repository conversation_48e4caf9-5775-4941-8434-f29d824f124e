import { AppointmentStatus } from 'api/appointments';
import { AbsenceDto } from 'api/users';
import { TimeSpan } from 'api/utils/format';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import { TFunction } from 'i18next';
import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import { DateTime } from 'luxon';
import moment from 'moment';
import React, { memo, useContext, useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { useRepairShopAppointmentSettings } from '../common';

export type Appointment = {
    number: string;
    orderNumber?: string;
    repairShopId: string;
    status: AppointmentStatusEnum;
    customerId?: string;
    customerFirstName?: string;
    customerLastName?: string;
    customerEmail?: string;
    customerMobile?: string;
    userServiceId: string;
    userServiceDisplayName: string;
    userServiceAdvisorColor: string;
    duration?: number;
    vehiclePlates?: string;
    vehicleVIN?: string;
    vehicleMake?: string;
    vehicleModel?: string;
    vehicleYear?: string;
    vehicleColor?: string;
    jobType: string;
    id: string;
    startDate: string;
    endDate: string;
    isThirdPartyCreated?: boolean;
    isThirdPartyUpdated?: boolean;
    isThirdPartyDeleted?: boolean;
    isFromAppointmentSite?: boolean;
    hasConflict: boolean | null;
    withValetService: boolean;
};

export type AppointmentsGroup = {
    list: Appointment[];
    groupKey: string;
    key: string;
};

module absenceGridItem {
    export type Slice = {
        originalAbsence: AbsenceDto;
        start: DateTime;
        end: DateTime;
        duration: number;
    };

    function getSlice(originalAbsence: AbsenceDto, day: DateTime): Slice | null {
        const startOfDay = day;
        const endOfDay = day.plus({ day: 1 });
        const startsAt = DateTime.fromISO(originalAbsence.startsAt);
        const endsAt = DateTime.fromISO(originalAbsence.endsAt);

        if (startsAt >= endOfDay || endsAt <= startOfDay) return null;

        const blockStart = startsAt > startOfDay ? startsAt : startOfDay;
        const blockEnd = endsAt > endOfDay ? endOfDay : endsAt;

        return {
            originalAbsence,
            start: blockStart,
            end: blockEnd,
            duration: blockEnd.diff(blockStart).as('minutes'),
        };
    }

    export function getGroupKey(group: Group): string {
        return `g/${group.start.toMillis()}-${group.end.toMillis()}/${
            group.parentGroupKey
        }/${group.absences.map((x) => x.originalAbsence.id).join('-')}`;
    }

    /**
     * group of absence slices that overlap with each other
     */
    export type Group = {
        absences: Slice[];
        start: DateTime;
        end: DateTime;
        parentGroupKey: string;
    };

    function intersectsWithGroup(a: Slice, g: Group): boolean {
        return a.start < g.end && a.end > g.start;
    }

    function tryAddToGroup(a: Slice, g: Group): boolean {
        if (intersectsWithGroup(a, g)) {
            g.absences.push(a);
            g.start = g.start < a.start ? g.start : a.start;
            g.end = g.end > a.end ? g.end : a.end;
            return true;
        }

        return false;
    }

    function createEmptyGroup(slice: Slice, parentGroupKey: string): Group {
        return {
            absences: [slice],
            start: slice.start,
            end: slice.end,
            parentGroupKey,
        };
    }

    /**
     *
     * @param absences list of absences
     * @param days list of days in the format yyyy-MM-dd
     * @returns mapping of day -> list of appointment group
     */
    function groupIntersectingAbsencesByDays(
        absences: AbsenceDto[],
        days: string[]
    ): Record<string, Group[]> {
        const dayGroups: Record<string, Group[]> = {};
        const absencesSorted = sortBy(absences, (x) => DateTime.fromISO(x.startsAt).toMillis());

        for (const dayStr of days) {
            const groups: Group[] = [];
            dayGroups[dayStr] = groups;
            const day = DateTime.fromFormat(dayStr, 'yyyy-MM-dd');

            for (const absence of absencesSorted) {
                const slice = getSlice(absence, day);
                if (slice === null) {
                    // slice is empty meaning this absence does not overlap with the current day
                    continue;
                }

                if (groups.length === 0) {
                    groups.push({
                        start: slice.start,
                        end: slice.end,
                        absences: [slice],
                        parentGroupKey: dayStr,
                    });
                } else {
                    const lastGroup = groups[groups.length - 1];
                    if (!tryAddToGroup(slice, lastGroup)) {
                        groups.push(createEmptyGroup(slice, dayStr));
                    }
                }
            }
        }

        return dayGroups;
    }

    function groupIntersectingAbsencesByUser(
        userIds: string[],
        absences: AbsenceDto[],
        dayFilter: string,
        zone: string = 'local'
    ) {
        const userGroups: Record<string, Group[]> = {};
        for (const userId of userIds) userGroups[userId] = [];
        const day = DateTime.fromFormat(dayFilter, 'yyyy-MM-dd', { zone });

        for (const absence of absences) {
            if (absence.userId && !userIds.includes(absence.userId)) continue;
            const slice = getSlice(absence, day);
            if (slice === null) continue;

            const applicableUserIds = absence.userId ? [absence.userId] : userIds;

            for (const userId of applicableUserIds) {
                const list = userGroups[userId];
                if (list.length === 0) {
                    list.push(createEmptyGroup(slice, userId));
                } else {
                    const lastGroup = list[list.length - 1];
                    if (!tryAddToGroup(slice, lastGroup)) {
                        list.push(createEmptyGroup(slice, userId));
                    }
                }
            }
        }

        return userGroups;
    }

    export function createAbsencesGridItems(
        absences: AbsenceDto[],
        days: string[]
    ): AppointmentGridItem[] {
        const dayGroups = groupIntersectingAbsencesByDays(absences, days);
        const items: AppointmentGridItem[] = [];

        for (const groupKey in dayGroups) {
            const groups = dayGroups[groupKey];

            for (const group of groups) {
                items.push({
                    groupKey,
                    absenceGroup: group,
                    duration: group.end.diff(group.start).as('minutes'),
                    startsAt: group.start.hour * 60 + group.start.minute,
                    key: absenceGridItem.getGroupKey(group),
                    type: 'absence',
                });
            }
        }

        return items;
    }

    export function createAbsencesGridItemsForDay(
        absences: AbsenceDto[],
        day: string,
        userIds: string[],
        zone: string = 'local'
    ): AppointmentGridItem[] {
        const groupsMapping = groupIntersectingAbsencesByUser(userIds, absences, day, zone);
        const items: AppointmentGridItem[] = [];

        for (const groupKey in groupsMapping) {
            const groups = groupsMapping[groupKey];
            for (const group of groups) {
                items.push({
                    groupKey,
                    absenceGroup: group,
                    duration: group.end.diff(group.start).as('minutes'),
                    startsAt: group.start.hour * 60 + group.start.minute,
                    key: absenceGridItem.getGroupKey(group),
                    type: 'absence',
                });
            }
        }

        return items;
    }
}

export type AbsenceSlice = absenceGridItem.Slice;
export type AbsenceGroup = absenceGridItem.Group;

export const createAbsencesGridItems = absenceGridItem.createAbsencesGridItems;
export const createAbsencesGridItemsForDay = absenceGridItem.createAbsencesGridItemsForDay;

export type AppointmentGridItem = {
    key: string;
    groupKey: string;
    startsAt: number;
    duration: number;
} & (
    | {
          type: 'appointments';
          group: AppointmentsGroup;
      }
    | {
          type: 'absence';
          absenceGroup: AbsenceGroup;
      }
);

export function createAppoitmentsGridItems(
    appointmentGroups: AppointmentsGroup[]
): AppointmentGridItem[] {
    return appointmentGroups.map((x) => {
        const startsAt = DateTime.fromISO(x.list[0].startDate);
        const endsAt = DateTime.fromISO(x.list[0].endDate);

        const item: AppointmentGridItem = {
            type: 'appointments',
            group: x,
            groupKey: x.groupKey,
            duration: endsAt.diff(startsAt).as('minutes'),
            startsAt: startsAt.hour * 60 + startsAt.minute,
            key: x.key,
        };

        return item;
    });
}

export function groupAppointmentsByUser(appointments: Appointment[]): AppointmentsGroup[] {
    function getKey(appointment: Appointment) {
        const userId = appointment.userServiceId;
        const dateTime = +moment(appointment.startDate).toDate() / 60000;
        const key = `${userId}_${dateTime}`;
        return key;
    }

    function getGroupKey(appointment: Appointment) {
        return appointment.userServiceId;
    }

    // group by start date rounding up to a minute
    return Object.values(groupBy(appointments, getKey)).map((list) => ({
        list,
        groupKey: getGroupKey(list[0]),
        key: getKey(list[0]),
    }));
}

export function groupAppointmentsByDate(appointments: Appointment[]): AppointmentsGroup[] {
    function getKey(appointment: Appointment) {
        const date = moment(appointment.startDate).format('YYYY-MM-DD');
        const dateTime = +moment(appointment.startDate).toDate() / 60000;
        const key = `${date}_${dateTime}`;
        return key;
    }

    function getGroupKey(appointment: Appointment) {
        return moment(appointment.startDate).format('YYYY-MM-DD');
    }

    // group by start date rounding up to a minute
    return Object.values(groupBy(appointments, getKey)).map((list) => ({
        list,
        groupKey: getGroupKey(list[0]),
        key: getKey(list[0]),
    }));
}

export const getStatusText = (
    status: AppointmentStatus | AppointmentStatusEnum | undefined,
    t: TFunction
): string => {
    switch (status) {
        case 'Confirmed':
        case AppointmentStatusEnum.Confirmed:
            return t('appointments.status.confirmed');
        case 'CustomerArrived':
        case AppointmentStatusEnum.CustomerArrived:
            return t('appointments.status.customerArrived');
        case 'CustomerDidNotArrive':
        case AppointmentStatusEnum.CustomerDidNotArrive:
            return t('appointments.status.customerDidNotArrive');
        case 'OrderCreated':
        case AppointmentStatusEnum.OrderCreated:
            return t('appointments.status.orderCreated');
        case 'Unconfirmed':
        case AppointmentStatusEnum.Unconfirmed:
        default:
            return t('appointments.status.unconfirmed');
    }
};

type AppointmentsScheduleData = {
    localScheduleInMinutes: {
        start: number;
        end: number;
    }[];
    synchronizationEnabled: boolean;
    tzName: string;
};
const DEFAULT_DATA: AppointmentsScheduleData = {
    localScheduleInMinutes: Array.from({ length: 7 }).map(() => ({
        start: 0,
        end: 24 * 60,
    })),
    synchronizationEnabled: false,
    tzName: 'local',
};

const AppointmentsScheduleDataContext = React.createContext<AppointmentsScheduleData>(DEFAULT_DATA);

export function useAppointmentScheduleDataContext() {
    const ctx = useContext(AppointmentsScheduleDataContext);
    if (ctx === null) throw new Error('AppointmentsScheduleDataContext is not found');
    return ctx;
}

const selectRequiredState = createSelector(selectSettings, (s) => ({
    ianaTzId: s.internationalization.ianaTzId,
}));

export const AppointmentScheduleDataProvider = memo(({ children }: React.PropsWithChildren<{}>) => {
    const { ianaTzId } = useAppSelector(selectRequiredState);
    const workingDays = useRepairShopAppointmentSettings()?.workingDays;

    const appointmentSettings = useRepairShopAppointmentSettings();
    const synchronizeAppointmentsEnabled =
        appointmentSettings?.synchronizeAppointmentsEnabled ?? true;
    const value: AppointmentsScheduleData = useMemo(() => {
        if (!workingDays) return DEFAULT_DATA;

        const localScheduleInMinutes = workingDays
            .map((x) => {
                // convert sunday to 7 according to ISO standard
                if (x.dayNumber === 0) return { ...x, dayNumber: 7 };
                return { ...x };
            })
            .sort((a, b) => a.dayNumber - b.dayNumber)
            .map((w) =>
                w.active
                    ? {
                          start: TimeSpan.fromString(w.opening).fullMinutes,
                          end: TimeSpan.fromString(w.closing).fullMinutes,
                      }
                    : { start: 0, end: 0 }
            );

        return {
            localScheduleInMinutes,
            synchronizationEnabled: synchronizeAppointmentsEnabled,
            tzName: ianaTzId,
        };
    }, [workingDays, synchronizeAppointmentsEnabled, ianaTzId]);

    return (
        <AppointmentsScheduleDataContext.Provider value={value}>
            {children}
        </AppointmentsScheduleDataContext.Provider>
    );
});

export function calculateIsWithinSchedule(
    ctx: AppointmentsScheduleData,
    dateString: string,
    minutesOffsetFromMidnight: number
) {
    const localDate = DateTime.fromFormat(dateString, 'yyyy-MM-dd', { zone: 'local' });
    const localStartOfWindow = localDate.plus({ minutes: minutesOffsetFromMidnight }); // start of window in locale time zone
    const startOfWindow = localStartOfWindow.setZone(ctx.tzName);

    // if it's in the past, then it's outside of schedule
    if (startOfWindow.toMillis() < Date.now()) return false;

    const shopDate = startOfWindow.set({ hour: 0, minute: 0 }); // what date is it in the repair shop
    const scheduleForTheDay = ctx.localScheduleInMinutes[shopDate.weekday - 1]; // ISO week day where 1 is monday, 7 is sunday
    if (scheduleForTheDay == undefined || scheduleForTheDay.start === scheduleForTheDay.end) {
        return false;
    }

    const shopOpensAt = shopDate
        .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
        .plus({ minutes: scheduleForTheDay.start });
    const shopClosesAt = shopOpensAt.plus({
        minutes: scheduleForTheDay.end - scheduleForTheDay.start,
    });

    const isWithinSchedule = startOfWindow >= shopOpensAt && startOfWindow < shopClosesAt;

    return isWithinSchedule;
}
