import { useQuery } from '@tanstack/react-query';
import AppointmentsApi, { AppointmentDto } from 'api/appointments';
import useForceRender from 'common/hooks/useForceRender';
import moment from 'moment';
import { useEffect, useRef } from 'react';

export function useAppointmentDetails(
    id?: string,
    enabled: boolean = true,
    onSuccess?: (data: AppointmentDto) => void
) {
    const query = useQuery(['appointment', id], () => AppointmentsApi.get(id!), {
        cacheTime: 600000,
        staleTime: 2000,
        enabled: enabled && !!id,
        onSuccess,
    });

    return query;
}

export function useNow(granularity: moment.unitOfTime.StartOf = 'm') {
    const now = useRef(new Date());
    const fr = useForceRender();

    useEffect(() => {
        const interval = setInterval(() => {
            const newNow = moment();

            if (!newNow.isSame(now.current, granularity)) {
                now.current = newNow.toDate();
                fr();
            }
        }, 60000);
        return () => clearInterval(interval);
    }, [fr, granularity]);
    return now.current;
}
