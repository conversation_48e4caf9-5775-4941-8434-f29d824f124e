import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { ContentGridTdComponent } from 'common/components/AppointmentsScheduler/Content/ContentGrid';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { NotAllowedIcon } from 'common/components/Icons/NotAllowedIcon';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { DateTime } from 'luxon';
import { memo, useContext, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useScheduleAbsencePopup } from 'views/absence/ScheduleAbsence/context';
import { calculateIsWithinSchedule, useAppointmentScheduleDataContext } from '../_common';
import { DaySchedulerDateContext } from './_context';
import useQueryParam from 'common/hooks/useQueryParam';

const useStyles = makeStyles((theme) => ({
    td: {
        position: 'relative',
        '&:hover $actionOverlay': {
            opacity: 1,
        },
    },

    disabled: {
        backgroundColor: 'var(--neutral3)',
        '& > $actionOverlay': {
            display: 'none',
        },
    },

    actionOverlay: {
        userSelect: 'none',
        textDecoration: 'none',
        cursor: 'pointer',
        backgroundColor: rgba(theme.palette.primary.main, 0.1),
        ...theme.typography.h6Inter,
        borderRadius: 4,
        position: 'absolute',
        left: 6,
        right: 6,
        top: 3,
        bottom: 3,
        color: theme.palette.primary.main,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        opacity: 0,
        border: `1px dashed ${theme.palette.primary.main}`,
        fontSize: 11,
    },
}));

const TdComponent: ContentGridTdComponent<string> = memo(({ group: advisorId, startTimestamp }) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const userPermission = useSelector(selectUserPermission);

    const dateStr = useContext(DaySchedulerDateContext);
    const date = DateTime.fromFormat(dateStr, 'yyyy-MM-dd').plus({
        minutes: startTimestamp,
    });
    const dateFormatted = date.toFormat('yyyyMMddHHmm');
    const ctx = useAppointmentScheduleDataContext();
    const isActive = calculateIsWithinSchedule(ctx, dateStr, startTimestamp);

    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [hovered, setHovered] = useState<boolean>(false);

    const scheduleAbsencePopup = useScheduleAbsencePopup();
    const addAbsence = () => {
        scheduleAbsencePopup.open(date.toMillis(), advisorId, rootRef.current!);
        setAnchorEl(null);
    };
    const rootRef = useRef<HTMLTableCellElement | null>(null);

    const onMouseOver = isActive ? () => setHovered(true) : undefined;
    const onMouseLeave = isActive ? () => setHovered(false) : undefined;
    const newAppointmentLink = useNewAppointmentLink(dateFormatted, advisorId);

    return (
        <div
            ref={rootRef}
            aria-hidden={!isActive}
            className={clsx(styles.td, !isActive && styles.disabled)}
            onMouseOver={onMouseOver}
            onMouseLeave={onMouseLeave}
        >
            {!ctx.synchronizationEnabled && userPermission.allowEditAppointments && hovered && (
                <>
                    <div
                        className={styles.actionOverlay}
                        onClick={(e) => setAnchorEl(e.currentTarget)}
                    >
                        {t('appointments.scheduleNewAppointmentOrAbsence')}
                    </div>
                    <SMenu
                        borders
                        anchorOrigin={{ horizontal: 'center', vertical: 'center' }}
                        transformOrigin={{ horizontal: 'center', vertical: 'center' }}
                        id="scheduler-add-work-menu"
                        anchorEl={anchorEl}
                        onClose={() => {
                            setAnchorEl(null);
                        }}
                        transitionDuration={150}
                        open={!!anchorEl}
                        keepMounted
                    >
                        <SMenuItem2>
                            <NavLink
                                draggable="false"
                                to={newAppointmentLink}
                                className={styles.actionOverlay}
                            />
                            <CalendarIcon size={24} fill="currentColor" />
                            {t('appointments.scheduleNewAppointment')}
                        </SMenuItem2>
                        <SMenuItem2 onClick={addAbsence}>
                            <NotAllowedIcon size={24} fill="currentColor" />
                            {t('appointments.scheduleAbsence')}
                        </SMenuItem2>
                    </SMenu>
                </>
            )}
        </div>
    );
});

const useNewAppointmentLink = (dateFormatted: string, advisorId: string): string => {
    const [vehicleIdParam] = useQueryParam('vehicleId');
    const [customerIdParam] = useQueryParam('customerId');

    const searchParams = new URLSearchParams({
        date: dateFormatted,
        advisor: advisorId,
    });

    if (vehicleIdParam) {
        searchParams.set('vehicleId', vehicleIdParam);
    }
    if (customerIdParam) {
        searchParams.set('customerId', customerIdParam);
    }

    return `${ROUTES.APPOINTMENTS.BASE}${
        ROUTES.APPOINTMENTS.SUBROUTES.NEW
    }?${searchParams.toString()}`;
};

export default TdComponent;
