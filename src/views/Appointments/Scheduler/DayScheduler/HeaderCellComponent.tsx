import { styled } from '@mui/material/styles';
import { HeaderCellComponent } from 'common/components/AppointmentsScheduler/Header';
import { memo, useMemo } from 'react';
import { useRepairShopServiceAdvisors } from 'views/Appointments/common';

const AppointmentsHeaderCellComponent: HeaderCellComponent<string> = memo(({ value }) => {
    const advisors = useRepairShopServiceAdvisors();
    const advisor = useMemo(() => (advisors ?? []).find((x) => x.id === value), [advisors, value]);

    return <Root>{advisor?.name ?? value}</Root>;
});

const Root = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[7],
    ...theme.typography.h5Inter,
    textAlign: 'center',
}));

export default AppointmentsHeaderCellComponent;
