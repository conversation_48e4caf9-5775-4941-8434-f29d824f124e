import { useMediaQuery } from '@mui/material';
import { AbsenceDto } from 'api/users';
import AppointmentScheduler, {
    OnSchedulerScrollChanged<PERSON>andler,
    SchedulerScrollController,
} from 'common/components/AppointmentsScheduler';
import {
    ContentInnerWrapperComponent,
    GetItemDataFunction,
} from 'common/components/AppointmentsScheduler/Content';
import { KeyGetterFunction } from 'common/components/AppointmentsScheduler/_util';
import { debounce } from 'lodash';
import moment from 'moment';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { setWeekSchedulerScroll } from 'store/slices/appointments';
import { selectFilters, selectWeekData } from 'store/slices/appointments/selectors';
import { useRepairShopServiceAdvisors } from 'views/Appointments/common';
import AppointmentItem from '../AppointmentItem';
import ColumnDecorations from '../ColumnDecorations';
import Cursor from '../Cursor';
import StopComponent from '../StopComponent';
import {
    Appointment,
    AppointmentGridItem,
    AppointmentScheduleDataProvider,
    createAbsencesGridItemsForDay,
    createAppoitmentsGridItems,
    groupAppointmentsByUser,
} from '../_common';
import AppointmentsHeaderCellComponent from './HeaderCellComponent';
import TdComponent from './TdComponent';
import { DaySchedulerDateContext } from './_context';

const getAppointmentKey: KeyGetterFunction<AppointmentGridItem> = (a) => a.key;
const getAppointmentGroupKey: KeyGetterFunction<AppointmentGridItem> = (a) => a.groupKey;
const getGroupKey: KeyGetterFunction<string> = (d) => d;
const getAppointmentData: GetItemDataFunction<AppointmentGridItem> = (a) => {
    return {
        ts: a.startsAt,
        sizeInMinutes: a.duration,
    };
};

export type DaySchedulerProps = {
    appointments: Appointment[];
    stopsInterval: number;
    absences: AbsenceDto[];
};

const ContentInnerWrapper: ContentInnerWrapperComponent = memo(({ children }) => {
    return (
        <>
            {children}
            <Cursor />
        </>
    );
});

export default function DayScheduler({ appointments, stopsInterval, absences }: DaySchedulerProps) {
    const appointmentGroups = useMemo(() => groupAppointmentsByUser(appointments), [appointments]);
    const { date } = useSelector(selectFilters);
    const [scrollController, setScrollController] = useState<SchedulerScrollController | null>(
        null
    );
    const { uiData } = useAppSelector(selectWeekData);
    const [from, to] = [0, 24 * 60];
    const isSmall = useMediaQuery('(max-width: 900px)');

    useEffect(() => {
        if (!scrollController) return;
        const now = moment();
        if (uiData.scrollInMinutes !== null) {
            scrollController.scrollAtTopInMinutes(uiData.scrollInMinutes);
        } else {
            scrollController.scrollAtCenterInMinutes(now.hour() * 60 + now.minute() - from);
        }
    }, [scrollController, uiData, from]);

    const dispatch = useAppDispatch();
    const onScrollChanged: OnSchedulerScrollChangedHandler = useCallback(
        (data, _event) => {
            dispatch(setWeekSchedulerScroll(data.scrollTopMinutes));
        },
        [dispatch]
    );
    const onScrollChangedDebounced = useMemo(
        () => debounce(onScrollChanged, 1000),
        [onScrollChanged]
    );

    const { advisors: advisorsState } = useAppSelector(selectFilters);
    const allAdvisors = useRepairShopServiceAdvisors();
    const advisors =
        advisorsState.length === 0 ? (allAdvisors ?? []).map((x) => x.id) : advisorsState;

    const gridItems = useMemo(() => {
        const items: AppointmentGridItem[] = [
            ...createAbsencesGridItemsForDay(absences, date, advisors),
            ...createAppoitmentsGridItems(appointmentGroups),
        ];
        return items;
    }, [absences, date, appointmentGroups, advisors]);

    return (
        <AppointmentScheduleDataProvider>
            <DaySchedulerDateContext.Provider value={date}>
                <AppointmentScheduler<AppointmentGridItem, string>
                    groups={advisors}
                    showTimezone={false}
                    items={gridItems}
                    getItemKey={getAppointmentKey}
                    getItemGroupKey={getAppointmentGroupKey}
                    getItemData={getAppointmentData}
                    getGroupKey={getGroupKey}
                    components={{
                        StopComponent,
                        HeaderCellComponent: AppointmentsHeaderCellComponent,
                        ItemComponent: AppointmentItem,
                        ContentGridColumnComponent: ColumnDecorations,
                        ContentInnerWrapperComponent: ContentInnerWrapper,
                        ContentGridTdComponent: TdComponent,
                    }}
                    range={[from, to]} // full day
                    sizes={{
                        columnWidth: 150,
                        // advisors.length > 7 ? 200 : `${100 / Math.max(1, advisors.length)}%`,
                        headerHeight: 50,
                        timelineWidth: isSmall ? 70 : 120,
                        pixelsPerMinute: (1.8 * 30) / stopsInterval,
                    }}
                    stopsInterval={stopsInterval}
                    scrollControllerRef={setScrollController}
                    onScrollChanged={onScrollChangedDebounced}
                />
            </DaySchedulerDateContext.Provider>
        </AppointmentScheduleDataProvider>
    );
}
