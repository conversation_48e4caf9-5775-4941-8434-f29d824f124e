import { styled } from '@mui/material/styles';
import { HeaderCellComponent } from 'common/components/AppointmentsScheduler/Header';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { memo, useMemo } from 'react';

const AppointmentsHeaderCellComponent: HeaderCellComponent<string> = memo(({ value }) => {
    const date = moment(value);
    const { t } = useAppTranslation();
    const dayKey = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][
        date.isoWeekday() - 1
    ];
    const dayName = t(`commonLabels.days.${dayKey}`);
    const isActive = useMemo(() => moment().format('YYYY-MM-DD') === value, [value]);

    return isActive ? (
        <ActiveRoot>
            <DayName>{dayName}</DayName>
            <DayNum>{date.date()}</DayNum>
        </ActiveRoot>
    ) : (
        <Root>
            <DayName>{dayName}</DayName>
            <DayNum>{date.date()}</DayNum>
        </Root>
    );
});

const Root = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    width: '100%',
    gap: 2,
    color: theme.palette.neutral[7],
    justifySelf: 'stretch',
}));

const ActiveRoot = styled(Root)(({ theme }) => ({
    color: theme.palette.primary.light,
    backgroundColor: '#0069FF1A',
}));

const DayName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
}));

const DayNum = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
}));

export default AppointmentsHeaderCellComponent;
