import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { ContentGridTdComponent } from 'common/components/AppointmentsScheduler/Content/ContentGrid';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { DateTime } from 'luxon';
import { memo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useScheduleAppointmentActionsMenuContext } from '../ScheduleAppointmentActionsMenu/context';
import { calculateIsWithinSchedule, useAppointmentScheduleDataContext } from '../_common';

const useStyles = makeStyles((theme) => ({
    td: {
        position: 'relative',
        '&:hover $actionOverlay': {
            opacity: 1,
        },
    },

    disabled: {
        backgroundColor: 'var(--neutral3)',
        '& > $actionOverlay': {
            display: 'none',
        },
    },

    actionOverlay: {
        userSelect: 'none',
        textDecoration: 'none',
        cursor: 'pointer',
        backgroundColor: rgba(theme.palette.primary.main, 0.1),
        ...theme.typography.h6Inter,
        borderRadius: 4,
        position: 'absolute',
        left: 6,
        right: 6,
        top: 3,
        bottom: 3,
        color: theme.palette.primary.main,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        opacity: 0,
        border: `1px dashed ${theme.palette.primary.main}`,
        fontSize: 11,
    },
}));

const TdComponent: ContentGridTdComponent<string> = memo(({ group: dateStr, startTimestamp }) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const userPermission = useSelector(selectUserPermission);
    const ctx = useAppointmentScheduleDataContext();
    const actionsPopupMenu = useScheduleAppointmentActionsMenuContext();

    const rootRef = useRef<HTMLTableCellElement | null>(null);
    const date = DateTime.fromFormat(dateStr, 'yyyy-MM-dd').plus({
        minutes: startTimestamp,
    });
    const isActive = calculateIsWithinSchedule(ctx, dateStr, startTimestamp);

    const [hovered, setHovered] = useState<boolean>(false);

    const onMouseOver = isActive ? () => setHovered(true) : undefined;
    const onMouseLeave = isActive ? () => setHovered(false) : undefined;

    return (
        <div
            ref={rootRef}
            aria-hidden={!isActive}
            className={clsx(styles.td, !isActive && styles.disabled)}
            onMouseOver={onMouseOver}
            onMouseLeave={onMouseLeave}
        >
            {!ctx.synchronizationEnabled && userPermission.allowEditAppointments && hovered && (
                <>
                    <div
                        className={styles.actionOverlay}
                        onClick={(e) => actionsPopupMenu.open(date, null, e.clientX, e.clientY)}
                    >
                        {t('appointments.scheduleNewAppointmentOrAbsence')}
                    </div>
                </>
            )}
        </div>
    );
});

export default TdComponent;
