import { Box, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentAPI from 'api/Appointment';
import { LocalDateObject, localDateTimeDtoToLuxon } from 'api/appointments';
import axios from 'axios';
import { capitalizeFirstLetter } from 'common/Helpers';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { DateTime } from 'luxon';
import { useMemo, useRef, useState } from 'react';
import { isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { deleteAppointmentById } from 'store/slices/appointments';
import { useTz } from 'store/slices/globalSettingsSlice';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { useRepairShopAppointmentSettings } from 'views/Appointments/common';
import useStyles from './css';

type DeleteAppointmentModalProps = {
    open: boolean;
    appointmentData?: {
        id: string;
        customer: string;
        vehicle: string;
        date: LocalDateObject;
    };
    onDeleted: (id: string) => void;
    onClose: () => void;
};

const BodyText = styled('span')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[7],
}));

export default function DeleteAppointmentModal({
    appointmentData,
    open,
    onDeleted,
    onClose,
}: DeleteAppointmentModalProps) {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const tz = useTz();
    const toasters = useToasters();
    const isOmnichannelAppointmentsEnabled =
        useAppSelector(selectSettings).repairShopSettings?.features
            .isOmnichannelAppointmentsEnabled;
    const startDate = appointmentData ? localDateTimeDtoToLuxon(appointmentData.date, tz) : null;
    const settings = useRepairShopAppointmentSettings();
    const endDate = startDate?.plus({ minutes: settings?.duration ?? 0 });
    const id = appointmentData?.id;
    const [isDeleteErrorModalOpen, setIsDeleteErrorModalOpen] = useState<boolean>(false);
    const isSaveOnlyCmos = useRef(false);
    const [errorMessage, setErrorMessage] = useState<string>('');

    const deleteAppointment = useMutation(
        async (skipOmnichannel?: boolean) => {
            if (!id) throw new Error('appointment has no id');
            await AppointmentAPI.delete(id, skipOmnichannel);
        },
        {
            onSuccess: () => {
                if (isOmnichannelAppointmentsEnabled && !isSaveOnlyCmos.current) {
                    toasters.success(t('appointments.appointmentDeletedInYourIntegratedSoftware'));
                }
                isSaveOnlyCmos.current = false;
                dispatch(deleteAppointmentById(id!));
                onDeleted(id!);
            },
            onError: (error) => {
                if (
                    (isErrorResponse(error) || axios.isAxiosError(error)) &&
                    isOmnichannelAppointmentsEnabled
                ) {
                    setIsDeleteErrorModalOpen(true);
                    setErrorMessage(error.message);
                    toasters.danger(
                        t(
                            'appointments.omnichannelModal.appointmentNotDeletedInYourIntegratedSoftware'
                        )
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
        }
    );

    const body = (
        <>
            <Box>
                <Box justifyContent="center" display="flex">
                    <BodyText>
                        <strong>{appointmentData?.customer}</strong>
                    </BodyText>
                </Box>
            </Box>
            <Box style={{ marginTop: 5 }}>
                <Box justifyContent="center" display="flex">
                    <BodyText>
                        <strong>{appointmentData?.vehicle}</strong>
                    </BodyText>
                </Box>
            </Box>
            <Box style={{ marginTop: 5 }}>
                <Box justifyContent="center" display="flex">
                    <BodyText>
                        {startDate && endDate
                            ? `${capitalizeFirstLetter(
                                  startDate.toFormat(t('dateFormats.shortDateWithDayLuxon'))
                              )} ${startDate.toFormat('HH:mm')} - ${endDate.toFormat(
                                  'HH:mm'
                              )}hrs (${settings?.duration} min)`
                            : '--'}
                    </BodyText>
                </Box>
            </Box>
        </>
    );

    const disabled =
        useMemo(() => {
            if (!startDate) return true;
            return startDate < DateTime.now();
        }, [startDate]) || !id;

    return (
        <>
            <DeleteConfirmationPopup
                open={open}
                isConfirmDisabled={disabled}
                title={t('appointments.deleteTitle')}
                body={body}
                cancel={t('commonLabels.doNotDelete')}
                confirm={t('appointments.delete')}
                onConfirm={() => deleteAppointment.mutate(false)}
                onClose={onClose}
                showLoader={deleteAppointment.isLoading}
            />
            <DeleteConfirmationPopup
                open={isDeleteErrorModalOpen}
                title={t(
                    'appointments.omnichannelModal.appointmentNotDeletedInYourIntegratedSoftware'
                )}
                body={
                    <>
                        <Box className={styles.textContent}>
                            {t(
                                'appointments.omnichannelModal.reasonForErrorInYourIntegratedSoftware'
                            )}
                            : "{errorMessage}"
                        </Box>
                        <Box className={styles.textContent} style={{ marginTop: 15 }}>
                            {t(
                                'appointments.omnichannelModal.doYouWantToDeleteTheAppointmentOnlyInClearMechanic'
                            )}
                        </Box>
                    </>
                }
                cancel={t('appointments.omnichannelModal.noKeepTheAppointment')}
                confirm={t('appointments.omnichannelModal.yesDeleteTheAppointment')}
                onConfirm={() => {
                    isSaveOnlyCmos.current = true;
                    setIsDeleteErrorModalOpen(false);
                    deleteAppointment.mutate(true);
                }}
                onClose={() => {
                    setIsDeleteErrorModalOpen(false);
                }}
            />
        </>
    );
}
