import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { NotAllowedIcon } from 'common/components/Icons/NotAllowedIcon';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useScheduleAbsencePopup } from 'views/absence/ScheduleAbsence/context';
import {
    IScheduleAppointmentActionsMenuContext,
    ScheduleAppointmentActionsMenuContext,
} from './context';

type ScheduleAppointmentActionsMenuProps = {
    formattedDate: string;
    anchorEl: HTMLElement | null;
    onClose: () => void;
    addAbsenceClick: React.MouseEventHandler;
};

export default function ScheduleAppointmentActionsMenu({
    formattedDate,
    anchorEl,
    addAbsenceClick,
    onClose,
}: ScheduleAppointmentActionsMenuProps) {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const createAppointmentRoute = `${ROUTES.APPOINTMENTS.BASE}${ROUTES.APPOINTMENTS.SUBROUTES.NEW}?date=${formattedDate}`;

    return (
        <SMenu
            borders
            anchorOrigin={{ horizontal: 'center', vertical: 'bottom' }}
            transformOrigin={{ horizontal: 'center', vertical: 'top' }}
            id="scheduler-add-work-menu-keke"
            anchorEl={anchorEl}
            onClose={onClose}
            transitionDuration={150}
            open={!!anchorEl}
            keepMounted
        >
            <SMenuItem2 onClick={() => navigate(createAppointmentRoute)}>
                <CalendarIcon size={24} fill="currentColor" />

                <NavLink className="a-inherit" draggable="false" to={createAppointmentRoute}>
                    {t('appointments.scheduleNewAppointment')}
                </NavLink>
            </SMenuItem2>
            <SMenuItem2 onClick={addAbsenceClick}>
                <NotAllowedIcon size={24} fill="currentColor" />
                {t('appointments.scheduleAbsence')}
            </SMenuItem2>
        </SMenu>
    );
}

export function ScheduleAppointmentActionsMenuContextProvider({
    children,
}: React.PropsWithChildren<{}>) {
    const [state, setState] = useState<{
        anchorEl: HTMLElement;
        date: DateTime;
        userId: string | null;
    } | null>(null);

    useEffect(() => {
        return () => state?.anchorEl.remove();
    }, [state?.anchorEl]);

    const scheduleAbsencePopup = useScheduleAbsencePopup();

    const addAbsence = useCallback(() => {
        if (!state) return;
        scheduleAbsencePopup.open(state.date.toMillis(), state.userId, state.anchorEl);
        // close the menu after 200ms to give the schedule absence popup some time to position iteself in the right place before
        // removing anchor element
        setTimeout(() => {
            setState(null);
        }, 200);
    }, [scheduleAbsencePopup, state]);

    const ctx: IScheduleAppointmentActionsMenuContext = useMemo(
        () => ({
            open: (dt, userId, x, y) => {
                // create temporary anchor element to avoid menu acting weird if original anchor disappears
                const el = document.createElement('div');
                el.style.position = 'fixed';
                el.style.left = `${x}px`;
                el.style.top = `${y}px`;
                el.style.width = el.style.height = '1px';
                document.body.appendChild(el);

                setState({
                    anchorEl: el,
                    date: dt,
                    userId,
                });
            },
        }),
        []
    );

    return (
        <>
            <ScheduleAppointmentActionsMenuContext.Provider value={ctx}>
                {children}
            </ScheduleAppointmentActionsMenuContext.Provider>
            <ScheduleAppointmentActionsMenu
                formattedDate={state ? state.date.toFormat('yyyyMMddHHmm') : ''}
                anchorEl={state ? state.anchorEl : null}
                addAbsenceClick={addAbsence}
                onClose={() => setState(null)}
            />
        </>
    );
}
