import { styled } from '@mui/styles';
import { StopComponent } from 'common/components/AppointmentsScheduler/TimeLine';
import moment from 'moment';
import { memo } from 'react';

const day = moment();

const Stop: StopComponent = memo(({ ts }) => {
    day.hour(0).minute(0).add(ts, 'm');
    const str = day.format('HH:mm');
    return <StyledStop>{str}</StyledStop>;
});

const StyledStop = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[6],
    ...theme.typography.h6Inter,
}));

export default Stop;
