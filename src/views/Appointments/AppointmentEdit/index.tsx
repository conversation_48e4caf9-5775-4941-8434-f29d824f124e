// import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useMutation, useQuery } from '@tanstack/react-query';
import AppointmentAPI from 'api/Appointment';
import CustomersAPI from 'api/Clients/Customers';
import AppointmentsApi from 'api/appointments';
import CustomersApi from 'api/customers';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import { TimeSpan } from 'api/utils/format';
import axios from 'axios';
import { capitalizeFirstLetter } from 'common/Helpers';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import {
    DeleteConfirmationPopup,
    WarningConfirmationPopup,
} from 'common/components/Popups/ConfirmationPopup';
import { isUuid, ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { hasSubCode, isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { clearAppointmentAttachments } from 'store/slices/appointmentAttachment';
import { selectAppointmentAttachments } from 'store/slices/appointmentAttachment/selectors';
import { appointmentsActions } from 'store/slices/appointments';
import { selectEditData } from 'store/slices/appointments/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user';
import { PermissionsGate } from 'views/Components/AuthorizationGate';
import AppointmentPartB from '../AppointmentPartB';
import AppointmentStepOne from '../AppointmentStepOne';
import AppointmentStepThree from '../AppointmentStepThree';
import AppointmentStepTwo from '../AppointmentStepTwo';
import { useEditAppointmentData, useRepairShopAppointmentSettings } from '../common';
import AppointmentEditHeader from '../common/AppointmentEditHeader';

const OMNICHANNEL_CREATE = 'omnichannelCreate';

function AppointmentEditInner() {
    const appointmentId = useParams<{ appointmentId: string }>().appointmentId ?? '';

    if (isUuid(appointmentId)) {
        return <AppointmentLegacyRedirect id={appointmentId} />;
    }

    return <AppointmentEditInnerMain />;
}

function AppointmentLegacyRedirect({ id }: { id: string }) {
    const idRef = useRef(id);
    idRef.current = id;

    const navigate = useNavigate();

    useEffect(() => {
        AppointmentsApi.getNumberById(id).then((number) => {
            if (idRef.current === id) {
                navigate(`/appointments/${number}`, {
                    replace: true,
                });
            }
        });
    }, [id, navigate]);

    return <p>Please wait, you will be redirected shortly...</p>;
}

function AppointmentEditInnerMain() {
    const appointmentNumber = useParams<{ appointmentId: string }>().appointmentId ?? '';

    const navigate = useNavigate();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const files = useAppSelector(selectAppointmentAttachments);
    const permissions = useAppSelector(selectUserPermission);

    const isOmnichannelAppointmentsEnabled =
        useAppSelector(selectSettings).repairShopSettings?.features
            .isOmnichannelAppointmentsEnabled;

    const isInEditMode = !!appointmentNumber;

    const settings = useRepairShopAppointmentSettings();

    useQuery(
        ['appointment', 'details', appointmentNumber],
        () => AppointmentsApi.getDetailsByNumber(appointmentNumber),
        {
            enabled: isInEditMode,
            onSuccess(response) {
                dispatch(appointmentsActions.setInitialAppointmentData(response));
            },
        }
    );

    const { appointmentData, vehicle, customer } = useAppSelector(selectEditData);

    const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false);
    const [openConfirmationModal, setOpenConfirmationModal] = useState<boolean>(false);
    const [isErrorModalOpen, setIsErrorModalOpen] = useState<boolean>(false);
    const [isDeleteErrorModalOpen, setIsDeleteErrorModalOpen] = useState<boolean>(false);
    const isSaveOnlyCmos = useRef(false);
    const [errorMessage, setErrorMessage] = useState<string>('');

    const { valid, modified } = useEditAppointmentData();

    const isCustomerModified = useMemo(() => {
        let isUpdated = false;
        if (customer) {
            isUpdated =
                appointmentData.customerFirstName !== customer.firstName ||
                appointmentData.customerLastName !== customer.lastName ||
                appointmentData.customerMobile !== customer.mobile ||
                appointmentData.customerEmail !== customer.email ||
                appointmentData.customerTaxId !== customer.taxIdentification;
        }

        return isUpdated;
    }, [appointmentData, customer]);

    const isVehicleModified = useMemo(() => {
        let isUpdated = false;

        if (vehicle) {
            isUpdated =
                appointmentData.vehiclePlates !== vehicle.plates ||
                appointmentData.vehicleModel !== vehicle.model ||
                appointmentData.vehicleBrand !== vehicle.brand ||
                appointmentData.vehicleVin !== vehicle.vin ||
                appointmentData.vehicleYear !== vehicle.year ||
                appointmentData.vehicleColor !== vehicle.color;
        }

        return isUpdated;
    }, [appointmentData, vehicle]);

    const handleCancel = () => {
        navigate(`${ROUTES.APPOINTMENTS.BASE}`);
        dispatch(appointmentsActions.resetAppointmentData());
    };

    const updateCustomer = async () => {
        if (appointmentData.customerId) {
            await CustomersAPI.updateCustomer(appointmentData.customerId, {
                firstName: appointmentData.customerFirstName || undefined,
                lastName: appointmentData.customerLastName || undefined,
                mobile: appointmentData.customerMobile || undefined,
                email: appointmentData.customerEmail || undefined,
                taxIdentification: appointmentData.customerTaxId || undefined,
            });
        }
    };

    const updateVehicle = async () => {
        if (appointmentData.vehicleId) {
            await CustomersApi.updateVehicle(appointmentData.vehicleId, {
                plates: appointmentData.vehiclePlates || undefined,
                vin: appointmentData.vehicleVin || undefined,
                model: appointmentData.vehicleModel || undefined,
                brand: appointmentData.vehicleBrand || undefined,
                year: appointmentData.vehicleYear || undefined,
                color: appointmentData.vehicleColor || undefined,
            });
        }
    };

    const saveAppointment = useMutation(
        async (skipOmnichannel?: boolean) => {
            const canUpdateCustomer = permissions.allowEditCustomers;
            const canUpdateVehicle = permissions.allowEditVehicles;

            if (!settings) return;
            if (!appointmentData.scheduleWindow) throw new Error('Schedule window is required');
            if (!appointmentData.customerId) throw new Error('Customer is required');
            if (isCustomerModified) {
                await updateCustomer();
            }

            if (isVehicleModified) {
                await updateVehicle();
            }

            if (!appointmentData.id)
                throw new Error('cannot update appointment that has no assigned id');

            const response = await AppointmentsApi.update(
                appointmentData.id,
                {
                    reasons: appointmentData.reasons.map((x) => x.id),
                    customReasons: appointmentData.customReasons,
                    declinedItemReasonsForView: appointmentData.declinedItemReasonsForView,
                    promisedAt: appointmentData.deliverDate,
                    startsAt: appointmentData.startsAt,
                    observations: appointmentData.observations,
                    duration: Math.floor(
                        (TimeSpan.fromString(appointmentData.scheduleWindow.end).totalSeconds -
                            TimeSpan.fromString(appointmentData.scheduleWindow.start)
                                .totalSeconds) /
                            60
                    ),
                    serviceAdvisorId: appointmentData.serviceAdvisorId,
                    originId: appointmentData.origin?.id ?? null,
                    withValetService: appointmentData.withValetService,

                    customerUpdateData: canUpdateCustomer
                        ? {
                              customer: {
                                  id: appointmentData.customerId,
                                  firstName: appointmentData.customerFirstName,
                                  lastName: appointmentData.customerLastName,
                                  email: appointmentData.customerEmail,
                                  taxIdentification: appointmentData.customerTaxId,
                                  mobile: appointmentData.customerMobile,
                              },
                          }
                        : null,

                    vehicleUpdateData: canUpdateVehicle
                        ? {
                              vehicle: appointmentData.vehicleId
                                  ? {
                                        id: appointmentData.vehicleId,
                                        plates: appointmentData.vehiclePlates,
                                        model: appointmentData.vehicleModel,
                                        brand: appointmentData.vehicleBrand,
                                        vin: appointmentData.vehicleVin,
                                        year: appointmentData.vehicleYear,
                                        color: appointmentData.vehicleColor,
                                    }
                                  : null,
                          }
                        : null,
                },
                skipOmnichannel
            );

            const uploadFiles = files.filter(
                (attachment): attachment is File => attachment instanceof File
            );

            if (uploadFiles.length > 0) {
                await AppointmentsApi.uploadAttachments(uploadFiles, appointmentData.id);
            }

            dispatch(clearAppointmentAttachments());

            return response;
        },
        {
            onError: (error) => {
                if (isErrorResponse(error) && isOmnichannelAppointmentsEnabled) {
                    setIsErrorModalOpen(true);
                    setErrorMessage(error.message);
                    toasters.danger(
                        t(
                            'appointments.omnichannelModal.appointmentNotUpdatedInYourIntegratedSoftware'
                        )
                    );
                } else if (
                    isErrorResponse(error) &&
                    hasSubCode(error, 'ValidationError', 'InvalidMobileNumberError')
                ) {
                    toasters.danger(
                        t('appointments.invalidMobileNumberError'),
                        t('toasters.errorOccurred')
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
            onSuccess: () => {
                toasters.success(
                    `${appointmentData.customerFirstName} ${
                        appointmentData.customerLastName || ' '
                    } - ${moment(
                        `${appointmentData.startsAt.date} ${appointmentData.startsAt.time}`
                    ).format(t('dateFormats.midDate'))}`,
                    t('appointments.appointmentUpdated')
                );

                if (isOmnichannelAppointmentsEnabled && !isSaveOnlyCmos.current) {
                    toasters.success(t('appointments.appointmentUpdatedInYourIntegratedSoftware'));
                }
                isSaveOnlyCmos.current = false;

                navigate(`${ROUTES.APPOINTMENTS.BASE}`);
            },
            onSettled: () => {
                toasters.dismiss(OMNICHANNEL_CREATE);
            },
        }
    );

    const deleteModalContent = (
        <>
            <Box>
                <Box justifyContent="center" style={{ display: 'flex' }}>
                    <BodyText>
                        <strong>{`${appointmentData?.customerFirstName} ${appointmentData?.customerLastName}`}</strong>
                    </BodyText>
                </Box>
            </Box>
            <Box style={{ marginTop: 5 }}>
                <Box justifyContent="center" style={{ display: 'flex' }}>
                    <BodyText>
                        <strong>
                            {[
                                appointmentData.vehiclePlates ?? '--',
                                appointmentData.vehicleVin,
                                appointmentData.vehicleBrand,
                                appointmentData.vehicleModel,
                                appointmentData.vehicleYear,
                                appointmentData.vehicleColor,
                            ]
                                .filter(Boolean)
                                .join(', ')}
                        </strong>
                    </BodyText>
                </Box>
            </Box>
            <Box style={{ marginTop: 5 }}>
                <Box justifyContent="center" style={{ display: 'flex' }}>
                    {appointmentData.startsAt && (
                        <BodyText>
                            {`${capitalizeFirstLetter(
                                moment(
                                    `${appointmentData.startsAt.date} ${appointmentData.startsAt.time}`
                                ).format(t('dateFormats.shortDateWithDay'))
                            )} ${moment(
                                `${appointmentData.startsAt.date} ${appointmentData.startsAt.time}`
                            ).format('HH:mm')} - ${moment(
                                `${appointmentData.startsAt.date} ${appointmentData.startsAt.time}`
                            )
                                .add(settings?.duration ?? 0, 'm')
                                .format('HH:mm')}hrs (${settings?.duration} min)`}
                        </BodyText>
                    )}
                </Box>
            </Box>
        </>
    );

    const deleteAppointment = useMutation(
        async (skipOmnichannel?: boolean) => {
            if (!isInEditMode) return;
            if (!appointmentData.id)
                throw new Error('cannot update appointment that has no assigned id');
            await AppointmentAPI.delete(appointmentData.id, skipOmnichannel);
        },
        {
            onSuccess: () => {
                toasters.success(
                    t('appointments.theAppointmentHasBeenDeletedSuccessfully'),
                    t('appointments.appointmentDeleted')
                );

                if (isOmnichannelAppointmentsEnabled && !isSaveOnlyCmos.current) {
                    toasters.success(t('appointments.appointmentDeletedInYourIntegratedSoftware'));
                }
                isSaveOnlyCmos.current = false;

                navigate(`${ROUTES.APPOINTMENTS.BASE}`);
            },
            onError: (error) => {
                if (isErrorResponse(error) && isOmnichannelAppointmentsEnabled) {
                    setIsDeleteErrorModalOpen(true);
                    setErrorMessage(error.message);
                    toasters.danger(
                        t(
                            'appointments.omnichannelModal.appointmentNotDeletedInYourIntegratedSoftware'
                        )
                    );
                } else if (axios.isAxiosError(error) && isOmnichannelAppointmentsEnabled) {
                    setIsDeleteErrorModalOpen(true);
                    setErrorMessage(error.message);
                    toasters.danger(
                        t(
                            'appointments.omnichannelModal.appointmentNotDeletedInYourIntegratedSoftware'
                        )
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
        }
    );

    const isDeleteDisable = useMemo(() => {
        const currentDate = new Date();
        const apptDate = moment(
            `${appointmentData.startsAt.date} ${appointmentData.startsAt.time}`
        );
        return !(
            apptDate.isAfter(currentDate) ||
            currentDate <=
                apptDate
                    .clone()
                    .add(settings?.duration ?? 0, 'm')
                    .toDate()
        );
    }, [appointmentData.startsAt, settings?.duration]);

    useEffect(() => {
        return () => {
            dispatch(appointmentsActions.resetAppointmentData());
        };
    }, [dispatch]);

    useDocumentTitle(t('titles.appointments'));

    return (
        <CustomersSearchApiProvider mode="shop">
            <RootContainer
                container
                spacing={0}
                justifyContent="center"
                style={{ zIndex: 1, overflow: 'hidden' }}
            >
                <StyledContainer item xs={11}>
                    <Grid container spacing={0} justifyContent="center">
                        <Grid item xs={11}>
                            <AppointmentEditHeader
                                isSaving={saveAppointment.isLoading}
                                modified={modified}
                                valid={valid}
                                onDelete={() => setOpenDeleteModal(true)}
                                onCancel={handleCancel}
                                onSave={() => {
                                    if (!(isCustomerModified || isVehicleModified)) {
                                        saveAppointment.mutate(false);
                                    } else {
                                        setOpenConfirmationModal(true);
                                    }
                                }}
                            />
                            <Grid container spacing={2} style={{ marginTop: 5 }}>
                                <Grid item xs={12} md={9} style={{ overflow: 'hidden' }}>
                                    <PartA>
                                        <AppointmentStepOne
                                            repairShopKey={settings?.repairShopKey}
                                        />
                                        <div
                                            style={{
                                                marginTop: 30,
                                                marginBottom: 30,
                                                borderTop: `1px solid #EBEBEB`,
                                                width: '100%',
                                            }}
                                        />
                                        <AppointmentStepTwo />
                                        <div
                                            style={{
                                                marginTop: 30,
                                                marginBottom: 30,
                                                borderTop: `1px solid #EBEBEB`,
                                                width: '100%',
                                            }}
                                        />
                                        <AppointmentStepThree />
                                    </PartA>
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <AppointmentPartB disableAppointmentNumber />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </StyledContainer>
            </RootContainer>
            <ConfirmationModal
                open={openConfirmationModal}
                cancelCaptionButton={t('appointments.cancel')}
                confirmationCaptionButton={t('appointments.updateCustomer')}
                onClose={() => setOpenConfirmationModal(false)}
                onConfirmation={() => {
                    setOpenConfirmationModal(false);
                    saveAppointment.mutate(false);
                }}
            >
                {t('appointments.confirmCustomerUpdateText')}
            </ConfirmationModal>
            <DeleteConfirmationPopup
                open={openDeleteModal}
                isConfirmDisabled={isDeleteDisable}
                title={t('appointments.deleteTitle')}
                body={deleteModalContent}
                cancel={t('commonLabels.doNotDelete')}
                confirm={t('appointments.delete')}
                onConfirm={() => {
                    deleteAppointment.mutate(false);
                    setOpenDeleteModal(false);
                }}
                onClose={() => {
                    setOpenDeleteModal(false);
                }}
            />
            <WarningConfirmationPopup
                open={isErrorModalOpen}
                title={t(
                    'appointments.omnichannelModal.appointmentNotUpdatedInYourIntegratedSoftware'
                )}
                body={
                    <TextContent>
                        {t('appointments.omnichannelModal.reasonForErrorInYourIntegratedSoftware')}:
                        "{errorMessage}"
                    </TextContent>
                }
                cancel={t('appointments.omnichannelModal.retryApptUpdate')}
                confirm={t('appointments.omnichannelModal.updateApptInClearmechanic')}
                onConfirm={() => {
                    isSaveOnlyCmos.current = true;
                    setIsErrorModalOpen(false);
                    saveAppointment.mutate(true);
                }}
                onClose={() => {
                    setIsErrorModalOpen(false);
                }}
                onCancel={() => {
                    setIsErrorModalOpen(false);
                    toasters.progress(
                        t(
                            'appointments.omnichannelModal.weAreUpdatingTheAppointmentInYourIntegratedSoftware'
                        ),
                        t('appointments.omnichannelModal.oneMomentPlease'),
                        { id: OMNICHANNEL_CREATE }
                    );
                    saveAppointment.mutate(false);
                }}
            />
            <DeleteConfirmationPopup
                open={isDeleteErrorModalOpen}
                title={t(
                    'appointments.omnichannelModal.appointmentNotDeletedInYourIntegratedSoftware'
                )}
                body={
                    <>
                        <TextContent>
                            {t(
                                'appointments.omnichannelModal.reasonForErrorInYourIntegratedSoftware'
                            )}
                            : "{errorMessage}"
                        </TextContent>
                        <TextContent style={{ marginTop: 15 }}>
                            {t(
                                'appointments.omnichannelModal.doYouWantToDeleteTheAppointmentOnlyInClearMechanic'
                            )}
                        </TextContent>
                    </>
                }
                cancel={t('appointments.omnichannelModal.noKeepTheAppointment')}
                confirm={t('appointments.omnichannelModal.yesDeleteTheAppointment')}
                onConfirm={() => {
                    isSaveOnlyCmos.current = true;
                    setIsDeleteErrorModalOpen(false);
                    deleteAppointment.mutate(true);
                }}
                onClose={() => {
                    setIsDeleteErrorModalOpen(false);
                }}
            />
        </CustomersSearchApiProvider>
    );
}

export default function AppointmentEdit() {
    const { t } = useAppTranslation();

    return (
        <PermissionsGate
            predicate={['allowEditAppointments', 'allowSeeAppointments']}
            text={t('appointments.accessDeniedText')}
            hint={t('appointments.accessDeniedHint')}
        >
            <AppointmentEditInner />
        </PermissionsGate>
    );
}

const RootContainer = styled(Grid)({
    zIndex: 1,
    overflow: 'hidden',
});

const StyledContainer = styled(Grid)(({ theme }) => ({
    border: '1px solid #DBDCDD',
    borderRadius: 12,
    paddingTop: 35,
    marginTop: 20,
    height: '100%',
    background: theme.palette.neutral[1],
}));

const PartA = styled(Box)({
    padding: 10,
    maxHeight: 'calc(100vh - 245px)',
    overflowY: 'scroll',
    overflowX: 'hidden',
});

const BodyText = styled(Typography)(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[7],
}));

const TextContent = styled(Box)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    padding: '0px 10px',
}));
