import { Box, styled, useTheme } from '@mui/material';
import Grid from '@mui/material/Grid';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';

export type CollapsibleSectionType = {
    title: string | JSX.Element;
    isTitleComponent?: boolean;
    isCollapse?: boolean;
    children?: React.ReactNode;
    isChildElement?: boolean;
    disabled?: boolean;
};

const CollapsibleSection = ({
    title,
    isTitleComponent,
    isCollapse,
    children,
    isChildElement,
    disabled,
}: CollapsibleSectionType) => {
    const theme = useTheme();
    const [isCollapsed, setIsCollapsed] = useState(isCollapse);
    return (
        <CollapsibleContainer
            style={{
                height: isCollapsed ? 50 : 'auto',
                paddingBottom: isCollapsed ? 0 : 18,
            }}
        >
            <TitleSection
                isChildElement={isChildElement ?? false}
                onClick={() => !disabled && setIsCollapsed(!isCollapsed)}
            >
                <Grid container spacing={0} justifyContent="flex-start">
                    <Grid
                        item
                        xs={1}
                        sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                    >
                        <Box sx={{ paddingRight: 4 }}>
                            {isCollapsed ? (
                                <DownIcon
                                    fill={
                                        !disabled
                                            ? theme.palette.primary.dark
                                            : theme.palette.grey[500]
                                    }
                                    size={IconSize.M}
                                />
                            ) : (
                                <UpIcon
                                    fill={
                                        !disabled
                                            ? theme.palette.primary.dark
                                            : theme.palette.grey[500]
                                    }
                                    size={IconSize.M}
                                />
                            )}
                        </Box>
                    </Grid>
                    <Grid
                        item
                        xs={10}
                        sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
                    >
                        {isTitleComponent ? title : <span>{title}</span>}
                    </Grid>
                </Grid>
            </TitleSection>
            {children}
        </CollapsibleContainer>
    );
};

const CollapsibleContainer = styled('div')({
    padding: '0 24px',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
});

const TitleSection = styled('div')<{ isChildElement: boolean }>(({ theme, isChildElement }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    padding: `${isChildElement ? '0' : '12px'} 0 20px 0`,
    textAlign: 'left',
    cursor: 'pointer',
}));

export default CollapsibleSection;
