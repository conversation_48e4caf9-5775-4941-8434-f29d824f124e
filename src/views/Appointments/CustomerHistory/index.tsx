import LaunchIcon from '@mui/icons-material/Launch';
import { Box, Grid, IconButton, styled, useTheme } from '@mui/material';
import Divider from '@mui/material/Divider';
import { OrderShortInfo } from 'api/appointments';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { ENTERPRISE_ROUTES, ROUTES } from 'common/constants';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { generatePath } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectCustomerHistory } from 'store/slices/appointments';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import CollapsibleSection from './CollapsibleSection';

type CustomerHistoryProps = { onClose: () => void };

export default function CustomerHistory({ onClose }: CustomerHistoryProps) {
    const theme = useTheme();
    const settings = useAppSelector(selectSettings);
    const { t } = useAppTranslation();
    const isEnterprise = useIsEnterpriseRoute();

    const [isExpandedOrderList, setIsExpandedOrderList] = useState<boolean>(false);
    const [isExpandedNotes, setIsExpandedNotes] = useState<boolean>(false);

    const customerHistory = useAppSelector(selectCustomerHistory);

    const dateFormat = useMemo(
        () => (settings.internationalization.language === 'en' ? 'MM/DD/YYYY' : 'DD/MM/YYYY'),
        [settings.internationalization.language]
    );

    const urgentItems = useMemo(
        () =>
            customerHistory?.repairItems.filter(
                (x) => x.priorityLevel === getPriorityIndex(PriorityLevel.Urgent)
            ) ?? [],
        [customerHistory?.repairItems]
    );

    const mediumItems = useMemo(
        () =>
            customerHistory?.repairItems.filter(
                (x) => x.priorityLevel === getPriorityIndex(PriorityLevel.Med)
            ) ?? [],
        [customerHistory?.repairItems]
    );

    const urgentItemsTotal = useMemo(() => {
        const total = urgentItems.reduce((sum, value) => sum + value.subtotal, 0);

        return total === 0 ? '$-' : `$${total}`;
    }, [urgentItems]);

    const mediumItemsTotal = useMemo(() => {
        const total = mediumItems.reduce((sum, value) => sum + value.subtotal, 0);

        return total === 0 ? '$-' : `$${total}`;
    }, [mediumItems]);

    const openOrderLink = (orderId: number) => {
        const orderLink =
            '/dashboard' +
            generatePath(isEnterprise ? ENTERPRISE_ROUTES.ORDERS_DETAIL : ROUTES.ORDERS_DETAIL, {
                id: orderId,
            }) +
            '?section=estimate';

        if (orderLink) {
            window.open(orderLink, '_blank');
        }
    };

    function renderOrderShortInfo(order: OrderShortInfo) {
        return (
            <>
                <StyledGrid item xs={7}>
                    <OrderListItem>{`${t('appointments.customerHistory.order')} #${
                        order.orderNumber
                    }`}</OrderListItem>
                </StyledGrid>
                <StyledGrid item xs={3}>
                    <OrderListItem>{moment(order.uploadTime).format(dateFormat)}</OrderListItem>
                </StyledGrid>
                <StyledGrid item xs={2} sx={{ padding: '0 !important' }}>
                    <OrderListButonContainer>
                        <ArrowTooltip content={t('appointments.customerHistory.goToOrderDetail')}>
                            <IconButton
                                size="large"
                                sx={{
                                    padding: '0 0 0 0',
                                    alignItems: 'end',
                                }}
                                onClick={() => openOrderLink(order.orderId)}
                            >
                                <LaunchIcon
                                    fontSize="small"
                                    sx={{ color: theme.palette.primary.light }}
                                />
                            </IconButton>
                        </ArrowTooltip>
                    </OrderListButonContainer>
                </StyledGrid>
            </>
        );
    }

    return (
        <StyledCustomerHistory>
            {customerHistory != null ? (
                <>
                    <StyledFirstHeader>
                        <StyledHeaderContainer>
                            <StyledCustomerName>{customerHistory.customerName}</StyledCustomerName>
                            <IconButton size="small" onClick={onClose}>
                                <CloseIcon fill={theme.palette.common.white} />
                            </IconButton>
                        </StyledHeaderContainer>
                    </StyledFirstHeader>
                    <StyledScrollable>
                        <CollapsibleSection
                            title={t('appointments.customerHistory.previousOrders')}
                            isCollapse
                        >
                            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                                <Grid
                                    container
                                    width={'90%'}
                                    spacing="10px"
                                    direction="row"
                                    justifyContent="center"
                                >
                                    {isExpandedOrderList
                                        ? customerHistory.ordersList.map((x) =>
                                              renderOrderShortInfo(x)
                                          )
                                        : customerHistory.ordersList
                                              .slice(0, 3)
                                              .map((x) => renderOrderShortInfo(x))}
                                    {customerHistory.ordersList.length > 3 && (
                                        <Grid item sx={{ padding: '15px 0 !important' }} xs={12}>
                                            <SeeMoreButton
                                                onClick={() =>
                                                    setIsExpandedOrderList(!isExpandedOrderList)
                                                }
                                            >
                                                {isExpandedOrderList
                                                    ? t('appointments.customerHistory.seeLess')
                                                    : t('appointments.customerHistory.seeMore')}
                                            </SeeMoreButton>
                                        </Grid>
                                    )}
                                </Grid>
                            </Box>
                        </CollapsibleSection>
                        <StyledDivider />
                        <CollapsibleSection
                            title={t('appointments.customerHistory.nextMaintenance')}
                            isCollapse
                        >
                            <Grid container spacing="15px" direction="row" justifyContent="center">
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.nextMaintenanceDate')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.nextMaintenanceInfo.nextMaintenanceDate !=
                                        null
                                            ? moment(
                                                  customerHistory.nextMaintenanceInfo
                                                      .nextMaintenanceDate
                                              ).format(dateFormat)
                                            : '--'}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.mileage')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.nextMaintenanceInfo.mileage != null
                                            ? InternationalizationLogic.numberFormat(
                                                  settings.internationalization,
                                                  customerHistory.nextMaintenanceInfo.mileage
                                              )
                                            : '--'}
                                    </StyledText>
                                </Grid>
                            </Grid>
                        </CollapsibleSection>
                        <StyledDivider variant="middle" />
                        <CollapsibleSection
                            title={t('appointments.customerHistory.lastOrder')}
                            isCollapse
                        >
                            <Grid container spacing="15px" direction="row" justifyContent="center">
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.order')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.lastOrderInfo.orderNumber}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.date')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {moment(customerHistory.lastOrderInfo.uploadTime).format(
                                            dateFormat
                                        )}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.mileage')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.lastOrderInfo.mileage != null
                                            ? InternationalizationLogic.numberFormat(
                                                  settings.internationalization,
                                                  customerHistory.lastOrderInfo.mileage
                                              )
                                            : '--'}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.serviceAdvisor')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.lastOrderInfo.serviceAdvisorName
                                            ? customerHistory.lastOrderInfo.serviceAdvisorName
                                            : '--'}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.orderType')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.lastOrderInfo.orderType
                                            ? customerHistory.lastOrderInfo.orderType
                                            : '--'}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.reasonForTheAppointment')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledText>
                                        {customerHistory.lastOrderInfo.reasonsForAppointment
                                            ? customerHistory.lastOrderInfo.reasonsForAppointment
                                            : '--'}
                                    </StyledText>
                                </Grid>
                                <Grid item xs={6}>
                                    <StyledLabel>
                                        {t('appointments.customerHistory.notes')}
                                    </StyledLabel>
                                </Grid>
                                <Grid item xs={6}>
                                    <>
                                        <NotesContainer isExpandedNotes={isExpandedNotes}>
                                            {customerHistory.lastOrderInfo.notes
                                                ? customerHistory.lastOrderInfo.notes
                                                : '--'}
                                        </NotesContainer>
                                        {customerHistory.lastOrderInfo.notes &&
                                            customerHistory.lastOrderInfo.notes.length > 78 && (
                                                <SeeMoreButton
                                                    sx={{ paddingTop: '10px' }}
                                                    onClick={() =>
                                                        setIsExpandedNotes(!isExpandedNotes)
                                                    }
                                                >
                                                    {isExpandedNotes
                                                        ? t('appointments.customerHistory.seeLess')
                                                        : t('appointments.customerHistory.seeMore')}
                                                </SeeMoreButton>
                                            )}
                                    </>
                                </Grid>
                            </Grid>
                        </CollapsibleSection>
                        <StyledDivider variant="middle" />
                        <CollapsibleSection
                            title={t('appointments.customerHistory.itemsNotApproved')}
                            isCollapse
                        >
                            <CollapsibleSection
                                title={
                                    <RepairTitle>
                                        {`${t('appointments.customerHistory.urgent')} (`}
                                        <TotalTitle>{urgentItemsTotal}</TotalTitle>
                                        {')'}
                                        <Priority
                                            style={{
                                                backgroundColor: theme.palette.error.light,
                                            }}
                                        >
                                            {urgentItems.length}
                                        </Priority>
                                    </RepairTitle>
                                }
                                disabled={urgentItems.length === 0}
                                isTitleComponent
                                isCollapse
                                isChildElement
                            >
                                <Grid
                                    container
                                    spacing="10px"
                                    direction="row"
                                    justifyContent="center"
                                >
                                    {urgentItems.map((x) => (
                                        <>
                                            <StyledGrid item xs={6}>
                                                <RepairItem>{`- ${x.repairName}`}</RepairItem>
                                            </StyledGrid>
                                            <StyledGrid item xs={6}>
                                                <Estimate>
                                                    {x.subtotal === 0 ? '$-' : `$${x.subtotal}`}
                                                </Estimate>
                                            </StyledGrid>
                                        </>
                                    ))}
                                </Grid>
                            </CollapsibleSection>
                            <CollapsibleSection
                                title={
                                    <RepairTitle>
                                        {`${t('appointments.customerHistory.suggested')} (`}
                                        <TotalTitle>{mediumItemsTotal}</TotalTitle>
                                        {')'}
                                        <Priority
                                            style={{
                                                backgroundColor: theme.palette.warning.light,
                                            }}
                                        >
                                            {mediumItems.length}
                                        </Priority>
                                    </RepairTitle>
                                }
                                disabled={mediumItems.length === 0}
                                isTitleComponent
                                isCollapse
                                isChildElement
                            >
                                <Grid
                                    container
                                    spacing="10px"
                                    direction="row"
                                    justifyContent="center"
                                >
                                    {mediumItems.map((x) => (
                                        <>
                                            <StyledGrid item xs={6}>
                                                <RepairItem>{`- ${x.repairName}`}</RepairItem>
                                            </StyledGrid>
                                            <StyledGrid item xs={6}>
                                                <Estimate>
                                                    {x.subtotal === 0 ? '$-' : `$${x.subtotal}`}
                                                </Estimate>
                                            </StyledGrid>
                                        </>
                                    ))}
                                </Grid>
                            </CollapsibleSection>
                        </CollapsibleSection>
                    </StyledScrollable>
                </>
            ) : (
                <>
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            alignItems: 'center',
                            padding: '20px',
                        }}
                    >
                        <IconButton size="small" onClick={onClose}>
                            <CloseIcon fill={theme.palette.neutral[8]} />
                        </IconButton>
                    </Box>

                    <Box
                        sx={{
                            height: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            padding: '0 20px',
                        }}
                    >
                        <LabelNotFoundData>
                            {t('appointments.customerHistory.noPreviousOrders')}
                        </LabelNotFoundData>
                    </Box>
                </>
            )}
        </StyledCustomerHistory>
    );
}

function getPriorityIndex(priority: PriorityLevel): number {
    switch (priority) {
        case PriorityLevel.Low:
            return 0;
        case PriorityLevel.Med:
            return 1;
        case PriorityLevel.Urgent:
            return 2;
        case PriorityLevel.NA:
            return 3;
    }
}

const LabelNotFoundData = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[8],
    textAlign: 'center',
}));

const StyledCustomerHistory = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '0px 4px 16px 0px rgba(130, 130, 130, 0.25)',
    backgroundColor: theme.palette.neutral[1],
    width: 430,
    zIndex: 1,
}));

const StyledFirstHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    backgroundColor: theme.palette.neutral[8],
    paddingLeft: 24,
    paddingRight: 16,
    paddingTop: 10,
    paddingBottom: 8,
}));

const StyledHeaderContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
});

const StyledCustomerName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    fontSize: 18,
}));

const StyledScrollable = styled('div')({
    overflowY: 'auto',
    flexGrow: 1,
    paddingBottom: '20px',
});

const StyledGrid = styled(Grid)(({ theme }) => ({
    border: 'solid',
    borderWidth: '0 0 1px 0',
    borderColor: theme.palette.grey[300],
}));

const OrderListButonContainer = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
});

const OrderListItem = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.grey[600],
    fontWeight: 'normal',
    paddingBottom: '5px',
}));

const StyledLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.grey[600],
    fontWeight: 'bold',
    paddingLeft: '10px',
}));

const StyledText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.grey[600],
    fontWeight: 'normal',
    wordWrap: 'break-word',
}));

const NotesContainer = styled('div')<{ isExpandedNotes: boolean }>(
    ({ theme, isExpandedNotes }) => ({
        ...theme.typography.h6Inter,
        height: !isExpandedNotes ? '45px' : undefined,
        textOverflow: !isExpandedNotes ? 'ellipsis' : undefined,
        color: theme.palette.grey[600],
        fontWeight: 'normal',
        overflow: 'hidden',
        display: '-webkit-box',
        '-webkit-line-clamp': !isExpandedNotes ? '3' : undefined,
        '-webkit-box-orient': 'vertical',
    })
);

const RepairTitle = styled('div')({
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    width: '100%',
});

const Priority = styled('div')(({ theme }) => ({
    width: 22,
    height: 22,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '50%',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[1],
    textAlign: 'center',
    position: 'absolute',
    right: 0,
}));

const TotalTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.primary.light,
    fontWeight: 'bold',
}));

const RepairItem = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.grey[600],
    fontWeight: 'normal',
    paddingBottom: '5px',
}));

const Estimate = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'end',
    paddingRight: '30px',

    ...theme.typography.h6Inter,
    color: theme.palette.grey[600],
    fontWeight: 'normal',
    paddingBottom: '5px',
}));

const SeeMoreButton = styled('div')(({ theme }) => ({
    fontWeight: 'normal !important',
    color: theme.palette.primary.light,
    cursor: 'pointer',

    '&:hover': {
        textDecoration: 'underline',
    },
}));

const StyledDivider = styled(Divider)({
    margin: '0 16px !important',
});
