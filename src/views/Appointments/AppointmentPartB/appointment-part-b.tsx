import { Switch } from '@mui/material';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useQuery } from '@tanstack/react-query';
import AppointmentNotesApi, { AppointmentNoteDto } from 'api/appointmentNotes';
import AppointmentsApi, { AppointmentOriginDto, AppointmentStatus } from 'api/appointments';
import { TimeSpan } from 'api/utils/format';
import clsx from 'clsx';
import InfoTooltip from 'common/components/InfoTooltip';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import { isEqual } from 'lodash';
import moment from 'moment';
import { memo, useEffect, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { appointmentsActions, updateAppointmentData } from 'store/slices/appointments';
import { selectEditData } from 'store/slices/appointments/selectors';
import { useCurrentUser } from 'store/slices/user';
import { useDebounce } from 'use-debounce';
import { isDateValid } from 'utils';
import DateFormField from '../../../common/components/Inputs/DateFormField';
import TextFormField from '../../../common/components/Inputs/TextField';
import Radio from '../../../common/components/Radio';
import { Colors } from '../../../common/styles/Colors';
import { AppointmentStatusEnum } from '../../../datacontracts/Scheduler';
import { useRepairShopAppointmentSettings } from '../common';
import AppointmentFiles from '../common/AppointmentFiles';
import AppointmentNotes from './AppointmentNotes';
import OriginsSelector from './OriginsSelector';

export type PreviewProps = {
    conversation: ConversationDto;
};

const selectPartBState = createSelector(
    selectEditData,
    ({ appointmentData: { observations, number, origin, id, status, withValetService } }) => ({
        status,
        id,
        observations,
        origin,
        number,
        withValetService,
    })
);

const AppointmentPartB = ({
    disableAppointmentNumber = false,
}: {
    disableAppointmentNumber?: boolean;
}) => {
    const dispatch = useAppDispatch();
    const appointmentSettings = useRepairShopAppointmentSettings();
    const { t } = useAppTranslation();
    const user = useCurrentUser();
    const toaster = useToasters();

    const [appointmentNumber, setAppointmentNumber] = useState<string>('');

    const [newNoteIdentity, setNewNoteIdentity] = useState<number>(0);
    const appointmentData = useAppSelector(selectPartBState, isEqual);
    const origin = appointmentData.origin;
    const setOrigin = (origin: AppointmentOriginDto) =>
        dispatch(appointmentsActions.setOrigin(origin));
    const [appointmentNotes, setAppointmentNotes] = useState<AppointmentNoteDto[]>([]);

    const [isAppointmentNumberInUse, setIsAppointmentNumberInUse] = useState(false);
    const [debouncedNumber] = useDebounce(appointmentNumber, 300);

    useEffect(() => {
        if (!debouncedNumber?.trim()) {
            setIsAppointmentNumberInUse(false);
            return;
        }

        const checkIsAppointmentNumberInUse = async () => {
            const res = await AppointmentsApi.isAppointmentNumberInUse(debouncedNumber);
            setIsAppointmentNumberInUse(res);
        };

        checkIsAppointmentNumberInUse();
    }, [debouncedNumber]);

    useEffect(() => {
        if (isAppointmentNumberInUse && !appointmentData.id) {
            toaster.warning(
                t('appointments.weSuggestUsingAnotherNumber'),
                t('appointments.appointmentNumberAlreadyTaken')
            );
        }
    }, [isAppointmentNumberInUse, t, toaster, debouncedNumber, appointmentData.id]);

    const { data, refetch } = useQuery(
        ['appointmentNotes', appointmentData?.id],
        () => AppointmentNotesApi.getNotes(appointmentData?.id || ''),
        {
            enabled: Boolean(appointmentData?.id),
            staleTime: 30000, // 30 seconds
            cacheTime: Infinity,
        }
    );

    const handleAppointmentNotesChange = (notes: AppointmentNoteDto[]) => {
        setAppointmentNotes(notes);
        dispatch(appointmentsActions.setNotes(notes));
        if (Boolean(appointmentData?.id)) {
            refetch();
        }
    };

    useEffect(() => {
        if (data) {
            setAppointmentNotes(data);
        }
    }, [data]);

    useEffect(() => {
        if (appointmentData?.id) {
            if (appointmentData.number) {
                setAppointmentNumber(appointmentData.number.toUpperCase());
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appointmentData.id]);

    return (
        <Grid container spacing={0}>
            <Grid item xs={12}>
                <Box style={{ display: 'flex', alignItems: 'end' }}>
                    <WhoSchedules>{t('appointments.personWhoSchedules')}:</WhoSchedules>
                    <WhoSchedulesValue>{user.displayName}</WhoSchedulesValue>
                </Box>
                <Line />
                <Box>
                    <TextFormField
                        name="appointment-number"
                        cmosVariant="grey"
                        label={t('appointments.appointmentNumber')}
                        placeholder={
                            appointmentSettings?.activateAutomaticAppointmentNumber
                                ? t('appointments.itIsAssignedAutomatically')
                                : t('appointments.enterTheAppointmentNumber')
                        }
                        isRequired
                        showValidationIndicators
                        disabled={
                            disableAppointmentNumber !== undefined
                                ? disableAppointmentNumber
                                : !!appointmentData.id ||
                                  appointmentSettings?.activateAutomaticAppointmentNumber
                        }
                        value={appointmentNumber}
                        isInvalid={isAppointmentNumberInUse}
                        onChange={(event) => {
                            setAppointmentNumber(event.target.value.toUpperCase());
                            dispatch(
                                updateAppointmentData({
                                    appointment: { number: event.target.value },
                                })
                            );
                        }}
                    />
                </Box>
                <Line />
                {appointmentData?.status === 'Unconfirmed' ? null : (
                    <Box>
                        <Caption>
                            {t('appointments.status.appointmentStatus')}
                            <span style={{ color: Colors.CM1 }}> *</span>
                        </Caption>
                        <RadioGroup
                            defaultValue={'Unconfirmed'}
                            aria-label="status"
                            name="appointmentStatus"
                            value={appointmentData?.status || 'Unconfirmed'}
                            onChange={(_, value) => {
                                dispatch(
                                    updateAppointmentData({
                                        appointment: { status: value as AppointmentStatus },
                                    })
                                );
                            }}
                        >
                            <FormControlLabel
                                classes={{
                                    root: clsx(RadioLabelRoot),
                                    label: clsx(RadioLabel),
                                }}
                                disabled={
                                    !!appointmentData.id ||
                                    appointmentData?.status === 'OrderCreated'
                                }
                                value={AppointmentStatusEnum.Unconfirmed}
                                control={<Radio className="" />}
                                label={t('appointments.status.unconfirmed')}
                            />
                            <TooltipContainer>
                                <FormControlLabel
                                    classes={{
                                        root: clsx(RadioLabelRoot),
                                        label: clsx(RadioLabel),
                                    }}
                                    disabled={
                                        !!appointmentData.id ||
                                        appointmentData.status === 'OrderCreated'
                                    }
                                    value={AppointmentStatusEnum.Confirmed}
                                    control={<Radio className="" />}
                                    label={t('appointments.status.confirmed')}
                                />
                                <InfoTooltip text={t('appointments.status.confirmedDisabled')} />
                            </TooltipContainer>
                            <FormControlLabel
                                classes={{
                                    root: clsx(RadioLabelRoot),
                                    label: clsx(RadioLabel),
                                }}
                                value={AppointmentStatusEnum.CustomerArrived}
                                disabled={appointmentData.status === 'OrderCreated'}
                                control={<Radio className="" />}
                                label={t('appointments.status.customerArrived')}
                            />
                            <FormControlLabel
                                classes={{
                                    root: clsx(RadioLabelRoot),
                                    label: clsx(RadioLabel),
                                }}
                                value={AppointmentStatusEnum.CustomerDidNotArrive}
                                disabled={appointmentData.status === 'OrderCreated'}
                                control={<Radio className="" />}
                                label={t('appointments.status.customerDidNotArrive')}
                            />
                            <TooltipContainer>
                                <FormControlLabel
                                    classes={{
                                        root: clsx(RadioLabelRoot),
                                        label: clsx(RadioLabel),
                                    }}
                                    disabled={Boolean(appointmentData.id)}
                                    value={AppointmentStatusEnum.OrderCreated}
                                    control={<Radio className="" />}
                                    label={t('appointments.status.orderCreated')}
                                />
                                <InfoTooltip text={t('appointments.status.orderCreatedDisabled')} />
                            </TooltipContainer>
                        </RadioGroup>
                    </Box>
                )}
                <Box>
                    <OriginsSelector selected={origin} onSelected={setOrigin} />
                </Box>
                {appointmentSettings?.valetServiceEnabled ? (
                    <div>
                        <ValetServiceTopLine />
                        <ValetServiceContainer>
                            <SSwitch
                                checked={appointmentData.withValetService}
                                onChange={(_, checked) => {
                                    dispatch(
                                        updateAppointmentData({
                                            appointment: { withValetService: checked },
                                        })
                                    );
                                }}
                                name="checkedA"
                            />
                            <ValetServiceLabel>{t('appointments.valetService')}</ValetServiceLabel>
                        </ValetServiceContainer>
                        <ValetServiceBottomLine />
                    </div>
                ) : (
                    <Line />
                )}
                <Box>
                    <DeliveryDate />
                </Box>
                <Box
                    component="div"
                    sx={{
                        borderTop: '1px solid #EBEBEB',
                        width: '100%',
                        margin: '7px 0px 15px 0px',
                    }}
                />
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                    }}
                >
                    <AppointmentNotes
                        appointmentId={appointmentData.id}
                        notesType="ForCustomer"
                        newNoteIdentity={newNoteIdentity}
                        onAddNewNote={() => setNewNoteIdentity((identity) => identity + 1)}
                        notes={appointmentNotes}
                        onChange={handleAppointmentNotesChange}
                    />
                    <AppointmentNotes
                        appointmentId={appointmentData.id}
                        notesType="ForInternal"
                        newNoteIdentity={newNoteIdentity}
                        onAddNewNote={() => setNewNoteIdentity((identity) => identity + 1)}
                        notes={appointmentNotes}
                        onChange={handleAppointmentNotesChange}
                    />
                </Box>
                <Box
                    component="div"
                    sx={{
                        borderTop: '1px solid #EBEBEB',
                        width: '100%',
                        margin: '15px 0px 15px 0px',
                    }}
                />
                <Box>
                    <AppointmentFiles appointmentId={appointmentData.id} />
                </Box>
            </Grid>
        </Grid>
    );
};

const selectDeliveryDate = createSelector(selectEditData, (d) => d.appointmentData.deliverDate);

const DeliveryDate = memo(() => {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const deliveryDate = useAppSelector(selectDeliveryDate);
    const deliveryDateMoment = useMemo(
        () => (deliveryDate ? moment(`${deliveryDate.date} ${deliveryDate.time}`) : null),
        [deliveryDate]
    );
    const currentDate = new Date();

    return (
        <>
            <DateFormField
                disablePast
                clearable
                name="delivery-date"
                enableEnterComplete
                label={t('appointments.date')}
                variant="grey"
                onChange={(date) => {
                    if (date && isDateValid(date)) {
                        dispatch(
                            updateAppointmentData({
                                appointment: {
                                    deliverDate: date
                                        ? {
                                              time: deliveryDate?.time ?? '00:00:00',
                                              date: moment(date).format('YYYY-MM-DD'),
                                          }
                                        : null,
                                },
                            })
                        );
                    }
                }}
                value={deliveryDateMoment?.toDate()}
            />
            <TimeFormField
                name="delivery-hour"
                disablePast={
                    currentDate.getDay() === deliveryDateMoment?.day() &&
                    currentDate.getMonth() === deliveryDateMoment?.month()
                }
                cmosVariant="grey"
                label={t('appointments.time')}
                placeholder={t('appointments.selectAHour')}
                value={
                    deliveryDateMoment
                        ? [deliveryDateMoment.hour(), deliveryDateMoment.minute()]
                        : null
                }
                onChange={([h, m]) => {
                    dispatch(
                        updateAppointmentData({
                            appointment: {
                                deliverDate: {
                                    date: deliveryDate?.date ?? moment().format('YYYY-MM-DD'),
                                    time: TimeSpan.fromParts(h, m).toString(),
                                },
                            },
                        })
                    );
                }}
                slotProps={{
                    inputWrapper: {
                        className: clsx(TimePicker),
                    },
                }}
            />
        </>
    );
});

const WhoSchedules = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[8],
    marginRight: 5,
}));

const WhoSchedulesValue = styled(Typography)(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[8],
}));

const Line = styled('div')({
    borderTop: '1px solid #EBEBEB',
    width: '100%',
    margin: '15px 0px',
});

const ValetServiceTopLine = styled('div')({
    borderTop: '1px solid #EBEBEB',
    width: '100%',
    marginTop: 15,
    marginBottom: 3,
});

const ValetServiceBottomLine = styled('div')({
    borderTop: '1px solid #EBEBEB',
    width: '100%',
    marginBottom: 15,
    marginTop: 3,
});

const Caption = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
}));

const RadioLabelRoot = styled('div')({
    margin: 0,
});

const RadioLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
}));

const TooltipContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const TimePicker = styled('div')({
    paddingBottom: 8,
});

const ValetServiceContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 5,
});

const SSwitch = styled(Switch)(({ theme }) => ({
    '& .MuiSwitch-switchBase': {
        '&.Mui-checked': {
            color: theme.palette.neutral[1],
            '& .MuiSwitch-thumb:before': {
                color: theme.palette.neutral[1],
            },
            '& + .MuiSwitch-track': {
                opacity: 1,
            },
        },
        '& .MuiSwitch-thumb:before': {
            color: theme.palette.neutral[1],
        },
    },
}));

const ValetServiceLabel = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[8],
}));

export default memo(AppointmentPartB);
