import { Box, IconButton, InputAdornment } from '@mui/material';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import MuiTooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { AppointmentNoteDto } from 'api/appointmentNotes';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CustomerNoteIcon } from 'common/components/Icons/CustomerNoteIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { TeamMemberNoteIcon } from 'common/components/Icons/TeamMemberNoteIcon';
import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IconSize } from 'common/styles/IconSize';
import moment from 'moment';
import { useMemo, useState } from 'react';

type NoteEditorProps = {
    appointmentNote: AppointmentNoteDto;
    onEdit?: (value: AppointmentNoteDto) => void;
    onDelete?: (value: AppointmentNoteDto) => void;
};

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const NotePopupContent = ({ appointmentNote }: NoteEditorProps) => {
    const { t } = useAppTranslation();
    const hasHistory = useMemo(
        () => appointmentNote.historyNotes && appointmentNote.historyNotes.length > 1,
        [appointmentNote.historyNotes]
    );

    return (
        <Box
            component="div"
            sx={{ maxWidth: '330px', minHeight: '30px', padding: '8px', background: '#F6F6F6' }}
        >
            {!hasHistory ? (
                <Typography
                    sx={{
                        fontFamily: 'Roboto',
                        fontSize: '12px',
                        fontStyle: 'normal',
                        fontWeight: 400,
                        lineHeight: 'normal',
                        color: '#5C6477',
                    }}
                >
                    {`${t('appointments.appointmentNotes.addedBy')} ${
                        appointmentNote.isFromCustomer
                            ? t('appointments.appointmentNotes.customer')
                            : appointmentNote.user?.name
                    } ${t('appointments.appointmentNotes.on')} ${moment(
                        appointmentNote.createdAt
                    ).format(t('dateFormats.shortDate'))} ${t(
                        'appointments.appointmentNotes.at'
                    )} ${moment(appointmentNote.createdAt).format(t('dateFormats.time'))}`}
                </Typography>
            ) : (
                <List sx={{ listStyleType: 'disc', padding: '8px' }}>
                    {appointmentNote.historyNotes &&
                        appointmentNote.historyNotes.map((historyNote, index) => (
                            <ListItem
                                key={historyNote.id}
                                sx={{ display: 'list-item', padding: 0, color: '#5C6477' }}
                            >
                                <Typography
                                    sx={{
                                        fontFamily: 'Roboto',
                                        fontSize: '12px',
                                        fontStyle: 'normal',
                                        fontWeight: 400,
                                        lineHeight: 'normal',
                                        color: '#5C6477',
                                    }}
                                >
                                    {`${t(
                                        `appointments.appointmentNotes.${
                                            index === 0 ? 'addedBy' : 'modifiedBy'
                                        }`
                                    )} ${
                                        appointmentNote.isFromCustomer
                                            ? t('appointments.appointmentNotes.customer')
                                            : appointmentNote.user?.name
                                    } ${t('appointments.appointmentNotes.on')} ${moment(
                                        historyNote.createdAt
                                    ).format(t('dateFormats.shortDate'))} ${t(
                                        'appointments.appointmentNotes.at'
                                    )} ${moment(historyNote.createdAt).format(
                                        t('dateFormats.time')
                                    )}: "${historyNote.note}"`}
                                </Typography>
                            </ListItem>
                        ))}
                </List>
            )}
        </Box>
    );
};

const NoteEditor = ({ appointmentNote, onDelete, onEdit }: NoteEditorProps) => {
    const { t } = useAppTranslation();
    const [show, setShow] = useState<boolean>(false);
    const [isEdition, setIsEdition] = useState<boolean>(false);
    const [note, setNote] = useState<string>(appointmentNote.note);
    const showActions = useMemo(
        () => !appointmentNote.isFromCustomer && show,
        [appointmentNote.isFromCustomer, show]
    );

    const handleSave = () => {
        setIsEdition(false);
        onEdit && onEdit({ ...appointmentNote, note });
    };

    const handleDelete = () => {
        setIsEdition(false);
        onDelete && onDelete(appointmentNote);
    };

    return isEdition ? (
        <Grid container spacing={1}>
            <Grid item xs={11}>
                <TextFormField
                    name="appointment-note"
                    label=""
                    placeholder={t('appointments.appointmentNotes.enterNote')}
                    value={note}
                    onChange={(event) => {
                        setNote(event.target.value);
                    }}
                    onEnterPress={() => {
                        handleSave();
                    }}
                    enableEnterComplete
                    cmosVariant="roundedGrey"
                    endAdornment={
                        <InputAdornment position="end">
                            <IconButton
                                size="small"
                                onClick={() => {
                                    handleSave();
                                }}
                            >
                                <CheckIcon size={IconSize.M} />
                            </IconButton>
                        </InputAdornment>
                    }
                />
            </Grid>
            <Grid item xs={1}>
                <Button
                    color="#899198"
                    cmosVariant={'typography'}
                    Icon={CloseIcon}
                    label=""
                    onClick={() => {
                        setIsEdition(false);
                    }}
                />
            </Grid>
        </Grid>
    ) : (
        <Grid container spacing={0} alignItems="center">
            <Grid item xs={1} sx={{ display: 'flex', alignItems: 'center', height: '24px' }}>
                <Tooltip
                    title={<NotePopupContent appointmentNote={appointmentNote} />}
                    placement="left"
                >
                    <Box component="div">
                        {appointmentNote.isFromCustomer ? (
                            <CustomerNoteIcon />
                        ) : (
                            <TeamMemberNoteIcon />
                        )}
                    </Box>
                </Tooltip>
            </Grid>
            <Grid
                item
                xs={11}
                onMouseOver={() => setShow(true)}
                onMouseOut={() => setShow(false)}
                display="flex"
                alignItems="center"
                sx={{
                    minHeight: '24px',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                }}
            >
                <Typography
                    component="div"
                    sx={(theme) => ({
                        ...theme.typography.h6Roboto,
                        fontWeight: 400,
                        color: appointmentNote.isFromCustomer ? '#899198' : '#5C6477',
                        inlineSize: 'auto',
                        overflowWrap: 'break-word',
                    })}
                >
                    {appointmentNote.note}
                </Typography>
                {showActions && (
                    <Box
                        display="flex"
                        justifyContent="flex-end"
                        sx={{
                            width: '100%',
                            position: 'absolute',
                            bottom: 0,
                        }}
                    >
                        <IconButton
                            size="small"
                            onClick={() => setIsEdition(true)}
                            sx={{ padding: 0 }}
                        >
                            <EditIcon fill="#ACB7C0" size={24} />
                        </IconButton>
                        <IconButton size="small" onClick={handleDelete} sx={{ padding: 0 }}>
                            <DeleteIcon fill="#ACB7C0" size={24} />
                        </IconButton>
                    </Box>
                )}
            </Grid>
        </Grid>
    );
};

export default NoteEditor;
