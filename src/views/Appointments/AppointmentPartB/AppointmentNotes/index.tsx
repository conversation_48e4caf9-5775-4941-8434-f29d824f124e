import { Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { useMutation } from '@tanstack/react-query';
import AppointmentNotesApi, { AppointmentNoteDto, AppointmentNoteType } from 'api/appointmentNotes';
import { JobTitle } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useCurrentUser } from 'store/slices/user';
import AppointmentNoteForm from 'views/Appointments/common/AppointmentNotesForm';
import NoteEditor from 'views/Appointments/common/AppointmentNotesForm/NoteEditor';

type AppointmentNoteFormProps = {
    appointmentId?: string;
    notesType: AppointmentNoteType;
    newNoteIdentity: number;
    onAddNewNote: () => void;
    notes?: AppointmentNoteDto[];
    onChange?: (notes: AppointmentNoteDto[]) => void;
};

const AppointmentNotes = ({
    notes = [],
    appointmentId,
    notesType,
    newNoteIdentity,
    onAddNewNote,
    onChange,
}: AppointmentNoteFormProps) => {
    const { t } = useAppTranslation();
    const user = useCurrentUser();
    const [noteId, setNoteId] = useState<string>();

    const { addNoteMutate } = useAddNoteMutation((data) => {
        onChange && onChange([...notes, data]);
    });

    const { updateNoteMutate } = useUpdateNoteMutation((data) => {
        onChange && onChange(notes.map((x) => (x.id === data.id ? data : x)));
    });

    const { deleteNoteMutate } = useDeleteNoteMutation(() => {
        onChange && onChange(notes.filter((x) => x.id !== noteId));
        setNoteId(undefined);
    });

    const handleSave = (note: string) => {
        if (appointmentId) {
            addNoteMutate({ note, appointmentId, noteType: notesType });
        } else {
            onChange &&
                onChange([
                    ...notes,
                    {
                        id: `${newNoteIdentity}-${appointmentId || user.key}`,
                        isFromCustomer: false,
                        type: notesType,
                        section: 'Appointments',
                        createdAt: new Date(),
                        note,
                        user: {
                            id: user.key,
                            name: user.name,
                            job: user.jobTitle ? (user.jobTitle as JobTitle) : '',
                        },
                        historyNotes: null,
                    },
                ]);
            onAddNewNote();
        }
    };

    const handleEdit = (note: AppointmentNoteDto) => {
        if (appointmentId) {
            updateNoteMutate({
                note: note.note,
                appointmentNoteId: note.id,
            });
        } else {
            const index = notes.findIndex((n) => n.id === note.id);
            if (index >= 0) {
                onChange &&
                    onChange(notes.map((x) => (x.id === note.id ? { ...x, note: note.note } : x)));
            }
        }
    };

    const handleDelete = (note: AppointmentNoteDto) => {
        if (appointmentId) {
            setNoteId(note.id);
            deleteNoteMutate({ appointmentNoteId: note.id });
        } else {
            onChange && onChange(notes.filter((x) => x.id !== note.id));
        }
    };

    const filteredNotes = notes.filter((x) => x.type === notesType);

    return (
        <Box component="div">
            <Typography
                component="div"
                sx={(theme) => ({
                    fontFamily: 'Inter',
                    fontSize: '12px',
                    fontStyle: 'normal',
                    fontWeight: 700,
                    lineHeight: 'normal',
                    color: theme.palette.neutral[8],
                    marginBottom: '5px',
                })}
            >
                {notesType === 'ForCustomer'
                    ? t('appointments.notesVisibleForCustomer')
                    : t('appointments.internalNotes')}
            </Typography>
            {filteredNotes.map((note, index) => (
                <Box key={`${note.id}-${index}`} component="div">
                    <NoteEditor
                        appointmentNote={note}
                        editSection="Appointments"
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                </Box>
            ))}
            <Box sx={{ paddingTop: '3px' }}>
                <AppointmentNoteForm onClickSave={handleSave} />
            </Box>
        </Box>
    );
};

const useAddNoteMutation = (onSuccess?: (data: AppointmentNoteDto) => void) => {
    const { mutate: addNoteMutate } = useMutation(
        (body: { note: string; appointmentId: string; noteType: AppointmentNoteType }) =>
            AppointmentNotesApi.addNote(
                body.appointmentId,
                body.note,
                body.noteType,
                'Appointments'
            ),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { addNoteMutate };
};

const useUpdateNoteMutation = (onSuccess?: (data: AppointmentNoteDto) => void) => {
    const { mutate: updateNoteMutate } = useMutation(
        (body: { note: string; appointmentNoteId: string }) =>
            AppointmentNotesApi.updateNote(body.appointmentNoteId, body.note),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { updateNoteMutate };
};

const useDeleteNoteMutation = (onSuccess?: () => void) => {
    const { mutate: deleteNoteMutate } = useMutation(
        (body: { appointmentNoteId: string }) =>
            AppointmentNotesApi.deleteNote(body.appointmentNoteId),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );
    return { deleteNoteMutate };
};

export default AppointmentNotes;
