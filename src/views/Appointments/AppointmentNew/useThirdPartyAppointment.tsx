import { useCallback, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectAppointmentNumberAndId } from 'store/slices/appointments/selectors';
import { selectSyncAppointmentsThirdParty } from 'store/slices/globalSettingsSlice';
import { useRepairShopAppointmentSettings } from '../common';

export const useThirdPartyAppointment = () => {
    const [openModal, setOpenModal] = useState<boolean>(false);
    const [activeRequiredNumber, setActiveRequiredNumber] = useState<boolean>(false);
    const [skipOmnichannelThirdParty, setSkipOmnichannelThirdParty] = useState<boolean>(false);
    const [wasModalActivated, setWasModalActivated] = useState<boolean>(false);

    const isSyncAppointmentsThirdPartyEnabled = useAppSelector(selectSyncAppointmentsThirdParty);
    const activateAutomaticAppointmentNumber =
        useRepairShopAppointmentSettings()?.activateAutomaticAppointmentNumber;

    const [appointmentNumber, appointmentId] = useAppSelector(selectAppointmentNumberAndId);

    const handleClose = useCallback(() => {
        setOpenModal(false);
        setActiveRequiredNumber(false);
        setSkipOmnichannelThirdParty(false);
        setWasModalActivated(false);
    }, []);

    const handleShowModal = useCallback(() => {
        setOpenModal(true);
        setWasModalActivated(true);
    }, []);

    const handleSkipOmnichannelThirdParty = useCallback(
        (handleSave: (skipOmnichannel: boolean) => void) => {
            if (!activateAutomaticAppointmentNumber && !appointmentNumber) {
                setActiveRequiredNumber(true);
                setSkipOmnichannelThirdParty(true);
                setOpenModal(false);
            } else {
                handleSave(true);
            }
        },
        [activateAutomaticAppointmentNumber, appointmentNumber]
    );

    const isValid = useMemo(() => {
        if (isSyncAppointmentsThirdPartyEnabled) {
            return activeRequiredNumber && !activateAutomaticAppointmentNumber
                ? appointmentNumber !== ''
                : true;
        } else {
            return activateAutomaticAppointmentNumber;
        }
    }, [
        activateAutomaticAppointmentNumber,
        appointmentNumber,
        activeRequiredNumber,
        isSyncAppointmentsThirdPartyEnabled,
    ]);

    const disableAppointmentNumber = useMemo(() => {
        if (isSyncAppointmentsThirdPartyEnabled) {
            if (activeRequiredNumber) {
                return !!appointmentId || activateAutomaticAppointmentNumber;
            } else {
                return true;
            }
        } else {
            return !!appointmentId || activateAutomaticAppointmentNumber;
        }
    }, [
        activateAutomaticAppointmentNumber,
        activeRequiredNumber,
        appointmentId,
        isSyncAppointmentsThirdPartyEnabled,
    ]);

    return {
        isValid,
        isSyncAppointmentsThirdPartyEnabled,
        showModal: openModal,
        disableAppointmentNumber,
        skipOmnichannelThirdParty,
        wasModalActivated,

        handleClose,
        handleShowModal,
        handleSkipOmnichannelThirdParty,
    };
};
