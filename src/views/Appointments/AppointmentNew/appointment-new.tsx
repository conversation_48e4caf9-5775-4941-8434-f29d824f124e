import { Box, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useQuery } from '@tanstack/react-query';
import AppointmentsApi, { LocalDateObject } from 'api/appointments';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import { isAxiosError } from 'axios';
import { Button } from 'common/components/Button';
import CancelModal from 'common/components/Popups/CancelModal';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useQueryParam from 'common/hooks/useQueryParam';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { motion } from 'framer-motion';
import isEqual from 'lodash/isEqual';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { generatePath, useNavigate } from 'react-router-dom';
import { createSelector } from 'reselect';
import { hasCode, hasSubCode, isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { clearAppointmentAttachments } from 'store/slices/appointmentAttachment';
import { selectAppointmentAttachments } from 'store/slices/appointmentAttachment/selectors';
import {
    resetAppointmentData,
    setAppointmentDateTime,
    setFilters,
    setWeekSchedulerScroll,
    updateAppointmentData,
} from 'store/slices/appointments';
import { selectEditData } from 'store/slices/appointments/selectors';
import {
    selectRepairShopIntegrationAccountName,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import { useDebounce } from 'use-debounce';
import { v4 as uuidv4 } from 'uuid';
import AppointmentPartB from '../AppointmentPartB';
import AppointmentStepOne from '../AppointmentStepOne';
import AppointmentStepThree from '../AppointmentStepThree';
import AppointmentStepTwo from '../AppointmentStepTwo';
import { useAppointmentEditDataGetter, useIsAppointmentDataValid } from '../common';
import AppointmentNewHeader from '../common/AppointmentNewHeader';
import CustomerHistory from '../CustomerHistory';
import { useThirdPartyAppointment } from './useThirdPartyAppointment';

function usePreselectedDate(): LocalDateObject | undefined {
    const [date] = useQueryParam('date');
    const zone = useAppSelector(selectSettings).internationalization.ianaTzId;

    return useMemo(() => {
        if (!date || !zone) return undefined;
        const parsedDate = DateTime.fromFormat(date, 'yyyyMMddHHmm')
            .setZone(zone)
            .set({ second: 0, millisecond: 0 });
        return {
            time: parsedDate.toFormat('HH:mm:ss'),
            date: parsedDate.toFormat('yyyy-MM-dd'),
        };
    }, [date, zone]);
}

const selectNewAppointmentModified =
    appointmentEditorStateSelectors.createNewAppointmentModifiedSelector(selectEditData);
const selectCustomerVehicleModified =
    appointmentEditorStateSelectors.createAppointmentCustomerOrVehicleModifiedSelector(
        selectEditData
    );

const selectNewAppointmentData = createSelector(
    selectNewAppointmentModified,
    selectCustomerVehicleModified,
    (m, cv) => ({
        somethingModified: m,
        customerModified: cv.customer,
        vehicleModified: cv.vehicle,
    })
);

const AppointmentNew = () => {
    const navigate = useNavigate();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const {
        isSyncAppointmentsThirdPartyEnabled,
        showModal: showThirdPartyModal,
        isValid: isAppointmentNumberValid,
        disableAppointmentNumber,
        skipOmnichannelThirdParty,
        wasModalActivated,

        handleShowModal: handleShowThirdPartyModal,
        handleClose: closeThirdPartyModal,
        handleSkipOmnichannelThirdParty,
    } = useThirdPartyAppointment();

    const isOmnichannelAppointmentsEnabled =
        useAppSelector(selectSettings).repairShopSettings?.features
            .isOmnichannelAppointmentsEnabled;
    const { customerModified, vehicleModified, somethingModified } = useAppSelector(
        selectNewAppointmentData,
        isEqual
    );

    const { vehicle, customer, appointmentData } = useAppSelector(selectEditData);

    const appointmentNumber = appointmentData.number;
    const [debouncedNumber] = useDebounce(appointmentNumber, 300);
    const [isAppointmentNumberInUse, setIsAppointmentNumberInUse] = useState(false);

    useEffect(() => {
        if (!debouncedNumber?.trim()) {
            setIsAppointmentNumberInUse(false);
            return;
        }

        const checkAppointmentNumberInUse = async () => {
            const result = await AppointmentsApi.isAppointmentNumberInUse(debouncedNumber);
            setIsAppointmentNumberInUse(result);
        };

        checkAppointmentNumberInUse();
    }, [debouncedNumber]);

    const isValid = useIsAppointmentDataValid(isAppointmentNumberInUse, isAppointmentNumberValid);
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);

    const [openCancelModal, setOpenCancelModal] = useState<boolean>(false);
    const [openConfirmationModal, setOpenConfirmationModal] = useState<boolean>(false);
    const preselectedDate = usePreselectedDate();

    const [isWarningModalOpen, setIsWarningModalOpen] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>('');
    const OMNICHANNEL_CREATE = 'omnichannelCreate';

    const [advisorQueryParam] = useQueryParam('advisor');
    const [orderIdQueryParam] = useQueryParam('orderId');
    const [customerQueryParam] = useQueryParam('customerId');
    const [vehicleQueryParam] = useQueryParam('vehicleId');
    const [masterListItemIdsQueryParam] = useQueryParam('masterListItemIds');
    const [inChargeUserIdQueryParam] = useQueryParam('inChargeUserId');
    const masterListItemIds = useMemo(() => {
        if (masterListItemIdsQueryParam && masterListItemIdsQueryParam !== '') {
            return masterListItemIdsQueryParam.indexOf(',') !== -1
                ? masterListItemIdsQueryParam.split(',').map((x) => Number(x))
                : [Number(masterListItemIdsQueryParam)];
        }
        return [];
    }, [masterListItemIdsQueryParam]);
    const inChargeUserId = useMemo(
        () => (inChargeUserIdQueryParam ? Number(inChargeUserIdQueryParam) : null),
        [inChargeUserIdQueryParam]
    );
    const isPrefilled = !!customerQueryParam;

    const enabledCustomerHistory = !!customer && !!vehicle;

    const [detailsVisibility, setDetailsVisibility] = useState<'visible' | 'hidden'>('hidden');

    const ready = useRef(false);
    useEffect(() => {
        if (ready.current) return;
        ready.current = true;
        dispatch(resetAppointmentData());
        if (advisorQueryParam) {
            dispatch(
                updateAppointmentData({
                    appointment: {
                        serviceAdvisorId: advisorQueryParam,
                    },
                })
            );
        }
        if (preselectedDate) {
            dispatch(setAppointmentDateTime(preselectedDate));
        }
    }, [dispatch, advisorQueryParam, preselectedDate]);

    useQuery(
        ['appointments', 'prefilled-data', customerQueryParam],
        () =>
            AppointmentsApi.getAppointmentPrefilledData(
                customerQueryParam!,
                masterListItemIds,
                vehicleQueryParam,
                inChargeUserId
            ),
        {
            enabled: isPrefilled,
            onSuccess(data) {
                dispatch(
                    updateAppointmentData({
                        customer: data.customer
                            ? {
                                  id: data.customer.customerId,
                                  firstName: data.customer.firstName,
                                  lastName: data.customer.lastName,
                                  mobile: data.customer.mobile,
                                  taxIdentification: data.customer.taxIdentification,
                                  email: data.customer.email,
                              }
                            : null,

                        vehicle: data.vehicle
                            ? {
                                  brand: data.vehicle.brand,
                                  color: data.vehicle.color,
                                  id: data.vehicle.vehicleId,
                                  model: data.vehicle.model,
                                  plates: data.vehicle.plates,
                                  vin: data.vehicle.vin,
                                  year: data.vehicle.year,
                              }
                            : null,

                        appointment: {
                            customerEmail: data.customer.email,
                            customerFirstName: data.customer.firstName,
                            customerLastName: data.customer.lastName,
                            customerId: data.customer.customerId,
                            customerMobile: data.customer.mobile,
                            customerTaxId: data.customer.taxIdentification,
                            vehicleBrand: data.vehicle ? data.vehicle.brand : '',
                            vehicleColor: data.vehicle ? data.vehicle.color : '',
                            vehicleId: data.vehicle ? data.vehicle.vehicleId : '',
                            vehicleModel: data.vehicle ? data.vehicle.model : '',
                            vehiclePlates: data.vehicle ? data.vehicle.plates : '',
                            vehicleVin: data.vehicle ? data.vehicle.vin : '',
                            vehicleYear: data.vehicle ? data.vehicle.year : '',
                            // NOTE (MB) we remove other props and only leave id, name and color
                            reasons: data.reasons.map(({ id, name, color }) => ({
                                id,
                                name,
                                color,
                            })),
                            declinedItemReasonsForView: data.declinedItemReasonsForView.map(
                                ({ id, name }) => ({
                                    id,
                                    name,
                                })
                            ),
                            serviceAdvisorId: data.serviceAdvisorKey ?? advisorQueryParam,
                        },
                    })
                );
            },
        }
    );

    useEffect(
        () => () => {
            dispatch(resetAppointmentData());
        },
        [dispatch]
    );

    const handleCancel = () => {
        if (orderIdQueryParam) {
            navigate(generatePath(ROUTES.ORDERS_DETAIL, { id: orderIdQueryParam }));
        } else {
            navigate(`${ROUTES.APPOINTMENTS.BASE}`);
        }
    };

    const toggleCustomerHistory = () =>
        detailsVisibility === 'hidden'
            ? setDetailsVisibility('visible')
            : setDetailsVisibility('hidden');

    const getEditData = useAppointmentEditDataGetter();
    const files = useSelector(selectAppointmentAttachments);
    const savingRef = useRef(false);
    const [isSaving, setSaving] = useState(false);
    const saveAppointment = async (skipOmnichannel?: boolean) => {
        setSaving(true);
        if (savingRef.current) return;
        savingRef.current = true;
        try {
            const appointment = getEditData().appointmentData;
            if (!appointment.customerId) throw new Error('customerId is required');

            const appointmentId = uuidv4();
            await AppointmentsApi.create(
                {
                    reasons: appointment.reasons.map((r) => r.id),
                    declinedItemReasonsForView: appointment.declinedItemReasonsForView,
                    customReasons: appointment.customReasons,
                    observations: appointment.observations,
                    promisedAt: appointment.deliverDate,
                    startsAt: appointment.startsAt,
                    originId: appointment.origin?.id ?? null,
                    withValetService: appointment.withValetService,
                    serviceAdvisorId: appointment.serviceAdvisorId,
                    number: appointment.number,
                    duration: null,
                    customer: {
                        id: appointment.customerId,
                        firstName: appointment.customerFirstName,
                        lastName: appointment.customerLastName,
                        email: appointment.customerEmail,
                        taxIdentification: appointment.customerTaxId,
                        mobile: appointment.customerMobile,
                    },
                    vehicle: appointment.vehicleId
                        ? {
                              id: appointment.vehicleId,
                              vin: appointment.vehicleVin,
                              brand: appointment.vehicleBrand,
                              model: appointment.vehicleModel,
                              year: appointment.vehicleYear,
                              color: appointment.vehicleColor,
                              plates: appointment.vehiclePlates,
                          }
                        : null,
                    notes: appointment.notes,
                },
                appointmentId,
                skipOmnichannel || skipOmnichannelThirdParty
            );

            const uploadFiles = files.filter(
                (attachment): attachment is File => attachment instanceof File
            );

            if (uploadFiles.length > 0) {
                await AppointmentsApi.uploadAttachments(uploadFiles, appointmentId);
            }

            dispatch(clearAppointmentAttachments());
            dispatch(setWeekSchedulerScroll(scrollToTime(appointment.startsAt.time)));
            dispatch(
                setFilters({
                    date: appointment.startsAt.date,
                })
            );

            toasters.success(
                isSyncAppointmentsThirdPartyEnabled
                    ? t('appointments.appointmentCreatedInYourIntegratedSoftwareFirst', {
                          integratedAccountName,
                      })
                    : `${appointment.customerFirstName} ${
                          appointment.customerLastName || ' '
                      } - ${moment(
                          `${appointment.startsAt.date} ${appointment.startsAt.time}`
                      ).format(t('dateFormats.midDate'))}`,
                t('appointments.appointmentCreated')
            );

            if (
                isOmnichannelAppointmentsEnabled &&
                !isSyncAppointmentsThirdPartyEnabled &&
                !skipOmnichannel
            ) {
                toasters.success(t('appointments.appointmentCreatedInYourIntegratedSoftware'));
            }

            navigate(`${ROUTES.APPOINTMENTS.BASE}`);
        } catch (e) {
            if (isAxiosError(e) && isErrorResponse(e.response?.data)) {
                const errorData = e.response!.data;
                if (hasCode(errorData, 'Appointments.InvalidAppointmentNumber')) {
                    toasters.danger(
                        t('appointments.appointmentNumberIsDuplicated'),
                        t('toasters.errorOccurred')
                    );
                } else if (hasSubCode(errorData, 'ValidationError', 'InvalidMobileNumberError')) {
                    toasters.danger(
                        t('appointments.invalidMobileNumberError'),
                        t('toasters.errorOccurred')
                    );
                } else {
                    if (isSyncAppointmentsThirdPartyEnabled) {
                        if (!wasModalActivated) handleShowThirdPartyModal();
                        toasters.danger(
                            t(
                                'appointments.omnichannelModal.appointmentNotCreatedInYourIntegratedSoftwareFirstMessage',
                                {
                                    errorMessage: errorData.message,
                                }
                            ),
                            t(
                                'appointments.omnichannelModal.appointmentNotCreatedInYourIntegratedSoftwareFirstTitle',
                                {
                                    integratedAccountName,
                                }
                            )
                        );
                    } else if (
                        isOmnichannelAppointmentsEnabled &&
                        !isSyncAppointmentsThirdPartyEnabled
                    ) {
                        setIsWarningModalOpen(true);
                        setErrorMessage(errorData.message);
                        toasters.danger(
                            t(
                                'appointments.omnichannelModal.appointmentNotCreatedInYourIntegratedSoftware'
                            )
                        );
                    } else {
                        toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                    }
                }
            } else {
                console.warn('unknown error occurred while updating the entry: ', e);
            }
        } finally {
            setSaving(false);
            savingRef.current = false;
            toasters.dismiss(OMNICHANNEL_CREATE);
        }
    };

    useDocumentTitle(t('titles.appointments'));

    return (
        <CustomersSearchApiProvider mode="shop">
            <Grid
                container
                spacing={0}
                justifyContent="center"
                style={{ zIndex: 1, overflow: 'hidden' }}
            >
                <StyledGrid item xs={11}>
                    <Grid container spacing={0} justifyContent="center">
                        <Grid item xs={11}>
                            <AppointmentNewHeader
                                loading={isSaving}
                                modified={somethingModified}
                                valid={isValid ?? false}
                                onCancel={handleCancel}
                                onSave={() => {
                                    if (!(customerModified || vehicleModified)) {
                                        saveAppointment();
                                    } else {
                                        setOpenConfirmationModal(true);
                                    }
                                }}
                                enabledCustomerHistory={enabledCustomerHistory}
                                toggleCustomerHistory={toggleCustomerHistory}
                            />
                            <Grid container spacing={2} style={{ marginTop: 25 }}>
                                <Grid item xs={12} md={9} style={{ overflow: 'hidden' }}>
                                    <StyledPartA>
                                        <AppointmentStepOne />
                                        <div
                                            style={{
                                                marginTop: 30,
                                                marginBottom: 30,
                                                borderTop: `1px solid #EBEBEB`,
                                                width: '100%',
                                            }}
                                        />
                                        <AppointmentStepTwo />
                                        <div
                                            style={{
                                                marginTop: 30,
                                                marginBottom: 30,
                                                borderTop: `1px solid #EBEBEB`,
                                                width: '100%',
                                            }}
                                        />
                                        <AppointmentStepThree />
                                    </StyledPartA>
                                </Grid>
                                <Grid item xs={12} md={3}>
                                    <AppointmentPartB {...{ disableAppointmentNumber }} />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </StyledGrid>
            </Grid>
            <ConfirmationModal
                open={openConfirmationModal}
                cancelCaptionButton={t('appointments.cancel')}
                confirmationCaptionButton={t('appointments.updateCustomer')}
                onClose={() => setOpenConfirmationModal(false)}
                onConfirmation={() => {
                    setOpenConfirmationModal(false);
                    saveAppointment();
                }}
            >
                {t('appointments.confirmCustomerUpdateText')}
            </ConfirmationModal>
            <CancelModal
                open={openCancelModal}
                title={t('appointments.cancelNewAppointmentRegister')}
                onCancel={handleCancel}
                onClose={() => setOpenCancelModal(false)}
            />
            <WarningConfirmationPopup
                open={isWarningModalOpen}
                title={t(
                    'appointments.omnichannelModal.appointmentNotCreatedInYourIntegratedSoftware'
                )}
                body={
                    <StyledTextContent>
                        {t('appointments.omnichannelModal.reasonForErrorInYourIntegratedSoftware')}:
                        "{errorMessage}"
                    </StyledTextContent>
                }
                cancel={t('appointments.omnichannelModal.retryApptCreation')}
                confirm={t('appointments.omnichannelModal.createApptInClearmechanic')}
                onConfirm={() => {
                    setIsWarningModalOpen(false);
                    saveAppointment(true);
                }}
                onClose={() => {
                    setIsWarningModalOpen(false);
                }}
                onCancel={() => {
                    setIsWarningModalOpen(false);
                    toasters.progress(
                        t(
                            'appointments.omnichannelModal.weAreCreatingTheAppointmentInYourIntegratedSOftware'
                        ),
                        t('appointments.omnichannelModal.oneMomentPlease'),
                        { id: OMNICHANNEL_CREATE }
                    );
                    saveAppointment();
                }}
            />
            <WarningConfirmationPopup
                open={showThirdPartyModal}
                title={t('appointments.omnichannelModal.appointmentThirdPartyModalTitle')}
                body={
                    <StyledTextContent sx={{ lineHeight: '20px' }}>
                        <Box sx={{ marginBottom: '4px' }}>
                            {t(
                                'appointments.omnichannelModal.appointmentThirdPartyModalBodyMessage'
                            )}
                        </Box>
                        <Box>
                            {t(
                                'appointments.omnichannelModal.appointmentThirdPartyModalBodyQuestion',
                                {
                                    integratedAccountName,
                                }
                            )}
                        </Box>
                    </StyledTextContent>
                }
                onClose={() => {
                    if (!isSaving) closeThirdPartyModal();
                }}
                actionsContent={
                    <Box
                        sx={{
                            marginTop: '20px',
                            display: 'flex',
                            justifyContent: 'center',
                            gap: '5px',
                        }}
                    >
                        <Button
                            disabled={isSaving}
                            color={Colors.Neutral3}
                            cmosVariant={'filled'}
                            onClick={() => {
                                saveAppointment();
                            }}
                            sx={{
                                width: '131px',
                            }}
                            label={t('appointments.omnichannelModal.appointmentThirdPartyRetry')}
                        />
                        <Button
                            disabled={isSaving}
                            color={Colors.Neutral3}
                            cmosVariant={'filled'}
                            onClick={closeThirdPartyModal}
                            sx={{
                                width: '131px',
                            }}
                            label={t('appointments.omnichannelModal.appointmentThirdPartyCancel')}
                        />
                        <Button
                            showLoader={isSaving}
                            disabled={isSaving}
                            color={Colors.Warning}
                            cmosVariant={'filled'}
                            sx={{
                                width: '131px',
                            }}
                            onClick={() => {
                                handleSkipOmnichannelThirdParty(saveAppointment);
                            }}
                            label={t('appointments.omnichannelModal.appointmentThirdPartyConfirm')}
                        />
                    </Box>
                }
            />
            <MotionPreview
                animate={detailsVisibility}
                variants={{
                    visible: { right: '0px', opacity: 1 },
                    hidden: { right: '-430px', opacity: 0 },
                }}
                transition={{ duration: 0.15, ease: 'easeOut' }}
            >
                <CustomerHistory
                    onClose={() => {
                        setDetailsVisibility('hidden');
                    }}
                />
            </MotionPreview>
        </CustomersSearchApiProvider>
    );
};

const StyledGrid = styled(Grid)(({ theme }) => ({
    border: '1px solid #DBDCDD',
    borderRadius: 12,
    paddingTop: 35,
    marginTop: 20,
    height: '100%',
    background: theme.palette.neutral[1],
}));

const StyledPartA = styled(Grid)({
    padding: 10,
    maxHeight: 'calc(100vh - 245px)',
    overflowY: 'scroll',
    overflowX: 'hidden',
    ...scrollbarStyle(),
});

const StyledTextContent = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    padding: '0px 10px',
}));

const MotionPreview = motion(
    styled('div')({
        zIndex: 3,
        height: 'calc(100% - var(--header-bar-height))',
        display: 'flex',
        position: 'fixed',
        top: 52,
        right: 0,
    })
);

const scrollToTime = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
};

export default AppointmentNew;
