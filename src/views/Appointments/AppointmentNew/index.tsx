import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { PermissionsGate } from 'views/Components/AuthorizationGate';
import AppointmentNewInner from './appointment-new';

export default function AppointmentNew() {
    const { t } = useAppTranslation();

    return (
        <PermissionsGate
            predicate={['allowEditAppointments', 'allowSeeAppointments']}
            text={t('appointments.accessDeniedText')}
            hint={t('appointments.accessDeniedHint')}
        >
            <AppointmentNewInner />
        </PermissionsGate>
    );
}
