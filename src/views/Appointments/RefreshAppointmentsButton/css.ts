import { makeStyles } from '@mui/styles';

const useRefreshAppointmentsButtonStyles = makeStyles((theme) => ({
    btn: {
        padding: 1,
    },
    icon: {},
    refreshing: {
        '& $icon': {
            animation: '$rotate 1.8s linear infinite',
        },
    },
    '@keyframes rotate': {
        '0%': {
            transform: 'rotate(0deg)',
        },
        '100%': {
            transform: 'rotate(360deg)',
        },
    },
}));

export default useRefreshAppointmentsButtonStyles;
