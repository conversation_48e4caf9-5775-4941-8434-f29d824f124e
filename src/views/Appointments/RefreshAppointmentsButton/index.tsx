import { IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentAPI from 'api/Appointment';
import clsx from 'clsx';
import { SyncUpIcon } from 'common/components/Icons/SyncUpIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback } from 'react';
import { useAppSelector } from 'store';
import {
    selectRepairShopIntegrationAccountName,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import useRefreshAppointmentsButtonStyles from './css';

export type RefreshAppointmentsButtonProps = {
    onRefreshed: () => void;
    activeSuccessSync: () => void;
};

const TOASTER_ID = 'Appointments3rdPartyRefresh';
const delay = () => new Promise((resolve) => setTimeout(resolve, 1500));

export default function RefreshAppointmentsButton({
    onRefreshed,
    activeSuccessSync,
}: RefreshAppointmentsButtonProps) {
    const styles = useRefreshAppointmentsButtonStyles();
    const refreshEnabled = useAppSelector(selectSettings).repairShopSettings?.features.syncAppts;
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const refreshMutation = useMutation(
        () => Promise.all([AppointmentAPI.syncAppointmentsWithOpenApi(), delay()]),
        {
            onMutate: () => {
                toasters.progress(
                    t('appointments.gettingAppts3rdParty', { integratedAccountName }),
                    t('toasters.oneMoment'),
                    {
                        id: TOASTER_ID,
                    }
                );
            },
            onSuccess: () => {
                activeSuccessSync();
                onRefreshed();
            },
            onError: () => {
                toasters.danger(
                    t('appointments.get3rdParty.error', {
                        integratedAccountName,
                    }),
                    t('toasters.errorOccurred')
                );
            },
            onSettled: () => toasters.dismiss(TOASTER_ID),
        }
    );
    const onClick = useCallback(() => {
        if (refreshMutation.isLoading) return;
        refreshMutation.mutate();
    }, [refreshMutation]);

    if (!refreshEnabled) return null;

    return (
        <IconButton
            onClick={onClick}
            className={clsx(styles.btn, refreshMutation.isLoading && styles.refreshing)}
            size="large"
        >
            <SyncUpIcon className={styles.icon} size={30} />
        </IconButton>
    );
}
