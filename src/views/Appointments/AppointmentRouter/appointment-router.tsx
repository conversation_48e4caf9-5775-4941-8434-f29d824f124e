import { Navigate, Route, Routes } from 'react-router-dom';
import AppointmentEdit from '../AppointmentEdit';
import AppointmentList from '../AppointmentList';
import AppointmentNew from '../AppointmentNew';
import { ROUTES } from './../../../common/constants/RoutesDefinition';

const AppointmentRouter = () => {
    return (
        <Routes>
            <Route path="" element={<AppointmentList />} />
            <Route path="new" element={<AppointmentNew />} />
            <Route path=":appointmentId" element={<AppointmentEdit />} />
            <Route path="*" element={<Navigate replace to={ROUTES.APPOINTMENTS.BASE} />} />
        </Routes>
    );
};

export default AppointmentRouter;
