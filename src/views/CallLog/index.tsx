import { Box, Divider, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { PhoneCallListItem } from 'datacontracts/PhoneCallListItem';
import { useEffect, useRef, useState } from 'react';
import PhoneCall from './PhoneCall';

export type CallLogProps = {
    open: boolean;
    onClose: () => void;
    phoneCalls: PhoneCallListItem[];
    className?: string;
};

export default function CallLog({ open, onClose, phoneCalls, className }: CallLogProps) {
    const { t } = useAppTranslation();

    const [activePhoneCall, setActivePhoneCall] = useState<PhoneCallListItem | null>(null);

    const handlePlay = (item: PhoneCallListItem) => {
        setActivePhoneCall(item);
    };

    const handlePause = () => {
        setActivePhoneCall(null);
    };

    const handlePlayError = () => {
        setActivePhoneCall(null);
    };

    useEffect(() => {
        if (!open) {
            setActivePhoneCall(null);
        }
    }, [open]);

    const scrollbarParent = useRef<HTMLDivElement | null>(null);

    return (
        <StyledContainer style={{ visibility: open ? 'initial' : 'hidden' }} className={className}>
            <StyledHeading>
                <span style={{ ...FontPrimary(HeaderStyles.H5_14px, true, Colors.Black) }}>
                    {t('callLog')}
                </span>
                <Button
                    cmosVariant={'typography'}
                    cmosSize={'medium'}
                    iconPosition="right"
                    color={Colors.Neutral3}
                    Icon={CloseIcon}
                    onClick={onClose}
                />
            </StyledHeading>
            <Box
                sx={{ pt: 1, overflowY: 'auto', overscrollBehavior: 'contain' }}
                className="custom-scrollbar"
                ref={scrollbarParent}
            >
                {phoneCalls.map((phoneCall, index) => {
                    return (
                        <div key={index}>
                            <PhoneCall
                                phoneCall={phoneCall}
                                onPlay={handlePlay}
                                onPause={handlePause}
                                onPlayError={handlePlayError}
                                playing={activePhoneCall === phoneCall}
                                isOpen={open}
                            />
                            {index < phoneCalls.length - 1 && (
                                <div style={{ paddingBottom: 18 }}>
                                    <Divider
                                        orientation="horizontal"
                                        style={{ marginLeft: 5, marginRight: 11 }}
                                    />
                                </div>
                            )}
                        </div>
                    );
                })}
            </Box>
        </StyledContainer>
    );
}

const StyledContainer = styled('div')({
    paddingLeft: 24,
    paddingRight: 16,
    paddingTop: 16,
    paddingBottom: 8,
    width: 259,
    height: 236,
    backgroundColor: '#ffffff',
    boxShadow: '0px 4px 5px #c8c8c8',
    borderRadius: 24,
    position: 'fixed',
    right: 10,
    bottom: 10,
    zIndex: 10000,
    display: 'flex',
    flexDirection: 'column',
});

const StyledHeading = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.palette.neutral[1],
}));
