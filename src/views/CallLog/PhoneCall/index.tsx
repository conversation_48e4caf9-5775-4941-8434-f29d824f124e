import styled from '@emotion/styled';
import { Box, IconButton, useTheme } from '@mui/material';
import { formatTime } from 'common/Helpers/format';
import { CallRejectedIcon } from 'common/components/Icons/CallRejectedIcon';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { PlayIcon } from 'common/components/Icons/PlayIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { usePlayer } from 'common/hooks/usePlayer';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { IconSize } from 'common/styles/IconSize';
import { PhoneCallListItem } from 'datacontracts/PhoneCallListItem';
import moment from 'moment';
import { useEffect, useRef } from 'react';
import { PhoneCallSlider } from './PhoneCallSlider';

export type PhoneCallProps = {
    phoneCall: PhoneCallListItem;
    onPlay: (item: PhoneCallListItem) => void;
    onPause: () => void;
    onPlayError: () => void;
    playing: boolean;
    isOpen: boolean;
};

export default function PhoneCall({
    phoneCall,
    onPlay,
    onPause,
    onPlayError,
    playing,
    isOpen,
}: PhoneCallProps) {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const audioRef = useRef<HTMLAudioElement | null>(null);

    useEffect(() => {
        if (isOpen) {
            if (!audioRef.current) audioRef.current = new Audio(phoneCall.audioLink);
        } else audioRef.current?.pause();
    }, [isOpen]);

    const { currentTime, setCurrentTime, duration } = usePlayer({
        element: audioRef.current,
        playing,
        onPause,
        onPlayError,
    });

    const handleSliderChange = (_: unknown, newValue: number | number[]) => {
        setCurrentTime(newValue as number);
    };

    const getTime = () => formatTime(duration - currentTime);

    const handlePlay = () => {
        if (duration > 0) onPlay(phoneCall);
    };

    return (
        <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex' }}>
                {phoneCall.isSuccessful ? (
                    <PhoneIcon fill={Colors.Success} size={IconSize.M} />
                ) : (
                    <CallRejectedIcon fill={Colors.Error} size={IconSize.M} />
                )}
                <div style={{ marginTop: 5, marginLeft: 8 }}>
                    <div style={{ ...theme.typography.h6Inter, color: theme.palette.neutral[7] }}>
                        {phoneCall.displayName}
                    </div>
                    <div style={{ ...FontPrimary(HeaderStyles.H6_12px, false, Colors.Neutral7) }}>
                        {moment(phoneCall.callTime).format(t('dateFormats.long'))}
                    </div>
                </div>
            </Box>
            <StyledAudioContainer>
                {playing ? (
                    <StyledPlayPauseButton disableRipple onClick={onPause}>
                        <PauseIcon fill={Colors.CM1} size={IconSize.M} />
                    </StyledPlayPauseButton>
                ) : (
                    <StyledPlayPauseButton
                        disableRipple
                        onClick={handlePlay}
                        disabled={duration === 0}
                    >
                        <PlayIcon
                            fill={duration > 0 ? Colors.CM1 : Colors.Neutral5}
                            size={IconSize.M}
                        />
                    </StyledPlayPauseButton>
                )}
                <PhoneCallSlider
                    style={{ marginLeft: 14, marginRight: 14 }}
                    step={1}
                    value={currentTime}
                    max={duration}
                    onChange={handleSliderChange}
                    disabled={duration === 0}
                />
                <div
                    style={{
                        ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Neutral6),
                        marginRight: 17,
                    }}
                >
                    {getTime()}
                </div>
            </StyledAudioContainer>
        </Box>
    );
}

const StyledPlayPauseButton = styled(IconButton)({
    padding: 0,
    backgroundColor: 'transparent',
    border: 'none',
    height: 32,
    width: 32,

    '&:not([disabled])': {
        cursor: 'pointer',

        ':hover': {
            backgroundColor: 'var(--neutral3)',
        },
    },
    '&:focus': {
        outline: 0,
    },
});

const StyledAudioContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    marginTop: 11,
});
