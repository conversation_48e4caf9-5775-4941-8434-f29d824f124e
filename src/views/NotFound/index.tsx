import { Button } from 'common/components/Button';
import { ENTERPRISE_ROUTES, ROUTES, STATUS_PAGE_ROUTES } from 'common/constants/RoutesDefinition';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { useLocation, useNavigate } from 'react-router-dom';
import styles from './styles.module.css';

const NotFound = () => {
    const match = useLocation();
    const navigate = useNavigate();
    const { t } = useAppTranslation();

    useDocumentTitle(t('titles.notFound'));

    const goBack = () => {
        navigate(
            match.pathname.startsWith(ENTERPRISE_ROUTES.BASE)
                ? ENTERPRISE_ROUTES.LOGIN
                : ROUTES.LOGIN
        );
    };

    return (
        <div className={styles.mainContainer}>
            <div className={styles.image} />
            <span
                className={styles.haveProblem}
                style={{
                    ...FontPrimary(HeaderStyles.H1_34px, true, Colors.CM1),
                }}
            >
                {t('notFound.haveProblem')}
            </span>
            <span
                className={styles.weCanNotFind}
                style={{
                    ...FontPrimary(HeaderStyles.H3_21px, false, Colors.Neutral7),
                }}
            >
                {t('notFound.weCanNotFind')}
            </span>
            {!match.pathname.startsWith(STATUS_PAGE_ROUTES.BASE) && (
                <div style={{ marginTop: '32px', width: '185px' }}>
                    <Button
                        blockMode
                        label={t('notFound.goBack')}
                        cmosVariant={'filled'}
                        cmosSize={'large'}
                        onClick={goBack}
                    />
                </div>
            )}
        </div>
    );
};

export default NotFound;
