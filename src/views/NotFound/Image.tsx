import { useForkRef } from '@mui/material';
import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { forwardRef, lazy, useCallback, useEffect, useState } from 'react';

// because with the addition of the easter egg this thing is actually kinda heavy (~28 kb)
// we can probably optimize it, but ehhh... does it really matter?
const getNotFoundImage = () => import('assets/images/not_found.svg?react');

const NotFoundImageComponent = lazy(getNotFoundImage);

const useStyles = makeStyles((theme) => ({
    '@keyframes star-animation': {
        '0%': {
            transform: 'scale(1)',
        },
        '100%': {
            transform: 'scale(1)',
        },
    },

    svg: {
        '& .not-found-img__stars > path': {},
    },
}));

const NotFoundImage = forwardRef(
    (
        { className, ...props }: React.SVGAttributes<SVGSVGElement>,
        ref: React.ForwardedRef<SVGSVGElement>
    ) => {
        const styles = useStyles();

        const handleSvgElement = useCallback((svg: SVGSVGElement | null) => {
            if (svg) setupDraggableMoon(svg);
        }, []);
        const finalRef = useForkRef(ref, handleSvgElement);
        const [ready, setReady] = useState(false);

        useEffect(() => {
            getNotFoundImage().then(() => setReady(true));
        }, []);

        if (!ready) return null;

        return (
            <NotFoundImageComponent
                ref={finalRef}
                className={clsx(styles.svg, className)}
                {...props}
            />
        );
    }
);

export default NotFoundImage;

const MAX_MOONS = 1;

function setupDraggableMoon(svg: SVGSVGElement) {
    const moonGroup = svg.querySelector('.not-found-img__moon') as SVGGElement | null;
    if (!moonGroup) return;
    if (moonGroup.dataset.draggableSetup) return;
    moonGroup.dataset.draggableSetup = 'true';
    const bbox = moonGroup.getBBox();
    moonGroup.style.transformOrigin = `${bbox.x + bbox.width / 2}px ${bbox.y + bbox.height / 2}px`;

    let x0: number | undefined,
        y0: number | undefined,
        moonReplacedCounter = 0;
    const moonThief = svg.querySelector('.not-found-img__moonThief') as SVGGElement | null;

    moonGroup.style.cursor = 'grab';

    const handler = (e: MouseEvent) => {
        moonGroup.style.cursor = 'grabbing';

        const bbox = moonGroup.getBoundingClientRect();
        const offsetX = e.pageX - bbox.x,
            offsetY = e.pageY - bbox.y;

        if (x0 === undefined) x0 = e.pageX - offsetX;
        if (y0 === undefined) y0 = e.pageY - offsetY;

        const onMouseMove = (e: MouseEvent) => {
            e.preventDefault();
            const xCoefficient = svg.viewBox.baseVal.width / svg.clientWidth;
            const yCoefficient = svg.viewBox.baseVal.height / svg.clientHeight;

            window.requestAnimationFrame(() => {
                moonGroup.style.transform = `translate(${
                    (e.pageX - x0! - offsetX) * xCoefficient
                }px, ${(e.pageY - y0! - offsetY) * yCoefficient}px)`;
            });
        };

        document.addEventListener('mousemove', onMouseMove);

        const stopDrag = () => {
            moonGroup.style.cursor = 'grab';
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', stopDrag);
            document.removeEventListener('mouseleave', stopDrag);

            const isMoonGone = checkIfMoonIsGone(moonGroup, svg);

            if (isMoonGone) {
                if (moonReplacedCounter + 1 > MAX_MOONS && moonThief) {
                    setTimeout(() => {
                        moonThief.style.opacity = '1';
                    }, 900);
                    return;
                }

                moonReplacedCounter++;
                moonGroup.style.display = 'none';
                moonGroup.style.pointerEvents = 'none';

                setTimeout(() => {
                    moonGroup.style.transform = 'scale(0)';
                    moonGroup.style.transition = '.2s ease-in-out';
                    setTimeout(() => {
                        moonGroup.style.removeProperty('display');
                        moonGroup.style.transform = 'scale(1)';
                        setTimeout(() => {
                            moonGroup.style.removeProperty('pointer-events');
                            moonGroup.style.removeProperty('transition');
                        }, 200);
                    }, 700);
                }, 100);
            }
        };

        document.addEventListener('mouseup', stopDrag);
        document.addEventListener('mouseleave', stopDrag);
    };

    moonGroup.addEventListener('mousedown', handler);

    return () => moonGroup.removeEventListener('mousedown', handler);
}
function checkIfMoonIsGone(moonGroup: SVGGElement, svg: SVGSVGElement): boolean {
    const bbox = moonGroup.getBoundingClientRect();
    const svgBbox = svg.getBoundingClientRect();
    return (
        bbox.x < svgBbox.x - bbox.width ||
        bbox.y < svgBbox.y - bbox.height ||
        bbox.x > svgBbox.x + svgBbox.width ||
        bbox.y > svgBbox.y + svgBbox.height
    );
}
