import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { Colors } from '../../../../common/styles/Colors';
import { FontPrimary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';
import styles from './styles.module.css';

const OrderDetailNotFound = (props: any) => {
    const { t } = useAppTranslation();
    const [orderNumber, setOrderNumber] = useState();

    useEffect(() => {
        setOrderNumber(props.location.state.orderNumber);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (
        <div className={styles.mainContainer}>
            <div className={styles.image} />
            <span
                className={styles.haveProblem}
                style={{
                    ...FontPrimary(HeaderStyles.H1_34px, true, Colors.CM1),
                }}
            >
                {t('notFound.haveProblem')}
            </span>
            <span
                className={styles.weCanNotFind}
                style={{
                    ...FontPrimary(HeaderStyles.H3_21px, false, Colors.Neutral7),
                }}
            >
                {t('notFound.weCanNotFindOrderDetail', { orderNumber })}
            </span>
        </div>
    );
};

export default OrderDetailNotFound;
