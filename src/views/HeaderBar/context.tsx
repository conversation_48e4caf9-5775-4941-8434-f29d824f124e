import use<PERSON><PERSON>ceRender from 'common/hooks/useForceRender';
import React, { createContext, useCallback, useContext, useEffect, useMemo, useRef } from 'react';
import ReactDOM from 'react-dom';
import { z } from 'zod';

export const HeaderSlotContext = createContext<HTMLElement | null>(null);

type HeaderSlotProviderProps = React.PropsWithChildren<{
    slot: HTMLElement | null;
}>;

export default function HeaderSlotProvider({ slot, children }: HeaderSlotProviderProps) {
    return <HeaderSlotContext.Provider value={slot}>{children}</HeaderSlotContext.Provider>;
}

export function CustomHeaderContent({ children }: React.PropsWithChildren<{}>) {
    const slot = useContext(HeaderSlotContext);

    if (!slot) return null;

    return ReactDOM.createPortal(children, slot);
}

interface IHeaderLoadingController {
    add(): void;
    release(): void;
    showsLoader(): boolean;
}
class HeaderLoadingController implements IHeaderLoadingController {
    private _counter: number = 0;
    private _forceRender: () => void;

    constructor(fr: () => void) {
        this._forceRender = fr;
    }
    add(): void {
        this._add(1);
    }
    release(): void {
        this._add(-1);
    }
    showsLoader(): boolean {
        return this._counter > 0;
    }

    private _add(v: number) {
        const newCounterValue = this._counter + v;
        if (newCounterValue === this._counter) return;

        const isShowingLoader = this.showsLoader();
        this._counter = newCounterValue;
        const isShowingLoaderAfterUpdate = this.showsLoader();
        if (isShowingLoader !== isShowingLoaderAfterUpdate) {
            this._forceRender();
        }
    }
}

const HeaderLoadingControllerContext = createContext<IHeaderLoadingController | null>(null);
const HeaderLoadingContext = createContext<boolean>(false);

export function useIsHeaderLoaderLoading() {
    return useContext(HeaderLoadingContext);
}

export function useHeaderLoadingController() {
    return useContext(HeaderLoadingControllerContext);
}

/**
 * Hook to control the header loading indicator.
 *
 * When called outside of a `HeaderLoadingProvider` component, the loading indicator will not be shown (i.e. hook does nothing).
 *
 * @param loading Flag indicating whether the loading indicator should be shown.
 */
export function useHeaderLoading(loading: boolean): void {
    const controller = useHeaderLoadingController();

    useEffect(() => {
        if (!controller || !loading) return () => {};

        controller.add();
        return () => {
            controller.release();
        };
    }, [loading, controller]);
}

export function HeaderLoadingProvider({ children }: React.PropsWithChildren<{}>) {
    const fr = useForceRender();
    const controller = useMemo(() => new HeaderLoadingController(fr), [fr]);

    return (
        <HeaderLoadingControllerContext.Provider value={controller}>
            <HeaderLoadingContext.Provider value={controller.showsLoader()}>
                {children}
            </HeaderLoadingContext.Provider>
        </HeaderLoadingControllerContext.Provider>
    );
}

const HeaderTitleContext = createContext<DocumentTitleObject | null>(null);
const HeaderTitleControllerContext = createContext<HeaderTitleController>({ set: () => () => {} });

export function HeaderTitleProvider({ children }: React.PropsWithChildren<{}>) {
    const [controller, title] = useNewHeaderTitleController();

    return (
        <HeaderTitleControllerContext.Provider value={controller}>
            <HeaderTitleContext.Provider value={title}>{children}</HeaderTitleContext.Provider>
        </HeaderTitleControllerContext.Provider>
    );
}

export function useHeaderTitleController(): HeaderTitleController {
    return useContext(HeaderTitleControllerContext);
}

export function useCurrentHeaderTitle() {
    return useContext(HeaderTitleContext);
}

export function HeaderTitle() {
    const title = useCurrentHeaderTitle();

    return <>{title?.titleJsx || 'ClearMechanic'}</>;
}

export function MirrorHeaderTitleToDocumentTitle() {
    const currentTitle = useContext(HeaderTitleContext);

    useEffect(() => {
        if (!currentTitle || currentTitle.title === 'ClearMechanic' || currentTitle.title === '') {
            document.title = 'ClearMechanic';
        } else {
            document.title = `ClearMechanic - ${currentTitle.title}`;
        }
    }, [currentTitle]);

    return null;
}

const documentTitleObjectSchema = z.object({
    title: z.string(),
    titleJsx: z
        .custom<JSX.Element>(
            (e) => (e as any)?.$$typeof === Symbol.for('react.element'),
            'value must be a React Element'
        )
        .or(z.string()),
});

type DocumentTitleObject = z.infer<typeof documentTitleObjectSchema>;

export type DocumentTitle = string | DocumentTitleObject;

function createDocumentTitleObject(title: DocumentTitle): DocumentTitleObject {
    if (typeof title === 'string') {
        return { title, titleJsx: title };
    } else {
        return title;
    }
}

type HeaderTitleController = {
    set(title: DocumentTitle): () => void;
};

type HeaderTitleId = string;

function newHeaderTitleId(): HeaderTitleId {
    return Math.random().toString().substring(2);
}

function useNewHeaderTitleController(): [HeaderTitleController, DocumentTitleObject | null] {
    const fr = useForceRender();
    const state = useRef<{
        order: HeaderTitleId[];
        titles: Record<HeaderTitleId, DocumentTitleObject>;
    }>({ order: [], titles: {} });

    const set = useCallback(
        (title: DocumentTitle): (() => void) => {
            const id = newHeaderTitleId();
            const titleObject = createDocumentTitleObject(title);
            state.current = {
                order: [...state.current.order, id],
                titles: {
                    ...state.current.titles,
                    [id]: titleObject,
                },
            };
            fr();

            return () => {
                const titles = { ...state.current.titles };
                delete titles[id];
                state.current = {
                    order: state.current.order.filter((x) => x !== id),
                    titles,
                };
                fr();
            };
        },
        [fr]
    );

    const title: DocumentTitleObject | null =
        state.current.order.length === 0
            ? null
            : state.current.titles[state.current.order[state.current.order.length - 1]];
    const controller = useMemo(() => ({ set }), [set]);

    return [controller, title];
}
