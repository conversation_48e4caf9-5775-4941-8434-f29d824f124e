import { IconButton, LinearProgress } from '@mui/material';
import { makeStyles } from '@mui/styles';
import RealTimeNotificationsApi from 'api/notifications';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import ScrollDetector from 'common/components/ScrollDetector';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import { DateTime } from 'luxon';
import React, { useCallback, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    CmosNotification,
    notificationsActions,
    selectRealTimeNotifications,
} from 'store/slices/notifications';
import NotificationCard from './NotificationCard';

const useStyles = makeStyles((theme) => ({
    root: {
        width: 360,
        position: 'relative',
        overflow: 'hidden',
    },

    header: {
        position: 'relative',
        zIndex: 2,
        backgroundColor: '#fff',
    },

    headerText: {
        padding: '24px 20px',
        margin: 0,
        color: theme.palette.neutral[7],
        ...theme.typography.h4Roboto,
    },

    closeBtn: {
        color: theme.palette.primary.main,
        position: 'absolute',
        right: 10,
        top: '50%',
        transform: 'translateY(-50%)',
    },

    contentWrapper: {
        height: 'fit-content',
        position: 'relative',
    },

    content: {
        maxHeight: 360,
        minHeight: 250,
        overflow: 'auto',
        ...scrollbarStyle(),

        '&::after': {
            content: '" "',
            display: 'block',
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: 20,
            backgroundImage: 'linear-gradient(transparent, #f2f2f2)',
            zIndex: 1,
            transition: 'transform .15s',
            transform: 'translateY(100%)',
            pointerEvents: 'none',
        },

        '&:not([data-bottom])::after': {
            transform: 'translateY(0)',
        },
    },

    progress: {
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
    },
}));

type NotificationsPopupContentProps = {
    onClose: () => void;
    isLoading: boolean;
    onFetchNextPage: () => void;
};

function getNextItem(el: Element): HTMLElement | null {
    let item: ChildNode | null = el.nextSibling;
    let nextItem: HTMLElement | null = null;

    while (item) {
        if (item instanceof HTMLElement) {
            if (item.hasAttribute('data-notification')) {
                nextItem = item;
                break;
            }
            item = item.nextSibling;
        } else {
            item = null;
        }
    }

    return nextItem;
}

function getPrevItem(el: Element): HTMLElement | null {
    let item: ChildNode | null = el.previousSibling;
    let nextItem: HTMLElement | null = null;

    while (item) {
        if (item instanceof HTMLElement) {
            if (item.hasAttribute('data-notification')) {
                nextItem = item;
                break;
            }
            item = item.previousSibling;
        } else {
            item = null;
        }
    }

    return nextItem;
}

export default function NotificationsPopupContent({
    onClose,
    isLoading,
    onFetchNextPage,
}: NotificationsPopupContentProps) {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const notifications = useAppSelector(selectRealTimeNotifications);
    const dispatch = useAppDispatch();

    const groupedNotifications = useMemo(() => {
        const groups = Object.entries(
            groupBy(notifications, (x) => DateTime.fromISO(x.createdAt).toFormat('yyyy-MM-dd'))
        );
        return sortBy(groups, (x) => -DateTime.fromFormat(x[0], 'yyyy-MM-dd').toMillis()).map(
            (x) => ({
                date: x[0],
                notifications: sortBy(x[1], (x) => -DateTime.fromISO(x.createdAt).toMillis()),
            })
        );
    }, [notifications]);

    const onNotificationClick = useCallback(
        async (n: CmosNotification) => {
            if (n.read) return;
            await RealTimeNotificationsApi.markAsRead({ notificationId: n.id });
            dispatch(notificationsActions.markAsRead(n.id));
        },
        [dispatch]
    );

    const onKeyDown = useCallback((e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.code !== 'ArrowDown' && e.code !== 'ArrowUp') return;
        const rootEl = e.target as HTMLDivElement;
        if (document.activeElement && rootEl.contains(document.activeElement)) {
            if (e.code === 'ArrowDown') {
                const next = getNextItem(document.activeElement);
                if (next) {
                    next.focus();
                }
            } else {
                const next = getPrevItem(document.activeElement);
                if (next) {
                    next.focus();
                }
            }
        }
    }, []);

    const onScroll = useCallback(
        (e: React.UIEvent) => {
            if (!(e.target instanceof HTMLElement)) return;
            if (e.target.scrollTop + e.target.clientHeight === e.target.scrollHeight) {
                onFetchNextPage();
            }
        },
        [onFetchNextPage]
    );

    return (
        <div className={styles.root}>
            <header className={styles.header}>
                <LinearProgress
                    className={styles.progress}
                    style={{ visibility: isLoading ? 'visible' : 'hidden' }}
                />

                <h4 className={styles.headerText}>{t('notifications.header')}</h4>
                <IconButton size="small" className={styles.closeBtn} onClick={onClose}>
                    <CloseIcon />
                </IconButton>
            </header>
            <div className={styles.contentWrapper}>
                <ScrollDetector
                    onKeyDown={onKeyDown}
                    onScroll={onScroll}
                    className={styles.content}
                >
                    {groupedNotifications.map((group) => (
                        <React.Fragment key={group.date}>
                            <NotificationGroupHeader date={group.date} />
                            {group.notifications.map((n) => (
                                <NotificationCard
                                    key={n.id}
                                    onClick={onNotificationClick}
                                    notification={n}
                                />
                            ))}
                        </React.Fragment>
                    ))}
                </ScrollDetector>
            </div>
        </div>
    );
}

type NotificationGroupHeaderProps = {
    date: string;
};

const useNotificationGroupStyles = makeStyles((theme) => ({
    root: {
        ...theme.typography.h5Inter,
        backgroundColor: theme.palette.neutral[4],
        padding: '3px 12px',
        color: theme.palette.neutral[7],
    },
}));

function NotificationGroupHeader({ date }: NotificationGroupHeaderProps) {
    const styles = useNotificationGroupStyles();
    const { t } = useAppTranslation();
    const str = useMemo(() => {
        const now = DateTime.now();
        const dt = DateTime.fromFormat(date, 'yyyy-MM-dd');

        if (now.hasSame(dt, 'day')) {
            return t('commonLabels.today');
        } else if (now.minus({ day: 1 }).hasSame(dt, 'day')) {
            return t('commonLabels.yesterday');
        } else {
            return dt.toFormat('MMM dd');
        }
    }, [date, t]);

    return <div className={styles.root}>{str}</div>;
}
