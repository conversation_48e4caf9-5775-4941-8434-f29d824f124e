import { IconButton, styled, useTheme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { $Icon } from 'common/components/Icons/$Icon';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { CashIcon } from 'common/components/Icons/CashIcon';
import { DetailsIcon } from 'common/components/Icons/DetailsIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { IconProps } from 'common/components/Icons/Icon';
import { InspectionIcon } from 'common/components/Icons/InspectionIcon';
import { PollsIcon } from 'common/components/Icons/PollsIcon';
import { TrafficLightIcon } from 'common/components/Icons/TrafficLightIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { UserIcon } from 'common/components/Icons/UserIcon';
import { WarningIcon } from 'common/components/Icons/WarningIcon';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { DateTime } from 'luxon';
import { ComponentType, useEffect, useMemo, useRef, useState } from 'react';
import { NavLink, generatePath } from 'react-router-dom';
import { CmosNotification } from 'store/slices/notifications';
import { BinnacleOutlineIcon } from '../../../../common/components/Icons/BinnacleOutlineIcon';

type NotificationCardProps = {
    notification: CmosNotification;
    onClick?: (notif: CmosNotification) => void;
};

const useStyles = makeStyles((theme) => ({
    root: {
        all: 'initial',
        display: 'block',
        width: '100%',
        boxSizing: 'border-box',
        position: 'relative',
        padding: 5,
        cursor: 'pointer',
        transition: 'background-color .15s',
        ...theme.typography.body1,

        '&:hover': {
            backgroundColor: rgba(theme.palette.primary.light, 0.2),
        },

        '&:focus': {
            outline: `2px solid var`,
            outlineOffset: -2,
        },
    },

    unread: {
        backgroundColor: rgba(theme.palette.primary.light, 0.1),
    },

    title: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        display: 'flex',
        alignItems: 'center',
        color: theme.palette.neutral[7],
    },

    time: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        position: 'absolute',
        top: 10,
        right: 13,
        color: theme.palette.neutral[7],
    },
}));

export default function NotificationCard({ notification, onClick }: NotificationCardProps) {
    const { t } = useAppTranslation();
    const styles = useStyles();
    const theme = useTheme();

    const [isCollapsed, setIsCollapsed] = useState(true);
    const [textIsTooLong, setTextIsTooLong] = useState(false);
    const textRef = useRef<HTMLParagraphElement | null>(null);

    const dt = useMemo(() => DateTime.fromISO(notification.createdAt), [notification.createdAt]);
    const timeStr = useMemo(() => {
        const now = DateTime.now();
        if (now.hasSame(dt, 'day')) {
            return dt.toFormat("HH:mm 'hrs'");
        }
        if (now.minus({ day: 1 }).hasSame(dt, 'day')) {
            return t('commonLabels.yesterday');
        }
        return dt.toFormat('MMM dd');
    }, [dt, t]);

    useEffect(() => {
        if (!textRef.current) {
            return;
        }

        if (textRef.current.scrollHeight > textRef.current.clientHeight) {
            setTextIsTooLong(true);
        }
    }, [textRef]);

    const { IconComponent, href } = getNotificationData(notification);

    return (
        <NavLink
            role="button"
            data-notification
            className={clsx(styles.root, !notification.read && styles.unread)}
            to={href ?? '#'}
            onClick={() => onClick && onClick(notification)}
        >
            <div className={styles.title}>
                {IconComponent ? <IconComponent fill="currentColor" size={20} /> : undefined}
                {notification.title}
            </div>
            <time className={styles.time} dateTime={dt.toISO()}>
                {timeStr}
            </time>
            <TextContainer>
                <NotificationText ref={textRef} collapse={isCollapsed}>
                    {notification.body}
                </NotificationText>
                {textIsTooLong && (
                    <SIconButton
                        onClick={(e) => {
                            setIsCollapsed(!isCollapsed);
                            e.preventDefault();
                        }}
                    >
                        {isCollapsed ? (
                            <DownIcon fill={theme.palette.neutral[1]} />
                        ) : (
                            <UpIcon fill={theme.palette.neutral[1]} />
                        )}
                    </SIconButton>
                )}
            </TextContainer>
        </NavLink>
    );
}

const TextContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const NotificationText = styled('p')<{ collapse: boolean }>(({ theme, collapse }) => ({
    ...theme.typography.body1,
    margin: '5px 0',
    paddingLeft: 4,
    ...(collapse
        ? {
              display: '-webkit-box',
              '-webkit-line-clamp': '3',
              '-webkit-box-orient': 'vertical',
              overflow: 'hidden',
          }
        : undefined),
}));

const SIconButton = styled(IconButton)(({ theme }) => ({
    backgroundColor: theme.palette.primary.main,
    width: '28px',
    height: '23px',
    borderRadius: '23px',
}));

type NotificationData = {
    IconComponent?: ComponentType<IconProps>;
    href?: string;
};

function getNotificationData(notification: CmosNotification): NotificationData {
    const notificationData: NotificationData = {};

    switch (notification.type) {
        case 'OrderAssigned':
            notificationData.IconComponent = UserIcon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'OrderCreated':
            notificationData.IconComponent = InspectionIcon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'InspectionItemDeclined':
        case 'InspectionItemApproved':
            notificationData.IconComponent = CashIcon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'SurveyWithNegativeRating':
            notificationData.IconComponent = PollsIcon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'AppointmentAssigned':
            notificationData.IconComponent = CalendarIcon;
            notificationData.href =
                ROUTES.APPOINTMENTS.BASE + '?ap.id=' + notification.data.appointmentId;
            break;
        case 'OrderPhaseModified':
        case 'PhaseSetback':
            notificationData.IconComponent = TrafficLightIcon;
            notificationData.href =
                ROUTES.WORKSHOP_PLANNER + '?op.num=' + notification.data.orderNumber;
            break;
        case 'PriorityLevelCount':
        case 'PriorityLevelPercentage':
        case 'InspectionItem':
        case 'HighMileageVehicle':
        case 'InspectionNotSent':
        case 'NoInternalViews':
            notificationData.IconComponent = WarningIcon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'NewRedItem':
            notificationData.IconComponent = DetailsIcon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'PaymentReceived':
            notificationData.IconComponent = $Icon;
            notificationData.href = generatePath(ROUTES.ORDERS_DETAIL, {
                id: notification.data.orderId,
            });
            break;
        case 'JobAssigned':
            notificationData.IconComponent = BinnacleOutlineIcon;
            notificationData.href = '#';
            break;
        case '__invalid':
            notificationData.IconComponent = WarningIcon;
            notificationData.href = '#';
            break;
    }

    return notificationData;
}
