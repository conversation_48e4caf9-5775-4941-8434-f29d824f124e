import { useQueryClient } from '@tanstack/react-query';
import RealTimeNotificationsApi from 'api/notifications';
import { PUBNUB_CHANNELS } from 'api/pubnub';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useAppDispatch } from 'store';
import { fetchUnseenThunk, notificationsActions } from 'store/slices/notifications';
import { useCurrentUser } from 'store/slices/user';
import { usePubnubListener } from 'utils/pubnub';

const PAGE_LIMIT = 10;

export default function useNotificationFetcher(uiIsOpen: boolean = false) {
    const dispatch = useAppDispatch();

    const nextPage = useRef(2);
    const reachedEnd = useRef(false);

    const client = useQueryClient();
    const user = useCurrentUser();
    const [isLoading, setIsLoading] = useState(0);
    const [firstPageError, setFirstPageError] = useState<unknown>();

    const fetchPage = useCallback(
        async (page: number) => {
            setFirstPageError(undefined);
            setIsLoading((v) => v + 1);
            try {
                const notifications = await client.ensureQueryData({
                    queryKey: ['real-time-notifications', 'page', page],
                    queryFn: () => RealTimeNotificationsApi.getAll(page, PAGE_LIMIT),
                    cacheTime: 0,
                    staleTime: 0,
                });

                if (notifications.length > 0) {
                    dispatch(notificationsActions.ensureExists(notifications));
                }
                if (notifications.length !== PAGE_LIMIT) {
                    reachedEnd.current = true;
                }
            } catch (e: unknown) {
                if (page === 1) setFirstPageError(e);
                throw e;
            } finally {
                setIsLoading((v) => v - 1);
            }
        },
        [dispatch, client]
    );

    const initialFetch = useRef(false);

    useEffect(() => {
        if (uiIsOpen || !initialFetch.current) {
            initialFetch.current = true;
            fetchPage(1);
        }
    }, [fetchPage, uiIsOpen]);

    usePubnubListener<unknown>(
        () => {
            dispatch(fetchUnseenThunk());
        },
        {
            channels: [PUBNUB_CHANNELS.user(user.key)],
            types: ['rtn.new'],
        }
    );

    return {
        isLoading: isLoading !== 0,
        fetchNextPage: useCallback(() => {
            if (reachedEnd.current) return;
            fetchPage(nextPage.current);
            nextPage.current++;
        }, [fetchPage]),
        fetchFirstPage: useCallback(() => fetchPage(1), [fetchPage]),
        firstPageError,
    };
}
