import { Popover } from '@mui/material';
import { NotificationsIcon } from 'common/components/Icons/NotificationsIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIntervalIfDocumentActive from 'common/hooks/useIntervalIfDocumentActive';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import {
    fetchUnseenThunk,
    markAsSeenThunk,
    selectHasUnseenNotifications,
} from 'store/slices/notifications';
import SimpleErrorBoundary2, {
    SimpleErrorDisplay2,
} from 'utils/errorsHandling/SimpleErrorBoundary2';
import HeaderButtonBase from '../HeaderButtonBase';
import NotificationsPopupContent from './NotificationsPopupContent';
import useNotificationFetcher from './fetcher';

export default function NotificationsButton() {
    const { t } = useAppTranslation();
    const hasPendingNotifications = useHasUnseenNotifications();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const path = useLocation().pathname;
    const dispatch = useAppDispatch();

    useEffect(() => {
        setAnchorEl(null);
    }, [path]);

    const { isLoading, fetchNextPage, firstPageError, fetchFirstPage } = useNotificationFetcher(
        !!anchorEl
    );
    const open = !!anchorEl;

    useEffect(() => {
        if (open) {
            dispatch(markAsSeenThunk());
        }
    }, [open, dispatch]);

    return (
        <>
            <HeaderButtonBase
                badge={hasPendingNotifications && !anchorEl}
                tooltipContent={t('headerOptions.notifications')}
                onClick={(e) => setAnchorEl(e.target as HTMLElement)}
            >
                <NotificationsIcon size={30} />
            </HeaderButtonBase>
            <Popover
                transitionDuration={150}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                elevation={1}
                onClose={() => setAnchorEl(null)}
                anchorEl={anchorEl}
                PaperProps={{ style: { top: 10, minWidth: 360, minHeight: 320 } }}
                open={!!anchorEl}
            >
                {firstPageError ? (
                    <SimpleErrorDisplay2 onRetry={fetchFirstPage} />
                ) : (
                    <SimpleErrorBoundary2>
                        <NotificationsPopupContent
                            isLoading={isLoading}
                            onFetchNextPage={fetchNextPage}
                            onClose={() => setAnchorEl(null)}
                        />
                    </SimpleErrorBoundary2>
                )}
            </Popover>
        </>
    );
}

function useHasUnseenNotifications() {
    const dispatch = useAppDispatch();
    const has = useAppSelector(selectHasUnseenNotifications);

    useIntervalIfDocumentActive(
        () => {
            dispatch(fetchUnseenThunk());
        },
        60000,
        true
    );

    return has;
}
