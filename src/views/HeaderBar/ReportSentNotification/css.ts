import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useSubmitModalStyles = makeStyles((theme) => ({
    messageSentContent: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
    },
    messageSentIcon: {
        display: 'flex',
        height: '40px',
        width: '40px',
        border: `1px solid ${Colors.Success}`,
        borderRadius: '50%',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: '24px',
    },
    messageSentCaption: {
        textAlign: 'center',
        width: '317px',
    },
    messageText: {
        marginTop: '16px',
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
        textAlign: 'center',
    },
    messageThankYou: {
        marginTop: '16px',
        ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[7]),
        textAlign: 'center',
    },
    outer: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        height: '100%',
        width: '100%',
        '&:focus': {
            outline: 'none',
        },
    },
    modal: {
        width: '317px',
        padding: '15px 24px 32px 24px',
        borderRadius: '10px',
        backgroundColor: theme.palette.neutral[1],
        marginLeft: 'auto',
        marginRight: 'auto',
    },
    modalTitle: {
        ...FontPrimary(HeaderStyles.H5_14px, true, Colors.Black),
        textAlign: 'left',
    },
    modalIcon: {
        cursor: 'pointer',
    },
    modalIconButton: {
        '& svg:hover path': {
            fill: theme.palette.primary.light,
        },
    },
    firstGrid: {
        display: 'flex',
        justifyContent: 'end',
    },
}));
