import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Modal from '@mui/material/Modal';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { IconSize } from 'common/styles/IconSize';
import { IReportSentNotification } from './IReportSentNotification';
import { useSubmitModalStyles } from './css';

const ReportSentNotification = ({
    onClose: onModalCloseClick,
    open,
}: IReportSentNotification): JSX.Element => {
    const { t } = useAppTranslation();
    const submitStyles = useSubmitModalStyles();

    return (
        <Modal
            open={open}
            aria-labelledby="simple-modal-title"
            aria-describedby="simple-modal-description"
        >
            <div className={submitStyles.outer}>
                <div>
                    <div className={submitStyles.modal}>
                        <Grid item className={submitStyles.firstGrid}>
                            <IconButton
                                onClick={() => onModalCloseClick()}
                                className={submitStyles.modalIconButton}
                                size="small"
                            >
                                <CloseIcon
                                    fill={Colors.Neutral7}
                                    className={submitStyles.modalIcon}
                                />
                            </IconButton>
                        </Grid>
                        <div className={submitStyles.messageSentContent}>
                            <div className={submitStyles.messageSentIcon}>
                                <CheckIcon fill={Colors.Success} size={IconSize.M} />
                            </div>
                            <span
                                className={submitStyles.messageSentCaption}
                                style={{
                                    ...FontPrimary(HeaderStyles.H4_18px, true, Colors.Black),
                                }}
                            >
                                {t('headerOptions.submitModalHeader')}
                            </span>
                            <span className={submitStyles.messageText}>
                                <span className={submitStyles.messageThankYou}>
                                    {t('headerOptions.thankYou')}
                                </span>
                                {t('headerOptions.submitModalText')}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default ReportSentNotification;
