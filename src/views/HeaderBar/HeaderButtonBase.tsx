import { Badge, IconButton, IconButtonProps, styled } from '@mui/material';
import Tooltip from 'common/components/Tooltip';
import React, { forwardRef } from 'react';

type HeaderButtonBaseProps = IconButtonProps & {
    badge?: boolean;
    tooltipContent?: string;
};

const HeaderButtonBase = forwardRef(
    (
        { className, badge, tooltipContent, ...props }: HeaderButtonBaseProps,
        ref: React.ForwardedRef<HTMLButtonElement>
    ) => {
        return (
            <NoticeBadge
                overlap="rectangular"
                badgeContent={badge ? true : undefined}
                color="primary"
            >
                {tooltipContent ? (
                    <Tooltip content={tooltipContent}>
                        <StyledIconButton ref={ref} disableRipple {...props} size="large" />
                    </Tooltip>
                ) : (
                    <StyledIconButton ref={ref} disableRipple {...props} size="large" />
                )}
            </NoticeBadge>
        );
    }
);

const NoticeBadge = styled(Badge)({
    '& > .MuiBadge-badge': {
        '--badge-size': '10px',
        top: 6,
        right: 6,
        height: 'var(--badge-size)',
        width: 'var(--badge-size)',
        minWidth: 'initial',
        padding: 0,
        backgroundColor: 'var(--danger)',
        borderRadius: 100,
    },
});

const StyledIconButton = styled(IconButton)({
    height: 42,
    width: 42,
    border: '1px solid var(--neutral3)',

    '&:hover': {
        borderColor: 'var(--neutral3)',
        backgroundColor: 'var(--neutral3)',
        color: 'var(--neutral9)',
    },

    '&:active': {
        color: 'var(--cm1)',
    },

    '@media (max-width: 1360px)': {
        width: 32,
        height: 32,
    },
});

export default HeaderButtonBase;
