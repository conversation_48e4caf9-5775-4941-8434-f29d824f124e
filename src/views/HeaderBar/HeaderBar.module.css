:global(body) {
    --header-height: 52px;
    /* height of header itself */
    --header-bar-height: 52px;
    --header-z-index: 6;
}

.container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: var(--header-z-index);
}

.root {
    box-sizing: border-box;
    display: grid;
    grid-template-columns: auto 1fr auto;
    background-color: #ffffff;
    border-bottom: 1px solid var(--neutral3);
    height: var(--header-bar-height);
    overflow: hidden;
}

.customHeader {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    overflow: hidden;
}

.titleGroup {
    display: flex;
    align-items: center;
    padding-left: 16px;
    white-space: nowrap;
}

.title {
    font-family: Inter;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 2.29;
    letter-spacing: normal;
    color: #899198;
}

.buttonGroup {
    height: 100%;
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    padding-right: 30px;
}

.moreVertIcon {
    width: 32px;
    height: 32px;
    position: relative;
    border: solid 0px #eaeaea;
    background-color: #ffffff;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: solid 2px transparent;
    transition: scale 0.3s;
}

.moreVertIcon:hover {
    background-color: rgba(211, 224, 255, 0.67) !important;
}

.moreVertIcon:focus {
    border-color: var(--cm2);
    outline: none;
}

.profileContainer {
    width: 54px;
    height: 54px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #6a6e72;
}

.profileLabel {
    font-family: Inter;
    font-size: 24px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    letter-spacing: normal;
    color: #f6f6f6;
}

.logout {
    position: absolute;
    bottom: 53px;
    cursor: pointer;
}

.config {
    position: absolute;
    bottom: 88px;
    cursor: pointer;
}

.labelFooter {
    margin-left: 13px;
    opacity: 0.9;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: normal;
    text-align: left;
    color: #acb7c0;
}

.itemSelected {
    background-color: rgba(137, 145, 152, 0.14);
    border-left: solid 4px #467cfc;
}

.itemIcon {
    margin-left: 30px;
    color: #acb7c0;
}

.SettingsCard {
    margin: 0px 0px 0 0;
    padding: 0px;
    border-radius: 11px;
    box-shadow: 0 0 13px 0 rgba(201, 205, 211, 0.57);
    background-color: #ffffff;
}

.cardText {
    font-size: 12px;
    font-family: Inter;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    text-decoration: none;
}

.cardIcon {
    margin-left: 0px;
}

.MuiListItemIcon {
    min-width: 20px !important;
    margin-right: 10px;
    justify-content: center;
}

.MuiListItemButton[disabled] {
    pointer-events: none;
}

.MuiListItemButton:hover {
    background-color: rgba(211, 224, 255, 0.27) !important;
    cursor: pointer;
}

.MuiListItemButton:active {
    background-color: rgba(211, 224, 255, 0.67) !important;
}

.overdueBanner {
    background-color: #ffce31;
    padding: 12px;
    grid-column: 1 / span 3;
    color: #fff;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    white-space: pre-line;
    height: 60px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overdueBanner a {
    color: #fff;
    font-weight: bold;
}

.customizableBanner {
    padding: 0px 80px 0px 12px;
    grid-column: 1 / span 3;
    font-family: Inter;
    font-size: 14px;
    white-space: pre-line;
    box-sizing: border-box;
}

.loadingBarContainer {
    position: fixed;
    top: var(--header-bar-height);
    left: 0;
    right: 0;
}

@media (min-width: 1360px) {
    .title {
        font-size: calc(1.2 * 14px);
    }
    .moreVertIcon {
        width: calc(1.2 * 32px);
        height: calc(1.2 * 32px);
    }
    .buttonGroup {
        gap: 12px;
    }
}

@media (min-width: 1920px) {
    .title {
        font-size: calc(1.4 * 14px);
    }
    .moreVertIcon {
        width: calc(1.4 * 32px);
        height: calc(1.4 * 32px);
    }
    .buttonGroup {
        gap: 14px;
    }
}
