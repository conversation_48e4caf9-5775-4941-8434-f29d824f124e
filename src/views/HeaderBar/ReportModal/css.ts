import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useReportModalStyles = makeStyles((theme) => ({
    outer: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        height: '100%',
        width: '100%',
        '&:hover': {
            outline: 'none',
        },
    },
    modal: {
        height: '33%',
        minHeight: '372px',
        padding: '22px 24px 12px 24px',
        borderRadius: '24px',
        backgroundColor: theme.palette.neutral[1],
        marginLeft: 'auto',
        marginRight: 'auto',
    },
    modalTitle: {
        ...FontPrimary(HeaderStyles.H5_14px, true, Colors.Black),
        textAlign: 'left',
    },
    modalIconButton: {
        '& svg:hover path': {
            fill: theme.palette.primary.light,
        },
    },
    modalIcon: {
        cursor: 'pointer',
        '&:hover': {
            fill: theme.palette.primary.light,
        },
    },
    inputLabel: {
        height: '15px',
        margin: '4px 33px 30px 0',
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        textAlign: 'left',
    },
    inputContainer: {
        position: 'relative',
        width: '284px',
        height: '32px',
        display: 'flex',
        alignItems: 'center',
        marginLeft: '72px',
    },
    textareaInput: {
        height: '104px',
    },
    line: {
        width: '100%',
        border: `1px solid ${theme.palette.neutral[3]}`,
    },
    textareaLabel: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        textAlign: 'left',
        marginBottom: '8px',
    },
    textareaInputContainer: {
        position: 'relative',
        height: '106px',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
    },
    buttonContainer: {
        marginTop: '20px',
    },
}));
