import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Modal from '@mui/material/Modal';
import { EmailSendLogic } from 'business/EmailSendLogic';
import { getSubdomain } from 'common/ApiHelper';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { LimitedTextArea } from 'common/components/Inputs';
import TextField from 'common/components/Inputs/TextField';
import { emailRegex } from 'common/constants';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import { useCurrentUserOptional } from 'store/slices/user';
import { IReportModal } from './IReportModal';
import { useReportModalStyles } from './css';

const ReportModal = ({
    onClose: onModalCloseClick,
    onMessageSent,
    open,
}: IReportModal): JSX.Element => {
    const { t } = useAppTranslation();
    const maxMessageLength = 150;

    const { callApi } = useApiCall();
    const reportStyles = useReportModalStyles();

    const [email, setEmail] = useState<string | undefined>('');
    const [message, setMessage] = useState<string>('');

    const userData = useCurrentUserOptional();

    const isEnterprise = useIsEnterpriseRoute();

    useEffect(() => {
        if (!userData) return;
        if (emailRegex.test(userData.name.toLowerCase())) {
            setEmail(userData.name);
        }
    }, [userData]);

    const sendReport = async () => {
        if (email && emailRegex.test(email.toLowerCase())) {
            const subdomain = getSubdomain();
            await callApi(
                () =>
                    EmailSendLogic.send(
                        subdomain,
                        isEnterprise,
                        userData?.displayName ?? null,
                        email,
                        message,
                        true
                    ),
                {
                    selectErrorContent: () => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );
            close();
            onMessageSent();
        }
    };

    const close = () => {
        if (!userData) return;
        setEmail(userData.name);
        setMessage('');

        onModalCloseClick();
    };

    return (
        <Modal
            open={open}
            aria-labelledby="simple-modal-title"
            aria-describedby="simple-modal-description"
        >
            <div className={reportStyles.outer}>
                <div>
                    <div className={reportStyles.modal}>
                        <Grid
                            container
                            justifyContent="space-between"
                            alignItems="center"
                            style={{ marginBottom: 35 }}
                        >
                            <Grid item xs={6}>
                                <p id="simple-modal-title" className={reportStyles.modalTitle}>
                                    {t('headerOptions.reportAProblem')}
                                </p>
                            </Grid>
                            <Grid item>
                                <IconButton
                                    onClick={() => close()}
                                    className={reportStyles.modalIconButton}
                                    size="small"
                                >
                                    <CloseIcon
                                        fill={Colors.Neutral7}
                                        className={reportStyles.modalIcon}
                                    />
                                </IconButton>
                            </Grid>
                        </Grid>

                        {/* TEXT EMAIL */}
                        <Grid container alignItems="baseline" style={{ marginBottom: 10 }}>
                            <Grid item xs={1}>
                                <p className={reportStyles.inputLabel}>{t('login.emailLabel')}</p>
                            </Grid>
                            <Grid item xs={11}>
                                <div className={reportStyles.inputContainer}>
                                    <TextField
                                        name={'users.email'}
                                        value={email}
                                        showValidationIndicators={true}
                                        placeholder={'<EMAIL>'}
                                        onChange={(event) => setEmail(event.target.value)}
                                    />
                                </div>
                            </Grid>
                        </Grid>

                        {/*LINE*/}
                        <Grid container alignItems="baseline" style={{ marginBottom: 24 }}>
                            <div className={reportStyles.line} />
                        </Grid>

                        {/* TEXTAREA DESCRIPTION */}
                        <Grid container alignItems="baseline">
                            <Grid item xs={12}>
                                <div className={reportStyles.textareaLabel}>
                                    {t('headerOptions.provideADetailedDescriptionOfYourProblem')}
                                </div>
                            </Grid>
                            <Grid item xs={12}>
                                <LimitedTextArea
                                    maxLength={maxMessageLength}
                                    hideLabel
                                    value={message}
                                    id="modalTextarea"
                                    name="modalTextarea"
                                    onChange={(event) => setMessage(event.target.value)}
                                    className={reportStyles.textareaInput}
                                    placeholder={t('headerOptions.placeholder')}
                                />
                            </Grid>
                        </Grid>

                        {/* SEND BUTTON */}
                        <Grid
                            container
                            justifyContent="flex-end"
                            className={reportStyles.buttonContainer}
                        >
                            <Grid item xs={4}>
                                <Button
                                    label={t('headerOptions.send')}
                                    cmosVariant={'filled'}
                                    cmosSize={'medium'}
                                    color={Colors.CM1}
                                    type="button"
                                    blockMode
                                    onClick={sendReport}
                                    disabled={
                                        !email ||
                                        !emailRegex.test(email.toLowerCase()) ||
                                        !message ||
                                        message.length > maxMessageLength
                                    }
                                />
                            </Grid>
                        </Grid>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default ReportModal;
