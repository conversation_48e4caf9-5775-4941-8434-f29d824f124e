import {
    Divider,
    List,
    ListItemIcon,
    ListItemText,
    listItemTextClasses,
    styled,
} from '@mui/material';
import { SettingsIcon } from 'common/components/Icons/SettingsIcon';
import { WarningIcon } from 'common/components/Icons/WarningIcon';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import nr from 'utils/nr';
import ReportModal from '../ReportModal';
import ReportSentNotification from '../ReportSentNotification';

type SettingsPopoverContentProps = {
    closePopover: () => void;
};

export const SettingsPopoverContent = ({ closePopover }: SettingsPopoverContentProps) => {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const userPermission = useAppSelector(selectUserPermission);
    const globalSettings = useSelector(selectSettings);
    const [showProblemModal, setShowProblemModal] = useState(false);
    const [isMessageSentOpened, setIsMessageSentOpened] = useState(false);

    const handleModalClose = () => {
        setShowProblemModal(false);
    };

    return (
        <>
            <StyledList>
                {globalSettings.appMode === 'RepairShop' && (
                    <>
                        <StyledListItem
                            onClick={() => {
                                navigate(ROUTES.SETTINGS.GENERAL.DEFAULT);
                                nr('click.settings.sidebar');
                                closePopover();
                            }}
                            disabled={!userPermission.settingsAccess}
                        >
                            <ListItemIcon sx={{ color: 'inherit' }}>
                                <SettingsIcon fill="currentColor" />
                            </ListItemIcon>
                            <StyledListItemText>{t('headerOptions.settings')}</StyledListItemText>
                        </StyledListItem>
                        <StyledDivider />
                    </>
                )}
                <StyledListItem
                    onClick={() => {
                        setShowProblemModal(true);
                    }}
                >
                    <ItemContainer>
                        <ListItemIcon sx={{ color: 'inherit' }}>
                            <WarningIcon fill="currentColor" />
                        </ListItemIcon>
                        <StyledListItemText>{t('headerOptions.reportAProblem')}</StyledListItemText>
                    </ItemContainer>
                </StyledListItem>
            </StyledList>
            <ReportModal
                open={showProblemModal}
                onMessageSent={() => setIsMessageSentOpened(true)}
                onClose={handleModalClose}
            />
            <ReportSentNotification
                open={isMessageSentOpened}
                onClose={() => {
                    setIsMessageSentOpened(false);
                }}
            />
        </>
    );
};

const ItemContainer = styled(List)({
    display: 'flex',
});
const StyledList = styled(List)({
    padding: 0,
});

const StyledListItem = styled(SMenuItem2)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'bold',
    padding: '8px 16px',
}));

const StyledListItemText = styled(ListItemText)(({ theme }) => ({
    [`& .${listItemTextClasses.primary}`]: {
        ...theme.typography.h6Inter,
        fontWeight: 'bold',
        marginRight: 10,
    },
    alignContent: 'center',
}));

const StyledDivider = styled(Divider)({
    margin: '0px !important',
});

export default SettingsPopoverContent;
