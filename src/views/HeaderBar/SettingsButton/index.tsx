import { MoreVert } from '@mui/icons-material';
import { Popover } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import HeaderButtonBase from '../HeaderButtonBase';
import SettingsPopoverContent from './SettingsPopoverContent';

export const SettingsButton = () => {
    const { t } = useAppTranslation();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const open = Boolean(anchorEl);

    const handleMoreVertClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(anchorEl ? null : event.currentTarget);
    };

    return (
        <>
            <HeaderButtonBase
                tooltipContent={t('headerOptions.settings')}
                onClick={handleMoreVertClick}
            >
                <MoreVert />
            </HeaderButtonBase>
            <Popover
                slotProps={{
                    paper: {
                        sx: {
                            display: 'flex',
                            flexDirection: 'column',
                            width: 'fit-content',
                        },
                    },
                }}
                open={open}
                anchorEl={anchorEl}
                onClose={() => setAnchorEl(null)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
                <SettingsPopoverContent closePopover={() => setAnchorEl(null)} />
            </Popover>
        </>
    );
};

export default SettingsButton;
