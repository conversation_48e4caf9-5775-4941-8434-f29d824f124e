import { Popover, styled } from '@mui/material';
import { PUBNUB_CHANNELS, PUBNUB_MESSAGE_TYPES, WaMessageIncomingPayload } from 'api/pubnub';
import { ConversationGotControlFlowMessage, ConversationUpdatedMessage } from 'api/whatsapp';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, generatePath } from 'react-router-dom';
import { Virtuoso } from 'react-virtuoso';
import {
    ConversationInboundNotification,
    chatButtonActions,
    readAll as readAllConversations,
    resetState,
    setNextPage,
} from 'store/slices/chatButton';
import {
    selectAvailableConversationsCount,
    selectConversationsNotifications,
} from 'store/slices/chatButton/selector';
import fetchInboundNotifications from 'store/slices/chatButton/thunk/fetchInboundNotification';
import getCountAvailableConversationWindows from 'store/slices/chatButton/thunk/getCountAvailableConversationWindows';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { useCurrentUser } from 'store/slices/user';
import { usePubnubListener } from 'utils/pubnub';
import { AvailableWhatsAppConversations } from 'views/Components/AvailableWhatsAppConversations';
import HeaderButtonBase from '../HeaderButtonBase';

const Area = styled('div')({
    display: 'flex',
    flexGrow: 1,
    backgroundColor: '#fff',
});

const Line = styled('div')({
    height: 1,
    backgroundColor: Colors.Neutral3,
});

const Header = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    paddingTop: 10,
    paddingBottom: 11,
    paddingLeft: 24,
    paddingRight: 16,
});

const ReadAllButton = styled('button')(({ theme }) => ({
    ...theme.typography.h6Inter,
    border: 'none',
    backgroundColor: 'transparent',
    color: 'var(--cm1)',
    marginLeft: 12,
    marginTop: 8,
    marginBottom: 8,
    alignSelf: 'start',
    padding: 6,
    borderRadius: 6,

    '&:not(:disabled)': {
        cursor: 'pointer',

        '&:hover': {
            backgroundColor: rgba(theme.palette.primary.main, 0.05),
        },

        '&:active': {
            backgroundColor: rgba(theme.palette.primary.main, 0.1),
        },
    },

    '&:disabled': {
        color: Colors.Neutral4,
    },

    '&:focus': {
        outline: '2px solid var(--cm1)',
    },
}));

const ConversationHeader = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
});

const Conversation = styled(Link)({
    display: 'flex',
    flexDirection: 'column',
    paddingLeft: 16,
    paddingTop: 12,
    paddingRight: 16,
    paddingBottom: 10,
    backgroundColor: Colors.White,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: Colors.Neutral2,
    minHeight: 90,
    height: 'auto',
    boxSizing: 'border-box',
    color: 'initial',
    textDecoration: 'initial',
    transition: 'background-color .2s',

    '&:hover': {
        backgroundColor: '#e6effe',

        '&.unread': {
            backgroundColor: '#e6effe',
        },
    },

    '&:active': {
        backgroundColor: '#caddfd',
    },

    '&:focus': {
        outline: `2px solid var(--cm4)`,
        outlineOffset: -2,
    },

    '&.unread': {
        backgroundColor: '#f8fbfe',
    },
});

const SVirtuoso = styled(Virtuoso)({
    overflowY: 'auto',
    display: 'flex',
    height: 'auto',
    flexDirection: 'column',
    width: '100%',
    ...scrollbarStyle(),
}) as typeof Virtuoso;

const BlueDot = styled('div')({
    width: 8,
    height: 8,
    borderRadius: '50%',
    backgroundColor: Colors.CM3,
    marginLeft: 5,
});

const Circle = styled('div')(({ theme }) => ({
    borderRadius: 12,
    backgroundColor: Colors.CM2,
    ...theme.typography.h6Inter,
    color: Colors.White,
    height: 24,
    minWidth: 24,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 7.5,
    paddingRight: 7.5,
    boxSizing: 'border-box',
    marginLeft: 8,
}));

const Message = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: 'var(--neutral7)',
    fontWeight: 'normal',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    marginTop: 3,
    marginBottom: 3,
}));

const Bottom = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
});

const Details = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    width: 0,
    flexGrow: 1,
});

const Right = styled('div')(({ theme }) => ({
    display: 'flex',
    ...theme.typography.h7Inter,
    color: 'var(--neutral6)',
    fontWeight: 'normal',
    alignItems: 'center',
}));

const Left = styled('div')(({ theme }) => ({
    display: 'flex',
    ...theme.typography.h7Inter,
    color: 'var(--neutral7)',
    fontWeight: 'normal',
    alignItems: 'center',
}));

const CustomerName = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: 'var(--neutral8)',
    marginTop: 4,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
}));

const ConversationsTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: 'var(--neutral6)',
    marginTop: 6,
}));

export const ChatButton = () => {
    const { t } = useAppTranslation();
    const [isPressed, setPressed] = useState(false);
    const button = useRef<HTMLButtonElement>(null);

    const dispatch = useDispatch();
    const notifications = useSelector(selectConversationsNotifications);
    const conversationsCount = useSelector(selectAvailableConversationsCount);
    const globalSettings = useSelector(selectSettings);
    const user = useCurrentUser();

    useEffect(() => {
        dispatch(fetchInboundNotifications());
    }, [dispatch]);

    usePubnubListener<WaMessageIncomingPayload>(
        (message) => {
            const p = message.message.payload;

            dispatch(
                chatButtonActions.addNotification({
                    conversationId: p.conversationId,
                    displayedName: p.displayedName,
                    messageContent: p.messageContent,
                    unreadMessages: p.unreadMessages,
                    sentAt: p.sentAt,
                })
            );
        },
        {
            channels: [PUBNUB_CHANNELS.user(user.key)],
            types: [PUBNUB_MESSAGE_TYPES.WA_MSG_INCOMING],
            listenerEnabled: globalSettings.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    usePubnubListener<unknown>(
        async () => {
            dispatch(resetState());
            dispatch(fetchInboundNotifications());
        },
        {
            channels: [PUBNUB_CHANNELS.user(user.key)],
            types: ['user.updated'],
            unsubscribeIfListenerDisabled: true,
        }
    );

    usePubnubListener<ConversationUpdatedMessage | ConversationGotControlFlowMessage>(
        async (_message) => {
            const notification = notifications.find(
                (x) => x.conversationId === _message.message.payload.conversationId
            );
            if (
                notification &&
                notification.unreadMessages > _message.message.payload.unreadMessages
            )
                dispatch(chatButtonActions.markAsRead(_message.message.payload.conversationId));
        },
        {
            channels: [PUBNUB_CHANNELS.shopConversations(globalSettings?.uid || '')],
            types: ['conversation.updated', 'conversation.gotcontrolflow'],
            listenerEnabled: globalSettings?.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    const unreadCount = useMemo(
        () => notifications.filter((c) => c.unreadMessages > 0).length,
        [notifications]
    );

    const hasDot = unreadCount > 0;

    const readAll = () => {
        if (unreadCount > 0) {
            setPressed(false);
            dispatch(readAllConversations());
        }
    };

    const getDate = (conversation: ConversationInboundNotification) => {
        const inboundDate = moment(conversation.sentAt);
        const isToday = inboundDate.isSame(new Date(), 'day');
        if (isToday) return inboundDate.format('h:mm A');

        return inboundDate.format(t('conversations.dateFormat'));
    };

    function endReached(index: number): void {
        dispatch(setNextPage());
        dispatch(fetchInboundNotifications());
    }

    const itemsHeight = Math.min(notifications.length * 92, 92 * 4);

    return (
        <>
            <HeaderButtonBase
                ref={button}
                onClick={() => {
                    setPressed(true);
                    dispatch(getCountAvailableConversationWindows());
                }}
                badge={hasDot}
                tooltipContent={t('headerOptions.conversations')}
            >
                <CommentsIcon fill="currentColor" size={30} />
            </HeaderButtonBase>

            <Popover
                transitionDuration={150}
                slotProps={{
                    paper: {
                        sx: {
                            display: 'flex',
                            flexDirection: 'column',
                            width: 360,
                        },
                    },
                }}
                open={isPressed}
                anchorEl={button.current}
                onClose={() => setPressed(false)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            >
                <Header>
                    <ConversationsTitle>
                        {t('chatNotifications.conversationsNumber', {
                            count: unreadCount,
                        })}
                    </ConversationsTitle>
                    <Button
                        cmosVariant={'typography'}
                        color={Colors.CM1}
                        Icon={CloseIcon}
                        onClick={() => {
                            setPressed(false);
                            dispatch(getCountAvailableConversationWindows());
                        }}
                    />
                </Header>
                <Line />
                <ReadAllButton disabled={unreadCount === 0} onClick={readAll}>
                    {t('chatNotifications.readAll')}
                </ReadAllButton>
                <Area>
                    <SVirtuoso
                        style={{
                            height: `${itemsHeight}px`,
                        }}
                        data={notifications}
                        endReached={endReached}
                        itemContent={(index, c) => {
                            return (
                                <Conversation
                                    id={index === 0 ? 'first-conversation-notification' : undefined}
                                    to={generatePath(ROUTES.CONVERSATIONS, {
                                        conversationId: c.conversationId,
                                    })}
                                    onClick={() => {
                                        dispatch(chatButtonActions.markAsRead(c.conversationId));
                                        setPressed(false);
                                    }}
                                    className={c.unreadMessages > 0 ? 'unread' : undefined}
                                    key={c.conversationId}
                                >
                                    <ConversationHeader>
                                        <Left>
                                            <CommentsIcon
                                                fill={
                                                    c.unreadMessages > 0
                                                        ? Colors.CM1
                                                        : Colors.Neutral7
                                                }
                                            />
                                            <div>
                                                {c.unreadMessages > 0
                                                    ? t('chatNotifications.unreadMessages')
                                                    : t('chatNotifications.conversationWith')}
                                            </div>
                                        </Left>
                                        <Right>
                                            <div>{getDate(c)}</div>
                                            {c.unreadMessages > 0 && <BlueDot />}
                                        </Right>
                                    </ConversationHeader>
                                    <Bottom>
                                        <Details>
                                            <CustomerName>{c.displayedName}</CustomerName>
                                            <Message>{c.messageContent}</Message>
                                        </Details>
                                        {c.unreadMessages > 1 && (
                                            <Circle>{c.unreadMessages}</Circle>
                                        )}
                                    </Bottom>
                                </Conversation>
                            );
                        }}
                        increaseViewportBy={{ top: 0, bottom: 100 }}
                    />
                </Area>
                <AvailableWhatsAppConversations
                    available={conversationsCount.available}
                    maximum={conversationsCount.maximum}
                    marketing={conversationsCount.marketing}
                    utilityService={conversationsCount.utilityService}
                />
            </Popover>
        </>
    );
};

export default ChatButton;
