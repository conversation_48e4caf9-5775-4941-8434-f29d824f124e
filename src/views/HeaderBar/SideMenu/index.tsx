import { Drawer } from '@mui/material';
import { AftersalesCrmMenuOptions, MenuOptions } from 'common/constants';
import { useEffect, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import SideMenuBody from './SideMenuBody';

export type SideMenuProps = {
    open: boolean;
    onClose: () => void;
};

export default function SideMenu({ open, onClose }: SideMenuProps) {
    const settings = useAppSelector(selectSettings);

    const [keepMounted, setKeepMounted] = useState(open);

    useEffect(() => {
        // once side bar is open once - keep it mounted to make it faster
        // next time
        if (open && !keepMounted) {
            setKeepMounted(true);
        }
    }, [open, keepMounted]);

    const isAftersalesCrmInShop =
        settings.appMode === 'RepairShop' &&
        !!settings.repairShopSettings?.features.enableAftersalesCrm;

    const menuOptions = isAftersalesCrmInShop ? AftersalesCrmMenuOptions : MenuOptions;

    return (
        <Drawer
            PaperProps={{
                sx: {
                    width: 250,
                    backgroundColor: '#1D2027',
                    padding: '25px 0 20px 0',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    borderRadius: '0 25px 25px 0',
                    boxSizing: 'border-box',
                    boxShadow: 'none',
                },
            }}
            open={open}
            keepMounted={keepMounted}
            onClose={onClose}
        >
            <SideMenuBody
                onCloseMenu={onClose}
                optionsMenu={menuOptions}
                currentProduct="ClearMechanic"
            />
        </Drawer>
    );
}
