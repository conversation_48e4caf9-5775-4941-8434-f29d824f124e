import { makeStyles } from '@mui/styles';
import { rgba } from 'common/styles/ColorHelpers';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    itemMenu: {
        width: '100%',
        height: 40,
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
        boxSizing: 'border-box',
        borderLeft: 'solid 4px transparent',
        textDecoration: 'none',
        color: theme.palette.neutral[5],
        fill: theme.palette.neutral[5],
        paddingLeft: 10,
        transition: '.15s',
        '&:hover': {
            backgroundColor: rgba(theme.palette.neutral[7], 0.14),
            borderLeft: 'solid 4px #467cfc',
            fill: theme.palette.neutral[1],
            color: theme.palette.neutral[1],
        },
    },
    collapseItem: {
        cursor: 'pointer',
        width: '100%',
        height: 40,
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
        borderLeft: 'solid 4px transparent',
        boxSizing: 'border-box',
        textDecoration: 'none',
        color: theme.palette.neutral[5],
        fill: theme.palette.neutral[5],
        paddingLeft: 10,
        '&:hover': {
            backgroundColor: rgba(theme.palette.neutral[7], 0.14),
            borderLeft: 'solid 4px #467cfc',
            fill: theme.palette.neutral[1],
            color: theme.palette.neutral[1],
        },
    },
    labelFooter: {
        marginLeft: 13,
        ...FontPrimary(HeaderStyles.H5_14px, false, theme.palette.neutral[5]),
    },
    productsLabel: {
        display: 'block',
        ...FontPrimary(HeaderStyles.H7_11px, false, theme.palette.neutral[6]),
    },
    productsBoldLabel: {
        ...FontPrimary(HeaderStyles.H7_11px, true, theme.palette.neutral[6]),
    },
}));
