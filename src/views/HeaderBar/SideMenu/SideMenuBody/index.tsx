import { ExpandLess, ExpandMore } from '@mui/icons-material';
import {
    Backdrop,
    Box,
    CircularProgress,
    Collapse,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    styled,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import buildInfo from 'build-id';
import { AftersalesCrmMenuIcon } from 'common/components/Icons/AftersalesCrmMenuIcon';
import { AnalysisIcon } from 'common/components/Icons/AnalysisIcon';
import { AppointmentsMenuIcon } from 'common/components/Icons/AppointmentsMenuIcon';
import { BinnacleIcon } from 'common/components/Icons/BinnacleIcon';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { ClearMechanicIcon } from 'common/components/Icons/ClearMechanicIcon';
import { ConversationIcon } from 'common/components/Icons/ConversationIcon';
import { IndicatorsIcon } from 'common/components/Icons/IndicatorsIcon';
import { LogOutIcon } from 'common/components/Icons/LogOutIcon';
import { MegaphoneIcon } from 'common/components/Icons/MegaphoneIcon';
import { PollsIcon } from 'common/components/Icons/PollsIcon';
import { ProspectionIcon } from 'common/components/Icons/ProspectionIcon';
import { SettingsIcon } from 'common/components/Icons/SettingsIcon';
import { SortIcon } from 'common/components/Icons/SortIcon';
import ArrowTooltip from 'common/components/Tooltip';
import {
    ENTERPRISE_ROUTES,
    JobTitle,
    MenuSubOption,
    NavigationMenu,
    ROUTES,
} from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from 'common/styles/Colors';
import { DateTime } from 'luxon';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { useSelector } from 'react-redux';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import Server from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectHasUnsavedChanges } from 'store/slices/leavePageConfirmation/selectors';
import { logoutThunk, selectUserPermission, useCurrentUser } from 'store/slices/user';
import nr from 'utils/nr';
import ProductLogo from 'views/Components/ProductLogo';
import LeavePageConfirmationPopup from '../../../../common/components/Popups/LeavePageConfirmationPopup';
import GlobalSettings from '../../../../datacontracts/GlobalSettings';
import { resetReportChanges } from '../../../../store/slices/customReport';
import { ISideMenuBody } from './ISideMenuBody';
import { useStyles } from './css';

const ProfileContainer = styled('div')({
    marginTop: 20,
    marginBottom: 36,
    textAlign: 'center',
});

const Item = styled(ListItemButton)(({ theme }) => ({
    borderLeft: 'solid 4px transparent',
    color: theme.palette.neutral[5],
    paddingLeft: 10,
    transition: 'color .15s, background-color .15s, ',
    outlineOffset: -3,
    height: 40,

    '&:hover': {
        backgroundColor: rgba(theme.palette.neutral[7], 0.08),
        // borderLeft: 'solid 4px #467cfc',
        fill: theme.palette.neutral[1],
        color: theme.palette.neutral[1],
    },

    '& > .MuiListItemIcon-root': {
        minWidth: 36,
        color: 'inherit',
    },

    '& .MuiListItemText-primary': {
        ...theme.typography.h5Inter,
        fontWeight: 'normal',
        textAlign: 'left',
        display: 'inline-block',
    },

    '&:hover .MuiListItemText-primary': {
        fontWeight: '500',
    },

    '&[data-selected=true]': {
        color: theme.palette.neutral[2],
        fill: theme.palette.neutral[2],
        backgroundColor: rgba(theme.palette.neutral[7], 0.14),
        borderColor: '#467cfc',
    },

    '&:focus': {
        outline: '2px solid rgba(255,255,255,0.4)',
    },

    '& .MuiTouchRipple-root': {
        display: 'none',
    },
})) as typeof ListItemButton;

const SubItem = styled(Item)({
    paddingLeft: '24px',
}) as typeof Item;

const BottomContainer = styled('div')({
    marginTop: 'auto',
    width: '100%',
});

const AppInfoContainer = styled('div')({
    margin: '15px',
});

const getSumMenuLabel = (option: MenuSubOption, globalSettings: GlobalSettings) => {
    if (typeof option.label === 'function') {
        return option.label(globalSettings);
    }
    return option.label;
};

const SideMenuBody = ({ optionsMenu, onCloseMenu }: ISideMenuBody) => {
    const { t } = useAppTranslation();
    const globalSettings = useAppSelector(selectSettings);
    const userPermission = useSelector(selectUserPermission);
    const navigate = useNavigate();
    const settings = useSelector(selectSettings);
    const hasUnsavedChanges = useSelector(selectHasUnsavedChanges);
    const sideMenuStyles = useStyles();
    const path = useLocation().pathname;
    const dispatch = useAppDispatch();
    const isEnterprise = useIsEnterpriseRoute();
    const userJobTitle = useCurrentUser().jobTitle as JobTitle;
    const [showBlockLeavingConfirmation, setShowBlockLeavingConfirmation] =
        useState<boolean>(false);

    const logoutMutation = useMutation(
        async () => {
            const result = await dispatch(logoutThunk({ reason: 'ManualLogout' }));
            if (result.meta.requestStatus === 'rejected') {
                throw result.payload;
            }
        },
        {
            onSettled: () => {
                nr('logout.sidebar', { currentUrl: window.location.href });
                navigate(isEnterprise ? ENTERPRISE_ROUTES.LOGIN : ROUTES.LOGIN);
            },
        }
    );

    const logoutIfAllSaved = () => {
        if (hasUnsavedChanges) {
            setShowBlockLeavingConfirmation(true);
        } else {
            logoutMutation.mutate();
        }
    };

    return (
        <>
            <ProfileContainer>
                <ProductLogo scale={1.4} />
            </ProfileContainer>

            <StyledOverlayScrollbars options={{ scrollbars: { theme: 'os-theme-light' } }}>
                <List>
                    {optionsMenu
                        .filter((opc) => opc.isVisible(globalSettings, userPermission))
                        .map((opc) => {
                            if (opc.subMenuOptions)
                                return (
                                    <Collapsable
                                        icon={opc.icon ?? getIcon(opc.key)}
                                        title={t(opc.label)}
                                        isCollapse={opc.subMenuOptions.every(
                                            (x) => x.getRedirectTo(settings) !== path
                                        )}
                                        key={`link_to_${opc.key}`}
                                        shouldBeExpanded={
                                            opc.shouldBeExpanded?.(userJobTitle) ?? false
                                        }
                                    >
                                        {opc.subMenuOptions
                                            ?.filter((subItem) =>
                                                subItem.isVisible(globalSettings, userPermission)
                                            )
                                            .map((subMenuOptionsItem) => (
                                                <ArrowTooltip
                                                    content={t(
                                                        subMenuOptionsItem.tooltip?.title || ''
                                                    )}
                                                    position="right"
                                                    disabled={
                                                        !(
                                                            subMenuOptionsItem.showTooltip &&
                                                            subMenuOptionsItem.showTooltip(settings)
                                                        )
                                                    }
                                                    key={subMenuOptionsItem.key}
                                                >
                                                    <span
                                                        onClick={() => {
                                                            // NOTE (AP) Omg, it's really weird
                                                            if (
                                                                subMenuOptionsItem.isDisabled &&
                                                                subMenuOptionsItem.isDisabled(
                                                                    settings
                                                                ) &&
                                                                subMenuOptionsItem.disabledRedirectTo
                                                            ) {
                                                                navigate(
                                                                    subMenuOptionsItem.disabledRedirectTo
                                                                );
                                                            }
                                                        }}
                                                    >
                                                        <SubItem
                                                            component={Link}
                                                            data-selected={
                                                                subMenuOptionsItem.getRedirectTo(
                                                                    settings
                                                                ) === path
                                                            }
                                                            to={subMenuOptionsItem.getRedirectTo(
                                                                settings
                                                            )}
                                                            disabled={
                                                                subMenuOptionsItem.isDisabled &&
                                                                subMenuOptionsItem.isDisabled(
                                                                    settings
                                                                )
                                                                    ? true
                                                                    : false
                                                            }
                                                        >
                                                            <ListItemIcon>
                                                                {subMenuOptionsItem.icon ??
                                                                    getIcon(subMenuOptionsItem.key)}
                                                            </ListItemIcon>
                                                            <ListItemText
                                                                primary={t(
                                                                    getSumMenuLabel(
                                                                        subMenuOptionsItem,
                                                                        settings
                                                                    )
                                                                )}
                                                            />
                                                        </SubItem>
                                                    </span>
                                                </ArrowTooltip>
                                            ))}
                                    </Collapsable>
                                );

                            return (
                                <ArrowTooltip
                                    content={t(opc.tooltip?.title || '')}
                                    position="right"
                                    disabled={!(opc.showTooltip && opc.showTooltip(settings))}
                                    key={`link_to_${opc.key}`}
                                >
                                    <span
                                        onClick={() => {
                                            if (
                                                opc.isDisabled &&
                                                opc.isDisabled(settings) &&
                                                opc.disabledRedirectTo
                                            ) {
                                                navigate(opc.disabledRedirectTo);
                                            }
                                        }}
                                    >
                                        <Item
                                            component={Link}
                                            data-selected={opc.getRedirectTo(settings) === path}
                                            to={opc.getRedirectTo(settings)}
                                            disabled={
                                                opc.isDisabled && opc.isDisabled(settings)
                                                    ? true
                                                    : false
                                            }
                                        >
                                            <ListItemIcon>
                                                {opc.icon ?? getIcon(opc.key)}
                                            </ListItemIcon>
                                            <ListItemText primary={t(opc.label)} />
                                        </Item>
                                    </span>
                                </ArrowTooltip>
                            );
                        })}
                </List>
            </StyledOverlayScrollbars>

            <BottomContainer>
                <Item
                    sx={{
                        width: '100%',
                    }}
                    data-test-id="logoutButton"
                    onClick={() => {
                        if (logoutMutation.isIdle) {
                            logoutIfAllSaved();
                        }
                    }}
                >
                    {logoutMutation.isLoading ? (
                        <CircularProgress sx={{ color: 'white' }} size={24} />
                    ) : (
                        <LogOutIcon fill={Colors.Neutral5} />
                    )}

                    <span className={sideMenuStyles.labelFooter}>{t('commonLabels.logout')}</span>
                </Item>
                <AppInfoContainer>
                    <VersionInfo>
                        <strong>{t('commonLabels.enabledProducts')}</strong>: CM,PT{' '}
                        {t('commonLabels.enabledProductsItem')} AP <br /> <AppVersion />
                    </VersionInfo>
                </AppInfoContainer>
            </BottomContainer>
            <LeavePageConfirmationPopup
                open={showBlockLeavingConfirmation}
                onConfirm={() => {
                    setShowBlockLeavingConfirmation(false);
                    dispatch(resetReportChanges());
                    logoutMutation.mutate();
                }}
                onClose={() => {
                    setShowBlockLeavingConfirmation(false);
                }}
            />
        </>
    );
};

const VersionInfo = styled('div')(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
}));

const StyledOverlayScrollbars = styled(OverlayScrollbarsComponent)({
    width: '100%',
    overflowY: 'scroll',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    colorScheme: 'dark',
    color: '#fff',
});

/**
 * @deprecated this should be in the config (MenuOptions.ts) not here. DO NOT add anything to this.
 */
const getIcon = (key: string) => {
    switch (key) {
        case 'Settings':
            return <SettingsIcon fill="currentColor" />;
        case NavigationMenu.APPOINTMENTS:
            return <AppointmentsMenuIcon />;
        case NavigationMenu.WORKSHOP_PLANNER:
            return <BinnacleIcon fill="currentColor" />; //
        case NavigationMenu.ORDERS:
            return <CarIcon />;
        case NavigationMenu.CONVERSATIONS:
            return <ConversationIcon fill="currentColor" />;
        case NavigationMenu.ANALYSIS:
            return <AnalysisIcon />;
        case NavigationMenu.WORKSHOP:
            return <ClearMechanicIcon fill="currentColor" />;
        case NavigationMenu.VEHICLES:
            return <CarIcon />;
        case NavigationMenu.VEHICLE_DATABASE:
            return <CarIcon />;
        case NavigationMenu.TASKS:
            return <CalendarIcon fill="currentColor" />;
        case NavigationMenu.AFTERSALES_CRM:
            return <AftersalesCrmMenuIcon />;
        case NavigationMenu.SETTINGS_AFTERSALES_CRM:
            return <AftersalesCrmMenuIcon />;
        case NavigationMenu.BDC_INDICATORS:
            return <IndicatorsIcon fill="currentColor" />;
        case NavigationMenu.REPORTS:
            return <SortIcon fill="currentColor" />;
        case NavigationMenu.SURVEYS:
            return <PollsIcon fill="curentColor" />;
        case NavigationMenu.MASSIVESENDING:
            return <MegaphoneIcon fill="currentColor" />;
        case NavigationMenu.SETTINGS:
            return <SettingsIcon fill="currentColor" />;
        case NavigationMenu.CLEARMECHANIC:
            return <ClearMechanicIcon fill="currentColor" />;
        case NavigationMenu.PROSPECTIONS:
            return <ProspectionIcon fill="currentColor" />;
        case NavigationMenu.INDICATORS:
            return <IndicatorsIcon fill="currentColor" />;
        default:
            return <></>;
    }
};

export default SideMenuBody;

const Collapsable = ({
    title,
    isCollapse,
    children,
    icon,
    shouldBeExpanded,
}: {
    title: string;
    isCollapse: boolean;
    children?: JSX.Element | JSX.Element[];
    icon?: React.ReactNode;
    shouldBeExpanded: boolean;
}) => {
    const [isCollapsed, setIsCollapsed] = useState(!shouldBeExpanded && isCollapse);

    return (
        <>
            <Item onClick={() => setIsCollapsed((v) => !v)}>
                <ListItemIcon>{icon}</ListItemIcon>
                <ListItemText primary={title} />
                {isCollapsed ? <ExpandMore /> : <ExpandLess />}
            </Item>
            <Collapse timeout={200} in={!isCollapsed}>
                {children}
            </Collapse>
        </>
    );
};

function AppVersion() {
    const clickCounter = useRef(0);
    const [open, setOpen] = useState(false);

    function onClick() {
        clickCounter.current += 1;
        // open version info on 4th click
        if (clickCounter.current >= 4) {
            setOpen(true);
        }
    }

    function onClose() {
        setOpen(false);
        clickCounter.current = 0;
    }

    const { data: apiVersion } = useQuery({
        queryKey: ['api-version'],
        cacheTime: 0,
        staleTime: 0,
        queryFn: () =>
            Server.getResponse<string>('version', { responseType: 'text' }).then((r) => r.data),
        enabled: open,
    });

    return (
        <>
            <Box component="span" sx={{ cursor: 'default' }} role="button" onClick={onClick}>
                Version: {buildInfo.APP_VERSION}
            </Box>
            {open &&
                ReactDOM.createPortal(
                    <Backdrop
                        open
                        sx={{ zIndex: 4000 }}
                        onClick={(e) => {
                            if (e.target === e.currentTarget) {
                                onClose();
                            }
                        }}
                    >
                        <Box
                            sx={{
                                backgroundColor: 'var(--neutral1)',
                                p: 4,
                                left: '10vw',
                                right: '10vw',
                                bottom: 0,
                                position: 'fixed',
                                zIndex: 4000,
                            }}
                        >
                            <h2>CMOS Version Info</h2>
                            <p>Click outside to close </p>
                            <pre>
                                UI version: {buildInfo.APP_VERSION}
                                <br />
                                Build ID: {buildInfo.BUILD_ID}
                                <br />
                                Deployed at:{' '}
                                {buildInfo.BUILD_TIMESTAMP
                                    ? `${buildInfo.BUILD_TIMESTAMP} (${DateTime.fromMillis(
                                          buildInfo.BUILD_TIMESTAMP
                                      ).toString()})`
                                    : '---'}{' '}
                                <br />
                                Git Commit: {buildInfo.GIT_COMMIT}
                                <br />
                                Api version: {apiVersion || 'Loading...'}
                                <br />
                                VITE_NR_PROFILE: {import.meta.env.VITE_NR_PROFILE}
                                <br />
                                VITE_SHOW_VERSION_NOTICE: {import.meta.env.VITE_SHOW_VERSION_NOTICE}
                            </pre>
                        </Box>
                    </Backdrop>,
                    document.body
                )}
        </>
    );
}
