import { createContext, useContext, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import ChatButton from './ChatButton';
import EnterpriseChatButton from './EnterpriseChatButton';
import styles from './HeaderBar.module.css';
import NotificationsButton from './NotificationsButton';
import SettingsButton from './SettingsButton';

const HeaderButtonsContext = createContext<React.ReactNode>(undefined);
const SetHeaderButtonsContext = createContext<(value: React.ReactNode) => void>((_) => {});

export function CustomHeaderButtons({ children }: React.PropsWithChildren<{}>) {
    const setHeaderButtons = useContext(SetHeaderButtonsContext);

    useEffect(() => {
        setHeaderButtons(children);
        return () => {
            setHeaderButtons(undefined);
        };
    }, [setHeaderButtons, children]);

    return null;
}

export function CustomHeaderButtonsProvider({ children }: React.PropsWithChildren<{}>) {
    const [content, setContent] = useState<React.ReactNode>(null);
    return (
        <SetHeaderButtonsContext.Provider value={setContent}>
            <HeaderButtonsContext.Provider value={content}>
                {children}
            </HeaderButtonsContext.Provider>
        </SetHeaderButtonsContext.Provider>
    );
}

export default function HeaderButtons() {
    const content = useContext(HeaderButtonsContext);

    if (content) {
        return <>{content}</>;
    }

    return <HeaderButtonsDefaultContent />;
}

function HeaderButtonsDefaultContent() {
    const globalSettings = useSelector(selectSettings);

    return (
        <div className={styles.buttonGroup}>
            {globalSettings.appMode === 'RepairShop' && <NotificationsButton />}
            {globalSettings.appMode === 'RepairShop' && <ChatButton />}
            {globalSettings.appMode === 'Enterprise' && <EnterpriseChatButton />}
            <SettingsButton />
        </div>
    );
}
