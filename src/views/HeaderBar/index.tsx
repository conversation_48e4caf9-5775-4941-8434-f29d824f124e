import { Menu } from '@mui/icons-material';
import { IconButton, styled, useTheme } from '@mui/material';
import useResizeObserver from '@react-hook/resize-observer';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import styles from './HeaderBar.module.css';
import HeaderSlotProvider, {
    HeaderLoadingProvider,
    HeaderTitle,
    HeaderTitleProvider,
    MirrorHeaderTitleToDocumentTitle,
} from './context';

import { IconButtonProps } from '@mui/material';
import {
    CUSTOMIZABLE_BANNER_COLORS,
    CUSTOMIZABLE_BANNER_TEXT_COLORS,
} from 'common/constants/BannerColors';
import { IconSize } from 'common/styles/IconSize';
import moment from 'moment';
import {
    globalSettingsActions,
    selectIsCustomizableBannerClosed,
} from 'store/slices/globalSettingsSlice';
import { isDark } from 'utils/colors';
import HeaderButtons, { CustomHeaderButtonsProvider } from './HeaderButtons';
import HeaderLoadingBar from './LoadingBar';
import {
    CustomHeaderContent,
    useCurrentHeaderTitle,
    useHeaderLoading,
    useHeaderLoadingController,
    useHeaderTitleController,
} from './context';

export {
    CustomHeaderContent,
    useCurrentHeaderTitle,
    useHeaderLoading,
    useHeaderLoadingController,
    useHeaderTitleController,
};

type HeaderBarProps = React.PropsWithChildren<{
    onOpenSideBar?: () => void;
}>;

const selectShowOverdueMessage = (r: RootState) =>
    r.globalSettings.settings?.repairShopSettings?.features.showAccountOverdueMessage ?? false;

const selectCustomizableBanner = (r: RootState) => r.globalSettings.settings?.customizableBanner;

const selectUtcOffset = (r: RootState) =>
    r.globalSettings.settings?.internationalization.timeZoneOffset;

function HeaderBar({ onOpenSideBar, children }: HeaderBarProps) {
    const dispatch = useAppDispatch();
    const showOverdueMessage = useAppSelector(selectShowOverdueMessage);
    const customizableBanner = useAppSelector(selectCustomizableBanner);
    const utcOffset = useAppSelector(selectUtcOffset);
    const isCustomizableBannerClosed = useAppSelector(selectIsCustomizableBannerClosed);
    const [customHeaderRoot, setCustomHeaderRoot] = useState<HTMLElement | null>(null);
    const theme = useTheme();
    const containerRef = useRef<HTMLDivElement | null>(null);
    useResizeObserver(containerRef.current, (x) =>
        document.body.style.setProperty('--header-height', `${x.contentRect.height}px`)
    );

    const customizableBannerIsExpired = useMemo(() => {
        if (!customizableBanner) {
            return false;
        }

        let now = new Date();
        now = new Date(
            now.getUTCFullYear(),
            now.getUTCMonth(),
            now.getUTCDate(),
            now.getUTCHours(),
            now.getUTCMinutes(),
            now.getUTCSeconds(),
            now.getUTCMilliseconds()
        );

        if (utcOffset) {
            now = new Date(now.getTime() + utcOffset * 60000);
        }
        now.setHours(12, 0, 0, 0);

        const startDate = moment(customizableBanner.startDate).toDate();
        startDate.setHours(12, 0, 0, 0);

        const endDate = moment(customizableBanner.endDate).toDate();
        endDate.setHours(12, 0, 0, 0);

        if (now < startDate || now > endDate) {
            return false;
        }

        return true;
    }, [customizableBanner, utcOffset]);

    return (
        <>
            <HeaderLoadingProvider>
                <CustomHeaderButtonsProvider>
                    <HeaderTitleProvider>
                        <MirrorHeaderTitleToDocumentTitle />
                        <div className={styles.container} ref={containerRef}>
                            <div className={styles.root}>
                                <div className={styles.titleGroup}>
                                    <OpenSidebarButton
                                        disableRipple
                                        // open sidebar on mousedown event instead of click
                                        // will make it seem like it's faster than it actually is
                                        onMouseDown={onOpenSideBar}
                                        size="large"
                                    >
                                        <Menu />
                                    </OpenSidebarButton>
                                    <span className={styles.title}>
                                        <HeaderTitle />
                                    </span>
                                </div>

                                <div ref={setCustomHeaderRoot} className={styles.customHeader} />
                                <HeaderButtons />
                            </div>
                            <div className={styles.loadingBarContainer}>
                                <HeaderLoadingBar />
                            </div>
                            {showOverdueMessage && (
                                <div className={styles.overdueBanner}>
                                    <Trans
                                        i18nKey="headerOptions.accountOverdueMessage"
                                        values={{
                                            email: '<EMAIL>',
                                            percentage: 20,
                                        }}
                                        // eslint-disable-next-line jsx-a11y/anchor-has-content, jsx-a11y/anchor-is-valid
                                        components={{ 1: <a /> }}
                                    />
                                </div>
                            )}
                            {customizableBanner &&
                                !isCustomizableBannerClosed &&
                                customizableBannerIsExpired && (
                                    <CustomizableBanner
                                        textColor={
                                            CUSTOMIZABLE_BANNER_TEXT_COLORS[
                                                customizableBanner.textBannerColorId
                                            ]
                                        }
                                        bannerColor={
                                            CUSTOMIZABLE_BANNER_COLORS[
                                                customizableBanner.bannerColorId
                                            ]
                                        }
                                        className={styles.customizableBanner}
                                    >
                                        <CustomizableBannerBody
                                            dangerouslySetInnerHTML={{
                                                __html: customizableBanner.bannerBody,
                                            }}
                                        />
                                        <StyledIconButton
                                            onClick={() => {
                                                dispatch(
                                                    globalSettingsActions.setIsCustomizableBannerClosed(
                                                        true
                                                    )
                                                );
                                            }}
                                        >
                                            <CloseIcon
                                                size={IconSize.LL}
                                                fill={
                                                    isDark(
                                                        CUSTOMIZABLE_BANNER_COLORS[
                                                            customizableBanner.bannerColorId
                                                        ]
                                                    )
                                                        ? theme.palette.neutral[9]
                                                        : theme.palette.neutral[6]
                                                }
                                            />
                                        </StyledIconButton>
                                    </CustomizableBanner>
                                )}
                        </div>
                        <HeaderSlotProvider slot={customHeaderRoot}>{children}</HeaderSlotProvider>
                    </HeaderTitleProvider>
                </CustomHeaderButtonsProvider>
            </HeaderLoadingProvider>
        </>
    );
}

const OpenSidebarButton = styled(IconButton)({
    color: 'var(--neutral5)',
    transition: 'none',
    ':hover': {
        color: '#000',
        backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
});

const CustomizableBanner = styled('div')<{ textColor: string; bannerColor: string }>(
    ({ textColor, bannerColor }) => ({
        backgroundColor: bannerColor,
        color: textColor,
        display: 'flex',
    })
);

const CustomizableBannerBody = styled('div')({
    width: '100%',
    padding: '18px 0px',
});

const StyledIconButton = styled(({ ...props }: IconButtonProps) => <IconButton {...props} />)({
    padding: '5px 0px',
    height: 'min-content',
});

export default HeaderBar;
