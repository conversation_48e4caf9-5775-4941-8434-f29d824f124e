import { useMutation } from '@tanstack/react-query';
import { Button } from 'common/components/Button';
import { TextFormField } from 'common/components/Inputs';
import PasswordField from 'common/components/Inputs/PasswordField';
import { ENTERPRISE_ROUTES, ROUTES } from 'common/constants/RoutesDefinition';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useQueryParam from 'common/hooks/useQueryParam';
import { useEffect, useMemo, useRef, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { resetState } from 'store/slices/chatButton';
import { resetState as resetEnterpriseState } from 'store/slices/enterprise/chatButton';
import { loginThunk, selectUser, useCurrentUserOptional, UserState } from 'store/slices/user';
import nr from 'utils/nr';
import NamePlaque from './NamePlaque';
import styles from './styles.module.css';

import { Box, Paper, styled } from '@mui/material';
import { motion, useAnimation } from 'framer-motion';
import ReactDOM from 'react-dom';
import { useStore } from 'react-redux';
import { globalSettingsActions, selectSettings } from 'store/slices/globalSettingsSlice';
import LoginLayout from './LoginLayout';

export { LoginLayout };

const Login = () => {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const isEnterprise = useIsEnterpriseRoute();
    const navigate = useNavigate();
    const store = useStore();
    const getNextRoute = useNextRoute();
    const user = useCurrentUserOptional();
    const [mail, setMail] = useState('');
    const [password, setPassword] = useState('');

    const loginMutation = useMutation(
        async () => {
            const result = await dispatch(
                loginThunk({
                    username: mail,
                    password,
                    isEnterprise,
                })
            );
            if (result.meta.requestStatus === 'rejected') {
                throw result.payload;
            }
        },
        {
            onSuccess() {
                if (isEnterprise) {
                    dispatch(resetEnterpriseState());
                } else {
                    dispatch(resetState());
                }

                dispatch(globalSettingsActions.setIsCustomizableBannerClosed(false));
                const user = selectUser(store.getState());
                navigate(getNextRoute(user));
            },
        }
    );

    const onEnterKeyPressHelper = () => {
        loginMutation.mutate();
    };

    const firstRender = useRef(true);
    useEffect(() => {
        if (!firstRender.current) return;
        firstRender.current = true;

        if (user) {
            nr('redirect.alreadyLoggedIn');
            navigate(getNextRoute(user));
        }
    }, [user, getNextRoute, navigate]);

    return (
        <>
            <NamePlaque />
            <Box sx={{ minWidth: 'min(360px, 92%)', alignSelf: 'center' }}>
                <div className={styles.logo} />
                <TextFormField
                    dataTestId="email"
                    size="medium"
                    label={t('login.emailLabel')}
                    placeholder={t('login.emailPlaceholderLabel')}
                    onChange={(e) => setMail(e.target.value)}
                    type={'text'}
                    value={mail}
                    isRequired={true}
                    isInvalid={loginMutation.isError}
                    onEnterPress={onEnterKeyPressHelper}
                    enableEnterComplete={true}
                />
                <PasswordField
                    dataTestId="password"
                    size="medium"
                    label={t('login.passwordLabel')}
                    placeholder={t('login.passwordPlaceholderLabel')}
                    onChange={(e) => setPassword(e.target.value)}
                    value={password}
                    isRequired={true}
                    isInvalid={loginMutation.isError}
                    onEnterPress={onEnterKeyPressHelper}
                    enableEnterComplete={true}
                />
                <div className={styles.row} style={{ marginTop: '23px' }}>
                    <Button
                        label={t('login.buttonLoginLabel')}
                        cmosVariant={'filled'}
                        cmosSize={'large'}
                        blockMode
                        showLoader={loginMutation.isLoading}
                        onClick={() => loginMutation.mutate()}
                    />
                </div>
                <div
                    className={styles.row}
                    style={{
                        alignItems: 'center',
                        justifyContent: 'space-around',
                        marginTop: '47px',
                    }}
                >
                    {!isEnterprise && (
                        <NavLink className={styles.link} to={ROUTES.RESET_PASSWORD}>
                            {t('login.forgotMyPassword')}
                        </NavLink>
                    )}
                </div>
            </Box>

            {ReactDOM.createPortal(<ReasonToast />, document.body)}
        </>
    );
};

export default Login;

function useNextRoute(): (user: UserState['user'] | null) => string {
    const settings = useAppSelector(selectSettings);
    const [next] = useQueryParam('next');
    const isEnterprise = useIsEnterpriseRoute();

    if (next) {
        return () => next;
    }

    if (isEnterprise) {
        return () => ENTERPRISE_ROUTES.ORDERS;
    }

    return (user) => {
        if (
            settings.repairShopSettings &&
            settings.repairShopSettings.features.enableAftersalesCrm
        ) {
            if (user?.jobTitle === 'BdcAdvisor' || user?.jobTitle === 'BdcSupervisor') {
                return ROUTES.VEHICLES;
            }
        }

        return ROUTES.ORDERS;
    };
}

const AnimatedDivToast = motion(
    styled(Paper)({
        position: 'fixed',
        left: '50%',
        top: 40,
        padding: '10px 40px',
        textAlign: 'center',
        borderRadius: 12,
        maxWidth: 370,
        overflow: 'hidden',
        whiteSpace: 'pre-line',
    })
);

function ReasonToast() {
    const [reasonParam, setReasonQueryParam] = useQueryParam('reason');
    const reason = useRef(reasonParam).current;
    const { t } = useAppTranslation();

    const message = useMemo(() => {
        if (reason === '401' || reason === '403') {
            return t('login.loggedOutDueToLackOfPerms');
        }
    }, [reason, t]);

    const animate = useAnimation();
    const progressLineAnimate = useAnimation();

    useEffect(() => {
        setReasonQueryParam(null, true);
        if (!message) return;

        animate.set({
            scale: 0,
            translateX: '-50%',
        });
        animate.start(
            {
                scale: 1,
                translateX: '-50%',
            },
            { duration: 0.3 }
        );
    }, [animate, message, progressLineAnimate, setReasonQueryParam]);

    if (!message) return;

    return <AnimatedDivToast animate={animate}>{message}</AnimatedDivToast>;
}
