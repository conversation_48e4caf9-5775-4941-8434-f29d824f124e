import { styled } from '@mui/material';
import background from 'assets/images/backgroundLogin.png';
import Footer from 'common/components/Footer';
import { motion } from 'framer-motion';

type LoginLayoutProps = React.PropsWithChildren<{}>;

const Main = styled('main')(({ theme }) => ({
    margin: 'auto',
    width: 'min(90vw, 1088px)',
    minWidth: 'min(90vw, 680px)',
    height: '550px',
    backgroundColor: '#fff',
    boxShadow: '15px 30px 80px 0 #f2f3f7',
    backgroundPosition: 'bottom',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    display: 'flex',
    alignItems: 'stretch',
    flexDirection: 'column',
    borderRadius: 10,

    [theme.breakpoints.up('md')]: {
        backgroundImage: `url(${background})`,
        height: '600px',
    },
    [theme.breakpoints.up('lg')]: {
        height: '650px',
    },
}));

const MainAnimated = motion(Main);

export default function LoginLayout({ children }: LoginLayoutProps) {
    return (
        <>
            <MainAnimated
                initial={{
                    scale: 0.98,
                    opacity: 0,
                }}
                transition={{
                    duration: 0.3,
                }}
                animate={{
                    scale: 1,
                    opacity: 1,
                }}
            >
                {children}
            </MainAnimated>
            <Footer />
        </>
    );
}
