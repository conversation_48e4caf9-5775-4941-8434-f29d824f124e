import { styled } from '@mui/material';
import ReactDOM from 'react-dom';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { showVersionInfo } from 'utils/debug';

let NamePlaque: React.ComponentType<{}> = () => null;

if (import.meta.env.VITE_SHOW_VERSION_NOTICE === 'True') {
    if (showVersionInfo()) {
        const Root = styled('div')(({ theme }) => ({
            position: 'fixed',
            left: 20,
            top: 20,
            ...theme.typography.h7Inter,
            color: theme.palette.neutral[8],
            padding: 10,
            borderRadius: 14,
            transition: 'opacity 0.3s',
            zIndex: 10,
        }));

        NamePlaque = () => {
            const { appMode, name, uid, id } = useAppSelector(selectSettings);

            return ReactDOM.createPortal(
                <Root>
                    You are logging in into "{name}" <br />
                    ID={appMode === 'RepairShop' ? uid : id}, appMode={appMode} <br />
                    THIS IS ONLY VISIBLE IN UAT
                </Root>,
                document.body
            );
        };
    }
}

export default NamePlaque;
