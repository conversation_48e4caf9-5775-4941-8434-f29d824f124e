import { IconButton, InputAdornment, styled } from '@mui/material';
import OrderAPI from 'api/Order';
import { DiscountType } from 'api/settings/OrdersSettings';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import TextFormField from 'common/components/Inputs/TextField';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { orderActions, selectIsOrderClosed, selectOrder } from 'store/slices/order/orderDetails';
import { selectEstimatesSet, selectItemsSet } from 'store/slices/order/orderInspectionFormsSlice';
import { selectUserPermission } from 'store/slices/user/selectors';
import { calculateSubtotal } from '../InspectionForms/helper';

interface RowProps {
    title: string;
    value: number | null;
    isNegative?: boolean;
    requireDecimals?: boolean;
    type: DiscountType;
}

export const TotalEstimates = () => {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const items = useSelector(selectItemsSet);
    const entries = useMemo(
        () =>
            Object.entries(items)
                .filter(
                    (item) =>
                        item[1].priority !== PriorityLevel.Low &&
                        item[1].priority !== PriorityLevel.NA
                )
                .map((item) => item[1]),
        [items]
    );
    const userPermission = useSelector(selectUserPermission);
    const objEstimates = useSelector(selectEstimatesSet);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);
    const [isModified, setIsModified] = useState<boolean>(false);
    const repairOrder = useSelector(selectOrder);
    const [discount, setDiscount] = useState<number>(0);
    const { repairShopSettings } = useSelector(selectSettings);

    const requireDecimals = useMemo(
        () => (repairShopSettings?.features.enableRemoveDecimals ? false : true),
        [repairShopSettings]
    );

    const roundAwayFromZero = useCallback(
        (value: number): number => {
            return repairShopSettings?.features.enableRemoveDecimals ? Math.round(value) : value;
        },
        [repairShopSettings]
    );

    const estimates = useMemo(() => {
        if (!entries.length) {
            return {
                discount: 0,
                totalCost: 0,
                totalTaxes: 0,
                totalCostWithTaxes: 0,
                totalApproved: 0,
                totalDeclined: 0,
                pendingCost: 0,
            };
        }

        const estimateEntries = entries.map((item) => {
            const estimatesSubtotals = item.estimates.map((id) =>
                calculateSubtotal(objEstimates[id], requireDecimals, repairOrder?.discountType)
            );

            return {
                isApproved: item.isApproved,
                isDeclined: item.isDeclined,
                name: item.name,
                total: estimatesSubtotals.length ? estimatesSubtotals.reduce((a, b) => a + b) : 0,
            };
        });

        let totalCost = roundAwayFromZero(
            estimateEntries
                .map((item) => item.total)
                .reduce((a, b) => {
                    return a + b;
                })
        );

        const discount =
            repairOrder?.discountType === 'Currency'
                ? roundAwayFromZero(repairOrder?.discount || 0)
                : repairOrder?.discount || 0;

        if (discount > 0 && totalCost > 0) {
            if (repairOrder?.discountType === 'Currency') {
                totalCost = totalCost - discount;
            } else if (repairOrder?.discountType === 'Percentage') {
                totalCost = totalCost * ((100 - discount) / 100);
            }
        }

        const taxPercentage = repairShopSettings?.taxPercentage;

        const totalTaxes = roundAwayFromZero((totalCost * (taxPercentage ?? 0)) / 100);

        const totalCostWithTaxes = roundAwayFromZero(totalCost + totalTaxes);

        let totalApprovedWithoutTaxes = roundAwayFromZero(
            estimateEntries.filter((item) => item.isApproved).length
                ? estimateEntries
                      .filter((item) => item.isApproved)
                      .map((item) => item.total)
                      .reduce((a, b) => {
                          return a + b;
                      })
                : 0
        );

        if (discount > 0 && totalApprovedWithoutTaxes > 0) {
            if (repairOrder?.discountType === 'Currency') {
                totalApprovedWithoutTaxes = totalApprovedWithoutTaxes - discount;
            } else if (repairOrder?.discountType === 'Percentage') {
                totalApprovedWithoutTaxes = totalApprovedWithoutTaxes * ((100 - discount) / 100);
            }
        }

        const totalApproved = roundAwayFromZero(
            taxPercentage
                ? totalApprovedWithoutTaxes +
                      (totalApprovedWithoutTaxes * (taxPercentage ?? 0)) / 100
                : totalApprovedWithoutTaxes
        );

        let totalDeclinedWithoutTaxes = roundAwayFromZero(
            estimateEntries.filter((item) => item.isDeclined).length
                ? estimateEntries
                      .filter((item) => item.isDeclined)
                      .map((item) => item.total)
                      .reduce((a, b) => {
                          return a + b;
                      })
                : 0
        );

        if (discount > 0 && totalDeclinedWithoutTaxes > 0) {
            if (repairOrder?.discountType === 'Currency') {
                totalDeclinedWithoutTaxes = totalDeclinedWithoutTaxes - discount;
            } else if (repairOrder?.discountType === 'Percentage') {
                totalDeclinedWithoutTaxes = totalDeclinedWithoutTaxes * ((100 - discount) / 100);
            }
        }

        const totalDeclined = roundAwayFromZero(
            taxPercentage
                ? totalDeclinedWithoutTaxes +
                      (totalDeclinedWithoutTaxes * (taxPercentage ?? 0)) / 100
                : totalDeclinedWithoutTaxes
        );

        let pendingCostWithoutTaxes = roundAwayFromZero(
            estimateEntries.filter((item) => !item.isApproved && !item.isDeclined).length
                ? estimateEntries
                      .filter((item) => !item.isApproved && !item.isDeclined)
                      .map((item) => item.total)
                      .reduce((a, b) => {
                          return a + b;
                      })
                : 0
        );

        if (discount > 0 && pendingCostWithoutTaxes > 0) {
            if (repairOrder?.discountType === 'Currency') {
                pendingCostWithoutTaxes = pendingCostWithoutTaxes - discount;
            } else if (repairOrder?.discountType === 'Percentage') {
                pendingCostWithoutTaxes = pendingCostWithoutTaxes * ((100 - discount) / 100);
            }
        }

        const pendingCost = roundAwayFromZero(
            taxPercentage
                ? pendingCostWithoutTaxes + (pendingCostWithoutTaxes * (taxPercentage ?? 0)) / 100
                : pendingCostWithoutTaxes
        );

        return {
            discount,
            totalCost,
            totalTaxes,
            totalCostWithTaxes,
            totalApproved,
            totalDeclined,
            pendingCost,
        };
    }, [
        entries,
        objEstimates,
        repairOrder?.discount,
        repairOrder?.discountType,
        repairShopSettings?.taxPercentage,
        requireDecimals,
        roundAwayFromZero,
    ]);

    const saveDiscount = async () => {
        if (repairOrder) {
            const result = await OrderAPI.save({
                repairOrderId: repairOrder.repairOrderId,
                discount: discount,
            });

            if (result?.orderInfo) {
                dispatch(orderActions._setDiscount(result.orderInfo.discount));
                setIsModified(false);
            }
        }
    };

    useEffect(() => {
        if (repairOrder) {
            setDiscount(repairOrder.discount || 0);
        } else {
            setDiscount(0);
        }
    }, [repairOrder]);

    return (
        <div style={{ display: 'flex' }}>
            <TotalEstimatesContainer style={{ marginTop: 5 }}>
                {isModified ? (
                    <DiscountTextField>
                        <TextFieldArea>
                            <TextFormField
                                autoFocus
                                cmosVariant="roundedPrimary"
                                name="discount"
                                type="number"
                                value={`${discount}`}
                                isRequired
                                endAdornment={
                                    <InputAdornment position="end">
                                        <IconButton onClick={saveDiscount}>
                                            <CheckIcon />
                                        </IconButton>
                                    </InputAdornment>
                                }
                                onChange={(event) => setDiscount(+event.target.value)}
                                onEnterPress={saveDiscount}
                                onEscPress={() => {
                                    if (userPermission.allowEditOrders) {
                                        setIsModified(false);
                                        setDiscount(estimates.discount || 0);
                                    }
                                }}
                            />
                        </TextFieldArea>
                        <Button
                            cmosVariant={'typography'}
                            iconPosition="right"
                            Icon={() => <CloseIcon fill={Colors.Neutral6} />}
                            onClick={() => {
                                if (userPermission.allowEditOrders) {
                                    setIsModified(false);
                                    setDiscount(estimates.discount || 0);
                                }
                            }}
                        />
                    </DiscountTextField>
                ) : (
                    <Row
                        title={t('orderDetails.totalEstimates.discount')}
                        value={estimates.discount}
                        requireDecimals={requireDecimals}
                        isNegative
                        type={repairOrder!.discountType}
                    />
                )}

                <Row
                    title={t('orderDetails.totalEstimates.subtotal')}
                    value={estimates.totalCost}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />
                <Row
                    title={t('orderDetails.totalEstimates.taxes')}
                    value={estimates.totalTaxes}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />
                <Row
                    title={t('orderDetails.totalEstimates.total')}
                    value={estimates.totalCostWithTaxes}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />

                <Line />

                <Row
                    title={t('orderDetails.totalEstimates.approved')}
                    value={estimates.totalApproved}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />
                <Row
                    title={t('orderDetails.totalEstimates.declined')}
                    value={estimates.totalDeclined}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />
                <Row
                    title={t('orderDetails.totalEstimates.pending')}
                    value={estimates.pendingCost}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />
                <Row
                    title={t('orderDetails.totalEstimates.total')}
                    value={estimates.totalCostWithTaxes}
                    requireDecimals={requireDecimals}
                    type="Currency"
                />
            </TotalEstimatesContainer>
            {userPermission.allowEditOrders && (
                <IconButtonWrapper
                    style={isModified || orderIsClosed ? { visibility: 'hidden' } : undefined}
                    onClick={() => {
                        setIsModified(true);
                    }}
                >
                    <EditIcon />
                </IconButtonWrapper>
            )}
        </div>
    );
};

const Row = ({ title, value, isNegative, type, requireDecimals = true }: RowProps) => {
    const { internationalization } = useSelector(selectSettings);

    return (
        <RowContainer>
            <Title>{title}</Title>
            <Value>
                {isNegative ? '-' : ''}
                {type === 'Percentage'
                    ? (value ?? 0) + '%'
                    : InternationalizationLogic.numberToCurrency(internationalization, value ?? 0, {
                          allowZero: true,
                          requireDecimals: requireDecimals,
                      })}
            </Value>
        </RowContainer>
    );
};

const RowContainer = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
});

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h6,
    color: theme.palette.neutral[8],
    fontWeight: 'bold',
}));

const Value = styled('div')(({ theme }) => ({
    ...theme.typography.h5,
    color: theme.palette.primary.main,
    fontWeight: 'bold',
}));

const TotalEstimatesContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    rowGap: 10,
    minWidth: 231,
});

const Line = styled('div')(({ theme }) => ({
    height: 1,
    backgroundColor: theme.palette.neutral[4],
    marginTop: 11,
    marginBottom: 11,
}));

const IconButtonWrapper = styled('span')({
    marginLeft: 7,
    width: 24,
    height: 24,
    cursor: 'pointer',
});

const DiscountTextField = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
});

const TextFieldArea = styled('div')({
    width: 200,
});

export default TotalEstimates;
