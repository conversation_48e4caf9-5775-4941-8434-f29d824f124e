import { CircularProgress, Divider } from '@mui/material';
import OrderAPI from 'api/Order';
import { useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { orderActions, selectOrder } from 'store/slices/order/orderDetails';
import EstimateNotes from '../EstimateNotes';
import InspectionForms from '../InspectionForms';
import TotalEstimates from '../TotalEstimates';
import useEstimateTabStyles from './css';

export default function EstimateTab() {
    const order = useAppSelector(selectOrder);
    const dispatch = useAppDispatch();
    const styles = useEstimateTabStyles();

    const { id: idString } = useParams<{ id: string }>();
    const id = +(idString ?? '');

    if (!order) return <CircularProgress />;

    const handleShowEstimatesChange = async (value: boolean) => {
        if (!order) return;

        const response = await OrderAPI.showEstimatesChange({
            repairOrderId: order.repairOrderId,
            showEstimates: value,
        });
        if (response) {
            dispatch(orderActions._setShowEstimates(response.showEstimates));
        }
    };

    return (
        <>
            <div style={{ marginBottom: 30 }}>
                <InspectionForms
                    repairOrderId={id}
                    repairOrderNumber={order?.repairOrderNumber ?? ''}
                    showEstimates={order.showEstimates ?? false}
                    onShowEstimatesChange={handleShowEstimatesChange}
                />
            </div>
            <Divider />
            <div className={styles.bottomArea}>
                <EstimateNotes orderId={id} />
                <TotalEstimates />
            </div>
        </>
    );
}
