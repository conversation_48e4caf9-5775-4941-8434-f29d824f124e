import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import NotFoundImage from 'views/NotFound/Image';

const Inner = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
    overflow: 'hidden',
    position: 'relative',
    marginTop: -20,

    '& > img': {
        marginBottom: 30,
    },
}));

const Header = styled('h4')(({ theme }) => ({
    ...theme.typography.h3Inter,
    margin: '8px 0',
}));

const Content = styled('div')({
    margin: '0 40px 0 40px',
    position: 'absolute',
    top: 60,
    right: 0,
    width: '50%',
});

const ImageContainer = styled('div')({
    display: 'flex',
});

export default function OrderNotFound() {
    const { id } = useParams<{ id: string }>();
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const rootRef = useRef<HTMLDivElement>(null);

    const handleSvgRefCallback = useCallback((svg: SVGSVGElement) => {
        const root = rootRef.current;

        if (!root || !svg) return;

        // stretch SVG to fit the parent element
        const width = root.getBoundingClientRect().width;
        svg.viewBox.baseVal.width = width;
        svg.style.width = `${width}px`;
    }, []);

    return (
        <Inner ref={rootRef}>
            <ImageContainer>
                <NotFoundImage viewBox="-100 0 564 492" ref={handleSvgRefCallback} />
            </ImageContainer>

            <Content>
                <Header>{t('notFound.haveProblem')}</Header>
                <p>Order with ID "{id}" not found</p>
                <Box sx={{ display: 'flex', marginTop: 10, alignItems: 'center', gridGap: 2 }}>
                    <Button
                        w="md"
                        onClick={() => navigate(-1)}
                        label={t('notFound.goBack')}
                        cmosVariant="filled"
                        cmosSize="large"
                    />
                </Box>
            </Content>
        </Inner>
    );
}
