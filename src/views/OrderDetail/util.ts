import { useQuery } from '@tanstack/react-query';
import { EnterpriseOrdersApi, EnterpriseOrderTypesApi } from 'api/enterprise/orders';
import { OrderTypesApi, PhaseDto } from 'api/orders';
import WpPhasesApi from 'api/workshopPlanner/phases';
import { Phases } from 'common/constants';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { TFunction } from 'i18next';
import { useAppSelector } from 'store';
import { selectOrderShopKey } from 'store/slices/order/orderDetails';

export function useOrderPhases() {
    const isEnterprise = useIsEnterpriseRoute();
    const shopKey = useAppSelector(selectOrderShopKey);
    const enabled = isEnterprise ? !!shopKey : true;
    const queryKey = isEnterprise ? ['enterprise', 'phases', shopKey] : ['phases'];
    const queryFn = isEnterprise
        ? () => EnterpriseOrdersApi.phases.getByShopId(shopKey!)
        : () => WpPhasesApi.getPhases();
    const { data } = useQuery(queryKey, queryFn, { enabled });
    return data ?? [];
}

export function getPhaseName(phase: PhaseDto, t: TFunction): string {
    if (phase.id === Phases.Closed) {
        return t('orderDetails.closedOrder');
    } else if (phase.id === Phases.NoPhase) {
        return t('orderDetails.noPhase');
    } else {
        return phase.name;
    }
}

export function useOrderTypes() {
    const isEnterprise = useIsEnterpriseRoute();
    const shopId = useAppSelector(selectOrderShopKey);
    const enabled = isEnterprise ? !!shopId : true;

    const queryKey = isEnterprise ? ['enterprise', 'order-types', shopId] : ['order-types'];
    const queryFn = isEnterprise
        ? () => EnterpriseOrderTypesApi.getList(shopId!)
        : () => OrderTypesApi.getList();
    const { data } = useQuery(queryKey, queryFn, { initialData: [], enabled });
    return data;
}
