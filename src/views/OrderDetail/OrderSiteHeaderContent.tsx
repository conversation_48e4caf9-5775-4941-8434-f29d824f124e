import { makeStyles } from '@mui/styles';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import React, { useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectOrder, selectOrderData } from 'store/slices/order/orderDetails';
import { CustomHeaderContent } from 'views/HeaderBar';
import TowerNumber from 'views/WorkshopPlanner/common/TowerNumber';
import { getPhaseName, useOrderPhases, useOrderTypes } from './util';

const selectData = createSelector([selectOrderData, selectOrder], (s, order) => ({
    customer: `${s.customer.firstName} ${s.customer.lastName}`.trim(),
    vehicle: `${s.vehicle.brand ?? ''} ${s.vehicle.model ?? ''} ${s.vehicle.year ?? ''}`.trim(),
    vin: s.vehicle.vin,
    tower: s.tower,
    phaseId: s.phaseId,
    orderTypeId: s.orderTypeId,
    hasOrder: !!order,
    inChargeUserId: s.inChargeUserId,
    appointmentReasonColor: order?.appointmentReasonColor || null,
}));

// const Row = styled(Breadcrumbs)(({ theme }) => ({
//     color: theme.palette.neutral[6],
//     ...theme.typography.h5Inter,
//     fontWeight: 'normal',
//     overflowX: 'hidden',
//     flexWrap: 'nowrap',
// }));

module row {
    const useStyles = makeStyles((theme) => ({
        root: {
            display: 'flex',
            alignItems: 'center',
            color: theme.palette.neutral[6],
            ...theme.typography.h5Inter,
            fontWeight: 'normal',
            overflowX: 'hidden',
            flexWrap: 'nowrap',
            whiteSpace: 'nowrap',
        },
        delimiter: {
            padding: '0 6px',
        },
    }));

    /**
     * Basically just a custom Breadcrumb element. Except Breadcrumb is
     * a "nav" tag and should only be used for navigation and this thing
     * is just a div.
     */
    export function Row({ children }: React.PropsWithChildren<{}>) {
        const styles = useStyles();
        return (
            <div className={styles.root}>
                {React.Children.map(children, (child, index) => (
                    <>
                        <div className={styles.delimiter}>/</div>
                        {child}
                    </>
                ))}
            </div>
        );
    }
}

export default function OrderSiteHeaderContent() {
    const { t } = useAppTranslation();
    const {
        customer,
        vehicle,
        vin,
        tower,
        phaseId,
        orderTypeId,
        hasOrder,
        inChargeUserId,
        appointmentReasonColor,
    } = useAppSelector(selectData, isEqual);
    const phases = useOrderPhases();
    const phaseName = useMemo(() => {
        const phase = phases.find((x) => x.id === phaseId);
        if (!phase) return '';
        return getPhaseName(phase, t);
    }, [t, phaseId, phases]);

    const orderTypes = useOrderTypes();
    const orderTypeName = useMemo(
        () => orderTypes.find((x) => x.key === orderTypeId)?.name,
        [orderTypes, orderTypeId]
    );

    if (!hasOrder) return null;

    return (
        <CustomHeaderContent>
            <row.Row>
                <span>{customer || '--'}</span>
                <span>{vehicle || '--'}</span>
                <span>{vin || '--'}</span>
                {tower ? (
                    <TowerNumber
                        userIdOrKey={inChargeUserId}
                        orderTypeKey={orderTypeId}
                        appointmentReasonColor={appointmentReasonColor}
                        towerNumber={tower}
                    />
                ) : (
                    <span>--</span>
                )}
                <span>{phaseName}</span>
                <span>{orderTypeName ?? '--'}</span>
            </row.Row>
        </CustomHeaderContent>
    );
}
