import { Box, Divider, styled, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { getErrorMessage } from 'api/error';
import { PackageAppliedDto, PackageMatchDto, PackagesAppliedDto } from 'api/orders';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { Button } from 'common/components/Button';
import { Checkbox, CheckboxProps } from 'common/components/Inputs';
import { Modal } from 'common/components/Modal';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { usePostHog } from 'common/hooks/usePostHog';
import useToasters from 'common/hooks/useToasters';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { PropsWithChildren, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectOrderData, selectOrderNumber } from 'store/slices/order/orderDetails';
import { selectDisabledPackageItems } from 'store/slices/order/orderInspectionFormsSlice';
import { applyPackagesThunk } from 'store/slices/order/orderInspectionFormsSlice/thunks';

export type PackagesPopupProps = {
    packages: PackageMatchDto[];
    open: boolean;
    onClose: () => void;
    triggeredByMasterItemId: number;
};

export default function PackagesPopup({
    packages,
    open,
    onClose,
    triggeredByMasterItemId,
}: PackagesPopupProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { internationalization } = useAppSelector(selectSettings);
    const disabledPackages = useAppSelector((r) => selectDisabledPackageItems(r, packages));
    const orderNumber = useAppSelector(selectOrderNumber);
    const orderData = useAppSelector(selectOrderData);

    const toasters = useToasters();

    const { capturePosthogIdentifiedEvent } = usePostHog();

    const [selected, setSelected] = useState<Record<string, { items: Record<string, boolean> }>>(
        {}
    );

    const addPackages = useMutation(
        async (
            packagesToApply: {
                packageId: string;
                items: string[];
            }[]
        ) => {
            const result = await dispatch(
                applyPackagesThunk({
                    packages: packagesToApply.map((p) => ({
                        id: p.packageId,
                        cfg: {
                            selectedItems: p.items,
                        },
                    })),
                })
            );

            if (result.meta.requestStatus === 'rejected') {
                throw new Error(
                    'failed to apply packages: ' +
                        packagesToApply.map((x) => x.packageId).join(', ')
                );
            } else if (result.meta.requestStatus === 'fulfilled') {
                const response = result.payload as PackagesAppliedDto;
                addPostHogPackagesLog(response.packages);

                const failedPackagesPairs = Object.entries(response.failedPackages);

                const errors = failedPackagesPairs.map(([packageId, error]) => {
                    const packageMatch = packages.find((x) => x.id === packageId);
                    const packageName = packageMatch ? packageMatch.name : packageId;
                    return { packageName, error };
                });

                if (errors.length > 0) {
                    addPostHogFailedPackagesLog(errors);
                    console.error(`Failed to apply ${errors.length} packages:`, errors);
                    throw new Error(
                        'failed to apply packages: ' + errors.map((x) => x.packageName).join(', ')
                    );
                }
            }
        }
    );

    const onSave = () => {
        const selectedPackages = Object.entries(selected).map(([packageId, { items }]) => ({
            packageId,
            items: Object.entries(items)
                .filter((x) => x[1])
                .map((x) => x[0]),
        }));

        if (selectedPackages.length === 0) {
            onClose();
        } else {
            if (addPackages.isLoading) return;
            addPackages
                .mutateAsync(selectedPackages)
                .then(() => {
                    onClose();
                    toasters.success(
                        t('orderDetails.packagePopup.packageAdded.text'),
                        t('orderDetails.packagePopup.packageAdded.title')
                    );
                })
                .catch((e: unknown) => {
                    toasters.danger(getErrorMessage(e), t('toasters.errorOccurredWhenSaving'));
                });
        }
    };

    const selectPackage = (packageId: string) => {
        const items = packages.find((x) => x.id === packageId)?.items;
        if (!items) return;

        setSelected((selectedPackages) => {
            const disabledItems = disabledPackages[packageId] ?? [];

            return {
                ...selectedPackages,
                [packageId]: {
                    items: Object.fromEntries(
                        items
                            .filter((item) => !disabledItems.includes(item.id))
                            .map((item) => [item.id, true])
                    ),
                },
            };
        });
    };

    const deselectPackage = (packageId: string) => {
        setSelected((selectedPackages) => {
            const result = { ...selectedPackages };
            delete result[packageId];
            return result;
        });
    };

    const setItemSelected = (packageId: string, itemId: string, selected: boolean) => {
        setSelected((selectedPackages) => {
            return {
                ...selectedPackages,
                [packageId]: {
                    items: {
                        ...selectedPackages[packageId]?.items,
                        [itemId]: selected,
                    },
                },
            };
        });
    };

    const addPostHogPackagesLog = (appliedPackages: PackageAppliedDto[]) => {
        appliedPackages.forEach(({ packageId }) => {
            capturePosthogIdentifiedEvent('Package_added_to_OR', {
                orderNumber,
                packageName: packages.find((x) => x.id === packageId)?.name,
                vehicleBrand: orderData.vehicle.brand,
                vehicleModel: orderData.vehicle.model,
                vehicleYear: orderData.vehicle.year,
            });
        });
    };

    const addPostHogFailedPackagesLog = (
        failedPackages: {
            packageName: string;
            error: string;
        }[]
    ) => {
        failedPackages.forEach((p) => {
            capturePosthogIdentifiedEvent('Package_failed_adding_to_OR', {
                orderNumber,
                packageName: p.packageName,
                error: p.error,
                vehicleBrand: orderData.vehicle.brand,
                vehicleModel: orderData.vehicle.model,
                vehicleYear: orderData.vehicle.year,
            });
        });
    };

    const noPackagesSelected = Object.keys(selected).length === 0;

    return (
        <Modal
            open={open}
            onClose={onClose}
            boxComponent={BoxComponent}
            onTransitionExited={() => setSelected({})}
        >
            <header>
                <Typography component="h4" variant="h4Inter">
                    {t('orderDetails.packagePopup.title')}
                </Typography>
                <Typography
                    variant="h6Roboto"
                    sx={{ fontWeight: 'normal', paddingTop: '10px' }}
                    component="h6"
                >
                    {t('orderDetails.packagePopup.subtitle')}
                </Typography>
            </header>

            <StyledOverlayScrollbarsComponent>
                <CheckboxWithContent
                    checked={noPackagesSelected}
                    onChange={(e) => {
                        if (e.target.checked) {
                            setSelected({});
                        }
                    }}
                >
                    <Typography variant="body1">
                        {t('orderDetails.packagePopup.withoutPackages')}
                    </Typography>
                </CheckboxWithContent>
                <Divider sx={{ my: 2 }} />
                {packages.map((p, packageIndex) => {
                    const packageState = selected[p.id];
                    const disabledItems = disabledPackages[p.id] ?? [];
                    const packageDisabled = p.items.every((x) => disabledItems.includes(x.id));

                    const isItemSelected = (id: string) =>
                        packageState ? packageState.items[id] ?? false : false;
                    const isPackageSelected = packageState !== undefined;

                    return (
                        <>
                            {packageIndex !== 0 && <Divider sx={{ my: 2 }} />}
                            <CheckboxWithContent
                                checked={isPackageSelected}
                                disabled={packageDisabled}
                                onChange={(e) => {
                                    if (e.target.checked) {
                                        selectPackage(p.id);
                                    } else {
                                        deselectPackage(p.id);
                                    }
                                }}
                            >
                                <Typography variant="body1">
                                    {t('orderDetails.packagePopup.package')}:{' '}
                                    <strong>{p.name}</strong>
                                </Typography>

                                <DivSubtotal>
                                    <SpanSubtotalLabel>
                                        {t('inspectionForms.spreadsheetView.subtotal')}
                                        :&nbsp;
                                    </SpanSubtotalLabel>
                                    <SpanSubtotalValue>
                                        {InternationalizationLogic.numberToCurrency(
                                            internationalization,
                                            p.items.reduce((acc, item) => {
                                                if (!isPackageSelected) {
                                                    if (disabledItems.includes(item.id)) {
                                                        return acc;
                                                    }
                                                    return acc + item.subtotal;
                                                }

                                                if (isItemSelected(item.id)) {
                                                    return acc + item.subtotal;
                                                }

                                                return acc;
                                            }, 0)
                                        )}
                                    </SpanSubtotalValue>
                                </DivSubtotal>

                                <DivItems>
                                    {p.items.map((item) => {
                                        const disabled = disabledItems.includes(item.id);
                                        const checked = isItemSelected(item.id);

                                        return (
                                            <CheckboxWithContent
                                                checked={checked}
                                                disabled={disabled}
                                                disabledTooltip={t(
                                                    'orderDetails.packagePopup.itemDisabledHint'
                                                )}
                                                onChange={(e) => {
                                                    setItemSelected(
                                                        p.id,
                                                        item.id,
                                                        e.target.checked
                                                    );
                                                }}
                                            >
                                                <SpanItemName>{item.name}</SpanItemName>
                                                <DivSubtotal>
                                                    <SpanSubtotalLabel>
                                                        {t(
                                                            'inspectionForms.spreadsheetView.subtotal'
                                                        )}
                                                        :&nbsp;
                                                    </SpanSubtotalLabel>
                                                    <SpanSubtotalValue>
                                                        {InternationalizationLogic.numberToCurrency(
                                                            internationalization,
                                                            item.subtotal
                                                        )}
                                                    </SpanSubtotalValue>
                                                </DivSubtotal>

                                                <UlItems>
                                                    {item.estimates.map((estimate) => (
                                                        <li>{estimate.description}</li>
                                                    ))}
                                                </UlItems>
                                            </CheckboxWithContent>
                                        );
                                    })}
                                </DivItems>
                            </CheckboxWithContent>
                        </>
                    );
                })}
            </StyledOverlayScrollbarsComponent>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Button
                    w="md"
                    onClick={onSave}
                    disabled={addPackages.isLoading}
                    showLoader={addPackages.isLoading}
                >
                    {t('commonLabels.save')}
                </Button>
            </Box>
        </Modal>
    );
}

const StyledOverlayScrollbarsComponent = styled(OverlayScrollbarsComponent)({
    maxHeight: 500,
    paddingRight: 30,
    marginTop: 10,
    flexGrow: '1',
});

const BoxComponent = styled('div')({
    minWidth: 630,
    minHeight: 520,
    maxHeight: '100vh',
    padding: '40px 10px 25px 40px',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
});

const SpanItemName = styled('span')({
    fontWeight: '600',
    display: 'inline-block',
    paddingRight: 100,
    maxWidth: 550,
});

const UlItems = styled('ul')({
    margin: '6px 0 0 0',
});

const DivItems = styled('div')({
    marginTop: 10,
});

type CheckboxWithContentProps = PropsWithChildren<{
    checked: boolean;
    onChange: CheckboxProps['onChange'];
    disabled?: boolean;
    disabledTooltip?: string;
}>;

function CheckboxWithContent({
    checked,
    onChange,
    children,
    disabled = false,
    disabledTooltip,
}: CheckboxWithContentProps) {
    return (
        <DivCheckboxLayout>
            <ArrowTooltip disabled={!disabled} content={disabledTooltip}>
                {/* just div so that ArrowTooltip will work */}
                <div>
                    <Checkbox
                        disabled={disabled}
                        sx={{ padding: '1px' }}
                        checked={checked}
                        onChange={onChange}
                    />
                </div>
            </ArrowTooltip>

            <DivContent sx={disabled ? { opacity: 0.3 } : undefined}>{children}</DivContent>
        </DivCheckboxLayout>
    );
}

const DivCheckboxLayout = styled('div')({
    display: 'grid',
    gridTemplateColumns: 'auto 1fr',
    alignItems: 'start',
    position: 'relative',
    marginTop: 5,
});

const DivContent = styled('div')({
    paddingLeft: 0,
    paddingTop: 5,
});

const DivSubtotal = styled('div')({
    position: 'absolute',
    top: 5,
    right: 0,
    fontWeight: '600',
});

const SpanSubtotalLabel = styled('span')({
    color: 'var(--cm1)',
});

const SpanSubtotalValue = styled('span')({});
