import { PackageMatchDto } from 'api/orders';
import {
    createContext,
    PropsWithChildren,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useStore } from 'react-redux';
import { createSelector } from 'reselect';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectOrderData } from 'store/slices/order/orderDetails';
import {
    selectDisabledPackageItems,
    selectMatchingPackages,
} from 'store/slices/order/orderInspectionFormsSlice';
import { ensureMatchingPackagesFetchedThunk } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import PackagesPopup from './PackagesPopup';

const selectPackagesFeature = createSelector(
    selectSettings,
    (g) => g.appMode === 'RepairShop' && (g.repairShopSettings?.features.packages ?? false)
);

export default function PackagesPopupProvider({ children }: PropsWithChildren<{}>) {
    const packagesEnabled = useAppSelector(selectPackagesFeature);

    if (!packagesEnabled) {
        return <>{children}</>;
    } else {
        return <PackagesPopupProviderInner>{children}</PackagesPopupProviderInner>;
    }
}

const PackagePopupContext = createContext<{
    openIfHasApplicablePackages: (masterItemId: number) => void;
}>({
    openIfHasApplicablePackages: (_masterItemId) => {},
});

export function usePackagePopupContext() {
    return useContext(PackagePopupContext);
}

function isEmptyPackage(p: PackageMatchDto): boolean {
    if (p.items.length > 1) return false;
    if (p.items.length === 0) return true; // sanity check: should never happen under normal circumstance
    if (p.items[0].estimates.length > 0) return false;

    // at this point we know there is 1 item with 0 estimates
    const item = p.items[0];
    // if subtotal is 0 that means there is nothing in labor/parts fields
    return item.subtotal === 0;
}

function PackagesPopupProviderInner({ children }: PropsWithChildren<{}>) {
    const matchingPackagesRef = useOrderPackagesRef();
    const [packages, setPackages] = useState<PackageMatchDto[] | undefined>(undefined);
    const [triggeredByMasterItemId, setTriggeredByMasterItemId] = useState(0);
    const store = useStore();

    const ctx = useMemo(
        () => ({
            openIfHasApplicablePackages: (masterItemId: number) => {
                const applicablePackages = matchingPackagesRef.current.filter(
                    (x) =>
                        x.items.some((item) => item.masterItemId === masterItemId) &&
                        !isEmptyPackage(x)
                );
                const disabledPackages = selectDisabledPackageItems(
                    store.getState(),
                    applicablePackages
                );

                const matchingPackages = applicablePackages.filter(
                    (x) => disabledPackages[x.id].length < x.items.length
                );
                if (matchingPackages.length === 0) return;
                setPackages(matchingPackages);
                setTriggeredByMasterItemId(masterItemId);
            },
        }),
        [matchingPackagesRef, store]
    );

    return (
        <>
            <PackagePopupContext.Provider value={ctx}>{children}</PackagePopupContext.Provider>
            <PackagesPopup
                onClose={() => setPackages(undefined)}
                packages={packages ?? []}
                open={packages !== undefined}
                triggeredByMasterItemId={triggeredByMasterItemId}
            />
        </>
    );
}

function useOrderPackagesRef() {
    const dispatch = useAppDispatch();
    const matchingPackagesRef = useRef<PackageMatchDto[]>([]);
    const store = useStore();
    const vehicleKey = useAppSelector((r) => {
        const order = selectOrderData(r);
        return `${order.vehicle.brand} ${order.vehicle.model} ${order.vehicle.year}`;
    });

    useEffect(() => {
        dispatch(ensureMatchingPackagesFetchedThunk());
    }, [dispatch, vehicleKey]);

    useEffect(() => {
        return store.subscribe(() => {
            const r = store.getState() as RootState;
            const packagesState = selectMatchingPackages(r);
            matchingPackagesRef.current = packagesState.matchingPackages;
        });
    });

    return matchingPackagesRef;
}
