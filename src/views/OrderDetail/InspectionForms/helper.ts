import { DiscountType } from 'api/settings/OrdersSettings';
import { EstimateDto } from 'datacontracts/InspectionForms/EstimateDto';
import { EstimateData } from 'store/slices/order/orderInspectionFormsSlice/types';

type ItemJobGroup = {
    name: string;
    id: number;
    estimates: EstimateDto[];
};

type SubItemsGroups = {
    ungrouped: EstimateDto[];
    groups: ItemJobGroup[];
};

export function groupSubItems(estimates: EstimateDto[]): SubItemsGroups {
    const result: SubItemsGroups = {
        groups: [],
        ungrouped: [],
    };
    const groups: Record<number, ItemJobGroup> = {};

    for (const e of estimates) {
        if (e.itemJobId) {
            const g = groups[e.itemJobId];

            if (g) {
                g.estimates.push(e);
            } else {
                groups[e.itemJobId] = {
                    id: e.itemJobId,
                    name: e.itemJobDescription ?? '',
                    estimates: [e],
                };
            }
        } else {
            result.ungrouped.push(e);
        }
    }

    result.groups = Object.values(groups);

    return result;
}

export const roundAwayFromZero = (value: number, requireDecimals?: boolean) => {
    return requireDecimals ? value : Math.round(value);
};

export const calculateSubtotal = (
    estimate: EstimateData,
    requireDecimals?: boolean,
    discountType?: DiscountType
) => {
    let subtotal =
        roundAwayFromZero(
            roundAwayFromZero(estimate.hourUnitPrice ?? 0, requireDecimals) * (estimate.hours ?? 0),
            requireDecimals
        ) +
        roundAwayFromZero(
            (estimate.quantity ?? 1) *
                roundAwayFromZero(estimate.partUnitPrice ?? 0, requireDecimals),
            requireDecimals
        );

    if (estimate.discount && discountType) {
        if (discountType === 'Currency')
            subtotal = subtotal - roundAwayFromZero(estimate.discount, requireDecimals);
        else if (discountType === 'Percentage')
            subtotal = subtotal * ((100 - estimate.discount) / 100);
    }

    return roundAwayFromZero(subtotal, requireDecimals);
};
