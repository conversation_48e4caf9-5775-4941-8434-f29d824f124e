import { useMutation, useQuery, useQueryClient, UseQueryResult } from '@tanstack/react-query';
import AccountApi from 'api/account';
import debounce from 'lodash/debounce';
import { useCallback, useMemo } from 'react';

export type InspectionFormsUserStorage = {
    spreadsheetEnabled?: boolean;

    spreadsheet: {
        columnsWidth: Record<string, number>;
        toggles: Record<string, boolean>;
    };
};

const defaultUserStorage: InspectionFormsUserStorage = {
    spreadsheet: {
        columnsWidth: {},
        toggles: {},
    },
};

const inspectionFormsUiStateKey = ['inspection-forms-ui'];

try {
    if (new URLSearchParams(window.location.search).get('debug_deleteInspectionFormsUserStorage')) {
        AccountApi.setValue('inspection_forms_ui', '');
    }
} catch (e: unknown) {
    console.error(
        'failed to check for debug action: debug_deleteInspectionFormsUserStorage, this error will not affect operation of CMOS',
        e
    );
}

export function useInspectionFormsUserStorage(): [
    query: Omit<UseQueryResult<InspectionFormsUserStorage, unknown>, 'data'> & {
        data: InspectionFormsUserStorage;
    },
    update: (updateFn: (value: InspectionFormsUserStorage) => InspectionFormsUserStorage) => void
] {
    const query = useQuery(
        inspectionFormsUiStateKey,
        async () => {
            const layout = tryParseSpreadsheetLayout(
                await AccountApi.getValue('inspection_forms_ui')
            );

            return layout;
        },
        {
            cacheTime: Infinity,
            staleTime: 10000,
        }
    );

    const updateMutation = useMutation(async (newValue: InspectionFormsUserStorage) => {
        await AccountApi.setValue('inspection_forms_ui', JSON.stringify(newValue));
    });

    const queryClient = useQueryClient();

    const debouncedUpdate = useMemo(
        () => debounce(updateMutation.mutateAsync, 1000),
        [updateMutation.mutateAsync]
    );

    const update = useCallback(
        (updateFn: (value: InspectionFormsUserStorage) => InspectionFormsUserStorage) => {
            const value: InspectionFormsUserStorage =
                queryClient.getQueryData(inspectionFormsUiStateKey) ?? defaultUserStorage;
            const newValue = updateFn(value);
            queryClient.setQueryData(inspectionFormsUiStateKey, newValue);
            debouncedUpdate(newValue);
        },
        [queryClient, debouncedUpdate]
    );

    return [
        {
            ...query,
            data: query.data ?? defaultUserStorage,
        },
        update,
    ];
}

function tryParseSpreadsheetLayout(data: string): InspectionFormsUserStorage {
    try {
        const value: unknown = JSON.parse(data);

        if (typeof value === 'object' && value !== null) {
            return value as InspectionFormsUserStorage;
        }

        return { ...defaultUserStorage };
    } catch (e: unknown) {
        return { ...defaultUserStorage };
    }
}
