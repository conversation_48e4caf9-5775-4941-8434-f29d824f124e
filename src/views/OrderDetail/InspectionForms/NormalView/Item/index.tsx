import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, styled } from '@mui/material';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { Button } from 'common/components/Button';
import { AddMenuIcon } from 'common/components/Icons/AddMenuIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { ImageIcon } from 'common/components/Icons/ImageIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { EstimateDto } from 'datacontracts/InspectionForms/EstimateDto';
import { Fragment, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import {
    selectRepairShopFeatures,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import {
    selectIsOrderClosed,
    selectOrderDiscountType,
    selectOrderSaving,
} from 'store/slices/order/orderDetails';
import {
    selectEstimatesSet,
    selectItemById,
} from 'store/slices/order/orderInspectionFormsSlice/selectors';
import { addEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks/addEstimate';
import { changePriority } from 'store/slices/order/orderInspectionFormsSlice/thunks/changePriority';
import { deleteEstimate as deleteEstimateAction } from 'store/slices/order/orderInspectionFormsSlice/thunks/deleteEstimate';
import { deleteEstimateGroup } from 'store/slices/order/orderInspectionFormsSlice/thunks/deleteEstimateGroup';
import { switchVisibility } from 'store/slices/order/orderInspectionFormsSlice/thunks/switchVisibility';
import { selectUserPermission } from 'store/slices/user/selectors';
import ApproveDeclineActionsView from '../../Common/ApproveDeclineActionsView';
import EvidenceModal from '../../Common/EvidenceModal';
import JaguarLandRoverMenuPricingModal from '../../Common/JaguarLandRoverMenuPricingModal';
import OpenAPIMenuPricingModal from '../../Common/OpenAPIMenuPricingModal';
import { calculateSubtotal, groupSubItems } from '../../helper';
import { usePackagePopupContext } from '../../PackagesPopupProvider';
import CommentsModal from '../CommentsModal';
import EditEstimateModal from '../EditEstimateModal';
import Estimate from '../Estimate';
import Priority from '../Priority';
import { useStyles } from './css';

interface ItemProps {
    itemId: number;
    repairOrderId: number;
}

const FormattedSubtotalContainer = styled('div')({
    display: 'flex',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    gridColumn: 4,
});

const Item = ({ itemId, repairOrderId }: ItemProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const orderSaving = useSelector(selectOrderSaving);
    const userPermission = useSelector(selectUserPermission);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const [isCommentsModalOpen, setIsCommentsModalOpen] = useState<boolean>(false);
    const [isDeleteEstimateOpen, setIsDeleteEstimateOpen] = useState(false);
    const [estimateToDelete, setEstimateToDelete] = useState<EstimateDto | null>(null);
    const [isEvidenceOpen, setIsEvidenceOpen] = useState(false);
    const [isEditEstimateOpen, setIsEditEstimateOpen] = useState(false);
    const [isMenuPricingOpen, setIsMenuPricingOpen] = useState(false);

    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const estimatesSet = useSelector(selectEstimatesSet);
    const requireDecimals = useMemo(
        () => (repairShopSettings?.features.enableRemoveDecimals ? false : true),
        [repairShopSettings]
    );

    const repairShopFeatures = useAppSelector(selectRepairShopFeatures);
    const enableEstimateIntegration = useMemo(
        () => repairShopFeatures?.enableEstimateIntegration || false,
        [repairShopFeatures]
    );
    const item = useSelector((state: RootState) => selectItemById(state, itemId));
    const estimates = item.estimates.map((estimateId) => estimatesSet[estimateId]);
    const subItems = estimates.filter((estimate) => estimate.isSubItem);
    const jobIds = subItems.filter((si) => si.jobId != null).map((si) => si.jobId!);
    const itemEstimate = estimates.find((estimate) => !estimate.isSubItem) ?? null;
    const groupedSubItems = groupSubItems(subItems);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);
    const [tooltipEl, setTooltipEl] = useState<null | HTMLElement>(null);

    const discountType = useAppSelector(selectOrderDiscountType);

    const showTooltip = (event: React.MouseEvent<HTMLElement>) => {
        const el = event.currentTarget;
        timeoutRef.current = setTimeout(() => {
            setTooltipEl(el);
        }, 350);
    };

    const hideTooltip = () => {
        setTooltipEl(null);
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = undefined;
        }
    };

    const calculateItemTotal = () => {
        let sum = 0;
        estimates.forEach(
            (estimate) => (sum += calculateSubtotal(estimate, requireDecimals, discountType))
        );
        return sum;
    };

    const packagePopup = usePackagePopupContext();

    const handlePriorityChange = (newPriority: PriorityLevel) => {
        dispatch(
            changePriority({ repairOrderId, masterItemId: itemId, priority: newPriority })
        ).then(() => {
            if (
                (item.priority === PriorityLevel.NA || item.priority === PriorityLevel.Low) &&
                (newPriority === PriorityLevel.Urgent || newPriority === PriorityLevel.Med)
            ) {
                packagePopup.openIfHasApplicablePackages(item.masterItemId);
            }
        });
    };

    const handleCommentsClick = () => {
        if (item.repairId !== null && userPermission.allowEditEstimates && !orderIsClosed)
            setIsCommentsModalOpen(true);
    };

    const handleVisibilitySwitch = () => {
        dispatch(switchVisibility({ repairOrderId, masterItemId: itemId }));
    };

    const handleAddEstimate = () => {
        dispatch(addEstimate({ masterItemId: itemId, repairOrderId }));
    };

    const handleDeleteEstimate = (estimate: EstimateDto) => {
        if (estimate.name && estimate.name.length) {
            setEstimateToDelete(estimate);
            setIsDeleteEstimateOpen(true);
        } else {
            deleteEstimate(estimate);
        }
    };

    const handleDeleteEstimateConfirm = async () => {
        await deleteEstimate(estimateToDelete!);
        setIsDeleteEstimateOpen(false);
    };

    const deleteEstimate = async (estimate: EstimateDto) => {
        dispatch(deleteEstimateAction({ itemId, estimateId: estimate.estimateId, repairOrderId }));
    };

    const deleteGroup = async (id: number) => {
        dispatch(
            deleteEstimateGroup({
                itemId,
                itemJobId: id,
                orderId: repairOrderId,
            })
        );
    };

    const addButton = () => {
        if (repairShopSettings?.features.itemJobs)
            return (
                <Button
                    cmosVariant={'typography'}
                    cmosSize={'large'}
                    disabled={!userPermission.allowEditEstimates || orderIsClosed}
                    iconPosition="right"
                    color={Colors.CM1}
                    Icon={AddMenuIcon}
                    onClick={() => setIsMenuPricingOpen(true)}
                />
            );
        if (subItems.length == 0 && !enableEstimateIntegration)
            return (
                <Button
                    cmosVariant={'typography'}
                    cmosSize={'large'}
                    disabled={!userPermission.allowEditEstimates || orderIsClosed}
                    iconPosition="right"
                    color={Colors.CM1}
                    Icon={PlusIcon}
                    onClick={handleAddEstimate}
                />
            );
        return <div />;
    };

    return (
        <>
            <div className={styles.item}>
                <div className={styles.itemButtons}>
                    {addButton()}
                    <Priority
                        disabled={!userPermission.allowEditEstimates || orderIsClosed}
                        priority={item.priority}
                        onChange={handlePriorityChange}
                    />
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={'large'}
                        iconPosition="right"
                        disabled={!item.hasMedia}
                        color={Colors.CM1}
                        Icon={ImageIcon}
                        onClick={() => setIsEvidenceOpen(true)}
                    />
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={'large'}
                        disabled={!userPermission.allowEditEstimates || orderIsClosed}
                        iconPosition="right"
                        Icon={() =>
                            item.isHidden ? (
                                <HideIcon fill={Colors.Neutral6} />
                            ) : (
                                <ShowIcon fill={Colors.CM1} />
                            )
                        }
                        onClick={handleVisibilitySwitch}
                    />
                </div>
                <div className={styles.itemName}>
                    <div className={styles.ellipsis}>
                        {item.campaignId ? `${t('inspectionForms.normalView.campaign')} - ` : ''}
                        {item.name}
                    </div>
                </div>
                <div
                    className={`${styles.comment} ${
                        item.repairId !== null ? styles.clickable : ''
                    }`}
                    onClick={handleCommentsClick}
                    onMouseEnter={showTooltip}
                    onMouseLeave={hideTooltip}
                >
                    <div className={styles.ellipsis}>{item.comments}</div>
                    <Popper
                        style={{ zIndex: 10000 }}
                        open={Boolean(tooltipEl)}
                        anchorEl={tooltipEl}
                    >
                        <div className={styles.tooltip}>{item.comments}</div>
                    </Popper>
                </div>
                <div
                    className={styles.subtotal}
                    onClick={() => {
                        if (
                            userPermission.allowEditEstimates &&
                            !enableEstimateIntegration &&
                            !orderIsClosed
                        ) {
                            setIsEditEstimateOpen(true);
                        }
                    }}
                >
                    {InternationalizationLogic.numberToCurrency(
                        internationalization,
                        itemEstimate
                            ? calculateSubtotal(itemEstimate, requireDecimals, discountType)
                            : 0,
                        {
                            allowZero: true,
                            requireDecimals: requireDecimals,
                        }
                    )}
                    <DownIcon
                        fill={
                            userPermission.allowEditEstimates &&
                            !enableEstimateIntegration &&
                            !orderIsClosed
                                ? Colors.CM1
                                : Colors.Neutral5
                        }
                    />
                </div>
                <div className={styles.approve}>
                    {(item.priority === PriorityLevel.Med ||
                        item.priority === PriorityLevel.Urgent) && (
                        <ApproveDeclineActionsView
                            disabled={!userPermission.allowEditEstimates || orderIsClosed}
                            masterItemId={item.masterItemId}
                            repairId={item.repairId}
                            repairOrderId={repairOrderId}
                        />
                    )}
                </div>
                {groupedSubItems.ungrouped.map((subItem, index) => (
                    <Estimate
                        key={subItem.estimateId}
                        estimateId={subItem.estimateId}
                        showAdd={index == groupedSubItems.ungrouped.length - 1}
                        onAdd={handleAddEstimate}
                        onDelete={() => handleDeleteEstimate(subItem)}
                        repairOrderId={repairOrderId}
                        masterItemId={itemId}
                    />
                ))}
                {groupedSubItems.groups.map((g) => (
                    <Fragment key={g.id}>
                        <div className={styles.subItemsGroupHeaderRow}>
                            <div className={styles.subItemsGroupName}>{g.name}</div>
                            <IconButton onClick={() => deleteGroup(g.id)} size="large">
                                <DeleteIcon />
                            </IconButton>
                        </div>
                        {g.estimates.map((subItem, _) => (
                            <Estimate
                                showAdd={false}
                                key={subItem.estimateId}
                                estimateId={subItem.estimateId}
                                onAdd={handleAddEstimate}
                                onDelete={() => handleDeleteEstimate(subItem)}
                                repairOrderId={repairOrderId}
                                masterItemId={itemId}
                            />
                        ))}
                    </Fragment>
                ))}
                {subItems.length > 0 && (
                    <div className={styles.totalRow}>
                        <div className={styles.totalSummary}>
                            {t('inspectionForms.normalView.itemTotal')} {item.name}
                            &nbsp;
                        </div>
                        <FormattedSubtotalContainer>
                            <span className={styles.total}>
                                {InternationalizationLogic.numberToCurrency(
                                    internationalization,
                                    calculateItemTotal(),
                                    {
                                        allowZero: true,
                                        requireDecimals: requireDecimals,
                                    }
                                )}
                            </span>
                        </FormattedSubtotalContainer>
                    </div>
                )}
            </div>
            <CommentsModal
                itemId={itemId}
                isOpen={isCommentsModalOpen}
                repairId={item.repairId}
                itemName={item.name}
                repairOrderId={repairOrderId}
                masterItemId={item.masterItemId}
                onClose={() => setIsCommentsModalOpen(false)}
            />
            <DeleteConfirmationPopup
                open={isDeleteEstimateOpen}
                title={t('inspectionForms.deleteEstimateModal.title')}
                body={t('inspectionForms.deleteEstimateModal.body')}
                cancel={t('inspectionForms.deleteEstimateModal.cancel')}
                confirm={t('inspectionForms.deleteEstimateModal.confirm')}
                onConfirm={handleDeleteEstimateConfirm}
                onClose={() => {
                    setIsDeleteEstimateOpen(false);
                }}
            />
            {isEvidenceOpen && (
                <EvidenceModal
                    onClose={() => setIsEvidenceOpen(false)}
                    repairId={item.repairId}
                    masterItemId={item.masterItemId}
                    repairOrderId={repairOrderId}
                />
            )}
            {isEditEstimateOpen && (
                <EditEstimateModal
                    name={item.name}
                    onClose={() => setIsEditEstimateOpen(false)}
                    estimate={itemEstimate}
                    repairId={item.repairId}
                    repairOrderId={repairOrderId}
                    masterItemId={item.masterItemId}
                    commentsSupported
                    itemId={itemId}
                />
            )}
            {isMenuPricingOpen && !orderSaving && (
                <>
                    {repairShopSettings!.features.itemJobsFromOpenApi ? (
                        <OpenAPIMenuPricingModal
                            repairOrderId={repairOrderId}
                            repairId={item.repairId}
                            masterItemId={item.masterItemId}
                            onClose={() => setIsMenuPricingOpen(false)}
                        />
                    ) : (
                        <JaguarLandRoverMenuPricingModal
                            orderId={repairOrderId}
                            repairId={item.repairId}
                            masterItemId={item.masterItemId}
                            name={item.name}
                            existingJobIds={jobIds}
                            onClose={() => setIsMenuPricingOpen(false)}
                        />
                    )}
                </>
            )}
        </>
    );
};

export default Item;
