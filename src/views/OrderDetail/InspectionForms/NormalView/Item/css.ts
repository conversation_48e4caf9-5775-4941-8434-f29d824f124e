import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    item: {
        display: 'grid',
        gridTemplateColumns: '168px 304fr 451fr 150px 77px',
        gridAutoRows: '40px',
        columnGap: '1px',
        rowGap: '1px',
        borderTopStyle: 'solid',
        borderTop: '1px',
        borderTopColor: theme.palette.neutral[2],
        borderBottomStyle: 'solid',
        borderBottom: '1px',
        borderBottomColor: theme.palette.neutral[2],
        backgroundColor: '#F6F6F6',
        alignItems: 'stretch',
        '&>*': {
            backgroundColor: '#FFFFFF',
        },
    },
    itemButtons: {
        backgroundColor: theme.palette.neutral[2],
        display: 'grid',
        alignItems: 'center',
        gridTemplateColumns: '40px 48px 40px 40px',
    },
    itemName: {
        display: 'flex',
        alignItems: 'center',
        paddingLeft: 12,
        paddingRight: 12,
        backgroundColor: theme.palette.neutral[2],
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[6]),
    },
    subItemsGroupHeaderRow: {
        gridColumn: '1 / 6',
        boxSizing: 'border-box',
        display: 'grid',
        gridTemplateColumns: '169px 1fr 77px',
        paddingLeft: 12,
        alignItems: 'center',
        height: 40,
        backgroundColor: 'rgba(123, 171, 249, 0.2)',
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.primary.light),
        '& > button': {
            width: 'max-content',
            padding: 6,
        },
    },
    subItemsGroupName: {
        gridColumnStart: 2,
    },
    ellipsis: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        flexGrow: 1,
        width: 0,
    },
    comment: {
        display: 'flex',
        alignItems: 'center',
        paddingLeft: 12,
        paddingRight: 12,
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.neutral[6]),
    },
    clickable: {
        cursor: 'pointer',
    },
    subtotal: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
    },
    totalRow: {
        backgroundColor: 'rgba(123, 171, 249, 0.1)',
        gridColumnStart: 1,
        gridColumnEnd: 6,
        display: 'grid',
        gridAutoFlow: 'row',
        gridTemplateColumns: '168px 304fr 451fr 150px 77px',
    },
    totalSummary: {
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.primary.light),
        display: 'flex',
        alignItems: 'center',
        paddingLeft: 12,
        gridColumn: 2,
    },
    total: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.primary.light),
    },
    approve: {
        display: 'flex',
        alignItems: 'center',
        padding: '0 5px',
        justifyContent: 'center',
    },
    tooltip: {
        padding: 10,
        marginTop: 8,
        ...FontSecondary(HeaderStyles.H8_10px, false, theme.palette.neutral[7]),
        borderRadius: 15,
        background: theme.palette.neutral[1],
        boxShadow: '0px 0px 5px rgba(200, 200, 200, 0.41)',
        maxWidth: 250,
        overflow: 'hidden',
        lineHeight: '18px',
    },
}));
