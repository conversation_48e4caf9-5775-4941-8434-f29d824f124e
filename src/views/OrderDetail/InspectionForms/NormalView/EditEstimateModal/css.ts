import { makeStyles } from '@mui/styles';
import { Colors } from '../../../../../common/styles/Colors';
import { FontPrimary, FontSecondary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    content: {
        paddingTop: 34,
        paddingLeft: 40,
        paddingRight: 37,
        paddingBottom: 38,
        boxSizing: 'border-box',
        width: 556,
    },
    header: {
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
        paddingBottom: 28,
    },
    buttonsArea: {
        marginTop: 20,
    },
    cancelButton: {
        marginRight: 15,
        width: 160,
    },
    saveButton: {
        width: 160,
    },
    label: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
        marginRight: 20,
    },
    field: {
        width: 140,
    },
    fieldRow: {
        marginBottom: 15,
    },
    value: {
        ...FontSecondary(HeaderStyles.H5_14px, true, Colors.CM3),
        paddingRight: 24,
    },
    totalColumn: {
        minWidth: 180,
        marginTop: 2,
    },
    totalRow: {
        height: 27,
    },
    cleanData: {
        ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Error),
        cursor: 'pointer',
        marginTop: 5,
    },
    commentsCaption: {
        marginTop: 20,
        marginBottom: 5,
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
    },
    heading: {
        height: 32,
        borderBottom: `1px solid ${theme.palette.neutral[7]}`,
        marginBottom: 20,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    headingText: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[9]),
    },
}));
