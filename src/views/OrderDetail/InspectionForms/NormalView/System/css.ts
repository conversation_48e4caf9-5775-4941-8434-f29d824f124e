import { makeStyles } from '@mui/styles';
import { FontPrimary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    systemName: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
        marginLeft: 40,
        marginTop: 15,
        marginBottom: 12,
    },
    addItem: {
        paddingLeft: 40,
        paddingTop: 20,
        paddingBottom: 20,
    },
}));
