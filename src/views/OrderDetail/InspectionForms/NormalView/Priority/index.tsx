import { Divider, Popover, styled } from '@mui/material';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useRef, useState } from 'react';

interface PriorityProps {
    priority: PriorityLevel;
    onChange: (newPriority: PriorityLevel) => void;
    disabled?: boolean;
}

const OptionButton = styled('button')(({ theme }) => ({
    padding: '8px 10px',
    borderRadius: 0,
    margin: 0,
    backgroundColor: 'transparent',
    border: 'none',
    transition: '.3s background-color',
    cursor: 'pointer',

    '&:hover': {
        backgroundColor: theme.palette.neutral[3],
    },

    '&:active': {
        backgroundColor: theme.palette.neutral[4],
    },
}));

const OptionCircle = styled('div', {
    shouldForwardProp: (prop) => !['priority'].includes(prop as string),
})<{ priority: PriorityLevel }>(({ theme, priority }) => ({
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    borderStyle: 'none',
    padding: 0,

    width: 20,
    height: 20,
    minWidth: 20,
    minHeight: 20,

    borderRadius: '50%',

    backgroundColor: {
        [PriorityLevel.Urgent]: '#F15857',
        [PriorityLevel.Med]: '#FFC626',
        [PriorityLevel.Low]: '#36CE91',
        [PriorityLevel.NA]: 'transparent',
    }[priority],

    ...(priority === PriorityLevel.NA && {
        '&:after': {
            content: '"N/A"',
            ...theme.typography.h6Roboto,
            color: theme.palette.neutral[7],
        },
    }),

    '&:focus': {
        outline: 'none',
    },
}));

const ContainerElement = styled('div')({
    position: 'relative',
    filter: 'drop-shadow(0px 1px 2px #C8C8C8)',
    backgroundColor: '#FFFFFF',
    marginTop: 15,
    marginLeft: 4,
    marginRight: 4,
    marginBottom: 4,
    borderRadius: 5,

    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
});

const Triangle = styled('div')({
    position: 'absolute',
    top: 0,
    left: '50%',
    width: 12,
    height: 12,
    backgroundColor: '#fff',
    transform: 'translate(-50%, -50%) rotate(45deg)',
    clipPath: 'polygon(0 0, 100% 0, 0 100%)',
    zIndex: 1,
});

const SDivider = styled(Divider)({
    width: 24,
});

const Priority = ({ priority, onChange, disabled }: PriorityProps) => {
    const [isOpened, setIsOpened] = useState(false);
    const button = useRef<HTMLButtonElement>(null);

    const choosePriority = (chosenPriority: PriorityLevel) => {
        setIsOpened(false);
        if (chosenPriority !== priority) onChange(chosenPriority);
    };

    return (
        <>
            <OptionButton
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: 0,
                }}
                disabled={disabled}
                ref={button}
                onClick={() => setIsOpened(!isOpened)}
            >
                <OptionCircle priority={priority} />
            </OptionButton>

            {isOpened && (
                <Popover
                    id={'priorityPopover'}
                    open
                    anchorEl={button.current}
                    onClose={() => setIsOpened(false)}
                    elevation={0}
                    transitionDuration={0}
                    anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'left',
                    }}
                    transformOrigin={{
                        vertical: 1,
                        horizontal: 14,
                    }}
                    slotProps={{
                        paper: {
                            sx: {
                                boxShadow: 'none',
                                background: 'none',
                            },
                        },
                    }}
                >
                    <ContainerElement>
                        <Triangle />
                        <OptionButton onClick={() => choosePriority(PriorityLevel.Urgent)}>
                            <OptionCircle priority={PriorityLevel.Urgent} />
                        </OptionButton>
                        <SDivider />
                        <OptionButton onClick={() => choosePriority(PriorityLevel.Med)}>
                            <OptionCircle priority={PriorityLevel.Med} />
                        </OptionButton>
                        <SDivider />
                        <OptionButton onClick={() => choosePriority(PriorityLevel.Low)}>
                            <OptionCircle priority={PriorityLevel.Low} />
                        </OptionButton>
                        <SDivider />
                        <OptionButton onClick={() => choosePriority(PriorityLevel.NA)}>
                            <OptionCircle priority={PriorityLevel.NA} />
                        </OptionButton>
                    </ContainerElement>
                </Popover>
            )}
        </>
    );
};

export default Priority;
