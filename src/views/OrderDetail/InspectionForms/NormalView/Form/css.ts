import { makeStyles } from '@mui/styles';
import { FontPrimary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    formName: {
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[7]),
        paddingLeft: 40,
        paddingRight: 40,
        marginTop: 33,
    },
    tableHeaders: {
        display: 'grid',
        gridTemplateColumns: '168px 304fr 451fr 150px 77px',
        gridTemplateRows: '40px',
        backgroundColor: theme.palette.neutral[2],
        alignItems: 'center',
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        marginTop: 12,
    },
    itemHeader: {
        paddingLeft: 11,
    },
    comments: {
        textTransform: 'uppercase',
    },
    subtotal: {
        display: 'flex',
        justifyContent: 'center',
    },
}));
