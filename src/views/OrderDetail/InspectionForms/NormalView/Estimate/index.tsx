import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { Button } from 'common/components/Button';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import {
    selectRepairShopFeatures,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import { selectIsOrderClosed, selectOrderDiscountType } from 'store/slices/order/orderDetails';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import { saveEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks/saveEstimate';
import { calculateSubtotal } from '../../helper';
import EditEstimateModal from '../EditEstimateModal';
import { useStyles } from './css';

interface EstimateProps {
    estimateId: number;
    showAdd: boolean;
    onAdd: () => void;
    onDelete: () => void;
    repairOrderId: number;
    masterItemId: number;
}

const Estimate = ({
    estimateId,
    showAdd,
    onAdd,
    onDelete,
    repairOrderId,
    masterItemId,
}: EstimateProps) => {
    const styles = useStyles();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);
    const requireDecimals = useMemo(
        () => (repairShopSettings?.features.enableRemoveDecimals ? false : true),
        [repairShopSettings]
    );
    const estimate = useSelector((state: RootState) => selectEstimateById(state, estimateId));
    const [name, setName] = useState(estimate.name ?? '');
    const [oldName, setOldName] = useState(estimate.name ?? '');
    const [isEditEstimateOpen, setIsEditEstimateOpen] = useState(false);

    const repairShopFeatures = useAppSelector(selectRepairShopFeatures);
    const enableEstimateIntegration = useMemo(
        () => repairShopFeatures?.enableEstimateIntegration || false,
        [repairShopFeatures]
    );

    const discountType = useAppSelector(selectOrderDiscountType);

    const handleNameChange = (event: any) => {
        setName(event.target.value);
    };

    const handleNameFocus = () => {
        setOldName(name);
    };

    const handleKeyDown = (e: any) => {
        if (e.key === 'Escape') {
            setName(oldName);
        }

        if (e.key === 'Enter' || e.key === 'Escape') {
            setTimeout(() => {
                e.target.blur();
            });
        }
    };

    const handleNameBlur = async () => {
        if (name !== oldName) {
            const result = await dispatch(
                saveEstimate({
                    repairOrderId,
                    repairId: estimate.repairId,
                    masterItemId,
                    estimateId: estimate.estimateId,
                    name,
                    quantity: estimate.quantity,
                    partUnitCost: estimate.partUnitCost,
                    partUnitPrice: estimate.partUnitPrice,
                    hours: estimate.hours,
                    hourUnitPrice: estimate.hourUnitPrice,
                })
            );
            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.savedEstimateBody'),
                    t('inspectionForms.savedEstimateTitle')
                );
            }
        }
    };

    return (
        <>
            <div className={styles.buttons}>
                {showAdd && !enableEstimateIntegration ? (
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={'large'}
                        iconPosition="right"
                        color={Colors.CM1}
                        Icon={PlusIcon}
                        onClick={onAdd}
                        disabled={orderIsClosed}
                    />
                ) : (
                    <div />
                )}
                {!enableEstimateIntegration && (
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={'large'}
                        iconPosition="right"
                        color={Colors.CM1}
                        Icon={LessIcon}
                        onClick={onDelete}
                        disabled={orderIsClosed}
                    />
                )}
            </div>
            <div className={styles.estimateNameColumn}>
                <input
                    className={styles.estimateName}
                    value={name}
                    onChange={handleNameChange}
                    onFocus={handleNameFocus}
                    onKeyDown={handleKeyDown}
                    onBlur={handleNameBlur}
                    disabled={orderIsClosed}
                />
            </div>
            <div />
            <div
                className={styles.subtotal}
                onClick={() => !orderIsClosed && setIsEditEstimateOpen(true)}
            >
                {InternationalizationLogic.numberToCurrency(
                    internationalization,
                    calculateSubtotal(estimate, requireDecimals, discountType),
                    {
                        allowZero: true,
                        requireDecimals: requireDecimals,
                    }
                )}
            </div>
            <div />
            {isEditEstimateOpen && (
                <EditEstimateModal
                    name={estimate.name}
                    onClose={() => setIsEditEstimateOpen(false)}
                    estimate={estimate}
                    repairId={estimate.repairId}
                    repairOrderId={repairOrderId}
                    masterItemId={masterItemId}
                    commentsSupported={false}
                    itemId={masterItemId}
                />
            )}
        </>
    );
};

export default Estimate;
