import { makeStyles } from '@mui/styles';
import { FontPrimary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    buttons: {
        backgroundColor: theme.palette.neutral[2],
        display: 'grid',
        alignItems: 'center',
        gridTemplateColumns: '40px 40px',
    },
    estimateNameColumn: {
        display: 'flex',
    },
    estimateName: {
        width: '100%',
        paddingLeft: 32,
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[6]),
        border: 'none',
        '&:focus': {
            outline: 'none',
        },
    },
    subtotal: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
    },
}));
