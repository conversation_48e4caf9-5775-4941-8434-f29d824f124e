import { Box, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import { MasterItemDto } from 'api/MasterItem';
import { normalizeAccent } from 'common/Helpers';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import FilterTextField from 'common/components/Inputs/FilterTextField';
import { Modal } from 'common/components/Modal';
import { SMenuItem } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import groupBy from 'lodash/groupBy';
import { useMemo, useState } from 'react';
import { GroupedVirtuoso } from 'react-virtuoso';
import { batch } from 'utils/collection';
import ErrorBoundary from 'utils/errorsHandling/ErrorBoundary';

type InspectionItemsModalProps = {
    open: boolean;
    onClose: () => void;
    options: MasterItemDto[];
    onSelected: (data: MasterItemDto) => void;
};

const ITEM_HEIGHT = 40;

export default function InspectionItemsModal({
    open,
    onClose,
    options,
    onSelected,
}: InspectionItemsModalProps): JSX.Element {
    const { t } = useAppTranslation();

    const [searchQuery, setSearchQuery] = useState('');
    const hasSearchQuery = searchQuery.trim() !== '';
    const searchResults = useMemo(() => {
        const normalizedQuery = normalizeAccent(searchQuery.trim());
        if (normalizedQuery === '') return options;
        return options.filter((x) => normalizeAccent(x.name).includes(normalizedQuery));
    }, [searchQuery, options]);
    const { groups, items } = useMemo(() => {
        const groupedItems = Object.entries(
            groupBy(searchResults, (x) => {
                if (x.name === '') {
                    return '?';
                }
                const firstLetter = normalizeAccent(x.name.charAt(0)).toUpperCase();
                if (firstLetter === ' ') {
                    return '?';
                }

                if (/[0-9]/.test(firstLetter)) {
                    return '0-9';
                }

                if (!/[a-zA-Z]/.test(firstLetter)) {
                    return '&';
                }

                return firstLetter;
            })
        ).map(([letter, items]) => ({ letter, items }));

        groupedItems.sort((a, b) => {
            return a.letter.localeCompare(b.letter);
        });

        const batchSize = hasSearchQuery ? 1 : 4;
        const groups = groupedItems.map((x) => ({
            letter: x.letter,
            items: batch(x.items, batchSize),
        }));

        return {
            groups: groups.map((x) => ({
                letter: x.letter,
                count: x.items.length,
            })),
            items: groups.flatMap((x) => x.items),
        };
    }, [searchResults, hasSearchQuery]);

    const context: Context = {
        groups,
        items,
        hasSearchQuery,
        onSelected: (item) => {
            onSelected(item);
            onClose();
        },
        searchQuery,
    };

    return (
        <Modal open={open} onClose={onClose}>
            <DivRoot>
                <Grid
                    container
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{
                        mb: 2,
                        px: 3,
                        pt: 2,
                    }}
                >
                    <Grid item>
                        <H5ModalTitle>
                            {t('inspectionForms.inspectionItemsModal.inspectionItems')}
                        </H5ModalTitle>
                        <H5ModalText>
                            {t('inspectionForms.inspectionItemsModal.listAllInspectionItems')}
                        </H5ModalText>
                        <FilterTextField
                            sx={{ width: 400 }}
                            name={'filter'}
                            isRequired={false}
                            placeholder={t('inspectionForms.inspectionItemsModal.search')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </Grid>
                    <Grid item style={{ alignSelf: 'start' }}>
                        <IconButton onClick={() => onClose()} size="small">
                            <CloseIcon fill={Colors.Neutral7} />
                        </IconButton>
                    </Grid>
                </Grid>
                <Box sx={{ height: 500 }}>
                    <ErrorBoundary renderError={() => <span>Something went wrong</span>}>
                        <StyledGroupedVirtuoso<void, Context>
                            groupContent={renderGroupContent}
                            itemContent={renderItemContent}
                            groupCounts={groups.map((x) => x.count)}
                            context={context}
                        />
                    </ErrorBoundary>
                </Box>
            </DivRoot>
        </Modal>
    );
}

const StyledGroupedVirtuoso = styled(GroupedVirtuoso)({
    ...scrollbarStyle(),
}) as typeof GroupedVirtuoso;

type Context = {
    groups: {
        letter: string;
        count: number;
    }[];
    items: MasterItemDto[][];
    hasSearchQuery: boolean;
    onSelected: (item: MasterItemDto) => void;
    searchQuery: string;
};

function renderGroupContent(index: number, context: Context): React.ReactNode {
    const group = context.groups[index];

    return <StyledH6>{group.letter}</StyledH6>;
}

const StyledH6 = styled('h6')(({ theme }) => ({
    borderBottom: `solid 1px ${theme.palette.neutral[4]}`,
    ...theme.typography.h5Inter,
    padding: '8px 16px',
    margin: 0,
    backgroundColor: theme.palette.neutral[1],
    color: theme.palette.neutral[8],
}));

function renderItemContent(
    index: number,
    groupIndex: number,
    _data: void,
    { onSelected, items, hasSearchQuery }: Context
): React.ReactNode {
    const rowItems = items[index];

    if (rowItems.length === 1 && hasSearchQuery) {
        return (
            <StyledSMenuItem
                onClick={() => {
                    onSelected(rowItems[0]);
                }}
            >
                {rowItems[0].name}
            </StyledSMenuItem>
        );
    }

    return (
        <StyledRowContainer data-index={index} data-group-index={groupIndex}>
            {rowItems.map((item) => (
                <StyledSMenuItem
                    onClick={() => {
                        onSelected(item);
                    }}
                    key={item.id}
                >
                    {item.name}
                </StyledSMenuItem>
            ))}
        </StyledRowContainer>
    );
}

const StyledRowContainer = styled('ul')({
    margin: 0,
    padding: 0,
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 25%)',

    '&.hasSearchQuery': {
        gridTemplateColumns: '100%',
    },
});

const DivRoot = styled('div')({
    width: 900,
    margin: 20,
});

const StyledSMenuItem = styled(SMenuItem)({
    height: ITEM_HEIGHT,
    textWrap: 'wrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
});

const H5ModalTitle = styled('h5')(({ theme }) => ({
    ...theme.typography.h5Inter,
    textAlign: 'left',
    margin: '5px 0',
}));

const H5ModalText = styled('h5')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    textAlign: 'left',
}));
