import { makeStyles } from '@mui/styles';
import { FontPrimary } from '../../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    content: {
        paddingTop: 34,
        paddingLeft: 40,
        paddingRight: 40,
        paddingBottom: 38,
        width: 476,
    },
    header: {
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
        paddingBottom: 28,
    },
    okButton: {
        marginTop: 18,
        width: 160,
    },
}));
