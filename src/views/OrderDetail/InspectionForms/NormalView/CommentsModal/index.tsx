import { Grid } from '@mui/material';
import RepairCommentAPI from 'api/RepairComment';
import TechnicianCommentAPI from 'api/TechnicianComment';
import { Button } from 'common/components/Button';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import DropdownMulti from 'common/components/Inputs/DropdownMulti';
import { Modal } from 'common/components/Modal';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { RepairCommentDto } from 'datacontracts/InspectionForms/RepairCommentDto';
import { useEffect, useMemo, useState } from 'react';
import { useAppDispatch } from 'store';
import { updateOrder } from 'store/slices/order/orderDetails';
import { inspectionFormsActions } from 'store/slices/order/orderInspectionFormsSlice';
import { useStyles } from './css';

type CommentsModalProps = {
    itemId: number;
    isOpen: boolean;
    repairId: number | null;
    itemName: string;
    repairOrderId: number;
    masterItemId: number;
    onClose: () => void;
};

type Comment = {
    repairCommentId: number | null;
    technicianCommentId: number | null;
};

const CommentsModal = ({
    itemId,
    isOpen,
    repairId,
    itemName,
    repairOrderId,
    masterItemId,
    onClose,
}: CommentsModalProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const { callApi: callApiRepairComments } = useApiCall();
    const { callApi: callApiTechnicianComments } = useApiCall();
    const { callApi: callApiAddComment } = useApiCall();
    const { callApi: callApiDeleteComment } = useApiCall();
    const dispatch = useAppDispatch();
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [comments, setComments] = useState<Readonly<OptionData<Comment>[]>>([]);
    const [options, setOptions] = useState<OptionData<Comment>[]>([]);

    const availableComments = useMemo(
        () =>
            options.filter(
                (o) =>
                    !comments.some(
                        (c) => c.value.technicianCommentId === o.value.technicianCommentId
                    )
            ),
        [comments, options]
    );

    useEffect(() => {
        (async () => {
            if (isOpen) {
                try {
                    const repairCommentsPromise = repairId
                        ? callApiRepairComments(() => RepairCommentAPI.list(repairId!), {
                              selectErrorContent: (_) => ({
                                  body: t('toasters.errorOccurredWhenLoading'),
                              }),
                          })
                        : new Promise<RepairCommentDto[]>((resolve, _reject) => {
                              resolve([]);
                          });
                    const technicianCommentsPromise = callApiTechnicianComments(
                        () => TechnicianCommentAPI.list(repairOrderId, masterItemId),
                        {
                            selectErrorContent: (_) => ({
                                body: t('toasters.errorOccurredWhenLoading'),
                            }),
                        }
                    );

                    const fetchedComments = await repairCommentsPromise;
                    setComments(
                        fetchedComments.map((comment) => ({
                            label: comment.text,
                            value: {
                                repairCommentId: comment.repairCommentId,
                                technicianCommentId: comment.technicianCommentId,
                            },
                        }))
                    );
                    const technicianComments = await technicianCommentsPromise;
                    setOptions(
                        technicianComments.map((comment) => ({
                            label: comment.text,
                            value: {
                                repairCommentId: null,
                                technicianCommentId: comment.technicianCommentId!,
                            },
                        }))
                    );
                } catch {
                    onClose();
                }
            }
            setIsModalOpen(isOpen);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    const handleCreate = async (option: OptionData<Comment | null>) => {
        const response = await callApiAddComment(
            () =>
                RepairCommentAPI.add(
                    repairOrderId,
                    repairId,
                    masterItemId,
                    option.value?.technicianCommentId ?? null,
                    option.label
                ),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenSaving'),
                }),
            }
        );

        const newComments = [
            ...comments,
            {
                label: option.label,
                value: {
                    repairCommentId: response.repairCommentId,
                    technicianCommentId: option.value?.technicianCommentId ?? null,
                },
            },
        ];

        setComments(newComments);
        dispatch(
            inspectionFormsActions.setComments({
                comments: newComments.map((comment) => comment.label),
                itemId: itemId,
            })
        );
        dispatch(updateOrder(response.orderUpdate));
    };

    const handleSelect = async (option: OptionData<Comment>) => {
        setComments((x) => [...x, option]);

        handleCreate(option);
    };

    const handleRemove = async (option: OptionData<Comment>) => {
        if (option.value.repairCommentId == null) {
            return;
        }

        const orderUpdate = await callApiDeleteComment(
            () => RepairCommentAPI.delete(option.value.repairCommentId!),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenSaving'),
                }),
            }
        );
        const newComments = comments.filter(
            (c) => c.value.repairCommentId !== option.value.repairCommentId
        );
        setComments(newComments);

        if (
            option.value.technicianCommentId &&
            !options.some((x) => x.value.technicianCommentId === option.value.technicianCommentId)
        ) {
            setOptions((x) => [...x, option]);
        }

        dispatch(
            inspectionFormsActions.setComments({
                comments: newComments.map((comment) => comment.label),
                itemId: itemId,
            })
        );
        dispatch(updateOrder(orderUpdate));
    };

    return (
        <Modal open={isModalOpen}>
            <div className={styles.content}>
                <div className={styles.header}>
                    {t('inspectionForms.normalView.comments')}: {itemName}
                </div>
                <DropdownMulti<Comment>
                    cmosVariant="grey"
                    value={comments}
                    options={availableComments}
                    onCreate={handleCreate}
                    onSelect={handleSelect}
                    onRemove={handleRemove}
                    creatable
                />

                <Grid container justifyContent="flex-end">
                    <Button
                        className={styles.okButton}
                        cmosVariant={'filled'}
                        label="OK"
                        onClick={onClose}
                    />
                </Grid>
            </div>
        </Modal>
    );
};

export default CommentsModal;
