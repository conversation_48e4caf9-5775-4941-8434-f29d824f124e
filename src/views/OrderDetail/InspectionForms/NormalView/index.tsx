import { useSelector } from 'react-redux';
import { OTHER_SYSTEM_ID } from 'store/slices/order/orderInspectionFormsSlice';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useAppSelector } from '../../../../store';
import { selectIsOrderClosed } from '../../../../store/slices/order/orderDetails';
import { selectFormsSet } from '../../../../store/slices/order/orderInspectionFormsSlice/selectors';
import { ItemData } from '../../../../store/slices/order/orderInspectionFormsSlice/types';
import { AddItem } from '../Common/AddItem';
import Form from './Form';
import { useStyles } from './css';

interface NormalViewProps {
    repairOrderId: number;
    itemFilter: (item: ItemData) => boolean;
}

const NormalView = ({ repairOrderId, itemFilter }: NormalViewProps) => {
    const userPermission = useSelector(selectUserPermission);
    const formsSet = useSelector(selectFormsSet);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const styles = useStyles();
    const forms = Object.values(formsSet);

    return (
        <div className={styles.normalView}>
            {forms.map((form) => (
                <Form
                    key={form.templateId}
                    formId={form.templateId}
                    repairOrderId={repairOrderId}
                    itemFilter={itemFilter}
                />
            ))}
            {forms.length === 0 && (
                <AddItem
                    className={styles.addItem}
                    repairOrderId={repairOrderId}
                    disabled={!userPermission.allowEditEstimates || orderIsClosed}
                    // NOTE (AP) We place all new items in 'Other' system, because on spreadsheet UI there's no way to select any system explicitly
                    systemId={OTHER_SYSTEM_ID}
                />
            )}
        </div>
    );
};

export default NormalView;
