import { CircularProgress, Collapse, styled, tooltipClasses } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import OrdersApi from 'api/orders';
import { Button } from 'common/components/Button';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import Tooltip from 'common/components/Tooltip';
import { formatTime } from 'common/Helpers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { useEffect, useRef, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { useAppSelector } from '../../../../store';
import { selectOrderShopKey } from '../../../../store/slices/order/orderDetails';

const itTakesLongerDelay = 30000;

export default function DownloadEvidenceButton({
    repairOrderId,
    orderNumber,
}: {
    repairOrderId: number;
    orderNumber: string;
}) {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const isEnterprise = useIsEnterpriseRoute();
    const shopId = useAppSelector(selectOrderShopKey);
    const [startedDownloadAt, setStartedDownloadAt] = useState(0);

    const downloadEvidence = useMutation(
        async () => {
            const forceAsyncExecution =
                new URLSearchParams(window.location.search).get('debug.zip.forceAsyncExecution') ===
                'true';

            if (!shopId) {
                toasters.danger(
                    t('orderDetails.inspectionForms.downloadEvidenceError'),
                    t('toasters.errorOccurred')
                );
                return;
            }

            const response = await (isEnterprise
                ? EnterpriseOrdersApi.orders.zip(repairOrderId, shopId, forceAsyncExecution)
                : OrdersApi.zipOrder(repairOrderId, forceAsyncExecution));
            await OrdersApi.downloadZipOrder(response, 15 * 60 * 1000); // timeout of 15 minutes
        },
        {
            onError: () => {
                toasters.danger(
                    t('orderDetails.inspectionForms.downloadEvidenceError'),
                    t('toasters.errorOccurred')
                );
            },
        }
    );

    const handleClick = () => {
        setStartedDownloadAt(Date.now());
        downloadEvidence.mutate();
    };

    return (
        <Tooltip content={t('orderDetails.inspectionForms.downloadPhotos')}>
            <StyledButton
                disabled={downloadEvidence.isLoading}
                onClick={handleClick}
                cmosVariant="typography"
            >
                {downloadEvidence.isLoading ? (
                    <CircularProgress size={24} thickness={5} />
                ) : (
                    <DownloadIcon fill="var(--cm1)" />
                )}
                <Downloading since={startedDownloadAt} visible={downloadEvidence.isLoading} />
            </StyledButton>
        </Tooltip>
    );
}

const StyledButton = styled(Button)({
    height: '48px !important',
    minWidth: 48,

    '&:not(:disabled):hover': {
        backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },

    '.MuiTouchRipple-child': {
        backgroundColor: 'var(--neutral8)',
    },
});

function Downloading({ visible: visibleProp, since }: { visible: boolean; since: number }) {
    const { t } = useAppTranslation();
    const rootRef = useRef<HTMLSpanElement | null>(null);
    const [showItTakesLonger, setShowItTakesLonger] = useState(false);
    const [visible] = useDebounce(visibleProp, 400);

    useEffect(() => {
        const { current: root } = rootRef;
        if (!root) return;
        const elements = [...root.querySelectorAll('[data-dot]')];

        if (!visible) {
            return;
        }

        let visibleCount = elements.length;

        const update = () => {
            if (visibleCount >= elements.length) {
                visibleCount = 0;
                window.requestAnimationFrame(() => {
                    for (const el of elements) {
                        if (el instanceof HTMLElement) {
                            el.style.visibility = 'hidden';
                        }
                    }
                });
            } else {
                visibleCount++;
                const el = elements[visibleCount - 1];
                if (el instanceof HTMLElement) {
                    window.requestAnimationFrame(() => {
                        el.style.visibility = 'visible';
                    });
                }
            }
        };

        update();
        const interval = setInterval(update, 500);
        return () => clearInterval(interval);
    }, [visible]);

    const showItTakesLongerTimeout = useRef(0);
    useEffect(() => {
        if (visible) {
            if (showItTakesLongerTimeout.current) clearInterval(showItTakesLongerTimeout.current);
            showItTakesLongerTimeout.current = window.setTimeout(() => {
                setShowItTakesLonger(true);
            }, itTakesLongerDelay);
        } else {
            setShowItTakesLonger(false);
            if (showItTakesLongerTimeout.current) {
                clearInterval(showItTakesLongerTimeout.current);
                showItTakesLongerTimeout.current = 0;
            }
        }
    }, [visible, since]);

    return (
        <Collapse orientation="horizontal" in={visible}>
            <StyledTooltip
                open={visible && showItTakesLonger}
                content={
                    <ItTakesLongerText>
                        <PTimeSinceParent>
                            <TimeSince since={since} />
                        </PTimeSinceParent>
                        {t('commonLabels.downloadingTakesLonger')}
                    </ItTakesLongerText>
                }
            >
                <span ref={rootRef}>
                    &nbsp;&nbsp;&nbsp;{t('commonLabels.downloading')}
                    <span data-dot>.</span>
                    <span data-dot>.</span>
                    <span data-dot>.</span>
                </span>
            </StyledTooltip>
        </Collapse>
    );
}

const StyledTooltip = styled(Tooltip)({
    [`& .${tooltipClasses.tooltip}`]: {
        borderRadius: 5,
    },
});

const PTimeSinceParent = styled('p')({
    fontWeight: 'bold',
    marginBottom: 5,
    marginTop: 0,
});

const ItTakesLongerText = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
}));

function TimeSince({ since }: { since: number }) {
    const ref = useRef<HTMLSpanElement | null>(null);

    useEffect(() => {
        const update = () => {
            const elapsed = Math.floor((Date.now() - since) / 1000);
            const newText = formatTime(elapsed);
            window.requestAnimationFrame(() => {
                if (ref.current) ref.current.innerText = newText;
            });
        };
        update();
        const interval = setInterval(update, 800);
        return () => clearInterval(interval);
    }, [since]);

    return <span ref={ref} />;
}
