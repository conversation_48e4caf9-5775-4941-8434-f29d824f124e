import { Divider, IconButton, styled, useTheme } from '@mui/material';
import OrderAPI from 'api/Order';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { ClipIcon } from 'common/components/Icons/ClipIcon';
import { GridIcon } from 'common/components/Icons/GridIcon';
import { ListIcon } from 'common/components/Icons/ListIcon';
import Checkbox from 'common/components/Inputs/Checkbox';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import Tooltip from 'common/components/Tooltip';
import { ROUTES } from 'common/constants';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import {
    selectEnableManualPaymentRegistration,
    selectIsEnterprise,
    selectIsSpreadsheetSelected,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import { selectIsOrderClosed, selectOrderData } from 'store/slices/order/orderDetails';
import { inspectionFormsActions } from 'store/slices/order/orderInspectionFormsSlice';
import {
    selectFormsSet,
    selectItemsSet,
} from 'store/slices/order/orderInspectionFormsSlice/selectors';
import {
    fetchEnterpriseMasterItems,
    fetchMasterItems,
} from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { fetchInspectionForms } from 'store/slices/order/orderInspectionFormsSlice/thunks/fetchInspectionForms';
import { ItemData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { selectUserPermission } from 'store/slices/user/selectors';
import SyncEstimateButton from 'views/Components/SyncEstimateButton';
import OrderOnlinePayments from './Common/OrderOnlinePayments';
import DownloadEstimatesExcelButton from './DownloadEstimatesExcelButton';
import DownloadEvidenceButton from './DownloadEvidenceButton';
import NormalView from './NormalView';
import PackagesPopupProvider from './PackagesPopupProvider';
import { PriorityFilterType, PriorityFilterTypeLabel } from './PriorityFilterType';
import { SpreadsheetView } from './SpreadsheetView';
import { useInspectionFormsUserStorage } from './user-storage';

type InspectionFormsProps = {
    repairOrderId: number;
    repairOrderNumber: string;
    showEstimates: boolean;
    onShowEstimatesChange: (value: boolean) => void;
};

const Header = styled('div')(({ theme }) => ({
    paddingLeft: 40,
    paddingRight: 40,
    display: 'flex',
    justifyContent: 'space-between',
}));

const AlignCenterVertically = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: 4,
}));

const PriorityFilterArea = styled('div')(({ theme }) => ({
    width: 220,
}));

const EstimateContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
}));

const InspectionForms = ({
    repairOrderId,
    repairOrderNumber,
    showEstimates,
    onShowEstimatesChange,
}: InspectionFormsProps) => {
    const { t } = useAppTranslation();
    const userPermission = useSelector(selectUserPermission);
    const isEnterprise = useSelector(selectIsEnterprise);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);
    const dispatch = useAppDispatch();
    const attachFileRef = useRef<HTMLInputElement>(null);
    const formsSet = useSelector(selectFormsSet);
    const items = useAppSelector(selectItemsSet);
    const { repairShopSettings } = useSelector(selectSettings);
    const toasters = useToasters();
    const theme = useTheme();
    const navigate = useNavigate();

    const enableManualPaymentRegistration = useAppSelector(selectEnableManualPaymentRegistration);
    const priorityFilterOptions: OptionData[] = Object.values(PriorityFilterType).map((value) => {
        return { label: t(PriorityFilterTypeLabel(value)), value };
    });
    const [priorityFilterSelectedOption, setPriorityFilterSelectedOption] = useState<OptionData>({
        label: t(PriorityFilterTypeLabel(PriorityFilterType.ALL)),
        value: PriorityFilterType.ALL,
    });

    const [isSpreadsheetSelected, setSpreadsheetSelected, isSpreadsheetSelectedLoading] =
        useIsSpreadsheetSelected();

    const fetchItems = async () => {
        if (isEnterprise) {
            dispatch(fetchEnterpriseMasterItems());
        } else {
            dispatch(fetchMasterItems());
        }
        dispatch(fetchInspectionForms(repairOrderId));
    };

    useEffect(() => {
        fetchItems();
        // eslint-disable-next-line react-hooks/exhaustive-deps
        return () => {
            dispatch(inspectionFormsActions.clearForms());
        };
    }, []);

    const handlePriorityFilterChange = (option: OptionData | null) => {
        if (option !== null) setPriorityFilterSelectedOption(option);
    };

    const handleAttachFile = () => {
        if (attachFileRef.current) attachFileRef.current.click();
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (!event.target.files) return;
        if (!event.target.files[0]) return;

        OrderAPI.attachment(event.target.files[0], repairOrderId).then(
            () => {
                toasters.success(
                    t('orderDetails.inspectionForms.attachFileSuccessBody') +
                        ' ' +
                        event.target.files![0].name,
                    t('orderDetails.inspectionForms.attachFileSuccessTitle')
                );
            },
            () => {
                toasters.danger(
                    t('orderDetails.inspectionForms.attachFileErrorBody'),
                    t('orderDetails.inspectionForms.attachFileErrorTitle')
                );
            }
        );
    };

    const itemFilter = useCallback(
        (item: ItemData) => {
            if (priorityFilterSelectedOption.value === PriorityFilterType.ALL) return true;
            if (priorityFilterSelectedOption.value === PriorityFilterType.RED)
                return item.priority === PriorityLevel.Urgent;
            if (priorityFilterSelectedOption.value === PriorityFilterType.YELLOW)
                return item.priority === PriorityLevel.Med;
            return false;
        },
        [priorityFilterSelectedOption.value]
    );

    const checklistName = useMemo(() => {
        return Object.values(formsSet)[0]?.name;
    }, [formsSet]);

    const orderData = useAppSelector(selectOrderData);

    const handleScheduleAppointment = async () => {
        var path = `${ROUTES.APPOINTMENTS.BASE}${ROUTES.APPOINTMENTS.SUBROUTES.NEW}?orderId=${repairOrderId}&`;

        const customerIdParam = orderData.customer.id;
        const vehicleIdParam = orderData.vehicle.id;
        const inChargeUserIdParam = orderData.inChargeUserId;
        const masterListItems = Object.values(items);
        const masterListItemIdsParam = masterListItems.some((x) => x.isDeclined)
            ? masterListItems.filter((x) => !x.isApproved).map((x) => x.masterItemId)
            : [];

        if (customerIdParam) {
            path += `customerId=${customerIdParam}&`;
        }
        if (vehicleIdParam) {
            path += `vehicleId=${vehicleIdParam}&`;
        }
        if (inChargeUserIdParam) {
            path += `inChargeUserId=${inChargeUserIdParam}&`;
        }
        if (masterListItemIdsParam.length > 0) {
            path += `masterListItemIds=${masterListItemIdsParam}&`;
        }

        navigate(path.slice(0, -1));
    };

    return (
        <>
            {isSpreadsheetSelected && checklistName && (
                <DivTitle>
                    Checklist: <strong>{checklistName}</strong>
                </DivTitle>
            )}
            <Header>
                <AlignCenterVertically>
                    <SyncEstimateButton
                        orderId={repairOrderId}
                        style={{ marginRight: 10 }}
                        onEstimateLoaded={fetchItems}
                    />
                    <PriorityFilterArea>
                        <Dropdown
                            name="priorityFilter"
                            options={priorityFilterOptions}
                            value={priorityFilterSelectedOption}
                            cmosVariant="roundedPrimary"
                            onChange={handlePriorityFilterChange}
                        />
                    </PriorityFilterArea>
                    {enableManualPaymentRegistration && (
                        <OrderOnlinePayments itemFilter={itemFilter} orderId={repairOrderId} />
                    )}
                </AlignCenterVertically>
                <AlignCenterVertically>
                    {repairShopSettings?.features.estimateReview &&
                    userPermission.allowShowEstimates ? (
                        <EstimateContainer>
                            <DivShowEstimateToConsumer>
                                {t('orderDetails.inspectionForms.showEstimateToCustomer')}
                            </DivShowEstimateToConsumer>
                            <Checkbox
                                onChange={(e: any, checked: boolean) => {
                                    onShowEstimatesChange(checked);
                                }}
                                name={'showEstimateToConsumer'}
                                checked={showEstimates}
                                disabled={orderIsClosed}
                            />
                        </EstimateContainer>
                    ) : (
                        ''
                    )}
                    {!isEnterprise &&
                        userPermission.allowEditAppointments &&
                        !repairShopSettings?.features.syncAppts && (
                            <>
                                <Divider orientation="vertical" flexItem />
                                <Tooltip
                                    content={t('orderDetails.inspectionForms.scheduleAppointment')}
                                >
                                    <IconButton onClick={handleScheduleAppointment} size="medium">
                                        <CalendarIcon
                                            fill={theme.palette.primary.light}
                                            size={32}
                                        />
                                    </IconButton>
                                </Tooltip>
                            </>
                        )}

                    <Divider orientation="vertical" flexItem />
                    <Tooltip content={t('orderDetails.inspectionForms.spreadSheetView')}>
                        <IconButton
                            disabled={isSpreadsheetSelectedLoading}
                            onClick={() => setSpreadsheetSelected(true)}
                            size="large"
                        >
                            <GridIcon
                                fill={
                                    isSpreadsheetSelected
                                        ? theme.palette.primary.light
                                        : theme.palette.neutral[4]
                                }
                            />
                        </IconButton>
                    </Tooltip>
                    <Tooltip content={t('orderDetails.inspectionForms.normalView')}>
                        <IconButton
                            disabled={isSpreadsheetSelectedLoading}
                            onClick={() => setSpreadsheetSelected(false)}
                            size="large"
                        >
                            <ListIcon
                                fill={
                                    !isSpreadsheetSelected
                                        ? theme.palette.primary.light
                                        : theme.palette.neutral[4]
                                }
                            />
                        </IconButton>
                    </Tooltip>
                    <Divider orientation="vertical" flexItem />
                    <Tooltip content={t('orderDetails.inspectionForms.downloadAllEvidence')}>
                        <span>
                            <DownloadEvidenceButton
                                orderNumber={repairOrderNumber}
                                repairOrderId={repairOrderId}
                            />
                        </span>
                    </Tooltip>
                    <Tooltip content={t('orderDetails.inspectionForms.attachFile')}>
                        <IconButton
                            onClick={handleAttachFile}
                            size="large"
                            disabled={orderIsClosed}
                        >
                            <input
                                ref={attachFileRef}
                                type="file"
                                disabled={!userPermission.allowEditEstimates || orderIsClosed}
                                style={{ display: 'none' }}
                                onChange={handleFileChange}
                                accept=".doc, .docx, .xlsx, .xls, .text, .txt, .pdf"
                            />
                            <ClipIcon />
                        </IconButton>
                    </Tooltip>
                    <DownloadEstimatesExcelButton orderId={repairOrderId} />
                </AlignCenterVertically>
            </Header>
            <PackagesPopupProvider>
                {isSpreadsheetSelected ? (
                    <SpreadsheetView orderId={repairOrderId} itemFilter={itemFilter} />
                ) : (
                    <NormalView repairOrderId={repairOrderId} itemFilter={itemFilter} />
                )}
            </PackagesPopupProvider>
        </>
    );
};

export default InspectionForms;

function useIsSpreadsheetSelected(): [
    value: boolean,
    set: (value: boolean) => void,
    isLoading: boolean
] {
    const defaultValue = useSelector(selectIsSpreadsheetSelected) ?? false;
    const [{ data: uiSettings, isFetching }, update] = useInspectionFormsUserStorage();
    const value = uiSettings.spreadsheetEnabled ?? defaultValue;

    const set = useCallback(
        (value: boolean) => {
            update((s) => ({
                ...s,
                spreadsheetEnabled: value,
            }));
        },
        [update]
    );

    return [value, set, isFetching];
}

const DivTitle = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[7],
    ...theme.typography.h4Inter,
    fontWeight: 'normal',
    marginTop: 33,
    marginBottom: 12,
    marginLeft: 40,
}));

const DivShowEstimateToConsumer = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[6],
    fontFamily: 'Inter',
    fontSize: '12px',
    fontStyle: 'normal',
    fontWeight: '400',
}));
