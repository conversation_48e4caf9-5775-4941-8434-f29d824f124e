import { Box, IconButton } from '@mui/material';
import clsx from 'clsx';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { IconSize } from 'common/styles/IconSize';
import { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from 'store';
import { selectItemById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import approveItem from 'store/slices/order/orderInspectionFormsSlice/thunks/approveItem';
import declineItem from 'store/slices/order/orderInspectionFormsSlice/thunks/declineItem';
import useApproveDeclineStyles from './styles';

type ApproveDeclineActionsViewProps = {
    repairOrderId: number;
    repairId: number | null;
    masterItemId: number;
    disabled?: boolean;
};

export default function ApproveDeclineActionsView({
    repairId,
    repairOrderId,
    masterItemId,
    disabled,
}: ApproveDeclineActionsViewProps) {
    const dispatch = useAppDispatch();
    const styles = useApproveDeclineStyles();
    const { isApproved, isDeclined } = useSelector((s: RootState) =>
        selectItemById(s, masterItemId)
    );

    const onApprove = useCallback(() => {
        dispatch(
            approveItem({
                masterItemId,
                repairId,
                repairOrderId,
                isApproved: !isApproved,
            })
        );
    }, [dispatch, repairId, repairOrderId, masterItemId, isApproved]);

    const onDecline = useCallback(() => {
        dispatch(
            declineItem({
                masterItemId,
                repairOrderId,
                isDeclined: !isDeclined,
            })
        );
    }, [dispatch, repairOrderId, masterItemId, isDeclined]);

    return (
        <Box display="flex" gap={1}>
            <IconButton
                disabled={disabled}
                onClick={onApprove}
                size="small"
                className={clsx(styles.btn, styles.approveBtn, isApproved && styles.activeBtn)}
            >
                <CheckIcon size={IconSize.S} fill="currentColor" />
            </IconButton>

            <IconButton
                disabled={disabled}
                onClick={onDecline}
                size="small"
                className={clsx(styles.btn, styles.declineBtn, isDeclined && styles.activeBtn)}
            >
                <CloseIcon size={IconSize.S} fill="currentColor" />
            </IconButton>
        </Box>
    );
}
