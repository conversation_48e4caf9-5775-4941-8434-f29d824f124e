import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';

const useApproveDeclineStyles = makeStyles((theme) => ({
    btn: {
        padding: 0,
        boxShadow: '0 0 0 1px currentColor',

        '&:disabled': {
            color: theme.palette.neutral[5],
        },
        '&:hover': {
            color: 'var(--btn-color)',
        },
    },
    activeBtn: {
        boxShadow: '0 0 0 3px currentColor',
        color: 'var(--btn-color)',
    },
    approveBtn: {
        '--btn-color': Colors.Success,
    },
    declineBtn: {
        '--btn-color': Colors.Error,
    },
}));

export default useApproveDeclineStyles;
