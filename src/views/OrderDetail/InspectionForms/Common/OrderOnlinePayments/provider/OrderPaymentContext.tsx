import { useQuery } from '@tanstack/react-query';
import PaymentsApi from 'api/payments';
import { OrderPaymentDto, OrderPaymentMethodType } from 'api/payments/_common';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useOrderPaymentTranslations } from '../hooks/useOrderPaymentTranslations';
import { useTotalApprovedPayment } from '../hooks/useTotalApprovedPayment';
import { RegisterPaymentDropdownOption } from '../PaymentsDrawer/PaymentTabs/PaymentRegister/PaymentDropdown';
import {
    HistoryData,
    initialPaymentState,
    OrderPayment,
    PaymentState,
    TPaymentDetail,
} from './useOrderPayment';

type PaymentRegisterContextProps = {
    children: React.ReactNode;
    orderId: number;
    handleClose: () => void;
    resetPanel: boolean;
};

const selectCurrencyTemplate = createSelector(
    selectSettings,
    (s) => s.internationalization.currency
);

export const OrderPaymentContext = ({
    children,
    orderId,
    handleClose,
    resetPanel,
}: PaymentRegisterContextProps) => {
    const labels = useOrderPaymentTranslations();
    const dropdownLabels =
        labels.register.fields.paymentMethodDropdown.fields.paymentMethod.dropdown;

    const currencyTemplate = useSelector(selectCurrencyTemplate);
    const totalApproved = useTotalApprovedPayment();

    const [payment, setPayment] = useState<PaymentState>(
        initialPaymentState(dropdownLabels.cash, totalApproved)
    );
    const [expandedMethod, setExpandedMethod] = useState(1);
    const [historyData, setHistoryData] = useState<HistoryData[]>([]);

    const paymentMethodOptions: RegisterPaymentDropdownOption[] = useMemo(
        () => [
            { label: dropdownLabels.cash, value: OrderPaymentMethodType.CASH },
            { label: dropdownLabels.card, value: OrderPaymentMethodType.DEBIT_CREDIT_CARD },
            { label: dropdownLabels.bank, value: OrderPaymentMethodType.BANK_TRANSFER },
        ],
        [dropdownLabels]
    );

    const amountPaid = useMemo(
        () => payment.methods.reduce((total, method) => total + method.amountPaid, 0),
        [payment.methods]
    );

    const pendingToPaid = useMemo(() => {
        const lastPendingValue =
            historyData.length > 0 ? historyData[0].pendingValue : totalApproved;
        return lastPendingValue - amountPaid;
    }, [totalApproved, historyData, amountPaid]);

    useEffect(() => {
        if (historyData.length) {
            const totalPendingToPay = historyData[0].pendingValue;
            setPayment(initialPaymentState(dropdownLabels.cash, totalPendingToPay));
        }
    }, [historyData, dropdownLabels]);

    const currencyParse = useCallback(
        (value: number) => {
            const formattedValue = value.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            });
            return currencyTemplate.replace('{0}', formattedValue);
        },
        [currencyTemplate]
    );

    const paymentDetails = useMemo(
        () => ({
            totalApproved: currencyParse(totalApproved),
            pendingPayment: currencyParse(pendingToPaid),
            amountPaid: currencyParse(amountPaid),
            methods: payment.methods.map((method) => ({
                label: paymentMethodOptions.find((option) => option.value === method.paymentMethod)
                    ?.label,
                amount: currencyParse(method.amountPaid),
                amountNumber: method.amountPaid,
            })),
        }),
        [
            totalApproved,
            pendingToPaid,
            amountPaid,
            payment.methods,
            paymentMethodOptions,
            currencyParse,
        ]
    ) as TPaymentDetail;

    const paymentMethodOptionsAvailable = useMemo(
        () =>
            paymentMethodOptions.filter(
                (option) => !payment.methods.some((method) => method.paymentMethod === option.value)
            ),
        [payment.methods, paymentMethodOptions]
    );

    const paymentMethodLabel = useCallback(
        (method: OrderPaymentMethodType) => {
            switch (method) {
                case OrderPaymentMethodType.CASH:
                    return dropdownLabels.cash;
                case OrderPaymentMethodType.DEBIT_CREDIT_CARD:
                    return dropdownLabels.card;
                case OrderPaymentMethodType.BANK_TRANSFER:
                    return dropdownLabels.bank;
                default:
                    return '';
            }
        },
        [dropdownLabels]
    );

    const paymentDateFormat = useCallback(
        (date: string) => moment(date).format(`${labels.dateFormant} ${labels.timeFormat}`),
        [labels]
    );

    const transformOrderPaymentsToHistoryData = useCallback(
        (orderPayments: OrderPaymentDto[]): HistoryData[] => {
            const historyData: HistoryData[] = [];

            let paymentPending = totalApproved;
            let totalAmountPaid = 0;

            orderPayments.forEach((orderPayment, groupId) => {
                if (orderPayment.isPaymentLink) {
                    const amount = orderPayment.linkMethod?.amount || 0;
                    const method = orderPayment.linkMethod?.paymentMethod || '';
                    const historyRecord: HistoryData = {
                        paymentId: orderPayment.paymentId!,
                        groupId: groupId,
                        teamMember: orderPayment.createdBy as string,
                        paymentDate: paymentDateFormat(orderPayment.createdAt ?? ''),
                        paymentMethod: method,
                        amountPaid: currencyParse(amount),
                        totalAmountPaid: currencyParse((totalAmountPaid += amount)),
                        paymentPending: currencyParse(paymentPending - amount),
                        comments: orderPayment.comments || '--',
                        pendingValue: paymentPending - amount,
                        amount: amount,
                        synchronized: orderPayment.linkMethod?.synchronized,
                    };

                    paymentPending -= amount;
                    historyData.push(historyRecord);
                } else {
                    orderPayment.methods!.forEach((method) => {
                        const historyRecord: HistoryData = {
                            paymentId: orderPayment.paymentId!,
                            groupId: groupId,
                            teamMember: orderPayment.createdBy as string,
                            paymentDate: paymentDateFormat(orderPayment.createdAt ?? ''),
                            paymentMethod: paymentMethodLabel(method.paymentMethod),
                            paymentMethodType: method.paymentMethod,
                            amountPaid: currencyParse(method.amount),
                            totalAmountPaid: currencyParse((totalAmountPaid += method.amount)),
                            paymentPending: currencyParse(paymentPending - method.amount),
                            comments: orderPayment.comments || '--',
                            pendingValue: paymentPending - method.amount,
                            amount: method.amount,
                            synchronized: method.synchronized,
                        };

                        paymentPending -= method.amount;
                        historyData.push(historyRecord);
                    });
                }
            });

            return historyData.sort((a, b) => {
                if (a.groupId === b.groupId) {
                    return b.paymentId - a.paymentId;
                }
                return b.groupId - a.groupId;
            });
        },
        [paymentDateFormat, paymentMethodLabel, currencyParse, totalApproved]
    );

    useQuery(
        ['OrderHistoryPayment', orderId, resetPanel],
        () => PaymentsApi.getHistoryPayment(orderId),
        {
            enabled: Boolean(orderId),
            onSuccess: (data) => {
                if (data) {
                    setHistoryData(transformOrderPaymentsToHistoryData(data));
                }
            },
        }
    );

    useEffect(() => {
        setExpandedMethod(payment.methods.length);
    }, [payment.methods.length]);

    useEffect(() => {
        if (resetPanel) {
            setPayment(initialPaymentState(dropdownLabels.cash, totalApproved));
            setExpandedMethod(1);
        }
    }, [resetPanel, dropdownLabels.cash, totalApproved]);

    const contextValue = useMemo(
        () => ({
            orderId,
            payment,
            setPayment,
            expandedMethod,
            setExpandedMethod,
            handleClose,
            paymentMethodOptions,
            paymentMethodOptionsAvailable,
            currencyTemplate,
            pendingToPaid,
            paymentDetails,
            historyData,
            currencyParse,
        }),
        [
            orderId,
            payment,
            expandedMethod,
            handleClose,
            paymentMethodOptions,
            paymentMethodOptionsAvailable,
            currencyTemplate,
            pendingToPaid,
            paymentDetails,
            historyData,
            currencyParse,
        ]
    );

    return <OrderPayment.Provider value={contextValue}>{children}</OrderPayment.Provider>;
};
