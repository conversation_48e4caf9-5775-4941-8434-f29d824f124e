import {
    OrderPaymentDto,
    OrderPaymentMethodDto,
    OrderPaymentMethodType,
} from 'api/payments/_common';
import moment from 'moment';
import { createContext, Dispatch, SetStateAction, useContext } from 'react';
import { RegisterPaymentDropdownOption } from '../PaymentsDrawer/PaymentTabs/PaymentRegister/PaymentDropdown';

export const OrderPayment = createContext<UseOrderPayment | null>(null);

export interface UseOrderPayment {
    orderId: number;
    payment: PaymentState;
    setPayment: Dispatch<SetStateAction<PaymentState>>;
    expandedMethod: number;
    setExpandedMethod: Dispatch<SetStateAction<number>>;
    handleClose: () => void;
    paymentMethodOptions: RegisterPaymentDropdownOption[];
    paymentMethodOptionsAvailable: RegisterPaymentDropdownOption[];
    currencyTemplate: string;
    pendingToPaid: number;
    historyData: HistoryData[];
    paymentDetails: TPaymentDetail;
    currencyParse: (value: number) => string;
}

export type PaymentState = Omit<OrderPaymentDto, 'methods'> & {
    methods: PaymentMethodState[];
};
export type PaymentMethodState = Omit<OrderPaymentMethodDto, 'amount' | 'paymentMethod'> & {
    paymentMethod: OrderPaymentMethodType | null;
    paymentMethodLogLabel: string;
    amountPaid: number;
};

export const initialMethodState: (
    _methodLogLabel: string,
    initialAmount?: number
) => PaymentMethodState = (methodLogLabel, initialAmount = 0) => ({
    paymentMethodId: 1,
    paymentMethod: OrderPaymentMethodType.CASH,
    paymentMethodLogLabel: methodLogLabel,
    amountPaid: initialAmount,
});

export const initialPaymentState: (
    _methodLogLabel: string,
    initialAmount?: number
) => PaymentState = (methodLogLabel, initialAmount) => ({
    methods: [initialMethodState(methodLogLabel, initialAmount)],
    paymentDateTime: moment().format('YYYY-MM-DD'),
    comments: '',
});

export type TPaymentDetail = {
    totalApproved: string;
    pendingPayment: string;
    amountPaid: string;
    methods: PaymentMethod[];
};

export type PaymentMethod = {
    label: string | undefined;
    amount: string;
    amountNumber: number;
};

export type HistoryData = {
    paymentId: number;
    groupId: number;
    teamMember: string;
    paymentDate: string;
    paymentMethod: string;
    paymentMethodType?: OrderPaymentMethodType;
    amountPaid: string;
    totalAmountPaid: string;
    paymentPending: string;
    comments: string;
    pendingValue: number;
    amount?: number;
    synchronized?: boolean;
};

export const useOrderPayment = (): UseOrderPayment => {
    const context = useContext(OrderPayment);

    if (!context) {
        throw new Error('useOrderPayment should be used within PaymentRegisterContext');
    }

    return context;
};
