import useLockedBody from 'common/hooks/useLockedBody';
import { useOpen } from 'common/hooks/useOpen';
import isEqual from 'lodash/isEqual';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootState, useAppSelector } from 'store';
import { ItemData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { selectIsOrderClosed } from '../../../../../store/slices/order/orderDetails';
import { selectRowGroups } from '../../SpreadsheetView/Spreadsheet';
import { PaymentsButton } from './PaymentsButton';
import { PaymentsDrawer } from './PaymentsDrawer';
import { PaymentHistory } from './PaymentsDrawer/PaymentTabs/PaymentHistory';
import { PaymentRegister } from './PaymentsDrawer/PaymentTabs/PaymentRegister';
import { OrderPaymentContext } from './provider/OrderPaymentContext';

export enum OrderPaymentsTabValue {
    REGISTER,
    HISTORY,
}

export type OrderPaymentsTab = {
    value: OrderPaymentsTabValue;
    label: string;
    content: React.ReactNode;
};

type OrderOnlinePaymentsProps = {
    orderId: number;
    itemFilter: (item: ItemData) => boolean;
};

function OrderOnlinePayments({ itemFilter, orderId }: OrderOnlinePaymentsProps) {
    const { t } = useTranslation();
    const prefix = 'orderDetails.inspectionForms.payments';

    const [open, handleOpen, handleClose] = useOpen();
    const [selectedTab, setSelectedTab] = useState<OrderPaymentsTabValue>(
        OrderPaymentsTabValue.REGISTER
    );

    useLockedBody(open);

    const rowsGroups = useAppSelector(
        useCallback(
            (r: RootState) =>
                selectRowGroups(r, { orderId, filter: itemFilter, onlyApproved: true }),
            [itemFilter, orderId]
        ),
        isEqual
    );
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const handleChangeTab = (newSelected: OrderPaymentsTabValue) => setSelectedTab(newSelected);

    const handleOpenPaymentPanel = useCallback(() => {
        handleOpen();
        setSelectedTab(OrderPaymentsTabValue.REGISTER);
    }, [handleOpen]);

    const paymentsTabs: OrderPaymentsTab[] = useMemo(
        () => [
            {
                value: OrderPaymentsTabValue.REGISTER,
                label: t(`${prefix}.register.tab`),
                content: <PaymentRegister />,
            },
            {
                value: OrderPaymentsTabValue.HISTORY,
                label: t(`${prefix}.history.tab`),
                content: <PaymentHistory />,
            },
        ],
        [t]
    );

    return (
        <OrderPaymentContext orderId={orderId} handleClose={handleClose} resetPanel={open}>
            <PaymentsButton
                disabled={rowsGroups.length === 0 || orderIsClosed}
                handleOpen={handleOpenPaymentPanel}
            />
            <PaymentsDrawer
                tabs={paymentsTabs}
                TabSelected={renderTab(paymentsTabs, selectedTab)}
                {...{ open, handleClose, selectedTab, handleChangeTab }}
            />
        </OrderPaymentContext>
    );
}

const renderTab = (tabs: OrderPaymentsTab[], selectedTab: OrderPaymentsTabValue) => {
    const Component = tabs.find((tab) => tab.value === selectedTab)?.content;

    if (!Component) return null;

    return Component;
};

export default OrderOnlinePayments;
