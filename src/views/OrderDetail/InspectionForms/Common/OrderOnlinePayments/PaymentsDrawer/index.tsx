import { TabContext } from '@mui/lab';
import { Box, Drawer, styled, Tab, Tabs } from '@mui/material';
import React, { useCallback } from 'react';
import { OrderPaymentsTab, OrderPaymentsTabValue } from '..';

type PaymentsDrawerProps = {
    open: boolean;
    handleClose: () => void;
    selectedTab: OrderPaymentsTabValue;
    handleChangeTab: (_selected: OrderPaymentsTabValue) => void;
    tabs: OrderPaymentsTab[];
    TabSelected: React.ReactNode;
};

export const PaymentsDrawer = React.memo(
    ({
        open,
        handleClose,
        selectedTab,
        handleChangeTab,
        tabs,
        TabSelected,
    }: PaymentsDrawerProps) => {
        const handleTabChange: (
            event: React.SyntheticEvent,
            newSelected: OrderPaymentsTabValue
        ) => void = useCallback(
            (_, newSelected: OrderPaymentsTabValue) => {
                handleChangeTab(newSelected);
            },
            [handleChangeTab]
        );

        return (
            <Drawer
                anchor="right"
                PaperProps={{
                    sx: {
                        background: '#FFFFFF',
                        borderTopLeftRadius: 10,
                        borderBottomLeftRadius: 10,
                        borderTopRightRadius: 0,
                        borderBottomRightRadius: 0,
                        display: 'flex',
                        flexDirection: 'column',
                        width: 400,
                        overflowX: 'hidden',
                        overflowY: 'auto',
                    },
                }}
                open={open}
                onClose={handleClose}
            >
                <TabContext value={selectedTab}>
                    <STabs
                        value={selectedTab}
                        onChange={handleTabChange}
                        indicatorColor="primary"
                        textColor="primary"
                    >
                        {tabs.map(({ label, value }) => (
                            <STab key={value} aria-label={label} label={label} value={value} />
                        ))}
                    </STabs>
                </TabContext>
                <RootContainer>
                    <Box sx={{ padding: '0 16px', height: '100%', position: 'relative' }}>
                        {TabSelected}
                    </Box>
                </RootContainer>
            </Drawer>
        );
    }
);

const RootContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    height: '100%',
    position: 'relative',
    width: 400,
    paddingTop: 0,
    overflowX: 'hidden',
    scrollbarWidth: 'thin',
    scrollbarColor: '#c1c1c1',
    '&::-webkit-scrollbar': {
        width: '6px',
    },
}));

const STabs = styled(Tabs)(({ theme }) => ({
    ...theme.typography.body1,
    backgroundColor: '#FFFFFF',
    color: theme.palette.primary.main,
    borderBottom: 'solid',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(201, 205, 211, 0.5)',
    '& .MuiTabs-indicator': {
        backgroundColor: '#FFFFFF',
    },
    '& .MuiButtonBase-root': {
        minWidth: 0,
        width: '50%',
    },
    '& .MuiTab-root': {
        paddingLeft: 0,
        paddingRight: 0,
        textTransform: 'none',
        color: '#FFFFFF',
        backgroundColor: theme.palette.primary.main,
        fontWeight: 'bold',
    },
    '& .Mui-selected': {
        color: theme.palette.primary.main,
        backgroundColor: '#FFFFFF',
        fontWeight: 'bold',
    },
}));

const STab = styled(Tab)(({ theme }) => ({
    root: {
        ...theme.typography.body2,
    },
    selected: {
        fontWeight: 'bold',
    },
}));
