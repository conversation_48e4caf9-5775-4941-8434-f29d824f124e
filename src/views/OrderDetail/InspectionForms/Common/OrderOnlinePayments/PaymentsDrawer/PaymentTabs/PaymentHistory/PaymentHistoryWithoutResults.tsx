import { Box, styled } from '@mui/material';
import React from 'react';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';

export const PaymentHistoryWithoutResults = React.memo(() => {
    const { history: labels } = useOrderPaymentTranslations();
    return (
        <Root>
            <Title>{labels.withoutResults.title}</Title>
            <Description>{labels.withoutResults.description}</Description>
        </Root>
    );
});

const Root = styled(Box)(() => ({
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    alignItems: 'center',
    textAlign: 'center',
}));

const Title = styled(Box)(({ theme }) => ({
    ...theme.typography.h1Inter,
    color: '#0069FF',
    fontSize: '21px',
    fontWeight: 700,
}));

const Description = styled(Box)(({ theme }) => ({
    ...theme.typography.h2Inter,
    color: '#6A6E72',
    fontSize: '14px',
    fontWeight: 400,
}));
