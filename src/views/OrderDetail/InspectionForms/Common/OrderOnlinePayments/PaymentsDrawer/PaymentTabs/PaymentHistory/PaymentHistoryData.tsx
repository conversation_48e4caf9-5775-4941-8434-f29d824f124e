import { styled } from '@mui/material';
import Box from '@mui/material/Box';
import { OrderPaymentMethodType } from 'api/payments/_common';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from 'store';
import {
    selectFeatureFlags,
    selectRepairShopIntegrationAccountName,
} from 'store/slices/globalSettingsSlice/selectors';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { HistoryData, useOrderPayment } from '../../../provider/useOrderPayment';
import { SyncPaymentsButton } from './SyncPaymentsButton';

export const PaymentHistoryData = React.memo(() => {
    const { historyData } = useOrderPayment();

    return (
        <Root>
            {historyData.map((data, index) => (
                <Box key={index}>
                    <PaymentDetailRowContainer historyData={data} />
                    <DividerStyled />
                </Box>
            ))}
        </Root>
    );
});

const PaymentDetailRowContainer = React.memo(({ historyData }: { historyData: HistoryData }) => {
    const {
        history: { record: labels },
    } = useOrderPaymentTranslations();

    const { paymentFormThirdPartyFeature } = useAppSelector(selectFeatureFlags);

    const data = useMemo(
        () => [
            { label: labels.teamMember, value: historyData.teamMember },
            { label: labels.paymentDate, value: historyData.paymentDate },
            { label: labels.paymentMethod, value: historyData.paymentMethod },
            { label: labels.amountPaid, value: historyData.amountPaid },
            { label: labels.totalAmountPaid, value: historyData.totalAmountPaid },
            { label: labels.paymentPending, value: historyData.paymentPending },
            { label: labels.comments, value: historyData.comments },
        ],
        [historyData, labels]
    );

    return (
        <InitialValuesBlock>
            <InitialValuesRow>
                <Chip>{labels.title}</Chip>
            </InitialValuesRow>
            {data.map(({ label, value }, index) => (
                <InitialValuesRow key={index}>
                    <InitialValuesLeftColumn>{label + ':'}</InitialValuesLeftColumn>
                    <HistoryRightColumn>{value}</HistoryRightColumn>
                </InitialValuesRow>
            ))}
            {paymentFormThirdPartyFeature && <PaymentSynchronizationRow {...{ historyData }} />}
        </InitialValuesBlock>
    );
});

const PaymentSynchronizationRow = React.memo(({ historyData }: { historyData: HistoryData }) => {
    const { t } = useTranslation();
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);
    const {
        thirdParty: { synchronization: labels },
    } = useOrderPaymentTranslations();
    const method = historyData.paymentMethodType ?? historyData.paymentMethod;

    const [isSyncing, setIsSyncing] = useState(historyData.synchronized);

    return (
        <InitialValuesRow>
            {isSyncing ? (
                <InitialValuesLeftColumn sx={{ color: '#36CE91', whiteSpace: 'nowrap' }}>
                    {t(labels.success, { integratedAccountName })}
                </InitialValuesLeftColumn>
            ) : (
                <InitialValuesLeftColumn sx={{ color: '#F15857' }}>
                    {t(labels.error, { integratedAccountName }) + ':'}
                </InitialValuesLeftColumn>
            )}
            <HistoryRightColumn>
                {!isSyncing && (
                    <SyncPaymentsButton
                        paymentId={historyData.paymentId}
                        paymentMethod={method}
                        isManualPayment={isManualPaymentMethod(method)}
                        setIsSyncing={setIsSyncing}
                    />
                )}
            </HistoryRightColumn>
        </InitialValuesRow>
    );
});

const isManualPaymentMethod = (paymentMethod: string) => {
    return (
        paymentMethod === OrderPaymentMethodType.CASH ||
        paymentMethod === OrderPaymentMethodType.BANK_TRANSFER ||
        paymentMethod === OrderPaymentMethodType.DEBIT_CREDIT_CARD
    );
};

const Root = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    height: '100%',
    position: 'relative',
    width: '100%',
}));

const Chip = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '4px 8px',
    backgroundColor: '#EFEFEF',
    borderRadius: '5px',
    marginBottom: '16px',
    fontWeight: 700,
    color: '#0069FF',
}));

const DividerStyled = styled(Box)(({ theme }) => ({
    width: '100%',
    height: 0,
    borderTop: '1px solid',
    borderColor: theme.palette.grey[300],
    opacity: 0.5,
}));

const InitialValuesBlock = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '100%',
    fontFamily: 'Roboto',
    margin: '24px 0',
}));

const InitialValuesRow = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
}));

const InitialValuesLeftColumn = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    maxWidth: 200,
    minWidth: 115,
    color: '#919CA5',
}));

const HistoryRightColumn = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    textAlign: 'left',
    width: '45%',
    color: '#5C6477',
}));
