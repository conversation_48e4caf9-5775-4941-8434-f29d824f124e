/* eslint-disable react-hooks/exhaustive-deps */
import { OrderPaymentMethodType } from 'api/payments/_common';
import BillsIcon from 'common/components/Icons/BillsIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import React, { useCallback } from 'react';
import { useOrderPaymentTranslations } from '../../../../hooks/useOrderPaymentTranslations';
import { PaymentMethodState, useOrderPayment } from '../../../../provider/useOrderPayment';

type DropdownOption = {
    label: string;
    value: OrderPaymentMethodType;
};

export const PaymentSelectMethodField = ({
    method,
    handleChangeMethod,
}: {
    method: PaymentMethodState;
    handleChangeMethod: (_newValues: PaymentMethodState) => void;
}) => {
    const { register: labels } = useOrderPaymentTranslations();
    const { paymentMethodOptions, paymentMethodOptionsAvailable } = useOrderPayment();

    const selectedPaymentMethod = paymentMethodOptions?.find(
        (option) => option.value === method.paymentMethod
    );

    const handleValueChange = useCallback(
        (newValue: DropdownOption | null) => {
            if (newValue) {
                handleChangeMethod({
                    ...method,
                    paymentMethod: newValue.value,
                    paymentMethodLogLabel: newValue.label,
                });
            }
        },
        [handleChangeMethod, method]
    );

    return (
        <Dropdown
            name={'shortcuts'}
            CustomDropdownIndicator={CustomDropdownIndicator}
            onChange={handleValueChange}
            placeholder={labels.fields.paymentMethodDropdown.fields.paymentMethod.placeholder}
            options={paymentMethodOptionsAvailable}
            value={selectedPaymentMethod}
        />
    );
};

const CustomDropdownIndicator = React.memo(() => <BillsIcon style={{ paddingRight: 5 }} />);
