import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    styled,
    Typography,
} from '@mui/material';
import { OrderPaymentMethodType } from 'api/payments/_common';
import { UpIcon } from 'common/components/Icons/UpIcon';
import React, { useCallback } from 'react';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { PaymentMethodState, useOrderPayment } from '../../../provider/useOrderPayment';
import { PaymentRegisterInput } from './fields/common/PaymentRegisterInput';
import { PaymentAmountField } from './fields/PaymentAmountField';
import { PaymentSelectMethodField } from './fields/PaymentSelectMethodField';
import { PaymentDeleteModal } from './PaymentDeleteModal';

export type RegisterPaymentDropdownOption = {
    label: string;
    value: Partial<OrderPaymentMethodType>;
};

interface PaymentDropdownMethodProps {
    method: PaymentMethodState;
    id: number;
}

export const PaymentDropdownMethod = React.memo(({ method, id }: PaymentDropdownMethodProps) => {
    const { register: labels } = useOrderPaymentTranslations();
    const { expandedMethod, setExpandedMethod } = useOrderPayment();

    const handleExpandAccordion = useCallback(() => setExpandedMethod(id), [setExpandedMethod, id]);

    return (
        <Container>
            <Accordion expanded={expandedMethod === id} onChange={handleExpandAccordion}>
                <SAccordionSummary
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                    expandIcon={<UpIcon fill="#5c6477" />}
                >
                    <PaymentDeleteModal id={id} />
                    <SAccordionTitle>
                        {labels.fields.paymentMethodDropdown.title} {id}
                    </SAccordionTitle>
                </SAccordionSummary>
                <PaymentDropdownDetails method={method} id={id} />
            </Accordion>
        </Container>
    );
});

const PaymentDropdownDetails = React.memo(
    ({ method, id }: { method: PaymentMethodState; id: number }) => {
        const { register: labels } = useOrderPaymentTranslations();
        const { setPayment } = useOrderPayment();

        const fieldsLabels = labels.fields.paymentMethodDropdown.fields;

        const handleChangeMethod = useCallback(
            (newValues: PaymentMethodState) => {
                setPayment((prev) => ({
                    ...prev,
                    methods: prev.methods.map((method, index) =>
                        index + 1 === id ? { ...method, ...newValues } : method
                    ),
                }));
            },
            [setPayment, id]
        );

        return (
            <AccordionDetails
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    padding: '12px 16px 22px',
                }}
            >
                <Box>
                    <PaymentRegisterInput label={fieldsLabels.amountPaid}>
                        <PaymentAmountField
                            method={method}
                            handleChangeMethod={handleChangeMethod}
                        />
                    </PaymentRegisterInput>
                </Box>
                <Box>
                    <PaymentRegisterInput label={fieldsLabels.paymentMethod.label}>
                        <PaymentSelectMethodField
                            method={method}
                            handleChangeMethod={handleChangeMethod}
                        />
                    </PaymentRegisterInput>
                </Box>
            </AccordionDetails>
        );
    }
);

const Container = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    position: 'relative',
    marginBottom: '16px',
}));

const SAccordionSummary = styled(AccordionSummary)(() => ({
    backgroundColor: '#f6f6f6',
    width: '100%',
    minHeight: 46,
    height: 46,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    '&.Mui-expanded': {
        minHeight: 46,
        height: 46,
    },
    '.MuiAccordionSummary-content': {
        margin: 0,
        alignItems: 'center',
    },
}));

const SAccordionTitle = styled(Typography)(() => ({
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: 700,
    fontSize: 12,
    color: '#6A6E72',
    marginLeft: '12px',
}));
