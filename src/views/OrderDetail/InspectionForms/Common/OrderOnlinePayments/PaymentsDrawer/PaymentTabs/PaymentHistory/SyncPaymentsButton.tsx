import { IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import PaymentsApi from 'api/payments';
import { SyncUpIcon } from 'common/components/Icons/SyncUpIcon';
import useToasters from 'common/hooks/useToasters';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from 'store';
import { selectRepairShopIntegrationAccountName } from 'store/slices/globalSettingsSlice/selectors';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { useOrderPayment } from '../../../provider/useOrderPayment';

export const SyncPaymentsButton = ({
    paymentId,
    paymentMethod,
    isManualPayment,
    setIsSyncing,
}: {
    paymentId: number;
    paymentMethod: string;
    isManualPayment: boolean;
    setIsSyncing: React.Dispatch<React.SetStateAction<boolean | undefined>>;
}) => {
    const { syncPayments, isSyncPaymentsLoading } = useSyncPaymentMutation(setIsSyncing);

    const handleClick = useCallback(() => {
        syncPayments({ paymentId, paymentMethod, isManualPayment });
    }, [syncPayments, paymentId, paymentMethod, isManualPayment]);

    return (
        <IconButton
            onClick={handleClick}
            sx={{
                marginTop: '-8px',
                marginLeft: '24px',
                ...(isSyncPaymentsLoading && {
                    '& svg': {
                        animation: 'spin 1.8s linear infinite',
                    },
                    '@keyframes spin': {
                        '0%': {
                            transform: 'rotate(0deg)',
                        },
                        '100%': {
                            transform: 'rotate(360deg)',
                        },
                    },
                }),
            }}
        >
            <SyncUpIcon size={30} />
        </IconButton>
    );
};

const useSyncPaymentMutation = (
    setIsSyncing: React.Dispatch<React.SetStateAction<boolean | undefined>>
) => {
    const { t } = useTranslation();
    const toasters = useToasters();
    const { thirdParty: labels } = useOrderPaymentTranslations();
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);
    const { orderId } = useOrderPayment();

    const { mutate: syncPayments, isLoading: isSyncPaymentsLoading } = useMutation(
        ({
            paymentId,
            paymentMethod,
            isManualPayment,
        }: {
            paymentId: number;
            paymentMethod: string;
            isManualPayment: boolean;
        }) =>
            PaymentsApi.syncPayments(orderId, paymentId, {
                paymentMethods: [paymentMethod],
                isManualPayment,
            }),
        {
            onSuccess: ({ results }) => {
                const failResults = results.filter((result) => !result.success);

                if (failResults.length > 0) {
                    failResults.forEach((result) => {
                        toasters.danger(
                            t(labels.error.description, {
                                errorMessage: result.message,
                            }),
                            t(labels.error.title, {
                                integratedAccountName,
                            })
                        );
                    });
                } else {
                    toasters.success(
                        t(labels.success.description, {
                            integratedAccountName,
                        }),
                        t(labels.success.title)
                    );

                    setIsSyncing(true);
                }
            },
            onError: (error: { message: string }) => {
                toasters.danger(
                    t(labels.error.description, {
                        errorMessage: error.message,
                    }),
                    t(labels.error.title, {
                        integratedAccountName,
                    })
                );
            },
        }
    );

    return { syncPayments, isSyncPaymentsLoading };
};
