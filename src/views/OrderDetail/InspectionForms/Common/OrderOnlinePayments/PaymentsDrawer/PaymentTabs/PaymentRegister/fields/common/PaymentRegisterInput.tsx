import { Box, styled, Typography } from '@mui/material';
import React from 'react';

export const PaymentRegisterInput = React.memo(
    ({
        children,
        label,
        hideDot,
    }: {
        children: React.ReactNode;
        label: string;
        hideDot?: boolean;
    }) => {
        return (
            <InputContainer>
                <InputLabel>
                    {label} {!hideDot && <DotContainer> *</DotContainer>}
                </InputLabel>
                {children}
            </InputContainer>
        );
    }
);

const InputContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: '8px',
}));

const InputLabel = styled(Typography)(({ theme }) => ({
    fontWeight: 'bold',
    display: 'inline-flex',
    position: 'relative',
    color: '#6A6E72',
}));

const DotContainer = styled('span')(({ theme }) => ({
    ...theme.typography.button,
    color: theme.palette.primary.main,
    position: 'absolute',
    top: -3,
    right: -11,
}));
