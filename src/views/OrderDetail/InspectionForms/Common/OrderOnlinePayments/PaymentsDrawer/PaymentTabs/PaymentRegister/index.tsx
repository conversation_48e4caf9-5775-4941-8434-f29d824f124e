import { Box, IconButton, styled } from '@mui/material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Colors } from 'common/styles/Colors';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from 'store/slices/user';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { PaymentMethodState, useOrderPayment } from '../../../provider/useOrderPayment';
import { AddPaymentMethodButton } from './AddPaymentMethodButton';
import { PaymentCommentsField } from './fields/PaymentCommentsField';
import { PaymentDateField } from './fields/PaymentDateField';
import { PaymentDetail } from './PaymentDetail';
import { PaymentDropdownMethod } from './PaymentDropdown';
import { PaymentSubmitButton } from './PaymentSubmitButton';

export const PaymentRegister = React.memo(() => {
    const { payment, handleClose } = useOrderPayment();
    const { register: labels } = useOrderPaymentTranslations();
    const user = useSelector(selectUser);

    const paymentMethods = useMemo(
        () =>
            payment.methods.map((method: PaymentMethodState, index) => (
                <PaymentDropdownMethod key={index} method={method} id={index + 1} />
            )),
        [payment.methods]
    );

    return (
        <>
            <HeaderContainer>
                <Title>
                    {labels.description} <Member>{user!.displayName}</Member>
                </Title>
                <IconButton sx={{ padding: 0 }} size="small" onClick={handleClose}>
                    <CloseIcon fill={Colors.Grey5} />
                </IconButton>
            </HeaderContainer>
            <Divider style={{ marginBottom: '24px' }} />
            {paymentMethods}
            <AddPaymentMethodButton />
            <Box sx={{ marginTop: '24px' }}>
                <PaymentDateField />
            </Box>
            <Box sx={{ marginTop: '16px' }}>
                <PaymentCommentsField />
            </Box>
            <Divider sx={{ margin: '24px 0' }} />
            <PaymentDetail />
            <PaymentSubmitButton />
        </>
    );
});

const HeaderContainer = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: '10px',
    padding: '16px 0',
    width: '100%',
    position: 'sticky',
}));

const Title = styled('span')(({ theme }) => ({
    ...theme.typography.body2,
    color: '#6A6E72',
    paddingTop: '2px',
    fontSize: '12px',
}));

const Member = styled('span')(() => ({
    fontWeight: 700,
}));

const Divider = styled(Box)(() => ({
    height: '1px',
    width: '368px',
    borderTop: '1px solid',
    borderColor: '#C9CDD3',
    marginBottom: 15,
    opacity: 0.5,
}));
