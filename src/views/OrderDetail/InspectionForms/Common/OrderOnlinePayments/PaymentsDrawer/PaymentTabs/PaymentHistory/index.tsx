import { Box, IconButton, styled } from '@mui/material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Colors } from 'common/styles/Colors';
import React from 'react';
import { useOrderPayment } from '../../../provider/useOrderPayment';
import { PaymentHistoryData } from './PaymentHistoryData';
import { PaymentHistoryWithoutResults } from './PaymentHistoryWithoutResults';

export const PaymentHistory = React.memo(() => {
    const { historyData, handleClose } = useOrderPayment();

    return (
        <>
            <HeaderContainer sx={{ position: 'relative' }}>
                <IconButton
                    sx={{ position: 'absolute', zIndex: 99, padding: 0, paddingTop: '16px' }}
                    size="small"
                    onClick={handleClose}
                >
                    <CloseIcon fill={Colors.Grey5} />
                </IconButton>
            </HeaderContainer>
            {historyData.length === 0 ? <PaymentHistoryWithoutResults /> : <PaymentHistoryData />}
        </>
    );
});

const HeaderContainer = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    gap: '10px',
    width: '100%',
    position: 'sticky',
}));
