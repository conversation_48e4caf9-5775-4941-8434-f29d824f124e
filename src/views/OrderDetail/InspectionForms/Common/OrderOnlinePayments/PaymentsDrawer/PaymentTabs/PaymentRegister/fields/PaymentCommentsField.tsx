import { TextArea } from 'common/components/Inputs';
import { useCallback } from 'react';
import { useOrderPaymentTranslations } from '../../../../hooks/useOrderPaymentTranslations';
import { useOrderPayment } from '../../../../provider/useOrderPayment';
import { PaymentRegisterInput } from './common/PaymentRegisterInput';

export const PaymentCommentsField = () => {
    const { register: labels } = useOrderPaymentTranslations();
    const { payment, setPayment } = useOrderPayment();

    const handleChange = useCallback(
        (newValue: string) => {
            setPayment((prev) => ({ ...prev, comments: newValue }));
        },
        [setPayment]
    );

    return (
        <PaymentRegisterInput label={labels.fields.comments.label} hideDot>
            <TextArea
                name="payment-comments"
                placeholder={labels.fields.comments.placeholder}
                value={payment.comments || ''}
                rows={5}
                hideLabel
                onChange={(e) => handleChange(e.target.value)}
            />
        </PaymentRegisterInput>
    );
};
