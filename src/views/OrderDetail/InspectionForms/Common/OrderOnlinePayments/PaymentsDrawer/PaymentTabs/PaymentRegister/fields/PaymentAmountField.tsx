import NumberInput from 'common/components/Inputs/NumberField/NumberInput';
import { useCallback } from 'react';
import { PaymentMethodState, useOrderPayment } from '../../../../provider/useOrderPayment';

export const PaymentAmountField = ({
    method,
    handleChangeMethod,
}: {
    method: PaymentMethodState;
    handleChangeMethod: (_newValues: PaymentMethodState) => void;
}) => {
    const { currencyTemplate } = useOrderPayment();
    const decimalScale = 2;

    const handleValueChange = useCallback(
        (value: string) => {
            const numericValue = parseFloat(value.replace(/[^0-9.-]+/g, ''));
            handleChangeMethod({
                ...method,
                amountPaid: isNaN(numericValue) || numericValue === 0 ? 0 : numericValue,
            });
        },
        [handleChangeMethod, method]
    );

    return (
        <NumberInput
            onValueChange={({ value }) => handleValueChange(value)}
            placeholder={currencyTemplate.replace('{0}', '0.00')}
            fullWidth
            value={method.amountPaid !== 0 ? method.amountPaid.toFixed(decimalScale) : ''}
            template={currencyTemplate}
            fixedDecimalScale={decimalScale > 0}
            decimalScale={decimalScale > 0 ? decimalScale : 0}
            thousandSeparator
        />
    );
};
