import { Box, IconButton, styled } from '@mui/material';
import BillsIcon from 'common/components/Icons/BillsIcon';
import RemoveIcon from 'common/components/Icons/RemoveIcon';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useCallback, useState } from 'react';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { useOrderPayment } from '../../../provider/useOrderPayment';

export const PaymentDeleteModal = ({ id }: { id: number }) => {
    const { setPayment } = useOrderPayment();
    const { register: labels } = useOrderPaymentTranslations();
    const [open, setOpen] = useState(false);

    const handleDeleteMethod = useCallback(() => {
        setPayment((prev) => ({
            ...prev,
            methods: prev.methods.filter((_, index) => index + 1 !== id),
        }));
        setOpen(false);
    }, [id, setPayment]);

    const handleOpen = useCallback(() => setOpen(true), []);
    const handleClose = useCallback(() => setOpen(false), []);

    if (id === 1) return <BillsIcon />;

    return (
        <>
            <IconButton sx={{ padding: 0, zIndex: 99 }} onClick={handleOpen}>
                <RemoveIcon style={{ margin: 3, marginRight: 5 }} />
            </IconButton>
            <DeleteConfirmationPopup
                open={open}
                title={labels.deleteModal.title}
                cancel={labels.deleteModal.button.back}
                confirm={labels.deleteModal.button.delete}
                body={<Divider />}
                onConfirm={handleDeleteMethod}
                onClose={handleClose}
            />
        </>
    );
};

const Divider = styled(Box)(() => ({
    height: '1px',
    width: '217px',
    borderTop: '1px solid',
    borderColor: '#E5E7EA',
}));
