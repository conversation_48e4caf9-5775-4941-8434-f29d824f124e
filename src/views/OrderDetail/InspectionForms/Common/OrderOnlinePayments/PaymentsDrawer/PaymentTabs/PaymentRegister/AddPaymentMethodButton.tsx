import { Button } from 'common/components/Button';
import React, { useCallback } from 'react';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { useOrderPayment } from '../../../provider/useOrderPayment';

export const AddPaymentMethodButton = React.memo(() => {
    const { register: labels } = useOrderPaymentTranslations();
    const { setPayment, payment, paymentMethodOptions } = useOrderPayment();

    const handleAddMethod = useCallback(() => {
        setPayment((prev) => ({
            ...payment,
            methods: [
                ...prev.methods,
                {
                    paymentMethodId: payment.methods.length + 1,
                    paymentMethod: null,
                    paymentMethodLogLabel: '',
                    amountPaid: 0,
                },
            ],
        }));
    }, [setPayment, payment]);

    return (
        <Button
            disabled={paymentMethodOptions.length === payment.methods.length}
            cmosVariant="stroke"
            onClick={handleAddMethod}
        >
            {labels.anotherPaymentMethod}
        </Button>
    );
});
