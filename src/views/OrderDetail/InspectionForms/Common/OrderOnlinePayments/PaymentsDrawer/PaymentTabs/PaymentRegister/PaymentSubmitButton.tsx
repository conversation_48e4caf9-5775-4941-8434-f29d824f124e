import { Box, CircularProgress, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import PaymentsApi from 'api/payments';
import { OrderPaymentDto, OrderPaymentMethodType } from 'api/payments/_common';
import { Button } from 'common/components/Button';
import useToasters from 'common/hooks/useToasters';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from 'store';
import {
    selectFeatureFlags,
    selectRepairShopIntegrationAccountName,
} from '../../../../../../../../store/slices/globalSettingsSlice';
import { useOrderPaymentTranslations } from '../../../hooks/useOrderPaymentTranslations';
import { PaymentState, useOrderPayment } from '../../../provider/useOrderPayment';

export const PaymentSubmitButton = () => {
    const { register: labels } = useOrderPaymentTranslations();
    const { payment, orderId } = useOrderPayment();
    const { registerPayment, isRegisterPaymentLoading } = useRegisterPaymentMutation();

    const isValid = useIsValid();
    const isLoading = useMemo(() => isRegisterPaymentLoading, [isRegisterPaymentLoading]);

    const handleClick = useCallback(() => {
        registerPayment({ orderId, payment });
    }, [registerPayment, orderId, payment]);

    return (
        <Footer>
            <Button disabled={isLoading || !isValid} onClick={handleClick} sx={{ width: 368 }}>
                {isLoading ? (
                    <Box>
                        <CircularProgress size={20} style={{ color: '#fff' }} />
                    </Box>
                ) : (
                    <Box>{labels.button}</Box>
                )}
            </Button>
        </Footer>
    );
};

const useIsValid = () => {
    const { payment, pendingToPaid } = useOrderPayment();

    return useMemo(
        () =>
            !!payment.paymentDateTime &&
            payment.methods.every((method) => !!method.amountPaid && !!method.paymentMethod) &&
            pendingToPaid >= 0,
        [payment, pendingToPaid]
    );
};

const useRegisterPaymentMutation = (onSuccess?: () => void, onError?: () => void) => {
    const { t } = useTranslation();
    const toasters = useToasters();
    const { register: labels, thirdParty: thirdPartyLabels } = useOrderPaymentTranslations();
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);

    const { handleClose } = useOrderPayment();

    const { paymentFormThirdPartyFeature } = useAppSelector(selectFeatureFlags);

    const { mutate: registerPayment, isLoading: isRegisterPaymentLoading } = useMutation(
        ({ orderId, payment }: { orderId: number; payment: PaymentState }) =>
            PaymentsApi.addPayment(orderId, paymentBodyAdapter(payment)),
        {
            onSuccess: (response) => {
                handleClose();
                toasters.success(
                    labels.notification.success.description,
                    labels.notification.success.title
                );

                if (paymentFormThirdPartyFeature) {
                    response.syncResults.forEach((result) => {
                        if (!result.success) {
                            toasters.danger(
                                t(thirdPartyLabels.error.description, {
                                    errorMessage: result.message,
                                }),
                                t(thirdPartyLabels.error.title, {
                                    integratedAccountName,
                                })
                            );
                        }
                    });
                }

                onSuccess && onSuccess();
            },
            onError: () => {
                toasters.danger(
                    labels.notification.error.description,
                    labels.notification.error.title
                );
                onError && onError();
            },
        }
    );

    return { registerPayment, isRegisterPaymentLoading };
};

const paymentBodyAdapter = (payment: PaymentState): OrderPaymentDto => ({
    ...payment,
    methods: payment.methods.map((method) => ({
        ...method,
        paymentMethod: method.paymentMethod as OrderPaymentMethodType,
        amount: method.amountPaid,
    })),
});

const Footer = styled(Box)(() => ({
    height: 'auto',
    width: 368,
    position: 'sticky',
    bottom: 0,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    marginTop: 'auto',
}));
