import DateField from 'common/components/Inputs/DateField';
import { DateTime } from 'luxon';
import { useCallback, useMemo } from 'react';
import { isDateValid } from 'utils';
import { useOrderPaymentTranslations } from '../../../../hooks/useOrderPaymentTranslations';
import { useOrderPayment } from '../../../../provider/useOrderPayment';
import { PaymentRegisterInput } from './common/PaymentRegisterInput';

export const PaymentDateField = () => {
    const { register: labels } = useOrderPaymentTranslations();
    const { payment, setPayment } = useOrderPayment();

    const jsDate = useMemo(() => {
        try {
            return DateTime.fromFormat(payment.paymentDateTime, 'yyyy-MM-dd')
                .set({
                    millisecond: 0,
                    second: 0,
                    minute: 0,
                    hour: 0,
                })
                .toJSDate();
        } catch (_err: unknown) {
            return null;
        }
    }, [payment.paymentDateTime]);

    const onDateChange = useCallback(
        (d: Date | null) => {
            if (!d) d = new Date();
            if (!isDateValid(d)) return false;

            setPayment((prev) => ({
                ...prev,
                paymentDateTime: DateTime.fromJSDate(d!).toFormat('yyyy-MM-dd'),
            }));
        },
        [setPayment]
    );

    return (
        <PaymentRegisterInput label={labels.fields.paymentDate}>
            <DateField
                variant="grey"
                fullWidth
                value={jsDate}
                disableFuture
                onChange={onDateChange}
            />
        </PaymentRegisterInput>
    );
};
