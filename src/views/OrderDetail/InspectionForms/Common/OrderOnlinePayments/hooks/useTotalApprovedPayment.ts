import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectOrder } from 'store/slices/order/orderDetails';
import { selectEstimatesSet, selectItemsSet } from 'store/slices/order/orderInspectionFormsSlice';
import { calculateSubtotal } from 'views/OrderDetail/InspectionForms/helper';

export const useTotalApprovedPayment = () => {
    const items = useSelector(selectItemsSet);
    const objEstimates = useSelector(selectEstimatesSet);
    const repairOrder = useSelector(selectOrder);
    const { repairShopSettings } = useSelector(selectSettings);

    const requireDecimals = useMemo(
        () => !repairShopSettings?.features.enableRemoveDecimals,
        [repairShopSettings]
    );

    const roundAwayFromZero = useCallback(
        (value: number): number => {
            return repairShopSettings?.features.enableRemoveDecimals ? Math.round(value) : value;
        },
        [repairShopSettings]
    );

    return useMemo(() => {
        const filteredItems = Object.values(items).filter(
            (item) => item.priority !== PriorityLevel.Low && item.priority !== PriorityLevel.NA
        );

        if (!filteredItems.length) return 0;

        const estimateEntries = filteredItems.map((item) => {
            const estimatesSubtotals = item.estimates.map((id) =>
                calculateSubtotal(objEstimates[id], requireDecimals, repairOrder?.discountType)
            );

            return {
                isApproved: item.isApproved,
                total: estimatesSubtotals.reduce((a, b) => a + b, 0),
            };
        });

        const approvedTotals = estimateEntries
            .filter((item) => item.isApproved)
            .map((item) => item.total)
            .reduce((a, b) => a + b, 0);

        const discount = repairOrder?.discount || 0;
        const totalApprovedWithoutTaxes = roundAwayFromZero(
            repairOrder?.discountType === 'Currency'
                ? approvedTotals - discount
                : approvedTotals * ((100 - discount) / 100)
        );

        const taxPercentage = repairShopSettings?.taxPercentage || 0;
        const totalApproved = roundAwayFromZero(
            totalApprovedWithoutTaxes + (totalApprovedWithoutTaxes * taxPercentage) / 100
        );

        return totalApproved;
    }, [
        items,
        objEstimates,
        repairOrder?.discount,
        repairOrder?.discountType,
        repairShopSettings?.taxPercentage,
        requireDecimals,
        roundAwayFromZero,
    ]);
};
