import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

export const useOrderPaymentTranslations = () => {
    const { t } = useTranslation();
    const thirdPartyPrefix = 'orderDetails.inspectionForms.payments.thirdParty';
    const registerPrefix = 'orderDetails.inspectionForms.payments.register';
    const historyPrefix = 'orderDetails.inspectionForms.payments.history';

    return useMemo(
        () => ({
            thirdParty: {
                error: {
                    title: `${thirdPartyPrefix}.error.title`,
                    description: `${thirdPartyPrefix}.error.description`,
                },
                success: {
                    title: `${thirdPartyPrefix}.success.title`,
                    description: `${thirdPartyPrefix}.success.description`,
                },
                synchronization: {
                    success: `${thirdPartyPrefix}.synchronization.success`,
                    error: `${thirdPartyPrefix}.synchronization.error`,
                },
            },
            dateFormant: t(`orderDetails.inspectionForms.payments.dateFormat`),
            timeFormat: t(`orderDetails.inspectionForms.payments.timeFormat`),
            register: {
                tab: t(`${registerPrefix}.tab`),
                description: t(`${registerPrefix}.description`),
                button: t(`${registerPrefix}.button`),
                fields: {
                    paymentMethodDropdown: {
                        title: t(`${registerPrefix}.fields.paymentMethodDropdown.title`),
                        fields: {
                            amountPaid: t(
                                `${registerPrefix}.fields.paymentMethodDropdown.fields.amountPaid`
                            ),
                            paymentMethod: {
                                label: t(
                                    `${registerPrefix}.fields.paymentMethodDropdown.fields.paymentMethod.label`
                                ),
                                placeholder: t(
                                    `${registerPrefix}.fields.paymentMethodDropdown.fields.paymentMethod.placeholder`
                                ),
                                dropdown: {
                                    cash: t(
                                        `${registerPrefix}.fields.paymentMethodDropdown.fields.paymentMethod.dropdown.cash`
                                    ),
                                    card: t(
                                        `${registerPrefix}.fields.paymentMethodDropdown.fields.paymentMethod.dropdown.card`
                                    ),
                                    bank: t(
                                        `${registerPrefix}.fields.paymentMethodDropdown.fields.paymentMethod.dropdown.bank`
                                    ),
                                },
                            },
                        },
                    },
                    paymentDate: t(`${registerPrefix}.fields.paymentDate`),
                    comments: {
                        label: t(`${registerPrefix}.fields.comments.label`),
                        placeholder: t(`${registerPrefix}.fields.comments.placeholder`),
                    },
                },
                anotherPaymentMethod: t(`${registerPrefix}.anotherPaymentMethod`),
                deleteModal: {
                    title: t(`${registerPrefix}.deleteModal.title`),
                    button: {
                        back: t(`${registerPrefix}.deleteModal.button.back`),
                        delete: t(`${registerPrefix}.deleteModal.button.delete`),
                    },
                },
                details: {
                    title: {
                        left: t(`${registerPrefix}.details.title.left`),
                        right: t(`${registerPrefix}.details.title.right`),
                    },
                    approved: t(`${registerPrefix}.details.approved`),
                    method: {
                        left: t(`${registerPrefix}.details.method.left`),
                        right: t(`${registerPrefix}.details.method.right`),
                    },
                    pending: t(`${registerPrefix}.details.pending`),
                    totalAmount: t(`${registerPrefix}.details.totalAmount`),
                },
                notification: {
                    success: {
                        title: t(`${registerPrefix}.notification.success.title`),
                        description: t(`${registerPrefix}.notification.success.description`),
                    },
                    error: {
                        title: t(`${registerPrefix}.notification.error.title`),
                        description: t(`${registerPrefix}.notification.error.description`),
                    },
                },
            },
            history: {
                tab: t(`${historyPrefix}.history.tab`),
                withoutResults: {
                    title: t(`${historyPrefix}.withoutResults.title`),
                    description: t(`${historyPrefix}.withoutResults.description`),
                },
                record: {
                    title: t(`${historyPrefix}.record.title`),
                    teamMember: t(`${historyPrefix}.record.teamMember`),
                    paymentDate: t(`${historyPrefix}.record.paymentDate`),
                    paymentMethod: t(`${historyPrefix}.record.paymentMethod`),
                    amountPaid: t(`${historyPrefix}.record.amountPaid`),
                    totalAmountPaid: t(`${historyPrefix}.record.totalAmountPaid`),
                    paymentPending: t(`${historyPrefix}.record.paymentPending`),
                    comments: t(`${historyPrefix}.record.comments`),
                },
            },
        }),
        [t, registerPrefix, historyPrefix]
    );
};
