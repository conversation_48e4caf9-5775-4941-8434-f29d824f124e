import { styled, Typography, useTheme } from '@mui/material';
import { Button } from 'common/components/Button';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

type PaymentsButtonProps = {
    handleOpen: () => void;
    disabled: boolean;
};

export const PaymentsButton = React.memo(({ handleOpen, disabled }: PaymentsButtonProps) => {
    const memoizedHandleOpen = useCallback(() => {
        handleOpen();
    }, [handleOpen]);

    return (
        <SButton onClick={memoizedHandleOpen} disabled={disabled} cmosVariant="stroke">
            <Label />
        </SButton>
    );
});

const Label = React.memo(() => {
    const theme = useTheme();
    const { t } = useTranslation();
    return (
        <Typography
            sx={{
                ...theme.typography.h6Inter,
                fontWeight: 'bold',
                color: theme.palette.primary.main,
            }}
        >
            {t('orderDetails.inspectionForms.payments.button')}
        </Typography>
    );
});

const SButton = styled(Button)(() => ({
    width: 150,
    height: 32,
    marginLeft: 10,
    borderRadius: 51,
    display: 'flex',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    border: '1px solid #0069ff',
    backgroundColor: 'transparent',
    '&:hover': {
        backgroundColor: '#d0dffe',
    },
    '&:focus': {
        outline: 'none',
    },
}));
