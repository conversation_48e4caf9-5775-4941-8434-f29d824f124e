import { styled } from '@mui/material';
import { PlayIcon } from 'common/components/Icons/PlayIcon';

export default function BigPlay() {
    return (
        <DivRoot className="big-play">
            <DivEllipse />
            <DivCenterPlayIcon>
                <PlayIcon size={80} fill={'#fff'} />
            </DivCenterPlayIcon>
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    position: 'absolute',
    cursor: 'pointer',
    borderRadius: '50%',
    pointerEvents: 'none',
    transition: '0.2s opacity',
});

const DivEllipse = styled('div')(({ theme }) => ({
    background: theme.palette.neutral[9],
    opacity: 0.3,
    borderRadius: '50%',
    width: 88,
    height: 88,
}));

const DivCenterPlayIcon = styled('div')({
    position: 'absolute',
    left: '15%',
    top: 0,
    width: '80%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '50%',
});
