import { Box, styled } from '@mui/material';
import CrossedOutVideocam from 'assets/icons/crossedOutVideocam.svg';
import { formatTime } from 'common/Helpers/format';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { PlayIcon } from 'common/components/Icons/PlayIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { usePlayer } from 'common/hooks/usePlayer';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { MediaDto } from 'datacontracts/InspectionForms/Media/MediaDto';
import { useRef, useState } from 'react';
import { FullScreen, useFullScreenHandle } from 'react-full-screen';
import isFullScreenAvailable from 'utils/isFullScreenAvailable';
import BigPlay from '../BigPlay';
import EvidenceSlider from '../EvidenceSlider';
import FullScreenButton from '../FullScreenButton';
import Loading from './Loading';

type VideoEvidenceProps = {
    video: MediaDto;
};

const fullscreenAvailable = isFullScreenAvailable('div');

const VideoEvidence = ({ video }: VideoEvidenceProps) => {
    const { t } = useAppTranslation();
    const fullScreen = useFullScreenHandle();

    const [playing, setPlaying] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);
    const { currentTime, setCurrentTime, duration, loading } = usePlayer({
        element: videoRef.current,
        playing,
        onPause: () => setPlaying(false),
        onPlayError: () => setPlaying(false),
    });

    const timeStr = formatTime(currentTime) + ' / ' + formatTime(duration);

    const handleSliderChange = (_: unknown, newValue: number | number[]) => {
        setCurrentTime(newValue as number);
    };

    const handleSliderMouseDown = () => {
        setPlaying(false);
        document.addEventListener(
            'mouseup',
            () => {
                setPlaying(playing);
            },
            { once: true }
        );
    };

    const handlePlayPause = () => {
        if (video.isProcessed) {
            setPlaying((current) => !current);
        }
    };

    return (
        <DivRoot>
            {/* @ts-ignore */}
            <FullScreen handle={fullScreen}>
                {video.isProcessed && (
                    <DivVideoArea>
                        <StyledVideo
                            src={video.url}
                            preload="auto"
                            poster={video.thumbnailUrl}
                            onContextMenu={(e) => e.preventDefault()}
                            ref={videoRef}
                            onClick={handlePlayPause}
                        />
                        {loading && <StyledLoading />}
                        {!playing && <BigPlay />}
                    </DivVideoArea>
                )}
                {!video.isProcessed && (
                    <DivProcessing>
                        <Box
                            component="img"
                            sx={{
                                paddingTop: '22px',
                                marginBottom: '40px',
                            }}
                            src={CrossedOutVideocam}
                            alt="processing"
                        />
                        <SpanVideoInProcessing>
                            {t('inspectionForms.evidenceModal.videoInProcessing')}
                        </SpanVideoInProcessing>
                    </DivProcessing>
                )}

                <DivBottom>
                    <EvidenceSlider
                        value={currentTime}
                        max={duration}
                        onChange={handleSliderChange}
                        onMouseDown={handleSliderMouseDown}
                        disabled={duration === 0}
                        step={0.1}
                        sx={{
                            position: 'absolute',
                            top: 0,
                            marginTop: '-14px',
                            zIndex: 2,
                        }}
                    />

                    <DivBottomButtons>
                        <DivPlayPauseButton onClick={handlePlayPause}>
                            {playing && <PauseIcon size={IconSize.L} fill={Colors.White} />}
                            {!playing && <PlayIcon size={IconSize.L} fill={Colors.White} />}
                        </DivPlayPauseButton>
                        <DivTime>{timeStr}</DivTime>
                        <Box sx={{ flexGrow: 1 }} />
                        {fullscreenAvailable && (
                            <FullScreenButton
                                fullScreenActive={fullScreen.active}
                                onEnter={fullScreen.enter}
                                onExit={fullScreen.exit}
                            />
                        )}
                    </DivBottomButtons>
                </DivBottom>
            </FullScreen>
        </DivRoot>
    );
};

export default VideoEvidence;

const DivRoot = styled('div')({
    width: '100%',
    height: '100%',
    position: 'relative',

    '& .fullscreen': {
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const DivVideoArea = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    '& .big-play': {
        opacity: 0,
    },
    '&:hover': {
        '& .big-play': {
            opacity: 1,
        },
    },
});

const StyledVideo = styled('video')({
    width: '100%',
    height: '100%',
});

const StyledLoading = styled(Loading)({
    position: 'absolute',
    pointerEvents: 'none',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
});

const DivProcessing = styled('div')({
    width: '100%',
    height: '100%',
    backgroundColor: '#bebfbf',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
});

const SpanVideoInProcessing = styled('span')(({ theme }) => ({
    ...theme.typography.h4Roboto,
    color: theme.palette.neutral[1],
}));

const DivPlayPauseButton = styled('button')({
    width: 40,
    height: 40,
    display: 'flex',
    background: 'none',
    border: 'none',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',

    color: '#fff',
    opacity: 1,

    ':hover': {
        opacity: 0.75,
    },

    ':active': {
        transform: 'scale(.9)',
    },
});

const DivBottomButtons = styled('div')({
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 8,
    paddingBottom: 8,
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    boxSizing: 'border-box',
});

const DivTime = styled('div')(({ theme }) => ({
    marginLeft: 16,
    ...theme.typography.h5Inter,
    lineHeight: 'normal',
    color: theme.palette.neutral[1],
}));

const DivBottom = styled('div')({
    position: 'absolute',
    left: 0,
    bottom: 0,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: 'rgba(0,0,0,.25)',
});
