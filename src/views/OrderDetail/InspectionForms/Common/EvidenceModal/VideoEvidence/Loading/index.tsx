import { styled } from '@mui/material';
import { CSSProperties } from 'react';

interface LoadingProps {
    className?: string;
}

const Root = styled('div')({
    paddingTop: 46,
});

const Spinner = styled('div')(({ theme }) => ({
    color: theme.palette.common.white,
    display: 'inline-block',
    position: 'relative',
    '--size': '100px',
    width: 'var(--size)',
    height: 'var(--size)',

    '@keyframes spinner': {
        '0%': {
            opacity: 1,
        },
        '100%': {
            opacity: 0,
        },
    },

    '& div': {
        transformOrigin: `calc(var(--size) / 2)px calc(var(--size) / 2)`,
        animation: `spinner 1.2s linear infinite`,
        '&::after': {
            content: '" "',
            display: 'block',
            position: 'absolute',
            // NOTE (MB) Code from https://loading.io/css/ was using 80 as the size so we need to adjust all values
            // no the size we get from the props
            top: `calc(${13 / 80} * var(--size))`,
            left: `calc(${37 / 80} * var(--size))`,
            width: `calc(${6 / 80} * var(--size))`,
            height: `calc(${18 / 80} * var(--size))`,
            borderRadius: '20%',
            background: '#fff',
        },
        ...generateSegmentsCss(12),
    },
}));

const ITEMS = Object.keys(new Array(12).fill(undefined)).map((v) => <div key={v} />);

export const Loading = ({ className }: LoadingProps) => {
    return (
        <Root className={className}>
            <Spinner>{ITEMS}</Spinner>
        </Root>
    );
};

export default Loading;

function generateSegmentsCss(count: number): Record<string, CSSProperties> {
    const r: Record<string, CSSProperties> = {};

    for (let i = 0; i < count; i++) {
        r[`&:nth-child(${i + 1})`] = {
            transform: `rotate(${(360 / count) * i}deg)`,
            animationDelay: `${(count - i - 1) * -0.1}s`,
        };
    }

    return r;
}
