import { SliderTypeMap, styled } from '@mui/material';
import { DefaultComponentProps } from '@mui/material/OverridableComponent';
import StyledSlider from './StyledSlider';

export default function EvidenceSlider({
    className,
    sx,
    ...props
}: DefaultComponentProps<SliderTypeMap>) {
    // TODO (MB) CMOS-1993: props.onChange now accepts 3 arguments, I don't know what 3rd one is, I passed 0, but who knows if it'll break something or not

    return (
        <DivRoot sx={sx} className={className}>
            <DivPlaceholder />
            <StyledSlider {...props} />
        </DivRoot>
    );
}

const DivPlaceholder = styled('div')({
    width: 10,
    backgroundColor: 'var(--cm1)',
    height: 6,
});

const DivRoot = styled('div')({
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    boxSizing: 'border-box',
});
