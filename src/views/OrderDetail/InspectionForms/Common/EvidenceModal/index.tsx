import { IconButton, styled } from '@mui/material';
import MediaAPI from 'api/Media';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { LeftIcon } from 'common/components/Icons/LeftIcon';
import { RightIcon } from 'common/components/Icons/RightIcon';
import { Modal } from 'common/components/Modal';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { FileType } from 'datacontracts/InspectionForms/Media/FileType';
import { MediaDto } from 'datacontracts/InspectionForms/Media/MediaDto';
import { useState } from 'react';
import { useAsyncMemo } from 'use-async-memo';
import AudioEvidence from './AudioEvidence';
import ImageEvidence from './ImageEvidence';
import VideoEvidence from './VideoEvidence';
import { useStyles } from './css';

interface EvidenceModalProps {
    onClose: () => void;
    repairId: number | null;
    masterItemId: number;
    repairOrderId: number;
}

const EvidenceModal = ({ onClose, repairId, masterItemId, repairOrderId }: EvidenceModalProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const [activeMediaIndex, setActiveMediaIndex] = useState(0);
    const [isOpen, setIsOpen] = useState(false);

    const compareMediaItems = (a: MediaDto, b: MediaDto) => {
        function getPriority(item: MediaDto) {
            switch (item.fileType) {
                case FileType.Photo:
                    return 1;
                case FileType.Video:
                    return 2;
                case FileType.Audio:
                    return 3;
            }
        }
        const diff = getPriority(a) - getPriority(b);
        return diff === 0 ? a.sequenceNumber - b.sequenceNumber : diff;
    };

    const medias = useAsyncMemo<MediaDto[]>(
        async () => {
            const result = (
                await callApi(() => MediaAPI.list(repairId, masterItemId, repairOrderId), {
                    selectErrorContent: () => ({
                        body: t('toasters.errorOccurredWhenLoading'),
                    }),
                })
            ).sort(compareMediaItems);
            setIsOpen(true);
            return result;
        },
        [],
        []
    );

    const activeMedia =
        activeMediaIndex >= 0 && activeMediaIndex < medias.length ? medias[activeMediaIndex] : null;

    return (
        <Modal open={isOpen} classes={{ box: styles.box }} onClose={onClose}>
            <DivContent>
                {activeMedia && (
                    // use key to force update of the component even if media has not changed (but index did)
                    // useful during dev, but otherwise unnecessary
                    <DivEvidence key={activeMediaIndex}>
                        {activeMedia.fileType === FileType.Photo && (
                            <ImageEvidence image={activeMedia} showOverlay />
                        )}
                        {activeMedia.fileType === FileType.Video && (
                            <VideoEvidence video={activeMedia} />
                        )}
                        {activeMedia.fileType === FileType.Audio && (
                            <AudioEvidence audio={activeMedia} />
                        )}
                    </DivEvidence>
                )}
                <DivHeader>
                    {activeMedia?.uploadedFromGallery &&
                        activeMedia?.fileType === FileType.Video && (
                            <SpanUploadedFromGallery>
                                {t('inspectionForms.evidenceModal.uploadedFromGallery')}
                            </SpanUploadedFromGallery>
                        )}
                    <ButtonClose onClick={onClose}>
                        <CloseIcon size={IconSize.L} fill={Colors.White} />
                    </ButtonClose>
                </DivHeader>
                <DivBottomLine>
                    {activeMedia?.uploadedFromGallery &&
                        activeMedia?.fileType === FileType.Photo && (
                            <SpanUploadedFromGallery>
                                {t('inspectionForms.evidenceModal.uploadedFromGallery')}
                            </SpanUploadedFromGallery>
                        )}
                </DivBottomLine>
                <DivBottomControls>
                    {medias.length > 1 && (
                        <IconButton
                            onClick={() =>
                                setActiveMediaIndex((value) => (value > 0 ? value - 1 : 0))
                            }
                        >
                            <LeftIcon />
                        </IconButton>
                    )}
                    {medias.length > 1 && (
                        <IconButton
                            onClick={() =>
                                setActiveMediaIndex((value) =>
                                    value < medias.length - 1 ? value + 1 : medias.length - 1
                                )
                            }
                        >
                            <RightIcon />
                        </IconButton>
                    )}
                    <DivCount>
                        {t('inspectionForms.evidenceModal.count', {
                            n: activeMediaIndex + 1,
                            count: medias.length,
                        })}
                    </DivCount>
                    <DivCircles>
                        {[...Array(medias.length)].map((_, index) => (
                            <DivCircle
                                key={index}
                                role="button"
                                className={index === activeMediaIndex ? 'checked' : undefined}
                                onClick={() => setActiveMediaIndex(index)}
                            />
                        ))}
                    </DivCircles>
                </DivBottomControls>
            </DivContent>
        </Modal>
    );
};

export default EvidenceModal;

const DivEvidence = styled('div')({
    flexGrow: 1,
    height: 0,
    backgroundColor: 'black',
});

const DivContent = styled('div')({
    width: 834,
    height: 509,
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
});

const DivHeader = styled('div')({
    display: 'flex',
    justifyContent: 'start',
    position: 'absolute',
    top: 0,
    width: '100%',
    height: 72,
    alignItems: 'center',
    paddingLeft: 32,
    paddingRight: 16,
    boxSizing: 'border-box',
});

const DivBottomLine = styled('div')({
    display: 'flex',
    justifyContent: 'start',
    position: 'absolute',
    bottom: 61,
    width: '90%',
    height: 72,
    alignItems: 'center',
    paddingLeft: 32,
    paddingRight: 16,
    boxSizing: 'border-box',
    pointerEvents: 'none',
});

const SpanUploadedFromGallery = styled('span')(({ theme }) => ({
    ...theme.typography.h3Roboto,
    color: theme.palette.neutral[1],
    display: 'inline-block',
}));

const ButtonClose = styled('button')({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    border: 'none',
    background: 'none',
    marginLeft: 'auto',
});

const DivBottomControls = styled('div')({
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 14,
    paddingBottom: 15,
    display: 'flex',
    alignItems: 'center',
});

const DivCount = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.primary.main,
    marginLeft: 16,
    minWidth: 160,
}));

const DivCircles = styled('div')({
    display: 'flex',
    gap: 10,
    marginLeft: 25,
    flexWrap: 'wrap',
});

const DivCircle = styled('div')(({ theme }) => ({
    borderRadius: '50%',
    width: 12,
    height: 12,
    backgroundColor: theme.palette.neutral[4],
    cursor: 'pointer',
    '&.checked': {
        backgroundColor: theme.palette.primary.main,
        cursor: 'auto',
    },
}));
