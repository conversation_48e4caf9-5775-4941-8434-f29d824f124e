import { styled } from '@mui/material';
import { formatTime } from 'common/Helpers/format';
import { MicIcon } from 'common/components/Icons/MicIcon';
import { PauseIcon } from 'common/components/Icons/PauseIcon';
import { PlayIcon } from 'common/components/Icons/PlayIcon';
import { usePlayer } from 'common/hooks/usePlayer';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { MediaDto } from 'datacontracts/InspectionForms/Media/MediaDto';
import { useEffect, useMemo, useState } from 'react';
import ErrorBoundary from 'utils/errorsHandling/ErrorBoundary';
import BigPlay from '../BigPlay';
import EvidenceSlider from '../EvidenceSlider';
import AudioViz from './AudioViz';

type AudioEvidenceProps = {
    audio: MediaDto;
};

export default function AudioEvidence({ audio }: AudioEvidenceProps) {
    const [playing, setPlaying] = useState(false);
    const { audioElement, audioContext, audioSource } = useMemo(() => {
        const element = new Audio(audio.url);
        element.crossOrigin = 'anonymous';
        document.body.appendChild(element);

        const audioContext = new AudioContext();
        const audioSource = audioContext.createMediaElementSource(element);

        return {
            audioElement: element,
            audioContext,
            audioSource,
        };
    }, [audio.url]);

    useEffect(
        () => () => {
            audioElement.pause();
            audioElement.remove();
        },
        [audioElement]
    );

    const { currentTime, setCurrentTime, duration } = usePlayer({
        element: audioElement,
        playing,
        onPause: () => setPlaying(false),
        onPlayError: () => setPlaying(false),
    });

    const timeStr = formatTime(currentTime) + ' / ' + formatTime(duration);

    const handleSliderChange = (_: unknown, newValue: number | number[]) => {
        setCurrentTime(newValue as number);
    };

    const handleSliderMouseDown = () => {
        setPlaying(false);
        document.addEventListener(
            'mouseup',
            () => {
                setPlaying(playing);
            },
            { once: true }
        );
    };

    const handlePlayPause = () => {
        if (duration > 0) setPlaying((current) => !current);
    };

    return (
        <DivRoot>
            <DivPlaceholder onClick={handlePlayPause}>
                <ErrorBoundary renderError={() => null}>
                    <AudioViz
                        audioElement={audioElement}
                        audioContext={audioContext}
                        audioSource={audioSource}
                    />
                </ErrorBoundary>
                <MicIcon size={120} fill="#77acfd" />
                {!playing && <BigPlay />}
            </DivPlaceholder>
            <DivTopGradient />
            <DivBottom>
                <StyledSlider
                    value={currentTime}
                    max={duration}
                    onChange={handleSliderChange}
                    onMouseDown={handleSliderMouseDown}
                    disabled={duration === 0}
                    step={0.1}
                />

                <DivBottomButtons>
                    <DivPlayPauseButton onClick={handlePlayPause}>
                        {playing && <PauseIcon size={IconSize.L} fill={Colors.White} />}
                        {!playing && <PlayIcon size={IconSize.L} fill={Colors.White} />}
                    </DivPlayPauseButton>
                    <DivTime>{timeStr}</DivTime>
                </DivBottomButtons>
            </DivBottom>
        </DivRoot>
    );
}

const StyledSlider = styled(EvidenceSlider)({
    position: 'absolute',
    top: 0,
    marginTop: -13,
    zIndex: 2,
});

const DivRoot = styled('div')({
    width: '100%',
    height: '100%',
    position: 'relative',

    '& .fullscreen': {
        width: '100%',
        height: '100%',
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const DivPlaceholder = styled('div')({
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    background: 'linear-gradient(180deg, #0069FF 0%, #7BABF9 100%)',
    position: 'relative',
});

const DivTopGradient = styled('div')({
    position: 'absolute',
    left: 0,
    top: 0,
    width: '100%',
    height: 72,
    background: 'linear-gradient(180deg, rgba(38, 41, 43, 0.8) 0%, rgba(38, 41, 43, 0) 100%)',
});

const DivPlayPauseButton = styled('button')({
    width: 40,
    height: 40,
    display: 'flex',
    background: 'none',
    border: 'none',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',

    color: '#fff',
    opacity: 1,

    ':hover': {
        opacity: 0.75,
    },

    ':active': {
        transform: 'scale(.9)',
    },
});

const DivBottomButtons = styled('div')({
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 8,
    paddingBottom: 8,
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    boxSizing: 'border-box',
});

const DivTime = styled('div')(({ theme }) => ({
    marginLeft: 16,
    ...theme.typography.h5Inter,
    lineHeight: 'normal',
    color: theme.palette.neutral[1],
}));

const DivBottom = styled('div')({
    position: 'absolute',
    left: 0,
    bottom: 0,
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: 'rgba(0,0,0,.25)',
});
