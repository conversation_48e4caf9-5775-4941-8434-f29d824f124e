import { styled } from '@mui/material';
import { useEffect, useState } from 'react';

export default function AudioViz({
    audioElement,
    audioContext,
    audioSource,
}: {
    audioElement: HTMLAudioElement;
    audioContext: AudioContext;
    audioSource: MediaElementAudioSourceNode;
}) {
    const [rootElement, setRootElement] = useState<HTMLDivElement | null>(null);

    useEffect(() => {
        if (!rootElement) return;
        if (!window.AudioContext) return;

        // step 1: prepare some things

        const analyser = audioContext.createAnalyser();
        analyser.fftSize = 64;
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        audioSource.connect(analyser);
        analyser.connect(audioContext.destination);

        // clear root element
        rootElement.innerHTML = '';

        // create a bunch of bars
        const elements = Array.from({ length: bufferLength }).map((_, i) => {
            const el = document.createElement('div');
            rootElement.appendChild(el);
            return el;
        });

        rootElement.style.setProperty('--buffer-length', bufferLength + '');

        // step 2: subscribe to audio file and let the magic happen

        let stopFlag = true;
        let lastUpdateAt = 0;
        const minTimeSinceLastUpdate = 1000 / 30; // limit to 30fps (to avoid excessive updates that will distract user)

        const callback = () => {
            if (stopFlag) return;
            if (Date.now() - lastUpdateAt < minTimeSinceLastUpdate) {
                window.requestAnimationFrame(callback);
                return;
            } else {
                lastUpdateAt = Date.now();
            }

            analyser.getByteTimeDomainData(dataArray);

            dataArray.forEach((d, i) => {
                const volume = (Math.abs(128 - d) * 2.8125) | 0;
                elements[i].style.setProperty('--c', volume + '');
            });
            const rmsValue = rms(dataArray);
            rootElement.style.setProperty('--rms', rmsValue + '');

            window.requestAnimationFrame(callback);
        };

        const onPlay = () => {
            audioContext.resume();
            stopFlag = false;
            rootElement.style.setProperty('--transition', '33.33ms');
            window.requestAnimationFrame(callback);
        };
        const onPause = () => {
            audioContext.suspend();
            stopFlag = true;
            rootElement.style.setProperty('--transition', '0.1s');
            elements.forEach((el) => {
                el.style.setProperty('--c', '0');
            });
        };

        audioElement.addEventListener('play', onPlay);
        audioElement.addEventListener('pause', onPause);

        if (!audioElement.paused) {
            onPlay();
        }

        return () => {
            audioElement.removeEventListener('play', onPlay);
            audioElement.removeEventListener('pause', onPause);
            analyser.disconnect();
            audioSource.disconnect();
            rootElement.innerHTML = '';
        };
    }, [audioElement, rootElement, audioContext, audioSource]);

    return <DivAudioViz ref={setRootElement} />;
}

const DivAudioViz = styled('div')({
    height: '70%',
    position: 'absolute',
    display: 'flex',
    gap: 4,
    alignItems: 'end',
    justifyContent: 'linear',

    div: {
        borderRadius: 4,
        width: '10px',
        backgroundColor: 'rgba(0,0,0,0.25)',
        display: 'inline-block',
        height: 'calc(var(--c) / var(--rms, 360) * 80%)',
        transition: 'ease-in-out height var(--transition)',
    },
});

/**
 * root mean square
 */
function rms(arr: Uint8Array): number {
    const n = arr.length;
    if (n === 0) return 0;

    let rms = 0;

    for (let i = 0; i < n; i++) {
        rms += Math.pow(arr[i], 2);
    }

    rms = Math.sqrt((1 / n) * rms);

    return rms;
}
