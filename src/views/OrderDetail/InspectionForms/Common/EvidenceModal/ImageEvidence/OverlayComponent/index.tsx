import { useEffect, useRef } from 'react';
import { useWindowDimension } from '../../../../../../../common/hooks/useWindowDimension';
import { Overlay } from '../../../../../../../datacontracts/InspectionForms/Media/Overlay';
import { useStyles } from './css';

interface OverlayComponentProps {
    overlay: Overlay;
    originalWidth: number;
    originalHeight: number;
}

const OverlayComponent = ({ overlay, originalWidth, originalHeight }: OverlayComponentProps) => {
    const styles = useStyles();
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useWindowDimension();

    useEffect(() => {
        if (canvasRef.current && containerRef.current) {
            canvasRef.current.width = containerRef.current.clientWidth;
            canvasRef.current.height = containerRef.current.clientHeight;

            draw(canvasRef.current);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        canvasRef.current,
        containerRef.current,
        containerRef.current?.clientWidth,
        containerRef.current?.clientHeight,
    ]);

    const draw = (canvas: HTMLCanvasElement) => {
        const canvasWidth = canvas.clientWidth;
        var canvasHeight = canvas.clientHeight;
        const maxDimension = canvasWidth >= canvasHeight ? canvasWidth : canvasHeight;

        const proportionX = canvasWidth / originalWidth;
        const proportionY = canvasHeight / originalHeight;

        const proportion = Math.min(proportionX, proportionY);
        const offsetX =
            proportionY < proportionX ? (canvasWidth - originalWidth * proportion) / 2 : 0;
        const offsetY =
            proportionX < proportionY ? (canvasHeight - originalHeight * proportion) / 2 : 0;

        overlay.overlayList.forEach((o, index) => {
            switch (o.type) {
                case 'Circle':
                    drawCircle(
                        canvas,
                        maxDimension,
                        o.size * proportion,
                        offsetX + o.x * proportion,
                        offsetY + o.y * proportion
                    );
                    if (overlay.overlayList.length > 1)
                        drawCircleNumber(
                            canvas,
                            (index + 1).toString(),
                            o.size * proportion,
                            offsetX + o.x * proportion,
                            offsetY + o.y * proportion
                        );
                    break;
                case 'Arrow':
                    drawArrow(
                        canvas,
                        maxDimension,
                        o.size * proportion,
                        offsetX + o.x * proportion,
                        offsetY + o.y * proportion
                    );
                    if (overlay.overlayList.length > 1)
                        drawArrowNumber(
                            canvas,
                            (index + 1).toString(),
                            o.size * proportion,
                            offsetX + o.x * proportion,
                            offsetY + o.y * proportion
                        );
                    break;
            }
        });
    };

    const drawCircle = (
        canvas: HTMLCanvasElement,
        maxDimension: number,
        size: number,
        x: number,
        y: number
    ) => {
        let lineWidth = 5;
        if (maxDimension >= 120 && maxDimension <= 360) lineWidth = 3;
        else if (maxDimension < 120) lineWidth = 2;

        const shadowOffset = lineWidth / 2;
        const radius = size / 2 - lineWidth / 2 - shadowOffset;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        ctx.beginPath();
        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = lineWidth;
        ctx.arc(x, y, radius, 0, 2 * Math.PI);

        ctx.shadowColor = '#000';
        ctx.shadowBlur = lineWidth / 2;
        ctx.shadowOffsetX = shadowOffset;
        ctx.shadowOffsetY = shadowOffset;

        ctx.stroke();
    };

    const drawArrow = (
        canvas: HTMLCanvasElement,
        maxDimension: number,
        size: number,
        x: number,
        y: number
    ) => {
        let minSize = 25;
        let startThickness = 5;

        if (maxDimension >= 150 && maxDimension <= 360) {
            minSize = 15;
            startThickness = 3;
        } else if (maxDimension < 150) {
            minSize = 10;
            startThickness = 2;
        }

        size = Math.max(size, minSize);

        const angle = 0.75 * Math.PI;
        const shadowOffset = startThickness / 4;
        size = size - startThickness - shadowOffset;
        const length = Math.abs(size / Math.cos(0.75 * Math.PI));
        const endThickness = startThickness / 2;
        const headLength = startThickness * 4;
        const arrowBodyLengthReduction = startThickness * 2;
        const arrowBodyAngle = Math.PI / 6;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        ctx.beginPath();

        ctx.fillStyle = '#ff0000';

        const tlX = x + Math.cos(angle) * length - Math.sin(angle) * startThickness;
        const tlY = y + Math.sin(angle) * length + Math.cos(angle) * startThickness;

        const trX = x + Math.cos(angle) * arrowBodyLengthReduction - Math.sin(angle) * endThickness;
        const trY = y + Math.sin(angle) * arrowBodyLengthReduction + Math.cos(angle) * endThickness;

        const blX = x + Math.cos(angle) * length + Math.sin(angle) * startThickness;
        const blY = y + Math.sin(angle) * length - Math.cos(angle) * startThickness;

        const brX = x + Math.cos(angle) * arrowBodyLengthReduction + Math.sin(angle) * endThickness;
        const brY = y + Math.sin(angle) * arrowBodyLengthReduction - Math.cos(angle) * endThickness;

        ctx.moveTo(trX, trY);
        ctx.lineTo(tlX, tlY);
        ctx.quadraticCurveTo(tlX, blY, blX, blY);
        ctx.lineTo(brX, brY);
        ctx.lineTo(trX, trY);

        ctx.moveTo(x, y);
        ctx.lineTo(
            x + Math.cos(angle - arrowBodyAngle) * headLength,
            y + Math.sin(angle - arrowBodyAngle) * headLength
        );

        ctx.moveTo(x, y);
        ctx.lineTo(
            x + Math.cos(angle + arrowBodyAngle) * headLength,
            y + Math.sin(angle + arrowBodyAngle) * headLength
        );

        ctx.quadraticCurveTo(
            x - Math.sin(angle) * 6 + Math.cos(angle - arrowBodyAngle) * headLength,
            y - Math.cos(angle) * 6 + Math.sin(angle + arrowBodyAngle) * headLength,
            x + Math.cos(angle - arrowBodyAngle) * headLength,
            y + Math.sin(angle - arrowBodyAngle) * headLength
        );

        ctx.shadowColor = '#000';
        ctx.shadowBlur = startThickness / 2;
        ctx.shadowOffsetX = shadowOffset;
        ctx.shadowOffsetY = shadowOffset;

        ctx.fill();
    };

    const drawCircleNumber = (
        canvas: HTMLCanvasElement,
        number: string,
        size: number,
        x: number,
        y: number
    ) => drawNumber(canvas, number, x - 2, y + size / 2 + 13);

    const drawArrowNumber = (
        canvas: HTMLCanvasElement,
        number: string,
        size: number,
        x: number,
        y: number
    ) => drawNumber(canvas, number, x - size, y + size + 13);

    const drawNumber = (canvas: HTMLCanvasElement, number: string, x: number, y: number) => {
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        ctx.font = 'bold 14px Arial';
        ctx.shadowColor = 'black';
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
        ctx.fillStyle = '#ff0000';
        ctx.fillText(number, x, y);
    };

    return (
        <div className={styles.container} ref={containerRef}>
            <canvas ref={canvasRef} />
        </div>
    );
};

export default OverlayComponent;
