import { IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import OpenAPIItemJobsAPI, {
    InventoryItemsDto,
    JobDto,
    LaborDto,
    PartDto,
} from 'api/OpenAPIItemJobs';
import AreaSpinner from 'common/components/AreaSpinner';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { Checkbox } from 'common/components/Inputs';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectAllCustomizableColumns } from 'store/slices/order/orderInspectionFormsSlice';
import { addEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { createEstimatesFromOpenAPIIntegration } from 'store/slices/order/orderInspectionFormsSlice/thunks/createEstimatesFromOpenAPIIntegration';
import { FieldValue } from 'store/slices/order/orderInspectionFormsSlice/types';
import { useStyles } from './css';

// eslint-disable-next-line no-restricted-syntax
interface OpenAPIMenuPricingModalProps {
    repairOrderId: number;
    repairId: number;
    masterItemId: number;
    onClose: () => void;
}

const OpenAPIMenuPricingModal = ({
    repairOrderId,
    repairId,
    masterItemId,
    onClose,
}: OpenAPIMenuPricingModalProps) => {
    const [isCancelEstimateOpen, setIsCancelEstimateOpen] = useState(false);
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [textInput, setTextInput] = useState('');
    const searchTextRef = useRef('');
    const [saving, setSaving] = useState(false);
    const dispatch = useAppDispatch();

    const allCustomFields = useAppSelector(selectAllCustomizableColumns);

    const [foundItems, setFoundItems] = useState<InventoryItemsDto>({
        jobs: [],
        parts: [],
        labors: [],
    });
    const [selected, setSelected] = useState<{
        jobs: JobDto[];
        parts: PartDto[];
        labors: LaborDto[];
    }>({
        jobs: [],
        parts: [],
        labors: [],
    });
    const selectedCount = useMemo(
        () => selected.jobs.length + selected.parts.length + selected.labors.length,
        [selected]
    );

    /*
        Set the max amount of parts/labors/jobs to 50 since we only show at most 50 items per category.
    */
    const count = useMemo(
        () =>
            Math.min(foundItems.jobs?.length || 0, 50) +
            Math.min(foundItems.parts?.length || 0, 50) +
            Math.min(foundItems.labors?.length || 0, 50),
        [foundItems]
    );

    const [view, setView] = useState<'performSearch' | 'noInfo' | 'jobs'>('performSearch');

    const refreshMutation = useMutation(
        (props: { keyWord: string; orderId: number }) => {
            return OpenAPIItemJobsAPI.getJobs(props.keyWord, props.orderId);
        },
        {
            onSuccess: (data) => {
                setFoundItems(data);
                setView('jobs');
            },
            onError: () => {
                setView('noInfo');
            },
        }
    );

    const search = async () => {
        if (textInput.length === 0) {
            setView('performSearch');
            return;
        }

        searchTextRef.current = textInput;

        refreshMutation.mutate({ keyWord: textInput, orderId: repairOrderId });
    };

    const save = async () => {
        if (saving) return;

        const { labors, jobs, parts } = selected;

        setSaving(true);
        try {
            const result = await dispatch(
                createEstimatesFromOpenAPIIntegration({
                    labors: laborsWithFieldIds(labors, allCustomFields),
                    jobs: jobsWithFieldIds(jobs, allCustomFields),
                    parts: partsWithFieldIds(parts, allCustomFields),
                    masterItemId,
                    repairOrderId,
                    repairId,
                })
            );
            if (!('error' in result)) onClose();
        } finally {
            setSaving(false);
        }
    };

    const addManualEstimate = async () => {
        if (saving) return;

        setSaving(true);
        try {
            const result = await dispatch(addEstimate({ masterItemId, repairOrderId }));
            if (!('error' in result)) onClose();
        } finally {
            setSaving(false);
        }
    };

    const handleCancelEstimateConfirm = () => {
        selectedCount > 0 ? setIsCancelEstimateOpen(true) : onClose();
    };

    const header = (
        <div className={styles.top}>
            <TextFormField
                cmosVariant="roundedGrey"
                placeholder={t('menuPricing.search')}
                name={'search'}
                value={textInput}
                endAdornment={
                    <IconButton onClick={search}>
                        <SearchIcon />
                    </IconButton>
                }
                inputWrapperClasses={{ self: styles.search }}
                onEnterPress={search}
                onChange={(e) => setTextInput(e.target.value)}
            />
            <div className={styles.buttons}>
                <Button
                    cmosVariant={'stroke'}
                    cmosSize={'medium'}
                    color={Colors.CM1}
                    Icon={PlusIcon}
                    label={t('menuPricing.addManualEstimate')}
                    onClick={addManualEstimate}
                    className={styles.addManualEstimate}
                />
                <Button
                    cmosVariant={'filled'}
                    cmosSize={'medium'}
                    color={Colors.Neutral3}
                    label={t('commonLabels.cancel')}
                    onClick={handleCancelEstimateConfirm}
                    className={styles.cancel}
                />
                <Button
                    cmosVariant={'filled'}
                    cmosSize={'medium'}
                    color={Colors.Success}
                    label={t('commonLabels.save')}
                    onClick={save}
                    showLoader={saving}
                    className={styles.save}
                />
            </div>
        </div>
    );

    const body = useMemo(() => {
        if (view === 'jobs') {
            return (
                <>
                    <div className={styles.zeroResults}>{t('menuPricing.results', { count })}</div>
                    <div className={styles.info}>
                        <div className={styles.columns}>
                            <div className={styles.column}>
                                {!!foundItems.labors?.length && (
                                    <div className={styles.group}>
                                        <div className={styles.groupName}>
                                            {t('menuPricing.labor')}
                                        </div>
                                        {foundItems.labors.slice(0, 50).map((j) => (
                                            <div className={styles.checkBoxArea} key={j.laborId}>
                                                <Checkbox
                                                    className={styles.checkBox}
                                                    onChange={(_, checked) => {
                                                        if (checked) {
                                                            if (
                                                                !selected.labors.some(
                                                                    (labor) =>
                                                                        labor.laborId === j.laborId
                                                                )
                                                            ) {
                                                                setSelected({
                                                                    ...selected,
                                                                    labors: [...selected.labors, j],
                                                                });
                                                            }
                                                        } else {
                                                            if (
                                                                selected.labors.some(
                                                                    (labor) =>
                                                                        labor.laborId === j.laborId
                                                                )
                                                            ) {
                                                                setSelected({
                                                                    ...selected,
                                                                    labors: selected.labors.filter(
                                                                        (l) =>
                                                                            l.laborId !== j.laborId
                                                                    ),
                                                                });
                                                            }
                                                        }
                                                    }}
                                                    checked={selected.labors.some(
                                                        (labor) => labor.laborId === j.laborId
                                                    )}
                                                />
                                                <div className={styles.name}>{j.laborName}</div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                {!!foundItems.parts?.length && (
                                    <div className={styles.group}>
                                        <div className={styles.groupName}>
                                            {t('menuPricing.parts')}
                                        </div>
                                        {foundItems.parts.slice(0, 50).map((j) => (
                                            <div className={styles.checkBoxArea} key={j.partId}>
                                                <Checkbox
                                                    className={styles.checkBox}
                                                    onChange={(_, checked) => {
                                                        if (checked) {
                                                            if (
                                                                !selected.parts.some(
                                                                    (part) =>
                                                                        part.partId === j.partId
                                                                )
                                                            ) {
                                                                setSelected({
                                                                    ...selected,
                                                                    parts: [...selected.parts, j],
                                                                });
                                                            }
                                                        } else {
                                                            if (
                                                                selected.parts.some(
                                                                    (part) =>
                                                                        part.partId === j.partId
                                                                )
                                                            ) {
                                                                setSelected({
                                                                    ...selected,
                                                                    parts: selected.parts.filter(
                                                                        (l) => l.partId !== j.partId
                                                                    ),
                                                                });
                                                            }
                                                        }
                                                    }}
                                                    checked={selected.parts.some(
                                                        (part) => part.partId === j.partId
                                                    )}
                                                />
                                                <div className={styles.name}>{j.partName}</div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                            <div className={styles.column}>
                                {!!foundItems.jobs?.length && (
                                    <div className={styles.group}>
                                        <div className={styles.groupName}>
                                            {t('menuPricing.jobs')}
                                        </div>
                                        {foundItems.jobs.slice(0, 50).map((j) => (
                                            <div className={styles.checkBoxArea} key={j.jobId}>
                                                <Checkbox
                                                    className={styles.checkBox}
                                                    onChange={(_, checked) => {
                                                        if (checked) {
                                                            if (
                                                                !selected.jobs.some(
                                                                    (job) => job.jobId === j.jobId
                                                                )
                                                            ) {
                                                                setSelected({
                                                                    ...selected,
                                                                    jobs: [...selected.jobs, j],
                                                                });
                                                            }
                                                        } else {
                                                            if (
                                                                selected.jobs.some(
                                                                    (job) => job.jobId === j.jobId
                                                                )
                                                            ) {
                                                                setSelected({
                                                                    ...selected,
                                                                    jobs: selected.jobs.filter(
                                                                        (l) => l.jobId !== j.jobId
                                                                    ),
                                                                });
                                                            }
                                                        }
                                                    }}
                                                    checked={selected.jobs.some(
                                                        (job) => job.jobId === j.jobId
                                                    )}
                                                />
                                                <div className={styles.name}>{j.jobName}</div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </>
            );
        }

        const searchText = searchTextRef.current;
        const caption =
            view === 'performSearch'
                ? t('menuPricing.performSearch')
                : t('menuPricing.noResults', { searchText });
        return (
            <>
                <div className={styles.zeroResults}>{t('menuPricing.zeroResults')}</div>
                <div className={styles.searchInfoContainer}>
                    <div className={styles.searchInfo}>
                        <SearchIcon fill={Colors.CM1} size={IconSize.XL} />
                        <div className={styles.searchInfoCaption}>{caption}</div>
                    </div>
                </div>
            </>
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [view, foundItems, selected, setSelected, styles, t]);

    const selectedItems = useMemo(() => {
        return (
            <>
                <div className={styles.selectedCount}>
                    {t('menuPricing.selectedCount', { selectedCount })}
                </div>
                <div className={styles.selectedContainer}>
                    <div className={styles.selectedItems}>
                        {!!selected.parts?.length && (
                            <div>
                                <div className={styles.selectedGroupName}>
                                    {t('menuPricing.parts')}
                                </div>
                                {selected.parts.map((j) => (
                                    <div className={styles.selectedCheckBoxArea} key={j.partId}>
                                        <Checkbox
                                            className={styles.checkBox}
                                            onChange={(_, checked) => {
                                                if (checked) {
                                                    if (
                                                        !selected.parts.some(
                                                            (part) => part.partId === j.partId
                                                        )
                                                    ) {
                                                        setSelected({
                                                            ...selected,
                                                            parts: [...selected.parts, j],
                                                        });
                                                    }
                                                } else {
                                                    if (
                                                        selected.parts.some(
                                                            (part) => part.partId === j.partId
                                                        )
                                                    ) {
                                                        setSelected({
                                                            ...selected,
                                                            parts: selected.parts.filter(
                                                                (l) => l.partId !== j.partId
                                                            ),
                                                        });
                                                    }
                                                }
                                            }}
                                            checked={selected.parts.some(
                                                (part) => part.partId === j.partId
                                            )}
                                        />
                                        <div className={styles.name}>{j.partName}</div>
                                    </div>
                                ))}
                            </div>
                        )}
                        {!!selected.labors?.length && (
                            <div>
                                <div className={styles.selectedGroupName}>
                                    {t('menuPricing.labor')}
                                </div>
                                {selected.labors.map((j) => (
                                    <div className={styles.selectedCheckBoxArea} key={j.laborId}>
                                        <Checkbox
                                            className={styles.checkBox}
                                            onChange={(_, checked) => {
                                                if (checked) {
                                                    if (
                                                        !selected.labors.some(
                                                            (labor) => labor.laborId === j.laborId
                                                        )
                                                    ) {
                                                        setSelected({
                                                            ...selected,
                                                            labors: [...selected.labors, j],
                                                        });
                                                    }
                                                } else {
                                                    if (
                                                        selected.labors.some(
                                                            (labor) => labor.laborId === j.laborId
                                                        )
                                                    ) {
                                                        setSelected({
                                                            ...selected,
                                                            labors: selected.labors.filter(
                                                                (l) => l.laborId !== j.laborId
                                                            ),
                                                        });
                                                    }
                                                }
                                            }}
                                            checked={selected.labors.some(
                                                (labor) => labor.laborId === j.laborId
                                            )}
                                        />
                                        <div className={styles.name}>{j.laborName}</div>
                                    </div>
                                ))}
                            </div>
                        )}
                        {!!selected.jobs?.length && (
                            <div>
                                <div className={styles.selectedGroupName}>
                                    {t('menuPricing.jobs')}
                                </div>
                                {selected.jobs.map((j) => (
                                    <div className={styles.selectedCheckBoxArea} key={j.jobId}>
                                        <Checkbox
                                            className={styles.checkBox}
                                            onChange={(_, checked) => {
                                                if (checked) {
                                                    if (
                                                        !selected.jobs.some(
                                                            (job) => job.jobId === j.jobId
                                                        )
                                                    ) {
                                                        setSelected({
                                                            ...selected,
                                                            jobs: [...selected.jobs, j],
                                                        });
                                                    }
                                                } else {
                                                    if (
                                                        selected.jobs.some(
                                                            (job) => job.jobId === j.jobId
                                                        )
                                                    ) {
                                                        setSelected({
                                                            ...selected,
                                                            jobs: selected.jobs.filter(
                                                                (l) => l.jobId !== j.jobId
                                                            ),
                                                        });
                                                    }
                                                }
                                            }}
                                            checked={selected.jobs.some(
                                                (job) => job.jobId === j.jobId
                                            )}
                                        />
                                        <div className={styles.name}>{j.jobName}</div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </>
        );
    }, [selectedCount, selected, setSelected, styles, t]);

    return (
        <Modal open onClose={onClose}>
            <div className={styles.menuPricing}>
                <div className={styles.header}>
                    <span>{t('menuPricing.header')}</span>
                    <div className={styles.closeButton} onClick={onClose}>
                        <CloseIcon fill="#899198" />
                    </div>
                </div>
                {header}
                {selectedItems}
                {refreshMutation.isLoading ? <AreaSpinner /> : body}
            </div>
            <DeleteConfirmationPopup
                open={isCancelEstimateOpen}
                title={t('menuPricing.cancelEstimateModal.title')}
                body=""
                cancel={t('menuPricing.cancelEstimateModal.cancel')}
                confirm={t('menuPricing.cancelEstimateModal.confirm')}
                onConfirm={onClose}
                onClose={() => {
                    setIsCancelEstimateOpen(false);
                }}
            />
        </Modal>
    );
};

const laborsWithFieldIds = (labors: LaborDto[], allCustomFields: FieldValue[]) => {
    return labors.map((l) => {
        return {
            ...l,
            customizableFields: l.customizableFields?.map((cf) => {
                const field = allCustomFields.find((f) => f.name === cf.name);
                return {
                    ...cf,
                    fieldId: field?.id,
                };
            }),
        };
    });
};

const partsWithFieldIds = (parts: PartDto[], allCustomFields: FieldValue[]) => {
    return parts.map((p) => {
        return {
            ...p,
            customizableFields: p.customizableFields?.map((cf) => {
                const field = allCustomFields.find((f) => f.name === cf.name);
                return {
                    ...cf,
                    fieldId: field?.id,
                };
            }),
        };
    });
};

const jobsWithFieldIds = (jobs: JobDto[], allCustomFields: FieldValue[]) => {
    return jobs.map((j) => {
        return {
            ...j,
            parts: partsWithFieldIds(j.parts, allCustomFields),
            labors: laborsWithFieldIds(j.labors, allCustomFields),
        };
    });
};

export default OpenAPIMenuPricingModal;
