import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

export const useStyles = makeStyles((theme) => ({
    menuPricing: {
        width: 1021,
        height: 624,
        display: 'flex',
        flexDirection: 'column',
        paddingTop: 24,
        paddingBottom: 24,
        paddingLeft: 42,
        paddingRight: 42,
        backgroundColor: theme.palette.neutral[2],
        borderRadius: 20,
    },
    header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...FontPrimary(HeaderStyles.H5_14px, true),
        color: '#000000',
    },
    closeButton: {
        width: 40,
        height: 40,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
    },
    top: {
        display: 'flex',
        justifyContent: 'space-between',
        paddingTop: 16,
        paddingBottom: 16,
    },
    search: {
        width: 312,
    },
    buttons: {
        display: 'flex',
    },
    addManualEstimate: {
        paddingLeft: 16,
        paddingRight: 16,
        width: 'initial',
    },
    cancel: {
        width: 160,
        marginLeft: 24,
        marginRight: 16,
    },
    save: {
        width: 160,
    },
    zeroResults: {
        ...FontPrimary(HeaderStyles.H6_12px, true, Colors.Black0),
        marginBottom: 16,
    },
    searchInfoContainer: {
        flexGrow: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    searchInfo: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        rowGap: 8,
    },
    searchInfoCaption: {
        ...FontSecondary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
    },
    info: {
        height: 0,
        flexGrow: 1,
        overflowY: 'auto',
        display: 'flex',
        alignItems: 'flex-start',
        paddingRight: 1,
        marginRight: -1,
        ...scrollbarStyle(),
    },
    columns: {
        width: 0,
        flexGrow: 1,
        display: 'flex',
        columnGap: 25,
        paddingBottom: 5,
    },
    column: {
        display: 'flex',
        flexDirection: 'column',
        width: 0,
        rowGap: 25,
        flexGrow: 1,
    },
    group: {
        boxShadow: '0px 1px 2px #C8C8C8',
        backgroundColor: 'white',
        borderRadius: 6,
        padding: 16,
    },
    groupName: {
        ...FontPrimary(HeaderStyles.H6_12px, true),
        color: Colors.Black0,
        paddingBottom: 12,
    },
    checkBoxArea: {
        padding: '8px 0',
        display: 'flex',
        columnGap: 8,
        alignItems: 'center',
    },
    checkBox: {
        '&.MuiCheckbox-root': {
            padding: 0,
        },
    },
    name: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
        lineHeight: 'initial',
    },
    selectedCount: {
        borderTop: '1px solid rgba(201, 205, 211, 0.5)',
        ...FontPrimary(HeaderStyles.H6_12px, true, Colors.Black0),
        paddingTop: 10,
        marginBottom: 8,
    },
    selectedContainer: {
        borderBottom: '1px solid rgba(201, 205, 211, 0.5)',
        marginBottom: 10,
        paddingBottom: 4,
        maxHeight: 65,
        overflowY: 'auto',
        ...scrollbarStyle(),
    },
    selectedItems: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'start',
        justifyContent: 'center',
    },
    selectedGroupName: {
        ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
        marginLeft: 6,
        marginBottom: 2,
    },
    selectedCheckBoxArea: {
        padding: '0px 0',
        display: 'flex',
        columnGap: 8,
        alignItems: 'center',
    },
}));
