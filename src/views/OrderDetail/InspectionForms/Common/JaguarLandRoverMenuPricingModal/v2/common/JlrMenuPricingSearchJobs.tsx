import { Box } from '@mui/material';
import { styled } from '@mui/styles';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { IconSize } from 'common/styles/IconSize';
import { useJlrMenuPricingModal } from '../JlrMenuPricingProvider';

export const JlrMenuPricingSearchJobs = () => {
    const { t } = useAppTranslation();
    const { totalJobs } = useJlrMenuPricingModal();
    return (
        <SearchResultsContainer>
            {t('menuPricing.results', { count: totalJobs })}
        </SearchResultsContainer>
    );
};

export const JlrMenuPricingWithoutSearchResults = () => {
    const { t } = useAppTranslation();
    const { search } = useJlrMenuPricingModal();

    const message =
        search.length > 0
            ? t('menuPricing.noResults', { searchText: search })
            : t('menuPricing.performSearch');

    return (
        <WithoutSearchResultsContainer>
            <SearchIcon fill={Colors.CM1} size={IconSize.XL} />
            <NoResultContainer>{message}</NoResultContainer>
        </WithoutSearchResultsContainer>
    );
};

const SearchResultsContainer = styled(Box)(() => ({
    ...FontPrimary(HeaderStyles.H6_12px, true),
    color: '#000000',
    lineHeight: '15px',
    borderTop: '1px solid #C9CDD380',
    padding: '8px 0',
}));

const WithoutSearchResultsContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    rowGap: 8,
    paddingTop: 88,
}));

const NoResultContainer = styled(Box)(({ theme }) => ({
    ...FontSecondary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
}));
