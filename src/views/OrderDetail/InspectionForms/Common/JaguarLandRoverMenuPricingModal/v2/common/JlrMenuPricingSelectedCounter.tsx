import { Box, styled, Typography } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { useJlrMenuPricingModal } from '../JlrMenuPricingProvider';
import { JlrMenuPricingModalBodyHierarchyItem } from './JlrMenuPricingModalBodyHierarchy/JlrMenuPricingModalBodyHierarchyItem';

export const JlrMenuPricingSelectedCounter = () => {
    const { showSelectedJobs, selectedJobs } = useJlrMenuPricingModal();
    const { t } = useAppTranslation();

    return (
        <SelectedJobsCounterContainer>
            <SelectedResultsContainer>
                {t('menuPricing.selectedCount', { selectedCount: selectedJobs.length })}
            </SelectedResultsContainer>
            {showSelectedJobs.map((group) => (
                <SelectedJobsContainer key={group.groupId}>
                    <SelectedJobsTitle>{group.groupDescription.toUpperCase()}</SelectedJobsTitle>
                    {group.jobs.map((job) => (
                        <JlrMenuPricingModalBodyHierarchyItem
                            operation={job}
                            groupId={group.groupId}
                            key={job.jobId}
                        />
                    ))}
                </SelectedJobsContainer>
            ))}
        </SelectedJobsCounterContainer>
    );
};

const SelectedJobsCounterContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    maxHeight: 100,
    borderTop: '1px solid #C9CDD380',
    padding: '10px 0',
    overflowY: 'auto',
}));

const SelectedResultsContainer = styled(Box)(() => ({
    ...FontPrimary(HeaderStyles.H6_12px, true),
    color: '#000000',
    lineHeight: '15px',
}));

const SelectedJobsContainer = styled(Box)(() => ({
    paddingTop: 5,
    paddingLeft: 5,
}));

const SelectedJobsTitle = styled(Typography)(() => ({
    paddingBottom: 5,
}));
