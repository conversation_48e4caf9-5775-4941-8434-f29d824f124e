/* eslint-disable react-hooks/exhaustive-deps */
import { Box, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { JLR_MENU_PRICING_CONSTANTS, useJlrMenuPricingModal } from '../../JlrMenuPricingProvider';
import { JlrMenuPricingModalBodyHierarchyItem } from './JlrMenuPricingModalBodyHierarchyItem';

export const JlrMenuPricingModalBodyHierarchyGroupSearch = () => {
    const {
        jlrGroups: groups,
        handleSeeJobs,
        isExpanded,
        getExpandedGroups,
        showExpandButton,
    } = useJlrMenuPricingModal();
    const { t } = useAppTranslation();

    const { GROUP_PADDING, GROUP_NAME, GAP_BETWEEN } = JLR_MENU_PRICING_CONSTANTS;

    const expandedGroups = getExpandedGroups(groups);

    return (
        <GroupViewContainer>
            <ColumnContainer style={{ rowGap: GAP_BETWEEN.GROUPS }}>
                {expandedGroups.map((group) => (
                    <GroupContainer
                        style={{
                            paddingTop: GROUP_PADDING.TOP,
                            paddingBottom: GROUP_PADDING.BOTTOM,
                        }}
                        key={group.groupId}
                    >
                        <GroupNameContainer
                            style={{
                                lineHeight: `${GROUP_NAME.HEIGHT}px`,
                                marginBottom: GROUP_NAME.MARGIN_BOTTOM,
                            }}
                        >
                            {group.groupDescription.toUpperCase()}
                        </GroupNameContainer>
                        <OperationContainer style={{ rowGap: GAP_BETWEEN.OPERATIONS }}>
                            {group.jobs.map((operation) => (
                                <JlrMenuPricingModalBodyHierarchyItem
                                    key={operation.jobId}
                                    operation={operation}
                                    groupId={group.groupId}
                                    markDescription
                                />
                            ))}
                            {showExpandButton(groups).some(
                                (expandedGroup) => expandedGroup === group.groupId
                            ) && (
                                <ExpandButton onClick={() => handleSeeJobs(group.groupId)}>
                                    {isExpanded(group.groupId)
                                        ? t('workshopPlanner.orderPopup.seeLess')
                                        : t('workshopPlanner.orderPopup.seeMore')}
                                </ExpandButton>
                            )}
                        </OperationContainer>
                    </GroupContainer>
                ))}
            </ColumnContainer>
        </GroupViewContainer>
    );
};
const GroupViewContainer = styled(Box)(() => ({
    flexGrow: 1,
    overflowY: 'auto',
    display: 'flex',
    alignItems: 'flex-start',
}));
const ColumnContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    width: 0,
    flexGrow: 1,
}));
const GroupContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    borderBottom: '1px solid #C9CDD380',
}));
const GroupNameContainer = styled(Box)(() => ({
    ...FontPrimary(HeaderStyles.H6_12px, true),
    fontWeight: 400,
}));
const OperationContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
}));
const ExpandButton = styled(Box)(({ theme }) => ({
    cursor: 'pointer',
    textTransform: 'none',
    color: '#0069FF',
    display: 'flex',
    justifyContent: 'flex-end',
    marginBottom: -5,
    ...theme.typography.h10Roboto,
}));
