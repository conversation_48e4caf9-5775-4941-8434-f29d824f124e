import { Box, styled } from '@mui/material';
import { OperationJob } from 'api/jlrItemJobs/_common';
import { Checkbox as MuiCheckbox } from 'common/components/Inputs';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { memo } from 'react';
import { useJlrMenuPricingModal } from '../../JlrMenuPricingProvider';

export const JlrMenuPricingModalBodyHierarchyItem = memo(
    ({
        operation,
        groupId,
        markDescription,
    }: {
        operation: OperationJob;
        groupId: string;
        markDescription?: boolean;
    }) => {
        const { selectedJobs, handleSelectJob } = useJlrMenuPricingModal();

        const isSelected = selectedJobs.some(
            (selected) => selected.jobId === operation.jobId && selected.groupId === groupId
        );
        return (
            <OperationContainer>
                <HeaderContainer>
                    <CheckboxAreaContainer>
                        <Checkbox
                            onChange={() => handleSelectJob({ jobId: operation.jobId, groupId })}
                            checked={isSelected}
                        />
                        <Name sx={{ color: isSelected && markDescription ? '#0069FF' : 'inherit' }}>
                            {operation.jobDescription}
                        </Name>
                    </CheckboxAreaContainer>
                </HeaderContainer>
            </OperationContainer>
        );
    }
);

const OperationContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 6,
    '&.open': {
        backgroundColor: 'rgba(0, 105, 255, 0.1)',
        paddingTop: 8,
        marginTop: -8,
        paddingLeft: 8,
        marginLeft: -8,
        paddingRight: 8,
        marginRight: -8,
        paddingBottom: 25,
        marginBottom: -4,
    },
}));

const HeaderContainer = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
}));

const CheckboxAreaContainer = styled(Box)(() => ({
    display: 'flex',
    columnGap: 8,
    alignItems: 'center',
    width: 0,
    flexGrow: 1,
}));

const Checkbox = styled(MuiCheckbox)(() => ({
    '&.MuiCheckbox-root': {
        padding: 0,
    },
}));

const Name = styled(Box)(({ theme }) => ({
    ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
    lineHeight: 'initial',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
}));
