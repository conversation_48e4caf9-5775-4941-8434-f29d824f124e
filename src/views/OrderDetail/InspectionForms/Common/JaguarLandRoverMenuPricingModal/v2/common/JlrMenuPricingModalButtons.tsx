import { styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useJlrMenuPricingModal } from '../JlrMenuPricingProvider';

export const AddManualEstimateButton = () => {
    const { t } = useAppTranslation();
    const { handleAddManualEstimate } = useJlrMenuPricingModal();
    return (
        <AddManualEstimateButtonStyled
            cmosVariant={'stroke'}
            cmosSize={'medium'}
            color={Colors.CM1}
            Icon={PlusIcon}
            label={t('menuPricing.addManualEstimate')}
            onClick={handleAddManualEstimate}
        />
    );
};

export const CancelButton = () => {
    const { t } = useAppTranslation();
    const { onClose } = useJlrMenuPricingModal();
    return (
        <CancelButtonStyled
            cmosVariant={'filled'}
            cmosSize={'medium'}
            color={Colors.Neutral3}
            label={t('commonLabels.cancel')}
            onClick={onClose}
        />
    );
};

export const SaveButton = () => {
    const { t } = useAppTranslation();
    const { handleSave } = useJlrMenuPricingModal();
    return (
        <SaveButtonStyled
            cmosVariant={'filled'}
            cmosSize={'medium'}
            color={Colors.Success}
            label={t('commonLabels.save')}
            onClick={handleSave}
        />
    );
};

const AddManualEstimateButtonStyled = styled(Button)(() => ({
    paddingLeft: 16,
    paddingRight: 16,
    width: 'initial',
}));

const CancelButtonStyled = styled(Button)(() => ({
    width: 160,
    marginLeft: 24,
    marginRight: 16,
}));

const SaveButtonStyled = styled(Button)(() => ({
    width: 160,
}));
