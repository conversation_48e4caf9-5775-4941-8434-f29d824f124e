import { Box, styled } from '@mui/material';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useJlrMenuPricingModal } from '../JlrMenuPricingProvider';

export const JlrMenuPricingModalHeader = () => {
    const { onClose } = useJlrMenuPricingModal();
    const { t } = useAppTranslation();
    return (
        <Container>
            <span>{t('menuPricing.header')}</span>
            <Button onClick={onClose}>
                <CloseIcon fill="#899198" />
            </Button>
        </Container>
    );
};

const Container = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontFamily: 'Inter',
    fontWeight: 700,
    color: '#000000',
}));

const Button = styled(Box)(() => ({
    width: 40,
    height: 40,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
}));
