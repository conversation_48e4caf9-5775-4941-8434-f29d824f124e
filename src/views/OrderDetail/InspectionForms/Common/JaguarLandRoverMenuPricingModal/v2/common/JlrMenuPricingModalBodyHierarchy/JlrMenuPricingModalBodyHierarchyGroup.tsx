/* eslint-disable react-hooks/exhaustive-deps */
import { Box, styled } from '@mui/material';
import { OperationGroup } from 'api/jlrItemJobs/_common';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { useMemo } from 'react';
import { JLR_MENU_PRICING_CONSTANTS, useJlrMenuPricingModal } from '../../JlrMenuPricingProvider';
import { JlrMenuPricingModalBodyHierarchyItem } from './JlrMenuPricingModalBodyHierarchyItem';

export const JlrMenuPricingModalBodyHierarchyGroup = () => {
    const { jlrGroups: groups } = useJlrMenuPricingModal();
    const { GROUP_PADDING, GROUP_NAME, GAP_BETWEEN, OPERATION_HEIGHT } = JLR_MENU_PRICING_CONSTANTS;

    const [leftGroups, rightGroups] = useMemo(() => {
        const getGroupHeight = (group: OperationGroup) =>
            GROUP_PADDING.TOP +
            GROUP_PADDING.BOTTOM +
            GROUP_NAME.HEIGHT +
            GROUP_NAME.MARGIN_BOTTOM +
            GAP_BETWEEN.OPERATIONS * Math.max(group.jobs.length - 1, 0) +
            OPERATION_HEIGHT * group.jobs.length;
        const getColumnHeight = (groups: OperationGroup[]) =>
            groups.reduce((prev, group) => prev + getGroupHeight(group), 0) +
            GAP_BETWEEN.GROUPS * Math.max(groups.length - 1, 0);
        const leftGroupsResult: OperationGroup[] = [...groups];
        const rightGroupsResult: OperationGroup[] = [];
        while (
            Math.abs(getColumnHeight(leftGroupsResult) - getColumnHeight(rightGroupsResult)) >
            Math.abs(
                getColumnHeight(leftGroupsResult.slice(0, -1)) -
                    getColumnHeight([...leftGroupsResult.slice(-1), ...rightGroupsResult])
            )
        ) {
            const item = leftGroupsResult.pop();
            if (!item) break;
            rightGroupsResult.unshift(item);
        }
        return [leftGroupsResult, rightGroupsResult];
    }, [groups]);

    return (
        <GroupViewContainer>
            <ColumnsContainer>
                <Column groups={leftGroups} />
                <Column groups={rightGroups} />
            </ColumnsContainer>
        </GroupViewContainer>
    );
};
export const Column = ({ groups }: { groups: OperationGroup[] }) => {
    const { handleSeeJobs, isExpanded, getExpandedGroups, showExpandButton } =
        useJlrMenuPricingModal();
    const { t } = useAppTranslation();

    const { GAP_BETWEEN, GROUP_PADDING, GROUP_NAME } = JLR_MENU_PRICING_CONSTANTS;

    const expandedGroups = getExpandedGroups(groups);

    return (
        <ColumnContainer style={{ rowGap: GAP_BETWEEN.GROUPS }}>
            {expandedGroups.map((group) => (
                <GroupContainer
                    style={{ paddingTop: GROUP_PADDING.TOP, paddingBottom: GROUP_PADDING.BOTTOM }}
                    key={group.groupId}
                >
                    <GroupNameContainer
                        style={{
                            lineHeight: `${GROUP_NAME.HEIGHT}px`,
                            marginBottom: GROUP_NAME.MARGIN_BOTTOM,
                        }}
                    >
                        {group.groupDescription}
                    </GroupNameContainer>
                    <OperationContainer style={{ rowGap: GAP_BETWEEN.OPERATIONS }}>
                        {group.jobs.map((operation) => (
                            <JlrMenuPricingModalBodyHierarchyItem
                                key={operation.jobId}
                                operation={operation}
                                groupId={group.groupId}
                                markDescription
                            />
                        ))}
                        {showExpandButton(groups).some(
                            (expandedGroup) => expandedGroup === group.groupId
                        ) && (
                            <ExpandButton onClick={() => handleSeeJobs(group.groupId)}>
                                {isExpanded(group.groupId)
                                    ? t('workshopPlanner.orderPopup.seeLess')
                                    : t('workshopPlanner.orderPopup.seeMore')}
                            </ExpandButton>
                        )}
                    </OperationContainer>
                </GroupContainer>
            ))}
        </ColumnContainer>
    );
};
const GroupViewContainer = styled(Box)(() => ({
    height: 0,
    flexGrow: 1,
    overflowY: 'auto',
    display: 'flex',
    alignItems: 'flex-start',
    paddingRight: 1,
    marginRight: -1,
}));
const ColumnsContainer = styled(Box)(() => ({
    width: 0,
    flexGrow: 1,
    display: 'flex',
    columnGap: 25,
    paddingBottom: 5,
}));
const ColumnContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    width: 0,
    flexGrow: 1,
}));
const GroupContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: theme.palette.neutral[1],
    borderRadius: 10,
    boxShadow: '0px 4px 4px rgba(213, 213, 213, 0.25)',
    paddingLeft: 16,
    paddingRight: 16,
}));
const GroupNameContainer = styled(Box)(() => ({
    ...FontPrimary(HeaderStyles.H6_12px, true),
    color: '#000000',
}));
const OperationContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
}));
const ExpandButton = styled(Box)(({ theme }) => ({
    cursor: 'pointer',
    textTransform: 'none',
    color: '#0069FF',
    display: 'flex',
    justifyContent: 'flex-end',
    ...theme.typography.h10Roboto,
}));
