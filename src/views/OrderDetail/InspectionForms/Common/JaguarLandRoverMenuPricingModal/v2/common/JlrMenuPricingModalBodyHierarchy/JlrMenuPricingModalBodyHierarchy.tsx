import { styled } from '@mui/material';
import AreaSpinner from 'common/components/AreaSpinner';
import React from 'react';
import { useJlrMenuPricingModal } from '../../JlrMenuPricingProvider';
import {
    JlrMenuPricingSearchJobs,
    JlrMenuPricingWithoutSearchResults,
} from '../JlrMenuPricingSearchJobs';
import { JlrMenuPricingVehicleNotFound } from '../JlrMenuPricingVehicleNotFound';
import { JlrMenuPricingModalBodyHierarchyGroup } from './JlrMenuPricingModalBodyHierarchyGroup';
import { JlrMenuPricingModalBodyHierarchyGroupSearch } from './JlrMenuPricingModalBodyHierarchyGroupSearch';

export const JlrMenuPricingModalBodyHierarchy = () => {
    const { isFetching, withoutVehicle } = useJlrMenuPricingModal();

    if (isFetching) return <AreaSpinner />;

    return (
        <Container>
            {withoutVehicle ? <JlrMenuPricingVehicleNotFound /> : <JlrMenuPricingVehicleFound />}
        </Container>
    );
};

const JlrMenuPricingVehicleFound = () => {
    const { jlrGroups } = useJlrMenuPricingModal();

    return (
        <React.Fragment>
            <JlrMenuPricingSearchJobs />
            {jlrGroups.length === 0 ? <JlrMenuPricingWithoutSearchResults /> : <JlrGroup />}
        </React.Fragment>
    );
};

const JlrGroup = () => {
    const { search } = useJlrMenuPricingModal();

    const showSearchGroup = search.length > 0;
    if (showSearchGroup) {
        return <JlrMenuPricingModalBodyHierarchyGroupSearch />;
    } else {
        return <JlrMenuPricingModalBodyHierarchyGroup />;
    }
};

const Container = styled('div')(() => ({
    display: 'flex',
    flexDirection: 'column',
    height: 0,
    flexGrow: 1,
}));
