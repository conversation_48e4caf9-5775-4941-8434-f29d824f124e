import { Box, IconButton, styled } from '@mui/material';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import TextField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useJlrMenuPricingModal } from '../JlrMenuPricingProvider';
import { AddManualEstimateButton, CancelButton, SaveButton } from './JlrMenuPricingModalButtons';
import styles from './styles.module.css';

export const JlrMenuPricingModalBodyTop = () => {
    const { t } = useAppTranslation();
    const { handleSearch } = useJlrMenuPricingModal();

    const [inputSearch, setInputSearch] = useState('');

    return (
        <Container>
            <TextField
                cmosVariant="roundedGrey"
                placeholder={t('menuPricing.search')}
                name={'search'}
                value={inputSearch}
                endAdornment={
                    <IconButton onClick={() => handleSearch(inputSearch)}>
                        <SearchIcon />
                    </IconButton>
                }
                inputWrapperClasses={{ self: styles.search }}
                onEnterPress={() => handleSearch(inputSearch)}
                onChange={(e) => setInputSearch(e.target.value)}
            />
            <ButtonsContainer>
                <AddManualEstimateButton />
                <CancelButton />
                <SaveButton />
            </ButtonsContainer>
        </Container>
    );
};

const Container = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    paddingTop: 16,
    paddingBottom: 32,
}));

const ButtonsContainer = styled(Box)(() => ({
    display: 'flex',
}));

export default JlrMenuPricingModalBodyTop;
