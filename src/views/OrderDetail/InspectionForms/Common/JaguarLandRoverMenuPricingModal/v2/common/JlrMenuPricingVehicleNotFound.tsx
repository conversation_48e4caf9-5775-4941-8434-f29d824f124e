import { Box, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { AddManualEstimateButton } from './JlrMenuPricingModalButtons';

export const JlrMenuPricingVehicleNotFound = () => {
    const { t } = useAppTranslation();

    return (
        <VehicleNotFoundContainer>
            <VehicleNotFoundCaption>{t('menuPricing.vehicleNotFound')}</VehicleNotFoundCaption>
            <AddManualEstimateButton />
        </VehicleNotFoundContainer>
    );
};

const VehicleNotFoundContainer = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    rowGap: 16,
    flexGrow: 1,
}));

const VehicleNotFoundCaption = styled(Box)(({ theme }) => ({
    ...FontSecondary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
}));
