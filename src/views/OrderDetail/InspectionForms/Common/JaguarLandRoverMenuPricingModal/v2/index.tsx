import { Box, styled } from '@mui/material';
import {
    JlrMenuPricingModalBodyHierarchy,
    JlrMenuPricingModalBodyTop,
    JlrMenuPricingModalHeader,
    JlrMenuPricingSelectedCounter,
} from './common';
import JlrMenuPricingModalProvider from './JlrMenuPricingProvider';

interface JlrMenuPricingModalProps {
    orderId: number;
    repairId: number;
    masterItemId: number;
    onClose: () => void;
}

const JaguarLandRoverMenuPricingModalV2 = (props: JlrMenuPricingModalProps) => {
    return (
        <JlrMenuPricingModalProvider {...props}>
            <Container>
                <JlrMenuPricingModalHeader />
                <JlrMenuPricingModalBodyTop />
                <JlrMenuPricingSelectedCounter />
                <JlrMenuPricingModalBodyHierarchy />
            </Container>
        </JlrMenuPricingModalProvider>
    );
};

const Container = styled(Box)(({ theme }) => ({
    width: 1021,
    height: 624,
    display: 'flex',
    flexDirection: 'column',
    paddingTop: 24,
    paddingBottom: 24,
    paddingLeft: 42,
    paddingRight: 42,
    backgroundColor: theme.palette.neutral[2],
    borderRadius: 20,
}));

export default JaguarLandRoverMenuPricingModalV2;
