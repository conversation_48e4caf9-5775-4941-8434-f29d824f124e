import {
    ItemJobDto,
    ItemJobGroupDto,
    ItemJobOperationDto,
    ItemJobVariantDto,
} from 'datacontracts/ItemJobHierarchyDto';

interface Openable {
    open: boolean;
    setOpen: (open: boolean) => void;
}

export interface Checkable {
    checked: boolean;
    setChecked: (checked: boolean) => void;
}

export interface Searchable {
    searchStrings: string[];
}

export interface ItemJobGroup extends ItemJobGroupDto {
    operations: ItemJobOperation[];
}

export interface ItemJobOperation extends ItemJobOperationDto, Openable, Checkable, Searchable {
    jobVariants: ItemJobVariant[];
}

export interface ItemJobVariant extends ItemJobVariantDto, Openable, Checkable, Searchable {
    jobs: ItemJob[];
}

export interface ItemJob extends ItemJobDto, Checkable, Searchable {}
