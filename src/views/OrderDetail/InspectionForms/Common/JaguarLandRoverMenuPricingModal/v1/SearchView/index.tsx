import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useEffect, useRef, useState } from 'react';
import { Checkable, ItemJobGroup, ItemJobOperation, ItemJobVariant, Searchable } from '../types';
import SearchResult from './SearchResult';
import { useStyles } from './css';

interface ISearchResult {
    getTarget: (context: ItemJobGroup[]) => Searchable & Checkable;
    key: string;
}

interface SearchViewProps {
    visible: boolean;
    searchText: string;
    groups: ItemJobGroup[];
}

export const SearchView = ({ visible, searchText, groups }: SearchViewProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [searchCandidates, setSearchCandidates] = useState<ISearchResult[]>([]);
    const [searchResults, setSearchResults] = useState<ISearchResult[]>([]);
    const searchResultsTop = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        setSearchCandidates(groups.flatMap((g) => getCandidatesFromGroup(g)));

        function getCandidatesFromGroup(group: ItemJobGroup): ISearchResult[] {
            return group.operations.flatMap((o) => getCandidatesFromOperation(group, o));
        }

        function getCandidatesFromOperation(
            group: ItemJobGroup,
            operation: ItemJobOperation
        ): ISearchResult[] {
            return [
                {
                    getTarget: (context) =>
                        context
                            .find((g) => g.id === group.id)!
                            .operations.find((o) => o.id === operation.id)!,
                    key: 'o' + group.id + operation.id,
                },
                ...operation.jobVariants.flatMap((jv) =>
                    getCandidatesFromJobVariant(group, operation, jv)
                ),
            ];
        }
        function getCandidatesFromJobVariant(
            group: ItemJobGroup,
            operation: ItemJobOperation,
            jobVariant: ItemJobVariant
        ): ISearchResult[] {
            return [
                {
                    getTarget: (context) =>
                        context
                            .find((g) => g.id === group.id)!
                            .operations.find((o) => o.id === operation.id)!
                            .jobVariants.find((jv) => jv.id === jobVariant.id)!,
                    key: 'jv' + group.id + operation.id + jobVariant.id,
                },
                ...jobVariant.jobs.map(
                    (job): ISearchResult => ({
                        getTarget: (context) =>
                            context
                                .find((g) => g.id === group.id)!
                                .operations.find((o) => o.id === operation.id)!
                                .jobVariants.find((jv) => jv.id === jobVariant.id)!
                                .jobs.find((j) => j.id === job.id)!,
                        key: 'j' + group.id + operation.id + jobVariant.id + job.id,
                    })
                ),
            ];
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [groups.length]);

    useEffect(() => {
        if (!visible) return;

        setSearchResults(
            searchCandidates.filter((c) =>
                c
                    .getTarget(groups)
                    .searchStrings.join(' / ')
                    .toLocaleLowerCase()
                    .includes(searchText.toLocaleLowerCase())
            )
        );
        searchResultsTop.current?.scrollIntoView();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible, searchText, searchCandidates]);

    return (
        <div style={{ display: visible ? undefined : 'none' }} className={styles.searchView}>
            <div className={styles.searchHeader}>
                {t('menuPricing.results', { count: searchResults.length })}
            </div>
            {searchResults.length > 0 && (
                <div className={styles.searchResults}>
                    <div ref={searchResultsTop} />
                    {searchResults.map((sr) => (
                        <SearchResult
                            key={sr.key}
                            checked={sr.getTarget(groups).checked}
                            setChecked={sr.getTarget(groups).setChecked}
                            searchStrings={sr.getTarget(groups).searchStrings}
                            highlighted={searchText}
                        />
                    ))}
                </div>
            )}
            {searchResults.length === 0 && (
                <div className={styles.noResults}>
                    <SearchIcon fill={Colors.CM1} size={IconSize.XL} />
                    <div className={styles.noResultsCaption}>
                        {t('menuPricing.noResults', { searchText })}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SearchView;
