import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    searchView: {
        display: 'flex',
        flexDirection: 'column',
        height: 0,
        flexGrow: 1,
    },
    searchHeader: {
        ...FontPrimary(HeaderStyles.H6_12px, true),
        color: '#000000',
        lineHeight: '15px',
        paddingBottom: 16,
    },
    searchResults: {
        display: 'flex',
        flexDirection: 'column',
        height: 0,
        flexGrow: 1,
        overflowY: 'auto',
    },
    noResults: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        rowGap: 8,
        paddingTop: 88,
        borderTop: '1px solid rgba(201, 205, 211, 0.5)',
    },
    noResultsCaption: {
        ...FontSecondary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
    },
}));
