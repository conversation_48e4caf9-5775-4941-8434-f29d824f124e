import { makeStyles } from '@mui/styles';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    searchResult: {
        display: 'flex',
        alignItems: 'center',
        columnGap: 14,
        borderTop: '1px solid rgba(201, 205, 211, 0.5)',
        paddingTop: 16,
        paddingBottom: 16,
    },
    checkBox: {
        '&.MuiCheckbox-root': {
            padding: 0,
        },
    },
    text: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
        lineHeight: '14px',
    },
    part: {
        paddingTop: 1.5,
        paddingBottom: 1.5,

        '&.bold': {
            ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
            lineHeight: '14px',
        },

        '&.highlighted': {
            backgroundColor: 'rgba(0, 105, 255, 0.15);',
        },
    },
}));
