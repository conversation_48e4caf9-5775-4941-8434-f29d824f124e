import clsx from 'clsx';
import { Checkbox } from 'common/components/Inputs';
import { memo, useMemo } from 'react';
import { useStyles } from './css';

interface SearchResultProps {
    setChecked: (checked: boolean) => void;
    checked: boolean;
    searchStrings: string[];
    highlighted: string;
}

interface Part {
    text: string;
    highlighted: boolean;
    bold: boolean;
}

export const SearchResult = memo(
    ({ setChecked, checked, searchStrings, highlighted }: SearchResultProps) => {
        const styles = useStyles();

        const parts = useMemo(() => {
            const wholeString = searchStrings.join(' / ');
            const matchAll = Array.from(
                wholeString.matchAll(
                    new RegExp(highlighted.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
                )
            );
            const bounds = [
                0,
                ...matchAll.flatMap((m) => [m.index ?? 0, (m.index ?? 0) + m[0].length]),
                wholeString.length,
                searchStrings[0].length,
            ]
                .sort((a, b) => a - b)
                .filter((v, i, a) => a.indexOf(v) === i);

            return bounds.reduce(
                (prev, current, index, array) =>
                    index === array.length - 1
                        ? prev
                        : prev.concat({
                              text: wholeString.substring(current, array[index + 1]),
                              highlighted: matchAll.some(
                                  (m) =>
                                      (m.index ?? 0) <= current &&
                                      current < (m.index ?? 0) + m[0].length
                              ),
                              bold: current < searchStrings[0].length,
                          }),
                new Array<Part>()
            );
        }, [searchStrings, highlighted]);

        return (
            <div className={styles.searchResult}>
                <Checkbox
                    className={styles.checkBox}
                    onChange={(_, checked) => setChecked(checked)}
                    checked={checked}
                />
                <div className={styles.text}>
                    {parts.map((part, index) => (
                        <span
                            key={index}
                            className={clsx(
                                styles.part,
                                part.bold && 'bold',
                                part.highlighted && 'highlighted'
                            )}
                        >
                            {part.text}
                        </span>
                    ))}
                </div>
            </div>
        );
    }
);

export default SearchResult;
