import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { Checkbox } from 'common/components/Inputs';
import { Colors } from 'common/styles/Colors';
import { memo } from 'react';
import { ItemJobVariant } from '../../types';
import { useStyles } from './css';

interface JobVariantProps {
    jobVariant: ItemJobVariant;
}

export const JobVariant = memo(({ jobVariant }: JobVariantProps) => {
    const styles = useStyles();

    return (
        <div className={styles.jobVariant}>
            <div className={styles.header}>
                <div className={styles.checkBoxArea}>
                    <Checkbox
                        className={styles.checkBox}
                        checked={jobVariant.checked}
                        onChange={(_, checked) => jobVariant.setChecked(checked)}
                    />
                    <div className={clsx(styles.name, jobVariant.open && 'open')}>
                        {jobVariant.description}
                    </div>
                </div>
                {jobVariant.jobs.length > 0 && (
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={'small'}
                        color={Colors.CM1}
                        Icon={jobVariant.open ? LessIcon : PlusIcon}
                        onClick={() => jobVariant.setOpen(!jobVariant.open)}
                        className={styles.button}
                    />
                )}
            </div>
            <div className={clsx(styles.jobs, jobVariant.open && 'open')}>
                {jobVariant.jobs.map((job) => (
                    <div className={styles.job} key={job.id}>
                        <Checkbox
                            className={styles.checkBox}
                            checked={job.checked}
                            onChange={(_, checked) => job.setChecked(checked)}
                        />
                        <div className={styles.name}>{job.description}</div>
                    </div>
                ))}
            </div>
        </div>
    );
});

export default JobVariant;
