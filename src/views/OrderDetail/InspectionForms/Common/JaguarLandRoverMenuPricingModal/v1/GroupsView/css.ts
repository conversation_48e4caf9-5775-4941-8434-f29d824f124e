import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    groupsView: {
        height: 0,
        flexGrow: 1,
        overflowY: 'auto',
        display: 'flex',
        alignItems: 'flex-start',
        paddingRight: 1,
        marginRight: -1,
    },
    columns: {
        width: 0,
        flexGrow: 1,
        display: 'flex',
        columnGap: 25,
        paddingBottom: 5,
    },
    column: {
        display: 'flex',
        flexDirection: 'column',
        width: 0,
        flexGrow: 1,
    },
    group: {
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: theme.palette.neutral[1],
        borderRadius: 10,
        boxShadow: '0px 4px 4px rgba(213, 213, 213, 0.25)',
        paddingLeft: 16,
        paddingRight: 16,
    },
    groupName: {
        ...FontPrimary(HeaderStyles.H6_12px, true),
        color: '#000000',
    },
    operations: {
        display: 'flex',
        flexDirection: 'column',
    },
}));
