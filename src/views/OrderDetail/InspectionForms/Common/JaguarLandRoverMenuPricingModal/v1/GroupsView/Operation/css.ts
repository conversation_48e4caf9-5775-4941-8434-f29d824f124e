import { makeStyles } from '@mui/styles';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    operation: {
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 6,
        '&.open': {
            backgroundColor: 'rgba(0, 105, 255, 0.1)',
            paddingTop: 8,
            marginTop: -8,
            paddingLeft: 8,
            marginLeft: -8,
            paddingRight: 8,
            marginRight: -8,
            paddingBottom: 25,
            marginBottom: -4,
        },
    },
    header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    checkBoxArea: {
        display: 'flex',
        columnGap: 8,
        alignItems: 'center',
        width: 0,
        flexGrow: 1,
    },
    checkBox: {
        '&.MuiCheckbox-root': {
            padding: 0,
        },
    },
    name: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
        lineHeight: 'initial',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
    },
    line: {
        borderTop: '1px solid rgba(201, 205, 211, 0.5)',
        marginTop: 8,
        marginBottom: 8,
    },
    button: {
        marginRight: 6,
    },
    jobVariants: {
        display: 'none',
        flexDirection: 'column',
        rowGap: 18,

        '&.open': {
            display: 'flex',
        },
    },
}));
