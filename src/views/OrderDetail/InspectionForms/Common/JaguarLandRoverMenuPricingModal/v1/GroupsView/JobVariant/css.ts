import { makeStyles } from '@mui/styles';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    jobVariant: {
        display: 'flex',
        flexDirection: 'column',
        marginLeft: 13,
    },
    header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    checkBoxArea: {
        display: 'flex',
        columnGap: 8,
        alignItems: 'center',
        width: 0,
        flexGrow: 1,
    },
    checkBox: {
        '&.MuiCheckbox-root': {
            padding: 0,
        },
    },
    name: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
        lineHeight: 'initial',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',

        '&.open': {
            ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[8]),
            lineHeight: 'initial',
        },
    },
    button: {
        marginRight: 6,
    },
    jobs: {
        display: 'none',
        flexDirection: 'column',
        marginLeft: 32,

        '&.open': {
            display: 'flex',
        },
    },
    job: {
        display: 'flex',
        alignItems: 'center',
        columnGap: 8,
        marginTop: 18,
    },
}));
