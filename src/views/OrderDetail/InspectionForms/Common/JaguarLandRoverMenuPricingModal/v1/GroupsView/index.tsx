import { useMemo } from 'react';
import { ItemJobGroup } from '../types';
import Operation from './Operation';
import { useStyles } from './css';

interface GroupsViewProps {
    visible: boolean;
    groups: ItemJobGroup[];
}

export const GroupsView = ({ visible, groups }: GroupsViewProps) => {
    const styles = useStyles();

    const groupPaddingTop = 16;
    const groupPaddingBottom = 16;
    const groupNameHeight = 15;
    const groupNameMarginBottom = 21;
    const gapBetweenOperations = 20;
    const operationHeight = 24;
    const gapBetweenGroups = 24;

    const [leftGroups, rightGroups] = useMemo(() => {
        const getGroupHeight = (group: ItemJobGroup) =>
            groupPaddingTop +
            groupPaddingBottom +
            groupNameHeight +
            groupNameMarginBottom +
            gapBetweenOperations * Math.max(group.operations.length - 1, 0) +
            operationHeight * group.operations.length;

        const getColumnHeight = (groups: ItemJobGroup[]) =>
            groups.reduce((prev, group) => prev + getGroupHeight(group), 0) +
            gapBetweenGroups * Math.max(groups.length - 1, 0);

        const leftGroupsResult: ItemJobGroup[] = [...groups];
        const rightGroupsResult: ItemJobGroup[] = [];
        while (
            Math.abs(getColumnHeight(leftGroupsResult) - getColumnHeight(rightGroupsResult)) >
            Math.abs(
                getColumnHeight(leftGroupsResult.slice(0, -1)) -
                    getColumnHeight([...leftGroupsResult.slice(-1), ...rightGroupsResult])
            )
        ) {
            const item = leftGroupsResult.pop();
            if (!item) break;
            rightGroupsResult.unshift(item);
        }
        return [leftGroupsResult, rightGroupsResult];
    }, [groups]);

    return (
        <div style={{ display: visible ? undefined : 'none' }} className={styles.groupsView}>
            <div className={styles.columns}>
                {renderColumn(leftGroups)}
                {renderColumn(rightGroups)}
            </div>
        </div>
    );

    function renderColumn(groups: ItemJobGroup[]) {
        return (
            <div style={{ rowGap: gapBetweenGroups }} className={styles.column}>
                {groups.map((group) => (
                    <div
                        style={{ paddingTop: groupPaddingTop, paddingBottom: groupPaddingBottom }}
                        className={styles.group}
                        key={group.id}
                    >
                        <div
                            style={{
                                lineHeight: `${groupNameHeight}px`,
                                marginBottom: groupNameMarginBottom,
                            }}
                            className={styles.groupName}
                        >
                            {group.description}
                        </div>
                        <div style={{ rowGap: gapBetweenOperations }} className={styles.operations}>
                            {group.operations.map((operation) => (
                                <Operation operation={operation} key={operation.id} />
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        );
    }
};

export default GroupsView;
