import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { Checkbox } from 'common/components/Inputs';
import { Colors } from 'common/styles/Colors';
import { memo } from 'react';
import { ItemJobOperation } from '../../types';
import JobVariant from '../JobVariant';
import { useStyles } from './css';

interface OperationProps {
    operation: ItemJobOperation;
}

export const Operation = memo(({ operation }: OperationProps) => {
    const styles = useStyles();

    return (
        <div className={clsx(styles.operation, operation.open && 'open')}>
            <div className={styles.header}>
                <div className={styles.checkBoxArea}>
                    <Checkbox
                        className={styles.checkBox}
                        onChange={(_, checked) => operation.setChecked(checked)}
                        checked={operation.checked}
                    />
                    <div className={styles.name}>{operation.description}</div>
                </div>
                {operation.jobVariants.length > 0 && (
                    <Button
                        cmosVariant={'typography'}
                        cmosSize={'small'}
                        color={Colors.CM1}
                        Icon={operation.open ? LessIcon : PlusIcon}
                        onClick={() => operation.setOpen(!operation.open)}
                        className={styles.button}
                    />
                )}
            </div>
            {operation.open && <div className={styles.line} />}
            <div className={clsx(styles.jobVariants, operation.open && 'open')}>
                {operation.jobVariants.map((jobVariant) => (
                    <JobVariant jobVariant={jobVariant} key={jobVariant.id} />
                ))}
            </div>
        </div>
    );
});

export default Operation;
