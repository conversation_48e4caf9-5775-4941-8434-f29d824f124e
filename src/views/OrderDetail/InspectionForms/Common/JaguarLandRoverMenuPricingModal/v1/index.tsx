import { Icon<PERSON>utton } from '@mui/material';
import ItemJobsAPI from 'api/ItemJobs';
import AreaSpinner from 'common/components/AreaSpinner';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import TextField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { addEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { createEstimatesFromJaguarIntegration } from 'store/slices/order/orderInspectionFormsSlice/thunks/createEstimatesFromJaguarIntegration';
import GroupsView from './GroupsView';
import SearchView from './SearchView';
import { useStyles } from './css';
import { ItemJobGroup } from './types';

interface JaguarLandRoverMenuPricingModalProps {
    orderId: number;
    repairId: number;
    masterItemId: number;
    name: string;
    existingJobIds: string[];
    onClose: () => void;
}

export const JaguarLandRoverMenuPricingModalV1 = ({
    orderId,
    repairId,
    masterItemId,
    name,
    existingJobIds,
    onClose,
}: JaguarLandRoverMenuPricingModalProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { callApi: getHierarchyCall, apiCallStatus: getHierarchyStatus } = useApiCall();
    const [groups, setGroups] = useState<ItemJobGroup[]>([]);
    const [searchText, setSearchText] = useState('');
    const [saving, setSaving] = useState(false);

    const [view, setView] = useState<'groups' | 'search'>('groups');
    const [highlighted, setHighlighted] = useState('');

    useEffect(() => {
        (async () => {
            const result = await getHierarchyCall(
                () => ItemJobsAPI.getJobHierarchy(orderId, name),
                {
                    selectErrorContent: () => ({
                        body: t('toasters.errorOccurredWhenLoading'),
                    }),
                }
            );
            const gs: ItemJobGroup[] = result.groups.map((g) => ({
                ...g,
                operations: g.operations.map((o) => ({
                    ...o,
                    checked: calculateIsChecked(
                        o.jobVariants.flatMap((jv) => jv.jobs.flatMap((j) => j.id))
                    ),
                    open: o.jobVariants
                        .flatMap((jv) => jv.jobs.flatMap((j) => j.id))
                        .some((id) => existingJobIds.includes(id)),
                    setOpen: (open) => setOperationOpen(open, g.id, o.id),
                    searchStrings: [g.description, o.description],
                    setChecked: (checked) => setOperationChecked(checked, g.id, o.id),
                    jobVariants: o.jobVariants.map((jv) => ({
                        ...jv,
                        checked: calculateIsChecked(jv.jobs.flatMap((j) => j.id)),
                        open: jv.jobs
                            .flatMap((j) => j.id)
                            .some((id) => existingJobIds.includes(id)),
                        setOpen: (open) => setJobVariantOpen(open, g.id, o.id, jv.id),
                        searchStrings: [g.description, o.description, jv.description],
                        setChecked: (checked) => setJobVariantChecked(checked, g.id, o.id, jv.id),
                        jobs: jv.jobs.map((j) => ({
                            ...j,
                            checked: existingJobIds.includes(j.id),
                            searchStrings: [
                                g.description,
                                o.description,
                                jv.description,
                                j.description,
                            ],
                            setChecked: (checked) =>
                                setJobChecked(checked, g.id, o.id, jv.id, j.id),
                        })),
                    })),
                })),
            }));
            setGroups(gs);
        })();
    }, [getHierarchyCall, orderId, name, t, existingJobIds]);

    function setOperationChecked(checked: boolean, groupId: string, operationId: string) {
        setGroups((current) =>
            current.map((g) =>
                g.id === groupId
                    ? {
                          ...g,
                          operations: g.operations.map((o) =>
                              o.id === operationId
                                  ? {
                                        ...o,
                                        checked: checked,
                                        jobVariants: o.jobVariants.map((jv) => ({
                                            ...jv,
                                            checked: checked,
                                            jobs: jv.jobs.map((j) => ({
                                                ...j,
                                                checked: checked,
                                            })),
                                        })),
                                    }
                                  : o
                          ),
                      }
                    : g
            )
        );
    }

    function setJobVariantChecked(
        checked: boolean,
        groupId: string,
        operationId: string,
        jobVariantId: string
    ) {
        setGroups((current) =>
            current.map((g) =>
                g.id === groupId
                    ? {
                          ...g,
                          operations: g.operations.map((o) =>
                              o.id === operationId
                                  ? {
                                        ...o,
                                        jobVariants: o.jobVariants.map((jv) =>
                                            jv.id === jobVariantId
                                                ? {
                                                      ...jv,
                                                      checked,
                                                      jobs: jv.jobs.map((j) => ({
                                                          ...j,
                                                          checked: checked,
                                                      })),
                                                  }
                                                : jv
                                        ),
                                        checked:
                                            checked &&
                                            o.jobVariants
                                                .filter((jv) => jv.id !== jobVariantId)
                                                .every((jv) => jv.checked),
                                    }
                                  : o
                          ),
                      }
                    : g
            )
        );
    }

    function setJobChecked(
        checked: boolean,
        groupId: string,
        operationId: string,
        jobVariantId: string,
        jobId: string
    ) {
        setGroups((current) =>
            current.map((g) =>
                g.id === groupId
                    ? {
                          ...g,
                          operations: g.operations.map((o) =>
                              o.id === operationId
                                  ? {
                                        ...o,
                                        jobVariants: o.jobVariants.map((jv) =>
                                            jv.id === jobVariantId
                                                ? {
                                                      ...jv,
                                                      jobs: jv.jobs.map((j) =>
                                                          j.id === jobId
                                                              ? { ...j, checked: checked }
                                                              : j
                                                      ),
                                                      checked:
                                                          checked &&
                                                          jv.jobs
                                                              .filter((j) => j.id !== jobId)
                                                              .every((j) => j.checked),
                                                  }
                                                : jv
                                        ),
                                        checked:
                                            checked &&
                                            o.jobVariants
                                                .flatMap((jv) => jv.jobs)
                                                .filter((j) => j.id !== jobId)
                                                .every((j) => j.checked),
                                    }
                                  : o
                          ),
                      }
                    : g
            )
        );
    }

    function setOperationOpen(open: boolean, groupId: string, operationId: string) {
        setGroups((current) =>
            current.map((g) =>
                g.id === groupId
                    ? {
                          ...g,
                          operations: g.operations.map((o) =>
                              o.id === operationId ? { ...o, open } : o
                          ),
                      }
                    : g
            )
        );
    }

    function setJobVariantOpen(
        open: boolean,
        groupId: string,
        operationId: string,
        jobVariantId: string
    ) {
        setGroups((current) =>
            current.map((g) =>
                g.id === groupId
                    ? {
                          ...g,
                          operations: g.operations.map((o) =>
                              o.id === operationId
                                  ? {
                                        ...o,
                                        jobVariants: o.jobVariants.map((jv) =>
                                            jv.id === jobVariantId ? { ...jv, open } : jv
                                        ),
                                    }
                                  : o
                          ),
                      }
                    : g
            )
        );
    }

    const search = () => {
        if (searchText.length === 0) {
            setView('groups');
            return;
        }
        setView('search');
        setHighlighted(searchText);
    };

    const save = async () => {
        if (saving) return;

        const checkedJobIds = groups.flatMap((g) =>
            g.operations
                .flatMap((o) => o.jobVariants.flatMap((jv) => jv.jobs))
                .filter((j) => j.checked)
                .map((j) => j.id)
        );

        setSaving(true);
        try {
            const result = await dispatch(
                createEstimatesFromJaguarIntegration({
                    masterItemId,
                    repairId,
                    orderId,
                    itemJobIds: checkedJobIds,
                })
            );
            if (!('error' in result)) onClose();
        } finally {
            setSaving(false);
        }
    };

    const addManualEstimate = async () => {
        if (saving) return;

        setSaving(true);
        try {
            const result = await dispatch(addEstimate({ masterItemId, repairOrderId: orderId }));
            if (!('error' in result)) onClose();
        } finally {
            setSaving(false);
        }
    };

    const calculateIsChecked = (jobIds: string[]) => {
        return jobIds.length > 0 && jobIds.every((id) => existingJobIds.includes(id));
    };

    return (
        <Modal open onClose={onClose}>
            <div className={styles.menuPricing}>
                <div className={styles.header}>
                    <span>{t('menuPricing.header')}</span>
                    <div className={styles.closeButton} onClick={onClose}>
                        <CloseIcon fill="#899198" />
                    </div>
                </div>
                {getHierarchyStatus === 'Finished' && groups.length !== 0 && (
                    <>
                        {renderTop()}
                        <GroupsView visible={view === 'groups'} groups={groups} />
                        <SearchView
                            visible={view === 'search'}
                            groups={groups}
                            searchText={highlighted}
                        />
                    </>
                )}
                {getHierarchyStatus === 'Finished' &&
                    groups.length === 0 &&
                    renderVehicleNotFound()}
                {getHierarchyStatus !== 'Finished' && <AreaSpinner />}
            </div>
        </Modal>
    );

    function renderTop() {
        return (
            <div className={styles.top}>
                <TextField
                    cmosVariant="roundedGrey"
                    placeholder={t('menuPricing.search')}
                    name={'search'}
                    value={searchText}
                    endAdornment={
                        <IconButton onClick={search}>
                            <SearchIcon />
                        </IconButton>
                    }
                    inputWrapperClasses={{ self: styles.search }}
                    onEnterPress={search}
                    onChange={(e) => setSearchText(e.target.value)}
                />
                <div className={styles.buttons}>
                    <Button
                        cmosVariant={'stroke'}
                        cmosSize={'medium'}
                        color={Colors.CM1}
                        Icon={PlusIcon}
                        label={t('menuPricing.addManualEstimate')}
                        onClick={addManualEstimate}
                        className={styles.addManualEstimate}
                    />
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={Colors.Neutral3}
                        label={t('commonLabels.cancel')}
                        onClick={onClose}
                        className={styles.cancel}
                    />
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={Colors.Success}
                        label={t('commonLabels.save')}
                        onClick={save}
                        className={styles.save}
                    />
                </div>
            </div>
        );
    }

    function renderVehicleNotFound() {
        return (
            <div className={styles.vehicleNotFound}>
                <div className={styles.vehicleNotFoundCaption}>
                    {t('menuPricing.vehicleNotFound')}
                </div>
                <Button
                    cmosVariant={'stroke'}
                    cmosSize={'medium'}
                    color={Colors.CM1}
                    Icon={PlusIcon}
                    label={t('menuPricing.addManualEstimate')}
                    onClick={addManualEstimate}
                    className={styles.addManualEstimate}
                />
            </div>
        );
    }
};

export default JaguarLandRoverMenuPricingModalV1;
