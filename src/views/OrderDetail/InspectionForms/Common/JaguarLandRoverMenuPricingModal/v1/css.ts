import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    menuPricing: {
        width: 1021,
        height: 624,
        display: 'flex',
        flexDirection: 'column',
        paddingTop: 24,
        paddingBottom: 24,
        paddingLeft: 42,
        paddingRight: 42,
        backgroundColor: theme.palette.neutral[2],
        borderRadius: 20,
    },
    header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...FontPrimary(HeaderStyles.H5_14px, true),
        color: '#000000',
    },
    closeButton: {
        width: 40,
        height: 40,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
    },
    top: {
        display: 'flex',
        justifyContent: 'space-between',
        paddingTop: 16,
        paddingBottom: 32,
    },
    search: {
        width: 312,
    },
    buttons: {
        display: 'flex',
    },
    addManualEstimate: {
        paddingLeft: 16,
        paddingRight: 16,
        width: 'initial',
    },
    cancel: {
        width: 160,
        marginLeft: 24,
        marginRight: 16,
    },
    save: {
        width: 160,
    },
    vehicleNotFound: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        rowGap: 16,
        flexGrow: 1,
    },
    vehicleNotFoundCaption: {
        ...FontSecondary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
    },
    searchIcon: {
        cursor: 'pointer',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
}));
