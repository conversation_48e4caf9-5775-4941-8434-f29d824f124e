import { IntegrationTypes } from 'datacontracts/GlobalSettings/RepairShopSettingsDto';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import JaguarLandRoverMenuPricingModalV1 from './v1';
import JaguarLandRoverMenuPricingModalV2 from './v2';

export interface JaguarLandRoverMenuPricingModalProps {
    orderId: number;
    repairId: number;
    masterItemId: number;
    name: string;
    existingJobIds: string[];
    onClose: () => void;
}

const JaguarLandRoverMenuPricingModal = (props: JaguarLandRoverMenuPricingModalProps) => {
    const isNewVersion = useSelector(selectJlrMenuPricingFlag);

    if (isNewVersion) {
        return <JaguarLandRoverMenuPricingModalV2 {...props} />;
    } else {
        return <JaguarLandRoverMenuPricingModalV1 {...props} />;
    }
};

const selectJlrMenuPricingFlag = createSelector([selectSettings], (settings) => {
    return settings?.repairShopSettings?.integrationType === IntegrationTypes.JaguarLandRoverV2;
});

export default JaguarLandRoverMenuPricingModal;
