import {
    Box,
    CircularProgress,
    InputAdornment,
    ListItemButton,
    autocompleteClasses,
    useTheme,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { MasterItemDto } from 'api/MasterItem';
import clsx from 'clsx';
import { normalizeAccent } from 'common/Helpers';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { TextField } from 'common/components/mui';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { HTMLAttributes, createContext, forwardRef, useContext, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import { useAppDispatch } from 'store';
import {
    OTHER_SYSTEM_ID,
    inspectionFormsActions,
} from 'store/slices/order/orderInspectionFormsSlice';
import { selectMasterItemsToAdd } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import { addItem } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { usePackagePopupContext } from '../../PackagesPopupProvider';
import InspectionItemsModal from './../../NormalView/InspectionItemsModal';

type AddItemProps = {
    className?: string;
    repairOrderId: number;
    systemId: number;
    disabled?: boolean;
};

const ITEM_HEIGHT = 32;

export function AddItem({ className, repairOrderId, systemId, disabled }: AddItemProps) {
    const { t } = useAppTranslation();
    const [isEditMode, setIsEditMode] = useState(false);
    const [showInspectionModal, setShowInspectionModal] = useState(false);
    const masterItems = useSelector(selectMasterItemsToAdd);
    const dispatch = useAppDispatch();
    const theme = useTheme();

    const handleModal = () => {
        setShowInspectionModal(true);
    };

    const handleModalClose = () => {
        setShowInspectionModal(false);
    };

    const handleAddItemClick = () => {
        setIsEditMode(true);
    };

    const packagePopup = usePackagePopupContext();

    const addItemMutation = useMutation(async (item: MasterItemDto | string) => {
        // NOTE (AP) In case of working with Spreadsheet, we can face situation, when
        // we want to create new item, but "Other" system is not defined yet,
        // So lets ensure it already added
        if (systemId === OTHER_SYSTEM_ID) {
            dispatch(inspectionFormsActions.ensureOtherSystemExists());
        }

        let masterItemId: number | null = null;
        let masterItemName: string = '';

        if (typeof item === 'string') {
            masterItemName = item;
        } else {
            masterItemName = item.name;
            masterItemId = item.id;
        }

        const result = await dispatch(
            addItem({
                masterItemId,
                masterItemName,
                repairOrderId: repairOrderId,
                systemId: systemId,
            })
        );
        if (!('error' in result)) {
            setIsEditMode(false);
            if ([PriorityLevel.Urgent, PriorityLevel.Med].includes(result.payload.item.priority)) {
                packagePopup.openIfHasApplicablePackages(result.payload.item.masterItemId);
            }
        }
    });

    if (isEditMode)
        return (
            <Box sx={{ display: 'flex', alignItems: 'center' }} className={className}>
                <Box sx={{ width: 320 }}>
                    <InspectionItemsAutocomplete
                        isLoading={addItemMutation.isLoading}
                        onCreateNew={(name) => {
                            addItemMutation.mutate(name);
                        }}
                        onSelected={(item) => {
                            addItemMutation.mutate(item);
                        }}
                    />
                </Box>
                <Button
                    cmosVariant={'filled'}
                    label={t('inspectionForms.addItem.seeAll')}
                    sx={{ width: 95, marginLeft: '5px' }}
                    onClick={() => handleModal()}
                />
                {showInspectionModal && (
                    <InspectionItemsModal
                        open={showInspectionModal}
                        onClose={handleModalClose}
                        options={masterItems}
                        onSelected={(item) => {
                            if (item) {
                                addItemMutation.mutate(item);
                            }
                        }}
                    />
                )}
                <Button
                    sx={{ marginLeft: '5px' }}
                    cmosVariant={'typography'}
                    cmosSize={'small'}
                    iconPosition="right"
                    Icon={() => <CloseIcon fill={Colors.Neutral6} />}
                    onClick={() => setIsEditMode(false)}
                />
            </Box>
        );

    return (
        <div className={className}>
            <Button
                cmosVariant="stroke"
                disabled={disabled}
                color={theme.palette.primary.main}
                onClick={handleAddItemClick}
            >
                {t('inspectionForms.addItem.addItem')}
                <PlusIcon fill={theme.palette.primary.main} />
            </Button>
        </div>
    );
}

type InspectionItemsAutocompleteProps = {
    onSelected: (masterItem: MasterItemDto) => void;
    onCreateNew: (text: string) => void;
    isLoading?: boolean;
};

function InspectionItemsAutocomplete({
    onSelected,
    onCreateNew,
    isLoading = false,
}: InspectionItemsAutocompleteProps) {
    const masterItems = useSelector(selectMasterItemsToAdd);
    const items: Item[] = useMemo(
        () => masterItems.map((value) => ({ value, type: 'existing' })),
        [masterItems]
    );

    return (
        <SAutocomplete<Item>
            ListboxComponent={VirtualizedListboxComponent}
            disableListWrap
            selectOnFocus
            clearOnBlur
            handleHomeEndKeys
            sx={{
                [`& .${autocompleteClasses.input}`]: {
                    padding: '1px 8px !important',
                },
            }}
            renderOption={(props, item) => ({ props, item } as never as React.ReactNode)}
            getOptionLabel={(option) =>
                option.type === 'existing' ? option.value.name : option.text
            }
            renderTags={(value) =>
                value.map((x) => (x.type === 'existing' ? x.value.name : x.text)).join(', ')
            }
            options={items}
            filterOptions={(options, state) => {
                const normalizedQuery = normalizeAccent(state.inputValue.trim());
                if (normalizedQuery === '') return options;
                const results = options.filter(
                    (x) =>
                        x &&
                        normalizeAccent(x.type === 'existing' ? x.value.name : x.text).includes(
                            normalizedQuery
                        )
                );

                if (results.length === 0) {
                    return [
                        {
                            type: 'addNew',
                            text: state.inputValue,
                        },
                    ];
                }

                return results;
            }}
            onChange={(_event, value) => {
                if (value.type === 'existing') {
                    onSelected(value.value);
                } else {
                    onCreateNew(value.text);
                }
            }}
            value={null}
            renderInput={({ InputProps, ...params }) => (
                <TextField
                    {...params}
                    InputProps={{
                        ...InputProps,
                        endAdornment: isLoading ? (
                            <InputAdornment position="end">
                                <CircularProgress size={16} thickness={4} />
                            </InputAdornment>
                        ) : undefined,
                    }}
                    cmosVariant="roundedPrimary"
                />
            )}
            disabled={isLoading}
        />
    );
}

const OuterElementContext = createContext<HTMLAttributes<HTMLDivElement>>({});

const OuterElementType = forwardRef<HTMLDivElement>((props, ref) => {
    const { className, ...outerProps } = useContext(OuterElementContext);

    return (
        <div ref={ref} {...props} {...outerProps} className={clsx(className, 'custom-scrollbar')} />
    );
});

type Item =
    | {
          type: 'existing';
          value: MasterItemDto;
      }
    | {
          type: 'addNew';
          text: string;
      };
type ListboxComponentProps = React.HTMLAttributes<HTMLElement>;
type VirtualListData = { props: HTMLAttributes<HTMLLIElement>; item: Item }[];

const VirtualizedListboxComponent = forwardRef<HTMLDivElement, ListboxComponentProps>(
    ({ children, role, ...other }, ref) => {
        const items = children as never as VirtualListData;
        const height = Math.max(50, Math.min(items.length, 10) * ITEM_HEIGHT);

        return (
            <div ref={ref} style={{ padding: '10px 0' }}>
                <OuterElementContext.Provider value={other}>
                    <FixedSizeList
                        itemSize={ITEM_HEIGHT}
                        width="100%"
                        height={height}
                        itemCount={items.length}
                        overscanCount={5}
                        itemData={items}
                        outerElementType={OuterElementType}
                    >
                        {InspectionItemVirtualizedRenderer}
                    </FixedSizeList>
                </OuterElementContext.Provider>
            </div>
        );
    }
);

function InspectionItemVirtualizedRenderer({
    style,
    index,
    data,
}: ListChildComponentProps<VirtualListData>) {
    const { props, item } = data[index];

    return (
        <div style={style}>
            <ListItemButton sx={{ height: 32 }} role="button" component="li" {...props}>
                {item.type === 'existing' ? item.value.name : <>Add "{item.text}"</>}
            </ListItemButton>
        </div>
    );
}
