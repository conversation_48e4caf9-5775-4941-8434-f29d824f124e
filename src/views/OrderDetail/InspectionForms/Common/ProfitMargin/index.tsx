import { useMemo } from 'react';
import { useProfitMarginStyles } from './css';

export interface ProfirMarginProps {
    cost: number | null;
    price: number | null;
}

export const ProfitMargin = ({ cost, price }: ProfirMarginProps) => {
    const classes = useProfitMarginStyles();

    const percentage = useMemo((): string => {
        if (cost === null || price === null) return '';

        if (cost > price) return '| 0%';

        const percent = Math.round(((price - cost) / price) * 100);

        return `| ${percent}%`;
    }, [cost, price]);

    return (
        <div className={classes.container}>
            <div className={classes.percentage}>{percentage}</div>
        </div>
    );
};
