import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useProfitMarginStyles = makeStyles((theme) => ({
    container: {
        paddingRight: 5,
        height: 14.52,
        display: 'flex',
        alignItems: 'center',
        minWidth: 'fit-content',
    },
    percentage: {
        ...FontPrimary(HeaderStyles.H10_8px, false, theme.palette.neutral[6]),
    },
}));
