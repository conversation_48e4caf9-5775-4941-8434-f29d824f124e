import { CircularProgress, IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useSelector } from 'react-redux';
import { EnterpriseOrdersApi } from '../../../../api/enterprise/orders';
import OrdersApi from '../../../../api/orders';
import { ExcelIcon } from '../../../../common/components/Icons/ExcelIcon';
import Tooltip from '../../../../common/components/Tooltip';
import { useAppTranslation } from '../../../../common/hooks/useAppTranslation';
import useToasters from '../../../../common/hooks/useToasters';
import { selectIsEnterprise } from '../../../../store/slices/globalSettingsSlice';
import theme from '../../../../theme';

type DownloadButtonProps = {
    orderId: number;
};

export default function DownloadEstimatesExcelButton({ orderId }: DownloadButtonProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const isEnterprise = useSelector(selectIsEnterprise);

    const downloadExcelMutation = useMutation(
        async () => {
            const { fileName, excelFile } = isEnterprise
                ? await EnterpriseOrdersApi.orders.getOrderEstimatesExcel(orderId)
                : await OrdersApi.getOrderEstimatesExcel(orderId);

            const byteCharacters = atob(excelFile);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });

            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        },
        {
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const handleDownload = () => {
        downloadExcelMutation.mutate();
    };

    return (
        <Tooltip content={t('orderDetails.inspectionForms.downloadEstimatesExcel')}>
            <IconButton
                onClick={handleDownload}
                size="large"
                disabled={downloadExcelMutation.isLoading}
            >
                {downloadExcelMutation.isLoading ? (
                    <CircularProgress size={24} thickness={5} />
                ) : (
                    <ExcelIcon size={20} fill={theme.palette.primary.light} />
                )}
            </IconButton>
        </Tooltip>
    );
}
