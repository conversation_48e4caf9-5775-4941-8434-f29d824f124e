import { IconButton, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { AddMenuIcon } from 'common/components/Icons/AddMenuIcon';
import { ImageIcon } from 'common/components/Icons/ImageIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import InfoTooltip from 'common/components/InfoTooltip';
import ArrowTooltip from 'common/components/Tooltip';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import isEqual from 'lodash/isEqual';
import moment from 'moment';
import { memo, useCallback, useState } from 'react';
import { createSelector } from 'reselect';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import {
    selectIsOrderClosed,
    selectOrderDiscountType,
    selectOrderSaving,
} from 'store/slices/order/orderDetails';
import {
    selectColumns,
    selectEstimateById,
    selectEstimatesSet,
    selectItemById,
} from 'store/slices/order/orderInspectionFormsSlice/selectors';
import { addEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { changePriority } from 'store/slices/order/orderInspectionFormsSlice/thunks/changePriority';
import { selectUserPermission } from 'store/slices/user';
import ApproveDeclineActionsView from '../../Common/ApproveDeclineActionsView';
import EvidenceModal from '../../Common/EvidenceModal';
import JaguarLandRoverMenuPricingModal from '../../Common/JaguarLandRoverMenuPricingModal';
import OpenAPIMenuPricingModal from '../../Common/OpenAPIMenuPricingModal';
import Priority from '../../NormalView/Priority';
import { usePackagePopupContext } from '../../PackagesPopupProvider';
import CellRenderer from '../CellRenderer';
import CommentsModal from '../CommentsModal';
import CopyTooltip from '../CopyTooltip';
import DiscountCell from '../DiscountCell';
import OpenCommentsTooltip from '../OpenCommentsTooltip';
import { ReadonlyCell } from '../ReadonlyCell';
import {
    getColumnWidthVariableName,
    PREDEFINED_COLUMN_IDS,
    useSpreadsheetColumn,
} from '../Spreadsheet/layout';
import { useIsSpreadsheetReadonly } from '../context';
import { getFormattedSubtotal } from './helper';

type ItemRowProps = {
    rowNo: number;
    orderId: number;
    itemId: number;
    estimateId: number | null;
    showAddIcon: boolean;
};

const ButtonContainer = styled('div')(({ theme }) => ({
    position: 'absolute',
    bottom: '0',
    right: '0',
    display: 'flex',
    alignItems: 'center',
    padding: '0 12px 0 10px',
    backgroundImage: `linear-gradient(to right, transparent, ${theme.palette.neutral[2]} 25%)`,

    '& button': {
        width: '24px',
        height: '24px',
        padding: '0 !important',
    },
    '& div': {
        marginLeft: '3px',
    },
}));

const selectData = createSelector(
    [
        selectEstimatesSet,
        selectSettings,
        (r, p: { itemId: number; estimateId: number | null }) => ({
            estimate: p.estimateId ? selectEstimateById(r, p.estimateId) : null,
            item: selectItemById(r, p.itemId),
        }),
        selectColumns,
        selectOrderDiscountType,
    ],
    (estimates, settings, { item, estimate }, columns, discountType) => {
        const { internationalization, repairShopSettings } = settings;
        const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;

        return {
            jobIds: (item.estimates ?? [])
                .map((id) => estimates[id])
                .filter((estimate) => estimate.isSubItem && estimate.jobId != null)
                .map((si) => si.jobId)
                .filter((x: string | null): x is string => !!x),
            formattedSubtotal: getFormattedSubtotal(
                internationalization,
                discountType,
                estimate,
                requireDecimals
            ),
            itemJobsFeature: settings.repairShopSettings?.features.itemJobs,
            openApiItemJobs: settings.repairShopSettings?.features.itemJobsFromOpenApi,
            enableEstimateIntegration:
                settings.repairShopSettings?.features.enableEstimateIntegration,
            item,
            estimate,
            columns,
        };
    }
);

const ItemRow = ({ rowNo, orderId, itemId, estimateId, showAddIcon }: ItemRowProps) => {
    const readOnly = useIsSpreadsheetReadonly();
    const { allowEditEstimates } = useAppSelector(selectUserPermission);

    const orderSaving = useAppSelector(selectOrderSaving);
    const selector = useCallback(
        (r: RootState) => selectData(r, { estimateId, itemId }),
        [itemId, estimateId]
    );
    const {
        openApiItemJobs,
        itemJobsFeature,
        formattedSubtotal,
        jobIds,
        enableEstimateIntegration,
        item,
        estimate,
        columns: { parts, labor },
    } = useAppSelector(selector, isEqual);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const dispatch = useAppDispatch();

    const [isEvidenceOpen, setIsEvidenceOpen] = useState(false);
    const [isCommentsOpen, toggleComments] = useState(false);
    const [isMenuPricingOpen, setIsMenuPricingOpen] = useState(false);

    const { t } = useAppTranslation();

    const handleOnAdd = useCallback(() => {
        dispatch(addEstimate({ masterItemId: itemId, repairOrderId: orderId }));
    }, [dispatch, itemId, orderId]);

    const openCommentsModal = useCallback(() => toggleComments(true), []);
    const closeCommentsModal = useCallback(() => toggleComments(false), []);

    const packagePopup = usePackagePopupContext();

    const handlePriorityChange = (newPriority: PriorityLevel) => {
        dispatch(
            changePriority({ repairOrderId: orderId, masterItemId: itemId, priority: newPriority })
        ).then(() => {
            if (
                (item.priority === PriorityLevel.NA || item.priority === PriorityLevel.Low) &&
                (newPriority === PriorityLevel.Urgent || newPriority === PriorityLevel.Med)
            ) {
                packagePopup.openIfHasApplicablePackages(item.masterItemId);
            }
        });
    };

    const addButton = (() => {
        if (itemJobsFeature)
            return (
                <IconButton
                    size="small"
                    disabled={readOnly || orderIsClosed}
                    onClick={() => setIsMenuPricingOpen(true)}
                >
                    <AddMenuIcon />
                </IconButton>
            );
        if (showAddIcon && !enableEstimateIntegration)
            return (
                <IconButton size="small" disabled={readOnly || orderIsClosed} onClick={handleOnAdd}>
                    <PlusIcon />
                </IconButton>
            );
        return null;
    })();

    return (
        <>
            <DivRow className={readOnly || orderIsClosed ? 'disabled' : undefined}>
                <ItemDefContainer>
                    <ItemDefCell rowNo={rowNo} colNo={0}>
                        {addButton}
                    </ItemDefCell>
                    <ItemDefCell rowNo={rowNo} colNo={1}>
                        <Priority
                            disabled={!allowEditEstimates || readOnly || orderIsClosed}
                            priority={item.priority}
                            onChange={handlePriorityChange}
                        />
                    </ItemDefCell>
                    <ItemDefCell rowNo={rowNo} colNo={2}>
                        <Button
                            cmosVariant={'typography'}
                            cmosSize={'large'}
                            disabled={!item.hasMedia || orderIsClosed}
                            color={Colors.CM1}
                            Icon={ImageIcon}
                            onClick={() => setIsEvidenceOpen(true)}
                        />
                    </ItemDefCell>
                    <ItemDefCell sx={{ width: 'initial' }} rowNo={rowNo} colNo={3}>
                        <DivItemName>
                            <ArrowTooltip
                                content={
                                    item.campaignId
                                        ? `${t('inspectionForms.spreadsheetView.campaign')} - ${
                                              item.name
                                          }`
                                        : item.name
                                }
                                position="top-start"
                            >
                                <SpanItemName>
                                    {item.campaignId
                                        ? `${t('inspectionForms.spreadsheetView.campaign')} - `
                                        : ''}
                                    {item.name}
                                </SpanItemName>
                            </ArrowTooltip>
                            <ButtonContainer className="buttonContainer">
                                <CopyTooltip
                                    text={item.name}
                                    tooltipText={t('orderDetails.tooltipCopy')}
                                />
                                {/* We can't create comment for an estimate without id */}
                                {estimateId && (
                                    <CommentsModal
                                        open={isCommentsOpen}
                                        target={(ref) => (
                                            <OpenCommentsTooltip
                                                disabled={readOnly || orderIsClosed}
                                                ref={ref}
                                                onClick={openCommentsModal}
                                                tooltipText={t('orderDetails.tooltipComments')}
                                            />
                                        )}
                                        onClose={closeCommentsModal}
                                        itemEstimateId={estimateId}
                                    />
                                )}
                                {estimate?.estimateUpdateTime && (
                                    <InfoTooltip
                                        text={`${t('orderDetails.estimationDate')}: ${moment(
                                            estimate.estimateUpdateTime
                                        ).format(t('dateFormats.longDate'))}`}
                                    />
                                )}
                            </ButtonContainer>
                        </DivItemName>
                    </ItemDefCell>
                </ItemDefContainer>

                {parts.map((field, idx) => (
                    <CellRenderer
                        field={field}
                        itemId={itemId}
                        estimateId={estimateId}
                        rowNo={rowNo}
                        colNo={4 + idx}
                        key={field.id}
                        disabled={orderIsClosed}
                    />
                ))}
                {labor.map((field, idx) => (
                    <CellRenderer
                        rowNo={rowNo}
                        colNo={4 + idx + parts.length}
                        key={field.id}
                        field={field}
                        itemId={itemId}
                        estimateId={estimateId}
                        disabled={orderIsClosed}
                    />
                ))}
                <DiscountCell
                    rowNo={rowNo}
                    colNo={4 + labor.length + parts.length}
                    masterItemId={itemId}
                    estimateId={estimateId}
                    disabled={orderIsClosed}
                />
                <SubtotalCell rowNo={rowNo} colNo={5 + labor.length + parts.length}>
                    <b>{formattedSubtotal}</b>
                </SubtotalCell>
                <ApproveDeclineCell rowNo={rowNo} colNo={6 + labor.length + parts.length}>
                    <ApproveDeclineActionsView
                        disabled={!allowEditEstimates || orderIsClosed}
                        repairId={item.repairId}
                        masterItemId={item.masterItemId}
                        repairOrderId={orderId}
                    />
                </ApproveDeclineCell>
            </DivRow>
            {isEvidenceOpen && (
                <EvidenceModal
                    onClose={() => setIsEvidenceOpen(false)}
                    repairId={item.repairId}
                    masterItemId={item.masterItemId}
                    repairOrderId={orderId}
                />
            )}
            {isMenuPricingOpen && !orderSaving && (
                <>
                    {openApiItemJobs ? (
                        <OpenAPIMenuPricingModal
                            repairOrderId={orderId}
                            repairId={item.repairId}
                            masterItemId={item.masterItemId}
                            onClose={() => setIsMenuPricingOpen(false)}
                        />
                    ) : (
                        <JaguarLandRoverMenuPricingModal
                            orderId={orderId}
                            repairId={item.repairId}
                            masterItemId={item.masterItemId}
                            name={item.name}
                            existingJobIds={jobIds}
                            onClose={() => setIsMenuPricingOpen(false)}
                        />
                    )}
                </>
            )}
        </>
    );
};

export default memo(ItemRow);

const DivRow = styled('div')(({ theme }) => ({
    display: 'flex',
    height: 40,
    backgroundColor: theme.palette.neutral[1],

    '& .Spreadsheet-cell': {
        backgroundColor: theme.palette.neutral[1],
    },

    '&:not(.disabled):hover .Spreadsheet-cell': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

const ItemDefCell = styled(ReadonlyCell)(({ theme }) => ({
    background: theme.palette.neutral[2],
    width: 40,
    minWidth: 40,
}));

function ItemDefContainer({ children }: { children: React.ReactNode }) {
    const { fixed, style } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems);

    return (
        <DivItemDefContainer
            style={style}
            sx={fixed ? { left: 0, position: 'sticky', zIndex: 5 } : undefined}
        >
            {children}
        </DivItemDefContainer>
    );
}

const DivItemDefContainer = styled('div')({
    width: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems)})`,
    minWidth: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems)})`,
    display: 'flex',

    '& > :last-child': {
        flexBasis: '100%',
    },
});

const DivItemName = styled('div')(({ theme }) => ({
    display: 'block',
    width: '100%',
    padding: '0px 12px',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    boxSizing: 'border-box',
    position: 'relative',
    textAlign: 'justify',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',

    '&:not(:hover) .buttonContainer': {
        visibility: 'hidden',
    },
}));

const SpanItemName = styled('span')({
    paddingRight: 12,
    textAlign: 'left',
    flexGrow: 1,
    margin: '5px 0px',
    wordBreak: 'break-all',
    lineHeight: '20px',
});

function SubtotalCell({
    children,
    rowNo,
    colNo,
}: {
    children: React.ReactNode;
    rowNo: number;
    colNo: number;
}) {
    const { style, fixed } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.subtotal);

    return (
        <StyledSubtotalCell
            rowNo={rowNo}
            colNo={colNo}
            style={style}
            className={fixed ? 'SubtotalCell-fixed' : undefined}
        >
            {children}
        </StyledSubtotalCell>
    );
}

const StyledSubtotalCell = styled(ReadonlyCell)({
    '&.SubtotalCell-fixed': {
        position: 'sticky',
        right: 0,
        zIndex: 1,
    },

    '& > div': {
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const ApproveDeclineCell = styled(ReadonlyCell)({
    width: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.approveDecline)})`,
    minWidth: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.approveDecline)})`,
    flexBasis: '100%',

    '& > div': {
        justifyContent: 'center',
    },
});
