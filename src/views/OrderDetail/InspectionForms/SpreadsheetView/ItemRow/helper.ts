import { DiscountType } from 'api/settings/OrdersSettings';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { EstimateDto } from 'datacontracts/InspectionForms/EstimateDto';
import { InternationalizationDto } from 'datacontracts/Interationalization/InternationalizationDto';
import { EstimateData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { calculateSubtotal } from '../../helper';

export type EditableEstimatePart = Pick<
    EstimateDto,
    | 'partsNumber'
    | 'quantity'
    | 'availability'
    | 'partUnitCost'
    | 'partUnitPrice'
    | 'hours'
    | 'hourUnitPrice'
>;

export const getEmptyEstimate = (repairId: number): EstimateData => ({
    estimateId: 0,
    repairId: repairId,
    name: '',
    isSubItem: false,
    itemJobId: null,
    itemJobDescription: null,
    partsNumber: null,
    quantity: null,
    availability: null,
    partUnitCost: null,
    partUnitPrice: null,
    hours: null,
    hourUnitPrice: null,
    total: 0,
    jobId: null,
    estimateUpdateTime: null,
    discount: null,
});

export const getFormattedSubtotal = (
    internationalization: InternationalizationDto,
    discountType: DiscountType | undefined,
    estimate: EstimateData | null,
    requireDecimals?: boolean
): string => {
    const formattedResult = InternationalizationLogic.numberToCurrency(
        internationalization,
        estimate ? calculateSubtotal(estimate, requireDecimals, discountType) : 0,
        {
            allowZero: true,
            requireDecimals: requireDecimals !== undefined ? requireDecimals : true,
        }
    );

    return formattedResult;
};
