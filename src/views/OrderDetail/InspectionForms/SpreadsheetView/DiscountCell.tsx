import { CSSObject } from '@emotion/react';
import useToasters from 'common/hooks/useToasters';
import isEqual from 'lodash/isEqual';
import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectOrderDiscountType } from 'store/slices/order/orderDetails';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import useSpreadsheet, { useIsSpreadsheetReadonly } from './context';
import { NumberInput } from './NumberInput';
import { SelectableCell } from './SelectableCell';
import {
    getColumnWidthVariableName,
    PREDEFINED_COLUMN_IDS,
    useSpreadsheetColumn,
} from './Spreadsheet/layout';

type DiscountCellProps = {
    rowNo: number;
    colNo: number;
    estimateId: number | null;
    masterItemId: number;
    className?: string;
    rightAdornment?: React.ReactNode;
    disabled: boolean;
};

const selectData = (r: RootState, estimateId: number | null) => {
    if (estimateId === null) return { name: '', lastEdited: null };
    const estimate = selectEstimateById(r, estimateId);
    return {
        discount: estimate.discount,
    };
};

export default function DiscountCell({
    colNo,
    rowNo,
    estimateId,
    masterItemId,
    disabled,
}: DiscountCellProps) {
    const { fixed: discountFixed, style } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.discount);
    const { fixed: subtotalFixed } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.subtotal);

    const readOnly = useIsSpreadsheetReadonly();
    const spreadsheet = useSpreadsheet();
    const selector = useCallback((r: RootState) => selectData(r, estimateId), [estimateId]);
    const { discount } = useAppSelector(selector, isEqual);
    const discountType = useAppSelector(selectOrderDiscountType);
    const { repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;
    const toaster = useToasters();
    const [value, setValue] = useState(discount);

    useEffect(() => {
        setValue(discount);
    }, [discount]);

    function getCellTemplate() {
        if (discountType === 'Percentage') return '{0}%';
        // eslint-disable-next-line no-template-curly-in-string
        else if (discountType === 'Currency') return '${0}';
        else toaster.danger('Invalid discount type', 'Error');
    }

    const changeDiscount = (newValue: number | null) => {
        if (newValue) {
            if (newValue < 0) {
                newValue = 0;
            } else if (discountType === 'Percentage' && newValue > 100) {
                newValue = 100;
            }
        }
        setValue(newValue);
    };

    return (
        <SelectableCell
            style={style}
            sx={getDiscountStyle(discountFixed, subtotalFixed)}
            rowNo={rowNo}
            colNo={colNo}
            disabled={disabled || readOnly}
            onClear={(saveImmediately) => {
                if (discount !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { discount: null });
                setValue(null);
            }}
        >
            {({ ref, onBlur, onFocus, ...props }) => (
                <NumberInput
                    {...props}
                    onBlur={() => {
                        if (discount !== value)
                            spreadsheet.updateEstimate(estimateId, masterItemId, {
                                discount: value,
                            });
                    }}
                    onChange={(e) => changeDiscount(e)}
                    value={value === undefined ? null : value}
                    template={getCellTemplate()}
                    ref={ref}
                    onFocus={onFocus}
                    decimalScale={discountType === 'Currency' ? (requireDecimals ? 2 : 0) : 2}
                    fixedDecimalScale={requireDecimals}
                />
            )}
        </SelectableCell>
    );
}

// this will break if we try to re-order the columns
function getDiscountStyle(discountFixed: boolean, subtotalFixed: boolean): CSSObject {
    if (!discountFixed) return {};

    const style: CSSObject = {
        position: 'sticky',
        right: '0px',
        zIndex: 1,

        '::before': {
            display: 'block',
            content: '" "',
            position: 'absolute',
            left: -1,
            top: 0,
            width: '1px',
            backgroundColor: 'var(--neutral3)',
            height: '100%',
        },
    };

    if (subtotalFixed) {
        style.right = `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.subtotal)})`;
    }

    return style;
}
