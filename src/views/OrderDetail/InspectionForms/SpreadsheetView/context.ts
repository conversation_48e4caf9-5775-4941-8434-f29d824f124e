import { createContext, useContext } from 'react';
import { SaveEstimateArg } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { SetColumnValueArg } from 'store/slices/order/orderInspectionFormsSlice/thunks/setColumnValue';

export interface ISpreadsheetContext {
    updateEstimate(
        estimateId: number | null,
        masterItemId: number,
        update: Omit<SaveEstimateArg, 'estimateId' | 'masterItemId'>,
        updateCustomColumns?: SetColumnValueArg[]
    ): Promise<void>;
}

export const SpreadsheetContext = createContext<ISpreadsheetContext | null>(null);

export default function useSpreadsheet() {
    const ctx = useContext(SpreadsheetContext);
    if (!ctx) throw new Error('SpreadsheetContext is not available');
    return ctx;
}

export const SpreadsheetReadonly = createContext(false);

export function useIsSpreadsheetReadonly() {
    return useContext(SpreadsheetReadonly);
}
