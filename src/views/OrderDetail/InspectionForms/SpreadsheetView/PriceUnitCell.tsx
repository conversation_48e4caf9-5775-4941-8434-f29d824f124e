import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import CurrencyCell from './CurrencyCell';
import useSpreadsheet from './context';

type PriceUnitCellProps = {
    estimateId: number | null;
    fieldName: string;
    masterItemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectValue = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.partUnitPrice : null;

const selectProfitMargin = (r: RootState, id: number | null, priceValue: number | null) => {
    if (!id) return null;
    const settings = selectSettings(r);
    const taxPercentage = settings.repairShopSettings?.taxPercentage;
    if (typeof taxPercentage !== 'number') return null;
    const estimate = selectEstimateById(r, id);
    if (estimate.partUnitCost && typeof priceValue === 'number' && estimate.partUnitCost > 0) {
        return Math.max(0, ((priceValue - estimate.partUnitCost) / priceValue) * 100);
    }
    return null;
};

export default function PriceUnitCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: PriceUnitCellProps) {
    const priceUnit = useAppSelector((r) => selectValue(r, estimateId));
    const spreadsheet = useSpreadsheet();
    const [value, setValue] = useState<number | null>(priceUnit ?? null);
    const profitMargin = useAppSelector((r) => selectProfitMargin(r, estimateId, value));

    useEffect(() => {
        setValue(priceUnit ?? null);
    }, [priceUnit]);

    const { repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;

    return (
        <CurrencyCell
            estimateId={estimateId}
            value={value}
            onChange={setValue}
            percentage={profitMargin}
            onBlur={() => {
                if (value !== priceUnit)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { partUnitPrice: value });
            }}
            onClear={(saveImmediately) => {
                setValue(null);
                if (priceUnit !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { partUnitPrice: null });
            }}
            decimalScale={requireDecimals ? 2 : 0}
            disabled={disabled}
            {...props}
        />
    );
}
