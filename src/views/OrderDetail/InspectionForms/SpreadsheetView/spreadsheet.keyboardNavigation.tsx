import { NavigationDirection } from 'store/slices/order/spreadsheet/types';

interface INavigationContext {
    isInputFocused: boolean;
    isInputEditable: boolean;
    keyDownEvent: React.KeyboardEvent<HTMLDivElement>;
}

type NavigationResult = {
    direction: NavigationDirection | null;
    focusInput: boolean;
    clearInput: boolean;
    clearInputSaveImmidiately: boolean;
};

export function processKeyboardNavigation(ctx: INavigationContext): NavigationResult {
    const { isInputFocused, isInputEditable, keyDownEvent } = ctx;
    const { key: keyPressed } = keyDownEvent;

    const result: NavigationResult = {
        direction: null,
        focusInput: false,
        clearInput: false,
        clearInputSaveImmidiately: false,
    };

    const KEY_REGEX = /^[\d\p{L}_\- +=[\](){}]{1}$/u;

    if (keyPressed === 'Tab') {
        if (keyDownEvent.shiftKey) return result;
        keyDownEvent.preventDefault();
        result.direction = keyDownEvent.shiftKey ? 'GoLeft' : 'GoRight';
    } else if (isInputFocused) {
        if (keyPressed === 'Enter') {
            result.direction = 'GoBottom';
        }
    } else if (isInputEditable) {
        if (keyPressed.match(KEY_REGEX)) {
            result.focusInput = true;
            result.clearInput = true;
        } else if (keyPressed === 'Delete') {
            result.clearInput = true;
            result.clearInputSaveImmidiately = true;
        } else if (keyPressed === 'Enter') result.focusInput = true;
        else if (keyPressed === 'ArrowLeft') result.direction = 'GoLeft';
        else if (keyPressed === 'ArrowRight') result.direction = 'GoRight';
        else if (keyPressed === 'ArrowUp') result.direction = 'GoTop';
        else if (keyPressed === 'ArrowDown') result.direction = 'GoBottom';
    } else {
        if (keyPressed === 'Enter') result.focusInput = true;
        else if (keyPressed === 'ArrowLeft') result.direction = 'GoLeft';
        else if (keyPressed === 'ArrowRight') result.direction = 'GoRight';
        else if (keyPressed === 'ArrowUp') result.direction = 'GoTop';
        else if (keyPressed === 'ArrowDown') result.direction = 'GoBottom';
    }

    return result;
}
