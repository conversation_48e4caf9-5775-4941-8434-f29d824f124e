import { NumberInput } from './NumberInput';
import { SelectableCell, SelectableCellProps } from './SelectableCell';

export type NumericCellProps = Omit<SelectableCellProps, 'children'> & {
    estimateId: number | null;
    fieldName: string;
    value: number | null;
    onChange: (value: number | null) => void;
    template?: string;
    onBlur: React.FocusEventHandler;
    decimalScale?: number;
    rightAdornment?: React.ReactNode;
};

export default function NumericCell({
    value,
    onChange,
    onBlur: onBlur0,
    template,
    decimalScale = 0,
    ...props
}: NumericCellProps) {
    return (
        <SelectableCell {...props}>
            {({ ref, onBlur, onFocus, ...props }) => (
                <NumberInput
                    {...props}
                    onChange={onChange}
                    value={value}
                    template={template}
                    ref={ref}
                    onBlur={(e) => {
                        onBlur(e);
                        onBlur0(e);
                    }}
                    onFocus={onFocus}
                    fixedDecimalScale={decimalScale > 0}
                    decimalScale={decimalScale > 0 ? decimalScale : 0}
                />
            )}
        </SelectableCell>
    );
}
