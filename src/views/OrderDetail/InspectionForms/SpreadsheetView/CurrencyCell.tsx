import { styled } from '@mui/material';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import NumericCell, { NumericCellProps } from './NumericCell';

type CurrencyCellProps = Omit<NumericCellProps, 'template' | 'rightAdornment'> & {
    percentage?: number | null;
};

const selectCurrencyTemplate = createSelector(
    selectSettings,
    (s) => s.internationalization.currency
);

export default function CurrencyCell({ percentage, ...props }: CurrencyCellProps) {
    const currencyTemplate = useSelector(selectCurrencyTemplate);

    return (
        <NumericCell
            rightAdornment={
                typeof percentage !== 'number' ? undefined : (
                    <SpanPercentage>|&nbsp;{roundPercentage(percentage)}%</SpanPercentage>
                )
            }
            {...props}
            template={currencyTemplate}
            decimalScale={props.decimalScale ?? 2}
        />
    );
}

const SpanPercentage = styled('span')({
    whiteSpace: 'nowrap',
});

function roundPercentage(percentage: number) {
    if (percentage > 20) return Math.round(percentage);
    return Math.round(percentage * 10) / 10;
}
