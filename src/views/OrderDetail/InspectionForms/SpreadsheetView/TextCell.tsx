import { SelectableCell, SelectableCellProps } from './SelectableCell';
import TextInput from './TextInput';

export type TextCellProps = Omit<SelectableCellProps, 'children'> & {
    fieldName: string;
    estimateId: number | null;
    value: string;
    onChange: (value: string) => void;
    onBlur: React.FocusEventHandler;
};

export default function TextCell({ value, onChange, onBlur: onBlur2, ...props }: TextCellProps) {
    return (
        <SelectableCell {...props}>
            {({ onBlur, ...props }) => (
                <TextInput
                    {...props}
                    onChange={(e) => onChange((e.target as HTMLInputElement).value ?? '')}
                    value={value ?? ''}
                    onBlur={(e) => {
                        onBlur(e);
                        onBlur2(e);
                    }}
                />
            )}
        </SelectableCell>
    );
}
