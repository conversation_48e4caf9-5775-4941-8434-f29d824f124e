import { FieldType, PREDEFINED_FIELDS } from 'api/fields';
import { ComponentType, CSSProperties } from 'react';
import AvailabilityCell from './AvailabilityCell';
import CostUnitCell from './CostUnitCell';
import CustomCell from './CustomCell';
import HourPriceCell from './HourPriceCell';
import HoursCell from './HoursCell';
import PartNumberCell from './PartNumberCell';
import PriceUnitCell from './PriceUnitCell';
import QuantityCell from './QuantityCell';
import { useSpreadsheetColumn } from './Spreadsheet/layout';

type CellRendererProps = {
    field: { id: string; name: string; type: FieldType };
    estimateId: number | null;
    itemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const PREDEFINED_FIELD: Record<
    string,
    ComponentType<{
        estimateId: number | null;
        fieldName: string;
        masterItemId: number;
        rowNo: number;
        colNo: number;
        style: CSSProperties;
        disabled: boolean;
    }>
> = {
    [PREDEFINED_FIELDS.PARTS_AVAIL]: AvailabilityCell,
    [PREDEFINED_FIELDS.PARTS_QTY]: QuantityCell,
    [PREDEFINED_FIELDS.PARTS_NUMBER]: PartNumberCell,
    [PREDEFINED_FIELDS.PARTS_COST_UNIT]: CostUnitCell,
    [PREDEFINED_FIELDS.PARTS_PRICE_UNIT]: PriceUnitCell,

    [PREDEFINED_FIELDS.LABOR_HRS]: HoursCell,
    [PREDEFINED_FIELDS.LABOR_PRICE_HR]: HourPriceCell,
};

export default function CellRenderer({
    field,
    estimateId,
    itemId,
    rowNo,
    colNo,
    disabled,
}: CellRendererProps) {
    const { style } = useSpreadsheetColumn(field.id);

    if (field.type === 'Predefined' || field.type === 'PredefinedAdditionalPosition') {
        const Component = PREDEFINED_FIELD[field.name];
        if (Component)
            return (
                <Component
                    masterItemId={itemId}
                    fieldName={field.name}
                    rowNo={rowNo}
                    colNo={colNo}
                    estimateId={estimateId}
                    style={style}
                    disabled={disabled}
                />
            );

        return <div />;
    }

    return (
        <CustomCell
            itemId={itemId}
            estimateId={estimateId}
            type={field.type}
            rowNo={rowNo}
            colNo={colNo}
            fieldId={field.id}
            style={style}
            fieldName={field.name}
            disabled={disabled}
        />
    );
}
