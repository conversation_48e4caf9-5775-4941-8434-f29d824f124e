import { styled, SxProps } from '@mui/material';
import clsx from 'clsx';
import { CSSProperties, ReactNode, useEffect } from 'react';
import { useAppDispatch } from 'store';
import { spreadsheetActions } from 'store/slices/order/spreadsheet/index';

export type ReadonlyCellProps = {
    rowNo: number;
    colNo: number;
    children?: ReactNode;
    className?: string;
    style?: CSSProperties;
    sx?: SxProps;
};

export function ReadonlyCell({ style, sx, rowNo, colNo, children, className }: ReadonlyCellProps) {
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(spreadsheetActions.setupReadonlyCell({ x: colNo, y: rowNo }));
    }, [dispatch, colNo, rowNo]);

    return (
        <DivRoot
            className={clsx(className, 'Spreadsheet-cell', 'Spreadsheet-cell-readonly')}
            style={style}
            sx={sx}
        >
            <DivCell>{children}</DivCell>
        </DivRoot>
    );
}

const DivRoot = styled('div')(({ theme }) => ({
    borderStyle: 'solid',
    borderWidth: '1px 1px 0 0',
    boxSizing: 'border-box',
    borderColor: theme.palette.neutral[3],
    height: '100%',
}));

const DivCell = styled('div')(({ theme }) => ({
    textAlign: 'center',
    color: theme.palette.neutral[6],
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
}));
