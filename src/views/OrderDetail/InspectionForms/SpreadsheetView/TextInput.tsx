import clsx from 'clsx';
import { ComponentType, forwardRef } from 'react';
import styles from './TextInput.module.css';

const TextInput = forwardRef(
    (
        { className, ...props }: React.InputHTMLAttributes<HTMLInputElement>,
        ref: React.ForwardedRef<HTMLInputElement>
    ) => {
        return <input ref={ref} className={clsx(styles.input, className)} {...props} />;
    }
);

export type TextInputProps = ComponentType<typeof TextInput>;

export default TextInput;
