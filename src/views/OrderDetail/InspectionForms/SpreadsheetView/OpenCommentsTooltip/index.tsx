import { IconButton, styled, TooltipProps } from '@mui/material';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import React, { forwardRef, useCallback } from 'react';
import ArrowTooltip from '../../../../../common/components/Tooltip';

export interface OpenCommentsButtonProps {
    onClick: () => void;
    tooltipText: string;
    tooltipPosition?: string;
    disabled?: boolean;
    noPadding?: boolean;
}

const OpenCommentsTooltip = forwardRef<HTMLButtonElement, OpenCommentsButtonProps>(
    ({ onClick, tooltipText, tooltipPosition, disabled, noPadding = false }, ref) => {
        const onClickCallback = useCallback(
            (e: React.MouseEvent<HTMLElement>) => {
                e.stopPropagation();
                onClick();
            },
            [onClick]
        );

        return (
            <ArrowTooltip
                content={tooltipText}
                position={(tooltipPosition || 'bottom') as TooltipProps['placement']}
            >
                <IconButton
                    style={{ zIndex: 1, padding: noPadding ? 0 : 5 }}
                    size="small"
                    ref={ref}
                    disabled={disabled}
                    onClick={onClickCallback}
                >
                    <StyledCommentsIcon />
                </IconButton>
            </ArrowTooltip>
        );
    }
);

export default OpenCommentsTooltip;

const StyledCommentsIcon = styled(CommentsIcon)(({ theme }) => ({
    '& path': {
        fill: theme.palette.neutral[5],
    },
    '&:hover path': {
        fill: theme.palette.primary.main,
    },
}));
