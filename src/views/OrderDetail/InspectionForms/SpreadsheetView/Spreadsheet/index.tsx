import { IconButton, IconButtonProps, Interpolation, styled, Theme } from '@mui/material';
import { LockIcon } from 'common/components/Icons/LockIcon';
import Resizable, { resizableClasses, ResizeEvent } from 'common/components/ResizableColumn';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { CSSProperties, useCallback, useEffect } from 'react';
import { createSelector } from 'reselect';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import {
    selectColumns,
    selectEstimatesSet,
    selectItemsSet,
    selectOrderId,
    selectSystemsSet,
} from 'store/slices/order/orderInspectionFormsSlice';
import { ItemData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { spreadsheetActions } from 'store/slices/order/spreadsheet';
import { ItemRows } from '../ItemRows';
import { getRowsGroups, sortItemsByUrgency, spreadsheetItemFilter } from './helper';
import {
    getColumnWidthVariableName,
    PREDEFINED_COLUMN_IDS,
    useSpreadsheetColumn,
    useSpreadsheetLayout,
} from './layout';

// IMPORTANT CONSTANTS
const NUMBER_OF_PREDEFINED_COLUMNS = {
    PARTS: 5,
    LABOR: 2,
    END: 3, // columns at the end of the spreadsheet
};

type SpreadsheetProps = {
    orderId: number;
    itemFilter: (item: ItemData) => boolean;
};

export const selectRowGroups = createSelector(
    [
        selectSystemsSet,
        selectItemsSet,
        selectEstimatesSet,
        selectOrderId,
        (
            _,
            params: { filter: (item: ItemData) => boolean; orderId: number; onlyApproved?: boolean }
        ) => params,
    ],
    (
        systemsSet,
        itemsSet,
        estimatesSet,
        fetchedOrderId,
        { filter, orderId, onlyApproved = false }
    ) => {
        if (fetchedOrderId !== orderId) return [];
        const allEstimates = Object.values(estimatesSet);
        const orderedItems = Object.values(systemsSet)
            .flatMap((s) => s.items.map((i) => itemsSet[i]))
            .sort(sortItemsByUrgency);
        const shownItems = orderedItems.filter(spreadsheetItemFilter).filter(filter);

        const approvedItems = shownItems.filter((x) => x.isApproved);

        return getRowsGroups(onlyApproved ? approvedItems : shownItems, allEstimates);
    }
);

export function Spreadsheet({ itemFilter, orderId }: SpreadsheetProps) {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const lt = (key: string) => t('inspectionForms.spreadsheetView.' + key);
    const fields = useAppSelector(selectColumns, isEqual);
    const { labor: laborExtraFields, parts: partsExtraFields } = fields;

    const rowsGroups = useAppSelector(
        useCallback(
            (r: RootState) => selectRowGroups(r, { orderId, filter: itemFilter }),
            [itemFilter, orderId]
        ),
        isEqual
    );

    useEffect(() => {
        return () => {
            dispatch(spreadsheetActions.resetSpreadsheet());
        };
    }, [dispatch]);

    const { setFixed, setWidth, rootElementRef, SpreadsheetLayoutProvider } =
        useSpreadsheetLayout();

    if (rowsGroups.length === 0) return null;

    const firstColumnsCount = 4;

    return (
        <SpreadsheetLayoutProvider>
            <DivRoot
                ref={rootElementRef}
                style={
                    {
                        [getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.approveDecline)]: '100px',
                    } as CSSProperties
                }
            >
                <DivHeader>
                    <JobsAndInspectionItemsSection
                        setFixed={setFixed}
                        setWidth={setWidth}
                        name={lt('jobsAndInspectionItems').toUpperCase()}
                    />

                    <DivColumnsSection>
                        <DivColumnSectionHeader>{lt('partsHeading')}</DivColumnSectionHeader>
                        <DivColumnSectionBody>
                            {partsExtraFields.map((x) => (
                                <ColumnHeader
                                    key={x.id}
                                    id={x.id}
                                    onResize={setWidth}
                                    name={
                                        x.type === 'Predefined'
                                            ? t(
                                                  `settings.customizableFields.predefined.${x.name.replaceAll(
                                                      '.',
                                                      '_'
                                                  )}`
                                              )
                                            : x.name
                                    }
                                />
                            ))}
                        </DivColumnSectionBody>
                    </DivColumnsSection>

                    <DivColumnsSection>
                        <DivColumnSectionHeader>{lt('laborHeading')}</DivColumnSectionHeader>
                        <DivColumnSectionBody>
                            {laborExtraFields.map((x) => (
                                <ColumnHeader
                                    key={x.id}
                                    id={x.id}
                                    onResize={setWidth}
                                    name={
                                        x.type === 'Predefined'
                                            ? t(
                                                  `settings.customizableFields.predefined.${x.name.replaceAll(
                                                      '.',
                                                      '_'
                                                  )}`
                                              )
                                            : x.name
                                    }
                                />
                            ))}
                        </DivColumnSectionBody>
                    </DivColumnsSection>

                    <DiscountAndSubtotalSection
                        setFixed={setFixed}
                        setWidth={setWidth}
                        subtotalName={lt('subtotal').toUpperCase()}
                        discountName={lt('discount')}
                    />

                    <DivColumnsSection sx={{ flexBasis: '100%' }}>
                        <DivColumnSectionPlaceholder />
                        <DivColumnSectionBody>
                            <NotResizableColumn
                                sx={{
                                    minWidth: `var(${getColumnWidthVariableName(
                                        PREDEFINED_COLUMN_IDS.approveDecline
                                    )})`,
                                    flexBasis: '100%',
                                }}
                            >
                                {lt('approved')}
                            </NotResizableColumn>
                        </DivColumnSectionBody>
                    </DivColumnsSection>
                </DivHeader>

                {rowsGroups.map((rowsGroup) => (
                    <ItemRows
                        orderId={orderId}
                        key={rowsGroup.itemId}
                        columnsCount={
                            partsExtraFields.length +
                            laborExtraFields.length +
                            NUMBER_OF_PREDEFINED_COLUMNS.END +
                            firstColumnsCount
                        }
                        {...rowsGroup}
                    />
                ))}
            </DivRoot>
        </SpreadsheetLayoutProvider>
    );
}

const DivRoot = styled('div')({
    minWidth: '100%',
    width: 'fit-content',
});

const DivHeader = styled('div')(({ theme }) => ({
    display: 'flex',
    backgroundColor: theme.palette.neutral[2],
    alignItems: 'stretch',
    textAlign: 'center',
    textTransform: 'uppercase',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
}));

function JobsAndInspectionItemsSection({
    setFixed,
    setWidth,
    name,
}: {
    name: string;
    setFixed: (id: string, value: boolean) => void;
    setWidth: (id: string, value: number) => void;
}) {
    const { fixed } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems);

    return (
        <DivColumnsSection
            sx={
                fixed
                    ? {
                          position: 'sticky',
                          zIndex: 5,
                          left: 0,
                          borderRight: '1px solid var(--neutral4)',
                          marginLeft: '-1px',
                      }
                    : undefined
            }
        >
            <DivColumnSectionPlaceholder />
            <DivColumnSectionBody>
                <ColumnHeader
                    hasToggle
                    onToggled={(value) =>
                        setFixed(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems, value)
                    }
                    id={PREDEFINED_COLUMN_IDS.jobsAndInspectionItems}
                    name={name}
                    onResize={setWidth}
                    minWidth={240}
                    maxWidth={700}
                />
            </DivColumnSectionBody>
        </DivColumnsSection>
    );
}

function DiscountAndSubtotalSection({
    discountName,
    subtotalName,
    setFixed,
    setWidth,
}: {
    discountName: string;
    subtotalName: string;
    setFixed: (id: string, value: boolean) => void;
    setWidth: (id: string, value: number) => void;
}) {
    const { fixed: discountFixed } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.discount);
    const { fixed: subtotalFixed } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.subtotal);

    const discountHeader = (
        <ColumnHeader
            key={PREDEFINED_COLUMN_IDS.discount}
            id={PREDEFINED_COLUMN_IDS.discount}
            name={discountName}
            onResize={setWidth}
            hasToggle
            onToggled={(value) => setFixed(PREDEFINED_COLUMN_IDS.discount, value)}
        />
    );

    const subtotalHeader = (
        <ColumnHeader
            key={PREDEFINED_COLUMN_IDS.subtotal}
            id={PREDEFINED_COLUMN_IDS.subtotal}
            name={subtotalName}
            onResize={setWidth}
            hasToggle
            onToggled={(value) => setFixed(PREDEFINED_COLUMN_IDS.subtotal, value)}
        />
    );

    const fixedHeaders: React.ReactElement[] = [];
    const notFixedHeaders: React.ReactElement[] = [];

    if (discountFixed) {
        fixedHeaders.push(discountHeader);
    } else {
        notFixedHeaders.push(discountHeader);
    }

    if (subtotalFixed) {
        fixedHeaders.push(subtotalHeader);
    } else {
        notFixedHeaders.push(subtotalHeader);
    }

    const children: (React.ReactElement | undefined)[] = [
        notFixedHeaders.length ? (
            <DivColumnsSection key="not-fixed">
                <DivColumnSectionPlaceholder />
                <DivColumnSectionBody>{notFixedHeaders}</DivColumnSectionBody>
            </DivColumnsSection>
        ) : undefined,
        fixedHeaders.length ? (
            <DivColumnsSection
                key="fixed"
                sx={{
                    position: 'sticky',
                    right: -1,
                    border: 'solid var(--neutral4)',
                    borderWidth: '0 1px',
                    zIndex: 1,
                }}
            >
                <DivColumnSectionPlaceholder />
                <DivColumnSectionBody>{fixedHeaders}</DivColumnSectionBody>
            </DivColumnsSection>
        ) : undefined,
    ];

    if (discountFixed && !subtotalFixed) {
        children.reverse();
    }

    return children;
}

const DivColumnsSection = styled('div')({
    backgroundColor: 'var(--neutral2)',
});

const DivColumnSectionPlaceholder = styled('div')({
    height: 40,
    width: 'calc(100% - 8px)',
    margin: '0px 4px',
});

const DivColumnSectionHeader = styled(DivColumnSectionPlaceholder)(({ theme }) => ({
    borderBottom: `1px solid ${theme.palette.neutral[7]}`,
    display: 'grid',
    alignItems: 'center',
    color: theme.palette.neutral[9],
    ...theme.typography.h6Inter,
    textTransform: 'none',
}));

const DivColumnSectionBody = styled('div')({
    display: 'flex',
    minWidth: '100%',
    alignItems: 'stretch',
    height: 40,
});

function ColumnHeader({
    name,
    id,
    onResize,
    minWidth = 50,
    maxWidth = MAX_CELL_WIDTH,
    hasToggle,
    onToggled,
}: {
    name: string;
    id: string;
    onResize: (id: string, width: number) => void;
    minWidth?: number;
    maxWidth?: number;
    onToggled?: (enabled: boolean) => void;
    hasToggle?: boolean;
}) {
    const { fixed, style } = useSpreadsheetColumn(id);

    const handleResize = useCallback(
        (event: ResizeEvent) => {
            onResize(id, event.width);
        },
        [id, onResize]
    );

    return (
        <ResizableColumnRoot
            sx={{ minWidth, maxWidth }}
            minWidth={minWidth}
            maxWidth={maxWidth}
            onResize={handleResize}
            style={style}
            disabled={fixed}
        >
            {hasToggle && <ToggleButton locked={fixed} onClick={() => onToggled?.(!fixed)} />}
            <ColumnHeaderText>{name}</ColumnHeaderText>
        </ResizableColumnRoot>
    );
}

function ToggleButton({
    locked,
    onClick,
}: {
    locked: boolean;
    onClick: IconButtonProps['onClick'];
}) {
    return (
        <IconButton size="small" onClick={onClick}>
            <LockIcon fill={locked ? 'var(--cm1)' : 'var(--neutral6)'} />
        </IconButton>
    );
}

const MAX_CELL_WIDTH = 400;

const columnStyle: Interpolation<Theme> = {
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 50,
    maxWidth: MAX_CELL_WIDTH,
    position: 'relative',
};
const NotResizableColumn = styled('div')(columnStyle);
const ResizableColumnRoot = styled(Resizable)({
    ...columnStyle,

    [`&:not(.${resizableClasses.resizing}) .${resizableClasses.handle}:not(:hover)`]: {
        '::after': {
            backgroundColor: 'var(--neutral4)',
            width: 1,
            transform: 'translateX(2px) scaleY(0.7)',
        },
    },
});

const ColumnHeaderText = styled('div')({
    textTransform: 'none',
    display: '-webkit-box',
    '-webkit-line-clamp': 2,
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
});
