import { PriorityLevel } from 'common/constants/PriorityLevel';
import { EstimateData, ItemData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { RepairRowsGroup } from '../spreadsheet.types';

export const spreadsheetItemFilter = (item: ItemData) => {
    //in spreadsheet we only show medium and urgent items
    return item.priority === PriorityLevel.Urgent || item.priority === PriorityLevel.Med;
};

export const getRowsGroups = (
    shownItems: ItemData[],
    allEstimates: EstimateData[]
): RepairRowsGroup[] => {
    let globalRowsCounter = 0;

    const result = shownItems.map((i) => {
        const relatedEstimates = allEstimates.filter((e) => e.repairId === i.repairId);
        const relatedEstimatesCount = relatedEstimates.length;
        const repairHasEstimate = relatedEstimates.some((e) => e.isSubItem !== true);
        //we're adding an additional row for estimates related to repair (even if it's not exactly exists)
        const rowsCount = relatedEstimatesCount + (repairHasEstimate ? 0 : 1);

        const result = {
            itemId: i.masterItemId,
            firstRowNo: globalRowsCounter,
            rowsCount: rowsCount,
        };

        globalRowsCounter += rowsCount;

        return result;
    });

    return result;
};

export const sortItemsByUrgency = (a: ItemData, b: ItemData) => {
    function getPriorityIndex(priority: PriorityLevel): number {
        switch (priority) {
            case PriorityLevel.Urgent:
                return 0;
            case PriorityLevel.Med:
                return 1;
            case PriorityLevel.Low:
                return 2;
            case PriorityLevel.NA:
                return 3;
        }
    }
    return getPriorityIndex(a.priority) - getPriorityIndex(b.priority);
};
