import {
    createContext,
    CSSProperties,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
} from 'react';
import { Subject, useSubject } from 'utils/event';
import { InspectionFormsUserStorage, useInspectionFormsUserStorage } from '../../user-storage';

export const PREDEFINED_COLUMN_IDS = {
    jobsAndInspectionItems: 'job-and-items',
    subtotal: 'subtotal',
    discount: 'discount',
    approveDecline: 'approve-decline',
};

/**
 * Columns that can be fixed (i.e. position: sticky).
 */
const FIXABLE_COLUMNS = [
    PREDEFINED_COLUMN_IDS.subtotal,
    PREDEFINED_COLUMN_IDS.jobsAndInspectionItems,
    PREDEFINED_COLUMN_IDS.discount,
];

const DEFAULT_SIZE = 140;

const DEFAULT_SIZE_BY_TYPE: Record<string, number> = {
    [PREDEFINED_COLUMN_IDS.jobsAndInspectionItems]: 300,
};

export function getColumnWidthVariableName(column: string): string {
    return `--spreadsheet-column-${column}`;
}

function applyLayout(
    layout: InspectionFormsUserStorage['spreadsheet'],
    rootElement: HTMLElement | null,
    width: Record<string, number>,
    columns: Record<string, Subject<ColumnState>>
) {
    for (const id in layout.columnsWidth) {
        width[id] = layout.columnsWidth[id];
    }

    window.requestAnimationFrame(() => {
        for (const id in layout.toggles) {
            // updating these causes css update so do it inside raf
            const fixed = layout.toggles[id] && FIXABLE_COLUMNS.includes(id);
            if (!columns[id]) {
                columns[id] = createDefaultSubject(id);
            }
            columns[id].update((x) => ({ ...x, fixed }));
        }

        if (rootElement) {
            for (const id in layout.columnsWidth) {
                rootElement.style.setProperty(getColumnWidthVariableName(id), `${width[id]}px`);
            }
        }
    });
}

type ColumnState = {
    fixed: boolean;
    style: CSSProperties;
};

function createDefaultSubject(id: string): Subject<ColumnState> {
    return new Subject({
        fixed: false,
        style: {
            width: `var(${getColumnWidthVariableName(id)})`,
            maxWidth: `var(${getColumnWidthVariableName(id)})`,
            minWidth: `var(${getColumnWidthVariableName(id)})`,
        },
    });
}

export type SpreadsheetLayoutContext = {
    column(id: string): Subject<ColumnState>;
};

const SpreadsheetLayoutContextInstance = createContext<SpreadsheetLayoutContext | null>(null);

export function useSpreadsheetColumn(id: string): ColumnState {
    const ctx = useSpreadsheetLayoutContext();
    return useSubject(ctx.column(id));
}

export function useSpreadsheetLayoutContext(): SpreadsheetLayoutContext {
    const ctx = useContext(SpreadsheetLayoutContextInstance);
    if (!ctx) throw new Error('SpreadsheetLayoutContext is not available');
    return ctx;
}

export function useSpreadsheetLayout() {
    const rootElement = useRef<HTMLElement | null>(null);
    const columns = useRef<Record<string, Subject<ColumnState>>>({});
    const width = useRef<Record<string, number>>({});

    const [{ data: uiSettings }, update] = useInspectionFormsUserStorage();
    const data = uiSettings.spreadsheet;

    useEffect(() => {
        if (!data) return;

        applyLayout(data, rootElement.current, width.current, columns.current);
    }, [data]);

    const syncWithServer = useMemo(() => {
        return () => {
            const columnsWidth: Record<string, number> = {};
            const toggles: Record<string, boolean> = {};

            for (const id in columns.current) {
                const subject = columns.current[id];
                columnsWidth[id] = width.current[id] ?? DEFAULT_SIZE_BY_TYPE[id] ?? DEFAULT_SIZE;

                // only save if it's true
                if (subject.value.fixed) {
                    toggles[id] = true;
                }
            }

            const layout: InspectionFormsUserStorage['spreadsheet'] = {
                columnsWidth,
                toggles,
            };
            update((x) => ({
                ...x,
                spreadsheet: layout,
            }));
        };
    }, [update]);

    const getColumn: (id: string) => Subject<ColumnState> = useCallback((id) => {
        if (!columns.current[id]) {
            columns.current[id] = createDefaultSubject(id);
            window.requestAnimationFrame(() => {
                if (rootElement.current) {
                    rootElement.current.style.setProperty(
                        getColumnWidthVariableName(id),
                        `${DEFAULT_SIZE_BY_TYPE[id] ?? DEFAULT_SIZE}px`
                    );
                }
            });
        }
        return columns.current[id];
    }, []);

    return {
        column: getColumn,

        SpreadsheetLayoutProvider: useMemo(
            () => createSpreadsheetLayoutContextProvider(getColumn),
            [getColumn]
        ),

        setWidth: useCallback(
            (id: string, value: number) => {
                if (rootElement.current) {
                    width.current[id] = value;
                    syncWithServer();

                    window.requestAnimationFrame(() => {
                        if (rootElement.current) {
                            rootElement.current.style.setProperty(
                                getColumnWidthVariableName(id),
                                `${value}px`
                            );
                        }
                    });
                }
            },
            [syncWithServer]
        ),

        setFixed: useCallback(
            (key: string, value: boolean) => {
                if (columns.current[key]) {
                    columns.current[key].update((x) => ({
                        ...x,
                        fixed: value,
                    }));
                    syncWithServer();
                }
            },
            [syncWithServer]
        ),

        rootElementRef: useCallback((element: HTMLElement | null) => {
            rootElement.current = element;

            if (element) {
                window.requestAnimationFrame(() => {
                    if (rootElement.current) {
                        for (const id in width.current) {
                            rootElement.current.style.setProperty(
                                getColumnWidthVariableName(id),
                                `${width.current[id]}px`
                            );
                        }
                    }
                });
            }
        }, []),
    };
}

function createSpreadsheetLayoutContextProvider(
    getColumn: (id: string) => Subject<ColumnState>
): React.FC<React.PropsWithChildren<{}>> {
    const ctx: SpreadsheetLayoutContext = { column: getColumn };

    return ({ children }) => {
        return (
            <SpreadsheetLayoutContextInstance.Provider value={ctx}>
                {children}
            </SpreadsheetLayoutContextInstance.Provider>
        );
    };
}
