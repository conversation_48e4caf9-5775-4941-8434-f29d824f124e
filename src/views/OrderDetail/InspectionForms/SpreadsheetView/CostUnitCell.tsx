import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import CurrencyCell from './CurrencyCell';
import useSpreadsheet from './context';

type CostUnitCellProps = {
    estimateId: number | null;
    fieldName: string;
    masterItemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectValue = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.partUnitCost : null;

export default function CostUnitCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: CostUnitCellProps) {
    const costUnit = useAppSelector((r) => selectValue(r, estimateId));
    const spreadsheet = useSpreadsheet();
    const [value, setValue] = useState<number | null>(costUnit ?? null);

    useEffect(() => {
        setValue(costUnit ?? null);
    }, [costUnit]);

    const { repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;

    return (
        <CurrencyCell
            estimateId={estimateId}
            value={value}
            onChange={setValue}
            onBlur={() => {
                if (costUnit !== value)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { partUnitCost: value });
            }}
            onClear={(saveImmediately) => {
                setValue(null);
                if (costUnit !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { partUnitCost: null });
            }}
            decimalScale={requireDecimals ? 2 : 0}
            disabled={disabled}
            {...props}
        />
    );
}
