import { styled } from '@mui/material';
import { ComponentType, ForwardedRef, forwardRef } from 'react';
import NumberFormat, { InputAttributes, NumberFormatProps } from 'react-number-format';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { SpreadsheetInputProps } from '../spreadsheet.types';

type NumberInputProps = Omit<NumberFormatProps, 'onChange' | 'prefix' | 'suffix'> &
    SpreadsheetInputProps & {
        template?: string;
        value: number | null;
        onChange: (value: number | null) => void;
    };

export const NumberInput = forwardRef(
    (
        { template, value, onChange, onBlur, onFocus, ...props }: NumberInputProps,
        ref: ForwardedRef<HTMLInputElement>
    ) => {
        const matches = template?.match(/(.*)\{0\}(.*)/);
        const prefix = (matches && matches[1]) ?? '';
        const suffix = (matches && matches[2]) ?? '';

        const { internationalization } = useSelector(selectSettings);
        const { numberGroupSeparator, numberDecimalSeparator } = internationalization;

        return (
            <StyledNumberFormat
                getInputRef={ref}
                prefix={prefix}
                suffix={suffix}
                value={value === null ? '' : value}
                decimalSeparator={numberDecimalSeparator}
                thousandSeparator={numberGroupSeparator}
                onValueChange={(e) => {
                    if (e.floatValue === undefined) onChange(null);
                    else onChange(+e.floatValue.toFixed(2));
                }}
                onBlur={onBlur}
                onFocus={onFocus}
                isAllowed={(x) => !x.floatValue || Math.abs(x.floatValue) < 100000000}
                {...props}
            />
        );
    }
);

const StyledNumberFormat = styled(NumberFormat)(({ theme }) => ({
    outline: 'none',
    border: 'none',
    width: '100%',
    padding: '0 12px',
    background: 'transparent',
    textAlign: 'center',
    color: theme.palette.neutral[6],
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
})) as ComponentType<NumberFormatProps<InputAttributes>>;
