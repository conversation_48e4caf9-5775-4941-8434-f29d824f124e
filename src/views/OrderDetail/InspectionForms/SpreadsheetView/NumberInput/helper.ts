export const convertPropsValueToNumberValue = (propsValue: number | null): number | string => {
    //Replace 'null' by 'empty-string', if needed
    if (propsValue === null) {
        return '';
    }

    return propsValue;
};

export const convertNumberValueToPropsValue = (numberValue: number | string): number | null => {
    if (numberValue === '') {
        return null;
    }

    return Number(numberValue);
};
