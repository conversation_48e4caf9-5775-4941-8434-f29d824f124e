import { makeStyles } from '@mui/styles';

export const useClasses = makeStyles((theme) => ({
    root: {},
    spreadsheetContainer: {
        overflowX: 'auto',
        overflowY: 'hidden',

        '&::-webkit-scrollbar': {
            height: '30px',
        },

        '&::-webkit-scrollbar-track': {
            background: `${theme.palette.common.white} !important`,
        },

        '&::-webkit-scrollbar-thumb': {
            background: `${theme.palette.neutral[5]} !important`,
            borderRadius: '50px',
            border: `solid 9px ${theme.palette.common.white}`,

            '&:hover': {
                background: `${theme.palette.neutral[7]} !important`,
            },
        },
    },
    addItem: {
        marginLeft: 40,
    },
}));
