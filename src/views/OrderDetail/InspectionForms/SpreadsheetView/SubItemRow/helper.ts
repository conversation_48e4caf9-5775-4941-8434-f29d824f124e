import { DiscountType } from 'api/settings/OrdersSettings';
import { EstimateDto } from 'datacontracts/InspectionForms/EstimateDto';
import { InternationalizationDto } from 'datacontracts/Interationalization/InternationalizationDto';
import { EstimateData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { InternationalizationLogic } from '../../../../../business/InternationalizationLogic';
import { calculateSubtotal } from '../../helper';

export type EditableEstimatePart = Pick<
    EstimateDto,
    | 'name'
    | 'partsNumber'
    | 'quantity'
    | 'availability'
    | 'partUnitCost'
    | 'partUnitPrice'
    | 'hours'
    | 'hourUnitPrice'
>;

export const getFormattedSubtotal = (
    internationalization: InternationalizationDto,
    discountType: DiscountType | undefined,
    estimate: EstimateData,
    requireDecimals?: boolean
): string => {
    const result = requireDecimals
        ? calculateSubtotal(estimate, requireDecimals, discountType)
        : +calculateSubtotal(estimate, requireDecimals, discountType).toFixed();
    const formattedResult = InternationalizationLogic.numberToCurrency(
        internationalization,
        result,
        {
            allowZero: true,
            requireDecimals: requireDecimals !== undefined ? requireDecimals : true,
        }
    );

    return formattedResult;
};
