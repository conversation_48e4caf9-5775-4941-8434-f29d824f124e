import { IconButton, styled } from '@mui/material';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { memo, useCallback, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectIsOrderClosed, selectOrderDiscountType } from 'store/slices/order/orderDetails';
import { selectColumns, selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice';
import { addEstimate, deleteEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import CellRenderer from '../CellRenderer';
import DiscountCell from '../DiscountCell';
import EstimateNameCell from '../EstimateNameCell';
import { ReadonlyCell } from '../ReadonlyCell';
import {
    getColumnWidthVariableName,
    PREDEFINED_COLUMN_IDS,
    useSpreadsheetColumn,
} from '../Spreadsheet/layout';
import { useIsSpreadsheetReadonly } from '../context';
import { getFormattedSubtotal } from './helper';

type SubItemRowProps = {
    rowNo: number;
    orderId: number;
    itemId: number;
    estimateId: number;
    showAddIcon: boolean;
};

const selectData = createSelector(
    [selectSettings, selectEstimateById, selectColumns, selectOrderDiscountType],
    (settings, estimate, columns, discountType) => {
        const requireDecimals = settings.repairShopSettings?.features.enableRemoveDecimals
            ? false
            : true;

        return {
            columns,
            estimate,
            enableEstimateIntegration:
                settings.repairShopSettings?.features.enableEstimateIntegration || false,
            formattedSubtotal: getFormattedSubtotal(
                settings.internationalization,
                discountType,
                estimate,
                requireDecimals
            ),
        };
    }
);

const SubItemRow = ({ rowNo, orderId, itemId, estimateId, showAddIcon }: SubItemRowProps) => {
    const {
        estimate,
        enableEstimateIntegration,
        columns: { parts, labor },
        formattedSubtotal,
    } = useAppSelector((r) => selectData(r, estimateId), isEqual);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    const [isDeleteEstimateOpen, setIsDeleteEstimateOpen] = useState(false);

    const handleOnAdd = useCallback(() => {
        dispatch(addEstimate({ masterItemId: itemId, repairOrderId: orderId }));
    }, [dispatch, orderId, itemId]);

    const handleOnRemove = useCallback(() => {
        setIsDeleteEstimateOpen(true);
    }, []);

    const handleOnRemoveConfirm = useCallback(() => {
        dispatch(
            deleteEstimate({ itemId, estimateId: estimate.estimateId, repairOrderId: orderId })
        );
    }, [dispatch, itemId, orderId, estimate.estimateId]);

    const readOnly = useIsSpreadsheetReadonly();

    return (
        <>
            {isDeleteEstimateOpen && (
                <DeleteConfirmationPopup
                    open={isDeleteEstimateOpen}
                    title={t('inspectionForms.deleteEstimateModal.title')}
                    body={t('inspectionForms.deleteEstimateModal.body')}
                    cancel={t('inspectionForms.deleteEstimateModal.cancel')}
                    confirm={t('inspectionForms.deleteEstimateModal.confirm')}
                    onConfirm={handleOnRemoveConfirm}
                    onClose={() => {
                        setIsDeleteEstimateOpen(false);
                    }}
                />
            )}
            <DivRow className={readOnly || orderIsClosed ? 'disabled' : undefined}>
                <ItemDefContainer>
                    <StyledSmallReadonlyCell rowNo={rowNo} colNo={0}>
                        {showAddIcon && !enableEstimateIntegration && (
                            <IconButton
                                size="small"
                                disabled={readOnly || orderIsClosed}
                                onClick={handleOnAdd}
                            >
                                <PlusIcon />
                            </IconButton>
                        )}
                    </StyledSmallReadonlyCell>
                    <StyledSmallReadonlyCell rowNo={rowNo} colNo={1}>
                        {!enableEstimateIntegration && (
                            <IconButton
                                size="small"
                                disabled={readOnly || orderIsClosed}
                                onClick={handleOnRemove}
                            >
                                <LessIcon />
                            </IconButton>
                        )}
                    </StyledSmallReadonlyCell>
                    <StyledSmallReadonlyCell rowNo={rowNo} colNo={2} />
                    <EstimateNameCell
                        masterItemId={itemId}
                        estimateId={estimateId}
                        rowNo={rowNo}
                        colNo={3}
                        disabled={orderIsClosed}
                    />
                </ItemDefContainer>

                {parts.map((field, idx) => (
                    <CellRenderer
                        field={field}
                        itemId={itemId}
                        estimateId={estimateId}
                        rowNo={rowNo}
                        colNo={4 + idx}
                        key={field.id}
                        disabled={orderIsClosed}
                    />
                ))}
                {labor.map((field, idx) => (
                    <CellRenderer
                        field={field}
                        itemId={itemId}
                        estimateId={estimateId}
                        rowNo={rowNo}
                        colNo={4 + idx + parts.length}
                        key={field.id}
                        disabled={orderIsClosed}
                    />
                ))}
                <DiscountCell
                    rowNo={rowNo}
                    colNo={4 + labor.length + parts.length}
                    masterItemId={itemId}
                    estimateId={estimateId}
                    disabled={orderIsClosed}
                />
                <SubtotalCell rowNo={rowNo} colNo={5 + parts.length + labor.length}>
                    <b>{formattedSubtotal}</b>
                </SubtotalCell>
                <ApproveDeclineCell rowNo={rowNo} colNo={6 + parts.length + labor.length} />
            </DivRow>
        </>
    );
};

export default memo(SubItemRow);

const DivRow = styled('div')(({ theme }) => ({
    display: 'flex',
    height: 40,
    backgroundColor: theme.palette.neutral[1],

    '& .Spreadsheet-cell': {
        backgroundColor: theme.palette.neutral[1],
    },

    '&:not(.disabled):hover .Spreadsheet-cell': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

function ItemDefContainer({ children }: { children: React.ReactNode }) {
    const { fixed, style } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems);

    return (
        <DivItemDefContainer
            style={style}
            sx={fixed ? { left: 0, position: 'sticky', zIndex: 5 } : undefined}
        >
            {children}
        </DivItemDefContainer>
    );
}

const DivItemDefContainer = styled('div')({
    width: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems)})`,
    minWidth: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems)})`,
    display: 'flex',

    '& > :last-child': {
        flexBasis: '100%',
    },
});

function SubtotalCell({
    children,
    rowNo,
    colNo,
}: {
    children: React.ReactNode;
    rowNo: number;
    colNo: number;
}) {
    const { style, fixed } = useSpreadsheetColumn(PREDEFINED_COLUMN_IDS.subtotal);

    return (
        <StyledSubtotalCell
            rowNo={rowNo}
            colNo={colNo}
            style={style}
            className={fixed ? 'SubtotalCell-fixed' : undefined}
        >
            {children}
        </StyledSubtotalCell>
    );
}

const StyledSubtotalCell = styled(ReadonlyCell)({
    '&.SubtotalCell-fixed': {
        position: 'sticky',
        right: 0,
        zIndex: 5,
    },

    '& > div': {
        justifyContent: 'center',
    },
});

const StyledSmallReadonlyCell = styled(ReadonlyCell)({
    width: 40,
    minWidth: 40,
});

const ApproveDeclineCell = styled(ReadonlyCell)({
    width: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.approveDecline)})`,
    minWidth: `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.approveDecline)})`,
    flexBasis: '100%',
});
