import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import CurrencyCell from './CurrencyCell';
import useSpreadsheet from './context';

type HourPriceCellProps = {
    estimateId: number | null;
    fieldName: string;
    masterItemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectValue = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.hourUnitPrice : null;

export default function HourPriceCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: HourPriceCellProps) {
    const hourPrice = useAppSelector((r) => selectValue(r, estimateId));
    const spreadsheet = useSpreadsheet();
    const [value, setValue] = useState<number | null>(hourPrice ?? null);

    useEffect(() => {
        setValue(hourPrice ?? null);
    }, [hourPrice]);

    const { repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;

    return (
        <CurrencyCell
            estimateId={estimateId}
            value={value}
            onChange={setValue}
            onBlur={() => {
                if (value !== hourPrice)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { hourUnitPrice: value });
            }}
            onClear={(saveImmediately) => {
                setValue(null);
                if (hourPrice !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { hourUnitPrice: null });
            }}
            decimalScale={requireDecimals ? 2 : 0}
            disabled={disabled}
            {...props}
        />
    );
}
