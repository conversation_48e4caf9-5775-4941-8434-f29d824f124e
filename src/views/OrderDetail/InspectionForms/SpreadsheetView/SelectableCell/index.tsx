import { styled, SxProps } from '@mui/material';
import clsx from 'clsx';
import React, {
    CSSProperties,
    ForwardedRef,
    ReactNode,
    useCallback,
    useEffect,
    useRef,
    useState,
} from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from 'store';
import { selectIsPositionSelected, spreadsheetActions } from 'store/slices/order/spreadsheet';
import { useIsSpreadsheetReadonly } from '../context';
import { processKeyboardNavigation } from '../spreadsheet.keyboardNavigation';
import { SpreadsheetInputProps } from '../spreadsheet.types';

export type SelectableCellProps = {
    rowNo: number;
    colNo: number;
    className?: string;
    onClear: (saveImmediatly: boolean) => void;
    children: (props: SpreadsheetInputProps & { ref: ForwardedRef<HTMLInputElement> }) => ReactNode;
    rightAdornment?: React.ReactNode;
    style?: CSSProperties;
    sx?: SxProps;
    disabled: boolean;
};

export const SelectableCell = ({
    rowNo,
    colNo,
    children,
    className,
    onClear,
    rightAdornment,
    style,
    sx,
    disabled,
}: SelectableCellProps) => {
    const readOnly = useIsSpreadsheetReadonly();
    const dispatch = useAppDispatch();
    const cellRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const isSelected = useSelector((state: RootState) =>
        selectIsPositionSelected(state, { x: colNo, y: rowNo })
    );

    const [isInputFocused, setIsInputFocused] = useState<boolean>(false);

    useEffect(() => {
        window.requestIdleCallback(() => {
            dispatch(spreadsheetActions.setupCell({ x: colNo, y: rowNo }));
        });
    }, [dispatch, colNo, rowNo]);

    useEffect(() => {
        if (isSelected) {
            cellRef.current!.focus();
        }
    }, [isSelected]);

    const handleCellClick = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            if (readOnly) {
                event.preventDefault();
                return;
            }

            if (!isSelected) {
                dispatch(spreadsheetActions.setPosition({ x: colNo, y: rowNo }));
                event.stopPropagation();
            }
        },
        [isSelected, dispatch, colNo, rowNo, readOnly]
    );

    const handleCellKeyDown = useCallback(
        (event: React.KeyboardEvent<HTMLDivElement>) => {
            const action = processKeyboardNavigation({
                isInputFocused,
                isInputEditable: !readOnly || disabled,
                keyDownEvent: event,
            });

            if (action.clearInput) onClear(action.clearInputSaveImmidiately);

            if (action.direction) {
                inputRef.current?.blur();
                dispatch(spreadsheetActions.changePosition({ direction: action.direction }));
            } else {
                if (action.focusInput) {
                    inputRef.current?.focus();
                }
            }
        },
        [dispatch, onClear, readOnly, isInputFocused, disabled]
    );

    const handleInputFocus = useCallback(
        (_: React.FocusEvent<HTMLInputElement>) => {
            setIsInputFocused(true);
            dispatch(spreadsheetActions.setPosition({ x: colNo, y: rowNo }));
        },
        [dispatch, colNo, rowNo]
    );

    const handleInputBlur = useCallback((_: React.FocusEvent<HTMLInputElement>) => {
        setIsInputFocused(false);
    }, []);

    const content = children({
        ref: inputRef,
        disabled: readOnly || disabled,
        onFocus: handleInputFocus,
        onBlur: handleInputBlur,
    });

    return (
        <DivRoot style={style} sx={sx} className="Spreadsheet-cell Spreadsheet-cell-selectable">
            <DivCell
                ref={cellRef}
                tabIndex={-1}
                onClick={handleCellClick}
                onKeyDown={handleCellKeyDown}
                className={clsx(
                    className,
                    (readOnly || disabled) && 'disabled',
                    isSelected && 'selected'
                )}
            >
                {/* we must render adornment even if it's undefined or null,
                 * because if we only render content (without rightAdornmentContainer)
                 * then input HTML tag will be re-created after adornment is added
                 * in some cases that causes an issue - when element is re-created it loses focus
                 *
                 * so as a result you get this:
                 *  1. click on cell and type any digit
                 *  2. input receives focus (as per requirements)
                 *  3. rightAdornment is added
                 *  4. HTML DOM is modified and "input" element is re-created
                 *  5. as a result "input" loses focus (cuz it was re-created, duh)
                 *
                 * God, I love React
                 */}
                <RightAdornmentContainer className={rightAdornment ? 'has-adornment' : undefined}>
                    {content}
                    <DivRightAdornment>{rightAdornment}</DivRightAdornment>
                </RightAdornmentContainer>
                {!isSelected && <DivOverlay className="overlay" />}
            </DivCell>
        </DivRoot>
    );
};

const DivOverlay = styled('div')({
    position: 'absolute',
    width: '100%',
    height: '100%',
    cursor: 'pointer',
});

const DivRightAdornment = styled('div')({
    padding: '0 5px',
    fontSize: '0.8em',

    '&:empty': {
        display: 'none',
    },
});

const RightAdornmentContainer = styled('div')({
    width: '100%',

    '&.has-adornment': {
        display: 'grid',

        gridTemplateColumns: '3fr 1fr',
    },
});

const DivRoot = styled('div')(({ theme }) => ({
    borderStyle: 'solid',
    borderWidth: '1px 1px 0 0',
    boxSizing: 'border-box',
    borderColor: theme.palette.neutral[3],
    backgroundColor: 'inherit',
}));

const DivCell = styled('div')(({ theme }) => ({
    textAlign: 'center',
    color: theme.palette.neutral[6],
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    position: 'relative',
    width: '100%',
    height: '100%',
    maxHeight: '100%',

    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',

    '&.selected': {
        outline: `2px solid ${theme.palette.primary.main}`,
        outlineOffset: -2,
    },

    '&.disabled .overlay': {
        display: 'none',
    },
}));
