import { styled } from '@mui/material';
import InfoTooltip from 'common/components/InfoTooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import moment from 'moment';
import { useCallback, useEffect, useState } from 'react';
import { RootState, useAppSelector } from 'store';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import CommentsModal from './CommentsModal';
import useSpreadsheet, { useIsSpreadsheetReadonly } from './context';
import CopyTooltip from './CopyTooltip';
import OpenCommentsTooltip from './OpenCommentsTooltip';
import { SelectableCell } from './SelectableCell';
import TextInput from './TextInput';

type EstimateNameCellProps = {
    rowNo: number;
    colNo: number;
    estimateId: number | null;
    masterItemId: number;
    disabled: boolean;
};

const SelectableCellStyled = styled(SelectableCell)({
    '&:not(:hover) .buttonContainer': {
        visibility: 'hidden',
    },

    '& .itemName:has(input) .buttonContainer': {
        visibility: 'hidden',
    },
});

const ItemName = styled('div')({
    width: '100%',
    padding: '0 12px',
    fontWeight: '700',
    position: 'relative',
    height: '100%',
});

const NameInput = styled(TextInput)({
    padding: '0 12px 0 21px',
    fontWeight: 'bold',
    textAlign: 'left',
});

const NameContainerList = styled('div')({
    margin: 0,
    paddingLeft: '20px',
});

const NameContainer = styled('div')({
    margin: '5px 0px',
    flexGrow: 1,
    textAlign: 'left',
    wordBreak: 'break-all',
    lineHeight: '20px',
    paddingRight: '12px',
    minHeight: '20px',

    '&:not(:empty)': {
        display: 'list-item',
    },
});

const ButtonContainer = styled('div')(({ theme }) => ({
    position: 'absolute',
    bottom: '0',
    right: '0',
    display: 'flex',
    alignItems: 'center',
    padding: '0 12px 0 15px',
    backgroundImage: `linear-gradient(to right, transparent, ${theme.palette.neutral[2]} 15%)`,

    '& button': {
        width: '24px',
        height: '24px',
        padding: '0 !important',
    },
    '& div': {
        marginLeft: '3px',
    },
}));

const selectData = (r: RootState, estimateId: number | null) => {
    if (estimateId === null) return { name: '', lastEdited: null };
    const estimate = selectEstimateById(r, estimateId);
    return {
        name: estimate.name,
        lastEdited: estimate.estimateUpdateTime,
    };
};

export default function EstimateNameCell({
    colNo,
    rowNo,
    estimateId,
    masterItemId,
    disabled,
}: EstimateNameCellProps) {
    const spreadsheet = useSpreadsheet();
    const readOnly = useIsSpreadsheetReadonly();
    const { t } = useAppTranslation();
    const selector = useCallback((r: RootState) => selectData(r, estimateId), [estimateId]);
    const { name, lastEdited } = useAppSelector(selector, isEqual);

    const [value, setValue] = useState(name ?? '');
    const [isEditMode, setIsEditMode] = useState(false);
    const [isCommentsModalOpen, setCommentsModalOpen] = useState(false);

    useEffect(() => {
        setValue(name);
    }, [name]);

    return (
        <SelectableCellStyled
            rowNo={rowNo}
            colNo={colNo}
            onClear={(saveImmediately) => {
                if (name !== '' && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { name: '' });
                setValue('');
            }}
            disabled={disabled}
        >
            {({ onBlur, ...props }) => (
                <ItemName className="itemName">
                    {isEditMode ? (
                        <NameInput
                            {...props}
                            name="name"
                            value={value}
                            onBlur={(e) => {
                                onBlur(e);
                                if (name !== value)
                                    spreadsheet.updateEstimate(estimateId, masterItemId, {
                                        name: value,
                                    });
                                setIsEditMode(false);
                            }}
                            autoFocus={isEditMode}
                            onChange={(e) => setValue(e.target.value)}
                            maxLength={255}
                            disabled={disabled}
                        />
                    ) : (
                        <NameContainerList>
                            <NameContainer onDoubleClick={() => !disabled && setIsEditMode(true)}>
                                {value}
                            </NameContainer>
                        </NameContainerList>
                    )}
                    <ButtonContainer className="buttonContainer">
                        <CopyTooltip text={value} tooltipText={t('orderDetails.tooltipCopy')} />
                        {estimateId && (
                            <CommentsModal
                                open={isCommentsModalOpen}
                                target={(ref) => (
                                    <OpenCommentsTooltip
                                        ref={ref}
                                        disabled={readOnly || disabled}
                                        onClick={() => setCommentsModalOpen(true)}
                                        tooltipText={t('orderDetails.tooltipComments')}
                                    />
                                )}
                                onClose={() => setCommentsModalOpen(false)}
                                itemEstimateId={estimateId}
                            />
                        )}

                        {lastEdited && (
                            <InfoTooltip
                                text={`${t('orderDetails.estimationDate')}: ${moment(
                                    lastEdited
                                ).format(t('dateFormats.longDate'))}`}
                                slotProps={{
                                    internal: {
                                        sx: { zIndex: 1 },
                                    },
                                }}
                                sx={{ zIndex: 1 }}
                            />
                        )}
                    </ButtonContainer>
                </ItemName>
            )}
        </SelectableCellStyled>
    );
}
