import PQueue from 'p-queue';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { OTHER_SYSTEM_ID } from 'store/slices/order/orderInspectionFormsSlice';
import { saveEstimate } from 'store/slices/order/orderInspectionFormsSlice/thunks';
import { ItemData } from 'store/slices/order/orderInspectionFormsSlice/types';
import { selectUserPermission } from 'store/slices/user/selectors';
import { selectIsOrderClosed } from '../../../../store/slices/order/orderDetails';
import { AddItem } from '../Common/AddItem';
import { Spreadsheet } from './Spreadsheet';
import { ISpreadsheetContext, SpreadsheetContext, SpreadsheetReadonly } from './context';
import { useClasses } from './css';

type SpreadsheetViewProps = {
    orderId: number;
    itemFilter: (item: ItemData) => boolean;
};

export const SpreadsheetView = ({ itemFilter, orderId }: SpreadsheetViewProps) => {
    const classes = useClasses();
    const { repairShopSettings } = useSelector(selectSettings);
    const userPermission = useSelector(selectUserPermission);
    const canBeEdited =
        userPermission.allowEditEstimates && !repairShopSettings?.features.estimateIntegration;
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const spreadsheetContext = useCreateSpreadsheetContext(!canBeEdited);

    return (
        <div className={classes.root}>
            <div className={classes.spreadsheetContainer}>
                <SpreadsheetContext.Provider value={spreadsheetContext}>
                    <SpreadsheetReadonly.Provider value={!canBeEdited}>
                        <Spreadsheet itemFilter={itemFilter} orderId={orderId} />
                    </SpreadsheetReadonly.Provider>
                </SpreadsheetContext.Provider>
            </div>
            <AddItem
                className={classes.addItem}
                repairOrderId={orderId}
                disabled={!userPermission.allowEditEstimates || orderIsClosed}
                // NOTE (AP) We place all new items in 'Other' system, because on spreadsheet UI there's no way to select any system explicitly
                systemId={OTHER_SYSTEM_ID}
            />
        </div>
    );
};

/**
 * PQueue for saving estimates to make sure we don't save the same estimate twice at
 * the same time, it also means that 2 different estimates cannot be saved at the same
 * which is not what we want, but it's not a big issue at the moment
 *
 * Ideally we only want to avoid having concurrent saves of the same estimate.
 */
const estimatesQueue = new PQueue({ concurrency: 1 });

function useCreateSpreadsheetContext(readOnlyMode: boolean = false): ISpreadsheetContext {
    const dispatch = useAppDispatch();

    return useMemo(
        () => ({
            async updateEstimate(estimateId, masterItemId, update, updateCustomColumns) {
                await estimatesQueue.add(async () => {
                    await dispatch(
                        saveEstimate({
                            estimateId,
                            masterItemId,
                            customColumns: updateCustomColumns,
                            ...update,
                        })
                    );
                });
            },
            readOnlyMode,
        }),
        [dispatch, readOnlyMode]
    );
}
