import { useEffect, useState } from 'react';
import { RootState, useAppSelector } from 'store';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import NumericCell from './NumericCell';
import useSpreadsheet from './context';

type HoursCellProps = {
    estimateId: number | null;
    fieldName: string;
    masterItemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectValue = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.hours : null;

export default function HoursCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: HoursCellProps) {
    const hours = useAppSelector((r) => selectValue(r, estimateId));
    const spreadsheet = useSpreadsheet();
    const [value, setValue] = useState<number | null>(hours ?? null);

    useEffect(() => {
        setValue(hours ?? null);
    }, [hours]);

    return (
        <NumericCell
            estimateId={estimateId}
            value={value}
            onChange={setValue}
            onBlur={() => {
                if (hours !== value)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { hours: value });
            }}
            onClear={(saveImmediately) => {
                setValue(null);
                if (hours !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { hours: null });
            }}
            decimalScale={2}
            disabled={disabled}
            {...props}
        />
    );
}
