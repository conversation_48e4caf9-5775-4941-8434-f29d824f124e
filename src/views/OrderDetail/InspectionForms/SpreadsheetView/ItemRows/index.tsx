import { I<PERSON><PERSON><PERSON>on, LinearProgress, styled, useTheme } from '@mui/material';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { Fragment, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectOrderDiscountType } from 'store/slices/order/orderDetails';
import {
    selectColumns,
    selectEstimatesSet,
    selectItemById,
    selectItemIsSaving,
} from 'store/slices/order/orderInspectionFormsSlice/selectors';
import { deleteEstimateGroup } from 'store/slices/order/orderInspectionFormsSlice/thunks/deleteEstimateGroup';
import { groupSubItems } from '../../helper';
import ItemRow from '../ItemRow';
import { getColumnWidthVariableName, PREDEFINED_COLUMN_IDS } from '../Spreadsheet/layout';
import SubItemRow from '../SubItemRow';
import { getFormattedSubtotal } from './helper';

type ItemRowsProps = {
    orderId: number;
    itemId: number;
    firstRowNo: number;
    columnsCount: number;
};

const selectData = createSelector(
    [selectEstimatesSet, selectItemById, selectOrderDiscountType],
    (estimates, item, discountType) => {
        return {
            item,
            estimates: item.estimates.map((x) => estimates[x]).filter(Boolean),
            discountType,
        };
    }
);

export function ItemRows({ itemId, firstRowNo, orderId, columnsCount }: ItemRowsProps) {
    const isItemSavingInProgress = useSelector((state: RootState) =>
        selectItemIsSaving(state, itemId)
    );
    const theme = useTheme();
    const dispatch = useAppDispatch();
    const { item, estimates, discountType } = useSelector(
        (state: RootState) => selectData(state, itemId),
        isEqual
    );
    const { internationalization, repairShopSettings } = useSelector(selectSettings);

    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;
    const formattedSubtotal = getFormattedSubtotal(
        internationalization,
        discountType,
        estimates,
        requireDecimals
    );
    const repairEstimate = estimates.find((e) => !e.isSubItem);
    const subEstimates = useMemo(() => estimates.filter((e) => e.isSubItem), [estimates]);

    const { t } = useAppTranslation();
    const lt = (key: string) => t('inspectionForms.spreadsheetView.' + key);

    const deleteGroup = (id: number) => {
        dispatch(
            deleteEstimateGroup({
                itemJobId: id,
                itemId,
                orderId,
            })
        );
    };

    const subEstimatesGroups = useMemo(() => groupSubItems(subEstimates), [subEstimates]);

    const columns = useAppSelector(selectColumns, isEqual);

    const itemNameCol = [
        `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.jobsAndInspectionItems)})`,
        ...columns.parts.map((p) => `var(${getColumnWidthVariableName(p.id)})`),
        ...columns.labor.map((l) => `var(${getColumnWidthVariableName(l.id)})`),
        `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.discount)})`,
    ];

    const subtotalCol = `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.subtotal)})`;
    const approveCol = `var(${getColumnWidthVariableName(PREDEFINED_COLUMN_IDS.approveDecline)})`;

    const gridTemplate = `calc(${itemNameCol.join(' + ')}) ${subtotalCol} ${approveCol}`;

    return (
        <DivRoot>
            <DivRowsGroup>
                <ItemRow
                    orderId={orderId}
                    key={item.repairId}
                    rowNo={firstRowNo++}
                    itemId={itemId}
                    estimateId={repairEstimate?.estimateId ?? null}
                    showAddIcon={subEstimates.length === 0}
                />

                {subEstimatesGroups.ungrouped.map((estimate, index) => (
                    <SubItemRow
                        orderId={orderId}
                        key={`${item.repairId}_${estimate.estimateId}`}
                        rowNo={firstRowNo++}
                        itemId={itemId}
                        estimateId={estimate.estimateId}
                        showAddIcon={index === subEstimatesGroups.ungrouped.length - 1}
                    />
                ))}

                {subEstimatesGroups.groups.map((g) => (
                    <Fragment key={g.id}>
                        <DivItemJobGroupHeaderRow>
                            <DivItemJobGroupName>{g.name}</DivItemJobGroupName>
                            <IconButton onClick={() => deleteGroup(g.id)} size="large">
                                <DeleteIcon fill={theme.palette.neutral[6]} />
                            </IconButton>
                        </DivItemJobGroupHeaderRow>
                        {g.estimates.map((estimate) => (
                            <SubItemRow
                                orderId={orderId}
                                key={`${item.repairId}_${estimate.estimateId}`}
                                rowNo={firstRowNo++}
                                itemId={itemId}
                                estimateId={estimate.estimateId}
                                showAddIcon={false}
                            />
                        ))}
                    </Fragment>
                ))}
                {subEstimates.length > 0 && (
                    <DivSummary style={{ gridTemplateColumns: gridTemplate }}>
                        <ItemNameContainer>{lt('itemTotal') + ' ' + item.name}</ItemNameContainer>
                        <FormattedSubtotalContainer>
                            <b>{formattedSubtotal}</b>
                        </FormattedSubtotalContainer>
                    </DivSummary>
                )}
            </DivRowsGroup>
            {isItemSavingInProgress ? <LinearProgress /> : <div style={{ height: 4 }} />}
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    marginTop: 8,
    marginBottom: 20,
});

const DivRowsGroup = styled('div')({
    borderColor: 'var(--neutral3)',
    borderStyle: 'solid',
    borderWidth: '0 0 1px 0',
    boxSizing: 'border-box',
});

const DivItemJobGroupHeaderRow = styled('div')(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: '120px minmax(960px, 1472px) minmax(86px, auto)',
    boxSizing: 'border-box',
    alignItems: 'center',
    paddingLeft: 12,
    height: 40,
    borderTop: 'solid 1px',
    borderColor: theme.palette.neutral[3],
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],

    '& > button': {
        width: 'max-content',
        padding: 6,
    },
}));

const DivItemJobGroupName = styled('div')({
    gridColumnStart: 2,
    paddingLeft: 12,
});

const DivSummary = styled('div')(({ theme }) => ({
    display: 'grid',
    gridAutoFlow: 'row',
    alignItems: 'center',
    height: 40,
    backgroundColor: 'rgba(123, 171, 249, 0.1)',
    color: theme.palette.primary.light,
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));

const ItemNameContainer = styled('div')({
    textAlign: 'left',
    paddingLeft: 12,
    gridColumn: 1,
    marginLeft: 120, // // Combined width of first 3 columns in "JOBS AND INSPECTION ITEMS"
});

const FormattedSubtotalContainer = styled('div')({
    display: 'flex',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    gridColumn: 2,
});
