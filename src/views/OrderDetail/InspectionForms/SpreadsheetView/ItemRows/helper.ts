import { DiscountType } from 'api/settings/OrdersSettings';
import { InternationalizationDto } from 'datacontracts/Interationalization/InternationalizationDto';
import { InternationalizationLogic } from '../../../../../business/InternationalizationLogic';
import { EstimateData } from '../../../../../store/slices/order/orderInspectionFormsSlice/types';
import { calculateSubtotal } from '../../helper';

export const getFormattedSubtotal = (
    internationalization: InternationalizationDto,
    discountType: DiscountType | undefined,
    estimates: EstimateData[],
    requireDecimals?: boolean
): string => {
    const result = estimates
        .map((e) =>
            requireDecimals
                ? calculateSubtotal(e, requireDecimals, discountType)
                : +calculateSubtotal(e, requireDecimals, discountType).toFixed()
        )
        .reduce((sum, value) => sum + value, 0);

    const formattedResult = InternationalizationLogic.numberToCurrency(
        internationalization,
        result,
        {
            allowZero: true,
            requireDecimals: requireDecimals,
        }
    );

    return formattedResult;
};
