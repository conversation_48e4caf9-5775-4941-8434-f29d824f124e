import { useEffect, useState } from 'react';
import { RootState, useAppSelector } from 'store';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import NumericCell from './NumericCell';
import useSpreadsheet from './context';

type QuantityCellProps = {
    estimateId: number | null;
    fieldName: string;
    masterItemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectValue = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.quantity : null;

export default function QuantityCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: QuantityCellProps) {
    const quantity = useAppSelector((r) => selectValue(r, estimateId));
    const [value, setValue] = useState<number | null>(quantity ?? null);
    const spreadsheet = useSpreadsheet();

    useEffect(() => {
        setValue(quantity ?? null);
    }, [quantity]);

    return (
        <NumericCell
            value={value}
            onChange={setValue}
            estimateId={estimateId}
            onBlur={() => {
                if (value !== quantity)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { quantity: value });
            }}
            onClear={(saveImmediately) => {
                setValue(null);
                if (quantity !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { quantity: null });
            }}
            decimalScale={2}
            disabled={disabled}
            {...props}
        />
    );
}
