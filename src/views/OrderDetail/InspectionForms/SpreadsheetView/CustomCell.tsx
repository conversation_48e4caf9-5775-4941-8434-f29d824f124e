import { FieldType } from 'api/fields';
import { ComponentType, CSSProperties, useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectColumnValue } from 'store/slices/order/orderInspectionFormsSlice';
import setColumnValue from 'store/slices/order/orderInspectionFormsSlice/thunks/setColumnValue';
import CurrencyCell from './CurrencyCell';
import NumericCell from './NumericCell';
import { SelectableCell, SelectableCellProps } from './SelectableCell';
import TextCell from './TextCell';

type CustomCellProps = Omit<SelectableCellProps, 'children' | 'onClear'> & {
    fieldId: string;
    fieldName: string;
    type: Exclude<FieldType, 'Predefined' | 'PredefinedAdditionalPosition'>;
    estimateId: number | null;
    itemId: number;
    style: CSSProperties;
    disabled: boolean;
};

export default function CustomCell({
    fieldId,
    fieldName,
    type,
    estimateId,
    itemId,
    colNo,
    rowNo,
    style,
    disabled,
}: CustomCellProps) {
    const dispatch = useAppDispatch();
    const value = useAppSelector((r) =>
        estimateId ? selectColumnValue(r, { estimateId, id: fieldId }) : ''
    );

    const [internalValue, setInternalValue] = useState(value);

    useEffect(() => {
        setInternalValue(value);
    }, [value]);

    const Component = CUSTOM_CELL_IMPLEMENTATION[type];
    if (Component)
        return (
            <Component
                fieldName={fieldName}
                estimateId={estimateId}
                rowNo={rowNo}
                colNo={colNo}
                value={internalValue}
                onBlur={() => {
                    if (value !== internalValue)
                        dispatch(
                            setColumnValue({
                                estimateId,
                                itemId,
                                fieldId,
                                value: internalValue,
                                type,
                                name: fieldName,
                            })
                        );
                }}
                onChange={setInternalValue}
                onClear={(saveImmediately) => {
                    setInternalValue('');
                    if (saveImmediately && value !== '')
                        dispatch(
                            setColumnValue({
                                estimateId,
                                itemId,
                                fieldId,
                                value: '',
                                type,
                                name: fieldName,
                            })
                        );
                }}
                style={style}
                disabled={disabled}
            />
        );

    return (
        <SelectableCell
            onClear={() => {}}
            rowNo={rowNo}
            colNo={colNo}
            style={style}
            disabled={disabled}
        >
            {() => <div>???</div>}
        </SelectableCell>
    );
}

type CustomCellImplProps = {
    fieldName: string;
    estimateId: number | null;
    onChange: (value: string) => void;
    onBlur: React.FocusEventHandler;
    onClear: (saveImmediately: boolean) => void;
    value: string;
    rowNo: number;
    colNo: number;
    style?: CSSProperties;
    disabled: boolean;
};

function CustomFieldFreeTextCell(props: CustomCellImplProps) {
    return <TextCell {...props} />;
}

function CustomFieldNumericCell({ onChange, value, ...props }: CustomCellImplProps) {
    const numValue = parseValue(value);

    return <NumericCell onChange={(v) => onChange(v + '')} value={numValue} {...props} />;
}

function CustomFieldCurrencyCell({ value, onChange, ...props }: CustomCellImplProps) {
    const numValue = parseValue(value);

    return <CurrencyCell onChange={(v) => onChange(v + '')} value={numValue} {...props} />;
}

const CUSTOM_CELL_IMPLEMENTATION: Partial<Record<FieldType, ComponentType<CustomCellImplProps>>> = {
    ShortText: CustomFieldFreeTextCell,
    Numeric: CustomFieldNumericCell,
    Currency: CustomFieldCurrencyCell,
};

function parseValue(v: string): number | null {
    v = v.trim();
    if (v === '') return null;
    const num = parseFloat(v);
    if (!Number.isNaN(num)) return num;
    return null;
}
