import { useEffect, useState } from 'react';
import { RootState, useAppSelector } from 'store';
import { selectEstimateById } from 'store/slices/order/orderInspectionFormsSlice/selectors';
import NumericCell from './NumericCell';
import useSpreadsheet from './context';

type AvailabilityCellProps = {
    masterItemId: number;
    estimateId: number | null;
    fieldName: string;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectPartNumber = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.availability : null;

export default function AvailabilityCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: AvailabilityCellProps) {
    const availability = useAppSelector((r) => selectPartNumber(r, estimateId));
    const spreadsheet = useSpreadsheet();
    const [value, setValue] = useState<number | null>(availability ?? null);

    useEffect(() => {
        setValue(availability ?? null);
    }, [availability]);

    return (
        <NumericCell
            estimateId={estimateId}
            value={value}
            onChange={setValue}
            onBlur={() => {
                if (value !== availability)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { availability: value });
            }}
            onClear={(saveImmediately) => {
                setValue(null);
                if (availability !== null && saveImmediately)
                    spreadsheet.updateEstimate(estimateId, masterItemId, { availability: null });
            }}
            disabled={disabled}
            {...props}
        />
    );
}
