import { useMutation } from '@tanstack/react-query';
import EstimateAPI, { InventoryCustomizableField, InventoryItem } from 'api/Estimate';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useEffect, useMemo, useState } from 'react';
import { RootState, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectOrder } from 'store/slices/order/orderDetails';
import {
    selectAllCustomizableColumns,
    selectEstimateById,
} from 'store/slices/order/orderInspectionFormsSlice/selectors';
import { SetColumnValueArg } from 'store/slices/order/orderInspectionFormsSlice/thunks/setColumnValue';
import { FieldValue } from 'store/slices/order/orderInspectionFormsSlice/types';
import useSpreadsheet from './context';
import TextCell from './TextCell';

type PartNumberCellProps = {
    estimateId: number | null;
    fieldName: string;
    masterItemId: number;
    rowNo: number;
    colNo: number;
    disabled: boolean;
};

const selectPartNumber = (r: RootState, id: number | null) =>
    id ? selectEstimateById(r, id)?.partsNumber : null;

const isValidPartData = (data: InventoryItem) => {
    return (
        data.partId &&
        data.partName &&
        data.availability != null &&
        data.quantity != null &&
        data.partUnitPrice != null
    );
};

const isValidLaborData = (data: InventoryItem) => {
    return data.laborId && data.laborName && data.laborHours && data.laborHourPrice;
};

const getCustomizableFieldsToUpdate = (
    estimateId: number | null,
    masterItemId: number,
    allCustomFields: FieldValue[],
    customizableFields?: InventoryCustomizableField[]
): SetColumnValueArg[] => {
    return allCustomFields.map((c) => ({
        estimateId,
        itemId: masterItemId,
        fieldId: c.id,
        name: c.name,
        value: customizableFields?.find((cf) => cf.name === c.name)?.value ?? '',
        type: c.type,
        section: customizableFields?.find((cf) => cf.name === c.name)?.section,
    }));
};

const useInventoryUpdate = (estimateId: number | null, masterItemId: number) => {
    const order = useAppSelector(selectOrder);
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const allCustomFields = useAppSelector(selectAllCustomizableColumns);
    const { repairShopSettings } = useAppSelector(selectSettings);
    const spreadsheet = useSpreadsheet();

    const getItemJobsFromOpenApi = useMemo(
        () =>
            repairShopSettings?.features.itemJobsFromOpenApi &&
            repairShopSettings?.features.itemJobs,
        [repairShopSettings]
    );

    const mutation = useMutation({
        mutationFn: async (newValue: string) => {
            if (!getItemJobsFromOpenApi) {
                await spreadsheet.updateEstimate(estimateId, masterItemId, {
                    partsNumber: newValue,
                });
                return;
            }

            const data = await EstimateAPI.getInventoryItem(
                newValue,
                order?.repairOrderNumber ?? ''
            );

            if (isValidPartData(data) || isValidLaborData(data)) {
                const customizableFields = getCustomizableFieldsToUpdate(
                    estimateId,
                    masterItemId,
                    allCustomFields,
                    data.customizableFields
                );

                await spreadsheet.updateEstimate(
                    estimateId,
                    masterItemId,
                    {
                        partsNumber: data.partId || data.laborId,
                        name: data.partName || data.laborName,
                        availability: data.availability,
                        quantity: data.quantity,
                        partUnitPrice: data.partUnitPrice,
                        hours: data.laborHours,
                        hourUnitPrice: data.laborHourPrice,
                        discount: data.discount,
                    },
                    customizableFields
                );

                toasters.success(
                    t('menuPricing.estimateUpdatedBody'),
                    t('menuPricing.estimateUpdatedTitle')
                );
            } else {
                toasters.danger(`${t('toasters.noResultsFoundFor')} "${newValue}"`);
                await spreadsheet.updateEstimate(estimateId, masterItemId, {
                    partsNumber: newValue,
                });
            }
        },
        onError: async (_, newValue: string) => {
            toasters.danger(`${t('toasters.noResultsFoundFor')} "${newValue}"`);
            await spreadsheet.updateEstimate(estimateId, masterItemId, {
                partsNumber: newValue,
            });
        },
    });

    return { updateInventory: mutation.mutateAsync, isLoading: mutation.isLoading };
};

export default function PartNumberCell({
    estimateId,
    masterItemId,
    disabled,
    ...props
}: PartNumberCellProps) {
    const partNumber = useAppSelector((r) => selectPartNumber(r, estimateId));
    const [value, setValue] = useState(partNumber ?? '');
    const { updateInventory } = useInventoryUpdate(estimateId, masterItemId);

    useEffect(() => {
        setValue(partNumber ?? '');
    }, [partNumber]);

    return (
        <TextCell
            value={value}
            onChange={setValue}
            estimateId={estimateId}
            onBlur={() => {
                if (partNumber !== value) {
                    updateInventory(value);
                }
            }}
            onClear={(saveImmediately) => {
                setValue('');
                if (partNumber !== '' && saveImmediately) {
                    updateInventory('');
                }
            }}
            disabled={disabled}
            {...props}
        />
    );
}
