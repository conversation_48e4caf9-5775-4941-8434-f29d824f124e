import { IconButton, styled, TooltipProps } from '@mui/material';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import ArrowTooltip from '../../../../../common/components/Tooltip';
import useToasters from '../../../../../common/hooks/useToasters';
import { useAppTranslation } from '../../../../../common/hooks/useAppTranslation';

export type CopyTooltipProps = {
    text: string;
    tooltipText: string;
    tooltipPosition?: string;
};

export const CopyTooltip = ({ tooltipPosition, text, tooltipText }: CopyTooltipProps) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const copyToClipboard = async () => {
        navigator.clipboard.writeText(text).then(() => {
            toasters.success('', t('orderDetails.tooltipTextCopied'));
        });
    };
    return (
        <ArrowTooltip
            content={tooltipText}
            position={(tooltipPosition || 'bottom') as TooltipProps['placement']}
        >
            <IconButton style={{ zIndex: 1 }} size="small" onClick={() => copyToClipboard()}>
                <StyledCopyIcon />
            </IconButton>
        </ArrowTooltip>
    );
};

export default CopyTooltip;

const StyledCopyIcon = styled(CopyIcon)(({ theme }) => ({
    '& path': {
        fill: theme.palette.neutral[5],
    },
    '&:hover path': {
        fill: theme.palette.primary.main,
    },
}));
