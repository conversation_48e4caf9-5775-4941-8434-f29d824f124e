import { Box, IconButton, InputAdornment, Popover, styled } from '@mui/material';
import { ItemEstimateCommentAPI } from 'api/ItemEstimateCommentAPI';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import TextF<PERSON><PERSON>ield from 'common/components/Inputs/TextField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import Tag from 'common/components/Tag';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useInputValue } from 'common/hooks/useInputValue';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import ItemEstimateCommentDto from 'datacontracts/InspectionForms/ItemEstimateCommentDto';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';

export type CommentsModalProps = {
    itemEstimateId: number;
    onClose: () => void;
    open: boolean;
    target: (ref: (e: HTMLElement | null) => void) => React.ReactNode;
};

export default function CommentsModal({
    itemEstimateId,
    onClose,
    target,
    open,
}: CommentsModalProps) {
    const { callApi } = useApiCall();
    const [comments, setComments] = useState<ItemEstimateCommentDto[]>([]);
    const [commentsWereFetched, setCommentsWereFetched] = useState(false);
    const [text, setText] = useInputValue('');
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    useEffect(() => {
        if (!open || commentsWereFetched) return;
        callApi(() => ItemEstimateCommentAPI.findByItemEstimateId(itemEstimateId)).then(
            ({ comments }) => {
                setCommentsWereFetched(true);
                setComments(comments);
            }
        );
    }, [callApi, itemEstimateId, commentsWereFetched, open]);

    const isValid = text.trim() !== '';

    const addComment = async () => {
        if (!isValid) return;

        setComments([...comments, { itemEstimateId, id: -1, text }]);
        setText('');
        try {
            const comment = await callApi(() =>
                ItemEstimateCommentAPI.createComment({ itemEstimateId, text })
            );
            setComments([...comments, comment]);
            const notification = new NotificationData(
                t('inspectionForms.spreadsheetView.availabilityCommentsUpdatedText'),
                t('inspectionForms.spreadsheetView.availabilityCommentsUpdated'),
                NotificationType.success
            );
            dispatch(setNewToaster(notification));
        } catch {
            // NOTE: callApi will create error toaster
            setComments(comments);
        }
    };

    const deleteComment = async (id: number) => {
        if (id === -1) return;
        setComments(comments.filter((c) => c.id !== id));
        try {
            await callApi(() => ItemEstimateCommentAPI.deleteComment(id));
            const notification = new NotificationData(
                t('inspectionForms.spreadsheetView.availabilityCommentDeletedText'),
                t('inspectionForms.spreadsheetView.availabilityCommentDeleted'),
                NotificationType.success
            );
            dispatch(setNewToaster(notification));
        } catch {
            setComments(comments);
        }
    };

    return (
        <>
            {target(setAnchorEl)}
            {open && (
                <Popover
                    onClick={(e) => e.stopPropagation()}
                    onKeyDown={(e) => e.stopPropagation()}
                    onClose={onClose}
                    open={open}
                    anchorEl={anchorEl}
                    slotProps={{
                        paper: { sx: { maxWidth: 400 } },
                    }}
                    // anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
                    // transformOrigin={{ vertical: 'top', horizontal: 'center' }}
                >
                    <DivContent>
                        <DivHeader>
                            {t('inspectionForms.spreadsheetView.availabilityComments', {
                                n: comments.length,
                            })}
                        </DivHeader>
                        <Box display="flex" flexWrap="wrap" alignContent="flex-start" gap="10px">
                            {comments.map((c) => (
                                <Tag onClose={() => deleteComment(c.id)} key={c.id}>
                                    {c.text}
                                </Tag>
                            ))}
                        </Box>
                    </DivContent>
                    <DivFooter>
                        <TextFormField
                            placeholder={t(
                                'inspectionForms.spreadsheetView.availabilityCommentPlaceholder'
                            )}
                            value={text}
                            onChange={setText}
                            endAdornment={
                                <InputAdornment position="end">
                                    <IconButton
                                        disabled={!isValid}
                                        size="small"
                                        onClick={addComment}
                                    >
                                        <CheckIcon fill={isValid ? undefined : Colors.Neutral6} />
                                    </IconButton>
                                </InputAdornment>
                            }
                            onEnterPress={addComment}
                            onEscPress={onClose}
                            name="newComment"
                            cmosVariant="roundedGrey"
                        />
                        <IconButton size="small" onClick={onClose}>
                            <CloseIcon fill={Colors.Error} />
                        </IconButton>
                    </DivFooter>
                </Popover>
            )}
        </>
    );
}

const DivContent = styled('div')({
    padding: 20,
    maxHeight: 300,
    overflowY: 'scroll',
    ...scrollbarStyle(),
});

const DivHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[5],
    paddingBottom: 28,
}));

const DivFooter = styled('div')({
    marginTop: 20,
    paddingLeft: 20,
    paddingRight: 20,
    paddingBottom: 20,
    flexWrap: 'nowrap',
    display: 'flex',
});
