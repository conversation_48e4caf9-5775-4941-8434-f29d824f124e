import { IconButton } from '@mui/material';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import React, { forwardRef, useCallback } from 'react';

export interface OpenCommentsButtonProps {
    onClick: () => void;
    disabled?: boolean;
    fill?: string;
    noPadding?: boolean;
}

const OpenCommentsButton = forwardRef<HTMLButtonElement, OpenCommentsButtonProps>(
    ({ onClick, disabled, fill, noPadding = false }, ref) => {
        const onClickCallback = useCallback(
            (e: React.MouseEvent<HTMLElement>) => {
                e.stopPropagation();
                onClick();
            },
            [onClick]
        );

        return (
            <IconButton
                style={{ zIndex: 1, padding: noPadding ? 0 : 5 }}
                size="small"
                ref={ref}
                disabled={disabled}
                onClick={onClickCallback}
            >
                <CommentsIcon fill={fill} />
            </IconButton>
        );
    }
);

export default OpenCommentsButton;
