import React, { createContext, Dispatch, SetStateAction, useContext } from 'react';

export const TabSelectionContext = createContext<any>(null);

type TabSelectionProviderProps = {
    children: React.ReactNode;
    setSection: Dispatch<SetStateAction<string>>;
    section: string;
};

export const TabSelectionProvider = ({
    setSection,
    section,
    children,
}: TabSelectionProviderProps) => {
    const setInfoSection = () => setSection('info');
    const setEstimateSection = () => setSection('estimate');
    return (
        <TabSelectionContext.Provider value={{ setInfoSection, setEstimateSection, section }}>
            {children}
        </TabSelectionContext.Provider>
    );
};

export const useTabSelection = () => useContext(TabSelectionContext);
