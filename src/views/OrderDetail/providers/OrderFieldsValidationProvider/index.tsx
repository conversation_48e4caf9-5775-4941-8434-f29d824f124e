import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { useTabSelection } from '../TabSelectionProvider';
import { selectOrderFieldValidationData } from './generalInfoFieldValues';
import {
    EstimateValueFieldFormat,
    GeneralInfoValueFieldFormat,
    IUseOrderDetailsValidationsContext,
    OrderDetailsValidationsProviderProps,
    RedirectInvalidField,
} from './types';

const OrderDetailsValidationsContext = createContext<IUseOrderDetailsValidationsContext | null>(
    null
);

export const OrderDetailsValidationsProvider = ({
    children,
}: OrderDetailsValidationsProviderProps) => {
    const { setInfoSection } = useTabSelection();
    const [redirectField, setRedirectField] = useState<RedirectInvalidField | null>(null);
    const [startRedirect, setStartRedirect] = useState(false);

    const generalInfoValueFields = useAppSelector(selectOrderFieldValidationData);

    const allInvalidValueFields = useMemo(
        () => generalInfoValueFields.filter((field) => !field.value),
        [generalInfoValueFields]
    );

    const isValid = allInvalidValueFields.length === 0;

    const invalidFields = allInvalidValueFields.map((x) => ({
        name: x.fieldName,
        isPredefined: x.isPredefined,
    }));

    const cleanRedirect = () => setRedirectField(null);

    useEffect(() => {
        if (startRedirect) {
            setRedirectField(() => {
                const field = allInvalidValueFields[0] as GeneralInfoValueFieldFormat &
                    EstimateValueFieldFormat;

                if ((field as GeneralInfoValueFieldFormat).section === 'info') {
                    setInfoSection();

                    const generalField = field as GeneralInfoValueFieldFormat;
                    return {
                        fieldId: generalField.fieldId,
                        tab: generalField.section,
                        name: generalField.fieldName,
                        isPredefined: generalField.isPredefined,
                    };
                }

                if ((field as EstimateValueFieldFormat).section === 'estimate') {
                    const estimateField = field as EstimateValueFieldFormat;
                    const firstInvalidEstimate = estimateField.estimates.find(
                        (e) => !Boolean(e.value)
                    );

                    return {
                        fieldId: estimateField.fieldId,
                        tab: estimateField.section,
                        estimateId: firstInvalidEstimate
                            ? firstInvalidEstimate.estimateId.toString()
                            : undefined,
                        name: estimateField.fieldName,
                        isPredefined: estimateField.isPredefined,
                    };
                }

                return null;
            });

            setStartRedirect(false);
        }
    }, [startRedirect, allInvalidValueFields, setInfoSection]);

    const focusElementInsideContainer = useCallback((focusRef?: () => void) => {
        if (focusRef) focusRef();
        cleanRedirect();
        setStartRedirect(false);
    }, []);

    return (
        <OrderDetailsValidationsContext.Provider
            value={{
                isValid,
                invalidFields,
                redirectField,
                setStartRedirect,
                focusElementInsideContainer,
            }}
        >
            {children}
        </OrderDetailsValidationsContext.Provider>
    );
};

export const useOrderDetailsValidationsContext = () => {
    const context = useContext(OrderDetailsValidationsContext);
    if (!context) {
        throw new Error(
            'useOrderDetailsValidationsContext must be used within a OrderDetailsValidationsProvider'
        );
    }
    return context;
};
