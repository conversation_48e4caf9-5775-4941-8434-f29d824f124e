/* eslint-disable @typescript-eslint/no-explicit-any */
import { isPredefinedField, PREDEFINED_FIELDS } from 'api/fields';
import { PriorityLevel } from 'common/constants/PriorityLevel';
import { EstimateDto } from 'datacontracts/InspectionForms/EstimateDto';
import { createSelector } from 'reselect';
import {
    RequiredCol,
    selectEstimatesSet,
    selectItemsSet,
    selectRequiredColumns,
} from 'store/slices/order/orderInspectionFormsSlice';
import { EstimateValueFieldFormat } from './types';

const selectVisibleEstimates = createSelector(
    [selectItemsSet, selectEstimatesSet],
    (items, estimates) => {
        const estimateIds = Object.entries(items)
            .filter(
                ([, item]) =>
                    item.priority !== PriorityLevel.Low && item.priority !== PriorityLevel.NA
            )
            .map(([, item]) => item.estimates);

        const visibleEstimates = Object.values(estimates).filter((estimate) =>
            estimateIds.some((ids) => ids.some((id) => id === estimate.estimateId))
        );

        return { estimateIds, visibleEstimates };
    }
);

export const selectIncompleteEstimateFieldValues = createSelector(
    [selectVisibleEstimates, selectRequiredColumns],
    ({ visibleEstimates }, columns) => {
        const estimateValueFields: EstimateValueFieldFormat[] = columns
            .filter((col) => col.isRequired)
            .map((col) => ({
                section: 'estimate',
                fieldId: col.id,
                fieldName: col.name,
                isPredefined: isPredefinedField(col.type),
                estimates: visibleEstimates.map((estimate) => ({
                    estimateId: estimate.estimateId,
                    value: getFieldValue(col, estimate),
                })),
            }));
        return estimateValueFields;
    }
);

export function getFieldValue(field: RequiredCol, estimate: EstimateDto) {
    switch (field.name) {
        case PREDEFINED_FIELDS.PARTS_AVAIL:
            return estimate.availability;

        case PREDEFINED_FIELDS.PARTS_QTY:
            return estimate.quantity;

        case PREDEFINED_FIELDS.PARTS_NUMBER:
            return estimate.partsNumber;

        case PREDEFINED_FIELDS.PARTS_COST_UNIT:
            return estimate.partUnitCost;

        case PREDEFINED_FIELDS.PARTS_PRICE_UNIT:
            return estimate.partUnitPrice;

        case PREDEFINED_FIELDS.LABOR_HRS:
            return estimate.hours;

        case PREDEFINED_FIELDS.LABOR_PRICE_HR:
            return estimate.hourUnitPrice;

        default: {
            return field.estimateValues?.find((x) => x.estimateId === estimate.estimateId)?.value;
        }
    }
}
