import { Dispatch, SetStateAction } from 'react';

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface OrderDetailsValidationsProviderProps {
    children: React.ReactNode;
}

export interface ValueFieldFormat {
    fieldId: string;
    fieldName: string;
    isPredefined: boolean;
}

export interface EstimateValueFieldFormat extends ValueFieldFormat {
    section: 'estimate';
    estimates: {
        estimateId: number;
        value?: any;
    }[];
}

export interface GeneralInfoValueFieldFormat extends ValueFieldFormat {
    section: 'info';
    value: string;
}

export interface ValueFieldFormatRedirect {
    redirectId: string;
    tab: 'info' | 'estimate';
}

export type RedirectInvalidField = {
    fieldId: string;
    estimateId?: string;
    tab: 'info' | 'estimate';
    name: string;
    isPredefined: boolean;
};

export type InvalidField = {
    name: string;
    isPredefined: boolean;
};

export interface IUseOrderDetailsValidationsContext {
    isValid: boolean;
    invalidFields: InvalidField[];
    redirectField: RedirectInvalidField | null;
    setStartRedirect: Dispatch<SetStateAction<boolean>>;
    focusElementInsideContainer: any;
}
