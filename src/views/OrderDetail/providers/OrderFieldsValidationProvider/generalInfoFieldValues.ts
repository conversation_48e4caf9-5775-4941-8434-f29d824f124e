/* eslint-disable @typescript-eslint/no-explicit-any */
import { isPredefinedField, PREDEFINED_FIELDS } from 'api/fields';
import { FieldValue } from 'api/viewByCost';
import { createSelector } from 'reselect';
import {
    OrderState,
    selectCustomerFields,
    selectOrderData,
    selectOrderInfoFields,
    selectVehicleFields,
} from 'store/slices/order/orderDetails';
import { RequiredCol } from 'store/slices/order/orderInspectionFormsSlice';
import { GeneralInfoValueFieldFormat } from './types';

export type FieldValueRequired = FieldValue & { isRequired: boolean; value?: any };
export const selectOrderFieldValidationData = createSelector(
    [selectCustomerFields, selectVehicleFields, selectOrderInfoFields, selectOrderData],
    (customerFields, vehicleFields, orderInfoFields, data) => {
        const columns = [
            ...customerFields,
            ...vehicleFields,
            ...orderInfoFields,
        ] as FieldValueRequired[];

        const generalInfoValueFields: GeneralInfoValueFieldFormat[] = columns
            .filter((col) => col.isRequired)
            .map((col) => ({
                section: 'info',
                fieldId: col.id,
                fieldName: col.name,
                isPredefined: isPredefinedField(col.type),
                value: getFieldValue(data, col),
            }));

        return generalInfoValueFields;
    }
);

const getFieldValue = (orderData: OrderState, field: RequiredCol): any => {
    if (field.value) {
        if (field.type === 'Select' || field.type === 'MultiSelect') {
            const result = JSON.parse(field.value);
            return result.value.length > 0;
        }

        return field.value;
    }

    switch (field.name) {
        case PREDEFINED_FIELDS.CUSTOMER_NAME:
            return orderData.customer.firstName;
        case PREDEFINED_FIELDS.CUSTOMER_EMAIL:
            return orderData.customer.email;
        case PREDEFINED_FIELDS.CUSTOMER_LANDLINE:
            return orderData.customer.landline;
        case PREDEFINED_FIELDS.CUSTOMER_MOBILE:
            return orderData.customer.mobile;
        case PREDEFINED_FIELDS.CUSTOMER_BUSINESS_NAME:
            return orderData.customer.businessName;
        case PREDEFINED_FIELDS.CUSTOMER_ID_DOCUMENT:
            return orderData.customer.documentId;
        case PREDEFINED_FIELDS.CUSTOMER_PAYMENT_METHOD:
            return orderData.customer.paymentMethod;
        case PREDEFINED_FIELDS.VEHICLE_VIN:
            return orderData.vehicle.vin;
        case PREDEFINED_FIELDS.VEHICLE_BRAND:
            return orderData.vehicle.brand;
        case PREDEFINED_FIELDS.VEHICLE_MODEL:
            return orderData.vehicle.model;
        case PREDEFINED_FIELDS.VEHICLE_YEAR:
            return orderData.vehicle.year;
        case PREDEFINED_FIELDS.VEHICLE_MILEAGE:
            return orderData.vehicle.mileage;
        case PREDEFINED_FIELDS.VEHICLE_PLATES:
            return orderData.vehicle.plates;
        case PREDEFINED_FIELDS.ORDER_PHASE:
            return orderData.phaseId;
        case PREDEFINED_FIELDS.ORDER_TOWER:
            return orderData.tower;
        case PREDEFINED_FIELDS.ORDER_TYPE:
            return orderData.orderTypeId;
        case PREDEFINED_FIELDS.ORDER_DELIVERY_DATE:
            return orderData.deliveryDate;
        case PREDEFINED_FIELDS.ORDER_IN_CHARGE:
            return orderData.inChargeUserId;
        case PREDEFINED_FIELDS.ORDER_ASSIGNED_TO:
            return orderData.assignedToUserId;
        case PREDEFINED_FIELDS.ORDER_NOTE:
            return true;
        default: {
            return '';
        }
    }
};
