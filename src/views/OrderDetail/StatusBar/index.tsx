import { Grid, styled } from '@mui/material';
import { GetTimePassed } from 'common/DateTimeHelpers';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { PhotoIcon } from 'common/components/Icons/PhotoIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { UserIcon } from 'common/components/Icons/UserIcon';
import { VideoIcon } from 'common/components/Icons/VideoIcon';
import { OrderStatus, OrderStatusLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import moment from 'moment';
import React from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectOrder, selectOrderLoading } from 'store/slices/order/orderDetails';

export type OrderStats = {
    uploadTime: Date;
    lastUpdateTime: Date;
    inspectionLink: string;
    inspectionFormStatus: OrderStatus;
    consumerViews: number;
    internalViews: number;
    phoneCallsCount: number;
    photoCount: number;
    videoCount: number;
    lastCommunicationTime: Date;
    appointmentNumber?: number;
};

const selectStats = createSelector([selectOrder, selectOrderLoading], (o, loading) =>
    o
        ? {
              uploadedAt: moment(o.uploadTime),
              lastUpdatedAt: moment(o.lastUpdateTime),
              inspectionLink: o.inspectionLink,
              inspectionFormStatus: o.inspectionFormStatus,
              consumerViews: o.consumerViews,
              internalViews: o.internalViews,
              phoneCalls: o.phoneCallsCount,
              photo: o.photoCount,
              video: o.videoCount,
              lastCommunication: moment(o.lastCommunicationTime),
              appointmentNumber: o.appointmentNumber,
              loading,
          }
        : null
);

const StatusColumn = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
});

const DivRoot = styled('div')({
    backgroundColor: '#fafafa',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomColor: '#dbdcdd',
    borderBottomStyle: 'solid',
    borderBottomWidth: 1,
    paddingLeft: 40,
    paddingRight: 40,
    paddingTop: 18,
    paddingBottom: 14,
});

const SpanLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'bold',
}));

const SpanNumber = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
}));

const ALink = styled('a')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
    outlineOffset: 2,
    '&:focus': {
        outline: '1px dashed #444',
    },
}));

const StatusBar = React.memo(() => {
    const { t } = useAppTranslation();
    const stats = useAppSelector(selectStats);

    return (
        <DivRoot>
            <Grid container spacing={0} justifyContent="space-between">
                <StatusColumn>
                    <SpanLabel>{t('orders.tableHeaders.appointmentNumber')}</SpanLabel>
                    <SpanNumber>{stats && (stats.appointmentNumber || '--')}</SpanNumber>
                </StatusColumn>
                <StatusColumn>
                    <SpanLabel>{t('orderDetails.evidence')}</SpanLabel>
                    {
                        <Grid container spacing={1}>
                            <Grid item xs="auto">
                                <Grid container alignItems="center">
                                    <VideoIcon fill={Colors.Neutral6} />
                                    <SpanNumber>{stats?.video}</SpanNumber>
                                </Grid>
                            </Grid>
                            <Grid item xs="auto">
                                <Grid container alignItems="center">
                                    <PhotoIcon fill={Colors.Neutral6} />
                                    <SpanNumber>{stats?.photo}</SpanNumber>
                                </Grid>
                            </Grid>
                            <Grid item xs="auto">
                                <Grid container alignItems="center">
                                    <PhoneIcon fill={Colors.Neutral6} />
                                    <SpanNumber>{stats?.phoneCalls}</SpanNumber>
                                </Grid>
                            </Grid>
                        </Grid>
                    }
                </StatusColumn>
                <StatusColumn>
                    <SpanLabel>{t('orderDetails.views')}</SpanLabel>
                    {
                        <Grid container spacing={1}>
                            <Grid item xs="auto">
                                <Grid container alignItems="center">
                                    <ShowIcon fill={Colors.Neutral6} />
                                    <SpanNumber>{stats?.consumerViews}</SpanNumber>
                                </Grid>
                            </Grid>
                            <Grid item xs="auto">
                                <Grid container alignItems="center">
                                    <UserIcon fill={Colors.Neutral6} />
                                    <SpanNumber>{stats?.internalViews}</SpanNumber>
                                </Grid>
                            </Grid>
                        </Grid>
                    }
                </StatusColumn>
                <StatusColumn>
                    <SpanLabel>{t('orderDetails.lastCommunication')}</SpanLabel>
                    <SpanNumber>
                        {stats?.inspectionFormStatus &&
                            t(OrderStatusLabel(stats.inspectionFormStatus)) +
                                ', ' +
                                GetTimePassed(stats.lastCommunication, t)}
                    </SpanNumber>
                </StatusColumn>
                <StatusColumn>
                    <SpanLabel>{t('orderDetails.orderLink')}</SpanLabel>
                    {
                        <ALink
                            href={stats?.inspectionLink}
                            target="_blank"
                            rel="noreferrer"
                            style={{ textDecoration: 'none' }}
                        >
                            {stats?.inspectionLink}
                        </ALink>
                    }
                </StatusColumn>
                <StatusColumn>
                    <SpanLabel>{t('orders.tableHeaders.charged')}</SpanLabel>
                    <SpanNumber>
                        {stats && stats.uploadedAt.format(t('dateFormats.long'))}
                    </SpanNumber>
                </StatusColumn>
                <StatusColumn>
                    <SpanLabel>{t('orderDetails.lastUpdate')}</SpanLabel>
                    <SpanNumber>
                        {stats && stats.lastUpdatedAt.format(t('dateFormats.long'))}
                    </SpanNumber>
                </StatusColumn>
            </Grid>
        </DivRoot>
    );
});

export default StatusBar;
