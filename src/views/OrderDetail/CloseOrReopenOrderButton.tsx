import { Box, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import OrdersApi from 'api/orders';
import { Button } from 'common/components/Button';
import { ButtonProps } from 'common/components/Button/ButtonProps';
import {
    OrderJobsInProgressCheckProvider,
    useOrderJobsInProgressCheck,
} from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import isEqual from 'lodash/isEqual';
import { useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    selectRepairShopIntegrationAccountName,
    selectSettings,
} from 'store/slices/globalSettingsSlice';
import { reloadOrderThunk, selectOrder } from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { Phases } from '../../common/constants';

const selectData = createSelector([selectOrder, selectUserPermission], (o, perm) => ({
    shopId: o?.shopId,
    orderId: o?.repairOrderId,
    orderKey: o?.key,
    orderNumber: o?.repairOrderNumber,
    isClosed: o?.phaseId === Phases.Closed,
    canEdit: perm.allowEditOrders,
    canReopen: perm.allowReopenOrders,
}));

const BoxComponent = styled('div')({
    padding: '30px 50px 20px 50px',
});

export default function CloseOrReopenOrderButton() {
    const toasters = useToasters();
    const [closeModalOpen, setCloseModalOpen] = useState(false);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { shopId, orderId, orderKey, orderNumber, isClosed, canEdit, canReopen } = useAppSelector(
        selectData,
        isEqual
    );
    const isEnterprise = useIsEnterpriseRoute();
    const accountIntegratedName = useAppSelector(selectRepairShopIntegrationAccountName);
    const { repairShopSettings } = useAppSelector(selectSettings);
    const shouldShowReopenButton = isClosed && canReopen && !isEnterprise;

    const closeOrder = useMutation(
        async () => {
            if (orderId === undefined)
                throw new Error('orderId is undefined in CloseOrReopenOrderButton');
            if (shopId === undefined)
                throw new Error('shopId is undefined in CloseOrReopenOrderButton');
            if (!!isClosed) throw new Error('order is already closed in CloseOrReopenOrderButton');
            if (isEnterprise) {
                await EnterpriseOrdersApi.orders.closeOrder(shopId, orderId);
            } else {
                await OrdersApi.closeOrder(orderId);
            }
        },
        {
            onSettled: () => {
                dispatch(reloadOrderThunk());
                setCloseModalOpen(false);
            },
            onSuccess: () => {
                if (repairShopSettings?.features.closeOrderEnabled)
                    toasters.success(
                        t('orderDetails.closeOrderIntegratedAccount.success.description', {
                            accountIntegratedName,
                        }),
                        t('orderDetails.closeOrderIntegratedAccount.success.title')
                    );
            },
            onError: (error: any) => {
                if (repairShopSettings?.features.closeOrderEnabled) {
                    const errorMessage = error.response.data?.message;
                    toasters.danger(
                        `${t(
                            'orderDetails.closeOrderIntegratedAccount.error.description'
                        )}: ${errorMessage}`,
                        t('orderDetails.closeOrderIntegratedAccount.error.title', {
                            accountIntegratedName,
                        })
                    );
                }
            },
        }
    );

    const reopenOrder = useMutation(
        async () => {
            if (orderId === undefined)
                throw new Error('orderId is undefined in CloseOrReopenOrderButton');
            if (shopId === undefined)
                throw new Error('shopId is undefined in CloseOrReopenOrderButton');
            if (!isClosed) throw new Error('order is already opened in CloseOrReopenOrderButton');

            await OrdersApi.reopenOrder(orderId);
        },
        {
            onSettled: () => {
                dispatch(reloadOrderThunk());
            },
            onError: (error: any) => {
                if (repairShopSettings?.features.closeOrderEnabled) {
                    const errorMessage = error.response.data?.message;
                    toasters.danger(
                        `${t(
                            'orderDetails.closeOrderIntegratedAccount.error.description'
                        )}: ${errorMessage}`,
                        t('orderDetails.closeOrderIntegratedAccount.error.title', {
                            accountIntegratedName,
                        })
                    );
                }
            },
        }
    );

    return (
        <>
            {shouldShowReopenButton ? (
                <>
                    <Button
                        w="md"
                        cmosVariant={'stroke'}
                        disabled={!canReopen || reopenOrder.isLoading || orderId === undefined}
                        showLoader={reopenOrder.isLoading}
                        onClick={() => reopenOrder.mutate()}
                        label={t('orderDetails.reopenOrder')}
                    />
                </>
            ) : (
                <>
                    <Button
                        w="md"
                        cmosVariant={'stroke'}
                        label={t('orderDetails.closeOrder')}
                        disabled={isClosed || !canEdit || orderId === undefined}
                        onClick={() => setCloseModalOpen(true)}
                    />
                    <Modal
                        boxComponent={BoxComponent}
                        open={closeModalOpen}
                        onClose={() => setCloseModalOpen(false)}
                    >
                        <div
                            style={{
                                ...FontPrimary(HeaderStyles.H4_18px, true, Colors.Neutral8),
                            }}
                        >
                            {t('orderDetails.closeOrder')}
                        </div>
                        <div
                            style={{
                                ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Neutral8),
                                marginTop: 11,
                                marginBottom: 25,
                            }}
                        >
                            {t('orderDetails.areYouSureToClose')}
                        </div>
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                            <Button
                                blockMode
                                w="md"
                                cmosVariant={'filled'}
                                color={Colors.Neutral3}
                                onClick={() => setCloseModalOpen(false)}
                                label={t('commonLabels.cancel')}
                            />
                            <OrderJobsInProgressCheckProvider>
                                <ConfirmCloseOrderButton
                                    orderKey={orderKey ?? ''}
                                    orderNumber={orderNumber ?? ''}
                                    disabled={closeOrder.isLoading || orderId === undefined}
                                    showLoader={closeOrder.isLoading}
                                    onClick={() => closeOrder.mutate()}
                                />
                            </OrderJobsInProgressCheckProvider>
                        </Box>
                    </Modal>
                </>
            )}
        </>
    );
}

type ConfirmCloseOrderButtonProps = Pick<ButtonProps, 'disabled' | 'showLoader'> & {
    orderKey: string;
    orderNumber: string;
    onClick: () => void;
};

function ConfirmCloseOrderButton({
    disabled,
    showLoader,
    orderKey,
    orderNumber,
    onClick,
}: ConfirmCloseOrderButtonProps) {
    const { t } = useAppTranslation();
    const [loading, setLoading] = useState(false);
    const orderJobsInProgressCheck = useOrderJobsInProgressCheck();

    const handleClick = () => {
        orderJobsInProgressCheck.checkOrderJobsInProgress(
            { destinationPhaseId: Phases.Closed, orderId: orderKey, orderNumber },
            () => {
                onClick();
                setLoading(false);
            },
            () => setLoading(false)
        );
    };

    return (
        <Button
            blockMode
            w="md"
            cmosVariant={'filled'}
            color={Colors.CM1}
            disabled={disabled || loading}
            showLoader={showLoader || loading}
            onClick={() => handleClick()}
            label={t('orderDetails.closeOrder')}
        />
    );
}
