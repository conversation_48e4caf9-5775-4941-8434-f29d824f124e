import { IconButton } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { FolderOpenIcon } from 'common/components/Icons/FolderOpenIcon';
import AttachedFilesModal from 'common/components/AttachedFiles';
import OrdersApi from 'api/orders';
import Tooltip from 'common/components/Tooltip';

export default function AttachedFilesButton({ repairOrderId }: { repairOrderId: number }) {
    const { t } = useAppTranslation();
    const [showAttachedFileModal, setAttachedFileModal] = useState(false);

    return (
        <>
            <Tooltip content={t('commonLabels.attachedFiles')}>
                <IconButton onClick={() => setAttachedFileModal(true)} size="small">
                    <FolderOpenIcon fill={'var(--cm1)'} />
                </IconButton>
            </Tooltip>
            <AttachedFilesModal
                id={repairOrderId}
                open={showAttachedFileModal}
                onClose={() => setAttachedFileModal(false)}
                fetchAttachments={() => OrdersApi.getOrderAttachments(repairOrderId)}
            />
        </>
    );
}
