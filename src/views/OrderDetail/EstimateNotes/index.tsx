import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import EstimateNoteAPI from '../../../api/EstimateNote';
import { EstimateNoteDto, EstimateNoteType } from '../../../datacontracts/EstimateNoteDto';
import { useAppSelector } from '../../../store';
import { selectIsOrderClosed } from '../../../store/slices/order/orderDetails';
import { selectUserPermission } from '../../../store/slices/user/selectors';
import AddNote from './AddNote';
import Note from './Note';

interface EstimateNotesParams {
    orderId: number;
}

export const EstimateNotes = ({ orderId }: EstimateNotesParams) => {
    const userPermission = useSelector(selectUserPermission);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);

    const [notes, setNotes] = useState<EstimateNoteDto[]>([]);

    const { data, refetch } = useQuery(['order', orderId, 'estimate-notes'], () =>
        EstimateNoteAPI.getNotes(orderId)
    );

    useEffect(() => {
        if (data) {
            setNotes(data);
        }
    }, [data]);

    const handleDelete = (note: EstimateNoteDto) => {
        setNotes((value) => [...value.filter((n) => n.estimateNoteId !== note.estimateNoteId)]);
        refetch();
    };

    const handleAddNote = (note: EstimateNoteDto) => {
        setNotes((value) => [...value, note]);
        refetch();
    };

    const handleEdit = (note: EstimateNoteDto) => {
        setNotes((value) => [
            ...value.map((n) => (n.estimateNoteId === note.estimateNoteId ? note : n)),
        ]);
        refetch();
    };

    return (
        <DivRoot>
            <InternalEstimateNotes
                orderId={orderId}
                notes={notes}
                notesType="ForCustomer"
                disabled={!userPermission.allowEditOrders || orderIsClosed}
                onAddNote={handleAddNote}
                onDeleteNote={handleDelete}
                onEditNote={handleEdit}
            />
            <InternalEstimateNotes
                orderId={orderId}
                notes={notes}
                notesType="ForInternal"
                disabled={!userPermission.allowEditOrders || orderIsClosed}
                onAddNote={handleAddNote}
                onDeleteNote={handleDelete}
                onEditNote={handleEdit}
            />
        </DivRoot>
    );
};

type InternalEstimateNotesProps = {
    orderId: number;
    notes: EstimateNoteDto[];
    notesType: EstimateNoteType;
    disabled: boolean;
    onAddNote: (note: EstimateNoteDto) => void;
    onDeleteNote: (note: EstimateNoteDto) => void;
    onEditNote: (note: EstimateNoteDto) => void;
};

function InternalEstimateNotes({
    orderId,
    notes,
    notesType,
    disabled,
    onAddNote,
    onDeleteNote,
    onEditNote,
}: InternalEstimateNotesProps) {
    const { t } = useAppTranslation();

    return (
        <DivContent>
            <DivHeader>
                {notesType === 'ForCustomer'
                    ? t('orderDetails.estimateNotes.notesForCustomer')
                    : t('orderDetails.estimateNotes.notesForInternal')}
            </DivHeader>
            <DivNotesWrapper>
                {notes
                    .filter((x) => x.type === notesType)
                    .map((note) => (
                        <Note
                            key={note.estimateNoteId}
                            note={note}
                            disabled={disabled}
                            onDelete={() => onDeleteNote(note)}
                            onEdit={onEditNote}
                        />
                    ))}
            </DivNotesWrapper>
            <AddNote
                orderId={orderId}
                disabled={disabled}
                noteType={notesType}
                onAddNote={onAddNote}
            />
        </DivContent>
    );
}

const DivRoot = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
});

const DivContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
});

const DivHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h4Roboto,
    color: theme.palette.neutral[7],
    marginBottom: 20,
}));

const DivNotesWrapper = styled('div')({
    marginBottom: 8,
});

export default EstimateNotes;
