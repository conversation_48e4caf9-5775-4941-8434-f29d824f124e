import { styled, Typography } from '@mui/material';
import MuiTooltip, { tooltipClasses, TooltipProps } from '@mui/material/Tooltip';
import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useState } from 'react';
import ThreeDotMenu from 'views/OrderDetail/common/ThreeDotMenu';
import EstimateNoteAPI from '../../../../api/EstimateNote';
import { Button } from '../../../../common/components/Button';
import { LessIcon } from '../../../../common/components/Icons/LessIcon';
import { EstimateNoteDto } from '../../../../datacontracts/EstimateNoteDto';
import EditNote from '../EditNote';
import NoteActivityLogTooltipContent from '../NoteActivityLogTooltipContent';

type NoteProps = {
    note: EstimateNoteDto;
    onDelete: () => void;
    onEdit: (note: EstimateNoteDto) => void;
    disabled?: boolean;
};

export const Note = ({ note, onDelete, onEdit, disabled }: NoteProps) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [isEditMode, setIsEditMode] = useState(false);
    const [showActions, setShowActions] = useState<boolean>(false);

    const deleteMutation = useMutation(
        async () => EstimateNoteAPI.deleteNote(note.estimateNoteId!),
        {
            onSuccess: () => onDelete(),
            onError: () => toasters.warning(t('toasters.errorOccurredWhenSaving')),
        }
    );

    const handleDelete = () => {
        deleteMutation.mutateAsync();
    };

    const updateMutation = useMutation(
        (body: { text: string }) => EstimateNoteAPI.updateNote(note.estimateNoteId, body.text),
        {
            onSuccess: (data) => onEdit(data),
            onError: () => toasters.warning(t('toasters.errorOccurredWhenSaving')),
        }
    );

    const handleSave = async (text: string) => {
        if (note.text !== text) {
            await updateMutation.mutateAsync({ text });
        }

        setIsEditMode(false);
    };

    return (
        <DivRoot>
            <Button
                cmosVariant={'typography'}
                cmosSize={'medium'}
                disabled={disabled}
                iconPosition="right"
                color={'var(--cm1)'}
                Icon={LessIcon}
                onClick={handleDelete}
            />
            <DivDivider />

            {isEditMode && (
                <EditNote
                    text={note.text}
                    onSave={handleSave}
                    onCancel={() => setIsEditMode(false)}
                />
            )}
            {!isEditMode && (
                <Tooltip
                    title={<NoteActivityLogTooltipContent estimateNote={note} />}
                    placement="right"
                >
                    <DivTextWrapper
                        onMouseOver={() => setShowActions(true)}
                        onMouseOut={() => setShowActions(false)}
                    >
                        <Typography
                            component="div"
                            sx={(theme) => ({
                                ...theme.typography.h6Roboto,
                                fontWeight: 400,
                                color: theme.palette.neutral[7],
                                inlineSize: 'auto',
                                overflowWrap: 'break-word',
                                maxWidth: '100%',
                            })}
                        >
                            {note.text}
                        </Typography>
                        {showActions && (
                            <ThreeDotMenu
                                onClose={() => setShowActions(false)}
                                onEdit={() => setIsEditMode(true)}
                                onDelete={handleDelete}
                                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
                            />
                        )}
                    </DivTextWrapper>
                </Tooltip>
            )}
        </DivRoot>
    );
};

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: theme.palette.neutral[1],
        filter: `drop-shadow(0px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.neutral[1],
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const DivRoot = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const DivDivider = styled('div')(({ theme }) => ({
    height: 32,
    borderLeftWidth: 1,
    borderLeftStyle: 'solid',
    borderLeftColor: theme.palette.neutral[4],
    marginLeft: 16,
    marginRight: 16,
}));

const DivTextWrapper = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[8],
    width: '100%',

    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    padding: '8px 4px',
    borderRadius: '4px',

    '&:hover': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

export default Note;
