import { IconButton, InputAdornment, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import CharacterIndicator from 'common/components/Inputs/CharacterIndicator';
import { TextFormField } from 'common/components/Inputs/TextField';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';

type EditNoteProps = {
    text?: string;
    onSave: (text: string) => void;
    onCancel: () => void;
};

export const EditNote = ({ text: initialText = '', onSave, onCancel }: EditNoteProps) => {
    const [text, setText] = useState(initialText);

    const handleApply = () => {
        if (text.trim().length) {
            onSave(text);
        }
    };

    return (
        <DivRoot>
            <DivTextWrapper>
                <TextFormField
                    autoFocus
                    name="text"
                    label=""
                    maxLength={100}
                    value={text}
                    onChange={(event) => {
                        if (event.target.value.length <= 100) {
                            setText(event.target.value);
                        }
                    }}
                    onEnterPress={handleApply}
                    enableEnterComplete
                    multiline
                    endAdornment={
                        <InputAdornment position="end">
                            <IconButton size="small" onClick={handleApply}>
                                <CheckIcon size={IconSize.M} />
                            </IconButton>
                            <CharacterIndicator charactersCount={text.length} />
                        </InputAdornment>
                    }
                />
            </DivTextWrapper>

            <CloseButton
                cmosVariant={'typography'}
                cmosSize={'small'}
                iconPosition="right"
                Icon={() => <CloseIcon fill="var(--neutral6)" />}
                onClick={onCancel}
            />
        </DivRoot>
    );
};

const DivRoot = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const DivTextWrapper = styled('div')({
    width: 320,
});

const CloseButton = styled(Button)({
    marginLeft: 5,
});

export default EditNote;
