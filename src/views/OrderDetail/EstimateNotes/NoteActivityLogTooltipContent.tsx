import { Box, List, ListItem, styled, Typography } from '@mui/material';
import { JobTitle, jobTitleLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { EstimateNoteDto } from 'datacontracts/EstimateNoteDto';
import moment from 'moment';
import { useMemo } from 'react';

type NoteActivityLogTooltipContentProps = {
    estimateNote: EstimateNoteDto;
};

// NOTE (AK) AdditionalNotes and EstimateNotes have the same structure of tooltip with lil differences
// maybe it can be more common component
function NoteActivityLogTooltipContent({ estimateNote }: NoteActivityLogTooltipContentProps) {
    const { t } = useAppTranslation();

    const reversedHistory = useMemo(
        () => (estimateNote.historyNotes ? estimateNote.historyNotes.toReversed() : []),
        [estimateNote.historyNotes]
    );

    return (
        <Box
            component="div"
            sx={(theme) => ({
                maxWidth: '330px',
                maxHeight: '99vh',
                minHeight: '30px',
                padding: '8px',
                background: theme.palette.neutral[1],
                overflowY: 'auto',
                ...scrollbarStyle(),
            })}
        >
            {reversedHistory.length <= 1 ? (
                <HistoryItem
                    isFirst
                    isListItem={false}
                    highlighted={false}
                    userJob={estimateNote.user?.job}
                    userName={estimateNote.user?.name}
                    createdAt={reversedHistory.length ? reversedHistory[0].createdAt : null}
                />
            ) : (
                <>
                    <HistoryItem
                        isFirst={false}
                        isListItem={false}
                        highlighted
                        userJob={estimateNote.user?.job}
                        userName={estimateNote.user?.name}
                        createdAt={reversedHistory[0].createdAt}
                        note={estimateNote.text}
                    />
                    <Typography
                        sx={(theme) => ({
                            fontFamily: 'Roboto',
                            fontSize: '12px',
                            fontStyle: 'normal',
                            fontWeight: 700,
                            lineHeight: 'normal',
                            color: theme.palette.neutral[7],
                        })}
                    >
                        {t('orderDetails.estimateNotes.noteActivityLog')}
                    </Typography>
                    <List sx={{ listStyleType: 'none', padding: '2px 8px 2px 0px' }}>
                        {reversedHistory.length > 0 &&
                            reversedHistory
                                .slice(1, reversedHistory.length)
                                .map((historyNote, index) => (
                                    <ListItem
                                        key={historyNote.id}
                                        sx={(theme) => ({
                                            display: 'list-item',
                                            padding: '2px 0px',
                                            color: theme.palette.neutral[7],
                                        })}
                                    >
                                        <HistoryItem
                                            isFirst={index === reversedHistory.length - 2}
                                            isListItem
                                            highlighted={false}
                                            userJob={estimateNote.user?.job}
                                            userName={historyNote.user?.name}
                                            createdAt={historyNote.createdAt}
                                            note={historyNote.text}
                                        />
                                    </ListItem>
                                ))}
                    </List>
                </>
            )}
        </Box>
    );
}

type HistoryItemProps = {
    isFirst: boolean;
    isListItem: boolean;
    highlighted: boolean;
    userJob: JobTitle | undefined | null;
    userName: string | undefined;
    createdAt: Date | null;
    note?: string;
};

const HistoryItem = ({
    isFirst,
    isListItem,
    highlighted,
    userJob,
    userName,
    createdAt,
    note,
}: HistoryItemProps) => {
    const { t } = useAppTranslation();

    const localCreatedAt = createdAt ? moment.utc(createdAt).local() : null;

    return (
        <Typography
            sx={(theme) => ({
                fontFamily: 'Roboto',
                fontSize: '12px',
                fontStyle: 'normal',
                fontWeight: 400,
                lineHeight: 'normal',
                color: theme.palette.neutral[7],
                marginBottom: highlighted ? '20px' : 'none',
            })}
        >
            <SpanBold bold={highlighted}>
                {isListItem && '- '}
                {`${t(`orderDetails.estimateNotes.${isFirst ? 'addedBy' : 'modifiedBy'}`)} ${`${
                    userJob ? t(jobTitleLabel(userJob)) : ''
                } ${userName}`} ${
                    localCreatedAt
                        ? `${t('orderDetails.estimateNotes.on')} ${localCreatedAt.format(
                              t('dateFormats.shortDate')
                          )} ${t('orderDetails.estimateNotes.at')} ${localCreatedAt.format(
                              t('dateFormats.time')
                          )}`
                        : ''
                }`}
            </SpanBold>
            {note && `: "${note}"`}
        </Typography>
    );
};

const SpanBold = styled('span')<{ bold: boolean }>(({ bold }) => ({
    fontWeight: bold ? 700 : 'inherit',
}));

export default NoteActivityLogTooltipContent;
