import { Box, Typography } from '@mui/material';
import <PERSON><PERSON><PERSON><PERSON>on from '@mui/material/Button';
import { useMutation } from '@tanstack/react-query';
import EstimateNoteAPI from 'api/EstimateNote';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useState } from 'react';
import { PlusIcon } from '../../../../common/components/Icons/PlusIcon';
import { EstimateNoteDto, EstimateNoteType } from '../../../../datacontracts/EstimateNoteDto';
import EditNote from '../EditNote';

type AddNoteProps = {
    className?: string;
    orderId: number;
    noteType: EstimateNoteType;
    onAddNote: (note: EstimateNoteDto) => void;
    disabled?: boolean;
};

function AddNote({ className, orderId, noteType, onAddNote, disabled }: AddNoteProps) {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const [isEditMode, setIsEditMode] = useState(false);

    const handleAddNoteClick = () => {
        setIsEditMode(true);
    };

    const addNoteMutation = useMutation(
        (body: { text: string }) => EstimateNoteAPI.addNote(orderId, body.text, noteType),
        {
            onSuccess: (data) => {
                onAddNote(data);
                setIsEditMode(false);
            },
            onError: () => toasters.warning(t('toasters.errorOccurredWhenSaving')),
        }
    );

    const handleSave = async (text: string) => {
        await addNoteMutation.mutateAsync({ text });
    };

    if (isEditMode) return <EditNote onSave={handleSave} onCancel={() => setIsEditMode(false)} />;

    return (
        <div className={className}>
            <MuiButton
                sx={(theme) => ({
                    border: `1px solid ${theme.palette.neutral[4]}`,
                    borderRadius: '4px !important',
                    background: theme.palette.neutral[2],
                    width: '240px',
                    height: 32,
                })}
                onClick={handleAddNoteClick}
                disabled={disabled}
            >
                <Box
                    component="div"
                    sx={{
                        display: 'block',
                        width: '100%',
                    }}
                >
                    <Typography
                        component="span"
                        sx={(theme) => ({
                            ...theme.typography.h6Inter,
                            fontWeight: 'normal',
                            color: theme.palette.neutral[7],
                            textTransform: 'none',
                            float: 'left',
                            height: 24,
                            display: 'flex',
                            alignItems: 'center',
                        })}
                    >
                        {t('orderDetails.estimateNotes.addNote')}
                    </Typography>
                    <Box
                        component="span"
                        sx={{
                            float: 'right',
                            height: 24,
                        }}
                    >
                        <PlusIcon fill="var(--neutral7)" />
                    </Box>
                </Box>
            </MuiButton>
        </div>
    );
}

export default AddNote;
