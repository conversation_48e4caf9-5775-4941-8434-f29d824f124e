import { styled } from '@mui/material';
import StatusBar from '../StatusBar';

const Root = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
    overflow: 'hidden',
    borderRadius: 10,
    border: `1px solid ${theme.palette.neutral[4]}`,
    marginBottom: 30,

    '& > main': {
        padding: '15px 0 15px 0',
    },
}));

export default function OrderHeaderInfo({ children }: React.PropsWithChildren<{}>) {
    return (
        <Root>
            <OrderHeaderInner />
            <main>{children}</main>
        </Root>
    );
}

function OrderHeaderInner() {
    return <StatusBar />;
}
