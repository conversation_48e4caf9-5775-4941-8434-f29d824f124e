import MuiTooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { ProhibitionIcon } from 'common/components/Icons/ProhibitionIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AdditionalNoteDto, AdditionalNoteSection } from 'datacontracts/AdditionalNoteDto';
import { useCallback, useMemo, useRef, useState } from 'react';
import NoteEditor from 'views/Components/NoteEditor';
import ThreeDotMenu from '../ThreeDotMenu';
import NoteActivityLogTooltipContent from './NoteActivityLogTooltipContent';

type NoteProps = {
    additionalNote: AdditionalNoteDto;
    editSection: AdditionalNoteSection;
    disabled?: boolean;
    onEdit: (value: AdditionalNoteDto) => void;
    onDelete: (value: AdditionalNoteDto) => void;
};

const Note = ({ additionalNote, editSection, disabled, onDelete, onEdit }: NoteProps) => {
    const { t } = useAppTranslation();

    const [show, setShow] = useState<boolean>(false);
    const [isEdition, setIsEdition] = useState<boolean>(false);
    const anchorHint = useRef<HTMLDivElement | null>(null);

    const showActions = useMemo(
        () => additionalNote.section === editSection && show,
        [additionalNote.section, editSection, show]
    );

    const handleSave = (text: string) => {
        setIsEdition(false);
        onEdit && onEdit({ ...additionalNote, text });
    };

    const handleDelete = () => {
        setIsEdition(false);
        onDelete && onDelete(additionalNote);
    };

    const handleMouseMove = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;
        if (anchorHint.current === el) {
            // NOTE (AK) little hack for floating hint copied from RichTextEditorContent
            return;
        }

        if (el instanceof HTMLDivElement) {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'flex';
                anchorHint.current.style.top = `${e.clientY}px`;
                anchorHint.current.style.left = `${e.clientX}px`;
            }
        } else {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    const handleMouseOut = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;
        if (anchorHint.current === el) {
            // NOTE (AK) little hack for floating hint copied from RichTextEditorContent
            return;
        }

        if (el instanceof HTMLDivElement) {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    return isEdition ? (
        <NoteEditor
            text={additionalNote.text}
            placeholder={t('orderDetails.additionalNotes.enterNote')}
            onEdit={(text) => {
                handleSave(text);
            }}
            onCancel={() => {
                setIsEdition(false);
            }}
        />
    ) : (
        <>
            <Tooltip
                title={<NoteActivityLogTooltipContent additionalNote={additionalNote} />}
                placement="left"
            >
                <DivNoteWrapper
                    onMouseMove={(e) =>
                        !disabled && additionalNote.section !== editSection && handleMouseMove(e)
                    }
                    onMouseOver={() =>
                        !disabled && additionalNote.section === editSection && setShow(true)
                    }
                    onMouseOut={(e) => {
                        !disabled && additionalNote.section === editSection && setShow(false);
                        !disabled && additionalNote.section !== editSection && handleMouseOut(e);
                    }}
                >
                    <Typography
                        component="div"
                        sx={(theme) => ({
                            ...theme.typography.h6Roboto,
                            fontWeight: 400,
                            color: theme.palette.neutral[7],
                            inlineSize: 'auto',
                            overflowWrap: 'break-word',
                            maxWidth: '100%',
                        })}
                    >
                        {additionalNote.text}
                    </Typography>
                    {showActions && (
                        <ThreeDotMenu
                            onClose={() => setShow(false)}
                            onEdit={() => setIsEdition(true)}
                            onDelete={handleDelete}
                        />
                    )}
                </DivNoteWrapper>
            </Tooltip>
            <DivMouseHint ref={anchorHint}>
                <ProhibitionIcon fill="var(--neutral5)" size={10} />
                {t('orderDetails.additionalNotes.noteCannotBeEdited')}
            </DivMouseHint>
        </>
    );
};

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: theme.palette.neutral[1],
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.neutral[1],
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const DivNoteWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    padding: '3px 0',
    borderRadius: '4px',
    width: '100%',

    '&:hover': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

const DivMouseHint = styled('div')(({ theme }) => ({
    minWidth: 'max-content',
    zIndex: 10,
    padding: 3,
    position: 'fixed',
    transform: 'translate(15px, 0px)',
    pointEvents: 'none',
    display: 'none',
    gap: '2px',
    ...theme.typography.h9Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[5],
}));

export default Note;
