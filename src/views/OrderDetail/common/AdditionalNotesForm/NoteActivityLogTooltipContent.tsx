import { Box, List, ListItem, styled, Typography } from '@mui/material';
import { JobTitle, jobTitleLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { AdditionalNoteDto } from 'datacontracts/AdditionalNoteDto';
import moment from 'moment';
import { useMemo } from 'react';

type NoteActivityLogTooltipContentProps = {
    additionalNote: AdditionalNoteDto;
};

// NOTE (AK) AdditionalNotes and EstimateNotes have the same structure of tooltip with lil differences
// maybe it can be more common component
function NoteActivityLogTooltipContent({ additionalNote }: NoteActivityLogTooltipContentProps) {
    const { t } = useAppTranslation();

    const reversedHistory = useMemo(
        () => (additionalNote.historyNotes ? additionalNote.historyNotes.toReversed() : []),
        [additionalNote.historyNotes]
    );

    return (
        <Box
            component="div"
            sx={(theme) => ({
                maxWidth: '330px',
                minHeight: '30px',
                padding: '8px',
                background: theme.palette.neutral[1],
                overflowY: 'auto',
                ...scrollbarStyle(),
            })}
        >
            {reversedHistory.length <= 1 ? (
                <HistoryItem
                    isFirst
                    isListItem={false}
                    highlighted={false}
                    isFromCustomer={additionalNote.isFromCustomer}
                    userJob={additionalNote.user?.job}
                    userName={additionalNote.user?.name}
                    createdAt={reversedHistory.length ? reversedHistory[0].createdAt : null}
                />
            ) : (
                <>
                    <HistoryItem
                        isFirst={false}
                        isListItem={false}
                        highlighted
                        isFromCustomer={additionalNote.isFromCustomer}
                        userJob={additionalNote.user?.job}
                        userName={additionalNote.user?.name}
                        createdAt={reversedHistory[0].createdAt}
                        note={additionalNote.text}
                    />
                    <Typography
                        sx={(theme) => ({
                            fontFamily: 'Roboto',
                            fontSize: '12px',
                            fontStyle: 'normal',
                            fontWeight: 700,
                            lineHeight: 'normal',
                            color: theme.palette.neutral[7],
                        })}
                    >
                        {t('orderDetails.additionalNotes.noteActivityLog')}
                    </Typography>
                    <List sx={{ listStyleType: 'none', padding: '2px 8px 2px 0px' }}>
                        {reversedHistory.length > 0 &&
                            reversedHistory
                                .slice(1, reversedHistory.length)
                                .map((historyNote, index) => (
                                    <ListItem
                                        key={historyNote.id}
                                        sx={(theme) => ({
                                            display: 'list-item',
                                            padding: '2px 0px',
                                            color: theme.palette.neutral[7],
                                        })}
                                    >
                                        <HistoryItem
                                            isFirst={index === reversedHistory.length - 2}
                                            isListItem
                                            highlighted={false}
                                            isFromCustomer={additionalNote.isFromCustomer}
                                            userJob={additionalNote.user?.job}
                                            userName={historyNote.user?.name}
                                            createdAt={historyNote.createdAt}
                                            note={historyNote.text}
                                        />
                                    </ListItem>
                                ))}
                    </List>
                </>
            )}
        </Box>
    );
}

type HistoryItemProps = {
    isFirst: boolean;
    isListItem: boolean;
    highlighted: boolean;
    isFromCustomer: boolean;
    userJob: JobTitle | undefined | null;
    userName: string | undefined;
    createdAt: Date | null;
    note?: string;
};

const HistoryItem = ({
    isFirst,
    isListItem,
    highlighted,
    isFromCustomer,
    userJob,
    userName,
    createdAt,
    note,
}: HistoryItemProps) => {
    const { t } = useAppTranslation();

    const localCreatedAt = createdAt ? moment.utc(createdAt).local() : null;

    return (
        <Typography
            sx={(theme) => ({
                fontFamily: 'Roboto',
                fontSize: '12px',
                fontStyle: 'normal',
                fontWeight: 400,
                lineHeight: 'normal',
                color: theme.palette.neutral[7],
                marginBottom: highlighted ? '20px' : 'none',
            })}
        >
            <SpanBold bold={highlighted}>
                {isListItem && '- '}
                {`${t(`orderDetails.additionalNotes.${isFirst ? 'addedBy' : 'modifiedBy'}`)} ${
                    isFromCustomer
                        ? t('orderDetails.additionalNotes.customer')
                        : `${userJob ? t(jobTitleLabel(userJob)) : ''} ${
                              userName ?? t('orderDetails.additionalNotes.teamMember')
                          }`
                } ${
                    localCreatedAt
                        ? `${t('orderDetails.additionalNotes.on')} ${localCreatedAt.format(
                              t('dateFormats.shortDate')
                          )} ${t('orderDetails.additionalNotes.at')} ${localCreatedAt.format(
                              t('dateFormats.time')
                          )}`
                        : ''
                }`}
            </SpanBold>
            {note && `: "${note}"`}
        </Typography>
    );
};

const SpanBold = styled('span')<{ bold: boolean }>(({ bold }) => ({
    fontWeight: bold ? 700 : 'inherit',
}));

export default NoteActivityLogTooltipContent;
