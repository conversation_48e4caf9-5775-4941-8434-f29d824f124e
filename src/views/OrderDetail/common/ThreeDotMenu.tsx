import { MoreVertOutlined } from '@mui/icons-material';
import { PopoverOrigin, styled } from '@mui/material';
import { DeleteIcon, EditIcon } from 'common/components/Icons';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';

type ThreeDotMenuProps = {
    onClose: () => void;
    onEdit: () => void;
    onDelete?: () => void;
    transformOrigin?: PopoverOrigin;
};

export default function ThreeDotMenu({
    onClose,
    onEdit,
    onDelete,
    transformOrigin,
}: ThreeDotMenuProps) {
    const { t } = useAppTranslation();

    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState(false);

    const handleClose = () => {
        setOpen(false);
        onClose();
    };

    return (
        <>
            <StyledButton ref={setAnchorEl} onClick={() => setOpen(true)}>
                <MoreVertOutlined
                    fontSize="small"
                    sx={(theme) => ({
                        height: '18px',
                        width: '18px',
                        color: theme.palette.neutral[6],
                    })}
                />
            </StyledButton>
            <SMenu
                borders
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={transformOrigin ?? { vertical: 'top', horizontal: 'left' }}
                open={open}
                onClose={handleClose}
                anchorEl={anchorEl}
                disableScrollLock={true}
            >
                <SMenuItem2
                    onClick={() => {
                        onEdit();
                        handleClose();
                    }}
                >
                    <EditIcon style={{ marginRight: 5 }} size={24} fill="currentColor" />
                    {t('orderDetails.additionalNotes.editNote')}
                </SMenuItem2>
                {onDelete && (
                    <SMenuItem2
                        onClick={() => {
                            onDelete();
                            handleClose();
                        }}
                    >
                        <DeleteIcon style={{ marginRight: 5 }} size={24} fill="currentColor" />
                        {t('orderDetails.additionalNotes.deleteNote')}
                    </SMenuItem2>
                )}
            </SMenu>
        </>
    );
}

const StyledButton = styled('button')(({ theme }) => ({
    boxSizing: 'border-box',
    backgroundColor: theme.palette.neutral[2],
    position: 'absolute',
    top: 0,
    right: 0,
    borderRadius: '50%',
    '--size': '24px',
    height: 'var(--size)',
    width: 'var(--size)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: '.3s',
    border: 'none',

    '&:hover': {
        backgroundColor: theme.palette.neutral[3],
    },
}));
