/* eslint-disable react/jsx-no-target-blank */

import { Box, IconButton, IconButtonProps, styled, tooltipClasses } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import SignaturesApi from 'api/Signatures';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import OrdersApi from 'api/orders';
import Container from 'common/components/Container';
import PrintIcon from 'common/components/Icons/PrintIcon';
import InfoTooltip from 'common/components/InfoTooltip';
import Tooltip from 'common/components/Tooltip';
import SMenu from 'common/components/mui/SMenu';
import { SMenuItem2 } from 'common/components/mui/SMenuItem';
import SimpleTabsWithContent from 'common/components/tabs/SimpleTabsWithContent';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useQueryParam from 'common/hooks/useQueryParam';
import isEqual from 'lodash/isEqual';
import { DateTime } from 'luxon';
import React, { forwardRef, useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { createSelector } from 'reselect';
import { HttpUtils } from 'services';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseUsersAction } from 'store/slices/enterprise/users';
import loadEnterpriseUsers from 'store/slices/enterprise/users/thunks/loadEnterpriseUsers';
import {
    selectIanaTz,
    selectRepairShopFeatures,
    selectSettings,
} from 'store/slices/globalSettingsSlice';
import { SimpleErrorDisplay2 } from 'utils/errorsHandling/SimpleErrorBoundary2';
import { StickyTabsWrapper } from 'views/Components/StickyTabsWrapper';
import { useHeaderLoading } from 'views/HeaderBar';
import { PhoneCallLogic } from '../../business/PhoneCallLogic';
import { WarningConfirmationPopup } from '../../common/components/Popups/ConfirmationPopup';
import useIsEnterpriseRoute from '../../common/hooks/useIsEnterpriseRoute';
import { PhoneCallListItem } from '../../datacontracts/PhoneCallListItem';
import {
    loadOrderThunk,
    orderActions,
    OrderError,
    selectIsOrderClosed,
    selectOrder,
    selectOrderData,
    selectOrderError,
    selectOrderFromMicroservices,
    selectOrderLoading,
    selectOrderShopKey,
} from '../../store/slices/order/orderDetails';
import CallLog from '../CallLog';
import { CallCustomer, CallCustomerProps } from '../Components/CallCustomer';
import { SendMessage } from '../Components/SendMessage';
import ActivityLogButton from './ActivityLogButton';
import AttachedFilesButton from './AttachedFilesButton';
import CloseOrReopenOrderButton from './CloseOrReopenOrderButton';
import CustomPdfModal from './CustomPdfModal';
import EstimateTab from './EstimateTab';
import GeneralInfo from './GeneralInfo';
import OrderHeaderInfo from './OrderHeaderInfo';
import OrderNotFound from './OrderNotFound';
import OrderSiteHeaderContent from './OrderSiteHeaderContent';
import { OrderDetailsValidationsProvider } from './providers';
import { TabSelectionProvider } from './providers/TabSelectionProvider';

const selectOrderDetailsData = createSelector(
    [selectOrder, selectIsOrderClosed, selectOrderError, selectOrderShopKey],
    (order, orderIsClosed, error, shopKey) => ({
        orderNumber: order?.repairOrderNumber,
        orderIsClosed,
        error,
        shopKey,
    })
);

const OrderDetails = () => {
    const { t } = useAppTranslation();
    const [sectionParam] = useQueryParam('section');
    const id = +(useParams<{ id: string }>().id ?? '');
    const isEnterprise = useIsEnterpriseRoute();
    const { orderNumber, orderIsClosed, error, shopKey } = useAppSelector(selectOrderDetailsData);
    const dispatch = useAppDispatch();
    const { callApi: callApiPhoneCallsList } = useApiCall();

    useDocumentTitle(`${t('orderDetails.orderDetail')}${orderNumber ?? ''}`);

    const [section, setSection] = useState(sectionParam ?? 'info');
    const [phoneCalls, setPhoneCalls] = useState<PhoneCallListItem[]>([]);
    const [isCallLogOpened, setIsCallLogOpened] = useState(false);
    const [orderIsClosedWarningPopupIsOpen, setOrderIsClosedWarningPopupIsOpen] =
        useState<boolean>(false);
    const [orderIsClosedWarningPopupWasShown, setOrderIsClosedWarningPopupWasShown] =
        useState<boolean>(false);

    useEffect(() => {
        dispatch(loadOrderThunk({ orderId: id }));

        return () => {
            dispatch(orderActions.clearOrder());
        };
    }, [dispatch, id]);

    useEffect(() => {
        if (orderIsClosed && !isEnterprise) setOrderIsClosedWarningPopupIsOpen(true);
    }, [orderIsClosed, isEnterprise]);

    useEffect(() => {
        if (shopKey && isEnterprise) {
            dispatch(loadEnterpriseUsers({ shopsIds: [shopKey] }));
        }

        return () => {
            dispatch(enterpriseUsersAction.reset());
        };
    }, [dispatch, shopKey, isEnterprise]);

    const { refetch: fetchPhoneCalls } = useQuery(
        ['order', 'calls', id],
        () =>
            callApiPhoneCallsList(() => PhoneCallLogic.list(id), {
                selectErrorContent: () => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }),
        {
            onSuccess: (calls) => {
                setPhoneCalls(calls);
            },
        }
    );

    function onClosedOrderWarningClose() {
        setOrderIsClosedWarningPopupIsOpen(false);
        setOrderIsClosedWarningPopupWasShown(true);
    }

    const errorMessage = error ? <OrderErrorDisplay error={error} orderId={id} /> : null;

    const children = (
        <TabSelectionProvider setSection={setSection} section={section}>
            <OrderDetailsValidationsProvider>
                <div className="scrollbar-gutter-fix">
                    <Container w="min(1800px, 90vw)">
                        <OrderSiteHeaderContent />

                        <SimpleTabsWithContent
                            sx={{ height: 46 }}
                            disabled={!!errorMessage}
                            tabs={[
                                {
                                    key: 'info',
                                    label: t('orderDetails.generalInfo'),
                                    content: <GeneralInfo />,
                                },
                                {
                                    key: 'estimate',
                                    label: t('orderDetails.estimate'),
                                    content: <EstimateTab />,
                                },
                            ]}
                            selected={section}
                            onTabSelected={setSection}
                            renderLayout={({ tabs, content }) => (
                                <>
                                    <StickyTabsWrapper
                                        style={{ marginBottom: 20 }}
                                        alwaysDetached
                                        innerWidth="min(1800px, 90vw)"
                                    >
                                        <DivHeader>
                                            {tabs}

                                            <Box
                                                sx={{ display: 'flex', alignItems: 'center' }}
                                                gap={0.5}
                                            >
                                                <AttachedFilesButton repairOrderId={id} />
                                                <ActivityLogButton repairOrderId={id} />
                                                <Tooltip content={t('orderDetails.printTooltip')}>
                                                    <OpenPdfLinkButton />
                                                </Tooltip>
                                                <SendMessageButton />
                                                <CallCustomerButton
                                                    viewCallLogEnabled={!!phoneCalls.length}
                                                    onViewCallLog={() => setIsCallLogOpened(true)}
                                                    onCall={fetchPhoneCalls}
                                                />
                                                <CloseOrReopenOrderButton />
                                            </Box>
                                        </DivHeader>
                                        <WarningConfirmationPopup
                                            body={<CloseOrderWarningBody />}
                                            title={t('orderDetails.closedOrderWarning.title')}
                                            open={
                                                orderIsClosed &&
                                                orderIsClosedWarningPopupIsOpen &&
                                                !orderIsClosedWarningPopupWasShown
                                            }
                                            showCloseBtn={false}
                                            displayDivider={true}
                                            onClose={onClosedOrderWarningClose}
                                            confirm={t('orderDetails.closedOrderWarning.button')}
                                            onConfirm={onClosedOrderWarningClose}
                                        />
                                    </StickyTabsWrapper>
                                    {errorMessage}
                                    <HideUntilLoaded orderId={id}>
                                        <OrderHeaderInfo>{content}</OrderHeaderInfo>
                                    </HideUntilLoaded>
                                </>
                            )}
                        />

                        <CallLog
                            open={isCallLogOpened}
                            onClose={() => setIsCallLogOpened(false)}
                            phoneCalls={phoneCalls}
                        />
                    </Container>
                </div>
                <OrderDetailsHeaderLoader />
            </OrderDetailsValidationsProvider>
        </TabSelectionProvider>
    );

    if (isEnterprise) {
        return (
            <CustomersSearchApiProvider mode="enterprise" shopId={shopKey}>
                {children}
            </CustomersSearchApiProvider>
        );
    } else {
        return <CustomersSearchApiProvider mode="shop">{children}</CustomersSearchApiProvider>;
    }
};

const DivHeader = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr auto',
    height: '100%',
    alignItems: 'center',
});

function OrderErrorDisplay({ error, orderId }: { error: OrderError; orderId: number }) {
    const dispatch = useAppDispatch();

    if (error.notFound) {
        return <OrderNotFound />;
    }
    return (
        <SimpleErrorDisplay2
            message={error.text}
            errorCombo={error.combo}
            onRetry={() => dispatch(loadOrderThunk({ orderId }))}
            sx={{ position: 'static' }}
        />
    );
}

function CloseOrderWarningBody() {
    const { t } = useAppTranslation();
    const orderFromMicroservices = useAppSelector(selectOrderFromMicroservices);
    const ianaTz = useAppSelector(selectIanaTz);

    return (
        <div>
            <p>
                {t('orderDetails.closedOrderWarning.body1', {
                    userDisplayName: orderFromMicroservices?.phaseChangedByUserDisplayName,
                    formattedDateTime:
                        orderFromMicroservices?.phaseChangedAt &&
                        DateTime.fromISO(orderFromMicroservices?.phaseChangedAt, {
                            zone: 'utc',
                        })
                            .setZone(ianaTz)
                            .toFormat(t('dateFormats.luxon.shortRightSlashDivider')),
                })}
            </p>
            <p>{t('orderDetails.closedOrderWarning.body2')}</p>
        </div>
    );
}

function HideUntilLoaded({ children, orderId }: React.PropsWithChildren<{ orderId: number }>) {
    const ready = useAppSelector((r) => {
        const loading = selectOrderLoading(r);
        if (loading) return false;
        const loadedOrderId = selectOrder(r)?.repairOrderId;
        return loadedOrderId === orderId;
    });

    if (!ready) return null;

    return <>{children}</>;
}

const selectCallCustomerData = createSelector(
    [selectOrder, selectOrderData, selectSettings],
    (order, state) => ({
        canCallLandline: !!state.customer.landline,
        canCallMobile: !!state.customer.mobile,
        repairOrderId: order?.repairOrderId,
        shopId: order?.shopId,
    })
);

function CallCustomerButton(
    props: Omit<CallCustomerProps, 'canCallMobile' | 'canCallLandline' | 'repairOrderId'>
) {
    const { canCallLandline, canCallMobile, repairOrderId, shopId } =
        useAppSelector(selectCallCustomerData);

    const { repairShopSettings, enterpriseSettings } = useAppSelector(selectSettings);

    const phoneCallsSettings = useMemo(() => {
        if (repairOrderId) {
            if (repairShopSettings) {
                return {
                    enablePhoneCalls: repairShopSettings.features.phoneCalls,
                };
            } else if (enterpriseSettings) {
                const shop = enterpriseSettings.shops.find((s) => s.key === shopId);
                if (shop) {
                    return {
                        enablePhoneCalls: shop.enablePhoneCalls,
                    };
                }
            }
        }

        return {
            enablePhoneCalls: false,
        };
    }, [repairOrderId, repairShopSettings, enterpriseSettings, shopId]);

    if (!repairOrderId || !phoneCallsSettings.enablePhoneCalls) return null;

    return (
        <CallCustomer
            {...props}
            repairOrderId={repairOrderId}
            canCallLandline={canCallLandline}
            canCallMobile={canCallMobile}
        />
    );
}

const selectSendMessageButtonData = createSelector([selectOrder, selectOrderData], (order, state) =>
    order
        ? {
              id: order.repairOrderId,
              number: order.repairOrderNumber,
              email: state.customer.email,
              inspectionLink: order.inspectionLink,
              mobile: state.customer.mobile,
              shopId: order.shopId,
          }
        : null
);

function SendMessageButton() {
    const data = useSelector(selectSendMessageButtonData, isEqual);
    const isLoading = useAppSelector(selectOrderLoading);
    const repairOrder = isLoading ? undefined : data;
    const { repairShopSettings, enterpriseSettings } = useSelector(selectSettings);

    const sendSettings = useMemo(() => {
        if (repairOrder) {
            if (repairShopSettings) {
                return {
                    sendWhatsAppToCustomer: repairShopSettings.features.sendWhatsappToConsumer,
                    sendSmsToCustomer: repairShopSettings.features.sms,
                };
            } else if (enterpriseSettings) {
                const shop = enterpriseSettings.shops.find((s) => s.key === repairOrder.shopId);
                if (shop) {
                    return {
                        sendWhatsAppToCustomer: shop.sendWhatsAppToCustomer,
                        sendSmsToCustomer: shop.sendSmsToCustomer,
                    };
                }
            }
        }

        return {
            sendWhatsAppToCustomer: false,
            sendSmsToCustomer: false,
            enablePhoneCalls: false,
        };
    }, [repairOrder, repairShopSettings, enterpriseSettings]);

    if (!repairOrder) return null;

    return (
        <SendMessage
            canSendEmail={!!repairOrder?.email}
            canSendWhatsApp={!!repairOrder?.mobile && sendSettings.sendWhatsAppToCustomer}
            canSendSms={!!repairOrder?.mobile && sendSettings.sendSmsToCustomer}
            repairOrderId={repairOrder.id}
            email={repairOrder?.email ?? ''}
            number={repairOrder?.mobile ?? ''}
            inspectionLink={repairOrder?.inspectionLink ?? ''}
        />
    );
}

function OrderDetailsHeaderLoader() {
    const { id: idString } = useParams<{ id: string }>();
    const id = +(idString ?? '');
    const dispatch = useAppDispatch();

    const isLoading = useAppSelector(selectOrderLoading);
    const queryEnabled = !!id && !isNaN(id);

    useHeaderLoading(isLoading && queryEnabled);
    useEffect(() => {
        dispatch(orderActions.setLoading(isLoading));
    }, [isLoading, dispatch]);

    return null;
}

const selectPdfLinks = createSelector(selectOrder, (x) => ({
    orderPdfLink: x?.pdfLink,
    estimatePdfLink: x?.spreadsheetPdfLink,
    jobsPdfLink: x?.jobsPdfLink,
    customPdfLink: x?.customPdfLink,
}));

const CustomMenu = styled(SMenu)(({ theme }) => ({
    minWidth: 170,

    '& .MuiList-root': {
        padding: 0,
    },

    '& .MuiMenuItem-root': {
        padding: 14,

        '&:not(:last-child)': {
            borderBottom: 'none',

            '::after': {
                content: '""',
                display: 'block',
                height: 1,
                width: '90%',
                backgroundColor: theme.palette.neutral[3],
                position: 'absolute',
                bottom: 0,
                left: '5%',
            },
        },
    },
}));

const OpenPdfLinkButton = forwardRef(
    (props: IconButtonProps, ref: React.ForwardedRef<HTMLButtonElement>) => {
        const { t } = useAppTranslation();
        const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
        const [customPdfModalOpen, setCustomPdfModalOpen] = useState(false);
        const { orderPdfLink, estimatePdfLink, jobsPdfLink, customPdfLink } =
            useAppSelector(selectPdfLinks);
        const repairShopFeatures = useAppSelector(selectRepairShopFeatures);
        const { id: idString } = useParams<{ id: string }>();
        const id = +(idString ?? '');

        const { data: inspectionFormTemplatesResponse } = useQuery(
            ['orders', 'inspections-forms', 'templates-info', id],
            () => OrdersApi.getOrderTemplatesInfo(id)
        );

        const { data: signatureHeaders, isFetched: signatureHeadersFetched } = useQuery(
            ['orders', 'signature-headers', id],
            () => SignaturesApi.getSignatureHeaders(id)
        );

        //const [signatureHeaders, setSignatureHeaders] = useState<SignatureHeadersDto | null>(null);

        const openPdfLink = () => {
            if (!orderPdfLink) return;

            HttpUtils.openLinkViaAnchor(orderPdfLink);
        };

        const openEstimatePdf = useCallback(() => {
            if (!estimatePdfLink) return;

            HttpUtils.openLinkViaAnchor(estimatePdfLink);
        }, [estimatePdfLink]);

        const openJobsPdf = useCallback(() => {
            if (!jobsPdfLink) return;

            HttpUtils.openLinkViaAnchor(jobsPdfLink);
        }, [jobsPdfLink]);

        return (
            <>
                <IconButton
                    ref={ref}
                    size="small"
                    onClick={(e) => setAnchorEl(e.target as HTMLButtonElement)}
                    {...props}
                >
                    <PrintIcon />
                </IconButton>
                <CustomMenu
                    borders
                    open={!!anchorEl}
                    anchorEl={anchorEl}
                    onClose={() => setAnchorEl(null)}
                >
                    <SMenuItem2 onClick={openPdfLink}>
                        <span>{t('orderDetails.printOrder')}</span>
                        <InfoTooltip
                            text={t('orderDetails.printOrderTooltip')}
                            position="right"
                            sx={{
                                [`& .${tooltipClasses.tooltip}`]: {
                                    maxWidth: '200px !important',
                                },
                            }}
                        />
                    </SMenuItem2>
                    <SMenuItem2 onClick={openEstimatePdf}>
                        <span>{t('orderDetails.inspectionForms.printEstimate')}</span>
                        <InfoTooltip
                            text={t('orderDetails.inspectionForms.printEstimateTooltip')}
                            position="right"
                            sx={{
                                [`& .${tooltipClasses.tooltip}`]: {
                                    maxWidth: '200px !important',
                                },
                            }}
                        />
                    </SMenuItem2>
                    {repairShopFeatures?.enableWp && (
                        <SMenuItem2 onClick={openJobsPdf}>
                            <span>{t('orderDetails.printJobs')}</span>
                            <InfoTooltip
                                text={t('orderDetails.printJobsTooltip')}
                                position="right"
                                sx={{
                                    [`& .${tooltipClasses.tooltip}`]: {
                                        maxWidth: '200px !important',
                                    },
                                }}
                            />
                        </SMenuItem2>
                    )}
                    <SMenuItem2 onClick={() => setCustomPdfModalOpen(true)}>
                        <span>{t('orderDetails.printCustomPdf')}</span>
                        <InfoTooltip
                            text={t('orderDetails.printCustomPdfTooltip')}
                            position="right"
                            sx={{
                                [`& .${tooltipClasses.tooltip}`]: {
                                    maxWidth: '200px !important',
                                },
                            }}
                        />
                    </SMenuItem2>
                </CustomMenu>
                {signatureHeadersFetched && (
                    <CustomPdfModal
                        open={customPdfModalOpen}
                        customPdfLink={customPdfLink}
                        inspectionFormTemplates={inspectionFormTemplatesResponse?.templates ?? []}
                        signatureHeaders={signatureHeaders!}
                        onClose={() => setCustomPdfModalOpen(false)}
                    />
                )}
            </>
        );
    }
);

export default OrderDetails;
