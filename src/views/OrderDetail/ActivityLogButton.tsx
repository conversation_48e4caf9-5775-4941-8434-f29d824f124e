import { IconButton } from '@mui/material';
import { DelayIcon } from 'common/components/Icons/DelayIcon';
import Tooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import ActivityLog from './ActivityLog';

export default function ActivityLogButton({ repairOrderId }: { repairOrderId: number }) {
    const { t } = useAppTranslation();
    const [open, setOpen] = useState(false);

    return (
        <>
            <Tooltip content={t('orderDetails.activityLog.activityLog')}>
                <IconButton size="small" onClick={() => setOpen(true)}>
                    <DelayIcon />
                </IconButton>
            </Tooltip>
            {open && <ActivityLog onClose={() => setOpen(false)} orderId={repairOrderId} />}
        </>
    );
}
