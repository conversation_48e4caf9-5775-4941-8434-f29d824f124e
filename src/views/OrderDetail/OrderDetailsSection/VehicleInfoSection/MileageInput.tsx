import { useMutation } from '@tanstack/react-query';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useResettableState } from '../util';

type MileageInputProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectSettings,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, gs, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.vehicle.mileage,
        internationalization: gs.internationalization,
    })
);

export default function MileageInput({ id, isSelected, onKeyDown, disabled }: MileageInputProps) {
    const ref = useRef<HTMLDivElement>(null);

    const {
        canEdit,
        value: originalValue,
        internationalization,
    } = useAppSelector(selectData, isEqual);
    const dispatch = useAppDispatch();

    const { t } = useAppTranslation();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();
    const [stringValue, setStringValue] = useResettableState(
        originalValue ? originalValue + '' : ''
    );

    const mutation = useMutation(
        async (value: string) => {
            const parsedValue = parseValueToNumber(value);
            setStringValue(parsedValue === null ? '' : parsedValue + '');

            dispatch(orderActions.update({ vehicle: { mileage: parsedValue } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updateMileage = async (value: string) => {
        await mutation.mutateAsync(value);
    };

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    function formattingFunction(value: string): string {
        const num = parseValueToNumber(value);
        const formatted = InternationalizationLogic.numberFormat(internationalization, num);
        return formatted;
    }

    return (
        <OrderDetailsResettableTextFormField
            inputRef={ref}
            name="mileage"
            label={t('orderDetails.mileage')}
            value={stringValue}
            disabled={!canEdit || disabled}
            onSave={(value) => updateMileage(value)}
            placeholder={t('orderDetails.mileagePlaceholder')}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
            onKeyDown={onKeyDown}
            formatting={(value) => formattingFunction(value)}
            size="small"
        />
    );
}

function parseValueToNumber(value: string): number | null {
    value = value.trim();
    if (value === '') return null;

    let num = parseFloat(value);
    if (Number.isNaN(num)) {
        value = value.replaceAll(/[^\d]/g, '');
        num = parseFloat(value);
        if (Number.isNaN(num)) {
            return null;
        }
    }

    return num;
}
