import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type PlatesFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (s, perm, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.vehicle.plates,
    })
);

export default function PlatesField({ id, isSelected, onKeyDown, disabled }: PlatesFieldProps) {
    const ref = useRef<HTMLDivElement>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { canEdit, value: originalValue } = useAppSelector(selectData, isEqual);
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const mutation = useMutation(
        async (value: string) => {
            dispatch(orderActions.update({ vehicle: { plates: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updatePlates = async (value: string) => {
        await mutation.mutateAsync(value);
    };

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <OrderDetailsResettableTextFormField
            inputRef={ref}
            name="plates"
            label={t('commonLabels.plates')}
            value={originalValue}
            disabled={!canEdit || disabled}
            onSave={(value) => updatePlates(value)}
            placeholder={t('orderDetails.platesPlaceholder')}
            onEdit={(value) => value.toUpperCase()}
            onKeyDown={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
            size="small"
        />
    );
}
