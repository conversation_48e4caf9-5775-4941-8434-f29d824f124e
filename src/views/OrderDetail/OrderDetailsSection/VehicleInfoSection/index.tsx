import { useAppSelector } from 'store';
import { selectVehicleFields } from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user';
import Column from '../Column';
import CustomFieldRenderer from '../CustomFieldRenderer';

export default function VehicleInfoSection() {
    const fields = useAppSelector(selectVehicleFields);
    const allowEditVehicles = useAppSelector((r) => selectUserPermission(r).allowEditVehicles);

    return (
        <Column>
            {fields.map((field) => {
                return (
                    <CustomFieldRenderer
                        key={field.id}
                        disabledPredefinedFields={!allowEditVehicles}
                        field={field}
                    />
                );
            })}
        </Column>
    );
}
