import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type VinFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (s, perm, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.vehicle.vin,
    })
);

export default function VinField({ id, isSelected, onKeyDown, disabled }: VinFieldProps) {
    const ref = useRef<HTMLDivElement>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { canEdit, value: originalValue } = useAppSelector(selectData, isEqual);
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const mutation = useMutation(
        async (value: string) => {
            dispatch(orderActions.update({ vehicle: { vin: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updateVin = async (value: string) => {
        await mutation.mutateAsync(value);
    };

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <OrderDetailsResettableTextFormField
            inputRef={ref}
            name="vin"
            label={t('commonLabels.vin')}
            value={originalValue}
            disabled={!canEdit || disabled}
            onSave={(value) => updateVin(value)}
            placeholder={t('commonLabels.vinPlaceholder')}
            onEdit={(value) => value.toUpperCase()}
            size="small"
            onKeyDown={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
        />
    );
}
