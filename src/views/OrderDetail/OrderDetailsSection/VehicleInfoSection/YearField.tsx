import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import YearPicker from 'views/Components/Autocomplete/YearPicker';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type YearFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (s, perm, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        model: s.vehicle.model,
        brand: s.vehicle.brand,
        year: s.vehicle.year,
    })
);

export default function YearField({ id, isSelected, onKeyDown, disabled }: YearFieldProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { canEdit, brand, model, year } = useAppSelector(selectData, isEqual);
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const { mutate } = useMutation(
        async (update: { brand: string; model: string; year: number | null }) => {
            dispatch(
                orderActions.update({
                    vehicle: { brand: update.brand, model: update.model, year: update.year },
                })
            );
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    return (
        <YearPicker
            fieldId={id}
            placeholder={t('orderDetails.yearPlaceholder')}
            label={t('commonLabels.year')}
            disabled={!canEdit || disabled}
            brandName={brand}
            modelName={model}
            value={year ? year + '' : null}
            isSelected={isSelected}
            onKeyDown={onKeyDown}
            onChange={(year) => {
                mutate({
                    model,
                    brand,
                    year: year ? +year : null,
                });
            }}
        />
    );
}
