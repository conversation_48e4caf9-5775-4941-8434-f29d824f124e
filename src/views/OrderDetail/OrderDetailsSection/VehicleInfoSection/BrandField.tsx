import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import BrandPicker from 'views/Components/Autocomplete/BrandPicker';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type BrandFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectOrderLoading,
        selectOrderShopKey,
        selectIsOrderClosed,
    ],
    (s, perm, loading, repairShopKey, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        brand: s.vehicle.brand,
        repairShopKey,
    })
);

export default function BrandField({ id, isSelected, onKeyDown, disabled }: BrandFieldProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { canEdit, brand, repairShopKey } = useAppSelector(selectData, isEqual);
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const { mutate } = useMutation(
        async (update: { brand: string; model: string; year: number | null }) => {
            dispatch(
                orderActions.update({
                    vehicle: { brand: update.brand, model: update.model, year: update.year },
                })
            );
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    return (
        <BrandPicker
            fieldId={id}
            placeholder={t('orderDetails.brandPlaceholder')}
            label={t('commonLabels.brand')}
            disabled={!canEdit || disabled}
            value={brand}
            enterpriseRepairShopKey={repairShopKey}
            isSelected={isSelected}
            onKeyDown={onKeyDown}
            onChange={(brand) => {
                mutate({
                    brand: brand ?? '',
                    model: '',
                    year: null,
                });
            }}
        />
    );
}
