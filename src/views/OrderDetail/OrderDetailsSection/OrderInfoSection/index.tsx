import React from 'react';
import { useAppSelector } from 'store';
import { selectOrderInfoFields } from 'store/slices/order/orderDetails';
import Column from '../Column';
import CustomFieldRenderer from '../CustomFieldRenderer';
import TeamMemberSignature from './TeamMemberSignature';

const OrderInfoSection = React.memo(() => {
    const fields = useAppSelector(selectOrderInfoFields);

    return (
        <Column>
            {fields.map((field) => {
                // OrderInfoSection has not additional disabled conditions so just pass false
                return (
                    <CustomFieldRenderer
                        key={field.id}
                        field={field}
                        disabledPredefinedFields={false}
                    />
                );
            })}

            <TeamMemberSignature />
        </Column>
    );
});

export default OrderInfoSection;
