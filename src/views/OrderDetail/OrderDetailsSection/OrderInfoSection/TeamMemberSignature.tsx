import { Box } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import SignaturesApi from 'api/Signatures';
import EnterpriseSignaturesApi from 'api/enterprise/signatures';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import {
    selectIsOrderClosed,
    selectOrderId,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import SignatureField from '../SignatureField';

const selectData = createSelector(
    [
        selectUserPermission,
        selectOrderId,
        selectOrderShopKey,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (perm, orderId, shopKey, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        shopKey,
        orderId,
    })
);

export default function TeamMemberSignature() {
    const isEnterprise = useIsEnterpriseRoute();
    const { canEdit, shopKey, orderId } = useAppSelector(selectData);
    const { t } = useAppTranslation();

    const queryKey = isEnterprise
        ? ['enterprise', 'order', orderId!, 'team-member-signature-url']
        : ['order', orderId!, 'team-member-signature-url'];

    const queryFn = isEnterprise
        ? () => EnterpriseSignaturesApi.getTeamMemberReceptionSignature(orderId!, shopKey!)
        : () => SignaturesApi.getTeamMemberReceptionSignatures(orderId!);

    const { data } = useQuery(queryKey, queryFn);

    return (
        <>
            {data?.length
                ? data.map((signature, index) => (
                      <Box key={signature.url} sx={{ marginBottom: '24px' }}>
                          <SignatureField
                              key={index}
                              url={signature.url}
                              label={
                                  signature.headerText
                                      ? signature.headerText
                                      : t('orderDetails.teamMemberSignature')
                              }
                              signature={`${signature.userDisplayName ?? ''} ${
                                  signature.jobTitle
                                      ? `- ${t(`users.jobTitleTypes.${signature.jobTitle}`)}`
                                      : signature.jobTitleOther
                                      ? `- ${signature.jobTitleOther}`
                                      : ''
                              }`}
                              signedAt={signature.signatureDate}
                              disabled={!canEdit}
                          />
                      </Box>
                  ))
                : ''}
        </>
    );
}
