import { createSelector } from '@reduxjs/toolkit';
import { useMutation } from '@tanstack/react-query';
import WpOrdersApi from 'api/workshopPlanner/orders';
import DateTimePicker from 'common/components/DateTimePicker';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { KeyboardEventHandler } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    selectIsOrderClosed,
    selectOrder,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type DeliveryDateTimePickerProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectOrder,
        selectOrderShopKey,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, order, shopKey, loading, orderIsClosed) => ({
        shopKey,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.deliveryDate,
        orderNumber: order?.repairOrderNumber,
    })
);

export default function DeliveryDateTimePicker({
    id,
    isSelected,
    onKeyDown,
    disabled,
}: DeliveryDateTimePickerProps) {
    const { t } = useAppTranslation();
    const { canEdit, value: originalValue, orderNumber } = useAppSelector(selectData);
    const isEnterprise = useIsEnterpriseRoute();
    const dispatch = useAppDispatch();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const update = useMutation(async () => {
        if (isEnterprise) {
            //TODO - We need to add support of enterprises to WP controller or move updateOrder method to common controller.
            throw new Error('not implemented for enterprise');
        } else {
            const result = await WpOrdersApi.updateOrder(orderNumber!, {
                deliveryDate: originalValue,
            });
            showOrderUpdateNotification(result);

            return result;
        }
    });

    return (
        <DateTimePicker
            idField={id}
            label={t('workshopPlanner.orderPopup.deliveryDate')}
            disabled={!canEdit || disabled}
            onChange={(newValue) => {
                dispatch(orderActions.update({ deliveryDate: newValue }));
                update.mutate();
            }}
            value={originalValue}
            isSelected={isSelected}
            onKeyDown={onKeyDown}
        />
    );
}
