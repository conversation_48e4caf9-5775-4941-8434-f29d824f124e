import { UserListItem } from 'api/users';
import { useUsers } from 'common/hooks/useUsers';
import { useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectOrderData } from 'store/slices/order/orderDetails';

const selectTechnicians = createSelector(
    [selectOrderData, (_, users: UserListItem[]) => users],
    (state, users) => {
        const technicians: string[] = [];

        state.technicianIds.forEach((id) => {
            const user = users.find((x) => x.id === id);
            if (user) {
                technicians.push(user.key);
            }
        });

        return technicians;
    }
);

export function useTechniciansIds(shopId?: string) {
    const users = useUsers(shopId);
    const selectedTechnicianIds = useAppSelector((s) => selectTechnicians(s, users));

    return useMemo(() => [...selectedTechnicianIds], [selectedTechnicianIds]);
}
