import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import MuiTooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useQuery } from '@tanstack/react-query';
import AdditionalNotesAPI from 'api/AdditionalNotes';
import { OrderReasonDto } from 'api/orders/_common';
import { CustomerNoteIcon } from 'common/components/Icons/CustomerNoteIcon';
import { TeamMemberNoteIcon } from 'common/components/Icons/TeamMemberNoteIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AdditionalNoteDto } from 'datacontracts/AdditionalNoteDto';
import { useEffect, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import {
    selectIsOrderClosed,
    selectOrderId,
    selectOrderLoading,
    selectOrderReasons,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user';
import AdditionalNotes from './AdditionalNotes';
import OrderReasons from './OrderReasons';

const selectData = createSelector(
    [
        selectOrderId,
        selectOrderReasons,
        selectUserPermission,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (orderId, reasons, perm, loading, orderIsClosed) => ({
        orderId,
        reasons,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
    })
);

const OrderNotes = ({ disabled }: { disabled: boolean }) => {
    const { t } = useAppTranslation();
    const { orderId, reasons, canEdit } = useAppSelector(selectData);
    const [additionalNotes, setAdditionalNotes] = useState<AdditionalNoteDto[]>([]);

    const [internalOrderReasons, setInternalOrderReasons] = useState<OrderReasonDto[]>([]);
    const [customerOrderReasons, setCustomerOrderReasons] = useState<OrderReasonDto[]>([]);

    const { data, refetch } = useQuery(['order', orderId, 'additionalNotes'], () =>
        orderId ? AdditionalNotesAPI.getNotes(orderId) : Promise.resolve([])
    );

    useEffect(() => {
        if (data) {
            setAdditionalNotes(data);
        }
    }, [data]);

    useMemo(() => {
        const internalReasons: OrderReasonDto[] = [];
        const customerReasons: OrderReasonDto[] = [];

        if (reasons) {
            reasons.map((r) => {
                if (r.isFromCustomer) customerReasons.push(r);
                else internalReasons.push(r);
            });
            setInternalOrderReasons(internalReasons);
            setCustomerOrderReasons(customerReasons);
        }
    }, [reasons]);

    const handleAdditionalNotesChange = (notes: AdditionalNoteDto[]) => {
        setAdditionalNotes(notes);
        refetch();
    };

    return (
        <div>
            {customerOrderReasons && orderId && (
                <DivNotesSectionContainer>
                    <OrderReasons
                        reasons={customerOrderReasons}
                        orderId={orderId}
                        isCustomer={true}
                        disabled={!canEdit || disabled}
                    />
                    <OrderReasons
                        reasons={internalOrderReasons}
                        orderId={orderId}
                        onChange={(reasons: OrderReasonDto[]) => {
                            setInternalOrderReasons(reasons);
                        }}
                        isCustomer={false}
                        disabled={!canEdit || disabled}
                    />
                </DivNotesSectionContainer>
            )}
            {orderId ? (
                <DivNotesSectionContainer>
                    <AdditionalNotes
                        orderId={orderId}
                        notesType="ForCustomer"
                        notes={additionalNotes}
                        onChange={handleAdditionalNotesChange}
                        disabled={!canEdit || disabled}
                    />
                    <AdditionalNotes
                        orderId={orderId}
                        notesType="ForInternal"
                        notes={additionalNotes}
                        onChange={handleAdditionalNotesChange}
                        disabled={!canEdit || disabled}
                    />
                </DivNotesSectionContainer>
            ) : (
                <Grid container sx={{ marginTop: '10px' }}>
                    <Grid item xs={12}>
                        <Box component="div" sx={{ marginBottom: '10px' }}>
                            <Typography
                                component="span"
                                sx={(theme) => ({
                                    ...theme.typography.h6Inter,
                                    fontStyle: 'normal',
                                    fontWeight: 700,
                                    color: theme.palette.neutral[8],
                                })}
                            >
                                {t('orderDetails.notes')}
                            </Typography>
                        </Box>
                        {additionalNotes
                            .filter((x) => x.type === 'ForCustomer')
                            .map((note, index) => (
                                <Grid
                                    key={`${note.orderId}-${index}`}
                                    container
                                    spacing={0}
                                    alignItems="center"
                                >
                                    <Grid
                                        item
                                        xs={1}
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            height: '24px',
                                        }}
                                    >
                                        <Tooltip
                                            title={
                                                <TooltipContent
                                                    isFromCustomer={note.isFromCustomer}
                                                />
                                            }
                                            placement="left"
                                        >
                                            <Box component="div">
                                                {note.isFromCustomer ? (
                                                    <CustomerNoteIcon />
                                                ) : (
                                                    <TeamMemberNoteIcon />
                                                )}
                                            </Box>
                                        </Tooltip>
                                    </Grid>
                                    <Grid
                                        item
                                        xs={11}
                                        display="flex"
                                        alignItems="center"
                                        sx={{
                                            minHeight: '24px',
                                            position: 'relative',
                                            display: 'flex',
                                            alignItems: 'center',
                                        }}
                                    >
                                        <Typography
                                            component="div"
                                            sx={(theme) => ({
                                                ...theme.typography.h7Roboto,
                                                fontStyle: 'normal',
                                                fontWeight: 400,
                                                color: theme.palette.neutral[6],
                                                inlineSize: 'auto',
                                                overflowWrap: 'break-word',
                                            })}
                                        >
                                            {note.text}
                                        </Typography>
                                    </Grid>
                                </Grid>
                            ))}
                    </Grid>
                </Grid>
            )}
        </div>
    );
};

type TooltipContentProps = {
    isFromCustomer: boolean;
};

const TooltipContent = ({ isFromCustomer }: TooltipContentProps) => {
    const { t } = useAppTranslation();
    return (
        <Typography
            sx={{
                fontFamily: 'Roboto',
                fontSize: '12px',
                fontStyle: 'normal',
                fontWeight: 400,
                lineHeight: 'normal',
                color: 'var(--grey5)',
            }}
        >
            {isFromCustomer
                ? t('orderDetails.addedByCustomer')
                : t('orderDetails.addedByTeamMember')}
        </Typography>
    );
};

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const DivNotesSectionContainer = styled('div')({
    marginTop: '18px',
    display: 'flex',
    flexDirection: 'column',
    gap: '18px',
});

export default OrderNotes;
