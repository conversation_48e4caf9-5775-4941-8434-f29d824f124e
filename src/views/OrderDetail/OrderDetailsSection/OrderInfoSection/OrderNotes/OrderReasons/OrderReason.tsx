import { styled, Typography } from '@mui/material';
import MuiTooltip, { tooltipClasses, TooltipProps } from '@mui/material/Tooltip';
import { OrderReasonDto } from 'api/orders/_common';
import { ProhibitionIcon } from 'common/components/Icons/ProhibitionIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useRef, useState } from 'react';
import ActivityLogTooltipContent from 'views/Components/ActivityLogTooltipContent';
import NoteEditor from 'views/Components/NoteEditor';
import ThreeDotMenu from 'views/OrderDetail/common/ThreeDotMenu';

type OrderReasonProps = {
    orderReason: OrderReasonDto;
    onEdit: (value: OrderReasonDto) => void;
    onDelete: (value: OrderReasonDto) => void;
    isReadOnly?: boolean;
};

const OrderReason = ({ orderReason, onDelete, onEdit, isReadOnly = false }: OrderReasonProps) => {
    const { t } = useAppTranslation();

    const [isEdition, setIsEdition] = useState<boolean>(false);

    const anchorHint = useRef<HTMLDivElement | null>(null);

    const handleSave = (text: string) => {
        setIsEdition(false);
        onEdit && onEdit({ ...orderReason, text });
    };

    const handleDelete = () => {
        setIsEdition(false);
        onDelete && onDelete(orderReason);
    };

    const handleMouseMove = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;

        if (el instanceof HTMLDivElement) {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'flex';
                anchorHint.current.style.top = `${e.clientY}px`;
                anchorHint.current.style.left = `${e.clientX}px`;
            }
        } else {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    const handleMouseOut = useCallback((e: React.MouseEvent<HTMLElement>) => {
        const el = e.target;

        if (el instanceof HTMLDivElement) {
            if (anchorHint.current) {
                anchorHint.current.style.display = 'none';
            }
        }
    }, []);

    return isEdition ? (
        <NoteEditor
            text={orderReason.text}
            onEdit={(text) => {
                handleSave(text);
            }}
            onCancel={() => {
                setIsEdition(false);
            }}
            placeholder={t('orderDetails.orderReasons.enterReason')}
        />
    ) : (
        <>
            <Tooltip
                title={
                    <ActivityLogTooltipContent
                        activityLog={orderReason.history.map(({ id, text, createdAt, user }) => ({
                            id: id,
                            text: text,
                            createdAt: createdAt,
                            user: user,
                        }))}
                        isFromCustomer={orderReason.isFromCustomer}
                    />
                }
                placement="left"
            >
                <DivReasonWrapper
                    onMouseMove={(e) => isReadOnly && handleMouseMove(e)}
                    onMouseOut={(e) => {
                        handleMouseOut(e);
                    }}
                >
                    <Typography
                        component="div"
                        sx={(theme) => ({
                            ...theme.typography.h6Roboto,
                            fontWeight: 400,
                            color: theme.palette.neutral[7],
                            inlineSize: 'auto',
                            overflowWrap: 'break-word',
                            maxWidth: '100%',
                        })}
                    >
                        {orderReason.text}
                    </Typography>
                    {!isReadOnly && (
                        <div className="three-dot-container">
                            <ThreeDotMenu
                                onClose={() => {}}
                                onEdit={() => setIsEdition(true)}
                                //onDelete={handleDelete} // It is hidden on UI because requirements did not provide for this functionality.
                                // However the other logic is fully implemented. The only thing we need to make it work is just to uncomment this part.
                            />
                        </div>
                    )}
                </DivReasonWrapper>
            </Tooltip>
            <DivMouseHint ref={anchorHint}>
                <ProhibitionIcon fill="var(--neutral5)" size={10} />
                {t('orderDetails.additionalNotes.noteCannotBeEdited')}
            </DivMouseHint>
        </>
    );
};

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: theme.palette.neutral[1],
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.neutral[1],
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const DivReasonWrapper = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    padding: '4px 0px',
    borderRadius: '4px',
    width: '100%',

    '.three-dot-container': {
        display: 'none',
    },

    '&:hover': {
        backgroundColor: theme.palette.neutral[2],
        '.three-dot-container': {
            display: 'flex',
        },
    },
}));

const DivMouseHint = styled('div')(({ theme }) => ({
    minWidth: 'max-content',
    zIndex: 10,
    padding: 3,
    position: 'fixed',
    transform: 'translate(15px, 0px)',
    pointEvents: 'none',
    display: 'none',
    gap: '2px',
    ...theme.typography.h9Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[5],
}));

export default OrderReason;
