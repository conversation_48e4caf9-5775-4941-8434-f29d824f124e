import { Box, Typography } from '@mui/material';
import { styled } from '@mui/styles';
import { useMutation } from '@tanstack/react-query';
import OrderReasonsAPI from 'api/OrderReasons';
import { OrderReasonDto } from 'api/orders/_common';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import ReasonForm from 'views/Components/ReasonForm';
import OrderReason from './OrderReason';

type OrderReasonsFormProps = {
    orderId: number;
    reasons: OrderReasonDto[];
    isCustomer: boolean;
    onChange?: (reasons: OrderReasonDto[]) => void;
    disabled?: boolean;
};

const OrderReasons = ({
    reasons = [],
    orderId,
    isCustomer,
    onChange,
    disabled,
}: OrderReasonsFormProps) => {
    const { t } = useAppTranslation();
    const [reasonId, setReasonId] = useState<string>();

    const { createReasonMutate } = useCreateReasonMutation((data) => {
        onChange && onChange([...reasons, data]);
    });

    const { updateReasonMutate } = useUpdateReasonMutation((data) => {
        onChange && onChange(reasons.map((x) => (x.id === data.id ? data : x)));
    });

    const { deleteReasonMutate } = useDeleteReasonMutation(() => {
        onChange && onChange(reasons.filter((x) => x.id !== reasonId));
        setReasonId(undefined);
    });

    const handleSave = (text: string) => {
        createReasonMutate({ text, orderId });
    };

    const handleEdit = (reason: OrderReasonDto) => {
        updateReasonMutate({
            text: reason.text,
            orderReasonId: reason.id,
        });
    };

    const handleDelete = (reason: OrderReasonDto) => {
        setReasonId(reason.id);
        deleteReasonMutate({ orderReasonId: reason.id });
    };

    return (
        <div>
            <Typography
                component="div"
                sx={(theme) => ({
                    fontFamily: 'Inter',
                    fontSize: '12px',
                    fontStyle: 'normal',
                    fontWeight: 700,
                    lineHeight: 'normal',
                    color: theme.palette.neutral[8],
                    marginBottom: '2px',
                })}
            >
                {isCustomer
                    ? t('orderDetails.orderReasons.customerReasonsForVisit')
                    : t('orderDetails.orderReasons.workshopReasonsForVisit')}
            </Typography>

            {reasons.length > 0 ? (
                <ul style={{ paddingLeft: '16px', margin: 0 }}>
                    {reasons.map((reason, index) => (
                        <li>
                            <OrderReason
                                orderReason={reason}
                                onEdit={handleEdit}
                                onDelete={handleDelete}
                                isReadOnly={isCustomer || disabled}
                                key={`${reason.id}-${index}`}
                            />
                        </li>
                    ))}
                </ul>
            ) : (
                <>
                    {isCustomer && (
                        <EmptyReasonsContainer>
                            {t('orderDetails.orderReasons.noCustomerReasonsForVisit')}
                        </EmptyReasonsContainer>
                    )}
                </>
            )}
            {onChange && !isCustomer && (
                <Box sx={{ paddingTop: '3px' }}>
                    <ReasonForm disabled={disabled} onClickSave={handleSave} />
                </Box>
            )}
        </div>
    );
};

const useCreateReasonMutation = (onSuccess?: (data: OrderReasonDto) => void) => {
    const { mutate: createReasonMutate } = useMutation(
        (body: { text: string; orderId: number }) =>
            OrderReasonsAPI.create(body.orderId, body.text),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { createReasonMutate };
};

const useUpdateReasonMutation = (onSuccess?: (data: OrderReasonDto) => void) => {
    const { mutate: updateReasonMutate } = useMutation(
        (body: { text: string; orderReasonId: string }) =>
            OrderReasonsAPI.update(body.orderReasonId, body.text),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { updateReasonMutate };
};

const useDeleteReasonMutation = (onSuccess?: () => void) => {
    const { mutate: deleteReasonMutate } = useMutation(
        (body: { orderReasonId: string }) => OrderReasonsAPI.deleteReason(body.orderReasonId),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );
    return { deleteReasonMutate };
};

const EmptyReasonsContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'top',
    height: '36px',
    ...theme.typography.h6Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[7],
    padding: '4px 0',
}));

export default OrderReasons;
