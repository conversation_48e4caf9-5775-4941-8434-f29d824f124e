import { Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { useMutation } from '@tanstack/react-query';
import AdditionalNotesAPI from 'api/AdditionalNotes';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AdditionalNoteDto, AdditionalNoteType } from 'datacontracts/AdditionalNoteDto';
import { useState } from 'react';
import AdditionalNoteForm from 'views/OrderDetail/common/AdditionalNotesForm';
import Note from 'views/OrderDetail/common/AdditionalNotesForm/Note';

type AdditionalNoteFormProps = {
    orderId: number;
    notesType: AdditionalNoteType;
    notes: AdditionalNoteDto[];
    onChange: (notes: AdditionalNoteDto[]) => void;
    disabled?: boolean;
};

const AdditionalNotes = ({
    notes = [],
    orderId,
    notesType,
    onChange,
    disabled,
}: AdditionalNoteFormProps) => {
    const { t } = useAppTranslation();
    const [noteId, setNoteId] = useState<number>();

    const { addNoteMutate } = useAddNoteMutation((data) => {
        onChange && onChange([...notes, data]);
    });

    const { updateNoteMutate } = useUpdateNoteMutation((data) => {
        onChange &&
            onChange(notes.map((x) => (x.additionalNoteId === data.additionalNoteId ? data : x)));
    });

    const { deleteNoteMutate } = useDeleteNoteMutation(() => {
        onChange && onChange(notes.filter((x) => x.additionalNoteId !== noteId));
        setNoteId(undefined);
    });

    const handleSave = (text: string) => {
        addNoteMutate({ text, orderId, noteType: notesType });
    };

    const handleEdit = (note: AdditionalNoteDto) => {
        updateNoteMutate({
            text: note.text,
            additionalNoteId: note.additionalNoteId,
        });
    };

    const handleDelete = (note: AdditionalNoteDto) => {
        setNoteId(note.additionalNoteId);
        deleteNoteMutate({ additionalNoteId: note.additionalNoteId });
    };

    const filteredNotes = notes.filter((x) => x.type === notesType);

    return (
        <Box component="div">
            <Typography
                component="div"
                sx={(theme) => ({
                    fontFamily: 'Inter',
                    fontSize: '12px',
                    fontStyle: 'normal',
                    fontWeight: 700,
                    lineHeight: 'normal',
                    color: theme.palette.neutral[8],
                })}
            >
                {notesType === 'ForCustomer'
                    ? t('orderDetails.additionalNotes.notesForCustomer')
                    : t('orderDetails.additionalNotes.notesForInternal')}
            </Typography>
            {filteredNotes.length === 0 && <Box sx={{ marginBottom: '4px' }}></Box>}
            {filteredNotes.map((note, index) => (
                <Box key={`${note.additionalNoteId}-${index}`} component="div">
                    <Note
                        additionalNote={note}
                        editSection="OrderDetails"
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        disabled={disabled}
                    />
                </Box>
            ))}
            <Box sx={{ paddingTop: '3px' }}>
                <AdditionalNoteForm disabled={disabled} onClickSave={handleSave} />
            </Box>
        </Box>
    );
};

const useAddNoteMutation = (onSuccess?: (data: AdditionalNoteDto) => void) => {
    const { mutate: addNoteMutate } = useMutation(
        (body: { text: string; orderId: number; noteType: AdditionalNoteType }) =>
            AdditionalNotesAPI.addNote(body.orderId, body.text, body.noteType, 'OrderDetails'),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { addNoteMutate };
};

const useUpdateNoteMutation = (onSuccess?: (data: AdditionalNoteDto) => void) => {
    const { mutate: updateNoteMutate } = useMutation(
        (body: { text: string; additionalNoteId: number }) =>
            AdditionalNotesAPI.updateNote(body.additionalNoteId, body.text),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { updateNoteMutate };
};

const useDeleteNoteMutation = (onSuccess?: () => void) => {
    const { mutate: deleteNoteMutate } = useMutation(
        (body: { additionalNoteId: number }) =>
            AdditionalNotesAPI.deleteNote(body.additionalNoteId),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );
    return { deleteNoteMutate };
};

export default AdditionalNotes;
