import { useMutation } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { CircleIconWithBorder } from 'common/components/Icons';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useTowerAlert from 'common/hooks/useTowerInUseAlert';
import { useUsers } from 'common/hooks/useUsers';
import { KeyboardEventHandler, useEffect, useMemo, useRef } from 'react';
import { createSelector } from 'reselect';
import { getErrorResponse, hasCode } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderKey,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import AutocompleteDropdown from 'views/Components/Autocomplete/AutocompleteDropdown';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type InChargeFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectOrderKey,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, orderId, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        userId: s.inChargeUserId,
        orderId,
    })
);

export default function InChargeField({ id, isSelected, onKeyDown, disabled }: InChargeFieldProps) {
    const ref = useRef<HTMLInputElement>(null);
    const { t } = useAppTranslation();
    const { canEdit, userId, orderId } = useAppSelector(selectData);
    if (!orderId) throw new Error('orderId is undefined');
    const shopId = useAppSelector(selectOrderShopKey);
    const teamMembers = useUsers(shopId);
    const options = useMemo(
        () =>
            teamMembers
                .filter((x) => x.job === 'ServiceAdvisor')
                .map((x) => ({
                    label: `${x.initials} - ${x.name}`,
                    value: x.id!,
                    color: x.color ?? undefined,
                    icon: CircleIconWithBorder,
                })),
        [teamMembers]
    );
    const selectedOption = useMemo(() => {
        const option = options.find((x) => x.value === userId);
        if (option) return option;

        if (userId) {
            const user = teamMembers.find((x) => x.id === userId);
            if (user) {
                return {
                    label: `${user.initials} - ${user.name}`,
                    value: userId,
                    color: user.color ?? undefined,
                    icon: CircleIconWithBorder,
                };
            } else {
                return {
                    label: `${userId}`,
                    value: userId,
                };
            }
        }

        return null;
    }, [options, teamMembers, userId]);
    const dispatch = useAppDispatch();

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    const { showTowerInUseAlert } = useTowerAlert();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const update = useMutation(
        async (id: number) => {
            dispatch(orderActions.update({ inChargeUserId: id }));

            const result = await OrderAPI.saveInCharge({ orderId, inChargeUserId: id });
            showOrderUpdateNotification(result);
        },
        {
            onError: (err) => {
                const response = getErrorResponse(err);

                if (response && hasCode(response, 'General.WP.TowerNumberConflict')) {
                    showTowerInUseAlert(response.meta.takenBy.number ?? '');
                }
            },
        }
    );

    return (
        <AutocompleteDropdown<number | null>
            inputRef={ref}
            label={t('orderDetails.inCharge')}
            disabled={!canEdit || disabled}
            loading={update.isLoading}
            placeholder={t('orderDetails.inCharge')}
            options={options}
            value={selectedOption}
            onChange={(value) => value && typeof value === 'number' && update.mutate(value)}
            onBlur={() => {
                dispatch(orderActions.clearSelectedField({ id }));
            }}
            getOptionKey={(x) => x ?? -1}
            onKeyDown={onKeyDown}
        />
    );
}
