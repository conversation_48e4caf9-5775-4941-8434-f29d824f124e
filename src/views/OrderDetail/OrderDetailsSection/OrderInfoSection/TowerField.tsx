import { useMutation } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import WpOrdersApi from 'api/workshopPlanner/orders';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useTowerAlert from 'common/hooks/useTowerInUseAlert';
import { KeyboardEventHandler, useEffect, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { getErrorResponse, hasCode } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    selectIsOrderClosed,
    selectOrder,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useOrderDetailsValidationsContext } from 'views/OrderDetail/providers';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type TowerFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectOrder,
        selectOrderShopKey,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, order, shopKey, loading, orderIsClosed) => ({
        shopKey,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.tower,
        orderNumber: order?.repairOrderNumber,
    })
);

export default function TowerField({ id, isSelected, onKeyDown, disabled }: TowerFieldProps) {
    const ref = useRef<HTMLDivElement>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const isEnterprise = useIsEnterpriseRoute();
    const { canEdit, value: originalValue, orderNumber, shopKey } = useAppSelector(selectData);
    const { showTowerInUseAlert } = useTowerAlert();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const { redirectField, focusElementInsideContainer } = useOrderDetailsValidationsContext();

    const mutation = useMutation(
        async (value: string) => {
            setIsTowerUsed(false);
            if (isEnterprise) {
                return EnterpriseOrdersApi.orders.updateTower(shopKey!, orderNumber!, value);
            } else {
                const result = await WpOrdersApi.updateOrder(orderNumber!, { tower: value });
                showOrderUpdateNotification(result);
                return result.orderInfo.tower;
            }
        },
        {
            onSuccess: (tower: string | null) => {
                if (tower) dispatch(orderActions.update({ tower: tower }));
            },
            onError: (err) => {
                const response = getErrorResponse(err);

                if (response && hasCode(response, 'General.WP.TowerNumberConflict')) {
                    setIsTowerUsed(true);
                    showTowerInUseAlert(response.meta.takenBy.number ?? '');
                }
            },
        }
    );

    const updateTower = async (value: string) => {
        await mutation.mutateAsync(value);
    };

    const [isTowerUsed, setIsTowerUsed] = useState(false);

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    useEffect(() => {
        if (redirectField !== null && redirectField.fieldId === id)
            focusElementInsideContainer(() => ref.current?.focus());
    }, [redirectField, focusElementInsideContainer, id]);

    return (
        <OrderDetailsResettableTextFormField
            inputRef={ref}
            name="email"
            label={t('orderDetails.tower')}
            value={originalValue}
            disabled={!canEdit || disabled}
            onSave={(value) => updateTower(value)}
            placeholder={t('orderDetails.enterTower')}
            isInvalid={isTowerUsed}
            size="small"
            onFocus={() => setIsTowerUsed(false)}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
            onKeyDown={onKeyDown}
        />
    );
}
