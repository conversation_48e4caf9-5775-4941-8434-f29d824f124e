import { useMutation } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import EnterprisePhaseSetbackApi from 'api/enterprise/phaseSetback';
import PhaseSetbackApi from 'api/phaseSetback';
import { PUBNUB_CHANNELS } from 'api/pubnub';
import WpOrdersApi, { OrderUpdatedEvent } from 'api/workshopPlanner/orders';
import {
    OrderJobsInProgressCheckProvider,
    useOrderJobsInProgressCheck,
} from 'common/components/ChangePhaseProviders/OrderJobsInProgressCheck';
import {
    PhaseSetbackCheckProvider,
    usePhaseSetbackCheck,
} from 'common/components/ChangePhaseProviders/PhaseSetbackCheck';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import isEqual from 'lodash/isEqual';
import {
    ForwardedRef,
    forwardRef,
    KeyboardEventHandler,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    orderActions,
    reloadOrderThunk,
    selectIsOrderClosed,
    selectOrder,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { usePubnubListener } from 'utils/pubnub';
import { getPhaseName, useOrderPhases } from 'views/OrderDetail/util';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type PhaseFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectOrderShopKey,
        selectOrder,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, shopKey, order, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.phaseId,
        shopKey,
        orderKey: order?.key,
        orderNumber: order?.repairOrderNumber,
    })
);

export default function PhaseField({ id, isSelected, onKeyDown, disabled }: PhaseFieldProps) {
    const ref = useRef<HTMLSelectElement>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();
    const { canEdit, value, shopKey, orderKey, orderNumber } = useAppSelector(selectData, isEqual);
    if (!orderNumber) throw new Error('orderNumber is undefined');
    const isEnterprise = useIsEnterpriseRoute();
    if (isEnterprise && !shopKey) throw new Error('shopKey is undefined in enterprise mode');
    const { options } = usePhasesState();
    const settings = useAppSelector(selectSettings);

    const update = useMutation(
        async ({ phaseId, reason }: { phaseId: number | null; reason?: string }) => {
            ref.current?.blur();

            if (!phaseId) return;

            const originPhaseId = value;

            dispatch(orderActions.update({ phaseId }));
            if (isEnterprise) {
                await EnterpriseOrdersApi.phases.updatePhase(shopKey!, orderNumber, phaseId);
            } else {
                const result = await WpOrdersApi.updateOrder(orderNumber, { phaseId });
                dispatch(
                    orderActions.update({ assignedToUserId: result.orderInfo.assignedTo?.intId })
                );

                showOrderUpdateNotification(result);
            }

            // NOTE (AK) little workaround
            if (phaseId === Phases.Closed) {
                dispatch(reloadOrderThunk());
            }

            if (reason) {
                const phaseSetback = {
                    orderKey: orderKey!,
                    originPhaseId,
                    destinationPhaseId: phaseId,
                    reason,
                };
                if (isEnterprise) {
                    await EnterprisePhaseSetbackApi.savePhaseSetback(shopKey!, phaseSetback);
                } else {
                    await PhaseSetbackApi.savePhaseSetback(phaseSetback);
                }
            }
        }
    );

    const formattedSelectedOption = useMemo(() => {
        const selectedOption = options.find((x) => x.value === value);
        return selectedOption
            ? {
                  label: t('orderDetails.phase') + selectedOption.label,
                  value: selectedOption.value,
              }
            : selectedOption;
    }, [t, options, value]);

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    usePubnubListener<OrderUpdatedEvent>(
        async (_message) => {
            if (_message.message.payload.orderKey === orderKey) {
                dispatch(orderActions.update({ phaseId: _message.message.payload.phaseId }));
            }
        },
        {
            channels: [PUBNUB_CHANNELS.orders(settings.uid)],
            types: ['order.updated'],
            listenerEnabled: settings.appMode === 'RepairShop',
            unsubscribeIfListenerDisabled: true,
        }
    );

    return (
        <PhaseSetbackCheckProvider>
            <OrderJobsInProgressCheckProvider>
                <PhasePicker
                    ref={ref}
                    cmosVariant="grey"
                    name="phases"
                    disabled={!canEdit || disabled}
                    placeholder={'Enter tower'}
                    options={options}
                    value={formattedSelectedOption}
                    onChange={(phaseId, reason) => {
                        update.mutate({ phaseId, reason });
                    }}
                    onEnterPress={onKeyDown}
                    onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
                    openMenuOnFocus
                    repairShopKey={isEnterprise ? shopKey! : undefined}
                    orderId={orderKey!}
                    orderNumber={orderNumber}
                />
            </OrderJobsInProgressCheckProvider>
        </PhaseSetbackCheckProvider>
    );
}

type PhasePickerProps = {
    repairShopKey?: string;
    orderId: string;
    orderNumber: string;
    onChange: (phaseId: number, reason?: string) => void;
} & Omit<DropdownProps, 'onChange'>;

const PhasePicker = forwardRef(
    (
        {
            repairShopKey,
            value: originPhaseId,
            orderId,
            orderNumber,
            onChange,
            ...props
        }: PhasePickerProps,
        ref?: ForwardedRef<HTMLSelectElement>
    ) => {
        const phaseSetbackCheck = usePhaseSetbackCheck();
        const orderJobsInProgressCheck = useOrderJobsInProgressCheck();

        const [showLoader, setShowLoader] = useState(false);

        const handleChangePhase = (destinationPhaseId: OptionData<number>) => {
            if (!originPhaseId) {
                onChange(destinationPhaseId.value);
                return;
            }

            if (originPhaseId.value === destinationPhaseId.value) {
                return;
            }

            setShowLoader(true);

            phaseSetbackCheck.checkPhasesIds(
                {
                    originPhaseId: originPhaseId.value,
                    destinationPhaseId: destinationPhaseId.value,
                    repairShopKey,
                },
                (reason?: string) =>
                    orderJobsInProgressCheck.checkOrderJobsInProgress(
                        { destinationPhaseId: destinationPhaseId.value, orderId, orderNumber },
                        () => {
                            onChange(destinationPhaseId.value, reason);
                            setShowLoader(false);
                        },
                        () => setShowLoader(false)
                    ),
                () => setShowLoader(false)
            );
        };

        return (
            <Dropdown
                ref={ref}
                {...props}
                value={originPhaseId}
                onChange={(option) => {
                    if (option !== null) handleChangePhase(option);
                }}
                showLoader={showLoader}
            />
        );
    }
);

function usePhasesState() {
    const { t } = useAppTranslation();
    const phases = useOrderPhases();

    const options = useMemo(
        () =>
            phases.map((x) => ({
                label: getPhaseName(x, t),
                value: x.id,
            })),
        [t, phases]
    );

    return {
        options,
    };
}
