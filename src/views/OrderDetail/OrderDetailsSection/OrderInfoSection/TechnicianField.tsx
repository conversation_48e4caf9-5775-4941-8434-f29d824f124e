import { Box } from '@mui/material';
import { UserListItem } from 'api/users';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useRef } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    saveOrderTechniciansThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderKey,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { MultipleUserSelectByJobTitle } from 'views/Components/MultipleUserSelect';
import { useTechniciansIds } from './helpers';
import { createSelector } from 'reselect';
import { selectUserPermission } from '../../../../store/slices/user';

type TechnicianFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: React.KeyboardEventHandler;
};

const selectData = createSelector(
    [selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (perm, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
    })
);

export default function TechnicianField({
    id: _id,
    isSelected,
    onKeyDown: _onKeyDown,
}: TechnicianFieldProps) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLInputElement>(null);
    const dispatch = useAppDispatch();

    const { canEdit } = useAppSelector(selectData);
    const shopId = useAppSelector(selectOrderShopKey);
    const technicianIds = useTechniciansIds(shopId);

    function handleChange(_keys: string[], users: UserListItem[]) {
        const ids = users.map((x) => x.id);
        dispatch(saveOrderTechniciansThunk(ids));
    }

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <InputWrapper fullWidth label={t('commonLabels.technician')} disabled={!canEdit}>
            <Box sx={{ flex: 1 }}>
                <MultipleUserSelectByJobTitle
                    ref={ref}
                    slotProps={{
                        textField: {
                            cmosVariant: 'grey',
                        },
                    }}
                    shopId={shopId}
                    jobTitle="Technician"
                    value={technicianIds}
                    onChange={handleChange}
                    onBlur={() => dispatch(orderActions.clearSelectedField({ id: _id }))}
                    disabled={!canEdit}
                />
            </Box>
        </InputWrapper>
    );
}
