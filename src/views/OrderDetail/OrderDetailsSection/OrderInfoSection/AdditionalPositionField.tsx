import { selectClasses } from '@mui/material';
import { createSelector } from '@reduxjs/toolkit';
import { FieldValueWithName, isPredefinedField, PredefinedFieldValue } from 'api/fields';
import { UserListItem } from 'api/users/_common';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { KeyboardEventHandler, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderInfoFields,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { saveAdditionalPositionThunk } from 'store/slices/order/orderDetails/thunks/saveAdditionalPosition';
import { selectUserPermission } from 'store/slices/user';
import { selectUsers } from 'store/slices/users';
import UserSelectByJobTitle from 'views/Components/UserSelect/UserSelectByJobTitle';

type AdditionalPositionFieldProps = {
    id: string;
    isSelected: boolean;
    disabled: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
};

const selectAdditionalPosition = createSelector(
    [selectOrderData, selectUsers, (_, num: number) => num],
    (state, users, num) => {
        let id: number | null = null;

        switch (num) {
            case 1:
                id = state.additionalPositionUserId1;
                break;
            case 2:
                id = state.additionalPositionUserId2;
                break;
            case 3:
                id = state.additionalPositionUserId3;
                break;
            case 4:
                id = state.additionalPositionUserId4;
                break;
            default:
                return null;
        }

        if (id) {
            for (const key in users) {
                if (users[key].id === id) {
                    return key;
                }
            }
        }

        return null;
    }
);

const selectCanEdit = createSelector(
    [selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (perm, loading, orderIsClosed) => perm.allowEditOrders && !loading && !orderIsClosed
);

function isPredefined(field: FieldValueWithName): field is PredefinedFieldValue {
    return isPredefinedField(field.type);
}

export function AdditionalPositionField({ id, isSelected }: AdditionalPositionFieldProps) {
    const ref = useRef<HTMLInputElement>(null);
    const dispatch = useAppDispatch();
    const { t } = useTranslation();
    const field = useAppSelector((r) => selectOrderInfoFields(r).find((x) => x.id === id));
    const shopId = useAppSelector(selectOrderShopKey);
    const canEdit = useAppSelector(selectCanEdit);

    if (!field) {
        throw new Error('cannot find additional position field with id ' + id);
    }

    if (!isPredefined(field)) {
        throw new Error('additional position field with id ' + id + ' is not predefined');
    }

    const positionIndex = field.additionalPositionIndex ?? 0;
    const value = useAppSelector((r) => selectAdditionalPosition(r, positionIndex));
    const jobTitle = field.additionalPositionJobTitle ?? 'Technician';

    function handleChange(_userId: string, user: UserListItem | undefined) {
        if (typeof positionIndex !== 'number')
            throw new Error('cannot update additional position field without provided index');
        if (!user)
            throw new Error(
                'cannot update additional position field without UserListItem, this might indicate a bug in the field'
            );
        dispatch(
            saveAdditionalPositionThunk({
                index: positionIndex,
                userId: user.id,
            })
        );
    }

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    if (!field) {
        return <span>Invalid additional position field. Field with id {id} not found.</span>;
    }

    const translatedKey = `settings.customizableFields.predefined.${field.name.replaceAll(
        '.',
        '_'
    )}`;

    const name = t(translatedKey, { defaultValue: field.name });

    return (
        <InputWrapper label={name} id={`additionalPosition${positionIndex}`} disabled={!canEdit}>
            <UserSelectByJobTitle
                inputRef={ref}
                labelId={`additionalPosition${positionIndex}`}
                shopId={shopId}
                sx={{
                    backgroundColor: 'var(--neutral2)',
                    [`& .${selectClasses.icon}`]: {
                        backgroundColor: 'var(--neutral2)',
                    },
                }}
                menuBorders
                userId={value}
                onChange={handleChange}
                placeholder={name}
                jobTitle={jobTitle}
                onBlur={() => {
                    dispatch(orderActions.clearSelectedField({ id }));
                }}
                disabled={!canEdit}
            />
        </InputWrapper>
    );
}
