import { useMutation } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { CircleIconWithBorder } from 'common/components/Icons';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useMemo, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderId,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import AutocompleteDropdown from 'views/Components/Autocomplete/AutocompleteDropdown';
import { useOrderTypes } from 'views/OrderDetail/util';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type OrderTypeFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderId, selectOrderLoading, selectIsOrderClosed],
    (s, perm, orderId, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        value: s.orderTypeId,
        orderId,
    })
);

export default function OrderTypeField({
    id,
    isSelected,
    onKeyDown,
    disabled,
}: OrderTypeFieldProps) {
    const ref = useRef<HTMLInputElement>(null);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();
    const { canEdit, value, orderId } = useAppSelector(selectData, isEqual);
    if (!orderId) throw new Error('orderId is undefined');

    const update = useMutation(async (id: string) => {
        dispatch(orderActions.update({ orderTypeId: id }));
        const result = await OrderAPI.saveOrderType(orderId, id);

        showOrderUpdateNotification(result);
    });
    const orderTypes = useOrderTypes();
    const orderTypeOptions = useMemo(
        () =>
            orderTypes.map((type) => ({
                value: type.key,
                label: type.name,
                color: type.color,
                icon: CircleIconWithBorder,
            })),
        [orderTypes]
    );

    const selectedOption = useMemo(
        () => orderTypeOptions.find((x) => x.value === value),
        [value, orderTypeOptions]
    );

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <AutocompleteDropdown<string | null>
            inputRef={ref}
            label={t('orderDetails.repairOrderType')}
            disabled={!canEdit || disabled}
            loading={update.isLoading}
            placeholder={t('orderDetails.repairOrderType')}
            options={orderTypeOptions}
            value={selectedOption}
            onChange={(value) => value && update.mutate(value)}
            onBlur={() => {
                dispatch(orderActions.clearSelectedField({ id }));
            }}
            getOptionKey={(x) => x ?? -1}
            onKeyDown={onKeyDown}
        />
    );
}
