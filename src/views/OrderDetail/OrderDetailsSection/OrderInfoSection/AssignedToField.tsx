import { useMutation } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useUsers } from 'common/hooks/useUsers';
import { KeyboardEventHandler, useEffect, useMemo, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderId,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import AutocompleteDropdown from 'views/Components/Autocomplete/AutocompleteDropdown';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type AssignedToFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderId, selectOrderLoading, selectIsOrderClosed],
    (s, perm, orderId, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        userId: s.assignedToUserId,
        orderId,
    })
);

export default function AssignedToField({
    id,
    isSelected,
    onKeyDown,
    disabled,
}: AssignedToFieldProps) {
    const ref = useRef<HTMLInputElement>(null);
    const { t } = useAppTranslation();
    const { canEdit, userId, orderId } = useAppSelector(selectData);
    if (!orderId) throw new Error('orderId is undefined');
    const shopId = useAppSelector(selectOrderShopKey);
    const teamMembers = useUsers(shopId);
    const options = useMemo(
        () =>
            teamMembers.map((x) => ({
                label: `${x.initials} - ${x.name}`,
                value: x.id!,
            })),
        [teamMembers]
    );
    const selectedOption = useMemo(() => {
        const option = options.find((x) => x.value === userId);
        if (option) return option;

        if (userId) {
            const user = teamMembers.find((x) => x.id === userId);
            if (user) {
                return {
                    label: `${user.initials} - ${user.name}`,
                    value: userId,
                };
            } else {
                return {
                    label: `${userId}`,
                    value: userId,
                };
            }
        }

        return null;
    }, [options, userId, teamMembers]);
    const dispatch = useAppDispatch();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();
    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    const update = useMutation(async (id: number) => {
        dispatch(orderActions.update({ assignedToUserId: id }));
        const result = await OrderAPI.saveAssignedTo(orderId, id);
        showOrderUpdateNotification(result);

        return result;
    });

    return (
        <AutocompleteDropdown<number | null>
            inputRef={ref}
            label={t('commonLabels.assignedTo')}
            disabled={!canEdit || disabled}
            loading={update.isLoading}
            placeholder={t('commonLabels.assignedTo')}
            options={options}
            value={selectedOption}
            onChange={(value) => value && typeof value === 'number' && update.mutate(value)}
            onBlur={() => {
                dispatch(orderActions.clearSelectedField({ id }));
            }}
            getOptionKey={(x) => x ?? -1}
            onKeyDown={onKeyDown}
        />
    );
}
