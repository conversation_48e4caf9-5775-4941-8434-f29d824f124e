import { inputBaseClasses, styled } from '@mui/material';
import ResettableMaskedTextFormField from 'common/components/Inputs/ResettableMaskedTextField';
import ResettableTextFormField from 'common/components/Inputs/ResettableTextField';

export const OrderDetailsResettableTextFormField = styled(ResettableTextFormField)({
    [`& .${inputBaseClasses.root}`]: {
        backgroundColor: 'var(--neutral2)',
    },
});

export const OrderDetailsResettableMaskedTextFormField = styled(ResettableMaskedTextFormField)({
    [`& .${inputBaseClasses.root}`]: {
        backgroundColor: 'var(--neutral2)',
    },
});
