import { useQuery } from '@tanstack/react-query';
import { EnterpriseOrdersApi } from 'api/enterprise/orders';
import WpPhasesApi from 'api/workshopPlanner/phases';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import React, { Dispatch, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';

export function blurOnEnterHandler(e: React.KeyboardEvent<HTMLInputElement>) {
    if (e.key === 'Enter' || e.key === 'Escape') {
        setTimeout(() => {
            (e.target as HTMLElement).blur();
        });
    }
}

function getChanges<T>(changedMap: Partial<Record<keyof T, boolean>>, source: T): Partial<T> {
    const changes: Partial<T> = {};
    for (const k in changedMap) {
        if (changedMap[k]) {
            changes[k as keyof T] = source[k];
        }
    }
    return changes;
}

export function usePartiallyUpdatableObject<T extends Record<string, any>>(value: T) {
    const state = useRef({ ...value });
    const originalValue = useRef<T>(value);
    const loading = useRef<Partial<Record<keyof T, boolean>>>({});
    const changed = useRef<Partial<Record<keyof T, boolean>>>({});
    const forceRender = useForceRender();

    const reset = useCallback(
        (key: keyof T) => {
            if (originalValue.current[key] === state.current[key]) return;
            state.current[key] = originalValue.current[key];
            changed.current[key] = false;
            forceRender();
        },
        [state, forceRender]
    );

    const isLoading = useCallback((key: keyof T) => loading.current[key] ?? false, []);

    const setLoading = useCallback(
        (key: keyof T, v: boolean) => {
            loading.current[key] = v;
            forceRender();
        },
        [forceRender]
    );

    const set = useCallback(
        <K extends keyof T>(key: K, value: T[K]) => {
            changed.current[key] = originalValue.current[key] !== value;
            state.current[key] = value;
            forceRender();
        },
        [state, forceRender]
    );

    const saveWithPromise = useCallback(
        <K extends keyof T>(
            key: K | undefined,
            callback: (changes: Partial<T>) => Promise<void>
        ) => {
            if (key) setLoading(key, true);
            const changes = getChanges(changed.current, state.current);
            callback(changes).finally(() => {
                if (key) setLoading(key, false);
                changed.current = {};
            });
        },
        [setLoading]
    );

    useEffect(() => {
        if (state.current !== value) {
            originalValue.current = value;
            changed.current = {};
            loading.current = {};
            state.current = { ...value };
            forceRender();
        }
    }, [value, forceRender]);

    const isChanged = useCallback(() => Object.values(changed.current).some((v) => v), []);

    return {
        updatedState: state.current,
        reset,
        set,
        isLoading,
        isChanged,
        saveWithPromise,
        getChanges: useCallback(() => getChanges(changed.current, state.current), []),
    };
}

const selectIsEnterprise = createSelector(selectSettings, (s) => s.appMode === 'Enterprise');

export function usePhasesState(shopId: string) {
    const isEnterprise = useAppSelector(selectIsEnterprise);
    const { t } = useAppTranslation();
    const queryKey = isEnterprise ? ['enterprise', 'phases', shopId] : ['phases'];
    const queryFn = isEnterprise
        ? () => EnterpriseOrdersApi.phases.getByShopId(shopId)
        : () => WpPhasesApi.getPhases();
    const { data } = useQuery(queryKey, queryFn);
    const phases = useMemo(() => data ?? [], [data]);

    const options = useMemo(
        () =>
            phases.map((x) => {
                if (x.id === Phases.Closed) {
                    return {
                        label: t('orderDetails.closedOrder'),
                        value: Phases.Closed,
                    };
                } else if (x.id === Phases.NoPhase) {
                    return {
                        value: Phases.NoPhase,
                        label: t('orderDetails.noPhase'),
                    };
                } else {
                    return {
                        value: x.id,
                        label: x.name,
                    };
                }
            }),
        [t, phases]
    );

    return {
        phases,
        options,
    };
}

export function useResettableState<T>(originalValue: T): [T, Dispatch<T>, () => void] {
    const [value, setValue] = useState(originalValue);
    const originalValueRef = useRef(originalValue);
    originalValueRef.current = originalValue;

    useEffect(() => {
        setValue(originalValue);
    }, [originalValue]);

    const reset = useCallback(() => {
        setValue(originalValueRef.current);
    }, []);

    return [value, setValue, reset];
}
