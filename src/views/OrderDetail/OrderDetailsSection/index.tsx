import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectOrder } from 'store/slices/order/orderDetails';
import CustomerAutocompleteWithPopup from './CustomerAutocompleteWithPopup';
import CustomerInfoSection from './CustomerInfoSection';
import OrderInfoSection from './OrderInfoSection';
import VehicleAutocompleteWithPopup from './VehicleAutocompleteWithPopup';
import VehicleInfoSection from './VehicleInfoSection';

const selectOrderLoaded = createSelector(selectOrder, (x) => !!x);

const CommonSectionTitle = styled('div')(({ theme }) => ({
    marginBottom: 6,
    color: theme.palette.neutral[7],
    ...theme.typography.h6Inter,

    [theme.breakpoints.down('md')]: {
        fontSize: '1rem',
        lineHeight: 'initial',
    },
}));

const OrderSectionTitle = styled(CommonSectionTitle)({
    marginBottom: 26,
});

const CssGrid = styled('div')(({ theme }) => ({
    paddingLeft: 40,
    paddingRight: 40,
    paddingTop: 25,
    display: 'grid',
    rowGap: 12,
    columnGap: 35,
    gridTemplate: 'auto 1fr / minmax(0, 1fr) minmax(0, 1fr) minmax(0, 1fr)',
    gridTemplateAreas: '"ch vh oi" "cs vs oi"',
    '& > .ch': { gridArea: 'ch' },
    '& > .vh': { gridArea: 'vh' },
    '& > .oi': { gridArea: 'oi' },
    '& > .cs': { gridArea: 'cs' },
    '& > .vs': { gridArea: 'vs' },

    '& > *': {
        overflow: 'hidden',
    },

    [theme.breakpoints.down('md')]: {
        display: 'flex',
        flexDirection: 'column',
        paddingLeft: 16,
        paddingRight: 16,
    },
}));

export default function OrderDetailsSection() {
    const { t } = useAppTranslation();
    const isLoaded = useAppSelector(selectOrderLoaded);

    return (
        <CssGrid>
            <div className="ch">
                <CommonSectionTitle>{t('orderDetails.customerInformation')}</CommonSectionTitle>

                <div style={{ marginTop: 26 }}>{isLoaded && <CustomerAutocompleteWithPopup />}</div>
            </div>

            <div className="cs">{isLoaded && <CustomerInfoSection />}</div>

            <div className="vh">
                <CommonSectionTitle>{t('orderDetails.vehicleInformation')}</CommonSectionTitle>

                <div style={{ marginTop: 26 }}>{isLoaded && <VehicleAutocompleteWithPopup />}</div>
            </div>

            <div className="vs">{isLoaded && <VehicleInfoSection />}</div>

            <div className="oi">
                <OrderSectionTitle>{t('orderDetails.orderInformation')}</OrderSectionTitle>

                {isLoaded && <OrderInfoSection />}
            </div>
        </CssGrid>
    );
}
