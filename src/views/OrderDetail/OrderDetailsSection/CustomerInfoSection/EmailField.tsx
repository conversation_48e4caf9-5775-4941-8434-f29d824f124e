import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type EmailFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (s, perm, loading, orderIsClosed) => ({
        value: s.customer.email,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
    })
);

export default function EmailField({ id, isSelected, onKeyDown, disabled }: EmailFieldProps) {
    const ref = useRef<HTMLDivElement>(null);
    const { canEdit, value: originalValue } = useAppSelector(selectData, isEqual);
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const mutation = useMutation(
        async (value: string) => {
            dispatch(orderActions.update({ customer: { email: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updateEmail = async (value: string) => {
        await mutation.mutateAsync(value);
    };

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <OrderDetailsResettableTextFormField
            inputRef={ref}
            name="email"
            label={t('commonLabels.email')}
            value={originalValue}
            disabled={!canEdit || disabled}
            onSave={(value) => updateEmail(value)}
            placeholder={t('orderDetails.emailPlaceholder')}
            size="small"
            onKeyDown={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
        />
    );
}
