import { Grid } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type CustomerNameFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectOrderLoading, selectUserPermission, selectIsOrderClosed],
    (s, loading, perm, orderIsClosed) => ({
        firstName: s.customer.firstName,
        lastName: s.customer.lastName,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
    })
);

export default function CustomerNameField({
    id,
    isSelected,
    onKeyDown,
    disabled,
}: CustomerNameFieldProps) {
    const firstNameRef = useRef<HTMLDivElement>(null);
    const lastNameRef = useRef<HTMLDivElement>(null);

    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const [selectedSecondElement, setSelectedSecondElement] = useState(false);

    const {
        canEdit,
        firstName: originalFirstName,
        lastName: originalLastName,
    } = useAppSelector(selectData, isEqual);
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const mutationFirstName = useMutation(
        async (value: string) => {
            dispatch(orderActions.update({ customer: { firstName: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const mutationLastName = useMutation(
        async (value: string) => {
            dispatch(orderActions.update({ customer: { lastName: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updateFirstName = async (value: string) => {
        await mutationFirstName.mutateAsync(value);
    };

    const updateLastName = async (value: string) => {
        await mutationLastName.mutateAsync(value);
    };

    useEffect(() => {
        if (isSelected) {
            firstNameRef.current?.focus();
        }
    }, [isSelected]);

    useEffect(() => {
        if (selectedSecondElement) {
            lastNameRef.current?.focus();
        }
    }, [selectedSecondElement]);

    return (
        <Grid container spacing={1} alignItems="flex-end">
            <Grid item xs={6}>
                <OrderDetailsResettableTextFormField
                    inputRef={firstNameRef}
                    name="firstName"
                    label={t('commonLabels.name')}
                    value={originalFirstName}
                    disabled={!canEdit || disabled}
                    onSave={(value) => updateFirstName(value)}
                    placeholder={t('orderDetails.firstNamePlaceholder')}
                    size="small"
                    onEnterPress={() => setSelectedSecondElement(true)}
                    onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
                />
            </Grid>
            <Grid item xs={6}>
                <OrderDetailsResettableTextFormField
                    inputRef={lastNameRef}
                    name="lastName"
                    value={originalLastName}
                    disabled={!canEdit || disabled}
                    onSave={(value) => updateLastName(value)}
                    placeholder={t('orderDetails.lastNamePlaceholder')}
                    size="small"
                    onEnterPress={(e) => {
                        onKeyDown(e);
                        setSelectedSecondElement(false);
                    }}
                    onBlur={() => {
                        setSelectedSecondElement(false);
                    }}
                />
            </Grid>
        </Grid>
    );
}
