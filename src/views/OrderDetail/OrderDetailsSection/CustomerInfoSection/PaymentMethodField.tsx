import { useMutation } from '@tanstack/react-query';
import Dropdown from 'common/components/Inputs/Dropdown';
import { PaymentMethodType, PaymentMethodTypeLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useMemo, useRef } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type PaymentMethodFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectSettings,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, gs, loading, orderIsClosed) => ({
        value: s.customer.paymentMethod,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        enabled: !!gs.repairShopSettings?.features.paymentMethod,
    })
);

export default function PaymentMethodField({
    id,
    isSelected,
    onKeyDown,
    disabled,
}: PaymentMethodFieldProps) {
    const ref = useRef<HTMLSelectElement>(null);
    const { canEdit, value, enabled } = useAppSelector(selectData, isEqual);
    const { t } = useAppTranslation();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();
    const paymentMethodTypeOptions = useMemo(
        () =>
            Object.values(PaymentMethodType).map((value) => {
                return { label: t(PaymentMethodTypeLabel(value)), value };
            }),
        [t]
    );

    const dispatch = useAppDispatch();
    const update = useMutation(
        async (paymentMethod: PaymentMethodType | null) => {
            dispatch(orderActions.update({ customer: { paymentMethod } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const selectedOption = useMemo(
        () => (value ? paymentMethodTypeOptions.find((x) => x.value === value) : undefined),
        [paymentMethodTypeOptions, value]
    );

    useEffect(() => {
        if (isSelected && enabled) {
            ref.current?.focus();
        }
    }, [isSelected]);

    if (!enabled) return null;

    return (
        <Dropdown
            ref={ref}
            name="PaymentMethodType"
            label={t('orderDetails.paymentMethod')}
            placeholder={t('orderDetails.paymentMethod')}
            options={paymentMethodTypeOptions}
            disabled={!canEdit || disabled}
            cmosVariant="grey"
            showLoader={update.isLoading}
            value={selectedOption}
            onChange={(event) => event && update.mutate(event.value)}
            onEnterPress={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
            openMenuOnFocus
        />
    );
}
