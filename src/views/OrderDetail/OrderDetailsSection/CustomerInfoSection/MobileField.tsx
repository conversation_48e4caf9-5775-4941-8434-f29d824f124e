import { useMutation } from '@tanstack/react-query';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { phoneFormatRegexMask } from 'common/FormatersHelper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef, useState } from 'react';
import MaskedInput from 'react-text-mask';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { OrderDetailsResettableMaskedTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type MobileFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectSettings,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, gs, loading, orderIsClosed) => ({
        value: s.customer.mobile,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        mask: phoneFormatRegexMask(gs.internationalization.phoneNumberFormat),
        maxLengthPhone: InternationalizationLogic.maxLengthPhone(gs.internationalization),
    })
);

export default function MobileField({ id, isSelected, onKeyDown, disabled }: MobileFieldProps) {
    const ref = useRef<MaskedInput>(null);
    const {
        canEdit,
        maxLengthPhone,
        mask,
        value: originalValue,
    } = useAppSelector(selectData, isEqual);

    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const [visibleValue, setvisibleValue] = useState(originalValue);

    const mutation = useMutation(
        async (value: string) => {
            setvisibleValue(value);
            dispatch(orderActions.update({ customer: { mobile: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updateMobile = async (value: string) => {
        await mutation.mutateAsync(value);
    };

    useEffect(() => {
        if (isSelected) {
            ref.current?.inputElement.focus();
        }
    }, [isSelected]);

    return (
        <OrderDetailsResettableMaskedTextFormField
            ref={ref}
            name="mobile"
            label={t('commonLabels.mobile')}
            mask={mask}
            showMask={!!visibleValue.length}
            maxLength={maxLengthPhone}
            value={originalValue}
            disabled={!canEdit || disabled}
            onSave={(value) => updateMobile(value)}
            placeholder={t('orderDetails.mobilePlaceholder')}
            size="small"
            onKeyDown={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
        />
    );
}
