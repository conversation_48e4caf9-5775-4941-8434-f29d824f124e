import { PaymentMethodType } from 'common/constants';
import { IdentificationDocumentTypeDto } from 'datacontracts/Order/IdentificationDocumentDto';
import { useAppSelector } from 'store';
import { selectCustomerFields } from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user';
import Column from '../Column';
import CustomFieldRenderer from '../CustomFieldRenderer';
import CustomerNotifications from './CustomerNotifications';
import CustomerSignature from './CustomerSignature';

export type CustomerInfo = {
    id?: string;
    firstName: string;
    mobile: string;

    lastName: string;
    landline: string;
    email: string;
    businessName: string;
    identificationDocumentNumber: string;
    identificationDocumentType: IdentificationDocumentTypeDto | null;
    paymentMethodType: PaymentMethodType | null;
};

export function getDefaultCustomerInfo(): CustomerInfo {
    return {
        firstName: '',
        mobile: '',
        lastName: '',
        landline: '',
        email: '',
        businessName: '',
        identificationDocumentNumber: '',
        identificationDocumentType: null,
        paymentMethodType: null,
    };
}

export default function CustomerInfoSection() {
    const fields = useAppSelector(selectCustomerFields);

    const allowEditCustomers = useAppSelector((r) => selectUserPermission(r).allowEditCustomers);

    return (
        <Column>
            {fields.map((field) => {
                return (
                    <CustomFieldRenderer
                        key={field.id}
                        disabledPredefinedFields={!allowEditCustomers}
                        field={field}
                    />
                );
            })}

            <CustomerSignature />
            <CustomerNotifications />
        </Column>
    );
}
