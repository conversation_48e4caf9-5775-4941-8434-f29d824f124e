import { styled, Switch } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { isEqual } from 'lodash';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user';

type FieldsToUpdate = {
    appointmentsNotificationsEnabled: boolean;
    massSendingEnabled: boolean;
};

const selectData = createSelector(
    [selectOrderData, selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (s, perm, loading, orderIsClosed) => ({
        value: {
            appointmentsNotificationsEnabled: s.customer.appointmentsNotificationsEnabled,
            massSendingEnabled: s.customer.massSendingEnabled,
        },
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
    })
);

export default function CustomerNotifications() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    const { canEdit, value: originalValue } = useAppSelector(selectData, isEqual);

    const mutation = useMutation(async (values: Partial<FieldsToUpdate>) => {
        dispatch(orderActions.update({ customer: { ...values } }));
        return await dispatch(saveOrderThunk());
    });

    return (
        <>
            <SwitchSetting>
                <SSwitch
                    checked={originalValue.appointmentsNotificationsEnabled}
                    onChange={() =>
                        mutation.mutate({
                            appointmentsNotificationsEnabled:
                                !originalValue.appointmentsNotificationsEnabled,
                        })
                    }
                    name="ApptNotificationsSwitch"
                    disabled={!canEdit}
                />
                <SwitchTextBox>
                    <SwitchTitleBox>
                        {t('orderDetails.customerNotifications.appointmentsNotificationsTitle')}
                    </SwitchTitleBox>
                    <SwitchDescriptionBox>
                        {t(
                            'orderDetails.customerNotifications.appointmentsNotificationsDescription'
                        )}
                    </SwitchDescriptionBox>
                </SwitchTextBox>
            </SwitchSetting>
            <SwitchSetting>
                <SSwitch
                    checked={originalValue.massSendingEnabled}
                    onChange={() =>
                        mutation.mutate({
                            massSendingEnabled: !originalValue.massSendingEnabled,
                        })
                    }
                    name="MassSendingSwitch"
                    disabled={!canEdit}
                />
                <SwitchTextBox>
                    <SwitchTitleBox>
                        {t('orderDetails.customerNotifications.massSendingsTitle')}
                    </SwitchTitleBox>
                    <SwitchDescriptionBox>
                        {t('orderDetails.customerNotifications.massSendingsDescription')}
                    </SwitchDescriptionBox>
                </SwitchTextBox>
            </SwitchSetting>
        </>
    );
}

const SwitchSetting = styled('div')({
    display: 'flex',
});

const SSwitch = styled(Switch)(({ theme }) => ({
    '& .MuiSwitch-switchBase': {
        '&.Mui-checked': {
            color: theme.palette.neutral[1],
            '& .MuiSwitch-thumb:before': {
                color: theme.palette.neutral[1],
            },
            '& + .MuiSwitch-track': {
                opacity: 1,
            },
        },
        '& .MuiSwitch-thumb:before': {
            color: theme.palette.neutral[1],
        },
    },
}));

const SwitchTextBox = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-around',
});

const SwitchTitleBox = styled('div')(({ theme }) => ({
    ...theme.typography.h6,
    color: theme.palette.neutral[7],
}));

const SwitchDescriptionBox = styled(SwitchTitleBox)(({ theme }) => ({
    fontWeight: 'normal',
}));
