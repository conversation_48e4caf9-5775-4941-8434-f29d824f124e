import { useQuery } from '@tanstack/react-query';
import SignaturesApi from 'api/Signatures';
import EnterpriseSignaturesApi from 'api/enterprise/signatures';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import {
    selectIsOrderClosed,
    selectOrderId,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import SignatureField from '../SignatureField';

const selectData = createSelector(
    [
        selectUserPermission,
        selectOrderId,
        selectOrderShopKey,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (perm, orderId, shopKey, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        shopKey,
        orderId,
    })
);

export default function CustomerSignature() {
    const { t } = useAppTranslation();
    const isEnterprise = useIsEnterpriseRoute();
    const { canEdit, shopKey, orderId } = useAppSelector(selectData);

    const queryKey = isEnterprise
        ? ['enterprise', 'order', orderId!, 'customers-signature-url']
        : ['order', orderId!, 'customers-signature-url'];
    const queryFn = isEnterprise
        ? () => EnterpriseSignaturesApi.getCustomerSignatures(orderId!, shopKey!)
        : () => SignaturesApi.getCustomerSignatures(orderId!);

    const { data } = useQuery(queryKey, queryFn);

    return (
        <>
            <SignatureField
                url={data ? data.receptionSignatureUrl : null}
                signedAt={data?.receptionSignatureDate}
                label={
                    data?.receptionHeaderText
                        ? data?.receptionHeaderText
                        : t('orderDetails.customerSignatureAtReception')
                }
                disabled={!canEdit}
            />

            <SignatureField
                url={data ? data.deliverySignatureUrl : null}
                signedAt={data?.deliverySignatureDate}
                label={
                    data?.deliveryHeaderText
                        ? data?.deliveryHeaderText
                        : t('orderDetails.customerSignatureAtDelivery')
                }
                disabled={!canEdit}
            />

            <SignatureField
                url={data ? data.adhesionSignatureUrl : null}
                signedAt={data?.adhesionSignatureDate}
                label={
                    data?.adhesionSignatureHeaderText
                        ? data?.adhesionSignatureHeaderText
                        : t('orderDetails.customerSignatureAdhesion')
                }
                disabled={!canEdit}
            />

            <SignatureField
                url={data ? data.privacyNoticeSignatureUrl : null}
                signedAt={data?.privacyNoticeSignatureDate}
                label={
                    data?.privacyNoticeHeaderText
                        ? data?.privacyNoticeHeaderText
                        : t('orderDetails.customerSignaturePrivacyNotice')
                }
                disabled={!canEdit}
            />
        </>
    );
}
