import { Grid } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { KeyboardEventHandler, useEffect, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopId,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import IdentificationDocumentTypePicker from 'views/OrderDetail/IdentificationDocumentTypePicker';
import { OrderDetailsResettableTextFormField } from '../OrderDetailsResettableTextFormField';
import { useThirdPartyOrderInteraction } from '../hooks/useThirdPartyOrderInteraction';

type IdentificationDocumentFieldProps = {
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

const selectData = createSelector(
    [
        selectOrderData,
        selectUserPermission,
        selectOrderShopId,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (s, perm, shopId, loading, orderIsClosed) => ({
        documentId: s.customer.documentId,
        documentValue: s.customer.documentValue,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        shopId,
    })
);

export default function IdentificationDocumentField({
    id,
    isSelected,
    onKeyDown,
    disabled,
}: IdentificationDocumentFieldProps) {
    const ref = useRef<HTMLDivElement>(null);
    const [selectedSecondElement, setSelectedSecondElement] = useState(false);
    const { t } = useAppTranslation();
    const { documentId, documentValue, canEdit, shopId } = useAppSelector(selectData, isEqual);
    const dispatch = useAppDispatch();
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const updateDocumentId = useMutation(
        async (documentId: number | null) => {
            dispatch(orderActions.update({ customer: { documentId } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const mutationDocumentValue = useMutation(
        async (value: string) => {
            dispatch(orderActions.update({ customer: { documentValue: value } }));
            return await dispatch(saveOrderThunk());
        },
        {
            onSuccess: ({ payload }) => {
                showOrderUpdateNotification(payload as SaveOrderResult);
            },
        }
    );

    const updateDocumentValue = async (value: string) => {
        await mutationDocumentValue.mutateAsync(value);
    };

    useEffect(() => {
        if (selectedSecondElement) {
            ref.current?.focus();
        }
    }, [selectedSecondElement]);

    return (
        <Grid container spacing={1} alignItems="flex-end">
            <Grid item xs={6} marginTop={'-5px'}>
                <IdentificationDocumentTypePicker
                    id={id}
                    repairShopId={shopId}
                    disabled={!canEdit || disabled}
                    onNewDocumentType={(type) => {
                        updateDocumentId.mutate(type?.id ?? null);
                    }}
                    documentTypeId={documentId}
                    isSelected={isSelected}
                    onKeyDown={() => setSelectedSecondElement(true)}
                />
            </Grid>
            <Grid item xs={6} marginTop={'-5px'}>
                <OrderDetailsResettableTextFormField
                    inputRef={ref}
                    name="identDoc"
                    value={documentValue}
                    disabled={!canEdit || disabled}
                    onSave={(value) => updateDocumentValue(value)}
                    placeholder={t('orderDetails.identNum')}
                    size="small"
                    onKeyDown={(e) => {
                        onKeyDown(e);
                        setSelectedSecondElement(false);
                    }}
                    onBlur={() => {
                        setSelectedSecondElement(false);
                    }}
                />
            </Grid>
        </Grid>
    );
}
