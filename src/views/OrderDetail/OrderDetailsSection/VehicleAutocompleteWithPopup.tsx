import CustomersApi, { VehicleDetailsDto, VehicleListItemDto } from 'api/customers';
import { EnterpriseCustomersApi } from 'api/enterprise';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { useCallback, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    saveOrderVehicleThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import CreateNewVehiclePopup from 'views/Components/CreateNewVehiclePopup';
import CustomerVehiclesAutocompleteCustomApi from 'views/Components/CustomerVehiclesAutocomplete/CustomerVehiclesAutocompleteCustomApi';

const selectData = createSelector(
    [
        selectOrderShopKey,
        selectUserPermission,
        selectOrderData,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (shopKey, perm, s, loading, orderIsClosed) => ({
        shopKey,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        customerId: s.customer.id,
        vehicleId: s.vehicle.id,
        vehicleDetails: s.vehicleDetails,
    })
);

export default function VehicleAutocompleteWithPopup() {
    const isEnterprise = useIsEnterpriseRoute();
    const dispatch = useAppDispatch();
    const { shopKey, canEdit, customerId, vehicleId, vehicleDetails } = useAppSelector(selectData);
    if (isEnterprise && !shopKey) throw new Error('shopKey is required in enterprise mode');

    const initialValue: VehicleListItemDto | null = vehicleDetails;

    const [createVehiclePopupOpen, setCreateVehiclePopupOpen] = useState(false);

    const onVehicleSelected = useCallback(
        (item: VehicleListItemDto | VehicleDetailsDto | undefined) => {
            dispatch(
                saveOrderVehicleThunk(
                    item
                        ? {
                              id: item.id,
                              plates: item.plates,
                              vin: item.vin,
                              model: item.model,
                              brand: item.brand,
                              year: item.year,
                              color: item.color,
                          }
                        : undefined
                )
            );
        },
        [dispatch]
    );

    const onVehicleAutoSelected = useCallback(
        (item: VehicleListItemDto) => {
            dispatch(saveOrderVehicleThunk(item));
        },
        [dispatch]
    );

    return (
        <>
            <CustomerVehiclesAutocompleteCustomApi
                cacheKey={isEnterprise ? shopKey! : 'rs'}
                value={vehicleId}
                initialValue={initialValue}
                disabled={!canEdit}
                customerId={customerId}
                onSelect={onVehicleSelected}
                onAutoSelect={onVehicleAutoSelected}
                onOpenPopup={() => setCreateVehiclePopupOpen(true)}
                getVehicles={
                    isEnterprise
                        ? (customerId) => EnterpriseCustomersApi.getVehicles(shopKey!, customerId)
                        : CustomersApi.getCustomerVehicles
                }
            />
            <CreateNewVehiclePopup
                open={createVehiclePopupOpen}
                customerId={customerId}
                repairShopKey={shopKey}
                onVehicleCreated={onVehicleSelected}
                onClose={() => setCreateVehiclePopupOpen(false)}
            />
        </>
    );
}
