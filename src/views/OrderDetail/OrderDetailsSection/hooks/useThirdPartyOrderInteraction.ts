import { WpOrderUpdated } from 'api/workshopPlanner/orders';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { SaveOrderResult } from 'store/slices/order/orderDetails';

const selectShopIntegratedOrderUpdate = createSelector([selectSettings], (settings) => ({
    updateOrderInfo3rdParty: settings.repairShopSettings?.features.updateOrderInfo3rdParty ?? false,
    integrationAccountName: settings.repairShopSettings?.integrationAccountName,
}));

type ThirdPartyOrderInteraction = {
    showOrderUpdateNotification: (
        saveResult:
            | SaveOrderResult
            | { orderInfo?: WpOrderUpdated; thirdPartyUpdateErrors: string[] }
    ) => void;
};

export const useThirdPartyOrderInteraction = (): ThirdPartyOrderInteraction => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const { updateOrderInfo3rdParty, integrationAccountName } = useAppSelector(
        selectShopIntegratedOrderUpdate
    );

    const showOrderUpdateNotification = (
        saveResult:
            | SaveOrderResult
            | { orderInfo?: WpOrderUpdated; thirdPartyUpdateErrors: string[] }
    ) => {
        if (updateOrderInfo3rdParty && saveResult && 'thirdPartyUpdateErrors' in saveResult) {
            if (saveResult.thirdPartyUpdateErrors!.length > 0) {
                toasters.danger(
                    t('orderDetails.thirdParty.updateOrderInfo.error.description', {
                        errorMessage: [...new Set(saveResult.thirdPartyUpdateErrors)].join(', '),
                    }),
                    t('orderDetails.thirdParty.updateOrderInfo.error.title', {
                        accountIntegratedName: integrationAccountName,
                    })
                );
            } else {
                toasters.success(
                    t('orderDetails.thirdParty.updateOrderInfo.success.description', {
                        accountIntegratedName: integrationAccountName,
                    }),
                    t('orderDetails.thirdParty.updateOrderInfo.success.title')
                );
            }
        }
    };

    return { showOrderUpdateNotification };
};
