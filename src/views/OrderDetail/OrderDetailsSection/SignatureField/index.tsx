import { CheckCircleRounded } from '@mui/icons-material';
import { styled } from '@mui/material';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useAppSelector } from '../../../../store';
import { selectSettings } from '../../../../store/slices/globalSettingsSlice';

type SignatureFieldProps = {
    label: string;
    url: string | null;
    signedAt?: string | null;
    signature?: string;
    disabled?: boolean;
};
export default function SignatureField({
    label,
    url,
    signedAt,
    signature,
    disabled,
}: SignatureFieldProps) {
    const { t } = useAppTranslation();
    const language = useAppSelector((r) => selectSettings(r).internationalization.language);

    const formatDateWithLowerCaseMonth = (date: string) => {
        const dateFormat =
            language === 'en' ? t('dateFormats.longTimeAgo') : 'D [de] MMMM, YYYY hh:mma';

        const formattedDate = moment(date).format(dateFormat);

        if (language !== 'en') {
            return formattedDate.replace(/(\b\p{Lu}\p{Ll}*\b)/u, (match) => match.toLowerCase());
        }

        return formattedDate;
    };

    return (
        <DivRoot>
            <StyledInputLabel disabled={!!disabled}>{label}</StyledInputLabel>
            <DivFieldContainer disabled={!!disabled}>
                {url && <ImageField alt="signature" src={url} />}
            </DivFieldContainer>
            {signature && (
                <>
                    <StyledInputLabel disabled={!!disabled}>
                        {t('orderDetails.signatureNameAndJobTitle')}
                    </StyledInputLabel>
                    <DivSignatureField>{signature}</DivSignatureField>
                </>
            )}
            {signedAt && (
                <DivSignedAtContainer>
                    <SpanSignedAt>{formatDateWithLowerCaseMonth(signedAt)}</SpanSignedAt>
                    <StyledCheckCircleRounded />
                </DivSignedAtContainer>
            )}
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    overflow: 'hidden',
    marginTop: -5,
});

const DivSignatureField = styled('div')(({ theme }) => ({
    border: `solid 1px ${theme.palette.neutral[4]}`,
    borderRadius: 5,
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[9],
    fontWeight: 'normal',
    padding: '8px 10px',
    backgroundColor: theme.palette.neutral[2],
    minHeight: 33,
}));

const DivFieldContainer = styled('div', {
    shouldForwardProp: (prop) => !['disabled'].includes(prop as string),
})<{ disabled: boolean }>(({ theme, disabled }) => ({
    position: 'relative',
    border: `solid 1px ${theme.palette.neutral[4]}`,
    borderRadius: 5,
    textAlign: 'center',
    padding: 5,
    height: 94,
    background: theme.palette.neutral[2],

    ...(disabled
        ? {
              pointerEvents: 'none',
              opacity: 0.5,
          }
        : undefined),
}));

const StyledInputLabel = styled(InputLabel, {
    shouldForwardProp: (prop) => !['disabled'].includes(prop as string),
})<{ disabled: boolean }>(({ disabled }) => ({
    ...(disabled
        ? {
              pointerEvents: 'none',
              opacity: 0.5,
          }
        : undefined),
}));

const ImageField = styled('img')({
    height: '100%',
    borderRadius: 5,
});

const DivSignedAtContainer = styled('div')({
    display: 'flex',
    marginTop: '7px',
});

const SpanSignedAt = styled('span')(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[6],
}));

const StyledCheckCircleRounded = styled(CheckCircleRounded)(({ theme }) => ({
    color: theme.palette.primary.main,
    fontSize: '14.41px',
    marginLeft: '5px',
}));
