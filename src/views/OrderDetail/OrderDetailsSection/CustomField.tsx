import { CircularProgress, styled } from '@mui/material';
import { CustomizableFieldValue, FieldType } from 'api/fields';
import { TimeSpan } from 'api/utils/format';
import { $Icon } from 'common/components/Icons/$Icon';
import { IconProps } from 'common/components/Icons/Icon';
import DateField from 'common/components/Inputs/DateField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import NumberInput from 'common/components/Inputs/NumberField/NumberInput';
import ResettableTextInput from 'common/components/Inputs/ResettableTextField/ResettableTextInput';
import { TimeField } from 'common/components/Inputs/TimeField';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { OptionStyle } from 'common/styles/OptionStyle';
import debounce from 'lodash/debounce';
import { DateTime } from 'luxon';
import React, {
    ComponentType,
    KeyboardEventHandler,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    orderActions,
    SaveOrderResult,
    saveOrderThunk,
    selectIsOrderClosed,
    selectOrderFieldValue,
    selectOrderLoading,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import { isDateValid } from 'utils';
import ErrorBoundary from 'utils/errorsHandling/ErrorBoundary';
import { useThirdPartyOrderInteraction } from './hooks/useThirdPartyOrderInteraction';

export type CustomFieldProps = {
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    field: CustomizableFieldValue;
    disabled: boolean;
};

const selectData = createSelector(
    [selectUserPermission, selectOrderLoading, selectIsOrderClosed],
    (perm, loading, orderIsClosed) => ({
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
    })
);

export default function CustomField({ field, isSelected, onKeyDown, disabled }: CustomFieldProps) {
    const [saving, setSaving] = useState(false);
    const { canEdit } = useAppSelector(selectData);
    const value = useAppSelector((r) => selectOrderFieldValue(r, field.id));
    const dispatch = useAppDispatch();
    const Component = INPUT_COMPONENTS[field.type];
    const { showOrderUpdateNotification } = useThirdPartyOrderInteraction();

    const onValueChange = useCallback(
        (value: string) => {
            dispatch(orderActions.setOrderFieldValue({ id: field.id, value }));
        },
        [dispatch, field.id]
    );

    const onSave = useCallback(() => {
        setSaving(true);
        dispatch(saveOrderThunk())
            .then((result) => {
                showOrderUpdateNotification(result.payload as SaveOrderResult);
            })
            .finally(() => setSaving(false));
    }, [dispatch, showOrderUpdateNotification]);

    return (
        <InputWrapper
            disabled={!canEdit || disabled}
            label={field.name}
            rightAdornment={
                saving && (
                    <CircularProgress
                        sx={{ alignSelf: 'start', position: 'relative', top: 8 }}
                        size={16}
                        thickness={4}
                    />
                )
            }
        >
            <FieldErrorBoundary>
                {Component && (
                    <Component
                        id={field.id}
                        showLoader={saving}
                        value={value}
                        placeholder={field.name}
                        onChange={onValueChange}
                        onSaveRequested={onSave}
                        disabled={!canEdit || disabled}
                        isSelected={isSelected}
                        onKeyDown={onKeyDown}
                        field={field}
                        isSaving={saving}
                    />
                )}
            </FieldErrorBoundary>
        </InputWrapper>
    );
}

type CustomInputProps = {
    id: string;
    value: string;
    onChange: (value: string) => void;
    onSaveRequested: () => void;
    placeholder: string;
    showLoader: boolean;
    disabled: boolean;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    field: CustomizableFieldValue;
    isSaving: boolean;
};

function ShortTextInput(props: CustomInputProps) {
    return <FreeTextInput multiline={false} maxLength={500} {...props} />;
}

function LongTextInput(props: CustomInputProps) {
    return <FreeTextInput multiline maxLength={1000} {...props} />;
}

function FreeTextInput({
    id,
    value,
    onChange,
    onSaveRequested,
    placeholder,
    disabled,
    onKeyDown,
    isSelected,
    multiline,
    maxLength,
}: CustomInputProps & { multiline: boolean; maxLength: number }) {
    const ref = useRef<HTMLDivElement>(null);
    const dispatch = useAppDispatch();
    const updateValue = (value: string) => {
        value = value.trim();
        onChange(value);
        onSaveRequested();
    };

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <ResettableTextInput
            cmosVariant="grey"
            inputRef={ref}
            value={value}
            disabled={disabled}
            onSave={async (value) => updateValue(value)}
            placeholder={placeholder}
            maxLength={maxLength}
            size="small"
            onKeyDown={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
            multiline={multiline}
        />
    );
}

module numericInput {
    type NumericInputProps = CustomInputProps & {
        precision?: number;
        icon?: ComponentType<IconProps>;
    };

    export function NumericInput({
        id,
        value,
        onChange,
        onSaveRequested,
        placeholder,
        precision = 6,
        icon: Icon,
        showLoader,
        disabled,
        onKeyDown,
        isSelected,
    }: NumericInputProps) {
        const ref = useRef<HTMLDivElement>(null);
        const dispatch = useAppDispatch();
        const [internalValue, setInternalValue] = useState<number | null>(null);
        const [focus, setFocus] = useState(false);

        const parsedValue = useMemo(() => parseStringValue(value, precision), [value, precision]);

        useEffect(() => {
            if (focus) {
                setInternalValue(parsedValue);
            }
        }, [parsedValue, focus]);

        useEffect(() => {
            if (isSelected) {
                ref.current?.focus();
            }
        }, [isSelected]);

        return (
            <NumberInput
                fullWidth
                inputRef={ref}
                placeholder={placeholder}
                InputProps={{
                    endAdornment: Icon && <Icon />,
                }}
                showLoader={showLoader}
                cmosVariant="grey"
                value={focus ? internalValue : parsedValue}
                onBlur={() => {
                    setFocus(false);
                    onChange(internalValue === null ? '' : internalValue.toString());
                    onSaveRequested();
                    dispatch(orderActions.clearSelectedField({ id }));
                }}
                onFocus={() => setFocus(true)}
                onValueChange={(v) => setInternalValue(v.floatValue ?? null)}
                disabled={disabled}
                onKeyDown={onKeyDown}
            />
        );
    }

    function parseStringValue(value: string, precision: number): number | null {
        value = value.trim();
        if (value === null) return null;

        let num = Number.parseFloat(value);
        if (!Number.isNaN(num)) {
            return roundNum(num, precision);
        }

        value = value.replaceAll(/[^\d]/g, '');
        if (value === '') return null;
        num = Number.parseFloat(value);
        return Number.isNaN(num) ? null : roundNum(num, precision);
    }

    function roundNum(num: number, precision: number): number {
        const d = 10 ** precision;
        return Math.floor(num * d) / d;
    }
}

module currencyInput {
    const selectCurrencySymbol = createSelector(selectSettings, (s) =>
        s.internationalization.currency.replace('{0}', '').trim()
    );

    const CustomCurrency = styled('div')({
        fontWeight: 'bold',
        color: 'var(--neutral7)',
        display: 'flex',
        justifyContent: 'center',
        verticalAlign: 'middle',
        lineHeight: '24px',
        height: 24,
        width: 24,
    });

    function normalizeSymbol(symbol: string): string {
        if (symbol === 'USD') return '$';

        return symbol;
    }

    function Icon() {
        const symbol: string = normalizeSymbol(useAppSelector(selectCurrencySymbol));

        const scale = 1.5 / Math.sqrt(Math.max(1, symbol.length));

        if (symbol !== '$')
            return (
                <CustomCurrency style={{ transform: `scale(${scale})` }}>{symbol}</CustomCurrency>
            );

        // eslint-disable-next-line react/jsx-pascal-case
        return <$Icon fill={Colors.Neutral7} />;
    }

    export function CurrencyInput({
        id,
        onKeyDown,
        isSelected,
        disabled,
        ...props
    }: CustomInputProps) {
        return (
            <numericInput.NumericInput
                {...props}
                id={id}
                precision={2}
                icon={Icon}
                onKeyDown={onKeyDown}
                isSelected={isSelected}
                disabled={disabled}
            />
        );
    }
}

function DateInput({
    id,
    value,
    onChange,
    onSaveRequested,
    disabled,
    onKeyDown,
    isSelected,
}: CustomInputProps) {
    const ref = useRef<HTMLInputElement>(null);
    const dispatch = useAppDispatch();
    const date = useMemo(() => {
        try {
            return DateTime.fromFormat(value, 'yyyy-MM-dd').set({
                millisecond: 0,
                second: 0,
                minute: 0,
                hour: 0,
            });
        } catch (_err: unknown) {
            return null;
        }
    }, [value]);
    const jsDate = date?.toJSDate() ?? null;

    const onDateChange = useCallback(
        (d: Date | null) => {
            if (!d) d = new Date();
            if (!isDateValid(d)) return false;

            const stringValue = DateTime.fromJSDate(d).toFormat('yyyy-MM-dd');
            onChange(stringValue);
            onSaveRequested();
        },
        [onChange, onSaveRequested]
    );

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <DateField
            inputRef={ref}
            disabled={disabled}
            variant="grey"
            fullWidth
            value={jsDate}
            onChange={onDateChange}
            onEnterPress={onKeyDown}
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
        />
    );
}

module timeInput {
    export function TimeInput({
        id,
        value,
        onChange,
        onSaveRequested,
        disabled,
        onKeyDown,
        isSelected,
    }: CustomInputProps) {
        const ref = useRef<HTMLInputElement>(null);
        const dispatch = useAppDispatch();
        const parsedValue = useMemo(() => parseTimeValue(value), [value]);

        const onValueChange = useCallback(
            ([h, m]: [number, number]) => {
                onChange(TimeSpan.fromParts(h, m).toString());
                onSaveRequested();
            },
            [onChange, onSaveRequested]
        );

        useEffect(() => {
            if (isSelected) {
                (ref.current as any).inputElement.focus();
            }
        }, [isSelected]);

        return (
            <TimeField
                ref={ref}
                cmosVariant="grey"
                disabled={disabled}
                disableInput={disabled}
                fullWidth
                value={parsedValue}
                onChange={onValueChange}
                onKeyDown={onKeyDown}
                onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
            />
        );
    }

    function parseTimeValue(value: string): [number, number] | null {
        value = value.trim();
        if (value === '') return null;

        if (/^\d{2}:\d{2}(:\d{2})?$/.test(value)) {
            const parts = value.split(':').map(Number);
            const h = Math.min(23, parts[0]);
            const m = Math.min(59, parts[1]);
            return [h, m];
        }

        return null;
    }
}

type DropDownOption = { name: string };

function SelectInput(props: CustomInputProps) {
    return <InternalMultiSelectInput isMulti={false} {...props} />;
}

type MultiSelectFieldValue = {
    value: DropDownOption[];
    text?: string | null;
};

function MultiSelectInput(props: CustomInputProps) {
    return <InternalMultiSelectInput isMulti {...props} />;
}

function InternalMultiSelectInput({
    id,
    value: valueProp,
    onChange,
    onSaveRequested,
    disabled,
    onKeyDown,
    isSelected,
    field,
    isMulti,
    isSaving,
}: CustomInputProps & { isMulti: boolean }) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLSelectElement>(null);

    const parsedValue: MultiSelectFieldValue = useMemo(() => {
        try {
            const { v: _, ...value } = valueProp
                ? (JSON.parse(valueProp) as MultiSelectFieldValue & { v?: unknown })
                : { v: 0, value: [] };
            return value;
        } catch {
            return { value: [] };
        }
    }, [valueProp]);

    const hasValueInExtraText = useRef(!!parsedValue.text);
    const hasExtraText = hasValueInExtraText.current || field.hasExtraText === true;

    // calculate dropdown options
    const options = useMemo(() => {
        const settingsOptions = (field.options ?? []).map((o) => ({
            label: o.name,
            value: o.name,
        }));

        //Merge options taken from settings with options stored as value for a specific order field to add options that are deleted in settings.
        const mergedOptions = parsedValue.value.reduce((accumulator, orderItem) => {
            if (!accumulator.some((settingsItem) => settingsItem.value === orderItem.name)) {
                accumulator.push({ value: orderItem.name, label: orderItem.name });
            }
            return accumulator;
        }, settingsOptions);

        return mergedOptions.sort((l, r) => {
            return l.label.localeCompare(r.label);
        });
    }, [field, parsedValue.value]);

    const selectedValues = useMemo(() => parsedValue.value.map((x) => x.name), [parsedValue.value]);

    const selectedOptions = useMemo(() => {
        return options.filter((o) => selectedValues.includes(o.value));
    }, [options, selectedValues]);

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    const propsRef = useRef({ onSaveRequested });
    propsRef.current.onSaveRequested = onSaveRequested;
    const debouncedSave = useMemo(
        () => debounce(() => propsRef.current.onSaveRequested(), 300),
        []
    );

    const handleSelectChange = (
        valuesArg: ReadonlyArray<OptionData<string>> | OptionData<string> | null
    ) => {
        let value: MultiSelectFieldValue;
        if (valuesArg instanceof Array) {
            if (!isMulti) throw new Error('unreachable code');
            value = {
                value: valuesArg.map((x) => ({ name: x.value })),
                text: parsedValue.text || undefined,
            };
        } else {
            if (isMulti) throw new Error('unreachable code');
            value = {
                value: valuesArg ? [{ name: valuesArg.value }] : [],
                text: parsedValue.text || undefined,
            };
        }

        onChange(JSON.stringify(value));
        debouncedSave();
    };

    const lastTextValue = useRef(parsedValue.text);

    const handleExtraTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value: MultiSelectFieldValue = {
            value: parsedValue.value,
            text: e.target.value,
        };
        onChange(JSON.stringify(value));
    };

    const handleExtraTextBlur = () => {
        if (lastTextValue.current !== parsedValue.text) {
            lastTextValue.current = parsedValue.text;
            onSaveRequested();
        }
    };

    return (
        <MultiSelectContainer>
            <Dropdown
                multiple={isMulti}
                showValueCounterAfter={3}
                ref={ref}
                cmosVariant="grey"
                disabled={disabled}
                placeholder={
                    t('customizableFields.placeholders.select') + ' ' + field.name.toLowerCase()
                }
                options={options}
                value={selectedOptions}
                onChange={handleSelectChange}
                styles={{
                    indicatorsContainer: isSaving ? { visibility: 'hidden' } : undefined,
                }}
                optionStyle={OptionStyle.checkbox}
            />
            {hasExtraText && (
                <TextField
                    cmosVariant="grey"
                    value={parsedValue.text ?? ''}
                    onBlur={handleExtraTextBlur}
                    onChange={handleExtraTextChange}
                    disabled={disabled}
                    placeholder={field.name}
                />
            )}
        </MultiSelectContainer>
    );
}

const MultiSelectContainer = styled('div')({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 6,
});

const INPUT_COMPONENTS: Partial<Record<FieldType, ComponentType<CustomInputProps>>> = {
    ShortText: ShortTextInput,
    LongText: LongTextInput,
    Date: DateInput,
    Numeric: numericInput.NumericInput,
    Currency: currencyInput.CurrencyInput,
    Time: timeInput.TimeInput,
    Select: SelectInput,
    MultiSelect: MultiSelectInput,
};

function FieldErrorBoundary({ children }: React.PropsWithChildren<{}>) {
    return (
        <ErrorBoundary
            renderError={({ error }) => {
                let errorMessage: string = 'unknown';
                if (error instanceof Error) {
                    errorMessage = error.message;
                } else if (typeof error === 'string') {
                    errorMessage = error;
                }
                console.error(error);
                return (
                    <ErrorMessage>
                        <small>
                            <strong>CUSTOM FIELD ERROR</strong>
                        </small>
                        <br />
                        {errorMessage}
                    </ErrorMessage>
                );
            }}
        >
            {children}
        </ErrorBoundary>
    );
}

const ErrorMessage = styled('div')({
    border: '1px solid var(--danger)',
    padding: '8px',
    // maxWidth: '300px',
    width: '100%',
});
