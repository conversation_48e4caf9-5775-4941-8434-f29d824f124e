import { CustomerSearchItemDto } from 'api/Clients/Customers';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { useCallback, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import {
    saveOrderCustomerAndVehicleThunk,
    saveOrderCustomerThunk,
    selectIsOrderClosed,
    selectOrderData,
    selectOrderLoading,
    selectOrderShopKey,
} from 'store/slices/order/orderDetails';
import { selectUserPermission } from 'store/slices/user/selectors';
import CreateNewCustomerPopup from 'views/Components/CreateNewCustomerPopup';
import { CustomerCreatedEvent } from 'views/Components/CreateNewCustomerPopup/CreateNewCustomerPopupCustomApi';
import CustomerAutocomplete from 'views/Components/CustomerAutocomplete';
import { useVehiclesQueryModifiers } from 'views/Components/CustomerVehiclesAutocomplete/CustomerVehiclesAutocompleteCustomApi';

const selectData = createSelector(
    [
        selectOrderShopKey,
        selectUserPermission,
        selectOrderData,
        selectOrderLoading,
        selectIsOrderClosed,
    ],
    (shopKey, perm, s, loading, orderIsClosed) => ({
        shopKey,
        canEdit: perm.allowEditOrders && !loading && !orderIsClosed,
        customerId: s.customer.id,
        initiallySelectedCustomer: s.customerDetails,
        appointmentsNotificationsEnabled: s.customer.appointmentsNotificationsEnabled,
        massSendingEnabled: s.customer.massSendingEnabled,
    })
);

export default function CustomerAutocompleteWithPopup() {
    const isEnterprise = useIsEnterpriseRoute();
    const dispatch = useAppDispatch();
    const {
        shopKey,
        canEdit,
        customerId,
        initiallySelectedCustomer,
        appointmentsNotificationsEnabled,
        massSendingEnabled,
    } = useAppSelector(selectData);

    const initiallySelectedSearchItem: CustomerSearchItemDto | null = useMemo(
        () =>
            initiallySelectedCustomer
                ? {
                      id: initiallySelectedCustomer.customerId,
                      firstName: initiallySelectedCustomer.firstName,
                      lastName: initiallySelectedCustomer.lastName,
                      mobile: initiallySelectedCustomer.mobile,
                      taxIdentification: initiallySelectedCustomer.taxIdentification,
                      email: initiallySelectedCustomer.email,
                      businessName: initiallySelectedCustomer.businessName,
                      appointmentsNotificationsEnabled,
                      massSendingEnabled,
                      vehiclePlates: initiallySelectedCustomer.linkedVehicles.map((x) => x.plates),
                  }
                : null,
        [appointmentsNotificationsEnabled, initiallySelectedCustomer, massSendingEnabled]
    );

    if (isEnterprise && !shopKey) throw new Error('shopKey is required in enterprise mode');

    const [createCustomerPopupOpen, setCreateCustomerPopupOpen] = useState(false);

    const { add: addVehicleToVehiclesQuery } = useVehiclesQueryModifiers();

    const onCustomerCreated = useCallback(
        (event: CustomerCreatedEvent) => {
            if (event.vehicle) {
                addVehicleToVehiclesQuery(event.customer.id, event.vehicle);
                dispatch(saveOrderCustomerAndVehicleThunk([event.customer, event.vehicle]));
            } else {
                dispatch(saveOrderCustomerThunk(event.customer));
            }
        },
        [dispatch, addVehicleToVehiclesQuery]
    );

    const onCustomerSelected = useCallback(
        (item: CustomerSearchItemDto) => {
            dispatch(saveOrderCustomerThunk(item));
        },
        [dispatch]
    );

    return (
        <>
            <CustomerAutocomplete
                value={customerId}
                initiallySelected={initiallySelectedSearchItem}
                disabled={!canEdit}
                onSelect={onCustomerSelected}
                onOpenCreatePopup={() => setCreateCustomerPopupOpen(true)}
            />
            <CreateNewCustomerPopup
                open={createCustomerPopupOpen}
                repairShopKey={shopKey}
                onCustomerAndVehicleCreated={onCustomerCreated}
                onClose={() => setCreateCustomerPopupOpen(false)}
            />
        </>
    );
}
