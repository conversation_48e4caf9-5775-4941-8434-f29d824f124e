import {
    CustomizableFieldValue,
    FieldValueWithName,
    isPredefinedField,
    PREDEFINED_FIELDS,
} from 'api/fields';
import { handleCommonKeyDowns } from 'common/components/Inputs/utils';
import { FunctionComponent, KeyboardEventHandler, useCallback, useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { orderActions, selectedItem } from 'store/slices/order/orderDetails';
import { useOrderDetailsValidationsContext } from '../providers';
import CustomField from './CustomField';
import BusinessNameField from './CustomerInfoSection/BusinessNameField';
import CustomerNameField from './CustomerInfoSection/CustomerNameField';
import EmailField from './CustomerInfoSection/EmailField';
import IdentificationDocumentField from './CustomerInfoSection/IdentificationDocumentField';
import LandlineField from './CustomerInfoSection/LandlineField';
import MobileField from './CustomerInfoSection/MobileField';
import PaymentMethodField from './CustomerInfoSection/PaymentMethodField';
import { AdditionalPositionField } from './OrderInfoSection/AdditionalPositionField';
import AssignedToField from './OrderInfoSection/AssignedToField';
import DeliveryDateTimePicker from './OrderInfoSection/DeliveryDateTimePicker';
import InChargeField from './OrderInfoSection/InChargeField';
import OrderNotes from './OrderInfoSection/OrderNotes';
import OrderTypeField from './OrderInfoSection/OrderTypeField';
import PhaseField from './OrderInfoSection/PhaseField';
import TechnicianField from './OrderInfoSection/TechnicianField';
import TowerField from './OrderInfoSection/TowerField';
import BrandField from './VehicleInfoSection/BrandField';
import MileageInput from './VehicleInfoSection/MileageInput';
import ModelField from './VehicleInfoSection/ModelField';
import PlatesField from './VehicleInfoSection/PlatesField';
import VinField from './VehicleInfoSection/VinField';
import YearField from './VehicleInfoSection/YearField';

const PREDEFINED_FIELDS_COMPONENTS = {
    [PREDEFINED_FIELDS.CUSTOMER_NAME]: CustomerNameField,
    [PREDEFINED_FIELDS.CUSTOMER_EMAIL]: EmailField,
    [PREDEFINED_FIELDS.CUSTOMER_LANDLINE]: LandlineField,
    [PREDEFINED_FIELDS.CUSTOMER_MOBILE]: MobileField,
    [PREDEFINED_FIELDS.CUSTOMER_BUSINESS_NAME]: BusinessNameField,
    [PREDEFINED_FIELDS.CUSTOMER_ID_DOCUMENT]: IdentificationDocumentField,
    [PREDEFINED_FIELDS.CUSTOMER_PAYMENT_METHOD]: PaymentMethodField,

    [PREDEFINED_FIELDS.VEHICLE_BRAND]: BrandField,
    [PREDEFINED_FIELDS.VEHICLE_MODEL]: ModelField,
    [PREDEFINED_FIELDS.VEHICLE_YEAR]: YearField,
    [PREDEFINED_FIELDS.VEHICLE_PLATES]: PlatesField,
    [PREDEFINED_FIELDS.VEHICLE_VIN]: VinField,
    [PREDEFINED_FIELDS.VEHICLE_MILEAGE]: MileageInput,

    [PREDEFINED_FIELDS.ORDER_PHASE]: PhaseField,
    [PREDEFINED_FIELDS.ORDER_IN_CHARGE]: InChargeField,
    [PREDEFINED_FIELDS.ORDER_ASSIGNED_TO]: AssignedToField,
    [PREDEFINED_FIELDS.ORDER_TYPE]: OrderTypeField,
    [PREDEFINED_FIELDS.ORDER_DELIVERY_DATE]: DeliveryDateTimePicker,
    [PREDEFINED_FIELDS.ORDER_TOWER]: TowerField,
    [PREDEFINED_FIELDS.ORDER_NOTE]: OrderNotes,
    [PREDEFINED_FIELDS.ORDER_TECHNICIAN]: TechnicianField,

    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_ONE]: AdditionalPositionField,
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_TWO]: AdditionalPositionField,
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_THREE]: AdditionalPositionField,
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_FOUR]: AdditionalPositionField,
};

type PredefinedFieldProps = {
    id: string;

    /**
     * if set to true, field should
     * automatically receive auto-focus
     */
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
    disabled: boolean;
};

export default function CustomFieldRenderer({
    field,
    disabledPredefinedFields,
}: {
    field: FieldValueWithName;
    disabledPredefinedFields: boolean;
}) {
    const dispatch = useAppDispatch();
    const isSelected = useAppSelector((state) => selectedItem(state, field.id));
    const { redirectField, focusElementInsideContainer } = useOrderDetailsValidationsContext();
    const [activeFocusRef, setActiveFocusRef] = useState(false);

    const isFocusField = (isSelected?.selected ?? false) || activeFocusRef;

    const onEnterPressCallBack = useCallback(() => {
        dispatch(orderActions.setNextFieldIsSelected({ id: field.id }));
    }, [dispatch, field.id]);

    useEffect(() => {
        if (redirectField !== null && redirectField.fieldId === field.id) {
            focusElementInsideContainer(() => {
                if (field.type !== 'Time') setActiveFocusRef(true);
            });
        }
    }, [redirectField, focusElementInsideContainer, field]);

    if (isPredefinedField(field.type)) {
        let Component: FunctionComponent<PredefinedFieldProps>;
        if (field.type === 'PredefinedAdditionalPosition') {
            Component = AdditionalPositionField;
        } else {
            Component = PREDEFINED_FIELDS_COMPONENTS[field.name];
            if (!Component) return <div>Unknown field {field.name}</div>;
        }

        return (
            <Component
                id={field.id}
                isSelected={isFocusField}
                disabled={disabledPredefinedFields}
                onKeyDown={handleCommonKeyDowns({
                    onEnterPress: () => onEnterPressCallBack(),
                })}
            />
        );
    }

    return (
        <CustomField
            isSelected={isFocusField}
            onKeyDown={handleCommonKeyDowns({
                onEnterPress: () => onEnterPressCallBack(),
            })}
            disabled={false}
            field={field as CustomizableFieldValue}
        />
    );
}
