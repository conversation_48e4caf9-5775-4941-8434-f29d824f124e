import { OrderTemplateInfo } from 'api/orders';
import { SignatureHeadersDto } from 'api/Signatures';

export type PrintCustomPdfRequest = {
    generalInformation: boolean;
    customerInformation: boolean;
    vehicleInformation: boolean;
    orderInformation: boolean;
    reasonsForVisit: boolean;
    estimate: boolean;
    summary: boolean;
    notes: boolean;
    jobsAndInspectionItems: boolean;
    discountsSubtotalTaxesTotal: boolean;
    evidence: boolean;
    jobs: boolean;
    digitalSignatures: boolean;
    customerSignatureAtReception: boolean;
    estimatedApprovalSignature: boolean;
    legalRepresentativeSignature: boolean;
    customerSignatureAtDelivery: boolean;
    adhesionContract: boolean;
    privacyNotice: boolean;
    businessSignature: boolean;
    inspectionFormTemplates: string[];
    tmSign1: boolean;
    tmSign2: boolean;
    tmSign3: boolean;
    tmSign4: boolean;
    tmHandwrittenOrderSign: boolean;
    tmHandwrittenEstimateSign: boolean;
    customerHandwrittenSignature: boolean;
};

export function createDefaultPrintCustomPdfRequest(): PrintCustomPdfRequest {
    return {
        generalInformation: false,
        customerInformation: false,
        vehicleInformation: false,
        orderInformation: false,
        reasonsForVisit: false,
        estimate: false,
        summary: false,
        notes: false,
        jobsAndInspectionItems: false,
        discountsSubtotalTaxesTotal: false,
        evidence: false,
        jobs: false,
        digitalSignatures: false,
        customerSignatureAtReception: false,
        estimatedApprovalSignature: false,
        legalRepresentativeSignature: false,
        customerSignatureAtDelivery: false,
        adhesionContract: false,
        privacyNotice: false,
        businessSignature: false,
        inspectionFormTemplates: [],
        tmSign1: false,
        tmSign2: false,
        tmSign3: false,
        tmSign4: false,
        tmHandwrittenOrderSign: false,
        tmHandwrittenEstimateSign: false,
        customerHandwrittenSignature: false,
    };
}

export type CustomPdfSectionData = {
    display: boolean;
    section: keyof PrintCustomPdfRequest;
    name: string | null;
    value?: string;
    subsections?: CustomPdfSectionData[];
};

export function createInitialCustomPdfSections(
    templates: OrderTemplateInfo[],
    signatureHeaders: SignatureHeadersDto
): CustomPdfSectionData[] {
    return [
        {
            display: true,
            section: 'generalInformation',
            name: 'orderDetails.customPdfModal.constructor.generalInformation',
            subsections: [
                {
                    display: true,
                    section: 'customerInformation',
                    name: 'orderDetails.customPdfModal.constructor.customerInformation',
                },
                {
                    display: true,
                    section: 'vehicleInformation',
                    name: 'orderDetails.customPdfModal.constructor.vehicleInformation',
                },
                {
                    display: true,
                    section: 'orderInformation',
                    name: 'orderDetails.customPdfModal.constructor.orderInformation',
                },
                {
                    display: true,
                    section: 'reasonsForVisit',
                    name: 'orderDetails.customPdfModal.constructor.reasonsForVisit',
                },
            ],
        },
        {
            display: true,
            section: 'estimate',
            name: 'orderDetails.customPdfModal.constructor.estimate',
            subsections: [
                {
                    display: true,
                    section: 'summary',
                    name: 'orderDetails.customPdfModal.constructor.summary',
                },
                {
                    display: true,
                    section: 'notes',
                    name: 'orderDetails.customPdfModal.constructor.notes',
                },
                {
                    display: true,
                    section: 'jobsAndInspectionItems',
                    name: 'orderDetails.customPdfModal.constructor.jobsAndInspectionItems',
                    subsections: templates.length
                        ? templates.map((x) => ({
                              display: true,
                              section: 'inspectionFormTemplates',
                              name: x.name,
                              value: x.id + '',
                          }))
                        : undefined,
                },
                {
                    display: true,
                    section: 'discountsSubtotalTaxesTotal',
                    name: 'orderDetails.customPdfModal.constructor.discountsSubtotalTaxesTotal',
                },
            ],
        },
        {
            display: true,
            section: 'evidence',
            name: 'orderDetails.customPdfModal.constructor.evidence',
        },
        {
            display: true,
            section: 'jobs',
            name: 'orderDetails.customPdfModal.constructor.jobs',
        },
        {
            display: true,
            section: 'digitalSignatures',
            name: 'orderDetails.customPdfModal.constructor.digitalSignatures',
            subsections: [
                {
                    display: true,
                    section: 'customerSignatureAtReception',
                    name: signatureHeaders.receptionSignatureHeader,
                },
                {
                    display: true,
                    section: 'estimatedApprovalSignature',
                    name: signatureHeaders.estimateApprovalSignatureHeader,
                },
                {
                    display: true,
                    section: 'customerSignatureAtDelivery',
                    name: signatureHeaders.deliverySignatureHeader,
                },
                {
                    display: true,
                    section: 'adhesionContract',
                    name: signatureHeaders.adhesionContractSignatureHeader,
                },
                {
                    display: true,
                    section: 'privacyNotice',
                    name: signatureHeaders.noticePrivacySignatureHeader,
                },
                {
                    display: true,
                    section: 'legalRepresentativeSignature',
                    name: signatureHeaders.legalRepresentativeSignature,
                },
                {
                    display: true,
                    section: 'customerHandwrittenSignature',
                    name: signatureHeaders.customerHandwrittenSignature,
                },
                {
                    display: true,
                    section: 'businessSignature',
                    name: 'orderDetails.customPdfModal.constructor.businessSignature',
                    subsections: [
                        {
                            display: true,
                            section: 'tmSign1',
                            name: signatureHeaders.teamMemberSignatureHeader1,
                        },
                        {
                            display: true,
                            section: 'tmSign2',
                            name: signatureHeaders.teamMemberSignatureHeader2,
                        },
                        {
                            display: true,
                            section: 'tmSign3',
                            name: signatureHeaders.teamMemberSignatureHeader3,
                        },
                        {
                            display: true,
                            section: 'tmSign4',
                            name: signatureHeaders.teamMemberSignatureHeader4,
                        },
                        {
                            display: true,
                            section: 'tmHandwrittenOrderSign',
                            name: signatureHeaders.teamMemberHandwrittenCustomerFormHeader,
                        },
                        {
                            display: true,
                            section: 'tmHandwrittenEstimateSign',
                            name: signatureHeaders.teamMemberHandwrittenEstimateHeader,
                        },
                    ],
                },
            ],
        },
    ];
}
