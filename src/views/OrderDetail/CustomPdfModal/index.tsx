import { List, styled, useTheme } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { OrderTemplateInfo } from 'api/orders';
import { SignatureHeadersDto } from 'api/Signatures';
import { Button } from 'common/components/Button';
import { Checkbox } from 'common/components/Inputs';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useMemo, useState } from 'react';
import { HttpUtils } from 'services';
import {
    createDefaultPrintCustomPdfRequest,
    createInitialCustomPdfSections,
    CustomPdfSectionData,
    PrintCustomPdfRequest,
} from './util';

type CustomPdfModalProps = {
    open: boolean;
    customPdfLink?: string;
    inspectionFormTemplates: OrderTemplateInfo[];
    signatureHeaders: SignatureHeadersDto;
    onClose: () => void;
};

export default function CustomPdfModal({
    open,
    customPdfLink,
    inspectionFormTemplates,
    signatureHeaders,
    onClose,
}: CustomPdfModalProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const theme = useTheme();

    const [sections, setSections] = useState<CustomPdfSectionData[]>(
        createInitialCustomPdfSections([], signatureHeaders!)
    );

    useEffect(() => {
        if (inspectionFormTemplates.length) {
            setSections(createInitialCustomPdfSections(inspectionFormTemplates, signatureHeaders!));
        }
    }, [inspectionFormTemplates, signatureHeaders]);

    const noSectionsToDisplay = useMemo(() => !sections.some((x) => x.display), [sections]);

    const updateSection = (
        section: CustomPdfSectionData,
        display: boolean,
        indexes: number[]
    ): CustomPdfSectionData => {
        // NOTE (AK) check if it is toggled section or section with toggled subsection
        if ((display || !indexes.length) && section.display !== display) {
            // NOTE (AK) section display property changes only if it is toggled section
            // or toggled subsection is changed to true
            // (of course if current section has opposite display property)
            section.display = display;
        } else if (!indexes.length) {
            // NOTE (AK) if this section is not changed
            // or there is no subsections to be changed
            // just return this section with changed (or not) display property
            return section;
        }

        // NOTE (AK) update subsections
        if (section.subsections && section.subsections.length) {
            // NOTE (AK) update all subsections if it is toggled section
            if (!indexes.length) {
                section.subsections = section.subsections.map((x) => updateSection(x, display, []));
            } else {
                // NOTE (AK) try to update toggled subsection
                section.subsections = section.subsections.map((x, i) =>
                    i === indexes[0] ? updateSection(x, display, indexes.slice(1)) : x
                );

                // NOTE (AK) if all subsections are not displayed
                // then section should not be displayed
                if (!display && section.display && section.subsections.every((x) => !x.display)) {
                    section.display = display;
                }
            }
        }

        return section;
    };

    const handleSectionChange = (display: boolean, indexes: number[]) => {
        indexes.reverse();
        if (indexes.length === 0) {
            return;
        }

        setSections(
            sections.map((x, i) =>
                i === indexes[0] ? updateSection(x, display, indexes.slice(1)) : x
            )
        );
    };

    const sendMutation = useMutation(
        async () => {
            if (noSectionsToDisplay || !customPdfLink) {
                return;
            }

            const printPdfRequest = createDefaultPrintCustomPdfRequest();

            const updatePrintRequestBySection = (section: CustomPdfSectionData) => {
                if (!section.display) {
                    return;
                }

                if (section.section === 'inspectionFormTemplates') {
                    if (section.value?.trim().length) {
                        printPdfRequest[section.section]!.push(section.value?.trim());
                    }
                    return;
                } else {
                    printPdfRequest[section.section] = section.display;

                    if (section.subsections && section.subsections.length) {
                        for (const subsection of section.subsections) {
                            updatePrintRequestBySection(subsection);
                        }
                    }
                }
            };

            for (const section of sections) {
                updatePrintRequestBySection(section);
            }

            let finalLink = customPdfLink;

            for (const section of Object.keys(printPdfRequest)) {
                const sectionKey = section as keyof PrintCustomPdfRequest;
                if (
                    sectionKey === 'inspectionFormTemplates' &&
                    printPdfRequest[sectionKey].length
                ) {
                    finalLink += `&${sectionKey}=${printPdfRequest[sectionKey].join(
                        `&${sectionKey}=`
                    )}`;
                } else if (
                    sectionKey !== 'inspectionFormTemplates' &&
                    printPdfRequest[sectionKey]
                ) {
                    finalLink += `&${sectionKey}=true`;
                }
            }

            HttpUtils.openLinkViaAnchor(finalLink);
            onClose();
        },
        {
            onError: () => {
                toasters.warning(t('toasters.errorOccurredWhenLoading'));
            },
        }
    );

    return (
        <Modal open={open} onClose={onClose}>
            <DivRoot>
                <DivMainLabel>{t('orderDetails.customPdfModal.mainLabel')}</DivMainLabel>
                <DivSecondaryLabel>
                    {t('orderDetails.customPdfModal.secondaryLabel')}
                </DivSecondaryLabel>

                <CustomPdfConstructor sections={sections} onSectionChange={handleSectionChange} />
                <DivButtonsWrapper>
                    <Button
                        label={t('commonLabels.goBack')}
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={theme.palette.neutral[3]}
                        type="button"
                        blockMode
                        onClick={onClose}
                    />
                    <Button
                        label={t('orderDetails.customPdfModal.constructor.printPdf')}
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={theme.palette.primary.main}
                        type="button"
                        blockMode
                        onClick={() => sendMutation.mutate()}
                        disabled={noSectionsToDisplay || !customPdfLink || sendMutation.isLoading}
                    />
                </DivButtonsWrapper>
            </DivRoot>
        </Modal>
    );
}

type CustomPdfConstructorProps = {
    sections: CustomPdfSectionData[];
    onSectionChange: (display: boolean, indexes: number[]) => void;
};

function CustomPdfConstructor({ sections, onSectionChange }: CustomPdfConstructorProps) {
    return (
        <CustomPdfConstructorWrapper>
            <RootList>
                {sections.map((x, i) => (
                    <CustomPdfSection
                        key={i}
                        index={i}
                        firstLevel
                        data={x}
                        onChange={onSectionChange}
                    />
                ))}
            </RootList>
        </CustomPdfConstructorWrapper>
    );
}

type CustomPdfSectionProps = {
    index: number;
    data: CustomPdfSectionData;
    firstLevel?: boolean;
    onChange: (display: boolean, indexes: number[]) => void;
};

function CustomPdfSection({ index, data, firstLevel, onChange }: CustomPdfSectionProps) {
    const { t } = useAppTranslation();

    if (!data.name) return <></>;

    return firstLevel ? (
        <FirstLevelItem>
            <CheckBoxContainer>
                <Checkbox
                    onChange={(_, checked: boolean) => onChange(checked, [index])}
                    value={true}
                    name={data.name}
                    checked={data.display}
                    sx={{ fontWeight: 700 }}
                    size="small"
                />
                <SpanFirstLevelSectionName>{t(data.name)}</SpanFirstLevelSectionName>
            </CheckBoxContainer>
            {data.subsections && data.subsections.length && (
                <ChildList>
                    {data.subsections.map((x, i) => (
                        <CustomPdfSection
                            key={i}
                            index={i}
                            data={x}
                            onChange={(display, indexes) => {
                                indexes.push(index);
                                onChange(display, indexes);
                            }}
                        />
                    ))}
                </ChildList>
            )}
        </FirstLevelItem>
    ) : (
        <div>
            <CheckBoxContainer>
                <Checkbox
                    onChange={(_, checked: boolean) => onChange(checked, [index])}
                    value={true}
                    name={data.name}
                    checked={data.display}
                    size="small"
                />
                <NameEllipsisWrapper>{t(data.name)}</NameEllipsisWrapper>
            </CheckBoxContainer>
            {data.subsections && data.subsections.length && (
                <ChildList>
                    {data.subsections.map((x, i) => (
                        <CustomPdfSection
                            key={i}
                            index={i}
                            data={x}
                            onChange={(display, indexes) => {
                                indexes.push(index);
                                onChange(display, indexes);
                            }}
                        />
                    ))}
                </ChildList>
            )}
        </div>
    );
}

const DivRoot = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[8],
    maxHeight: '90vh',
    width: '578px',
    padding: '40px 40px 40px 70px',
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
}));

const DivMainLabel = styled('div')(({ theme }) => ({
    paddingLeft: '9px',
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[8],
}));

const DivSecondaryLabel = styled('div')(({ theme }) => ({
    paddingLeft: '9px',
    ...theme.typography.h6Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[8],
}));

const CustomPdfConstructorWrapper = styled('div')({
    overflowY: 'auto',
    ...scrollbarStyle(),
});

const RootList = styled(List)({
    padding: 0,
});

const ChildList = styled(List)({
    paddingLeft: '32px',
});

const FirstLevelItem = styled('div')({
    paddingBottom: '16px',
});

const CheckBoxContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    height: '28px',
});

const SpanFirstLevelSectionName = styled('span')({
    fontWeight: 700,
});

const DivButtonsWrapper = styled('div')({
    display: 'flex',
    gap: '16px',
});

const NameEllipsisWrapper = styled('div')({
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});
