import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import IdentificationDocumentAPI from 'api/IdentificationDocument';
import axios from 'axios';
import Dropdown from 'common/components/Inputs/Dropdown';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { IdentificationDocumentTypeDto } from 'datacontracts/Order/IdentificationDocumentDto';
import { KeyboardEventHandler, useCallback, useEffect, useRef } from 'react';
import { useAppDispatch } from 'store';
import { orderActions } from 'store/slices/order/orderDetails';

type IdentificationDocumentTypePickerProps = {
    repairShopId: number | undefined;
    documentTypeId: number | null;
    onNewDocumentType?: (type: IdentificationDocumentTypeDto | null) => void;
    disabled: boolean;
    id: string;
    isSelected: boolean;
    onKeyDown: KeyboardEventHandler<Element>;
};

function useAllTypes(repairShopId: number | undefined) {
    const queryKey = ['id-doc-types', repairShopId];
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { data: allTypes, refetch } = useQuery(
        queryKey,
        async () =>
            repairShopId ? await IdentificationDocumentAPI.getAvailable(repairShopId) : [],
        {
            initialData: [],
        }
    );
    const queryClient = useQueryClient();

    const { mutateAsync: createNewMutate } = useMutation(
        async (name: string) => {
            if (!repairShopId)
                throw new Error('cannot create new document type without repairShopId');
            return await IdentificationDocumentAPI.create({ name, repairShopId: repairShopId });
        },
        {
            onSuccess: (type) => {
                const data = queryClient.getQueryData(queryKey) as Awaited<
                    ReturnType<typeof IdentificationDocumentAPI.getAvailable>
                >;
                queryClient.setQueryData(queryKey, [...data, type]);
                toasters.success(
                    t('orderDetails.identDocType.typeSavedText'),
                    t('orderDetails.identDocType.typeSaved')
                );
            },
            onError: (err) => {
                if (axios.isAxiosError(err) && err.response?.status === 409) {
                    // another document type with the same already exists,
                    // handling this error is not necessary because it is unlikely to occur
                    refetch();
                }
            },
        }
    );

    return {
        allTypes,
        add: useCallback(
            (name: string) => {
                return createNewMutate(name.trim());
            },
            [createNewMutate]
        ),
    };
}

export default function IdentificationDocumentTypePicker({
    repairShopId,
    documentTypeId,
    onNewDocumentType,
    disabled,
    id,
    isSelected,
    onKeyDown,
}: IdentificationDocumentTypePickerProps) {
    const ref = useRef<HTMLSelectElement>(null);
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const { allTypes, add } = useAllTypes(repairShopId);

    const createAndSelectNewType = async (name: string) => {
        const normalizedName = name.trim().toLowerCase();
        const existingType = allTypes.find((t) => t.name.toLowerCase() === normalizedName);
        if (existingType) {
            if (onNewDocumentType) onNewDocumentType(existingType);
            return;
        }

        const newIdDoc = await add(name);
        if (onNewDocumentType) onNewDocumentType(newIdDoc);
    };

    const options = allTypes.map((t) => ({
        value: t.id,
        label: t.name,
    }));
    const value = options.find((t) => t.value === documentTypeId);

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <Dropdown
            ref={ref}
            isClearable
            creatable
            disabled={disabled}
            label={t('orderDetails.identDoc')}
            placeholder={t('orderDetails.identDocType.placeholder')}
            cmosVariant="grey"
            name="identDocType"
            onChange={(v, { action }) => {
                if (v === null) {
                    if (onNewDocumentType) onNewDocumentType(null);
                    return;
                }
                const { label, value } = v;
                if (action === 'create-option') {
                    createAndSelectNewType(label);
                } else {
                    if (onNewDocumentType)
                        onNewDocumentType(allTypes.find((t) => t.id === value) ?? null);
                }
            }}
            options={options}
            value={value}
            onEnterPress={onKeyDown}
            openMenuOnFocus
            onBlur={() => dispatch(orderActions.clearSelectedField({ id }))}
        />
    );
}
