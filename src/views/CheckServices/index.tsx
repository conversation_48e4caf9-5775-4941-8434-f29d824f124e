import { Box } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import CheckServicesApi from 'api/checkServices';
import AreaSpinner from 'common/components/AreaSpinner';
import UnauthorizedLayout from 'common/components/UnauthorizedLayout';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import useStyles from './css';

export const CheckServices = () => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const classes = useStyles();

    const { isFetching: isFetchingServicesInfo, data } = useQuery(
        ['check-services', 'servicesInfo'],
        () => CheckServicesApi.getRepairShopInfo(),
        {
            refetchInterval: 30000,
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            cacheTime: 0,
        }
    );

    const isfirstFetching = isFetchingServicesInfo && data === undefined;
    const listItems = data?.map((service, index, arr) => (
        <div key={service.serviceName} className={classes.element}>
            <div className={classes.elementData}>{service.serviceName}</div>
            <div
                style={{
                    color: service.healthStatus === 'Healthy' ? Colors.Success : Colors.Error,
                }}
                className={classes.elementData}
            >
                {service.healthStatus}
            </div>
        </div>
    ));

    return (
        <UnauthorizedLayout noPadding>
            {!isfirstFetching ? (
                <div className={classes.container}>{listItems}</div>
            ) : (
                <Box component="div" className={classes.loadingContainer}>
                    <AreaSpinner />
                </Box>
            )}
        </UnauthorizedLayout>
    );
};

export default CheckServices;
