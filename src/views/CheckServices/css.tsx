import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';

const CheckServicesTabStyles = makeStyles(() => ({
    container: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        margin: '50px',
    },
    element: {
        width: '50%',
        display: 'flex',
        padding: '15px',
        border: 'solid 1px',
        borderColor: Colors.Neutral4,

        '&:first-child': {
            borderTopLeftRadius: '5px',
            borderTopRightRadius: '5px',
        },

        '&:not(:first-child)': {
            borderTop: 'none',
            borderRadius: 0,
        },

        '&:last-child': {
            borderTop: 'none',
            borderBottomLeftRadius: '5px',
            borderBottomRightRadius: '5px',
            marginBottom: '30px',
        },
    },
    elementData: {
        width: '50%',
        display: 'flex',
    },
    loadingContainer: {
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
}));

export default CheckServicesTabStyles;
