import { getSubdomain } from 'common/ApiHelper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { EmailSendLogic } from '../../../business/EmailSendLogic';
import { Button } from '../../../common/components/Button';
import { CloseIcon } from '../../../common/components/Icons/CloseIcon';
import { LimitedTextArea } from '../../../common/components/Inputs';
import TextFormField from '../../../common/components/Inputs/TextField';
import { Modal } from '../../../common/components/Modal';
import { NotificationType } from '../../../common/components/Notification/INotificationProps';
import { NotificationPull } from '../../../common/components/NotificationPull';
import { NotificationData } from '../../../common/components/NotificationPull/NotificationData';
import { emailRegex } from '../../../common/constants/RegexValidation';
import { ROUTES } from '../../../common/constants/RoutesDefinition';
import { Colors } from '../../../common/styles/Colors';
import { FontPrimary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';
import styles from './styles.module.css';

export interface SupportFormProps {
    isOpen: boolean;
    onClose: Function;
}

export const SupportForm = ({ isOpen, onClose }: SupportFormProps) => {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const maxMessageLength = 150;

    const [email, setEmail] = useState<string>('');
    const [message, setMessage] = useState<string>('');
    const [newNotification, onNotification] = useState<NotificationData>();

    const isSendButtonDisabled = () => {
        return !email || !message || message.length > maxMessageLength;
    };

    const isEnterprise = useIsEnterpriseRoute();

    const send = async () => {
        if (email && emailRegex.test(email.toLowerCase())) {
            const subdomain = getSubdomain();
            await EmailSendLogic.send(subdomain, isEnterprise, null, email, message, true);
            navigate(ROUTES.HELP_EMAIL_SENT);
        } else {
            const notification = new NotificationData(
                t('accountDeactivated.supportForm.enterValidUserName'),
                t('accountDeactivated.supportForm.invalidUserName'),
                NotificationType.warning
            );
            onNotification(notification);
        }
    };

    return (
        <>
            <Modal open={isOpen}>
                <div className={styles.content}>
                    <div className={styles.header}>
                        <span
                            style={{
                                ...FontPrimary(HeaderStyles.H5_14px, true, Colors.Black),
                            }}
                        >
                            {t('accountDeactivated.supportForm.header')}
                        </span>
                        <Button
                            cmosVariant={'typography'}
                            iconPosition="right"
                            color={Colors.Neutral3}
                            Icon={CloseIcon}
                            onClick={() => onClose()}
                        />
                    </div>
                    <div className={styles.email}>
                        <TextFormField
                            label={t('accountDeactivated.supportForm.email')}
                            placeholder={t('accountDeactivated.supportForm.emailPlaceholder')}
                            name={'users.email'}
                            isRequired={true}
                            value={email}
                            maxLength={128}
                            onChange={(event) => {
                                setEmail(event.target.value);
                            }}
                            showValidationIndicators={true}
                            classes={{ root: styles.emailInput }}
                        />
                    </div>
                    <div className={styles.message}>
                        <LimitedTextArea
                            label={t('accountDeactivated.supportForm.message')}
                            placeholder={t('accountDeactivated.supportForm.messagePlaceholder')}
                            rows={5}
                            name={'message'}
                            isRequired={true}
                            value={message}
                            showValidationIndicators={true}
                            onChange={(event) => {
                                setMessage(event.target.value);
                            }}
                            className={styles.messageTextArea}
                            maxLength={maxMessageLength}
                        />
                    </div>
                    <div className={styles.buttonSendContainer}>
                        <Button
                            label={t('accountDeactivated.supportForm.send')}
                            cmosVariant={'filled'}
                            cmosSize={'medium'}
                            onClick={() => {
                                send();
                            }}
                            disabled={isSendButtonDisabled()}
                        />
                    </div>
                </div>
            </Modal>
            <NotificationPull newNotification={newNotification} />
        </>
    );
};
