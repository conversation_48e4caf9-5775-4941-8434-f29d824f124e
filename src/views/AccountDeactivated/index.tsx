import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { Button } from '../../common/components/Button';
import { withFooter } from '../../common/hocs/withFooter';
import { Colors } from '../../common/styles/Colors';
import { FontPrimary } from '../../common/styles/FontHelper';
import { HeaderStyles } from '../../common/styles/HeaderStyles';
import { SupportForm } from './SupportForm';
import styles from './styles.module.css';

const AccountDeactivated = () => {
    const { t } = useAppTranslation();
    const [isModalOpen, setIsModalOpen] = useState(false);

    return (
        <>
            <div className={styles.mainContainer}>
                <div className={styles.image} />
                <span
                    className={styles.somethingDisconnected}
                    style={{
                        ...FontPrimary(HeaderStyles.H1_34px, true, Colors.CM1),
                    }}
                >
                    {t('accountDeactivated.somethingDisconnected')}
                </span>
                <span
                    className={styles.info}
                    style={{
                        ...FontPrimary(HeaderStyles.H3_21px, false, Colors.Neutral7),
                    }}
                >
                    {t('accountDeactivated.accountIsNotActivated')}
                </span>
                <span
                    className={styles.info}
                    style={{
                        ...FontPrimary(HeaderStyles.H3_21px, false, Colors.Neutral7),
                    }}
                >
                    {t('accountDeactivated.executiveCanHelp')}
                </span>
                <div className={styles.contactButtonContainer}>
                    <Button
                        label={t('accountDeactivated.contact')}
                        cmosVariant={'filled'}
                        onClick={() => setIsModalOpen(true)}
                    />
                </div>
            </div>
            <SupportForm onClose={() => setIsModalOpen(false)} isOpen={isModalOpen} />
        </>
    );
};

export default withFooter(AccountDeactivated);
