import { IconProps } from '../../../common/components/Icons/Icon';
import { SignalIcon } from '../../../common/components/Icons/SignalIcon';
import { Colors } from '../../../common/styles/Colors';
import { IconSize } from '../../../common/styles/IconSize';
import { useStyles } from './css';

export const NetworkErrorIcon = ({ fill = Colors.Neutral6, size = IconSize.M }: IconProps) => {
    const styles = useStyles();

    return (
        <div className={styles.circle} style={{ backgroundColor: fill, width: size, height: size }}>
            <SignalIcon fill={Colors.White} />
        </div>
    );
};
