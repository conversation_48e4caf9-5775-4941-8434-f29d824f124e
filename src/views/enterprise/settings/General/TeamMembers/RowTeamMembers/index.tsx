import { Grid, TableRow } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { TeamMemberDto } from 'api/enterprise/settings/teamMembers';
import { TableCell } from 'common/components';
import { Button } from 'common/components/Button';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { textOverFlow } from 'common/styles/TextHelpers';

const useStyles = makeStyles((theme) => ({
    root: {
        minHeight: '56px!important',
        height: 56,
        backgroundColor: Colors.White,
        '&:hover': {
            backgroundColor: Colors.CM5,
            '&:hover': {
                '& .options': {
                    opacity: '1!important',
                },
            },
        },
        '& .options': {
            opacity: 1,
        },
        '&.inactive td>div': {
            opacity: 0.4,
        },
    },
}));
export const RowTeamMembers = ({
    user,
    onEdit,
}: {
    user: TeamMemberDto;
    onEdit: (user: TeamMemberDto) => void;
}) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    return (
        <TableRow
            onClick={(event: any) => {}}
            className={`${styles.root} ${user ? '' : 'inactive'}`}
        >
            <TableCell component="td" scope="row" style={{ paddingLeft: 30 }}>
                <Grid container>{textOverFlow(user.displayName, 42)}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.phoneNumber}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.mobilePhoneNumber}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.userName}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.isActive ? t('users.active') : t('users.inactive')}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <div className="options">
                    <Button
                        label=""
                        color={Colors.Neutral6}
                        cmosVariant={'typography'}
                        cmosSize={'medium'}
                        Icon={EditIcon}
                        onClick={() => onEdit(user)}
                    />
                </div>
            </TableCell>
        </TableRow>
    );
};
