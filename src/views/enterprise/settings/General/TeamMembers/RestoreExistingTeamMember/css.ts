import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    content: {
        width: 605,
        height: 204 - 80,
        maxHeight: 204,
        marginTop: 40,
        marginBottom: 40,
        display: 'flex',
        justifyContent: 'flex-start',
        flexDirection: 'column',
        alignItems: 'center',
    },
    row: {
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
    },
    title: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[8]),
        marginBottom: 16,
    },
    body: {
        marginBottom: 2,
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
    },
    options: {
        marginTop: 30,
        display: 'flex',
        '&>button': {
            width: 160,
        },
        '&>button:first-child': {
            marginRight: 16,
        },
    },
}));
