import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useStyles } from './css';

export interface RestoreExistingTeamMemberProps {
    open: boolean;
    onClose: () => void;
    onConfirm?: () => void;
}
export const RestoreExistingTeamMember = ({
    open,
    onClose,
    onConfirm,
}: RestoreExistingTeamMemberProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();

    const onConfirmHandler = () => {
        if (onConfirm) {
            onClose();
            onConfirm();
        }
    };

    return (
        <>
            <Modal open={open}>
                <div className={styles.content}>
                    <div className={styles.title}>{t('users.popupUserExist.title')}</div>
                    <div
                        className={styles.body}
                        dangerouslySetInnerHTML={{
                            __html: t('users.popupUserExist.body'),
                        }}
                    />
                    <div className={styles.body}>{t('users.popupUserExist.question')}</div>
                    <div className={styles.options}>
                        <Button
                            color={Colors.Neutral3}
                            cmosVariant={'filled'}
                            onClick={onClose}
                            label={t('commonLabels.cancel')}
                        />
                        <Button
                            color={Colors.CM1}
                            cmosVariant={'filled'}
                            onClick={onConfirmHandler}
                            label={t('users.popupUserExist.confirm')}
                        />
                    </div>
                </div>
            </Modal>
        </>
    );
};
