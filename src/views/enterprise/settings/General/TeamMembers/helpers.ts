import { TeamMemberDto } from 'api/enterprise/settings/teamMembers';

export function getCleanUser(user?: TeamMemberDto): TeamMemberDto {
    if (user) {
        return { ...user };
    }

    return getEmptyUser();
}

export function getEmptyUser(): TeamMemberDto {
    return {
        userName: '',
        displayName: '',
        initials: '',
        mobilePhoneNumber: '',
        phoneNumber: '',
        phoneExtension: '',
        isActive: true,
        permission: {
            allowEditEstimates: true,
            settingsAccess: true,
            allowShowEstimates: true,
            allowGenerateReports: true,
            allowSeeAppointments: true,
            allowEditAppointments: true,
            allowSeeAllOrders: true,
            allowSeeAllConversations: true,
        },
    };
}
