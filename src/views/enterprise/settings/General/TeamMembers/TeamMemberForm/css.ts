import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
export const useStyles = makeStyles((theme: Theme) => ({
    content: {
        width: 961 - 170,
        height: 450,
        marginTop: 52,
        marginBottom: 69,
        marginRight: 85,
        marginLeft: 85,
    },
    row: {
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    title: {
        display: 'flex',
        alignItems: 'center',
        ...FontSecondary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
    },
    buttons: {
        width: '50.94%',
        display: 'flex',
        justifyContent: 'space-between',
    },
    buttonCancel: {
        width: 164,
    },
    buttonSuccess: {
        width: 227,
    },
    tabsIndicator: {
        backgroundColor: theme.palette.primary.main,
    },
    tabRoot: {
        textTransform: 'none',
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 14,
        lineHeight: '16px',
        color: theme.palette.neutral[7],
        '&:hover': {
            color: theme.palette.primary.main,
            opacity: 1,
        },
        '&:focus': {
            color: theme.palette.primary.main,
        },
    },
    tabSelected: {
        color: theme.palette.primary.main,
    },
    col1: {
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        rowGap: 20,
    },
    col3: {
        width: '45%',
        display: 'flex',
        flexDirection: 'column',
        rowGap: 20,
    },
    passwordMessage: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[5]),
        paddingTop: 9,
        paddingBottom: 29,
    },
    rootPermission: {
        height: 310,
        overflowY: 'scroll',
        '&::-webkit-scrollbar': {
            width: '5px',
        },
        '&::-webkit-scrollbar-track': {
            background: theme.palette.neutral[4],
            borderRadius: '10px',
        },
        '&::-webkit-scrollbar-thumb': {
            background: theme.palette.neutral[6],
            borderRadius: '10px',
        },
    },
    permissionsTitle: {
        ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[6]),
        fontWeight: 700,
    },
    permissionActive: {
        marginTop: 16,
        marginBottom: 9,
        width: '100%',
        display: 'flex',
        justifyContent: 'space-start',
    },
    activeLabel: {
        ...FontSecondary(HeaderStyles.H5_14px, true, theme.palette.neutral[8]),
        marginBottom: 4,
    },
    activeDescription: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[6]),
        width: '100%',
        '&>*': {
            display: 'contents',
        },
        '&>.strong': {
            ...FontSecondary(HeaderStyles.H6_12px, true, theme.palette.neutral[6]),
        },
    },
    separator: {
        borderBottom: `solid 1px ${theme.palette.neutral[3]}`,
        width: '100%',
        marginTop: 21,
        marginRight: 21,
        marginBottom: 30,
    },
}));
