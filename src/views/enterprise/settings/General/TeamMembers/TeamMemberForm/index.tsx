import { Grid, Tab, Tabs } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import TeamMembersAPI, {
    TeamMemberDto,
    UserExistsResponse,
} from 'api/enterprise/settings/teamMembers';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { phoneFormatRegexMask } from 'common/FormatersHelper';
import { checkInvalidPassword } from 'common/PasswordHelper';
import { Button } from 'common/components/Button';
import { TextField } from 'common/components/Inputs';
import MaskedTextFormField from 'common/components/Inputs/MaskedTextFormField';
import PasswordField from 'common/components/Inputs/PasswordField';
import { Switch } from 'common/components/Inputs/Switch';
import { Modal } from 'common/components/Modal';
import { emailRegex } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { RestoreExistingTeamMember } from '../RestoreExistingTeamMember';
import TabPanel from '../TabPanel';
import { getCleanUser, getEmptyUser } from '../helpers';
import { useStyles } from './css';

export interface TeamMemberFormProps {
    open: boolean;
    onClose: Function;
    onSave?: (user: TeamMemberDto) => void;
    userEdit?: TeamMemberDto;
}
export const TeamMemberForm = ({ open, onClose, userEdit, onSave }: TeamMemberFormProps) => {
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const maxLengthPhone = useMemo(
        () => InternationalizationLogic.maxLengthPhone(internationalization),
        [internationalization]
    );

    const { t } = useAppTranslation();
    const styles = useStyles();
    const toasters = useToasters();

    const [user, setUser] = useState<TeamMemberDto>(getEmptyUser);
    const [userEdited, setUserEdited] = useState<TeamMemberDto>(getEmptyUser);
    const [existUserData, setExistUserData] = useState<UserExistsResponse>();
    const [password, setPassword] = useState('');
    const [passwordConfirm, setPasswordConfirm] = useState('');
    const [isEnteredDataValid, setIsEnteredDataValid] = useState(false);
    const [openExist, setIsOpenPopupExist] = useState(false);
    const [tabValue, setTabValue] = useState<number>(0);

    const { valid: isEmailValid, reset: resetEmailValidation } = useIsEmailValid(user.userName);

    useEffect(() => {
        resetEmailValidation();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user.id]);
    const saveHandler = async () => {
        if (tabValue === 0 && !user.id) {
            setTabValue(tabValue + 1);
        } else {
            const validated = await validationForm();
            if (!validated) return;
            if (saveMutation.isLoading) return;
            saveMutation.mutate();
        }
    };

    const saveMutation = useMutation(
        async () => {
            const response = await TeamMembersAPI.save({
                user: user,
                password,
                confirmPassword: passwordConfirm,
            });

            if (onSave) onSave({ ...response.user! });
            closeHandler();
            setTabValue(0);
        },
        {
            onSuccess: () => {
                const notificationBody = user.id
                    ? 'users.notifications.userUpdatedSuccessfullyBody'
                    : 'users.notifications.userAddedSuccessfullyBody';

                toasters.success(t(notificationBody), t('toasters.settingSuccessfullyUpdated'));
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const restoreMutation = useMutation(
        async () => {
            if (
                !existUserData ||
                !existUserData.userWithUserName ||
                existUserData.userWithUserName.isActive
            ) {
                throw new Error('Confirm without existUserData');
            }

            if (existUserData.userWithUserName.matchPassword) {
                toasters.danger(
                    t(`users.notifications.reviewPasswordBody`),
                    t(`users.notifications.reviewPasswordTitle`)
                );
                return;
            }
            const response = await TeamMembersAPI.save({
                user: {
                    ...user,
                    id: existUserData.userWithUserName!.id,
                    isActive: true,
                },
                password,
                confirmPassword: passwordConfirm,
            });

            if (onSave) onSave({ ...response.user! });
            closeHandler();
        },
        {
            onSuccess: () => {
                toasters.success(
                    t('users.notifications.userActivatedSuccessfullyBody'),
                    t('users.notifications.userActivatedSuccessfullyTitle')
                );
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const validationForm = async (): Promise<boolean> => {
        if (password !== passwordConfirm) {
            toasters.danger(
                t('users.notifications.matchPasswordBody'),
                t('users.notifications.matchPasswordTitle')
            );
            return false;
        } else if (password !== '') {
            const errors = checkInvalidPassword(password);
            if (errors.length > 0) {
                toasters.danger(
                    t('users.notifications.rememberBody'),
                    t('users.notifications.rememberTitle')
                );
                return false;
            }
        }

        const checkExists = await TeamMembersAPI.exists({
            userName: user.userName,
            password,
        });

        if (!checkExists) {
            console.error('Error CheckExist error...');
            return false;
        }

        setExistUserData(checkExists);

        if (
            checkExists.userWithUserName &&
            checkExists.userWithUserName.id &&
            checkExists.userWithUserName.id !== user.id
        ) {
            if (checkExists.userWithUserName.isActive) {
                toasters.danger(t('users.notifications.userNameIsUsedBody'), '');
                return false;
            } else {
                setIsOpenPopupExist(true);
                return false;
            }
        }

        return true;
    };

    const closeHandler = () => {
        if (onClose) {
            onClose({ ...user });
        }
        setUser(getEmptyUser());
    };

    const isEdited = useMemo(() => {
        return (
            !user.id ||
            JSON.stringify(user) !== JSON.stringify(userEdited) ||
            (password.trim() !== '' &&
                passwordConfirm.trim() !== '' &&
                password === passwordConfirm)
        );
    }, [user, userEdited, password, passwordConfirm]);

    useEffect(() => {
        //Reset password
        const cleanUser = getCleanUser(userEdit);
        setUser(cleanUser);
        setUserEdited(cleanUser);
        setPassword('');
        setPasswordConfirm('');
    }, [user.id, open]);

    /**
     * Update Enable Send Form
     */
    useEffect(() => {
        if (user.id) {
            //VALIDATIONS EDIT MODE
            if (password.trim() !== '' && passwordConfirm.trim() === '')
                return setIsEnteredDataValid(false);
        } else {
            //VALIDATIONS ADD MODE
            if (password.trim() === '' || passwordConfirm.trim() === '')
                return setIsEnteredDataValid(false);
        }

        if (!user.displayName.trim() || !isEmailValid || !user.initials || !user.initials.trim()) {
            return setIsEnteredDataValid(false);
        }

        return setIsEnteredDataValid(true);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user.id, user.displayName, user.initials, isEmailValid, password, passwordConfirm]);

    const a11yProps = (index: any) => {
        return {
            id: `tab-${index}`,
            'aria-controls': `tabpanel-${index}`,
        };
    };

    return (
        <>
            <Modal open={open}>
                <div className={styles.content}>
                    {/* #region Title / Buttons */}
                    <div className={styles.row} style={{ marginBottom: 40 }}>
                        <div className={styles.title}>
                            {t(`users.${user.id ? 'edit' : 'add'}.title`)}
                        </div>
                        <div className={styles.buttons}>
                            <Button
                                className={styles.buttonCancel}
                                label={
                                    tabValue === 0 || user.id
                                        ? t('commonLabels.cancel')
                                        : t('commonLabels.goBack')
                                }
                                onClick={() => {
                                    if (tabValue !== 0 && !user.id) {
                                        setTabValue(tabValue - 1);
                                    } else if (onClose) {
                                        onClose(user);
                                        setTabValue(0);
                                    }
                                }}
                                cmosVariant={'filled'}
                                color={Colors.Neutral3}
                                cmosSize={'medium'}
                            />
                            <Button
                                className={styles.buttonSuccess}
                                showLoader={saveMutation.isLoading}
                                disabled={
                                    !isEdited || !isEnteredDataValid || saveMutation.isLoading
                                }
                                label={
                                    user.id
                                        ? t('users.edit.saveButton')
                                        : tabValue === 1
                                        ? t('users.add.saveButton')
                                        : t('commonLabels.continue')
                                }
                                onClick={saveHandler}
                                cmosVariant={'filled'}
                                color={Colors.Success}
                                cmosSize={'medium'}
                            />
                        </div>
                    </div>

                    <Tabs
                        classes={{ indicator: styles.tabsIndicator }}
                        value={tabValue}
                        style={{ marginBottom: 36 }}
                        onChange={(event: ChangeEvent<{}>, newValue: number) => {
                            setTabValue(newValue);
                        }}
                    >
                        <Tab
                            aria-label={t('users.generalInformation')}
                            label={t('users.generalInformation')}
                            classes={{ root: styles.tabRoot, selected: styles.tabSelected }}
                            {...a11yProps(0)}
                        />
                        <Tab
                            aria-label={t('users.permissions.sectionTitle')}
                            label={t('users.permissions.sectionTitle')}
                            classes={{ root: styles.tabRoot, selected: styles.tabSelected }}
                            disabled={!isEnteredDataValid}
                            {...a11yProps(1)}
                        />
                    </Tabs>

                    {/* #region General Data */}
                    <TabPanel value={tabValue} index={0}>
                        <div className={styles.col1}>
                            <div className={styles.row}>
                                <div className={styles.col3}>
                                    <div className={styles.row}>
                                        <div style={{ width: 280 }}>
                                            <TextField
                                                block
                                                label={t('users.fullName')}
                                                placeholder={t('users.placeholder.fullName')}
                                                name={'users.fullName'}
                                                isRequired={true}
                                                maxLength={128}
                                                value={user?.displayName}
                                                onChange={(event) => {
                                                    if (restoreMutation.isLoading) return;
                                                    setUser((old) => ({
                                                        ...old,
                                                        displayName: event.target.value,
                                                    }));
                                                }}
                                                showValidationIndicators={true}
                                            />
                                        </div>
                                        <div style={{ width: 60 }}>
                                            <TextField
                                                block
                                                label={t('users.initials')}
                                                placeholder={t('users.placeholder.initials')}
                                                name={'users.initials'}
                                                isRequired={true}
                                                value={user?.initials}
                                                maxLength={2}
                                                onChange={(event) => {
                                                    if (restoreMutation.isLoading) return;
                                                    setUser((old) => ({
                                                        ...old,
                                                        initials: event.target.value,
                                                    }));
                                                }}
                                                showValidationIndicators={true}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className={styles.col3}>
                                    <TextField
                                        block
                                        label={t('users.email')}
                                        placeholder={t('users.placeholder.email')}
                                        name={'users.email'}
                                        isRequired={true}
                                        value={user?.userName}
                                        maxLength={128}
                                        isInvalid={isEmailValid === false}
                                        onChange={(event) => {
                                            setUser((old) => ({
                                                ...old,
                                                userName: event.target.value,
                                            }));
                                        }}
                                        showValidationIndicators={true}
                                    />
                                </div>
                            </div>
                            <div className={styles.row}>
                                <div className={styles.col3}>
                                    <MaskedTextFormField
                                        fullWidth
                                        label={t('users.mobile')}
                                        placeholder={t('users.placeholder.mobile')}
                                        name={'users.mobile'}
                                        maxLength={maxLengthPhone}
                                        isRequired={false}
                                        value={user.mobilePhoneNumber}
                                        onChange={(event: any) => {
                                            if (restoreMutation.isLoading) return;
                                            setUser((old) => ({
                                                ...old,
                                                mobilePhoneNumber: event.target.value,
                                            }));
                                        }}
                                        showValidationIndicators={false}
                                        mask={phoneFormatRegexMask(
                                            internationalization.phoneNumberFormat
                                        )}
                                    />
                                </div>
                                <div className={styles.col3}>
                                    <div className={styles.row}>
                                        <div style={{ width: 280 }}>
                                            <MaskedTextFormField
                                                fullWidth
                                                label={t('users.phone')}
                                                placeholder={t('users.placeholder.phone')}
                                                name={'users.phone'}
                                                maxLength={maxLengthPhone}
                                                isRequired={false}
                                                value={user.phoneNumber}
                                                onChange={(event: any) => {
                                                    if (restoreMutation.isLoading) return;
                                                    setUser((old) => ({
                                                        ...old,
                                                        phoneNumber: event.target.value,
                                                    }));
                                                }}
                                                showValidationIndicators={false}
                                                mask={phoneFormatRegexMask(
                                                    internationalization.phoneNumberFormat
                                                )}
                                            />
                                        </div>
                                        <div style={{ width: 60 }}>
                                            <TextField
                                                block
                                                label={t('users.phoneExtension')}
                                                placeholder={t('users.placeholder.phoneExtension')}
                                                name={'users.phoneExtension'}
                                                maxLength={10}
                                                type="number"
                                                isRequired={false}
                                                value={user.phoneExtension}
                                                onChange={(event) => {
                                                    if (restoreMutation.isLoading) return;
                                                    setUser((old) => ({
                                                        ...old,
                                                        phoneExtension: event.target.value,
                                                    }));
                                                }}
                                                showValidationIndicators={false}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={styles.row}>
                                <div className={styles.col3}>
                                    <PasswordField
                                        block
                                        label={t('users.password')}
                                        placeholder={t('users.placeholder.password')}
                                        name={'users.password'}
                                        isRequired={true}
                                        autoComplete={'new-password'}
                                        value={password}
                                        onChange={(e) => {
                                            if (restoreMutation.isLoading) return;
                                            setPassword(e.target.value);
                                        }}
                                        showValidationIndicators={true}
                                    />
                                </div>

                                <div className={styles.col3}>
                                    <PasswordField
                                        block
                                        label={t('users.confirmPassword')}
                                        placeholder={t('users.placeholder.confirmPassword')}
                                        name={'users.confirmPassword'}
                                        autoComplete={'new-password'}
                                        onChange={(e) => {
                                            if (restoreMutation.isLoading) return;
                                            setPasswordConfirm(e.target.value);
                                        }}
                                        isRequired={true}
                                        value={passwordConfirm}
                                        showValidationIndicators={true}
                                    />
                                </div>
                            </div>
                            <div className={[styles.row, styles.passwordMessage].join(' ')}>
                                {t('users.passwordSecurityMessage')}
                            </div>
                        </div>
                    </TabPanel>

                    <TabPanel value={tabValue} index={1}>
                        <Grid container spacing={0} className={styles.rootPermission}>
                            <div className={`${styles.row} ${styles.permissionsTitle}`}>
                                {t('users.permissions.administrationSectionTitle')}
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.settingsAccess}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                settingsAccess: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.administrationLabel.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(`users.permissions.administrationLabel.description`)}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.separator} />
                            <div className={`${styles.row} ${styles.permissionsTitle}`}>
                                {t('users.permissions.activitySectionTitle')}
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.isActive}
                                    onChange={(_: any, checked: boolean) => {
                                        setUser({
                                            ...user,
                                            isActive: checked,
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.activeSwitchContainer.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(`users.permissions.activeSwitchContainer.description`)}
                                    </div>
                                </div>
                            </div>
                            {repairShopSettings?.features.estimateReview ? (
                                <div className={styles.permissionActive}>
                                    <Switch
                                        checked={user.permission.allowShowEstimates}
                                        onChange={(_: any, checked: boolean) => {
                                            setUser({
                                                ...user,
                                                permission: {
                                                    ...user.permission,
                                                    allowShowEstimates: checked,
                                                },
                                            });
                                        }}
                                        name="checkedA"
                                    />
                                    <div>
                                        <div className={styles.activeLabel}>
                                            {t(`users.permissions.showEstimationContainer.title`)}
                                        </div>
                                        <div className={styles.activeDescription}>
                                            {t(
                                                `users.permissions.showEstimationContainer.description`
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                ''
                            )}
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.allowEditEstimates}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowEditEstimates: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.changeEstimationContainer.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(
                                            `users.permissions.changeEstimationContainer.description`
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.allowGenerateReports}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowGenerateReports: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.generateReportContainer.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(`users.permissions.generateReportContainer.description`)}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.allowSeeAppointments}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSeeAppointments: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.seeAllAppointments.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(`users.permissions.seeAllAppointments.description`)}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.allowEditAppointments}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowEditAppointments: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.createAndEditAppointments.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(
                                            `users.permissions.createAndEditAppointments.description`
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.allowSeeAllOrders}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSeeAllOrders: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.seeAllOrdersContainer.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(`users.permissions.seeAllOrdersContainer.description`)}
                                    </div>
                                </div>
                            </div>
                            <div className={styles.permissionActive}>
                                <Switch
                                    checked={user.permission.allowSeeAllConversations}
                                    onChange={(_: any, checked: boolean) => {
                                        if (restoreMutation.isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSeeAllConversations: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <div className={styles.activeLabel}>
                                        {t(`users.permissions.seeAllConversationContainer.title`)}
                                    </div>
                                    <div className={styles.activeDescription}>
                                        {t(
                                            `users.permissions.seeAllConversationContainer.description`
                                        )}
                                    </div>
                                </div>
                            </div>
                        </Grid>
                    </TabPanel>
                </div>
            </Modal>
            {openExist && (
                <RestoreExistingTeamMember
                    open={openExist}
                    onClose={() => setIsOpenPopupExist(false)}
                    onConfirm={() => {
                        restoreMutation.mutate();
                    }}
                />
            )}
        </>
    );
};

function useIsEmailValid(email: string) {
    const stateRef = useRef({
        wasChanged: false,
        initialValue: email,
        valid: null as null | boolean,
    });
    const fr = useForceRender();
    if (!stateRef.current.wasChanged) {
        if (email === stateRef.current.initialValue) {
            stateRef.current.valid = null;
        } else {
            stateRef.current.valid = false; // set to false to trigger revalidation below
            stateRef.current.wasChanged = true;
            stateRef.current.initialValue = email;
        }
    }

    if (stateRef.current.valid !== null)
        stateRef.current.valid = emailRegex.test(email.toLowerCase());

    return {
        valid: stateRef.current.valid,
        reset: useCallback(() => {
            stateRef.current.wasChanged = false;
            stateRef.current.initialValue = '';
            stateRef.current.valid = null;
            fr();
        }, [fr]),
    };
}
