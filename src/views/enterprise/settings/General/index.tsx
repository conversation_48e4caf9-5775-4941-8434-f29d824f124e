import { RouterTab } from 'common/components/tabs/RouterParameterBasedTabs';
import { ENTERPRISE_ROUTES, GEN_SETTINGS_TABS } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { PermissionsGate } from 'views/Components/AuthorizationGate';
import SettingsRouterParameterBasedTabs from 'views/Settings/common/SettingsRouterParameterBasedTabs';
import InspectionFormsSettings from './InspectionForms';
import { TeamMembersSettings } from './TeamMembers';

type Tab = {
    section: string;
    label: string;
    component: React.ReactNode;
    isVisible: (glSettings: GlobalSettingsDto) => boolean;
};

const Tabs: Tab[] = [
    {
        section: GEN_SETTINGS_TABS.TEAM_MEMBERS,
        label: 'settings.general.teamMembers',
        component: <TeamMembersSettings />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.INSPECTION_FORMS,
        label: 'settings.general.inspectionForms',
        component: <InspectionFormsSettings />,
        isVisible: () => true,
    },
];

function GeneralSettings() {
    const { t } = useAppTranslation();
    const globalSettings = useSelector(selectSettings);

    const tabs: RouterTab[] = useMemo(
        () =>
            Tabs.filter((m) => m.isVisible(globalSettings)).map((v) => ({
                content: v.component,
                value: v.section,
                label: t(v.label),
            })),
        [globalSettings, t]
    );

    useDocumentTitle(t('titles.settings.general'));

    return (
        <PermissionsGate predicate="settingsAccess" text={t('settings.accessDeniedText')}>
            <SettingsRouterParameterBasedTabs
                urlPattern={ENTERPRISE_ROUTES.SETTINGS.GENERAL.PATH}
                parameterName="section"
                tabs={tabs}
            >
                {(content) => content}
            </SettingsRouterParameterBasedTabs>
        </PermissionsGate>
    );
}
export default GeneralSettings;
