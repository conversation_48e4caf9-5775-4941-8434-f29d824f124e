import {
    Box,
    CircularProgress,
    Divider,
    FormControlLabel,
    IconButton,
    InputAdornment,
    outlinedInputClasses,
    SelectChangeEvent,
    styled,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { LocationDto } from 'api/enterprise';
import EnterpriseInspectionFormsApi, {
    OrderTemplateDto,
} from 'api/enterprise/settings/inspectionForms';
import { Button } from 'common/components/Button';
import { DeleteIcon, EditIcon } from 'common/components/Icons';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import InfoText from 'common/components/InfoText';
import { Checkbox, TextFormField } from 'common/components/Inputs';
import { SMenuItem, SSelect } from 'common/components/mui';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { t } from 'i18next';
import { useEffect, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { useAppDispatch, useAppSelector } from 'store';
import {
    cloneTemplateThunk,
    cloneTemplateToAllThunk,
    createTemplateThunk,
    deleteTemplateThunk,
    inspectionFormSettingsActions,
    selectTemplates,
    updateTemplateThunk,
} from 'store/slices/enterprise/inspectionFormsSettings';
import { useLocations } from 'store/slices/enterprise/locations';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import PageContent from 'views/Components/Page';
import CreateNewSystemButton from './CreateNewSystemButton';
import TemplateEditor from './TemplateEditor';
import { InspectionItemCommentsModalContextProvider } from './TemplateEditor/InspectionItemCommentsModal';
import { InspectionItemDetailsModalContextProvider } from './TemplateEditor/InspectionItemDetailsModal';
import { InspectionItemEstimateModalContextProvider } from './TemplateEditor/InspectionItemEstimateModal';

export default function InspectionFormsSettings() {
    const dispatch = useAppDispatch();

    const [location, setLocation] = useState<LocationDto | null>(null);
    const [cloneDestination, setCloneDestination] = useState<LocationDto | null>(null);
    const [template, setTemplate] = useState<OrderTemplateDto | null>(null);
    const [editorMode, setEditorMode] = useState<'none' | 'create' | 'edit' | 'clone'>('none');
    const [openDeletePopup, setOpenDeletePopup] = useState(false);

    const [name, setName] = useState(template?.name ?? '');
    const [showDetails, setShowDetails] = useState(template?.showDetails ?? false);
    const [displayInPdf, setDisplayInPdf] = useState(template?.displayInPdf ?? false);

    useEffect(() => {
        if (template) {
            setName(template.name);
            setShowDetails(template.showDetails);
            setDisplayInPdf(template.displayInPdf);
        } else {
            setName('');
            setShowDetails(false);
            setDisplayInPdf(false);
        }
    }, [template]);

    const updateTemplate = useMutation({
        mutationFn: async (
            checkboxes: Partial<Pick<OrderTemplateDto, 'showDetails' | 'displayInPdf'>>
        ) => {
            if (template) {
                await dispatch(
                    updateTemplateThunk({
                        templateId: template.id,
                        shopId: location?.repairShopId ?? null,
                        data: {
                            showDetails,
                            name,
                            displayInPdf,
                            ...checkboxes,
                        },
                    })
                );
                setEditorMode('none');
            } else {
                throw new Error('template is null');
            }
        },
    });

    const createTemplate = useMutation({
        mutationFn: async () => {
            await dispatch(
                createTemplateThunk({
                    shopId: location?.repairShopId ?? null,
                    data: {
                        showDetails,
                        name,
                        displayInPdf,
                    },
                })
            );
            setEditorMode('none');
        },
    });

    const deleteMutation = useMutation({
        mutationFn: async () => {
            if (template) {
                await dispatch(
                    deleteTemplateThunk({
                        templateId: template.id,
                        shopId: location?.repairShopId ?? null,
                    })
                );
            }
            setOpenDeletePopup(false);
            setTemplate(null);
        },
    });

    const cloneTemplate = useMutation({
        mutationFn: async () => {
            if (!template) {
                return;
            }

            if (cloneDestination) {
                const result = await dispatch(
                    cloneTemplateThunk({
                        data: {
                            toShopId: cloneDestination.repairShopId,
                            fromShopId: location?.repairShopId ?? null,
                            templateId: template.id,
                        },
                    })
                );

                if (result.meta.requestStatus === 'fulfilled') {
                    const response = result.payload as OrderTemplateDto;
                    setTemplate(response);
                    setLocation(cloneDestination);
                } else {
                    throw result.payload;
                }
            } else {
                const result = await dispatch(
                    cloneTemplateToAllThunk({
                        data: {
                            fromShopId: location?.repairShopId ?? null,
                            templateId: template.id,
                        },
                    })
                );

                if (result.meta.requestStatus === 'rejected') {
                    throw result.payload;
                }
            }

            setEditorMode('none');
            setCloneDestination(null);
        },
    });

    const handleSaveClick = () => {
        if (editorMode === 'edit') {
            updateTemplate.mutate({});
        } else {
            createTemplate.mutate();
        }
    };

    const handleCreateClick = () => {
        setEditorMode('create');
        setName('');
        setShowDetails(false);
        setDisplayInPdf(false);
    };

    return (
        <PageContent paddedX paddedY>
            <DeleteConfirmationPopup
                open={openDeletePopup}
                onClose={() => setOpenDeletePopup(false)}
                body={
                    <Trans
                        i18nKey="inspectionForms.templates.confirmationDelete.body"
                        components={{ strong: <strong /> }}
                    />
                }
                title={t('inspectionForms.templates.confirmationDelete.title')}
                cancel={t('inspectionForms.templates.confirmationDelete.cancel')}
                confirm={t('inspectionForms.templates.confirmationDelete.confirm')}
                onConfirm={() => deleteMutation.mutate()}
            />
            <Header>
                <DivSelectors sx={{ marginBottom: 'none' }}>
                    <LocationOrEnterpriseSelector
                        value={location}
                        onChange={(location) => {
                            setLocation(location);
                            setTemplate(null);
                        }}
                    />
                    {editorMode !== 'edit' && editorMode !== 'create' && (
                        <TemplatesSelector
                            onCreate={handleCreateClick}
                            shopId={location?.repairShopId}
                            value={template}
                            onChange={setTemplate}
                        />
                    )}

                    {(editorMode === 'edit' || editorMode === 'create') && (
                        <EditTemplateName
                            createMode={editorMode === 'create'}
                            isLoading={updateTemplate.isLoading || createTemplate.isLoading}
                            name={name}
                            setName={setName}
                            onSave={handleSaveClick}
                            showDetails={showDetails}
                            setShowDetails={setShowDetails}
                        />
                    )}

                    {editorMode === 'clone' && (
                        <>
                            <CloneDestinationSelector
                                source={location}
                                value={cloneDestination}
                                onChange={setCloneDestination}
                            />
                            <IconButton
                                disabled={cloneTemplate.isLoading}
                                size="small"
                                onClick={() => cloneTemplate.mutate()}
                            >
                                {cloneTemplate.isLoading ? (
                                    <CircularProgress size={16} sx={{ m: '3px' }} thickness={4} />
                                ) : (
                                    <CheckIcon />
                                )}
                            </IconButton>
                        </>
                    )}

                    {editorMode !== 'none' && (
                        <IconButton
                            sx={{ ml: -1 }}
                            size="small"
                            onClick={() => setEditorMode('none')}
                        >
                            <CloseIcon />
                        </IconButton>
                    )}

                    {editorMode === 'none' && (
                        <>
                            <ArrowTooltip content={t('commonLabels.copy')}>
                                <IconButton size="small" onClick={() => setEditorMode('clone')}>
                                    <CopyIcon fill="currentColor" />
                                </IconButton>
                            </ArrowTooltip>

                            <ArrowTooltip content={t('commonLabels.edit')}>
                                <IconButton size="small" onClick={() => setEditorMode('edit')}>
                                    <EditIcon fill="currentColor" />
                                </IconButton>
                            </ArrowTooltip>

                            <ArrowTooltip content={t('commonLabels.delete')}>
                                <IconButton size="small" onClick={() => setOpenDeletePopup(true)}>
                                    <DeleteIcon fill="currentColor" />
                                </IconButton>
                            </ArrowTooltip>
                        </>
                    )}
                    <DivSpacer />
                    <CreateNewSystemButton
                        onSystemCreated={(system) => {
                            dispatch(inspectionFormSettingsActions.addSystem(system));
                        }}
                        shopId={location?.repairShopId ?? null}
                        templateId={template?.id}
                    />
                </DivSelectors>

                {template && (
                    <>
                        <FormControlLabel
                            sx={{
                                ml: '-12px',
                            }}
                            slotProps={{
                                typography: {
                                    variant: 'h6Inter',
                                    sx: {
                                        color: 'var(--neutral6)',
                                        fontWeight: 'normal',
                                    },
                                },
                            }}
                            control={
                                <Checkbox
                                    sx={{ padding: '6px 9px 3px 9px' }}
                                    onChange={(e) =>
                                        editorMode === 'create'
                                            ? setShowDetails(e.target.checked)
                                            : updateTemplate.mutate({
                                                  showDetails: e.target.checked,
                                              })
                                    }
                                    checked={showDetails}
                                />
                            }
                            label={t('inspectionForms.templates.showDetails')}
                        />

                        <FormControlLabel
                            sx={{
                                ml: '-12px',
                            }}
                            slotProps={{
                                typography: {
                                    variant: 'h6Inter',
                                    sx: {
                                        color: 'var(--neutral6)',
                                        fontWeight: 'normal',
                                    },
                                },
                            }}
                            control={
                                <Checkbox
                                    sx={{ padding: '3px 9px' }}
                                    onChange={(e) =>
                                        editorMode === 'create'
                                            ? setDisplayInPdf(e.target.checked)
                                            : updateTemplate.mutate({
                                                  displayInPdf: e.target.checked,
                                              })
                                    }
                                    checked={displayInPdf}
                                />
                            }
                            label={t('inspectionForms.templates.displayInPdf')}
                        />
                    </>
                )}
            </Header>

            {location === null && (
                <InfoText sx={{ my: 3 }}>{t('inspectionForms.enterpriseFormNotice')}</InfoText>
            )}

            <InspectionItemCommentsModalContextProvider>
                <InspectionItemDetailsModalContextProvider>
                    <InspectionItemEstimateModalContextProvider>
                        {template && (
                            <TemplateEditor
                                template={template}
                                shopId={location?.repairShopId ?? null}
                            />
                        )}
                    </InspectionItemEstimateModalContextProvider>
                </InspectionItemDetailsModalContextProvider>
            </InspectionItemCommentsModalContextProvider>
        </PageContent>
    );
}

const DivSelectors = styled('div')({
    display: 'flex',
    gap: 8,
    alignItems: 'end',
    height: 60,
});

const DivSpacer = styled('div')({
    flexBasis: '100%',
    flex: '1',
});

const Header = styled('header')({
    marginBottom: 12,
    display: 'flex',
    flexDirection: 'column',
});

function LocationOrEnterpriseSelector({
    value,
    onChange,
}: {
    value: LocationDto | null;
    onChange: (value: LocationDto | null) => void;
}) {
    const gs = useAppSelector(selectSettings);
    const locations = useLocations();

    function handleChange(event: SelectChangeEvent<string>) {
        const value = event.target.value;
        if (value === '') {
            onChange(null);
        } else {
            const location = locations.find((x) => x.repairShopId === value);
            if (location) {
                onChange(location);
            }
        }
    }

    return (
        <StyledSelect value={value?.repairShopId || ''} onChange={handleChange}>
            <SMenuItem value={''}>{gs.name}</SMenuItem>
            {locations.map((location) => (
                <SMenuItem value={location.repairShopId}>{location.name}</SMenuItem>
            ))}
        </StyledSelect>
    );
}

function CloneDestinationSelector({
    source,
    value,
    onChange,
}: {
    value: LocationDto | null;
    source: LocationDto | null;
    onChange: (value: LocationDto | null) => void;
}) {
    const locations = useLocations();
    const { t } = useAppTranslation();

    function handleChange(event: SelectChangeEvent<string>) {
        const value = event.target.value;
        if (value === '') {
            onChange(null);
        } else {
            const location = locations.find((x) => x.repairShopId === value);
            if (location) {
                onChange(location);
            }
        }
    }

    const availableLocations = source
        ? locations.filter((x) => x.repairShopId !== source.repairShopId)
        : locations;

    return (
        <StyledSelect value={value?.repairShopId || ''} onChange={handleChange}>
            <SMenuItem value="">{t('inspectionForms.cloneToAll')}</SMenuItem>
            <Divider />
            {availableLocations.map((location) => (
                <SMenuItem value={location.repairShopId}>{location.name}</SMenuItem>
            ))}
        </StyledSelect>
    );
}

const StyledSelect = styled(SSelect)(({ theme }) => ({
    width: 275,

    '& .SSelect-placeholder': {
        color: 'var(--cm1)',
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
    },

    [`& .${outlinedInputClasses.notchedOutline}`]: {
        borderColor: 'var(--cm1) !important',
    },
})) as typeof SSelect;

function TemplatesSelector({
    shopId,
    value,
    onChange,
    onCreate,
}: {
    shopId: string | undefined;
    value: OrderTemplateDto | null;
    onChange: (value: OrderTemplateDto | null) => void;
    onCreate: () => void;
}) {
    const dispatch = useAppDispatch();
    useQuery(['templates', shopId ? shopId : 'enterprise'], () => {
        EnterpriseInspectionFormsApi.getTemplates(shopId).then((data) => {
            dispatch(inspectionFormSettingsActions.setTemplates(data));
            if (!value || !data.some((x) => x.id === value.id)) {
                onChange(data.length === 0 ? null : data[0]);
            }
        });
    });
    const data = useAppSelector(selectTemplates);

    const propsRef = useRef({ onChange });
    propsRef.current.onChange = onChange;

    useEffect(() => {
        if (value) {
            const valueFromList = data.find((x) => x.id === value.id);
            if (valueFromList && valueFromList !== value) {
                propsRef.current.onChange(valueFromList);
            }
        }
    }, [data, value]);

    function handleChange(event: SelectChangeEvent<number>) {
        const newTemplate = data?.find((x) => x.id === event.target.value);
        if (newTemplate) {
            onChange(newTemplate);
        }
    }

    return (
        <StyledSelect<number>
            onChange={handleChange}
            value={value?.id || ''}
            placeholder={t('commonLabels.select') + '...'}
        >
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                <Button cmosVariant="stroke" color="neutral" onClick={() => onCreate()}>
                    <PlusIcon fill="currentColor" />
                    {t('inspectionForms.templates.addButton')}
                </Button>
            </Box>
            {data?.map((template) => (
                <SMenuItem key={template.id} value={template.id}>
                    {template.name}
                </SMenuItem>
            ))}
        </StyledSelect>
    );
}

function EditTemplateName({
    createMode,
    isLoading,
    name,
    setName,
    onSave,
    showDetails,
    setShowDetails,
}: {
    createMode: boolean;
    isLoading: boolean;
    name: string | null;
    setName: (name: string) => void;
    onSave: () => void;
    showDetails: boolean;
    setShowDetails: (show: boolean) => void;
}) {
    const { t } = useAppTranslation();

    return (
        <Box sx={{ mb: 'none' }}>
            <TextFormField
                label={createMode ? '' : t('inspectionForms.templates.nameField.label')}
                placeholder={t('inspectionForms.templates.nameField.placeholder')}
                sx={{ width: 340 }}
                value={name}
                onChange={(event) => setName(event.target.value)}
                isRequired
                endAdornment={
                    <InputAdornment position="end">
                        <IconButton size="small" onClick={onSave}>
                            {isLoading ? (
                                <CircularProgress size={16} thickness={4} />
                            ) : (
                                <CheckIcon />
                            )}
                        </IconButton>
                    </InputAdornment>
                }
            />
        </Box>
    );
}
