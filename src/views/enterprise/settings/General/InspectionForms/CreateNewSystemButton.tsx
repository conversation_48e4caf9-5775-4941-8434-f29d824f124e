import { Box, IconButton, InputAdornment } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import EnterpriseInspectionFormsApi, {
    OrderSystemDto,
} from 'api/enterprise/settings/inspectionForms';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useState } from 'react';

export default function CreateNewSystemButton({
    shopId,
    templateId,
    onSystemCreated,
}: {
    shopId: string | null;
    templateId: number | undefined;
    onSystemCreated: (system: OrderSystemDto) => void;
}) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const [name, setName] = useState('');
    const [isEditorMode, setIsEditorMode] = useState(false);

    const createSystem = useMutation({
        mutationFn: async () => {
            if (!templateId) return;

            const newSystem = await EnterpriseInspectionFormsApi.createSystem(templateId, shopId, {
                name,
            });
            onSystemCreated(newSystem);
            setIsEditorMode(false);
            setName('');

            toasters.success(
                t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                t('inspectionForms.systems.notifications.successfullyUpdatedTitle')
            );
        },
        onError: (err) => {
            console.error(err);
            toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
        },
    });

    if (isEditorMode && templateId) {
        return (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TextField
                    sx={{ width: 290 }}
                    cmosVariant="roundedGrey"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder={t('inspectionForms.systems.nameField.placeholder')}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    sx={{ color: 'var(--cm1)' }}
                                    size="small"
                                    onClick={() => createSystem.mutate()}
                                    disabled={createSystem.isLoading || name.trim().length === 0}
                                >
                                    <CheckIcon fill="currentColor" />
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                />
                <IconButton
                    size="small"
                    onClick={() => {
                        setIsEditorMode(false);
                        setName('');
                    }}
                >
                    <CloseIcon />
                </IconButton>
            </Box>
        );
    }

    return (
        <Button
            disabled={!templateId}
            onClick={() => {
                setIsEditorMode(true);
                setName('');
            }}
            w="md"
        >
            {t('inspectionForms.systems.addButton')}
        </Button>
    );
}
