import { DndContext, DragEndEvent, DragOverlay } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import isEqual from 'lodash/isEqual';
import { useState } from 'react';
import { useStore } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import {
    selectSystemItems,
    swapInspectionItemThunk,
} from 'store/slices/enterprise/inspectionFormsSettings';
import InspectionItemEditor from './InspectionItemEditor';

const dropAnimationDuration = 300;

export default function InspectionItemsEditor({
    systemId,
    shopId,
}: {
    systemId: number;
    shopId: string | null;
}) {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const store = useStore();
    const itemIds = useAppSelector(
        (r) => selectSystemItems(r, systemId)?.map((x) => x.masterItemId),
        isEqual
    );
    if (!itemIds) throw new Error('system with id ' + systemId + ' not found');

    const [activeMasterItemId, setActiveMasterItemId] = useState<number | null>(null);

    return (
        <DivList>
            <DndContext
                onDragStart={(e) => setActiveMasterItemId(e.active.id as number)}
                onDragEnd={handleDragEnd}
            >
                <SortableContext strategy={verticalListSortingStrategy} items={itemIds}>
                    {itemIds.map((itemId) => (
                        <InspectionItemEditor
                            key={itemId}
                            systemId={systemId}
                            shopId={shopId}
                            masterItemId={itemId}
                        />
                    ))}
                </SortableContext>
                {activeMasterItemId && (
                    <DragOverlay
                        dropAnimation={{
                            duration: dropAnimationDuration,
                            easing: 'ease',
                        }}
                    >
                        <InspectionItemEditor
                            isDragOverlay
                            systemId={systemId}
                            shopId={shopId}
                            masterItemId={activeMasterItemId}
                        />
                    </DragOverlay>
                )}
            </DndContext>
        </DivList>
    );

    function handleDragEnd(event: DragEndEvent) {
        setTimeout(() => {
            setActiveMasterItemId(null);
        }, dropAnimationDuration);

        const { active, over } = event;
        const items = selectSystemItems(store.getState() as RootState, systemId);
        if (!items) return;
        if (over && active.id !== over.id) {
            const oldIndex = items.findIndex((x) => x.masterItemId === active.id);
            const newIndex = items.findIndex((x) => x.masterItemId === over.id);

            dispatch(
                swapInspectionItemThunk({
                    systemId,
                    shopId,
                    itemIndex: oldIndex,
                    newItemIndex: newIndex,
                })
            ).then((result) => {
                if (result.meta.requestStatus === 'fulfilled') {
                    toasters.success(
                        t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
                        t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle')
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            });
        }
    }
}

const DivList = styled('div')({
    backgroundColor: 'var(--neutral2)',
    padding: 12,
    borderRadius: 12,
});
