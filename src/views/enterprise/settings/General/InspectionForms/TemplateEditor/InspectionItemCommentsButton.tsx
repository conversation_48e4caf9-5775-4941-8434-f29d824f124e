import { IconButton } from '@mui/material';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import { useAppSelector } from 'store';
import { selectSystemItem } from 'store/slices/enterprise/inspectionFormsSettings';
import { useInspectionItemCommentsModal } from './InspectionItemCommentsModal';

export default function InspectionItemCommentsButton({
    systemId,
    masterItemId,
    shopId,
}: {
    systemId: number;
    masterItemId: number;
    shopId: string | null;
}) {
    const hasComments = useAppSelector((r) => {
        const item = selectSystemItem(r, systemId, masterItemId);
        return item ? item.commentsCount > 0 : false;
    });

    const modal = useInspectionItemCommentsModal();

    function handleClick() {
        modal.open(shopId, masterItemId);
    }

    return (
        <IconButton size="small" onClick={handleClick}>
            <CommentsIcon fill={hasComments ? 'var(--cm1)' : 'var(--neutral7)'} />
        </IconButton>
    );
}
