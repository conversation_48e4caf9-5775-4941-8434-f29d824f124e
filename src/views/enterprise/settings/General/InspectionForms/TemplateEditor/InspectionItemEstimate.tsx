import { IconButton } from '@mui/material';
import { CashIcon } from 'common/components/Icons/CashIcon';
import { useAppSelector } from 'store';
import { selectSystemItem } from 'store/slices/enterprise/inspectionFormsSettings';
import { useInspectionItemEstimateModal } from './InspectionItemEstimateModal';

export default function InspectionItemEstimate({
    shopId,
    masterItemId,
    systemId,
}: {
    shopId: string;
    masterItemId: number;
    systemId: number;
}) {
    const hasEditedEstimate = useAppSelector((r) => {
        return selectSystemItem(r, systemId, masterItemId)?.hasEditedEstimate === true;
    });
    const modal = useInspectionItemEstimateModal();

    function handleClick() {
        modal.open(shopId, systemId, masterItemId);
    }

    return (
        <IconButton size="small" onClick={handleClick}>
            <CashIcon fill={hasEditedEstimate ? 'var(--cm1)' : 'var(--neutral7)'} />
        </IconButton>
    );
}
