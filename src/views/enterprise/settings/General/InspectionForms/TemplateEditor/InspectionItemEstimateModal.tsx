import { Box, Divider, styled, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEnterpriseShopSettings } from 'api/enterprise/locations';
import EnterpriseInspectionFormsApi from 'api/enterprise/settings/inspectionForms';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { Button } from 'common/components/Button';
import { NumberField } from 'common/components/Inputs/NumberField';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { createContext, useContext, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    inspectionFormSettingsActions,
    selectSystemItem,
} from 'store/slices/enterprise/inspectionFormsSettings';
import { selectSettings } from 'store/slices/globalSettingsSlice';

export default function InspectionItemEstimateModal({
    open,
    masterItemId,
    systemId,
    shopId,
    onClose,
    onSaved,
}: {
    open: boolean;
    masterItemId: number;
    systemId: number;
    shopId: string;
    onClose: () => void;
    onSaved: () => void;
}) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { internationalization } = useAppSelector(selectSettings);
    const currencySymbol = internationalization.currency;

    const [quantity, setQuantity] = useState<number | null>(null);
    const [unitPrice, setUnitPrice] = useState<number | null>(null);
    const [hours, setHours] = useState<number | null>(null);
    const [hourPrice, setHourPrice] = useState<number | null>(null);
    const { data } = useQuery({
        enabled: !!masterItemId && open,
        queryKey: ['enterprise', 'estimate', masterItemId, shopId],
        queryFn: async () => {
            const item = await EnterpriseInspectionFormsApi.getInspectionItemEstimate(
                masterItemId,
                shopId
            );
            setQuantity(item.quantity);
            setUnitPrice(item.unitPrice);
            setHourPrice(item.hourPrice);
            setHours(item.hours);
            return item;
        },
        staleTime: 0,
        cacheTime: 0,
    });
    const hasChanges =
        !!data &&
        ((data.quantity || 0) !== (quantity || 0) ||
            (data.unitPrice || 0) !== (unitPrice || 0) ||
            (data.hourPrice || 0) !== (hourPrice || 0) ||
            (data.hours || 0) !== (hours || 0));

    const taxPercentage = useTaxPercentage(shopId);
    const item = useAppSelector((r) => selectSystemItem(r, systemId, masterItemId));

    const subtotal = (quantity || 0) * (unitPrice || 0) + (hours || 0) * (hourPrice || 0);
    const tax = (subtotal * (taxPercentage || 0)) / 100;
    const total = subtotal + tax;

    const saveChanges = useMutation({
        mutationFn: async () => {
            await EnterpriseInspectionFormsApi.updateInspectionItemEstimate(masterItemId, shopId, {
                quantity,
                unitPrice,
                hours,
                hourPrice,
            });
            dispatch(
                inspectionFormSettingsActions.setItemHasEstimate({
                    systemId,
                    masterItemId,
                    hasEstimate:
                        (quantity || 0) !== 0 ||
                        (unitPrice || 0) !== 0 ||
                        (hours || 0) !== 0 ||
                        (hourPrice || 0) !== 0,
                })
            );
            toasters.success(
                t('inspectionForms.estimateForm.successfully.text'),
                t('inspectionForms.estimateForm.successfully.title')
            );
            onSaved();
        },
    });

    return (
        <Modal
            open={open}
            boxComponent={BoxComponent}
            onClose={onClose}
            onTransitionExited={() => {
                if (!open) {
                    setQuantity(null);
                    setUnitPrice(null);
                    setHours(null);
                    setHourPrice(null);
                }
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h4Inter" color="neutral.7">
                    {t('inspectionForms.estimateForm.title', { item: item?.name })}
                </Typography>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button color="neutral" w="md" onClick={onClose}>
                        {t('commonLabels.cancel')}
                    </Button>
                    <Button
                        color="success"
                        w="md"
                        disabled={saveChanges.isLoading || !hasChanges}
                        showLoader={saveChanges.isLoading}
                        onClick={() => saveChanges.mutate()}
                    >
                        {t('commonLabels.save')}
                    </Button>
                </Box>
            </Box>

            <CssGrid>
                <Column>
                    <HorizontalField>
                        <HorizontalFieldLabel>
                            <label htmlFor="ii-estimate-quantity">
                                {t('inspectionForms.estimateForm.fieldQuantity.label')}
                            </label>
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            <NumberField
                                id="ii-estimate-quantity"
                                placeholder={t(
                                    'inspectionForms.estimateForm.fieldQuantity.placeholder'
                                )}
                                sx={{ width: 260 }}
                                value={quantity}
                                onValueChange={(v) => setQuantity(v.floatValue || null)}
                                maxLength={8}
                                decimalScale={2}
                                fixedDecimalScale
                            />
                        </HorizontalFieldValue>
                    </HorizontalField>

                    <HorizontalField>
                        <HorizontalFieldLabel>
                            <label htmlFor="ii-estimate-unit-price">
                                {t('inspectionForms.estimateForm.fieldUnitPrice.label')}
                            </label>
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            <NumberField
                                id="ii-estimate-unit-price"
                                placeholder={t(
                                    'inspectionForms.estimateForm.fieldUnitPrice.placeholder'
                                )}
                                sx={{ width: 260 }}
                                value={unitPrice}
                                onValueChange={(v) => setUnitPrice(v.floatValue || null)}
                                maxLength={8}
                                decimalScale={2}
                                fixedDecimalScale
                                template={currencySymbol}
                            />
                        </HorizontalFieldValue>
                    </HorizontalField>

                    <Divider sx={{ my: 3 }} />

                    <HorizontalField>
                        <HorizontalFieldLabel>
                            {t('inspectionForms.estimateForm.subtotal')}
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            {InternationalizationLogic.numberToCurrency(
                                internationalization,
                                subtotal,
                                { allowZero: true }
                            )}
                        </HorizontalFieldValue>
                    </HorizontalField>

                    <HorizontalField>
                        <HorizontalFieldLabel>
                            {t('inspectionForms.estimateForm.taxes')} ({taxPercentage}%)
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            {InternationalizationLogic.numberToCurrency(internationalization, tax, {
                                allowZero: true,
                            })}
                        </HorizontalFieldValue>
                    </HorizontalField>

                    <Divider sx={{ my: 3 }} />

                    <HorizontalField>
                        <HorizontalFieldLabel>
                            {t('inspectionForms.estimateForm.total')}
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            {InternationalizationLogic.numberToCurrency(
                                internationalization,
                                total,
                                { allowZero: true }
                            )}
                        </HorizontalFieldValue>
                    </HorizontalField>
                </Column>

                <Column>
                    <HorizontalField>
                        <HorizontalFieldLabel>
                            <label htmlFor="ii-estimate-hours">
                                {t('inspectionForms.estimateForm.fieldHours.label')}
                            </label>
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            <NumberField
                                id="ii-estimate-hours"
                                placeholder={t(
                                    'inspectionForms.estimateForm.fieldHours.placeholder'
                                )}
                                sx={{ width: 260 }}
                                value={hours}
                                onValueChange={(v) => setHours(v.floatValue || null)}
                                maxLength={8}
                                decimalScale={2}
                                fixedDecimalScale
                            />
                        </HorizontalFieldValue>
                    </HorizontalField>

                    <HorizontalField>
                        <HorizontalFieldLabel>
                            <label htmlFor="ii-estimate-cost-per-hour">
                                {t('inspectionForms.estimateForm.field$Hours.label')}
                            </label>
                        </HorizontalFieldLabel>
                        <HorizontalFieldValue>
                            <NumberField
                                id="ii-estimate-cost-per-hour"
                                placeholder={t(
                                    'inspectionForms.estimateForm.field$Hours.placeholder'
                                )}
                                sx={{ width: 260 }}
                                value={hourPrice}
                                onValueChange={(v) => setHourPrice(v.floatValue || null)}
                                maxLength={8}
                                decimalScale={2}
                                fixedDecimalScale
                                template={currencySymbol}
                            />
                        </HorizontalFieldValue>
                    </HorizontalField>
                </Column>
            </CssGrid>
        </Modal>
    );
}

const CssGrid = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    columnGap: '32px',
    rowGap: '16px',
});

const Column = styled('div')({
    '& > *:not(:last-child)': {
        marginBottom: '16px',
    },
});

const HorizontalField = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: 'var(--neutral8)',
}));
const HorizontalFieldLabel = styled('div')({});
const HorizontalFieldValue = styled('div')({});

function useTaxPercentage(shopId: string): number | null {
    const { data } = useEnterpriseShopSettings(shopId || '');

    if (!shopId || !data) return null;
    return data.taxPercentage;
}

const BoxComponent = styled('div')({
    padding: '40px 70px',
    width: 900,
});

const InspectionItemEstimateModalContext = createContext<{
    open: (shopId: string, systemId: number, masterItemId: number) => void;
}>({ open: () => {} });

export function useInspectionItemEstimateModal() {
    return useContext(InspectionItemEstimateModalContext);
}

export function InspectionItemEstimateModalContextProvider({
    children,
}: React.PropsWithChildren<{}>) {
    const [state, setState] = useState({
        masterItemId: 0,
        systemId: 0,
        shopId: '',
        open: false,
    });

    const ctx = useMemo(
        () => ({
            open: (shopId: string, systemId: number, masterItemId: number) => {
                setState({ shopId, masterItemId, systemId, open: true });
            },
        }),
        []
    );

    return (
        <InspectionItemEstimateModalContext.Provider value={ctx}>
            <InspectionItemEstimateModal
                onSaved={() => setState((s) => ({ ...s, open: false }))}
                onClose={() => setState((s) => ({ ...s, open: false }))}
                {...state}
            />
            {children}
        </InspectionItemEstimateModalContext.Provider>
    );
}
