import { IconButton } from '@mui/material';
import { HideIcon, ShowIcon } from 'common/components/Icons';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSystem, updateSystemThunk } from 'store/slices/enterprise/inspectionFormsSettings';

export default function SystemIsHiddenButton({
    systemId,
    shopId,
}: {
    shopId: string | null;
    systemId: number;
}) {
    const dispatch = useAppDispatch();
    const isHidden = useAppSelector((r) => selectSystem(r, systemId)?.isHidden ?? false);

    function handleToggle() {
        dispatch(
            updateSystemThunk({
                shopId,
                systemId,
                isHidden: !isHidden,
            })
        );
    }

    return (
        <IconButton size="small" onClick={handleToggle}>
            {isHidden ? <HideIcon fill="currentColor" /> : <ShowIcon />}
        </IconButton>
    );
}
