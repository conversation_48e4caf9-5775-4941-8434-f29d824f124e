import { IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { DeleteIcon } from 'common/components/Icons';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { Trans } from 'react-i18next';
import { useAppDispatch } from 'store';
import { deleteSystemThunk } from 'store/slices/enterprise/inspectionFormsSettings';

export default function SystemDeleteButton({
    shopId,
    systemId,
}: {
    shopId: string | null;
    systemId: number;
}) {
    const [isPopupOpen, setPopupOpen] = useState(false);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    const deleteSystem = useMutation({
        mutationFn: async () => {
            await dispatch(
                deleteSystemThunk({
                    shopId,
                    systemId,
                })
            );
            setPopupOpen(false);
        },
    });

    return (
        <>
            <DeleteConfirmationPopup
                open={isPopupOpen}
                title={t('inspectionForms.systems.confirmationDelete.title')}
                body={
                    <Trans
                        components={{ strong: <strong /> }}
                        i18nKey="inspectionForms.systems.confirmationDelete.body"
                    />
                }
                cancel={t('inspectionForms.systems.confirmationDelete.cancel')}
                confirm={t('inspectionForms.systems.confirmationDelete.confirm')}
                onConfirm={() => deleteSystem.mutate()}
                showLoader={deleteSystem.isLoading}
                onClose={() => setPopupOpen(false)}
            />
            <IconButton size="small" onClick={() => setPopupOpen(true)}>
                <DeleteIcon fill="currentColor" />
            </IconButton>
        </>
    );
}
