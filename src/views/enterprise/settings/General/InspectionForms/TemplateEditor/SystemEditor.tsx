import { ArrowDownward, ArrowUpward } from '@mui/icons-material';
import { CircularProgress, IconButton, InputAdornment, styled, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { EditIcon } from 'common/components/Icons';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import isEqual from 'lodash/isEqual';
import { useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    renameSystemThunk,
    selectSystem,
    swapSystemsRelativeThunk,
} from 'store/slices/enterprise/inspectionFormsSettings';
import AddItemButton from './AddItemButton';
import InspectionItemsEditor from './InspectionItemsEditor';
import SystemDeleteButton from './SystemDeleteButton';
import SystemIsHiddenButton from './SystemIsHiddenButton';

export default function SystemEditor({
    systemId,
    shopId,
    templateId,
}: {
    systemId: number;
    templateId: number;
    shopId: string | null;
}) {
    const { t } = useAppTranslation();

    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const system = useAppSelector((r) => {
        const sys = selectSystem(r, systemId);
        if (!sys) return undefined;
        const { name, index, isFirst, isLast } = sys;
        return {
            name,
            index,
            isFirst,
            isLast,
        };
    }, isEqual);

    if (!system) {
        throw new Error(`system with id ${systemId} not found`);
    }

    function handleSwapUp() {
        if (!system) throw new Error('unreachable code');
        dispatch(
            swapSystemsRelativeThunk({
                systemIndex: system.index,
                newRelativePosition: -1,
                shopId,
                templateId,
            })
        ).then((result) => {
            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.systems.notifications.successfullyUpdatedTitle')
                );
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        });
    }

    function handleSwapDown() {
        if (!system) throw new Error('unreachable code');
        dispatch(
            swapSystemsRelativeThunk({
                systemIndex: system.index,
                newRelativePosition: 1,
                shopId,
                templateId,
            })
        ).then((result) => {
            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.systems.notifications.successfullyUpdatedTitle')
                );
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        });
    }

    return (
        <DivRoot>
            <Header>
                <SystemNameEditor systemId={systemId} shopId={shopId} value={system.name} />

                <DivActions>
                    <SystemIsHiddenButton systemId={systemId} shopId={shopId} />
                    <SystemDeleteButton systemId={systemId} shopId={shopId} />
                    <IconButton size="small" disabled={system.isFirst} onClick={handleSwapUp}>
                        <ArrowUpward />
                    </IconButton>
                    <IconButton size="small" disabled={system.isLast} onClick={handleSwapDown}>
                        <ArrowDownward />
                    </IconButton>
                </DivActions>
            </Header>
            <DivInner>
                <InspectionItemsEditor systemId={systemId} shopId={shopId} />
                <AddItemButton systemId={systemId} shopId={shopId} templateId={templateId} />
            </DivInner>
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    border: '1px solid var(--neutral4)',
    borderRadius: 12,
});

const DivInner = styled('div')({
    padding: 18,
});

const Header = styled('header')({
    backgroundColor: 'var(--neutral3)',
    height: 40,
    borderRadius: '12px 12px 0 0',
    position: 'relative',
});

const DivActions = styled('div')({
    position: 'absolute',
    right: 0,
    top: 0,
    height: '100%',
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
});

function SystemNameEditor({
    value,
    shopId,
    systemId,
}: {
    value: string;
    systemId: number;
    shopId: string | null;
}) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    const [isEditing, setEditing] = useState(false);
    const [editingValue, setEditingValue] = useState(value);

    function startEditing() {
        setEditingValue(value);
        setEditing(true);
    }

    function stopEditing() {
        setEditing(false);
    }

    const updateName = useMutation({
        mutationFn: async () => {
            const result = await dispatch(
                renameSystemThunk({
                    shopId,
                    systemId,
                    name: editingValue.trim(),
                })
            );

            if (result.meta.requestStatus === 'rejected') {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
                throw result.payload;
            } else {
                toasters.success(
                    t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.systems.notifications.successfullyUpdatedTitle')
                );
            }
        },
    });

    function handleUpdateName() {
        const newName = editingValue.trim();
        if (newName === '') return;

        if (value !== newName) {
            updateName.mutateAsync().then(() => {
                setEditing(false);
            });
        } else {
            setEditing(false);
        }
    }

    if (isEditing) {
        return (
            <SystemNameEditorRoot>
                <TextField
                    cmosVariant="roundedPrimary"
                    sx={{ width: '260px' }}
                    value={editingValue}
                    onChange={(e) => setEditingValue(e.target.value)}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    disabled={updateName.isLoading}
                                    onClick={handleUpdateName}
                                    size="small"
                                >
                                    {!updateName.isLoading ? (
                                        <CheckIcon />
                                    ) : (
                                        <CircularProgress size={16} thickness={5} />
                                    )}
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                />
                <IconButton onClick={stopEditing} size="small">
                    <CloseIcon fill="currentColor" />
                </IconButton>
            </SystemNameEditorRoot>
        );
    } else {
        return (
            <SystemNameEditorRoot>
                <Typography variant="h5Roboto">
                    {t('inspectionForms.normalView.system')} {value}{' '}
                    <IconButton onClick={startEditing} className="edit-button" size="small">
                        <EditIcon fill="currentColor" />
                    </IconButton>
                </Typography>
            </SystemNameEditorRoot>
        );
    }
}

const SystemNameEditorRoot = styled('div')({
    height: '100%',
    paddingLeft: 12,
    display: 'flex',
    alignItems: 'center',

    '& .edit-button': {
        opacity: 0.3,
        transition: '0.3s opacity',
    },

    '&:hover .edit-button': {
        opacity: 1,
    },
});
