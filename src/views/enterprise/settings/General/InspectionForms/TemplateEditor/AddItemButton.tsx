import { Box, IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { isAxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useState } from 'react';
import { hasCode, isErrorResponse } from 'services/Server';
import { useAppDispatch } from 'store';
import { createInspectionItemThunk } from 'store/slices/enterprise/inspectionFormsSettings';
import InspectionItemsAutocomplete from 'views/Components/InspectionItemsAutocomplete';

export default function AddItemButton({
    systemId,
    templateId,
    shopId,
}: {
    systemId: number;
    templateId: number;
    shopId: string | null;
}) {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const [isEditing, setIsEditing] = useState(false);

    const addItem = useMutation({
        mutationFn: async (name: string) => {
            const nameTrimmed = name.trim();
            if (nameTrimmed.length === 0) return;

            const result = await dispatch(
                createInspectionItemThunk({
                    systemId,
                    shopId,
                    name: nameTrimmed,
                })
            );
            if (result.meta.requestStatus === 'rejected') {
                throw result.payload;
            }
            setIsEditing(false);
            toasters.success(
                t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
                t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle')
            );
        },
        onError(error) {
            if (isAxiosError(error) && isErrorResponse(error.response?.data)) {
                if (hasCode(error.response.data, 'General.DuplicatedInspectionItem')) {
                    toasters.danger(
                        t('inspectionForms.templateItems.nameDuplicatedError'),
                        t('toasters.errorOccurred')
                    );
                }
            }
        },
    });

    return (
        <Box sx={{ display: 'flex', gap: '5px', alignItems: 'center', mt: 1, height: 48 }}>
            {isEditing ? (
                <>
                    <InspectionItemsAutocomplete
                        templateId={templateId}
                        isLoading={addItem.isLoading}
                        onChange={(value) => addItem.mutateAsync(value)}
                        styles={{ input: { width: 290 } }}
                    />
                    <IconButton
                        size="small"
                        onClick={() => {
                            setIsEditing(false);
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </>
            ) : (
                <Button onClick={() => setIsEditing(true)} cmosVariant="stroke" color="neutral">
                    {t('inspectionForms.templateItems.addButton')}
                    <PlusIcon fill="currentColor" />
                </Button>
            )}
        </Box>
    );
}
