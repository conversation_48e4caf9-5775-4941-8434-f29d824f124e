import { IconButton } from '@mui/material';
import { HideIcon, ShowIcon } from 'common/components/Icons';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useAppDispatch, useAppSelector } from 'store';
import {
    selectSystemItem,
    updateInspectionItemThunk,
} from 'store/slices/enterprise/inspectionFormsSettings';

export default function InspectionItemVisibleToggle({
    systemId,
    masterItemId,
    shopId,
}: {
    masterItemId: number;
    systemId: number;
    shopId: string | null;
}) {
    const dispatch = useAppDispatch();
    const isHidden = useAppSelector(
        (r) => selectSystemItem(r, systemId, masterItemId)?.isHidden ?? false
    );
    const toasters = useToasters();
    const { t } = useAppTranslation();

    function toggle() {
        dispatch(
            updateInspectionItemThunk({ shopId, masterItemId, isHidden: !isHidden, systemId })
        ).then((result) => {
            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle')
                );
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        });
    }

    return (
        <IconButton size="small" onClick={toggle}>
            {isHidden ? <HideIcon fill="var(--neutral7)" /> : <ShowIcon />}
        </IconButton>
    );
}
