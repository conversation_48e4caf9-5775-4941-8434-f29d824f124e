import { IconButton } from '@mui/material';
import { DetailsIcon } from 'common/components/Icons/DetailsIcon';
import { useAppSelector } from 'store';
import { selectSystemItem } from 'store/slices/enterprise/inspectionFormsSettings';
import { useInspectionItemDetailsModal } from './InspectionItemDetailsModal';

export default function InspectionItemDetails({
    shopId,
    masterItemId,
    systemId,
}: {
    shopId: string | null;
    masterItemId: number;
    systemId: number;
}) {
    const hasDetails = useAppSelector((r) => {
        return !!selectSystemItem(r, systemId, masterItemId)?.details;
    });
    const modal = useInspectionItemDetailsModal();

    function handleClick() {
        modal.open(shopId, systemId, masterItemId);
    }

    return (
        <IconButton size="small" onClick={handleClick}>
            <DetailsIcon fill={hasDetails ? 'var(--cm1)' : 'var(--neutral7)'} />
        </IconButton>
    );
}
