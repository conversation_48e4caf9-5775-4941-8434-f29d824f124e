import { Box, styled, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { Button } from 'common/components/Button';
import InfoText from 'common/components/InfoText';
import { TextArea } from 'common/components/Inputs';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    selectSystemItem,
    updateInspectionItemThunk,
} from 'store/slices/enterprise/inspectionFormsSettings';

export default function InspectionItemDetailsModal({
    open,
    masterItemId,
    systemId,
    shopId,
    onClose,
    onSaved,
}: {
    open: boolean;
    masterItemId: number;
    systemId: number;
    shopId: string | null;
    onClose: () => void;
    onSaved: () => void;
}) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const detailsValue = useAppSelector(
        (r) => selectSystemItem(r, systemId, masterItemId)?.details
    );

    const [details, setDetails] = useState('');
    const hasChanges = details !== (detailsValue || '');

    useEffect(() => {
        if (detailsValue) {
            setDetails(detailsValue);
        }
    }, [detailsValue]);

    const saveChanges = useMutation({
        mutationFn: async () => {
            const result = await dispatch(
                updateInspectionItemThunk({
                    masterItemId,
                    shopId,
                    details,
                    systemId,
                })
            );

            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.detailsForm.successfully.text'),
                    t('inspectionForms.detailsForm.successfully.title')
                );
                onSaved();
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        },
    });

    return (
        <Modal
            open={open}
            boxComponent={BoxComponent}
            onClose={onClose}
            onTransitionExited={() => {
                if (!open) {
                    setDetails(detailsValue || '');
                }
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h4Inter" color="neutral.7">
                    {t('inspectionForms.detailsForm.title')}
                </Typography>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button color="neutral" w="md" onClick={onClose}>
                        {t('commonLabels.cancel')}
                    </Button>
                    <Button
                        color="success"
                        w="md"
                        disabled={saveChanges.isLoading || !hasChanges}
                        showLoader={saveChanges.isLoading}
                        onClick={() => saveChanges.mutate()}
                    >
                        {t('commonLabels.save')}
                    </Button>
                </Box>
            </Box>

            <TextArea
                label={t('inspectionForms.detailsForm.label')}
                placeholder={t('inspectionForms.detailsForm.placeholder')}
                value={details}
                onChange={(e) => setDetails(e.target.value)}
                rows={5}
            />
            <InfoText sx={{ mt: 2 }}>{t('inspectionForms.detailsForm.starttyping')}</InfoText>
        </Modal>
    );
}

const BoxComponent = styled('div')({
    padding: '40px 70px',
    width: 900,
});

const InspectionItemDetailsModalContext = createContext<{
    open: (shopId: string | null, systemId: number, masterItemId: number) => void;
}>({ open: () => {} });

export function useInspectionItemDetailsModal() {
    return useContext(InspectionItemDetailsModalContext);
}

export function InspectionItemDetailsModalContextProvider({
    children,
}: React.PropsWithChildren<{}>) {
    const [state, setState] = useState({
        masterItemId: 0,
        systemId: 0,
        shopId: null as string | null,
        open: false,
    });

    const ctx = useMemo(
        () => ({
            open: (shopId: string | null, systemId: number, masterItemId: number) => {
                setState({ shopId, masterItemId, systemId, open: true });
            },
        }),
        []
    );

    return (
        <InspectionItemDetailsModalContext.Provider value={ctx}>
            <InspectionItemDetailsModal
                onSaved={() => setState((s) => ({ ...s, open: false }))}
                onClose={() => setState((s) => ({ ...s, open: false }))}
                {...state}
            />
            {children}
        </InspectionItemDetailsModalContext.Provider>
    );
}
