import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import EnterpriseInspectionFormsApi, {
    OrderTemplateDto,
} from 'api/enterprise/settings/inspectionForms';
import isEqual from 'lodash/isEqual';
import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    inspectionFormSettingsActions,
    selectSystems,
} from 'store/slices/enterprise/inspectionFormsSettings';
import { useHeaderLoading } from 'views/HeaderBar';
import SystemEditor from './SystemEditor';

export default function TemplateEditor({
    template,
    shopId,
}: {
    template: OrderTemplateDto;
    shopId: string | null;
}) {
    const dispatch = useAppDispatch();

    const { data, isLoading } = useQuery({
        queryKey: ['enterprise', 'template', template.id, 'systems'],
        queryFn: () => EnterpriseInspectionFormsApi.getSystems(template.id),
    });

    useHeaderLoading(isLoading);

    useEffect(() => {
        if (data) {
            dispatch(inspectionFormSettingsActions.setSystems(data));
        }
    }, [data, dispatch]);

    const systemIds = useAppSelector((r) => selectSystems(r).map((x) => x.id), isEqual);

    return (
        <DivRoot>
            {systemIds.map((systemId) => (
                <SystemEditor
                    key={systemId}
                    templateId={template.id}
                    systemId={systemId}
                    shopId={shopId}
                />
            ))}
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    '& > *:not(:last-child)': {
        marginBottom: 12,
    },
});
