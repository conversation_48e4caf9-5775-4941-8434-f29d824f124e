import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { IconButton, InputAdornment, outlinedInputClasses, styled } from '@mui/material';
import { isAxiosError } from 'axios';
import { EditIcon } from 'common/components/Icons';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { rgba } from 'common/styles/ColorHelpers';
import isEqual from 'lodash/isEqual';
import React, { useState } from 'react';
import { hasCode, isErrorResponse } from 'services/Server';
import { useAppDispatch, useAppSelector } from 'store';
import {
    selectSystemItem,
    updateInspectionItemThunk,
} from 'store/slices/enterprise/inspectionFormsSettings';
import InspectionItemCommentsButton from './InspectionItemCommentsButton';
import InspectionItemDeleteButton from './InspectionItemDeleteButton';
import InspectionItemDetails from './InspectionItemDetails';
import InspectionItemEstimate from './InspectionItemEstimate';
import InspectionItemVisibleToggle from './InspectionItemVisibleToggle';

export default function InspectionItemEditor({
    masterItemId,
    systemId,
    isDragOverlay = false,
    shopId,
}: {
    masterItemId: number;
    systemId: number;
    isDragOverlay?: boolean;
    shopId: string | null;
}) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
        id: masterItemId,
    });

    const { t } = useAppTranslation();
    const toasters = useToasters();
    const dispatch = useAppDispatch();
    const item = useAppSelector((r) => selectSystemItem(r, systemId, masterItemId), isEqual);

    const style: React.CSSProperties = {
        transform: CSS.Transform.toString(transform),
        transition,
        ...(isDragging
            ? {
                  visibility: 'hidden',
                  pointerEvents: 'none',
                  userSelect: 'none',
              }
            : undefined),
    };

    const [isEditing, setEditing] = useState(false);
    const [nameState, setNameState] = useState('');

    function handleEnterEditing() {
        if (!item) return;
        setNameState(item.name);
        setEditing(true);
    }

    function handleExitEditing() {
        if (!item) return;
        setEditing(false);
        setNameState(item.name);
    }

    function updateName() {
        if (!isEditing) return;
        const trimmedName = nameState.trim();
        if (trimmedName === item?.name || trimmedName === '') return;
        setEditing(false);
        dispatch(
            updateInspectionItemThunk({
                shopId,
                masterItemId,
                name: trimmedName,
                systemId,
            })
        ).then((result) => {
            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle')
                );
            } else {
                const error = result.payload;

                if (isAxiosError(error) && isErrorResponse(error.response?.data)) {
                    if (hasCode(error.response.data, 'General.DuplicatedInspectionItem')) {
                        toasters.danger(
                            t('inspectionForms.templateItems.nameDuplicatedError'),
                            t('toasters.errorOccurred')
                        );
                    } else {
                        toasters.danger(
                            t('toasters.errorOccurredWhenSaving'),
                            t('toasters.errorOccurred')
                        );
                    }
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            }
        });
    }

    if (!item) return null;

    return (
        <DivRoot ref={setNodeRef} style={style} {...attributes}>
            <Container focused={isEditing}>
                <DivHandle {...listeners}>
                    <DragAndDropIcon size={25} fill="var(--neutral7)" />
                </DivHandle>
                {isEditing ? (
                    <StyledTextField
                        value={nameState}
                        onChange={(e) => setNameState(e.target.value)}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton size="small" onClick={updateName}>
                                        <CheckIcon />
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                    />
                ) : (
                    <ItemName>{item.name}</ItemName>
                )}
            </Container>
            {!isDragOverlay && (
                <DivActions>
                    {isEditing ? (
                        <IconButton size="small" onClick={handleExitEditing}>
                            <CloseIcon />
                        </IconButton>
                    ) : (
                        <IconButton size="small" onClick={handleEnterEditing}>
                            <EditIcon />
                        </IconButton>
                    )}
                    <InspectionItemCommentsButton
                        systemId={systemId}
                        masterItemId={masterItemId}
                        shopId={shopId}
                    />
                    <InspectionItemDetails
                        shopId={shopId}
                        systemId={systemId}
                        masterItemId={masterItemId}
                    />
                    {shopId && (
                        <InspectionItemEstimate
                            shopId={shopId}
                            systemId={systemId}
                            masterItemId={masterItemId}
                        />
                    )}
                    <InspectionItemVisibleToggle
                        shopId={shopId}
                        systemId={systemId}
                        masterItemId={masterItemId}
                    />
                    <InspectionItemDeleteButton
                        shopId={shopId}
                        masterItemId={masterItemId}
                        systemId={systemId}
                    />
                </DivActions>
            )}
        </DivRoot>
    );
}

const ItemName = styled('div')(({ theme }) => ({
    paddingLeft: 14,
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
}));

const StyledTextField = styled(TextField)({
    alignSelf: 'stretch',
    width: '100%',
    borderRadius: '10px',
    overflow: 'hidden',
    height: 'calc(var(--ii-height) - 2px)',

    [`& .${outlinedInputClasses.notchedOutline}`]: {
        border: 'none',
    },
});

const Container = styled('div')<{ focused: boolean }>(({ theme, focused }) => ({
    display: 'flex',
    alignItems: 'center',
    background: theme.palette.background.paper,
    width: '100%',
    height: '100%',
    borderRadius: 10,
    border: `1px solid ${focused ? theme.palette.primary.main : theme.palette.neutral[5]}`,
    boxShadow: focused ? `0 0 0 2px ${rgba(theme.palette.primary.main, 0.3)}` : undefined,
}));

const DivHandle = styled('button')({
    padding: 5,
    borderRadius: 8,
    marginLeft: 5,
    cursor: 'grab',
    border: 'none',
    backgroundColor: 'transparent',
    transition: 'transform 0.3s, background-color 0.3s',

    '&:hover': {
        backgroundColor: 'var(--neutral3)',
    },
});

const DivRoot = styled('div')({
    '--ii-height': '44px',
    height: 'var(--ii-height)',
    margin: '4px 0',
    display: 'grid',
    gridTemplateColumns: `1fr ${32 * 6 + 12}px`,
});

const DivActions = styled('div')({
    display: 'grid',
    gridTemplateColumns: 'repeat(6, 32px)',
    marginLeft: 8,
    height: 32,
    alignSelf: 'center',
});
