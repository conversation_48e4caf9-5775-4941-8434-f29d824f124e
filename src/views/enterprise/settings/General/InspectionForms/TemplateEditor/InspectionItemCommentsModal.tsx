import { Box, Collapse, Divider, styled, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import EnterpriseInspectionFormsApi, {
    OrderTechnicianCommentDto,
} from 'api/enterprise/settings/inspectionForms';
import { Button } from 'common/components/Button';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { createContext, useContext, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { useAppDispatch } from 'store';
import { updateTechnicianCommentsThunk } from 'store/slices/enterprise/inspectionFormsSettings';
import TechnicianCommentsEditor, { TechnicianCommentChange } from './TechnicianCommentsEditor';

export default function InspectionItemCommentsModal({
    open,
    masterItemId,
    shopId,
    onClose,
    onSaved,
}: {
    open: boolean;
    masterItemId: number;
    shopId: string | null;
    onClose: () => void;
    onSaved: () => void;
}) {
    const { t } = useAppTranslation();
    const [showTips, setShowTips] = useState(false);
    const [showFillInBlankTips, setShowFillInBlankTips] = useState(false);
    const [originalComments, setOriginalComments] = useState<OrderTechnicianCommentDto[]>([]);
    const dispatch = useAppDispatch();
    const toasters = useToasters();

    useQuery({
        queryKey: ['enterprise', 'technician-comments', masterItemId, shopId],
        queryFn: async () => {
            const data = await EnterpriseInspectionFormsApi.getTechnicianComments(
                masterItemId,
                shopId
            );
            setOriginalComments(data);
        },
        staleTime: 0,
        cacheTime: 0,
        enabled: open,
    });

    const saveChanges = useMutation({
        mutationFn: async () => {
            const newComments: string[] = [];
            const deletedIds: number[] = [];

            for (const change of changes) {
                if (change.type === 'delete') {
                    deletedIds.push(change.id);
                } else {
                    newComments.push(change.name);
                }
            }

            const result = await dispatch(
                updateTechnicianCommentsThunk({
                    masterItemId,
                    shopId,
                    request: {
                        delete: deletedIds,
                        newComments,
                    },
                })
            );

            if (result.meta.requestStatus === 'fulfilled') {
                toasters.success(
                    t('inspectionForms.commentsForm.successfully.text'),
                    t('inspectionForms.commentsForm.successfully.title')
                );
                onSaved();
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        },
    });

    const [fillInTheBlankComments, setFillInTheBlankComments] = useState(0);
    const [changes, setChanges] = useState<TechnicianCommentChange[]>([]);

    return (
        <Modal open={open} boxComponent={BoxComponent} onClose={onClose}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h4Inter" color="neutral.7">
                    {t('inspectionForms.commentsForm.title')}
                </Typography>

                <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button color="neutral" w="md" onClick={onClose}>
                        {t('commonLabels.cancel')}
                    </Button>
                    <Button
                        color="success"
                        w="md"
                        disabled={changes.length === 0 || saveChanges.isLoading}
                        showLoader={saveChanges.isLoading}
                        onClick={() => saveChanges.mutate()}
                    >
                        {t('commonLabels.saveChanges')}
                    </Button>
                </Box>
            </Box>

            <InputWrapper label={t('inspectionForms.commentsForm.label')} fullWidth>
                <TechnicianCommentsEditor
                    onChanges={setChanges}
                    onFillInTheBlankCountChange={setFillInTheBlankComments}
                    comments={originalComments}
                />
            </InputWrapper>
            <Box sx={{ mt: 2 }}>
                <LabeledValue>
                    <b>{t('inspectionForms.commentsForm.note')}</b>
                    <div>
                        <span>{t('inspectionForms.commentsForm.starttyping')}</span> <br />
                        <ShowMoreButton
                            cmosVariant="typography"
                            onClick={() => setShowTips((x) => !x)}
                        >
                            {showTips
                                ? t('inspectionForms.commentsForm.hideInfo')
                                : t('inspectionForms.commentsForm.moreInfo')}
                        </ShowMoreButton>
                        <Collapse in={showTips} sx={{ color: 'var(--neutral8)' }}>
                            <h2>{t('inspectionForms.commentsForm.tipsCustomComments')}</h2>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.addCustomCommentsInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.deleteCustomCommentsInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.InstantSynchronizationInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.officialCommentsInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                        </Collapse>
                    </div>
                </LabeledValue>
                <Divider sx={{ my: 1.5 }} />
                <LabeledValue>
                    <PlaceholderCommentsCount>{fillInTheBlankComments}</PlaceholderCommentsCount>
                    <div>
                        <span>{t('inspectionForms.commentsForm.fillInTheBlankInfo')}</span> <br />
                        <ShowMoreButton
                            cmosVariant="typography"
                            onClick={() => setShowFillInBlankTips((x) => !x)}
                        >
                            {showFillInBlankTips
                                ? t('inspectionForms.commentsForm.hideInfo')
                                : t('inspectionForms.commentsForm.moreInfo')}
                        </ShowMoreButton>
                        <Collapse in={showFillInBlankTips} sx={{ color: 'var(--neutral8)' }}>
                            <h2>{t('inspectionForms.commentsForm.tipsStyleComments')}</h2>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.addStyleCommentsInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.deleteStyleCommentsInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.InstantSynchronizationInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                            <p>
                                <Trans
                                    i18nKey="inspectionForms.commentsForm.commentsTypeCaptureInformationInfo"
                                    components={{ b: <b /> }}
                                />
                            </p>
                        </Collapse>
                    </div>
                </LabeledValue>
            </Box>
        </Modal>
    );
}

const ShowMoreButton = styled(Button)({
    padding: '0px !important',
    borderRadius: '5px !important',
});

const BoxComponent = styled('div')({
    padding: '40px 70px',
    width: 900,
});

const LabeledValue = styled('div')({
    display: 'grid',
    gridTemplateColumns: '34px 1fr',
    columnGap: '5px',
    color: 'var(--neutral7)',
});

const PlaceholderCommentsCount = styled('span')({
    backgroundColor: 'var(--yellow)',
    display: 'inline-flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: 'bold',
    fontSize: '0.8rem',
    height: '1.5em',
    width: '1.5em',
    borderRadius: '100px',
    color: '#fff',
});

const InspectionItemCommentsModalContext = createContext<{
    open: (shopId: string | null, masterItemId: number) => void;
}>({ open: () => {} });

export function useInspectionItemCommentsModal() {
    return useContext(InspectionItemCommentsModalContext);
}

export function InspectionItemCommentsModalContextProvider({
    children,
}: React.PropsWithChildren<{}>) {
    const [state, setState] = useState({
        masterItemId: 0,
        shopId: null as string | null,
        open: false,
    });

    const ctx = useMemo(
        () => ({
            open: (shopId: string | null, masterItemId: number) => {
                setState({ shopId, masterItemId, open: true });
            },
        }),
        []
    );

    return (
        <InspectionItemCommentsModalContext.Provider value={ctx}>
            <InspectionItemCommentsModal
                onSaved={() => setState((s) => ({ ...s, open: false }))}
                onClose={() => setState((s) => ({ ...s, open: false }))}
                {...state}
            />
            {children}
        </InspectionItemCommentsModalContext.Provider>
    );
}
