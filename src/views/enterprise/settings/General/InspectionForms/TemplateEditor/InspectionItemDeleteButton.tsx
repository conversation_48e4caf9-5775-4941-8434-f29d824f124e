import { IconButton } from '@mui/material';
import { DeleteIcon } from 'common/components/Icons';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useAppDispatch } from 'store';
import { deleteInspectionItemThunk } from 'store/slices/enterprise/inspectionFormsSettings';

export default function InspectionItemDeleteButton({
    masterItemId,
    shopId,
    systemId,
}: {
    masterItemId: number;
    shopId: string | null;
    systemId: number;
}) {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const [popupOpen, setPopupOpen] = useState(false);

    function deleteItem() {
        dispatch(deleteInspectionItemThunk({ shopId, masterItemId, systemId }));
        setPopupOpen(false);
    }

    return (
        <>
            <IconButton size="small" onClick={() => setPopupOpen(true)}>
                <DeleteIcon fill="var(--neutral7)" />
            </IconButton>
            <DeleteConfirmationPopup
                open={popupOpen}
                title={t('inspectionForms.templateItems.confirmationDelete.title')}
                body={t('inspectionForms.templateItems.confirmationDelete.body')}
                cancel={t('inspectionForms.templateItems.confirmationDelete.cancel')}
                confirm={t('inspectionForms.templateItems.confirmationDelete.confirm')}
                onConfirm={deleteItem}
                onClose={() => setPopupOpen(false)}
            />
        </>
    );
}
