import { Card, Popper, styled } from '@mui/material';
import { OrderTechnicianCommentDto } from 'api/enterprise/settings/inspectionForms';
import clsx from 'clsx';
import TagsInput, { Tag } from 'common/components/Inputs/TagsInput';
import { SMenuItem } from 'common/components/mui';
import { ZLayer } from 'common/styles/ZLayer';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDebounce } from 'use-debounce';
import hasPlaceholder, { validatePlaceholder } from './technicianCommentsUtil';

export type TechnicianCommentChange =
    | {
          type: 'delete';
          id: number;
      }
    | {
          type: 'add';
          name: string;
      };

export type TechnicianCommentsEditorProps = {
    onFillInTheBlankCountChange: (count: number) => void;
    comments: OrderTechnicianCommentDto[];
    onChanges: (changes: TechnicianCommentChange[]) => void;
};

type CommentState = {
    // if the comment is new then this is null
    comment: OrderTechnicianCommentDto | null;
    // will be deleted on save
    isDeleted: boolean;
    // name of the comment
    name: string;
};

export default function TechnicianCommentsEditor({
    comments,
    onChanges,
    onFillInTheBlankCountChange,
}: TechnicianCommentsEditorProps) {
    const [commentState, setCommentState] = useState<CommentState[]>([]);
    const propsRef = useRef({ onFillInTheBlankCountChange, onChanges });
    propsRef.current.onChanges = onChanges;
    propsRef.current.onFillInTheBlankCountChange = onFillInTheBlankCountChange;
    const rootRef = useRef<HTMLDivElement | null>(null);
    const inputRef = useRef<HTMLInputElement | null>(null);

    const [isFocusedValue, setIsFocused] = useState(false);
    const [isFocused] = useDebounce(isFocusedValue, 100);

    useEffect(() => {
        setCommentState(
            comments.map((c) => ({
                comment: c,
                isDeleted: false,
                name: c.name,
            }))
        );
    }, [comments]);

    const fillInTheBlankCount = useMemo(() => {
        let count = 0;
        for (const comment of commentState) {
            if (hasPlaceholder(comment.name)) {
                count++;
            }
        }
        return count;
    }, [commentState]);

    const changes = useMemo(() => {
        const changes: TechnicianCommentChange[] = [];

        for (const comment of commentState) {
            if (comment.isDeleted && comment.comment) {
                changes.push({ type: 'delete', id: comment.comment.id });
            } else if (comment.comment === null && !comment.isDeleted) {
                changes.push({ type: 'add', name: comment.name });
            }
        }

        return changes;
    }, [commentState]);

    const deletedComments = useMemo(() => commentState.filter((c) => c.isDeleted), [commentState]);

    useEffect(() => {
        propsRef.current.onFillInTheBlankCountChange(fillInTheBlankCount);
    }, [fillInTheBlankCount]);

    useEffect(() => {
        propsRef.current.onChanges(changes);
    }, [changes]);

    function removeItem(name: string) {
        setCommentState((state) => {
            const itemIndex = state.findIndex((c) => c.name === name);
            if (itemIndex === -1) return state;
            const newState = [...state];
            newState[itemIndex] = { ...newState[itemIndex], isDeleted: true };
            return newState;
        });
    }

    function onAdded(name: string) {
        if (commentState.some((x) => x.name === name)) return;
        setCommentState((state) => [
            ...state,
            {
                comment: null,
                isDeleted: false,
                name,
            },
        ]);
    }

    function restoreItem(name: string) {
        setCommentState((state) => {
            const itemIndex = state.findIndex((c) => c.name === name);
            if (itemIndex === -1) return state;
            const newState = [...state];
            newState[itemIndex] = { ...newState[itemIndex], isDeleted: false };
            return newState;
        });
    }

    function handleKeyDown(e: React.KeyboardEvent<HTMLInputElement>) {
        if (e.key === '_') {
            const el = e.target as HTMLInputElement;
            if (el.selectionStart !== null) {
                const newValue =
                    el.value.substring(0, el.selectionStart) +
                    '_' +
                    el.value.substring(el.selectionStart);
                if (!validatePlaceholder(newValue)) {
                    e.preventDefault();
                }
            }
        }
    }

    return (
        <TagsInput
            sx={{ width: '100%' }}
            ref={rootRef}
            onAdded={onAdded}
            slotProps={{
                input: {
                    onBlur: () => setIsFocused(false),
                    onFocus: () => setIsFocused(true),
                    onKeyDown: handleKeyDown,
                },
            }}
            inputRef={inputRef}
        >
            {commentState
                .filter((x) => !x.isDeleted)
                .map((comment) => (
                    <StyledTag
                        className={clsx({
                            'has-placeholder': hasPlaceholder(comment.name),
                        })}
                        key={comment.name}
                        onRemove={() => {
                            if (isFocused) {
                                window.requestAnimationFrame(() => {
                                    inputRef.current?.focus();
                                });
                            }
                            removeItem(comment.name);
                        }}
                        label={comment.name}
                    />
                ))}
            <Popper
                disablePortal
                anchorEl={rootRef.current}
                open={deletedComments.length > 0 && isFocused}
                placement="bottom-start"
                style={{ zIndex: ZLayer.menu, width: '100%' }}
                modifiers={[
                    {
                        name: 'offset',
                        options: { offset: [0, 5] },
                    },
                ]}
            >
                <DropdownCard>
                    {deletedComments.map((comment) => (
                        <StyledMenuItem
                            key={comment.name}
                            onMouseDown={() => {
                                restoreItem(comment.name);
                                window.requestAnimationFrame(() => {
                                    inputRef.current?.focus();
                                });
                            }}
                        >
                            {comment.name}
                        </StyledMenuItem>
                    ))}
                </DropdownCard>
            </Popper>
        </TagsInput>
    );
}

const StyledTag = styled(Tag)({
    '&.has-placeholder': {
        border: '1px solid var(--yellow)',
    },
});

const StyledMenuItem = styled(SMenuItem)({
    height: '42px',
});

const DropdownCard = styled(Card)({
    borderRadius: 4,
});
