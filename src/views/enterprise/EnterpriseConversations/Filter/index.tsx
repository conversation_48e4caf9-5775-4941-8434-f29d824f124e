import { styled } from '@mui/material';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import ConversationLocationSelector from 'common/components/LocationSelector/ConversationLocationSelector';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/enterprise/conversationsSlice';
import { selectConversationsFilters } from 'store/slices/enterprise/conversationsSlice/selectors';
import fetchConversations from 'store/slices/enterprise/conversationsSlice/thunks/fetchConversations';
import { FilterButton } from 'views/Conversations/Filter/FilterButton';
import MutePreferencesButton from 'views/Conversations/MutePreferencesButton';

type TrayOption = 'both' | 'advisor' | 'chatBot';
type ActiveOption = 'active' | 'closed';

type FilterProps = {
    shouldResetFilter: boolean;
};

export const Filter = ({ shouldResetFilter }: FilterProps) => {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const conversationFilter = useAppSelector(selectConversationsFilters);
    const showFilters = useMemo(
        () => conversationFilter.showFilter,
        [conversationFilter.showFilter]
    );
    const setShowFilters = (value: boolean) => {
        dispatch(setFilters({ ...conversationFilter, showFilter: value }));
    };

    const onFilterChanged = useCallback(() => {
        dispatch(fetchConversations());
    }, [dispatch]);

    const onFilterChangedDebounced = useMemo(
        () => debounce(onFilterChanged, 250),
        [onFilterChanged]
    );

    useEffect(() => {
        onFilterChangedDebounced();
    }, [
        conversationFilter.locations,
        conversationFilter.active,
        conversationFilter.inbox,
        conversationFilter.searchTerm,
        onFilterChangedDebounced,
    ]);

    useEffect(() => {
        if (shouldResetFilter) {
            dispatch(
                setFilters({
                    locations: [],
                    inbox: 'both',
                    active: 'active',
                    showFilter: false,
                    searchTerm: '',
                })
            );
        }
    }, [dispatch, shouldResetFilter]);

    const trayOptions = useMemo<OptionData<TrayOption>[]>(
        () => [
            { label: t('conversations.filter.bothInboxes'), value: 'both' },
            { label: t('conversations.filter.advisorInbox'), value: 'advisor' },
            { label: t('conversations.filter.chatbotInbox'), value: 'chatBot' },
        ],
        [t]
    );

    const trayFilter = useMemo(() => {
        const selected =
            trayOptions.find((opt) => opt.value === conversationFilter.inbox) || trayOptions[0];
        return selected;
    }, [trayOptions, conversationFilter]);

    const setTrayFilter = (value: OptionData<TrayOption>) => {
        dispatch(setFilters({ ...conversationFilter, inbox: value.value }));
    };

    const activeOptions = useMemo<OptionData<ActiveOption>[]>(
        () => [
            { label: t('conversations.filter.active'), value: 'active' },
            { label: t('conversations.filter.closed'), value: 'closed' },
        ],
        [t]
    );

    const activeFilter = useMemo(() => {
        const selected =
            activeOptions.find((opt) => opt.value === conversationFilter.active) ||
            activeOptions[0];

        return selected;
    }, [activeOptions, conversationFilter]);

    const setActiveFilter = (value: OptionData<ActiveOption>) => {
        dispatch(setFilters({ ...conversationFilter, active: value.value }));
    };

    const setLocationsFilter = (value: string[]) => {
        dispatch(setFilters({ ...conversationFilter, locations: value }));
    };

    return (
        <Root>
            {showFilters && (
                <>
                    <Dropdown
                        name={'tray'}
                        cmosVariant="roundedPrimary"
                        slotProps={{
                            inputWrapper: {
                                sx: { width: 200 },
                            },
                        }}
                        options={trayOptions}
                        value={trayFilter}
                        onChange={(option) => option && setTrayFilter(option)}
                        isSearchable={false}
                    />
                    {trayFilter.value === 'advisor' && (
                        <Dropdown
                            name={'active'}
                            cmosVariant="roundedGrey"
                            slotProps={{
                                inputWrapper: {
                                    sx: { width: 200 },
                                },
                            }}
                            options={activeOptions}
                            value={activeFilter}
                            onChange={(option) => option && setActiveFilter(option)}
                            isSearchable={false}
                        />
                    )}
                    <ConversationLocationSelector
                        value={conversationFilter.locations}
                        useOptionsDividers
                        onChange={(v) => {
                            setLocationsFilter(v);
                        }}
                    />
                    <HideFiltersButton onClick={() => setShowFilters(false)}>
                        {t('conversations.filter.hideFilters')}
                    </HideFiltersButton>
                </>
            )}
            {!showFilters && <FilterButton onClick={() => setShowFilters(true)} />}
            <MutePreferencesButton />
        </Root>
    );
};

const Root = styled('div')({
    marginLeft: 20,
    paddingLeft: 20,
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
    gap: 20,

    '::before': {
        content: '""',
        display: 'block',
        width: 1,
        backgroundColor: 'var(--neutral4)',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
    },
});

const HideFiltersButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.primary.main,
    margin: '0 16px',
    cursor: 'pointer',
    fontWeight: 'normal',
}));

export default Filter;
