import { ConversationUpdatedMessage } from 'api/whatsapp';
import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { useDispatch } from 'react-redux';
import {
    LostControlFlowEvent,
    handleConversationUpdate,
    handleLostControlFlow,
} from 'store/slices/enterprise/conversationsSlice';
import { usePubnubListener } from 'utils/pubnub';

function useEnterpriseConversationsPubnubListener(settings: GlobalSettingsDto | null) {
    const dispatch = useDispatch();

    usePubnubListener<ConversationUpdatedMessage>(
        (_message) => {
            dispatch(handleConversationUpdate(_message.message.payload));
        },
        {
            channelGroups: [`cg_v1_web_conversations_enterprise_${settings?.id}`],
            types: ['conversation.updated'], // NOTE (MB) removed conversation.gotcontrolflow from here because it's useless for handleConversationUpdate
            listenerEnabled: settings?.appMode === 'Enterprise',
            unsubscribeIfListenerDisabled: true,
        }
    );

    usePubnubListener<LostControlFlowEvent>(
        (_message) => {
            dispatch(handleLostControlFlow(_message.message.payload));
        },
        {
            channelGroups: [`cg_v1_web_conversations_enterprise_${settings?.id}`],
            types: ['conversation.lostcontrolflow'],
            listenerEnabled: settings?.appMode === 'Enterprise',
            unsubscribeIfListenerDisabled: true,
        }
    );

    //#endregion
}

export default useEnterpriseConversationsPubnubListener;
