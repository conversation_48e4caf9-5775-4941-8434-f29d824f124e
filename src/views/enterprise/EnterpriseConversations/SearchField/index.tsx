import { InputAdornment } from '@mui/material';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/enterprise/conversationsSlice';
import { selectConversationsFilters } from 'store/slices/enterprise/conversationsSlice/selectors';
import styles from './styles.module.css';

const SearchField = () => {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const conversationFilter = useAppSelector(selectConversationsFilters);

    const setSearchTerm = (value: string) => {
        dispatch(setFilters({ ...conversationFilter, searchTerm: value }));
    };

    return (
        <TextFormField
            cmosVariant="roundedGrey"
            name={'search'}
            value={conversationFilter.searchTerm}
            placeholder={t('conversations.filter.searchPlaceholder')}
            endAdornment={
                <InputAdornment position="end">
                    <SearchIcon />
                </InputAdornment>
            }
            inputWrapperClasses={{ self: styles.search }}
            onChange={(e) => setSearchTerm(e.target.value)}
        />
    );
};

export default SearchField;
