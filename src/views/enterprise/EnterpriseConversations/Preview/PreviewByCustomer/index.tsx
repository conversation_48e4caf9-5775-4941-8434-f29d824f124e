import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import styled from '@mui/styles/styled';
import { useQuery } from '@tanstack/react-query';
import { EnterpriseCustomersApi } from 'api/enterprise';
import { CustomerPreviewDto } from 'api/enterprise/customers';
import { EnterprisePhoneCallsApi } from 'api/enterprise/phoneCalls';
import { getErrorMessage } from 'api/error';
import AreaSpinner from 'common/components/AreaSpinner';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { EnterpriseConversationDto } from 'datacontracts/WhatsApp/EnterpriseConversationDto';
import { useEffect, useState } from 'react';
import { SimpleErrorDisplay2 } from 'utils/errorsHandling/SimpleErrorBoundary2';
import { getCustomerName } from 'views/Conversations/logic';
import CustomerDetails from '../CustomerDetails';
import { ScheduleMenu } from '../ScheduleMenu';

type PreviewByCustomerProps = {
    conversation: EnterpriseConversationDto;
    onClose: () => void;
    enabled: boolean;
};

export const PreviewByCustomer = ({ conversation, onClose, enabled }: PreviewByCustomerProps) => {
    const { t } = useAppTranslation();

    const [customerData, setCustomerData] = useState<CustomerPreviewDto | null>(null);

    const {
        data: customerDataFromBackend,
        isFetching,
        error,
        refetch,
    } = useQuery({
        enabled,
        queryKey: ['customers', 'details', conversation.shopId, conversation.customerId],
        queryFn: () => {
            return EnterpriseCustomersApi.getCustomerPreview(
                conversation.shopId,
                conversation.customerId || ''
            );
        },
    });

    useEffect(() => {
        if (!customerDataFromBackend) return;
        setCustomerData(customerDataFromBackend);
    }, [customerDataFromBackend]);

    const { data: phoneCallsLog } = useQuery(
        ['phone-calls', 'log', conversation.customerId],
        () => {
            return EnterprisePhoneCallsApi.getLog(
                conversation.shopId,
                conversation.customerId || ''
            );
        }
    );

    if (isFetching) {
        return (
            <StyledPreview>
                <StyledSpinner />
            </StyledPreview>
        );
    }

    if (!customerData || error) {
        const errorMessage = getErrorMessage(error);

        return (
            <StyledPreview>
                <SimpleErrorDisplay2 message={errorMessage} onRetry={refetch} />
            </StyledPreview>
        );
    }

    return (
        <StyledPreview>
            {isFetching || !customerData ? (
                <StyledSpinner />
            ) : (
                <>
                    <StyledFirstHeader>
                        <StyledHeaderContainer>
                            <StyledHeaderContent>
                                <StyledCustomerName>
                                    {getCustomerName(customerData)}{' '}
                                </StyledCustomerName>
                            </StyledHeaderContent>
                            <div>
                                <IconButton size="small" onClick={onClose}>
                                    <CloseIcon fill={Colors.White} />
                                </IconButton>
                            </div>
                        </StyledHeaderContainer>
                    </StyledFirstHeader>
                    <StyledGridRowContainer>
                        <StyledGridRowCaption xs={12}>
                            {t('commonLabels.location')}
                        </StyledGridRowCaption>
                        <StyledGridRowValue xs={12}>{conversation.shopName}</StyledGridRowValue>
                    </StyledGridRowContainer>
                    <StyledScrollable>
                        <CustomerDetails
                            customer={customerData}
                            phoneCallsLog={phoneCallsLog || null}
                        />
                    </StyledScrollable>

                    <StyledBottom>
                        <Grid container spacing={1}>
                            <Grid item xs={12}>
                                <ScheduleMenu
                                    customer={
                                        customerData
                                            ? {
                                                  customerId: customerData.customerId,
                                                  firstName: customerData.firstName,
                                                  lastName: customerData.lastName,
                                                  mobile: customerData.mobile,
                                                  landline: customerData.landline,
                                                  taxIdentification: customerData.taxIdentification,
                                                  email: customerData.email,
                                                  businessName: customerData.businessName,
                                              }
                                            : undefined
                                    }
                                    vehicle={
                                        customerData?.vehicle
                                            ? {
                                                  vehicleId: customerData.vehicle.vehicleId,
                                                  brand: customerData.vehicle.brand,
                                                  model: customerData.vehicle.model,
                                                  plates: customerData.vehicle.plates,
                                                  vin: customerData.vehicle.vin,
                                                  year: customerData.vehicle.year,
                                                  color: customerData.vehicle.color,
                                              }
                                            : undefined
                                    }
                                    shopId={conversation.shopId}
                                    hasFollowUp={customerData.maintenanceInfo?.hasFollowUp}
                                    onFollowUpSaved={(followUpDate) => {
                                        setCustomerData({
                                            ...customerData,
                                            maintenanceInfo: {
                                                ...customerData.maintenanceInfo,
                                                hasFollowUp: true,
                                                nextServiceDate: followUpDate.toString(),
                                            },
                                        });
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </StyledBottom>
                </>
            )}
        </StyledPreview>
    );
};

const StyledPreview = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '0px 4px 16px 0px rgba(130, 130, 130, 0.25)',
    backgroundColor: theme.palette.neutral[1],
    width: 430,
    zIndex: 1,
}));

const StyledSpinner = styled(AreaSpinner)({
    paddingBottom: 56,
});

const StyledFirstHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    backgroundColor: theme.palette.neutral[8],
    paddingLeft: 24,
    paddingRight: 16,
    paddingTop: 10,
    paddingBottom: 8,
}));

const StyledHeaderContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
});
const StyledHeaderContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    rowGap: 5,
});
const StyledCustomerName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    fontSize: 18,
    lineHeight: '22px',
}));
const StyledGridRowContainer = styled(Grid)({
    display: 'flex',
    padding: '0 24px',
    paddingTop: '16px',
});

const StyledGridRowCaption = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    marginRight: '30px',
    color: 'var(--greyBlue)',
    textAlign: 'left',
}));

const StyledGridRowValue = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
}));

const StyledScrollable = styled('div')({
    overflowY: 'auto',
    height: 0,
    flexGrow: 1,
});

const StyledBottom = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[2],
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 14,
    paddingBottom: 14,
}));
