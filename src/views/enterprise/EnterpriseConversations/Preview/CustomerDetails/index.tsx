import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { CustomerPreviewDto } from 'api/customers';
import { PhoneCallsLogItem } from 'api/enterprise/phoneCalls';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { CollapsibleSection } from 'views/Conversations/Preview/Collapsible';

type CustomerPreviewDetailsProps = {
    customer: CustomerPreviewDto | undefined;
    phoneCallsLog: PhoneCallsLogItem[] | null;
};

const StyledCustomerInformationLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: 'var(--greyBlue)',
    textAlign: 'left',
}));

const StyledCustomerInformationValue = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    width: 0,
    flexGrow: 1,
    display: 'flex',
    alignItems: 'flex-end',
    textAlign: 'left',
    '& > span': {
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
    },
}));

const StyledPhoneCallLogItem = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    display: 'flex',
    flexDirection: 'column',
    textAlign: 'left',
    padding: '2px 0px 10px 0px',
    marginLeft: '32px',
    borderBottom: '1px solid var(--neutral3)',
}));

const CustomerDetails = ({ customer, phoneCallsLog }: CustomerPreviewDetailsProps) => {
    const { t } = useAppTranslation();
    const { internationalization } = useSelector(selectSettings);

    const vehicleInformation = useMemo(() => {
        const brandModel = [customer?.vehicle?.brand, customer?.vehicle?.model]
            .filter((value) => Boolean(value))
            .join(' ');
        const vehicleInformation = [brandModel, customer?.vehicle?.year]
            .filter((value) => Boolean(value))
            .join(', ');
        return vehicleInformation || '--';
    }, [customer?.vehicle]);

    const [callsLogOpen, setCallsLogOpen] = useState<boolean>(false);

    return (
        <>
            <CollapsibleSection title={t('orders.preview.customerInformation')}>
                <Grid container spacing={1} direction="row" justifyContent="center">
                    <Grid item xs={6}>
                        <Grid container direction="column" spacing={1}>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <StyledCustomerInformationLabel>
                                        {t('commonLabels.mobile')}
                                    </StyledCustomerInformationLabel>
                                </Grid>
                                <StyledCustomerInformationValue item>
                                    <span>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            customer?.mobile || null
                                        )}
                                    </span>
                                </StyledCustomerInformationValue>
                            </Grid>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <StyledCustomerInformationLabel>
                                        {t('commonLabels.email')}
                                    </StyledCustomerInformationLabel>
                                </Grid>
                                <StyledCustomerInformationValue item>
                                    <span>{customer?.email || '--'}</span>
                                </StyledCustomerInformationValue>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={6}>
                        <Grid container direction="column" spacing={1}>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <StyledCustomerInformationLabel>
                                        {t('commonLabels.phone')}
                                    </StyledCustomerInformationLabel>
                                </Grid>
                                <StyledCustomerInformationValue item>
                                    <span>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            customer?.landline || null
                                        )}
                                    </span>
                                </StyledCustomerInformationValue>
                            </Grid>
                            <Grid container item>
                                <Grid item xs={5}>
                                    <StyledCustomerInformationLabel>
                                        {t('commonLabels.taxIdentification')}
                                    </StyledCustomerInformationLabel>
                                </Grid>
                                <StyledCustomerInformationValue item style={{ marginLeft: 5 }}>
                                    <span>{customer?.taxIdentification || '--'}</span>
                                </StyledCustomerInformationValue>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.vehicleInformation')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.vehicle')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                <span>{vehicleInformation}</span>
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.vin')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                <span>{customer?.vehicle?.vin || '--'}</span>
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                        <StyledCustomerInformationLabel>
                                            {t('commonLabels.plates')}
                                        </StyledCustomerInformationLabel>
                                    </Grid>
                                    <StyledCustomerInformationValue item xs={6}>
                                        <span>{customer?.vehicle?.plates || '--'}</span>
                                    </StyledCustomerInformationValue>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.lastVisit')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.date')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.lastLinkedOrder?.lastUpdatedTime
                                    ? moment(
                                          new Date(customer.lastLinkedOrder.lastUpdatedTime)
                                      ).format(t('dateFormats.longDate'))
                                    : '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.mileage')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.lastLinkedOrder?.mileage || '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.serviceAdvisor')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.lastLinkedOrder?.inChargeUser || '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.orderType')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.lastLinkedOrder?.orderType || '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.maintenanceInformation')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t(
                                        `commonLabels.${
                                            customer?.maintenanceInfo?.hasFollowUp
                                                ? 'followUpDate'
                                                : 'nextServiceDate'
                                        }`
                                    )}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.maintenanceInfo?.nextServiceDate
                                    ? moment(
                                          new Date(customer?.maintenanceInfo.nextServiceDate)
                                      ).format(t('dateFormats.longDate'))
                                    : '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.calculatedMileage')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.maintenanceInfo?.calculatedMileage || '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <StyledCustomerInformationLabel>
                                    {t('commonLabels.serviceAdvisor')}
                                </StyledCustomerInformationLabel>
                            </Grid>
                            <StyledCustomerInformationValue item>
                                {customer?.maintenanceInfo?.serviceAdvisor || '--'}
                            </StyledCustomerInformationValue>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            {phoneCallsLog && phoneCallsLog.length > 0 && (
                <CollapsibleSection title={t('orders.preview.callHistory')} isCollapse={true}>
                    <Grid
                        container
                        spacing={1}
                        direction="row"
                        justifyContent="center"
                        alignItems="center"
                        alignContent="center"
                    >
                        {phoneCallsLog
                            ?.slice(0, callsLogOpen ? phoneCallsLog.length + 1 : 2)
                            .map((logItem) => (
                                <Grid item xs={12}>
                                    <StyledPhoneCallLogItem>
                                        <div>{`${t('orders.preview.phoneCallTo')} ${
                                            logItem.customerNumber
                                        }`}</div>
                                        <div>{`${t('orders.preview.started')} ${
                                            logItem.start
                                                ? moment(new Date(logItem.start)).format(
                                                      t('dateFormats.long')
                                                  )
                                                : '--'
                                        }`}</div>
                                        <div>{`${t('orders.preview.finished')} ${
                                            logItem.end
                                                ? moment(new Date(logItem.end)).format(
                                                      t('dateFormats.long')
                                                  )
                                                : '--'
                                        }`}</div>
                                        {logItem.recordUrl && (
                                            <a href={logItem.recordUrl}>
                                                {t('orders.preview.callRecord')}
                                            </a>
                                        )}
                                        <div>{`${t('orders.preview.by')} ${
                                            logItem.teamMemberName
                                        }`}</div>
                                    </StyledPhoneCallLogItem>
                                </Grid>
                            ))}
                        {phoneCallsLog && phoneCallsLog.length > 2 && (
                            <Grid item xs={12} sx={{ paddingLeft: 32 }}>
                                <Button
                                    sx={{ marginLeft: '22px' }}
                                    cmosVariant={'typography'}
                                    onClick={() => setCallsLogOpen(!callsLogOpen)}
                                >
                                    {callsLogOpen
                                        ? t('orders.preview.hide')
                                        : t('orders.preview.seeMore')}
                                </Button>
                            </Grid>
                        )}
                    </Grid>
                </CollapsibleSection>
            )}
        </>
    );
};

export default CustomerDetails;
