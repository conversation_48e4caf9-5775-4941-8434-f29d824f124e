import { Grid, Popover } from '@mui/material';
import { VehicleProspectionLogApi } from 'api/enterprise/vehicleProspectionLogApi';
import { Button } from 'common/components/Button';
import { DownIcon } from 'common/components/Icons/DownIcon';
import DateField from 'common/components/Inputs/DateField';
import { InputLabel } from 'common/components/Inputs/InputLabel';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from 'store';
import { useStyles } from './css';

type CustomerDetails = {
    customerId: string;
    firstName: string;
    lastName: string;
    mobile: string;
    landline: string;
    taxIdentification: string;
    email: string;
    businessName: string;
};

type VehicleDetails = {
    vehicleId: string;
    plates: string;
    vin: string;
    model: string;
    brand: string;
    year: string;
    color: string;
};

type ScheduleMenuProps = {
    customer?: CustomerDetails;
    vehicle?: VehicleDetails;
    shopId: string;
    hasFollowUp?: boolean;
    onFollowUpSaved: (followUpDate: Date) => void;
};

export function ScheduleMenu(data: ScheduleMenuProps) {
    const { t } = useAppTranslation();
    const styles = useStyles();
    const [anchorEl, setAnchorEl] = useState<null | Element>(null);
    const open = Boolean(anchorEl);
    const popoverId = open ? 'simple-popover' : undefined;
    const dispatch = useAppDispatch();
    const [followUpOpen, setFollowUpOpen] = useState<boolean>(false);
    const [followUpDate, setFollowUpDate] = useState<Date>(new Date());
    const navigate = useNavigate();
    const { callApi } = useApiCall();

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleSaveFollowUp = async () => {
        if (data.vehicle && data.vehicle.vehicleId) {
            const response = await callApi(
                () =>
                    VehicleProspectionLogApi.create({
                        shopId: data.shopId,
                        vehicleId: data.vehicle?.vehicleId || '',
                        followUpDate: followUpDate,
                    }),
                {
                    selectErrorContent: () => ({
                        body: t('toasters.errorOccuredWhenSaving'),
                    }),
                }
            );
            data.onFollowUpSaved(response.followUpDate);
        }
        setFollowUpOpen(false);
    };

    const handleScheduleAppointment = async () => {
        handleClose();

        navigate(
            `${ENTERPRISE_ROUTES.APPOINTMENTS_NEW}?shopId=${data.shopId}` +
                (data.customer ? `&customerId=${data.customer.customerId}` : '')
        );
    };
    return (
        <>
            <Button
                blockMode
                cmosVariant={'filled'}
                color={Colors.CM1}
                iconPosition="right"
                Icon={DownIcon}
                label={t('orders.preview.schedule')}
                onClick={handleClick}
            />
            <Popover
                id={popoverId}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
            >
                <ul className={styles.scheduleList}>
                    <li onClick={handleScheduleAppointment}>{t('orders.preview.appointment')}</li>
                    {!data.hasFollowUp ? (
                        <li
                            onClick={() => {
                                handleClose();
                                setFollowUpOpen(true);
                            }}
                        >
                            {t('orders.preview.followUp')}
                        </li>
                    ) : null}
                </ul>
            </Popover>
            {followUpOpen && (
                <div className={styles.followUpFormContainer}>
                    <div className={styles.followUpFormBackdrop} />
                    <div className={styles.followUpForm}>
                        <Grid container>
                            <Grid item xs={6}>
                                <InputLabel>{t('orders.preview.followUpDate')}</InputLabel>
                                <DateField
                                    name="follow-up-date"
                                    variant="grey"
                                    disablePast
                                    value={followUpDate}
                                    onChange={(date: Date | null) => {
                                        if (date) setFollowUpDate(date);
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </div>
                    <div className={styles.bottom}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <Button
                                    cmosVariant={'stroke'}
                                    color={Colors.CM1}
                                    label={t('orders.preview.cancel')}
                                    onClick={() => setFollowUpOpen(false)}
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <Button
                                    cmosVariant={'filled'}
                                    color={Colors.Success}
                                    label={t('orders.preview.scheduleFollowUp')}
                                    onClick={handleSaveFollowUp}
                                />
                            </Grid>
                        </Grid>
                    </div>
                </div>
            )}
        </>
    );
}
