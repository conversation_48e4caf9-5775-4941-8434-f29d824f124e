import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import {
    IPreviewItem,
    IRepairOrderPreviewResponse,
} from 'datacontracts/Order/IOrderPreviewResponse';
import moment from 'moment';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { CollapsibleEstimate, CollapsibleSection } from 'views/Conversations/Preview/Collapsible';
import { useStyles } from './css';

interface OrderPreviewDetailsProps {
    order: IRepairOrderPreviewResponse | undefined;
    twoColumnCustomerInformation?: boolean;
}

const OrderDetails = ({ order }: OrderPreviewDetailsProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const { internationalization } = useSelector(selectSettings);

    const getTotalEstimateWithSubitems = (items: IPreviewItem[] | undefined) => {
        return (
            items?.reduce((acc, current) => {
                const subitemsTotal =
                    current.subitems.reduce((acc, current) => acc + (current.totalCost ?? 0), 0) ??
                    0;
                return acc + (current.totalCost ?? 0) + subitemsTotal;
            }, 0) ?? 0
        );
    };

    const urgentEstimatesTotal = getTotalEstimateWithSubitems(order?.redItems);
    const medEstimatesTotal = getTotalEstimateWithSubitems(order?.yellowItems);

    const vehicleInformation = useMemo(() => {
        const brandModel = [order?.make, order?.model].filter((value) => Boolean(value)).join(' ');
        const vehicleInformation = [brandModel, order?.year]
            .filter((value) => Boolean(value))
            .join(', ');
        return vehicleInformation || '--';
    }, [order]);

    const ticketAverage = useMemo(() => {
        const redItems =
            order?.redItems.reduce((prev, current) => prev + (current.totalCost ?? 0), 0) ?? 0;
        const yellowItems =
            order?.yellowItems.reduce((prev, current) => prev + (current.totalCost ?? 0), 0) ?? 0;

        return (
            (redItems + yellowItems) /
            ((order?.redItems.length || 0) + (order?.yellowItems.length || 0))
        );
    }, [order]);

    return (
        <>
            <CollapsibleSection title={t('orders.preview.customerInformation')} isCollapse>
                <Grid container spacing={1} direction="row" justifyContent="center">
                    <Grid item xs={6}>
                        <Grid container direction="column" spacing={1}>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <span className={styles.customerInformationLabel}>
                                        {t('commonLabels.mobile')}
                                    </span>
                                </Grid>
                                <Grid item className={styles.customerInformationValue}>
                                    <span>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            order?.mobilePhone || null
                                        )}
                                    </span>
                                </Grid>
                            </Grid>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <span className={styles.customerInformationLabel}>
                                        {t('commonLabels.email')}
                                    </span>
                                </Grid>
                                <Grid item className={styles.customerInformationValue}>
                                    <span>{order?.email || '--'}</span>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={6}>
                        <Grid container direction="column" spacing={1}>
                            <Grid container item>
                                <Grid item xs={4}>
                                    <span className={styles.customerInformationLabel}>
                                        {t('commonLabels.phone')}
                                    </span>
                                </Grid>
                                <Grid item className={styles.customerInformationValue}>
                                    <span>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            order?.landlinePhone || null
                                        )}
                                    </span>
                                </Grid>
                            </Grid>
                            <Grid container item>
                                <Grid item xs={5}>
                                    <span className={styles.customerInformationLabel}>
                                        {t('commonLabels.businessName')}
                                    </span>
                                </Grid>
                                <Grid
                                    item
                                    className={styles.customerInformationValue}
                                    style={{ marginLeft: 5 }}
                                >
                                    <span>{order?.businessName || '--'}</span>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.vehicleInformation')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <span className={styles.customerInformationLabel}>
                                    {t('commonLabels.vehicle')}
                                </span>
                            </Grid>
                            <Grid item className={styles.customerInformationValue}>
                                <span>{vehicleInformation}</span>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <span className={styles.customerInformationLabel}>
                                    {t('commonLabels.vin')}
                                </span>
                            </Grid>
                            <Grid item className={styles.customerInformationValue}>
                                <span>{order?.vin || '--'}</span>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                        <span className={styles.customerInformationLabel}>
                                            {t('commonLabels.plates')}
                                        </span>
                                    </Grid>
                                    <Grid item xs={6} className={styles.customerInformationValue}>
                                        <span>{order?.plates || '--'}</span>
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={6}>
                                <Grid container spacing={1}>
                                    <Grid item xs={6}>
                                        <span className={styles.customerInformationLabel}>
                                            {t('commonLabels.averageTicket')}
                                        </span>
                                    </Grid>
                                    <Grid item xs={6} className={styles.customerInformationValue}>
                                        <span>
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                ticketAverage
                                            )}
                                        </span>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleSection
                title={
                    <div style={{ display: 'flex' }}>
                        <span className={styles.title} style={{ marginRight: 5 }}>
                            {t('orders.preview.inspectionItems')}
                        </span>
                        -
                        <Link
                            classes={{ root: styles.titleLink }}
                            href={order?.inspectionLink || '#'}
                            style={{ marginLeft: 5 }}
                        >
                            {order?.inspectionLink || '--'}
                        </Link>
                    </div>
                }
            >
                <CollapsibleEstimate
                    padded
                    title={`${t('orders.preview.businessOpportunity')} - ${t(
                        'commonLabels.priorities.urgent'
                    )}`}
                    total={InternationalizationLogic.numberToCurrency(
                        internationalization,
                        urgentEstimatesTotal
                    )}
                    estimateQuantity={order?.redItems ? order?.redItems.length : 0}
                    priority={'Urgent'}
                    isHide={false}
                >
                    {order?.redItems && <BusinessOpportunityList items={order.redItems} />}
                </CollapsibleEstimate>

                <CollapsibleEstimate
                    padded
                    title={`${t('orders.preview.businessOpportunity')} - ${t(
                        'commonLabels.priorities.suggested'
                    )}`}
                    total={InternationalizationLogic.numberToCurrency(
                        internationalization,
                        medEstimatesTotal
                    )}
                    estimateQuantity={order?.yellowItems ? order?.yellowItems.length : 0}
                    priority={'Med'}
                    isHide={true}
                >
                    {order?.yellowItems && <BusinessOpportunityList items={order.yellowItems} />}
                </CollapsibleEstimate>
            </CollapsibleSection>

            <CollapsibleSection title={t('orders.preview.maintenanceInformation')}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <span className={styles.customerInformationLabel}>
                                    {t(
                                        `commonLabels.${
                                            order?.hasFollowUp ? 'followUpDate' : 'nextServiceDate'
                                        }`
                                    )}
                                </span>
                            </Grid>
                            <Grid item className={styles.customerInformationValue}>
                                {order?.nextServiceDate
                                    ? moment(new Date(order.nextServiceDate)).format(
                                          t('dateFormats.longDate')
                                      )
                                    : '--'}
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <span className={styles.customerInformationLabel}>
                                    {t('commonLabels.calculatedMileage')}
                                </span>
                            </Grid>
                            <Grid item className={styles.customerInformationValue}>
                                {order?.calculatedMileage || '--'}
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={6}>
                                <span className={styles.customerInformationLabel}>
                                    {t('commonLabels.serviceAdvisor')}
                                </span>
                            </Grid>
                            <Grid item className={styles.customerInformationValue}>
                                {order?.serviceAdvisor?.name || '--'}
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>
        </>
    );
};

const BusinessOpportunityList = ({ items }: { items: IPreviewItem[] }) => {
    const styles = useStyles();
    const { internationalization } = useSelector(selectSettings);
    return (
        <div>
            {items.map((estimate, index) => (
                <div key={index}>
                    <div className={styles.estimateRow}>
                        <span>- {estimate.repairName}</span>
                        <span>
                            {InternationalizationLogic.numberToCurrency(
                                internationalization,
                                estimate.totalCost
                            )}
                        </span>
                    </div>
                    {estimate.subitems.length > 0
                        ? estimate.subitems.map((subItem, index) => (
                              <div key={index} className={styles.subitemRow}>
                                  <span>* {subItem.name}</span>
                                  <span>
                                      {InternationalizationLogic.numberToCurrency(
                                          internationalization,
                                          subItem.totalCost
                                      )}
                                  </span>
                              </div>
                          ))
                        : null}

                    {index + 1 !== items.length ? <Divider /> : null}
                </div>
            ))}
        </div>
    );
};

export default OrderDetails;
