import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme: Theme) => ({
    customerInformationLabel: {
        ...theme.typography.h6Inter,
        color: 'var(--greyBlue)',
        textAlign: 'left',
    },
    customerInformationValue: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[6],
        width: 0,
        flexGrow: 1,
        display: 'flex',
        alignItems: 'flex-end',
        textAlign: 'left',
        '& > span': {
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
        },
    },
    estimateRow: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 5,
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.neutral[6]),
    },
    subitemRow: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '3px 5px 5px 5px',
        ...FontPrimary(HeaderStyles.H8_10px, false, theme.palette.neutral[6]),
    },
    title: {
        ...theme.typography.h6Inter,
        color: 'var(--greyBlue)',
    },
    titleLink: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.primary.light,
    },
}));
