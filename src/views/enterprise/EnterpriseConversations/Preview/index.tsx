import { EnterpriseConversationDto } from 'datacontracts/WhatsApp/EnterpriseConversationDto';
import { PreviewByCustomer } from './PreviewByCustomer';
import { PreviewByOrder } from './PreviewByOrder';

type PreviewProps = {
    conversation: EnterpriseConversationDto;
    onClose: () => void;
    enabled: boolean;
};

export const Preview = ({ conversation, onClose, enabled }: PreviewProps) => {
    return (
        <>
            {conversation.repairOrderId ? (
                <PreviewByOrder enabled={enabled} conversation={conversation} onClose={onClose} />
            ) : (
                conversation.customerId && (
                    <PreviewByCustomer
                        enabled={enabled}
                        conversation={conversation}
                        onClose={onClose}
                    />
                )
            )}
        </>
    );
};

export default Preview;
