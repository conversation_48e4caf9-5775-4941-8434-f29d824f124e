import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton/IconButton';
import { useQuery } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { CustomerDetailsDto, VehicleListItemDto } from 'api/customers';
import { EnterpriseCustomersApi } from 'api/enterprise';
import { getErrorMessage } from 'api/error';
import AreaSpinner from 'common/components/AreaSpinner';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IRepairOrderPreviewResponse } from 'datacontracts/Order/IOrderPreviewResponse';
import { EnterpriseConversationDto } from 'datacontracts/WhatsApp/EnterpriseConversationDto';
import moment from 'moment';
import { useState } from 'react';
import { generatePath, useNavigate } from 'react-router-dom';
import { SimpleErrorDisplay2 } from 'utils/errorsHandling/SimpleErrorBoundary2';
import { getConsumerName } from 'views/Conversations/logic';
import PreviewDetail from '../OrderDetails';
import { ScheduleMenu } from '../ScheduleMenu';

type PreviewByOrderProps = {
    conversation: EnterpriseConversationDto;
    onClose: () => void;
    enabled: boolean;
};

export const PreviewByOrder = ({ conversation, onClose, enabled }: PreviewByOrderProps) => {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const [data, setData] = useState<IRepairOrderPreviewResponse | null>(null);
    const [customerDetails, setCustomerDetails] = useState<CustomerDetailsDto | null>(null);
    const [vehicle, setVehicle] = useState<VehicleListItemDto | null>(null);

    const seeOrderDetails = async () => {
        if (data) {
            navigate(
                generatePath(ENTERPRISE_ROUTES.ORDERS_DETAIL, { id: data.repairOrderId + '' })
            );
        }
    };

    const { isLoading, error, refetch } = useQuery({
        queryKey: ['customers', 'details', conversation.repairOrderId],
        queryFn: async () => {
            if (!conversation.repairOrderId)
                throw new Error('conversation does not have order key is not set');
            const orderId = await OrderAPI.getIdByKey(conversation.repairOrderId);
            const orderPreviewData = await OrderAPI.preview(orderId);

            const customer = await EnterpriseCustomersApi.details(
                conversation.shopId,
                orderPreviewData.customerId || undefined,
                orderPreviewData.firstName || undefined,
                orderPreviewData.lastName || undefined
            );

            return { customer, orderPreviewData };
        },
        cacheTime: 0,
        staleTime: 0,
        enabled: !!conversation.repairOrderId && enabled,
        onSuccess: ({ customer, orderPreviewData }): void => {
            setCustomerDetails(customer);
            setData(orderPreviewData);

            if (customer.linkedVehicles && customer.linkedVehicles.length) {
                setVehicle(
                    customer.linkedVehicles.find(
                        (v) =>
                            v.plates === orderPreviewData?.plates &&
                            v.brand === orderPreviewData.make &&
                            v.year ===
                                `${orderPreviewData.year !== null ? orderPreviewData.year : ''}` &&
                            v.model === orderPreviewData.model &&
                            v.vin === orderPreviewData.vin
                    ) ?? null
                );
            }
        },
    });

    useQuery(
        [
            'customers',
            'details',
            conversation.shopId,
            data?.customerId,
            data?.firstName,
            data?.lastName,
        ],
        () => {
            return EnterpriseCustomersApi.details(
                conversation.shopId,
                data?.customerId || '',
                data?.firstName || '',
                data?.lastName || ''
            );
        },
        {
            enabled: data != null,
            onSuccess: (customer: CustomerDetailsDto): void => {
                setCustomerDetails(customer);

                if (customer.linkedVehicles && customer.linkedVehicles.length) {
                    setVehicle(
                        customer.linkedVehicles.find(
                            (v) =>
                                v.plates === data?.plates &&
                                v.brand === data.make &&
                                v.year === `${data.year !== null ? data.year : ''}` &&
                                v.model === data.model &&
                                v.vin === data.vin
                        ) ?? null
                    );
                }
            },
        }
    );

    if (isLoading) {
        return (
            <StyledPreview>
                <StyledSpinner />
            </StyledPreview>
        );
    }

    if (!data || error) {
        const errorMessage = getErrorMessage(error);

        return (
            <StyledPreview>
                <SimpleErrorDisplay2 message={errorMessage} onRetry={refetch} />
            </StyledPreview>
        );
    }

    return (
        <StyledPreview>
            <StyledFirstHeader>
                <StyledHeaderContainer>
                    <StyledHeaderContent>
                        <StyledCustomerName>{`${getConsumerName(conversation)} - ${t(
                            'conversations.orderNumber',
                            {
                                number: data.repairOrderNumber,
                            }
                        )}`}</StyledCustomerName>
                        <StyledOrderDate>
                            {moment(data.uploadTime).format(t('dateFormats.longDate'))}
                        </StyledOrderDate>
                    </StyledHeaderContent>
                    <div>
                        <IconButton size="small" onClick={onClose}>
                            <CloseIcon fill={Colors.White} />
                        </IconButton>
                    </div>
                </StyledHeaderContainer>
            </StyledFirstHeader>
            <StyledGridRowContainer>
                <StyledGridRowCaption xs={3}>{t('commonLabels.location')}</StyledGridRowCaption>
                <StyledGridRowValue xs={7}>{conversation.shopName}</StyledGridRowValue>
            </StyledGridRowContainer>
            <StyledScrollable>
                <PreviewDetail order={data} twoColumnCustomerInformation />
            </StyledScrollable>

            <StyledBottom>
                <Grid container spacing={1}>
                    <Grid item xs={6}>
                        <Button
                            blockMode
                            cmosVariant={'stroke'}
                            color={Colors.CM1}
                            label={t('orders.preview.seeOrderDetail')}
                            onClick={() => seeOrderDetails()}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <ScheduleMenu
                            customer={
                                customerDetails
                                    ? {
                                          customerId: customerDetails.customerId,
                                          firstName: customerDetails.firstName,
                                          lastName: customerDetails.lastName,
                                          mobile: customerDetails.mobile,
                                          landline: customerDetails.landline,
                                          taxIdentification: customerDetails.taxIdentification,
                                          email: customerDetails.email,
                                          businessName: customerDetails.businessName,
                                      }
                                    : undefined
                            }
                            vehicle={
                                vehicle
                                    ? {
                                          vehicleId: vehicle.id,
                                          brand: vehicle.brand,
                                          model: vehicle.model,
                                          plates: vehicle.plates,
                                          vin: vehicle.vin,
                                          year: vehicle.year,
                                          color: vehicle.color,
                                      }
                                    : undefined
                            }
                            shopId={conversation.shopId}
                            hasFollowUp={data.hasFollowUp}
                            onFollowUpSaved={(followUpDate) => {
                                setData({
                                    ...data,
                                    hasFollowUp: true,
                                    nextServiceDate: followUpDate,
                                });
                            }}
                        />
                    </Grid>
                </Grid>
            </StyledBottom>
        </StyledPreview>
    );
};

const StyledPreview = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    boxShadow: '0px 4px 16px 0px rgba(130, 130, 130, 0.25)',
    backgroundColor: theme.palette.neutral[1],
    width: 430,
    zIndex: 1,
}));

const StyledSpinner = styled(AreaSpinner)({
    paddingBottom: 56,
});

const StyledFirstHeader = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    backgroundColor: theme.palette.neutral[8],
    paddingLeft: 24,
    paddingRight: 16,
    paddingTop: 10,
    paddingBottom: 8,
}));

const StyledHeaderContainer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
});
const StyledHeaderContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    rowGap: 5,
});
const StyledCustomerName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[1],
    fontSize: 18,
    lineHeight: '22px',
}));
const StyledOrderDate = styled('span')(({ theme }) => ({
    ...theme.typography.h7Roboto,
    color: theme.palette.neutral[1],
}));
const StyledGridRowContainer = styled(Grid)({
    display: 'flex',
    padding: '0 24px',
    paddingTop: '16px',
});

const StyledGridRowCaption = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    marginRight: '30px',
    color: 'var(--greyBlue)',
    textAlign: 'left',
}));

const StyledGridRowValue = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
}));

const StyledScrollable = styled('div')({
    overflowY: 'auto',
    height: 0,
    flexGrow: 1,
});

const StyledBottom = styled('div')({
    backgroundColor: Colors.Neutral2,
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 14,
    paddingBottom: 14,
});
