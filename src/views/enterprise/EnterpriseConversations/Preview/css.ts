import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

export const useStyles = makeStyles((theme: Theme) => ({
    preview: {
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0px 4px 16px 0px rgba(130, 130, 130, 0.25)',
        backgroundColor: theme.palette.neutral[1],
        width: 430,
        zIndex: 1,
    },
    gridRowContainer: {
        padding: '0 24px',
        paddingTop: '16px',
    },
    gridRowCaption: {
        ...theme.typography.h6Inter,
        color: 'var(--greyBlue)',
        textAlign: 'left',
    },
    gridRowValue: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[6],
        width: 0,
        flexGrow: 1,
        display: 'flex',
        alignItems: 'flex-end',
        textAlign: 'left',
        '& > span': {
            textOverflow: 'ellipsis',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
        },
    },

    firstHeader: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[1],
        backgroundColor: theme.palette.neutral[8],
        paddingLeft: 24,
        paddingRight: 16,
        paddingTop: 10,
        paddingBottom: 8,
    },
    headerContainer: {
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
    },
    headerContent: {
        display: 'flex',
        flexDirection: 'column',
        rowGap: 5,
    },
    customerName: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[1],
        fontSize: 18,
        lineHeight: '22px',
    },
    orderNumber: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[1],
    },
    orderDate: {
        ...theme.typography.h7Roboto,
        color: theme.palette.neutral[1],
    },
    areaSpinner: {
        paddingBottom: 56,
    },
    secondHeader: {
        height: 60,
        boxSizing: 'border-box',
        borderBottomWidth: 1,
        borderBottomStyle: 'solid',
        borderBottomColor: theme.palette.neutral[3],
        backgroundColor: theme.palette.neutral[2],
        paddingLeft: 24,
        paddingRight: 24,
        display: 'flex',
        justifyItems: 'space-between',
        alignItems: 'center',
    },
    vehicle: {
        ...theme.typography.h4Inter,
        color: '#000000',
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        width: 0,
        flexGrow: 1,
    },
    statistics: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[6],
        display: 'flex',
        columnGap: 8,
        marginLeft: 4,
    },
    statisticsItem: {
        display: 'flex',
        columnGap: 4,
        alignItems: 'center',
    },
    scrollable: {
        overflowY: 'auto',
        height: 0,
        flexGrow: 1,
    },
    bottom: {
        backgroundColor: theme.palette.neutral[2],
        paddingLeft: 24,
        paddingRight: 24,
        paddingTop: 14,
        paddingBottom: 14,
    },
    scheduleList: {
        listStyleType: 'none',
        margin: 0,
        padding: '15px 19.5px',
        '& li': {
            cursor: 'pointer',
            ...theme.typography.h6Roboto,
            padding: 8,
            fontWeight: 'normal',
            color: theme.palette.neutral[7],
            borderBottom: `1px solid ${theme.palette.neutral[3]}`,
            '&:last-child': {
                border: 'none',
            },
        },
    },
    followUpForm: {
        borderTop: `1px solid ${theme.palette.neutral[2]}`,
        padding: '18px 25px',
    },
}));
