import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    content: {
        display: 'flex',
        flexDirection: 'column',
        flexGrow: 1,
    },
    top: {
        minHeight: 32,
    },
    useTemplate: {
        width: 116,

        '&.disabled': {
            opacity: 0.3,
            pointerEvents: 'none',
        },
    },
    scrollArea: {
        width: 0,
        flexGrow: 1,
    },
    textArea: {
        paddingTop: 2,
    },
    scrollBarContent: {
        padding: 0,
        height: 0,
    },
    fastMessages: {
        display: 'flex',
        justifyContent: 'center',
        columnGap: 16,
    },
    fastMessage: {
        width: 'initial',
        paddingLeft: 16,
        paddingRight: 16,
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.primary.main),
    },
    send: {
        boxSizing: 'border-box',
        height: 40,
        width: 110,
        marginTop: 8,
        marginLeft: 13,
        fontSize: 16,
    },
    disabled: {
        pointerEvents: 'none',
        backgroundColor: theme.palette.neutral[4],
    },
    templates: {
        width: 320,
    },
    template: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[6]),
        whiteSpace: 'break-spaces',
    },
    warning: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[6]),
    },
}));
