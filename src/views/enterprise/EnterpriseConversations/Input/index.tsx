import { useMemo } from 'react';
import ShopInput, { InputProps as ShopInputProps } from 'views/Conversations/Input';
import { EnterpriseConversationDto } from '../../../../datacontracts/WhatsApp/EnterpriseConversationDto';

type InputProps = {
    conversation: EnterpriseConversationDto;
    rightOffset: number;
    onMessageCreated: () => void;
};

const Input = ({ conversation, rightOffset, onMessageCreated }: InputProps) => {
    const shopConversation = useMemo(() => {
        const c: ShopInputProps['conversation'] = {
            conversationId: conversation.conversationId,
            lastInboundMessage: conversation.lastInboundMessage,
            chatBotMode: conversation.chatBotMode,
            currentControlFlow: conversation.currentControlFlow,
            repairOrderId: conversation.repairOrderId,
            appointmentId: conversation.appointmentId,
            vehicleId: conversation.vehicleId,
            massSendingId: conversation.massSendingId,
        };
        return c;
    }, [conversation]);

    return (
        <ShopInput
            rightOffset={rightOffset}
            onMessageCreated={onMessageCreated}
            shopId={conversation.shopId}
            conversation={shopConversation}
        />
    );
};

export default Input;
