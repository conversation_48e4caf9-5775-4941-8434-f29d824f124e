import { styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { EnterprisePhoneCallsApi } from 'api/enterprise/phoneCalls';
import { isCmosError } from 'api/error';
import { Button } from 'common/components/Button';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { EnterpriseConversationDto } from 'datacontracts/WhatsApp/EnterpriseConversationDto';
import { useMemo, useState } from 'react';
import { isErrorResponse } from 'services/Server';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { useCurrentUser } from 'store/slices/user';
import IsConversationMuted from 'views/Conversations/IsConversationMuted';
import { getConsumerName } from 'views/Conversations/logic';
import ConnectingCall from '../../../Components/ConnectingCall';

type HeaderProps = {
    conversation: EnterpriseConversationDto;
    isDetailsOpened: boolean;
    toggleDetails: () => void;
};

export const Header = ({ conversation, isDetailsOpened, toggleDetails }: HeaderProps) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [isConnectingCallOpened, setIsConnectingCallOpened] = useState(false);
    const { enterpriseSettings } = useAppSelector(selectSettings);
    const user = useCurrentUser();

    const phoneCallSettings = useMemo(() => {
        if (conversation && enterpriseSettings) {
            const shop = enterpriseSettings.shops.find((s) => s.key === conversation.shopId);
            if (shop) {
                return {
                    enablePhoneCalls: shop.enablePhoneCalls,
                };
            }
        }
        return {
            enablePhoneCalls: false,
        };
    }, [conversation, enterpriseSettings]);

    const callMutation = useMutation(EnterprisePhoneCallsApi.makeCall, {
        onMutate: () => {
            setIsConnectingCallOpened(true);
        },
        onError: () => {
            // NOTE (MB) this is unlikely to happen so it's fine
            toasters.danger('Failed to call the customer', t('toasters.errorOccurred'));
        },
    });
    const errorMessage = useMemo(() => {
        const err = callMutation.error;
        if (isCmosError(err)) {
            return `${err.cmosMessage}`;
        }

        if (isErrorResponse(err)) {
            return `${err.code}: ${err.message}`;
        }

        return err ? `${err}` : undefined;
    }, [callMutation.error]);

    const callClickHandler = () => {
        callMutation.mutate({
            shopId: conversation.shopId,
            callTo: conversation.customerPhoneNumber,
            orderId: conversation.repairOrderId,
            customerId: conversation.customerId,
        });
    };

    return (
        <>
            <Root>
                <div>
                    <Name>{getConsumerName(conversation)}</Name>
                    <ShopName>{conversation.shopName}</ShopName>
                    <IsConversationMuted conversationId={conversation.conversationId} />
                </div>
                <Buttons>
                    {phoneCallSettings.enablePhoneCalls && (
                        <ArrowTooltip
                            content={t('orders.preview.masterPhoneCallTooltip')}
                            disabled={user.role !== 'Master'}
                        >
                            <div>
                                <CalloutButton
                                    cmosVariant={'stroke'}
                                    cmosSize={'medium'}
                                    iconPosition="right"
                                    color={Colors.CM1}
                                    Icon={PhoneIcon}
                                    onClick={callClickHandler}
                                    disabled={user.role === 'Master'}
                                />
                            </div>
                        </ArrowTooltip>
                    )}
                    {conversation.customerId && (
                        <DetailsButton
                            cmosVariant={'stroke'}
                            cmosSize={'medium'}
                            color={Colors.CM1}
                            label={
                                isDetailsOpened
                                    ? t('conversations.hideCustomerDetail')
                                    : t('conversations.showCustomerDetail')
                            }
                            onClick={toggleDetails}
                        />
                    )}
                </Buttons>
            </Root>
            <ConnectingCall
                open={isConnectingCallOpened}
                isLoading={callMutation.isLoading}
                errorMessage={errorMessage}
                callFailed={callMutation.isError}
                onClose={() => setIsConnectingCallOpened(false)}
            />
        </>
    );
};

const Name = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[5],
}));

const Root = styled('div')({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 41,
    paddingTop: 20,
    paddingBottom: 20,
    paddingRight: 45,
    height: 52,
    width: '100%',
    boxSizing: 'border-box',
});

const Buttons = styled('div')({
    display: 'flex',
    columnGap: 16,
});

const CalloutButton = styled(Button)({
    maxWidth: 32,
});

const DetailsButton = styled(Button)({
    width: 180,
});

const ShopName = styled('div')(({ theme }) => ({
    color: Colors.Grey5,
    ...theme.typography.h6Roboto,
    padding: '3px 0',
    fontWeight: 400,
}));

export default Header;
