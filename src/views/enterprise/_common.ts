import { useQuery } from '@tanstack/react-query';
import { EnterpriseAppointmentsApi } from 'api/enterprise';
import { useMemo } from 'react';

export function useAppointmentSettingsForShop(shopId: string | null) {
    const allSettings = useAppointmentSettings();
    return useMemo(
        () => allSettings?.find((x) => x.repairShopKey === shopId),
        [shopId, allSettings]
    );
}

export function useValetServiceEnabledForShops(shopIds: string[]) {
    const allSettings = useAppointmentSettings();
    return useMemo(() => {
        const map: Record<string, boolean> = {};
        for (const id of shopIds) {
            if (id) {
                const found = allSettings?.find((x) => x.repairShopKey === id);
                map[id] = found?.valetServiceEnabled ?? false;
            }
        }
        return map;
    }, [allSettings, shopIds]);
}

export function useAppointmentSettings(staleTime: number = 10000) {
    const { data } = useQuery(
        ['enterprise', 'appointments', 'settings'],
        () => EnterpriseAppointmentsApi.getEnterpriseAppointmentSettings(),
        {
            cacheTime: Infinity,
            staleTime,
        }
    );

    return data;
}
