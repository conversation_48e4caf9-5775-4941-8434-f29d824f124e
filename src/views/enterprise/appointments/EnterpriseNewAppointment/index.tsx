import { styled } from '@mui/material';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import { EnterpriseAppointmentsApi } from 'api/enterprise';
import { ApiError } from 'api/error';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { motion } from 'framer-motion';
import isEqual from 'lodash/isEqual';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { createSelector } from 'reselect';
import { hasCode, isErrorResponse } from 'services/Server';
import { useAppSelector } from 'store';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import { useDebounce } from 'use-debounce';
import AppointmentNewHeader from 'views/Appointments/common/AppointmentNewHeader';
import CustomerHistory from 'views/Appointments/CustomerHistory';
import PageContent, { WidePageLayout } from 'views/Components/Page';
import { useAppointmentSettingsForShop } from 'views/enterprise/_common';
import AppointmentPartB from '../AppointmentPartB';
import AppointmentStepOne from '../AppointmentStepOne';
import AppointmentStepThree from '../AppointmentStepThree';
import AppointmentStepTwo from '../AppointmentStepTwo';
import { withClearAppointmentState } from '../ClearAppointmentStateGuard';
import {
    useEnterpriseCreateAppointmentMutation,
    usePreselectedCustomer,
    usePreselectedDateTimeApplier,
    usePreselectedShopId,
} from './helpers';

const Root = styled('div')({
    display: 'grid',
    gridTemplate: '1fr auto / 9fr 3fr',
    gap: 15,
    padding: '33px 0',
    '& .MuiDivider-root': {
        margin: '22px 0',
        backgroundColor: 'var(--neutral3)',
    },
    '& > aside': {},
    '& > :first-child': {
        gridColumn: '1 / 3',
    },
});

const selectShopId = createSelector(selectEditData, (e) => e.enterpriseData.shopId);
const selectAppointmentNumber = createSelector(selectEditData, (e) => e.appointmentData.number);

function useNewAppointmentPageInit() {
    usePreselectedShopId();
    usePreselectedCustomer();
    usePreselectedDateTimeApplier();
}

function useNewAppointmentPageData() {
    const shopId = useAppSelector(selectShopId);
    const requiresAppointmentNumber = !(
        useAppointmentSettingsForShop(shopId)?.activateAutomaticAppointmentNumber ?? false
    ); // TODO (MB) implement

    const appointmentNumber = useAppSelector(selectAppointmentNumber);
    const [debouncedNumber] = useDebounce(appointmentNumber, 300);
    const [isAppointmentNumberInUse, setIsAppointmentNumberInUse] = useState(false);

    useEffect(() => {
        if (!shopId || !debouncedNumber?.trim()) {
            setIsAppointmentNumberInUse(false);
            return;
        }

        const checkIsAppointmentNumberInUse = async () => {
            const result = await EnterpriseAppointmentsApi.isAppointmentNumberInUse(
                shopId,
                debouncedNumber
            );
            setIsAppointmentNumberInUse(result);
        };

        checkIsAppointmentNumberInUse();
    }, [shopId, debouncedNumber]);

    const selector = useMemo(
        () =>
            createSelector(
                appointmentEditorStateSelectors.createAppointmentDataValidSelector(
                    selectEditData,
                    requiresAppointmentNumber,
                    isAppointmentNumberInUse
                ),
                appointmentEditorStateSelectors.createNewAppointmentModifiedSelector(
                    selectEditData
                ),
                (valid, modified) => ({ valid, modified })
            ),
        [requiresAppointmentNumber, isAppointmentNumberInUse]
    );
    return useAppSelector(selector, isEqual);
}

function EnterpriseNewAppointment() {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const toasters = useToasters();

    useNewAppointmentPageInit();
    const shopId = useAppSelector(selectShopId);
    const { valid, modified } = useNewAppointmentPageData();

    const { vehicle, customer } = useAppSelector(selectEditData);

    const [detailsVisibility, setDetailsVisibility] = useState<'visible' | 'hidden'>('hidden');

    const enabledCustomerHistory = !!customer && !!vehicle;

    const toggleCustomerHistory = () =>
        detailsVisibility === 'hidden'
            ? setDetailsVisibility('visible')
            : setDetailsVisibility('hidden');

    const createAppointment = useEnterpriseCreateAppointmentMutation(
        () => navigate(ENTERPRISE_ROUTES.APPOINTMENTS),
        (e: ApiError) => {
            const errorData = e.response?.data;
            if (isErrorResponse(errorData)) {
                if (hasCode(errorData, 'Appointments.InvalidAppointmentNumber')) {
                    toasters.danger(
                        t('appointments.appointmentNumberIsDuplicated'),
                        t('toasters.errorOccurred')
                    );
                } else {
                    toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                }
            } else {
                console.warn('unknown error occurred while updating the entry: ', e);
            }
        }
    );

    return (
        <CustomersSearchApiProvider mode="enterprise" shopId={shopId} key={`shop-${shopId}`}>
            <WidePageLayout>
                <PageContent paddedX>
                    <Root>
                        <AppointmentNewHeader
                            loading={createAppointment.isLoading}
                            valid={valid}
                            modified={modified}
                            onCancel={() => navigate(ENTERPRISE_ROUTES.APPOINTMENTS)}
                            onSave={() => createAppointment.mutate()}
                            enabledCustomerHistory={enabledCustomerHistory}
                            toggleCustomerHistory={toggleCustomerHistory}
                        />
                        <section id="appointment-main">
                            <AppointmentStepOne repairShopKey={shopId ?? undefined} />
                            <AppointmentStepTwo />
                            <AppointmentStepThree />
                        </section>

                        <aside>
                            <AppointmentPartB />
                        </aside>
                        <MotionPreview
                            animate={detailsVisibility}
                            variants={{
                                visible: { right: '0px', opacity: 1 },
                                hidden: { right: '-430px', opacity: 0 },
                            }}
                            transition={{ duration: 0.15, ease: 'easeOut' }}
                        >
                            <CustomerHistory
                                onClose={() => {
                                    setDetailsVisibility('hidden');
                                }}
                            />
                        </MotionPreview>
                    </Root>
                </PageContent>
            </WidePageLayout>
        </CustomersSearchApiProvider>
    );
}

const MotionPreview = motion(
    styled('div')({
        zIndex: 3,
        height: 'calc(100% - var(--header-bar-height))',
        display: 'flex',
        position: 'fixed',
        top: 52,
        right: 0,
    })
);

export default withClearAppointmentState(EnterpriseNewAppointment);
