import { useMutation, useQuery } from '@tanstack/react-query';
import { AppointmentCreateDto } from 'api/appointments';
import { EnterpriseAppointmentsApi, EnterpriseCustomersApi } from 'api/enterprise';
import { ApiError } from 'api/error';
import { isUuid } from 'common/constants';
import useQueryParam from 'common/hooks/useQueryParam';
import { DateTime } from 'luxon';
import { useEffect, useMemo, useRef } from 'react';
import { useStore } from 'react-redux';
import { createSelector } from 'reselect';
import { useAppDispatch } from 'store';
import {
    enterpriseAppointmentsActions,
    updateAppointmentData,
} from 'store/slices/enterprise/appointments';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { useLocations } from 'store/slices/enterprise/locations';

const selector = createSelector(selectEditData, (edit) => ({
    shopId: edit.enterpriseData.shopId,
    customer: edit.customer,
    vehicle: edit.vehicle,
    appointment: edit.appointmentData,
}));

export function useEnterpriseCreateAppointmentMutation(
    onSuccess: (id: string) => void,
    onError: (err: ApiError) => void
) {
    const store = useStore();

    return useMutation(
        async () => {
            const data = selector(store.getState());
            if (!data.shopId) throw new Error('cannot create appointment without a shopId');
            if (!data.appointment.customerId)
                throw new Error('cannot create appointment without a customer');

            const request: AppointmentCreateDto = {
                declinedItemReasonsForView: null,
                reasons: data.appointment.reasons.map((x) => x.id),
                customReasons: data.appointment.customReasons || [],
                observations: data.appointment.observations,
                promisedAt: data.appointment.deliverDate,
                startsAt: data.appointment.startsAt,
                originId: data.appointment.origin?.id ?? null,
                withValetService: false,
                serviceAdvisorId: data.appointment.serviceAdvisorId,
                number: data.appointment.number,
                duration: null, // default duration since UI doesn't allow to chose
                customer: {
                    id: data.appointment.customerId,
                    firstName: data.appointment.customerFirstName,
                    lastName: data.appointment.customerLastName,
                    email: data.appointment.customerEmail,
                    taxIdentification: data.appointment.customerTaxId,
                    mobile: data.appointment.customerMobile,
                },
                vehicle: data.appointment.vehicleId
                    ? {
                          id: data.appointment.vehicleId,
                          plates: data.appointment.vehiclePlates,
                          brand: data.appointment.vehicleBrand,
                          model: data.appointment.vehicleModel,
                          year: data.appointment.vehicleYear,
                          color: data.appointment.vehicleColor,
                          vin: data.appointment.vehicleVin,
                      }
                    : null,
                notes: data.appointment.notes,
            };
            return await EnterpriseAppointmentsApi.create(data.shopId, request);
        },
        {
            onSuccess,
            onError,
        }
    );
}

export function usePreselectedShopId() {
    const [shopId] = useQueryParam('shopId');
    const locations = useLocations();
    const applied = useRef(false);
    const dispatch = useAppDispatch();

    useEffect(() => {
        if (applied.current) return;
        if (!shopId || !isUuid(shopId) || !locations?.length) return;

        const location = locations.find((x) => x.repairShopId === shopId);
        if (location) {
            dispatch(enterpriseAppointmentsActions.setShop(location));
            applied.current = true;
        }
    }, [locations, shopId, dispatch]);
}

export function usePreselectedCustomer() {
    const dispatch = useAppDispatch();
    const [shopId] = useQueryParam('shopId');
    const [customerId] = useQueryParam('customerId');

    useQuery(
        ['customers', 'details', shopId, customerId],
        () => EnterpriseCustomersApi.getCustomerPreview(shopId || '', customerId || ''),
        {
            enabled: !!customerId && !!shopId,
            onSuccess: (customer) => {
                dispatch(
                    updateAppointmentData({
                        customer: {
                            id: customer.customerId,
                            firstName: customer.firstName,
                            lastName: customer.lastName,
                            mobile: customer.mobile,
                            taxIdentification: customer.taxIdentification,
                            email: customer.email,
                        },
                        vehicle: {
                            brand: customer.vehicle.brand,
                            color: customer.vehicle.color,
                            id: customer.vehicle.vehicleId,
                            model: customer.vehicle.model,
                            plates: customer.vehicle.plates,
                            vin: customer.vehicle.vin,
                            year: customer.vehicle.year,
                        },
                        appointment: {
                            customerEmail: customer.email,
                            customerFirstName: customer.firstName,
                            customerLastName: customer.lastName,
                            customerId: customer.customerId,
                            customerMobile: customer.mobile,
                            customerTaxId: customer.taxIdentification,
                            vehicleBrand: customer.vehicle.brand,
                            vehicleColor: customer.vehicle.color,
                            vehicleId: customer.vehicle.vehicleId,
                            vehicleModel: customer.vehicle.model,
                            vehiclePlates: customer.vehicle.plates,
                            vehicleVin: customer.vehicle.vin,
                            vehicleYear: customer.vehicle.year,
                        },
                    })
                );
            },
        }
    );
}

function usePreselectedDate() {
    const [date] = useQueryParam('date');

    return useMemo(() => {
        if (date) {
            try {
                return DateTime.fromFormat(date, 'yyyyMMddHHmm');
            } catch (e: unknown) {
                return null;
            }
        } else {
            return null;
        }
    }, [date]);
}

export function usePreselectedDateTimeApplier() {
    const dateTime = usePreselectedDate();
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(enterpriseAppointmentsActions.setPreselectedDate(dateTime?.toISO() ?? null));
    }, [dateTime, dispatch]);
}

export function usePreselectedServiceAdvisorApplier() {
    const [advisorId] = useQueryParam('advisor');
    const dispatch = useAppDispatch();
    useEffect(() => {
        if (advisorId && isUuid(advisorId)) {
            dispatch(enterpriseAppointmentsActions.setPreselectedAdvisorId(advisorId));
        }
    }, [advisorId, dispatch]);
}
