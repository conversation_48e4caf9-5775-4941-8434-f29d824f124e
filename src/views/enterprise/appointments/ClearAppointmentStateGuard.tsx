import { ComponentProps, ComponentType, useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';

export default function ClearAppointmentStateGuard({ children }: React.PropsWithChildren<{}>) {
    const dispatch = useAppDispatch();
    const [ready, setReady] = useState(false);

    useEffect(() => {
        dispatch(enterpriseAppointmentsActions.resetAppointmentData());
        setReady(true);
    }, [dispatch]);

    if (!ready) return null;
    return <>{children}</>;
}

export function withClearAppointmentState<T extends ComponentType<any>>(
    Component: T
): ComponentType<ComponentProps<T>> {
    return (props: ComponentProps<T>) => (
        <ClearAppointmentStateGuard>
            <Component {...props} />
        </ClearAppointmentStateGuard>
    );
}
