import {
    Autocomplete,
    Backdrop,
    Grow,
    ListItemButton,
    Popper,
    PopperProps,
    TextField,
    UseAutocompleteProps,
    inputBaseClasses,
    styled,
    useMediaQuery,
} from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { CircleIcon } from 'common/components/Icons/CircleIcon';
import { CrossedCircleIcon } from 'common/components/Icons/CrossedCircleIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { HTMLAttributes, useCallback, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/enterprise/appointments';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { ServiceAdvisorDto } from '../../../../../../api/enterprise/appointments';
import { useServiceAdvisors } from '../../_common';

const Input = styled(TextField)(({ theme }) => ({
    minWidth: 100,
    cursor: 'pointer',

    [`& > .${inputBaseClasses.root}`]: {
        ...theme.typography.h6Inter,
        color: theme.palette.primary.main,
        display: 'flex',
        alignItems: 'center',
        paddingTop: 5,
        paddingBottom: 5,
        height: 32,
        padding: '0 7px !important',
        borderRadius: 20,
    },
    [`& .${inputBaseClasses.input}`]: {
        cursor: 'pointer',
        '&::placeholder': {
            color: 'var(--cm1)',
            fontWeight: 'bold',
            opacity: 1,
        },
    },
}));

const SAutocomplete = styled(Autocomplete)({
    cursor: 'pointer',

    '& .MuiFormControl-root': {
        margin: 0,
    },
    '& fieldset': {
        borderColor: 'var(--cm1) !important',
    },
}) as typeof Autocomplete;

export default function AdvisorsSelector() {
    const { advisors: selectedAdvisors, locations: selectedLocations } =
        useAppSelector(selectFilters);
    const advisors = useServiceAdvisors();
    const { t } = useAppTranslation();
    const shorter = useMediaQuery('(max-width:1300px)');

    const dispatch = useAppDispatch();

    const handleChange: UseAutocompleteProps<
        ServiceAdvisorDto | null,
        true,
        true,
        false
    >['onChange'] = (event, newValue, reason, details) => {
        const isAllAdvisorsOption = details?.option === null;

        if (reason === 'selectOption' && isAllAdvisorsOption) {
            dispatch(
                setFilters({
                    advisors: [],
                })
            );
        } else {
            dispatch(
                setFilters({
                    advisors: newValue.filter((x) => x !== null).map((x) => x!.id),
                })
            );
        }
        return;
    };

    const filteredOptions = useMemo(() => {
        return advisors.filter(
            (x) => selectedLocations.length == 0 || selectedLocations.includes(x.repairShopId)
        );
    }, [advisors, selectedLocations]);

    const value = useMemo(
        () =>
            selectedAdvisors.length === 0
                ? [null]
                : selectedAdvisors
                      .map((id) => filteredOptions?.find((x) => x.id === id))
                      .filter((x): x is ServiceAdvisorDto => !!x),
        [selectedAdvisors, filteredOptions]
    );

    const [focus, setFocus] = useState(false);
    const placeholder = useMemo(() => {
        if (focus) return t('commonLabels.search') + '...';
        if (selectedAdvisors.length === 0) return t('appointments.allAdvisors');

        const advisor =
            filteredOptions?.find((x) => x.id === selectedAdvisors[0])?.name ?? selectedAdvisors[0];
        if (selectedAdvisors.length > 1) return `${advisor} +${selectedAdvisors.length - 1}`;
        return advisor;
    }, [t, selectedAdvisors, filteredOptions, focus]);

    return (
        <>
            <SAutocomplete<ServiceAdvisorDto | null, true, true>
                style={{ width: shorter ? 120 : 200 }}
                popupIcon={<DownIcon style={{ backgroundColor: '#fff' }} />}
                value={value}
                noOptionsText={t('commonLabels.noDataSelector')}
                getOptionLabel={(x) => (x ? x.name : '')}
                onChange={handleChange}
                renderInput={(params) => {
                    return (
                        <Input
                            {...params}
                            placeholder={placeholder}
                            variant="outlined"
                            margin="normal"
                        />
                    );
                }}
                renderOption={useCallback(
                    (props: HTMLAttributes<HTMLLIElement>, o: ServiceAdvisorDto | null) => {
                        if (!o)
                            return (
                                <ListItemButton component="li" {...props}>
                                    {selectedAdvisors.length === 0 ? (
                                        <CheckBoxIcon />
                                    ) : (
                                        <UncheckBoxIcon />
                                    )}
                                    <CrossedCircleIcon />
                                    {t('appointments.allAdvisors')}
                                </ListItemButton>
                            );
                        return (
                            <ListItemButton component="li" {...props}>
                                {selectedAdvisors.includes(o.id) ? (
                                    <CheckBoxIcon />
                                ) : (
                                    <UncheckBoxIcon />
                                )}
                                <CircleIcon size={20} fill={o.color ?? '#aaa'} /> {o.name}
                            </ListItemButton>
                        );
                    },
                    [t, selectedAdvisors]
                )}
                options={[null, ...(filteredOptions ?? [])]}
                disableCloseOnSelect
                disableClearable
                renderTags={(_) => null}
                onFocus={() => setFocus(true)}
                onBlur={() => setFocus(false)}
                openOnFocus
                multiple
                PopperComponent={PopperComponent}
            />
        </>
    );
}

const CustomPopper = styled(Popper)(({ theme }) => ({
    '& .MuiPaper-root': {
        boxShadow: 'none',
        border: '1px solid var(--neutral5)',
        '& .MuiAutocomplete-option': {
            '&:hover': {
                backgroundColor: rgba(theme.palette.primary.main, 0.03),
            },
            '&[aria-selected=true]': {
                backgroundColor: rgba(theme.palette.primary.main, 0.08),
                '&:hover': {
                    backgroundColor: rgba(theme.palette.primary.main, 0.12),
                },
            },
        },
    },
    '& .MuiAutocomplete-listbox': {
        ...scrollbarStyle(),
    },
    '& .MuiAutocomplete-noOptions': {
        color: 'var(--cm1)',
        fontWeight: 'bold',
        textAlign: 'center',
    },
}));

const PopperComponent: React.ComponentType<PopperProps> = ({ children, open, ...props }) => {
    return (
        <Backdrop style={{ zIndex: 3, backgroundColor: 'transparent' }} open={open}>
            <CustomPopper transition open={open} {...props}>
                {({ TransitionProps }) => (
                    <Grow {...TransitionProps}>{children as React.ReactElement<any, any>}</Grow>
                )}
            </CustomPopper>
        </Backdrop>
    );
};
