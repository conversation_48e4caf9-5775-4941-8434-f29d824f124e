import { useMediaQuery } from '@mui/material';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import Tooltip from 'common/components/Tooltip';
import { ENTERPRISE_ROUTES } from 'common/constants/RoutesDefinition';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useAppSelector } from 'store';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useAppointmentSettings } from 'views/enterprise/_common';

export default function CreateAppointmentButton() {
    const { t } = useAppTranslation();
    const { locations: selectedLocations } = useAppSelector(selectFilters);
    const appointmentsSettings = useAppointmentSettings();
    const userPermission = useSelector(selectUserPermission);

    const filteredAppointmentSettings = useMemo(() => {
        return appointmentsSettings?.filter(
            (x) => selectedLocations.length === 0 || selectedLocations.includes(x.repairShopKey)
        );
    }, [appointmentsSettings, selectedLocations]);

    const allLocationsWithSynchronizeAppointmentsEnabled =
        filteredAppointmentSettings?.every((x) => x.synchronizeAppointmentsEnabled) ?? true;

    const isSmall = useMediaQuery('(max-width: 1440px)');

    return (
        <Tooltip
            content={t('appointments.appointmentMustBeCreatedUsing3rdPartySoftware')}
            disabled={!allLocationsWithSynchronizeAppointmentsEnabled}
        >
            <div>
                <Button
                    href={ENTERPRISE_ROUTES.APPOINTMENTS_NEW}
                    cmosVariant={'filled'}
                    disabled={
                        allLocationsWithSynchronizeAppointmentsEnabled ||
                        !userPermission.allowEditAppointments
                    }
                    label={isSmall ? undefined : t('appointments.newAppointment')}
                    Icon={isSmall ? PlusIcon : undefined}
                    sx={{
                        width: isSmall ? undefined : '170px',
                    }}
                />
            </div>
        </Tooltip>
    );
}
