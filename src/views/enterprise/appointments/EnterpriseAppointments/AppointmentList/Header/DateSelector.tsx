import { Box, Button, IconButton, Popover, styled, useMediaQuery } from '@mui/material';
import { capitalizeFirstLetter } from 'common/Helpers';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { LeftIcon } from 'common/components/Icons/LeftIcon';
import { RightIcon } from 'common/components/Icons/RightIcon';
import Calendar from 'common/components/Inputs/Calendar';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useCallback, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { moveDate, setFilters } from 'store/slices/enterprise/appointments';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';

const StyledIconButton = styled(IconButton)({
    height: 'fit-content',
    padding: 2,
});

const ExpanderButton = styled(Button)({
    borderRadius: 0,
    height: '100%',
    maxHeight: '100%',
});

const CalendarDate = styled('div')(({ theme }) => ({
    all: 'initial',
    cursor: 'inherit',
    color: theme.palette.neutral[7],
    marginLeft: 5,
    padding: 5,
    ...theme.typography.h5Inter,
    borderRadius: 6,
}));

const CalendarPopover = styled(Popover)({
    zIndex: 5,
    '& .MuiPaper-root': {
        border: `1px solid var(--neutral3)`,
        borderRadius: 10,
    },
});

export default function DateSelector() {
    const { date, mode } = useAppSelector(selectFilters);
    const isSmall = useMediaQuery('(max-width: 900px)');
    const { t, i18n } = useAppTranslation();

    const dispatch = useAppDispatch();

    const dateNode = useMemo(() => {
        const m = moment(date);
        if (mode === 'week') {
            const start = m.startOf('isoWeek');
            const end = start.clone().add(6, 'd');
            const dateFormat = i18n.language.includes('en') ? 'MMM DD' : 'DD MMM';
            const range = `${start.format(dateFormat)} - ${end.format(dateFormat)}`;
            if (start.year() !== moment().year()) {
                return (
                    <>
                        <span>{range}</span> <br />
                        <small>
                            {start.year()}
                            {end.year() !== start.year() ? `-${end.year()}` : undefined}
                        </small>
                    </>
                );
            }
            return range;
        } else if (mode === 'day') {
            if (m.isSame(moment(), 'd')) {
                return t('commonLabels.today') + ', ' + m.format('MMM DD');
            }
            return (
                capitalizeFirstLetter(m.format('dddd')) +
                ', ' +
                capitalizeFirstLetter(m.format('MMM DD'))
            );
        } else {
            return 'Unknown mode: ' + mode;
        }
    }, [date, mode, t]);

    const move = useCallback(
        (change: number) => {
            dispatch(moveDate(change));
        },
        [dispatch]
    );

    const [expanderEl, setExpanderEl] = useState<HTMLButtonElement | null>(null);
    const [calendarOpen, setCalendarOpen] = useState(false);

    return (
        <Box display="flex" alignItems="center" height="100%">
            <ExpanderButton onClick={() => setCalendarOpen(true)} ref={setExpanderEl}>
                <CalendarIcon size={32} fill="var(--cm1)" />

                <CalendarDate
                    style={{ width: mode === 'day' ? (isSmall ? 120 : 140) : isSmall ? 100 : 125 }}
                >
                    {dateNode}
                </CalendarDate>
            </ExpanderButton>
            <CalendarPopover
                elevation={2}
                keepMounted
                anchorEl={expanderEl}
                open={calendarOpen}
                transitionDuration={150}
                onClose={() => setCalendarOpen(false)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                sx={{ zIndex: 101 }}
            >
                <Calendar
                    onChange={function (date: Date | null): void {
                        if (date) {
                            setCalendarOpen(false);
                            dispatch(
                                setFilters({
                                    date: moment(date).format('YYYY-MM-DD'),
                                })
                            );
                        }
                    }}
                    value={new Date(date)}
                />
            </CalendarPopover>
            <StyledIconButton onClick={() => move(-1)} style={{ transform: 'translateX(2px)' }}>
                <LeftIcon size={30} />
            </StyledIconButton>
            <StyledIconButton onClick={() => move(1)} style={{ transform: 'translateX(-2px)' }}>
                <RightIcon size={30} />
            </StyledIconButton>
        </Box>
    );
}
