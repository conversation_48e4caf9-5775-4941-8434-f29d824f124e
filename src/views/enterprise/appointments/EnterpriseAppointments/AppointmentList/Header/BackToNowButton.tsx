import { Box } from '@mui/material';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import moment from 'moment';
import { useCallback, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { setFilters } from 'store/slices/enterprise/appointments';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { useNow } from '../../_common';

export default function BackToNowButton() {
    const { mode, date } = useAppSelector(selectFilters);
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const returnToNow = useCallback(
        () =>
            dispatch(
                setFilters({
                    date: moment().format('YYYY-MM-DD'),
                })
            ),
        [dispatch]
    );

    const nowDate = useNow('day');
    const shown = useMemo(() => {
        const now = moment(nowDate);

        if (mode === 'week') {
            return (
                now.startOf('isoWeek').format('YYYY-MM-DD') !==
                moment(date).startOf('isoWeek').format('YYYY-MM-DD')
            );
        } else {
            return !now.isSame(date, 'd');
        }
    }, [mode, date, nowDate]);

    if (!shown) return null;

    return (
        <Box display="flex" alignItems="center" justifyContent="center" height="100%">
            <Button
                label={t('appointments.backToToday')}
                cmosVariant={'stroke'}
                onClick={returnToNow}
                color={Colors.Neutral3}
            />
        </Box>
    );
}
