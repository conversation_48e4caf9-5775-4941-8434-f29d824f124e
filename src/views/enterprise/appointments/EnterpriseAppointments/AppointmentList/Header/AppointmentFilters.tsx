import { useMediaQuery } from '@mui/material';
import LocationSelector from 'common/components/LocationSelector';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/enterprise/appointments';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import AdvisorsSelector from './AdvisorsSelector';
import AppointmentStatusSelector from './AppointmentStatusSelector';
import CombinedAppointmentFilters from './CombinedAppointmentFilters';
import ModeSelector from './ModeSelector';

export default function AppointmentsFilters() {
    const { locations: selectedLocations, mode } = useAppSelector(selectFilters);
    const dispatch = useAppDispatch();
    const isSmall = useMediaQuery('(max-width: 1440px)');

    let body: React.ReactElement;

    if (isSmall) {
        body = <CombinedAppointmentFilters />;
    } else {
        body = (
            <>
                <ModeSelector />
                <AppointmentStatusSelector />
                <AdvisorsSelector />
            </>
        );
    }

    return (
        <>
            {body}
            <LocationSelector
                value={selectedLocations}
                disableSelectNewOption={mode == 'day' && selectedLocations.length >= 30}
                onChange={(locations) => {
                    dispatch(
                        setFilters({
                            locations: locations,
                            advisors: [],
                        })
                    );
                }}
            />
        </>
    );
}
