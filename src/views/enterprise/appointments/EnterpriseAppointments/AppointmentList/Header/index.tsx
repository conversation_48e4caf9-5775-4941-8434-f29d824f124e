import { styled } from '@mui/material';
import AppointmentsFilters from './AppointmentFilters';
import CreateAppointmentButton from './CreateAppointmentButton';
import DateSelector from './DateSelector';

const Root = styled('div')(({ theme }) => ({
    '&::after, &::before': {
        width: 1,
        backgroundColor: theme.palette.neutral[5],
        content: '""',
        display: 'block',
        left: 0,
        height: 30,
        top: '50%',
        transform: 'translateY(-50%)',
        position: 'absolute',
    },
    '&::after': {
        right: 0,
        left: 'initial',
    },
    gap: 10,
    position: 'relative',
    margin: '0 30px',
    padding: '0 30px',
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItem: 'center',
    color: theme.palette.neutral[5],
    alignItems: 'center',
}));

const Filler = styled('div')({
    flexGrow: 1,
});

export default function Header() {
    return (
        <Root>
            <DateSelector />
            <Filler />
            <AppointmentsFilters />
            <CreateAppointmentButton />
        </Root>
    );
}
