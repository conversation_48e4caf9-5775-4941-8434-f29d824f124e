import { MenuItem, useMediaQuery } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { SSelect } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/enterprise/appointments';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import {
    APPOINTMENTS_STATUS_TRANSLATION_KEYS,
    AppointmentStatusMenu,
} from 'views/Appointments/AppointmentList/Header/AppointmentStatusSelector';

const ALL = 'ALL';

export default function AppointmentStatusSelector() {
    const { statuses } = useAppSelector(selectFilters);
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const shorter = useMediaQuery('(max-width:1400px)');

    const setStatuses = useCallback(
        (value: string[]) => {
            if (value.length > 0 && value[value.length - 1] === ALL) {
                dispatch(
                    setFilters({
                        statuses: [],
                    })
                );
            } else {
                dispatch(
                    setFilters({
                        statuses: value.filter((x) => x !== ALL) as AppointmentStatusMenu[],
                    })
                );
            }
        },
        [dispatch]
    );

    return (
        <SSelect
            style={{ width: shorter ? 120 : 200 }}
            value={statuses.length === 0 ? [ALL] : statuses}
            multiple
            displayEmpty
            renderValue={(x) => {
                const s = x as AppointmentStatusMenu[];
                if (s.length === 0 || (s.length === 1 && (s[0] as string) === ALL))
                    return t('appointments.allAppointments');
                const first = t(APPOINTMENTS_STATUS_TRANSLATION_KEYS[s[0]]);
                if (s.length > 1) return `${first} +${s.length - 1}`;
                return first;
            }}
            onChange={(e) => setStatuses(e.target.value as string[])}
        >
            <MenuItem value={ALL}>
                {statuses.length === 0 ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                {t('appointments.allAppointments')}
            </MenuItem>
            {Object.entries(APPOINTMENTS_STATUS_TRANSLATION_KEYS).map(([value, tKey]) => (
                <MenuItem key={value} value={value}>
                    {statuses.includes(value as AppointmentStatusMenu) ? (
                        <CheckBoxIcon />
                    ) : (
                        <UncheckBoxIcon />
                    )}
                    {t(tKey)}
                </MenuItem>
            ))}
        </SSelect>
    );
}
