import { AppointmentStatus } from 'api/appointments';
import { AbsenceDto } from 'api/users';
import { TimeSpan } from 'api/utils/format';
import { AppointmentWorkingDayDto } from 'datacontracts/Scheduler';
import { TFunction } from 'i18next';
import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import { DateTime } from 'luxon';
import moment from 'moment';
import React, { memo, useContext, useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { useAppointmentSettings } from 'views/enterprise/_common';

export type Appointment = {
    number: string;
    orderNumber?: string;
    repairShopId: string;
    status: AppointmentStatus;
    customerId?: string;
    customerFirstName?: string;
    customerLastName?: string;
    customerEmail?: string;
    customerMobile?: string;
    userServiceId: string;
    userServiceDisplayName: string;
    userServiceAdvisorColor: string;
    duration?: number;
    vehiclePlates?: string;
    vehicleVIN?: string;
    vehicleMake?: string;
    vehicleModel?: string;
    vehicleYear?: string;
    vehicleColor?: string;
    id: string;
    startDate: string;
    endDate: string;
    withValetService: boolean;
};

export type AppointmentsGroup = {
    list: Appointment[];
    groupKey: string;
    key: string;
};

module absenceGridItem {
    export type Slice = {
        originalAbsence: AbsenceDto;
        start: DateTime;
        end: DateTime;
        duration: number;
    };

    function getSlice(originalAbsence: AbsenceDto, day: DateTime): Slice | null {
        const startOfDay = day;
        const endOfDay = day.plus({ day: 1 });
        const startsAt = DateTime.fromISO(originalAbsence.startsAt);
        const endsAt = DateTime.fromISO(originalAbsence.endsAt);

        if (startsAt >= endOfDay || endsAt <= startOfDay) return null;

        const blockStart = startsAt > startOfDay ? startsAt : startOfDay;
        const blockEnd = endsAt > endOfDay ? endOfDay : endsAt;

        return {
            originalAbsence,
            start: blockStart,
            end: blockEnd,
            duration: blockEnd.diff(blockStart).as('minutes'),
        };
    }

    export function getGroupKey(group: Group): string {
        return `g/${group.start.toMillis()}-${group.end.toMillis()}/${
            group.parentGroupKey
        }/${group.absences.map((x) => x.originalAbsence.id).join('-')}`;
    }

    /**
     * group of absence slices that overlap with each other
     */
    export type Group = {
        absences: Slice[];
        start: DateTime;
        end: DateTime;
        parentGroupKey: string;
    };

    function intersectsWithGroup(a: Slice, g: Group): boolean {
        return a.start < g.end && a.end > g.start;
    }

    function tryAddToGroup(a: Slice, g: Group): boolean {
        if (intersectsWithGroup(a, g)) {
            g.absences.push(a);
            g.start = g.start < a.start ? g.start : a.start;
            g.end = g.end > a.end ? g.end : a.end;
            return true;
        }

        return false;
    }

    function createEmptyGroup(slice: Slice, parentGroupKey: string): Group {
        return {
            absences: [slice],
            start: slice.start,
            end: slice.end,
            parentGroupKey,
        };
    }

    /**
     *
     * @param absences list of absences
     * @param days list of days in the format yyyy-MM-dd
     * @returns mapping of day -> list of appointment group
     */
    function groupIntersectingAbsencesByDays(
        absences: AbsenceDto[],
        days: string[]
    ): Record<string, Group[]> {
        const dayGroups: Record<string, Group[]> = {};
        const absencesSorted = sortBy(absences, (x) => DateTime.fromISO(x.startsAt).toMillis());

        for (const dayStr of days) {
            const groups: Group[] = [];
            dayGroups[dayStr] = groups;
            const day = DateTime.fromFormat(dayStr, 'yyyy-MM-dd');

            for (const absence of absencesSorted) {
                const slice = getSlice(absence, day);
                if (slice === null) {
                    // slice is empty meaning this absence does not overlap with the current day
                    continue;
                }

                if (groups.length === 0) {
                    groups.push({
                        start: slice.start,
                        end: slice.end,
                        absences: [slice],
                        parentGroupKey: dayStr,
                    });
                } else {
                    const lastGroup = groups[groups.length - 1];
                    if (!tryAddToGroup(slice, lastGroup)) {
                        groups.push(createEmptyGroup(slice, dayStr));
                    }
                }
            }
        }

        return dayGroups;
    }

    function groupIntersectingAbsencesByUser(
        userIds: string[],
        absences: AbsenceDto[],
        dayFilter: string,
        zone: string = 'local'
    ) {
        const userGroups: Record<string, Group[]> = {};
        for (const userId of userIds) userGroups[userId] = [];
        const day = DateTime.fromFormat(dayFilter, 'yyyy-MM-dd', { zone });

        for (const absence of absences) {
            if (absence.userId && !userIds.includes(absence.userId)) continue;
            const slice = getSlice(absence, day);
            if (slice === null) continue;

            const applicableUserIds = absence.userId ? [absence.userId] : userIds;

            for (const userId of applicableUserIds) {
                const list = userGroups[userId];
                if (list.length === 0) {
                    list.push(createEmptyGroup(slice, userId));
                } else {
                    const lastGroup = list[list.length - 1];
                    if (!tryAddToGroup(slice, lastGroup)) {
                        list.push(createEmptyGroup(slice, userId));
                    }
                }
            }
        }

        return userGroups;
    }

    export function createAbsencesGridItems(
        absences: AbsenceDto[],
        days: string[]
    ): AppointmentGridItem[] {
        const dayGroups = groupIntersectingAbsencesByDays(absences, days);
        const items: AppointmentGridItem[] = [];

        for (const groupKey in dayGroups) {
            const groups = dayGroups[groupKey];

            for (const group of groups) {
                items.push({
                    groupKey,
                    absenceGroup: group,
                    duration: group.end.diff(group.start).as('minutes'),
                    startsAt: group.start.hour * 60 + group.start.minute,
                    key: absenceGridItem.getGroupKey(group),
                    type: 'absence',
                });
            }
        }

        return items;
    }

    export function createAbsencesGridItemsForDay(
        absences: AbsenceDto[],
        day: string,
        userIds: string[],
        zone: string = 'local'
    ): AppointmentGridItem[] {
        const groupsMapping = groupIntersectingAbsencesByUser(userIds, absences, day, zone);
        const items: AppointmentGridItem[] = [];

        for (const groupKey in groupsMapping) {
            const groups = groupsMapping[groupKey];
            for (const group of groups) {
                items.push({
                    groupKey,
                    absenceGroup: group,
                    duration: group.end.diff(group.start).as('minutes'),
                    startsAt: group.start.hour * 60 + group.start.minute,
                    key: absenceGridItem.getGroupKey(group),
                    type: 'absence',
                });
            }
        }

        return items;
    }
}

export type AbsenceSlice = absenceGridItem.Slice;
export type AbsenceGroup = absenceGridItem.Group;

export const createAbsencesGridItems = absenceGridItem.createAbsencesGridItems;
export const createAbsencesGridItemsForDay = absenceGridItem.createAbsencesGridItemsForDay;

export type AppointmentGridItem = {
    key: string;
    groupKey: string;
    startsAt: number;
    duration: number;
} & (
    | {
          type: 'appointments';
          group: AppointmentsGroup;
      }
    | {
          type: 'absence';
          absenceGroup: AbsenceGroup;
      }
);

export function createAppoitmentsGridItems(
    appointmentGroups: AppointmentsGroup[]
): AppointmentGridItem[] {
    return appointmentGroups.map((x) => {
        const startsAt = DateTime.fromISO(x.list[0].startDate);
        const endsAt = DateTime.fromISO(x.list[0].endDate);

        const item: AppointmentGridItem = {
            type: 'appointments',
            group: x,
            groupKey: x.groupKey,
            duration: endsAt.diff(startsAt).as('minutes'),
            startsAt: startsAt.hour * 60 + startsAt.minute,
            key: x.key,
        };

        return item;
    });
}

export function groupAppointmentsByUser(appointments: Appointment[]): AppointmentsGroup[] {
    function getKey(appointment: Appointment) {
        const userId = appointment.userServiceId;
        const dateTime = +moment(appointment.startDate).toDate() / 60000;
        const key = `${userId}_${dateTime}`;
        return key;
    }

    function getGroupKey(appointment: Appointment) {
        return appointment.userServiceId;
    }

    // group by start date rounding up to a minute
    return Object.values(groupBy(appointments, getKey)).map((list) => ({
        list,
        groupKey: getGroupKey(list[0]),
        key: getKey(list[0]),
    }));
}

export function groupAppointmentsByDate(appointments: Appointment[]): AppointmentsGroup[] {
    function getKey(appointment: Appointment) {
        const date = moment(appointment.startDate).format('YYYY-MM-DD');
        const dateTime = +moment(appointment.startDate).toDate() / 60000;
        const tailMinute = dateTime % 30;
        const offsetMinute = tailMinute > 15 ? 30 - tailMinute : -tailMinute;

        const key = `${date}_${dateTime + offsetMinute}`;
        return key;
    }

    function getGroupKey(appointment: Appointment) {
        return moment(appointment.startDate).format('YYYY-MM-DD');
    }

    // group by start date rounding up to a minute
    return Object.values(groupBy(appointments, getKey)).map((list) => ({
        list,
        groupKey: getGroupKey(list[0]),
        key: getKey(list[0]),
    }));
}

export const getStatusText = (status: AppointmentStatus | undefined, t: TFunction): string => {
    switch (status) {
        case 'Confirmed':
            return t('appointments.status.confirmed');
        case 'CustomerArrived':
            return t('appointments.status.customerArrived');
        case 'CustomerDidNotArrive':
            return t('appointments.status.customerDidNotArrive');
        case 'OrderCreated':
            return t('appointments.status.orderCreated');
        case 'Unconfirmed':
        default:
            return t('appointments.status.unconfirmed');
    }
};

type AppointmentsScheduleData = {
    localScheduleInMinutes: {
        start: number;
        end: number;
    }[];
    synchronizationEnabled: boolean;
    tzOffset: number;
};
const DEFAULT_DATA: AppointmentsScheduleData = {
    localScheduleInMinutes: Array.from({ length: 7 }).map(() => ({
        start: 0,
        end: 24 * 60,
    })),
    synchronizationEnabled: false,
    tzOffset: 0,
};

const AppointmentsScheduleDataContext = React.createContext<AppointmentsScheduleData>(DEFAULT_DATA);

export function useAppointmentScheduleDataContext() {
    const ctx = useContext(AppointmentsScheduleDataContext);
    if (ctx === null) throw new Error('AppointmentsScheduleDataContext is not found');
    return ctx;
}

const selectRequiredState = createSelector(selectSettings, (s) => ({
    tzOffset: s.internationalization.timeZoneOffset,
}));

export const AppointmentScheduleDataProvider = memo(({ children }: React.PropsWithChildren<{}>) => {
    const { tzOffset } = useAppSelector(selectRequiredState);
    const { locations: selectedLocations } = useAppSelector(selectFilters);
    const appointmentsSettings = useAppointmentSettings();

    const workingDays: ReadonlyArray<AppointmentWorkingDayDto> | undefined = useMemo(() => {
        return appointmentsSettings
            ?.filter(
                (x) => selectedLocations.length === 0 || selectedLocations.includes(x.repairShopKey)
            )
            ?.map((x) => x.workingDays)
            .flat();
    }, [appointmentsSettings, selectedLocations]);

    const filteredAppointmentSettings = useMemo(() => {
        return appointmentsSettings?.filter(
            (x) => selectedLocations.length === 0 || selectedLocations.includes(x.repairShopKey)
        );
    }, [appointmentsSettings, selectedLocations]);

    const synchronizeAppointmentsEnabled =
        filteredAppointmentSettings?.every((x) => x.synchronizeAppointmentsEnabled) ?? true;
    const value: AppointmentsScheduleData = useMemo(() => {
        if (!workingDays) return DEFAULT_DATA;
        return {
            localScheduleInMinutes: Object.entries(
                groupBy(
                    workingDays.map((x) => {
                        // convert sunday to 7 according to ISO standard
                        // NOTE (MB) we are making a copy to avoid side effects
                        if (x.dayNumber === 0) return { ...x, dayNumber: 7 };
                        return { ...x };
                    }),
                    (x) => x.dayNumber
                )
            )
                .sort((a, b) => a[0][0].charCodeAt(0) - b[0][0].charCodeAt(0))
                .map((w) =>
                    w[1].some((x) => x.active)
                        ? {
                              start: TimeSpan.fromString(
                                  w[1].reduce(function (a, b) {
                                      return a.opening <= b.opening ? a : b;
                                  }).opening
                              ).fullMinutes,
                              end: TimeSpan.fromString(
                                  w[1].reduce(function (a, b) {
                                      return a.closing <= b.closing ? b : a;
                                  }).closing
                              ).fullMinutes,
                          }
                        : { start: 0, end: 0 }
                ),
            synchronizationEnabled: synchronizeAppointmentsEnabled,
            tzOffset,
        };
    }, [workingDays, synchronizeAppointmentsEnabled, tzOffset]);

    return (
        <AppointmentsScheduleDataContext.Provider value={value}>
            {children}
        </AppointmentsScheduleDataContext.Provider>
    );
});

export function calculateIsWithinSchedule(
    dayTs: moment.Moment,
    startInMinutes: number,
    duration: number,
    tzOffset: number,
    scheduleStartInRsTz: number,
    scheduleEndInRsTz: number
) {
    if (scheduleEndInRsTz - scheduleStartInRsTz <= 0) return false;
    const m = dayTs
        .clone()
        .milliseconds(0)
        .second(0)
        .minute(0)
        .hour(0)
        .add(startInMinutes, 'm')
        .utcOffset(tzOffset);
    const m2 = m.clone().add(duration - 1, 'm');
    const start = m.clone().hours(0).minute(0).add(scheduleStartInRsTz, 'm');
    const end = start.clone().add(scheduleEndInRsTz - scheduleStartInRsTz, 'm');
    return m.isSameOrAfter(start, 'm') && m2.isSameOrBefore(end, 'm');
}
