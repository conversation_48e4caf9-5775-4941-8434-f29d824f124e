import { makeStyles } from '@mui/styles';
import { StopComponent } from 'common/components/AppointmentsScheduler/TimeLine';
import moment from 'moment';
import { memo } from 'react';

const useStyles = makeStyles((theme) => ({
    stop: {
        color: theme.palette.neutral[6],
        ...theme.typography.h6Inter,
    },
}));

const day = moment();

const Stop: StopComponent = memo(({ ts }) => {
    const styles = useStyles();
    day.hour(0).minute(0).add(ts, 'm');
    const str = day.format('HH:mm');
    return <span className={styles.stop}>{str}</span>;
});

export default Stop;
