import { styled } from '@mui/material';
import { ContentGridColumnComponent } from 'common/components/AppointmentsScheduler/Content/ContentGrid';
import moment from 'moment';
import { memo, useMemo } from 'react';

const ActiveColumnOverlay = styled('div')({
    backgroundColor: '#0069FF1A',
    height: '100%',
});

const ColumnDecorations: ContentGridColumnComponent<string> = memo(({ group }) => {
    const isActive = useMemo(() => moment().format('YYYY-MM-DD') === group, [group]);

    return <>{isActive && <ActiveColumnOverlay />}</>;
});

export default ColumnDecorations;
