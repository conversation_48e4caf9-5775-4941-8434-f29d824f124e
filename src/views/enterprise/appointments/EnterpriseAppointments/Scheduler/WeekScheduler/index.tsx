import { useMediaQuery } from '@mui/material';
import { AbsenceDto } from 'api/users';
import AppointmentScheduler, {
    OnSchedulerScrollChangedHandler,
    SchedulerScrollController,
} from 'common/components/AppointmentsScheduler';
import {
    ContentInnerWrapperComponent,
    GetItemDataFunction,
} from 'common/components/AppointmentsScheduler/Content';
import { KeyGetterFunction } from 'common/components/AppointmentsScheduler/_util';
import { debounce } from 'lodash';
import moment from 'moment';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { setWeekSchedulerScroll } from 'store/slices/enterprise/appointments';
import { selectWeekData } from 'store/slices/enterprise/appointments/selectors';
import BackToNowButton from '../../AppointmentList/Header/BackToNowButton';
import AppointmentGridItemComponent from '../AppointmentItem';
import ColumnDecorations from '../ColumnDecorations';
import Cursor from '../Cursor';
import StopComponent from '../StopComponent';
import {
    Appointment,
    AppointmentGridItem,
    AppointmentScheduleDataProvider,
    createAbsencesGridItems,
    createAppoitmentsGridItems,
    groupAppointmentsByDate,
} from '../_common';
import AppointmentsHeaderCellComponent from './HeaderCellComponent';
import TdComponent from './TdComponent';

const getAppointmentKey: KeyGetterFunction<AppointmentGridItem> = (a) => a.key;
const getAppointmentGroupKey: KeyGetterFunction<AppointmentGridItem> = (a) => a.groupKey;
const getGroupKey: KeyGetterFunction<string> = (d) => d;
const getAppointmentData: GetItemDataFunction<AppointmentGridItem> = (a) => {
    return {
        ts: a.startsAt,
        sizeInMinutes: a.duration,
    };
};

export type WeekSchedulerProps = {
    appointments: Appointment[];
    dateReference: moment.MomentInput;
    stepsInterval: number;
    absences: AbsenceDto[];
};

const ContentInnerWrapper: ContentInnerWrapperComponent = memo(({ children }) => {
    const activeGroupKey = moment().format('YYYY-MM-DD');

    return (
        <>
            {children}
            <Cursor groupKey={activeGroupKey} />
        </>
    );
});

export default function WeekScheduler({
    dateReference,
    appointments,
    stepsInterval,
    absences,
}: WeekSchedulerProps) {
    const isSmall = useMediaQuery('(max-width: 900px)');
    const days = useMemo(() => {
        const monday = moment(dateReference)
            .hour(0)
            .minute(0)
            .second(0)
            .millisecond(0)
            .startOf('isoWeek');
        return Array.from({ length: 7 }).map((_, i) =>
            monday.clone().add(i, 'day').format('YYYY-MM-DD')
        );
    }, [dateReference]);
    const appointmentGroups = useMemo(() => groupAppointmentsByDate(appointments), [appointments]);
    const [scrollController, setScrollController] = useState<SchedulerScrollController | null>(
        null
    );
    const { uiData } = useAppSelector(selectWeekData);
    const [from, to] = [0, 24 * 60];
    const scrolledRef = useRef(false);

    useEffect(() => {
        if (!scrollController) return;
        if (scrolledRef.current) return;
        scrolledRef.current = true;
        const now = moment();
        if (uiData.scrollInMinutes !== null) {
            scrollController.scrollAtTopInMinutes(uiData.scrollInMinutes);
        } else {
            scrollController.scrollAtCenterInMinutes(now.hour() * 60 + now.minute() - from);
        }
    }, [scrollController, uiData, from]);

    const dispatch = useAppDispatch();
    const onScrollChanged: OnSchedulerScrollChangedHandler = useCallback(
        (data, _event) => {
            dispatch(setWeekSchedulerScroll(data.scrollTopMinutes));
        },
        [dispatch]
    );
    const onScrollChangedDebounced = useMemo(
        () => debounce(onScrollChanged, 1000),
        [onScrollChanged]
    );

    const gridItems = useMemo(() => {
        const items: AppointmentGridItem[] = [
            ...createAbsencesGridItems(absences, days),
            ...createAppoitmentsGridItems(appointmentGroups),
        ];
        return items;
    }, [absences, days, appointmentGroups]);

    return (
        <AppointmentScheduleDataProvider>
            <AppointmentScheduler<AppointmentGridItem, string>
                groups={days}
                items={gridItems}
                showTimezone={true}
                getItemKey={getAppointmentKey}
                getItemGroupKey={getAppointmentGroupKey}
                getItemData={getAppointmentData}
                getGroupKey={getGroupKey}
                components={{
                    StopComponent,
                    HeaderCellComponent: AppointmentsHeaderCellComponent,
                    ItemComponent: AppointmentGridItemComponent,
                    ContentGridColumnComponent: ColumnDecorations,
                    ContentGridTdComponent: TdComponent,
                    ContentInnerWrapperComponent: ContentInnerWrapper,
                    CornerComponent: BackToNowButton,
                }}
                range={[from, to]} // full day
                sizes={{
                    columnWidth: 160,
                    headerHeight: 50,
                    timelineWidth: isSmall ? 70 : 120,
                    pixelsPerMinute: (1.8 * 30) / stepsInterval,
                }}
                stopsInterval={stepsInterval}
                scrollControllerRef={setScrollController}
                onScrollChanged={onScrollChangedDebounced}
            />
        </AppointmentScheduleDataProvider>
    );
}
