import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import { HeaderCellComponent } from 'common/components/AppointmentsScheduler/Header';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { memo, useMemo } from 'react';

const useStyles = makeStyles((theme) => ({
    dayName: {
        ...theme.typography.h5Inter,
        fontWeight: 'normal',
    },
    num: {
        ...theme.typography.h5Inter,
    },
    root: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        width: '100%',
        gap: 2,
        color: theme.palette.neutral[7],
        justifySelf: 'stretch',
    },
    active: {
        color: theme.palette.primary.light,
        backgroundColor: '#0069FF1A',
    },
}));

const AppointmentsHeaderCellComponent: HeaderCellComponent<string> = memo(({ value }) => {
    const date = moment(value);
    const { t } = useAppTranslation();
    const dayKey = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'][
        date.isoWeekday() - 1
    ];
    const dayName = t(`commonLabels.days.${dayKey}`);
    const styles = useStyles();
    const isActive = useMemo(() => moment().format('YYYY-MM-DD') === value, [value]);

    return (
        <div className={clsx(styles.root, isActive && styles.active)}>
            <span className={styles.dayName}>{dayName}</span>
            <span className={styles.num}>{date.date()}</span>
        </div>
    );
});

export default AppointmentsHeaderCellComponent;
