.cursor {
    position: absolute;
    pointer-events: none;
    width: var(--cmos-aps-column-size);
    top: 0;
    left: var(--offset);
    height: 2px;
    background-color: var(--cm1);
    transform: translateY(var(--pos));
    z-index: 1;
}

.cursor:not(.full)::before {
    display: block;
    content: ' ';
    position: absolute;
    top: -5px;
    left: 0;
    height: 10px;
    width: 10px;
    background-color: var(--cm1);
    z-index: 3;
    border-radius: 5px;
}

.full {
    width: calc(var(--cmos-aps-column-size) * var(--cmos-aps-columns-count));
    left: 0;
}
