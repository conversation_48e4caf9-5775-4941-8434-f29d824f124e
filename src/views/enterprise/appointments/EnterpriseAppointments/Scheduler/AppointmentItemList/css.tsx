import { makeStyles } from '@mui/styles';
import { Colors } from '../../../../../../common/styles/Colors';
import { scrollbarStyle } from '../../../../../../common/styles/ScrollbarStyles';

const useAppointmentItemListStyles = makeStyles((theme) => ({
    root: {
        width: 420,
        marginTop: 50,
        padding: '0 0 20px 0',
        flexDirection: 'column',
        justifyContent: 'flex-end',
        alignItems: 'center',
        boxSizing: 'border-box',
        height: '100%',
        overflowY: 'hidden',
    },

    header: {
        margin: 0,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: 52,
        paddingLeft: 24,
        paddingRight: 11,
        backgroundColor: theme.palette.neutral[8],
    },

    title: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 700,
    },

    list: {
        marginTop: 20,
        padding: '0 14px 0 0',
        maxHeight: 'calc(100vh - 150px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        ...scrollbarStyle(),
    },

    labelContainer: {
        display: 'flex',
        alignItems: 'baseline',
        marginBottom: 10,
        gap: 4,
        flexDirection: 'row',
        justifyContent: 'flex-start',
    },

    label: {
        padding: 0,
        margin: 0,
        fontSize: 14,
        fontWeight: 700,
        color: Colors.CM3,
    },

    timezone: {
        padding: 0,
        margin: 0,
        fontSize: 10,
        fontWeight: 700,
        color: Colors.Grey5,
    },

    subItem: {
        display: 'flex',
        marginBottom: 17,
        flexDirection: 'column',
        width: '100%',
        height: 'auto',
        '&:last-child': {
            marginBottom: 20,
        },
    },

    wrapper: {
        display: 'flex',
        height: '100%',
        flexDirection: 'column',
        padding: '0 13px 0 25px',
    },
}));

export default useAppointmentItemListStyles;
