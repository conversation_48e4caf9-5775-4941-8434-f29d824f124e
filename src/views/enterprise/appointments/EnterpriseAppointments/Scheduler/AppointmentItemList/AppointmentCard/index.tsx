import { Typography } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { forwardRef } from 'react';
import { Appointment, getStatusText } from '../../_common';
import useAppointmentCardStyles from './css';

export type AppointmentCardProps = {
    appointment: Appointment;
    enableTooltip?: boolean;
    onClick: () => void;
};

const dateFormat = (date: Date): string => {
    return moment(date).format('HH:mm');
};

const AppointmentCard = forwardRef(
    (
        { appointment, onClick, enableTooltip }: AppointmentCardProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const classes = useAppointmentCardStyles();
        const { t } = useAppTranslation();

        // const restProps = { ...otherProps, style, data, children };
        const isNotArrived = appointment.status === 'CustomerDidNotArrive';

        return (
            <div ref={ref} className={classes.root} id={appointment.id} onClick={onClick}>
                <div
                    className={classes.header}
                    style={{ background: appointment?.userServiceAdvisorColor ?? '#fff' }}
                />
                <div
                    className={`${classes.content} ${isNotArrived ? classes.contentNotArrive : ''}`}
                >
                    <div style={{ display: 'flex' }}>
                        <Typography
                            className={`${
                                isNotArrived ? classes.customerNotArrived : classes.customerTitle
                            }`}
                            style={{
                                width: '70%',
                                overflow: 'hidden',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis',
                            }}
                        >
                            {`${appointment.customerFirstName} ${
                                appointment.customerLastName ?? ''
                            }`}
                        </Typography>
                        <Typography
                            className={`${classes.platesTitle}`}
                            style={{ display: 'flex', justifyContent: 'end', width: '30%' }}
                        >
                            {appointment.vehiclePlates || '--'}
                        </Typography>
                    </div>
                    <Typography className={classes.hoursText}>
                        {dateFormat(new Date(appointment.startDate)) +
                            ' - ' +
                            dateFormat(new Date(appointment.endDate))}
                    </Typography>
                    <div style={{ display: 'flex' }}>
                        <Typography className={classes.personChargeText} style={{ width: '50%' }}>
                            {appointment.userServiceDisplayName}
                        </Typography>
                        <Typography
                            className={classes.personChargeText}
                            style={{ display: 'flex', justifyContent: 'end', width: '50%' }}
                        >
                            {getStatusText(appointment.status, t)}
                        </Typography>
                    </div>
                </div>
            </div>
        );
    }
);

export default AppointmentCard;
