import { makeStyles } from '@mui/styles';

const useAppointmentCardStyles = makeStyles((theme) => ({
    root: {
        border: '1px solid #C9CDD3 !important',
        boxSizing: 'border-box',
        borderRadius: '4px',
        position: 'relative',
        zIndex: 2,
        cursor: 'pointer',
        overflow: 'hidden',
        transition: '.25s',
        height: '100%',
        '&:hover': {
            border: '1px solid var(--cm2) !important',
        },
    },
    header: {
        width: '100%',
        height: '4px',
    },
    arrived: {
        background: '#36CE91',
    },
    notArrived: {
        background: '#F15857',
    },
    confirmed: {
        background: '#36CE91',
    },
    notConfirmed: {
        background: '#467CFC',
    },
    orderCreated: {
        background: '#8000FF',
    },
    content: {
        padding: '5px 8px 5px 8px',
        height: '100%',
        background: '#FFFFFF',
    },
    contentNotArrive: {
        background: '#EFEFEF',
    },
    customerTitle: {
        fontWeight: 700,
        fontSize: 14,
        lineHeight: '17px',
        color: theme.palette.neutral[7],
    },
    platesTitle: {
        fontWeight: 700,
        fontSize: 10,
        lineHeight: '12px',
        color: theme.palette.neutral[7],
    },
    customerNotArrived: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
        lineHeight: '15px',
        color: '#6A6E72',
        textDecoration: 'line-through',
    },
    hoursText: {
        fontWeight: 400,
        fontSize: 12,
        marginTop: 16,
        lineHeight: '14px',
        color: theme.palette.neutral[6],
        whiteSpace: 'nowrap',
    },
    personChargeText: {
        fontWeight: 400,
        fontSize: 10,
        lineHeight: '12px',
        color: theme.palette.neutral[7],
        marginTop: 5,
    },
}));

export default useAppointmentCardStyles;
