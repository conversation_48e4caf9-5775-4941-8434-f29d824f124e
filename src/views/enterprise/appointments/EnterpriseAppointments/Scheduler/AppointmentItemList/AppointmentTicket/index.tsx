import { getVehicleFormatted } from 'common/Helpers';
import { ROUTES } from 'common/constants';
import { useState } from 'react';
import { generatePath, useNavigate } from 'react-router-dom';
import { useAppointmentsQueryController } from '../../../_common';
import AppointmentPopup from '../../AppointmentItem/AppointmentPopup';
import DeleteAppointmentModal from '../../AppointmentItem/DeleteAppointmentModal';
import { Appointment } from '../../_common';
import AppointmentCard from '../AppointmentCard';

type AppointmentTicketProps = {
    appointment: Appointment;
};

export default function AppointmentTicket({ appointment }: AppointmentTicketProps) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    const { refetchWeek } = useAppointmentsQueryController();
    const onDeleted = () => {
        setShowDeleteModal(false);
        setOpen(false);
        refetchWeek();
    };

    return (
        <>
            <AppointmentCard
                ref={setAnchorEl}
                appointment={appointment}
                onClick={() => setOpen(true)}
            />
            <AppointmentPopup
                anchorEl={anchorEl}
                open={open}
                id={appointment.id}
                onClose={() => setOpen(false)}
                onOrderCreated={(orderId) =>
                    navigate(generatePath(ROUTES.ORDERS_DETAIL, { id: orderId }))
                }
                onDeleteRequested={() => setShowDeleteModal(true)}
                onCustomerDidNotArrive={() => setOpen(false)}
            />
            <DeleteAppointmentModal
                open={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onDeleted={onDeleted}
                appointmentData={{
                    id: appointment.id,
                    duration: appointment.duration,
                    customer: `${appointment.customerFirstName ?? ''} ${
                        appointment.customerLastName ?? ''
                    }`.trim(),
                    vehicle: getVehicleFormatted(appointment),
                    date: appointment.startDate,
                }}
            />
        </>
    );
}
