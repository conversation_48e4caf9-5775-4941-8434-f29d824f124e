import { Box, IconButton, Popover } from '@mui/material';
import { GetOffsetLabel } from 'common/DateTimeHelpers';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import groupBy from 'lodash/groupBy';
import { useLocations } from 'store/slices/enterprise/locations';
import { Appointment } from '../_common';
import AppointmentTicket from './AppointmentTicket';
import useAppointmentItemListStyles from './css';

export type AppointmentItemListProps = {
    appointments: Appointment[];
    open: boolean;
    onClose: () => void;
};

export default function AppointmentItemList({
    appointments,
    open,
    onClose,
}: AppointmentItemListProps) {
    const { t } = useAppTranslation();
    const locations = useLocations();
    const styles = useAppointmentItemListStyles();

    const groupedAppointments = Object.entries(groupBy(appointments, (x) => x.repairShopId)).sort(
        (a, b) => {
            const firstShopName = locations?.find((x) => x.repairShopId === a[0])?.name ?? '';
            const secondShopName = locations?.find((x) => x.repairShopId === b[0])?.name ?? '';
            return firstShopName.localeCompare(secondShopName);
        }
    );

    const body = (
        <ul className={styles.list}>
            {groupedAppointments.map((a) => {
                const location = locations?.find((x) => x.repairShopId === a[0]);
                return (
                    <div key={a[0]}>
                        <div className={styles.labelContainer}>
                            <span className={styles.label}>{location?.name} </span>
                            <span className={styles.timezone}>
                                {GetOffsetLabel(location?.timeZone)}
                            </span>
                        </div>
                        {a[1]
                            .sort((a, b) =>
                                `${a.customerFirstName}${a.customerLastName}`.localeCompare(
                                    `${b.customerFirstName}${b.customerLastName}`
                                )
                            )
                            .map((c, i) => (
                                <li key={c.id} className={styles.subItem}>
                                    <AppointmentTicket appointment={c} />
                                </li>
                            ))}
                    </div>
                );
            })}
        </ul>
    );

    return (
        <>
            <Popover
                disableScrollLock
                elevation={3}
                marginThreshold={0}
                classes={{ paper: styles.root }}
                anchorOrigin={{ horizontal: 'right', vertical: 'center' }}
                transformOrigin={{ horizontal: 'right', vertical: 'center' }}
                onClose={onClose}
                open={open}
                style={{ zIndex: 4 }}
            >
                <Box className={styles.header} marginTop={1} display="flex" alignItems="center">
                    <span className={styles.title}>
                        {appointments.length} {t('titles.appointments')}
                    </span>
                    <IconButton
                        style={{ width: 13, height: 13 }}
                        onClick={() => onClose()}
                        size="large"
                    >
                        <CloseIcon fill={'#fff'} />
                    </IconButton>
                </Box>
                <Box className={styles.wrapper}>{body}</Box>
            </Popover>
        </>
    );
}
