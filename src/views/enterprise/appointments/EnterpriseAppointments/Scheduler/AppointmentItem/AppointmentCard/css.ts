import { makeStyles } from '@mui/styles';

const useAppointmentCardStyles = makeStyles((theme) => ({
    root: {
        border: '1px solid #C9CDD3 !important',
        boxSizing: 'border-box',
        borderRadius: '4px',
        position: 'relative',
        zIndex: 2,
        cursor: 'pointer',
        overflow: 'hidden',
        transition: '.25s',
        height: '100%',
        '&:hover': {
            border: '1px solid var(--cm2) !important',
        },
    },
    header: {
        width: '100%',
        height: '4px',
    },
    arrived: {
        background: '#36CE91',
    },
    notArrived: {
        background: '#F15857',
    },
    confirmed: {
        background: '#36CE91',
    },
    notConfirmed: {
        background: '#467CFC',
    },
    orderCreated: {
        background: '#8000FF',
    },
    content: {
        padding: '5px 8px 5px 8px',
        height: '100%',
        background: '#FFFFFF',
    },
    contentNotArrive: {
        background: '#EFEFEF',
    },
    customerTitle: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
        lineHeight: '15px',
        color: '#6A6E72',
    },
    platesTitle: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 7,
        lineHeight: '8px',
        color: '#6A6E72',
    },
    customerNotArrived: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
        lineHeight: '15px',
        color: '#6A6E72',
        textDecoration: 'line-through',
    },
    hoursText: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: 9,
        lineHeight: '11px',
        color: '#6A6E72',
        whiteSpace: 'nowrap',
    },
    personChargeText: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: 7,
        lineHeight: '8px',
        color: '#6A6E72',
        marginTop: 1,
    },
}));

export default useAppointmentCardStyles;
