import { Typography, useTheme } from '@mui/material';
import { ValetServiceIcon } from 'common/components/Icons/ValetServiceIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { forwardRef } from 'react';
import { useLocations } from 'store/slices/enterprise/locations';
import { Appointment, getStatusText } from '../../_common';
import useAppointmentCardStyles from './css';

export type AppointmentCardProps = {
    appointment: Appointment;
    valetServiceEnabled: boolean;
    enableTooltip?: boolean;
    onClick: () => void;
};

const AppointmentCard = forwardRef(
    (
        { appointment, onClick, valetServiceEnabled }: AppointmentCardProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        const classes = useAppointmentCardStyles();
        const { t } = useAppTranslation();
        const theme = useTheme();
        const location = useLocations().find((x) => x.repairShopId == appointment.repairShopId);

        // const restProps = { ...otherProps, style, data, children };
        const isNotArrived = appointment.status === 'CustomerDidNotArrive';

        return (
            <div ref={ref} className={classes.root} id={appointment.id} onClick={onClick}>
                <div
                    className={classes.header}
                    style={{ background: appointment?.userServiceAdvisorColor ?? '#fff' }}
                />
                <div
                    className={`${classes.content} ${isNotArrived ? classes.contentNotArrive : ''}`}
                >
                    <div style={{ display: 'flex' }}>
                        {valetServiceEnabled && appointment.withValetService && (
                            <ValetServiceIcon
                                fill={theme.palette.primary.main}
                                size={10}
                                style={{ marginRight: 3, marginTop: 2 }}
                            />
                        )}
                        <Typography
                            className={`${
                                isNotArrived ? classes.customerNotArrived : classes.customerTitle
                            }`}
                            style={{
                                width: '70%',
                                overflow: 'hidden',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis',
                            }}
                        >
                            {`${appointment.customerFirstName} ${
                                appointment.customerLastName ?? ''
                            }`}
                        </Typography>
                        <Typography
                            className={`${classes.platesTitle}`}
                            style={{ display: 'flex', justifyContent: 'end', width: '30%' }}
                        >
                            {appointment.vehiclePlates || '--'}
                        </Typography>
                    </div>
                    <Typography className={classes.hoursText}>{location?.name}</Typography>
                    <div style={{ display: 'flex' }}>
                        <Typography className={classes.personChargeText} style={{ width: '50%' }}>
                            {appointment.userServiceDisplayName}
                        </Typography>
                        <Typography
                            className={classes.personChargeText}
                            style={{ display: 'flex', justifyContent: 'end', width: '50%' }}
                        >
                            {getStatusText(appointment.status, t)}
                        </Typography>
                    </div>
                </div>
            </div>
        );
    }
);

export default AppointmentCard;
