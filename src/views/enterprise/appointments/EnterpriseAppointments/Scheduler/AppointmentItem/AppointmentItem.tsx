import { styled } from '@mui/material/styles';
import { getVehicleFormatted } from 'common/Helpers';
import { ItemComponent } from 'common/components/AppointmentsScheduler/Content';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { ENTERPRISE_ROUTES } from 'common/constants';
import moment from 'moment';
import { memo, useMemo, useState } from 'react';
import { NavLink, generatePath, useNavigate } from 'react-router-dom';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { useValetServiceEnabledForShops } from 'views/enterprise/_common';
import { Colors } from '../../../../../../common/styles/Colors';
import { useAppointmentsQueryController, useNow } from '../../_common';
import AppointmentItemList from '../AppointmentItemList';
import { Appointment, AppointmentsGroup } from '../_common';
import AppointmentCard from './AppointmentCard';
import AppointmentPopup from './AppointmentPopup';
import DeleteAppointmentModal from './DeleteAppointmentModal';

const selectMode = createSelector(selectFilters, (s) => s.mode);

const AppointmentItem: ItemComponent<AppointmentsGroup> = memo(({ value: appointmentGroup }) => {
    const mode = useAppSelector(selectMode);
    const appointments = appointmentGroup.list;
    const firstAppointment = appointments[0];

    const [openAppointmentItemList, setOpenAppointmentItemList] = useState(false);
    // all appointments in the group will belong to the same service advisor in "day" mode
    const advisorId = mode === 'day' ? firstAppointment.userServiceId : undefined;

    const now = useNow();
    const date = moment(firstAppointment.startDate);
    const dateFormmated = date.format('YYYYMMDDHHmm');
    const isInPast = date.isBefore(now, 'm');

    const shopIds = useMemo(
        () => [...new Set(appointments.map((a) => a.repairShopId))],
        [appointments]
    );
    const valetServiceMap = useValetServiceEnabledForShops(shopIds);

    if (appointments.length === 0) return null; // what?

    let body: React.ReactNode;

    if (appointments.length === 1) {
        body = <AppointmentSubItem appointment={firstAppointment} />;
    } else {
        const sortedAppointments = appointments.sort((a, b) =>
            `${a.customerFirstName}${a.customerLastName}`.localeCompare(
                `${b.customerFirstName}${b.customerLastName}`
            )
        );
        body =
            sortedAppointments.length > 4 ? (
                <Root>
                    {sortedAppointments.slice(0, 3).map((a, idx) => (
                        <SubItem key={a.id} style={{ left: `${(50 / 2) * idx}%` }}>
                            <AppointmentSubItem appointment={a} />
                        </SubItem>
                    ))}
                    <AdditionalBlock onClick={() => setOpenAppointmentItemList(true)}>
                        +{sortedAppointments.length - 3}
                    </AdditionalBlock>
                    <AppointmentItemList
                        open={openAppointmentItemList}
                        appointments={sortedAppointments}
                        onClose={() => setOpenAppointmentItemList(false)}
                    />
                </Root>
            ) : (
                <Root>
                    {sortedAppointments.map((a, idx) => (
                        <SubItem
                            key={a.id}
                            style={{ left: `${(50 / (sortedAppointments.length - 1)) * idx}%` }}
                        >
                            <AppointmentSubItem appointment={a} />
                        </SubItem>
                    ))}
                </Root>
            );
    }

    return (
        <Wrapper>
            {body}
            {!isInPast && (
                <PlusBtn
                    className="plsBtn"
                    to={
                        `${ENTERPRISE_ROUTES.APPOINTMENTS_NEW}?date=${dateFormmated}` +
                        (advisorId
                            ? `&advisor=${advisorId}&shopId=${firstAppointment.repairShopId}`
                            : '')
                    }
                >
                    <PlusIcon fill="currentColor" />
                </PlusBtn>
            )}
        </Wrapper>
    );
});

type AppointmentSubItemProps = {
    appointment: Appointment;
};

function AppointmentSubItem({ appointment }: AppointmentSubItemProps) {
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    const { refetchWeek } = useAppointmentsQueryController();
    const onDeleted = () => {
        setShowDeleteModal(false);
        setOpen(false);
        refetchWeek();
    };

    return (
        <>
            <AppointmentCard
                ref={setAnchorEl}
                appointment={appointment}
                onClick={() => setOpen(true)}
                valetServiceEnabled={appointment.withValetService}
            />
            <AppointmentPopup
                anchorEl={anchorEl}
                open={open}
                id={appointment.id}
                onClose={() => setOpen(false)}
                onOrderCreated={(orderId) =>
                    navigate(generatePath(ENTERPRISE_ROUTES.ORDERS_DETAIL, { id: orderId }))
                }
                onDeleteRequested={() => setShowDeleteModal(true)}
                onCustomerDidNotArrive={() => setOpen(false)}
            />
            <DeleteAppointmentModal
                open={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onDeleted={onDeleted}
                appointmentData={{
                    id: appointment.id,
                    duration: appointment.duration,
                    customer: `${appointment.customerFirstName ?? ''} ${
                        appointment.customerLastName ?? ''
                    }`.trim(),
                    vehicle: getVehicleFormatted(appointment),
                    date: appointment.startDate,
                }}
            />
        </>
    );
}

const Root = styled('ul')({
    position: 'relative',
    height: '100%',
    listStyle: 'none',
    margin: 0,
});

const SubItem = styled('li')({
    position: 'absolute',
    left: 0,
    width: '50%',
    height: '100%',
});

const AdditionalBlock = styled('li')({
    position: 'absolute',
    height: '94%',
    left: '76%',
    width: '23%',
    top: '2%',
    cursor: 'pointer',
    zIndex: 2,
    borderRadius: 4,
    border: 'solid 1px #0069FF',
    backgroundColor: Colors.CM5,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: 14,
    fontWeight: 400,
    color: Colors.CM1,
    '&:hover': {
        outline: '2px auto #0069FF',
    },
});

const PlusBtn = styled(NavLink)({
    all: 'initial',
    backgroundColor: 'var(--cm1)',
    display: 'block',
    zIndex: 2,
    height: 24,
    width: 24,
    borderRadius: 12,
    position: 'absolute',
    right: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    color: '#fff',
    cursor: 'pointer',
    scale: '0',
    visibility: 'hidden',
    transition: 'scale .15s',
    transformOrigin: 'center',
    '&:hover': {
        backgroundColor: 'var(--cm2)',
    },
});

const Wrapper = styled('div')({
    display: 'contents',
    position: 'relative',
    '&:hover .plsBtn': {
        scale: '1',
        visibility: 'visible',
    },
});

export default AppointmentItem;
