import { makeStyles } from '@mui/styles';
import { useAppointmentSchedulerContext } from 'common/components/AppointmentsScheduler';
import { ItemComponentProps } from 'common/components/AppointmentsScheduler/Content';
import { CSSProperties } from 'react';
import AbsenceBlock from 'views/Components/AbsenceBlock';
import { AbsenceGroup } from '../_common';

const useStyles = makeStyles((theme) => ({
    root: {
        height: '100%',
        position: 'relative',
    },
    item: {
        boxSizing: 'border-box',
        position: 'absolute',
        left: 'calc(100% / var(--total-count) * var(--index) * 2 / 3)',
        top: 'calc(var(--cmos-aps-pixels-per-minute) * var(--absence-offset))',
        height: 'calc(var(--cmos-aps-pixels-per-minute) * var(--absence-duration))',
    },
}));

function getWidth(index: number, totalCount: number): number {
    return 100 - ((index / totalCount) * 100 * 2) / 3;
}

export default function AbsenceItem({ value }: ItemComponentProps<AbsenceGroup>) {
    const styles = useStyles();
    const { pixelsPerMinute } = useAppointmentSchedulerContext();

    return (
        <div
            className={styles.root}
            style={{ '--total-count': value.absences.length } as CSSProperties}
        >
            {value.absences.map((s, idx) => (
                <div
                    key={s.originalAbsence.id}
                    className={styles.item}
                    style={
                        {
                            '--index': idx,
                            '--absence-offset': s.start.diff(value.start).as('seconds') / 60,
                            '--absence-duration': s.duration,
                            width: `${getWidth(idx, value.absences.length)}%`,
                        } as CSSProperties
                    }
                >
                    <AbsenceBlock
                        pixelPerMinute={pixelsPerMinute}
                        vertical
                        absence={s.originalAbsence}
                        topOffset={3}
                        overrideDuration={s.duration}
                    />
                </div>
            ))}
        </div>
    );
}
