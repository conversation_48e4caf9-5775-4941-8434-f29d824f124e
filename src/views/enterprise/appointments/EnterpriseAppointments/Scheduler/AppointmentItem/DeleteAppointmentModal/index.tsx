import { Box, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentAPI from 'api/Appointment';
import { capitalizeFirstLetter } from 'common/Helpers';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useMemo } from 'react';
import { useAppDispatch } from 'store';
import { deleteAppointmentById } from 'store/slices/enterprise/appointments';

type DeleteAppointmentModalProps = {
    open: boolean;
    appointmentData: {
        id: string;
        duration?: number;
        customer: string;
        vehicle: string;
        date: string;
    };
    onDeleted: (id: string) => void;
    onClose: () => void;
};

const BodyText = styled('span')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[7],
}));

export default function DeleteAppointmentModal({
    appointmentData,
    open,
    onDeleted,
    onClose,
}: DeleteAppointmentModalProps) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const startDate = moment(appointmentData?.date);
    const endDate = startDate.clone().add(appointmentData.duration ?? 0, 'm');
    const id = appointmentData?.id;

    const deleteAppointment = useMutation(
        async () => {
            if (!id) throw new Error('appointment has no id');
            return AppointmentAPI.delete(id);
        },
        {
            onSuccess: () => {
                dispatch(deleteAppointmentById(id!));
                onDeleted(id!);
            },
        }
    );

    const body = (
        <>
            <Box>
                <Box justifyContent="center" display="flex">
                    <BodyText>
                        <strong>{appointmentData?.customer}</strong>
                    </BodyText>
                </Box>
            </Box>
            <Box style={{ marginTop: 5 }}>
                <Box justifyContent="center" display="flex">
                    <BodyText>
                        <strong>{appointmentData?.vehicle}</strong>
                    </BodyText>
                </Box>
            </Box>
            <Box style={{ marginTop: 5 }}>
                <Box justifyContent="center" display="flex">
                    <BodyText>
                        {`${capitalizeFirstLetter(
                            startDate.format(t('dateFormats.shortDateWithDay'))
                        )} ${startDate.format('HH:mm')} - ${endDate.format('HH:mm')}hrs (${
                            appointmentData?.duration
                        } min)`}
                    </BodyText>
                </Box>
            </Box>
        </>
    );

    const disabled =
        useMemo(() => {
            if (!appointmentData) return true;
            const currentDate = new Date();
            return new Date(appointmentData.date) < currentDate;
        }, [appointmentData]) || !id;

    return (
        <DeleteConfirmationPopup
            open={open}
            isConfirmDisabled={disabled}
            title={t('appointments.deleteTitle')}
            body={body}
            cancel={t('commonLabels.doNotDelete')}
            confirm={t('appointments.delete')}
            onConfirm={() => deleteAppointment.mutate()}
            onClose={onClose}
            showLoader={deleteAppointment.isLoading}
        />
    );
}
