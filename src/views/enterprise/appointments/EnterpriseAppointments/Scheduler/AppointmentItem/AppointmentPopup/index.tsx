import TurnedInIcon from '@mui/icons-material/TurnedIn';
import {
    Box,
    CircularProgress,
    Divider,
    Grid,
    IconButton,
    Popover,
    styled,
    useTheme,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { AppointmentStatus } from 'api/appointments';
import { GetOffsetLabel } from 'common/DateTimeHelpers';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CustomerIcon } from 'common/components/Icons/CustomerIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import Tooltip from 'common/components/Tooltip';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import moment from 'moment';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { generatePath, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { updateAppointmentStatus } from 'store/slices/enterprise/appointments';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { useLocations } from 'store/slices/enterprise/locations';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useHeaderLoading } from 'views/HeaderBar';
import { useAppointmentSettings } from 'views/enterprise/_common';
import { getStatusText } from '../../_common';
import { useAppointmentDetails } from '../../_hooks';
import Parameter from './Parameter';
import useAppointmentPopupStyles from './css';

const Header = styled('header')(({ theme }) => ({
    position: 'relative',
    height: 40,
}));

const Status = styled('header')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[7],
    display: 'inline-block',
    padding: '17px 0 0 40px',
    textTransform: 'uppercase',
}));

const ReasonsUl = styled('ul')({
    listStyle: 'none',
    paddingLeft: 0,
    margin: '5px 0',
});

const NotesList = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: 5,
});

export type OnAppointmentDeleteRequestedHandler = (appointmentData: {
    id: string;
    number: string;
}) => void;

export type AppointmentPopupProps = {
    id?: string;
    open: boolean;
    onClose: () => void;
    anchorEl?: HTMLElement | null;
    onOrderCreated: (orderId: number) => void;
    onCustomerDidNotArrive?: () => void;
    onDeleteRequested: OnAppointmentDeleteRequestedHandler;
};

export default function AppointmentPopup({
    onClose,
    open,
    id,
    onDeleteRequested,
    onCustomerDidNotArrive,
    anchorEl,
}: AppointmentPopupProps) {
    const enabled = open && !!anchorEl;
    const { data, isInitialLoading, isFetching } = useAppointmentDetails(id, enabled);
    useHeaderLoading(isFetching);
    const { t } = useAppTranslation();
    const theme = useTheme();
    const usedLocation = useLocations().find((x) => x.repairShopId === data?.repairShopId);
    const dispatch = useAppDispatch();
    const { locations: selectedLocations } = useAppSelector(selectFilters);
    const appointmentsSettings = useAppointmentSettings();
    const userPermission = useSelector(selectUserPermission);

    const filteredAppointmentSettings = useMemo(() => {
        return appointmentsSettings?.filter(
            (x) => selectedLocations.length === 0 || selectedLocations.includes(x.repairShopKey)
        );
    }, [appointmentsSettings, selectedLocations]);

    const navigate = useNavigate();
    const synchronizeAppointmentsEnabled =
        filteredAppointmentSettings?.every((x) => x.synchronizeAppointmentsEnabled) ?? true;

    const usedAppointmentSettings = appointmentsSettings?.find(
        (x) => x.repairShopKey === data?.repairShopId
    );

    const syncAppointmentWith3rdPartySoftwareTooltopEnabled = usedAppointmentSettings
        ? usedAppointmentSettings.synchronizeAppointmentsEnabled &&
          !usedAppointmentSettings.omnichannelEnabled
        : true;

    const styles = useAppointmentPopupStyles();

    const markAsDidNotArrive = useMutation(
        async () => {
            throw new Error('not implemented');
            // await AppointmentsApi.updateStatus(id!, 'CustomerDidNotArrive');
        },
        {
            onSuccess: () => {
                dispatch(
                    updateAppointmentStatus({
                        id: id!,
                        status: 'CustomerArrived',
                    })
                );
                if (onCustomerDidNotArrive) onCustomerDidNotArrive();
            },
        }
    );

    const customerNotes = useMemo(
        () => (data ? data.notes?.filter((x) => x.type === 'ForCustomer') : []),
        [data]
    );

    const internalNotes = useMemo(
        () => (data ? data.notes?.filter((x) => x.type === 'ForInternal') : []),
        [data]
    );

    if (!anchorEl) return null;

    let body: React.ReactNode;

    const editUrl = data
        ? generatePath(ENTERPRISE_ROUTES.APPOINTMENTS_EDIT, {
              num: data.number,
          }) +
          '?shopId=' +
          data.repairShopId
        : undefined;

    if (data && !isInitialLoading) {
        body = (
            <>
                <Header>
                    <Status>
                        {getStatusText(AppointmentStatusEnum[data.status] as AppointmentStatus, t)}
                    </Status>

                    <div className={styles.headerActions}>
                        {data.status !== AppointmentStatusEnum.OrderCreated && (
                            <>
                                <IconButton
                                    style={{ width: 11, height: 14 }}
                                    onClick={() =>
                                        onDeleteRequested({
                                            id: data.id!,
                                            number: data.number! + '',
                                        })
                                    }
                                    size="large"
                                    disabled={
                                        synchronizeAppointmentsEnabled ||
                                        !userPermission.allowEditAppointments
                                    }
                                >
                                    <DeleteIcon fill={theme.palette.neutral[5]} />
                                </IconButton>
                                <Tooltip
                                    content={t(
                                        'appointments.appointmentMustBeEditedUsing3rdPartySoftware'
                                    )}
                                    disabled={!syncAppointmentWith3rdPartySoftwareTooltopEnabled}
                                >
                                    <IconButton
                                        href={editUrl ?? '#'}
                                        style={{ width: 0, height: 0 }}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            if (editUrl) navigate(editUrl);
                                        }}
                                        size="large"
                                        disabled={
                                            synchronizeAppointmentsEnabled ||
                                            !userPermission.allowEditAppointments
                                        }
                                    >
                                        <EditIcon fill={theme.palette.neutral[5]} />
                                    </IconButton>
                                </Tooltip>
                            </>
                        )}
                        <IconButton
                            style={{ width: 11, height: 14 }}
                            onClick={() => onClose()}
                            size="large"
                        >
                            <CloseIcon fill={theme.palette.neutral[5]} />
                        </IconButton>
                    </div>
                </Header>

                <div className={styles.summary}>
                    <Grid container>
                        <Grid item xs={6}>
                            <Parameter
                                label={t('appointments.appointmentNumber')}
                                value={data.number}
                            />
                        </Grid>

                        <Grid item xs={6}>
                            <Parameter
                                label={t('appointments.orderNumber')}
                                value={data.orderNumber || '--'}
                            />
                        </Grid>
                    </Grid>
                    <Parameter
                        label={t('appointments.location')}
                        value={`${usedLocation?.name} ${GetOffsetLabel(usedLocation?.timeZone)}`}
                    />
                    <Parameter
                        icon={
                            <RoundedIcon style={{ color: 'var(--neutral5)' }}>
                                <CustomerIcon fill="currentColor" size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.customerName')}
                        value={`${data.customerFirstName ?? ''} ${
                            data.customerLastName ?? ''
                        }`.trim()}
                    />
                    <Parameter
                        label={t('appointments.e-mail')}
                        value={data.customerEmail || '--'}
                    />
                    <Parameter
                        label={t('appointments.mobile')}
                        value={data.customerMobile || '--'}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <CarIcon size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.vehicle')}
                        value={[
                            data.vehiclePlates ?? '--',
                            data.vehicleVIN,
                            data.vehicleMake,
                            data.vehicleModel,
                            data.vehicleYear,
                            data.vehicleColor,
                        ]
                            .filter(Boolean)
                            .join(', ')}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <ClockIcon size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.duration')}
                        value={`${moment(data.startDate).format('HH:mm')} - ${moment(data.startDate)
                            .add(data.duration, 'm')
                            .format('HH:mm')} (${data.duration}m)`}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <CustomerIcon fill={theme.palette.neutral[5]} size={18} />
                            </RoundedIcon>
                        }
                        label={t('appointments.serviceAdvisor')}
                        value={data.userServiceDisplayName}
                    />
                    <Divider />
                    <Parameter
                        icon={
                            <RoundedIcon>
                                <TurnedInIcon sx={{ height: 18, width: 18, padding: '2px' }} />
                            </RoundedIcon>
                        }
                        label={t('appointments.reasonForTheAppointment')}
                        value={
                            <ReasonsUl>
                                {data.reasons.map((r) => (
                                    <li>{r.name}</li>
                                ))}
                            </ReasonsUl>
                        }
                    />

                    <Parameter
                        label={t('appointments.notesVisibleForCustomer')}
                        value={
                            customerNotes && customerNotes.length ? (
                                <NotesList>
                                    {customerNotes.map((note, index) => (
                                        <div key={index}>{note.note}</div>
                                    ))}
                                </NotesList>
                            ) : (
                                '--'
                            )
                        }
                    />
                    <Parameter
                        label={t('appointments.internalNotes')}
                        value={
                            internalNotes && internalNotes.length ? (
                                <NotesList>
                                    {internalNotes.map((note, index) => (
                                        <div key={index}>{note.note}</div>
                                    ))}
                                </NotesList>
                            ) : (
                                '--'
                            )
                        }
                    />
                </div>

                <footer className={styles.actions}>
                    {/* TODO uncomment when this button can be used again */}
                    {/* {[
                        AppointmentStatusEnum.Confirmed,
                        AppointmentStatusEnum.Unconfirmed,
                        AppointmentStatusEnum.CustomerArrived,
                    ].includes(data.status) ? (
                        <Button
                        
                            onClick={() => markAsDidNotArrive.mutate()}
                            showLoader={markAsDidNotArrive.isLoading}
                            label={t('appointments.customerDidNotArrive')}
                            cmosVariant={'stroke'}
                        />
                    ) : null} */}
                </footer>
            </>
        );
    } else {
        body = (
            <Box display="flex" justifyContent="center" padding={10}>
                <CircularProgress size={25} />
            </Box>
        );
    }

    return (
        <>
            <Popover
                disableScrollLock
                elevation={3}
                classes={{ paper: styles.root }}
                anchorOrigin={{ horizontal: 'center', vertical: 'center' }}
                transformOrigin={{ horizontal: 'center', vertical: 'top' }}
                onClose={onClose}
                anchorEl={anchorEl}
                open={enabled}
                style={{ zIndex: 2 }}
            >
                {body}
            </Popover>
        </>
    );
}

const RoundedIcon = styled('div')({
    height: 18,
    width: 18,
    color: 'var(--neutral1)',
    backgroundColor: 'var(--neutral5)',
    borderRadius: 10,
});
