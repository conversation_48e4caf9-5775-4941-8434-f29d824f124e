import { makeStyles } from '@mui/styles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

const useAppointmentPopupStyles = makeStyles((theme) => ({
    root: {
        backgroundColor: '#fff',
        overflow: 'hidden',
        position: 'relative',
        width: 310,
        padding: 0,
        borderRadius: 8,
        border: `1px solid ${theme.palette.neutral[3]}`,
    },

    actions: {
        display: 'flex',
        flexDirection: 'column',
        gap: 6,
        alignItems: 'stretch',
        margin: 8,
    },

    summary: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
        gap: 12,
        padding: '10px 16px',
        maxHeight: 430,
        overflow: 'auto',
        scrollBehavior: 'smooth',
        ...scrollbarStyle(),
    },

    headerActions: {
        position: 'absolute',
        right: 10,
        top: 12,
    },

    colorStripe: {
        height: 5,
    },
}));

export default useAppointmentPopupStyles;
