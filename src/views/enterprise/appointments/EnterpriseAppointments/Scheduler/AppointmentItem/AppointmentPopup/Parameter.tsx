import { styled } from '@mui/material';

const Label = styled('span')(({ theme }) => ({
    ...theme.typography.h7Inter,
    fontWeight: 'normal',
    gridArea: 'l',
}));
const Value = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[8],
    gridArea: 'v',
}));
const Icon = styled('div')({
    gridArea: 'i',
    width: 18,
    marginRight: 6,
});
const Root = styled('div')({
    display: 'grid',
    gridTemplate: 'auto 1fr / auto 1fr',
    gridTemplateAreas: '"i l" "i v"',
});

export type ParameterProps = {
    value: React.ReactNode;
    label: string;
    icon?: React.ReactNode;
};

export default function Parameter({ icon, value, label }: ParameterProps) {
    return (
        <Root>
            <Icon>{icon}</Icon>
            <Label>{label}</Label>
            <Value>{value}</Value>
        </Root>
    );
}
