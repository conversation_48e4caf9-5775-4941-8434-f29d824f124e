import { makeStyles } from '@mui/styles';
import { ServiceAdvisorWithShopIdDto } from 'api/appointments';
import clsx from 'clsx';
import { HeaderCellComponent } from 'common/components/AppointmentsScheduler/Header';
import { memo } from 'react';

const useStyles = makeStyles((theme) => ({
    root: {
        color: theme.palette.neutral[7],
        ...theme.typography.h5Inter,
        textAlign: 'center',
    },
}));

const AppointmentsHeaderCellComponent: HeaderCellComponent<ServiceAdvisorWithShopIdDto> = memo(
    ({ value }) => {
        const styles = useStyles();

        return <div className={clsx(styles.root)}>{value.name}</div>;
    }
);

export default AppointmentsHeaderCellComponent;
