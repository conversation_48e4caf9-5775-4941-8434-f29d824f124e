import { makeStyles } from '@mui/styles';
import { ServiceAdvisorWithShopIdDto } from 'api/appointments';
import clsx from 'clsx';
import { ContentGridTdComponent } from 'common/components/AppointmentsScheduler/Content/ContentGrid';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import moment from 'moment';
import { memo, useContext } from 'react';
import { useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom';
import { selectUserPermission } from 'store/slices/user/selectors';
import { useNow } from '../../_common';
import { calculateIsWithinSchedule, useAppointmentScheduleDataContext } from '../_common';
import { DaySchedulerDateContext } from './_context';

const useStyles = makeStyles((theme) => ({
    td: {
        position: 'relative',
        '&:hover $actionOverlay': {
            opacity: 1,
        },
    },

    disabled: {
        backgroundColor: 'var(--neutral3)',
        '& > $actionOverlay': {
            display: 'none',
        },
    },

    actionOverlay: {
        userSelect: 'none',
        textDecoration: 'none',
        cursor: 'pointer',
        backgroundColor: rgba(theme.palette.primary.main, 0.1),
        ...theme.typography.h6Inter,
        borderRadius: 4,
        position: 'absolute',
        left: 6,
        right: 6,
        top: 3,
        bottom: 3,
        color: theme.palette.primary.main,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        opacity: 0,
        border: `1px dashed ${theme.palette.primary.main}`,
    },
}));

const TdComponent: ContentGridTdComponent<ServiceAdvisorWithShopIdDto> = memo(
    ({ group: advisor, startTimestamp, stopTimestamp }) => {
        const styles = useStyles();
        const { t } = useAppTranslation();

        const dateStr = useContext(DaySchedulerDateContext);
        const date = moment(dateStr).add(startTimestamp, 'm');

        const ctx = useAppointmentScheduleDataContext();
        const { start, end } = ctx.localScheduleInMinutes[date.isoWeekday() - 1];
        const isActiveDay = !(start === 0 && end === 0);
        const duration = stopTimestamp - startTimestamp;
        const isWithinSchedule = calculateIsWithinSchedule(
            date,
            startTimestamp,
            duration,
            ctx.tzOffset,
            start,
            end
        );
        const now = useNow();
        const isNotInThePast = date.isAfter(now, 'minute');
        const isActive = isWithinSchedule && isNotInThePast && isActiveDay;
        const userPermission = useSelector(selectUserPermission);

        return (
            <div aria-hidden={!isActive} className={clsx(styles.td, !isActive && styles.disabled)}>
                {!ctx.synchronizationEnabled && userPermission.allowEditAppointments && (
                    <NavLink
                        draggable="false"
                        to={`${ENTERPRISE_ROUTES.APPOINTMENTS_NEW}?date=${date.format(
                            'YYYYMMDDHHmm'
                        )}&advisor=${advisor.id}&shopId=${advisor.repairShopId}`}
                        className={styles.actionOverlay}
                    >
                        {t('appointments.scheduleNewAppointment')}
                    </NavLink>
                )}
            </div>
        );
    }
);

export default TdComponent;
