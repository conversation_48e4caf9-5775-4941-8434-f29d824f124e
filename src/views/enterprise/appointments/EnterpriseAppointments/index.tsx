import { styled } from '@mui/material';
import { AppointmentStatus } from 'api/appointments';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { selectFilters } from 'store/slices/enterprise/appointments/selectors';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { usePubnubListener } from 'utils/pubnub';
import { CustomHeaderContent, useHeaderLoading } from 'views/HeaderBar';
import Header from './AppointmentList/Header';
import DayScheduler from './Scheduler/DayScheduler';
import WeekScheduler from './Scheduler/WeekScheduler';
import { Appointment } from './Scheduler/_common';
import { useAppointmentsQuery } from './_common';

const defaultIntervalMilliseconds = 120000;

export default function EnterpriseAppointments() {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dispatch = useDispatch();

    const {
        date,
        advisors: selectedAdvisors,
        locations: selectedLocations,
        statuses,
        mode,
    } = useAppSelector(selectFilters);

    const [refetchInterval] = useState<number>(defaultIntervalMilliseconds);

    const {
        appointments: data,
        isFetching,
        refetch,
        absences: allAbsences,
    } = useAppointmentsQuery(date, {
        refetchInterval,
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), '');
        },
    });
    const absences = useMemo(() => {
        let list = allAbsences;

        if (!list) return [];

        if (selectedAdvisors.length !== 0) {
            list = list.filter((x) => x.userId === null || selectedAdvisors.includes(x.userId));
        }

        if (selectedLocations.length !== 0) {
            list = list.filter((x) => selectedLocations.includes(x.shopId));
        }

        return list;
    }, [allAbsences, selectedLocations, selectedAdvisors]);
    useHeaderLoading(isFetching);
    const settings = useAppSelector(selectSettings);
    usePubnubListener(
        (_event) => {
            refetch();
        },
        {
            unsubscribeTimeoutMs: 60000,
            channelGroups: [`cg_v1_web_appointments_enterprise_${settings.id}`],
            types: ['appointment.updated'],
            listenerEnabled: settings.appMode === 'Enterprise',
            unsubscribeIfListenerDisabled: true,
        }
    );

    const appointments: Appointment[] = useMemo(() => {
        let list = (data ?? []).map((a) => ({
            number: a.number,
            orderNumber: a.orderNumber,
            status: AppointmentStatusEnum[a.status] as AppointmentStatus,
            customerId: a.customerId,
            repairShopId: a.repairShopId,
            customerFirstName: a.customerFirstName,
            customerLastName: a.customerLastName,
            customerEmail: a.customerEmail,
            customerMobile: a.customerMobile,
            userServiceDisplayName: a.userServiceDisplayName,
            userServiceAdvisorColor: a.userServiceAdvisorColor,
            userServiceId: a.userServiceAdvisorId,
            duration: a.duration,
            vehiclePlates: a.vehiclePlates,
            vehicleVIN: a.vehicleVIN,
            vehicleMake: a.vehicleMake,
            vehicleModel: a.vehicleModel,
            vehicleYear: a.vehicleYear,
            vehicleColor: a.vehicleColor,
            jobType: a.jobType,
            id: a.id ?? '',
            startDate: a.startDate + '',
            endDate: a.endDate + '',
            withValetService: a.withValetService,
        }));

        if (selectedAdvisors.length) {
            list = list.filter((x) => selectedAdvisors.includes(x.userServiceId));
        }

        if (selectedLocations.length) {
            list = list.filter((x) => selectedLocations.includes(x.repairShopId));
        }

        if (statuses.length) {
            list = list.filter((x) => statuses.includes(x.status));
        }

        if (mode === 'day') {
            const start = moment(date);
            list = list.filter((x) => moment(x.startDate).isSame(start, 'd'));
        }

        return list;
    }, [data, selectedAdvisors, selectedLocations, statuses, mode, date]);

    useDocumentTitle(t('titles.appointments'));

    return (
        <>
            <CustomHeaderContent>
                <Header />
            </CustomHeaderContent>
            <Main>
                {mode === 'week' ? (
                    <WeekScheduler
                        absences={absences}
                        stepsInterval={30}
                        appointments={appointments}
                        dateReference={date}
                    />
                ) : (
                    <DayScheduler
                        absences={absences}
                        stepsInterval={30}
                        appointments={appointments}
                    />
                )}
            </Main>
        </>
    );
}

const Main = styled('main')(({ theme }) => ({
    position: 'relative',
    paddingLeft: 28,
    paddingRight: 44,
    backgroundColor: theme.palette.neutral[1],
    height: 'calc(100vh - var(--header-height))',

    '@media (max-width: 900px)': {
        paddingLeft: 0,
        paddingRight: 0,
    },
}));
