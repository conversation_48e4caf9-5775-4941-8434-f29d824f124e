import { Query<PERSON>ey, useQuery, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import moment from 'moment/moment';
import { useEffect, useMemo, useRef } from 'react';
import { EnterpriseAbsencesApi, EnterpriseAppointmentsApi } from '../../../../api/enterprise';
import useForceRender from '../../../../common/hooks/useForceRender';
import { GetAppointmentsResponse } from '../../../../datacontracts/Scheduler/GetAppointmentsResponse';
import { RootState, useAppDispatch, useAppSelector } from '../../../../store';
import { setFetchedAppointments } from '../../../../store/slices/enterprise/appointments';
import {
    selectAppointments,
    selectFilters,
} from '../../../../store/slices/enterprise/appointments/selectors';

function getAppointmentsQueryKey(date: moment.Moment) {
    const monday = moment(date).startOf('isoWeek');
    return ['appointments', 'week', monday.format('YYYY-MM-DD')];
}

export function useAppointmentsQueryController() {
    const queryClient = useQueryClient();
    const { date } = useAppSelector(selectFilters);
    const day = moment(date).format('YYYY-MM-DD');

    return useMemo(
        () => ({
            refetchWeek: () => {
                queryClient.refetchQueries(getAppointmentsQueryKey(moment(day).startOf('isoWeek')));
            },
        }),
        [queryClient, day]
    );
}

export function useServiceAdvisors() {
    const { data } = useQuery(
        ['users', 'advisors'],
        EnterpriseAppointmentsApi.getServiceAdvisorsByEnterprise,
        {
            cacheTime: Infinity,
            staleTime: 1000,
        }
    );

    //Remove all in-active service advisors
    return useMemo(() => data?.filter((s) => s.isActive) ?? [], [data]);
}

export function useNow(granularity: moment.unitOfTime.StartOf = 'm') {
    const now = useRef(new Date());
    const fr = useForceRender();

    useEffect(() => {
        const interval = setInterval(() => {
            const newNow = moment();

            if (!newNow.isSame(now.current, granularity)) {
                now.current = newNow.toDate();
                fr();
            }
        }, 60000);
        return () => clearInterval(interval);
    }, [fr, granularity]);
    return now.current;
}

export function useAppointmentsQuery(
    date: moment.MomentInput,
    {
        onSuccess,
        ...options
    }: Omit<UseQueryOptions<GetAppointmentsResponse>, 'queryKey' | 'queryFn' | 'meta'> = {}
) {
    const monday = moment(date).startOf('isoWeek');
    const dateStr = monday.format('YYYY-MM-DD');
    const queryKey = getAppointmentsQueryKey(monday);
    const dispatch = useAppDispatch();
    const { refetch, isFetching } = useQuery(
        queryKey as QueryKey,
        () => {
            const startDate = monday;
            const endDate = startDate.clone().add(7, 'd').add(-1, 's');
            return EnterpriseAppointmentsApi.getAppointmentsByEnterprise(
                startDate.toDate(),
                endDate.toDate()
            );
        },
        {
            onSuccess: (data: GetAppointmentsResponse): void => {
                dispatch(
                    setFetchedAppointments({
                        date: dateStr,
                        appointments: data.data,
                    })
                );
                if (onSuccess) onSuccess(data);
            },
            staleTime: 100,
            cacheTime: 120000,
            meta: {
                noPersist: true, // disable persistency for this query
            },
            ...options,
        }
    );

    const { data: allAbsences } = useQuery(
        ['enterprise', 'absences', dateStr],
        () => {
            const startDate = monday;
            const endDate = startDate.clone().add(7, 'd').add(-1, 's');
            return EnterpriseAbsencesApi.get(startDate.toISOString(), endDate.toISOString());
        },
        {
            initialData: {},
            refetchInterval: 120000,
        }
    );

    const appointments = useAppSelector((r: RootState) => selectAppointments(r, dateStr));
    const absences = useMemo(
        () =>
            Object.entries(allAbsences)
                .map(([shopId, list]) => list.map((x) => ({ ...x, shopId })))
                .flat(),
        [allAbsences]
    );
    return {
        appointments,
        refetch,
        isFetching,
        absences,
    };
}
