import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { rgba } from 'common/styles/ColorHelpers';

const useStyles = makeStyles((theme: Theme) => ({
    title: {
        ...theme.typography.h4Inter,
        color: theme.palette.primary.main,
    },
    subTitle: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[8],
    },
    selectedDate: {
        ...theme.typography.h6Roboto,
        color: theme.palette.primary.main,
    },
    caption: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        marginBottom: 15,
    },
    scheduleItem: {
        flex: '0 33.3%',
        marginBottom: 10,
    },
    scheduleButton: {
        ...theme.typography.h6Inter,
        color: theme.palette.primary.main,
        background: theme.palette.neutral[1],
        border: `1px solid ${theme.palette.primary.main}`,
        borderRadius: 20,
        width: 120,
        height: 32,
        '&:hover': {
            background: theme.palette.primary.main,
            color: theme.palette.neutral[1],
        },
    },
    scheduleDisableButton: {
        background: theme.palette.neutral[1],
        border: `1px solid ${theme.palette.primary.main}`,
        borderRadius: 20,
        opacity: 0.3,
    },
    scheduleActiveButton: {
        ...theme.typography.h6Inter,
        color: `${theme.palette.neutral[1]} !important`,
        background: theme.palette.primary.main,
        border: `1px solid ${theme.palette.primary.main}`,
        borderRadius: 20,
        width: 120,
        height: 32,
        '&:hover': {
            background: theme.palette.primary.main,
            color: theme.palette.neutral[1],
        },
    },
    scheduleActiveButtonInvalid: {
        background: 'var(--danger)',
        border: `1px solid var(--danger)`,
        '&:hover': {
            background: 'var(--danger)',
            color: 'var(--danger)',
        },
    },
    scheduleOriginallySelectedButton: {
        '&:not($scheduleActiveButton):not(:hover)': {
            borderWidth: 2,
            backgroundColor: rgba(theme.palette.primary.main, 0.08),
        },
    },
    scheduleList: {
        listStyle: 'none',
        flexWrap: 'wrap',
        display: 'flex',
        padding: 0,
    },
}));

export default useStyles;
