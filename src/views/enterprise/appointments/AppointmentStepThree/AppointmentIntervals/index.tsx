import { styled } from '@mui/material';
import { LocalDateObject, ScheduleWindow } from 'api/appointments';
import IntervalChip from '../IntervalChip';

export type AppointmentIntervalsProps = {
    originalAppointmentDate?: LocalDateObject | null;
    appointmentDate: LocalDateObject | null | undefined;
    windows: ScheduleWindow[];
    onWindowChange: (newWindow: ScheduleWindow) => void;
    loading: boolean;
};

const Ul = styled('ul')({
    display: 'flex',
    gap: 10,
    listStyle: 'none',
    flexWrap: 'wrap',
    padding: 0,
    margin: '6px 0 0 0',
});

export default function AppointmentIntervals({
    originalAppointmentDate,
    appointmentDate,
    windows,
    onWindowChange,
}: AppointmentIntervalsProps) {
    const time = originalAppointmentDate?.time;

    return (
        <>
            <Ul>
                {windows.map((window) => (
                    <li key={window.start}>
                        <IntervalChip
                            active={window.start === appointmentDate?.time}
                            available={window.available}
                            start={window.start}
                            end={window.end}
                            originallySelected={time === window.start}
                            onClick={() => onWindowChange(window)}
                        />
                    </li>
                ))}
            </Ul>
        </>
    );
}
