import { Box, Grid } from '@mui/material';
import Typography from '@mui/material/Typography';
import { ScheduleWindow } from 'api/appointments';
import { BoxShadow } from 'common/components/Box/BoxShadow';
import { CircleIconWithBorder } from 'common/components/Icons';
import InfoTooltip from 'common/components/InfoTooltip';
import Calendar from 'common/components/Inputs/Calendar';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { OptionStyle } from 'common/styles/OptionStyle';
import { isEqual } from 'lodash';
import moment from 'moment';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';
import {
    selectEditData,
    selectServiceAdvisorsForSelectedShop,
    useEnterpriseScheduleWindows,
} from 'store/slices/enterprise/appointments/selectors';
import fetchServiceAdvisors from 'store/slices/enterprise/appointments/thunks/fetchServiceAdvisors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import { StepHeader } from 'views/Appointments/common';
import { useAppointmentSettingsForShop } from 'views/enterprise/_common';
import AppointmentIntervals from './AppointmentIntervals';
import useStyles from './css';
import { capitalizeFirstLetter } from './helper';

const selectIsDisabled =
    appointmentEditorStateSelectors.createStep3DisabledSelector(selectEditData);
const selectStepThreeState = createSelector(
    selectEditData,
    selectIsDisabled,
    selectServiceAdvisorsForSelectedShop,
    (
        {
            appointmentData: { serviceAdvisorId, startsAt, id },
            originalData,
            enterpriseData: { shopId },
        },
        isDisabled,
        serviceAdvisors
    ) => ({
        serviceAdvisorId,
        startsAt,
        id,
        isDisabled,
        originalData,
        shopId,
        serviceAdvisors,
    })
);

const AppointmentStepThree = () => {
    const { t } = useAppTranslation();
    const {
        isDisabled,
        shopId,
        serviceAdvisors: advisors,
        ...appointment
    } = useAppSelector(selectStepThreeState, isEqual);
    const styles = useStyles();
    const dispatch = useAppDispatch();
    const settings = useAppointmentSettingsForShop(shopId);

    // advisors
    useEffect(() => {
        dispatch(fetchServiceAdvisors({ overrideCachedValue: true }));
    }, [dispatch, shopId]);
    const options: OptionData<string | null>[] = useMemo(
        () => [
            {
                label: t('appointments.step3.anyServiceAdvisor'),
                value: null,
                icon: CircleIconWithBorder,
            },

            ...(advisors ?? []).map((x) => ({
                value: x.id,
                label: x.name,
                icon: CircleIconWithBorder,
                color: x.color,
            })),
        ],
        [advisors, t]
    );
    const selectedAdvisorOption = useMemo(
        () => options.find((x) => x.value === appointment.serviceAdvisorId),
        [appointment.serviceAdvisorId, options]
    );

    // date and time
    const localTime = useMemo(
        () => moment(`${appointment.startsAt.date} ${appointment.startsAt.time}`),
        [appointment.startsAt]
    );
    const setStrDate = (date: string) =>
        dispatch(
            enterpriseAppointmentsActions.setAppointmentDate(moment(date).format('YYYY-MM-DD'))
        );

    const toasters = useToasters();
    const response = useEnterpriseScheduleWindows({
        onPreselectedScheduleOutsideOfShopSchedule: () => {
            toasters.danger(
                t('enterprise.appointments.new.outsideLocationSchedule.text'),
                t('enterprise.appointments.new.outsideLocationSchedule.title')
            );
        },
    });
    const availableScheduleWindows = response?.windows ?? [];
    const hasAvailability = response?.hasAvailability ?? false;
    const isFetching = false; // TODO re-implement

    const selectedDate = appointment.startsAt;
    const selectSchedule = (schedule: ScheduleWindow | undefined) => {
        if (!schedule) return;
        dispatch(enterpriseAppointmentsActions.setScheduleWindow(schedule));
    };

    const daysOff = useMemo(
        () => settings?.workingDays.filter((day) => !day.active).map((day) => day.dayNumber),
        [settings]
    );
    const disableDaysOff = useCallback(
        (date: Date | null): boolean => {
            if (isDisabled) return true;

            if (!date || !daysOff) {
                return false;
            }

            return daysOff.findIndex((day) => day === date.getDay()) !== -1;
        },
        [daysOff, isDisabled]
    );

    return (
        <>
            <Grid container spacing={0}>
                <Grid item xs={12} md={6}>
                    <StepHeader
                        number={3}
                        title={t('appointments.step3.selectTheTimeAndDateOfTheAppointment')}
                    />
                </Grid>
                <Grid
                    item
                    xs={12}
                    md={6}
                    style={{ display: 'flex', alignItems: 'end', marginBottom: '22px' }}
                >
                    <Typography className={styles.selectedDate}>{`${capitalizeFirstLetter(
                        localTime.format(t('dateFormats.longDateWithDay'))
                    )} / ${
                        selectedDate.time === '00:00:00' ? '--' : selectedDate.time
                    }`}</Typography>
                </Grid>
            </Grid>
            <Box display={'flex'} sx={{ marginTop: 4, gap: 6 }}>
                <Box>
                    <Typography className={styles.caption}>
                        {t('appointments.step3.selectTheDate')}{' '}
                        <span style={{ color: Colors.CM1 }}>*</span>
                    </Typography>
                    <BoxShadow style={{ margin: 0 }}>
                        <Calendar
                            disablePast
                            disabled={isDisabled}
                            value={localTime.toDate()}
                            shouldDisableDate={disableDaysOff}
                            onChange={(date) => {
                                if (date) {
                                    setStrDate(
                                        moment(new Date(date)).format('YYYY-MM-DDT00:00:00')
                                    );
                                }
                            }}
                        />
                    </BoxShadow>
                </Box>

                <Box>
                    <Grid container spacing={0}>
                        <Grid item xs={8}>
                            <Typography className={styles.caption}>
                                {`${t('appointments.step3.selectTheServiceAdvisorInCharge')} `}
                                <span style={{ color: Colors.CM1 }}>*</span>
                            </Typography>
                            <Dropdown
                                name="users"
                                cmosVariant="roundedPrimary"
                                optionStyle={OptionStyle.icons}
                                isSearchable
                                options={options}
                                disabled={isDisabled}
                                value={selectedAdvisorOption}
                                onChange={(event) => {
                                    event &&
                                        dispatch(
                                            enterpriseAppointmentsActions.setAdvisor(event.value)
                                        );
                                }}
                            />
                        </Grid>
                        <Grid container spacing={0} style={{ marginTop: 30 }}>
                            {hasAvailability ? (
                                <Grid item xs>
                                    <Typography component="header" className={styles.caption}>
                                        {`${t('appointments.step3.selectTime')} `}
                                        <span style={{ color: Colors.CM1 }}>*</span>
                                        <InfoTooltip text={t('appointments.shopTzNotice')} />
                                    </Typography>
                                    <AppointmentIntervals
                                        loading={isFetching}
                                        originalAppointmentDate={appointment.originalData?.startsAt}
                                        windows={availableScheduleWindows}
                                        onWindowChange={selectSchedule}
                                        appointmentDate={selectedDate}
                                    />
                                </Grid>
                            ) : (
                                <Typography className={styles.caption}>
                                    {t('appointments.step3.thereIsNoAvailability')}
                                </Typography>
                            )}
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </>
    );
};

export default memo(AppointmentStepThree);
