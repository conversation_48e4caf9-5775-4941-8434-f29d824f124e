import { ServiceAdvisorDto } from 'api/appointments';
import { isUuid } from 'common/constants';
import useQueryParam from 'common/hooks/useQueryParam';
import isEqual from 'lodash/isEqual';
import { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import {
    enterpriseAppointmentsActions,
    selectEditData,
} from 'store/slices/enterprise/appointments';

export const capitalizeFirstLetter = (value: string, locale: string = navigator.language) => {
    return value.charAt(0).toLocaleUpperCase(locale) + value.slice(1);
};

const selectPreselectedServiceAdvisorData = createSelector(selectEditData, (d) => ({
    shopId: d.enterpriseData.shopId,
}));

export function usePreselectedServiceAdvisorApplier(
    queryParamName: string,
    advisors: ServiceAdvisorDto[]
) {
    const [advisorId] = useQueryParam(queryParamName);
    const { shopId } = useAppSelector(selectPreselectedServiceAdvisorData, isEqual);
    const appliedFor = useRef<string | null>(null);
    const dispatch = useDispatch();

    useEffect(() => {
        if (
            !shopId ||
            shopId === appliedFor.current ||
            !advisorId ||
            !isUuid(advisorId) ||
            advisors.length === 0
        )
            return;

        const advisor = advisors.find((x) => x.id === advisorId);
        if (advisor) dispatch(enterpriseAppointmentsActions.setAdvisor(advisorId));

        appliedFor.current = shopId;
    }, [advisorId, shopId, advisors, dispatch]);
}
