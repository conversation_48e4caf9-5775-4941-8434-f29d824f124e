import { Button } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { TimeSpan } from 'api/utils/format';
import clsx from 'clsx';
import { rgba } from 'common/styles/ColorHelpers';

type IntervalChipProps = {
    start: string;
    end: string;
    available: boolean;
    active: boolean;
    originallySelected: boolean;
    onClick: () => void;
};

const useStyles = makeStyles((theme) => ({
    default: {
        ...theme.typography.h6Inter,
        color: theme.palette.primary.main,
        background: theme.palette.neutral[1],
        border: `1px solid ${theme.palette.primary.main}`,
        borderRadius: 20,
        width: 120,
        height: 32,
        transition: 'none',
        '&:hover': {
            background: theme.palette.primary.main,
            color: theme.palette.neutral[1],
        },
        '&[disabled]': {
            background: theme.palette.neutral[1],
            color: theme.palette.neutral[6],
            border: `1px solid ${theme.palette.neutral[6]}`,
            borderRadius: 20,
        },
    },
    active: {
        ...theme.typography.h6Inter,
        color: `${theme.palette.neutral[1]} !important`,
        background: theme.palette.primary.main,
        border: `1px solid ${theme.palette.primary.main}`,
        borderRadius: 20,
        width: 120,
        height: 32,
        '&:hover': {
            background: theme.palette.primary.main,
            color: theme.palette.neutral[1],
        },
        '&[disabled]': {
            // the same as invalid
            backgroundColor: 'var(--danger)',
            borderColor: 'var(--danger)',
        },
    },
    invalid: {
        '&.$active': {
            background: 'var(--danger)',
            border: `1px solid var(--danger)`,
            '&:hover': {
                background: 'var(--danger)',
                color: 'var(--danger)',
            },
        },
    },
    originallySelected: {
        '&:not($active):not(:hover)': {
            borderWidth: 2,
            backgroundColor: rgba(theme.palette.primary.main, 0.08),
        },
    },
}));

export default function IntervalChip({
    start,
    end,
    originallySelected,
    onClick,
    active,
    available,
}: IntervalChipProps) {
    const styles = useStyles();

    return (
        <Button
            onClick={onClick}
            className={clsx(styles.default, {
                [styles.active]: active,
                [styles.originallySelected]: originallySelected,
            })}
            disabled={!available}
        >
            {TimeSpan.fromString(start).toShortString()} -{' '}
            {TimeSpan.fromString(end).toShortString()}
        </Button>
    );
}
