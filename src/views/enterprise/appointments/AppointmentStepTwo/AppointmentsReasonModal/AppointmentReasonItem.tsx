import { ListItemButton, ListItemIcon, styled } from '@mui/material';
import { AppointmentReasonDto } from 'api/appointments';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { useEffect, useState } from 'react';
import { ReasonsContext } from './context';

export type Controller = {
    ctx: ReasonsContext;
    searchMode: boolean;
};

const SListItem = styled(ListItemButton)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    height: 40,
    '& .MuiListItemIcon-root': {
        width: 40,
    },
}));

export const LetterHeader = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[7],
    borderBottom: `1px solid ${theme.palette.neutral[4]}`,
    ...theme.typography.h5Inter,
    display: 'flex',
    alignItems: 'end',
    boxSizing: 'border-box',
    padding: '6px 15px',
    backgroundColor: '#fff',
}));

type AppointmentReasonItemReasonProps = {
    ctx: ReasonsContext;
    reason: AppointmentReasonDto;
    searchMode: boolean;
};

export function AppointmentReasonItem({
    ctx,
    reason,
    searchMode,
}: AppointmentReasonItemReasonProps) {
    const isLongName = reason.name.length > 30 && !searchMode;
    const displayedName = isLongName ? reason.name.substring(0, 30) + '...' : reason.name;
    const [enabled, setEnabled] = useState(false);

    useEffect(() => ctx.subscribe(reason.id, setEnabled), [reason.id, ctx]);

    return (
        <SListItem
            title={isLongName ? reason.name : undefined}
            onClick={() => ctx.toggle(reason.id, !enabled)}
        >
            <ListItemIcon style={{ height: 20 }}>
                {enabled ? <CheckBoxIcon /> : <UncheckBoxIcon />}
            </ListItemIcon>
            {displayedName}
        </SListItem>
    );
}
