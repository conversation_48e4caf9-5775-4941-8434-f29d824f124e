import { AppointmentReasonDto } from 'api/appointments';
import difference from 'lodash/difference';

export class ReasonsContext implements ReasonsContext {
    private readonly _reasons: Record<string, boolean> = {};
    private readonly _callbacks: Record<string, (checked: boolean) => void> = {};

    constructor(reasons: AppointmentReasonDto[]) {
        for (const reason of reasons) {
            this._reasons[reason.id] = false;
        }
    }

    toggle(id: string, enabled: boolean): void {
        if (this._reasons[id] === enabled) return;
        this._reasons[id] = enabled;
        if (this._callbacks[id]) this._callbacks[id](enabled);
    }

    subscribe(id: string, callback: (checked: boolean) => void): () => void {
        this._callbacks[id] = callback;
        callback(this._reasons[id] ?? false);
        return () => {
            delete this._callbacks[id];
        };
    }

    setSelectedReasons(selectedReasons: AppointmentReasonDto[]) {
        const newSelectedIds = selectedReasons.map((r) => r.id);
        const selectedIds = this.getSelectedReasonIds();

        const toBeDisabled = difference(selectedIds, newSelectedIds);
        for (const id of toBeDisabled) this.toggle(id, false);
        const toBeEnabled = difference(newSelectedIds, selectedIds);
        for (const id of toBeEnabled) this.toggle(id, true);
    }

    getSelectedReasonIds() {
        const ids: string[] = [];

        for (const id in this._reasons) {
            if (this._reasons[id]) {
                ids.push(id);
            }
        }

        return ids;
    }
}
