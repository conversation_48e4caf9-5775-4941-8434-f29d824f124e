import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

const useStyles = makeStyles((theme) => ({
    outer: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        height: '100%',
        width: '100%',
        '&:hover': {
            outline: 'none',
        },
    },
    closeButton: {
        position: 'absolute',
        right: 42,
        top: 36,
    },
    modal: {
        width: '1021px',
        height: 624,
        padding: '36px 42px 42px 42px',
        borderRadius: '24px',
        backgroundColor: theme.palette.neutral[1],
        marginLeft: 'auto',
        marginRight: 'auto',
        position: 'relative',
    },
    container: {
        overflow: 'auto',
        height: 480,
    },
    grid: {
        ...scrollbarStyle(),
    },
    modalTitle: {
        ...FontPrimary(HeaderStyles.H5_14px, true, Colors.Black),
        textAlign: 'left',
    },
    modalText: {
        ...FontPrimary(HeaderStyles.H6_12px, false, Colors.Black),
    },
    modalIconButton: {
        '& svg:hover path': {
            fill: theme.palette.primary.light,
        },
    },
    modalIcon: {
        cursor: 'pointer',
        '&:hover': {
            fill: theme.palette.primary.light,
        },
    },
    filter: {
        width: 432,
    },
    sectionHeader: {
        width: 214,
        ...FontPrimary(HeaderStyles.H6_12px, true, Colors.Black),
        borderBottom: `solid 1px ${theme.palette.neutral[4]}`,
        marginBottom: 15,
    },
    sectionItems: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
    },
    listItem: {
        '&:hover': {
            backgroundColor: Colors.CM5,
        },
    },
}));

export default useStyles;
