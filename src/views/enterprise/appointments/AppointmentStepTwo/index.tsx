import Grid from '@mui/material/Grid';
import { AppointmentReasonCreateDto } from 'api/appointments';
import { EnterpriseAppointmentsApi } from 'api/enterprise';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { isEqual } from 'lodash';
import { memo, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { appointmentEditorStateSelectors } from 'store/util/appointments';
import {
    AppointmentSection,
    StepHeader,
    useEnterpriseAppointmentReasons,
} from 'views/Appointments/common';
import ReasonAutocomplete from 'views/Components/ReasonAutocomplete';
import AppointmentReasonsList from './AppointmentReasonsList';
import AppointmentsReasonModal from './AppointmentsReasonModal';

export type AppointmentStepTwoProps = {
    disabled?: boolean;
};

const selectStepTwoState = createSelector(
    selectEditData,
    appointmentEditorStateSelectors.createAppointmentStep2DisabledSelector(selectEditData),
    ({ appointmentData: { reasons }, enterpriseData: { shopId } }, disabled) => ({
        reasons,
        shopId,
        disabled,
    })
);

function AppointmentStepTwo() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { reasons, shopId, disabled } = useAppSelector(selectStepTwoState, isEqual);
    const [showInspectionModal, setShowInspectionModal] = useState<boolean>(false);
    const { add } = useEnterpriseAppointmentReasons(shopId, false);

    const callbacks = useMemo(
        () => ({
            searchReasons: (query: string) =>
                shopId
                    ? EnterpriseAppointmentsApi.reasons.search(shopId, query)
                    : Promise.resolve([]),
            create: (dto: AppointmentReasonCreateDto) =>
                shopId
                    ? EnterpriseAppointmentsApi.reasons.create(shopId, dto)
                    : Promise.reject(new Error('shopId is required')),
            getFrequent: () =>
                shopId
                    ? EnterpriseAppointmentsApi.reasons.getFrequent(shopId)
                    : Promise.resolve([]),
        }),
        [shopId]
    );

    return (
        <>
            <AppointmentSection>
                <StepHeader
                    number={2}
                    title={t('appointments.step2.selectTheReasonForTheAppointment')}
                    isRequired={true}
                />
            </AppointmentSection>
            <AppointmentSection>
                <AppointmentReasonsList
                    selectedReasons={reasons}
                    onRemoveReason={(id) =>
                        dispatch(enterpriseAppointmentsActions.removeAppointmentReason(id))
                    }
                />
            </AppointmentSection>
            <AppointmentSection>
                <Grid container spacing={1}>
                    <Grid item xs={4}>
                        <ReasonAutocomplete
                            cacheKey="default"
                            searchReasons={callbacks.searchReasons}
                            getFrequentReasons={callbacks.getFrequent}
                            createReason={callbacks.create}
                            disabled={disabled}
                            excludeItems={reasons}
                            onChange={(reason) => {
                                add(reason);
                                dispatch(
                                    enterpriseAppointmentsActions.addAppointmentReason(reason)
                                );
                            }}
                        />
                    </Grid>
                    <Grid item xs={2} style={{ display: 'flex', alignItems: 'end' }}>
                        <Button
                            blockMode
                            cmosVariant={'filled'}
                            label={t('appointments.step2.seeAll')}
                            disabled={disabled}
                            onClick={() => setShowInspectionModal(true)}
                        />
                    </Grid>
                </Grid>
            </AppointmentSection>
            <AppointmentsReasonModal
                open={showInspectionModal}
                onClose={() => setShowInspectionModal(false)}
                onSelected={(values) =>
                    dispatch(enterpriseAppointmentsActions.setAppointmentReason(values))
                }
            />
        </>
    );
}

export default memo(AppointmentStepTwo);
