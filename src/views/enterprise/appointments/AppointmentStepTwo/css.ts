import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    title: {
        ...theme.typography.h4Inter,
        color: theme.palette.primary.main,
    },
    subTitle: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[8],
    },
    caption: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
    },
    item: theme.typography.body1,
    frequentCustomer: {
        ...theme.typography.h6Roboto,
        fontWeight: 'bold',
        color: theme.palette.primary.light,
        marginTop: 16,
    },
}));

export default useStyles;
