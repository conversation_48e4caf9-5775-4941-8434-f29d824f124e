import { styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { CustomersSearchApiProvider } from 'api/customers/dynamic-api';
import { EnterpriseAppointmentsApi } from 'api/enterprise';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useQueryParam from 'common/hooks/useQueryParam';
import { useCallback } from 'react';
import { useStore } from 'react-redux';
import { NavLink, useNavigate, useParams } from 'react-router-dom';
import { useAppDispatch } from 'store';
import {
    enterpriseAppointmentsActions,
    selectEditData,
} from 'store/slices/enterprise/appointments';
import AppointmentEditHeader from 'views/Appointments/common/AppointmentEditHeader';
import { useEnterpriseEditAppointmentData } from 'views/Appointments/common/hooks';
import ErrorJumbotron from 'views/Components/ErrorJumbotron';
import PageContent, { WidePageLayout } from 'views/Components/Page';
import AppointmentPartB from '../AppointmentPartB';
import AppointmentStepOne from '../AppointmentStepOne';
import AppointmentStepThree from '../AppointmentStepThree';
import AppointmentStepTwo from '../AppointmentStepTwo';
import { withClearAppointmentState } from '../ClearAppointmentStateGuard';

function EditAppointment() {
    const [shopId] = useQueryParam('shopId');
    const { num: number } = useParams<{ num?: string }>();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    useDocumentTitle(t('titles.appointments'));

    const { isLoading } = useQuery(
        ['enterprise', 'appointments', { shopId, number }],
        () => EnterpriseAppointmentsApi.getDetailsByNumber(shopId!, number!),
        {
            enabled: !!number && !!shopId,
            onSuccess: (response) => {
                dispatch(enterpriseAppointmentsActions.setInitialAppointmentData(response));
            },
        }
    );

    let content: React.ReactNode;

    if (!number || !shopId) {
        content = (
            <ErrorJumbotron title="Invalid URL parameters">
                You must provide a <code>shopId</code> and an appointment number in a URL.
                <br />
                <NavLink to={ENTERPRISE_ROUTES.APPOINTMENTS}>Go back</NavLink>
            </ErrorJumbotron>
        );
    } else {
        content = (
            <CustomersSearchApiProvider mode="enterprise" shopId={shopId}>
                <EditAppointmentContent loading={isLoading} />
            </CustomersSearchApiProvider>
        );
    }

    return (
        <WidePageLayout>
            <PageContent paddedX>{content}</PageContent>
        </WidePageLayout>
    );
}

export default withClearAppointmentState(EditAppointment);

const EditAppointmentContentRoot = styled('div')({
    display: 'grid',
    gridTemplate: '1fr auto / 9fr 3fr',
    gap: 15,
    padding: '33px 0',
    '& .MuiDivider-root': {
        margin: '22px 0',
        backgroundColor: 'var(--neutral3)',
    },
    '& > aside': {},
    '& > :first-child': {
        gridColumn: '1 / 3',
    },
});

export function useAppointmentEditDataGetter() {
    const store = useStore();
    return useCallback(() => selectEditData(store.getState()), [store]);
}

export function useUpdateAppointmentMutation() {
    const getState = useAppointmentEditDataGetter();
    const navigate = useNavigate();

    return useMutation(
        async () => {
            const { enterpriseData, appointmentData: a } = getState();
            if (!enterpriseData.shopId) throw new Error('shopId is required');
            if (!a.serviceAdvisorId) throw new Error('serviceAdvisorId is required');
            if (!a.customerId) throw new Error('customerId is required');
            if (!a.id) throw new Error('id is required');

            await EnterpriseAppointmentsApi.update(enterpriseData.shopId, a.id, {
                observations: a.observations,
                originId: a.origin?.id ?? null,
                withValetService: a.withValetService,
                declinedItemReasonsForView: a.declinedItemReasonsForView,
                reasons: a.reasons.map((x) => x.id),
                customReasons: a.customReasons || [],
                promisedAt: a.deliverDate,
                startsAt: a.startsAt,
                duration: null,
                serviceAdvisorId: a.serviceAdvisorId,

                // NOTE: customer and vehicle updates are not restricted by permissions in enterprises
                customerUpdateData: {
                    customer: {
                        id: a.customerId,
                        firstName: a.customerFirstName,
                        lastName: a.customerLastName,
                        email: a.customerEmail,
                        taxIdentification: a.customerTaxId,
                        mobile: a.customerMobile,
                    },
                },
                vehicleUpdateData: {
                    vehicle: a.vehicleId
                        ? {
                              id: a.vehicleId,
                              brand: a.vehicleBrand,
                              model: a.vehicleModel,
                              year: a.vehicleYear,
                              color: a.vehicleColor,
                              plates: a.vehiclePlates,
                              vin: a.vehicleVin,
                          }
                        : null,
                },
            });
        },
        {
            onSuccess: () => {
                navigate(ENTERPRISE_ROUTES.APPOINTMENTS);
            },
        }
    );
}

function EditAppointmentContent({ loading }: { loading: boolean }) {
    const { valid, modified } = useEnterpriseEditAppointmentData();
    const navigate = useNavigate();
    const updateAppointment = useUpdateAppointmentMutation();
    const getState = useAppointmentEditDataGetter();
    const { enterpriseData } = getState();

    return (
        <EditAppointmentContentRoot>
            <header>
                <AppointmentEditHeader
                    modified={modified}
                    valid={valid}
                    isSaving={loading || updateAppointment.isLoading}
                    onCancel={() => navigate(ENTERPRISE_ROUTES.APPOINTMENTS)}
                    onSave={() => updateAppointment.mutate()}
                />
            </header>

            <section>
                <AppointmentStepOne repairShopKey={enterpriseData.shopId ?? undefined} />
                <AppointmentStepTwo />
                <AppointmentStepThree />
            </section>

            <aside>
                <AppointmentPartB />
            </aside>
        </EditAppointmentContentRoot>
    );
}
