import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    whoSchedules: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[8],
        marginRight: 5,
    },
    whoSchedulesValue: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[8],
    },
    line: {
        borderTop: '1px solid #EBEBEB',
        width: '100%',
        margin: '15px 0px',
    },
    caption: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
    },
    radioLabelRoot: {
        margin: 0,
    },
    radioLabel: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[6],
    },
    timePicker: {
        paddingBottom: 8,
    },
    tooltip: {
        display: 'flex',
        alignItems: 'center',
    },
}));

export default useStyles;
