import { Switch } from '@mui/material';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useQuery } from '@tanstack/react-query';
import { AppointmentOriginDto, AppointmentStatus } from 'api/appointments';
import EnterpriseAppointmentNotesApi, { AppointmentNoteDto } from 'api/enterprise/appointmentNotes';
import EnterpriseAppointmentsApi from 'api/enterprise/appointments';
import { TimeSpan } from 'api/utils/format';
import InfoTooltip from 'common/components/InfoTooltip';
import DateFormField from 'common/components/Inputs/DateFormField';
import { TextArea } from 'common/components/Inputs/TextArea';
import TextField from 'common/components/Inputs/TextField';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import Radio from 'common/components/Radio';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { AppointmentStatusEnum } from 'datacontracts/Scheduler';
import { ConversationDto } from 'datacontracts/WhatsApp/ConversationDto';
import { isEqual } from 'lodash';
import moment from 'moment';
import { memo, useEffect, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { useCurrentUser } from 'store/slices/user';
import { useDebounce } from 'use-debounce';
import { isDateValid } from 'utils';
import { useAppointmentSettingsForShop } from 'views/enterprise/_common';
import AppointmentNotes from './AppointmentNotes';
import OriginsSelector from './OriginsSelector';
import useStyles from './css';

export type PreviewProps = {
    conversation: ConversationDto;
};

const selectPartBState = createSelector(
    selectEditData,
    ({
        appointmentData: { observations, number, origin, withValetService, id, status },
        enterpriseData: { shopId },
    }) => ({
        status,
        id,
        observations,
        origin,
        withValetService,
        number,
        shopId,
    })
);

const AppointmentPartB = () => {
    const classes = useStyles();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const user = useCurrentUser();
    const toaster = useToasters();
    const { shopId, ...appointmentData } = useAppSelector(selectPartBState, isEqual);

    const [isAppointmentNumberInUse, setIsAppointmentNumberInUse] = useState(false);
    const [debouncedNumber] = useDebounce(appointmentData.number, 300);

    useEffect(() => {
        if (!debouncedNumber?.trim() || !shopId) {
            setIsAppointmentNumberInUse(false);
            return;
        }

        const checkIsAppointmentNumberInUse = async () => {
            const res = await EnterpriseAppointmentsApi.isAppointmentNumberInUse(
                shopId,
                debouncedNumber
            );
            setIsAppointmentNumberInUse(res);
        };

        checkIsAppointmentNumberInUse();
    }, [debouncedNumber, shopId]);

    useEffect(() => {
        if (isAppointmentNumberInUse && !appointmentData.id) {
            toaster.warning(
                t('appointments.weSuggestUsingAnotherNumber'),
                t('appointments.appointmentNumberAlreadyTaken')
            );
        }
    }, [isAppointmentNumberInUse, t, toaster, debouncedNumber, appointmentData.id]);

    const appointmentSettings = useAppointmentSettingsForShop(shopId);
    const origin = appointmentData.origin;
    const setOrigin = (origin: AppointmentOriginDto) =>
        dispatch(enterpriseAppointmentsActions.setOrigin(origin));
    const [newNoteIdentity, setNewNoteIdentity] = useState<number>(0);
    const [appointmentNotes, setAppointmentNotes] = useState<AppointmentNoteDto[]>([]);

    const { data, refetch } = useQuery(
        ['enterprise', 'appointmentNotes', shopId, appointmentData?.id],
        () => EnterpriseAppointmentNotesApi.getNotes(shopId || '', appointmentData?.id || ''),
        {
            enabled: Boolean(shopId && appointmentData?.id),
            staleTime: 30000, // 30 seconds
            cacheTime: Infinity,
        }
    );

    const handleAppointmentNotesChange = (notes: AppointmentNoteDto[]) => {
        setAppointmentNotes(notes);
        dispatch(enterpriseAppointmentsActions.setNotes(notes));
        if (Boolean(appointmentData?.id)) {
            refetch();
        }
    };

    useEffect(() => {
        if (data) {
            setAppointmentNotes(data);
        }
    }, [data]);

    return (
        <Grid container spacing={0}>
            <Grid item xs={12}>
                <Box style={{ display: 'flex', alignItems: 'end' }}>
                    <Typography className={classes.whoSchedules}>
                        {t('appointments.personWhoSchedules')}:
                    </Typography>
                    <Typography className={classes.whoSchedulesValue}>
                        {user.displayName}
                    </Typography>
                </Box>
                <div className={classes.line} />
                <Box>
                    <TextField
                        name="appointment-number"
                        cmosVariant="grey"
                        label={t('appointments.appointmentNumber')}
                        placeholder={
                            appointmentSettings?.activateAutomaticAppointmentNumber
                                ? t('appointments.itIsAssignedAutomatically')
                                : t('appointments.enterTheAppointmentNumber')
                        }
                        isRequired
                        showValidationIndicators
                        disabled={
                            !!appointmentData.id ||
                            appointmentSettings?.activateAutomaticAppointmentNumber ||
                            false
                        }
                        value={appointmentData.number}
                        isInvalid={isAppointmentNumberInUse}
                        onChange={(event) => {
                            dispatch(
                                enterpriseAppointmentsActions.updateAppointmentData({
                                    appointment: { number: event.target.value.toUpperCase() },
                                })
                            );
                        }}
                    />
                </Box>
                <div className={classes.line} />
                {appointmentData?.status === 'Unconfirmed' ? null : (
                    <Box>
                        <Typography className={classes.caption}>
                            {t('appointments.status.appointmentStatus')}
                            <span style={{ color: Colors.CM1 }}> *</span>
                        </Typography>
                        <RadioGroup
                            defaultValue={'Unconfirmed'}
                            aria-label="status"
                            name="appointmentStatus"
                            value={appointmentData?.status || 'Unconfirmed'}
                            onChange={(_, value) => {
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { status: value as AppointmentStatus },
                                    })
                                );
                            }}
                        >
                            <FormControlLabel
                                classes={{
                                    root: classes.radioLabelRoot,
                                    label: classes.radioLabel,
                                }}
                                disabled={
                                    !!appointmentData.id ||
                                    appointmentData?.status === 'OrderCreated'
                                }
                                value={AppointmentStatusEnum.Unconfirmed}
                                control={<Radio className="" />}
                                label={t('appointments.status.unconfirmed')}
                            />
                            <div className={classes.tooltip}>
                                <FormControlLabel
                                    classes={{
                                        root: classes.radioLabelRoot,
                                        label: classes.radioLabel,
                                    }}
                                    disabled={
                                        !!appointmentData.id ||
                                        appointmentData.status === 'OrderCreated'
                                    }
                                    value={AppointmentStatusEnum.Confirmed}
                                    control={<Radio className="" />}
                                    label={t('appointments.status.confirmed')}
                                />
                                <InfoTooltip text={t('appointments.status.confirmedDisabled')} />
                            </div>
                            <FormControlLabel
                                classes={{
                                    root: classes.radioLabelRoot,
                                    label: classes.radioLabel,
                                }}
                                value={AppointmentStatusEnum.CustomerArrived}
                                disabled={appointmentData.status === 'OrderCreated'}
                                control={<Radio className="" />}
                                label={t('appointments.status.customerArrived')}
                            />
                            <FormControlLabel
                                classes={{
                                    root: classes.radioLabelRoot,
                                    label: classes.radioLabel,
                                }}
                                value={AppointmentStatusEnum.CustomerDidNotArrive}
                                disabled={appointmentData.status === 'OrderCreated'}
                                control={<Radio className="" />}
                                label={t('appointments.status.customerDidNotArrive')}
                            />
                            <div className={classes.tooltip}>
                                <FormControlLabel
                                    classes={{
                                        root: classes.radioLabelRoot,
                                        label: classes.radioLabel,
                                    }}
                                    disabled={Boolean(appointmentData.id)}
                                    value={AppointmentStatusEnum.OrderCreated}
                                    control={<Radio className="" />}
                                    label={t('appointments.status.orderCreated')}
                                />
                                <InfoTooltip text={t('appointments.status.orderCreatedDisabled')} />
                            </div>
                        </RadioGroup>
                    </Box>
                )}
                <Box>
                    <OriginsSelector shopId={shopId} selected={origin} onSelected={setOrigin} />
                </Box>
                {appointmentSettings?.valetServiceEnabled ? (
                    <div>
                        <ValetServiceTopLine />
                        <ValetServiceContainer>
                            <SSwitch
                                checked={appointmentData.withValetService}
                                onChange={(_, checked) => {
                                    dispatch(
                                        enterpriseAppointmentsActions.updateAppointmentData({
                                            appointment: { withValetService: checked },
                                        })
                                    );
                                }}
                                name="checkedA"
                            />
                            <ValetServiceLabel>{t('appointments.valetService')}</ValetServiceLabel>
                        </ValetServiceContainer>
                        <ValetServiceBottomLine />
                    </div>
                ) : (
                    <div className={classes.line} />
                )}

                <Box>
                    <DeliveryDate />
                </Box>
                <div className={classes.line} />
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                    }}
                >
                    <AppointmentNotes
                        shopId={shopId}
                        appointmentId={appointmentData.id}
                        notesType="ForCustomer"
                        newNoteIdentity={newNoteIdentity}
                        onAddNewNote={() => setNewNoteIdentity((identity) => identity + 1)}
                        notes={appointmentNotes}
                        onChange={handleAppointmentNotesChange}
                    />
                    <AppointmentNotes
                        shopId={shopId}
                        appointmentId={appointmentData.id}
                        notesType="ForInternal"
                        newNoteIdentity={newNoteIdentity}
                        onAddNewNote={() => setNewNoteIdentity((identity) => identity + 1)}
                        notes={appointmentNotes}
                        onChange={handleAppointmentNotesChange}
                    />
                </Box>
                <div className={classes.line} />
                <Box style={{ paddingBottom: 33 }}>
                    <TextArea
                        name="appointment-observations"
                        label={t('appointments.observations')}
                        placeholder={t('appointments.enterTheObservations')}
                        value={appointmentData.observations || ''}
                        rows={3}
                        onChange={(event) => {
                            dispatch(
                                enterpriseAppointmentsActions.updateAppointmentData({
                                    appointment: { observations: event.target.value },
                                })
                            );
                        }}
                    />
                </Box>
            </Grid>
        </Grid>
    );
};

const selectDeliveryDate = createSelector(selectEditData, (d) => d.appointmentData.deliverDate);

const DeliveryDate = memo(() => {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const deliveryDate = useAppSelector(selectDeliveryDate);
    const deliveryDateMoment = useMemo(
        () => (deliveryDate ? moment(`${deliveryDate.date} ${deliveryDate.time}`) : null),
        [deliveryDate]
    );
    const currentDate = new Date();
    const classes = useStyles();

    return (
        <>
            <DateFormField
                disablePast
                clearable
                name="delivery-date"
                enableEnterComplete
                label={t('appointments.date')}
                variant="grey"
                onChange={(date) => {
                    if (date && isDateValid(date)) {
                        dispatch(
                            enterpriseAppointmentsActions.updateAppointmentData({
                                appointment: {
                                    deliverDate: {
                                        time: deliveryDate?.time ?? '00:00:00',
                                        date: moment(date).format('YYYY-MM-DD'),
                                    },
                                },
                            })
                        );
                    }
                }}
                value={deliveryDateMoment?.toDate()}
            />
            <TimeFormField
                name="delivery-hour"
                disablePast={
                    currentDate.getDay() === deliveryDateMoment?.day() &&
                    currentDate.getMonth() === deliveryDateMoment?.month()
                }
                cmosVariant="grey"
                label={t('appointments.time')}
                placeholder={t('appointments.selectAHour')}
                value={
                    deliveryDateMoment
                        ? [deliveryDateMoment.hour(), deliveryDateMoment.minute()]
                        : null
                }
                onChange={([h, m]) => {
                    dispatch(
                        enterpriseAppointmentsActions.updateAppointmentData({
                            appointment: {
                                deliverDate: {
                                    date: deliveryDate?.date ?? moment().format('YYYY-MM-DD'),
                                    time: TimeSpan.fromParts(h, m).toString(),
                                },
                            },
                        })
                    );
                }}
                slotProps={{
                    inputWrapper: {
                        className: classes.timePicker,
                    },
                }}
            />
        </>
    );
});

const ValetServiceTopLine = styled('div')({
    borderTop: '1px solid #EBEBEB',
    width: '100%',
    marginTop: 15,
    marginBottom: 3,
});

const ValetServiceBottomLine = styled('div')({
    borderTop: '1px solid #EBEBEB',
    width: '100%',
    marginBottom: 15,
    marginTop: 3,
});

const ValetServiceContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 5,
});

const SSwitch = styled(Switch)(({ theme }) => ({
    '& .MuiSwitch-switchBase': {
        '&.Mui-checked': {
            color: theme.palette.neutral[1],
            '& .MuiSwitch-thumb:before': {
                color: theme.palette.neutral[1],
            },
            '& + .MuiSwitch-track': {
                opacity: 1,
            },
        },
        '& .MuiSwitch-thumb:before': {
            color: theme.palette.neutral[1],
        },
    },
}));

const ValetServiceLabel = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[8],
}));

export default memo(AppointmentPartB);
