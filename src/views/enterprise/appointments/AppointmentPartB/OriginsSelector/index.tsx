import { AppointmentOriginDto } from 'api/appointments';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import { useEnterpriseAppointmentOrigins } from 'views/Appointments/common';

type OriginsSelectorProps = {
    shopId: string | null;
    selected: AppointmentOriginDto | undefined | null;
    onSelected: (selected: AppointmentOriginDto) => void;
};

export default function OriginsSelector({ shopId, selected, onSelected }: OriginsSelectorProps) {
    const { data } = useEnterpriseAppointmentOrigins(shopId);
    const { t } = useAppTranslation();

    const options = useMemo(
        () =>
            (data ?? []).map((o) => ({
                label: o.name,
                value: o.id,
            })),
        [data]
    );
    const selectedOption = useMemo(
        () => options.find((o) => o.value === selected?.id),
        [options, selected]
    );

    return (
        <Dropdown
            name="origin"
            cmosVariant="grey"
            options={options}
            value={selectedOption}
            onChange={(o: OptionData<string> | null) => {
                if (o === null) return;
                const newSelected = data?.find((x) => x.id === o.value) ?? {
                    id: o.value,
                    name: o.label,
                };
                onSelected(newSelected);
            }}
            label={t('appointments.appointmentOrigin')}
            placeholder={t('appointments.selectAnOrigin')}
        />
    );
}
