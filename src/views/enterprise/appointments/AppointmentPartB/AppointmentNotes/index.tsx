import { Typography } from '@mui/material';
import Box from '@mui/material/Box';
import { useMutation } from '@tanstack/react-query';
import { AppointmentNoteDto } from 'api/appointmentNotes';
import EnterpriseAppointmentNotesApi, {
    AppointmentNoteType,
} from 'api/enterprise/appointmentNotes';
import { JobTitle } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import { useCurrentUser } from 'store/slices/user';
import AppointmentNoteForm from 'views/Appointments/common/AppointmentNotesForm';
import NoteEditor from 'views/Appointments/common/AppointmentNotesForm/NoteEditor';

type AppointmentNoteFormProps = {
    shopId: string | null;
    appointmentId?: string;
    notesType: AppointmentNoteType;
    newNoteIdentity: number;
    onAddNewNote: () => void;
    notes?: AppointmentNoteDto[];
    onChange?: (notes: AppointmentNoteDto[]) => void;
};

const AppointmentNotes = ({
    notes = [],
    shopId,
    appointmentId,
    newNoteIdentity,
    onAddNewNote,
    notesType,
    onChange,
}: AppointmentNoteFormProps) => {
    const { t } = useAppTranslation();
    const user = useCurrentUser();
    const [noteId, setNoteId] = useState<string>();

    const { addNoteMutate } = useAddNoteMutation((data) => {
        onChange && onChange([...notes, data]);
    });

    const { updateNoteMutate } = useUpdateNoteMutation((data) => {
        onChange && onChange(notes.map((x) => (x.id === data.id ? data : x)));
    });

    const { deleteNoteMutate } = useDeleteNoteMutation(() => {
        onChange && onChange(notes.filter((x) => x.id !== noteId));
        setNoteId(undefined);
    });

    const handleSave = (note: string) => {
        if (appointmentId && shopId) {
            addNoteMutate({ note, shopId, appointmentId, noteType: notesType });
        } else {
            onChange &&
                onChange([
                    ...notes,
                    {
                        id: `${newNoteIdentity}-${appointmentId || user.key}`,
                        isFromCustomer: false,
                        type: notesType,
                        section: 'Appointments',
                        createdAt: new Date(),
                        note,
                        user: {
                            id: user.key,
                            name: user.name,
                            job: user.jobTitle ? (user.jobTitle as JobTitle) : '',
                        },
                        historyNotes: null,
                    },
                ]);
            onAddNewNote();
        }
    };

    const handleEdit = (note: AppointmentNoteDto) => {
        if (appointmentId && shopId) {
            updateNoteMutate({
                note: note.note,
                shopId,
                appointmentNoteId: note.id,
            });
        } else {
            const index = notes.findIndex((n) => n.id === note.id);
            if (index >= 0) {
                onChange &&
                    onChange(notes.map((x) => (x.id === note.id ? { ...x, note: note.note } : x)));
            }
        }
    };

    const handleDelete = (note: AppointmentNoteDto) => {
        if (appointmentId && shopId) {
            setNoteId(note.id);
            deleteNoteMutate({ shopId, appointmentNoteId: note.id });
        } else {
            onChange && onChange(notes.filter((x) => x.id !== note.id));
        }
    };

    const filteredNotes = notes.filter((x) => x.type === notesType);

    return (
        <Box component="div">
            <Typography
                component="div"
                sx={(theme) => ({
                    fontFamily: 'Inter',
                    fontSize: '12px',
                    fontStyle: 'normal',
                    fontWeight: 700,
                    lineHeight: 'normal',
                    color: theme.palette.neutral[8],
                    marginBottom: '5px',
                })}
            >
                {notesType === 'ForCustomer'
                    ? t('appointments.notesVisibleForCustomer')
                    : t('appointments.internalNotes')}
            </Typography>
            {filteredNotes.map((note, index) => (
                <Box key={`${note.id}-${index}`} component="div" sx={{ padding: '5px 0px' }}>
                    <NoteEditor
                        appointmentNote={note}
                        editSection="Appointments"
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                </Box>
            ))}
            <Box sx={{ paddingTop: '3px' }}>
                <AppointmentNoteForm onClickSave={handleSave} />
            </Box>
        </Box>
    );
};

const useAddNoteMutation = (onSuccess?: (data: AppointmentNoteDto) => void) => {
    const { mutate: addNoteMutate } = useMutation(
        (body: {
            note: string;
            shopId: string;
            appointmentId: string;
            noteType: AppointmentNoteType;
        }) =>
            EnterpriseAppointmentNotesApi.addNote(
                body.shopId,
                body.appointmentId,
                body.note,
                body.noteType
            ),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { addNoteMutate };
};

const useUpdateNoteMutation = (onSuccess?: (data: AppointmentNoteDto) => void) => {
    const { mutate: updateNoteMutate } = useMutation(
        (body: { note: string; shopId: string; appointmentNoteId: string }) =>
            EnterpriseAppointmentNotesApi.updateNote(
                body.shopId,
                body.appointmentNoteId,
                body.note
            ),
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
            },
        }
    );
    return { updateNoteMutate };
};

const useDeleteNoteMutation = (onSuccess?: () => void) => {
    const { mutate: deleteNoteMutate } = useMutation(
        (body: { shopId: string; appointmentNoteId: string }) =>
            EnterpriseAppointmentNotesApi.deleteNote(body.shopId, body.appointmentNoteId),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );
    return { deleteNoteMutate };
};

export default AppointmentNotes;
