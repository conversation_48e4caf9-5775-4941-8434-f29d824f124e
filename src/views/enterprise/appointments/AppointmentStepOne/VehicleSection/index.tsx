import { Grid } from '@mui/material';
import { VehicleDetailsDto, VehicleListItemDto } from 'api/customers';
import { EnterpriseCustomersApi } from 'api/enterprise';
import TextField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { isEqual } from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { getCustomerHistory } from 'store/slices/enterprise/appointments/thunks/getCustomerHistory';
import { AppointmentSection } from 'views/Appointments/common/basic-components';
import BrandPicker from 'views/Components/BrandPicker';
import CreateNewVehiclePopupCustomApi from 'views/Components/CreateNewVehiclePopup/CreateNewVehiclePopupCustomApi';
import CustomerVehiclesAutocompleteCustomApi, {
    useVehiclesQueryModifiers,
} from 'views/Components/CustomerVehiclesAutocomplete/CustomerVehiclesAutocompleteCustomApi';
import ModelPicker from 'views/Components/ModelPicker';
import YearPicker from 'views/Components/YearPicker';

const selectVehicleSectionData = createSelector(
    selectEditData,
    ({
        vehicle,
        customer,
        appointmentData: {
            vehicleBrand,
            vehicleColor,
            vehicleId,
            vehicleModel,
            vehiclePlates,
            vehicleVin,
            vehicleYear,
        },
        initialVehicle,
        enterpriseData: { shopId },
    }) => ({
        vehicleBrand,
        vehicleColor,
        vehicleId,
        vehicleModel,
        vehiclePlates,
        vehicleVin,
        vehicleYear,
        vehicle,
        shopId,
        customerId: customer?.id,
        initialVehicle,
    })
);

export default function VehicleSection({ repairShopKey }: { repairShopKey?: string }) {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { vehicle, customerId, shopId, initialVehicle, ...appointment } = useAppSelector(
        selectVehicleSectionData,
        isEqual
    );
    const disabled = !shopId;
    const cacheKey = customerId ?? '';
    const vehicleSelected = !!vehicle;

    const [createVehiclePopupOpen, setCreateVehiclePopupOpen] = useState(false);

    const { add: addVehicle } = useVehiclesQueryModifiers();

    useEffect(() => {
        if (repairShopKey && customerId && vehicle) {
            dispatch(
                getCustomerHistory({
                    shopId: repairShopKey,
                    customerId: customerId,
                    vehicleId: vehicle.id,
                })
            );
        }
    }, [repairShopKey, customerId, vehicle, dispatch]);

    const onVehicleCreated = useCallback(
        (v: VehicleDetailsDto) => {
            dispatch(enterpriseAppointmentsActions.setNewVehicle(v));
            if (customerId) addVehicle(customerId, v);
        },
        [dispatch, addVehicle, customerId]
    );

    const onVehicleSelected = useCallback(
        (v?: VehicleListItemDto) => {
            if (v) {
                dispatch(enterpriseAppointmentsActions.setVehicleFromListItem(v));
            } else {
                dispatch(enterpriseAppointmentsActions.removeVehicle());
            }
        },
        [dispatch]
    );

    const onVehicleAutoSelected = useCallback(
        (v: VehicleListItemDto) => {
            dispatch(enterpriseAppointmentsActions.setVehicleFromListItem(v));
        },
        [dispatch]
    );

    return (
        <>
            <CreateNewVehiclePopupCustomApi
                createNewVehicle={useCallback(
                    (c) => {
                        if (shopId) return EnterpriseCustomersApi.createVehicle(shopId, c);
                        throw new Error('shopId is not defined');
                    },
                    [shopId]
                )}
                open={createVehiclePopupOpen}
                repairShopKey={shopId ?? undefined}
                onClose={() => setCreateVehiclePopupOpen(false)}
                onVehicleCreated={onVehicleCreated}
                customerId={customerId ?? null}
            />
            <AppointmentSection>
                <CustomerVehiclesAutocompleteCustomApi
                    disabled={disabled}
                    cacheKey={cacheKey}
                    getVehicles={useCallback(
                        (c) => {
                            if (shopId) return EnterpriseCustomersApi.getVehicles(shopId, c);
                            throw new Error('shopId is not defined');
                        },
                        [shopId]
                    )}
                    customerId={customerId}
                    initialValue={initialVehicle}
                    value={vehicle?.id}
                    onSelect={onVehicleSelected}
                    onAutoSelect={onVehicleAutoSelected}
                    onOpenPopup={() => setCreateVehiclePopupOpen(true)}
                />
            </AppointmentSection>
            <AppointmentSection>
                <Grid container spacing={1}>
                    <Grid item xs={12} md={2}>
                        <TextField
                            name="vehicle-plate"
                            cmosVariant="grey"
                            label={t('appointments.step1.plates')}
                            placeholder={t('appointments.step1.plates')}
                            disabled={!vehicleSelected || disabled}
                            value={appointment.vehiclePlates || ''}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { vehiclePlates: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextField
                            name="vehicle-vin"
                            cmosVariant="grey"
                            label={t('commonLabels.vin')}
                            placeholder={t('commonLabels.vin')}
                            disabled={!vehicleSelected}
                            value={appointment.vehicleVin || ''}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { vehicleVin: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <BrandPicker
                            disabled={!vehicleSelected || disabled}
                            value={appointment.vehicleBrand ?? undefined}
                            enterpriseRepairShopKey={repairShopKey}
                            onChange={(brandName) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: {
                                            vehicleBrand: brandName,
                                            vehicleModel: '',
                                            vehicleYear: '',
                                        },
                                    })
                                )
                            }
                            name="vehicle-brand"
                            label={t('appointments.step1.brand')}
                            placeholder={t('appointments.step1.brand')}
                            cmosVariant="grey"
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <ModelPicker
                            brandName={appointment.vehicleBrand ?? undefined}
                            name="vehicle-model"
                            label={t('appointments.step1.model')}
                            placeholder={t('appointments.step1.model')}
                            disabled={!vehicleSelected || disabled}
                            value={appointment.vehicleModel}
                            onChange={(modelName) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: {
                                            vehicleModel: modelName,
                                            vehicleYear: '',
                                        },
                                    })
                                )
                            }
                            cmosVariant="grey"
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <YearPicker
                            name="vehicle-year"
                            modelName={appointment.vehicleModel ?? undefined}
                            brandName={appointment.vehicleBrand ?? undefined}
                            label={t('appointments.step1.year')}
                            placeholder={t('appointments.step1.year')}
                            disabled={!vehicleSelected || disabled}
                            value={appointment.vehicleYear}
                            onChange={(y) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: {
                                            vehicleYear: y,
                                        },
                                    })
                                )
                            }
                            cmosVariant="grey"
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextField
                            name="vehicle-color"
                            cmosVariant="grey"
                            label={t('appointments.step1.color')}
                            placeholder={t('appointments.step1.color')}
                            disabled={!vehicleSelected || disabled}
                            value={appointment.vehicleColor || ''}
                            onChange={(event) => {
                                if (vehicle)
                                    dispatch(
                                        enterpriseAppointmentsActions.updateAppointmentData({
                                            appointment: {
                                                vehicleColor: event.target.value,
                                            },
                                        })
                                    );
                            }}
                        />
                    </Grid>
                </Grid>
            </AppointmentSection>
        </>
    );
}
