import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        ...theme.typography.body1,
        borderRadius: '100px',
        background: '#F6F6F6',
        border: `1px solid ${theme.palette.neutral[4]}`,
        height: 32,
        paddingLeft: 16,
        '&:focus': {
            border: `solid 1px ${theme.palette.primary.main}`,
        },
    },
    autocomplete: {
        width: '100%',
    },
    title: {
        ...theme.typography.h4Inter,
        color: theme.palette.primary.main,
    },
    subTitle: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[8],
    },
}));

export default useStyles;
