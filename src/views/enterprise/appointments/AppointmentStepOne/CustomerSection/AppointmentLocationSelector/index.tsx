import { styled } from '@mui/material';
import { LocationDto } from 'api/enterprise';
import { GetOffsetLabel } from 'common/DateTimeHelpers';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { LocationSelectorBaseSingle } from 'common/components/LocationSelector/LocationSelectorBaseSingle';
import CancelModal from 'common/components/Popups/CancelModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { useAppointmentSettings } from 'views/enterprise/_common';

const SLocationSelectorBaseSingle = styled(LocationSelectorBaseSingle)({
    flex: 1,
    '& fieldset': {
        borderColor: 'var(--neutral4)',
    },
    '& .MuiInputBase-input': {
        color: 'var(--neutral8)',
        fontWeight: 'normal',
        '&::placeholder': {
            color: 'var(--neutral8)',
        },
    },
});

const selectData = createSelector(selectEditData, (d) => ({
    shopId: d.enterpriseData.shopId,
    isNew: !d.appointmentData.id,
    hasData: !!d.appointmentData.customerId,
}));

export default function AppointmentLocationSelector() {
    const dispatch = useAppDispatch();
    const { shopId, isNew, hasData } = useAppSelector(selectData);
    const { t } = useAppTranslation();
    const [pendingChangeTo, setPendingChangeTo] = useState<LocationDto | null>(null);
    const showLocationChangeWarning = !!pendingChangeTo;

    const allShopSettings = useAppointmentSettings();

    const filterFn = useCallback(
        (location: LocationDto) => {
            if (!allShopSettings) return false;

            const settings = allShopSettings.find((x) => x.repairShopKey === location.repairShopId);
            if (!settings) return false;

            return !settings.synchronizeAppointmentsEnabled;
        },
        [allShopSettings]
    );

    const getToolTipText = useCallback(
        (location: LocationDto) => {
            if (!allShopSettings) return '';

            const settings = allShopSettings.find((x) => x.repairShopKey === location.repairShopId);
            if (!settings) return '';

            if (settings.synchronizeAppointmentsEnabled && !settings.omnichannelEnabled) {
                return t('appointments.appointmentMustBeCreatedUsing3rdPartySoftware');
            }

            return '';
        },
        [allShopSettings, t]
    );

    return (
        <>
            <InputWrapper label={t('locations.location')} showValidationIndicators isRequired>
                <SLocationSelectorBaseSingle
                    filterFn={filterFn}
                    numericPlaceholderAfterCount={1}
                    noValuePlaceholder={t('locations.selectLocation')}
                    getLocationLabel={(l) => `${l.name} ${GetOffsetLabel(l.timeZone)}`}
                    getLocationTooltip={(l) => getToolTipText(l)}
                    value={shopId}
                    disabled={!isNew}
                    onChange={(shopId, location) => {
                        if (hasData) {
                            setPendingChangeTo(location);
                        } else {
                            dispatch(enterpriseAppointmentsActions.setShop(location));
                        }
                    }}
                />
            </InputWrapper>
            {showLocationChangeWarning && (
                <CancelModal
                    title={t('locations.locationSwitchWarning.title')}
                    text={t('locations.locationSwitchWarning.text')}
                    open
                    cancelCaptionButton={t('commonLabels.cancel')}
                    confirmationCaptionButton={t('locations.locationSwitchWarning.yes')}
                    onCancel={() => {
                        dispatch(enterpriseAppointmentsActions.setShop(pendingChangeTo));
                        setPendingChangeTo(null);
                    }}
                    onClose={() => setPendingChangeTo(null)}
                />
            )}
        </>
    );
}
