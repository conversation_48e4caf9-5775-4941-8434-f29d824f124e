import { Grid } from '@mui/material';
import { CustomerCreateDto, CustomerSearchItemDto } from 'api/Clients/Customers';
import { VehicleCreateDto } from 'api/customers';
import { EnterpriseCustomersApi } from 'api/enterprise';
import { isValidEmail, isValidMobileNumber } from 'common/Helpers';
import TextField from 'common/components/Inputs/TextField';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { isEqual } from 'lodash';
import { useCallback, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppDispatch, useAppSelector } from 'store';
import { enterpriseAppointmentsActions } from 'store/slices/enterprise/appointments';
import { selectEditData } from 'store/slices/enterprise/appointments/selectors';
import { selectCountryCode } from 'store/slices/globalSettingsSlice';
import { useNameValidator } from 'views/Appointments/common';
import { AppointmentSection } from 'views/Appointments/common/basic-components';
import CreateNewCustomerPopupCustomApi, {
    CustomerCreatedEvent,
} from 'views/Components/CreateNewCustomerPopup/CreateNewCustomerPopupCustomApi';
import CustomerAutocomplete from 'views/Components/CustomerAutocomplete';
import AppointmentLocationSelector from './AppointmentLocationSelector';

const selectCustomerSectionData = createSelector(
    selectEditData,
    ({
        appointmentData: {
            customerEmail,
            customerFirstName,
            customerLastName,
            customerId,
            customerMobile,
            customerTaxId,
        },
        enterpriseData: { shopId },
        initialCustomer,
        customer,
    }) => ({
        customerEmail,
        customerFirstName,
        customerLastName,
        customerId,
        customerMobile,
        customerTaxId,
        customer,
        shopId,
        initialCustomer,
    })
);

export default function CustomerSection() {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { customer, shopId, initialCustomer, ...appointment } = useAppSelector(
        selectCustomerSectionData,
        isEqual
    );
    const disabled = !shopId;
    const customerSelected = !!customer;
    const [isMobileValid, setIsMobileValid] = useState(true);

    const countryCode = useAppSelector(selectCountryCode);

    const onSelectCustomer = useCallback(
        (c: CustomerSearchItemDto) => {
            dispatch(enterpriseAppointmentsActions.setCustomerFromSearchItem(c));
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [dispatch]
    );

    // VALIDATION
    const nameValidation = useNameValidator(appointment.customerFirstName);
    const validEmail = !appointment.customerEmail || isValidEmail(appointment.customerEmail);

    // CREATE CUSTOMER POPUP
    const [createCustomerPopupOpen, setCreateCustomerPopupOpen] = useState(false);
    const onCustomerAndVehicleCreated = useCallback(
        (event: CustomerCreatedEvent) => {
            dispatch(
                enterpriseAppointmentsActions.setNewCustomerAndVehicle({
                    customer: event.customer,
                    vehicle: event.vehicle || undefined,
                })
            );
        },
        [dispatch]
    );

    const validateMobile = async () => {
        if (!appointment.customerMobile.trim()) {
            toasters.warning(
                t('appointments.step3.theMobileCannotBeEmpty'),
                t('appointments.step3.theClientCouldNotBeEdited')
            );
            setIsMobileValid(false);
            return;
        }

        if (!shopId) {
            throw Error('shopId is not defined');
        }
        if (!customer) {
            throw new Error('customer is not defined');
        }

        if (!isValidMobileNumber(countryCode, appointment.customerMobile)) {
            setIsMobileValid(false);
            return;
        }

        try {
            const isDuplicated = await EnterpriseCustomersApi.isDuplicatedMobile(
                shopId,
                customer.id,
                appointment.customerMobile
            );
            if (isDuplicated) {
                toasters.warning(
                    t('appointments.step3.theMobileIsRegistered'),
                    t('appointments.step3.theClientCouldNotBeEdited')
                );
                setIsMobileValid(false);
                return;
            }
        } catch (e) {}

        setIsMobileValid(true);
    };

    function createNewCustomer(c: CustomerCreateDto) {
        if (shopId) return EnterpriseCustomersApi.create(shopId, c);
        throw new Error('shopId is not defined');
    }

    function createNewVehicle(c: VehicleCreateDto) {
        if (shopId) return EnterpriseCustomersApi.createVehicle(shopId, c);
        throw new Error('shopId is not defined');
    }

    return (
        <>
            <CreateNewCustomerPopupCustomApi
                createNewVehicle={createNewVehicle}
                createNewCustomer={createNewCustomer}
                open={createCustomerPopupOpen && !disabled}
                repairShopKey={shopId ?? undefined}
                onClose={() => setCreateCustomerPopupOpen(false)}
                onCustomerAndVehicleCreated={onCustomerAndVehicleCreated}
            />
            <AppointmentSection>
                <AppointmentLocationSelector />
            </AppointmentSection>
            <ArrowTooltip
                disabled={!disabled}
                content={t('appointments.pleaseSelectLocationFirst')}
                position="bottom-start"
            >
                <AppointmentSection>
                    <CustomerAutocomplete
                        disabled={disabled}
                        value={customer?.id}
                        initiallySelected={initialCustomer}
                        onSelect={onSelectCustomer}
                        onOpenCreatePopup={() => setCreateCustomerPopupOpen(true)}
                    />
                </AppointmentSection>
            </ArrowTooltip>
            <AppointmentSection>
                <Grid container spacing={1} alignItems="flex-end">
                    <Grid item xs={12} md={2}>
                        <TextField
                            name="customer-name"
                            cmosVariant="grey"
                            label={t('appointments.step1.customerName')}
                            placeholder={t('appointments.step1.name')}
                            isRequired
                            showValidationIndicators
                            disabled={!customerSelected || disabled}
                            isInvalid={!nameValidation.valid}
                            value={appointment.customerFirstName || ''}
                            onBlur={nameValidation.validate}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { customerFirstName: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextField
                            name="customer-lastName"
                            cmosVariant="grey"
                            label={t('appointments.step1.lastNames')}
                            placeholder={t('appointments.step1.lastNames')}
                            showValidationIndicators
                            disabled={!customerSelected || disabled}
                            value={appointment.customerLastName || ''}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { customerLastName: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <TextField
                            name="customer-mobile"
                            cmosVariant="grey"
                            label={t('appointments.step1.mobile')}
                            placeholder={t('appointments.step1.mobile')}
                            isRequired
                            showValidationIndicators
                            disabled={!customerSelected || disabled}
                            isInvalid={!isMobileValid}
                            value={appointment.customerMobile || ''}
                            onBlur={validateMobile}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { customerMobile: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <TextField
                            name="customer-email"
                            cmosVariant="grey"
                            label={t('appointments.step1.email')}
                            placeholder={t('appointments.step1.email')}
                            disabled={!customerSelected || disabled}
                            value={appointment.customerEmail || ''}
                            isInvalid={!validEmail}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { customerEmail: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <TextField
                            name="customer-taxIdentification"
                            cmosVariant="grey"
                            label={t('appointments.step1.taxIdentification')}
                            placeholder={t('appointments.step1.taxIdentification')}
                            disabled={!customerSelected || disabled}
                            value={appointment.customerTaxId || ''}
                            onChange={(event) =>
                                dispatch(
                                    enterpriseAppointmentsActions.updateAppointmentData({
                                        appointment: { customerTaxId: event.target.value },
                                    })
                                )
                            }
                        />
                    </Grid>
                </Grid>
            </AppointmentSection>
        </>
    );
}
