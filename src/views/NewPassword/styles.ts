/* eslint-disable no-useless-computed-key */
import { makeStyles } from '@mui/styles';

export const useNewPasswordStyles = makeStyles((theme) => ({
    root: {
        flexGrow: 1,
    },
    formContainer: {
        /* display: 'flex', */
        width: 371,
        justifyContent: 'center',
        marginTop: '15vh',
    },
    row: {
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        margin: '14px 0 17px 0',
    },
    label: {
        fontFamily: 'inter',
        fontSize: 15,
        fontWeight: 900,
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: '2.13',
        letterSpacing: 'normal',
        textAlign: 'center',
        color: '#4a4d51',
    },
    spaceUp: {
        marginTop: '5vh',
    },
    success: {
        padding: '33px 40px 10px',
        borderRadius: 8,
        border: 'solid 1px #36ce91',
        backgroundColor: '#ffffff',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: '5vh',
    },
    successIcon: {
        width: 51,
        height: 51,
        padding: 20,
        border: 'solid 1px #36ce91',
        color: '#36ce91',
        borderRadius: '50%',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
    },
    successTitle: {
        fontFamily: 'inter',
        fontSize: 16,
        fontWeight: 900,
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: '1.06',
        letterSpacing: 'normal',
        textAlign: 'center',
        color: '#26292b',
    },
    successText: {
        fontFamily: 'Roboto',
        fontSize: 12,
        fontWeight: 'normal',
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: '1.17',
        letterSpacing: 'normal',
        textAlign: 'center',
        color: '#676767',
    },
    successTextContainer: {
        marginTop: -12,
    },
    backToLogin: {
        color: '#6897F5',
        textDecoration: 'none',
        fontFamily: 'Roboto',
        fontSize: 14,
        fontWeight: 'bold',
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: '1.17',
        letterSpacing: 'normal',
        textAlign: 'center',
        marginTop: 200,
    },
    backToLoginContainer: {
        marginTop: 20,
    },
}));
