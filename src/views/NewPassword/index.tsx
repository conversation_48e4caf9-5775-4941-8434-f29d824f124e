import DoneIcon from '@mui/icons-material/Done';
import { Box, Grid, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { Link, useParams } from 'react-router-dom';
import { SessionLogic } from '../../business/SessionLogic';
import { checkInvalidPassword } from '../../common/PasswordHelper';
import { Button } from '../../common/components/Button';
import { LeftIcon } from '../../common/components/Icons/LeftIcon';
import PasswordField from '../../common/components/Inputs/PasswordField';
import { NotificationType } from '../../common/components/Notification/INotificationProps';
import { NotificationPull } from '../../common/components/NotificationPull';
import { NotificationData } from '../../common/components/NotificationPull/NotificationData';
import { ROUTES } from '../../common/constants/RoutesDefinition';
import { IconSize } from '../../common/styles/IconSize';
import { useNewPasswordStyles } from './styles';

const BackLink = styled(Link)(({ theme }) => ({
    position: 'absolute',
    left: 20,
    top: 20,
    display: 'flex',
    alignItems: 'center',
    textDecoration: 'none',
    ...theme.typography.h5Inter,
    color: 'var(--cm3)',
    padding: '5px 10px 5px 0',
    borderRadius: 2,

    '&:hover': {
        color: 'var(--cm1)',
    },

    '&:active': {
        backgroundColor: 'var(--neutral3)',
    },
}));

const Header = styled('header')({
    width: '100%',
    minHeight: 80,
    position: 'relative',
});

const NewPassword = () => {
    const { t } = useAppTranslation();
    const googleReCaptchaProps = useGoogleReCaptcha();
    const styles = useNewPasswordStyles();
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [validConfirm, setValidConfirm] = useState(true);
    const [isSent, setIsSent] = useState(false);
    const [notificationError, setNotificationError] = useState<any>(null);
    const [labelBar, setLabelBar] = useState('');
    const [secureLevel, setSecureLevel] = useState(25);
    const [secureLevelClass, setSecureLevelClass] = useState('danger');
    const token = useParams<{ token: string }>().token ?? '';
    const [captcha, setCaptcha] = useState<any>();
    // Create an event handler so you can call the verification on button click event or form submit
    const handleReCaptchaVerify = async () => {
        const { executeRecaptcha } = googleReCaptchaProps;
        if (!executeRecaptcha) {
            console.warn('Execute recaptcha not yet available');
            return;
        }

        const token = await executeRecaptcha('resetpassword');
        setCaptcha(token);
        // Do whatever you want with the token
    };
    useEffect(() => {
        const errors = checkInvalidPassword(password);
        if (errors.length > 0 || password !== confirmPassword) {
            setValidConfirm(false);
        } else {
            setValidConfirm(true);
        }
    }, [password, confirmPassword]);

    useEffect(() => {
        if (Boolean(captcha)) send();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [captcha]);

    const didChange = (e: any) => {
        const { value } = e.target;
        const errors = checkInvalidPassword(value);
        if (errors.length > 0) {
            setLabelBar(t(`passwordValidation.label.${errors[0]}`));
            setSecurityLevelsByErrors(errors.length);
        } else {
            setLabelBar(t(`passwordValidation.secure`));
            setSecurityLevelsByErrors(0);
        }
        setPassword((old) => value);
    };

    const didChangeConfirm = (e: any) => {
        const { value } = e.target;
        setConfirmPassword(value);
    };

    const validateAndSend = () => {
        const errors = checkInvalidPassword(password);
        if (validConfirm) {
            checkCaptcha();
        } else if (password.length === 0 || confirmPassword.length === 0) {
            const notification = new NotificationData(
                t('passwordValidation.notification.empty'),
                t('passwordValidation.notification.title'),
                NotificationType.danger
            );
            setNotificationError(notification);
        } else if (password !== confirmPassword) {
            const notification = new NotificationData(
                t('passwordValidation.notification.equal'),
                t('passwordValidation.notification.title'),
                NotificationType.danger
            );
            setNotificationError(notification);
            return;
        } else if (errors.length > 0) {
            const notification = new NotificationData(
                '',
                t('passwordValidation.notification.title'),
                NotificationType.danger
            );
            notification.bodyElement = () => (
                <>
                    <ol style={{ listStyleType: 'inherit', paddingLeft: 5 }}>
                        {errors.map((error, i) => (
                            <li key={error}>{t(`passwordValidation.notification.${error}`)}</li>
                        ))}
                    </ol>
                </>
            );
            setNotificationError(notification);
            return;
        }
    };

    const setSecurityLevelsByErrors = (errors: number) => {
        if (errors >= 2) {
            setSecureLevel(25);
            setSecureLevelClass('danger');
        } else if (errors === 1) {
            setSecureLevelClass('warning');
            setSecureLevel(75);
        } else if (errors === 0) {
            setSecureLevelClass('success');
            setSecureLevel(100);
        }
    };

    const checkCaptcha = async () => {
        return await handleReCaptchaVerify();
    };
    const send = async () => {
        try {
            //TODO: send new password
            if (validConfirm) {
                try {
                    await SessionLogic.resetPassword(token, password, confirmPassword, captcha);
                    setIsSent(true);
                } catch (error: any) {
                    let notification;
                    if (error.response.status === 400) {
                        notification = new NotificationData(
                            error.response.data.messages.length > 0
                                ? error.response.data.messages[0]
                                : error.message,
                            t('login.wrongPassword'),
                            NotificationType.danger
                        );
                    } else {
                        notification = new NotificationData(
                            t('login.wrongPassword'),
                            'Request error',
                            NotificationType.danger
                        );
                    }
                    setNotificationError(notification);
                }
            }
        } catch (error) {
            setValidConfirm(false);
        }
    };

    // #region Rendered variables
    function MailInput() {
        return (
            <div className={styles.spaceUp}>
                <PasswordField
                    size="medium"
                    label={t('login.newPassword')}
                    placeholder={t('login.newPassword')}
                    onChange={didChange}
                    value={password}
                    isInvalid={secureLevel < 100}
                />

                <PasswordField
                    size="medium"
                    label={t('login.confirmNewPassword')}
                    placeholder={t('login.confirmNewPassword')}
                    onChange={didChangeConfirm}
                    value={confirmPassword}
                    isRequired={true}
                    isInvalid={!validConfirm}
                />
                <div className={styles.row} />
                <Button
                    label={t('login.resetPassword')}
                    cmosVariant={'filled'}
                    cmosSize={'large'}
                    blockMode
                    onClick={validateAndSend}
                />
            </div>
        );
    }

    function SuccessMessage() {
        return (
            <div className={[styles.success, styles.root].join(' ')}>
                <Grid
                    container
                    justifyContent="center"
                    alignItems="center"
                    style={{ textAlign: 'center' }}
                >
                    <Grid item xs={12}>
                        <DoneIcon className={styles.successIcon} />
                    </Grid>
                    <Grid item xs={12}>
                        <p className={styles.successTitle}>{t('commonLabels.success')}</p>
                    </Grid>
                    <Grid item xs={12} className={styles.successTextContainer}>
                        <p className={styles.successText}>{t('login.yourPasswordWasChanges')}</p>
                    </Grid>
                    <Grid item xs={12} className={styles.backToLoginContainer}>
                        <Link to={ROUTES.LOGIN} className={styles.backToLogin}>
                            <p>{t('login.returnToLoginPage')}</p>
                        </Link>
                    </Grid>
                </Grid>
            </div>
        );
    }

    return (
        <>
            <Header>
                <BackLink to={ROUTES.LOGIN}>
                    <LeftIcon size={IconSize.M} fill="currentColor" />
                    {t('commonLabels.back')}
                </BackLink>
            </Header>
            <Box
                sx={{ minWidth: 'min(360px, 92%)', alignSelf: 'center' }}
                className={styles.formContainer}
            >
                <p className={styles.label}>{t('login.resetYourPassword')}</p>
                {isSent ? SuccessMessage() : MailInput()}
            </Box>
            <NotificationPull newNotification={notificationError} />
        </>
    );
};

export default NewPassword;
