import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';

import { HeaderStyles } from 'common/styles/HeaderStyles';

const useResetPasswordStyles = makeStyles((theme) => ({
    btn: {
        marginTop: 30,
    },
    formContainer: {
        width: 371,
        justifyContent: 'center',
        marginTop: 98,
    },
    label: {
        ...FontPrimary(HeaderStyles.H4_18px, true, Colors.Black),
        textAlign: 'center',
    },
    text: {
        ...FontSecondary(HeaderStyles.H5_14px, false, theme.palette.neutral[7]),
        marginBottom: 40,
        textAlign: 'center',
    },
}));

export default useResetPasswordStyles;
