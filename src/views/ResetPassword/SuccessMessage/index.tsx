import { Done } from '@mui/icons-material';
import { Box, Typography } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { motion } from 'framer-motion';
import useSuccessMesssageStyles from './css';

type SuccessMessageProps = {
    mail: string;
};

const AnimatedBox = motion(Box);

export default function SuccessMessage({ mail }: SuccessMessageProps) {
    const styles = useSuccessMesssageStyles();
    const { t } = useAppTranslation();

    return (
        <AnimatedBox
            initial={{
                scale: 0.98,
                opacity: 0,
            }}
            transition={{
                duration: 0.3,
            }}
            animate={{
                scale: 1,
                opacity: 1,
            }}
            sx={{
                borderRadius: 2,
                padding: '30px 10px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 2,
                border: '2px solid var(--success)',
                backgroundColor: '#fdfffd',
            }}
        >
            <Box
                sx={{
                    borderRadius: 100,
                    border: `1px solid var(--success)`,
                    color: 'var(--success)',
                    width: 40,
                    height: 40,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            >
                <Done fontSize="large" />
            </Box>
            <Typography variant="h4Inter">{t('login.forgotPasswordSuccess3')}</Typography>
            <p className={styles.successText}>
                {t('login.forgotPasswordSuccess1')}
                <b>{mail}</b>
                {t('login.forgotPasswordSuccess2')}{' '}
            </p>
        </AnimatedBox>
    );
}
