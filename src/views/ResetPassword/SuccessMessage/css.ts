import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const useSuccessMesssageStyles = makeStyles((theme) => ({
    success: {
        width: '370px',
        maxHeight: '230px',
        padding: 40,
        borderRadius: 8,
        border: `solid 1px ${Colors.Success}`,
        backgroundColor: theme.palette.neutral[1],
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: '5vh',
    },
    successIconContainer: {
        display: 'flex',
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
    },
    successIcon: {
        color: Colors.Success,
    },
    successCircle: {
        width: '38px',
        height: '38px',
        border: `solid 1px ${Colors.Success}`,
        borderRadius: '50%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
    },
    successTitle: {
        ...FontPrimary(HeaderStyles.H4_18px, true, Colors.Black),
        textAlign: 'center',
    },
    successText: {
        ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Success),
        textAlign: 'center',
    },
}));

export default useSuccessMesssageStyles;
