import { Box, styled } from '@mui/material';
import axios from 'axios';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useState } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import { Link } from 'react-router-dom';
import { SessionLogic } from '../../business/SessionLogic';
import { LeftIcon } from '../../common/components/Icons/LeftIcon';
import { NotificationType } from '../../common/components/Notification/INotificationProps';
import { NotificationPull } from '../../common/components/NotificationPull';
import { NotificationData } from '../../common/components/NotificationPull/NotificationData';
import { emailRegex } from '../../common/constants/RegexValidation';
import { ROUTES } from '../../common/constants/RoutesDefinition';
import { IconSize } from '../../common/styles/IconSize';
import { MailInput } from './MailInput';
import SuccessMessage from './SuccessMessage';
import useResetPasswordStyles from './css';
export interface ResetPasswordProps {
    t: Function;
}

const BackLink = styled(Link)(({ theme }) => ({
    position: 'absolute',
    left: 20,
    top: 20,
    display: 'flex',
    alignItems: 'center',
    textDecoration: 'none',
    ...theme.typography.h5Inter,
    color: 'var(--cm3)',
    padding: '5px 10px 5px 0',
    borderRadius: 2,

    '&:hover': {
        color: 'var(--cm1)',
    },

    '&:active': {
        backgroundColor: 'var(--neutral3)',
    },
}));

const Header = styled('header')({
    width: '100%',
    minHeight: 80,
    position: 'relative',
});

const ResetPassword = () => {
    const [mail, setMail] = useState('');
    const [validMail, setValidMail] = useState(true);
    const [isSent, setIsSent] = useState(false);
    const [notificationError, setNotificationError] = useState<any>(null);
    const styles = useResetPasswordStyles();
    const { t } = useAppTranslation();
    const { executeRecaptcha } = useGoogleReCaptcha();
    const [loading, setLoading] = useState(false);

    const send = useCallback(
        async (captchaToken: string) => {
            if (!validMail) return;
            try {
                setLoading(true);
                await SessionLogic.requestPasswordReset(mail, captchaToken);
                setIsSent(true);
            } catch (error: unknown) {
                if (axios.isAxiosError(error)) {
                    if (error.response) {
                        if (error.response.status === 429) {
                            const notification = new NotificationData(
                                t('errorMessages.requestThrottledBody'),
                                t('errorMessages.requestThrottledTitle'),
                                NotificationType.danger
                            );
                            setNotificationError(notification);
                        } else if (error.response.status === 404) {
                            const notification = new NotificationData(
                                t('errorMessages.userDoesntExistBody'),
                                t('errorMessages.userDoesntExistTitle'),
                                NotificationType.danger
                            );
                            setNotificationError(notification);
                        } else if (error.response.status === 400) {
                            const notification = new NotificationData(
                                t('errorMessages.recaptchaErrorBody'),
                                t('errorMessages.recaptchaErrorTitle'),
                                NotificationType.danger
                            );
                            setNotificationError(notification);
                        }
                    }
                } else {
                    const notification = new NotificationData(
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        ((error as any) || {}).data?.message,
                        'Request error',
                        NotificationType.danger
                    );
                    setNotificationError(notification);
                }
            } finally {
                setLoading(false);
            }
        },
        [mail, validMail, t]
    );

    const resetPassword = useCallback(async () => {
        const validMail = emailRegex.test(mail);
        setValidMail(validMail);

        if (!validMail) {
            return;
        }

        if (!executeRecaptcha) {
            console.warn('Execute recaptcha not yet available');
            return;
        }

        const token = await executeRecaptcha('resetpassword');
        await send(token);
    }, [mail, executeRecaptcha, send]);

    return (
        <>
            <Header>
                <BackLink to={ROUTES.LOGIN}>
                    <LeftIcon size={IconSize.M} fill="currentColor" />
                    {t('commonLabels.back')}
                </BackLink>
            </Header>
            <Box
                sx={{ minWidth: 'min(360px, 92%)', alignSelf: 'center' }}
                className={styles.formContainer}
            >
                <p className={styles.label}>{t('login.forgotPasswordTitle')}</p>
                <p className={styles.text}>
                    {t('login.toResetPassword')} <b>{t('login.enterYouremailAddress')}</b>
                    {t('login.youMayNeedToCheckSpam')}
                </p>
                {isSent ? (
                    <SuccessMessage mail={mail} />
                ) : (
                    <>
                        <MailInput
                            value={mail}
                            onChange={(value) => {
                                setMail(value);
                                setValidMail(emailRegex.test(value));
                            }}
                            valid={validMail}
                        />
                        <Button
                            className={styles.btn}
                            label={t('login.resetPassword')}
                            cmosVariant={'filled'}
                            cmosSize={'large'}
                            showLoader={loading}
                            disabled={!validMail || loading || mail === ''}
                            blockMode
                            onClick={resetPassword}
                        />
                    </>
                )}
            </Box>
            <NotificationPull newNotification={notificationError} />
        </>
    );
};

export default ResetPassword;
