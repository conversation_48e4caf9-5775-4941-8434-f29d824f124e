import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

type MailInputProps = {
    value: string;
    onChange: (value: string) => void;
    valid: boolean;
};

export function MailInput({ onChange, value, valid }: MailInputProps) {
    const { t } = useAppTranslation();

    return (
        <TextFormField
            label={t('login.emailLabel')}
            placeholder={t('login.emailPlaceholderLabel')}
            type="text"
            onChange={(e) => onChange(e.target.value)}
            name="mail"
            size="medium"
            value={value}
            isInvalid={!valid}
        />
    );
}
