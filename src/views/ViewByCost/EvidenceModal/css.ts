import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        width: 834,
        height: 509,
        backgroundColor: theme.palette.neutral[1],
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 10,
        position: 'relative',
        [theme.breakpoints.down('md')]: {
            width: '100%',
            height: 235,
        },
    },
    header: {
        position: 'absolute',
        width: 834,
        display: 'flex',
        zIndex: 9,
        [theme.breakpoints.down('md')]: {
            width: '100%',
        },
    },
    bottomLine: {
        position: 'absolute',
        bottom: 88,
        width: '50%',
        display: 'flex',
        zIndex: 9,
        pointerEvents: 'none',
        [theme.breakpoints.down('md')]: {
            width: '100%',
        },
    },
    title: {
        ...theme.typography.h3Roboto,
        fontWeight: 'bold',
        color: '#fff',
        [theme.breakpoints.down('md')]: {
            ...theme.typography.h6Roboto,
            fontWeight: 'bold',
        },
    },
    container: {
        width: '100%',
        height: 437,
        display: 'flex',
        overflow: 'hidden',
        position: 'relative',
        zIndex: 1,
        [theme.breakpoints.down('md')]: {
            height: 203,
        },
    },
    slide: {
        flex: '0 0 100%',
        transition: 'transform 0.3s ease-in-out',
    },
    active: {
        transform: 'translateX(0)',
    },
    photo: {
        width: '100%',
        height: '100%',
        objectFit: 'contain',
    },
    video: {
        width: '100%',
        height: '100%',
    },
    navigation: {
        width: '100%',
        height: 72,
        [theme.breakpoints.down('md')]: {
            ...theme.typography.h9Roboto,
            height: 32,
        },
    },
    navigationText: {
        ...theme.typography.h4Roboto,
        fontWeight: 'bold',
        color: theme.palette.primary.main,
        [theme.breakpoints.down('md')]: {
            ...theme.typography.h9Roboto,
            fontWeight: 'bold',
        },
    },
}));

export default useStyles;
