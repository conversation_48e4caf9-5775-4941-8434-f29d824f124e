import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useQuery } from '@tanstack/react-query';
import ViewByCostApi, {
    FileType,
    FileType as FileTypeViewByCost,
    ViewByCostRepairImageGalleryDto,
} from 'api/viewByCost';
import { CircleIcon } from 'common/components/Icons/CircleIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { LeftIcon } from 'common/components/Icons/LeftIcon';
import { RightIcon } from 'common/components/Icons/RightIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { FileType as FileTypeValue } from 'datacontracts/InspectionForms/Media/FileType';
import { MediaDto } from 'datacontracts/InspectionForms/Media/MediaDto';
import { useMemo, useState } from 'react';
import AudioEvidence from 'views/OrderDetail/InspectionForms/Common/EvidenceModal/AudioEvidence';
import ImageEvidence from 'views/OrderDetail/InspectionForms/Common/EvidenceModal/ImageEvidence';
import VideoEvidence from 'views/OrderDetail/InspectionForms/Common/EvidenceModal/VideoEvidence';
import useStyles from './css';

type EvidenceModalProps = {
    isOpen: boolean;
    repairOrderId: string;
    repairId: string;
    repairMasterListId: string;
    onClose: () => void;
};

const EvidenceModal = ({
    isOpen,
    repairOrderId,
    repairId,
    repairMasterListId,
    onClose,
}: EvidenceModalProps) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const classes = useStyles();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [isVideo, setIsVideo] = useState<boolean>(false);

    const [evidenceGallery, setEvidenceGallery] = useState<ViewByCostRepairImageGalleryDto[]>([]);
    const [activeIndex, setActiveIndex] = useState<number>(0);

    const compareMediaItems = (
        a: ViewByCostRepairImageGalleryDto,
        b: ViewByCostRepairImageGalleryDto
    ) => {
        const getPriority = (item: ViewByCostRepairImageGalleryDto) => {
            switch (item.fileType) {
                case FileTypeViewByCost.Photo:
                    return 1;
                case FileTypeViewByCost.Video:
                    return 2;
                case FileTypeViewByCost.Audio:
                    return 3;
            }
        };
        const diff = getPriority(a) - getPriority(b);
        return diff === 0 ? a.orderNumber - b.orderNumber : diff;
    };

    useQuery(
        [
            'view-by-cost',
            'inspection-item',
            'evidences',
            repairOrderId,
            repairId,
            repairMasterListId,
        ],
        () => ViewByCostApi.getRepairEvidence(repairOrderId, repairId, repairMasterListId),
        {
            onSuccess: (evidences) => {
                if (evidences && evidences.galleryItems) {
                    setEvidenceGallery(evidences.galleryItems.sort(compareMediaItems));
                }
            },
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            cacheTime: 0,
        }
    );

    const nextSlide = () => {
        const evidence = evidenceGallery.find(
            (item, index) => index === (activeIndex + 1) % evidenceGallery.length
        );

        if (evidence && evidence.fileType === FileTypeViewByCost.Video) {
            setIsVideo(true);
        } else {
            setIsVideo(false);
        }

        setActiveIndex((prevIndex) => (prevIndex + 1) % evidenceGallery.length);
    };

    const prevSlide = () => {
        const evidence = evidenceGallery.find(
            (item, index) =>
                index === (activeIndex === 0 ? evidenceGallery.length - 1 : activeIndex - 1)
        );

        if (evidence && evidence.fileType === FileTypeViewByCost.Video) {
            setIsVideo(true);
        } else {
            setIsVideo(false);
        }

        setActiveIndex((prevIndex) =>
            prevIndex === 0 ? evidenceGallery.length - 1 : prevIndex - 1
        );
    };

    const handleClick = (value: number) => {
        const evidence = evidenceGallery.find((item, index) => index === value);

        if (evidence && evidence.fileType === FileTypeViewByCost.Video) {
            setIsVideo(true);
        } else {
            setIsVideo(false);
        }
        setActiveIndex(value);
    };

    const mediaDtoMap = (media: ViewByCostRepairImageGalleryDto): MediaDto => {
        let fileType: FileTypeValue;
        switch (media.fileType) {
            case FileTypeViewByCost.Video:
                fileType = FileTypeValue.Video;
                break;
            case FileTypeViewByCost.Audio:
                fileType = FileTypeValue.Audio;
                break;

            default:
                fileType = FileTypeValue.Photo;
                break;
        }

        return {
            sequenceNumber: media.orderNumber,
            url: media.url,
            thumbnailUrl: media.thumbnailUrl,
            fileType,
            isProcessed: media.isProcessed,
            overlay: media.overlay || undefined,
            uploadedFromGallery: false,
        };
    };

    const evidenceNamePhoto = useMemo(() => {
        if (evidenceGallery.length === 0) return '';

        if (
            evidenceGallery[activeIndex].fileType === FileType.Photo &&
            evidenceGallery[activeIndex].uploadedFromGallery
        ) {
            return t('inspectionForms.evidenceModal.uploadedFromGallery');
        }

        return '';
    }, [t, evidenceGallery, activeIndex]);

    const evidenceNameVideo = useMemo(() => {
        if (evidenceGallery.length === 0) return '';

        if (
            evidenceGallery[activeIndex].fileType === FileType.Video &&
            evidenceGallery[activeIndex].uploadedFromGallery
        ) {
            return t('inspectionForms.evidenceModal.uploadedFromGallery');
        }

        return '';
    }, [t, evidenceGallery, activeIndex]);

    return (
        <Modal open={isOpen} onClose={onClose} width={isMobile ? '100%' : undefined}>
            <Box className={classes.root}>
                <div className={classes.header}>
                    <div
                        style={{
                            paddingLeft: isMobile ? '15px' : '32px',
                            paddingTop: 22,
                            width: '100%',
                        }}
                    >
                        <span className={classes.title}>{evidenceNameVideo}</span>
                    </div>
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'end',
                            width: '100%',
                            padding: isMobile ? '8px 8px 0px 0px' : '16px 16px 0px 0px',
                        }}
                    >
                        <IconButton
                            onClick={() => {
                                onClose();
                            }}
                            style={{ padding: 0 }}
                            size="large"
                        >
                            <CloseIcon
                                fill={isVideo ? Colors.White : '#848484'}
                                size={isMobile ? 20 : IconSize.LL}
                            />
                        </IconButton>
                    </div>
                </div>
                <div className={classes.bottomLine}>
                    <div
                        style={{
                            paddingLeft: isMobile ? '15px' : '32px',
                        }}
                    >
                        <span className={classes.title}>{evidenceNamePhoto}</span>
                    </div>
                </div>
                <Box className={classes.container}>
                    {evidenceGallery.map((evidence, index) => (
                        <div
                            key={`${evidence.orderNumber}-${index}`}
                            className={classes.slide}
                            style={{
                                transform: `translateX(-${activeIndex * 100}%)`,
                                backgroundColor: '#000',
                            }}
                        >
                            {evidence.fileType === FileTypeViewByCost.Photo && (
                                <ImageEvidence
                                    image={mediaDtoMap(evidence)}
                                    showOverlay={index === activeIndex}
                                />
                            )}
                            {evidence.fileType === FileTypeViewByCost.Video && (
                                <VideoEvidence video={mediaDtoMap(evidence)} />
                            )}
                            {evidence.fileType === FileTypeViewByCost.Audio && (
                                <AudioEvidence audio={mediaDtoMap(evidence)} />
                            )}
                        </div>
                    ))}
                </Box>
                <Box className={classes.navigation}>
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            marginTop: isMobile ? 5 : 17,
                        }}
                    >
                        <IconButton onClick={prevSlide} style={{ padding: 0 }} size="large">
                            <LeftIcon fill={Colors.CM1} size={isMobile ? 20 : IconSize.LL} />
                        </IconButton>
                        <IconButton onClick={nextSlide} style={{ padding: 0 }} size="large">
                            <RightIcon fill={Colors.CM1} size={isMobile ? 20 : IconSize.LL} />
                        </IconButton>
                        <span className={classes.navigationText}>{`${
                            evidenceGallery.length ? activeIndex + 1 : 0
                        } ${t('viewByCost.modalEvidences.of')} ${evidenceGallery.length} ${t(
                            'viewByCost.modalEvidences.evidences'
                        )}`}</span>
                        <div style={{ display: 'flex', marginLeft: 10 }}>
                            {evidenceGallery.map((evidence, index) => (
                                <div
                                    key={index}
                                    style={{ cursor: 'pointer', paddingTop: 3 }}
                                    onClick={() => handleClick(index)}
                                >
                                    <CircleIcon
                                        fill={index === activeIndex ? Colors.CM1 : Colors.Neutral4}
                                        size={13}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                </Box>
            </Box>
        </Modal>
    );
};

export default EvidenceModal;
