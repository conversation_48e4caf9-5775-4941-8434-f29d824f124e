import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppSelector } from 'store';
import { generalInfoSelector } from 'store/slices/viewByCost/selectors';
import WorkshopBannerInfo from '../common/WorkshopBannerInfo';

import GeneralTabSection from './GeneralTabSection';

const GeneralTab = () => {
    const { t } = useAppTranslation();
    const { generalLocationInfo, vehicle, customer, order, serviceHours } =
        useAppSelector(generalInfoSelector);

    return (
        <>
            <Box component="div" sx={{ display: { xs: 'none', sm: 'block' } }}>
                <WorkshopBannerInfo />
                <Box sx={{ marginBottom: '25px' }} />
            </Box>

            <Grid
                container
                md={9}
                sx={{
                    display: 'grid',
                    gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
                    columnGap: { xs: 0, md: '24px' },
                    rowGap: '16px',
                }}
            >
                <Box>
                    <GeneralTabSection
                        title={t('viewByCost.generalInfo.orderInformation')}
                        fields={order}
                        sx={{
                            borderBottom: { xs: '2px solid #6A6E72', md: 'none' },
                            marginTop: { xs: '20px', md: '0' },
                            paddingBottom: { xs: '26px', md: '0px' },
                        }}
                    />
                    <GeneralTabSection
                        title={t('viewByCost.generalInfo.customerInfo')}
                        fields={customer}
                        sx={{
                            borderBottom: { xs: '2px solid #6A6E72', md: 'none' },
                            paddingBottom: { xs: '26px', md: '0px' },
                        }}
                    />
                </Box>
                <Box sx={{ marginBottom: { xs: '65px', md: '0px' } }}>
                    <GeneralTabSection
                        title={t('viewByCost.generalInfo.generalLocationInfo')}
                        fields={[...generalLocationInfo, ...serviceHours]}
                        sx={{
                            borderBottom: { xs: '2px solid #6A6E72', md: 'none' },
                            paddingBottom: { xs: '26px', md: '0px' },
                            marginTop: { xs: '-17px', md: '0px' },
                        }}
                    />

                    <GeneralTabSection
                        title={t('viewByCost.generalInfo.vehicleInfo')}
                        fields={vehicle}
                    />
                </Box>
            </Grid>
        </>
    );
};

export default GeneralTab;
