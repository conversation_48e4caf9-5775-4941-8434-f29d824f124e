import { styled, SxProps, Theme } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useAppSelector } from 'store';
import {
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';

import { CustomizableFieldValue, isPredefinedField, PREDEFINED_FIELDS } from 'api/fields';
import { FieldValue, GeneralInfo } from 'api/viewByCost';

import { Phases } from 'common/constants';
import { LanguagesEnum } from 'common/constants/Language';
import moment from 'moment/moment';
import { useMemo } from 'react';

type GeneralTabSectionProps = {
    title: string;
    fields: FieldValue[];
    sx?: SxProps<Theme>;
};

const fieldMapping: Record<keyof GeneralInfo, string> = {
    deliveryDate: PREDEFINED_FIELDS.ORDER_DELIVERY_DATE,
    note: PREDEFINED_FIELDS.ORDER_NOTE,
    towerNumber: PREDEFINED_FIELDS.ORDER_TOWER,
    serviceAdvisorName: PREDEFINED_FIELDS.ORDER_IN_CHARGE,
    type: PREDEFINED_FIELDS.ORDER_TYPE,
    phaseName: PREDEFINED_FIELDS.ORDER_PHASE,
    phaseId: PREDEFINED_FIELDS.ORDER_PHASE,
    assignedToDisplayName: PREDEFINED_FIELDS.ORDER_ASSIGNED_TO,
    technicians: PREDEFINED_FIELDS.ORDER_TECHNICIAN,

    make: PREDEFINED_FIELDS.VEHICLE_BRAND,
    model: PREDEFINED_FIELDS.VEHICLE_MODEL,
    year: PREDEFINED_FIELDS.VEHICLE_YEAR,
    mileage: PREDEFINED_FIELDS.VEHICLE_MILEAGE,
    vin: PREDEFINED_FIELDS.VEHICLE_VIN,
    plates: PREDEFINED_FIELDS.VEHICLE_PLATES,

    firstName: PREDEFINED_FIELDS.CUSTOMER_NAME,
    lastName: PREDEFINED_FIELDS.CUSTOMER_NAME,
    email: PREDEFINED_FIELDS.CUSTOMER_EMAIL,
    mobilePhone: PREDEFINED_FIELDS.CUSTOMER_MOBILE,
    landlinePhone: PREDEFINED_FIELDS.CUSTOMER_LANDLINE,
    businessName: PREDEFINED_FIELDS.CUSTOMER_BUSINESS_NAME,
    customerDocumentId: PREDEFINED_FIELDS.CUSTOMER_ID_DOCUMENT,
    paymentMethodType: PREDEFINED_FIELDS.CUSTOMER_PAYMENT_METHOD,

    accountName: PREDEFINED_FIELDS.LOCATION_NAME,
    address: PREDEFINED_FIELDS.LOCATION_ADDRESS,
    phoneNumber: PREDEFINED_FIELDS.LOCATION_PHONE_NUMBER,
    repairShopUrl: PREDEFINED_FIELDS.LOCATION_WEBSITE,
    taxIdentification: PREDEFINED_FIELDS.LOCATION_TAX_IDENTIFICATION,
    repairShopEmail: PREDEFINED_FIELDS.LOCATION_EMAIL_ADDRESS,
    repairShopBusinessName: PREDEFINED_FIELDS.LOCATION_LEGAL_NAME,

    serviceHoursMondayToFriday: PREDEFINED_FIELDS.LOCATION_MONDAY_TO_FRIDAY_HOURS,
    serviceHoursSaturday: PREDEFINED_FIELDS.LOCATION_SATURDAY_HOURS,
};

function parseSelect(jsonString: string): string {
    if (!jsonString) return '-';
    const parsed = JSON.parse(jsonString);
    const options = parsed.value.map((option: { name: string }) => option.name).join(', ');
    return `${options} ${parsed.text ?? ''}`;
}

function formatDate(dateStr: string): string {
    return dateStr ? moment(dateStr).format('DD/MM/YYYY') : '-';
}
function formatTime(timeStr: string): string {
    return timeStr ? moment(timeStr, 'HH:mm:ss').format('HH:mm') : '-';
}

const GeneralTabSection = ({ title, fields, sx }: GeneralTabSectionProps) => {
    const { t, i18n } = useAppTranslation();
    const shopValues = useAppSelector(repairShopInfoSelector);
    const predefinedFieldValues = useAppSelector(repairOrderDetailSelector);

    const combinedFieldValues = useMemo(
        () => ({ ...shopValues, ...predefinedFieldValues }),
        [shopValues, predefinedFieldValues]
    );

    const predefinedFieldValueHandlers: Partial<
        Record<keyof GeneralInfo, (values: GeneralInfo) => string>
    > = useMemo(
        () => ({
            firstName: (values) => {
                const firstName = values['firstName'] || '';
                const lastName = values['lastName'] || '';
                return `${firstName} ${lastName}`.trim() || '-';
            },
            deliveryDate: (values) => {
                const format =
                    i18n.language === LanguagesEnum.ENGLISH
                        ? t('dateFormats.longEn')
                        : t('dateFormats.longEs');
                const value = values['deliveryDate'];

                if (!value) return '-';

                const formattedDate = moment(value).format(format);
                return formattedDate || '-';
            },
            phaseName: (values) => {
                const phaseId = values['phaseId'];
                const phaseName = values['phaseName'];

                if (phaseId === Phases.Closed) {
                    return t('orderDetails.closedOrder');
                } else if (phaseId === Phases.NoPhase) {
                    return t('orderDetails.noPhase');
                } else {
                    return phaseName || '-';
                }
            },
            technicians: (values) => {
                const technicians = values['technicians'];
                return technicians.length > 0 ? technicians.join(', ') : '-';
            },
        }),
        [i18n.language, t]
    );

    return (
        <Grid item xs={12} md={11} sx={{ marginBottom: '24px', ...sx }}>
            <Title>{title}</Title>
            <Grid container spacing={1}>
                {fields.map((field) => {
                    let fieldValue = '-';
                    let fieldName = field.name;

                    const formattedFieldName = field.name.replaceAll('.', '_');
                    const translatedName = t(
                        `settings.customizableFields.predefined.${formattedFieldName}`
                    );

                    if (isPredefinedField(field.type)) {
                        fieldName =
                            translatedName !==
                            `settings.customizableFields.predefined.${formattedFieldName}`
                                ? translatedName
                                : field.name;

                        const mappedKey = Object.keys(fieldMapping).find(
                            (key) => fieldMapping[key as keyof typeof fieldMapping] === field.name
                        ) as keyof typeof fieldMapping;

                        if (mappedKey) {
                            const handler = predefinedFieldValueHandlers[mappedKey];
                            if (handler) {
                                fieldValue = handler(combinedFieldValues);
                            } else {
                                fieldValue = `${combinedFieldValues[mappedKey] || '-'}`;
                            }
                        }
                    } else {
                        switch (field.type) {
                            case 'Date':
                                fieldValue = formatDate(field.value);
                                break;
                            case 'Time':
                                fieldValue = formatTime(field.value);
                                break;
                            case 'MultiSelect':
                                fieldValue = parseSelect(field.value);
                                break;
                            case 'Select':
                                fieldValue = parseSelect(field.value);
                                break;
                            default:
                                const customFiled = field as CustomizableFieldValue;
                                fieldValue = customFiled.value || '-';
                        }
                    }
                    return (
                        <Grid item xs={12} key={field.id}>
                            <>
                                <Label>{fieldName}:</Label>
                                <Caption style={{ marginLeft: 5 }}>{fieldValue}</Caption>
                            </>
                        </Grid>
                    );
                })}
            </Grid>
        </Grid>
    );
};

export default GeneralTabSection;

const Title = styled('h2')(({ theme }) => ({
    ...theme.typography.h2Inter,
    color: theme.palette.neutral[8],
    margin: 0,
    marginBottom: '10px',
}));

const Label = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[8],
}));

const Caption = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[8],
}));
