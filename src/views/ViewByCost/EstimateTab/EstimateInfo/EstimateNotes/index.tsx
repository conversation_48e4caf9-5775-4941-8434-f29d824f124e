import { ViewByCostEstimateNoteDto } from 'api/viewByCost';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useStyles from './css';

type EstimateNotesProps = {
    notes: ViewByCostEstimateNoteDto[];
};

const EstimateNotes = ({ notes }: EstimateNotesProps) => {
    const { t } = useAppTranslation();

    const classes = useStyles();

    return (
        <div className={classes.container}>
            <h1 className={classes.title}>{t('viewByCost.estimate.notes')}</h1>
            {notes &&
                notes.map((note) => (
                    <span key={note.estimateNoteId} className={classes.notes}>
                        {note.text}
                    </span>
                ))}
        </div>
    );
};

export default EstimateNotes;
