import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const EstimateNotesStyles = makeStyles((theme) => ({
    container: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'start',
        marginTop: 11,
        [theme.breakpoints.up('sm')]: {
            marginBottom: 45,
        },
    },
    title: {
        ...FontPrimary(HeaderStyles.H12_16px, true, theme.palette.primary.main),
        lineHeight: 'normal',
    },
    notes: {
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
    },
}));

export default EstimateNotesStyles;
