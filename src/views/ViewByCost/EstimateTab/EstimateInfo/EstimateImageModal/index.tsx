import { Popover } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { useMemo } from 'react';
import useStyles from './css';

type EvidenceModalProps = {
    item: ViewByCostEstimateItemDto;
    isOpen: boolean;
    anchorEl: HTMLElement | null;
    isVideo?: boolean;
    isAudio?: boolean;
};

const EstimateImageModal = ({
    item,
    isOpen,
    anchorEl,
    isVideo = false,
    isAudio = false,
}: EvidenceModalProps) => {
    const classes = useStyles();

    const { largeImage } = item;

    const imageUrl = useMemo(() => largeImage || '', [largeImage]);
    return (
        <>
            {!isVideo && !isAudio && (
                <Popover
                    open={isOpen}
                    id="mouse-over-popover"
                    className={classes.popover}
                    anchorEl={anchorEl}
                    anchorOrigin={{
                        vertical: 'center',
                        horizontal: 'right',
                    }}
                    transformOrigin={{
                        vertical: 'bottom',
                        horizontal: 'left',
                    }}
                    disableRestoreFocus
                >
                    <img src={imageUrl} alt="" className={classes.image} />
                </Popover>
            )}
        </>
    );
};

export default EstimateImageModal;
