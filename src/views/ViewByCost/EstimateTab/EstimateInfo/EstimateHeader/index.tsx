import Grid from '@mui/material/Grid';
import { Checkbox } from 'common/components/Inputs';

import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { ChangeEvent, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { checkAllItems } from 'store/slices/viewByCost';
import { repairOrderDetailSelector } from 'store/slices/viewByCost/selectors';

const EstimateHeader = () => {
    const { t } = useAppTranslation();

    const dispatch = useAppDispatch();

    const [isAllChecked, setIsAllChecked] = useState(false);

    const { showEstimateToConsumer } = useAppSelector(repairOrderDetailSelector);

    const handleCheckAll = (e: ChangeEvent<HTMLInputElement>) => {
        setIsAllChecked(e.target.checked);
        dispatch(checkAllItems(e.target.checked));
    };

    return (
        <GridContainer container>
            {showEstimateToConsumer && (
                <GridAll item xs={'auto'}>
                    <StyledCheckbox
                        checked={isAllChecked}
                        onChange={handleCheckAll}
                        style={{ width: 40 }}
                    />
                    <AllTitle>{t('viewByCost.tableHeaders.all')}</AllTitle>
                </GridAll>
            )}
            <JobsAndInspectionItems item xs={'auto'}>
                {!showEstimateToConsumer && <DivSpacer />}
                <Title>{t('viewByCost.tableHeaders.jobsAndInspectionItems')}</Title>
            </JobsAndInspectionItems>
            {showEstimateToConsumer && (
                <GridEstimate item xs={'auto'}>
                    <Title>{t('viewByCost.tableHeaders.estimate')}</Title>
                </GridEstimate>
            )}
        </GridContainer>
    );
};

const GridAll = styled(Grid)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    [theme.breakpoints.up('sm')]: {
        width: 90,
        paddingRight: 24,
    },
}));

const GridEstimate = styled(Grid)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    paddingRight: 7,
    width: 'auto',
    marginLeft: 'auto',
    [theme.breakpoints.up('sm')]: {
        paddingRight: 70,
    },
}));

const Title = styled('span')(({ theme }) => ({
    color: Colors.Grey5,
    ...theme.typography.h6Roboto,
}));

const AllTitle = styled(Title)(({ theme }) => ({
    display: 'none',
    [theme.breakpoints.up('sm')]: {
        display: 'inline-block',
    },
}));

const JobsAndInspectionItems = styled(Grid)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    paddingRight: 10,
    flexGrow: 1,
}));

const DivSpacer = styled('div')(({ theme }) => ({
    [theme.breakpoints.up('sm')]: {
        width: 32,
    },
}));

const StyledCheckbox = styled(Checkbox)({
    padding: 1,
});

const GridContainer = styled(Grid)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    width: '90vw',
    border: '1px solid rgba(201, 205, 211, 0.5)',
    borderRadius: '12px 12px 0 0',
    overflow: 'hidden',
    height: 48,
    backgroundColor: '#F6F6F6',
    [theme.breakpoints.up('sm')]: {
        padding: '0px 21px',
        width: 'initial',
        maxWidth: 680,
    },
}));

export default EstimateHeader;
