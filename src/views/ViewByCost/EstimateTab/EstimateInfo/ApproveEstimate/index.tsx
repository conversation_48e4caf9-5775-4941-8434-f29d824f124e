import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { Button } from 'common/components/Button';
import { ApproveStatusEnum, Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { usePostHog } from 'common/hooks/usePostHog';
import { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectEnableOnlinePayment } from 'store/slices/globalSettingsSlice';
import {
    estimateItemsSelector,
    paymentUrlSelector,
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import image from '../../../../../assets/images/to_do_list.png';
import ApproveModal from '../ApproveModal';
import EstimateDetail from './EstimateDetail/EstimateDetail';

const ApproveEstimate = () => {
    const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);
    const estimateItems = useAppSelector(estimateItemsSelector);
    const { repairOrderNumber, make, model, year, phaseId, discount, discountType } =
        useAppSelector(repairOrderDetailSelector);
    const { taxPercentage } = useAppSelector(repairShopInfoSelector);
    const enableOnlinePayments = useAppSelector(selectEnableOnlinePayment);
    const paymentUrl = useAppSelector(paymentUrlSelector);
    const { orderId } = useParams<{ orderId: string }>();
    const { capturePosthogEvent } = usePostHog();

    const { t } = useAppTranslation();

    const orderIsClosed = useMemo(() => phaseId === Phases.Closed, [phaseId]);

    const canApprove = useMemo(
        () =>
            estimateItems.some(
                (item: ViewByCostEstimateItemDto) =>
                    item.isChecked &&
                    item.approveStatus !== ApproveStatusEnum.ApprovedByConsumer &&
                    item.approveStatus !== ApproveStatusEnum.ApprovedByTeamMember
            ),
        [estimateItems]
    );

    const wasApproved =
        estimateItems.some(
            (item: ViewByCostEstimateItemDto) =>
                item.approveStatus === ApproveStatusEnum.ApprovedByConsumer ||
                item.approveStatus === ApproveStatusEnum.ApprovedByTeamMember ||
                item.isPayed
        ) && enableOnlinePayments;

    const approveEstimateHandler = () => {
        setIsApproveModalOpen(true);
    };

    const checkedItems = useMemo(
        () => estimateItems.filter(({ isChecked }) => isChecked),
        [estimateItems]
    );
    const subtotal = useMemo(
        () =>
            checkedItems.reduce((sum, item) => {
                return sum + (item.totalCost || 0);
            }, 0),
        [checkedItems]
    );

    const subtotalDiscounted = useMemo(() => {
        if (discount && discount > 0 && subtotal > 0) {
            if (discountType === 'Currency') {
                return subtotal - discount;
            } else if (discountType === 'Percentage') {
                return subtotal * ((100 - discount) / 100);
            }
        }
        return subtotal;
    }, [discount, discountType, subtotal]);

    const taxes = subtotalDiscounted * (taxPercentage || 0);
    const total = subtotalDiscounted + taxes;

    return (
        <>
            <DivContainer className={canApprove ? 'canApprove' : undefined}>
                <Grid
                    container
                    justifyContent="center"
                    alignContent="center"
                    direction="column"
                    alignItems="center"
                >
                    {wasApproved || canApprove ? (
                        <EstimateDetail />
                    ) : (
                        <>
                            <Image src={image} />
                            <PCaption>
                                {t('viewByCost.estimate.text1')}
                                <br />
                                {t('viewByCost.estimate.text2')}
                            </PCaption>
                        </>
                    )}
                    <Button
                        label={t(
                            wasApproved && !canApprove
                                ? 'viewByCost.estimate.approved'
                                : 'viewByCost.estimate.approve'
                        )}
                        cmosVariant={'filled'}
                        cmosSize={'large'}
                        disabled={!canApprove || orderIsClosed}
                        blockMode
                        onClick={approveEstimateHandler}
                    />
                    {enableOnlinePayments &&
                        paymentUrl &&
                        paymentUrl?.some((x) => x.orderId === orderId) && (
                            <Button
                                label={t('viewByCost.estimate.pay')}
                                cmosVariant={'stroke'}
                                cmosSize={'large'}
                                blockMode
                                sx={{ marginTop: '16px' }}
                                disabled={canApprove}
                                onClick={() => {
                                    const url = paymentUrl.find((x) => x.orderId === orderId)?.url;
                                    capturePosthogEvent('pay_from_estimate_tab_clicked', {
                                        orderNumber: repairOrderNumber,
                                        totalApproved: total,
                                        vehicleBrand: make,
                                        vehicleModel: model,
                                        vehicleYear: year,
                                    });
                                    if (url) {
                                        window.open(url, '_blank');
                                    }
                                }}
                            />
                        )}
                </Grid>
            </DivContainer>
            {isApproveModalOpen && (
                <ApproveModal
                    isOpen={isApproveModalOpen}
                    onClose={() => {
                        setIsApproveModalOpen(false);
                    }}
                />
            )}
        </>
    );
};

const DivContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '90vw',
    borderRadius: 12,
    padding: '11px 30px',
    marginTop: 24,

    [theme.breakpoints.down('md')]: {
        backgroundColor: theme.palette.neutral[1],
        maxWidth: '680px',
    },

    [theme.breakpoints.up('md')]: {
        width: 328,
        marginTop: 104,
        padding: 0,
        display: 'inline-block',
        position: 'sticky',
        top: 'min(324px, calc((100vh - 236px) / 2))',
    },

    '&.canApprove': {
        backgroundColor: theme.palette.neutral[1],
        [theme.breakpoints.up('md')]: {
            backgroundColor: 'transparent',
        },
    },
}));

const Image = styled('img')(({ theme }) => ({
    height: '142px',
    width: '150px',
    display: 'none',
    [theme.breakpoints.up('sm')]: {
        display: 'inline-block',
    },
}));

const PCaption = styled('p')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    textAlign: 'center',
}));

export default ApproveEstimate;
