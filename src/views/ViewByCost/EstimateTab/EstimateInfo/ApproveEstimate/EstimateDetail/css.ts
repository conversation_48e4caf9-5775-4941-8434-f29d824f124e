import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const ApproveEstimateStyles = makeStyles((theme: Theme) => ({
    container: {
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 11,
        marginBottom: 24,
        [theme.breakpoints.up('sm')]: {
            marginTop: 30,
        },
    },
    title: {
        ...FontSecondary(HeaderStyles.H5_14px, true, theme.palette.primary.main),
    },
    estimateContainer: {
        display: 'flex',
        flexDirection: 'column',
        gap: 16,
    },
    estimateRow: {
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
    },
    text: {
        ...FontSecondary(HeaderStyles.H6_12px, false),
    },
    estimate: {
        ...FontPrimary(HeaderStyles.H5_14px, true),
    },
}));

export default ApproveEstimateStyles;
