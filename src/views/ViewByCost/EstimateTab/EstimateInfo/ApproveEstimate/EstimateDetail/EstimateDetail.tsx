import { Grid } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    estimateItemsSelector,
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import { formatNumber } from 'views/ViewByCost/common/functions';
import useStyles from './css';

const EstimateDetail = () => {
    const { discount, discountType } = useAppSelector(repairOrderDetailSelector);
    const estimateItems = useAppSelector(estimateItemsSelector);
    const { taxPercentage } = useAppSelector(repairShopInfoSelector);
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = useMemo(
        () => (repairShopSettings?.features.enableRemoveDecimals ? false : true),
        [repairShopSettings]
    );
    const { t } = useAppTranslation();

    const classes = useStyles();

    const checkedItems = useMemo(
        () => estimateItems.filter(({ isChecked }) => isChecked),
        [estimateItems]
    );
    const subtotal = useMemo(
        () =>
            checkedItems.reduce((sum, item) => {
                return sum + (item.totalCost || 0);
            }, 0),
        [checkedItems]
    );

    const subtotalDiscounted = useMemo(() => {
        if (discount && discount > 0 && subtotal > 0) {
            if (discountType === 'Currency') {
                return subtotal - discount;
            } else if (discountType === 'Percentage') {
                return subtotal * ((100 - discount) / 100);
            }
        }
        return subtotal;
    }, [checkedItems]);

    const taxes = subtotalDiscounted * (taxPercentage || 0);
    const total = subtotalDiscounted + taxes;

    return (
        <>
            <div className={classes.container}>
                <h2 className={classes.title}>{t('viewByCost.estimate.approveTitle')}</h2>
                <Grid container className={classes.estimateContainer}>
                    {discount && discount > 0 ? (
                        <Grid item className={classes.estimateRow}>
                            <span className={classes.text} style={{ color: Colors.Grey5 }}>
                                {t('viewByCost.estimate.discount')}
                            </span>
                            <span className={classes.estimate} style={{ color: Colors.CM3 }}>
                                {discountType === 'Percentage'
                                    ? (discount || 0) + '%'
                                    : formatNumber(
                                          internationalization,
                                          discount || 0,
                                          requireDecimals
                                      )}
                            </span>
                        </Grid>
                    ) : (
                        <></>
                    )}
                    <Grid item className={classes.estimateRow}>
                        <span className={classes.text} style={{ color: Colors.Grey5 }}>
                            {t('viewByCost.estimate.subtotal')}
                        </span>
                        <span className={classes.estimate} style={{ color: Colors.CM3 }}>
                            {formatNumber(
                                internationalization,
                                subtotalDiscounted || 0,
                                requireDecimals
                            )}
                        </span>
                    </Grid>
                    <Grid item className={classes.estimateRow}>
                        <span className={classes.text} style={{ color: Colors.Grey5 }}>
                            {t('viewByCost.estimate.taxes')}
                        </span>
                        <span className={classes.estimate} style={{ color: Colors.CM3 }}>
                            {formatNumber(internationalization, taxes || 0, requireDecimals)}
                        </span>
                    </Grid>
                    <Grid item className={classes.estimateRow}>
                        <span className={classes.text} style={{ color: Colors.Success }}>
                            {t('viewByCost.estimate.total')}
                        </span>
                        <span className={classes.estimate} style={{ color: Colors.Success }}>
                            {formatNumber(internationalization, total || 0, requireDecimals)}
                        </span>
                    </Grid>
                </Grid>
            </div>
        </>
    );
};

export default EstimateDetail;
