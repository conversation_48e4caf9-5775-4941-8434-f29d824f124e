import { styled } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { EstimatePriorityEnum } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useMemo } from 'react';
import EstimateItemsAccordion from './EstimateItemsAccordion';

type EstimateItemTableProps = {
    estimateItems: ViewByCostEstimateItemDto[];
};

const EstimateItemTable = ({ estimateItems }: EstimateItemTableProps) => {
    const { t } = useAppTranslation();

    const urgentItems = useMemo(
        () => estimateItems?.filter(({ priority }) => priority === EstimatePriorityEnum.Urgent),
        [estimateItems]
    );
    const mediumItems = useMemo(
        () => estimateItems?.filter(({ priority }) => priority === EstimatePriorityEnum.Medium),
        [estimateItems]
    );
    const lowItems = useMemo(
        () => estimateItems?.filter(({ priority }) => priority === EstimatePriorityEnum.Low),
        [estimateItems]
    );

    return (
        <DivContainer id={'view-by-cost-item-table'}>
            <EstimateItemsAccordion
                items={urgentItems}
                label={t('viewByCost.common.urgent')}
                color={Colors.Error}
                defaultExpanded
            />
            <EstimateItemsAccordion
                items={mediumItems}
                label={t('viewByCost.common.medium')}
                color={Colors.Warning}
            />
            <EstimateItemsAccordion
                items={lowItems}
                label={t('viewByCost.common.ok')}
                color={Colors.Success}
            />
        </DivContainer>
    );
};

const DivContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 16,
});

export default EstimateItemTable;
