import { AccordionDetails, Box, Grid, styled } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { Checkbox } from 'common/components/Inputs/Checkbox';
import { ApproveStatusEnum } from 'common/constants';
import { ChangeEvent, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { selectEnableOnlinePayment, selectSettings } from 'store/slices/globalSettingsSlice';
import { checkItem } from 'store/slices/viewByCost';
import {
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import OpenCommentsButton from 'views/OrderDetail/InspectionForms/SpreadsheetView/OpenCommentsButton';
import EvidenceModal from 'views/ViewByCost/EvidenceModal';
import { formatNumber } from 'views/ViewByCost/common/functions';
import EstimateItemCommentsModal from './CommentsModal';
import EstimateItemDescription from './EstimateItemDescription';

type EstimateItemsAccordionDetailsProps = {
    items: ViewByCostEstimateItemDto[];
};

const EstimateItemsAccordionDetails = ({ items }: EstimateItemsAccordionDetailsProps) => {
    const [estimateItem, setEstimateItem] = useState<ViewByCostEstimateItemDto>();
    const [isCommentsModalOpen, setIsCommentsModalOpen] = useState<boolean>(false);
    const [repairName, setRepairName] = useState<string>();
    const [comments, setComments] = useState<string>();

    const { orderId } = useParams<{ orderId: string }>();
    const dispatch = useAppDispatch();

    const enableOnlinePayments = useAppSelector(selectEnableOnlinePayment);
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = useMemo(
        () => (repairShopSettings?.features.enableRemoveDecimals ? false : true),
        [repairShopSettings]
    );

    const { showEstimateToConsumer } = useAppSelector(repairOrderDetailSelector);
    const { approveOnlyEstimated, taxPercentage } = useAppSelector(repairShopInfoSelector);

    const handleCheckItem = (e: ChangeEvent<HTMLInputElement>, repairName: string) => {
        dispatch(checkItem({ isChecked: e.target.checked, repairName }));
    };

    return (
        <StyledAccordionDetails>
            <Grid container sx={{ height: 'auto', padding: 0 }}>
                {items.map((item, index) => {
                    const showCheckbox =
                        showEstimateToConsumer && !(approveOnlyEstimated && item.totalCost === 0);

                    return (
                        <AccordionRow item key={`${item.repairName}-${index}`}>
                            <GridCheckboxContainer item xs={'auto'}>
                                <Checkbox
                                    sx={showCheckbox ? undefined : { visibility: 'hidden' }}
                                    checked={item.isChecked}
                                    onChange={(e) => {
                                        if (!showCheckbox) return;
                                        handleCheckItem(e, item.repairName);
                                    }}
                                    style={{ padding: 1, width: 40 }}
                                    disabled={
                                        item.approveStatus ===
                                            ApproveStatusEnum.ApprovedByConsumer ||
                                        item.approveStatus ===
                                            ApproveStatusEnum.ApprovedByTeamMember ||
                                        (enableOnlinePayments && item.isPayed)
                                    }
                                />
                            </GridCheckboxContainer>

                            <EstimateItemDescription
                                orderId={orderId ?? ''}
                                estimateItem={item}
                                onImageClick={() => setEstimateItem(item)}
                            />
                            <EstimateAndComments
                                className={item.repairNotes ? 'withComments' : undefined}
                            >
                                {item.repairNotes && (
                                    <Box component="div">
                                        <OpenCommentsButton
                                            onClick={() => {
                                                setIsCommentsModalOpen(true);
                                                setRepairName(item.repairName);
                                                setComments(item.repairNotes);
                                            }}
                                            noPadding
                                        />
                                    </Box>
                                )}
                                {showEstimateToConsumer && (
                                    <GridEstimate item xs={'auto'}>
                                        <Text>
                                            {formatNumber(
                                                internationalization,
                                                item.totalCost
                                                    ? item.totalCost +
                                                          item.totalCost * (taxPercentage || 0)
                                                    : 0,
                                                requireDecimals
                                            )}
                                        </Text>
                                    </GridEstimate>
                                )}
                            </EstimateAndComments>
                        </AccordionRow>
                    );
                })}
            </Grid>
            {estimateItem && estimateItem.repairId && estimateItem.repairMasterListId && (
                <EvidenceModal
                    isOpen={!!estimateItem}
                    repairOrderId={orderId ?? ''}
                    repairId={estimateItem.repairId}
                    repairMasterListId={estimateItem.repairMasterListId}
                    onClose={() => {
                        setEstimateItem(undefined);
                    }}
                />
            )}
            {isCommentsModalOpen && repairName && comments && (
                <EstimateItemCommentsModal
                    isOpen={isCommentsModalOpen}
                    repairName={repairName}
                    comments={comments}
                    onClose={() => {
                        setIsCommentsModalOpen(false);
                    }}
                />
            )}
        </StyledAccordionDetails>
    );
};

export default EstimateItemsAccordionDetails;

const StyledAccordionDetails = styled(AccordionDetails)(({ theme }) => ({
    padding: 0,
    border: `1px solid ${theme.palette.neutral[4]}`,
    borderTop: 'none',
}));

const AccordionRow = styled(Grid)(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
    minHeight: 56,
    display: 'flex',
    alignItems: 'center',
    paddingTop: 5,
    paddingBottom: 5,
    width: '100%',
    borderTop: `1px solid ${theme.palette.neutral[4]}`,
}));

const GridCheckboxContainer = styled(Grid)(({ theme }) => ({
    paddingLeft: 0,
    placeContent: 'center',
    [theme.breakpoints.up('sm')]: {
        paddingLeft: 21,
    },
}));

const Text = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: '2',
    WebkitBoxOrient: 'vertical',
}));

const GridEstimate = styled(Grid)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    paddingRight: 4,
    [theme.breakpoints.up('sm')]: {
        paddingRight: 20,
    },
}));

const EstimateAndComments = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    marginTop: -4,
    [theme.breakpoints.up('sm')]: {
        flexDirection: 'row-reverse',
        justifyContent: 'space-between',
        gap: 0,
        marginRight: 68,
        '&.withComments': {
            [theme.breakpoints.up('sm')]: {
                marginRight: 44,
            },
        },
    },
}));
