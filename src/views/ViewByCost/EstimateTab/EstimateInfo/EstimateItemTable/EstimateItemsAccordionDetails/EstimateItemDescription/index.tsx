import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import ItemImage from '../ItemImage';

type EstimateItemDescriptionProps = {
    orderId: string;
    estimateItem: ViewByCostEstimateItemDto;
    onImageClick?: (value: ViewByCostEstimateItemDto) => void;
};

const InspectionItem = styled(Grid)(({ theme }) => ({
    position: 'relative',
    display: 'flex',
    gap: 12,
    minHeight: 26,
    flexGrow: 1,
    [theme.breakpoints.up('sm')]: {
        paddingLeft: 50,
    },
}));

const Caption = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 700,
}));

const Text = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 400,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: '2',
    WebkitBoxOrient: 'vertical',
}));

const EstimateItemDescription = ({
    orderId,
    estimateItem,
    onImageClick,
}: EstimateItemDescriptionProps) => {
    const { t } = useAppTranslation();
    return (
        <InspectionItem
            item
            sx={{ cursor: estimateItem.itemImage ? 'pointer' : 'default' }}
            onClick={() => {
                estimateItem.itemImage && onImageClick && onImageClick(estimateItem);
            }}
        >
            <ItemImage item={estimateItem} orderId={orderId} />
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'start',
                }}
            >
                <Text>{estimateItem.repairName}</Text>
                {estimateItem.isAvailable !== null && (
                    <div style={{ display: 'flex', marginTop: 5 }}>
                        <Caption>{t('viewByCost.estimate.available')}:&nbsp;</Caption>
                        <Text>
                            {t(`viewByCost.estimate.${estimateItem.isAvailable ? 'yes' : 'no'}`)}
                        </Text>
                    </div>
                )}
                {estimateItem.estimatesNotes && (
                    <>
                        <div style={{ display: 'flex', marginTop: 5 }}>
                            <Caption>
                                {t('viewByCost.estimate.inventoryComments')}:
                                <Text style={{ marginLeft: 5 }}>{estimateItem.estimatesNotes}</Text>
                            </Caption>
                        </div>
                        {Boolean(estimateItem.subEstimates?.length) && (
                            <div
                                style={{
                                    borderTop: `1px solid #CAC4D0`,
                                    width: '90%',
                                    marginTop: 8,
                                    marginBottom: 8,
                                }}
                            />
                        )}
                    </>
                )}
                {Boolean(estimateItem.subEstimates?.length) &&
                    estimateItem.subEstimates?.map((item, index) => (
                        <div key={`${item.estimateName}-${index}`} style={{ marginBottom: 9 }}>
                            <Text>{item.estimateName}</Text>
                            {item.isAvailable !== null && (
                                <div style={{ display: 'flex', marginTop: 5 }}>
                                    <Caption>{t('viewByCost.estimate.available')}:&nbsp;</Caption>
                                    <Text>
                                        {t(
                                            `viewByCost.estimate.${item.isAvailable ? 'yes' : 'no'}`
                                        )}
                                    </Text>
                                </div>
                            )}
                            {item.estimateNotes && (
                                <>
                                    <div style={{ display: 'flex', marginTop: 5 }}>
                                        <Caption>
                                            {t('viewByCost.estimate.inventoryComments')}:
                                            <Text style={{ marginLeft: 5 }}>
                                                {item.estimateNotes}
                                            </Text>
                                        </Caption>
                                    </div>
                                    {(estimateItem.subEstimates?.length || 0) > index + 1 && (
                                        <div
                                            style={{
                                                borderTop: `1px solid #CAC4D0`,
                                                width: '100%',
                                                marginTop: 8,
                                            }}
                                        />
                                    )}
                                </>
                            )}
                        </div>
                    ))}
            </div>
        </InspectionItem>
    );
};

export default EstimateItemDescription;
