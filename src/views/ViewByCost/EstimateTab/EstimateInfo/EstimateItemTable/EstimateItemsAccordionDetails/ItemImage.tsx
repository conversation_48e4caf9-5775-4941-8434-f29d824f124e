import { Box, styled } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { MicIcon } from 'common/components/Icons/MicIcon';
import React, { useMemo, useState } from 'react';
import default_logo from '../../../../../../assets/images/default-logo.png';
import { VideoIcon } from '../../../../../../common/components/Icons/VideoIcon';
import { IconSize } from '../../../../../../common/styles/IconSize';
import EstimateImageModal from '../../EstimateImageModal';
import DefaultNaIcon from './DefaultNaIcon';

type ItemImageProps = {
    item: ViewByCostEstimateItemDto;
    orderId: string;
};

const VideoThumbnail = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 36,
    flexShrink: 0,
    cursor: 'pointer',
    borderRadius: 5,
    border: `1px solid ${theme.palette.neutral[4]}`,
}));

const Image = styled('img')(({ theme }) => ({
    width: 48,
    height: 36,
    margin: 'auto 0px',
    flexShrink: 0,
    borderRadius: 5,
    cursor: 'pointer',
}));

const ItemImage = ({ item, orderId }: ItemImageProps) => {
    const [estimateItemHover, setEstimateItemHover] = useState<ViewByCostEstimateItemDto | null>(
        null
    );
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

    const showEstimateImageModal = (
        item: ViewByCostEstimateItemDto,
        event: React.MouseEvent<HTMLElement, MouseEvent>
    ) => {
        if (!item.itemImage) return;
        setAnchorEl(event.currentTarget);
        setEstimateItemHover(item);
    };

    const isVideo = useMemo(() => (item.itemImage === 'Video' ? true : false), [item.itemImage]);
    const isAudio = useMemo(() => (item.itemImage === 'Audio' ? true : false), [item.itemImage]);

    const itemOrVideoThumbnail = () => {
        if (isVideo)
            return (
                <>
                    <VideoThumbnail
                        component="div"
                        onMouseOver={(e: React.MouseEvent<HTMLDivElement>) =>
                            showEstimateImageModal(item, e)
                        }
                        onMouseOut={() => setEstimateItemHover(null)}
                    >
                        <VideoIcon size={IconSize.L} />
                    </VideoThumbnail>
                    <EstimateImageModal
                        item={item}
                        isOpen={estimateItemHover?.repairId === item.repairId}
                        anchorEl={anchorEl}
                        isVideo
                    />
                </>
            );
        if (isAudio)
            return (
                <>
                    <VideoThumbnail
                        component="div"
                        onMouseOver={(e: React.MouseEvent<HTMLDivElement>) =>
                            showEstimateImageModal(item, e)
                        }
                        onMouseOut={() => setEstimateItemHover(null)}
                    >
                        <MicIcon size={IconSize.L} />
                    </VideoThumbnail>
                    <EstimateImageModal
                        item={item}
                        isOpen={estimateItemHover?.repairId === item.repairId}
                        anchorEl={anchorEl}
                        isAudio
                    />
                </>
            );
        return (
            <>
                <Image
                    src={item.itemImage || default_logo}
                    alt="imagen"
                    onMouseOver={(e: React.MouseEvent<HTMLDivElement>) =>
                        showEstimateImageModal(item, e)
                    }
                    onMouseOut={() => setEstimateItemHover(null)}
                />
                <EstimateImageModal
                    item={item}
                    isOpen={estimateItemHover?.repairId === item.repairId}
                    anchorEl={anchorEl}
                />
            </>
        );
    };

    return item.itemImage ? itemOrVideoThumbnail() : <DefaultNaIcon />;
};

export default ItemImage;
