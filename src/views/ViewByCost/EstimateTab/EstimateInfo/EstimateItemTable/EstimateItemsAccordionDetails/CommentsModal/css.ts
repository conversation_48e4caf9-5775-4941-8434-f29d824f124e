import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
const useStyles = makeStyles((theme: Theme) => ({
    root: {
        width: 483,
        minHeight: 167,
        backgroundColor: theme.palette.neutral[1],
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        borderRadius: 10,
        [theme.breakpoints.down('md')]: {
            width: 376,
            minHeight: 182,
        },
    },
    header: {
        width: 463,
        display: 'flex',
        justifyContent: 'end',
        zIndex: 9,
        paddingTop: 13,
        paddingRight: 30,
        [theme.breakpoints.down('md')]: {
            width: 346,
        },
    },
    row: {
        width: 403,
        paddingLeft: 40,
        paddingRight: 40,
        [theme.breakpoints.down('md')]: {
            width: 296,
        },
    },
    title: {
        ...theme.typography.h4Inter,
        color: theme.palette.neutral[8],
        fontWeight: 'bold',
    },
    subTitle: {
        ...theme.typography.h5Roboto,
        color: theme.palette.neutral[7],
        fontWeight: 'bold',
    },
    notesText: {
        ...theme.typography.h6Roboto,
        color: theme.palette.neutral[7],
        fontWeight: 'normal',
    },
}));

export default useStyles;
