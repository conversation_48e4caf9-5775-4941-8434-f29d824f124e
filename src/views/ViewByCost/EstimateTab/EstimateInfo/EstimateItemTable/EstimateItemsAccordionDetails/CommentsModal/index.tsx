import Box from '@mui/material/Box';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useStyles from './css';

type EstimateItemCommentsModalProps = {
    isOpen: boolean;
    repairName: string;
    comments: string;
    onClose: () => void;
};

const EstimateItemCommentsModal = ({
    isOpen,
    repairName,
    comments,
    onClose,
}: EstimateItemCommentsModalProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    return (
        <Modal open={isOpen} onClose={onClose}>
            <Box component="div" className={classes.root}>
                <div className={classes.header}>
                    <Button
                        onClick={onClose}
                        Icon={CloseIcon}
                        color="#848484"
                        cmosVariant={'typography'}
                    />
                </div>
                <div style={{ width: '100%', paddingBottom: 10 }}>
                    <div className={classes.row}>
                        <span className={classes.title}>{repairName}</span>
                    </div>
                    <div className={classes.row} style={{ marginTop: 23 }}>
                        <span className={classes.subTitle}>
                            {t('viewByCost.modalComments.notes')}:
                        </span>
                    </div>
                    <div className={classes.row} style={{ marginTop: 9 }}>
                        <div className={classes.notesText} style={{ textAlign: 'justify' }}>
                            {comments}
                        </div>
                    </div>
                </div>
            </Box>
        </Modal>
    );
};

export default EstimateItemCommentsModal;
