import { styled } from '@mui/material';
import { HideIcon } from 'common/components/Icons/HideIcon';

const DefaultIcon = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 36,
    margin: 'auto 0px',
    flexShrink: 0,
    borderRadius: 5,
    cursor: 'default',
    backgroundColor: theme.palette.neutral[4],
}));

const DefaultNaIcon = () => {
    return (
        <DefaultIcon>
            <HideIcon fill="#FFF" />
        </DefaultIcon>
    );
};

export default DefaultNaIcon;
