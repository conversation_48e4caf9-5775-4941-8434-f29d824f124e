import { Accordion, AccordionSummary, styled } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import EstimateItemsAccordionDetails from './EstimateItemsAccordionDetails/EstimateItemsAccordionDetails';

type EstimateItemsAccordionProps = {
    items: ViewByCostEstimateItemDto[];
    label: string;
    color: Colors;
    defaultExpanded?: boolean;
};

const EstimateItemsAccordion = ({
    items,
    label,
    color,
    defaultExpanded,
}: EstimateItemsAccordionProps) => {
    return (
        <StyledAccordion defaultExpanded={defaultExpanded}>
            <StyledAccordionSummary
                expandIcon={<DownIcon fill={Colors.White} size={IconSize.M} />}
                aria-controls="panel1a-content"
                id="panel1a-header"
                style={{ backgroundColor: color }}
                disabled={items.length === 0}
            >
                <SpanAccordionSummaryTitle>{`${label} (${items.length})`}</SpanAccordionSummaryTitle>
            </StyledAccordionSummary>
            <EstimateItemsAccordionDetails items={items} />
        </StyledAccordion>
    );
};

const SpanAccordionSummaryTitle = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[1],
    ...theme.typography.h6Roboto,
}));

const StyledAccordion = styled(Accordion)(({ theme }) => ({
    overflow: 'hidden',
    borderRadius: '0 0 5px 5px !important',
    width: '90vw !important',
    [theme.breakpoints.up('sm')]: {
        maxWidth: '680px',
        width: 'initial !important',
    },
}));

const StyledAccordionSummary = styled(AccordionSummary)({
    margin: 0,
    height: 48,
    padding: '0px 42px 0px 21px',
    '&.Mui-expanded': {
        minHeight: '0',
    },
    '& .MuiAccordionSummary-content.Mui-expanded': {
        margin: 0,
    },
});

export default EstimateItemsAccordion;
