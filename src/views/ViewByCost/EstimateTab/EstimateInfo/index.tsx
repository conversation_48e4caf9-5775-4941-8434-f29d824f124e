import { Box, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { PriorityTextValues } from 'api/settings/OrdersSettings';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { ApproveStatusEnum, Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ReactNode, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import {
    estimateItemsSelector,
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import theme from 'theme';
import { WarningConfirmationPopup } from '../../../../common/components/Popups/ConfirmationPopup';
import ApproveEstimate from './ApproveEstimate';
import EstimateItemCount from './EstimateItemCount';
import EstimateItems from './EstimateItems';
import EstimateNotes from './EstimateNotes';

const EstimateInfo = () => {
    const { estimateNotes, showEstimateToConsumer, phaseId } =
        useAppSelector(repairOrderDetailSelector);
    const estimateItems = useAppSelector(estimateItemsSelector);
    const { orderConfig } = useAppSelector(repairShopInfoSelector);
    const { t } = useAppTranslation();

    const [warningPopupIsOpen, setWarningPopupIsOpen] = useState<boolean>(true);

    const parsedPriorityTextValues: PriorityTextValues | null =
        orderConfig?.orderPriorityTextValues || null;

    const canApprove = useMemo(
        () =>
            estimateItems.some(
                (item: ViewByCostEstimateItemDto) =>
                    item.isChecked &&
                    item.approveStatus !== ApproveStatusEnum.ApprovedByConsumer &&
                    item.approveStatus !== ApproveStatusEnum.ApprovedByTeamMember
            ),
        [estimateItems]
    );

    const shouldApplyOrderModificationProhibitingLogic = useMemo(
        () => phaseId === Phases.Closed,
        [phaseId]
    );

    const ClosedOrderWarningBody: ReactNode = (
        <div>
            <p>{t('viewByCost.estimate.closedOrderWarning.body1')}</p>
            <p>{t('viewByCost.estimate.closedOrderWarning.body2')}</p>
        </div>
    );

    return (
        <StyledGrid>
            <Box sx={{ minWidth: 'min(500px, 90vw)' }}>
                <EstimateItemCount estimateItems={estimateItems} />
                {orderConfig && orderConfig.enableOrderPriorityText && (
                    <InstructionalTextSection>
                        <InstructionalTextSectionItem>
                            <PriorityBar color="#F15857" />
                            <InstructionalText>
                                {parsedPriorityTextValues?.red ||
                                    t('settings.orders.orderPriorityText.redPriorityDefaultText')}
                            </InstructionalText>
                        </InstructionalTextSectionItem>
                        <InstructionalTextSectionItem>
                            <PriorityBar color="#FFC626" />
                            <InstructionalText>
                                {parsedPriorityTextValues?.yellow ||
                                    t(
                                        'settings.orders.orderPriorityText.yellowPriorityDefaultText'
                                    )}
                            </InstructionalText>
                        </InstructionalTextSectionItem>
                        <InstructionalTextSectionItem>
                            <PriorityBar color="#36CE91" />
                            <InstructionalText>
                                {parsedPriorityTextValues?.green ||
                                    t('settings.orders.orderPriorityText.greenPriorityDefaultText')}
                            </InstructionalText>
                        </InstructionalTextSectionItem>
                    </InstructionalTextSection>
                )}
                <StyledGridItemInfo>
                    <EstimateItems estimateItems={estimateItems} />
                    {estimateNotes && estimateNotes.length > 0 && !canApprove && (
                        <EstimateNotes notes={estimateNotes} />
                    )}
                </StyledGridItemInfo>
            </Box>
            {showEstimateToConsumer && <ApproveEstimate />}
            <WarningConfirmationPopup
                body={ClosedOrderWarningBody}
                title={t('viewByCost.estimate.closedOrderWarning.title')}
                open={warningPopupIsOpen && shouldApplyOrderModificationProhibitingLogic}
                showCloseBtn={false}
                displayDivider={true}
                onClose={() => setWarningPopupIsOpen(false)}
                confirm={t('viewByCost.estimate.closedOrderWarning.button')}
                onConfirm={() => setWarningPopupIsOpen(false)}
            />
        </StyledGrid>
    );
};

const InstructionalTextSection = styled(Box)({
    display: 'grid',
    gap: 6,
    marginTop: 10,
    marginLeft: 37,
});

const InstructionalTextSectionItem = styled(Box)({
    display: 'flex',
    gap: 7,
});

const PriorityBar = styled('div')<{ color: string }>(({ color }) => ({
    marginTop: 8,
    height: 3,
    width: 16,
    backgroundColor: color,
}));

const InstructionalText = styled(Box)({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
});

const StyledGridItemInfo = styled(Grid)(({ theme }) => ({
    '&::-webkit-scrollbar': {
        width: 0,
    },
    '&::-webkit-scrollbar-track': {
        background: `transparent !important`,
    },
}));

const StyledGrid = styled('div')(({ theme }) => ({
    marginBottom: 70,
    [theme.breakpoints.up('md')]: {
        display: 'flex',
        justifyContent: 'justify-start',
        alignItems: 'flex-start',
        gap: '108px',
    },
}));

export default EstimateInfo;
