import { Box, Grid } from '@mui/material';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useConfirmEstimateMutation from 'common/hooks/useConfirmEstimateMutation';
import useConfirmPaymentEstimateMutation from 'common/hooks/useConfirmPaymentEstimateMutation';
import { usePostHog } from 'common/hooks/usePostHog';
import { Colors } from 'common/styles/Colors';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { selectEnableOnlinePayment, selectSettings } from 'store/slices/globalSettingsSlice';
import { setPayItems, setPaymentUrlButton } from 'store/slices/viewByCost';
import {
    estimateItemsSelector,
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import { formatNumber } from 'views/ViewByCost/common/functions';
import { ApprovalReportedModal } from './ApprovalReportedModal';
import useStyles from './css';

type ApproveModalProps = {
    isOpen: boolean;
    onClose: () => void;
};

export enum APPROVE_MODAL_SHOW {
    OLD_ALERT,
    NEW_ALERT,
    PAYMENT_PROCESSED,
    WHATSAPP_PAYMENT,
}

const ApproveModal = ({ isOpen, onClose }: ApproveModalProps) => {
    const dispatch = useAppDispatch();
    const [isApproved, setIsApproved] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const { orderId } = useParams<{ orderId: string }>();

    const [activeUrl, setActiveUrl] = useState('');

    const { repairOrderNumber, discount, discountType, make, model, year } =
        useAppSelector(repairOrderDetailSelector);
    const estimateItems = useAppSelector(estimateItemsSelector);
    const { taxPercentage } = useAppSelector(repairShopInfoSelector);
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const enableOnlinePayments = useAppSelector(selectEnableOnlinePayment);
    const requireDecimals = useMemo(
        () => (repairShopSettings?.features.enableRemoveDecimals ? false : true),
        [repairShopSettings]
    );

    const { capturePosthogEvent } = usePostHog();

    const defaultAlert = enableOnlinePayments
        ? APPROVE_MODAL_SHOW.NEW_ALERT
        : APPROVE_MODAL_SHOW.OLD_ALERT;
    const [show, setShow] = useState<APPROVE_MODAL_SHOW>(defaultAlert);

    const { t } = useAppTranslation();
    const classes = useStyles();

    const { mutate: confirmEstimateMutate } = useConfirmEstimateMutation(() => {
        setIsApproved(true);
    });

    const handleClose = () => {
        if (!paymentNowIsLoading && !paymentLaterIsLoading) {
            if (Boolean(onClose)) onClose();
            setShow(defaultAlert);
        }
    };

    const handleConfirmEstimate = () => {
        capturePosthogEvent('estimate_approved', {
            orderNumber: repairOrderNumber,
            totalApproved: total,
            vehicleBrand: make,
            vehicleModel: model,
            vehicleYear: year,
        });
        if (enableOnlinePayments) {
            setIsApproved(true);
        } else {
            confirmEstimateMutate({
                repairOrderId: orderId?.toString() || '',
                items: checkedItems,
            });
        }
    };

    const checkedItems = useMemo(
        () => estimateItems.filter(({ isChecked }) => isChecked),
        [estimateItems]
    );
    const subtotal = useMemo(
        () =>
            checkedItems.reduce((sum, item) => {
                return sum + (item.totalCost || 0);
            }, 0),
        [checkedItems]
    );

    const subtotalDiscounted = useMemo(() => {
        if (discount && discount > 0 && subtotal > 0) {
            if (discountType === 'Currency') {
                return subtotal - discount;
            } else if (discountType === 'Percentage') {
                return subtotal * ((100 - discount) / 100);
            }
        }
        return subtotal;
    }, [discount, discountType, subtotal]);

    const taxes = subtotalDiscounted * (taxPercentage || 0);
    const total = subtotalDiscounted + taxes;

    const { mutate: confirmEstimatePaymentNowMutate, isLoading: paymentNowIsLoading } =
        useConfirmPaymentEstimateMutation(
            (url) => {
                dispatch(
                    setPayItems({
                        items: checkedItems,
                    })
                );
                setShow(APPROVE_MODAL_SHOW.PAYMENT_PROCESSED);
                dispatch(setPaymentUrlButton({ url, orderId: orderId || '' }));

                setActiveUrl(url);
            },
            true,
            'pay_now_clicked',
            {
                orderNumber: repairOrderNumber,
                totalApproved: total,
                vehicleBrand: make,
                vehicleModel: model,
                vehicleYear: year,
            }
        );

    const { mutate: confirmEstimatePaymentLaterMutate, isLoading: paymentLaterIsLoading } =
        useConfirmPaymentEstimateMutation(
            (url) => {
                dispatch(
                    setPayItems({
                        items: checkedItems,
                    })
                );
                setShow(APPROVE_MODAL_SHOW.WHATSAPP_PAYMENT);
                dispatch(setPaymentUrlButton({ url, orderId: orderId || '' }));
            },
            false,
            'pay_later_clicked',
            {
                orderNumber: repairOrderNumber,
                totalApproved: total,
                vehicleBrand: make,
                vehicleModel: model,
                vehicleYear: year,
            }
        );

    useEffect(() => {
        if (activeUrl) {
            const link = document.createElement('a');
            link.href = activeUrl;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            setActiveUrl('');
        }
    }, [activeUrl]);

    return (
        <Modal open={isOpen} onClose={handleClose}>
            {isApproved ? (
                <ApprovalReportedModal
                    paymentLaterIsLoading={paymentLaterIsLoading}
                    paymentNowIsLoading={paymentNowIsLoading}
                    show={show}
                    onClose={handleClose}
                    handlePayLater={() => {
                        confirmEstimatePaymentLaterMutate({
                            repairOrderId: orderId?.toString() || '',
                            items: checkedItems,
                        });
                    }}
                    handlePayNow={() => {
                        confirmEstimatePaymentNowMutate({
                            repairOrderId: orderId?.toString() || '',
                            items: checkedItems,
                        });
                    }}
                />
            ) : (
                <Box component="div" className={classes.container}>
                    <h2 className={classes.title}>{t('viewByCost.estimate.approveTitle2')}</h2>
                    <Grid
                        container
                        ref={containerRef}
                        className={clsx(classes.estimateContainer, classes.scrollable)}
                    >
                        {checkedItems.map(({ repairName, totalCost }) => (
                            <Grid item className={classes.estimateRow}>
                                <span className={classes.itemText}>{repairName}</span>
                                <span className={classes.itemEstimate}>
                                    {formatNumber(
                                        internationalization,
                                        totalCost || 0,
                                        requireDecimals
                                    )}
                                </span>
                            </Grid>
                        ))}
                    </Grid>
                    <div className={classes.divider} />
                    <Grid container className={classes.totalsContainer}>
                        {discount && discount > 0 ? (
                            <Grid item className={classes.estimateRow}>
                                <span className={classes.text} style={{ color: Colors.Grey5 }}>
                                    {t('viewByCost.estimate.discount')}
                                </span>
                                <span className={classes.estimate} style={{ color: Colors.CM3 }}>
                                    {discountType === 'Percentage'
                                        ? (discount || 0) + '%'
                                        : formatNumber(
                                              internationalization,
                                              discount || 0,
                                              requireDecimals
                                          )}
                                </span>
                            </Grid>
                        ) : (
                            <></>
                        )}
                        <Grid item className={classes.estimateRow}>
                            <span className={classes.text} style={{ color: Colors.Grey5 }}>
                                {t('viewByCost.estimate.subtotal')}
                            </span>
                            <span className={classes.estimate} style={{ color: Colors.CM3 }}>
                                {formatNumber(
                                    internationalization,
                                    subtotalDiscounted || 0,
                                    requireDecimals
                                )}
                            </span>
                        </Grid>
                        <Grid item className={classes.estimateRow}>
                            <span className={classes.text} style={{ color: Colors.Grey5 }}>
                                {t('viewByCost.estimate.taxes')}
                            </span>
                            <span className={classes.estimate} style={{ color: Colors.CM3 }}>
                                {formatNumber(internationalization, taxes || 0, requireDecimals)}
                            </span>
                        </Grid>
                        <Grid item className={classes.estimateRow}>
                            <span className={classes.text} style={{ color: Colors.Success }}>
                                {t('viewByCost.estimate.total')}
                            </span>
                            <span className={classes.estimate} style={{ color: Colors.Success }}>
                                {formatNumber(internationalization, total || 0, requireDecimals)}
                            </span>
                        </Grid>
                    </Grid>
                    <Grid container className={classes.buttonContainer}>
                        <Grid item style={{ width: 140 }}>
                            <Button
                                label={t('viewByCost.estimate.cancel')}
                                cmosVariant={'filled'}
                                cmosSize={'medium'}
                                color={Colors.GrayBlue}
                                blockMode
                                onClick={handleClose}
                            />
                        </Grid>
                        <Grid item style={{ width: 140 }}>
                            <Button
                                label={t('viewByCost.estimate.confirm')}
                                cmosVariant={'filled'}
                                cmosSize={'medium'}
                                blockMode
                                onClick={handleConfirmEstimate}
                            />
                        </Grid>
                    </Grid>
                </Box>
            )}
        </Modal>
    );
};

export default ApproveModal;
