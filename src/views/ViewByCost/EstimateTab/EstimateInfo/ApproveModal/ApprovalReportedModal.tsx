import { Box, CircularProgress, Grid, Icon<PERSON>utton, Stack, styled, Typography } from '@mui/material';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useTranslation } from 'react-i18next';
import { APPROVE_MODAL_SHOW } from '.';
import useStyles from './css';

type ApprovalReportedModalActionsProps = {
    onClose: () => void;
};
export const ApprovalReportedModal = ({
    show,
    ...props
}: ApprovalReportedModalActionsProps & {
    show: APPROVE_MODAL_SHOW;
    handlePayLater: () => void;
    handlePayNow: () => void;
    paymentNowIsLoading: boolean;
    paymentLaterIsLoading: boolean;
}) => {
    return (
        <>
            {show === APPROVE_MODAL_SHOW.OLD_ALERT && <OldAlert onClose={props.onClose} />}
            {show === APPROVE_MODAL_SHOW.NEW_ALERT && (
                <NewAlert
                    handlePayLater={props.handlePayLater}
                    handlePayNow={props.handlePayNow}
                    paymentNowIsLoading={props.paymentNowIsLoading}
                    paymentLaterIsLoading={props.paymentLaterIsLoading}
                />
            )}
            {show === APPROVE_MODAL_SHOW.PAYMENT_PROCESSED && (
                <ProcessedAlert onClose={props.onClose} />
            )}
            {show === APPROVE_MODAL_SHOW.WHATSAPP_PAYMENT && (
                <WhatsappAlert onClose={props.onClose} />
            )}
        </>
    );
};

const OldAlert = ({ onClose }: ApprovalReportedModalActionsProps) => {
    const { t } = useTranslation();
    const classes = useStyles();

    return (
        <Grid
            container
            className={classes.approveContainer}
            justifyContent="center"
            alignItems="center"
        >
            <Grid item className={classes.iconContainer}>
                <CheckIcon size={IconSize.L} fill={Colors.Success} />
            </Grid>
            <Grid item>
                <h1 className={classes.approveMessage}>
                    {t('viewByCost.estimate.approvedMessage')}
                </h1>
            </Grid>
            <Box
                component="div"
                sx={{ display: { xs: 'block', sm: 'none' } }}
                className={classes.xIcon}
            >
                <IconButton onClick={onClose} size="large">
                    <CloseIcon fill={Colors.Neutral6} />
                </IconButton>
            </Box>
        </Grid>
    );
};

const NewAlert = ({
    handlePayNow,
    handlePayLater,
    paymentNowIsLoading,
    paymentLaterIsLoading,
}: {
    handlePayLater: () => void;
    handlePayNow: () => void;
    paymentNowIsLoading: boolean;
    paymentLaterIsLoading: boolean;
}) => {
    const { t } = useTranslation();
    const classes = useStyles();
    const disabled = paymentNowIsLoading || paymentLaterIsLoading;

    return (
        <NewModalContainer container justifyContent="center" alignItems="center">
            <Grid item className={classes.iconPaymentContainer}>
                <CheckIcon size={IconSize.L} fill={Colors.CM1} />
            </Grid>
            <Grid
                item
                display="flex"
                justifyContent="center"
                alignItems="center"
                direction="column"
            >
                <Typography
                    className={classes.approveMessage}
                    sx={{ width: { xs: '340px', sm: 'auto' } }}
                >
                    {t('viewByCost.estimate.approvedMessage')}
                </Typography>
                <Message sx={{ marginTop: '26px', width: { xs: '340px', sm: 'auto' } }}>
                    {t('viewByCost.estimate.approvedMessageDescription')}
                </Message>
                <Message sx={{ marginTop: '17px' }}>
                    {t('viewByCost.estimate.approvedMessageQuestion')}
                </Message>
                <Stack
                    direction="row"
                    justifyContent="center"
                    sx={{ marginTop: '23px', gap: '12px' }}
                >
                    <SButton color={Colors.Neutral3} onClick={handlePayLater} disabled={disabled}>
                        {paymentLaterIsLoading ? (
                            <CircularProgress size={20} />
                        ) : (
                            <>{t('viewByCost.estimate.laterButton')}</>
                        )}
                    </SButton>
                    <SButton onClick={handlePayNow} disabled={disabled}>
                        {paymentNowIsLoading ? (
                            <CircularProgress size={20} style={{ color: Colors.CM5 }} />
                        ) : (
                            <>{t('viewByCost.estimate.nowButton')}</>
                        )}
                    </SButton>
                </Stack>
            </Grid>
        </NewModalContainer>
    );
};

const NewModalContainer = styled(Grid)(({ theme }) => ({
    position: 'relative',
    width: 360,
    border: `1px solid ${Colors.CM1}`,
    borderRadius: 14,
    padding: '24px 16px',
    gap: 30,
    [theme.breakpoints.up('sm')]: {
        width: 476,
        padding: '24px',
    },
}));

const ProcessedAlert = ({ onClose }: ApprovalReportedModalActionsProps) => {
    const { t } = useTranslation();
    const classes = useStyles();

    return (
        <Grid
            container
            className={classes.approveContainer}
            justifyContent="center"
            alignItems="center"
        >
            <Grid item className={classes.iconContainer}>
                <CheckIcon size={IconSize.L} fill={Colors.Success} />
            </Grid>
            <Grid
                item
                display="flex"
                justifyContent="center"
                alignItems="center"
                direction="column"
            >
                <Typography
                    className={classes.approveMessage}
                    sx={{ width: { xs: '320px', sm: 'auto' } }}
                >
                    {t('viewByCost.estimate.paymentProcessedTitle')}
                </Typography>
                <Message sx={{ marginTop: '26px', width: { xs: '320px', sm: 'auto' } }}>
                    {t('viewByCost.estimate.paymentProcessedDescription')}
                </Message>
                <Box display="flex" justifyContent="center" sx={{ marginTop: '23px' }}>
                    <SButton color={Colors.Neutral3} onClick={onClose}>
                        {t('viewByCost.estimate.closeButton')}
                    </SButton>
                </Box>
            </Grid>
            <Box
                component="div"
                sx={{ display: { xs: 'block', sm: 'none' } }}
                className={classes.xIcon}
            >
                <IconButton onClick={onClose} size="large">
                    <CloseIcon fill={Colors.Neutral6} />
                </IconButton>
            </Box>
        </Grid>
    );
};

const WhatsappAlert = ({ onClose }: ApprovalReportedModalActionsProps) => {
    const { t } = useTranslation();
    const classes = useStyles();

    return (
        <Grid
            container
            className={classes.approveContainer}
            justifyContent="center"
            alignItems="center"
        >
            <Grid item className={classes.iconContainer}>
                <CheckIcon size={IconSize.L} fill={Colors.Success} />
            </Grid>
            <Grid
                item
                display="flex"
                justifyContent="center"
                alignItems="center"
                direction="column"
            >
                <Typography
                    className={classes.approveMessage}
                    sx={{ width: { xs: '300px', sm: 'auto' } }}
                >
                    {t('viewByCost.estimate.paymentWhatsappTitle')}
                </Typography>
                <Message sx={{ marginTop: '26px', width: { xs: '320px', sm: 'auto' } }}>
                    {t('viewByCost.estimate.paymentWhatsappDescription')}
                </Message>
                <Box display="flex" justifyContent="center" sx={{ marginTop: '23px' }}>
                    <SButton color={Colors.Neutral3} onClick={onClose}>
                        {t('viewByCost.estimate.closeButton')}
                    </SButton>
                </Box>
            </Grid>
            <Box
                component="div"
                sx={{ display: { xs: 'block', sm: 'none' } }}
                className={classes.xIcon}
            >
                <IconButton onClick={onClose} size="large">
                    <CloseIcon fill={Colors.Neutral6} />
                </IconButton>
            </Box>
        </Grid>
    );
};

const Message = styled(Box)(() => ({
    fontFamily: 'Roboto',
    fontWeight: 400,
    color: '#4A4D51',
    fontSize: '12px',
    textAlign: 'center',
    width: '360px',
}));

const SButton = styled(Button)(({ theme }) => ({
    width: '140px',
    [theme.breakpoints.up('sm')]: {
        width: '170px',
    },
}));
