import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

const ApproveModalStyles = makeStyles((theme: Theme) => ({
    container: {
        display: 'flex',
        flexDirection: 'column',
        gap: 19,
        maxHeight: 450,
        padding: '34px 20px 25px 20px',
        [theme.breakpoints.up('sm')]: {
            width: 476,
            padding: '34px 40px 25px 40px',
        },
    },
    title: {
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
        margin: 0,
    },
    estimateContainer: {
        display: 'flex',
        flexDirection: 'column',
        gap: 5,
        flexWrap: 'nowrap',
    },
    scrollable: {
        ...scrollbarStyle(),
        overflowY: 'scroll',
    },
    totalsContainer: {
        display: 'flex',
        flexDirection: 'column',
        gap: 5,
    },
    estimateRow: {
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
    },
    divider: {
        width: '100%',
        height: 1,
        backgroundColor: '#CAC4D0',
    },
    itemText: {
        maxWidth: '65%',
        ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[7]),
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: '-webkit-box',
        WebkitLineClamp: '2',
        WebkitBoxOrient: 'vertical',
    },
    itemEstimate: {
        ...FontSecondary(HeaderStyles.H5_14px, true, Colors.CM3),
    },
    text: {
        ...FontSecondary(HeaderStyles.H6_12px, false),
    },
    estimate: {
        ...FontPrimary(HeaderStyles.H5_14px, true),
    },
    buttonContainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 15,
        [theme.breakpoints.up('sm')]: {
            justifyContent: 'flex-end',
            gap: 10,
        },
    },
    approveContainer: {
        position: 'relative',
        width: 360,
        border: `1px solid ${Colors.Success}`,
        borderRadius: 14,
        padding: '24px 16px',
        gap: 30,
        [theme.breakpoints.up('sm')]: {
            width: 476,
            padding: '24px',
        },
    },
    xIcon: {
        position: 'absolute',
        top: 8,
        right: 8,
    },
    iconContainer: {
        width: 50,
        height: 50,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '50%',
        border: `1px solid ${Colors.Success}`,
    },
    iconPaymentContainer: {
        width: 50,
        height: 50,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '50%',
        border: `1px solid ${Colors.CM1}`,
    },
    approveMessage: {
        ...FontPrimary(HeaderStyles.H4_18px, true, theme.palette.neutral[8]),
        textAlign: 'center',
        margin: 0,
    },
}));

export default ApproveModalStyles;
