import { styled } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import EstimateHeader from '../EstimateHeader';
import EstimateItemTable from '../EstimateItemTable';

type EstimateItemsProps = {
    estimateItems: ViewByCostEstimateItemDto[];
};
const EstimateItems = ({ estimateItems }: EstimateItemsProps) => {
    return (
        <DivRoot>
            <EstimateHeader />
            <EstimateItemTable estimateItems={estimateItems} />
        </DivRoot>
    );
};

const DivRoot = styled('div')(({ theme }) => ({
    '--orders-table-row-height': '68px',
    marginTop: 16,
    [theme.breakpoints.up('sm')]: {
        marginBottom: 20,
        marginTop: 24,
        overflow: 'auto !important',
    },
}));

export default EstimateItems;
