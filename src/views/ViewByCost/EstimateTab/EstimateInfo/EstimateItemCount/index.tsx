import { styled } from '@mui/material';
import { ViewByCostEstimateItemDto } from 'api/viewByCost';
import { EstimatePriority, EstimatePriorityEnum } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import ItemCount from './ItemCount';

type EstimateITemCountProps = {
    estimateItems: ViewByCostEstimateItemDto[];
};

const EstimateItemCount = ({ estimateItems }: EstimateITemCountProps) => {
    const { t } = useAppTranslation();

    const urgentItemCount = useMemo(
        () =>
            estimateItems?.filter(({ priority }) => priority === EstimatePriorityEnum.Urgent)
                .length,
        [estimateItems]
    );
    const mediumItemCount = useMemo(
        () =>
            estimateItems?.filter(({ priority }) => priority === EstimatePriorityEnum.Medium)
                .length,
        [estimateItems]
    );
    const lowItemCount = useMemo(
        () => estimateItems?.filter(({ priority }) => priority === EstimatePriorityEnum.Low).length,
        [estimateItems]
    );

    return (
        <GridContainer>
            <GridItem>
                <ItemCount
                    itemCount={urgentItemCount || 0}
                    label={t('viewByCost.common.urgent')}
                    priority={EstimatePriority.Urgent}
                />
            </GridItem>
            <GridItem>
                <ItemCount
                    itemCount={mediumItemCount || 0}
                    label={t('viewByCost.common.medium')}
                    priority={EstimatePriority.Medium}
                />
            </GridItem>
            <GridItem>
                <ItemCount
                    itemCount={lowItemCount || 0}
                    label={t('viewByCost.common.ok')}
                    priority={EstimatePriority.Low}
                />
            </GridItem>
        </GridContainer>
    );
};

export default EstimateItemCount;

const GridContainer = styled('div')(({ theme }) => ({
    marginTop: 16,
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    justifyContent: 'justify-start',
    alignItems: 'center',
    [theme.breakpoints.up('sm')]: {
        marginTop: '32px',
        maxWidth: '680px',
    },
}));

const GridItem = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    gap: 8,
}));
