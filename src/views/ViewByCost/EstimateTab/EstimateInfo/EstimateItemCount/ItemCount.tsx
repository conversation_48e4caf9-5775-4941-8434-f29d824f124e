import { styled } from '@mui/material';
import { EstimatePriority } from 'common/constants';

type ItemCountProps = {
    itemCount: number;
    label: string;
    priority: EstimatePriority;
};

const ItemCount = ({ itemCount, label, priority }: ItemCountProps) => {
    return (
        <>
            <H1ItemCount>{itemCount}</H1ItemCount>
            <H2Label>{label}</H2Label>
            <DivBar className={priority} />
        </>
    );
};

export default ItemCount;

const H2Label = styled('h2')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[9],
    lineHeight: 'normal',
    margin: 0,
    [theme.breakpoints.up('sm')]: {},
}));

const H1ItemCount = styled('h1')(({ theme }) => ({
    ...theme.typography.h2Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[9],
    lineHeight: 'normal',
    margin: 0,
    [theme.breakpoints.up('sm')]: {
        ...theme.typography.h1Inter,
        fontWeight: 'normal',
    },
}));

const DivBar = styled('div')({
    marginTop: 8,
    height: 8,
    width: '100%',

    [`&.${EstimatePriority.Urgent}`]: {
        backgroundColor: '#F15857',
    },
    [`&.${EstimatePriority.Medium}`]: {
        backgroundColor: '#FFC626',
    },
    [`&.${EstimatePriority.Low}`]: {
        backgroundColor: '#36CE91',
    },
});
