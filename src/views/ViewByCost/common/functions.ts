import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { InternationalizationDto } from 'datacontracts/Interationalization/InternationalizationDto';

export const a11yProps = (index: any) => {
    return {
        id: `tab-${index}`,
        'aria-controls': `tabpanel-${index}`,
    };
};

export const formatNumber = (
    internationalization: InternationalizationDto,
    number: number,
    requireDecimals: boolean
) => {
    let currency = InternationalizationLogic.numberToCurrency(internationalization, number, {
        allowZero: true,
        requireDecimals: requireDecimals,
    });
    // Add .00 if the number has no decimals.
    if (currency.indexOf('.') === -1 && requireDecimals) currency = `${currency}.00`;
    return currency;
};
