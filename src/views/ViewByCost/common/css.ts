import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const WorkshopBannerInfoStyles = makeStyles((theme: Theme) => ({
    container: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start',
        gap: '13px',
        [theme.breakpoints.up('sm')]: {
            gap: '24px',
            marginTop: 90,
        },
    },
    logoContainer: {},
    logo: {
        borderRadius: '50%',
        aspectRatio: '1',
        objectFit: 'cover',
        width: '45px',
        [theme.breakpoints.up('sm')]: {
            width: '75px',
        },
    },
    divisor: {
        width: '1px',
        height: '45px',
        backgroundColor: '#CAC4D0',
        [theme.breakpoints.up('sm')]: {
            height: '64px',
        },
    },
    info: {
        height: '64px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'start',
        gap: '4px',
        [theme.breakpoints.up('sm')]: {
            height: '64px',
        },
    },
    title: {
        ...FontPrimary(HeaderStyles.H4_18px, true),
        margin: '0',
        color: theme.palette.neutral[9],
    },
    salute: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[9]),
        margin: '0',
        color: theme.palette.neutral[9],
    },
}));

export default WorkshopBannerInfoStyles;
