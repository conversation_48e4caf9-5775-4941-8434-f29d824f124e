import { Grid } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Http } from 'services/Http';
import { useAppSelector } from 'store';
import {
    repairOrderDetailSelector,
    repairShopInfoSelector,
} from 'store/slices/viewByCost/selectors';
import default_logo from '../../../assets/images/default-logo.png';
import useStyles from './css';

const WorkshopBannerInfo = () => {
    const { t } = useAppTranslation();
    const workshopInfo = useAppSelector(repairShopInfoSelector);
    const { firstName, lastName } = useAppSelector(repairOrderDetailSelector);
    const classes = useStyles();
    return (
        <Grid
            container
            justifyContent="flex-start"
            className={classes.container}
            alignItems="center"
        >
            <Grid item className={classes.logoContainer}>
                <img
                    src={workshopInfo.workshopLogoUrl?.replace('~', Http.HOST) || default_logo}
                    alt="Logo"
                    className={classes.logo}
                />
            </Grid>
            <div className={classes.divisor} />
            <Grid item className={classes.info}>
                <h1 className={classes.title}>{workshopInfo.accountName}</h1>
                <h2 className={classes.salute}>
                    {t('viewByCost.common.greeting')}
                    {`${firstName || ''} ${lastName || ''}`}
                </h2>
            </Grid>
        </Grid>
    );
};

export default WorkshopBannerInfo;
