import { styled, Tab, Tabs } from '@mui/material';
import { EstimateIcon } from 'common/components/Icons/EstimateIcon';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { StoplightIcon } from 'common/components/Icons/StoplightIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { IconSize } from 'common/styles/IconSize';
import { ChangeEvent, Dispatch, SetStateAction } from 'react';
import { a11yProps } from '../../common/functions';

type MobileTabsProps = {
    tabValue: number;
    setTabValue: Dispatch<SetStateAction<number>>;
};

const MobileTabs = ({ tabValue, setTabValue }: MobileTabsProps) => {
    const { t } = useAppTranslation();

    return (
        <DivContainer>
            <StyledTabs
                onChange={(event: ChangeEvent<{}>, newValue: number) => {
                    setTabValue(newValue);
                }}
                value={tabValue}
            >
                <StyledTab
                    aria-label={t('viewByCost.tabs.mobileEstimate')}
                    label={t('viewByCost.tabs.mobileEstimate')}
                    icon={<EstimateIcon size={IconSize.L} fill="currentColor" />}
                    {...a11yProps(0)}
                />
                <StyledTab
                    aria-label={t('viewByCost.tabs.mobilePhase')}
                    label={t('viewByCost.tabs.mobilePhase')}
                    icon={<StoplightIcon size={IconSize.L} fill="currentColor" />}
                    {...a11yProps(1)}
                />
                <StyledTab
                    aria-label={t('viewByCost.tabs.mobileGeneralInfo')}
                    label={t('viewByCost.tabs.mobileGeneralInfo')}
                    icon={<InfoIcon size={IconSize.L} fill="currentColor" />}
                    {...a11yProps(2)}
                />
            </StyledTabs>
        </DivContainer>
    );
};

export default MobileTabs;

const DivContainer = styled('div')(({ theme }) => ({
    position: 'fixed',
    left: 0,
    right: 0,
    transition: 'background-color .1s',
    height: 70,
    bottom: 0,
    backgroundColor: theme.palette.neutral[9],
    padding: 0,
    zIndex: 2,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
    width: '100%',
    height: '100%',
    '& .MuiTabs-flexContainer': {
        gap: 'var(--gap)',
        justifyContent: 'stretch',
    },
    '& .MuiTabs-indicator': {
        backgroundColor: theme.palette.primary.main,
        height: 4,
        top: 0,
    },
    '--gap': '20px',
    backgroundColor: theme.palette.neutral[9],
}));

const StyledTab = styled(Tab)(({ theme }) => ({
    textTransform: 'initial',
    padding: '10px 2px',
    width: 100,
    height: 'var(--tabs-height)',
    minHeight: 'initial',
    color: theme.palette.neutral[6],
    opacity: 1,
    ...theme.typography.h6Roboto,
    fontWeight: '500',
    flexGrow: 1,

    transition: 'font-weight .2s, color .2s',

    '& > svg': {
        transition: 'transform .2s',
    },

    '&.Mui-selected': {
        color: theme.palette.neutral[1],
        backgroundColor: theme.palette.neutral[8],
        fontWeight: '700',

        '& > svg': {
            transform: 'scale(1.15)',
        },
    },
    '&::after': {
        content: '" "',
        display: 'block',
        position: 'absolute',
        left: 10,
        right: 10,
        height: 2,
        bottom: -2,
        transition: 'all .3s',
    },
    '&:hover:not(.Mui-selected)::after': {
        left: 3,
        right: 3,
        backgroundColor: theme.palette.neutral[7],
        bottom: 0,
    },
}));
