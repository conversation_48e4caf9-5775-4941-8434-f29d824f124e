import { Box, styled } from '@mui/material';
import { useState } from 'react';
import TabPanel from 'views/Settings/General/TeamMembers/TeamMemberForm/TabPanel';
import EstimateTab from '../EstimateTab';
import GeneralTab from '../GeneralTab';
import PhaseTab from '../PhaseTab';
import MobileTabs from './MobileTabs';
import WebTabs from './WebTabs';

const ViewByCostTabs = () => {
    const [tabValue, setTabValue] = useState<number>(0);

    return (
        <>
            <DivContainer>
                <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                    <WebTabs tabValue={tabValue} setTabValue={setTabValue} />
                </Box>

                <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
                    <MobileTabs tabValue={tabValue} setTabValue={setTabValue} />
                </Box>

                <TabPanelNoScrollbar value={tabValue} index={0}>
                    <EstimateTab />
                </TabPanelNoScrollbar>
                <TabPanel value={tabValue} index={1}>
                    <PhaseTab />
                </TabPanel>
                <TabPanel value={tabValue} index={2}>
                    <GeneralTab />
                </TabPanel>
            </DivContainer>
        </>
    );
};

export default ViewByCostTabs;

const TabPanelNoScrollbar = styled(TabPanel)({
    '&::-webkit-scrollbar': {
        width: '0.1em',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'transparent',
    },
});

const DivContainer = styled('div')(({ theme }) => ({
    width: '90%',
    boxSizing: 'border-box',
    height: '95%',
    margin: '0 auto',
    [theme.breakpoints.up('sm')]: {
        // overflowY: 'hidden',
        paddingBottom: 0,
    },
    '&::-webkit-scrollbar': {
        width: '0.4em',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'transparent',
    },
}));
