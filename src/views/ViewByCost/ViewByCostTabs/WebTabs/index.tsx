import { styled, Tab, Tabs } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ChangeEvent, Dispatch, SetStateAction } from 'react';
import { a11yProps } from '../../common/functions';

type WebTabsProps = {
    tabValue: number;
    setTabValue: Dispatch<SetStateAction<number>>;
};

const WebTabs = ({ tabValue, setTabValue }: WebTabsProps) => {
    const { t } = useAppTranslation();

    return (
        <DivContainer>
            <StyledTabs
                onChange={(_event: ChangeEvent<{}>, newValue: number) => {
                    setTabValue(newValue);
                }}
                value={tabValue}
            >
                <StyledTab
                    aria-label={t('viewByCost.tabs.estimate')}
                    label={t('viewByCost.tabs.estimate')}
                    {...a11yProps(0)}
                />
                <StyledTab
                    aria-label={t('viewByCost.tabs.phase')}
                    label={t('viewByCost.tabs.phase')}
                    {...a11yProps(1)}
                />
                <StyledTab
                    aria-label={t('viewByCost.tabs.generalInfo')}
                    label={t('viewByCost.tabs.generalInfo')}
                    {...a11yProps(2)}
                />
            </StyledTabs>
        </DivContainer>
    );
};

export default WebTabs;

const DivContainer = styled('div')(({ theme }) => ({
    position: 'fixed',
    // make it 1px higher than the header to hide the border
    top: 'calc(var(--header-height) - 1px)',
    backgroundColor: theme.palette.neutral[1],
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
    zIndex: 20,
    width: '100vw',
    paddingLeft: '5vw',
    left: 0,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
    '& .MuiTabs-flexContainer': {
        gap: 'var(--gap)',
    },
    '& .MuiTabs-indicator': {
        backgroundColor: theme.palette.primary.main,
        height: 3,
        borderRadius: 2,
    },
    '--gap': '40px',
    height: 46,
    minHeight: 46,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
    textTransform: 'initial',
    padding: '15px 2px',
    minWidth: 30,
    height: '46',
    minHeight: 'initial',
    color: theme.palette.neutral[7],
    opacity: 1,
    ...theme.typography.h5Roboto,
    fontWeight: '500',

    '&.Mui-selected': {
        color: theme.palette.primary.main,
        fontWeight: '700',
    },
    '&::after': {
        content: '" "',
        display: 'block',
        position: 'absolute',
        left: 0,
        right: 0,
        height: 2,
        bottom: 1,
    },
    '&:hover:not(.Mui-selected)::after': {
        backgroundColor: theme.palette.neutral[7],
    },
}));
