import { styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import ViewByCostApi from 'api/viewByCost';
import AreaSpinner from 'common/components/AreaSpinner';
import UnauthorizedLayout from 'common/components/UnauthorizedLayout';
import { ApproveStatusEnum } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { usePostHog } from 'common/hooks/usePostHog';
import useToasters from 'common/hooks/useToasters';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { RootState, useAppDispatch } from 'store';
import { selectEnableOnlinePayment } from 'store/slices/globalSettingsSlice';
import { setGeneralInfo, setRepairOrderInfo, setRepairShopInfo } from 'store/slices/viewByCost';
import ViewByCostTabs from './ViewByCostTabs';

export const ViewByCost = () => {
    const [repairOrderNumber, setRepairOrderNumber] = useState<string | null>('');
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const { identifyPostHogCustomerFromRoData, registerPageView } = usePostHog();

    const enableOnlinePayments = useSelector(selectEnableOnlinePayment);

    const gs = useSelector((r: RootState) => ({
        uid: r.globalSettings.settings?.uid,
    }));

    const { orderId } = useParams<{ orderId: string }>();

    const { isFetching: isFetchingRepairShop } = useQuery(
        ['view-by-cost', 'repairShopInfo', gs.uid],
        () => ViewByCostApi.getRepairShopInfo(gs.uid || ''),
        {
            onSuccess: (repairShopInfo) => {
                dispatch(
                    setRepairShopInfo({
                        ...repairShopInfo,
                        taxPercentage: (repairShopInfo.taxPercentage || 0) / 100,
                    })
                );
            },
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            cacheTime: 0,
        }
    );

    const { isFetching: isFetchingRepairOrder } = useQuery(
        ['view-by-cost', 'repairOrderDetails', gs.uid, orderId],
        () => ViewByCostApi.getRepairOrderDetails(gs.uid || '', orderId || ''),
        {
            onSuccess: (repairOrderDetails) => {
                let { estimateItems } = repairOrderDetails;
                estimateItems =
                    estimateItems &&
                    estimateItems.map(({ approveStatus, ...item }) => {
                        let isChecked = false;
                        if (
                            approveStatus === ApproveStatusEnum.ApprovedByConsumer ||
                            approveStatus === ApproveStatusEnum.ApprovedByTeamMember ||
                            (enableOnlinePayments && item.isPayed)
                        )
                            isChecked = true;
                        return { ...item, approveStatus, isChecked };
                    });
                repairOrderDetails.estimateItems = estimateItems;
                dispatch(setRepairOrderInfo(repairOrderDetails));
                identifyPostHogCustomerFromRoData(repairOrderDetails);
                registerPageView('VIEW_BY_COST');
                setRepairOrderNumber(repairOrderDetails.repairOrderNumber);
            },
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            cacheTime: 0,
        }
    );

    const { data } = useQuery(
        ['view-by-cost', 'generalInfoDetails', gs.uid, orderId],
        () => ViewByCostApi.orderFieldsValues(gs.uid || '', orderId || ''),
        {
            staleTime: 0,
        }
    );

    useEffect(() => {
        if (data) {
            dispatch(setGeneralInfo(data));
        }
    }, [data, dispatch]);

    useQuery(
        ['view-by-cost', 'increaseConsumerVisitsCounter', orderId],
        () => ViewByCostApi.increaseConsumerVisitsCounter(orderId!),
        {
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            cacheTime: 0,
            enabled: !!orderId,
        }
    );

    useDocumentTitle(`${t('viewByCost.common.documentTitle')}${repairOrderNumber}`);

    return (
        <UnauthorizedLayout
            noPadding
            titleSection={`${t('viewByCost.common.documentTitle')}${repairOrderNumber}`}
        >
            {!isFetchingRepairOrder && !isFetchingRepairShop ? (
                <ViewByCostTabs />
            ) : (
                <DivLoadingContainer>
                    <AreaSpinner />
                </DivLoadingContainer>
            )}
        </UnauthorizedLayout>
    );
};

export default ViewByCost;

const DivLoadingContainer = styled('div')({
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
});
