import { Box } from '@mui/material';
import { SideCarIcon } from 'common/components/Icons/SideCarIcon';
import Stepper from 'common/components/Stepper';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useAppSelector } from 'store';
import {
    repairOrderDetailSelector,
    repairShopPhasesSelector,
} from 'store/slices/viewByCost/selectors';
import useStyles from './css';

const PhaseStepper = () => {
    const { t } = useAppTranslation();
    const classes = useStyles();
    const phases = useAppSelector(repairShopPhasesSelector);
    const { phaseId } = useAppSelector(repairOrderDetailSelector);

    const phaseSteps = phases.map(({ name, id }) => {
        if (id === Phases.NoPhase) {
            name = t('workshopPlanner.phases.noPhase');
        }
        if (id === Phases.Closed) {
            name = t('workshopPlanner.phases.closedOrder');
        }
        return { label: name || '', id: id! };
    });

    const activeStep = phases.findIndex(({ id }) => id === phaseId);
    return (
        <Box component="div" className={classes.container}>
            {/* Web horizontal stepper */}
            <Box component="div" sx={{ display: { xs: 'none', md: 'block' } }}>
                <Stepper
                    activeStep={activeStep}
                    steps={phaseSteps}
                    activeIcon={<SideCarIcon fill={Colors.CM1} size={IconSize.LL} />}
                />
            </Box>
            {/* Mobile vertical stepper */}
            <Box component="div" sx={{ display: { xs: 'block', md: 'none' } }}>
                <Stepper
                    activeStep={activeStep}
                    steps={phaseSteps}
                    activeIcon={<SideCarIcon fill={Colors.CM1} size={IconSize.LL} />}
                    vertical
                />
            </Box>
        </Box>
    );
};

export default PhaseStepper;
