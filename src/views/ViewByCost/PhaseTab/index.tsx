import { Box, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import ViewByCostApi from 'api/viewByCost';
import { LanguagesEnum } from 'common/constants/Language';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import moment from 'moment';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import { setPhases } from 'store/slices/viewByCost';
import { repairOrderDetailSelector } from 'store/slices/viewByCost/selectors';
import WorkshopBannerInfo from '../common/WorkshopBannerInfo';
import PhaseStepper from './PhaseStepper';

const PhaseTab = () => {
    const dispatch = useAppDispatch();
    const toasters = useToasters();
    const gs = useSelector((r: RootState) => ({
        uid: r.globalSettings.settings?.uid,
    }));
    const { t, i18n } = useAppTranslation();

    useQuery(
        ['view-by-cost', 'phases', gs.uid],
        () => ViewByCostApi.getRepairShopPhases(gs.uid || ''),
        {
            onSuccess: (phases) => {
                dispatch(setPhases(phases));
            },
            onError: () => {
                toasters.danger(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred')
                );
            },
            staleTime: 30000, // 30 seconds
            cacheTime: Infinity,
        }
    );

    const { deliveryDate } = useAppSelector(repairOrderDetailSelector);

    const format =
        i18n.language === LanguagesEnum.ENGLISH ? t('dateFormats.longEn') : t('dateFormats.longEs');

    const formattedDate = deliveryDate ? moment(deliveryDate).format(format) : '';
    return (
        <>
            <DivContainer>
                <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                    <WorkshopBannerInfo />
                </Box>
                <DivDeliveryDateContainer>
                    <strong>{t('viewByCost.phase.deliveryDate')}</strong>
                    <span>
                        {formattedDate} {deliveryDate ? 'hrs.' : '--'}
                    </span>
                </DivDeliveryDateContainer>
                <PhaseStepper />
            </DivContainer>
        </>
    );
};

const DivDeliveryDateContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 16,
    marginTop: 16,

    [theme.breakpoints.up('sm')]: {
        position: 'absolute',
        marginTop: 0,
        top: 20,
        right: 50,
        flexDirection: 'row',
        gap: 4,
    },
}));

const DivContainer = styled('div')({
    position: 'relative',
    marginBottom: 70,
    zIndex: 1,
});

export default PhaseTab;
