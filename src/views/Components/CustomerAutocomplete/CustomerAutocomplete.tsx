import { useCustomersSearchApi } from 'api/customers/dynamic-api';
import CustomerAutocompleteCustomApi, {
    CustomerAutocompleteCustomApiProps,
} from './CustomerAutocompleteCustomApi';

type CustomerAutocompleteProps = Omit<
    CustomerAutocompleteCustomApiProps,
    'queryKeyPrefix' | 'searchCustomers' | 'getCustomerById' | 'getFrequentCustomers'
>;

export default function CustomerAutocomplete({ disabled, ...props }: CustomerAutocompleteProps) {
    const api = useCustomersSearchApi();

    return (
        <CustomerAutocompleteCustomApi
            getCustomerById={api.getCustomerById}
            searchCustomers={api.searchCustomers}
            getFrequentCustomers={api.getFrequentCustomers}
            queryKeyPrefix={api.queryKeyPrefix}
            disabled={api.isReady ? disabled : true}
            {...props}
        />
    );
}
