import { styled } from '@mui/material';
import Typography from '@mui/material/Typography';
import { useQuery } from '@tanstack/react-query';
import { CustomerSearchItemDto } from 'api/Clients/Customers';
import { TFunction } from 'i18next';
import isEqual from 'lodash/isEqual';
import { useEffect, useRef, useState } from 'react';
import useStyles from './css';

type useStylesType = ReturnType<typeof useStyles>;

const StyledCaption = styled('span')({
    color: 'var(--neutral8)',
});

const StyledLabel = styled('span')({
    color: 'var(--neutral6)',
});

export function renderCustomerItem(customer: CustomerSearchItemDto, t: TFunction) {
    return (
        <Typography>
            <StyledCaption>{`${customer.firstName} ${customer.lastName} `}</StyledCaption>
            <span>/</span>
            <StyledCaption>{` ${t('appointments.step1.mobile')}: `}</StyledCaption>
            <StyledLabel>{`${customer.mobile} `}</StyledLabel>
            <span>/</span>
            <StyledCaption>{` ${t('appointments.step1.plates')}: `}</StyledCaption>
            <StyledLabel>
                {customer.vehiclePlates && customer.vehiclePlates.length
                    ? customer.vehiclePlates.join(', ')
                    : `${t('appointments.step1.noPlates')}`}
            </StyledLabel>
        </Typography>
    );
}

export function customerToString(customer: CustomerSearchItemDto, t: TFunction): string {
    return `${customer.firstName} ${customer.lastName} / ${t('appointments.step1.mobile')}: ${
        customer.mobile
    } / ${t('appointments.step1.plates')}: ${
        customer.vehiclePlates && customer.vehiclePlates.length
            ? customer.vehiclePlates.join(', ')
            : `${t('appointments.step1.noPlates')}`
    }`;
}

export function customerToQuerySearch(customer: CustomerSearchItemDto): string {
    return [customer.firstName, customer.lastName].filter(Boolean).join(' ');
}

export function useCustomersSearch(
    searchCustomers: (query: string) => Promise<CustomerSearchItemDto[]>,
    getFrequentCustomers: () => Promise<CustomerSearchItemDto[]>,
    query: string,
    queryKeyPrefix: string,
    enabled: boolean
) {
    const [customers, setCustomers] = useState<CustomerSearchItemDto[]>([]);
    const customersRef = useRef(customers);
    customersRef.current = customers;

    query = query.toLowerCase().trim();
    // search requires at least 2 characters
    if (query.length < 2) {
        query = '';
    }

    const canSearchUsingQuery = query.length >= 2;

    const { data } = useQuery(
        ['customers', 'search', `prefix=${queryKeyPrefix}`, query],
        () => (query === '' ? getFrequentCustomers() : searchCustomers(query)),
        {
            cacheTime: query === '' ? Infinity : 30000,
            staleTime: query === '' ? 10000 : 100,
            enabled,
        }
    );

    useEffect(() => {
        if (!data || isEqual(data, customersRef.current)) return;
        setCustomers(data);
    }, [data]);

    return { customers, canSearchUsingQuery };
}
