import {
    Autocomplete,
    Button,
    ListItem,
    ListItemButton,
    TextField as MuiTextField,
    Paper,
    PaperProps,
    styled,
} from '@mui/material';
import { CustomerSearchItemDto } from 'api/Clients/Customers';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Colors } from 'common/styles/Colors';
import React, { forwardRef, useCallback, useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import { useDebounce } from 'use-debounce';
import { useAutoCompleteCommonStyles } from '../common';
import useCustomerAutocompleteStyles from './css';
import { customerToQuerySearch, renderCustomerItem, useCustomersSearch } from './helpers';

const FrequentCustomerLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'bold',
    color: theme.palette.primary.light,
}));

const HeaderItem = styled(ListItem)({
    flexDirection: 'column',
    justifyContent: 'start',
    alignItems: 'start',
    paddingTop: 12,

    '&:empty': {
        display: 'none',
    },
});

const AddCustomButton = styled(Button)(({ theme }) => ({
    textTransform: 'none',
    background: theme.palette.neutral[2],
    borderRadius: 100,
    border: `1px solid ${theme.palette.neutral[4]}`,
    color: theme.palette.neutral[8],
    height: 32,
    alignItems: 'center',
    verticalAlign: 'center',
    display: 'flex',
    gap: 5,
    paddingLeft: 12,
}));

export type CustomerAutocompleteCustomApiProps = {
    value?: CustomerSearchItemDto | string | null;
    initiallySelected?: CustomerSearchItemDto | null;
    onSelect: (customer: CustomerSearchItemDto) => void;
    onOpenCreatePopup: (() => void) | null;
    searchCustomers: (query: string) => Promise<CustomerSearchItemDto[]>;
    getFrequentCustomers: () => Promise<CustomerSearchItemDto[]>;
    getCustomerById: (id: string) => Promise<CustomerSearchItemDto | null>;
    queryKeyPrefix: string;
    className?: string;
    disabled?: boolean;
    disableCreateButton?: boolean;
    noOptionsText?: string;
};

type HeaderData = {
    onOpenCreatePopup?: () => void;
    showFrequentCustomersLabel: boolean;
    hideCreateButton: boolean;
    disableCreateButton: boolean;
};

const PaperComponent = forwardRef(
    (
        {
            children,
            _headerData,
            ...props
        }: React.HTMLAttributes<HTMLDivElement> & { _headerData?: HeaderData },
        _ref
    ) => {
        const { t } = useAppTranslation();

        if (!_headerData) return null;

        const {
            onOpenCreatePopup,
            showFrequentCustomersLabel,
            disableCreateButton,
            hideCreateButton,
        } = _headerData;

        return (
            <Paper
                {...props}
                sx={{
                    '& .MuiListItem-root': {
                        paddingBottom: '0 !important',
                    },
                    '& .MuiAutocomplete-listbox': {
                        paddingTop: '8px',
                    },
                }}
            >
                <HeaderItem
                    onMouseDown={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                    }}
                >
                    {!hideCreateButton && (
                        <AddCustomButton
                            size="small"
                            disabled={disableCreateButton}
                            onClick={() =>
                                onOpenCreatePopup && !disableCreateButton
                                    ? onOpenCreatePopup()
                                    : undefined
                            }
                        >
                            <span>{t('appointments.step1.createNewCustomer')}</span>
                            <PlusIcon fill={Colors.Neutral6} />
                        </AddCustomButton>
                    )}
                    {showFrequentCustomersLabel && (
                        <FrequentCustomerLabel style={{ marginTop: disableCreateButton ? 0 : 16 }}>
                            {t('appointments.step1.frequentCustomer')}
                        </FrequentCustomerLabel>
                    )}
                </HeaderItem>

                {children}
            </Paper>
        );
    }
);

export default function CustomerAutocompleteCustomApi({
    onOpenCreatePopup,
    value: valueOrId,
    initiallySelected,
    onSelect,
    className,
    searchCustomers,
    getFrequentCustomers,
    queryKeyPrefix,
    disabled,
    getCustomerById,
    disableCreateButton = false,
    noOptionsText,
}: CustomerAutocompleteCustomApiProps) {
    const styles = useCustomerAutocompleteStyles();
    const commonStyles = useAutoCompleteCommonStyles();
    const { t } = useAppTranslation();
    const permissions = useAppSelector(selectUserPermission);
    const isEnterprise = useIsEnterpriseRoute();
    const canCreateCustomer = permissions.allowEditCustomers || isEnterprise;

    const [open, setOpen] = useState(false);
    const [[searchQuery, _doSearch], setSearchQuery] = useState(['', true]);
    const normalizedQuery = useMemo(() => searchQuery.trim(), [searchQuery]);
    const [[debouncedQuery, doSearch]] = useDebounce<[string, boolean]>(
        [normalizedQuery, _doSearch],
        500,
        {
            equalityFn(left, right) {
                return left[0] === right[0] && left[1] === right[1];
            },
        }
    );

    const { customers: __customers, canSearchUsingQuery } = useCustomersSearch(
        searchCustomers,
        getFrequentCustomers,
        debouncedQuery,
        queryKeyPrefix,
        !disabled && doSearch
    );

    const customerById = useCustomerById(valueOrId, getCustomerById);
    const [value, setValue] = useState<CustomerSearchItemDto | null>(initiallySelected ?? null);

    useEffect(() => {
        if (customerById) {
            setValue(customerById);
        } else if (valueOrId === null) {
            setValue(null);
        }
    }, [customerById, valueOrId]);

    const customers = useMemo(() => {
        if (value && !__customers.some((x) => x.id === value.id)) {
            return [value, ...__customers];
        }
        return __customers;
    }, [__customers, value]);

    const customersOptions = useMemo(() => {
        if (value && customers.findIndex((c) => c.id === value.id) !== -1) {
            return [value, ...customers.filter((c) => c.id !== value.id)];
        }

        return customers;
    }, [customers, value]);

    const onEnterKeyDown = (event: any) => {
        if (event.key === 'Enter') {
            if (customersOptions[1]) {
                onSelect(customersOptions[1]);
            }
            event.target.blur();
        }
    };

    const headerData: HeaderData = {
        onOpenCreatePopup: onOpenCreatePopup ?? undefined,
        showFrequentCustomersLabel: !canSearchUsingQuery,
        hideCreateButton: disableCreateButton,
        disableCreateButton: !canCreateCustomer,
    };

    return (
        <Autocomplete<CustomerSearchItemDto>
            id="customer-autocomplete"
            autoComplete
            open={open}
            // at this moment this component cannot be cleared because
            // onSelect does not support passing null
            clearIcon={null}
            noOptionsText={noOptionsText}
            options={customersOptions}
            inputValue={searchQuery}
            value={value ?? null}
            className={className}
            disabled={disabled}
            classes={{ paper: commonStyles.paperAutoComplete }}
            slotProps={{
                paper: {
                    _headerData: headerData,
                } as PaperProps, // shut up TS, we need to pass this to PaperComponent somehow
            }}
            PaperComponent={PaperComponent}
            renderInput={useCallback(
                (params) => (
                    <MuiTextField
                        {...params}
                        fullWidth
                        placeholder={t(
                            'appointments.step1.searchAndSelectCustomerByNameMobileOrPlates'
                        )}
                        InputProps={{
                            ...params.InputProps,
                            type: 'text',
                            className: commonStyles.root,
                            classes: {
                                focused: commonStyles.inputFocused,
                            },
                        }}
                    />
                ),
                [commonStyles, t]
            )}
            filterOptions={(options) => options}
            renderOption={(props, option) => {
                return (
                    <ListItemButton disableRipple component="li" {...props} className={styles.item}>
                        {renderCustomerItem(option, t)}
                    </ListItemButton>
                );
            }}
            isOptionEqualToValue={(option, value) => option?.id === value?.id}
            getOptionLabel={(o) => (o === null ? '' : customerToQuerySearch(o) || o.id)}
            getOptionKey={(o) => o.id}
            onChange={(_e, value) => {
                if (typeof value === 'string' || value === null) return;
                setSearchQuery([value ? customerToQuerySearch(value) : '', false]);
                onSelect(value);
            }}
            onInputChange={(_e, value, reason) => {
                setSearchQuery([value, reason === 'input']);
            }}
            onOpen={() => {
                setOpen(true);
            }}
            onClose={() => setOpen(false)}
            onKeyDown={onEnterKeyDown}
        />
    );
}

function useCustomerById(
    customerOrId: string | CustomerSearchItemDto | undefined | null,
    getCustomerById: (id: string) => Promise<CustomerSearchItemDto | null>
): CustomerSearchItemDto | undefined {
    const [state, setState] = useState<CustomerSearchItemDto>();

    useEffect(() => {
        if (typeof customerOrId === 'string') {
            setState(undefined);
            getCustomerById(customerOrId).then((c) => {
                if (c) setState(c);
            });
        } else if (typeof customerOrId === 'undefined' || customerOrId === null) {
            setState(undefined);
        } else if (typeof customerOrId === 'object' && customerOrId !== null) {
            setState(customerOrId);
        }
    }, [customerOrId, getCustomerById]);

    return state;
}
