import { IconButton, InputAdornment } from '@mui/material';
import Grid from '@mui/material/Grid';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { TextFormField } from 'common/components/Inputs';
import CharacterIndicator from 'common/components/Inputs/CharacterIndicator';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';

type NoteEditorProps = {
    text?: string;
    placeholder?: string;
    onEdit: (value: string) => void;
    onCancel: () => void;
};

const NoteEditor = ({ text: initialText = '', placeholder, onEdit, onCancel }: NoteEditorProps) => {
    const [text, setText] = useState<string>(initialText);

    const handleSave = () => {
        if (text.trim().length) {
            onEdit && onEdit(text);
        }
    };

    return (
        <Grid container spacing={1} display={'flex'} alignItems={'center'}>
            <Grid item xs={11}>
                <TextFormField
                    name="note-editor"
                    maxLength={100}
                    placeholder={placeholder}
                    value={text}
                    onChange={(event) => {
                        if (event.target.value.length <= 100) {
                            setText(event.target.value);
                        }
                    }}
                    onEnterPress={() => {
                        handleSave();
                    }}
                    enableEnterComplete
                    multiline
                    endAdornment={
                        <InputAdornment position="end">
                            <IconButton
                                size="small"
                                onClick={() => {
                                    handleSave();
                                }}
                            >
                                <CheckIcon size={IconSize.M} />
                            </IconButton>
                            <CharacterIndicator charactersCount={text.length} />
                        </InputAdornment>
                    }
                />
            </Grid>
            <Grid item xs={1}>
                <Button
                    color="#899198"
                    cmosVariant={'typography'}
                    Icon={CloseIcon}
                    label=""
                    onClick={onCancel}
                />
            </Grid>
        </Grid>
    );
};

export default NoteEditor;
