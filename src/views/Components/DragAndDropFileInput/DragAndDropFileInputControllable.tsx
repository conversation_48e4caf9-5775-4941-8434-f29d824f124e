import { Box, CircularProgress, SxProps, Theme, Typography, styled } from '@mui/material';
import { CloudUploadIcon2 } from 'common/components/Icons/CloudUploadIcon2';
import { ExcelIcon } from 'common/components/Icons/ExcelIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import React, { useEffect, useRef, useState } from 'react';
import { formatBytes } from 'utils/Files';

export type FileController = {
    set: (file: File | undefined) => void;
};
export type DragAndDropFileInputControllableProps = {
    defaultFile?: {
        name: string;
        size: number;
    };
    acceptedFormats?: AcceptedFormat[];
    onFileSet: (file: File | null) => void;
    file: File | null;
    icon?: (hasFile: boolean) => React.ReactNode;
    additionalContent?: React.ReactNode;
    disabled?: boolean;
    showLoader?: boolean;
    label?: string;
    controllerRef?: (fileController: FileController) => void;
    sx?: SxProps<Theme>;
};

const AcceptedFormatMappings = {
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    csv: 'text/csv',
};

export type AcceptedFormat = keyof typeof AcceptedFormatMappings;

const InputStyled = styled('input')({
    display: 'none',
});

const getAcceptString = (acceptedFormats?: AcceptedFormat[]): string => {
    if (acceptedFormats && acceptedFormats.length > 0) {
        return acceptedFormats
            .map((acceptedFormat) => {
                return AcceptedFormatMappings[acceptedFormat];
            })
            .join(', ');
    } else {
        return '';
    }
};

const matchesFormat = (format: string, allowedFormats: AcceptedFormat[] | undefined): boolean => {
    if (allowedFormats === undefined) return true;
    return allowedFormats.map((x) => AcceptedFormatMappings[x]).includes(format);
};

export default function DragAndDropFileInputControllable({
    onFileSet,
    acceptedFormats,
    icon,
    defaultFile,
    additionalContent,
    disabled,
    label,
    showLoader,
    file,
    sx,
}: DragAndDropFileInputControllableProps) {
    const { t } = useAppTranslation();

    const fileInput = useRef<HTMLInputElement | null>(null);
    const [isDraggingOver, setIsDraggingOver] = useState(false);

    const iconContainerRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        const preventDefault = (e: DragEvent) => {
            e.preventDefault();
        };

        // prevent browser from opening file in new tab
        // when file is dragged
        document.addEventListener('dragover', preventDefault);
        document.addEventListener('drop', preventDefault);

        return () => {
            document.removeEventListener('dragover', preventDefault);
            document.removeEventListener('drop', preventDefault);
        };
    }, []);

    const handleDragAreaClick = (event: React.MouseEvent<HTMLInputElement>) => {
        fileInput.current?.click();
    };

    const handleInputClick = (event: React.MouseEvent<HTMLInputElement>) => {
        if (event.target instanceof HTMLInputElement) {
            event.target.value = '';
        }
    };

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        event.preventDefault();
        if (event.target.files && event.target.files[0]) {
            const file = event.target.files[0];
            onFileSet(file);
        } else {
            onFileSet(null);
        }
    };

    // https://stackoverflow.com/questions/7110353/html5-dragleave-fired-when-hovering-a-child-element
    const dragCounter = useRef(0);

    const handleDragOver = (event: React.DragEvent<HTMLInputElement>) => {
        event.preventDefault();
        event.stopPropagation();
    };

    const handleDragEnter = (event: React.DragEvent<HTMLInputElement>) => {
        event.preventDefault();
        event.stopPropagation();
        dragCounter.current++;
        if (dragCounter.current === 1) setIsDraggingOver(true);
    };

    const handleDragLeave = (event: React.DragEvent<HTMLInputElement>) => {
        event.preventDefault();
        event.stopPropagation();
        dragCounter.current--;
        if (dragCounter.current === 0) setIsDraggingOver(false);
    };

    const handleDragAreaDrop = (event: React.DragEvent<HTMLInputElement>) => {
        event.preventDefault();
        event.stopPropagation();
        setIsDraggingOver(false);
        dragCounter.current = 0;
        if (event.dataTransfer.files && event.dataTransfer.files[0]) {
            const file = event.dataTransfer.files[0];

            if (matchesFormat(file.type, acceptedFormats)) {
                onFileSet(file);
            } else {
                if (iconContainerRef.current && iconContainerRef.current.animate) {
                    // shake the icon because we can and so we shall
                    iconContainerRef.current.style.transformOrigin = 'bottom center';
                    iconContainerRef.current
                        .animate(
                            [
                                { transform: 'translateX(-5px) rotate(-10deg)' },
                                { transform: 'translateX(5px) rotate(10deg)' },
                                { transform: 'translateX(-5px) rotate(-10deg)' },
                                { transform: 'translateX(5px) rotate(10deg)' },
                                { transform: 'translateX(0)  rotate(0deg)' },
                            ],
                            { duration: 500, easing: 'ease-in-out' }
                        )
                        .addEventListener('finish', () => {
                            if (iconContainerRef.current) {
                                iconContainerRef.current.style.removeProperty('transform-origin');
                            }
                        });
                }
            }
        } else {
            onFileSet(null);
        }
    };

    return (
        <DivRoot sx={sx}>
            <DivDragArea
                className={isDraggingOver ? 'draggingOver' : undefined}
                onClick={handleDragAreaClick}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDragOver={handleDragOver}
                onDrop={handleDragAreaDrop}
            >
                <DivIconContainer
                    ref={iconContainerRef}
                    className={isDraggingOver ? 'draggingOver' : undefined}
                >
                    {showLoader ? (
                        <CircularProgress size={30} />
                    ) : (
                        <CloudUploadIcon2 size={IconSize.XL} fill="currentColor" />
                    )}
                </DivIconContainer>

                <SpanLabel>{label ?? t('components.dragAndDropFileInput.title')}</SpanLabel>

                <InputStyled
                    disabled={disabled}
                    ref={fileInput}
                    onClick={handleInputClick}
                    onChange={handleFileInputChange}
                    type="file"
                    multiple={false}
                    accept={getAcceptString(acceptedFormats)}
                    style={{ display: 'none' }}
                />
            </DivDragArea>
            <DivContent>
                <DivFileInfoContainer>
                    <div>{(icon ?? defaultIcon)(!!file || !!defaultFile)}</div>

                    <Box display="flex" flexDirection="column">
                        {file != null || !!defaultFile ? (
                            <Typography variant="h5Roboto" color="primary">
                                {file ? file.name : defaultFile?.name}
                            </Typography>
                        ) : (
                            <Typography variant="h5Roboto" color="neutral.7">
                                {t('components.dragAndDropFileInput.noFile')}
                            </Typography>
                        )}
                        <Typography variant="h7Roboto" color="neutral.7">
                            {t('components.dragAndDropFileInput.size')}:{' '}
                            {file != null || defaultFile
                                ? formatBytes(file ? file.size : defaultFile?.size ?? 0)
                                : '0 MB'}
                        </Typography>
                    </Box>
                </DivFileInfoContainer>

                {additionalContent}
            </DivContent>
        </DivRoot>
    );
}

const defaultIcon: (hasFile: boolean) => React.ReactNode = (hasFile) => (
    <ExcelIcon size={37} fill={!hasFile ? Colors.Neutral5 : Colors.CM1} />
);

const DivDragArea = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    paddingBottom: 16,
    justifyContent: 'center',
    alignItems: 'center',
    width: 'clamp(300px, 400px, 558px)',
    height: 150,
    boxSizing: 'border-box',
    background: rgba(theme.palette.primary.light, 0.1),
    border: '2px dashed ' + theme.palette.primary.light,
    borderRadius: 15,
    flex: '1 1 auto',
    cursor: 'pointer',
    transition: 'all .2s',

    '&.disabled': {
        borderColor: theme.palette.neutral[7],
        background: rgba(theme.palette.neutral[7], 0.1),
    },

    '&.draggingOver': {
        background: rgba(theme.palette.primary.light, 0.05),
    },
}));

const DivContent = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
});

const DivFileInfoContainer = styled('div')({
    paddingTop: 3,
    display: 'flex',
    gap: 10,
});

const DivRoot = styled('div')(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: 'auto 1fr',
    gap: 32,

    [theme.breakpoints.down('md')]: {
        gridTemplateColumns: '1fr',
    },
}));

const SpanLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.primary.main,
    fontWeight: 'normal',
}));

const DivIconContainer = styled('div')({
    height: 64,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    color: 'var(--cm3)',
    transition: 'all .2s',

    '&.draggingOver': {
        transform: 'scale(1.1)',
        color: 'var(--cm1)',
    },
});
