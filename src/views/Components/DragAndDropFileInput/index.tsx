import { useCallback, useState } from 'react';
import DragAndDropFileInputControllable, {
    DragAndDropFileInputControllableProps,
} from './DragAndDropFileInputControllable';

export type FileController = {
    set: (file: File | undefined) => void;
};
export type DragAndDropFileInputProps = Omit<DragAndDropFileInputControllableProps, 'file'>;

export default function DragAndDropFileInput({ onFileSet, ...props }: DragAndDropFileInputProps) {
    const [file, setFile] = useState<File | null>(null);

    return (
        <DragAndDropFileInputControllable
            file={file}
            onFileSet={useCallback(
                (f) => {
                    setFile(f);
                    onFileSet(f);
                },
                [onFileSet]
            )}
            {...props}
        />
    );
}
