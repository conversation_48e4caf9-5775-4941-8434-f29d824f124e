import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        color: theme.palette.neutral[5],
    },
    box: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',

        maxWidth: 174,
        padding: 8,
        zIndex: 1500,

        backgroundColor: theme.palette.neutral[1],

        ...theme.typography.subtitle2,
        color: theme.palette.neutral[5],

        borderRadius: 5,
        boxShadow: '0px 4px 4px rgba(166, 166, 166, 0.25)',
    },
    icon: {
        width: 'max-content',
        height: 'max-content',
        color: theme.palette.neutral[6],

        '&:hover': {
            color: theme.palette.primary.main,
        },
    },
    arrowBottom: {
        position: 'absolute',
        bottom: -4,
        width: 8,
        height: 8,

        backgroundColor: theme.palette.neutral[1],
        transform: 'rotate(45deg)',
    },
    arrowLeft: {
        position: 'absolute',
        left: -4,
        width: 8,
        height: 8,

        backgroundColor: theme.palette.neutral[1],
        transform: 'rotate(45deg)',
    },
}));
export default useStyles;
