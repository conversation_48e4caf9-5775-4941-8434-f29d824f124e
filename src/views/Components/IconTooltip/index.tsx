import ArrowTooltip from 'common/components/Tooltip';
import useStyles from './css';
export type IconTooltipProps = {
    icon: JSX.Element;
    tooltipText?: string;
    children?: React.ReactNode;
};

const IconTooltip = ({ icon, tooltipText, children }: IconTooltipProps) => {
    const classes = useStyles();

    return (
        <ArrowTooltip position="top" content={<div>{children ?? tooltipText}</div>}>
            <div className={classes.icon}>{icon}</div>
        </ArrowTooltip>
    );
};

export default IconTooltip;
