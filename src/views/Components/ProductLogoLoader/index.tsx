import { styled } from '@mui/material';
import ProductLogo from '../ProductLogo';

const Container = styled('div')({
    position: 'fixed',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    background: 'white',
});

export default function ProductLogoLoader({ children }: React.PropsWithChildren<{}>) {
    return (
        <Container>
            <ProductLogo scale={2.35} fill="var(--cm1)" />
            {children}
        </Container>
    );
}
