import { SelectChangeEvent } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { OrderTypesApi } from 'api/orders';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo, useState } from 'react';

type OrderTypePickerMultipleProps = {
    orderTypeIds: string[];
    onChange: (orderTypeIds: string[]) => void;
    placeholder?: string;
    disabled?: boolean;
};

const ALL_TYPES_MAGIC_VALUE = 'allOrderTypes';

export default function OrderTypePickerMultiple({
    orderTypeIds,
    onChange,
    placeholder,
    disabled,
}: OrderTypePickerMultipleProps) {
    const { t } = useAppTranslation();

    const { data, isFetching } = useQuery(['orders', 'order-types'], OrderTypesApi.getList, {
        staleTime: 1000,
        cacheTime: Infinity,
    });

    const orderTypes = data ?? [];

    const [selectedIds, setSelectedIds] = useState<string[]>(orderTypeIds);
    const [isChanged, setIsChanged] = useState<boolean>(false);

    const handleChange = useCallback(
        (e: SelectChangeEvent<string[]>) => {
            if (typeof e.target.value !== 'object') {
                // basically never going to happen (sanity check)
                return;
            }

            if (e.target.value.includes(ALL_TYPES_MAGIC_VALUE)) {
                if (e.target.value[e.target.value.length - 1] === ALL_TYPES_MAGIC_VALUE) {
                    // user clicked "All order types"
                    setSelectedIds([]);
                    setIsChanged(true);
                } else {
                    // use clicked something else
                    const value = e.target.value
                        .filter((x) => x !== ALL_TYPES_MAGIC_VALUE)
                        .toSorted();
                    setSelectedIds(value);
                    setIsChanged(true);
                }
            } else {
                const value = e.target.value.toSorted();
                setSelectedIds(value);
                setIsChanged(true);
            }
        },
        [setSelectedIds]
    );

    const isAllTypesSelected = selectedIds.length === 0;
    const adjustedSelectedIds = useMemo(() => {
        if (selectedIds.length === 0) {
            //We always treat none selected as all selected.
            return [ALL_TYPES_MAGIC_VALUE];
        } else {
            return selectedIds;
        }
    }, [selectedIds]);

    return (
        <SSelectInput<string[]>
            value={adjustedSelectedIds}
            multiple
            disabled={disabled || isFetching}
            onChange={handleChange}
            onClose={() => {
                if (isChanged) onChange(selectedIds);
                setIsChanged(false);
            }}
            renderValue={(value) => {
                const ids = value as string[];
                if (ids.length === 0) return placeholder;
                return ids
                    .map((x) => {
                        if (x === ALL_TYPES_MAGIC_VALUE) return t('orderTypePicker.allOrderTypes');
                        const orderType = orderTypes.find((t) => t.key === x);
                        return orderType ? orderType.name : '';
                    })
                    .join(', ');
            }}
        >
            {
                <SMenuItem value={ALL_TYPES_MAGIC_VALUE}>
                    {isAllTypesSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    {t('orderTypePicker.allOrderTypes')}
                </SMenuItem>
            }
            {orderTypes.map((orderType) => (
                <SMenuItem key={orderType.key} value={orderType.key}>
                    {selectedIds.includes(orderType.key) ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    {orderType.name}
                </SMenuItem>
            ))}
        </SSelectInput>
    );
}
