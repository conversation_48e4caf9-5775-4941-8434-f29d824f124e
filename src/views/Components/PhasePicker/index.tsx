import { useQuery } from '@tanstack/react-query';
import WpPhasesApi from 'api/workshopPlanner/phases';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';

export type PhasePickerProps = {
    phaseId: number | null;
    onChange: (phaseId: number) => void;
    className?: string;
    name?: string;
    hidePhaseIds?: number[];
} & Pick<DropdownProps<number>, 'onOpen' | 'disabled' | 'placeholder' | 'cmosVariant'>;

export default function PhasePicker({
    phaseId,
    onChange,
    className,
    name,
    placeholder,
    hidePhaseIds,
    ...props
}: PhasePickerProps) {
    const { data } = useQuery(['wp', 'phases'], WpPhasesApi.getPhases, {
        staleTime: 1000,
        cacheTime: Infinity,
    });
    const { t } = useAppTranslation();

    const options = useMemo(
        () =>
            (data ?? [])
                .filter((v) => !hidePhaseIds || !hidePhaseIds!.some((hpid) => v.id == hpid))
                .map((v) => ({
                    value: v.id,
                    label: ['noPhase', 'closedOrder'].includes(v.name)
                        ? t(`orderDetails.${v.name}`)
                        : v.name,
                })),
        [data, t]
    );
    const selectedOption = useMemo(
        () => options.find((o) => o.value === phaseId),
        [options, phaseId]
    );

    return (
        <Dropdown
            name={name}
            placeholder={placeholder || t('workshopPlanner.orderPopup.selectPhasePlaceholder')}
            options={options}
            multiple={false}
            value={selectedOption}
            onChange={(o) => {
                if (o) onChange(o.value);
            }}
            slotProps={{
                inputWrapper: { className },
            }}
            {...props}
        />
    );
}
