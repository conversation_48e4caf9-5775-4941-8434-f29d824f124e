import { styled } from '@mui/material';

type ErrorJumbotronProps = {
    title: string;
    children?: React.ReactNode;
};

const ErrorJumbotronSection = styled('section')(({ theme }) => ({
    '& > header': {
        ...theme.typography.h4Inter,
    },
    '& > p': {},
    padding: '20px',
}));

export default function ErrorJumbotron({ title, children }: ErrorJumbotronProps) {
    return (
        <ErrorJumbotronSection>
            <header>{title}</header>
            <p>{children}</p>
        </ErrorJumbotronSection>
    );
}
