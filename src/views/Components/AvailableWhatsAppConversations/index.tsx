import { Divider, styled, useTheme } from '@mui/material';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { WhatsappIcon } from 'common/components/Icons/WhatsappIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';

type AvailableWhatsAppConversationsProps = {
    available: number;
    maximum: number;
    marketing: number;
    utilityService: number;
};

export const AvailableWhatsAppConversations = ({
    available,
    maximum,
    marketing,
    utilityService,
}: AvailableWhatsAppConversationsProps) => {
    const theme = useTheme();
    const [open, setOpen] = useState(false);

    const percentage = (available / maximum) * 100;

    const { t } = useAppTranslation();

    return (
        <>
            <DivMainContainer role="button" onClick={() => setOpen(!open)}>
                <DivIconContainer percentage={percentage}>
                    <WhatsappIcon fill={theme.palette.success.main} size={24} />
                </DivIconContainer>
                <div>
                    {`${available}/${maximum} ` + t('chatNotifications.conversationsAvailable')}
                </div>
                {open ? (
                    <UpIcon fill="var(--grey5)" size={32} />
                ) : (
                    <DownIcon fill="var(--grey5)" size={32} />
                )}
            </DivMainContainer>
            {open && (
                <DivSecondaryContainer>
                    <Divider style={{ width: '100%' }} />
                    <DivSecondaryText>
                        {t('chatNotifications.marketingConversations') + ` ${marketing}`}
                    </DivSecondaryText>
                    <Divider style={{ width: '90%' }} />
                    <DivSecondaryText>
                        {t('chatNotifications.utilityServiceConversations') + ` ${utilityService}`}
                    </DivSecondaryText>
                </DivSecondaryContainer>
            )}
        </>
    );
};

const DivMainContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: 12,
    padding: 10,
    cursor: 'pointer',

    ...theme.typography.h5Roboto,
    color: 'var(--grey5)',

    '&:hover': {
        backgroundColor: 'var(--neutral2)',
        color: 'var(--neutral8)',
    },
}));

const DivIconContainer = styled('div')<{ percentage: number }>(({ theme, percentage }) => ({
    color: theme.palette.success.main,
    borderRadius: '50%',
    width: 30,
    height: 30,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: `radial-gradient(closest-side, white 89%, transparent 95% 100%), conic-gradient(${theme.palette.success.main} ${percentage}%, ${theme.palette.neutral[2]} 0)`,
}));

const DivSecondaryContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 12,
    paddingBottom: 12,
});

const DivSecondaryText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: 'var(--grey5)',
    fontWeight: 400,
}));
