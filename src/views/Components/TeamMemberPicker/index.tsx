import { CircleIconWithBorder } from 'common/components/Icons';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { OptionStyle } from 'common/styles/OptionStyle';
import { UserShortDto } from 'datacontracts/UserShortDto';
import { useCallback, useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectTeamMembers } from 'store/slices/teamMembers/selectors';
import ensureTeamMembers from 'store/slices/teamMembers/thunks/ensureTeamMembers';

export type TeamMemberPickerProps = Omit<
    DropdownProps<string>,
    'value' | 'onChange' | 'options' | 'theme'
> & {
    userId: string | null;
    onChange: (user: UserShortDto | null) => void;
    optionsFilter?: (dto: UserShortDto) => boolean;
};

export default function TeamMemberPicker({
    userId,
    onChange,
    optionsFilter,
    ...props
}: TeamMemberPickerProps) {
    const dispatch = useAppDispatch();
    const teamMembers = useAppSelector(selectTeamMembers);

    useEffect(() => {
        dispatch(ensureTeamMembers());
    }, [dispatch]);

    const teamMembersOptions: OptionData<string>[] = useMemo(() => {
        const defaultOptionsFilter = () => true;
        const filterFunc = optionsFilter ?? defaultOptionsFilter;

        return teamMembers.filter(filterFunc).map((v) => ({
            value: v.userKey!,
            label: `${v.initials} - ${v.displayName}`,
            icon: CircleIconWithBorder,
            color: v.color,
        }));
    }, [teamMembers, optionsFilter]);

    const selectedOption = useMemo(
        () => teamMembersOptions.find((m) => m.value === userId) ?? null,
        [userId, teamMembersOptions]
    );

    const handleChange = useCallback(
        (option: OptionData<string> | null) => {
            if (option === null) return;
            onChange(teamMembers.find((t) => t.userKey === option.value) ?? null);
        },
        [onChange, teamMembers]
    );

    return (
        <Dropdown
            cmosVariant="grey"
            optionStyle={OptionStyle.icons}
            options={teamMembersOptions}
            value={selectedOption}
            onChange={handleChange}
            {...props}
        />
    );
}
