import { ErrorOutline } from '@mui/icons-material';
import { Box, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import icon from '../../../assets/images/connecting_call.png';
import { Button } from '../../../common/components/Button';
import { CloseIcon } from '../../../common/components/Icons/CloseIcon';
import { Modal } from '../../../common/components/Modal';
import { Colors } from '../../../common/styles/Colors';
import animationCssModule from './animation.module.css';

interface ConnectingCallProps {
    open: boolean;
    onClose: () => void;
    isLoading?: boolean;
    callFailed?: boolean;
    errorMessage?: string;
}

const Title = styled('div')(({ theme }) => ({
    ...theme.typography.h4Inter,
    color: theme.palette.neutral[9],
    maxWidth: 100,
    textAlign: 'center',
}));

const Caption = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    textAlign: 'center',
}));

const ConnectingCallContent = styled('div')({
    width: 282,
    paddingTop: 32,
    paddingLeft: 30,
    paddingRight: 30,
    paddingBottom: 39,
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
});

const ConnectingCallImage = styled('div')({
    backgroundImage: `url(${icon})`,
    height: 50,
    width: 50,
    backgroundRepeat: 'no-repeat',
    marginBottom: 12,
    position: 'relative',
});

const Spinner = styled('div')(({ theme }) => ({
    position: 'absolute',
    inset: 0,
    borderRadius: 100,
    border: '2px solid var(--success)',
    clipPath:
        'polygon(50% 50%, 0% 0%, 50% 0%, 100% 0%, 100% 50%, 100% 50%, 100% 50%, 100% 50%, 100% 50%)',
}));

const CloseContainer = styled('div')({
    position: 'absolute',
    right: 16,
    top: 16,
});

const Line = styled('div')({
    borderBottomStyle: 'solid',
    borderWidth: 1,
    borderColor: '#efefef',
    marginTop: 10,
    marginBottom: 12,
    width: '100%',
});

const OkBtnContainer = styled('div')({
    marginTop: 20,
    width: 182,
});

export const ConnectingCall = ({
    open,
    isLoading = false,
    onClose,
    errorMessage,
    callFailed = false,
}: ConnectingCallProps) => {
    const { t } = useAppTranslation();

    return (
        <Modal open={open}>
            <ConnectingCallContent>
                <CloseContainer>
                    <Button
                        cmosVariant={'typography'}
                        iconPosition="right"
                        color={Colors.Neutral3}
                        Icon={CloseIcon}
                        onClick={onClose}
                    />
                </CloseContainer>
                {callFailed ? (
                    <Box
                        sx={{
                            width: 50,
                            height: 50,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginBottom: 12,
                        }}
                    >
                        <ErrorOutline color="error" fontSize="large" />
                    </Box>
                ) : (
                    <ConnectingCallImage>
                        {isLoading && <Spinner className={animationCssModule.rotate} />}
                    </ConnectingCallImage>
                )}

                <Title>
                    {callFailed ? t('toasters.errorOccurred') : t('orders.calls.dialing')}
                </Title>
                <Line />
                <Caption>
                    {callFailed
                        ? errorMessage || 'Unknown error'
                        : t('orders.calls.yourPhoneWillRing')}
                </Caption>
                <OkBtnContainer>
                    <Button
                        disabled={isLoading}
                        blockMode
                        cmosVariant={'filled'}
                        color={Colors.Success}
                        onClick={onClose}
                        label={'Ok'}
                    />
                </OkBtnContainer>
            </ConnectingCallContent>
        </Modal>
    );
};

export default ConnectingCall;
