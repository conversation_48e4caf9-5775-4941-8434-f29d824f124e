import { styled } from '@mui/material';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

export type PageLayoutProps = {
    marginTop?: boolean;
};

/**
 * PageLayout is a default layout for a page with medium margin on left and right.
 */
export const PageLayout = styled('div')<PageLayoutProps>(({ marginTop }) => ({
    '--margin-top': marginTop ? '22px' : '0px',
    '--margin-bottom': '30px',
    marginTop: 'var(--margin-top)',
    boxSizing: 'border-box',
    marginLeft: 'auto',
    marginRight: 'auto',
    marginBottom: 20,
    width: '83.33vw',
}));

/**
 * WidePageLayout is a layout for a wide page with relatively small margin on left and right.
 */
export const WidePageLayout = styled(PageLayout)({
    width: `${(11 / 12) * 100}%`,
});

/**
 * PageContent is a container for page's content with white background and rounded borders.
 * This component can be used to make page's content stand out.
 */
export const PageContent = styled('div', {
    shouldForwardProp: (prop) => prop !== 'paddedX' && prop !== 'paddedY',
})<{ paddedX?: boolean; paddedY?: boolean }>(({ paddedX, paddedY }) => ({
    ...scrollbarStyle({ widerHorizontal: true }),
    boxSizing: 'border-box',
    width: '100%',
    borderRadius: 12,
    border: 'solid 1px #dbdcdd',
    backgroundColor: '#ffffff',
    padding: `${paddedY ? '50px' : '0px'} ${paddedX ? '50px' : '0px'}`,
    overflow: 'hidden',
}));

export default PageContent;
