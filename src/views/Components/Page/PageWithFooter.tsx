import { styled } from '@mui/material';
import { ComponentProps } from 'react';
import PageContent from '.';

export type PageWithFooterProps = ComponentProps<typeof PageContent> & {
    footer: React.ReactNode;
};

const StyledPageContent = styled(PageContent)({
    height: 'initial',
    flexGrow: 1,
});

const Root = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    flexGrow: 1,
});

export default function PageContentWithFooter({ footer, ...props }: PageWithFooterProps) {
    return (
        <Root>
            <StyledPageContent {...props} />
            <div>{footer}</div>
        </Root>
    );
}
