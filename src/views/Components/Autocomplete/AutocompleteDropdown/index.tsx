import { ExpandMore } from '@mui/icons-material';
import {
    Autocomplete,
    AutocompleteProps,
    Box,
    CircularProgress,
    ListItemButton,
    outlinedInputClasses,
    styled,
    TextField,
} from '@mui/material';
import clsx from 'clsx';
import { IconProps } from 'common/components/Icons/Icon';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { FocusEvent, FormEvent, ReactNode, Ref, useCallback, useState } from 'react';
import styles from './styles.module.css';

export type AutocompleteDropdownProps<T> = {
    label: ReactNode;
    getOptionKey: (value: T | null) => string | number;
    onChange: (value: T | string) => void;
    hideIcons?: boolean;
    creatable?: boolean;
    grouped?: boolean;
    inputRef?: Ref<HTMLInputElement>;
    placeholder?: string;
} & Omit<
    AutocompleteProps<OptionData<T>, false, false, false, 'div'>,
    | 'autoComplete'
    | 'selectOnFocus'
    | 'clearOnBlur'
    | 'openOnFocus'
    | 'clearIcon'
    | 'renderInput'
    | 'filterOptions'
    | 'renderGroup'
    | 'popupIcon'
    | 'isOptionEqualToValue'
    | 'getOptionLabel'
    | 'getOptionKey'
    | 'groupBy'
    | 'onChange'
>;

export type OptionData<T> = {
    label: string;
    value: T | null;
    icon?: React.ComponentType<IconProps>;
    color?: string;
    group?: string;
};

// NOTE (AK) alternative version of Dropdown
// instead of Select/Creatable the Autocomplete is used here
export default function AutocompleteDropdown<T>({
    label,
    hideIcons,
    creatable,
    grouped,
    inputRef,
    placeholder,
    getOptionKey,
    onChange,

    value,
    onInput,
    onBlur,
    classes,
    slotProps,
    loading,

    ...props
}: AutocompleteDropdownProps<T>) {
    const { t } = useAppTranslation();
    const [isEdit, setIsEdit] = useState(false);

    const {
        paper: paperClasses,
        root: rootClasses,
        popupIndicator: popupIndicatorClasses,
        ...otherClasses
    } = classes ?? { paper: '', root: '', popupIndicator: '' };

    const { paper: paperProps, ...otherSlotProps } = slotProps ?? { paper: {} };

    const handleInput = useCallback(
        (e: FormEvent<HTMLDivElement>) => {
            setIsEdit(true);
            onInput?.(e);
        },
        [onInput]
    );

    const handleChange = useCallback(
        (option: OptionData<T> | null) => {
            if (option === null || option.label === value?.label) return;

            if (option.value) {
                onChange(option.value);
            } else if (creatable) {
                const regExp = `${t('commonLabels.create')} "(.*)"`;
                const match = option.label.match(regExp);

                if (!match) {
                    return;
                }

                const label = match[1];
                onChange(label);
            }
        },
        [onChange, creatable, value, t]
    );

    const handleBlur = useCallback(
        (e: FocusEvent<HTMLDivElement, Element>) => {
            setIsEdit(false);
            onBlur?.(e);
        },
        [onBlur]
    );

    return (
        <InputWrapper label={label} disabled={props.disabled}>
            <Autocomplete<OptionData<T>>
                autoComplete
                selectOnFocus
                clearOnBlur
                openOnFocus
                // at this moment this component cannot be cleared because
                // onSelect does not support passing null
                clearIcon={null}
                loading={loading}
                value={value}
                onInput={handleInput}
                classes={{
                    paper: clsx(styles.paper, paperClasses),
                    root: clsx(styles.root, rootClasses),
                    popupIndicator: clsx(styles.popupIndicator, popupIndicatorClasses),
                    ...otherClasses,
                }}
                slotProps={{
                    paper: {
                        sx: {
                            '& .MuiListItem-root': {
                                paddingBottom: '0 !important',
                            },
                            '& .MuiAutocomplete-listbox': {
                                ...scrollbarStyle(),
                                paddingBottom: '0 !important',
                            },
                        },
                        ...paperProps,
                    },
                    ...otherSlotProps,
                }}
                renderInput={(params) => (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TextField
                            {...params}
                            inputRef={inputRef}
                            fullWidth
                            placeholder={placeholder ?? t('newVehicle.brandPlaceholder')}
                            InputProps={{
                                ...params.InputProps,
                                type: 'text',
                                sx: (theme) => ({
                                    ...theme.typography.body1,
                                    background: theme.palette.neutral[2],
                                    height: 32,
                                    paddingLeft: 16,

                                    '& input::placeholder': {
                                        opacity: 0.75,
                                    },

                                    [`& .${outlinedInputClasses.notchedOutline}`]: {
                                        borderColor: theme.palette.neutral[5],
                                    },

                                    '&:focus': {
                                        borderColor: theme.palette.primary.main,
                                    },

                                    '&:hover:not(:has(input[disabled]))': {
                                        [`& .${outlinedInputClasses.notchedOutline}`]: {
                                            borderColor: `${theme.palette.primary.main}`,
                                        },
                                    },
                                }),
                                startAdornment: !hideIcons && value?.icon && !isEdit && (
                                    <StartAdornment
                                        style={{ width: 24, height: 24, paddingLeft: 4 }}
                                    >
                                        <value.icon size={18} fill={value.color} />
                                    </StartAdornment>
                                ),
                                endAdornment: (
                                    <>
                                        {loading && (
                                            <CircularProgress
                                                sx={{ marginRight: 0.75 }}
                                                size={16}
                                            />
                                        )}
                                        {params.InputProps.endAdornment}
                                    </>
                                ),
                            }}
                        />
                    </Box>
                )}
                filterOptions={(options, params) => {
                    const { inputValue } = params;

                    const filtered = options.filter((x) =>
                        x.label.toLowerCase().includes(inputValue.toLowerCase())
                    );

                    if (!creatable) {
                        return filtered;
                    }

                    const isExisting = options.some(
                        (x) => x.label.toLowerCase() === inputValue.toLowerCase()
                    );
                    if (inputValue !== '' && !isExisting) {
                        filtered.push({
                            label: `${t('commonLabels.create')} "${inputValue}"`,
                            value: null,
                            group: '',
                        });
                    }

                    return filtered;
                }}
                renderOption={(props, option) => {
                    return (
                        <ListItemButton
                            disableRipple
                            component="li"
                            sx={(theme) => ({
                                '&:not(:last-child)': {
                                    borderBottom: `1px solid ${theme.palette.neutral[3]}`,
                                },
                            })}
                            {...props}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {!hideIcons && (
                                    <StartAdornment style={{ width: 24, height: 24 }}>
                                        {option.icon && (
                                            <option.icon size={18} fill={option.color} />
                                        )}
                                    </StartAdornment>
                                )}
                                <div>{option.label}</div>
                            </Box>
                        </ListItemButton>
                    );
                }}
                renderGroup={
                    grouped
                        ? (params) => (
                              <GroupListItem key={params.key}>
                                  {params.group && <Group>{params.group}</Group>}
                                  {params.children}
                              </GroupListItem>
                          )
                        : undefined
                }
                popupIcon={
                    <ExpandMore
                        sx={{
                            fill: 'var(--neutral9)',
                        }}
                    />
                }
                isOptionEqualToValue={(option, value) =>
                    getOptionKey(option?.value) === getOptionKey(value?.value)
                }
                getOptionLabel={(o) => o?.label ?? ''}
                getOptionKey={(o) => getOptionKey(o.value) ?? -1}
                groupBy={grouped ? (o) => o.group ?? '' : undefined}
                onChange={(_e, value) => {
                    handleChange(value);
                }}
                onBlur={handleBlur}
                {...props}
            />
        </InputWrapper>
    );
}

export function Group({ children }: React.PropsWithChildren<{}>) {
    return (
        <LabelWrapper>
            <span>{children}</span>
        </LabelWrapper>
    );
}

const StartAdornment = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'start',
    marginRight: 4,
    width: 24,
});

const GroupListItem = styled('li')({
    marginBottom: 15,
});

const LabelWrapper = styled('div')(({ theme }) => ({
    fontStyle: 'normal',
    fontSize: 12,
    lineHeight: '13.31px',
    color: theme.palette.info.light,
    fontFamily: 'Roboto',
    fontWeight: 700,
    cursor: 'default',
    display: 'flex',
    height: 24,
    paddingBottom: 2,
    paddingTop: 9,
    paddingLeft: 12,
    paddingRight: 12,
    alignItems: 'center',
    boxSizing: 'border-box',
}));
