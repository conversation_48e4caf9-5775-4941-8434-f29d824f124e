import { useQuery } from '@tanstack/react-query';
import BrandsAPI from 'api/Clients/Brands';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands, selectModels } from 'store/slices/clientsSlice/selectors';
import { orderActions } from 'store/slices/order/orderDetails';
import { cleanYear } from 'views/Components/YearPicker/helpers';
import AutocompleteDropdown, { AutocompleteDropdownProps } from '../AutocompleteDropdown';

export type YearPickerProps = {
    fieldId?: string;
    value: string | null | undefined;
    brandName: string | undefined;
    modelName: string | undefined;
    onChange?: (year: string | undefined) => void;
    isSelected?: boolean;
} & Pick<
    AutocompleteDropdownProps<string | null>,
    'label' | 'disabled' | 'hideIcons' | 'placeholder' | 'onKeyDown'
>;

export default function YearPicker({
    value,
    modelName,
    brandName,
    onChange,
    placeholder,
    fieldId,
    isSelected,
    ...props
}: YearPickerProps) {
    const ref = useRef<HTMLInputElement>(null);
    const dispatch = useAppDispatch();
    const brands = useAppSelector(selectBrands);
    const brandId = useMemo(
        () => brands.find((b) => b.name === brandName)?.id,
        [brands, brandName]
    );
    const models = useAppSelector((s) => {
        return selectModels(s, brandId ?? -1);
    });
    const modelId = useMemo(
        () => models.find((m) => m.name === modelName)?.id,
        [models, modelName]
    );

    const [years, setYears] = useState<string[]>([]);
    const { t } = useAppTranslation();

    const { data } = useQuery(
        ['year-picker'],
        async () => (modelId ? await BrandsAPI.getYears(modelId) : []),
        {
            enabled: modelId !== undefined,
        }
    );

    useEffect(() => {
        if (data) {
            setYears(data);
        } else {
            setYears([]);
        }
    }, [data]);

    const onChangeCallback = useCallback(
        (option: string | null) => {
            if (onChange && option) {
                onChange(cleanYear(option));
            }
        },
        [onChange]
    );

    const options = useMemo(() => years.map((y) => ({ value: y, label: y })), [years]);

    const selected = useMemo(
        () =>
            options.find((o) => o.value === value) ??
            (value ? { label: value, value: null } : null),
        [value, options]
    );

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <AutocompleteDropdown<string | null>
            inputRef={ref}
            creatable
            placeholder={placeholder ?? t('newCustomer.modelPlaceholder')}
            options={options}
            value={selected ?? null}
            onChange={(value) => {
                onChangeCallback(value);
            }}
            onBlur={() => {
                fieldId && dispatch(orderActions.clearSelectedField({ id: fieldId }));
            }}
            getOptionKey={(x) => x ?? -1}
            {...props}
        />
    );
}
