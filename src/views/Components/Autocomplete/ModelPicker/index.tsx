import { VehicleModelDto } from 'api/Clients/Brands';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands, selectModels } from 'store/slices/clientsSlice/selectors';
import ensureModelsFetched from 'store/slices/clientsSlice/thunks/ensureModelsFetched';
import { orderActions } from 'store/slices/order/orderDetails';
import AutocompleteDropdown, {
    AutocompleteDropdownProps,
    OptionData,
} from '../AutocompleteDropdown';

export type ModelPickerProps = {
    value?: string | null;
    onChange: (value: string | undefined, model?: VehicleModelDto) => void;
    brandName?: string;
    fieldId?: string;
    isSelected?: boolean;
} & Pick<
    AutocompleteDropdownProps<VehicleModelDto | null>,
    'label' | 'disabled' | 'hideIcons' | 'placeholder' | 'onKeyDown'
>;

export default function ModelPicker({
    fieldId,
    value,
    onChange,
    brandName,
    placeholder,
    isSelected,
    onKeyDown,
    ...props
}: ModelPickerProps) {
    const ref = useRef<HTMLInputElement>(null);
    const brands = useAppSelector(selectBrands);
    const brandId = useMemo(
        () => brands.find((b) => b.name === brandName)?.id,
        [brands, brandName]
    );
    const models = useAppSelector((r) => selectModels(r, brandId ?? -1));
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    useEffect(() => {
        if (brandId) {
            dispatch(ensureModelsFetched({ brandId }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [brandId]);

    const options: OptionData<VehicleModelDto | null>[] = useMemo(
        () => models.map((m) => ({ label: m.name, value: m })),
        [models]
    );

    const selected = useMemo(() => {
        const predefined = options.find((o) => o.value?.name === value);

        if (!predefined) {
            if (value) {
                return { label: value, value: null };
            } else {
                return null;
            }
        }
        return predefined;
    }, [options, value]);

    const onChangeCallback = useCallback(
        (option: VehicleModelDto | string | null) => {
            if (!option) return;

            if (onChange) {
                if (typeof option === 'string') {
                    if (option === value) return;

                    onChange(option);
                } else {
                    if (option.name === value) return;

                    onChange(option.name, option);
                }
            }
        },
        [onChange, value]
    );

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <AutocompleteDropdown<VehicleModelDto | null>
            inputRef={ref}
            creatable
            placeholder={placeholder ?? t('newCustomer.modelPlaceholder')}
            options={options}
            value={selected ?? null}
            onChange={(value) => {
                onChangeCallback(value);
            }}
            onBlur={() => {
                fieldId && dispatch(orderActions.clearSelectedField({ id: fieldId }));
            }}
            getOptionKey={(x) => x?.id ?? -1}
            {...props}
        />
    );
}
