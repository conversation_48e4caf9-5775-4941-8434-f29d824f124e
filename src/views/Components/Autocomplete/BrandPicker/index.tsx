import { BrandDto } from 'api/Clients/Brands';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands, selectFrequentBrands } from 'store/slices/clientsSlice/selectors';
import ensureBrandsFetched from 'store/slices/clientsSlice/thunks/ensureBrandsFetched';
import { orderActions } from 'store/slices/order/orderDetails';
import AutocompleteDropdown, {
    AutocompleteDropdownProps,
    OptionData,
} from '../AutocompleteDropdown';

type BrandPickerProps = {
    value?: string;
    onChange?: (brandName: string | undefined, brand?: BrandDto) => void;

    /**
     * if set to a value will use enterprise API endpoint to fetch list of
     * brands.
     */
    enterpriseRepairShopKey?: string;

    fieldId?: string;
    isSelected?: boolean;
} & Pick<
    AutocompleteDropdownProps<BrandDto | null>,
    'label' | 'disabled' | 'hideIcons' | 'placeholder' | 'onKeyDown'
>;

export default function BrandPicker({
    value,
    onChange,
    placeholder,
    enterpriseRepairShopKey,
    fieldId,
    isSelected,
    ...props
}: BrandPickerProps) {
    const ref = useRef<HTMLInputElement>(null);
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const brands = useAppSelector(selectBrands);
    const frequentBrands = useAppSelector(selectFrequentBrands);
    const options: OptionData<BrandDto | null>[] = useMemo(
        () => brands.map((x) => makeOption(x, t('commonLabels.allBrands'))),
        [brands, t]
    );
    const frequentOptions: OptionData<BrandDto | null>[] = useMemo(
        () => frequentBrands.map((x) => makeOption(x, t('commonLabels.frequentBrands'))),
        [frequentBrands, t]
    );
    const allOptions: OptionData<BrandDto | null>[] = useMemo(
        () => [...frequentOptions, ...options],
        [frequentOptions, options]
    );

    const selectedOption = useMemo(() => {
        const predefined = options.find((o) => o.value?.name === value);

        if (!predefined) {
            if (value) {
                return { label: value, value: null, filter: t('commonLabels.allBrands') };
            } else {
                return null;
            }
        }
        return predefined;
    }, [options, value, t]);

    const onChangeCallback = useCallback(
        (option: BrandDto | string | null) => {
            if (!option) return;

            if (onChange) {
                if (typeof option === 'string') {
                    if (option === value) return;

                    onChange(option);
                } else {
                    if (option.name === value) return;

                    onChange(option.name, option);
                }
            }
        },
        [onChange, value]
    );

    const isEnterprise = useIsEnterpriseRoute();

    useEffect(() => {
        if (isEnterprise) {
            if (enterpriseRepairShopKey) {
                dispatch(ensureBrandsFetched(enterpriseRepairShopKey));
            }
        } else {
            dispatch(ensureBrandsFetched());
        }
    }, [dispatch, enterpriseRepairShopKey, isEnterprise]);

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <AutocompleteDropdown<BrandDto | null>
            inputRef={ref}
            grouped
            creatable
            placeholder={placeholder ?? t('newVehicle.brandPlaceholder')}
            options={allOptions}
            value={selectedOption ?? null}
            onChange={(value) => {
                onChangeCallback(value);
            }}
            onBlur={() => {
                fieldId && dispatch(orderActions.clearSelectedField({ id: fieldId }));
            }}
            getOptionKey={(x) => x?.id ?? -1}
            {...props}
        />
    );
}

const makeOption = (brand: BrandDto, group: string) => {
    return {
        label: brand.name,
        value: brand,
        icon: brand.hasLogo ? () => <img alt="" src={brand.brandUrl} /> : undefined,
        group,
    };
};
