import { JobTitle } from 'common/constants';
import { useUsers } from 'common/hooks/useUsers';
import { useMemo } from 'react';
import UserSelect, { UserSelectProps } from './UserSelect';

export type UserSelectByJobTitleProps = Omit<UserSelectProps, 'userIds'> & {
    jobTitle: JobTitle;
};

export default function UserSelectByJobTitle({
    jobTitle,
    shopId,
    ...props
}: UserSelectByJobTitleProps) {
    const allUsers = useUsers(shopId);

    const userIds = useMemo(
        () => allUsers.filter((x) => x.job === jobTitle).map((x) => x.key),
        [allUsers, jobTitle]
    );

    return <UserSelect userIds={userIds} shopId={shopId} {...props} />;
}
