import { styled } from '@mui/material';
import { UserListItem } from 'api/users';
import { PropsWithTestId } from 'common/components';
import SMenuItem from 'common/components/mui/SMenuItem';
import { SSelectInput, SSelectProps } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useUsers } from 'common/hooks/useUsers';
import { useMemo } from 'react';
import { User } from 'store/slices/users';

export type UserSelectProps = Omit<SSelectProps<string>, 'onChange' | 'value'> &
    PropsWithTestId<{
        userId?: string | null;
        userIds?: string[];
        shopId?: string;
        showJobTitle?: boolean;
        showEmail?: boolean;
        onChange?: (userId: string, user: UserListItem | undefined) => void;
        SelectComponent?: typeof SSelectInput;
    }>;

const ColorCircle = styled('span')({
    display: 'inline-flex',
    marginRight: 5,
    width: 10,
    minWidth: 10,
    height: 10,
    borderRadius: 8,
});

const SubText = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[6],
    ...theme.typography.h7Inter,
    paddingLeft: 15,
    flexBasis: '100%',
}));

export default function UserSelect({
    userId,
    userIds,
    shopId,
    showJobTitle = false,
    showEmail = false,
    onChange,
    dataTestId,
    SelectComponent = SSelectInput,
    ...props
}: UserSelectProps) {
    const allUsers = useUsers(shopId);

    const { listedUsers, invalidUser } = useMemo(() => {
        let users: User[];
        let invalidUser = false;

        if (userIds) {
            users = allUsers.filter((x) => userIds.includes(x.key));
        } else {
            users = Object.values(allUsers);
        }
        users.sort((a, b) => a.name.localeCompare(b.name));

        if (userId && users.every((x) => x.key !== userId)) {
            const user = allUsers.find((x) => x.key === userId);
            if (user) {
                users.unshift(user);
            } else {
                invalidUser = true;
            }
        }

        return {
            listedUsers: users,
            invalidUser,
        };
    }, [allUsers, userId, userIds]);
    const { t } = useAppTranslation();

    function renderValue(id: string) {
        const user = allUsers.find((x) => x.key === id);

        if (!user) return id;

        return (
            <>
                <ColorCircle style={{ backgroundColor: user.color || '#bbb' }} />
                {user.name}
            </>
        );
    }

    return (
        <SelectComponent<string>
            data-test-id={dataTestId}
            value={userId ?? ''}
            onChange={(event) => {
                const userId = event.target.value as string;
                const user = allUsers.find((x) => x.key === userId);
                if (onChange) onChange(userId, user);
            }}
            {...(userId
                ? {
                      renderValue,
                  }
                : undefined)}
            {...props}
        >
            {invalidUser && userId && <SMenuItem value={userId}>INVALID:{userId}</SMenuItem>}
            {listedUsers.map((x) => (
                <SMenuItem value={x.key} key={x.key}>
                    <ColorCircle style={{ backgroundColor: x.color || '#bbb' }} />
                    {x.name}
                    {x.job && showJobTitle && (
                        <SubText>{t('users.jobTitleTypes.' + x.job)}</SubText>
                    )}
                    {showEmail && <SubText>{x.userName}</SubText>}
                </SMenuItem>
            ))}
        </SelectComponent>
    );
}
