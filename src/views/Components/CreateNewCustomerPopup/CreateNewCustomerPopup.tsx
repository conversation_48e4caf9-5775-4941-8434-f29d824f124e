import CustomersApi, { VehicleCreateDto } from 'api/customers';
import { useCallback } from 'react';
import { useSelector } from 'react-redux';
import CustomersAPI, { CustomerCreateDto } from '../../../api/Clients/Customers';
import { EnterpriseCustomersApi } from '../../../api/enterprise';
import { selectIsEnterprise } from '../../../store/slices/globalSettingsSlice';
import CreateNewCustomerPopupCustomApi, {
    CreateNewCustomerPopupCustomApiProps,
} from './CreateNewCustomerPopupCustomApi';

export type CreateNewCustomerPopupProps = Omit<
    CreateNewCustomerPopupCustomApiProps,
    'createNewCustomer' | 'createNewVehicle'
>;

export default function CreateNewCustomerPopup(props: CreateNewCustomerPopupProps) {
    const isEnterprise = useSelector(selectIsEnterprise);

    return (
        <CreateNewCustomerPopupCustomApi
            createNewVehicle={useCallback(
                (v: VehicleCreateDto) => {
                    if (isEnterprise)
                        return EnterpriseCustomersApi.createVehicle(props.repairShopKey!, v);
                    else return CustomersApi.createVehicle(v);
                },
                [isEnterprise, props.repairShopKey]
            )}
            createNewCustomer={useCallback(
                (c: CustomerCreateDto) => {
                    if (isEnterprise) return EnterpriseCustomersApi.create(props.repairShopKey!, c);
                    else return CustomersAPI.createCustomer(c);
                },
                [isEnterprise, props.repairShopKey]
            )}
            {...props}
        />
    );
}
