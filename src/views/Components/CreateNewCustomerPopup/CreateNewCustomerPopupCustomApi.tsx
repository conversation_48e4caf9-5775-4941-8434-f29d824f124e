import { Box, Divider, IconButton, styled, Typography } from '@mui/material';
import { BrandDto, VehicleModelDto } from 'api/Clients/Brands';
import { CustomerCreateDto, CustomerNewDto, CustomerSearchItemDto } from 'api/Clients/Customers';
import { VehicleCreateDto, VehicleDetailsDto, VehiclesCrmApi } from 'api/customers';
import axios, { AxiosError } from 'axios';
import buildInfo from 'build-id';
import { isValidEmail } from 'common/Helpers';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { NumberFormField } from 'common/components/Inputs/NumberField';
import PhoneField from 'common/components/Inputs/PhoneField';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import PhoneNumberDisplay from 'common/components/PhoneNumberDisplay';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import BrandPicker from 'views/Components/BrandPicker';
import ModelPicker from 'views/Components/ModelPicker';
import YearPicker from 'views/Components/YearPicker';
import CustomerAutocomplete from '../CustomerAutocomplete';
import { getNewCustomer, getNewVehicle } from './helpers';
import EnterpriseVehiclesApi from 'api/enterprise/vehiclesApi';

export type CustomerCreatedEvent = {
    customer: CustomerNewDto;
    customerWasCreated: boolean;
    vehicle: VehicleDetailsDto | null;
};

export type CreateNewCustomerPopupCustomApiProps = {
    open: boolean;
    repairShopKey?: string;
    onClose: () => void;
    onCustomerAndVehicleCreated: (event: CustomerCreatedEvent) => void;
    createNewCustomer: (dto: CustomerCreateDto) => Promise<CustomerNewDto>;
    createNewVehicle: (dto: VehicleCreateDto) => Promise<VehicleDetailsDto>;
    vehicleIsRequired?: boolean;
};

export default function CreateNewCustomerPopupCustomApi({
    open,
    repairShopKey,
    onClose,
    onCustomerAndVehicleCreated,
    createNewCustomer,
    createNewVehicle,
    vehicleIsRequired = false,
}: CreateNewCustomerPopupCustomApiProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const permissions = useAppSelector(selectUserPermission);
    const isEnterprise = useIsEnterpriseRoute();
    const canCreateVehicle = permissions.allowEditVehicles || isEnterprise;
    const canCreateCustomer = permissions.allowEditCustomers || isEnterprise;

    // we can use a single callApi because we're only going to call it sequentially (no parallel calls)
    const { callApi } = useApiCall();

    // selected customer data
    const [_existingCustomer, setExistingCustomer] = useState<CustomerSearchItemDto | null>(null);
    const existingCustomer = canCreateVehicle ? _existingCustomer : null;

    // form data
    const [customer, setCustomer] = useState(getNewCustomer);
    const [vehicle, setVehicle] = useState(getNewVehicle);
    const [brand, setBrand] = useState<BrandDto>();
    const [model, setModel] = useState<VehicleModelDto | undefined>(undefined);
    const [saving, setSaving] = useState(false);

    const [isPopupOpen, setPopupOpen] = useState(false);
    const [isPhoneConflict, setPhoneConflict] = useState(false);

    const vehicleHasData = !!(
        vehicle.brand?.trim() ||
        vehicle.color?.trim() ||
        vehicle.model?.trim() ||
        vehicle.plates?.trim() ||
        vehicle.vin?.trim() ||
        vehicle.year?.trim() ||
        vehicle.mileage !== undefined
    );

    // validation stuff for customer
    const validEmail = !customer.email || isValidEmail(customer.email.trim());

    const trimmedFirstName = customer.firstName.trim();
    const validFirstName = trimmedFirstName.length <= 100;

    const validLastName = !customer.lastName || customer.lastName.trim().length <= 100;

    const validCustomer =
        validFirstName &&
        trimmedFirstName.length > 0 &&
        validLastName &&
        customer.mobile.trim() !== '' &&
        validEmail;

    // validation stuff for vehicle
    const hasVin = !!vehicle.vin && vehicle.vin.trim().length > 0;

    const hasPlates = !!vehicle.plates && vehicle.plates.trim().length > 0;

    const hasBrandModelAndYear =
        !!vehicle.brand &&
        vehicle.brand.trim().length > 0 &&
        !!vehicle.model &&
        vehicle.model.trim().length > 0 &&
        !!vehicle.year &&
        vehicle.year.trim().length > 0;

    const validVehicle = !!(hasBrandModelAndYear || hasPlates || hasVin) && vehicleHasData;

    let valid = false,
        hasData = false;

    // if existing customer selected it does not need to be validated
    if (existingCustomer) {
        valid = validVehicle;
        hasData = vehicleHasData;
    } else {
        valid = validCustomer && (validVehicle || (!vehicleHasData && !vehicleIsRequired));
        hasData =
            !!(
                customer.firstName.trim() ||
                customer.lastName?.trim() ||
                customer.email?.trim() ||
                customer.mobile.trim() ||
                customer.taxIdentification?.trim()
            ) || vehicleHasData;
    }

    function checkVehicleExists(params: any) {
        if (isEnterprise && repairShopKey) {
            return EnterpriseVehiclesApi.checkVehicleExists({ ...params, shopId: repairShopKey });
        } else {
            return VehiclesCrmApi.checkVehicleExists(params);
        }
    }

    const onCloseCallback = useCallback(() => {
        if (hasData) {
            setPopupOpen(true);
        } else {
            onClose();
        }
    }, [onClose, hasData]);

    useEffect(() => {
        if (!open) {
            // reset state
            setExistingCustomer(null);
            setVehicle(getNewVehicle());
            setCustomer(getNewCustomer());
            setPhoneConflict(false);
            setBrand(undefined);
        }
    }, [open]);

    const save = async () => {
        if (!canCreateCustomer) return;
        if (!valid) return;
        setSaving(true);

        if (isPhoneConflict) {
            setPhoneConflict(false);
        }

        try {
            // first validate vehicle and check there are no duplicates
            // TODO move this to backend where it belongs I guess? Honestly this should not be here
            if (vehicleHasData && canCreateVehicle) {
                const vinDuplicatedPromise = vehicle.vin
                    ? checkVehicleExists({ vin: vehicle.vin })
                    : Promise.resolve(false);

                const platesDuplicatedPromise = vehicle.plates
                    ? checkVehicleExists({ plates: vehicle.plates })
                    : Promise.resolve(false);

                const vinDuplicated = await vinDuplicatedPromise;
                const platesDuplicated = await platesDuplicatedPromise;

                if (vinDuplicated && platesDuplicated) {
                    toasters.danger(
                        t('newVehicle.vinAndPlatesDuplicated'),
                        t('newVehicle.vehicleNotCreated')
                    );
                    return;
                } else if (vinDuplicated) {
                    toasters.danger(
                        t('newVehicle.vinDuplicated'),
                        t('newVehicle.vehicleNotCreated')
                    );
                    return;
                } else if (platesDuplicated) {
                    toasters.danger(
                        t('newVehicle.platesDuplicated'),
                        t('newVehicle.vehicleNotCreated')
                    );
                    return;
                }
            }

            let createdCustomer: CustomerNewDto,
                customerWasCreated = true;

            if (existingCustomer) {
                createdCustomer = {
                    id: existingCustomer.id,
                    firstName: existingCustomer.firstName,
                    mobile: existingCustomer.mobile,
                    lastName: existingCustomer.lastName,
                    businessName: existingCustomer.businessName,
                    taxIdentification: existingCustomer.taxIdentification,
                    email: existingCustomer.email,
                };
                customerWasCreated = false;
            } else {
                const newCustomer = await callApi(
                    () =>
                        createNewCustomer({
                            ...customer,
                            email: customer.email?.trim(),
                        }),
                    {
                        selectErrorContent: ({ response }: AxiosError) =>
                            response?.status === 409
                                ? {
                                      body: t('newCustomer.notCreatedBody'),
                                      title: t('newCustomer.notCreated'),
                                  }
                                : {},
                    }
                );
                createdCustomer = newCustomer;
            }

            if (vehicleHasData && canCreateVehicle) {
                try {
                    // only validate brand-model-year combo if no vin, plates and vehicle is created for existing customer
                    if (
                        vehicle.brand &&
                        vehicle.model &&
                        vehicle.year &&
                        !vehicle.vin &&
                        !vehicle.plates &&
                        existingCustomer
                    ) {
                        if (
                            await VehiclesCrmApi.checkVehicleExists({
                                brand: vehicle.brand,
                                model: vehicle.model,
                                year: vehicle.year,
                                customerId: createdCustomer.id,
                            })
                        ) {
                            toasters.danger(
                                t('newVehicle.brandModelYearDuplicated'),
                                t('newVehicle.vehicleNotCreated')
                            );
                            onCustomerAndVehicleCreated({
                                customer: createdCustomer,
                                customerWasCreated,
                                vehicle: null,
                            });
                            return;
                        }
                    }

                    const newVehicle = await callApi(
                        () =>
                            createNewVehicle({
                                customerId: createdCustomer.id,
                                ...vehicle,
                            }),
                        {
                            selectErrorContent: () => ({
                                body: t('toasters.errorOccurredWhenSaving'),
                            }),
                        }
                    );
                    // If something goes wrong with creating the vehicle we just display new customer.
                    // User can create vehicle separately later.
                    onCustomerAndVehicleCreated({
                        customer: createdCustomer,
                        customerWasCreated,
                        vehicle: newVehicle,
                    });
                } catch {
                    onCustomerAndVehicleCreated({
                        customer: createdCustomer,
                        customerWasCreated,
                        vehicle: null,
                    });
                }
            } else {
                onCustomerAndVehicleCreated({
                    customer: createdCustomer,
                    customerWasCreated,
                    vehicle: null,
                });
            }
            onClose();
            toasters.success(t('newCustomer.createdBody'), t('newCustomer.created'));
        } catch (e) {
            if (axios.isAxiosError(e) && e.response?.status === 409) {
                setPhoneConflict(true);
            }
        } finally {
            setSaving(false);
        }
    };

    return (
        <Modal open={open}>
            <Modal open={isPopupOpen}>
                <AreYouSureBox gap={1.25} alignItems="center" display="flex" flexDirection="column">
                    <CloseButton onClick={() => setPopupOpen(false)} size="large">
                        <CloseIcon />
                    </CloseButton>

                    <AreYouSureTitle>{t('newCustomer.areYouSure.title')}</AreYouSureTitle>

                    <span>{t('newCustomer.areYouSure.subtitle')}</span>

                    <Box display="flex" gap={2} marginTop={1}>
                        <Button
                            customStyles={{ width: 200 }}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            onClick={() => setPopupOpen(false)}
                            label={t('commonLabels.goBack')}
                        />
                        <Button
                            customStyles={{ width: 200 }}
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            onClick={() => {
                                setPopupOpen(false);
                                onClose();
                            }}
                            label={t('newCustomer.areYouSure.yes')}
                        />
                    </Box>
                </AreYouSureBox>
            </Modal>
            <Root>
                <Box display="flex" justifyContent="space-between" marginBottom={2}>
                    <div>
                        <Title>{t('newCustomer.title')}</Title>
                    </div>

                    <Box display="flex" gap={1.25}>
                        <Button
                            w="md"
                            onClick={onCloseCallback}
                            label={t('commonLabels.goBack')}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                        />
                        <Button
                            w="md"
                            onClick={save}
                            customStyles={{ width: 200 }}
                            label={t('newCustomer.create')}
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            showLoader={saving}
                            disabled={!valid || saving}
                        />
                    </Box>
                </Box>

                {/* i hate this but we gotta fix it quick */}
                {!buildInfo.BUILD_ID.startsWith('production') && canCreateVehicle && (
                    <div style={{ display: existingCustomer !== null ? 'none' : 'block' }}>
                        <CustomerAutocomplete
                            value={existingCustomer}
                            onSelect={setExistingCustomer}
                            onOpenCreatePopup={null}
                            disableCreateButton
                            noOptionsText={t('newCustomer.noCustomersText')}
                        />
                    </div>
                )}

                {existingCustomer && (
                    <div>
                        <br />
                        <Typography color="neutral.8" variant="h4Inter">
                            {existingCustomer.firstName} {existingCustomer.lastName}{' '}
                            <ChangeCustomerButton
                                type="button"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    setExistingCustomer(null);
                                }}
                            >
                                {t('newCustomer.changeCustomer')}
                            </ChangeCustomerButton>
                        </Typography>
                        <br />
                        <Typography color="neutral.8" variant="h7Inter">
                            <PhoneNumberDisplay phoneNumber={existingCustomer.mobile} />
                        </Typography>
                    </div>
                )}

                <Box
                    sx={{
                        mt: 2,
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr 1fr',
                        gap: '10px 20px',
                    }}
                >
                    <TextFormField
                        disabled={saving || existingCustomer !== null || !canCreateCustomer}
                        name="firstName"
                        label={t('newCustomer.name')}
                        placeholder={t('newCustomer.namePlaceholder')}
                        isRequired
                        value={
                            (existingCustomer ? existingCustomer.firstName : customer.firstName) ||
                            ''
                        }
                        showValidationIndicators
                        isInvalid={!validFirstName}
                        onChange={(e) => setCustomer({ ...customer, firstName: e.target.value })}
                        cmosVariant="grey"
                    />
                    <TextFormField
                        disabled={saving || existingCustomer !== null || !canCreateCustomer}
                        name="lastName"
                        label={t('newCustomer.lastName')}
                        placeholder={t('newCustomer.lastNamePlaceholder')}
                        value={
                            (existingCustomer ? existingCustomer.lastName : customer.lastName) || ''
                        }
                        isInvalid={!validLastName}
                        onChange={(e) => setCustomer({ ...customer, lastName: e.target.value })}
                        cmosVariant="grey"
                    />

                    <TextFormField
                        disabled={saving || existingCustomer !== null || !canCreateCustomer}
                        name="email"
                        label={t('newCustomer.email')}
                        placeholder={t('newCustomer.emailPlaceholder')}
                        value={(existingCustomer ? existingCustomer.email : customer.email) || ''}
                        isInvalid={!validEmail}
                        onChange={(e) => setCustomer({ ...customer, email: e.target.value })}
                        cmosVariant="grey"
                    />
                    <PhoneField
                        isInvalid={isPhoneConflict}
                        placeholder={t('newCustomer.mobilePlaceholder')}
                        disabled={saving || existingCustomer !== null || !canCreateCustomer}
                        showValidationIndicators
                        isRequired
                        value={(existingCustomer ? existingCustomer.mobile : customer.mobile) || ''}
                        onChange={(mobile) => setCustomer({ ...customer, mobile })}
                        cmosVariant="grey"
                    />
                    <TextFormField
                        disabled={saving || existingCustomer !== null || !canCreateCustomer}
                        name="taxIdent"
                        label={t('newCustomer.taxIdent')}
                        placeholder={t('newCustomer.taxIdentPlaceholder')}
                        value={
                            (existingCustomer
                                ? existingCustomer.taxIdentification
                                : customer.taxIdentification) || ''
                        }
                        onChange={(e) =>
                            setCustomer({ ...customer, taxIdentification: e.target.value })
                        }
                        cmosVariant="grey"
                    />

                    <TextFormField
                        disabled={saving || existingCustomer !== null || !canCreateCustomer}
                        name="lastName"
                        label={t('newCustomer.businessName')}
                        placeholder={t('newCustomer.businessNamePlaceholder')}
                        value={
                            (existingCustomer
                                ? existingCustomer.businessName
                                : customer.businessName) || ''
                        }
                        isInvalid={!validLastName}
                        onChange={(e) => setCustomer({ ...customer, businessName: e.target.value })}
                        cmosVariant="grey"
                    />
                </Box>
                <StyledDivider />

                <p style={{ color: 'var(--neutral7)', display: 'flex', alignItems: 'center' }}>
                    <Box
                        component="span"
                        sx={{
                            backgroundColor: 'var(--neutral7)',
                            color: 'var(--neutral1)',
                            borderRadius: 100,
                            height: 16,
                            width: 16,
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mr: '8px',
                        }}
                    >
                        <InfoIcon size={14} fill="currentColor" />
                    </Box>
                    <span style={{ display: 'inline-flex' }}>{t('newCustomer.vehicleNotice')}</span>
                </p>
                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr 1fr',
                        gap: '10px 20px',
                    }}
                >
                    <TextFormField
                        disabled={saving || !canCreateVehicle}
                        name="plates"
                        label={t('commonLabels.plates')}
                        placeholder={t('newCustomer.platesPlaceholder')}
                        value={vehicle.plates}
                        onChange={(e) =>
                            setVehicle({
                                ...vehicle,
                                plates: e.target.value?.toUpperCase(),
                            })
                        }
                        cmosVariant="grey"
                    />
                    <TextFormField
                        disabled={saving || !canCreateVehicle}
                        name="vin"
                        label={t('commonLabels.vin')}
                        placeholder={t('commonLabels.vinPlaceholder')}
                        value={vehicle.vin}
                        onChange={(e) =>
                            setVehicle({ ...vehicle, vin: e.target.value?.toUpperCase() })
                        }
                        cmosVariant="grey"
                    />
                    <NumberFormField
                        disabled={saving || !canCreateVehicle}
                        name="mileage"
                        label={t('commonLabels.mileage')}
                        placeholder={t('commonLabels.mileagePlaceholder')}
                        value={vehicle.mileage}
                        onValueChange={(value) =>
                            setVehicle({ ...vehicle, mileage: value.floatValue })
                        }
                        cmosVariant="grey"
                    />
                    <BrandPicker
                        placeholder={t('newCustomer.brandPlaceholder')}
                        disabled={saving || !canCreateVehicle}
                        value={brand?.name}
                        enterpriseRepairShopKey={repairShopKey}
                        onChange={(brandName, brand) => {
                            setBrand(brand);
                            setVehicle({
                                ...vehicle,
                                brand: brandName ?? null,
                                model: null,
                                year: null,
                            });
                        }}
                        label={t('commonLabels.make')}
                        name="brand"
                        cmosVariant="grey"
                    />
                    <ModelPicker
                        isInvalid={!validVehicle && !!vehicle.brand && !vehicle.model}
                        disabled={saving || !canCreateVehicle}
                        brandName={brand?.name}
                        value={vehicle?.model ?? undefined}
                        onChange={(modelName, newModel) => {
                            setModel(newModel);
                            setVehicle({
                                ...vehicle,
                                model: modelName ?? null,
                                year: null,
                            });
                        }}
                        label={t('commonLabels.model')}
                        placeholder={t('newCustomer.modelPlaceholder')}
                        name="model"
                        cmosVariant="grey"
                    />
                    <YearPicker
                        isInvalid={!validVehicle && !!vehicle.brand}
                        disabled={saving || !canCreateVehicle}
                        modelName={model?.name}
                        brandName={brand?.name}
                        value={vehicle.year}
                        onChange={(year) =>
                            setVehicle({
                                ...vehicle,
                                year,
                            })
                        }
                        placeholder={t('newCustomer.yearPlaceholder')}
                        label={t('newCustomer.year')}
                        name="year"
                        cmosVariant="grey"
                    />
                    <TextFormField
                        disabled={saving || !canCreateVehicle}
                        name="color"
                        label={t('newCustomer.color')}
                        placeholder={t('newCustomer.colorPlaceholder')}
                        value={vehicle.color ?? ''}
                        onChange={(e) => setVehicle({ ...vehicle, color: e.target.value })}
                        cmosVariant="grey"
                    />
                </Box>
            </Root>
        </Modal>
    );
}

const ChangeCustomerButton = styled('button')({
    background: 'none',
    border: 'none',
    color: 'var(--cm1)',
    cursor: 'pointer',
    padding: 0,

    ':hover': {
        textDecoration: 'underline',
    },
});

const Root = styled('div')({
    padding: '45px 80px',
    width: 900,
});

const Title = styled('h4')(({ theme }) => ({
    ...theme.typography.h4Inter,
    margin: 0,
    color: theme.palette.neutral[7],
}));

const StyledDivider = styled(Divider)({
    margin: '30px 0',
});

const AreYouSureBox = styled(Box)({
    padding: '40px 30px 30px 30px',
    position: 'relative',
});

const AreYouSureTitle = styled('h4')(({ theme }) => ({
    ...theme.typography.h4Inter,
    margin: 0,
    color: theme.palette.neutral[8],
}));

const CloseButton = styled(IconButton)({
    position: 'absolute',
    top: 0,
    right: 0,
});
