import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    button: {
        width: 220,
        height: 32,
        borderRadius: 51,
        display: 'flex',
        cursor: 'pointer',
        alignItems: 'center',
        justifyContent: 'center',
        border: '1px solid #0069ff',
        backgroundColor: 'transparent',
        '&:hover': {
            backgroundColor: '#d0dffe',
        },
        '&:focus': {
            outline: 'none',
        },
    },
    caption: {
        ...theme.typography.h6Inter,
        fontWeight: 'bold',
        color: theme.palette.primary.main,
    },
    container: {
        border: '1px solid #c9cdd3',
        boxSizing: 'border-box',
        borderRadius: 10,
        overflow: 'hidden',
        width: 220,
        padding: '10px 0px',
    },
    item: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
        backgroundColor: 'transparent',
        cursor: 'pointer',
        borderStyle: 'none',
        display: 'block',
        minHeight: 38,
        padding: '6px 15px',
        '&:focus': {
            outline: 'none',
        },
        '&:hover': {
            backgroundColor: 'var(--cm5)',
        },
    },
}));

export default useStyles;
