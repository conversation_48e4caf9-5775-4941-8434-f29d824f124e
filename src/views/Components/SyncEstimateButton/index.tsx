import { Box, styled, tooltipClasses } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import { useMutation } from '@tanstack/react-query';
import OrderAPI from 'api/Order';
import { isCmosError } from 'api/error';
import { SyncUpIcon } from 'common/components/Icons/SyncUpIcon';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { CSSProperties, useMemo, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import {
    selectRepairShopFeatures,
    selectRepairShopIntegrationAccountName,
    selectRepairShopQuotesIntegrationType,
} from 'store/slices/globalSettingsSlice/selectors';
import { useOrderDetailsValidationsContext } from 'views/OrderDetail/providers';
import { selectIsOrderClosed } from '../../../store/slices/order/orderDetails';
import useStyles from './css';

type SyncEstimateButtonProps = {
    orderId: number;
    style?: CSSProperties;
    onEstimateLoaded?: () => void;
    onInvoiceLoaded?: () => void;
};

const SyncButton = styled('button')(({ theme }) => ({
    width: 220,
    height: 32,
    borderRadius: 51,
    display: 'flex',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    border: '1px solid #0069ff',
    backgroundColor: 'transparent',
    '&:hover': {
        backgroundColor: '#d0dffe',
    },
    '&:focus': {
        outline: 'none',
    },
}));

const SArrowTooltip = styled(ArrowTooltip)(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        ...theme.typography.h6Roboto,
        fontWeight: 400,
        fontSize: 12,
        padding: '9px 16px',
        boxShadow: '0px 2px 2px 0px rgba(0, 0, 0, 0.25)',
        borderColor: 'transparent',
    },
    [`& .${tooltipClasses.arrow}::before`]: {
        backgroundColor: '#fff',
        boxShadow: '0px 2px 2px 0px rgba(0, 0, 0, 0.25)',
    },
}));

const SyncEstimateButton = ({
    orderId,
    style,
    onEstimateLoaded,
    onInvoiceLoaded,
}: SyncEstimateButtonProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    const [openValidation, setOpenValidation] = useState(false);
    const button = useRef<HTMLButtonElement>(null);
    const integrationAccountName = useAppSelector(selectRepairShopIntegrationAccountName);
    const repairShopFeatures = useAppSelector(selectRepairShopFeatures);
    const orderIsClosed = useAppSelector(selectIsOrderClosed);
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);

    const { estimateIntegration, invoiceIntegration } = useMemo(
        () => ({
            estimateIntegration: repairShopFeatures?.enableEstimateIntegration,
            invoiceIntegration: repairShopFeatures?.invoiceIntegration,
        }),
        [repairShopFeatures]
    );

    const { mutate: synchronizeMutate, isLoading: isSynchronizeLoading } = useSynchronizeMutation(
        () => {
            onEstimateLoaded && onEstimateLoaded();
        }
    );

    const { mutate: synchronizeInvoiceMutate, isLoading: isSynchronizeInvoiceLoading } =
        useSynchronizeInvoiceMutation();

    const { isValid, invalidFields, setStartRedirect } = useOrderDetailsValidationsContext();

    const isLoading = useMemo(
        () => isSynchronizeLoading || isSynchronizeInvoiceLoading,
        [isSynchronizeLoading, isSynchronizeInvoiceLoading]
    );

    const handleSynchronization = () => {
        if (!isValid && invoiceIntegration) {
            handleOpenValidation();
        } else {
            if (estimateIntegration) synchronizeMutate(orderId);
            if (invoiceIntegration) synchronizeInvoiceMutate(orderId);
        }
    };

    const getTooltipText = () => {
        if (estimateIntegration && !!integratedAccountName)
            return t('syncEstimate.obtainEstimateFrom', { integratedAccountName });

        if (invoiceIntegration && !!integratedAccountName)
            return t('syncEstimate.uploadEstimateTo', { integratedAccountName });

        if (estimateIntegration) return t('syncEstimate.obtainEstimateFrom3rdPartySoftware');
        if (invoiceIntegration) return t('syncEstimate.uploadEstimateTo3rdPartySoftware');
        return '';
    };

    const handleCloseValidation = () => {
        setOpenValidation(false);
    };

    const handleOpenValidation = () => {
        setOpenValidation(true);
    };

    const handleConfirmValidation = () => {
        setOpenValidation(false);
        setStartRedirect(true);
    };

    return (
        <>
            <WarningConfirmationPopup
                open={openValidation}
                title={t('inspectionForms.validationQuoterModal.title')}
                confirm={t('inspectionForms.validationQuoterModal.button')}
                body={t('inspectionForms.validationQuoterModal.message', {
                    integrationAccountName,
                    fieldName: `${invalidFields
                        .map((field) => {
                            if (field.isPredefined) {
                                const translatedName = t(
                                    `settings.customizableFields.predefined.${field.name.replaceAll(
                                        '.',
                                        '_'
                                    )}`
                                );
                                return translatedName !==
                                    `settings.customizableFields.predefined.${field.name.replaceAll(
                                        '.',
                                        '_'
                                    )}`
                                    ? translatedName
                                    : field.name;
                            }
                            return field.name;
                        })
                        .join(', ')}`,
                })}
                onConfirm={handleConfirmValidation}
                onClose={handleCloseValidation}
            />
            {(estimateIntegration || invoiceIntegration) && (
                <div style={style}>
                    <SArrowTooltip
                        content={getTooltipText()}
                        position="top"
                        disabled={orderIsClosed}
                    >
                        <SyncButton
                            onClick={handleSynchronization}
                            ref={button}
                            disabled={orderIsClosed}
                        >
                            {isLoading ? (
                                <div>
                                    <CircularProgress size={17} style={{ color: '#467CFC' }} />
                                </div>
                            ) : (
                                <Box
                                    style={{
                                        display: 'flex',
                                        gap: 8,
                                        alignItems: 'center',
                                    }}
                                >
                                    <SyncUpIcon fill={Colors.CM1} />
                                    <span className={classes.caption}>
                                        {t('syncEstimate.syncEstimate')}
                                    </span>
                                </Box>
                            )}
                        </SyncButton>
                    </SArrowTooltip>
                </div>
            )}
        </>
    );
};

const useSynchronizeMutation = (onSuccess?: () => void, onError?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const integratedAccountName = useAppSelector(selectRepairShopIntegrationAccountName);
    const mutation = useMutation((orderId: number) => OrderAPI.syncExternalEstimates(orderId), {
        onSuccess: () => {
            toasters.success(
                t('syncEstimate.estimateSynchronized.success.description', {
                    integratedAccountName,
                }),
                t('syncEstimate.estimateSynchronized.success.title')
            );
            onSuccess && onSuccess();
        },
        onError: (error: any) => {
            toasters.danger(
                `${t('syncEstimate.estimateSynchronized.error.description', {
                    integratedAccountName,
                })}: ${error.response.data}`,
                t('syncEstimate.estimateSynchronized.error.title', {
                    integratedAccountName,
                })
            );
            onError && onError();
        },
    });

    return mutation;
};

const selectInvoiceMutation = createSelector(
    [selectRepairShopIntegrationAccountName, selectRepairShopQuotesIntegrationType],
    (integratedAccountName, quotesIntegrationType) => ({
        integratedAccountName,
        quotesIntegrationType,
    })
);

const useSynchronizeInvoiceMutation = (onSuccess?: () => void, onError?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { integratedAccountName, ...invoiceMutation } = useAppSelector(selectInvoiceMutation);
    const mutation = useMutation(
        (orderId: number) =>
            OrderAPI.syncExternalEstimatesInvoice({
                repairOrderId: orderId,
                ...invoiceMutation,
            }),
        {
            onSuccess: () => {
                toasters.success(
                    t('syncEstimate.estimateSynchronized.success.description', {
                        integratedAccountName,
                    }),
                    t('syncEstimate.estimateSynchronized.success.title')
                );
                onSuccess && onSuccess();
            },
            onError: (error: any) => {
                let errorMessage = error.message;
                if (isCmosError(error)) {
                    errorMessage = error.cmosMessage;
                }
                toasters.danger(
                    `${t('syncEstimate.estimateSynchronized.error.description', {
                        integratedAccountName,
                    })}: ${errorMessage}`,
                    t('syncEstimate.estimateSynchronized.error.title', {
                        integratedAccountName,
                    })
                );
                onError && onError();
            },
        }
    );

    return mutation;
};

export default SyncEstimateButton;
