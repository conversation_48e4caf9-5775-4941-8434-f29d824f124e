.caption {
    margin-left: 3px;
    margin-right: 3px;
}

.container {
    border: 1px solid #c9cdd3;
    box-sizing: border-box;
    border-radius: 10px;
    overflow: hidden;
}

.item {
    background-color: transparent;
    cursor: pointer;
    border-style: none;
    display: block;
    height: 38px;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    color: #6a6e72;
    padding-left: 20px;
    width: 100%;
}

.item:focus {
    outline: none;
}

.item:disabled {
    color: #d1d1d1;
}

.line {
    border-bottom-style: solid;
    border-width: 1px;
    border-color: #efefef;
    margin-right: 10px;
    margin-left: 10px;
}

.loaderContainer {
    min-width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}
