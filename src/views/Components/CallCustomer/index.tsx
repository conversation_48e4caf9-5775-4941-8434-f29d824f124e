import { Popover } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import OrderAPI from 'api/Order';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useDelay } from 'common/hooks/useDelay';
import { useRef, useState } from 'react';
import { useCurrentUser } from 'store/slices/user';
import ConnectingCall from '../ConnectingCall';
import styles from './styles.module.css';

export type CallCustomerProps = {
    canCallLandline: boolean;
    canCallMobile: boolean;
    repairOrderId: number;
    viewCallLogEnabled: boolean;
    onViewCallLog: () => void;
    className?: string;
    onCall: () => void;
};

export function CallCustomer({
    viewCallLogEnabled,
    onViewCallLog,
    className,
    canCallLandline,
    canCallMobile,
    repairOrderId,
    onCall,
}: CallCustomerProps) {
    const { t } = useAppTranslation();
    const [isOpened, setIsOpened] = useState(false);
    const [isConnectingCallOpened, setIsConnectingCallOpened] = useState(false);
    const [showLoader, setLoading] = useDelay();
    const [error, setError] = useState<string>();
    const button = useRef<HTMLButtonElement>(null);
    const user = useCurrentUser();

    const handleButtonClick = () => {
        setIsOpened(!isOpened);
    };

    const handleClose = () => {
        setIsOpened(false);
    };

    const call = async (isMobile: boolean) => {
        if (!repairOrderId) return;
        setIsConnectingCallOpened(true);
        setError(undefined);
        setIsOpened(false);
        setLoading(true);
        try {
            await OrderAPI.phoneCall(repairOrderId, isMobile);
            onCall();
        } catch (e: unknown) {
            if (typeof e === 'object' && e) {
                if (e instanceof Error) {
                    setError(e.message);
                } else if (typeof (e as { toString: unknown }).toString === 'function') {
                    setError(e.toString());
                }
            }
        } finally {
            setLoading(false);
        }
    };

    const handleConnectingCallClose = () => {
        setIsConnectingCallOpened(false);
    };

    if (!repairOrderId) return null;

    return (
        <>
            <Button
                w="md"
                cmosVariant={'stroke'}
                className={clsx(className)}
                onClick={handleButtonClick}
                ref={button}
            >
                {showLoader ? (
                    <div className={styles.loaderContainer}>
                        <CircularProgress size={17} style={{ color: 'var(--cm2)' }} />
                    </div>
                ) : (
                    <PhoneIcon />
                )}
                <span className={styles.caption}>{t('communication.callCustomer')}</span>
                {isOpened ? <UpIcon /> : <DownIcon />}
            </Button>
            <Popover
                id={'popover'}
                open={isOpened}
                anchorEl={button.current}
                onClose={handleClose}
                elevation={0}
                transitionDuration={0}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: -8,
                    horizontal: 'left',
                }}
            >
                <div className={styles.container} style={{ minWidth: button.current?.offsetWidth }}>
                    {canCallLandline && (
                        <ArrowTooltip
                            content={t('orders.preview.masterPhoneCallTooltip')}
                            position="left"
                            disabled={user.role !== 'Master'}
                        >
                            <div>
                                <button
                                    disabled={user.role === 'Master'}
                                    className={styles.item}
                                    onClick={() => call(false)}
                                >
                                    {t('communication.callLandline')}
                                </button>
                                <div className={styles.line} />
                            </div>
                        </ArrowTooltip>
                    )}

                    {canCallMobile && (
                        <ArrowTooltip
                            content={t('orders.preview.masterPhoneCallTooltip')}
                            position="left"
                            disabled={user.role !== 'Master'}
                        >
                            <div>
                                <button
                                    disabled={user.role === 'Master'}
                                    className={styles.item}
                                    onClick={() => call(true)}
                                >
                                    {t('communication.callMobile')}
                                </button>
                                <div className={styles.line} />
                            </div>
                        </ArrowTooltip>
                    )}

                    <button
                        className={styles.item}
                        disabled={!viewCallLogEnabled}
                        onClick={() => {
                            onViewCallLog();
                            setIsOpened(false);
                        }}
                    >
                        {t('communication.viewCallsLog')}
                    </button>
                </div>
            </Popover>
            <ConnectingCall
                isLoading={showLoader}
                callFailed={!!error}
                errorMessage={error}
                open={isConnectingCallOpened}
                onClose={handleConnectingCallClose}
            />
        </>
    );
}

export default CallCustomer;
