import { styled } from '@mui/material';
import { SideArrowForMessageIcon } from 'common/components/Icons/SideArrowForMessageIcon';
import moment from 'moment';
import ReplyButtons from 'views/MassiveSending/ReplyButtons';
import whatsappBackground from '../../../assets/images/whatsapp-background.jpg';

export type WhatsAppMessagePreviewProps = {
    hasButtons: boolean;
    text: React.ReactNode;
    image?: string | null;
    dateTime: string;
    imageHeight?: number;
};

const Background = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    backgroundPosition: 'center',
    padding: '25px',
    width: '100%',
    background: `url(${whatsappBackground})`,
    boxSizing: 'border-box',
});

const MessageBlock = styled('div')({
    backgroundColor: '#FFFFFF',
    borderRadius: '0 5px 5px 5px',
    position: 'relative',
});

const TextWrapper = styled('div')({
    padding: 8,
    position: 'relative',
});

const ImageBlock = styled('div')({
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '100%',
    maxWidth: 269,
    height: 'max-content',
    marginBottom: 16,
    '& > img': {
        width: '100%',
        borderRadius: 8,
    },
});

const Text = styled('div')(({ theme }) => ({
    width: 269,
    fontFamily: 'Roboto, sans-serif',
    fontStyle: 'normal',
    fontWeight: 'normal',
    fontSize: '12px',
    lineHeight: '14px',
    color: theme.palette.neutral[7],
    paddingBottom: 12,
    wordBreak: 'break-word',
}));

const TimeBlock = styled('div')(({ theme }) => ({
    position: 'absolute',
    bottom: 8,
    right: 8,

    fontFamily: 'Inter, sans-serif',
    fontStyle: 'normal',
    fontWeight: 'normal',
    fontSize: '10px',
    lineHeight: '12px',
    color: theme.palette.neutral[5],
}));

const ArrowIcon = styled(SideArrowForMessageIcon)({
    position: 'absolute',
    top: 0,
    left: -11,
});

export default function WhatsAppMessagePreview({
    hasButtons,
    text,
    image,
    dateTime,
    imageHeight = 150,
}: WhatsAppMessagePreviewProps) {
    return (
        <Background>
            <MessageBlock>
                <TextWrapper>
                    {image && (
                        <ImageBlock>
                            <img src={image} />
                        </ImageBlock>
                    )}
                    <Text>{text}</Text>
                    <ArrowIcon fill="#FFFFFF" />
                    <TimeBlock>{`${moment(dateTime).format('HH:mm')}hrs`}</TimeBlock>
                </TextWrapper>
                {hasButtons && <ReplyButtons previewMode />}
            </MessageBlock>
        </Background>
    );
}
