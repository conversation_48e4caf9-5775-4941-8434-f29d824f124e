import { ArrowBack } from '@mui/icons-material';
import { styled } from '@mui/material';
import { UserPermissionDto } from 'api/account';
import { SessionLogic } from 'business/SessionLogic';
import { Button } from 'common/components/Button';
import Container from 'common/components/Container';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { ENTERPRISE_ROUTES, ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { selectUserPermission } from 'store/slices/user/selectors';

type NotAuthorizedPagePlaceholderProps = {
    devErrorMessage?: string;
    title?: string;
    text?: string;
    hint?: string;
};

const Content = styled('div')({
    paddingTop: 20,
});

const Header = styled('h1')(({ theme }) => ({
    ...theme.typography.h1Inter,
    fontWeight: 'bold',
    marginTop: 60,
}));

const Buttons = styled('div')({
    display: 'flex',
    gap: 10,
    marginTop: 40,
});

const Text = styled('p')({
    fontSize: '16px',
    whiteSpace: 'preserve-breaks',
});

const Code = styled('code')(({ theme }) => ({
    color: theme.palette.neutral[4],
    whiteSpace: 'pre',

    ':hover': {
        color: 'black',
    },
}));

function NotAuthorizedPagePlaceholder({
    devErrorMessage = 'insufficient_permissions:generic',
    title,
    text,
    hint,
}: NotAuthorizedPagePlaceholderProps) {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const isEnterprise = useIsEnterpriseRoute();
    const goBack = useCallback(() => navigate(-1), [navigate]);

    return (
        <Container>
            <Header>{title ?? t('toasters.insufficientPermissionsTitle')}</Header>

            <Content>
                <Text>{text ?? t('toasters.insufficientPermissionsBody')}</Text>

                {hint && <Text>{hint}</Text>}

                <Buttons>
                    <Button onClick={goBack}>
                        <ArrowBack />
                        {t('commonLabels.goBack')}
                    </Button>
                    <Button
                        cmosVariant="stroke"
                        href={isEnterprise ? ENTERPRISE_ROUTES.ORDERS : ROUTES.ORDERS}
                    >
                        <CarIcon />
                        {t('titles.orders')}
                    </Button>
                </Buttons>
                <p>
                    <Code>{devErrorMessage}</Code>
                </p>
            </Content>
        </Container>
    );
}

export type AuthorizationGateProps = {
    show: boolean;
    children?: React.ReactElement;
} & NotAuthorizedPagePlaceholderProps;

export function AuthorizationGate({ show, children, ...props }: AuthorizationGateProps) {
    if (!show) {
        return children;
    }
    return <NotAuthorizedPagePlaceholder {...props} />;
}

export type PermissionsGateProps = Omit<AuthorizationGateProps, 'show'> & {
    predicate:
        | keyof UserPermissionDto
        | (keyof UserPermissionDto)[]
        | ((permissions: UserPermissionDto) => boolean);
};

export function PermissionsGate({ predicate, devErrorMessage, ...props }: PermissionsGateProps) {
    const permissions = useSelector(selectUserPermission);
    const userId = SessionLogic.getUserId();
    const { hasAccess, devMessage } = useMemo(() => {
        let hasAccess = false,
            devMessage = '';
        if (Array.isArray(predicate)) {
            hasAccess = true;
            devMessage = predicate.join(' & ');
            for (const perm of predicate) {
                if (!permissions[perm]) {
                    hasAccess = false;
                    break;
                }
            }
        } else if (typeof predicate === 'function') {
            hasAccess = predicate(permissions);
            devMessage = 'custom';
        } else {
            hasAccess = permissions[predicate];
            devMessage = predicate;
        }

        return { hasAccess, devMessage: `Required permissions: ${devMessage}\n(userId=${userId})` };
    }, [permissions, predicate, userId]);

    return (
        <AuthorizationGate
            {...props}
            show={!hasAccess}
            devErrorMessage={devErrorMessage ?? devMessage}
        />
    );
}
