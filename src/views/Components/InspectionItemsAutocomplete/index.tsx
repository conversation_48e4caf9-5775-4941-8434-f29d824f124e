import {
    autocompleteClasses,
    CircularProgress,
    InputAdornment,
    ListItem,
    SxProps,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import EnterpriseInspectionFormsApi from 'api/enterprise/settings/inspectionForms';
import { MasterItemDto } from 'api/MasterItem';
import { TemplateItemsSettings } from 'api/settings/InspectionForms/TemplateItemsSettings';
import { TextField } from 'common/components/mui';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { normalizeAccent } from 'common/Helpers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectIsEnterprise } from 'store/slices/globalSettingsSlice';
import { useDebounce } from 'use-debounce';

type InspectionItemsAutocompleteProps = {
    value?: string;
    templateId: number;
    isLoading: boolean;
    onChange: (text: string) => void;
    styles?: { root?: SxProps; input?: SxProps };
};

type Item =
    | {
          type: 'existing';
          value: MasterItemDto;
      }
    | {
          type: 'addNew';
          text: string;
      };

export default function InspectionItemsAutocomplete({
    value: initialValue,
    templateId,
    isLoading,
    onChange,
    styles,
}: InspectionItemsAutocompleteProps) {
    const { t } = useAppTranslation();
    const [value, setValue] = useState(initialValue ?? '');
    const [debouncedSearchText] = useDebounce(value, 200);
    const isEnterprise = useAppSelector(selectIsEnterprise);

    const { data } = useQuery(
        ['settings', 'inspection-items', debouncedSearchText],
        () =>
            isEnterprise
                ? EnterpriseInspectionFormsApi.searchMasterItems(templateId, debouncedSearchText)
                : TemplateItemsSettings.searchMasterItems(templateId, debouncedSearchText),
        { keepPreviousData: true }
    );

    const options = useMemo(() => {
        if (data !== undefined) {
            return data.map((x) => ({ type: 'existing', value: x } as Item));
        }
        return [];
    }, [data]);

    return (
        <SAutocomplete<Item>
            disableListWrap
            selectOnFocus
            clearOnBlur
            handleHomeEndKeys
            sx={{
                [`& .${autocompleteClasses.input}`]: {
                    padding: '1px 8px !important',
                },
                ...styles?.root,
            }}
            renderOption={(props, option) => (
                <ListItem {...props}>
                    {option.type === 'existing' ? option.value.name : <>Add "{option.text}"</>}
                </ListItem>
            )}
            getOptionLabel={(option) =>
                option.type === 'existing' ? option.value.name : option.text
            }
            getOptionKey={(option) => (option.type === 'existing' ? option.value.id : -1)}
            options={options}
            filterOptions={(options, state) => {
                const normalizedQuery = normalizeAccent(state.inputValue.trim());
                if (normalizedQuery === '') return [];

                if (
                    !options.find(
                        (x) =>
                            x.type === 'existing' &&
                            normalizeAccent(x.value.name) === normalizedQuery
                    )
                ) {
                    return [
                        ...options,
                        {
                            type: 'addNew',
                            text: state.inputValue,
                        },
                    ];
                }

                return options;
            }}
            onInputChange={(_, v, reason) => {
                if (reason !== 'reset') setValue(v);
            }}
            onChange={(_event, option) =>
                onChange(option.type === 'existing' ? option.value.name : option.text)
            }
            value={null}
            disabled={isLoading}
            renderInput={({ InputProps, ...params }) => (
                <TextField
                    {...params}
                    sx={styles?.input}
                    cmosVariant="roundedGrey"
                    placeholder={t(
                        'inspectionForms.templateItems.newInspectionItemField.placeholder'
                    )}
                    InputProps={{
                        ...InputProps,
                        endAdornment: isLoading ? (
                            <InputAdornment position="end">
                                <CircularProgress thickness={4} size={16} />
                            </InputAdornment>
                        ) : undefined,
                    }}
                />
            )}
        />
    );
}
