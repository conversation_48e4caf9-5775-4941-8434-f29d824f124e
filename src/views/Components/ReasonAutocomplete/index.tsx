import { Autocomplete, CircularProgress, ListItemButton } from '@mui/material';
import TextField from '@mui/material/TextField';
import { useQuery } from '@tanstack/react-query';
import { AppointmentReasonCreateDto, AppointmentReasonDto } from 'api/appointments';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { useAutoCompleteCommonStyles } from '../common';
import useStyles from './css';
import { useCreateReasonMutation } from './helper';

type ReasonAutocompleteProps = {
    cacheKey: string;
    searchReasons: (query: string) => Promise<AppointmentReasonDto[]>;
    getFrequentReasons: () => Promise<AppointmentReasonDto[]>;
    createReason: (reason: AppointmentReasonCreateDto) => Promise<AppointmentReasonDto>;
    disabled?: boolean;
    excludeItems?: AppointmentReasonDto[];
    onChange?: (item: AppointmentReasonDto) => void;
};

const emptyList = [] as AppointmentReasonDto[];

type ReasonAutocompleteItem =
    | {
          new: false;
          reason: AppointmentReasonDto;
      }
    | {
          new: true;
          name: string;
      };

const ReasonAutocomplete = ({
    disabled,
    excludeItems = emptyList,
    cacheKey,
    onChange,
    createReason,
    searchReasons,
    getFrequentReasons,
}: ReasonAutocompleteProps) => {
    const { t } = useAppTranslation();
    const classes = useStyles();
    const commonStyles = useAutoCompleteCommonStyles();
    const [open, setOpen] = useState<boolean>(false);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const normalizedQuery = useMemo(() => searchQuery.trim(), [searchQuery]);
    const [debouncedQuery] = useDebounce(normalizedQuery, 100);

    const {
        data: reasonsData,
        isFetching: reasonsIsFetching,
        fetchStatus,
    } = useQuery(
        ['appointment', 'reasons', cacheKey, debouncedQuery],
        () => searchReasons(debouncedQuery),
        {
            cacheTime: 0,
            enabled: debouncedQuery !== '',
        }
    );
    const reasonsIsLoading = reasonsIsFetching && fetchStatus === 'fetching';

    const { data: frequentReasons, isLoading: frequentsAreLoading } = useQuery(
        ['appointment', 'reasons', 'frequent', cacheKey],
        getFrequentReasons,
        {
            cacheTime: 30000,
            enabled: open,
            staleTime: 10000,
        }
    );

    const frequents = useMemo(
        () =>
            (frequentReasons || []).filter(
                (item) => excludeItems.findIndex((excluded) => excluded.id === item?.id) === -1
            ),
        [frequentReasons, excludeItems]
    );

    const filterReasons = useMemo(
        () =>
            (reasonsData || []).filter(
                (item) =>
                    excludeItems?.findIndex(
                        (excluded) => item !== null && excluded.id === item.id
                    ) === -1
            ) || [],
        [reasonsData, excludeItems]
    );

    const reasonsOptions: ReasonAutocompleteItem[] = useMemo(() => {
        if (debouncedQuery.length) {
            if (filterReasons.length) {
                return filterReasons.map((item) => ({
                    new: false,
                    reason: item,
                }));
            } else {
                return [
                    {
                        new: true,
                        name: debouncedQuery,
                    },
                ];
            }
        } else {
            return frequents.map((item) => ({
                new: false,
                reason: item,
            }));
        }
    }, [filterReasons, frequents, debouncedQuery]);

    const { mutate: createReasonMutate } = useCreateReasonMutation(createReason, (data) => {
        onChange && onChange(data);
    });

    const autocompleteIsLoading = reasonsIsLoading || frequentsAreLoading;
    return (
        <Autocomplete<ReasonAutocompleteItem>
            id="reason-autocomplete"
            autoComplete
            autoHighlight
            disabled={disabled}
            open={open && !disabled}
            options={autocompleteIsLoading ? [] : reasonsOptions}
            inputValue={searchQuery}
            value={null}
            classes={{ paper: commonStyles.paperAutoComplete }}
            loading={reasonsIsLoading || frequentsAreLoading}
            loadingText={<CircularProgress style={{ color: Colors.CM2 }} size={20} />}
            noOptionsText={t('commonLabels.noDataSelector')}
            renderInput={useCallback(
                (params) => (
                    <TextField
                        {...params}
                        fullWidth
                        placeholder={t('appointments.step2.selectOrAddTheReasonForTheAppointment')}
                        InputProps={{
                            ...params.InputProps,
                            type: 'text',
                            className: commonStyles.root,
                            classes: {
                                focused: commonStyles.inputFocused,
                            },
                        }}
                    />
                ),
                [commonStyles, t]
            )}
            filterOptions={(options) => options}
            renderOption={(props, option) => {
                if (option.new) {
                    return (
                        <ListItemButton
                            disableRipple
                            component="li"
                            {...props}
                            className={classes.item}
                        >
                            <PlusIcon size={15} style={{ marginRight: 2 }} />
                            <div>{option.name}</div>
                        </ListItemButton>
                    );
                } else {
                    return (
                        <ListItemButton
                            disableRipple
                            component="li"
                            {...props}
                            className={classes.item}
                        >
                            {option.reason.name}
                        </ListItemButton>
                    );
                }
            }}
            isOptionEqualToValue={(_option, _value) => false}
            getOptionLabel={(option) => (option.new ? option.name : option.reason.name)}
            onChange={(_e, value) => {
                if (!value) return;
                if (value.new) {
                    createReasonMutate({
                        name: value.name,
                    });
                } else {
                    onChange && onChange(value.reason);
                }
                setSearchQuery('');
            }}
            onInputChange={(_e, value, reason) => {
                setSearchQuery(value);
            }}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
        />
    );
};

export default ReasonAutocomplete;
