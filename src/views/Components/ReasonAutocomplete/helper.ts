import { useMutation } from '@tanstack/react-query';
import { AppointmentReasonCreateDto, AppointmentReasonDto } from 'api/appointments';
import axios from 'axios';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';

export const createGuid = (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
};

export const useCreateReasonMutation = (
    createReason: (reason: AppointmentReasonCreateDto) => Promise<AppointmentReasonDto>,
    onSuccess?: (data: AppointmentReasonDto) => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const mutation = useMutation(createReason, {
        onSuccess: (data) => {
            onSuccess && onSuccess(data);
        },
        onError: (err) => {
            if (axios.isAxiosError(err) && err.response?.status === 409) {
                toasters.info(
                    t('appointments.step2.appointmentReasonDuplicated'),
                    t('toasters.errorOccurred')
                );
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        },
    });

    return mutation;
};
