import CustomersApi from 'api/customers';
import CustomerVehiclesAutocompleteCustomApi, {
    CustomerVehiclesAutocompleteCustomApiProps,
} from './CustomerVehiclesAutocompleteCustomApi';

type CustomerVehiclesAutocompleteProps = Omit<
    CustomerVehiclesAutocompleteCustomApiProps,
    'getVehicles' | 'cacheKey'
>;

/**
 * default implementation of CustomerVehiclesAutocomplete that uses
 * repair shop api
 */
export default function CustomerVehiclesAutocomplete(props: CustomerVehiclesAutocompleteProps) {
    return (
        <CustomerVehiclesAutocompleteCustomApi
            getVehicles={CustomersApi.getCustomerVehicles}
            cacheKey="default"
            {...props}
        />
    );
}
