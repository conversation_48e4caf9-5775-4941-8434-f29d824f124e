import { Add } from '@mui/icons-material';
import {
    Autocomplete,
    Box,
    Button,
    ListItemButton,
    TextField as MuiTextField,
    Paper,
    PaperProps,
    styled,
    Typography,
} from '@mui/material';
import { QueryKey, useQuery, useQueryClient } from '@tanstack/react-query';
import { VehicleListItemDto } from 'api/customers';
import { useCustomersSearchApi } from 'api/customers/dynamic-api';
import { getErrorMessage } from 'api/error';
import { normalizeAccent } from 'common/Helpers/helper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import { forwardRef, HTMLAttributes, useCallback, useEffect, useMemo, useState } from 'react';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import { SimpleErrorDisplay2 } from 'utils/errorsHandling/SimpleErrorBoundary2';
import { useAutoCompleteCommonStyles } from '../common';

export type CustomerVehiclesAutocompleteCustomApiProps = {
    onOpenPopup: () => void;
    getVehicles: (customerId: string) => Promise<VehicleListItemDto[]>;
    cacheKey: string;
    customerId?: string | null;
    value?: VehicleListItemDto | string | null;
    initialValue?: VehicleListItemDto | null;
    onSelect: (vehicle?: VehicleListItemDto) => void;
    onAutoSelect?: (vehicle: VehicleListItemDto) => void;
    className?: string;
    disabled?: boolean;
};

const AddButton = styled(Button)(({ theme }) => ({
    textTransform: 'none',
    background: theme.palette.neutral[2],
    borderRadius: 100,
    border: `1px solid ${theme.palette.neutral[4]}`,
    color: theme.palette.neutral[8],
    height: 32,
    alignItems: 'center',
    verticalAlign: 'center',
    display: 'flex',
    gap: 5,
    paddingLeft: 12,
}));

type HeaderData = {
    onOpenPopup: () => void;
    error?: unknown;
    retry: () => void;
    allowEditVehicles: boolean;
};

const PaperComponent = forwardRef(
    (
        {
            children,
            _headerData,
            ...props
        }: React.HTMLAttributes<HTMLDivElement> & { _headerData?: HeaderData },
        _ref
    ) => {
        const { t } = useAppTranslation();

        if (!_headerData) return null;

        const { onOpenPopup, error, retry, allowEditVehicles } = _headerData;

        return (
            <Paper {...props}>
                <Box
                    sx={{ padding: '12px 6px 0 6px' }}
                    onMouseDown={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                    }}
                >
                    <AddButton
                        disabled={!allowEditVehicles}
                        onClick={allowEditVehicles ? () => onOpenPopup() : undefined}
                    >
                        <span>{t('appointments.step1.createNewVehicle')}</span>
                        <Add />
                    </AddButton>
                </Box>

                {!!error && (
                    <SimpleErrorDisplay2
                        sx={{
                            position: 'static',
                        }}
                        message={getErrorMessage(error)}
                        onRetry={retry}
                    />
                )}

                {children}
            </Paper>
        );
    }
);

export default function CustomerVehiclesAutocompleteCustomApi({
    onOpenPopup,
    getVehicles,
    cacheKey,
    value: valueOrId,
    initialValue,
    onSelect,
    onAutoSelect,
    customerId,
    className,
    disabled,
}: CustomerVehiclesAutocompleteCustomApiProps) {
    const { t } = useAppTranslation();
    const commonStyles = useAutoCompleteCommonStyles();
    const allowEditVehicles = useAppSelector((r) => selectUserPermission(r).allowEditVehicles);

    const { data, error, refetch } = useQuery(
        getQueryKey(customerId, cacheKey),
        () => getVehicles(customerId!),
        {
            enabled: !!customerId,
            onSuccess(data) {
                if (data.length === 1 && data[0].id !== valueOrId && !valueOrId) {
                    onAutoSelect && onAutoSelect(data[0]);
                }
            },
        }
    );

    const availableVehicles = useMemo(() => {
        const vehicles = (data ?? []).map((vehicle) => ({
            ...vehicle,
            plates: vehicle.plates || t('appointments.step1.noPlates'),
        }));
        vehicles.sort((a, b) => a.plates.localeCompare(b.plates));
        return vehicles;
    }, [data, t]);

    const value = useMemo(
        () =>
            (typeof valueOrId === 'string'
                ? availableVehicles.find((v) => v.id === valueOrId)
                : valueOrId) ?? initialValue,
        [valueOrId, availableVehicles, initialValue]
    );

    const displayedVehicles: (VehicleListItemDto & { missing?: true })[] = useMemo(() => {
        if (!value || availableVehicles.some((x) => x.id === value.id)) {
            return availableVehicles;
        }

        return [{ ...value, missing: true }, ...availableVehicles];
    }, [availableVehicles, value]);

    const [open, setOpen] = useState(false);
    const [[searchQuery, doSearch], setSearchQuery] = useState(['', true]);
    const normalizedQuery = doSearch ? normalizeAccent(searchQuery) : '';

    const filteredVehicles = useMemo(() => {
        if (normalizedQuery) {
            return displayedVehicles.filter(
                (vehicle) =>
                    normalizeAccent(vehicle.plates).includes(normalizedQuery) ||
                    normalizeAccent(vehicle.vin).includes(normalizedQuery) ||
                    normalizeAccent(vehicle.brand).includes(normalizedQuery) ||
                    normalizeAccent(vehicle.model).includes(normalizedQuery) ||
                    normalizeAccent(vehicle.color).includes(normalizedQuery) ||
                    vehicle.year.toLowerCase().includes(normalizedQuery) ||
                    // NOTE: if there is a selected value, it must be displayed no matter what
                    // to avoid issues
                    (value && value.id === vehicle.id)
            );
        } else {
            return displayedVehicles;
        }
    }, [normalizedQuery, displayedVehicles, value]);

    useEffect(() => {
        if (value) {
            setSearchQuery([vehicleToString(value, t), false]);
        } else {
            // trigger empty query (all vehicles)
            setSearchQuery(['', true]);
        }
    }, [value, t]);

    const headerData: HeaderData = {
        onOpenPopup,
        error,
        retry: refetch,
        allowEditVehicles,
    };

    return (
        <Autocomplete<VehicleListItemDto & { missing?: true }>
            className={className}
            disabled={!customerId || disabled}
            id="customer-vehicles"
            autoComplete
            autoHighlight
            open={open}
            options={filteredVehicles}
            inputValue={searchQuery}
            value={value ?? null}
            classes={{ paper: commonStyles.paperAutoComplete }}
            PaperComponent={PaperComponent}
            slotProps={{
                paper: {
                    _headerData: headerData,
                } as PaperProps, // shut up TS, we need to pass this to PaperComponent somehow
            }}
            renderInput={useCallback(
                (params) => (
                    <MuiTextField
                        {...params}
                        placeholder={t('vehicleAutocomplete.selectAVehicle')}
                        InputProps={{
                            ...params.InputProps,
                            type: 'text',
                            className: commonStyles.root,
                            classes: {
                                focused: commonStyles.inputFocused,
                            },
                        }}
                    />
                ),
                [commonStyles, t]
            )}
            filterOptions={(options) => options}
            renderOption={(props, option) => {
                return <VehicleItem vehicle={option} liProps={props} />;
            }}
            isOptionEqualToValue={(option, value) => option?.id === value?.id}
            getOptionLabel={(v) => {
                return v === null ? '' : vehicleToString(v, t);
            }}
            onChange={(event, newValue) => {
                if (newValue) {
                    onSelect(newValue);
                } else {
                    // if event.type is 'change' and newValue is null that means user
                    // deleted all input's content and we do not want to clear input in that case
                    if (event.type !== 'change') {
                        onSelect(undefined);
                    }
                }
            }}
            onInputChange={(e, value, reason) => {
                setSearchQuery([value, reason === 'input']);
            }}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
        />
    );
}
function getQueryKey(customerId: string | undefined | null, cacheKey: string): QueryKey {
    return ['vehicles', `cache-key=${cacheKey}`, `customer-id=${customerId}`];
}

function VehicleItem({
    vehicle,
    liProps,
}: {
    vehicle: VehicleListItemDto & { missing?: true };
    liProps: HTMLAttributes<HTMLLIElement>;
}) {
    const { t } = useAppTranslation();

    const brand = [vehicle.brand, vehicle.model, vehicle.year, vehicle.color]
        .filter(Boolean)
        .join(', ');
    return (
        <Item component="li" {...liProps}>
            <Typography>
                {vehicle.missing && (
                    <Typography
                        variant="h7Inter"
                        color="neutral.7"
                        sx={{ mb: 0.5, display: 'block', textDecoration: 'underline' }}
                    >
                        {t('vehicleAutocomplete.missingVehicleNotice')}
                    </Typography>
                )}
                <SpanCaption>{`${t('commonLabels.plates')}: `}</SpanCaption>
                <SpanLabel>{`${
                    vehicle.plates || `${t('appointments.step1.noPlates')}`
                }, `}</SpanLabel>
                <SpanCaption>{`${t('commonLabels.vin')}: `}</SpanCaption>
                <SpanLabel>{`${vehicle.vin || '--'}, `}</SpanLabel>
                <SpanCaption>{`${t('commonLabels.vehicle')}: `}</SpanCaption>
                <SpanLabel>{`${brand || '--'}`}</SpanLabel>
            </Typography>
        </Item>
    );
}

const SpanCaption = styled('span')({
    color: 'var(--neutral8)',
});

const SpanLabel = styled('span')({
    color: 'var(--neutral6)',
});

const Item = styled(ListItemButton)(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[8],
})) as typeof ListItemButton;

function vehicleToString(vehicle: VehicleListItemDto, t: TFunction) {
    const brand = [vehicle.brand, vehicle.model, vehicle.year, vehicle.color]
        .filter(Boolean)
        .join(', ');
    return [
        `${t('appointments.step1.plates')}: ${vehicle.plates || t('appointments.step1.noPlates')}`,
        `${t('commonLabels.vin')}: ${vehicle.vin || '--'}`,
        `${t('commonLabels.vehicle')}: ${brand || '--'}`,
    ]
        .filter(Boolean)
        .join(', ');
}

export function useVehiclesQueryModifiers() {
    const api = useCustomersSearchApi();
    const queryClient = useQueryClient();
    return useMemo(
        () => ({
            add: (customerId: string, vehicle: VehicleListItemDto) => {
                const data = queryClient.getQueryData(
                    getQueryKey(customerId, api.queryKeyPrefix)
                ) as VehicleListItemDto[] | undefined;
                if (!data) return;
                queryClient.setQueryData(getQueryKey(customerId, api.queryKeyPrefix), [
                    ...data,
                    vehicle,
                ]);
            },
        }),
        [queryClient, api.queryKeyPrefix]
    );
}
