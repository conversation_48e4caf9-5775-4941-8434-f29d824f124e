import { VehicleModelDto } from 'api/Clients/Brands';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import { KeyboardEventHandler, useCallback, useEffect, useMemo, useRef } from 'react';
import { ActionMeta } from 'react-select';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands, selectModels } from 'store/slices/clientsSlice/selectors';
import ensureModelsFetched from 'store/slices/clientsSlice/thunks/ensureModelsFetched';
import { orderActions } from 'store/slices/order/orderDetails';

export type ModelPickerProps = Omit<
    DropdownProps<VehicleModelDto | null>,
    'options' | 'onChange' | 'value'
> & {
    value?: string | null;
    onChange: (value: string | undefined, model?: VehicleModelDto) => void;
    brandName?: string;
    fieldId?: string;
    isSelected?: boolean;
    onKeyDown?: KeyboardEventHandler<Element>;
};

type ModelOption = OptionData<VehicleModelDto | null>;

export default function ModelPicker({
    fieldId,
    value,
    onChange,
    brandName,
    placeholder,
    isSelected,
    onKeyDown,
    ...props
}: ModelPickerProps) {
    const ref = useRef<HTMLSelectElement>(null);
    const brands = useAppSelector(selectBrands);
    const brandId = useMemo(
        () => brands.find((b) => b.name === brandName)?.id,
        [brands, brandName]
    );
    const models = useAppSelector((r) => selectModels(r, brandId ?? -1));
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    useEffect(() => {
        if (brandId) {
            dispatch(ensureModelsFetched({ brandId }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [brandId]);

    const options: ModelOption[] = useMemo(
        () => models.map((m) => ({ label: m.name, value: m })),
        [models]
    );
    const selected = useMemo(() => {
        const predefined = options.find((o) => o.value?.name === value);

        if (!predefined) {
            if (value) {
                return { label: value, value: null };
            } else {
                return null;
            }
        }
        return predefined;
    }, [options, value]);

    const onChangeCallback = useCallback(
        (option: ModelOption | null, { action }: ActionMeta<ModelOption>) => {
            if (option === null || option.label === value) return;
            if (onChange) {
                if (option.value && action !== 'create-option') {
                    onChange(option.value.name, option.value);
                } else {
                    // new model (custom)
                    onChange(option.label);
                }
            }
        },
        [onChange, value]
    );

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <Dropdown
            creatable
            ref={ref}
            value={selected}
            optionStyle={OptionStyle.icons}
            options={options}
            placeholder={placeholder ?? t('newCustomer.modelPlaceholder')}
            onChange={onChangeCallback}
            onEnterPress={onKeyDown}
            onBlur={() => fieldId && dispatch(orderActions.clearSelectedField({ id: fieldId }))}
            openMenuOnFocus
            {...props}
        />
    );
}
