import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { ReasonForPause } from 'common/constants/ReasonForPause';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';

export type PauseReasonPickerProps = {
    reason?: ReasonForPause | null;
    onChange: (reason: ReasonForPause | null) => void;
} & Omit<DropdownProps<ReasonForPause, false>, 'onChange' | 'value' | 'options'>;

export default function PauseReasonPicker({
    reason,
    isClearable = true,
    onChange,
    ...props
}: PauseReasonPickerProps) {
    const { t } = useAppTranslation();

    const options = useMemo(
        () => [
            {
                value: ReasonForPause.AssignmentofAnotherVehicle,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.assignmentofAnotherVehicle'),
            },
            {
                value: ReasonForPause.Lunch,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.lunch'),
            },
            {
                value: ReasonForPause.WaitingForCustomerAuthorization,
                label: t(
                    'workshopPlanner.pausedOrders.reasonsForPause.waitingForCustomerAuthorization'
                ),
            },
            {
                value: ReasonForPause.WaitingForServiceBay,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.waitingForServiceBay'),
            },
            {
                value: ReasonForPause.WaitingForTools,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.waitingForTools'),
            },
            {
                value: ReasonForPause.WaitingForTechnicianReassignment,
                label: t(
                    'workshopPlanner.pausedOrders.reasonsForPause.waitingForTechnicianReassignment'
                ),
            },
            {
                value: ReasonForPause.WaitingForParts,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.waitingForParts'),
            },
            {
                value: ReasonForPause.InWarrantyProcess,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.inWarrantyProcess'),
            },
            {
                value: ReasonForPause.TOT,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.tot'),
            },
            {
                value: ReasonForPause.Other,
                label: t('workshopPlanner.pausedOrders.reasonsForPause.other'),
            },
        ],
        [t]
    );

    const selectedOption = useMemo(
        () => options.find((o) => o.value === reason),
        [options, reason]
    );

    return (
        <Dropdown
            placeholder={t('workshopPlanner.pausedOrders.reasonsForPause.pauseFor')}
            options={options}
            value={selectedOption}
            isClearable={isClearable}
            onChange={(o) => {
                onChange(o?.value ? o.value : null);
            }}
            {...props}
        />
    );
}
