import CustomersApi from 'api/customers';
import CreateNewVehiclePopupCustomApi, {
    CreateNewVehiclePopupCustomApiProps,
} from './CreateNewVehiclePopupCustomApi';

type CreateNewVehiclePopupProps = Omit<CreateNewVehiclePopupCustomApiProps, 'createNewVehicle'>;

export default function CreateNewVehiclePopup(props: CreateNewVehiclePopupProps) {
    return (
        <CreateNewVehiclePopupCustomApi createNewVehicle={CustomersApi.createVehicle} {...props} />
    );
}
