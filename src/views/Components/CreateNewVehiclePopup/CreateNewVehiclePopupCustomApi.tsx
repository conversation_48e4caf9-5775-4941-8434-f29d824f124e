import { Error as ErrorIcon } from '@mui/icons-material';
import { Alert, Box, IconButton, styled } from '@mui/material';
import { VehicleCreateDto, VehicleDetailsDto, VehiclesCrmApi } from 'api/customers';
import { AxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { NumberFormField } from 'common/components/Inputs/NumberField';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useCallback, useState } from 'react';
import { useAppSelector } from 'store';
import { selectUserPermission } from 'store/slices/user';
import BrandPicker from 'views/Components/BrandPicker';
import ModelPicker from 'views/Components/ModelPicker';
import YearPicker from 'views/Components/YearPicker';

export type CreateNewVehiclePopupCustomApiProps = {
    onClose: () => void;
    open: boolean;
    customerId: string | null;
    repairShopKey?: string;
    onVehicleCreated: (vehicle: VehicleDetailsDto) => void;
    createNewVehicle: (data: VehicleCreateDto) => Promise<VehicleDetailsDto>;
};

export default function CreateNewVehiclePopupCustomApi({
    open,
    onClose,
    customerId,
    repairShopKey,
    onVehicleCreated,
    createNewVehicle,
}: CreateNewVehiclePopupCustomApiProps) {
    const [vehicle, setVehicle] = useState<Omit<VehicleCreateDto, 'customerId'>>({});
    const [isPopupOpen, setPopupOpen] = useState(false);
    const { t } = useAppTranslation();
    const [saving, setSaving] = useState(false);
    const { callApi } = useApiCall();
    const toasters = useToasters();
    const permissions = useAppSelector(selectUserPermission);
    const isEnterprise = useIsEnterpriseRoute();
    const canCreateVehicle = permissions.allowEditVehicles || isEnterprise;

    const hasData =
        vehicle.brand?.trim() ||
        vehicle.color?.trim() ||
        vehicle.model?.trim() ||
        vehicle.plates?.trim() ||
        vehicle.vin?.trim() ||
        vehicle.year?.trim() ||
        vehicle.mileage !== undefined;

    const hasVin = !!vehicle.vin && vehicle.vin.trim().length > 0;

    const hasPlates = !!vehicle.plates && vehicle.plates.trim().length > 0;

    const hasBrandModelAndYear =
        !!vehicle.brand &&
        vehicle.brand.trim().length > 0 &&
        !!vehicle.model &&
        vehicle.model.trim().length > 0 &&
        !!vehicle.year &&
        vehicle.year.trim().length > 0;

    const validVehicle = !!(hasBrandModelAndYear || hasPlates || hasVin) && hasData;

    const onCloseCallback = useCallback(() => {
        if (hasData) {
            setPopupOpen(true);
        } else {
            setVehicle({});
            onClose();
        }
    }, [onClose, hasData]);

    const save = async () => {
        if (!canCreateVehicle) return;
        if (!customerId || !validVehicle) return;
        setSaving(true);
        try {
            const vinDuplicatedPromise = vehicle.vin
                ? VehiclesCrmApi.checkVehicleExists({ vin: vehicle.vin })
                : Promise.resolve(false);
            const platesDuplicatedPromise = vehicle.plates
                ? VehiclesCrmApi.checkVehicleExists({ plates: vehicle.plates })
                : Promise.resolve(false);

            const vinDuplicated = await vinDuplicatedPromise;
            const platesDuplicated = await platesDuplicatedPromise;

            if (vinDuplicated && platesDuplicated) {
                toasters.danger(
                    t('newVehicle.vinAndPlatesDuplicated'),
                    t('newVehicle.vehicleNotCreated')
                );
                return;
            } else if (vinDuplicated) {
                toasters.danger(t('newVehicle.vinDuplicated'), t('newVehicle.vehicleNotCreated'));

                return;
            } else if (platesDuplicated) {
                toasters.danger(
                    t('newVehicle.platesDuplicated'),
                    t('newVehicle.vehicleNotCreated')
                );

                return;
            }

            if (vehicle.brand && vehicle.model && vehicle.year && !vehicle.vin && !vehicle.plates) {
                if (
                    await VehiclesCrmApi.checkVehicleExists({
                        brand: vehicle.brand,
                        model: vehicle.model,
                        year: vehicle.year,
                        customerId: customerId,
                    })
                ) {
                    toasters.danger(
                        t('newVehicle.brandModelYearDuplicated'),
                        t('newVehicle.vehicleNotCreated')
                    );
                    return;
                }
            }

            const newVehicle = await callApi(
                () =>
                    createNewVehicle({
                        customerId,
                        ...vehicle,
                    }),
                {
                    selectErrorContent: ({ response }: AxiosError) => {
                        return response?.status === 409
                            ? {
                                  body: t('newVehicle.vehicleDuplicated'),
                                  title: t('newVehicle.vehicleNotCreated'),
                              }
                            : {};
                    },
                }
            );
            onVehicleCreated(newVehicle);
            onClose();
            setVehicle({});
            toasters.success(
                t('newVehicle.vehicleCreatedSuccessfully'),
                t('newVehicle.vehicleCreated')
            );
        } finally {
            setSaving(false);
        }
    };

    return (
        <Modal open={open}>
            <Modal open={isPopupOpen}>
                <AreYouSureBox gap={1.25} alignItems="center" display="flex" flexDirection="column">
                    <CloseButton onClick={() => setPopupOpen(false)} size="large">
                        <CloseIcon />
                    </CloseButton>

                    <AreYouSureTitle>{t('newVehicle.areYouSure.title')}</AreYouSureTitle>

                    <SubTitle>{t('newVehicle.areYouSure.subtitle')}</SubTitle>

                    <Box display="flex" gap={2} marginTop={1}>
                        <Button
                            customStyles={{ width: 200 }}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            onClick={() => setPopupOpen(false)}
                            label={t('commonLabels.goBack')}
                        />
                        <Button
                            customStyles={{ width: 200 }}
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            onClick={() => {
                                setPopupOpen(false);
                                setVehicle({});
                                onClose();
                            }}
                            label={t('newVehicle.areYouSure.yes')}
                        />
                    </Box>
                </AreYouSureBox>
            </Modal>
            <Root>
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        mb: 4,
                    }}
                >
                    <div>
                        <Title>{t('newVehicle.title')}</Title>
                    </div>

                    <Box display="flex" gap={1.25}>
                        <Button
                            onClick={onCloseCallback}
                            label={t('commonLabels.goBack')}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            blockMode
                        />
                        <Button
                            onClick={save}
                            disabled={!validVehicle || saving}
                            customStyles={{ width: 400 }}
                            label={t('newVehicle.create')}
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            showLoader={saving}
                            blockMode
                        />
                    </Box>
                </Box>

                {!canCreateVehicle && (
                    <Alert color="error" icon={<ErrorIcon />}>
                        {t('toasters.insufficientPermissionsTitle')}
                    </Alert>
                )}

                <p style={{ color: 'var(--neutral7)', display: 'flex', alignItems: 'center' }}>
                    <Box
                        component="span"
                        sx={{
                            backgroundColor: 'var(--neutral7)',
                            color: 'var(--neutral1)',
                            borderRadius: 100,
                            height: 16,
                            width: 16,
                            display: 'inline-flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mr: '8px',
                        }}
                    >
                        <InfoIcon size={14} fill="currentColor" />
                    </Box>
                    <span style={{ display: 'inline-flex' }}>{t('newCustomer.vehicleNotice')}</span>
                </p>

                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr 1fr',
                        gap: '10px 20px',
                    }}
                >
                    <TextFormField
                        disabled={saving || !canCreateVehicle}
                        name="plates"
                        label={t('commonLabels.plates')}
                        placeholder={t('newVehicle.platesPlaceholder')}
                        value={vehicle.plates}
                        onChange={(e) =>
                            setVehicle({
                                ...vehicle,
                                plates: e.target.value?.toUpperCase(),
                            })
                        }
                        cmosVariant="grey"
                    />
                    <TextFormField
                        disabled={saving || !canCreateVehicle}
                        name="vin"
                        label={t('commonLabels.vin')}
                        placeholder={t('commonLabels.vinPlaceholder')}
                        value={vehicle.vin}
                        onChange={(e) =>
                            setVehicle((v) => ({ ...v, vin: e.target.value?.toUpperCase() }))
                        }
                        cmosVariant="grey"
                    />
                    <NumberFormField
                        disabled={saving || !canCreateVehicle}
                        name="mileage"
                        label={t('commonLabels.mileage')}
                        placeholder={t('commonLabels.mileagePlaceholder')}
                        value={vehicle.mileage}
                        onValueChange={(value) =>
                            setVehicle({ ...vehicle, mileage: value.floatValue })
                        }
                        cmosVariant="grey"
                    />

                    <BrandPicker
                        placeholder={t('newVehicle.brandPlaceholder')}
                        disabled={saving || !canCreateVehicle}
                        value={vehicle.brand ?? undefined}
                        enterpriseRepairShopKey={repairShopKey}
                        onChange={(brandName) => {
                            setVehicle((v) => ({
                                ...v,
                                brand: brandName ?? null,
                                model: null,
                                year: null,
                            }));
                        }}
                        label={t('commonLabels.make')}
                        name="brand"
                        cmosVariant="grey"
                    />
                    <ModelPicker
                        isInvalid={!validVehicle && !!vehicle.brand && !vehicle.model}
                        disabled={saving || !canCreateVehicle}
                        brandName={vehicle.brand ?? undefined}
                        value={vehicle.model}
                        onChange={(modelName) => {
                            setVehicle((v) => ({ ...v, model: modelName ?? null, year: null }));
                        }}
                        label={t('commonLabels.model')}
                        placeholder={t('newVehicle.modelPlaceholder')}
                        name="model"
                        cmosVariant="grey"
                    />
                    <YearPicker
                        isInvalid={!validVehicle && !!vehicle.brand}
                        disabled={saving || !canCreateVehicle}
                        brandName={vehicle.brand ?? undefined}
                        modelName={vehicle.model ?? undefined}
                        value={vehicle.year}
                        onChange={(year) =>
                            setVehicle((v) => ({
                                ...v,
                                year,
                            }))
                        }
                        placeholder={t('newVehicle.yearPlaceholder')}
                        label={t('commonLabels.year')}
                        name="year"
                        cmosVariant="grey"
                    />
                    <TextFormField
                        disabled={saving || !canCreateVehicle}
                        name="color"
                        label={t('newVehicle.color')}
                        placeholder={t('newVehicle.colorPlaceholder')}
                        value={vehicle.color ?? ''}
                        onChange={(e) => setVehicle((v) => ({ ...v, color: e.target.value }))}
                        cmosVariant="grey"
                    />
                </Box>
            </Root>
        </Modal>
    );
}

const Root = styled('div')({
    padding: '45px 80px',
    width: 900,
});

const Title = styled('h4')(({ theme }) => ({
    ...theme.typography.h4Inter,
    margin: 0,
    color: theme.palette.neutral[7],
}));

const SubTitle = styled('span')(({ theme }) => ({
    ...theme.typography.body1,
    color: theme.palette.neutral[7],
}));

const AreYouSureBox = styled(Box)({
    padding: '40px 30px 30px 30px',
    position: 'relative',
});

const AreYouSureTitle = styled('h4')(({ theme }) => ({
    ...theme.typography.h4Inter,
    margin: 0,
    color: theme.palette.neutral[8],
}));

const CloseButton = styled(IconButton)({
    position: 'absolute',
    top: 0,
    right: 0,
});
