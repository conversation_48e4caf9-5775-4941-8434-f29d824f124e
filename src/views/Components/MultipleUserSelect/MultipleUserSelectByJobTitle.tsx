import { JobTitle } from 'common/constants';
import { useUsers } from 'common/hooks/useUsers';
import { useEffect, useMemo } from 'react';
import MultipleUserSelect, { MultipleUserSelectProps } from './MultipleUserSelect';

export type MultipleUserSelectByJobTitleProps = Omit<MultipleUserSelectProps, 'userIds'> & {
    jobTitle: JobTitle;
};

export default function MultipleUserSelectByJobTitle({
    jobTitle,
    shopId,
    ...props
}: MultipleUserSelectByJobTitleProps) {
    const users = useUsers(shopId);
    const ids = useMemo(
        () => users.filter((x) => x.job === jobTitle).map((x) => x.key),
        [users, jobTitle]
    );

    useEffect(() => {
        console.log('users', jobTitle, users);
    }, [jobTitle, users]);

    return <MultipleUserSelect userIds={ids} shopId={shopId} {...props} />;
}
