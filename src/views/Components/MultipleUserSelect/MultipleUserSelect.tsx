import { ExpandMore } from '@mui/icons-material';
import {
    InputAdornment,
    ListItemButton,
    outlinedInputClasses,
    styled,
    SxProps,
    Theme,
} from '@mui/material';
import { UserListItem } from 'api/users';
import { PropsWithTestId } from 'common/components';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { TextField } from 'common/components/mui';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useUsers } from 'common/hooks/useUsers';
import React, { HTMLAttributes, useCallback, useEffect, useMemo, useState } from 'react';

export type MultipleUserSelectProps = PropsWithTestId<{
    id?: string;
    shopId?: string;
    value?: string[] | null;
    userIds?: string[];
    onChange?: (userIds: string[], users: UserListItem[]) => void;
    onBlur?: () => void;
    sx?: SxProps<Theme>;
    slotProps?: {
        textField?: React.ComponentProps<typeof TextField>;
    };
    ref?: React.ForwardedRef<HTMLInputElement>;
    disabled?: boolean;
}>;

export default function MultipleUserSelect({
    id,
    shopId,
    value: selectedUserIds,
    userIds,
    onChange,
    onBlur,
    dataTestId,
    sx,
    slotProps = {},
    ref,
    disabled,
}: MultipleUserSelectProps) {
    const { t } = useAppTranslation();

    const [open, setOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState<UserListItem[]>([]);
    const allUsers = useUsers(shopId);

    const listedUsers = useMemo(() => {
        let users: UserListItem[];
        if (userIds) {
            users = allUsers.filter((x) => userIds.includes(x.key));
        } else {
            users = Object.values(allUsers);
        }

        if (userIds && selectedUserIds) {
            const missingIds = selectedUserIds.filter((id) => !userIds.includes(id));
            const missingUsers = allUsers.filter((x) => missingIds.includes(x.key));
            users = [...missingUsers, ...users];
        }

        users.sort((a, b) => a.name.localeCompare(b.name));

        return users;
    }, [allUsers, selectedUserIds, userIds]);

    const passedValue = useMemo(() => {
        if (!selectedUserIds) return [];

        const users: UserListItem[] = [];

        for (const id of selectedUserIds) {
            const user = allUsers.find((x) => x.key === id);
            if (user) {
                users.push(user);
            }
        }

        return users;
    }, [allUsers, selectedUserIds]);

    useEffect(() => {
        if (selectedUserIds) {
            setSelectedValue(passedValue);
        }
    }, [passedValue, selectedUserIds]);

    const handleChange = useCallback((_: React.SyntheticEvent, newValue: UserListItem[]) => {
        setSelectedValue(newValue);
    }, []);

    function handleClose() {
        setOpen(false);
        setSelectedValue(passedValue);
        if (!onChange) return;
        const newIds = selectedValue.map((x) => x.key);
        onChange(newIds, selectedValue);
    }

    function handleOpen() {
        setOpen(true);
    }

    return (
        <StyledAutocomplete<UserListItem, true>
            id={id}
            sx={sx}
            ref={ref}
            data-test-id={dataTestId}
            popupIcon={<DownIcon style={{ backgroundColor: '#fff' }} />}
            value={open ? selectedValue : passedValue}
            noOptionsText={t('commonLabels.noDataSelector')}
            getOptionLabel={(x) => (x ? x.name : '')}
            onChange={handleChange}
            onBlur={onBlur}
            renderInput={({ InputProps, ...params }) => {
                const { InputProps: slottedInputProps, ...slottedProps } =
                    slotProps.textField ?? {};

                return (
                    <TextField
                        {...params}
                        ref={ref}
                        sx={{
                            [`& .${outlinedInputClasses.input}`]: { padding: '0px !important' },
                        }}
                        margin="normal"
                        placeholder={selectedValue.length === 0 ? t('commonLabels.technician') : ''}
                        InputProps={{
                            ...InputProps,
                            endAdornment: (
                                <InputAdornment position="end">
                                    <ExpandMore style={{ color: 'var(--neutral9)' }} />
                                </InputAdornment>
                            ),
                            ...slottedInputProps,
                        }}
                        {...slottedProps}
                    />
                );
            }}
            renderOption={useCallback(
                (props: HTMLAttributes<HTMLLIElement>, user: UserListItem) => {
                    return (
                        <ListItemButton component="li" {...props}>
                            {selectedValue.some((x) => x.id === user.id) ? (
                                <CheckBoxIcon />
                            ) : (
                                <UncheckBoxIcon />
                            )}
                            <span style={{ marginLeft: 4 }}>
                                {user.initials ? `${user.initials} - ` : ''}
                                {user.name}
                            </span>
                        </ListItemButton>
                    );
                },
                [selectedValue]
            )}
            options={listedUsers}
            disableCloseOnSelect
            disableClearable
            renderTags={renderUserTags}
            open={open}
            onOpen={handleOpen}
            onClose={handleClose}
            openOnFocus
            multiple
            disabled={disabled}
        />
    );
}

const StyledAutocomplete = styled(SAutocomplete)({
    [`&:hover .${outlinedInputClasses.notchedOutline}`]: {
        backgroundColor: 'transparent !important',
    },

    [`& .${outlinedInputClasses.root}`]: {
        paddingLeft: 13,
    },
}) as typeof SAutocomplete;

function renderUserTags(users: UserListItem[]) {
    if (users.length === 0) return null;

    if (users.length <= 2) {
        return users.map((x) => x.name).join(', ');
    }

    return `${users[0].name}, ${users[1].name}, +${users.length - 2}`;
}
