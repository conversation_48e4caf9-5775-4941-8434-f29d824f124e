import { styled } from '@mui/material/styles';

type IconCounterProps = {
    count: number;
    icon: JSX.Element;
};
const IconCounter = ({ icon, count }: IconCounterProps) => {
    return (
        <Container>
            {icon}
            <Count>{count}</Count>
        </Container>
    );
};

const Container = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    gap: '3px',
});

const Count = styled('div')(({ theme }) => ({
    fontFamily: 'Roboto, sans-serif',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '12px',
    lineHeight: '14px',
    color: theme.palette.primary.main,
    display: 'flex',
    alignItems: 'center',
}));

export default IconCounter;
