import {
    Autocomplete,
    Button,
    Click<PERSON><PERSON><PERSON><PERSON><PERSON>,
    ListItemButton,
    TextField as MuiTextField,
    autocompleteClasses,
    buttonClasses,
    listItemButtonClasses,
    styled,
    textFieldClasses,
} from '@mui/material';

import { AutocompleteRenderInputParams } from '@mui/material/Autocomplete';
import { Report } from 'api/Reports';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { TextFormField } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo, useState } from 'react';

import { ExpandMore } from '@mui/icons-material';
import Box from '@mui/material/Box';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { SaveIcon } from 'common/components/Icons/SaveIcon';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useSelector } from 'react-redux';
import { selectSelectedReport } from 'store/slices/customReport/selectors';
import { scrollbarStyle } from '../../../common/styles/ScrollbarStyles';
import theme from '../../../theme';

type CustomReportAutocompleteProps = {
    savedReports: Report[];
    onSelect: (customReport?: Report) => void;
    onDelete: (customReport: Report) => void;
    onSave: (item: Report) => void;
    onClear: () => void;
    disabled?: boolean;
};

type ReportOptionActionsProps = {
    onEditClick: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
    onDeleteClick: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
};

type EditActionButtonsProps = {
    onSaveClick: (event: React.MouseEvent<HTMLButtonElement>) => Promise<void>;
    onCancelClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
    isSaveDisabled: boolean;
};

const ReportEditActions: React.FC<ReportOptionActionsProps> = ({ onEditClick, onDeleteClick }) => {
    return (
        <>
            <Button key={'editButton'} onClick={onEditClick}>
                <EditIcon fill={'#5C6477'} />
            </Button>
            <Button onClick={onDeleteClick}>
                <DeleteIcon fill={'#5C6477'} />
            </Button>
        </>
    );
};

const ReportSaveActions: React.FC<EditActionButtonsProps> = ({
    onSaveClick,
    onCancelClick,
    isSaveDisabled,
}) => {
    return (
        <Box display="flex" marginLeft="15px">
            <Button key={'saveButton'} onClick={onSaveClick} disabled={isSaveDisabled}>
                <SaveIcon fill={'#5C6477'} />
            </Button>
            <Button onMouseDown={onCancelClick}>
                <CloseIcon fill={'#5C6477'} />
            </Button>
        </Box>
    );
};

const ListItemDiv = styled('div')(() => ({
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    width: 260,
}));

const DivNoOptionText = styled('div')(() => ({
    textAlign: 'center',
    fontFamily: 'Inter',
    fontWeight: 700,
    fontSize: 12,
    color: '#5C6477',
}));

const Separator = styled('div')(() => ({
    borderBottom: `solid 1px ${theme.palette.neutral[3]}`,
    width: '100%',
    marginBottom: 24,
}));

const DivDeleteModalReportName = styled('div')(() => ({
    color: '#5C6477',
    textAlign: 'center',
    fontFamily: 'Inter',
    fontSize: '14px',
    fontStyle: 'normal',
    fontWeight: '700',
    lineHeight: 'normal',
    marginBottom: 9,
}));

const AutocompleteStyledWrapper = styled('div')(({ theme }) => ({
    [`& .${autocompleteClasses.inputRoot}`]: {
        borderRadius: 16,
        height: 32,
        paddingLeft: 9,
        color: `${theme.palette.primary.main}`,
        fontWeight: 700,
        borderColor: `${theme.palette.primary.main}!important`,
        '&:hover': {
            backgroundColor: '#cce1ff',
            '& fieldset': {
                borderColor: `${theme.palette.primary.main}!important`,
            },
        },
        '& fieldset': {
            borderColor: `var(--cm1) !important`,
        },
    },
    [`& .${autocompleteClasses.option}`]: {
        justifyContent: 'space-between!important',
    },
    [`& .${autocompleteClasses.paper}`]: {
        backgroundColor: `${theme.palette.neutral[2]}!Important`,
        marginTop: 7,
        border: `solid 1px ${theme.palette.primary.main}`,
        '&:hover': {
            '& fieldset': {
                borderColor: `${theme.palette.primary.main}!important`,
            },
        },
    },
    [`& .${listItemButtonClasses.root}`]: {
        height: 33,
    },
    [`& .${buttonClasses.root}`]: {
        minWidth: 0,
        padding: 0,
    },
    [`& .${autocompleteClasses.listbox}`]: {
        ...scrollbarStyle(),
    },
    [`& .${textFieldClasses.root}`]: {
        '& input::placeholder': {
            color: `${theme.palette.primary.main}`,
            fontStyle: 'Inter',
            fontSize: 12,
            fontWeight: '700!important',
        },
    },
}));

export default function CustomReportAutocomplete({
    onSave,
    savedReports,
    onDelete,
    onSelect,
    onClear,
    disabled,
}: CustomReportAutocompleteProps) {
    const { t } = useAppTranslation();
    const value = useSelector(selectSelectedReport);

    const [open, setOpen] = useState(false);
    const [[searchQuery, doSearch], setSearchQuery] = useState(['', true]);
    const normalizedQuery = doSearch ? searchQuery.trim() : '';
    const [hoveredItem, setHoveredItem] = useState<Report | null>(null);
    const [isEditMode, setIsEditMode] = useState<boolean>(false);
    const [editableItem, setEditableItem] = useState<Report | null>(null);
    const [deletableItem, setDeletableItem] = useState<Report | null>(null);
    const [isDeletePopupOpen, setIsDeletePopupOpen] = useState<boolean>(false);

    function handleItemMouseEnter(item: Report) {
        setHoveredItem(item);
    }

    function handleItemMouseLeave() {
        setHoveredItem(null);
    }

    function handleItemMouseClick(item: Report) {
        if (isEditMode && item.id !== editableItem?.id) {
            closeDropDown();
            onSelect({ id: item.id, name: item.name, columns: item.columns });
        }
    }

    function handleExitEdit(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
        e.stopPropagation();
        setIsEditMode(false);
        setEditableItem(null);
    }

    function handleEditButtonClick(
        e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        item: Report
    ) {
        e.stopPropagation();
        setIsEditMode(true);
        setEditableItem(item);
    }

    function handleOnFocusInput(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
        if (isEditMode) {
            event.preventDefault();
            event.stopPropagation();
        }
    }

    function handleTrashButtonOnClick(
        e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        item: Report
    ) {
        e.stopPropagation();
        setDeletableItem(item);
        setIsDeletePopupOpen(true);
    }

    async function handleOnSaveClick(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
        e.stopPropagation();
        setIsEditMode(false);
        setEditableItem(null);
        onSave(editableItem!);
    }

    function closeDropDown() {
        setIsEditMode(false);
        setEditableItem(null);
        setDeletableItem(null);
        setOpen(false);
    }

    function onDeleteConfirm() {
        onDelete(deletableItem!);
        setIsDeletePopupOpen(false);
    }

    function handleOnChangeInput(event: React.ChangeEvent<{ value: string }>) {
        event.preventDefault();
        const newItem: Report = {
            id: editableItem?.id!,
            name: event.target.value,
            columns: editableItem?.columns ?? [],
        };

        setEditableItem(newItem);
    }

    const filteredReports = useMemo(() => {
        if (normalizedQuery) {
            return savedReports.filter((report) =>
                report.name.toLowerCase().includes(normalizedQuery.toLowerCase())
            );
        } else {
            return savedReports;
        }
    }, [normalizedQuery, savedReports]);

    return (
        <ClickAwayListener
            onClickAway={() => {
                if (!isEditMode) closeDropDown();
            }}
        >
            <AutocompleteStyledWrapper>
                <Autocomplete
                    disabled={disabled}
                    popupIcon={<ExpandMore />}
                    noOptionsText={<DivNoOptionText>{t('reports.nosavedreports')}</DivNoOptionText>}
                    id="custom-reports-dropdown"
                    autoComplete
                    autoHighlight
                    disablePortal={true}
                    open={open}
                    options={filteredReports}
                    inputValue={searchQuery}
                    value={value ?? null}
                    renderInput={useCallback(
                        (params: AutocompleteRenderInputParams) => (
                            <MuiTextField
                                {...params}
                                sx={{
                                    input: {
                                        fontWeight: '700!important',
                                        fontFamily: 'Inter!important',
                                        '&::placeholder': {
                                            opacity: 1,
                                        },
                                    },
                                }}
                                placeholder={t('reports.selectcustomizablereport')}
                            />
                        ),
                        [t]
                    )}
                    filterOptions={(options) => options}
                    renderOption={(props, option) => {
                        return (
                            <ListItemButton
                                onMouseEnter={() => handleItemMouseEnter(option)}
                                onMouseLeave={handleItemMouseLeave}
                                onMouseDown={() => handleItemMouseClick(option)}
                                onBlur={closeDropDown}
                                component="li"
                                {...props}
                            >
                                {isEditMode && option.id === editableItem?.id ? (
                                    <TextFormField
                                        value={editableItem.name}
                                        onBlur={closeDropDown}
                                        isInvalid={!(editableItem?.name.trim().length > 0)}
                                        onKeyDown={(event) => {
                                            event.stopPropagation();
                                        }}
                                        onMouseDown={(event) => {
                                            event.stopPropagation();
                                        }}
                                        sx={{
                                            '& .MuiOutlinedInput-root': {
                                                '&.Mui-focused input': {
                                                    backgroundColor: 'white',
                                                },
                                            },
                                        }}
                                        onChange={(event) => handleOnChangeInput(event)}
                                        onClick={(event) => {
                                            handleOnFocusInput(event);
                                        }}
                                        autoFocus
                                    />
                                ) : (
                                    <ListItemDiv>{option.name}</ListItemDiv>
                                )}
                                {!isEditMode && hoveredItem && hoveredItem.id === option.id && (
                                    <ReportEditActions
                                        onEditClick={(e) => handleEditButtonClick(e, option)}
                                        onDeleteClick={(e) => handleTrashButtonOnClick(e, option)}
                                    />
                                )}
                                {isEditMode && editableItem?.id === option.id && (
                                    <ReportSaveActions
                                        onSaveClick={async (e) => await handleOnSaveClick(e)}
                                        onCancelClick={(e) => handleExitEdit(e)}
                                        isSaveDisabled={editableItem?.name.length < 1}
                                    />
                                )}
                            </ListItemButton>
                        );
                    }}
                    isOptionEqualToValue={(option, value) => option?.id === value?.id}
                    getOptionLabel={(v) => {
                        return v === null ? '' : v.name;
                    }}
                    onChange={(_, value) => {
                        if (value) {
                            onSelect(value);
                            closeDropDown();
                        } else {
                            onClear();
                        }
                    }}
                    onInputChange={(_e, value, reason) => {
                        setSearchQuery([value, reason === 'input']);
                    }}
                    onOpen={() => setOpen(true)}
                />
                <DeleteConfirmationPopup
                    open={isDeletePopupOpen}
                    title={t('reports.deletecustomreporttitle')}
                    body={
                        <>
                            <Separator />
                            <DivDeleteModalReportName>
                                {deletableItem?.name}
                            </DivDeleteModalReportName>
                            <>{t('reports.deletecustomreport')}</>
                        </>
                    }
                    cancel={t('reports.canceldelete')}
                    confirm={t('reports.confirmreportdelete')}
                    onConfirm={onDeleteConfirm}
                    onClose={() => {
                        setEditableItem(null);
                        setIsDeletePopupOpen(false);
                    }}
                />
            </AutocompleteStyledWrapper>
        </ClickAwayListener>
    );
}
