export function cleanYear(y: string) {
    y = y.replace(/[^0-9]/g, '');
    if (y === '') return '';
    const yearNumber = +y;
    const thisYear = new Date().getFullYear();

    if (yearNumber < (thisYear % 100) + 3) {
        // small numbers like 5, 6, 12 transform into 20XX
        // i.e. 5 -> 2005, 10 -> 2010 etc.
        return yearNumber + 2000 + '';
    } else if (yearNumber < 100) {
        // convert bigger number into 20th century
        // 56 -> 1956, 95 -> 1995
        return yearNumber + 1900 + '';
    } else {
        // if you input weird number like 15000 it's you fault, sorry not sorry
        return yearNumber + '';
    }
}
