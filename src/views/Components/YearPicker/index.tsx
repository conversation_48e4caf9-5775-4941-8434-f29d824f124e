import BrandsAPI from 'api/Clients/Brands';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import { KeyboardEventHandler, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands, selectModels } from 'store/slices/clientsSlice/selectors';
import { orderActions } from 'store/slices/order/orderDetails';
import { cleanYear } from './helpers';

export type YearPickerProps = Omit<
    DropdownProps<string | null, false>,
    'onChange' | 'value' | 'options'
> & {
    fieldId?: string;
    value: string | null | undefined;
    brandName: string | undefined;
    modelName: string | undefined;
    disableApi?: boolean;
    onChange?: (year: string | undefined) => void;
    isSelected?: boolean;
    onKeyDown?: KeyboardEventHandler<Element>;
};

type YearOption = OptionData<string | null>;

const STATIC_YEARS = (() => {
    const years: string[] = [];

    for (let i = 1950; i <= new Date().getFullYear() + 1; i++) {
        years.push(`${i}`);
    }

    return years;
})();

export default function YearPicker({
    value,
    modelName,
    brandName,
    onChange,
    placeholder,
    fieldId,
    isSelected,
    onKeyDown,
    disableApi = false,
    ...props
}: YearPickerProps) {
    const ref = useRef<HTMLSelectElement>(null);
    const { callApi } = useApiCall();
    const dispatch = useAppDispatch();
    const brands = useAppSelector(selectBrands);
    const brandId = useMemo(
        () => brands.find((b) => b.name === brandName)?.id,
        [brands, brandName]
    );
    const models = useAppSelector((s) => {
        return selectModels(s, brandId ?? -1);
    });
    const modelId = useMemo(
        () => models.find((m) => m.name === modelName)?.id,
        [models, modelName]
    );

    const [years, setYears] = useState<string[]>([]);
    const { t } = useAppTranslation();

    useEffect(() => {
        if (disableApi) {
            setYears(STATIC_YEARS);
            return;
        }

        if (modelId) {
            callApi(() => BrandsAPI.getYears(modelId)).then(setYears);
        } else if (years.length > 0) {
            setYears([]);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [modelId, disableApi]);

    const onChangeCallback = useCallback(
        (option: YearOption | null) => {
            if (onChange && option) {
                onChange(cleanYear(option.label));
            }
        },
        [onChange]
    );

    const options = useMemo(
        () => years.map((y) => ({ value: y, label: y } as YearOption)),
        [years]
    );

    const selected = useMemo(
        () =>
            options.find((o) => o.value === value) ??
            (value ? { label: value, value: null } : null),
        [value, options]
    );

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <Dropdown
            ref={ref}
            creatable
            optionStyle={OptionStyle.icons}
            onChange={onChangeCallback}
            options={options}
            value={selected}
            placeholder={placeholder ?? t('newVehicle.yearPlaceholder')}
            openMenuOnFocus
            onEnterPress={onKeyDown}
            onBlur={() => fieldId && dispatch(orderActions.clearSelectedField({ id: fieldId }))}
            {...props}
        />
    );
}
