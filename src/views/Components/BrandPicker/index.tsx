import { styled } from '@mui/material';
import { BrandDto } from 'api/Clients/Brands';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { GroupData, OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { OptionStyle } from 'common/styles/OptionStyle';
import { KeyboardEventHandler, useCallback, useEffect, useMemo, useRef } from 'react';
import { ActionMeta, components } from 'react-select';
import { useAppDispatch, useAppSelector } from 'store';
import { selectBrands, selectFrequentBrands } from 'store/slices/clientsSlice/selectors';
import ensureBrandsFetched from 'store/slices/clientsSlice/thunks/ensureBrandsFetched';
import { orderActions } from 'store/slices/order/orderDetails';

type BrandPickerProps = Omit<
    DropdownProps<BrandDto | null, false>,
    'value' | 'options' | 'optionStyle' | 'onChange' | 'multiple'
> & {
    value?: string;
    hideIcons?: boolean;
    onChange?: (brandName: string | undefined, brand?: BrandDto) => void;

    /**
     * if set to a value will use enterprise API endpoint to fetch list of
     * brands.
     */
    enterpriseRepairShopKey?: string;

    fieldId?: string;
    isSelected?: boolean;
    onKeyDown?: KeyboardEventHandler<Element>;
};

export default function BrandPicker({
    value,
    hideIcons,
    onChange,
    placeholder,
    enterpriseRepairShopKey,
    fieldId,
    isSelected,
    onKeyDown,
    ...props
}: BrandPickerProps) {
    const ref = useRef<HTMLSelectElement>(null);
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const brands = useAppSelector(selectBrands);
    const frequentBrands = useAppSelector(selectFrequentBrands);
    const options: OptionData<BrandDto | null>[] = useMemo(() => brands.map(makeOption), [brands]);
    const frequentOptions: OptionData<BrandDto | null>[] = useMemo(
        () => frequentBrands.map(makeOption),
        [frequentBrands]
    );
    const groupedOptions: GroupData[] = useMemo(
        () => [
            {
                label: t('commonLabels.frequentBrands'),
                options: frequentOptions,
            },
            {
                label: t('commonLabels.allBrands'),
                options: options,
            },
        ],
        [frequentOptions, options, t]
    );

    const selectedOption = useMemo(() => {
        const predefined = options.find((o) => o.value?.name === value);

        if (!predefined) {
            if (value) {
                return { label: value, value: null };
            } else {
                return null;
            }
        }
        return predefined;
    }, [options, value]);

    const onChangeCallback = useCallback(
        (
            option: OptionData<BrandDto | null> | null,
            { action }: ActionMeta<OptionData<BrandDto | null>>
        ) => {
            if (option === null || option.label === value) return;
            if (onChange) {
                if (option.value && action !== 'create-option') {
                    onChange(option.value.name, option.value);
                } else {
                    // new model (custom)
                    onChange(option.label);
                }
            }
        },
        [onChange, value]
    );

    const isEnterprise = useIsEnterpriseRoute();

    useEffect(() => {
        if (isEnterprise) {
            if (enterpriseRepairShopKey) {
                dispatch(ensureBrandsFetched(enterpriseRepairShopKey));
            }
        } else {
            dispatch(ensureBrandsFetched());
        }
    }, [dispatch, enterpriseRepairShopKey, isEnterprise]);

    useEffect(() => {
        if (isSelected) {
            ref.current?.focus();
        }
    }, [isSelected]);

    return (
        <Dropdown
            ref={ref}
            creatable
            options={groupedOptions}
            value={selectedOption}
            onChange={onChangeCallback}
            iconSize={24}
            optionStyle={hideIcons ? OptionStyle.text : OptionStyle.icons}
            placeholder={placeholder ?? t('newVehicle.brandPlaceholder')}
            CustomGroup={Group}
            onEnterPress={onKeyDown}
            openMenuOnFocus
            {...props}
            onBlur={() => fieldId && dispatch(orderActions.clearSelectedField({ id: fieldId }))}
        />
    );
}

const makeOption = (brand: BrandDto) => {
    return {
        label: brand.name,
        value: brand,
        icon: brand.hasLogo ? () => <img alt="" src={brand.brandUrl} /> : undefined,
    };
};

export const LabelWrapper = styled('div')(({ theme }) => ({
    fontStyle: 'normal',
    fontSize: 12,
    lineHeight: '13.31px',
    color: theme.palette.info.light,
    fontFamily: 'Roboto',
    fontWeight: 700,
    cursor: 'default',
    display: 'flex',
    height: 24,
    paddingBottom: 2,
    paddingTop: 9,
    paddingLeft: 12,
    paddingRight: 12,
    alignItems: 'center',
    boxSizing: 'border-box',
}));

export const Group: typeof components.Group = (props) => {
    return (
        <div {...props} style={{ marginBottom: 13 }}>
            <LabelWrapper>
                <span>{props.label}</span>
            </LabelWrapper>
            <div>{props.children}</div>
        </div>
    );
};
