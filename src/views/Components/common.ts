import { autocompleteClasses, outlinedInputClasses } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';

export const useAutoCompleteCommonStyles = makeStyles((theme) => ({
    paperAutoComplete: {
        marginTop: 5,
        background: theme.palette.neutral[2],
        border: `1px solid ${theme.palette.neutral[4]}`,
        [`& .${autocompleteClasses.listbox}`]: {
            ...scrollbarStyle(),
        },
    },
    root: {
        ...theme.typography.body1,
        background: '#FAFAFA',
        height: 32,
        paddingLeft: 16,

        '& input::placeholder': {
            opacity: 0.75,
        },

        [`& .${outlinedInputClasses.notchedOutline}`]: {
            borderColor: theme.palette.neutral[5],
        },

        ['&:hover:not(:has(input[disabled]))']: {
            [`& .${outlinedInputClasses.notchedOutline}`]: {
                borderColor: `${theme.palette.primary.main}`,
            },
        },
    },
    inputFocused: {
        borderColor: theme.palette.primary.main,
    },
    addButtonAutoComplete: {
        borderRadius: 30,
        background: theme.palette.neutral[2],
        border: `1px solid ${theme.palette.neutral[4]}`,
        [theme.breakpoints.down('md')]: {
            width: '100%',
        },
    },
    addButtonText: {
        ...theme.typography.h5Roboto,
        color: theme.palette.neutral[6],
        textTransform: 'none',
    },
}));
