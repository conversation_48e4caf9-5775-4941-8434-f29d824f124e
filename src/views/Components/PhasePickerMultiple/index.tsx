import { SelectChangeEvent } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { PhaseDto } from 'api/orders';
import { WpPhasesApi } from 'api/workshopPlanner';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo } from 'react';

type PhasePickerMultipleProps = {
    phaseIds: number[];
    onChange: (phaseIds: number[]) => void;
    placeholder?: string;
    disabled?: boolean;

    /**
     * If set to true and list of phases is empty, "All phases" option
     * is selected, if set to false (default), "All phases" options is missing.
     */
    treatNoPhasesAsAllPhases?: boolean;
};

const ALL_PHASES_MAGIC_VALUE = -1000;

export default function PhasePickerMultiple({
    phaseIds,
    onChange,
    placeholder,
    disabled,
    treatNoPhasesAsAllPhases = false,
}: PhasePickerMultipleProps) {
    const { t } = useAppTranslation();
    const { data, isFetching } = useQuery(['wp', 'phases'], WpPhasesApi.getPhases, {
        staleTime: 1000,
        cacheTime: Infinity,
    });
    const phases = data ?? [];

    const handleChange = useCallback(
        (e: SelectChangeEvent<number[]>) => {
            if (typeof e.target.value === 'string') {
                // basically never going to happen (sanity check)
                return;
            }

            if (e.target.value.includes(ALL_PHASES_MAGIC_VALUE)) {
                if (e.target.value[e.target.value.length - 1] === ALL_PHASES_MAGIC_VALUE) {
                    // user clicked "All phases"
                    onChange([]);
                } else {
                    // use clicked something else
                    const value = e.target.value
                        .filter((x) => x !== ALL_PHASES_MAGIC_VALUE)
                        .toSorted();
                    onChange(value);
                }
            } else {
                const value = e.target.value.toSorted();
                onChange(value);
            }
        },
        [onChange]
    );

    const getPhaseName = (phase: PhaseDto) =>
        ['noPhase', 'closedOrder'].includes(phase.name) ? t(`phases.${phase.name}`) : phase.name;

    const isAllPhasesSelected = phaseIds.length === 0;

    const adjustedPhaseIds = useMemo(() => {
        if (treatNoPhasesAsAllPhases) {
            if (phaseIds.length === 0) {
                return [ALL_PHASES_MAGIC_VALUE];
            } else {
                return phaseIds;
            }
        } else {
            return phaseIds;
        }
    }, [phaseIds, treatNoPhasesAsAllPhases]);

    return (
        <SSelectInput<number[]>
            value={adjustedPhaseIds}
            multiple
            disabled={disabled || isFetching}
            onChange={handleChange}
            renderValue={(value) => {
                const ids = value as number[];
                if (ids.length === 0) return placeholder;
                return ids
                    .map((x) => {
                        if (x === ALL_PHASES_MAGIC_VALUE) return t('phases.allPhases');
                        const phase = phases.find((p) => p.id === x);
                        return phase ? getPhaseName(phase) : '';
                    })
                    .join(', ');
            }}
        >
            {treatNoPhasesAsAllPhases && (
                <SMenuItem value={ALL_PHASES_MAGIC_VALUE}>
                    {isAllPhasesSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    {t('phases.allPhases')}
                </SMenuItem>
            )}
            {phases.map((phase) => (
                <SMenuItem key={phase.id} value={phase.id}>
                    {phaseIds.includes(phase.id) ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    {getPhaseName(phase)}
                </SMenuItem>
            ))}
        </SSelectInput>
    );
}
