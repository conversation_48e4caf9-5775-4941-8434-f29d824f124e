import { Box, ListProps, styled } from '@mui/material';
import { useBrandsWithModels } from 'api/customers';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SMenuItem, SSelect } from 'common/components/mui';
import { SSelectGrey, SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { FixedSizeList, ListChildComponentProps } from 'react-window';

export type BrandModelsData = {
    name: string;
    models: string[];
};

export type MultipleModelPickerProps = {
    value?: BrandModelsData[];
    filterBrands: string[];
    onChange: (values: BrandModelsData[]) => void;
    /**
     * If set to true disables a hint displayed at the top of the list.
     */
    disablePlaceholderHint?: boolean;
    cmosVariant?: 'default' | 'grey' | 'rounded';
    disabled?: boolean;
    disableSelecting?: boolean;
    dateTestId?: string;
    showAllModelsOption?: boolean;
    onChangeBehavior?: 'onBlur' | 'immediate';
};

type MultipleModelPickerContextValue = {
    value: BrandModelsData[];
    brands: string[];
    toggleBrandModel: (brand: string, model: string, toggle: boolean) => void;
    setAllModels: () => void;
    disablePlaceholderHint: boolean;
    showAllModelsOption: boolean;
};

const MultipleModelPickerContext = createContext<MultipleModelPickerContextValue>({
    value: [],
    brands: [],
    toggleBrandModel() {},
    setAllModels() {},
    disablePlaceholderHint: false,
    showAllModelsOption: false,
});

const MultipleModelPicker = ({
    value: valueParam,
    filterBrands,
    onChange,
    disablePlaceholderHint = false,
    showAllModelsOption = false,
    disabled,
    disableSelecting = false,
    cmosVariant,
    onChangeBehavior = 'immediate',
}: MultipleModelPickerProps) => {
    const [value, setValue] = useState(() =>
        valueParam ? valueParam.filter((m) => m.models.length > 0) : []
    );
    const refs = useRef({ value, onChange });
    refs.current.value = value;
    refs.current.onChange = onChange;
    const { t } = useAppTranslation();

    useEffect(() => {
        setValue(valueParam ? valueParam.filter((m) => m.models.length > 0) : []);
    }, [valueParam]);

    let SelectComponent: typeof SSelect;

    switch (cmosVariant) {
        case 'grey':
            SelectComponent = SSelectGrey;
            break;
        case 'rounded':
            SelectComponent = SSelect;
            break;
        case 'default':
        default:
            SelectComponent = SSelectInput;
            break;
    }

    const contextValue: MultipleModelPickerContextValue = useMemo(
        () => ({
            value: value ?? [],
            brands: filterBrands,
            setAllModels() {},
            toggleBrandModel(brand, model, toggle) {
                const newValue = [...refs.current.value];
                const brandEntryIndex = newValue.findIndex((x) => x.name === brand);
                if (brandEntryIndex === -1) {
                    if (toggle) {
                        newValue.push({
                            name: brand,
                            models: [model],
                        });
                    } else {
                        return;
                    }
                } else {
                    if (toggle) {
                        if (!newValue[brandEntryIndex].models.includes(model)) {
                            newValue[brandEntryIndex] = {
                                name: newValue[brandEntryIndex].name,
                                models: [...newValue[brandEntryIndex].models, model],
                            };
                        } else {
                            return;
                        }
                    } else {
                        if (newValue[brandEntryIndex].models.includes(model)) {
                            newValue[brandEntryIndex] = {
                                name: newValue[brandEntryIndex].name,
                                models: newValue[brandEntryIndex].models.filter((m) => m !== model),
                            };
                        } else {
                            return;
                        }
                    }
                }

                setValue(newValue);
                if (onChangeBehavior === 'immediate') {
                    refs.current.onChange(newValue);
                }
            },
            disablePlaceholderHint,
            showAllModelsOption,
        }),
        [value, filterBrands, disablePlaceholderHint, showAllModelsOption, onChangeBehavior]
    );

    return (
        <>
            <MultipleModelPickerContext.Provider value={contextValue}>
                <InputWrapper label={t('settings.prospections.maintenance.form.model')}>
                    <SelectComponent
                        fullWidth
                        disabled={disabled}
                        multiple
                        onClose={handleClose}
                        value={value}
                        renderValue={(value) => {
                            const models = value.flatMap((x) => x.models);
                            if (models.length === 0)
                                return t('settings.prospections.maintenance.form.allModels');
                            if (models.length === 1) return models[0];
                            return (
                                <>
                                    {models[0]}
                                    &nbsp;+{models.length - 1}
                                </>
                            );
                        }}
                        placeholder={t('settings.prospections.maintenance.form.allBrands')}
                        MenuProps={{
                            MenuListProps: {
                                component: ListboxComponent,
                            },
                            TransitionProps: {
                                timeout: 0,
                            },
                            transformOrigin: { vertical: 'top', horizontal: 'left' },
                            anchorOrigin: { vertical: 38, horizontal: 'left' },
                            slotProps: {
                                paper: {
                                    sx: {
                                        minWidth: 250,
                                    },
                                },
                            },
                        }}
                    />
                </InputWrapper>
            </MultipleModelPickerContext.Provider>
        </>
    );

    function handleClose() {
        if (onChangeBehavior === 'onBlur' && !disableSelecting) {
            onChange(value);
        }
    }
};

export default MultipleModelPicker;

type VListData = {
    items: VItemData[];
    selected: Record<string, string[]>;
};

type VItemData =
    | {
          type: 'group';
          name: string;
      }
    | {
          type: 'model';
          name: string;
          brand: string;
      };

const ListboxComponent = React.forwardRef(
    (_props: ListProps, ref: React.ForwardedRef<HTMLUListElement>) => {
        const ctx = useContext(MultipleModelPickerContext);

        const allBrands = useBrandsWithModels();

        const data = useMemo(() => {
            const data: VListData = {
                items: [],
                selected: {},
            };

            const brands = [...ctx.brands];

            for (const brand of ctx.value) {
                if (!brands.includes(brand.name)) {
                    brands.push(brand.name);
                }
            }
            brands.sort();

            for (const brand of brands) {
                data.items.push({
                    type: 'group',
                    name: brand,
                });

                const allModels = allBrands.find((x) => x.name === brand)?.models;
                if (!allModels) continue;

                const selectedModels = ctx.value.find((x) => x.name === brand)?.models ?? [];

                data.selected[brand] = selectedModels;
                allModels.forEach((model) => {
                    data.items.push({
                        type: 'model',
                        name: model,
                        brand: brand,
                    });
                });
            }

            return data;
        }, [allBrands, ctx.brands, ctx.value]);

        return (
            <FixedSizeList<VListData>
                outerRef={ref}
                outerElementType={UL}
                width="100%"
                height={400}
                overscanCount={5}
                itemSize={40}
                itemCount={data.items.length}
                itemData={data}
            >
                {ListboxVItem}
            </FixedSizeList>
        );
    }
);

function ListboxVItem({ style, index, data }: ListChildComponentProps<VListData>) {
    const ctx = useContext(MultipleModelPickerContext);

    if (!ctx.disablePlaceholderHint) {
        if (index === 0) {
            return (
                <Box style={style} sx={{ display: 'flex', alignItems: 'center', pl: 2 }}>
                    <span>
                        <Trans i18nKey="settings.prospections.maintenance.form.selectSpecificModels" />
                    </span>
                </Box>
            );
        }

        index--;
    }

    if (ctx.showAllModelsOption) {
        if (index === 0) {
            const isSelected = Object.keys(data.selected).length === 0;

            return (
                <SMenuItem
                    sx={{ flexWrap: 'nowrap' }}
                    style={style}
                    onClick={() => {
                        ctx.setAllModels();
                    }}
                >
                    {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    <div aria-hidden style={{ width: 24, height: 24 }} />
                    &nbsp;
                    <Trans i18nKey="commonLabels.allBrands" />
                </SMenuItem>
            );
        }

        index--;
    }

    const item = data.items[index];

    if (item.type === 'group') {
        return <GroupLabel style={style}>{item.name}</GroupLabel>;
    } else if (item.type === 'model') {
        const isSelected = (data.selected[item.brand] ?? []).includes(item.name);
        return (
            <SMenuItem
                sx={{ flexWrap: 'nowrap', whiteSpace: 'normal' }}
                style={style}
                onClick={() => {
                    ctx.toggleBrandModel(item.brand, item.name, !isSelected);
                }}
            >
                {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                &nbsp;{item.name}
            </SMenuItem>
        );
    } else return <div style={style} />;
}

const GroupLabel = styled('span')({
    fontWeight: 'bold',
    color: 'var(--cm2)',
    display: 'flex',
    alignItems: 'center',
    paddingLeft: 10,
});

const UL = styled('ul')({
    margin: 0,
    padding: 0,
});
