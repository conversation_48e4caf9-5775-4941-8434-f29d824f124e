import { Box, ListProps, styled } from '@mui/material';
import { useBrandsWithModels } from 'api/customers';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SMenuItem } from 'common/components/mui';
import SSelect, { SSelectGrey, SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import { useAppDispatch, useAppSelector } from 'store';
import { selectFrequentBrands } from 'store/slices/clientsSlice/selectors';
import ensureBrandsFetched from 'store/slices/clientsSlice/thunks/ensureBrandsFetched';

export type MultipleBrandPickerProps = {
    value?: string[];
    onChange: (values: string[]) => void;
    /**
     * If set to true disables a hint displayed at the top of the list.
     */
    disablePlaceholderHint?: boolean;
    repairShopKey?: string;
    disabled?: boolean;
    disableSelecting?: boolean;
    dataTestId?: string;
    cmosVariant?: 'default' | 'grey' | 'rounded';
    onChangeBehavior?: 'immediately' | 'onBlur';
    showAllBrandsOption?: boolean;
};

type MultipleBrandPickerContextValue = {
    selectedBrands: string[];
    allBrands: { name: string; logo: string | null }[];
    frequentBrands: string[];
    toggleBrand: (brand: string, toggle: boolean) => void;
    setAllBrands: () => void;
    disablePlaceholderHint: boolean;
    showAllBrandsOption: boolean;
};

const MultipleBrandPickerContext = createContext<MultipleBrandPickerContextValue>({
    selectedBrands: [],
    allBrands: [],
    frequentBrands: [],
    toggleBrand: () => {},
    setAllBrands: () => {},
    disablePlaceholderHint: false,
    showAllBrandsOption: false,
});

const MultipleBrandPicker = ({
    value: valueParam,
    onChange,
    disabled,
    disablePlaceholderHint = false,
    cmosVariant = 'default',
    repairShopKey,
    disableSelecting,
    dataTestId,
    onChangeBehavior = 'immediately',
    showAllBrandsOption = false,
}: MultipleBrandPickerProps) => {
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    const [value, setValue] = useState<string[]>([]);
    const refs = useRef({ value, onChange });
    refs.current.value = value;
    refs.current.onChange = onChange;

    const brands = useBrandsWithModels();
    const frequentBrands = useAppSelector(selectFrequentBrands);

    const isEnterprise = useIsEnterpriseRoute();

    useEffect(() => {
        if (isEnterprise) {
            dispatch(ensureBrandsFetched(repairShopKey));
        } else {
            dispatch(ensureBrandsFetched());
        }
    }, [dispatch, isEnterprise, repairShopKey]);

    useEffect(() => {
        setValue(valueParam ?? []);
    }, [valueParam]);

    const contextValue: MultipleBrandPickerContextValue = useMemo(() => {
        return {
            frequentBrands: frequentBrands.map((x) => x.name),
            allBrands: brands.map((x) => ({ name: x.name, logo: x.logo })),
            selectedBrands: value ?? [],
            disablePlaceholderHint,
            showAllBrandsOption,
            setAllBrands: () => {
                setValue([]);

                if (onChangeBehavior === 'immediately' && !disableSelecting) {
                    refs.current.onChange([]);
                }
            },
            toggleBrand: (brand, toggle) => {
                const newValue = ((v: string[]) => {
                    const selected = v.includes(brand);
                    if (selected !== toggle) {
                        if (toggle) {
                            return [...v, brand];
                        } else {
                            return v.filter((x) => x !== brand);
                        }
                    } else {
                        return v;
                    }
                })(refs.current.value);

                newValue.sort();

                setValue(newValue);

                if (onChangeBehavior === 'immediately' && !disableSelecting) {
                    refs.current.onChange(newValue);
                }
            },
        };
    }, [
        brands,
        disablePlaceholderHint,
        disableSelecting,
        frequentBrands,
        onChangeBehavior,
        showAllBrandsOption,
        value,
    ]);

    let SelectComponent: typeof SSelect;

    switch (cmosVariant) {
        case 'grey':
            SelectComponent = SSelectGrey;
            break;
        case 'rounded':
            SelectComponent = SSelect;
            break;
        case 'default':
        default:
            SelectComponent = SSelectInput;
            break;
    }

    return (
        <MultipleBrandPickerContext.Provider value={contextValue}>
            <InputWrapper label={t('settings.prospections.maintenance.form.brand')}>
                <SelectComponent
                    fullWidth
                    disabled={disabled}
                    data-test-id={dataTestId}
                    multiple
                    onClose={handleClose}
                    value={value}
                    renderValue={(value) => {
                        if (value.length === 0)
                            return t('settings.prospections.maintenance.form.allBrands');
                        if (value.length === 1) return <BrandDisplay brand={value[0]} />;
                        return (
                            <>
                                <BrandDisplay brand={value[0]} />
                                &nbsp;+{value.length - 1}
                            </>
                        );
                    }}
                    placeholder={t('settings.prospections.maintenance.form.allBrands')}
                    MenuProps={{
                        MenuListProps: {
                            component: ListboxComponent,
                        },
                        TransitionProps: {
                            timeout: 0,
                        },
                        transformOrigin: { vertical: 'top', horizontal: 'left' },
                        anchorOrigin: { vertical: 38, horizontal: 'left' },
                        slotProps: {
                            paper: {
                                sx: {
                                    width: 220,
                                },
                            },
                        },
                    }}
                />
            </InputWrapper>
        </MultipleBrandPickerContext.Provider>
    );

    function handleClose() {
        if (onChangeBehavior === 'onBlur' && !disableSelecting) {
            onChange(value);
        }
    }
};

function BrandDisplay({ brand }: { brand: string }) {
    const brands = useBrandsWithModels();
    const brandData = useMemo(() => brands.find((x) => x.name === brand), [brands, brand]);
    return (
        <>
            {brandData?.logo ? (
                <img src={brandData.logo} alt={`${brand} logo`} style={{ width: 24, height: 24 }} />
            ) : (
                <div aria-hidden style={{ width: 24, height: 24 }} />
            )}
            &nbsp;
            {brand}
        </>
    );
}

const ListboxComponent = React.forwardRef(
    (_props: ListProps, ref: React.ForwardedRef<HTMLUListElement>) => {
        const ctx = useContext(MultipleBrandPickerContext);

        let itemCount = ctx.allBrands.length;

        if (ctx.frequentBrands.length > 0) {
            // add two items for headers
            itemCount += 2;
        } else {
            // only one
            itemCount += 1;
        }

        if (ctx.showAllBrandsOption) {
            itemCount += 1;
        }

        if (!ctx.disablePlaceholderHint) {
            itemCount += 1;
        }

        return (
            <FixedSizeList<MultipleBrandPickerContextValue>
                outerRef={ref}
                outerElementType={UL}
                width="max(200px, 100%)"
                height={400}
                overscanCount={5}
                itemSize={40}
                itemCount={itemCount}
                itemData={ctx}
            >
                {ListboxVItem}
            </FixedSizeList>
        );
    }
);

function ListboxVItem({
    style,
    index,
    data,
}: ListChildComponentProps<MultipleBrandPickerContextValue>) {
    if (!data.disablePlaceholderHint) {
        if (index === 0) {
            return (
                <Box style={style} sx={{ display: 'flex', alignItems: 'center', pl: 2 }}>
                    <span>
                        <Trans i18nKey="settings.prospections.maintenance.form.selectSpecificBrands" />
                    </span>
                </Box>
            );
        }

        index--;
    }

    if (data.showAllBrandsOption) {
        if (index === 0) {
            const isSelected = data.selectedBrands.length === 0;

            return (
                <SMenuItem
                    sx={{ flexWrap: 'nowrap' }}
                    style={style}
                    onClick={() => {
                        data.setAllBrands();
                    }}
                >
                    {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    <div aria-hidden style={{ width: 24, height: 24 }} />
                    &nbsp;
                    <Trans i18nKey="commonLabels.allBrands" />
                </SMenuItem>
            );
        }

        index--;
    }

    if (data.frequentBrands.length > 0) {
        if (index === 0) {
            return (
                <GroupLabel style={style}>
                    <Trans i18nKey="commonLabels.frequentBrands" />
                </GroupLabel>
            );
        } else if (index < data.frequentBrands.length + 1) {
            const frequentBrand = data.frequentBrands[index - 1];
            const brand = data.allBrands.find((x) => x.name === frequentBrand);
            return brand ? renderBrand(brand) : '-';
        } else if (index === data.frequentBrands.length + 1) {
            return (
                <GroupLabel style={style}>
                    <Trans i18nKey="commonLabels.allBrands" />
                </GroupLabel>
            );
        } else {
            return renderBrand(data.allBrands[index - data.frequentBrands.length - 2]);
        }
    } else {
        if (index === 0) {
            return (
                <GroupLabel style={style}>
                    <Trans i18nKey="commonLabels.allBrands" />
                </GroupLabel>
            );
        }

        return renderBrand(data.allBrands[index - 1]);
    }

    function renderBrand(brand: (typeof data.allBrands)[number]) {
        const isSelected = data.selectedBrands.includes(brand.name);

        return (
            <SMenuItem
                sx={{ flexWrap: 'nowrap' }}
                style={style}
                onClick={() => {
                    data.toggleBrand(brand.name, !isSelected);
                }}
            >
                {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                {brand.logo ? (
                    <img
                        style={{ height: 24, width: 24 }}
                        src={brand.logo}
                        alt={`${brand.name} logo`}
                    />
                ) : (
                    <div aria-hidden style={{ width: 24, height: 24 }} />
                )}
                &nbsp;{brand.name}
            </SMenuItem>
        );
    }
}

const GroupLabel = styled('span')({
    fontWeight: 'bold',
    color: 'var(--cm2)',
    display: 'flex',
    alignItems: 'center',
    paddingLeft: 10,
});

const UL = styled('ul')({
    margin: 0,
    padding: 0,
});

export default MultipleBrandPicker;
