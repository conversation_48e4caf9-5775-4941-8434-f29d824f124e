import { Box, ListItemIcon, Paper, PaperProps, SelectChangeEvent, styled } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { SMenuItem, SSelect } from 'common/components/mui';
import { SSelectGrey } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { SSelectInput } from 'views/Settings/General/AppointmentReasons/CustomReasonSelectMulti/SSelect';

export type MultipleYearPickerProps = {
    value?: string[];
    onChange: (values: string[]) => void;
    /**
     * If set to true disables a hint displayed at the top of the list.
     */
    disablePlaceholderHint?: boolean;
    cmosVariant?: 'default' | 'grey' | 'rounded';
    onChangeBehavior?: 'immediately' | 'onBlur';
    disabled?: boolean;
    disableSelecting?: boolean;
    dataTestId?: string;
    showEmptyList?: boolean;
};

const YEARS = (() => {
    const v: string[] = [];
    const year = moment().year() + 1;
    for (let y = 1950; y <= year; y++) v.push(y + '');
    return v.reverse();
})();

const MultipleYearPicker = ({
    value: valueParam,
    onChange,
    disablePlaceholderHint,
    disabled,
    disableSelecting = false,
    cmosVariant = 'default',
    dataTestId,
    onChangeBehavior = 'onBlur',
    showEmptyList = false,
}: MultipleYearPickerProps) => {
    const { t } = useAppTranslation();

    // we *should* ask backend for a list of years, but currently backend returns list of years per-model
    // but it's going to be the same every time because we don't actually store list of years anywhere,
    // we just return hard-coded list of numbers. Ultimately I think it's better to just display a static list of numbers for now
    // than ask backend to give the same list of numbers
    // it's not the "right" solution but it's a solution that makes more sense with current business requirements

    const [value, setValue] = useState(valueParam ?? []);

    useEffect(() => {
        setValue(valueParam ?? []);
    }, [valueParam]);

    let SelectComponent: typeof SSelect;

    switch (cmosVariant) {
        case 'grey':
            SelectComponent = SSelectGrey;
            break;
        case 'rounded':
            SelectComponent = SSelect;
            break;
        case 'default':
        default:
            SelectComponent = SSelectInput;
            break;
    }

    return (
        <InputWrapper label={t('settings.prospections.maintenance.form.year')}>
            <SelectComponent
                fullWidth
                disabled={disabled}
                data-test-id={dataTestId}
                multiple
                onClose={handleClose}
                onChange={handleChange}
                value={value}
                renderValue={(value) => {
                    if (value.length === 0)
                        return t('settings.prospections.maintenance.form.allYears');
                    if (value.length === 1) return value[0];
                    return `${value[0]} +${value.length - 1}`;
                }}
                MenuProps={{
                    TransitionProps: {
                        timeout: 0,
                    },
                    transformOrigin: { vertical: 'top', horizontal: 'left' },
                    anchorOrigin: { vertical: 38, horizontal: 'left' },
                    slots: {
                        paper: disablePlaceholderHint ? StyledPaper : StyledPaperWithHint,
                    },
                    slotProps: {
                        paper: {
                            sx: {
                                width: 220,
                                maxHeight: 400,
                            },
                        },
                    },
                }}
            >
                {/* IMPORTANT: this MUST be an array not a fragment! */}
                {!showEmptyList && [
                    <SMenuItem key="all" value="all">
                        <ListItemIcon>
                            {value.length === 0 ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                        </ListItemIcon>
                        {t('settings.prospections.maintenance.form.allYears')}
                    </SMenuItem>,
                    ...YEARS.map((year) => (
                        <SMenuItem key={year} value={year}>
                            <ListItemIcon>
                                {value.includes(year) ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                            </ListItemIcon>
                            {year}
                        </SMenuItem>
                    )),
                ]}
            </SelectComponent>
        </InputWrapper>
    );

    function handleClose() {
        if (onChangeBehavior === 'onBlur' && !disableSelecting) {
            onChange(value);
        }
    }

    function handleChange(event: SelectChangeEvent<string[]>) {
        let newValue = event.target.value;
        if (typeof newValue === 'string') return;

        if (newValue[newValue.length - 1] === 'all') {
            newValue = [];
        }

        setValue(newValue);

        if (onChangeBehavior === 'immediately' && !disableSelecting) {
            onChange(newValue);
        }
    }
};

const StyledPaperWithHint = React.forwardRef(
    ({ children, ...props }: PaperProps, ref: React.ForwardedRef<HTMLDivElement>) => {
        const { t } = useAppTranslation();
        return (
            <StyledPaper {...props} ref={ref}>
                <Box sx={{ display: 'flex', justifyContent: 'center', padding: '12px 0' }}>
                    <span>{t('settings.prospections.maintenance.form.selectSpecificYears')}</span>
                </Box>
                {children}
            </StyledPaper>
        );
    }
);

const StyledPaper = styled(Paper)({
    position: 'fixed',
    overflow: 'auto',
});

export default MultipleYearPicker;
