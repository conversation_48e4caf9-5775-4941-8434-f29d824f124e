import { Box } from '@mui/material';
import { useCallback, useRef } from 'react';
import LabeledSettingsCheckbox from 'views/Settings/common/LabeledSettingsCheckbox';

export type ValueDescriptor = {
    value: boolean;
    label: string;
    disabled?: boolean;
    onChange?: (value: boolean) => void;
};

export type CheckboxArrayProps = {
    /**
     * Array of value descriptors for each checkbox.
     */
    values: ValueDescriptor[];
    /**
     * Optional width of each checkbox element.
     */
    width?: number;

    /**
     * Optional min width of each checkbox element.
     */
    minWidth?: number;

    /**
     * Optional margin to the right of each checkbox element.
     */
    marginRight?: number;
};

/**
 * Renders a group of checkboxes based on the provided values, with optional width and margin settings.
 */
export default function CheckboxArray({
    values,
    width,
    minWidth = 100,
    marginRight,
}: CheckboxArrayProps) {
    // Using `useRef` to maintain consistent reference across re-renders
    const nameRef = useRef(`checkboxArray${Math.random().toString(16).substring(2)}`);

    // Optimized change handler to avoid creating new function on each render
    const handleChange = useCallback((valueDescriptor: ValueDescriptor, checked: boolean) => {
        valueDescriptor.onChange?.(checked);
    }, []);

    return (
        <Box display="flex" flexWrap="wrap" justifyContent="flex-start" gap="12px">
            {values.map((desc) => (
                <LabeledSettingsCheckbox
                    key={desc.label} // Added key for list rendering performance
                    name={nameRef.current}
                    checked={desc.value}
                    onChange={(event) => handleChange(desc, event.target.checked)}
                    disabled={desc.disabled}
                    label={desc.label}
                    marginRight={marginRight}
                    width={width}
                    minWidth={width && minWidth ? Math.min(width, minWidth) : minWidth}
                />
            ))}
        </Box>
    );
}
