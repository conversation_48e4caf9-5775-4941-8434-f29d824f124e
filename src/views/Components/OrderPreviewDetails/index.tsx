import { Collapse, Divider, Grid, styled } from '@mui/material';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { PaymentMethodTypeLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import {
    IPreviewItem,
    IRepairOrderPreviewResponse,
} from 'datacontracts/Order/IOrderPreviewResponse';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';

type OrderPreviewDetailsProps = {
    order: IRepairOrderPreviewResponse | undefined;
    twoColumnCustomerInformation?: boolean;
};

export const OrderPreviewDetails = ({
    order,
    twoColumnCustomerInformation,
}: OrderPreviewDetailsProps) => {
    const { t } = useAppTranslation();
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;

    const getCustomerName = () => {
        const firstName = order?.firstName ?? '';
        const lastName = order?.lastName ?? '';
        let name = firstName;
        if (firstName.length || lastName.length) name += ' ';
        name += lastName;
        return name;
    };

    const customerName = getCustomerName();

    const getTotalEstimateWithSubitems = (items: IPreviewItem[] | undefined) => {
        return (
            items?.reduce((acc, current) => {
                const subitemsTotal =
                    current.subitems.reduce((acc, current) => acc + (current.totalCost ?? 0), 0) ??
                    0;
                return acc + (current.totalCost ?? 0) + subitemsTotal;
            }, 0) ?? 0
        );
    };

    const urgentEstimatesTotal = getTotalEstimateWithSubitems(order?.redItems);
    const medEstimatesTotal = getTotalEstimateWithSubitems(order?.yellowItems);

    return (
        <>
            <CollapsibleSection title={t('orders.preview.customerInformation')} isCollapse={false}>
                {twoColumnCustomerInformation && (
                    <Grid container spacing={1} direction="row" justifyContent="center">
                        <Grid item xs={6}>
                            <Grid container direction="column" spacing={1}>
                                <Grid container item>
                                    <Grid item xs={4}>
                                        <span>{t('commonLabels.name')}</span>
                                    </Grid>
                                    <GridCustomerInformationValue item>
                                        <span>{customerName.length ? customerName : '-'}</span>
                                    </GridCustomerInformationValue>
                                </Grid>
                                <Grid container item>
                                    <Grid item xs={4}>
                                        <span>{t('commonLabels.email')}</span>
                                    </Grid>
                                    <GridCustomerInformationValue item>
                                        <span>{order?.email || '-'}</span>
                                    </GridCustomerInformationValue>
                                </Grid>
                            </Grid>
                        </Grid>
                        <Grid item xs={6}>
                            <Grid container direction="column" spacing={1}>
                                <Grid container item>
                                    <Grid item xs={4}>
                                        <span>{t('commonLabels.phone')}</span>
                                    </Grid>
                                    <GridCustomerInformationValue item>
                                        <span>
                                            {InternationalizationLogic.numberToPhone(
                                                internationalization,
                                                order?.landlinePhone || null
                                            )}
                                        </span>
                                    </GridCustomerInformationValue>
                                </Grid>
                                <Grid container item>
                                    <Grid item xs={4}>
                                        <span>{t('commonLabels.mobile')}</span>
                                    </Grid>
                                    <GridCustomerInformationValue item>
                                        <span>
                                            {InternationalizationLogic.numberToPhone(
                                                internationalization,
                                                order?.mobilePhone || null
                                            )}
                                        </span>
                                    </GridCustomerInformationValue>
                                </Grid>
                                <Grid container item>
                                    <Grid item xs={4}>
                                        <span>{t('commonLabels.paymentMethod')}</span>
                                    </Grid>
                                    <GridCustomerInformationValue item>
                                        <span>
                                            {order?.paymentMethod
                                                ? t(PaymentMethodTypeLabel(order.paymentMethod))
                                                : '-'}
                                        </span>
                                    </GridCustomerInformationValue>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                )}
                {!twoColumnCustomerInformation && (
                    <Grid
                        container
                        spacing={1}
                        direction="row"
                        justifyContent="center"
                        alignItems="center"
                        alignContent="center"
                    >
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={4}>
                                    <SpanCustomerInformationLabel>
                                        {t('commonLabels.name')}
                                    </SpanCustomerInformationLabel>
                                </Grid>
                                <GridCustomerInformationValue item>
                                    <span>{customerName.length ? customerName : '-'}</span>
                                </GridCustomerInformationValue>
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={4}>
                                    <SpanCustomerInformationLabel>
                                        {t('commonLabels.email')}
                                    </SpanCustomerInformationLabel>
                                </Grid>
                                <GridCustomerInformationValue item>
                                    <span>{order?.email || '-'}</span>
                                </GridCustomerInformationValue>
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={4}>
                                    <SpanCustomerInformationLabel>
                                        {t('commonLabels.phone')}
                                    </SpanCustomerInformationLabel>
                                </Grid>
                                <GridCustomerInformationValue item>
                                    {InternationalizationLogic.numberToPhone(
                                        internationalization,
                                        order?.landlinePhone || null
                                    )}
                                </GridCustomerInformationValue>
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={4}>
                                    <SpanCustomerInformationLabel>
                                        {t('commonLabels.mobile')}
                                    </SpanCustomerInformationLabel>
                                </Grid>
                                <GridCustomerInformationValue item>
                                    <span>
                                        {InternationalizationLogic.numberToPhone(
                                            internationalization,
                                            order?.mobilePhone || null
                                        )}
                                    </span>
                                </GridCustomerInformationValue>
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={1}>
                                <Grid item xs={4}>
                                    <SpanCustomerInformationLabel>
                                        {t('commonLabels.paymentMethod')}
                                    </SpanCustomerInformationLabel>
                                </Grid>
                                <GridCustomerInformationValue item>
                                    <span>
                                        {order?.paymentMethod
                                            ? t(PaymentMethodTypeLabel(order.paymentMethod))
                                            : '-'}
                                    </span>
                                </GridCustomerInformationValue>
                            </Grid>
                        </Grid>
                    </Grid>
                )}
            </CollapsibleSection>
            <CollapsibleSection title={t('orders.preview.vehicleInformation')} isCollapse={true}>
                <Grid
                    container
                    spacing={1}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                    alignContent="center"
                >
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <SpanCustomerInformationLabel>
                                    {t('commonLabels.make')}
                                </SpanCustomerInformationLabel>
                            </Grid>
                            <GridCustomerInformationValue item>
                                <span>{order?.make || '-'}</span>
                            </GridCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <SpanCustomerInformationLabel>
                                    {t('commonLabels.model')}
                                </SpanCustomerInformationLabel>
                            </Grid>
                            <GridCustomerInformationValue item>
                                <span>{order?.model || '-'}</span>
                            </GridCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <SpanCustomerInformationLabel>
                                    {t('commonLabels.year')}
                                </SpanCustomerInformationLabel>
                            </Grid>
                            <GridCustomerInformationValue item>
                                <span>{order?.year || '-'}</span>
                            </GridCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <SpanCustomerInformationLabel>
                                    {t('commonLabels.plates')}
                                </SpanCustomerInformationLabel>
                            </Grid>
                            <GridCustomerInformationValue item>
                                <span>{order?.plates || '-'}</span>
                            </GridCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <SpanCustomerInformationLabel>
                                    {t('commonLabels.vin')}
                                </SpanCustomerInformationLabel>
                            </Grid>
                            <GridCustomerInformationValue item>
                                <span>{order?.vin || '-'}</span>
                            </GridCustomerInformationValue>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Grid container spacing={1}>
                            <Grid item xs={3}>
                                <SpanCustomerInformationLabel>
                                    {t('commonLabels.mileage')}
                                </SpanCustomerInformationLabel>
                            </Grid>
                            <GridCustomerInformationValue item>
                                <span>
                                    {InternationalizationLogic.numberFormat(
                                        internationalization,
                                        order?.mileage
                                    ) + (order?.mileage ? ' km' : '')}
                                </span>
                            </GridCustomerInformationValue>
                        </Grid>
                    </Grid>
                </Grid>
            </CollapsibleSection>

            <CollapsibleEstimate
                title={`${t('orders.preview.businessOpportunity')} - ${t(
                    'commonLabels.priorities.urgent'
                )}`}
                total={InternationalizationLogic.numberToCurrency(
                    internationalization,
                    urgentEstimatesTotal,
                    {
                        requireDecimals: requireDecimals,
                    }
                )}
                estimateQuantity={order?.redItems ? order?.redItems.length : 0}
                priority={'Urgent'}
                isHide={false}
            >
                {order?.redItems && <BusinessOpportunityList items={order.redItems} />}
            </CollapsibleEstimate>

            <CollapsibleEstimate
                title={`${t('orders.preview.businessOpportunity')} - ${t(
                    'commonLabels.priorities.med'
                )}`}
                total={InternationalizationLogic.numberToCurrency(
                    internationalization,
                    medEstimatesTotal,
                    {
                        requireDecimals: requireDecimals,
                    }
                )}
                estimateQuantity={order?.yellowItems ? order?.yellowItems.length : 0}
                priority={'Med'}
                isHide={true}
            >
                {order?.yellowItems && <BusinessOpportunityList items={order.yellowItems} />}
            </CollapsibleEstimate>
        </>
    );
};

const GridCustomerInformationValue = styled(Grid)(({ theme }) => ({
    color: theme.palette.neutral[6],
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
    width: 0,
    flexGrow: 1,
    display: 'flex',
    alignItems: 'flex-end',
    textAlign: 'left',
    '& > span': {
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
    },
}));

const SpanCustomerInformationLabel = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    textAlign: 'left',
}));

const CollapsibleSection = ({
    title,
    isCollapse,
    children,
}: {
    title: string;
    isCollapse: boolean;
    children?: React.ReactNode;
}) => {
    const [isCollapsed, setIsCollapsed] = useState(isCollapse);
    return (
        <DivRoot>
            <DivTitleSection role="button" onClick={() => setIsCollapsed(!isCollapsed)}>
                <DivIconWrapper className={isCollapsed ? 'collapsed' : undefined}>
                    <UpIcon fill={Colors.CM1} size={IconSize.M} />
                </DivIconWrapper>
                <span>{title}</span>
            </DivTitleSection>
            <Collapse timeout={200} in={isCollapsed}>
                <DivContentWrapper>{children}</DivContentWrapper>
            </Collapse>
        </DivRoot>
    );
};

const DivRoot = styled('div')(({ theme }) => ({
    borderBottom: `1px solid ${theme.palette.neutral[3]}`,

    ':last-child': {
        borderBottom: 'none',
    },
}));

const DivTitleSection = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    padding: '18px 24px',
    textAlign: 'left',
    cursor: 'pointer',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],

    ':hover': {
        color: theme.palette.neutral[9],
    },

    ':active': {
        backgroundColor: theme.palette.neutral[2],
    },
}));

const DivIconWrapper = styled('div')({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 24,
    height: 24,
    marginRight: 10,
    transition: 'transform .2s',

    '&.collapsed': {
        transform: 'rotate(180deg)',
    },
});

const DivContentWrapper = styled('div')({
    padding: '8px 24px',
});

const CollapsibleEstimate = ({
    title,
    total,
    estimateQuantity,
    priority,
    isHide,
    children,
}: {
    title: string;
    total: string;
    estimateQuantity: number;
    priority: string;
    isHide: boolean;
    children?: JSX.Element | string;
}) => {
    const [isCollapsed, setIsCollapsed] = useState(isHide);
    return (
        <DivRoot>
            <DivTitleSection onClick={() => setIsCollapsed(!isCollapsed)}>
                <DivIconWrapper className={isCollapsed ? 'collapsed' : undefined}>
                    <UpIcon fill={Colors.CM1} size={IconSize.M} />
                </DivIconWrapper>
                <DivEstimateTitleLayout>
                    <div>
                        <span>{title}</span>&#160;(
                        <SpanEstimateTotalTitle>{total}</SpanEstimateTotalTitle>)
                    </div>
                    <DivPriority className={priority === 'Med' ? 'medEstimate' : 'urgentEstimate'}>
                        {estimateQuantity}
                    </DivPriority>
                </DivEstimateTitleLayout>
            </DivTitleSection>
            <Collapse timeout={200} in={isCollapsed}>
                <DivContentWrapper>{children}</DivContentWrapper>
            </Collapse>
        </DivRoot>
    );
};

const SpanEstimateTotalTitle = styled('span')({
    color: '#467cfc',
});

const DivPriority = styled('div')({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 20,
    height: 20,
    margin: '0px 2px 0 2px!important',
    borderRadius: '50%',
    fontFamily: 'inter',
    fontSize: 10,
    fontWeight: 'bold',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: 1.2,
    letterSpacing: 'normal',
    textAlign: 'center',
    color: '#ffffff',
    '&.medEstimate': {
        backgroundColor: '#ffc626',
    },
    '&.urgentEstimate': {
        backgroundColor: '#f15857',
    },
});

const DivEstimateTitleLayout = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr auto',
    flex: 1,
    alignItems: 'center',
});

const BusinessOpportunityList = ({ items }: { items: IPreviewItem[] }) => {
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;
    return (
        <div>
            {items.map((estimate, index) => (
                <div key={index}>
                    <DivEstimateRow>
                        <span>- {estimate.repairName}</span>
                        <span>
                            {InternationalizationLogic.numberToCurrency(
                                internationalization,
                                estimate.totalCost,
                                {
                                    requireDecimals: requireDecimals,
                                }
                            )}
                        </span>
                    </DivEstimateRow>
                    {estimate.subitems.length > 0
                        ? estimate.subitems.map((subItem, index) => (
                              <DivSubItemRow key={index}>
                                  <span>* {subItem.name}</span>
                                  <span>
                                      {InternationalizationLogic.numberToCurrency(
                                          internationalization,
                                          subItem.totalCost,
                                          {
                                              requireDecimals: requireDecimals,
                                          }
                                      )}
                                  </span>
                              </DivSubItemRow>
                          ))
                        : null}

                    {index + 1 !== items.length ? <Divider /> : null}
                </div>
            ))}
        </div>
    );
};

const DivSubItemRow = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '3px 5px 5px 5px',
    ...theme.typography.h8Inter,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
}));

const DivEstimateRow = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 5,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
}));

export default OrderPreviewDetails;
