import { debounce, styled } from '@mui/material';
import useForceRender from 'common/hooks/useForceRender';
import useRandomId from 'common/hooks/useRandomId';
import React, { CSSProperties, useEffect, useRef, useState } from 'react';

const tabsHeight = 46;

const DivPlaceholder = styled('div')({
    height: tabsHeight,
});

const AsideRoot = styled('aside')(({ theme }) => ({
    position: 'absolute',
    top: 'var(--header-height)',
    left: 0,
    width: '100%',
    boxSizing: 'border-box',
    transition: 'background-color .1s',
    height: tabsHeight,

    '&.fixed': {
        position: 'fixed',
        borderBottom: `1px solid ${theme.palette.neutral[3]}`,
        paddingBottom: 0,
        top: 'var(--header-height)',
        backgroundColor: '#fff',
        zIndex: 'var(--header-z-index)',
        boxSizing: 'content-box',
    },
}));

const DivInner = styled('div')({
    width: 'var(--inner-width)',
    margin: '0 auto',
    boxSizing: 'border-box',
    height: '100%',
    overflow: 'hidden',
});

function isElementInViewport(el: HTMLElement) {
    const rect = el.getBoundingClientRect();
    const css = window.getComputedStyle(el, null);
    const headerHeight = parseFloat(css.getPropertyValue('--header-height'));

    return (
        rect.top - headerHeight > -1 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

function useIsInBounds(el?: HTMLElement | null, disabled: boolean = false) {
    const inBoundsRef = useRef(true);
    const fr = useForceRender();

    useEffect(() => {
        if (!el || disabled) return () => {};

        const update = () => {
            const inBounds = isElementInViewport(el);
            if (inBounds !== inBoundsRef.current) {
                inBoundsRef.current = inBounds;
                fr();
            }
        };
        update();
        const callback = debounce(update, 10);

        document.addEventListener('scroll', callback);
        return () => document.removeEventListener('scroll', callback);
    }, [el, fr, disabled]);

    return inBoundsRef.current;
}

type TabsWrapperProps = React.PropsWithChildren<{
    innerWidth?: string;
    className?: string;
    style?: React.CSSProperties;

    /**
     * if set to true, tabs will always be in detached mode,
     * even if they are visible on screen
     */
    alwaysDetached?: boolean;
}>;

export function StickyTabsWrapper({
    children,
    innerWidth = '83.3%',
    className,
    style,
    alwaysDetached = false,
}: TabsWrapperProps) {
    const [tabsWrapper, setTabsWrapper] = useState<HTMLElement | null>(null);
    const inBounds = useIsInBounds(tabsWrapper, alwaysDetached);
    const id = useRandomId();
    const cssVariable = `--sticky-tabs-${id}-position-x`;

    useEffect(() => {
        if (!tabsWrapper) return;
        const updatePosition = () => {
            const pos = tabsWrapper.getBoundingClientRect();
            document.body.style.setProperty(cssVariable, `${Math.round(pos.x)}px`);
        };

        updatePosition();

        const removeProperty = () => {
            document.body.style.removeProperty(cssVariable);
        };

        if (window.ResizeObserver) {
            const observer = new window.ResizeObserver(updatePosition);
            observer.observe(tabsWrapper);

            return () => {
                observer.disconnect();
                removeProperty();
            };
        }

        return removeProperty;
    }, [tabsWrapper, cssVariable]);

    return (
        <div
            style={{ '--inner-width': innerWidth, ...style } as CSSProperties}
            className={className}
        >
            <DivPlaceholder ref={setTabsWrapper} />
            <AsideRoot className={inBounds && !alwaysDetached ? undefined : 'fixed'}>
                <DivInner style={{ marginLeft: `var(${cssVariable})` }}>{children}</DivInner>
            </AsideRoot>
        </div>
    );
}
