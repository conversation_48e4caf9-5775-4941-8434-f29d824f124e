import { ArrowBack } from '@mui/icons-material';
import { styled } from '@mui/material';
import { Button } from 'common/components/Button';
import Container from 'common/components/Container';
import { CarIcon } from 'common/components/Icons/CarIcon';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { FeatureFlags } from 'datacontracts/GlobalSettings/RepairShopSettingsDto';
import { useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from 'store';
import { selectRepairShopFeatures } from 'store/slices/globalSettingsSlice';

type FeatureDisabledPagePlaceholderProps = {
    devErrorMessage?: string;
    title?: string;
    text?: string;
    hint?: string;
};

function FeatureDisabledPagePlaceholder({
    devErrorMessage = 'disabled_feature:generic',
    title,
    text,
    hint,
}: FeatureDisabledPagePlaceholderProps) {
    const { t } = useAppTranslation();
    const navigate = useNavigate();
    const goBack = useCallback(() => navigate(-1), [navigate]);

    return (
        <Container>
            <Header>{title ?? t('toasters.disabledFeatureTitle')}</Header>

            <Content>
                <Text>{text ?? t('toasters.disabledFeatureBody')}</Text>

                {hint && <Text>{hint}</Text>}

                <Buttons>
                    <Button onClick={goBack}>
                        <ArrowBack />
                        {t('commonLabels.goBack')}
                    </Button>
                    <Button cmosVariant="stroke" href={ROUTES.ORDERS}>
                        <CarIcon />
                        {t('titles.orders')}
                    </Button>
                </Buttons>
                <p>
                    <Code>{devErrorMessage}</Code>
                </p>
            </Content>
        </Container>
    );
}

export type SwitchableFeatureProps = {
    enabled: boolean;
    children?: React.ReactElement;
} & FeatureDisabledPagePlaceholderProps;

export function SwitchableFeature({ enabled, children, ...props }: SwitchableFeatureProps) {
    if (enabled) {
        return children;
    }
    return <FeatureDisabledPagePlaceholder {...props} />;
}

export type FeaturesGateProps = Omit<SwitchableFeatureProps, 'enabled'> & {
    predicate: keyof FeatureFlags | (keyof FeatureFlags)[] | ((features: FeatureFlags) => boolean);
};

export function FeaturesGate({ predicate, devErrorMessage, ...props }: FeaturesGateProps) {
    const isEnterprise = useIsEnterpriseRoute();
    const features = useAppSelector(selectRepairShopFeatures);

    const { enabled, devMessage } = useMemo(() => {
        if (isEnterprise) {
            // There are no features for enterprise yet, so just return without checking
            return { enabled: true };
        }

        if (!features) {
            // If there are no features, there is no access
            return { enabled: false };
        }

        let enabled = false,
            devMessage = '';
        if (Array.isArray(predicate)) {
            enabled = true;
            devMessage = predicate.join(' & ');
            for (const perm of predicate) {
                if (!features[perm]) {
                    enabled = false;
                    break;
                }
            }
        } else if (typeof predicate === 'function') {
            enabled = predicate(features);
            devMessage = 'custom';
        } else {
            enabled = !!features[predicate];
            devMessage = predicate;
        }

        return { enabled, devMessage: `Required features: ${devMessage}` };
    }, [features, predicate, isEnterprise]);

    return (
        <SwitchableFeature
            {...props}
            enabled={enabled}
            devErrorMessage={devErrorMessage ?? devMessage}
        />
    );
}

const Content = styled('div')({
    paddingTop: 20,
});

const Header = styled('h1')(({ theme }) => ({
    ...theme.typography.h1Inter,
    fontWeight: 'bold',
    marginTop: 60,
}));

const Buttons = styled('div')({
    display: 'flex',
    gap: 10,
    marginTop: 40,
});

const Text = styled('p')({
    fontSize: '16px',
    whiteSpace: 'preserve-breaks',
});

const Code = styled('code')(({ theme }) => ({
    color: theme.palette.neutral[4],
    whiteSpace: 'pre',

    ':hover': {
        color: 'black',
    },
}));
