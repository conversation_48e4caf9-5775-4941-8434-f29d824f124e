import { styled } from '@mui/material';
import { AbsenceDto } from 'api/users';
import clsx from 'clsx';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { CSSProperties, useCallback, useEffect, useMemo, useRef } from 'react';
import { useAppSelector } from 'store';
import { selectIanaTz } from 'store/slices/globalSettingsSlice';
import { selectUsers } from 'store/slices/users';

type AbsencePartialDto = Pick<AbsenceDto, 'id' | 'reason' | 'startsAt' | 'endsAt' | 'userId'>;

type AbsenceBlockProps = {
    onClick?: React.MouseEventHandler;
    topOffset?: number;
    leftOffset?: number;
    absence: AbsencePartialDto;
    vertical: boolean;

    /**
     * if set, block's size will be determined based on the given ratio of
     * pixels per minute, if vertical is set to true, this will determine the height of the block,
     * otherwise width
     */
    pixelPerMinute?: number;
    overrideDuration?: number;

    /**
     * Amount of vertical space available for the block.
     */
    availableVerticalSpace?: number;
};

function getScale(height: number): number {
    const scale = height / 60;

    return Math.max(0.3, Math.min(1, scale));
}

function isSmall(height: number): boolean {
    return height <= 35;
}

export default function AbsenceBlock({
    onClick,
    absence,
    leftOffset = 10,
    topOffset = 10,
    vertical,
    pixelPerMinute,
    overrideDuration,
    availableVerticalSpace = Infinity,
}: AbsenceBlockProps) {
    const { t } = useAppTranslation();
    const ianaTz = useAppSelector(selectIanaTz);

    const users = useAppSelector(selectUsers);

    const timeStr = useCallback(
        (absence: AbsencePartialDto) => {
            const startDt = DateTime.fromISO(absence.startsAt).setZone(ianaTz);
            const endDt = DateTime.fromISO(absence.endsAt).setZone(ianaTz);
            if (startDt.hasSame(endDt, 'day')) {
                return `${startDt.toFormat('HH:mm')} - ${endDt.toFormat('HH:mm')}`;
            }
            return t('absences.block.allDay');
        },
        [t, ianaTz]
    );

    const rootElRef = useRef<HTMLDivElement | null>(null);

    const duration = useMemo(() => {
        if (overrideDuration) {
            return overrideDuration;
        }

        const startDt = DateTime.fromISO(absence.startsAt);
        const endDt = DateTime.fromISO(absence.endsAt);
        return endDt.diff(startDt).as('minutes');
    }, [overrideDuration, absence.startsAt, absence.endsAt]);

    const style = useMemo(() => {
        const s: CSSProperties = {};

        if (pixelPerMinute) {
            const size = pixelPerMinute * duration;
            s[vertical ? 'height' : 'width'] = `${size}px`;
        }

        return s;
    }, [pixelPerMinute, vertical, duration]);

    useEffect(() => {
        // I hate this, but it works
        if (!rootElRef.current) throw new Error();
        const rootEl = rootElRef.current;
        rootEl.style.setProperty('--absence-block-offset-y', `0px`);
        rootEl.style.setProperty('--absence-block-offset-x', `0px`);
        const scrollbarParent = getScrollableParent(rootEl);
        if (!scrollbarParent) return;

        let parentRect = scrollbarParent.getBoundingClientRect();

        const observer = new ResizeObserver(() => {
            parentRect = scrollbarParent.getBoundingClientRect();
            callback();
        });
        observer.observe(scrollbarParent);

        const callback = () => {
            const rootRect = rootEl.getBoundingClientRect();

            if (!vertical) {
                const rootElementOffset = rootRect.left;
                const diff = parentRect.left - rootElementOffset;

                if (diff > -leftOffset) {
                    rootEl.style.setProperty('--absence-block-offset-x', `${diff + leftOffset}px`);
                } else {
                    rootEl.style.setProperty('--absence-block-offset-x', `0px`);
                }
            } else {
                const rootElementOffset = rootRect.top;
                const diff = parentRect.top - rootElementOffset;

                if (diff > -topOffset) {
                    rootEl.style.setProperty('--absence-block-offset-y', `${diff + topOffset}px`);
                } else {
                    rootEl.style.setProperty('--absence-block-offset-y', '0px');
                }
            }
        };
        callback();

        scrollbarParent.addEventListener('scroll', callback);
        return () => {
            scrollbarParent.removeEventListener('scroll', callback);
            observer.disconnect();
        };
    }, [rootElRef, leftOffset, topOffset, vertical]);

    return (
        <DivRoot
            style={{ ...style, '--abb-scale': getScale(availableVerticalSpace) } as CSSProperties}
            ref={rootElRef}
            role="button"
            className={clsx(
                vertical ? 'AbsenceBlock-vertical' : undefined,
                isSmall(availableVerticalSpace) ? 'AbsenceBlock-sm' : undefined
            )}
            onClick={onClick}
        >
            <DivInner className="AbsenceBlock-inner">
                <div key={absence.id} data-absence-id={absence.id} data-test-id="absence-block">
                    <SpanReason>
                        <RemoveCircleIcon
                            fill="currentColor"
                            size={18 * getScale(availableVerticalSpace)}
                        />
                        {absence.reason}
                    </SpanReason>
                    <br />
                    {timeStr(absence)}
                    <br />
                    <strong>
                        {absence.userId
                            ? users[absence.userId]?.name
                            : t('absences.block.allUsers')}
                    </strong>
                </div>
            </DivInner>
        </DivRoot>
    );
}

function getScrollableParent(element: HTMLElement): HTMLElement | null {
    let node = element.parentElement;

    while (node !== null) {
        // once we encounter a parent that is marked with data-scroll-parent
        // attribute or with  data-overlayscrollbars-contents attribute
        // return this attribute as a scroll parent
        if (
            node.dataset.scrollParent ||
            node.classList.contains('data-scroll-parent') ||
            node.hasAttribute('data-overlayscrollbars-viewport')
        ) {
            return node;
        }

        node = node.parentElement;
    }

    return null;
}

const SpanReason = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[8],
    ...theme.typography.h5Inter,
    fontSize: `calc(${theme.typography.h5Inter.fontSize} * var(--abb-scale))`,
    lineHeight: `calc(${theme.typography.h5Inter.lineHeight} * var(--abb-scale))`,
    fontWeight: 'bold',
    display: 'inline-flex',
    gap: 4,
    left: 0,
}));

const DivInner = styled('div')({
    transform: 'translate(var(--absence-block-offset-x), var(--absence-block-offset-y))',
    position: 'absolute',
    top: 0,
    left: 0,
    padding: '2px 5px',
    whiteSpace: 'nowrap',
    display: 'flex',
    gap: 8,
    width: '100%',
    boxSizing: 'border-box',
});

const DivRoot = styled('div')(({ theme }) => ({
    display: 'block',
    backgroundColor: theme.palette.neutral[4],
    borderRadius: 5,
    height: '100%',
    width: '100%',
    cursor: 'pointer',
    boxSizing: 'border-box',
    position: 'relative',
    color: theme.palette.neutral[8],
    ...theme.typography.h6Inter,
    fontSize: `calc(${theme.typography.h6Inter.fontSize} * var(--abb-scale))`,
    lineHeight: `calc(${theme.typography.h6Inter.lineHeight} * var(--abb-scale))`,
    fontWeight: 'normal',
    border: `1px solid ${theme.palette.neutral[5]}`,
    '&:hover': {
        outline: '1px solid',
        outlineOffset: -1,
    },
    overflow: 'hidden',

    '&.AbsenceBlock-vertical': {
        '& .AbsenceBlock-inner': {
            flexDirection: 'column',
        },
    },

    '&.AbsenceBlock-sm .AbsenceBlock-inner': {
        paddingTop: 0,
        paddingBottom: 0,
    },
}));
