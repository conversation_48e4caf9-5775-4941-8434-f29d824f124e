import { Popover } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import OrderAPI from 'api/Order';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useDelay } from 'common/hooks/useDelay';
import { Colors } from 'common/styles/Colors';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { IconSize } from 'common/styles/IconSize';
import { useRef, useState } from 'react';
import { SendEmail } from './SendEmail';
import { SendSms } from './SendSms';
import styles from './styles.module.css';

type SendMessageProps = {
    canSendWhatsApp: boolean;
    canSendSms: boolean;
    canSendEmail: boolean;
    repairOrderId: number;
    email: string;
    number: string;
    inspectionLink: string;
};

export const SendMessage = ({
    canSendWhatsApp,
    canSendSms,
    canSendEmail,
    repairOrderId,
    email,
    number,
    inspectionLink,
}: SendMessageProps) => {
    const { t } = useAppTranslation();
    const [isOpened, setIsOpened] = useState(false);
    const [isMessageSentOpened, setIsMessageSentOpened] = useState(false);
    const [isSendEmailOpened, setIsSendEmailOpened] = useState(false);
    const [isSendSmsOpened, setIsSendSmsOpened] = useState(false);
    const [showLoader, setLoading] = useDelay();
    const button = useRef<HTMLButtonElement>(null);

    const handleButtonClick = () => {
        setIsOpened(!isOpened);
    };

    const handleClose = () => {
        setIsOpened(false);
    };

    const sendAll = () => {
        send(canSendWhatsApp, canSendSms, canSendEmail);
    };

    const send = async (sendWhatsApp: boolean, sendSms: boolean, sendEmail: boolean) => {
        setIsOpened(false);
        setLoading(true);
        await OrderAPI.message(repairOrderId, sendSms, sendWhatsApp, sendEmail);
        setLoading(false);
        setIsMessageSentOpened(true);
    };

    return (
        <>
            <Button w="md" cmosVariant={'stroke'} onClick={handleButtonClick} ref={button}>
                {showLoader ? (
                    <div className={styles.loaderContainer}>
                        <CircularProgress size={17} style={{ color: 'var(--cm2)' }} />
                    </div>
                ) : (
                    <CommentsIcon />
                )}
                <span className={styles.caption}>{t('communication.sendOrder')}</span>
                {isOpened ? <UpIcon /> : <DownIcon />}
            </Button>
            <Popover
                id={'popover'}
                open={isOpened}
                anchorEl={button.current}
                onClose={handleClose}
                elevation={0}
                transitionDuration={0}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: -8,
                    horizontal: 'left',
                }}
            >
                <div className={styles.container} style={{ minWidth: button.current?.offsetWidth }}>
                    {(canSendWhatsApp ||
                        (canSendWhatsApp && canSendSms) ||
                        (canSendWhatsApp && canSendEmail) ||
                        (canSendSms && canSendEmail)) && (
                        <div className={styles.group}>WhatsApp</div>
                    )}
                    {canSendWhatsApp && (
                        <button className={styles.item} onClick={() => send(true, false, false)}>
                            {t('communication.sendWa')}
                        </button>
                    )}
                    {canSendWhatsApp && canSendSms && !canSendEmail && (
                        <button className={styles.item} onClick={() => sendAll()}>
                            {t('communication.sendWaSms')}
                        </button>
                    )}
                    {canSendWhatsApp && canSendEmail && (
                        <button className={styles.item} onClick={() => send(true, false, true)}>
                            {t('communication.sendWaEmail')}
                        </button>
                    )}
                    {!canSendWhatsApp && canSendSms && canSendEmail && (
                        <button className={styles.item} onClick={() => sendAll()}>
                            {t('communication.sendSmsEmail')}
                        </button>
                    )}

                    <div className={styles.group}>E-mail</div>
                    {canSendEmail && (
                        <>
                            <button
                                className={styles.item}
                                onClick={() => send(false, false, true)}
                            >
                                {t('communication.sendEmail')}
                            </button>
                            <div className={styles.line} />
                        </>
                    )}
                    <button
                        className={styles.item}
                        onClick={() => {
                            setIsSendEmailOpened(true);
                            setIsOpened(false);
                        }}
                    >
                        {t('communication.sendEmailWithNote')}
                    </button>

                    {canSendSms && <div className={styles.group}>SMS</div>}
                    {canSendSms && (
                        <>
                            <button
                                className={styles.item}
                                onClick={() => send(false, true, false)}
                            >
                                {t('communication.sendSms')}
                            </button>
                            <div className={styles.line} />
                        </>
                    )}
                    {canSendSms && (
                        <button
                            className={styles.item}
                            onClick={() => {
                                setIsSendSmsOpened(true);
                                setIsOpened(false);
                            }}
                        >
                            {t('communication.sendSmsWithNote')}
                        </button>
                    )}
                    {canSendWhatsApp && canSendSms && canSendEmail && (
                        <button className={styles.item} onClick={() => sendAll()}>
                            {t('communication.sendWaSmsEmail')}
                        </button>
                    )}
                </div>
            </Popover>
            <Modal open={isMessageSentOpened} onClose={() => setIsMessageSentOpened(false)}>
                <div className={styles.messageSentContent}>
                    <div className={styles.messageSentIcon}>
                        <CheckIcon fill={Colors.Success} size={IconSize.M} />
                    </div>
                    <span
                        className={styles.messageSentCaption}
                        style={{
                            ...FontPrimary(HeaderStyles.H2_24px, true, Colors.Neutral8),
                        }}
                    >
                        {t('communication.messageSent')}
                    </span>
                </div>
            </Modal>
            <SendEmail
                open={isSendEmailOpened}
                initialEmail={email}
                repairOrderId={repairOrderId}
                inspectionLink={inspectionLink}
                onClose={() => {
                    setIsSendEmailOpened(false);
                }}
                onMessageSent={() => setIsMessageSentOpened(true)}
            />
            <SendSms
                open={isSendSmsOpened}
                initialNumber={number}
                repairOrderId={repairOrderId}
                inspectionLink={inspectionLink}
                onClose={() => {
                    setIsSendSmsOpened(false);
                }}
                onMessageSent={() => setIsMessageSentOpened(true)}
            />
        </>
    );
};

export default SendMessage;
