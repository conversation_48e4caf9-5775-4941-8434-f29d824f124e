.caption {
    margin-left: 3px;
    margin-right: 3px;
}

.container {
    border: 1px solid #c9cdd3;
    box-sizing: border-box;
    border-radius: 10px;
    overflow: hidden;
}

.group {
    background-color: #efefef;
    font-family: Roboto;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 13px;
    letter-spacing: 0em;
    text-align: left;
    height: 30px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    color: #6a6e72;
}

.item {
    background-color: transparent;
    cursor: pointer;
    border-style: none;
    display: block;
    height: 38px;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0em;
    text-align: left;
    color: #6a6e72;
    padding-left: 20px;
    width: 100%;
}

.item:focus {
    outline: none;
}

.line {
    border-bottom-style: solid;
    border-width: 1px;
    border-color: #efefef;
    margin-right: 10px;
    margin-left: 10px;
}

.loaderContainer {
    min-width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.messageSentContent {
    padding-top: 44px;
    padding-bottom: 48px;
    width: 421px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.messageSentCaption {
    text-align: center;
    width: 290px;
}

.messageSentIcon {
    display: flex;
    width: 50px;
    height: 50px;
    border: 1px solid #36ce91;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
}
