import { Box, Typography, styled } from '@mui/material';
import OrderAPI from 'api/Order';
import { SMenuItem } from 'common/components/mui';
import { SSelectTinted } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { InternationalizationLogic } from '../../../../business/InternationalizationLogic';
import { ShortcutLogic } from '../../../../business/ShortcutLogic';
import { phoneFormatRegexMask } from '../../../../common/FormatersHelper';
import { Button } from '../../../../common/components/Button';
import { UrlIcon } from '../../../../common/components/Icons/UrlIcon';
import { LimitedTextArea } from '../../../../common/components/Inputs';
import MaskedTextFormField from '../../../../common/components/Inputs/MaskedTextFormField';
import { Modal } from '../../../../common/components/Modal';
import { useInputValue } from '../../../../common/hooks/useInputValue';
import { Colors } from '../../../../common/styles/Colors';
import { ShortcutDto, ShortcutType } from '../../../../datacontracts/Shortcut/ShortcutDto';

interface SendSmsProps {
    open: boolean;
    repairOrderId: number;
    initialNumber: string;
    inspectionLink: string;
    onClose: Function;
    onMessageSent: () => void;
}

const BoxComponent = styled('div')({
    paddingLeft: '40px',
    paddingRight: '40px',
    paddingTop: '35px',
    paddingBottom: '35px',
    width: '750px',
    display: 'flex',
    flexDirection: 'column',
});

const Row = styled('div')({
    display: 'flex',
    gap: 5,
    marginTop: 16,
});

const Label = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    minWidth: 120,
}));

export const SendSms = ({
    open,
    repairOrderId,
    initialNumber,
    inspectionLink,
    onClose,
    onMessageSent,
}: SendSmsProps) => {
    const { t } = useAppTranslation();
    const { internationalization } = useSelector(selectSettings);
    const [number, setNumber] = useInputValue('');
    const [shortcuts, setShortcuts] = useState<ShortcutDto[]>([]);
    const textArea = useRef<HTMLTextAreaElement>(null);
    const [comment, setComment] = useInputValue('');
    const maxCommentLength = 150;
    const toasters = useToasters();

    useEffect(() => {
        (async () => {
            const shortcuts = await ShortcutLogic.list(repairOrderId);
            const smsShortcuts = shortcuts.filter((shortcut) => shortcut.type === ShortcutType.SMS);
            setShortcuts(smsShortcuts);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setNumber(initialNumber);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialNumber]);

    const insertText = (text: string) => {
        if (textArea.current) {
            textArea.current.focus();
            const value = textArea.current.value;

            const start = textArea.current.selectionStart;
            const end = textArea.current.selectionEnd;

            setComment(value.slice(0, start) + text + value.slice(end));

            textArea.current.selectionStart = textArea.current.selectionEnd = start + text.length;
        }
    };

    const send = async () => {
        try {
            await OrderAPI.sendCustomSMS(repairOrderId, number, comment);
            close();
            onMessageSent();
        } catch {
            toasters.danger(t('sendSms.sendFailedBody'), t('sendSms.sendFailedTitle'));
        }
    };

    const close = () => {
        setNumber(initialNumber);
        setComment('');
        onClose();
    };

    return (
        <Modal boxComponent={BoxComponent} open={open}>
            <Typography variant="h5Inter">{t('sendSms.sendSms')}</Typography>
            <Typography variant="h7Roboto" marginTop={1}>
                {t('sendSms.writeTheMessage')}
            </Typography>

            <Row>
                <Label>{t('sendSms.number')}</Label>
                <MaskedTextFormField
                    name={'number'}
                    label={''}
                    maxLength={InternationalizationLogic.maxLengthPhone(internationalization)}
                    value={number}
                    onChange={(e) => {
                        setNumber(e.target.value);
                    }}
                    mask={phoneFormatRegexMask(internationalization.phoneNumberFormat)}
                    placeholder={t('sendSms.numberPlaceholder')}
                />
            </Row>
            <Row>
                <Label>{t('sendSms.message')}</Label>
                <Box sx={{ display: 'flex', gap: 1 }}>
                    <SSelectTinted
                        disabled={shortcuts.length === 0}
                        slotProps={{
                            placeholder: {
                                sx: (theme) => ({
                                    ...theme.typography.h6Inter,
                                    color: theme.palette.primary.main,
                                }),
                            },
                        }}
                        placeholder={t('sendSms.shortcuts')}
                        value={null}
                    >
                        {shortcuts.map((shortcut) => {
                            return (
                                <SMenuItem
                                    key={shortcut.shortcutId}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        insertText(shortcut.text);
                                    }}
                                >
                                    {shortcut.name}
                                </SMenuItem>
                            );
                        })}
                    </SSelectTinted>
                    <Button cmosVariant="typography" onClick={() => insertText(inspectionLink)}>
                        {t('sendSms.insertLink')}

                        <UrlIcon />
                    </Button>
                </Box>
            </Row>
            <Box sx={{ mt: 2 }}>
                <LimitedTextArea
                    rows={4}
                    name={'comment'}
                    placeholder={t('sendSms.writeComment')}
                    hideLabel={true}
                    ref={textArea}
                    value={comment}
                    onChange={setComment}
                    maxLength={maxCommentLength}
                />
            </Box>
            <Box sx={{ display: 'flex', gap: 1, mt: 1, justifyContent: 'end' }}>
                <Button
                    blockMode
                    cmosVariant={'filled'}
                    color={Colors.Neutral3}
                    label={'Cancel'}
                    onClick={() => close()}
                    w="md"
                />
                <Button
                    blockMode
                    cmosVariant={'filled'}
                    color={Colors.CM1}
                    label={t('commonLabels.send')}
                    onClick={() => send()}
                    disabled={comment.length > maxCommentLength}
                    w="md"
                />
            </Box>
        </Modal>
    );
};
