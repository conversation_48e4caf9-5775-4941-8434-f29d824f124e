import { Box, Typography, styled } from '@mui/material';
import OrderAPI from 'api/Order';
import { AttachmentLogic } from 'business/AttachmentLogic';
import { ShortcutLogic } from 'business/ShortcutLogic';
import { Button } from 'common/components/Button';
import FileUploadButton from 'common/components/FileUploadButton';
import { UrlIcon } from 'common/components/Icons/UrlIcon';
import { LimitedTextArea } from 'common/components/Inputs';
import TextField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { SMenuItem } from 'common/components/mui';
import { SSelectTinted } from 'common/components/mui/SSelect';
import { SUPPORTED_EMAIL_ATTACHMENT_EXTENSIONS } from 'common/constants/FileExtensions';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { ShortcutDto, ShortcutType } from 'datacontracts/Shortcut/ShortcutDto';
import { useEffect, useRef, useState } from 'react';

type SendEmailProps = {
    open: boolean;
    repairOrderId: number;
    initialEmail: string;
    inspectionLink: string;
    onClose: Function;
    onMessageSent: () => void;
};

const BoxComponent = styled('div')({
    paddingLeft: '40px',
    paddingRight: '40px',
    paddingTop: '35px',
    paddingBottom: '35px',
    width: '750px',
    display: 'flex',
    flexDirection: 'column',
});

const Row = styled('div')({
    display: 'flex',
    gap: 5,
    marginTop: 16,
});

const Label = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    minWidth: 120,
}));

export const SendEmail = ({
    open,
    repairOrderId,
    initialEmail,
    inspectionLink,
    onClose,
    onMessageSent,
}: SendEmailProps) => {
    const maxMessageLength = 2000;
    const { t } = useAppTranslation();
    const [email, setEmail] = useState('');
    const [file, setFile] = useState<File | null>(null);
    const [shortcuts, setShortcuts] = useState<ShortcutDto[]>([]);
    const textArea = useRef<HTMLTextAreaElement>(null);
    const [comment, setComment] = useState('');
    const toasters = useToasters();

    useEffect(() => {
        (async () => {
            const shortcuts = await ShortcutLogic.list(repairOrderId);
            const emailShortcuts = shortcuts.filter(
                (shortcut) => shortcut.type === ShortcutType.email
            );
            setShortcuts(emailShortcuts);
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setEmail(initialEmail);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [initialEmail]);

    const handleFileChange = (file: File | null) => {
        setFile(file);
    };

    const insertText = (text: string) => {
        if (textArea.current) {
            textArea.current.focus();
            const value = textArea.current.value;

            const start = textArea.current.selectionStart;
            const end = textArea.current.selectionEnd;

            setComment(value.slice(0, start) + text + value.slice(end));

            textArea.current.selectionStart = textArea.current.selectionEnd = start + text.length;
        }
    };

    const send = async () => {
        try {
            let fileKey: string = '';
            if (file) {
                fileKey = await AttachmentLogic.upload(file);
            }
            await OrderAPI.sendMailWithNote(
                repairOrderId,
                email,
                comment,
                file?.name ?? '',
                fileKey
            );

            close();
            onMessageSent();
        } catch {
            toasters.danger(t('sendEmail.sendFailedBody'), t('sendEmail.sendFailedTitle'));
        }
    };

    const close = () => {
        setEmail(initialEmail);
        setFile(null);
        setComment('');

        onClose();
    };

    return (
        <Modal boxComponent={BoxComponent} open={open}>
            <Typography variant="h5Inter">{t('sendEmail.sendEmail')}</Typography>
            <Typography variant="h7Roboto" marginTop={1}>
                {t('sendEmail.writeTheMessage')}
            </Typography>
            <Row>
                <Label>{t('sendEmail.email')}</Label>
                <TextField
                    sx={{ width: 300 }}
                    name={'email'}
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={t('sendEmail.emailPlaceholder')}
                />
            </Row>
            <Row>
                <Label>{t('sendEmail.message')}</Label>
                <Box sx={{ display: 'flex', gap: 1 }}>
                    <FileUploadButton
                        label={t('sendEmail.attachFile')}
                        onFileDelete={() => setFile(null)}
                        onFileSelected={handleFileChange}
                        accept={SUPPORTED_EMAIL_ATTACHMENT_EXTENSIONS}
                    />
                    <SSelectTinted
                        disabled={shortcuts.length === 0}
                        slotProps={{
                            placeholder: {
                                sx: (theme) => ({
                                    ...theme.typography.h6Inter,
                                    color: theme.palette.primary.main,
                                }),
                            },
                        }}
                        placeholder={t('commonLabels.shortcuts')}
                        value={null}
                    >
                        {shortcuts.map((shortcut) => {
                            return (
                                <SMenuItem
                                    key={shortcut.shortcutId}
                                    onClick={(e) => {
                                        e.preventDefault();
                                        insertText(shortcut.text);
                                    }}
                                >
                                    {shortcut.name}
                                </SMenuItem>
                            );
                        })}
                    </SSelectTinted>
                    <Button cmosVariant="typography" onClick={() => insertText(inspectionLink)}>
                        {t('sendEmail.insertLink')}

                        <UrlIcon />
                    </Button>
                </Box>
            </Row>
            <Box sx={{ mt: 1 }}>
                <LimitedTextArea
                    name={'comment'}
                    placeholder={t('sendEmail.writeComment')}
                    hideLabel={true}
                    ref={textArea}
                    value={comment}
                    rows={4}
                    onChange={(e) => setComment(e.target.value)}
                    maxLength={maxMessageLength}
                />
            </Box>
            <Box sx={{ display: 'flex', gap: 1, mt: 1, justifyContent: 'end' }}>
                <Button
                    blockMode
                    cmosVariant={'filled'}
                    color={Colors.Neutral3}
                    label={'Cancel'}
                    onClick={() => close()}
                    w="md"
                />
                <Button
                    blockMode
                    cmosVariant={'filled'}
                    color={Colors.CM1}
                    label={t('commonLabels.send')}
                    onClick={() => send()}
                    disabled={comment.length > maxMessageLength}
                    w="md"
                />
            </Box>
        </Modal>
    );
};
