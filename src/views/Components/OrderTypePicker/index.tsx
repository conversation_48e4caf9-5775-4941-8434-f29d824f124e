import { useQuery } from '@tanstack/react-query';
import { OrderTypeDto, OrderTypesApi } from 'api/orders';
import { CircleIconWithBorder } from 'common/components/Icons';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';
import { OptionStyle } from 'common/styles/OptionStyle';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';

export type OrderTypePickerProps = {
    orderTypeId: string | null;
    onChange: (orderType: OrderTypeDto | null) => void;
} & Omit<DropdownProps<string>, 'options' | 'multiple' | 'optionStyle' | 'onChange' | 'value'>;

export default function OrderTypePicker({ orderTypeId, onChange, ...props }: OrderTypePickerProps) {
    const globalSettings = useSelector(selectSettings);
    const { data } = useQuery(
        ['orderTypes', globalSettings.appMode, globalSettings.id],
        OrderTypesApi.getList,
        {
            staleTime: 30000, // 30 seconds
            cacheTime: Infinity,
        }
    );

    const options = useMemo(
        () =>
            (data ?? []).map((v) => ({
                value: v.key,
                label: v.name,
                color: v.color,
                icon: CircleIconWithBorder,
            })),
        [data]
    );
    const selectedOption = useMemo(
        () => options.find((o) => o.value === orderTypeId),
        [options, orderTypeId]
    );

    return (
        <Dropdown
            cmosVariant="grey"
            options={options}
            multiple={false}
            value={selectedOption}
            slotProps={{ inputWrapper: { style: { minWidth: 200 } } }}
            optionStyle={OptionStyle.icons}
            onChange={(o) => {
                const orderType = o ? data?.find((ot) => ot.key === o.value) ?? null : null;
                onChange(orderType);
            }}
            {...props}
        />
    );
}
