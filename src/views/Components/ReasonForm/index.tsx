import { Box, Grid, Typography } from '@mui/material';
import MuiButton from '@mui/material/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import NoteEditor from '../NoteEditor';

type ReasonFormFormProps = {
    disabled?: boolean;
    onClickSave?: (value: string) => void;
};

const ReasonForm = ({ disabled, onClickSave }: ReasonFormFormProps) => {
    const { t } = useAppTranslation();
    const [inputMode, setInputMode] = useState<boolean>(false);

    const handleSave = (text: string) => {
        onClickSave && onClickSave(text.trim());
        setInputMode(false);
    };

    return (
        <Grid container spacing={0}>
            {inputMode ? (
                <Grid item xs={12}>
                    <NoteEditor
                        onEdit={(text) => {
                            handleSave(text);
                        }}
                        onCancel={() => {
                            setInputMode(false);
                        }}
                        placeholder={t('orderDetails.orderReasons.enterReason')}
                    />
                </Grid>
            ) : (
                <Grid item xs={12}>
                    <MuiButton
                        sx={(theme) => ({
                            border: `1px solid ${theme.palette.neutral[4]}`,
                            borderRadius: '4px !important',
                            background: theme.palette.neutral[2],
                            width: '100%',
                            height: 32,
                        })}
                        onClick={() => {
                            setInputMode(true);
                        }}
                        disabled={disabled}
                    >
                        <Box
                            component="div"
                            sx={{
                                display: 'block',
                                width: '100%',
                            }}
                        >
                            <Typography
                                component="span"
                                sx={(theme) => ({
                                    ...theme.typography.h6Inter,
                                    fontWeight: 'normal',
                                    color: theme.palette.neutral[7],
                                    textTransform: 'none',
                                    float: 'left',
                                    height: 24,
                                    display: 'flex',
                                    alignItems: 'center',
                                })}
                            >
                                {t('orderDetails.orderReasons.addReason')}
                            </Typography>
                            <Box
                                component="span"
                                sx={{
                                    float: 'right',
                                    height: 24,
                                }}
                            >
                                <PlusIcon fill="var(--neutral7)" />
                            </Box>
                        </Box>
                    </MuiButton>
                </Grid>
            )}
        </Grid>
    );
};

export default ReasonForm;
