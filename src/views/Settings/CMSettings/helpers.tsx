import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { CM_SETTINGS_TABS } from '../../../common/constants';
import { CampaignTab } from './CampaignsTab';
import FollowUpTab from './FollowUpTab';
import GeneralTab from './GeneralTab';
import SignaturesTab from './SignaturesTab';

export enum TabEnum {
    General = 'general',
    Signatures = 'signatures',
    Campaigns = 'campaigns',
    FollowUp = 'followup',
}

export type Tab = {
    id: TabEnum;
    section: string;
    label: string;
    component: React.ReactNode;
    isVisible: (glSettings: GlobalSettingsDto) => boolean;
};

export const tabs: Tab[] = [
    {
        id: TabEnum.General,
        section: CM_SETTINGS_TABS.GENERAL,
        label: 'settings.cm.general',
        component: <GeneralTab />,
        isVisible: () => true,
    },
    {
        id: TabEnum.Signatures,
        section: CM_SETTINGS_TABS.SIGNATURES,
        label: 'settings.cm.signatures',
        component: <SignaturesTab />,
        isVisible: () => true,
    },

    {
        id: TabEnum.FollowUp,
        section: CM_SETTINGS_TABS.FOLLOW_UP,
        label: 'settings.cm.followUp.header',
        component: <FollowUpTab />,
        isVisible: () => true,
    },
    {
        id: TabEnum.Campaigns,
        section: CM_SETTINGS_TABS.CAMPAIGNS,
        label: 'settings.cm.campaigns.tab',
        component: <CampaignTab />,
        isVisible: () => true,
    },
];
