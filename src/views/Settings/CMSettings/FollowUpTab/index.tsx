import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import PageContent from 'views/Components/Page';
import SurveysSettings from './SurveysSettings';

export default function FollowUpTab() {
    const { t } = useAppTranslation();

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.cm.followUp.header')}`);

    return <PageContent paddedX>{<SurveysSettings />}</PageContent>;
}
