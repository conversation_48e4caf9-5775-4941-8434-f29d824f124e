import { useMutation, useQuery } from '@tanstack/react-query';
import SurveysApi from 'api/surveys';
import { SurveySettingsDto } from 'api/surveys/_common';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from 'store';
import PhasePicker from 'views/Components/PhasePicker';
import {
    BooleanSettingControl,
    NumberSettingControl,
    SettingLayoutControl,
    SettingsSection,
} from 'views/Settings/common';
import useSettingStyles from 'views/Settings/common/styles';
import PersonalizedQuestionList from '../PersonalizedQuestionList';
import { getDefaultSurveys } from './helpers';

export default function SurveysSettings() {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const styles = useSettingStyles();
    const [settings, setSettings] = useState<SurveySettingsDto>(() => getDefaultSurveys());

    const gs = useSelector((r: RootState) => ({
        uid: r.globalSettings.settings?.uid,
    }));

    useQuery(
        ['surveys', 'settings', gs.uid],
        () => {
            return SurveysApi.settings.getWithQuestions();
        },
        {
            onSuccess: (data) => {
                setSettings(data.settings);
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenLoading'), '');
            },
        }
    );

    const updateSettings = (newSettings: SurveySettingsDto) => {
        setSettings(newSettings);
        updateMutation.mutate(newSettings);
    };

    const updateMutation = useMutation(
        (settings: SurveySettingsDto) => SurveysApi.settings.update(settings),
        {
            onSuccess: (data) => {
                toasters.success('', t('toasters.updatedConfiguration'));
            },
            onError: () => {},
        }
    );

    return (
        <SettingsSection label={t('settings.cm.surveys.header')}>
            <BooleanSettingControl
                label={t('settings.cm.surveys.enable')}
                value={settings.enableSurveys}
                onChange={(enableSurveys) => updateSettings({ ...settings, enableSurveys })}
            />
            <SettingLayoutControl label={t('settings.cm.surveys.phase')}>
                <PhasePicker
                    cmosVariant="default"
                    name="survey-phase"
                    phaseId={settings.triggeringPhaseId}
                    onChange={(triggeringPhaseId) =>
                        updateSettings({ ...settings, triggeringPhaseId })
                    }
                    className={styles.settingsControl}
                />
            </SettingLayoutControl>
            <NumberSettingControl
                required
                validate={(v) => v >= 0 && v <= 99999}
                label={t('settings.cm.surveys.days')}
                name="sendSurveyAfterDays"
                value={settings.sendSurveyAfterDaysPass}
                onValueChange={(sendSurveyAfterDaysPass) =>
                    updateSettings({ ...settings, sendSurveyAfterDaysPass })
                }
                className={styles.settingsControl}
            />
            <BooleanSettingControl
                label={t('settings.cm.surveys.personalizeQuestions')}
                hasHint={true}
                hintText={t('settings.cm.surveys.personalizeQuestionsTooltip')}
                value={settings.enablePersonalizedSurveys}
                onChange={(enablePersonalizedSurveys) =>
                    updateSettings({ ...settings, enablePersonalizedSurveys })
                }
            />
            {settings.enablePersonalizedSurveys && <PersonalizedQuestionList />}
        </SettingsSection>
    );
}
