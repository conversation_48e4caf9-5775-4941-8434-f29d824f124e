import { makeStyles } from '@mui/styles';

export const useStyles = makeStyles((theme) => ({
    questionContainer: {
        display: 'flex',
        flexDirection: 'column',
        rowGap: 10,
    },
    questionLabel: {
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'row',
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
    },
    textBlock: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',

        padding: '16px 29px 16px 16px',
        marginBottom: 10,
        backgroundColor: theme.palette.neutral[2],

        border: `1px solid #C9CDD3`,
        borderRadius: 14,

        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '14px',
        lineHeight: '17px',
        color: theme.palette.neutral[5],
        width: '60%',
    },
    blueText: {
        color: theme.palette.primary.light,
    },
    footerPrimaryText: {
        marginBottom: 6,
    },
    editorWrapper: {
        position: 'relative',
        alignSelf: 'stretch',
        margin: '16px 0',
    },
    toolbar: {
        marginBottom: 0,
        borderRadius: '10px 10px 0px 0px',
    },
    content: {
        borderRadius: '0px 0px 10px 10px',
        color: theme.palette.neutral[8],
        height: 152,
        maxHeight: 152,
        overflow: 'auto',
    },
    charCounter: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 400,
        fontSize: '10px',
        lineHeight: '12px',
        position: 'absolute',
        right: 20,
        bottom: 10,
        color: theme.palette.neutral[5],
    },
}));
