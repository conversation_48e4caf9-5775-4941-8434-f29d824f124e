import { Editor } from '@tiptap/core';
import { AnswerType, PersonalizedQuestionDto } from 'api/surveys/_common';
import { Button } from 'common/components/Button';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import RichTextEditor from 'common/components/Inputs/RichTextEditor';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useEffect, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { ValueDescriptor } from 'views/Components/RadioButtonArray';
import RadioButtonArrayControl from 'views/Settings/common/RadioButtonArrayControl';
import {
    getPlaceholderKeyByOption,
    getTextBoxFooterPrimaryByOption,
    getTextBoxFooterSecondaryByOption,
} from '../helpers';
import { useStyles } from './css';

export interface PersonalizedQuestionProps {
    question: PersonalizedQuestionDto;

    onUpdate: (value: PersonalizedQuestionDto) => void;
    onDeletion: (question: PersonalizedQuestionDto) => void;
}

const PersonalizedQuestion = (props: PersonalizedQuestionProps) => {
    const getDefaultPlaceholder = (option: AnswerType): string => {
        return t(getPlaceholderKeyByOption(option));
    };

    const { t } = useAppTranslation();
    const classes = useStyles();
    const toasters = useToasters();
    const textLength = useRef(0);

    const [placeholder, setPlaceholder] = useState<string>(
        getDefaultPlaceholder(props.question.answerType)
    );
    const [footerTextPrimary, setFooterTextPrimary] = useState<string>('');
    const [footerTextSecondary, setFooterTextSecondary] = useState<string>('');

    const [text, setText] = useState<string>(props.question.text);
    const [type, setType] = useState<AnswerType>(props.question.answerType);

    const [isTextValid, setIsTextValid] = useState<boolean>(true);
    const [textIsChanged, setTextIsChanged] = useState<boolean>(false);
    const [typeIsChanged, setTypeIsChanged] = useState<boolean>(false);

    const wasModifiedAtLeastOnceRef = useRef(false);
    const maxTextLength: number = 900;

    const onHtmlChange = (value: string, editor: Editor) => {
        const txt = editor.getText().replace(/\s{5,}/, '    ');
        const isValid = txt.indexOf('\n') === -1;

        textLength.current = txt.length;
        wasModifiedAtLeastOnceRef.current = true;

        if (isValid) {
            setText(value.replace(/\s{5,}/, '    '));
        }

        setIsTextValid(isValid);
        setTextIsChanged(true);
    };

    useEffect(() => {
        if (!isTextValid) {
            toasters.warning(
                t('settings.cm.surveys.personalizedQuestions.lineBrakesWarn'),
                t('commonLabels.error')
            );
        }
    }, [isTextValid]);

    const options: ValueDescriptor<AnswerType>[] = [
        {
            id: '1',
            value: 'Numeric1To5',
            label: t('settings.cm.surveys.personalizedQuestions.1to5Option.label'),
        },
        {
            id: '2',
            value: 'Numeric1To10',
            label: t('settings.cm.surveys.personalizedQuestions.1to10Option.label'),
        },
        {
            id: '3',
            value: 'YesNo',
            label: t('settings.cm.surveys.personalizedQuestions.yesNoOption.label'),
        },
    ];

    const onOptionChanged = (option: AnswerType) => {
        setPlaceholder(t(getPlaceholderKeyByOption(option)));
        setFooterTextPrimary(t(getTextBoxFooterPrimaryByOption(option)));
        setFooterTextSecondary(t(getTextBoxFooterSecondaryByOption(option)));
    };

    const updateQuestion = () => {
        props.onUpdate({
            id: props.question.id,
            number: props.question.number,
            answerType: type,
            text: text,
        });
    };

    useEffect(() => {
        onOptionChanged(type);
        if (typeIsChanged) {
            updateQuestion();
        }
    }, [type, typeIsChanged]);

    const updateText = () => {
        if (textIsChanged && isTextValid) {
            updateQuestion();
            setTextIsChanged(false);
        }
    };

    return (
        <div className={classes.questionContainer}>
            <div className={classes.questionLabel}>
                <div>{`${t('settings.cm.surveys.personalizedQuestions.question')} ${
                    props.question.number
                }`}</div>
                {props.question.number > 1 && (
                    <Button
                        cmosVariant={'typography'}
                        color={Colors.Error}
                        Icon={RemoveCircleIcon}
                        onClick={() => props.onDeletion(props.question)}
                    />
                )}
            </div>

            <RadioButtonArrayControl<AnswerType>
                values={options}
                value={type}
                label={t('settings.cm.surveys.personalizedQuestions.selectAnswerOption')}
                onChange={(value) => {
                    setType(value);
                    setTypeIsChanged(true);
                }}
            />
            <div className={classes.textBlock}>
                <div>
                    {props.question.number === 1 ? (
                        <Trans
                            i18nKey="settings.cm.surveys.personalizedQuestions.textBoxHeaderPrimary"
                            values={{
                                blueText: classes.blueText,
                            }}
                            components={{ 1: <span /> }}
                        />
                    ) : (
                        t('settings.cm.surveys.personalizedQuestions.textBoxHeaderSecondary')
                    )}
                </div>
                <div className={classes.editorWrapper}>
                    <RichTextEditor
                        _disableEnter={true}
                        editorButtons={['bold', 'italic', 'strike']}
                        html={text}
                        classes={{ toolbar: classes.toolbar, content: classes.content }}
                        changeCallbackBehavior="onUpdate"
                        maxTextLength={maxTextLength}
                        onHtmlChange={(value, _, editor) => onHtmlChange(value, editor)}
                        placeholder={placeholder}
                        onBlur={updateText}
                    />
                    <span className={classes.charCounter}>
                        {textLength.current + '/' + maxTextLength}
                    </span>
                </div>
                <div>
                    <div className={classes.footerPrimaryText}>
                        <b>{footerTextPrimary}</b>
                    </div>
                    <div>{footerTextSecondary}</div>
                </div>
            </div>
        </div>
    );
};

export default PersonalizedQuestion;
