import Grid from '@mui/material/Grid/Grid';
import { useMutation, useQuery } from '@tanstack/react-query';
import SurveysApi from 'api/surveys';
import { PersonalizedQuestionDto, PutQuestionDto, QuestionDto } from 'api/surveys/_common';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from 'store';
import PersonalizedQuestion from './PersonalizedQuestion';
import { useStyles } from './css';
import { getPlaceholderKeyByOption } from './helpers';

export interface PersonalizedQuestionListProps {
    questions: QuestionDto[];
}

interface UpdateDto {
    id: string;
    dto: PutQuestionDto;
}

const PersonalizedQuestionList = () => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const defaultOption = 'Numeric1To5';

    const [questions, setQuestions] = useState<PersonalizedQuestionDto[]>([]);

    const gs = useSelector((r: RootState) => ({
        uid: r.globalSettings.settings?.uid,
    }));

    const { refetch } = useQuery(
        ['surveys', 'questions', gs.uid],
        () => {
            return SurveysApi.settings.getWithQuestions();
        },
        {
            onSuccess: (data) => {
                if (data.questions.length === 0) {
                    addQuestion();
                } else {
                    const qs = data.questions.map((q, index) => {
                        return {
                            answerType: q.answerType,
                            text: q.text,
                            id: q.id,
                            number: index + 1,
                        };
                    });

                    setQuestions(qs);
                }
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenLoading'), '');
            },
            enabled: false,
        }
    );

    useEffect(() => {
        refetch();
    }, []);

    const createMutation = useMutation((dto: PutQuestionDto) => SurveysApi.questions.create(dto), {
        onSuccess: (data) => {
            let lastNumber = 0;

            if (questions.length > 0) {
                questions.sort((a, b) => a.number - b.number);
                lastNumber = questions[questions.length - 1].number;
            }

            const newQuestion: PersonalizedQuestionDto = {
                answerType: data.answerType,
                text: data.text,
                id: data.id,
                number: lastNumber + 1,
            };

            setQuestions([...questions, newQuestion]);

            toasters.success(
                t('settings.cm.surveys.personalizedQuestions.questionsSavedText'),
                t('settings.cm.surveys.personalizedQuestions.questionsSavedTitle')
            );
        },
        onError: () => {},
    });

    const deleteMutation = useMutation((id: string) => SurveysApi.questions.delete_(id), {
        onSuccess: () => {
            toasters.success('', t('toasters.updatedConfiguration'));
        },
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), '');
        },
    });

    const updateMutation = useMutation(
        (updateDto: UpdateDto) => SurveysApi.questions.update(updateDto.id, updateDto.dto),
        {
            onSuccess: () => {
                toasters.success(
                    t('settings.cm.surveys.personalizedQuestions.questionsSavedText'),
                    t('settings.cm.surveys.personalizedQuestions.questionsSavedTitle')
                );
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenLoading'), '');
            },
        }
    );

    const addQuestion = () => {
        createMutation.mutate({
            type: defaultOption,
            text: t(getPlaceholderKeyByOption(defaultOption)),
        });
    };

    const deleteQuestion = (question: PersonalizedQuestionDto) => {
        const updatedQuestions: PersonalizedQuestionDto[] = questions
            .filter((q) => {
                return q.id !== question.id;
            })
            .map((q) => {
                return { ...q, number: q.number > question.number ? q.number - 1 : q.number };
            });

        setQuestions(() => [...updatedQuestions]);

        deleteMutation.mutate(question.id);
    };

    const updateQuestion = (question: PersonalizedQuestionDto) => {
        const dto: PutQuestionDto = {
            text: question.text,
            type: question.answerType,
        };
        updateMutation.mutate({ id: question.id, dto });
    };

    return (
        <>
            <div className={classes.container}>
                {questions.map((q) => {
                    return (
                        <PersonalizedQuestion
                            question={q}
                            onDeletion={deleteQuestion}
                            onUpdate={updateQuestion}
                            key={q.id}
                        />
                    );
                })}
                <Grid item container>
                    <Grid item xs={3} md={2}>
                        <Button
                            className={classes.addButton}
                            cmosVariant={'stroke'}
                            cmosSize={'medium'}
                            iconPosition="right"
                            color={Colors.GrayBlue}
                            Icon={PlusIcon}
                            label={t('settings.cm.surveys.personalizedQuestions.addQuestion')}
                            onClick={addQuestion}
                            disabled={createMutation.isLoading}
                            showLoader={createMutation.isLoading}
                        />
                    </Grid>
                </Grid>
            </div>
        </>
    );
};

export default PersonalizedQuestionList;
