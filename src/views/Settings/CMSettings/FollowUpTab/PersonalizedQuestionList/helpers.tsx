import { AnswerType } from 'api/surveys/_common';

export const getPlaceholderKeyByOption = (option: AnswerType): string => {
    switch (option) {
        case 'Numeric1To5':
            return 'settings.cm.surveys.personalizedQuestions.1to5Option.placeholder';

        case 'Numeric1To10':
            return 'settings.cm.surveys.personalizedQuestions.1to10Option.placeholder';

        case 'YesNo':
            return 'settings.cm.surveys.personalizedQuestions.yesNoOption.placeholder';

        default:
            return '';
    }
};

export const getTextBoxFooterPrimaryByOption = (option: AnswerType): string => {
    switch (option) {
        case 'Numeric1To5':
            return 'settings.cm.surveys.personalizedQuestions.1to5Option.textBoxFooterPrimary';

        case 'Numeric1To10':
            return 'settings.cm.surveys.personalizedQuestions.1to10Option.textBoxFooterPrimary';

        case 'YesNo':
            return 'settings.cm.surveys.personalizedQuestions.yesNoOption.textBoxFooterPrimary';

        default:
            return '';
    }
};

export const getTextBoxFooterSecondaryByOption = (option: AnswerType): string => {
    switch (option) {
        case 'Numeric1To5':
            return 'settings.cm.surveys.personalizedQuestions.1to5Option.textBoxFooterSecondary';

        case 'Numeric1To10':
            return 'settings.cm.surveys.personalizedQuestions.1to10Option.textBoxFooterSecondary';

        case 'YesNo':
            return '';

        default:
            return '';
    }
};
