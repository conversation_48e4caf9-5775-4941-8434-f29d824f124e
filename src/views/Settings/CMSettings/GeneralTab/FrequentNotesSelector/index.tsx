import { Grid, styled } from '@mui/material';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { FrequentNote, FrequentNoteType } from 'datacontracts/Settings/CM';
import { useMemo } from 'react';
import { SettingLayoutControl } from 'views/Settings/common';
import { TagsSelector } from '../TagsSelector';

type FrequentNotesSelectorProps = {
    allFrequentNotes: FrequentNote[];
    frequentNotes: FrequentNote[];
    notesType: FrequentNoteType;
    onFrequentNoteAdd: (option: OptionData, noteType: FrequentNoteType) => Promise<void>;
    onFrequentNoteRemove: (option: OptionData) => Promise<void>;
};

export default function FrequentNotesSelector({
    allFrequentNotes,
    frequentNotes,
    notesType,
    onFrequentNoteAdd,
    onFrequentNoteRemove,
}: FrequentNotesSelectorProps) {
    const { t } = useAppTranslation();

    const filteredAllFrequentNotes = useMemo(
        () => allFrequentNotes.filter((x) => x.type === notesType),
        [allFrequentNotes, notesType]
    );
    const filteredFrequentNotes = useMemo(
        () => frequentNotes.filter((x) => x.type === notesType),
        [frequentNotes, notesType]
    );

    const blanksCount = useMemo(
        () => filteredFrequentNotes.filter((x) => x.text.match(/[_]{1,}/g)).length,
        [filteredFrequentNotes]
    );

    return (
        <SettingLayoutControl
            label={
                notesType === 'ForCustomer'
                    ? t('settings.cm.mobileApp.frequentNotesForCustomerLabel')
                    : t('settings.cm.mobileApp.frequentNotesForInternalLabel')
            }
            hasHint
            hintText={
                notesType === 'ForCustomer'
                    ? t('settings.cm.mobileApp.frequentNotesForCustomerHint')
                    : t('settings.cm.mobileApp.frequentNotesForInternalHint')
            }
            hintPosition="right"
        >
            <Grid container style={{ rowGap: 10 }}>
                <TagsSelector
                    allOptions={filteredAllFrequentNotes.map((fn) => ({
                        label: fn.text,
                        value: fn.frequentNoteId,
                    }))}
                    selected={filteredFrequentNotes.map((fn) => ({
                        label: fn.text,
                        value: fn.frequentNoteId,
                    }))}
                    addTag={(option) => onFrequentNoteAdd(option, notesType)}
                    removeTag={onFrequentNoteRemove}
                />

                <Grid item container justifyContent="flex-end">
                    <Grid item container style={{ gap: 8 }}>
                        <GridOptionNoticeIcon
                            item
                            container
                            justifyContent="center"
                            sx={{ backgroundColor: 'var(--neutral5)' }}
                        >
                            <InfoIcon size={14} fill="var(--neutral1)" />
                        </GridOptionNoticeIcon>
                        <GridOptionNoticeLabel item>
                            {t('settings.cm.mobileApp.frequentNotesNote1')}
                        </GridOptionNoticeLabel>
                    </Grid>
                    <Grid item container style={{ gap: 8, marginTop: 6 }}>
                        <GridOptionNoticeIcon
                            item
                            container
                            justifyContent="center"
                            style={{ backgroundColor: 'var(--cm1)' }}
                        >
                            <SpanNumber>{blanksCount}</SpanNumber>
                        </GridOptionNoticeIcon>
                        <GridOptionNoticeLabel item>
                            {t('settings.cm.mobileApp.frequentNotesNote2')}
                        </GridOptionNoticeLabel>
                    </Grid>
                </Grid>
            </Grid>
        </SettingLayoutControl>
    );
}

const GridOptionNoticeIcon = styled(Grid)(({ theme }) => ({
    ...theme.typography.h7Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[1],

    width: 14,
    height: 14,
    borderRadius: '50%',
}));

const GridOptionNoticeLabel = styled(Grid)(({ theme }) => ({
    ...theme.typography.h7Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[6],
}));

const SpanNumber = styled('span')({ marginTop: 1 });
