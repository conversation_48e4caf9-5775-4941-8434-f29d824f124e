import { Box } from '@mui/material';
import { MobileSignatureDto } from 'api/settings';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import PhasePicker from 'views/Components/PhasePicker';
import LabeledSettingsCheckbox from 'views/Settings/common/LabeledSettingsCheckbox';

type MobilePhaseSettingsEntryProps = {
    label: string;
    signature?: MobileSignatureDto;
    onChange: (signature: MobileSignatureDto) => void;
    disabled?: boolean;
    isSignatureDisabled: boolean;
    width?: number;
};

const DEFAULT_INTERNAL_STATE: MobileSignatureDto = {
    phaseId: null,
    required: false,
};

export default function MobilePhaseSettingsEntry({
    label,
    onChange,
    signature,
    disabled = false,
    isSignatureDisabled,
    width,
}: MobilePhaseSettingsEntryProps) {
    const { t } = useAppTranslation();
    const [internal, setInternal] = useState(signature ?? DEFAULT_INTERNAL_STATE);

    useEffect(() => {
        setInternal(signature ?? DEFAULT_INTERNAL_STATE);
    }, [signature]);

    const update = (upd: Partial<MobileSignatureDto>) => {
        const newValue = { ...internal, ...upd };
        onChange(newValue);
        setInternal(newValue);
    };

    const labelWidth = width ? Math.floor((width * 40) / 100) : undefined;
    const dropdownWidth = labelWidth && width ? width - labelWidth : undefined;

    return (
        <Box sx={{ display: 'flex' }} style={{ gap: 8, width }}>
            <LabeledSettingsCheckbox
                tooltip={
                    isSignatureDisabled
                        ? t('settings.cm.mobileApp.signatureDisabledHint')
                        : undefined
                }
                checked={internal.required}
                label={label}
                disabled={disabled || isSignatureDisabled}
                onChange={(_, checked) => update({ required: checked })}
                width={labelWidth}
            />
            {/* i kinda hate this, but touching Dropdown is a bad idea considering CMOS-1993 is merging soon */}
            <span style={{ minWidth: 150, width: dropdownWidth }}>
                <PhasePicker
                    cmosVariant="default"
                    placeholder={t('settings.cm.mobileApp.phaseToSign')}
                    disabled={disabled || !internal.required || isSignatureDisabled}
                    //size={InputSize.S}
                    phaseId={internal.phaseId}
                    onChange={(phaseId) => update({ phaseId })}
                />
            </span>
        </Box>
    );
}
