import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    tab: {
        height: '80vh',
    },
    contentWrapper: {
        width: '100%',
    },
    content: {
        padding: '0 50px',
        flexWrap: 'nowrap',
    },
    optionTitle: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
    },
    optionRadioButtonWrapper: {
        display: 'flex',
        flexDirection: 'column',
        gap: 4,
    },
    optionRadioButton: {
        padding: 0,
    },
    optionRadioButtonDescription: {
        ...FontSecondary(HeaderStyles.H7_11px, false, theme.palette.neutral[6]),
        paddingLeft: 28,
    },
    optionNoticeIcon: {
        width: 14,
        height: 14,
        borderRadius: '50%',
        ...FontSecondary(HeaderStyles.H7_11px, false, theme.palette.neutral[1]),
    },
    optionNoticeLabel: {
        ...FontSecondary(HeaderStyles.H7_11px, false, theme.palette.neutral[6]),
    },
    startingOrderTextField: {
        width: '64px',
    },
}));
