import { styled } from '@mui/material';
import { useCallback, useMemo, useState } from 'react';
import { DropdownMulti } from '../../../../../common/components/Inputs';
import { OptionData } from '../../../../../common/components/Inputs/Dropdown/DataOption';
import { sortOptions } from './helper';

type ITagsSelectorProps = {
    allOptions: OptionData[];
    selected: OptionData[];
    addTag: (tag: OptionData) => Promise<void>;
    removeTag: (tag: OptionData) => Promise<void>;
};

const SDropdownMulti = styled(DropdownMulti)({
    width: '100%',
});

export const TagsSelector = ({ allOptions, selected, addTag, removeTag }: ITagsSelectorProps) => {
    const [isInputInvalid, setIsInputInvalid] = useState<boolean>(false);

    const availableOptions = useMemo(() => {
        const result = allOptions.filter((op) => !selected.some((se) => se.label === op.label));
        return result;
    }, [allOptions, selected]);

    const createHandler = useCallback(
        (data: OptionData) => {
            const trimmedLabel = data.label.trim();
            if (trimmedLabel === '') return;

            const duplicated = allOptions.some((o) => o.label === trimmedLabel);
            if (duplicated) return;

            addTag({
                ...data,
                label: trimmedLabel,
            });
        },
        [addTag, allOptions]
    );

    const checkInputOnDuplication = useCallback(
        (input: string) => {
            const trimmedLabel = input.trim();
            const onlyWhitespaces = trimmedLabel === '' && input !== trimmedLabel;
            const sameOptionExists = selected.some((o) => o.label === trimmedLabel);
            setIsInputInvalid(onlyWhitespaces || sameOptionExists);
        },
        [selected]
    );

    return (
        <SDropdownMulti
            options={availableOptions.sort(sortOptions)}
            value={selected.sort(sortOptions)}
            onCreate={createHandler}
            onSelect={addTag}
            onRemove={removeTag}
            onInputChange={checkInputOnDuplication}
            isInvalid={isInputInvalid}
            creatable
        />
    );
};
