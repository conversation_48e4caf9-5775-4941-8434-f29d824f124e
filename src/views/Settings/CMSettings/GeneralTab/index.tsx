import { styled, useMediaQuery } from '@mui/material';
import { QueryKey, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    AutoRoSettingAPI,
    FrequentNotesAPI,
    GeneralSettingsAPI,
    MobileSettingsDto,
    MobileSignatureDto,
} from 'api/settings';
import TeamMemberSignatureSettingsApi, {
    TeamMemberSignatureSettingsDto,
    getDefaultTeamMemberSignatureSettings,
} from 'api/settings/TeamMemberSignature';
import AreaSpinner from 'common/components/AreaSpinner';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import TextFormField from 'common/components/Inputs/TextField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import {
    AutoROSetting,
    FrequentNote,
    FrequentNoteType,
    GeneralSettings,
    getDefaultGeneralSettings,
} from 'datacontracts/Settings/CM';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { isNetworkError } from 'services/Server';
import { useAppSelector } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { globalSettingsActions, selectSettings } from 'store/slices/globalSettingsSlice';
import PageContent from 'views/Components/Page';
import {
    BooleanSettingControl,
    RadioButtonArrayControl,
    SettingLayoutControl,
    SettingsSection,
} from 'views/Settings/common';
import CheckboxArrayControl from 'views/Settings/common/CheckboxArrayControl';
import { useStyles } from './css';
import FrequentNotesSelector from './FrequentNotesSelector';
import MobilePhaseSettingsEntry from './MobileSignatureSettingsEntry';

const GeneralTab = () => {
    const { t } = useAppTranslation();
    const styles = useStyles();
    const [showLoader, setLoading] = useState(true);
    const [frequentNotes, setFrequentNotes] = useState<FrequentNote[]>([]);
    const [allFrequentNotes, setAllFrequentNotes] = useState<FrequentNote[]>([]);
    const [settings, setSettings] = useState<GeneralSettings>(getDefaultGeneralSettings);
    const [teamMemberSignatureSettings, setTeamMemberSignatureSettings] =
        useState<TeamMemberSignatureSettingsDto>(getDefaultTeamMemberSignatureSettings);
    const [autoRoGeneration, setAutoRoGeneration] = useState<AutoROSetting>({
        settingIsDisabled: false,
        automaticallyCreateRoNumbers: false,
        startingRoNumber: undefined,
    });
    const dispatch = useDispatch();
    const { repairShopSettings } = useAppSelector(selectSettings);
    const isSmallScreen = useMediaQuery('(max-width:1200px)');
    const { mobileSettings, mobileSettingsLoading, updateSignature, updateMobileSettings } =
        useMobileSettings();

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.cm.general')}`);

    const showSaveSucceedToaster = (text?: string, title?: string) => {
        const notification = new NotificationData(
            text ?? t('toasters.settingSuccessfullyUpdated'),
            title ?? t('toasters.settingUpdated'),
            NotificationType.success
        );
        notification.expiresAt = Date.now() + 3000;
        dispatch(setNewToaster(notification));
    };

    const showSaveFailedToaster = (title?: string, text?: string) => {
        const notification = new NotificationData(
            text ?? t('toasters.errorOccurredWhenSaving'),
            title ?? t('toasters.errorOccurred'),
            NotificationType.danger
        );
        dispatch(setNewToaster(notification));
    };

    useEffect(() => {
        (async () => {
            try {
                const getFrequentNotes = FrequentNotesAPI.get();
                const getGeneralSettings = GeneralSettingsAPI.get();
                const getAutoRoSetting = AutoRoSettingAPI.get();
                const getTeamMemberSignatureSettings = TeamMemberSignatureSettingsApi.get();

                const allFrequentNotes = (await getFrequentNotes).frequentNotes;
                setAllFrequentNotes(allFrequentNotes);
                setFrequentNotes(allFrequentNotes.filter((fn) => !fn.isDeleted));

                const generalSettings = await getGeneralSettings;
                setSettings(generalSettings);

                const autoRoSetting = await getAutoRoSetting;
                setAutoRoGeneration(autoRoSetting);

                const teamMemberSignatureSettings = await getTeamMemberSignatureSettings;
                setTeamMemberSignatureSettings(teamMemberSignatureSettings);

                setLoading(false);
            } catch (_) {
                const notification = new NotificationData(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred'),
                    NotificationType.danger
                );
                dispatch(setNewToaster(notification));
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const saveGeneralSettings = async (
        changes: GeneralSettings,
        notificationText?: string,
        notificationTitle?: string
    ) => {
        setSettings(changes);

        try {
            //Maybe add throttling here?
            await GeneralSettingsAPI.post(changes);
            showSaveSucceedToaster(notificationText, notificationTitle);
        } catch (_) {
            showSaveFailedToaster();
        }
    };

    const saveAutoRoGeneration = async (changes: AutoROSetting) => {
        setAutoRoGeneration(changes);

        dispatch(
            globalSettingsActions.updateRsFeatureFlags({
                autoOrderNumber: changes.automaticallyCreateRoNumbers,
            })
        );

        try {
            //Maybe add throttling here?
            const response = await AutoRoSettingAPI.post(changes);
            setAutoRoGeneration(response);
            showSaveSucceedToaster();
        } catch (_) {
            showSaveFailedToaster();
        }
    };

    const handleFrequentNoteAdd = async (option: OptionData, noteType: FrequentNoteType) => {
        try {
            const response = await FrequentNotesAPI.post({ text: option.label, type: noteType });
            const updatedList = [...frequentNotes, response.frequentNote];
            setFrequentNotes(updatedList);
            showSaveSucceedToaster();

            //try add option to full list
            if (!allFrequentNotes.some((fn) => fn.text === response.frequentNote.text)) {
                const updatedFillList = [...allFrequentNotes, response.frequentNote];
                setAllFrequentNotes(updatedFillList);
            }
        } catch (_) {
            showSaveFailedToaster();
        }
    };

    const handleFrequentNoteRemove = async (option: OptionData) => {
        try {
            await FrequentNotesAPI.delete({ frequentNoteId: option.value });
            const updatedList = frequentNotes.filter((fn) => fn.frequentNoteId !== option.value);
            setFrequentNotes(updatedList);
            showSaveSucceedToaster();
        } catch (er) {
            console.error(er);
        }
    };

    return (
        <PageContent paddedX>
            {showLoader ? (
                <AreaSpinner />
            ) : (
                <>
                    <SettingsSection label={t('settings.cm.mobileApp.header')}>
                        <DivFrequentNotesWrapper>
                            <FrequentNotesSelector
                                allFrequentNotes={allFrequentNotes}
                                frequentNotes={frequentNotes}
                                notesType="ForCustomer"
                                onFrequentNoteAdd={handleFrequentNoteAdd}
                                onFrequentNoteRemove={handleFrequentNoteRemove}
                            />
                            <FrequentNotesSelector
                                allFrequentNotes={allFrequentNotes}
                                frequentNotes={frequentNotes}
                                notesType="ForInternal"
                                onFrequentNoteAdd={handleFrequentNoteAdd}
                                onFrequentNoteRemove={handleFrequentNoteRemove}
                            />
                        </DivFrequentNotesWrapper>

                        <RadioButtonArrayControl<boolean>
                            values={[
                                {
                                    id: 'f',
                                    value: false,
                                    label: t(
                                        'settings.cm.mobileApp.sharedOrdersRestrictionOption1'
                                    ),
                                    description: t(
                                        'settings.cm.mobileApp.sharedOrdersRestrictionDescription1'
                                    ),
                                },
                                {
                                    id: 't',
                                    value: true,
                                    label: t(
                                        'settings.cm.mobileApp.sharedOrdersRestrictionOption2'
                                    ),
                                    description: t(
                                        'settings.cm.mobileApp.sharedOrdersRestrictionDescription2'
                                    ),
                                },
                            ]}
                            label={t('settings.cm.mobileApp.sharedOrdersRestrictionLabel')}
                            value={settings.sharedInspectionsAddOnlyMode}
                            onChange={(v) =>
                                saveGeneralSettings({
                                    ...settings,
                                    sharedInspectionsAddOnlyMode: v,
                                })
                            }
                        />

                        <BooleanSettingControl
                            value={settings.showConsumerInformationToTechnicians}
                            onChange={(checked) =>
                                saveGeneralSettings({
                                    ...settings,
                                    showConsumerInformationToTechnicians: checked,
                                })
                            }
                            label={t('settings.cm.mobileApp.showCustomerInfoLabel')}
                        />

                        <RadioButtonArrayControl<boolean>
                            disabled={autoRoGeneration.settingIsDisabled}
                            value={autoRoGeneration.automaticallyCreateRoNumbers}
                            onChange={(checked) =>
                                saveAutoRoGeneration({
                                    ...autoRoGeneration,
                                    automaticallyCreateRoNumbers: checked,
                                })
                            }
                            values={[
                                {
                                    label: t(
                                        'settings.cm.mobileApp.generateOrderNumberAutomaticallyOption1'
                                    ),
                                    description: t(
                                        'settings.cm.mobileApp.generateOrderNumberAutomaticallyDescription1'
                                    ),
                                    value: false,
                                    id: 'f',
                                },
                                {
                                    label: t(
                                        'settings.cm.mobileApp.generateOrderNumberAutomaticallyOption2'
                                    ),
                                    description: t(
                                        'settings.cm.mobileApp.generateOrderNumberAutomaticallyDescription2'
                                    ),
                                    value: true,
                                    id: 't',
                                },
                            ]}
                            label={t('settings.cm.mobileApp.generateOrderNumberAutomaticallyLabel')}
                        />

                        <SettingLayoutControl
                            label={t('settings.cm.mobileApp.startingOrderNumberLabel')}
                        >
                            <TextFormField
                                classes={{ root: styles.startingOrderTextField }}
                                type="number"
                                maxLength={5}
                                name={'startingRoNumber'}
                                value={autoRoGeneration.startingRoNumber + ''}
                                disabled={
                                    autoRoGeneration.settingIsDisabled ||
                                    !autoRoGeneration.automaticallyCreateRoNumbers
                                }
                                placeholder="0"
                                onChange={(e) => {
                                    setAutoRoGeneration({
                                        ...autoRoGeneration,
                                        startingRoNumber: +e.target.value,
                                    });
                                }}
                                onBlur={() => {
                                    saveAutoRoGeneration(autoRoGeneration);
                                }}
                                onEnterPress={(e: any) => e.target.blur()}
                                enableEnterComplete={true}
                            />
                        </SettingLayoutControl>

                        <SettingLayoutControl
                            label={t('settings.cm.mobileApp.addCharactersBeforeEachOrderNumber')}
                        >
                            <TextFormField
                                classes={{ root: styles.startingOrderTextField }}
                                type="text"
                                maxLength={6}
                                name={'order-prefix'}
                                value={autoRoGeneration.roPrefix}
                                disabled={
                                    autoRoGeneration.settingIsDisabled ||
                                    !autoRoGeneration.automaticallyCreateRoNumbers
                                }
                                onChange={(e) => {
                                    setAutoRoGeneration({
                                        ...autoRoGeneration,
                                        roPrefix: e.target.value,
                                    });
                                }}
                                onBlur={() => {
                                    saveAutoRoGeneration(autoRoGeneration);
                                }}
                                onEnterPress={(e: any) => e.target.blur()}
                                enableEnterComplete={true}
                            />
                        </SettingLayoutControl>

                        <BooleanSettingControl
                            label={t('settings.cm.mobileApp.hideAddInspectionItemFeatureLabel')}
                            value={settings.hideAddInspectionItemFeature}
                            onChange={(checked) => {
                                saveGeneralSettings({
                                    ...settings,
                                    hideAddInspectionItemFeature: checked,
                                });
                            }}
                        />

                        <CheckboxArrayControl
                            slotProps={{
                                layout: {
                                    hasHint: true,
                                    hintText: t(
                                        'settings.cm.mobileApp.mandatoryFieldsReceptionHint'
                                    ),
                                },
                            }}
                            label={t('settings.cm.mobileApp.mandatoryFieldsReception')}
                            values={[
                                {
                                    value: settings.isReceptonSignatureRequired,
                                    label: t('settings.cm.mobileApp.receptionSignature'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isReceptonSignatureRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isDeliverySignatureRequired,
                                    label: t('settings.cm.mobileApp.deliverySignature'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isDeliverySignatureRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isTowerRequired,
                                    label: t('settings.cm.mobileApp.tower'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isTowerRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isOrderTypeRequired,
                                    label: t('settings.cm.mobileApp.orderType'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isOrderTypeRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isDeliveryDayRequired,
                                    label: t('settings.cm.mobileApp.deliveryDay'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isDeliveryDayRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isDeliveryHourRequired,
                                    label: t('settings.cm.mobileApp.deliveryHour'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isDeliveryHourRequired: checked,
                                        });
                                    },
                                },
                            ]}
                            width={106}
                            marginRight={28}
                        >
                            <SignaturesGrid>
                                <MobilePhaseSettingsEntry
                                    width={isSmallScreen ? undefined : 330}
                                    signature={mobileSettings?.signatures.signature1}
                                    label={t('settings.cm.mobileApp.tmSignatures.1')}
                                    disabled={!mobileSettings || mobileSettingsLoading}
                                    isSignatureDisabled={
                                        !teamMemberSignatureSettings.enableTeamMemberDigitalSignature1
                                    }
                                    onChange={(sig) => updateSignature(1, sig)}
                                />
                                <MobilePhaseSettingsEntry
                                    width={isSmallScreen ? undefined : 330}
                                    signature={mobileSettings?.signatures.signature2}
                                    label={t('settings.cm.mobileApp.tmSignatures.2')}
                                    disabled={!mobileSettings || mobileSettingsLoading}
                                    isSignatureDisabled={
                                        !teamMemberSignatureSettings.enableTeamMemberDigitalSignature2
                                    }
                                    onChange={(sig) => updateSignature(2, sig)}
                                />
                                <MobilePhaseSettingsEntry
                                    width={isSmallScreen ? undefined : 330}
                                    signature={mobileSettings?.signatures.signature3}
                                    label={t('settings.cm.mobileApp.tmSignatures.3')}
                                    disabled={!mobileSettings || mobileSettingsLoading}
                                    isSignatureDisabled={
                                        !teamMemberSignatureSettings.enableTeamMemberDigitalSignature3
                                    }
                                    onChange={(sig) => updateSignature(3, sig)}
                                />
                                <MobilePhaseSettingsEntry
                                    width={isSmallScreen ? undefined : 330}
                                    signature={mobileSettings?.signatures.signature4}
                                    label={t('settings.cm.mobileApp.tmSignatures.4')}
                                    disabled={!mobileSettings || mobileSettingsLoading}
                                    isSignatureDisabled={
                                        !teamMemberSignatureSettings.enableTeamMemberDigitalSignature4
                                    }
                                    onChange={(sig) => updateSignature(4, sig)}
                                />
                            </SignaturesGrid>
                        </CheckboxArrayControl>

                        <CheckboxArrayControl
                            label={t('settings.cm.mobileApp.mandatoryFieldsCustomer')}
                            width={106}
                            values={[
                                {
                                    value: settings.isNameRequired,
                                    label: t('settings.cm.mobileApp.name'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isNameRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isLastNameRequired,
                                    label: t('settings.cm.mobileApp.lastName'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isLastNameRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isEmailRequired,
                                    label: t('settings.cm.mobileApp.email'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isEmailRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isMobileRequired,
                                    label: t('settings.cm.mobileApp.mobile'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isMobileRequired: checked,
                                        });
                                    },
                                },
                                // TODO: uncomment before 3.20 release
                                // {
                                //     value: settings.isSignatureInPrivacyNoticeRequired,
                                //     label: t('settings.cm.mobileApp.signatureInPrivacyNotice'),
                                //     onChange: (checked) => {
                                //         saveGeneralSettings({
                                //             ...settings,
                                //             isSignatureInPrivacyNoticeRequired: checked,
                                //         });
                                //     },
                                // },
                            ]}
                        />

                        <CheckboxArrayControl
                            label={t('settings.cm.mobileApp.mandatoryFieldsVehicle')}
                            width={106}
                            values={[
                                {
                                    value: settings.isPlatesRequired,
                                    label: t('settings.cm.mobileApp.plates'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isPlatesRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isBrandRequired,
                                    label: t('settings.cm.mobileApp.brand'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isBrandRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isModelRequired,
                                    label: t('settings.cm.mobileApp.model'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isModelRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isYearRequired,
                                    label: t('settings.cm.mobileApp.year'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isYearRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isMileageRequired,
                                    label: t('settings.cm.mobileApp.mileage'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isMileageRequired: checked,
                                        });
                                    },
                                },
                                {
                                    value: settings.isVINRequired,
                                    label: t('settings.cm.mobileApp.VIN'),
                                    onChange: (checked) => {
                                        saveGeneralSettings({
                                            ...settings,
                                            isVINRequired: checked,
                                        });
                                    },
                                },
                            ]}
                        />
                        <BooleanSettingControl
                            label={t(
                                'settings.cm.mobileApp.allowUploadingMediaFilesFromTheMobileGallery'
                            )}
                            value={mobileSettings?.allowUploadingMediaFromGallery || false}
                            onChange={(checked) => {
                                updateMobileSettings({
                                    ...mobileSettings!,
                                    allowUploadingMediaFromGallery: checked,
                                });
                            }}
                            disabled={!mobileSettings || mobileSettingsLoading}
                            hasHint={true}
                            hintText={t(
                                'settings.cm.mobileApp.allowUploadingMediaFilesFromTheMobileGalleryTooltip'
                            )}
                        />
                    </SettingsSection>

                    <SettingsSection label={t('settings.cm.inCharge.header')}>
                        <BooleanSettingControl
                            label={t('settings.cm.inCharge.automaticallyInCharge')}
                            value={settings.automaticallyInCharge}
                            onChange={(checked) => {
                                saveGeneralSettings({
                                    ...settings,
                                    automaticallyInCharge: checked,
                                });
                            }}
                        />
                    </SettingsSection>

                    <SettingsSection label={t('settings.cm.assignedTo.header')}>
                        <BooleanSettingControl
                            value={settings.automaticallyAssignedTo}
                            label={t('settings.cm.assignedTo.automaticallyAssignedTo')}
                            onChange={(checked) => {
                                saveGeneralSettings({
                                    ...settings,
                                    automaticallyAssignedTo: checked,
                                });
                            }}
                        />
                    </SettingsSection>

                    {/* ESTIMATES */}
                    <SettingsSection label={t('settings.cm.estimates.header')}>
                        <BooleanSettingControl
                            label={t('settings.cm.estimates.enableSpreadsheetViewByDefault')}
                            onChange={(checked) =>
                                saveGeneralSettings({
                                    ...settings,
                                    enableSpreadsheetViewByDefault: checked,
                                })
                            }
                            value={settings.enableSpreadsheetViewByDefault}
                        />

                        <BooleanSettingControl
                            label={t('settings.cm.estimates.enableEstimateReview')}
                            onChange={(checked) =>
                                saveGeneralSettings({
                                    ...settings,
                                    enableEstimateReview: checked,
                                })
                            }
                            value={settings.enableEstimateReview}
                        />
                    </SettingsSection>

                    {repairShopSettings?.features.dmsIntegration && (
                        <SettingsSection label={t('settings.cm.dmsIntegration.header')}>
                            <BooleanSettingControl
                                value={settings.showAllRosFromDms}
                                label={t('settings.cm.dmsIntegration.showAllRosFromDms')}
                                onChange={(checked) => {
                                    saveGeneralSettings({
                                        ...settings,
                                        showAllRosFromDms: checked,
                                    });
                                }}
                            />
                        </SettingsSection>
                    )}
                </>
            )}
        </PageContent>
    );
};

const mobileSettingsQueryKey: QueryKey = ['settings', 'cm', 'general', 'mobile'];

function useMobileSettings() {
    const { data, isLoading } = useQuery(mobileSettingsQueryKey, () =>
        GeneralSettingsAPI.getMobile()
    );

    const queryClient = useQueryClient();
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const { mutateAsync } = useMutation(
        (settings: MobileSettingsDto) => GeneralSettingsAPI.updateMobile(settings),
        {
            onMutate: (settings) => {
                queryClient.setQueryData(mobileSettingsQueryKey, settings);
            },
            networkMode: 'always',
            onError(error, _variables, _context) {
                if (isNetworkError(error)) return;
                toasters.danger(
                    t('toasters.errorOccurredWhenSaving') + `: ${error}`,
                    t('toasters.errorOccurred')
                );
            },
        }
    );

    return {
        mobileSettings: data,
        mobileSettingsLoading: isLoading,
        updateSignature: useCallback(
            (index: 1 | 2 | 3 | 4, newValue: MobileSignatureDto) => {
                const data = queryClient.getQueryData(mobileSettingsQueryKey);
                if (!data) return;
                const typedData = { ...(data as MobileSettingsDto) };
                typedData.signatures[`signature${index}`] = newValue;
                return mutateAsync(typedData);
            },
            [mutateAsync, queryClient]
        ),
        updateMobileSettings: (newValue: MobileSettingsDto) => {
            mutateAsync(newValue);
        },
    };
}

const DivFrequentNotesWrapper = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
});

const SignaturesGrid = styled('div')(({ theme }) => ({
    display: 'grid',
    rowGap: 10,
    columnGap: 24,
    gridTemplateColumns: '1fr 1fr',
    width: 'fit-content',
    maxWidth: '100%',
    marginTop: 16,
    marginRight: 28,

    [theme.breakpoints.down('md')]: {
        gridTemplateColumns: '1fr',
    },
}));

export default GeneralTab;
