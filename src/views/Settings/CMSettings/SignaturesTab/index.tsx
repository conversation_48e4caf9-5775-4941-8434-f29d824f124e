import { styled } from '@mui/material';
import { LegalRepresentativeSignatureAPI } from 'api/settings';
import CustomerSignatureSettingsApi, {
    CustomerSignatureSettingsDto,
    GetDefaultCustomerSignatureSettings,
} from 'api/settings/CustomerSignature';
import TeamMemberSignatureSettingsApi, {
    TeamMemberSignatureSettingsDto,
    getDefaultTeamMemberSignatureSettings,
} from 'api/settings/TeamMemberSignature';
import UploadAPI from 'api/settings/Upload';
import AreaSpinner from 'common/components/AreaSpinner';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import {
    LegalRepresentativeSignature,
    getDefaultLegalRepresentativeSignature,
} from 'datacontracts/Settings/CM';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Http } from 'services/Http';
import { setNewToaster } from 'store/actions/toasters.action';
import PageContent from 'views/Components/Page';
import {
    BooleanSettingControl,
    ImageUploadControl,
    MultilineTextSettingControl,
    SettingsSection,
    TextSettingControl,
} from 'views/Settings/common';
import { MultiTextSettingControl } from 'views/Settings/common/MultiTextSettingControl';

const SignaturesTab = () => {
    const { t } = useAppTranslation();
    const [showLoader, setLoading] = useState(true);
    const [customerSignatureSettings, setCustomerSignatureSettings] =
        useState<CustomerSignatureSettingsDto>(GetDefaultCustomerSignatureSettings);
    const [teamMemberSignatureSettings, setTeamMemberSignatureSettings] =
        useState<TeamMemberSignatureSettingsDto>(getDefaultTeamMemberSignatureSettings);
    const [legalReprSign, setLegalReprSign] = useState(getDefaultLegalRepresentativeSignature);
    const dispatch = useDispatch();

    const [receptionSignatureCheckboxes, setReceptionSignatureCheckboxes] = useState<string[]>([]);

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.cm.general')}`);

    const showSaveSucceedToaster = (text?: string, title?: string) => {
        const notification = new NotificationData(
            text ?? t('toasters.settingSuccessfullyUpdated'),
            title ?? t('toasters.settingUpdated'),
            NotificationType.success
        );
        notification.expiresAt = Date.now() + 3000;
        dispatch(setNewToaster(notification));
    };

    const showSaveFailedToaster = (title?: string, text?: string) => {
        const notification = new NotificationData(
            text ?? t('toasters.errorOccurredWhenSaving'),
            title ?? t('toasters.errorOccurred'),
            NotificationType.danger
        );
        dispatch(setNewToaster(notification));
    };

    useEffect(() => {
        (async () => {
            try {
                const getCustomerSignaturesSettings = CustomerSignatureSettingsApi.get();
                const getLegalRepresentativeSignature = LegalRepresentativeSignatureAPI.get();
                const getTeamMemberSignatureSettings = TeamMemberSignatureSettingsApi.get();

                const customerSignatureSettings = await getCustomerSignaturesSettings;
                setCustomerSignatureSettings(customerSignatureSettings);
                setReceptionSignatureCheckboxes(
                    JSON.parse(customerSignatureSettings.receptionSignatureCheckbox || '[]')
                );

                const legalRepr = await getLegalRepresentativeSignature;
                setLegalReprSign(legalRepr);

                const teamMemberSignatureSettings = await getTeamMemberSignatureSettings;
                setTeamMemberSignatureSettings(teamMemberSignatureSettings);

                setLoading(false);
            } catch (er) {
                const notification = new NotificationData(
                    t('toasters.errorOccurredWhenLoading'),
                    t('toasters.errorOccurred'),
                    NotificationType.danger
                );
                dispatch(setNewToaster(notification));
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const saveCustomerSignature = async (
        changes: CustomerSignatureSettingsDto,
        notificationText?: string,
        notificationTitle?: string
    ) => {
        setCustomerSignatureSettings(changes);

        try {
            //Maybe add throttling here?
            await CustomerSignatureSettingsApi.update(changes);
            showSaveSucceedToaster(notificationText, notificationTitle);
        } catch (er) {
            showSaveFailedToaster();
        }
    };

    const saveTeamMemberSignatureSettings = async (changes: TeamMemberSignatureSettingsDto) => {
        setTeamMemberSignatureSettings(changes);

        try {
            await TeamMemberSignatureSettingsApi.update(changes);
            showSaveSucceedToaster();
        } catch (err) {
            showSaveFailedToaster();
        }
    };

    const saveLegalReprSign = async (changes: Partial<LegalRepresentativeSignature>) => {
        const legalSign = {
            ...legalReprSign,
            ...changes,
        };

        setLegalReprSign(legalSign);

        try {
            await LegalRepresentativeSignatureAPI.post(legalSign);
            showSaveSucceedToaster();
        } catch (err) {
            showSaveFailedToaster();
        }
    };

    const uploadLRSFile = async (file: File) => {
        if (!UploadAPI.isValidImageFile(file)) {
            showSaveFailedToaster(
                t('toasters.settingUpdateFailure'),
                t('toasters.fileNotSupported')
            );
            return;
        }
        try {
            await UploadAPI.uploadLegalRepresentativeSignature(file);
            setLegalReprSign(await LegalRepresentativeSignatureAPI.get());
            showSaveSucceedToaster();
        } catch (e) {
            showSaveFailedToaster();
        }
    };

    const deleteLRSFile = async () => {
        try {
            await UploadAPI.deleteLegalRepresentativeSignature();
            showSaveSucceedToaster();
            setLegalReprSign({
                ...legalReprSign,
                legalRepresentativeSignaturePath: null,
            });
        } catch (e) {
            showSaveFailedToaster();
        }
    };

    return (
        <PageContent paddedX>
            {showLoader ? (
                <AreaSpinner />
            ) : (
                <>
                    {/* NOTE: this entire section has custom notification when saving */}
                    <SettingsSection label={t('settings.cm.customerSignature.header')}>
                        {/* ELECTRONIC SIGNATURE */}

                        <BooleanSettingControl
                            label={t('settings.cm.customerSignature.enableElectronicSignature')}
                            onChange={(checked) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        enableElectronicSignature: checked,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.enableElectronicSignature}
                        />

                        <TextSettingControl
                            name="electronicSignatureHeader"
                            value={customerSignatureSettings.electronicSignatureHeader || ''}
                            onSave={(v) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        electronicSignatureHeader: v,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            placeholder={t(
                                'settings.cm.customerSignature.electronicSignatureHeaderPlaceholder'
                            )}
                            label={t('settings.cm.customerSignature.electronicSignatureHeader')}
                        />

                        <MultilineTextSettingControl
                            label={t('settings.cm.customerSignature.electronicSignatureText')}
                            placeholder={t(
                                'settings.cm.customerSignature.electronicSignatureTextPlaceholder'
                            )}
                            name="electronicSignatureTextArea"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    electronicSignatureText: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.electronicSignatureText || ''}
                            isRequired={true}
                        />

                        <MultiTextSettingControl
                            values={receptionSignatureCheckboxes}
                            onChange={(newList) => {
                                setReceptionSignatureCheckboxes(newList);

                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        receptionSignatureCheckbox: JSON.stringify(newList),
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                );
                            }}
                            label={t('settings.cm.customerSignature.electronicSignatureCheckbox')}
                        />

                        {/* ADHESION CONTRACT */}

                        <BooleanSettingControl
                            label={t(
                                'settings.cm.customerSignature.enableAdhesionContractSignature'
                            )}
                            onChange={(checked) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        enableAdhesionContractSignature: checked,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.enableAdhesionContractSignature}
                        />

                        <TextSettingControl
                            name="adhesionContractSignatureHeaderField"
                            value={customerSignatureSettings.adhesionContractSignatureHeader || ''}
                            onSave={(v) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        adhesionContractSignatureHeader: v,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            label={t(
                                'settings.cm.customerSignature.adhesionContractSignatureHeader'
                            )}
                            placeholder={t(
                                'settings.cm.customerSignature.adhesionContractSignatureHeaderPlaceholder'
                            )}
                        />

                        <MultilineTextSettingControl
                            label={t('settings.cm.customerSignature.adhesionContractSignatureText')}
                            placeholder={t(
                                'settings.cm.customerSignature.adhesionContractSignatureTextPlaceholder'
                            )}
                            name="adhesionContractSignatureTextField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    adhesionContractSignatureText: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.adhesionContractSignatureText || ''}
                            isRequired={true}
                        />

                        <MultilineTextSettingControl
                            label={t(
                                'settings.cm.customerSignature.adhesionContractSignatureCheckbox'
                            )}
                            placeholder={t(
                                'settings.cm.customerSignature.adhesionContractSignatureCheckboxPlaceholder'
                            )}
                            name="adhesionContractSignatureCheckboxTextField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    adhesionContractSignatureCheckbox: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={
                                customerSignatureSettings.adhesionContractSignatureCheckbox || ''
                            }
                        />

                        {/* NOTICE OF PRIVACY */}

                        <BooleanSettingControl
                            label={t('settings.cm.customerSignature.enableNoticePrivacySignature')}
                            onChange={(checked) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        enableNoticePrivacySignature: checked,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.enableNoticePrivacySignature}
                        />

                        <TextSettingControl
                            name="noticePrivacySignatureHeaderField"
                            value={customerSignatureSettings.noticePrivacySignatureHeader || ''}
                            onSave={(v) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        noticePrivacySignatureHeader: v,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            label={t('settings.cm.customerSignature.noticePrivacySignatureHeader')}
                            placeholder={t(
                                'settings.cm.customerSignature.noticePrivacySignatureHeaderPlaceholder'
                            )}
                        />

                        <MultilineTextSettingControl
                            label={t('settings.cm.customerSignature.noticePrivacySignatureText')}
                            placeholder={t(
                                'settings.cm.customerSignature.noticePrivacySignatureTextPlaceholder'
                            )}
                            name="noticePrivacySignatureTextField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    noticePrivacySignatureText: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.noticePrivacySignatureText || ''}
                            isRequired={true}
                        />

                        <MultilineTextSettingControl
                            label={t(
                                'settings.cm.customerSignature.privacyNoticeSignatureCheckbox'
                            )}
                            placeholder={t(
                                'settings.cm.customerSignature.privacyNoticeSignatureCheckboxPlaceholder'
                            )}
                            name="privacyNoticeSignatureCheckboxTextField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    privacyNoticeSignatureCheckbox: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.privacyNoticeSignatureCheckbox || ''}
                        />

                        {/* DIGITAL DELIVERY */}

                        <BooleanSettingControl
                            label={t(
                                'settings.cm.customerSignature.enableElectronicDeliverySignature'
                            )}
                            onChange={(checked) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        enableElectronicDeliverySignature: checked,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.enableElectronicDeliverySignature}
                        />

                        <TextSettingControl
                            name="electronicDeliverySignatureHeaderField"
                            value={
                                customerSignatureSettings.electronicDeliverySignatureHeader || ''
                            }
                            onSave={(v) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        electronicDeliverySignatureHeader: v,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            label={t(
                                'settings.cm.customerSignature.electronicDeliverySignatureHeader'
                            )}
                            placeholder={t(
                                'settings.cm.customerSignature.electronicDeliverySignatureHeaderPlaceholder'
                            )}
                        />

                        <MultilineTextSettingControl
                            label={t(
                                'settings.cm.customerSignature.electronicDeliverySignatureText'
                            )}
                            placeholder={t(
                                'settings.cm.customerSignature.electronicDeliverySignatureTextPlaceholder'
                            )}
                            name="electronicDeliverySignatureTextField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    electronicDeliverySignatureText: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.electronicDeliverySignatureText || ''}
                            isRequired={true}
                        />

                        <MultilineTextSettingControl
                            placeholder={t(
                                'settings.cm.customerSignature.electronicDeliverySignatureCheckboxPlaceholder'
                            )}
                            label={t(
                                'settings.cm.customerSignature.electronicDeliverySignatureCheckbox'
                            )}
                            name="electronicDeliverySignatureCheckboxField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    electronicDeliverySignatureCheckbox: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={
                                customerSignatureSettings.electronicDeliverySignatureCheckbox || ''
                            }
                        />

                        {/* #region HANDWRITTEN SIGNATURE */}

                        <BooleanSettingControl
                            label={t('settings.cm.customerSignature.enableHandwrittenSignature')}
                            onChange={(checked) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        enableHandwrittenSignature: checked,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.enableHandwrittenSignature}
                        />

                        <TextSettingControl
                            name="handwrittenSignatureHeaderField"
                            value={customerSignatureSettings.handwrittenSignatureHeader || ''}
                            onSave={(v) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        handwrittenSignatureHeader: v,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            label={t('settings.cm.customerSignature.handwrittenSignatureHeader')}
                            placeholder={t(
                                'settings.cm.customerSignature.handwrittenSignatureHeaderPlaceholder'
                            )}
                        />

                        <MultilineTextSettingControl
                            label={t('settings.cm.customerSignature.handwrittenSignatureText')}
                            placeholder={t(
                                'settings.cm.customerSignature.handwrittenSignatureTextPlaceholder'
                            )}
                            name="handwrittenSignatureTextField"
                            onChange={(v) => {
                                setCustomerSignatureSettings({
                                    ...customerSignatureSettings,
                                    handwrittenSignatureText: v.target.value,
                                });
                            }}
                            onSave={() =>
                                saveCustomerSignature(
                                    customerSignatureSettings,
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={customerSignatureSettings.handwrittenSignatureText || ''}
                        />

                        <BooleanSettingControl
                            label={t(
                                'settings.cm.customerSignature.enableHandwrittenSignatureForEstimateApproval'
                            )}
                            onChange={(checked) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        activateHandwrittenSignatureForEstimateApproval: checked,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            value={
                                customerSignatureSettings.activateHandwrittenSignatureForEstimateApproval
                            }
                        />

                        <TextSettingControl
                            name="handwrittenSignatureHeaderField"
                            value={
                                customerSignatureSettings.handwrittenSignatureForEstimateApprovalHeader ||
                                ''
                            }
                            onSave={(v) =>
                                saveCustomerSignature(
                                    {
                                        ...customerSignatureSettings,
                                        handwrittenSignatureForEstimateApprovalHeader: v,
                                    },
                                    t('toasters.configurationChangedSuccessfully'),
                                    t('toasters.updatedConfiguration')
                                )
                            }
                            label={t(
                                'settings.cm.customerSignature.handwrittenSignatureForEstimateApprovalHeader'
                            )}
                            placeholder={t(
                                'settings.cm.customerSignature.handwrittenSignatureForEstimateApprovalHeaderPlaceholder'
                            )}
                        />
                    </SettingsSection>

                    <SettingsSection label={t('settings.cm.legalRepresentativeSignature.header')}>
                        <ImageUploadControl
                            note={t('settings.cm.legalRepresentativeSignature.dimensionsNote')}
                            onFileDelete={deleteLRSFile}
                            src={legalReprSign.legalRepresentativeSignaturePath?.replace(
                                '~',
                                Http.HOST
                            )}
                            onFileUpload={uploadLRSFile}
                        />

                        <BooleanSettingControl
                            label={t(
                                'settings.cm.legalRepresentativeSignature.enableSignatureInPDF'
                            )}
                            value={legalReprSign.enableLegalRepresentativeSignature}
                            onChange={(checked) =>
                                saveLegalReprSign({
                                    enableLegalRepresentativeSignature: checked,
                                })
                            }
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.legalRepresentativeSignature.signaturePlaceholder'
                            )}
                            name="legalRepresentativeSignatureLabelField"
                            label={t('settings.cm.legalRepresentativeSignature.signatureLabel')}
                            value={legalReprSign.legalRepresentativeSignatureLabel}
                            onSave={(value) =>
                                saveLegalReprSign({ legalRepresentativeSignatureLabel: value })
                            }
                        />
                    </SettingsSection>

                    <SettingsSection
                        label={t('settings.cm.teamMemberSignature.mobilAppSignaturesHeader')}
                    >
                        <BooleanSettingControl
                            value={teamMemberSignatureSettings?.enableTeamMemberDigitalSignature1}
                            onChange={(checked) => {
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    enableTeamMemberDigitalSignature1: checked,
                                });
                            }}
                            label={t(
                                'settings.cm.teamMemberSignature.activateDigitalSignatureInMobileApplication1'
                            )}
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder1'
                            )}
                            name="teamMemberDigitalSignatureHeaderField"
                            label={t('settings.cm.teamMemberSignature.signatureHeaderText1')}
                            value={teamMemberSignatureSettings.teamMemberDigitalSignatureHeader1}
                            onSave={(value) =>
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberDigitalSignatureHeader1:
                                        value == ''
                                            ? t(
                                                  'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder1'
                                              )
                                            : value,
                                })
                            }
                        />

                        <BooleanSettingControl
                            value={teamMemberSignatureSettings?.enableTeamMemberDigitalSignature2}
                            onChange={(checked) => {
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    enableTeamMemberDigitalSignature2: checked,
                                });
                            }}
                            label={t(
                                'settings.cm.teamMemberSignature.activateDigitalSignatureInMobileApplication2'
                            )}
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder2'
                            )}
                            name="teamMemberDigitalSignatureHeaderField"
                            label={t('settings.cm.teamMemberSignature.signatureHeaderText2')}
                            value={teamMemberSignatureSettings.teamMemberDigitalSignatureHeader2}
                            onSave={(value) =>
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberDigitalSignatureHeader2:
                                        value == ''
                                            ? t(
                                                  'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder2'
                                              )
                                            : value,
                                })
                            }
                        />

                        <BooleanSettingControl
                            value={teamMemberSignatureSettings?.enableTeamMemberDigitalSignature3}
                            onChange={(checked) => {
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    enableTeamMemberDigitalSignature3: checked,
                                });
                            }}
                            label={t(
                                'settings.cm.teamMemberSignature.activateDigitalSignatureInMobileApplication3'
                            )}
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder3'
                            )}
                            name="teamMemberDigitalSignatureHeaderField"
                            label={t('settings.cm.teamMemberSignature.signatureHeaderText3')}
                            value={teamMemberSignatureSettings.teamMemberDigitalSignatureHeader3}
                            onSave={(value) =>
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberDigitalSignatureHeader3:
                                        value == ''
                                            ? t(
                                                  'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder3'
                                              )
                                            : value,
                                })
                            }
                        />

                        <BooleanSettingControl
                            value={teamMemberSignatureSettings?.enableTeamMemberDigitalSignature4}
                            onChange={(checked) => {
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    enableTeamMemberDigitalSignature4: checked,
                                });
                            }}
                            label={t(
                                'settings.cm.teamMemberSignature.activateDigitalSignatureInMobileApplication4'
                            )}
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder4'
                            )}
                            name="teamMemberDigitalSignatureHeaderField"
                            label={t('settings.cm.teamMemberSignature.signatureHeaderText4')}
                            value={teamMemberSignatureSettings.teamMemberDigitalSignatureHeader4}
                            onSave={(value) =>
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberDigitalSignatureHeader4:
                                        value == ''
                                            ? t(
                                                  'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder4'
                                              )
                                            : value,
                                })
                            }
                        />
                    </SettingsSection>
                    <SettingsSection
                        label={t('settings.cm.teamMemberSignature.handwrittenSignaturesHeader')}
                    >
                        <BooleanSettingControl
                            value={
                                teamMemberSignatureSettings?.teamMemberHandwrittenSignatureInPdfCustomerFormEnabled
                            }
                            onChange={(checked) => {
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberHandwrittenSignatureInPdfCustomerFormEnabled: checked,
                                });
                            }}
                            label={t('settings.cm.teamMemberSignature.customerFormPdfSignature')}
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder'
                            )}
                            name="teamMemberHandwrittenSignatureInPdfCustomerFormHeaderField"
                            label={t('settings.cm.teamMemberSignature.signatureHeaderText')}
                            value={
                                teamMemberSignatureSettings.teamMemberHandwrittenSignatureInPdfCustomerFormHeader
                            }
                            onSave={(value) =>
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberHandwrittenSignatureInPdfCustomerFormHeader: value,
                                })
                            }
                        />

                        <BooleanSettingControl
                            value={
                                teamMemberSignatureSettings?.teamMemberHandwrittenSignatureInPdfEstimateEnabled
                            }
                            onChange={(checked) => {
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberHandwrittenSignatureInPdfEstimateEnabled: checked,
                                });
                            }}
                            label={t('settings.cm.teamMemberSignature.estimatesPdfSignature')}
                        />

                        <TextSettingControl
                            placeholder={t(
                                'settings.cm.teamMemberSignature.signatureHeaderTextPlaceholder'
                            )}
                            name="teamMemberHandwrittenSignatureInPdfEstimateHeaderField"
                            label={t('settings.cm.teamMemberSignature.signatureHeaderText')}
                            value={
                                teamMemberSignatureSettings.teamMemberHandwrittenSignatureInPdfEstimateHeader
                            }
                            onSave={(value) =>
                                saveTeamMemberSignatureSettings({
                                    ...teamMemberSignatureSettings,
                                    teamMemberHandwrittenSignatureInPdfEstimateHeader: value,
                                })
                            }
                        />
                    </SettingsSection>
                </>
            )}
        </PageContent>
    );
};

const CheckboxTextList = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'row',
}));

export default SignaturesTab;
