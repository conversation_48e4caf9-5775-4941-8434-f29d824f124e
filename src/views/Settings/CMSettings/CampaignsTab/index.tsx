import { Box } from '@mui/material';
import { styled } from '@mui/styles';
import CampaignAPI, { Campaign } from 'api/settings/Campaigns';
import { TableProvider } from 'common/components/Table';
import { TablePaginationQuery } from 'common/components/Table/types';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { RepairShopCampaignConfiguration } from 'datacontracts/GlobalSettings/RepairShopSettingsDto';
import { createContext, useContext } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
    globalSettingsActions,
    selectIanaTz,
    selectRepairShopCampaignsConfiguration,
} from 'store/slices/globalSettingsSlice';
import { campaignsTabData } from './CampaignDataTable';
import CampaignTableContent from './CampaignTableContent';

const Root = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    flexGrow: 1,
});

type CampaignConfigurationContextType = {
    data: RepairShopCampaignConfiguration;
};

const CampaignConfigurationContext = createContext<CampaignConfigurationContextType | undefined>(
    undefined
);

export const CampaignTab = () => {
    const { t } = useAppTranslation();
    const ianaTz = useAppSelector(selectIanaTz);
    const { campaignConfiguration } = useRepairShopCampaignConfiguration();

    return (
        <Root>
            <CampaignConfigurationContext.Provider value={{ data: campaignConfiguration }}>
                <TableProvider<Campaign>
                    apiSearch={{
                        queryKey: ['settings/campaigns/page'],
                        queryFn: (query: TablePaginationQuery) =>
                            CampaignAPI.getCampaignsPage(query),
                    }}
                    columns={campaignsTabData({
                        ianaTz,
                        format: t('dateFormats.luxon.shortRightSlashDivider'),
                    })}
                    startActive={campaignConfiguration.enableVehicleCampaign}
                >
                    <CampaignTableContent />
                </TableProvider>
            </CampaignConfigurationContext.Provider>
        </Root>
    );
};

export const useCampaignConfiguration = () => {
    const context = useContext(CampaignConfigurationContext);
    if (!context) {
        throw new Error(
            'useCampaignConfiguration must be used within a CampaignConfigurationProvider'
        );
    }
    return context;
};

export const useRepairShopCampaignConfiguration = () => {
    const dispatch = useAppDispatch();
    const campaignConfiguration = useAppSelector(
        selectRepairShopCampaignsConfiguration
    ) as RepairShopCampaignConfiguration;

    const handleChangeCampaignConfiguration = (
        newConfig: Partial<RepairShopCampaignConfiguration>
    ) => {
        dispatch(
            globalSettingsActions.updateRsFeatureFlags({
                campaigns: { ...campaignConfiguration, ...newConfig },
            })
        );
    };

    return { campaignConfiguration, handleChangeCampaignConfiguration };
};
