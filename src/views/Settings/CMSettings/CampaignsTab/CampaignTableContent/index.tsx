import { Box, Table } from '@mui/material';
import { styled } from '@mui/styles';
import { useTable } from 'common/components/Table';
import PageContent from 'views/Components/Page';
import {
    CampaignTableBody,
    CampaignTableEnhanced,
    CampaignTableHead,
    CampaignTablePagination,
} from './common';

const STable = styled(Table)({
    flexGrow: 1,
    height: '100%',
    '& td': {
        maxWidth: 0,
    },
});

export const StyledCampaignPageContent = styled(PageContent)({
    height: 'initial',
    flexGrow: 1,
    minHeight: 755,
});

const PaginationContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
    margin: '30px 0 0 0',

    '& .MuiPagination-ul': {
        gap: 8,
    },
    '& .MuiPaginationItem-page:not(.Mui-selected)': {
        color: theme.palette.neutral[6],
    },
    '& .MuiPaginationItem-icon': {
        color: theme.palette.info.main,
    },
}));

const CampaignTableContent = <T,>() => {
    const {
        isLoading,
        page: { totalCount },
    } = useTable<T>();

    const showPagination = totalCount > 0 || isLoading;

    return (
        <>
            <StyledCampaignPageContent>
                <CampaignTableEnhanced />
                <STable>
                    <CampaignTableHead />
                    <CampaignTableBody />
                </STable>
            </StyledCampaignPageContent>

            {showPagination && (
                <PaginationContainer>
                    <CampaignTablePagination />
                </PaginationContainer>
            )}
        </>
    );
};

export default CampaignTableContent;
export { CampaignTableBody, CampaignTableEnhanced, CampaignTableHead, CampaignTablePagination };
