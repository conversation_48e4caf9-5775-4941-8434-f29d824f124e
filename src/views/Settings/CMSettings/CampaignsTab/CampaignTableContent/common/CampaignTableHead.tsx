import { useTheme } from '@mui/styles';
import { TableHead } from 'common/components/Table';
import { Colors } from 'common/styles/Colors';

export const CampaignTableHead = () => {
    const theme = useTheme();

    return (
        <TableHead
            tableCell={{
                sx: {
                    ...theme.typography.h6Inter,
                    color: theme.palette.neutral[7],
                    padding: '16px 10px',
                    backgroundColor: Colors.Neutral2,
                    textTransform: 'uppercase',
                    boxSizing: 'border-box',
                    height: '60px',
                    paddingLeft: '28px',

                    '& .MuiTableSortLabel-root.MuiTableSortLabel-active': {
                        ...theme.typography.h6Inter,
                        color: theme.palette.neutral[7],
                    },
                },
            }}
        />
    );
};
