import CampaignAPI, { Campaign } from 'api/settings/Campaigns';

import { DeleteIcon } from 'common/components/Icons';

import { Box, IconButton, Typography, styled } from '@mui/material';

import { useMutation } from '@tanstack/react-query';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useTable } from 'common/components/Table';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useOpen } from 'common/hooks/useOpen';
import useToasters from 'common/hooks/useToasters';

export const CampaignDeleteButton = ({ campaign }: { campaign: Campaign }) => {
    const { t } = useAppTranslation();
    const { disable, refetch, handleChangeQuery, page, query } = useTable();
    const [open, handleOpen, handleClose] = useOpen();

    const { deleteCampaign, isLoading } = useDeleteCampaign(() => {
        handleClose();

        if (page.rows.length === 1 && page.totalCount > query.pageSize) {
            handleChangeQuery({
                ...query,
                page: query.page - 1,
            });
        } else {
            refetch();
        }
    });

    const handleDeleteCampaign = () => {
        deleteCampaign(campaign.campaignId);
    };

    const handleCloseModal = () => {
        if (!isLoading) handleClose();
    };

    return (
        <Box sx={{ marginLeft: '-24px' }}>
            <IconButton size="medium" onClick={handleOpen} disabled={disable}>
                <DeleteIcon fill="currentColor" style={{ color: '#ACB7C0' }} />
            </IconButton>
            <DeleteConfirmationPopup
                open={open}
                title={t('settings.cm.campaigns.deleteButton.title')}
                body={
                    <TextContent>{t('settings.cm.campaigns.deleteButton.description')}</TextContent>
                }
                isConfirmDisabled={isLoading}
                showLoader={isLoading}
                cancel={t('settings.cm.campaigns.deleteButton.cancel')}
                confirm={t('settings.cm.campaigns.deleteButton.delete')}
                onConfirm={handleDeleteCampaign}
                onClose={handleCloseModal}
            />
        </Box>
    );
};

const TextContent = styled(Typography)({
    fontSize: '12px',
    fontWeight: 400,
    color: '#6A6E72',
});

const useDeleteCampaign = (onSuccess: () => void) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const { mutate: deleteCampaign, isLoading } = useMutation({
        mutationFn: (campaignId: string) => CampaignAPI.deleteCampaign(campaignId),
        onSuccess: () => {
            toasters.success('', t('settings.cm.campaigns.deleteButton.success'));

            onSuccess && onSuccess();
        },
    });

    return { deleteCampaign, isLoading };
};
