import { useTheme } from '@mui/styles';
import instruction from 'assets/images/instruction.png';
import { TableBody, useTable } from 'common/components/Table';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

export const CampaignTableBody = () => {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const { disable } = useTable();

    return (
        <TableBody
            rowSize={56}
            withoutDataConfig={{
                image: instruction,
                title: t('settings.cm.campaigns.noCampaigns.title'),
                description: t('settings.cm.campaigns.noCampaigns.description'),
                height: 500,
            }}
            tableRow={{
                sx: {
                    backgroundColor: theme.palette.background.paper,
                    paddingLeft: '28px',
                    flexGrow: 1,
                    '&:hover': !disable
                        ? {
                              '&:hover': {
                                  backgroundColor: 'var(--cm5)',
                                  '& .options': {
                                      opacity: '1 !important',
                                  },
                              },
                          }
                        : undefined,
                    '& .options': {
                        opacity: 0,
                    },
                },
            }}
            tableCell={{
                sx: {
                    paddingLeft: '28px',
                },
            }}
        />
    );
};
