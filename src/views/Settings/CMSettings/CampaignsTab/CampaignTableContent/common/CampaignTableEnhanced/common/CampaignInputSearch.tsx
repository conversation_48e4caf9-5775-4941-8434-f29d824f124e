import { Box, IconButton, InputAdornment } from '@mui/material';
import { styled } from '@mui/styles';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { TextFormField } from 'common/components/Inputs';
import { useTable } from 'common/components/Table';
import { initialTableQuery } from 'common/components/Table/helpers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { memo, useCallback, useState } from 'react';

const SearchFieldBox = styled(Box)({
    marginLeft: '20px',
    minWidth: 280,
});

export const CampaignInputSearch = memo(() => {
    const { t } = useAppTranslation();
    const { handleChangeQuery, isLoading, disable, refetch, query } = useTable();

    const [searchValue, setSearchInput] = useState('');

    const handleChangeInputSearch = useCallback(
        (value: string, active: boolean = false) => {
            setSearchInput(value);

            if (value.length === 0 || value.length >= 3 || active) {
                handleChangeQuery(
                    initialTableQuery({
                        searchValue: value,
                    })
                );
            }
        },
        [handleChangeQuery]
    );

    const handleInputChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            handleChangeInputSearch(e.target.value);
        },
        [handleChangeInputSearch]
    );

    const handleButtonClick = useCallback(() => {
        if (query.searchValue === searchValue) {
            refetch();
        } else {
            handleChangeInputSearch(searchValue, true);
        }
    }, [handleChangeInputSearch, searchValue, query.searchValue, refetch]);

    return (
        <SearchFieldBox>
            <TextFormField
                cmosVariant="roundedGrey"
                name={'search-campaign'}
                value={searchValue}
                placeholder={t(`settings.cm.campaigns.seekerPlaceholder`)}
                showLoader={isLoading}
                disabled={disable}
                enableEnterComplete={true}
                sx={{
                    width: '300px',
                }}
                onChange={handleInputChange}
                endAdornment={
                    <InputAdornment position="end">
                        <IconButton
                            onClick={handleButtonClick}
                            disabled={disable}
                            style={{
                                marginRight: -8,
                            }}
                        >
                            <SearchIcon fill={disable ? 'var(--cm4)' : 'var(--cm1)'} />
                        </IconButton>
                    </InputAdornment>
                }
            />
        </SearchFieldBox>
    );
});
