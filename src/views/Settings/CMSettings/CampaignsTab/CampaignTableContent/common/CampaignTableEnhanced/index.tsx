import { Box, styled, Typography } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { memo } from 'react';
import {
    CampaignImportModal,
    CampaignInputSearch,
    CampaignPriorityLevel,
    CampaignSwitchActivate,
} from './common';

const Title = styled(Typography)({
    color: '#26292B',
    fontWeight: 700,
    fontSize: '14px',
});

const CampaignTableEnhanced = memo(() => {
    const { t } = useAppTranslation();

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                padding: '25px 54px 24px 40px',
                gap: '24px',
            }}
        >
            <Title>{t('settings.cm.campaigns.title')}</Title>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                }}
            >
                <CampaignSwitchActivate />
                <Box
                    sx={{
                        display: 'flex',
                        gap: '15px',
                    }}
                >
                    <CampaignInputSearch />
                    <CampaignImportModal />
                </Box>
            </Box>
            <CampaignPriorityLevel />
        </Box>
    );
});
CampaignTableEnhanced.displayName = 'CampaignTableEnhanced';

export {
    CampaignImportModal,
    CampaignInputSearch,
    CampaignPriorityLevel,
    CampaignSwitchActivate,
    CampaignTableEnhanced,
};
