import { Box, styled, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import CampaignAPI from 'api/settings/Campaigns';
import { useTable } from 'common/components/Table';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { memo, useCallback, useMemo } from 'react';
import { useRepairShopCampaignConfiguration } from 'views/Settings/CMSettings/CampaignsTab';
import { RadioButtonArrayControl } from 'views/Settings/common';

const CAMPAIGN_PRIORITY = {
    URGENT: 'Urgent',
    SUGGESTED: 'Suggested',
    OK: 'Ok',
} as const;

type CampaignPriorityType = (typeof CAMPAIGN_PRIORITY)[keyof typeof CAMPAIGN_PRIORITY];

const textPrefix = 'settings.cm.campaigns.priorityLevel';

const Label = styled(Typography)({
    width: '210px',
    color: '#6A6E72',
    fontWeight: 700,
    fontSize: '12px',
    whiteSpace: 'normal',
    paddingTop: '4px',
});

export const CampaignPriorityLevel = memo(() => {
    const { t } = useAppTranslation();
    const { disable } = useTable();
    const { updateCampaignPriorityLevel, priorityLevel } = useCampaignPriorityLevel();

    const handleChange = useCallback(
        (newValue: CampaignPriorityType) => {
            updateCampaignPriorityLevel(newValue);
        },
        [updateCampaignPriorityLevel]
    );

    const radioValues = useMemo(
        () => [
            {
                id: CAMPAIGN_PRIORITY.URGENT,
                value: CAMPAIGN_PRIORITY.URGENT,
                label: t(`${textPrefix}.level.urgent`),
            },
            {
                id: CAMPAIGN_PRIORITY.SUGGESTED,
                value: CAMPAIGN_PRIORITY.SUGGESTED,
                label: t(`${textPrefix}.level.suggested`),
            },
            {
                id: CAMPAIGN_PRIORITY.OK,
                value: CAMPAIGN_PRIORITY.OK,
                label: t(`${textPrefix}.level.ok`),
            },
        ],
        [t]
    );

    return (
        <Box
            sx={{
                paddingTop: '-4px',
            }}
        >
            <RadioButtonArrayControl<CampaignPriorityType>
                disabled={disable}
                onChange={handleChange}
                label={
                    <Label
                        sx={{
                            opacity: disable ? 0.5 : 1,
                            width: '225px',
                        }}
                    >
                        {t(`${textPrefix}.text`)}
                    </Label>
                }
                value={priorityLevel}
                values={radioValues}
            />
        </Box>
    );
});

const useCampaignPriorityLevel = () => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { campaignConfiguration, handleChangeCampaignConfiguration } =
        useRepairShopCampaignConfiguration();

    const { mutate: updateCampaignPriorityLevel } = useMutation({
        mutationFn: (newLevel: CampaignPriorityType) =>
            CampaignAPI.updateCampaignConfiguration({
                ...campaignConfiguration,
                priorityLevel: newLevel,
            }),
        onSuccess: (data) => {
            toasters.success(
                t('settings.appointment.changeAppointmentStatusAfterSavedSuccessfully'),
                t('settings.appointment.changeAppointmentStatusAfterUpdated')
            );

            handleChangeCampaignConfiguration({
                priorityLevel: data.priorityLevel as CampaignPriorityType,
            });
        },
    });

    return { updateCampaignPriorityLevel, priorityLevel: campaignConfiguration.priorityLevel };
};
