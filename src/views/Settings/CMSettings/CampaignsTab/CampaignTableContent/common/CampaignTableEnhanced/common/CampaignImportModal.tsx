/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, Divider, styled, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { UploadSpotDto } from 'api/common';
import { getErrorMessage } from 'api/error';
import CampaignAPI from 'api/settings/Campaigns';
import { isAxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { DownloadCloudIcon } from 'common/components/Icons/DownloadCloudIcon';
import { Modal } from 'common/components/Modal';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { useTable } from 'common/components/Table';
import { initialTableQuery } from 'common/components/Table/helpers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useOpen } from 'common/hooks/useOpen';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { Interweave } from 'interweave';
import React, { useEffect, useState } from 'react';
import { HttpUtils } from 'services';
import { hasCode, isErrorResponse } from 'services/Server';
import DragAndDropFileInputControllable from 'views/Components/DragAndDropFileInput/DragAndDropFileInputControllable';
import { ImportedCampaignsDto } from '../../../../../../../../api/settings/Campaigns';

export function CampaignImportModal() {
    const { t } = useAppTranslation();
    const [open, handleOpen, handleClose] = useOpen();

    return (
        <React.Fragment>
            <Button onClick={handleOpen} sx={{ width: '200px' }}>
                {t('settings.cm.campaigns.importModal.openButton')}
            </Button>
            <ImportModalContent open={open} onClose={handleClose} />
        </React.Fragment>
    );
}

type ImportModalContentProps = {
    open: boolean;
    onClose: () => void;
};

type UploadFile = {
    token: string;
    file: File;
};

function ImportModalContent({ open, onClose }: ImportModalContentProps) {
    const { t } = useAppTranslation();
    const { refetch, query, handleChangeQuery } = useTable();
    const [closeModalOpen, setCloseModalOpen] = useState(false);
    const [file, setFile] = useState<File | null>(null);
    const [uploadedFile, setUploadedFile] = useState<UploadFile | null>(null);
    const [skippedRows, setSkippedRows] = useState<number[]>([]);
    const [disableTemplateDownload, setDisableTemplateDownload] = useState(false);

    useEffect(() => {
        (window as any).file = file;
    }, [file]);

    const uploadSpot = useUploadSpot(open);

    const resetState = () => {
        setSkippedRows([]);
        setUploadedFile(null);
        setFile(null);
        setCloseModalOpen(false);
    };

    const handleClose = () => {
        if (!isLoading) {
            if (!file) {
                onClose();
            } else {
                setCloseModalOpen(true);
            }
        }
    };

    const onResetPagination = () => {
        const initialQuery = initialTableQuery();
        if (
            query.page !== initialQuery.page ||
            query.pageSize !== initialQuery.pageSize ||
            query.searchValue !== initialQuery.searchValue
        ) {
            handleChangeQuery(initialQuery);
        } else {
            refetch();
        }
    };

    const { mutate: importCampaigns, isLoading } = useImportCampaigns({
        uploadSpot,
        onSuccess: () => {
            onResetPagination();
            onClose();
        },
    });

    const handleImport = () => {
        importCampaigns({
            uploadedFile: uploadedFile,
            skippedRows: skippedRows,
            file: file,
            handleUploadedFile: setUploadedFile,
        });
    };

    const handleTemplateDownload = async () => {
        CampaignAPI.downloadTemplate();
        setDisableTemplateDownload(true);
        setTimeout(() => {
            setDisableTemplateDownload(false);
        }, 700);
    };

    return (
        <Modal
            boxComponent={BoxComponent}
            open={open}
            onClose={handleClose}
            onTransitionExited={resetState}
        >
            <ConfirmationModal
                open={closeModalOpen}
                onClose={() => setCloseModalOpen(false)}
                onConfirmation={() => {
                    setCloseModalOpen(false);
                    onClose();
                }}
                cancelCaptionButton={t('settings.cm.campaigns.importModal.cancelButton.back')}
                confirmationCaptionButton={t(
                    'settings.cm.campaigns.importModal.cancelButton.cancel'
                )}
            >
                {t('settings.packages.importModal.cancel')}
            </ConfirmationModal>
            <Box sx={{ display: 'flex', p: 2, justifyContent: 'space-between' }}>
                <Typography variant="h5Roboto" color="neutral.9">
                    {t('settings.cm.campaigns.importModal.title')}
                </Typography>

                <Box sx={{ display: 'flex', gap: '5px' }}>
                    <Button
                        w="md"
                        color={Colors.Neutral3}
                        disabled={isLoading}
                        onClick={handleClose}
                    >
                        {t('commonLabels.cancel')}
                    </Button>
                    <Button
                        w="md"
                        color="success"
                        disabled={!file || isLoading}
                        showLoader={isLoading}
                        onClick={handleImport}
                    >
                        {t('settings.packages.importModal.uploadFile')}
                    </Button>
                </Box>
            </Box>

            <DragAndDropFileInputControllable
                acceptedFormats={['xlsx']}
                onFileSet={setFile}
                file={file}
            />

            <Divider sx={{ my: 3 }} />

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr auto' }}>
                <Typography
                    variant="h6Roboto"
                    sx={{ fontWeight: 'normal', color: 'var(--neutral8)' }}
                >
                    <strong>{t('commonLabels.notes').toUpperCase()}</strong>
                    <Box sx={{ m: 0, whiteSpace: 'pre' }}>
                        {t('settings.packages.importModal.notes')}
                    </Box>
                </Typography>

                <Button
                    disabled={disableTemplateDownload}
                    cmosVariant="typography"
                    onClick={handleTemplateDownload}
                >
                    <DownloadCloudIcon fill="currentColor" />
                    {t('settings.packages.importModal.downloadTemplate')}
                </Button>
            </Box>
        </Modal>
    );
}

const BoxComponent = styled('div')({
    width: 820,
    padding: '30px 40px',
});

function useUploadSpot(enabled: boolean): UploadSpotDto | undefined {
    const { data } = useQuery(
        ['settings', 'campaigns-import-upload-spot'],
        () => CampaignAPI.getCampaignsImportUploadSpot(),
        {
            staleTime: 10 * 1000,
            cacheTime: 24 * 60 * 60 * 1000,
            enabled,
        }
    );

    return data;
}

function useImportCampaigns({
    uploadSpot,
    onSuccess,
}: {
    uploadSpot: UploadSpotDto | undefined;
    onSuccess: () => void;
}) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    return useMutation({
        mutationFn: async ({
            uploadedFile,
            skippedRows,
            file,
            handleUploadedFile,
        }: {
            uploadedFile: UploadFile | null;
            skippedRows: number[];
            file: File | null;
            handleUploadedFile: (file: UploadFile) => void;
        }) => {
            if (!uploadSpot) {
                throw new Error('upload spot has not been created yet');
            }
            if (!file) {
                throw new Error('file is not null');
            }

            if (
                !uploadedFile ||
                uploadedFile.file !== file ||
                uploadedFile.token !== uploadSpot.fileToken
            ) {
                await HttpUtils.uploadFileViaPut(uploadSpot.uploadLink, file);
                handleUploadedFile({ file, token: uploadSpot.fileToken });
            }

            return await CampaignAPI.importCampaign({
                fileToken: uploadSpot.fileToken,
                skippedRows: skippedRows,
            });
        },

        onSuccess(result: ImportedCampaignsDto) {
            if (result.errorTypes.length > 0) {
                toasters.warning(
                    <Interweave
                        content={t('toasters.documentPartiallyUploadedText', {
                            failedRowsFileUrl: result.failedRowsFileUrl,
                            totalRows: result.totalRows,
                            importedRows: result.importedRows,
                        })}
                    />,
                    t('toasters.documentPartiallyUploadedTitle'),
                    { duration: 15000 }
                );
            } else {
                toasters.success(
                    t('settings.packages.importModal.success.text'),
                    t('settings.packages.importModal.success.title')
                );
            }

            onSuccess && onSuccess();
        },
        onError(error, _variables, _context) {
            if (isAxiosError(error) && isErrorResponse(error.response?.data)) {
                if (hasCode(error.response.data, 'General.Packages.RequiredColumnMissing')) {
                    toasters.danger(
                        t('settings.packages.importModal.mandatoryColMissing.text'),
                        t('settings.packages.importModal.mandatoryColMissing.title')
                    );
                    return;
                }
            }

            toasters.danger(
                t('toasters.errorOccurredWhenSaving') + ': ' + getErrorMessage(error),
                t('toasters.errorOccurred')
            );
        },
    });
}
