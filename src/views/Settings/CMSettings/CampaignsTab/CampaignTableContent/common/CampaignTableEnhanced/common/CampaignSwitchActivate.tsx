import { Box, Typography } from '@mui/material';
import { styled } from '@mui/styles';
import { useMutation } from '@tanstack/react-query';
import CampaignAPI from 'api/settings/Campaigns';
import { Switch } from 'common/components/Inputs/Switch';
import { useTable } from 'common/components/Table';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { memo, useCallback } from 'react';
import { useRepairShopCampaignConfiguration } from 'views/Settings/CMSettings/CampaignsTab';

const SSwitch = styled(Switch)(({ theme }) => ({
    '& .MuiSwitch-switchBase': {
        '&.Mui-checked': {
            color: theme.palette.neutral[1],
            '& .MuiSwitch-thumb:before': {
                color: theme.palette.neutral[1],
            },
            '& + .MuiSwitch-track': {
                opacity: 1,
            },
        },
        '& .MuiSwitch-thumb:before': {
            color: theme.palette.neutral[1],
        },
    },
}));

const SwitchLabel = styled(Typography)({
    color: '#4A4D51',
    fontWeight: 700,
    fontSize: '12px',
});

const Text = styled(Typography)({
    color: '#4A4D51',
    fontWeight: 400,
    fontSize: '12px',
    fontFamily: 'Roboto',
});

export const CampaignSwitchActivate = memo(() => {
    const { t } = useAppTranslation();

    const { activeCampaign, active } = useActiveCampaign();

    const handleToggleActiveCampaign = useCallback(() => {
        activeCampaign();
    }, [activeCampaign]);

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                marginTop: '-10px',
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    gap: '4px',
                    marginLeft: '-10px',
                }}
            >
                <SSwitch checked={active} onChange={handleToggleActiveCampaign} name="checkedA" />
                <SwitchLabel>{t('settings.cm.campaigns.activateSwitch.label')}</SwitchLabel>
            </Box>
            <Box
                sx={{
                    paddingLeft: '50px',
                }}
            >
                <Text>{t('settings.cm.campaigns.activateSwitch.subText')}</Text>
            </Box>
        </Box>
    );
});

const useActiveCampaign = () => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { handleToggleActive, active } = useTable();
    const { campaignConfiguration, handleChangeCampaignConfiguration } =
        useRepairShopCampaignConfiguration();

    const onSuccess = useCallback(() => {
        toasters.success(
            t('settings.appointment.changeAppointmentStatusAfterSavedSuccessfully'),
            t('settings.appointment.changeAppointmentStatusAfterUpdated')
        );

        handleToggleActive();
        handleChangeCampaignConfiguration({ enableVehicleCampaign: !active });
    }, [handleToggleActive, handleChangeCampaignConfiguration, active, t, toasters]);

    const { mutate: activeCampaign } = useMutation({
        mutationFn: () =>
            CampaignAPI.updateCampaignConfiguration({
                ...campaignConfiguration,
                enableVehicleCampaign: !active,
            }),
        onSuccess,
    });

    return { activeCampaign, active };
};
