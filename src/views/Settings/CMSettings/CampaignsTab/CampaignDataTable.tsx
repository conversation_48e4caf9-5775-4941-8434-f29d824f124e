import { Box, SxProps, Typography } from '@mui/material';
import { useTheme } from '@mui/styles';
import { useQuery } from '@tanstack/react-query';
import CampaignAPI, { Campaign } from 'api/settings/Campaigns';
import { TableProviderColumn } from 'common/components/Table/types';
import TextOverflowTooltip from 'common/components/TextOverflowTooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import { useRef, useState } from 'react';
import { CampaignDeleteButton } from './CampaignTableContent/common/CampaignDeleteButton';

type CampaignsDate = {
    value: string;
    ianaTz: string;
    format: string;
};

enum AllColumnBodyTextType {
    allBrands = 'allBrands',
    allModels = 'allModels',
    allYears = 'allYears',
    allVins = 'allVins',
}

export const campaignsTabData = (
    dateFormat: Omit<CampaignsDate, 'value'>
): TableProviderColumn<Campaign>[] => [
    {
        head: {
            field: 'CampaignCode',
            render: ({ disable }) => (
                <ColumnTitle
                    {...{ disable }}
                    text="campaignCode"
                    styles={{ marginLeft: '12px', whiteSpace: 'nowrap', width: '70px' }}
                />
            ),
        },
        body: ({ campaignCode }, { disable }) => (
            <ColumnBodyText
                text={campaignCode}
                {...{ disable }}
                styles={{ marginLeft: '12px', width: '100px' }}
            />
        ),
    },

    {
        head: {
            field: 'CampaignName',
            render: ({ disable }) => (
                <ColumnTitle
                    {...{ disable }}
                    text="campaignName"
                    styles={{ whiteSpace: 'nowrap', width: '80px' }}
                />
            ),
        },
        body: ({ campaignName }, { disable }) => (
            <ColumnBodyText text={campaignName} {...{ disable }} styles={{ width: '150px' }} />
        ),
    },
    {
        head: {
            field: 'Vin',
            render: ({ disable }) => (
                <ColumnTitle {...{ disable }} text="vins" styles={{ width: '90px' }} />
            ),
        },
        body: ({ vins, campaignId }, { disable }) => (
            <ColumnBodyTextType {...{ campaignId, initialData: vins, disable, type: 'vins' }} />
        ),
    },
    {
        head: {
            field: 'Brand',
            render: ({ disable }) => <ColumnTitle {...{ disable }} text="brand" />,
        },
        body: ({ brands, vins, campaignId }, { disable }) => (
            <ColumnBodyTextType
                showDash={vins.length > 0}
                {...{ campaignId, initialData: brands, disable, type: 'brands' }}
            />
        ),
    },
    {
        head: {
            field: 'Model',
            render: ({ disable }) => <ColumnTitle {...{ disable }} text="model" />,
        },
        body: ({ models, vins, campaignId }, { disable }) => (
            <ColumnBodyTextType
                showDash={vins.length > 0}
                {...{ campaignId, initialData: models, disable, type: 'models' }}
            />
        ),
    },
    {
        head: {
            field: 'Year',
            render: ({ disable }) => (
                <ColumnTitle {...{ disable }} text="year" styles={{ marginRight: '30px' }} />
            ),
        },
        body: ({ years, campaignId, vins }, { disable }) => (
            <ColumnBodyTextType
                showDash={vins.length > 0}
                {...{ campaignId, initialData: years, disable, type: 'years' }}
            />
        ),
    },
    {
        head: {
            field: 'StartDate',
            render: ({ disable }) => (
                <ColumnTitle
                    {...{ disable }}
                    text="startDate"
                    styles={{ whiteSpace: 'nowrap', width: '60px' }}
                />
            ),
        },
        body: ({ startDate }, { disable }) => (
            <ColumnBodyText
                text={formatDate({ ...dateFormat, value: startDate })}
                {...{ disable }}
            />
        ),
    },
    {
        head: {
            field: 'EndDate',
            render: ({ disable }) => (
                <ColumnTitle
                    {...{ disable }}
                    text="endDate"
                    styles={{ whiteSpace: 'nowrap', width: '60px' }}
                />
            ),
        },
        body: ({ endDate }, { disable }) => (
            <ColumnBodyText text={formatDate({ ...dateFormat, value: endDate })} {...{ disable }} />
        ),
    },
    {
        head: {
            field: '',
            render: () => '',
        },
        body: (campaign) => (
            <Box sx={{ padingRight: '40px' }}>
                <CampaignDeleteButton {...{ campaign }} />
            </Box>
        ),
    },
];

const formatDate = (date: CampaignsDate) => {
    return DateTime.fromISO(date.value, {
        zone: 'utc',
    })
        .setZone(date.ianaTz)
        .toFormat(date.format);
};

const ColumnTitle = ({
    disable,
    text,
    styles,
}: {
    disable?: boolean;
    text: string;
    styles?: SxProps;
}) => {
    const { t } = useAppTranslation();
    const theme = useTheme();

    return (
        <Typography
            sx={{
                ...theme.typography.h6Inter,
                opacity: !disable ? '100%' : '40%',
                color: '#6A6E72',
                fontSize: 12,
                fontStyle: 'normal',
                fontWeight: 700,
                lineHeight: 'normal',
                ...styles,
            }}
        >
            {' '}
            {t(`${'settings.cm.campaigns.tableHeaders'}.${text}`)}
        </Typography>
    );
};

const AllColumnBodyText = ({
    disable,
    text,
    dash,
}: {
    disable?: boolean;
    text: AllColumnBodyTextType;
    dash?: boolean;
}) => {
    const { t } = useAppTranslation();
    return (
        <ColumnBodyText
            text={t(`${'settings.cm.campaigns.columns'}.${text}`)}
            {...{ disable, dash }}
        />
    );
};

const ColumnBodyTextType = ({
    campaignId,
    initialData,
    disable,
    type,
    showDash = false,
}: {
    campaignId: string;
    initialData: string[];
    disable: boolean;
    type: 'models' | 'years' | 'brands' | 'vins';
    showDash?: boolean;
}) => {
    const [data, setData] = useState<string[]>(initialData);
    const [isHovered, setIsHovered] = useState(false);
    const hasLoadedRef = useRef(false);

    useQuery(
        ['campaigns', type, campaignId],
        () => CampaignAPI.getCampaignExtraDetails(campaignId, type),
        {
            enabled: !!campaignId && initialData.length > 0 && isHovered && !hasLoadedRef.current,
            onSuccess: (result: string[]) => {
                setData(result);
                hasLoadedRef.current = true;
            },
        }
    );

    return (
        <div onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
            {data.length > 0 ? (
                <ColumnBodyText text={data.join(', ')} {...{ disable }} />
            ) : showDash ? (
                <ColumnBodyText text={'-'} {...{ disable }} />
            ) : (
                <AllColumnBodyText text={AllColumnBodyTextType.allModels} {...{ disable }} />
            )}
        </div>
    );
};

const ColumnBodyText = ({
    disable,
    text,
    dash,
    styles,
}: {
    disable?: boolean;
    text: string;
    dash?: boolean;
    styles?: SxProps;
}) => {
    return (
        <TextOverflowTooltip text={text}>
            {(ref) => (
                <Typography
                    {...{ ref }}
                    sx={{
                        display: 'inline-block',
                        whiteSpace: 'nowrap' as const,
                        textOverflow: 'ellipsis',
                        overflow: 'hidden',
                        width: '100px',
                        opacity: !disable ? '100%' : '40%',
                        ...styles,
                    }}
                >
                    {dash ? '-' : text}
                </Typography>
            )}
        </TextOverflowTooltip>
    );
};

export const campaignDictionaryPrefix = 'settings.cm.campaigns.tableHeaders';
