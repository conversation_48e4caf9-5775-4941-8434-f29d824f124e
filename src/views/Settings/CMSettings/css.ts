import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';
import { scrollbarStyle } from '../../../common/styles/ScrollbarStyles';

export const useStyles = makeStyles((theme) => ({
    navTab: {
        gap: 40,
        marginTop: 26,
    },
    navTabButton: {
        ...FontSecondary(HeaderStyles.H5_14px, true, theme.palette.neutral[7]),
        cursor: 'pointer',
        textDecoration: 'none',
        '&:hover': {
            paddingBottom: 6,
            borderBottom: `2px solid ${theme.palette.neutral[7]}`,
        },
    },
    navTabButtonActive: {
        color: `${theme.palette.primary.main}!important`,
        paddingBottom: 6,
        borderBottom: `2px solid ${theme.palette.primary.main}!important`,
    },
    tabContainer: {
        overflowY: 'auto',
        maxHeight: 'calc(80vh + 2px)',
        ...scrollbarStyle(),
    },
}));
