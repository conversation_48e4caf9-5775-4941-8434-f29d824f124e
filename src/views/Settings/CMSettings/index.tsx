import { RouterTab } from 'common/components/tabs/RouterParameterBasedTabs';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import SettingsRouterParameterBasedTabs from '../common/SettingsRouterParameterBasedTabs';
import { tabs } from './helpers';

const CMSettings = () => {
    const { t } = useAppTranslation();
    const globalSettings = useSelector(selectSettings);

    const visibleTabs: RouterTab[] = useMemo(
        () =>
            tabs
                .filter((m) => m.isVisible(globalSettings))
                .map(({ component, id, label }) => ({
                    value: id,
                    content: component,
                    label: t(label),
                })),
        [globalSettings, t]
    );
    useDocumentTitle(t('titles.settings.cm'));

    return (
        <SettingsRouterParameterBasedTabs
            urlPattern={ROUTES.SETTINGS.CM.PATH}
            parameterName="section"
            tabs={visibleTabs}
        />
    );
};
export default CMSettings;
