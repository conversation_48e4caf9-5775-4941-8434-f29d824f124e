import RadioButtonArray, { RadioButtonArrayProps } from 'views/Components/RadioButtonArray';
import SettingLayoutControl, { SettingLayoutControlProps } from './SettingLayoutControl';

export type RadioButtonArrayControlProps<T> = RadioButtonArrayProps<T> &
    Pick<
        SettingLayoutControlProps,
        'hasHint' | 'hintText' | 'hintPosition' | 'customHint' | 'alignItems'
    > & {
        label: string | React.ReactNode;
    };

export default function RadioButtonArrayControl<T>({
    label,
    hasHint,
    hintText,
    customHint,
    hintPosition,
    alignItems,
    ...props
}: RadioButtonArrayControlProps<T>) {
    return (
        <SettingLayoutControl
            label={label}
            hasHint={hasHint}
            hintText={hintText}
            hintPosition={hintPosition}
            customHint={customHint}
            alignItems={alignItems}
        >
            <RadioButtonArray<T> {...props} />
        </SettingLayoutControl>
    );
}
