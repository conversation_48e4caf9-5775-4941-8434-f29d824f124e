import { makeStyles } from '@mui/styles';
import debounce from 'lodash/debounce';
import { createContext, useContext, useMemo, useState } from 'react';
import ReactDOM from 'react-dom';

interface IFieldSectionTooltipContext {
    open(imageUrl: string): void;
    requireClose(): void;
}

const FieldSectionTooltipContext = createContext<IFieldSectionTooltipContext | null>(null);

export function useFieldSectionTooltipContext(): IFieldSectionTooltipContext {
    const ctx = useContext(FieldSectionTooltipContext);

    if (!ctx) throw new Error('FieldSectionTooltipContext is not available');

    return ctx;
}

const useStyles = makeStyles((theme) => ({
    popup: {
        overflow: 'hidden',
        backgroundColor: theme.palette.neutral[1],
        borderRadius: 10,
        boxShadow: `0 2px 6px ${theme.palette.neutral[5]}`,
        maxWidth: '50vw',
        maxHeight: '50vh',
        padding: 5,
        pointerEvents: 'none',
        position: 'fixed',
        top: '50vh',
        left: '40vw',
        transform: 'translate(0, -50%)',
        zIndex: 2000,
        height: '70vh',
    },

    img: {
        borderRadius: 10,
        height: '100%',
    },
}));

export function FieldSectionTooltipContextProvider({ children }: React.PropsWithChildren<{}>) {
    const [imageUrl, setImageUrl] = useState<string>();
    const styles = useStyles();

    const requireClose = useMemo(() => debounce(() => setImageUrl(undefined), 1000), []);

    const ctx: IFieldSectionTooltipContext = useMemo(
        () => ({
            open: (url) => {
                setImageUrl(url);
                requireClose.cancel();
            },
            requireClose,
        }),
        [requireClose]
    );

    return (
        <FieldSectionTooltipContext.Provider value={ctx}>
            {children}
            {ReactDOM.createPortal(
                imageUrl ? (
                    <div className={styles.popup}>
                        <img className={styles.img} src={imageUrl} alt="" />
                    </div>
                ) : null,
                document.body
            )}
        </FieldSectionTooltipContext.Provider>
    );
}
