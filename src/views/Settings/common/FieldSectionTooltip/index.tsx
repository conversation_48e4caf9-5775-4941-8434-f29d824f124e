import { FieldSection } from 'api/fields';
import { InfoTooltipInternal } from 'common/components/InfoTooltip';
import React, { useCallback, useEffect } from 'react';
import { useFieldSectionTooltipContext } from './context';

import customerInfoSectionImage from 'assets/images/customer_info_section.png';
import generalInformationSectionImage from 'assets/images/general_information.png';
import jobInfoSectionImage from 'assets/images/job_info_section.png';
import mobileCustomerSectionImage from 'assets/images/mobile_customer_section.png';
import mobileJobSectionImage from 'assets/images/mobile_job_section.png';
import mobileOrderSectionImage from 'assets/images/mobile_order_section.png';
import mobileVehicleSectionImage from 'assets/images/mobile_vehicle_section.png';
import orderInfoSectionImage from 'assets/images/order_info_section.png';
import orderLaborSectionImage from 'assets/images/order_labor.png';
import orderPartsSectionImage from 'assets/images/order_parts.png';
import serviceHoursInfoSectionImage from 'assets/images/service_hours_info.png';
import vehicleInfoSectionImage from 'assets/images/vehicle_info_section.png';

type FieldSectionTooltipProps = {
    section: FieldSection;
};

const IMAGES: Partial<Record<FieldSection, string>> = {
    WebLocationGeneralInfo: generalInformationSectionImage,
    WebLocationServiceHoursInfo: serviceHoursInfoSectionImage,
    WebCustomerInfo: customerInfoSectionImage,
    WebVehicleInfo: vehicleInfoSectionImage,
    WebOrderInfo: orderInfoSectionImage,
    WebJobInfo: jobInfoSectionImage,
    MobileCustomer: mobileCustomerSectionImage,
    MobileOrder: mobileOrderSectionImage,
    MobileVehicle: mobileVehicleSectionImage,
    WebLabor: orderLaborSectionImage,
    WebParts: orderPartsSectionImage,
    MobileJobInfo: mobileJobSectionImage,
};

export default function FieldSectionTooltip({ section }: FieldSectionTooltipProps) {
    const ctx = useFieldSectionTooltipContext();

    useEffect(() => {
        preloadAllImages();
    }, []);
    const imageUrl = IMAGES[section];

    const onMouseEnter = useCallback(
        (e: React.MouseEvent<HTMLDivElement>) => {
            if (imageUrl) ctx.open(imageUrl);
        },
        [ctx, imageUrl]
    );

    const close = useCallback(() => ctx.requireClose(), [ctx]);

    if (!imageUrl) return null;

    return <InfoTooltipInternal onMouseLeave={() => close()} onMouseEnter={onMouseEnter} />;
}

function preloadImage(url: string) {
    const img = new Image();
    img.src = url;
}

let preloaded = false;

function preloadAllImages() {
    if (preloaded) return;

    preloadImage(customerInfoSectionImage);
    preloadImage(vehicleInfoSectionImage);
    preloadImage(orderInfoSectionImage);
    preloadImage(mobileCustomerSectionImage);
    preloadImage(mobileOrderSectionImage);
    preloadImage(mobileVehicleSectionImage);
    preloadImage(orderPartsSectionImage);
    preloadImage(orderLaborSectionImage);
    preloadImage(generalInformationSectionImage);
    preloadImage(serviceHoursInfoSectionImage);
    preloadImage(jobInfoSectionImage);
    preloadImage(mobileJobSectionImage);

    preloaded = true;
}
