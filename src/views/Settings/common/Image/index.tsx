import { forwardRef } from 'react';
import useImageStyles from './styles';

export type ImageProps = React.ImgHTMLAttributes<HTMLImageElement> & {
    noImageText?: string;
};

const Image = forwardRef<HTMLImageElement, ImageProps>(
    ({ children, alt, noImageText, ...props }, ref) => {
        const styles = useImageStyles();

        return (
            <div className={styles.wrapper}>
                {noImageText && !props.src && (
                    <span className={styles.noImageText}>{noImageText}</span>
                )}
                {props.src && <img alt={alt ?? noImageText} {...props} ref={ref} />}
            </div>
        );
    }
);

export default Image;
