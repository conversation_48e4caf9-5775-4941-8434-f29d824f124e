import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const useImageStyles = makeStyles((theme) => ({
    wrapper: {
        overflow: 'hidden',
        borderRadius: 10,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        border: `1px solid ${theme.palette.neutral[4]}`,
        maxWidth: '200px',
        minHeight: '100px',
    },
    noImageText: {
        ...FontPrimary(HeaderStyles.H6_12px, true, theme.palette.neutral[4]),
    },
}));

export default useImageStyles;
