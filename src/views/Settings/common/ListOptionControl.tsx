import { Box, Grid } from '@mui/material';
import { Button } from 'common/components/Button';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import useSettingStyles from './styles';

export type ListOptionValue<T> = {
    id: T;
    key: string;
    value: string;
};

export type KeyEditor = (props: {
    value: string;
    onChange: (value: string) => void;
}) => JSX.Element;

export type ValueEditor = (props: {
    value: string;
    onChange: (value: string) => void;
}) => JSX.Element;

type ListOptionControlProps<T> = {
    value: ListOptionValue<T>;
    handleOnSave: (value: ListOptionValue<T>) => Promise<void>;
    handleOnDelete: (value: ListOptionValue<T>) => Promise<void>;
    handleOnCancel: () => void;
    KeyEditor?: KeyEditor;
    ValueEditor: ValueEditor;
    forceEditState?: boolean;
    isOptionValid?: (value: ListOptionValue<T>) => boolean;
};

export default function ListOptionControl<T>({
    value,
    handleOnSave,
    handleOnDelete,
    handleOnCancel,
    KeyEditor,
    ValueEditor,
    forceEditState,
    isOptionValid,
}: ListOptionControlProps<T>) {
    const [isEditing, setEditing] = useState(forceEditState);
    const [editedValue, setEditedValue] = useState(value);

    const { t } = useAppTranslation();
    const styles = useSettingStyles();

    //Update value, when Id, for example, beign changed
    useEffect(() => {
        setEditedValue({ ...editedValue, id: value.id, key: value.key });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value.id, value.key]);

    const handleEditClicked = async () => {
        setEditing(true);
    };

    const handleDeleteClicked = async () => {
        await handleOnDelete(editedValue);
    };

    const handleCancelClick = () => {
        handleOnCancel();
        setEditing(false);
    };

    const handleSaveClick = async () => {
        await handleOnSave(editedValue);
        setEditing(false);
    };

    return (
        <Grid item container alignItems="flex-start">
            <Grid className={styles.labelContainer} item xs={4} md={3}>
                {isEditing && KeyEditor ? (
                    <KeyEditor
                        value={editedValue.key}
                        onChange={(e) => setEditedValue({ ...editedValue, key: e })}
                    />
                ) : (
                    <label className={styles.label}>{editedValue.key}</label>
                )}
            </Grid>
            <Grid xs={8} md={9} item>
                <Box alignItems="center" display="flex" justifyContent="space-between">
                    {isEditing ? (
                        <ValueEditor
                            value={editedValue.value}
                            onChange={(e) => setEditedValue({ ...editedValue, value: e })}
                        />
                    ) : (
                        <>
                            <Box className={styles.listOptionTextContainer}>
                                <label>{editedValue.value}</label>
                            </Box>
                            <Box display="flex">
                                <Button
                                    cmosVariant={'typography'}
                                    Icon={EditIcon}
                                    color={Colors.Neutral3}
                                    onClick={handleEditClicked}
                                />
                                <Button
                                    cmosVariant={'typography'}
                                    Icon={DeleteIcon}
                                    color={Colors.Neutral3}
                                    onClick={handleDeleteClicked}
                                />
                            </Box>
                        </>
                    )}
                </Box>
                {isEditing && (
                    <Box
                        display="flex"
                        justifyContent="flex-end"
                        gap={2}
                        marginBottom="12px"
                        marginTop="12px"
                    >
                        <Button
                            customStyles={{ width: 128 }}
                            cmosVariant={'stroke'}
                            color={Colors.Neutral3}
                            label={t('commonLabels.cancel')}
                            onClick={handleCancelClick}
                        />

                        <Button
                            customStyles={{ width: 128 }}
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            disabled={isOptionValid !== undefined && !isOptionValid(editedValue)}
                            label={t('commonLabels.save')}
                            onClick={handleSaveClick}
                        />
                    </Box>
                )}
            </Grid>
        </Grid>
    );
}
