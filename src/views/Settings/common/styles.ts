import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { SingleLineText } from 'common/styles/TextHelpers';

const useSettingStyles = makeStyles((theme) => ({
    tabContent: {
        height: '80vh',
    },
    section: {
        padding: '32px 0 32px 0',
        gap: 24,
        display: 'flex',
        flexDirection: 'column',

        '&:not(:last-child)': {
            borderBottom: `solid 1px ${theme.palette.neutral[3]}`,
        },
    },
    sectionTitle: {
        ...FontPrimary(HeaderStyles.H5_14px, true, Colors.Black),
    },
    labelContainer: {
        paddingRight: 20,
        maxWidth: 240,
        boxSizing: 'border-box',
        lineHeight: '16px',
    },
    label: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        wordBreak: 'break-word',
        position: 'relative',
    },
    checkbox: {
        padding: 1,
    },
    textArea: {
        width: '100%',
    },
    button: {
        width: '130px',
    },

    radioButton: {
        padding: 0,
    },
    radioButtonDescription: {
        ...FontSecondary(HeaderStyles.H7_11px, false, theme.palette.neutral[6]),
        paddingLeft: 28,
    },

    // resetable components
    lockedResettableField: {
        cursor: 'pointer',
    },
    addButton: {
        width: 189,

        '& > div': {
            justifyContent: 'space-between',
            width: 'calc(100% - 20px)',
        },
    },

    listOptionsContainer: {
        '& > :last-child': {
            marginTop: 5,
        },
    },

    listOptionTextContainer: {
        ...SingleLineText(),
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.neutral[5]),
    },

    settingsControl: {
        width: '60%',
    },
}));

export default useSettingStyles;
