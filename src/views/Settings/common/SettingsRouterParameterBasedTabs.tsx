import { styled } from '@mui/styles';
import RouterParameterBasedTabs, {
    RouterParameterBasedTabsProps,
} from 'common/components/tabs/RouterParameterBasedTabs';
import SimpleErrorBoundary from 'utils/errorsHandling/SimpleErrorBoundary';
import { PageLayout } from 'views/Components/Page';
import { StickyTabsWrapper } from 'views/Components/StickyTabsWrapper';

const tabsHeight = 46;

type SettingsRouterParameterBasedTabsProps = Omit<RouterParameterBasedTabsProps, 'children'> &
    Partial<Pick<RouterParameterBasedTabsProps, 'children'>>;

const directChildren: RouterParameterBasedTabsProps['children'] = (c) => c;

export default function SettingsRouterParameterBasedTabs({
    children = directChildren,
    ...props
}: SettingsRouterParameterBasedTabsProps) {
    return (
        <RouterParameterBasedTabs
            sx={{ height: tabsHeight }}
            renderLayout={(v) => (
                <div className="scrollbar-gutter-fix">
                    <PageLayout>
                        <StickyTabsWrapper alwaysDetached>{v.tabs}</StickyTabsWrapper>

                        <DivContent>
                            <SimpleErrorBoundary key={v.selected}>{v.content}</SimpleErrorBoundary>
                        </DivContent>
                    </PageLayout>
                </div>
            )}
            {...props}
            children={children}
        />
    );
}

const DivContent = styled('div')({
    paddingTop: 15,
    paddingBottom: 30,
});
