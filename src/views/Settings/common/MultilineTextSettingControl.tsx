import { Box } from '@mui/material';
import { isNullOrWhiteSpaces } from 'common/StringHelper';
import { Button } from 'common/components/Button';
import { TextArea } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { ChangeEvent, useState } from 'react';
import SettingLayoutControl from './SettingLayoutControl';
import useStyles from './styles';

type MultilineTextSettingControlProps = {
    value: string;
    onSave: (value: string) => Promise<void>;
    onChange: (value: ChangeEvent<HTMLTextAreaElement>) => void;
    label: string;
    placeholder?: string;
    name?: string;
    isRequired?: boolean;
};

/**
 * Multiline text field (TextArea) with a "save" button and onSave callback.
 * If onSave returns a promise, button will be blocked until the promise
 * is resolved or rejected.
 */
export default function MultilineTextSettingControl({
    onSave,
    label,
    value,
    isRequired,
    ...props
}: MultilineTextSettingControlProps) {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [isLoading, setLoading] = useState(false);
    const onClickCallback = async () => {
        setLoading(true);
        try {
            await onSave(value);
        } finally {
            setLoading(false);
        }
    };

    return (
        <SettingLayoutControl label={label}>
            <TextArea
                rows={6}
                hideLabel
                className={styles.textArea}
                value={value ?? ''}
                isRequired={isRequired}
                {...props}
            />
            <Box marginTop={2} display="flex" justifyContent="flex-end">
                <Box>
                    {/* TODO: Use something like "Saving..." instead of "..." */}
                    <Button
                        disabled={isLoading || (isRequired && isNullOrWhiteSpaces(value))}
                        className={styles.button}
                        onClick={onClickCallback}
                        cmosVariant={'filled'}
                        color={Colors.Success}
                        label={isLoading ? '...' : t('commonLabels.save')}
                    />
                </Box>
            </Box>
        </SettingLayoutControl>
    );
}
