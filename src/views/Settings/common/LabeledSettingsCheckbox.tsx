import { FormControlLabel, Typography, styled } from '@mui/material';
import ArrowTooltip from 'common/components/Tooltip';
import { forwardRef } from 'react';
import SettingsCheckbox, { SettingsCheckboxProps } from './SettingsCheckbox';

export type LabeledSettingsCheckboxProps = SettingsCheckboxProps & {
    label: string;
    width?: number;
    minWidth?: number;
    marginRight?: number;
    tooltip?: string;
};

const Label = styled(Typography)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',
}));

const CustomFormControlLabel = styled(FormControlLabel)({
    marginLeft: 0,
    marginRight: 0,
});

const Root = styled('div')({
    display: 'flex',
    alignItems: 'center',
});

const LabeledSettingsCheckbox = forwardRef(
    (
        { label, marginRight, minWidth, width, tooltip, ...props }: LabeledSettingsCheckboxProps,
        ref: React.ForwardedRef<HTMLDivElement>
    ) => {
        return (
            <ArrowTooltip content={tooltip ?? ''} disabled={!tooltip}>
                <Root ref={ref} style={{ width, minWidth }}>
                    <CustomFormControlLabel
                        control={<SettingsCheckbox {...props} />}
                        label={<Label>{label}</Label>}
                    />
                </Root>
            </ArrowTooltip>
        );
    }
);

export default LabeledSettingsCheckbox;
