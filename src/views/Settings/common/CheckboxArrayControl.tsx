import CheckboxArray, { CheckboxArrayProps } from 'views/Components/CheckboxArray';
import SettingLayoutControl, { SettingLayoutControlProps } from './SettingLayoutControl';

export type CheckboxArrayControlProps = CheckboxArrayProps & {
    label: string;
    width?: number;
    children?: React.ReactNode;
    slotProps?: {
        layout?: Partial<SettingLayoutControlProps>;
    };
};

export default function CheckboxArrayControl({
    label,
    children,
    slotProps = {},
    ...props
}: CheckboxArrayControlProps) {
    return (
        <SettingLayoutControl label={label} {...slotProps.layout}>
            <CheckboxArray {...props} />
            {children}
        </SettingLayoutControl>
    );
}
