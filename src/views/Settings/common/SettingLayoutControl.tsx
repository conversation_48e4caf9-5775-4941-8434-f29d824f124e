import { styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import InfoTooltip from 'common/components/InfoTooltip';
import { CSSProperties } from 'react';
import SettingLayoutLabel from './SettingLayoutLabel';

export type SettingLayoutControlProps = {
    disabled?: boolean;
    label?: React.ReactNode;
    children?: React.ReactNode;
    hasHint?: boolean;
    hintText?: string;
    hintPosition?: string;
    customHint?: React.ReactNode;
    alignItems?: CSSProperties['alignItems'];
    expanded?: boolean;
    childrenBelow?: React.ReactNode;
    labelMD?: number;
    childrenMD?: number;
};

/**
 * Main component for all settings controls. Has a label on the left side
 * and arbitrary content (children components).
 */
export default function SettingLayoutControl({
    disabled,
    label,
    children,
    hasHint,
    hintText,
    hintPosition,
    customHint,
    alignItems = 'flex-start',
    childrenBelow,
    expanded,
    labelMD = 3,
    childrenMD = 8,
}: SettingLayoutControlProps) {
    const inner = (
        <Grid item container sx={{ alignItems }}>
            <GridLabelContainer item xs={4} md={labelMD}>
                <SettingLayoutLabel style={{ display: 'flex' }}>
                    <SLabel opaque={!disabled}>{label}</SLabel>

                    {hasHint &&
                        (customHint ? (
                            customHint
                        ) : (
                            <InfoTooltipContainer>
                                <InfoTooltip text={hintText ?? ''} position={hintPosition} />
                            </InfoTooltipContainer>
                        ))}
                </SettingLayoutLabel>
            </GridLabelContainer>
            <Grid item xs={8} md={childrenMD}>
                {children}
            </Grid>

            {childrenBelow && expanded !== false && (
                <Grid item xs={12}>
                    {childrenBelow}
                </Grid>
            )}
        </Grid>
    );

    if (expanded === undefined) return inner;

    return <Expander expanded={expanded}>{inner}</Expander>;
}

const GridLabelContainer = styled(Grid)({
    paddingRight: 40,
    maxWidth: '240px !important',
    boxSizing: 'border-box',
    lineHeight: '16px',
});

const InfoTooltipContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    marginLeft: '6px',
});

function Expander({
    expanded,
    children,
}: React.PropsWithChildren<{
    expanded: boolean;
}>) {
    return (
        <DivExpanderRoot className={expanded ? 'expanded' : undefined}>{children}</DivExpanderRoot>
    );
}

const DivExpanderRoot = styled('div')(({ theme }) => ({
    transition: 'padding-top 0.1s, padding-bottom 0.1s, margin-top 0.1s, margin-bottom 0.1s',

    '&.expanded': {
        borderRadius: 12,
        border: `1px solid ${theme.palette.neutral[4]}`,
        padding: 22,
        margin: '0 -23px',
    },
}));

const SLabel = styled('div')<{ opaque: boolean }>(({ opaque }) => ({
    opacity: opaque ? '100%' : '40%',
    display: 'inline-block',
}));
