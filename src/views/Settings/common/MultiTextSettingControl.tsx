import { IconButton } from '@mui/material';
import { styled } from '@mui/system';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import ResettableTextFormField from 'common/components/Inputs/ResettableTextField';
import { useEffect, useState } from 'react';
import SettingLayoutControl from './SettingLayoutControl';

export interface MultiTextSettingControlProps {
    label: string;
    values: string[];
    onChange: (newValues: string[]) => void;
}

export function MultiTextSettingControl({
    label,
    values: valuesProp,
    onChange,
}: MultiTextSettingControlProps) {
    const [values, setValues] = useState<string[]>(valuesProp);
    const [canSave, setCanSave] = useState<boolean>(false);

    async function updateValue(newVal: string, index: number): Promise<void> {
        let newArray = [...values];
        newArray[index] = newVal;
        setValues(newArray);

        if (newVal.length === 0) {
            setCanSave(false);
        } else {
            setCanSave(true);
        }
    }

    useEffect(() => {
        if (canSave) {
            onChange(values);
            setCanSave(false);
        }
    }, [values, canSave]);

    return (
        <SettingLayoutControl label={label} alignItems={'center'}>
            {values.length === 0 ? (
                <IconButton
                    onClick={() => {
                        setValues([...values, '']);
                    }}
                    size="small"
                >
                    <PlusIcon />
                </IconButton>
            ) : (
                values.map((v, i) => (
                    <CheckboxTextList key={i}>
                        <ResettableTextFormField
                            value={v}
                            showValidationIndicators
                            showEditButton
                            required={true}
                            onSave={(newValue: string) => updateValue(newValue, i)}
                        />
                        {i === 0 ? (
                            <IconButton
                                onClick={() => {
                                    setValues([...values, '']);
                                }}
                                size="small"
                            >
                                <PlusIcon />
                            </IconButton>
                        ) : (
                            <IconButton
                                onClick={() => {
                                    let newValues = [...values];
                                    newValues.splice(i, 1);
                                    setValues(newValues);
                                    setCanSave(true);
                                }}
                                size="small"
                            >
                                <LessIcon />
                            </IconButton>
                        )}
                    </CheckboxTextList>
                ))
            )}
        </SettingLayoutControl>
    );
}

const CheckboxTextList = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'row',
    marginBottom: '10px',
}));
