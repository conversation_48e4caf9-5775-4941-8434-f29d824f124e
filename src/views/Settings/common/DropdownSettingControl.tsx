import { Tooltip, TooltipProps, styled, tooltipClasses } from '@mui/material';
import Grid, { GridProps } from '@mui/material/Grid';
import { InfoTooltipInternal } from 'common/components/InfoTooltip';
import Dropdown, { DropdownProps } from 'common/components/Inputs/Dropdown';

export type DropdownSettingControlProps = {
    label?: React.ReactNode;
    labelMD?: number;
    childrenMD?: number;
    hasHint?: boolean;
    hintText?: string;
    hintPosition?: TooltipProps['placement'];
} & DropdownProps;

export default function DropdownSettingControl({
    label,
    labelMD = 3,
    childrenMD = 1,
    hasHint,
    hintText,
    hintPosition = 'right',
    ...props
}: DropdownSettingControlProps) {
    return (
        <Grid item container sx={{ alignItems: 'center' }}>
            <GridLabelContainer item xs={4} md={labelMD}>
                <StyledLabel>
                    <div>{label}</div>
                    {hasHint && (
                        <SpanTooltipContainer>
                            <StyledTooltip title={hintText ?? ''} placement={hintPosition} arrow>
                                <InfoTooltipInternal />
                            </StyledTooltip>
                        </SpanTooltipContainer>
                    )}
                </StyledLabel>
            </GridLabelContainer>
            <StyledGrid item xs={8} md={childrenMD}>
                <Dropdown showDisabledStyles={true} {...props} />
            </StyledGrid>
        </Grid>
    );
}

const StyledLabel = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    wordBreak: 'break-word',
}));

const GridLabelContainer = styled(Grid)({
    maxWidth: '240px !important',
    boxSizing: 'border-box',
    lineHeight: '16px',
});

const SpanTooltipContainer = styled('span')({
    paddingRight: 20,
});

const StyledGrid = styled(({ ...props }: GridProps) => <Grid {...props} />)({
    minWidth: 170,
});

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.grey[100],
        color: theme.palette.neutral[7],
        filter: `drop-shadow(0px 4px 1px ${theme.palette.neutral[3]})`,
        padding: '6px 7px',
        maxWidth: 'none',
    },

    [`& .${tooltipClasses.arrow}::before`]: {
        backgroundColor: theme.palette.grey[100],
    },
}));
