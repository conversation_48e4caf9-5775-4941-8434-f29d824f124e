import { Box, IconButton, InputAdornment } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { Checkbox } from 'common/components/Inputs';

import { NumberFormField } from 'common/components/Inputs/NumberField';
import { Colors } from 'common/styles/Colors';
import { ChangeEvent, useCallback, useState } from 'react';
import { NumberFormatValues } from 'react-number-format';
import { NumberSettingControlProps } from './NumberSettingControl';
import SettingLayoutControl from './SettingLayoutControl';
import useSettingStyles from './styles';

type PropsOverride = {
    value: number | null;
    defaultValue?: number;
    onValueChange: (value: number | null) => void;
    validate?: (value: number | null) => boolean;
};

export type OptionalNumberSettingControlProps = Omit<
    NumberSettingControlProps,
    keyof PropsOverride
> &
    PropsOverride;

const useLocalStyles = makeStyles((theme) => ({
    checkbox: {
        margin: '4px 0',
    },
}));

/**
 * Like NumberSettingControl but has checkbox that determines wheter this field has a value or not,
 * if not the value is null.
 *
 * Notes:
 *   - When the checkbox is unchecked - value is set to null
 *   - When the checkbox is checked - value is only set to a number after
 *     "Save" icon is pressed.
 *
 * Known issues (more like inconveniences):
 *   -  if you check the checkbox and then uncheck it, onValueChange will be called with null even
 *      though it doesn't have to (value is unchanged and equal to null)
 */
export default function OptionalNumberSettingControl({
    label,
    value,
    onValueChange,
    defaultValue,
    validate,
    ...props
}: OptionalNumberSettingControlProps) {
    const [isEditing, setEditing] = useState(false);
    const [newValue, setNewValue] = useState<number | null>(null);
    // savedValue = value that was set before we unchecked the checkbox (and therefore set the value to null)
    const [savedValue, setSavedValue] = useState<number | undefined>(undefined);
    const styles = useSettingStyles();
    const localStyles = useLocalStyles();

    const displayValue = isEditing ? newValue : value;
    const isValid = validate ? validate(displayValue) : true;

    const startEditing = () => {
        setEditing(true);
        setNewValue(value);
    };

    const onIconClick = () => {
        if (isEditing) {
            if (!isValid) return;
            onValueChange(newValue);
            setEditing(false);
        } else {
            startEditing();
        }
    };

    const onCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            setNewValue(savedValue ?? defaultValue ?? 0);
            if (savedValue) {
                onValueChange(savedValue);
            } else {
                setEditing(true);
            }
        } else {
            setEditing(false);
            setNewValue(null);
            // just do type assertion?
            if (value !== null) setSavedValue(value);
            onValueChange(null);
        }
    };

    const fieldOnValueChanged = useCallback(
        (value: NumberFormatValues) => {
            if (!isEditing) return;
            setNewValue(value.floatValue ?? defaultValue ?? 0);
        },
        [isEditing, defaultValue]
    );

    const cancelEditing = useCallback(() => {
        setEditing(false);
    }, []);

    return (
        <SettingLayoutControl label={label}>
            <Box display="flex" alignItems="center">
                <Checkbox
                    className={`${styles.checkbox} ${localStyles.checkbox}`}
                    checked={displayValue !== null}
                    onChange={onCheckboxChange}
                />
                {displayValue !== null && (
                    <Box display="flex" alignItems="center" marginLeft={3}>
                        <Box>
                            <NumberFormField
                                onClick={() => !isEditing && startEditing()}
                                classes={{
                                    root: isEditing ? undefined : styles.lockedResettableField,
                                }}
                                isInvalid={!(validate ? validate(displayValue) : undefined)}
                                value={displayValue}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                onClick={onIconClick}
                                                sx={{ marginRight: -1.5 }}
                                            >
                                                {isEditing ? <CheckIcon /> : <EditIcon />}
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                    readOnly: !isEditing,
                                }}
                                onValueChange={fieldOnValueChanged}
                                {...props}
                            />
                        </Box>
                        {isEditing && (
                            <IconButton size="small" onClick={cancelEditing}>
                                <CloseIcon fill={Colors.Neutral6} />
                            </IconButton>
                        )}
                    </Box>
                )}
            </Box>
        </SettingLayoutControl>
    );
}
