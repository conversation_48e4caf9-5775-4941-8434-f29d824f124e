import { styled } from '@mui/material';
import ResettableTextFormField from 'common/components/Inputs/ResettableTextField';
import { TextFieldProps } from 'common/components/mui';
import { CSSProperties, ChangeEvent, useCallback, useState } from 'react';
import SettingLayoutControl from './SettingLayoutControl';

type SettingsTextFieldProps = Omit<TextFieldProps, 'IconRight' | 'onIconRightClick' | 'value'> & {
    value: string;
    onSave: (value: string) => Promise<void>;
    placeholder?: string;
    name: string;
    required?: boolean;
    hasCharacterCounter?: boolean;
    alignItems?: CSSProperties['alignItems'];
    isInvalid?: boolean;
    showValidationIndicators?: boolean;
    maxLength?: number;
    onChange?: TextFieldProps['onChange'];
    onEnterPress?: React.KeyboardEventHandler;
    onEscPress?: React.KeyboardEventHandler;
    disabled?: boolean;
    childrenMD?: number;
    hasHint?: boolean;
    hintText?: string;
};

const StyledResettableTextFieldContainer = styled('div')(({ theme }) => ({
    [theme.breakpoints.down('md')]: {
        width: '100%',
    },
}));

/**
 * Single-line text setting control. Allows to edit a value by clicking "edit" icon and then either confirm new value (which
 * will trigger onSave callback) or reject it, leaving value unchanging.
 */
export default function TextSettingControl({
    label,
    required,
    alignItems = 'center',
    childrenMD = 5,
    hasHint,
    hintText,
    disabled,
    ...props
}: SettingsTextFieldProps) {
    return (
        <SettingLayoutControl
            label={label}
            alignItems={alignItems}
            childrenMD={childrenMD}
            hasHint={hasHint}
            hintText={hintText}
        >
            <StyledResettableTextFieldContainer>
                <ResettableTextFormField
                    required={required}
                    disabled={disabled}
                    {...props}
                    showEditButton
                />
            </StyledResettableTextFieldContainer>
        </SettingLayoutControl>
    );
}

interface ValidatableSettingsTextFieldProps<T = any> extends SettingsTextFieldProps {
    object: T;
    onValidate: (newVal: string, object: T) => boolean;
}

export function ValidatableTextSettingControl({
    label,
    required,
    hasCharacterCounter,
    alignItems = 'center',
    object,
    onValidate,
    ...props
}: ValidatableSettingsTextFieldProps) {
    const [isInvalid, setIsInValid] = useState<boolean>(false);

    const onChangeCallback = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            const isInvalid = onValidate(e.target.value, object);
            setIsInValid(isInvalid);
        },
        [object, onValidate]
    );

    return (
        <SettingLayoutControl label={label} alignItems={alignItems}>
            <StyledResettableTextFieldContainer>
                <ResettableTextFormField
                    required={required}
                    isInvalid={isInvalid}
                    hasCharacterCounter={hasCharacterCounter}
                    showValidationIndicators
                    showEditButton
                    onChange={onChangeCallback}
                    {...props}
                />
            </StyledResettableTextFieldContainer>
        </SettingLayoutControl>
    );
}
