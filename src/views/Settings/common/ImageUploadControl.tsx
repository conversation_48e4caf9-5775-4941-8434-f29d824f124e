import { Grid } from '@mui/material';
import { SUPPORTED_IMAGE_EXTENSIONS } from 'api/settings';
import Note from 'common/components/Note';
import { UploadImage } from 'common/components/Upload';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import Image from './Image';
import useSettingStyles from './styles';

export type ImageUploadControlProps = {
    note?: string | ReactNode;
    src?: string;
    onFileSelected?: (file: File | null) => void;
    onFileUpload?: (file: File) => Promise<void>;
    onFileDelete?: () => void;
    disabled?: boolean;
    showInfoIcon?: boolean;
};

/**
 * Image upload control for the settings UI.
 *
 * Props' notes:
 *  - Delete button is only shown when onFileDelete is set.
 *
 * @param {ImageUploadControlProps} params Component paramters.
 */
export default function ImageUploadControl({
    note,
    onFileSelected,
    onFileUpload,
    onFileDelete,
    src,
    disabled,
    showInfoIcon,
}: ImageUploadControlProps) {
    const { t } = useAppTranslation();
    const [file, setFile] = useState<File | null>(null);
    const [selectedImageSrc, setSelectedImageSrc] = useState<string>();
    const styles = useSettingStyles();
    const [isUploading, setUploading] = useState(false);

    // this can throw an error if file is read as data-url after
    // this component is unloaded
    const readAsDataUrl = (file: File) => {
        if (file === null) return;
        const reader = new FileReader();
        reader.onload = () => {
            setSelectedImageSrc(reader.result as string);
        };
        reader.readAsDataURL(file);
    };

    useEffect(() => {
        if (file) {
            readAsDataUrl(file);
        } else {
            setSelectedImageSrc(undefined);
        }
    }, [file]);

    const onFileReady = useCallback(
        async (file: File | null) => {
            if (onFileSelected) onFileSelected(file ?? null);
            if (file && onFileUpload) {
                setFile(null);
                setUploading(true);
                try {
                    await onFileUpload(file);
                } finally {
                    setUploading(false);
                }
            }
        },
        [onFileSelected, onFileUpload]
    );

    const onFileDeleteCallback = useCallback(() => {
        if (!onFileDelete) return;

        onFileDelete();
        setFile(null); // NOTE: input[type=file] still has value (maybe fix that? is it a problem?)
    }, [onFileDelete]);

    return (
        <Grid container alignItems="flex-start">
            <Grid item xs={3} className={styles.labelContainer}>
                <Image
                    noImageText={t('commonLabels.upload.preview')}
                    src={selectedImageSrc ?? src}
                    height={100}
                />
            </Grid>
            <Grid container item xs={9} style={{ padding: 10, gap: 10 }}>
                <UploadImage
                    disabled={disabled}
                    isFileUploaded={!!src}
                    accept={SUPPORTED_IMAGE_EXTENSIONS.map((e) => '.' + e).join(',')}
                    file={file}
                    deleteDisabled={!src}
                    onDelete={onFileDeleteCallback}
                    onFileReady={onFileReady}
                    isUploading={isUploading}
                />
                {note && <Note showInfoIcon={showInfoIcon}>{note}</Note>}
            </Grid>
        </Grid>
    );
}
