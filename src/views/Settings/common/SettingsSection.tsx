import { Box } from '@mui/material';
import InfoTooltip from 'common/components/InfoTooltip';
import { CSSProperties } from 'react';
import { ReactElementType } from 'react-window';
import useSettingStyles from './styles';

export interface SettingsSectionProps {
    children?: React.ReactNode;
    label?: string;
    tooltipText?: string;
    tooltipPosition?: string;
    styles?: CSSProperties;
    CustomTitle?: ReactElementType;
}

export default function SettingsSection({
    children,
    label,
    tooltipText,
    tooltipPosition: tooltipPlacement,
    styles,
    CustomTitle,
}: SettingsSectionProps) {
    const settingsStyles = useSettingStyles();

    return (
        <div className={settingsStyles.section} style={styles}>
            {label && (
                <Box display="flex">
                    {CustomTitle ? (
                        <CustomTitle className={settingsStyles.sectionTitle}>{label}</CustomTitle>
                    ) : (
                        <div className={settingsStyles.sectionTitle}>{label}</div>
                    )}
                    {tooltipText ? (
                        <InfoTooltip text={tooltipText!} position={tooltipPlacement} />
                    ) : null}
                </Box>
            )}
            {children}
        </div>
    );
}
