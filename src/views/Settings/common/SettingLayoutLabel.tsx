import clsx from 'clsx';
import React from 'react';
import useStyles from './styles';

const SettingLayoutLabel = React.forwardRef<
    HTMLLabelElement,
    React.LabelHTMLAttributes<HTMLLabelElement>
>(({ className, ...props }: React.LabelHTMLAttributes<HTMLLabelElement>, ref) => {
    const styles = useStyles();
    return <label ref={ref} className={clsx(styles.label, className)} {...props} />;
});

export default SettingLayoutLabel;
