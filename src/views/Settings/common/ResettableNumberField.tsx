import { Box, IconButton, InputAdornment } from '@mui/material';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { NumberFormField, NumberFormFieldProps } from 'common/components/Inputs/NumberField';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';
import useSettingStyles from './styles';

type PropsOverride = {
    onValueChange: (value: number) => void;
    placeholder?: string;
    validate?: (v: number) => boolean;
    value: number;
    saveIfInvalid?: boolean;
    editIfDisabled?: boolean;
    onInvalidValueChange?: (value: number) => void;
};

export type ResettableNumberFieldProps = Omit<
    NumberFormFieldProps,
    'onChange' | 'isInvalid' | 'defaultValue' | 'endAdornment' | keyof PropsOverride
> &
    PropsOverride;

export default function ResettableNumberField({
    value,
    onValueChange,
    className,
    validate,
    saveIfInvalid,
    editIfDisabled,
    onInvalidValueChange,
    ...props
}: ResettableNumberFieldProps) {
    const [isEditing, setEditing] = useState(false);
    const [newValue, setNewValue] = useState<number | undefined>(0);
    const [valid, setValid] = useState(true);
    const styles = useSettingStyles();
    const disabled = props.disabled || (!saveIfInvalid && !valid && isEditing);

    const onIconClick = useCallback(() => {
        if (isEditing) {
            if (valid || saveIfInvalid) {
                onValueChange(newValue ?? 0);
                setEditing(false);
            }
        } else {
            setEditing(true);
            setNewValue(value);
        }
    }, [isEditing, newValue, onValueChange, value, saveIfInvalid, valid]);

    const cancelCallback = useCallback(() => {
        setEditing(false);
    }, []);

    const onClick = useCallback(() => {
        if (disabled) return;

        if (!isEditing) {
            setEditing(true);
            setNewValue(value);
        }
    }, [isEditing, value, disabled]);

    useEffect(() => {
        if (!editIfDisabled && disabled) setEditing(false);
    }, [editIfDisabled, disabled]);

    useEffect(() => {
        const isValid = validate ? validate(isEditing ? newValue ?? 0 : value) : true;
        if (isValid !== valid) {
            setValid(isValid);
        }
    }, [validate, valid, isEditing, newValue, value]);

    useEffect(() => {
        if (onInvalidValueChange && !valid) {
            onInvalidValueChange(isEditing ? newValue ?? 0 : value);
        }
    }, [onInvalidValueChange, valid]);

    return (
        <Box className={className} alignItems="center" display="flex">
            <Box flexGrow={1}>
                <NumberFormField
                    isInvalid={!valid}
                    classes={{ root: isEditing ? undefined : styles.lockedResettableField }}
                    onClick={onClick}
                    readonly={!isEditing}
                    value={isEditing ? newValue : value}
                    endAdornment={
                        <InputAdornment position="end">
                            <IconButton disabled={disabled} onClick={onIconClick} size="small">
                                {isEditing ? (
                                    <CheckIcon />
                                ) : (
                                    <EditIcon fill={disabled ? 'var(--cm4)' : 'var(--cm2)'} />
                                )}
                            </IconButton>
                        </InputAdornment>
                    }
                    onValueChange={(v) => setNewValue(v.floatValue)}
                    {...props}
                />
            </Box>
            {isEditing && (
                <IconButton size="small" onClick={cancelCallback}>
                    <CloseIcon fill={Colors.Neutral6} />
                </IconButton>
            )}
        </Box>
    );
}
