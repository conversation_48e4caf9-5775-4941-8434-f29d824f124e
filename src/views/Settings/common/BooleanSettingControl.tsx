import React, { useCallback } from 'react';
import SettingsControl, { SettingLayoutControlProps } from './SettingLayoutControl';
import SettingsCheckbox from './SettingsCheckbox';

type SettingsCheckboxFieldProps = {
    value: boolean;
    onChange: (value: boolean) => void;
    label?: string;
    name?: string;
    disabled?: boolean;
    hasHint?: boolean;
    hintText?: string;
    hintPosition?: string;
    customHint?: React.ReactNode;

    slotProps?: {
        layout?: Partial<SettingLayoutControlProps>;
    };
};
/**
 * Simple checkbox control for settings. Basically just a wrapper around a Checkbox component
 * that adds a label.
 * TODO (MB) rename to BooleanSettingControl
 */
const SettingCheckboxControl = React.memo(
    ({
        label,
        value,
        hasHint,
        hintText,
        hintPosition,
        customHint,
        onChange,
        name,
        disabled,
        slotProps = {},
    }: SettingsCheckboxFieldProps) => {
        const onChangeCallback = useCallback(
            (_: React.ChangeEvent<HTMLInputElement>, checked: boolean) => onChange(checked),
            [onChange]
        );

        return (
            <SettingsControl
                disabled={disabled}
                label={label}
                hasHint={hasHint}
                hintText={hintText}
                hintPosition={hintPosition}
                customHint={customHint}
                alignItems="center"
                {...slotProps.layout}
            >
                <SettingsCheckbox
                    disabled={disabled}
                    name={name}
                    onChange={onChangeCallback}
                    checked={value}
                />
            </SettingsControl>
        );
    }
);
SettingCheckboxControl.displayName = 'SettingCheckboxControl';

export default SettingCheckboxControl;
