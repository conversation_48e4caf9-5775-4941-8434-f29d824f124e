import { styled } from '@mui/material';
import ResettableNumberField, { ResettableNumberFieldProps } from './ResettableNumberField';
import SettingLayoutControl from './SettingLayoutControl';

export type NumberSettingControlProps = {
    label?: string;
    labelMD?: number;
    childrenMD?: number;
    alignItems?: string;
} & ResettableNumberFieldProps;

export default function NumberSettingControl({
    label,
    labelMD,
    childrenMD,
    alignItems,
    ...props
}: NumberSettingControlProps) {
    return (
        <SettingLayoutControl
            disabled={props.disabled}
            label={label}
            labelMD={labelMD}
            childrenMD={childrenMD}
            alignItems={alignItems}
        >
            <StyledField {...props} />
        </SettingLayoutControl>
    );
}

const StyledField = styled(({ ...props }: ResettableNumberFieldProps) => (
    <ResettableNumberField {...props} />
))({
    minWidth: 170,
});
