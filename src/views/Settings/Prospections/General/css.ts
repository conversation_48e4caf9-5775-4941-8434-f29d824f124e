import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        background: theme.palette.neutral[1],
        borderRadius: 12,
        border: `1px solid #DBDCDD`,
        width: '100%',
        minHeight: 745,
        padding: '40px 46px',
    },
    title: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[9],
    },
    weekDay: {
        display: 'flex',
    },
    checkBoxLabel: {
        display: 'flex',
        alignItems: 'center',
        marginLeft: 9,
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[6],
        '&$checkedLabel': {
            fontWeight: 'bold',
            color: theme.palette.primary.main,
        },
    },
    checkedLabel: {},
    timePickerLabel: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        marginBottom: 7,
    },
    exampleLabel: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[6],
    },
    inputDays: {
        width: 280,
    },
}));

export default useStyles;
