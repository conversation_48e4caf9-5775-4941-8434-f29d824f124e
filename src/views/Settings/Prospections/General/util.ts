import {
    ProspectionGeneralSettingDto,
    SchedulePolicyDto,
    UpdateProspectionGeneralSettingDto,
    UpdateSchedulePolicyDto,
} from 'api/Prospections/ProspectionGeneral';

export function getOptimisticUpdate(
    data: UpdateProspectionGeneralSettingDto
): (settings: ProspectionGeneralSettingDto) => ProspectionGeneralSettingDto {
    return (settings) => {
        const newValue: ProspectionGeneralSettingDto = {
            enableFollowUps: data.enableFollowUps ?? settings.enableFollowUps,
            unapprovedItemWhatsAppFollowUpDays:
                data.unapprovedItemWhatsAppFollowUpDays ??
                settings.unapprovedItemWhatsAppFollowUpDays,
            schedulePolicy: data.schedulePolicy
                ? updateSchedulePolicy(settings.schedulePolicy, data.schedulePolicy)
                : settings.schedulePolicy,
        };

        return newValue;
    };
}
function updateSchedulePolicy(
    sp: SchedulePolicyDto,
    update: UpdateSchedulePolicyDto
): SchedulePolicyDto {
    return {
        monday: update.monday ?? sp.monday,
        tuesday: update.tuesday ?? sp.tuesday,
        wednesday: update.wednesday ?? sp.wednesday,
        thursday: update.thursday ?? sp.thursday,
        friday: update.friday ?? sp.friday,
        saturday: update.saturday ?? sp.saturday,
        sunday: update.sunday ?? sp.sunday,
        from: update.from ?? sp.from,
        to: update.to ?? sp.to,
    };
}
