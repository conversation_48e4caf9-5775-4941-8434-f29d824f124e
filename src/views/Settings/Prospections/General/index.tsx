import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import {
    ProspectionGeneralSettingDto,
    ProspectionGeneralSettingsApi,
    UpdateProspectionGeneralSettingDto,
} from 'api/Prospections/ProspectionGeneral';
import clsx from 'clsx';
import { Checkbox, TextField } from 'common/components/Inputs';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { SettingsSection } from 'views/Settings/common';
import SettingCheckboxControl from 'views/Settings/common/BooleanSettingControl';
import useStyles from './css';
import ProspectionGeneralSettingsProps, { connector } from './props';
import { getOptimisticUpdate } from './util';

enum DayOfWeek {
    Monday = 1,
    Tuesday,
    Wednesday,
    Thursday,
    Friday,
    Saturday,
    Sunday,
}

const ProspectionGeneralSettings = ({ setNewToaster }: ProspectionGeneralSettingsProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const [generalSettings, setGeneralSettings] = useState<ProspectionGeneralSettingDto>({
        unapprovedItemWhatsAppFollowUpDays: 15,
        schedulePolicy: {
            monday: true,
            tuesday: true,
            wednesday: true,
            thursday: true,
            friday: true,
            saturday: false,
            sunday: false,
            from: '09:00:00',
            to: '18:00:00',
        },
        enableFollowUps: true,
    });
    const [currentDays, setCurrentDays] = useState<number>(0);
    const [type, setType] = useState<'number' | 'text'>('text');

    const fetchGeneralSettings = useCallback(async () => {
        const response = await callApi(
            () => ProspectionGeneralSettingsApi.getProspectionGeneralSettings(),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }
        );

        if (response) {
            setGeneralSettings(response);

            if (response.unapprovedItemWhatsAppFollowUpDays) {
                setCurrentDays(response.unapprovedItemWhatsAppFollowUpDays);
            }
        }
    }, [callApi, t]);

    const getTime = (timeString: string): [number, number] => {
        const time = timeString.split(':');
        return [+time[0], +time[1]];
    };

    const updateGeneralSettings = async (
        data: UpdateProspectionGeneralSettingDto
    ): Promise<ProspectionGeneralSettingDto> => {
        // optimistic update
        setGeneralSettings(getOptimisticUpdate(data));
        const response = await callApi(() => ProspectionGeneralSettingsApi.update(data), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        if (response) {
            setNewToaster(
                new NotificationData(
                    t('settings.prospections.general.theConfigurationWasSuccessfullyUpdated'),
                    t('settings.prospections.general.updatedConfiguration'),
                    NotificationType.success
                )
            );
        }

        return response;
    };

    const handleDayChange = async (dayOfWeek: DayOfWeek) => {
        const [isValid, daysValid] = atLeastOneDay;
        let response: ProspectionGeneralSettingDto | undefined;
        let showAlert = false;
        switch (dayOfWeek) {
            case DayOfWeek.Monday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.monday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { monday: false },
                    });
                } else if (!generalSettings.schedulePolicy.monday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { monday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
            case DayOfWeek.Tuesday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.tuesday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { tuesday: false },
                    });
                } else if (!generalSettings.schedulePolicy.tuesday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { tuesday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
            case DayOfWeek.Wednesday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.wednesday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { wednesday: false },
                    });
                } else if (!generalSettings.schedulePolicy.wednesday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { wednesday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
            case DayOfWeek.Thursday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.thursday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { thursday: false },
                    });
                } else if (!generalSettings.schedulePolicy.thursday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { thursday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
            case DayOfWeek.Friday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.friday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { friday: false },
                    });
                } else if (!generalSettings.schedulePolicy.friday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { friday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
            case DayOfWeek.Saturday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.saturday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { saturday: false },
                    });
                } else if (!generalSettings.schedulePolicy.saturday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { saturday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
            case DayOfWeek.Sunday:
                if (isValid && daysValid > 1 && generalSettings.schedulePolicy.sunday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { sunday: false },
                    });
                } else if (!generalSettings.schedulePolicy.sunday) {
                    response = await updateGeneralSettings({
                        schedulePolicy: { sunday: true },
                    });
                } else {
                    showAlert = true;
                }
                break;
        }
        if (showAlert) {
            setNewToaster(
                new NotificationData(
                    t(
                        'settings.prospections.general.notAllDaysOfTheWeekCanBeDeselectedPleaseSelectAtLeastOneDay'
                    ),
                    t('settings.prospections.general.deselectedDaysOfTheWeek'),
                    NotificationType.warning
                )
            );
        }

        if (response) {
            setGeneralSettings(response);
        }
    };

    const handleTimeChange =
        (direction: 'startTime' | 'endTime') => async (value: [number, number]) => {
            let response: ProspectionGeneralSettingDto | undefined;
            const startTime = generalSettings.schedulePolicy.from.split(':').map((v) => +v);
            const endTime = generalSettings.schedulePolicy.to.split(':').map((v) => +v);

            if (direction === 'startTime') {
                if (value[0] === startTime[0] && value[1] === startTime[1]) {
                    return;
                }

                if (
                    (value[0] === endTime[0] && value[1] === endTime[1]) ||
                    value[0] > endTime[0] ||
                    (value[0] === endTime[0] && value[1] > endTime[1])
                ) {
                    setNewToaster(
                        new NotificationData(
                            t(
                                'settings.prospections.general.theEndTimeCannotBeTheSameAsTheStartTimePleaseSelectAnotherEndTime'
                            ),
                            t('settings.prospections.general.theEndTimeIsEqualToTheStartTime'),
                            NotificationType.warning
                        )
                    );
                    return;
                }

                response = await updateGeneralSettings({
                    schedulePolicy: { from: `${value[0]}:${value[1]}:00` },
                });
            } else {
                if (value[0] === endTime[0] && value[1] === endTime[1]) {
                    return;
                }

                if (
                    (value[0] === startTime[0] && value[1] === startTime[1]) ||
                    value[0] < startTime[0] ||
                    (value[0] === startTime[0] && value[1] < startTime[1])
                ) {
                    setNewToaster(
                        new NotificationData(
                            t(
                                'settings.prospections.general.theEndTimeCannotBeTheSameAsTheStartTimePleaseSelectAnotherEndTime'
                            ),
                            t('settings.prospections.general.theEndTimeIsEqualToTheStartTime'),
                            NotificationType.warning
                        )
                    );
                    return;
                }

                response = await updateGeneralSettings({
                    schedulePolicy: { to: `${value[0]}:${value[1]}:00` },
                });
            }

            if (response) {
                setGeneralSettings(response);
            }
        };

    const handleDaysToSendWhatsAppChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const days = +event.target.value;
        if (days < 1000) {
            setGeneralSettings((old) => ({
                ...old,
                unapprovedItemWhatsAppFollowUpDays: days !== 0 ? days : undefined,
            }));
        }
    };

    const handleDaysToSendWhatsAppBlur = async () => {
        if (!generalSettings.unapprovedItemWhatsAppFollowUpDays) {
            setGeneralSettings((old) => ({
                ...old,
                unapprovedItemWhatsAppFollowUpDays: currentDays,
            }));
            setType('text');
            return;
        }

        if (
            generalSettings.unapprovedItemWhatsAppFollowUpDays &&
            generalSettings.unapprovedItemWhatsAppFollowUpDays > 0 &&
            generalSettings.unapprovedItemWhatsAppFollowUpDays < 1000
        ) {
            const response = await updateGeneralSettings({
                unapprovedItemWhatsAppFollowUpDays:
                    generalSettings.unapprovedItemWhatsAppFollowUpDays,
            });
            if (response && response.unapprovedItemWhatsAppFollowUpDays) {
                setGeneralSettings(response);
                setCurrentDays(response.unapprovedItemWhatsAppFollowUpDays);
            }
        }

        setType('text');
    };

    const atLeastOneDay: [boolean, number] = useMemo(() => {
        let validDays: number = 0;
        const valid =
            generalSettings.schedulePolicy.monday ||
            generalSettings.schedulePolicy.tuesday ||
            generalSettings.schedulePolicy.wednesday ||
            generalSettings.schedulePolicy.thursday ||
            generalSettings.schedulePolicy.friday ||
            generalSettings.schedulePolicy.saturday ||
            generalSettings.schedulePolicy.sunday;

        if (generalSettings.schedulePolicy.monday) {
            validDays += 1;
        }

        if (generalSettings.schedulePolicy.tuesday) {
            validDays += 1;
        }

        if (generalSettings.schedulePolicy.wednesday) {
            validDays += 1;
        }

        if (generalSettings.schedulePolicy.thursday) {
            validDays += 1;
        }

        if (generalSettings.schedulePolicy.friday) {
            validDays += 1;
        }

        if (generalSettings.schedulePolicy.saturday) {
            validDays += 1;
        }

        if (generalSettings.schedulePolicy.sunday) {
            validDays += 1;
        }

        return [valid, validDays];
    }, [generalSettings]);

    useEffect(() => {
        fetchGeneralSettings();
    }, [fetchGeneralSettings]);

    return (
        <Box style={{ width: '100%', height: '100%', paddingBottom: 68 }}>
            <Grid container className={classes.root}>
                <Grid item xs={12}>
                    <Typography variant="h5">
                        {t('settings.prospections.general.sendingProspectionMessages')}
                    </Typography>
                    <Box style={{ marginTop: 30, display: 'flex' }}>
                        <Box style={{ display: 'flex' }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.monday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Monday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.monday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.monday')}
                            </Typography>
                        </Box>
                        <Box style={{ display: 'flex', marginLeft: 29 }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.tuesday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Tuesday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.tuesday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.tuesday')}
                            </Typography>
                        </Box>
                        <Box style={{ display: 'flex', marginLeft: 29 }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.wednesday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Wednesday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.wednesday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.wednesday')}
                            </Typography>
                        </Box>
                        <Box style={{ display: 'flex', marginLeft: 29 }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.thursday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Thursday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.thursday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.thursday')}
                            </Typography>
                        </Box>
                        <Box style={{ display: 'flex', marginLeft: 29 }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.friday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Friday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.friday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.friday')}
                            </Typography>
                        </Box>
                        <Box style={{ display: 'flex', marginLeft: 29 }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.saturday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Saturday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.saturday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.saturday')}
                            </Typography>
                        </Box>
                        <Box style={{ display: 'flex', marginLeft: 29 }}>
                            <Checkbox
                                style={{ padding: 0 }}
                                checked={generalSettings.schedulePolicy.sunday}
                                onChange={() => {
                                    handleDayChange(DayOfWeek.Sunday);
                                }}
                            />
                            <Typography
                                className={clsx(
                                    classes.checkBoxLabel,
                                    generalSettings.schedulePolicy.sunday && classes.checkedLabel
                                )}
                            >
                                {t('settings.prospections.general.sunday')}
                            </Typography>
                        </Box>
                    </Box>
                    <Grid container style={{ marginTop: 20 }}>
                        <Grid item xs={4}>
                            <Grid container spacing={3}>
                                <Grid item xs={6}>
                                    <Typography className={classes.timePickerLabel}>
                                        {t('settings.prospections.general.startTime')}{' '}
                                        <span>*</span>
                                    </Typography>
                                    <TimeFormField
                                        name="start-time"
                                        isRequired
                                        // disableTextEdition
                                        showValidationIndicators
                                        value={getTime(generalSettings.schedulePolicy.from)}
                                        onChange={handleTimeChange('startTime')}
                                    />
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography className={classes.timePickerLabel}>
                                        {t('settings.prospections.general.endTime')}{' '}
                                        <span style={{ color: Colors.CM1 }}>*</span>
                                    </Typography>
                                    <TimeFormField
                                        name="end-time"
                                        isRequired
                                        // disableTextEdition
                                        showValidationIndicators
                                        value={getTime(generalSettings.schedulePolicy.to)}
                                        onChange={handleTimeChange('endTime')}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid container style={{ marginTop: 25, display: 'block' }}>
                        <Typography className={classes.exampleLabel}>
                            {t('settings.prospections.general.weSuggestThatMessageSendingSchedule')}
                        </Typography>
                        <Typography className={classes.exampleLabel}>
                            <Trans
                                i18nKey="settings.prospections.general.exampleMondayToFridayFromTo"
                                t={t}
                            />
                        </Typography>
                    </Grid>
                    <div
                        style={{
                            marginTop: 50,
                            marginBottom: 30,
                            width: '100%',
                            borderBottom: `1px solid rgba(0, 0, 0, 0.1)`,
                        }}
                    />
                    <SettingsSection
                        label={t(
                            'settings.prospections.general.prospectionOfUnapprovedInspectionItems'
                        )}
                    >
                        <SettingCheckboxControl
                            label={t('settings.prospections.general.enableFollowUps')}
                            value={generalSettings.enableFollowUps}
                            onChange={(v) => updateGeneralSettings({ enableFollowUps: v })}
                        />
                        <Box style={{ width: 280 }}>
                            <TextField
                                label={t(
                                    'settings.prospections.general.daysToSendWhatsAppFollowUp'
                                )}
                                name="days-to-send-whatsapp-follow-up"
                                isRequired
                                showValidationIndicators
                                type={type}
                                value={
                                    type === 'text'
                                        ? `${
                                              generalSettings.unapprovedItemWhatsAppFollowUpDays ||
                                              ''
                                          } ${t(
                                              `settings.prospections.general.${
                                                  generalSettings.unapprovedItemWhatsAppFollowUpDays ===
                                                  1
                                                      ? 'day'
                                                      : 'days'
                                              }`
                                          )}`
                                        : `${
                                              generalSettings.unapprovedItemWhatsAppFollowUpDays ||
                                              ''
                                          }`
                                }
                                onFocus={() => {
                                    setType('number');
                                }}
                                onChange={handleDaysToSendWhatsAppChange}
                                onBlur={handleDaysToSendWhatsAppBlur}
                            />
                        </Box>
                    </SettingsSection>
                </Grid>
            </Grid>
        </Box>
    );
};

export default connector(ProspectionGeneralSettings);
