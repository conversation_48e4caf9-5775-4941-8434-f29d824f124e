import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    navTab: {
        gap: 40,
    },
    navTabButton: {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[7],
        textDecoration: 'none',
        cursor: 'pointer',
        '&:hover': {
            paddingBottom: 6,
            borderBottom: `2px solid ${theme.palette.neutral[7]}`,
        },
    },
    navTabButtonActive: {
        color: `${theme.palette.primary.main}!important`,
        paddingBottom: 6,
        borderBottom: `2px solid ${theme.palette.primary.main}!important`,
    },
    container: {
        display: 'flex',
        position: 'relative',
        justifyContent: 'flex-start',
        alignItems: 'center',
        flexDirection: 'column',
    },
    outerGrid: {
        flexGrow: 1,
        height: 0,
    },
    innerGrid: {
        height: '100%',
    },
}));

export default useStyles;
