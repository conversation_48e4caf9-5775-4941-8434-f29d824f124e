import { RouterTab } from 'common/components/tabs/RouterParameterBasedTabs';
import { ROUTES, SETTINGS_PROSPECTION_TABS } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { FeaturesGate } from 'views/Components/FeaturesGate';
import SettingsRouterParameterBasedTabs from '../common/SettingsRouterParameterBasedTabs';
import General from './General';
import MaintenanceProspection from './MaintenanceProspection';

const ProspectionsSettings = () => {
    const { t } = useAppTranslation();
    useDocumentTitle(t('settings.prospections.tabs.prospectionSettings'));
    const settings = useSelector(selectSettings);

    const tabs: RouterTab[] = useMemo(
        () => [
            {
                value: SETTINGS_PROSPECTION_TABS.GENERAL,
                label: t('settings.prospections.tabs.general'),
                content: <General />,
                disabled: !settings.repairShopSettings?.features.enableAutoProspection,
                disabledTooltipTitle: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled',
            },
            {
                value: SETTINGS_PROSPECTION_TABS.MAINTENANCE_PROSPECTION,
                label: t('settings.prospections.tabs.maintenanceProspection'),
                content: <MaintenanceProspection />,
                disabled: !settings.repairShopSettings?.features.enableAutoProspection,
                disabledTooltipTitle: 'titles.yourAccountDoesNotHaveThisFunctionalityEnabled',
            },
            // TODO: Uncomment when will be ready
            // {
            //     value: SETTINGS_PROSPECTION_TABS.PROSPECTION_EXCEPTION,
            //     label: 'settings.prospections.tabs.prospectionException',
            //     component: <ProspectionExceptionsSettings />,
            // },
            // {
            //     value: SETTINGS_PROSPECTION_TABS.CALL_RESULTS,
            //     label: 'settings.prospections.tabs.callResults',
            //     component: <CallResultsSettings />,
            // },
        ],
        [settings, t]
    );

    return (
        <FeaturesGate predicate={'enableAutoProspection'}>
            <SettingsRouterParameterBasedTabs
                urlPattern={ROUTES.SETTINGS.PROSPECTIONS.PATH}
                parameterName="section"
                tabs={tabs}
            />
        </FeaturesGate>
    );
};

export default ProspectionsSettings;
