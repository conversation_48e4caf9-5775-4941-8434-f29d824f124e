import { InputAdornment, ListItem } from '@mui/material';
import { AutocompleteRenderInputParams } from '@mui/material/Autocomplete';
import { useQuery } from '@tanstack/react-query';
import MaintenanceProspectionAPI, {
    MaintenanceOrderType,
    MaintenanceProspectionItemDto,
} from 'api/Prospections/MaintenanceProspection';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { TextField } from 'common/components/mui';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';

type MaintenanceProspectionAutocompleteProps = {
    value?: MaintenanceProspectionItemDto;
    onSelect: (maintenanceProspection: MaintenanceProspectionItemDto) => void;
    onInputChange?: (value: string) => void;
};

const MaintenanceProspectionAutocomplete = ({
    value,
    onSelect,
    onInputChange,
}: MaintenanceProspectionAutocompleteProps) => {
    const { t } = useAppTranslation();
    // const { callApi } = useApiCall();
    const [open, setOpen] = useState(false);
    const [[searchQuery, doSearch], setSearchQuery] = useState(['', false]);
    const normalizedQuery = useMemo(() => searchQuery.trim(), [searchQuery]);
    const [debouncedQuery] = useDebounce(normalizedQuery, 700);

    const brands = useCallback((item: MaintenanceProspectionItemDto) => {
        const brand = item.ruleBrands?.map((b) => b.brand).join(', ') || '--';
        return brand.length > 15 ? brand.substring(0, 15) + '...' : brand;
    }, []);

    const models = useCallback((item: MaintenanceProspectionItemDto) => {
        const model =
            item.ruleBrands
                .map((b) => b.ruleModels)
                .flat(1)
                .map((m) => m?.model)
                .join(', ') || '--';
        return model.length > 15 ? model.substring(0, 15) + '...' : model;
    }, []);

    const years = useCallback((item: MaintenanceProspectionItemDto) => {
        const year = item.years.join(', ') || '--';
        return year.length > 15 ? year.substring(0, 15) + '...' : year;
    }, []);

    const { data: maintenanceProspections } = useQuery({
        queryKey: ['prospection', 'rulesSearch', debouncedQuery],
        queryFn: () =>
            MaintenanceProspectionAPI.getMaintenanceProspectionsPaginated(
                1,
                10,
                MaintenanceOrderType.MileageAscending,
                undefined,
                debouncedQuery
            ),
        enabled: debouncedQuery.trim() !== '' && doSearch,
    });

    const maintenanceProspectionOptions = useMemo(() => {
        if (!maintenanceProspections) return [];

        if (value) {
            return [value, ...maintenanceProspections.data.filter((m) => m.id !== value.id)];
        }

        return [...maintenanceProspections.data];
    }, [maintenanceProspections, value]);

    return (
        <SAutocomplete
            id="maintenance-autocomplete"
            autoComplete
            autoHighlight
            open={open && maintenanceProspectionOptions.length > 0}
            options={maintenanceProspectionOptions}
            inputValue={searchQuery}
            value={value ?? null}
            renderInput={useCallback(
                (params: AutocompleteRenderInputParams) => (
                    <TextField
                        {...params}
                        fullWidth
                        cmosVariant="roundedGrey"
                        placeholder={t('settings.prospections.maintenance.search')}
                        InputProps={{
                            ...params.InputProps,
                            sx: {
                                '--input-background-color': 'var(--neutral2)',
                            },
                            type: 'text',
                            endAdornment: (
                                <InputAdornment position="end">
                                    <SearchIcon />
                                </InputAdornment>
                            ),
                        }}
                    />
                ),
                [t]
            )}
            filterOptions={(options) => options}
            renderOption={(props, option) => {
                return (
                    <ListItem {...props}>
                        {[
                            `${option?.mileageNextService} ${t(
                                'settings.prospections.maintenance.mileageUnit'
                            )}` || '--',
                            option?.monthsBeforeNextService || '--',
                            brands(option),
                            models(option),
                            years(option),
                        ].join(' | ')}
                    </ListItem>
                );
            }}
            isOptionEqualToValue={(option) => option?.id === value?.id}
            getOptionLabel={(option) =>
                option === null
                    ? ''
                    : [
                          `${option?.mileageNextService} ${t(
                              'settings.prospections.maintenance.mileageUnit'
                          )}` || '--',
                          option?.monthsBeforeNextService || '--',
                          brands(option),
                          models(option),
                          years(option),
                      ].join(' | ')
            }
            onChange={(_, value) => {
                if (typeof value === 'string' || value === null) return;
                onSelect(value);
            }}
            onInputChange={(_, value, reason) => {
                if (onInputChange) {
                    onInputChange(value);
                }

                setSearchQuery([
                    value,
                    reason === 'input' && (value.length > 2 || !Boolean(value)),
                ]);
            }}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
        />
    );
};

export default MaintenanceProspectionAutocomplete;
