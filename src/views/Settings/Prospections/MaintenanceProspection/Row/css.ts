import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        backgroundColor: theme.palette.neutral[1],
        '&:hover': {
            backgroundColor: 'var(--cm5)',
            '&:hover': {
                '& $options': {
                    opacity: 1,
                },
            },
        },
        '& $options': {
            display: 'flex',
            justifyContent: 'flex-end',
            opacity: 0,
        },
    },
    options: {},
}));

export default useStyles;
