import IconButton from '@mui/material/IconButton';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { MaintenanceProspectionItemDto } from 'api/Prospections/MaintenanceProspection';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { Key, useMemo } from 'react';
import useStyles from './css';

type MaintenanceProspectionRowProps = {
    id?: Key | undefined | null;
    value: MaintenanceProspectionItemDto;
    onEditClick: (value: MaintenanceProspectionItemDto) => void;
    onDeleteClick: (value: MaintenanceProspectionItemDto) => void;
};
const MaintenanceProspectionRow = ({
    value,
    id,
    onEditClick,
    onDeleteClick,
}: MaintenanceProspectionRowProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    const brands = useMemo(() => value.ruleBrands?.map((b) => b.brand).join(', ') || '--', [value]);
    const models = useMemo(
        () =>
            value.ruleBrands
                ?.map((b) => b.ruleModels)
                .flat(1)
                .map((m) => m?.model)
                .join(', ') || '--',
        [value]
    );
    const years = useMemo(() => value.years.join(', ') || '--', [value]);

    return (
        <TableRow key={id} className={classes.root}>
            <TableCell
                component="td"
                scope="row"
                style={{ width: '20%', padding: '16px 10px 16px 50px' }}
            >
                {`${value.mileageNextService} ${t(
                    'settings.prospections.maintenance.mileageUnit'
                )}`}
            </TableCell>
            <TableCell component="td" scope="row" style={{ width: '30%', padding: '16px 10px' }}>
                {`${value.monthsBeforeNextService} ${
                    value.monthsBeforeNextService > 1
                        ? t('settings.prospections.maintenance.months')
                        : t('settings.prospections.maintenance.month')
                }`}
            </TableCell>
            <TableCell
                component="td"
                scope="row"
                style={{ width: '10%', padding: '16px 10px', fontWeight: 'bold' }}
            >
                {brands.length > 15 ? brands.substring(0, 15) + ' ...' : brands}
            </TableCell>
            <TableCell component="td" scope="row" style={{ width: '10%', padding: '16px 10px' }}>
                {models.length > 15 ? models.substring(0, 15) + ' ...' : models}
            </TableCell>
            <TableCell component="td" scope="row" style={{ width: '10%', padding: '16px 10px' }}>
                {years.length > 15 ? years.substring(0, 15) + ' ...' : years}
            </TableCell>
            <TableCell
                component="td"
                scope="row"
                style={{ width: '20%', padding: '16px 50px 16px 10px' }}
            >
                <div className={classes.options}>
                    <IconButton
                        style={{ height: 32, width: 32 }}
                        onClick={() => onEditClick(value)}
                        size="large"
                    >
                        <EditIcon fill={Colors.Neutral5} />
                    </IconButton>
                    <IconButton
                        style={{ height: 32, width: 32 }}
                        onClick={() => onDeleteClick(value)}
                        size="large"
                    >
                        <DeleteIcon fill={Colors.Neutral5} />
                    </IconButton>
                </div>
            </TableCell>
        </TableRow>
    );
};

export default MaintenanceProspectionRow;
