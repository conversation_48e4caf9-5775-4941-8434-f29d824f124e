import { Pagination, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import MaintenanceProspectionAPI, {
    MaintenanceOrderType,
    MaintenanceProspectionItemDto,
} from 'api/Prospections/MaintenanceProspection';
import { Button } from 'common/components/Button';
import { UpIcon } from 'common/components/Icons/UpIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import TableHeadCell from 'common/components/TableHeadCell';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import MaintenanceProspectionAutocomplete from './Autocomplete';
import MaintenanceProspectionFormModal from './FormModal';
import MaintenanceProspectionRow from './Row';
import MaintenanceProspectionSettingsProps, { connector } from './props';

const MaintenanceProspectionSettings = ({ setNewToaster }: MaintenanceProspectionSettingsProps) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const pageSize = 10;
    const [page, setPage] = useState<number>(1);
    const [pages, setPages] = useState<number>(1);
    const [brands, setBrands] = useState<string[]>([]);
    const [brand, setBrand] = useState<OptionData>({
        label: t('settings.prospections.maintenance.allBrands'),
        value: 'all',
    });
    const [orderByType, setOrderByType] = useState<MaintenanceOrderType>(
        MaintenanceOrderType.MileageAscending
    );
    const [maintenanceProspections, setMaintenanceProspections] = useState<
        MaintenanceProspectionItemDto[]
    >([]);
    const [ruleFilter, setRuleFilter] = useState<MaintenanceProspectionItemDto>();
    const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);
    const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false);
    const [rule, setRule] = useState<MaintenanceProspectionItemDto>();

    const fetchMaintenanceProspections = async () => {
        const response = await callApi(
            () =>
                MaintenanceProspectionAPI.getMaintenanceProspectionsPaginated(
                    page,
                    pageSize,
                    orderByType,
                    brand.value !== 'all' ? brand.value : undefined,
                    undefined,
                    ruleFilter?.id
                ),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }
        );

        if (response.data) {
            setMaintenanceProspections(response.data);
            setPages(Math.ceil(response.pagerData.totalItemsCount / pageSize));
        }
    };

    const fetchAllBrands = async () => {
        try {
            const response = await MaintenanceProspectionAPI.getBrands();
            setBrands(response);
        } catch (_) {
            // TODO: Pending to add toaster
        }
    };

    const brandOptions: OptionData[] = useMemo(
        () => [
            {
                label: t('settings.prospections.maintenance.allBrands'),
                value: 'all',
            },
            ...brands.map((b) => ({
                label: b,
                value: b,
            })),
        ],
        [brands, t]
    );

    const changePage = (page: number) => {
        const params = new URLSearchParams(location.search);
        params.set('page', `${page}`);
        navigate({ search: params.toString() });
        setPage(page);
    };

    const handleRowEdition = async ({ id }: MaintenanceProspectionItemDto) => {
        if (id) {
            const response = await callApi(() => MaintenanceProspectionAPI.getDetail(id), {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            });
            setRule(response);
            setOpenCreateModal(true);
        }
    };

    const handleOnDelete = async () => {
        if (rule) {
            await callApi(() => MaintenanceProspectionAPI.deleteRule(rule.id ?? 0), {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenSaving'),
                }),
            });

            setOpenDeleteModal(false);
            setRule(undefined);
            setNewToaster(
                new NotificationData(
                    t(
                        'settings.prospections.maintenance.maintenanceProspectionRuleDeletedSuccessfully'
                    ),
                    t('settings.prospections.maintenance.maintenanceProspectionRuleDeleted'),
                    NotificationType.success
                )
            );
            fetchMaintenanceProspections();
        }
    };

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const pageParam = params.get('page');
        changePage(pageParam ? +pageParam : 1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        fetchAllBrands();
        fetchMaintenanceProspections();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orderByType, brand, page, ruleFilter]);

    return (
        <>
            <Grid container justifyContent="flex-start" direction="column">
                <StyledTableContainer>
                    <Grid
                        container
                        justifyContent="space-between"
                        style={{
                            paddingLeft: 30,
                            paddingTop: 30,
                            paddingBottom: 24,
                            paddingRight: 30,
                        }}
                    >
                        <Grid item xs={12} md={6}>
                            <Grid container spacing={1}>
                                <Grid item xs={8}>
                                    <MaintenanceProspectionAutocomplete
                                        value={ruleFilter}
                                        onSelect={(value) => {
                                            setRuleFilter(value);
                                        }}
                                        onInputChange={() => {
                                            setRuleFilter(undefined);
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={4}>
                                    <Dropdown
                                        name="brands"
                                        cmosVariant="roundedGrey"
                                        options={brandOptions}
                                        value={brand}
                                        onChange={(event) => {
                                            event && setBrand(event);
                                        }}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            md={6}
                            justifyContent="flex-end"
                            style={{ display: 'flex' }}
                        >
                            <Button
                                label={t(
                                    'settings.prospections.maintenance.createNewMaintenanceProspectionRule'
                                )}
                                cmosVariant={'filled'}
                                cmosSize={'medium'}
                                color={Colors.CM1}
                                onClick={() => setOpenCreateModal(true)}
                            />
                        </Grid>
                    </Grid>
                    <Table stickyHeader>
                        <TableHead>
                            <TableRow>
                                <TableHeadCell
                                    component="td"
                                    scope="row"
                                    style={{ width: '20%', padding: '16px 10px 16px 50px' }}
                                >
                                    <TableSortLabel
                                        active={
                                            orderByType === MaintenanceOrderType.MileageAscending ||
                                            orderByType === MaintenanceOrderType.MileageDescending
                                        }
                                        direction={
                                            orderByType === MaintenanceOrderType.MileageAscending
                                                ? 'asc'
                                                : 'desc'
                                        }
                                        onClick={() => {
                                            setOrderByType(
                                                orderByType ===
                                                    MaintenanceOrderType.MileageAscending
                                                    ? MaintenanceOrderType.MileageDescending
                                                    : MaintenanceOrderType.MileageAscending
                                            );
                                        }}
                                        IconComponent={useCallback(
                                            (propsIcon: { className: string }) => (
                                                <div {...propsIcon}>
                                                    <UpIcon
                                                        size={IconSize.M}
                                                        fill={Colors.Neutral7}
                                                    />
                                                </div>
                                            ),
                                            []
                                        )}
                                    >
                                        {t('settings.prospections.maintenance.mileage')}
                                    </TableSortLabel>
                                </TableHeadCell>
                                <TableHeadCell
                                    component="td"
                                    scope="row"
                                    style={{ width: '30%', padding: '16px 10px' }}
                                >
                                    <TableSortLabel
                                        active={
                                            orderByType ===
                                                MaintenanceOrderType.MonthNextServiceAscending ||
                                            orderByType ===
                                                MaintenanceOrderType.MonthNextServiceDescending
                                        }
                                        direction={
                                            orderByType ===
                                            MaintenanceOrderType.MonthNextServiceAscending
                                                ? 'asc'
                                                : 'desc'
                                        }
                                        onClick={() => {
                                            setOrderByType(
                                                orderByType ===
                                                    MaintenanceOrderType.MonthNextServiceAscending
                                                    ? MaintenanceOrderType.MonthNextServiceDescending
                                                    : MaintenanceOrderType.MonthNextServiceAscending
                                            );
                                        }}
                                        IconComponent={useCallback(
                                            (propsIcon: { className: string }) => (
                                                <div {...propsIcon}>
                                                    <UpIcon
                                                        size={IconSize.M}
                                                        fill={Colors.Neutral7}
                                                    />
                                                </div>
                                            ),
                                            []
                                        )}
                                    >
                                        {t(
                                            'settings.prospections.maintenance.monthsForNextService'
                                        )}
                                    </TableSortLabel>
                                </TableHeadCell>
                                <TableHeadCell
                                    component="td"
                                    scope="row"
                                    style={{ width: '10%', padding: '16px 10px' }}
                                >
                                    {t('settings.prospections.maintenance.brand')}
                                </TableHeadCell>
                                <TableHeadCell
                                    component="td"
                                    scope="row"
                                    style={{ width: '10%', padding: '16px 10px' }}
                                >
                                    {t('settings.prospections.maintenance.model')}
                                </TableHeadCell>
                                <TableHeadCell
                                    component="td"
                                    scope="row"
                                    style={{ width: '10%', padding: '16px 10px' }}
                                >
                                    {t('settings.prospections.maintenance.year')}
                                </TableHeadCell>
                                <TableHeadCell
                                    component="td"
                                    scope="row"
                                    style={{ width: '20%', padding: '16px 50px 16px 10px' }}
                                />
                            </TableRow>
                        </TableHead>
                        {maintenanceProspections && maintenanceProspections.length ? (
                            <TableBody>
                                {maintenanceProspections.map((m, index) => (
                                    <MaintenanceProspectionRow
                                        key={`maintenance-prospection-${index}-${m.id}`}
                                        id={`maintenance-prospection-${index}-${m.id}`}
                                        value={m}
                                        onEditClick={handleRowEdition}
                                        onDeleteClick={(value) => {
                                            setRule(value);
                                            setOpenDeleteModal(true);
                                        }}
                                    />
                                ))}
                            </TableBody>
                        ) : null}
                    </Table>
                </StyledTableContainer>
            </Grid>
            <Grid container justifyContent="center" style={{ marginTop: 29 }}>
                {maintenanceProspections && maintenanceProspections.length && pages > 1 ? (
                    <Pagination
                        page={page}
                        size="small"
                        count={pages}
                        color="primary"
                        onChange={(_, page) => {
                            changePage(page);
                        }}
                    />
                ) : null}
            </Grid>
            <DeleteConfirmationPopup
                open={openDeleteModal}
                title={t('settings.prospections.maintenance.deleteMaintenanceProspectionRule')}
                body={
                    <Trans
                        i18nKey="settings.prospections.maintenance.byDeletingThisMaintenanceRuleItWillRemovedFromTheListOfMaintenanceProspectionRule"
                        t={t}
                    />
                }
                cancel={t('commonLabels.doNotDelete')}
                confirm={t('settings.prospections.maintenance.deleteProspectionRule')}
                onConfirm={handleOnDelete}
                onClose={() => {
                    setOpenDeleteModal(false);
                    setRule(undefined);
                }}
            />
            <MaintenanceProspectionFormModal
                open={openCreateModal}
                value={rule}
                onClose={() => {
                    setOpenCreateModal(false);
                    setRule(undefined);
                }}
                onSave={() => {
                    setOpenCreateModal(false);
                    setRule(undefined);
                    fetchAllBrands();
                    fetchMaintenanceProspections();
                }}
            />
        </>
    );
};

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
    flexGrow: 1,
    minHeight: 734,
    width: '100%',
    borderRadius: 12,
    border: 'solid 1px #dbdcdd',
    backgroundColor: theme.palette.neutral[1],

    overflowY: 'scroll',
    ...scrollbarStyle(),
}));

export default connector(MaintenanceProspectionSettings);
