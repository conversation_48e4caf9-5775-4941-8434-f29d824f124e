import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import MaintenanceProspectionAPI, {
    MaintenanceProspectionItemDto,
    ProspectionRuleBrandDto,
} from 'api/Prospections/MaintenanceProspection';
import { Button } from 'common/components/Button';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useMemo, useState } from 'react';
import MultipleModelPicker, { BrandModelsData } from 'views/Components/MultipleModelPicker';
import MultipleYearPicker from 'views/Components/MultipleYearPicker';
import MultipleBrandPicker from '../../../../Components/MultipleBrandPicker';
import useStyles from './css';
import MaintenanceProspectionFormModalProps, { connector } from './props';

const MaintenanceProspectionFormModal = ({
    open,
    value,
    onClose,
    onSave,
    setNewToaster,
}: MaintenanceProspectionFormModalProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    const [maintenanceRule, setMaintenanceRule] = useState<MaintenanceProspectionItemDto>(
        value ?? {
            mileageNextService: 0,
            monthsBeforeNextService: 0,
            years: [],
            ruleBrands: [],
        }
    );
    const [brand, setBrand] = useState<string[]>([]);
    const [model, setModel] = useState<BrandModelsData[]>([]);
    const [year, setYear] = useState<string[]>([]);
    const [brandTouched, setBrandTouched] = useState<boolean>(false);
    const [modelTouched, setModelTouched] = useState<boolean>(false);

    const isClearable = useMemo(() => !Boolean(value?.id), [value]);

    const handleOnSave = async () => {
        const ruleBrands: ProspectionRuleBrandDto[] = brand.map((b) => ({
            brand: b,
            ruleModels: (model.find((m) => m.name === b)?.models ?? []).map((model) => ({
                model,
            })),
        }));

        try {
            let response: MaintenanceProspectionItemDto;
            if (value?.id) {
                response = await MaintenanceProspectionAPI.update({
                    ...maintenanceRule,
                    id: value.id,
                    ruleBrands,
                });
            } else {
                response = await MaintenanceProspectionAPI.create({
                    ...maintenanceRule,
                    ruleBrands,
                });
            }

            setNewToaster(
                new NotificationData(
                    t(
                        `settings.prospections.maintenance.form.${
                            value?.id
                                ? 'maintenanceProspectionRuleSavedSuccessfully'
                                : 'maintenanceProspectionRuleCreatedSuccessfully'
                        }`
                    ),
                    t(
                        `settings.prospections.maintenance.form.${
                            value?.id
                                ? 'maintenanceProspectionRuleEdited'
                                : 'maintenanceProspectionRuleCreated'
                        }`
                    ),
                    NotificationType.success
                )
            );
            setMaintenanceRule({
                mileageNextService: 0,
                monthsBeforeNextService: 0,
                years: [],
                ruleBrands: [],
            });
            setBrand([]);
            setModel([]);
            setYear([]);
            onSave({
                ...response,
            });
        } catch ({ response }: any) {
            const { status } = response as any;
            if (status === 409) {
                setNewToaster(
                    new NotificationData(
                        t(
                            'settings.prospections.maintenance.form.theMaintenanceProspectionRuleIsDuplicated'
                        ),
                        t(
                            `settings.prospections.maintenance.form.${
                                value?.id
                                    ? 'maintenanceProspectionRuleNotEdited'
                                    : 'maintenanceProspectionRuleNotCreated'
                            }`
                        ),
                        NotificationType.danger
                    )
                );
            } else {
                setNewToaster(
                    new NotificationData(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred'),
                        NotificationType.danger
                    )
                );
            }
        }
    };

    const handleYearChange = (value: string[]) => {
        setYear(value);
        setMaintenanceRule((old) => ({
            ...old,
            years: value, // remove duplicates
        }));
    };

    useEffect(() => {
        if (value?.id) {
            setMaintenanceRule(value);
            if (value.ruleBrands) {
                setBrand(value.ruleBrands.map((b) => b.brand));
                setModel(
                    value.ruleBrands.map((b) => ({
                        name: b.brand,
                        models: b.ruleModels.map((x) => x.model),
                    }))
                );
            }

            if (value.years) {
                setYear(value.years);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    return (
        <Modal open={open}>
            <Box className={classes.root}>
                <Grid container spacing={1}>
                    <Grid item xs={6}>
                        <Typography className={classes.title}>
                            {t(
                                `settings.prospections.maintenance.form.${
                                    value && value.id
                                        ? 'editMaintenanceProspectionRule'
                                        : 'createMaintenanceProspectionRule'
                                }`
                            )}
                        </Typography>
                    </Grid>
                    <Grid item xs={6}>
                        <Grid item container spacing={1}>
                            <Grid item xs={6}>
                                <Button
                                    cmosSize={'medium'}
                                    color={Colors.Neutral3}
                                    cmosVariant={'filled'}
                                    blockMode
                                    label={t('commonLabels.cancel')}
                                    onClick={() => {
                                        setMaintenanceRule({
                                            mileageNextService: 0,
                                            monthsBeforeNextService: 0,
                                            years: [],
                                            ruleBrands: [],
                                        });
                                        setBrand([]);
                                        setModel([]);
                                        setYear([]);
                                        setBrandTouched(false);
                                        setModelTouched(false);
                                        onClose();
                                    }}
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <Button
                                    cmosSize={'medium'}
                                    color={Colors.Success}
                                    cmosVariant={'filled'}
                                    blockMode
                                    label={t(
                                        `settings.prospections.maintenance.form.${
                                            value && value.id ? 'saveData' : 'createNewRule'
                                        }`
                                    )}
                                    disabled={
                                        !maintenanceRule?.mileageNextService ||
                                        !maintenanceRule?.monthsBeforeNextService
                                    }
                                    onClick={handleOnSave}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
                <Grid container spacing={3} style={{ marginTop: 39 }}>
                    <Grid item xs={12} md={4}>
                        <TextFormField
                            label={t(
                                'settings.prospections.maintenance.form.mileageForTheNextService'
                            )}
                            placeholder={t(
                                'settings.prospections.maintenance.form.enterTheMileageForProspection'
                            )}
                            type="number"
                            name="order-type"
                            isRequired
                            maxLength={128}
                            value={
                                maintenanceRule.mileageNextService
                                    ? `${maintenanceRule.mileageNextService}`
                                    : undefined
                            }
                            onKeyDown={(event) => {
                                ['e', 'E', '.'].includes(event.key) && event.preventDefault();
                            }}
                            onChange={(event) => {
                                setMaintenanceRule((old) => ({
                                    ...old,
                                    mileageNextService: +event.target.value.replace(/[^0-9]/, ''),
                                }));
                            }}
                            showValidationIndicators
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <TextFormField
                            label={t(
                                'settings.prospections.maintenance.form.monthsForTheNextService'
                            )}
                            placeholder={t(
                                'settings.prospections.maintenance.form.enterTheMonthsForProspection'
                            )}
                            type="number"
                            name="order-type"
                            isRequired
                            maxLength={128}
                            value={
                                maintenanceRule.monthsBeforeNextService
                                    ? `${maintenanceRule.monthsBeforeNextService}`
                                    : undefined
                            }
                            onKeyDown={(event) => {
                                ['e', 'E', '.'].includes(event.key) && event.preventDefault();
                            }}
                            onChange={(event) => {
                                setMaintenanceRule((old) => ({
                                    ...old,
                                    monthsBeforeNextService: +event.target.value.replace(
                                        /[^0-9]/,
                                        ''
                                    ),
                                }));
                            }}
                            showValidationIndicators
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <MultipleBrandPicker
                            value={brand}
                            cmosVariant="default"
                            onChange={(event) => {
                                setBrand(event);
                                if (isClearable || brandTouched) {
                                    setModel([]);
                                    setYear([]);
                                    setMaintenanceRule((old) => ({ ...old, years: [] }));
                                } else {
                                    setBrandTouched(true);
                                }
                            }}
                        />
                    </Grid>
                </Grid>
                <Grid container spacing={3} style={{ marginTop: 25 }}>
                    <Grid item xs={12} md={4}>
                        <MultipleModelPicker
                            value={model}
                            cmosVariant="default"
                            onChange={(event) => {
                                setModel(event);
                                if (isClearable || modelTouched) {
                                    setYear([]);
                                    setMaintenanceRule((old) => ({ ...old, years: [] }));
                                } else {
                                    setModelTouched(true);
                                }
                            }}
                            filterBrands={brand}
                        />
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <MultipleYearPicker
                            showEmptyList={
                                model.map((x) => x.models.length).reduce((a, b) => a + b, 0) === 0
                            }
                            value={year}
                            cmosVariant="default"
                            onChange={handleYearChange}
                        />
                    </Grid>
                    <Grid item xs={12} md={4} />
                </Grid>
            </Box>
        </Modal>
    );
};

export default connector(MaintenanceProspectionFormModal);
