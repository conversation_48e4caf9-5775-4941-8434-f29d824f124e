import { MaintenanceProspectionItemDto } from 'api/Prospections/MaintenanceProspection';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { connect } from 'react-redux';
// import { createStructuredSelector } from 'reselect';
import { ExtractConnectType } from 'store';
import * as ToastersActions from 'store/actions/toasters.action';

// const mapStateToProps = createStructuredSelector({
// });

const mapDispatchToProps = (dispatch: any) => ({
    setNewToaster: (newToaster: NotificationData) =>
        dispatch(ToastersActions.setNewToaster(newToaster)),
});

export const connector = connect(null, mapDispatchToProps);

type PropsFromRedux = ExtractConnectType<typeof connector>;

export default interface MaintenanceProspectionFormModalProps extends PropsFromRedux {
    open: boolean;
    value?: MaintenanceProspectionItemDto;
    onClose: () => void;
    onSave: (value: MaintenanceProspectionItemDto) => void;
}
