import { SETTINGS_PROSPECTION_TABS } from 'common/constants';
import ProspectionGeneralSettings from './General';
import MaintenanceProspectionSettings from './MaintenanceProspection';

export enum ProspectionTabEnum {
    General = 'general',
    MaintenanceProspection = 'maintenance',
    ProspectionExceptions = 'exceptions',
    ImportCustomers = 'import-customers',
    CallResults = 'call-results',
}

export type ProspectionTab = {
    id: ProspectionTabEnum;
    section: string;
    label: string;
    component: React.ReactNode;
};

export const ProspectionTabs: ProspectionTab[] = [
    {
        id: ProspectionTabEnum.General,
        section: SETTINGS_PROSPECTION_TABS.GENERAL,
        label: 'settings.prospections.tabs.general',
        component: <ProspectionGeneralSettings />,
    },
    {
        id: ProspectionTabEnum.MaintenanceProspection,
        section: SETTINGS_PROSPECTION_TABS.MAINTENANCE_PROSPECTION,
        label: 'settings.prospections.tabs.maintenanceProspection',
        component: <MaintenanceProspectionSettings />,
    },
    // TODO: Uncomment when will be ready
    // {
    //     id: ProspectionTabEnum.ProspectionExceptions,
    //     section: SETTINGS_PROSPECTION_TABS.PROSPECTION_EXCEPTION,
    //     label: 'settings.prospections.tabs.prospectionException',
    //     component: <ProspectionExceptionsSettings />,
    // },
    // {
    //     id: ProspectionTabEnum.CallResults,
    //     section: SETTINGS_PROSPECTION_TABS.CALL_RESULTS,
    //     label: 'settings.prospections.tabs.callResults',
    //     component: <CallResultsSettings />,
    // },
];
