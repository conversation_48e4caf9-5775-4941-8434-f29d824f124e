import {
    Box,
    Grid,
    TableRow,
    Tooltip,
    TooltipProps,
    styled,
    tooltipClasses,
    Typography,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { MoveDownIcon } from 'common/components/Icons/MoveDownIcon';
import { MoveUpIcon } from 'common/components/Icons/MoveUpIcon';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo, useState } from 'react';
import { Button } from '../../../../../common/components/Button';
import TableCell from '../../../../../common/components/TableCell';
import { Colors } from '../../../../../common/styles/Colors';
import { AddActivityForm } from '../AddActivityForm';
import ActivityClassificationApi, {
    CustomerActivityDto,
    CustomerActivityUpdateRequest,
    CustomerStatusDto,
} from 'api/settings/AftersalesCrm/activityClassification';
import { Trans } from 'react-i18next';

type RowActivityProps = {
    activity: CustomerActivityDto;
    customerStatuses: CustomerStatusDto[];
    index: number;
    isLastItem: boolean;
    isSingleItem: boolean;
    readonly?: boolean;
    handleMove: (index: number, direction: number) => void;
    refetch?: () => void;
};

const Row = styled(TableRow)(({ theme }) => ({
    minHeight: '56px!important',
    height: 56,
    backgroundColor: theme.palette.common.white,
}));

const CellContentR = styled(Grid)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
}));

const CellContentI = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));

const STooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
        filter: `drop-shadow(0px 0px 1px ${theme.palette.neutral[7]})`,
        position: 'relative',
        borderRadius: 5,
    },
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
    },
}));

export const RowActivity = ({
    activity,
    customerStatuses,
    index,
    readonly,
    isLastItem,
    isSingleItem,
    handleMove,
    refetch,
}: RowActivityProps) => {
    const { t } = useAppTranslation();
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);
    const [showEditActivityPopUp, setShowEditActivityPopUp] = useState<boolean>(false);
    const { handleDelete, handleUpdate, isLoading, isSuccess, isError, reset } = useRowActivity(
        activity,
        () => {
            setShowDeleteConfirmation(false);
            refetch && refetch();
        }
    );

    const { name, description } = activity;

    const handleEdit = async () => {
        setShowEditActivityPopUp(true);
    };

    const moveUpHandler = async () => {
        if (index <= 0) return; // index bound detection
        handleMove(index, -1);
    };

    const moveDownHandler = () => {
        if (isLastItem) return; // index bound detection
        handleMove(index, 1);
    };

    const nameSpan =
        name.length > 80 ? (
            <STooltip title={name} placement="top-start" arrow>
                <span>{`${name.slice(0, 80)}...`}</span>
            </STooltip>
        ) : (
            <span>{name}</span>
        );

    const descriptionSpan =
        description?.length > 90 ? (
            <STooltip title={description} placement="top-start" arrow>
                <span>{`${description.slice(0, 90)}...`}</span>
            </STooltip>
        ) : (
            <span>{description}</span>
        );

    return (
        <>
            <Row>
                <TableCell component="td" scope="row" style={{ paddingLeft: 30, width: '45%' }}>
                    <CellContentR>{nameSpan}</CellContentR>
                </TableCell>
                <TableCell component="td" scope="row" style={{ width: '45%' }}>
                    <CellContentI container>{descriptionSpan}</CellContentI>
                </TableCell>
                <TableCell component="td" scope="row" style={{ paddingRight: 30, width: '10%' }}>
                    <Box style={{ display: 'flex' }}>
                        <Button
                            disabled={readonly || index === 0}
                            cmosVariant={'typography'}
                            Icon={MoveUpIcon}
                            color={index === 0 ? 'transparent' : '#899198'}
                            onClick={moveUpHandler}
                        />
                        <Button
                            disabled={readonly || isLastItem}
                            cmosVariant={'typography'}
                            Icon={MoveDownIcon}
                            color={isLastItem ? 'transparent' : '#899198'}
                            onClick={moveDownHandler}
                        />
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={EditIcon}
                            color={Colors.Neutral5}
                            onClick={handleEdit}
                        />
                        <Button
                            disabled={readonly || isSingleItem}
                            cmosVariant={'typography'}
                            Icon={DeleteIcon}
                            color={Colors.Neutral5}
                            onClick={() => setShowDeleteConfirmation(true)}
                        />
                        <div />
                    </Box>
                </TableCell>
                {showDeleteConfirmation && (
                    <DeleteConfirmationPopup
                        open={showDeleteConfirmation}
                        title={t(
                            'settings.aftersalesCrm.classificationOfActivities.deleteActivityModal.title'
                        )}
                        body={
                            <DeleteBodyBox>
                                <Typography sx={{ fontSize: 14 }}>{activity.name}</Typography>
                                <Typography>
                                    <Trans
                                        i18nKey="settings.aftersalesCrm.classificationOfActivities.deleteActivityModal.text"
                                        t={t}
                                    />
                                </Typography>
                            </DeleteBodyBox>
                        }
                        cancel={t('settings.aftersalesCrm.classificationOfActivities.cancel')}
                        confirm={t('settings.aftersalesCrm.classificationOfActivities.delete')}
                        onConfirm={() => handleDelete(activity)}
                        onClose={() => {
                            setShowDeleteConfirmation(false);
                        }}
                    />
                )}
                {showEditActivityPopUp && (
                    <AddActivityForm
                        open={showEditActivityPopUp}
                        onClose={() => {
                            setShowEditActivityPopUp(false);
                            reset();
                        }}
                        activity={activity}
                        customerStatusId={activity.statusId}
                        onConfirm={handleUpdate}
                        onConfirmText={t(
                            'settings.aftersalesCrm.classificationOfActivities.activityModal.editActivityButton'
                        )}
                        title={t(
                            'settings.aftersalesCrm.classificationOfActivities.activityModal.editActivity'
                        )}
                        isLoading={isLoading}
                        isSuccess={isSuccess}
                        isError={isError}
                    />
                )}
            </Row>
        </>
    );
};

const useRowActivity = (activity: CustomerActivityDto, onSuccess?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const {
        mutate: updateActivity,
        isLoading,
        isSuccess,
        isError,
        reset,
    } = useMutation(
        (request: CustomerActivityUpdateRequest) =>
            ActivityClassificationApi.updateCustomerActivity(request),
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
            onError: (_, request) => {
                toasters.danger(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.activityExists'
                    )
                );
            },
        }
    );

    const { mutate: deleteActivity } = useMutation(
        (request: CustomerActivityUpdateRequest) =>
            ActivityClassificationApi.deleteCustomerActivity(request.id),
        {
            onSuccess: () => {
                toasters.success(
                    activity.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.activityDeleted'
                    )
                );
                onSuccess && onSuccess();
            },
        }
    );

    const handleUpdate = async (body: CustomerActivityDto) => {
        const { id, statusId, name, description } = body;
        const updateBody: CustomerActivityUpdateRequest = {
            id: id,
            statusId: statusId,
            name: name,
            description: description,
        };

        updateActivity(updateBody);
    };

    const handleDelete = async (body: CustomerActivityDto) => {
        const { id } = body;
        deleteActivity({
            id: id,
        });
    };

    return {
        handleDelete: handleDelete,
        isLoading,
        isSuccess,
        isError,
        handleUpdate,
        reset,
    };
};

const DeleteBodyBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: 5,
    marginTop: '-8px',
    '& :first-child': {
        color: theme.palette.neutral[7],
    },
    '& :last-child': {
        color: theme.palette.neutral[6],
    },
}));
