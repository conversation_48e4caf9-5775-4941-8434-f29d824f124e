import { Table, TableBody, TableContainer, TableHead, TableRow, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import ActivityClassificationApi, {
    CustomerActivityDto,
    CustomerStatusDto,
    CustomerActivityUpdateRequest,
} from 'api/settings/AftersalesCrm/activityClassification';
import { TableHeadCell } from 'common/components';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { RowActivity } from '../RowActivify';

type CustomerActivityTableProps = {
    customerStatus: CustomerStatusDto;
    customerStatuses: CustomerStatusDto[];
    readonly?: boolean;
    refetch?: () => void;
};

const STableContainer = styled(TableContainer)({
    maxHeight: 330,
    ...scrollbarStyle(),
});

const CustomerActivityTable = ({
    customerStatus,
    customerStatuses,
    readonly,
    refetch,
}: CustomerActivityTableProps) => {
    const activities = customerStatus.activities;
    const { t } = useAppTranslation();

    const { updateCustomerActivity } = useCustomerActivityTable(refetch);

    const handleMove = (index: number, direction: number) => {
        const promises = [];
        const body1 = {
            id: activities[index].id,
            order: activities[index + direction].order,
        };
        const body2 = {
            id: activities[index + direction].id,
            order: activities[index].order,
        };

        promises.push(updateCustomerActivity(body1));
        promises.push(updateCustomerActivity(body2));
    };

    return (
        <STableContainer>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableHeadCell style={{ paddingLeft: 30, width: '45%' }}>
                            {t('settings.aftersalesCrm.classificationOfActivities.activity')}
                        </TableHeadCell>
                        <TableHeadCell style={{ width: '45%' }}>
                            {t('settings.aftersalesCrm.classificationOfActivities.description')}
                        </TableHeadCell>
                        <TableHeadCell style={{ width: '10%' }} />
                    </TableRow>
                </TableHead>
                <TableBody>
                    {activities &&
                        activities.map((customerActivity: CustomerActivityDto, idxRow: number) => (
                            <RowActivity
                                customerStatuses={customerStatuses}
                                activity={customerActivity}
                                key={idxRow}
                                readonly={readonly}
                                index={idxRow}
                                isLastItem={idxRow === activities.length - 1}
                                isSingleItem={activities.length === 1}
                                handleMove={handleMove}
                                refetch={refetch}
                            />
                        ))}
                </TableBody>
            </Table>
        </STableContainer>
    );
};

export default CustomerActivityTable;

const useCustomerActivityTable = (onSuccess?: () => void) => {
    const {
        mutate: updateCustomerActivity,
        isLoading,
        isSuccess,
    } = useMutation(
        (request: CustomerActivityUpdateRequest) => {
            return ActivityClassificationApi.updateCustomerActivity(request);
        },
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
        }
    );

    return {
        updateCustomerActivity: updateCustomerActivity,
        isLoading,
        isSuccess,
    };
};
