import { Box } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import ActivityClassificationApi, {
    CustomerStatusDto,
    CustomerActivityDto,
    CustomerActivityCreateRequest,
} from 'api/settings/AftersalesCrm/activityClassification';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useEffect, useMemo, useState } from 'react';
import { AddActivityForm } from '../AddActivityForm';
import CustomerActivityTable from '../CustomerActivityTable';

type CustomerActivitiesListProps = {
    customerStatus: CustomerStatusDto;
    customerStatuses: CustomerStatusDto[];
    readonly?: boolean;
    refetch?: () => void;
};

export const CustomerActivitiesList = ({
    customerStatus,
    customerStatuses,
    readonly,
    refetch,
}: CustomerActivitiesListProps) => {
    const [showAddDetailsPopUp, setShowAddDetailsPopUp] = useState(false);
    const { t } = useAppTranslation();

    const { createActivity, isLoading, isSuccess, isError, reset } = useAddActivityForm(refetch);

    const handleAddActivity = () => {
        setShowAddDetailsPopUp(true);
    };

    const handleCreateActivity = (body: CustomerActivityDto) => {
        createActivity(body);
        // reset();
    };

    useEffect(() => {
        if (!isLoading && isSuccess) {
            setShowAddDetailsPopUp(false);
        }
    }, [isLoading, isSuccess, setShowAddDetailsPopUp]);

    return (
        <Box
            sx={{
                width: '100%',
                paddingTop: 0,
                paddingBottom: 2.5,
            }}
        >
            {!!customerStatus.activities.length && (
                <CustomerActivityTable
                    customerStatuses={customerStatuses}
                    customerStatus={customerStatus}
                    readonly={readonly}
                    refetch={refetch}
                />
            )}
            {/* buttons */}
            <Box
                sx={{
                    display: 'flex',
                    gap: 1,
                    marginTop: 2.5,
                    marginLeft: 2,
                }}
            >
                <Button
                    disabled={readonly}
                    customStyles={{
                        width: 280,
                    }}
                    buttonInnercustomStyles={{
                        width: '100%',
                        justifyContent: 'space-between',
                        padding: '0 5px 0 10px',
                    }}
                    cmosVariant={'stroke'}
                    iconPosition="right"
                    Icon={PlusIcon}
                    color={Colors.Neutral3}
                    onClick={handleAddActivity}
                    label={t('settings.aftersalesCrm.classificationOfActivities.addActivity')}
                />
            </Box>
            {showAddDetailsPopUp && (
                <AddActivityForm
                    open={showAddDetailsPopUp}
                    customerStatusId={customerStatus.id}
                    onClose={() => {
                        setShowAddDetailsPopUp(false);
                        reset();
                    }}
                    onConfirm={handleCreateActivity}
                    onConfirmText={t(
                        'settings.aftersalesCrm.classificationOfActivities.activityModal.newActivityButton'
                    )}
                    title={t(
                        'settings.aftersalesCrm.classificationOfActivities.activityModal.newActivity'
                    )}
                    isLoading={isLoading}
                    isSuccess={isSuccess}
                    isError={isError}
                />
            )}
        </Box>
    );
};

const useAddActivityForm = (onSuccess?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const {
        mutate: createActivity,
        isLoading,
        isSuccess,
        isError,
        reset,
    } = useMutation(
        (request: CustomerActivityCreateRequest) =>
            ActivityClassificationApi.createActivity(request),
        {
            onSuccess: (_, request) => {
                toasters.success(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.activityCreated'
                    )
                );
                onSuccess && onSuccess();
            },
            onError: (_, request) => {
                toasters.danger(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.activityExists'
                    )
                );
            },
        }
    );

    return {
        createActivity: createActivity,
        isLoading,
        isSuccess,
        isError,
        reset,
    };
};
