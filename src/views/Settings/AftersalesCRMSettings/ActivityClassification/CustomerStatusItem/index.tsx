import { Box, Grid, styled, Typography, useTheme } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import ActivityClassificationApi, {
    CustomerStatusDto,
    CustomerStatusUpdateRequest,
} from 'api/settings/AftersalesCrm/activityClassification';
import { Button } from 'common/components/Button';
import { DeleteIcon, EditIcon, MoveDownIcon, MoveUpIcon } from 'common/components/Icons';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useState, useRef } from 'react';
import { Trans } from 'react-i18next';
import { CustomerActivitiesList } from '../CustomActivitiesList';
import ResettableTextInputWithCustomControl from '../../../../../common/components/Inputs/ResettableTextFieldWithCustomControl/ResettableTextInputWithCustomControl';

export interface CustomerStatusItemProps {
    customerStatus: CustomerStatusDto;
    customerStatuses: CustomerStatusDto[];
    index: number;
    isLastItem: boolean;
    refetch: () => void;
    handleMove: (index: number, direction: number) => void;
    readonly?: boolean;
}

export const CustomerStatusItem = ({
    customerStatus,
    customerStatuses,
    index,
    handleMove,
    isLastItem,
    refetch,
    readonly,
}: CustomerStatusItemProps) => {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const startEditingRef = useRef(() => {});
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);

    const { updateCustomerStatus, deleteCustomerStatus } = useCustomerStatusItem(
        customerStatus,
        refetch
    );

    const handleRename = async (name: string) => {
        return await updateCustomerStatus({ id: customerStatus.id, name: name });
    };

    const handleDelete = async () => {
        await deleteCustomerStatus(customerStatus.id);
        setShowDeleteConfirmation(false);
    };

    const moveUpHandler = async () => {
        if (index <= 0) return; // index bound detection
        handleMove(index, -1);
    };

    const moveDownHandler = () => {
        if (isLastItem) return; // index bound detection
        handleMove(index, 1);
    };

    return (
        <>
            <Grid
                container
                justifyContent="center"
                sx={{
                    border: '1px solid rgba(201, 205, 211, 0.5)',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    marginBottom: 5,
                }}
            >
                <CssGrid>
                    <div className="title">
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'start',
                                alignItems: 'center',
                                marginLeft: 2,
                                gap: 1, // 1 = 8px
                                '& .title': {
                                    width: 'max-content',
                                },
                                width: 320,
                            }}
                        >
                            <ResettableTextInputWithCustomControl
                                control={
                                    <>
                                        <Typography
                                            sx={(theme) => ({
                                                ...theme.typography.h5,
                                                color: readonly
                                                    ? theme.palette.neutral[4]
                                                    : theme.palette.neutral[8],
                                            })}
                                        >
                                            {customerStatus.name}
                                        </Typography>
                                        <Button
                                            disabled={readonly}
                                            color={theme.palette.neutral[6]}
                                            cmosVariant={'typography'}
                                            Icon={EditIcon}
                                            onClick={() => startEditingRef.current()}
                                        />
                                    </>
                                }
                                setStartEditingFn={(fn) => (startEditingRef.current = fn)}
                                value={customerStatus.name}
                                onSave={handleRename}
                                hasCharacterCounter={true}
                                maxLength={60}
                                size={'small'}
                                cmosVariant="roundedGrey"
                            />
                        </Box>
                    </div>
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={DeleteIcon}
                        color={Colors.Neutral4}
                        onClick={() => setShowDeleteConfirmation(true)}
                    />
                    <div />
                    {index === 0 ? (
                        <div /> // NOTE: this div serves a purpose since parent element is a grid and we need an actual element to take up space
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={MoveUpIcon}
                            color={Colors.Neutral4}
                            onClick={moveUpHandler}
                        />
                    )}
                    {isLastItem ? (
                        <div />
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={MoveDownIcon}
                            color={Colors.Neutral4}
                            onClick={moveDownHandler}
                        />
                    )}
                </CssGrid>

                <CustomerActivitiesList
                    readonly={readonly}
                    customerStatus={customerStatus}
                    customerStatuses={customerStatuses}
                    refetch={refetch}
                />
            </Grid>
            {showDeleteConfirmation && (
                <DeleteConfirmationPopup
                    open={showDeleteConfirmation}
                    title={t(
                        'settings.aftersalesCrm.classificationOfActivities.deleteStatusModal.title'
                    )}
                    body={
                        <DeleteBodyBox>
                            <Typography sx={{ fontSize: 14 }}>{customerStatus.name}</Typography>
                            <Typography>
                                <Trans
                                    i18nKey="settings.aftersalesCrm.classificationOfActivities.deleteStatusModal.text"
                                    t={t}
                                />
                            </Typography>
                        </DeleteBodyBox>
                    }
                    cancel={t('settings.aftersalesCrm.classificationOfActivities.cancel')}
                    confirm={t('settings.aftersalesCrm.classificationOfActivities.delete')}
                    onConfirm={handleDelete}
                    onClose={() => {
                        setShowDeleteConfirmation(false);
                    }}
                />
            )}
        </>
    );
};

const useCustomerStatusItem = (customerStatus: CustomerStatusDto, onSuccess: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const { mutateAsync: updateCustomerStatus } = useMutation(
        async (request: CustomerStatusUpdateRequest) => {
            return await ActivityClassificationApi.updateCustomerStatus(request);
        },
        {
            onSuccess: () => {
                onSuccess && onSuccess();
            },
            onError: (_, request) => {
                toasters.danger(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.statusExists'
                    )
                );
            },
        }
    );

    const { mutateAsync: deleteCustomerStatus } = useMutation(
        async (statusId: string) => {
            return await ActivityClassificationApi.deleteCustomerStatus(statusId);
        },
        {
            onSuccess: () => {
                toasters.success(
                    customerStatus.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.statusDeleted'
                    )
                );
                onSuccess && onSuccess();
            },
            onError: (_, request) => {},
        }
    );

    return {
        updateCustomerStatus: updateCustomerStatus,
        deleteCustomerStatus: deleteCustomerStatus,
    };
};

const CssGrid = styled(Box)(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: '1fr auto 32px 32px 32px',
    alignItems: 'center',
    background: theme.palette.neutral[3],
    paddingLeft: '10px',
    paddingRight: 3,
    width: '100%',
    height: 38,
    '& .title': {
        ...theme.typography.h5Inter,
        color: theme.palette.neutral[8],
        fontWeight: 'bold',
    },
}));

const DeleteBodyBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    gap: 5,
    marginTop: '-8px',
    '& :first-child': {
        color: theme.palette.neutral[7],
    },
    '& :last-child': {
        color: theme.palette.neutral[6],
    },
}));
