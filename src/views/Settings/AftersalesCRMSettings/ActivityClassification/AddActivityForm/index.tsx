import { Box, Typography, styled, inputAdornmentClasses } from '@mui/material';
import {
    CustomerActivityDto,
    CustomerStatusDto,
} from 'api/settings/AftersalesCrm/activityClassification';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useMemo, useState } from 'react';
import { Modal } from 'common/components/Modal';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import Dropdown from 'common/components/Inputs/Dropdown';
import TextFieldWithCounter from '../../../../../common/components/Inputs/TextFieldWithCounter/TextFieldWithCounter';
import { TextField } from '../../../../../common/components/mui';

type AddActivityFormProps = {
    open: boolean;
    onClose: () => void;
    customerStatusId: string;
    activity?: CustomerActivityDto;
    onConfirm: (body: CustomerActivityDto) => void;
    title: string;
    onConfirmText: string;
    isLoading: boolean;
    isSuccess: boolean;
    isError: boolean;
};

const Row = styled('div')({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
});

export const AddActivityForm = ({
    open,
    onClose,
    customerStatusId,
    activity,
    onConfirm,
    title,
    onConfirmText,
    isLoading,
    isSuccess,
    isError,
}: AddActivityFormProps) => {
    const { t } = useAppTranslation();

    const [canNameSave, setCanNameSave] = useState<boolean>(false);
    const [canDescriptionSave, setCanDescriptionSave] = useState<boolean>(false);
    const [customerActivity, setCustomerActivity] = useState<CustomerActivityDto>({
        id: '',
        statusId: customerStatusId!,
        name: '',
        order: 0,
        description: '',
    });

    const handleTitleChange = (value: string) => {
        setCustomerActivity((prev) => ({
            ...prev,
            name: value,
        }));
        return value;
    };

    const handleDescriptionChange = (value: string) => {
        setCustomerActivity((prev) => ({
            ...prev,
            description: value,
        }));
        return value;
    };

    const handleStatusChange = (value: string) => {
        setCustomerActivity((prev) => ({
            ...prev,
            statusId: value,
        }));
        return true;
    };

    const handleCreateActivity = () => {
        onConfirm(customerActivity!);
    };

    const handleClose = () => {
        onClose();
    };

    useEffect(() => {
        if (!isLoading && isSuccess) {
            onClose();
        }
    }, [isLoading, isSuccess, onClose]);

    useEffect(() => {
        if (activity?.id) {
            setCustomerActivity({
                id: activity.id,
                statusId: activity.statusId,
                name: activity.name,
                order: activity.order,
                description: activity.description,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activity]);

    return (
        <>
            <Modal open={open}>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: 2.5,
                        paddingY: 6,
                        paddingX: '86px',
                        width: 960,
                    }}
                >
                    {/* Title & buttons */}
                    <Row style={{ marginBottom: 40, display: 'flex', alignItems: 'center' }}>
                        <Typography
                            sx={(theme) => ({
                                ...theme.typography.h4,
                                color: theme.palette.neutral[8],
                            })}
                        >
                            {title}
                        </Typography>
                        <Box
                            sx={{
                                display: 'flex',
                                gap: 1,
                            }}
                        >
                            <Button
                                customStyles={{ width: 164 }}
                                label={t('commonLabels.cancel')}
                                onClick={handleClose}
                                cmosVariant={'filled'}
                                color={Colors.Neutral3}
                                cmosSize={'medium'}
                            />
                            <Button
                                data-test-id="save-button"
                                customStyles={{ width: 250 }}
                                disabled={
                                    customerActivity.name == '' ||
                                    customerActivity.description == '' ||
                                    customerActivity.statusId == '' ||
                                    !canNameSave ||
                                    !canDescriptionSave
                                }
                                label={onConfirmText}
                                onClick={handleCreateActivity}
                                cmosVariant={'filled'}
                                color={Colors.Success}
                                cmosSize={'medium'}
                            />
                        </Box>
                    </Row>

                    {/* 1st Row */}
                    <Row
                        style={{
                            display: 'flex',
                            gap: 28,
                            justifyContent: 'start',
                        }}
                    >
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'start',
                                gap: 1,
                                width: '100%',
                            }}
                        >
                            <Box sx={{ display: 'flex' }}>
                                <Typography
                                    sx={(theme) => ({
                                        ...theme.typography.h6,
                                        color: theme.palette.neutral[8],
                                    })}
                                >
                                    {t(
                                        'settings.aftersalesCrm.classificationOfActivities.activityModal.nameTitle'
                                    )}
                                </Typography>
                                <Typography
                                    sx={(theme) => ({
                                        ...theme.typography.h6,
                                        color: theme.palette.primary.light,
                                    })}
                                >
                                    &nbsp;*
                                </Typography>
                            </Box>
                            <TextFieldWithCounter
                                placeholder={t(
                                    'settings.aftersalesCrm.classificationOfActivities.activityModal.namePlaceholder'
                                )}
                                value={customerActivity?.name || ''}
                                onEdit={handleTitleChange}
                                onCanSave={setCanNameSave}
                                hasCharacterCounter={true}
                                maxLength={100}
                                isInvalid={isError}
                            />
                        </Box>
                    </Row>
                    <Row
                        style={{
                            display: 'flex',
                            gap: 28,
                            justifyContent: 'start',
                        }}
                    >
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'start',
                                gap: 1,
                                width: '100%',
                            }}
                        >
                            <Box sx={{ display: 'flex' }}>
                                <Typography
                                    sx={(theme) => ({
                                        ...theme.typography.h6,
                                        color: theme.palette.neutral[8],
                                    })}
                                >
                                    {t(
                                        'settings.aftersalesCrm.classificationOfActivities.activityModal.descriptionTitle'
                                    )}
                                </Typography>
                                <Typography
                                    sx={(theme) => ({
                                        ...theme.typography.h6,
                                        color: theme.palette.primary.light,
                                    })}
                                >
                                    &nbsp;*
                                </Typography>
                            </Box>
                            <TextFieldWithCounter
                                placeholder={t(
                                    'settings.aftersalesCrm.classificationOfActivities.activityModal.descriptionPlaceholder'
                                )}
                                value={customerActivity?.description || ''}
                                onEdit={handleDescriptionChange}
                                onCanSave={setCanDescriptionSave}
                                multiline={true}
                                hasCharacterCounter={true}
                                maxLength={250}
                                rows={2}
                            />
                        </Box>
                    </Row>
                </Box>
            </Modal>
        </>
    );
};
