import PageContent from 'views/Components/Page';
import { useAppTranslation } from '../../../../common/hooks/useAppTranslation';
import { Box, styled, useTheme } from '@mui/material';
import { useMemo, useRef } from 'react';
import ActivityClassificationApi, {
    CustomerStatusCreateRequest,
    CustomerStatusUpdateRequest,
} from 'api/settings/AftersalesCrm/activityClassification';
import useDocumentTitle from '../../../../common/hooks/useDocumentTitle';
import { Button } from '../../../../common/components/Button';
import { CustomerStatusesList } from './CustomerStatusesList';
import useToasters from '../../../../common/hooks/useToasters';
import { useMutation, useQuery } from '@tanstack/react-query';
import ResettableTextInputWithCustomControl from '../../../../common/components/Inputs/ResettableTextFieldWithCustomControl/ResettableTextInputWithCustomControl';
import image from 'assets/images/man_on_chair.png';

export const ACTIVITY_CLASSIFICATIONS_QUERY_KEY = ['settings', 'activityClassifications'];

export default function ActivityClassification() {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const startNewStatusEditingRef = useRef(() => {});
    const { customerStatuses, isLoading, refetch, updateCustomerStatus, createCustomerStatus } =
        useCustomerStatuses();

    useDocumentTitle(
        `${t('titles.settings.settings')} - ${t(
            'settings.aftersalesCrm.classificationOfActivities.title'
        )}`
    );

    const handleMove = async (index: number, direction: number) => {
        const promises = [];
        const body1 = {
            id: customerStatuses[index].id,
            order: customerStatuses[index + direction].order,
        };
        const body2 = {
            id: customerStatuses[index + direction].id,
            order: customerStatuses[index].order,
        };

        promises.push(updateCustomerStatus(body1));
        promises.push(updateCustomerStatus(body2));
        await Promise.all(promises);
    };

    const handleCreate = async (value: string): Promise<boolean> => {
        return await createCustomerStatus({
            name: value,
        });
    };

    return (
        <>
            <PageContent paddedX>
                <Box
                    sx={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'end',
                        alignItems: 'center',
                        marginTop: 4,
                        marginBottom: 4,
                    }}
                >
                    <Box sx={{ width: 320, display: 'flex' }}>
                        <ResettableTextInputWithCustomControl
                            control={
                                <Button
                                    color={theme.palette.primary.main}
                                    cmosVariant={'filled'}
                                    blockMode
                                    label={t(
                                        'settings.aftersalesCrm.classificationOfActivities.addStatus'
                                    )}
                                    onClick={() => startNewStatusEditingRef.current()}
                                />
                            }
                            setStartEditingFn={(fn) => (startNewStatusEditingRef.current = fn)}
                            onSave={handleCreate}
                            hasCharacterCounter={true}
                            maxLength={60}
                            size={'small'}
                            cmosVariant="roundedGrey"
                            placeholder={t(
                                'settings.aftersalesCrm.classificationOfActivities.addStatusPlaceholder'
                            )}
                        />
                    </Box>
                </Box>
                {customerStatuses.length === 0 && (
                    <EmptyPage>
                        <EmptyPageImage />
                        <EmptyPageTitle>
                            {t('settings.aftersalesCrm.classificationOfActivities.emptyPageTitle')}
                        </EmptyPageTitle>
                        <EmptyPageBody>
                            {t('settings.aftersalesCrm.classificationOfActivities.emptyPageBody')}
                        </EmptyPageBody>
                    </EmptyPage>
                )}
                {customerStatuses.length > 0 && (
                    <CustomerStatusesList
                        customerStatuses={customerStatuses}
                        isLoadingInProgress={isLoading}
                        handleMove={handleMove}
                        refetch={refetch}
                    />
                )}
            </PageContent>
        </>
    );
}

function useCustomerStatuses() {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const { data, isLoading, refetch } = useQuery(
        ACTIVITY_CLASSIFICATIONS_QUERY_KEY,
        () => ActivityClassificationApi.getCustomerStatuses(),
        {
            cacheTime: Infinity,
            refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
            refetchInterval: 120000,
        }
    );
    const customerStatuses = useMemo(() => data?.customerStatuses ?? [], [data]);

    const { mutateAsync: createCustomerStatus } = useMutation(
        async (request: CustomerStatusCreateRequest) => {
            return await ActivityClassificationApi.createCustomerStatus(request);
        },
        {
            onSuccess: (_, request) => {
                toasters.success(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.statusCreated'
                    )
                );
                refetch();
            },
            onError: (_, request) => {
                toasters.danger(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.statusExists'
                    )
                );
            },
        }
    );

    const { mutateAsync: updateCustomerStatus } = useMutation(
        async (request: CustomerStatusUpdateRequest) => {
            return await ActivityClassificationApi.updateCustomerStatus(request);
        },
        {
            onSuccess: (_, request) => {
                refetch();
            },
            onError: (_, request) => {
                toasters.danger(
                    request.name,
                    t(
                        'settings.aftersalesCrm.classificationOfActivities.notifications.statusExists'
                    )
                );
            },
        }
    );

    return {
        customerStatuses: customerStatuses,
        isLoading,
        refetch,
        createCustomerStatus: createCustomerStatus,
        updateCustomerStatus: updateCustomerStatus,
    };
}

const EmptyPage = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '20px 0 100px',
}));
const EmptyPageImage = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    backgroundImage: `url(${image})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'contain',
    height: 350,
    width: 450,
}));
const EmptyPageTitle = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Inter',
    fontWeight: 700,
    fontSize: 34,
    color: '#0069FF',
    height: 41,
    marginTop: 30,
}));
const EmptyPageBody = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Inter',
    fontWeight: 400,
    fontSize: 18,
    color: '#6A6E72',
    height: 22,
    marginTop: 10,
}));
