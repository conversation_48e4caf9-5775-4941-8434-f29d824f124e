import { Box } from '@mui/material';
import { CustomerStatusDto } from 'api/settings/AftersalesCrm/activityClassification';
import AreaSpinner from 'common/components/AreaSpinner';
import { CustomerStatusItem } from '../CustomerStatusItem';

type CustomerStatusesListProps = {
    isLoadingInProgress: boolean;
    customerStatuses: CustomerStatusDto[];
    refetch: () => void;
    handleMove: (index: number, direction: number) => void;
    readonly?: boolean;
};

export const CustomerStatusesList = ({
    customerStatuses,
    isLoadingInProgress,
    handleMove,
    refetch,
    readonly,
}: CustomerStatusesListProps) => {
    return (
        <>
            {isLoadingInProgress ? (
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        alignContent: 'center',
                        width: '100%',
                        height: 'calc(100vh - 320px)!important',
                    }}
                >
                    <AreaSpinner />
                </Box>
            ) : (
                customerStatuses &&
                customerStatuses.map((status, index) => (
                    <CustomerStatusItem
                        readonly={readonly}
                        key={`CustomerStatus-${status.id}`}
                        customerStatus={status}
                        customerStatuses={customerStatuses}
                        index={index}
                        isLastItem={index === customerStatuses.length - 1}
                        refetch={refetch}
                        handleMove={handleMove}
                    />
                ))
            )}
        </>
    );
};
