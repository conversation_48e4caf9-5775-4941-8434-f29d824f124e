import { styled } from '@mui/material';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import AftersalesCrmSettingsApi, {
    PendingVehicleImportDto,
} from 'api/settings/AftersalesCrmSettingsApi';
import { CloudUploadIcon } from 'common/components/Icons/CloudUploadIcon';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { crmVehiclesImportActions, selectInitiatedImportIds } from 'store/slices/crmVehiclesImport';
import { selectUserAuthorizationStatus } from 'store/slices/user';

const IMPORT_VEHICLE_NOTIF_FLAG = 'crm:importVehicles:pendingImportFlag';

export function CrmImportVehicleInit() {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const pendingImports = usePendingImports();
    const dispatch = useAppDispatch();

    const authStatus = useAppSelector(selectUserAuthorizationStatus);

    useEffect(() => {
        if (authStatus === 'maybe') return;

        if (authStatus === false) {
            toasters.forEach((t) => {
                if (t.getData().data[IMPORT_VEHICLE_NOTIF_FLAG] === true) {
                    t.dismiss();
                }
            });

            return;
        } else if (authStatus === true) {
            for (const pendingImport of pendingImports) {
                if (pendingImport.totalBatches === 0) {
                    // skip invalid import
                    continue;
                }

                if (pendingImport.finishedAt) {
                    // IMPORTANT: this callback may be called when this component is
                    // destroyed (probably not, but in theory) so please do not use any
                    // component state (like useState) in here
                    const onClose = () =>
                        dispatch(crmVehiclesImportActions.removeInitiatedImport(pendingImport.id));

                    if (pendingImport.success) {
                        const duration = Math.max(
                            0,
                            30000 -
                                DateTime.now()
                                    .diff(DateTime.fromISO(pendingImport.finishedAt))
                                    .shiftTo('milliseconds').milliseconds
                        );

                        if (duration > 0)
                            toasters.success(
                                t(
                                    'settings.aftersalesCrm.importVehicles.pendingImports.completedText'
                                ),
                                t('settings.aftersalesCrm.importVehicles.pendingImports.completed'),
                                {
                                    duration,
                                    id: pendingImport.id,
                                    isDetachable: true,
                                    onClose,
                                    data: { [IMPORT_VEHICLE_NOTIF_FLAG]: true },
                                }
                            );
                    } else if (pendingImport.errors.length === pendingImport.totalBatches) {
                        // complete failure due to internal error
                        toasters.danger(
                            t(
                                'settings.aftersalesCrm.importVehicles.pendingImports.failedImportInternalErrorText'
                            ),
                            t(
                                'settings.aftersalesCrm.importVehicles.pendingImports.failedImportInternalError'
                            ),
                            {
                                duration: Infinity,
                                id: pendingImport.id,
                                isDetachable: true,
                                onClose,
                                data: { [IMPORT_VEHICLE_NOTIF_FLAG]: true },
                            }
                        );
                    } else {
                        // partial failure due to internal error
                        const duration = Math.max(
                            0,
                            60000 -
                                DateTime.now()
                                    .diff(DateTime.fromISO(pendingImport.finishedAt))
                                    .shiftTo('milliseconds').milliseconds
                        );

                        toasters.warning(
                            <>
                                {t(
                                    'settings.aftersalesCrm.importVehicles.pendingImports.partialFailInternalErrorText.line1',
                                    {
                                        successful: pendingImport.completedVehicles,
                                        total: pendingImport.totalVehicles,
                                    }
                                )}
                                <br />
                                <br />
                                <span style={{ fontWeight: 'bold', color: 'var(--danger)' }}>
                                    {t(
                                        'settings.aftersalesCrm.importVehicles.pendingImports.partialFailInternalErrorText.line2'
                                    )}
                                </span>
                                <br />
                                {t(
                                    'settings.aftersalesCrm.importVehicles.pendingImports.partialFailInternalErrorText.line3'
                                )}
                            </>,
                            t(
                                'settings.aftersalesCrm.importVehicles.pendingImports.partialFailInternalError'
                            ),
                            {
                                duration,
                                id: pendingImport.id,
                                isDetachable: true,
                                onClose,
                                data: { [IMPORT_VEHICLE_NOTIF_FLAG]: true },
                            }
                        );
                    }
                } else {
                    toasters.info(
                        <>
                            {t(
                                'settings.aftersalesCrm.importVehicles.pendingImports.inProgressText'
                            )}

                            <ArrowTooltip
                                content={`${
                                    pendingImport.completedCustomers +
                                    pendingImport.completedVehicles
                                }/${pendingImport.totalCustomers + pendingImport.totalVehicles}`}
                            >
                                <ProgressLine
                                    value={
                                        pendingImport.completedBatches / pendingImport.totalBatches
                                    }
                                />
                            </ArrowTooltip>
                        </>,
                        t('settings.aftersalesCrm.importVehicles.pendingImports.inProgress'),
                        {
                            duration: Infinity,
                            id: pendingImport.id,
                            isDetachable: true,
                            closeable: false,
                            data: { [IMPORT_VEHICLE_NOTIF_FLAG]: true },
                            icon: <CloudUploadIcon fill="white" />,
                        }
                    );
                }
            }
        }
    }, [toasters, pendingImports, t, dispatch, authStatus]);

    return null;
}

const ProgressLine = styled('div', {
    shouldForwardProp: (prop) => prop !== 'value',
})<{ value: number }>(({ value }) => ({
    height: 6,
    backgroundColor: '#F6F6F6',
    borderRadius: 3,
    marginTop: 20,
    position: 'relative',

    '&::before': {
        content: '""',
        display: 'block',
        position: 'absolute',
        height: '100%',
        left: 0,
        backgroundColor: 'var(--success)',
        width: `max(${value * 100}%, 6px)`,
        borderRadius: 3,
    },
}));

export function usePendingImportRefetch() {
    const queryClient = useQueryClient();

    return useCallback(() => {
        queryClient.fetchQuery({
            queryFn: AftersalesCrmSettingsApi.getPendingVehicleImports,
            queryKey: ['settings', 'crm', 'vehicle-import', 'pending'],
        });
    }, [queryClient]);
}

function usePendingImports(): PendingVehicleImportDto[] {
    const isLoggedIn = useAppSelector(selectUserAuthorizationStatus) === true;
    const initiatedIds = useAppSelector(selectInitiatedImportIds);
    const loc = useLocation();
    const dispatch = useAppDispatch();

    const { data } = useQuery({
        queryFn: async () => {
            const imports = await AftersalesCrmSettingsApi.getPendingVehicleImports();
            const missingIds = initiatedIds.filter((id) => imports.every((i) => i.id !== id));
            for (const missingId of missingIds) {
                dispatch(crmVehiclesImportActions.removeInitiatedImport(missingId));
            }
            return imports;
        },
        queryKey: ['settings', 'crm', 'vehicle-import', 'pending'],
        refetchInterval(data, _query) {
            if (!data) return false;
            if (data.some((imp) => !imp.finishedAt)) {
                return 2000;
            }

            return 30000;
        },
        refetchOnWindowFocus: true,
        refetchOnReconnect: 'always',
        refetchIntervalInBackground: initiatedIds.length > 0,
        // enable only if some ids were marked as initiated and also if import vehicles page is open
        enabled:
            isLoggedIn &&
            (initiatedIds.length > 0 ||
                loc.pathname === '/settings/aftersales-crm/import-vehicles'),
    });

    const imports = useMemo(() => {
        if (!data) return [];
        return data.filter((x) => x.totalBatches > 0).filter((x) => initiatedIds.includes(x.id));
    }, [data, initiatedIds]);

    return imports;
}
