import { Box, Divider, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { getErrorMessage } from 'api/error';
import AftersalesCRMSettingsApi from 'api/settings/AftersalesCrmSettingsApi';
import { Button } from 'common/components/Button';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useState } from 'react';
import { useAppDispatch } from 'store';
import { crmVehiclesImportActions } from 'store/slices/crmVehiclesImport';
import { getFileExtension } from 'utils/Files';
import DragAndDropFileInput from '../../../Components/DragAndDropFileInput';
import PageContent from '../../../Components/Page';
import { usePendingImportRefetch } from './notifications';

export default function ImportVehicles() {
    const maxAllowedSize = 5000000;
    const toasters = useToasters();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const [file, setFile] = useState<File | null>(null);
    const refetch = usePendingImportRefetch();

    const uploadFileMutation = useMutation(
        (file: File) => {
            let fileType: 'xlsx' | 'csv';

            if (file.type === 'text/csv') {
                fileType = 'csv';
            } else if (
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ) {
                fileType = 'xlsx';
            } else {
                throw new Error('unsupported file type: ' + file.type);
            }

            return AftersalesCRMSettingsApi.importVehicles(file, fileType);
        },
        {
            onSuccess: (response) => {
                if (response.importId) {
                    dispatch(crmVehiclesImportActions.addInitiatedImport(response.importId));
                }

                if (response.isFileValid) {
                    if (!response.failedImportResult) {
                        toasters.success(
                            t('settings.aftersalesCrm.importVehicles.successBody'),
                            t('settings.aftersalesCrm.importVehicles.successTitle')
                        );
                        setFile(null);
                    } else {
                        toasters.warning(
                            <>
                                {t('settings.aftersalesCrm.importVehicles.partialImport.text', {
                                    successful: response.failedImportResult.importedRows,
                                    total: response.failedImportResult.totalRows,
                                })}{' '}
                                <br />
                                <br />
                                <a
                                    target="_blank"
                                    href={response.failedImportResult.failedRowsFileUrl}
                                    rel="noreferrer"
                                >
                                    {t('settings.aftersalesCrm.importVehicles.partialImport.link')}
                                </a>
                            </>,
                            t('toasters.documentPartiallyUploadedTitle'),
                            { duration: 15000 }
                        );
                    }
                } else {
                    console.error(
                        'Invalid CSV file: ' + (response.parsingError || 'unknown error')
                    );
                    toasters.danger(
                        t('settings.aftersalesCrm.importVehicles.invalidVehicleListBody'),
                        t('settings.aftersalesCrm.importVehicles.invalidVehicleListTitle')
                    );
                }

                refetch();
            },
            onError: (error) => {
                toasters.danger(getErrorMessage(error), t('toasters.errorOccurred'));
            },
        }
    );

    function handleClickHandler() {
        if (file) uploadFileMutation.mutate(file);
    }

    const downloadTemplate = useMutation({
        mutationFn: () => {
            // artificial to make loader less jarring
            return Promise.all([
                AftersalesCRMSettingsApi.downloadImportVehiclesTemplate(),
                new Promise((r) => setTimeout(r, 500)),
            ]);
        },
    });

    const fileChanged = (file: File | null) => {
        const extension = getFileExtension(file);
        setFile(file);

        if (!file || !extension || (extension !== 'csv' && extension !== 'xlsx')) {
            processInvalidFileFormat();
        }

        if (file && file.size > maxAllowedSize) {
            processFileSizeLimitExceeded();
        }
    };

    const processInvalidFileFormat = () => {
        toasters.danger(
            t('settings.aftersalesCrm.importVehicles.incorrectFormatBody'),
            t('settings.aftersalesCrm.importVehicles.incorrectFormatTitle')
        );
        setFile(null);
    };

    const processFileSizeLimitExceeded = () => {
        toasters.danger(
            t('settings.aftersalesCrm.importVehicles.fileSizeLimitExceededBody'),
            t('settings.aftersalesCrm.importVehicles.fileSizeLimitExceededTitle')
        );
        setFile(null);
    };

    return (
        <PageContent>
            <Box sx={{ width: '100%', height: '100%', padding: '30px 50px 20px 50px' }}>
                <Box sx={{ display: 'flex', justifyContent: 'end' }}>
                    <Button
                        w="lg"
                        disabled={!file || uploadFileMutation.isLoading}
                        color={'success'}
                        label={t('settings.aftersalesCrm.importVehicles.uploadFile')}
                        cmosVariant={'filled'}
                        onClick={handleClickHandler}
                        showLoader={uploadFileMutation.isLoading}
                    />
                </Box>
                <Box sx={{ marginTop: 2 }}>
                    <DragAndDropFileInput
                        acceptedFormats={['csv', 'xlsx']}
                        onFileSet={fileChanged}
                        sx={{
                            gridTemplateColumns: '1fr 1fr !important',
                            gap: '32px !important',
                        }}
                    />
                </Box>
                <Divider sx={{ my: 4 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                        <StyledBoldP>
                            {t('settings.aftersalesCrm.importVehicles.notes')}:
                        </StyledBoldP>
                        <StyledP>
                            {t('settings.aftersalesCrm.importVehicles.acceptedFormat')}
                        </StyledP>
                        <StyledP>
                            {t('settings.aftersalesCrm.importVehicles.maximumAllowedSize')}
                        </StyledP>
                    </div>
                    <div>
                        <Button
                            label={t('settings.aftersalesCrm.importVehicles.downloadTemplate')}
                            cmosVariant={'typography'}
                            Icon={DownloadIcon}
                            iconPosition="right"
                            showLoader={downloadTemplate.isLoading}
                            disabled={downloadTemplate.isLoading}
                            onClick={() => downloadTemplate.mutate()}
                        />
                    </div>
                </Box>
            </Box>
        </PageContent>
    );
}

const StyledP = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));

const StyledBoldP = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
}));
