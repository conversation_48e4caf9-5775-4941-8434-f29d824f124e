import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import SettingsRouterParameterBasedTabs from '../common/SettingsRouterParameterBasedTabs';
import ImportVehicles from './ImportVehicles';
import ActivityClassification from './ActivityClassification';

export default function AftersalesCRMSettings() {
    const { t } = useAppTranslation();

    return (
        <SettingsRouterParameterBasedTabs
            tabs={[
                {
                    value: 'general',
                    label: t('settings.aftersalesCrm.general'),
                    content: <div>TODO general</div>,
                },
                {
                    value: 'classification-of-activities',
                    label: t('settings.aftersalesCrm.classificationOfActivities.title'),
                    content: <ActivityClassification />,
                },
                {
                    value: 'prospection-priorities',
                    label: t('settings.aftersalesCrm.prospectionPriorities'),
                    content: <div>TODO prospection-priorities</div>,
                },
                {
                    value: 'classification-of-appointments',
                    label: t('settings.aftersalesCrm.classificationOfAppointments'),
                    content: <div>TODO classification-of-appointments</div>,
                },
                {
                    value: 'import-vehicles',
                    label: t('settings.aftersalesCrm.importVehicles.title'),
                    content: <ImportVehicles />,
                },
            ]}
            urlPattern={ROUTES.SETTINGS.AFTERSALES_CRM.PATH}
            parameterName={'section'}
        />
    );
}
