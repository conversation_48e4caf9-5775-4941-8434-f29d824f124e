import { Box, Divider, styled } from '@mui/material';
import CustomersApi from 'api/customers';
import ImportCustomersApi from 'api/settings/ImportCustomers';
import { Button } from 'common/components/Button';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';
import { Interweave } from 'interweave';
import { useState } from 'react';
import { getFileExtension } from 'utils/Files';
import DragAndDropFileInput from 'views/Components/DragAndDropFileInput';

const ImportCustomersSettings = () => {
    const maxAllowedSize = 2000000;

    const toasters = useToasters();
    const { t } = useAppTranslation();

    const [file, setFile] = useState<File | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const uploadFile = async () => {
        try {
            setIsLoading(true);
            if (!file) return;
            const response = await ImportCustomersApi.uploadCustomersFile(file);
            if (response.isFileValid && response.failedExcelImportResult == null) {
                toasters.success(
                    t('settings.prospections.importCustomers.successBody'),
                    t('settings.prospections.importCustomers.successTitle')
                );
                setFile(null);
            } else {
                toasters.warning(
                    <Interweave
                        content={t('toasters.documentPartiallyUploadedText', {
                            failedRowsFileUrl: response.failedExcelImportResult.failedRowsFileUrl,
                            totalRows: response.failedExcelImportResult.totalRows,
                            importedRows: response.failedExcelImportResult.importedRows,
                        })}
                    />,
                    t('toasters.documentPartiallyUploadedTitle'),
                    { duration: 15000 }
                );
            }
        } catch (e) {
            console.error(e);
        } finally {
            setIsLoading(false);
        }
    };

    const downloadTemplate = async () => {
        await CustomersApi.downloadTemplate(
            t('settings.prospections.importCustomers.templateFileName')
        );
    };

    const fileChanged = (file: File | null) => {
        const extension = getFileExtension(file);
        setFile(file);

        if (!file || !extension || extension !== 'xlsx' || file.size > maxAllowedSize) {
            processInvalidFile();
        }
    };

    const processInvalidFile = () => {
        toasters.danger(
            t('settings.prospections.importCustomers.incorrectFormatBody'),
            t('settings.prospections.importCustomers.incorrectFormatTitle')
        );
        setFile(null);
    };

    return (
        <Box style={{ width: '100%', height: '100%', paddingBottom: 68 }}>
            <DivRoot>
                <Box sx={{ display: 'flex', justifyContent: 'end' }}>
                    <Button
                        w="lg"
                        disabled={!file || isLoading}
                        color={Colors.Success}
                        label={t('settings.prospections.importCustomers.uploadFile')}
                        cmosVariant={'filled'}
                        onClick={uploadFile}
                        showLoader={isLoading}
                    />
                </Box>
                <Box sx={{ marginTop: 2 }}>
                    <DragAndDropFileInput acceptedFormats={['xlsx']} onFileSet={fileChanged} />
                </Box>
                <Divider sx={{ my: 4 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                        <p
                            style={{
                                ...FontSecondary(HeaderStyles.H5_14px, true, Colors.Neutral7),
                            }}
                        >
                            {t('settings.prospections.importCustomers.notes')}:
                        </p>
                        <p
                            style={{
                                ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Neutral7),
                            }}
                        >
                            {t('settings.prospections.importCustomers.acceptedFormat')}
                        </p>
                        <p
                            style={{
                                ...FontSecondary(HeaderStyles.H6_12px, false, Colors.Neutral7),
                            }}
                        >
                            {t('settings.prospections.importCustomers.maximumAllowedSize')}
                        </p>
                    </div>
                    <div>
                        <Button
                            color={Colors.CM2}
                            label={t('settings.prospections.importCustomers.downloadTemplate')}
                            cmosVariant={'typography'}
                            Icon={DownloadIcon}
                            iconPosition="right"
                            onClick={downloadTemplate}
                        />
                    </div>
                </Box>
            </DivRoot>
        </Box>
    );
};

export default ImportCustomersSettings;

const DivRoot = styled('div')({
    background: 'var(--neutral1)',
    borderRadius: 12,
    border: `1px solid #DBDCDD`,
    width: '100%',
    padding: '45px 50px',
    flexDirection: 'column',
});
