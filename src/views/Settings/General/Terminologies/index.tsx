import { Box, styled } from '@mui/material';
import TerminologiesAPI, { TerminologyType } from 'api/Terminologies';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { TerminologyDto, terminologyActions } from 'store/slices/terminologies';
import { selectTerminologies } from 'store/slices/terminologies/selectors';
import PageContent from 'views/Components/Page';
import { SettingsSection } from 'views/Settings/common';
import { ValidatableTextSettingControl } from 'views/Settings/common/TextSettingControl';

export default function Terminologies() {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const toasters = useToasters();

    const terminologies = useAppSelector(selectTerminologies);

    const getMaxLength = useCallback((term: TerminologyDto): number => {
        const terminologyMaxLengths: Partial<Record<TerminologyType, number>> = {
            JobDescription: 51,
        };
        return terminologyMaxLengths[term.type] ?? term.originalTitle.length;
    }, []);

    const save = async (changes: Partial<TerminologyDto>) => {
        const term = changes as TerminologyDto;

        if (term.id === undefined) return;

        await TerminologiesAPI.updateTerminology(term.id, term.alias ?? '').then(() => {
            toasters.success(
                t('settings.terminologies.updateToastBody'),
                t('settings.terminologies.updateToastTitle')
            );
        });

        dispatch(terminologyActions.update(term));
    };

    const validate = useCallback(
        (alias: string, term: TerminologyDto): boolean => {
            const maxLength = getMaxLength(term);

            const isInvalid = alias?.length > maxLength;
            if (isInvalid) {
                toasters.danger(
                    t('settings.terminologies.invalidLengthBody'),
                    t('settings.terminologies.invalidLengthTitle')
                );
            }

            return isInvalid;
        },
        [getMaxLength, t, toasters]
    );

    return (
        <PageContent>
            <Box
                display="flex"
                flexDirection="column"
                style={{
                    padding: '0 50px',
                }}
            >
                <SettingsSection
                    label={t('settings.terminologies.header')}
                    styles={{ gap: 10, width: 750 }}
                    CustomTitle={CustomTitle}
                >
                    {terminologies.map((term) => {
                        return (
                            <ValidatableTextSettingControl
                                object={term}
                                onValidate={validate}
                                key={term.id}
                                name={`term_${term.id}`}
                                alignItems="center"
                                label={<Box display="flex">{term.originalTitle}</Box>}
                                placeholder={t('settings.terminologies.placeholder')}
                                value={term.alias ?? ''}
                                maxLength={getMaxLength(term) + 1}
                                onSave={(v) => save({ ...term, alias: v })}
                                hasCharacterCounter={true}
                            />
                        );
                    })}
                </SettingsSection>
            </Box>
        </PageContent>
    );
}

const CustomTitle = styled('div')({
    marginBottom: 14,
});
