import { Box, IconButton } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentsApi, { AppointmentOriginDto } from 'api/appointments';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import AutoReadonlyTextField from 'common/components/Inputs/AutoReadonlyTextField';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';

type OriginEditorProps = {
    origin: AppointmentOriginDto;
    onChange: (origin: AppointmentOriginDto) => void;
    onDeleted: (origin: AppointmentOriginDto) => void;
};

export default function OriginEditor({ origin, onChange, onDeleted }: OriginEditorProps) {
    const [readonly, setReadonly] = useState(true);
    const [name, setName] = useState(origin.name);
    const [nameCopy, setCopy] = useState(origin.name);

    useEffect(() => setName(origin.name), [origin]);

    const save = useMutation(
        async () => {
            await AppointmentsApi.updateOrigin(origin.id, { name });
            return { id: origin.id, name };
        },
        {
            onSuccess: onChange,
        }
    );

    const delete_ = useMutation(() => AppointmentsApi.deleteOrigin(origin.id), {
        onSuccess: () => onDeleted(origin),
    });

    const handleEditClick = useCallback(() => {
        setReadonly(false);
        setCopy(name);
    }, [name, setReadonly, setCopy]);

    const handleSaveClick = useCallback(() => {
        setReadonly(true);
        save.mutate();
    }, [setReadonly, save]);

    return (
        <Box display="flex" alignItems="center" gap="6px">
            <AutoReadonlyTextField
                sx={{ flexGrow: 1 }}
                name={`origin-${origin.id}`}
                readonly={readonly}
                value={name}
                onChange={(event) => setName(event.target.value)}
                onEditClick={handleEditClick}
                onEnterPress={handleSaveClick}
                onBlur={handleSaveClick}
                onSaveClick={handleSaveClick}
            />
            {!readonly ? (
                <IconButton
                    size="small"
                    onClick={() => {
                        setReadonly(true);
                        setName(nameCopy);
                    }}
                >
                    <CloseIcon fill={Colors.Neutral6} />
                </IconButton>
            ) : (
                <IconButton size="small" onClick={() => delete_.mutate()}>
                    <DeleteIcon fill={Colors.Neutral6} />
                </IconButton>
            )}
        </Box>
    );
}
