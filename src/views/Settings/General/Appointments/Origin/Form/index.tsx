import { IconButton, InputAdornment, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useMutation } from '@tanstack/react-query';
import AppointmentsApi, { AppointmentOriginDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';
import { isErrorResponse } from 'services/Server';

type AppointmentOriginFormProps = {
    onSave: (origin: AppointmentOriginDto) => void;
};

const AppointmentOriginForm = ({ onSave }: AppointmentOriginFormProps) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [origin, setOrigin] = useState<string>('');
    const [inputMode, setInputMode] = useState<boolean>(false);

    const save = useMutation(
        () => {
            return AppointmentsApi.createOrigin({
                name: origin,
            });
        },
        {
            onSuccess: (id) => {
                toasters.success(
                    t('settings.appointment.appointmentOriginSavedSuccessfully'),
                    t('settings.appointment.appointmentOriginCreated')
                );
                setOrigin('');
                setInputMode(false);
                onSave({ name: origin, id });
            },
            onError: (error) => {
                if (isErrorResponse(error)) {
                    toasters.warning(
                        t('settings.appointment.appointmentOriginIsDuplicated'),
                        t('settings.appointment.appointmentOriginNotCreated')
                    );
                }
            },
        }
    );

    return (
        <Grid container spacing={0}>
            {!inputMode && (
                <Grid container spacing={0}>
                    <Grid item xs={6}>
                        <StyledButton
                            blockMode
                            cmosVariant={'stroke'}
                            iconPosition="right"
                            Icon={PlusIcon}
                            color={Colors.Neutral3}
                            onClick={() => {
                                setInputMode(true);
                            }}
                            label={t('settings.appointment.createNewOrigin')}
                        />
                    </Grid>
                </Grid>
            )}
            {inputMode && (
                <Grid container spacing={0}>
                    <Grid item xs={6}>
                        <Grid container spacing={1}>
                            <Grid item xs={11}>
                                <TextFormField
                                    autoFocus
                                    name="new-origin"
                                    label=""
                                    placeholder={t('settings.appointment.enterAppointmentOrigin')}
                                    type="text"
                                    isRequired={false}
                                    readonly={save.isLoading}
                                    value={origin}
                                    onChange={(event) => {
                                        setOrigin(event.target.value);
                                    }}
                                    onEnterPress={() => {
                                        if (origin.trim().length) save.mutate();
                                    }}
                                    enableEnterComplete
                                    cmosVariant="roundedGrey"
                                    showLoader={save.isLoading}
                                    endAdornment={
                                        <InputAdornment position="end">
                                            <IconButton
                                                size="small"
                                                onClick={() => {
                                                    if (origin.trim().length) save.mutate();
                                                }}
                                            >
                                                <CheckIcon size={IconSize.M} />
                                            </IconButton>
                                        </InputAdornment>
                                    }
                                />
                            </Grid>
                            <Grid item xs={1}>
                                <Button
                                    color={Colors.Neutral3}
                                    cmosVariant={'typography'}
                                    Icon={CloseIcon}
                                    label=""
                                    onClick={() => {
                                        if (save.isLoading) return;
                                        setInputMode(false);
                                    }}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            )}
        </Grid>
    );
};

const StyledButton = styled(Button)({
    '& > div': {
        justifyContent: 'space-between',
        width: '100%',
    },
});

export default AppointmentOriginForm;
