import { Box, styled } from '@mui/material';
import AppointmentsApi, { AppointmentOriginDto } from 'api/appointments';
import { useCallback, useEffect, useState } from 'react';
import AppointmentOriginForm from './Form';
import OriginEditor from './Form/OriginEditor';

const ListRoot = styled('ul')({
    listStyle: 'none',
    padding: 0,
    margin: '0 0 10px 0',
    display: 'flex',
    flexDirection: 'column',
    width: '80%',
    gap: 10,
});

const AppointmentOrigin = () => {
    const [appointmentOrigins, setAppointmentOrigins] = useState<AppointmentOriginDto[]>([]);

    useEffect(() => {
        getListByRepairShop();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getListByRepairShop = async () => {
        const response = await AppointmentsApi.getOrigins();

        if (response && response.length) {
            setAppointmentOrigins(response);
        }
    };

    const onOriginDeleted = useCallback((o: AppointmentOriginDto) => {
        setAppointmentOrigins((v) => v.filter((x) => x.id !== o.id));
    }, []);

    const onOriginChanged = useCallback((o: AppointmentOriginDto) => {
        setAppointmentOrigins((v) => v.map((x) => (x.id === o.id ? o : x)));
    }, []);

    return (
        <Box>
            <ListRoot>
                {appointmentOrigins && appointmentOrigins.length
                    ? appointmentOrigins.map((origin, index) => (
                          <OriginEditor
                              key={origin.id}
                              origin={origin}
                              onDeleted={onOriginDeleted}
                              onChange={onOriginChanged}
                          />
                      ))
                    : undefined}
            </ListRoot>

            <AppointmentOriginForm
                onSave={(event) => {
                    setAppointmentOrigins([...appointmentOrigins, event]);
                }}
            />
        </Box>
    );
};

export default AppointmentOrigin;
