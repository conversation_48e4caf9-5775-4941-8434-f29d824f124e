import { styled } from '@mui/material';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import Switch, { switchClasses } from '@mui/material/Switch';
import Typography from '@mui/material/Typography';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import AppointmentSettingsAPI from 'api/AppointmentSettings';
import { RepairShopWorkingDayDto, ShopScheduleApi } from 'api/settings';
import SiteForAppointmentsApi from 'api/siteForAppointments';
import { Button } from 'common/components/Button';
import { Checkbox, TextField } from 'common/components/Inputs';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { useApiCall } from 'common/hooks/useApiCall';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import {
    AppointmentSettingsDto,
    AppointmentSettingWorkingDaysDto,
    Direction,
    WeekDay,
} from 'datacontracts/Scheduler';
import { ChangeEvent, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from 'store';
import { globalSettingsActions } from 'store/slices/globalSettingsSlice';
import {
    APPOINTMENT_SETTINGS_QUERY_KEY,
    useRepairShopAppointmentSettings,
} from 'views/Appointments/common';
import AppointmentOrigin from './Origin';
import useStyles from './css';

const durationList: OptionData<number>[] = [
    { label: '5 min', value: 5 },
    { label: '10 min', value: 10 },
    { label: '15 min', value: 15 },
    { label: '20 min', value: 20 },
    { label: '30 min', value: 30 },
    { label: '45 min', value: 45 },
    { label: '60 min', value: 60 },
];

const timeAfterList: OptionData<number>[] = [
    { label: '5 min', value: 5 },
    { label: '10 min', value: 10 },
    { label: '15 min', value: 15 },
    { label: '20 min', value: 20 },
    { label: '30 min', value: 30 },
    { label: '60 min', value: 60 },
];

const defaultWorkingDays: RepairShopWorkingDayDto[] = [
    {
        active: true,
        dayNumber: 1,
        opening: '08:00:00',
        closing: '19:00:00',
    },
    {
        active: true,
        dayNumber: 2,
        opening: '08:00:00',
        closing: '19:00:00',
    },
    {
        active: true,
        dayNumber: 3,
        opening: '08:00:00',
        closing: '19:00:00',
    },
    {
        active: true,
        dayNumber: 4,
        opening: '08:00:00',
        closing: '19:00:00',
    },
    {
        active: true,
        dayNumber: 5,
        opening: '08:00:00',
        closing: '19:00:00',
    },
    {
        active: true,
        dayNumber: 6,
        opening: '08:00:00',
        closing: '15:00:00',
    },
    {
        active: false,
        dayNumber: 0,
        opening: '08:00:00',
        closing: '15:00:00',
    },
];

const ToggleOption = styled('div')({
    marginTop: 16,
    marginBottom: 9,
    width: '100%',
    display: 'flex',
    justifyContent: 'space-start',
});

const StyledSwitch = styled(Switch)({
    [`& .${switchClasses.switchBase}`]: {
        [`&.${switchClasses.checked}`]: {
            color: '#FFFFFF',
            [`&.${switchClasses.thumb}:before`]: {
                color: '#FFFFFF',
            },
            [`& + .${switchClasses.track}`]: {
                opacity: 1,
            },
        },
        [`&.${switchClasses.thumb}:before`]: {
            color: '#FFFFFF',
        },
    },
});

const DescriptionContent = styled('div')({
    marginRight: 22,
});

const ActiveLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[8],
    marginBottom: 5,
}));

const ActiveDescription = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[8],
    fontWeight: 400,
}));

const AppointmentsSettings = () => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const { callApi } = useApiCall();
    const gs = useSelector((r: RootState) => ({
        isAppointmentSiteEnabled:
            r.globalSettings.settings?.repairShopSettings?.features.isAppointmentSiteEnabled,
    }));
    const dispatch = useAppDispatch();

    const settingsFromBackend = useRepairShopAppointmentSettings(0);
    const queryClient = useQueryClient();
    const [settings, setSettings] = useState<AppointmentSettingWorkingDaysDto>({
        repairShopKey: '',
        duration: 30,
        activateAutomaticAppointmentNumber: false,
        initialAppointmentNumber: 1,
        lastAppointmentNumber: 0,
        workingDays: [],
        synchronizeAppointmentsEnabled: false,
        omnichannelEnabled: false,
        activateChangeAppointmentStatusAfter: false,
        changeAppointmentStatusAfterTime: 30,
        valetServiceEnabled: false,
    });
    useEffect(() => {
        if (settingsFromBackend) setSettings(settingsFromBackend);
    }, [settingsFromBackend]);

    const [lastAppointmentNumber, setLastAppointmentNumber] = useState<number>(0);
    const [isInitialNumberInvalid, setIsInitialNumberInvalid] = useState<boolean>(false);

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.appointments')}`);

    const queryKey = ['shop-schedule'];
    const { data: schedule } = useQuery({
        queryKey,
        queryFn: () => ShopScheduleApi.get(),
    });

    const saveWorkingDay = useMutation(
        (workingDay: RepairShopWorkingDayDto) => ShopScheduleApi.update(workingDay),
        {
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOcurred'));
            },

            onSuccess(data, _variables, _context) {
                queryClient.setQueryData<Awaited<ReturnType<typeof ShopScheduleApi.get>>>(
                    queryKey,
                    (currentData) => {
                        return currentData?.map((x) => (x.dayNumber === data.dayNumber ? data : x));
                    }
                );
            },

            onMutate(variables) {
                queryClient.setQueryData<Awaited<ReturnType<typeof ShopScheduleApi.get>>>(
                    queryKey,
                    (currentData) => {
                        return currentData?.map((x) =>
                            x.dayNumber === variables.dayNumber ? variables : x
                        );
                    }
                );
            },
        }
    );

    const activateSiteForAppointments = useMutation(
        (isEnabled: boolean) => SiteForAppointmentsApi.activateSiteForAppointments(isEnabled),
        {
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOcurred'));
            },
        }
    );

    const handleActivateSiteForAppointments = async (isEnabled: boolean) => {
        await activateSiteForAppointments.mutateAsync(isEnabled);
        await saveAppointmentSettings(settings);
        dispatch(
            globalSettingsActions.updateRsFeatureFlags({ isAppointmentSiteEnabled: isEnabled })
        );
    };

    const saveAppointmentSettings = async (appointmentSetting: AppointmentSettingsDto) => {
        const response = await callApi(
            () => AppointmentSettingsAPI.createAppointmentSettings(appointmentSetting),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenSaving'),
                }),
            }
        );

        if (response) {
            const newSettings = {
                ...settings,
                ...response,
            };
            queryClient.setQueryData(APPOINTMENT_SETTINGS_QUERY_KEY, newSettings);
            setSettings(newSettings);
            return true;
        } else {
            return false;
        }
    };

    const saveDuration = async (duration: number) => {
        const response = await saveAppointmentSettings({
            ...settings,
            duration: duration,
        });

        if (response) {
            toasters.success(
                t('settings.appointment.defaultAppointmentDurationSavedSuccessfully'),
                t('settings.appointment.defaultAppointmentDurationUpdated')
            );
        }
    };

    const saveInitialAppointmentNumber = async () => {
        if (!lastAppointmentNumber) {
            setIsInitialNumberInvalid(true);
            toasters.danger(
                t('settings.appointment.theInitialAppointmentNumberFieldDoesNotAcceptAnEmptyValue'),
                t('settings.appointment.initialAppointmentNumberNotSaved')
            );
            return;
        }

        if (lastAppointmentNumber <= (settings.lastAppointmentNumber || 0)) {
            setIsInitialNumberInvalid(true);
            toasters.danger(
                t('settings.appointment.theInitialAppointmentNumberFieldMustBeMore'),
                t('settings.appointment.initialAppointmentNumberNotSaved')
            );
            return;
        }

        setIsInitialNumberInvalid(false);

        await saveAppointmentSettings({
            ...settings,
            initialAppointmentNumber: lastAppointmentNumber,
        });
    };

    const saveChangeStatusAfter = async (timeAfter: number) => {
        const response = await saveAppointmentSettings({
            ...settings,
            changeAppointmentStatusAfterTime: timeAfter,
        });

        if (response) {
            toasters.success(
                t('settings.appointment.changeAppointmentStatusAfterSavedSuccessfully'),
                t('settings.appointment.changeAppointmentStatusAfterUpdated')
            );
        }
    };

    const fetchAppointmentSettings = async () => {
        const response = await callApi(() => AppointmentSettingsAPI.getAppointmentSettings(), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenLoading'),
            }),
        });

        if (response) {
            if (!response.workingDays || !response.workingDays.length) {
                response.workingDays = defaultWorkingDays;
            }
            setLastAppointmentNumber(response.initialAppointmentNumber || 1);
            setSettings(response);
        }
    };

    const getActive = (day: WeekDay): boolean => {
        const workingDay =
            schedule?.find((workingDay) => workingDay.dayNumber === getDayNumber(day)) ??
            defaultWorkingDays.find((workingDay) => workingDay.dayNumber === getDayNumber(day));

        return workingDay?.active || false;
    };

    const getTime = (day: WeekDay, direction: Direction): [number, number] => {
        const workDay =
            schedule?.find((workingDay) => workingDay.dayNumber === getDayNumber(day)) ??
            defaultWorkingDays.find((workingDay) => workingDay.dayNumber === getDayNumber(day));

        if (!workDay) {
            return [0, 0];
        }

        const time =
            direction === 'open' ? workDay.opening?.split(':') : workDay.closing?.split(':');

        if (!time) {
            return [0, 0];
        }

        return [+time[0], +time[1]];
    };

    const handleActiveChange =
        (day: WeekDay) => async (_event: ChangeEvent<{}>, checked: boolean) => {
            if (!schedule) return;
            const workDay =
                schedule.find((workingDay) => workingDay.dayNumber === getDayNumber(day)) ??
                defaultWorkingDays.find((workingDay) => workingDay.dayNumber === getDayNumber(day));

            if (workDay) {
                await saveWorkingDay.mutateAsync({
                    ...workDay,
                    active: checked,
                });
            }
        };

    const handleTimeChange = (day: WeekDay, direction: Direction) => async (value: number[]) => {
        const workDay =
            schedule?.find((workingDay) => workingDay.dayNumber === getDayNumber(day)) ??
            defaultWorkingDays.find((workingDay) => workingDay.dayNumber === getDayNumber(day));

        if (workDay) {
            const hour = `${value[0]}`.padStart(2, '0');
            const minutes = `${value[1]}`.padStart(2, '0');
            if (direction === 'open') {
                workDay.opening = `${hour}:${minutes}:00`;
            } else {
                workDay.closing = `${hour}:${minutes}:00`;
            }

            await saveWorkingDay.mutateAsync(workDay);
        }
    };

    const handleCopySiteForApptLink = () => {
        const url = `${
            import.meta.env.VITE_NR_PROFILE === 'uat' ? 'uat' : ''
        }sitiodereservas.clearmechanic.com/${window.location.hostname}`;
        navigator.clipboard.writeText(url).then(() => {
            toasters.success('', t('settings.appointment.linkCopiedSuccessfully'));
        });
    };

    useEffect(() => {
        fetchAppointmentSettings();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Grid container spacing={0}>
            <Grid item xs={12}>
                <Grid container spacing={0} className={styles.root}>
                    <Grid item xs={12}>
                        <Typography className={styles.title}>
                            {t('settings.appointment.general')}
                        </Typography>
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={12} lg={3}>
                                <Dropdown
                                    name="appointment-duration"
                                    label={t('settings.appointment.defaultAppointmentDuration')}
                                    isRequired
                                    showValidationIndicators
                                    options={durationList}
                                    cmosVariant="default"
                                    value={{
                                        label: `${settings.duration || '30'} min`,
                                        value: settings.duration || 30,
                                    }}
                                    onChange={(event) => {
                                        if (event === null) return;
                                        saveDuration(event.value);
                                    }}
                                />
                            </Grid>
                            <Grid item xs={12} md={12} lg={3} style={{ minWidth: 288 }}>
                                <Grid container spacing={0} style={{ alignItems: 'center' }}>
                                    <Grid item xs={12} md={12} lg={11}>
                                        <Typography className={styles.caption}>
                                            {t(
                                                'settings.appointment.activateAutomaticAppointmentNumber'
                                            )}
                                        </Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        xs={12}
                                        md={12}
                                        lg={1}
                                        style={{ display: 'flex', justifyContent: 'flex-end' }}
                                    >
                                        <Checkbox
                                            name="automatic-appointment-number"
                                            value={true}
                                            checked={
                                                settings.activateAutomaticAppointmentNumber || false
                                            }
                                            style={{ padding: 0 }}
                                            onChange={async (_, checked) => {
                                                setSettings({
                                                    ...settings,
                                                    activateAutomaticAppointmentNumber: checked,
                                                });

                                                await saveAppointmentSettings({
                                                    ...settings,
                                                    activateAutomaticAppointmentNumber: checked,
                                                });
                                            }}
                                        />
                                    </Grid>
                                </Grid>
                                <Grid container spacing={0} style={{ alignItems: 'center' }}>
                                    <Grid item xs={12} md={12} lg={6}>
                                        <Typography className={styles.caption}>
                                            {t('settings.appointment.initialAppointmentNumber')}
                                        </Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        xs={12}
                                        md={12}
                                        lg={6}
                                        style={{ display: 'flex', justifyContent: 'flex-end' }}
                                    >
                                        <TextField
                                            name="initial-appointment-number"
                                            value={`${lastAppointmentNumber}`}
                                            disabled={
                                                !settings.activateAutomaticAppointmentNumber ||
                                                false
                                            }
                                            maxLength={9}
                                            onChange={(event) => {
                                                if (!isNaN(+event.target.value)) {
                                                    setLastAppointmentNumber(+event.target.value);
                                                }
                                            }}
                                            onBlur={saveInitialAppointmentNumber}
                                            isInvalid={isInitialNumberInvalid}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12} md={12} lg={3} style={{ minWidth: 334 }}>
                                <Grid
                                    container
                                    spacing={0}
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <Grid item xs={12} md={12} lg={11}>
                                        <Typography className={styles.caption}>
                                            {t('settings.appointment.changeAppointmentStatusAfter')}
                                        </Typography>
                                    </Grid>
                                    <Grid
                                        item
                                        xs={12}
                                        md={12}
                                        lg={1}
                                        style={{
                                            display: 'flex',
                                            justifyContent: 'flex-end',
                                        }}
                                    >
                                        <Checkbox
                                            name="enable-change-appointment-status-after"
                                            value={true}
                                            checked={settings.activateChangeAppointmentStatusAfter}
                                            style={{ padding: 0 }}
                                            onChange={async (_, checked) => {
                                                setSettings({
                                                    ...settings,
                                                    activateChangeAppointmentStatusAfter: checked,
                                                });

                                                await saveAppointmentSettings({
                                                    ...settings,
                                                    activateChangeAppointmentStatusAfter: checked,
                                                });
                                            }}
                                        />
                                    </Grid>
                                </Grid>
                                <Dropdown
                                    name="change-appointment-status-after"
                                    isRequired
                                    disabled={!settings.activateChangeAppointmentStatusAfter}
                                    options={timeAfterList}
                                    cmosVariant="default"
                                    value={{
                                        label: `${
                                            settings.changeAppointmentStatusAfterTime || '30'
                                        } min`,
                                        value: settings.duration || 30,
                                    }}
                                    onChange={(event) => {
                                        if (event === null) return;
                                        saveChangeStatusAfter(event.value);
                                    }}
                                />
                            </Grid>
                        </Grid>
                        <Grid item xs={12}>
                            <ToggleOption>
                                <StyledSwitch
                                    checked={settings.valetServiceEnabled}
                                    onChange={async (_, checked: boolean) => {
                                        setSettings({
                                            ...settings,
                                            valetServiceEnabled: checked,
                                        });

                                        await saveAppointmentSettings({
                                            ...settings,
                                            valetServiceEnabled: checked,
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <DescriptionContent>
                                    <ActiveLabel>
                                        {t('settings.appointment.valetServiceTitle')}
                                    </ActiveLabel>
                                    <ActiveDescription>
                                        {t('settings.appointment.valetServiceDescription')}
                                    </ActiveDescription>
                                </DescriptionContent>
                            </ToggleOption>
                        </Grid>
                        <Typography className={styles.title} style={{ margin: '31px 0px 21px' }}>
                            {t('settings.appointment.openingAndClosingSchedule')}
                        </Typography>
                        <Grid container spacing={1}>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            {t('settings.appointment.weekdays')}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            {t('settings.appointment.openingTime')}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            {t('settings.appointment.closingTime')}
                                        </Typography>
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('monday')}
                                                onChange={handleActiveChange('monday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.monday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="monday-open"
                                            value={getTime('monday', 'open')}
                                            onChange={handleTimeChange('monday', 'open')}
                                            disabled={!getActive('monday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="monday-close"
                                            value={getTime('monday', 'close')}
                                            onChange={handleTimeChange('monday', 'close')}
                                            disabled={!getActive('monday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('tuesday')}
                                                onChange={handleActiveChange('tuesday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.tuesday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="tuesday-open"
                                            value={getTime('tuesday', 'open')}
                                            onChange={handleTimeChange('tuesday', 'open')}
                                            disabled={!getActive('tuesday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="tuesday-close"
                                            value={getTime('tuesday', 'close')}
                                            onChange={handleTimeChange('tuesday', 'close')}
                                            disabled={!getActive('tuesday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('wednesday')}
                                                onChange={handleActiveChange('wednesday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.wednesday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="wednesday-open"
                                            value={getTime('wednesday', 'open')}
                                            onChange={handleTimeChange('wednesday', 'open')}
                                            disabled={!getActive('wednesday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="wednesday-close"
                                            value={getTime('wednesday', 'close')}
                                            onChange={handleTimeChange('wednesday', 'close')}
                                            disabled={!getActive('wednesday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('thursday')}
                                                onChange={handleActiveChange('thursday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.thursday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="thursday-open"
                                            value={getTime('thursday', 'open')}
                                            onChange={handleTimeChange('thursday', 'open')}
                                            disabled={!getActive('thursday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="thursday-close"
                                            value={getTime('thursday', 'close')}
                                            onChange={handleTimeChange('thursday', 'close')}
                                            disabled={!getActive('thursday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('friday')}
                                                onChange={handleActiveChange('friday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.friday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="friday-open"
                                            value={getTime('friday', 'open')}
                                            onChange={handleTimeChange('friday', 'open')}
                                            disabled={!getActive('friday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="friday-close"
                                            value={getTime('friday', 'close')}
                                            onChange={handleTimeChange('friday', 'close')}
                                            disabled={!getActive('friday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('saturday')}
                                                onChange={handleActiveChange('saturday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.saturday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="saturday-open"
                                            value={getTime('saturday', 'open')}
                                            onChange={handleTimeChange('saturday', 'open')}
                                            disabled={!getActive('saturday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="saturday-close"
                                            value={getTime('saturday', 'close')}
                                            onChange={handleTimeChange('saturday', 'close')}
                                            disabled={!getActive('saturday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} md={3}>
                                        <Typography className={styles.caption2}>
                                            <FormControlLabel
                                                checked={getActive('sunday')}
                                                onChange={handleActiveChange('sunday')}
                                                control={
                                                    <Switch
                                                        color="primary"
                                                        className={styles.switch}
                                                    />
                                                }
                                                label={t('settings.appointment.sunday')}
                                            />
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="sunday-open"
                                            value={getTime('sunday', 'open')}
                                            onChange={handleTimeChange('sunday', 'open')}
                                            disabled={!getActive('sunday')}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3}>
                                        <TimeFormField
                                            name="sunday-close"
                                            value={getTime('sunday', 'close')}
                                            onChange={handleTimeChange('sunday', 'close')}
                                            disabled={!getActive('sunday')}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
                <Grid container spacing={0} className={styles.root}>
                    <Grid item xs={12}>
                        <Typography className={styles.title}>
                            {t('settings.appointment.appointmentOrigin')}
                        </Typography>
                        <Grid container spacing={0} style={{ marginTop: 20 }}>
                            <Grid item xs={12} md={6}>
                                <AppointmentOrigin />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>

                <Grid container spacing={0} className={styles.root}>
                    <Grid item xs={12}>
                        <Typography className={styles.title}>
                            {t('settings.appointment.siteForAppointments')}
                        </Typography>
                        <ToggleOption>
                            <StyledSwitch
                                checked={gs.isAppointmentSiteEnabled}
                                onChange={(_, checked: boolean) => {
                                    handleActivateSiteForAppointments(checked);
                                }}
                                name="checkedA"
                            />
                            <DescriptionContent>
                                <ActiveLabel>
                                    {t('settings.appointment.activateSiteForApptsTitle')}
                                </ActiveLabel>
                                <ActiveDescription>
                                    {t('settings.appointment.activateSiteForApptsDescription')}
                                </ActiveDescription>
                            </DescriptionContent>
                            <Button
                                color={Colors.CM1}
                                label={t('settings.appointment.copySiteForApptsLink')}
                                cmosVariant={'stroke'}
                                onClick={handleCopySiteForApptLink}
                                customStyles={{ width: 230 }}
                                disabled={!gs.isAppointmentSiteEnabled}
                            />
                        </ToggleOption>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default AppointmentsSettings;

const getDayNumber = (day: WeekDay): number => {
    switch (day) {
        case 'monday':
            return 1;
        case 'tuesday':
            return 2;
        case 'wednesday':
            return 3;
        case 'thursday':
            return 4;
        case 'friday':
            return 5;
        case 'saturday':
            return 6;
        case 'sunday':
            return 0;
    }
};
