import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        background: '#FFFFFF',
        border: `1px solid #DBDCDD`,
        borderRadius: 12,
        padding: '30px 48px',
        '&:not(:first-child)': {
            marginTop: 22,
        },
    },
    title: {
        // TODO: JJJG Replace by Theme config after complete pending merge
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 14,
        lineHeight: '17px',
        color: '#26292B', // Neutrals 9
    },
    caption: {
        // TODO: JJJ<PERSON> Replace by Theme config after complete pending merge
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
        lineHeight: '15px',
        color: '#4A4D51', // Neutrals 8
    },
    caption2: {
        // TODO: <PERSON><PERSON><PERSON><PERSON> Replace by Theme config after complete pending merge
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
        lineHeight: '15px',
        color: '#899198', // Neutrals 6
    },
    switch: {
        '& .MuiSwitch-switchBase': {
            '&.Mui-checked': {
                color: '#FFFFFF',
                '& .MuiSwitch-thumb:before': {
                    color: '#FFFFFF',
                },
                '& + .MuiSwitch-track': {
                    opacity: 1,
                },
            },
            '& .MuiSwitch-thumb:before': {
                color: '#FFFFFF',
            },
        },
    },
}));

export default useStyles;
