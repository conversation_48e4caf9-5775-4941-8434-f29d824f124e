import {
    CustomizableFieldValue,
    isPredefinedField,
    LocationFieldValueWithName,
    PREDEFINED_FIELDS,
} from 'api/fields';
import { useCallback } from 'react';

import { styled } from '@mui/material';
import { LocationSettingsDto } from 'api/settings/LocationSettings';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import ResettableTextInput from 'common/components/Inputs/ResettableTextField/ResettableTextInput';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import CustomField from './CustomField';

type LocationFieldsRendererProps = {
    field: LocationFieldValueWithName;
    onSaveCustomField: (newValue: string, id: string) => void;
    onSavePredefinedField: (newValue: Partial<LocationSettingsDto>) => void;
    locationSettings: LocationSettingsDto;
};

const LocationFieldName = {
    LocationName: PREDEFINED_FIELDS.LOCATION_NAME,
    LegalName: PREDEFINED_FIELDS.LOCATION_LEGAL_NAME,
    Address: PREDEFINED_FIELDS.LOCATION_ADDRESS,
    PhoneNumber: PREDEFINED_FIELDS.LOCATION_PHONE_NUMBER,
    EmailAddress: PREDEFINED_FIELDS.LOCATION_EMAIL_ADDRESS,
    Website: PREDEFINED_FIELDS.LOCATION_WEBSITE,
    TaxIdentification: PREDEFINED_FIELDS.LOCATION_TAX_IDENTIFICATION,
    MondayToFridayHours: PREDEFINED_FIELDS.LOCATION_MONDAY_TO_FRIDAY_HOURS,
    SaturdayHours: PREDEFINED_FIELDS.LOCATION_SATURDAY_HOURS,
} as const;

const newPredefinedFieldValue = (
    fieldName: string,
    value: string
): Partial<LocationSettingsDto> => {
    switch (fieldName) {
        case LocationFieldName.LocationName:
            return { locationName: value };
        case LocationFieldName.LegalName:
            return { legalName: value };
        case LocationFieldName.Address:
            return { address: value };
        case LocationFieldName.PhoneNumber:
            return { phoneNumber: value };
        case LocationFieldName.EmailAddress:
            return { emailAddress: value };
        case LocationFieldName.Website:
            return { website: value };
        case LocationFieldName.TaxIdentification:
            return { taxIdentification: value };
        case LocationFieldName.MondayToFridayHours:
            return { serviceHoursMondayToFriday: value };
        case LocationFieldName.SaturdayHours:
            return { serviceHoursSaturday: value };
        default:
            return {};
    }
};

const getPredefinedFieldValue = (locationSettings: LocationSettingsDto, fieldName: string) => {
    switch (fieldName) {
        case LocationFieldName.LocationName:
            return locationSettings.locationName;
        case LocationFieldName.LegalName:
            return locationSettings.legalName;
        case LocationFieldName.Address:
            return locationSettings.address;
        case LocationFieldName.PhoneNumber:
            return locationSettings.phoneNumber;
        case LocationFieldName.EmailAddress:
            return locationSettings.emailAddress;
        case LocationFieldName.Website:
            return locationSettings.website;
        case LocationFieldName.TaxIdentification:
            return locationSettings.taxIdentification;
        case LocationFieldName.MondayToFridayHours:
            return locationSettings.serviceHoursMondayToFriday;
        case LocationFieldName.SaturdayHours:
            return locationSettings.serviceHoursSaturday;
        default:
            return '';
    }
};

const requiredPredefinedFields = [LocationFieldName.LocationName];

const isFieldRequired = (fieldName: string): boolean => {
    return requiredPredefinedFields.includes(fieldName);
};

export default function LocationFieldsRenderer({
    field,
    onSaveCustomField,
    onSavePredefinedField,
    locationSettings,
}: LocationFieldsRendererProps) {
    const { t } = useAppTranslation();

    const handlePredefinedFieldSave = useCallback(
        async (value: string, fieldName: string) => {
            const newValue = newPredefinedFieldValue(fieldName, value);
            onSavePredefinedField(newValue);
        },
        [onSavePredefinedField]
    );

    if (isPredefinedField(field.type)) {
        return (
            <InputWrapper
                sx={{ display: 'flex', flexDirection: 'row', gap: 7 }}
                label={<EllipsisLabel>{t(`settings.${field.name}`)}</EllipsisLabel>}
            >
                <ResettableTextInput
                    value={getPredefinedFieldValue(locationSettings, field.name)}
                    isRequired={isFieldRequired(field.name)}
                    onSave={async (v) => {
                        await handlePredefinedFieldSave(v, field.name);
                    }}
                    showEditButton
                    placeholder={t(`settings.${field.name}Placeholder`)}
                    size="small"
                />
            </InputWrapper>
        );
    }

    return <CustomField field={field as CustomizableFieldValue} onFieldSave={onSaveCustomField} />;
}

const EllipsisLabel = styled('div')({
    width: '255px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});
