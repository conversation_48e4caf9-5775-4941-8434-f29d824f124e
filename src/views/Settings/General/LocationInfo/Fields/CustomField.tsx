import { styled } from '@mui/material';
import FieldsApi, { CustomizableFieldValue, FieldType } from 'api/fields';
import { TimeSpan } from 'api/utils/format';
import { $Icon } from 'common/components/Icons/$Icon';
import { IconProps } from 'common/components/Icons/Icon';
import DateField from 'common/components/Inputs/DateField';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import NumberInput from 'common/components/Inputs/NumberField/NumberInput';
import ResettableTextInput from 'common/components/Inputs/ResettableTextField/ResettableTextInput';
import { TimeField } from 'common/components/Inputs/TimeField';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import { DateTime } from 'luxon';
import React, { ComponentType, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import theme from 'theme';
import { isDateValid } from 'utils';
import ErrorBoundary from 'utils/errorsHandling/ErrorBoundary';

export type CustomFieldProps = {
    field: CustomizableFieldValue;
    onFieldSave: (value: string, id: string) => void;
};

export default function CustomField({ field, onFieldSave }: CustomFieldProps) {
    const Component = INPUT_COMPONENTS[field.type];

    async function onSave(value: string) {
        onFieldSave(field.id, value);
        await FieldsApi.setLocationValue(field.id, value);
    }

    return (
        <InputWrapper
            sx={{ display: 'flex', flexDirection: 'row', gap: 7 }}
            label={<EllipsisLabel>{field.name}</EllipsisLabel>}
        >
            <FieldErrorBoundary>
                {Component && (
                    <Component
                        id={field.id}
                        value={field.value}
                        placeholder={field.name}
                        onSave={onSave}
                        field={field}
                    />
                )}
            </FieldErrorBoundary>
        </InputWrapper>
    );
}

type CustomInputProps = {
    id: string;
    value: string;
    onSave: (value: string) => void;
    placeholder: string;
    field: CustomizableFieldValue;
};

function ShortTextInput(props: CustomInputProps) {
    return <FreeTextInput multiline={false} {...props} />;
}

function LongTextInput(props: CustomInputProps) {
    return <FreeTextInput multiline {...props} />;
}

function FreeTextInput({
    value,
    onSave,
    placeholder,
    multiline,
}: CustomInputProps & { multiline: boolean }) {
    const ref = useRef<HTMLDivElement>(null);

    return (
        <ResettableTextInput
            inputRef={ref}
            value={value}
            onSave={async (v) => {
                onSave(v);
            }}
            showEditButton
            placeholder={placeholder}
            maxLength={1000}
            size="small"
            multiline={multiline}
        />
    );
}

module numericInput {
    type NumericInputProps = CustomInputProps & {
        precision?: number;
        icon?: ComponentType<IconProps>;
    };

    export function NumericInput({
        value,
        onSave,
        placeholder,
        precision = 6,
        icon: Icon,
    }: NumericInputProps) {
        const ref = useRef<HTMLDivElement>(null);
        const [internalValue, setInternalValue] = useState<number | null>(null);
        const [focus, setFocus] = useState(false);

        const parsedValue = useMemo(() => parseStringValue(value, precision), [value, precision]);

        useEffect(() => {
            if (focus) {
                setInternalValue(parsedValue);
            }
        }, [parsedValue, focus]);

        return (
            <NumberInput
                inputRef={ref}
                placeholder={placeholder}
                InputProps={{
                    endAdornment: Icon && <Icon />,
                }}
                cmosVariant="grey"
                value={focus ? internalValue : parsedValue}
                onBlur={() => {
                    setFocus(false);
                    onSave(internalValue === null ? '' : internalValue.toString());
                }}
                onFocus={() => setFocus(true)}
                onValueChange={(v) => setInternalValue(v.floatValue ?? null)}
            />
        );
    }

    function parseStringValue(value: string, precision: number): number | null {
        value = value.trim();

        if (value === null || value === undefined) return null;

        let num = Number.parseFloat(value);
        if (!Number.isNaN(num)) {
            return roundNum(num, precision);
        }

        value = value.replaceAll(/[^\d]/g, '');
        if (value === '') return null;
        num = Number.parseFloat(value);
        return Number.isNaN(num) ? null : roundNum(num, precision);
    }

    function roundNum(num: number, precision: number): number {
        const d = 10 ** precision;
        return Math.floor(num * d) / d;
    }
}

module currencyInput {
    const selectCurrencySymbol = createSelector(selectSettings, (s) =>
        s.internationalization.currency.replace('{0}', '').trim()
    );

    const CustomCurrency = styled('div')({
        fontWeight: 'bold',
        color: 'var(--neutral7)',
        display: 'flex',
        justifyContent: 'center',
        verticalAlign: 'middle',
        lineHeight: '24px',
        height: 24,
        width: 24,
    });

    function normalizeSymbol(symbol: string): string {
        if (symbol === 'USD') return '$';

        return symbol;
    }

    function Icon() {
        const symbol: string = normalizeSymbol(useAppSelector(selectCurrencySymbol));

        const scale = 1.5 / Math.sqrt(Math.max(1, symbol.length));

        if (symbol !== '$')
            return (
                <CustomCurrency style={{ transform: `scale(${scale})` }}>{symbol}</CustomCurrency>
            );

        // eslint-disable-next-line react/jsx-pascal-case
        return <$Icon fill={theme.palette.neutral[7]} />;
    }

    export function CurrencyInput({ id, ...props }: CustomInputProps) {
        return <numericInput.NumericInput {...props} id={id} precision={2} icon={Icon} />;
    }
}

function DateInput({ value, onSave }: CustomInputProps) {
    const ref = useRef<HTMLInputElement>(null);
    const date = useMemo(() => {
        try {
            return DateTime.fromFormat(value, 'yyyy-MM-dd').set({
                millisecond: 0,
                second: 0,
                minute: 0,
                hour: 0,
            });
        } catch (_err: unknown) {
            return null;
        }
    }, [value]);
    const jsDate = date?.toJSDate() ?? null;

    const onDateChange = useCallback(
        (d: Date | null) => {
            if (!d) d = new Date();
            if (!isDateValid(d)) return false;

            const stringValue = DateTime.fromJSDate(d).toFormat('yyyy-MM-dd');
            onSave(stringValue);
        },
        [onSave]
    );

    return (
        <DateField inputRef={ref} variant="grey" fullWidth value={jsDate} onChange={onDateChange} />
    );
}

module timeInput {
    export function TimeInput({ value, onSave }: CustomInputProps) {
        const ref = useRef<HTMLInputElement>(null);
        const parsedValue = useMemo(() => parseTimeValue(value), [value]);

        const onValueChange = useCallback(
            ([h, m]: [number, number]) => {
                onSave(TimeSpan.fromParts(h, m).toString());
            },
            [onSave]
        );

        return (
            <TimeField
                ref={ref}
                cmosVariant="grey"
                fullWidth
                value={parsedValue}
                onChange={onValueChange}
            />
        );
    }

    function parseTimeValue(value: string): [number, number] | null {
        value = value.trim();
        if (value === '') return null;

        if (/^\d{2}:\d{2}(:\d{2})?$/.test(value)) {
            const parts = value.split(':').map(Number);
            const h = Math.min(23, parts[0]);
            const m = Math.min(59, parts[1]);
            return [h, m];
        }

        return null;
    }
}

type DropDownOption = { name: string };

function SelectInput(props: CustomInputProps) {
    return <InternalMultiSelectInput isMulti={false} {...props} />;
}

type MultiSelectFieldValue = {
    value: DropDownOption[];
    text?: string | null;
};

function MultiSelectInput(props: CustomInputProps) {
    return <InternalMultiSelectInput isMulti {...props} />;
}

function InternalMultiSelectInput({
    value: valueProp,
    onSave,
    field,
    isMulti,
}: CustomInputProps & { isMulti: boolean }) {
    const { t } = useAppTranslation();
    const ref = useRef<HTMLSelectElement>(null);

    const parsedValue: MultiSelectFieldValue = useMemo(() => {
        try {
            const { v: _, ...value } = valueProp
                ? (JSON.parse(valueProp) as MultiSelectFieldValue & { v?: unknown })
                : { v: 0, value: [] };
            return value;
        } catch {
            return { value: [] };
        }
    }, [valueProp]);

    const hasValueInExtraText = useRef(!!parsedValue.text);
    const hasExtraText = hasValueInExtraText.current || field.hasExtraText === true;

    // calculate dropdown options
    const options = useMemo(() => {
        const settingsOptions = (field.options ?? []).map((o) => ({
            label: o.name,
            value: o.name,
        }));

        //Merge options taken from settings with options stored as value for a specific location field to add options that are deleted in settings.
        const mergedOptions = parsedValue.value.reduce((accumulator, locationItem) => {
            if (!accumulator.some((settingsItem) => settingsItem.value === locationItem.name)) {
                accumulator.push({ value: locationItem.name, label: locationItem.name });
            }
            return accumulator;
        }, settingsOptions);

        return mergedOptions.sort((l, r) => {
            return l.label.localeCompare(r.label);
        });
    }, [field, parsedValue.value]);

    const selectedValues = useMemo(() => parsedValue.value.map((x) => x.name), [parsedValue.value]);

    const selectedOptions = useMemo(() => {
        return options.filter((o) => selectedValues.includes(o.value));
    }, [options, selectedValues]);

    const handleSelectChange = (
        valuesArg: ReadonlyArray<OptionData<string>> | OptionData<string> | null
    ) => {
        let value: MultiSelectFieldValue;
        if (valuesArg instanceof Array) {
            if (!isMulti) throw new Error('unreachable code');
            value = {
                value: valuesArg.map((x) => ({ name: x.value })),
                text: parsedValue.text || undefined,
            };
        } else {
            if (isMulti) throw new Error('unreachable code');
            value = {
                value: valuesArg ? [{ name: valuesArg.value }] : [],
                text: parsedValue.text || undefined,
            };
        }

        onSave(JSON.stringify(value));
    };

    const handleExtraTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value: MultiSelectFieldValue = {
            value: parsedValue.value,
            text: e.target.value,
        };
        onSave(JSON.stringify(value));
    };

    return (
        <MultiSelectContainer>
            <Dropdown
                multiple={isMulti}
                showValueCounterAfter={3}
                ref={ref}
                cmosVariant="grey"
                placeholder={
                    t('customizableFields.placeholders.select') + ' ' + field.name.toLowerCase()
                }
                options={options}
                value={selectedOptions}
                onChange={handleSelectChange}
                styles={{
                    indicatorsContainer: undefined,
                }}
                optionStyle={OptionStyle.checkbox}
            />
            {hasExtraText && (
                <TextField
                    value={parsedValue.text ?? ''}
                    onChange={handleExtraTextChange}
                    placeholder={field.name}
                />
            )}
        </MultiSelectContainer>
    );
}

const MultiSelectContainer = styled('div')({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 6,
});

const INPUT_COMPONENTS: Partial<Record<FieldType, ComponentType<CustomInputProps>>> = {
    ShortText: ShortTextInput,
    LongText: LongTextInput,
    Date: DateInput,
    Numeric: numericInput.NumericInput,
    Currency: currencyInput.CurrencyInput,
    Time: timeInput.TimeInput,
    Select: SelectInput,
    MultiSelect: MultiSelectInput,
};

function FieldErrorBoundary({ children }: React.PropsWithChildren<{}>) {
    return (
        <ErrorBoundary
            renderError={({ error }) => {
                let errorMessage: string = 'unknown';
                if (error instanceof Error) {
                    errorMessage = error.message;
                } else if (typeof error === 'string') {
                    errorMessage = error;
                }
                console.error(error);
                return (
                    <ErrorMessage>
                        <small>
                            <strong>CUSTOM FIELD ERROR</strong>
                        </small>
                        <br />
                        {errorMessage}
                    </ErrorMessage>
                );
            }}
        >
            {children}
        </ErrorBoundary>
    );
}

const ErrorMessage = styled('div')({
    border: '1px solid var(--danger)',
    padding: '8px',
    width: '100%',
});

const EllipsisLabel = styled('div')({
    width: '255px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});
