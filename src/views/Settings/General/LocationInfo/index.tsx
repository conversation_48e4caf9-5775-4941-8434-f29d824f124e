import { Box } from '@mui/material';

import { useAppTranslation } from 'common/hooks/useAppTranslation';

import { useEffect, useState } from 'react';

import PageContent from '../../../Components/Page';
import {
    BooleanSettingControl,
    ImageUploadControl,
    SettingsSection,
    TextSettingControl,
} from '../../common';

import { useMutation, useQuery } from '@tanstack/react-query';
import LocationSettingsAPI, { LocationSettingsDto } from 'api/settings/LocationSettings';
import { Http } from 'services/Http';
import UploadAPI from 'api/settings/Upload';
import useToasters from 'common/hooks/useToasters';
import FieldsApi, { LocationFieldsValues } from '../../../../api/fields';
import LocationFieldsRenderer from './Fields/LocationFieldsRenderer';

function getDefaultLocationGeneralInfo(): LocationSettingsDto {
    return {
        locationName: '',
        legalName: '',
        address: '',
        phoneNumber: '',
        emailAddress: '',
        website: '',
        taxIdentification: '',
        serviceHoursSaturday: '',
        serviceHoursMondayToFriday: '',
        consumerLogoPath: '',
        showVehicleMakeLogo: false,
    };
}

export const LocationInfoSettings = () => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [locationSettings, setLocationSettings] = useState<LocationSettingsDto>(
        getDefaultLocationGeneralInfo
    );

    const [locationFields, setLocationFields] = useState<LocationFieldsValues>();

    const { data, isLoading } = useQuery(['LocationSettings'], {
        queryFn: LocationSettingsAPI.get,
        cacheTime: 0,
        staleTime: 0,
    });

    const { data: customFieldsData } = useQuery(['CustomFieldsData'], {
        queryFn: FieldsApi.getLocationValues,
        cacheTime: 0,
        staleTime: 0,
    });

    useEffect(() => {
        if (data) {
            setLocationSettings(data);
        }
    }, [data]);

    useEffect(() => {
        if (customFieldsData) {
            setLocationFields(customFieldsData);
        }
    }, [customFieldsData]);

    const uploadInspectionFormLogo = async (file: File) => {
        try {
            const { path } = await UploadAPI.uploadInspectionFormLogo(file);

            await locationSettingsMutation.mutateAsync({ consumerLogoPath: path });

            showSaveSucceedToaster(t('settings.location.orderLogoUpdated'));
        } catch (e) {
            showSaveFailedToaster();
        }
    };

    function onSaveCustomFields(id: string, newValue: string) {
        if (locationFields) {
            const updatedGeneralFields = locationFields.general.map((f) =>
                f.id === id ? { ...f, value: newValue } : f
            );
            const updatedServiceHoursFields = locationFields.serviceHours.map((f) =>
                f.id === id ? { ...f, value: newValue } : f
            );
            setLocationFields((prev) => ({
                ...prev,
                general: updatedGeneralFields,
                serviceHours: updatedServiceHoursFields,
            }));
            showSaveSucceedToaster();
        }
    }

    async function onSavePredefinedFields(newValue: Partial<LocationSettingsDto>) {
        await locationSettingsMutation.mutateAsync(newValue);
    }

    const showSaveSucceedToaster = (text?: string) => {
        toasters.success(
            text ?? t('toasters.settingSuccessfullyUpdated'),
            t('toasters.settingUpdated'),
            { duration: 3000 }
        );
    };

    const showSaveFailedToaster = (title?: string, text?: string) => {
        toasters.danger(
            text ?? t('toasters.errorOccurredWhenSaving'),
            title ?? t('toasters.errorOccurred'),
            { duration: 3000 }
        );
    };

    const deleteInspectionFormLogo = async () => {
        try {
            await UploadAPI.deleteInspectionFormLogo();
            const newSettings = { ...locationSettings, consumerLogoPath: null };
            setLocationSettings(newSettings);
            showSaveSucceedToaster(t('settings.location.orderLogoRemoved'));
        } catch (e) {
            showSaveFailedToaster();
        }
    };

    const locationSettingsMutation = useMutation(async (changes: Partial<LocationSettingsDto>) => {
        const newSettings = { ...locationSettings, ...changes };
        setLocationSettings(newSettings);
        const isSuccess = await LocationSettingsAPI.save(newSettings);
        if (isSuccess) {
            showSaveSucceedToaster();
        }
    });

    return (
        <>
            <PageContent paddedX sx={{ marginBottom: '30px' }}>
                <SettingsSection label={t('settings.location.generalInformation')}>
                    {locationFields?.general.map((field) => (
                        <Box sx={{ width: '55%' }} key={field.id}>
                            <LocationFieldsRenderer
                                key={field.id}
                                locationSettings={locationSettings}
                                field={field}
                                onSaveCustomField={onSaveCustomFields}
                                onSavePredefinedField={onSavePredefinedFields}
                            />
                        </Box>
                    ))}
                </SettingsSection>
            </PageContent>
            <PageContent paddedX sx={{ marginBottom: '30px' }}>
                <SettingsSection label={t('settings.location.serviceHours')}>
                    {locationFields?.serviceHours.map((field) => {
                        return (
                            <Box sx={{ width: '55%' }}>
                                <LocationFieldsRenderer
                                    key={field.id}
                                    locationSettings={locationSettings}
                                    field={field}
                                    onSaveCustomField={onSaveCustomFields}
                                    onSavePredefinedField={onSavePredefinedFields}
                                />
                            </Box>
                        );
                    })}
                </SettingsSection>
            </PageContent>
            <PageContent paddedX>
                <SettingsSection label={t('settings.location.header')}>
                    <ImageUploadControl
                        disabled={isLoading}
                        onFileDelete={deleteInspectionFormLogo}
                        onFileUpload={uploadInspectionFormLogo}
                        showInfoIcon={false}
                        note={
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: t('settings.location.dimensionsNote'),
                                }}
                            />
                        }
                        src={locationSettings.consumerLogoPath?.replace('~', Http.HOST)}
                    />
                    <BooleanSettingControl
                        disabled={isLoading}
                        value={locationSettings.showVehicleMakeLogo}
                        name="showBrandLogoChk"
                        label={t('settings.orders.image.showBrandLogo')}
                        onChange={async (v) => {
                            await locationSettingsMutation.mutateAsync({ showVehicleMakeLogo: v });
                        }}
                    />
                </SettingsSection>
            </PageContent>
        </>
    );
};
