import { RouterTab } from 'common/components/tabs/RouterParameterBasedTabs';
import { ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import SettingsRouterParameterBasedTabs from '../common/SettingsRouterParameterBasedTabs';
import { Tabs } from './helpers';

const GeneralSettings = () => {
    const { t } = useAppTranslation();
    const globalSettings = useSelector(selectSettings);

    const tabs: RouterTab[] = useMemo(
        () =>
            Tabs.filter((m) => m.isVisible(globalSettings)).map((v) => ({
                content: v.component,
                value: v.section,
                label: t(v.label),
            })),
        [globalSettings, t]
    );

    useDocumentTitle(t('titles.settings.general'));

    return (
        <SettingsRouterParameterBasedTabs
            urlPattern={ROUTES.SETTINGS.GENERAL.PATH}
            parameterName="section"
            tabs={tabs}
        >
            {(content) => content}
        </SettingsRouterParameterBasedTabs>
    );
};
export default GeneralSettings;
