import { makeStyles } from '@mui/styles';
import { FontSecondary } from '../../../common/styles/FontHelper';
import { HeaderStyles } from '../../../common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    navTab: {
        gap: 40,
        marginTop: 26,
    },
    navTabButton: {
        ...FontSecondary(HeaderStyles.H5_14px, true, theme.palette.neutral[7]),
        textDecoration: 'none',
        cursor: 'pointer',
        '&:hover': {
            paddingBottom: 6,
            borderBottom: `2px solid ${theme.palette.neutral[7]}`,
        },
    },
    navTabButtonActive: {
        color: `${theme.palette.primary.main}!important`,
        paddingBottom: 6,
        borderBottom: `2px solid ${theme.palette.primary.main}!important`,
    },
    container: {
        display: 'flex',
        position: 'relative',
        justifyContent: 'flex-start',
        alignItems: 'center',
        flexDirection: 'column',
    },
    outerGrid: {
        flexGrow: 1,
        height: 0,
    },
    innerGrid: {
        height: '100%',
    },
}));
