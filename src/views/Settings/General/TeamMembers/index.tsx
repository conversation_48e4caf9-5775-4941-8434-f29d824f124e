import {
    Grid,
    InputAdornment,
    Pagination,
    Table,
    TableBody,
    TableContainer,
    TableHead,
    TableRow,
    TableSortLabel,
    styled,
} from '@mui/material';
import { UsersActiveStatus } from 'api/enterprise/settings/teamMembers';
import UserAPI from 'api/settings/User';
import clsx from 'clsx';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'store';
import { setFilters } from 'store/slices/Settings/teamMembers';
import { selectFilters } from 'store/slices/Settings/teamMembers/selectors';
import PageWithFooter from 'views/Components/Page/PageWithFooter';
import AreaSpinner from '../../../../common/components/AreaSpinner';
import { Button } from '../../../../common/components/Button';
import { SearchIcon } from '../../../../common/components/Icons/SearchIcon';
import { UpIcon } from '../../../../common/components/Icons/UpIcon';
import Dropdown from '../../../../common/components/Inputs/Dropdown';
import TextFormField from '../../../../common/components/Inputs/TextField';
import TableHeadCell from '../../../../common/components/TableHeadCell';
import { useApiCall } from '../../../../common/hooks';
import { useInputValue } from '../../../../common/hooks/useInputValue';
import { Colors } from '../../../../common/styles/Colors';
import { IconSize } from '../../../../common/styles/IconSize';
import { paginationStyles } from '../../../../common/styles/PaginationStyles';
import { UserDto } from '../../../../datacontracts/UserDto';
import { UsersOrderByType } from '../../../../datacontracts/UserListRequest';
import { RowTeamMembers } from './RowTeamMembers';
import { TeamMemberForm } from './TeamMemberForm';
import { useStyles } from './css';

const SearchContainer = styled('div')({
    display: 'grid',
    gridTemplateColumns: '200px 200px 1fr',
    gap: 8,
    alignItems: 'end',
});

export const TeamMembersSettings = () => {
    const { t } = useAppTranslation();
    const { callApi, apiCallStatus } = useApiCall();
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    const location = useLocation();
    const pagionationClasses = paginationStyles();
    const styles = useStyles();

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.teamMembers')}`);

    const pageSize = 10;
    const optionsStatus: OptionData<UsersActiveStatus>[] = [
        {
            label: t('users.statusFilter.all'),
            value: 'All',
        },
        {
            label: t('users.statusFilter.active'),
            value: 'Active',
        },
        {
            label: t('users.statusFilter.inactive'),
            value: 'Inactive',
        },
    ];

    const { status } = useAppSelector(selectFilters);
    const [numberOfPages, setNumberOfPages] = useState<number>(0);
    const [open, setIsOpenPopup] = useState(false);
    const [users, setUsers] = useState<UserDto[]>([]);
    const [userEdit, setUserEdit] = useState<UserDto>();
    const [orderByType, setOrderByType] = useState<UsersOrderByType>(
        UsersOrderByType.DisplayNameAsc
    );
    const [dropdownStatus, setDropDownStatus] = useState(status);
    const [pageIndex, setPageIndex] = useState<number>(1);
    const [searchInput, setSearchInput] = useInputValue('');

    const fetchUsers = async () => {
        if (pageIndex === 0) return;

        const response = (
            await callApi(
                () =>
                    UserAPI.list({
                        pageIndex: pageIndex - 1,
                        maxRows: pageSize,
                        ordBy: orderByType,
                        isActive: dropdownStatus,
                        searchField: searchInput,
                    }),
                {
                    selectErrorContent: (_) => ({
                        body: t('toasters.errorOccurredWhenLoading'),
                    }),
                }
            )
        ).users;
        if (response?.data) {
            setUsers([...response.data]);
            setNumberOfPages(response.pagerData.pageCount);
        }
    };

    const changePageHandler = (_: any, page: number) => {
        changePageIndex(page);
    };

    const changeOrderFullNameColumnHandler = () => {
        if (orderByType == UsersOrderByType.DisplayNameAsc) {
            setOrderByType(UsersOrderByType.DisplayNameDesc);
        } else {
            setOrderByType(UsersOrderByType.DisplayNameAsc);
        }
    };

    const changeOrderEmailColumnHandler = () => {
        if (orderByType == UsersOrderByType.UserNameAsc) {
            setOrderByType(UsersOrderByType.UserNameDesc);
        } else {
            setOrderByType(UsersOrderByType.UserNameAsc);
        }
    };

    const changePageIndex = (page: number) => {
        const params = new URLSearchParams(location.search);
        params.set('page', page.toString());
        navigate({ search: params.toString() });
        setPageIndex(page);
    };

    const openModalEdit = (user: UserDto) => {
        setUserEdit(user);
        setIsOpenPopup(true);
    };
    const onClosePopup = () => {
        setIsOpenPopup(false);
        if (userEdit) {
            setUserEdit(undefined);
        }
    };
    const onSavePopup = (_: UserDto) => {
        fetchUsers();
    };

    useEffect(() => {
        fetchUsers();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orderByType, pageIndex, dropdownStatus, searchInput]);

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const pageParam = params.get('page');
        //const orderByString: string = params.get("orderby") ?? "UploadedDesc";
        //const orderByParam = orderByString;
        //setOrderByType(orderByParam);
        changePageIndex(pageParam ? parseInt(pageParam) : 1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <PageWithFooter
                footer={
                    <div className={pagionationClasses.root}>
                        <Pagination
                            count={numberOfPages}
                            page={pageIndex}
                            onChange={changePageHandler}
                            color="primary"
                            size={'small'}
                        />
                    </div>
                }
            >
                <TableContainer
                    className={clsx(
                        // pageStyles.scroll,
                        // pageStyles.tableViewContainer,
                        styles.tableContainer
                    )}
                >
                    <Grid container spacing={1} padding={2}>
                        <Grid item xs={12} md={9}>
                            <SearchContainer>
                                <Dropdown
                                    name={'statusFilter'}
                                    cmosVariant="roundedGrey"
                                    options={optionsStatus}
                                    value={optionsStatus.find((o) => o.value === dropdownStatus)}
                                    isRequired={true}
                                    isSearchable={false}
                                    onChange={(e) => {
                                        if (e === null) return;
                                        setDropDownStatus(e.value);
                                        changePageIndex(1);
                                        dispatch(setFilters({ status: e.value }));
                                    }}
                                    isInvalid={!false}
                                />
                                <TextFormField
                                    cmosVariant="roundedGrey"
                                    name={'search-members-by-name'}
                                    value={searchInput}
                                    placeholder={t('commonLabels.search')}
                                    showValidationIndicators={false}
                                    enableEnterComplete={true}
                                    onChange={(e: any) => {
                                        setSearchInput(e);
                                        changePageIndex(1);
                                    }}
                                    endAdornment={
                                        <InputAdornment position="end">
                                            <SearchIcon />
                                        </InputAdornment>
                                    }
                                />
                            </SearchContainer>
                        </Grid>

                        <Grid
                            item
                            xs={12}
                            md={3}
                            sx={{ alignItems: 'end', display: 'flex', justifyContent: 'end' }}
                        >
                            <Button
                                cmosVariant={'filled'}
                                label={t('users.addTeamMember')}
                                cmosSize={'medium'}
                                onClick={() => setIsOpenPopup(true)}
                                className={styles.addTeamMemberButton}
                            />
                        </Grid>
                    </Grid>

                    {/** FILTROS */}
                    <Table stickyHeader>
                        <TableHead>
                            <TableRow>
                                <TableHeadCell style={{ width: '30%', paddingLeft: 30 }}>
                                    <TableSortLabel
                                        active={
                                            orderByType === UsersOrderByType.DisplayNameAsc ||
                                            orderByType === UsersOrderByType.DisplayNameDesc
                                        }
                                        direction={
                                            orderByType === UsersOrderByType.DisplayNameDesc
                                                ? 'desc'
                                                : 'asc'
                                        }
                                        onClick={changeOrderFullNameColumnHandler}
                                        IconComponent={(propsIcon) => (
                                            <div {...propsIcon}>
                                                <UpIcon size={IconSize.M} fill={Colors.Neutral7} />
                                            </div>
                                        )}
                                    >
                                        {t('users.fullName')}
                                    </TableSortLabel>
                                </TableHeadCell>
                                <TableHeadCell style={{ width: '20%' }}>
                                    {t('users.jobPosition')}
                                </TableHeadCell>
                                <TableHeadCell style={{ width: '30%' }}>
                                    <TableSortLabel
                                        active={
                                            orderByType === UsersOrderByType.UserNameAsc ||
                                            orderByType === UsersOrderByType.UserNameDesc
                                        }
                                        direction={
                                            orderByType === UsersOrderByType.UserNameDesc
                                                ? 'desc'
                                                : 'asc'
                                        }
                                        onClick={changeOrderEmailColumnHandler}
                                        IconComponent={(propsIcon) => (
                                            <div {...propsIcon}>
                                                <UpIcon size={IconSize.M} fill={Colors.Neutral7} />
                                            </div>
                                        )}
                                    >
                                        {t('users.email')}
                                    </TableSortLabel>
                                </TableHeadCell>
                                <TableHeadCell style={{ width: '10%' }}>
                                    {t('users.status')}
                                </TableHeadCell>
                                <TableHeadCell style={{ width: '10%' }} />
                            </TableRow>
                        </TableHead>
                        {apiCallStatus !== 'Pending' && (
                            <TableBody>
                                {users &&
                                    users.map((user: UserDto, idxRow: number) => (
                                        <RowTeamMembers
                                            user={user}
                                            key={`row_${idxRow}`}
                                            onEdit={openModalEdit}
                                        />
                                    ))}
                            </TableBody>
                        )}
                    </Table>
                    {apiCallStatus === 'Pending' && (
                        <div
                            style={{
                                height: '80%',
                                display: 'flex',
                            }}
                        >
                            <AreaSpinner />
                        </div>
                    )}
                </TableContainer>
            </PageWithFooter>

            <TeamMemberForm onClose={onClosePopup} onSave={onSavePopup} {...{ open, userEdit }} />
        </>
    );
};
