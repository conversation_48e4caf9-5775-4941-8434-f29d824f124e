import Grid from '@mui/material/Grid';
import TimeFormField from 'common/components/Inputs/TimeFormField';

export type TeamMemberScheduleProps = {
    value?: [[number, number], [number, number]];
    onChange?: (value: [number, number], direction: 'open' | 'close') => void;
    disabled: boolean;
};

const TeamMemberSchedule = (props: TeamMemberScheduleProps) => {
    const { value, onChange } = props;
    return (
        <Grid container spacing={1}>
            <Grid item xs={6}>
                <TimeFormField
                    name={`member-schedule-`}
                    value={value ? value[0] : null}
                    onChange={(event) => {
                        if (onChange) {
                            onChange(event, 'open');
                        }
                    }}
                    disabled={props.disabled}
                />
            </Grid>
            <Grid item xs={6}>
                <TimeFormField
                    name={`member-schedule-`}
                    value={value ? value[1] : null}
                    onChange={(event) => {
                        if (onChange) {
                            onChange(event, 'close');
                        }
                    }}
                    disabled={props.disabled}
                />
            </Grid>
        </Grid>
    );
};

export default TeamMemberSchedule;
