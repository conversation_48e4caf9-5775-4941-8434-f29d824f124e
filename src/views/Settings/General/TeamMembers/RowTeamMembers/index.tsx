import { Grid, TableRow } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Button } from '../../../../../common/components/Button';
import { EditIcon } from '../../../../../common/components/Icons/EditIcon';
import TableCell from '../../../../../common/components/TableCell';
import { jobTitleLabel } from '../../../../../common/constants/JobTitle';
import { Colors } from '../../../../../common/styles/Colors';
import { textOverFlow } from '../../../../../common/styles/TextHelpers';
import { UserDto } from '../../../../../datacontracts/UserDto';

const useStyles = makeStyles((theme) => ({
    root: {
        minHeight: '56px!important',
        height: 56,
        backgroundColor: Colors.White,
        '&:hover': {
            backgroundColor: Colors.CM5,
            '&:hover': {
                '& .options': {
                    opacity: '1!important',
                },
            },
        },
        '& .options': {
            opacity: 0,
        },
    },
}));

export const RowTeamMembers = ({
    user,
    onEdit,
}: {
    user: UserDto;
    onEdit: (user: UserDto) => void;
}) => {
    const styles = useStyles();
    const { t } = useAppTranslation();

    return (
        <TableRow onClick={() => onEdit(user)} className={`${styles.root}`}>
            <TableCell component="td" scope="row" style={{ paddingLeft: 30 }}>
                <Grid container>{textOverFlow(user.displayName, 42)}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.jobTitle ? t(jobTitleLabel(user.jobTitle)) : ''}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.userName}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <Grid container>{user.isActive ? t('users.active') : t('users.inactive')}</Grid>
            </TableCell>
            <TableCell component="td" scope="row">
                <div className="options">
                    <Button
                        label=""
                        color={Colors.Neutral6}
                        cmosVariant={'typography'}
                        cmosSize={'medium'}
                        Icon={EditIcon}
                        onClick={() => onEdit(user)}
                    />
                </div>
            </TableCell>
        </TableRow>
    );
};
