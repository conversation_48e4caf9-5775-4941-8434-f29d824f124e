import { makeStyles } from '@mui/styles';

const useScheduleDayStyles = makeStyles((theme) => ({
    switch: {
        '& .MuiSwitch-switchBase': {
            '&.Mui-checked': {
                color: '#FFFFFF',
                '& .MuiSwitch-thumb:before': {
                    color: '#FFFFFF',
                },
                '& + .MuiSwitch-track': {
                    opacity: 1,
                },
            },
            '& .MuiSwitch-thumb:before': {
                color: '#FFFFFF',
            },
        },
    },
}));

export default useScheduleDayStyles;
