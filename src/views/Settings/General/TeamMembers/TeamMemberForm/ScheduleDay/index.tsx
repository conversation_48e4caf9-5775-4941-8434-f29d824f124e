import { FormControlLabel, Grid, Switch, formControlLabelClasses, styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { WeekDay } from 'datacontracts/WeekDay.enum';
import WorkingDay, { WorkingDayProps } from '../WorkingDay';
import useScheduleDayStyles from './css';
import { getScheduleActive, weekDayToString } from './helpers';

type ScheduleDayProps = {
    onChecked: (weekDay: WeekDay, checked: boolean) => void;
} & React.HTMLAttributes<HTMLDivElement> &
    WorkingDayProps;

const Label = styled(FormControlLabel)(({ theme }) => ({
    [`& .${formControlLabelClasses.label}`]: {
        ...theme.typography.h6Roboto,
        fontWeight: 'normal',
    },
}));

export default function ScheduleDay({
    weekDay,
    user,
    onChecked,
    onAddSchedule,
    onCopySchedule,
    onRemoveSchedule,
    onScheduleWindowChange,
    ...props
}: ScheduleDayProps) {
    const styles = useScheduleDayStyles();
    const { t } = useAppTranslation();

    return (
        <Grid container spacing={0} {...props}>
            <Grid item xs={2}>
                <Label
                    checked={getScheduleActive(weekDay, user)}
                    control={<Switch color="primary" className={styles.switch} />}
                    label={t(`users.schedule.${weekDayToString(weekDay)}`)}
                    onChange={(_, checked) => onChecked(weekDay, checked)}
                />
            </Grid>
            <Grid item xs={10}>
                <WorkingDay
                    user={user}
                    weekDay={weekDay}
                    onAddSchedule={onAddSchedule}
                    onRemoveSchedule={onRemoveSchedule}
                    onCopySchedule={onCopySchedule}
                    onScheduleWindowChange={onScheduleWindowChange}
                />
            </Grid>
        </Grid>
    );
}
