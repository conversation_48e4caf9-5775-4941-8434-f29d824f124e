import { UserDto } from 'datacontracts/UserDto';
import { WeekDay } from 'datacontracts/WeekDay.enum';

export function getScheduleActive(weekDay: WeekDay, user: UserDto): boolean {
    const schedule = user.userSchedule.find((workDay) => workDay.weekDay === weekDay);

    return schedule?.active || false;
}

export function weekDayToString(weekDay: WeekDay): string {
    switch (weekDay) {
        case WeekDay.Monday:
            return 'monday';
        case WeekDay.Tuesday:
            return 'tuesday';
        case WeekDay.Wednesday:
            return 'wednesday';
        case WeekDay.Thursday:
            return 'thursday';
        case WeekDay.Friday:
            return 'friday';
        case WeekDay.Saturday:
            return 'saturday';
        case WeekDay.Sunday:
            return 'sunday';
    }
}
