import { useTheme } from '@mui/material';
import styled from '@mui/styles/styled';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import SpecialtiesApi, { SpecialtyDto } from 'api/Specialties';
import { Button } from 'common/components/Button';
import { EditIcon } from 'common/components/Icons';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { TextField } from 'common/components/Inputs';
import Dropdown from 'common/components/Inputs/Dropdown';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { UserDto } from 'datacontracts/UserDto';
import { useEffect, useRef, useState } from 'react';

export type SpecialtyDropdownProps = {
    user: UserDto;
    setUser: (user: UserDto) => void;
};

export default function SpecialtyDropdown({ user, setUser }: SpecialtyDropdownProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const theme = useTheme();
    const [isEditing, setIsEditing] = useState(false);
    const [newSpecialty, setNewSpecialty] = useState('');
    const [editingSpecialty, setEditingSpecialty] = useState<SpecialtyDto | null>(null);
    const dropdownRef = useRef<HTMLDivElement | null>(null);
    const [menuIsOpen, setMenuIsOpen] = useState(false);
    const inputRef = useRef<HTMLInputElement | null>(null);
    const cursorPositionRef = useRef<number | null>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setMenuIsOpen(false);
            }
        };

        if (menuIsOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }

        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [menuIsOpen]);

    useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
            if (cursorPositionRef.current !== null) {
                inputRef.current.setSelectionRange(
                    cursorPositionRef.current,
                    cursorPositionRef.current
                );
            }
        }
    }, [editingSpecialty, newSpecialty]);

    const queryClient = useQueryClient();

    const { data: specialties = [] } = useQuery(
        ['specialties'],
        () => SpecialtiesApi.getSpecialties(),
        { staleTime: 10000, cacheTime: Infinity }
    );

    const createSpecialtyMutation = useMutation(
        async (name: string) => {
            return await SpecialtiesApi.createSpecialty(name);
        },
        {
            onSuccess: () => {
                queryClient.invalidateQueries(['specialties']);
            },
        }
    );

    const updateSpecialtyMutation = useMutation(
        async ({ id, name }: { id: number; name: string }) => {
            return await SpecialtiesApi.updateSpecialty(id, name);
        },
        {
            onSuccess: () => {
                queryClient.invalidateQueries(['specialties']);
                toasters.success('', t('users.notifications.updatedSpecialty'));
            },
        }
    );

    const editingSpecialtyIsValid = !!(
        editingSpecialty &&
        editingSpecialty.name.length > 0 &&
        editingSpecialty?.name?.length <= 50 &&
        !specialties.some(
            (specialty) =>
                specialty.name === editingSpecialty.name && specialty.id !== editingSpecialty.id
        )
    );

    const resetEditingSpecialty = () => {
        setEditingSpecialty(null);
        cursorPositionRef.current = null;
        setIsEditing(false);
    };

    const handleUpdateSpecialty = async () => {
        if (!editingSpecialtyIsValid) {
            resetEditingSpecialty();
            return;
        }

        const originalSpecialty = specialties.find((s) => s.id === editingSpecialty.id);
        if (originalSpecialty && originalSpecialty.name === editingSpecialty.name) {
            resetEditingSpecialty();
            return;
        }

        await updateSpecialtyMutation.mutateAsync({
            id: editingSpecialty.id,
            name: editingSpecialty.name,
        });

        resetEditingSpecialty();
    };

    const newSpecialtyIsValid = !!(
        newSpecialty.length <= 50 &&
        !specialties.some((specialty) => specialty.name === newSpecialty)
    );

    const resetNewSpecialty = () => {
        setNewSpecialty('');
        cursorPositionRef.current = null;
        setIsEditing(false);
    };

    const handleCreateSpecialty = async () => {
        if (newSpecialtyIsValid && newSpecialty.trim()) {
            createSpecialtyMutation.mutate(newSpecialty);
            setIsEditing(false);
            resetNewSpecialty();
        }
    };

    const handleEditSpecialty = (id: number, name: string) => {
        setEditingSpecialty({ id, name });
        setIsEditing(false);
    };

    const handleCancelEditing = () => {
        resetEditingSpecialty();
        resetNewSpecialty();
    };

    const handleChangeSpecialty = (specialtyId: number) => {
        setUser({ ...user, specialtyId: specialtyId });
        setMenuIsOpen(false);
    };

    return (
        <DropdownContainer ref={dropdownRef}>
            <Dropdown
                label={t('users.specialty')}
                placeholder={t('users.placeholder.specialty')}
                name={'users.specialty'}
                cmosVariant="default"
                menuIsOpen={menuIsOpen}
                closeMenuOnSelect={false}
                value={
                    user.specialtyId
                        ? {
                              value: user.specialtyId,
                              label: specialties.find((s) => s.id === user.specialtyId)?.name || '',
                          }
                        : null
                }
                showNoOptionsMessage={false}
                options={specialties
                    .map((specialty) => ({
                        label: specialty.name,
                        value: specialty.id,
                        id: specialty.id,
                    }))
                    .sort((a, b) => a.label.localeCompare(b.label))}
                onOpen={() => {
                    resetEditingSpecialty();
                    resetNewSpecialty();
                    setMenuIsOpen(true);
                }}
                onChange={() => setMenuIsOpen(false)}
                CustomOption={({ data }) => (
                    <>
                        {editingSpecialty?.id === data.id ? (
                            <EditContainer>
                                <TextField
                                    maxLength={50}
                                    isInvalid={!editingSpecialtyIsValid}
                                    cmosVariant="roundedPrimary"
                                    autoFocus
                                    onMouseDown={(e) => e.stopPropagation()}
                                    name={'users.placeholder.editSpecialty'}
                                    placeholder={t('users.placeholder.enterSpecialty')}
                                    type={'text'}
                                    value={editingSpecialty?.name}
                                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                                        if (inputRef.current) {
                                            cursorPositionRef.current =
                                                inputRef.current.selectionStart;
                                        }
                                        editingSpecialty &&
                                            setEditingSpecialty({
                                                ...editingSpecialty,
                                                name: event.target.value,
                                            });
                                    }}
                                    inputRef={inputRef}
                                    endAdornment={
                                        <Button
                                            color={theme.palette.primary.main}
                                            cmosVariant={'typography'}
                                            Icon={CheckIcon}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleUpdateSpecialty();
                                            }}
                                        />
                                    }
                                />
                                <Button
                                    color={theme.palette.neutral[3]}
                                    cmosVariant={'typography'}
                                    Icon={CloseIcon}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleCancelEditing();
                                    }}
                                />
                            </EditContainer>
                        ) : (
                            <OptionContainer>
                                <OptionLabel onClick={() => handleChangeSpecialty(data.id)}>
                                    {data.label}
                                </OptionLabel>

                                <EditIconWrapper className="edit-icon-wrapper">
                                    <Button
                                        color={theme.palette.neutral[6]}
                                        cmosVariant={'typography'}
                                        Icon={EditIcon}
                                        onClick={() => handleEditSpecialty(data.id, data.label)}
                                        cmosSize={'small'}
                                    />
                                </EditIconWrapper>
                            </OptionContainer>
                        )}
                        <Divider />
                    </>
                )}
                CustomFooterMenu={() => (
                    <CreateContainer>
                        {isEditing ? (
                            <>
                                <TextField
                                    maxLength={50}
                                    isInvalid={!newSpecialtyIsValid}
                                    cmosVariant="roundedPrimary"
                                    autoFocus
                                    onMouseDown={(e) => e.stopPropagation()}
                                    name={'users.placeholder.newSpecialty'}
                                    placeholder={t('users.placeholder.enterSpecialty')}
                                    type={'text'}
                                    value={newSpecialty}
                                    inputRef={inputRef}
                                    onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                                        if (inputRef.current) {
                                            cursorPositionRef.current =
                                                inputRef.current.selectionStart;
                                        }
                                        setNewSpecialty(event.target.value);
                                        setMenuIsOpen(true);
                                    }}
                                    endAdornment={
                                        <Button
                                            color={theme.palette.primary.main}
                                            cmosVariant={'typography'}
                                            Icon={CheckIcon}
                                            onClick={() => {
                                                setMenuIsOpen(true);
                                                handleCreateSpecialty();
                                            }}
                                        />
                                    }
                                />
                                <Button
                                    color={theme.palette.neutral[3]}
                                    cmosVariant={'typography'}
                                    Icon={CloseIcon}
                                    onClick={() => {
                                        setMenuIsOpen(true);
                                        handleCancelEditing();
                                    }}
                                />
                            </>
                        ) : (
                            <Button
                                cmosVariant={'stroke'}
                                color={theme.palette.neutral[3]}
                                label={t('users.placeholder.newSpecialty')}
                                Icon={PlusIcon}
                                onClick={() => {
                                    setIsEditing(true);
                                    setEditingSpecialty(null);
                                }}
                                iconPosition="right"
                                buttonInnercustomStyles={{
                                    width: '100%',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                }}
                                sx={{ display: 'flex', width: '100%' }}
                            />
                        )}
                    </CreateContainer>
                )}
            />
        </DropdownContainer>
    );
}

const DropdownContainer = styled('div')({
    width: '100%',
});

const CreateContainer = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    padding: 8,
    paddingTop: 0,
    gap: 8,
});

const EditContainer = styled('div')({
    display: 'flex',
    justifyContent: 'center',
    padding: 8,
});

const OptionContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    width: '100%',
    height: 32,
    alignItems: 'center',
    paddingLeft: 12,
    paddingRight: 12,
    cursor: 'pointer',
    '&:hover': {
        backgroundColor: theme.palette.neutral[2],
    },
    '&:hover .edit-icon-wrapper': {
        display: 'flex',
    },
}));

const OptionLabel = styled('span')({
    flexGrow: 1,
    padding: 5,
});

const EditIconWrapper = styled('div')({
    display: 'none',
    alignItems: 'center',
});

const Divider = styled('div')(({ theme }) => ({
    width: '90%',
    display: 'flex',
    alignItems: 'center',
    height: 1,
    backgroundColor: theme.palette.neutral[3],
    marginLeft: 12,
}));
