export function parseTime(s: string): [number, number] {
    const arrValue = s.split(':');
    return [+arrValue[0], +arrValue[1]];
}

export function stringifyTime([h, m]: [number, number]): string {
    return `${h.toLocaleString('en-US', {
        minimumIntegerDigits: 2,
        useGrouping: false,
    })}:${m.toLocaleString('en-US', {
        minimumIntegerDigits: 2,
        useGrouping: false,
    })}:00`;
}
