import { Box, Grid, IconButton } from '@mui/material';
import MuiTooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import styled from '@mui/styles/styled';
import { CopyIcon } from 'common/components/Icons/CopyIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { UserDto } from 'datacontracts/UserDto';
import { UserScheduleWindow } from 'datacontracts/Users';
import { WeekDay } from 'datacontracts/WeekDay.enum';
import { useMemo } from 'react';
import SettingsCheckbox from 'views/Settings/common/SettingsCheckbox';
import TeamMemberSchedule from '../../TeamMemberSchedule';
import { parseTime, stringifyTime } from './helpers';

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

export type WorkingDayProps = {
    user: UserDto;
    weekDay: WeekDay;
    onScheduleWindowChange: (
        wd: WeekDay,
        scheduleWindow: UserScheduleWindow,
        index: number
    ) => void;
    onAddSchedule: (wd: WeekDay, scheduleWindow: UserScheduleWindow, index: number) => void;
    onRemoveSchedule: (wd: WeekDay, scheduleWindow: UserScheduleWindow, index: number) => void;
    onCopySchedule: (wd: WeekDay, scheduleWindow: UserScheduleWindow, index: number) => void;
};

export default function WorkingDay({
    user,
    weekDay,
    onCopySchedule,
    onAddSchedule,
    onRemoveSchedule,
    onScheduleWindowChange,
}: WorkingDayProps) {
    const { t } = useAppTranslation();
    const daySchedule = useMemo(
        () => user.userSchedule?.find((schedule) => schedule.weekDay === weekDay),
        [user]
    );

    return (
        <>
            {daySchedule?.scheduleWindows?.map((schedule, index) => (
                <Grid key={index} container spacing={0} style={{ marginTop: index === 0 ? 0 : 23 }}>
                    <Grid item xs={9}>
                        <TeamMemberSchedule
                            value={[
                                parseTime(schedule.open as string),
                                parseTime(schedule.close as string),
                            ]}
                            onChange={(v, mode) => {
                                onScheduleWindowChange(
                                    weekDay,
                                    { ...schedule, [mode]: stringifyTime(v) },
                                    index
                                );
                            }}
                            disabled={!daySchedule?.active}
                        />
                    </Grid>
                    <Grid item xs={user.receptionScheduleEnabled ? 1 : 2}>
                        {(index === 0 && daySchedule?.scheduleWindows.length === 1) ||
                        (index === 0 && daySchedule?.scheduleWindows.length < 4) ? (
                            <IconButton
                                style={{ padding: 3 }}
                                onClick={() => onAddSchedule(weekDay, schedule, index)}
                                size="large"
                            >
                                <PlusIcon />
                            </IconButton>
                        ) : undefined}
                        {index === 0 && weekDay === WeekDay.Monday ? (
                            <IconButton
                                style={{ padding: 3 }}
                                onClick={() => onCopySchedule(weekDay, schedule, index)}
                                size="large"
                            >
                                <CopyIcon />
                            </IconButton>
                        ) : undefined}
                        {index !== 0 ? (
                            <IconButton
                                style={{ padding: 3 }}
                                onClick={() => onRemoveSchedule(weekDay, schedule, index)}
                                size="large"
                            >
                                <RemoveCircleIcon fill={Colors.Error} />
                            </IconButton>
                        ) : undefined}
                    </Grid>
                    <Grid
                        item
                        xs={user.receptionScheduleEnabled ? 2 : 1}
                        display="flex"
                        justifyContent="center"
                        sx={{ marginLeft: '-10px' }}
                    >
                        {user.receptionScheduleEnabled && (
                            <Tooltip
                                title={
                                    <Box
                                        component="div"
                                        sx={(theme) => ({
                                            fontFamily: 'Roboto',
                                            fontSize: '12px',
                                            fontStyle: 'normal',
                                            fontWeight: 400,
                                            lineHeight: 'normal',
                                            color: '#5C6477',
                                        })}
                                    >
                                        {t('users.schedule.receptionScheduleTooltip')}
                                    </Box>
                                }
                                placement="top"
                            >
                                <Box component="div">
                                    <SettingsCheckbox
                                        disabled={!daySchedule?.active}
                                        name="isReceptionSchedule"
                                        onChange={(_, checked) => {
                                            onScheduleWindowChange(
                                                weekDay,
                                                { ...schedule, isReceptionSchedule: checked },
                                                index
                                            );
                                        }}
                                        checked={schedule.isReceptionSchedule}
                                    />
                                </Box>
                            </Tooltip>
                        )}
                    </Grid>
                </Grid>
            ))}
        </>
    );
}
