import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import MuiSwitch from '@mui/material/Switch';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import MuiTooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled, useTheme } from '@mui/material/styles';
import { useMutation, useQuery } from '@tanstack/react-query';
import TeamMemberAPI from 'api/TeamMember';
import { UserPermissionDto } from 'api/account';
import UserAPI from 'api/settings/User';
import { InternationalizationLogic } from 'business/InternationalizationLogic';
import { phoneFormatRegexMask } from 'common/FormatersHelper';
import { checkInvalidPassword } from 'common/PasswordHelper';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import MaskedTextFormField from 'common/components/Inputs/MaskedTextFormField';
import PasswordField from 'common/components/Inputs/PasswordField';
import { Switch } from 'common/components/Inputs/Switch';
import TextField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { JobTitle, jobTitleLabel } from 'common/constants/JobTitle';
import { emailRegex } from 'common/constants/RegexValidation';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useForceRender from 'common/hooks/useForceRender';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { OptionStyle } from 'common/styles/OptionStyle';
import { UserDto } from 'datacontracts/UserDto';
import { UserExistsResponse } from 'datacontracts/UserExistsResponse';
import { UserScheduleWindow } from 'datacontracts/Users';
import { WeekDay } from 'datacontracts/WeekDay.enum';
import sortBy from 'lodash/sortBy';
import React, { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppSelector } from 'store';
import {
    selectRepairShopFeatures,
    selectSettings,
} from 'store/slices/globalSettingsSlice/selectors';
import { RestoreExistingTeamMember } from '../RestoreExistingTeamMember/index';
import ScheduleDay from './ScheduleDay';
import SpecialtyDropdown from './SpecialtyDropDown';
import TabPanel from './TabPanel';
import styles from './css.module.css';
import { COLOR_OPTIONS, DAYS_ORDER, getCleanUser, getEmptyUser } from './helpers';

export type TeamMemberFormProps = {
    open: boolean;
    onClose: Function;
    onSave?: (user: UserDto) => void;
    userEdit?: UserDto;
};

export const TeamMemberForm = ({ open, onClose, userEdit, onSave }: TeamMemberFormProps) => {
    const { internationalization, repairShopSettings, uid } = useSelector(selectSettings);
    const maxLengthPhone = useMemo(
        () => InternationalizationLogic.maxLengthPhone(internationalization),
        [internationalization]
    );

    const show3rPartyUserName =
        repairShopSettings?.features.campaigns.integration ||
        repairShopSettings?.features.sroValidationIntegration ||
        false;
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const theme = useTheme();

    const [user, setUser] = useState<UserDto>(getEmptyUser);
    // despite being named userEdited this is actually NOT edited user i.e. the original user that has not been changed in any way
    const [userEdited, setUserEdited] = useState<UserDto>(getEmptyUser);
    const [existUserData, setExistUserData] = useState<UserExistsResponse>();
    const [existEmail, setExistEmail] = useState<string>();
    const [existDmsIdNumber, setExistDmsIdNumber] = useState<string>();
    const [existDmsUserName, setExistDmsUserName] = useState<string>();
    const [password, setPassword] = useState('');
    const [passwordConfirm, setPasswordConfirm] = useState('');
    const [isEnteredDataValid, setIsEnteredDataValid] = useState(false);
    const [openExist, setIsOpenPopupExist] = useState(false);
    const [tabValue, setTabValue] = useState<number>(0);
    const [usedColors, setUsedColors] = useState<string[]>([]);
    const [isDmsIntegrationModalOpen, setIsDmsIntegrationModalOpen] = useState<boolean>(false);
    const enableAftersalesCrm = useAppSelector(selectRepairShopFeatures)?.enableAftersalesCrm;

    const { valid: isEmailValid, reset: resetEmailValidation } = useIsEmailValid(user.userName);

    useEffect(() => {
        resetEmailValidation();
        setExistUserData(undefined);
        setExistEmail(undefined);
        setExistDmsIdNumber(undefined);
        setExistDmsUserName(undefined);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user.userId]);

    const setUserJobTitle = (job: JobTitle) => {
        setUser((u) => {
            return {
                ...u,
                jobTitle: job,
                isActive: JOB_TITLES_ACTIVATING_USER.includes(job) ? true : u.isActive,
                permission: {
                    ...u.permission,
                    ...getDefaultJobPermissions(job),
                },
            };
        });
    };

    const saveMutation = useMutation(
        async (data: {
            successNotification: { body: string; title?: string };
            onSuccess?: () => void;
            payload: {
                user: UserDto;
                password?: string;
                confirmPassword?: string;
            };
        }) => await UserAPI.save(data.payload),
        {
            onSuccess: (response, { successNotification, onSuccess }) => {
                toasters.success(successNotification.body, successNotification.title);

                if (onSave) {
                    onSave({ ...response! });
                }

                closeHandler();

                if (onSuccess) {
                    onSuccess();
                }
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'));
            },
        }
    );

    const saveHandler = async () => {
        if (
            (tabValue === 0 || user.userId) &&
            repairShopSettings?.features.dmsIntegration &&
            !user.dmsIDNumber
        ) {
            setIsDmsIntegrationModalOpen(true);
        }
        if (tabValue !== 2 && !user.userId) {
            setTabValue(tabValue + 1);
            return;
        }

        if (!(await validationForm())) return;
        if (saveMutation.isLoading) return;

        const successNotificationBody = user.userId
            ? t('users.notifications.userUpdatedSuccessfullyBody')
            : t('users.notifications.userAddedSuccessfullyBody');

        saveMutation.mutateAsync({
            successNotification: { body: successNotificationBody },
            onSuccess: () => setTabValue(0),
            payload: {
                user: user,
                password,
                confirmPassword: passwordConfirm,
            },
        });
    };

    const validationForm = async (): Promise<boolean> => {
        if (password !== passwordConfirm) {
            toasters.danger(
                t('users.notifications.matchPasswordBody'),
                t('users.notifications.matchPasswordTitle')
            );
            return false;
        } else if (password !== '') {
            const errors = checkInvalidPassword(password);
            if (errors.length > 0) {
                toasters.danger(
                    t('users.notifications.rememberBody'),
                    t('users.notifications.rememberTitle')
                );
                return false;
            }
        }

        const checkExists = await UserAPI.getExists({
            userName: user.userName,
            dmsId: user.dmsIDNumber,
            password,
            dmsUserName: user.dmsUserName,
        });

        if (!checkExists) {
            console.error('Error CheckExist error...');
            return false;
        }

        setExistUserData(checkExists);
        let checkExistsResult = true;

        if (
            checkExists.userWithUserName &&
            checkExists.userWithUserName.userId &&
            checkExists.userWithUserName.userId !== user.userId
        ) {
            checkExistsResult = false;
            setExistEmail(user.userName);

            if (checkExists.userWithUserName.isActive) {
                toasters.danger(t('users.notifications.userNameIsUsedBody'));
            } else {
                setIsOpenPopupExist(true);
            }
        }

        if (
            user.dmsIDNumber &&
            checkExists.userWithDMSIdNumber &&
            checkExists.userWithDMSIdNumber.userId !== user.userId &&
            checkExists.userWithDMSIdNumber.isActive
        ) {
            toasters.danger(
                t('users.notifications.employeeIdIsUsedBody'),
                t('users.notifications.employeeIdIsUsedTitle')
            );

            setExistDmsIdNumber(user.dmsIDNumber);
            checkExistsResult = false;
        }

        if (
            user.dmsUserName &&
            show3rPartyUserName &&
            checkExists.userWithDmsUserName &&
            checkExists.userWithDmsUserName.userId !== user.userId &&
            checkExists.userWithDmsUserName.isActive
        ) {
            toasters.danger(
                t('users.notifications.dmsUserNameIsUsedBody'),
                t('users.notifications.dmsUserNameIsUsedTitle')
            );

            setExistDmsUserName(user.dmsUserName);
            checkExistsResult = false;
        }

        return checkExistsResult;
    };

    const { data: fetchedColors, isLoading: usedColorsAreLoading } = useQuery(
        ['teamMembers', 'usedColors', uid],
        UserAPI.getUserUsedColors
    );

    useEffect(() => {
        if (fetchedColors && fetchedColors.length) {
            setUsedColors(fetchedColors.map((x) => x.color));
        }
    }, [fetchedColors]);

    const confirmUserRestoreHandler = async () => {
        if (isLoading) return;

        if (
            !existUserData ||
            !existUserData.userWithUserName ||
            existUserData.userWithUserName.isActive
        ) {
            throw new Error('Confirm without existUserData');
        }

        if (existUserData.userWithUserName.matchPassword) {
            toasters.danger(
                t(`users.notifications.reviewPasswordBody`),
                t(`users.notifications.reviewPasswordTitle`)
            );
            return;
        }

        saveMutation.mutateAsync({
            successNotification: {
                body: t(`users.notifications.userActivatedSuccessfullyBody`),
                title: t(`users.notifications.userActivatedSuccessfullyTitle`),
            },
            payload: {
                user: {
                    ...user,
                    userId: existUserData.userWithUserName!.userId,
                    isActive: true,
                },
                password,
                confirmPassword: passwordConfirm,
            },
        });
    };

    const closeHandler = () => {
        if (onClose) {
            onClose({ ...user });
        }
        setUser(getEmptyUser());
    };

    const isEdited = useMemo(() => {
        return (
            !user.userId ||
            JSON.stringify(user) !== JSON.stringify(userEdited) ||
            (password.trim() !== '' &&
                passwordConfirm.trim() !== '' &&
                password === passwordConfirm)
        );
    }, [user, userEdited, password, passwordConfirm]);

    const { data: userSchedule, isLoading: userScheduleIsLoading } = useQuery(
        ['teamMembers', 'userSchedule', user.userId],
        async () => (user.userKey ? await TeamMemberAPI.getSchedules(user.userKey) : [])
    );

    useEffect(() => {
        if (open && userEdit != null && userSchedule) {
            setUser((old) => ({ ...old, userSchedule: userSchedule }));
            setUserEdited((old) => ({ ...old, userSchedule: userSchedule }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user.userId, userSchedule]);

    const isLoading = saveMutation.isLoading || usedColorsAreLoading || userScheduleIsLoading;

    useEffect(() => {
        setUser(getCleanUser(userEdit));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userEdit]);

    useEffect(() => {
        //Reset password
        const cleanUser = getCleanUser(userEdit);
        setUser(cleanUser);
        setUserEdited(cleanUser);
        setPassword('');
        setPasswordConfirm('');
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [user.userId, open]);

    /**
     * Update Enable Send Form
     */
    useEffect(() => {
        if (user.userId) {
            //VALIDATIONS EDIT MODE
            if (password.trim() !== '' && passwordConfirm.trim() === '')
                return setIsEnteredDataValid(false);
        } else {
            //VALIDATIONS ADD MODE
            if (password.trim() === '' || passwordConfirm.trim() === '')
                return setIsEnteredDataValid(false);
        }

        if (
            !user.displayName.trim() ||
            isEmailValid === false ||
            !user.jobTitle ||
            !user.initials ||
            !user.initials.trim() ||
            (user.jobTitle === 'ServiceAdvisor' && !user.color)
        ) {
            return setIsEnteredDataValid(false);
        }

        return setIsEnteredDataValid(true);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        user.userId,
        user.displayName,
        user.initials,
        user.jobTitle,
        user.color,
        isEmailValid,
        password,
        passwordConfirm,
    ]);

    const a11yProps = (index: number) => {
        return {
            id: `tab-${index}`,
            'aria-controls': `tabpanel-${index}`,
        };
    };

    const handleAddSchedule = (
        weekDay: WeekDay,
        scheduleWindow: UserScheduleWindow,
        index: number
    ) => {
        const schedule = user.userSchedule.find((workDay) => workDay.weekDay === weekDay);
        if (schedule) {
            const scheduleWindow: UserScheduleWindow = {
                open:
                    schedule.scheduleWindows && schedule.scheduleWindows.length
                        ? schedule.scheduleWindows[0].close
                        : '08:00',
                close: '19:00:00',
                isReceptionSchedule: false,
            };
            setUser((old) => ({
                ...old,
                userSchedule: [
                    ...user.userSchedule.filter((workDay) => workDay.weekDay !== weekDay),
                    {
                        ...schedule,
                        scheduleWindows: [...(schedule.scheduleWindows || []), scheduleWindow],
                    },
                ],
            }));
        }
    };

    const handleCopySchedule = () => {
        const schedule = user.userSchedule.find((workDay) => workDay.weekDay === 1);
        if (schedule) {
            setUser((old) => ({
                ...old,
                userSchedule: [
                    schedule,
                    { ...schedule, weekDay: 2 },
                    { ...schedule, weekDay: 3 },
                    { ...schedule, weekDay: 4 },
                    { ...schedule, weekDay: 5 },
                    { ...schedule, weekDay: 6 },
                    { ...schedule, weekDay: 0 },
                ],
            }));
        }
    };

    const handleRemoveSchedule = (
        weekDay: WeekDay,
        scheduleWindow: UserScheduleWindow,
        index: number
    ) => {
        const schedule = user.userSchedule.find((workDay) => workDay.weekDay === weekDay);
        if (schedule) {
            setUser((old) => ({
                ...old,
                userSchedule: [
                    ...user.userSchedule.filter((workDay) => workDay.weekDay !== weekDay),
                    {
                        ...schedule,
                        scheduleWindows: schedule.scheduleWindows.filter((_, i) => i !== index),
                    },
                ],
            }));
        }
    };

    const handleScheduleChecked = (weekDay: WeekDay, checked: boolean) => {
        const schedule = user.userSchedule.find((workDay) => workDay.weekDay === weekDay);
        if (schedule) {
            setUser((old) => ({
                ...old,
                userSchedule: [
                    ...old.userSchedule.filter((workDay) => workDay.weekDay !== weekDay),
                    { ...schedule, active: checked },
                ],
            }));
        }
    };

    const handleScheduleWindowChange = (
        weekDay: WeekDay,
        scheduleWindow: UserScheduleWindow,
        index: number
    ) => {
        const schedule = user.userSchedule.find((workDay) => workDay.weekDay === weekDay);
        if (schedule) {
            setUser((old) => ({
                ...old,
                userSchedule: [
                    ...user.userSchedule.filter((workDay) => workDay.weekDay !== weekDay),
                    {
                        ...schedule,
                        scheduleWindows: [
                            ...(schedule.scheduleWindows?.filter((_, idx) => idx !== index) || []),
                            scheduleWindow,
                        ].sort((a, b) => {
                            if ((a.open || '') < (b.open || '')) {
                                return -1;
                            }
                            if ((a.open || '') > (b.open || '')) {
                                return 1;
                            }
                            return 0;
                        }),
                    },
                ],
            }));
        }
    };

    const jobTitleOptions = useMemo(() => {
        const options: { label: string; value: JobTitle }[] = [
            {
                value: 'Administrator',
                label: t(jobTitleLabel('Administrator')),
            },
            {
                value: 'Owner',
                label: t(jobTitleLabel('Owner')),
            },
            {
                value: 'Manager',
                label: t(jobTitleLabel('Manager')),
            },
            {
                value: 'WorkshopManager',
                label: t(jobTitleLabel('WorkshopManager')),
            },
            {
                value: 'Parts',
                label: t(jobTitleLabel('Parts')),
            },
            {
                value: 'ServiceAdvisor',
                label: t(jobTitleLabel('ServiceAdvisor')),
            },
            {
                value: 'Technician',
                label: t(jobTitleLabel('Technician')),
            },
            {
                value: 'CarWasher',
                label: t(jobTitleLabel('CarWasher')),
            },
            {
                value: 'AppointmentsExecutive',
                label: t(jobTitleLabel('AppointmentsExecutive')),
            },
            {
                value: 'Other',
                label: t(jobTitleLabel('Other')),
            },
        ];

        if (
            repairShopSettings?.features.enableAftersalesCrm ||
            userEdited.jobTitle === 'BdcAdvisor' ||
            userEdited.jobTitle === 'BdcSupervisor'
        ) {
            options.push(
                {
                    value: 'BdcSupervisor',
                    label: t(jobTitleLabel('BdcSupervisor')),
                },
                {
                    value: 'BdcAdvisor',
                    label: t(jobTitleLabel('BdcAdvisor')),
                }
            );
        }

        return sortBy(options, (x) => x.label);
    }, [t, repairShopSettings?.features.enableAftersalesCrm, userEdited.jobTitle]);

    return (
        <>
            <Modal open={open}>
                <BoxContent component="div" sx={{ width: 1060, height: 577 }}>
                    {/* #region Title / Buttons */}
                    <DivRow style={{ marginBottom: 40 }}>
                        <DivTitle>{t(`users.${user.userId ? 'edit' : 'add'}.title`)}</DivTitle>
                        <DivButtons>
                            <ButtonCancel
                                label={
                                    tabValue === 0 || user.userId
                                        ? t('commonLabels.cancel')
                                        : t('commonLabels.goBack')
                                }
                                onClick={() => {
                                    if (tabValue !== 0 && !user.userId) {
                                        setTabValue(tabValue - 1);
                                    } else if (onClose) {
                                        onClose(user);
                                        setTabValue(0);
                                    }
                                }}
                                cmosVariant={'filled'}
                                color={theme.palette.neutral[3]}
                                cmosSize={'medium'}
                            />
                            <ButtonSuccess
                                data-test-id="save-button"
                                showLoader={saveMutation.isLoading}
                                disabled={!isEdited || !isEnteredDataValid || isLoading}
                                label={
                                    user.userId
                                        ? t('users.edit.saveButton')
                                        : tabValue === 2
                                        ? t('users.add.saveButton')
                                        : t('commonLabels.continue')
                                }
                                onClick={saveHandler}
                                cmosVariant={'filled'}
                                color={theme.palette.success.main}
                                cmosSize={'medium'}
                            />
                        </DivButtons>
                    </DivRow>

                    <Tabs
                        classes={{ indicator: styles.tabsIndicator }}
                        value={tabValue}
                        style={{ marginBottom: 36 }}
                        onChange={(event: ChangeEvent<{}>, newValue: number) => {
                            setTabValue(newValue);
                        }}
                    >
                        <Tab
                            aria-label={t('users.generalInformation')}
                            label={t('users.generalInformation')}
                            classes={{ root: styles.tabRoot, selected: styles.tabSelected }}
                            {...a11yProps(0)}
                        />
                        <Tab
                            aria-label={t('users.schedules')}
                            label={t('users.schedules')}
                            classes={{ root: styles.tabRoot, selected: styles.tabSelected }}
                            disabled={!isEnteredDataValid}
                            {...a11yProps(1)}
                        />
                        <Tab
                            aria-label={t('users.permissions.sectionTitle')}
                            label={t('users.permissions.sectionTitle')}
                            classes={{ root: styles.tabRoot, selected: styles.tabSelected }}
                            disabled={!isEnteredDataValid}
                            {...a11yProps(2)}
                        />
                    </Tabs>

                    {/* #region General Data */}
                    <TabPanel value={tabValue} index={0}>
                        <DivRow style={{ paddingBottom: 38 }}>
                            <DivCol3 style={{ paddingRight: 25, gap: 16 }}>
                                <DivColBigPart>
                                    <TextField
                                        block
                                        label={t('users.fullName')}
                                        placeholder={t('users.placeholder.fullName')}
                                        name={'users.fullName'}
                                        isRequired={true}
                                        maxLength={128}
                                        value={user.displayName}
                                        onChange={(event) => {
                                            if (isLoading) return;
                                            setUser((old) => ({
                                                ...old,
                                                displayName: event.target.value,
                                            }));
                                        }}
                                        showValidationIndicators={true}
                                    />
                                </DivColBigPart>
                                <DivColSmallPart>
                                    <TextField
                                        block
                                        label={t('users.initials')}
                                        placeholder={t('users.placeholder.initials')}
                                        name={'users.initials'}
                                        isRequired={true}
                                        value={user.initials}
                                        maxLength={2}
                                        onChange={(event) => {
                                            if (isLoading) return;
                                            setUser((old) => ({
                                                ...old,
                                                initials: event.target.value,
                                            }));
                                        }}
                                        showValidationIndicators={true}
                                    />
                                </DivColSmallPart>
                            </DivCol3>
                            <DivCol3 style={{ paddingRight: 25, paddingLeft: 25 }}>
                                <TextField
                                    block
                                    label={t('users.email')}
                                    placeholder={t('users.placeholder.email')}
                                    name={'users.email'}
                                    autoComplete={'new-password'}
                                    isRequired={true}
                                    value={user.userName}
                                    maxLength={128}
                                    isInvalid={
                                        isEmailValid === false ||
                                        (existEmail !== undefined && user.userName === existEmail)
                                    }
                                    onChange={(event) => {
                                        setUser((old) => ({
                                            ...old,
                                            userName: event.target.value,
                                        }));
                                    }}
                                    showValidationIndicators={true}
                                />
                            </DivCol3>
                            <DivCol3 style={{ paddingLeft: 25 }}>
                                <Dropdown
                                    label={t('users.jobPosition')}
                                    placeholder={t('users.placeholder.jobPosition')}
                                    name={'users.jobPosition'}
                                    isRequired
                                    cmosVariant="default"
                                    showValidationIndicators
                                    value={
                                        user.jobTitle
                                            ? {
                                                  value: user.jobTitle,
                                                  label: user.jobTitle
                                                      ? t(jobTitleLabel(user.jobTitle))
                                                      : user.jobTitle,
                                              }
                                            : null
                                    }
                                    isSearchable={false}
                                    onChange={(event) => event && setUserJobTitle(event.value)}
                                    options={jobTitleOptions}
                                    size="small"
                                />
                            </DivCol3>
                        </DivRow>
                        <DivRow style={{ paddingBottom: 38 }}>
                            <DivCol3 style={{ paddingRight: 25 }}>
                                <SpecialtyDropdown user={user} setUser={setUser} />
                            </DivCol3>
                            <DivCol3 style={{ paddingRight: 25, paddingLeft: 25, gap: 16 }}>
                                <DivColBigPart>
                                    <MaskedTextFormField
                                        fullWidth
                                        label={t('users.phone')}
                                        placeholder={t('users.placeholder.phone')}
                                        name={'users.phone'}
                                        maxLength={maxLengthPhone}
                                        isRequired={false}
                                        value={user.landlinePhoneNumber}
                                        onChange={(event) => {
                                            if (isLoading) return;
                                            setUser((old) => ({
                                                ...old,
                                                landlinePhoneNumber: event.target.value,
                                            }));
                                        }}
                                        showValidationIndicators={false}
                                        mask={phoneFormatRegexMask(
                                            internationalization.phoneNumberFormat
                                        )}
                                    />
                                </DivColBigPart>
                                <DivColSmallPart>
                                    <TextField
                                        block
                                        label={t('users.phoneExtension')}
                                        placeholder={t('users.placeholder.phoneExtension')}
                                        name={'users.phoneExtension'}
                                        maxLength={10}
                                        type="number"
                                        isRequired={false}
                                        value={user.landlinePhoneExtension}
                                        onChange={(event) => {
                                            if (isLoading) return;
                                            setUser((old) => ({
                                                ...old,
                                                landlinePhoneExtension: event.target.value,
                                            }));
                                        }}
                                        showValidationIndicators={false}
                                    />
                                </DivColSmallPart>
                            </DivCol3>
                            <DivCol3 style={{ paddingLeft: 25 }}>
                                <MaskedTextFormField
                                    fullWidth
                                    label={t('users.mobile')}
                                    placeholder={t('users.placeholder.mobile')}
                                    name={'users.mobile'}
                                    maxLength={maxLengthPhone}
                                    isRequired={false}
                                    value={user.mobilePhoneNumber}
                                    onChange={(event) => {
                                        if (isLoading) return;
                                        setUser((old) => ({
                                            ...old,
                                            mobilePhoneNumber: event.target.value,
                                        }));
                                    }}
                                    showValidationIndicators={false}
                                    mask={phoneFormatRegexMask(
                                        internationalization.phoneNumberFormat
                                    )}
                                />
                            </DivCol3>
                        </DivRow>

                        <DivRow style={{ paddingBottom: 38 }}>
                            <DivCol3 style={{ paddingRight: 25 }}>
                                <Dropdown<string>
                                    name="color"
                                    label={t('users.serviceAdvisorColor')}
                                    placeholder={t('users.selectAColor')}
                                    optionStyle={OptionStyle.icons}
                                    isRequired={true}
                                    isSearchable={false}
                                    showValidationIndicators={true}
                                    disabled={user && user.jobTitle !== 'ServiceAdvisor'}
                                    cmosVariant="default"
                                    value={
                                        user.color
                                            ? {
                                                  label: '',
                                                  value: user.color,
                                                  icon: CircleDiv,
                                                  color: user.color,
                                              }
                                            : undefined
                                    }
                                    onChange={(event) => {
                                        if (event === null) return;
                                        setUser((old) => ({
                                            ...old,
                                            color: event.value,
                                        }));
                                    }}
                                    CustomMenu={(props) => {
                                        return (
                                            <DivColorsContainer>
                                                {COLOR_OPTIONS.map((palette, optIdx) => (
                                                    <DivColorsRow key={`lst-${optIdx}`}>
                                                        {palette.map((color) => (
                                                            <IconButton
                                                                key={color}
                                                                disableRipple={usedColors.includes(
                                                                    color
                                                                )}
                                                                className={
                                                                    usedColors.includes(color)
                                                                        ? styles.disabledColor
                                                                        : ''
                                                                }
                                                                onClick={() => {
                                                                    if (usedColors.includes(color))
                                                                        return;
                                                                    props.selectOption({
                                                                        label: '',
                                                                        value: color,
                                                                        icon: CircleDiv,
                                                                        color: color,
                                                                    });
                                                                }}
                                                                size="large"
                                                            >
                                                                <CircleDiv fill={color as Colors} />
                                                            </IconButton>
                                                        ))}
                                                    </DivColorsRow>
                                                ))}
                                            </DivColorsContainer>
                                        );
                                    }}
                                />
                            </DivCol3>
                            <DivCol3 style={{ paddingRight: 25, paddingLeft: 25 }}>
                                <PasswordField
                                    block
                                    label={t('users.password')}
                                    placeholder={t('users.placeholder.password')}
                                    name={'users.password'}
                                    isRequired={true}
                                    autoComplete={'new-password'}
                                    value={password}
                                    onChange={(e) => {
                                        if (isLoading) return;
                                        setPassword(e.target.value);
                                    }}
                                    showValidationIndicators={true}
                                />
                            </DivCol3>
                            <DivCol3 style={{ paddingLeft: 25 }}>
                                <PasswordField
                                    block
                                    label={t('users.confirmPassword')}
                                    placeholder={t('users.placeholder.confirmPassword')}
                                    name={'users.confirmPassword'}
                                    autoComplete={'new-password'}
                                    onChange={(e) => {
                                        if (isLoading) return;
                                        setPasswordConfirm(e.target.value);
                                    }}
                                    isRequired={true}
                                    value={passwordConfirm}
                                    showValidationIndicators={true}
                                />
                            </DivCol3>
                        </DivRow>
                        <DivRow style={{ paddingBottom: 38 }}>
                            <DivCol3 style={{ paddingRight: 25 }}>
                                <TextField
                                    block
                                    label={t('users.employeeId')}
                                    placeholder={t('users.placeholder.employeeId')}
                                    name={'users.DMSID'}
                                    isRequired={false}
                                    isInvalid={
                                        existDmsIdNumber !== undefined &&
                                        user.dmsIDNumber === existDmsIdNumber
                                    }
                                    value={user.dmsIDNumber}
                                    onChange={(event) => {
                                        if (isLoading) return;
                                        setUser((old) => ({
                                            ...old,
                                            dmsIDNumber: event.target.value,
                                        }));
                                    }}
                                    showValidationIndicators={false}
                                />
                            </DivCol3>
                            {show3rPartyUserName && (
                                <>
                                    <DivCol3 style={{ paddingRight: 25, paddingLeft: 25 }}>
                                        <TextField
                                            block
                                            label={t('users.3rPartyUserName')}
                                            placeholder={t('users.placeholder.3rPartyUserName')}
                                            name={'users.3rPartyUserName'}
                                            isRequired={false}
                                            isInvalid={
                                                existDmsUserName !== undefined &&
                                                user.dmsUserName === existDmsUserName
                                            }
                                            value={user.dmsUserName}
                                            onChange={(event) => {
                                                if (isLoading) return;
                                                setUser((old) => ({
                                                    ...old,
                                                    dmsUserName: event.target.value.toUpperCase(),
                                                }));
                                            }}
                                            showValidationIndicators={false}
                                        />
                                    </DivCol3>
                                    <DivCol3 style={{ paddingRight: 25 }} />
                                </>
                            )}
                        </DivRow>
                        <DivPasswordMessage>
                            {t('users.passwordSecurityMessage')}
                        </DivPasswordMessage>
                    </TabPanel>

                    <TabPanel value={tabValue} index={1}>
                        <>
                            <Grid container spacing={0}>
                                <Grid item xs={6} sx={{ display: 'flex' }}>
                                    <SwitchNoLabel
                                        checked={user.receptionScheduleEnabled}
                                        onChange={(_, checked: boolean) => {
                                            if (isLoading) return;
                                            setUser({
                                                ...user,
                                                receptionScheduleEnabled: checked,
                                            });
                                        }}
                                        name="checkedA"
                                        sx={{ marginLeft: '-11px' }}
                                    />
                                    <Box component="div">
                                        <Box
                                            component="div"
                                            sx={(theme) => ({
                                                ...theme.typography.h6Inter,
                                                fontWeight: 700,
                                                color: theme.palette.neutral[8],
                                                width: '360px',
                                            })}
                                        >
                                            {t(`users.schedule.appointmentReceptionLabel.title`)}
                                        </Box>
                                        <Box
                                            component="div"
                                            sx={(theme) => ({
                                                ...theme.typography.h6Inter,
                                                fontWeight: 400,
                                                color: theme.palette.neutral[8],
                                                width: '360px',
                                            })}
                                        >
                                            {t(
                                                `users.schedule.appointmentReceptionLabel.description`
                                            )}
                                        </Box>
                                    </Box>
                                </Grid>
                                <Grid item xs={6}>
                                    <Box display="flex">
                                        <Box
                                            component="div"
                                            display="flex"
                                            alignContent="center"
                                            sx={(theme) => ({
                                                ...theme.typography.h6Inter,
                                                color: theme.palette.neutral[8],
                                                marginRight: '26px',
                                            })}
                                        >
                                            {t('users.schedule.appointmentsPerDay')}
                                            <Tooltip
                                                title={
                                                    <Box
                                                        component="div"
                                                        sx={(theme) => ({
                                                            fontFamily: 'Roboto',
                                                            fontSize: '12px',
                                                            fontStyle: 'normal',
                                                            fontWeight: 400,
                                                            lineHeight: 'normal',
                                                            color: '#5C6477',
                                                        })}
                                                    >
                                                        {t(
                                                            'users.schedule.appointmentsPerDayTooltip'
                                                        )}
                                                    </Box>
                                                }
                                                placement="top"
                                            >
                                                <Box
                                                    component="div"
                                                    sx={(theme) => ({
                                                        width: 14,
                                                        height: 14,
                                                        display: 'inline-block',
                                                        borderRadius: '50%',
                                                        backgroundColor: theme.palette.primary.main,
                                                        marginLeft: '5px',
                                                    })}
                                                >
                                                    <InfoIcon size={14} fill="#FFFFFF" />
                                                </Box>
                                            </Tooltip>
                                        </Box>
                                        <Box sx={{ width: '100px' }}>
                                            <TextField
                                                type="number"
                                                value={`${user.appointmentsPerDay}`}
                                                InputProps={{
                                                    inputProps: { min: 1, max: 999 },
                                                }}
                                                onChange={(event) => {
                                                    if (+event.target.value > 999) {
                                                        return;
                                                    }

                                                    setUser((old) => ({
                                                        ...old,
                                                        appointmentsPerDay: +event.target.value,
                                                    }));
                                                }}
                                            />
                                        </Box>
                                    </Box>
                                </Grid>
                            </Grid>
                            <Box
                                component="div"
                                sx={{
                                    width: '100%',
                                    borderTop: `1px solid #EFEFEF`,
                                    marginTop: '20px',
                                    marginBottom: '30px',
                                }}
                            />
                        </>

                        <GridRootSchedule container spacing={0} height={310}>
                            <Grid item xs={12}>
                                <Grid container spacing={0}>
                                    <Grid item xs={2}>
                                        <TypographyScheduleCaption>
                                            {t('users.schedule.availability')}
                                        </TypographyScheduleCaption>
                                    </Grid>
                                    <Grid item xs={10}>
                                        <Grid container spacing={0}>
                                            <Grid item xs={9}>
                                                <Grid container spacing={1}>
                                                    <Grid
                                                        item
                                                        xs={6}
                                                        display="flex"
                                                        justifyContent="center"
                                                    >
                                                        <TypographyScheduleCaption>
                                                            {t('users.schedule.startOfWork')}
                                                        </TypographyScheduleCaption>
                                                    </Grid>
                                                    <Grid
                                                        item
                                                        xs={6}
                                                        display="flex"
                                                        justifyContent="center"
                                                    >
                                                        <TypographyScheduleCaption>
                                                            {t('users.schedule.closureOfWork')}
                                                        </TypographyScheduleCaption>
                                                    </Grid>
                                                </Grid>
                                            </Grid>
                                            <Grid item xs={1} />
                                            <Grid
                                                item
                                                xs={2}
                                                display="flex"
                                                justifyContent="center"
                                                sx={{ marginLeft: '-10px' }}
                                            >
                                                {user.receptionScheduleEnabled && (
                                                    <>
                                                        <TypographyScheduleCaption>
                                                            {t('users.schedule.receptionSchedule')}
                                                        </TypographyScheduleCaption>
                                                        <Tooltip
                                                            title={
                                                                <Box
                                                                    component="div"
                                                                    sx={(theme) => ({
                                                                        fontFamily: 'Roboto',
                                                                        fontSize: '12px',
                                                                        fontStyle: 'normal',
                                                                        fontWeight: 400,
                                                                        lineHeight: 'normal',
                                                                        color: '#5C6477',
                                                                    })}
                                                                >
                                                                    {t(
                                                                        'users.schedule.receptionScheduleTooltip'
                                                                    )}
                                                                </Box>
                                                            }
                                                            placement="top-end"
                                                        >
                                                            <Box
                                                                component="div"
                                                                sx={(theme) => ({
                                                                    width: 14,
                                                                    height: 14,
                                                                    display: 'inline-block',
                                                                    borderRadius: '50%',
                                                                    backgroundColor:
                                                                        theme.palette.primary.main,
                                                                    marginLeft: '3px',
                                                                    marginRight: '3px',
                                                                })}
                                                            >
                                                                <InfoIcon
                                                                    size={14}
                                                                    fill="#FFFFFF"
                                                                />
                                                            </Box>
                                                        </Tooltip>
                                                    </>
                                                )}
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                </Grid>
                                {DAYS_ORDER.map((day, index) => {
                                    return (
                                        <React.Fragment key={index}>
                                            <ScheduleDay
                                                onChecked={handleScheduleChecked}
                                                onAddSchedule={handleAddSchedule}
                                                onRemoveSchedule={handleRemoveSchedule}
                                                onCopySchedule={handleCopySchedule}
                                                onScheduleWindowChange={handleScheduleWindowChange}
                                                user={user}
                                                weekDay={day}
                                                style={{ marginTop: 7 }}
                                            />
                                            {index !== DAYS_ORDER.length - 1 ? (
                                                <DivScheduleSeparator />
                                            ) : undefined}
                                        </React.Fragment>
                                    );
                                })}
                            </Grid>
                        </GridRootSchedule>
                    </TabPanel>

                    <TabPanel value={tabValue} index={2}>
                        <GridRootPermission container spacing={0}>
                            <DivPermissionsTitle>
                                {t('users.permissions.administrationSectionTitle')}
                            </DivPermissionsTitle>
                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.settingsAccess}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                settingsAccess: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.administrationLabel.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.administrationLabel.description`)}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionsSeparator />

                            <DivPermissionsTitle>
                                {t('users.permissions.activitySectionTitle')}
                            </DivPermissionsTitle>
                            <DivPermissionActive>
                                <Switch
                                    checked={user.isActive}
                                    onChange={(_, checked: boolean) => {
                                        setUser({
                                            ...user,
                                            isActive: checked,
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.activeSwitchContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.activeSwitchContainer.description`)}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            {repairShopSettings?.features.estimateReview ? (
                                <DivPermissionActive>
                                    <Switch
                                        checked={user.permission.allowShowEstimates}
                                        onChange={(_, checked: boolean) => {
                                            setUser({
                                                ...user,
                                                permission: {
                                                    ...user.permission,
                                                    allowShowEstimates: checked,
                                                },
                                            });
                                        }}
                                        name="checkedA"
                                    />
                                    <div>
                                        <DivActiveLabel>
                                            {t(`users.permissions.showEstimationContainer.title`)}
                                        </DivActiveLabel>
                                        <DivActiveDescription>
                                            {t(
                                                `users.permissions.showEstimationContainer.description`
                                            )}
                                        </DivActiveDescription>
                                    </div>
                                </DivPermissionActive>
                            ) : (
                                ''
                            )}

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowEditEstimates}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowEditEstimates: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.changeEstimationContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(
                                            `users.permissions.changeEstimationContainer.description`
                                        )}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowGenerateReports}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowGenerateReports: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.generateReportContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.generateReportContainer.description`)}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowSeeAppointments}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSeeAppointments: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.seeAllAppointments.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.seeAllAppointments.description`)}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowEditAppointments}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowEditAppointments: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.createAndEditAppointments.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(
                                            `users.permissions.createAndEditAppointments.description`
                                        )}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowSeeAllOrders}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSeeAllOrders: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.seeAllOrdersContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.seeAllOrdersContainer.description`)}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowEditOrders}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowEditOrders: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.createAndEditOrdersContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(
                                            `users.permissions.createAndEditOrdersContainer.description`
                                        )}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowReopenOrders}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowReopenOrders: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.allowReopenOrdersContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(
                                            `users.permissions.allowReopenOrdersContainer.description`
                                        )}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowSeeAllConversations}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSeeAllConversations: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.seeAllConversationContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(
                                            `users.permissions.seeAllConversationContainer.description`
                                        )}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowEditWorkshopPlanner}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowEditWorkshopPlanner: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(
                                            `users.permissions.editInfoInWorkshopPlannerContainer.title`
                                        )}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(
                                            `users.permissions.editInfoInWorkshopPlannerContainer.description`
                                        )}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>

                            {repairShopSettings?.features.enableWp && (
                                <>
                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowShowJobs}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowShowJobs: checked,
                                                        allowSeeAllJobs: checked
                                                            ? user.permission.allowSeeAllJobs
                                                            : false,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <div>
                                            <DivActiveLabel>
                                                {t(`users.permissions.allowShowJobs.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.allowShowJobs.description`)}
                                            </DivActiveDescription>
                                        </div>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowSeeAllJobs}
                                            disabled={!user.permission.allowShowJobs}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowSeeAllJobs: checked,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <OpacityContainer isEnabled={user.permission.allowShowJobs}>
                                            <DivActiveLabel>
                                                {t(`users.permissions.allowSeeAllJobs.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.allowSeeAllJobs.description`)}
                                            </DivActiveDescription>
                                        </OpacityContainer>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowManageJobs}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowManageJobs: checked,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <div>
                                            <DivActiveLabel>
                                                {t(`users.permissions.allowManageJobs.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.allowManageJobs.description`)}
                                            </DivActiveDescription>
                                        </div>
                                    </DivPermissionActive>
                                </>
                            )}
                            {enableAftersalesCrm && (
                                <>
                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowEditVehicles}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowEditVehicles: checked,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <Box>
                                            <DivActiveLabel>
                                                {t(`users.permissions.editVehicles.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.editVehicles.description`)}
                                            </DivActiveDescription>
                                        </Box>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowEditCustomers}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowEditCustomers: checked,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <Box>
                                            <DivActiveLabel>
                                                {t(`users.permissions.editCustomers.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.editCustomers.description`)}
                                            </DivActiveDescription>
                                        </Box>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowSeeTasks}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowSeeTasks: checked,
                                                        allowSeeAllTasks: checked
                                                            ? user.permission.allowSeeAllTasks
                                                            : false,
                                                        allowEditTasks: checked
                                                            ? user.permission.allowSeeAllTasks
                                                            : false,
                                                        allowManageMassTasks: checked
                                                            ? user.permission.allowSeeAllTasks
                                                            : false,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <Box>
                                            <DivActiveLabel>
                                                {t(`users.permissions.seeTasks.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.seeTasks.description`)}
                                            </DivActiveDescription>
                                        </Box>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowSeeAllTasks}
                                            disabled={!user.permission.allowSeeTasks}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowSeeAllTasks: checked,
                                                        allowEditTasks: checked
                                                            ? user.permission.allowSeeAllTasks
                                                            : false,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <OpacityContainer isEnabled={user.permission.allowSeeTasks}>
                                            <DivActiveLabel>
                                                {t(`users.permissions.seeAllTasks.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.seeAllTasks.description`)}
                                            </DivActiveDescription>
                                        </OpacityContainer>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowEditTasks}
                                            disabled={!user.permission.allowSeeAllTasks}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowEditTasks: checked,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <OpacityContainer
                                            isEnabled={user.permission.allowSeeAllTasks}
                                        >
                                            <DivActiveLabel>
                                                {t(`users.permissions.editTasks.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(`users.permissions.editTasks.description`)}
                                            </DivActiveDescription>
                                        </OpacityContainer>
                                    </DivPermissionActive>

                                    <DivPermissionActive>
                                        <Switch
                                            checked={user.permission.allowManageMassTasks}
                                            disabled={!user.permission.allowSeeTasks}
                                            onChange={(_, checked: boolean) => {
                                                if (isLoading) return;
                                                setUser({
                                                    ...user,
                                                    permission: {
                                                        ...user.permission,
                                                        allowManageMassTasks: checked,
                                                    },
                                                });
                                            }}
                                            name="checkedA"
                                        />
                                        <OpacityContainer isEnabled={user.permission.allowSeeTasks}>
                                            <DivActiveLabel>
                                                {t(`users.permissions.allowManageMassTasks.title`)}
                                            </DivActiveLabel>
                                            <DivActiveDescription>
                                                {t(
                                                    `users.permissions.allowManageMassTasks.description`
                                                )}
                                            </DivActiveDescription>
                                        </OpacityContainer>
                                    </DivPermissionActive>
                                </>
                            )}

                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.allowSendMassiveSendings}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                allowSendMassiveSendings: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <Box>
                                    <DivActiveLabel>
                                        {t(`users.permissions.sendMassiveSendings.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.sendMassiveSendings.description`)}
                                    </DivActiveDescription>
                                </Box>
                            </DivPermissionActive>

                            <DivPermissionsSeparator />

                            <DivPermissionsTitle>
                                {t('users.permissions.notificationSectionTitle')}
                            </DivPermissionsTitle>
                            <DivPermissionActive>
                                <Switch
                                    checked={user.permission.shouldReceiveUploadNotifications}
                                    onChange={(_, checked: boolean) => {
                                        if (isLoading) return;
                                        setUser({
                                            ...user,
                                            permission: {
                                                ...user.permission,
                                                shouldReceiveUploadNotifications: checked,
                                            },
                                        });
                                    }}
                                    name="checkedA"
                                />
                                <div>
                                    <DivActiveLabel>
                                        {t(`users.permissions.notificationsContainer.title`)}
                                    </DivActiveLabel>
                                    <DivActiveDescription>
                                        {t(`users.permissions.notificationsContainer.description`)}
                                    </DivActiveDescription>
                                </div>
                            </DivPermissionActive>
                        </GridRootPermission>
                    </TabPanel>
                </BoxContent>
            </Modal>
            {openExist && (
                <RestoreExistingTeamMember
                    open={openExist}
                    onClose={() => setIsOpenPopupExist(false)}
                    onConfirm={confirmUserRestoreHandler}
                />
            )}
            <Modal
                open={isDmsIntegrationModalOpen}
                onClose={() => setIsDmsIntegrationModalOpen(false)}
            >
                <DivDmsIdModalHeader>
                    <DivDmsIdCloseIcon onClick={() => setIsDmsIntegrationModalOpen(false)}>
                        <CloseIcon />
                    </DivDmsIdCloseIcon>
                </DivDmsIdModalHeader>
                <DivDmsIdModalContent>
                    <DivDmsIdModalIcon>
                        <CheckIcon fill={theme.palette.warning.main} size={IconSize.M} />
                    </DivDmsIdModalIcon>
                    <SpanDmsIdModalCaption>
                        {t('users.dmsModal.emptyDmsIdField')}
                    </SpanDmsIdModalCaption>
                    <SpanDmsIdModalSubtitle>
                        {t('users.dmsModal.theTeamMemberWillNotAbleWithOutDMSId')}
                    </SpanDmsIdModalSubtitle>
                </DivDmsIdModalContent>
            </Modal>
        </>
    );
};

const SwitchNoLabel = styled(MuiSwitch)(({ theme }) => ({
    '& .MuiSwitch-switchBase': {
        '&.Mui-checked': {
            color: '#FFFFFF',
            '& .MuiSwitch-thumb:before': {
                color: '#FFFFFF',
            },
            '& + .MuiSwitch-track': {
                opacity: 1,
            },
        },
        '& .MuiSwitch-thumb:before': {
            color: '#FFFFFF',
        },
    },
}));

const Tooltip = styled(({ className, ...props }: TooltipProps) => (
    <MuiTooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        borderRadius: '5px',
        filter: `drop-shadow(1px 1px 1px ${theme.palette.neutral[7]})`,
    },
}));

const BoxContent = styled(Box)({
    marginTop: 52,
    marginBottom: 52,
    marginRight: 85,
    marginLeft: 85,
});

const DivRow = styled('div')({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
});

const DivTitle = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',

    ...theme.typography.h4Roboto,
    color: theme.palette.neutral[8],
}));

const DivButtons = styled('div')({
    width: '50.94%',
    display: 'flex',
    justifyContent: 'right',
});

const ButtonCancel = styled(Button)({
    width: 164,
    marginRight: 12,
});

const ButtonSuccess = styled(Button)({
    width: 227,
});

const DivCol3 = styled('div')({
    width: 'calc(100% / 3)',
    display: 'flex',
    justifyContent: 'space-between',
});

const DivColBigPart = styled('div')({
    width: 250,
});

const DivColSmallPart = styled('div')({
    width: 59,
});

const CircleDiv = styled('div')<{ fill?: string }>(({ fill }) => ({
    height: 20,
    width: 20,
    backgroundColor: fill,
    border: fill === '#FFFFFF' ? '1px solid #DDDDDD' : undefined,
    borderRadius: '50%',
    display: 'inline-block',
}));

const DivColorsContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    height: 212,
    overflowY: 'scroll',
    position: 'relative',
    '&::-webkit-scrollbar': {
        width: '5px',
    },
    '&::-webkit-scrollbar-track': {
        background: theme.palette.neutral[4],
        borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb': {
        background: theme.palette.neutral[6],
        borderRadius: '10px',
    },
}));

const DivColorsRow = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    '& button': {
        padding: 13,
    },
});

const DivPasswordMessage = styled(DivRow)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[5],
    fontWeight: 'normal',

    paddingTop: 29,
    paddingBottom: 29,
}));

const GridRootSchedule = styled(Grid)(({ theme }) => ({
    overflowY: 'scroll',
    '&::-webkit-scrollbar': {
        width: '5px',
    },
    '&::-webkit-scrollbar-track': {
        background: theme.palette.neutral[4],
        borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb': {
        background: theme.palette.neutral[6],
        borderRadius: '10px',
    },
}));

const TypographyScheduleCaption = styled(Typography)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[6],
}));

const DivScheduleSeparator = styled('div')(({ theme }) => ({
    borderTop: `1px solid ${theme.palette.neutral[3]}`,
    width: '100%',
    marginTop: 40,
    marginBottom: 40,
}));

const GridRootPermission = styled(Grid)(({ theme }) => ({
    height: 420,
    overflowY: 'scroll',
    '&::-webkit-scrollbar': {
        width: '5px',
    },
    '&::-webkit-scrollbar-track': {
        background: theme.palette.neutral[4],
        borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb': {
        background: theme.palette.neutral[6],
        borderRadius: '10px',
    },
}));

const DivPermissionsTitle = styled(DivRow)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[6],
}));

const DivPermissionActive = styled('div')({
    marginTop: 16,
    marginBottom: 9,
    width: '100%',
    display: 'flex',
    justifyContent: 'space-start',
});

const DivActiveLabel = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[8],
    marginBottom: 4,
}));

const DivActiveDescription = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[6],
    fontWeight: 'normal',

    width: '100%',

    '&>*': {
        display: 'contents',
    },

    '&>.strong': {
        ...theme.typography.h6Roboto,
        color: theme.palette.neutral[6],
    },
}));

const DivPermissionsSeparator = styled('div')(({ theme }) => ({
    borderBottom: `solid 1px ${theme.palette.neutral[3]}`,
    width: '100%',
    marginTop: 21,
    marginRight: 21,
    marginBottom: 30,
}));

const OpacityContainer = styled('div')<{ isEnabled: boolean }>(({ isEnabled }) => ({
    opacity: isEnabled ? 1 : 0.5,
}));

const DivDmsIdModalHeader = styled('div')({
    display: 'flex',
    justifyContent: 'end',
    width: '100%',
});

const DivDmsIdCloseIcon = styled('div')({
    padding: '6px 6px 0px 0px',
    cursor: 'pointer',
});

const DivDmsIdModalContent = styled('div')({
    paddingTop: 10,
    paddingBottom: 48,
    width: 421,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
});

const DivDmsIdModalIcon = styled('div')({
    display: 'flex',
    width: 50,
    height: 50,
    border: '1px solid #FFC626',
    borderRadius: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
});

const SpanDmsIdModalCaption = styled('span')(({ theme }) => ({
    ...theme.typography.h4Inter,
    fontWeight: 'bold',
    color: theme.palette.neutral[8],
}));

const SpanDmsIdModalSubtitle = styled('span')(({ theme }) => ({
    ...theme.typography.h4Inter,
    fontWeight: 'normal',
    fontSize: '12px',
    lineHeight: '14.52px',
    color: theme.palette.neutral[8],
    width: 315,
    marginTop: 15,
    textAlign: 'center',
}));

function getDefaultJobPermissions(job: JobTitle): Partial<UserPermissionDto> | undefined {
    switch (job) {
        case 'AppointmentsExecutive':
            return {
                allowSeeAppointments: true,
                allowEditAppointments: true,
                allowEditWorkshopPlanner: true,
                allowShowJobs: true,
                allowReopenOrders: true,
            };
        case 'Administrator':
            return {
                allowSeeAllTasks: true,
                allowSeeTasks: true,
                allowEditTasks: true,
                allowSendMassiveSendings: true,
            };
        case 'BdcSupervisor':
            return {
                allowSeeAllTasks: true,
                allowSeeTasks: true,
                allowEditTasks: true,
                allowSendMassiveSendings: true,
            };
        case 'BdcAdvisor':
            return {
                allowSeeTasks: true,
                allowSendMassiveSendings: true,
            };
        default:
            return {
                allowShowJobs: true,
                allowReopenOrders: true,
            };
    }
}

const JOB_TITLES_ACTIVATING_USER: JobTitle[] = ['AppointmentsExecutive'];

function useIsEmailValid(email: string) {
    const stateRef = useRef({
        wasChanged: false,
        initialValue: email,
        valid: null as null | boolean,
    });
    const fr = useForceRender();
    if (!stateRef.current.wasChanged) {
        if (email === stateRef.current.initialValue) {
            stateRef.current.valid = null;
        } else {
            stateRef.current.valid = false; // set to false to trigger revalidation below
            stateRef.current.wasChanged = true;
            stateRef.current.initialValue = email;
        }
    }

    if (stateRef.current.valid !== null)
        stateRef.current.valid = emailRegex.test(email.toLowerCase());

    return {
        valid: stateRef.current.valid,
        reset: useCallback(() => {
            stateRef.current.wasChanged = false;
            stateRef.current.initialValue = '';
            stateRef.current.valid = null;
            fr();
        }, [fr]),
    };
}
