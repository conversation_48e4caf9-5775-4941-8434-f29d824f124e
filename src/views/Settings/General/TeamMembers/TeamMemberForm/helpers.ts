import { USER_PERMISSION_DEFAULT } from 'api/account';
import { UserDto } from 'datacontracts/UserDto';
import { UserSchedule } from 'datacontracts/Users';
import { WeekDay } from 'datacontracts/WeekDay.enum';

export function getCleanUser(user?: UserDto): UserDto {
    if (user) {
        if (!user.userSchedule || !user.userSchedule.length) {
            return { ...user, userSchedule: getDefaultSchedule() };
        }

        return { ...user };
    }

    return getEmptyUser();
}

export function getEmptyUser(): UserDto {
    return {
        userId: null,
        userName: '',
        userKey: null,
        displayName: '',
        initials: '',
        jobTitle: '',
        jobTitleOther: '',
        dmsIDNumber: '',
        landlinePhoneNumber: '',
        landlinePhoneExtension: '',
        mobilePhoneNumber: '',
        color: '',
        userSchedule: getDefaultSchedule(),
        isActive: true,
        receptionScheduleEnabled: false,
        appointmentsPerDay: 6,
        permission: USER_PERMISSION_DEFAULT,
        specialtyId: null,
        dmsUserName: '',
    };
}

export function getDefaultSchedule(): UserSchedule[] {
    const userSchedule: UserSchedule[] = [
        {
            weekDay: 0,
            active: false,
            scheduleWindows: [{ open: '08:00:00', close: '19:00:00', isReceptionSchedule: false }],
        },
    ];
    for (let i = 1; i < 7; i++) {
        userSchedule.push({
            weekDay: i,
            active: true,
            scheduleWindows: [
                {
                    open: '08:00:00',
                    close: i !== 6 ? '19:00:00' : '15:00:00',
                    isReceptionSchedule: false,
                },
            ],
        });
    }

    return userSchedule;
}

export const COLOR_OPTIONS: string[][] = [
    ['#B71C1C', '#D32F2F', '#F44336', '#E57373', '#FFCDD2'],
    ['#880E4F', '#C2185B', '#E91E63', '#F06292', '#F8BBD0'],
    ['#4A148C', '#7B1FA2', '#9C27B0', '#BA68C8', '#E1BEE7'],
    ['#311B92', '#512DA8', '#673AB7', '#9575CD', '#D1C4E9'],
    ['#1A237E', '#303F9F', '#3F51B5', '#7986CB', '#C5CAE9'],
    ['#0D47A1', '#1976D2', '#2196F3', '#64B5F6', '#BBDEFB'],
    ['#01579B', '#0288D1', '#03A9F4', '#4FC3F7', '#B3E5FC'],
    ['#006064', '#0097A7', '#00BCD4', '#4DD0E1', '#B2EBF2'],
    ['#004D40', '#00796B', '#009688', '#4DB6AC', '#B2DFDB'],
    ['#194D33', '#388E3C', '#4CAF50', '#81C784', '#C8E6C9'],
    ['#33691E', '#689F38', '#8BC34A', '#AED581', '#DCEDC8'],
    ['#827717', '#AFB42B', '#CDDC39', '#DCE775', '#F0F4C3'],
    ['#F57F17', '#FBC02D', '#FFEB3B', '#FFF176', '#FFF9C4'],
    ['#FF6F00', '#FFA000', '#FFC107', '#FFD54F', '#FFECB3'],
    ['#E65100', '#F57C00', '#FF9800', '#FFB74D', '#FFE0B2'],
    ['#BF360C', '#E64A19', '#FF5722', '#FF8A65', '#FFCCBC'],
    ['#3E2723', '#5D4037', '#795548', '#A1887F', '#D7CCC8'],
    ['#263238', '#455A64', '#607D8B', '#90A4AE', '#CFD8DC'],
    ['#000000', '#525252', '#969696', '#D9D9D9', '#FFFFFF'],
];

export const DAYS_ORDER: WeekDay[] = [
    WeekDay.Monday,
    WeekDay.Tuesday,
    WeekDay.Wednesday,
    WeekDay.Thursday,
    WeekDay.Friday,
    WeekDay.Saturday,
    WeekDay.Sunday,
];
