import { Box } from '@mui/material';
import { SiteForApptField } from 'api/siteForAppointments/_common';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import FieldsList from '../common/FieldsList';
import {
    IconWrapper,
    List,
    STooltip,
    StepContainer,
    StepTitle,
    Title,
    TitleSection,
} from '../common/styledComponents';
import useSiteForAppointments from '../common/useSiteForAppointments';
import EditBrandsPopup from './EditBrandsPopup';

type Step1Props = {
    fields: SiteForApptField[];
};

const Step1 = ({ fields }: Step1Props) => {
    const [isEditBrandsPopupOpen, setIsEditBrandsPopupOpen] = useState(false);
    const { t } = useAppTranslation();

    const { updateItem, updateBrands, brandsValue } = useSiteForAppointments();

    const onFieldsReordered = (fields: SiteForApptField[]) => {
        fields.forEach((field, index) => {
            updateItem({
                fieldId: field.id,
                body: {
                    isHidden: field.isHidden,
                    isMandatory: field.isMandatory,
                    order: index + 2,
                },
            });
        });
    };

    const handleEditBrandsClick = () => {
        setIsEditBrandsPopupOpen(true);
    };

    const handleSaveBrands = (brands: string[]) => {
        updateBrands(brands);
    };

    return (
        <>
            <StepContainer container justifyContent="center">
                <TitleSection
                    sx={(theme) => ({
                        background: '#EFEFEF',
                        paddingLeft: '15px',
                        paddingRight: '25px',
                    })}
                >
                    <StepTitle>{t('siteForAppointments.step1.title')}</StepTitle>
                    <Title>{t('siteForAppointments.step1.description')}</Title>
                    <Box>
                        <STooltip
                            title={t('siteForAppointments.step1.info')}
                            placement="right"
                            arrow
                        >
                            <IconWrapper>
                                <InfoIcon size={14} fill="currentColor" />
                            </IconWrapper>
                        </STooltip>
                    </Box>
                </TitleSection>
                <List>
                    <FieldsList
                        isLoading={false}
                        fields={fields}
                        onFieldsReordered={onFieldsReordered}
                        onEdit={handleEditBrandsClick}
                    />
                </List>
            </StepContainer>
            {isEditBrandsPopupOpen && (
                <EditBrandsPopup
                    onClose={() => setIsEditBrandsPopupOpen(false)}
                    onSave={handleSaveBrands}
                    value={brandsValue}
                />
            )}
        </>
    );
};

export default Step1;
