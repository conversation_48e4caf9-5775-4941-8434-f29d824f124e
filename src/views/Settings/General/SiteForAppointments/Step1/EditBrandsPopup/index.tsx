import { Box, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import MultipleBrandPicker from 'views/Settings/General/AppointmentReasons/AddDetailsForm/MultipleBrandPicker';

export type EditBrandsPopupProps = {
    onSave: (value: string[]) => void;
    onClose: () => void;
    value: string[];
};

const Header = styled('h3')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[7],
    fontSize: 18,
    fontWeight: 700,
    marginTop: 0,
    marginBottom: 0,
}));

const Text = styled('p')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[7],
    fontSize: 14,
    fontWeight: 700,
    whiteSpace: 'pre-line',
    marginTop: 32,
    marginBottom: 8,
}));

const SButton = styled(Button)({
    width: 164,
});

export default function EditBrandsPopup({ onClose, onSave, value }: EditBrandsPopupProps) {
    const [brands, setBrands] = useState<string[]>([]);
    const { t } = useAppTranslation();

    const restore = async () => {
        onSave(brands);
        onClose();
    };
    const handleBrandChange = (value: string[]) => {
        setBrands(value);
    };

    useEffect(() => {
        setBrands(value);
    }, [value]);

    return (
        <Modal open>
            <Box padding={'50px'} width={840}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Header>{t('siteForAppointments.step1.brand')}</Header>
                    <Box display="flex" justifyContent="end" gap={2}>
                        <SButton
                            onClick={onClose}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            label={t('commonLabels.cancel')}
                        />

                        <SButton
                            onClick={restore}
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            label={t('commonLabels.save')}
                        />
                    </Box>
                </Box>
                <Text>{t('siteForAppointments.step1.selectBrandsTitle')}</Text>
                <MultipleBrandPicker
                    value={brands}
                    onChange={handleBrandChange}
                    noTitle
                    fullWidth
                />
            </Box>
        </Modal>
    );
}
