import { CircularProgress, styled } from '@mui/material';
import { SiteForApptField } from 'api/siteForAppointments/_common';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback, useEffect, useState } from 'react';
import {
    DragDropContext,
    Draggable,
    DropResult,
    Droppable,
    ResponderProvided,
} from 'react-beautiful-dnd';
import FieldElement from './FieldElement';

type FieldsListProps = {
    onFieldsReordered: (fields: SiteForApptField[]) => void;
    fields: SiteForApptField[];
    isLoading: boolean;
    onEdit?: () => void;
};

export default function FieldsList({
    isLoading,
    fields: fieldsProp,
    onFieldsReordered,
    onEdit,
}: FieldsListProps) {
    // use additional state to store fields to avoid flickering
    // i.e. when fields are moved around they flicker back to initial state for a moment
    const [fields, setFields] = useState(fieldsProp);
    const { t } = useAppTranslation();
    const toasters = useToasters();
    useEffect(() => {
        setFields(fieldsProp);
    }, [fieldsProp]);

    const onDragEnd = useCallback(
        (result: DropResult, provided: ResponderProvided) => {
            const fromIndex = result.source.index;
            let toIndex = result.destination?.index;

            if (toIndex === undefined) {
                console.warn('[FieldList] toIndex is undefined');
                return;
            }

            if (toIndex <= 1) toIndex = 2;

            const newFields = [...fields];
            const movedField = newFields[fromIndex];
            newFields.splice(fromIndex, 1);
            newFields.splice(toIndex, 0, movedField);
            setFields(newFields);
            onFieldsReordered(newFields.slice(2));
            toasters.success('', t('settings.siteForAppointments.notifications.settingsUpdated'));
        },
        [fields, onFieldsReordered]
    );

    return (
        <Body>
            <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="settings-fields">
                    {(droppable, snapshot) => (
                        <List ref={droppable.innerRef} {...droppable.droppableProps}>
                            {isLoading ? (
                                <CircularProgress style={{ padding: 5 }} size={20} />
                            ) : (
                                fields.map((field, index) => (
                                    <Draggable
                                        index={index}
                                        draggableId={field.id}
                                        key={field.id}
                                        isDragDisabled={field.order === 0 || field.order === 1}
                                    >
                                        {(draggable) => (
                                            <Item
                                                ref={draggable.innerRef}
                                                {...draggable.draggableProps}
                                            >
                                                <FieldElement
                                                    isDragging={
                                                        snapshot.draggingOverWith === field.id
                                                    }
                                                    dragHandleProps={
                                                        draggable.dragHandleProps ?? undefined
                                                    }
                                                    field={field}
                                                    onEdit={onEdit}
                                                />
                                            </Item>
                                        )}
                                    </Draggable>
                                ))
                            )}
                            {droppable.placeholder}
                        </List>
                    )}
                </Droppable>
            </DragDropContext>
        </Body>
    );
}

const Body = styled('div')({
    padding: '0 18px',
});

const List = styled('div')({
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',

    // because "gap" prop doesn't work with react-beautiful-dnd
    paddingBottom: 1,
    '& > *': {
        marginBottom: 10,
    },
});

const Item = styled('div')({
    willChange: 'transform',
});
