import {
    Box,
    FormControlLabel,
    IconButton,
    Switch,
    Typography,
    switchClasses,
} from '@mui/material';
import { styled } from '@mui/styles';
import { FieldType } from 'api/fields';
import { PREDEFINED_SITE_FOR_APPT_FIELDS, SiteForApptField } from 'api/siteForAppointments/_common';
import ALetterIcon from 'common/components/Icons/ALetterIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { MailIcon } from 'common/components/Icons/MailIcon';
import { PhoneIcon } from 'common/components/Icons/PhoneIcon';
import { PtIcon } from 'common/components/Icons/PtIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { TFunction } from 'i18next';
import { memo, useCallback } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import useSiteForAppointments from '../../useSiteForAppointments';

type FieldElementProps = {
    dragHandleProps?: DraggableProvidedDragHandleProps;
    field: SiteForApptField;
    isDragging: boolean;
    onEdit?: () => void;
};

const Root = styled('div')(({ theme }) => ({
    borderRadius: 10,
    border: `1px solid ${theme.palette.neutral[4]}`,
    backgroundColor: theme.palette.neutral[1],
    padding: '9px 10px',
    display: 'flex',
    gap: 10,
    alignItems: 'center',
    position: 'relative',
    height: 44,
}));

const SSwitch = styled(Switch)({
    [`& .${switchClasses.switchBase}`]: {
        [`& + .${switchClasses.track}`]: {
            height: 8,
            width: 22,
            alignSelf: 'center',
        },
        [`&.${switchClasses.checked}`]: {
            color: '#FFFFFF',
            [`&.${switchClasses.thumb}:before`]: {
                color: '#FFFFFF',
            },
            [`& + .${switchClasses.track}`]: {
                opacity: 1,
            },
        },
        [`&.${switchClasses.thumb}:before`]: {
            color: '#FFFFFF',
        },
    },
    [`& .${switchClasses.thumb}`]: {
        width: 12,
        height: 12,
        marginTop: 3.5,
    },
});

const IsMandatory = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 700,
    color: theme.palette.neutral[7],
}));

const Handle = styled('div')({
    borderRadius: 5,
    display: 'flex',
    alignItems: 'center',
});

const Name = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));

const Type = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    display: 'flex',
    alignItems: 'center',
    color: theme.palette.neutral[7],
}));

const ActionBlock = styled('div')({
    marginLeft: 'auto',
    display: 'flex',
    flexWrap: 'nowrap',
});

export default function FieldElement({
    dragHandleProps,
    field,
    isDragging,
    onEdit,
}: FieldElementProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const { updateItem } = useSiteForAppointments();

    const fieldName = t(`settings.siteForAppointments.fields.${field.name.replaceAll('.', '_')}`);

    const onHideButtonClick = useCallback(() => {
        updateItem({
            fieldId: field.id,
            body: { isHidden: !field.isHidden, isMandatory: field.isMandatory, order: field.order },
        });
        toasters.success('', t('settings.siteForAppointments.notifications.settingsUpdated'));
    }, [field, updateItem]);

    const onMandatoryToggle = useCallback(() => {
        updateItem({
            fieldId: field.id,
            body: { isHidden: field.isHidden, isMandatory: !field.isMandatory, order: field.order },
        });
        toasters.success('', t('settings.siteForAppointments.notifications.settingsUpdated'));
    }, [field, updateItem]);

    return (
        <Root>
            {!field.isRequired || field.name === 'customer.mobile' ? (
                <Handle {...(dragHandleProps ?? {})}>
                    <DragAndDropIcon size={25} fill={Colors.Neutral7} />
                </Handle>
            ) : (
                <Box style={{ width: 24 }} />
            )}
            <Name>{fieldName}</Name>
            <Type>
                |&nbsp;
                <FieldTypeIcon name={field.name} type={field.type} />
                &nbsp; {getFieldTypeName(t, field)}
            </Type>
            {!field.isRequired && (
                <Type>
                    |&nbsp;&nbsp;&nbsp;
                    <FormControlLabel
                        checked={field.isMandatory}
                        onChange={onMandatoryToggle}
                        control={<SSwitch color="primary" />}
                        label={
                            <IsMandatory>
                                {t('siteForAppointments.common.mandatoryField')}
                            </IsMandatory>
                        }
                    />
                </Type>
            )}
            <ActionBlock>
                {field.isHidden ? (
                    <IconButton
                        size="small"
                        onClick={onHideButtonClick}
                        disabled={field.isRequired}
                    >
                        <HideIcon fill={field.isRequired ? Colors.Transparent : Colors.Neutral7} />
                    </IconButton>
                ) : (
                    <IconButton
                        size="small"
                        onClick={onHideButtonClick}
                        disabled={field.isRequired}
                    >
                        <ShowIcon fill={field.isRequired ? Colors.Transparent : Colors.CM1} />
                    </IconButton>
                )}
                {field.name === 'vehicle.brand' && (
                    <IconButton size="small" onClick={onEdit}>
                        <EditIcon fill={Colors.Neutral7} />
                    </IconButton>
                )}
            </ActionBlock>
        </Root>
    );
}

// extended field type with some custom types for special predefined fields
type FieldTypeX = 'FreeText' | 'Email' | 'PhoneNumber' | 'Dropdown';

const PREDEFINED_FIELD_EXTENDED_TYPES: Record<
    string,
    Exclude<FieldTypeX, 'Predefined' | 'PredefinedAdditionalPosition'>
> = {
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_BRAND]: 'Dropdown',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_MODEL]: 'Dropdown',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_YEAR]: 'Dropdown',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_PLATES]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_MILEAGE]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_VIN]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.VEHICLE_COLOR]: 'FreeText',

    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_NAME]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_LAST_NAME]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_EMAIL]: 'Email',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_MOBILE]: 'PhoneNumber',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_BUSINESS_NAME]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_TAX_ID]: 'FreeText',
    [PREDEFINED_SITE_FOR_APPT_FIELDS.CUSTOMER_NOTES]: 'FreeText',
};

const DROPDOWN_ICON = <PtIcon fill="currentColor" />;
const FREETEXT_ICON = <ALetterIcon />;
const EMAIL_ICON = <MailIcon fill="currentColor" />;
const PHONENUMBER_ICON = <PhoneIcon fill="currentColor" />;

const FIELD_ICONS: Record<
    Exclude<FieldTypeX, 'Predefined' | 'PredefinedAdditionalPosition'>,
    JSX.Element
> = {
    FreeText: FREETEXT_ICON,
    Email: EMAIL_ICON,
    PhoneNumber: PHONENUMBER_ICON,
    Dropdown: DROPDOWN_ICON,
};

function getFieldTypeName(t: TFunction, field: SiteForApptField): string {
    const xType = PREDEFINED_FIELD_EXTENDED_TYPES[field.name] ?? 'FreeText';
    return t(`siteForAppointments.types.${xType}`);
}

export const FieldTypeIcon = memo(
    ({ name, className }: { type: FieldType; name?: string; className?: string }) => {
        if (!name) return null;
        const xType = PREDEFINED_FIELD_EXTENDED_TYPES[name];
        return FIELD_ICONS[xType] ?? null;
    }
);
if (import.meta.env.NODE_ENV === 'development') FieldTypeIcon.displayName = 'FieldTypeIcon';
