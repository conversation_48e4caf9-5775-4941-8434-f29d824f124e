import {
    Grid,
    Switch,
    Tooltip,
    TooltipProps,
    Typography,
    styled,
    switchClasses,
    tooltipClasses,
} from '@mui/material';

export const StepContainer = styled(Grid)(({ theme }) => ({
    border: '1px solid rgba(201, 205, 211, 0.5)',
    borderRadius: '12px',
    overflow: 'hidden',
    marginBottom: 28,
}));

export const Header = styled(Grid)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    gap: 1,
    paddingLeft: 15,
    paddingBottom: 12,
    borderBottom: '1px solid rgba(201, 205, 211, 0.5)',
    width: '100%',
    background: theme.palette.neutral[3],
}));

export const TitleSection = styled(Grid)({
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    height: 38,
    gap: 5,
});

export const StepTitle = styled(Typography)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    fontWeight: 700,
    fontSize: 14,
    color: theme.palette.primary.main,
}));

export const Title = styled(Typography)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    fontWeight: 700,
    fontSize: 14,
    color: theme.palette.neutral[8],
}));

export const IconWrapper = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[5],
    color: 'white',
    borderRadius: 100,
    height: 14,
    width: '14px !important',
    display: 'inline-block',
    '&:hover': {
        backgroundColor: theme.palette.primary.main,
    },
}));

export const ScheduleInfo = styled(Typography)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    fontSize: 12,
    fontWeight: 400,
    color: '#5C6477',
    lineHeight: 'normal',
}));

export const ScheduleInfoSection = styled('div')({
    display: 'flex',
    gap: 4,
});

export const List = styled('div')({
    width: '100%',
    paddingTop: 20.2,
    paddingBottom: 20.2,
});

export const SSwitch = styled(Switch)({
    [`& .${switchClasses.switchBase}`]: {
        [`&.${switchClasses.checked}`]: {
            color: '#FFFFFF',
            [`&.${switchClasses.thumb}:before`]: {
                color: '#FFFFFF',
            },
            [`& + .${switchClasses.track}`]: {
                opacity: 1,
            },
        },
        [`&.${switchClasses.thumb}:before`]: {
            color: '#FFFFFF',
        },
    },
});

export const ConfigLabel = styled(Typography)(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontSize: 14,
    color: theme.palette.neutral[7],
}));

export const STooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        ...theme.typography.h6Roboto,
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
        filter: `drop-shadow(0px 0px 1px ${theme.palette.neutral[7]})`,
        position: 'relative',
        borderRadius: 5,
        // marginBottom: '6px !important',
        boxShadow: '0px 4px 4px 0px rgba(166, 166, 166, 0.25)',
        textWrap: 'nowrap',
        maxWidth: '100% !important',
        // maxWidth: 'none !important',
        // maxWidth: '600px !important',
    },
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
    },
}));
