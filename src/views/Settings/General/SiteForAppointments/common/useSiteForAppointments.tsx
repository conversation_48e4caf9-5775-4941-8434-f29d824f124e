import { useMutation, useQuery } from '@tanstack/react-query';
import SiteForAppointmentsApi, {
    SiteForAppointmentFieldUpdateDTO,
    SiteForAppointmentsGeneralSettings,
    SiteForAppointmentsStepThree,
} from 'api/siteForAppointments';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo } from 'react';

export const SITE_FOR_APPOINTMENTS_QUERY_KEY = ['settings', 'siteForAppointments'];

const useSiteForAppointments = () => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const {
        data: settings,
        isLoading,
        refetch,
    } = useQuery(
        SITE_FOR_APPOINTMENTS_QUERY_KEY,
        () => SiteForAppointmentsApi.getSiteForAppointmentsSettings(),
        {
            cacheTime: Infinity,
            refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
            refetchInterval: 120000,
        }
    );

    const stepOneFields = useMemo(
        () =>
            settings?.stepOne.sort((a, b) => {
                if (a.order < b.order) {
                    return -1;
                }
                if (a.order > b.order) {
                    return 1;
                }
                return 0;
            }) ?? [],
        [settings]
    );
    const stepTwoFields = useMemo(
        () =>
            settings?.stepTwo.sort((a, b) => {
                if (a.order < b.order) {
                    return -1;
                }
                if (a.order > b.order) {
                    return 1;
                }
                return 0;
            }) ?? [],
        [settings]
    );
    const stepThree = useMemo(
        () =>
            settings?.stepThree ?? {
                showCancellationPolicies: false,
                cancellationPolicies: '',
                showPrivacyNotice: false,
            },
        [settings]
    );

    const general = useMemo(() => {
        return (
            settings?.general?.minimumTimeSchedule ??
            ({
                mininumTime: 6,
                minimumTimeToScheduleEnabled: false,
                intervalTimeToSchedule: 'Hour',
            } as SiteForAppointmentsGeneralSettings)
        );
    }, [settings, settings?.general]);

    const { data: brands, refetch: refetchBrands } = useQuery(
        [SITE_FOR_APPOINTMENTS_QUERY_KEY, 'brands'],
        () => SiteForAppointmentsApi.getSiteForAppointmentsBrands(),
        {
            cacheTime: Infinity,
            refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
            refetchInterval: 120000,
        }
    );

    const brandsValue = useMemo(() => brands?.map((b) => b.brand) ?? [], [brands]);

    const { mutate: updateItem } = useMutation(
        (field: { fieldId: string; body: SiteForAppointmentFieldUpdateDTO }) => {
            return SiteForAppointmentsApi.updateSiteForAppointmentsField(field.fieldId, {
                isHidden: field.body.isHidden,
                isMandatory: field.body.isMandatory,
                order: field.body.order,
            });
        },
        {
            onSuccess: () => {
                refetch();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('settings.siteForAppointments.notifications.somethingWentWrong')
                );
            },
        }
    );

    const { mutate: updateStepThree } = useMutation(
        (body: SiteForAppointmentsStepThree) => {
            return SiteForAppointmentsApi.updateSiteForAppointmentsSettings(body);
        },
        {
            onSuccess: () => {
                refetch();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('settings.siteForAppointments.notifications.somethingWentWrong')
                );
            },
        }
    );

    const { mutate: updateGeneral } = useMutation(
        (body: SiteForAppointmentsGeneralSettings) => {
            return SiteForAppointmentsApi.updateSiteForAppointmentsSettings(body);
        },
        {
            onSuccess: () => {
                refetch();
                toasters.success(
                    t('settings.siteForAppointments.notifications.settingsUpdatedSuccessfully'),
                    t('settings.siteForAppointments.notifications.settingsUpdated')
                );
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('settings.siteForAppointments.notifications.somethingWentWrong')
                );
            },
        }
    );

    const { mutate: updateBrands } = useMutation(
        (brands: string[]) => {
            return SiteForAppointmentsApi.updateBrands({ brands });
        },
        {
            onSuccess: () => {
                refetchBrands();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('settings.siteForAppointments.notifications.somethingWentWrong')
                );
            },
        }
    );

    return {
        updateItem,
        updateStepThree,
        brandsValue,
        updateBrands,
        stepOneFields,
        stepTwoFields,
        stepThree,
        isLoading,
        refetch,
        general,
        updateGeneral,
    };
};

export default useSiteForAppointments;
