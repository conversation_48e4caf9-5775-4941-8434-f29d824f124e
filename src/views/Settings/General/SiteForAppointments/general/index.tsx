import { Box, FormControlLabel, styled, Typography } from '@mui/material';
import { SiteForAppointmentsGeneralSettings } from 'api/siteForAppointments';
import { CalendarCloseIcon } from 'common/components/Icons/CalendarCloseIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo, useState } from 'react';
import {
    ConfigLabel,
    List,
    SSwitch,
    StepContainer,
    StepTitle,
    TitleSection,
} from '../common/styledComponents';
import useSiteForAppointments from '../common/useSiteForAppointments';

const Description = styled(Typography)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontSize: 14,
    fontWeight: 400,
    color: theme.palette.neutral[8],
    marginLeft: 48,
}));

const ApptSiteGeneralSettings = ({ general }: { general: SiteForAppointmentsGeneralSettings }) => {
    const [selectedHours, setSelectedHours] = useState(general.mininumTime);

    const { t } = useAppTranslation();
    const toasters = useToasters();
    const { updateGeneral } = useSiteForAppointments();

    const timeIntervalOptions = useMemo(
        () => [
            {
                label:
                    selectedHours === 1
                        ? t('siteForAppointments.general.hour')
                        : t('siteForAppointments.general.hours'),
                value: 'Hour',
            },
            {
                label:
                    selectedHours === 1
                        ? t('siteForAppointments.general.day')
                        : t('siteForAppointments.general.days'),
                value: 'Day',
            },
            {
                label:
                    selectedHours === 1
                        ? t('siteForAppointments.general.week')
                        : t('siteForAppointments.general.weeks'),
                value: 'Week',
            },
        ],
        [selectedHours]
    );

    const selectedTimeInterval = useMemo(() => {
        return timeIntervalOptions.find(({ value }) => value === general.intervalTimeToSchedule)!;
    }, [general, general.minimumTimeToScheduleEnabled, timeIntervalOptions]);

    const handleDropdownChange = (event: string) => {
        updateGeneral({
            ...general,
            intervalTimeToSchedule: event as 'Hour' | 'Day' | 'Week',
        });
    };

    const handleLeadTimeToggle = (checked: boolean) => {
        updateGeneral({
            ...general,
            minimumTimeToScheduleEnabled: checked,
        });
    };

    const handleConfirmSelectedHours = () => {
        if (selectedHours < 1) {
            setSelectedHours(general.mininumTime);
            toasters.danger('', t('settings.siteForAppointments.notifications.invalidValue'));
            return;
        }
        if (selectedHours > 999) {
            setSelectedHours(general.mininumTime);
            toasters.danger('', t('settings.siteForAppointments.notifications.invalidValue'));
            return;
        }
        updateGeneral({
            ...general,
            mininumTime: selectedHours,
            intervalTimeToSchedule: selectedTimeInterval.value as 'Hour' | 'Day' | 'Week',
        });
    };

    return (
        <>
            <StepContainer container justifyContent="center" sx={{ marginTop: '16.63px' }}>
                <TitleSection
                    sx={{
                        background: '#EFEFEF',
                        paddingLeft: '15px',
                        paddingRight: '25px',
                    }}
                >
                    <StepTitle style={{ color: '#4A4D51' }}>
                        {t('siteForAppointments.general.title')}
                    </StepTitle>
                </TitleSection>
                <List style={{ marginTop: -8 }}>
                    <Box
                        style={{
                            marginLeft: 25,
                        }}
                    >
                        <FormControlLabel
                            checked={general.minimumTimeToScheduleEnabled}
                            onChange={(_e, checked) => {
                                handleLeadTimeToggle(checked);
                            }}
                            control={<SSwitch color="primary" />}
                            label={
                                <Box
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 4,
                                    }}
                                >
                                    <CalendarCloseIcon fill={'#6A6E72'} />
                                    <ConfigLabel style={{ color: '#4A4D51' }}>
                                        {t('siteForAppointments.general.minimumTime')}
                                    </ConfigLabel>
                                </Box>
                            }
                        />
                        <Description>
                            {t('siteForAppointments.general.selectLeadTime')}
                            <span style={{ color: '#0069FF' }}>{' *'}</span>
                        </Description>
                        <Box
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 4,
                                marginLeft: 48,
                                marginTop: 8,
                            }}
                        >
                            <Box sx={{ width: 75 }}>
                                <TextField
                                    type="number"
                                    value={`${selectedHours}`}
                                    InputProps={{
                                        inputProps: { min: 1, max: 999 },
                                    }}
                                    onChange={(event) => {
                                        setSelectedHours(+event.target.value);
                                    }}
                                    disabled={!general.minimumTimeToScheduleEnabled}
                                    onBlur={handleConfirmSelectedHours}
                                />
                            </Box>
                            <Box sx={{ width: 162 }}>
                                <Dropdown
                                    name={'timeInterval'}
                                    cmosVariant="default"
                                    value={selectedTimeInterval}
                                    onChange={(event) => event && handleDropdownChange(event.value)}
                                    options={timeIntervalOptions}
                                    size="small"
                                    disabled={!general.minimumTimeToScheduleEnabled}
                                    styles={{
                                        indicatorsContainer: {
                                            color: '#4A4D51',
                                        },
                                    }}
                                    showDisabledStyles
                                />
                            </Box>
                        </Box>
                    </Box>
                </List>
            </StepContainer>
        </>
    );
};

export default ApptSiteGeneralSettings;
