import { Box, FormControlLabel } from '@mui/material';
import { Editor } from '@tiptap/core';
import { SiteForAppointmentsStepThree } from 'api/siteForAppointments';
import { Button } from 'common/components/Button';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { InspectionIcon } from 'common/components/Icons/InspectionIcon';
import RichTextEditor from 'common/components/Inputs/RichTextEditor';
import { HtmlChangeSource } from 'common/components/Inputs/RichTextEditor/RichTextEditor';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useEffect, useState } from 'react';
import {
    ConfigLabel,
    IconWrapper,
    List,
    SSwitch,
    STooltip,
    StepContainer,
    StepTitle,
    Title,
    TitleSection,
} from '../common/styledComponents';
import useSiteForAppointments from '../common/useSiteForAppointments';
import RestoreCancellationPoliciesPopup from './RestoreCancellationPoliciesPopup';

type Step3Props = {
    stepThree: SiteForAppointmentsStepThree;
};

const Step3 = ({ stepThree }: Step3Props) => {
    const { showCancellationPolicies, cancellationPolicies, showPrivacyNotice } = stepThree;
    const [html, setHtml] = useState(cancellationPolicies);
    const [textLength, setTextLength] = useState<number>(0);
    const [isRestorePopupOpen, setRestorePopupOpen] = useState(false);
    const { t } = useAppTranslation();
    const { updateStepThree } = useSiteForAppointments();
    const toasters = useToasters();

    const maxTextLength: number = 2000;
    const onHtmlChange = (html: string, source: HtmlChangeSource, editor: Editor) => {
        setHtml(html);
        const text = editor.getText().replace(/\s{5,}/, '    ');
        setTextLength(text.length);
        if (source === 'sourceChanged') {
            updateStepThree({
                showCancellationPolicies,
                cancellationPolicies: html,
                showPrivacyNotice,
            });
        }
    };

    const saveCancellationPolicies = (restore?: boolean) => {
        updateStepThree({
            showCancellationPolicies,
            cancellationPolicies: restore ? '<p></p>' : html,
            showPrivacyNotice,
        });
        toasters.success(t('toasters.settingSuccessfullyUpdated'), t('toasters.settingUpdated'));
    };

    const restoreCancellationPolicies = () => {
        setHtml('<p></p>');
        setTextLength(0);
        saveCancellationPolicies(true);
    };

    const handleShowCancellationPoliciesChange = (checked: boolean) => {
        updateStepThree({
            showCancellationPolicies: checked,
            cancellationPolicies: cancellationPolicies,
            showPrivacyNotice,
        });
    };

    const handleShowPrivacyNoticeChange = (checked: boolean) => {
        updateStepThree({
            showCancellationPolicies,
            cancellationPolicies: cancellationPolicies,
            showPrivacyNotice: checked,
        });
    };

    useEffect(() => {
        setHtml(cancellationPolicies);
    }, [cancellationPolicies]);

    return (
        <>
            <StepContainer container justifyContent="center">
                <TitleSection
                    sx={(theme) => ({
                        background: theme.palette.neutral[3],
                        paddingLeft: '15px',
                        paddingRight: '25px',
                    })}
                >
                    <StepTitle>{t('siteForAppointments.step3.title')}</StepTitle>
                    <Title>{t('siteForAppointments.step3.description')}</Title>
                    <STooltip title={t('siteForAppointments.step3.info')} placement="right" arrow>
                        <IconWrapper>
                            <InfoIcon size={14} fill="currentColor" />
                        </IconWrapper>
                    </STooltip>
                </TitleSection>
                <List>
                    <Box
                        style={{
                            marginLeft: 25,
                        }}
                    >
                        <FormControlLabel
                            checked={showCancellationPolicies}
                            onChange={(_e, checked) => {
                                handleShowCancellationPoliciesChange(checked);
                            }}
                            control={<SSwitch color="primary" />}
                            label={
                                <Box style={{ display: 'flex', alignItems: 'center' }}>
                                    <InspectionIcon size={IconSize.L} fill={Colors.Neutral7} />
                                    <ConfigLabel>
                                        {t('siteForAppointments.step3.showCancellationPolicy')}
                                    </ConfigLabel>
                                </Box>
                            }
                        />
                    </Box>
                    <Box style={{ width: '92%', marginLeft: 80 }}>
                        <RichTextEditor
                            html={html}
                            onHtmlChange={onHtmlChange}
                            readOnly={!showCancellationPolicies}
                            changeCallbackBehavior="onUpdate"
                        />
                        <span style={{ color: textLength <= maxTextLength ? '' : 'red' }}>
                            {textLength + '/' + maxTextLength}
                        </span>

                        <Box display="flex" justifyContent="end" marginTop={2} gap={0.75}>
                            <Button
                                color={Colors.CM1}
                                label={t('siteForAppointments.step3.clear.clear')}
                                cmosVariant={'stroke'}
                                onClick={() => setRestorePopupOpen(true)}
                                customStyles={{ width: 160 }}
                                disabled={!showCancellationPolicies}
                            />
                            <Button
                                color={Colors.Success}
                                label={t('commonLabels.save')}
                                cmosVariant={'filled'}
                                onClick={() => saveCancellationPolicies()}
                                customStyles={{ width: 160 }}
                                disabled={!showCancellationPolicies || textLength > maxTextLength}
                            />
                        </Box>
                    </Box>
                    <Box
                        style={{
                            marginLeft: 25,
                        }}
                    >
                        <FormControlLabel
                            checked={showPrivacyNotice}
                            onChange={(_e, checked) => {
                                handleShowPrivacyNoticeChange(checked);
                            }}
                            control={<SSwitch color="primary" />}
                            label={
                                <Box style={{ display: 'flex', alignItems: 'center' }}>
                                    <InspectionIcon size={IconSize.L} fill={Colors.Neutral7} />
                                    <ConfigLabel>
                                        {t('siteForAppointments.step3.showPrivacyNotice')}
                                    </ConfigLabel>
                                </Box>
                            }
                        />
                    </Box>
                    <Box
                        style={{
                            marginTop: 10,
                            marginLeft: 80,
                            paddingRight: 36,
                        }}
                    >
                        <Box
                            style={{
                                borderRadius: 5,
                                padding: '15px 24px',
                                backgroundColor: '#E6EEFE',
                            }}
                        >
                            {t('siteForAppointments.step3.privacyNoticeText')}
                        </Box>
                    </Box>
                </List>
            </StepContainer>
            {isRestorePopupOpen && (
                <RestoreCancellationPoliciesPopup
                    onRestore={restoreCancellationPolicies}
                    onClose={() => setRestorePopupOpen(false)}
                />
            )}
        </>
    );
};

export default Step3;
