import { Box, styled } from '@mui/material';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';

export type RestoreCancellationPoliciesPopupProps = {
    onRestore: () => void;
    onClose: () => void;
};

const Header = styled('h3')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[8],
    fontSize: 14,
    fontWeight: 700,
    marginTop: 0,
}));

const Text = styled('p')({
    whiteSpace: 'pre-line',
});

const SButton = styled(Button)({
    width: 160,
});

export default function RestoreCancellationPoliciesPopup({
    onClose,
    onRestore,
}: RestoreCancellationPoliciesPopupProps) {
    const { t } = useAppTranslation();

    const restore = async () => {
        onRestore();
        onClose();
    };

    return (
        <Modal open>
            <Box padding={4} width={700}>
                <Header>{t('siteForAppointments.step3.clear.title')}</Header>
                <Text>{t('siteForAppointments.step3.clear.text')}</Text>

                <Box display="flex" justifyContent="end" gap={2} marginTop={4}>
                    <SButton
                        onClick={onClose}
                        cmosVariant={'filled'}
                        color={Colors.Neutral3}
                        label={t('commonLabels.cancel')}
                    />

                    <SButton
                        onClick={restore}
                        cmosVariant={'filled'}
                        color={Colors.CM1}
                        label={t('siteForAppointments.step3.clear.clear')}
                    />
                </Box>
            </Box>
        </Modal>
    );
}
