import { Popover, styled } from '@mui/material';
import { useMemo } from 'react';
import teamMemberScheduleImage from '../../../../../../assets/images/team_member_schedule.png';

type ScheduleImagePopupProps = {
    isOpen: boolean;
    anchorEl: HTMLElement | null;
};

const SPopover = styled(Popover)({
    pointerEvents: 'none',
});

const Image = styled('img')({
    width: 685,
    height: 543,
    padding: 10,
});

const ScheduleImagePopup = ({ isOpen, anchorEl }: ScheduleImagePopupProps) => {
    const imageUrl = useMemo(() => teamMemberScheduleImage, [teamMemberScheduleImage]);
    return (
        <SPopover
            open={isOpen}
            id="mouse-over-popover"
            anchorEl={anchorEl}
            transformOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
            }}
            disableRestoreFocus
        >
            <Image src={imageUrl} alt="" />
        </SPopover>
    );
};

export default ScheduleImagePopup;
