import { Box } from '@mui/material';
import { SiteForApptField } from 'api/siteForAppointments/_common';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useState } from 'react';
import FieldsList from '../common/FieldsList';
import {
    Header,
    IconWrapper,
    List,
    STooltip,
    ScheduleInfo,
    ScheduleInfoSection,
    StepContainer,
    StepTitle,
    Title,
    TitleSection,
} from '../common/styledComponents';
import useSiteForAppointments from '../common/useSiteForAppointments';
import ScheduleImagePopup from './ScheduleImagePopup';

type Step2Props = {
    fields: SiteForApptField[];
};

const Step2 = ({ fields }: Step2Props) => {
    const { t } = useAppTranslation();
    const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
    const [isScheduleImagePopupOpen, setIsScheduleImagePopupOpen] = useState(false);

    const showScheduleImagePopup = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
        setAnchorEl(event.currentTarget);
        setIsScheduleImagePopupOpen(true);
    };

    const { updateItem } = useSiteForAppointments();

    const onFieldsReordered = (fields: SiteForApptField[]) => {
        fields.forEach((field, index) => {
            updateItem({
                fieldId: field.id,
                body: {
                    isHidden: field.isHidden,
                    isMandatory: field.isMandatory,
                    order: index + 2,
                },
            });
        });
    };

    return (
        <>
            <StepContainer container justifyContent="center">
                <Header>
                    <TitleSection>
                        <StepTitle>{t('siteForAppointments.step2.title')}</StepTitle>
                        <Title>{t('siteForAppointments.step2.description')}</Title>
                        <Box style={{ position: 'relative' }}>
                            <STooltip
                                title={t('siteForAppointments.step2.info')}
                                placement="right"
                                arrow
                            >
                                <IconWrapper>
                                    <InfoIcon size={14} fill="currentColor" />
                                </IconWrapper>
                            </STooltip>
                        </Box>
                    </TitleSection>
                    <ScheduleInfoSection>
                        <ScheduleInfo>
                            {t('siteForAppointments.step2.schedulesInfo')}
                            &nbsp;
                            <IconWrapper
                                style={{ position: 'absolute' }}
                                onMouseOver={(e: React.MouseEvent<HTMLDivElement>) =>
                                    showScheduleImagePopup(e)
                                }
                                onMouseOut={() => setIsScheduleImagePopupOpen(false)}
                            >
                                <InfoIcon size={14} fill="currentColor" />
                            </IconWrapper>
                            <ScheduleImagePopup
                                isOpen={isScheduleImagePopupOpen}
                                anchorEl={anchorEl}
                            />
                        </ScheduleInfo>
                    </ScheduleInfoSection>
                </Header>
                <List>
                    <FieldsList
                        isLoading={false}
                        fields={fields}
                        onFieldsReordered={onFieldsReordered}
                    />
                </List>
            </StepContainer>
        </>
    );
};

export default Step2;
