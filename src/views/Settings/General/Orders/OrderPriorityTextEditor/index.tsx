import { Box, styled } from '@mui/material';
import { PriorityTextValues } from 'api/settings/OrdersSettings';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useEffect, useState } from 'react';
import theme from 'theme';
import { BooleanSettingControl, TextSettingControl } from 'views/Settings/common';

type Settings = {
    enabled: boolean;
    priorityTextValues: PriorityTextValues | null;
};

type OrderPriorityTextEditorProps = {
    labelText: string;
    settings: Settings;
    onSettingsChanged: (settings: Settings) => void;
};

const OrderPriorityTextEditor = ({
    labelText,
    settings,
    onSettingsChanged,
}: OrderPriorityTextEditorProps) => {
    const { t } = useAppTranslation();
    const [state, setState] = useState<Settings>(settings);

    useEffect(() => {
        setState({
            ...settings,
            priorityTextValues: {
                red: settings.priorityTextValues?.red || '',
                yellow: settings.priorityTextValues?.yellow || '',
                green: settings.priorityTextValues?.green || '',
            },
        });
    }, [settings]);

    const handleSave = useCallback(
        (key: keyof PriorityTextValues, value: string): void => {
            const updatedTexts: PriorityTextValues = {
                red: state.priorityTextValues?.red || '',
                yellow: state.priorityTextValues?.yellow || '',
                green: state.priorityTextValues?.green || '',
                [key]: value,
            };
            const updatedState = { ...state, priorityTextValues: updatedTexts };
            setState(updatedState);
            onSettingsChanged(updatedState);
        },
        [state, onSettingsChanged]
    );

    return (
        <BooleanSettingControl
            label={labelText}
            value={state.enabled}
            hasHint={false}
            onChange={(enabled) => {
                const updatedState = { ...state, enabled };
                setState(updatedState);
                onSettingsChanged(updatedState);
            }}
            slotProps={{
                layout: {
                    expanded: state.enabled,
                    childrenBelow: state.enabled && (
                        <EditorContainer>
                            <PrioritySectionHeader>
                                {t('settings.orders.orderPriorityText.prioritySectionHeader')}
                            </PrioritySectionHeader>
                            <PrioritySectionContainer>
                                <TextSettingControl
                                    name="priority-red"
                                    alignItems="center"
                                    label={t('settings.orders.orderPriorityText.redPriorityLabel')}
                                    placeholder={t(
                                        'settings.orders.orderPriorityText.redPriorityDefaultText'
                                    )}
                                    value={state.priorityTextValues?.red || ''}
                                    onSave={async (v) => handleSave('red', v)}
                                />

                                <TextSettingControl
                                    name="priority-yellow"
                                    alignItems="center"
                                    label={t(
                                        'settings.orders.orderPriorityText.yellowPriorityLabel'
                                    )}
                                    placeholder={t(
                                        'settings.orders.orderPriorityText.yellowPriorityDefaultText'
                                    )}
                                    value={state.priorityTextValues?.yellow || ''}
                                    onSave={async (v) => handleSave('yellow', v)}
                                />

                                <TextSettingControl
                                    name="priority-green"
                                    alignItems="center"
                                    label={t(
                                        'settings.orders.orderPriorityText.greenPriorityLabel'
                                    )}
                                    placeholder={t(
                                        'settings.orders.orderPriorityText.greenPriorityDefaultText'
                                    )}
                                    value={state.priorityTextValues?.green || ''}
                                    onSave={async (v) => handleSave('green', v)}
                                />
                            </PrioritySectionContainer>
                        </EditorContainer>
                    ),
                },
            }}
        />
    );
};

const EditorContainer = styled(Box)({
    display: 'flex',
    alignItems: 'flex-start',
    gap: theme.spacing(20),
    marginTop: theme.spacing(2),
});

const PrioritySectionHeader = styled('div')({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    marginTop: theme.spacing(1.5),
});

const PrioritySectionContainer = styled(Box)({
    flex: 1,
    display: 'grid',
    gap: theme.spacing(3),
});

export default OrderPriorityTextEditor;
