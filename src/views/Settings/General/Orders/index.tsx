import { QueryKey, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import OrdersApi from 'api/orders';
import {
    ConsumerFormType,
    DiscountType,
    OrdersSettings,
    OrdersSettingsApi,
} from 'api/settings/OrdersSettings';
import { $Icon } from 'common/components/Icons/$Icon';
import { NumbersIcon } from 'common/components/Icons/NumbersIcon';
import { TrafficLightIcon } from 'common/components/Icons/TrafficLightIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { ENTERPRISE_ROUTES } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import debounce from 'lodash/debounce';
import { useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch } from 'store';
import { loadGlobalSettingsThunk } from 'store/slices/globalSettingsSlice';
import SimpleErrorDisplay from 'utils/errorsHandling/SimpleErrorDisplay';
import PageContent from 'views/Components/Page';
import {
    BooleanSettingControl,
    OptionalNumberSettingControl,
    RadioButtonArrayControl,
    SettingsSection,
} from 'views/Settings/common';
import AutomaticallyCloseOrders from './AutomaticallyCloseOrders';
import OrderPriorityTextEditor from './OrderPriorityTextEditor';

const defaultOrderSettings: OrdersSettings = {
    liveGalleryImagePath: '',
    showDefaultItems: false,
    includeEstimateInDigitalOrder: false,
    defaultFormType: 'BySystem',
    showDetails: false,
    approveOnlyEstimated: false,
    showServiceAdvisorInfo: false,
    showCutRepairOrderNumber: false,
    hideMakeLogos: false,
    enableHoursOfUse: false,
    taxPercentage: null,
    costPerHour: null,
    showOriginalPhotos: false,
    enableFacebookButton: false,
    showVehicleMakeLogo: false,
    autoCloseOrders: {
        enabled: false,
        phaseIds: [],
        timeout: '',
    },
    discountType: 'Currency',
    orderConfig: {
        enableOrderPriorityText: false,
        orderPriorityTextValues: null,
        timeInterval: 15,
    },
};

export default function Orders() {
    const { t } = useAppTranslation();
    const match = useLocation();
    const isEnterprise = match.pathname.startsWith(ENTERPRISE_ROUTES.BASE);
    const toasters = useToasters();

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.orders')}`);

    const queryKey: QueryKey = ['settings', 'orders'];
    const queryClient = useQueryClient();
    const { data, error, isLoading, refetch } = useQuery(queryKey, {
        queryFn: OrdersSettingsApi.get,
        cacheTime: 0,
        staleTime: 0,
        enabled: !isEnterprise,
    });

    const { data: timeInterval } = useQuery({
        queryFn: OrdersApi.getTimeInterval,
        cacheTime: 0,
        staleTime: 0,
    });

    const inspectionForm = useMemo(() => data ?? defaultOrderSettings, [data]);

    const [selectedTimeInterval, setSelectedTimeInterval] = useState<OptionData>();

    const timeIntervalOptions = useMemo(
        () => [
            { label: '5 min', value: 5 },
            { label: '10 min', value: 10 },
            { label: '15 min', value: 15 },
            { label: '20 min', value: 20 },
            { label: '30 min', value: 30 },
            { label: '45 min', value: 45 },
            { label: '60 min', value: 60 },
        ],
        []
    );

    const defaultTimeInterval = 15;

    const defaultTimeIntervalOption = useMemo(
        () => timeIntervalOptions.find((opt) => opt.value === defaultTimeInterval),
        [timeIntervalOptions, defaultTimeInterval]
    );

    useEffect(() => {
        const value = timeIntervalOptions.find((opt) => opt.value === timeInterval);
        setSelectedTimeInterval(value);
    }, [timeInterval, timeIntervalOptions]);

    const dispatch = useAppDispatch();
    const updateSettingsMutation = useMutation(
        async (settings: OrdersSettings) => {
            return await OrdersSettingsApi.update(settings);
        },
        {
            onMutate(settings) {
                queryClient.setQueryData<OrdersSettings>(queryKey, settings);
            },
            onSuccess() {
                setTimeout(() => {
                    dispatch(loadGlobalSettingsThunk());
                }, 20);
            },
        }
    );

    const updateSettingsDebounced = useMemo(
        () => debounce((settings: OrdersSettings) => updateSettingsMutation.mutate(settings), 750),
        [updateSettingsMutation]
    );

    const updateSettings = async (update: Partial<OrdersSettings>, toasterText?: string) => {
        updateSettingsDebounced({
            ...inspectionForm,
            ...update,
        });
        showSaveSucceedToaster(toasterText);
    };

    const showSaveSucceedToaster = (text?: string) => {
        toasters.success(
            text ?? t('toasters.settingSuccessfullyUpdated'),
            t('toasters.settingUpdated'),
            { duration: 3000 }
        );
    };

    if (isEnterprise) {
        return <div>This page does not work in enterprise</div>;
    }

    return (
        <PageContent paddedX>
            {!!error && (
                <SimpleErrorDisplay
                    alwaysShowError
                    sx={{ mt: 3 }}
                    ctx={{ error }}
                    retry={refetch}
                />
            )}

            <SettingsSection label={t('settings.orders.design.header')}>
                <RadioButtonArrayControl<ConsumerFormType>
                    disabled={isLoading}
                    onChange={(value) => updateSettings({ defaultFormType: value })}
                    label={t('settings.orders.design.formType.header')}
                    value={inspectionForm.defaultFormType}
                    values={[
                        {
                            id: 'BySystem',
                            value: 'BySystem',
                            label: t('settings.orders.design.formType.system'),
                            icon: NumbersIcon, // TODO: change icon to the one used in figma mockup
                        },
                        {
                            id: 'ByPriority',
                            value: 'ByPriority',
                            label: t('settings.orders.design.formType.priority'),
                            icon: TrafficLightIcon,
                        },
                        {
                            id: 'ByEstimate',
                            value: 'ByEstimate',
                            label: t('settings.orders.design.formType.cost'),
                            icon: $Icon,
                        },
                    ]}
                />
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.includeEstimateInDigitalOrder}
                    name="includeEstimateChk"
                    label={t('settings.orders.design.includeEstimate')}
                    onChange={(value) => updateSettings({ includeEstimateInDigitalOrder: value })}
                />
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.showDefaultItems}
                    name="showNaChk"
                    label={t('settings.orders.design.showNA')}
                    onChange={(value) => updateSettings({ showDefaultItems: value })}
                />
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.showDetails}
                    name="showDetailsChk"
                    label={t('settings.orders.design.showDetails')}
                    onChange={(value) => updateSettings({ showDetails: value })}
                />
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.approveOnlyEstimated}
                    name="approveOnlyEstimatedChk"
                    label={t('settings.orders.design.approveOnlyEstimated')}
                    onChange={(value) => updateSettings({ approveOnlyEstimated: value })}
                />
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.showCutRepairOrderNumber}
                    name="showCutRepairOrderNumberChk"
                    label={t('settings.orders.design.showCutOrderNum')}
                    onChange={(value) => updateSettings({ showCutRepairOrderNumber: value })}
                />

                <OptionalNumberSettingControl
                    disabled={isLoading}
                    template="{0}%"
                    validate={(v) => v === null || (v >= 0 && v <= 100)}
                    label={t('settings.orders.design.taxPercentage')}
                    value={inspectionForm.taxPercentage}
                    onValueChange={(value) =>
                        updateSettings(
                            { taxPercentage: value },
                            t('settings.orders.design.taxesSaved')
                        )
                    }
                    name="taxField"
                />

                <OptionalNumberSettingControl
                    disabled={isLoading}
                    template="${0}"
                    validate={(v) => v === null || v >= 0}
                    label={t('settings.orders.design.addHourlyCost')}
                    value={inspectionForm.costPerHour}
                    onValueChange={(value) =>
                        updateSettings(
                            { costPerHour: value },
                            t('settings.orders.design.costSaved')
                        )
                    }
                    thousandSeparator=","
                    name="addHourlyCostField"
                />

                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.showServiceAdvisorInfo}
                    name="showServiceAdvisorInfoChk"
                    label={t('settings.orders.design.showServiceAdvisorInfo')}
                    onChange={(value) => updateSettings({ showServiceAdvisorInfo: value })}
                />

                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.hideMakeLogos}
                    name="hideMakeLogosChk"
                    label={t('settings.orders.design.hideMakeLogos')}
                    onChange={(value) => updateSettings({ hideMakeLogos: value })}
                />

                <AutomaticallyCloseOrders
                    settings={{
                        enabled: inspectionForm.autoCloseOrders.enabled,
                        phaseIds: inspectionForm.autoCloseOrders.phaseIds,
                        inactivityTime: inspectionForm.autoCloseOrders.timeout,
                    }}
                    onSettingsChanged={async (value) => {
                        await updateSettings({
                            autoCloseOrders: {
                                enabled: value.enabled,
                                phaseIds: value.phaseIds,
                                timeout: value.inactivityTime,
                            },
                        });
                    }}
                />

                <RadioButtonArrayControl<DiscountType>
                    disabled={isLoading}
                    onChange={(value) => updateSettings({ discountType: value })}
                    label={t('settings.orders.design.discount.header')}
                    value={inspectionForm.discountType}
                    values={[
                        {
                            id: 'Currency',
                            value: 'Currency',
                            label: t('settings.orders.design.discount.currency'),
                        },
                        {
                            id: 'Percentage',
                            value: 'Percentage',
                            label: t('settings.orders.design.discount.percentage'),
                        },
                    ]}
                />
                <SettingsSection>
                    <OrderPriorityTextEditor
                        labelText={t('settings.orders.orderPriorityText.header')}
                        settings={{
                            enabled: inspectionForm?.orderConfig.enableOrderPriorityText,
                            priorityTextValues: inspectionForm?.orderConfig.orderPriorityTextValues,
                        }}
                        onSettingsChanged={(value) => {
                            updateSettings({
                                orderConfig: {
                                    ...inspectionForm?.orderConfig,
                                    enableOrderPriorityText: value.enabled,
                                    orderPriorityTextValues: value.priorityTextValues,
                                },
                            });
                        }}
                    />
                </SettingsSection>
            </SettingsSection>
            <SettingsSection label={t('settings.orders.photos.header')}>
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.showOriginalPhotos}
                    name="showNaChk"
                    label={t('settings.orders.photos.showOriginal')}
                    onChange={(value) => updateSettings({ showOriginalPhotos: value })}
                />
                <BooleanSettingControl
                    disabled={isLoading}
                    value={inspectionForm.enableFacebookButton}
                    name="showNaChk"
                    label={t('settings.orders.photos.activateFacebook')}
                    onChange={(value) => updateSettings({ enableFacebookButton: value })}
                />
            </SettingsSection>
            <SettingsSection
                label={t('settings.orders.timePickerInterval.header')}
                styles={{ gap: 14 }}
            >
                <Dropdown
                    tooltipText={t('settings.orders.timePickerInterval.tooltip')}
                    label={t('settings.orders.timePickerInterval.label')}
                    name={'tray'}
                    cmosVariant="lightGray"
                    slotProps={{
                        inputWrapper: {
                            sx: { width: 290 },
                        },
                    }}
                    options={timeIntervalOptions}
                    value={selectedTimeInterval || defaultTimeIntervalOption}
                    onChange={(option) => {
                        option &&
                            (() => {
                                setSelectedTimeInterval(option);
                                updateSettings({
                                    orderConfig: {
                                        ...inspectionForm?.orderConfig,
                                        timeInterval: option.value,
                                    },
                                });
                                queryClient.setQueryData(['orders', 'time-interval'], option.value);
                            })();
                    }}
                    isSearchable={false}
                />
            </SettingsSection>
        </PageContent>
    );
}
