import { Box } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { TimeSpan } from 'api/utils/format';
import { Button } from 'common/components/Button';
import InfoText from 'common/components/InfoText';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { NumberField } from 'common/components/Inputs/NumberField';
import CancelModal from 'common/components/Popups/CancelModal';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import isEqual from 'lodash/isEqual';
import { useEffect, useMemo, useState } from 'react';
import PhasePickerMultiple from 'views/Components/PhasePickerMultiple';
import { BooleanSettingControl } from 'views/Settings/common';

type Settings = {
    enabled: boolean;
    phaseIds: number[];
    inactivityTime: string;
};

type AutomaticallyCloseOrdersProps = {
    settings: Settings;
    onSettingsChanged: (settings: Settings) => Promise<void>;
};

type PeriodUnit = 'd' | 'w' | 'M' | 'y';

const SECONDS_IN_A_DAY = 24 * 60 * 60;

const MULTIPLIERS: Record<PeriodUnit, number> = {
    d: 1,
    w: 7,
    M: 30,
    y: 365,
};

type InternalState = {
    unit: PeriodUnit;
    value: number | undefined;
    phaseIds: number[];
    enabled: boolean;
};

function defaultInternalState(): InternalState {
    return {
        unit: 'd',
        value: 0,
        phaseIds: [],
        enabled: false,
    };
}

export default function AutomaticallyCloseOrders({
    settings,
    onSettingsChanged,
}: AutomaticallyCloseOrdersProps) {
    const { t } = useAppTranslation();
    const hintText = t('settings.orders.autoCloseOrders.hint');
    const [cancelPopupOpen, setCancelPopupOpen] = useState(false);
    const [disablePopupOpen, setDisablePopupOpen] = useState(false);
    const [state, setState] = useState(defaultInternalState);

    useEffect(() => {
        setState(settingsToInternalState(settings));
    }, [settings]);

    const saveMutation = useMutation(async () => {
        const newSettings = internalStateToSettings(state);
        await onSettingsChanged(newSettings);
    });

    const setEnabled = (value: boolean) => {
        if (settings.enabled === value) {
            setState((s) => ({
                ...s,
                enabled: value,
            }));
            return;
        }

        if (!value) {
            setDisablePopupOpen(true);
        } else {
            setState((s) => ({
                ...s,
                enabled: value,
            }));
        }
    };

    const disable = () => {
        setDisablePopupOpen(false);
        setState((x) => ({ ...x, enabled: false }));
        onSettingsChanged({
            enabled: false,
            phaseIds: [],
            inactivityTime: new TimeSpan(0).toString(),
        });
    };

    const onCancel = (force: boolean = false) => {
        if (!force && hasChanges(settings, state)) {
            setCancelPopupOpen(true);
        } else {
            setEnabled(false);
            setState(settingsToInternalState(settings));
        }
    };

    const hasSomethingChanged = useMemo(
        () => hasChanges(settings, state, false),
        [settings, state]
    );

    const canSave = state.value !== undefined && state.value !== 0;

    return (
        <>
            <BooleanSettingControl
                label={t('settings.orders.autoCloseOrders.header')}
                value={state.enabled}
                hasHint={!state.enabled}
                hintText={hintText}
                onChange={setEnabled}
                slotProps={{
                    layout: {
                        expanded: state.enabled,
                        childrenBelow: (
                            <>
                                <InfoText sx={{ mt: 2 }}>
                                    <span>{hintText}</span>
                                </InfoText>

                                <Box sx={{ display: 'flex', gap: 8, mt: 2 }}>
                                    <InputWrapper
                                        label={t('settings.orders.autoCloseOrders.inactivityTime')}
                                        isRequired
                                        showValidationIndicators
                                        hideRoundIndicator
                                    >
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                            <NumberField
                                                sx={{ width: 100 }}
                                                value={state.value}
                                                maxLength={4}
                                                decimalScale={0}
                                                InputProps={{
                                                    onKeyDown: (e) => {
                                                        if (e.key === '-') {
                                                            e.preventDefault();
                                                        }
                                                    },
                                                }}
                                                placeholder="6"
                                                disallowInvalidInputValue
                                                onValueChange={(v) => {
                                                    const value = v.floatValue;
                                                    setState((x) => ({
                                                        ...x,
                                                        value:
                                                            value === undefined
                                                                ? undefined
                                                                : Math.floor(Math.max(0, value)),
                                                    }));
                                                }}
                                            />
                                            <SSelectInput
                                                disabled={
                                                    state.value === 0 || state.value === undefined
                                                }
                                                value={state.unit}
                                                onChange={(e) => {
                                                    setState((x) => ({
                                                        ...x,
                                                        unit: e.target.value as PeriodUnit,
                                                    }));
                                                }}
                                            >
                                                <SMenuItem value="d">
                                                    {state.value === 1
                                                        ? t('commonLabels.timePeriods.d-singular')
                                                        : t('commonLabels.timePeriods.d')}
                                                </SMenuItem>
                                                <SMenuItem value="w">
                                                    {state.value === 1
                                                        ? t('commonLabels.timePeriods.w-singular')
                                                        : t('commonLabels.timePeriods.w')}
                                                </SMenuItem>
                                                <SMenuItem value="M">
                                                    {state.value === 1
                                                        ? t('commonLabels.timePeriods.M-singular')
                                                        : t('commonLabels.timePeriods.M')}
                                                </SMenuItem>
                                                <SMenuItem value="y">
                                                    {state.value === 1
                                                        ? t('commonLabels.timePeriods.y-singular')
                                                        : t('commonLabels.timePeriods.y')}
                                                </SMenuItem>
                                            </SSelectInput>
                                        </Box>
                                    </InputWrapper>

                                    <InputWrapper
                                        sx={{ width: 200 }}
                                        label={t('settings.orders.autoCloseOrders.selectPhase')}
                                    >
                                        <PhasePickerMultiple
                                            treatNoPhasesAsAllPhases
                                            placeholder={t(
                                                'settings.orders.autoCloseOrders.selectPhasePlaceholder'
                                            )}
                                            phaseIds={state.phaseIds}
                                            disabled={
                                                state.value === 0 || state.value === undefined
                                            }
                                            onChange={(phaseIds) =>
                                                setState((x) => ({ ...x, phaseIds }))
                                            }
                                        />
                                    </InputWrapper>
                                </Box>
                                <Box
                                    sx={{
                                        display: 'flex',
                                        justifyContent: 'flex-end',
                                        gap: 2,
                                        mr: 4,
                                        mt: 2,
                                    }}
                                >
                                    <Button
                                        w="md"
                                        cmosVariant="stroke"
                                        color={Colors.Neutral6}
                                        onClick={() => onCancel()}
                                    >
                                        {t('commonLabels.cancel')}
                                    </Button>
                                    <Button
                                        w="md"
                                        color={Colors.Success}
                                        onClick={() => saveMutation.mutate()}
                                        showLoader={saveMutation.isLoading}
                                        disabled={!canSave || !hasSomethingChanged}
                                    >
                                        {t('commonLabels.save')}
                                    </Button>
                                </Box>
                            </>
                        ),
                    },
                }}
            />
            <CancelModal
                open={cancelPopupOpen}
                title={t('settings.orders.autoCloseOrders.cancelPopupTitle')}
                onCancel={() => {
                    onCancel(true);
                    setCancelPopupOpen(false);
                }}
                onClose={() => {
                    setCancelPopupOpen(false);
                }}
            />
            <ConfirmationModal
                onConfirmation={disable}
                onClose={() => setDisablePopupOpen(false)}
                open={disablePopupOpen}
            >
                {t('settings.orders.autoCloseOrders.disablePopupTitle')}
            </ConfirmationModal>
        </>
    );
}

function settingsToInternalState(settings: Settings): InternalState {
    let unit: PeriodUnit;
    let value: number;

    try {
        const ts = TimeSpan.fromString(settings.inactivityTime);
        const totalDaysFloat = ts.totalSeconds / SECONDS_IN_A_DAY;
        const totalDays = Math.floor(totalDaysFloat);

        const isPositiveInteger = (v: number) => Number.isInteger(v) && v > 0;

        if (isPositiveInteger(totalDays / 365)) {
            value = totalDaysFloat / 365;
            unit = 'y';
        } else if (isPositiveInteger(totalDays / 30)) {
            value = totalDaysFloat / 30;
            unit = 'M';
        } else if (isPositiveInteger(totalDays / 7)) {
            value = totalDaysFloat / 7;
            unit = 'w';
        } else {
            value = totalDaysFloat;
            unit = 'd';
        }
    } catch {
        value = 0;
        unit = 'd';
    }

    return {
        phaseIds: settings.phaseIds,
        unit,
        value: value === 0 ? undefined : value,
        enabled: settings.enabled,
    };
}

function internalStateToSettings(state: InternalState): Settings {
    const ts = new TimeSpan((state.value ?? 0) * MULTIPLIERS[state.unit] * SECONDS_IN_A_DAY);
    return {
        inactivityTime: ts.toString(),
        phaseIds: state.phaseIds,
        enabled: state.enabled,
    };
}

function hasChanges(
    settings: Settings,
    state: InternalState,
    ignoreEnabled: boolean = true
): boolean {
    const stateFromSettings = settingsToInternalState(settings);
    if (ignoreEnabled) {
        stateFromSettings.enabled = state.enabled;
    }
    return !isEqual(stateFromSettings, state);
}
