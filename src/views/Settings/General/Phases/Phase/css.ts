import { makeStyles } from '@mui/styles';
export const usePhaseStyles = makeStyles((theme) => ({
    header: {
        marginBottom: 8,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        '& .title': {
            width: 653,
            display: 'flex',
            alignItems: 'center',
            height: 44,
            flexGrow: 1,
            '& .drag-and-drop-editable': {
                width: '100%',
            },
        },
        '& .options': {
            display: 'flex',
            justifyContent: 'flex-start',
            flexDirection: 'row',
            paddingRight: 78,
        },
    },
}));
