import { PhaseDto } from 'api/orders';
import WpPhasesApi, { UpdatePhaseDto } from 'api/workshopPlanner/phases';
import { AxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { DragAndDropEditableTextField } from 'common/components/Inputs/DragAndDropEditableTextField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { ErrorDto } from 'datacontracts/ErrorDto';
import { useCallback, useState } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import { usePhaseStyles } from './css';

interface PhaseProps {
    phase: PhaseDto;
    onDelete?: (item: PhaseDto) => void;
    onChange?: (item: PhaseDto) => void;
    dragOptions?: DraggableProvidedDragHandleProps;
    readonly?: boolean;
    isPredefined: boolean;
}

const Phase = ({ phase, dragOptions, onDelete, onChange, readonly, isPredefined }: PhaseProps) => {
    const styles = usePhaseStyles();
    const toaster = useToasters();
    const { t } = useAppTranslation();
    const { callApi, apiCallStatus } = useApiCall();
    const [editMode, setEditMode] = useState(false);
    const [name, setName] = useState(phase.name);
    const canPerformSave = apiCallStatus !== 'Pending';

    const handleEdit = useCallback(() => {
        setEditMode(!editMode);
        setName(phase.name);
    }, [editMode, phase]);

    const handleOpenDeletion = useCallback(() => {
        onDelete && onDelete(phase);
    }, [onDelete, phase]);

    const fetchUpdateName = async () => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;

        if (name.length > 50) {
            toaster.warning(
                t('workshopPlanner.phases.settings.notifications.tooLongNamePhaseBody'),
                t('workshopPlanner.phases.settings.notifications.tooLongNamePhaseTitle')
            );
            return;
        }

        const request: UpdatePhaseDto = {
            id: phase.id,
            name: name,
        };

        if (name !== phase.name) {
            await callApi(async () => WpPhasesApi.updatePhase(request), {
                selectErrorContent: (errorResponse: AxiosError<ErrorDto>) => {
                    setEditMode(false);
                    setName(phase.name);
                    return errorResponse?.code === 'EntityDuplicated'
                        ? {
                              title: t(
                                  'workshopPlanner.phases.settings.notifications.duplicatePhaseTitle'
                              ),
                              body: t(
                                  'workshopPlanner.phases.settings.notifications.duplicatePhaseBody'
                              ),
                              type: NotificationType.danger,
                          }
                        : {
                              body: t('toasters.errorOccurredWhenSaving'),
                          };
                },
            });

            if (onChange) {
                onChange({ ...phase, name });
            }

            toaster.success(
                t('workshopPlanner.phases.settings.notifications.updatedPhaseBody'),
                t('workshopPlanner.phases.settings.notifications.updatedPhaseTitle')
            );
        }

        setEditMode(false);
    };

    return (
        <>
            <div className={styles.header}>
                <div className={'title'}>
                    <DragAndDropEditableTextField
                        disabled={readonly}
                        dragAndDropProps={dragOptions}
                        value={name}
                        onChange={(event) => setName(event.target.value)}
                        isEditMode={editMode && apiCallStatus !== 'Pending'}
                        showLoader={editMode && apiCallStatus === 'Pending'}
                        onSaveClick={() => fetchUpdateName()}
                        isDraggable={!isPredefined}
                    />
                </div>
                {!isPredefined ? (
                    <div className="options">
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={editMode ? CloseIcon : EditIcon}
                            color={Colors.Neutral3}
                            onClick={handleEdit}
                        />
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={DeleteIcon}
                            color={Colors.Neutral3}
                            onClick={handleOpenDeletion}
                        />
                    </div>
                ) : (
                    <div style={{ width: 142 }} />
                )}
            </div>
        </>
    );
};

export default Phase;
