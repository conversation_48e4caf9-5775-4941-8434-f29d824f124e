import { PhaseDto } from 'api/orders';
import WpPhasesApi from 'api/workshopPlanner/phases';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useEffect, useState } from 'react';
import PageContent from 'views/Components/Page';
import PhasesList from './PhasesList';
import { usePhasesSettingsStyles } from './css';

const PhasesSettings = () => {
    const styles = usePhasesSettingsStyles();
    const { t } = useAppTranslation();
    const { callApi, apiCallStatus } = useApiCall();
    const [phases, setPhases] = useState<PhaseDto[]>([]);

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.phases')}`);

    const getPhases = async () => {
        const response = await callApi(
            async () =>
                WpPhasesApi.getPhases().then((x) =>
                    x.filter((p) => !['noPhase', 'closedOrder'].includes(p.name))
                ),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }
        );

        setPhases(response);
    };

    useEffect(() => {
        getPhases();
    }, []);

    return (
        <PageContent paddedX paddedY>
            <span className={styles.header}>{t('settings.general.phases')}</span>
            <div className={styles.list}>
                <PhasesList isLoadingInProgress={apiCallStatus === 'Pending'} phases={phases} />
            </div>
        </PageContent>
    );
};

export default PhasesSettings;
