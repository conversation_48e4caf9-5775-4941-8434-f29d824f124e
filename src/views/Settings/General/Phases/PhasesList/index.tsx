import { Divider, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { PhaseDto } from 'api/orders';
import PhaseSetbackApi from 'api/phaseSetback';
import WpPhasesApi, { ReorderPhasesDto } from 'api/workshopPlanner/phases';
import AreaSpinner from 'common/components/AreaSpinner';
import {
    DeleteConfirmationPopup,
    WarningConfirmationPopup,
} from 'common/components/Popups/ConfirmationPopup';
import { Phases } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters, { UseToasters } from 'common/hooks/useToasters';
import { TFunction } from 'i18next';
import { useEffect, useState } from 'react';
import { DragDropContext, Draggable, DropResult, Droppable } from 'react-beautiful-dnd';
import { AppDispatch, useAppDispatch, useAppSelector } from 'store';
import phaseSetbackSlice from 'store/slices/phaseSetback';
import { selectHasAnyPhaseSetbackRule } from 'store/slices/phaseSetback/selectors';
import Phase from '../Phase';
import PhaseForm from '../PhaseForm';
import { usePhaseListStyles } from './css';

const reorder = (list: PhaseDto[], startIndex: number, endIndex: number) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
};

interface PhasesListProps {
    phases: PhaseDto[];
    isLoadingInProgress: boolean;
}

const PhaseList = ({ phases, isLoadingInProgress }: PhasesListProps) => {
    const styles = usePhaseListStyles();
    const { t } = useAppTranslation();
    const [list, setList] = useState<PhaseDto[]>([]);
    const [isDeletePopupOpen, setIsDeletePopupOpen] = useState<boolean>(false);
    const [phaseToDelete, setPhaseToDelete] = useState<PhaseDto>();

    const hasAnyPhaseSetbackRule = useAppSelector(selectHasAnyPhaseSetbackRule);
    const [isReorderPopupOpen, setIsReorderPopupOpen] = useState(false);
    const [reorderSourceIndex, setReorderSourceIndex] = useState<number | null>(null);
    const [reorderDestinationIndex, setReorderDestinationIndex] = useState<number | null>(null);

    const updatePhaseNameHandler = (phase: PhaseDto) => {
        const newList = list.map((item) => (item.id === phase.id ? { ...phase } : { ...item }));
        setList([...newList]);
    };

    const deletePhaseHandler = (phase: PhaseDto) => {
        setPhaseToDelete(phase);
        setIsDeletePopupOpen(true);
    };

    const { mutate: deleteMutate } = useDeleteMutation(hasAnyPhaseSetbackRule, (data) => {
        setList((old) => [...old.filter((p) => p.id !== data.id)]);
        setIsDeletePopupOpen(false);
    });

    const onDeleteConfirm = () => {
        if (phaseToDelete) {
            deleteMutate(phaseToDelete?.id);
        }
    };

    const addNewPhaseHandler = (item: PhaseDto) => {
        setList([...list, { ...item }]);
    };

    const closeReorderPopup = () => {
        setReorderSourceIndex(null);
        setReorderDestinationIndex(null);

        setIsReorderPopupOpen(false);
    };

    const reorderMutation = useReorderMutation(hasAnyPhaseSetbackRule, (newPhasesList) => {
        setList(newPhasesList);
        closeReorderPopup();
    });

    const reorderPhases = (sourceIndex: number, destinationIndex: number) => {
        const newList = reorder(list, sourceIndex, destinationIndex);

        const reorderPhases = newList.map<ReorderPhasesDto>((value) => ({
            id: value.id,
            order: newList.lastIndexOf(value) + 1,
        }));

        reorderMutation.mutate({
            reorderPhases,
            newPhasesList: newList,
            movedPhaseIndex: destinationIndex,
        });
    };

    const onDragEnd = async (result: DropResult) => {
        if (!result.destination || result.destination.index === result.source.index) return;

        if (hasAnyPhaseSetbackRule) {
            setReorderSourceIndex(result.source.index);
            setReorderDestinationIndex(result.destination.index);
            setIsReorderPopupOpen(true);
        } else {
            reorderPhases(result.source.index, result.destination.index);
        }
    };

    const onReorderConfirm = () => {
        if (reorderSourceIndex === null || reorderDestinationIndex === null) {
            return;
        }

        reorderPhases(reorderSourceIndex, reorderDestinationIndex);
    };

    useEffect(() => {
        setList(phases);
    }, [phases]);

    // TODO (MB) do not hard-code default phases, instead return default: true or something from backend to distinguish them

    return (
        <>
            {isLoadingInProgress ? (
                <div className={styles.loader}>
                    <AreaSpinner />
                </div>
            ) : (
                list && (
                    <div>
                        <div className={styles.root}>
                            <DragDropContext onDragEnd={onDragEnd}>
                                <Phase
                                    phase={{
                                        id: -1,
                                        name: t('workshopPlanner.phases.noPhase'),
                                        order: 0,
                                    }}
                                    isPredefined
                                />
                                <Droppable droppableId={`list-phases`}>
                                    {(provided) => (
                                        <div ref={provided.innerRef} {...provided.droppableProps}>
                                            {list.map((phase: PhaseDto, index: number) => (
                                                <Draggable
                                                    draggableId={phase.name.toString()}
                                                    index={index}
                                                    key={`phase-${phase.name}`}
                                                >
                                                    {(provided) => (
                                                        <div
                                                            ref={provided.innerRef}
                                                            {...provided.draggableProps}
                                                        >
                                                            <Phase
                                                                phase={phase}
                                                                onChange={updatePhaseNameHandler}
                                                                onDelete={deletePhaseHandler}
                                                                dragOptions={
                                                                    provided.dragHandleProps ??
                                                                    undefined
                                                                }
                                                                isPredefined={false}
                                                            />
                                                        </div>
                                                    )}
                                                </Draggable>
                                            ))}
                                            {provided.placeholder}
                                        </div>
                                    )}
                                </Droppable>
                                <Phase
                                    phase={{
                                        id: Phases.Closed,
                                        name: t('workshopPlanner.phases.closedOrder'),
                                        order: Number.MAX_SAFE_INTEGER,
                                    }}
                                    isPredefined
                                />
                            </DragDropContext>
                        </div>
                        <div className={styles.addSection}>
                            <PhaseForm onAdd={addNewPhaseHandler} />
                        </div>
                        <DeleteConfirmationPopup
                            open={isDeletePopupOpen}
                            title={t(
                                'workshopPlanner.phases.settings.deletePopup.deletePhaseTitle'
                            )}
                            body={
                                <PopupBody>
                                    <Divider style={{ width: '50%', marginBottom: '15px' }} />
                                    {hasAnyPhaseSetbackRule
                                        ? t(
                                              'workshopPlanner.phases.settings.deletePopup.changesAffectSettings'
                                          )
                                        : t(
                                              'workshopPlanner.phases.settings.deletePopup.doYouWantToDeleteThisPhase?'
                                          )}
                                </PopupBody>
                            }
                            cancel={t('commonLabels.doNotDelete')}
                            confirm={t('workshopPlanner.phases.settings.deletePopup.deletePhase')}
                            onConfirm={onDeleteConfirm}
                            onClose={() => {
                                setPhaseToDelete(undefined);
                                setIsDeletePopupOpen(false);
                            }}
                        />
                        <WarningConfirmationPopup
                            open={isReorderPopupOpen}
                            title={t('workshopPlanner.phases.settings.reorderPopup.title')}
                            body={
                                <PopupBody>
                                    <Divider style={{ width: '50%', marginBottom: '15px' }} />
                                    {t('workshopPlanner.phases.settings.reorderPopup.body')}
                                </PopupBody>
                            }
                            cancel={t('commonLabels.goBack')}
                            confirm={t('workshopPlanner.phases.settings.reorderPopup.confirm')}
                            onConfirm={onReorderConfirm}
                            onClose={closeReorderPopup}
                        />
                    </div>
                )
            )}
        </>
    );
};

const PopupBody = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    fontSize: '14px',
});

const useDeleteMutation = (
    hasAnyPhaseSetbackRule: boolean,
    onSuccess?: (data: PhaseDto) => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const mutation = useMutation(
        async (phaseId: number) => {
            const result = await WpPhasesApi.deletePhase(phaseId);

            if (hasAnyPhaseSetbackRule) {
                await removeAffectedRules(phaseId, toasters, t, dispatch);
            }

            return result;
        },
        {
            onSuccess: (data) => {
                onSuccess && onSuccess(data);
                toasters.success(
                    t('workshopPlanner.phases.settings.notifications.phaseDeletedSuccessfully'),
                    t('workshopPlanner.phases.settings.notifications.updatedPhase')
                );
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    return mutation;
};

const useReorderMutation = (
    hasAnyPhaseSetbackRule: boolean,
    onSuccess?: (newPhasesList: PhaseDto[]) => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const mutation = useMutation(
        async ({
            reorderPhases,
            newPhasesList,
            movedPhaseIndex,
        }: {
            reorderPhases: ReorderPhasesDto[];
            newPhasesList: PhaseDto[];
            movedPhaseIndex: number;
        }) => {
            await WpPhasesApi.reorderPhases(reorderPhases);

            if (hasAnyPhaseSetbackRule) {
                const movedPhaseId = reorderPhases[movedPhaseIndex].id;

                await removeAffectedRules(movedPhaseId, toasters, t, dispatch);
            }
        },
        {
            onSuccess(_, { newPhasesList }) {
                onSuccess && onSuccess(newPhasesList);
                toasters.success(
                    t('workshopPlanner.phases.settings.notifications.updatedPhaseBody'),
                    t('workshopPlanner.phases.settings.notifications.updatedPhaseTitle')
                );
            },
            onError() {
                toasters.danger('', t('toasters.errorOccurredWhenSaving'));
            },
        }
    );

    return mutation;
};

const removeAffectedRules = async (
    phaseId: number,
    toasters: UseToasters,
    t: TFunction,
    dispatch: AppDispatch
) => {
    try {
        const newRules = await PhaseSetbackApi.removeAffectedRules(phaseId);
        dispatch(phaseSetbackSlice.actions.reset(newRules));
    } catch {
        toasters.danger('', t('toasters.errorOccurredWhenSaving'));
    }
};

export default PhaseList;
