import { makeStyles } from '@mui/styles';
import { FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const usePhaseFormStyles = makeStyles((theme) => ({
    buttonAdd: {
        width: 189,
        '& button>div': {
            width: 171,
            justifyContent: 'space-between',
            display: 'flex',
        },
    },
    form: {
        '& .showDetail': {
            ...FontSecondary(HeaderStyles.H6_12px, false, theme.palette.neutral[5]),
        },
    },
    inputContainer: {
        width: 320,
        display: 'flex',
        '& .field': {
            width: 320,
        },
        '& .options': {},
    },
    save: {
        display: 'flex',
        cursor: 'pointer',
    },
}));
