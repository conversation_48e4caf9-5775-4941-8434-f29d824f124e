import { IconButton, InputAdornment } from '@mui/material';
import { PhaseDto } from 'api/orders';
import WpPhasesApi, { CreatePhaseDto } from 'api/workshopPlanner/phases';
import { AxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import TextFormField from 'common/components/Inputs/TextField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { ErrorDto } from 'datacontracts/ErrorDto';
import { useState } from 'react';
import { usePhaseFormStyles } from './css';

interface PhaseFormProps {
    onAdd: (templateItem: PhaseDto) => void;
}

const PhaseForm = ({ onAdd }: PhaseFormProps) => {
    const styles = usePhaseFormStyles();
    const { t } = useAppTranslation();
    const toaster = useToasters();
    const [inputMode, setInputMode] = useState(false);
    const [name, setName] = useState('');
    const { callApi, apiCallStatus } = useApiCall();

    const fetchSave = async () => {
        console.debug(name);
        if (name.length > 50) {
            toaster.warning(
                t('workshopPlanner.phases.settings.notifications.tooLongNamePhaseBody'),
                t('workshopPlanner.phases.settings.notifications.tooLongNamePhaseTitle')
            );
            return;
        }

        const requestSave: CreatePhaseDto = {
            name: name,
        };

        const response = await callApi(async () => WpPhasesApi.createPhase(requestSave), {
            selectErrorContent: (errorResponse: AxiosError<ErrorDto>) => {
                setName('');
                setInputMode(false);
                return errorResponse?.code === 'EntityDuplicated'
                    ? {
                          title: t(
                              'workshopPlanner.phases.settings.notifications.duplicatePhaseTitle'
                          ),
                          body: t(
                              'workshopPlanner.phases.settings.notifications.duplicatePhaseBody'
                          ),
                          type: NotificationType.danger,
                      }
                    : {
                          body: t('toasters.errorOccurredWhenSaving'),
                      };
            },
        });

        if (!response) return;
        if (onAdd) onAdd(response);

        toaster.success(
            t('workshopPlanner.phases.settings.notifications.phaseCreatedBody'),
            t('workshopPlanner.phases.settings.notifications.phaseCreatedTitle')
        );

        setName('');
        setInputMode(false);
    };

    return (
        <>
            {!inputMode && (
                <div className={styles.buttonAdd}>
                    <Button
                        customStyles={{
                            backgroundColor: `${Colors.Neutral2}!important`,
                            /* Neutrales/4 */
                            border: `1px solid ${Colors.Neutral4}`,
                        }}
                        cmosVariant={'stroke'}
                        iconPosition="right"
                        Icon={PlusIcon}
                        color={Colors.Neutral3}
                        onClick={() => {
                            setInputMode(true);
                        }}
                        label={t('workshopPlanner.phases.settings.addPhase')}
                    />
                </div>
            )}
            {inputMode && (
                <div className={styles.form}>
                    <div className={styles.inputContainer}>
                        <div className="field">
                            <TextFormField
                                autoFocus
                                name={'newPhaseField'}
                                label={''}
                                placeholder={t('workshopPlanner.phases.settings.enterPhase')}
                                type={'text'}
                                isRequired={false}
                                value={name}
                                readonly={apiCallStatus === 'Pending'}
                                onChange={(event) => {
                                    setName(event.target.value);
                                }}
                                onEnterPress={name.length >= 1 ? fetchSave : undefined}
                                enableEnterComplete={true}
                                cmosVariant="roundedGrey"
                                showLoader={apiCallStatus === 'Pending'}
                                endAdornment={
                                    <InputAdornment position="end">
                                        <IconButton onClick={fetchSave} disabled={name.length < 1}>
                                            <CheckIcon size={IconSize.M} />
                                        </IconButton>
                                    </InputAdornment>
                                }
                            />
                        </div>
                        <div className="options">
                            <Button
                                color={Colors.Neutral3}
                                cmosVariant={'typography'}
                                Icon={CloseIcon}
                                label=""
                                onClick={() => {
                                    if (apiCallStatus === 'Pending') return;
                                    setInputMode(false);
                                }}
                            />
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default PhaseForm;
