import { Box, Divider, styled, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { UploadSpotDto } from 'api/common';
import { getErrorMessage } from 'api/error';
import PackagesSettingsApi from 'api/settings/packages';
import { isAxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { DownloadCloudIcon } from 'common/components/Icons/DownloadCloudIcon';
import { Modal } from 'common/components/Modal';
import ConfirmationModal from 'common/components/Popups/ConfirmationModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { Interweave } from 'interweave';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { HttpUtils } from 'services';
import { hasCode, isErrorResponse } from 'services/Server';
import DragAndDropFileInputControllable from 'views/Components/DragAndDropFileInput/DragAndDropFileInputControllable';

const ImportPackageModalContext = createContext<{
    open(): void;
} | null>(null);

export function useImportPackageModal() {
    const ctx = useContext(ImportPackageModalContext);
    if (!ctx) throw new Error('ImportPackageModal is not available');
    return ctx;
}

export default function ImportPackageModalProvider({
    children,
    onImported,
}: React.PropsWithChildren<{ onImported: () => void }>) {
    const [open, setOpen] = useState(false);

    const ctx = useMemo(
        () => ({
            open: () => {
                setOpen(true);
            },
        }),
        []
    );

    return (
        <ImportPackageModalContext.Provider value={ctx}>
            <ImportPackageModal
                onImported={onImported}
                open={open}
                onClose={() => setOpen(false)}
            />
            {children}
        </ImportPackageModalContext.Provider>
    );
}

function ImportPackageModal({
    open,
    onClose,
    onImported,
}: {
    onClose: () => void;
    open: boolean;
    onImported: () => void;
}) {
    const { t, i18n } = useAppTranslation();
    const [closeModalOpen, setCloseModalOpen] = useState(false);
    const [file, setFile] = useState<File | null>(null);
    const [uploadedFile, setUploadedFile] = useState<{ token: string; file: File } | null>(null);
    const [skippedRows, setSkippedRows] = useState<number[]>([]);
    const [disableTemplateDownload, setDisableTemplateDownload] = useState(false);

    useEffect(() => {
        (window as any).file = file;
    }, [file]);

    const uploadSpot = useUploadSpot(open);
    const toasters = useToasters();

    const resetState = () => {
        setSkippedRows([]);
        setUploadedFile(null);
        setFile(null);
        setCloseModalOpen(false);
    };

    const handleClose = () => {
        if (!file) {
            onClose();
        } else {
            setCloseModalOpen(true);
        }
    };

    const importPackage = useMutation(
        async () => {
            if (!uploadSpot) {
                throw new Error('upload spot has not been created yet');
            }
            if (!file) {
                throw new Error('file is not null');
            }

            if (
                !uploadedFile ||
                uploadedFile.file !== file ||
                uploadedFile.token !== uploadSpot.fileToken
            ) {
                await HttpUtils.uploadFileViaPut(uploadSpot.uploadLink, file);
                setUploadedFile({ file, token: uploadSpot.fileToken });
            }

            const result = await PackagesSettingsApi.importPackage({
                fileToken: uploadSpot.fileToken,
                skippedRows: skippedRows,
            });

            if (result.failedExcelImportResult != null) {
                toasters.warning(
                    <Interweave
                        content={t('toasters.documentPartiallyUploadedText', {
                            failedRowsFileUrl: result.failedExcelImportResult.failedRowsFileUrl,
                            totalRows: result.failedExcelImportResult.totalRows,
                            importedRows: result.failedExcelImportResult.importedRows,
                        })}
                    />,
                    t('toasters.documentPartiallyUploadedTitle'),
                    { duration: 15000 }
                );
            } else {
                toasters.success(
                    t('settings.packages.importModal.success.text'),
                    t('settings.packages.importModal.success.title')
                );
            }

            onImported();
            onClose();
        },
        {
            onError(error, _variables, _context) {
                if (isAxiosError(error) && isErrorResponse(error.response?.data)) {
                    if (hasCode(error.response.data, 'General.Packages.RequiredColumnMissing')) {
                        toasters.danger(
                            t('settings.packages.importModal.mandatoryColMissing.text'),
                            t('settings.packages.importModal.mandatoryColMissing.title')
                        );
                        return;
                    }
                }

                toasters.danger(
                    t('toasters.errorOccurredWhenSaving') + ': ' + getErrorMessage(error),
                    t('toasters.errorOccurred')
                );
            },
        }
    );

    const handleTemplateDownload = async () => {
        PackagesSettingsApi.downloadTemplate();
        setDisableTemplateDownload(true);
        setTimeout(() => {
            setDisableTemplateDownload(false);
        }, 700);
    };

    return (
        <Modal
            boxComponent={BoxComponent}
            open={open}
            onClose={handleClose}
            onTransitionExited={resetState}
        >
            <ConfirmationModal
                open={closeModalOpen}
                onClose={() => setCloseModalOpen(false)}
                onConfirmation={() => {
                    setCloseModalOpen(false);
                    onClose();
                }}
            >
                {t('settings.packages.importModal.cancel')}
            </ConfirmationModal>
            <Box sx={{ display: 'flex', p: 2, justifyContent: 'space-between' }}>
                <Typography variant="h5Roboto" color="neutral.9">
                    {t('settings.packages.import')}
                </Typography>

                <Box sx={{ display: 'flex', gap: '5px' }}>
                    <Button w="md" color={Colors.Neutral3} onClick={handleClose}>
                        {t('commonLabels.cancel')}
                    </Button>
                    <Button
                        w="md"
                        color="success"
                        disabled={!uploadSpot || importPackage.isLoading || !file}
                        showLoader={!uploadSpot || importPackage.isLoading}
                        onClick={() => importPackage.mutate()}
                    >
                        {t('settings.packages.importModal.uploadFile')}
                    </Button>
                </Box>
            </Box>

            <DragAndDropFileInputControllable
                // acceptedFormats={['xlsx']}
                onFileSet={setFile}
                file={file}
            />

            <Divider sx={{ my: 3 }} />

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr auto' }}>
                <Typography
                    variant="h6Roboto"
                    sx={{ fontWeight: 'normal', color: 'var(--neutral8)' }}
                >
                    <strong>{t('commonLabels.notes').toUpperCase()}</strong>
                    <Box sx={{ m: 0, whiteSpace: 'pre' }}>
                        {t('settings.packages.importModal.notes')}
                    </Box>
                </Typography>

                <Button
                    disabled={disableTemplateDownload}
                    cmosVariant="typography"
                    onClick={handleTemplateDownload}
                >
                    <DownloadCloudIcon fill="currentColor" />
                    {t('settings.packages.importModal.downloadTemplate')}
                </Button>
            </Box>
        </Modal>
    );
}

const BoxComponent = styled('div')({
    width: 820,
    padding: '30px 40px',
});

const uploadSpotQueryKey = ['settings', 'packages-import-upload-spot'];

function useUploadSpot(enabled: boolean): UploadSpotDto | undefined {
    const { data } = useQuery(
        uploadSpotQueryKey,
        () => PackagesSettingsApi.getPackagesImportUploadSpot(),
        {
            staleTime: 10 * 1000,
            cacheTime: 24 * 60 * 60 * 1000,
            enabled,
        }
    );

    return data;
}
