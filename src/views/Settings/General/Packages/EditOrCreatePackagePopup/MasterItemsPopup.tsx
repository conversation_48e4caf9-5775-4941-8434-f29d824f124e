import { Box, IconButton, styled } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import MasterItemAPI, { MasterItemDto } from 'api/MasterItem';
import clsx from 'clsx';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import FilterTextField from 'common/components/Inputs/FilterTextField';
import { Modal } from 'common/components/Modal';
import { SMenuItem } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import isEqual from 'lodash/isEqual';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { createSelector } from 'reselect';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import {
    packagesActions,
    selectPackageItem,
    selectPackageItems,
} from 'store/slices/Settings/packages';
import VirtualizedAlphabeticallyGroupedList, {
    VirtualizedAlphabeticallyGroupedListItemProps,
} from './VirtualizedAlphabeticallyGroupedList';

const MasterItemPopupContext = createContext<{
    open(itemKey: string): void;
} | null>(null);

export function useMasterItemPopupContext() {
    const ctx = useContext(MasterItemPopupContext);
    if (!ctx) throw new Error('MasterItemPopupContext is not available');
    return ctx;
}

const selectPackageItemMasterItemId = createSelector(
    (r: RootState, itemKey: string | undefined) => {
        if (itemKey === undefined) return undefined;

        try {
            return selectPackageItem(r, itemKey);
        } catch {
            return undefined;
        }
    },
    (i) => i?.item.masterItemId
);

export function MasterItemPopupProvider({ children }: React.PropsWithChildren<{}>) {
    const [itemKey, setItemKey] = useState<string>();
    const masterItemId = useAppSelector((r) => selectPackageItemMasterItemId(r, itemKey));
    const dispatch = useAppDispatch();

    const ctx = useMemo(() => {
        return {
            open(packageItemKey: string) {
                setItemKey(packageItemKey);
            },
        };
    }, []);

    const handleMasterItemSelected = useCallback(
        (masterItem: MasterItemDto) => {
            if (itemKey) {
                dispatch(
                    packagesActions.updateItem({
                        itemKey,
                        masterItemId: masterItem.id,
                    })
                );
                setItemKey(undefined);
            }
        },
        [itemKey, dispatch]
    );

    return (
        <MasterItemPopupContext.Provider value={ctx}>
            {itemKey && (
                <MasterItemsPopup
                    selectedMasterItemId={masterItemId ?? null}
                    onSelected={handleMasterItemSelected}
                    onClose={() => setItemKey(undefined)}
                />
            )}
            {children}
        </MasterItemPopupContext.Provider>
    );
}

type MasterItemsPopupProps = {
    selectedMasterItemId: number | null;
    onSelected: (masterItem: MasterItemDto) => void;
    onClose: () => void;
};

const selectMasterItemIds = createSelector(
    selectPackageItems,
    (items) => items.map((x) => x.masterItemId).filter((x) => !!x) as number[]
);

export default function MasterItemsPopup({
    selectedMasterItemId,
    onSelected,
    onClose,
}: MasterItemsPopupProps) {
    const { t } = useAppTranslation();
    const { data } = useQuery(['master-items'], () => MasterItemAPI.getAll(), {
        staleTime: 1000,
        cacheTime: Infinity,
    });
    const items = data ?? [];
    const [searchQuery, setSearchQuery] = useState('');

    const selectedMasterItemIds = useAppSelector(selectMasterItemIds, isEqual);
    const context: {
        selectedItemId: number | null;
        selectedItemIds: number[];
    } = useMemo(
        () => ({ selectedItemId: selectedMasterItemId, selectedItemIds: selectedMasterItemIds }),
        [selectedMasterItemId, selectedMasterItemIds]
    );

    return (
        <Modal open onClose={onClose}>
            <DivRoot>
                <Box
                    sx={{
                        mb: 2,
                        px: 3,
                        pt: 2,
                    }}
                >
                    <H5ModalTitle>
                        {t('inspectionForms.inspectionItemsModal.inspectionItems')}
                    </H5ModalTitle>
                    <H5ModalText>
                        {t('inspectionForms.inspectionItemsModal.listAllInspectionItems')}
                    </H5ModalText>
                    <FilterTextField
                        sx={{ width: 400 }}
                        name={'filter'}
                        isRequired={false}
                        placeholder={t('inspectionForms.inspectionItemsModal.search')}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </Box>

                <IconButton
                    sx={{ position: 'absolute', right: 0, top: 0 }}
                    onClick={() => onClose()}
                >
                    <CloseIcon fill={Colors.Neutral7} />
                </IconButton>

                <Box sx={{ height: 500 }}>
                    <VirtualizedAlphabeticallyGroupedList<MasterItemDto, typeof context>
                        getLabel={getLabel}
                        getKey={getKey}
                        component={MasterItemComponent}
                        options={items}
                        onSelected={onSelected}
                        searchQuery={searchQuery}
                        data={context}
                    />
                </Box>
            </DivRoot>
        </Modal>
    );
}

const DivRoot = styled('div')({
    width: 900,
    margin: 20,
    position: 'relative',
});

const H5ModalTitle = styled('h5')(({ theme }) => ({
    ...theme.typography.h5Inter,
    textAlign: 'left',
    margin: '5px 0',
}));

const H5ModalText = styled('h5')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    textAlign: 'left',
}));

const getLabel = (item: MasterItemDto) => item.name;
const getKey = (item: MasterItemDto) => item.id;

function MasterItemComponent({
    value,
    onSelected,
    data,
}: VirtualizedAlphabeticallyGroupedListItemProps<
    MasterItemDto,
    { selectedItemId: number | null; selectedItemIds: number[] }
>) {
    const { selectedItemId, selectedItemIds } = data!;
    const isSelected = selectedItemId === value.id;
    const cannotBeSelected = selectedItemIds.includes(value.id);

    return (
        <StyledSMenuItem
            onClick={() => {
                onSelected(value);
            }}
            className={clsx(
                isSelected && 'selected',
                cannotBeSelected && !isSelected && 'disabled'
            )}
            disabled={cannotBeSelected && !isSelected}
        >
            {value.name}
        </StyledSMenuItem>
    );
}

const StyledSMenuItem = styled(SMenuItem)({
    height: 40,
    textWrap: 'wrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',

    '&.selected': {
        fontWeight: 'bold',
        color: 'var(--cm1)',
    },

    '&.disabled': {
        opacity: 0.5,
        pointerEvents: 'none',
    },
});
