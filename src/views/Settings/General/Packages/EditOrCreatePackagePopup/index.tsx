import { Box, Divider, styled, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import PackagesSettingsApi, { PackageDto } from 'api/settings/packages';
import { Button } from 'common/components/Button';
import { ButtonProps } from 'common/components/Button/ButtonProps';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { TextFormField } from 'common/components/Inputs';
import { Modal } from 'common/components/Modal';
import CancelModal from 'common/components/Popups/CancelModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import isEqual from 'lodash/isEqual';
import {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import { useStore } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from 'store';
import {
    packagesActions,
    selectBrandsAndModelLimitReached,
    selectPackage,
    selectPackageData,
    selectPackageEditingState,
    selectPackageUpdateData,
} from 'store/slices/Settings/packages';
import { ValidationContextProvider } from 'utils/validation';
import MultipleBrandPicker from 'views/Components/MultipleBrandPicker';
import MultipleModelPicker from 'views/Components/MultipleModelPicker';
import MultipleYearPicker from 'views/Components/MultipleYearPicker';
import { MasterItemPopupProvider } from './MasterItemsPopup';
import PackagesItems from './PackagesItem';

const EditOrCreatePackagePopupContext = createContext<{
    open(packageId?: string): void;
} | null>(null);

export function useEditOrCreatePackagePopup() {
    const ctx = useContext(EditOrCreatePackagePopupContext);
    if (!ctx) throw new Error('EditOrCreatePackagePopupContext is not available');
    return ctx;
}

export function EditOrCreatePackagePopupProvider({
    children,
    onChange,
}: React.PropsWithChildren<{ onChange?: () => void }>) {
    const [open, setOpen] = useState(false);
    const [packageId, setPackageId] = useState<string>();

    const ctx = useMemo(
        () => ({
            open: (packageId?: string) => {
                setPackageId(packageId);
                setOpen(true);
            },
        }),
        []
    );

    return (
        <EditOrCreatePackagePopupContext.Provider value={ctx}>
            <EditOrCreatePackagePopup
                onPackageCreated={() => {
                    setOpen(false);
                    if (onChange) onChange();
                }}
                onPackageUpdated={() => {
                    setOpen(false);
                    if (onChange) onChange();
                }}
                onClose={() => setOpen(false)}
                packageId={packageId}
                open={open}
            />
            {children}
        </EditOrCreatePackagePopupContext.Provider>
    );
}

export type EditOrCreatePackagePopupProps = {
    packageId?: string;
    open: boolean;
    onClose: () => void;
    onPackageCreated?: (package_: PackageDto) => void;
    onPackageUpdated?: (package_: PackageDto) => void;
};

export default function EditOrCreatePackagePopup({
    packageId,
    open,
    onClose,
    onPackageCreated,
    onPackageUpdated,
}: EditOrCreatePackagePopupProps) {
    const { t } = useAppTranslation();

    const dispatch = useAppDispatch();
    const { isNew, isChanged, valid, validMinimal } = useAppSelector(
        selectPackageEditingState,
        isEqual
    );
    const { name, years, brands } = useAppSelector(selectPackage, isEqual);
    const isBrandsLimitReached = useAppSelector(selectBrandsAndModelLimitReached);
    const brandsNames = useMemo(() => brands.map((b) => b.name), [brands]);
    const [showCancelPopup, setShowCancelPopup] = useState(false);

    const setName = useCallback(
        (name: string) => {
            dispatch(packagesActions.setName(name));
        },
        [dispatch]
    );

    const setYears = useCallback(
        (years: string[]) => {
            dispatch(packagesActions.setYears(years));
        },
        [dispatch]
    );

    const setBrands = useCallback(
        (brands: { name: string; models: string[] }[]) => {
            dispatch(packagesActions.setModels(brands));
        },
        [dispatch]
    );

    const setBrandNames = useCallback(
        (brands: string[]) => {
            dispatch(packagesActions.setBrandNames(brands));
        },
        [dispatch]
    );

    const addItem = useCallback(() => {
        dispatch(packagesActions.addItem());
    }, [dispatch]);

    const store = useStore();
    const toasters = useToasters();

    const createPackage = useMutation(async () => {
        const data = selectPackageData(store.getState() as RootState);
        const package_ = await PackagesSettingsApi.create(data);
        toasters.success(null, t('settings.packages.editOrCreate.createdNotif'));
        if (onPackageCreated) onPackageCreated(package_);
    });

    const updatePackage = useMutation(async () => {
        if (!packageId) throw new Error('cannot update package without id');
        const data = selectPackageUpdateData(store.getState() as RootState);
        const package_ = await PackagesSettingsApi.update(packageId, data);
        toasters.success(null, t('settings.packages.editOrCreate.updatedNotif'));
        if (onPackageUpdated) onPackageUpdated(package_);
    });

    const validationContextRef = useRef<ValidationContextProvider | null>(null);
    const [wasValidated, setWasValidated] = useState(false);
    const saveIsDisabled = !(wasValidated ? valid : validMinimal) || (isNew ? false : !isChanged);

    const handleSaveButton = async () => {
        if (saveIsDisabled) return;
        if (updatePackage.isLoading || createPackage.isLoading) return;

        if (validationContextRef.current) {
            validationContextRef.current.resetAll();
            const valid = await new Promise<boolean>((resolve) => {
                // perform validation on next frame to let the components update
                window.requestAnimationFrame(() => {
                    try {
                        const result = validationContextRef.current!.validateAll();
                        resolve(result.valid !== false);
                    } catch (e: unknown) {
                        console.error('Failed to validate EditOrCreatePackagePopup inputs');
                        resolve(false);
                    }
                });
            });
            setWasValidated(true);
            if (!valid) {
                return;
            }
        } else {
            return;
        }

        if (!valid) return;

        if (packageId) {
            updatePackage.mutate();
        } else {
            createPackage.mutate();
        }
    };

    const handleCloseClick = () => {
        if (isChanged) {
            setShowCancelPopup(true);
        } else {
            onClose();
        }
    };

    const packageQuery = useQuery(['settings', 'package', packageId], {
        enabled: !!packageId && open,
        queryFn: async () => {
            if (!packageId) throw new Error('packageId is not defined');
            const p = await PackagesSettingsApi.getPackage(packageId);
            return p;
        },
        cacheTime: 60000 * 10,
    });

    useEffect(() => {
        if (packageQuery.data && open) {
            dispatch(packagesActions.setPackage(packageQuery.data));
        }
    }, [dispatch, packageQuery.data, open]);

    const formDisabled = packageId ? packageQuery.isFetching || !packageQuery.data : false;

    return (
        <Modal
            boxComponent={ModalBoxComponent}
            open={open}
            onClose={handleCloseClick}
            onTransitionExited={() => {
                dispatch(packagesActions.resetEditingState());
                setWasValidated(false);
            }}
            data-test-id="edit-or-create-package-modal"
        >
            <CancelModal
                title={
                    isNew
                        ? t('settings.packages.editOrCreate.cancelPackageCreation')
                        : t('settings.packages.editOrCreate.cancelPackageEditing')
                }
                onClose={() => setShowCancelPopup(false)}
                onCancel={() => {
                    onClose();
                    setShowCancelPopup(false);
                }}
                open={showCancelPopup}
            />

            <ValidationContextProvider ref={validationContextRef}>
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        position: 'sticky',
                        top: '0px',
                        backgroundColor: '#fff',
                        zIndex: 3,
                        paddingTop: '30px',
                        paddingBottom: '10px',
                    }}
                >
                    <Typography variant="h4Inter" color="neutral.7">
                        {packageId
                            ? t('settings.packages.editOrCreate.editPackage')
                            : t('settings.packages.editOrCreate.createPackage')}
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                            w="md"
                            color={Colors.CM3}
                            onClick={handleCloseClick}
                            data-test-id="cancel-button"
                        >
                            {t('commonLabels.cancel')}
                        </Button>
                        <SaveButton
                            isNew={isNew}
                            onClick={handleSaveButton}
                            showLoader={createPackage.isLoading || updatePackage.isLoading}
                            disabled={saveIsDisabled}
                        />
                    </Box>
                </Box>

                <MainGrid>
                    <TextFormField
                        isRequired
                        slotProps={{ inputWrapper: { shakeOnInvalid: true } }}
                        showValidationIndicators
                        label={t('settings.packages.editOrCreate.packageName')}
                        placeholder={t('settings.packages.editOrCreate.packageNamePlaceholder')}
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        disabled={formDisabled}
                        cmosVariant="grey"
                        dataTestId="package-name"
                    />
                    <MultipleBrandPicker
                        disableSelecting={isBrandsLimitReached}
                        disabled={formDisabled}
                        value={brandsNames}
                        onChange={setBrandNames}
                        dataTestId="brand"
                    />
                    <MultipleModelPicker
                        disableSelecting={isBrandsLimitReached}
                        filterBrands={brandsNames}
                        value={brands}
                        onChange={setBrands}
                        disabled={formDisabled}
                        dateTestId="model"
                    />
                    <MultipleYearPicker
                        disabled={formDisabled}
                        value={years}
                        onChange={setYears}
                        dataTestId="years"
                    />
                </MainGrid>

                <Divider sx={{ mt: 2 }} />

                <MasterItemPopupProvider>
                    <PackagesItems disabled={formDisabled} />
                </MasterItemPopupProvider>
            </ValidationContextProvider>

            <Box sx={{ display: 'flex', justifyContent: 'end', mt: 2 }}>
                <Button
                    data-test-id="add-package-item-button"
                    onClick={addItem}
                    cmosVariant="typography"
                >
                    <PlusIcon /> {t('settings.packages.editOrCreate.addPackageItem')}
                </Button>
            </Box>
        </Modal>
    );
}

const ModalBoxComponent = styled('div')({
    padding: '0 20px 30px 20px',
    width: 1250,

    '@media (min-width: 1400px)': {
        padding: '0 30px 30px 30px',
        width: 1270,
    },
    minWidth: 980,
    maxHeight: '100vh',
    ...scrollbarStyle({ size: 12 }),
    overflowY: 'auto',
});

const MainGrid = styled('div')({
    display: 'grid',
    gridTemplateColumns: '3fr 2fr 2fr 2fr',
    columnGap: '10px',
});

function SaveButton({
    onClick,
    isNew,
    showLoader,
    disabled,
}: {
    onClick: ButtonProps['onClick'];
    isNew: boolean;
    disabled: boolean;
    showLoader: boolean;
}) {
    const { t } = useAppTranslation();

    return (
        <Button
            w="md"
            color={Colors.Success}
            disabled={disabled}
            onClick={onClick}
            showLoader={showLoader}
            data-test-id="save-button"
        >
            {!isNew ? t('commonLabels.saveChanges') : t('commonLabels.save')}
        </Button>
    );
}
