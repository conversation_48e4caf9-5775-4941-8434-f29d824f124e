import { ExpandLess, ExpandMore } from '@mui/icons-material';
import { autocompleteClasses, Box, InputAdornment, ListItemButton } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import MasterItemAPI, { MasterItemDto } from 'api/MasterItem';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { TextField } from 'common/components/mui';
import SAutocomplete from 'common/components/mui/SAutocomplete';
import { normalizeAccent } from 'common/Helpers';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import {
    createContext,
    forwardRef,
    HTMLAttributes,
    startTransition,
    useContext,
    useMemo,
    useState,
} from 'react';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import { createSelector } from 'reselect';
import { useAppSelector } from 'store';
import { selectPackageItems } from 'store/slices/Settings/packages';
import { useMasterItemPopupContext } from './MasterItemsPopup';

export type MasterItemSelectProps = {
    masterItemId: number | null;
    onChange: (materItem: MasterItemDto) => void;
    fullWidth?: boolean;
    placeholder?: string;
    disabled?: boolean;
    packageItemKey: string;
    dataTestId?: string;
    isInvalid?: boolean;
};

const AutocompleteItemContext = createContext<{
    itemKey: string;
    setOpen: (open: boolean) => void;
} | null>(null);

const selectMasterItemIds = createSelector(
    selectPackageItems,
    (items) => items.map((x) => x.masterItemId).filter((x) => !!x) as number[]
);

export default function MasterItemSelect({
    masterItemId,
    onChange,
    fullWidth,
    placeholder,
    disabled,
    packageItemKey,
    dataTestId,
    isInvalid,
}: MasterItemSelectProps) {
    const { data, isLoading } = useQuery(['master-items'], () => MasterItemAPI.getAll(), {
        staleTime: 1000,
        cacheTime: Infinity,
    });

    const selectedMasterItemIds = useAppSelector(selectMasterItemIds, isEqual);

    const [open, setOpen] = useState(false);

    const selectedItem = useMemo(() => {
        if (!data) return null;
        return data.find((x) => x.id === masterItemId) ?? null;
    }, [masterItemId, data]);

    const items = useMemo(() => {
        if (!data) return [];
        if (selectedMasterItemIds.length === 0) return data;
        return data.filter((x) => x.id === masterItemId || !selectedMasterItemIds.includes(x.id));
    }, [selectedMasterItemIds, data, masterItemId]);

    const itemContext = useMemo(
        () => ({
            itemKey: packageItemKey,
            setOpen,
        }),
        [packageItemKey]
    );

    return (
        <AutocompleteItemContext.Provider value={itemContext}>
            <SAutocomplete<MasterItemDto>
                ListboxComponent={VirtualizedListboxComponent}
                disableListWrap
                open={open}
                onOpen={() => setOpen(true)}
                onClose={() => setOpen(false)}
                handleHomeEndKeys
                fullWidth={fullWidth}
                sx={{
                    [`& .${autocompleteClasses.input}`]: {
                        padding: '1px 8px !important',
                    },
                }}
                renderOption={(props, item, state) => ({ props, item } as never as React.ReactNode)}
                getOptionLabel={(option) => option.name}
                renderTags={(value) => value.map((x) => x.name).join(', ')}
                options={items}
                filterOptions={(options, state) => {
                    const normalizedQuery = normalizeAccent(state.inputValue.trim());
                    if (normalizedQuery === '') return options;
                    const results = options.filter(
                        (x) => x && normalizeAccent(x.name).includes(normalizedQuery)
                    );

                    return results;
                }}
                onChange={(_event, value) => {
                    onChange(value);
                }}
                value={selectedItem}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        placeholder={placeholder}
                        cmosVariant="grey"
                        data-test-id={dataTestId}
                        InputProps={{
                            ...params.InputProps,
                            error: isInvalid,
                            endAdornment: (
                                <InputAdornment sx={{ color: 'var(--neutral9)' }} position="end">
                                    {open ? <ExpandLess /> : <ExpandMore />}
                                </InputAdornment>
                            ),
                        }}
                    />
                )}
                disabled={isLoading || disabled}
            />
        </AutocompleteItemContext.Provider>
    );
}

const OuterElementContext = createContext<HTMLAttributes<HTMLDivElement>>({});

const OuterElementType = forwardRef<HTMLDivElement>((props, ref) => {
    const { className, ...outerProps } = useContext(OuterElementContext);

    return (
        <div ref={ref} {...props} {...outerProps} className={clsx(className, 'custom-scrollbar')} />
    );
});

type ListboxComponentProps = React.HTMLAttributes<HTMLElement>;
type VirtualListData = { props: HTMLAttributes<HTMLLIElement>; item: MasterItemDto }[];

const ITEM_HEIGHT = 40;

const VirtualizedListboxComponent = forwardRef<HTMLDivElement, ListboxComponentProps>(
    ({ children, role, ...other }, ref) => {
        const { t } = useAppTranslation();
        const items = children as never as VirtualListData;
        const height = Math.max(50, Math.min(items.length, 5.5) * ITEM_HEIGHT);

        const itemCtx = useContext(AutocompleteItemContext);
        if (itemCtx === null) throw new Error('AutocompleteItemContext is not available');
        const { itemKey, setOpen } = itemCtx;
        const masterItemPopup = useMasterItemPopupContext();

        return (
            <div ref={ref} style={{ padding: '0 0 10px 0' }}>
                <Box
                    onMouseDown={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                    }}
                    sx={{ padding: '5px 30px' }}
                >
                    <Button
                        sx={{ width: '100%' }}
                        onClick={() => {
                            // this closes the autocomplete and blurs the currently focused
                            // element (which should be the input of the Autocomplete)
                            // if we don't blur it, it will get focus back after modal is closed and autocomplete will be opened again
                            setOpen(false);
                            if (document.activeElement instanceof HTMLElement) {
                                document.activeElement.blur();
                            }
                            startTransition(() => {
                                masterItemPopup.open(itemKey);
                            });
                        }}
                    >
                        {t('inspectionForms.addItem.seeAll')}
                    </Button>
                </Box>
                <OuterElementContext.Provider value={other}>
                    <FixedSizeList
                        itemSize={ITEM_HEIGHT}
                        width="100%"
                        height={height}
                        itemCount={items.length}
                        overscanCount={5}
                        itemData={items}
                        outerElementType={OuterElementType}
                    >
                        {InspectionItemVirtualizedRenderer}
                    </FixedSizeList>
                </OuterElementContext.Provider>
            </div>
        );
    }
);

function InspectionItemVirtualizedRenderer({
    style,
    index,
    data,
}: ListChildComponentProps<VirtualListData>) {
    const { props, item } = data[index];

    return (
        <div style={style}>
            <ListItemButton
                sx={{ height: ITEM_HEIGHT, borderBottom: '1px solid var(--neutral3)' }}
                role="button"
                component="li"
                {...props}
            >
                {item.name}
            </ListItemButton>
        </div>
    );
}
