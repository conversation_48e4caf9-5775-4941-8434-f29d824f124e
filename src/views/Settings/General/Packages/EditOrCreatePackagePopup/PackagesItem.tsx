import { Box, Collapse, IconButton, styled } from '@mui/material';
import { MasterItemDto } from 'api/MasterItem';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { TextFormField } from 'common/components/Inputs';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { NumberFormField } from 'common/components/Inputs/NumberField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { useCallback } from 'react';
import { TransitionGroup } from 'react-transition-group';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    packagesActions,
    selectPackageItem,
    selectPackageItemsKeys,
    selectReachedMaxItems,
} from 'store/slices/Settings/packages';
import { useDebounce } from 'use-debounce';
import { useValidation, useValidationContextInput } from 'utils/validation';
import { useLastTruthyValue } from '../utils';
import MasterItemSelect, { MasterItemSelectProps } from './MasterItemSelect';
import PackageItemEstimate from './PackageItemEstimate';

type PackagesItemsProps = {
    disabled: boolean;
};

export default function PackagesItems({ disabled }: PackagesItemsProps) {
    const itemKeys = useAppSelector(selectPackageItemsKeys, isEqual);
    const [disabledDebounced] = useDebounce(disabled, 150);
    const disableAnimation = disabled ? true : disabledDebounced;

    return (
        <TransitionGroup exit={!disableAnimation} enter={!disableAnimation} appear={true}>
            {itemKeys.map((key, idx) => (
                <Collapse key={idx} timeout={150}>
                    <PackageItem itemKey={key} disabled={disabled} allowRemove={idx !== 0} />
                </Collapse>
            ))}
        </TransitionGroup>
    );
}

function PackageItem({
    disabled,
    itemKey,
    allowRemove,
}: {
    disabled: boolean;
    itemKey: string;
    allowRemove: boolean;
}) {
    if (!itemKey) throw new Error('itemKey is required');

    const { t } = useAppTranslation();
    const gs = useAppSelector(selectSettings);
    // because of TransitionGroup the selector might return undefined while component is mounted
    // so keep last truthy (not undefined) value intact
    const itemData = useLastTruthyValue(
        useAppSelector((r) => selectPackageItem(r, itemKey), isEqual)
    );
    if (itemData === undefined) throw new Error(`item with key ${itemKey} does not exist`);
    const { item, index } = itemData;
    const reachedLimit = useAppSelector(selectReachedMaxItems);
    const dispatch = useAppDispatch();

    const removeItem = useCallback(() => {
        dispatch(packagesActions.removeItem({ itemKey }));
    }, [dispatch, itemKey]);

    const addEstimate = useCallback(() => {
        if (reachedLimit) return;
        dispatch(packagesActions.addEstimateToItem({ key: itemKey }));
    }, [dispatch, itemKey, reachedLimit]);

    const handleMasterItem = useCallback(
        (item: MasterItemDto) => {
            dispatch(
                packagesActions.updateItem({
                    itemKey,
                    masterItemId: item.id,
                })
            );
        },
        [dispatch, itemKey]
    );

    const [disabledDebounced] = useDebounce(disabled, 150);
    const disableAnimation = disabled ? true : disabledDebounced;

    return (
        <>
            <DivPackageItemMain data-test-id="package-item">
                <DivPackageItemLeft>
                    <Box
                        sx={{ display: 'flex', justifyContent: 'end', pr: 1, alignItems: 'center' }}
                    >
                        {item.estimateKeys.length === 0 && (
                            <IconButton
                                disabled={disabled || reachedLimit}
                                onClick={addEstimate}
                                sx={{ width: 24, height: 24, mt: 3.25 }}
                                size="small"
                                data-test-id="add-estimate"
                            >
                                <PlusIcon style={{ opacity: disabled || reachedLimit ? 0.5 : 1 }} />
                            </IconButton>
                        )}

                        {allowRemove && (
                            <IconButton
                                disabled={disabled}
                                onClick={removeItem}
                                sx={{ width: 24, height: 24, mt: 3.25 }}
                                size="small"
                                data-test-id="remove-estimate"
                            >
                                <LessIcon />
                            </IconButton>
                        )}
                    </Box>
                    <MasterItemFormSelect
                        disabled={disabled}
                        index={index}
                        masterItemId={item.masterItemId ?? null}
                        packageItemKey={itemKey}
                        onChange={handleMasterItem}
                    />
                </DivPackageItemLeft>

                <DivPackageItemRight>
                    <TextFormField
                        disabled={disabled}
                        label={t('settings.packages.editOrCreate.partsNumber')}
                        placeholder={t('settings.packages.editOrCreate.partsNumberPlaceholder')}
                        value={item.partNumber}
                        onChange={(e) =>
                            dispatch(
                                packagesActions.updateItem({ itemKey, partNumber: e.target.value })
                            )
                        }
                        cmosVariant="grey"
                        dataTestId="part-number-input"
                    />

                    <NumberFormField
                        disabled={disabled}
                        label={t('settings.packages.editOrCreate.quantity')}
                        placeholder={t('settings.packages.editOrCreate.quantityPlaceholder')}
                        value={item.quantity}
                        onValueChange={(v) =>
                            dispatch(
                                packagesActions.updateItem({ itemKey, quantity: v.floatValue })
                            )
                        }
                        decimalScale={2}
                        fixedDecimalScale
                        cmosVariant="grey"
                        dataTestId="quantity-input"
                    />

                    <NumberFormField
                        disabled={disabled}
                        label={t('settings.packages.editOrCreate.price')}
                        placeholder={t('settings.packages.editOrCreate.pricePlaceholder')}
                        value={item.price}
                        onValueChange={(v) =>
                            dispatch(packagesActions.updateItem({ itemKey, price: v.floatValue }))
                        }
                        decimalScale={2}
                        fixedDecimalScale
                        template={gs.internationalization.currency}
                        cmosVariant="grey"
                        dataTestId="price-input"
                    />

                    <NumberFormField
                        disabled={disabled}
                        label={t('settings.packages.editOrCreate.hours')}
                        placeholder={t('settings.packages.editOrCreate.hoursPlaceholder')}
                        value={item.hours}
                        onValueChange={(v) =>
                            dispatch(packagesActions.updateItem({ itemKey, hours: v.floatValue }))
                        }
                        decimalScale={2}
                        fixedDecimalScale
                        cmosVariant="grey"
                        dataTestId="hours-input"
                    />

                    <NumberFormField
                        disabled={disabled}
                        label={t('settings.packages.editOrCreate.hourPrice')}
                        placeholder={t('settings.packages.editOrCreate.hourPricePlaceholder')}
                        value={item.hourPrice}
                        onValueChange={(v) =>
                            dispatch(
                                packagesActions.updateItem({ itemKey, hourPrice: v.floatValue })
                            )
                        }
                        decimalScale={2}
                        fixedDecimalScale
                        template={gs.internationalization.currency}
                        cmosVariant="grey"
                        dataTestId="hourPrice-input"
                    />
                </DivPackageItemRight>
            </DivPackageItemMain>
            <TransitionGroup enter={!disableAnimation} exit={!disableAnimation} appear={false}>
                {item.estimateKeys.map((key) => (
                    <Collapse key={key} timeout={150}>
                        <PackageItemEstimate
                            key={key}
                            disabled={disabled}
                            estimateKey={key}
                            itemKey={itemKey}
                        />
                    </Collapse>
                ))}
            </TransitionGroup>
        </>
    );
}

const DivPackageItemMain = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr 2fr',
    columnGap: '10px',
    paddingBottom: 8,
    marginTop: 16,
});

const DivPackageItemLeft = styled('div')({
    display: 'grid',
    gridTemplateColumns: '57px 1fr',
    marginRight: 4,
});

const DivPackageItemRight = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr 1fr 1fr',
    columnGap: 10,
});

function MasterItemFormSelect({
    disabled,
    masterItemId,
    index,
    packageItemKey,
    onChange,
}: {
    disabled: boolean;
    index: number;
    masterItemId: number | null;
    packageItemKey: string;
    onChange: MasterItemSelectProps['onChange'];
}) {
    const { t } = useAppTranslation();

    const { valid, validate, reset } = useValidation(masterItemId, (x) => !!x);

    useValidationContextInput({
        onValidate: validate,
        onReset: reset,
    });

    return (
        <InputWrapper
            disabled={disabled}
            isRequired
            shakeOnInvalid
            isInvalid={valid === false}
            showValidationIndicators={index === 0}
            label={t('settings.packages.editOrCreate.inspectionItemLabel')}
        >
            <MasterItemSelect
                isInvalid={valid === false}
                disabled={disabled}
                packageItemKey={packageItemKey}
                fullWidth
                masterItemId={masterItemId}
                onChange={onChange}
                placeholder={t('settings.packages.editOrCreate.inspectionItemPlaceholder')}
                dataTestId="inspection-item-select"
            />
        </InputWrapper>
    );
}
