import { Box, IconButton, styled } from '@mui/material';
import { LessIcon } from 'common/components/Icons/LessIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { TextFormField } from 'common/components/Inputs';
import { NumberFormField } from 'common/components/Inputs/NumberField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import isEqual from 'lodash/isEqual';
import { memo, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    packagesActions,
    selectPackageItemEstimate,
    selectReachedMaxItems,
} from 'store/slices/Settings/packages';
import { useLastTruthyValue } from '../utils';

type PackageItemEstimateProps = {
    disabled: boolean;
    itemKey: string;
    estimateKey: string;
};

const PackageItemEstimate = memo(({ itemKey, disabled, estimateKey }: PackageItemEstimateProps) => {
    if (!estimateKey) throw new Error('estimateKey is required');

    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const gs = useAppSelector(selectSettings);
    const estimate = useLastTruthyValue(
        useAppSelector((r) => selectPackageItemEstimate(r, [itemKey, estimateKey]), isEqual)
    );
    if (estimate === undefined) throw new Error(`estimate ${itemKey}>${estimateKey} is undefined`);
    const reachedLimit = useAppSelector(selectReachedMaxItems);

    const addEstimateAfterCurrentOne = useCallback(() => {
        if (reachedLimit) return;
        dispatch(
            packagesActions.addEstimateToItemAfter({
                estimateKey,
                itemKey,
            })
        );
    }, [dispatch, estimateKey, itemKey, reachedLimit]);

    const removeEstimate = useCallback(() => {
        dispatch(
            packagesActions.removeEstimateFromItem({
                estimateKey,
                itemKey,
            })
        );
    }, [dispatch, estimateKey, itemKey]);

    return (
        <DivEstimateRoot data-test-id="package-estimate">
            <DivEstimateLeft>
                <Box sx={{ mt: '26px', display: 'flex' }}>
                    <IconButton
                        disabled={disabled || reachedLimit}
                        sx={{ width: 24, height: 24 }}
                        onClick={addEstimateAfterCurrentOne}
                        data-test-id="add-estimate-after-button"
                    >
                        <PlusIcon style={{ opacity: disabled || reachedLimit ? 0.5 : 1 }} />
                    </IconButton>

                    <IconButton sx={{ width: 24, height: 24 }} onClick={removeEstimate}>
                        <LessIcon />
                    </IconButton>
                </Box>

                <TextFormField
                    isRequired
                    slotProps={{ inputWrapper: { shakeOnInvalid: true } }}
                    disabled={disabled}
                    value={estimate.description}
                    onChange={(e) =>
                        dispatch(
                            packagesActions.updateEstimate({
                                estimateKey,
                                itemKey,
                                description: e.target.value,
                            })
                        )
                    }
                    label={t('settings.packages.editOrCreate.description')}
                    placeholder={t('settings.packages.editOrCreate.subItemDescriptionPlaceholder')}
                    cmosVariant="grey"
                    dataTestId="estimate-description-input"
                />
            </DivEstimateLeft>

            <DivEstimateRight>
                <TextFormField
                    disabled={disabled}
                    label={t('settings.packages.editOrCreate.partsNumber')}
                    placeholder={t('settings.packages.editOrCreate.partsNumberPlaceholder')}
                    value={estimate.partNumber}
                    onChange={(e) =>
                        dispatch(
                            packagesActions.updateEstimate({
                                estimateKey,
                                itemKey,
                                partNumber: e.target.value,
                            })
                        )
                    }
                    cmosVariant="grey"
                    dataTestId="estimate-part-number-input"
                />

                <NumberFormField
                    disabled={disabled}
                    label={t('settings.packages.editOrCreate.quantity')}
                    placeholder={t('settings.packages.editOrCreate.quantityPlaceholder')}
                    value={estimate.quantity}
                    onValueChange={(v) =>
                        dispatch(
                            packagesActions.updateEstimate({
                                estimateKey,
                                itemKey,
                                quantity: v.floatValue,
                            })
                        )
                    }
                    decimalScale={2}
                    fixedDecimalScale
                    cmosVariant="grey"
                    dataTestId="estimate-quantity-input"
                />

                <NumberFormField
                    disabled={disabled}
                    label={t('settings.packages.editOrCreate.price')}
                    placeholder={t('settings.packages.editOrCreate.pricePlaceholder')}
                    value={estimate.price}
                    onValueChange={(v) =>
                        dispatch(
                            packagesActions.updateEstimate({
                                estimateKey,
                                itemKey,
                                price: v.floatValue,
                            })
                        )
                    }
                    decimalScale={2}
                    fixedDecimalScale
                    template={gs.internationalization.currency}
                    cmosVariant="grey"
                    dataTestId="estimate-price-input"
                />

                <NumberFormField
                    disabled={disabled}
                    label={t('settings.packages.editOrCreate.hours')}
                    placeholder={t('settings.packages.editOrCreate.hoursPlaceholder')}
                    value={estimate.hours}
                    onValueChange={(v) =>
                        dispatch(
                            packagesActions.updateEstimate({
                                estimateKey,
                                itemKey,
                                hours: v.floatValue,
                            })
                        )
                    }
                    decimalScale={2}
                    fixedDecimalScale
                    cmosVariant="grey"
                    dataTestId="estimate-hours-input"
                />

                <NumberFormField
                    disabled={disabled}
                    label={t('settings.packages.editOrCreate.hourPrice')}
                    placeholder={t('settings.packages.editOrCreate.hourPricePlaceholder')}
                    value={estimate.hourPrice}
                    onValueChange={(v) =>
                        dispatch(
                            packagesActions.updateEstimate({
                                estimateKey,
                                itemKey,
                                hourPrice: v.floatValue,
                            })
                        )
                    }
                    decimalScale={2}
                    fixedDecimalScale
                    template={gs.internationalization.currency}
                    cmosVariant="grey"
                    dataTestId="estimate-hour-price-input"
                />
            </DivEstimateRight>
        </DivEstimateRoot>
    );
});

export default PackageItemEstimate;

const DivEstimateRoot = styled('div')({
    display: 'grid',
    columnGap: '10px',
    gridTemplateColumns: '1fr 2fr',

    ':last-child': {
        marginBottom: '10px',
    },
});

const DivEstimateLeft = styled('div')({
    paddingLeft: '24px',
    paddingRight: '4px',
    display: 'flex',
    gap: '8px',
    alignItems: 'center',
});

const DivEstimateRight = styled('div')({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr 1fr 1fr',
    columnGap: '10px',
});
