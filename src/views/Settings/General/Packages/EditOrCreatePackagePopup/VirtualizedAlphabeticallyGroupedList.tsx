import { styled } from '@mui/material';
import { normalizeAccent } from 'common/Helpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import groupBy from 'lodash/groupBy';
import { useMemo } from 'react';
import { GroupedVirtuoso } from 'react-virtuoso';
import { batch } from 'utils/collection';
import ErrorBoundary from 'utils/errorsHandling/ErrorBoundary';

export type VirtualizedAlphabeticallyGroupedListItemProps<T, Data> = {
    value: T;
    onSelected: (value: T) => void;
    data: Data | undefined;
};

/**
 * Props for {@link VirtualizedAlphabeticallyGroupedList}.
 *
 * @template T - The type of the items in the list.
 * @template Data - The type of the additional data passed to the component.
 */
export type VirtualizedAlphabeticallyGroupedListProps<T, Data> = {
    /**
     * A function that returns the label for an item.
     *
     * @param {T} value - The item.
     * @return {string} The label for the item.
     */
    getLabel: (value: T) => string;
    /**
     * A function that returns the key for an item.
     *
     * @param {T} value - The item.
     * @return {React.Key} The key for the item.
     */
    getKey: (value: T) => React.Key;
    /**
     * The component to render for each item.
     *
     * @template Data - The type of the additional data passed to the component.
     */
    component: React.ComponentType<VirtualizedAlphabeticallyGroupedListItemProps<T, Data>>;
    /**
     * The options for the list.
     */
    options: T[];
    /**
     * The function to call when an item is selected.
     *
     * @param {T} item - The selected item.
     * @return {void}
     */
    onSelected: (item: T) => void;
    /**
     * The search query to filter the list.
     *
     * @type {string}
     */
    searchQuery?: string;
    /**
     * The additional data to pass to the component.
     */
    data?: Data;
};

/**
 * Renders a virtuoso list of alphabetically grouped items.
 *
 * @template T - The type of the items in the list.
 * @template Data - The type of the additional data passed to the component.
 */
export default function VirtualizedAlphabeticallyGroupedList<T, Data = undefined>({
    options,
    getLabel,
    getKey,
    component,
    onSelected,
    searchQuery = '',
    data,
}: VirtualizedAlphabeticallyGroupedListProps<T, Data>) {
    const hasSearchQuery = searchQuery.trim() !== '';

    const searchResults = useMemo(() => {
        const normalizedQuery = normalizeAccent(searchQuery.trim());
        if (normalizedQuery === '') return options;
        return options.filter((x) => normalizeAccent(getLabel(x)).includes(normalizedQuery));
    }, [searchQuery, options, getLabel]);

    const { groups, items } = useMemo(() => {
        const groupedItems = Object.entries(
            groupBy(searchResults, (x) => {
                const label = getLabel(x);
                if (label === '') {
                    return '?';
                }
                const firstLetter = normalizeAccent(label.charAt(0)).toUpperCase();
                if (firstLetter === ' ') {
                    return '?';
                }

                if (/[0-9]/.test(firstLetter)) {
                    return '0-9';
                }

                if (!/[a-zA-Z]/.test(firstLetter)) {
                    return '&';
                }

                return firstLetter;
            })
        ).map(([letter, items]) => ({ letter, items }));

        groupedItems.sort((a, b) => {
            return a.letter.localeCompare(b.letter);
        });

        const batchSize = hasSearchQuery ? 1 : 4;
        const groups = groupedItems.map((x) => ({
            letter: x.letter,
            items: batch(x.items, batchSize),
        }));

        return {
            groups: groups.map((x) => ({
                letter: x.letter,
                count: x.items.length,
            })),
            items: groups.flatMap((x) => x.items),
        };
    }, [searchResults, hasSearchQuery, getLabel]);

    const context: Context<T, Data> = {
        groups,
        items,
        hasSearchQuery,
        onSelected,
        searchQuery,
        component,
        getKey,
        data,
    };

    return (
        <ErrorBoundary renderError={() => <span>Something went wrong</span>}>
            <StyledGroupedVirtuoso<void, Context<T, Data>>
                groupContent={renderGroupContent}
                itemContent={renderItemContent}
                groupCounts={groups.map((x) => x.count)}
                context={context}
            />
        </ErrorBoundary>
    );
}
const StyledGroupedVirtuoso = styled(GroupedVirtuoso)({
    ...scrollbarStyle(),
}) as typeof GroupedVirtuoso;

type Context<T, Data> = {
    groups: {
        letter: string;
        count: number;
    }[];
    items: T[][];
    hasSearchQuery: boolean;
    onSelected: (item: T) => void;
    searchQuery: string;
    component: React.ComponentType<VirtualizedAlphabeticallyGroupedListItemProps<T, Data>>;
    getKey: (item: T) => React.Key;
    data: Data | undefined;
};

function renderGroupContent<T, Data>(index: number, context: Context<T, Data>): React.ReactNode {
    const group = context.groups[index];

    return <StyledH6>{group.letter}</StyledH6>;
}

const StyledH6 = styled('h6')(({ theme }) => ({
    borderBottom: `solid 1px ${theme.palette.neutral[4]}`,
    ...theme.typography.h5Inter,
    padding: '8px 16px',
    margin: 0,
    backgroundColor: theme.palette.neutral[1],
    color: theme.palette.neutral[8],
}));

function renderItemContent<T, Data>(
    index: number,
    groupIndex: number,
    _data: void,
    { onSelected, items, hasSearchQuery, component: Component, getKey, data }: Context<T, Data>
): React.ReactNode {
    const rowItems = items[index];

    if (rowItems.length === 1 && hasSearchQuery) {
        return (
            <Component
                key={`${index}_item_${getKey(rowItems[0])}`}
                value={rowItems[0]}
                onSelected={onSelected}
                data={data}
            />
        );
    }

    return (
        <StyledRowContainer
            key={`${index}_group__${groupIndex}`}
            data-index={index}
            data-group-index={groupIndex}
        >
            {rowItems.map((item) => (
                <Component key={getKey(item)} value={item} onSelected={onSelected} data={data} />
            ))}
        </StyledRowContainer>
    );
}

const StyledRowContainer = styled('ul')({
    margin: 0,
    padding: 0,
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 25%)',

    '&.hasSearchQuery': {
        gridTemplateColumns: '100%',
    },
});
