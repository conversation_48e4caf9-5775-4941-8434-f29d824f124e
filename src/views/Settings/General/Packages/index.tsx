import {
    Box,
    IconButton,
    InputAdornment,
    Pagination,
    styled,
    Switch,
    Table,
    TableBody,
    TableHead,
    TableRow,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import PackagesSettingsApi from 'api/settings/packages';
import { TableHeadCell } from 'common/components';
import AreaSpinner from 'common/components/AreaSpinner';
import { Button } from 'common/components/Button';
import { SearchIcon } from 'common/components/Icons/SearchIcon';
import { TextFormField } from 'common/components/Inputs';
import Instruction from 'common/components/Instruction';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import debounce from 'lodash/debounce';
import { useEffect, useMemo, useState } from 'react';
import { useAppDispatch } from 'store';
import { loadGlobalSettingsThunk } from 'store/slices/globalSettingsSlice';
import PageContentWithFooter from 'views/Components/Page/PageWithFooter';
import { DeletePackagePopupProvider } from './DeletePackagePopup';
import {
    EditOrCreatePackagePopupProvider,
    useEditOrCreatePackagePopup,
} from './EditOrCreatePackagePopup';
import ImportPackageModalProvider, { useImportPackageModal } from './ImportPackageModal';
import RowPackages from './RowPackages';

export default function Packages() {
    const { t } = useAppTranslation();

    const [pageIndex, setPageIndex] = useState(1);

    const [searchInput, setSearchInput] = useState('');
    const [searchField, setSearchField] = useState('');
    const [searchAll, setSearchAll] = useState(false);

    const [arePackagesActivatedState, setArePackagesActivatedState] = useState<boolean | null>(
        null
    );

    const {
        packages,
        arePackagesActivated,
        hasAnyPackage,
        pagesCount,
        isLoading,
        refetch,
        activatePackages,
    } = usePackages(pageIndex, searchField, searchAll);

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.packages')}`);

    const handleTogglePackages = () => {
        activatePackages({ arePackagesActivated: !arePackagesActivated });
    };

    const handleSearchInput = (input: string) => {
        if (input.length >= 3) {
            changeSearchParams(input);
        } else if (input.length === 0) {
            changeSearchParams('');
        }

        setSearchInput(input);
    };

    const initiateSearch = () => {
        if (searchField !== searchInput) {
            changeSearchParams(searchInput);
        }
    };

    const changeSearchParams = (newSearchField: string) => {
        setSearchField(newSearchField);
        if (t('settings.packages.table.all').toUpperCase().includes(newSearchField.toUpperCase())) {
            setSearchAll(true);
        } else {
            setSearchAll(false);
        }
        setPageIndex(1);
    };

    const changePageHandler = (_: unknown, page: number) => {
        setPageIndex(page);
    };

    // use callback, when there are changes in packages
    const onChangeHandler = () => {
        refetch();
    };

    useEffect(() => {
        if (!isLoading) setArePackagesActivatedState(arePackagesActivated);
    }, [isLoading, arePackagesActivated]);

    return (
        <SPageContentWithFooter
            footer={
                <PaginationContainer>
                    <Pagination
                        count={pagesCount > 0 ? pagesCount : 1}
                        page={pageIndex}
                        onChange={changePageHandler}
                        color="primary"
                        size={'small'}
                        disabled={!arePackagesActivatedState}
                    />
                </PaginationContainer>
            }
        >
            <EditOrCreatePackagePopupProvider onChange={onChangeHandler}>
                <DeletePackagePopupProvider onChange={onChangeHandler}>
                    <ImportPackageModalProvider
                        onImported={() => {
                            onChangeHandler();
                            setPageIndex(1);
                        }}
                    >
                        {arePackagesActivatedState !== null && (
                            <>
                                <HeaderBox>
                                    <LeftHeaderBox>
                                        <SSwitch
                                            checked={arePackagesActivatedState ?? false}
                                            onChange={() => {
                                                handleTogglePackages();
                                            }}
                                            name="checkedA"
                                        />
                                        <SwitchTextBox>
                                            <SwitchTitleBox
                                                opaque={arePackagesActivatedState ?? false}
                                            >
                                                {t('settings.packages.activate.title')}
                                            </SwitchTitleBox>
                                            <SwitchDescriptionBox
                                                opaque={arePackagesActivatedState ?? false}
                                            >
                                                {t('settings.packages.activate.description')}
                                            </SwitchDescriptionBox>
                                        </SwitchTextBox>
                                        <SearchFieldBox>
                                            <TextFormField
                                                cmosVariant="roundedGrey"
                                                name={'search-packages-by-name'}
                                                value={searchInput}
                                                placeholder={t('settings.packages.search')}
                                                showLoader={isLoading}
                                                disabled={!arePackagesActivatedState}
                                                onEnterPress={initiateSearch}
                                                enableEnterComplete={true}
                                                onChange={(e) => {
                                                    handleSearchInput(e.target.value);
                                                }}
                                                endAdornment={
                                                    <InputAdornment position="end">
                                                        <IconButton
                                                            onClick={initiateSearch}
                                                            disabled={!arePackagesActivatedState}
                                                            style={{ marginRight: -8 }}
                                                        >
                                                            <SearchIcon
                                                                fill={
                                                                    arePackagesActivatedState
                                                                        ? 'var(--cm1)'
                                                                        : 'var(--cm4)'
                                                                }
                                                            />
                                                        </IconButton>
                                                    </InputAdornment>
                                                }
                                            />
                                        </SearchFieldBox>
                                    </LeftHeaderBox>
                                    <RightHeaderBox>
                                        <Box>
                                            <ImportPackageButton
                                                disabled={!arePackagesActivatedState}
                                            />
                                        </Box>
                                        <Box>
                                            <CreatePackageButton
                                                disabled={!arePackagesActivatedState}
                                            />
                                        </Box>
                                    </RightHeaderBox>
                                </HeaderBox>

                                <STable>
                                    <TableHead>
                                        <TableRow>
                                            <TableHeadCell
                                                style={{ width: '25%', paddingLeft: 30 }}
                                            >
                                                <ColumnTitle
                                                    opaque={arePackagesActivatedState ?? false}
                                                >
                                                    {t('settings.packages.table.name')}
                                                </ColumnTitle>
                                            </TableHeadCell>
                                            <TableHeadCell style={{ width: '25%' }}>
                                                <ColumnTitle
                                                    opaque={arePackagesActivatedState ?? false}
                                                >
                                                    {t('settings.packages.table.brand')}
                                                </ColumnTitle>
                                            </TableHeadCell>
                                            <TableHeadCell style={{ width: '25%' }}>
                                                <ColumnTitle
                                                    opaque={arePackagesActivatedState ?? false}
                                                >
                                                    {t('settings.packages.table.model')}
                                                </ColumnTitle>
                                            </TableHeadCell>
                                            <TableHeadCell style={{ width: '15%' }}>
                                                <ColumnTitle
                                                    opaque={arePackagesActivatedState ?? false}
                                                >
                                                    {t('settings.packages.table.year')}
                                                </ColumnTitle>
                                            </TableHeadCell>
                                            <TableHeadCell style={{ width: '10%' }} />
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {packages.length > 0 &&
                                            packages.map((p) => (
                                                <RowPackages
                                                    packageItem={p}
                                                    readonly={!arePackagesActivatedState}
                                                />
                                            ))}
                                    </TableBody>
                                </STable>
                                {!hasAnyPackage && !isLoading && (
                                    <InstructionContainer disabled={!arePackagesActivatedState}>
                                        <Instruction
                                            mainText={t('settings.packages.instruction.mainText')}
                                            secondaryText={t(
                                                'settings.packages.instruction.secondaryText'
                                            )}
                                        />
                                    </InstructionContainer>
                                )}
                            </>
                        )}
                        {isLoading && (
                            <AreaSpinnerContainer>
                                <AreaSpinner />
                            </AreaSpinnerContainer>
                        )}
                    </ImportPackageModalProvider>
                </DeletePackagePopupProvider>
            </EditOrCreatePackagePopupProvider>
        </SPageContentWithFooter>
    );
}

function usePackages(pageIndex: number, searchInput: string, searchAll: boolean) {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const pageSize = 12;

    const { data, isLoading, refetch } = useQuery(
        ['settings', 'packages', pageIndex, searchInput, searchAll],
        {
            queryFn: () =>
                PackagesSettingsApi.getAllPackages(pageIndex, pageSize, searchInput, searchAll),
        }
    );

    const packages = data?.packages ?? [];
    const arePackagesActivated = data?.arePackagesActivated ?? false;
    const hasAnyPackage = data?.hasAnyPackage ?? false;
    const pagesCount = data?.totalPages ?? 0;

    const debouncedLoadGlobalSettings = useMemo(
        () =>
            debounce(() => {
                dispatch(loadGlobalSettingsThunk());
            }, 1000),
        [dispatch]
    );

    const { mutate: activatePackages } = useMutation(
        (body: { arePackagesActivated: boolean }) => PackagesSettingsApi.activatePackages(body),
        {
            onSuccess: () => {
                toasters.success(
                    t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated')
                );
                refetch();
                debouncedLoadGlobalSettings();
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    return {
        packages,
        arePackagesActivated,
        hasAnyPackage,
        pagesCount,
        isLoading,
        refetch,
        activatePackages,
    };
}

function CreatePackageButton({ disabled }: { disabled: boolean }) {
    const { t } = useAppTranslation();
    const popup = useEditOrCreatePackagePopup();

    return (
        <Button disabled={disabled} sx={{ width: 180 }} onClick={() => popup.open()}>
            {t('settings.packages.create')}
        </Button>
    );
}

function ImportPackageButton({ disabled }: { disabled: boolean }) {
    // TODO implement opening popup
    const { t } = useAppTranslation();
    const importPopup = useImportPackageModal();

    return (
        <Button
            onClick={() => importPopup.open()}
            disabled={disabled}
            cmosVariant="stroke"
            cmosSize="medium"
            sx={{ width: 180 }}
        >
            {t('settings.packages.import')}
        </Button>
    );
}

const SPageContentWithFooter = styled(PageContentWithFooter)({
    minHeight: 830,
});

const PaginationContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
    margin: '30px 0 0 0',

    '& .MuiPagination-ul': {
        gap: 8,
    },
    '& .MuiPaginationItem-page:not(.Mui-selected)': {
        color: theme.palette.neutral[6],
    },
    '& .MuiPaginationItem-icon': {
        color: theme.palette.info.main,
    },
}));

const HeaderBox = styled(Box)({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 16,
    marginBottom: 32,
    paddingLeft: 30,
    paddingRight: 30,
    gap: 8,
});

const LeftHeaderBox = styled(Box)({
    marginTop: 16,
    marginBottom: 8,
    display: 'flex',
    alignItems: 'center',
    gap: 4,
});

const RightHeaderBox = styled(Box)({
    marginTop: 16,
    marginBottom: 8,
    display: 'grid',
    gridTemplateColumns: '180px 180px',
    gap: 8,
});

const SSwitch = styled(Switch)(({ theme }) => ({
    '& .MuiSwitch-switchBase': {
        '&.Mui-checked': {
            color: theme.palette.neutral[1],
            '& .MuiSwitch-thumb:before': {
                color: theme.palette.neutral[1],
            },
            '& + .MuiSwitch-track': {
                opacity: 1,
            },
        },
        '& .MuiSwitch-thumb:before': {
            color: theme.palette.neutral[1],
        },
    },
}));

const SwitchTextBox = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-around',
});

const SwitchTitleBox = styled(Box)<{ opaque: boolean }>(({ theme, opaque }) => ({
    ...theme.typography.h6,
    opacity: opaque ? '100%' : '40%',
}));

const SwitchDescriptionBox = styled(Box)<{ opaque: boolean }>(({ theme, opaque }) => ({
    ...theme.typography.h6,
    fontWeight: 'normal',
    opacity: opaque ? '100%' : '40%',
}));

const SearchFieldBox = styled(Box)({
    marginLeft: '20px',
    minWidth: 280,
});

const STable = styled(Table)({
    '& td': {
        maxWidth: 0,
    },
});

const ColumnTitle = styled('span')<{ opaque: boolean }>(({ opaque }) => ({
    opacity: opaque ? '100%' : '40%',
}));

const InstructionContainer = styled('div')<{ disabled: boolean }>(({ disabled }) => ({
    height: 600,
    opacity: disabled ? '40%' : '100%',
}));

const AreaSpinnerContainer = styled('div')({
    height: 600,
    display: 'flex',
});
