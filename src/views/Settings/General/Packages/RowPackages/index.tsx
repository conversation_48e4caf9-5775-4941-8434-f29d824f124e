import { styled, TableRow, useTheme } from '@mui/material';
import { PackageListItemDto } from 'api/settings/packages';
import { TableCell } from 'common/components';
import { Button } from 'common/components/Button';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import TextOverflowTooltip from 'common/components/TextOverflowTooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';
import { useDeletePackagePopup } from '../DeletePackagePopup';
import { useEditOrCreatePackagePopup } from '../EditOrCreatePackagePopup';

type RowPackagesProps = {
    packageItem: PackageListItemDto;
    readonly: boolean;
};

const RowPackages = ({ packageItem, readonly }: RowPackagesProps) => {
    const { t } = useAppTranslation();

    const brands = useMemo(() => {
        if (packageItem.brands.length === 0) {
            return t('settings.packages.table.all');
        }

        return packageItem.brands.map((x) => x.brand).join(', ');
    }, [packageItem.brands, t]);

    const models = useMemo(() => {
        const models = packageItem.brands.filter((x) => x.models.length > 0).map((x) => x.models);
        if (models.length === 0) {
            return t('settings.packages.table.all');
        }

        return models.map((x) => x.join(', ')).join(', ');
    }, [packageItem.brands, t]);

    const years = useMemo(() => {
        if (packageItem.years.length === 0) {
            return t('settings.packages.table.all');
        }

        return packageItem.years.join(', ');
    }, [packageItem.years, t]);

    return (
        <STableRow readonly={readonly}>
            <TableCell component="td" scope="row" style={{ paddingLeft: 30 }}>
                <TextOverflowTooltip text={packageItem.name}>
                    {(ref) => (
                        <TableField ref={ref} opaque={!readonly}>
                            {packageItem.name}
                        </TableField>
                    )}
                </TextOverflowTooltip>
            </TableCell>
            <TableCell component="td" scope="row">
                <TextOverflowTooltip text={brands}>
                    {(ref) => (
                        <TableField ref={ref} opaque={!readonly}>
                            {brands}
                        </TableField>
                    )}
                </TextOverflowTooltip>
            </TableCell>
            <TableCell component="td" scope="row">
                <TextOverflowTooltip text={models}>
                    {(ref) => (
                        <TableField ref={ref} opaque={!readonly}>
                            {models}
                        </TableField>
                    )}
                </TextOverflowTooltip>
            </TableCell>
            <TableCell component="td" scope="row">
                <TextOverflowTooltip text={years}>
                    {(ref) => (
                        <TableField ref={ref} opaque={!readonly}>
                            {years}
                        </TableField>
                    )}
                </TextOverflowTooltip>
            </TableCell>
            <TableCell component="td" scope="row">
                <ButtonsContainer className="options">
                    <EditPackageButton packageId={packageItem.id} disabled={readonly} />
                    <DeletePackageButton packageId={packageItem.id} disabled={readonly} />
                </ButtonsContainer>
            </TableCell>
        </STableRow>
    );
};

function EditPackageButton({ packageId, disabled }: { packageId: string; disabled: boolean }) {
    const theme = useTheme();
    const popup = useEditOrCreatePackagePopup();

    return (
        <Button
            disabled={disabled}
            onClick={() => popup.open(packageId)}
            label=""
            color={theme.palette.neutral[5]}
            cmosVariant={'typography'}
            cmosSize={'medium'}
            Icon={EditIcon}
        />
    );
}

function DeletePackageButton({ packageId, disabled }: { packageId: string; disabled: boolean }) {
    const theme = useTheme();
    const popup = useDeletePackagePopup();

    return (
        <Button
            disabled={disabled}
            onClick={() => popup.open(packageId)}
            label=""
            color={theme.palette.neutral[5]}
            cmosVariant={'typography'}
            cmosSize={'medium'}
            Icon={DeleteIcon}
        />
    );
}

const STableRow = styled(TableRow)<{ readonly: boolean }>(({ theme, readonly }) => ({
    minHeight: '56px!important',
    height: 56,
    backgroundColor: theme.palette.background.paper,
    '&:hover': !readonly
        ? {
              '&:hover': {
                  backgroundColor: 'var(--cm5)',
                  '& .options': {
                      opacity: '1 !important',
                  },
              },
          }
        : undefined,
    '& .options': {
        opacity: 0,
    },
}));

const TableField = styled('span')<{ opaque: boolean }>(({ opaque }) => ({
    display: 'inline-block',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    width: '100%',
    opacity: opaque ? '100%' : '40%',
}));

const ButtonsContainer = styled('div')({
    display: 'flex',
});

export default RowPackages;
