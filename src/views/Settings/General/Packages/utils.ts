import { useRef } from 'react';

/**
 * Returns the last truthy value received, or the current value if it is truthy.
 *
 * @param {T} value - The current value to check for truthiness.
 * @return {T} The last truthy value received, or the current value if it is truthy.
 */
export function useLastTruthyValue<T>(value: T): T {
    const lastTruthyValue = useRef(value);
    if (value) {
        lastTruthyValue.current = value;
        return value;
    } else {
        return lastTruthyValue.current;
    }
}
