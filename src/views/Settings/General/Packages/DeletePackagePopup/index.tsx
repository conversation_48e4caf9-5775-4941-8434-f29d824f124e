import { useMutation } from '@tanstack/react-query';
import PackagesSettingsApi from 'api/settings/packages';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { createContext, useContext, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';

const DeletePackagePopupContext = createContext<{
    open(packageId?: string): void;
} | null>(null);

export function useDeletePackagePopup() {
    const ctx = useContext(DeletePackagePopupContext);
    if (!ctx) throw new Error('DeletePackagePopupContext is not available');
    return ctx;
}

export function DeletePackagePopupProvider({
    children,
    onChange,
}: React.PropsWithChildren<{ onChange?: () => void }>) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [open, setOpen] = useState(false);
    const [packageId, setPackageId] = useState<string>();

    const ctx = useMemo(
        () => ({
            open: (packageId?: string) => {
                setPackageId(packageId);
                setOpen(true);
            },
        }),
        []
    );

    const deletePackage = useMutation(
        async () => {
            if (!packageId) throw new Error('cannot delete package without id');
            await PackagesSettingsApi.deletePackage(packageId);
            closePopup();
        },
        {
            onSuccess: () => {
                if (onChange) onChange();
                toasters.success(null, t('settings.packages.deletePopup.deletedSuccessfully'));
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const closePopup = () => {
        setOpen(false);
        setPackageId(undefined);
    };

    return (
        <DeletePackagePopupContext.Provider value={ctx}>
            <DeleteConfirmationPopup
                open={open}
                title={t('settings.packages.deletePopup.title')}
                body={
                    <Trans
                        i18nKey="settings.packages.deletePopup.body"
                        t={t}
                        components={{ 1: <b /> }}
                    />
                }
                cancel={t('commonLabels.doNotDelete')}
                confirm={t('settings.packages.deletePopup.deleteButton')}
                onConfirm={() => {
                    deletePackage.mutate();
                }}
                onClose={closePopup}
            />
            {children}
        </DeletePackagePopupContext.Provider>
    );
}
