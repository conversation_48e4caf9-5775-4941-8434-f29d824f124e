import { Box } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentReasonsApi, {
    CustomAppointmentReasonDto,
    CustomApptDetailBody,
} from 'api/appointmentReasons';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useEffect, useMemo, useState } from 'react';
import { AddDetailsForm } from '../AddDetailsForm';
import CustomApptReasonDetailTable from '../CustomApptReasonDetailTable';
import { ImportDetailsForm } from '../ImportDetailsForm';

interface CustomApptReasonsDetailsListProps {
    customAppointmentReason: CustomAppointmentReasonDto;
    readonly?: boolean;
    refetch?: () => void;
}

export const CustomApptReasonsDetailsList = ({
    customAppointmentReason,
    readonly,
    refetch,
}: CustomApptReasonsDetailsListProps) => {
    const [showAddDetailsPopUp, setShowAddDetailsPopUp] = useState(false);
    const [showImportDetailsPopUp, setShowImportDetailsPopUp] = useState(false);
    const { t } = useAppTranslation();

    const { createReasonDetail, isLoading, isSuccess, reset } = useAddDetailsForm(
        customAppointmentReason.id,
        refetch
    );

    const reasonDetails = useMemo(() => {
        return customAppointmentReason.customAppointmentReasonDetails || [];
    }, [customAppointmentReason.customAppointmentReasonDetails]);

    const handleAddReasonDetail = () => {
        setShowAddDetailsPopUp(true);
    };

    const handleShowImportDetailsPopUp = () => {
        setShowImportDetailsPopUp(true);
    };

    const handleCreateReasonDetail = (body: CustomApptDetailBody) => {
        createReasonDetail(body);
        // reset();
    };

    useEffect(() => {
        if (!isLoading && isSuccess) {
            setShowAddDetailsPopUp(false);
        }
    }, [isLoading, isSuccess, setShowAddDetailsPopUp]);

    return (
        <Box
            sx={{
                width: '100%',
                paddingTop: 0,
                paddingBottom: 2.5,
            }}
        >
            {/* details */}
            {!!reasonDetails.length && (
                <CustomApptReasonDetailTable
                    customAppointmentReason={customAppointmentReason}
                    readonly={readonly}
                    refetch={refetch}
                />
            )}
            {/* buttons */}
            <Box
                sx={{
                    display: 'flex',
                    gap: 1,
                    marginTop: 2.5,
                    marginLeft: 2,
                }}
            >
                <Button
                    disabled={readonly}
                    customStyles={{
                        width: 280,
                    }}
                    buttonInnercustomStyles={{
                        width: '100%',
                        justifyContent: 'space-between',
                    }}
                    cmosVariant={'stroke'}
                    iconPosition="right"
                    Icon={PlusIcon}
                    color={Colors.Neutral3}
                    onClick={handleAddReasonDetail}
                    label={t('customAppointmentReasonsForms.details.addButton')}
                />
                <Button
                    disabled={readonly}
                    customStyles={{
                        width: 260,
                        alignItems: 'center',
                    }}
                    cmosVariant={'stroke'}
                    color={Colors.Neutral3}
                    onClick={handleShowImportDetailsPopUp}
                    label={t('customAppointmentReasonsForms.details.importButton')}
                />
            </Box>
            {showAddDetailsPopUp && (
                <AddDetailsForm
                    open={showAddDetailsPopUp}
                    onClose={() => {
                        setShowAddDetailsPopUp(false);
                        reset();
                    }}
                    onConfirm={handleCreateReasonDetail}
                    onConfirmText={t('customAppointmentReasonsForms.details.addButton')}
                    title={t('customAppointmentReasonsForms.details.form.title')}
                    isLoading={isLoading}
                    isSuccess={isSuccess}
                />
            )}
            {showImportDetailsPopUp && (
                <ImportDetailsForm
                    customApptReasonId={customAppointmentReason.id}
                    open={showImportDetailsPopUp}
                    onClose={() => {
                        setShowImportDetailsPopUp(false);
                        refetch && refetch();
                    }}
                />
            )}
        </Box>
    );
};

const useAddDetailsForm = (reasonId: string, onSuccess?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const {
        mutate: createReasonDetail,
        isLoading,
        isSuccess,
        isError,
        reset,
    } = useMutation(
        (body: CustomApptDetailBody) => AppointmentReasonsApi.createReasonDetail(reasonId, body),
        {
            onSuccess: () => {
                toasters.success(
                    '',
                    t('customAppointmentReasonsForms.details.notifications.createSuccessTitle')
                );
                onSuccess && onSuccess();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('customAppointmentReasonsForms.details.notifications.missingName')
                );
            },
        }
    );

    return {
        createReasonDetail,
        isLoading,
        isSuccess,
        isError,
        reset,
    };
};
