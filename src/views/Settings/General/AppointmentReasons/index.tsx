import { Box, Switch, useTheme } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import AppointmentReasonsApi, {
    CustomAppointmentReasonDto,
    CustomApptReasonUpdateRequest,
} from 'api/appointmentReasons';
import { Button } from 'common/components/Button';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import useToasters from 'common/hooks/useToasters';
import debounce from 'lodash/debounce';
import { useMemo, useState } from 'react';
import { useAppDispatch } from 'store';
import { loadGlobalSettingsThunk } from 'store/slices/globalSettingsSlice';
import PageContent from 'views/Components/Page';
import CustomAppointmentReasonFormModal from './CustomAppointmentReasonFormModal';
import { CustomAppointmentReasonsList } from './CustomAppointmentReasonsList';

export const CUSTOM_APPT_REASONS_QUERY_KEY = ['settings', 'customAppointmentReasons'];

export function AppointmentReasonsSettings() {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
    const [editableItem, setEditableItem] = useState<CustomAppointmentReasonDto>();

    const {
        customAppointmentReasons,
        isLoading,
        refetch,
        updateCustomAppointmentReason,
        isCustomApptReasonEnabled,
        enableCustomApptReasons,
    } = useCustomAppointmentReasons();

    useDocumentTitle(
        `${t('titles.settings.settings')} - ${t('settings.general.appointmentReasons')}`
    );

    const handleToggleReasonsForAppointment = () => {
        enableCustomApptReasons({ isCustomApptReasonEnabled: !isCustomApptReasonEnabled });
    };

    const handleMove = async (index: number, direction: number) => {
        const promises = [];
        const body1 = {
            id: customAppointmentReasons[index].id,
            order: customAppointmentReasons[index + direction].order,
        };
        const body2 = {
            id: customAppointmentReasons[index + direction].id,
            order: customAppointmentReasons[index].order,
        };

        promises.push(updateCustomAppointmentReason(body1));
        promises.push(updateCustomAppointmentReason(body2));
        await Promise.all(promises);
    };

    const colorsInUse = useMemo(() => {
        return (
            customAppointmentReasons
                // allow to reselect color which is related to item currently edited
                .filter((r) => r.id !== editableItem?.id)
                .map((r) => r.color)
                .filter((c) => c)
        );
    }, [customAppointmentReasons, editableItem]);

    return (
        <>
            <PageContent paddedX>
                <Box
                    sx={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginTop: 2,
                        marginBottom: 4,
                    }}
                >
                    <Box
                        sx={{
                            marginTop: 2,
                            marginBottom: 1,
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-start',
                            gap: 1,
                        }}
                    >
                        <Switch
                            sx={{
                                '& .MuiSwitch-switchBase': {
                                    '&.Mui-checked': {
                                        color: '#FFFFFF',
                                        '& .MuiSwitch-thumb:before': {
                                            color: '#FFFFFF',
                                        },
                                        '& + .MuiSwitch-track': {
                                            opacity: 1,
                                        },
                                    },
                                    '& .MuiSwitch-thumb:before': {
                                        color: '#FFFFFF',
                                    },
                                },
                            }}
                            checked={isCustomApptReasonEnabled}
                            onChange={() => {
                                handleToggleReasonsForAppointment();
                            }}
                            name="checkedA"
                        />
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'space-around',
                            }}
                        >
                            <Box
                                sx={(theme) => ({
                                    ...theme.typography.h6,
                                })}
                            >
                                {t(`customAppointmentReasonsForms.activate.title`)}
                            </Box>
                            <Box
                                sx={(theme) => ({
                                    ...theme.typography.h6,
                                    fontWeight: 'normal',
                                })}
                            >
                                {t(`customAppointmentReasonsForms.activate.description`)}
                            </Box>
                        </Box>
                    </Box>
                    <Box
                        sx={{
                            width: '30%',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-end',
                        }}
                    >
                        <Box sx={{ width: 232, display: 'flex' }}>
                            <Button
                                disabled={!isCustomApptReasonEnabled}
                                color={theme.palette.primary.main}
                                cmosVariant={'filled'}
                                blockMode
                                label={t(
                                    'customAppointmentReasonsForms.appointmentReasons.addButton'
                                )}
                                onClick={() => {
                                    setEditableItem(undefined);
                                    setIsOpenModal(true);
                                }}
                            />
                        </Box>
                    </Box>
                </Box>
                <CustomAppointmentReasonsList
                    readonly={!isCustomApptReasonEnabled}
                    customAppointmentReasons={customAppointmentReasons}
                    isLoadingInProgress={isLoading}
                    handleMove={handleMove}
                    refetch={refetch}
                    onEdit={(r) => {
                        setEditableItem(r);
                        setIsOpenModal(true);
                    }}
                />
            </PageContent>
            <CustomAppointmentReasonFormModal
                isOpen={isOpenModal}
                colorsInUse={colorsInUse}
                value={editableItem}
                onClose={() => {
                    setIsOpenModal(false);
                    // let modal close be animated til the end, before cleaning form
                    setTimeout(() => setEditableItem(undefined), 150);
                }}
                onSave={() => {
                    setIsOpenModal(false);
                    refetch();
                    // let modal close be animated til the end, before cleaning form
                    setTimeout(() => setEditableItem(undefined), 150);
                }}
            />
        </>
    );
}

function useCustomAppointmentReasons() {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    const { data, isLoading, refetch } = useQuery(
        CUSTOM_APPT_REASONS_QUERY_KEY,
        () => AppointmentReasonsApi.getCustomAppointmentReasons(),
        {
            cacheTime: Infinity,
            refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
            refetchInterval: 120000,
        }
    );
    const customAppointmentReasons = useMemo(() => data?.customAppointmentReasons ?? [], [data]);

    const isCustomApptReasonEnabled = useMemo(
        () => data?.isCustomApptReasonEnabled ?? false,
        [data]
    );

    const debouncedLoadGlobalSettings = useMemo(
        () =>
            debounce(() => {
                dispatch(loadGlobalSettingsThunk());
            }, 1000),
        [dispatch]
    );

    const { mutate: updateCustomAppointmentReason } = useMutation(
        (body: CustomApptReasonUpdateRequest & { id: string }) => {
            const { id, ...customApptReason } = body;
            return AppointmentReasonsApi.updateCustomAppointmentReason(id, customApptReason);
        },
        {
            onSuccess: () => {
                toasters.success(
                    '',
                    t(
                        'customAppointmentReasonsForms.appointmentReasons.notifications.updateSuccessTitle'
                    )
                );
                refetch();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('customAppointmentReasonsForms.appointmentReasons.notifications.errorTitle')
                );
            },
        }
    );
    const { mutate: enableCustomApptReasons } = useMutation(
        (body: { isCustomApptReasonEnabled: boolean }) =>
            AppointmentReasonsApi.enableCustomApptReasons(body),
        {
            onSuccess: () => {
                toasters.success(
                    '',
                    t(
                        'customAppointmentReasonsForms.appointmentReasons.notifications.settingsSuccessTitle'
                    )
                );
                refetch();
                debouncedLoadGlobalSettings();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('customAppointmentReasonsForms.appointmentReasons.notifications.errorTitle')
                );
            },
        }
    );

    return {
        customAppointmentReasons,
        isCustomApptReasonEnabled,
        enableCustomApptReasons,
        isLoading,
        refetch,
        updateCustomAppointmentReason,
    };
}
