import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { useMutation } from '@tanstack/react-query';
import AppointmentReasonsApi, {
    ImportCustomApptReasonDetailResponse,
} from 'api/appointmentReasons';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { DownloadCloudIcon } from 'common/components/Icons/DownloadCloudIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { Interweave } from 'interweave';
import { useState } from 'react';
import { getFileExtension } from 'utils/Files';
import DragAndDropFileInput from 'views/Components/DragAndDropFileInput';
import { Modal } from '../../../../../common/components/Modal';

export interface ImportDetailsFormProps {
    open: boolean;
    onClose: () => void;
    customApptReasonId: string;
}

export const ImportDetailsForm = ({
    open,
    customApptReasonId,
    onClose,
}: ImportDetailsFormProps) => {
    const maxAllowedSize = 10000000;
    const { t } = useAppTranslation();
    const [file, setFile] = useState<File | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [warnModalOpen, setWarnModalOpen] = useState<boolean>(false);

    const { importCustomApptReasonDetail } = useCustomAppointmentReasonDetail(
        () => {
            setIsLoading(false);
        },
        (response) => {
            if (response.isFileValid) onClose();
        }
    );

    const downloadTemplate = () => {
        // Download template
        AppointmentReasonsApi.downloadTemplate();
    };

    const fileChanged = (file: File | null) => {
        const extension = getFileExtension(file);
        setFile(file);

        if (!file || !extension || extension !== 'xlsx' || file.size > maxAllowedSize) {
            processInvalidFile();
        }
    };

    const handleOnClose = () => {
        if (file == null) {
            onClose();
        } else {
            setWarnModalOpen(true);
        }
    };

    const processInvalidFile = () => {
        setFile(null);
    };

    const uploadFile = () => {
        if (!file) return;
        importCustomApptReasonDetail({ customApptReasonId, file: file });
    };

    return (
        <>
            <Modal open={open}>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: 2.5,
                        padding: '40px 40px 24px 40px',
                        width: 812,
                        height: 344,
                    }}
                >
                    <Grid container spacing={0} display="flex" alignItems="center">
                        <Grid item xs={5}>
                            <Typography
                                component="span"
                                sx={(theme) => ({
                                    ...theme.typography.h5Roboto,
                                    color: theme.palette.neutral[7],
                                })}
                            >
                                {t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.importAppointmentReasonDetails'
                                )}
                            </Typography>
                        </Grid>
                        <Grid item xs={7}>
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Button
                                        color={Colors.Grey5}
                                        label={t(
                                            'settings.customAppointmentReasons.importAppointmentReasonDetail.goBack'
                                        )}
                                        onClick={handleOnClose}
                                        sx={{ width: '100%' }}
                                    />
                                </Grid>
                                <Grid item xs={6}>
                                    <Button
                                        color={Colors.Success}
                                        disabled={!file || isLoading}
                                        label={t(
                                            'settings.customAppointmentReasons.importAppointmentReasonDetail.uploadFile'
                                        )}
                                        onClick={uploadFile}
                                        sx={{ width: '100%' }}
                                        showLoader={isLoading}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Box component="div" sx={{ width: '100%' }}>
                        <DragAndDropFileInput
                            acceptedFormats={['xlsx']}
                            onFileSet={fileChanged}
                            label={t(
                                'settings.customAppointmentReasons.importAppointmentReasonDetail.dragOrClickHereToUploadYourExcelFile'
                            )}
                            sx={{
                                gridTemplateColumns: '1fr 1fr !important',
                                gap: '32px !important',
                            }}
                        />
                    </Box>
                    <Box
                        component="div"
                        sx={(theme) => ({ width: '100%', borderTop: `1px solid #C9CDD3` })}
                    />
                    <Grid container spacing={0}>
                        <Grid item xs={6}>
                            <Typography
                                component="p"
                                sx={{
                                    fontFamily: 'Roboto',
                                    fontSize: '12px',
                                    fontStyle: 'normal',
                                    fontWeight: 700,
                                    lineHeight: 'normal',
                                    color: '#5C6477',
                                }}
                            >
                                {t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.notes'
                                )}
                                :
                            </Typography>
                            <Typography
                                component="p"
                                sx={{
                                    fontFamily: 'Roboto',
                                    fontSize: '12px',
                                    fontStyle: 'normal',
                                    fontWeight: 400,
                                    lineHeight: 'normal',
                                    color: '#5C6477',
                                }}
                            >
                                {t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.acceptedFormat'
                                )}
                            </Typography>
                            <Typography
                                component="p"
                                sx={{
                                    fontFamily: 'Roboto',
                                    fontSize: '12px',
                                    fontStyle: 'normal',
                                    fontWeight: 400,
                                    lineHeight: 'normal',
                                    color: '#5C6477',
                                }}
                            >
                                {t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.maximumAllowedSize'
                                )}
                            </Typography>
                        </Grid>
                        <Grid item xs={6} display="flex" justifyContent="flex-end">
                            <Button
                                color={Colors.Grey5}
                                label={t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.downloadTemplate'
                                )}
                                cmosVariant={'typography'}
                                Icon={DownloadCloudIcon}
                                iconPosition="left"
                                onClick={downloadTemplate}
                            />
                        </Grid>
                    </Grid>
                </Box>
            </Modal>
            <Modal open={warnModalOpen} onClose={() => setWarnModalOpen(false)}>
                <Box
                    component="div"
                    sx={{
                        width: 508,
                        height: 154,
                    }}
                >
                    <Grid
                        container
                        spacing={0}
                        display="flex"
                        alignItems="center"
                        justifyContent="flex-end"
                    >
                        <IconButton onClick={() => setWarnModalOpen(false)}>
                            <CloseIcon />
                        </IconButton>
                    </Grid>
                    <Grid
                        container
                        spacing={0}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                    >
                        <Typography
                            component="span"
                            sx={(theme) => ({
                                ...theme.typography.h4Inter,
                                color: theme.palette.neutral[8],
                                fontWeight: 700,
                            })}
                        >
                            {t(
                                'settings.customAppointmentReasons.importAppointmentReasonDetail.cancelXlsxFileUpload'
                            )}
                        </Typography>
                    </Grid>
                    <Grid
                        container
                        spacing={2}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        sx={{ padding: '0px 47px', marginTop: '12px' }}
                    >
                        <Grid item xs={6}>
                            <Button
                                color={Colors.Grey5}
                                label={t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.goBack'
                                )}
                                onClick={() => setWarnModalOpen(false)}
                                sx={{ width: '100%' }}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <Button
                                color={Colors.Success}
                                disabled={!file || isLoading}
                                label={t(
                                    'settings.customAppointmentReasons.importAppointmentReasonDetail.yesCancel'
                                )}
                                onClick={() => {
                                    setWarnModalOpen(false);
                                    onClose();
                                }}
                                sx={{ width: '100%' }}
                                showLoader={isLoading}
                            />
                        </Grid>
                    </Grid>
                </Box>
            </Modal>
        </>
    );
};

const useCustomAppointmentReasonDetail = (
    onSettled: () => void,
    onSuccess?: (data: ImportCustomApptReasonDetailResponse) => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const { mutate: importCustomApptReasonDetail } = useMutation(
        (body: { customApptReasonId: string; file: File }) => {
            return AppointmentReasonsApi.importCustomAppointmentReasons(
                body.customApptReasonId,
                body.file
            );
        },
        {
            onSuccess: (response) => {
                if (response.isFileValid && response.failedExcelImportResult == null) {
                    toasters.success(
                        t(
                            'settings.customAppointmentReasons.importAppointmentReasonDetail.documentUploadedText'
                        ),
                        t(
                            'settings.customAppointmentReasons.importAppointmentReasonDetail.documentUploadedTitle'
                        )
                    );
                } else {
                    toasters.warning(
                        <Interweave
                            content={t('toasters.documentPartiallyUploadedText', {
                                failedRowsFileUrl:
                                    response.failedExcelImportResult.failedRowsFileUrl,
                                totalRows: response.failedExcelImportResult.totalRows,
                                importedRows: response.failedExcelImportResult.importedRows,
                            })}
                        />,
                        t('toasters.documentPartiallyUploadedTitle'),
                        { duration: 15000 }
                    );
                }
                onSuccess && onSuccess(response);
            },
            onError: () => {
                toasters.danger(
                    t(
                        'settings.customAppointmentReasons.importAppointmentReasonDetail.documentNotUploadedText'
                    ),
                    t(
                        'settings.customAppointmentReasons.importAppointmentReasonDetail.documentNotUploadedTitle'
                    )
                );
                onSuccess &&
                    onSuccess({
                        isFileValid: false,
                        failedExcelImportResult: {
                            importedRows: 0,
                            totalRows: 0,
                            failedRowsFileUrl: '',
                        },
                    });
            },
            onSettled: () => {
                onSettled();
            },
        }
    );

    return { importCustomApptReasonDetail };
};
