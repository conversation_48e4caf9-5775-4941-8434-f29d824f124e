import {
    Paper,
    Select,
    SelectProps,
    listClasses,
    menuItemClasses,
    selectClasses,
    styled,
} from '@mui/material';
import clsx from 'clsx';
import { rgba } from 'common/styles/ColorHelpers';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { ComponentProps, ComponentType, forwardRef } from 'react';
import { DownIcon } from './DownIcon';

/**
 * Styled Select for all selects
 */
const SelectBase = styled(Select)(({ theme }) => ({
    minWidth: 100,
    '--select-height': '32px',
    height: 'var(--select-height)',
    padding: 0,
    overflow: 'hidden',
    backgroundColor: theme.palette.neutral[2],
    [`& .${selectClasses.icon}`]: {
        backgroundColor: theme.palette.neutral[2],
        top: 'calc(50% - 12px)',
        right: 9,
    },
    [`& .${selectClasses.root}`]: {
        ...theme.typography.h6Inter,
        display: 'flex',
        alignItems: 'end',
        paddingTop: 5,
        fontWeight: 'initial',
        paddingBottom: 5,
        height: '100%',
        '&:focus': {
            backgroundColor: 'transparent',
        },
    },
    [`& .${selectClasses.select}`]: {
        ...theme.typography.h7Roboto,
        display: 'flex',
        alignItems: 'center',
        paddingTop: 0,
        paddingBottom: 0,
        height: 'var(--select-height)',
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
    },
    [`& .${selectClasses.nativeInput}`]: {
        height: 'var(--select-height)',
    },
})) as ComponentType<SelectProps>; // NOTE (MB) type assertion is necessary because otherwise TS complains for no reason, Trust Me™

const SelectCustomPaper = styled(Paper)(({ theme }) => ({
    marginTop: 5,
    border: `1px solid ${theme.palette.primary.main}`,
    position: 'fixed',
    borderRadius: 5,
    overflowY: 'auto',
    ...scrollbarStyle(),
    [`& .${menuItemClasses.root}`]: {
        display: 'flex',
        gap: 4,
        ...theme.typography.h6Inter,
        fontWeight: 400,
        color: rgba(theme.palette.neutral[7], 0.8),
        padding: '4px 0px',
        marginLeft: 16,
        borderBottom: `1px solid`,
        borderImage: `linear-gradient(to right, ${rgba(
            theme.palette.neutral[4],
            0.5
        )} 99%, transparent 10%) 100% 1 100% 1`,
        '&:hover': {
            backgroundColor: rgba(theme.palette.primary.main, 0.03),
        },

        '&:last-child': {
            borderBottom: 'none',
        },
    },
    '& .Mui-selected': {
        color: rgba(theme.palette.primary.main, 0.8),
        backgroundColor: `${theme.palette.common.white} !important`,
        '&:hover': {
            backgroundColor: `${rgba(theme.palette.primary.main, 0.03)} !important`,
        },
    },
    '& .MuiTouchRipple-child': {
        backgroundColor: theme.palette.primary.main,
    },

    [`& .${listClasses.root}`]: {
        maxHeight: 140,
        paddingTop: 0,
        overflowY: 'auto',
        ...scrollbarStyle(),
    },
}));

export type SSelectProps<T> = Omit<
    React.PropsWithoutRef<SelectProps<T>>,
    'MenuProps' | 'color' | 'variant' | 'slotProps'
> & { ref?: React.Ref<HTMLDivElement> | undefined } & {
    isInvalid?: boolean;

    /**
     * if set to true, border is added between menu items of the
     * select component
     */
    menuBorders?: boolean;

    slotProps?: SelectProps<T>['slotProps'] & {
        placeholder?: ComponentProps<typeof Placeholder>;
    };
};

const SSelectBase = forwardRef<
    HTMLDivElement,
    SSelectProps<unknown> & { innerComponent: ComponentType<SelectProps> }
>(
    (
        {
            innerComponent: Inner,
            isInvalid,
            className,
            placeholder,
            slotProps = {},
            value,
            ...props
        },
        ref
    ) => {
        const { placeholder: placeholderProps, ...otherSlotProps } = slotProps;

        return (
            <Inner
                ref={ref}
                aria-invalid={isInvalid}
                color="primary"
                variant="outlined"
                IconComponent={DownIcon}
                displayEmpty
                value={value}
                MenuProps={{
                    slots: {
                        paper: SelectCustomPaper,
                    },
                    elevation: 0,
                }}
                slotProps={otherSlotProps}
                className={clsx(isInvalid && 'invalid', className)}
                {...props}
                renderValue={
                    placeholder && Array.isArray(value) && value.length === 0
                        ? (v) => <Placeholder {...placeholderProps}>{placeholder}</Placeholder>
                        : props.renderValue
                }
            />
        );
    }
);

const Placeholder = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    fontSize: 11,
}));

function createSelectComponent(inputComponent: React.ComponentType<SelectProps>) {
    return forwardRef<HTMLDivElement, SSelectProps<unknown>>(({ ...props }, ref) => {
        return <SSelectBase ref={ref} innerComponent={inputComponent} {...props} />;
    }) as <T>(props: SSelectProps<T>) => JSX.Element;
}

export const SSelectRect = createSelectComponent(SelectBase);

export default SSelectRect;

export const SSelectInput = createSelectComponent(
    styled(SelectBase)(({ theme }) => ({
        flexGrow: 1,
        '&.invalid > fieldset': {
            borderColor: `var(--danger) !important`,
        },
    }))
);
