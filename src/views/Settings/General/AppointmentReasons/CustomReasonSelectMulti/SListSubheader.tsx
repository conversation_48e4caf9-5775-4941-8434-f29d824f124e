import { ListSubheaderProps, ListSubheader as MListSubheader, styled } from '@mui/material';

const SListSubheader = styled(MListSubheader)(({ theme }) => ({
    ...theme.typography.h7Inter,
    fontWeight: 'bold',
    color: theme.palette.neutral[6],
    backgroundColor: theme.palette.neutral[1],
    lineHeight: '32px',
    top: 0,
    paddingLeft: 16,
    paddingRight: 16,
}));

const ListSubheader = (props: ListSubheaderProps & { muiSkipListHighlight: boolean }) => {
    const { muiSkipListHighlight, ...other } = props;
    return <SListSubheader {...other} />;
};
export default ListSubheader;
