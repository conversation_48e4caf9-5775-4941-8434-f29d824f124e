import { Table, TableBody, TableContainer, TableHead, TableRow, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentReasonsApi, {
    CustomAppointmentReasonDetailsDto,
    CustomAppointmentReasonDto,
    CustomApptReasonDetailsUpdateRequest,
} from 'api/appointmentReasons';
import { TableHeadCell } from 'common/components';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { RowReasonDetail } from '../RowReasonDetail';

type CustomApptReasonDetailTableProps = {
    customAppointmentReason: CustomAppointmentReasonDto;
    readonly?: boolean;
    refetch?: () => void;
};

const STableContainer = styled(TableContainer)({
    maxHeight: 330,
    ...scrollbarStyle(),
});

const CustomApptReasonDetailTable = ({
    customAppointmentReason,
    readonly,
    refetch,
}: CustomApptReasonDetailTableProps) => {
    const reasonDetails = customAppointmentReason.customAppointmentReasonDetails || [];
    const { t } = useAppTranslation();

    const { updateCustomAppointmentReasonDetail } = useCustomApptReasonDetailTable(
        customAppointmentReason,
        refetch
    );

    const handleMove = (index: number, direction: number) => {
        const promises = [];
        const body1 = {
            id: reasonDetails[index].id,
            order: reasonDetails[index + direction].order,
        };
        const body2 = {
            id: reasonDetails[index + direction].id,
            order: reasonDetails[index].order,
        };

        promises.push(updateCustomAppointmentReasonDetail(body1));
        promises.push(updateCustomAppointmentReasonDetail(body2));
    };

    return (
        <STableContainer>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableHeadCell style={{ paddingLeft: 30, width: '25%' }}>
                            {t('customAppointmentReasonsForms.details.name')}
                        </TableHeadCell>
                        <TableHeadCell style={{ width: '25%' }}>
                            {t('customAppointmentReasonsForms.details.brand')}
                        </TableHeadCell>
                        <TableHeadCell style={{ width: '20%' }}>
                            {t('customAppointmentReasonsForms.details.model')}
                        </TableHeadCell>
                        <TableHeadCell style={{ width: '20%' }}>
                            {t('customAppointmentReasonsForms.details.year')}
                        </TableHeadCell>
                        <TableHeadCell style={{ width: '10%' }} />
                    </TableRow>
                </TableHead>
                <TableBody>
                    {reasonDetails &&
                        reasonDetails.map(
                            (reasonDetail: CustomAppointmentReasonDetailsDto, idxRow: number) => (
                                <RowReasonDetail
                                    reasonId={customAppointmentReason.id}
                                    reasonDetail={reasonDetail}
                                    key={`row_${idxRow}`}
                                    readonly={readonly}
                                    index={idxRow}
                                    isLastItem={idxRow === reasonDetails.length - 1}
                                    handleMove={handleMove}
                                    refetch={refetch}
                                />
                            )
                        )}
                </TableBody>
            </Table>
        </STableContainer>
    );
};

export default CustomApptReasonDetailTable;

const useCustomApptReasonDetailTable = (
    customAppointmentReason: CustomAppointmentReasonDto,
    onSuccess?: () => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const {
        mutate: updateCustomAppointmentReasonDetail,
        isLoading,
        isSuccess,
    } = useMutation(
        (body: CustomApptReasonDetailsUpdateRequest & { id: string }) => {
            return AppointmentReasonsApi.updateCustomAppointmentReasonDetail(
                customAppointmentReason.id,
                body.id || '',
                body
            );
        },
        {
            onSuccess: () => {
                toasters.success(
                    '',
                    t('customAppointmentReasonsForms.details.notifications.updateSuccessTitle')
                );
                onSuccess && onSuccess();
            },
            onError: () => {
                toasters.danger(
                    '',
                    t('customAppointmentReasonsForms.details.notifications.updateErrorTitle')
                );
            },
        }
    );

    return {
        updateCustomAppointmentReasonDetail,
        isLoading,
        isSuccess,
    };
};
