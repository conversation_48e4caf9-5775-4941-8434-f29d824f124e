import { Box, Grid, TableRow, Tooltip, TooltipProps, styled, tooltipClasses } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentReasonsApi, {
    CustomAppointmentReasonDetailsDto,
    CustomApptDetailBody,
    CustomApptReasonDetailsUpdateRequest,
} from 'api/appointmentReasons';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { MoveDownIcon } from 'common/components/Icons/MoveDownIcon';
import { MoveUpIcon } from 'common/components/Icons/MoveUpIcon';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo, useState } from 'react';
import { Button } from '../../../../../common/components/Button';
import TableCell from '../../../../../common/components/TableCell';
import { Colors } from '../../../../../common/styles/Colors';
import { AddDetailsForm } from '../AddDetailsForm';

type RowReasonDetailProps = {
    reasonDetail: CustomAppointmentReasonDetailsDto;
    index: number;
    reasonId: string;
    isLastItem: boolean;
    readonly?: boolean;
    handleMove: (index: number, direction: number) => void;
    refetch?: () => void;
};

const Row = styled(TableRow)(({ theme }) => ({
    minHeight: '56px!important',
    height: 56,
    backgroundColor: theme.palette.common.white,
}));

const CellContentR = styled(Grid)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    fontWeight: 'normal',
}));

const CellContentI = styled(Grid)(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'normal',
}));

const STooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: '#F6F6F6',
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
        filter: `drop-shadow(0px 0px 1px ${theme.palette.neutral[7]})`,
        position: 'relative',
        borderRadius: 5,
    },
    [`& .${tooltipClasses.arrow}`]: {
        color: '#F6F6F6',
    },
}));

export const RowReasonDetail = ({
    reasonDetail,
    index,
    readonly,
    isLastItem,
    reasonId,
    handleMove,
    refetch,
}: RowReasonDetailProps) => {
    const { t } = useAppTranslation();
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);
    const [showEditDetailsPopUp, setShowEditDetailsPopUp] = useState<boolean>(false);
    const { deleteReasonDetail, handleUpdate, isLoading, isSuccess, reset } = useRowReasonDetail(
        reasonId,
        reasonDetail.id,
        () => {
            setShowDeleteConfirmation(false);
            refetch && refetch();
        }
    );

    const { appointmentReasonDetail, brands, years } = reasonDetail;

    const handleEdit = async () => {
        setShowEditDetailsPopUp(true);
    };

    const handleDelete = async () => {
        setShowDeleteConfirmation(true);
    };

    const moveUpHandler = async () => {
        if (index <= 0) return; // index bound detection
        handleMove(index, -1);
    };

    const moveDownHandler = () => {
        if (isLastItem) return; // index bound detection
        handleMove(index, 1);
    };

    const nameSpan = useMemo(() => {
        return appointmentReasonDetail.length > 40 ? (
            <STooltip title={appointmentReasonDetail} placement="top-start" arrow>
                <span>{`${appointmentReasonDetail.slice(0, 40)}...`}</span>
            </STooltip>
        ) : (
            <span>{appointmentReasonDetail}</span>
        );
    }, [appointmentReasonDetail]);

    const brandString = useMemo(() => {
        return brands.length === 0
            ? t('customAppointmentReasonsForms.details.allBrands')
            : brands.map((brand) => brand.customApptReasonDetailBrand).join(', ');
    }, [brands, t]);

    const brandSpan = useMemo(() => {
        return brandString.length > 42 ? (
            <STooltip title={brandString} placement="top-start" arrow>
                <span>{`${brandString.slice(0, 42)}...`}</span>
            </STooltip>
        ) : (
            <span>{brandString}</span>
        );
    }, [brandString]);

    const modelString = useMemo(() => {
        if (brands.length === 0) return t('customAppointmentReasonsForms.details.allDefault');

        const modelString = brands
            .filter((brand) => brand.models.length > 0)
            .map((brand) =>
                brand.models.map((model) => model.customApptReasonDetailModel).join(', ')
            )
            .join(', ');
        return modelString.length === 0
            ? t('customAppointmentReasonsForms.details.allDefault')
            : modelString;
    }, [brands, t]);

    const modelSpan = useMemo(() => {
        return modelString.length > 42 ? (
            <STooltip title={modelString} placement="top-start" arrow>
                <span>{`${modelString.slice(0, 42)}...`}</span>
            </STooltip>
        ) : (
            <span>{modelString}</span>
        );
    }, [modelString]);

    const yearString = useMemo(() => {
        return years.length === 0
            ? t('customAppointmentReasonsForms.details.allDefault')
            : years.join(', ');
    }, [years, t]);

    const yearSpan = useMemo(() => {
        return yearString.length > 30 ? (
            <STooltip title={yearString} placement="top-start" arrow>
                <span>{`${yearString.slice(0, 30)}...`}</span>
            </STooltip>
        ) : (
            <span>{yearString}</span>
        );
    }, [yearString]);

    return (
        <>
            <Row>
                <TableCell component="td" scope="row" style={{ paddingLeft: 30, width: '25%' }}>
                    <CellContentR>{nameSpan}</CellContentR>
                </TableCell>
                <TableCell component="td" scope="row" style={{ width: '25%' }}>
                    <CellContentI container>{brandSpan}</CellContentI>
                </TableCell>
                <TableCell component="td" scope="row" style={{ width: '20%' }}>
                    <CellContentI container>{modelSpan}</CellContentI>
                </TableCell>
                <TableCell component="td" scope="row" style={{ width: '20%' }}>
                    <CellContentI container>{yearSpan}</CellContentI>
                </TableCell>
                <TableCell component="td" scope="row" style={{ paddingRight: 30, width: '10%' }}>
                    <Box style={{ display: 'flex' }}>
                        <Button
                            disabled={readonly || index === 0}
                            cmosVariant={'typography'}
                            Icon={MoveUpIcon}
                            color={index === 0 ? 'transparent' : '#899198'}
                            onClick={moveUpHandler}
                        />
                        <Button
                            disabled={readonly || isLastItem}
                            cmosVariant={'typography'}
                            Icon={MoveDownIcon}
                            color={isLastItem ? 'transparent' : '#899198'}
                            onClick={moveDownHandler}
                        />
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={EditIcon}
                            color={Colors.Neutral5}
                            onClick={handleEdit}
                        />
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={DeleteIcon}
                            color={Colors.Neutral5}
                            onClick={handleDelete}
                        />
                        <div />
                    </Box>
                </TableCell>
                {showDeleteConfirmation && (
                    <DeleteConfirmationPopup
                        open={showDeleteConfirmation}
                        title={t('customAppointmentReasonsForms.details.deleteTitle')}
                        body={<span />}
                        cancel={t('customAppointmentReasonsForms.appointmentReasons.delete.cancel')}
                        confirm={t(
                            'customAppointmentReasonsForms.appointmentReasons.delete.confirm'
                        )}
                        onConfirm={deleteReasonDetail}
                        onClose={() => {
                            setShowDeleteConfirmation(false);
                        }}
                    />
                )}
                {showEditDetailsPopUp && (
                    <AddDetailsForm
                        open={showEditDetailsPopUp}
                        onClose={() => {
                            setShowEditDetailsPopUp(false);
                            reset();
                        }}
                        detail={reasonDetail}
                        onConfirm={handleUpdate}
                        onConfirmText={t('customAppointmentReasonsForms.details.form.editButton')}
                        title={t('customAppointmentReasonsForms.details.form.editTitle')}
                        isLoading={isLoading}
                        isSuccess={isSuccess}
                    />
                )}
            </Row>
        </>
    );
};

const useRowReasonDetail = (reasonId: string, detailId: string, onSuccess?: () => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();

    const {
        mutate: updateCustomAppointmentReason,
        isLoading,
        isSuccess,
        reset,
    } = useMutation(
        (body: CustomApptReasonDetailsUpdateRequest) =>
            AppointmentReasonsApi.updateCustomAppointmentReasonDetail(reasonId, detailId, body),
        {
            onSuccess: () => {
                toasters.success(
                    'success',
                    t(
                        'customAppointmentReasonsForms.appointmentReasons.notifications.updateSuccessTitle'
                    )
                );
                onSuccess && onSuccess();
            },
            onError: () => {
                toasters.danger(
                    'error',
                    t('customAppointmentReasonsForms.appointmentReasons.notifications.errorTitle')
                );
            },
            onSettled() {
                // onSuccess && onSuccess();
            },
        }
    );

    const handleUpdate = async (body: CustomApptDetailBody) => {
        const { CustomAppointmentReasonDetail, brands, years } = body;
        const updateBody: CustomApptReasonDetailsUpdateRequest = {
            customApptReasonDetail: CustomAppointmentReasonDetail || undefined,
            brands: brands.map((brand) => ({
                brand: brand.brand,
                models: brand.models.map((model) => ({ model: model.model })),
            })),
            years,
        };

        updateCustomAppointmentReason(updateBody);
    };

    const deleteReasonDetail = async () => {
        updateCustomAppointmentReason({ isDeleted: true });
    };

    return {
        deleteReasonDetail,
        isLoading,
        isSuccess,
        handleUpdate,
        reset,
    };
};
