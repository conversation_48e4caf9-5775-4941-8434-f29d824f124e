import { Box, Grid, Typography, useTheme } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentReasonsApi, {
    CustomAppointmentReasonDto,
    CustomApptReasonUpdateRequest,
} from 'api/appointmentReasons';
import { Button } from 'common/components/Button';
import {
    CircleIconWithBorder,
    DeleteIcon,
    EditIcon,
    HideIcon,
    MoveDownIcon,
    MoveUpIcon,
    ShowIcon,
} from 'common/components/Icons';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useState } from 'react';
import { Trans } from 'react-i18next';
import { CustomApptReasonsDetailsList } from '../CustomApptReasonsDetailsList';

export interface CustomAppointmentReasonItemProps {
    customAppointmentReason: CustomAppointmentReasonDto;
    index: number;
    isLastItem: boolean;
    refetch: () => void;
    handleMove: (index: number, direction: number) => void;
    readonly: boolean;
    onEdit: (reason: CustomAppointmentReasonDto) => void;
}

export const CustomAppointmentReasonItem = ({
    customAppointmentReason,
    index,
    handleMove,
    isLastItem,
    refetch,
    readonly,
    onEdit,
}: CustomAppointmentReasonItemProps) => {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);

    const { updateCustomAppointmentReason } = useCustomApptReasonItem(
        customAppointmentReason,
        refetch
    );

    const handleHide = async () => {
        updateCustomAppointmentReason({ isHidden: !customAppointmentReason.isHidden });
    };

    const handleDelete = async () => {
        updateCustomAppointmentReason({ isDeleted: true });
    };

    const moveUpHandler = async () => {
        if (index <= 0) return; // index bound detection
        handleMove(index, -1);
    };

    const moveDownHandler = () => {
        if (isLastItem) return; // index bound detection
        handleMove(index, 1);
    };

    return (
        <>
            <Grid
                container
                justifyContent="center"
                sx={{
                    border: '1px solid rgba(201, 205, 211, 0.5)',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    marginBottom: 5,
                }}
            >
                <Box
                    sx={(theme) => ({
                        display: 'grid',
                        gridTemplateColumns: 'auto 1fr auto auto auto 32px 32px',
                        alignItems: 'center',
                        background: theme.palette.neutral[3],
                        paddingLeft: '10px',
                        paddingRight: 3,
                        width: '100%',
                        height: 38,
                        '& .title': {
                            ...theme.typography.h5,
                            color: theme.palette.neutral[8],
                            fontWeight: 'bold',
                        },
                    })}
                >
                    <div style={{ marginTop: 4, marginRight: 2 }}>
                        <CircleIconWithBorder
                            size={IconSize.S}
                            fill={customAppointmentReason.color}
                        />
                    </div>
                    <div className="title">
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'start',
                                alignItems: 'center',
                                gap: 1, // 1 = 8px
                                '& .title': {
                                    width: 'max-content',
                                },
                            }}
                        >
                            <Typography
                                sx={(theme) => ({
                                    ...theme.typography.h5,
                                    color: readonly
                                        ? theme.palette.neutral[4]
                                        : theme.palette.neutral[8],
                                })}
                            >
                                {customAppointmentReason.name}
                            </Typography>
                            <Button
                                disabled={readonly}
                                color={theme.palette.neutral[6]}
                                cmosVariant={'typography'}
                                Icon={EditIcon}
                                onClick={() => onEdit(customAppointmentReason)}
                            />
                        </Box>
                    </div>
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={customAppointmentReason.isHidden ? HideIcon : ShowIcon}
                        color={customAppointmentReason.isHidden ? Colors.Neutral4 : Colors.CM1}
                        onClick={handleHide}
                    />
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={DeleteIcon}
                        color={Colors.Neutral4}
                        onClick={() => setShowDeleteConfirmation(true)}
                    />
                    <div />
                    {index === 0 ? (
                        <div /> // NOTE: this div serves a purpose since parent element is a grid and we need an actual element to take up space
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={MoveUpIcon}
                            color={Colors.Neutral4}
                            onClick={moveUpHandler}
                        />
                    )}
                    {isLastItem ? (
                        <div />
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={MoveDownIcon}
                            color={Colors.Neutral4}
                            onClick={moveDownHandler}
                        />
                    )}
                </Box>

                <CustomApptReasonsDetailsList
                    readonly={readonly}
                    customAppointmentReason={customAppointmentReason}
                    refetch={refetch}
                />
            </Grid>
            {showDeleteConfirmation && (
                <DeleteConfirmationPopup
                    open={showDeleteConfirmation}
                    title={t('customAppointmentReasonsForms.appointmentReasons.delete.title')}
                    body={
                        <Trans
                            i18nKey="customAppointmentReasonsForms.appointmentReasons.delete.description"
                            t={t}
                        />
                    }
                    cancel={t('customAppointmentReasonsForms.appointmentReasons.delete.cancel')}
                    confirm={t('customAppointmentReasonsForms.appointmentReasons.delete.confirm')}
                    onConfirm={handleDelete}
                    onClose={() => {
                        setShowDeleteConfirmation(false);
                    }}
                />
            )}
        </>
    );
};

const useCustomApptReasonItem = (
    customAppointmentReason: CustomAppointmentReasonDto,
    onSuccess: () => void
) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const { id, name } = customAppointmentReason;

    const {
        mutate: updateCustomAppointmentReason,
        isLoading,
        isSuccess,
    } = useMutation(
        (body: CustomApptReasonUpdateRequest) =>
            AppointmentReasonsApi.updateCustomAppointmentReason(id, body),
        {
            onSuccess: () => {
                toasters.success(
                    name,
                    t(
                        'customAppointmentReasonsForms.appointmentReasons.notifications.updateSuccessTitle'
                    )
                );
                onSuccess && onSuccess();
            },
            onError: () => {
                toasters.danger(
                    name,
                    t('customAppointmentReasonsForms.appointmentReasons.notifications.errorTitle')
                );
            },
        }
    );

    return {
        updateCustomAppointmentReason,
        isLoading,
        isSuccess,
    };
};
