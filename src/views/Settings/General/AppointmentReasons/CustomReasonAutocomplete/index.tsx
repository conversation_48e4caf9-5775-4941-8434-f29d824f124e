import {
    Autocomplete,
    Box,
    CircularProgress,
    ListItemButton,
    Paper as MPaper,
    Typography,
    autocompleteClasses,
    styled,
} from '@mui/material';
import TextField from '@mui/material/TextField';
import { useQuery } from '@tanstack/react-query';
import AppointmentReasonAPI from 'api/AppointmentReason';
import { AppointmentReasonDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import { DownIcon } from 'common/components/Icons/DownIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { rgba } from 'common/styles/ColorHelpers';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';
import AppointmentsReasonModal from 'views/Appointments/AppointmentStepTwo/AppointmentsReasonModal';
import { useCreateReasonMutation } from './helper';

type CustomReasonAutocompleteProps = {
    cacheKey: string;
    onChange: (item: AppointmentReasonDto) => void;
    disabled?: boolean;
    excludeItems?: AppointmentReasonDto[];
    value?: AppointmentReasonDto;
};

const emptyList = [] as AppointmentReasonDto[];

type CustomReasonAutocompleteItem =
    | {
          new: false;
          reason: AppointmentReasonDto;
      }
    | {
          new: true;
          name: string;
      };

const RequiredIndicator = styled('div')(({ theme }) => ({
    height: 8,
    width: 8,
    borderRadius: 100,
    background: theme.palette.primary.main,
    position: 'absolute',
    right: -2,
    top: -3,
}));

const SelectCustomPaper = styled(MPaper)(({ theme }) => ({
    marginTop: 5,
    border: `1px solid ${theme.palette.primary.main}`,
    borderRadius: 5,
    ...scrollbarStyle(),
    [`& .${autocompleteClasses.listbox}`]: {
        background: theme.palette.neutral[2],
        maxHeight: 140,
        overflowY: 'auto',
        ...scrollbarStyle(),
    },
    [`& .${autocompleteClasses.noOptions}`]: {
        background: theme.palette.neutral[2],
        padding: '6px 40px',
    },
}));

const CustomReasonAutocomplete = ({
    disabled,
    excludeItems = emptyList,
    cacheKey,
    onChange,
    value,
}: CustomReasonAutocompleteProps) => {
    const [showInspectionModal, setShowInspectionModal] = useState<boolean>(false);
    const [selectedDetail, setSelectedDetail] = useState<CustomReasonAutocompleteItem | null>(null);
    const { t } = useAppTranslation();

    const {
        open,
        setOpen,
        searchQuery,
        setSearchQuery,
        reasonsIsLoading,
        reasonsOptions,
        createReasonMutate,
    } = useCustomReasonAutocomplete(cacheKey, onChange, excludeItems);

    const SeeAllButton: ReactNode = (
        <Button
            blockMode
            cmosVariant={'filled'}
            label={t('appointments.step2.seeAll')}
            customStyles={{ height: 28 }}
            onClick={() => setShowInspectionModal(true)}
        />
    ) as ReactNode;

    const autocompleteIsLoading = reasonsIsLoading;

    useEffect(() => {
        if (value) setSelectedDetail({ new: false, reason: value });
    }, [value]);

    return (
        <>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    width: 380,
                    gap: 1,
                }}
            >
                <Box sx={{ display: 'flex' }}>
                    <Typography
                        sx={(theme) => ({
                            ...theme.typography.h6,
                            color: theme.palette.neutral[7],
                        })}
                    >
                        {t('customAppointmentReasonsForms.details.form.newReason')}
                    </Typography>
                    <Typography
                        sx={(theme) => ({
                            ...theme.typography.h6,
                            color: theme.palette.primary.light,
                        })}
                    >
                        &nbsp;*
                    </Typography>
                </Box>
                <Box sx={{ width: '380px', position: 'relative' }}>
                    <Autocomplete<CustomReasonAutocompleteItem>
                        id="reason-autocomplete"
                        autoComplete
                        autoHighlight
                        disabled={disabled}
                        open={open && !disabled}
                        options={autocompleteIsLoading ? [] : reasonsOptions}
                        inputValue={searchQuery}
                        value={selectedDetail}
                        loading={reasonsIsLoading}
                        loadingText={
                            <Box
                                sx={{
                                    height: 120,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}
                            >
                                <CircularProgress style={{ color: Colors.CM2 }} size={20} />
                            </Box>
                        }
                        noOptionsText={SeeAllButton}
                        renderInput={useCallback(
                            (params) => {
                                const { className, ...InputProps } = params.InputProps;
                                return (
                                    <TextField
                                        {...params}
                                        fullWidth
                                        placeholder={t(
                                            'customAppointmentReasonsForms.details.form.newReasonPlaceholder'
                                        )}
                                        InputProps={{
                                            ...InputProps,
                                            endAdornment: params.InputProps.endAdornment,
                                            sx: {
                                                height: '32px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                input: {
                                                    '&::placeholder': {
                                                        fontSize: 11,
                                                        opacity: 1,
                                                        color: '#899198',
                                                    },
                                                },
                                            },
                                        }}
                                        sx={(theme) => ({
                                            ...theme.typography.h6Roboto,
                                            backgroundColor: theme.palette.neutral[2],
                                            color: theme.palette.neutral[7],
                                        })}
                                    />
                                );
                            },
                            [t]
                        )}
                        PaperComponent={SelectCustomPaper}
                        popupIcon={<DownIcon fill={Colors.Neutral7} />}
                        renderOption={(props, option) => {
                            if (option.new) {
                                return (
                                    <ListItemButton
                                        disableRipple
                                        component="li"
                                        {...props}
                                        sx={(theme) => ({
                                            display: 'flex',
                                            justifyContent: 'center',
                                            padding: '10px 12px !important',
                                            backgroundColor: `${theme.palette.neutral[2]} !important`,
                                        })}
                                    >
                                        <PlusIcon size={15} style={{ marginRight: 2 }} />
                                        <div>{option.name}</div>
                                    </ListItemButton>
                                );
                            } else {
                                if (option.reason.id === 'SeeAllButton')
                                    return (
                                        <ListItemButton
                                            disableRipple
                                            component="li"
                                            {...props}
                                            sx={(theme) => ({
                                                display: 'flex',
                                                justifyContent: 'center',
                                                padding: '0px 40px !important',
                                                backgroundColor: `${theme.palette.neutral[2]} !important`,
                                            })}
                                        >
                                            {SeeAllButton}
                                        </ListItemButton>
                                    );
                                return (
                                    <ListItemButton
                                        disableRipple
                                        component="li"
                                        {...props}
                                        sx={(theme) => ({
                                            display: 'flex',
                                            justifyContent: 'center',
                                            marginLeft: 2,
                                            padding: '10px 0px !important',
                                            borderBottom: `1px solid`,
                                            borderImage: `linear-gradient(to right, ${rgba(
                                                theme.palette.neutral[4],
                                                0.5
                                            )} 95%, transparent 10%) 100% 1 100% 1`,
                                            '&:last-child': {
                                                borderBottom: 'none',
                                            },
                                            backgroundColor: `${theme.palette.neutral[2]} !important`,
                                        })}
                                    >
                                        <PlusIcon size={15} style={{ marginRight: 2 }} />
                                        {option.reason.name}
                                    </ListItemButton>
                                );
                            }
                        }}
                        isOptionEqualToValue={(_option, _value) => true}
                        getOptionLabel={(option) => (option.new ? option.name : option.reason.name)}
                        onChange={(_e, value) => {
                            setSelectedDetail(value);
                            if (!value) {
                                onChange && onChange({ id: '', name: '' });
                                return;
                            }
                            if (value.new) {
                                createReasonMutate({
                                    name: value.name,
                                });
                            } else {
                                onChange && onChange(value.reason);
                            }
                        }}
                        onInputChange={(_e, value, reason) => {
                            setSearchQuery(value);
                        }}
                        onOpen={() => setOpen(true)}
                        onClose={() => {
                            setOpen(false);
                        }}
                    />
                    <RequiredIndicator />
                </Box>
            </Box>
            <AppointmentsReasonModal
                open={showInspectionModal}
                onClose={() => setShowInspectionModal(false)}
                onSelected={(values) => {
                    const value = values[0];
                    if (!value) setSelectedDetail(null);
                    else setSelectedDetail({ new: false, reason: value });
                    onChange && onChange(value);
                }}
                title={'customAppointmentReasonsForms.details.form.modalTitle'}
                description={'customAppointmentReasonsForms.details.form.modalDescription'}
            />
        </>
    );
};

export default CustomReasonAutocomplete;

const useCustomReasonAutocomplete = (
    cacheKey: string,
    onChange: (item: AppointmentReasonDto) => void,
    excludeItems?: AppointmentReasonDto[]
) => {
    const [open, setOpen] = useState<boolean>(false);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const normalizedQuery = useMemo(() => searchQuery.trim(), [searchQuery]);
    const [debouncedQuery] = useDebounce(normalizedQuery, 100);

    const {
        data: reasonsData,
        isFetching: reasonsIsFetching,
        fetchStatus,
    } = useQuery(
        ['appointment', 'customReasons', cacheKey, debouncedQuery],
        () => AppointmentReasonAPI.getFilterReasons(debouncedQuery),
        {
            cacheTime: 0,
            enabled: debouncedQuery !== '',
        }
    );
    const reasonsIsLoading = reasonsIsFetching && fetchStatus === 'fetching';
    const { data: frequentReasons, isLoading: frequentsAreLoading } = useQuery(
        ['appointment', 'customReasons', 'frequent', cacheKey],
        AppointmentReasonAPI.getFrequentReasons,
        {
            cacheTime: 30000,
            enabled: open,
            staleTime: 10000,
        }
    );

    const frequents = useMemo(
        () =>
            (frequentReasons || []).filter(
                (item) => excludeItems?.findIndex((excluded) => excluded.id === item?.id) === -1
            ),
        [frequentReasons, excludeItems]
    );

    const filterReasons = useMemo(
        () =>
            (reasonsData || []).filter(
                (item) =>
                    excludeItems?.findIndex(
                        (excluded) => item !== null && excluded.id === item.id
                    ) === -1
            ) || [],
        [reasonsData, excludeItems]
    );
    const reasonsOptions: CustomReasonAutocompleteItem[] = useMemo(() => {
        if (debouncedQuery.length) {
            if (filterReasons.length) {
                return [
                    {
                        new: false,
                        reason: { id: 'SeeAllButton', name: `${debouncedQuery}-SeeAllButton` },
                    } as any,
                    ...filterReasons.map((item) => ({
                        new: false,
                        reason: item,
                    })),
                ];
            } else {
                return [
                    {
                        new: false,
                        reason: { id: 'SeeAllButton', name: `${debouncedQuery}-SeeAllButton` },
                    },
                    {
                        new: true,
                        name: debouncedQuery,
                    },
                ];
            }
        } else {
            return [
                {
                    new: false,
                    reason: { id: 'SeeAllButton', name: `${debouncedQuery}-SeeAllButton` },
                } as any,
                ...frequents.map((item) => ({
                    new: false,
                    reason: item,
                })),
            ];
        }
    }, [filterReasons, frequents, debouncedQuery]);

    const { mutate: createReasonMutate } = useCreateReasonMutation(
        AppointmentReasonAPI.createReason,
        (data) => {
            onChange && onChange(data);
        }
    );

    return {
        open,
        setOpen,
        searchQuery,
        setSearchQuery,
        reasonsIsLoading,
        reasonsOptions,
        createReasonMutate,
    };
};
