import { Box, Typography, styled } from '@mui/material';
import { CustomAppointmentReasonDetailsDto, CustomApptDetailBody } from 'api/appointmentReasons';
import { AppointmentReasonDto } from 'api/appointments';
import { Button } from 'common/components/Button';
import CancelModal from 'common/components/Popups/CancelModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import { Modal } from '../../../../../common/components/Modal';
import CustomReasonAutocomplete from '../CustomReasonAutocomplete';
import MultipleBrandPicker from './MultipleBrandPicker';
import MultipleModelPicker, { BrandModelsData } from './MultipleModelPicker';
import MultipleYearPicker from './MultipleYearPicker';

export interface AddDetailsFormProps {
    open: boolean;
    onClose: () => void;
    detail?: CustomAppointmentReasonDetailsDto;
    onConfirm: (body: CustomApptDetailBody) => void;
    title: string;
    onConfirmText: string;
    isLoading: boolean;
    isSuccess: boolean;
}

const Row = styled('div')({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
});

export const AddDetailsForm = ({
    open,
    onClose,
    detail,
    onConfirm,
    title,
    onConfirmText,
    isLoading,
    isSuccess,
}: AddDetailsFormProps) => {
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);
    const [reasonDetail, setReasonDetail] = useState<CustomApptDetailBody>(
        detail
            ? {
                  CustomAppointmentReasonDetail: detail.appointmentReasonDetail,
                  brands: detail.brands.map((b) => ({
                      brand: b.customApptReasonDetailBrand,
                      models: b.models.map((m) => ({ model: m.customApptReasonDetailModel })),
                  })),
                  years: detail.years,
              }
            : {
                  CustomAppointmentReasonDetail: '',
                  brands: [],
                  years: [],
              }
    );
    const [reasonDetailName, setReasonDetailName] = useState<AppointmentReasonDto | null>(null);
    const [brands, setBrands] = useState<string[]>([]);
    const [models, setModels] = useState<BrandModelsData[]>([]);
    const [years, setYears] = useState<string[]>([]);

    const { t } = useAppTranslation();

    const handleReasonDetailChange = (reasonDetail?: AppointmentReasonDto) => {
        setReasonDetailName(reasonDetail || null);
        setReasonDetail((prev) => ({
            ...prev,
            CustomAppointmentReasonDetail: reasonDetail?.name || '',
        }));
    };

    const handleBrandChange = (value: string[]) => {
        setBrands(value);
        setModels([]);
        setYears([]);
        setReasonDetail((prev) => ({
            ...prev,
            brands: value.map((b) => ({ brand: b, models: [] })),
        }));
    };

    const handleModelChange = (value: BrandModelsData[]) => {
        setModels(value);
        setYears([]);
        const brandModels = value.map((b) => ({
            brand: b.name,
            models: b.models.map((m) => ({ model: m })),
        }));
        setReasonDetail((prev) => ({
            ...prev,
            brands: brandModels,
            years: [],
        }));
    };

    const handleYearChange = (value: string[]) => {
        setYears(value);
        setReasonDetail((prev) => ({ ...prev, years: value }));
    };

    const handleCreateReasonDetail = () => {
        onConfirm(reasonDetail);
    };

    const handleClose = () => {
        if (reasonDetail != null && reasonDetail.CustomAppointmentReasonDetail != '')
            setShowDeleteConfirmation(true);
        else onClose();
    };

    useEffect(() => {
        if (!isLoading && isSuccess) {
            onClose();
        }
    }, [isLoading, isSuccess, onClose]);

    useEffect(() => {
        if (detail?.id) {
            setReasonDetailName({
                id: detail.id,
                name: detail.appointmentReasonDetail,
            });
            setReasonDetail({
                CustomAppointmentReasonDetail: detail.appointmentReasonDetail,
                brands: detail.brands.map((b) => ({
                    brand: b.customApptReasonDetailBrand,
                    models: b.models.map((m) => ({ model: m.customApptReasonDetailModel })),
                })),
                years: detail.years,
            });
            if (detail.brands) {
                setBrands(detail.brands.map((b) => b.customApptReasonDetailBrand));
                setModels(
                    detail.brands.map((b) => ({
                        name: b.customApptReasonDetailBrand,
                        models: b.models.map((x) => x.customApptReasonDetailModel),
                    }))
                );
            }

            if (detail.years) {
                setYears(detail.years);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [detail]);

    useEffect(() => {
        return () => {
            setReasonDetail({
                CustomAppointmentReasonDetail: '',
                brands: [],
                years: [],
            });
            setReasonDetailName(null);
            setBrands([]);
            setModels([]);
            setYears([]);
        };
    }, []);

    return (
        <>
            <Modal open={open}>
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: 2.5,
                        paddingY: 6,
                        paddingX: '86px',
                        width: 960,
                    }}
                >
                    {/* Title & buttons */}
                    <Row style={{ marginBottom: 40, display: 'flex', alignItems: 'center' }}>
                        <Typography
                            sx={(theme) => ({
                                ...theme.typography.h4,
                                color: theme.palette.neutral[8],
                            })}
                        >
                            {title}
                        </Typography>
                        <Box
                            sx={{
                                display: 'flex',
                                gap: 1,
                            }}
                        >
                            <Button
                                customStyles={{ width: 164 }}
                                label={t('commonLabels.cancel')}
                                onClick={handleClose}
                                cmosVariant={'filled'}
                                color={Colors.Neutral3}
                                cmosSize={'medium'}
                            />
                            <Button
                                data-test-id="save-button"
                                customStyles={{ width: 250 }}
                                disabled={reasonDetailName == null || reasonDetailName.name == ''}
                                label={onConfirmText}
                                onClick={handleCreateReasonDetail}
                                cmosVariant={'filled'}
                                color={Colors.Success}
                                cmosSize={'medium'}
                            />
                        </Box>
                    </Row>

                    {/* 1st Row */}
                    <Row
                        style={{
                            display: 'flex',
                            gap: 28,
                            justifyContent: 'start',
                        }}
                    >
                        <CustomReasonAutocomplete
                            cacheKey="default"
                            onChange={handleReasonDetailChange}
                            value={reasonDetailName || undefined}
                        />
                        <MultipleBrandPicker value={brands} onChange={handleBrandChange} />
                    </Row>
                    <Row
                        style={{
                            display: 'flex',
                            gap: 28,
                            justifyContent: 'start',
                        }}
                    >
                        <MultipleModelPicker
                            value={models}
                            onChange={handleModelChange}
                            filterBrands={brands}
                        />
                        <MultipleYearPicker
                            showEmptyList={
                                models.map((x) => x.models.length).reduce((a, b) => a + b, 0) === 0
                            }
                            value={years}
                            onChange={handleYearChange}
                        />
                    </Row>
                </Box>
            </Modal>
            {showDeleteConfirmation && (
                <CancelModal
                    open={showDeleteConfirmation}
                    title={t('customAppointmentReasonsForms.details.form.cancelTitle')}
                    onCancel={onClose}
                    onClose={() => {
                        setShowDeleteConfirmation(false);
                    }}
                />
            )}
        </>
    );
};
