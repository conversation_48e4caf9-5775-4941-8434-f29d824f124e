import { Box, SelectChangeEvent, Typography, styled } from '@mui/material';
import { useBrandsWithModels } from 'api/customers';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import ArrowTooltip from 'common/components/Tooltip';
import { SMenuItem } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ReactNode, useCallback, useMemo } from 'react';
import ListSubheader from '../../CustomReasonSelectMulti/SListSubheader';
import SSelectMulti from '../../CustomReasonSelectMulti/SSelectMulti';

export type BrandModelsData = {
    name: string;
    models: string[];
};

export type MultipleModelPickerProps = {
    value?: BrandModelsData[];
    onChange: (values: BrandModelsData[]) => void;
    filterBrands: string[];
};

const ModelPickerHeaderMenu = styled(Box)(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[6],
    display: 'flex',
    fontWeight: 500,
    justifyContent: 'center',
    padding: '8px 0 8px',
}));

/**
 * @deprecated use the other MultipleModelPicker component instead (the on in views/Components).
 */
const MultipleModelPicker = ({ value, onChange, filterBrands }: MultipleModelPickerProps) => {
    const { t } = useAppTranslation();

    const brands = useBrandsWithModels();

    const models = useMemo(
        () =>
            brands
                .filter((x) => filterBrands.includes(x.name))
                .map((x) => x.models.map((m) => ({ model: m, brand: x.name })))
                .flat(),
        [brands, filterBrands]
    );

    const modelOptions: OptionData<{ model: string; brand: string }>[] = useMemo(() => {
        const options = models.map((m) => ({
            label: m.model,
            value: m,
            disabled: false,
        }));
        return options;
    }, [models]);

    const selectedModels = useMemo(() => {
        if (!value) return [];

        return modelOptions.filter((x) => {
            const brand = value.find((i) => i.name === x.value.brand);
            if (!brand) return false;
            return brand.models.includes(x.value.model);
        });
    }, [value, modelOptions]);

    const selectedModelsList = useMemo(
        () => selectedModels.map((x) => x.value.model),
        [selectedModels]
    );

    const renderValue = useCallback(
        (v: string[]) => {
            const itemListString = selectedModelsList.join(', ');

            const stringElement =
                itemListString.length > 60 ? (
                    <ArrowTooltip content={itemListString} position="top-start">
                        <span>{`${itemListString.slice(0, 60)}...`}</span>
                    </ArrowTooltip>
                ) : (
                    <span>{itemListString}</span>
                );

            return stringElement;
        },
        [selectedModelsList, value]
    );

    const handleOnChange = useCallback(
        (e: SelectChangeEvent<string[]>) => {
            if (e.target.value instanceof Array) {
                const value = e.target.value;
                const newValue: BrandModelsData[] = filterBrands.map((brand) => ({
                    name: brand,
                    models: value.filter(
                        (x) => modelOptions.find((m) => m.value.model === x)?.value.brand === brand
                    ),
                }));
                onChange(newValue);
            }
        },
        [filterBrands, modelOptions, onChange]
    );

    const items = useMemo(
        () =>
            modelOptions.map((model) => (
                <SMenuItem
                    key={`${model.value.brand} - ${model.value.model}`}
                    value={model.value.model}
                >
                    {selectedModelsList.includes(model.value.model) ? (
                        <CheckBoxIcon />
                    ) : (
                        <UncheckBoxIcon />
                    )}
                    {model.value.model}
                </SMenuItem>
            )),
        [modelOptions, selectedModelsList]
    );

    const groupedItems = useMemo(() => {
        const groupedItemsArray: ReactNode[] = [];
        filterBrands?.map((brand) => {
            groupedItemsArray.push(
                <ListSubheader key={brand} muiSkipListHighlight>
                    {brand}
                </ListSubheader>
            );
            groupedItemsArray.push(
                ...modelOptions
                    .filter((item) => item.value.brand === brand)
                    .map((item) => (
                        <SMenuItem
                            key={`${item.value.brand} - ${item.value.model}`}
                            value={item.value.model}
                        >
                            {selectedModelsList.includes(item.value.model) ? (
                                <CheckBoxIcon />
                            ) : (
                                <UncheckBoxIcon />
                            )}
                            {item.value.model}
                        </SMenuItem>
                    ))
            );
        });
        return groupedItemsArray;
    }, [filterBrands, selectedModelsList, modelOptions]);

    const MultiSelect = useMemo(
        () => (
            <SSelectMulti<string>
                renderValue={renderValue}
                style={{ width: 380 }}
                onChange={handleOnChange}
                placeholder={t('customAppointmentReasonsForms.details.form.allModelsSelected')}
                value={selectedModelsList}
            >
                <ModelPickerHeaderMenu>
                    <span>{t('settings.prospections.maintenance.form.selectSpecificBrands')}</span>
                </ModelPickerHeaderMenu>
                {filterBrands === undefined || filterBrands.length <= 1 ? items : groupedItems}
            </SSelectMulti>
        ),
        [renderValue, handleOnChange, selectedModelsList, items, groupedItems, filterBrands]
    );

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                width: 380,
                gap: 1,
            }}
        >
            <Typography
                sx={(theme) => ({
                    ...theme.typography.h6,
                    color: theme.palette.neutral[7],
                })}
            >
                {t('customAppointmentReasonsForms.details.form.model')}
            </Typography>
            {MultiSelect}
        </Box>
    );
};

export default MultipleModelPicker;
