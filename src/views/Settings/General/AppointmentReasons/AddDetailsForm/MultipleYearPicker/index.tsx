import { Box, SelectChangeEvent, Typography, styled } from '@mui/material';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import ArrowTooltip from 'common/components/Tooltip';
import { SMenuItem } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import { useCallback, useMemo } from 'react';
import SSelectMulti from '../../CustomReasonSelectMulti/SSelectMulti';

export type MultipleYearPickerProps = {
    value?: string[];
    onChange: (values: string[]) => void;
    showEmptyList: boolean;
};

const YearPickerHeaderMenu = styled(Box)(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[6],
    display: 'flex',
    fontWeight: 500,
    justifyContent: 'center',
    padding: '8px 0 8px',
}));

const YEARS = (() => {
    const v: string[] = [];
    const year = moment().year() + 1;
    for (let y = 1950; y <= year; y++) v.push(y + '');
    return v.reverse();
})();
const YEAR_OPTIONS: OptionData<string>[] = YEARS.map((x) => ({ value: x, label: x }));

/**
 * @deprecated use the other MultipleYearPicker component instead (the on in views/Components).
 */
const MultipleYearPicker = ({ value, onChange, showEmptyList }: MultipleYearPickerProps) => {
    const { t } = useAppTranslation();

    // we *should* ask backend for a list of years, but currently backend returns list of years per-model
    // but it's going to be the same every time because we don't actually store list of years anywhere,
    // we just return hard-coded list of numbers. Ultimately I think it's better to just display a static list of numbers for now
    // than ask backend to give the same list of numbers
    // it's not the "right" solution but it's a solution that makes more sense with current business requirements

    const selectedYears = useMemo(
        () => YEAR_OPTIONS.filter((x) => value?.includes(x.value)),
        [value]
    );

    const yearOptions = useMemo(() => {
        const options = showEmptyList ? [] : YEAR_OPTIONS;
        return options;
    }, [showEmptyList]);

    const selectedYearString = useMemo(() => {
        return selectedYears.map((x) => x.label).join(', ');
    }, [selectedYears]);

    const renderValue = useCallback(
        (v: string[]) => {
            const stringElement =
                selectedYearString.length > 30 ? (
                    <ArrowTooltip content={selectedYearString} position="top-start">
                        <span>{`${selectedYearString.slice(0, 30)}...`}</span>
                    </ArrowTooltip>
                ) : (
                    <span>{selectedYearString}</span>
                );

            return stringElement;
        },
        [selectedYearString]
    );

    const handleOnChange = useCallback(
        (e: SelectChangeEvent<string[]>) => {
            if (e.target.value instanceof Array) {
                const newValue = e.target.value;
                onChange(newValue);
            }
        },
        [onChange]
    );

    const items = useMemo(
        () =>
            yearOptions.map((year) => (
                <SMenuItem key={year.value} value={year.value}>
                    {selectedYearString.includes(year.value) ? (
                        <CheckBoxIcon />
                    ) : (
                        <UncheckBoxIcon />
                    )}
                    {year.label}
                </SMenuItem>
            )),
        [yearOptions, selectedYearString]
    );

    const MultiSelect = useMemo(
        () => (
            <SSelectMulti<string>
                renderValue={renderValue}
                style={{ width: 380 }}
                onChange={handleOnChange}
                placeholder={t('customAppointmentReasonsForms.details.form.allYearsSelected')}
                value={selectedYears.map((x) => x.value)}
            >
                <YearPickerHeaderMenu>
                    <span>{t('settings.prospections.maintenance.form.selectSpecificModels')}</span>
                </YearPickerHeaderMenu>
                {items}
            </SSelectMulti>
        ),
        [renderValue, handleOnChange, selectedYears, items]
    );

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                width: 380,
                gap: 1,
            }}
        >
            <Typography
                sx={(theme) => ({
                    ...theme.typography.h6,
                    color: theme.palette.neutral[7],
                })}
            >
                {t('customAppointmentReasonsForms.details.form.year')}
            </Typography>
            {MultiSelect}
        </Box>
    );
};

export default MultipleYearPicker;
