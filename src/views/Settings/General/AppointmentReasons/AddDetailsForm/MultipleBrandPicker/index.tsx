import { Box, SelectChangeEvent, Typography, styled } from '@mui/material';
import { useBrandsWithModels } from 'api/customers';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import Arrow<PERSON>ooltip from 'common/components/Tooltip';
import { SMenuItem } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo } from 'react';
import SSelectMulti from '../../CustomReasonSelectMulti/SSelectMulti';

export type MultipleBrandPickerProps = {
    value?: string[];
    onChange: (values: string[]) => void;
    noTitle?: boolean;
    fullWidth?: boolean;
};

const BrandPickerHeaderMenu = styled(Box)(({ theme }) => ({
    ...theme.typography.h7Inter,
    color: theme.palette.neutral[6],
    display: 'flex',
    fontWeight: 500,
    justifyContent: 'center',
    padding: '8px 0 8px',
}));

/**
 * @deprecated use the other MultipleBrandPicker component instead (the on in views/Components).
 */
const MultipleBrandPicker = ({
    value,
    onChange,
    noTitle = false,
    fullWidth = false,
}: MultipleBrandPickerProps) => {
    const { t } = useAppTranslation();

    const brands = useBrandsWithModels();

    const brandOptions = useMemo(
        () => brands.map((b) => ({ label: b.name, value: b.name, disabled: false })),
        [brands]
    );

    const selectedBrand = useMemo(
        () =>
            value?.length
                ? brandOptions.filter((b) => value.includes(b.value)).map((b) => b.value)
                : [],
        [value, brandOptions]
    );

    const renderValue = useCallback(
        (v: string[]) => {
            const itemList = brands
                .filter((data) => value?.includes(data.name))
                .map((data) => data.name);

            const itemListString = itemList.join(', ');

            const stringElement =
                itemListString.length > 60 ? (
                    <ArrowTooltip content={itemListString} position="top-start">
                        <span>{`${itemListString.slice(0, 60)}...`}</span>
                    </ArrowTooltip>
                ) : (
                    <span>{itemListString}</span>
                );

            return stringElement;
        },
        [brands, value]
    );

    const handleOnChange = useCallback(
        (e: SelectChangeEvent<string[]>) => {
            if (e.target.value instanceof Array) {
                const newValue = e.target.value;
                onChange(newValue);
            }
        },
        [onChange]
    );

    const items = useMemo(
        () =>
            brandOptions.map((brand) => (
                <SMenuItem key={brand.value} value={brand.value}>
                    {selectedBrand.includes(brand.value) ? <CheckBoxIcon /> : <UncheckBoxIcon />}
                    {brand.label}
                </SMenuItem>
            )),
        [brandOptions, selectedBrand]
    );

    const MultiSelect = useMemo(
        () => (
            <SSelectMulti<string>
                renderValue={renderValue}
                style={{ width: fullWidth ? '100%' : 380 }}
                onChange={handleOnChange}
                placeholder={t('customAppointmentReasonsForms.details.form.allBrandsSelected')}
                value={selectedBrand}
            >
                <BrandPickerHeaderMenu>
                    <span>{t('settings.prospections.maintenance.form.selectSpecificBrands')}</span>
                </BrandPickerHeaderMenu>
                {items}
            </SSelectMulti>
        ),
        [renderValue, handleOnChange, selectedBrand, items]
    );

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                width: fullWidth ? '100%' : 380,
                gap: 1,
            }}
        >
            {!noTitle && (
                <Typography
                    sx={(theme) => ({
                        ...theme.typography.h6,
                        color: theme.palette.neutral[7],
                    })}
                >
                    {t('customAppointmentReasonsForms.details.form.brand')}
                </Typography>
            )}
            {MultiSelect}
        </Box>
    );
};

export default MultipleBrandPicker;
