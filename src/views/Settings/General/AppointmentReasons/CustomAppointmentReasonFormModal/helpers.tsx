import { Box, styled } from '@mui/material';
import { CustomAppointmentReasonDto } from 'api/appointmentReasons';
import { IconProps } from 'common/components/Icons/Icon';

export type FormValue = {
    id?: string;
    name: string;
    // this can be an empty string
    color: string;
};

export const getCleanFormValue = (value?: CustomAppointmentReasonDto): FormValue => {
    if (value) {
        return { ...value };
    }

    return {
        name: '',
        color: '',
    };
};

export const COLOR_OPTIONS: string[][] = [
    ['#ADD8E6', '#FFDAB9', '#98FF98', '#D3D3D3', '#FFFDD0'],
    ['#B0E0E6', '#FFB07C', '#B2F7EF', '#C0C0C0', '#FFFFF0'],
    ['#C5DCE7', '#FFAB76', '#A0D6B4', '#DCDCDC', '#F5F5DC'],
    ['#D1E7F2', '#FF9E80', '#D2E9A1', '#E8E8E8', '#FAF0E6'],
    ['#ADD8E6', '#FFD2B5', '#5DC1B9', '#BFC9CA', '#F3E5AB'],
    ['#AEC6CF', '#FFCBA4', '#ACE1AF', '#D3D3D3', '#FAFAD2'],
    ['#B3CDE0', '#FFA07A', '#9DC183', '#A9A9A9', '#FAF0E6'],
    ['#D6EAF8', '#FFE1C6', '#B9E8D3', '#D7DBDD', '#EEDC82'],
    ['#CFE3EF', '#F9D2C1', '#C3F1C3', '#C1C1C1', '#FFEBCD'],
    ['#AFDBE7', '#FFC3A0', '#BEE0B3', '#F5F5F5', '#FFF8DC'],
];

export const ContentBox = styled(Box)(() => ({
    width: 961,
    height: 281 - 47,
    paddingTop: 47,
    paddingLeft: 86,
    paddingRight: 86,
}));

export const ColorsContainerBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    height: 212,
    overflowY: 'scroll',
    position: 'relative',
    '&::-webkit-scrollbar': {
        width: '5px',
    },
    '&::-webkit-scrollbar-track': {
        background: theme.palette.neutral[4],
        borderRadius: '10px',
    },
    '&::-webkit-scrollbar-thumb': {
        background: theme.palette.neutral[6],
        borderRadius: '10px',
    },
}));

export const ColorsRowBox = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    '& button': {
        padding: 13,
    },
}));

export const CircleDiv = (props: IconProps) => (
    <div
        style={{
            height: 20,
            width: 20,
            backgroundColor: props.fill,
            border: props.fill === '#FFFFFF' ? '1px solid #DDDDDD' : undefined,
            borderRadius: '50%',
            display: 'inline-block',
        }}
    />
);
