import { Box, Grid, IconButton, Typography, useTheme } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import AppointmentReasonsApi, { CustomAppointmentReasonDto } from 'api/appointmentReasons';
import Dropdown from 'common/components/Inputs/Dropdown';
import CancelModal from 'common/components/Popups/CancelModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { OptionStyle } from 'common/styles/OptionStyle';
import { useEffect, useMemo, useState } from 'react';
import { Button } from '../../../../../common/components/Button';
import { TextField } from '../../../../../common/components/Inputs';
import { Modal } from '../../../../../common/components/Modal';
import {
    CircleDiv,
    COLOR_OPTIONS,
    ColorsContainerBox,
    ColorsRowBox,
    ContentBox,
    FormValue,
    getCleanFormValue,
} from './helpers';

interface CustomAppointmentReasonFormModalProps {
    isOpen: boolean;
    colorsInUse: string[];
    value?: CustomAppointmentReasonDto;
    onClose: () => void;
    onSave?: () => void;
}

const CustomAppointmentReasonFormModal = ({
    isOpen,
    colorsInUse,
    value,
    onClose,
    onSave,
}: CustomAppointmentReasonFormModalProps) => {
    const theme = useTheme();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [showCancelConfirmation, setShowCancelConfirmation] = useState<boolean>(false);
    const [formValue, setFormValue] = useState<FormValue>(getCleanFormValue(value));

    useEffect(() => {
        //cleanup on close
        if (!isOpen) {
            // let modal close be animated til the end, before cleaning form
            setTimeout(() => setFormValue({ name: '', color: '', id: undefined }), 150);
        }
    }, [isOpen]);

    useEffect(() => {
        setFormValue(getCleanFormValue(value));
    }, [value]);

    const canSave = useMemo(() => {
        if (value !== undefined) {
            return formValue.name.trim() !== value.name || formValue.color !== value.color;
        } else {
            return formValue.name.trim();
        }
    }, [value, formValue.name, formValue.color]);

    const handleClose = () => {
        if (value !== undefined) {
            // anything changed
            if (formValue.name.trim() !== value.name || formValue.color !== value.color) {
                setShowCancelConfirmation(true);
                return;
            }
        } else {
            // anything changed
            if (formValue.name.trim() || formValue.color !== '') {
                setShowCancelConfirmation(true);
                return;
            }
        }

        onClose();
    };

    const saveForm = useMutation(
        () => {
            if (value !== undefined) {
                return AppointmentReasonsApi.updateCustomAppointmentReason(value.id, {
                    name: formValue.name,
                    color: formValue.color,
                });
            } else {
                return AppointmentReasonsApi.createCustomAppointmentReason({
                    name: formValue.name,
                    color: formValue.color,
                });
            }
        },
        {
            onSuccess: () => {
                if (value !== undefined) {
                    toasters.success(
                        formValue.name,
                        t(
                            'customAppointmentReasonsForms.appointmentReasons.notifications.updateSuccessTitle'
                        )
                    );
                } else {
                    toasters.success(
                        formValue.name,
                        t(
                            'customAppointmentReasonsForms.appointmentReasons.notifications.createSuccessTitle'
                        )
                    );
                }
                onSave && onSave();
            },
            onError: () => {
                toasters.danger(
                    formValue.name,
                    t('customAppointmentReasonsForms.appointmentReasons.notifications.errorTitle')
                );
            },
        }
    );

    return (
        <>
            <Modal open={isOpen}>
                <ContentBox>
                    <Box display={'flex'} flexDirection="row" justifyContent={'space-between'}>
                        <Typography variant="h4Inter" color="neutral.7">
                            {t(
                                `customAppointmentReasonsForms.form.${
                                    value?.id ? 'editTitle' : 'newTitle'
                                }`
                            )}
                        </Typography>
                        <Box justifyContent="end" display="flex" flexDirection="row" gap={1.25}>
                            <Button
                                w={'md'}
                                cmosSize={'medium'}
                                color={theme.palette.neutral[3]}
                                cmosVariant={'filled'}
                                label={t('commonLabels.cancel')}
                                onClick={handleClose}
                            />
                            <Button
                                w={227}
                                cmosSize={'medium'}
                                color={theme.palette.success.main}
                                cmosVariant={'filled'}
                                label={t(
                                    `customAppointmentReasonsForms.form.${
                                        value?.id ? 'editButton' : 'newButton'
                                    }`
                                )}
                                disabled={!canSave}
                                onClick={() => saveForm.mutate()}
                            />
                        </Box>
                    </Box>
                    <Grid container columnSpacing={6} style={{ marginTop: 50 }}>
                        <Grid item xs={6}>
                            <TextField
                                label={t('customAppointmentReasonsForms.form.nameCaption')}
                                placeholder={t(
                                    'customAppointmentReasonsForms.form.namePlaceholder'
                                )}
                                name="custom-reason"
                                isRequired
                                maxLength={40}
                                value={formValue.name}
                                onChange={(event) => {
                                    setFormValue({
                                        ...formValue,
                                        name: event.target.value.trimStart(),
                                    });
                                }}
                                showValidationIndicators
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <Dropdown<string>
                                name="color"
                                label={t('customAppointmentReasonsForms.form.colorCaption')}
                                placeholder={t(
                                    'customAppointmentReasonsForms.form.colorPlaceholder'
                                )}
                                optionStyle={OptionStyle.icons}
                                isSearchable={false}
                                cmosVariant="default"
                                value={
                                    formValue.color
                                        ? {
                                              label: '',
                                              value: formValue.color,
                                              icon: CircleDiv,
                                              color: formValue.color,
                                          }
                                        : undefined
                                }
                                onChange={(event) => {
                                    if (event === null) return;
                                    setFormValue({
                                        ...formValue,
                                        color: event.value,
                                    });
                                }}
                                CustomMenu={(props) => {
                                    return (
                                        <ColorsContainerBox>
                                            {COLOR_OPTIONS.map((palette, optIdx) => (
                                                <ColorsRowBox key={`lst-${optIdx}`}>
                                                    {palette.map((color) => (
                                                        <IconButton
                                                            key={color}
                                                            disableRipple={colorsInUse.includes(
                                                                color
                                                            )}
                                                            // className={
                                                            //     colorsInUse.includes(color)
                                                            //         ? classes.disabledColor
                                                            //         : ''
                                                            // }
                                                            onClick={() => {
                                                                if (colorsInUse.includes(color))
                                                                    return;
                                                                props.selectOption({
                                                                    label: '',
                                                                    value: color,
                                                                    icon: CircleDiv,
                                                                    color: color,
                                                                });
                                                            }}
                                                            size="large"
                                                        >
                                                            <CircleDiv fill={color} />
                                                        </IconButton>
                                                    ))}
                                                </ColorsRowBox>
                                            ))}
                                        </ColorsContainerBox>
                                    );
                                }}
                            />
                        </Grid>
                    </Grid>
                </ContentBox>
            </Modal>
            <CancelModal
                open={showCancelConfirmation}
                title={t(
                    `customAppointmentReasonsForms.form.${
                        value?.id ? 'cancelEditTitle' : 'cancelNewTitle'
                    }`
                )}
                onCancel={() => {
                    setShowCancelConfirmation(false);
                    onClose();
                }}
                onClose={() => {
                    setShowCancelConfirmation(false);
                }}
            />
        </>
    );
};

export default CustomAppointmentReasonFormModal;
