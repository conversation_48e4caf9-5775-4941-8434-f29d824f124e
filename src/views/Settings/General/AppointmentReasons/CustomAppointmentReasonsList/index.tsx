import { Box } from '@mui/material';
import { CustomAppointmentReasonDto } from 'api/appointmentReasons';
import AreaSpinner from 'common/components/AreaSpinner';
import { CustomAppointmentReasonItem } from '../CustomAppointmentReasonItem';

interface CustomAppointmentReasonsListProps {
    isLoadingInProgress: boolean;
    customAppointmentReasons: CustomAppointmentReasonDto[];
    refetch: () => void;
    handleMove: (index: number, direction: number) => void;
    readonly: boolean;
    onEdit: (reason: CustomAppointmentReasonDto) => void;
}

export const CustomAppointmentReasonsList = ({
    customAppointmentReasons,
    isLoadingInProgress,
    handleMove,
    refetch,
    readonly,
    onEdit,
}: CustomAppointmentReasonsListProps) => {
    return (
        <>
            {isLoadingInProgress ? (
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        alignContent: 'center',
                        width: '100%',
                        height: 'calc(100vh - 320px)!important',
                    }}
                >
                    <AreaSpinner />
                </Box>
            ) : (
                customAppointmentReasons &&
                customAppointmentReasons.map((customAppointmentReason, index) => (
                    <CustomAppointmentReasonItem
                        readonly={readonly}
                        key={`CustomAppointmentReason-${customAppointmentReason.id}`}
                        customAppointmentReason={customAppointmentReason}
                        index={index}
                        isLastItem={index === customAppointmentReasons.length - 1}
                        refetch={refetch}
                        handleMove={handleMove}
                        onEdit={onEdit}
                    />
                ))
            )}
        </>
    );
};
