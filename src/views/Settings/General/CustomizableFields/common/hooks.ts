import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import FieldsA<PERSON>, { FieldListItemDto, FieldSection } from 'api/fields';
import { useMemo } from 'react';

export function useFields(section: FieldSection) {
    const queryKey = ['settings', 'fields', section];
    const { data, isLoading, refetch } = useQuery(
        queryKey,
        () => FieldsApi.getFieldsInSection(section),
        {
            cacheTime: Infinity,
            refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
            refetchInterval: 120000,
        }
    );
    const fields = useMemo(() => data ?? [], [data]);

    const queryClient = useQueryClient();

    const reorderFieldsMutation = useMutation(
        (order: string[]) => {
            return FieldsApi.setFieldsOrder(section, order);
        },
        {
            onMutate(order: string[]) {
                const storedData: typeof data = queryClient.getQueryData(queryKey);
                if (!storedData) return;
                queryClient.setQueryData(queryKey, reorderFields(storedData, order));
            },
            onSettled() {
                refetch();
            },
        }
    );

    return {
        fields,
        isLoading,
        reorderFieldsMutation,
        refetch,
    };
}

function reorderFields(fields: FieldListItemDto[], order: string[]): FieldListItemDto[] {
    const reorderedFields: FieldListItemDto[] = [];

    for (const id of order) {
        const field = fields.find((x) => x.id === id);
        if (!field) continue;
        reorderedFields.push(field);
    }

    if (reorderFields.length !== fields.length) {
        const missing = fields.filter((x) => !reorderedFields.includes(x));
        reorderedFields.push(...missing);
    }

    return reorderedFields;
}
