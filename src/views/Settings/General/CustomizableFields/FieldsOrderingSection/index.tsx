import { debounce } from '@mui/material';
import { FIELD_SECTIONS, FieldListItemDto, FieldSection } from 'api/fields';
import SMenuItem from 'common/components/mui/SMenuItem';
import { SSelectTinted } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import CreateFieldPopup from './CreateFieldPopup';
import { DeleteFieldPopupProvider } from './DeleteFieldPopup';
import { EditFieldPopupContextProvider } from './EditFieldPopup';

import { useAppDispatch } from 'store';
import { loadColorFieldsThunk } from 'store/slices/colorFields/thunks';
import FieldSectionTooltip from '../../../common/FieldSectionTooltip';
import { FieldSectionTooltipContextProvider } from '../../../common/FieldSectionTooltip/context';
import { useFields } from '../common/hooks';
import FieldsList from './FieldsList';

export default function FieldsOrderingSection() {
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();

    const [selectedSection, setSelectedSection] = useState<FieldSection>('WebLocationGeneralInfo');
    const {
        fields,
        isLoading,
        reorderFieldsMutation,
        refetch: refetchSelectedSection,
    } = useFields(selectedSection);
    const [showModal, setShowModal] = useState(false);

    const onFieldsReordered = useMemo(
        () =>
            debounce(
                (order: FieldListItemDto[]) => reorderFieldsMutation.mutate(order.map((x) => x.id)),
                1000
            ),
        [reorderFieldsMutation]
    );

    const onAddField = () => {
        setShowModal(true);
    };

    const refetch = () => {
        refetchSelectedSection();
        dispatch(loadColorFieldsThunk());
    };

    return (
        <FieldSectionTooltipContextProvider>
            <SSelectTinted
                menuBorders
                onChange={(e) => setSelectedSection(e.target.value as FieldSection)}
                value={selectedSection}
            >
                {Object.keys(FIELD_SECTIONS).map((name) => (
                    <SMenuItem key={name} value={name}>
                        {t(`settings.customizableFields.sections.${name}`)}
                        <FieldSectionTooltip section={name as FieldSection} />
                    </SMenuItem>
                ))}
            </SSelectTinted>
            <EditFieldPopupContextProvider onFieldSaved={refetch}>
                <DeleteFieldPopupProvider onDeleted={refetch}>
                    <FieldsList
                        isLoading={isLoading}
                        fields={fields}
                        section={selectedSection}
                        onAddField={onAddField}
                        onFieldsReordered={onFieldsReordered}
                    />
                </DeleteFieldPopupProvider>
            </EditFieldPopupContextProvider>
            <CreateFieldPopup
                open={showModal}
                onClose={() => setShowModal(false)}
                onFieldCreated={() => {
                    setShowModal(false);
                    refetch();
                }}
                section={selectedSection}
            />
        </FieldSectionTooltipContextProvider>
    );
}
