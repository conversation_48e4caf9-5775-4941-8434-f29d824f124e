import { MultipleChoiceOptionItem } from 'api/fields';
import { t } from 'i18next';

export function createDefaultOption(index: number): MultipleChoiceOptionItem {
    return {
        name: `${t('settings.customizableFields.multipleChoiceSection.defaultOption')} ${
            index + 1
        }`,
        id: null,
        color: null,
    };
}

export function findDuplicatedOptions(options: MultipleChoiceOptionItem[]): number[] {
    const duplicates = options.reduce(function (acc: number[], el, i, arr) {
        const duplicate = arr.find((o, j) => o.name === el.name && i != j);
        if (duplicate) acc.push(i);
        return acc;
    }, []);

    return duplicates;
}
