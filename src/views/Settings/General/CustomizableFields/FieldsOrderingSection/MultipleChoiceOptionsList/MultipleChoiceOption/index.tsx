import { Box, Grid, IconButton, styled, useTheme } from '@mui/material';
import { MultipleChoiceOptionItem } from 'api/fields';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { IconProps } from 'common/components/Icons/Icon';
import { UncheckedCircleIcon } from 'common/components/Icons/UncheckedCircleIcon';
import { TextField } from 'common/components/Inputs';
import Dropdown from 'common/components/Inputs/Dropdown';
import ArrowTooltip from 'common/components/Tooltip';
import { Colors } from 'common/styles/Colors';
import { OptionStyle } from 'common/styles/OptionStyle';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';

export type MultipleChoiceOptionProps = {
    option: MultipleChoiceOptionItem;
    enableExternalId: boolean;
    onDelete: (option: MultipleChoiceOptionItem) => void;
    onChanged: (option: MultipleChoiceOptionItem) => void;
    dragHandleProps?: DraggableProvidedDragHandleProps;
    isInvalidByIndex: boolean;
    dataTestId?: string;
    showColor: boolean;
    colorInUse: string[];
};

type EditMode = 'Name' | 'Id' | 'None';

export default function MultipleChoiceOption({
    option: optionProp,
    enableExternalId,
    onDelete,
    onChanged,
    dragHandleProps,
    isInvalidByIndex,
    showColor,
    colorInUse,
}: MultipleChoiceOptionProps) {
    const theme = useTheme();
    const [option, setOption] = useState<MultipleChoiceOptionItem>(optionProp);
    const [editMode, setEditMode] = useState<EditMode>('None');
    const [hasColorChanged, setHasColorChanged] = useState(false);

    const handleOptionChange = (newOption: Partial<MultipleChoiceOptionItem>) => {
        const updatedOption = { ...option, ...newOption };
        setOption(updatedOption);
    };

    useEffect(() => {
        setOption(optionProp);
    }, [optionProp]);

    const handleColorChange = (color: string) => {
        const updatedOption = { ...option, color };
        setOption(updatedOption);
        onChanged(updatedOption);
    };

    const updateOption = () => {
        setEditMode('None');
        onChanged(option);
    };

    return (
        <Box
            sx={{
                display: 'flex',
                gap: '24px',
                width: '100%',
            }}
        >
            <StyledContainer container isInvalid={isInvalidByIndex}>
                <Grid item xs={showColor ? 2 : 1}>
                    <div {...dragHandleProps}>
                        <DragAndDropIcon
                            size={25}
                            fill={Colors.Neutral5}
                            style={{ margin: '0 10px' }}
                        />
                    </div>
                </Grid>
                <Grid
                    item
                    xs={showColor ? 8 : 10}
                    style={{ display: 'flex', alignItems: 'center' }}
                >
                    {editMode === 'None' && (
                        <>
                            <UncheckedCircleIcon size={25} fill={Colors.Neutral5} />

                            <StyledNameLabel
                                isInvalid={isInvalidByIndex}
                                onClick={() => {
                                    setEditMode('Name');
                                }}
                            >
                                {option.name}
                            </StyledNameLabel>

                            {enableExternalId && (
                                <ArrowTooltip
                                    content={t(
                                        'settings.customizableFields.multipleChoiceSection.addExternalIdTooltip'
                                    )}
                                    position="right"
                                >
                                    <StyledIdLabel
                                        onClick={() => {
                                            setEditMode('Id');
                                        }}
                                    >
                                        {option.id ??
                                            t(
                                                'settings.customizableFields.multipleChoiceSection.addExternalId'
                                            )}
                                    </StyledIdLabel>
                                </ArrowTooltip>
                            )}
                        </>
                    )}
                    {editMode === 'Name' && (
                        <TextField
                            value={option.name}
                            onChange={(e) => {
                                handleOptionChange({ name: e.target.value });
                            }}
                            onBlur={updateOption}
                            onEnterPress={updateOption}
                            autoFocus
                        />
                    )}
                    {editMode === 'Id' && (
                        <TextField
                            inputProps={{ maxLength: 36 }}
                            value={option.id}
                            onChange={(e) => {
                                handleOptionChange({ id: e.target.value });
                            }}
                            onBlur={updateOption}
                            onEnterPress={updateOption}
                            autoFocus
                        />
                    )}
                </Grid>
                <Grid item xs={showColor ? 2 : 1}>
                    <IconButton size="small" onClick={() => onDelete(optionProp)}>
                        <DeleteIcon fill={Colors.Neutral5} />
                    </IconButton>
                </Grid>
            </StyledContainer>
            {showColor && (
                <Dropdown<string>
                    name="color"
                    showValidationIndicators
                    isRequired
                    placeholder={t(
                        'settings.customizableFields.multipleChoiceSection.jobColorPlaceholder'
                    )}
                    optionStyle={OptionStyle.icons}
                    size="medium"
                    value={
                        option.color
                            ? {
                                  label: '',
                                  value: option.color,
                                  icon: CircleDiv,
                                  color: option.color,
                              }
                            : undefined
                    }
                    onChange={(event) => {
                        if (event === null) return;
                        handleColorChange(event.value);
                    }}
                    onClose={() => setHasColorChanged(true)}
                    onOpen={() => setHasColorChanged(false)}
                    styles={{
                        control: {
                            borderColor:
                                hasColorChanged && option.color === null
                                    ? `${theme.palette.error.light} !important`
                                    : `${theme.palette.neutral[4]} !important`,
                        },
                    }}
                    CustomMenu={(props) => {
                        return (
                            <ColorsContainerBox>
                                {COLOR_OPTIONS.map((palette, optIdx) => (
                                    <ColorsRowBox key={`lst-${optIdx}`}>
                                        {palette.map((color) => (
                                            <IconButton
                                                key={color}
                                                onClick={() => {
                                                    if (colorInUse.includes(color)) return;
                                                    props.selectOption({
                                                        label: '',
                                                        value: color,
                                                        icon: CircleDiv,
                                                        color: color,
                                                    });
                                                }}
                                                size="large"
                                            >
                                                {colorInUse.includes(color) ? (
                                                    <CrossedCircleDiv fill={color} />
                                                ) : (
                                                    <CircleDiv fill={color} />
                                                )}
                                            </IconButton>
                                        ))}
                                    </ColorsRowBox>
                                ))}
                            </ColorsContainerBox>
                        );
                    }}
                />
            )}
        </Box>
    );
}

const StyledContainer = styled(Grid, {
    shouldForwardProp: (prop) => !['isInvalid'].includes(prop as string),
})<{ isInvalid: boolean }>(({ theme, isInvalid }) => ({
    display: 'flex',
    alignItems: 'center',
    border: '1px solid ' + (isInvalid ? 'var(--danger)' : theme.palette.neutral[4]),
    borderRadius: '5px',
    backgroundColor: '#FFF',
    height: '40px',
    position: 'relative',

    '&::after': {
        content: '""',
        height: 8,
        width: 8,
        borderRadius: 100,
        background: theme.palette.primary.main,
        position: 'absolute',
        right: -4,
        top: -4,
    },
}));

const StyledNameLabel = styled('div', {
    shouldForwardProp: (prop) => !['isInvalid'].includes(prop as string),
})<{ isInvalid: boolean }>(({ theme, isInvalid }) => ({
    fontSize: '12px',
    color: isInvalid ? 'var(--danger)' : theme.palette.neutral[7],
    maxWidth: '50%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
}));

const StyledIdLabel = styled('div')(({ theme }) => ({
    borderLeft: '2px solid ' + theme.palette.neutral[7],
    marginLeft: '5px',
    paddingLeft: '5px',
    fontSize: '12px',
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
    maxWidth: '50%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
}));

export const ColorsContainerBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    height: 212,
    overflowY: 'scroll',
    position: 'relative',
    margin: '5px',

    '&::-webkit-scrollbar': {
        width: '5px',
    },
    '&::-webkit-scrollbar-track': {
        background: theme.palette.neutral[4],
        borderRadius: '10px',
        margin: '2px',
    },
    '&::-webkit-scrollbar-thumb': {
        background: theme.palette.neutral[6],
        borderRadius: '10px',
    },
}));

export const ColorsRowBox = styled(Box)(() => ({
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',

    '& button': {
        padding: 13,
    },
}));

export const CircleDiv = (props: IconProps) => (
    <div
        style={{
            height: 25,
            width: 25,
            backgroundColor: props.fill,
            border: props.fill === '#FFFFFF' ? '1px solid #DDDDDD' : undefined,
            borderRadius: '50%',
            display: 'inline-block',
        }}
    />
);

export const CrossedCircleDiv = (props: IconProps) => (
    <div
        style={{
            width: 25,
            height: 25,
            borderRadius: '50%',
            backgroundColor: props.fill,
            border: props.fill === '#FFFFFF' ? '1px solid #DDD' : 'none',
            display: 'inline-block',
            position: 'relative',
        }}
    >
        <div
            style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: 15,
                height: 2,
                backgroundColor: '#FFF',
                transform: 'translate(-50%, -50%) rotate(45deg)',
            }}
        />
        <div
            style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: 15,
                height: 2,
                backgroundColor: '#FFF',
                transform: 'translate(-50%, -50%) rotate(-45deg)',
            }}
        />
    </div>
);

export const COLOR_OPTIONS: string[][] = [
    ['#1C70E0', '#4480EB', '#75A7F2', '#9AC3F0', '#BAD5F6', '#D3E4FA'],
    ['#3FC4BD', '#5AAEAE', '#6CCFC5', '#96F3E8', '#B9F8F2', '#D6FFFF'],
    ['#2FB842', '#49C366', '#79DA84', '#9EF2A6', '#C4FFCB', '#E2FFE2'],
    ['#F0B100', '#FBC700', '#FFE05F', '#FFF090', '#FFF6BC', '#FFFADE'],
    ['#F86060', '#FF7777', '#FFA3A3', '#FFD3D7', '#FFE5EA', '#FFEEF1'],
    ['#8C60D8', '#A178E8', '#C2A0FF', '#DDD0F8', '#EEE6FC', '#F6F3FF'],
    ['#C37B38', '#D49A6A', '#E2B992', '#EDD6B9', '#F2E2CD', '#FBF1E3'],
    ['#8929BA', '#9D44D0', '#BD8DE7', '#DDBFF3', '#EEDCF8', '#FAF0FF'],
    ['#667080', '#7D8694', '#9AA3AD', '#BDC6CE', '#D9DCE2', '#EFF1F4'],
    ['#F754A7', '#FF73BE', '#FFA2D2', '#FFC4E2', '#FFD9ED', '#FFF0F7'],
];
