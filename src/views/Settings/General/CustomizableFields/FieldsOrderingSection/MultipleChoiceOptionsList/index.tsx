import { Box, styled } from '@mui/material';
import { MultipleChoiceOptionItem } from 'api/fields';
import { Button } from 'common/components/Button';
import { t } from 'i18next';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    DragDropContext,
    Draggable,
    DropResult,
    Droppable,
    ResponderProvided,
} from 'react-beautiful-dnd';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import MultipleChoiceOption from './MultipleChoiceOption';
import { createDefaultOption, findDuplicatedOptions } from './helper';

export type MultipleChoiceOptionListProps = {
    fieldId: string;
    options: MultipleChoiceOptionItem[];
    onOptionsChanged: (options: MultipleChoiceOptionItem[]) => void;
    onValidationStateChanged: (isValid: boolean) => void;
    showColor: boolean;
};

export default function MultipleChoiceOptionList({
    options: optionsProp,
    onOptionsChanged,
    onValidationStateChanged,
    showColor,
}: MultipleChoiceOptionListProps) {
    const [options, setOptions] = useState<MultipleChoiceOptionItem[]>(optionsProp);

    useEffect(() => {
        setOptions(optionsProp);
    }, [optionsProp]);

    const conflictIndexes = useMemo(() => findDuplicatedOptions(options), [options]);

    const isValidByColors = options.every((x) => x.color !== null) || !showColor;
    const isValidByIndexes = conflictIndexes.length === 0;

    const propsRef = useRef({ onValidationStateChanged });
    propsRef.current.onValidationStateChanged = onValidationStateChanged;

    useEffect(() => {
        propsRef.current.onValidationStateChanged(isValidByColors && isValidByIndexes);
    }, [isValidByColors, isValidByIndexes]);

    const colorsInUse = useMemo(
        () => options.filter((x) => x.color !== null).map((x) => x.color!),
        [options]
    );

    const addOption = () => {
        const nextIndex = options.length;
        const newOptions = [...options, createDefaultOption(nextIndex)];
        setOptions(newOptions);
        onOptionsChanged(newOptions);
    };

    const onDragEnd = useCallback(
        (result: DropResult, _provided: ResponderProvided) => {
            const fromIndex = result.source.index;
            const toIndex = result.destination?.index;
            if (toIndex === undefined) {
                console.warn('[FieldList] toIndex is undefined');
                return;
            }

            const newOptions = [...options];
            const movedItem = newOptions[fromIndex];
            newOptions.splice(fromIndex, 1);
            newOptions.splice(toIndex, 0, movedItem);
            setOptions(newOptions);
            onOptionsChanged(newOptions);
        },
        [options, onOptionsChanged]
    );

    const deleteOption = (option: MultipleChoiceOptionItem) => {
        const newOptions = options.filter((o) => o.name !== option.name);
        setOptions(newOptions);
        onOptionsChanged(newOptions);
    };

    const shopSettings = useAppSelector(selectSettings).repairShopSettings?.features;
    const isAnyIntegrationEnabled = shopSettings
        ? shopSettings.estimateIntegration ||
          shopSettings.enableEstimateIntegration ||
          shopSettings.itemJobs ||
          shopSettings.invoiceIntegration ||
          shopSettings.vehicleReceptionIntegrationEnabled
        : false;

    return (
        <DivRoot>
            <Box
                sx={{
                    display: 'flex',
                    gap: '24px',
                }}
            >
                <LabelContainer showColor={showColor}>
                    <StyledLabelBold>
                        {t('settings.customizableFields.multipleChoiceSection.enterTheOptions')}
                    </StyledLabelBold>
                </LabelContainer>
                {showColor && (
                    <LabelContainer showColor={true}>
                        <StyledLabelBold>
                            {t('settings.customizableFields.multipleChoiceSection.jobColor')}
                        </StyledLabelBold>
                    </LabelContainer>
                )}
            </Box>
            <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId={`multiple-choice-options`}>
                    {(provided) => (
                        <StyledScrollContainer
                            ref={provided.innerRef}
                            data-test-id="multiple-choice-options-list"
                            {...provided.droppableProps}
                        >
                            {options.map((option: MultipleChoiceOptionItem, idx: number) => (
                                <Draggable
                                    draggableId={`${idx}-${option.name}`}
                                    index={idx}
                                    key={`option-${idx}-${option.name}`}
                                >
                                    {(provided) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            style={{
                                                ...provided.draggableProps.style,
                                                marginBottom: '10px',
                                            }}
                                        >
                                            <MultipleChoiceOption
                                                option={option}
                                                enableExternalId={isAnyIntegrationEnabled}
                                                onDelete={deleteOption}
                                                onChanged={(updated) => {
                                                    const newValue = [...options];
                                                    newValue[idx] = updated;
                                                    setOptions(newValue);
                                                    onOptionsChanged(newValue);
                                                }}
                                                dragHandleProps={
                                                    provided.dragHandleProps ?? undefined
                                                }
                                                isInvalidByIndex={conflictIndexes.includes(idx)}
                                                showColor={showColor}
                                                colorInUse={colorsInUse}
                                            />
                                        </div>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </StyledScrollContainer>
                    )}
                </Droppable>
            </DragDropContext>
            <Button
                cmosVariant={'stroke'}
                onClick={() => addOption()}
                label={t('settings.customizableFields.multipleChoiceSection.addAnotherOption')}
            />
        </DivRoot>
    );
}

const DivRoot = styled('div')({
    '& > *:not(:last-child)': {
        marginBottom: '10px',
    },
});

const LabelContainer = styled('div')<{ showColor: boolean }>(({ showColor }) => ({
    display: 'flex',
    alignItems: 'center',
    flex: showColor ? 1 : 'none',
}));

const StyledLabelBold = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[8],
    fontWeight: 'bold',

    '&::after': {
        content: '"*"',
        marginLeft: '3px',
        color: theme.palette.primary.light,
        fontWeight: 700,
    },
}));

const StyledScrollContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
});
