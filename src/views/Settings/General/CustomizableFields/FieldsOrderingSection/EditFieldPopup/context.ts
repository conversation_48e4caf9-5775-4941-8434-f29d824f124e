import { FieldSection } from 'api/fields';
import { createContext, useContext } from 'react';

interface IEditFieldPopupContext {
    open(fieldId: string, section: FieldSection, colorFieledId: string | null): void;
}

export const EditFieldPopupContext = createContext<IEditFieldPopupContext | null>(null);

export function useEditFieldPopupContext() {
    const ctx = useContext(EditFieldPopupContext);
    if (!ctx) throw new Error('EditFieldPopupContext is not available');

    return ctx;
}
