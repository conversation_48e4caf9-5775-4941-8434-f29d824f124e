import { Box, CircularProgress, Divider, styled, Typography } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { isCmosError } from 'api/error';
import FieldsApi, {
    FieldDto,
    FieldSection,
    FieldType,
    isPredefinedField,
    mapFieldTypeOption,
    mapSettingsFieldTypeOption,
    MultipleChoiceOptionItem,
    MultipleChoiceType,
    SELECTABLE_FIELD_TYPES,
    SettingsFieldTypeOption,
} from 'api/fields';
import { Button } from 'common/components/Button';
import Dropdown from 'common/components/Inputs/Dropdown';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import TextForm<PERSON>ield from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { JobTitle, jobTitleLabel } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import sortBy from 'lodash/sortBy';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import React, { useEffect, useMemo, useState } from 'react';
import { SwitchWithDetails } from '../CreateFieldPopup';
import { SettingsFieldTypeOptionIcon } from '../FieldsList/FieldElement';
import MultipleChoiceOptionList from '../MultipleChoiceOptionsList';
import { MultipleChoiceRadio } from '../MultipleChoiceRadio';
import OrderTypesSelector, { OrderType } from '../OrderTypesSelector';
import { EditFieldPopupContext } from './context';

function isColumnField(field: FieldDto) {
    // Sanity check: that is not supposed to happen
    if (field.sections.length === 0) {
        return false;
    }
    // Sanity check: and neither is this
    if (isPredefinedField(field.type)) {
        return false;
    }

    const section = field.sections[0];
    return section.type === 'WebParts' || section.type === 'WebLabor';
}

type EditFieldPopupProps = {
    open: boolean;
    fieldId: string | null;
    section: FieldSection | null;
    colorFieledId: string | null;
    onClose: () => void;
    onClosed?: () => void;
    onFieldSaved: () => void;
};

const FIELD_TYPES_IN_ORDER = SELECTABLE_FIELD_TYPES;
const COLUMN_TYPES_IN_ORDER = ['Numeric', 'Currency', 'ShortText'] as SettingsFieldTypeOption[];

const MOBILE_SECTIONS_NAMES: Partial<Record<FieldSection, FieldSection>> = {
    WebCustomerInfo: 'MobileCustomer',
    WebVehicleInfo: 'MobileVehicle',
    WebOrderInfo: 'MobileOrder',
    WebJobInfo: 'MobileJobInfo',
    MobileCustomer: 'WebCustomerInfo',
    MobileOrder: 'WebOrderInfo',
    MobileVehicle: 'WebVehicleInfo',
    MobileJobInfo: 'WebJobInfo',
};

const MOBILE_SECTIONS: Partial<Record<FieldSection, FieldSection>> = {
    WebCustomerInfo: 'MobileCustomer',
    WebVehicleInfo: 'MobileVehicle',
    WebOrderInfo: 'MobileOrder',
    WebJobInfo: 'MobileJobInfo',
    MobileCustomer: 'MobileCustomer',
    MobileOrder: 'MobileOrder',
    MobileVehicle: 'MobileVehicle',
    MobileJobInfo: 'MobileJobInfo',
};

export default function EditFieldPopup({
    open,
    fieldId,
    section,
    colorFieledId,
    onClose,
    onClosed,
    onFieldSaved,
}: EditFieldPopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const [name, setName] = useState('');
    const [fieldType, setFieldType] = useState<FieldType>('ShortText');
    const [type, setType] = useState<SettingsFieldTypeOption | null>(null);
    const [jobTitle, setJobTitle] = useState<JobTitle | null>(null);
    const [multipleChoiceType, setMultipleChoiceType] = useState<MultipleChoiceType>('Single');
    const [options, setOptions] = useState<MultipleChoiceOptionItem[] | null>(null);
    const [orderTypes, setOrderTypes] = useState<OrderType[] | null>(null);
    const [optionsValid, setOptionsValid] = useState<boolean>(true);
    const [requiredWeb, setRequiredWeb] = useState(false);
    const [requiredMobile, setRequiredMobile] = useState(false);
    const [hasExtraText, setHasExtraText] = useState(false);
    const [mobileSectionEnabled, setMobileSectionEnabled] = useState(false);
    const [hasColor, setHasColor] = useState(false);
    const mobileSectionName = section ? MOBILE_SECTIONS_NAMES[section] : undefined;
    const mobileSection = section ? MOBILE_SECTIONS[section] : undefined;

    const nameIsInvalid = name.trim().length === 0;
    const jobPositionIsInvalid = type === 'PredefinedAdditionalPosition' && jobTitle === null;
    const valid = !jobPositionIsInvalid && !nameIsInvalid && !!fieldType && optionsValid;
    const showHasColorSwitch = section === 'WebJobInfo' && fieldType === 'Select';

    const { data, isLoading } = useQuery(['field', fieldId], () => FieldsApi.get(fieldId!), {
        enabled: !!fieldId,
        cacheTime: 0,
    });

    useEffect(() => {
        if (data) {
            const translatedKey = `settings.customizableFields.predefined.${data.name.replaceAll(
                '.',
                '_'
            )}`;
            //lookup the translation for name value this field,
            // if it doesn't exist then display as is
            const name = t(translatedKey) !== translatedKey ? t(translatedKey) : data.name;

            setName(name);

            setFieldType(data.type);
            setJobTitle(data.jobTitle);
            setMultipleChoiceType(data.type === 'MultiSelect' ? 'Multiple' : 'Single');
            setType(mapFieldTypeOption(data.type) || null);
            setOptions(data.options);
            setOrderTypes(data.showConditions);
            setHasExtraText(data.hasExtraText);
            setRequiredWeb(data.requiredOnWeb);
            setRequiredMobile(data.requiredOnMobile);
            setHasColor(data.hasColor);
            setMobileSectionEnabled(data.sections.length > 1);
        }
    }, [data, t]);

    const jobPositionOptions = useMemo(
        () =>
            sortBy(
                [
                    { value: 'Administrator', label: t(jobTitleLabel('Administrator')) },
                    { value: 'Owner', label: t(jobTitleLabel('Owner')) },
                    { value: 'Manager', label: t(jobTitleLabel('Manager')) },
                    { value: 'WorkshopManager', label: t(jobTitleLabel('WorkshopManager')) },
                    { value: 'Parts', label: t(jobTitleLabel('Parts')) },
                    { value: 'ServiceAdvisor', label: t(jobTitleLabel('ServiceAdvisor')) },
                    { value: 'Technician', label: t(jobTitleLabel('Technician')) },
                    { value: 'CarWasher', label: t(jobTitleLabel('CarWasher')) },
                    {
                        value: 'AppointmentsExecutive',
                        label: t(jobTitleLabel('AppointmentsExecutive')),
                    },
                    { value: 'Other', label: t(jobTitleLabel('Other')) },
                    {
                        value: 'BdcSupervisor',
                        label: t(jobTitleLabel('BdcSupervisor')),
                    },
                    {
                        value: 'BdcAdvisor',
                        label: t(jobTitleLabel('BdcAdvisor')),
                    },
                ] as { label: string; value: JobTitle }[],
                (x) => x.label
            ),
        [t]
    );

    const isColumn = data && isColumnField(data);

    const allowedTypes = isColumn ? COLUMN_TYPES_IN_ORDER : FIELD_TYPES_IN_ORDER;
    const isPredefined = data && data.type === 'Predefined';

    const updateField = useMutation(
        async () => {
            if (isPredefined) throw new Error("Sanity check failed: can't edit predefined fields");
            if (!fieldId) throw new Error('cannot update the field if fieldId is not set');
            if (!data)
                throw new Error('cannot update the field until it was fetched from the server');

            const sections = data.sections.map((x) => x.type);
            if (mobileSection) {
                if (mobileSectionEnabled) {
                    sections.push(mobileSection);
                } else {
                    const index = sections.indexOf(mobileSection);
                    if (index !== -1) {
                        sections.splice(index, 1);
                    }
                }
            }

            await FieldsApi.update(fieldId, {
                name,
                type: fieldType,
                sections: sections,
                multipleChoiceOptions: options,
                showConditions: orderTypes,
                hasExtraText,
                jobTitle: jobTitle,
                hasColor,
                requiredOnWeb:
                    requiredWeb ||
                    (data?.sections.some(
                        (x) => x.type === 'WebJobInfo' || x.type === 'MobileJobInfo'
                    ) &&
                        requiredMobile),
                requiredOnMobile:
                    requiredMobile ||
                    (data?.sections.some(
                        (x) => x.type === 'WebJobInfo' || x.type === 'MobileJobInfo'
                    ) &&
                        requiredWeb),
            });
        },
        {
            onError: (err) => {
                if (isCmosError(err) && err.cmosCode === 'General.Fields.NameConflict') {
                    toasters.danger(
                        t('settings.customizableFields.createNewModal.nameConflict.text'),
                        t('settings.customizableFields.createNewModal.nameConflict.title')
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
            onSuccess: () => {
                if (isColumn) {
                    toasters.success(
                        t('settings.customizableFields.editModal.successNotifColumn.text'),
                        t('settings.customizableFields.editModal.successNotifColumn.title')
                    );
                } else {
                    toasters.success(
                        t('settings.customizableFields.editModal.successNotif.text'),
                        t('settings.customizableFields.editModal.successNotif.title')
                    );
                }
                onFieldSaved();
            },
        }
    );

    const onTypeSelectChanged = (newType: SettingsFieldTypeOption) => {
        setType(newType);

        if (newType === 'MultipleChoice') {
            setFieldType('Select');
        } else {
            const newFieldType = mapSettingsFieldTypeOption(newType);
            if (!newFieldType) {
                throw new Error('Unsupported field type.');
            }
            setFieldType(newFieldType);
        }
    };

    const getTranslation = useMemo(
        () => (key: string) =>
            t(`settings.customizableFields.createNewModal.${key}${isColumn ? 'Column' : ''}`),
        [isColumn, t]
    );

    return (
        <Modal
            boxComponent={ModalBox}
            open={open}
            onClose={onClose}
            onTransitionExited={() => {
                if (onClosed) onClosed();
            }}
        >
            {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <CircularProgress size={32} thickness={3} />
                </Box>
            ) : (
                <>
                    <Box sx={{ display: 'flex', marginBottom: 3, padding: '0 20px' }}>
                        <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="h4Inter">
                                {isColumn
                                    ? t('settings.customizableFields.editModal.headerColumn')
                                    : t('settings.customizableFields.editModal.header')}
                            </Typography>
                        </Box>

                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'end',
                                gap: 2,
                            }}
                        >
                            <Button
                                onClick={() => onClose()}
                                w="md"
                                color={Colors.Neutral3}
                                label={t('commonLabels.cancel')}
                            />
                            <Button
                                showLoader={updateField.isLoading}
                                disabled={updateField.isLoading || !valid || isLoading || !data}
                                onClick={() => updateField.mutate()}
                                w="md"
                                color={Colors.Success}
                                label={t('settings.customizableFields.editModal.save')}
                            />
                        </Box>
                    </Box>
                    <ScrollContainer>
                        <Box
                            sx={{
                                display: 'flex',
                                gap: '24px',
                                mb: 2,
                            }}
                        >
                            <Box sx={{ flex: 1 }}>
                                <TextFormField
                                    label={getTranslation('name')}
                                    placeholder={getTranslation('namePlaceholder')}
                                    value={name}
                                    inputProps={{
                                        maxLength:
                                            fieldType === 'PredefinedAdditionalPosition' ? 60 : 200,
                                    }}
                                    onChange={(e) => setName(e.target.value)}
                                    isRequired
                                    disabled={!data}
                                    isInvalid={nameIsInvalid}
                                    showValidationIndicators
                                />
                            </Box>
                            <Box
                                sx={{
                                    flex: 1,
                                    opacity: fieldType === 'PredefinedAdditionalPosition' ? 0.5 : 1,
                                }}
                            >
                                <InputWrapper
                                    isRequired
                                    showValidationIndicators
                                    label={getTranslation('type')}
                                >
                                    <SSelectInput
                                        menuBorders
                                        placeholder={getTranslation('typePlaceholder')}
                                        value={
                                            fieldType === 'PredefinedAdditionalPosition'
                                                ? 'Dropdown'
                                                : type
                                        }
                                        renderValue={(selected) =>
                                            fieldType === 'PredefinedAdditionalPosition'
                                                ? t(`customizableFields.types.predefined-Dropdown`)
                                                : t(`customizableFields.types.${selected}`)
                                        }
                                        disabled={
                                            !data || fieldType === 'PredefinedAdditionalPosition'
                                        }
                                        onChange={(e) =>
                                            onTypeSelectChanged(
                                                e.target.value as SettingsFieldTypeOption
                                            )
                                        }
                                    >
                                        {allowedTypes.map((x) => (
                                            <SMenuItem key={x} value={x}>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <SettingsFieldTypeOptionIcon type={x} />{' '}
                                                    {t(`customizableFields.types.${x}`)}
                                                </Box>
                                            </SMenuItem>
                                        ))}
                                    </SSelectInput>
                                </InputWrapper>
                            </Box>
                            {fieldType === 'PredefinedAdditionalPosition' && (
                                <Box sx={{ flex: 1 }}>
                                    <Dropdown
                                        label={t('users.jobPosition')}
                                        placeholder={t('users.placeholder.jobPosition')}
                                        name={'users.jobPosition'}
                                        isRequired
                                        cmosVariant="default"
                                        showValidationIndicators
                                        value={
                                            jobTitle
                                                ? {
                                                      value: jobTitle,
                                                      label: jobTitle
                                                          ? t(jobTitleLabel(jobTitle))
                                                          : jobTitle,
                                                  }
                                                : null
                                        }
                                        isSearchable={false}
                                        onChange={(event) => event && setJobTitle(event.value)}
                                        options={jobPositionOptions}
                                        size="small"
                                    />
                                </Box>
                            )}
                        </Box>

                        {data?.sections.some(
                            (x) => x.type === 'WebJobInfo' || x.type === 'MobileJobInfo'
                        ) && (
                            <Box
                                sx={{
                                    display: 'grid',
                                    gridTemplateColumns: '1fr 1fr',
                                    columnGap: '24px',
                                    mb: 2,
                                }}
                            >
                                <OrderTypesSelector
                                    orderTypes={orderTypes ?? []}
                                    setOrderTypes={setOrderTypes}
                                />
                            </Box>
                        )}

                        {type === 'MultipleChoice' && (
                            <>
                                <Divider style={{ marginBottom: '15px' }} />

                                <MultipleChoiceRadio
                                    value={multipleChoiceType}
                                    onChanged={(newMultipleChoiceType) => {
                                        setFieldType(
                                            newMultipleChoiceType === 'Single'
                                                ? 'Select'
                                                : 'MultiSelect'
                                        );
                                    }}
                                />

                                <MultipleChoiceOptionList
                                    fieldId={name}
                                    options={options || []}
                                    onOptionsChanged={setOptions}
                                    onValidationStateChanged={setOptionsValid}
                                    showColor={hasColor && showHasColorSwitch}
                                />

                                <Divider style={{ marginTop: '15px', marginBottom: '15px' }} />
                            </>
                        )}
                        {showHasColorSwitch && (
                            <>
                                <SwitchWithDetails
                                    value={hasColor}
                                    onChange={setHasColor}
                                    title={t('settings.customizableFields.createNewModal.jobColor')}
                                    subtitle={t(
                                        'settings.customizableFields.createNewModal.jobColorSubTitle'
                                    )}
                                    disabled={!!colorFieledId && colorFieledId !== fieldId}
                                    labelSeparately
                                />
                                <Divider style={{ marginTop: '15px', marginBottom: '15px' }} />
                            </>
                        )}
                        <DivSwitchesRoot>
                            {(fieldType === 'MultiSelect' || fieldType === 'Select') && (
                                <SwitchWithDetails
                                    value={hasExtraText}
                                    onChange={setHasExtraText}
                                    title={t(
                                        'settings.customizableFields.createNewModal.hasExtraText'
                                    )}
                                    subtitle={t(
                                        'settings.customizableFields.createNewModal.hasExtraTextSubtitle'
                                    )}
                                />
                            )}
                            <SwitchWithDetails
                                value={mobileSectionEnabled}
                                onChange={(v) => {
                                    setMobileSectionEnabled(v);
                                    if (!v) {
                                        setRequiredMobile(false);
                                    }
                                }}
                                title={t(
                                    'settings.customizableFields.createNewModal.additionalSection'
                                )}
                                subtitle={t(
                                    'settings.customizableFields.createNewModal.additionalSectionSubtitle',
                                    {
                                        main: t(`settings.customizableFields.sections.${section}`),
                                        additional: t(
                                            `settings.customizableFields.sections.${mobileSectionName}`
                                        ),
                                        interpolation: { escapeValue: false },
                                    }
                                )}
                            />
                            {(data?.sections.some(
                                (x) =>
                                    x.type === 'MobileCustomer' ||
                                    x.type === 'MobileOrder' ||
                                    x.type === 'MobileVehicle'
                            ) ||
                                type === 'PredefinedAdditionalPosition') && (
                                <SwitchWithDetails
                                    value={requiredMobile}
                                    onChange={setRequiredMobile}
                                    disabled={
                                        type === 'PredefinedAdditionalPosition'
                                            ? !mobileSectionEnabled
                                            : false
                                    }
                                    title={t(
                                        'settings.customizableFields.createNewModal.requiredMobile'
                                    )}
                                    subtitle={t(
                                        'settings.customizableFields.createNewModal.requiredMobileSubtitle'
                                    )}
                                />
                            )}
                            {data?.sections.some(
                                (x) => x.type === 'WebJobInfo' || x.type === 'MobileJobInfo'
                            ) && (
                                <SwitchWithDetails
                                    value={section === 'WebJobInfo' ? requiredWeb : requiredMobile}
                                    onChange={
                                        section === 'WebJobInfo'
                                            ? setRequiredWeb
                                            : setRequiredMobile
                                    }
                                    title={t(
                                        'settings.customizableFields.createNewModal.mandatoryField'
                                    )}
                                    subtitle={t(
                                        'settings.customizableFields.createNewModal.mandatoryFieldSubtitle'
                                    )}
                                />
                            )}
                        </DivSwitchesRoot>
                    </ScrollContainer>
                </>
            )}
        </Modal>
    );
}

const ModalBox = styled('div')({
    padding: '40px',
    width: 860,
});

const ScrollContainer = styled(OverlayScrollbarsComponent)({
    maxHeight: '516px',
    padding: '16px',
});

const DivSwitchesRoot = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: '8px',
});

export function EditFieldPopupContextProvider({
    children,
    onFieldSaved,
}: React.PropsWithChildren<{
    onFieldSaved: () => void;
}>) {
    const [fieldId, setFieldId] = useState<string | null>(null);
    const [section, setSection] = useState<FieldSection | null>(null);
    const [open, setOpen] = useState(false);
    const [colorFieledId, setColorFieledId] = useState<string | null>(null);

    const ctx = useMemo(
        () => ({
            open: (fieldId: string, section: FieldSection, colorFieledId: string | null) => {
                setFieldId(fieldId);
                setSection(section);
                setOpen(true);
                setColorFieledId(colorFieledId);
            },
        }),
        []
    );
    return (
        <EditFieldPopupContext.Provider value={ctx}>
            {children}
            <EditFieldPopup
                open={!!fieldId && open}
                fieldId={fieldId}
                section={section}
                colorFieledId={colorFieledId}
                onClose={() => setOpen(false)}
                onClosed={() => {
                    setFieldId(null);
                    setSection(null);
                }}
                onFieldSaved={() => {
                    setFieldId(null);
                    setSection(null);
                    setOpen(false);
                    onFieldSaved();
                }}
            />
        </EditFieldPopupContext.Provider>
    );
}
