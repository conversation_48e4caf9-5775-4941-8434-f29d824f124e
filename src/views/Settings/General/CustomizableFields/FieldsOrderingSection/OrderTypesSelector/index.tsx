import { useTheme } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { OrderTypesApi } from 'api/orders';
import { CheckBoxIcon } from 'common/components/Icons/CheckBoxIcon';
import { UncheckBoxIcon } from 'common/components/Icons/UncheckBoxIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { SMenuItem } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo } from 'react';
import { OptionProps } from 'react-select';

export type OrderType = { id: string; name: string };

export type OrderTypesSelectorProps = {
    orderTypes: OrderType[];
    setOrderTypes: (orderTypes: OrderType[]) => void;
};

export default function OrderTypesSelector({ orderTypes, setOrderTypes }: OrderTypesSelectorProps) {
    const { t } = useAppTranslation();
    const theme = useTheme();

    const { data: allTypes } = useQuery(
        ['customizableFields', 'orderTypes'],
        OrderTypesApi.getList
    );

    const onChange = useCallback(
        (value: Readonly<OptionData<OrderType>[]>) => {
            if (value.length > 0 && value[value.length - 1].value.id === '__all') {
                setOrderTypes([]);
            } else {
                setOrderTypes(value.filter((x) => x.value.id !== '__all').map((x) => x.value));
            }
        },
        [setOrderTypes]
    );

    const value = useMemo(
        () =>
            orderTypes.length === 0
                ? [
                      {
                          value: {
                              id: '__all',
                              name: t(
                                  'settings.customizableFields.orderTypesSelector.allOrderTypes'
                              ),
                          },
                          label: t('settings.customizableFields.orderTypesSelector.allOrderTypes'),
                      },
                  ]
                : orderTypes.map((x) => ({ value: x, label: x.name })),
        [t, orderTypes]
    );

    const options = useMemo(
        () =>
            [
                {
                    value: {
                        id: '__all',
                        name: t('settings.customizableFields.orderTypesSelector.allOrderTypes'),
                    },
                    label: t('settings.customizableFields.orderTypesSelector.allOrderTypes'),
                },
            ].concat(
                allTypes
                    ? allTypes.map((x) => ({ value: { id: x.key, name: x.name }, label: x.name }))
                    : []
            ),
        [t, allTypes]
    );

    return (
        <Dropdown
            label={t('settings.customizableFields.orderTypesSelector.label')}
            multiple
            cmosVariant="default"
            name={'orderTypesSelector'}
            value={value}
            options={options}
            CustomOption={CustomOption}
            onChange={onChange}
            getOptionValue={(x) => x.value.id}
            styles={{
                option: {
                    '& > div > :last-child': {
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                    },
                },
                indicatorsContainer: {
                    color: theme.palette.primary.light,
                },
            }}
        />
    );
}

function CustomOption(props: OptionProps<OptionData<OrderType>, true>) {
    const { children, innerProps, isSelected, innerRef } = props;

    return (
        <SMenuItem
            sx={props.getStyles('option', props)}
            ref={innerRef}
            component="div"
            selected={isSelected}
            {...innerProps}
        >
            {isSelected ? <CheckBoxIcon /> : <UncheckBoxIcon />}
            <div>{children}</div>
        </SMenuItem>
    );
}
