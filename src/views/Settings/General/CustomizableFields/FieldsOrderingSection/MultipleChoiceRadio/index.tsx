import { Box, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { styled } from '@mui/styles';
import { MultipleChoiceType } from 'api/fields';
import InfoTooltip from 'common/components/InfoTooltip';
import { t } from 'i18next';
import { useEffect, useState } from 'react';

export type MultipleChoiceRadioProps = {
    value: MultipleChoiceType;
    onChanged: (newValue: MultipleChoiceType) => void;
};
export function MultipleChoiceRadio({ value, onChanged }: MultipleChoiceRadioProps) {
    const [multipleChoiceType, setMultipleChoiceType] = useState<MultipleChoiceType>(value);

    useEffect(() => {
        setMultipleChoiceType(value);
    }, [value]);

    return (
        <>
            <Box sx={{ display: 'flex', marginBottom: '5px' }}>
                <StyledLabelBold>
                    {t('settings.customizableFields.multipleChoiceSection.selectionType')}
                </StyledLabelBold>
                <InfoTooltip
                    position="right"
                    text={t('settings.customizableFields.multipleChoiceSection.selectionTypeInfo')}
                />
            </Box>
            <StyledRadioGroup
                defaultValue={value}
                name="multipleChoiceType"
                value={multipleChoiceType}
                onChange={(_, newValue) => {
                    const newMultipleChoiceType = newValue as MultipleChoiceType;
                    setMultipleChoiceType(newMultipleChoiceType);
                    onChanged(newMultipleChoiceType);
                }}
            >
                <FormControlLabel
                    value="Single"
                    control={<Radio />}
                    label={
                        <StyledSpan>
                            {t(
                                'settings.customizableFields.multipleChoiceSection.multipleChoiceSingleOption'
                            )}
                        </StyledSpan>
                    }
                />
                <FormControlLabel
                    value="Multiple"
                    control={<Radio />}
                    label={
                        <StyledSpan>
                            {t(
                                'settings.customizableFields.multipleChoiceSection.multipleChoiceMultipleOption'
                            )}
                        </StyledSpan>
                    }
                />
            </StyledRadioGroup>
        </>
    );
}

const StyledLabelBold = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[8],
    fontWeight: 'bold',
}));

const StyledSpan = styled('span')(({ theme }) => ({
    color: theme.palette.neutral[8],
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
}));

const StyledRadioGroup = styled(RadioGroup)({
    marginBottom: '10px',
});
