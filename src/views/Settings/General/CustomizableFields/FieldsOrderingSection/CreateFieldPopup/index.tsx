import { Box, Divider, FormControlLabel, styled, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { isCmosError } from 'api/error';
import FieldsApi, {
    FieldSection,
    FieldType,
    mapSettingsFieldTypeOption,
    SELECTABLE_FIELD_TYPES,
    SettingsFieldTypeOption,
} from 'api/fields';
import { Button } from 'common/components/Button';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { Switch } from 'common/components/Inputs/Switch';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { SMenuItem } from 'common/components/mui';
import { SSelectInput } from 'common/components/mui/SSelect';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import { useMemo, useState } from 'react';
import { useFields } from '../../common/hooks';
import { SettingsFieldTypeOptionIcon } from '../FieldsList/FieldElement';
import MultipleChoiceOptionList from '../MultipleChoiceOptionsList';
import { createDefaultOption } from '../MultipleChoiceOptionsList/helper';
import { MultipleChoiceRadio } from '../MultipleChoiceRadio';
import OrderTypesSelector, { OrderType } from '../OrderTypesSelector';

type CreateFieldPopupProps = {
    open: boolean;
    section: FieldSection;
    onClose: () => void;
    onFieldCreated: () => void;
};

export type MultipleChoiceOptionItem = {
    id: string | null;
    name: string;
    color: string | null;
};

const MOBILE_SECTIONS: Partial<Record<FieldSection, FieldSection>> = {
    WebCustomerInfo: 'MobileCustomer',
    WebVehicleInfo: 'MobileVehicle',
    WebOrderInfo: 'MobileOrder',
    WebJobInfo: 'MobileJobInfo',
    MobileCustomer: 'WebCustomerInfo',
    MobileOrder: 'WebOrderInfo',
    MobileVehicle: 'WebVehicleInfo',
    MobileJobInfo: 'WebJobInfo',
};

const FIELD_TYPES_IN_ORDER = SELECTABLE_FIELD_TYPES;
const COLUMN_TYPES_IN_ORDER = ['Numeric', 'Currency', 'ShortText'] as SettingsFieldTypeOption[];

export default function CreateFieldPopup({
    open,
    section,
    onClose,
    onFieldCreated,
}: CreateFieldPopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const { fields } = useFields(section);

    const [name, setName] = useState('');
    const [type, setType] = useState<SettingsFieldTypeOption | null>(null);
    const [fieldType, setFieldType] = useState<FieldType | null>(null);
    const [optionsValid, setOptionsValid] = useState<boolean>(true);

    const [mobileSectionEnabled, setMobileSectionEnabled] = useState(false);
    const [requiredWeb, setRequiredWeb] = useState(false);
    const [requiredMobile, setRequiredMobile] = useState(false);
    const [hasExtraText, setHasExtraText] = useState(false);
    const [hasColor, setHasColor] = useState(false);

    const isColumn = section === 'WebLabor' || section === 'WebParts';
    const mobileSection = MOBILE_SECTIONS[section];
    const showMandatoryField = section === 'WebJobInfo' || section === 'MobileJobInfo';
    const showOrderTypeField = section === 'WebJobInfo' || section === 'MobileJobInfo';
    const showHasColorSwitch = section === 'WebJobInfo' && fieldType === 'Select';
    const allowedTypes = isColumn ? COLUMN_TYPES_IN_ORDER : FIELD_TYPES_IN_ORDER;

    const valid = name.trim().length > 0 && !!type && allowedTypes.includes(type) && optionsValid;

    const [options, setOptions] = useState<MultipleChoiceOptionItem[]>([]);
    const [orderTypes, setOrderTypes] = useState<OrderType[]>([]);

    const colorFieldId = useMemo(() => fields.find((f) => f.hasColor)?.id ?? null, [fields]);

    const createField = useMutation(
        async () => {
            const sections = [section];
            if (mobileSection && mobileSectionEnabled) {
                sections.push(mobileSection);
            }
            if (!fieldType) throw new Error('type is not set');

            await FieldsApi.create({
                name,
                sections,
                type: fieldType,
                multipleChoiceOptions: options,
                showConditions: orderTypes,
                requiredOnWeb:
                    requiredWeb ||
                    (showMandatoryField && mobileSection !== undefined && requiredMobile),
                requiredOnMobile:
                    requiredMobile ||
                    (showMandatoryField && mobileSection !== undefined && requiredWeb),
                hasExtraText,
                hasColor,
            });
        },
        {
            onError: (err) => {
                if (isCmosError(err) && err.cmosCode === 'General.Fields.NameConflict') {
                    toasters.danger(
                        t('settings.customizableFields.createNewModal.nameConflict.text'),
                        t('settings.customizableFields.createNewModal.nameConflict.title')
                    );
                } else {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
            onSuccess: () => {
                if (isColumn) {
                    toasters.success(
                        t('settings.customizableFields.createNewModal.successNotifColumn.text'),
                        t('settings.customizableFields.createNewModal.successNotifColumn.title')
                    );
                } else {
                    toasters.success(
                        t('settings.customizableFields.createNewModal.successNotif.text'),
                        t('settings.customizableFields.createNewModal.successNotif.title')
                    );
                }
                onFieldCreated();
            },
        }
    );

    const onTypeSelectChanged = (newType: SettingsFieldTypeOption) => {
        setType(newType);

        if (newType === 'MultipleChoice') {
            setFieldType('Select');
            setOptions([createDefaultOption(0), createDefaultOption(1)]);
        } else {
            const newFieldType = mapSettingsFieldTypeOption(newType);
            if (!newFieldType) {
                throw new Error('Unsupported field type.');
            }
            setFieldType(newFieldType);
        }
    };

    return (
        <Modal
            boxComponent={ModalBox}
            open={open}
            onClose={onClose}
            onTransitionExited={() => {
                if (!open) {
                    setName('');
                    setType(null);
                    setFieldType(null);
                    setOptionsValid(true);
                    setMobileSectionEnabled(false);
                    setRequiredWeb(false);
                    setRequiredMobile(false);
                    setHasExtraText(false);
                    setHasColor(false);
                    setOrderTypes([]);
                }
            }}
        >
            <Box display="flex" marginBottom={3} padding={'0 20px'}>
                <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h4Inter">
                        {isColumn
                            ? t('settings.customizableFields.createNewModal.headerColumn')
                            : t('settings.customizableFields.createNewModal.header')}
                    </Typography>
                </Box>

                <Box
                    sx={{ display: 'flex', alignItems: 'center', justifyContent: 'end' }}
                    style={{ gap: '16px' }}
                >
                    <Button
                        onClick={() => onClose()}
                        w="md"
                        color={Colors.Neutral3}
                        label={t('commonLabels.cancel')}
                    />
                    <Button
                        showLoader={createField.isLoading}
                        disabled={createField.isLoading || !valid}
                        onClick={() => createField.mutate()}
                        w="md"
                        color={Colors.Success}
                        label={
                            isColumn
                                ? t('settings.customizableFields.createNewModal.addColumn')
                                : t('settings.customizableFields.createNewModal.addField')
                        }
                    />
                </Box>
            </Box>
            <ScrollContainer>
                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        columnGap: '24px',
                        mb: 2,
                    }}
                >
                    <TextFormField
                        label={
                            isColumn
                                ? t('settings.customizableFields.createNewModal.nameColumn')
                                : t('settings.customizableFields.createNewModal.name')
                        }
                        placeholder={
                            isColumn
                                ? t(
                                      'settings.customizableFields.createNewModal.namePlaceholderColumn'
                                  )
                                : t('settings.customizableFields.createNewModal.namePlaceholder')
                        }
                        value={name}
                        maxLength={200}
                        onChange={(e) => setName(e.target.value)}
                        isRequired
                        showValidationIndicators
                    />
                    <InputWrapper
                        isRequired
                        showValidationIndicators
                        label={
                            isColumn
                                ? t('settings.customizableFields.createNewModal.typeColumn')
                                : t('settings.customizableFields.createNewModal.type')
                        }
                    >
                        <SSelectInput
                            menuBorders
                            placeholder={
                                isColumn
                                    ? t(
                                          'settings.customizableFields.createNewModal.typeColumnPlaceholder'
                                      )
                                    : t(
                                          'settings.customizableFields.createNewModal.typePlaceholder'
                                      )
                            }
                            value={type}
                            onChange={(e) =>
                                onTypeSelectChanged(e.target.value as SettingsFieldTypeOption)
                            }
                        >
                            {allowedTypes.map((x) => (
                                <SMenuItem key={x} value={x}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <SettingsFieldTypeOptionIcon type={x} />{' '}
                                        {t(`customizableFields.types.${x}`)}
                                    </Box>
                                </SMenuItem>
                            ))}
                        </SSelectInput>
                    </InputWrapper>
                </Box>

                {showOrderTypeField && (
                    <Box
                        sx={{
                            display: 'grid',
                            gridTemplateColumns: '1fr 1fr',
                            columnGap: '24px',
                            mb: 2,
                        }}
                    >
                        <OrderTypesSelector orderTypes={orderTypes} setOrderTypes={setOrderTypes} />
                    </Box>
                )}

                {type === 'MultipleChoice' && (
                    <>
                        <Divider style={{ marginBottom: '15px' }} />
                        <MultipleChoiceRadio
                            value={'Single'}
                            onChanged={(newMultipleChoiceType) => {
                                setFieldType(
                                    newMultipleChoiceType === 'Single' ? 'Select' : 'MultiSelect'
                                );
                            }}
                        />

                        <MultipleChoiceOptionList
                            fieldId={name}
                            options={options}
                            onOptionsChanged={setOptions}
                            onValidationStateChanged={setOptionsValid}
                            showColor={hasColor}
                        />

                        <Divider style={{ marginTop: '15px', marginBottom: '15px' }} />
                    </>
                )}

                {showHasColorSwitch && (
                    <>
                        <SwitchWithDetails
                            value={hasColor}
                            onChange={setHasColor}
                            title={t('settings.customizableFields.createNewModal.jobColor')}
                            subtitle={t(
                                'settings.customizableFields.createNewModal.jobColorSubTitle'
                            )}
                            disabled={!!colorFieldId}
                            labelSeparately
                        />
                        <Divider style={{ marginTop: '15px', marginBottom: '15px' }} />
                    </>
                )}

                <DivSwitchesRoot>
                    {(fieldType === 'MultiSelect' || fieldType === 'Select') && (
                        <SwitchWithDetails
                            value={hasExtraText}
                            onChange={setHasExtraText}
                            title={t('settings.customizableFields.createNewModal.hasExtraText')}
                            subtitle={t(
                                'settings.customizableFields.createNewModal.hasExtraTextSubtitle'
                            )}
                        />
                    )}

                    {mobileSection && !isColumn && (
                        <>
                            <SwitchWithDetails
                                value={mobileSectionEnabled}
                                onChange={(v) => {
                                    setMobileSectionEnabled(v);
                                    if (!v) {
                                        setRequiredMobile(false);
                                    }
                                }}
                                title={t(
                                    'settings.customizableFields.createNewModal.additionalSection'
                                )}
                                subtitle={t(
                                    'settings.customizableFields.createNewModal.additionalSectionSubtitle',
                                    {
                                        main: t(`settings.customizableFields.sections.${section}`),
                                        additional: t(
                                            `settings.customizableFields.sections.${mobileSection}`
                                        ),
                                        interpolation: { escapeValue: false },
                                    }
                                )}
                            />
                            {!showMandatoryField && (
                                <SwitchWithDetails
                                    value={requiredMobile}
                                    onChange={setRequiredMobile}
                                    disabled={!mobileSectionEnabled}
                                    title={t(
                                        'settings.customizableFields.createNewModal.requiredMobile'
                                    )}
                                    subtitle={t(
                                        'settings.customizableFields.createNewModal.requiredMobileSubtitle'
                                    )}
                                />
                            )}
                        </>
                    )}
                    {showMandatoryField && (
                        <SwitchWithDetails
                            value={section === 'WebJobInfo' ? requiredWeb : requiredMobile}
                            onChange={section === 'WebJobInfo' ? setRequiredWeb : setRequiredMobile}
                            title={t('settings.customizableFields.createNewModal.mandatoryField')}
                            subtitle={t(
                                'settings.customizableFields.createNewModal.mandatoryFieldSubtitle'
                            )}
                        />
                    )}
                </DivSwitchesRoot>
            </ScrollContainer>
        </Modal>
    );
}

const ModalBox = styled('div')({
    padding: '40px',
    width: 860,
});

const ScrollContainer = styled(OverlayScrollbarsComponent)({
    maxHeight: '516px',
    padding: '16px 16px 32px 16px',
});

const DivSwitchesRoot = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: '8px',
});

export function SwitchWithDetails({
    value,
    disabled = false,
    title,
    subtitle,
    onChange,
    labelSeparately = false,
}: {
    value: boolean;
    onChange: (value: boolean) => void;
    disabled?: boolean;
    title: string;
    subtitle: string;
    labelSeparately?: boolean;
}) {
    return (
        <>
            {labelSeparately ? (
                <>
                    <DivSwitchTitle>{title}</DivSwitchTitle>
                    <FormControlLabel
                        style={{ opacity: disabled ? 0.5 : 1 }}
                        checked={value}
                        onChange={(_, checked) => onChange(checked)}
                        control={<Switch color="primary" />}
                        disabled={disabled}
                        label={
                            <>
                                <DivSwitchSubtitle>{subtitle}</DivSwitchSubtitle>
                            </>
                        }
                    />
                </>
            ) : (
                <FormControlLabel
                    style={{ opacity: disabled ? 0.5 : 1 }}
                    checked={value}
                    onChange={(_, checked) => onChange(checked)}
                    control={<Switch color="primary" />}
                    disabled={disabled}
                    label={
                        <>
                            <DivSwitchTitle>{title}</DivSwitchTitle>
                            <DivSwitchSubtitle>{subtitle}</DivSwitchSubtitle>
                        </>
                    }
                />
            )}
        </>
    );
}

const DivSwitchSubtitle = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[8],
    ...theme.typography.body1,
}));

const DivSwitchTitle = styled('div')(({ theme }) => ({
    color: theme.palette.neutral[7],
    ...theme.typography.h6Inter,
}));
