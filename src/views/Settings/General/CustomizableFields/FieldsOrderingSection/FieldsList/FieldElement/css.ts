import { makeStyles } from '@mui/styles';

const useFieldElementStyles = makeStyles((theme) => ({
    root: {
        borderRadius: 10,
        border: `1px solid ${theme.palette.neutral[4]}`,
        backgroundColor: theme.palette.neutral[1],
        padding: '9px 10px',
        display: 'flex',
        gap: 10,
        alignItems: 'center',
        position: 'relative',
        '&:hover': {
            backgroundColor: theme.palette.neutral[3],
        },
    },

    handle: {
        borderRadius: 5,
        display: 'flex',
        alignItems: 'center',
    },

    name: {
        ...theme.typography.h5Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
    },

    actionBlock: {
        marginLeft: 'auto',
        display: 'flex',
        flexWrap: 'nowrap',
    },

    type: {
        ...theme.typography.h6Inter,
        display: 'flex',
        alignItems: 'center',
        color: theme.palette.neutral[7],
    },
}));

export default useFieldElementStyles;
