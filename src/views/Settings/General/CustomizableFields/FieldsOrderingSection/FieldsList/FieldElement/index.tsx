import {
    Box,
    FormControlLabel,
    IconButton,
    Switch,
    switchClasses,
    Typography,
} from '@mui/material';
import { styled } from '@mui/styles';
import {
    FieldListItemDto,
    FieldSection,
    FieldType,
    isPredefinedField,
    PREDEFINED_FIELDS,
    SettingsFieldTypeOption,
} from 'api/fields';
import clsx from 'clsx';
import { $Icon } from 'common/components/Icons/$Icon';
import ALetterIcon from 'common/components/Icons/ALetterIcon';
import BarCodeIcon from 'common/components/Icons/BarCode';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { CheckCircleIcon } from 'common/components/Icons/CheckCircleIcon';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { MicIcon } from 'common/components/Icons/MicIcon';
import { NumbersIcon } from 'common/components/Icons/NumbersIcon';
import { PtIcon } from 'common/components/Icons/PtIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { TFunction } from 'i18next';
import React, { memo, useCallback, useMemo } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import { useAppSelector } from 'store';
import { selectRepairShopFeatures } from 'store/slices/globalSettingsSlice';
import { useFields } from '../../../common/hooks';
import { useDeleteFieldPopup } from '../../DeleteFieldPopup/context';
import { useEditFieldPopupContext } from '../../EditFieldPopup/context';
import useFieldElementStyles from './css';

const SSwitch = styled(Switch)({
    [`& .${switchClasses.switchBase}`]: {
        [`& + .${switchClasses.track}`]: {
            height: 8,
            width: 22,
            alignSelf: 'center',
        },
        [`&.${switchClasses.checked}`]: {
            color: '#FFFFFF',
            [`&.${switchClasses.thumb}:before`]: {
                color: '#FFFFFF',
            },
            [`& + .${switchClasses.track}`]: {
                opacity: 1,
            },
        },
        [`&.${switchClasses.thumb}:before`]: {
            color: '#FFFFFF',
        },
    },
    [`& .${switchClasses.thumb}`]: {
        width: 12,
        height: 12,
        marginTop: 3.5,
    },
});

type FieldElementProps = {
    dragHandleProps?: DraggableProvidedDragHandleProps;
    field: FieldListItemDto;
    onHideIconClicked?: (fieldId: string, hidden: boolean) => void;
    onMandatoryIconClicked?: (fieldId: string, hidden: boolean) => void;
    section: FieldSection;
};

export default function FieldElement({
    dragHandleProps,
    field,
    onHideIconClicked,
    onMandatoryIconClicked,
    section,
}: FieldElementProps) {
    const styles = useFieldElementStyles();
    const { t } = useAppTranslation();
    const editFieldPopup = useEditFieldPopupContext();
    const deleteFieldPopup = useDeleteFieldPopup();
    const repairShopFeatures = useAppSelector(selectRepairShopFeatures);

    const { fields } = useFields(section);
    const colorFieldId = useMemo(() => fields.find((f) => f.hasColor)?.id ?? null, [fields]);

    const showMandatory = useMemo(
        () =>
            repairShopFeatures?.invoiceIntegration &&
            (section === 'WebOrderInfo' ||
                section === 'WebCustomerInfo' ||
                section === 'WebVehicleInfo'),
        [repairShopFeatures, section]
    );

    const isColumn = section === 'WebParts' || section === 'WebLabor';

    const translatedKey = `settings.customizableFields.predefined.${field.name.replaceAll(
        '.',
        '_'
    )}`;

    //lookup the translation for name value this field,
    // if it doesn't exist then display as is
    const fieldName = t(translatedKey) !== translatedKey ? t(translatedKey) : field.name;

    const onHideButtonClick = useCallback(() => {
        if (onHideIconClicked) onHideIconClicked(field.id, !field.isHidden);
    }, [field.id, field.isHidden, onHideIconClicked]);

    const onMandatoryButtonClick = useCallback(() => {
        if (onMandatoryIconClicked) onMandatoryIconClicked(field.id, !field.isRequired);
    }, [field.id, field.isRequired, onMandatoryIconClicked]);

    return (
        <div className={clsx(styles.root)}>
            <div className={styles.handle} {...(dragHandleProps ?? {})}>
                <DragAndDropIcon size={25} fill={Colors.Neutral7} />
            </div>
            <span className={styles.name}>{fieldName}</span>
            <div className={styles.type}>
                |&nbsp;
                <FieldTypeIcon name={field.name} type={field.type} /> {getFieldTypeName(t, field)}
            </div>
            {showMandatory && (
                <Box>
                    |&nbsp;
                    <FormControlLabel
                        checked={field.isRequired}
                        control={<SSwitch color="primary" />}
                        onClick={onMandatoryButtonClick}
                        label={
                            <Typography
                                sx={(theme) => ({
                                    ...theme.typography.h6Inter,
                                    fontWeight: 700,
                                    color: theme.palette.neutral[7],
                                    margin: '-5px',
                                })}
                            >
                                {t('siteForAppointments.common.mandatoryField')}
                            </Typography>
                        }
                        sx={{ marginLeft: '0px' }}
                    />
                </Box>
            )}
            <div className={styles.actionBlock}>
                {/* TODO add buttons for custom fields */}
                {field.canBeHidden && (
                    <IconButton size="small" onClick={onHideButtonClick}>
                        {field.isHidden ? (
                            <HideIcon fill={Colors.Neutral7} />
                        ) : (
                            <ShowIcon fill={Colors.CM1} />
                        )}
                    </IconButton>
                )}
                {field.isEditable && (
                    <>
                        <IconButton
                            size="small"
                            onClick={() => editFieldPopup.open(field.id, section, colorFieldId)}
                        >
                            <EditIcon fill={Colors.Neutral7} />
                        </IconButton>
                    </>
                )}
                {!isPredefinedField(field.type) && (
                    <>
                        <IconButton
                            size="small"
                            onClick={() =>
                                deleteFieldPopup.open({
                                    isColumn,
                                    id: field.id,
                                    name: field.name,
                                })
                            }
                        >
                            <DeleteIcon fill={Colors.Neutral7} />
                        </IconButton>
                    </>
                )}
            </div>
        </div>
    );
}

// extended field type with some custom types for special predefined fields
type FieldTypeX = FieldType | `predefined-${'Scan' | 'RecordAudio' | 'Dropdown'}`;

const PREDEFINED_FIELD_EXTENDED_TYPES: Record<
    string,
    Exclude<FieldTypeX, 'Predefined' | 'PredefinedAdditionalPosition'>
> = {
    [PREDEFINED_FIELDS.CUSTOMER_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_EMAIL]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_LANDLINE]: 'Numeric',
    [PREDEFINED_FIELDS.CUSTOMER_MOBILE]: 'Numeric',
    [PREDEFINED_FIELDS.CUSTOMER_BUSINESS_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_ID_DOCUMENT]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_PAYMENT_METHOD]: 'predefined-Dropdown',

    [PREDEFINED_FIELDS.VEHICLE_BRAND]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.VEHICLE_MODEL]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.VEHICLE_YEAR]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.VEHICLE_MILEAGE]: 'Numeric',
    [PREDEFINED_FIELDS.VEHICLE_PLATES]: 'ShortText',
    [PREDEFINED_FIELDS.VEHICLE_VIN]: 'ShortText',

    [PREDEFINED_FIELDS.ORDER_PHASE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_TOWER]: 'ShortText',
    [PREDEFINED_FIELDS.ORDER_TYPE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_IN_CHARGE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_TECHNICIAN]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_ONE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_TWO]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_THREE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_ADDITIONAL_POSITION_FOUR]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_ASSIGNED_TO]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_NOTE]: 'ShortText',

    [PREDEFINED_FIELDS.JOB_TEAM_MEMBER]: 'MultiSelect',
    [PREDEFINED_FIELDS.JOB_EMPLOYEE_ID]: 'ShortText',
    [PREDEFINED_FIELDS.JOB_PLANNING]: 'MultiSelect',
    [PREDEFINED_FIELDS.JOB_START_DATE]: 'Date',
    [PREDEFINED_FIELDS.JOB_START_TIME]: 'Time',
    [PREDEFINED_FIELDS.JOB_SCHEDULED_DURATION]: 'Numeric',
    [PREDEFINED_FIELDS.JOB_TIME_UNITS_SOLD]: 'Numeric',
    [PREDEFINED_FIELDS.JOB_JOB_DESCRIPTION]: 'LongText',

    [PREDEFINED_FIELDS.PARTS_NUMBER]: 'ShortText',
    [PREDEFINED_FIELDS.PARTS_QTY]: 'Numeric',
    [PREDEFINED_FIELDS.PARTS_AVAIL]: 'Numeric',
    [PREDEFINED_FIELDS.PARTS_PRICE_UNIT]: 'Currency',
    [PREDEFINED_FIELDS.PARTS_COST_UNIT]: 'Currency',

    [PREDEFINED_FIELDS.LABOR_HRS]: 'Numeric',
    [PREDEFINED_FIELDS.LABOR_PRICE_HR]: 'Currency',

    [PREDEFINED_FIELDS.MOB_ORDER]: 'Numeric',
    [PREDEFINED_FIELDS.MOB_ORDER_TOWER]: 'Numeric',
    [PREDEFINED_FIELDS.MOB_ORDER_TYPE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.MOB_ORDER_DELIVERY_DAY]: 'Date',
    [PREDEFINED_FIELDS.MOB_ORDER_DELIVERY_HOUR]: 'Time',
    [PREDEFINED_FIELDS.MOB_ORDER_ENTER_NOTES]: 'ShortText',
    [PREDEFINED_FIELDS.MOB_ORDER_RECORD_NOTES]: 'predefined-RecordAudio',

    [PREDEFINED_FIELDS.MOB_CUSTOMER_FIRST_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.MOB_CUSTOMER_LAST_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.MOB_CUSTOMER_MOBILE]: 'Numeric',
    [PREDEFINED_FIELDS.MOB_CUSTOMER_EMAIL]: 'ShortText',
    [PREDEFINED_FIELDS.MOB_CUSTOMER_LANDLINE]: 'ShortText',

    [PREDEFINED_FIELDS.MOB_VEHICLE_BRAND]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.MOB_VEHICLE_MODEL]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.MOB_VEHICLE_YEAR]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.MOB_VEHICLE_MILEAGE]: 'Numeric',
    [PREDEFINED_FIELDS.MOB_VEHICLE_SCAN_PLATES]: 'predefined-Scan',
    [PREDEFINED_FIELDS.MOB_VEHICLE_SCAN_VIN]: 'predefined-Scan',
    [PREDEFINED_FIELDS.MOB_VEHICLE_ENTER_VIN]: 'ShortText',
    [PREDEFINED_FIELDS.MOB_VEHICLE_ENTER_PLATES]: 'ShortText',

    [PREDEFINED_FIELDS.MOB_JOB_TEAM_MEMBER]: 'MultiSelect',
    [PREDEFINED_FIELDS.MOB_JOB_PLANNING]: 'MultiSelect',
    [PREDEFINED_FIELDS.MOB_JOB_JOB_DESCRIPTION]: 'LongText',

    [PREDEFINED_FIELDS.LOCATION_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_LEGAL_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_ADDRESS]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_PHONE_NUMBER]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_EMAIL_ADDRESS]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_WEBSITE]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_TAX_IDENTIFICATION]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_MONDAY_TO_FRIDAY_HOURS]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_SATURDAY_HOURS]: 'ShortText',
};

const DROPDOWN_ICON = <PtIcon fill="currentColor" />;
const RADIO_BUTTON_ICON = <CheckCircleIcon fill="currentColor" />;
const FREETEXT_ICON = <ALetterIcon />;
const NUMERIC_ICON = <NumbersIcon fill="currentColor" />;
const DATE_ICON = <CalendarIcon fill="currentColor" />;
const TIME_ICON = <ClockIcon fill="currentColor" />;
const RECORD_AUDIO_ICON = <MicIcon fill="currentColor" />;
const CURRENCY_ICON = (
    // eslint-disable-next-line react/jsx-pascal-case
    <$Icon fill="currentColor" />
);
const SCAN_ICON = <BarCodeIcon />;

const FIELD_ICONS: Record<
    Exclude<FieldTypeX, 'Predefined' | 'PredefinedAdditionalPosition'>,
    JSX.Element
> = {
    Currency: CURRENCY_ICON,
    Date: DATE_ICON,
    ShortText: FREETEXT_ICON,
    LongText: FREETEXT_ICON,
    Numeric: NUMERIC_ICON,
    Time: TIME_ICON,
    Select: RADIO_BUTTON_ICON,
    MultiSelect: RADIO_BUTTON_ICON,
    'predefined-Dropdown': DROPDOWN_ICON,
    'predefined-RecordAudio': RECORD_AUDIO_ICON,
    'predefined-Scan': SCAN_ICON,
};

function getFieldTypeName(t: TFunction, field: FieldListItemDto): string {
    if (field.type === 'PredefinedAdditionalPosition') {
        return t('customizableFields.types.predefined-Dropdown');
    }
    if (field.type === 'Predefined') {
        const xType = PREDEFINED_FIELD_EXTENDED_TYPES[field.name] ?? 'ShortText';
        return t(`customizableFields.types.${xType}`);
    } else if (field.type === 'Select' || field.type === 'MultiSelect') {
        return t('customizableFields.types.MultipleChoice');
    } else {
        return t(`customizableFields.types.${field.type}`);
    }
}

const FIELD_TYPES_ICONS: Record<
    Exclude<FieldType, 'Predefined' | 'PredefinedAdditionalPosition'>,
    JSX.Element
> = {
    ShortText: FREETEXT_ICON,
    LongText: FREETEXT_ICON,
    Numeric: NUMERIC_ICON,
    Time: TIME_ICON,
    Date: DATE_ICON,
    Currency: CURRENCY_ICON,
    Select: RADIO_BUTTON_ICON,
    MultiSelect: RADIO_BUTTON_ICON,
};

const SETTINGS_FIELD_TYPES_ICONS: Record<SettingsFieldTypeOption, JSX.Element> = {
    ShortText: FREETEXT_ICON,
    LongText: FREETEXT_ICON,
    Numeric: NUMERIC_ICON,
    Time: TIME_ICON,
    Date: DATE_ICON,
    Currency: CURRENCY_ICON,
    MultipleChoice: RADIO_BUTTON_ICON,
    PredefinedAdditionalPosition: DROPDOWN_ICON,
};

export const FieldTypeIcon = memo(
    ({ type, name, className }: { type: FieldType; name?: string; className?: string }) => {
        if (type === 'PredefinedAdditionalPosition') {
            return DROPDOWN_ICON;
        }
        if (type === 'Predefined') {
            if (!name) return null;
            const xType = PREDEFINED_FIELD_EXTENDED_TYPES[name];
            return FIELD_ICONS[xType] ?? null;
        }

        return React.cloneElement(FIELD_TYPES_ICONS[type], { className });
    }
);
if (import.meta.env.NODE_ENV === 'development') FieldTypeIcon.displayName = 'FieldTypeIcon';

export const SettingsFieldTypeOptionIcon = memo(
    ({ type, className }: { type: SettingsFieldTypeOption; className?: string }) => {
        return React.cloneElement(SETTINGS_FIELD_TYPES_ICONS[type], {
            className,
            style: { color: 'var(--neutral8)' },
        });
    }
);
