import { CircularProgress } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import FieldsApi, { FieldListItemDto, FieldSection } from 'api/fields';
import { Button } from 'common/components/Button';
import { AddMenuIcon } from 'common/components/Icons/AddMenuIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback, useEffect, useState } from 'react';
import { DragDropContext, Draggable, DropResult, Droppable } from 'react-beautiful-dnd';
import FieldSectionTooltip from '../../../../common/FieldSectionTooltip';
import FieldElement from './FieldElement';
import useFieldsListStyles from './css';

type FieldsListProps = {
    onAddField: (section: FieldSection) => void;
    onFieldsReordered: (fields: FieldListItemDto[]) => void;
    fields: FieldListItemDto[];
    section: FieldSection;
    isLoading: boolean;
};

export default function FieldsList({
    section,
    onAddField,
    isLoading,
    fields: fieldsProp,
    onFieldsReordered,
}: FieldsListProps) {
    const { t } = useAppTranslation();
    const styles = useFieldsListStyles();
    const toasters = useToasters();
    const isColumnSection = section === 'WebLabor' || section === 'WebParts';
    // use additional state to store fields to avoid flickering
    // i.e. when fields are moved around they flicker back to initial state for a moment
    const [fields, setFields] = useState(fieldsProp);

    useEffect(() => {
        setFields(fieldsProp);
    }, [fieldsProp]);

    const onDragEnd = useCallback(
        (result: DropResult) => {
            const fromIndex = result.source.index;
            const toIndex = result.destination?.index;
            if (toIndex === undefined) {
                console.warn('[FieldList] toIndex is undefined');
                return;
            }

            const newFields = [...fields];
            const movedField = newFields[fromIndex];
            newFields.splice(fromIndex, 1);
            newFields.splice(toIndex, 0, movedField);
            setFields(newFields);
            onFieldsReordered(newFields);
        },
        [fields, onFieldsReordered]
    );

    const { mutate: toggleFieldHidden } = useMutation(
        ({ id, value }: { id: string; value: boolean }) => {
            return FieldsApi.setHiddenFlag(id, section, value);
        },
        {
            onMutate({ id, value }) {
                setFields((f) =>
                    f.map((x) => {
                        if (x.id === id)
                            return {
                                ...x,
                                isHidden: value,
                            };
                        return x;
                    })
                );
            },
            onError: (_: unknown) => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const { mutate: toggleFieldMandatory } = useMutation(
        ({ id, value }: { id: string; value: boolean }) => {
            return FieldsApi.setMandatoryFlag(id, section, value);
        },
        {
            onMutate({ id, value }) {
                setFields((f) =>
                    f.map((x) => {
                        if (x.id === id)
                            return {
                                ...x,
                                isRequired: value,
                            };
                        return x;
                    })
                );
            },
            onError: (_: unknown) => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const onHideIconClicked = useCallback(
        (fieldId: string, hidden: boolean) => {
            const toggledField = fields.find((x) => x.id === fieldId);

            if (!toggledField) {
                throw Error(`try to hide unknown field ${fieldId}`);
            }

            if (!toggledField.canBeHidden) {
                throw Error(`try to hide field ${fieldId}, that can't be hidden`);
            }

            toggleFieldHidden({
                id: fieldId,
                value: hidden,
            });
        },
        [toggleFieldHidden, fields]
    );

    const onMandatoryIconClicked = useCallback(
        (fieldId: string, hidden: boolean) => {
            toggleFieldMandatory({
                id: fieldId,
                value: hidden,
            });
        },
        [toggleFieldMandatory]
    );

    return (
        <section className={styles.root}>
            <header className={styles.header}>
                <span>{t(`settings.customizableFields.sections.${section}-header`)}</span>{' '}
                <FieldSectionTooltip section={section} />
            </header>
            <div className={styles.body}>
                <DragDropContext onDragEnd={onDragEnd}>
                    <Droppable droppableId="settings-fields">
                        {(droppable) => (
                            <div
                                className={styles.list}
                                ref={droppable.innerRef}
                                {...droppable.droppableProps}
                            >
                                {isLoading ? (
                                    <CircularProgress size={20} thickness={5} />
                                ) : (
                                    fields.map((field, index) => (
                                        <Draggable
                                            index={index}
                                            draggableId={field.id}
                                            key={field.id}
                                        >
                                            {(draggable) => (
                                                <div
                                                    className={styles.item}
                                                    ref={draggable.innerRef}
                                                    {...draggable.draggableProps}
                                                >
                                                    <FieldElement
                                                        section={section}
                                                        dragHandleProps={
                                                            draggable.dragHandleProps ?? undefined
                                                        }
                                                        field={field}
                                                        onHideIconClicked={onHideIconClicked}
                                                        onMandatoryIconClicked={
                                                            onMandatoryIconClicked
                                                        }
                                                    />
                                                </div>
                                            )}
                                        </Draggable>
                                    ))
                                )}
                                {droppable.placeholder}
                            </div>
                        )}
                    </Droppable>
                </DragDropContext>
                <Button
                    disabled={isLoading}
                    className={styles.addButton}
                    onClick={() => onAddField(section)}
                    Icon={AddMenuIcon}
                    label={
                        isColumnSection
                            ? t('settings.customizableFields.addColumn')
                            : t('settings.customizableFields.addField')
                    }
                />
            </div>
        </section>
    );
}
