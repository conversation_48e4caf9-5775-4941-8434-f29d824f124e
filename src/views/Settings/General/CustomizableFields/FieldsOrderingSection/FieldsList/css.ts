import { makeStyles } from '@mui/styles';

const useFieldsListStyles = makeStyles((theme) => ({
    root: {
        borderRadius: 10,
        border: `1px solid ${theme.palette.neutral[4]}`,
        overflow: 'hidden',
        marginTop: 20,
    },
    header: {
        backgroundColor: theme.palette.neutral[3],
        padding: '12px 15px',
        display: 'flex',
        alignItems: 'center',

        '& > span': {
            ...theme.typography.h5Roboto,
        },
    },
    list: {
        padding: 11,
        borderRadius: 10,
        backgroundColor: theme.palette.neutral[2],
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',

        // because "gap" prop doesn't work with react-beautiful-dnd
        paddingBottom: 1,
        '& > *': {
            marginBottom: 10,
        },
    },
    item: {
        willChange: 'transform',
    },
    body: {
        padding: 18,
    },
    addButton: {
        marginTop: 20,
    },
}));

export default useFieldsListStyles;
