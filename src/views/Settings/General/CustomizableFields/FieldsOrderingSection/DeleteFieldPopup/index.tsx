import { useMutation } from '@tanstack/react-query';
import Fields<PERSON><PERSON> from 'api/fields';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import React, { useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import { DeleteFieldPopupContext, IDeleteFieldPopupContext } from './context';

type DeleteFieldPopupProps = {
    id?: string;
    open: boolean;
    name?: string;
    isColumn?: boolean;
    onDeleted: () => void;
    onClose: () => void;
};

export default function DeleteFieldPopup({
    open,
    name,
    onDeleted,
    id,
    onClose,
    isColumn,
}: DeleteFieldPopupProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const deleteMutation = useMutation(
        () => {
            if (!id) throw new Error('id must be set');
            return FieldsApi.delete_(id);
        },
        {
            onSuccess: () => {
                toasters.success(
                    isColumn
                        ? t('settings.customizableFields.deleteModal.columnNotif.text')
                        : t('settings.customizableFields.deleteModal.fieldNotif.text'),
                    isColumn
                        ? t('settings.customizableFields.deleteModal.columnNotif.title')
                        : t('settings.customizableFields.deleteModal.fieldNotif.title')
                );
                onDeleted();
            },
        }
    );

    const title = isColumn
        ? t('settings.customizableFields.deleteModal.columnTitle')
        : t('settings.customizableFields.deleteModal.fieldTitle');

    return (
        <DeleteConfirmationPopup
            open={open}
            onClose={onClose}
            showLoader={deleteMutation.isLoading}
            title={title}
            body={
                <Trans
                    i18nKey={
                        isColumn
                            ? 'settings.customizableFields.deleteModal.columnBody'
                            : 'settings.customizableFields.deleteModal.fieldBody'
                    }
                    components={{
                        name: <strong />,
                    }}
                    values={{ name }}
                />
            }
            cancel={t('commonLabels.doNotDelete')}
            confirm={t('commonLabels.delete')}
            onConfirm={() => deleteMutation.mutate()}
        />
    );
}

export function DeleteFieldPopupProvider({
    children,
    onDeleted,
}: React.PropsWithChildren<{ onDeleted: () => void }>) {
    const [state, setState] = useState<{ name: string; id: string; isColumn: boolean } | null>(
        null
    );
    const ctx: IDeleteFieldPopupContext = useMemo(
        () => ({
            open: setState,
        }),
        []
    );

    return (
        <DeleteFieldPopupContext.Provider value={ctx}>
            {children}
            <DeleteFieldPopup
                open={!!state}
                id={state?.id}
                isColumn={state?.isColumn}
                name={state?.name}
                onClose={() => setState(null)}
                onDeleted={() => {
                    setState(null);
                    onDeleted();
                }}
            />
        </DeleteFieldPopupContext.Provider>
    );
}
