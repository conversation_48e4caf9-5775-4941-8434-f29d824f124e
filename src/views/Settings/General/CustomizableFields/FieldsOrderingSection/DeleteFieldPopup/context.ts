import { createContext, useContext } from 'react';

export interface IDeleteFieldPopupContext {
    open(data: { name: string; id: string; isColumn: boolean }): void;
}

export const DeleteFieldPopupContext = createContext<IDeleteFieldPopupContext | null>(null);

export function useDeleteFieldPopup() {
    const ctx = useContext(DeleteFieldPopupContext);
    if (!ctx) throw new Error('DeleteFieldPopupContext is not available');
    return ctx;
}
