import { CircularProgress } from '@mui/material';
import clsx from 'clsx';
import { Checkbox } from 'common/components/Inputs';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import { useStyles } from './css';

interface NotificationsCheckBoxProps {
    caption: string;
    onChange: (checked: boolean) => Promise<void>;
    checked?: boolean;
    disabled: boolean;
}

export const SettingCheckBox = ({
    caption,
    onChange,
    checked = false,
    disabled,
}: NotificationsCheckBoxProps) => {
    const styles = useStyles();
    const [saving, setSaving] = useState(false);

    const handleChange = async (checked: boolean) => {
        setSaving(true);
        try {
            await onChange(checked);
        } finally {
            setSaving(false);
        }
    };

    return (
        <div className={styles.settingCheckBox}>
            <div className={styles.caption}>{caption}</div>
            <Checkbox
                onChange={(_, checked) => handleChange(checked)}
                checked={checked}
                disabled={disabled}
                className={styles.checkBox}
            />
            <div className={clsx(styles.progress, saving && 'visible')}>
                <CircularProgress size={14} style={{ color: Colors.CM4 }} />
            </div>
        </div>
    );
};

export default SettingCheckBox;
