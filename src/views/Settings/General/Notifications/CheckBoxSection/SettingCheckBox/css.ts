import { makeStyles } from '@mui/styles';

export const useStyles = makeStyles((theme) => ({
    settingCheckBox: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: 266,
    },
    caption: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        width: 0,
        flexGrow: 1,
    },
    checkBox: {
        '&.MuiCheckbox-root': {
            padding: 0,
            marginLeft: 18,
        },
    },
    progress: {
        width: 14,
        height: 14,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        visibility: 'hidden',

        '&.visible': {
            visibility: 'visible',
        },
    },
}));
