import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import SettingCheckboxControl from 'views/Settings/common/BooleanSettingControl';
import { useStyles } from './css';

interface CheckBoxSectionProps<T extends Record<string, boolean>> {
    onLoaded: () => void;
    get: () => Promise<T>;
    save: (data: T) => Promise<T>;
    successBody: string;
    children: { caption: string; property: Extract<keyof T, string> }[];
}

/**
 * @deprecated
 */
export const CheckBoxSection = <T extends Record<string, boolean>>({
    onLoaded,
    get,
    save,
    successBody,
    children,
}: CheckBoxSectionProps<T>) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const dispatch = useAppDispatch();
    const { callApi: callApiGet } = useApiCall();
    const { callApi: callApiSave, apiCallStatus: apiCallStatusSave } = useApiCall();
    const [data, setData] = useState<T | null>(null);

    useEffect(() => {
        (async () => {
            const result = await callApiGet(() => get(), {
                selectErrorContent: () => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            });
            setData(result);
            onLoaded();
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [callApiGet, get, t]);

    const doSave = async (key: keyof T, value: boolean) => {
        if (data) {
            const result = await callApiSave(
                () =>
                    save({
                        ...data,
                        [key]: value,
                    }),
                {
                    selectErrorContent: () => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );
            dispatch(
                setNewToaster(
                    new NotificationData(
                        successBody,
                        t('settings.cm.updatedConfiguration'),
                        NotificationType.success
                    )
                )
            );
            setData(result);
        }
    };

    return (
        <>
            {children.map((cb) => (
                <SettingCheckboxControl
                    key={cb.property}
                    label={cb.caption}
                    value={data?.[cb.property] ?? false}
                    onChange={(checked) => doSave(cb.property, checked)}
                    disabled={apiCallStatusSave === 'Pending'}
                />
            ))}
        </>
    );
};

export default CheckBoxSection;
