import { ActivityReportsAPI } from 'api/settings/ActivityReports';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { SettingsSection } from 'views/Settings/common';
import CheckBoxSection from '../CheckBoxSection';

interface ActivityReportsProps {
    onLoaded: () => void;
}

export const ActivityReports = ({ onLoaded }: ActivityReportsProps) => {
    const { t } = useAppTranslation();

    return (
        <SettingsSection label={t('settings.cm.activityReports.title')}>
            <CheckBoxSection
                onLoaded={onLoaded}
                get={ActivityReportsAPI.get}
                save={ActivityReportsAPI.save}
                successBody={t('settings.cm.activityReports.successBody')}
            >
                {[
                    {
                        caption: t('settings.cm.activityReports.enableDaily'),
                        property: 'enableDailyActivityReport',
                    },
                    {
                        caption: t('settings.cm.activityReports.enableWeekly'),
                        property: 'enableWeeklyActivityReport',
                    },
                    {
                        caption: t('settings.cm.activityReports.enableMonthly'),
                        property: 'enableMonthlyActivityReport',
                    },
                ]}
            </CheckBoxSection>
        </SettingsSection>
    );
};

export default ActivityReports;
