import { Grid } from '@mui/material';
import ShortcutAPI from 'api/settings/Shortcut';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { ShortcutDto, ShortcutType } from 'datacontracts/Shortcut/ShortcutDto';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { SettingsSection } from 'views/Settings/common';
import ListOptionControl, { ListOptionValue } from 'views/Settings/common/ListOptionControl';
import useSettingStyles from 'views/Settings/common/styles';
import { SmsShortcutKeyEditor } from './SmsShortcutKeyEditor';
import { SmsShortcutValueEditor } from './SmsShortcutValueEditor/SmsShortcutValueEditor';
import { shortcutTextMaxSymbols } from './helpers';

type SmsShortcutsProps = {
    onLoaded: () => void;
};

export default function SmsShortcuts({ onLoaded }: SmsShortcutsProps) {
    const [emptySmsShortcut, setEmptySmsShortcut] = useState<ShortcutDto>();
    const { callApi: callApiGet } = useApiCall();
    const { callApi: callApiSave } = useApiCall();
    const { callApi: callApiDelete } = useApiCall();
    const settingsStyles = useSettingStyles();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();
    const [smsShortcuts, setSmsShortcuts] = useState<ShortcutDto[]>([]);

    useEffect(() => {
        (async () => {
            const result = await callApiGet(() => ShortcutAPI.list(), {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            });
            setSmsShortcuts(result.shortcuts.filter((s) => s.type === ShortcutType.SMS));
            onLoaded();
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [callApiGet, t]);

    const onAddClicked = () => {
        setEmptySmsShortcut({
            shortcutId: 0,
            name: '',
            text: '',
            type: ShortcutType.SMS,
        });
    };

    const onUpdated = async (updated: ListOptionValue<number>) => {
        const newOption: ShortcutDto = {
            shortcutId: updated.id,
            type: ShortcutType.SMS,
            name: updated.key,
            text: updated.value,
        };

        //editing
        if (newOption.shortcutId > 0) {
            const updatedList = smsShortcuts.map((s) =>
                s.shortcutId === updated.id ? newOption : s
            );
            setSmsShortcuts(updatedList);

            await callApiSave(
                () =>
                    ShortcutAPI.save({
                        shortcutId: updated.id,
                        shortcut: newOption,
                    }),
                {
                    selectErrorContent: (_) => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );
        }
        //adding
        else {
            const response = await callApiSave(
                () =>
                    ShortcutAPI.save({
                        shortcutId: null,
                        shortcut: newOption,
                    }),
                {
                    selectErrorContent: (_) => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );

            const updatedList = [...smsShortcuts, response];
            setSmsShortcuts(updatedList);
            setEmptySmsShortcut(undefined);
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('settings.notifications.smsShortcuts.savedSuccessfully'),
                    t('toasters.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    const onDeleted = async (deleted: ListOptionValue<number>) => {
        const updateList = smsShortcuts.filter((s) => s.shortcutId !== deleted.id);
        setSmsShortcuts(updateList);

        await callApiDelete(() => ShortcutAPI.delete(deleted.id), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('settings.notifications.smsShortcuts.deletedSuccessfully'),
                    t('toasters.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    const onCancelled = () => {
        setEmptySmsShortcut(undefined);
    };

    return (
        <SettingsSection label={t('settings.notifications.smsShortcuts.header')}>
            <Grid item xs={12} className={settingsStyles.listOptionsContainer}>
                {[...smsShortcuts, ...(emptySmsShortcut ? [emptySmsShortcut] : [])].map((a) => (
                    <ListOptionControl
                        key={a.shortcutId}
                        value={{ id: a.shortcutId, key: a.name, value: a.text }}
                        KeyEditor={SmsShortcutKeyEditor}
                        ValueEditor={SmsShortcutValueEditor}
                        handleOnSave={onUpdated}
                        handleOnDelete={onDeleted}
                        handleOnCancel={onCancelled}
                        forceEditState={a === emptySmsShortcut}
                        isOptionValid={(o) =>
                            !!o.key && !!o.value && o.value.length <= shortcutTextMaxSymbols
                        }
                    />
                ))}
                {emptySmsShortcut === undefined && (
                    <Button
                        className={settingsStyles.addButton}
                        cmosVariant={'stroke'}
                        label={t('settings.notifications.smsShortcuts.addShortcut')}
                        color={Colors.Neutral3}
                        iconPosition="right"
                        Icon={PlusIcon}
                        onClick={onAddClicked}
                    />
                )}
            </Grid>
        </SettingsSection>
    );
}
