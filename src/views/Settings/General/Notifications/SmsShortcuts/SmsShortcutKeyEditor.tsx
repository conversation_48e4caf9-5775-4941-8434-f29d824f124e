import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { KeyEditor } from 'views/Settings/common/ListOptionControl';
import { shortcutNameMaxSymbols } from './helpers';

export const SmsShortcutKeyEditor: KeyEditor = ({ onChange, ...props }) => {
    const { t } = useAppTranslation();
    return (
        <TextFormField
            {...props}
            onChange={(e) => onChange(e.target.value)}
            type="text"
            maxLength={shortcutNameMaxSymbols}
            name={'shortcutName'}
            placeholder={t('settings.notifications.smsShortcuts.enterShortcutName')}
        />
    );
};
