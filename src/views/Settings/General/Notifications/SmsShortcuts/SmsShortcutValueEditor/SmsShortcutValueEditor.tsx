import { Box } from '@mui/material';
import { Button } from 'common/components/Button';
import { UrlIcon } from 'common/components/Icons/UrlIcon';
import { LimitedTextArea } from 'common/components/Inputs';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useRef } from 'react';
import { ValueEditor } from 'views/Settings/common/ListOptionControl';
import { shortcutTextMaxSymbols } from '../helpers';
import { useStyles } from './css';

export const SmsShortcutValueEditor: ValueEditor = ({ onChange, ...props }) => {
    const textAreaRef = useRef<HTMLTextAreaElement>(null);
    const { t } = useAppTranslation();
    const styles = useStyles();

    const onInsertLinkClicked = () => {
        const textArea = textAreaRef.current;
        if (textArea) {
            textArea.focus();

            const insertedText = 'http://vehi.mobi/code';
            const value = textArea.value;
            const start = textArea.selectionStart;
            const end = textArea.selectionEnd;
            const result = value.slice(0, start) + insertedText + value.slice(end);

            onChange(result);

            textArea.selectionStart = textArea.selectionEnd = start + insertedText.length;
        }
    };

    return (
        <Box display="flex" width="100%">
            <LimitedTextArea
                {...props}
                onChange={(e) => onChange(e.target.value)}
                maxLength={shortcutTextMaxSymbols}
                rows={3}
                ref={textAreaRef}
                hideLabel
                name={'shortcut'}
                placeholder={t('settings.notifications.smsShortcuts.enterShortcut')}
                counterClassName={styles.counter}
            />
            <Button
                cmosVariant={'typography'}
                Icon={UrlIcon}
                color={Colors.Neutral3}
                onClick={onInsertLinkClicked}
            />
        </Box>
    );
};
