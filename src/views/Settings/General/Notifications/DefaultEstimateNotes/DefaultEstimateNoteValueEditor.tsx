import TextFormField from 'common/components/Inputs/TextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { KeyEditor } from 'views/Settings/common/ListOptionControl';

export const DefaultEstimateNoteValueEditor: KeyEditor = ({ onChange, ...props }) => {
    const { t } = useAppTranslation();
    return (
        <TextFormField
            {...props}
            onChange={(e) => onChange(e.target.value)}
            type="text"
            name={'note'}
            placeholder={t('settings.notifications.defaultEstimateNotes.note')}
        />
    );
};
