import { Grid } from '@mui/material';
import DefaultEstimateNoteAPI from 'api/settings/DefaultEstimateNote';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { DefaultEstimateNoteDto } from 'datacontracts/DefaultEstimateNoteDto';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { SettingsSection } from 'views/Settings/common';
import ListOptionControl, { ListOptionValue } from 'views/Settings/common/ListOptionControl';
import useSettingStyles from 'views/Settings/common/styles';
import { DefaultEstimateNoteValueEditor } from './DefaultEstimateNoteValueEditor';

type DefaultEstimateNotesProps = {
    onLoaded: () => void;
};

export default function DefaultEstimateNotes({ onLoaded }: DefaultEstimateNotesProps) {
    const [defaultEstimateNotes, setDefaultEstimateNotes] = useState<DefaultEstimateNoteDto[]>([]);
    const [emptyDefaultEstimateNote, setEmptyDefaultEstimateNote] =
        useState<DefaultEstimateNoteDto>();

    const { callApi: callApiGet } = useApiCall();
    const { callApi: callApiSave } = useApiCall();
    const { callApi: callApiDelete } = useApiCall();
    const { id: repairShopId } = useSelector(selectSettings);
    const settingsStyles = useSettingStyles();
    const dispatch = useAppDispatch();
    const { t } = useAppTranslation();

    useEffect(() => {
        (async () => {
            const result = await callApiGet(() => DefaultEstimateNoteAPI.list(), {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            });

            setDefaultEstimateNotes(result.defaultEstimateNotes);
            onLoaded();
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [callApiGet, t]);

    const onAddClicked = () => {
        setEmptyDefaultEstimateNote({
            defaultEstimateNoteId: 0,
            repairShopId: repairShopId,
            text: '',
        });
    };

    const onUpdated = async (updated: ListOptionValue<number>) => {
        const newOption: DefaultEstimateNoteDto = {
            defaultEstimateNoteId: updated.id,
            repairShopId: repairShopId,
            text: updated.value,
        };

        //editing
        if (newOption.defaultEstimateNoteId > 0) {
            const updatedList = defaultEstimateNotes.map((s) =>
                s.defaultEstimateNoteId === updated.id ? newOption : s
            );
            setDefaultEstimateNotes(updatedList);

            await callApiSave(
                () =>
                    DefaultEstimateNoteAPI.save({
                        defaultEstimateNoteId: updated.id,
                        defaultEstimateNote: newOption,
                    }),
                {
                    selectErrorContent: (_) => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );
        }
        //adding
        else {
            const response = await callApiSave(
                () =>
                    DefaultEstimateNoteAPI.save({
                        defaultEstimateNoteId: null,
                        defaultEstimateNote: newOption,
                    }),
                {
                    selectErrorContent: (_) => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );

            const updatedList = [...defaultEstimateNotes, response];
            setDefaultEstimateNotes(updatedList);
            setEmptyDefaultEstimateNote(undefined);
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('settings.notifications.defaultEstimateNotes.savedSuccessfully'),
                    t('toasters.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    const onDeleted = async (deleted: ListOptionValue<number>) => {
        const updateList = defaultEstimateNotes.filter(
            (s) => s.defaultEstimateNoteId !== deleted.id
        );
        setDefaultEstimateNotes(updateList);

        await callApiDelete(() => DefaultEstimateNoteAPI.delete(deleted.id), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('settings.notifications.defaultEstimateNotes.deletedSuccessfully'),
                    t('toasters.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    const onCancelled = () => {
        setEmptyDefaultEstimateNote(undefined);
    };

    return (
        <SettingsSection label={t('settings.notifications.defaultEstimateNotes.header')}>
            <Grid item xs={12} className={settingsStyles.listOptionsContainer}>
                {[
                    ...defaultEstimateNotes,
                    ...(emptyDefaultEstimateNote ? [emptyDefaultEstimateNote] : []),
                ].map((a, i) => (
                    <ListOptionControl
                        key={a.defaultEstimateNoteId}
                        value={{
                            id: a.defaultEstimateNoteId,
                            key: `${t('settings.notifications.defaultEstimateNotes.note')} ${
                                i + 1
                            }`,
                            value: a.text,
                        }}
                        ValueEditor={DefaultEstimateNoteValueEditor}
                        handleOnSave={onUpdated}
                        handleOnDelete={onDeleted}
                        handleOnCancel={onCancelled}
                        forceEditState={a === emptyDefaultEstimateNote}
                        isOptionValid={(o) => !!o.value}
                    />
                ))}
                {emptyDefaultEstimateNote === undefined && (
                    <Button
                        className={settingsStyles.addButton}
                        cmosVariant={'stroke'}
                        label={t('settings.notifications.defaultEstimateNotes.addNote')}
                        color={Colors.Neutral3}
                        iconPosition="right"
                        Icon={PlusIcon}
                        onClick={onAddClicked}
                    />
                )}
            </Grid>
        </SettingsSection>
    );
}
