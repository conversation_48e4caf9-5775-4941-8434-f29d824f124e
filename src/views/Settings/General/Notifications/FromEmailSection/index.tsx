import { Box, IconButton, styled, useTheme } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import SmtpApi, { ConnectionType, SmtpDto } from 'api/settings/Smtp';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import InfoTooltip from 'common/components/InfoTooltip';
import { NumberField } from 'common/components/Inputs/NumberField';
import PasswordField from 'common/components/Inputs/PasswordField';
import { Modal } from 'common/components/Modal';
import { TextField } from 'common/components/mui';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import queryClient from 'query-client';
import { useEffect, useState } from 'react';
import RadioButtonArray, { RadioButtonArrayProps } from 'views/Components/RadioButtonArray';
import { SettingLayoutControl, SettingsSection } from 'views/Settings/common';
import SettingCheckboxControl from 'views/Settings/common/BooleanSettingControl';
import { useSettingsToasters } from 'views/Settings/helpers';

type FromEmailSectionProps = {
    onLoaded: () => void;
};

export default function FromEmailSection({ onLoaded }: FromEmailSectionProps) {
    const { t } = useAppTranslation();
    const theme = useTheme();
    const { settingSaved } = useSettingsToasters();

    const getDefaultPort = (connectionType: ConnectionType) => {
        switch (connectionType) {
            case 'Plain':
                return 25;
            case 'SSL':
                return 465;
            case 'TLS':
                return 587;
        }
    };

    const [smtpSettings, setSmtpSettings] = useState<SmtpDto>({
        enableCustomSmtpService: false,
        fromEmail: '',
        repairShopName: '',
        port: getDefaultPort('Plain'),
        connectionType: 'Plain',
        serverName: '',
        userName: '',
        password: '',
        supportContactEmail: '',
    });
    const [verifyResultPopupProps, setVerifyResultPopupProps] = useState({
        open: false,
        success: false,
    });

    const queryKey = ['smtp', 'settings'];
    const { data } = useQuery(queryKey, SmtpApi.get);

    useEffect(() => {
        if (data) {
            setSmtpSettings(data);
            onLoaded();
        }
    }, [data, onLoaded]);

    const updateSettings = (update: Partial<SmtpDto>) => {
        setSmtpSettings({ ...smtpSettings, ...update });
    };

    const verifyMutation = useMutation(SmtpApi.verify, {
        onSuccess: (response) => {
            if (response) {
                setVerifyResultPopupProps({ open: true, success: true });
            } else {
                setVerifyResultPopupProps({ open: true, success: false });
            }
        },
    });

    const saveMutation = useMutation(SmtpApi.save, {
        onSuccess: (data) => {
            queryClient.setQueryData(queryKey, data);
            settingSaved();
        },
    });

    return (
        <SettingsSection label={t('settings.notifications.fromEmail.header')}>
            <SettingCheckboxControl
                label={t('settings.notifications.fromEmail.enableFromEmail')}
                value={smtpSettings.enableCustomSmtpService}
                onChange={(v) => updateSettings({ enableCustomSmtpService: v })}
            />
            <TextSettingControl
                disabled={!smtpSettings.enableCustomSmtpService}
                name="emailAddress"
                label={t('settings.notifications.fromEmail.emailAddressLabel')}
                placeholder={t('settings.notifications.fromEmail.emailAddressPlaceholder')}
                hint={t('settings.notifications.fromEmail.emailAddressHint')}
                value={smtpSettings.fromEmail}
                onChange={(v) => updateSettings({ fromEmail: v })}
            />
            <TextSettingControl
                disabled={!smtpSettings.enableCustomSmtpService}
                name="repairShopName"
                label={t('settings.notifications.fromEmail.repairShopNameLabel')}
                placeholder={t('settings.notifications.fromEmail.repairShopNamePlaceholder')}
                hint={t('settings.notifications.fromEmail.repairShopNameHint')}
                value={smtpSettings.repairShopName}
                onChange={(v) => updateSettings({ repairShopName: v })}
            />
            <ConnectionTypeAndPortControl
                disabled={!smtpSettings.enableCustomSmtpService}
                onChange={(value) =>
                    updateSettings({ connectionType: value, port: getDefaultPort(value) })
                }
                value={smtpSettings.connectionType}
                values={[
                    {
                        id: 'Plain',
                        value: 'Plain',
                        label: t('settings.notifications.fromEmail.connectionType.plain'),
                    },
                    {
                        id: 'SSL',
                        value: 'SSL',
                        label: t('settings.notifications.fromEmail.connectionType.ssl'),
                    },
                    {
                        id: 'TLS',
                        value: 'TLS',
                        label: t('settings.notifications.fromEmail.connectionType.tls'),
                    },
                ]}
                port={smtpSettings.port !== null ? smtpSettings.port : 25}
                onPortChange={(v) => {
                    updateSettings({ port: v });
                }}
            />
            <TextSettingControl
                disabled={!smtpSettings.enableCustomSmtpService}
                name="serverName"
                label={t('settings.notifications.fromEmail.serverNameLabel')}
                placeholder={t('settings.notifications.fromEmail.serverNamePlaceholder')}
                value={smtpSettings.serverName}
                onChange={(v) => updateSettings({ serverName: v })}
            />
            <TextSettingControl
                disabled={!smtpSettings.enableCustomSmtpService}
                name="userName"
                label={t('settings.notifications.fromEmail.userNameLabel')}
                placeholder={t('settings.notifications.fromEmail.userNamePlaceholder')}
                value={smtpSettings.userName}
                onChange={(v) => updateSettings({ userName: v })}
            />
            <SettingLayoutControl label={t('settings.notifications.fromEmail.passwordLabel')}>
                <SPasswordField
                    disabled={!smtpSettings.enableCustomSmtpService}
                    name="password"
                    placeholder={t('settings.notifications.fromEmail.passwordPlaceholder')}
                    value={smtpSettings.password}
                    onChange={(e) => updateSettings({ password: e.target.value })}
                />
            </SettingLayoutControl>
            <SettingLayoutControl>
                <Box display="flex" justifyContent="end" gap={2} width={'60%'}>
                    <Button
                        disabled={!smtpSettings.enableCustomSmtpService}
                        color={theme.palette.primary.main}
                        label={t('settings.notifications.fromEmail.verifyButton')}
                        cmosVariant={'stroke'}
                        onClick={() => verifyMutation.mutate(smtpSettings)}
                        customStyles={{ width: 160 }}
                        showLoader={verifyMutation.isLoading}
                    />
                    <Button
                        color={theme.palette.success.main}
                        label={t('commonLabels.save')}
                        cmosVariant={'filled'}
                        onClick={() => saveMutation.mutate(smtpSettings)}
                        customStyles={{ width: 160 }}
                        showLoader={saveMutation.isLoading}
                    />
                </Box>
            </SettingLayoutControl>
            <VerifyResultPopup
                open={verifyResultPopupProps.open}
                success={verifyResultPopupProps.success}
                supportContact={smtpSettings.supportContactEmail}
                onClose={() => setVerifyResultPopupProps({ open: false, success: false })}
            />
        </SettingsSection>
    );
}

type TextSettingControlProps = {
    name: string;
    label: string;
    placeholder?: string;
    hint?: string;
    value: string;
    disabled: boolean;
    onChange: (value: string) => void;
};

function TextSettingControl({
    name,
    label,
    placeholder,
    hint,
    value,
    disabled,
    onChange,
}: TextSettingControlProps) {
    return (
        <SettingLayoutControl label={label}>
            <FieldContainer>
                <STextField
                    disabled={disabled}
                    name={name}
                    placeholder={placeholder}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                />
                {hint && <InfoTooltip text={hint} position="right" />}
            </FieldContainer>
        </SettingLayoutControl>
    );
}

const STextField = styled(TextField)({
    width: '60%',
    minWidth: '60%',
    height: 40,
});

const SNumberField = styled(NumberField)({
    width: 60,
    height: 40,
});

const SPasswordField = styled(PasswordField)({
    width: '60%',
    height: 40,
});

const FieldContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 5,
});

type ConnectionTypeAndPortControlProps = {
    disabled: boolean;
    port: number | null;
    onPortChange: (value: number | null) => void;
} & RadioButtonArrayProps<ConnectionType>;

function ConnectionTypeAndPortControl({
    disabled,
    port,
    onPortChange,
    ...props
}: ConnectionTypeAndPortControlProps) {
    const { t } = useAppTranslation();

    return (
        <SettingLayoutControl label={t('settings.notifications.fromEmail.connectionTypeLabel')}>
            <ConnectionTypeAndPortContainer>
                <RadioButtonArray<ConnectionType> {...props} disabled={disabled} />
                <PortFieldContainer>
                    <PortLabel>{t('settings.notifications.fromEmail.portLabel')}</PortLabel>
                    <SNumberField
                        disabled={disabled}
                        name="port"
                        value={port}
                        onValueChange={(v) => {
                            const value = v.floatValue === undefined ? null : v.floatValue;
                            onPortChange(value);
                        }}
                    />
                </PortFieldContainer>
            </ConnectionTypeAndPortContainer>
        </SettingLayoutControl>
    );
}

const ConnectionTypeAndPortContainer = styled('div')({
    display: 'flex',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'space-between',

    width: '60%',
});

const PortFieldContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    gap: 16,
});

const PortLabel = styled('span')(({ theme }) => ({
    display: 'inline-block',
    color: theme.palette.neutral[7],
    fontWeight: 700,
}));

type VerifyResultPopupProps = {
    open: boolean;
    success: boolean;
    supportContact: string;
    onClose: () => void;
};

function VerifyResultPopup({ open, success, supportContact, onClose }: VerifyResultPopupProps) {
    const { t } = useAppTranslation();
    const theme = useTheme();

    return (
        <Modal open={open} boxComponent={success ? SuccessBoxComponent : WarningBoxComponent}>
            <RelativeDiv>
                <CloseButtonContainer>
                    <SIconButton onClick={onClose} size="large">
                        <CloseIcon fill={theme.palette.neutral[6]} />
                    </SIconButton>
                </CloseButtonContainer>
            </RelativeDiv>
            <ContentContainer>
                <TitleDiv>
                    {success
                        ? t('commonLabels.success')
                        : t('settings.notifications.fromEmail.verifyFailedTitle')}
                </TitleDiv>
                <BodyDiv>
                    {success
                        ? t('settings.notifications.fromEmail.verifySuccess')
                        : `${t(
                              'settings.notifications.fromEmail.verifyFailedBody'
                          )} ${supportContact}.`}
                </BodyDiv>
            </ContentContainer>
        </Modal>
    );
}

const WarningBoxComponent = styled('div')({
    border: `1px solid var(--yellow)`,
});

const SuccessBoxComponent = styled('div')(({ theme }) => ({
    border: `1px solid ${theme.palette.success.main}`,
}));

const RelativeDiv = styled('div')({
    position: 'relative',
});

const CloseButtonContainer = styled('div')({
    position: 'absolute',
    top: 0,
    right: 0,
});

const SIconButton = styled(IconButton)({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: 'none',
    cursor: 'pointer',
    marginRight: 15,
    marginTop: 15,
    backgroundColor: 'transparent',

    '&:focus': {
        outline: 'none',
    },
});

const ContentContainer = styled('div')({
    width: 464,
    minHeight: 150,
    padding: '30px',
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    alignItems: 'center',
});

const TitleDiv = styled('div')(({ theme }) => ({
    ...theme.typography.h4,
    color: theme.palette.neutral[8],

    marginBottom: 16,
    textAlign: 'center',
}));

const BodyDiv = styled('div')(({ theme }) => ({
    textAlign: 'center',

    ...theme.typography.h6,
    color: theme.palette.neutral[7],
}));
