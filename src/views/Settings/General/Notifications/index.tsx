import clsx from 'clsx';
import AreaSpinner from 'common/components/AreaSpinner';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useState } from 'react';
import { PageContent } from 'views/Components/Page';
import ActivityReports from './ActivityReports';
import NotificationsAndAlerts from './Alerts';
import { useStyles } from './css';
import DefaultEmail from './DefaultEmail';
import DefaultEstimateNotes from './DefaultEstimateNotes';
import DefaultSms from './DefaultSms';
import FromEmailSection from './FromEmailSection';
import Others from './Others';
import RecipientsSection from './RecipientsSection';
import SmsShortcuts from './SmsShortcuts';

export default function Notifications() {
    const { t } = useAppTranslation();
    const styles = useStyles();
    const [recipientsLoading, setRecipientsLoading] = useState(true);
    const [fromEmailLoading, setFromEmailLoading] = useState(true);
    const [alertsLoading, setAlertsLoading] = useState(true);
    const [defaultEmailLoading, setDefaultEmailLoading] = useState(true);
    const [defaultSmsLoading, setDefaultSmsLoading] = useState(true);
    const [smsShortcutsLoading, setSmsShortcutsLoading] = useState(true);
    const [defaultEstimateNotesLoading, setDefaultEstimateNotesLoading] = useState(true);
    const [activityReportsLoading, setActivityReportsLoading] = useState(true);
    const [othersLoading, setOthersLoading] = useState(true);

    const showLoader =
        recipientsLoading ||
        fromEmailLoading ||
        alertsLoading ||
        defaultEmailLoading ||
        defaultSmsLoading ||
        smsShortcutsLoading ||
        defaultEstimateNotesLoading ||
        activityReportsLoading ||
        othersLoading;

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.notifications.header')}`);

    return (
        <PageContent>
            {showLoader && <AreaSpinner />}
            <div className={clsx(styles.content, showLoader && 'hidden')}>
                <RecipientsSection onLoaded={() => setRecipientsLoading(false)} />
                <FromEmailSection onLoaded={() => setFromEmailLoading(false)} />
                <NotificationsAndAlerts onLoaded={() => setAlertsLoading(false)} />
                <DefaultEmail onLoaded={() => setDefaultEmailLoading(false)} />
                <DefaultSms onLoaded={() => setDefaultSmsLoading(false)} />
                <SmsShortcuts onLoaded={() => setSmsShortcutsLoading(false)} />
                <DefaultEstimateNotes onLoaded={() => setDefaultEstimateNotesLoading(false)} />
                <ActivityReports onLoaded={() => setActivityReportsLoading(false)} />
                <Others onLoaded={() => setOthersLoading(false)} />
            </div>
        </PageContent>
    );
}
