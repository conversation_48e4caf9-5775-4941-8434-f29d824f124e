import { TextArea, TextAreaProps } from 'common/components/Inputs';
import React, { useCallback, useMemo } from 'react';

type TextAreaWithParamsProps = TextAreaProps;

const TextAreaWithParams = React.forwardRef(
    (
        { value, onChange, ...props }: TextAreaWithParamsProps,
        ref: React.ForwardedRef<HTMLTextAreaElement>
    ) => {
        // We capture all params on Component initialization to make sure value will always include them
        const params = useMemo(() => {
            return value.split(/(\{.*?\})/gi).filter((e) => e.startsWith('{') && e.endsWith('}'));
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        const validateValue = useCallback(
            (newValue: string) => {
                var isValid = params.every((p) => newValue.includes(p));
                return isValid;
            },
            [params]
        );

        const handleOnChange = useCallback(
            (e: React.ChangeEvent<HTMLTextAreaElement>) => {
                if (onChange && validateValue(e.target.value)) {
                    onChange(e);
                }
            },
            [onChange, validateValue]
        );

        const handleOnKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
            if (e.key === 'Backspace' || e.key === 'Delete') {
                const result = calculateValueAfterBackspaceOrDeleteClick(value, e);
                if (!validateValue(result)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        };

        return (
            <TextArea
                {...props}
                value={value}
                onChange={handleOnChange}
                onKeyDown={handleOnKeyDown}
                // disable dragging
                onDragStart={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                }}
                ref={ref}
            />
        );
    }
);

const calculateValueAfterBackspaceOrDeleteClick = (
    source: string,
    e: React.KeyboardEvent<HTMLTextAreaElement>
): string => {
    const { currentTarget, key } = e;
    const { selectionStart: start, selectionEnd: end } = currentTarget;

    let result;
    if (start < end) {
        // delete everything between selectionStart and selectionEnd
        result = source.substring(0, start) + source.substring(end);
    } else if (key === 'Backspace' && start > 0) {
        // delete previous symbol based on selectionStart
        result = source.substring(0, start - 1) + source.substring(start);
    } else if (key === 'Delete' && start < source.length) {
        // delete next symbol based on selectionStart
        result = source.substring(0, start) + source.substring(start + 1);
    } else {
        // do nothing
        result = source;
    }

    return result;
};

export default TextAreaWithParams;
