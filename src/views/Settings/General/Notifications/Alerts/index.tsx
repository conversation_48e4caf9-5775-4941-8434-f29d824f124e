import { useQuery } from '@tanstack/react-query';
import AlertsSettingsAPI from 'api/settings/Alerts';
import RealTimeNotificationsSettingsApi, {
    RealTimeNotificationTemplateDto,
    RealTimeNotificationType,
    RealTimeNotificationsOrder,
} from 'api/settings/RealTimeNotifications';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { AlertDto } from 'datacontracts/Settings/Alerts/Alert';
import { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectRepairShopFeatures } from 'store/slices/globalSettingsSlice';
import { SettingsSection } from 'views/Settings/common';
import RealTimeAlertEditor from './RealTimeAlertEditor';
import RealTimeNotificationEditor from './RealTimeNotificationEditor';
import { useStyles } from './css';

type SectionProps = {
    onLoaded: () => void;
};

type NotificationFilterValidation = {
    type: number;
    validation: boolean;
};

function validNotifications(
    notifications: RealTimeNotificationTemplateDto[],
    validations: NotificationFilterValidation[]
): RealTimeNotificationTemplateDto[] {
    return notifications.filter((notification) =>
        validations.every((v) => v.type !== notification.notificationTypeId || v.validation)
    );
}

export const NotificationsAndAlerts = ({ onLoaded }: SectionProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [alerts, setAlerts] = useState<AlertDto[]>([]);
    const [notifications, setNotifications] = useState<RealTimeNotificationTemplateDto[]>([]);
    const onlinePaymentsEnabled = useSelector(selectRepairShopFeatures)?.onlinePaymentsEnabled;

    useQuery(
        ['settings-notifications-notifications'],
        RealTimeNotificationsSettingsApi.fetchTemplates,
        {
            onSuccess: (data: RealTimeNotificationTemplateDto[]) => {
                const sortedNotifications = data.sort(
                    (a, b) =>
                        RealTimeNotificationsOrder[a.notificationTypeId] -
                        RealTimeNotificationsOrder[b.notificationTypeId]
                );
                setNotifications(sortedNotifications);
            },
            onSettled: onLoaded,
            enabled: true,
        }
    );

    useQuery(['settings-notifications-alerts'], AlertsSettingsAPI.get, {
        onSuccess: setAlerts,
        onSettled: onLoaded,
    });

    const onAlertUpdated = useCallback(
        (newAlert: AlertDto) => {
            const newAlerts = alerts.map((a) => (a.id === newAlert.id ? newAlert : a));
            setAlerts(newAlerts);
        },
        [alerts]
    );

    const onNotificationUpdated = useCallback(
        (newNotification: RealTimeNotificationTemplateDto) => {
            const newNotifications = notifications.map((n) =>
                n.id === newNotification.id ? newNotification : n
            );
            setNotifications(newNotifications);
        },
        [notifications]
    );

    return (
        <SettingsSection
            label={t('settings.notifications.realTime.header')}
            tooltipText={t('settings.notifications.realTime.headerTooltip')}
            tooltipPosition="right"
        >
            <div className={styles.alerts}>
                {validNotifications(notifications, [
                    {
                        type: RealTimeNotificationType.PaymentReceived,
                        validation: onlinePaymentsEnabled ?? false,
                    },
                ]).map((n) => (
                    <RealTimeNotificationEditor
                        onUpdated={onNotificationUpdated}
                        key={n.id}
                        initial={n}
                    />
                ))}
            </div>

            <div className={styles.alerts}>
                {alerts.map((a) => (
                    <RealTimeAlertEditor
                        onAlertUpdated={onAlertUpdated}
                        key={a.id}
                        initialAlert={a}
                    />
                ))}
            </div>
        </SettingsSection>
    );
};

export default NotificationsAndAlerts;
