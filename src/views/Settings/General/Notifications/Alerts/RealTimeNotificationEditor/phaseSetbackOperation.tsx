import { Grid, IconButton, useTheme } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { RealTimeNotificationRecipient } from 'api/settings/RealTimeNotifications';
import { WpPhasesApi } from 'api/workshopPlanner';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import RemoveCircleIcon from 'common/components/Icons/RemoveCircleIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import ArrowTooltip from 'common/components/Tooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import RealTimeNotificationRecipients from './RealTimeNotificationRecipients';
import useRealTimeEditorStyles from './styles';

type PhaseSetbackOperationProps = {
    triggerParams: string | null;
    availableRecipients: RealTimeNotificationRecipient[];
    onUpdate: (params: string) => void;
};

export type PhaseSetbackRuleDto = {
    originPhaseId?: number;
    destinationPhaseId?: number;
    recipients: RealTimeNotificationRecipient[];
};

export const PhaseSetbackOperation = ({
    triggerParams,
    availableRecipients,
    onUpdate,
}: PhaseSetbackOperationProps) => {
    const theme = useTheme();
    const { t } = useAppTranslation();
    const [rules, setRules] = useState<PhaseSetbackRuleDto[]>(() => {
        if (!triggerParams) {
            return [{ recipients: [] }];
        }

        const parsedRules = JSON.parse(triggerParams);
        return parsedRules.length > 0 ? parsedRules : [{ recipients: [] }];
    });

    const handleChangeOriginPhase = (
        index: number,
        phaseId: number,
        undefineDestinationPhaseId: boolean
    ) => {
        const newRules = rules.map((rule, ruleIndex) =>
            ruleIndex === index
                ? {
                      ...rule,
                      originPhaseId: phaseId,
                      destinationPhaseId: undefineDestinationPhaseId
                          ? undefined
                          : rule.destinationPhaseId,
                  }
                : rule
        );
        setRules(newRules);

        mutateNotification(newRules);
    };

    const handleChangeDestinationPhase = (index: number, phaseId: number) => {
        const newRules = rules.map((rule, ruleIndex) =>
            ruleIndex === index ? { ...rule, destinationPhaseId: phaseId } : rule
        );
        setRules(newRules);

        mutateNotification(newRules);
    };

    const addRecipient = (index: number, recipient: RealTimeNotificationRecipient) => {
        const oldRule = rules[index];
        const updatedRule = { ...oldRule, recipients: [...oldRule.recipients, recipient] };
        const newRules = rules.map((rule, ruleIndex) => (ruleIndex === index ? updatedRule : rule));
        setRules(newRules);

        mutateNotification(newRules);
    };

    const removeRecipient = (index: number, recipient: RealTimeNotificationRecipient) => {
        const oldRule = rules[index];
        const updatedRule = {
            ...oldRule,
            recipients: oldRule.recipients.filter((x) => x.id !== recipient.id),
        };
        const newRules = rules.map((rule, ruleIndex) => (ruleIndex === index ? updatedRule : rule));
        setRules(newRules);

        mutateNotification(newRules);
    };

    const addRule = () => {
        const newRules = [...rules, { recipients: [] }];
        setRules(newRules);

        mutateNotification(newRules);
    };

    const removeRule = (index: number) => {
        const newRules = [...rules.filter((_, i) => i !== index)];
        setRules(newRules);

        mutateNotification(newRules);
    };

    const mutateNotification = (newRules: PhaseSetbackRuleDto[]) =>
        onUpdate(JSON.stringify(newRules));

    return (
        <Grid container direction="column" alignItems="flex-start">
            {rules.map((rule, index) => (
                <Grid item container direction="row" key={index} alignItems="flex-end">
                    <Grid item container direction="row" spacing={6} xs={10} xl={11}>
                        <PhasesDropdowns
                            index={index}
                            originPhaseId={rule.originPhaseId}
                            destinationPhaseId={rule.destinationPhaseId}
                            onChangeOriginPhase={handleChangeOriginPhase}
                            onChangeDestinationPhase={handleChangeDestinationPhase}
                        />
                        <Grid item xs={4}>
                            <RealTimeNotificationRecipients
                                name="notificationRecipients"
                                selectedRecipients={rule.recipients}
                                availableRecipients={availableRecipients}
                                onAdded={(recipient) => addRecipient(index, recipient)}
                                onRemoved={(recipient) => removeRecipient(index, recipient)}
                            />
                        </Grid>
                    </Grid>
                    <Grid item container direction="row" xs={2} xl={1}>
                        <Grid item xs={6}>
                            {index === rules.length - 1 && (
                                <ArrowTooltip
                                    content={t(
                                        'settings.notifications.realTime.triggers.PhaseSetback.addPhaseSetbackSettingTooltip'
                                    )}
                                    position="bottom"
                                >
                                    <IconButton size="small" onClick={addRule}>
                                        <PlusIcon />
                                    </IconButton>
                                </ArrowTooltip>
                            )}
                        </Grid>
                        <Grid item xs={6}>
                            {rules.length > 1 && (
                                <ArrowTooltip
                                    content={t(
                                        'settings.notifications.realTime.triggers.PhaseSetback.removePhaseSetbackSettingTooltip'
                                    )}
                                    position="bottom"
                                >
                                    <IconButton size="small" onClick={() => removeRule(index)}>
                                        <RemoveCircleIcon fill={theme.palette.error.main} />
                                    </IconButton>
                                </ArrowTooltip>
                            )}
                        </Grid>
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

type PhasesDropdownsProps = {
    index: number;
    originPhaseId?: number;
    destinationPhaseId?: number;
    onChangeOriginPhase: (
        index: number,
        phaseId: number,
        undefineDestinationPhaseId: boolean
    ) => void;
    onChangeDestinationPhase: (index: number, phaseId: number) => void;
};

const PhasesDropdowns = ({
    index,
    originPhaseId,
    destinationPhaseId,
    onChangeOriginPhase,
    onChangeDestinationPhase,
}: PhasesDropdownsProps) => {
    const { t } = useAppTranslation();
    const phases = useOrderPhases();
    const styles = useRealTimeEditorStyles();

    const originPhasesOptions = useMemo(
        () =>
            phases.map((x) =>
                x.id === destinationPhaseId
                    ? { value: x.id, label: x.name, isDisabled: true }
                    : { value: x.id, label: x.name, isDisabled: false }
            ),
        [phases, destinationPhaseId]
    );

    const destinationPhasesOptions = useMemo(() => {
        const originPhaseIndex = phases.findIndex((x) => x.id === originPhaseId);

        return phases.map((x, i) =>
            originPhaseIndex === -1 || i < originPhaseIndex
                ? { value: x.id, label: x.name, isDisabled: false }
                : { value: x.id, label: x.name, isDisabled: true }
        );
    }, [phases, originPhaseId]);

    const handleChangeOriginPhase = (value: number) => {
        const newOriginPhaseIndex = phases.findIndex((x) => x.id === value);
        const destinationPhaseIndex = phases.findIndex((x) => x.id === destinationPhaseId);

        onChangeOriginPhase(index, value, newOriginPhaseIndex <= destinationPhaseIndex);
    };

    return (
        <>
            <Grid item xs={4}>
                <Dropdown
                    placeholder={t(
                        'settings.notifications.realTime.triggers.PhaseSetback.selectOriginPhase'
                    )}
                    slotProps={{
                        inputWrapper: { slotProps: { label: { className: styles.label } } },
                    }}
                    label={t('settings.notifications.realTime.triggers.PhaseSetback.originPhase')}
                    cmosVariant="grey"
                    onChange={(event) => {
                        if (event) handleChangeOriginPhase(event.value);
                    }}
                    name="selectOriginPhase"
                    options={originPhasesOptions}
                    value={originPhasesOptions.find((x) => x.value === originPhaseId)}
                />
            </Grid>
            <Grid item xs={4}>
                <Dropdown
                    placeholder={t(
                        'settings.notifications.realTime.triggers.PhaseSetback.selectDestinationPhase'
                    )}
                    slotProps={{
                        inputWrapper: { slotProps: { label: { className: styles.label } } },
                    }}
                    label={t(
                        'settings.notifications.realTime.triggers.PhaseSetback.destinationPhase'
                    )}
                    cmosVariant="grey"
                    onChange={(event) => {
                        if (event) onChangeDestinationPhase(index, event.value);
                    }}
                    name="selectDestinationPhase"
                    options={destinationPhasesOptions}
                    value={destinationPhasesOptions.find((x) => x.value === destinationPhaseId)}
                />
            </Grid>
        </>
    );
};

function useOrderPhases() {
    const { data } = useQuery(['phases'], () => WpPhasesApi.getPhases());
    return data ?? [];
}
