import { GridSize } from '@mui/material';
import AlertTriggerOperation from 'datacontracts/Settings/Alerts/AlertTriggerOperation';

export interface OperationProps {
    operation: AlertTriggerOperation;
    onOperation: (op: AlertTriggerOperation) => void;
    label?: string;
    name: string;
    styling?: OperationStyle;
}

export type OperationStyle = {
    size?: GridSize;
    placeholder?: string;
};
