import { Grid } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import RealTimeNotificationsSettingsApi from 'api/settings/RealTimeNotifications';
import { AnswerType } from 'api/surveys/_common';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useMemo, useState } from 'react';
import { OptionsType } from 'react-select';
import useRealTimeEditorStyles from './styles';

export type SurveyQuestionValue = {
    answers: string[];
    answerType: AnswerType | null;
};

export type SurveyOperationValueDto = {
    id: string;
    value: SurveyQuestionValue;
};

export type SurveyOperationProps = {
    triggerParams: string | null;
    onUpdate: (params: string) => void;
};

export const SurveyOperation = ({ triggerParams, onUpdate }: SurveyOperationProps) => {
    const { t } = useAppTranslation();
    const styles = useRealTimeEditorStyles();
    const [selectedQuestions, setSelectedQuestions] = useState<SurveyOperationValueDto[]>([]);

    const { data: allQuestions = [] } = useQuery(
        ['settings', 'notifications', 'survey-questions'],
        async () => {
            const allQuestions = await RealTimeNotificationsSettingsApi.fetchQuestions();
            const values: SurveyOperationValueDto[] = triggerParams
                ? JSON.parse(triggerParams)
                : [];
            setSelectedQuestions(values);
            return allQuestions;
        }
    );

    const allQuestionsOptions = useMemo(() => {
        return allQuestions.map((q, i) => ({
            label: `${t(
                'settings.notifications.realTime.triggers.SurveyWithNegativeRating.question'
            )} ${(++i).toString()}`,
            value: q.id,
        }));
    }, [allQuestions, t]);

    const availableQuestionsOptions = useMemo(() => {
        return allQuestionsOptions.filter((q) => {
            return selectedQuestions.every((v) => v.id !== q.value);
        });
    }, [allQuestionsOptions, selectedQuestions]);

    const canAddQuestion = useMemo(() => {
        return availableQuestionsOptions.length > 0 && selectedQuestions.every((q) => q.id !== '');
    }, [availableQuestionsOptions.length, selectedQuestions]);

    const addQuestion = () => {
        const emptyVal: SurveyOperationValueDto = {
            id: '',
            value: {
                answers: [],
                answerType: null,
            },
        };

        setSelectedQuestions((prevVal) => [...prevVal, emptyVal]);
    };

    const getAnswersComponent = (q: SurveyOperationValueDto) => {
        const handleMultipleChange = (values: Readonly<OptionData<string>[]>) => {
            q.value.answers = values.map((v) => v.value);
            const updatedValue = selectedQuestions.map((sq) => (sq.id === q.id ? q : sq));
            setSelectedQuestions(updatedValue);
            handleUpdate(updatedValue);
        };

        const handleSingleChange = (value: OptionsType<OptionData<string>>) => {
            q.value.answers = [value[value.length - 1].value];
            const updatedValue = selectedQuestions.map((sq) => (sq.id === q.id ? q : sq));
            setSelectedQuestions(updatedValue);
            handleUpdate(updatedValue);
        };

        switch (q.value.answerType) {
            case 'Numeric1To5':
                const options5 = [1, 2, 3, 4, 5].map((v, i) => ({
                    label: v.toString(),
                    value: v.toString(),
                }));
                return (
                    <Dropdown
                        multiple
                        placeholder={t(
                            'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectNegativeRating'
                        )}
                        classes={{ label: styles.label }}
                        label={t(
                            'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectNegativeRating'
                        )}
                        cmosVariant="grey"
                        onChange={handleMultipleChange}
                        name="selectNegativeRating1to5"
                        options={options5}
                        value={options5.filter((o) =>
                            selectedQuestions.some(
                                (v) => q.id === v.id && v.value.answers.includes(o.value)
                            )
                        )}
                    />
                );

            case 'Numeric1To10':
                const options10 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((v, i) => ({
                    label: v.toString(),
                    value: v.toString(),
                }));
                return (
                    <Dropdown
                        multiple={true}
                        placeholder={t(
                            'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectNegativeRating'
                        )}
                        classes={{ label: styles.label }}
                        label={t(
                            'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectNegativeRating'
                        )}
                        cmosVariant="grey"
                        onChange={handleMultipleChange}
                        name="selectNegativeRating1to10"
                        options={options10}
                        value={options10.filter((o) =>
                            selectedQuestions.some(
                                (v) => q.id === v.id && v.value.answers.includes(o.value)
                            )
                        )}
                    />
                );

            case 'YesNo':
                const optionsYesNo = [true, false].map((v, i) => ({
                    label: t(`commonLabels.${v ? 'yes' : 'no'}`),
                    value: v.toString(),
                }));
                return (
                    <Dropdown
                        multiple
                        placeholder={t(
                            'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectNegativeRating'
                        )}
                        classes={{ label: styles.label }}
                        label={t(
                            'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectNegativeRating'
                        )}
                        cmosVariant="grey"
                        onChange={handleSingleChange}
                        name="selectNegativeRatingyesno"
                        options={optionsYesNo}
                        value={optionsYesNo.filter((o) =>
                            selectedQuestions.some(
                                (v) => q.id === v.id && v.value.answers.includes(o.value)
                            )
                        )}
                    />
                );

            default:
                return '';
        }
    };

    const handleQuestionChange = (selectedQuestionId: string | undefined, oldIdValue: string) => {
        const question = allQuestions.find((a) => a.id === selectedQuestionId);

        const newQuestions = selectedQuestions.map((q) =>
            q.id === oldIdValue
                ? { id: question!.id, value: { answers: [], answerType: question!.answerType } }
                : q
        );
        setSelectedQuestions(newQuestions);
        handleUpdate(newQuestions);
    };

    const handleUpdate = useCallback(
        (value: SurveyOperationValueDto[]) => {
            onUpdate(JSON.stringify(value));
        },
        [onUpdate]
    );

    return (
        <Grid container direction="column" alignItems="flex-start">
            {selectedQuestions.map((sq) => {
                return (
                    <Grid item container direction="row" key={sq.id} spacing={6}>
                        <Grid item xs={6}>
                            <Dropdown
                                placeholder={t(
                                    'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectQuestionNumber'
                                )}
                                classes={{ label: styles.label }}
                                label={t(
                                    'settings.notifications.realTime.triggers.SurveyWithNegativeRating.selectQuestionNumber'
                                )}
                                cmosVariant="grey"
                                onChange={(newVal) => handleQuestionChange(newVal?.value, sq.id)}
                                name="selectqestionNumber"
                                options={availableQuestionsOptions}
                                value={allQuestionsOptions.find((o) => o.value === sq.id)}
                            />
                        </Grid>

                        <Grid item xs={6}>
                            {getAnswersComponent(sq)}
                        </Grid>
                    </Grid>
                );
            })}
            {canAddQuestion ? (
                <Button
                    customStyles={{
                        backgroundColor: `${Colors.Neutral2}!important`,
                        /* Neutrales/4 */
                        border: `1px solid ${Colors.Neutral4}`,
                        minWidth: '189px',
                        marginTop: '20px',
                    }}
                    buttonInnercustomStyles={{ width: '100%', justifyContent: 'space-between' }}
                    cmosVariant={'stroke'}
                    iconPosition="right"
                    Icon={PlusIcon}
                    color={Colors.Neutral3}
                    onClick={addQuestion}
                    label={t(
                        'settings.notifications.realTime.triggers.SurveyWithNegativeRating.addQuestion'
                    )}
                />
            ) : null}
        </Grid>
    );
};

export default SurveyOperation;
