import { styled, useTheme } from '@mui/material';
import RealTimeNotificationsSettingsApi, {
    RealTimeNotificationTemplateDto,
} from 'api/settings/RealTimeNotifications';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback } from 'react';

type RestorePopupProps = {
    alertId: number;
    open?: boolean;
    onClose: () => void;
    onRestore: (alert: RealTimeNotificationTemplateDto) => void;
};

export default function RestorePopup({ alertId, open, onClose, onRestore }: RestorePopupProps) {
    const { t } = useAppTranslation();
    const theme = useTheme();

    const onRestoreCallback = useCallback(async () => {
        const response = await RealTimeNotificationsSettingsApi.restore(alertId);
        onRestore(response);
    }, [alertId, onRestore]);

    return (
        <Modal open={open}>
            <RootContainer>
                <HeaderText>{t('settings.notifications.realTime.restorePopupTitle')}</HeaderText>
                <SecondaryText>
                    {t('settings.notifications.realTime.restorePopupText')}
                </SecondaryText>
                <StyledButtonContainer>
                    <StyledButton
                        onClick={onClose}
                        cmosVariant={'filled'}
                        color={theme.palette.neutral[3]}
                        label={t('commonLabels.cancel')}
                    />
                    <StyledButton
                        onClick={onRestoreCallback}
                        cmosVariant={'filled'}
                        color={theme.palette.primary.main}
                        label={t('settings.notifications.realTime.restore')}
                    />
                </StyledButtonContainer>
            </RootContainer>
        </Modal>
    );
}

const RootContainer = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[1],
    borderRadius: 10,
    padding: '40px 34px',
    width: 630,
    height: 200,
}));

const HeaderText = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    margin: '14px 0',
    color: theme.palette.neutral[8],
    fontWeight: 700,
}));

const SecondaryText = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[8],
    fontWeight: 400,
}));

const StyledButtonContainer = styled('div')({
    display: 'flex',
    gap: 7,
    justifyContent: 'right',
    marginTop: 35,
});

const StyledButton = styled(Button)({
    padding: '11px 16px 11px 16px',
    marginLeft: 8,
    marginRight: 8,
    minWidth: 160,
    minHeight: 32,
});
