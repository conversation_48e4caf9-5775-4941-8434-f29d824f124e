import { Box, Grid, IconButton, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { PhaseSetbackRuleDto } from 'api/phaseSetback';
import RealTimeNotificationsSettingsApi, {
    RealTimeNotificationRecipient,
    RealTimeNotificationTemplateDto,
    RealTimeNotificationType,
} from 'api/settings/RealTimeNotifications';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { EditIcon } from 'common/components/Icons/EditIcon';
import InfoTooltip from 'common/components/InfoTooltip';
import { Checkbox } from 'common/components/Inputs';
import Note from 'common/components/Note';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import _ from 'lodash';
import React, { ChangeEvent, useMemo, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import { selectCurrentShopId } from 'store/slices/globalSettingsSlice';
import phaseSetbackSlice from 'store/slices/phaseSetback';
import TextAreaWithParams from '../TextAreaWithParams';
import RealTimeNotificationRecipients from './RealTimeNotificationRecipients';
import RestorePopup from './RestorePopup';
import SubstituteParameters from './SubstituteParameters';
import {
    PhaseSetbackOperation,
    PhaseSetbackRuleDto as PhaseSetbackParamsDto,
} from './phaseSetbackOperation';
import PriorityOperation, { PriorityColor, getPriorityColor } from './priorityOperation';
import useRealTimeEditorStyles from './styles';
import SurveyOperation, { SurveyOperationValueDto } from './surveyOperation';

type RealTimeNotificationEditorProps = {
    initial: RealTimeNotificationTemplateDto;
    onUpdated: (newAlert: RealTimeNotificationTemplateDto) => void;
};

const RealTimeNotificationEditor = React.memo(
    ({ initial, onUpdated }: RealTimeNotificationEditorProps) => {
        const styles = useRealTimeEditorStyles();
        const { t } = useAppTranslation();
        const toasters = useToasters();
        const dispatch = useAppDispatch();
        const repairShopKey = useAppSelector(selectCurrentShopId);

        const [isEditing, setEditing] = useState(false);
        const [notificationState, setNotificationState] =
            useState<RealTimeNotificationTemplateDto>(initial);
        const [stateToRestore, setStateToRestore] =
            useState<RealTimeNotificationTemplateDto>(initial);
        const [isRestorePopupOpen, setRestorePopupOpen] = useState(false);
        const availableRecipients = useRecipients();

        const isBodyInvalid = notificationState.body.length > 175;
        const isInvalid = useMemo(() => {
            return (
                isBodyInvalid ||
                (notificationState.body === stateToRestore.body &&
                    notificationState.title === stateToRestore.title &&
                    notificationState.params === stateToRestore.params &&
                    _.isEqual(notificationState.recipients, stateToRestore.recipients))
            );
        }, [
            isBodyInvalid,
            notificationState.body,
            notificationState.title,
            notificationState.params,
            notificationState.recipients,
            stateToRestore.body,
            stateToRestore.title,
            stateToRestore.params,
            stateToRestore.recipients,
        ]);

        const addRecipient = async (recipient: RealTimeNotificationRecipient) => {
            const notification = {
                ...notificationState,
                recipients: [...notificationState.recipients, recipient],
            };
            setNotificationState(notification);
        };

        const removeRecipient = async (recipient: RealTimeNotificationRecipient) => {
            const notification = {
                ...notificationState,
                recipients: notificationState.recipients.filter((r) => r.id !== recipient.id),
            };
            setNotificationState(notification);
        };

        const onCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
            const isEnabled = e.target.checked;

            toggleMutation.mutate(isEnabled);
        };

        const onEdit = () => {
            if (isEditing) return;

            setEditing(true);
            setStateToRestore(notificationState);
        };

        const toggleMutation = useMutation(
            async (isEnabled: boolean) => {
                await RealTimeNotificationsSettingsApi.toggleEnable(
                    notificationState.id,
                    isEnabled
                );
            },
            {
                onMutate: (isEnabled) => {
                    setNotificationState((s) => ({
                        ...s,
                        isEnabled,
                    }));
                },
                onSuccess: (_, isEnabled) => {
                    onEnableChanged(notificationState, isEnabled);
                    onUpdated(notificationState);
                    toasters.success(
                        isEnabled
                            ? t('settings.notifications.realTime.enabledSuccessfully')
                            : t('settings.notifications.realTime.disabledSuccessfully'),
                        t('toasters.settingUpdated')
                    );
                },
                onError: () => {
                    toasters.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                },
            }
        );

        const onEnableChanged = (
            updatedNotification: RealTimeNotificationTemplateDto,
            isEnabled: boolean
        ) => {
            if (updatedNotification.notificationTypeId === RealTimeNotificationType.PhaseSetback) {
                if (!repairShopKey) throw new Error('Repair shop key is undefined');

                dispatch(
                    phaseSetbackSlice.actions.setEnabled({ repairShopKey, enabled: isEnabled })
                );
            }
        };

        const onParamsChanged = (params: string) => {
            const newNotification = {
                ...notificationState,
                params,
            };

            setNotificationState(newNotification);
        };

        const updateNotification = (changes: Partial<RealTimeNotificationTemplateDto>) => {
            setNotificationState((s) => ({ ...s, ...changes }));
        };

        const onCancel = () => {
            setEditing(false);
            setNotificationState(stateToRestore);
        };

        const onSave = async () => {
            if (
                notificationState.params &&
                notificationState.notificationTypeId ===
                    RealTimeNotificationType.SurveyWithNegativeRating
            ) {
                const values: SurveyOperationValueDto[] = JSON.parse(notificationState.params);
                const filteredValues = values.filter((q) => !!q.id && !!q.value.answers);
                notificationState.params = JSON.stringify(filteredValues);
            }

            if (
                notificationState.params &&
                notificationState.notificationTypeId === RealTimeNotificationType.PhaseSetback
            ) {
                const values: PhaseSetbackParamsDto[] = JSON.parse(notificationState.params);
                const filteredValues = values.filter(
                    (x) => !!x.originPhaseId && !!x.destinationPhaseId
                );
                notificationState.params = JSON.stringify(filteredValues);
            }

            let updatedTemplate: RealTimeNotificationTemplateDto | null = null;

            try {
                updatedTemplate = await RealTimeNotificationsSettingsApi.updateTemplate(
                    notificationState
                );
            } catch (ex) {
                console.error(ex);
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }

            if (!updatedTemplate) return;

            onNotificationStateUpdated(updatedTemplate);

            let recipientsUpdateFailed = false;

            const added = notificationState.recipients.filter(
                (r) => !stateToRestore.recipients.find((rr) => rr.id === r.id)
            );
            for (let i = 0; i < added.length; i++) {
                try {
                    await RealTimeNotificationsSettingsApi.addRecipient(
                        notificationState.id,
                        added[i].id
                    );
                } catch (err) {
                    recipientsUpdateFailed = true;
                    console.error(err);
                }
            }
            const removed = stateToRestore.recipients.filter(
                (r) => !notificationState.recipients.find((rr) => rr.id === r.id)
            );
            for (let i = 0; i < removed.length; i++) {
                try {
                    await RealTimeNotificationsSettingsApi.removeRecipient(
                        notificationState.id,
                        removed[i].id
                    );
                } catch (err) {
                    recipientsUpdateFailed = true;
                    console.error(err);
                }
            }

            if (recipientsUpdateFailed) {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            } else {
                toasters.success(
                    t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated')
                );
            }

            onUpdated(updatedTemplate);
            setEditing(false);
        };

        const onRestore = (newAlert: RealTimeNotificationTemplateDto) => {
            setNotificationState(newAlert);
            onUpdated(newAlert);
            setRestorePopupOpen(false);
        };

        const description = useMemo(() => {
            switch (notificationState.notificationTypeId) {
                case RealTimeNotificationType.NewAppointment:
                    return t('settings.notifications.realTime.appointmentAssignedTooltip');
                case RealTimeNotificationType.NewOrder:
                    return t('settings.notifications.realTime.newOrderTooltip');
                case RealTimeNotificationType.PhaseModified:
                    return t('settings.notifications.realTime.phaseModifiedTooltip');
                case RealTimeNotificationType.OrderAssigned:
                    return t('settings.notifications.realTime.orderAssignedTooltip');
                case RealTimeNotificationType.InspectionItemApproved:
                    return t('settings.notifications.realTime.inspectionItemApprovedTooltip');
                case RealTimeNotificationType.InspectionItemDeclined:
                    return t('settings.notifications.realTime.inspectionItemDeclinedTooltip');
                case RealTimeNotificationType.SurveyWithNegativeRating:
                    return t('settings.notifications.realTime.surveyWithNegativeRatingTooltip');
                case RealTimeNotificationType.NewRedItem:
                    return t('settings.notifications.realTime.newRedItemTooltip');
                case RealTimeNotificationType.PhaseSetback:
                    return t('settings.notifications.realTime.phaseSetbackTooltip');
                case RealTimeNotificationType.PaymentReceived:
                    return t('settings.notifications.realTime.paymentReceivedTooltip');
                case RealTimeNotificationType.JobAssigned:
                    return t('settings.notifications.realTime.jobAssignedTooltip');
                default:
                    return '';
            }
        }, [t, notificationState]);

        const parameters: Record<string, string> = useMemo(() => {
            const params: Record<string, string> = {};
            if (notificationState.notificationTypeId === RealTimeNotificationType.NewRedItem) {
                const priorityColor = getPriorityColor(notificationState.params).value;
                params['color'] = t(
                    `settings.notifications.realTime.triggers.NewRedItem.${
                        priorityColor === PriorityColor.NoColor ? PriorityColor.Red : priorityColor
                    }`
                );
            }
            return params;
        }, [notificationState.params, notificationState.notificationTypeId, t]);

        const onNotificationStateUpdated = (
            updatedNotification: RealTimeNotificationTemplateDto
        ) => {
            if (updatedNotification.notificationTypeId === RealTimeNotificationType.PhaseSetback) {
                if (!repairShopKey) throw new Error('Repair shop key is undefined');

                try {
                    const values: PhaseSetbackRuleDto[] = updatedNotification.params
                        ? JSON.parse(updatedNotification.params)
                        : [];
                    dispatch(
                        phaseSetbackSlice.actions.reset(
                            values.length > 0
                                ? {
                                      repairShopKey,
                                      enabled: updatedNotification.isEnabled,
                                      rules: values,
                                  }
                                : null
                        )
                    );
                } catch (ex) {
                    console.error(
                        `Can not reset Phase setback rules with given params ${updatedNotification.params}`,
                        ex
                    );
                }
            }
        };

        return (
            <StyledRootGrid
                item
                container
                alignItems={isEditing ? 'flex-start' : 'center'}
                editing={isEditing}
            >
                <RestorePopup
                    open={isRestorePopupOpen}
                    alertId={notificationState.id}
                    onClose={() => setRestorePopupOpen(false)}
                    onRestore={onRestore}
                />

                <Grid
                    item
                    xs={4}
                    md={3}
                    style={{ paddingRight: 20 }}
                    className={styles.labelContainer}
                >
                    <NotificationTitleWrapper
                        className={clsx(styles.label, styles.alertLabel, styles.labelWrapper)}
                        isEditing={isEditing}
                    >
                        <SubstituteParameters
                            parameters={parameters}
                            value={notificationState.title}
                        />
                        <InfoTooltipContainer>
                            <InfoTooltip text={description} position={'right'} />
                        </InfoTooltipContainer>
                    </NotificationTitleWrapper>
                </Grid>

                <Grid item xs={1}>
                    <Checkbox
                        className={styles.checkbox}
                        checked={notificationState.isEnabled}
                        onChange={onCheckboxChange}
                    />
                </Grid>

                <Grid item xs={8} md={8}>
                    {isEditing ? (
                        <Box>
                            <TextAreaWithParams
                                labelClassName={styles.textAreaLabel}
                                label={t('settings.notifications.realTime.alertBody')}
                                name="notificationBody"
                                value={notificationState.body}
                                onChange={(e) => updateNotification({ body: e.target.value })}
                                isInvalid={isBodyInvalid}
                            />
                            <div className={styles.noteContainer}>
                                <Note>
                                    {t('settings.notifications.realTime.maxChar', { count: 175 })}
                                </Note>
                            </div>

                            <Grid
                                container
                                alignItems="flex-start"
                                spacing={1}
                                className={styles.optionsContainer}
                            >
                                {notificationState.notificationTypeId ===
                                RealTimeNotificationType.SurveyWithNegativeRating ? (
                                    <Grid item xs={8}>
                                        <SurveyOperation
                                            triggerParams={notificationState.params}
                                            onUpdate={onParamsChanged}
                                        />
                                    </Grid>
                                ) : null}
                                {notificationState.notificationTypeId ===
                                RealTimeNotificationType.NewRedItem ? (
                                    <Grid item xs={4}>
                                        <PriorityOperation
                                            triggerParams={notificationState.params}
                                            onUpdate={onParamsChanged}
                                        />
                                    </Grid>
                                ) : null}
                                {notificationState.notificationTypeId ===
                                RealTimeNotificationType.PhaseSetback ? (
                                    <Grid item xs={12}>
                                        <PhaseSetbackOperation
                                            triggerParams={notificationState.params}
                                            availableRecipients={availableRecipients}
                                            onUpdate={onParamsChanged}
                                        />
                                    </Grid>
                                ) : null}
                                {notificationState.notificationTypeId !==
                                RealTimeNotificationType.PhaseSetback ? (
                                    <Grid item xs={4}>
                                        <RealTimeNotificationRecipients
                                            name="notificationRecipients"
                                            selectedRecipients={notificationState.recipients}
                                            availableRecipients={availableRecipients}
                                            onAdded={addRecipient}
                                            onRemoved={removeRecipient}
                                        />
                                    </Grid>
                                ) : null}
                            </Grid>

                            <Box gap={1.25} display="flex" justifyContent="end" marginTop={3}>
                                <Button
                                    customStyles={{ width: 150 }}
                                    cmosVariant={'stroke'}
                                    color={Colors.Neutral3}
                                    label={t('commonLabels.cancel')}
                                    onClick={onCancel}
                                />

                                <Button
                                    customStyles={{ width: 150 }}
                                    cmosVariant={'stroke'}
                                    color={Colors.CM1}
                                    label={t('settings.notifications.realTime.restore')}
                                    onClick={() => setRestorePopupOpen(true)}
                                />

                                <Button
                                    disabled={isInvalid}
                                    customStyles={{ width: 200 }}
                                    cmosVariant={'filled'}
                                    color={Colors.Success}
                                    label={t('commonLabels.save')}
                                    onClick={onSave}
                                />
                            </Box>
                        </Box>
                    ) : (
                        <Box display="flex">
                            <SubstituteParameters
                                parameters={parameters}
                                className={styles.alertBodyPreview}
                                value={notificationState.body}
                            />
                            <IconButton sx={{ marginLeft: '20px' }} onClick={onEdit} size="small">
                                <EditIcon />
                            </IconButton>
                        </Box>
                    )}
                </Grid>
            </StyledRootGrid>
        );
    }
);

function useRecipients() {
    const { data } = useQuery(
        ['rtn', 'recipients'],
        RealTimeNotificationsSettingsApi.fetchRecipients,
        {
            cacheTime: Infinity,
            staleTime: 500,
        }
    );
    return useMemo(() => data ?? [], [data]);
}

export default RealTimeNotificationEditor;

const StyledRootGrid = styled(Grid, {
    shouldForwardProp: (prop) => prop !== 'editing',
})<{ editing: boolean }>(({ theme, editing }) => ({
    transition: 'margin-top 0.1s',

    ...(editing && {
        position: 'relative',
        margin: '20px 0',
        '&::after': {
            content: '""',
            display: 'block',
            border: `1px solid ${theme.palette.neutral[4]}`,
            position: 'absolute',
            inset: -20,
            borderRadius: 20,
            pointerEvents: 'none',
        },
    }),
}));

const NotificationTitleWrapper = styled('span')<{ isEditing: boolean }>(({ isEditing }) => ({
    marginTop: isEditing ? 5 : 0,
}));

const InfoTooltipContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    marginLeft: '6px',
});
