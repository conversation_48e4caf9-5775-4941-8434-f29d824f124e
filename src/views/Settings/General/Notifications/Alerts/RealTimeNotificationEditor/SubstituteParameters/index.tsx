import { useMemo } from 'react';
import useNotificationsTabStyles from './styles';

interface SubstituteParametersProps {
    parameters: Record<string, string>;
    value: string;
    className?: string;
}

export default function SubstituteParameters({
    parameters,
    value,
    className,
}: SubstituteParametersProps) {
    const styles = useNotificationsTabStyles();

    return useMemo(
        () => (
            <span className={className}>
                {value.split(/(\{.*?\})/gi).map((element, i) => {
                    if (element.startsWith('{') && element.endsWith('}')) {
                        const param = element.substring(1, element.length - 1);
                        if (typeof parameters[param] !== 'undefined') {
                            return (
                                <span key={`${i}v${param}`} className={styles.parameter}>
                                    {parameters[param]}
                                </span>
                            );
                        } else {
                            return (
                                <span key={`${i}nv${param}`} className={styles.parameter}>
                                    {param}
                                </span>
                            );
                        }
                    }

                    return <span key={`${i}no`}>{element}</span>;
                })}
            </span>
        ),
        [parameters, value, styles, className]
    );
}
