import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import useRealTimeEditorStyles from './styles';

export enum PriorityColor {
    Red = 'Red',
    Yellow = 'Yellow',
    RedYellow = 'RedYellow',
    NoColor = '',
}

export type PriorityColorValueDto = {
    value: PriorityColor;
};

type PriorityOperationProps = {
    triggerParams: string | null;
    onUpdate: (params: string) => void;
};

export const PriorityOperation = ({ triggerParams, onUpdate }: PriorityOperationProps) => {
    const { t } = useAppTranslation();
    const styles = useRealTimeEditorStyles();
    const [selectedPriorityColor, setSelectedPriorityColor] = useState<PriorityColorValueDto>(
        getPriorityColor(triggerParams)
    );

    const options = useMemo(
        () =>
            [PriorityColor.Red, PriorityColor.Yellow, PriorityColor.RedYellow].map((o) => ({
                label: t(`settings.notifications.realTime.triggers.NewRedItem.${o}`),
                value: o,
            })),
        [t]
    );

    const onChange = (option: OptionData<PriorityColor> | null) => {
        if (option === null || option.value === selectedPriorityColor.value) return;

        setSelectedPriorityColor({ value: option.value });

        onUpdate(JSON.stringify(option));
    };

    return (
        <Dropdown
            placeholder={t('settings.notifications.realTime.triggers.SelectPriorityColor')}
            name="selectPriorityColor"
            label={t('settings.notifications.realTime.triggers.SelectPriorityColor')}
            slotProps={{ inputWrapper: { slotProps: { label: { className: styles.label } } } }}
            cmosVariant="grey"
            options={options}
            value={options.find((o) => o.value === selectedPriorityColor.value)}
            onChange={onChange}
        />
    );
};

export const getPriorityColor = (triggerParams: string | null) =>
    triggerParams
        ? (JSON.parse(triggerParams) as PriorityColorValueDto)
        : { value: PriorityColor.NoColor };

export default PriorityOperation;
