import { Box, Grid } from '@mui/material';
import { CustomList } from 'common/components/CustomList/CustomList';
import Dropdown, {
    CustomHeaderMenuComponent,
    DropdownImplementationComponents,
} from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import Tag from 'common/components/Tag';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import AlertRecipient from 'datacontracts/Settings/Alerts/AlertRecipient';
import { useMemo } from 'react';
import useRealTimeEditorStyles from './styles';

interface AlertRecipientProps {
    onAdded: (recipient: AlertRecipient) => void;
    onRemoved: (recipient: AlertRecipient) => void;
    selectedRecipients: AlertRecipient[];
    availableRecipients: AlertRecipient[];
    name: string;
}

const CustomHeaderMenu: CustomHeaderMenuComponent<AlertRecipient, true> = ({
    getValue,
    selectProps,
}) => (
    <Box padding={1} display="flex" alignItems="flex-start" flexWrap="wrap" gap={0.5}>
        {getValue().map((item) => (
            <Tag
                ellipsis
                size="small"
                key={item.value.userID}
                onClose={() => selectProps.onRemoved(item.value)}
            >
                {item.label}
            </Tag>
        ))}
    </Box>
);

const CustomMenu: DropdownImplementationComponents<AlertRecipient, true>['MenuList'] = (props) => {
    const { children, selectProps, maxHeight, ...otherProps } = props;
    return (
        <CustomList sx={{ maxHeight }}>
            <CustomHeaderMenu selectProps={selectProps} {...otherProps} />
            {children}
        </CustomList>
    );
};

export default function AlertRecipients({
    availableRecipients,
    selectedRecipients,
    onAdded,
    onRemoved,
    name,
}: AlertRecipientProps) {
    const styles = useRealTimeEditorStyles();
    const recipientsMap = useMemo(() => {
        const recipients: Record<number, AlertRecipient> = {};
        for (const r of availableRecipients) {
            recipients[r.userID] = r;
        }
        return recipients;
    }, [availableRecipients]);
    const { t } = useAppTranslation();
    const options = useMemo(
        () =>
            availableRecipients.map((r) => ({
                value: r,
                label: [r.name, r.jobTitle].filter(Boolean).join(' - '),
            })),
        [availableRecipients]
    );
    const value = useMemo(
        () =>
            selectedRecipients.map((r) => ({
                value: recipientsMap[r.userID] ?? r,
                label: [r.name, r.jobTitle].filter(Boolean).join(' - '),
            })),
        [selectedRecipients, recipientsMap]
    );

    // NOTE: we really need onAdded/onDeleted for Dropdown like in DropdownMulti
    // In this case we're using Dropdown with multiple=true because it's the closest in terms of design
    // but that comes at a cost of this
    const onChange = (selected: Readonly<OptionData<AlertRecipient>[]>) => {
        if (selected.length > value.length) {
            onAdded(selected[selected.length - 1].value);
        } else {
            // find removed item
            let removedIndex = 0;
            for (; removedIndex < selected.length; removedIndex++) {
                if (selected[removedIndex] !== value[removedIndex]) break;
            }
            onRemoved(selectedRecipients[removedIndex]);
        }
    };

    return (
        <Grid item xs={4}>
            <Dropdown
                classes={{ label: styles.label }}
                placeholder={t('settings.notifications.realTime.recipients')}
                CustomMenu={CustomMenu}
                label={t('settings.notifications.realTime.recipients')}
                multiple
                optionStyle={OptionStyle.checkbox}
                cmosVariant="grey"
                name={name}
                value={value}
                options={options}
                onChange={onChange}
                extraProps={{ onRemoved }}
                getOptionValue={(x) => x.value.userID + ''}
            />
        </Grid>
    );
}
