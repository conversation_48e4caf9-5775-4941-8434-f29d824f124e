import { useAppTranslation } from 'common/hooks/useAppTranslation';
import AlertTrigger from 'datacontracts/Settings/Alerts/AlertTrigger';
import AlertTriggerOperation from 'datacontracts/Settings/Alerts/AlertTriggerOperation';
import { findComponent } from './helpers';

interface TriggerFieldsProps {
    trigger: AlertTrigger;
    onTrigger: (triggers: AlertTrigger) => void;
}

export default function TriggerEditor({ trigger, onTrigger }: TriggerFieldsProps) {
    const { t } = useAppTranslation();
    const updateOperation = (newOp: AlertTriggerOperation | AlertTriggerOperation[]) => {
        const operations =
            newOp instanceof Array
                ? newOp
                : trigger.operations.map((o) => (o.name === newOp.name ? newOp : o));
        onTrigger({
            ...trigger,
            operations,
        });
    };
    const label = t(`settings.notifications.realTime.triggers.${trigger.name}`, {
        defaultValue: trigger.name,
    });

    const [FirstOp, s1] = findComponent(trigger.operations[0], t);
    const [SecondOp, s2] = trigger.operations[1]
        ? findComponent(trigger.operations[1], t)
        : [null, null];

    if (!FirstOp)
        throw new Error(
            `First operation's type (${trigger.operations[0].type}) does not have associated component`
        );

    return (
        <>
            <FirstOp
                label={label}
                styling={s1}
                name={`trigger-${trigger.name}_op-${trigger.operations[0].name}`}
                operation={trigger.operations[0]}
                onOperation={updateOperation}
            />
            {SecondOp && (
                <SecondOp
                    styling={s2}
                    name={`trigger-${trigger.name}_op-${trigger.operations[1].name}`}
                    operation={trigger.operations[1]}
                    onOperation={updateOperation}
                />
            )}
        </>
    );
}
