import { Grid } from '@mui/material';

import { NumberFormField } from 'common/components/Inputs/NumberField';
import { OperationProps } from './props';

export default function PercentageOperation({
    label,
    name,
    operation,
    onOperation,
    styling,
}: OperationProps) {
    const isValid = +operation.value >= 0 && +operation.value <= 100;

    return (
        <Grid item xs={styling?.size ?? 1}>
            <NumberFormField
                label={label}
                name={name}
                template="{0}%"
                isInvalid={!isValid}
                value={+operation.value}
                onValueChange={(v) => onOperation({ ...operation, value: v.floatValue + '' })}
            />
        </Grid>
    );
}
