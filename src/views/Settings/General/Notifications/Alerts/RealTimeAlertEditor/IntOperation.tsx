import { Grid } from '@mui/material';
import { NumberFormField } from 'common/components/Inputs/NumberField';
import { OperationProps } from './props';
import useRealTimeEditorStyles from './styles';

export default function IntOperation({
    label,
    name,
    operation,
    onOperation,
    styling,
}: OperationProps) {
    const styles = useRealTimeEditorStyles();

    return (
        <Grid item xs={styling?.size ?? 2}>
            <NumberFormField
                classes={{ label: styles.label }}
                label={label}
                name={name}
                value={+operation.value}
                onValueChange={(v) => onOperation({ ...operation, value: v.floatValue + '' })}
            />
        </Grid>
    );
}
