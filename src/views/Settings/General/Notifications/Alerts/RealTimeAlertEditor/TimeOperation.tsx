import { Grid } from '@mui/material';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';

import { NumberFormField } from 'common/components/Inputs/NumberField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo, useState } from 'react';
import { NumberFormatValues } from 'react-number-format';
import { OperationProps } from './props';
import useRealTimeEditorStyles from './styles';

const TIME_POINTS = [15, 30, 45, 60, 120, 180, 300, 600, 1440];

export default function TimeOperation({
    label,
    name,
    operation,
    onOperation,
    styling,
}: OperationProps) {
    const { t } = useAppTranslation();
    const [isCustom, setCustom] = useState(!TIME_POINTS.includes(+operation.value));
    const options = useMemo<OptionData<string>[]>(
        () => [
            ...TIME_POINTS.map((p) => ({
                value: p + '',
                label:
                    p >= 60
                        ? t('settings.notifications.realTime.hour', {
                              count: Math.floor(p / 60),
                          })
                        : t('settings.notifications.realTime.minute', { count: p }),
            })),
            {
                value: 'custom',
                label: t('settings.notifications.realTime.customTime'),
            },
        ],
        [t]
    );
    const styles = useRealTimeEditorStyles();

    const hours = Math.floor(+operation.value / 60);
    const minutes = +operation.value - hours * 60;

    const onTimeChange = (v: number) => {
        onOperation({
            ...operation,
            value: v + '',
        });
    };

    const onHoursChange = ({ floatValue }: NumberFormatValues) => {
        onTimeChange((floatValue ?? 0) * 60 + minutes);
    };

    const onMinutesChange = ({ floatValue }: NumberFormatValues) => {
        const m = Math.min(floatValue ?? 0, 59);
        onTimeChange(hours * 60 + m);
    };

    const onChange = (o: OptionData<string> | null) => {
        if (o === null) return;
        const newIsCustom = o.value === 'custom';
        if (newIsCustom !== isCustom) setCustom(newIsCustom);
        if (!newIsCustom) {
            onOperation({
                ...operation,
                value: o.value,
            });
        }
    };

    return (
        <>
            <Grid item xs={styling?.size ?? 4}>
                <Dropdown
                    classes={{ label: styles.label }}
                    label={label}
                    name={name}
                    onChange={onChange}
                    value={
                        isCustom
                            ? options[options.length - 1]
                            : options.find((o) => o.value === operation.value) ??
                              options[options.length - 1]
                    }
                    cmosVariant="grey"
                    options={options}
                />
            </Grid>
            {isCustom && (
                <>
                    <Grid item xs={2}>
                        <NumberFormField
                            classes={{ label: styles.label }}
                            value={hours}
                            name={`${name}_customHours`}
                            label={t('settings.notifications.realTime.hours')}
                            cmosVariant="grey"
                            onValueChange={onHoursChange}
                        />
                    </Grid>
                    <Grid item xs={2}>
                        <NumberFormField
                            classes={{ label: styles.label }}
                            value={minutes}
                            name={`${name}_customMinutes`}
                            label={t('settings.notifications.realTime.minutes')}
                            cmosVariant="grey"
                            onValueChange={onMinutesChange}
                        />
                    </Grid>
                </>
            )}
        </>
    );
}
