import { makeStyles } from '@mui/styles';
import { FontPrimary, FontSecondary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const useRealTimeEditorStyles = makeStyles((theme) => ({
    labelContainer: {
        paddingRight: 20,
        maxWidth: 240,
        boxSizing: 'border-box',
    },
    label: {
        ...theme.typography.h6Inter,
        color: theme.palette.neutral[7],
        fontWeight: 'initial',
        whiteSpace: 'nowrap',
    },
    alertLabel: {
        whiteSpace: 'initial',
        fontWeight: 700,
    },
    textAreaLabel: {
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
    },
    alertBodyPreview: {
        ...FontSecondary(HeaderStyles.H5_14px, false, theme.palette.neutral[8]),
        alignSelf: 'center',
        whiteSpace: 'pre-wrap',
    },
    operationsList: {
        display: 'flex',
        gap: '10px',
    },
    noteContainer: {
        paddingTop: 10,
    },
    optionsContainer: {
        marginTop: 10,
    },
    triggerEditor: {
        display: 'flex',
    },
    checkbox: {
        padding: 1,
    },
}));

export default useRealTimeEditorStyles;
