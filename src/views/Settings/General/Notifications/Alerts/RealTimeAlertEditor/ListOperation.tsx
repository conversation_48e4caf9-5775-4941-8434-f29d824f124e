import { Grid, ListItemButton, styled, useTheme } from '@mui/material';
import TriggerOperationValuesAPI from 'api/settings/TriggerOperationValues';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import ArrowTooltip from 'common/components/Tooltip';
import { useApiCall } from 'common/hooks';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { MenuListComponentProps, OptionsType } from 'react-select';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import { OperationProps } from './props';

const LIST_ITEM_SIZE = 32;

export default function ListOperation({
    label,
    name,
    operation,
    onOperation,
    styling,
}: OperationProps) {
    const [options, setOptions] = useState<OptionData<string>[]>([]);
    const { callApi } = useApiCall();
    const theme = useTheme();

    const onChange = useCallback(
        (option: OptionData<string> | null) => {
            if (option === null) return;
            onOperation({
                ...operation,
                value: option.value,
                displayValue: option.label,
            });
        },
        [onOperation, operation]
    );

    useEffect(() => {
        callApi(() => TriggerOperationValuesAPI.get(operation.name)).then((values) =>
            setOptions(values.map((v) => ({ label: v.displayValue, value: v.value })))
        );
    }, [operation, callApi]);

    return (
        <Grid item xs={styling?.size ?? 3}>
            <Dropdown
                CustomMenu={VirtualizedCustomMenu}
                placeholder={styling?.placeholder}
                slotProps={{
                    inputWrapper: {
                        slotProps: {
                            label: {
                                sx: {
                                    ...theme.typography.h6Inter,
                                    color: theme.palette.neutral[7],
                                    fontWeight: 'initial',
                                    whiteSpace: 'nowrap',
                                },
                            },
                        },
                    },
                }}
                label={label ?? ''}
                cmosVariant="grey"
                onChange={onChange}
                name={name}
                options={options}
                value={options.find((o) => o.value === operation.value)}
            />
        </Grid>
    );
}

const VirtualizedCustomMenu = ({
    options,
    selectOption,
}: MenuListComponentProps<OptionData<string>, false>) => {
    const height = Math.max(50, Math.min(options.length, 10) * LIST_ITEM_SIZE);

    return (
        <StyledListContainer>
            <FixedSizeList
                itemSize={LIST_ITEM_SIZE}
                width="100%"
                height={height}
                itemCount={options.length}
                overscanCount={15}
                itemData={{ options, selectOption }}
                outerElementType={StyledOuterElement}
            >
                {OperationValuesVirtualizedRenderer}
            </FixedSizeList>
        </StyledListContainer>
    );
};

const OperationValuesVirtualizedRenderer = ({
    index,
    data,
    style,
}: ListChildComponentProps<{
    options: OptionsType<OptionData<string>>;
    selectOption: (option: OptionData<string>) => void;
}>) => {
    const { options, selectOption } = data;
    const label = options[index].label;

    const spanRef = useRef<HTMLSpanElement>(null);
    const [isTruncated, setIsTruncated] = useState(false);

    useLayoutEffect(() => {
        const el = spanRef.current;
        if (!el) return;

        const truncated = el.scrollHeight > el.offsetHeight;
        setIsTruncated(truncated);
    }, [label]);

    const content = (
        <span
            ref={spanRef}
            style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
            }}
        >
            {label}
        </span>
    );

    return (
        <div style={style}>
            <ListItemButton
                sx={{ height: LIST_ITEM_SIZE }}
                role="button"
                component="li"
                onClick={() => selectOption(options[index])}
            >
                {isTruncated ? (
                    <ArrowTooltip content={label} position="left">
                        {content}
                    </ArrowTooltip>
                ) : (
                    content
                )}
            </ListItemButton>
        </div>
    );
};

const StyledListContainer = styled('div')({
    padding: '10px 0',
});

const StyledOuterElement = styled('div')({
    ...scrollbarStyle(),
});
