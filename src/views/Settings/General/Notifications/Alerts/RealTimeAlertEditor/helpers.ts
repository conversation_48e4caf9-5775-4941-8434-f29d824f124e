import AlertTriggerOperation from 'datacontracts/Settings/Alerts/AlertTriggerOperation';
import OperationType from 'datacontracts/Settings/Alerts/OperationType';
import { TFunction } from 'i18next';
import { ComponentType } from 'react';
import IntOperation from './IntOperation';
import ListOperation from './ListOperation';
import PercentageOperation from './PercentageOperation';
import TimeOperation from './TimeOperation';
import { OperationProps, OperationStyle } from './props';

export function findComponent(
    op: AlertTriggerOperation,
    t: TFunction
): [ComponentType<OperationProps>, OperationStyle] {
    const component = COMPONENTS_MAPPING[op.type];
    const styles: OperationStyle = {};

    switch (op.type) {
        case OperationType.Int:
            if (op.name === 'ItemsCount') {
                styles.size = 1;
            }
            break;
        case OperationType.List:
            if (op.name === 'Comparison') {
                styles.size = 2;
            }
            break;
        case OperationType.Item:
            styles.placeholder = t('settings.notifications.realTime.triggers.SelectItem');
            break;
    }
    return [component, styles];
}

const COMPONENTS_MAPPING: Record<string, ComponentType<OperationProps>> = {
    [OperationType.Int]: IntOperation,
    [OperationType.Percentage]: PercentageOperation,
    [OperationType.List]: ListOperation,
    [OperationType.Item]: ListOperation,
    [OperationType.Time]: TimeOperation,
};
