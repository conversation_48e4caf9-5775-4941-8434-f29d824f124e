import { Box, Grid, IconButton, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import AlertsSettingsAPI from 'api/settings/Alerts';
import clsx from 'clsx';
import { Button } from 'common/components/Button';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { Checkbox } from 'common/components/Inputs';
import Note from 'common/components/Note';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { AlertDto, UpdateAlertRequest } from 'datacontracts/Settings/Alerts';
import AlertRecipient from 'datacontracts/Settings/Alerts/AlertRecipient';
import AlertTrigger from 'datacontracts/Settings/Alerts/AlertTrigger';
import OperationType from 'datacontracts/Settings/Alerts/OperationType';
import { TFunction } from 'i18next';
import _, { isNaN } from 'lodash';
import { ChangeEvent, useMemo, useState } from 'react';
import TextAreaWithParams from '../TextAreaWithParams';
import AlertRecipients from './AlertRecipients';
import RestorePopup from './RestorePopup';
import SubstituteParameters from './SubstituteParameters';
import TriggerEditor from './TriggerEditor';
import useRealTimeEditorStyles from './styles';

type RealTimeAlertEditorProps = {
    initialAlert: AlertDto;
    onAlertUpdated: (newAlert: AlertDto) => void;
};

export default function RealTimeAlertEditor({
    initialAlert,
    onAlertUpdated,
}: RealTimeAlertEditorProps) {
    const toasters = useToasters();
    const [isEditing, setEditing] = useState(false);
    const [alertState, setAlertState] = useState<AlertDto>(initialAlert);
    const [stateToRestore, setStateToRestore] = useState<AlertDto>(initialAlert);
    const [isRestorePopupOpen, setRestorePopupOpen] = useState(false);
    const styles = useRealTimeEditorStyles();
    const { t } = useAppTranslation();
    const availableRecipients = useRecipients();

    const onStartEditing = () => {
        setEditing(true);
        setStateToRestore(alertState);
    };

    const addRecipient = async (recipient: AlertRecipient) => {
        const newAlert = {
            ...alertState,
            recipients: [...alertState.recipients, recipient],
        };
        setAlertState(newAlert);
    };

    const removeRecipient = async (recipient: AlertRecipient) => {
        const newAlert = {
            ...alertState,
            recipients: alertState.recipients.filter((r) => r.userID !== recipient.userID),
        };
        setAlertState(newAlert);
    };

    const toggleMutation = useMutation(
        async (enabled: boolean) =>
            await AlertsSettingsAPI.setEnabled({ id: alertState.id, enabled }),
        {
            onMutate: (enabled) => {
                setAlertState((alert) => ({ ...alert, enabled }));
            },
            onSuccess: (_, enabled) => {
                toasters.success(
                    enabled
                        ? t('settings.notifications.realTime.enabledSuccessfully')
                        : t('settings.notifications.realTime.disabledSuccessfully'),
                    t('toasters.settingUpdated')
                );
                onAlertUpdated(alertState);
            },
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const onCheckboxChange = async (e: ChangeEvent<HTMLInputElement>) => {
        const enabled = e.target.checked;

        await toggleMutation.mutateAsync(enabled);
    };

    const onSave = async () => {
        try {
            await AlertsSettingsAPI.update(convertAlertDtoToUpdateAlertRequest(alertState));
        } catch (err) {
            console.error(err);
            toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            return;
        }

        const added = alertState.recipients.filter(
            (r) => !stateToRestore.recipients.find((rr) => rr.userID === r.userID)
        );
        let failedRecipientsUpdate = false;
        for (let i = 0; i < added.length; i++) {
            try {
                await AlertsSettingsAPI.addRecipient(alertState.id, { userId: added[i].userID });
            } catch (err) {
                failedRecipientsUpdate = true;
                console.error(err);
            }
        }

        const removed = stateToRestore.recipients.filter(
            (r) => !alertState.recipients.find((rr) => rr.userID === r.userID)
        );
        for (let i = 0; i < removed.length; i++) {
            try {
                await AlertsSettingsAPI.removeRecipient(alertState.id, removed[i].userID);
            } catch (err) {
                failedRecipientsUpdate = true;
                console.error(err);
            }
        }

        if (failedRecipientsUpdate) {
            toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
        } else {
            toasters.success(
                t('toasters.settingSuccessfullyUpdated'),
                t('toasters.settingUpdated')
            );
        }

        onAlertUpdated(alertState);
        setEditing(false);
    };

    const onCancel = () => {
        setEditing(false);
        setAlertState(stateToRestore);
    };

    const onRestore = (newAlert: AlertDto) => {
        setAlertState(newAlert);
        onAlertUpdated(newAlert);
        setRestorePopupOpen(false);
    };

    const updateAlert = (changes: Partial<UpdateAlertRequest>) => {
        setAlertState((s) => ({ ...s, ...changes }));
    };

    const updateTrigger = (newTrigger: AlertTrigger) => {
        updateAlert({
            triggers: alertState.triggers.map((t) => (t.id === newTrigger.id ? newTrigger : t)),
        });
    };

    const parameters = useMemo(
        () => (isEditing ? {} : getParameters(t, alertState)),
        [alertState, isEditing, t]
    );

    const isBodyInvalid = alertState.body.length > 175;
    const isInvalid = useMemo(() => {
        return (
            isBodyInvalid ||
            (alertState.body === stateToRestore.body &&
                alertState.title === stateToRestore.title &&
                _.isEqual(alertState.triggers, stateToRestore.triggers) &&
                _.isEqual(alertState.recipients, stateToRestore.recipients))
        );
    }, [
        isBodyInvalid,
        alertState.body,
        alertState.title,
        alertState.triggers,
        alertState.recipients,
        stateToRestore.body,
        stateToRestore.title,
        stateToRestore.triggers,
        stateToRestore.recipients,
    ]);

    return (
        <StyledRootGrid editing={isEditing} item container alignItems="flex-start">
            <RestorePopup
                open={isRestorePopupOpen}
                alertId={alertState.id}
                onClose={() => setRestorePopupOpen(false)}
                onRestore={onRestore}
            />

            <Grid item xs={4} md={3} style={{ paddingRight: 20 }} className={styles.labelContainer}>
                {isEditing ? (
                    <>
                        <TextAreaWithParams
                            labelClassName={styles.textAreaLabel}
                            label={t('settings.notifications.realTime.alertTitle')}
                            onChange={(e) => updateAlert({ title: e.target.value })}
                            name={`alert${alertState.id}-title`}
                            value={alertState.title}
                        />
                        <div className={styles.noteContainer}>
                            <Note>
                                {t('settings.notifications.realTime.maxChar', { count: 75 })}
                            </Note>
                        </div>
                    </>
                ) : (
                    <span className={clsx(styles.label, styles.alertLabel)}>
                        <SubstituteParameters parameters={parameters} value={alertState.title} />
                    </span>
                )}
            </Grid>

            <Grid item xs={1}>
                <Checkbox
                    className={styles.checkbox}
                    checked={alertState.enabled}
                    onChange={onCheckboxChange}
                />
            </Grid>

            <Grid item xs={7} md={8}>
                {isEditing ? (
                    <Box>
                        <TextAreaWithParams
                            labelClassName={styles.textAreaLabel}
                            label={t('settings.notifications.realTime.alertBody')}
                            name="alertBody"
                            value={alertState.body}
                            onChange={(e) => updateAlert({ body: e.target.value })}
                            isInvalid={isBodyInvalid}
                        />
                        <div className={styles.noteContainer}>
                            <Note>
                                {t('settings.notifications.realTime.maxChar', { count: 175 })}
                            </Note>
                        </div>

                        <Grid
                            container
                            alignItems="flex-end"
                            spacing={1}
                            className={styles.optionsContainer}
                        >
                            {alertState.triggers.map((t) => (
                                <TriggerEditor key={t.id} trigger={t} onTrigger={updateTrigger} />
                            ))}
                            <AlertRecipients
                                name="alertRecipients"
                                selectedRecipients={alertState.recipients}
                                availableRecipients={availableRecipients}
                                onAdded={addRecipient}
                                onRemoved={removeRecipient}
                            />
                        </Grid>

                        <Box gap={1.25} display="flex" justifyContent="end" marginTop={3}>
                            <Button
                                customStyles={{ width: 150 }}
                                cmosVariant={'stroke'}
                                color={Colors.Neutral3}
                                label={t('commonLabels.cancel')}
                                onClick={onCancel}
                            />

                            <Button
                                customStyles={{ width: 150 }}
                                cmosVariant={'stroke'}
                                color={Colors.CM1}
                                label={t('settings.notifications.realTime.restore')}
                                onClick={() => setRestorePopupOpen(true)}
                            />

                            <Button
                                disabled={isInvalid}
                                customStyles={{ width: 200 }}
                                cmosVariant={'filled'}
                                color={Colors.Success}
                                label={t('commonLabels.save')}
                                onClick={onSave}
                            />
                        </Box>
                    </Box>
                ) : (
                    <Box display="flex">
                        <SubstituteParameters
                            className={styles.alertBodyPreview}
                            parameters={parameters}
                            value={alertState.body}
                        />
                        <IconButton
                            sx={{ marginLeft: '20px' }}
                            onClick={onStartEditing}
                            size="small"
                        >
                            <EditIcon />
                        </IconButton>
                    </Box>
                )}
            </Grid>
        </StyledRootGrid>
    );
}

function useRecipients() {
    const { data } = useQuery(
        ['alerts', 'recipients'],
        AlertsSettingsAPI.getAvailableRecipientOptions,
        {
            cacheTime: Infinity,
            staleTime: 500,
        }
    );
    return useMemo(() => data ?? [], [data]);
}

function getParameters(t: TFunction, alert: AlertDto): Record<string, string> {
    const params: Record<string, string> = {};
    for (const trg of alert.triggers) {
        for (const op of trg.operations) {
            // this assumes placeholder looks like this: {something here}
            if (!op.placeholder) continue;
            let value = op.displayValue || op.value;

            switch (op.type) {
                case OperationType.Time:
                    const m = +op.value;
                    value =
                        m >= 60
                            ? t('settings.notifications.realTime.hour', {
                                  count: Math.floor(m / 60),
                              })
                            : t('settings.notifications.realTime.minute', { count: m });
                    break;

                case OperationType.Int:
                    const numberVal = Number(value);
                    value = isNaN(numberVal) ? value : numberVal.toLocaleString();
                    break;
                case OperationType.Item:
                    if (!value) {
                        value = 'Item'; //just display as a placeholder
                    }
                    break;
            }

            params[op.placeholder.substring(1, op.placeholder.length - 1)] = value;
        }
    }
    return params;
}

function convertAlertDtoToUpdateAlertRequest({ recipients: _, ...update }: AlertDto) {
    return update;
}

const StyledRootGrid = styled(Grid, {
    shouldForwardProp: (prop) => prop !== 'editing',
})<{ editing: boolean }>(({ theme, editing }) => ({
    transition: 'margin-top 0.1s',

    ...(editing && {
        position: 'relative',
        margin: '20px 0',
        '&::after': {
            content: '""',
            display: 'block',
            border: `1px solid ${theme.palette.neutral[4]}`,
            position: 'absolute',
            inset: -20,
            borderRadius: 20,
            pointerEvents: 'none',
        },
    }),
}));
