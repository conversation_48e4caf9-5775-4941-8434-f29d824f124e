import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

const useRestoreEmailPopupStyles = makeStyles((theme) => ({
    header: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[8]),
        marginTop: 0,
    },
    text: {
        whiteSpace: 'pre-line',
    },
    button: {
        width: 160,
    },
}));

export default useRestoreEmailPopupStyles;
