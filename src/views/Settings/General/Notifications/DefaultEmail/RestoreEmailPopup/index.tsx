import { Box } from '@mui/material';
import DefaultEmailAPI, { DefaultEmailDto } from 'api/settings/DefaultEmail';
import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import useRestoreEmailPopupStyles from './styles';

export type RestoreEmailPopupProps = {
    onNewDefaultEmail: (text: DefaultEmailDto) => void;
    onClose: () => void;
};

export default function RestoreEmailPopup({ onClose, onNewDefaultEmail }: RestoreEmailPopupProps) {
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const styles = useRestoreEmailPopupStyles();
    const dispatch = useAppDispatch();

    const restore = async () => {
        const defaultEmail = await callApi(() => DefaultEmailAPI.restore());
        onNewDefaultEmail(defaultEmail);
        onClose();
        dispatch(
            setNewToaster(
                new NotificationData(
                    t('toasters.configurationChangedSuccessfully'),
                    t('toasters.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    return (
        <Modal open>
            <Box padding={4} width={700}>
                <h3 className={styles.header}>
                    {t('settings.notifications.defaultEmail.restore.title')}
                </h3>
                <p className={styles.text}>
                    {t('settings.notifications.defaultEmail.restore.text')}
                </p>

                <Box display="flex" justifyContent="end" gap={2} marginTop={4}>
                    <Button
                        className={styles.button}
                        onClick={onClose}
                        cmosVariant={'filled'}
                        color={Colors.Neutral3}
                        label={t('commonLabels.cancel')}
                    />

                    <Button
                        className={styles.button}
                        onClick={restore}
                        cmosVariant={'filled'}
                        color={Colors.CM1}
                        label={t('settings.notifications.defaultEmail.restore.restore')}
                    />
                </Box>
            </Box>
        </Modal>
    );
}
