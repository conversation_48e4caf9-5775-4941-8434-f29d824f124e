import { Box, IconButton, styled } from '@mui/material';
import DefaultEmailAPI, {
    DefaultEmailDto,
    DefaultEmailPreviewDto,
} from 'api/settings/DefaultEmail';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { MailIcon } from 'common/components/Icons/MailIcon';
import RichTextEditor from 'common/components/Inputs/RichTextEditor';
import { HtmlChangeSource } from 'common/components/Inputs/RichTextEditor/RichTextEditor';
import { Modal } from 'common/components/Modal';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { SettingLayoutControl, SettingsSection } from 'views/Settings/common';
import RestoreEmailPopup from './RestoreEmailPopup';

type DefaultEmailProps = {
    onLoaded: () => void;
};

export default function DefaultEmail({ onLoaded }: DefaultEmailProps) {
    const { t } = useAppTranslation();
    const [isEmailPopupOpen, setEmailPopupOpen] = useState(false);
    const [defaultEmail, setDefaultEmail] = useState<DefaultEmailDto>();
    const [html, setHtml] = useState('');
    const [preview, setPreview] = useState<DefaultEmailPreviewDto>();
    const { callApi } = useApiCall();
    const [iframe, setIframe] = useState<HTMLIFrameElement | null>(null);
    const dispatch = useAppDispatch();
    const [isRestorePopupOpen, setRestorePopupOpen] = useState(false);

    useEffect(() => {
        setHtml(defaultEmail?.body ?? '');
    }, [defaultEmail]);

    useEffect(() => {
        (async () => {
            const result = await callApi(() => DefaultEmailAPI.get());
            setDefaultEmail(result);
            onLoaded();
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [callApi]);

    useEffect(() => {
        if (iframe) {
            const doc = iframe.contentWindow?.document;
            if (doc) {
                doc.open();
                doc.write(preview?.body ?? '');
                doc.close();
                iframe.style.height = `${doc.documentElement.scrollHeight}px`;
            }
        }

        return () => {};
    }, [iframe, preview]);

    const fetchPreview = async () => {
        setPreview(await callApi(() => DefaultEmailAPI.getPreviewWithCustomContent(html)));
    };

    const showSuccessfulToaster = () =>
        dispatch(
            setNewToaster(
                new NotificationData(
                    t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated'),
                    NotificationType.success
                )
            )
        );

    const saveEmail = async () => {
        setDefaultEmail(
            await callApi(() =>
                DefaultEmailAPI.save({
                    ...defaultEmail,
                    body: html,
                })
            )
        );
        showSuccessfulToaster();
    };

    const onHtmlChange = (html: string, source: HtmlChangeSource) => {
        setHtml(html);
        if (source === 'sourceChanged') {
            // NOTE (MB) If source code was changed, save immidiately (CMOS-842)
            saveEmail();
        }
    };

    return (
        <>
            <SettingsSection label={t('settings.notifications.defaultEmail.header')}>
                <SettingLayoutControl label={t('settings.notifications.defaultEmail.preview')}>
                    <IconButton
                        onClick={() => {
                            setEmailPopupOpen(true);
                            fetchPreview();
                        }}
                        size="large"
                    >
                        <MailIcon />
                    </IconButton>
                </SettingLayoutControl>
                <SettingLayoutControl label={t('settings.notifications.defaultEmail.text')}>
                    <RichTextEditor html={html} onHtmlChange={onHtmlChange} />
                    <Box display="flex" justifyContent="end" marginTop={2} gap={0.75}>
                        <StyledButton
                            color={Colors.CM1}
                            label={t('settings.notifications.defaultEmail.restore.restore')}
                            cmosVariant={'stroke'}
                            onClick={() => setRestorePopupOpen(true)}
                        />

                        <StyledButton
                            color={Colors.Success}
                            label={t('commonLabels.save')}
                            cmosVariant={'filled'}
                            onClick={saveEmail}
                        />
                    </Box>
                </SettingLayoutControl>
            </SettingsSection>
            {isRestorePopupOpen && (
                <RestoreEmailPopup
                    onNewDefaultEmail={setDefaultEmail}
                    onClose={() => setRestorePopupOpen(false)}
                />
            )}
            {isEmailPopupOpen && (
                <Modal open>
                    <DivRoot>
                        <Box display="flex" alignItems="center" justifyContent="space-between">
                            <SpanHeader>
                                {t('settings.notifications.defaultEmail.previewHeader')}
                            </SpanHeader>
                            <Box>
                                <IconButton onClick={() => setEmailPopupOpen(false)} size="large">
                                    <CloseIcon />
                                </IconButton>
                            </Box>
                        </Box>
                        <SpanSubject>
                            <b>{t('settings.notifications.defaultEmail.subject')}:</b>{' '}
                            {preview?.subject}
                        </SpanSubject>
                        <DivContentWrapper>
                            <iframe
                                title={t('settings.notifications.defaultEmail.previewHeader')}
                                src="about:blank"
                                ref={setIframe}
                            />
                        </DivContentWrapper>
                    </DivRoot>
                </Modal>
            )}
        </>
    );
}

const DivContentWrapper = styled('div')(({ theme }) => ({
    borderRadius: 10,
    border: `1px solid ${theme.palette.neutral[5]}`,
    marginTop: 20,
    height: 400,
    overflowY: 'scroll',
    ...scrollbarStyle(),

    '& > iframe': {
        width: '100%',
        border: 0,
    },
}));

const SpanHeader = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[8],
    margin: '5px 0',
}));

const SpanSubject = styled('span')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[8],
}));

const DivRoot = styled('div')({
    maxWidth: 830,
    padding: 20,
});

const StyledButton = styled(Button)({
    width: 160,
});
