import { Box } from '@mui/material';
import RecipientsAPI, { RecipientsDto } from 'api/settings/Recipients';
import InfoTooltip from 'common/components/InfoTooltip';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { SettingsSection, TextSettingControl } from 'views/Settings/common';
import { getDefaultRecipients } from './helpers';

type RecipientsSectionProps = {
    onLoaded: () => void;
};

export default function RecipientsSection({ onLoaded }: RecipientsSectionProps) {
    const { t } = useAppTranslation();

    const [settings, setSettings] = useState<RecipientsDto>(getDefaultRecipients);
    const { callApi: callApiSave } = useApiCall('ContinueOld');
    const { callApi: callApiGet } = useApiCall();

    const dispatch = useAppDispatch();

    useEffect(() => {
        callApiGet(() => RecipientsAPI.get(), {
            selectErrorContent: () => ({
                body: t('toasters.errorOccurredWhenLoading'),
            }),
        }).then((settings) => {
            setSettings(settings);
            onLoaded();
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const save = async (changes: Partial<RecipientsDto>) => {
        const newSettings = { ...settings, ...changes };
        setSettings(newSettings);
        await callApiSave(() => RecipientsAPI.save(newSettings), {
            selectErrorContent: () => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });
        dispatch(
            setNewToaster(
                new NotificationData(
                    t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated'),
                    NotificationType.success
                )
            )
        );
    };

    return (
        <SettingsSection label={t('settings.notifications.recipients.header')}>
            <TextSettingControl
                name="emailSms"
                alignItems="center"
                label={
                    <Box display="flex">
                        {t('settings.notifications.recipients.emailSms')}
                        <InfoTooltip
                            text={t('settings.notifications.recipients.emailSmsTooltip')}
                        />
                    </Box>
                }
                placeholder={t('settings.notifications.recipients.emailPlaceholder')}
                value={settings.sentEmailAndSmsNotifications}
                onSave={(v) => save({ sentEmailAndSmsNotifications: v })}
            />
            <TextSettingControl
                name="upload"
                alignItems="center"
                label={
                    <Box display="flex">
                        {t('settings.notifications.recipients.upload')}
                        <InfoTooltip text={t('settings.notifications.recipients.uploadTooltip')} />
                    </Box>
                }
                placeholder={t('settings.notifications.recipients.emailPlaceholder')}
                value={settings.uploadNotifications}
                onSave={(v) => save({ uploadNotifications: v })}
            />
            <TextSettingControl
                name="support"
                alignItems="center"
                label={
                    <Box display="flex">
                        {t('settings.notifications.recipients.support')}
                        <InfoTooltip text={t('settings.notifications.recipients.supportTooltip')} />
                    </Box>
                }
                placeholder={t('settings.notifications.recipients.emailPlaceholder')}
                value={settings.supportContactEmail}
                onSave={(v) => save({ supportContactEmail: v })}
            />
            <TextSettingControl
                name="activity"
                alignItems="center"
                label={
                    <Box display="flex">
                        {t('settings.notifications.recipients.activity')}
                        <InfoTooltip
                            text={t('settings.notifications.recipients.activityTooltip')}
                        />
                    </Box>
                }
                placeholder={t('settings.notifications.recipients.emailPlaceholder')}
                value={settings.dailyActivityReportContactEmail}
                onSave={(v) => save({ dailyActivityReportContactEmail: v })}
            />
            <TextSettingControl
                name="followup"
                alignItems="center"
                label={
                    <Box display="flex">
                        {t('settings.notifications.recipients.followup')}
                        <InfoTooltip
                            text={t('settings.notifications.recipients.followupTooltip')}
                        />
                    </Box>
                }
                placeholder={t('settings.notifications.recipients.emailPlaceholder')}
                value={settings.declinedServiceEmail}
                onSave={(v) => save({ declinedServiceEmail: v })}
            />
            <TextSettingControl
                name="approval"
                alignItems="center"
                label={
                    <Box display="flex">
                        {t('settings.notifications.recipients.approval')}
                        <InfoTooltip
                            text={t('settings.notifications.recipients.approvalTooltip')}
                        />
                    </Box>
                }
                placeholder={t('settings.notifications.recipients.emailPlaceholder')}
                value={settings.replyToAddress}
                onSave={(v) => save({ replyToAddress: v })}
            />
        </SettingsSection>
    );
}
