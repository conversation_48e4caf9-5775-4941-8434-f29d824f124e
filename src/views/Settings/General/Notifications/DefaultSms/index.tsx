import { Box } from '@mui/material';
import { DefaultSmsAPI } from 'api/settings/DefaultSms';
import { Button } from 'common/components/Button';
import { LimitedTextArea } from 'common/components/Inputs';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import { useAppDispatch } from 'store';
import { setNewToaster } from 'store/actions/toasters.action';
import { SettingLayoutControl, SettingsSection } from 'views/Settings/common';
import RestoreDefaultModal from './RestoreDefaultModal';
import { useStyles } from './css';

interface DefaultSmsProps {
    onLoaded: () => void;
}

export const DefaultSms = ({ onLoaded }: DefaultSmsProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const { callApi: callApiGet } = useApiCall();
    const { callApi: callApiReset, apiCallStatus: resetStatus } = useApiCall();
    const { callApi: callApiSave, apiCallStatus: saveStatus } = useApiCall();
    const [text, setText] = useState('');
    const [restoreOpen, setRestoreOpen] = useState(false);
    const dispatch = useAppDispatch();

    const isSaving = resetStatus === 'Pending' || saveStatus === 'Pending';

    useEffect(() => {
        (async () => {
            const result = await callApiGet(() => DefaultSmsAPI.get(), {
                selectErrorContent: () => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            });
            setText(result ?? '');
            onLoaded();
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [callApiGet, t]);

    const restore = async () => {
        const result = await callApiReset(() => DefaultSmsAPI.reset(), {
            selectErrorContent: () => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });
        setText(result ?? '');
        setRestoreOpen(false);
        dispatch(
            setNewToaster(
                new NotificationData(
                    t('settings.cm.defaultSms.restoreSuccessBody'),
                    t('settings.cm.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    const save = async () => {
        const result = await callApiSave(() => DefaultSmsAPI.save(text), {
            selectErrorContent: () => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });
        setText(result ?? '');
        dispatch(
            setNewToaster(
                new NotificationData(
                    t('settings.cm.defaultSms.saveSuccessBody'),
                    t('settings.cm.updatedConfiguration'),
                    NotificationType.success
                )
            )
        );
    };

    return (
        <SettingsSection label={t('settings.cm.defaultSms.title')}>
            <SettingLayoutControl label={t('settings.cm.defaultSms.smsText')}>
                <LimitedTextArea
                    maxLength={150}
                    value={text}
                    name={'text'}
                    onChange={(e) => setText(e.target.value)}
                    hideLabel
                    rootClassName={styles.textAreaRoot}
                    textAreaClassName={styles.textArea}
                />
                <Box display="flex" justifyContent="end" marginTop={2} gap={0.75}>
                    <Button
                        cmosVariant={'stroke'}
                        cmosSize={'medium'}
                        color={Colors.CM1}
                        onClick={() => setRestoreOpen(true)}
                        disabled={isSaving}
                        label={t('settings.cm.defaultSms.restore')}
                        className={styles.button}
                    />
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={Colors.Success}
                        onClick={save}
                        disabled={isSaving || text.length > 150}
                        showLoader={saveStatus === 'Pending'}
                        label={t('settings.cm.defaultSms.save')}
                        className={styles.button}
                    />
                </Box>
            </SettingLayoutControl>
            {restoreOpen && (
                <RestoreDefaultModal onRestore={restore} onClose={() => setRestoreOpen(false)} />
            )}
        </SettingsSection>
    );
};

export default DefaultSms;
