import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    textAreaRoot: {
        width: '100%',
    },
    textArea: {
        ...FontPrimary(HeaderStyles.H6_12px, false),
        color: '#a1a7ad',
        backgroundColor: theme.palette.neutral[1],
        height: 64,
        boxSizing: 'border-box',
    },
    button: {
        width: 160,
    },
}));
