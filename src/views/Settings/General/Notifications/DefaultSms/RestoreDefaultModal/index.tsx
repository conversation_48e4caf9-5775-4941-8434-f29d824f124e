import { Button } from 'common/components/Button';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import { useStyles } from './css';

interface RestoreDefaultModalProps {
    onRestore: () => Promise<void>;
    onClose: () => void;
}

export const RestoreDefaultModal = ({ onRestore, onClose }: RestoreDefaultModalProps) => {
    const styles = useStyles();
    const { t } = useAppTranslation();
    const [pending, setPending] = useState(false);

    const handleRestore = async () => {
        setPending(true);
        try {
            await onRestore();
        } finally {
            setPending(false);
        }
    };

    return (
        <Modal open onClose={onClose}>
            <div className={styles.restoreDefault}>
                <div className={styles.title}>
                    {t('settings.notifications.restoreDefaultText.title')}
                </div>
                <div className={styles.text}>
                    {t('settings.notifications.restoreDefaultText.text')}
                </div>
                <div className={styles.buttons}>
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={Colors.Neutral3}
                        onClick={onClose}
                        label={t('commonLabels.cancel')}
                        className={styles.button}
                    />
                    <Button
                        cmosVariant={'filled'}
                        cmosSize={'medium'}
                        color={Colors.CM1}
                        onClick={handleRestore}
                        showLoader={pending}
                        label={t('settings.notifications.restoreDefaultText.restore')}
                        className={styles.button}
                    />
                </div>
            </div>
        </Modal>
    );
};

export default RestoreDefaultModal;
