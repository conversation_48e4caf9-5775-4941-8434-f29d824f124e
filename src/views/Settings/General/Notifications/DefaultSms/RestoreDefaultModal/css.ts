import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const useStyles = makeStyles((theme) => ({
    restoreDefault: {
        display: 'flex',
        flexDirection: 'column',
        paddingTop: 34,
        paddingBottom: 35,
        paddingLeft: 40,
        paddingRight: 40,
        width: 699,
        boxSizing: 'border-box',
    },
    title: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[8]),
        lineHeight: '17px',
        marginBottom: 16,
    },
    text: {
        ...FontPrimary(HeaderStyles.H6_12px, false, theme.palette.neutral[8]),
        whiteSpace: 'pre',
        lineHeight: '14px',
        marginBottom: 7,
    },
    buttons: {
        marginTop: 35,
        display: 'flex',
        justifyContent: 'flex-end',
        columnGap: 16,
    },
    button: {
        minWidth: 160,
        width: 'initial',
    },
}));
