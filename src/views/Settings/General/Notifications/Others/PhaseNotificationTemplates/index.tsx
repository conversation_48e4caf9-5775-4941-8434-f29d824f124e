import { Box, styled, useTheme } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import PhaseNotificationTemplateAPI from 'api/PhaseNotificationTemplate';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    addPhaseNotificationTemplate,
    deleteEmptyTemplate,
    getEmptyTemplate,
    setPhaseNotificationTemplate,
} from 'store/slices/Settings/PhaseNotificationTemplate';
import { selectPhaseNotificationTemplatesList } from 'store/slices/Settings/PhaseNotificationTemplate/selectors';
import { usePhasesState } from 'views/OrderDetail/OrderDetailsSection/util';
import PhaseNotificationElement from './PhaseNotificationElement';

type PhaseNotificationsProps = { onLoaded: () => void };

export default function PhaseNotificationTemplates({ onLoaded }: PhaseNotificationsProps) {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const toasters = useToasters();
    const theme = useTheme();

    const settings = useAppSelector(selectSettings);
    const { phases, options } = usePhasesState(settings.uid);
    const phaseNotificationTemplates = useAppSelector(selectPhaseNotificationTemplatesList);

    const allPhasesExists = useMemo(
        () => phaseNotificationTemplates.length === phases.length,
        [phaseNotificationTemplates, phases]
    );

    const canCreate = useMemo(
        () => phaseNotificationTemplates.findIndex((x) => x.id === 0) === -1,
        [phaseNotificationTemplates]
    );

    const uncreatedOptions = useMemo(
        () =>
            options.filter(
                (x) => phaseNotificationTemplates.findIndex((t) => t.phaseId === x.value) === -1
            ),
        [options, phaseNotificationTemplates]
    );

    useQuery(
        ['settings', 'phaseNotificationTemplates'],
        () => PhaseNotificationTemplateAPI.fetchPhaseNotificationTemplates(),
        {
            onSuccess: (data) => {
                dispatch(setPhaseNotificationTemplate(data));
                if (data.length === 0) {
                    createEmptyTemplate();
                }
                onLoaded();
            },
        }
    );

    const createEmptyTemplate = () => {
        const newPhaseNotificationTemplate = getEmptyTemplate();
        dispatch(addPhaseNotificationTemplate(newPhaseNotificationTemplate));
    };

    const create = (phaseId: number, message: string, notificationDelay: number) => {
        createMutation.mutate({ phaseId, message, notificationDelay });
    };

    const createMutation = useMutation(
        (param: { phaseId: number; message: string; notificationDelay: number }) =>
            PhaseNotificationTemplateAPI.createPhaseNotificationTemplate({
                phaseId: param.phaseId,
                message: param.message,
                enabled: true,
                notificationDelay: param.notificationDelay,
            }),
        {
            onSuccess: (response) => {
                toasters.success(
                    t('settings.notifications.phase.createToastBody'),
                    t('settings.notifications.phase.createToastTitle')
                );
                dispatch(deleteEmptyTemplate());
                dispatch(addPhaseNotificationTemplate(response));
            },
        }
    );

    return (
        <Box>
            {phaseNotificationTemplates &&
                phaseNotificationTemplates.map((x) => (
                    <PhaseNotificationElement
                        create={create}
                        phaseNotificationTemplate={x}
                        uncreatedOptions={uncreatedOptions}
                    />
                ))}
            {!allPhasesExists && (
                <StyledButton
                    w={250}
                    color={theme.palette.neutral[3]}
                    cmosVariant={'stroke'}
                    label={t('settings.notifications.phase.addPhase')}
                    Icon={PlusIcon}
                    onClick={createEmptyTemplate}
                    iconPosition={'right'}
                    disabled={createMutation.isLoading || !canCreate}
                />
            )}
        </Box>
    );
}

const StyledButton = styled(Button)({
    '& > div': {
        width: '100%',
        justifyContent: 'space-between',
    },
});
