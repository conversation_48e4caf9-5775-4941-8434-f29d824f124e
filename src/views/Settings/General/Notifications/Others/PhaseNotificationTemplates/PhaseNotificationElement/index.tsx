import { Box, Grid, styled, useTheme } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { Editor } from '@tiptap/core';
import PhaseNotificationTemplateAPI, {
    PhaseNotificationTemplateDto,
} from 'api/PhaseNotificationTemplate';
import { Button } from 'common/components/Button';
import InfoTooltip from 'common/components/InfoTooltip';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import RichTextEditor from 'common/components/Inputs/RichTextEditor';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import {
    PhaseNotificationTemplate,
    updatePhaseNotificationTemplate,
} from 'store/slices/Settings/PhaseNotificationTemplate';
import { usePhasesState } from 'views/OrderDetail/OrderDetailsSection/util';
import SettingsCheckbox from 'views/Settings/common/SettingsCheckbox';
import TemplateRender from '../../OrderNotificationToggleCheckbox/TemplateRenderer';
import { Text } from '@tiptap/extension-text';
import SettingLayoutLabel from '../../../../../common/SettingLayoutLabel';

type PhaseNotificationElementProps = {
    create: (phaseId: number, message: string, notificationDelay: number) => void;
    phaseNotificationTemplate: PhaseNotificationTemplate;
    uncreatedOptions: {
        label: string;
        value: number;
    }[];
};

export default function PhaseNotificationElement({
    create,
    phaseNotificationTemplate,
    uncreatedOptions,
}: PhaseNotificationElementProps) {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const toasters = useToasters();
    const theme = useTheme();

    const settings = useAppSelector(selectSettings);
    const { options } = usePhasesState(settings.uid);
    const [selectedPhase, setSelectedPhase] = useState<OptionData | null>(null);
    const [isMessageInvalid, setMessageInvalid] = useState(false);
    const [message, setMessage] = useState<string>(phaseNotificationTemplate.message);
    const [notificationDelay, setNotificationDelay] = useState<number>(
        phaseNotificationTemplate.notificationDelay
    );

    const textLength = useRef(getTextLength(phaseNotificationTemplate.message));

    const canSaveMessage =
        phaseNotificationTemplate.id !== 0 &&
        !isMessageInvalid &&
        message !== phaseNotificationTemplate.message;

    const maxTextLength = 900;

    useEffect(() => {
        const phase = options.find((x) => x.value === phaseNotificationTemplate.phaseId);
        if (phase) {
            setSelectedPhase(phase);
        }
        setMessage(phaseNotificationTemplate.message);
    }, [options, phaseNotificationTemplate, setSelectedPhase, setMessage]);

    useEffect(() => {
        if (isMessageInvalid) {
            toasters.warning(
                t('settings.notifications.phase.errorToastBody'),
                t('commonLabels.error')
            );
        }
    }, [isMessageInvalid, toasters, t]);

    const notificationDelayList: OptionData<number>[] = [
        { label: t('settings.notifications.phase.seconds', { sec: 0 }), value: 0 },
        { label: t('settings.notifications.phase.seconds', { sec: 15 }), value: 15 },
        { label: t('settings.notifications.phase.seconds', { sec: 30 }), value: 30 },
        { label: '1 min', value: 60 },
        { label: '2 min', value: 120 },
    ];

    const onHtmlChange = (value: string, editor: Editor) => {
        setMessageInvalid(false);
        const text = editor.getText();
        const isInvalid = text.indexOf('\n') !== -1;

        if (isInvalid) {
            setMessageInvalid(true);
        }

        setMessage(value);
        textLength.current = text.length;
    };

    const onPhaseChange = (phaseId: number) => {
        phaseNotificationTemplate.id === 0
            ? create(phaseId, message, notificationDelay)
            : updateMutation.mutate({
                  ...phaseNotificationTemplate,
                  phaseId: phaseId,
                  enabled: true,
              });
    };

    const restoreMessage = () => {
        setMessage(phaseNotificationTemplate.message);
        textLength.current = getTextLength(phaseNotificationTemplate.message);
    };

    const saveMessage = (value: string) => {
        const formattedText = textFormatting(value);
        updateMutation.mutate({
            ...phaseNotificationTemplate,
            message: formattedText,
        });
        setMessage(formattedText);
    };

    const saveNotificationDelay = async (notificationDelay: number) => {
        updateMutation.mutate({
            ...phaseNotificationTemplate,
            notificationDelay: notificationDelay,
        });
        setNotificationDelay(notificationDelay);
    };

    const updateMutation = useMutation(
        (changes: PhaseNotificationTemplateDto) =>
            PhaseNotificationTemplateAPI.updatePhaseNotificationTemplate(changes),
        {
            onSuccess: (response) => {
                toasters.success(
                    t('settings.notifications.phase.updateToastBody'),
                    t('settings.notifications.phase.updateToastTitle')
                );
                dispatch(updatePhaseNotificationTemplate(response));
            },
        }
    );

    return (
        <>
            <Grid item container sx={{ alignItems: 'flex-start' }}>
                <GridLabelContainer sx={{ paddingRight: 0 }}>
                    <SettingLayoutLabel style={{ display: 'flex', alignItems: 'center' }}>
                        {t('settings.notifications.phase.whatsappNotificationDelay')}
                    </SettingLayoutLabel>
                </GridLabelContainer>
                <GridLabelContainer item xs={1} md={1}>
                    <InfoTooltip
                        text={t('settings.notifications.phase.whatsappNotificationDelayTooltip')}
                        position={'right'}
                    />
                </GridLabelContainer>
                <Grid item xs={1} md={1}>
                    <Box display="flex" gap={0.5} alignItems="start">
                        <Box display="block" width="100%" marginBottom="20px">
                            <Dropdown
                                showValidationIndicators
                                options={notificationDelayList}
                                cmosVariant="default"
                                value={{
                                    label: `${
                                        notificationDelay >= 60
                                            ? notificationDelay / 60
                                            : notificationDelay
                                    } ${
                                        notificationDelay >= 60
                                            ? 'min'
                                            : t('settings.notifications.phase.sec')
                                    }`,
                                    value: notificationDelay,
                                }}
                                onChange={(event) => {
                                    if (event === null) return;
                                    saveNotificationDelay(event.value);
                                }}
                            />
                        </Box>
                    </Box>
                </Grid>
            </Grid>
            <Grid item container sx={{ alignItems: 'flex-start' }}>
                <GridLabelContainer item xs={4} md={4}>
                    <Dropdown
                        name="teamMemberPicker"
                        cmosVariant="roundedPrimary"
                        options={uncreatedOptions}
                        value={selectedPhase}
                        onChange={(option) => {
                            if (option !== null) onPhaseChange(option.value);
                        }}
                        placeholder={t('settings.notifications.phase.phaseSelectPlaceholder')}
                        slotProps={{
                            inputWrapper: {
                                sx: { display: 'inline-flex', width: '80%' },
                            },
                        }}
                    />
                    <InfoTooltipContainer>
                        <InfoTooltip
                            text={t('settings.notifications.phase.tooltip')}
                            position={'right'}
                        />
                    </InfoTooltipContainer>
                </GridLabelContainer>
                <Grid item xs={1}>
                    <SettingsCheckboxContainer>
                        <SettingsCheckbox
                            checked={phaseNotificationTemplate.enabled}
                            disabled={phaseNotificationTemplate.phaseId === 0}
                            onChange={(e) =>
                                updateMutation.mutate({
                                    ...phaseNotificationTemplate,
                                    enabled: e.target.checked,
                                })
                            }
                        />
                    </SettingsCheckboxContainer>
                </Grid>
                <Grid item xs={8} md={8}>
                    <Box display="flex" gap={0.5} alignItems="start">
                        <Box display="block" width="100%" marginBottom="20px">
                            <DivExpanderRoot>
                                <TemplateRender
                                    template={t(
                                        'settings.notifications.phase.template.phaseStatus'
                                    )}
                                />
                                <EditorContainer>
                                    <StyledRichTextEditor
                                        _disableEnter
                                        editorButtons={['bold', 'italic']}
                                        html={message}
                                        changeCallbackBehavior="onUpdate"
                                        maxTextLength={maxTextLength}
                                        placeholder={t('settings.notifications.phase.placeholder')}
                                        onHtmlChange={(newHtml, _, editor) =>
                                            onHtmlChange(newHtml, editor)
                                        }
                                    />
                                    <CharCounter>
                                        {textLength.current + '/' + maxTextLength}
                                    </CharCounter>
                                </EditorContainer>
                                <TemplateRender
                                    template={t(
                                        'settings.notifications.phase.template.orderNumber'
                                    )}
                                />
                                <Box
                                    display="flex"
                                    paddingTop={'30px'}
                                    gap={0.75}
                                    justifyContent={'end'}
                                >
                                    <Button
                                        w={160}
                                        cmosVariant={'stroke'}
                                        color={theme.palette.primary.main}
                                        onClick={restoreMessage}
                                        label={t('settings.notifications.phase.restore')}
                                    />
                                    <Button
                                        w={160}
                                        cmosVariant={'filled'}
                                        color={theme.palette.success.main}
                                        onClick={() => saveMessage(message)}
                                        disabled={updateMutation.isLoading || !canSaveMessage}
                                        label={t('settings.notifications.phase.save')}
                                    />
                                </Box>
                            </DivExpanderRoot>
                        </Box>
                    </Box>
                </Grid>
            </Grid>
        </>
    );
}

function textFormatting(value: string): string {
    return value.replace(/\s+/g, ' ');
}

function getTextLength(html: string): number {
    return html
        .replaceAll('<p>', '')
        .replaceAll('</p>', '')
        .replaceAll('<strong>', '')
        .replaceAll('</strong>', '')
        .replaceAll('<em>', '')
        .replaceAll('</em>', '').length;
}

const InfoTooltipContainer = styled('div')({
    display: 'inline-flex',
    alignItems: 'center',
    height: '32px',
    paddingLeft: '5px',
});

const SettingsCheckboxContainer = styled('div')({
    display: 'inline-flex',
    alignItems: 'center',
    height: '32px',
});

const EditorContainer = styled('div')({
    position: 'relative',
});

const StyledRichTextEditor = styled(RichTextEditor)({
    margin: '20px 0',

    '& > :nth-child(1)': {
        marginBottom: '0',
        borderRadius: '10px 10px 0px 0px',
    },

    '& > :nth-child(2)': {
        height: '170px',
        borderRadius: '0px 0px 10px 10px',

        '& > div': { height: '100%' },
    },
}) as typeof RichTextEditor;

const CharCounter = styled('span')(({ theme }) => ({
    fontFamily: 'Inter',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '10px',
    lineHeight: '12px',
    position: 'absolute',
    right: 15,
    bottom: 10,
    color: theme.palette.neutral[5],
}));

const GridLabelContainer = styled(Grid)({
    paddingRight: 20,
    maxWidth: '240px !important',
    boxSizing: 'border-box',
    lineHeight: '32px',
    display: 'inline',
});

const DivExpanderRoot = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[2],
    borderRadius: 12,
    border: `1px solid ${theme.palette.neutral[4]}`,
    padding: 22,
    marginBottom: 20,
}));
