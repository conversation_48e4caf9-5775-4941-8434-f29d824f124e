import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { NotificationsAPI, NotificationsDto } from 'api/settings/Notifications';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import { SettingsSection } from 'views/Settings/common';
import SettingCheckboxControl from 'views/Settings/common/BooleanSettingControl';
import { useSettingsToasters } from 'views/Settings/helpers';
import OrderNotificationToggleCheckbox from './OrderNotificationToggleCheckbox';
import PhaseNotificationTemplates from './PhaseNotificationTemplates';

interface OthersProps {
    onLoaded: () => void;
}

type NotificationPartialUpdate = Partial<Omit<NotificationsDto, 'notifications'>> & {
    notifications?: Partial<NotificationsDto['notifications']>;
};

const QUERY_KEY = ['settings', 'general', 'notifications'];

export const Others = ({ onLoaded }: OthersProps) => {
    const { t } = useAppTranslation();
    const { settingSaved } = useSettingsToasters();
    const queryClient = useQueryClient();

    const [otherNotificationsLoading, setOtherNotificationsLoading] = useState(true);
    const [phaseNotificationTemplatesLoading, setPhaseNotificationTemplatesLoading] =
        useState(true);

    useEffect(() => {
        if (!otherNotificationsLoading && !phaseNotificationTemplatesLoading) {
            onLoaded();
        }
    }, [otherNotificationsLoading, phaseNotificationTemplatesLoading, onLoaded]);

    const { data } = useQuery(QUERY_KEY, NotificationsAPI.get, {
        onSuccess: () => setOtherNotificationsLoading(false),
    });

    const saveMutation = useMutation(NotificationsAPI.save, {
        onMutate: (data) => {
            queryClient.setQueryData(QUERY_KEY, () => data);
        },
        onSuccess: (data) => {
            queryClient.setQueryData(QUERY_KEY, data);
            settingSaved();
        },
    });

    const save = ({
        notifications: notificationsUpdate,
        ...restUpdate
    }: NotificationPartialUpdate) => {
        if (!data) return;
        const { notifications, ...rest } = data!;
        saveMutation.mutate({
            ...rest,
            ...restUpdate,
            notifications: {
                ...notifications,
                ...(notificationsUpdate ?? {}),
            },
        });
    };

    return (
        <>
            <SettingsSection label={t('settings.cm.others.title')}>
                <SettingCheckboxControl
                    label={t('settings.cm.others.pdf')}
                    onChange={(v) => save({ addPdfLinkToUploadNotifications: v })}
                    value={data?.addPdfLinkToUploadNotifications ?? false}
                />
                <SettingCheckboxControl
                    label={t('settings.cm.others.customerContact')}
                    onChange={(v) => save({ enableAddCustomerContactFeatureInMobileApp: v })}
                    value={data?.enableAddCustomerContactFeatureInMobileApp ?? false}
                />
            </SettingsSection>
            <SettingsSection label={t('settings.notifications.orderx.title')}>
                <OrderNotificationToggleCheckbox
                    value={data?.notifications.receptionInProgress ?? false}
                    onChanged={(v) => save({ notifications: { receptionInProgress: v } })}
                    template={t('settings.notifications.orderx.tmpl.reception')}
                    hintText={t('settings.notifications.orderx.hints.reception')}
                    label={t('settings.notifications.orderx.reception')}
                />
                <OrderNotificationToggleCheckbox
                    value={data?.notifications.inspectionInProgress ?? false}
                    onChanged={(v) => save({ notifications: { inspectionInProgress: v } })}
                    template={t('settings.notifications.orderx.tmpl.inspection')}
                    hintText={t('settings.notifications.orderx.hints.inspection')}
                    label={t('settings.notifications.orderx.inspection')}
                />
                <OrderNotificationToggleCheckbox
                    value={data?.notifications.estimateIsReady ?? false}
                    onChanged={(v) => save({ notifications: { estimateIsReady: v } })}
                    template={t('settings.notifications.orderx.tmpl.estimateReady')}
                    hintText={t('settings.notifications.orderx.hints.estimateReady')}
                    label={t('settings.notifications.orderx.estimateReady')}
                />
                <OrderNotificationToggleCheckbox
                    value={data?.notifications.estimateApproval ?? false}
                    onChanged={(v) => save({ notifications: { estimateApproval: v } })}
                    template={t('settings.notifications.orderx.tmpl.estimateApproval')}
                    hintText={t('settings.notifications.orderx.hints.estimateApproval')}
                    label={t('settings.notifications.orderx.estimateApproval')}
                />
            </SettingsSection>
            <SettingsSection label={t('settings.notifications.phase.title')}>
                <PhaseNotificationTemplates
                    onLoaded={() => setPhaseNotificationTemplatesLoading(false)}
                />
            </SettingsSection>
        </>
    );
};

export default Others;
