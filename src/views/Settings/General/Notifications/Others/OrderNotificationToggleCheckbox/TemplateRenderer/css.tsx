import { makeStyles } from '@mui/styles';

const useTemplateRendererStyles = makeStyles((theme) => ({
    param: {
        color: theme.palette.primary.main,
    },
    boldParam: {
        color: theme.palette.primary.main,
        fontWeight: 'bold',
    },
    text: {
        whiteSpace: 'pre-line',
        color: theme.palette.neutral[7],
        ...theme.typography.h6,
        fontWeight: 'normal',
    },
}));

export default useTemplateRendererStyles;
