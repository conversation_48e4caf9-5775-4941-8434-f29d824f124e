import React from 'react';
import useTemplateRendererStyles from './css';

type TemplateRenderProps = {
    template: string;
};

const TemplateRender = React.memo(({ template }: TemplateRenderProps) => {
    const styles = useTemplateRendererStyles();
    const parts = template.split(/((?:\[.*?\])|(?:\*.*\*))/).map((item, index) => {
        if (index % 2 === 0) {
            return <span key={`text${index}`}>{item}</span>;
        }

        if (item.startsWith('*')) {
            return <b>{item.substring(1, item.length - 1)}</b>;
        }

        const param = item.substring(1, item.length - 1);
        if (param.startsWith('*')) {
            return (
                <span key={`param${index}`}>
                    [
                    <span className={styles.boldParam}>{param.substring(1, param.length - 1)}</span>
                    ]
                </span>
            );
        }

        return (
            <span key={`param${index}`}>
                [<span className={styles.param}>{param}</span>]
            </span>
        );
    });

    return <div className={styles.text}>{parts}</div>;
});

export default TemplateRender;
