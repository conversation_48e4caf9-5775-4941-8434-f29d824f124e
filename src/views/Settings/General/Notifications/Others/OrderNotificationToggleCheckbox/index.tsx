import { Box } from '@mui/material';
import { SettingLayoutControl } from 'views/Settings/common';
import SettingsCheckbox from 'views/Settings/common/SettingsCheckbox';
import TemplateRender from './TemplateRenderer';

type OrderNotificationToggleCheckboxProps = {
    value: boolean;
    label: string;
    onChanged: (value: boolean) => void;
    template: string;
    hintText?: string;
};

export default function OrderNotificationToggleCheckbox({
    value,
    label,
    onChanged,
    template,
    hintText,
}: OrderNotificationToggleCheckboxProps) {
    return (
        <SettingLayoutControl hasHint hintText={hintText} label={label} childrenMD={9}>
            <Box sx={{ display: 'flex', gap: '75px', alignItems: 'start' }}>
                <SettingsCheckbox checked={value} onChange={(e) => onChanged(e.target.checked)} />
                <TemplateRender template={template} />
            </Box>
        </SettingLayoutControl>
    );
}
