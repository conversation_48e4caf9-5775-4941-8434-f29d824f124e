import { Grid, styled } from '@mui/material';
import { SystemsSettingsAPI } from 'api/settings/InspectionForms/SystemsSettings';
import { Button } from 'common/components/Button';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { MoveDownIcon } from 'common/components/Icons/MoveDownIcon';
import { MoveUpIcon } from 'common/components/Icons/MoveUpIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import { Trans } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { setNewToaster } from 'store/actions/toasters.action';
import { SystemsForm } from '../SystemsForm';
import { TemplatesList } from '../TemplatesList';
import { SystemListItemProps } from './props';

export const SystemListItem = ({
    system,
    templateId,
    index,
    isLastItem,
    onDelete,
    onMove,
    onChangeIsHide,
    readonly,
}: SystemListItemProps) => {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const { callApi, apiCallStatus } = useApiCall();
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);
    const canPerformSave = apiCallStatus !== 'Pending';

    const fetchToggleHide = async (isHidden: boolean) => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;
        if (!system) return;
        if (system.isHidden === isHidden) return;

        const updatedSettings = {
            ...system,
            isHidden,
        };

        await callApi(async () => SystemsSettingsAPI.save(updatedSettings), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        if (onChangeIsHide) {
            onChangeIsHide(updatedSettings);
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.systems.notifications.successfullyUpdatedTitle'),
                    NotificationType.success
                )
            )
        );
    };

    const fetchDelete = async () => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;

        await callApi(async () => SystemsSettingsAPI.delete(system.systemId), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        if (onDelete) {
            onDelete(system);
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('inspectionForms.systems.notifications.successfullyDeleteBody'),
                    t('inspectionForms.systems.notifications.successfullyDeleteTitle'),
                    NotificationType.success
                )
            )
        );
    };

    const applyMove = async (newSerialNumber: number) => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;

        await callApi(async () => SystemsSettingsAPI.reorder(system.systemId, newSerialNumber), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        //if (response.serialNumber == newSerialNumber) {
        if (onMove) {
            onMove(system.serialNumber, newSerialNumber);
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.systems.notifications.successfullyUpdatedTitle'),
                    NotificationType.success
                )
            )
        );
    };

    const moveUpHandler = async () => {
        if (system.serialNumber <= 0) return; // index bound detection
        applyMove(system.serialNumber - 1);
    };

    const moveDownHandler = () => {
        if (isLastItem) return; // index bound detection
        applyMove(system.serialNumber + 1);
    };

    return (
        <>
            <GridSystem container justifyContent="center">
                <DivHeader>
                    <div className="title">
                        <SystemsForm
                            readonly={readonly}
                            system={system}
                            onSaveSystem={() => {
                                //is it correct?
                            }}
                            templateId={system.templateId}
                        />
                    </div>
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={system.isHidden ? HideIcon : ShowIcon}
                        color={system.isHidden ? Colors.Neutral4 : Colors.CM1}
                        onClick={() => fetchToggleHide(!system.isHidden)}
                    />
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={DeleteIcon}
                        color={Colors.Neutral4}
                        onClick={() => setShowDeleteConfirmation(true)}
                    />
                    <div />
                    {index === 0 ? (
                        <div /> // NOTE: this div serves a purpose since parent element is a grid and we need an actual element to take up space
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={MoveUpIcon}
                            color={Colors.Neutral4}
                            onClick={moveUpHandler}
                        />
                    )}
                    {isLastItem ? (
                        <div />
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={MoveDownIcon}
                            color={Colors.Neutral4}
                            onClick={moveDownHandler}
                        />
                    )}
                </DivHeader>
                <DivList>
                    <TemplatesList
                        readonly={readonly}
                        systemId={system.systemId}
                        templateId={templateId}
                        templateItems={system.items}
                    />
                </DivList>
            </GridSystem>
            {showDeleteConfirmation && (
                <DeleteConfirmationPopup
                    open={showDeleteConfirmation}
                    title={t('inspectionForms.systems.confirmationDelete.title')}
                    body={<Trans i18nKey="inspectionForms.systems.confirmationDelete.body" t={t} />}
                    cancel={t('inspectionForms.systems.confirmationDelete.cancel')}
                    confirm={t('inspectionForms.systems.confirmationDelete.confirm')}
                    onConfirm={fetchDelete}
                    onClose={() => {
                        setShowDeleteConfirmation(false);
                    }}
                />
            )}
        </>
    );
};

const GridSystem = styled(Grid)({
    border: '1px solid rgba(201, 205, 211, 0.5)',
    borderRadius: '12px',
    overflow: 'hidden',
    marginBottom: 39,
});

const DivHeader = styled('div')(({ theme }) => ({
    display: 'grid',
    gridTemplateColumns: '1fr auto auto auto 32px 32px',
    alignItems: 'center',
    background: theme.palette.neutral[3],
    paddingLeft: 15,
    paddingRight: 25,
    width: '100%',
    height: 38,
    '& .title': {
        ...theme.typography.h5Roboto,
        fontWeight: 'bold',
        color: theme.palette.neutral[8],
    },
}));

const DivList = styled('div')({
    width: '100%',
    paddingTop: 20,
    paddingBottom: 20,
});
