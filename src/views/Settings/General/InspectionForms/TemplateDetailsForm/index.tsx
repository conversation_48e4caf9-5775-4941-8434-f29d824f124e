import { Grid } from '@mui/material';
import { TemplateItemsSettings } from 'api/settings/InspectionForms/TemplateItemsSettings';
import { Button } from 'common/components/Button';
import { InfoIcon } from 'common/components/Icons/InfoIcon';
import { LimitedTextArea } from 'common/components/Inputs';
import { Modal } from 'common/components/Modal';
import { IDefaultErrorResponse, useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useInputValue } from 'common/hooks/useInputValue';
import { Colors } from 'common/styles/Colors';
import { useConfirmModalClasses } from 'common/styles/ConfirmModalStyles';
import { TemplateItemDto } from 'datacontracts/InspectionForms/TemplateItemDto';
import { useStyles } from './css';

export const TemplateDetailsForm = ({
    open: isOpen,
    onClose,
    templateItem: item,
    onSave,
}: {
    open: boolean;
    onClose: () => void;
    onSave: (details: string) => void;
    templateItem: TemplateItemDto;
}) => {
    const maxMessageLength = 256;
    const modalClasses = useConfirmModalClasses();
    const { t } = useAppTranslation();
    const styles = useStyles();
    const { callApi, apiCallStatus } = useApiCall();
    const [details, setDetails] = useInputValue(item.details ?? '');

    const handleSave = async () => {
        const trimmedDetails = details.trim();
        if (trimmedDetails === (item.details ?? '')) {
            //do not call server if details wasn't changed
            onClose();
            return;
        }

        await callApi(
            () =>
                TemplateItemsSettings.updateDetails(
                    item.systemId,
                    item.masterItemId,
                    trimmedDetails
                ),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        onSave(trimmedDetails);
    };

    return (
        <Modal open={isOpen}>
            <Grid container direction="column" style={{ gap: 30 }} className={modalClasses.content}>
                <Grid item>
                    <Grid container justifyContent="space-between">
                        <Grid item>
                            <Grid container alignContent="center" style={{ height: '100%' }}>
                                <label className={modalClasses.title}>
                                    {t('inspectionForms.detailsForm.title')}
                                </label>
                            </Grid>
                        </Grid>
                        <Grid item>
                            <Grid container style={{ gap: 10 }}>
                                <Grid item>
                                    <Button
                                        color={Colors.Neutral3}
                                        cmosSize={'medium'}
                                        className={modalClasses.cancelBtn}
                                        label={t('commonLabels.cancel')}
                                        onClick={onClose}
                                        cmosVariant={'filled'}
                                    />
                                </Grid>
                                <Grid item>
                                    <Button
                                        color={Colors.Success}
                                        cmosSize={'medium'}
                                        className={modalClasses.confirmBtn}
                                        label={t('commonLabels.save')}
                                        disabled={details.length > maxMessageLength}
                                        showLoader={apiCallStatus === 'Pending'}
                                        onClick={handleSave}
                                        cmosVariant={'filled'}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
                <Grid item>
                    <Grid container direction="column" style={{ gap: 7 }}>
                        <Grid item>
                            <LimitedTextArea
                                name={'comments'}
                                rows={5}
                                label={t('inspectionForms.detailsForm.label')}
                                placeholder={t('inspectionForms.detailsForm.placeholder')}
                                value={details}
                                onChange={setDetails}
                                maxLength={maxMessageLength}
                                counterClassName={styles.counter}
                            />
                        </Grid>
                        <Grid item>
                            <Grid container style={{ gap: 8 }} alignContent="center">
                                <Grid item className={modalClasses.tooltipIcon}>
                                    <InfoIcon size={18} fill={Colors.White} />
                                </Grid>
                                <Grid item>
                                    <Grid
                                        container
                                        alignContent="center"
                                        style={{ height: '100%' }}
                                    >
                                        <Grid item className={modalClasses.tooltipText}>
                                            {t('inspectionForms.detailsForm.starttyping')}
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        </Modal>
    );
};
