import { styled } from '@mui/material';
import { SystemsSettingsAPI } from 'api/settings/InspectionForms/SystemsSettings';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { InspectionFormTemplate } from 'datacontracts/InspectionForms/InspectionFormTemplate';
import { SystemDto } from 'datacontracts/InspectionForms/SystemDto';
import { SystemListItemDto } from 'datacontracts/InspectionForms/SystemListItemDto';
import { startTransition, useEffect, useState } from 'react';
import PageContent from 'views/Components/Page';
import { SystemsForm } from './SystemsForm/index';
import { SystemsList } from './SystemsList';
import { TemplatesForm } from './TemplatesForm/index';

export function InspectionFormsSettings() {
    const { t } = useAppTranslation();
    const [selectedTemplate, setSelectedTemplate] = useState<InspectionFormTemplate>();
    const [showSeparator, setShowSeparator] = useState(false);
    const [systems, setSystems] = useState<SystemListItemDto[]>([]);
    const { callApi, apiCallStatus } = useApiCall();

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.inspectionForms')}`);

    const isReadonly = !!selectedTemplate?.enterpriseName;

    const onNewHandler = (system: SystemDto) => {
        const systemListItem: SystemListItemDto = {
            ...(system as SystemListItemDto),
            items: [],
        };
        setSystems([...systems, systemListItem]);
    };

    const fetchListSystems = async () => {
        if (!selectedTemplate || !selectedTemplate.inspectionFormTemplateId) return;
        if (typeof selectedTemplate == 'undefined') return;

        const response = await callApi(
            async () => SystemsSettingsAPI.list(selectedTemplate.inspectionFormTemplateId ?? 0),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }
        );

        const totalItems = response.systems.map((x) => x.items.length).reduce((a, b) => a + b, 0);

        if (totalItems > 20) {
            // update state in a transition (slightly slower but UI won't freeze)
            startTransition(() => {
                setSystems(response.systems);
            });
        } else {
            setSystems(response.systems);
        }
    };

    const moveSystemHandler = (from: number, to: number) => {
        const newArray = systems
            .map((system, i) => {
                if (from === i) {
                    return { ...system, serialNumber: to };
                } else if (to === i) {
                    return { ...system, serialNumber: from };
                } else {
                    return { ...system };
                }
            })
            .sort((a, b) =>
                a.serialNumber < b.serialNumber ? -1 : a.serialNumber > b.serialNumber ? 1 : 0
            );
        setSystems(newArray);
    };

    const hideSystemHandler = ({ systemId, isHidden }: SystemListItemDto) => {
        const newArray = systems.map((s) =>
            s.systemId === systemId ? { ...s, isHidden } : { ...s }
        );
        setSystems([...newArray]);
    };

    const deleteSystemHandler = ({ systemId, serialNumber }: SystemListItemDto) => {
        const newArray = systems
            .map((s) => {
                if (s.serialNumber > serialNumber) {
                    return { ...s, serialNumber: s.serialNumber - 1 };
                } else {
                    return { ...s };
                }
            })
            .filter((s) => s.systemId !== systemId);
        setSystems(newArray);
    };

    useEffect(() => {
        fetchListSystems();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedTemplate]);

    return (
        <>
            <PageContent paddedX paddedY>
                <DivHeader>
                    <TemplatesForm
                        readonly={isReadonly}
                        onEditModeChange={(editMode) => setShowSeparator(editMode)}
                        onSelectTemplate={(template) => setSelectedTemplate(template)}
                    />
                    {selectedTemplate?.inspectionFormTemplateId && (
                        <SystemsForm
                            readonly={isReadonly}
                            templateId={selectedTemplate.inspectionFormTemplateId}
                            onSaveSystem={onNewHandler}
                        />
                    )}
                </DivHeader>
                {showSeparator && <DivSeparator />}
                <SystemsList
                    readonly={isReadonly}
                    systems={systems}
                    templateId={selectedTemplate?.inspectionFormTemplateId ?? -1}
                    isLoadingInProgress={apiCallStatus === 'Pending'}
                    deleteSystemHandler={deleteSystemHandler}
                    hideSystemHandler={hideSystemHandler}
                    moveSystemHandler={moveSystemHandler}
                />
            </PageContent>
        </>
    );
}

const DivSeparator = styled('div')(({ theme }) => ({
    borderBottom: `solid 1px ${theme.palette.neutral[3]}`,
    width: '100%',
    marginBottom: 30,
}));

const DivHeader = styled('div')({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: 34,
});
