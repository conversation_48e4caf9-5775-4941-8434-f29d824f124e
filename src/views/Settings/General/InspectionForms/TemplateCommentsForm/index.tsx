import { Grid, styled } from '@mui/material';
import { TechnicianCommentsSettings } from 'api/settings/InspectionForms/TechnicianCommentsSettings';
import { Button } from 'common/components/Button';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import DropdownMulti from 'common/components/Inputs/DropdownMulti';
import { Modal } from 'common/components/Modal';
import { IDefaultErrorResponse, useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { TechnicianCommentDto } from 'datacontracts/TechnicianCommentDto';
import { useEffect, useState } from 'react';
import { TemplateCommentsFormProps } from './props';

const sortable = (a: OptionData, b: OptionData) => {
    if (a.label.toLowerCase() < b.label.toLowerCase()) return -1;
    if (a.label.toLowerCase() > b.label.toLowerCase()) return 1;
    return 0;
};

export const TemplateCommentsForm = ({ open, templateItem, onSave }: TemplateCommentsFormProps) => {
    const { t } = useAppTranslation();
    const [showMoreCustomCommentInfo, setShowMoreCustomCommentInfo] = useState(false);
    const [showMoreStyleCommentInfo, setShowMoreStyleCommentInfo] = useState(false);
    const [options, setOptions] = useState<OptionData[]>([]);
    const [selectedOptions, setSelectedOptions] = useState<OptionData[]>([]);
    const [anythingChanged, setAnythingChanged] = useState(false);
    const { callApi } = useApiCall();

    const fetchLoadComments = async () => {
        if (!templateItem?.masterItemId) return;

        const response = await callApi(
            () => TechnicianCommentsSettings.list(templateItem.masterItemId),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        const mapped = response.map((comment) => ({
            label: comment.text,
            value: comment.technicianCommentId,
            isDeleted: comment.isDeleted,
        }));
        setOptions(mapped.sort(sortable));
        setSelectedOptions(mapped.filter((comment) => !comment.isDeleted).sort(sortable));
    };

    const deleteHandler = async (event: OptionData) => {
        if (!templateItem?.masterItemId) return;

        await callApi(async () => TechnicianCommentsSettings.delete(event.value), {
            selectErrorContent: (response: IDefaultErrorResponse) => ({
                body: response.data.message,
            }),
        });

        const newSelectedOptions = selectedOptions
            .filter((c) => c.value !== event.value)
            .sort(sortable);
        setSelectedOptions([...newSelectedOptions]);
        setAnythingChanged(true);
    };

    const selectHandler = async (option: OptionData): Promise<TechnicianCommentDto> => {
        const response = await callApi(
            async () =>
                TechnicianCommentsSettings.save(templateItem?.masterItemId ?? 0, option.label),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        setSelectedOptions(
            [
                ...selectedOptions,
                {
                    label: option.label,
                    value: response.technicianCommentId,
                },
            ].sort(sortable)
        );
        //replace Id for comment with same text, if it exists
        setOptions([
            ...options
                .map((c) =>
                    c.label === response.text
                        ? {
                              ...c,
                              value: response.technicianCommentId,
                          }
                        : c
                )
                .sort(sortable),
        ]);
        setAnythingChanged(true);

        return response;
    };

    const createHandler = async (option: OptionData) => {
        const comment = await selectHandler(option);

        setOptions(
            [
                ...options,
                {
                    label: option.label,
                    value: comment.technicianCommentId,
                },
            ].sort(sortable)
        );
    };

    useEffect(() => {
        fetchLoadComments();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [templateItem?.masterItemId]);

    return (
        <>
            <Modal open={open}>
                <DivContent>
                    <Grid container justifyContent="space-between">
                        <Grid item>
                            <GridTitle container alignContent="center">
                                <label>{t('inspectionForms.commentsForm.title')}</label>
                            </GridTitle>
                        </Grid>
                        <Grid item>
                            <Grid container style={{ gap: 10 }}>
                                <Grid item>
                                    <Button
                                        color={Colors.Neutral3}
                                        cmosSize={'medium'}
                                        label={t('commonLabels.cancel')}
                                        onClick={() => {
                                            //probably another action required
                                            if (onSave)
                                                onSave(anythingChanged, selectedOptions.length);
                                        }}
                                        cmosVariant={'filled'}
                                        sx={{ width: 164 }}
                                    />
                                </Grid>
                                <Grid item>
                                    <Button
                                        color={Colors.Success}
                                        cmosSize={'medium'}
                                        label={t('commonLabels.save')}
                                        onClick={() => {
                                            if (onSave)
                                                onSave(anythingChanged, selectedOptions.length);
                                        }}
                                        cmosVariant={'filled'}
                                        sx={{ width: 227 }}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                    <DivRowLabel>{t('inspectionForms.commentsForm.label')}</DivRowLabel>
                    <DropdownMulti
                        cmosVariant="grey"
                        options={options}
                        value={selectedOptions}
                        onCreate={createHandler}
                        onSelect={selectHandler}
                        onRemove={deleteHandler}
                        creatable
                    />
                    <DivRowInfo>
                        <div className={'noteLabel'}>{t('inspectionForms.commentsForm.note')}</div>
                        <div className={'label'}>
                            {t('inspectionForms.commentsForm.starttyping')}
                        </div>
                    </DivRowInfo>
                    <DivRowMoreLessInfo
                        onClick={() => setShowMoreCustomCommentInfo(!showMoreCustomCommentInfo)}
                    >
                        {t(
                            `inspectionForms.commentsForm.${
                                !showMoreCustomCommentInfo ? 'moreInfo' : 'hideInfo'
                            }`
                        )}
                    </DivRowMoreLessInfo>
                    {showMoreCustomCommentInfo && (
                        <>
                            <DivRowTipsTitle>
                                {t('inspectionForms.commentsForm.tipsCustomComments')}
                            </DivRowTipsTitle>

                            <DivRowTip>
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.addCustomCommentsInfo'
                                        ),
                                    }}
                                />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.deleteCustomCommentsInfo'
                                        ),
                                    }}
                                />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.InstantSynchronizationInfo'
                                        ),
                                    }}
                                />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.officialCommentsInfo'
                                        ),
                                    }}
                                />
                            </DivRowTip>
                        </>
                    )}
                    <DivRowDivider />
                    <DivRowCounter>
                        <div className={'number'}>
                            <div>
                                {
                                    selectedOptions.filter((comment) => comment.label.includes('_'))
                                        .length
                                }
                            </div>
                        </div>
                        <div className={'label'}>
                            {t('inspectionForms.commentsForm.fillInTheBlankInfo')}
                        </div>
                    </DivRowCounter>
                    <DivRowMoreLessInfo
                        onClick={() => setShowMoreStyleCommentInfo(!showMoreStyleCommentInfo)}
                    >
                        {t(
                            `inspectionForms.commentsForm.${
                                !showMoreStyleCommentInfo ? 'moreInfo' : 'hideInfo'
                            }`
                        )}
                    </DivRowMoreLessInfo>
                    {showMoreStyleCommentInfo && (
                        <>
                            <DivRowTipsTitle>
                                {t('inspectionForms.commentsForm.tipsStyleComments')}
                            </DivRowTipsTitle>
                            <DivRowTip>
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.addStyleCommentsInfo'
                                        ),
                                    }}
                                />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.deleteStyleCommentsInfo'
                                        ),
                                    }}
                                />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.InstantSynchronizationInfo'
                                        ),
                                    }}
                                />
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: t(
                                            'inspectionForms.commentsForm.commentsTypeCaptureInformationInfo'
                                        ),
                                    }}
                                />
                            </DivRowTip>
                        </>
                    )}
                </DivContent>
            </Modal>
        </>
    );
};

const DivContent = styled('div')({
    width: 961 - 86 - 86,
    marginTop: 47,
    marginBottom: 26,
    marginRight: 86,
    marginLeft: 86,
});

const GridTitle = styled(Grid)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    ...theme.typography.h4Inter,
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
    height: '100%',
}));

const DivRow = styled('div')({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
});

const DivRowLabel = styled(DivRow)(({ theme }) => ({
    marginTop: 15,
    marginBottom: 7,
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
}));

const DivRowInfo = styled(DivRow)(({ theme }) => ({
    marginTop: 14,
    marginBottom: 2.3,
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[7],
    fontWeight: 'normal',
    '& .noteLabel': {
        marginRight: 4,
        fontWeight: 'bold',
    },
}));

const DivRowMoreLessInfo = styled(DivRow)(({ theme }) => ({
    marginLeft: 33,
    cursor: 'pointer',
    ...theme.typography.h6Roboto,
    color: theme.palette.primary.main,
    fontWeight: 'bold',
}));

const DivRowTipsTitle = styled(DivRow)(({ theme }) => ({
    ...theme.typography.h4Roboto,
    fontWeight: 'bold',
    color: theme.palette.neutral[8],
    marginBottom: 24,
    marginTop: 24,
}));

const DivRowTip = styled(DivRow)(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[8],
    fontWeight: 'normal',
    maxHeight: 192,
    gap: 14,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    flexWrap: 'nowrap',
    '& > div': {
        marginTop: 0,
        marginBottom: 0,
        '&:last-child': {
            marginBottom: 0,
        },
    },
}));

const DivRowDivider = styled(DivRow)({
    width: '100%',
    minHeight: 1,
    borderTop: '1px solid #EFEFEF',
    marginTop: 18,
    marginBottom: 18,
});

const DivRowCounter = styled(DivRow)(({ theme }) => ({
    marginTop: 14,
    marginBottom: 2.3,
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    '& .number': {
        ...theme.typography.h5Roboto,
        fontWeight: 'bold',
        color: theme.palette.neutral[1],
        width: 20,
        height: 20,
        display: 'flex',
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'center',
        background: Colors.Warning,
        borderRadius: '100%',
        marginRight: 8,
        '&>div': {
            height: 14,
        },
    },
    '& .label': {},
}));
