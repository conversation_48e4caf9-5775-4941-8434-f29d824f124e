import { TemplateItemDto } from 'datacontracts/InspectionForms/TemplateItemDto';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
export interface TemplateItemListProps {
    templateId: number;
    templateItem: TemplateItemDto;
    onDelete: (item: TemplateItemDto) => void;
    onChange: (item: TemplateItemDto) => void;
    onEditEstimate: (item: TemplateItemDto) => void;
    onEditDetails: (item: TemplateItemDto) => void;
    onCommentsEdit: (item: TemplateItemDto) => void;
    dragOptions?: DraggableProvidedDragHandleProps;
    readonly?: boolean;
}
