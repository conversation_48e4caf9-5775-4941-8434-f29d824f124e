import { styled } from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import { TemplateItemsSettings } from 'api/settings/InspectionForms/TemplateItemsSettings';
import { Button } from 'common/components/Button';
import { CashIcon } from 'common/components/Icons/CashIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { CommentsIcon } from 'common/components/Icons/CommentsIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { DetailsIcon } from 'common/components/Icons/DetailsIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { DragAndDropEditableTextField } from 'common/components/Inputs/DragAndDropEditableTextField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { IDefaultErrorResponse, useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setNewToaster } from 'store/actions/toasters.action';
import { TemplateItemListProps } from './props';

export const TemplateItemList = ({
    templateItem,
    dragOptions,
    onCommentsEdit,
    onDelete,
    onEditEstimate,
    onEditDetails,
    onChange,
    readonly,
}: TemplateItemListProps) => {
    //#region states
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const { callApi, apiCallStatus } = useApiCall();
    const [name, setName] = useState(templateItem.name);
    const [editMode, setEditMode] = useState(false);
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);
    const canPerformSave = apiCallStatus !== 'Pending';

    const fetchToggleHide = async (isHidden: boolean) => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;
        if (isHidden === templateItem.isHidden) return;

        await callApi(
            async () =>
                TemplateItemsSettings.updateVisibility(
                    templateItem.systemId,
                    templateItem.masterItemId,
                    isHidden
                ),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        if (onChange) {
            onChange({ ...templateItem, isHidden });
        }

        setEditMode(false);

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle'),
                    NotificationType.success
                )
            )
        );
    };

    const fetchUpdateName = async () => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;

        if (name !== templateItem.name) {
            await callApi(
                async () =>
                    TemplateItemsSettings.updateName(
                        templateItem.systemId,
                        templateItem.masterItemId,
                        name
                    ),
                {
                    selectErrorContent: (response: IDefaultErrorResponse) => ({
                        body: response.data.message,
                    }),
                }
            );

            if (onChange) {
                onChange({ ...templateItem, name });
            }

            dispatch(
                setNewToaster(
                    new NotificationData(
                        t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
                        t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle'),
                        NotificationType.success
                    )
                )
            );
        }

        setEditMode(false);
    };

    const fetchDelete = async () => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;

        await callApi(
            async () =>
                TemplateItemsSettings.delete(templateItem.systemId, templateItem.masterItemId),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        if (onDelete) {
            onDelete(templateItem);
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('inspectionForms.templateItems.notifications.successfullyDeleteBody'),
                    t('inspectionForms.templateItems.notifications.successfullyDeleteTitle'),
                    NotificationType.success
                )
            )
        );
    };

    const handleEdit = useCallback(() => {
        setEditMode(!editMode);
        setName(templateItem.name);
    }, [editMode, templateItem]);

    const handleOpenComments = useCallback(() => {
        onCommentsEdit && onCommentsEdit(templateItem);
    }, [onCommentsEdit, templateItem]);

    const handleOpenDetails = useCallback(() => {
        onEditDetails && onEditDetails(templateItem);
    }, [onEditDetails, templateItem]);

    const handleOpenEstimate = useCallback(() => {
        onEditEstimate && onEditEstimate(templateItem);
    }, [onEditEstimate, templateItem]);

    const handleToggleHide = useCallback(() => {
        fetchToggleHide(!templateItem.isHidden);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [templateItem]);

    const handleOpenDeletion = useCallback(() => {
        setShowDeleteConfirmation(true);
    }, []);

    useEffect(() => {
        setName(templateItem.name);
    }, [templateItem.name]);

    return (
        <>
            <StyledContainer>
                <div className={'title'}>
                    <DragAndDropEditableTextField
                        disabled={readonly}
                        dragAndDropProps={dragOptions ?? undefined}
                        value={name}
                        onChange={(event) => setName(event.target.value)}
                        isEditMode={editMode && apiCallStatus !== 'Pending'}
                        showLoader={editMode && apiCallStatus === 'Pending'}
                        onSaveClick={() => fetchUpdateName()}
                    />
                </div>

                <div className="options">
                    {!templateItem.isCustom ? (
                        <Tooltip title={t('inspectionForms.templateItems.tooltip')}>
                            <span aria-disabled={false}>
                                <Button
                                    disabled={true}
                                    cmosVariant={'typography'}
                                    Icon={EditIcon}
                                    color={Colors.Neutral3}
                                    onClick={() => {}}
                                />
                            </span>
                        </Tooltip>
                    ) : (
                        <Button
                            disabled={readonly}
                            cmosVariant={'typography'}
                            Icon={editMode ? CloseIcon : EditIcon}
                            color={Colors.Neutral3}
                            onClick={handleEdit}
                        />
                    )}
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={CommentsIcon}
                        color={templateItem.commentsCount > 0 ? Colors.CM1 : Colors.Neutral3}
                        onClick={handleOpenComments}
                    />
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={DetailsIcon}
                        color={templateItem.details ? Colors.CM1 : Colors.Neutral3}
                        onClick={handleOpenDetails}
                    />
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={CashIcon}
                        color={templateItem.hasEditedEstimate ? Colors.CM1 : Colors.Neutral3}
                        onClick={handleOpenEstimate}
                    />
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={templateItem.isHidden ? HideIcon : ShowIcon}
                        color={templateItem.isHidden ? Colors.Neutral3 : Colors.CM1}
                        onClick={handleToggleHide}
                    />
                    <Button
                        disabled={readonly}
                        cmosVariant={'typography'}
                        Icon={DeleteIcon}
                        color={Colors.Neutral3}
                        onClick={handleOpenDeletion}
                    />
                </div>
            </StyledContainer>

            {showDeleteConfirmation && (
                <DeleteConfirmationPopup
                    open={showDeleteConfirmation}
                    title={t('inspectionForms.templateItems.confirmationDelete.title')}
                    body={t('inspectionForms.templateItems.confirmationDelete.body')}
                    cancel={t('inspectionForms.templateItems.confirmationDelete.cancel')}
                    confirm={t('inspectionForms.templateItems.confirmationDelete.confirm')}
                    onConfirm={async () => {
                        await fetchDelete();
                        setShowDeleteConfirmation(false);
                    }}
                    onClose={() => {
                        setShowDeleteConfirmation(false);
                    }}
                />
            )}
        </>
    );
};

const StyledContainer = styled('div')(({ theme }) => ({
    marginBottom: 8,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    '& .title': {
        width: 653,
        display: 'flex',
        alignItems: 'center',
        height: 44,
        flexGrow: 1,
        '& .drag-and-drop-editable': {
            width: '100%',
        },
    },
    '& .options': {
        display: 'flex',
        justifyContent: 'flex-start',
        flexDirection: 'row',
        paddingRight: 78,
    },
}));
