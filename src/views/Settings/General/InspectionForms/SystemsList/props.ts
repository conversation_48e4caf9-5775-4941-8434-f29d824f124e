import { SystemListItemDto } from 'datacontracts/InspectionForms/SystemListItemDto';

export interface SystemsListProps {
    isLoadingInProgress: boolean;
    systems: SystemListItemDto[];
    templateId: number;
    deleteSystemHandler: (item: SystemListItemDto) => void;
    hideSystemHandler: (item: SystemListItemDto) => void;
    moveSystemHandler: (from: number, to: number) => void;
    readonly?: boolean;
}
