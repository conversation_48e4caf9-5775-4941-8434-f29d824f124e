import { styled } from '@mui/material';
import AreaSpinner from 'common/components/AreaSpinner';
import { SystemListItem } from '../SystemListItem';
import { SystemsListProps } from './props';

export const SystemsList = ({
    systems,
    templateId,
    isLoadingInProgress,
    deleteSystemHandler,
    hideSystemHandler,
    moveSystemHandler,
    readonly,
}: SystemsListProps) => {
    return (
        <>
            {isLoadingInProgress ? (
                <DivLoaderContainer>
                    <AreaSpinner />
                </DivLoaderContainer>
            ) : (
                systems &&
                systems.map((system, index) => (
                    <SystemListItem
                        readonly={readonly}
                        key={`system-${system.templateId}-${system.systemId}`}
                        system={system}
                        templateId={templateId}
                        index={index}
                        isLastItem={index === systems.length - 1}
                        onDelete={deleteSystemHandler}
                        onChangeIsHide={hideSystemHandler}
                        onMove={moveSystemHandler}
                    />
                ))
            )}
        </>
    );
};
const DivLoaderContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    alignContent: 'center',
    width: '100%',
    height: 'calc(100vh - 320px)!important',
});
