import { IconButton, InputAdornment, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import TemplatesSettingsAPI from 'api/settings/InspectionForms/TemplatesSettings';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import Checkbox from 'common/components/Inputs/Checkbox';
import Dropdown from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import TextField from 'common/components/Inputs/TextField';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { IDefaultErrorResponse } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { IconSize } from 'common/styles/IconSize';
import { InspectionFormTemplate } from 'datacontracts/InspectionForms/InspectionFormTemplate';
import { TemplateSaveRequest } from 'datacontracts/InspectionForms/TemplateSaveRequest';
import { useEffect, useMemo, useState } from 'react';
import { Trans } from 'react-i18next';
import styles from './css.module.css';
import { TemplatesFormProps } from './props';

export const TemplatesForm = ({
    onSelectTemplate,
    onEditModeChange,
    readonly,
}: TemplatesFormProps) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [templates, setTemplates] = useState<InspectionFormTemplate[]>([]);
    const [showTemplateDeleteConfirmation, setShowTemplateDeleteConfirmation] =
        useState<boolean>(false);
    const [createTemplateMode, setCreateTemplateMode] = useState<boolean>(false);
    const [updateName, setUpdateName] = useState<string | null>(null);
    const [templateToCreate, setTemplateToCreate] = useState<TemplateSaveRequest | null>(null);
    const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);

    const {
        data,
        isError,
        isLoading: isFetching,
        refetch,
    } = useQuery(['inspection-forms', 'templates'], async () => TemplatesSettingsAPI.list());

    const selectedTemplate = useMemo(
        () =>
            templates.length
                ? templates.find((t) => t.inspectionFormTemplateId === selectedTemplateId) ??
                  templates[0]
                : null,
        [templates, selectedTemplateId]
    );

    useEffect(() => {
        if (!data) {
            return;
        }

        setTemplates(data.templates);

        const selection =
            data.templates.find((r) => r.inspectionFormTemplateId === selectedTemplateId) ??
            data.templates[0];

        if (!selection) {
            return;
        }

        onSelectTemplate?.(selection);
    }, [data, selectedTemplateId, onSelectTemplate]);

    useEffect(() => {
        if (isError) {
            toasters.warning(t('toasters.errorOccurredWhenLoading'));
        }
    }, [isError, t, toasters]);

    const createMutation = useMutation(
        async () => {
            if (templateToCreate) {
                return await TemplatesSettingsAPI.save(templateToCreate);
            }
        },
        {
            onError: (err: IDefaultErrorResponse) => {
                toasters.warning(err.data.message);
            },
            onSuccess: async (data) => {
                if (!data) {
                    return;
                }

                setSelectedTemplateId(data.inspectionFormTemplateId);
                await refetch();

                toasters.success(
                    t('inspectionForms.templates.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.templates.notifications.successfullyUpdatedTitle')
                );

                setCreateTemplateMode(false);
                setTemplateToCreate(null);
            },
        }
    );

    const updateMutation = useMutation(
        async (body: Partial<TemplateSaveRequest>) => {
            if (!selectedTemplate) {
                return;
            }

            const saveRequest = {
                templateId: selectedTemplate.inspectionFormTemplateId,
                templateName: selectedTemplate.inspectionFormTemplateName,
                showDetails: selectedTemplate.showDetails,
                displayInPdf: selectedTemplate.displayInPdf,
            };

            return await TemplatesSettingsAPI.save({ ...saveRequest, ...body });
        },
        {
            onError: (err: IDefaultErrorResponse) => {
                toasters.warning(err.data.message);
            },
            onSuccess: async (data) => {
                if (!data) {
                    return;
                }

                setSelectedTemplateId(data.inspectionFormTemplateId);
                await refetch();

                toasters.success(
                    t('inspectionForms.templates.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.templates.notifications.successfullyUpdatedTitle')
                );

                if (updateName) {
                    setUpdateName(null);
                }
            },
        }
    );

    const deleteMutation = useMutation(
        async () => {
            if (
                selectedTemplate &&
                !isLoading &&
                !createMutation.isLoading &&
                !updateMutation.isLoading
            ) {
                return await TemplatesSettingsAPI.delete_(
                    selectedTemplate.inspectionFormTemplateId
                );
            }
        },
        {
            onError: (err: IDefaultErrorResponse) => {
                toasters.warning(err.data.message);
            },
            onSuccess: async () => {
                setSelectedTemplateId(null);
                await refetch();

                toasters.success(
                    t('inspectionForms.templates.notifications.successfullyDeleteBody'),
                    t('inspectionForms.templates.notifications.successfullyDeleteTitle')
                );

                setCreateTemplateMode(false);
                setTemplateToCreate(null);
                setUpdateName(null);
            },
        }
    );

    const handleCreateNewTemplate = () => {
        setCreateTemplateMode(true);
        setTemplateToCreate({
            templateId: null,
            templateName: '',
            showDetails: true,
            displayInPdf: true,
        });
    };

    const changeTemplateSelectedHandler = (option: OptionData<number> | null) => {
        if (!option) {
            return;
        }

        console.debug(option);
        const selection = templates.find(
            (template) => template.inspectionFormTemplateId === option.value
        );
        if (!selection) return;
        if (onSelectTemplate) onSelectTemplate(selection);
        setSelectedTemplateId(selection.inspectionFormTemplateId);
    };

    const editNameHandler = () => {
        if (!selectedTemplate) return;

        setUpdateName(selectedTemplate.inspectionFormTemplateName);
    };

    const changeNameHandler = (name: string) => {
        createTemplateMode
            ? templateToCreate &&
              setTemplateToCreate({
                  ...templateToCreate,
                  templateName: name,
              })
            : setUpdateName(name);
    };

    const updateNameHandler = () => {
        if (createTemplateMode) {
            createMutation.mutateAsync();
        } else if (updateName && updateName !== selectedTemplate?.inspectionFormTemplateName) {
            updateMutation.mutateAsync({ templateName: updateName });
        }
    };

    const cancelUpdateNameHandler = () => {
        if (isLoading) return;
        if (createTemplateMode) {
            setCreateTemplateMode(false);
            setTemplateToCreate(null);
        } else {
            setUpdateName(null);
        }
    };

    const updateCheckboxHandler = (
        update: Partial<Omit<TemplateSaveRequest, 'templateId' | 'templateName'>>
    ) => {
        if (isLoading) return;

        if (createTemplateMode) {
            templateToCreate &&
                setTemplateToCreate({
                    ...templateToCreate,
                    ...update,
                });
        } else {
            updateMutation.mutateAsync(update);
        }
    };

    useEffect(() => {
        setSelectedTemplateId(null);
        refetch();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const isLoading =
        isFetching ||
        createMutation.isLoading ||
        updateMutation.isLoading ||
        deleteMutation.isLoading ||
        false;

    return (
        <>
            <div>
                {createTemplateMode || updateName !== null ? (
                    <DivInputContainer>
                        <DivInputField>
                            {createTemplateMode && (
                                <LabelInputField>
                                    {t('inspectionForms.templates.nameField.label')}
                                </LabelInputField>
                            )}
                            <TextField
                                autoFocus
                                name={'template.field'}
                                placeholder={t('inspectionForms.templates.nameField.placeholder')}
                                type={'text'}
                                isRequired={false}
                                value={
                                    createTemplateMode ? templateToCreate?.templateName : updateName
                                }
                                readonly={isLoading}
                                onChange={(event) => {
                                    changeNameHandler(event.target.value);
                                }}
                                onEnterPress={updateNameHandler}
                                enableEnterComplete={true}
                                showLoader={isLoading}
                                endAdornment={
                                    <InputAdornment position="end">
                                        <IconButton onClick={updateNameHandler}>
                                            <CheckIcon size={IconSize.M} />
                                        </IconButton>
                                    </InputAdornment>
                                }
                            />
                        </DivInputField>
                        <DivInputOptions>
                            <Button
                                color={'var(--neutral3)'}
                                cmosVariant={'typography'}
                                Icon={CloseIcon}
                                label=""
                                onClick={cancelUpdateNameHandler}
                            />
                        </DivInputOptions>
                    </DivInputContainer>
                ) : (
                    <DivTemplatesOptions>
                        <DivTemplatesOptionsSelector>
                            <Dropdown
                                name={'selectedTemplate'}
                                cmosVariant="roundedPrimary"
                                value={
                                    selectedTemplate
                                        ? {
                                              label: getTemplateName(selectedTemplate),
                                              value: selectedTemplate.inspectionFormTemplateId,
                                          }
                                        : null
                                }
                                options={templates.map((template) => {
                                    return {
                                        label: getTemplateName(template),
                                        value: template.inspectionFormTemplateId,
                                    };
                                })}
                                CustomHeaderMenu={() => {
                                    return (
                                        <DivButton>
                                            <Button
                                                cmosVariant={'stroke'}
                                                color={'var(--neutral3)'}
                                                label={t('inspectionForms.templates.addButton')}
                                                Icon={PlusIcon}
                                                onClick={handleCreateNewTemplate}
                                                iconPosition="right"
                                            />
                                        </DivButton>
                                    );
                                }}
                                onChange={changeTemplateSelectedHandler}
                            />
                        </DivTemplatesOptionsSelector>
                        {selectedTemplate && (
                            <DivTemplatesOptionsOptions>
                                <Button
                                    disabled={readonly}
                                    color={'var(--neutral3)'}
                                    cmosVariant={'typography'}
                                    Icon={EditIcon}
                                    label=""
                                    onClick={editNameHandler}
                                />
                                <Button
                                    disabled={readonly}
                                    color={'var(--neutral3)'}
                                    cmosVariant={'typography'}
                                    Icon={DeleteIcon}
                                    label=""
                                    onClick={() => {
                                        setShowTemplateDeleteConfirmation(true);
                                    }}
                                />
                            </DivTemplatesOptionsOptions>
                        )}
                    </DivTemplatesOptions>
                )}
                <DivCheckboxContainer>
                    <Checkbox
                        onChange={(_, checked: boolean) => {
                            updateCheckboxHandler({
                                showDetails: checked,
                            });
                        }}
                        className={styles.checkbox}
                        value={true}
                        name={'requestSaveTemplate.showDetails'}
                        checked={
                            createTemplateMode
                                ? !!templateToCreate && templateToCreate.showDetails
                                : !!selectedTemplate && selectedTemplate.showDetails
                        }
                    />
                    {t('inspectionForms.templates.showDetails')}
                </DivCheckboxContainer>
                <DivCheckboxContainer>
                    <Checkbox
                        onChange={(_, checked: boolean) => {
                            updateCheckboxHandler({
                                displayInPdf: checked,
                            });
                        }}
                        className={styles.checkbox}
                        value={true}
                        name={'requestSaveTemplate.displayInPdf'}
                        checked={
                            createTemplateMode
                                ? !!templateToCreate && templateToCreate.displayInPdf
                                : !!selectedTemplate && selectedTemplate.displayInPdf
                        }
                    />
                    {t('inspectionForms.templates.displayInPdf')}
                </DivCheckboxContainer>
            </div>

            {showTemplateDeleteConfirmation && (
                <DeleteConfirmationPopup
                    open={showTemplateDeleteConfirmation}
                    onClose={() => {
                        setShowTemplateDeleteConfirmation(false);
                    }}
                    onConfirm={async () => {
                        await deleteMutation.mutateAsync();
                        setShowTemplateDeleteConfirmation(false);
                    }}
                    title={t('inspectionForms.templates.confirmationDelete.title')}
                    body={
                        <Trans i18nKey="inspectionForms.templates.confirmationDelete.body" t={t} />
                    }
                    cancel={t('inspectionForms.templates.confirmationDelete.cancel')}
                    confirm={t('inspectionForms.templates.confirmationDelete.confirm')}
                />
            )}
        </>
    );
};

function getTemplateName(template: InspectionFormTemplate) {
    return `${template.inspectionFormTemplateName} ${
        template.enterpriseName ? `(${template.enterpriseName})` : ''
    }`;
}

const DivTemplatesOptions = styled('div')({
    display: 'flex',
});

const DivTemplatesOptionsSelector = styled('div')({
    width: 326,
});

const DivTemplatesOptionsOptions = styled('div')({
    marginLeft: 10,
    display: 'flex',
});

const DivInputContainer = styled('div')({
    display: 'flex',
});

const DivInputField = styled('div')({
    display: 'flex',
    width: 320,
    gap: 15,
    flexDirection: 'column',
});

const LabelInputField = styled('div')(({ theme }) => ({
    ...theme.typography.h5Roboto,
    color: theme.palette.neutral[7],
}));

const DivInputOptions = styled('div')({
    marginLeft: 5,
    display: 'flex',
    alignItems: 'flex-end',
});

const DivButton = styled('div')({
    marginLeft: 16,
    marginBottom: 7,
});

const DivCheckboxContainer = styled('div')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 400,
    color: theme.palette.neutral[6],
    marginTop: 15,
}));
