import { Box, styled } from '@mui/material';
import { TemplateItemsSettings } from 'api/settings/InspectionForms/TemplateItemsSettings';
import { IDefaultErrorResponse, useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { TemplateItemDto } from 'datacontracts/InspectionForms/TemplateItemDto';
import { useState } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { TemplateCommentsForm } from '../TemplateCommentsForm';
import { TemplateDetailsForm } from '../TemplateDetailsForm';
import { TemplateEstimateForm } from '../TemplateEstimateForm';
import { TemplateItemForm } from '../TemplateItemForm';
import { TemplateItemList } from '../TemplateItemList';

const reorder = (list: any[], startIndex: number, endIndex: number) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
};

type TemplatesListProps = {
    systemId: number;
    templateId: number;
    templateItems: TemplateItemDto[];
    readonly?: boolean;
};

export const TemplatesList = ({
    systemId,
    templateId,
    templateItems,
    readonly,
}: TemplatesListProps) => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const [list, setList] = useState(templateItems);
    const [selectedMasterItem, setSelectedMasterItem] = useState<TemplateItemDto>();
    const [showEditEstimatePopup, setShowEditEstimatePopup] = useState<boolean>(false);
    const [showEditDetailsPopup, setShowEditDetailsPopup] = useState<boolean>(false);
    const [showEditCommentsPopup, setShowEditCommentsPopup] = useState<boolean>(false);
    const { callApi, apiCallStatus } = useApiCall();
    const canPerformSave = apiCallStatus !== 'Pending';

    async function onDragEnd(result: any) {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;
        if (!result.destination) return;

        if (result.destination.index === result.source.index) {
            return;
        }

        const newList = reorder(list, result.source.index, result.destination.index);
        const templateItem = list[result.source.index];

        setList([...newList]);

        await callApi(
            async () =>
                TemplateItemsSettings.reorder(
                    templateItem.systemId,
                    templateItem.masterItemId,
                    result.destination.index
                ),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        toasters.success(
            t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
            t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle')
        );
    }
    const deleteHandler = ({ masterItemId, serialNumber }: TemplateItemDto) => {
        const newArray = list
            .map((s) => {
                if (s.serialNumber > serialNumber) {
                    return { ...s, serialNumber: s.serialNumber - 1 };
                } else {
                    return { ...s };
                }
            })
            .filter((s) => s.masterItemId !== masterItemId);
        setList([...newArray]);
    };

    const changeHandler = (templateItem: TemplateItemDto) => {
        const newList = list.map((item) =>
            item.masterItemId === templateItem.masterItemId ? { ...templateItem } : { ...item }
        );
        setList([...newList]);
    };
    const onSaveDetailsHandler = (details: string) => {
        toasters.success(
            t('inspectionForms.detailsForm.successfully.body'),
            t('inspectionForms.detailsForm.successfully.title')
        );
        setShowEditDetailsPopup(false);

        //update item row
        changeHandler({ ...selectedMasterItem!, details });
    };
    const onSaveEstimateHandler = (hasEditedEstimate: boolean) => {
        toasters.success(
            t('inspectionForms.estimateForm.successfully.body'),
            t('inspectionForms.estimateForm.successfully.title')
        );
        setShowEditEstimatePopup(false);

        //update item row
        changeHandler({ ...selectedMasterItem!, hasEditedEstimate });
    };
    const onSaveCommentsHandler = (anythingChanged: boolean, commentsCount: number) => {
        if (anythingChanged) {
            toasters.success(
                t('inspectionForms.commentsForm.successfully.body'),
                t('inspectionForms.commentsForm.successfully.title')
            );
        }
        setShowEditCommentsPopup(false);

        //update item row
        changeHandler({ ...selectedMasterItem!, commentsCount });
    };
    const editEstimateHandler = (templateItem: TemplateItemDto) => {
        setSelectedMasterItem({ ...templateItem });
        setShowEditEstimatePopup(true);
    };
    const editDetailsHandler = (templateItem: TemplateItemDto) => {
        setSelectedMasterItem({ ...templateItem });
        setShowEditDetailsPopup(true);
    };
    const editCommentsHandler = (templateItem: TemplateItemDto) => {
        setSelectedMasterItem({ ...templateItem });
        setShowEditCommentsPopup(true);
    };
    const addNewItemHandler = (item: TemplateItemDto) => {
        setList([...list, { ...item }]);
    };

    return (
        <>
            <StyledTemplatesList>
                <DragDropContext onDragEnd={onDragEnd}>
                    <Droppable droppableId={`list-${systemId}`}>
                        {(provided) => (
                            <div ref={provided.innerRef} {...provided.droppableProps}>
                                {list.map((templateItem: TemplateItemDto, index: number) => (
                                    <Draggable
                                        draggableId={templateItem.masterItemId.toString()}
                                        index={index}
                                        key={`template-item-${templateItem.masterItemId}`}
                                    >
                                        {(provided) => (
                                            <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                            >
                                                <TemplateItemList
                                                    templateId={templateId}
                                                    readonly={readonly}
                                                    templateItem={templateItem}
                                                    dragOptions={
                                                        provided.dragHandleProps ?? undefined
                                                    }
                                                    onDelete={deleteHandler}
                                                    onChange={changeHandler}
                                                    onEditEstimate={editEstimateHandler}
                                                    onEditDetails={editDetailsHandler}
                                                    onCommentsEdit={editCommentsHandler}
                                                />
                                            </div>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </div>
                        )}
                    </Droppable>
                </DragDropContext>
            </StyledTemplatesList>
            <Box sx={{ marginTop: '20px' }}>
                <TemplateItemForm
                    readonly={readonly}
                    onSaveSystem={addNewItemHandler}
                    systemId={systemId}
                    templateId={templateId}
                />
            </Box>
            {showEditEstimatePopup && (
                <TemplateEstimateForm
                    open={showEditEstimatePopup}
                    onSave={(hasEditedEstimate) => onSaveEstimateHandler(hasEditedEstimate)}
                    onClose={() => setShowEditEstimatePopup(false)}
                    templateItem={selectedMasterItem}
                />
            )}
            {showEditDetailsPopup && (
                <TemplateDetailsForm
                    open={showEditDetailsPopup}
                    onSave={(details) => onSaveDetailsHandler(details)}
                    onClose={() => setShowEditDetailsPopup(false)}
                    templateItem={selectedMasterItem!}
                />
            )}
            {showEditCommentsPopup && (
                <TemplateCommentsForm
                    open={showEditCommentsPopup}
                    onSave={onSaveCommentsHandler}
                    templateItem={selectedMasterItem}
                />
            )}
        </>
    );
};

const StyledTemplatesList = styled('div')(({ theme }) => ({
    background: theme.palette.neutral[2],
    borderRadius: 12,
    padding: 11,
    margin: '0px 18px',
}));
