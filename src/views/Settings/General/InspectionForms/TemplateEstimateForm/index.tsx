import { styled } from '@mui/material';
import { TemplateItemEstimatesSettings } from 'api/settings/InspectionForms/TemplateItemEstimatesSettings';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectSettings } from 'store/slices/globalSettingsSlice/selectors';
import { selectUserPermission } from 'store/slices/user/selectors';
import { InternationalizationLogic } from '../../../../../business/InternationalizationLogic';
import { Button } from '../../../../../common/components/Button';
import TextField from '../../../../../common/components/Inputs/TextField';
import { Modal } from '../../../../../common/components/Modal';
import { IDefaultErrorResponse, useApiCall } from '../../../../../common/hooks';
import { Colors } from '../../../../../common/styles/Colors';
import { TemplateItemEstimateDto } from '../../../../../datacontracts/InspectionForms/TemplateItemEstimateDto';
import { TemplateEstimateFormProps } from './props';

export const TemplateEstimateForm = ({
    open,
    onClose,
    templateItem,
    onSave,
}: TemplateEstimateFormProps) => {
    const { internationalization, repairShopSettings } = useSelector(selectSettings);
    const { currency } = internationalization;
    const currencySymbol = useMemo(() => currency.replace('{0}', ''), [currency]);

    const requireDecimals = repairShopSettings?.features.enableRemoveDecimals ? false : true;
    const { allowEditEstimates: isEditingEnabled } = useSelector(selectUserPermission);
    const { t } = useAppTranslation();
    const { callApi, apiCallStatus } = useApiCall();
    const [quantity, setQuantity] = useState<any>();
    const [partUnitPrice, setPartUnitPrice] = useState<any>();
    const [hourUnitPrice, setHourUnitPrice] = useState<any>();
    const [hours, setHours] = useState<any>();
    const [totals, setTotals] = useState<{
        total: number;
        subtotal: number;
        taxes: number;
    }>();
    const canPerformSave = apiCallStatus !== 'Pending';

    const fetchLoadEstimate = useCallback(async () => {
        if (!templateItem?.masterItemId) return;

        const response = await callApi(
            async () => TemplateItemEstimatesSettings.get(templateItem.masterItemId),
            {
                selectErrorContent: (response: IDefaultErrorResponse) => ({
                    body: response.data.message,
                }),
            }
        );

        setQuantity(response.quantity);
        setHourUnitPrice(response.hourUnitPrice);
        setHours(response.hours);
        setPartUnitPrice(response.partUnitPrice);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [templateItem?.masterItemId]);

    const setQuantityHandler = (event: ChangeEvent<HTMLInputElement>) => {
        setQuantity(parseFloat(event.target.value));
    };
    const setHoursHandler = (event: ChangeEvent<HTMLInputElement>) => {
        setHours(parseFloat(event.target.value));
    };
    const setPartUnitPriceHandler = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value.replace(currencySymbol, '').replace(/[^\d.]/g, '');
        setPartUnitPrice(value);
    };
    const setHourUnitPriceHandler = (event: ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value.replace(currencySymbol, '').replace(/[^\d.]/g, '');
        setHourUnitPrice(value);
    };
    const cleanHandler = () => {
        setQuantity('');
        setHourUnitPrice('');
        setHours('');
        setPartUnitPrice('');
    };
    const saveHandler = async () => {
        //Is it protection from users who clicks too fast? Not sure, but I prefer to stay it as is for now
        if (!canPerformSave) return;
        if (!templateItem?.masterItemId) return;

        const newEstimate: TemplateItemEstimateDto = {
            repairMasterListId: templateItem.masterItemId,
            quantity,
            partUnitPrice,
            hourUnitPrice,
            hours,
        };

        await callApi(async () => TemplateItemEstimatesSettings.save(newEstimate), {
            selectErrorContent: (response: IDefaultErrorResponse) => ({
                body: response.data.message,
            }),
        });

        if (onSave) onSave(!!quantity || !!partUnitPrice || !!hourUnitPrice || !!hours);
    };

    const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
        const { key } = event;
        if (key === 'e' || key === '+' || key === '-') {
            event.preventDefault();
        }

        if (!requireDecimals && key === '.') {
            event.preventDefault();
        }
    };
    useEffect(() => {
        let subtotal: number = 0.0;
        let total: number = 0.0;
        let taxes: number = 0.0;
        if (hourUnitPrice && hours) {
            subtotal = hours * hourUnitPrice;
        }
        if (quantity && partUnitPrice) {
            subtotal += quantity * partUnitPrice;
        }

        if (subtotal) {
            if (repairShopSettings?.taxPercentage) {
                taxes = subtotal * (repairShopSettings.taxPercentage / 100);
            }
            total = subtotal + taxes;
        }
        setTotals({ total, taxes, subtotal });
    }, [hourUnitPrice, hours, partUnitPrice, quantity, repairShopSettings?.taxPercentage]);

    useEffect(() => {
        fetchLoadEstimate();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [templateItem?.masterItemId]);

    return (
        <>
            <Modal open={open}>
                <StyledContent>
                    <StyledRow sx={{ marginBottom: '37px' }}>
                        <StyledTitle>
                            {t('inspectionForms.estimateForm.title', {
                                item: templateItem?.name ?? '',
                            })}
                        </StyledTitle>
                        <StyledButtonsContainer>
                            <StyledButtonCancel
                                color={Colors.Neutral3}
                                cmosSize={'medium'}
                                label={t('commonLabels.cancel')}
                                onClick={onClose}
                                disabled={apiCallStatus === 'Pending'}
                                cmosVariant={'filled'}
                            />
                            <StyledButtonSuccess
                                color={Colors.Success}
                                cmosSize={'medium'}
                                label={t('commonLabels.save')}
                                disabled={apiCallStatus === 'Pending' || !isEditingEnabled}
                                onClick={saveHandler}
                                showLoader={apiCallStatus === 'Pending'}
                                cmosVariant={'filled'}
                            />
                        </StyledButtonsContainer>
                    </StyledRow>
                    <StyledRow sx={{ marginBottom: '15px' }}>
                        <StyledColInputs sx={{ marginRight: '17px' }}>
                            <StyledRow>
                                <div className="label">
                                    {t('inspectionForms.estimateForm.fieldQuantity.label')}
                                </div>
                                <div className="input">
                                    <TextField
                                        type="number"
                                        name={'fieldQuantity'}
                                        value={quantity}
                                        disabled={!isEditingEnabled}
                                        onChange={setQuantityHandler}
                                        placeholder={t(
                                            'inspectionForms.estimateForm.fieldQuantity.placeholder'
                                        )}
                                        readonly={apiCallStatus === 'Pending'}
                                        onKeyDown={handleKeyPress}
                                    />
                                </div>
                            </StyledRow>
                        </StyledColInputs>
                        <StyledColInputs>
                            <StyledRow>
                                <div className="label">
                                    {t('inspectionForms.estimateForm.fieldHours.label')}
                                </div>
                                <div className="input">
                                    <TextField
                                        type="number"
                                        name={'fieldHours'}
                                        value={hours}
                                        disabled={!isEditingEnabled}
                                        onChange={setHoursHandler}
                                        placeholder={t(
                                            'inspectionForms.estimateForm.fieldHours.placeholder'
                                        )}
                                        readonly={apiCallStatus === 'Pending'}
                                        onKeyDown={handleKeyPress}
                                    />
                                </div>
                            </StyledRow>
                        </StyledColInputs>
                    </StyledRow>
                    <StyledRow sx={{ marginBottom: '15px' }}>
                        <StyledColInputs sx={{ marginRight: '17px' }}>
                            <StyledRow>
                                <div className="label">
                                    {t('inspectionForms.estimateForm.fieldUnitPrice.label')}
                                </div>
                                <div className="input">
                                    <TextField
                                        name={'fieldUnitPrice'}
                                        value={
                                            Boolean(partUnitPrice)
                                                ? `${currencySymbol}${partUnitPrice}`
                                                : ''
                                        }
                                        disabled={!isEditingEnabled}
                                        onChange={setPartUnitPriceHandler}
                                        placeholder={t(
                                            'inspectionForms.estimateForm.fieldUnitPrice.placeholder'
                                        )}
                                        readonly={apiCallStatus === 'Pending'}
                                        onKeyDown={handleKeyPress}
                                    />
                                </div>
                            </StyledRow>
                        </StyledColInputs>
                        <StyledColInputs>
                            <StyledRow>
                                <div className="label">
                                    {t('inspectionForms.estimateForm.field$Hours.label')}
                                </div>
                                <div className="input">
                                    <TextField
                                        name={'field$Hours'}
                                        value={
                                            Boolean(hourUnitPrice)
                                                ? `${currencySymbol}${hourUnitPrice}`
                                                : ''
                                        }
                                        disabled={!isEditingEnabled}
                                        onChange={setHourUnitPriceHandler}
                                        placeholder={t(
                                            'inspectionForms.estimateForm.field$Hours.placeholder'
                                        )}
                                        readonly={apiCallStatus === 'Pending'}
                                        onKeyDown={handleKeyPress}
                                    />
                                </div>
                            </StyledRow>
                        </StyledColInputs>
                    </StyledRow>
                    <StyledRow>
                        <StyledColInputs>
                            {repairShopSettings?.taxPercentage && (
                                <>
                                    <Divider />
                                    <StyledRow sx={{ marginBottom: '15px' }}>
                                        <div className={'label'}>
                                            {t('inspectionForms.estimateForm.subtotal')}:
                                        </div>
                                        <div className={'label'}>
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                totals?.subtotal,
                                                { allowZero: true }
                                            )}
                                        </div>
                                    </StyledRow>
                                    <StyledRow>
                                        <div className={'label'}>
                                            {t('inspectionForms.estimateForm.taxes')}:
                                        </div>
                                        <div className={'label'}>
                                            {InternationalizationLogic.numberToCurrency(
                                                internationalization,
                                                totals?.taxes,
                                                { allowZero: true }
                                            )}
                                        </div>
                                    </StyledRow>
                                </>
                            )}
                            <Divider />
                            <StyledRow>
                                <div className={'label'}>
                                    {t('inspectionForms.estimateForm.total')}:
                                </div>
                                <div className={'label'}>
                                    {InternationalizationLogic.numberToCurrency(
                                        internationalization,
                                        totals?.total,
                                        {
                                            allowZero: true,
                                        }
                                    )}
                                </div>
                            </StyledRow>
                        </StyledColInputs>
                        <StyledColClear>
                            <StyledButtonClean
                                color={Colors.CM1}
                                cmosSize={'medium'}
                                disabled={!isEditingEnabled}
                                label={t('inspectionForms.estimateForm.cleanButton')}
                                onClick={cleanHandler}
                                cmosVariant={'stroke'}
                            />
                        </StyledColClear>
                    </StyledRow>
                </StyledContent>
            </Modal>
        </>
    );
};

const StyledContent = styled('div')({
    width: 961 - 85 - 86,
    height: 423 - 47 - 70,
    marginTop: 47,
    marginBottom: 70,
    marginRight: 85,
    marginLeft: 86,
});

const StyledRow = styled('div')({
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
});

const StyledTitle = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    ...theme.typography.h4Inter,
    fontWeight: 'bold',
    color: theme.palette.neutral[7],
}));

const StyledButtonsContainer = styled('div')({
    width: 443,
    display: 'flex',
    justifyContent: 'flex-end',
});

const StyledButtonCancel = styled(Button)({
    width: 164,
    marginRight: 12,
});
const StyledButtonSuccess = styled(Button)({
    width: 227,
});
const StyledButtonClean = styled(Button)({
    width: 164,
});

const StyledColClear = styled('div')({
    width: 375,
    marginTop: 20,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
});

const StyledColInputs = styled('div')(({ theme }) => ({
    width: 375,
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'column',
    '& .input': {
        width: 282,
    },
    '& .label': {
        ...theme.typography.h6Roboto,
        fontWeight: 'normal',
        color: theme.palette.neutral[8],
        display: 'flex',
        alignItems: 'center',
    },
}));

const Divider = styled('div')({
    width: '100%',
    minHeight: 1,
    borderTop: '1px solid #EFEFEF',
    marginTop: 20,
    marginBottom: 22,
});
