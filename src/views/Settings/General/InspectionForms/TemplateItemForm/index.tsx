import { styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { TemplateItemsSettings } from 'api/settings/InspectionForms/TemplateItemsSettings';
import { isAxiosError } from 'axios';
import { Button } from 'common/components/Button';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useState } from 'react';
import InspectionItemsAutocomplete from 'views/Components/InspectionItemsAutocomplete';
import { TemplateItemFormProps } from './props';

export const TemplateItemForm = ({
    onSaveSystem,
    systemId,
    templateId,
    readonly,
}: TemplateItemFormProps) => {
    //#region states
    const { t } = useAppTranslation();
    const [inputMode, setInputMode] = useState<boolean>(false);
    const toasters = useToasters();

    const saveMutation = useMutation(TemplateItemsSettings.save, {
        onError: (error) => {
            if (isAxiosError<{ message: string }>(error) && error.response?.data.message) {
                toasters.danger(error.response.data.message, t('toasters.errorOccurred'));
            } else {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
        },
    });

    const fetchSave = async (value: string) => {
        const response = await saveMutation.mutateAsync({
            systemId: systemId,
            masterItemId: null,
            name: value,
        });

        if (!response) return;
        if (onSaveSystem) onSaveSystem(response);

        toasters.success(
            t('inspectionForms.templateItems.notifications.successfullyUpdatedBody'),
            t('inspectionForms.templateItems.notifications.successfullyUpdatedTitle')
        );

        setInputMode(false);
    };

    return (
        <>
            {!inputMode && systemId && (
                <AddButtonContainer>
                    <Button
                        disabled={readonly}
                        cmosVariant={'stroke'}
                        iconPosition="right"
                        Icon={PlusIcon}
                        color={Colors.Neutral3}
                        onClick={() => {
                            setInputMode(true);
                        }}
                        label={t('inspectionForms.templateItems.addButton')}
                    />
                </AddButtonContainer>
            )}
            {inputMode && (
                <FormContainer>
                    <InputContainer>
                        <div className="field">
                            <InspectionItemsAutocomplete
                                templateId={templateId}
                                isLoading={saveMutation.isLoading}
                                onChange={(value) => fetchSave(value)}
                            />
                        </div>
                        <div className="options">
                            <Button
                                color={Colors.Neutral3}
                                cmosVariant={'typography'}
                                Icon={CloseIcon}
                                label=""
                                onClick={() => {
                                    if (saveMutation.isLoading) return;
                                    setInputMode(false);
                                }}
                            />
                        </div>
                    </InputContainer>
                </FormContainer>
            )}
        </>
    );
};

const AddButtonContainer = styled('div')({
    width: 189,
    padding: '0 18px',
    '& button>div': {
        width: 171,
        justifyContent: 'space-between',
        display: 'flex',
    },
});

const FormContainer = styled('div')(({ theme }) => ({
    '& .showDetail': {
        ...theme.typography.h6Roboto,
        fontWeight: 'normal',
        color: theme.palette.neutral[5],
    },
    padding: '0 18px',
}));

const InputContainer = styled('div')({
    width: 320,
    display: 'flex',
    '& .field': {
        width: 320,
    },
    '& .options': {},
});
