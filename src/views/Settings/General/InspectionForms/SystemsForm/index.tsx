import { IconButton, InputAdornment, styled } from '@mui/material';
import { SystemsSettingsAPI } from 'api/settings/InspectionForms/SystemsSettings';
import { Button } from 'common/components/Button';
import { CheckIcon } from 'common/components/Icons/CheckIcon';
import { CloseIcon } from 'common/components/Icons/CloseIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import TextField from 'common/components/Inputs/TextField';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { SystemSaveRequest } from 'datacontracts/InspectionForms/SystemSaveRequest';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setNewToaster } from 'store/actions/toasters.action';
import { SystemsFormProps } from './props';

export const SystemsForm = ({ system, onSaveSystem, templateId, readonly }: SystemsFormProps) => {
    const dispatch = useDispatch();
    const { t } = useAppTranslation();
    const [inputMode, setInputMode] = useState<boolean>(false);
    const [requestSaveSystem, setRequestSaveSystem] = useState<SystemSaveRequest>();
    const { callApi, apiCallStatus } = useApiCall();

    const fetchSave = async () => {
        if (!requestSaveSystem) return;

        const response = await callApi(async () => SystemsSettingsAPI.save(requestSaveSystem), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenSaving'),
            }),
        });

        if (!response) return;
        if (onSaveSystem) onSaveSystem(response);

        if (!requestSaveSystem.systemId) {
            setRequestSaveSystem({
                templateId: templateId,
                isHidden: false,
                systemName: '',
            });
        }

        dispatch(
            setNewToaster(
                new NotificationData(
                    t('inspectionForms.systems.notifications.successfullyUpdatedBody'),
                    t('inspectionForms.systems.notifications.successfullyUpdatedTitle'),
                    NotificationType.success
                )
            )
        );

        setInputMode(false);
    };

    useEffect(() => {
        if (system) {
            setRequestSaveSystem({
                systemId: system?.systemId,
                templateId: templateId,
                isHidden: system?.isHidden,
                systemName: system.systemName,
            });
        } else {
            setRequestSaveSystem({
                templateId: templateId,
                isHidden: false,
                systemName: '',
            });
        }
    }, [system, templateId]);

    return (
        <>
            {!inputMode && requestSaveSystem && !requestSaveSystem.systemId && (
                <div style={{ width: 212, display: 'flex' }}>
                    <Button
                        disabled={readonly}
                        color={Colors.CM1}
                        cmosVariant={'filled'}
                        blockMode
                        label={t('inspectionForms.systems.addButton')}
                        onClick={() => {
                            setInputMode(true);
                        }}
                    />
                </div>
            )}
            {!inputMode && requestSaveSystem?.systemId && (
                <DivButtonEdit>
                    <div className="title">
                        {t('inspectionForms.systems.title')}: {requestSaveSystem.systemName}
                    </div>
                    <div className="editButton">
                        <Button
                            disabled={readonly}
                            color={Colors.Neutral3}
                            cmosVariant={'typography'}
                            label={''}
                            Icon={EditIcon}
                            onClick={() => {
                                setInputMode(true);
                            }}
                        />
                    </div>
                </DivButtonEdit>
            )}
            {inputMode && (
                <DivForm>
                    <DivInputContainer>
                        <div className="field">
                            <TextField
                                autoFocus
                                disabled={readonly}
                                name={'system.field'}
                                label={''}
                                placeholder={t('inspectionForms.systems.nameField.placeholder')}
                                type={'text'}
                                isRequired={false}
                                value={requestSaveSystem?.systemName}
                                readonly={apiCallStatus === 'Pending'}
                                onChange={(event) => {
                                    requestSaveSystem &&
                                        setRequestSaveSystem({
                                            ...requestSaveSystem,
                                            systemName: event.target.value,
                                        });
                                }}
                                onEnterPress={fetchSave}
                                enableEnterComplete={true}
                                cmosVariant="roundedGrey"
                                showLoader={apiCallStatus === 'Pending'}
                                endAdornment={
                                    <InputAdornment position="end">
                                        <IconButton onClick={fetchSave}>
                                            <CheckIcon size={IconSize.M} />
                                        </IconButton>
                                    </InputAdornment>
                                }
                            />
                        </div>
                        <div className="options">
                            <Button
                                disabled={readonly}
                                color={Colors.Neutral3}
                                cmosVariant={'typography'}
                                Icon={CloseIcon}
                                label=""
                                onClick={() => {
                                    if (apiCallStatus === 'Pending') return;
                                    setInputMode(false);
                                }}
                            />
                        </div>
                    </DivInputContainer>
                </DivForm>
            )}
        </>
    );
};

const DivButtonEdit = styled('div')(({ theme }) => ({
    display: 'flex',
    gap: 8,
    alignItems: 'center',
    '& .title': {
        width: 'max-content',
    },
}));

const DivInputContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    '& .field': {
        width: 320,
    },
    '& .options': {},
}));

const DivForm = styled('div')(({ theme }) => ({
    '& .showDetail': {
        ...theme.typography.h6Roboto,
        fontWeight: 'normal',
        color: theme.palette.neutral[5],
    },
}));
