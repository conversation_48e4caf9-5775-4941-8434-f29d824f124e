import GlobalSettingsDto from 'datacontracts/GlobalSettings';
import { GEN_SETTINGS_TABS } from '../../../common/constants';
import { AppointmentReasonsSettings } from './AppointmentReasons';
import AppointmentsSettings from './Appointments';
import CustomizableFieldsSettings from './CustomizableFields';
import ImportCustomersSettings from './ImportCustomers';
import { InspectionFormsSettings } from './InspectionForms';
import { LocationInfoSettings } from './LocationInfo';
import Notifications from './Notifications';
import OrderPdf from './OrderPdf';
import OrderTypesSettings from './OrderTypes';
import Orders from './Orders';
import Packages from './Packages';
import PhasesSettings from './Phases';
import SiteForAppointmentsSettings from './SiteForAppointments';
import { TeamMembersSettings } from './TeamMembers';
import Terminologies from './Terminologies';

export type Tab = {
    section: string;
    label: string;
    component: React.ReactNode;
    isVisible: (glSettings: GlobalSettingsDto) => boolean;
};

export const Tabs: Tab[] = [
    {
        section: GEN_SETTINGS_TABS.LOCATION_INFO,
        label: 'settings.general.locationInfo',
        component: <LocationInfoSettings />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.TEAM_MEMBERS,
        label: 'settings.general.teamMembers',
        component: <TeamMembersSettings />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.ORDERS,
        label: 'settings.general.orders',
        component: <Orders />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.ORDER_PDF,
        label: 'settings.general.orderPdf',
        component: <OrderPdf />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.INSPECTION_FORMS,
        label: 'settings.general.inspectionForms',
        component: <InspectionFormsSettings />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.PACKAGES,
        label: 'settings.general.packages',
        component: <Packages />,
        isVisible: (gs) => true,
    },
    {
        section: GEN_SETTINGS_TABS.APPOINTMENTS,
        label: 'settings.general.appointments',
        component: <AppointmentsSettings />,
        isVisible: (gs) => true,
    },
    {
        section: GEN_SETTINGS_TABS.SITE_FOR_APPOINTMENTS,
        label: 'settings.general.siteForAppointments',
        component: <SiteForAppointmentsSettings />,
        isVisible: (gs) => gs.repairShopSettings!.features.isAppointmentSiteEnabled,
    },
    {
        section: GEN_SETTINGS_TABS.APPOINTMENT_REASONS,
        label: 'settings.general.appointmentReasons',
        component: <AppointmentReasonsSettings />,
        isVisible: (gs) => true,
    },
    {
        section: GEN_SETTINGS_TABS.ORDER_TYPES,
        label: 'settings.general.orderTypes',
        component: <OrderTypesSettings />,
        isVisible: (gs) => true,
    },
    {
        section: GEN_SETTINGS_TABS.PHASES,
        label: 'settings.general.phases',
        component: <PhasesSettings />,
        isVisible: (gs) => gs.appMode === 'RepairShop' || false,
    },
    {
        section: GEN_SETTINGS_TABS.NOTIFICATIONS,
        label: 'settings.notifications.header',
        component: <Notifications />,
        isVisible: () => true,
    },

    {
        section: GEN_SETTINGS_TABS.CUSTOMIZABLE_FIELDS,
        label: 'settings.customizableFields.header',
        component: <CustomizableFieldsSettings />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.TERMINOLOGIES,
        label: 'settings.general.terminologies',
        component: <Terminologies />,
        isVisible: () => true,
    },
    {
        section: GEN_SETTINGS_TABS.IMPORT_CUSTOMERS,
        label: 'settings.prospections.tabs.importCustomers',
        component: <ImportCustomersSettings />,
        isVisible: () => true,
    },
];
