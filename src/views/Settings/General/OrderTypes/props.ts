import { connect } from 'react-redux';
import { NotificationData } from './../../../../common/components/NotificationPull/NotificationData';
import { ExtractConnectType } from './../../../../store';
import * as ToastersActions from './../../../../store/actions/toasters.action';

const mapDispatchToProps = (dispatch: any) => ({
    setNewToaster: (newToaster: NotificationData) =>
        dispatch(ToastersActions.setNewToaster(newToaster)),
});

export const connector = connect(null, mapDispatchToProps);

type PropsFromRedux = ExtractConnectType<typeof connector>;

export type OrderTypesSettingsProps = PropsFromRedux;
