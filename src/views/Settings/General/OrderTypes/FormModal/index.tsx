import { IconButton } from '@mui/material';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { createSelector } from '@reduxjs/toolkit';
import { useMutation, useQuery } from '@tanstack/react-query';
import OrderTypeSettingsApi, {
    CreateOrderTypeDto,
    OrderType,
} from 'api/settings/OrderTypeSettings';
import { IconProps } from 'common/components/Icons/Icon';
import Dropdown from 'common/components/Inputs/Dropdown';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { OptionStyle } from 'common/styles/OptionStyle';
import { useEffect, useState } from 'react';
import { useAppSelector } from 'store';
import { selectSettings } from 'store/slices/globalSettingsSlice';
import { Button } from '../../../../../common/components/Button';
import { TextField } from '../../../../../common/components/Inputs';
import { Modal } from '../../../../../common/components/Modal';
import { Colors } from '../../../../../common/styles/Colors';
import useStyles from './css';
import { OrderTypeModalProps } from './props';

export const COLOR_OPTIONS: string[][] = [
    ['#6495ED', '#DA70D6', '#20B2AA', '#F0E68C', '#F08080'],
    ['#87CEEB', '#DDA0DD', '#98FB98', '#EEE8AA', '#FA8072'],
    ['#B0E0E6', '#E0B0FF', '#90EE90', '#FFFEA3', '#FFA07A'],
    ['#89CFF0', '#E29CD2', '#7FFFD4', '#FFFEA3', '#FFE5B4'],
    ['#ADD8E6', '#EE82EE', '#ACE1AF', '#FFFACD', '#FADADD'],
    ['#AFEEEE', '#DABFFF', '#D0F0C0', '#FFEBCD', '#F4C2C2'],
    ['#C1C6FC', '#D8BFD8', '#AAF0D1', '#FFDAB9', '#FFB6C1'],
    ['#CCCCFF', '#CCCCFF', '#BDFCC9', '#F5F5DC', '#FFC0CB'],
    ['#E0FFFF', '#E6E6FA', '#F0FFF0', '#FFEFD5', '#FFF0F5'],
    ['#F0F8FF', '#FFF0F5', '#F5FFFA', '#FAFAD2', '#FFE4E1'],
];

const selectShowExternalOrderType = createSelector(
    selectSettings,
    (settings) => settings.repairShopSettings?.features.vehicleReceptionIntegrationEnabled ?? false
);

const OrderTypeFormModal = ({ isOpen, value, onClose, onSave }: OrderTypeModalProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    const toaster = useToasters();
    const showExternalOrderType = useAppSelector(selectShowExternalOrderType);

    const getCleanOrderType = (): OrderType => {
        if (value) {
            return { ...value };
        }

        return {
            id: '',
            name: '',
            color: '',
            createdDate: '',
            updatedDate: '',
        };
    };

    const [orderType, setOrderType] = useState<OrderType>(getCleanOrderType());

    useEffect(() => {
        if (isOpen) refetch();
    }, [isOpen]);

    useEffect(() => {
        setOrderType(value || getCleanOrderType());

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const { data: usedColors, refetch } = useQuery(
        ['settings', 'order-types', 'used-colors'],
        () => OrderTypeSettingsApi.getUsedColors(),
        {
            cacheTime: Infinity,
            staleTime: 0,
            initialData: [],
        }
    );

    const updateOrderTypeMutation = useMutation(
        async (payload: OrderType) => await OrderTypeSettingsApi.updateOrderType(payload),
        {
            onSuccess: (data) => {
                toaster.success(
                    t('settings.orderTypes.orderTypeSavedSuccessfully'),
                    t('settings.orderTypes.orderTypeEdited')
                );
                setOrderType(getCleanOrderType());
                onSave && onSave({ ...data });
            },
            onError: ({ response }) => {
                const { status, data } = response;
                if (status === 409) {
                    const messageText =
                        data.meta.entity === 'OrderType'
                            ? t('settings.orderTypes.theOrderTypeIsDuplicated')
                            : t('settings.orderTypes.colorInUse');
                    toaster.danger(messageText, t('settings.orderTypes.orderTypeNotEdited'));
                } else {
                    toaster.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
        }
    );

    const createOrderTypeMutation = useMutation(
        async (payload: CreateOrderTypeDto) => await OrderTypeSettingsApi.createOrderType(payload),
        {
            onSuccess: (data) => {
                toaster.success(
                    t('settings.orderTypes.orderTypeSavedSuccessfully'),
                    t('settings.orderTypes.orderTypeCreated')
                );
                setOrderType(getCleanOrderType());
                onSave && onSave({ ...data });
            },
            onError: ({ response }) => {
                const { status, data } = response;
                if (status === 409) {
                    const messageText =
                        data.meta.entity === 'OrderType'
                            ? t('settings.orderTypes.theOrderTypeIsDuplicated')
                            : t('settings.orderTypes.colorInUse');
                    toaster.danger(messageText, t('settings.orderTypes.orderTypeNotEdited'));
                } else {
                    toaster.danger(
                        t('toasters.errorOccurredWhenSaving'),
                        t('toasters.errorOccurred')
                    );
                }
            },
        }
    );

    const handleOnSave = async () => {
        if (orderType.id === '') {
            createOrderTypeMutation.mutate({
                name: orderType.name,
                color: orderType.color,
                externalKey: orderType.externalKey,
            });
        } else {
            updateOrderTypeMutation.mutate(orderType);
        }
    };

    return (
        <Modal open={isOpen}>
            <Box className={classes.content}>
                <Box display={'flex'} flexDirection="row" justifyContent={'space-between'}>
                    <Typography className={classes.title}>
                        {t(
                            `settings.orderTypes.${
                                value && value.id ? 'editOrderType' : 'newOrderType'
                            }`
                        )}
                    </Typography>
                    <Box justifyContent="end" display="flex" flexDirection="row" gap={1.25}>
                        <Button
                            w={'md'}
                            color={Colors.Neutral3}
                            cmosVariant={'filled'}
                            label={t('commonLabels.cancel')}
                            onClick={() => {
                                setOrderType(getCleanOrderType());
                                onClose();
                            }}
                        />
                        <Button
                            w={'md'}
                            color={Colors.Success}
                            cmosVariant={'filled'}
                            label={t('settings.orderTypes.saveOrderType')}
                            disabled={enableSubmitButton(orderType, showExternalOrderType)}
                            onClick={handleOnSave}
                        />
                    </Box>
                </Box>
                <Grid
                    container
                    columnSpacing={showExternalOrderType ? 2 : 6}
                    style={{ marginTop: 50 }}
                >
                    <Grid item xs={showExternalOrderType ? 4 : 6}>
                        <TextField
                            label={t('settings.orderTypes.orderTypeCaption')}
                            placeholder={t('settings.orderTypes.enterOrderType')}
                            name="order-type"
                            isRequired
                            maxLength={32}
                            value={orderType.name}
                            onChange={(event) => {
                                setOrderType({
                                    ...orderType,
                                    name: event.target.value.trimStart(),
                                });
                            }}
                            showValidationIndicators
                        />
                    </Grid>
                    <Grid item xs={showExternalOrderType ? 4 : 6}>
                        <Dropdown<string>
                            name="color"
                            label={t('settings.orderTypes.orderTypeColor')}
                            placeholder={t('settings.orderTypes.orderTypeColorPlaceholder')}
                            optionStyle={OptionStyle.icons}
                            isSearchable={false}
                            cmosVariant="default"
                            value={
                                orderType.color
                                    ? {
                                          label: '',
                                          value: orderType.color,
                                          icon: CircleDiv,
                                          color: orderType.color,
                                      }
                                    : undefined
                            }
                            onChange={(event) => {
                                if (event === null) return;
                                setOrderType({
                                    ...orderType,
                                    color: event.value,
                                });
                            }}
                            CustomMenu={(props) => {
                                return (
                                    <div className={classes.colorsContainer}>
                                        {COLOR_OPTIONS.map((palette, optIdx) => (
                                            <div
                                                className={classes.colorsRow}
                                                key={`lst-${optIdx}`}
                                            >
                                                {palette.map((color) => (
                                                    <IconButton
                                                        key={color}
                                                        disableRipple={usedColors.includes(color)}
                                                        className={
                                                            usedColors.includes(color)
                                                                ? classes.disabledColor
                                                                : ''
                                                        }
                                                        onClick={() => {
                                                            if (usedColors.includes(color)) return;
                                                            props.selectOption({
                                                                label: '',
                                                                value: color,
                                                                icon: CircleDiv,
                                                                color: color,
                                                            });
                                                        }}
                                                        size="large"
                                                    >
                                                        <CircleDiv fill={color} />
                                                    </IconButton>
                                                ))}
                                            </div>
                                        ))}
                                    </div>
                                );
                            }}
                        />
                    </Grid>
                    {showExternalOrderType && (
                        <Grid item xs={4} paddingLeft={0}>
                            <TextField
                                label={t('settings.orderTypes.externalOrderTypeCaption')}
                                placeholder={t(
                                    'settings.orderTypes.externalOrderTypeCaptionPlaceholder'
                                )}
                                name="order-type"
                                isRequired={showExternalOrderType}
                                maxLength={32}
                                value={orderType.externalKey}
                                onChange={(event) => {
                                    setOrderType({
                                        ...orderType,
                                        externalKey: event.target.value,
                                    });
                                }}
                                showValidationIndicators
                            />
                        </Grid>
                    )}
                </Grid>
            </Box>
        </Modal>
    );
};

export default OrderTypeFormModal;

const enableSubmitButton = (orderType: OrderType, showExternalOrderType: boolean) => {
    if (showExternalOrderType) {
        return !orderType.name.trim() || !orderType.externalKey;
    }

    return !orderType.name.trim();
};

const CircleDiv = (props: IconProps) => (
    <div
        style={{
            height: 20,
            width: 20,
            backgroundColor: props.fill,
            border: props.fill === '#FFFFFF' ? '1px solid #DDDDDD' : undefined,
            borderRadius: '50%',
            display: 'inline-block',
        }}
    />
);
