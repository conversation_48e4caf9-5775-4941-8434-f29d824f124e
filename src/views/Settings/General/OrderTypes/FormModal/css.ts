import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    content: {
        width: 961,
        height: 281 - 47,
        paddingTop: 47,
        paddingLeft: 86,
        paddingRight: 86,
    },
    title: {
        ...theme.typography.h4Inter,
        color: theme.palette.neutral[7],
    },
    colorsContainer: {
        display: 'flex',
        flexDirection: 'column',
        height: 212,
        overflowY: 'scroll',
        position: 'relative',
        '&::-webkit-scrollbar': {
            width: '5px',
        },
        '&::-webkit-scrollbar-track': {
            background: theme.palette.neutral[4],
            borderRadius: '10px',
        },
        '&::-webkit-scrollbar-thumb': {
            background: theme.palette.neutral[6],
            borderRadius: '10px',
        },
    },
    colorsRow: {
        display: 'flex',
        flexDirection: 'row',
        '& button': {
            padding: 13,
        },
    },
    disabledColor: {
        cursor: 'default',
    },
}));

export default useStyles;
