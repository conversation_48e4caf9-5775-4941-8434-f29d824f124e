import { Box, Pagination } from '@mui/material';
import Grid from '@mui/material/Grid';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import OrderTypeSettingsApi, { OrderType } from 'api/settings/OrderTypeSettings';
import { TableHeadCell } from 'common/components';
import AreaSpinner from 'common/components/AreaSpinner';
import { Button } from 'common/components/Button';
import { NotificationType } from 'common/components/Notification/INotificationProps';
import { NotificationData } from 'common/components/NotificationPull/NotificationData';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { Colors } from 'common/styles/Colors';
import { useEffect, useState } from 'react';
import { Trans } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import PageContent from 'views/Components/Page';
import OrderTypeFormModal from './FormModal';
import OrderTypeRow from './Row';
import useStyles from './css';
import { OrderTypesSettingsProps, connector } from './props';

const OrderTypesSettings = ({ setNewToaster }: OrderTypesSettingsProps) => {
    const styles = useStyles();
    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useAppTranslation();
    const { callApi, apiCallStatus } = useApiCall();

    const pageSize = 10;
    const [page, setPage] = useState<number>(1);
    const [pages, setPages] = useState<number>(1);
    const [orderTypes, setOrderTypes] = useState<OrderType[]>([]);
    const [orderType, setOrderType] = useState<OrderType>();
    const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
    const [isDeleteOrderTypeOpen, setIsDeleteOrderTypeOpen] = useState<boolean>(false);

    useDocumentTitle(`${t('titles.settings.settings')} - ${t('settings.general.orderTypes')}`);

    const fetchOrderTypes = async () => {
        const response = await callApi(
            () => OrderTypeSettingsApi.getOrderTypesPaged(pageSize, page),
            {
                selectErrorContent: (_) => ({
                    body: t('toasters.errorOccurredWhenLoading'),
                }),
            }
        );

        if (response?.orderTypes) {
            setOrderTypes([...response.orderTypes]);
            setPages(Math.ceil(response.totalCount / pageSize));
        }
    };

    const changePage = (page: number) => {
        const params = new URLSearchParams(location.search);
        params.set('page', `${page}`);
        navigate({ search: params.toString() });
        setPage(page);
    };

    const handleDeleteOrderType = async () => {
        if (orderType) {
            const response = await callApi(
                () => OrderTypeSettingsApi.deleteOrderType(orderType.id),
                {
                    selectErrorContent: (_) => ({
                        body: t('toasters.errorOccurredWhenSaving'),
                    }),
                }
            );

            if (response) {
                setIsDeleteOrderTypeOpen(false);
                setNewToaster(
                    new NotificationData(
                        t('settings.orderTypes.orderTypeDeletedSuccessfully'),
                        t('settings.orderTypes.orderTypeDeleted'),
                        NotificationType.success
                    )
                );
                fetchOrderTypes();
                setOrderType(undefined);
            }
        }
    };

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const pageParam = params.get('page');
        changePage(pageParam ? +pageParam : 1);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        fetchOrderTypes();

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [page]);

    return (
        <>
            <PageContent>
                <div className={styles.content}>
                    <Box display="flex" justifyContent="end" flexDirection="row" paddingX="30px">
                        <Button
                            cmosSize={'medium'}
                            color={Colors.CM1}
                            cmosVariant={'filled'}
                            label={t('settings.orderTypes.createNewOrderType')}
                            onClick={() => {
                                setIsOpenModal(true);
                            }}
                        />
                    </Box>
                    <Grid container spacing={0} className={styles.table}>
                        <Table stickyHeader>
                            <TableHead>
                                <TableRow>
                                    <TableHeadCell
                                        component="td"
                                        scope="row"
                                        style={{ width: '25%' }}
                                    >
                                        {t('settings.orderTypes.orderType')}
                                    </TableHeadCell>
                                    <TableHeadCell
                                        component="td"
                                        scope="row"
                                        style={{ width: '25%' }}
                                    >
                                        {t('settings.orderTypes.lastUpdate')}
                                    </TableHeadCell>
                                    <TableHeadCell
                                        component="td"
                                        scope="row"
                                        style={{ width: '25%' }}
                                    >
                                        {t('settings.orderTypes.color')}
                                    </TableHeadCell>
                                    <TableHeadCell
                                        component="td"
                                        scope="row"
                                        style={{ width: '25%' }}
                                    />
                                </TableRow>
                            </TableHead>
                            {orderTypes &&
                                (orderTypes.length ? true : undefined) &&
                                apiCallStatus !== 'Pending' && (
                                    <TableBody>
                                        {orderTypes.map((orderType, index) => (
                                            <OrderTypeRow
                                                key={`order-type-${index}-${orderType.id}`}
                                                id={`order-type-${index}-${orderType.id}`}
                                                value={orderType}
                                                onEditClick={(event) => {
                                                    setOrderType(event);
                                                    setIsOpenModal(true);
                                                }}
                                                onDeleteClick={(event) => {
                                                    setOrderType(event);
                                                    setIsDeleteOrderTypeOpen(true);
                                                }}
                                            />
                                        ))}
                                    </TableBody>
                                )}
                        </Table>
                        <DeleteConfirmationPopup
                            open={isDeleteOrderTypeOpen}
                            title={t('settings.orderTypes.deleteOrderType?')}
                            body={
                                <Trans
                                    t={t}
                                    i18nKey="settings.orderTypes.byDeletingThisOrderTypeItWillBeRemovedFromTheListOfOrderTypes"
                                >
                                    normal <b>bold</b>
                                </Trans>
                            }
                            cancel={t('commonLabels.doNotDelete')}
                            confirm={t('settings.orderTypes.deleteOrderType')}
                            onConfirm={handleDeleteOrderType}
                            onClose={() => {
                                setOrderType(undefined);
                                setIsDeleteOrderTypeOpen(false);
                            }}
                        />
                        {apiCallStatus === 'Pending' && (
                            <div
                                style={{
                                    height: '80%',
                                    width: '100%',
                                    display: 'flex',
                                    justifyContent: 'center',
                                }}
                            >
                                <AreaSpinner />
                            </div>
                        )}
                    </Grid>
                </div>
            </PageContent>
            <Box display="flex" justifyContent="center" paddingBottom={3} paddingTop={2}>
                <Pagination
                    page={page}
                    size="small"
                    count={pages}
                    color="primary"
                    onChange={(_, page) => {
                        changePage(page);
                    }}
                />
            </Box>
            <OrderTypeFormModal
                value={orderType}
                isOpen={isOpenModal}
                onClose={() => {
                    setIsOpenModal(false);
                    setOrderType(undefined);
                }}
                onSave={() => {
                    setIsOpenModal(false);
                    fetchOrderTypes();
                    setOrderType(undefined);
                }}
            />
        </>
    );
};

export default connector(OrderTypesSettings);
