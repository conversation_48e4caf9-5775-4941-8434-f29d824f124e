import IconButton from '@mui/material/IconButton';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/styles';
import { TableCell } from 'common/components';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import moment from 'moment';
import useStyles from './css';
import { OrderTypeRowProps } from './props';

const OrderTypeRow = ({ value, id, onEditClick, onDeleteClick }: OrderTypeRowProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    return (
        <TableRow key={id} className={classes.root}>
            <TableCell component="td" scope="row" style={{ width: '25%' }}>
                {value.name}
            </TableCell>
            <TableCell component="td" scope="row" style={{ width: '25%' }}>
                {value.updatedDate
                    ? moment(value.updatedDate).format(t('settings.orderTypes.updatedDateFormat'))
                    : '--'}
            </TableCell>
            <TableCell component="td" scope="row" style={{ width: '25%' }}>
                {value.color ? <CircleDiv color={value.color}></CircleDiv> : '--'}
            </TableCell>
            <TableCell component="td" scope="row" style={{ width: '25%' }}>
                <div className={classes.options}>
                    <IconButton
                        style={{ height: 32, width: 32 }}
                        onClick={() => onEditClick(value)}
                        size="large"
                    >
                        <EditIcon fill={Colors.Neutral5} />
                    </IconButton>
                    <IconButton
                        style={{ height: 32, width: 32 }}
                        onClick={() => onDeleteClick(value)}
                        size="large"
                    >
                        <DeleteIcon fill={Colors.Neutral5} />
                    </IconButton>
                </div>
            </TableCell>
        </TableRow>
    );
};

export default OrderTypeRow;

const CircleDiv = styled('div')(({ color }) => ({
    height: 20,
    width: 20,
    backgroundColor: color,
    border: color === '#FFFFFF' ? '1px solid #DDDDDD' : undefined,
    borderRadius: '50%',
    display: 'inline-block',
    marginTop: 2,
}));
