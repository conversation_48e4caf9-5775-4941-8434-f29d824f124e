import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Colors } from '../../../../../common/styles/Colors';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        backgroundColor: theme.palette.neutral[1],
        '&:hover': {
            backgroundColor: Colors.CM5,
            '&:hover': {
                '& $options': {
                    opacity: 1,
                },
            },
        },
        '& $options': {
            opacity: 0,
        },
    },
    options: {
        display: 'flex',
        justifyContent: 'end',
    },
}));

export default useStyles;
