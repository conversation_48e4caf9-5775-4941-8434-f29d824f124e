import { debounce } from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import FieldsApi, { FieldListItemDto, FieldSection } from 'api/fields';
import { useMemo } from 'react';

import OrderPdfSettingsAPI, {
    OrderPdfFieldItem,
    OrderPdfFieldsDto,
} from '../../../../api/settings/OrderPdfSettings';
import PageContent from '../../../Components/Page';
import { FieldSectionTooltipContextProvider } from '../../common/FieldSectionTooltip/context';
import FieldsList from './FieldsList';

const defaultGroupedFields: Record<FieldSection, FieldListItemDto[]> = {
    WebLocationGeneralInfo: [],
    WebLocationServiceHoursInfo: [],
    WebCustomerInfo: [],
    WebVehicleInfo: [],
    WebOrderInfo: [],
    WebJobInfo: [],
    WebParts: [],
    WebLabor: [],
    MobileOrder: [],
    MobileCustomer: [],
    MobileVehicle: [],
    MobileJobInfo: [],
};

type ReorderFieldsParams = {
    section: FieldSection;
    order: string[];
};

export default function OrderPdf() {
    const { sections, isLoading, reorderFieldsMutation } = useFields();

    const onFieldsReordered = useMemo(
        () =>
            debounce((section: FieldSection, order: OrderPdfFieldItem[]) => {
                const orderIds = order.map((x) => x.id) as string[];
                const params: ReorderFieldsParams = { section, order: orderIds };
                reorderFieldsMutation.mutate(params);
            }, 1000),
        [reorderFieldsMutation]
    );

    return (
        <FieldSectionTooltipContextProvider>
            <PageContent paddedX paddedY>
                <FieldsList
                    isLoading={isLoading}
                    sections={sections}
                    onFieldsReordered={onFieldsReordered}
                />
            </PageContent>
        </FieldSectionTooltipContextProvider>
    );
}

function useFields() {
    const queryKey = ['settings', 'order-pdf'];
    const { data, isLoading, refetch } = useQuery(queryKey, () => OrderPdfSettingsAPI.get(), {
        cacheTime: Infinity,
        refetchOnWindowFocus: (q) => q.isStaleByTime(30000),
        refetchInterval: 120000,
    });
    const sections = useMemo(() => data ?? { groupedFields: defaultGroupedFields }, [data]);

    const queryClient = useQueryClient();

    const reorderFieldsMutation = useMutation(
        (args: ReorderFieldsParams) => {
            return FieldsApi.setPdfFieldsOrder(args.section, args.order);
        },
        {
            onMutate(args: ReorderFieldsParams) {
                const storedData: typeof data = queryClient.getQueryData(queryKey);
                if (!storedData) return;
                queryClient.setQueryData(
                    queryKey,
                    reorderFields(storedData, args.section, args.order)
                );
            },
            onSettled() {
                refetch();
            },
        }
    );

    return {
        sections,
        isLoading,
        reorderFieldsMutation,
        refetch,
    };
}

function reorderFields(
    orderPdfFieldsDto: OrderPdfFieldsDto,
    section: FieldSection,
    order: string[]
): OrderPdfFieldsDto {
    const updatedOrderPdfFieldsDto = {
        ...orderPdfFieldsDto,
        groupedFields: {
            ...orderPdfFieldsDto.groupedFields,
        },
    };

    const fields = orderPdfFieldsDto.groupedFields[section] || [];
    const reorderedFields: OrderPdfFieldItem[] = [];

    for (const id of order) {
        const field = fields.find((x) => x.id === id);
        if (field) {
            reorderedFields.push(field);
        }
    }

    if (reorderedFields.length !== fields.length) {
        const missing = fields.filter((x) => !reorderedFields.includes(x));
        reorderedFields.push(...missing);
    }

    updatedOrderPdfFieldsDto.groupedFields[section] = reorderedFields;

    return updatedOrderPdfFieldsDto;
}
