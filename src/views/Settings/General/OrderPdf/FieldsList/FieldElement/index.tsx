import { IconButton, styled } from '@mui/material';
import { FieldSection, FieldType, isPredefinedField, PREDEFINED_FIELDS } from 'api/fields';
import { OrderPdfFieldItem } from 'api/settings/OrderPdfSettings';
import { $Icon } from 'common/components/Icons/$Icon';
import ALetterIcon from 'common/components/Icons/ALetterIcon';
import BarCodeIcon from 'common/components/Icons/BarCode';
import { CalendarIcon } from 'common/components/Icons/CalendarIcon';
import { CheckCircleIcon } from 'common/components/Icons/CheckCircleIcon';
import { ClockIcon } from 'common/components/Icons/ClockIcon';
import DragAndDropIcon from 'common/components/Icons/DragAndDropIcon';
import { HideIcon } from 'common/components/Icons/HideIcon';
import { MicIcon } from 'common/components/Icons/MicIcon';
import { NumbersIcon } from 'common/components/Icons/NumbersIcon';
import { PtIcon } from 'common/components/Icons/PtIcon';
import { ShowIcon } from 'common/components/Icons/ShowIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { TFunction } from 'i18next';
import React, { memo, useCallback } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';

type FieldElementProps = {
    dragHandleProps?: DraggableProvidedDragHandleProps;
    field: OrderPdfFieldItem;
    section: FieldSection;
    onHideIconClicked?: (fieldId: string, hidden: boolean, section: FieldSection) => void;
};

export default function FieldElement({
    dragHandleProps,
    field,
    section,
    onHideIconClicked,
}: FieldElementProps) {
    const { t } = useAppTranslation();

    const isPredefined = isPredefinedField(field.type);

    const translatedName = t(
        `settings.customizableFields.predefined.${field.name.replaceAll('.', '_')}`
    );
    const fieldName =
        isPredefined &&
        translatedName !==
            `settings.customizableFields.predefined.${field.name.replaceAll('.', '_')}`
            ? translatedName
            : field.name;

    const onHideButtonClick = useCallback(() => {
        if (onHideIconClicked) onHideIconClicked(field.id, !field.isHidden, section);
    }, [field.id, field.isHidden, section, onHideIconClicked]);

    return (
        <DivRoot>
            <DivHandle {...(dragHandleProps ?? {})}>
                <DragAndDropIcon size={25} fill={'var(--neutral7)'} />
            </DivHandle>
            <SpanName>{fieldName}</SpanName>
            <DivType>
                |&nbsp;
                <FieldTypeIcon name={field.name} type={field.type} /> {getFieldTypeName(t, field)}
            </DivType>
            <DivActionBlock>
                <IconButton size="small" onClick={onHideButtonClick}>
                    {field.isHidden ? (
                        <HideIcon fill={'var(--neutral7)'} />
                    ) : (
                        <ShowIcon fill={'var(--cm1)'} />
                    )}
                </IconButton>
            </DivActionBlock>
        </DivRoot>
    );
}

// Styled Components
const DivRoot = styled('div')(({ theme }) => ({
    borderRadius: 10,
    border: `1px solid ${theme.palette.neutral[4]}`,
    backgroundColor: theme.palette.neutral[1],
    padding: '9px 10px',
    display: 'flex',
    gap: 10,
    alignItems: 'center',
    position: 'relative',
    '&:hover': {
        backgroundColor: theme.palette.neutral[3],
    },
}));

const DivHandle = styled('div')(() => ({
    borderRadius: 5,
    display: 'flex',
    alignItems: 'center',
}));

const SpanName = styled('span')(({ theme }) => ({
    ...theme.typography.h5Inter,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
}));

const DivType = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    display: 'flex',
    alignItems: 'center',
    color: theme.palette.neutral[7],
}));

const DivActionBlock = styled('div')(() => ({
    marginLeft: 'auto',
    display: 'flex',
    flexWrap: 'nowrap',
}));

// extended field type with some custom types for special predefined fields
type FieldTypeX = FieldType | `predefined-${'Scan' | 'RecordAudio' | 'Dropdown'}`;

const PREDEFINED_FIELD_EXTENDED_TYPES: Record<
    string,
    Exclude<FieldTypeX, 'Predefined' | 'PredefinedAdditionalPosition'>
> = {
    [PREDEFINED_FIELDS.CUSTOMER_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_EMAIL]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_LANDLINE]: 'Numeric',
    [PREDEFINED_FIELDS.CUSTOMER_MOBILE]: 'Numeric',
    [PREDEFINED_FIELDS.CUSTOMER_BUSINESS_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_ID_DOCUMENT]: 'ShortText',
    [PREDEFINED_FIELDS.CUSTOMER_PAYMENT_METHOD]: 'predefined-Dropdown',

    [PREDEFINED_FIELDS.VEHICLE_BRAND]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.VEHICLE_MODEL]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.VEHICLE_YEAR]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.VEHICLE_MILEAGE]: 'Numeric',
    [PREDEFINED_FIELDS.VEHICLE_PLATES]: 'ShortText',
    [PREDEFINED_FIELDS.VEHICLE_VIN]: 'ShortText',

    [PREDEFINED_FIELDS.ORDER_PHASE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_TOWER]: 'ShortText',
    [PREDEFINED_FIELDS.ORDER_TYPE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_IN_CHARGE]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_ASSIGNED_TO]: 'predefined-Dropdown',
    [PREDEFINED_FIELDS.ORDER_NOTE]: 'ShortText',

    [PREDEFINED_FIELDS.LOCATION_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_LEGAL_NAME]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_ADDRESS]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_PHONE_NUMBER]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_EMAIL_ADDRESS]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_WEBSITE]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_TAX_IDENTIFICATION]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_MONDAY_TO_FRIDAY_HOURS]: 'ShortText',
    [PREDEFINED_FIELDS.LOCATION_SATURDAY_HOURS]: 'ShortText',
};

const DROPDOWN_ICON = <PtIcon fill="currentColor" />;
const RADIO_BUTTON_ICON = <CheckCircleIcon fill="currentColor" />;
const FREETEXT_ICON = <ALetterIcon />;
const NUMERIC_ICON = <NumbersIcon fill="currentColor" />;
const DATE_ICON = <CalendarIcon fill="currentColor" />;
const TIME_ICON = <ClockIcon fill="currentColor" />;
const RECORD_AUDIO_ICON = <MicIcon fill="currentColor" />;
const CURRENCY_ICON = (
    // eslint-disable-next-line react/jsx-pascal-case
    <$Icon fill="currentColor" />
);
const SCAN_ICON = <BarCodeIcon />;

const FIELD_ICONS: Record<
    Exclude<FieldTypeX, 'Predefined' | 'PredefinedAdditionalPosition'>,
    JSX.Element
> = {
    Currency: CURRENCY_ICON,
    Date: DATE_ICON,
    ShortText: FREETEXT_ICON,
    LongText: FREETEXT_ICON,
    Numeric: NUMERIC_ICON,
    Time: TIME_ICON,
    Select: RADIO_BUTTON_ICON,
    MultiSelect: RADIO_BUTTON_ICON,
    'predefined-Dropdown': DROPDOWN_ICON,
    'predefined-RecordAudio': RECORD_AUDIO_ICON,
    'predefined-Scan': SCAN_ICON,
};

function getFieldTypeName(t: TFunction, field: OrderPdfFieldItem): string {
    if (isPredefinedField(field.type)) {
        const xType = PREDEFINED_FIELD_EXTENDED_TYPES[field.name] ?? 'ShortText';
        return t(`customizableFields.types.${xType}`);
    } else if (field.type === 'Select' || field.type === 'MultiSelect') {
        return t('customizableFields.types.MultipleChoice');
    } else {
        return t(`customizableFields.types.${field.type}`);
    }
}

const FIELD_TYPES_ICONS: Record<
    Exclude<FieldType, 'Predefined' | 'PredefinedAdditionalPosition'>,
    JSX.Element
> = {
    ShortText: FREETEXT_ICON,
    LongText: FREETEXT_ICON,
    Numeric: NUMERIC_ICON,
    Time: TIME_ICON,
    Date: DATE_ICON,
    Currency: CURRENCY_ICON,
    Select: RADIO_BUTTON_ICON,
    MultiSelect: RADIO_BUTTON_ICON,
};

export const FieldTypeIcon = memo(
    ({ type, name, className }: { type: FieldType; name?: string; className?: string }) => {
        if (type === 'Predefined' || type === 'PredefinedAdditionalPosition') {
            if (!name) return null;
            const xType = PREDEFINED_FIELD_EXTENDED_TYPES[name];
            return FIELD_ICONS[xType] ?? null;
        }

        return React.cloneElement(FIELD_TYPES_ICONS[type], { className });
    }
);
if (import.meta.env.NODE_ENV === 'development') FieldTypeIcon.displayName = 'FieldTypeIcon';
