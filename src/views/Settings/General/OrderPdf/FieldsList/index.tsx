import { CircularProgress, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import FieldsApi, { FieldSection } from 'api/fields';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback, useEffect, useState } from 'react';
import {
    DragDropContext,
    Draggable,
    Droppable,
    DropResult,
    ResponderProvided,
} from 'react-beautiful-dnd';

import { OrderPdfFieldItem, OrderPdfFieldsDto } from 'api/settings/OrderPdfSettings';
import FieldSectionTooltip from '../../../common/FieldSectionTooltip';
import FieldElement from './FieldElement';

type FieldsListProps = {
    onFieldsReordered: (section: FieldSection, fields: OrderPdfFieldItem[]) => void;
    sections: OrderPdfFieldsDto;
    isLoading: boolean;
};

export default function FieldsList({
    isLoading,
    sections: sectionProps,
    onFieldsReordered,
}: FieldsListProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    // use additional state to store fields to avoid flickering
    // i.e. when fields are moved around they flicker back to initial state for a moment
    const [sections, setSections] = useState(sectionProps);

    useEffect(() => {
        setSections(sectionProps);
    }, [sectionProps]);

    const onDragEnd = useCallback(
        (result: DropResult, provided: ResponderProvided, section: FieldSection) => {
            const fromIndex = result.source.index;
            const toIndex = result.destination?.index;
            if (toIndex === undefined) {
                console.warn('[FieldList] toIndex is undefined');
                return;
            }

            const newSections = { ...sections };

            const newFields = newSections.groupedFields[section] || [];

            const movedField = newFields[fromIndex];
            newFields.splice(fromIndex, 1);
            newFields.splice(toIndex, 0, movedField);
            newSections.groupedFields[section] = newFields;
            setSections(newSections);
            onFieldsReordered(section, newFields);
        },
        [sections, onFieldsReordered]
    );

    const { mutate: toggleFieldHidden } = useMutation(
        ({ id, value, section }: { id: string; value: boolean; section: FieldSection }) => {
            return FieldsApi.setHiddenInPdfFlag(id, section, value);
        },
        {
            onMutate({ id, value, section }) {
                const newSections = { ...sections };

                newSections.groupedFields[section] = newSections.groupedFields[section].map((x) => {
                    if (x.id === id) {
                        return {
                            ...x,
                            isHidden: value,
                        };
                    }
                    return x;
                });

                setSections(newSections);
            },
            onError: (_: unknown) => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            },
        }
    );

    const onHideIconClicked = useCallback(
        (fieldId: string, hidden: boolean, section: FieldSection) => {
            toggleFieldHidden({
                id: fieldId,
                value: hidden,
                section: section,
            });
        },
        [toggleFieldHidden]
    );

    return (
        <>
            {Object.entries(sections.groupedFields).map(([section, fields]) => {
                const typedSection = section as FieldSection;
                return (
                    <SectionRoot>
                        <div key={typedSection}>
                            <SectionHeader>
                                <span>
                                    {t(
                                        `settings.customizableFields.sections.${typedSection}-header`
                                    )}
                                </span>{' '}
                                <FieldSectionTooltip section={typedSection} />
                            </SectionHeader>
                            <SectionBody>
                                <DragDropContext
                                    onDragEnd={(result, provided) =>
                                        onDragEnd(result, provided, typedSection)
                                    }
                                >
                                    <Droppable droppableId={`settings-fields-${typedSection}`}>
                                        {(droppable, snapshot) => (
                                            <FieldsListContainer
                                                ref={droppable.innerRef}
                                                {...droppable.droppableProps}
                                            >
                                                {isLoading ? (
                                                    <CircularProgress
                                                        style={{ padding: 5 }}
                                                        size={20}
                                                    />
                                                ) : (
                                                    fields.map((field, index) => (
                                                        <Draggable
                                                            index={index}
                                                            draggableId={field.id}
                                                            key={field.id}
                                                        >
                                                            {(draggable) => (
                                                                <FieldItem
                                                                    ref={draggable.innerRef}
                                                                    {...draggable.draggableProps}
                                                                >
                                                                    <FieldElement
                                                                        dragHandleProps={
                                                                            draggable.dragHandleProps ??
                                                                            undefined
                                                                        }
                                                                        field={field}
                                                                        section={typedSection}
                                                                        onHideIconClicked={
                                                                            onHideIconClicked
                                                                        }
                                                                    />
                                                                </FieldItem>
                                                            )}
                                                        </Draggable>
                                                    ))
                                                )}
                                                {droppable.placeholder}
                                            </FieldsListContainer>
                                        )}
                                    </Droppable>
                                </DragDropContext>
                            </SectionBody>
                        </div>
                    </SectionRoot>
                );
            })}
        </>
    );
}

const SectionRoot = styled('section')(({ theme }) => ({
    borderRadius: 10,
    border: `1px solid ${theme.palette.neutral[4]}`,
    overflow: 'hidden',
    marginTop: 20,
}));

const SectionHeader = styled('header')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[3],
    padding: '12px 15px',
    display: 'flex',
    alignItems: 'center',

    '& > span': {
        ...theme.typography.h5Roboto,
    },
}));

const SectionBody = styled('div')(({ theme }) => ({
    padding: 18,
}));

const FieldsListContainer = styled('div')(({ theme }) => ({
    padding: 11,
    borderRadius: 10,
    backgroundColor: theme.palette.neutral[2],
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',

    // because "gap" prop doesn't work with react-beautiful-dnd
    paddingBottom: 1,
    '& > *': {
        marginBottom: 10,
    },
}));

const FieldItem = styled('div')(() => ({
    willChange: 'transform',
}));
