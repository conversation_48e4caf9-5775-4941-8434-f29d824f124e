import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useMemo } from 'react';

type UseSettingsToasters = {
    settingSaved: () => void;
};

/**
 * Helper hook for calling toasters that are used in settings
 * often.
 */
export function useSettingsToasters(): UseSettingsToasters {
    const toaster = useToasters();
    const { t } = useAppTranslation();

    return useMemo(
        () => ({
            settingSaved: () =>
                toaster.success(
                    t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated')
                ),
        }),
        [t, toaster]
    );
}
