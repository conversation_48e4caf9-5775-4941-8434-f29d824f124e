import { RouterTab } from 'common/components/tabs/RouterParameterBasedTabs';
import { ROUTES, SETTINGS_WORKSHOPPLANNER_TABS } from 'common/constants';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { useMemo } from 'react';
import SettingsRouterParameterBasedTabs from '../common/SettingsRouterParameterBasedTabs';
import WorkshopPlannerGeneralSettings from './General';
import WorkshopPlannerPlanningsSettings from './Plannings';

const WorkshopPlannerSettings = () => {
    const { t } = useAppTranslation();
    useDocumentTitle(t('settings.workshopPlannerSettings.title'));
    const tabs: RouterTab[] = useMemo(() => {
        const tabs = [
            {
                value: SETTINGS_WORKSHOPPLANNER_TABS.GENERAL,
                label: t('settings.workshopPlannerSettings.tabs.general'),
                content: <WorkshopPlannerGeneralSettings />,
            },
        ];

        tabs.push({
            value: SETTINGS_WORKSHOPPLANNER_TABS.PLANNINGS,
            label: t('settings.workshopPlannerSettings.tabs.plannings'),
            content: <WorkshopPlannerPlanningsSettings />,
        });

        return tabs;
    }, [t]);
    return (
        <SettingsRouterParameterBasedTabs
            urlPattern={ROUTES.SETTINGS.WORKSHOP_PLANNER.PATH}
            parameterName="section"
            tabs={tabs}
        />
    );
};

export default WorkshopPlannerSettings;
