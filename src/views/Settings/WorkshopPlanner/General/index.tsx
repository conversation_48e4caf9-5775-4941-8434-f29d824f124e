import { styled, Tooltip, tooltipClasses, TooltipProps } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import TemplatesSettingsAPI from 'api/settings/InspectionForms/TemplatesSettings';
import WpSettingsApi, {
    AirportScreenViewType,
    CompletedJobsType,
    GeneralSettingsDto,
} from 'api/workshopPlanner/Settings/General/GeneralSettings';
import AreaSpinner from 'common/components/AreaSpinner';
import { InfoTooltipInternal } from 'common/components/InfoTooltip';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Duration } from 'luxon';
import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { globalSettingsActions } from 'store/slices/globalSettingsSlice';
import PageContent from 'views/Components/Page';
import {
    BooleanSettingControl,
    NumberSettingControl,
    RadioButtonArrayControl,
    SettingsSection,
    TextSettingControl,
} from 'views/Settings/common';
import DropdownSettingControl from 'views/Settings/common/DropdownSettingControl';
import HighlightOrdersSettings from './HighlightOrdersSettings';
import SelectOrderTypesCheckbox from './SelectOrderTypesCheckbox';
import WorkshopPlannerShowColorTypeSettings from './ShowColorSettings';

const DEFAULT_SCHEDULED_DURATION_MINUTES = 60;

const WorkshopPlannerGeneralSettings = () => {
    const { t } = useAppTranslation();
    const dispatch = useDispatch();
    const toasters = useToasters();

    const [settings, setSettings] = useState<GeneralSettingsDto>();

    const numbersOfDigitsList = useMemo(
        () => [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((n) => ({ label: n.toFixed(), value: n })),
        []
    );

    const defaultScheduledDuration: OptionData<number>[] = [
        { label: '00:15 min', value: 15 },
        { label: '00:30 min', value: 30 },
        { label: '00:45 min', value: 45 },
        { label: '01:00 hr', value: 60 },
        { label: '01:30 hrs', value: 90 },
        { label: '02:00 hrs', value: 120 },
    ];

    const formatDefaultScheduledDuration = (value: number) => {
        const predefinedLabel = defaultScheduledDuration.find((x) => x.value === value);
        if (predefinedLabel) return predefinedLabel.label;

        const format =
            value < DEFAULT_SCHEDULED_DURATION_MINUTES
                ? "hh:mm 'min'"
                : value === DEFAULT_SCHEDULED_DURATION_MINUTES
                ? "hh:mm 'hr'"
                : "hh:mm 'hrs'";
        return Duration.fromObject({ minute: value }).toFormat(format);
    };

    const { isLoading } = useQuery(
        ['settings', 'wp', 'general'],
        WpSettingsApi.getGeneralSettings,
        {
            onSuccess: (d) => {
                setSettings(d);
            },
            cacheTime: 120000,
            staleTime: 200,
        }
    );

    const mutateSettings = useMutation(
        async ({ settings }: { settings: GeneralSettingsDto; toasterText?: string }) => {
            return await WpSettingsApi.updateGeneralSettings(settings);
        },
        {
            onError: () => {
                toasters.danger(t('toasters.errorOccurredWhenSaving'));
            },
            onSuccess: (data, { toasterText }) => {
                setSettings(data);

                dispatch(
                    globalSettingsActions.updateRsFeatureFlags({
                        ...data,
                    })
                );

                toasters.success(
                    toasterText ?? t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated')
                );
            },
        }
    );

    const updateSettings = (update: Partial<GeneralSettingsDto>, toasterText?: string) => {
        if (!settings) {
            return;
        }

        mutateSettings.mutate({
            settings: {
                ...settings,
                ...update,
            },
            toasterText,
        });
    };

    const { data: templatesListResponse } = useQuery(
        ['settings', 'wp', 'templates'],
        TemplatesSettingsAPI.list,
        {
            staleTime: 1000,
            cacheTime: Infinity,
        }
    );

    const templateOptions = useMemo(
        () =>
            templatesListResponse?.templates.map((t) => ({
                label: t.inspectionFormTemplateName,
                value: t.inspectionFormTemplateId,
            })) ?? [],
        [templatesListResponse]
    );

    return (
        <DivPagesContainer>
            <PageContent paddedX>
                {isLoading ? (
                    <AreaSpinner />
                ) : (
                    <>
                        <SettingsSection
                            label={t('settings.workshopPlannerSettings.tabs.general')}
                            CustomTitle={CustomTitle}
                        >
                            <NumberSettingControl
                                template="{0} UT’s"
                                validate={(v) => Number.isInteger(v) && v >= 0 && v <= 2147483647}
                                label={t('settings.workshopPlannerSettings.general.UTsEqualTo')}
                                value={settings?.uTsEqualTo || 0}
                                placeholder="0 UT’s"
                                onValueChange={(value) =>
                                    updateSettings(
                                        { uTsEqualTo: value },
                                        t('settings.workshopPlannerSettings.general.UTsSaved')
                                    )
                                }
                                name="UTsEqualToField"
                                childrenMD={1}
                                alignItems="center"
                            />
                            <DropdownSettingControl
                                name="maximum-number-of-digits-tower-number"
                                label={t(
                                    'settings.workshopPlannerSettings.general.maximumNumberOfDigitsForTowerNumber'
                                )}
                                hasHint={true}
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.maximumNumberOfDigitsForTowerNumberHint'
                                )}
                                hintPosition="right"
                                isRequired
                                options={numbersOfDigitsList}
                                cmosVariant="default"
                                value={{
                                    label: `${
                                        settings?.maximumNumberOfDigitsToDisplayForTowerNumber ||
                                        '5'
                                    }`,
                                    value:
                                        settings?.maximumNumberOfDigitsToDisplayForTowerNumber || 5,
                                }}
                                onChange={(event) => {
                                    if (event === null) return;
                                    updateSettings(
                                        {
                                            maximumNumberOfDigitsToDisplayForTowerNumber:
                                                event.value,
                                        },
                                        t(
                                            'settings.workshopPlannerSettings.general.maximumNumberOfDigitsForTowerNumberSaved'
                                        )
                                    );
                                }}
                            />
                            <DropdownSettingControl
                                name="maximum-number-of-digits-order-appointment"
                                label={t(
                                    'settings.workshopPlannerSettings.general.maximumNumberOfDigitsForOrderAndAppointment'
                                )}
                                hasHint={true}
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.maximumNumberOfDigitsForOrderAndAppointmentHint'
                                )}
                                hintPosition="right"
                                isRequired
                                options={numbersOfDigitsList}
                                cmosVariant="default"
                                value={{
                                    label: `${
                                        settings?.maximumNumberOfDigitsToDisplayForOrderAndAppointment ||
                                        '5'
                                    }`,
                                    value:
                                        settings?.maximumNumberOfDigitsToDisplayForOrderAndAppointment ||
                                        5,
                                }}
                                onChange={(event) => {
                                    if (event === null) return;
                                    updateSettings(
                                        {
                                            maximumNumberOfDigitsToDisplayForOrderAndAppointment:
                                                event.value,
                                        },
                                        t(
                                            'settings.workshopPlannerSettings.general.maximumNumberOfDigitsForOrderAndAppointmentSaved'
                                        )
                                    );
                                }}
                            />
                            <RadioButtonArrayControl<AirportScreenViewType>
                                disabled={isLoading}
                                onChange={(value) => {
                                    updateSettings(
                                        { airportScreenView: value },
                                        t(
                                            'settings.workshopPlannerSettings.general.notifications.settingsUpdatedSuccessfully'
                                        )
                                    );
                                }}
                                label={t(
                                    'settings.workshopPlannerSettings.general.airportScreenView'
                                )}
                                value={settings?.airportScreenView || 'Vertical'}
                                values={[
                                    {
                                        id: 'Vertical',
                                        value: 'Vertical',
                                        label: t(
                                            'settings.workshopPlannerSettings.general.airportScreenViewOptions.verticalOption'
                                        ),
                                        tooltipDescription: t(
                                            'settings.workshopPlannerSettings.general.airportScreenViewOptions.verticalOptionHint'
                                        ),
                                    },
                                    {
                                        id: 'Horizontal',
                                        value: 'Horizontal',
                                        label: t(
                                            'settings.workshopPlannerSettings.general.airportScreenViewOptions.horizontalOption'
                                        ),
                                        tooltipDescription: t(
                                            'settings.workshopPlannerSettings.general.airportScreenViewOptions.horizontalOptionHint'
                                        ),
                                    },
                                ]}
                            />
                            <BooleanSettingControl
                                value={settings?.showTeamMemberSpecialty ?? false}
                                name="showTeamMemberSpecialty"
                                label={t(
                                    'settings.workshopPlannerSettings.general.showTeamMemberSpecialty'
                                )}
                                onChange={(value) => {
                                    updateSettings({ showTeamMemberSpecialty: value });
                                }}
                                slotProps={{
                                    layout: {
                                        labelMD: 3,
                                        childrenMD: 1,
                                    },
                                }}
                            />
                            <DropdownSettingControl
                                name="default-scheduled-duration"
                                label={t(
                                    'settings.workshopPlannerSettings.general.defaultScheduledDuration'
                                )}
                                hasHint
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.defaultScheduledDurationHint'
                                )}
                                options={defaultScheduledDuration}
                                cmosVariant="default"
                                value={{
                                    label: formatDefaultScheduledDuration(
                                        settings?.defaultScheduledDurationMinutes ??
                                            DEFAULT_SCHEDULED_DURATION_MINUTES
                                    ),
                                    value:
                                        settings?.defaultScheduledDurationMinutes ??
                                        DEFAULT_SCHEDULED_DURATION_MINUTES,
                                }}
                                onChange={(option) => {
                                    if (option === null) return;
                                    updateSettings({
                                        defaultScheduledDurationMinutes: option.value,
                                    });
                                }}
                            />
                        </SettingsSection>
                        <WorkshopPlannerShowColorTypeSettings />

                        <SettingsSection>
                            <BooleanSettingControl
                                value={settings?.showTechnicianCapacity ?? false}
                                name="showTechCap"
                                label={t(
                                    'settings.workshopPlannerSettings.general.showTechnicianCapacity'
                                )}
                                onChange={(value) => {
                                    updateSettings({ showTechnicianCapacity: value });
                                }}
                                hasHint
                                customHint={
                                    <DivTooltipContainer>
                                        <StyledTooltip
                                            title={<CustomTooltip />}
                                            placement="top"
                                            arrow
                                        >
                                            <InfoTooltipInternal />
                                        </StyledTooltip>
                                    </DivTooltipContainer>
                                }
                                slotProps={{
                                    layout: {
                                        labelMD: 3,
                                        childrenMD: 1,
                                    },
                                }}
                            />
                            <NumberSettingControl
                                disabled={!settings?.showTechnicianCapacity}
                                template="{0} %"
                                validate={(v) => Number.isInteger(v) && v >= 0 && v <= 100}
                                label={t(
                                    'settings.workshopPlannerSettings.general.maximumTechnicianCapacity'
                                )}
                                value={settings?.maximumTechnicianCapacity || 80}
                                placeholder="80 %"
                                onValueChange={(value) => {
                                    updateSettings({ maximumTechnicianCapacity: value });
                                }}
                                name="maxTechCap"
                                labelMD={3}
                                childrenMD={1}
                                alignItems="center"
                            />
                            <BooleanSettingControl
                                disabled={!settings?.showTechnicianCapacity}
                                value={settings?.allowSchedulingWithMaximumCapacity ?? false}
                                name="allowSchedule"
                                label={t(
                                    'settings.workshopPlannerSettings.general.allowSchedulingWithMaximum'
                                )}
                                onChange={(value) => {
                                    updateSettings({ allowSchedulingWithMaximumCapacity: value });
                                }}
                                slotProps={{
                                    layout: {
                                        labelMD: 3,
                                        childrenMD: 1,
                                    },
                                }}
                            />
                            <RadioButtonArrayControl<CompletedJobsType>
                                disabled={isLoading}
                                onChange={(value) => {
                                    updateSettings(
                                        { completedJobsType: value },
                                        t(
                                            'settings.workshopPlannerSettings.general.notifications.settingsUpdatedSuccessfully'
                                        )
                                    );
                                }}
                                label={t(
                                    'settings.workshopPlannerSettings.general.completedJobsTypeSetting'
                                )}
                                hasHint
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.completedJobsTypeSettingHint'
                                )}
                                alignItems="center"
                                value={settings?.completedJobsType || 'ActualTimeSpent'}
                                values={[
                                    {
                                        id: 'ScheduledTime',
                                        value: 'ScheduledTime',
                                        label: t(
                                            'settings.workshopPlannerSettings.general.completedJobsTypeSettingOptions.scheduledTime'
                                        ),
                                        tooltipDescription: t(
                                            'settings.workshopPlannerSettings.general.completedJobsTypeSettingOptions.scheduledTimeHint'
                                        ),
                                    },
                                    {
                                        id: 'ActualTimeSpent',
                                        value: 'ActualTimeSpent',
                                        label: t(
                                            'settings.workshopPlannerSettings.general.completedJobsTypeSettingOptions.actualTimeSpent'
                                        ),
                                        tooltipDescription: t(
                                            'settings.workshopPlannerSettings.general.completedJobsTypeSettingOptions.actualTimeSpentHint'
                                        ),
                                    },
                                ]}
                            />
                        </SettingsSection>

                        <SettingsSection>
                            <BooleanSettingControl
                                value={settings?.showWorkshopUtilizationMetric ?? false}
                                name="showUtilMetric"
                                label={t(
                                    'settings.workshopPlannerSettings.general.showWorkshopUtilizationMetric'
                                )}
                                onChange={(value) => {
                                    updateSettings({ showWorkshopUtilizationMetric: value });
                                }}
                                hasHint
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.showWorkshopUtilizationMetricTooltip'
                                )}
                                hintPosition="right"
                                slotProps={{
                                    layout: {
                                        labelMD: 3,
                                        childrenMD: 1,
                                    },
                                }}
                            />
                            <NumberSettingControl
                                disabled={!settings?.showWorkshopUtilizationMetric}
                                validate={(v) => Number.isInteger(v) && v >= 0}
                                label={t(
                                    'settings.workshopPlannerSettings.general.productiveWorkspace'
                                )}
                                value={settings?.productiveWorkspace || 1}
                                onValueChange={(value) => {
                                    updateSettings({ productiveWorkspace: value });
                                }}
                                name="productiveWorkspace"
                                labelMD={3}
                                childrenMD={1}
                                alignItems="center"
                            />
                        </SettingsSection>

                        <SettingsSection>
                            <BooleanSettingControl
                                value={settings?.urgentFlashingIndicationEnabled ?? false}
                                name="urgentFlashingIndicationEnabled"
                                label={t(
                                    'settings.workshopPlannerSettings.general.urgentFlashingIndication.title'
                                )}
                                onChange={(value) => {
                                    updateSettings({ urgentFlashingIndicationEnabled: value });
                                }}
                                hasHint={true}
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.urgentFlashingIndication.infoTooltipText'
                                )}
                            />
                            <DropdownSettingControl
                                label={
                                    <div style={{ fontWeight: 'normal' }}>
                                        {t(
                                            'settings.workshopPlannerSettings.general.urgentFlashingIndication.inspectionFormToConsider'
                                        )}
                                    </div>
                                }
                                placeholder={t(
                                    'settings.workshopPlannerSettings.general.urgentFlashingIndication.selectInspectionForm'
                                )}
                                options={templateOptions}
                                cmosVariant="default"
                                size="medium"
                                value={
                                    settings?.urgentFlashingIndicationTemplateId
                                        ? templateOptions.find(
                                              (t) =>
                                                  t.value ===
                                                  settings.urgentFlashingIndicationTemplateId
                                          )
                                        : null
                                }
                                onChange={(event) => {
                                    if (event === null) return;
                                    updateSettings(
                                        {
                                            urgentFlashingIndicationTemplateId: event.value,
                                        },
                                        t(
                                            'settings.workshopPlannerSettings.general.notifications.settingsUpdatedSuccessfully'
                                        )
                                    );
                                }}
                                childrenMD={4}
                                disabled={!settings?.urgentFlashingIndicationEnabled}
                            />
                            <TextSettingControl
                                name={'UrgentFlashingIndicationExplanatoryText'}
                                maxLength={60}
                                required={true}
                                label={
                                    <div style={{ fontWeight: 'normal' }}>
                                        {t(
                                            'settings.workshopPlannerSettings.general.urgentFlashingIndication.explanatoryTextLabel'
                                        )}
                                    </div>
                                }
                                value={settings?.urgentFlashingIndicationExplanatoryText || ''}
                                onSave={async (value: string) =>
                                    await updateSettings(
                                        { urgentFlashingIndicationExplanatoryText: value },
                                        t(
                                            'settings.workshopPlannerSettings.general.notifications.settingsUpdatedSuccessfully'
                                        )
                                    )
                                }
                                childrenMD={4}
                                hasHint={true}
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.urgentFlashingIndication.explanatoryTextTooltip'
                                )}
                                disabled={!settings?.urgentFlashingIndicationEnabled}
                            />
                        </SettingsSection>
                        <SettingsSection>
                            <SelectOrderTypesCheckbox
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.standardOperations.tooltip'
                                )}
                                labelText={t(
                                    'settings.workshopPlannerSettings.general.standardOperations.title'
                                )}
                                settings={{
                                    enabled: settings?.enableStandardOperations ?? false,
                                    orderTypeIds: settings
                                        ? JSON.parse(settings.standardOperationOrderTypes)
                                        : [],
                                }}
                                onSettingsChanged={(value) => {
                                    updateSettings({
                                        enableStandardOperations: value.enabled,
                                        standardOperationOrderTypes: JSON.stringify(
                                            value.orderTypeIds
                                        ),
                                    });
                                }}
                            />
                        </SettingsSection>
                        <SettingsSection>
                            <SelectOrderTypesCheckbox
                                hintText={t(
                                    'settings.workshopPlannerSettings.general.technicianSignatureForJob.tooltip'
                                )}
                                labelText={t(
                                    'settings.workshopPlannerSettings.general.technicianSignatureForJob.title'
                                )}
                                settings={{
                                    enabled: settings?.addTechnicianSignatureAtJob ?? false,
                                    orderTypeIds: settings
                                        ? JSON.parse(settings.technicianSignatureAtJobOrderTypes)
                                        : [],
                                }}
                                onSettingsChanged={(value) => {
                                    updateSettings({
                                        addTechnicianSignatureAtJob: value.enabled,
                                        technicianSignatureAtJobOrderTypes: JSON.stringify(
                                            value.orderTypeIds
                                        ),
                                    });
                                }}
                            />
                        </SettingsSection>
                    </>
                )}
            </PageContent>
            <PageContent paddedX>
                <SettingsSection
                    label={t('settings.workshopPlannerSettings.pausedOrders.title')}
                    CustomTitle={CustomTitle}
                >
                    <HighlightOrdersSettings
                        settings={{
                            deliveryPromiseDateChecked:
                                settings?.highlightExpiredDeliveryDateOrders ?? true,
                            daysPausedChecked: settings?.highlightExpiredPausedDaysOrders ?? false,
                            preventiveDays: settings?.preventiveExpiredPausedDays ?? 2,
                            urgentDays: settings?.urgentExpiredPausedDays ?? 5,
                        }}
                        onSettingsChanged={(value) =>
                            updateSettings({
                                highlightExpiredDeliveryDateOrders:
                                    value.deliveryPromiseDateChecked,
                                highlightExpiredPausedDaysOrders: value.daysPausedChecked,
                                preventiveExpiredPausedDays: value.preventiveDays,
                                urgentExpiredPausedDays: value.urgentDays,
                            })
                        }
                    />
                </SettingsSection>
            </PageContent>
        </DivPagesContainer>
    );
};

const CustomTooltip = () => {
    const { t } = useAppTranslation();

    return (
        <TooltipContentContainer>
            <div>
                {t(
                    'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.mainText'
                )}
            </div>
            <TooltipFormula>
                <div>
                    {t(
                        'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.technicalCapacity'
                    ) + ' ='}
                </div>
                <TooltipFormulaRightSide>
                    <div>
                        {t(
                            'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.totalTechnicians'
                        ) +
                            ' x ' +
                            t(
                                'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.availableHours'
                            ) +
                            ' x'}
                    </div>
                    <div>
                        {t(
                            'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.workDays'
                        ) +
                            ' x ' +
                            t(
                                'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.technicalProductivity'
                            )}
                    </div>
                </TooltipFormulaRightSide>
            </TooltipFormula>
            <TooltipFormula>
                <div>
                    {t(
                        'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.technicalProductivity'
                    ) + ' ='}
                </div>
                <TooltipFormulaRightSide>
                    <div>
                        {t(
                            'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.productiveHours'
                        )}
                    </div>
                    <CustomDivider />
                    <div>
                        {t(
                            'settings.workshopPlannerSettings.general.showTechnicianCapacityTooltip.workedHours'
                        )}
                    </div>
                </TooltipFormulaRightSide>
                <div>{'x 100'}</div>
            </TooltipFormula>
        </TooltipContentContainer>
    );
};

const DivPagesContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '40px',
});

const CustomTitle = styled('div')({
    padding: '18px 0px 10px 0px',
    letterSpacing: 0,
});

const DivTooltipContainer = styled('div')({
    display: 'flex',
    alignItems: 'center',
    marginLeft: '6px',
});

const TooltipContentContainer = styled('div')({
    display: 'flex',
    flexDirection: 'column',
    gap: '24px',
    width: '618px',
    fontSize: 14,
    fontWeight: 400,
    lineHeight: '17px',
});

const StyledTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.neutral[2],
        color: theme.palette.neutral[7],
        filter: `drop-shadow(0px 2px 1px ${theme.palette.neutral[4]})`,
        borderRadius: 10,
        border: `solid 1px, ${theme.palette.neutral[5]}`,
        padding: '24px 27px',
        maxWidth: 'none',
    },

    [`& .${tooltipClasses.arrow}::before`]: {
        backgroundColor: theme.palette.neutral[2],
    },
}));

const TooltipFormula = styled('div')({
    display: 'flex',
    gap: '8px',
    alignItems: 'center',
    fontStyle: 'italic',
});

const TooltipFormulaRightSide = styled('div')({
    display: 'flex',
    gap: '2px',
    flexDirection: 'column',
});

const CustomDivider = styled('div')(({ theme }) => ({
    borderTop: `solid 1px ${theme.palette.neutral[7]}`,
}));

export default WorkshopPlannerGeneralSettings;
