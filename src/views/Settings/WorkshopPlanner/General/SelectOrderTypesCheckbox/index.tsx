import { Box } from '@mui/material';
import InfoText from 'common/components/InfoText';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useEffect, useState } from 'react';
import OrderTypePickerMultiple from 'views/Components/OrderTypePickerMultiple';
import { BooleanSettingControl } from 'views/Settings/common';

type Settings = {
    enabled: boolean;
    orderTypeIds: string[];
};

type SelectOrderTypesCheckboxProps = {
    hintText: string;
    labelText: string;
    settings: Settings;
    onSettingsChanged: (settings: Settings) => void;
};

type InternalState = {
    orderTypeIds: string[];
    enabled: boolean;
};

function defaultInternalState(): InternalState {
    return {
        orderTypeIds: [],
        enabled: false,
    };
}

const SelectOrderTypesCheckbox = ({
    hintText,
    labelText,
    settings,
    onSettingsChanged,
}: SelectOrderTypesCheckboxProps) => {
    const { t } = useAppTranslation();
    const [state, setState] = useState<InternalState>(defaultInternalState);

    useEffect(() => {
        setState(settings);
    }, [settings]);

    return (
        <>
            <BooleanSettingControl
                label={labelText}
                value={state.enabled}
                hasHint={!state.enabled}
                hintText={hintText}
                onChange={(value) => {
                    onSettingsChanged({
                        ...state,
                        enabled: value,
                    });
                }}
                slotProps={{
                    layout: {
                        expanded: state.enabled,
                        childrenBelow: (
                            <>
                                <InfoText sx={{ mt: 2 }}>
                                    <span>{hintText}</span>
                                </InfoText>

                                <Box sx={{ display: 'flex', gap: 8, mt: 2 }}>
                                    <InputWrapper
                                        sx={{ width: 200 }}
                                        label={t('orderTypePicker.itWillAppearForOrders')}
                                    >
                                        <OrderTypePickerMultiple
                                            orderTypeIds={state.orderTypeIds}
                                            onChange={(orderTypeIds) =>
                                                onSettingsChanged({
                                                    ...state,
                                                    orderTypeIds,
                                                })
                                            }
                                        />
                                    </InputWrapper>
                                </Box>
                            </>
                        ),
                    },
                }}
            />
        </>
    );
};

export default SelectOrderTypesCheckbox;
