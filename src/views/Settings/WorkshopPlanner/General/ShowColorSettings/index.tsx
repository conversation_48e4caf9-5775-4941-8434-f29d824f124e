import { useMutation, useQuery } from '@tanstack/react-query';
import WpSettingsApi, {
    ShowColorTypeSettingsDto,
    ShowJobBlockColorType,
    ShowTowerColorType,
} from 'api/workshopPlanner/Settings/General/GeneralSettings';
import { WarningConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import debounce from 'lodash/debounce';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getErrorResponse, hasCode } from 'services/Server';
import { useAppDispatch } from 'store';
import { loadGlobalSettingsThunk } from 'store/slices/globalSettingsSlice';
import { useRepairShopCustomAppointmentReasons } from 'views/Appointments/common';
import { RadioButtonArrayControl, SettingsSection } from 'views/Settings/common';

const WorkshopPlannerShowColorTypeSettings = () => {
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const dispatch = useAppDispatch();
    const [settings, setSettings] = useState<ShowColorTypeSettingsDto>();
    const { isCustomApptReasonEnabled } = useRepairShopCustomAppointmentReasons();

    const [disabledShowTowerColor, setDisabledTowerColor] = useState<ShowTowerColorType | null>(
        null
    );
    const [disabledShowJobBlockColor, setDisabledJobBlockColor] =
        useState<ShowJobBlockColorType | null>(null);

    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const [configuration, setConfiguration] = useState<string>('');
    const [selectedOption, setSelectedOption] = useState<string>('');
    const [selectedOptionError, setSelectedOptionError] = useState<string>('');

    const navigate = useNavigate();

    const { isLoading } = useQuery(
        ['settings', 'wp', 'show-color-type'],
        WpSettingsApi.getShowColorTypeSettings,
        {
            onSuccess: (d) => {
                setSettings(d);
                changeDisabledTowerColor(d.showJobBlockColorType);
                changeDisabledJobBlockColor(d.showTowerColorType);
            },
            cacheTime: 120000,
            staleTime: 200,
        }
    );

    const mutateSettings = useMutation(
        async ({ settings }: { settings: ShowColorTypeSettingsDto; toasterText?: string }) => {
            return await WpSettingsApi.updateShowColorTypeSettings(settings);
        },
        {
            onError: (error) => {
                const response = getErrorResponse(error);

                if (response) {
                    if (hasCode(response, 'General.WP.IncompleteColorConfiguration')) {
                        setSelectedOptionError(response.meta.selectedOption);
                        setConfiguration(
                            t(
                                `settings.workshopPlannerSettings.general.incompleteColorConfigurationModal.${response.meta.configuration}`
                            )
                        );
                        setSelectedOption(
                            t(
                                `settings.workshopPlannerSettings.general.incompleteColorConfigurationModal.${response.meta.selectedOption}`
                            )
                        );
                        setIsModalOpen(true);
                        return;
                    }
                    toasters.danger(t('toasters.errorOccurredWhenSaving'));
                } else {
                    toasters.danger(t('toasters.unexpectedError'), t('toasters.errorOccurred'));
                }
            },
            onSuccess: (data, { toasterText }) => {
                scheduleGlobalSettingsRefetch();
                setSettings(data);
                changeDisabledTowerColor(data.showJobBlockColorType);
                changeDisabledJobBlockColor(data.showTowerColorType);

                toasters.success(
                    toasterText ?? t('toasters.settingSuccessfullyUpdated'),
                    t('toasters.settingUpdated')
                );
            },
        }
    );

    const scheduleGlobalSettingsRefetch = useMemo(
        () =>
            debounce(() => {
                dispatch(loadGlobalSettingsThunk());
            }, 5000),
        [dispatch]
    );

    const updateSettings = (update: Partial<ShowColorTypeSettingsDto>, toasterText?: string) => {
        if (!settings) {
            return;
        }

        mutateSettings.mutate({
            settings: {
                ...settings,
                ...update,
            },
            toasterText,
        });
    };

    const changeDisabledTowerColor = (showJobBlockColorType: ShowJobBlockColorType) => {
        switch (showJobBlockColorType) {
            case 'ByOrderType':
                setDisabledTowerColor('ByOrderType');
                break;
            case 'ByReasonForAppointment':
                setDisabledTowerColor('ByReasonForAppointment');
                break;
            case 'ByServiceAdvisor':
                setDisabledTowerColor('ByServiceAdvisor');
                break;
            case 'NoColor':
            default:
                setDisabledTowerColor(null);
                break;
        }
    };

    const changeDisabledJobBlockColor = (showTowerColorType: ShowTowerColorType) => {
        switch (showTowerColorType) {
            case 'ByOrderType':
                setDisabledJobBlockColor('ByOrderType');
                break;
            case 'ByReasonForAppointment':
                setDisabledJobBlockColor('ByReasonForAppointment');
                break;
            case 'ByServiceAdvisor':
                setDisabledJobBlockColor('ByServiceAdvisor');
                break;
            default:
                setDisabledJobBlockColor(null);
                break;
        }
    };

    const onConfirmModal = (selectedOptionError: string) => {
        setIsModalOpen(false);
        if (selectedOptionError === 'ByOrderType') navigate('/settings/general/ordertypes');
        else if (selectedOptionError === 'ByReasonForAppointment')
            navigate('/settings/general/appointmentreasons');
    };

    const onCloseModal = () => {
        setIsModalOpen(false);
        setConfiguration('');
        setSelectedOption('');
    };

    return (
        <>
            <SettingsSection>
                <RadioButtonArrayControl<ShowTowerColorType>
                    disabled={isLoading}
                    onChange={(value) => {
                        updateSettings(
                            { showTowerColorType: value },
                            t(
                                'settings.workshopPlannerSettings.general.notifications.settingsUpdatedSuccessfully'
                            )
                        );
                    }}
                    label={t('settings.workshopPlannerSettings.general.showTowerColor.title')}
                    value={settings?.showTowerColorType || 'ByServiceAdvisor'}
                    values={[
                        {
                            id: 'ByServiceAdvisor',
                            value: 'ByServiceAdvisor',
                            label: t(
                                'settings.workshopPlannerSettings.general.showTowerColor.byServiceAdvisor'
                            ),
                            disabled: disabledShowTowerColor === 'ByServiceAdvisor',
                        },
                        {
                            id: 'ByOrderType',
                            value: 'ByOrderType',
                            label: t(
                                'settings.workshopPlannerSettings.general.showTowerColor.byOrderType'
                            ),
                            disabled: disabledShowTowerColor === 'ByOrderType',
                        },
                        {
                            id: 'ByReasonForAppointment',
                            value: 'ByReasonForAppointment',
                            label: t(
                                'settings.workshopPlannerSettings.general.showTowerColor.byReasonForAppointment'
                            ),
                            disabled:
                                !isCustomApptReasonEnabled ||
                                disabledShowTowerColor === 'ByReasonForAppointment',
                            disabledTooltip: isCustomApptReasonEnabled
                                ? undefined
                                : t(
                                      'settings.workshopPlannerSettings.general.showTowerColor.reasonForAppointmentInactive'
                                  ),
                        },
                    ]}
                    hasHint={true}
                    hintText={t(
                        'settings.workshopPlannerSettings.general.showTowerColor.infoTooltipText'
                    )}
                    hintPosition={'top'}
                />
            </SettingsSection>
            <SettingsSection>
                <RadioButtonArrayControl<ShowJobBlockColorType>
                    disabled={isLoading}
                    onChange={(value) => {
                        updateSettings(
                            { showJobBlockColorType: value },
                            t(
                                'settings.workshopPlannerSettings.general.notifications.settingsUpdatedSuccessfully'
                            )
                        );
                    }}
                    label={t('settings.workshopPlannerSettings.general.showJobBlockColor.title')}
                    value={settings?.showJobBlockColorType || 'ByServiceAdvisor'}
                    values={[
                        {
                            id: 'ByServiceAdvisor',
                            value: 'ByServiceAdvisor',
                            label: t(
                                'settings.workshopPlannerSettings.general.showJobBlockColor.byServiceAdvisor'
                            ),
                            disabled: disabledShowJobBlockColor === 'ByServiceAdvisor',
                        },
                        {
                            id: 'ByOrderType',
                            value: 'ByOrderType',
                            label: t(
                                'settings.workshopPlannerSettings.general.showJobBlockColor.byOrderType'
                            ),
                            disabled: disabledShowJobBlockColor === 'ByOrderType',
                        },
                        {
                            id: 'ByReasonForAppointment',
                            value: 'ByReasonForAppointment',
                            label: t(
                                'settings.workshopPlannerSettings.general.showJobBlockColor.byReasonForAppointment'
                            ),
                            disabled:
                                !isCustomApptReasonEnabled ||
                                disabledShowJobBlockColor === 'ByReasonForAppointment',
                            disabledTooltip: isCustomApptReasonEnabled
                                ? undefined
                                : t(
                                      'settings.workshopPlannerSettings.general.showJobBlockColor.reasonForAppointmentInactive'
                                  ),
                        },
                        {
                            id: 'NoColor',
                            value: 'NoColor',
                            label: t(
                                'settings.workshopPlannerSettings.general.showJobBlockColor.noColor'
                            ),
                        },
                    ]}
                    hasHint={true}
                    hintText={t(
                        'settings.workshopPlannerSettings.general.showJobBlockColor.infoTooltipText'
                    )}
                    hintPosition={'top'}
                />
            </SettingsSection>

            <WarningConfirmationPopup
                open={isModalOpen}
                title={t(
                    'settings.workshopPlannerSettings.general.incompleteColorConfigurationModal.title'
                )}
                body={
                    <span style={{ lineHeight: '20px' }}>
                        {t(
                            'settings.workshopPlannerSettings.general.incompleteColorConfigurationModal.body',
                            { configuration: configuration, selectedOption: selectedOption }
                        )}
                    </span>
                }
                cancel={t(
                    'settings.workshopPlannerSettings.general.incompleteColorConfigurationModal.cancel'
                )}
                confirm={t(
                    'settings.workshopPlannerSettings.general.incompleteColorConfigurationModal.goToSettings'
                )}
                onConfirm={() => onConfirmModal(selectedOptionError)}
                onClose={onCloseModal}
            />
        </>
    );
};

export default WorkshopPlannerShowColorTypeSettings;
