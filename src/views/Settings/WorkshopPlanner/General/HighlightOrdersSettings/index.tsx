import { Grid, styled } from '@mui/material';
import InfoText from 'common/components/InfoText';
import InfoTooltip from 'common/components/InfoTooltip';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useEffect, useState } from 'react';
import { SettingLayoutControl } from 'views/Settings/common';
import ResettableNumberField from 'views/Settings/common/ResettableNumberField';
import SettingsControl from 'views/Settings/common/SettingLayoutControl';
import SettingLayoutLabel from 'views/Settings/common/SettingLayoutLabel';
import SettingsCheckbox from 'views/Settings/common/SettingsCheckbox';

type Settings = {
    deliveryPromiseDateChecked: boolean;
    daysPausedChecked: boolean;
    preventiveDays: number;
    urgentDays: number;
};

type HighlightOrdersSettingsProps = {
    settings: Settings;
    onSettingsChanged: (settings: Settings) => void;
};

export default function HighlightOrdersSettings({
    settings,
    onSettingsChanged,
}: HighlightOrdersSettingsProps) {
    const { t } = useAppTranslation();
    const toasters = useToasters();

    const [state, setState] = useState<Settings>({
        deliveryPromiseDateChecked: true,
        daysPausedChecked: false,
        preventiveDays: 2,
        urgentDays: 5,
    });

    useEffect(() => setState(settings), [settings]);

    const showInvalidDaysAlert = () => {
        toasters.danger(
            t('settings.workshopPlannerSettings.pausedOrders.urgentShouldBeHigherWarningText'),
            t('settings.workshopPlannerSettings.pausedOrders.urgentShouldBeHigherWarningTitle')
        );
    };

    return (
        <SettingsControl
            label={t('settings.workshopPlannerSettings.pausedOrders.highlightOrdersBy')}
        >
            <DivCheckBoxSettingContainer>
                <SettingsCheckbox
                    name={'deliveryPromiseDateChk'}
                    onChange={(_, value) =>
                        onSettingsChanged({ ...state, deliveryPromiseDateChecked: value })
                    }
                    checked={state.deliveryPromiseDateChecked}
                />
                <span>
                    {t('settings.workshopPlannerSettings.pausedOrders.deliveryPromiseDate')}
                </span>
                <InfoTooltip
                    text={t(
                        'settings.workshopPlannerSettings.pausedOrders.deliveryPromiseDateTooltip'
                    )}
                />
            </DivCheckBoxSettingContainer>
            <DivCheckBoxSettingContainer>
                {state.daysPausedChecked ? (
                    <DivExpandedCheckBoxSettingContainer>
                        <DivCheckBoxSettingContainer>
                            <SettingsCheckbox
                                name={'daysPausedChk'}
                                onChange={(_, value) =>
                                    onSettingsChanged({ ...state, daysPausedChecked: value })
                                }
                                checked={state.daysPausedChecked}
                            />
                            <span>
                                {t('settings.workshopPlannerSettings.pausedOrders.daysPaused')}
                            </span>
                        </DivCheckBoxSettingContainer>
                        <InfoText
                            sx={{
                                alignItems: 'flex-start',
                                marginLeft: '6px',
                                marginBottom: '24px',
                            }}
                        >
                            {t('settings.workshopPlannerSettings.pausedOrders.daysPausedTooltip')}
                        </InfoText>
                        <DivDaysSettingsLayoutContainer>
                            <SettingLayoutControl
                                label={t(
                                    'settings.workshopPlannerSettings.pausedOrders.daysUntilPauseWarning'
                                )}
                                labelMD={3}
                                childrenMD={9}
                                alignItems="center"
                            >
                                <GridDaysSettingsContainer container>
                                    <GridDaysSettingItem item xs={12} sm={12} md={12} lg={5}>
                                        <DaysSettingLabel>
                                            {t(
                                                'settings.workshopPlannerSettings.pausedOrders.preventive'
                                            )}
                                        </DaysSettingLabel>
                                        <ResettableNumberField
                                            template={`{0} ${
                                                state.preventiveDays === 1
                                                    ? t(
                                                          'settings.workshopPlannerSettings.pausedOrders.day'
                                                      )
                                                    : t(
                                                          'settings.workshopPlannerSettings.pausedOrders.days'
                                                      )
                                            }`}
                                            validate={(v) =>
                                                Number.isInteger(v) &&
                                                v >= 0 &&
                                                v <= state.urgentDays
                                            }
                                            value={state.preventiveDays}
                                            placeholder="0 days"
                                            onValueChange={(value) =>
                                                onSettingsChanged({
                                                    ...state,
                                                    preventiveDays: value,
                                                })
                                            }
                                            name="preventivePausedDays"
                                            editIfDisabled
                                            onInvalidValueChange={showInvalidDaysAlert}
                                        />
                                    </GridDaysSettingItem>
                                    <GridDaysSettingItem item xs={12} sm={12} md={12} lg={5}>
                                        <DaysSettingLabel>
                                            {t(
                                                'settings.workshopPlannerSettings.pausedOrders.urgent'
                                            )}
                                        </DaysSettingLabel>
                                        <ResettableNumberField
                                            template={`{0} ${
                                                state.urgentDays === 1
                                                    ? t(
                                                          'settings.workshopPlannerSettings.pausedOrders.day'
                                                      )
                                                    : t(
                                                          'settings.workshopPlannerSettings.pausedOrders.days'
                                                      )
                                            }`}
                                            validate={(v) =>
                                                Number.isInteger(v) &&
                                                v > state.preventiveDays &&
                                                v <= 2147483647
                                            }
                                            value={state.urgentDays}
                                            placeholder="0 days"
                                            onValueChange={(value) =>
                                                onSettingsChanged({ ...state, urgentDays: value })
                                            }
                                            name="urgentPausedDays"
                                            editIfDisabled
                                            onInvalidValueChange={showInvalidDaysAlert}
                                        />
                                    </GridDaysSettingItem>
                                </GridDaysSettingsContainer>
                            </SettingLayoutControl>
                        </DivDaysSettingsLayoutContainer>
                    </DivExpandedCheckBoxSettingContainer>
                ) : (
                    <>
                        <SettingsCheckbox
                            name={'daysPausedChk'}
                            onChange={(_, value) =>
                                onSettingsChanged({ ...state, daysPausedChecked: value })
                            }
                            checked={state.daysPausedChecked}
                        />
                        <span>{t('settings.workshopPlannerSettings.pausedOrders.daysPaused')}</span>
                        <InfoTooltip
                            text={t(
                                'settings.workshopPlannerSettings.pausedOrders.daysPausedTooltip'
                            )}
                        />
                    </>
                )}
            </DivCheckBoxSettingContainer>
        </SettingsControl>
    );
}

const DivCheckBoxSettingContainer = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: 2,
    marginBottom: '8px',
    color: theme.palette.neutral[7],
}));

const DivExpandedCheckBoxSettingContainer = styled('div')(({ theme }) => ({
    borderRadius: 12,
    border: `1px solid ${theme.palette.neutral[5]}`,
    padding: 11,
    margin: '0 -12px',
}));

const DivDaysSettingsLayoutContainer = styled('div')({
    display: 'flex',
    marginLeft: '6px',
});

const GridDaysSettingsContainer = styled(Grid)({
    alignItems: 'center',
});

const GridDaysSettingItem = styled(Grid)({
    display: 'flex',
    alignItems: 'center',
    marginRight: '32px',
});

const DaysSettingLabel = styled(SettingLayoutLabel)({
    minWidth: '65px',
    marginRight: '8px',
});
