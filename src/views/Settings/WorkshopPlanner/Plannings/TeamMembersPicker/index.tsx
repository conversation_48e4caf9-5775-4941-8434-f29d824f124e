import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { OptionStyle } from 'common/styles/OptionStyle';
import { useMemo } from 'react';
import { useAppSelector } from 'store';
import { selectTeamMembers } from 'store/slices/teamMembers/selectors';
import Dropdown from '../../../../../common/components/Inputs/Dropdown';
import { OptionData } from '../../../../../common/components/Inputs/Dropdown/DataOption';

interface ITeamMembersSelectorProps {
    values: string[] | null;
    onChange: (selectedIds: string[] | null) => void;
}

const TeamMembersPicker = ({
    values: selectedTeamMemberIds,
    onChange,
}: ITeamMembersSelectorProps) => {
    const { t } = useAppTranslation();
    const teamMembersAll = useAppSelector(selectTeamMembers);
    const allMembersValue = Number.MAX_VALUE.toString();

    const teamMembers = useMemo(() => {
        const sortedMembers = teamMembersAll
            .slice()
            .sort((a, b) => a.displayName.localeCompare(b.displayName));
        return sortedMembers;
    }, [teamMembersAll]);

    const teamMembersOptions = useMemo(() => {
        const options = teamMembers.map((v) => ({
            value: v.userKey!,
            label: v.displayName,
        }));

        options.unshift({
            value: allMembersValue,
            label: t('settings.workshopPlannerSettings.planning.editModal.allTeamMembers'),
        });

        return options;
    }, [teamMembers]);

    const selectedTeamMembers = useMemo(() => {
        if (!selectedTeamMemberIds) {
            return [];
        }

        if (selectedTeamMemberIds.length === 0) {
            return [teamMembersOptions[0]];
        }

        return teamMembersOptions.filter((o) => selectedTeamMemberIds.some((x) => x === o.value));
    }, [selectedTeamMemberIds]);
    const placeholder = useMemo(() => {
        if (selectedTeamMembers.length === 0)
            return t(
                'settings.workshopPlannerSettings.planning.editModal.selectTeamMembersPlaceholder'
            );
        if (selectedTeamMembers.some((v) => v!.value === allMembersValue)) {
            return t('settings.workshopPlannerSettings.planning.editModal.allTeamMembers');
        }

        const res = selectedTeamMembers[0]!.label;

        return res;
    }, [t, selectedTeamMembers, selectedTeamMemberIds]);

    const handleOnChange = (valuesArg: readonly OptionData[]) => {
        let values = [...valuesArg];
        if (values.length === 1 && values.some((value) => value.value === allMembersValue)) {
            onChange([]);

            return;
        }
        if (values.length > 1) {
            const first = values[0];
            const last = values[values.length - 1];

            if (first.value === allMembersValue) {
                values = values.splice(1, 1); //exclude "all"
            } else if (last.value === allMembersValue) {
                values = []; //leave only "all"
            }
        }

        const ids = values.map((m) => m!.value);
        onChange(ids);
    };

    return (
        <Dropdown
            name="teamMemberPicker"
            cmosVariant="grey"
            options={teamMembersOptions}
            label={t('settings.workshopPlannerSettings.planning.editModal.selectTeamMembersHeader')}
            optionStyle={OptionStyle.checkbox}
            isRequired={true}
            showValidationIndicators={true}
            value={selectedTeamMembers}
            onChange={handleOnChange}
            multiple
            placeholder={placeholder}
            showValueCounterAfter={2}
        />
    );
};

export default TeamMembersPicker;
