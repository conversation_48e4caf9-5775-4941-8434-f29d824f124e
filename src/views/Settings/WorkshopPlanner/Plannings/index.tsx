import { useQuery } from '@tanstack/react-query';
import WpPlanningApi, { PlanningDto } from 'api/workshopPlanner/plannings';
import { Button } from 'common/components/Button';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { addPlanning, setPlannings } from 'store/slices/Settings/WorkshopPlanner/plannings';
import ensureTeamMembers from 'store/slices/teamMembers/thunks/ensureTeamMembers';
import PageContent from 'views/Components/Page';
import AddPlanningModal from './AddPlanningModal';
import PlanningList from './PlanningsList';
import { usePlanningsSettingsStyles } from './css';

const WorkshopPlannerPlanningsSettings = () => {
    const styles = usePlanningsSettingsStyles();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const dispatch = useDispatch();

    const [addPlanningModalOpen, setAddPlanningModalOpen] = useState<boolean>(false);

    const { isLoading } = useQuery(['plannings', 'settings'], () => WpPlanningApi.getPlannings(), {
        onSuccess: (response) => {
            const list = response || [];
            dispatch(setPlannings(list));
        },
        onError: (error) => {
            toasters.danger(t('toasters.errorOccurredWhenLoading'), t('toasters.errorOccurred'));
        },

        cacheTime: Infinity,
    });

    useEffect(() => {
        dispatch(ensureTeamMembers());
    }, []);

    const openAddPlanningsModal = () => {
        setAddPlanningModalOpen(true);
    };

    const onPlanningAdded = useCallback((planning: PlanningDto) => {
        dispatch(addPlanning(planning));
        closeAddPlanningModal();

        toasters.success(
            t('settings.workshopPlannerSettings.planning.notifications.planningCreatedBody'),
            t('settings.workshopPlannerSettings.planning.notifications.planningCreatedTitle')
        );
    }, []);

    const closeAddPlanningModal = useCallback(() => {
        setAddPlanningModalOpen(false);
    }, []);

    return (
        <PageContent paddedX paddedY>
            <span className={styles.header}>
                {t('settings.workshopPlannerSettings.tabs.plannings')}
            </span>
            <div className={styles.list}>
                <PlanningList isLoadingInProgress={isLoading} />
            </div>
            <div>
                <Button
                    customStyles={{
                        backgroundColor: `${Colors.Neutral2}!important`,
                        /* Neutrales/4 */
                        minWidth: '189px',
                    }}
                    buttonInnercustomStyles={{ width: '100%', justifyContent: 'space-between' }}
                    cmosVariant={'stroke'}
                    iconPosition="right"
                    Icon={PlusIcon}
                    onClick={openAddPlanningsModal}
                    label={t('settings.workshopPlannerSettings.planning.addPlanning')}
                />
            </div>
            <AddPlanningModal
                open={addPlanningModalOpen}
                onAdded={onPlanningAdded}
                onClose={closeAddPlanningModal}
            />
        </PageContent>
    );
};

export default WorkshopPlannerPlanningsSettings;
