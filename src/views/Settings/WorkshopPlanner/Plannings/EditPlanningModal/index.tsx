import { Box } from '@mui/material';
import WpPlanningApi, { PlanningDto } from 'api/workshopPlanner/plannings';
import { Button } from 'common/components/Button';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import TeamMembersPicker from 'views/Settings/WorkshopPlanner/Plannings/TeamMembersPicker';
import useStyles from './css';

interface IEditModalProps {
    open: boolean;
    planningId: number;
    onClose: () => void;
    onEdit: (planning: PlanningDto) => void;
}

const EditPlanningModal = ({ open, planningId, onClose, onEdit }: IEditModalProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    const [name, setName] = useState('');
    const [selectedTeamMemberIds, setSelectedTeamMemberIds] = useState<string[] | null>(null);
    const toaster = useToasters();

    const prevName = useRef('');
    const prevMemberIds = useRef<string[]>([]);

    const isEditButtonDisabled = useMemo(() => {
        return (
            name.length === 0 ||
            !selectedTeamMemberIds ||
            (name.toLocaleLowerCase() === prevName.current.toLocaleLowerCase() &&
                _.isEqual(prevMemberIds.current, selectedTeamMemberIds))
        );
    }, [selectedTeamMemberIds, name]);

    useEffect(() => {
        const fetchDetails = async () => {
            const details = await WpPlanningApi.getPlanningDetails(planningId);
            return details;
        };

        if (open) {
            fetchDetails().then((res) => {
                prevName.current = res.name;
                prevMemberIds.current = res.memberIds;

                setName(res.name);
                setSelectedTeamMemberIds(res.memberIds);
            });
        }
    }, [open, planningId]);

    const onEditClick = async () => {
        const updatedPlanning = await WpPlanningApi.updatePlanning({
            id: planningId,
            name: name,
            memberIds: selectedTeamMemberIds!,
        }).catch((err) => {
            if (err.code === 'EntityDuplicated') {
                toaster.danger(
                    t(
                        'settings.workshopPlannerSettings.planning.notifications.planningAlreadyExistsBody'
                    ),
                    t(
                        'settings.workshopPlannerSettings.planning.notifications.planningAlreadyExistsTitle'
                    )
                );
            }
        });

        if (!updatedPlanning) return;

        onEdit(updatedPlanning);
        clearData();
    };

    const clearData = useCallback(() => {
        setName('');
        setSelectedTeamMemberIds(null);

        prevName.current = '';
        prevMemberIds.current = [];
    }, []);

    const onCloseClick = useCallback(() => {
        clearData();

        onClose();
    }, [onClose, clearData]);

    return (
        <Modal open={open}>
            <Box className={classes.content}>
                <Box display="flex" width="100%" justifyContent="space-between">
                    <h4 style={{ color: Colors.Neutral7, fontSize: '18px' }}>
                        {t('settings.workshopPlannerSettings.planning.editModal.title')}
                    </h4>
                    <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="space-between"
                        gap={1.25}
                    >
                        <Button
                            customStyles={{ width: '168px' }}
                            onClick={onCloseClick}
                            label={t(
                                'settings.workshopPlannerSettings.planning.editModal.cancelButton'
                            )}
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            cmosSize={'medium'}
                        />
                        <Button
                            customStyles={{ width: '225px' }}
                            disabled={isEditButtonDisabled}
                            onClick={onEditClick}
                            label={t(
                                'settings.workshopPlannerSettings.planning.editModal.saveChanges'
                            )}
                            cmosVariant={'filled'}
                            cmosSize={'medium'}
                            color={Colors.Success}
                        />
                    </Box>
                </Box>

                <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: '1fr 1fr', mt: 1 }}>
                    <TextFormField
                        isRequired
                        showValidationIndicators
                        name="name"
                        label={t('settings.workshopPlannerSettings.planning.editModal.nameHeader')}
                        placeholder={t(
                            'settings.workshopPlannerSettings.planning.editModal.namePlaceholder'
                        )}
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        maxLength={30}
                        cmosVariant="grey"
                    />

                    <TeamMembersPicker
                        values={selectedTeamMemberIds}
                        onChange={(v) => {
                            setSelectedTeamMemberIds(v);
                        }}
                    />
                </Box>
            </Box>
        </Modal>
    );
};

export default EditPlanningModal;
