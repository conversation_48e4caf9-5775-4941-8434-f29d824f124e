import { useMutation } from '@tanstack/react-query';
import WpPlanningApi, { PlanningDto, ReorderPlanningDto } from 'api/workshopPlanner/plannings';
import AreaSpinner from 'common/components/AreaSpinner';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { useCallback, useEffect, useState } from 'react';
import { DragDropContext, Draggable, DropResult, Droppable } from 'react-beautiful-dnd';
import { useDispatch, useSelector } from 'react-redux';
import { deletePlanning, updatePlanning } from 'store/slices/Settings/WorkshopPlanner/plannings';
import { selectPlanningsSettingsList } from 'store/slices/Settings/selector';
import EditPlanningModal from '../EditPlanningModal';
import Planning from '../Planning';
import { usePlanningListStyles } from './css';

const reorder = (list: PlanningDto[], startIndex: number, endIndex: number) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
};

interface PlanningListProps {
    isLoadingInProgress: boolean;
}

const PlanningList = ({ isLoadingInProgress }: PlanningListProps) => {
    const styles = usePlanningListStyles();
    const { t } = useAppTranslation();
    const toaster = useToasters();
    const [list, setList] = useState<PlanningDto[]>([]);
    const [isDeletePopupOpen, setIsDeletePopupOpen] = useState<boolean>(false);

    const [editPlanningModalOpen, setEditPlanningModalOpen] = useState<boolean>(false);
    const [editPlanningId, setEditPlanningId] = useState<number | null>(null);
    const [planningToDeleteId, setplanningToDeleteId] = useState<number | null>();

    const plannings = useSelector(selectPlanningsSettingsList);

    const dispatch = useDispatch();

    const onDragEnd = async (result: DropResult) => {
        if (!result.destination || result.destination.index === result.source.index) return;

        const newList = reorder(list, result.source.index, result.destination.index);

        const reorderplannings = newList.map<ReorderPlanningDto>((value) => ({
            id: value.id,
            order: newList.lastIndexOf(value) + 1,
        }));

        setList(newList);

        const data = await WpPlanningApi.reorderPlannings(reorderplannings).catch((error) => {
            toaster.danger(t('toasters.errorOccurredWhenLoading'), t('toasters.errorOccurred'));
            setList(list);
        });

        if (data) {
            toaster.success(
                t(
                    'settings.workshopPlannerSettings.planning.notifications.planningUpdatedSuccessfullyBody'
                ),
                t(
                    'settings.workshopPlannerSettings.planning.notifications.planningUpdatedSuccessfullyTitle'
                )
            );
        }
    };

    const onPlanningEdited = (planning: PlanningDto) => {
        dispatch(updatePlanning(planning));
        editCompleted();

        toaster.success(
            t('settings.workshopPlannerSettings.planning.notifications.planningEditedBody'),
            t('settings.workshopPlannerSettings.planning.notifications.planningEditedTitle')
        );
    };

    const editPlanningHandler = useCallback((planning: PlanningDto) => {
        setEditPlanningId(planning.id);
        setEditPlanningModalOpen(true);
    }, []);

    const deletePlanningHandler = useCallback((planning: PlanningDto) => {
        setplanningToDeleteId(planning.id);
        setIsDeletePopupOpen(true);
    }, []);

    const { mutate: deleteMutate } = useDeleteMutation((data) => {
        dispatch(deletePlanning(data.id));
        setIsDeletePopupOpen(false);
    });

    const onDeleteConfirm = () => {
        if (planningToDeleteId) {
            deleteMutate(planningToDeleteId);
        }
    };

    useEffect(() => {
        const orderedList = plannings.slice().sort((a, b) => a.order - b.order);
        setList(orderedList);
    }, [plannings]);

    const editCompleted = () => {
        setEditPlanningModalOpen(false);
        setEditPlanningId(null);
    };

    const onEditClosed = () => {
        editCompleted();
    };

    return (
        <>
            {isLoadingInProgress ? (
                <div className={styles.loader}>
                    <AreaSpinner />
                </div>
            ) : (
                list && (
                    <div>
                        <div className={styles.root}>
                            <DragDropContext onDragEnd={onDragEnd}>
                                <Droppable droppableId={`list-plannings`}>
                                    {(provided) => (
                                        <div ref={provided.innerRef} {...provided.droppableProps}>
                                            {list.map((planning: PlanningDto, index: number) => (
                                                <Draggable
                                                    draggableId={planning.name.toString()}
                                                    index={index}
                                                    key={`planning-${planning.name}`}
                                                >
                                                    {(provided) => (
                                                        <div
                                                            ref={provided.innerRef}
                                                            {...provided.draggableProps}
                                                        >
                                                            <Planning
                                                                planning={planning}
                                                                onEdit={editPlanningHandler}
                                                                onDelete={deletePlanningHandler}
                                                                dragOptions={
                                                                    provided.dragHandleProps ??
                                                                    undefined
                                                                }
                                                            />
                                                        </div>
                                                    )}
                                                </Draggable>
                                            ))}
                                            {provided.placeholder}
                                        </div>
                                    )}
                                </Droppable>
                            </DragDropContext>
                        </div>
                        <DeleteConfirmationPopup
                            open={isDeletePopupOpen}
                            title={t(
                                'settings.workshopPlannerSettings.planning.popup.deletePlanningTitle'
                            )}
                            body={
                                <div>
                                    {t(
                                        'settings.workshopPlannerSettings.planning.popup.deletePlanningBody'
                                    )}
                                </div>
                            }
                            cancel={t(
                                'settings.workshopPlannerSettings.planning.popup.doNotDeletePlanning'
                            )}
                            confirm={t(
                                'settings.workshopPlannerSettings.planning.popup.deletePlanning'
                            )}
                            onConfirm={onDeleteConfirm}
                            onClose={() => {
                                setplanningToDeleteId(null);
                                setIsDeletePopupOpen(false);
                            }}
                        />

                        <EditPlanningModal
                            open={editPlanningModalOpen}
                            planningId={editPlanningId!}
                            onEdit={onPlanningEdited}
                            onClose={onEditClosed}
                        />
                    </div>
                )
            )}
        </>
    );
};

const useDeleteMutation = (onSuccess?: (data: PlanningDto) => void) => {
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const mutation = useMutation((planningId: number) => WpPlanningApi.deletePlanning(planningId), {
        onSuccess: (data) => {
            onSuccess && onSuccess(data);
            toasters.success(
                t(
                    'settings.workshopPlannerSettings.planning.notifications.planningDeletedSuccessfullyBody'
                ),
                t(
                    'settings.workshopPlannerSettings.planning.notifications.planningDeletedSuccessfullyTitle'
                )
            );
        },
        onError: () => {
            toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
        },
    });

    return mutation;
};

export default PlanningList;
