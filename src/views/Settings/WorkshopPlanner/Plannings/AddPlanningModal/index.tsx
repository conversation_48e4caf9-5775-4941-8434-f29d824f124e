import { Box, Grid } from '@mui/material';
import WpPlanningApi, { PlanningDto } from 'api/workshopPlanner/plannings';
import { Button } from 'common/components/Button';
import TextFormField from 'common/components/Inputs/TextField';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { useCallback, useMemo, useState } from 'react';
import TeamMembersPicker from '../TeamMembersPicker';
import useStyles from './css';

interface IAddModalProps {
    open: boolean;
    onClose: () => void;
    onAdded: (planning: PlanningDto) => void;
}

const AddPlanningModal = ({ open, onClose, onAdded }: IAddModalProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    const [name, setName] = useState('');
    const [selectedTeamMemberIds, setSelectedTeamMemberIds] = useState<string[] | null>(null);
    const toaster = useToasters();
    const isAddButtonDisabled = useMemo(() => {
        return name.length === 0 || !selectedTeamMemberIds;
    }, [selectedTeamMemberIds, name]);

    const onAddClick = async () => {
        const createdPlanning = await WpPlanningApi.createPlanning({
            name: name,
            memberIds: selectedTeamMemberIds!,
        }).catch((err) => {
            if (err.code === 'EntityDuplicated') {
                toaster.danger(
                    t(
                        'settings.workshopPlannerSettings.planning.notifications.planningAlreadyExistsBody'
                    ),
                    t(
                        'settings.workshopPlannerSettings.planning.notifications.planningAlreadyExistsTitle'
                    )
                );
            }
        });

        if (!createdPlanning) return;
        onAdded(createdPlanning);
        clearData();
    };

    const clearData = () => {
        setName('');
        setSelectedTeamMemberIds(null);
    };

    const onCloseClick = useCallback(() => {
        clearData();
        onClose();
    }, [onClose]);

    return (
        <Modal open={open}>
            <Box className={classes.content}>
                <Grid container spacing={1}>
                    <Box display="flex" width="100%" justifyContent="space-between">
                        <h4 style={{ color: Colors.Neutral7, fontSize: '18px' }}>
                            {t('settings.workshopPlannerSettings.planning.createModal.title')}
                        </h4>
                        <Box
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                            gap={1.25}
                        >
                            <Button
                                customStyles={{ width: '168px' }}
                                onClick={onCloseClick}
                                label={t(
                                    'settings.workshopPlannerSettings.planning.createModal.cancelButton'
                                )}
                                cmosVariant={'filled'}
                                color={Colors.Neutral3}
                                cmosSize={'medium'}
                            />
                            <Button
                                customStyles={{ width: '225px' }}
                                disabled={isAddButtonDisabled}
                                onClick={onAddClick}
                                label={t(
                                    'settings.workshopPlannerSettings.planning.createModal.createNewButton'
                                )}
                                cmosVariant={'filled'}
                                cmosSize={'medium'}
                                color={Colors.Success}
                            />
                        </Box>
                    </Box>
                </Grid>

                <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: '1fr 1fr', mt: 1 }}>
                    <TextFormField
                        isRequired
                        showValidationIndicators
                        name="name"
                        label={t(
                            'settings.workshopPlannerSettings.planning.createModal.nameHeader'
                        )}
                        placeholder={t(
                            'settings.workshopPlannerSettings.planning.createModal.namePlaceholder'
                        )}
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        maxLength={30}
                        cmosVariant="grey"
                    />
                    <TeamMembersPicker
                        values={selectedTeamMemberIds}
                        onChange={(v) => {
                            setSelectedTeamMemberIds(v);
                        }}
                    />
                </Box>
            </Box>
        </Modal>
    );
};

export default AddPlanningModal;
