import { makeStyles } from '@mui/styles';
import { FontPrimary } from 'common/styles/FontHelper';
import { HeaderStyles } from 'common/styles/HeaderStyles';

export const usePlanningsSettingsStyles = makeStyles((theme) => ({
    content: {
        paddingLeft: 51,
        paddingRight: 84,
        paddingTop: 42,
    },
    header: {
        ...FontPrimary(HeaderStyles.H5_14px, true, theme.palette.neutral[9]),
    },
    list: {
        width: '100%',
        paddingTop: 20,
        paddingBottom: 20,
    },
}));
