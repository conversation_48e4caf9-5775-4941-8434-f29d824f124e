import { PlanningDto } from 'api/workshopPlanner/plannings';
import { Button } from 'common/components/Button';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { DragAndDropEditableTextField } from 'common/components/Inputs/DragAndDropEditableTextField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import { useCallback, useMemo } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import { usePlanningStyle } from './css';

type PlanningProps = {
    planning: PlanningDto;
    onDelete: (planning: PlanningDto) => void;
    onEdit: (planning: PlanningDto) => void;
    dragOptions?: DraggableProvidedDragHandleProps;
    readonly?: boolean;
};

const Planning = ({ planning, dragOptions, onDelete, onEdit }: PlanningProps) => {
    const styles = usePlanningStyle();
    const { t } = useAppTranslation();

    const handleEdit = useCallback(() => {
        onEdit && onEdit(planning);
    }, [onEdit, planning]);

    const handleOpenDeletion = useCallback(() => {
        onDelete && onDelete(planning);
    }, [onDelete, planning]);

    const name = useMemo(() => {
        if (planning.name === 'Advisors') {
            return t('settings.workshopPlannerSettings.planning.Advisors');
        } else if (planning.name === 'Technicians') {
            return t('settings.workshopPlannerSettings.planning.Technicians');
        }

        return planning.name;
    }, [planning.name, t]);

    return (
        <>
            <div className={styles.header}>
                <div className={'title'}>
                    <DragAndDropEditableTextField
                        dragAndDropProps={dragOptions}
                        value={name}
                        isEditMode={false}
                    />
                </div>
                {!planning.isReadonly ? (
                    <div className="options">
                        <Button
                            cmosVariant={'typography'}
                            Icon={EditIcon}
                            color={Colors.Neutral3}
                            onClick={handleEdit}
                        />
                        <Button
                            cmosVariant={'typography'}
                            Icon={DeleteIcon}
                            color={Colors.Neutral3}
                            onClick={handleOpenDeletion}
                        />
                    </div>
                ) : (
                    <div style={{ width: 142 }} />
                )}
            </div>
        </>
    );
};

export default Planning;
