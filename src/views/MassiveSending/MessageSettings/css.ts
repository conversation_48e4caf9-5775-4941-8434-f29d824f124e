import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    radioLabelRoot: {
        margin: 0,
    },
    radioLabel: {
        ...theme.typography.h6Inter,
        fontWeight: 'normal',
        color: theme.palette.neutral[7],
    },
    radioLabelActive: {
        fontWeight: 'bold',
        color: theme.palette.primary.main,
    },
    label: {
        fontStyle: 'normal',
        fontSize: '12px',
        lineHeight: '14px',
        fontFamily: 'Inter, sans-serif',
        fontWeight: 'bold',
        display: 'inline-flex',
        color: theme.palette.neutral[7],
        margin: 0,
        marginBottom: 5,
    },
    inputWithLabel: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
    },
    controlLabel: {
        fontStyle: 'normal',
        fontSize: '12px',
        lineHeight: '14px',
        fontFamily: 'Inter, sans-serif',
        fontWeight: 'bold',
        display: 'inline-flex',
        color: theme.palette.neutral[7],
        marginBottom: 8,
    },
    dot: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '12px',
        lineHeight: '14px',
        color: theme.palette.primary.main,
        marginLeft: 5,
    },
    divider: {
        width: '100%',
        marginTop: 24,
        marginBottom: 24,
        height: 0,
        borderTop: `1px solid ${theme.palette.neutral[6]}`,
    },
    radios: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
    },
    radio: {
        width: 150,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    imageBlock: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    imageBlockNotes: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        marginLeft: 24,
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '12px',
        lineHeight: '14px',
        color: theme.palette.neutral[5],
    },
    uploadedImage: {
        width: 250,
        height: 120,
        backgroundColor: '#FFF',
        borderRadius: 15,
        backgroundRepeat: 'no-repeat',
        backgroundSize: '250px 120px',
        backgroundPosition: 'center top',
        border: `1px solid ${theme.palette.neutral[6]}`,

        '&:hover > $overlay': {
            display: 'flex',
        },
    },
    overlay: {
        display: 'none',
        inset: 0,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.3)',
    },
    emptyImage: {
        width: 250,
        height: 120,
        backgroundColor: 'rgba(100, 98, 243, 0.1)',
        borderRadius: 15,
        border: '1px dashed',
        borderColor: theme.palette.primary.main,
        cursor: 'pointer',
    },
    plusIcon: {
        marginTop: 40,
        height: 25,
        textAlign: 'center',
        color: theme.palette.primary.main,
    },
    addImageLabel: {
        marginTop: 3,
        fontSize: 12,
        textAlign: 'center',
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        lineHeight: '14px',
        color: theme.palette.primary.main,
        fontWeight: 700,
    },
    labelAddPhotoIns: {
        marginTop: 3,
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '9px',
        lineHeight: '11px',
        color: theme.palette.primary.main,
        textAlign: 'center',
    },
    textBlock: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-evenly',
        alignItems: 'flex-start',

        padding: '16px 29px 16px 16px',
        backgroundColor: theme.palette.neutral[2],

        border: `1px solid ${theme.palette.neutral[4]}`,
        borderRadius: 14,

        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '14px',
        lineHeight: '17px',
        color: theme.palette.neutral[5],
    },

    charCounter: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontWeight: 400,
        fontSize: '10px',
        lineHeight: '12px',
        position: 'absolute',
        right: 20,
        bottom: 10,
        color: theme.palette.neutral[5],
    },
    toolbar: {
        marginBottom: 0,
        borderRadius: '10px 10px 0px 0px',
    },
    content: {
        borderRadius: '0px 0px 10px 10px',
        color: theme.palette.neutral[8],
        height: 152,
        maxHeight: 152,
    },
}));

export default useStyles;
