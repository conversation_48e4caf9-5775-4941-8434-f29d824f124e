import { PlusIcon } from 'common/components/Icons/PlusIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import useStyles from './css';

const EmptyImageComponent = () => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    return (
        <>
            <div className={classes.plusIcon}>
                <PlusIcon fill={Colors.CM1} />
            </div>
            <div className={classes.addImageLabel}>{t('massiveSending.modal.addImage')}</div>
            <div className={classes.labelAddPhotoIns}>
                {`${t('massiveSending.modal.recommendedSize')}: 430px x 240px`}
            </div>
        </>
    );
};

export default EmptyImageComponent;
