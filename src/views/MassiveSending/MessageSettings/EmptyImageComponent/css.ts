import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    plusIcon: {
        marginTop: 40,
        height: 25,
        textAlign: 'center',
        color: theme.palette.primary.main,
    },
    addImageLabel: {
        marginTop: 3,
        fontSize: 12,
        textAlign: 'center',
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'bold',
        lineHeight: '14px',
        color: theme.palette.primary.main,
    },
    labelAddPhotoIns: {
        marginTop: 3,
        fontFamily: 'Inter, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '9px',
        lineHeight: '11px',
        color: theme.palette.primary.main,
        textAlign: 'center',
    },
}));

export default useStyles;
