import { Popover, styled, useTheme } from '@mui/material';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';
import { Editor, Extension } from '@tiptap/core';
import { MassSendingContentType } from 'api/WhatsAppMassiveSending';
import clsx from 'clsx';
import { PlusIcon } from 'common/components/Icons/PlusIcon';
import ImageDropzone from 'common/components/ImageDropzone';
import Dropdown from 'common/components/Inputs/Dropdown';
import RichTextEditor from 'common/components/Inputs/RichTextEditor';
import Radio from 'common/components/Radio';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Plugin } from 'prosemirror-state';
import { ReplaceStep } from 'prosemirror-transform';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Trans } from 'react-i18next';
import { MassSendingData, useMassiveSendingState } from '../Modal/context';
import MsGreetingText from '../MsGreetingText';
import ReplyButtons from '../ReplyButtons';
import EmptyImageComponent from './EmptyImageComponent';
import useStyles from './css';

const maxFileSize: number = 800 * 1024; //800KB
const validMimeTypes = ['image/jpeg', 'image/jpg', 'image/png'];

enum ParagraphsOption {
    Paragraph,
    ThreeParagraphsAndButtons,
}

type TextParameter = keyof Pick<
    MassSendingData,
    'startText' | 'mainText' | 'endText' | 'automaticReply'
>;

export type MassiveSendingMessageSettingsProps = {
    onValidation: (value: boolean) => void;
};

const MassiveSendingMessageSettings = ({ onValidation }: MassiveSendingMessageSettingsProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();
    const toasters = useToasters();
    const { state, update, set } = useMassiveSendingState();

    const max1ParagraphTextLength = 900;
    const maxMainTextLength = 450;
    const maxStartTextLength = 250;
    const maxEndTextLength = 100;
    const maxAutomaticReplyLength = 1000;

    const [image, setImage] = useState<File | null>(state.image ?? null);
    const [imageUrl, setImageUrl] = useState<string | null>(null);
    const wasModifiedAtLeastOnceRef = useRef(false);

    const paragraphsOptions = useMemo(
        () => [
            {
                label: t('massiveSending.modal.paragraphsOptions.1paragraph'),
                value: ParagraphsOption.Paragraph,
            },
            {
                label: t('massiveSending.modal.paragraphsOptions.3paragraphsAndButton'),
                value: ParagraphsOption.ThreeParagraphsAndButtons,
            },
        ],
        [t]
    );

    const { type, paragraphsOption } = state;

    const imageValidationFn = (file: File) => {
        const error = getFileError(file);

        if (error === 'size') {
            toasters.warning(
                t('massiveSending.modal.imageSizeError.title'),
                t('massiveSending.modal.imageSizeError.text')
            );
        }

        return !error;
    };

    const handleFileUpload = (file: File) => {
        const error = getFileError(file);
        if (error) {
            if (error === 'size') {
                toasters.warning(
                    t('massiveSending.modal.imageSizeError.title'),
                    t('massiveSending.modal.imageSizeError.text')
                );
            }

            return;
        }

        setImage(file);
    };

    const onHtmlChange = (htmlText: string, text: string, parameter: TextParameter) => {
        onValidation(text.indexOf('\n') === -1);
        wasModifiedAtLeastOnceRef.current = true;
        set(parameter, htmlText);
    };

    const onParagraphsOptionChange = (value: ParagraphsOption) => {
        if (
            (paragraphsOption === 'Paragraph' && value === ParagraphsOption.Paragraph) ||
            (paragraphsOption === 'ThreeParagraphsAndButtons' &&
                value === ParagraphsOption.ThreeParagraphsAndButtons)
        ) {
            return;
        }

        set(
            'paragraphsOption',
            value === ParagraphsOption.Paragraph ? 'Paragraph' : 'ThreeParagraphsAndButtons'
        );
    };

    useEffect(() => {
        setImageUrl(state.imageUrl);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        if (image && !getFileError(image)) {
            const url = URL.createObjectURL(image);
            setImageUrl(url);
            update({ image, imageUrl: url });
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [image]);

    return (
        <Root>
            <div className={classes.divider} />
            <div>
                <Typography className={classes.controlLabel}>
                    {t('massiveSending.modal.templateType')}
                </Typography>
                <span className={classes.dot}>*</span>
            </div>
            <Box className={classes.radios}>
                <RadioGroup
                    defaultValue={'Text'}
                    aria-label="status"
                    name="content-type"
                    value={state.contentType}
                    style={{ flexDirection: 'row' }}
                    onChange={(_, value) => {
                        update({
                            contentType: value as MassSendingContentType,
                        });
                    }}
                >
                    <FormControlLabel
                        classes={{
                            root: classes.radioLabelRoot,
                            label: clsx(
                                classes.radioLabel,
                                state.contentType === 'Text' ? classes.radioLabelActive : ''
                            ),
                        }}
                        value={'Text'}
                        control={<Radio className="" />}
                        label={t('massiveSending.modal.text')}
                    />
                    <FormControlLabel
                        classes={{
                            root: classes.radioLabelRoot,
                            label: clsx(
                                classes.radioLabel,
                                state.contentType === 'ImageAndText' ? classes.radioLabelActive : ''
                            ),
                        }}
                        value={'ImageAndText'}
                        control={<Radio className="" />}
                        label={t('massiveSending.modal.imageAndText')}
                    />
                </RadioGroup>
            </Box>
            <div className={classes.divider} />
            {state.contentType === 'ImageAndText' && (
                <div>
                    <div>
                        <Typography className={classes.controlLabel}>
                            {t('massiveSending.modal.templateImage')}
                        </Typography>
                        <span className={classes.dot}>*</span>
                    </div>
                    <div className={classes.imageBlock}>
                        <ImageDropzone
                            imageOverlay
                            imageUrl={imageUrl}
                            emptyFileComponent={<EmptyImageComponent />}
                            onChange={handleFileUpload}
                            validationFunction={imageValidationFn}
                            dropzoneClass={imageUrl ? classes.uploadedImage : classes.emptyImage}
                        />
                        <div className={classes.imageBlockNotes}>
                            <span>
                                <b>{t('Notes') + ':'}</b>
                            </span>
                            <span>
                                {t(
                                    'massiveSending.modal.thisImageWillBeSentToTheCustomerByWhatsAppMessageAllowedFormatsForTheImage'
                                )}
                                <b>{t('massiveSending.modal.pngOrJpg')}</b>
                            </span>
                        </div>
                    </div>
                    <div className={classes.divider} />
                </div>
            )}
            <div>
                <Typography className={classes.controlLabel}>
                    {t('massiveSending.modal.templateText')}
                </Typography>
                <span className={classes.dot}>*</span>
            </div>

            <Dropdown
                name="paragraphs-options"
                options={paragraphsOptions}
                cmosVariant="grey"
                value={{
                    label:
                        paragraphsOption === 'Paragraph'
                            ? t('massiveSending.modal.paragraphsOptions.1paragraph')
                            : t('massiveSending.modal.paragraphsOptions.3paragraphsAndButton'),
                    value:
                        paragraphsOption === 'Paragraph'
                            ? ParagraphsOption.Paragraph
                            : ParagraphsOption.ThreeParagraphsAndButtons,
                }}
                onChange={(event) => {
                    if (event === null) return;
                    onParagraphsOptionChange(event.value);
                }}
                styles={{
                    control: {
                        width: '290px',
                        margin: '5px 0px 15px 0px',
                    },
                }}
            />

            {paragraphsOption === 'Paragraph' ? (
                <div className={classes.textBlock}>
                    <MsGreetingText type={type} />
                    <TextEditor
                        htmlText={state.mainText}
                        maxTextLength={max1ParagraphTextLength}
                        onHtmlChange={(htmlText, text) => onHtmlChange(htmlText, text, 'mainText')}
                    />
                    <span>{t('massiveSending.modal.weLookForwardToHearingFromYou!')}</span>
                </div>
            ) : (
                <>
                    <div className={classes.textBlock}>
                        <MsGreetingText type={type} hideWeWriteFrom />
                        <TextEditor
                            htmlText={state.startText || ''}
                            maxTextLength={maxStartTextLength}
                            onHtmlChange={(htmlText, text) =>
                                onHtmlChange(htmlText, text, 'startText')
                            }
                            labelKey="massiveSending.modal.paragraphs.startOfMessageLabel"
                            placeholder={t(
                                'massiveSending.modal.paragraphs.startOfMessagePlaceholder'
                            )}
                        />
                        <TextEditor
                            htmlText={state.mainText}
                            maxTextLength={maxMainTextLength}
                            onHtmlChange={(htmlText, text) =>
                                onHtmlChange(htmlText, text, 'mainText')
                            }
                            labelKey="massiveSending.modal.paragraphs.mainTextLabel"
                            placeholder={t('massiveSending.modal.paragraphs.mainTextPlaceholder')}
                        />
                        <TextEditor
                            htmlText={state.endText || ''}
                            maxTextLength={maxEndTextLength}
                            onHtmlChange={(htmlText, text) =>
                                onHtmlChange(htmlText, text, 'endText')
                            }
                            labelKey="massiveSending.modal.paragraphs.endOfMessageLabel"
                            placeholder={t(
                                'massiveSending.modal.paragraphs.endOfMessagePlaceholder'
                            )}
                        />
                        <Trans
                            i18nKey={'massiveSending.modal.paragraphs.buttonsLabel'}
                            components={{
                                1: <b />,
                            }}
                        />
                        <ReplyButtons />
                        <TextEditor
                            htmlText={state.automaticReply || ''}
                            maxTextLength={maxAutomaticReplyLength}
                            onHtmlChange={(htmlText, text) =>
                                onHtmlChange(htmlText, text, 'automaticReply')
                            }
                            labelKey="massiveSending.modal.paragraphs.automaticReplyLabel"
                            placeholder={t(
                                'massiveSending.modal.paragraphs.automaticReplyPlaceholder'
                            )}
                        />
                    </div>
                </>
            )}
        </Root>
    );
};

type TextEditorProps = {
    htmlText: string;
    maxTextLength: number;
    onHtmlChange: (htmlText: string, text: string) => void;
    labelKey?: string;
    placeholder?: string;
};

function TextEditor({
    htmlText,
    maxTextLength,
    onHtmlChange,
    labelKey,
    placeholder,
}: TextEditorProps) {
    const classes = useStyles();
    const [editor, setEditor] = useState<Editor | null>(null);
    const [textLength, setTextLength] = useState<number>(0);

    useEffect(() => {
        if (!editor) return;

        const text = editor.getText();
        setTextLength(text.length);
    }, [editor]);

    return (
        <>
            {labelKey && (
                <span>
                    <Trans
                        i18nKey={labelKey}
                        components={{
                            1: <Typography className={classes.controlLabel} />,
                            2: <span className={classes.dot} />,
                            3: <LabelInstruction />,
                        }}
                    />
                </span>
            )}
            <EditorWrapper highlighted={textLength <= 0}>
                <RichTextEditor
                    _disableEnter
                    editorButtons={['bold', 'italic', 'strike']}
                    html={htmlText}
                    modifyExtensions={(m) => [...m, NoMoreThan4Spaces]}
                    classes={{ toolbar: classes.toolbar, content: classes.content }}
                    changeCallbackBehavior="onUpdate"
                    maxTextLength={maxTextLength}
                    onHtmlChange={(value, _, editor) => {
                        const text = editor.getText();
                        setTextLength(text.length);
                        onHtmlChange(value, text);
                    }}
                    placeholder={placeholder}
                    customButtons={[<InsertVariables editor={editor} />]}
                    editorRef={setEditor}
                />
                <span className={classes.charCounter}>{textLength + '/' + maxTextLength}</span>
            </EditorWrapper>
        </>
    );
}

const Root = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    padding: 1,

    width: 932,
});

type InsertVariablesProps = {
    editor: Editor | null;
};

function InsertVariables({ editor }: InsertVariablesProps) {
    const theme = useTheme();
    const { t } = useAppTranslation();

    const [open, setOpen] = useState(false);
    const button = useRef<HTMLButtonElement>(null);

    const brandVariable = t('massiveSending.modal.insertVariablesButton.brand');
    const modelVariable = t('massiveSending.modal.insertVariablesButton.model');
    const yearVariable = t('massiveSending.modal.insertVariablesButton.year');
    const platesVariable = t('massiveSending.modal.insertVariablesButton.plates');
    const nameVariable = t('massiveSending.modal.insertVariablesButton.name');
    const lastNameVariable = t('massiveSending.modal.insertVariablesButton.lastName');

    const onClickVariableButton = (event: React.MouseEvent<HTMLElement>, variableName: string) => {
        event.preventDefault();
        event.stopPropagation();

        editor?.chain().focus().insertContent(`[${variableName}]`).run();
        setOpen(false);
    };

    return (
        <>
            <InsertVariablesButton onClick={() => setOpen(!open)} ref={button}>
                <PlusIcon size={16} fill={theme.palette.neutral[7]} />
                {t('massiveSending.modal.insertVariablesButton.buttonTitle')}
            </InsertVariablesButton>
            <Popover
                id={'popover'}
                open={open}
                anchorEl={button.current}
                onClose={() => setOpen(false)}
                elevation={0}
                transitionDuration={0}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: -8,
                    horizontal: 'left',
                }}
            >
                <VariablesContainer>
                    <VariablesGroup>
                        {t('massiveSending.modal.insertVariablesButton.vehicleInformation')}
                    </VariablesGroup>
                    <VariableButton
                        onClick={(e) => {
                            onClickVariableButton(e, brandVariable);
                        }}
                    >
                        <VariableName>{brandVariable}</VariableName>
                    </VariableButton>
                    <VariableButton
                        onClick={(e) => {
                            onClickVariableButton(e, modelVariable);
                        }}
                    >
                        <VariableName>{modelVariable}</VariableName>
                    </VariableButton>
                    <VariableButton
                        onClick={(e) => {
                            onClickVariableButton(e, yearVariable);
                        }}
                    >
                        <VariableName>{yearVariable}</VariableName>
                    </VariableButton>
                    <VariableButton
                        onClick={(e) => {
                            onClickVariableButton(e, platesVariable);
                        }}
                    >
                        <VariableName>{platesVariable}</VariableName>
                    </VariableButton>

                    <VariablesGroup>
                        {t('massiveSending.modal.insertVariablesButton.customerInformation')}
                    </VariablesGroup>
                    <VariableButton
                        onClick={(e) => {
                            onClickVariableButton(e, nameVariable);
                        }}
                    >
                        <VariableName>{nameVariable}</VariableName>
                    </VariableButton>
                    <VariableButton
                        onClick={(e) => {
                            onClickVariableButton(e, lastNameVariable);
                        }}
                    >
                        <VariableName>{lastNameVariable}</VariableName>
                    </VariableButton>
                </VariablesContainer>
            </Popover>
        </>
    );
}

const EditorWrapper = styled('div')<{ highlighted: boolean }>(({ highlighted, theme }) => ({
    position: 'relative',
    alignSelf: 'stretch',
    marginBottom: 10,

    border: highlighted ? `solid 1px ${theme.palette.error.main}` : 'none',
    borderRadius: 10,
}));

const InsertVariablesButton = styled('button')(({ theme }) => ({
    border: `1px solid ${theme.palette.neutral[4]}`,
    borderRadius: 5,
    backgroundColor: theme.palette.neutral[1],

    fontFamily: 'Inter',
    fontSize: '11px',
    fontWeight: 600,
    lineHeight: '13px',
    color: theme.palette.neutral[7],

    display: 'flex',
    alignItems: 'center',

    height: 24,
    paddingRight: 16,

    '&:hover': {
        backgroundColor: 'var(--cm5)',
    },
}));

const VariablesContainer = styled('div')(({ theme }) => ({
    borderRadius: 10,
}));

const VariablesGroup = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.neutral[3],
    fontWeight: 700,

    fontFamily: 'Roboto',
    fontSize: '12px',
    textAlign: 'left',
    letterSpacing: '0em',
    color: theme.palette.neutral[7],

    height: 30,
    display: 'flex',
    alignItems: 'center',
    paddingLeft: 12,
}));

const VariableButton = styled('button')(({ theme }) => ({
    backgroundColor: 'transparent',
    border: `none`,

    fontFamily: 'Roboto',
    fontSize: '12px',
    fontWeight: 400,
    textAlign: 'left',
    color: theme.palette.neutral[7],

    height: 30,
    padding: '0px 16px',
    width: '100%',

    '&:hover': {
        backgroundColor: 'var(--cm5)',
    },

    '&:not(:last-child)': {
        '&::after': {
            backgroundColor: theme.palette.neutral[3],
            content: '""',
            display: 'block',
            height: 1,
        },
    },
}));

const VariableName = styled('div')({
    height: '100%',
    display: 'flex',
    alignItems: 'center',
});

function getFileError(file: File): 'size' | 'type' | undefined {
    if (validMimeTypes.indexOf(file.type) === -1) {
        return 'type';
    }
    if (file.size > maxFileSize) return 'size';
}

const LabelInstruction = styled('span')(({ theme }) => ({
    fontFamily: 'Roboto, sans-serif',
    fontStyle: 'normal',
    fontWeight: 'normal',
    fontSize: '12px',
    lineHeight: '14px',
    color: theme.palette.neutral[7],
}));

export default MassiveSendingMessageSettings;

/**
 * ProseMirror plugin for limiting number of spaces to only 4. Only tested with text
 * nodes. If we add images or any non-text content into the mix it might stop working.
 */
const PmPluginNoMoreThan4SpacesAndNoTabs = new Plugin({
    filterTransaction(tr, state) {
        try {
            if (!tr.docChanged) return true;

            if (tr.steps.length === 1) {
                const step = tr.steps[0];
                // check if it's a char insert and there's only 1 char (step.from == step.to)
                if (step instanceof ReplaceStep && step.from === step.to) {
                    const node = step.slice.content.child(0);
                    if (node.text === ' ') {
                        // get 8 chars around the cursor, if the string contains 4+ consecutive spaces
                        // then stop transaction
                        const start = Math.max(0, step.from - 4);
                        const stop = Math.min(state.doc.content.size, step.to + 4);
                        const text = state.doc.textBetween(start, stop);
                        if (/\s{4,}/.test(text)) {
                            return false;
                        }
                    } else if (node.text === '\t') {
                        // disallow tabs
                        return false;
                    }
                }
            }
        } catch (e) {
            console.error('error in PmPluginNoMoreThan4SpacesAndNoTabs plugin', e);
        }

        return true;
    },
});

const NoMoreThan4Spaces = Extension.create({
    name: 'NoMoreThan4SpacesAndNoTabs',
    addProseMirrorPlugins() {
        return [PmPluginNoMoreThan4SpacesAndNoTabs];
    },
});
