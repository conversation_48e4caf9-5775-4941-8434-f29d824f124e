import { Pagination, styled } from '@mui/material';
import Grid from '@mui/material/Grid';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TableSortLabel from '@mui/material/TableSortLabel';
import Typography from '@mui/material/Typography';
import { useQuery } from '@tanstack/react-query';
import WhatsAppMassiveSending, {
    MassSendingDetailsDto,
    MassSendingListItem,
    MassiveSendingOrderType,
    MassiveSendingStatus,
} from 'api/WhatsAppMassiveSending';
import { Button } from 'common/components/Button';
import { MegaphoneIcon } from 'common/components/Icons/MegaphoneIcon';
import { UpIcon } from 'common/components/Icons/UpIcon';
import TableHeadCell from 'common/components/TableHeadCell';
import TableHeadSelectFilter from 'common/components/TableHeadSelectFilter';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useDocumentTitle from 'common/hooks/useDocumentTitle';
import { UseTrackedState } from 'common/hooks/useTrackedState';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { PermissionsGate } from 'views/Components/AuthorizationGate';
import { FeaturesGate } from 'views/Components/FeaturesGate';
import { WidePageLayout } from 'views/Components/Page';
import DeleteModal from './DeleteModal';
import MassSendingDetailPopup from './MassSendingDetailPopup';
import MassiveSendingModal from './Modal';
import { MassSendingData, createMassSendingCopyFromDetails } from './Modal/context';
import MassiveSendingRow from './Row';

const UpIconWrapped = (propsIcon: { className: string }) => (
    <div {...propsIcon}>
        <UpIcon size={IconSize.M} fill={Colors.Neutral7} />
    </div>
);

const MassiveSending = () => {
    return (
        <FeaturesGate predicate={'enableAutoProspection'}>
            <PermissionsGate predicate="allowSendMassiveSendings">
                <MassiveSendingInner />
            </PermissionsGate>
        </FeaturesGate>
    );
};

const MassiveSendingInner = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useAppTranslation();
    const { callApi } = useApiCall();
    const pageSize = 10;
    useDocumentTitle(t('titles.massiveSending'));
    const [page, setPage] = useState<number>(1);
    const [orderBy, setOrderBy] = useState<MassiveSendingOrderType>();
    const [status, setStatus] = useState<MassiveSendingStatus>();
    const trackingState = useRef<UseTrackedState<MassSendingData>>();
    const [deleteMsId, setDeleteMsId] = useState<string>();
    const [sendingModalOpen, setSendingModalOpen] = useState<boolean>(false);

    const [massSendingDetailId, setMassSendingDetailId] = useState<string>();

    const {
        data: response,
        refetch,
        // isFetched,
        // isInitialLoading,
        // isPreviousData,
    } = useQuery(
        ['ms', [page, pageSize, orderBy, status]],
        () => WhatsAppMassiveSending.getMassiveSendingList(page, pageSize, orderBy, status),
        {
            keepPreviousData: true,
            refetchInterval: 300000, // 5 minutes
        }
    );
    const massiveSendingList = response?.list ?? [];
    const pages = response?.totalPages ?? 1;
    const total = response?.total;

    const changePage = (page: number) => {
        const params = new URLSearchParams(location.search);
        params.set('page', `${page}`);
        navigate({ search: params.toString() });
        setPage(page);
    };

    const handleEditClick = async (value: MassSendingListItem) => {
        const response = await callApi(() => WhatsAppMassiveSending.getDetails(value.id || ''), {
            selectErrorContent: (_) => ({
                body: t('toasters.errorOccurredWhenLoading'),
            }),
        });
        trackingState.current?.setOriginalState({
            id: response.id,
            paragraphsOption: response.content.paragraphsOption,
            mainText: response.content.mainText,
            startText: response.content.startText,
            endText: response.content.endText,
            automaticReply: response.content.automaticReply,
            imageUrl: response.content.image,
            sendImmediately: false,
            sendingDate: response.sendAt,
            contentType: response.content.type,
            image: null,
            name: response.name,
            selector: response.selector,
            type: response.type,
            vinFile: response.vinFile
                ? {
                      id: response.vinFile.id,
                      name: response.vinFile.name,
                      size: response.vinFile.sizeBytes,
                      impactedCustomers: response.vinFile.impactedCustomers,
                  }
                : null,
            _meta: {
                uploadedFile: response.vinFile,
            },
        });
        setSendingModalOpen(true);
    };

    const duplicateMassSending = useCallback(async (details: MassSendingDetailsDto) => {
        setMassSendingDetailId(undefined);
        const data = await createMassSendingCopyFromDetails(details);
        setSendingModalOpen(true);
        trackingState.current?.setOriginalState(data);
    }, []);

    const handleDeleteClick = (value: MassSendingListItem) => setDeleteMsId(value.id);

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const pageParam = params.get('page');
        changePage(pageParam ? +pageParam : 1);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <WidePageLayout marginTop>
            <Grid container spacing={2}>
                <Grid item xs={12} md={8}>
                    <Title variant="subtitle1">{t('massiveSending.massiveSendingsLists')}</Title>
                    <IconWrapper variant="h4">
                        <MegaphoneIcon fill={Colors.Neutral7} />
                        {total}
                    </IconWrapper>
                </Grid>
                <Grid item xs={12} md={4} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Grid item xs={12} md={6}>
                        <Button
                            blockMode
                            cmosVariant={'filled'}
                            color={Colors.CM1}
                            label={t('massiveSending.newMassiveSending')}
                            onClick={() => {
                                setSendingModalOpen(true);
                                trackingState.current?.resetToDefault();
                            }}
                        />
                    </Grid>
                </Grid>
            </Grid>
            <TableWrapper>
                <Table stickyHeader>
                    <TableHead>
                        <TableRow>
                            <TableHeadCell
                                component="td"
                                scope="row"
                                style={{
                                    padding: '16px 10px',
                                    borderRadius: '12px 0px 0px 0px',
                                }}
                            >
                                {t('massiveSending.table.sendingName')}
                            </TableHeadCell>
                            <TableHeadCell
                                component="td"
                                scope="row"
                                style={{ padding: '16px 10px' }}
                            >
                                <TableSortLabel
                                    active={
                                        orderBy === MassiveSendingOrderType.SendingDateAscending ||
                                        orderBy === MassiveSendingOrderType.SendingDateDescending
                                    }
                                    direction={
                                        orderBy === MassiveSendingOrderType.SendingDateAscending
                                            ? 'asc'
                                            : 'desc'
                                    }
                                    onClick={() => {
                                        setOrderBy((old) =>
                                            old === MassiveSendingOrderType.SendingDateAscending
                                                ? MassiveSendingOrderType.SendingDateDescending
                                                : MassiveSendingOrderType.SendingDateAscending
                                        );
                                    }}
                                    IconComponent={UpIconWrapped}
                                >
                                    {t('massiveSending.table.sendDate')}
                                </TableSortLabel>
                            </TableHeadCell>
                            <TableHeadCell
                                component="td"
                                scope="row"
                                style={{ padding: '16px 10px' }}
                            >
                                {t('massiveSending.table.endingDate')}
                            </TableHeadCell>
                            <TableHeadCell
                                component="td"
                                scope="row"
                                style={{ padding: '16px 10px' }}
                            >
                                {t('massiveSending.table.impactedCustomers')}
                            </TableHeadCell>
                            <TableHeadCell
                                component="td"
                                scope="row"
                                style={{
                                    padding: '16px 10px',
                                }}
                            >
                                {t('massiveSending.table.status')}
                                <TableHeadSelectFilter
                                    hideApplyButton
                                    mainLabel={t('massiveSending.table.filterByStatus')}
                                    options={[
                                        {
                                            id: `${MassiveSendingStatus.Active}`,
                                            label: t('massiveSending.table.active'),
                                        },
                                        {
                                            id: `${MassiveSendingStatus.Finished}`,
                                            label: t('massiveSending.table.finalized'),
                                        },
                                    ]}
                                    selected={[
                                        ...(status === MassiveSendingStatus.Active
                                            ? [`${MassiveSendingStatus.Active}`]
                                            : status === MassiveSendingStatus.Finished
                                            ? [`${MassiveSendingStatus.Finished}`]
                                            : []),
                                    ]}
                                    onSelectedChanged={(selected) => {
                                        setStatus(
                                            selected.includes(`${MassiveSendingStatus.Active}`)
                                                ? MassiveSendingStatus.Active
                                                : selected.includes(
                                                      `${MassiveSendingStatus.Finished}`
                                                  )
                                                ? MassiveSendingStatus.Finished
                                                : undefined
                                        );
                                    }}
                                />
                            </TableHeadCell>
                            <TableHeadCell
                                component="td"
                                scope="row"
                                style={{
                                    padding: '16px 10px',
                                    borderRadius: '0px 12px 0px 0px',
                                }}
                            />
                        </TableRow>
                    </TableHead>
                    {massiveSendingList && massiveSendingList.length ? (
                        <TableBody>
                            {massiveSendingList.map((m, index) => (
                                <MassiveSendingRow
                                    onClick={m.id ? () => setMassSendingDetailId(m.id) : undefined}
                                    key={`massive-sending-${index}-${m.id}`}
                                    id={`massive-sending-${index}-${m.id}`}
                                    value={m}
                                    onEditClick={handleEditClick}
                                    onDeleteClick={handleDeleteClick}
                                />
                            ))}
                        </TableBody>
                    ) : null}
                </Table>
            </TableWrapper>
            <Grid container justifyContent="center" style={{ marginTop: 22 }}>
                {massiveSendingList && massiveSendingList.length && pages > 1 ? (
                    <Pagination
                        page={page}
                        size="small"
                        count={pages}
                        color="primary"
                        onChange={(_, page) => {
                            changePage(page);
                        }}
                    />
                ) : null}
            </Grid>
            {massSendingDetailId && (
                <MassSendingDetailPopup
                    onDuplicateRequested={duplicateMassSending}
                    onClose={() => setMassSendingDetailId(undefined)}
                    id={massSendingDetailId}
                />
            )}
            <MassiveSendingModal
                open={sendingModalOpen}
                trackedStateRef={trackingState}
                onClose={() => {
                    trackingState.current?.resetToOriginal();
                    setSendingModalOpen(false);
                    refetch();
                }}
            />
            <DeleteModal
                onDeleted={() => refetch()}
                onClose={useCallback(() => {
                    setDeleteMsId(undefined);
                }, [])}
                id={deleteMsId}
                open={!!deleteMsId}
            />
        </WidePageLayout>
    );
};

const Title = styled(Typography)(({ theme }) => ({
    color: theme.palette.neutral[6],
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 10,
}));

const IconWrapper = styled(Typography)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    color: theme.palette.neutral[7],
}));

const TableWrapper = styled('div')({
    marginTop: 24,
    borderRadius: 12,
    overflow: 'hidden',
    border: '1px solid #DBDCDD',
});

export default MassiveSending;
