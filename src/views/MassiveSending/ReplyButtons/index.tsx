import { Divider, styled } from '@mui/material';
import { ReplyIcon } from 'common/components/Icons/ReplyIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

type ReplyButtonsProps = {
    previewMode?: boolean;
};

export default function ReplyButtons({ previewMode = false }: ReplyButtonsProps) {
    const { t } = useAppTranslation();

    return (
        <>
            <DivRoot previewMode={previewMode}>
                <DivButton>
                    <ReplyIcon fill="currentColor" />
                    {t('massiveSending.modal.paragraphs.buttons.iWantToBeConducted')}
                </DivButton>
                <SDivider />
                <DivButton>
                    <ReplyIcon fill="currentColor" />
                    {t('massiveSending.modal.paragraphs.buttons.stopPromotions')}
                </DivButton>
            </DivRoot>
        </>
    );
}

const DivRoot = styled('div')<{ previewMode: boolean }>(({ theme, previewMode }) => ({
    width: '100%',

    padding: 0,
    margin: previewMode ? '10px 0px 0px 0px' : '10px 0px',

    ...(previewMode
        ? { borderTop: `solid 1px ${theme.palette.neutral[4]}` }
        : { border: `solid 1px ${theme.palette.neutral[4]}` }),
    borderRadius: previewMode ? '0px 0px 5px 5px' : 5,

    backgroundColor: theme.palette.neutral[1],
}));

const DivButton = styled('div')(({ theme }) => ({
    ...theme.typography.h6Inter,
    fontWeight: 'bold',
    padding: '8px 16px',
    color: theme.palette.primary.light,

    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 2,
}));

const SDivider = styled(Divider)({
    margin: '0px !important',
});
