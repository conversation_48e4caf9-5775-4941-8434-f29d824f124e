import WhatsAppMassiveSending from 'api/WhatsAppMassiveSending';
import { DeleteConfirmationPopup } from 'common/components/Popups/ConfirmationPopup';
import { useApiCall } from 'common/hooks';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useState } from 'react';

type DeleteModalProps = {
    id: string | undefined;
    open: boolean;
    onDeleted: () => void;
    onClose: () => void;
};

export default function DeleteModal({ open, id, onClose, onDeleted }: DeleteModalProps) {
    const { callApi } = useApiCall();
    const [deleting, setDeleting] = useState(false);
    const { t } = useAppTranslation();

    const delete_ = useCallback(async () => {
        if (!id) return;
        setDeleting(true);
        try {
            await callApi(() => WhatsAppMassiveSending.delete_(id));
            onDeleted();
            onClose();
        } finally {
            setDeleting(false);
        }
    }, [callApi, id, onClose, onDeleted]);

    return (
        <DeleteConfirmationPopup
            title={t('massiveSending.delete.title')}
            body={t('massiveSending.delete.body')}
            cancel={t('commonLabels.back')}
            confirm={t('massiveSending.delete.yesDelete')}
            open={open}
            onConfirm={delete_}
            showLoader={deleting}
            onClose={onClose}
        />
    );
}
