import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { Colors } from 'common/styles/Colors';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        cursor: 'pointer',
        backgroundColor: theme.palette.neutral[1],
        '&:hover': {
            backgroundColor: 'var(--cm5)',
            '&:hover': {
                '& $options': {
                    opacity: 1,
                },
            },
        },
        '& $options': {
            opacity: 0,
        },
    },
    options: {},
    impactedCustomersContainer: {
        display: 'flex',
        flexDirection: 'row',
    },
    status: {
        borderRadius: 5,
        color: '#FFFFFF',
        padding: '5px 10px',
        width: 'fit-content',
        fontFamily: 'Roboto',
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
    },
    statusActive: {
        color: Colors.Success,
        backgroundColor: Colors.Success_background,
    },
    statusFinalized: {
        marginLeft: -2,
        color: theme.palette.primary.main,
        backgroundColor: Colors.CM5,
    },
    tableCell: {
        padding: '16px 10px',
        '&.text': {
            color: theme.palette.neutral[7],
        },
    },
}));
export default useStyles;
