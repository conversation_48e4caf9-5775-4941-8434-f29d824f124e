import IconButton from '@mui/material/IconButton';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { MassSendingListItem } from 'api/WhatsAppMassiveSending';
import clsx from 'clsx';
import { CalendarCheckmarkIcon } from 'common/components/Icons/CalendarCheckmarkIcon';
import { DeleteIcon } from 'common/components/Icons/DeleteIcon';
import { EditIcon } from 'common/components/Icons/EditIcon';
import { PersonCheckmarkIcon } from 'common/components/Icons/PersonCheckmarkIcon';
import { PersonSpeechBalloonIcon } from 'common/components/Icons/PersonSpeechBalloonIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Colors } from 'common/styles/Colors';
import moment from 'moment';
import { Key, MouseEventHandler } from 'react';
import IconCounter from 'views/Components/IconCounter';
import IconTooltip from 'views/Components/IconTooltip';
import useStyles from './css';

type MassiveSendingRowProps = {
    id?: Key | null;
    value: MassSendingListItem;
    onEditClick: (value: MassSendingListItem) => void;
    onDeleteClick: (value: MassSendingListItem) => void;
    onClick?: MouseEventHandler;
};

const MassiveSendingRow = ({
    id,
    value,
    onClick,
    onEditClick,
    onDeleteClick,
}: MassiveSendingRowProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    return (
        <TableRow onClick={onClick} key={id} className={classes.root}>
            <TableCell component="td" scope="row" className={clsx(classes.tableCell, 'text')}>
                {value.name}
            </TableCell>
            <TableCell component="td" scope="row" className={clsx(classes.tableCell, 'text')}>
                <div>{moment(value.sendingDate).format(t('massiveSending.table.dateFormat'))}</div>
                <div>{moment(value.sendingDate).format(t('massiveSending.table.timeFormat'))}</div>
            </TableCell>
            <TableCell component="td" scope="row" className={clsx(classes.tableCell, 'text')}>
                {value.completionDate ? (
                    <>
                        <div>
                            {moment(value.sendingDate).format(t('massiveSending.table.dateFormat'))}
                        </div>
                        <div>
                            {moment(value.sendingDate).format(t('massiveSending.table.timeFormat'))}
                        </div>
                    </>
                ) : (
                    t('massiveSending.table.neverEnds')
                )}
            </TableCell>
            <TableCell component="td" scope="row" className={classes.tableCell}>
                <div className={classes.impactedCustomersContainer}>
                    <span style={{ marginRight: 10 }}>
                        <IconTooltip
                            icon={
                                <IconCounter
                                    count={value.sentToCustomersCount}
                                    icon={<PersonCheckmarkIcon />}
                                />
                            }
                            tooltipText={t('massiveSending.table.customersWhomMessageSent')}
                        />
                    </span>
                    <span style={{ marginRight: 10 }}>
                        <IconTooltip
                            icon={
                                <IconCounter
                                    count={value.responseReceivedCount}
                                    icon={<PersonSpeechBalloonIcon />}
                                />
                            }
                            tooltipText={t('massiveSending.table.customersRespondedMassiveSending')}
                        />
                    </span>
                    <span>
                        <IconTooltip
                            icon={
                                <IconCounter
                                    count={value.appointmentScheduleCount}
                                    icon={<CalendarCheckmarkIcon />}
                                />
                            }
                            tooltipText={t(
                                'massiveSending.table.appointmentScheduled72HoursAfterMassiveSending'
                            )}
                        />
                    </span>
                </div>
            </TableCell>
            <TableCell component="td" scope="row" className={classes.tableCell}>
                {value.status === 'Active' ? (
                    <div className={clsx(classes.status, classes.statusActive)}>
                        {t('massiveSending.table.active')}
                    </div>
                ) : (
                    <div className={clsx(classes.status, classes.statusFinalized)}>
                        {t('massiveSending.table.finalized')}
                    </div>
                )}
            </TableCell>
            <TableCell component="td" scope="row" className={classes.tableCell}>
                {value.status === 'Finished' ? null : (
                    <div className={classes.options}>
                        <IconButton
                            style={{ height: 32, width: 32 }}
                            onClick={(e) => {
                                e.stopPropagation();
                                onEditClick(value);
                            }}
                            size="large"
                        >
                            <EditIcon fill={Colors.Neutral5} />
                        </IconButton>
                        <IconButton
                            style={{ height: 32, width: 32 }}
                            onClick={(e) => {
                                e.stopPropagation();
                                onDeleteClick(value);
                            }}
                            size="large"
                        >
                            <DeleteIcon fill={Colors.Neutral5} />
                        </IconButton>
                    </div>
                )}
            </TableCell>
        </TableRow>
    );
};

export default MassiveSendingRow;
