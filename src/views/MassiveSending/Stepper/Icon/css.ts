import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        color: theme.palette.neutral[2],
        display: 'flex',
        height: 22,
        alignItems: 'center',
    },
    circle: {
        width: 14,
        height: 14,
        borderRadius: '50%',
        border: `1px solid ${theme.palette.neutral[5]}`,
    },
    activeCircle: {
        width: 14,
        height: 14,
        borderRadius: '50%',
        border: `1px solid ${theme.palette.primary.main}`,
        backgroundColor: theme.palette.primary.main,
    },
    completed: {
        color: 'var(--success)',
        zIndex: 1,
        fontSize: 22,
        fontWeight: 'bold',
    },
}));

export default useStyles;
