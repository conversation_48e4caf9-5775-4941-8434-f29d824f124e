import { Check } from '@mui/icons-material';
import { StepIconProps } from '@mui/material/StepIcon';
import useStyles from './css';

const StepIcon = ({ active, completed }: StepIconProps) => {
    const classes = useStyles();
    return (
        <div className={classes.root}>
            {completed ? (
                <Check className={classes.completed} />
            ) : active ? (
                <div className={classes.activeCircle} />
            ) : (
                <div className={classes.circle} />
            )}
        </div>
    );
};

export default StepIcon;
