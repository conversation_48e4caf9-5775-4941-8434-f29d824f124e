import Step from '@mui/material/Step';
import StepConnector, { stepConnectorClasses } from '@mui/material/StepConnector';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import { styled } from '@mui/material/styles';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import StepIcon from './Icon';
import useStyles from './css';

type MassiveSendingStepperProps = {
    activeStep: number;
};

type StepConnectorTypes = 'completed' | 'next' | 'neutral';

const StyledStepper = styled(Stepper)({
    padding: '16px 0px',
});

const MassiveSendingStepper = ({ activeStep }: MassiveSendingStepperProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    return (
        <StyledStepper alternativeLabel activeStep={activeStep} connector={<></>}>
            <Step>
                <StepLabel
                    StepIconComponent={StepIcon}
                    classes={{
                        alternativeLabel: classes.labelAlternativeLabel,
                        completed: classes.labelCompleted,
                        active: classes.labelActive,
                    }}
                >
                    {t('massiveSending.modal.sendingRules')}
                </StepLabel>
            </Step>
            <Step>
                <StepLabel
                    StepIconComponent={StepIcon}
                    classes={{
                        alternativeLabel: classes.labelAlternativeLabel,
                        completed: classes.labelCompleted,
                        active: classes.labelActive,
                    }}
                >
                    {t('massiveSending.modal.whatsAppTemplate')}
                </StepLabel>
                <StyledStepperConnector type={activeStep === 0 ? 'next' : 'completed'} />
            </Step>
            <Step>
                <StepLabel
                    StepIconComponent={StepIcon}
                    classes={{
                        alternativeLabel: classes.labelAlternativeLabel,
                        completed: classes.labelCompleted,
                        active: classes.labelActive,
                    }}
                >
                    {t('massiveSending.modal.preview')}
                </StepLabel>
                <StyledStepperConnector
                    type={activeStep === 1 ? 'next' : activeStep === 2 ? 'completed' : 'neutral'}
                />
            </Step>
        </StyledStepper>
    );
};

const StyledStepperConnector = styled(StepConnector)<{ type: StepConnectorTypes }>(
    ({ theme, type }) => ({
        [`&.${stepConnectorClasses.alternativeLabel}`]: {
            top: 10,
            left: 'calc(-50% + 16px)',
            right: 'calc(50% + 16px)',
        },
        [`& .${stepConnectorClasses.line}`]: {
            borderTopWidth: 1,
            borderRadius: 1,
            borderColor:
                type === 'completed'
                    ? theme.palette.success.main
                    : type === 'next'
                    ? theme.palette.primary.main
                    : theme.palette.neutral[7],
        },
    })
);

export default MassiveSendingStepper;
