import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    labelAlternativeLabel: {
        ...theme.typography.h6Roboto,
        color: `${theme.palette.neutral[7]} !important`,
        '& .MuiStepLabel-active': {
            color: `${theme.palette.primary.main} !important`,
        },
    },
    labelCompleted: {
        color: `var(--success) !important`,
    },
    labelActive: {
        color: `${theme.palette.primary.main} !important`,
    },
}));

export default useStyles;
