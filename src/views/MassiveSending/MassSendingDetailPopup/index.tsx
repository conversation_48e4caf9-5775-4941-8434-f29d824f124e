import { CircularProgress, styled } from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import WhatsAppMassiveSending, { MassSendingDetailsDto } from 'api/WhatsAppMassiveSending';
import { Button } from 'common/components/Button';
import { CloneIcon } from 'common/components/Icons/CloneIcon';
import { ExcelIcon } from 'common/components/Icons/ExcelIcon';
import { Modal } from 'common/components/Modal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { IconSize } from 'common/styles/IconSize';
import moment from 'moment';
import { HttpUtils } from 'services';
import { scrollbarStyle } from '../../../common/styles/ScrollbarStyles';
import MassSendingWhatsAppMessagePreview from '../MassSendingWhatsAppMessagePreview';
import { Parameter, Parameters } from './Parameter';

export type MassSendingDetailPopupProps = {
    id: string;
    onClose: () => void;
    onDuplicateRequested: (details: MassSendingDetailsDto) => void;
};

const ExcelIconSmaller = () => <ExcelIcon size={IconSize.S} />;
const CopyIconSmaller = () => <CloneIcon size={IconSize.M} />;

export default function MassSendingDetailPopup({
    id,
    onClose,
    onDuplicateRequested,
}: MassSendingDetailPopupProps) {
    const { data, isLoading } = useQuery(
        ['ms', 'details', id],
        () => WhatsAppMassiveSending.getDetails(id),
        {
            staleTime: 30000,
            cacheTime: 600000,
        }
    );
    const toasters = useToasters();

    const downloadReportMutation = useMutation(
        async () => {
            const result = await WhatsAppMassiveSending.createImpactedCustomersReport(id);
            HttpUtils.downloadViaAnchor(result.downloadLink);
        },
        {
            onError(error, _variables, _context) {
                console.error('Failed to download report, server error ocurred', error);
                toasters.danger('Failed to download report, server error ocurred', 'Error');
            },
        }
    );

    const { t } = useAppTranslation();

    // https://clearmechanic.atlassian.net/browse/CMOS-1018 c.iii
    function formatTypeOfCustomers(ms: MassSendingDetailsDto) {
        const brands = ms.selector.brands.map((x) => x.name).join(', ');
        const models = ms.selector.brands.flatMap((x) => x.models).join(', ');
        const years = ms.selector.years.join(', ');
        const lastActiveWithin = !ms.selector.lastVisitTimeInMonths
            ? t('massiveSending.modal.lastVisit.allMonths')
            : t('massiveSending.modal.lastVisit.months', {
                  count: ms.selector.lastVisitTimeInMonths,
              });

        const noFiltersSpecified =
            ms.selector.brands.length === 0 &&
            ms.selector.years.length === 0 &&
            ms.selector.lastVisitTimeInMonths === null;

        const result = noFiltersSpecified
            ? t('massiveSending.modal.allCustomers')
            : `${brands || '--'} / ${models || '--'} / ${years || '--'} / ${lastActiveWithin}`;
        return result;
    }

    return (
        <Modal onClose={onClose} open>
            <Root>
                <Header>
                    <HeaderTitle>{t('massiveSending.modal.sendingDetails')}</HeaderTitle>
                    <BackButton
                        color={Colors.Neutral3}
                        label={t('commonLabels.goBack')}
                        onClick={() => onClose()}
                        cmosVariant={'filled'}
                    />
                </Header>

                <ScrollableBox>
                    <Info>
                        {isLoading && <CircularProgress size={30} />}
                        {data && (
                            <Parameters>
                                <Parameter
                                    label={t('massiveSending.modal.sendingName')}
                                    value={data.name}
                                />
                                <Parameter
                                    label={t('massiveSending.sendingType._')}
                                    value={t(`massiveSending.sendingType.${data.type}`)}
                                />
                                {data.type === 'Personalized' && (
                                    <Parameter
                                        label={t('massiveSending.modal.typeOfCustomersToSend')}
                                        value={formatTypeOfCustomers(data)}
                                    />
                                )}
                                <Parameter
                                    label={t('massiveSending.modal.dateOfSending')}
                                    value={formatDateTimeString(data.sendAt)}
                                />
                            </Parameters>
                        )}
                    </Info>

                    {data && (
                        <Preview>
                            <MassSendingWhatsAppMessagePreview
                                type={data.type}
                                dateTime={data.sendAt}
                                paragraphsOption={data.content.paragraphsOption}
                                mainText={data.content.mainText}
                                startText={data.content.startText}
                                endText={data.content.endText}
                                image={data.content.image}
                            />
                        </Preview>
                    )}
                </ScrollableBox>

                {data && (
                    <Footer>
                        <Button
                            onClick={() => downloadReportMutation.mutate()}
                            disabled={downloadReportMutation.isLoading || data.status === 'Active'}
                            showLoader={downloadReportMutation.isLoading}
                            cmosVariant={'typography'}
                            color={Colors.Success}
                            Icon={ExcelIconSmaller}
                            label={t('massiveSending.modal.downloadImpactedCustomersReport')}
                        />
                        <Button
                            onClick={() => {
                                if (data) onDuplicateRequested(data);
                            }}
                            Icon={CopyIconSmaller}
                            cmosVariant={'typography'}
                            label={t('massiveSending.modal.duplicate')}
                        />
                    </Footer>
                )}
            </Root>
        </Modal>
    );
}

function formatDateTimeString(dtStr: string) {
    const dt = moment(dtStr);
    return dt.format('DD/MMMM/YYYY - HH:mm') + 'hrs';
}

const Root = styled('div')(() => ({
    padding: '40px 60px 40px 40px',
    gap: '0 45px',
    maxWidth: 810,
    width: 'min(100vw, 810px)',
}));

const Info = styled('div')(() => ({
    paddingBottom: 20,
}));

const Preview = styled('div')(() => ({
    marginRight: 25,
}));

const Header = styled('header')(() => ({
    marginBottom: 25,
    display: 'flex',
    justifyContent: 'space-between',
}));

const HeaderTitle = styled('div')(({ theme }) => ({
    ...theme.typography.h5Inter,
    color: theme.palette.neutral[8],
}));

const BackButton = styled(Button)(() => ({
    width: 160,
}));

const Footer = styled('div')(() => ({
    marginTop: 20,
    display: 'flex',
    justifyContent: 'space-between',
}));

const ScrollableBox = styled('div')({
    ...scrollbarStyle(),
    overflowY: 'auto',
    maxHeight: '50vh',
    display: 'flex',
    justifyContent: 'space-between',
});
