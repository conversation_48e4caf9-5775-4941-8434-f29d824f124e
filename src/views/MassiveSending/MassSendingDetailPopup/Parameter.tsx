import { styled } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { memo } from 'react';

const useParameterStyles = makeStyles((theme) => ({
    label: {
        ...theme.typography.h6Roboto,
        color: theme.palette.neutral[5],
        fontWeight: 'normal',
    },
    value: {
        ...theme.typography.h5Roboto,
        color: theme.palette.neutral[8],
        fontWeight: 'normal',
    },
    root: {},
}));

type ParameterProps = {
    label: string;
    value: string | null | undefined;
};

export const Parameter = memo(({ label, value }: ParameterProps) => {
    const styles = useParameterStyles();

    return (
        <div className={styles.root}>
            <div className={styles.label}>{label}</div>
            <div className={styles.value}>{value}</div>
        </div>
    );
});

export const Parameters = styled('div')(({ theme }) => ({
    display: 'flex',
    alignItems: 'stretch',
    flexDirection: 'column',
    '& > *:not(:last-child)::after': {
        content: '" "',
        display: 'block',
        height: 1,
        backgroundColor: theme.palette.neutral[3],
        margin: '20px 0',
    },
}));
