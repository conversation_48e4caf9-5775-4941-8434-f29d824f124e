import { Divider, Grid, styled } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import WhatsAppMassiveSending from 'api/WhatsAppMassiveSending';
import { Button } from 'common/components/Button';
import { DownloadIcon } from 'common/components/Icons/DownloadIcon';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { UseTrackedState } from 'common/hooks/useTrackedState';
import { Colors } from 'common/styles/Colors';
import { useCallback, useRef, useState } from 'react';
import { HttpUtils } from 'services';
import { hasCode, isErrorResponse } from 'services/Server';
import DragAndDropFileInputControllable from 'views/Components/DragAndDropFileInput/DragAndDropFileInputControllable';
import { MassSendingData } from 'views/MassiveSending/Modal/context';
import RecipientsCount from './RecipientsCount';

const DividerWithMargin = styled(Divider)({ margin: '20px 0' });

const Parameter = styled('p')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    fontWeight: 'normal',
    color: theme.palette.neutral[7],
    fontSize: theme.typography.h6Roboto.fontSize,
    margin: 0,
}));
const SectionHeader = styled('h4')(({ theme }) => ({
    ...theme.typography.h6Inter,
    color: theme.palette.neutral[7],
    margin: '2px 0',
}));

type UploadVinFile = {
    state: UseTrackedState<MassSendingData>;
    onLoading: (loading: boolean) => void;
};

export default function UploadVinFile({ state, onLoading }: UploadVinFile) {
    const { t } = useAppTranslation();
    const [customersCount, setCustomersCount] = useState<number>();
    const prevVinFile = useRef<File | undefined>(undefined);
    const { set } = state;

    const downloadTemplate = () => {
        HttpUtils.downloadViaAnchor(t('massiveSending.modal.vinUpload.tmplUrl'));
    };

    const getVinFile = useGetPersistentSpot();

    const toasters = useToasters();
    const showFileTooBigToaster = useCallback(
        () =>
            toasters.danger(
                t('massiveSending.modal.vinUpload.fileLimitExceeded'),
                t('commonLabels.error')
            ),
        [toasters, t]
    );
    const showTooManyVinsToaster = () =>
        toasters.danger(
            t('massiveSending.modal.vinUpload.vinsLimitExceeded'),
            t('commonLabels.error')
        );

    const uploadExcelFile = useMutation(
        async (file: File) => {
            const spot = await getVinFile();
            await HttpUtils.uploadFileViaPut(spot.uploadLink, file);

            const result = await WhatsAppMassiveSending.processVinFile(spot.id, file.name);
            setCustomersCount(result.impactedCustomers);
            state.update({
                vinFile: {
                    id: spot.id,
                    size: file.size,
                    name: file.name,
                    impactedCustomers: result.impactedCustomers,
                },
            });
        },
        {
            onMutate: () => onLoading(true),
            onSuccess: () => {
                toasters.success(
                    t('massiveSending.modal.vinUpload.uploadedText'),
                    t('massiveSending.modal.vinUpload.uploadedTitle')
                );
            },
            onSettled: () => onLoading(false),
            onError: (error) => {
                // restore previous file
                state.set('_vinFileObject', prevVinFile.current);

                if (isErrorResponse(error)) {
                    if (
                        hasCode(error, 'MassiveSending.InvalidVinFile-InvalidFileType') ||
                        hasCode(error, 'MassiveSending.InvalidVinFile-InvalidFileStructure')
                    ) {
                        toasters.danger(
                            t('massiveSending.modal.vinUpload.invalidFile'),
                            t('commonLabels.error')
                        );
                    } else if (hasCode(error, 'MassiveSending.InvalidVinFile-FileTooBig')) {
                        showFileTooBigToaster();
                    } else if (hasCode(error, 'MassiveSending.InvalidVinFile-TooManyVins')) {
                        showTooManyVinsToaster();
                    } else {
                        // that's not supposed to happen normally
                        toasters.danger('Internal server error', `${error.code}: ${error.message}`);
                    }
                }
            },
        }
    );

    const onVinFileSelected = useCallback(
        (f: File | null) => {
            if (!f) return;
            if (f.size > 2_000_000) {
                showFileTooBigToaster();
                return;
            }
            set('_vinFileObject', f ?? undefined);
            uploadExcelFile.mutate(f);
        },
        [uploadExcelFile, showFileTooBigToaster, set]
    );

    return (
        <>
            <DragAndDropFileInputControllable
                file={state.state._vinFileObject ?? null}
                onFileSet={onVinFileSelected}
                showLoader={uploadExcelFile.isLoading}
                disabled={uploadExcelFile.isLoading}
                defaultFile={
                    state.state._meta.uploadedFile
                        ? {
                              name: state.state._meta.uploadedFile.name,
                              size: state.state._meta.uploadedFile.sizeBytes,
                          }
                        : undefined
                }
                acceptedFormats={['xlsx']}
                additionalContent={
                    <div
                        style={{
                            flexGrow: 1,
                            display: 'flex',
                            alignItems: 'end',
                        }}
                    >
                        <RecipientsCount count={customersCount ?? 0} />
                    </div>
                }
            />
            <DividerWithMargin />
            <Grid container>
                <Grid item xs={12} md={6}>
                    <SectionHeader>
                        {t('settings.prospections.importCustomers.notes')}:
                    </SectionHeader>
                    <Parameter>
                        {t('settings.prospections.importCustomers.acceptedFormat')}
                    </Parameter>
                    <Parameter>
                        {t('settings.prospections.importCustomers.maximumAllowedSize')}
                    </Parameter>
                </Grid>
                <Grid item xs={12} md={6} style={{ display: 'flex', justifyContent: 'end' }}>
                    <Button
                        color={Colors.CM2}
                        label={t('settings.prospections.importCustomers.downloadTemplate')}
                        cmosVariant={'typography'}
                        Icon={DownloadIcon}
                        iconPosition="right"
                        onClick={downloadTemplate}
                    />
                </Grid>
            </Grid>
        </>
    );
}

function useGetPersistentSpot(): () => Promise<WhatsAppMassiveSending.CreateVinFileResponse> {
    return useCallback(async () => {
        let value: WhatsAppMassiveSending.CreateVinFileResponse | undefined;
        try {
            value = JSON.parse(localStorage['ms-vin-file-spot']);
        } catch {}

        const response = await WhatsAppMassiveSending.createVinFile(value?.id);
        localStorage['ms-vin-file-spot'] = JSON.stringify(response);
        return response;
    }, []);
}
