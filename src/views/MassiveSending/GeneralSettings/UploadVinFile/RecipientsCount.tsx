import { styled } from '@mui/material';
import { useAppTranslation } from 'common/hooks/useAppTranslation';

type RecipientsCountProps = {
    count: number;
};

const Label = styled('span')(({ theme }) => ({
    ...theme.typography.h6Roboto,
    color: theme.palette.neutral[8],
}));
const Count = styled('span')(({ theme }) => ({
    color: theme.palette.primary.main,
    ...theme.typography.h4Roboto,
}));
export default function RecipientsCount({ count }: RecipientsCountProps) {
    const { t } = useAppTranslation();

    return (
        <div>
            <Label>{t('massiveSending.modal.vinUpload.totalMessage')}:</Label>{' '}
            <Count>{count}</Count>
        </div>
    );
}
