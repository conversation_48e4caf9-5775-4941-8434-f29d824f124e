import Dropdown, { CmosDropdownVariant } from 'common/components/Inputs/Dropdown';
import { OptionData } from 'common/components/Inputs/Dropdown/DataOption';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useCallback, useMemo } from 'react';
import { MenuPlacement } from 'react-select';

export type LastVisitPickerProps = {
    value?: number | null;
    onChange: (value: number | null) => void;
    menuPlacement?: MenuPlacement;
    cmosVariant?: CmosDropdownVariant;
};

export default function LastVisitPicker({ value, onChange, ...props }: LastVisitPickerProps) {
    value = value ? value : -1; // treat null or undefined as "all months"
    const { t } = useAppTranslation();
    const options = useMemo(
        () =>
            [-1, 1, 3, 6, 12, 24].map((x) => {
                if (x < 0)
                    return {
                        value: -1,
                        label: t('massiveSending.modal.lastVisit.allMonths'),
                    };
                return {
                    label: t('massiveSending.modal.lastVisit.months', { count: x }),
                    value: x,
                };
            }),
        [t]
    );

    const selectedOption = useMemo(() => options.find((x) => x.value === value), [value, options]);
    const handleChange = useCallback(
        (opt: OptionData<number> | null) => {
            if (opt === null) return;
            onChange(opt.value < 0 ? null : opt.value);
        },
        [onChange]
    );

    return (
        <Dropdown
            menuPosition="fixed"
            label={t('massiveSending.modal.lastVisit.label')}
            placeholder={t('massiveSending.modal.lastVisit.placeholder')}
            cmosVariant="grey"
            onChange={handleChange}
            options={options}
            value={selectedOption}
            {...props}
        />
    );
}
