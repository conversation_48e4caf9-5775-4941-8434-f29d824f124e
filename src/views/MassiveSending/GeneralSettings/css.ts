import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        display: 'flex',
        flexDirection: 'column',
        minHeight: 300,
    },
    label: {
        ...theme.typography.body2,
        fontFamily: 'Inter, sans-serif',
        fontWeight: 'bold',
        display: 'inline-flex',
        color: theme.palette.neutral[4],
        margin: 0,
        marginBottom: 5,
    },
    controlRow: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: 650,
    },
    annualControlRow: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-between',
        width: 670,
    },
    inputWithLabel: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
    },
    controlLabel: {
        ...theme.typography.body2,
        fontFamily: 'Inter, sans-serif',
        fontWeight: 'bold',
        display: 'inline-flex',
        color: theme.palette.neutral[4],
        marginBottom: 8,
    },
    dot: {
        ...theme.typography.body2,
        color: theme.palette.primary.main,
        marginLeft: 5,
    },
    dateText: {
        ...theme.typography.body2,
        fontWeight: 'bold',
        color: theme.palette.neutral[5],
        '&::placeholder': {
            fontWeight: 'normal',
            color: '#676767',
        },
    },
    divider: {
        width: '100%',
        marginTop: 24,
        marginBottom: 24,
        borderTop: `1px solid ${theme.palette.neutral[6]}`,
    },
    customerTypes: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    customerTypeSelectors: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
    },
    customerTypeSelector: {
        marginRight: 12,
    },
    sendingTypes: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    sendingTypeEditText: {
        fontFamily: 'Inter',
        fontStyle: 'normal',
        fontSize: '12px',
        lineHeight: '15px',
        color: theme.palette.neutral[4],
        fontWeight: 700,
    },
    sendingTypeName: {
        ...theme.typography.body2,
        color: theme.palette.primary.main,
        fontWeight: 700,
        marginRight: 3,
        marginLeft: 3,
    },
    radios: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginLeft: -5,
    },
    radio: {
        width: 150,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    singleSendingSettings: {
        marginTop: 8,
        height: 100,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
    annualSendingSettings: {
        height: 200,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-around',
        alignItems: 'flex-start',
    },
    birthdaySettings: {
        height: 200,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-around',
        alignItems: 'flex-start',
    },
    switchBase: {
        padding: 1,
        opacity: 1,
        height: '100%',
        border: '3px solid #323947',
        '&.Mui-checked': {
            transform: 'translateX(13px)',
            border: '0px',
            color: theme.palette.common.white,
            '& + .MuiSwitch-track': {
                backgroundColor: '#36ce91',
                opacity: 1,
                border: 'none',
            },
            '& * .MuiSwitch-thumb': {
                width: 15,
            },
        },
        '&$focusVisible .MuiSwitch-thumb': {
            color: '#6462F3',
            border: '6px solid #fff',
        },
        '& * .MuiSwitch-thumb': {
            width: 12,
            height: 15,
            boxShadow: 'none',
        },
    },
    controlRowSwitch: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: 650,
        paddingTop: '20px',
    },
    switchLabel: {
        ...theme.typography.body2,
        color: theme.palette.neutral[5],
        fontWeight: 400,
        marginRight: 7,
        marginLeft: 9,
    },
    switchLabelActive: {
        color: theme.palette.primary.main,
        fontWeight: 700,
    },
    switch: {
        '& .MuiSwitch-switchBase': {
            '&.Mui-checked': {
                color: '#FFFFFF',
                '& .MuiSwitch-thumb:before': {
                    color: '#FFFFFF',
                },
                '& + .MuiSwitch-track': {
                    opacity: 1,
                },
            },
            '& .MuiSwitch-thumb:before': {
                color: '#FFFFFF',
            },
        },
    },
    infoIcon: {
        color: theme.palette.neutral[5],
        marginLeft: 5,
        width: 8,
        height: 8,
        minWidth: 8,
        cursor: 'pointer',
    },
    focusVisible: {},
    tooltip: {
        ...theme.typography.h8Roboto,
        fontWeight: 'normal',
        margin: 0,
        padding: 10,
        maxWidth: 222,
        borderRadius: 15,
        color: theme.palette.neutral[7],
        backgroundColor: theme.palette.neutral[1],
        boxShadow: '0px 0px 5px rgba(200, 200, 200, 0.41)',
    },
}));

export default useStyles;
