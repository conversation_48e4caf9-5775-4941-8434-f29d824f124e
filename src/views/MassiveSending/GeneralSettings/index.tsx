import { Info } from '@mui/icons-material';
import { Grid, styled } from '@mui/material';
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import Tooltip from '@mui/material/Tooltip';
import { MassSendingType } from 'api/WhatsAppMassiveSending';
import DateFormField from 'common/components/Inputs/DateFormField';
import InputWrapper from 'common/components/Inputs/InputWrapper';
import TextFormField from 'common/components/Inputs/TextField';
import TimeFormField from 'common/components/Inputs/TimeFormField';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { DateTime } from 'luxon';
import moment from 'moment';
import { useCallback, useMemo } from 'react';
import { isDateValid } from 'utils';
import MultipleBrandPicker from 'views/Components/MultipleBrandPicker';
import MultipleModelPicker from 'views/Components/MultipleModelPicker';
import MultipleYearPicker from 'views/Components/MultipleYearPicker';
import RadioButtonArray from 'views/Components/RadioButtonArray';
import { useMassiveSendingState } from '../Modal/context';
import LastVisitPicker from './LastVisitPicker';
import UploadVinFile from './UploadVinFile';
import useStyles from './css';

const FirstSection = styled('section')({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 2fr',
    gap: 16,
});

// const SecondSection = styled('section')({
//     display: 'grid',
//     gridTemplateColumns: '1fr 1fr',
//     gap: 16,
// });

type MassiveSendingGeneralSettingsProps = {
    onLoading: (loading: boolean) => void;
};

const MassiveSendingGeneralSettings = ({ onLoading }: MassiveSendingGeneralSettingsProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    const trackedState = useMassiveSendingState();
    const { state, update, set } = trackedState;
    const { sendingDate } = state;
    const time: [number, number] = useMemo(() => {
        const m = moment(sendingDate);

        return [m.hour(), m.minute()];
    }, [sendingDate]);

    const brandNames = state.selector.brands.map((x) => x.name);
    const brandsWithModels = state.selector.brands;

    const setTime = useCallback(
        (value: [number, number]) => {
            const d = moment(state.sendingDate).hour(value[0]).minute(value[1]).millisecond(0);
            update({
                sendingDate: d.toISOString(),
            });
        },
        [state, update]
    );

    return (
        <Box className={classes.root}>
            <TextFormField
                name="sending-name"
                label={t('massiveSending.modal.sendingName')}
                cmosVariant="grey"
                isRequired
                showValidationIndicators
                placeholder={t('massiveSending.modal.enterTheNameOfYouNewMassiveSending')}
                value={state.name}
                onChange={(event) => {
                    update({ name: event.target.value });
                }}
            />
            <div className={classes.divider} />
            <FirstSection>
                <DateFormField
                    disablePast
                    name="massive-sending-date"
                    enableEnterComplete
                    label={t('massiveSending.modal.dateOfSending')}
                    variant="grey"
                    disabled={state.sendImmediately}
                    isRequired={!state.sendImmediately}
                    showValidationIndicators={!state.sendImmediately}
                    onChange={(date) => {
                        if (date && isDateValid(date))
                            update({
                                sendingDate: date.toISOString(),
                            });
                    }}
                    value={DateTime.fromISO(state.sendingDate).toJSDate()}
                />
                <TimeFormField
                    closeAfterSelect
                    name="massive-sending-time"
                    cmosVariant="grey"
                    disabled={state.sendImmediately}
                    isRequired={!state.sendImmediately}
                    showValidationIndicators={!state.sendImmediately}
                    label={t('massiveSending.modal.timeOfSending')}
                    value={time}
                    onChange={setTime}
                />

                <InputWrapper
                    isRequired
                    showValidationIndicators
                    hideRoundIndicator
                    label={t('massiveSending.sendingType._')}
                >
                    <RadioButtonArray<MassSendingType>
                        value={state.type}
                        values={(['Personalized', 'WarrantyCampaign'] as MassSendingType[]).map(
                            (v) => ({
                                label: t(`massiveSending.sendingType.${v}`),
                                value: v,
                                id: v,
                                tooltipDescription: t(
                                    `massiveSending.sendingType.placeholders.${v}`
                                ),
                            })
                        )}
                        onChange={(value) => set('type', value)}
                    />
                </InputWrapper>
            </FirstSection>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FormControlLabel
                    checked={state.sendImmediately}
                    onChange={(_, checked) => {
                        update({ sendImmediately: checked });
                    }}
                    control={<Switch color="primary" className={classes.switch} />}
                    style={{
                        margin: 0,
                        marginLeft: -5,
                    }}
                    label={t('massiveSending.modal.sendRightNow')}
                />
                <Tooltip
                    classes={{ tooltip: classes.tooltip }}
                    title={
                        <>
                            {t(
                                'massiveSending.modal.theSendingWillBeMadeAtTheEndOfTheMassiveSendingCreation'
                            )}
                        </>
                    }
                    placement="right"
                >
                    <Info fontSize="small" color="primary" />
                </Tooltip>
            </Box>
            <>
                <div className={classes.divider} />
                {state.type === 'WarrantyCampaign' && (
                    <UploadVinFile onLoading={onLoading} state={trackedState} />
                )}
                {state.type === 'Personalized' && (
                    <>
                        <Grid container spacing={1}>
                            <Grid item md={6} xs={12}>
                                <MultipleBrandPicker
                                    disablePlaceholderHint
                                    value={brandNames}
                                    cmosVariant="default"
                                    onChange={(v) =>
                                        set('selector', (s) => {
                                            const existingBrands = s.brands.filter((x) =>
                                                v.includes(x.name)
                                            );
                                            const newBrands = v.filter(
                                                (x) =>
                                                    existingBrands.findIndex(
                                                        (xx) => xx.name === x
                                                    ) === -1
                                            );
                                            return {
                                                ...s,
                                                brands: [
                                                    ...existingBrands,
                                                    ...newBrands.map((x) => ({
                                                        name: x,
                                                        models: [],
                                                    })),
                                                ],
                                            };
                                        })
                                    }
                                />
                            </Grid>
                            <Grid item md={6} xs={12}>
                                <MultipleModelPicker
                                    disablePlaceholderHint
                                    filterBrands={brandNames}
                                    value={brandsWithModels}
                                    cmosVariant="default"
                                    onChange={(v) =>
                                        set('selector', (s) => ({
                                            ...s,
                                            brands: s.brands.map(
                                                (x) => v.find((b) => b.name === x.name) ?? x
                                            ),
                                        }))
                                    }
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <MultipleYearPicker
                                    disablePlaceholderHint
                                    value={state.selector.years}
                                    cmosVariant="default"
                                    onChange={(v) =>
                                        set('selector', { ...state.selector, years: v })
                                    }
                                />
                            </Grid>
                            <Grid item xs={6}>
                                <LastVisitPicker
                                    menuPlacement="top"
                                    value={state.selector.lastVisitTimeInMonths}
                                    cmosVariant="default"
                                    onChange={(v) =>
                                        set('selector', (s) => ({
                                            ...s,
                                            lastVisitTimeInMonths: v,
                                        }))
                                    }
                                />
                            </Grid>
                        </Grid>
                    </>
                )}
            </>
        </Box>
    );
};

export default MassiveSendingGeneralSettings;
