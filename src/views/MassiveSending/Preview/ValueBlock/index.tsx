import useStyles from './css';

export type ValueBlockProps = {
    label: string;
    value: string;
};
const ValueBlock = ({ label, value }: ValueBlockProps) => {
    const classes = useStyles();
    return (
        <div className={classes.value}>
            <div className={classes.valueLabel}>{label}</div>
            <div className={classes.valueText}>{value}</div>
        </div>
    );
};

export default ValueBlock;
