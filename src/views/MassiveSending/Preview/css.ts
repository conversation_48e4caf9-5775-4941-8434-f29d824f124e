import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    root: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    header: {
        fontFamily: 'Roboto, sans-serif',
        fontStyle: 'normal',
        fontWeight: 'normal',
        fontSize: '14px',
        lineHeight: '14px',
        color: theme.palette.neutral[7],

        marginTop: 16,
        marginBottom: 16,
    },
    blocks: {
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        width: '100%',
    },
    valuesBlock: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        padding: 16,
        width: '50%',
    },
    value: {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        marginBottom: 16,
    },
    divider: {
        width: '100%',
        marginTop: 20,
        marginBottom: 20,
        height: 0,
        borderTop: '1px solid',
        borderColor: theme.palette.neutral[4],
    },
    image: {
        width: 269,
        height: 152,
        objectFit: 'cover',
        objectPosition: 'center',
        borderRadius: 15,
    },
    imageDiv: {
        width: 250,
        height: 120,
        borderRadius: 15,
        backgroundSize: '250px 120px',
        backgroundColor: '#FFF',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center top',
    },
    goToSiteButton: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        width: 285,
        backgroundColor: '#FFFFFF',
        borderRadius: 5,
        marginTop: 3,
    },
    goToSiteButtonInnerContainer: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',

        color: theme.palette.primary.main,
        fontWeight: 700,

        width: 677,
        height: 32,
        borderRadius: 5,
    },
    goToSiteIcon: {
        margin: '0px 5px 4px 0px',
    },
}));

export default useStyles;
