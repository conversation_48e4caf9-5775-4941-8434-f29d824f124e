import { MassSendingParagraphsOption, MassSendingType } from 'api/WhatsAppMassiveSending';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import moment from 'moment';
import MassSendingWhatsAppMessagePreview from '../MassSendingWhatsAppMessagePreview';
import ValueBlock from './ValueBlock';
import useStyles from './css';

export type MassiveSendingPreviewProps = {
    name: string;
    paragraphsOption: MassSendingParagraphsOption;
    mainText: string;
    startText: string;
    endText: string;
    image?: string | null;
    dateTime: string;
    type: MassSendingType;
    impactedCustomers?: number;
};

const MassiveSendingPreview = ({
    name,
    image,
    dateTime,
    paragraphsOption,
    mainText,
    startText,
    endText,
    type,
    impactedCustomers,
}: MassiveSendingPreviewProps) => {
    const classes = useStyles();
    const { t } = useAppTranslation();

    return (
        <div className={classes.root}>
            <div className={classes.header}>
                {t('massiveSending.modal.verifyThatAllTheDataOfYourMassiveSendingAreCorrect')}
            </div>
            <div className={classes.blocks}>
                <div className={classes.valuesBlock}>
                    <ValueBlock
                        label={t('massiveSending.modal.nameOfTheSending')}
                        value={name || ''}
                    />
                    <div className={classes.divider} />
                    <ValueBlock
                        label={t('massiveSending.sendingType._')}
                        value={t('massiveSending.modal.singleSending')}
                    />
                    <div className={classes.divider} />
                    <ValueBlock
                        label={t('massiveSending.modal.dateOfSending')}
                        value={`${moment(dateTime).format('DD/MMMM/yyyy - HH:mm')}hrs`}
                    />
                    {typeof impactedCustomers !== 'undefined' && (
                        <>
                            <div className={classes.divider} />
                            <ValueBlock
                                label={t('massiveSending.modal.vinUpload.totalMessage')}
                                value={impactedCustomers + ''}
                            />
                        </>
                    )}
                </div>
                <div>
                    <MassSendingWhatsAppMessagePreview
                        type={type}
                        paragraphsOption={paragraphsOption}
                        mainText={mainText}
                        startText={startText}
                        endText={endText}
                        image={image}
                        dateTime={dateTime}
                    />
                </div>
            </div>
        </div>
    );
};

export default MassiveSendingPreview;
