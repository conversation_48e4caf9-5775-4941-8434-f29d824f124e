import { MassSendingType } from 'api/WhatsAppMassiveSending';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { useMemo } from 'react';

function renderMsParameters(value: string) {
    return value.split(/(\[.*?\])/gi).map((element, i) => {
        if (element.startsWith('[') && element.endsWith(']')) {
            return <b key={`${i}p`}>{element}</b>;
        }

        return <span key={`${i}no`}>{element}</span>;
    });
}

export type MsGreetingTextProps = {
    type: MassSendingType;
    hideWeWriteFrom?: boolean;
};

export default function MsGreetingText({ type, hideWeWriteFrom }: MsGreetingTextProps) {
    const { t } = useAppTranslation();

    const helloStr = t(`massiveSending.modal.hello`);
    const hello = useMemo(() => renderMsParameters(helloStr), [helloStr]);

    const weWriteFromStr = t(`massiveSending.modal.weWriteFrom.${type}`);
    const weWriteFrom = useMemo(() => renderMsParameters(weWriteFromStr), [weWriteFromStr]);

    const greetingMessage = useMemo(
        () => (
            <span>
                <p>
                    {hello} {!hideWeWriteFrom && weWriteFrom}
                </p>
            </span>
        ),
        [hello, weWriteFrom, hideWeWriteFrom]
    );

    return greetingMessage;
}
