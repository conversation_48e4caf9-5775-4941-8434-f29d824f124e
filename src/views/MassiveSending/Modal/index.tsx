import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import { styled } from '@mui/material';
import Backdrop from '@mui/material/Backdrop';
import Box from '@mui/material/Box';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Fade from '@mui/material/Fade';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import WhatsAppMassiveSending, { UpdateMassSendingDto } from 'api/WhatsAppMassiveSending';
import { Button } from 'common/components/Button';
import CancelModal from 'common/components/Popups/CancelModal';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import useToasters from 'common/hooks/useToasters';
import { Colors } from 'common/styles/Colors';
import { scrollbarStyle } from 'common/styles/ScrollbarStyles';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { WithTrackedStateProps } from 'utils/trackedStateContext';
import MassiveSendingGeneralSettings from '../GeneralSettings';
import MassiveSendingMessageSettings from '../MessageSettings';
import MassiveSendingPreview from '../Preview';
import MassiveSendingStepper from '../Stepper';
import { MassSendingData, isMassSendingDataEmpty, withMassiveSendingModalState } from './context';
import useStyles from './css';

export type MassiveSendingModalProps = {
    open: boolean;
    onClose?: () => void;
};

const Actions = styled('div')({
    display: 'flex',
    justifyContent: 'flex-end',
    width: 403,
});

const MassiveSendingModal = ({
    open,
    onClose,
    trackedState: { set, state, update, getChanges, changed },
}: WithTrackedStateProps<MassSendingData, MassiveSendingModalProps>) => {
    const classes = useStyles();
    const toasters = useToasters();
    const { t } = useAppTranslation();
    const [step, setStep] = useState<number>(0);
    const [tab, setTab] = useState<'first' | 'second' | 'third'>('first');
    const [cancelModalOpen, setCancelModalOpen] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // isBlockedByLoading=true when something is loading and therefore you can't save/continue to next step
    const [isBlockedByLoading, setBlockedByLoading] = useState(false);
    const [isMessageTextValid, setMessageTexValid] = useState(true);

    const sendingIsEmpty = useMemo(() => isMassSendingDataEmpty(state), [state]);
    const sendingHasNotBeenEdited = !changed;

    useEffect(() => {
        if (!isMessageTextValid) {
            toasters.warning(t('massiveSending.modal.nlWarn'), t('commonLabels.error'));
        }
    }, [isMessageTextValid, toasters, t]);

    const handleCloseModal = useCallback(() => {
        setStep(0);
        setTab('first');
        if (onClose) {
            onClose();
        }
    }, [onClose]);

    const onCloseHandler = useCallback(() => {
        if (sendingHasNotBeenEdited || sendingIsEmpty) {
            handleCloseModal();
        } else {
            // Open Cancel Modal
            setCancelModalOpen(true);
        }
    }, [handleCloseModal, sendingHasNotBeenEdited, sendingIsEmpty]);

    const handleTabChange = (_: React.SyntheticEvent, value: 'first' | 'second' | 'third') => {
        setTab(value);
    };

    const handleGoBackClick = () => {
        if (state.id) {
            if (sendingHasNotBeenEdited) {
                handleCloseModal();
            } else {
                // Open Cancel Modal
                setCancelModalOpen(true);
            }
        } else {
            if (step === 0) {
                if (sendingIsEmpty) {
                    handleCloseModal();
                } else {
                    // Open Cancel Modal
                    setCancelModalOpen(true);
                }
            } else {
                setStep((old) => old - 1);
            }
        }
    };

    const handleContinueButtonClick = async () => {
        if (state.id || step === 2) {
            setIsLoading(true);
            try {
                if (state.id) {
                    var changes = getChanges();
                    const sendingDate = changes.sendImmediately
                        ? moment().toISOString()
                        : changes.sendingDate;

                    const update: UpdateMassSendingDto = {
                        paragraphsOption: changes.paragraphsOption,
                        mainText: changes.mainText,
                        startText: changes.startText,
                        endText: changes.endText,
                        automaticReply: changes.automaticReply,
                        contentType: changes.contentType,
                        name: changes.name,
                        sendingDate,
                        type: changes.type,
                        vinFileId: changes.vinFile?.id,
                    };

                    if (state.type === 'Personalized') {
                        update.selector = changes.selector;
                    } else if (state.type === 'WarrantyCampaign') {
                    }

                    await WhatsAppMassiveSending.update(state.id, update, {
                        image: state.contentType === 'ImageAndText' ? changes.image : undefined,
                    });
                    toasters.success(state.name, t('massiveSending.modal.massiveSendingModified'));
                } else {
                    await WhatsAppMassiveSending.create(
                        {
                            withImage: !!state.image,
                            paragraphsOption: state.paragraphsOption,
                            mainText: state.mainText,
                            startText: state.startText,
                            endText: state.endText,
                            automaticReply: state.automaticReply,
                            sendingDate: state.sendImmediately
                                ? moment().toISOString()
                                : state.sendingDate,
                            contentType: state.contentType,
                            name: state.name,
                            selector: state.selector,
                            type: state.type,
                            vinFileId: state.vinFile?.id,
                        },
                        {
                            image: state.contentType === 'ImageAndText' ? state.image : undefined,
                        }
                    );
                    toasters.success(
                        state.name,
                        t('massiveSending.modal.massiveSendingSuccessfullyCreated')
                    );
                }
                handleCloseModal();
            } catch (error) {
                toasters.danger(t('toasters.errorOccurredWhenSaving'), t('toasters.errorOccurred'));
            }
            setIsLoading(false);
        } else {
            setStep((old) => old + 1);
        }
    };

    const isGeneralSettingsValid = useMemo(() => {
        if (!state) {
            return false;
        }

        return Boolean(state.name);
    }, [state]);

    const isMessageSettingsValid = !(
        !Boolean(state) ||
        !Boolean(state.mainText.replace(/<[^>]+>/g, '').trim()) ||
        (state.paragraphsOption === 'ThreeParagraphsAndButtons' &&
            (!Boolean(state.startText.replace(/<[^>]+>/g, '').trim()) ||
                !Boolean(state.endText.replace(/<[^>]+>/g, '').trim()) ||
                !Boolean(state.automaticReply.replace(/<[^>]+>/g, '').trim()))) ||
        (state.contentType === 'ImageAndText' && !state.imageUrl) ||
        !isMessageTextValid
    );

    const isFormValid = isGeneralSettingsValid && isMessageSettingsValid;

    const isContinueDisabled = useMemo(() => {
        if (state.id) {
            return !isFormValid || sendingHasNotBeenEdited;
        } else {
            switch (step) {
                case 0:
                    return !isGeneralSettingsValid;
                case 1:
                    return !isMessageSettingsValid;
                case 2:
                    return !isFormValid;
                default:
                    return false;
            }
        }
    }, [
        step,
        state,
        isFormValid,
        isGeneralSettingsValid,
        isMessageSettingsValid,
        sendingHasNotBeenEdited,
    ]);

    const previewElement = state ? (
        <MassiveSendingPreview
            type={state.type}
            name={state.name}
            paragraphsOption={state.paragraphsOption}
            mainText={state.mainText}
            startText={state.startText}
            endText={state.endText}
            image={state.contentType === 'ImageAndText' ? state.imageUrl : undefined}
            dateTime={state.sendingDate}
            impactedCustomers={state.vinFile?.impactedCustomers}
        />
    ) : undefined;

    const { type, paragraphsOption } = state;
    useEffect(() => {
        if (!open || (state.id && !changed)) {
            return;
        }

        const personalized = t(`massiveSending.modal.template.Personalized`);
        const warranty = t(`massiveSending.modal.template.WarrantyCampaign`);

        const mainText = t(`massiveSending.modal.template.mainText`);
        const startText = t(`massiveSending.modal.template.startOfMessage`);
        const endText = t(`massiveSending.modal.template.endOfMessage`);
        const automaticReply = t(`massiveSending.modal.template.automaticReply`);

        const setText = (currentText: string, initialText: string) => {
            if (currentText === initialText || currentText === '') {
                return initialText;
            } else {
                return currentText;
            }
        };

        set('mainText', (v) => {
            if (paragraphsOption === 'Paragraph') {
                return type === 'WarrantyCampaign'
                    ? setText(v, warranty)
                    : setText(v, personalized);
            } else {
                return setText(v, mainText);
            }
        });

        if (paragraphsOption === 'ThreeParagraphsAndButtons') {
            set('startText', (v) => setText(v, startText));
            set('automaticReply', (v) => setText(v, automaticReply));
            set('endText', (v) => setText(v, endText));
        }
    }, [type, paragraphsOption, t, set, open]);

    return (
        <Dialog
            aria-labelledby="transition-modal-title"
            aria-describedby="transition-modal-description"
            open={open}
            onClose={onCloseHandler}
            closeAfterTransition
            BackdropComponent={Backdrop}
            BackdropProps={{
                timeout: 500,
            }}
            PaperProps={{
                sx: { boxShadow: 'none', padding: 3 },
            }}
            maxWidth={false}
            scroll="paper"
            style={{ zIndex: 1299 }}
        >
            <DialogTitle component="div">
                <Box sx={{ display: 'grid', gridTemplateColumns: '1fr auto' }}>
                    <Typography sx={{ typography: 'h5Roboto' }}>
                        {t(
                            `massiveSending.modal.${
                                state.id ? 'editMassiveSending' : 'newMassiveSending'
                            }`
                        )}
                    </Typography>
                    <Actions>
                        <Button
                            cmosVariant={'filled'}
                            color={Colors.Neutral3}
                            customStyles={{ width: 164, marginRight: 12 }}
                            label={t('massiveSending.modal.goBack')}
                            onClick={handleGoBackClick}
                        />
                        <Button
                            cmosVariant={'filled'}
                            color={Colors.Success}
                            customStyles={{ width: 227 }}
                            disabled={
                                isContinueDisabled ||
                                isLoading ||
                                isBlockedByLoading ||
                                (step === 1 && !isMessageSettingsValid)
                            }
                            showLoader={isLoading}
                            label={t(
                                `massiveSending.modal.${
                                    state.id
                                        ? 'saveChanges'
                                        : step === 2
                                        ? 'confirmSending'
                                        : state.vinFile &&
                                          step === 0 &&
                                          state.type === 'WarrantyCampaign'
                                        ? 'uploadAndContinue'
                                        : 'continue'
                                }`
                            )}
                            onClick={handleContinueButtonClick}
                        />
                    </Actions>
                </Box>
                {state.id ? (
                    <TabContext value={tab}>
                        <Tabs
                            className={classes.tabs}
                            value={tab}
                            onChange={handleTabChange}
                            indicatorColor="primary"
                            textColor="primary"
                        >
                            <Tab
                                className={classes.tab}
                                label={t('massiveSending.modal.tabs.sendingRules')}
                                value="first"
                            />
                            <Tab
                                className={classes.tab}
                                label={t('massiveSending.modal.tabs.whatsAppTemplate')}
                                value="second"
                            />
                            <Tab
                                className={classes.tab}
                                label={t('massiveSending.modal.tabs.preview')}
                                value="third"
                            />
                        </Tabs>
                    </TabContext>
                ) : (
                    <MassiveSendingStepper activeStep={step} />
                )}
            </DialogTitle>
            <SDialogContent>
                <Fade in={open}>
                    <div>
                        {state.id ? (
                            <TabContext value={tab}>
                                <TabPanel value="first">
                                    <MassiveSendingGeneralSettings
                                        onLoading={setBlockedByLoading}
                                    />
                                </TabPanel>
                                <TabPanel value="second">
                                    <MassiveSendingMessageSettings
                                        onValidation={setMessageTexValid}
                                    />
                                </TabPanel>
                                <TabPanel value="third">{previewElement}</TabPanel>
                            </TabContext>
                        ) : (
                            <>
                                {step === 0 && (
                                    <MassiveSendingGeneralSettings
                                        onLoading={setBlockedByLoading}
                                    />
                                )}
                                {step === 1 && (
                                    <MassiveSendingMessageSettings
                                        onValidation={setMessageTexValid}
                                    />
                                )}
                                {step === 2 && previewElement}
                            </>
                        )}
                        <CancelModal
                            open={cancelModalOpen}
                            title={t(
                                `massiveSending.modal.${
                                    state.id
                                        ? 'cancelMassiveSendingEdition?'
                                        : 'cancelMassiveSending?'
                                }`
                            )}
                            cancelCaptionButton={t('massiveSending.modal.goBack')}
                            confirmationCaptionButton={t('massiveSending.modal.cancel')}
                            onCancel={() => {
                                handleCloseModal();
                                setCancelModalOpen(false);
                            }}
                            onClose={() => setCancelModalOpen(false)}
                        />
                    </div>
                </Fade>
            </SDialogContent>
        </Dialog>
    );
};

const SDialogContent = styled(DialogContent)({
    ...scrollbarStyle(),
});

export default withMassiveSendingModalState(MassiveSendingModal);
