import { Theme } from '@mui/material';
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles((theme: Theme) => ({
    tabs: {
        ...theme.typography.body2,
        '& .MuiTabs-indicator': {
            backgroundColor: '#7175FA',
        },
        '& .Mui-selected': {
            ...theme.typography.body2,
            color: theme.palette.primary.main,
            fontWeight: 'bold !important',
        },
        '& .MuiButtonBase-root': {
            minWidth: 0,
            alignItems: 'left',
            marginRight: 25,
        },
        '& .MuiTab-root': {
            paddingLeft: 0,
            paddingRight: 0,
            textTransform: 'none',
            ...theme.typography.body2,
        },
    },
    tab: {
        root: {
            ...theme.typography.body2,
        },
        selected: {
            fontWeight: 'bold',
        },
    },
}));

export default useStyles;
