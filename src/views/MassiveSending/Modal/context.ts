import {
    MassSendingContentType,
    MassSendingDetailsDto,
    MassSendingParagraphsOption,
    MassSendingSelectorDto,
    MassSendingType,
    MassSendingVinFileDto,
} from 'api/WhatsAppMassiveSending';
import { isSomethingChangedWithExceptions } from 'common/hooks/useTrackedState';
import isEqual from 'lodash/isEqual';
import moment from 'moment';
import createTrackedStateContext from 'utils/trackedStateContext';

export type MassSendingData = {
    id: string | null;
    paragraphsOption: MassSendingParagraphsOption;
    mainText: string;
    startText: string;
    endText: string;
    automaticReply: string;
    contentType: MassSendingContentType;
    imageUrl: string | null;
    image: File | null;
    name: string;
    sendingDate: string;
    sendImmediately: boolean;
    selector: MassSendingSelectorDto;
    type: MassSendingType;
    vinFile: {
        id: string;
        name: string;
        size: number;
        impactedCustomers: number;
    } | null;
    _vinFileObject?: File;

    // meta data that is ignored when calculating if data was changed or not
    _meta: {
        uploadedFile?: MassSendingVinFileDto;
    };
};

function defaultData(): MassSendingData {
    return {
        id: null,
        paragraphsOption: 'Paragraph',
        mainText: '',
        startText: '',
        endText: '',
        automaticReply: '',
        contentType: 'Text',
        imageUrl: null,
        image: null,
        name: '',
        sendingDate: moment().add(1, 'd').toISOString(),
        sendImmediately: false,
        type: 'Personalized',
        vinFile: null,
        selector: {
            years: [],
            brands: [],
            lastVisitTimeInMonths: null,
        },
        _meta: {},
    };
}

const defaultValue = defaultData();

export function isMassSendingDataEmpty(s: MassSendingData): boolean {
    return isEqual(s, defaultValue);
}

function downloadFile(url: string): Promise<File> {
    return fetch(url, {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache',
        },
    })
        .then((r) => r.blob())
        .then((b) => new File([b], 'image', { type: b.type }));
}

export async function createMassSendingCopyFromDetails(
    details: MassSendingDetailsDto
): Promise<MassSendingData> {
    let image: File | null = null;
    if (details.content.image) {
        // try to download file from S3
        try {
            image = await downloadFile(details.content.image);
        } catch (e) {
            alert(
                'failed to download mass sending image, see error in the console for more detail'
            );
            console.error(e);
        }
    }

    return {
        id: null, // remove id because it's going to be a new mass sending
        name: details.name,
        sendingDate: moment(details.sendAt).isBefore(moment())
            ? defaultData().sendingDate
            : details.sendAt,
        contentType: details.content.type,
        paragraphsOption: details.content.paragraphsOption,
        mainText: details.content.mainText,
        startText: details.content.startText,
        endText: details.content.endText,
        automaticReply: details.content.automaticReply,
        imageUrl: image ? URL.createObjectURL(image) : null,
        image,
        sendImmediately: false,
        selector: details.selector,
        type: details.type,
        vinFile: details.vinFile
            ? {
                  id: details.vinFile.id,
                  name: details.vinFile.name,
                  size: details.vinFile.sizeBytes,
                  impactedCustomers: details.vinFile.impactedCustomers,
              }
            : null,
        _meta: {
            uploadedFile: details.vinFile,
        },
    };
}

export const {
    Provider: MassiveSendingModalStateProvider,
    useStateHook: useMassiveSendingState,
    withState: withMassiveSendingModalState,
} = createTrackedStateContext<MassSendingData>(defaultData, (old, state, changed) => {
    return isSomethingChangedWithExceptions(
        changed,
        [
            ...(state.type === 'WarrantyCampaign' ? ['selector'] : ['vinFile', '_vinFileObject']),
            '_meta',
        ] // excluded properties
    );
});
