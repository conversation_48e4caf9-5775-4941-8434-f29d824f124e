import { MassSendingParagraphsOption, MassSendingType } from 'api/WhatsAppMassiveSending';
import { useAppTranslation } from 'common/hooks/useAppTranslation';
import { Interweave } from 'interweave';
import { Trans } from 'react-i18next';
import WhatsAppMessagePreview, {
    WhatsAppMessagePreviewProps,
} from 'views/Components/WhatsAppMessagePreview';
import MsGreetingText from '../MsGreetingText';

export type MassSendingWhatsAppMessagePreviewProps = Omit<
    WhatsAppMessagePreviewProps,
    'text' | 'hasButtons'
> & {
    paragraphsOption: MassSendingParagraphsOption;
    mainText: string;
    startText: string;
    endText: string;
    type: MassSendingType;
};

export default function MassSendingWhatsAppMessagePreview({
    paragraphsOption,
    mainText,
    startText,
    endText,
    dateTime,
    image,
    type,
}: MassSendingWhatsAppMessagePreviewProps) {
    const { t } = useAppTranslation();
    return (
        <WhatsAppMessagePreview
            hasButtons={paragraphsOption === 'ThreeParagraphsAndButtons'}
            image={image}
            text={
                <div>
                    <MsGreetingText
                        type={type}
                        hideWeWriteFrom={paragraphsOption === 'ThreeParagraphsAndButtons'}
                    />
                    <p />
                    {paragraphsOption === 'ThreeParagraphsAndButtons' && (
                        <>
                            <Interweave content={startText} />
                            <p />
                        </>
                    )}
                    <Interweave content={mainText} />
                    <p />
                    {paragraphsOption === 'ThreeParagraphsAndButtons' && (
                        <>
                            <Interweave content={endText} />
                            <p />
                        </>
                    )}
                    {paragraphsOption === 'Paragraph' ? (
                        <span>{t('massiveSending.modal.weLookForwardToHearingFromYou!')}</span>
                    ) : (
                        <Trans
                            i18nKey={'massiveSending.modal.paragraphs.buttonsLabel'}
                            components={{
                                1: <b />,
                            }}
                        />
                    )}
                </div>
            }
            dateTime={dateTime}
        />
    );
}
