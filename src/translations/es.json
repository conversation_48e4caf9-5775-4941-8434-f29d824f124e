{"dateFormats": {"long": "DD/MM/YY hh:mm a", "short": "dd/MM/yy", "dayOfMonth": "DD [de] MMM", "timeOfToday": "hh:mm a", "timeOfTodayNoZero": "h:mm a", "longTimeAgo": "DD [de] MMMM [de] YYYY[,] hh:mm a", "shortISO": "YYYY-MM-DD", "date": "DD MMM YY", "longDate": "DD [de] MMMM [de] YYYY", "longDateWithDay": "dddd[,] DD [de] MMMM YYYY", "shortDateWithDay": "dddd[,] DD [de] MMMM", "longDateWithDayLuxon": "cccc',' dd 'de' MMMM yyyy", "shortDateWithDayLuxon": "cccc',' dd 'de' MMMM", "midDate": "DD/MMM/YYYY", "navigationDate": "DD [de] MMMM", "longEs": "DD/MM/YY HH:mm", "longEn": "MM/DD/YY HH:mm", "shortDate": "DD/MM/YY", "time": "hh:mma", "luxon": {"shortRightSlashDivider": "dd/MM/yyyy"}}, "search": {"startTyping": "Empieza a escribir...", "nothingFound": "<PERSON><PERSON>", "apply": "Aplicar"}, "richTextEditor": {"multiple": "<PERSON><PERSON><PERSON><PERSON>", "p": "<PERSON><PERSON><PERSON><PERSON>", "h": "Encabezado {{level}}", "url": "URL", "urlPlaceholder": "Ingrese la URL aquí", "insertLink": "<PERSON>ser<PERSON> enlace", "editLink": "<PERSON><PERSON> enlace", "removeLink": "<PERSON><PERSON><PERSON> enlace", "newLink": "Nueva ventana", "sourceCodeTitle": "Editar correo desde código"}, "titles": {"appointments": "Citas", "analysis": "<PERSON><PERSON><PERSON><PERSON>", "workshop": "Taller", "bdcIndicators": "Indicadores BDC", "aftersalesCrm": "CRM Posventa", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tasks": "<PERSON><PERSON><PERSON>", "vehicleDatabase": "BD de vehículos", "workshopPlanner": "<PERSON><PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "inspectionItems": "Puntos de inspección", "followUp": "Segu<PERSON><PERSON><PERSON>", "reports": "Reportes", "surveys": "Encuestas", "massTasks": "<PERSON><PERSON><PERSON>", "massiveSending": "Env<PERSON>s <PERSON>", "indicators": "Indicadores", "indicatorsWhenCrmEnabled": "Indicadores Taller", "settings": {"settings": "Configuraciones", "cm": "ClearMechanic", "general": "Generales", "prospections": "Auto Prospección", "workshopPlanner": "<PERSON><PERSON><PERSON>"}, "conversations": "Conversaciones", "notFound": "Página no encontrada", "yourAccountDoesNotHaveThisFunctionalityEnabled": "Su cuenta no tiene habilitada esta funcionalidad."}, "headerOptions": {"conversations": "Conversaciones", "notifications": "Notificaciones", "settings": "Configuración", "tutorials": "Tu<PERSON>les", "reportAProblem": "Reportar un Problema", "support": "Soporte", "provideADetailedDescriptionOfYourProblem": "Por favor descríbenos a detalle el problema", "placeholder": "Escribe a detalle el problema aquí.", "send": "Enviar", "submitModalHeader": "¡Nos pondremos en contacto contigo pronto!", "submitModalText": "Un miembro del equipo de ClearMechanic One Solution se comunicará contigo dentro de un día hábil.", "thankYou": "¡<PERSON><PERSON><PERSON>! ", "accountOverdueMessage": "Su cuenta tiene un saldo vencido. Envíe su comprobante a <1 href='mailto:{{email}}'>{{email}}</1> para evitar que su cuenta sea suspendida. Obtenga hasta {{percentage}}% de descuento con pago anticipado anual."}, "commonLabels": {"ok": "Ok", "refresh": "Actualizar", "uploaded": "<PERSON><PERSON><PERSON><PERSON>", "uploadedDate": "Fecha/Hora de Carga", "attachedFiles": "Archivos adjuntos", "file": "Archivo", "files": "Archivos", "add": "Agregar", "accessDenied": "Acceso denegado", "appointment": "Cita", "note": "<PERSON>a", "selectJobDescription": "Escriba o seleccione trabajo a realizar", "order": "Orden", "loading": "Cargando", "somethingWentWrong": "Algo salió mal", "tryAgain": "Intentar otra vez", "enabledProducts": "Productos habilitados", "enabledProductsItem": "y", "footer": "ClearCheck y sus afiliados. | Vísitanos en:", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "save": "Guardar", "saveChanges": "Guardar cambios", "noDataSelector": "Sin datos encontrados", "optional": "(opcional)", "formatDateMedium": "dd/mm/aa", "logout": "<PERSON><PERSON><PERSON>", "settings": "Configuraciones", "send": "Enviar", "back": "Regresar", "goBack": "Regresar", "continue": "<PERSON><PERSON><PERSON><PERSON>", "success": "¡Éxito!", "noMatchesFound": "No se encontraron coincidencias", "maxCharacters": "caracteres como máximo", "sections": {"appt": "Mó<PERSON>lo de c<PERSON>", "ap": "Auto Prospección", "cm": "ClearMechanic", "pt": "<PERSON><PERSON><PERSON>"}, "inCharge": "<PERSON><PERSON><PERSON>", "assignedTo": "Asignado a", "result": "<PERSON><PERSON><PERSON><PERSON>", "technician": "[Title:Technician]", "name": "Nombre", "email": "<PERSON><PERSON><PERSON>", "phone": "Teléfono", "mobile": "<PERSON><PERSON><PERSON>", "make": "<PERSON><PERSON>", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "vin": "VIN", "vinPlaceholder": "Ingrese el VIN", "plates": "Placas", "mileage": "[Title:Mileage]", "mileagePlaceholder": "Ingrese el [Lower:Mileage]", "priorities": {"urgent": "Urgente", "med": "Sugerida", "low": "Baja", "suggested": "Sugerido"}, "number": "Número", "message": "Men<PERSON><PERSON>", "attachFile": "Adjuntar archivo", "shortcuts": "<PERSON><PERSON><PERSON>", "insertLink": "Insertar vínculo", "addComment": "Agregar commentario", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "Búsqueda", "upload": {"uploadImage": "<PERSON><PERSON> imagen", "changeImage": "Cambiar imagen", "noAttachedImage": "<PERSON> imagen adjunta", "uploadedImage": "Imagen cargada", "preview": "Vista previa"}, "businessName": "Razón social", "nextServiceDate": "Fecha de próximo servicio", "calculatedMileage": "[Title:Mileage] calculado", "serviceAdvisor": "[Title:ServiceAdvisor]", "averageTicket": "Ticket promedio", "followUpDate": "<PERSON><PERSON>", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "promise": "Promesa", "pause": "Pausar", "resume": "<PERSON><PERSON><PERSON>", "today": "Hoy", "yesterday": "Ayer", "tomorrow": "<PERSON><PERSON><PERSON>", "error": "Error", "paymentMethod": "Método de pago", "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "sunday": "Domingo"}, "location": "Sucursal", "step": "Paso {{number}}", "duration": "Duración", "yes": "Si", "no": "No", "all": "Todos", "doNotDelete": "No eliminar", "delete": "Eliminar", "taxIdentification": "[Sentence:TaxIdentification]", "clear": "Bo<PERSON>r", "date": "<PERSON><PERSON>", "orderType": "<PERSON><PERSON><PERSON> de orden", "frequentBrands": "Marcas frecuentes", "allBrands": "Todas las marcas", "timePeriods": {"d": "días", "d-singular": "día", "w": "semanas", "w-singular": "semana", "M": "meses", "M-singular": "mes", "y": "años", "y-singular": "año"}, "project": "Proyectar", "stopProjecting": "<PERSON><PERSON> de proyectar", "notes": "Notas", "downloading": "Descargando", "downloadingTakesLonger": "La descarga tardará un poco más de lo esperado inicialmente, por favor espere.", "select": "Seleccione", "edit": "<PERSON><PERSON>", "copy": "Copiar", "enter": "Ingrese ", "create": "<PERSON><PERSON><PERSON>", "blockLeavingConfirmation": {"title": "¿Desea salir sin guardar los cambios realizados?", "body": "Si decide salir ahora, los cambios realizados no serán guardados.", "cancelButton": "Regresar", "continueButton": "<PERSON><PERSON>, salir sin guardar cambios"}, "apply": "Aplicar"}, "login": {"emailLabel": "Correo electrónico", "emailPlaceholderLabel": "Ingresa tu correo electrónico", "passwordLabel": "Contraseña", "passwordPlaceholderLabel": "Ingresa tu contraseña", "buttonLoginLabel": "In<PERSON><PERSON>", "rememberMyData": "<PERSON><PERSON> mis datos", "forgotMyPassword": "¿Olvidaste tu contraseña?", "forgotPasswordTitle": "¿Olvidaste tu contraseña?", "toResetPassword": "Para restaurar tu contraseña, ", "enterYouremailAddress": "ingresa tu dirección de correo electrónico", "youMayNeedToCheckSpam": ". Es posible que tengas que consultar tu carpeta de spam.", "forgotPasswordSuccess1": "Si ya existe una cuenta de ClearMechanic para ", "forgotPasswordSuccess2": ", un correo electrónico será enviado con instrucciones.", "forgotPasswordSuccess3": "¡Realizado!", "resetYourPassword": "Restablece tu contraseña", "resetPassword": "Restablecer contraseña", "newPassword": "Nueva contraseña", "enterNewPassword": "Introduce tu nueva contraseña", "confirmNewPassword": "Confirma tu nueva contraseña", "yourPasswordWasChanges": "Tu contraseña ha sido restablecida exitosamente.", "returnToLoginPage": "In<PERSON><PERSON>", "wrongPassword": "Contrase<PERSON>", "loggedOutDueToLackOfPerms": "Se ha cerrado la sesión debido a un error interno de CMOS. Le pedimos disculpas por las molestias, vuelva a iniciar sesión."}, "inspectionForms": {"cloneToAll": "Copiar a Todas las Cuentas", "enterpriseFormNotice": "Esta es una Plantilla de Inspección compartida con todas las cuentas. Edición de plantilla Inspección afectará a todas las cuentas.", "systems": {"title": "Sistema", "addButton": "Agregar nuevo sistema", "confirmationDelete": {"title": "Eliminar sistema de inspección", "body": "Eliminar este sistema de inspección, <strong>eliminará permanentemente todos los puntos de inspección</strong> que están dentro de este sistema.", "cancel": "No eliminar", "confirm": "Eliminar sistema"}, "notifications": {"successfullyUpdatedBody": "Sistema de inspección guardado correctamente.", "successfullyUpdatedTitle": "Configuración actualizada", "successfullyDeleteTitle": "Sistema de inspección eliminado", "successfullyDeleteBody": "Sistema de inspección eliminado correctamente."}, "nameField": {"label": "", "placeholder": "Ingrese nombre del sistema"}}, "templates": {"addButton": "Crear nueva plantilla de inspección", "confirmationDelete": {"title": "Eliminar plantilla de inspección", "body": "Eliminar esta plantilla de inspección, <strong>eliminará permanentemente todos los sistemas y puntos de inspección</strong> que están dentro de esta plantilla.", "cancel": "No eliminar", "confirm": "Eliminar plantilla"}, "showDetails": "Mostrar \"Detalles\" en la forma de inspección visualizada por el cliente", "displayInPdf": "Mostrar esta forma de inspección en la Orden enviada al cliente", "notifications": {"successfullyUpdatedBody": "Plantilla de inspección guardada correctamente.", "successfullyUpdatedTitle": "Configuración actualizada", "successfullyDeleteTitle": "Forma de inspección eliminada", "successfullyDeleteBody": "Plantilla de inspección eliminada correctamente."}, "nameField": {"label": "Nueva plantilla de inspección", "placeholder": "Ingresa nombre de plantilla de inspección"}}, "templateItems": {"nameDuplicatedError": "El mismo elemento de inspección ya existe en este formulario de inspección.", "addButton": "<PERSON><PERSON><PERSON>", "seeAllButton": "Ver todo", "confirmationDelete": {"title": "Eliminar punto de inspección", "body": "¿Quieres eliminar este punto de inspección?", "cancel": "No eliminar", "confirm": "Eliminar punto de inspección"}, "notifications": {"successfullyUpdatedTitle": "Configuración actualizada", "successfullyUpdatedBody": "Punto de inspección guardado correctamente.", "successfullyDeleteTitle": "Forma de inspección actualizada", "successfullyDeleteBody": "Punto de inspección eliminado correctamente."}, "newInspectionItemField": {"label": "", "placeholder": "Ingrese punto de inspección"}, "tooltip": "Solo se pueden editar los nombres de los ”puntos personalizados”. Hay “puntos oficiales” de uso común en muchos talleres automotrices cuyos nombres no se pueden editar. Si no desea utilizar un punto oficial, puede eliminarlo de la Forma de Inspección y agregar un punto personalizado con el nombre de su elección."}, "estimateForm": {"title": "[Title:Estimate]: {{item}}", "subtotal": "Subtotal", "total": "Total", "taxes": "Impuestos", "fieldQuantity": {"label": "Cantidad", "placeholder": "Ingrese cantidad"}, "fieldUnitPrice": {"label": "Precio unitario", "placeholder": "Ingrese precio unitario"}, "fieldHours": {"label": "<PERSON><PERSON>", "placeholder": "Ingrese horas"}, "field$Hours": {"label": "Costo por hora", "placeholder": "Ingrese costo por hora"}, "cleanButton": "Limpiar datos", "successfully": {"title": "Configuración actualizada", "body": "[Title:Estimate] guardada correctamente."}}, "commentsForm": {"successfully": {"title": "Configuración actualizada", "body": "Comentarios en punto de inspección guardados correctamente."}, "title": "Comentarios", "label": "<PERSON><PERSON><PERSON>", "starttyping": "Comience a escribir para añadir comentarios. Presione la tecla “Enter” para confirmar nuevo comentario.", "hideInfo": "(Ocultar información)", "moreInfo": "(Más información)", "tipsCustomComments": "Sugerencias para los comentarios personalizados.", "addCustomCommentsInfo": "<b>Agregar comentario:</b> Comienza a teclear para agregar un nuevo comentario personalizado. Cuando termines, presiona la tecla \"Enter\" y el comentario se guardará.", "deleteCustomCommentsInfo": "<b>Eliminar comentario:</b> <PERSON>uedes eliminar el comentario personalizado dando clic en el ícono \"X\" que está a un lado del mismo comentario a eliminar.", "InstantSynchronizationInfo": "<b>Sincronización instantánea de los comentarios:</b> Todos los cambios en los comentarios personalizados se sincronizarán con tu aplicación móvil de ClearMechanic.", "officialCommentsInfo": "<b>Comentarios oficiales:</b> Notarás que ciertos comentarios personalizados no pueden ser eliminados. Estos son los comentarios oficiales de nuestra base de datos maestra disponible para todos nuestros clientes. Por favor contáctenos si consideras que algún comentario oficial es inapropiado o inadecuado.", "fillInTheBlankInfo": "Comentarios de tipo “Captura en el espacio vacío”.", "tipsStyleComments": "Sugerencias para los comentarios de tipo “Rellenar el espacio en blanco”.", "addStyleCommentsInfo": "<b>Agregar comentario:</b> Para agregar un comentario de tipo \"Rellenar el espacio en blanco\" debe agregar \"_:\" ejemplo: \"_: Km\" \"Color:_\". <PERSON><PERSON><PERSON> haya terminado, presione la tecla \"Enter\" y el comentario se guardará.", "deleteStyleCommentsInfo": "<b>Eliminar comentario:</b> <PERSON>uedes eliminar el comentario de tipo \"Rellenar el espacio en blanco\" dando clic en el ícono \"X\" que está a un lado del mismo comentario a eliminar.", "commentsTypeCaptureInformationInfo": "<b>Commentarios de tipo \"Capturar información\":</b> Estos comentarios estan hechos para poder agregar información personalizada en la Orden.", "note": "Nota:"}, "detailsForm": {"successfully": {"title": "Configuración actualizada", "body": "Detalles en el punto de inspección guardados correctamente."}, "title": "Detalles", "label": "<PERSON><PERSON><PERSON>", "placeholder": "Escriba un detalle aquí", "starttyping": "Comience a escribir para agregar detalles."}, "evidenceModal": {"count": "{{n}} de {{count}} evidencias", "videoInProcessing": "El video se está cargando en nuestro sistema, intenta visualizarlo más tarde.", "videoLoader": "El vídeo se está procesando", "uploadedFromGallery": "<PERSON>gado desde la galería"}, "addItem": {"addItem": "Ag<PERSON><PERSON> punto", "seeAll": "Ver todo"}, "normalView": {"item": "PUNTO", "jobsAndInspectionItems": "TRABAJOS Y PUNTOS DE INSPECCIÓN", "comments": "COMENTARIOS", "subtotal": "SUBTOTAL", "approved": "APROBADO", "itemTotal": "Total de punto:", "system": "Sistema:", "other": "<PERSON><PERSON><PERSON>", "campaign": "Campaña"}, "spreadsheetView": {"partsHeading": "[Title:Parts]", "laborHeading": "[Title:Labor]", "item": "Punt<PERSON>", "jobsAndInspectionItems": "Trabajos y Puntos de Inspección", "parts": "[Upper:PartNumber]", "quantity": "CANT.", "availability": "DISP.", "availabilityComments": "Comentarios ({{n}})", "availabilityCommentsUpdated": "Comentarios de disponibilidad actualizados", "availabilityCommentsUpdatedText": "Los comentarios de disponibilidad se han actualizado correctamente", "availabilityCommentPlaceholder": "Ingresa un comentario", "availabilityCommentDeleted": "Comentario de disponibilidad eliminado", "availabilityCommentDeletedText": "El comentario de disponibilidad se ha eliminado correctamente", "unitCost": "COSTO U.", "unitPrice": "PRECIO U.", "hours": "HRS.", "hourPrice": "PRECIO HR.", "subtotal": "Subtotal", "approved": "Aprobado", "addItem": "Ag<PERSON><PERSON> punto", "itemTotal": "Total de punto:", "discount": "DESC.", "campaign": "Campaña"}, "inspectionItemsModal": {"inspectionItems": "Puntos de inspección", "listAllInspectionItems": "Lista de todos los puntos de inspección existentes", "search": "Búsqueda"}, "editEstimateModal": {"title": "[Title:Estimate]: {{- name}}", "quantity": "Cantidad", "quantityPlaceholder": "Ingresa cantidad", "unitPrice": "Precio unitario", "unitPricePlaceholder": "Ingresa precio unitario", "hours": "<PERSON><PERSON>", "hoursPlaceholder": "Ingresa horas", "hourPrice": "<PERSON><PERSON> hora", "hourPricePlaceholder": "Ingresa precio por hora", "subtotal": "Subtotal:", "taxes": "Impuestos:", "total": "Total:", "cleanData": "Limpiar datos", "comments": "Comentarios", "repair": "[Title:Repair]", "parts": "[Title:Parts]", "labor": "[Title:Labor]", "unitCost": "Costo unitario", "unitCostPlaceholder": "Ingresa costo unitario", "discount": "Descuento:"}, "deleteEstimateModal": {"title": "<PERSON><PERSON><PERSON> fila", "body": "¿Estás seguro que deseas borrar esta fila?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Sí"}, "validationQuoterModal": {"title": "Campos incompletos", "message": "Completa los siguientes campos obligatorios para enviar la información de la Orden a {{integrationAccountName}}: {{fieldName}}", "button": "Completar campos"}, "savedEstimateTitle": "[Title:Estimate] actualizada", "savedEstimateBody": "[Title:Estimate] guardada correctamente.", "orderConfirmationModal": {"title": "¿<PERSON><PERSON><PERSON> solo en ClearMechanic?", "message": "Las Órdenes creadas desde el Tablero web no se sincronizarán con {{accountName}}. Para crear la Orden en {{accountName}}, use la app móvil CM Advanced.", "question": " ¿Desea continuar y crear la Orden solo en ClearMechanic?", "cancelButton": "No, cancelar", "confirmButton": "Si, continuar"}}, "newOrder": {"title": "Nueva Orden", "createOrder": "<PERSON><PERSON><PERSON>", "number": "Númer<PERSON> Orden", "numberPlaceholder": "Ingrese número de Orden", "numberLimitWarning": {"title": "Límite de caracteres alcanzado.", "body": "El número de orden no puede superar los 50 caracteres."}}, "orders": {"createOrder": "Nueva Orden", "createOrderTooltip": "Crear nueva Orden", "downloadOrderTooltip": "Descargar “Reporte de Actividad - Formas de inspección”", "columnFiltersTooltip": "Personalizar columnas", "orderCommunication": {"sms": "SMS", "email": "Email", "wa": "Whatsapp", "smsEmail": "SMS y Email", "smsWa": "SMS y WhatsApp", "emailWa": "Email y WhatsApp", "smsEmailWa": "SMS, Email y WhatsApp"}, "searchAutocomplete": {"orders": "<PERSON><PERSON><PERSON>", "RO#": "Orden #", "customer": "Cliente", "plates": "Placas", "vin": "VIN", "phase": "Etapa", "dateCreation": "Creación de la Orden", "noOptions": "No encontramos coincidencias para su búsqueda"}, "filters": {"filter": "Filtrar (Tipo de Orden, VIN, placas, cliente, miembro del equipo)", "RO#": "# Orden", "orderType": "<PERSON><PERSON><PERSON> Orden", "vin": "VIN", "plates": "Placas", "customerName": "Nombre del cliente", "team": "Equipo", "teamMember": "Miembro del equipo", "memberInCharge": "[Title:ServiceAdvisor]", "hideFilters": "Esconder filtros", "search": "Buscar", "newSearch": "Nueva búsqueda", "start": "<PERSON><PERSON>o", "end": "Fin", "dateRangeError": "Error en rango de fechas", "selectADifferentDate": "Selecciona una fecha distinta."}, "tableHeaders": {"team": "Equipo", "RO#": "# Orden", "customerVehicle": "Cliente/Vehículo", "plates": "Placas", "charged": "<PERSON><PERSON>", "updated": "Actualizado", "inspection": "Inspección", "estimate": "[Title:Estimate]", "communication": "Comunicación", "orderType": "<PERSON><PERSON><PERSON>", "account": "C<PERSON><PERSON>", "lastAction": "Última Acción", "appointmentNumber": "Cita", "phase": "Etapa"}, "jobHistoryTableHeaders": {"jobDescription": "Descripción de trabajo", "status": "<PERSON><PERSON><PERSON>", "assigned": "<PERSON><PERSON><PERSON>", "scheduled": "Programación", "pause": "Pausa"}, "preview": {"seeOrderDetails": "<PERSON>er de<PERSON>le de orden", "seeOrderDetail": "<PERSON>er <PERSON><PERSON><PERSON> de Orden", "customerInformation": "Información de cliente", "vehicleInformation": "Información de vehículo", "businessOpportunity": "Oportunidad de negocio", "inspectionItems": "PUNTOS DE INSPECCIÓN", "maintenanceInformation": "Información de mantenimientos", "schedule": "Agendar", "appointment": "Cita", "followUp": "Segu<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "scheduleFollowUp": "<PERSON><PERSON><PERSON>", "followUpDate": "<PERSON><PERSON>", "viewConversation": "Ver conversación", "lastVisit": "Última visita", "callHistory": "<PERSON>l de llamadas", "phoneCallTo": "Llamada telefónica a", "started": "Iniciada", "finished": "Finalizada", "callRecord": "Grabación de llamada", "by": "Por", "seeMore": "<PERSON>er más", "hide": "Ocultar", "masterPhoneCallTooltip": "El usuario “Master” no puede realizar llamadas. Por favor, cree un miembro del equipo y añada un número de teléfono.", "phoneCallTooltip": "<PERSON><PERSON><PERSON> al cliente"}, "orderByType": {"Uploaded": "<PERSON><PERSON>", "Updated": "Actualizado"}, "status": {"Uploading": "Cargando", "LoadedPosted": "<PERSON><PERSON>", "SentEmail": "E-mail enviado", "SentSMS": "SMS enviado", "SentSMSAndEmail": "SMS y E-mail enviado", "SentWA": "WA enviado", "SentEmailAndWA": "E-mail y WA enviado", "SentSMSAndWA": "SMS y WA enviado", "SentSMSEmailAndWA": "SMS, E-mail y WA enviado", "SentInspectionForm": "Inspection Form enviado", "ApprovedByConsumer": "Aprobado por el Cliente", "DeclinedByConsumer": "Rechazado por el Cliente", "ApprovedByTeamMember": "Aprobado por Miembro del Equipo", "DeclinedByTeamMember": "Declinado por Miembro del Equipo", "UploadedCM1SAppointment": "<PERSON>gado desde cita", "ConvertedFromAppointment": "<PERSON>gado desde cita", "Closed": "<PERSON><PERSON><PERSON>", "UploadedFromDms": "<PERSON><PERSON> desde DMS", "UploadedFromOpenAPI": "Cargado desde Open API", "Reopened": "Re-abierta"}, "calls": {"dialing": "<PERSON><PERSON><PERSON><PERSON>", "call": "LLAMAR", "yourPhoneWillRing": "Tu teléfono sonará pronto...", "callsRegister": "Registro de llamadas", "callToPhone": "Llamar a teléfono", "callToMobile": "<PERSON><PERSON><PERSON> a celular", "makeCall": "<PERSON><PERSON>"}, "alertStatuses": {"title": "Estado de alerta", "active": "Alertas activas", "resolved": "Alertas resueltas"}, "estimateStatuses": {"title": "<PERSON><PERSON><PERSON> de [Lower:Estimate]", "yes": "[Title:Estimate] a<PERSON><PERSON><PERSON>", "no": "Sin [Lower:Estimate] a<PERSON><PERSON><PERSON>"}, "repairTypes": {"title": "<PERSON><PERSON><PERSON>", "Canceled": "Cancelada", "BodyShop": "Carrocería y pintura", "Diagnosis": "Diagnós<PERSON><PERSON>", "Dwa": "Usados", "Warranty": "Garantía", "Internal": "Interna", "GeneralRepair": "[Title:Repair] general", "ExpressService": "Servicio express", "AuthorizedWork": "Trabajo autorizado"}, "paymentMethodTypes": {"NA": "N/A", "Cash": "Dinero en efectivo", "CreditOrDebitCard": "Tarjeta de crédito o débito", "Check": "Cheque", "WireTransfer": "Transferencia bancaria"}, "estimatesPopover": {"estimate": "[Title:Estimate]", "approved": "Aprobado", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "totalApproved": "Total aprobado", "totalRejected": "Total rechazado", "totalEstimated": "Total cotizado"}, "alerts": "<PERSON><PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "assignedMember": "Miembro asignado", "involvedMember": "Miembro involucrado", "orderAlphabetically": "Ordenar alfabéticamente", "totalApproved": "Total aprobado", "totalDeclined": "Total rechazado", "approvedByTeamMember": "Aprobado por miembro del equipo", "declinedByTeamMember": "Rechazado por miembro del equipo", "phaseFilterPopupTitle": "Filtrar por etapa"}, "users": {"active": "Activo", "inactive": "Inactivo", "statusFilter": {"active": "Activos", "inactive": "Inactivos", "all": "Todos"}, "status": "<PERSON><PERSON><PERSON>", "addTeamMember": "Agregar miembro del equipo", "passwordSecurityMessage": "La contraseña debe contener al menos 8 caracteres, un número, una mayúscula y una minúscula.", "fullName": "Nombre completo", "jobPosition": "Puesto de trabajo", "email": "Correo electrónico", "allStatus": "All", "serviceAdvisorColor": "Color del [Title:ServiceAdvisor]", "selectAColor": "Selecciona un color", "generalInformation": "Información general", "schedules": "Ho<PERSON>io laboral", "schedule": {"availability": "Disponibilidad", "startOfWork": "Inicio de labores", "closureOfWork": "Cierre de labores", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "sunday": "Domingo", "appointmentReceptionLabel": {"title": "Activar horario de recepción de citas en línea", "description": "Este usuario tiene un horario específico para la recepción de citas agendadas en línea por el cliente."}, "receptionSchedule": "Horario de recepción", "receptionScheduleTooltip": "Activar horario de recepción de citas en línea", "appointmentsPerDay": "Citas por día", "appointmentsPerDayTooltip": "Cantidad máxima de citas que el miembro del equipo puede atender."}, "popupUserExist": {"title": "Miembro del equipo existente", "body": "Este miembro del equipo ya existe en la base de datos pero fue <b>“desactivado”</b>.", "question": "¿Desea reactivar este miembro del equipo?", "confirm": "Activar miembro"}, "notifications": {"userUpdated": "<PERSON><PERSON>rio actual<PERSON>", "rememberBody": "<PERSON><PERSON><PERSON>, su contraseña debe incluir al menos 8 caracteres, 1 letra mayúscula, 1 letra minúscula, 1 carácter especial (!, @, #, $,%, ^, &, *,?, Etc.).", "rememberTitle": "Contrase<PERSON>", "matchPasswordBody": "Las contraseñas no coinciden, inténtalo de nuevo.", "matchPasswordTitle": "Contraseña y confirmación de contraseña, incorrectas", "userNameIsUsedBody": "Este correo electrónico se encuentra en uso por otro miembro del equipo.", "dmsIDNumberIsUsedBody": "El ID DMS se encuentra en uso por otro miembro del equipo.", "employeeIdIsUsedTitle": "El ID de empleado ya existe", "employeeIdIsUsedBody": "Intente con otro número.", "userActivatedSuccefullyBody": "Usuario activado correctamente.", "userDeactivatedSuccefullyBody": "Usuario desactivado correctamente.", "userAddedSuccessfullyBody": "Miembro del equipo guardado correctamente.", "userUpdatedSuccessfullyBody": "Miembro del equipo actualizado correctamente.", "userActivatedSuccessfullyTitle": "Miembro del equipo activado", "userActivatedSuccessfullyBody": "Miembro del equipo activado correctamente.", "reviewPasswordTitle": "Revisa la contraseña", "reviewPasswordBody": "La nueva contraseña y confirmación de contraseña, no pueden ser iguales a la contraseña anterior. Por favor inténtalo de nuevo", "updatedSpecialty": "[Title:Specialty] actualizada", "dmsUserNameIsUsedBody": "Intente con otro nombre de usuario.", "dmsUserNameIsUsedTitle": "El nombre de usuario ya existe"}, "activeLabel": {"label": "Usuario activo", "part1": "Este usuario trabaja actualmente en tu establecimiento", "part2": "y si podrá tener acceso a ClearMechanic One Solution."}, "inactiveLabel": {"label": "Usuario inactivo", "part1": "Este usuario no trabaja actualmente en tu establecimiento", "part2": "y no podrá tener acceso a ClearMechanic One Solution."}, "initials": "Iniciales", "specialty": "[Title:Specialty]", "phone": "Número de teléfono", "mobile": "<PERSON><PERSON><PERSON>", "phoneExtension": "Extensión", "DMSID": "ID DMS", "password": "Contraseña", "confirmPassword": "Confirmar con<PERSON>", "employeeId": "ID de empleado", "3rPartyUserName": "Nombre de usuario en software tercero", "placeholder": {"fullName": "Ingresa el nombre", "jobPosition": "Seleccione puesto de trabajo", "initials": "AB", "email": "Correo del miembro del equipo", "specialty": "Seleccione una [Lower:Specialty]", "newSpecialty": "Nueva [Lower:Specialty]", "enterSpecialty": "Ingrese [Lower:Specialty]", "phone": "Ingrese el teléfono", "mobile": "Ingrese el celular", "phoneExtension": "000", "DMSID": "Ingresa ID DMS", "password": "Ingresa la contraseña", "confirmPassword": "Confirma la contraseña", "employeeId": "Ingresa ID de empleado", "3rPartyUserName": "Ingrese el nombre alternativo"}, "edit": {"title": "Editar miembro del equipo", "saveButton": "Guardar datos"}, "add": {"title": "Nuevo miembro del equipo", "saveButton": "Agregar nuevo miembro"}, "jobTitleTypes": {"Administrator": "Administrador", "Owner": "Director", "Manager": "<PERSON><PERSON><PERSON>", "WorkshopManager": "<PERSON><PERSON>", "Parts": "[Title:Parts]", "ServiceAdvisor": "[Title:ServiceAdvisor]", "Technician": "[Title:Technician]", "CarWasher": "<PERSON><PERSON><PERSON>", "AppointmentsExecutive": "Ejecutivo de Citas", "BdcSupervisor": "Supervisor BDC", "BdcAdvisor": "<PERSON><PERSON>or B<PERSON>", "Other": "<PERSON><PERSON>"}, "permissions": {"sectionTitle": "<PERSON><PERSON><PERSON>", "administrationSectionTitle": "Administrador", "administrationLabel": {"title": "Accesos de administrador", "description": "Este usuario puede ver o modificar cualquier configuración"}, "activitySectionTitle": "Actividad", "activeSwitchContainer": {"title": "Usuario activo", "description": "Este usuario trabaja actualmente en tu negocio."}, "showEstimationContainer": {"title": "Usar la opción “Mostrar [Lower:Estimate] a cliente”", "description": "Este usuario puede utilizar la opción “Mostrar [Lower:Estimate] a cliente”."}, "changeEstimationContainer": {"title": "Realizar cambios en [Lower:Estimate]", "description": "Este usuario puede realizar cambios en la [Lower:Estimate]."}, "generateReportContainer": {"title": "Generar reportes", "description": "Este usuario puede generar reportes."}, "seeAllAppointments": {"title": "Ver todas las citas", "description": "Este usuario puede ver todas las citas agendadas en tu negocio."}, "createAndEditAppointments": {"title": "Crear y editar citas", "description": "Este usuario puede crear o editar citas de todos los miembros del equipo."}, "seeAllOrdersContainer": {"title": "Ver todas las Órdenes", "description": "Este usuario puede ver todas las Órdenes creadas en tu negocio."}, "createAndEditOrdersContainer": {"title": "<PERSON><PERSON><PERSON> y editar <PERSON>", "description": "Este usuario puede crear o editar Órdenes de todos los miembros del equipo."}, "allowReopenOrdersContainer": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Este usuario puede re-abrir las todas las Órdenes cerradas."}, "editVehicles": {"title": "C<PERSON>r y editar vehículos", "description": "Este usuario puede crear y editar los vehículos así como los detalles."}, "editCustomers": {"title": "Crear y editar clientes", "description": "Este usuario puede crear y editar clientes."}, "seeTasks": {"title": "Mostrar “<PERSON><PERSON><PERSON>”", "description": "Este usuario podrá visualizar la pantalla “Tareas” en la app web."}, "seeAllTasks": {"title": "Ver todas las tareas", "description": "Este usuario puede ver las tareas de todos los miembros del equipo."}, "editTasks": {"title": "Crear y editar tareas de otros miembros del equipo", "description": "Este usuario puede crear y editar tareas de otros miembros del equipo."}, "allowManageMassTasks": {"title": "Gestionar tareas masivas", "description": "Este usuario puede ver, crear y editar tareas asignadas masivamente dirigidas a grupos de vehículos, como campañas o acciones de prospección."}, "sendMassiveSendings": {"title": "Enviar env<PERSON>s ma<PERSON>", "description": "Este usuario puede crear, editar y eliminar envíos masivos."}, "seeAllConversationContainer": {"title": "Ver todas las conversaciones", "description": "Este usuario puede visualizar las conversaciones de todos los miembros del equipo."}, "allowShowJobs": {"title": "Mostrar “Trabajos”", "description": "Este usuario podrá visualizar la pantalla “Trabajos” en app móvil."}, "allowSeeAllJobs": {"title": "Ver todos los “Trabajos”", "description": "Este usuario puede visualizar todos los trabajos de todos los miembros del equipo en la app móvil."}, "allowManageJobs": {"title": "Gestionar “Trabajos”", "description": "Este usuario puede iniciar, finalizar, pausar o reanudar los trabajos de todos los miembros del equipo."}, "editInfoInWorkshopPlannerContainer": {"title": "Editar información en Planeador de Taller", "description": "Este usuario puede editar información en Planeador de Taller."}, "notificationSectionTitle": "Notificaciones", "notificationsContainer": {"title": "Notificaciones de carga", "description": "Este usuario será notificado por correo electrónico cuando se cargué una Orden."}, "labels": {"administrator": "Administrador", "allowToUseShowEstimateToConsumerOption": "Permitir usar la opción “Mostrar [Lower:Estimate] al cliente”", "editEstimates": "Agregar / Cambiar [Lower:Estimates]", "allowSeeAppointments": "<PERSON><PERSON><PERSON> ver citas", "allowGenerateReports": "Permit<PERSON>r reportes", "shouldReceiveUploadNotifications": "Recibir notificaciones de carga por correo electrónico"}}, "dmsModal": {"emptyDmsIdField": "Campo “ID DMS” vacío", "theTeamMemberWillNotAbleWithOutDMSId": "El miembro del equipo no podrá utilizar ciertas funcionalidades al no tener agregado un ID del DMS."}}, "orderDetails": {"generalInfo": "Información general", "estimate": "[Title:Estimate]", "orderDetail": "Detalle de Orden #", "RO#": "Orden #", "seeActivityLog": "<PERSON><PERSON> bit<PERSON>", "printTooltip": "Imprimir PDF de la Orden o PDF de la [Lower:Estimate]", "printOrder": "Imprimir PDF de la Orden", "printOrderTooltip": "El PDF de la Orden incluye las siguientes secciones: \"Información general\", \"[Title:Estimate]\", \"Evidencias\" y \"Firmas digitales\".", "printJobs": "Imprimir PDF con trabajos", "printJobsTooltip": "El PDF con trabajos incluye las secciones \"Información general\", \"Trabajos\" y \"Firmas digitales\". No incluye la sección \"[Title:Estimate]\" ni \"Evidencias\".", "printCustomPdf": "Imprimir PDF personalizado", "printCustomPdfTooltip": "Este PDF incluirá únicamente las secciones que usted seleccione.", "customPdfModal": {"mainLabel": "Personalizar el PDF de la Orden", "secondaryLabel": "Secciones que el PDF debe incluir:", "constructor": {"generalInformation": "Información general", "customerInformation": "Información del Cliente", "vehicleInformation": "Información del Vehículo", "orderInformation": "Información de la Orden", "reasonsForVisit": "Motivos de visita", "estimate": "[Title:Estimate]", "summary": "Resumen", "notes": "Notas para el cliente sobre la [Lower:Estimate]", "jobsAndInspectionItems": "Trabajos y puntos", "discountsSubtotalTaxesTotal": "Descuentos, Subtotal, Impuestos, Total", "evidence": "Evidencias", "jobs": "Trabajos", "digitalSignatures": "Firmas digitales", "customerSignatureAtReception": "Firma del Cliente en la Recepción", "estimatedApprovalSignature": "Firma de Aprobación de la [Title:Estimate]", "customerSignatureAtDelivery": "Firma del Cliente en la Entrega", "adhesionContract": "Contrato de Adhesión", "privacyNotice": "Aviso de Privacidad", "businessSignature": "Firma del Negocio", "printPdf": "Imprimir PDF"}}, "repairOrderType": "<PERSON><PERSON><PERSON> Orden", "closeOrder": "<PERSON><PERSON><PERSON>", "reopenOrder": "Re-abrir <PERSON>", "closeOrderIntegratedAccount": {"success": {"title": "<PERSON>den cerrada", "description": "Orden cerrada correctamente en {{accountIntegratedName}}."}, "error": {"title": "No fue posible cerrar la Orden en {{accountIntegratedName}}", "description": "<PERSON><PERSON><PERSON>"}}, "paymentMethod": "Método de pago", "evidence": "Evidencias", "views": "Vistas", "orderLink": "Orden digital", "lastUpdate": "Última actualización", "lastCommunication": "Última comunicación", "customerInformation": "Información del cliente", "vehicleInformation": "Información del vehículo", "mileage": "[Title:Mileage]", "inCharge": "[Title:ServiceAdvisor]", "communication": "Comunicación con el cliente", "areYouSureToClose": "¿Estás seguro que deseas cerrar la Orden?", "customNote": "Nota personalizada", "firstNamePlaceholder": "Nombre", "lastNamePlaceholder": "Apellido", "emailPlaceholder": "Ingrese el correo electrónico", "phonePlaceholder": "Ingrese el teléfono", "mobilePlaceholder": "Ingrese el celular", "mileagePlaceholder": "Ingrese el [Lower:Mileage]", "platesPlaceholder": "Ingrese las placas", "invalidEmailBody": "Ingresa un correo electrónico válido", "invalidEmailTitle": "Correo electrónico no válido", "teamMembers": "Miembros del equipo", "orderInformation": "Información de la Orden", "inspectionFormsLabel": "Formas de inspección", "multiInspectionForm": "Multi inspection form", "printInspection": "Print inspection", "businessName": "Nombre comercial", "businessNamePlaceholder": "Ingrese el nombre comercial", "identDoc": "Documento de identificación", "notes": "Notas", "additionalNotes": {"notesForCustomer": "Notas visibles para el cliente", "noNotesForCustomer": "Sin notas visibles para el cliente.", "notesForInternal": "Notas internas", "noInternalNotes": "Sin notas internas.", "addNote": "<PERSON><PERSON><PERSON>", "enterNote": "Ingrese nota", "noteActivityLog": "Bitácora", "noteCannotBeEdited": "No es posible editar el texto.", "addedBy": "<PERSON><PERSON><PERSON><PERSON> por", "on": "el", "modifiedBy": "Modificado por", "at": "a las", "customer": "cliente", "teamMember": "miembro del equipo", "editNote": "<PERSON><PERSON>", "deleteNote": "Eliminar"}, "orderReasons": {"customerReasonsForVisit": "[Sentence:CustomerReasonsForVisit]", "noCustomerReasonsForVisit": "Sin [Lower:CustomerReasonsForVisit]", "textCannotBeEdited": "No es posible editar el texto", "workshopReasonsForVisit": " [Sentence:WorkshopReasonsForVisit]", "noWorkshopReasonsForVisit": "Sin [Lower:WorkshopReasonsForVisit]", "addReason": "Agregar motivo", "enterReason": "Ingrese motivo"}, "estimationDate": "<PERSON><PERSON> [Lower:Estimate]", "identDocType": {"placeholder": "Tipo de documento", "typeNotSaved": "El tipo de documento de identificación no guardado", "typeCannotBeEmpty": "El tipo de documento de identificación no puede estar vacío o únicamente con espacios en blanco", "typeDuplicated": "El tipo de documento de identificación está duplicado", "typeSaved": "El tipo de documento de identificación guardado", "typeSavedText": "El tipo de documento de identificación guardado correctamente"}, "identNum": "Número de identificación", "inspectionForms": {"priorityFilter": {"All": "Mostrar todos los puntos", "Red": "Mostrar los puntos rojos", "Yellow": "Mostrar los puntos amarillos"}, "payments": {"thirdParty": {"success": {"title": "Registro de pago creado", "description": "Registro de pago creado correctamente en {{integratedAccountName}}."}, "error": {"title": "No fue posible crear el registro de pago en {{integratedAccountName}}", "description": "Mensaje de error: {{errorMessage}}"}, "synchronization": {"success": "Pago registrado correctamente en {{integratedAccountName}}", "error": "El pago no pudo registrarse en {{integratedAccountName}}, intente nuevamente"}}, "dateFormat": "DD/MM/YYYY", "timeFormat": "HH:mm", "button": "Pagos", "register": {"tab": "Registrar pago", "description": "Miembro del equipo que registra el pago: ", "button": "Registrar pago", "fields": {"paymentMethodDropdown": {"title": "Método de pago", "fields": {"amountPaid": "<PERSON><PERSON> paga<PERSON>", "paymentMethod": {"label": "Método de pago", "placeholder": "Seleccione método de pago", "dropdown": {"cash": "Efectivo", "card": "Tarjeta de Débito / Crédito", "bank": "Transferencia Bancaria", "link": "<PERSON>"}}}}, "paymentDate": "Fecha de pago", "comments": {"label": "Comentarios", "placeholder": "Escriba aquí sus comentarios"}}, "anotherPaymentMethod": "<PERSON><PERSON><PERSON> mé<PERSON> de pago", "deleteModal": {"title": "¿Desea eliminar este método de pago?", "button": {"back": "Regresar", "delete": "Eliminar"}}, "details": {"title": {"left": "Detalle", "right": "Total"}, "approved": "Total aprobado", "method": {"left": "Método de pago", "right": "Importe"}, "pending": "Pendiente por pagar", "totalAmount": "Monto total pagado de la Orden"}, "notification": {"success": {"title": "Pago registrado", "description": "Pago registrado exitosamente."}, "error": {"title": "Error al registrar el pago", "description": "Ocurrió un error al intentar registrar el pago. Por favor, inténtelo nuevamente."}}}, "history": {"tab": "Historial de pagos", "withoutResults": {"title": "Sin historial de pagos", "description": "Por favor registre un pago para generar información."}, "record": {"title": "Registro de pago", "teamMember": "Miembro del equipo", "paymentDate": "Fecha del pago", "paymentMethod": "Método de pago", "amountPaid": "<PERSON><PERSON> paga<PERSON>", "totalAmountPaid": "Monto total pagado de la Orden", "paymentPending": "Pendiente por pagar", "comments": "Comentarios"}}}, "downloadEvidence": "Descargar evidencia", "downloadAllEvidence": "<PERSON><PERSON><PERSON> todas las fotos, videos y archivos (PDF, XLSX, DOC)", "downloadEvidenceError": "Se produjo un error al descargar la evidencia", "attachFile": "Adjuntar archivo", "showEstimateToCustomer": "Mostrar [Lower:Estimate] a cliente", "scheduleAppointment": "Agendar cita para puntos rechazados", "spreadSheetView": "Vista hoja de cálculo", "normalView": "Vista normal", "printEstimate": "Imprimir PDF de la [Lower:Estimate]", "printEstimateTooltip": "El PDF de la [Lower:Estimate] incluye únicamente la sección \"[Title:Estimate]\". No incluye las secciones de \"Información general\", \"Evidencias\", \"Trabajos\" ni \"Firmas digitales\".", "downloadPhotos": "Descargar fotos", "attachFileErrorTitle": "Error al adjuntar archivo", "attachFileErrorBody": "Las extensiones admitidas son: .doc, .docx, .xlsx, .xls, .text, .txt, .pdf", "attachFileSuccessTitle": "Archivo adjuntado correctamente", "attachFileSuccessBody": "Se ha adjuntado correctamente el archivo", "downloadEstimatesExcel": "Exportar cotización en un archivo Excel (.xlsx)"}, "estimateNotes": {"notesForCustomer": "Notas visibles para el cliente", "notesForInternal": "Notas internas", "addNote": "Agregar nota", "noteActivityLog": "Bitácora", "addedBy": "<PERSON><PERSON><PERSON><PERSON> por", "on": "el", "modifiedBy": "Modificado por", "at": "a las"}, "activityLog": {"activityLog": "Bitácora", "clientCannotSee": "Actividad de seguimiento sólo se accesan desde tu tablero de control. El cliente nunca las podrá ver.", "phoneCall": "Llamada telefónica", "estimateAdded": "[Title:Estimate] agregada", "insertDateTime": "Insertar fecha/hora", "addNewComment": "Agregar nuevo evento", "commentPlaceholder": "Escriba un nuevo evento", "addComment": "Agregar evento", "phoneCallComment": "Llamada realizada al cliente el {{- date}} a {{- time}}.", "estimateProvidedComment": "[Title:Estimate] entregada al cliente el {{- date}} a {{- time}}.\r\n\t[Title:Labor]:\r\n\t[Title:Parts]:\r\n\tOtros Trabajos:\r\n\tTotal:", "dateTimeComment": "{{- date}} {{- time}}", "orderCreation": "Orden #{{orderNumber}}, creada por {{userName}} el {{- date}} a {{- time}} desde el Tablero web de ClearMechanic. Orden no sincronizada con el software integrado."}, "totalEstimates": {"discount": "Descuentos:", "subtotal": "Subtotal:", "taxes": "Impuestos:", "total": "TOTAL:", "approved": "Aprobado:", "declined": "Rechazado:", "pending": "Pendiente:"}, "tower": "Torre", "phase": "Etapa: ", "noPhase": "Sin etapa", "closedOrder": "<PERSON>den cerrada", "enterTower": "Ingrese torre", "customerSignature": "Firma del cliente", "customerSignatureAtReception": "Firma del cliente en la Recepción", "customerSignatureAtDelivery": "Firma del cliente en la Entrega", "customerSignatureAdhesion": "Firma del contrato de adhesión", "customerSignaturePrivacyNotice": "Firma del contrato de aviso de privacidad", "customerNotifications": {"appointmentsNotificationsTitle": "Notificaciones de citas", "appointmentsNotificationsDescription": "El cliente recibirá notificaciones por WhatsApp automáticos como: citas, recordatorios, etc.", "massSendingsTitle": "Env<PERSON>s <PERSON>", "massSendingsDescription": "El cliente recibirá mensajes de envíos masivos por WhatsApp."}, "teamMemberSignature": "Firma del miembro del equipo", "serviceAdvisorDeliverySignature": "Firma del Asesor de Servicio en Entrega", "serviceAdvisorReceptionSignature": "Firma del Asesor de Servicio en Recepción", "signatureNameAndJobTitle": "Nombre y puesto del firmante", "brandPlaceholder": "Seleccione una marca", "modelPlaceholder": "Seleccione un modelo", "yearPlaceholder": "Seleccione un año", "reasonsForAppointment": "[Sentence:ReasonsForVisit]", "addedByCustomer": "Agregado por cliente", "addedByTeamMember": "Agregado por miembro del equipo", "packagePopup": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitle": "Seleccionar paquete relacionado a este punto:", "package": "<PERSON><PERSON><PERSON>", "withoutPackages": "Continuar sin agregar paquetes", "itemDisabledHint": "Este punto ya está en la Orden. Seleccione otros paquetes para agregar nuevos puntos de inspección.", "packageAdded": {"title": "<PERSON><PERSON><PERSON>", "text": "El paquete ha sido agregado a la cotización correctamente."}}, "closedOrderWarning": {"body1": "La Orden ha sido cerrada por {{userDisplayName}} el {{- formattedDateTime}}.", "body2": "El botón “Re-abrir Orden” solo está habilitado para miembros del equipo con el permiso activado “Re-abrir Órdenes”.", "title": "<PERSON>den cerrada", "button": "Entendido"}, "tooltipCopy": "Copiar", "tooltipTextCopied": "Se copió el texto", "tooltipComments": "Comentarios de inventario", "thirdParty": {"updateOrderInfo": {"success": {"title": "Orden actualizada", "description": "La información general de la Orden fue actualizada correctamente en {{accountIntegratedName}}."}, "error": {"title": "No fue posible actualizar la Orden en {{accountIntegratedName}}", "description": "Mensaje de error: {{errorMessage}}."}}}}, "phases": {"noPhase": "Sin etapa", "closedOrder": "<PERSON>den cerrada", "allPhases": "Todas las etapas"}, "settings": {"accessDeniedText": "Lo siento, no tiene permisos para acceder a la configuraciones.", "location": {"name": "Nombre de la sucursal", "namePlaceholder": "Ingrese nombre de la sucursal", "legalName": "Razón social", "legalNamePlaceholder": "Ingrese la razón social de la sucursal", "address": "Dirección", "addressPlaceholder": "Ingrese dirección de la sucursal", "phoneNumber": "Teléfono", "phoneNumberPlaceholder": "Ingrese teléfono de la sucursal", "emailAddress": "Correo electrónico", "emailAddressPlaceholder": "Ingrese correo electrónico de la sucursal", "website": "Página web", "websitePlaceholder": "Ingrese página web de la sucursal", "taxIdentification": "[Sentence:TaxIdentification]", "taxIdentificationPlaceholder": "Ingrese [Sentence:TaxIdentification] de la sucursal", "mondayToFridayHours": "Lunes a viernes", "mondayToFridayHoursPlaceholder": "Ingrese el [Lower:ServiceHours] de Lunes a viernes", "saturdayHours": "Sábado", "saturdayHoursPlaceholder": "Ingrese el [Lower:ServiceHours] del sábado", "header": "Personalizar la imagen en Órdenes", "dimensionsNote": "<strong>Nota:</strong> La imagen debe tener las siguientes dimensiones: 220x126", "orderLogoRemoved": "Logotipo de la Orden eliminada correctamente", "orderLogoUpdated": "Logotipo de la Orden guardada correctamente", "invalidLogo": "El logotipo para la Orden no es válido", "generalInformation": "Información general", "serviceHours": "[Sentence:ServiceHours]", "showBrandLogo": "Mostrar logo de la marca del vehículo"}, "cm": {"general": "General", "signatures": "<PERSON><PERSON><PERSON>", "surveys": {"header": "Encuestas", "enable": "Activar envío de encuestas", "days": "# de días después de asignar la Orden en la etapa que se enviará encuesta", "personalizeQuestions": "<PERSON><PERSON><PERSON>", "personalizeQuestionsTooltip": "Sólo se enviarán preguntas adicionales, si el cliente contesta la pregunta anterior.", "phase": "Etapa en la que enviará la encuesta", "personalizedQuestions": {"selectAnswerOption": "Seleccione opción de respuesta", "textBoxHeaderPrimary": "¡Hola [<1 className={{blueText}}>Nombre del cliente</1>]!, agradecemos su visita a [<1 className={{blueText}}>Nombre del Negocio</1>]. Le pedimos que nos ayude a responder la siguiente encuesta.", "textBoxHeaderSecondary": "¡Gracias por su respuesta!, le pedimos responder la siguiente pregunta.", "question": "Pregunta", "1to5Option": {"label": "Del 1 - 5", "placeholder": "¿Qué calificación le daría a la atención dada por nuestro personal?", "textBoxFooterPrimary": "Por favor, responda con un número del 1 al 5.", "textBoxFooterSecondary": "Considerando que 5 significa una calificación “excelente”."}, "1to10Option": {"label": "Del 1 - 10", "placeholder": "¿Qué calificación le daría a la atención dada por nuestro personal?", "textBoxFooterPrimary": "Por favor, responda con un número del 1 al 10.", "textBoxFooterSecondary": "Considerando que 10 significa una calificación “excelente”."}, "yesNoOption": {"label": "Si / No", "placeholder": "¿Considera que la atención brindada por nuestro personal fue excelente?", "textBoxFooterPrimary": "Por favor, responda el número 1 para “Sí” o el número 2 para “No”."}, "lineBrakesWarn": "Los saltos de línea no son permitidos.", "questionsSavedTitle": "Pregunta guardada", "questionsSavedText": "Pregunta guardada exitosamente", "addQuestion": "<PERSON><PERSON><PERSON>"}}, "campaigns": {"tab": "Campañas", "title": "General", "activateSwitch": {"label": "Activar campaña<PERSON>", "subText": "Mostrar las campañas de vehículos en la app móvil al ingresar el VIN."}, "columns": {"allBrands": "<PERSON><PERSON>", "allModels": "Todos", "allYears": "Todos", "allVins": "Todos"}, "noCampaigns": {"title": "¡Sin campañas registradas!", "description": "Por favor, comience a importar campañas."}, "seekerPlaceholder": "Buscar campaña", "priorityLevel": {"text": "Nivel de prioridad por defecto para puntos de inspección de campañas", "level": {"urgent": "Urgente", "suggested": "Sugerido", "ok": "Ok"}}, "deleteButton": {"title": "Eliminar campaña", "description": "¿Está seguro de que desea eliminar la campaña?", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "success": "Campaña eliminada exitosamente"}, "tableHeaders": {"campaignCode": "CÓDIGO DE CAMPAÑA", "campaignName": "NOMBRE DE CAMPAÑA", "vins": "VIN", "brand": "MARCA", "model": "MODELO", "year": "AÑO", "startDate": "FECHA DE INICIO", "endDate": "FECHA FIN"}, "importModal": {"openButton": "Importar campañas", "title": "Importar campañas", "cancelButton": {"cancel": "<PERSON><PERSON>, cancelar", "back": "Regresar"}, "errors": {"missingMandatory": {"title": "Archivo no cargado", "description": "Tiene columnas obligatorias sin información."}, "invalid": {"title": "Documento no cargado", "description": "La lista de campañas no se ha cargado; revise los datos e intente nuevamente."}}}}, "followUp": {"header": "Encuestas", "surveys": {"header": "Encuestas", "enable": "Activar envío de encuestas", "days": "Envíe la encuesta después de [] días", "email": "Email para recibir notificaciones", "googlePlaceId": "ID de lugar en Google Maps", "defaultDate": {"label": "<PERSON>cha predeterminada para enviar la encuesta", "closed": "Orden fue cerrada", "firstTime": "Orden fue enviada por primera vez", "uploaded": "Orden fue cargada"}}}, "mobileApp": {"header": "Aplicación Móvil", "frequentNotesLabel": "Notas frecuentes", "frequentNotesForCustomerLabel": "Notas frecuentes visibles para el cliente", "frequentNotesForInternalLabel": "Notas frecuentes internas", "frequentNotesForCustomerHint": "Notas que pueden ser vistas por los miembros del equipo y el cliente.", "frequentNotesForInternalHint": "Notas que solo pueden ser vistas por los miembros del equipo.", "frequentNotesNote1": "Agregue su nota y presione “Enter” para guardar.", "frequentNotesNote2": "Nota(s) de tipo “Captura en el Espacio Vacío”", "sharedOrdersRestrictionLabel": "Permiso de edición de Órdenes compartidas", "sharedOrdersRestrictionOption1": "Edición sin límite", "sharedOrdersRestrictionOption2": "Edición límitada", "sharedOrdersRestrictionDescription1": "Edición total de Orden compartida", "sharedOrdersRestrictionDescription2": "Solo agregar nuevos elementos", "showCustomerInfoLabel": "Mostrar información del cliente a [Title:Technicians]", "generateOrderNumberAutomaticallyLabel": "Generar número de Orden automáticamente", "generateOrderNumberAutomaticallyOption1": "Desactivado", "generateOrderNumberAutomaticallyOption2": "Activado", "generateOrderNumberAutomaticallyDescription1": "Escribir número de Orden manualmente", "generateOrderNumberAutomaticallyDescription2": "Número de Orden generado automáticamente", "startingOrderNumberLabel": "Número de Orden inicial", "hideAddInspectionItemFeatureLabel": "Ocultar opción “Agregar punto de inspección”", "addCharactersBeforeEachOrderNumber": "Agregar caracteres antes de cada número de Orden", "mandatoryFieldsReception": "Campos obligatorios para generar Orden (Información de la Orden)", "receptionSignature": "Firma de recepción", "deliverySignature": "Firma de entrega", "mandatoryFieldsCustomer": "Campos obligatorios para generar Orden (Información del cliente)", "name": "Nombre", "lastName": "Apellido", "email": "Email", "mobile": "Móvil", "signatureInPrivacyNotice": "Firma en aviso de privacidad", "mandatoryFieldsVehicle": "Campos obligatorios para generar Orden (Información del vehículo)", "plates": "Placas", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "mileage": "[Title:Mileage]", "VIN": "VIN", "tower": "Torre", "orderType": "<PERSON><PERSON><PERSON> Orden", "deliveryDay": "Día de entrega", "deliveryHour": "Hora de en<PERSON>ga", "tmSignatures": {"1": "Firma del miembro del equipo 1", "2": "Firma del miembro del equipo 2", "3": "Firma del miembro del equipo 3", "4": "Firma del miembro del equipo 4"}, "signatureDisabledHint": "Antes de activar, asegúrese de habilitar la firma del miembro del equipo en la sección \"Firmas del miembro del equipo\".", "phaseToSign": "Etapa para firmar", "mandatoryFieldsReceptionHint": "La firma de miembro del equipo será obligatoria dependiendo la etapa seleccionada.", "allowUploadingMediaFilesFromTheMobileGallery": "Permitir cargar archivos multimedia desde galería móvil", "allowUploadingMediaFilesFromTheMobileGalleryTooltip": "Los miembros del equipo podrán agregar imágenes y videos a los puntos de inspección desde su galería móvil."}, "inCharge": {"header": "<PERSON><PERSON><PERSON>", "automaticallyInCharge": "Asignar automáticamente [Title:ServiceAdvisor] como “<PERSON>cargado“ de Ó<PERSON>es"}, "assignedTo": {"header": "Asignado a", "automaticallyAssignedTo": "Asignar automáticamente las Órdenes al [Title:Technician]"}, "estimates": {"header": "[Title:Estimate]", "enableSpreadsheetViewByDefault": "Mostrar automáticamente la vista de “Hoja de Cálculo“", "enableEstimateReview": "No mostrar la [Lower:Estimate] hasta la revisión del miembro del equipo"}, "dmsIntegration": {"header": "Integración DMS", "showAllRosFromDms": "Mostrar todas las Órdenes de DMS"}, "teamMemberSignature": {"mobilAppSignaturesHeader": "Firmas de miembros del equipo de la Orden en la app móvil", "handwrittenSignaturesHeader": "Firmas manuscritas de miembros del equipo", "activateDigitalSignatureInMobileApplication1": "Activar firma digital en la aplicación móvil para miembro del equipo 1", "activateDigitalSignatureInMobileApplication2": "Activar firma digital en la aplicación móvil para miembro del equipo 2", "activateDigitalSignatureInMobileApplication3": "Activar firma digital en la aplicación móvil para miembro del equipo 3", "activateDigitalSignatureInMobileApplication4": "Activar firma digital en la aplicación móvil para miembro del equipo 4", "signatureHeaderText": "Encabezado de la firma", "signatureHeaderText1": "Encabezado de la firma digital para miembro del equipo 1", "signatureHeaderText2": "Encabezado de la firma digital para miembro del equipo 2", "signatureHeaderText3": "Encabezado de la firma digital para miembro del equipo 3", "signatureHeaderText4": "Encabezado de la firma digital para miembro del equipo 4", "signatureHeaderTextPlaceholder1": "Firma del miembro del equipo 1", "signatureHeaderTextPlaceholder2": "Firma del miembro del equipo 2", "signatureHeaderTextPlaceholder3": "Firma del miembro del equipo 3", "signatureHeaderTextPlaceholder4": "Firma del miembro del equipo 4", "signatureHeaderTextPlaceholder": "Firma del miembro del equipo", "customerFormPdfSignature": "Activar firma manuscrita del miembro del equipo en la versión PDF de la Orden, vista normal", "estimatesPdfSignature": "Activar firma manuscrita del miembro del equipo en la versión PDF de la Orden, vista hoja de cálculo"}, "serviceAdvisorSignature": {"header": "Firma del [Title:ServiceAdvisor]", "enableServiceAdvisorSignature": "Activar firma del [Title:ServiceAdvisor]"}, "technicianSignature": {"header": "<PERSON>rma del [Title:Technician]", "enableTechnicianSignature": "Activar firma del [Title:Technician]"}, "customerSignature": {"header": "Firma del cliente", "enableElectronicSignature": "Activar firma digital de recepción en la aplicación móvil", "electronicSignatureHeader": "Encabezado de la firma de recepción", "electronicSignatureHeaderPlaceholder": "Ingresa el encabezado de la firma de recepción", "electronicSignatureText": "Texto de la firma de recepción", "electronicSignatureTextPlaceholder": "Ingresa el texto de la firma de recepción aquí", "electronicSignatureCheckbox": "Texto de casilla de la firma de recepción", "electronicSignatureCheckboxPlaceholder": "Ingresa el texto de casilla de la firma de recepción aquí", "enableAdhesionContractSignature": "Activar firma de contrato de adhesión en la aplicación móvil", "adhesionContractSignatureHeader": "Encabezado de firma de contrato de adhesión", "adhesionContractSignatureHeaderPlaceholder": "Ingresa el encabezado de firma de contrato de adhesión", "adhesionContractSignatureText": "Texto de firma de contrato de adhesión", "adhesionContractSignatureTextPlaceholder": "Ingresar el texto de firma de contrato de adhesión aquí", "adhesionContractSignatureCheckbox": "Texto de casilla de la firma de contrato de adhesión", "adhesionContractSignatureCheckboxPlaceholder": "Ingresa el texto de casilla de la firma de contrato de adhesión aquí", "enableNoticePrivacySignature": "Activar firma de aviso de privacidad en la aplicación móvil", "noticePrivacySignatureHeader": "Encabezado de firma de aviso de privacidad", "noticePrivacySignatureHeaderPlaceholder": "Ingresa el encabezado de firma de aviso de privacidad aquí", "noticePrivacySignatureText": "Texto de firma de aviso de privacidad", "noticePrivacySignatureTextPlaceholder": "Ingresa el texto de firma de aviso de privacidad aquí", "privacyNoticeSignatureCheckbox": "Texto de casilla de la firma de aviso de privacidad", "privacyNoticeSignatureCheckboxPlaceholder": "Ingresa el texto de firma de aviso de privacidad aquí", "enableElectronicDeliverySignature": "Activar firma digital de entrega en la aplicación móvil", "electronicDeliverySignatureHeader": "Encabezado de firma de entrega", "electronicDeliverySignatureHeaderPlaceholder": "Ingresa el encabezado de firma de entrega", "electronicDeliverySignatureText": "Texto de la firma de entrega", "electronicDeliverySignatureTextPlaceholder": "Ingresa el texto de la firma de entrega aquí", "electronicDeliverySignatureCheckbox": "Texto de la casilla de verificación de la firma de entrega", "electronicDeliverySignatureCheckboxPlaceholder": "Ingresa el texto de la casilla de verificación de la firma de entrega aquí", "enableHandwrittenSignature": "Activar firma manuscrita del cliente en la versión PDF de la Orden, vista normal", "handwrittenSignatureHeader": "Encabezado de texto en PDF", "handwrittenSignatureHeaderPlaceholder": "Ingresa el encabezado de texto en PDF", "handwrittenSignatureText": "Texto de la firma en PDF", "handwrittenSignatureTextPlaceholder": "Ingresa el texto de la firma en PDF aquí", "enableHandwrittenSignatureForEstimateApproval": "Activar firma manuscrita para aprobación de la cotización", "handwrittenSignatureForEstimateApprovalHeader": "Encabezado de la firma", "handwrittenSignatureForEstimateApprovalHeaderPlaceholder": "Ingresa el encabezado de la firma"}, "legalRepresentativeSignature": {"header": "Firma del representante legal", "dimensionsNote": "La firma debe tener dimensiones de 50 x 55", "enableSignatureInPDF": "Habilitar la firma del representante legal en PDF", "signatureLabel": "Etiqueta de la firma del representante legal", "signaturePlaceholder": "Firma del representante legal"}, "activityReports": {"title": "Reportes de actividad", "enableDaily": "Habilitar reporte de actividad diaria", "enableWeekly": "Habilitar reporte de actividad semanal", "enableMonthly": "Habilitar reporte de actividad mensual", "successBody": "El reporte de actividad se ha deshabilitado correctamente."}, "others": {"title": "<PERSON><PERSON><PERSON>", "pdf": "Incluya la opción de \"Imprimir Orden en PDF\" en notificaciones de carga", "customerContact": "Habilitar la función \"Agregar contacto de cliente\" en la aplicación móvil", "successBody": "La opción de la sección “Otros” se ha actualizado correctamente."}, "updatedConfiguration": "Configuración actualizada", "defaultSms": {"title": "SMS predeterminado", "smsText": "Texto de SMS", "restore": "Restaurar", "save": "Guardar", "saveSuccessBody": "El SMS predeterminado, se ha actualizado correctamente.", "restoreSuccessBody": "La configuración se ha cambiado con éxito."}}, "notifications": {"header": "Notificaciones", "realTime": {"headerOld": "Alertas en tiempo real", "header": "Notificaciones en Tiempo Real en la App Móvil y en el Tablero Web", "headerTooltip": "Las notificaciones activadas en esta sección se mostrarán al dar clic en el icono “Campana” del extremo superior derecho del Tablero Web y App Móvil. En el caso de la App Móvil, las notificaciones también se mostrarán en la pantalla principal del dispositivo móvil, incluso si el dispositivo está en reposo.", "restore": "Restaurar", "alertBody": "Texto de notificación", "alertTitle": "Título de notificación", "maxChar": "{{count}} caracteres como máximo", "minute": "{{count}} minutos", "hour_one": "{{count}} hora", "hour_other": "{{count}} horas", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "customTime": "Hora personalizada", "recipients": "Seleccionar destina<PERSON>", "disabledSuccessfully": "La alerta ha sido deshabilitada correctamente", "enabledSuccessfully": "La alerta ha sido habilitada correctamente", "triggers": {"SelectPriorityColor": "Seleccionar color de prioridad", "SelectPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SelectMileage": "[Title:Mileage]", "SelectCount": "Seleccionar cantidad", "SelectTime": "Seleccionar tiempo", "SelectCondition": "Seleccionar condición", "SelectItem": "<PERSON><PERSON><PERSON><PERSON><PERSON> punto", "SurveyWithNegativeRating": {"question": "Pregunta", "selectQuestionNumber": "Seleccionar número de pregunta", "selectNegativeRating": "Seleccionar calificación negativa", "addQuestion": "<PERSON><PERSON><PERSON>"}, "NewRedItem": {"Red": "rojo", "Yellow": "<PERSON><PERSON><PERSON>", "RedYellow": "rojo/amarillo"}, "PhaseSetback": {"originPhase": "Etapa origen", "selectOriginPhase": "Seleccionar etapa origen", "destinationPhase": "Etapa destino", "selectDestinationPhase": "Seleccionar etapa destino", "addPhaseSetbackSettingTooltip": "Agregar otra configuración de retroceso de etapa.", "removePhaseSetbackSettingTooltip": "Quitar esta configuración de retroceso de etapa."}}, "restorePopupTitle": "Restaurar texto predeterminado", "restorePopupText": "¿Esta seguro que desea restablecer al texto predeterminado?\nEsto anulará cualquier personalización realizada.", "appointmentAssignedTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando le sea asignada una cita a un miembro del equipo.", "newOrderTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando un miembro del equipo cree una nueva Orden.", "phaseModifiedTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando un miembro del equipo cambie de etapa una Orden.", "orderAssignedTooltip": " La notificación se mostrará a los destinatarios seleccionados que les sea asignada una Orden.", "inspectionItemApprovedTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando un cliente o miembro del equipo apruebe un punto de inspección o trabajo cotizado.", "inspectionItemDeclinedTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando un cliente o miembro del equipo rechace un punto de inspección o trabajo cotizado.", "surveyWithNegativeRatingTooltip": " La notificación se mostrará a los destinatarios seleccionados cuando una calificación tenga una calificación baja según la configuración realizada.", "newRedItemTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando se cargue un nuevo punto de inspección o trabajo en rojo y/o amarillo, según la configuración.", "phaseSetbackTooltip": "La notificación se mostrará a los destinatarios seleccionados cuando un miembro del equipo cambie la Orden de la etapa actual a una etapa anterior.", "paymentReceivedTooltip": "La notificación de pago se enviará a los destinatarios seleccionados cuando el cliente realice un pago parcial o completo de la Orden.", "jobAssignedTooltip": "La notificación se mostrará a los destinatarios que les sea asignado un trabajo."}, "recipients": {"header": "Des<PERSON><PERSON><PERSON>", "emailSms": "Envíos e-mail y SMS", "emailSmsTooltip": "Estas direcciones de correo electrónico son notificadas cada vez que un correo electrónico o texto SMS sea enviado a través de ClearMechanic One Solution.", "upload": "Notificaciones de carga", "uploadTooltip": "Estas direcciones de correo electrónico se notifican cada vez que se carga una foto o un video a la Orden a través de las aplicaciones móviles de ClearMechanic.", "support": "Contacto de soporte", "supportTooltip": "Estas direcciones de correo electrónico son notificadas cada vez que ocurre un problema [Lower:Technician] con las aplicaciones móviles de ClearMechanic, como una foto incompleta o una carga de video.", "activity": "Reporte de actividades", "activityTooltip": "Estas direcciones de correo electrónico reciben de manera diaria, semanal y mensual los reportes de actividad de ClearMechanic One Solution, los cuales contienen información como cantidad de mensajes enviados, cuantas fotos y videos fueron cargados y métricas de seguimiento con los clientes.", "followup": "Cartas de seguimiento", "followupTooltip": "Estas direcciones de correo electrónico son copiadas en todas las cartas de seguimiento.", "approval": "Recepción de aprobaciones", "approvalTooltip": "Cuando sus clientes aprueben tus recomendaciones, las aprobaciones se enviarán a estas direcciones de correo electrónico.", "emailPlaceholder": "<EMAIL>"}, "fromEmail": {"header": "Co<PERSON><PERSON> de \"Envío\"", "enableFromEmail": "Activar correo de \"Envío\"", "emailAddressLabel": "Correo electrónico", "emailAddressPlaceholder": "<EMAIL>", "emailAddressHint": "Esta es la dirección de correo de \"Envío\" que verán los clientes.", "repairShopNameLabel": "Nombre del centro de servicio", "repairShopNamePlaceholder": "Centro de servicio", "repairShopNameHint": "Corre<PERSON> de \"Envío\" visto por el cliente.", "connectionTypeLabel": "Tipo de conexión", "connectionType": {"plain": "Plain", "ssl": "SSL", "tls": "TLS"}, "portLabel": "Puerto", "serverNameLabel": "Nombre del servidor SMTP", "serverNamePlaceholder": "smtp-correo.dominio.com", "userNameLabel": "Nombre de usuario SMTP", "userNamePlaceholder": "<EMAIL>", "passwordLabel": "Contraseña SMTP", "passwordPlaceholder": "Contraseña", "verifyButton": "Verificar", "verifySuccess": "¡Tu dirección personalizada de correo electrónico de \"Envío\" ha sido configurada con éxito!", "verifyFailedTitle": "Problema en la Verificación", "verifyFailedBody": "No hemos podido configurar tu dirección de correo de \"Envío\". Por favor confirma que tu nombre de usuario SMTP y contraseña son correctos. Si continuas teniendo problemas, puedes ponerte en contacto con nosotros a"}, "defaultEmail": {"header": "E-mail predeterminado", "preview": "Vista preliminar", "previewHeader": "Preliminar", "subject": "<PERSON><PERSON><PERSON>", "text": "Texto de e-mail", "restore": {"restore": "Restaurar", "title": "Restaurar texto predeterminado", "text": "¿Esta seguro que desea restablecer al texto predeterminado?\nEsto anulará cualquier personalización realizada."}}, "smsShortcuts": {"header": "Atajos SMS", "shortcut": "Atajo 1", "enterShortcutName": "Ingrese el nombre del atajo", "enterShortcut": "Ingresa el atajo aquí", "addShortcut": "<PERSON><PERSON><PERSON>", "savedSuccessfully": "Los atajos SMS se han guardado correctamente.", "deletedSuccessfully": "Los atajos SMS se han eliminado correctamente."}, "defaultEstimateNotes": {"header": "Notas predeterminadas para el cliente sobre la [Lower:Estimate]", "note": "<PERSON>a", "addNote": "<PERSON><PERSON><PERSON>", "savedSuccessfully": "Las notas predeterminadas para el cliente sobre la [Lower:Estimate] se han guardado correctamente.", "deletedSuccessfully": "Las notas predeterminadas para el cliente sobre la [Lower:Estimate] se han eliminado correctamente."}, "restoreDefaultText": {"restore": "Restaurar", "title": "Restaurar texto predeterminado", "text": "¿Esta seguro que desea restablecer al texto predeterminado?\nEsto anulará cualquier personalización realizada."}, "orderx": {"title": "WhatsApps enviados al cliente durante la [Upper:RO]", "tmpl": {"reception": "Estimado [Nombre y apellido del cliente],\nEste es un mensaje de [Nombre de la sucursal]. El estatus de su vehículo es “*Recepción en proceso*”, significa que estamos creando la documentación necesaria para su servicio.\nComo referencia, su número de Orden es [Número de Orden]. Seguiremos enviando notificaciones durante este proceso.", "inspection": "Estimado [Nombre y apellido del cliente],\nEste es un mensaje de [Nombre de la sucursal]. El estatus de su vehículo es “*Inspección en proceso*”, significa que estamos inspeccionando su vehículo.\n\nComo referencia, su número de Orden es [Número de Orden]. Seguiremos enviando notificaciones durante este proceso.", "estimateReady": "Estimado [Nombre y apellido del cliente],\nEste es un mensaje de [Nombre de la sucursal]. El estatus de su vehículo es, “*[Title:Estimate] lista para ser aprobada*”, significa que ya puede revisar la [Lower:Estimate] preparada por nuestro equipo.\n\nFavor de responder con una de las siguientes opciones (1, 2, 3 o 4):\n1. Recibir un enlace a su Orden.\n2. Recibir un PDF de su Orden.\n3. Recibir un enlace y un PDF de su Orden.\n4. Hablar con un [Title:ServiceAdvisor].", "estimateApproval": "Estimado [Nombre y apellido del cliente],\nEste es un mensaje de [Nombre de la sucursal]. Hemos recibido su respuesta a la [Lower:Estimate] preparada por nuestro equipo.\n\nComo referencia, su número de Orden es [Número de Orden]. Seguiremos enviando notificaciones durante este proceso."}, "hints": {"reception": "WhatsApp enviado automáticamente al cliente final cuando la [Upper:RO] ha sido creada.", "inspection": "WhatsApp enviado automáticamente al cliente cuando el primer punto de inspección o trabajo ha sido agregado a la [Upper:RO].", "estimateReady": "WhatsApp enviado automáticamente al cliente cuando el campo “Mostrar [Lower:Estimate] al cliente” ha sido seleccionado en la pantalla “Detalle de orden”.", "estimateApproval": "WhatsApp enviado automáticamente cuando el cliente aprueba o rechaza un punto de inspección o trabajo."}, "reception": "Recepción en proceso", "inspection": "Inspección en proceso", "estimateReady": "[Title:Estimate] lista para ser aprobada", "estimateApproval": "[Title:Estimate] ha sido aprobada/rechazada"}, "phase": {"title": "Etapa modificada", "phaseSelectPlaceholder": "Seleccione la etapa", "tooltip": "Se notificará por WhatsApp de forma automática al cliente cuando la Orden se encuentre en esta etapa.", "placeholder": "Ingrese mensaje aquí", "addPhase": "<PERSON><PERSON>dir o<PERSON> WhatsApp de etapa", "updateToastTitle": "Plantilla actualizada", "updateToastBody": "La plantilla de notificación de fase se guardó correctamente.", "createToastTitle": "Plantilla creada", "createToastBody": "Plantilla de notificación de fase creada correctamente.", "errorToastBody": "Los saltos de línea no son permitidos.", "restore": "Restaurar", "save": "Guardar", "template": {"phaseStatus": "Estimado [Nombre y apellido del cliente],\nEste es un mensaje de [Nombre de la sucursal]. El estatus de su vehículo es “[*Etapa*]”.", "orderNumber": "Como referencia, su número de Orden es [*Número de Orden*]. Seguiremos enviando notificaciones durante este proceso."}, "whatsappNotificationDelay": "Periodo de espera antes de enviar WhatsApp sobre una etapa modificada", "whatsappNotificationDelayTooltip": "Esta configuración establece un periodo de espera antes de enviar notificaciones de WhatsApp cuando una Orden cambia de etapa. Esto permite a los miembros del equipo corregir cualquier cambio de etapa accidental antes de que se envíe la notificación al cliente.", "seconds": "{{sec}} seg", "sec": "seg"}}, "aftersalesCrm": {"general": "General", "classificationOfActivities": {"title": "Clasificación de actividades", "addStatus": "Agregar nuevo estatus del cliente", "addStatusPlaceholder": "Ingrese nuevo estatus del cliente", "addActivity": "Añadir actividad", "activity": "Actividad", "description": "Descripción", "activityModal": {"newActivity": "Nueva actividad", "editActivity": "Editar actividad", "nameTitle": "Nombre de la actividad", "namePlaceholder": "Ingrese el nombre", "statusTitle": "Estatus del cliente", "statusPlaceholder": "Seleccione estatus del cliente", "descriptionTitle": "Descripción de la actividad", "descriptionPlaceholder": "Describa brevemente la clasificación de actividad", "newActivityButton": "Crear nueva actividad", "editActivityButton": "Guardar cambios"}, "deleteStatusModal": {"title": "¿Eliminar este estatus de cliente?", "text": "Todas las actividades vinculadas a este estatus también serán eliminadas."}, "deleteActivityModal": {"title": "¿Eliminar esta actividad?", "text": "Solo se eliminará esta actividad."}, "notifications": {"statusCreated": "Estatus de cliente creado", "activityCreated": "Actividad creada", "statusDeleted": "Estatus de cliente eliminado", "activityDeleted": "Actividad eliminada", "statusExists": "Estatus de cliente existente", "activityExists": "Actividad existente"}, "delete": "Eliminar", "cancel": "<PERSON><PERSON><PERSON>", "emptyPageTitle": "¡Cree un estatus del cliente!", "emptyPageBody": "Es necesario para poder crear clasificaciones de actividades."}, "prospectionPriorities": "Prioridades de prospección", "classificationOfAppointments": "Clasificación de citas", "importVehicles": {"title": "Importar <PERSON>", "successTitle": "<PERSON>umento cargado", "successBody": "La lista de vehículos se ha cargado correctamente", "partialImportCompleted": "Importación parcialmente completada", "partialImport": {"text": "{{successful}} de {{total}} registros fueron cargados.", "link": "Ver los registros que no fueron cargados"}, "incorrectFormatTitle": "Error en la importación", "incorrectFormatBody": "Formato de archivo inválido. Asegúrate de cargar un archivo con extensión .xlsx o .csv", "invalidVehicleListTitle": "Error en la importación", "invalidVehicleListBody": "Formato de lista de vehículos no válido. Por favor, asegúrese de que cada vehículo incluya un VIN.", "fileSizeLimitExceededTitle": "Error en la importación", "fileSizeLimitExceededBody": "El archivo cargado excede el límite de 5MB. Por favor, sube un archivo más pequeño.", "uploadFile": "Subir archivo", "notes": "Notas", "acceptedFormat": "Formato aceptado .xlsx y .csv", "maximumAllowedSize": "Tamaño máximo permitido del documento 5 MB", "downloadTemplate": "Descargar plantilla", "pendingImports": {"inProgress": "Importación en curso", "inProgressText": "Puedes seguir utilizando otras pantallas y funciones mientras se completa la importación.", "completed": "Importación completada con éxito", "completedText": "Todos los registros se importaron correctamente.", "failedImportInternalErrorText": "La importación falló completamente debido a un error interno. Por favor, inténtalo de nuevo más tarde o contacta al equipo de soporte si el problema persiste.", "failedImportInternalError": "Importación fallida (Error interno)", "partialFailInternalErrorText": {"line1": "{{successful}} de {{total}} registros fueron cargados.", "line2": "Ocurrieron algunos errores", "line3": "Vuelva a subir el archivo o contacte a soporte."}, "partialFailInternalError": "Importación parcialmente completada"}}}, "terminologies": {"header": "Términos personalizables", "placeholder": "Ingrese un término", "updateToastTitle": "<PERSON><PERSON><PERSON><PERSON>", "updateToastBody": "<PERSON><PERSON><PERSON><PERSON> guardado correctamente.", "invalidLengthTitle": "Término no editado", "invalidLengthBody": "El límite de caracteres fue excedido."}, "general": {"locationInfo": "Info de la sucursal", "teamMembers": "Miembros del equipo", "orderPdf": "PDF de la Orden", "orders": "<PERSON><PERSON><PERSON>", "inspectionForms": "Formas de inspección", "appointments": "Citas", "siteForAppointments": "Citas en línea", "appointmentReasons": "[Sentence:ReasonsForVisit]", "orderTypes": "Tipos de Orden", "phases": "Etapas", "terminologies": "Términos personalizables", "packages": "<PERSON><PERSON><PERSON>"}, "appointment": {"general": "Generales", "defaultAppointmentDuration": "Duración por defecto de la cita", "activateAutomaticAppointmentNumber": "Activar número de cita automático", "initialAppointmentNumber": "Número de cita inicial", "changeAppointmentStatusAfter": "Cambiar cita a \"Cliente no llegó\" después de:", "valetServiceTitle": "Cita con servicio de valet", "valetServiceDescription": "Se mostrará una configuración en la pantalla \"Nueva cita\" para indicar si la cita requiere el servicio de valet.", "appointmentOrigin": "Origen de la cita", "openingAndClosingSchedule": "Horario de apertura y cierre", "weekdays": "Días de la semana", "openingTime": "Hora de apertura", "closingTime": "<PERSON><PERSON> c<PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "sunday": "Domingo", "appointmentOriginNotCreated": "Origen de cita no creado", "appointmentOriginNotEdited": "Origen de cita no editado", "appointmentOriginIsDuplicated": "El origen de cita es duplicado.", "appointmentOriginCreated": "Origen de cita creado", "appointmentOriginSavedSuccessfully": "Origen de cita guardado correctamente.", "appointmentOriginEdited": "Origen de cita editado", "appointmentOriginDeleted": "Origen de cita eliminado", "appointmentOriginRemovedSuccessfully": "Origen de cita eliminado correctamente.", "createNewOrigin": "Crear nuevo origen", "defaultAppointmentDurationUpdated": "Duración por defecto de la cita actualizada", "defaultAppointmentDurationSavedSuccessfully": "Duración por defecto de la cita guardada correctamente.", "changeAppointmentStatusAfterUpdated": "Configuracione actualizada", "changeAppointmentStatusAfterSavedSuccessfully": "Configuracione guardado correctamente.", "initialAppointmentNumberNotSaved": "Número de cita inicial no guardado", "theInitialAppointmentNumberFieldMustBeMore": "El campo del número de cita inicial debe ser mayor que el último número de cita.", "theInitialAppointmentNumberFieldDoesNotAcceptAnEmptyValue": "El campo número de cita inicial, no acepta un valor vacío.", "theInitialAppointmentNumberFieldDoesNotAcceptDuplicatedValues": "El campo número de cita inicial, no acepta valores duplicados.", "enterAppointmentOrigin": "Ingresa un origen de cita", "siteForAppointments": "Citas en línea", "activateSiteForApptsTitle": "Activar citas en línea", "activateSiteForApptsDescription": "Sus clientes pueden agendar citas en línea para la recepción de sus vehículos.", "copySiteForApptsLink": "Copiar link de citas en línea", "linkCopiedSuccessfully": "<PERSON> copiado"}, "siteForAppointments": {"general": "Citas en línea", "preview": "Vista previa", "fields": {"vehicle_brand": "<PERSON><PERSON>", "vehicle_model": "<PERSON><PERSON>", "vehicle_year": "<PERSON><PERSON>", "vehicle_vin": "VIN", "vehicle_mileage": "[Title:Mileage]", "vehicle_plates": "Placas", "vehicle_color": "Color", "customer_name": "Nombre(s)", "customer_last_name": "<PERSON><PERSON><PERSON><PERSON>(s)", "customer_lastname": "<PERSON><PERSON><PERSON><PERSON>(s)", "customer_mobile": "Celular/WhatsApp", "customer_email": "Correo Electrónico", "customer_tax_id": "[Sentence:TaxIdentification]", "customer_business_name": "Nombre comercial/Razón social", "customer_notes": "Notas"}, "notifications": {"settingsUpdated": "Configuración actualizada", "somethingWentWrong": "Algo salió mal", "settingsUpdatedSuccessfully": "Configuración actualizada correctamente", "invalidValue": "Valor no permitido"}}, "orderTypes": {"createNewOrderType": "Crear nuevo tipo de Orden", "newOrderType": "Nuevo tipo de Orden", "orderTypeNotCreated": "<PERSON><PERSON><PERSON> de Orden no creado", "orderTypeNotEdited": "Tipo de Orden no editada", "theOrderTypeIsDuplicated": "El tipo de Orden está duplicado.", "orderTypeCreated": "<PERSON><PERSON><PERSON> de Orden creado", "orderTypeEdited": "Tipo de Orden editada", "orderTypeSavedSuccessfully": "Tipo de Orden guardado correctamente.", "enterOrderType": "Ingresa tipo de Orden", "orderType": "TIPO DE ORDEN", "orderTypeCaption": "<PERSON><PERSON><PERSON> Orden", "externalOrderTypeCaption": "Id externo del tipo de Orden", "externalOrderTypeCaptionPlaceholder": "Ingrese el Id asignado en el sistema externo", "lastUpdate": "ÚLTIMA ACTUALIZACIÓN", "editOrderType": "Editar tipo de Orden", "saveData": "Guardar datos", "saveOrderType": "Guardar tipo de Orden", "deleteOrderType?": "¿Eliminar tipo de Orden?", "byDeletingThisOrderTypeItWillBeRemovedFromTheListOfOrderTypes": "Al eliminar este tipo de Orden, <1>se eliminará de la lista de tipos de Orden.</1>", "deleteOrderType": "Eliminar tipo de Orden", "orderTypeDeleted": "Tipo de Orden eliminado", "orderTypeDeletedSuccessfully": "Tipo de Orden eliminado correctamente.", "color": "COLOR", "orderTypeColor": "Color de tipo de Orden", "orderTypeColorPlaceholder": "Seleccione color de tipo de Orden", "updatedDateFormat": "DD/MM/YY", "colorInUse": "Color en uso"}, "orders": {"autoCloseOrders": {"header": "<PERSON><PERSON>r <PERSON> automáticamente", "hint": "Permitir cerrar las Órdenes sin actividad durante el tiempo configurado y en las etapas seleccionadas.", "inactivityTime": "Seleccione tiempo sin actividad", "selectPhase": "Seleccione la etapa", "selectPhasePlaceholder": "Etapas", "cancelPopupTitle": "¿Cancelar la configuración de cierre automático de Órdenes?", "disablePopupTitle": "¿Está seguro de que desea desactivar el cierre automático de Órdenes?"}, "design": {"header": "Diseño de órdenes", "formType": {"header": "Vista predeterminada", "cost": "Por costo", "priority": "Por prioridad", "system": "Por sistema"}, "accountName": "Nombre de cuenta mostrada", "includeEstimate": "Incluir cotización desglosada en la Orden enviada al cliente", "accountNamePlaceholder": "Ingrese nombre de cuenta mostrada", "accountNameSaved": "Nombre de cuenta mostrada guardado correctamente", "showNA": "Mostrar puntos “N/A“", "showDetails": "Mostrar “Detalles“ en los puntos de inspección vistos por el cliente", "approveOnlyEstimated": "Los puntos de inspección solo pueden ser aprobados si tienen [Lower:Estimate]", "showCutOrderNum": "Mostrar solo los últimos 8 dígitos para Orden#", "taxPercentage": "Calcular impuestos al agregar [Lower:Estimate]", "taxesSaved": "Porcentaje guardado correctamente", "addHourlyCost": "Agregar costo de [Lower:Labor] estándar por hora", "costSaved": "Costo guardado correctamente", "showServiceAdvisorInfo": "Mostrar información de [Title:ServiceAdvisor]", "hideMakeLogos": "No mostrar logotipos de marcas", "discount": {"header": "Tipo de descuento a aplicar", "currency": "Moneda", "percentage": "Po<PERSON>entaj<PERSON>"}}, "photos": {"header": "Fotos de Órdenes", "showOriginal": "Mostrar foto original sin flechas", "activateFacebook": "Activar opción compartir en “Facebook“"}, "image": {"header": "Personalizar la imagen en Órdenes", "dimensionsNote": "La imagen debe tener las siguientes dimensiones: 220x126", "orderLogoRemoved": "Logotipo de la Orden eliminada correctamente", "orderLogoUpdated": "Logotipo de la Orden guardada correctamente", "invalidLogo": "El logotipo para la Orden no es válido", "showBrandLogo": "Mostrar logo de la marca del vehículo"}, "orderPriorityText": {"header": "Texto instructivo para Orden digital vista por Costo", "prioritySectionHeader": "Texto por prioridad:", "redPriorityLabel": "Prioridad “Urgente” (Rojo)", "redPriorityDefaultText": "Recomendaciones a realizar urgentemente.", "yellowPriorityLabel": "Prioridad “Sugerido” (Amarillo)", "yellowPriorityDefaultText": "Recomendaciones para prevenir futuros problemas.", "greenPriorityLabel": "Prioridad “OK” (Verde)", "greenPriorityDefaultText": "En buen estado."}, "timePickerInterval": {"header": "Selectores de hora", "label": "Intervalo de tiempo entre opciones de hora", "tooltip": "Defina el intervalo de tiempo para mostrar las opciones de horario disponibles. Ej.: cada 5, 10, 15 minutos"}}, "prospections": {"tabs": {"general": "General", "maintenanceProspection": "Prospección de mantenimiento", "prospectionException": "Excepciones de prospección", "importCustomers": "Importar clientes", "callResults": "Resultados de llamadas", "prospectionSettings": "Configuraciones de Prospecciones"}, "general": {"sendingProspectionMessages": "Envío de mensajes de prospección", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "sunday": "Domingo", "startTime": "Horario de inicio", "endTime": "Horario de fin", "weSuggestThatMessageSendingSchedule": "Te sugerimos que el horario de envío de mensajes esté dentro de un horario habitual.", "exampleMondayToFridayFromTo": "<strong><PERSON><PERSON><PERSON><PERSON>:</strong> de Lunes a Viernes de 9:00 am a 6:00 pm.", "prospectionOfUnapprovedInspectionItems": "Prospección de puntos de inspección no aprobados", "enableFollowUps": "Activar la prospección de puntos de inspección no aprobados", "daysToSendWhatsAppFollowUp": "Días para envío de WhatsApp de seguimiento", "days": "días", "day": "día", "deselectedDaysOfTheWeek": "Días de la semana deseleccionados", "notAllDaysOfTheWeekCanBeDeselectedPleaseSelectAtLeastOneDay": "No pueden deseleccionarse todos los días de la semana, por favor seleccione al menos un día.", "theEndTimeIsEqualToTheStartTime": "La hora de fin es igual a la hora de inicio", "theEndTimeCannotBeTheSameAsTheStartTimePleaseSelectAnotherEndTime": "La hora de fin no puede ser igual o menor que la hora de inicio, por favor seleccione otra hora de fin.", "updatedConfiguration": "Configuración actualizada", "theConfigurationWasSuccessfullyUpdated": "La configuración fue actualizada correctamente."}, "maintenance": {"search": "Búsqueda", "mileageUnit": "KM", "allBrands": "Todas las marcas", "createNewMaintenanceProspectionRule": "Crear nueva regla de prospección de mantenimiento", "mileage": "[Upper:Mileage]", "monthsForNextService": "MESES PARA PRÓXIMO SERVICIO", "brand": "MARCA", "model": "MODELO", "year": "AÑO", "month": "mes", "months": "meses", "form": {"createMaintenanceProspectionRule": "Crear regla de prospección de mantenimiento", "editMaintenanceProspectionRule": "Editar regla de prospección de mantenimiento", "cancel": "<PERSON><PERSON><PERSON>", "createNewRule": "Crear nueva regla", "saveData": "Guardar datos", "mileageForTheNextService": "[Title:Mileage] para el próximo servicio", "enterTheMileageForProspection": "Ingrese el [Lower:Mileage] para la prospección", "monthsForTheNextService": "Meses para el próximo servicio", "enterTheMonthsForProspection": "Ingresa los meses para la prospección", "brand": "<PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "all": "Todos", "selectABrand": "Seleccione una marca(s)", "model": "<PERSON><PERSON>", "selectAModel": "Selecciona un modelo(s)", "year": "<PERSON><PERSON>", "selectAYear": "Selecciona un año(s)", "maintenanceProspectionRuleCreated": "Regla de prospección de mantenimiento creada", "maintenanceProspectionRuleCreatedSuccessfully": "Regla de prospección de mantenimiento creada correctamente.", "maintenanceProspectionRuleNotCreated": "Regla de prospección de mantenimiento no creada", "theMaintenanceProspectionRuleIsDuplicated": "La regla de prospección de mantenimiento está duplicada.", "maintenanceProspectionRuleEdited": "Regla de prospección de mantenimiento editada", "maintenanceProspectionRuleSavedSuccessfully": "Regla de prospección de mantenimiento guardada correctamente.", "maintenanceProspectionRuleNotEdited": "Regla de prospección de mantenimiento no editada", "allBrands": "Todas las marcas", "allModels": "Todos los modelos", "allYears": "Todos los años", "selectSpecificBrands": "Selecciona marcas específicas", "selectSpecificModels": "Selecciona modelos específicos", "selectSpecificYears": "Selecciona años específicos"}, "deleteMaintenanceProspectionRule": "¿Eliminar regla de prospección de mantenimiento?", "byDeletingThisMaintenanceRuleItWillRemovedFromTheListOfMaintenanceProspectionRule": "Al eliminar esta regla de prospección de mantenimiento, <strong>se eliminará de la lista de reglas de prospección de mantenimiento.</strong>", "deleteProspectionRule": "Eliminar regla de prospección", "maintenanceProspectionRuleDeleted": "Regla de prospección de mantenimiento eliminada", "maintenanceProspectionRuleDeletedSuccessfully": "Regla de prospección de mantenimiento eliminada correctamente."}, "importCustomers": {"uploadFile": "Subir archivo", "downloadTemplate": "Descargar plantilla", "notes": "Notas", "acceptedFormat": "Formato aceptado .xslx", "maximumAllowedSize": "Tamaño máximo permitido del documento 2 MB", "incorrectFormatTitle": "Documento no cargado", "incorrectFormatBody": "El formato de la lista de clientes no es correcto, por favor de usar el formato indicado.", "invalidRowsTitle": "Existen registros que no fueron cargados", "invalidRowsBody": "El registro de la fila {{rowNumber}} no fue cargado, por favor revise que los datos sean correctos.", "manyInvalidRowsTitle": "Existen registros que no fueron cargados", "manyInvalidRowsBody": "Aproximadamente {{rowsCount}} registros no se cargaron; por favor, verifique que los datos sean correctos.", "successTitle": "<PERSON>umento cargado", "successBody": "La lista de clientes ha sido cargada correctamente.", "templateFileName": "Lista de clientes.xlsx"}}, "workshopPlannerSettings": {"title": "Configuraciones Planeador <PERSON>", "tabs": {"general": "Generales", "plannings": "Planeaciones"}, "general": {"UTsEqualTo": "1 hora de tiempo es igual a:", "UTsSaved": "UT´s guardadas correctamente", "maximumNumberOfDigitsForTowerNumber": "Cantidad máxima de dígitos a mostrar de número de torre", "maximumNumberOfDigitsForTowerNumberSaved": "Cantidad máxima de dígitos para número de torre guardadas correctamente", "maximumNumberOfDigitsForTowerNumberHint": "La cantidad seleccionada determinará los dígitos del número de Orden o Torre que se mostrarán dentro del óvalo de la torre.", "maximumNumberOfDigitsForOrderAndAppointment": "Cantidad máxima de dígitos a mostrar de número de Orden/Cita", "maximumNumberOfDigitsForOrderAndAppointmentSaved": "Cantidad máxima de dígitos del número de Orden y Cita guardadas correctamente", "maximumNumberOfDigitsForOrderAndAppointmentHint": "La cantidad seleccionada determinará los dígitos finales del número de Orden y Cita que se mostrarán en el bloque.", "airportScreenView": "Vista de la pantalla tipo aeropuerto", "airportScreenViewOptions": {"verticalOption": "Vertical", "horizontalOption": "Horizontal", "verticalOptionHint": "Podrá visualizar a los miembros del equipo en la parte superior de la pantalla, y el horario de trabajo en la parte lateral izquierda.", "horizontalOptionHint": "Podrá visualizar a los miembros del equipo en la parte lateral izquierda de la pantalla, y el horario de trabajo en la parte superior."}, "showTeamMemberSpecialty": "Mostrar [Lower:Specialty] del miembro del equipo", "defaultScheduledDuration": "Duración por defecto del campo \"Duración programada\"", "defaultScheduledDurationHint": "Es la duración predeterminada que aparecerá en el campo \"Duración programada\" al agregar un trabajo a la Orden y la cita.", "showTechnicianCapacity": "Mostrar la métrica \"Capacidad Técnica Real\"", "maximumTechnicianCapacity": "Capacidad Técnica Real máxima", "allowSchedulingWithMaximum": "Permitir planear trabajos al alcanzar la Capacidad Técnica Real máxima", "showTechnicianCapacityTooltip": {"mainText": "Gestione la Capacidad Técnica Real en el Planeador de Taller para garantizar disponibilidad para trabajos urgentes, sin cita previa, y optimizar la carga de trabajo.", "technicalCapacity": "Capacidad Técnica Real", "totalTechnicians": "Total de Técnicos", "availableHours": "Horas disponibles", "workDays": "Días laborables", "technicalProductivity": "Productividad Técnica", "productiveHours": "Horas productivas", "workedHours": "<PERSON><PERSON> trabajadas"}, "showWorkshopUtilizationMetric": "Mostrar la métrica \"Utilización de Taller\"", "showWorkshopUtilizationMetricTooltip": "Métrica que muestra el cálculo de la utilización que tiene el taller y su capacidad potencial.", "productiveWorkspace": "Lugares productivos", "completedJobsTypeSetting": "Calcular métrica con trabajos finalizados según:", "completedJobsTypeSettingHint": "Define cómo se consideran los trabajos finalizados para el cálculo de las métricas \"Productividad Técnica\" y \"Capacidad Técnica Real\" de Planeador de Taller.", "completedJobsTypeSettingOptions": {"scheduledTime": "Tiempo programado", "scheduledTimeHint": "Se asume que un trabajo está completado cuando ha pasado su hora de finalización programada y no ha sido eliminado.", "actualTimeSpent": "Tiempo real invertido", "actualTimeSpentHint": "El trabajo solo se considera finalizado si el técnico usó los botones \"Iniciar\" y \"Finalizar\" de app web o app móvil."}, "showTowerColor": {"title": "Mostrar color de torre", "byServiceAdvisor": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "byOrderType": "Por tipo de Orden", "byReasonForAppointment": "Por [Lower:ReasonForVisit]", "infoTooltipText": "Seleccione cómo visualizar el color de la torre según los colores asignados al Asesor de Servicio, tipo de Orden y  [Lower:ReasonForVisit].", "reasonForAppointmentInactive": "Active los [Lower:ReasonsForVisit] personalizables en “Configuraciones / General / [Sentence:ReasonsForVisit]” para configurar el color de torre y  bloques de trabajo según el [Lower:ReasonForVisit]."}, "showJobBlockColor": {"title": "Mostrar color del bloque de trabajo", "byServiceAdvisor": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "byOrderType": "Por tipo de Orden", "byReasonForAppointment": "Por [Lower:ReasonForVisit]", "noColor": "Sin color", "infoTooltipText": "Seleccione cómo visualizar el color de los bloques de trabajo de Orden y de cita en la planeación, según los colores asignados al Asesor de Servicio, tipo de Orden y  [Lower:ReasonForVisit].", "reasonForAppointmentInactive": "Active los [Lower:ReasonsForVisit] personalizables en “Configuraciones / General / [Sentence:ReasonsForVisit]” para configurar el color de torre y  bloques de trabajo según el [Lower:ReasonForVisit]."}, "notifications": {"settingsUpdatedSuccessfully": "Configuración actualizada correctamente"}, "incompleteColorConfigurationModal": {"title": "Configuración de color incompleta", "body": "No se puede aplicar la opción \"{{configuration}} {{selectedOption}}\". Complete los colores antes de intentar de nuevo.", "goToSettings": "Ir a configuración", "cancel": "<PERSON><PERSON><PERSON>", "ShowTowerColorType": "Mostrar color de torre", "ShowJobBlockColorType": "Mostrar color del bloque de trabajo", "ByOrderType": "por tipo de Orden", "ByReasonForAppointment": "por [Lower:ReasonForVisit]"}, "standardOperations": {"title": "<PERSON><PERSON><PERSON> a<PERSON> \"Código de Operación\" y \"Tiempo estándar\" en el trabajo", "tooltip": "Se agregará los campos de “Código de operación” y “Tiempo estándar” en los pop-ups “Agregar trabajo” y “Editar trabajo” de la app web, exclusivamente para los tipos de Orden seleccionados. Ambos campos serán obligatorios para crear o editar un trabajo de dichos tipos de Orden."}, "technicianSignatureForJob": {"title": "Agregar firma de técnico en el trabajo", "tooltip": "Se agregará la firma del Técnico en la pantalla “Detalle de trabajo” en la app móvil para los tipos de Orden seleccionados. La firma del Técnico será obligatoria al detener el cronómetro de trabajo."}, "urgentFlashingIndication": {"title": "Orden parpadea en amarillo por puntos marcados \"Urgente\"", "inspectionFormToConsider": "Forma de inspección a considerar:", "infoTooltipText": "Las Órdenes parpadearán en color amarillo, cuando sean marcadas con prioridad “Urgente” (Rojo) los puntos de la forma de inspección seleccionada.", "selectInspectionForm": "Seleccione forma de inspección", "explanatoryTextLabel": "Texto explicativo a mostrar en Planeador de Taller", "explanatoryTextTooltip": "Texto que explica el significado del parpadeo amarillo en los bloques de Orden de Planeador de Taller."}}, "pausedOrders": {"title": "<PERSON><PERSON><PERSON> paus<PERSON>s", "highlightOrdersBy": "Resaltar <PERSON> por:", "deliveryPromiseDate": "Fecha promesa de entrega", "deliveryPromiseDateTooltip": "Las Órdenes pausadas parpadearán en color rojo cuando la fecha promesa haya vencido.", "daysPaused": "Días de pausa", "daysPausedTooltip": "Las Órdenes pausadas parpadearán en amarillo como advertencia preventiva y en naranja como alerta urgente cuando se alcance o exceda el número de días de pausa configurados. La alerta urgente debe ser mayor a la advertencia preventiva.", "daysUntilPauseWarning": "Días hasta advertencia de pausa:", "preventive": "Preventiva", "urgent": "Urgente", "days": "días", "day": "día", "urgentShouldBeHigherWarningTitle": "La alerta urgente debe ser", "urgentShouldBeHigherWarningText": "Mayor a la advertencia preventiva"}, "planning": {"Advisors": "<PERSON><PERSON><PERSON>", "Technicians": "[Title:Technicians]", "addPlanning": "<PERSON><PERSON>dir planeac<PERSON>", "createModal": {"title": "Nueva planeación", "nameHeader": "Nombre de la planeación", "namePlaceholder": "Ingrese el nombre", "selectTeamMembersHeader": "Seleccionar miembros del equipo", "selectTeamMembersPlaceholder": "Seleccione miembros del equipo", "allTeamMembers": "Todos los miembros del equipo", "cancelButton": "<PERSON><PERSON><PERSON>", "createNewButton": "Crear nueva planeación"}, "editModal": {"title": "Editar planeación", "nameHeader": "Nombre de la planeación", "namePlaceholder": "Ingrese el nombre", "selectTeamMembersHeader": "Seleccionar miembros del equipo", "selectTeamMembersPlaceholder": "Seleccione miembros del equipo", "allTeamMembers": "Todos los miembros del equipo", "cancelButton": "<PERSON><PERSON><PERSON>", "saveChanges": "Guardar cambios"}, "notifications": {"planningUpdatedSuccessfullyTitle": "Planeación actualizada", "planningUpdatedSuccessfullyBody": "Planeación guardada correctamente.", "planningDeletedSuccessfullyTitle": "Planeación actualizada", "planningDeletedSuccessfullyBody": "Planeación eliminada correctamente.", "planningAlreadyExistsTitle": "Planeación duplicada", "planningAlreadyExistsBody": "La planeación ya existe.", "planningCreatedTitle": "Planeación creada", "planningCreatedBody": "Planeación guardada correctamente.", "planningEditedTitle": "Planeación editada", "planningEditedBody": "Planeación guardada correctamente."}, "popup": {"deletePlanningTitle": "Eliminar planeación", "deletePlanningBody": "¿Quieres eliminar esta planeación?", "doNotDeletePlanning": "No eliminar", "deletePlanning": "Eliminar planeación"}}}, "customizableFields": {"header": "Campos personalizables", "addField": "Nuevo campo", "addColumn": "Nueva columna", "sections": {"WebLocationGeneralInfo": "Tablero web / Info de la sucursal", "WebLocationGeneralInfo-header": "Información general", "WebLocationServiceHoursInfo": "Tablero web / [Sentence:ServiceHours]", "WebLocationServiceHoursInfo-header": "[Sentence:ServiceHours]", "WebCustomerInfo": "Tablero web / Información del cliente", "WebCustomerInfo-header": "Información del cliente", "WebVehicleInfo": "Tablero web / Información del vehículo", "WebVehicleInfo-header": "Información del vehículo", "WebOrderInfo": "Tablero web / Información de la Orden", "WebOrderInfo-header": "Información de la Orden", "WebJobInfo": "<PERSON><PERSON><PERSON>er / Pop-up \"Más información del trabajo\"", "WebJobInfo-header": "Más información del trabajo", "WebParts": "Tablero web / [Title:Parts]", "WebParts-header": "[Title:Parts]", "WebLabor": "Tablero web / [Title:Labor]", "WebLabor-header": "[Title:Labor]", "MobileOrder": "App móvil / Orden", "MobileOrder-header": "Orden", "MobileCustomer": "App móvil / Cliente", "MobileCustomer-header": "Cliente", "MobileVehicle": "App móvil / Vehículo", "MobileVehicle-header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MobileJobInfo": "App móvil / Pantalla \"Más info del trabajo\" en \"Detalle de trabajo\"", "MobileJobInfo-header": "Más información del trabajo"}, "predefined": {"location_address": "Dirección", "location_phoneNumber": "Teléfono", "location_name": "Nombre de la sucursal", "location_emailAddress": "Correo electrónico", "location_website": "Página web", "location_taxIdentification": "[Sentence:TaxIdentification]", "location_legalName": "Razón social", "location_mondayToFridayHours": "Lunes a viernes", "location_saturdayHours": "Sábado", "customer_name": "Nombre", "customer_email": "<PERSON><PERSON><PERSON>", "customer_landline": "Teléfono", "customer_mobile": "<PERSON><PERSON><PERSON>", "customer_id_document": "Documento de identificación", "customer_business_name": "Nombre comercial", "customer_payment_method": "Método de pago", "vehicle_brand": "<PERSON><PERSON>", "vehicle_model": "<PERSON><PERSON>", "vehicle_year": "<PERSON><PERSON>", "vehicle_vin": "VIN", "vehicle_mileage": "[Title:Mileage]", "vehicle_plates": "Placas", "order_phase": "Etapa", "order_tower": "Torre", "order_type": "<PERSON><PERSON><PERSON> Orden", "order_in_charge": "<PERSON><PERSON><PERSON>", "order_assigned_to": "Asignado a", "order_note": "<PERSON>a", "order_technician": "Técnico", "order_additionalPositionOne": "Puesto adicional 1", "order_additionalPositionTwo": "Puesto adicional 2", "order_additionalPositionThree": "Puesto adicional 3", "order_additionalPositionFour": "Puesto adicional 4", "order_delivery_date": "[Sentence:PromisedDeliveryDateAndTime]", "job_team_member": "Miembro del equipo", "job_employee_id": "ID de empleado", "job_planning": "Planeación", "job_start_date": "Fecha de inicio", "job_start_time": "Hora de inicio", "job_scheduled_duration": "Duración programada", "job_time_units_sold": "Unidades de tiempo vendidas", "job_job_description": "Descripción de trabajo", "parts_number": "[Upper:PartNumber]", "parts_qty": "CANT.", "parts_avail": "DISP.", "parts_cost_unit": "COSTO U.", "parts_price_unit": "PRECIO U.", "labor_hrs": "HRS.", "labor_price_hr": "PRECIO HR.", "mob_order": "Orden", "mob_order_tower": "Torre", "mob_order_type": "<PERSON><PERSON><PERSON> Orden", "mob_order_delivery_day": "Día de entrega", "mob_order_delivery_hour": "Hora de en<PERSON>ga", "mob_order_record_notes": "<PERSON><PERSON><PERSON> notas", "mob_order_enter_notes": "Ingresar notas", "mob_customer_first_name": "Nombre", "mob_customer_last_name": "Apellido", "mob_customer_mobile": "<PERSON><PERSON><PERSON>", "mob_customer_email": "Correo electrónico", "mob_customer_landline": "Teléfono", "mob_vehicle_brand": "<PERSON><PERSON>", "mob_vehicle_model": "<PERSON><PERSON>", "mob_vehicle_year": "<PERSON><PERSON>", "mob_vehicle_mileage": "[Title:Mileage]", "mob_vehicle_scan_plates": "Escanear placas", "mob_vehicle_enter_plates": "Ingresar placas", "mob_vehicle_scan_vin": "Escanear VIN", "mob_vehicle_enter_vin": "Ingresar VIN", "mob_job_team_member": "Miembro del equipo", "mob_job_planning": "Planeación", "mob_job_job_description": "Descripción de trabajo"}, "createNewModal": {"header": "Nuevo campo", "headerColumn": "Nueva columna", "addField": "Agregar campo", "addColumn": "Agregar columna", "name": "Nombre del campo", "namePlaceholder": "Ingrese el nombre del campo", "nameColumn": "Nombre de la columna", "namePlaceholderColumn": "Ingrese el nombre de la columna", "type": "Tipo de campo", "typePlaceholder": "Seleccione tipo de campo", "typeColumn": "Tipo de columna", "typeColumnPlaceholder": "Seleccione tipo de columna", "additionalSection": "Agregar campo personalizable a otra sección", "additionalSectionSubtitle": "El campo será agregado a las secciones “{{main}}” y “{{additional}}”.", "hasExtraText": "Agregar <PERSON>rea de texto libre al final", "hasExtraTextSubtitle": "Este campo tendrá un campo de texto libre extra.", "requiredMobile": "Obligatorio en la app móvil", "requiredMobileSubtitle": "Este campo debe completarse de forma obligatoria en la aplicación móvil.", "mandatoryField": "Campo obligatorio", "mandatoryFieldSubtitle": "Este campo debe completarse de forma obligatoria en la aplicación móvil al finalizar el trabajo.", "jobColor": "Color de trabajo", "jobColorSubTitle": "Asignar color de trabajo de acuerdo a este campo.", "nameConflict": {"title": "Nombre del campo duplicado", "text": "El nombre del campo ya existe en un campo distinto."}, "successNotif": {"title": "Campo creado", "text": "Campo guardado correctamente."}, "successNotifColumn": {"title": "<PERSON><PERSON><PERSON> creada", "text": "Columna guardada correctamente."}}, "editModal": {"header": "<PERSON><PERSON> campo", "headerColumn": "Editar columna", "save": "Guardar cambios", "successNotif": {"title": "Campo editado", "text": "Campo guardado correctamente."}, "successNotifColumn": {"title": "Columna editada", "text": "Columna guardada correctamente."}}, "deleteModal": {"fieldTitle": "Eliminar campo", "columnTitle": "Eliminar columna", "fieldBody": "¿Quieres eliminar el campo <name>“{{name}}”</name>?", "columnBody": "¿Quieres eliminar la columna <name>“{{name}}”</name>?", "columnNotif": {"title": "Columna eliminada", "text": "Columna eliminada correctamente."}, "fieldNotif": {"title": "Campo eliminado", "text": "Campo eliminado correctamente."}}, "multipleChoiceSection": {"selectionType": "Tipo de selección", "selectionTypeInfo": "Seleccione el tipo de selección para la lista desplegable", "multipleChoiceSingleOption": "Permitir la selección de una sola opción", "multipleChoiceMultipleOption": "Permitir la selección de múltiples opciones", "enterTheOptions": "Ingrese las opciones", "jobColor": "Color de trabajo*", "defaultOption": "Opción", "addExternalId": "Agregar ID externo", "addExternalIdTooltip": "Ingrese un identificador único para esta opción. El ID puede tener hasta 36 caracteres y es utilizado para referencia.", "addAnotherOption": "Añadir otra opción", "jobColorPlaceholder": "Seleccione el color del tipo de trabajo"}, "orderTypesSelector": {"allOrderTypes": "Todos los tipos de Orden", "label": "Tipo(s) de Orden(es) donde aparecerá el campo"}}, "customAppointmentReasons": {"importAppointmentReasonDetail": {"importAppointmentReasonDetails": "Importar detalle de [Lower:ReasonForVisit]", "goBack": "Regresar", "uploadFile": "Subir archivo", "downloadTemplate": "Descargar plantilla", "notes": "Notas", "acceptedFormat": "Formato aceptado .xslx", "maximumAllowedSize": "Tamaño máximo permitido del documento 10 MB", "documentUploadedTitle": "<PERSON>umento cargado", "documentUploadedText": "La lista de detalle de [Lower:ReasonForVisit] ha sido cargada correctamente.", "documentNotUploadedTitle": "Documento no cargado", "documentNotUploadedText": "La lista de detalles del [Lower:ReasonForVisit] no se ha cargado; revise los datos e intente nuevamente.", "cancelXlsxFileUpload": "¿Cancelar la carga de archivo .xlsx?", "yesCancel": "Si, cancelar", "dragOrClickHereToUploadYourExcelFile": "Arrastra o da click aquí para subir tu archivo .xlsx"}}, "packages": {"activate": {"title": "Activar paquetes", "description": "Permitirá la creación y el uso de paquetes personalizables."}, "search": "Buscar paquete", "import": "Importar paquete", "create": "<PERSON><PERSON><PERSON> paquete", "instruction": {"mainText": "¡Sin paquetes registrados!", "secondaryText": "Por favor, comience a crear paquetes."}, "table": {"name": "Nombre de paquete", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "all": "Todos"}, "editOrCreate": {"createPackage": "<PERSON><PERSON><PERSON> paquete", "editPackage": "<PERSON><PERSON>", "packageName": "Nombre del paquete", "packageNamePlaceholder": "Ingrese nombre del paquete", "inspectionItemLabel": "Vincular punto o trabajo", "inspectionItemPlaceholder": "Escriba o seleccione punto o trabajo", "partsNumber": "[Title:Parts] - # Parte", "partsNumberPlaceholder": "Ingrese # de parte", "quantity": "[Title:Parts] - Cant.", "quantityPlaceholder": "Ingrese cantidad", "price": "[Title:Parts] - Precio <PERSON>.", "pricePlaceholder": "Ingrese precio U.", "hours": "[Title:Labor] - <PERSON><PERSON>", "hoursPlaceholder": "Ingrese horas", "hourPrice": "[Title:Labor] - <PERSON><PERSON>.", "hourPricePlaceholder": "Ingrese precio hr.", "description": "Descripción", "addPackageItem": "Vincular otro punto o trabajo", "createdNotif": "Paquete creado exitosamente", "updatedNotif": "Paquete editado exitosamente", "subItemDescriptionPlaceholder": "Ingrese descripción del subpunto", "cancelPackageCreation": "¿Cancelar creación de paquete?", "cancelPackageEditing": "¿Cancelar edición de paquete?"}, "deletePopup": {"title": "¿Eliminar paquete?", "body": "Al eliminar este paquete, <1>se eliminará de la lista de paquetes.</1>", "deleteButton": "Eliminar paquete", "deletedSuccessfully": "Paquete eliminado exitosamente."}, "importModal": {"uploadFile": "Subir archivo", "notes": "Formato aceptado: .xslx\nTamaño máximo permitido del documento: 10 MB", "downloadTemplate": "Descargar plantilla", "success": {"title": "<PERSON>umento cargado", "text": "La lista de paquetes ha sido cargada correctamente."}, "error": {"title": "Documento no cargado", "text": "La lista de paquetes no se ha cargado; revise los datos e intente nuevamente."}, "cancel": "¿Cancelar la carga de archivo .xlsx?", "conflict": {"text": "Hay un conflicto en la columna \"{{columns}}\". Elimine los datos incorrectos de la celda correspondiente.", "title": "El paquete \"{{packageName}}\" no se pudo importar", "descriptionColumns": "Descripción", "itemName": "Vincular punto o trabajo"}, "mandatoryColMissing": {"text": "Tiene columnas obligatorias sin información.", "title": "Archivo no cargado"}}}}, "customizableFields": {"types": {"predefined-Dropdown": "Selector desplegable", "predefined-Scan": "Escaneo", "predefined-RecordAudio": "Grabación de audio", "ShortText": "Texto libre corto", "LongText": "Texto libre largo", "Numeric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Date": "<PERSON><PERSON>", "Time": "<PERSON><PERSON>", "Currency": "Moneda", "MultipleChoice": "Opción múltiple", "MultiSelect": "Opción múltiple"}, "placeholders": {"select": "Seleccione"}}, "toasters": {"oneMoment": "Un momento por favor", "errorOccurred": "Ocurrió un error", "errorOccurredWhenLoading": "Ocurrió un error al cargar los datos", "errorOccurredWhenSaving": "Ocurrió un error al guardar los datos", "unexpectedError": "Ocurre un error inesperado", "settingUpdated": "Configuración actualizada", "settingSuccessfullyUpdated": "Configuración actualizada correctamente", "networkErrorTitle": "Acción fallida", "networkErrorBody": "Ha habido un problema de conexión y la acción no ha podido ser ejecutada correctamente.<br><br>Comprueba la conexión a internet antes de realizar nuevamente la acción", "settingUpdateFailure": "Configuración no puede ser actualizada", "fileNotSupported": "Archivo no compatible", "updatedConfiguration": "Configuración actualizada", "configurationChangedSuccessfully": "La configuración se ha cambiado con éxito", "noResultsFoundFor": "Sin resultados para", "insufficientPermissionsTitle": "Permisos insuficientes", "insufficientPermissionsBody": "No tienes derechos para ver esta página", "disabledFeatureTitle": "La función está deshabilitada", "disabledFeatureBody": "Esta función está deshabilitada en su cuenta", "documentPartiallyUploadedTitle": "Documento cargado parcialmente", "documentPartiallyUploadedText": " Se cargaron {{importedRows}} de {{totalRows}} registros </br> <a href=\"{{failedRowsFileUrl}}\">Ver los registros que no se cargaron</a>"}, "communication": {"sendEmail": "Enviar E-mail", "sendEmailWithNote": "Enviar E-mail con nota", "sendEmailWithNoteDesc": "Popup description", "sendSms": "Enviar SMS", "sendSmsWithNote": "Enviar SMS con nota", "sendSmsWithNoteDesc": "Popup description", "sendWa": "Enviar por WhatsApp", "sendWaSmsEmail": "Enviar WA, SMS y E-mail", "sendWaSms": "Enviar WA y SMS", "sendWaEmail": "Enviar por WA y E-mail", "sendSmsEmail": "Enviar SMS e E-mail", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "sendOrder": "<PERSON><PERSON><PERSON>", "callCustomer": "<PERSON><PERSON><PERSON> a cliente", "callLandline": "Llamar a teléfono", "callMobile": "<PERSON><PERSON><PERSON> a celular", "viewCallsLog": "Ver registro", "messageSent": "¡Su mensaje ha sido enviado exitosamente!"}, "sendEmail": {"sendEmail": "Enviar E-mail con nota personalizada", "writeTheMessage": "Escriba el mensaje con la nota personalizada que desea enviar.", "email": "E-mail", "emailPlaceholder": "<EMAIL>", "message": "Men<PERSON><PERSON>", "attachFile": "Adjuntar archivo", "shortcuts": "<PERSON><PERSON><PERSON>", "insertLink": "Insertar Vínculo", "writeComment": "Escriba aquí un comentario.", "sendFailedTitle": "<PERSON><PERSON>r de env<PERSON>", "sendFailedBody": "Error al enviar el E-mail, inténtalo de nuevo"}, "sendSms": {"sendFailedTitle": "<PERSON><PERSON>r de env<PERSON>", "sendFailedBody": "Error al enviar el mensaje, inténtalo de nuevo", "shortcuts": "<PERSON><PERSON><PERSON>", "sendSms": "Enviar SMS con nota personalizada", "writeTheMessage": "Escribe el mensaje con la nota personalizada que deseas enviar.", "number": "Número", "numberPlaceholder": "Ingresa el móvil del cliente", "message": "Men<PERSON><PERSON>", "insertLink": "Insertar Vínculo", "writeComment": "Escribe aquí un comentario."}, "errorMessages": {"invalidMailTitle": "Email Incorrecto", "invalidMailBody": "Por favor introduce un email válido.", "wrongPasswordTitle": "Nombre de usuario o contraseña incorrectos", "wrongPasswordBody": "Inténtelo de nuevo.", "passwordsDontMatchTitle": "Contraseñas no coinciden", "passwordsDontMatchBody": "Asegurate de que confimración y contraseña sean iguales.", "recaptchaErrorTitle": "ReCAPTCHA", "recaptchaErrorBody": "Tus acciones indican que podrías ser un bot, no podemos procesar tu solicitud", "userDoesntExistTitle": "El usuario no existe", "userDoesntExistBody": "Asegúrese de haber introducido correctamente el nombre de usuario", "requestThrottledTitle": "Lí<PERSON>", "requestThrottledBody": "Ha alcanzado el límite de restablecimientos de contraseña para este correo electrónico. Vuelva a intentarlo en 24 horas.", "userNotFound": "Usuario no encontrado"}, "notFound": {"haveProblem": "¡HOUSTON, TENEMOS UN PROBLEMA!", "weCanNotFind": "No encontramos la página que solicitaste.", "weCanNotFindOrderDetail": "No encontramos la Orden {{- orderNumber}}, por favor, intente de nuevo más tarde", "goBack": "Regresar"}, "passwordValidation": {"notification": {"title": "Error al restablecer la contraseña", "lowercase": "La contraseña debe de tener al menos una letra minúscula.", "uppercase": "La contraseña debe de tener al menos una letra mayúscula.", "empty": "Campo de contraseña no puede estar vacío.", "length": "La contraseña debe tener al menos 8 caracteres.", "digits": "La contraseña debe tener al menos un dígito.", "special": "La contraseña debe incluir al menos un caracter especial. (!,@,#,$,%,^,&,*,?,Etc.)", "equal": "Las contraseñas no coinciden. Intente nuevamente."}, "label": {"lowercase": "<PERSON>be incluir al menos una letra minúscula", "uppercase": "<PERSON>be incluir al menos una letra mayús<PERSON>", "digits": "Debe incluir al menos un número", "special": "Debe incluir al menos un carácter especial (!,@,#,$,%,^,&,*,?,Etc.)", "length": "Su contraseña debe tener al menos 8 caracteres"}, "secure": "Contraseña segura"}, "accountDeactivated": {"somethingDisconnected": "¡Oh oh! Parece que algo se ha desconectado", "accountIsNotActivated": "Tu cuenta no está activada.", "executiveCanHelp": "Pero no te preocupes, tu Ejecutivo de Cuenta puede ayudarte.", "contact": "Contactar Ejecutivo de Cuenta", "supportForm": {"header": "¿Necesitas ayuda?", "email": "Corre electrónico", "emailPlaceholder": "Ingrese aquí su correo de contacto", "message": "Detalle del problema", "messagePlaceholder": "Por favor descríbanos aquí el detalle de su problema", "send": "Enviar", "enterValidUserName": "Introduzca un nombre de usuario válido", "invalidUserName": "Nombre de usuario no válido"}}, "helpEmailSent": {"great": "¡GENIAL!", "notified": "Tu ejecutivo de cuenta ya fue notificado, pronto se pondrá en contacto contigo"}, "timePassed": {"secondAgo": "Hace 1 segundo", "xSecondsAgo": "Hace {{seconds}} segundos", "minuteAgo": "Hace 1 minuto", "xMinutesAgo": "Hace {{minutes}} minutos", "hourAgo": "Hace 1 hora", "xHoursAgo": "Hace {{hours}} horas", "dayAgo": "Hace 1 día", "xDaysAgo": "Hace {{days}} días"}, "callLog": "Registro de llamadas", "newVehicle": {"platesPlaceholder": "Ingrese las placas", "brandPlaceholder": "Deslice o teclee la marca", "modelPlaceholder": "Deslice o teclee el modelo", "year": "<PERSON><PERSON>", "yearPlaceholder": "Deslice o teclee el año", "colorPlaceholder": "Ingrese el color", "color": "Color", "areYouSure": {"yes": "<PERSON><PERSON>, cancelar", "subtitle": "Los cambios realizados no serán guardados.", "title": "¿Estás seguro que deseas cancelar la creación de vehículo?"}, "title": "Nuevo vehículo", "optionalFields": "Todos los campos son opcionales", "create": "<PERSON><PERSON><PERSON>", "vehicleCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>o", "vehicleCreatedSuccessfully": "El vehículo se ha creado correctamente.", "vehicleNotCreated": "Vehículo no creado", "vehicleDuplicated": "El vehículo del cliente ya fue registrando anteriormente, el cliente no puede tener vehículos duplicados.", "vinDuplicated": "Este número VIN ya está registrado en otro vehículo.", "platesDuplicated": "Estas placas ya están registradas en otro vehículo.", "vinAndPlatesDuplicated": "Este número de placas y VIN ya están registrados en otro vehículo.", "brandModelYearDuplicated": "Un vehículo con la misma marca, modelo y año ya existe para este cliente. Agregue el VIN o las placas para diferenciarlo."}, "vehicleAutocomplete": {"selectAVehicle": "Seleccione un vehículo", "missingVehicleNotice": "Este vehículo ya no pertenece al cliente seleccionado"}, "newCustomer": {"vehicleNotice": "Para crear un nuevo vehículo, por favor proporcione al menos placas, VIN o marca + modelo + año.", "title": "Nuevo cliente y vehículo", "requiredFields": "Campos obligatorios", "create": "Crear cliente y vehículo", "name": "Nombre", "namePlaceholder": "Ingrese el nombre", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "lastNamePlaceholder": "Ingrese los apellidos", "email": "Correo electrónico", "emailPlaceholder": "Ingrese el correo electrónico", "mobile": "<PERSON><PERSON><PERSON>", "mobilePlaceholder": "Ingrese el celular", "taxIdent": "[Sentence:TaxIdentification]", "taxIdentPlaceholder": "Ingrese la [Lower:TaxIdentification]", "platesPlaceholder": "Ingrese las placas", "brandPlaceholder": "Deslice o teclee la marca", "modelPlaceholder": "Deslice o teclee el modelo", "year": "<PERSON><PERSON>", "yearPlaceholder": "Deslice o teclee el año", "color": "Color", "colorPlaceholder": "Ingrese el color", "businessName": "Nombre comercial", "businessNamePlaceholder": "Ingrese el nombre comercial", "areYouSure": {"yes": "<PERSON><PERSON>, cancelar", "subtitle": "Los cambios realizados no serán guardados.", "title": "¿Estás seguro que deseas cancelar la creación del cliente?"}, "created": "Cliente y vehículo creados", "createdBody": "El cliente y el vehículo se han creado con éxito.", "notCreated": "Cliente no creado", "notCreatedBody": "Este número de celular ya está asociado a otro cliente.", "changeCustomer": "(Cambiar de cliente)", "noCustomersText": "No se encontraron clientes. Verifique los datos, intente otra búsqueda o complete los campos para crear uno nuevo."}, "locations": {"allLocations": "Todas las sucursales", "selectLocation": "Selecciona una sucursal", "location": "Sucursal", "locationsWithCount_one": "{{count}} sucursal", "locationsWithCount_other": "{{count}} sucursales", "locationSwitchWarning": {"title": "Nota sobre el cambio del campo \"Sucursal\"", "text": "Si el campo \"Sucursal\" se cambia a una ubicación diferente, el campo \"Cliente\" también se restablece. La razón es que la información del “Cliente” está vinculada a una ubicación específica.", "yes": "Si, cambia de sucursal"}}, "appointments": {"accessDeniedText": "Lo siento, no tiene permisos para crear citas.", "accessDeniedHint": "Puede actualizar sus permisos desde la pantalla \"Configuraciones\".", "gettingAppts3rdParty": "Estamos obteniendo citas de {{integratedAccountName}}.", "get3rdParty": {"success": {"title": "Citas sincronizadas correctamente", "description": "Las citas han sido sincronizadas correctamente desde {{integratedAccountName}}."}, "error": "Ocurrió un error al obtener las citas de {{integratedAccountName}}."}, "convertToOrder": "Convertir en Orden", "allAppointments": "Todas las citas", "allAdvisors": "Todos los Asesores", "filters": {"filters": "<PERSON><PERSON><PERSON>", "selectFilters": "Seleccione filtros", "apply": "Aplicar", "view": "Vista", "status": "<PERSON><PERSON><PERSON>", "advisors": "<PERSON><PERSON><PERSON>"}, "newAppointment": "Nueva cita", "editAppointment": "Editar cita", "requiredFields": "Campos obligatorios", "createAppointment": "Crear cita", "saveChanges": "Guardar cambios", "cancel": "<PERSON><PERSON><PERSON>", "pleaseSelectLocationFirst": "Por favor seleccione primero la sucursal", "shopTzNotice": "Considerar que los horarios que se muestran corresponden a la zona horaria de la sucursal", "repairOrderExists": "La orden de reparación con este número ya existe", "step1": {"step1": "PASO 1", "selectACustomer": "Seleccione un cliente", "searchAndSelectCustomerByNameMobileOrPlates": "Busque y seleccione el cliente por nombre, celular o placas", "customerName": "Nombre del cliente", "name": "Nombre", "lastNames": "<PERSON><PERSON><PERSON><PERSON>", "mobile": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "taxIdentification": "[Sentence:TaxIdentification]", "selectAVehicle": "Seleccione un vehículo", "plates": "Placas", "noPlate": "<PERSON> matrícula", "noPlates": "<PERSON> matrícula", "vin": "VIN", "noVin": "Sin VIN", "brand": "<PERSON><PERSON>", "noBrand": "Sin marca", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "color": "Color", "createNewCustomer": "Crear nuevo cliente", "createNewVehicle": "Crear nuevo vehículo", "frequentCustomer": "Clientes frecuentes", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "step2": {"step2": "PASO 2", "selectTheReasonForTheAppointment": "Seleccione el [Lower:ReasonForVisit]", "reasonForTheAppointment": "[Lower:ReasonForVisit]", "workshopReasonForAppointment": "[Sentence:WorkshopReasonForVisit]", "customerReasonForAppointment": "[Sentence:CustomerReasonForVisit]", "selectOrAddTheReasonForTheAppointment": "Selecciona o agrega el [Lower:ReasonForVisit]", "addReasonForAppointment": "Ag<PERSON><PERSON> o<PERSON> [Lower:ReasonForVisit]", "reasonDetailsForTheAppointment": "Detalle de [Lower:ReasonForVisit]", "selectTheReasonDetailsForTheAppointment": "Seleccione el detalle [Lower:ReasonForVisit]", "seeAll": "Ver todos", "save": "Guardar", "reasonsForTheAppointment": "[Sentence:ReasonsForVisit]", "listOfAllExistingReasonsForTheAppointment": "Lista de todos los [Lower:ReasonsForVisit] existentes", "search": "Búsqueda", "frequentReasons": "Motivos frecuentes", "appointmentReasonDuplicated": "El [Lower:ReasonForVisit] está duplicado"}, "step3": {"step3": "PASO 3", "selectTheTimeAndDateOfTheAppointment": "Seleccione la hora y fecha de la cita", "selectTheDate": "Seleccione la fecha", "selectTheServiceAdvisorInCharge": "Seleccione el [Title:ServiceAdvisor] a cargo", "anyServiceAdvisor": "Cualquier [Title:ServiceAdvisor]", "selectTime": "Seleccione la hora", "thereIsNoAvailability": "No hay disponibilidad para agendar citas para este día, por favor selecciona otro día.", "theClientCouldNotBeEdited": "El cliente no pudo ser editado", "theNameCannotBeEmpty": "El Nombre no puede estar vacío.", "theMobileCannotBeEmpty": "El Celular no puede estar vacío.", "theMobileIsRegistered": "El Celular ya está registrado."}, "appointmentFiles": {"deleteFile": "Eliminar archivos", "deleteFileQuestion": "¿Eliminar archivo?", "attachedFiles": "Archivos adjuntos", "attachFile": "Adjuntar archivo", "noAttachedFiles": "Sin archivos adjuntos", "downloadFile": "Descargar archivo", "areYouSureDelete": "¿Está seguro de eliminar este archivo?", "fileDeleted": "Archivo eliminado", "noDelete": "No eliminar", "fileSizeExceeded": "Tamaño del archivo excedido", "fileSizeExceeded10Mb": "El archivo supera los 10 MB. Por favor, reduzca el tamaño."}, "personWhoSchedules": "Persona que agenda", "appointmentNumber": "Número de cita", "appointmentNumberIsDuplicated": "El número de cita está duplicado", "invalidMobileNumberError": "Teléfono móvil en formato incorrecto", "appointmentNumberAlreadyTaken": "Número de cita ocupado", "weSuggestUsingAnotherNumber": "Le sugerimos usar otro número", "status": {"appointmentStatus": "Estatus de la cita", "unconfirmed": "<PERSON>ar", "confirmed": "<PERSON><PERSON>rma<PERSON>", "confirmedDisabled": "Este estatus solo se podrá seleccionar una vez que el cliente haya confirmado su asistencia en el mensaje de recordatorio de cita.", "customerArrived": "Cliente ll<PERSON>ó", "customerDidNotArrive": "Cliente no llegó", "orderCreated": "<PERSON><PERSON> creada", "orderCreatedDisabled": "Este estatus solo se podrá seleccionar una vez que la cita se haya convertido en una Orden.", "onTime": "En tiempo", "delayed": "<PERSON><PERSON><PERSON>"}, "appointmentOrigin": "Origen de cita", "valetService": "<PERSON><PERSON><PERSON>", "selectAnOrigin": "Selecciona un origen", "date": "<PERSON><PERSON> promesa", "selectADate": "Selecciona una fecha", "time": "<PERSON>ra promesa", "selectAHour": "Selecciona una hora", "observations": "Observaciones", "enterTheObservations": "Ingresa las observaciones", "enterTheAppointmentNumber": "Ingrese número de cita único", "itIsAssignedAutomatically": "Se asigna de forma automática", "deleteTitle": "¿Eliminar la cita?", "delete": "Eliminar cita", "orderNumber": "Númer<PERSON> Orden", "location": "Sucursal", "customerName": "Nombre del cliente", "e-mail": "Correo electrónico", "mobile": "<PERSON><PERSON><PERSON>", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": "Duración", "serviceAdvisor": "[Title:ServiceAdvisor]", "reasonForTheAppointment": "[Sentence:ReasonForVisit]", "customerArrived": "Cliente ll<PERSON>ó", "customerDidNotArrive": "Cliente no llegó", "cancelNewAppointmentRegister": "¿Cancelar registro de nueva cita?", "appointmentDeleted": "Cita eliminada", "theAppointmentHasBeenDeletedSuccessfully": "La cita se ha eliminado correctamente.", "cancelAppointmentEditing": "¿Cancelar edición de cita?", "deleteAppointmentTooltip": "Eliminar cita", "appointmentUpdated": "Cita actualizada", "confirm": "Confirmar", "goBack": "Regresar", "confirmCustomerDidNotArrive": "Confirmar cliente no llegó", "confirmCustomerArrived": "Confirmar cliente llegó", "didNotArriveForHisAppointment": "no llegó a su cita", "arrivedForHisAppointment": "llegó a su cita", "from": "de", "appointmentCreated": "<PERSON><PERSON> c<PERSON>a", "scheduleNewAppointmentOrAbsence": "Dar clic para agendar cita o ausencia", "scheduleNewAppointment": "Agendar nueva cita", "editAbsence": "<PERSON><PERSON> ausen<PERSON>", "scheduleAbsence": "Agendar ausencia", "backToToday": "Hoy", "updateCustomer": "Actualizar cliente", "confirmCustomerUpdateText": "La informacion de cliente y/o vehiculo existente ha sido modificada, ¿Estas seguro de guardar los cambios?", "week": "Se<PERSON>", "day": "Día", "notes": "Notas", "notesVisibleForCustomer": "Notas visibles para el cliente", "internalNotes": "Notas internas", "noNotesForCustomer": "Sin notas visibles para el cliente.", "noInternalNotes": "Sin notas internas.", "enterNotes": "Ingrese las notas", "editNote": "<PERSON><PERSON>", "deleteNote": "Eliminar", "noteActivityLog": "Bitácora", "noteCannotBeEdited": "No es posible editar el texto.", "appointmentCreatedInYourIntegratedSoftwareFirst": "<PERSON>ita creada correctamente en {{integratedAccountName}}.", "appointmentCreatedInYourIntegratedSoftware": "Cita creada en su software integrado.", "appointmentUpdatedInYourIntegratedSoftware": "Cita actualizada en su software integrado.", "appointmentDeletedInYourIntegratedSoftware": "Cita eliminada en su software integrado.", "appointmentMustBeCreatedUsing3rdPartySoftware": "La cita debe ser creada desde su software tercero. ", "appointmentMustBeEditedUsing3rdPartySoftware": "La cita debe ser editada desde su software tercero.", "omnichannelModal": {"appointmentNotCreatedInYourIntegratedSoftware": "Cita no creada en su software integrado.", "appointmentNotUpdatedInYourIntegratedSoftware": "Cita no actualizada en su software integrado.", "appointmentNotDeletedInYourIntegratedSoftware": "Cita no eliminada en su software integrado.", "oneMomentPlease": "Un momento por favor.", "weAreCreatingTheAppointmentInYourIntegratedSOftware": "Estamos creando la cita en su software integrado.", "weAreUpdatingTheAppointmentInYourIntegratedSoftware": "Estamos actualizando la cita en su software integrado.", "reasonForErrorInYourIntegratedSoftware": "Motivo de error de su software integrado", "doYouWantToDeleteTheAppointmentOnlyInClearMechanic": "¿Desea eliminar la cita únicamente en Clearmechanic?", "cancel": "<PERSON><PERSON><PERSON>", "retry": "Reintentar", "yesDeleteTheAppointment": "Si, eliminar la cita", "noKeepTheAppointment": "No, conservar la cita", "retryApptCreation": "Reintentar creación de cita", "createApptInClearmechanic": "Crear cita en ClearMechanic", "retryApptUpdate": "Reintentar actualización de cita", "updateApptInClearmechanic": "Actualizar cita en ClearMechanic", "synchronizeWithYourIntegratedSoftware": "Sincronizar con su software integrado", "appointmentNotCreatedInYourIntegratedSoftwareFirstTitle": "No fue posible crear la cita en {{integratedAccountName}}", "appointmentNotCreatedInYourIntegratedSoftwareFirstMessage": "Mensaje de error: {{errorMessage}}.", "appointmentThirdPartyModalTitle": "¿Crear cita  solo en ClearMechanic?", "appointmentThirdPartyModalBodyMessage": "La cita será creada en el Tablero web y no se sincronizará con su software integrado.", "appointmentThirdPartyModalBodyQuestion": "¿Desea reintentar crear la cita en {{integratedAccountName}} o continuar y crear la cita solo en ClearMechanic?", "appointmentThirdPartyRetry": "Reintentar", "appointmentThirdPartyCancel": "No, cancelar", "appointmentThirdPartyConfirm": "Sí, continuar"}, "appointmentNotes": {"addNote": "<PERSON><PERSON><PERSON>", "enterNote": "Ingrese nota", "addedBy": "<PERSON><PERSON><PERSON><PERSON> por", "on": "el", "modifiedBy": "Modificado por", "at": "a las", "customer": "cliente"}, "appointmentScheduledFromSiteForAppointments": "Cita agendada en línea.", "theServiceAdvisorAlreadyHasAnAppointment": "El Asesor de Servicio tiene una cita en este horario. Por favor, modifique la hora de la cita o asigne a otro Asesor de Servicio.", "conflictModal": {"title": "Conflicto de citas detectado", "theServiceAdvisor": "El Asesor de Servicio", "alreadyHasAnAppointmentAtSameTime": "ya tiene una cita en el mismo horario. ¿Desea modificar la cita actual o mantener ambas en el mismo horario?", "modifySchedule": "Modificar horario", "keepAppointment": "Mantener cita"}, "customerHistory": {"showCustomerHistory": "Ver historial de cliente", "goToOrderDetail": "<PERSON>r a de<PERSON><PERSON> de Orden", "noPreviousOrders": "Sin Órdenes anteriores para el cliente y vehículo seleccionados.", "previousOrders": "Órdenes anteriores", "nextMaintenance": "Próxi<PERSON>", "lastOrder": "Última Orden", "itemsNotApproved": "Puntos no aprobados en la última Orden", "order": "Orden", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "nextMaintenanceDate": "Fecha de próximo mantenim<PERSON>o", "mileage": "[Title:Mileage]", "serviceAdvisor": "[Title:ServiceAdvisor]", "date": "<PERSON><PERSON>", "orderType": "<PERSON><PERSON><PERSON> Orden", "reasonForTheAppointment": "Reason for the appointment", "notes": "Notas", "urgent": "Urgente", "suggested": "Sugerido"}}, "orderStatus": {"status": {"authPending": "Authorization pending", "readyForDelivery": "Listo para entrega", "prepForDelivery": "Preparación de entrega", "qualityControl": "Control de calidad", "onProcess": "En curso", "onQueue": "En fila", "inWash": "<PERSON><PERSON><PERSON>", "onHold": "Pendiente de {{reason}}"}, "detail": {"orderNumber": "[Upper:RO] No.", "promiseDate": "Fecha estimada de entrega", "tower": "Cono", "vehicleReady": "¡Tu vehículo está listo!", "holdingForDelivery": "Te esperamos para entregártelo", "estimateQuestions": "Si tienes duda sobre tu estimación, por favor, contacta a tu [Title:ServiceAdvisor].", "delivered": "<PERSON><PERSON><PERSON>", "answerPoll": "Responder encuesta", "authPending": "Pendiente de autorización"}}, "status": {"appointments": {"goBackTooltip": "Regresar", "selectColumnsTooltip": "Seleccionar columnas", "columns": "Columnas", "showAdvisorName": "Mostrar nombre de Asesor", "headers": {"appointmentTime": "Hora de Cita", "serviceAdvisor": "<PERSON><PERSON><PERSON>", "customer": "Cliente", "model": "<PERSON><PERSON>", "plates": "Placas", "status": "<PERSON><PERSON><PERSON>", "fullName": "Nombre completo", "initials": "Iniciales"}, "status": {"order#": "Orden #", "orderCreated": "<PERSON><PERSON> creada", "customerDidNotArrive": "Cliente no llegó", "confirmed": "Cita confirmada", "unconfirmed": "<PERSON>ar"}}, "orders": {"goBackTooltip": "Regresar", "selectColumnsTooltip": "Seleccionar columnas", "headers": {"deliveryTime": "Hora de en<PERSON>ga", "tower": "Torre", "#order": "# Orden", "customer": "Cliente", "model": "<PERSON><PERSON>", "plates": "Placas", "phase": "Etapa"}, "reasonsForPause": {"assignmentofAnotherVehicle": "Asignación de otro vehículo", "lunch": "<PERSON><PERSON><PERSON>", "waitingForCustomerAuthorization": "En espera de autorización del cliente", "waitingForServiceBay": "En espera de bahía", "waitingForTools": "En espera de herramientas", "waitingForTechnicianReassignment": "En espera de reasignación de [Title:Technician]", "waitingForParts": "En espera de [Lower:Parts] / repuestos", "inWarrantyProcess": "En proceso de garantía", "tot": "TOT", "other": "<PERSON><PERSON>"}, "noPhase": "Sin etapa"}}, "conversations": {"whatsAppTemplates": "Plantillas de WhatsApp", "sendWhatsAppTemplate": "Enviar plantilla de WhatsApp", "closedWindowTemplates": {"followUpAfterEstimate": "Seguimiento después de dar una cotización", "followUpAfterAppointmentNoShow": "Seguimiento por cita no asistida", "followUpNotAnsweringQuestion": "Seguimiento por no haber respondido a una duda del cliente"}, "voiceMessage": "Mensaje de voz", "dateFormat": "DD/MM/YYYY", "filter": {"tooltip": "Mostrar filtros", "searchPlaceholder": "Busca por nombre o teléfono", "bothInboxes": "Ambas bandejas", "advisorInbox": "<PERSON><PERSON><PERSON>", "chatbotInbox": "<PERSON><PERSON><PERSON>", "active": "Activas", "closed": "<PERSON><PERSON><PERSON>", "hideFilters": "Ocultar filtros"}, "mutePreferences": {"tooltip": "Gestionar notificaciones de mensajes específicos en todas las conversaciones", "popup": {"title": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "¿Cancelar configuración de silencio?", "messageType": "Tipo de mensaje", "exceptions": "Excepciones", "always": "Siempre", "custom": "Personalizado", "silenceDuration": "Duración del silencio", "silenceDurationTooltip": "Define el lapso de tiempo para desactivar las notificaciones de mensajes. Este ajuste influirá en los mensajes enviados por los clientes.", "templateCategoriesTooltip": "Desactiva las notificaciones de mensajes enviados. Esto aplica a los mensajes de clientes que se envían después de recibir dichas notificaciones.", "exceptionsLabel": "Seleccione los mensajes de clientes que desee habilitar", "businessCustomers": "Mensajes de clientes que son empresas", "businessCustomersTooltip": "Los mensajes de clientes empresariales identificados por “Nombre de empresa” en el detalle de la Orden no serán silenciados.", "ordersWithoutActivity": "Mensajes de clientes con Órdenes sin actividad", "ordersWithoutActivityTooltip": "Los mensajes de clientes con Órdenes inactivas, basándose en el tiempo especificado en “Tiempo sin actividad” no serán silenciados.", "timeWithoutActivity": "Tiempo sin actividad", "keywords": "Mensajes con palabras claves", "keywordsTooltip": "Los mensajes que contengan palabras clave ingresadas en el cuadro no serán silenciados.", "keywordsNote": "Comience a escribir para añadir palabras claves. Presione la tecla \"Enter\" para confirmar nueva palabra.", "keywordsLimitWarning": "Se permite un máximo de 20 palabras clave", "invalidSilenceDuration": "La duración del silencio debe ser mayor que 0"}, "templateCategories": {"label": "Seleccione los mensajes que desea silenciar.", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "All": "Todos los mensajes", "AppointmentConfirmation": "Mensajes de confirmación de cita", "AppointmentReminder": "Mensajes de recordatorio de cita", "SentDuringOrder": "Mensajes enviados al cliente durante la Orden", "Survey": "Mensajes de encuestas", "NextServiceReminder": "Mensajes de recordatorio de próximo servicio", "FollowUpMessages": "Mensajes de seguimiento", "MassSendingMessages": "Mensajes de envíos masivos"}}, "muteState": {"dateFmt": "d 'de' LL<PERSON> 'de' yyyy", "until": "Hasta {{value}}", "template": "Conversación silenciada: {{when}}", "always": "Siempre", "errorTooltip": "No se pudo recuperar el estado de la conversación silenciada, haga clic aquí para volver a recuperarlo"}, "showCustomerDetail": "Ver detalle del cliente", "hideCustomerDetail": "<PERSON><PERSON>r de<PERSON> del cliente", "useTemplate": "Usar plantilla", "textAreaPlaceholder": "Escribe un mensaje...", "fastMessages": {"welcome": "De nada.", "service": "Estamos para servirte.", "pleasure": "Un placer."}, "send": "Enviar", "templates": {"cm_consumer_numbers": "Información de la orden"}, "selectTemplate": "Selecciona una plantilla", "noContextWarning": "No es posible enviar mensajes a conversaciones que no estén vinculadas a una Orden.", "chatBotModeWarning": "No es posible enviar mensajes mientras el cliente no requiera la atención del asesor.", "closedWindowTimeWarning": "De acuerdo con las políticas de Meta, solo es posible enviar plantillas aprobadas al no tener respuesta del cliente en las últimas 24 horas.", "notCurrentControlFlowWarning": "No es posible enviar mensajes mientras otro [Lower:ServiceAdvisor] tenga el control actual de la conversación.", "orderNumber": "Orden #{{number}}", "sentBy": "<bold>Enviado por</bold>: {{name}}", "instruction": "Por favor, seleccione una conversación para ver más detalles.", "errorMessages": {"failedToCall": "No se pudo llamar al cliente", "phoneNumberIsMissing": "No hay un número telefónico configurado para el miembro del equipo"}}, "chatNotifications": {"conversationsNumber": "Conversaciones ({{count}})", "readAll": "Marcar todo como leído", "unreadMessages": "Mensajes sin leer", "conversationWith": "Conversación con:", "conversationsAvailable": "conversaciones de WA disp.", "marketingConversations": "Conv. de WA marketing utilizados:", "utilityServiceConversations": "Conv. de WA utilidad/servicio utilizados:"}, "notifications": {"header": "Notificaciones"}, "reports": {"reportsPageTitle": "Reportes", "forGeneralManager": "Para Gerente General", "forAftersalesManager": "Para el Gerente de Postventa", "forAll": "Para todos", "Question": "Pregunta", "Answer": "Respuesta pregunta", "ChecklistNumber": "Checklist #", "ItemsInChecklistNumber": "<PERSON><PERSON><PERSON> en Checklist #", "cr_OrderNumber": "# Orden", "cr_OrderNumberByOpeningDate": "# Orden por fecha de apertura", "cr_OrderNumberByClosingDate": "# Orden por fecha de cierre", "cr_Location": "Sucursal", "cr_Currency": "Moneda", "cr_GeneralInformation": "Información general", "cr_AppointmentNumber": "# Cita", "cr_ServiceAdvisor": "[Title:ServiceAdvisor]", "cr_Technician": "[Title:Technician]", "cr_CustomerName": "Nombre", "cr_CustomerEmail": "<PERSON><PERSON><PERSON>", "cr_CustomerMobile": "<PERSON><PERSON><PERSON>", "cr_CustomerLandline": "Teléfono", "cr_Customers": "Clientes", "cr_Appointments": "Citas", "cr_Appointment": "# Cita", "cr_AppointmentStatus": "Estatus de cita", "cr_AppointmentReason": "[Sentence:ReasonForVisit]", "cr_AppointmentDate": "Fecha de la cita", "cr_AppointmentTime": "Hora de la cita", "cr_AppointmentOrigin": "Origen de cita", "cr_AppointmentUserWhoSchedule": "Persona que agenda", "cr_Estimates": "[Title:Estimates]", "cr_EstimatesParts": "[Title:Estimate] - [Title:Parts]", "cr_EstimatesLabor": "[Title:Estimate] - [Title:Labor]", "cr_EstimatesSubTotal": "[Title:Estimate] - Subtotal", "cr_EstimatesTax": "Impuestos", "cr_EstimatesTotal": "[Title:Estimate] - Total", "cr_EstimatesTotalApproved": "Monto aprobado", "cr_EstimatesTotalRejected": "<PERSON><PERSON>", "cr_EstimatesPercentApproved": "% Aprobado", "cr_EstimatesApproveStatus": "¿Aprobado o rechazado?", "cr_Surveys": "Encuestas", "cr_SurveysDeliveryDate": "<PERSON><PERSON>", "cr_SurveysAnswerDate": "<PERSON><PERSON> de respuesta", "cr_Question1": "Pregunta 1", "cr_AnswerQuestion1": "Respuesta pregunta 1", "cr_Question2": "Pregunta 2", "cr_AnswerQuestion2": "Respuesta pregunta 2", "cr_Orders": "<PERSON><PERSON><PERSON>", "cr_OrdersNumber": "Orden #", "cr_OrdersTower": "Torre", "cr_OrdersType": "<PERSON><PERSON><PERSON> Orden", "cr_OrdersSecureLink": "Orden digital", "cr_OrderTechnician": "Técnico", "cr_WorkshopPlannerTechnician": "Técnico", "cr_OrdersRedItems": "Puntos rojos", "cr_OrdersYellowItems": "<PERSON>unt<PERSON> amarillo<PERSON>", "cr_OrdersGreenItems": "Puntos verdes", "cr_OrdersNaItems": "Puntos N/A", "cr_OrdersInspectionItems": "Puntos de inspección", "cr_OrdersSuccessfulCalls": "Llamadas exitosas", "cr_OrdersPriority": "Prioridad", "cr_OrdersFailedCalls": "Llamadas fallidas", "cr_OrdersCustomerViews": "Vistas del cliente", "cr_OrdersInternalViews": "Vistas internas", "cr_OrdersPhotos": "Fotos", "cr_OrdersVideos": "Videos", "cr_OrdersServiceAdvisor": "<PERSON><PERSON><PERSON>", "cr_OrdersUploadedBy": "<PERSON><PERSON> por", "cr_OrdersInCharge": "A cargo", "cr_OrdersAssignedTo": "Asignado a", "cr_OrdersDateOfUpload": "Fecha de apertura de la Orden", "cr_OrdersTimeOfUpload": "Hora de apertura de la Orden", "cr_OrdersDateOfLastUpdate": "Fecha de última actualización", "cr_OrdersTimeOfLastUpdate": "Hora de última actualización", "cr_OrdersFirstDateMessage": "Fecha de primer en<PERSON><PERSON> la Orden", "cr_OrdersFirstTimeMessage": "Hora de primer en<PERSON><PERSON> la Orden", "cr_OrdersTimeUntilFirstSending": "Tiempo hasta primer envío", "cr_OrdersLastCommunication": "Última comunicación", "cr_OrdersDateOfClosing": "<PERSON><PERSON> <PERSON> c<PERSON>re de la Orden", "cr_OrdersTimeOfClosing": "Hora de cierre de la Orden", "cr_OrdersTimeFromUploadedToClosed": "Tiempo de permanencia", "cr_OrdersPhaseSetback": "Retroceso de etapa", "cr_OrdersOriginPhase": "Etapa origen", "cr_OrdersDestinationPhase": "Etapa destino", "cr_OrdersPhaseSetbackReason": "Motivo del retroceso", "cr_WorkshopPlanner": "<PERSON><PERSON><PERSON>", "cr_WorkshopPlannerPhase": "Etapa actual", "cr_WorkshopPlannerDeliveryDate": "Fecha promesa de entrega", "cr_WorkshopPlannerDeliveryTime": "Hora promesa de entrega", "cr_WorkshopPlannerDuration": "<PERSON><PERSON><PERSON>", "cr_WorkshopPlannerPauseReason": "Motivo de pausa", "cr_WorkshopPlannerTimeUnitsSold": "Unidades de tiempo vendidas", "cr_WorkshopPlannerTimeOnPause": "Tiempo en pausa", "cr_WorkshopPlannerAvailableHours": "Horas disponibles", "cr_WorkshopPlannerHoursPresent": "Horas de presencia", "cr_WorkshopPlannerProductiveHours": "Horas productivas", "cr_WorkshopPlannerUnproductiveHours": "<PERSON><PERSON> improductivas", "cr_WorkshopPlannerPercentProductivity": "% productividad", "cr_WorkshopPlannerNumberOfJobs": "Cantidad de trabajos", "cr_WorkshopPlannerJobDescription": "Descripción del trabajo", "cr_WorkshopPlannerJobEndDateAndTime": "Fecha y hora fin de trabajo", "cr_WorkshopPlannerJobNumber": "Número de trabajo", "cr_WorkshopPlannerJobRealDuration": "Tiempo real del trabajo", "cr_WorkshopPlannerJobStartDateAndTime": "Fecha y hora inicio de trabajo", "cr_Vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cr_VehicleBrand": "<PERSON><PERSON>", "cr_VehicleModel": "<PERSON><PERSON>", "cr_VehicleYear": "<PERSON><PERSON>", "cr_VehicleVin": "VIN", "cr_VehicleMileage": "[Title:Mileage]", "cr_VehiclePlates": "Placas", "cr_OrdersRepairsIsSubItem": "¿Punto o sub-punto?", "noPhase": "Sin etapa", "timeInPhase": "Tiempo en \"{{phaseName}}\"", "OrdersTimeFromUploadedToClosedPropertyTooltip": "Tiempo que permaneció el vehículo en la sucursal.", "WorkshopPlannerDurationTooltip": "Suma de tiempos asignados a trabajo en la Orden.", "WorkshopPlannerTimeOnPauseTooltip": "Suma de tiempo en que la Orden ha estado en pausa.", "WorkshopPlannerCurrentPhaseTooltip": "Etapa actual que se tiene seleccionada la Orden.", "OrderByOpeningDateTooltip": "Las Órdenes incluidas en el reporte serán aquellas que se crearon dentro del rango de tiempo seleccionado.", "OrderByClosingDateTooltip": "Las Órdenes incluidas en el reporte serán aquellas que se cerraron dentro del rango de tiempo seleccionado.", "WorkshopPlannerJobNumberTooltip": "Se asigna un número a cada trabajo según su hora programada. El primer trabajo programado es el número 1, el segundo es el número 2, y así sucesivamente.", "WorkshopPlannerJobRealDurationTooltip": "Es la duración, en horas y minutos, entre la hora de inicio y la hora de finalización del trabajo. No incluye los períodos en los que estuvo pausado.", "executiveIntelligenceReportOption": "Reporte de inteligencia ejecutiva", "auditedProcessesOption": "Reporte de actividad: Procesos auditados", "inspectionPatternsReportOption": "Reporte de patrones de inspección", "financialKpisReportOption": "Reporte de KPI´s financieros", "operationalKpisReportOption": "Reporte de KPI´s operativos", "activityReportInspectionFormsOption": "Reporte de actividad: Formas de inspección", "activityReportFollowUpLettersOption": "Reporte de actividad: Cartas de seguimiento", "activityReportDmsOption": "Reporte de actividad: DMS", "activityReportPartsOption": "Reporte de actividad: [Title:Parts]", "activityReportEstimatesOption": "Reporte de actividad: [Title:Estimates]", "activityReportSurveysOption": "Reporte de actividad: Encuestas", "activityReportWorkshopPlanner": "Informe de actividad: <PERSON><PERSON><PERSON>", "uploadDate": "Órden<PERSON> por fecha de carga", "updateDate": "Órdenes por última acción", "approvalDate": "Fecha de aprobación", "createReport": "<PERSON><PERSON><PERSON> reporte", "downloadReport": "<PERSON><PERSON><PERSON> reporte", "reportType": "T<PERSON>o de Reporte", "start": "<PERSON><PERSON>o", "end": "Fin", "RowLimitExceeded": "Límite de filas excedido", "MoreOneMillionRowsError": "Hemos generado su reporte, pero excede el límite de 1,000,000 de filas para Excel, por lo que no se incluyeron las filas correspondientes a las fechas {{formattedDateFromString}} - {{formattedLastIncludedDateString}}", "CustomReportFileName": "Reporte personalizado", "DownloadCancel": "Descarga cancelada", "DownloadSuccess": "<PERSON><PERSON>", "DownloadingInProgress": "¡Descarga en proceso! ", "YouCanContinueUsingOtherScreens": "Puede seguir utilizando otras pantallas y funciones mientras espera a que la descarga se complete.", "columnNotFound": "Columna no encontrada", "tryAgainWithADifferentColumn": "Intente con una columna diferente", "ifYouCancel": "Si cancela, perderá los últimos cambios que realizó.", "ifYouCancelYouWillLoseTheLatestChangesYouMade": "<PERSON> cancela, el reporte no será creado.", "cancelCustomReportEdition": "¿Cancelar edición del reporte personalizado?", "cancelCustomReportCreation": "¿Cancelar creación de reporte personalizado?", "cancelCreationText": "<PERSON> cancela, el reporte no será creado.", "cancelEdition": "Cancelar edición", "cancelCreation": "Cancelar creación", "savedReport": "Reporte guardado", "updatedReport": "Reporte actualizado", "unsavedChangesWillBeLost": "Se perderán los cambios no guardados", "cancelCustomReportEditing": "¿Cancelar edición del reporte personalizado?", "reportTheSameName": "Ya existe un reporte con el mismo nombre", "nameAlreadyExisting": "Nombre ya existente", "saveChanges": "Guardar cambios", "newReport": "Nuevo reporte", "areYouSure": "¿E<PERSON>á seguro?", "saveCustomReport": "Guardar reporte personalizado", "cancelTemp": "<PERSON><PERSON>ar <PERSON>", "saveReport": "Guardar reporte", "name": "Nombre", "cancel": "<PERSON><PERSON><PERSON>", "tryAgain": "Intentar de nuevo", "downloadError": "Error en la descarga", "errorOccurred": "Ha ocurrido un error al descargar el archivo", "downloadingAgain": "¿Desea intentar descargar el informe nuevamente?", "enterTheNameOfTheCustomReport": "Ingrese el nombre del reporte personalizado", "thereIsAlreadyAReportWithTheSameNamePleaseWriteAnotherName": "Ya existe un reporte con el mismo nombre. Por favor, escriba otro nombre.", "thisReportWillBeSavedForEasyFutureReference": "Se guardará este reporte para que pueda consultarlo fácilmente en el futuro", "yesCancel": "<PERSON><PERSON>, cancelar", "goBack": "Regresar", "results": "resultados", "result": "resultado", "createReportImage": "¡<PERSON>ree un reporte!", "pleaseSelectColumnsImage": "Por favor seleccione columnas", "selectedcolumnstitle": "<PERSON>umnas selecci<PERSON>", "selectedcolumnstitletooltip": "Estas columnas aparecerán en el reporte personalizado que usted cree.", "searchcolumn": "Buscar columna", "primarycolumn": "Columna principal", "primarycolumntooltip": "Seleccione una columna principal.", "primary": "Principal", "reportDeleted": "Reporte eliminado", "confirmreportdelete": "Eliminar reporte", "canceldelete": "Regresar", "nosavedreports": "Sin reportes guardados", "selectcustomizablereport": "Seleccione reporte personalizable", "deletecustomreporttitle": "¿Eliminar reporte personalizado?", "deletecustomreport": "Se eliminará permanentemente este reporte.", "columnstitle": "COLUMNAS", "informativebox": "Esta es una vista previa con filas limitadas. Para ver el reporte completo, de clic en <strong>“<PERSON>car<PERSON> reporte”</strong>.", "uploadTooltipText": "Genere reportes para las Órdenes basadas en la fecha en que se cargó por primera vez la Orden.", "updateTooltipText": "Genere reportes para las Órdenes basados en la fecha en que se actualizó por última vez la Orden.", "approvalTooltipText": "Genere informes basados ​​en la fecha aprobada", "predefinedReports": "Reportes predeterminados", "customizableReports": "Reportes personalizables", "primaryColumnChangedPopupTitle": "Cambio de columna principal", "primaryColumnChangedPopupBody": "Ha cambiado la columna principal de “{{prevColumnName}}” a “{{nextColumnName}}”. Para conservar sus datos seleccionados, la columna principal anterior ({{prevColumnName}}) se ha movido a la primera columna secundaria.", "invalidDates": {"title": "La fecha inicio y la fecha fin deben ser del mismo año", "body1": "Para descargar este reporte se require que la “fecha inicio” y la “fecha final”, sean del mismo año.", "body2": "Por ejemplo: La fecha inicio puede ser “{{from}}” y la fecha final “{{to}}”.", "body3": "Por favor de seleccionar una fecha inicio y una fecha fin válidas.", "close": "<PERSON><PERSON><PERSON>", "dateFormat": "DD [de] MMMM [de] YYYY"}, "emailSentTitle": "Envío de reporte exitoso", "emailSentBody": "Revisa tu correo electrónico en aproximadamente 10 minutos", "largeReport": {"title": "Recibe un informe por correo electrónico", "text": "Este informe contiene una cantidad significativa de datos y llevará tiempo generarlo.\nIngrese su dirección de correo electrónico y le enviaremos el informe por correo electrónico en aproximadamente 10 minutos.", "emailCaption": "Correo electrónico ", "placeholder": "<EMAIL>", "submit": "Enviar", "invalidEmailTitle": "Correo no válido", "invalidEmailBody": "Introduzca un correo válido"}}, "indicators": {"start": "<PERSON><PERSON>o", "end": "Fin", "dateRangeErrorTitle": "Error en rango de fechas", "dateRangeErrorText": "Selecciona una fecha distinta.", "apply": "Aplicar", "filterTitle": "Seleccionar indicadores", "days": "días", "day": "día", "notAvailable": "N/A", "titles": {"AverageTicketResult": "Resultado de ticket promedio", "TotalEstimated": "Total cotizado", "TotalApproved": "Total aprobado", "PercentApprovedOfEstimated": "% Aprobado de lo cotizado", "TotalEstimatedVsTotalApproved": "Total cotizado vs Total aprobado", "RedOrYellowItemsAdded": "Puntos rojos o amarillos agregados", "RedOrYellowItemsEstimated": "Puntos rojos o amarillos cotizados", "RedOrYellowItemsWithoutEstimate": "Puntos rojos o amarillos sin cotización", "RedItemsAdded": "Puntos rojos agregados", "RedItemsEstimated": "Puntos rojos cotizados", "RedItemsWithoutEstimate": "Puntos rojos sin cotización", "YellowItemsAdded": "<PERSON>untos amarillos agregados", "YellowItemsEstimated": "Puntos amarillos cotizados", "YellowItemsWithoutEstimate": "Puntos amarillos sin cotización", "EstimatedItems": "Puntos cotizados", "Productivity": "Productividad", "OrdersCreated": "<PERSON><PERSON><PERSON> c<PERSON>", "OpenOrders": "<PERSON><PERSON><PERSON> abiertas", "ClosedOrders": "<PERSON><PERSON><PERSON>", "OrdersWithCompleteChecklist": "Ó<PERSON><PERSON> con checklist completo", "AverageDwellTime": "Tiempo de permanencia promedio", "OrdersActivity": "Actividad de Órdenes"}, "tooltips": {"AverageTicketResult": "Resultado obtenido del total aprobado por el cliente dividido entre las Órdenes creadas en el rango de fecha ingresado.", "TotalEstimated": "Es el total de lo cotizado en las Órdenes dentro del rango de fechas seleccionado.", "TotalApproved": "Es el total aprobado por un miembro del equipo o el cliente sobre las cotizaciones de las Órdenes en el rango de fechas seleccionado.", "PercentApprovedOfEstimated": "Porcentaje calculado dividiendo el monto total cotizado entre el monto total aprobado en las Órdenes del rango de fecha seleccionado.", "RedOrYellowItemsAdded": "Cantidad de puntos rojos o amarillos agregados en las Órdenes dentro del rango de fechas seleccionado.", "RedOrYellowItemsEstimated": "Cantidad de puntos rojos o amarillos que fueron cotizados en las Órdenes dentro del rango de fechas seleccionado.", "RedOrYellowItemsWithoutEstimate": "Cantidad de puntos rojos o amarillos que fueron agregados en las Órdenes pero no fueron cotizados.", "RedItemsAdded": "Cantidad de puntos rojos agregados en las Órdenes dentro del rango de fechas seleccionado.", "RedItemsEstimated": "Cantidad de puntos rojos que fueron cotizados en las Órdenes dentro del rango de fechas seleccionado.", "RedItemsWithoutEstimate": "Cantidad de puntos rojos que fueron agregados en las Órdenes pero no fueron cotizados.", "YellowItemsAdded": "Cantidad de puntos amarillos agregados en las Órdenes dentro del rango de fechas seleccionado.", "YellowItemsEstimated": "Cantidad de puntos amarillos que fueron cotizados en las Órdenes dentro del rango de fechas seleccionado.", "YellowItemsWithoutEstimate": "Cantidad de puntos amarillos que fueron agregados en las Órdenes pero no fueron cotizados.", "EstimatedItems": "Desglose de los totales aprobados y cotizados según la prioridad del punto de inspección y los miembros del equipo.", "OrdersCreated": "Cantidad de Órdenes creadas en el rango de fecha seleccionado.", "OpenOrders": "Cantidad de Órdenes creadas en el rango de fecha seleccionado que aún permanecen abiertas.", "ClosedOrders": "Cantidad de Órdenes que fueron cerradas en el rango de fecha seleccionado.", "OrdersWithCompleteChecklist": "Cantidad de Órdenes que tiene todos los puntos de inspección con una prioridad seleccionada.", "AverageDwellTime": "Tiempo promedio que permaneció el vehículo en la sucursal."}, "columns": {"TotalEstimatedVsTotalApproved": {"Location": "Sucursal", "TeamMember": "Miembro del equipo", "TotalEstimated": "Total cotizado", "TotalApproved": "Total aprobado", "PercentApprovedEstimated": "% Aprobado de lo cotizado"}, "EstimatedItems": {"Location": "Sucursal", "TeamMember": "Miembro del equipo", "RedItemsEstimated": "Cotizado de puntos rojos", "RedItemsApproved": "Aprobado de puntos rojos", "PercentRedItemsApproved": "% Aprobado de puntos rojos", "YellowItemsEstimated": "Cotizado de puntos amarillos", "YellowItemsApproved": "Aprobado de puntos amarillos", "PercentYellowItemsApproved": "% Aprobado de puntos amarillos"}, "Productivity": {"Location": "Sucursal", "TeamMember": "Miembro del equipo", "AvailableHours": "Horas disponibles", "PresenceHours": "<PERSON><PERSON> presencia", "ProductiveHours": "Horas productivas", "UnproductiveHours": "<PERSON><PERSON> improductivas", "PercentProductivity": "% productividad"}, "OrdersActivity": {"Location": "Sucursal", "TeamMember": "Miembro del equipo", "OpenOrders": "<PERSON><PERSON><PERSON> abiertas", "OrdersSentToCustomer": "Órdenes enviadas al cliente", "OrdersNotSentToCustomer": "Órdenes no enviadas al cliente", "OrdersWithCompleteChecklist": "Ó<PERSON><PERSON> con checklist completo", "OrdersWithoutCompleteChecklist": "Órdenes sin checklist completo"}, "tooltips": {"Productivity": {"AvailableHours": "Horas disponibles es el total de horas que el técnico se encuentra en la sucursal, según su horario laboral. Considerando ausencias registradas.", "PresenceHours": "Horas presencia es el total de horas que el técnico se encuentra en la sucursal, según su horario laboral. Omitiendo ausencias registradas.", "ProductiveHours": "Horas productivas es la suma de horas en las cuales el técnico estuvo trabajando en una Orden.", "UnproductiveHours": "Horas improductivas es la suma de horas en las cuales el técnico no tiene trabajos asignados.", "PercentProductivity": "El porcentaje de productividad se obtiene del cálculo de “Horas productivas” entre “Horas de presencia” por 100."}}}}, "menuPricing": {"header": "Menu pricing", "search": "Búsqueda", "addManualEstimate": "Agregar [Lower:Estimate] manual", "vehicleNotFound": "Vehículo no encontrado", "results": "{{count}} resultados", "noResults": "Sin resultados para \"{{searchText}}\"", "estimateUpdatedTitle": "Se actualizado la [Lower:Estimate]", "estimateUpdatedBody": "Se ha actualizado la [Lower:Estimate] con “Menu pricing”.", "performSearch": "Realiza tu búsqueda para mostrar resultados", "zeroResults": "0 resultados", "jobs": "Trabajos", "parts": "[Title:Parts]", "labor": "[Sentence:Labor]", "selectedCount": "{{selectedCount}} seleccionados", "cancelEstimateModal": {"title": "¿Está seguro de cancelar la selección?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Sí"}}, "cancelModal": {"doYouWantToCancel": "¿Quieres cancelar?", "goBack": "Regresar", "yesCancel": "Si, cancelar"}, "massiveSending": {"massiveSendingsLists": "Listas de envíos masivos", "newMassiveSending": "Nuevo envío masivo", "table": {"sendingName": "NOMBRE DEL ENVÍO", "sendDate": "FECHA DE ENVÍO", "endingDate": "FECHA DE TERMINO", "impactedCustomers": "CLIENTES IMPACTADOS", "status": "ESTATUS", "filterByStatus": "Filtrar por status", "dateFormat": "DD/MM/YYYY", "timeFormat": "HH:mm", "neverEnds": "Nunca termina", "customersWhomMessageSent": "Clientes a los que se les envió el mensaje de WhatsApp", "customersRespondedMassiveSending": "Clientes respondieron al envío masivo", "appointmentScheduled72HoursAfterMassiveSending": "Citas agendadas 72 horas después del envío masivo", "active": "Activo", "finalized": "Finalizado"}, "sendingType": {"_": "Tipo de envío", "placeholders": {"WarrantyCampaign": "Envío único de acuerdo a los números VIN cargados de un archivo Excel.", "Personalized": "Envío único de acuerdo a los filtros personalizados."}, "WarrantyCampaign": "Campaña de garantía (VIN)", "Personalized": "Personalizado"}, "modal": {"sendingRules": "Reglas de envío", "whatsAppTemplate": "Plantilla de WhatsApp", "preview": "Vista preliminar", "newMassiveSending": "Nuevo envío masivo", "editMassiveSending": "Editar envío masivo", "goBack": "Regresar", "cancel": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "uploadAndContinue": "Subir archivo y continuar", "saveChanges": "Guardar cambios", "confirmSending": "Confirmar env<PERSON>", "tabs": {"sendingRules": "Reglas de envío", "whatsAppTemplate": "Plantilla de WhatsApp", "preview": "Vista preliminar"}, "sendingName": "Nombre del envío", "enterTheNameOfYouNewMassiveSending": "Ingresa el nombre de tu nuevo envío masivo", "allCustomers": "Todos los clientes", "dateOfSending": "<PERSON><PERSON>", "timeOfSending": "<PERSON>ra de env<PERSON>", "sendRightNow": "Enviar en este momento", "addImage": "Agregar imagen", "recommendedSize": "<PERSON><PERSON><PERSON> recomendado", "text": "Texto", "imageAndText": "Imagen y texto", "templateType": "Tipo de la plantilla", "templateImage": "Imagen de la plantilla", "notes": "Notas", "thisImageWillBeSentToTheCustomerByWhatsAppMessageAllowedFormatsForTheImage": "Esta imagen se le hará llegar al cliente por medio del mensaje de WhatsApp. Formatos permitidos para la imagen: ", "pngOrJpg": ".png o .jpg", "templateText": "Texto de la plantilla", "hello": "<PERSON><PERSON>, [Nombre de cliente]", "weWriteFrom": {"Personalized": "escribimos de [Nombre de la agencia]", "WarrantyCampaign": "escribimos de [Nombre de la agencia] para notificarle sobre un tema de garantía."}, "template": {"Personalized": "Tenemos una promoción de 15% descuento para su servicio de mantenimiento. ¡Mencione este mensaje al agendar su cita para obtener su descuento!, Puede agendar su cita llamando al (XXX-XXX-XXXX).", "WarrantyCampaign": "<b>Detalles del servicio</b>: Revisión de airbags sin costo, para modelos 2018 a 2020.", "startOfMessage": "Queremos expresar nuestro agradecimiento por la reparación general de su [Marca] [Modelo] [Año].", "endOfMessage": "¡Esperamos verte pronto! Elige una opción.", "mainText": "Tenemos una promoción de 15% descuento para su próximo servicio de mantenimiento. ¡Mencione este mensaje al agendar su cita para obtener su descuento!", "automaticReply": "En un momento nos pondremos en contacto con usted"}, "paragraphs": {"startOfMessageLabel": "<1>Inicio del mensaje</1><2>*</2> <3>(Escriba un saludo inicial)</3>", "startOfMessagePlaceholder": "Escriba el texto con el desea iniciar su mensaje.", "mainTextLabel": "<1>Texto principal</1><2>*</2> <3>(Escriba el mensaje principal)</3>", "mainTextPlaceholder": "Escriba el mensaje principal que desea comunicar.", "endOfMessageLabel": "<1>Final del mensaje</1><2>*</2> <3>(Escriba el texto con el que desea terminar su mensaje)</3>", "endOfMessagePlaceholder": "Escriba la despedida de su mensaje.", "buttonsLabel": "<1>🔽 Presione el botón \"QUIERO QUE ME CONTACTEN\" y nos pondremos en contacto con usted.<br>🔽 Presione el botón \"DETENER PROMOCIONES\" para dejar de recibir estos mensajes.</1>", "buttons": {"iWantToBeConducted": "QUIERO QUE ME CONTACTEN", "stopPromotions": "DETENER PROMOCIONES"}, "automaticReplyLabel": "<1>Respuesta automática después de presionar \"QUIERO QUE ME CONTACTEN\"</1><2>*</2> <3>(Escriba el texto de respuesta automática)</3>", "automaticReplyPlaceholder": "Escriba la respuesta automática que su cliente recibirá después de presionar el botón \"QUIERO QUE ME CONTACTEN\"."}, "paragraphsOptions": {"1paragraph": "1 párrafo personalizable", "3paragraphsAndButton": "3 párrafos personalizables y botón"}, "insertVariablesButton": {"buttonTitle": "Insertar variable", "vehicleInformation": "Información del vehículo", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "plates": "Placas", "customerInformation": "Información del cliente", "name": "Nombre", "lastName": "Apellido"}, "verifyThatAllTheDataOfYourMassiveSendingAreCorrect": "Verifica que todos los datos de tu envío masivo sean los correctos", "nameOfTheSending": "Nombre del envío", "singleSending": "<PERSON><PERSON><PERSON>", "cancelMassiveSending?": "¿Cancelar nuevo envío masivo?", "cancelMassiveSendingEdition?": "¿Cancelar edición de envío masivo?", "weLookForwardToHearingFromYou!": "¡Estamos listos para atenderle!", "theSendingWillBeMadeAtTheEndOfTheMassiveSendingCreation": "El envío se realizará al finalizar la creación del envío masivo", "massiveSendingSuccessfullyCreated": "Envío masivo creado exitosamente", "massiveSendingModified": "Envío masivo modificado", "typeOfCustomersToSend": "Tipo de clientes a enviar", "downloadImpactedCustomersReport": "Descargar reporte de clientes impactados", "duplicate": "Duplicar y crear nuevo envío", "sendingDetails": "Detalle de envío masivo", "lastVisit": {"months_other": "<PERSON><PERSON><PERSON><PERSON> {{count}} meses", "months_one": "<PERSON><PERSON><PERSON>", "allMonths": "Todos los meses", "label": "Última visita", "placeholder": "Selecciona la última visita"}, "vinUpload": {"uploadedTitle": "Archivo cargado", "uploadedText": "Se ha subido el archivo exitosamente", "invalidFile": "Formato no permitido", "fileLimitExceeded": "Ha excedido el límite de peso del archivo", "vinsLimitExceeded": "Ha excedido la cantidad máxima de VINS", "totalMessage": "Total de mensajes a enviar", "tmplUrl": "https://clearmechanic.s3.ca-central-1.amazonaws.com/Content/MassSendings/ImpactedCustomers/Registro de VINs.xlsx"}, "imageSizeError": {"title": "Imagen excede peso máximo", "text": "Peso máximo permitido 800kb"}, "nlWarn": "Los saltos de línea no son permitidos."}, "delete": {"yesDelete": "Sí, eliminar", "title": "¿Eliminar envío masivo?", "body": "Si eliminas el envío masivo, se cancelará su envío."}}, "components": {"dragAndDropFileInput": {"title": "Arrastra o da clic aquí para subir tu archivo", "noFile": "Sin archivo adjunto", "size": "<PERSON><PERSON><PERSON>", "MB": "MB", "KB": "KB", "B": "B"}}, "workshopPlanner": {"advisors": "<PERSON><PERSON><PERSON>", "youMustHaveAtLeastOneServiceAdvisorSelected": "Debes tener por lo menos un [Title:ServiceAdvisor] seleccionado.", "youMustHaveAtLeastOneTechnicianSelected": "Debes tener por lo menos un [Title:Technician] sele<PERSON>onado.", "appointmentConfirmedOrOrderCreated": "Cita confirmada u Orden creada", "customerDidNotArrive": "Cliente no llegó", "ORDER": "ORDEN", "DID_NOT_ARRIVE": "NO LLEGÓ", "APPOINTMENT_CONFIRMED": "CITA CONFIRMADA", "APPOINTMENT_UNCONFIRMED": "SIN CONFIRMAR", "appointment": "Cita", "utLabel": "Unidades de tiempo vendidas", "redHighlightAnnotation": "Orden sin iniciar según la hora programada o con fecha promesa de entrega vencida", "schedulePopup": {"preview": "Vista previa", "changeOrder": "(cambiar <PERSON>)", "changeAppointment": "(cambiar cita)", "jobDescription": "[Sentence:JobDescription]:", "customerName": "Cliente", "subtitle": "Tiempo de inicio de trabajo: {{- date}} a las {{time}} hrs", "orderTitle": "Agregar trabajo a una Orden para el {{userPosition}} {{userName}}", "appointmentTitle": "Agregar trabajo a una cita para el {{userPosition}} {{userName}}", "selectAppointment": "Seleccionar cita", "selectAppointmentPlaceholder": "Buscar por número de cita", "selectOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectOrderPlaceholder": "Buscar por número de Orden", "duration": "Duración programada", "operationCode": "Código de operación", "operationCodePlaceholder": "Ingrese código de operación", "standardTime": "Tiempo estándar", "standardTimePlaceholder": "Ingrese tiempo estándar", "operationCodeDescription": "Descripción de código de operación", "operationCodeDescriptionPlaceholder": "Ingrese descripción de código de operación", "appointmentDateAndTime": "Fecha y hora de cita", "orderDateAndTime": "[Sentence:PromisedDeliveryDateAndTime]", "orderDateAndTimeAt": "[Sentence:PromisedDeliveryDateAndTime]: {{- date}} a las {{time}} hrs", "exceedsDuration": "La duración ingresada supera el horario laboral del miembro del equipo. Ajuste la duración o elija otro horario.", "appointmentCreated": {"title": "Trabajo agregado a la cita", "text": "El trabajo ha sido agregado a la cita #{{appointmentNumber}}"}, "orderCreated": {"title": "Trabajo agregado a la Orden", "text": "El trabajo ha sido agregado a la Orden #{{orderNumber}}"}, "teamMember": "Miembro del equipo", "teamMemberPlaceholder": "Seleccione el miembro del equipo", "planning": "Planeación", "planningPlaceholder": "Seleccione la planeación", "appointmentAltTitle": "Agregar trabajo para cita {{appointment}}", "appointmentSubtitle": "<PERSON><PERSON> y hora de cita: {{- date}} a las {{time}} hrs", "orderAltTitle": "Agregar trabajo para Orden {{order}}", "unassignedHint": "Después de guardar, el trabajo se asignará a <bold>{{user}}</bold>.", "moreInfo": "Más información", "sroValidation": {"success": {"title": "Validación de código de operación", "description": "Validación de código de operación correcta en {{dmsName}}"}, "error": {"title": "No fue posible validar el código de operación en {{dmsName}}", "description": "Mensaje de error: {{errorMessage}}"}}}, "unifiedSearch": {"searchPlaceholder": "Buscar: # <PERSON><PERSON>, # Or<PERSON>, VIN, placas", "noResults": "No encontramos coincidencias para su búsqueda", "results": "Resul<PERSON><PERSON>", "orderNumber": "Orden #", "appointmentNumber": "Cita #", "customer": "Cliente: ", "vehicle": "Vehículo: ", "vin": "VIN: ", "plates": "Placas: ", "phase": "Etapa: "}, "3dot": {"seeOrEditJob": "Ver o editar trabajo", "deleteJob": "Eliminar trabajo", "reassignJob": "Reassignar trabajo", "seeOrEditOrder": "Ver o editar Orden", "startJob": "Iniciar trabajo", "pauseJob": "Pausar trabajo", "stopJob": "Finalizar trabajo", "resumeJob": "<PERSON><PERSON><PERSON> t<PERSON>", "duplicateJob": "Duplicar trabajo"}, "orderPopup": {"noJobsInTimeline": "No hay trabajos agregados hasta el momento.", "quickOrderInfo": "Info rápida de la Orden", "timeline": "Línea de tiempo", "orderDetail": "<PERSON>er <PERSON><PERSON><PERSON> de Orden", "timelineTemplate": "{{user}} — Planeación de {{planning}}", "title": "Orden #{{orderNumber}}", "appointment": "Cita", "reasonForAppointment": "[Sentence:ReasonForVisit]", "customerName": "Nombre", "tower": "Torre", "orderType": "<PERSON><PERSON><PERSON> Orden", "mobile": "<PERSON><PERSON><PERSON>", "plates": "Placas", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deliveryDate": "[Sentence:PromisedDeliveryDateAndTime]", "date": "<PERSON><PERSON> de t<PERSON>ajo", "time": "Hora de trabajo", "timePlaceholder": "Selecciona una hora", "datePlaceholder": "Selecciona una fecha", "duration": "Duración", "scheduleWorkIsOutsideScheduleError": "Revise los datos ingresados.", "technicianNotAssignedError": "La técnica no está asignada a la orden.", "technicianCanNotBeReassigned": "El [Lower:Technician] no puede ser reasignado.", "canNotResumeNow": "No se puede reanudar el pedido porque el [Lower:Technician] está trabajando en otro pedido actualmente.", "startPointIsOutOfSchedule": "El pedido no se puede reanudar fuera de horario.", "orderScheduleOccupiedError": "Esta Orden es trabajada por {{displayName}}.", "durationMustBeGreater": "El valor ingresado debe ser mayor.", "technicianOccupied": {"title": "El [Title:Technician] está trabajando en otra Orden", "text": "No se puede asignar a un [Title:Technician] 2 <PERSON><PERSON><PERSON> al mismo tiempo."}, "phase": "Etapa actual", "orderTimeline": "Historial de etapas", "selectPhasePlaceholder": "Selecciona una Etapa", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "pauseOrder": "<PERSON><PERSON><PERSON>", "orderMustBeAssigned": "Se debe asignar el pedido y llevarlo al trabajo para establecer una pausa", "addJob": "Agregar trabajo a la Orden", "orderDetailPopup": {"title": "Será redireccionado a la pantalla de “Detalle de Orden”", "body": "Se guardarán los cambios que haya realizado en este pop-up y se cerrará al dar clic en el botón “Guardar cambios y continuar”.", "saveChanges": "Guardar cambios y continuar"}, "jobCreatedIntegrationSuccess": {"text": "Trabajo creado correctamente en {{integrationAccountName}}.", "title": "Trabajo c<PERSON>o"}, "jobCreatedIntegrationError": {"text": "Mensaje de error: {{errorMessage}}.", "title": "No fue posible crear el trabajo en {{integrationAccountName}}"}, "setActionIntegrationSuccess": {"text": "Trabajo actualizado correctamente en {{integrationAccountName}}.", "title": "Trabajo actualizado"}, "setActionIntegrationError": {"text": " Error message: {{errorMessage}}", "title": "No fue posible actualizar el trabajo en {{integrationAccountName}}"}}, "planOrderPopup": {"title": "Cita #{{appointmentN<PERSON>ber}}", "appointment": "Cita", "reasonForAppointment": "[Sentence:ReasonForVisit]", "customerName": "Nombre", "firstNamePlaceholder": "Nombre", "lastNamePlaceholder": "Apellido", "mobilePlaceholder": "Ingrese el celular", "platesPlaceholder": "Ingrese las placas", "mobile": "<PERSON><PERSON><PERSON>", "plates": "Placas", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON> de t<PERSON>ajo", "time": "Hora de trabajo", "timePlaceholder": "Selecciona una hora", "datePlaceholder": "Selecciona una fecha", "duration": "Duración", "scheduleWorkIsOutsideScheduleError": "Revise los datos ingresados.", "outsideDaySchedule": {"title": "Revise los datos ingresados.", "text": "La planificación de pedidos debe ajustarse a 1 día laborable"}, "technicianOccupied": {"title": "El [Title:Technician] está trabajando en otra Orden", "text": "No se puede asignar a un [Title:Technician] 2 <PERSON><PERSON><PERSON> al mismo tiempo."}}, "moreJobInfoPopup": {"title": "Más información del trabajo", "order": "Orden #{{orderNumber}}", "teamMember": "Miembro del equipo", "employeeId": "ID de empleado ", "planning": "Planeación", "startDate": "Fecha de inicio", "startHour": "Hora de inicio", "scheduledDuration": "Duración programada", "timeUnitsSold": "Unidades de tiempo vendidas", "jobDescription": "Descripción de trabajo:", "timeline": "Línea de tiempo de trabajo", "realDuration": "Duración real:", "technicianSignature": "Firma del Técnico", "technicianSignaturePlaceholder": "Agregue firma del técnico desde la app móvil"}, "phases": {"phasesTitle": "Etapas", "promiseLabel": "Promesa: ", "noPhase": "Sin etapa", "noPhaseDescription": "Ó<PERSON>es creadas sin etapa asignada", "closedOrder": "<PERSON>den cerrada", "closedOrderDescription": "Órdenes cerradas del día actual", "settings": {"addPhase": "<PERSON><PERSON><PERSON>", "enterPhase": "Ingresar etapa", "notifications": {"updatedPhaseTitle": "Etapa actualizada", "updatedPhaseBody": "Etapa guardada correctamente.", "phaseCreatedTitle": "Etapa creada", "phaseCreatedBody": "Etapa guardada correctamente.", "duplicatePhaseTitle": "Etapa duplicada", "duplicatePhaseBody": "La etapa ya existe.", "tooLongNamePhaseTitle": "El nombre de la fase es demasiado largo", "tooLongNamePhaseBody": "El nombre de la fase debe tener menos de 50 caracteres.", "updatedPhase": "Etapa actualizada", "phaseDeletedSuccessfully": "Etapa eliminada correctamente."}, "deletePopup": {"deletePhaseTitle": "Eliminar etapa", "doYouWantToDeleteThisPhase?": "¿Quiere eliminar esta etapa?", "changesAffectSettings": "Los cambios realizados afectarán las configuraciones realizadas en la notificación “Retroceso de etapa”.", "deletePhase": "Eliminar etapa"}, "reorderPopup": {"title": "¿Está seguro que desea modificar la secuencia de las etapas?", "body": "Los cambios realizados afectarán las configuraciones realizadas en la notificación “Retroceso de etapa”.", "confirm": "Sí, modificar"}}}, "pausedOrders": {"noPausedOrders": "No hay órdenes en pausa.", "paused": "<PERSON><PERSON><PERSON>", "order": "Orden", "orders": "<PERSON><PERSON><PERSON>", "filters": {"filterByPhase": "Filtrar por etapa", "filterByLatestTechnician": "Filtrar por último Técnico", "filterByReasonForPause": "Filtrar por motivo de pausa"}, "tableHeaders": {"tower": "Torre", "order": "Orden", "phase": "Etapa", "latestTechnician": "<PERSON><PERSON><PERSON> [Title:Technician]", "reasonForPause": "Motivo de pausa", "promiseDate": "<PERSON><PERSON> promesa", "daysOnPause": "Días de pausa"}, "days": "días", "day": "día", "reasonsForPause": {"pauseFor": "Pausar por", "assignmentofAnotherVehicle": "Asignación de otro vehículo", "lunch": "<PERSON><PERSON><PERSON>", "waitingForCustomerAuthorization": "En espera de autorización del cliente", "waitingForServiceBay": "En espera de bahía", "waitingForTools": "En espera de herramientas", "waitingForTechnicianReassignment": "En espera de reasignación de [Title:Technician]", "waitingForParts": "En espera de [Lower:Parts] / repuestos", "inWarrantyProcess": "En proceso de garantía", "tot": "TOT", "other": "<PERSON><PERSON>"}, "lightsLegends": {"expiredPausedDays": "Orden pausada por {{days}} días o más", "expiredDeliveryDay": "Orden con fecha promesa de entrega vencida"}}, "convertOrder": {"appointment": "Cita", "serviceAdvisor": "[Title:ServiceAdvisor]", "reasonForAppointment": "[Sentence:ReasonForVisit]", "notes": "Notas", "enterNotes": "Introducir notas", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertToOrder": "Convertir en Orden", "planOrder": "Planear cita", "editPlanning": "Editar planeación", "editOrder": "<PERSON><PERSON>", "addJob": "Agregar trabajo a la cita", "appointmentDetailTab": "Detalle de cita", "timelineTab": "Línea de tiempo"}, "technicians": {"title": "[Title:Technicians]", "absent": "Ausente", "noActivity": "Sin actividad", "legend": {"ByOrderType": "<PERSON><PERSON><PERSON> Orden", "ByReasonForAppointment": "[Sentence:ReasonForVisit]", "ByServiceAdvisor": "<PERSON><PERSON><PERSON>"}}, "add": {"appointment": "Agregar trabajo a la cita", "order": "Agregar trabajo a la Orden", "absence": "Agregar ausencia"}, "deleteBlock": {"yes": "Sí, eliminar", "text": "¿Está seguro de que quiere eliminar este trabajo?", "successNotif": {"text": "Trabajo eliminado correctamente.", "title": "Trabajo eliminado"}, "successIntegrationNotif": {"text": "Trabajo eliminado correctamente en {{integrationAccountName}}.", "title": "Trabajo eliminado"}, "errorIntegrationNotif": {"text": "Mensaje de error: {{errorMessage}}.", "title": "No fue posible eliminar el trabajo en {{integrationAccountName}}"}}, "editBlock": {"startDate": "Fecha de inicio", "startHour": "Hora de inicio", "successNotif": {"text": "Trabajo modificado correctamente.", "title": "Trabajo modificado"}, "outsideOfSchedule": {"text": "No se pueden asignar dos trabajos al mismo tiempo.", "title": "El miembro del equipo tiene otro trabajo asignado"}, "tooLarge": {"text": "El trabajo es demasiado grande.", "title": "El trabajo es demasiado grande y no se puede programar."}, "duplicateOperationCode": {"text": "Operation code is duplicated.", "title": "Por favor, cambie el código de operación."}}, "reassignBlock": {"reassignTo": "Reasignar a", "successNotif": {"text": "El trabajo fue reasignado a {{user}}", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "teamMemberBusyNotif": {"text": "No se pueden asignar dos trabajos al mismo tiempo.", "title": "El miembro del equipo tiene otro trabajo asignado"}}, "phaseChangingBlock": {"text": "¿Desea cambiar la etapa?", "onStart": {"positiveItemText": "<PERSON><PERSON>, iniciar trabajo y cambiar la etapa a \"{{phase}}\"", "negativeItemText": "No, iniciar trabajo y mantener la etapa \"{{phase}}\""}, "onStop": {"positiveItemText": "<PERSON><PERSON>, finalizar trabajo y cambiar la etapa a \"{{phase}}\"", "negativeItemText": "No, finalizar trabajo y mantener la etapa \"{{phase}}\""}}, "stopJobValidationPopup": {"incompleteInformationTitle": "Información incompleta", "pendingSignatureTitle": "Firma pendiente", "body": "Este trabajo no puede finalizarse hasta no tener", "mandatoryFieldNotSpecified": "Los campos del pop-up “Más información del trabajo” completados.", "noStandardOperationSpecified": "Los códigos de operación y los tiempos estándar completos desde el pop-up “Editar trabajo”.", "pendingSignature": "La firma del Técnico agregada desde la app móvil.", "completeInformation": "Completar información", "ok": "Entendido"}, "pauseJobBlock": {"selectPauseReason": "Por favor, seleccione el motivo de pausa"}, "requestPasswordBlock": {"assignToMessage": "El trabajo está asignado a {{name}}", "requestPasswordMessage": "<PERSON> usted es {{name}}, por favor ingrese su contraseña y de clic en en el botón “{{buttonTitle}}”.", "pleaseTryAgain": "Por favor, inté<PERSON>lo de nuevo.", "incorrectPassword": "Contrase<PERSON>", "startJob": "Iniciar trabajo", "pauseJob": "Pausar trabajo", "stopJob": "Finalizar trabajo", "resumeJob": "<PERSON><PERSON><PERSON> t<PERSON>"}, "detailsTooltip": {"orderType": "<PERSON><PERSON><PERSON> Orden", "promiseOfDelivery": "Promesa de entrega", "phase": "Etapa", "duration": "Duración", "serviceAdvisor": "[Title:ServiceAdvisor]", "appointmentTitle": "Cita", "appointmentReason": "[Sentence:ReasonForVisit]", "internalNotes": "Notas internas", "visibleToCustomerNotes": "Notas visibles para el cliente", "status": "<PERSON><PERSON><PERSON>", "appointmentTime": "Hora de la cita", "jobDescription": "Descripción de trabajo"}, "didNotArrive": {"title": "No llegó"}, "indicators": {"capacity": "Capacidad", "utilization": "Utilización"}, "technicianCapacityTooltip": {"inputData": "Datos de entrada", "period": "Periodo", "availableActualTechnicalCapacityTitle": "Capacidad Técnica Real disponible: {{hours}} horas", "availableActualTechnicalCapacity": "Capacidad Técnica Real disponible", "actualTechnicalCapacityUsed": "Capacidad Técnica Real utilizada", "actualTechnicalCapacityTitle": "Capacidad Técnica Real: {{hours}} horas", "actualTechnicalCapacity": "Capacidad Técnica Real", "technicians": "[Title:Technicians]", "availableHours": "Horas disponibles", "assignedHours": "Horas asignadas del periodo", "averageAvailableHours": "<PERSON>ras disponibles promedio por [Title:Technician]", "averageAvailableHoursWithNewLine": "<PERSON>ras disponibles promedio \npor [Title:Technician]", "workDays": "Días laborables", "technicalProductivity": "Productividad Técnica", "prevWeekTechnicalProductivity": "Productividad Técnica de la semana anterior", "prevWeekTechnicalProductivityWithNewLine": "Productividad Técnica \nde la semana anterior", "productiveHours": "Horas productivas", "workedHours": "<PERSON><PERSON> trabajadas", "technicianProductivity": "Productividad Técnica: {{percentage}} %", "completedJobs": "Trabajos completados"}, "technicianCapacityAlert": {"title": "Capacidad Técnica Real alcanzada", "body": "<1><2>La Capacidad Técnica Real ha alcanzado el {{capacity}}%. Se recomienda reservar el {{remain}}% restante para trabajos urgentes o sin cita previa.</2><2>¿Desea continuar agregando trabajos?</2></1>", "cancel": "No, cancelar", "confirm": "Sí, continuar"}, "workshopUtilizationTooltip": {"inputData": "Datos de entrada", "period": "Periodo", "actualTechnicalCapacity": "Capacidad Técnica Real", "averageAvailableHours": "<PERSON>ras disponibles promedio por [Title:Technician]", "workDays": "Días laborables", "potentialCapacity": "Capacidad Potencial", "potentialCapacityTitle": "Capacidad Potencial (Capacidad Instalada): {{hours}} horas", "productiveWorkspaces": "Lugares productivos", "workshopUtilization": "Utilización del Taller", "hours": "horas"}, "invalidSchedule": {"title": "Horario de labores no válido", "text": "Se ha destectado que el horario registrado para este día no es válido, debido a que la hora de cierre es anterior a la hora de apertura.", "settings": "Haga clic <1>aquí</1> para revisar la configuración.", "bugReport": "Si cree que esto es un error, por favor contáctenos."}, "changeJobStatusPopup": {"title": "¿Desea cambiar la etapa?", "body": "El trabajo ha sido {{action}} y la Orden está actualmente en la etapa <bold>\"{{currentPhase}}\"</bold>.", "cancelButton": "No, mantener etapa actual", "continueButton": "Sí, cambiar etapa", "actionStarted": "iniciado", "actionPaused": "pausado", "actionResumed": "reanu<PERSON><PERSON>", "actionStopped": "finalizado"}, "changeJobPhasePopup": {"title": "Por favor, seleccione la etapa:", "dropdownLabel": "Seleccione una etapa", "cancelButton": "Regresar", "continueButton": "Confirmar"}, "closedOrderWarning": {"body1": "La Orden ha sido cerrada por {{userDisplayName}} el {{- formattedDateTime}}.", "body2": "Para realizar cambios, es necesario que la Orden sea re-abierta.", "title": "<PERSON>den cerrada", "button": "Entendido"}}, "towerAlert": {"towerInUse": "Torre en uso", "theTowerNumberIsBeingUsedByOrder": "El número de torre está siendo utilizado por la Orden"}, "syncEstimate": {"syncEstimate": "Sincronizar [Lower:Estimate]", "getEstimateFromYourIntegratedSoftware": "Obtener [Lower:Estimate] de tu software integrado", "uploadEstimateToYourIntegratedSoftware": "<PERSON><PERSON> [Lower:Estimate] a tu software integrado", "estimateSuccessfullySynchronized": "[Title:Estimate] sincronizada correctamente.", "theEstimateWasNotSuccessfullySynchronized": "La [Lower:Estimate] no fue sincronizada correctamente", "obtainEstimateFrom": "Obtener cotización de {{integratedAccountName}}", "uploadEstimateTo": "Cargar cotización a {{integratedAccountName}}", "obtainEstimateFrom3rdPartySoftware": "Obtener cotización del software tercero", "uploadEstimateTo3rdPartySoftware": "Cargar cotización al software tercero", "estimateSynchronized": {"success": {"title": "[Title:Estimate] sincronizada correctamente.", "description": "[Title:Estimate] sincronizada correctamente con {{integratedAccountName}}."}, "error": {"title": "La [Lower:Estimate] no fue sincronizada correctamente con {{integratedAccountName}}.", "description": "Mensaje de error de {{integratedAccountName}}"}}}, "enterprise": {"appointments": {"new": {"outsideLocationSchedule": {"title": "La cita no puede ser agendada", "text": "La hora seleccionada está fuera del horario de trabajo de la sucursal"}}}}, "viewByCost": {"tabs": {"estimate": "[Title:ApproveEstimate]", "mobileEstimate": "[Title:Estimate]", "phase": "[Title:VehiclePhase]", "mobilePhase": "Etapa", "generalInfo": "Información general", "mobileGeneralInfo": "Inf. General"}, "common": {"documentTitle": "Orden #", "greeting": "¡Hola! ", "urgent": "Urgente", "medium": "Sugerido", "ok": "Ok", "options": "Opciones", "es-MX": "Español", "en-US": "English", "viewPdfOfTheOrder": "Ver PDF de la Orden"}, "estimate": {"notes": "Notas sobre costos", "approve": "<PERSON><PERSON><PERSON>", "approved": "Aprobado", "text1": "Seleccione trabajos y puntos para aprobar la", "text2": "[Title:Estimate], y agilizar la entrega de su vehículo.", "approveTitle": "Total de la [Lower:Estimate]", "approveTitle2": "Confirme su aprobación", "discount": "Descuentos:", "subtotal": "Subtotal:", "taxes": "Impuestos:", "total": "Total:", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "pay": "<PERSON><PERSON>", "approvedMessage": "<PERSON><PERSON><PERSON>, hemos informado su aprobación.", "approvedMessageDescription": "Para su comodidad, realizar el pago ahora agilizará el proceso y le permitirá evitar esperas en el establecimiento.", "approvedMessageQuestion": "¿Desea proceder con el pago en este momento?", "paymentProcessedTitle": "<PERSON><PERSON><PERSON>, su solicitud de pago está en proceso.", "paymentProcessedDescription": "Recibirá un WhatsApp de confirmación una vez que se complete el pago. Le recomendamos que guarde el comprobante o tome una captura de pantalla como respaldo adicional.", "paymentWhatsappTitle": "G<PERSON><PERSON>, su aprobación ha sido registrada.", "paymentWhatsappDescription": "<PERSON><PERSON>e pagar en cualquier momento con el enlace enviado por WhatsApp.", "nowButton": "<PERSON><PERSON> ahora", "laterButton": "Pagar más tarde", "closeButton": "<PERSON><PERSON><PERSON>", "available": "Disponible", "yes": "Sí", "no": "No", "inventoryComments": "Comentarios del inventario", "PDF": "Ver PDF de la Orden", "closedOrderWarning": {"title": "<PERSON>den cerrada", "body1": "La Orden está cerrada. Puede revisar toda la información relacionada, pero no es posible aprobar puntos de la cotización.", "body2": "Le sugerimos contacte a su Asesor de Servicio, para resolver dudas.", "button": "Entendido"}}, "tableHeaders": {"all": "Todos", "jobsAndInspectionItems": "Trabajos y puntos de inspección", "estimate": "[Title:Estimate]"}, "modalEvidences": {"of": "de", "evidences": "evidencias", "videoEvidence": "Evidencia de video"}, "modalComments": {"notes": "Notas"}, "generalInfo": {"orderInformation": "Información de la Orden", "generalLocationInfo": "Información de la Sucursal", "customerInfo": "Información del Cliente", "vehicleInfo": "Información del Vehículo", "name": "Nombre", "email": "<PERSON><PERSON><PERSON>", "mobile": "<PERSON><PERSON><PERSON>", "landline": "Teléfono", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mileage": "[Title:Mileage]", "vin": "VIN", "plates": "Placas", "businessInformation": "Información del Negocio", "address": "Dirección", "rfc": "RFC", "phone": "Teléfono", "teamMemberInCharge": "[Title:ServiceAdvisor]", "serviceHours": "[Default:ServiceHours]", "mondayFriday": "Lunes - Viernes", "saturday": "Sábado"}, "phase": {"deliveryDate": "Fecha estimada de entrega:", "hrs": "hrs."}}, "absences": {"block": {"allUsers": "Todos", "allDay": "Todo el día"}, "createPopup": {"title": "Ausencia", "personWhoSchedules": "Persona que agenda ausencia: {{user}}", "teamMemberLabel": "Miembro del equipo", "reasonLabel": "Motivo de ausencia", "reasonPlaceholder": "Ingrese motivo de ausencia", "allDayLabel": "Todo el día", "startDateLabel": "Fecha de inicio", "endDateLabel": "<PERSON><PERSON> de fin", "dateLabel": "<PERSON><PERSON>", "startTimeLabel": "Hora de inicio", "endTimeLabel": "Hora de fin", "notesLabel": "Notas", "notesPlaceholder": "Ingrese notas", "cancelButton": "<PERSON><PERSON><PERSON>", "createButton": "<PERSON><PERSON><PERSON> ausencia", "createdAbsencesTitle": "<PERSON><PERSON><PERSON>", "createdAbsencesBody": "Ausencia agendada por {{reason}}", "selectTeamMember": "Seleccione miembro del equipo"}, "editPopup": {"title": "Detalle de ausencia", "personWhoSchedules": "Persona que agendó ausencia: {{user}}", "teamMemberLabel": "Miembro del equipo", "reasonLabel": "Motivo de ausencia", "reasonPlaceholder": "Ingrese motivo de ausencia", "allDayLabel": "Todo el día", "startDateLabel": "Fecha de inicio", "endDateLabel": "<PERSON><PERSON> de fin", "dateLabel": "<PERSON><PERSON>", "startTimeLabel": "Hora de inicio", "endTimeLabel": "Hora de fin", "notesLabel": "Notas", "notesPlaceholder": "Ingrese notas", "cancelButton": "<PERSON><PERSON><PERSON>", "saveButton": "Guardar cambios", "updatedAbsencesTitle": "Ausencia actualizada", "updatedAbsencesBody": "La ausencia se ha sido actualizado correctamente.", "selectTeamMember": "Seleccione miembro del equipo"}, "deleteModal": {"title": "Eliminar ausencia", "body": "¿Quiere eliminar esta ausencia?", "cancelButton": "No eliminar", "confirmButton": "Eliminar ausencia", "notificationTitle": "Ausencia eliminada", "notificationBody": " La ausencia ha sido eliminada correctamente."}, "continueWithSchedule": {"title": "¿Continuar con la ausencia agendada?", "body": "Hay <1>{{count}}</1> citas u Órdenes programadas que este miembro del equipo no podría atender si agenda la ausencia.<2></2><2></2>Le sugerimos las mueva a otro horario o miembro del equipo, luego intente agendar la ausencia nuevamente.", "cancelButton": "<PERSON><PERSON><PERSON>", "yesCancel": "<PERSON><PERSON>, cancelar", "goBack": "Regresar", "cancelText": "¿Desea cancelar el registro de ausencia?", "cancelEditText": "¿Desea cancelar la edición de ausencia?", "saveButton": "Guardar cambios"}, "blockOverlapAbsenceErrorTitle": "<PERSON><PERSON><PERSON>", "blockOverlapAbsenceErrorBody": "Miembro del equipo no disponible en este horario."}, "customAppointmentReasonsForms": {"activate": {"title": "Activar [Lower:ReasonsForVisit] personalizables", "description": "Personaliza los [Lower:ReasonsForVisit] de tu negocio."}, "form": {"newTitle": "Nuevo [Lower:ReasonForVisit]", "editTitle": "Editar [Lower:ReasonForVisit]", "newButton": "<PERSON><PERSON><PERSON> [Lower:ReasonForVisit]", "editButton": "Guardar cambios", "nameCaption": "Nombre del [Sentence:ReasonForVisit]", "namePlaceholder": "Ingrese nombre del [Lower:ReasonForVisit]", "colorCaption": "Color de [Sentence:ReasonForVisit]", "colorPlaceholder": "Seleccione color de [Lower:ReasonForVisit]", "cancelNewTitle": "¿Cancelar creación de [Lower:ReasonForVisit]?", "cancelEditTitle": "¿Cancelar edición de [Lower:ReasonForVisit]?"}, "appointmentReasons": {"addButton": "Agregar nuevo [Lower:ReasonForVisit]", "notifications": {"createSuccessTitle": "[Sentence:ReasonForVisit] creado", "updateSuccessTitle": "[Sentence:ReasonForVisit] actualizado", "errorTitle": "[Sentence:ReasonForVisit] existente", "settingsSuccessTitle": "Configuraciones actualizadas"}, "delete": {"title": "¿Eliminar [Lower:ReasonForVisit] y sus detalles?", "description": "Esta acción es irreversible.", "cancel": "Regresar", "confirm": "Sí, eliminar"}}, "details": {"addButton": "<PERSON><PERSON><PERSON> de<PERSON><PERSON> de [Lower:ReasonForVisit]", "importButton": "Importar detalle de [Lower:ReasonForVisit]", "form": {"title": "Nuevo detalle de [Lower:ReasonForVisit]", "newReason": "Nombre de detalle de [Lower:ReasonForVisit]", "newReasonPlaceholder": "Seleccione o agregue detalle de [Lower:ReasonForVisit]", "brand": "<PERSON><PERSON>", "brandPlaceholder": "Seleccione la marca", "model": "<PERSON><PERSON>", "modelPlaceholder": "Seleccione el modelo", "year": "<PERSON><PERSON>", "yearPlaceholder": "Seleccione el año", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "selectAllBrands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allBrandsSelected": "Todas las marcas", "allModelsSelected": "Todos los modelos", "allYearsSelected": "Todos los años", "allSelected": "Todos seleccionados", "missingBrand": "Seleccione al menos una marca para ver los modelos", "modalTitle": "Detalle de [Lower:ReasonForVisit]", "modalDescription": "Lista de todos los detalles de [Lower:ReasonForVisit] exitentes", "editTitle": "Editar de<PERSON>le de [Lower:ReasonForVisit]", "editButton": "Guardar cambios", "cancelTitle": "¿Cancelar creación de detalle de [Lower:ReasonForVisit]?", "cancelConfirm": "<PERSON><PERSON>, cancelar", "goBack": "Regresar"}, "notifications": {"createSuccessTitle": "Detalle de [Lower:ReasonForVisit] creado", "missingName": "El nombre del detalle de [Lower:ReasonForVisit] es requerido", "errorTitle": "Detalle de [Lower:ReasonForVisit] ya existe", "updateSuccessTitle": "Detalle de [Lower:ReasonForVisit] actualizado", "updateErrorTitle": "Error al actualizar detalle de [Lower:ReasonForVisit]"}, "name": "Detalle de [Lower:ReasonForVisit]", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "allBrands": "<PERSON><PERSON>", "allDefault": "Todos", "deleteTitle": "¿Eliminar detalle de [Lower:ReasonForVisit]?"}}, "siteForAppointments": {"common": {"mandatoryField": "Campo obligatorio"}, "step1": {"title": "PASO 1", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> y [Sentence:ReasonForVisit]", "info": "Campos que aparecen en el PASO 1 al agendar una cita en línea.", "brand": "<PERSON><PERSON>", "selectBrandsTitle": "Seleccione las marcas con las que su cliente podrá agendar citas"}, "step2": {"title": "PASO 2", "description": "Horario y Cliente", "info": "Campos que aparecen en el PASO 2 al agendar una cita en línea.", "schedulesInfo": "La personalización de horarios la puede realizar desde Menú lateral -> Configuraciones -> “Generales” -> “Miembros del equipo” -> “Horario laboral”.  Puede personalizar el horario de recepción de citas activando la función “Activar horario de recepción de citas”."}, "step3": {"title": "PASO 3", "description": "Confirmación", "info": "Elija qué información aparece en el PASO 3 al agendar una cita en línea.", "showCancellationPolicy": "Mostrar políticas de cancelación", "showPrivacyNotice": "Mostrar aviso de privacidad", "privacyNoticeText": "La personalización del aviso de privacidad la puede realizar desde Menú lateral -> Configuraciones -> “ClearMechanic” -> “Generales”. Puede personalizar el encabezado del aviso de privacidad así como el texto del aviso de privacidad.", "clear": {"clear": "Bo<PERSON>r", "title": "Borrar políticas de cancelación", "text": "¿Estás seguro de eliminar las polícitas de cancelación?\nEsto anulará cualquier personalización realizada."}}, "types": {"Dropdown": "Selector desplegable", "FreeText": "Texto libre", "PhoneNumber": "Número telefónico", "Email": "Email"}, "general": {"title": "Generales", "minimumTime": "Tiempo mínimo para agendar cita", "selectLeadTime": "Seleccione tiempo de antelación", "hour": "<PERSON><PERSON>", "hours": "<PERSON><PERSON>", "day": "Día", "days": "Días", "week": "Se<PERSON>", "weeks": "Semanas"}}, "phaseSetbackPopup": {"title": "Escriba el motivo del retroceso de etapa", "placeholder": "Escriba aquí el motivo del retroceso"}, "jobsInProgressPopup": {"title": "Trabajos en progreso", "bodyChangePhase": "La Orden #{{orderNumber}} tiene trabajos en progreso. Puede cambiar la etapa de la Orden, pero primero le sugerimos finalizar estos trabajos.", "bodyClose": "Detectamos trabajos en progreso en la Orden #{{orderNumber}}. Para cerrar la Orden, es necesario finalizar los trabajos.", "scheduledJob": "Trabajo {{index}}<br/>Hora de inicio programado: {{time}}", "dateFormat": "DD MMM - HH:mm", "noEditJobsPermission": "No tienes derechos para editar trabajos", "finishAndChangePhase": "Finalizar y cambiar etapa", "finishAndClose": "Finalizarlos y cerrar ", "continueAnyway": "Continuar sin finalizar", "mandatoryFieldNotSpecified": "Los campos del pop-up “Más información del trabajo” no estan completados.", "noStandardOperationSpecified": "Los códigos de operación y los tiempos estándar no se completos desde el pop-up “Editar trabajo”.", "pendingSignature": "La firma del Técnico no se agregada desde la app móvil."}, "orderTypePicker": {"itWillAppearForOrders": "Aparecerá para las Órdenes:", "allOrderTypes": "Todos los tipos de Órdenes"}, "afterSalesCrm": {"vehicleDetails": {"contactDetails": {"placeholder": {"name": "Ingrese nombre", "lastName": "Ingrese apellidos", "phone": "Ingrese teléfono", "mobile": "Ingrese celular", "email": "Ingrese correo electrónico", "businessName": "Ingrese razón social", "taxId": "Ingrese [Lower:TaxIdentification]", "taxEmail": "Ingrese correo fiscal", "street": "Ingrese calle y número interior", "neighborhood": "Ingrese colonia", "municipality": "Ingrese municipio o alcaldía", "zipCode": "Ingrese C.P.", "city": "Ingrese ciudad", "state": "Ingrese estado", "selectYesOrNo": "Seleccione sí o no"}, "generalInfo": "Información general", "name": "Nombre", "phone": "Teléfono", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "mobile": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "associateContactWith": "Contacto asociado con", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "businessName": "Razón social", "taxId": "[Title:TaxIdentification]", "street": "Calle y número interior", "neighborhood": "Colonia", "municipality": "Municipio o alcaldía", "zipCode": "C.P.", "city": "Ciudad", "state": "Estado", "taxEmail": "Correo fiscal", "addContact": "Agregar contacto", "contactAlreadyAssociated": "Contacto ya asociado al vehículo.", "noContactsFound": "No se encontraron contactos", "tryAgainOrFillFields": "Intente nuevamente o complete los campos para crear uno.", "searchContactPlaceholder": "Buscar por nombre, celular o placa de otro vehículo", "successTitle": "<PERSON><PERSON>", "successMessage": "Contacto a<PERSON> exitosamente", "errorTitle": "Error", "errorMessage": "Error. <PERSON><PERSON> favor, inténtelo de nuevo más tarde.", "errorTitleAlreadyExist": "Celular ya registrado", "errorMessageAlreadyExist": "Registrado a: {{name}}", "errorTitleLocalAlreadyExist": "Contacto ya asociado", "errorMessageLocalAlreadyExist": "Este número celular ya está asociado a este vehículo.", "firstOwner": "Primer propietario", "contactAssociateWith": "Contacto asociado con", "editContact": "<PERSON><PERSON>", "saveChanges": "Guardar cambios", "updateSuccessTitle": "<PERSON>o editado", "updateSuccessBody": "Contacto editado exitosamente", "clear": "Cambiar", "update": "Actualizar", "establishAsOwner": "Establecer como propietario del vehículo", "willBeAssociatedAsOwnerOf": "será asociado como propietario del vehículo", "removeSuccessTitle": "Asociación eliminada", "removeSuccessMessage": "Contacto desvinculado del vehículo", "remove": "Eliminar asociación", "confirmationPlaceholder": "Indique la razón para desasociar este contacto. Debe tener al menos 5 caracteres y no puede contener solo símbolos o espacios.", "willBeUnlinkedFrom": "ya no será un contacto asociado al", "removeAssociationTitle": "Eliminar asociación con este vehículo", "removeLabel": "Motivo de desasociación:", "reasonForDisassociation": "Motivo de desasociación:", "ownerSuccessTitle": "Cambios guardados", "ownerSuccessMessage": "Contacto asociado como propietario"}, "notFound": "Vehículo no encontrado", "notFoundDescription": "El vehículo que solicitó no existe en su taller. Si cree que se trata de un error, por favor contacte al equipo de soporte.", "title": "Detalle de vehículo", "tabs": {"activities": "Actividades", "whatsapp": "WhatsApp", "orders": "<PERSON><PERSON><PERSON>", "activityLog": "Bitácora"}, "contacts": "Contactos", "owner": "Propietario", "contact": "Contacto", "callContact": "Llamar a este contacto", "copyEmail": "<PERSON><PERSON><PERSON> co<PERSON>o", "emailCopied": "¡Correo copiado!", "firstOwner": "Primer propietario", "billingInformation": "Datos de facturación", "noAssociatedContacts": "Este vehículo no tiene contactos registrados.", "createOrAssociateContact": "¡<PERSON>ree o asocie un contacto!", "removeAssociation": "Eliminar asociación con este vehículo", "setAsOwner": "Establecer como propietario del vehículo", "fallbackFromCacheWarning": "No fue posible obtener la información más reciente. Mostrando datos previamente guardados.", "sections": {"generalInfo": "Info general", "vehicleInfo": "Info del vehículo", "salesAndServiceInfo": "Info de ventas y servicio"}, "vinStatus": {"Inactive": "Inactivo", "Active": "Activo", "ActivatedByAnother": "Activado por otro", "Recent": "Reciente"}, "fields": {"assignedBdcAdvisor": "Asesor BDC asignado", "assignedBdcAdvisorPlaceholder": "Seleccione un Asesor BDC", "customerStatus": "Estatus del cliente", "customerStatusPlaceholder": "Ningún estado seleccionado", "vinStatus": "Estatus del VIN", "customerType": "Tipo de cliente", "prospectionPriority": "Prioridad de prospección", "retentionBand": "Franja de retención", "pendingCampaigns": "Campa<PERSON><PERSON> pendientes (recalls)", "lastActivity": "Última actividad", "lastActivityPlaceholder": "No se han realizado actividades", "lastActivityDate": "Fecha de última actividad", "monthsDaysSinceLastActivity": "Meses/días desde última actividad", "nextActivityDate": "Fecha de próxima actividad", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "plates": "Placas", "platesPlaceholder": "Ingrese placas", "platesAlreadyRegistered": {"title": "Placas ya registradas", "text": "Estas placas pertenecen a otro vehículo. Verifique la información."}, "vin": "VIN", "vinPlaceholder": "Ingrese VIN", "vinAlreadyRegistered": {"title": "VIN ya registrado", "text": "Este VIN pertenece a otro vehículo. Verifique la información."}, "mileage": "[Title:Mileage]", "saleDate": "<PERSON><PERSON>", "monthsFromDateOfSale": "Meses desde la fecha de venta", "lastServiceDate": "<PERSON><PERSON> de último servicio", "monthsSinceLastService": "Meses desde último servicio", "lastServiceDateWithMe": "<PERSON><PERSON> de último servicio conmigo", "monthsSinceLastServiceWithMe": "Meses desde el último servicio conmigo", "lastServiceDateWithAnother": "<PERSON><PERSON> de último servicio con otro", "monthsSinceLastServiceWithAnother": "Meses desde el último servicio con otro", "usageType": "Tipo de uso", "usageTypePlaceholder": "Seleccione un tipo de uso", "recommendedServiceFrequency": "Frecuencia de servicio recomendado", "recommendedServiceFrequencyPlaceholder": "Seleccione una frecuencia de servicio", "soldByDealer": "Vendido por distribuidor", "soldByDealerPlaceholder": "Seleccione una opción", "bacThatSold": "BAC que vendió (# sucursal)", "inPma": "En PMA (dentro de zona geográfica)"}, "customerType": {"Fleet": "Flotilla", "Public": "Público"}, "usageType": {"Personal": "Personal", "Commercial": "Comercial", "Rideshare": "Plataforma"}, "readonlyField": {"header": "Este campo no es editable", "customerStatus": "Se actualiza automáticamente con base en la última actividad o cita.", "vinStatus": "Se calcula por el CRM con base en el VIN, la fecha de venta y el historial de servicio.", "customerType": "Se obtiene desde el DMS.", "prospectionPriority": "Se calcula por el CRM con base en la fecha de venta e historial de servicio.", "retentionBand": "Se calcula por el CRM con base en el tiempo desde la venta del vehículo.", "pendingCampaigns": "Se obtiene desde el DMS.", "lastActivity": "Se actualiza automáticamente desde la pestaña “Actividades”.", "lastActivityDate": "Se llena automáticamente con la fecha de la última actividad.", "monthsDaysSinceLastActivity": "Se calcula con base en la fecha de la última actividad.", "nextActivityDate": "Se llena automáticamente con la próxima tarea o cita programada.", "monthsFromDateOfSale": "Se calcula con base en la fecha de venta.", "lastServiceDate": "Muestra la fecha más reciente entre “servicio conmigo” y “con otro taller”.", "monthsSinceLastService": "Se calcula con base en la fecha del último servicio.", "monthsSinceLastServiceWithMe": "Se calcula con base en el último servicio en tu agencia.", "monthsSinceLastServiceWithAnother": "Se calcula con base en el último servicio en otra agencia.", "bacThatSold": "Se obtiene del DMS o archivo importado.", "inPma": "Se obtiene del DMS o archivo importado."}}, "columns": {"vin": "VIN", "plates": "Placas", "brand": "<PERSON><PERSON>", "model": "<PERSON><PERSON>", "year": "<PERSON><PERSON>", "actions": "Acciones", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contacts": "Contacto", "activity": "Actividad", "nextActivity": "Próxima actividad", "priority": "Prioridad", "service": "<PERSON><PERSON><PERSON>", "bdcAdvisor": "<PERSON><PERSON>or B<PERSON>", "soldBy": "Vendido por", "phone": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "owner": "Propietario", "contact": "Contacto", "lastServiceDateWithOther": "<PERSON><PERSON> de último servicio con otro", "nextService": "Próximo servicio", "lastAtOtherDealership": "Último en otro distribuidor", "lastAtMyDealership": "Último en mi distribuidor", "monthsSinceServiced": "<PERSON><PERSON><PERSON> servicio hace {{months}} meses", "monthsSinceSold": "Vendido hace {{months}} meses", "recommendedService": "Last at my dealership", "call": "Llamada", "whatsapp": "WhatsApp", "whatsappTooltip": "Enviar WhatsApp", "note": "<PERSON>a", "noteTooltip": "<PERSON><PERSON><PERSON> nota", "task": "Tarea", "taskTooltip": "<PERSON><PERSON><PERSON> tarea", "appt": "Cita", "mileage": "Kilometraje", "lastActivity": "Última actividad", "lastActivityDate": "Fecha de última actividad", "monthsDaysSinceLastActivity": "Meses/Días desde última actividad", "dateOfNextActivity": "Fecha de próxima actividad", "recommendedServiceFrequency": "Frecuencia de servicio recomendado", "assignedBdcAdvisor": "Asesor BDC asignado", "pendingCampaigns": "Campañas pendientes (Recalls)", "neighborhood": "Colonia", "districtOrMunicipality": "Alcaldía o municipio", "zipCode": "C.P.", "city": "Ciudad", "state": "Estado", "vinStatus": "Estatus del VIN", "prospectionPriority": "Prioridad de prospección", "retentionBand": "Franja de retención", "soldByDealer": "Vendido por distribuidor", "bacThatSold": "BAC que vendió (# sucursal)", "inPma": "En PMA", "saleDate": "<PERSON><PERSON>", "monthsFromSaleDate": "Meses desde la fecha de venta", "lastServiceDate": "<PERSON><PERSON> de último servicio", "monthsSinceLastService": "Meses desde último servicio", "lastServiceDateWithMe": "<PERSON><PERSON> de último servicio conmigo", "monthsSinceLastServiceWithMe": "Meses desde último servicio conmigo", "lastServiceDateWithAnother": "<PERSON><PERSON> de último servicio con otro", "monthsSinceLastServiceWithAnother": "Meses desde último servicio con otro", "customerType": "Tipo de cliente", "usageType": "Tipo de uso", "customerStatus": "Estatus del cliente", "isFirstOwner": "Primer propietario"}, "values": {"noPriority": "Sin prioridad", "Inactive": "Inactivo", "Active": "Activo", "ActivatedByAnother": "Activado por otro", "Recent": "Reciente", "EmptyVin": "VIN vacío", "Fleet": "<PERSON><PERSON><PERSON>", "Public": "Público", "Platform": "Plataforma", "Personal": "Personal", "Commercial": "Comercial", "Rideshare": "Plataforma", "SixThousand": "Seis mil", "TwelveThousand": "Doce mil", "WithoutContact": "<PERSON>", "Unreachable": "Inalcanzable", "Contacted": "Contactado", "ScheduledAppointment": "Cita programada", "CompletedAppointment": "<PERSON>ita completada", "NoShow": "No asistió", "CanceledAppointment": "Cita cancelada", "NoAnswer": "Sin respuesta", "NoPhoneWhatsAppOrEmail": "Sin teléfono, WhatsApp o correo electrónico", "IncorrectNumberDifferentPerson": "<PERSON><PERSON><PERSON><PERSON>, otra persona", "CustomerDeceased": "Cliente fallecido", "CustomerNoLongerHasVehicle": "El cliente ya no tiene el vehículo", "VehicleTotalLoss": "Pérdida total del vehículo", "CourtesyCall": "Llamada de cortesía", "InformationalContact": "Contacto informativo", "CompletedService": "<PERSON><PERSON><PERSON> completado", "ComplaintOrBadExperience": "Queja o mala experiencia", "NotDueForServiceYet": "Aún no es tiempo de servicio", "NotInterestedDueToPrice": "No interesado por el precio", "NotInterestedOtherReason": "No interesado (otra razón)", "NotInterestedDueToTime": "No interesado por falta de tiempo", "NotWithinPMA": "Fuera de la PMA", "InterestedInFollowUp": "Interesado en seguimiento", "WillCallBack": "Llamará de vuelta", "DoesNotWishToBeContacted": "No desea ser contactado", "Other": "<PERSON><PERSON>", "ProactiveAppointmentScheduled": "Cita proactiva programada", "ReactiveAppointmentScheduled": "Cita reactiva programada", "AppointmentConfirmation": "Confirmación de cita", "RescheduledAppointment": "<PERSON>ita reprogramada", "VehicleReadyNotification": "Notificación de vehículo listo", "ThirdDayFollowUpCall": "Llamada de seguimiento del tercer día", "NoShowReschedulingNoContact": "Reprogramación por inasistencia, sin contacto", "RequestsToCancelAppointment": "Solicita cancelar la cita", "Yes": "Sí", "No": "No", "recommendedServiceFrequency": "Cada {{km}}km o cada {{months}} meses"}, "searchBar": {"placeholder": "Buscar: VIN, placas", "placeholderVehicles": "Buscar: VIN, placas, cliente, celular", "vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plate": "Placas", "vin": "VIN", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contact": "Contacto", "owner": "Propietario", "noResults": "No se encontraron coincidencias para la búsqueda realizada."}, "filterMenu": {"all": "Todo", "clear": "Limpiar", "Retention": "Retención", "FollowUp": "Segu<PERSON><PERSON><PERSON>", "Vehicle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GeographicLocation": "Ubicación geográfica", "allColumns": "Todas las columnas", "columnsTitle": "Columnas"}, "filters": {"filters": "<PERSON><PERSON><PERSON>", "filtersByVehicle": "Filtrar por vehículo", "selectFilters": "Seleccione filtros", "Personal": "Personal", "Commercial": "Comercial", "Rideshare": "Plataforma", "allVinStatus": "Todos los estatus", "allProspectionPriority": "Todas las prioridades", "allUsageType": "Todos los tipos de uso", "allCustomerStatus": "Todos los estatus de clientes", "allBrand": "Todas las marcas", "allModel": "Todos los modelos", "allYear": "Todos", "allAssignedBdcAdvisor": "Todos los asesores BDC", "allLastActivity": "Todas las actividades", "all": "Todos", "noBdcAdvisorsFound": "Sin coincidencias. Pruebe otro miembro del equipo o revise la ortografía.", "noYearsFound": "Sin coincidencias. <PERSON>rue<PERSON> otro año o revise la ortografía.", "noModelsFound": "Sin coincidencias. Pruebe otro modelo o revise la ortografía.", "noBrandsFound": "Sin coincidencias. Pruebe otra marca o revise la ortografía."}, "totalVehiclesResult": "Resultado: ", "totalVehicles": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "newVehicle": "Nuevo vehículo", "noVehiclesFoundTitle": "Aún no hay vehículos registrados en esta cuenta", "noVehiclesFoundText": "Cree un vehículo nuevo para comenzar a gestionar su base de datos.", "noMatchesFound": "No se encontraron coincidencias", "callButton": {"hint": "<PERSON><PERSON> llamada telefónica", "call": "Llamada {{number}}"}, "appointmentButton": {"hint": "Crear cita"}, "viewDetailsButton": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "tooltip": "Ver detalles del vehículo"}, "vehicleDetailsCell": {"mileage": "Kilometraje", "usageType": "Tipo de uso", "lastOrder": "Última Orden"}}}