{"dateFormats": {"long": "DD/MM/YY hh:mm a", "short": "MM/dd/yy", "dayOfMonth": "DD MMM", "timeOfToday": "hh:mm a", "timeOfTodayNoZero": "h:mm a", "longTimeAgo": "MMMM DD[,] YYYY[,] hh:mm a", "shortISO": "YYYY-MM-DD", "date": "DD MMM YY", "longDate": "MMMM DD[,] YYYY", "longDateWithDay": "dddd[,] MMMM D YYYY", "shortDateWithDay": "dddd[,] MMMM Do", "longDateWithDayLuxon": "cccc',' <PERSON><PERSON><PERSON> d yyyy", "shortDateWithDayLuxon": "cccc',' MMMM d", "midDate": "MMM/DD/YYYY", "navigationDate": "MMMM Do", "longEs": "DD/MM/YY HH:mm", "longEn": "MM/DD/YY HH:mm", "shortDate": "MM/DD/YY", "time": "hh:mma", "luxon": {"shortRightSlashDivider": "MM/dd/yyyy"}}, "search": {"startTyping": "Start typing...", "nothingFound": "Nothing found", "apply": "Apply"}, "richTextEditor": {"multiple": "Multiple", "p": "Paragraph", "h": "Heading {{level}}", "url": "URL", "urlPlaceholder": "Enter URL here", "insertLink": "Insert link", "editLink": "Edit link", "removeLink": "Remove link", "newLink": "Open in new tab", "sourceCodeTitle": "Edit email from code"}, "titles": {"appointments": "Appointments", "analysis": "Analysis", "workshop": "Workshop", "bdcIndicators": "BDC Indicators", "aftersalesCrm": "Aftersales CRM", "vehicles": "Vehicles", "tasks": "Tasks", "vehicleDatabase": "Vehicle database", "workshopPlanner": "Workshop Planner", "orders": "Orders", "inspectionItems": "Inspection Items", "followUp": "Follow Up", "reports": "Reports", "surveys": "Surveys", "massTasks": "Mass tasks", "massiveSending": "Mass sending", "indicators": "Indicators", "indicatorsWhenCrmEnabled": "Workshop Indicators", "settings": {"settings": "Settings", "cm": "ClearMechanic", "general": "General", "prospections": "Auto Prospecting", "workshopPlanner": "Workshop Planner"}, "conversations": "Conversations", "notFound": "Page not found", "yourAccountDoesNotHaveThisFunctionalityEnabled": "Your account does not have this functionality enabled."}, "headerOptions": {"conversations": "Conversations", "notifications": "Notifications", "settings": "Settings", "tutorials": "Tutorials", "reportAProblem": "Report a Problem", "support": "Support", "provideADetailedDescriptionOfYourProblem": "Please provide a detailed description of your problem", "placeholder": "Write the problem in detail here.", "send": "Send", "submitModalHeader": "We will contact you soon!", "submitModalText": "A member of the ClearMechanic One Solution team will contact you within one business day.", "thankYou": "Thank you! ", "accountOverdueMessage": "Your account has an overdue balance. Send your proof of payment to <1 href='mailto:{{email}}'>{{email}}</1> to avoid having your account suspended. Get up to {{percentage}}% discount with annual prepayment."}, "commonLabels": {"ok": "Ok", "refresh": "Refresh", "uploaded": "Attached By", "uploadedDate": "Uploaded Date/Time", "attachedFiles": "Attached files", "file": "File", "add": "Add", "files": "Files", "accessDenied": "Access denied", "appointment": "Appointment", "note": "Note", "selectJobDescription": "Type or select job to be done", "order": "Order", "loading": "Loading", "somethingWentWrong": "Something went wrong", "tryAgain": "Try again", "enabledProducts": "Enabled products", "enabledProductsItem": "&", "footer": "ClearCheck and its affiliates. | Visit us at:", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "saveChanges": "Save changes", "noDataSelector": "No data found", "optional": "(optional)", "formatDateMedium": "dd/mm/aa", "logout": "Log out", "settings": "Settings", "send": "Send", "back": "Back", "goBack": "Go back", "continue": "Continue", "success": "Success!", "noMatchesFound": "No matches found", "maxCharacters": "characters as maximum", "sections": {"appt": "Appointments", "ap": "Auto Prospection", "cm": "ClearMechanic", "pt": "Workshop Planner"}, "inCharge": "In Charge", "assignedTo": "Assigned to", "result": "Result", "technician": "[Title:Technician]", "name": "Name", "email": "Email", "phone": "Landline", "mobile": "Mobile", "make": "Brand", "brand": "Brand", "model": "Model", "year": "Year", "vin": "VIN", "vinPlaceholder": "Enter VIN", "plates": "Plates", "mileage": "[Title:Mileage]", "mileagePlaceholder": "Enter [Lower:Mileage]", "priorities": {"urgent": "<PERSON><PERSON>", "med": "Suggested", "low": "Low", "suggested": "Suggested"}, "number": "Number", "message": "Message", "attachFile": "Attach file", "shortcuts": "Shortcuts", "insertLink": "Insert link", "addComment": "Add comment", "vehicle": "Vehicle", "search": "Search", "upload": {"uploadImage": "Upload image", "changeImage": "Change image", "noAttachedImage": "No attached image", "uploadedImage": "Uploaded image", "preview": "Preview"}, "businessName": "Business name", "nextServiceDate": "Next service date", "calculatedMileage": "Calculated [Lower:Mileage]", "serviceAdvisor": "[Title:ServiceAdvisor]", "averageTicket": "Average ticket", "followUpDate": "Follow-up date", "hours": "Hours", "minutes": "Minutes", "promise": "Promise", "pause": "Pause", "resume": "Resume", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "error": "Error", "paymentMethod": "Payment method", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "location": "Location", "step": "Step {{number}}", "duration": "Duration", "yes": "Yes", "no": "No", "all": "All", "doNotDelete": "Don't delete", "delete": "Delete", "taxIdentification": "[Sentence:TaxIdentification]", "clear": "Clear", "date": "Date", "orderType": "Order type", "frequentBrands": "Frequent brands", "allBrands": "All brands", "timePeriods": {"d": "days", "d-singular": "day", "w": "weeks", "w-singular": "week", "M": "months", "M-singular": "month", "y": "years", "y-singular": "year"}, "project": "Project", "stopProjecting": "Stop projecting", "notes": "Notes", "downloading": "Downloading", "downloadingTakesLonger": "It takes a bit longer than was initially expected, please wait", "select": "Select", "edit": "Edit", "copy": "Copy", "enter": "Enter", "create": "Create", "blockLeavingConfirmation": {"title": "Do you want to leave without saving the changes made?", "body": "If you choose to leave now, the changes made will not be saved.", "cancelButton": "Go Back", "continueButton": "Yes, leave without saving changes"}, "apply": "Apply"}, "login": {"emailLabel": "Email", "emailPlaceholderLabel": "Enter your email", "passwordLabel": "Password", "passwordPlaceholderLabel": "Enter your password", "buttonLoginLabel": "Log in", "rememberMyData": "Remember me", "forgotMyPassword": "Forgot password?", "forgotPasswordTitle": "Did you forget your password?", "toResetPassword": "To reset your password, ", "enterYouremailAddress": "enter your email address", "youMayNeedToCheckSpam": ". You may need to check your spam folder.", "forgotPasswordSuccess1": "If a ClearMechanic account already exists for ", "forgotPasswordSuccess2": ", an email will be sent with further instructions.", "forgotPasswordSuccess3": "Success!", "resetYourPassword": "Reset your password", "resetPassword": "Reset password", "newPassword": "New password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm new password", "yourPasswordWasChanges": "Your password has been reset.", "returnToLoginPage": "Return to log in page", "wrongPassword": "Wrong password", "loggedOutDueToLackOfPerms": "You were logged out due to CMOS internal error. We apologize for the inconvenience, please log in again."}, "inspectionForms": {"cloneToAll": "<PERSON><PERSON> to All Accounts", "enterpriseFormNotice": "This is a shared Inspection Template available to all accounts. Editing this Inspection Template will affect all accounts.", "systems": {"title": "System", "addButton": "Add new system", "confirmationDelete": {"title": "Delete inspection system", "body": "Deleting this inspection system <strong>will permanently delete all inspection items</strong> that are within this system.", "cancel": "Don't delete", "confirm": "Delete system"}, "notifications": {"successfullyUpdatedBody": "Inspection system saved successfully.", "successfullyUpdatedTitle": "Updated settings", "successfullyDeleteTitle": "Inspection system deleted", "successfullyDeleteBody": "Inspection system deleted successfully."}, "nameField": {"label": "", "placeholder": "Enter system name"}}, "templates": {"confirmationDelete": {"title": "Delete inspection template", "body": "Deleting this inspection template <strong>will permanently delete all systems and inspection items</strong> that are within this template.", "cancel": "Don't delete", "confirm": "Delete template"}, "showDetails": "Show \"Details\" in inspection form seen by customer", "displayInPdf": "Display this inspection form in the Order sent to the customer", "notifications": {"successfullyUpdatedBody": "Inspection template saved successfully.", "successfullyUpdatedTitle": "Updated settings", "successfullyDeleteTitle": "Inspection form deleted", "successfullyDeleteBody": "Inspection template deleted successfully."}, "addButton": "Create new inspection template", "nameField": {"label": "New inspection template", "placeholder": "Enter name inspection template"}}, "templateItems": {"nameDuplicatedError": "The same Inspection Item already exists in this Inspection Form.", "addButton": "Add item", "seeAllButton": "See all", "confirmationDelete": {"title": "Delete inspection item", "body": "Do you want to remove this inspection item?", "cancel": "Don't delete", "confirm": "Delete inspection item"}, "notifications": {"successfullyUpdatedTitle": "Updated settings", "successfullyUpdatedBody": "Inspection item saved successfully.", "successfullyDeleteTitle": "Inspection form updated", "successfullyDeleteBody": "Inspection item removed successfully."}, "newInspectionItemField": {"label": "", "placeholder": "Enter inspection item"}, "tooltip": "Only the names of “custom items” can be edited. There are “official items” commonly used by many automotive workshops whose names cannot be edited. If you do not want to use an official item, you can remove it from the Inspection Form and add a custom item with the name of your choice."}, "estimateForm": {"title": "[Title:Estimate]: {{item}}", "subtotal": "Subtotal", "total": "Total", "taxes": "Taxes", "fieldQuantity": {"label": "Quantity", "placeholder": "Enter quantity"}, "fieldUnitPrice": {"label": "Unit price", "placeholder": "Enter unit price"}, "fieldHours": {"label": "Hours", "placeholder": "Enter hours"}, "field$Hours": {"label": "Cost per hour", "placeholder": "Enter cost per hour"}, "cleanButton": "Clean data", "successfully": {"title": "Updated settings", "body": "[Title:Estimate] saved successfully."}}, "commentsForm": {"successfully": {"title": "Updated settings", "body": "Comments in inspection item saved successfully."}, "title": "Comments", "label": "Add comments", "starttyping": "Start typing to add comments. Press the “Enter” key to confirm new comment.", "hideInfo": "(Hide info)", "moreInfo": "(More info)", "tipsCustomComments": "Tips for custom comments.", "addCustomCommentsInfo": "<b>Add comments:</b> Start typing to add a new custom comment. When you are finished, press \"Enter\" key and the comment will be saved.", "deleteCustomCommentsInfo": "<b>Delete comment:</b> You can delete a custom comment by clicking the \"X\" icon next to the same comment to delete.", "InstantSynchronizationInfo": "<b>Instant synchronization of comments:</b> All custom comment changes will be synchronized with your ClearMechanic mobile app.", "officialCommentsInfo": "<b>Official comments:</b> You will notice that certain comments cannot be deleted. These are the official comments from our master database available to all of our customers. Please contact us if you feel any official comments are inaccurate or inappropriate.", "fillInTheBlankInfo": "\"Fill in the blank\" style comments.", "tipsStyleComments": "Tips for “Capture information” type comments.", "addStyleCommentsInfo": "<b>Add comments:</b> To add a comment of \"Fill in the blank\" style comments you must add \"_:\" example: \"_: Km\" \"Color:_\". When you're done, press the \"Enter\" key and the comment will be saved.", "deleteStyleCommentsInfo": "<b>Delete comment:</b> You can delete the \"Fill in the blank\" style comments type comment by clicking on the \"X\" icon next to the same comment to be deleted.", "commentsTypeCaptureInformationInfo": "<b>Comments of type \"Capture information\":</b> These comments are made to be able to add personalized information in the Order.", "note": "Note:"}, "detailsForm": {"successfully": {"title": "Updated settings", "body": "Details at the inspection item saved successfully."}, "title": "Details", "label": "Add details", "placeholder": "Write a details here.", "starttyping": "Start typing to add details."}, "evidenceModal": {"count": "{{n}} of {{count}} evidences", "videoInProcessing": "The video is loading into our system, please try to view it later.", "videoLoader": "The video is processing", "uploadedFromGallery": "Uploaded from gallery"}, "addItem": {"addItem": "Add item", "seeAll": "See all"}, "normalView": {"item": "ITEM", "jobsAndInspectionItems": "JOBS AND INSPECTION ITEMS", "comments": "Comments", "subtotal": "SUBTOTAL", "approved": "APPROVED", "itemTotal": "Item total:", "system": "System:", "other": "Other", "campaign": "Campaign"}, "spreadsheetView": {"partsHeading": "[Title:Parts]", "laborHeading": "[Title:Labor]", "item": "<PERSON><PERSON>", "jobsAndInspectionItems": "Jobs and Inspection Items", "parts": "[Upper:PartNumber]", "quantity": "QTY.", "availability": "AVAIL.", "availabilityComments": "Comments ({{n}})", "availabilityCommentsUpdated": "Availability comments updated", "availabilityCommentsUpdatedText": "Availability comments have been successfully updated", "availabilityCommentPlaceholder": "Enter comment", "availabilityCommentDeleted": "Availability comment deleted", "availabilityCommentDeletedText": "Availability comment have been successfully deleted", "unitCost": "COST U.", "unitPrice": "PRICE U.", "hours": "HRS.", "hourPrice": "PRICE HR.", "subtotal": "Subtotal", "approved": "Approved", "addItem": "Add item", "itemTotal": "Item total:", "discount": "DISC.", "campaign": "Campaign"}, "inspectionItemsModal": {"inspectionItems": "Inspection items", "listAllInspectionItems": "List of all existing inspection items", "search": "Search"}, "editEstimateModal": {"title": "[Title:Estimate]: {{- name}}", "quantity": "Quantity", "quantityPlaceholder": "Enter quantity", "unitPrice": "Price unit", "unitPricePlaceholder": "Enter price unit", "hours": "Hours", "hoursPlaceholder": "Enter hours", "hourPrice": "Price hour", "hourPricePlaceholder": "Enter price per hour", "subtotal": "Subtotal:", "taxes": "Taxes:", "total": "Total:", "cleanData": "Clean data", "comments": "Comments", "repair": "[Title:Repair]", "parts": "[Title:Parts]", "labor": "[Title:Labor]", "unitCost": "Cost unit", "unitCostPlaceholder": "Enter cost unit", "discount": "Discount:"}, "deleteEstimateModal": {"title": "Delete row", "body": "Are you sure you want to delete this row?", "cancel": "Cancel", "confirm": "Yes"}, "validationQuoterModal": {"title": "Incomplete fields", "message": "Complete the following required fields to send the Order information to {{integrationAccountName}}: {{fieldName}}", "button": "Complete fields"}, "savedEstimateTitle": "Updated [Lower:Estimate]", "savedEstimateBody": "[Title:Estimate] successfully saved.", "orderConfirmationModal": {"title": "Create Order Only in ClearMechanic?", "message": "Orders created from the web dashboard will not sync with {{accountName}}. To create the Order in {{accountName}}, use the CM Advanced mobile app.", "question": "Do you want to proceed and create the Order only in ClearMechanic?", "cancelButton": "No, cancel", "confirmButton": "Yes, continue"}}, "newOrder": {"title": "New order", "createOrder": "Create Order", "number": "Order number", "numberPlaceholder": "Enter Order number", "numberLimitWarning": {"title": "Character limit reached.", "body": "The order number cannot exceed 50 characters."}}, "orders": {"createOrder": "New Order", "createOrderTooltip": "Create new Order", "downloadOrderTooltip": "Download “Activity Report - Inspection forms”", "columnFiltersTooltip": "Customize columns", "orderCommunication": {"sms": "SMS", "email": "Email", "wa": "WhatsApp", "smsEmail": "SMS & Email", "smsWa": "SMS & WhatsApp", "emailWa": "Email & WhatsApp", "smsEmailWa": "SMS, Email & WhatsApp"}, "searchAutocomplete": {"orders": "Orders", "RO#": "Order #", "customer": "Customer", "plates": "Plates", "vin": "VIN", "phase": "Phase", "dateCreation": "Order creation", "noOptions": "No matches found for your search."}, "filters": {"filter": "Filter (Order Type, VIN, Plates, Customer, Member of the team involved)", "RO#": "# Order", "orderType": "Order type", "vin": "VIN", "plates": "Plates", "customerName": "Customer Name", "team": "Team", "teamMember": "Team member", "memberInCharge": "[Title:ServiceAdvisor]", "hideFilters": "Hide filters", "search": "Search", "newSearch": "New search", "start": "Start", "end": "End", "dateRangeError": "Date range error", "selectADifferentDate": "Select a different date."}, "tableHeaders": {"team": "Team", "RO#": "# Order", "customerVehicle": "Customer/Vehicle", "plates": "Plates", "charged": "Uploaded", "updated": "Updated", "inspection": "Inspection", "estimate": "[Title:Estimate]", "communication": "Communication", "orderType": "Order type", "account": "Account", "lastAction": "Last action", "appointmentNumber": "Appointment", "phase": "Phase"}, "jobHistoryTableHeaders": {"jobDescription": "Job description", "status": "Status", "assigned": "Assigned", "scheduled": "Scheduled", "pause": "Pause"}, "preview": {"seeOrderDetails": "See order detail", "seeOrderDetail": "See Order detail", "customerInformation": "Customer information", "vehicleInformation": "Vehicle information", "businessOpportunity": "Business opportunity", "inspectionItems": "INSPECTION ITEMS", "maintenanceInformation": "Maintenance information", "schedule": "Schedule", "appointment": "Appointment", "followUp": "Follow-up", "cancel": "Cancel", "scheduleFollowUp": "Schedule follow-up", "followUpDate": "Follow-up date", "viewConversation": "View conversation", "lastVisit": "Last visit", "callHistory": "Call history", "phoneCallTo": "Phone call to", "started": "Started", "finished": "Finished", "callRecord": "Call record", "by": "By", "seeMore": "See more", "hide": "<PERSON>de", "masterPhoneCallTooltip": "The “Master” user cannot make calls. Please create a team member and add a phone number.", "phoneCallTooltip": "Call customer"}, "orderByType": {"Uploaded": "Uploaded", "Updated": "Updated"}, "status": {"Uploading": "Uploading", "LoadedPosted": "Uploaded", "SentEmail": "Sent E-mail", "SentSMS": "Sent SMS", "SentSMSAndEmail": "Sent SMS and E-mail", "SentWA": "Sent WA", "SentEmailAndWA": "Sent E-mail and WA", "SentSMSAndWA": "Sent SMS and WA", "SentSMSEmailAndWA": "Sent SMS, E-mail and WA", "SentInspectionForm": "Sent Inspection Form", "ApprovedByConsumer": "Approved by Consumer", "DeclinedByConsumer": "Declined by Consumer", "ApprovedByTeamMember": "Approved by Team Member", "DeclinedByTeamMember": "Declined by Team Member", "UploadedCM1SAppointment": "Uploaded from appointment", "ConvertedFromAppointment": "Uploaded from appointment", "Closed": "Closed", "UploadedFromDms": "Uploaded from DMS", "UploadedFromOpenAPI": "Uploaded from Open API", "Reopened": "Re-opened"}, "calls": {"dialing": "Connecting call", "call": "TO CALL", "yourPhoneWillRing": "Your phone will ring soon...", "callsRegister": "Call Register", "callToPhone": "Call to phone", "callToMobile": "Call to mobile", "makeCall": "Make call"}, "alertStatuses": {"title": "Alert status", "active": "Active alerts", "resolved": "Resolved alerts"}, "estimateStatuses": {"title": "[Title:Estimate] status", "yes": "[Title:Estimate] added", "no": "No [Lower:Estimate] added"}, "repairTypes": {"title": "Order type", "Canceled": "Cancelled", "BodyShop": "Body and paint", "Diagnosis": "Diagnosis", "Dwa": "Used", "Warranty": "Warranty", "Internal": "Internal", "GeneralRepair": "General [Lower:Repair]", "ExpressService": "Express service", "AuthorizedWork": "Authorized work"}, "paymentMethodTypes": {"NA": "N/A", "Cash": "Cash", "CreditOrDebitCard": "Credit or debit card", "Check": "Bank check", "WireTransfer": "Wire transfer"}, "estimatesPopover": {"estimate": "[Title:Estimate]", "approved": "Approved", "rejected": "Rejected", "totalApproved": "Total approved", "totalRejected": "Total rejected", "totalEstimated": "Total estimated"}, "alerts": "<PERSON><PERSON><PERSON>", "orders": "Orders", "assignedMember": "Assigned member", "involvedMember": "Member involved", "orderAlphabetically": "Order alphabetically", "totalApproved": "Total approved", "totalDeclined": "Total declined", "approvedByTeamMember": "Approved by team member", "declinedByTeamMember": "Declined by team member", "phaseFilterPopupTitle": "Filter by phase"}, "users": {"active": "Active", "inactive": "Inactive", "statusFilter": {"active": "Active", "inactive": "Inactive", "all": "All"}, "status": "Status", "addTeamMember": "Add team member", "passwordSecurityMessage": "The password must contain at least 8 characters, a number, an uppercase and a lowercase.", "fullName": "Full name", "jobPosition": "Job position", "email": "E-mail", "allStatus": "All", "serviceAdvisorColor": "[Title:ServiceAdvisor] Color", "selectAColor": "Select a color", "generalInformation": "General information", "schedules": "Work schedule", "schedule": {"availability": "Availability", "startOfWork": "Start of work", "closureOfWork": "Closure of work", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "appointmentReceptionLabel": {"title": "Activate reception schedule for online appointments", "description": "This user has a specific schedule for receiving appointments scheduled online by the customer."}, "receptionSchedule": "Reception schedule", "receptionScheduleTooltip": "Activate reception schedule for online appointments", "appointmentsPerDay": "Appointments per day", "appointmentsPerDayTooltip": "The maximum number of appointments that the team member can attend."}, "popupUserExist": {"title": "Team member already exists", "body": "This team member already exists in our database but has been marked as <b>“inactive”</b>.", "question": "Would you like to re-activate this team member?", "confirm": "Reactivate member"}, "notifications": {"userUpdated": "User updated", "rememberBody": "Remember, your password should include at least 8 characters long, 1 capital letter, 1 lower case letter, 1 special character (!,@,#,$,%,^,&,*,?, etc.).", "rememberTitle": "Wrong password", "matchPasswordBody": "Passwords do not match, please try again.", "matchPasswordTitle": "Incorrect password and password confirmation", "userNameIsUsedBody": "This E-mail is in use by another team member.", "dmsIDNumberIsUsedBody": "The DMS ID is in use by another team member.", "employeeIdIsUsedTitle": "Employee ID already exists", "employeeIdIsUsedBody": "Try a different number.", "userActivatedSuccefullyBody": "User activated successfully.", "userDeactivatedSuccefullyBody": "User deactivated successfully.", "userAddedSuccessfullyBody": "Team member saved successfully.", "userUpdatedSuccessfullyBody": "Team member successfully updated.", "userActivatedSuccessfullyTitle": "Team member activated", "userActivatedSuccessfullyBody": "Team member activated successfully.", "reviewPasswordTitle": "Review the password", "reviewPasswordBody": "The new password and confirm password cannot be the same as the previous password. Please try again.", "updatedSpecialty": "[Title:Specialty] updated", "dmsUserNameIsUsedBody": "Try a different username.", "dmsUserNameIsUsedTitle": "Username already exists"}, "initials": "Initials", "specialty": "[Title:Specialty]", "phone": "Phone number", "mobile": "Mobile", "phoneExtension": "Extension", "DMSID": "DMS ID", "password": "Password", "confirmPassword": "Confirm password", "employeeId": "Employee ID", "3rPartyUserName": "Third-party software username", "placeholder": {"fullName": "Enter name", "jobPosition": "Select job position", "initials": "AB", "email": "Team member E-mail", "specialty": "Select a [Lower:Specialty]", "newSpecialty": "New [Lower:Specialty]", "enterSpecialty": "Enter [Lower:Specialty]", "phone": "Enter landline", "mobile": "Enter mobile", "phoneExtension": "000", "DMSID": "Enter DMS ID", "password": "Enter password", "confirmPassword": "Confirm password", "employeeId": "Enter Employee ID", "3rPartyUserName": "Enter username"}, "edit": {"title": "Edit team member", "saveButton": "Save data"}, "add": {"title": "New team member", "saveButton": "Add new member"}, "jobTitleTypes": {"Administrator": "Administrator", "Owner": "Owner", "Manager": "Manager", "WorkshopManager": "Workshop Manager", "Parts": "[Title:Parts]", "ServiceAdvisor": "[Title:ServiceAdvisor]", "Technician": "[Title:Technician]", "CarWasher": "<PERSON> Was<PERSON>", "AppointmentsExecutive": "Appointment Executive", "BdcSupervisor": "BDC Supervisor", "BdcAdvisor": "BDC Advisor", "Other": "Other"}, "permissions": {"sectionTitle": "Permissions", "administrationSectionTitle": "Administrator", "administrationLabel": {"title": "Administrator access", "description": "This user can view or modify any settings"}, "activitySectionTitle": "Activity", "activeSwitchContainer": {"title": "Active user", "description": "This user currently works at your business."}, "showEstimationContainer": {"title": "Use the option “Show [Lower:Estimate] to customer”", "description": "This user can use the option “Show [Lower:Estimate] to customer”."}, "changeEstimationContainer": {"title": "Make changes in [Lower:Estimate]", "description": "This user can make changes to the [Lower:Estimate]."}, "generateReportContainer": {"title": "Generate reports", "description": "This user can generate reports."}, "seeAllAppointments": {"title": "See all appointments", "description": "This user can see all the appointments scheduled in your business."}, "createAndEditAppointments": {"title": "Create and edit appointments", "description": "This user can create or edit appointments for all team members."}, "seeAllOrdersContainer": {"title": "See all Orders", "description": "This user can see all the Orders created in your business."}, "createAndEditOrdersContainer": {"title": "Create and edit Orders", "description": "This user can create or edit Orders for all team members."}, "allowReopenOrdersContainer": {"title": "Re-open Orders", "description": "This user can re-open all closed Orders."}, "editVehicles": {"title": "Create and edit vehicles", "description": "This user can create and edit vehicles and their details."}, "editCustomers": {"title": "Create and edit customers", "description": "This user can create and edit customers."}, "seeTasks": {"title": "Show “Tasks”", "description": "This user can view the “Tasks” screen in the web app."}, "seeAllTasks": {"title": "See all the tasks", "description": "This user can view the tasks of all team members."}, "editTasks": {"title": "Create and edit tasks of other team members", "description": "This user can create and edit tasks of other team members."}, "allowManageMassTasks": {"title": "Manage mass tasks", "description": "This user can view, create, and edit mass-assigned tasks targeting groups of vehicles, such as campaigns or prospecting actions."}, "sendMassiveSendings": {"title": "Send mass sendings", "description": "This user can create, edit, and delete mass sendings."}, "seeAllConversationContainer": {"title": "See all conversations", "description": "This user can view the conversations of all team members."}, "allowShowJobs": {"title": "Show “Jobs”", "description": "This user will be able to view the “Jobs” screen in the mobile app."}, "allowSeeAllJobs": {"title": "See all “Jobs”", "description": "This user can view all the jobs of all team members in the mobile app."}, "allowManageJobs": {"title": "Manage “Jobs”", "description": "This user can start, finish, pause, or resume jobs for all team members."}, "editInfoInWorkshopPlannerContainer": {"title": "Edit information in Workshop Planner", "description": "This user can edit information in Workshop Planner."}, "notificationSectionTitle": "Notifications", "notificationsContainer": {"title": "Upload notifications", "description": "This user will be notified by email when an Order has been uploaded."}, "labels": {"administrator": "Administrator", "allowToUseShowEstimateToConsumerOption": "Allow to use “Show [Lower:Estimate] to customer” option", "editEstimates": "Add / Change [Lower:Estimates]", "allowSeeAppointments": "Allow to view appointments", "allowGenerateReports": "Allow to generate reports", "shouldReceiveUploadNotifications": "Receive upload notifications by E-mail"}}, "dmsModal": {"emptyDmsIdField": "Empty “DMS ID” field", "theTeamMemberWillNotAbleWithOutDMSId": "The team member will not be able to use certain functionalities without having added a DMS ID."}}, "orderDetails": {"generalInfo": "General information", "estimate": "[Title:Estimate]", "orderDetail": "Order detail #", "RO#": "Order #", "seeActivityLog": "See activity log", "printTooltip": "Print PDF of the Order or PDF of the [Title:Estimate]", "printOrder": "Print PDF of the Order", "printOrderTooltip": "The Order PDF includes the following sections: \"General information\", \"[Title:Estimate]\", \"Evidence\" and \"Digital signatures\".", "printJobs": "Print PDF with jobs", "printJobsTooltip": "The jobs PDF includes the sections \"General information\", \"Jobs\", and \"Digital signatures\". It does not include the \"[Title:Estimate]\" and \"Evidence\" section.", "printCustomPdf": "Print custom PDF", "printCustomPdfTooltip": "This PDF will only include the sections you select.", "customPdfModal": {"mainLabel": "Customize the Order PDF", "secondaryLabel": "Sections that the PDF must include:", "constructor": {"generalInformation": "General information", "customerInformation": "Customer information", "vehicleInformation": "Vehicle information", "orderInformation": "Order information", "reasonsForVisit": "Reasons for visit", "estimate": "[Title:Estimate]", "summary": "Summary", "notes": "Notes", "jobsAndInspectionItems": "Jobs and Inspection items", "discountsSubtotalTaxesTotal": "Discounts, Subtotal, Taxes, Total", "evidence": "Evidence", "jobs": "Jobs", "digitalSignatures": "Digital Signatures", "customerSignatureAtReception": "Customer's Signature at Reception", "estimatedApprovalSignature": "Estimated Approval Signature", "customerSignatureAtDelivery": "Customer's Signature at Delivery", "adhesionContract": "Adhesion Contract", "privacyNotice": "Privacy notice", "businessSignature": "Business Signature", "printPdf": "Print PDF"}}, "repairOrderType": "Order type", "closeOrder": "Close Order", "reopenOrder": "Re-open Order", "closeOrderIntegratedAccount": {"success": {"title": "Order closed", "description": "Order closed successfully in {{accountIntegratedName}}."}, "error": {"title": "Unable to close Order on {{accountIntegratedName}}", "description": "Error message"}}, "paymentMethod": "Payment method", "evidence": "Evidence", "views": "Views", "orderLink": "Digital Order", "lastUpdate": "Last update", "lastCommunication": "Last Communication", "customerInformation": "Customer information", "vehicleInformation": "Vehicle information", "mileage": "[Title:Mileage]", "inCharge": "[Title:ServiceAdvisor]", "communication": "Communication with the customer", "areYouSureToClose": "Are you sure you want to close the Order?", "customNote": "Custom Note", "firstNamePlaceholder": "Name", "lastNamePlaceholder": "Last name", "emailPlaceholder": "Enter email", "phonePlaceholder": "Enter landline", "mobilePlaceholder": "Enter mobile", "mileagePlaceholder": "Enter [Lower:Mileage]", "platesPlaceholder": "Enter plates", "invalidEmailBody": "Please enter a valid email", "invalidEmailTitle": "Invalid email", "teamMembers": "Team Members", "orderInformation": "Order Information", "inspectionFormsLabel": "Inspection forms", "multiInspectionForm": "Multi inspection form", "printInspection": "Print inspection", "businessName": "Business name", "businessNamePlaceholder": "Enter business name", "identDoc": "Identification document", "notes": "Notes", "additionalNotes": {"notesForCustomer": "Notes visible to the customer", "noNotesForCustomer": "No notes visible to the customer.", "notesForInternal": "Internal notes", "noInternalNotes": "No internal notes.", "addNote": "Add note", "enterNote": "Enter note", "noteActivityLog": "Activity log", "noteCannotBeEdited": "This text cannot be edited.", "addedBy": "Added by", "on": "on", "modifiedBy": "Modified by", "at": "at", "customer": "customer", "teamMember": "team member", "editNote": "Edit", "deleteNote": "Delete"}, "orderReasons": {"customerReasonsForVisit": "[Sentence:CustomerReasonsForVisit]", "noCustomerReasonsForVisit": "No [Lower:CustomerReasonsForVisit]", "textCannotBeEdited": "This text cannot be edited.", "workshopReasonsForVisit": "[Sentence:WorkshopReasonsForVisit]", "noWorkshopReasonsForVisit": "No [Lower:WorkshopReasonsForVisit]", "addReason": "Add reason", "enterReason": "Enter reason"}, "estimationDate": "Estimation date", "identDocType": {"placeholder": "Document type", "typeNotSaved": "The identification document type not saved", "typeCannotBeEmpty": "The identification document type cannot be empty or only blank spaces", "typeDuplicated": "The identification document type is duplicated", "typeSaved": "The identification document type saved", "typeSavedText": "The identification document type saved successfully"}, "identNum": "Identification number", "inspectionForms": {"priorityFilter": {"All": "Show all items", "Red": "Show red items", "Yellow": "Show yellow items"}, "payments": {"thirdParty": {"success": {"title": "Payment register created", "description": "Payment register created successfully in {{integratedAccountName}}."}, "error": {"title": "Unable to create the payment register in {{integratedAccountName}}", "description": "Error message: {{errorMessage}}"}, "synchronization": {"success": "Payment successfully registered in {{integratedAccountName}}", "error": "Payment could not be registered to {{integratedAccountName}}, please try again"}}, "dateFormat": "DD/MM/YYYY", "timeFormat": "HH:mm", "button": "Payments", "register": {"tab": "Register payment", "description": "Team member registering the payment: ", "button": "Register payment", "fields": {"paymentMethodDropdown": {"title": "Payment method", "fields": {"amountPaid": "Amount paid", "paymentMethod": {"label": "Payment method", "placeholder": "Select payment method", "dropdown": {"cash": "Cash", "card": "Debit/Credit Card", "bank": "Bank Transfer", "link": "Payment Link"}}}}, "paymentDate": "Payment date", "comments": {"label": "Comments", "placeholder": "Write your comments here"}}, "anotherPaymentMethod": "Add another payment method", "deleteModal": {"title": "Do you want to remove this payment method?", "button": {"back": "Go back", "delete": "Delete"}}, "details": {"title": {"left": "Detail", "right": "Total"}, "approved": "Total approved", "method": {"left": "Payment method", "right": "Amount"}, "pending": "Pending payment", "totalAmount": "Total amount paid for the Order"}, "notification": {"success": {"title": "Payment recorded", "description": "Payment successfully recorded."}, "error": {"title": "Error registering payment.", "description": "An error occurred while trying to record the payment. Please try again."}}}, "history": {"tab": "Payments history", "withoutResults": {"title": "No payments history", "description": "Please register a payment to generate information."}, "record": {"title": "Payment register", "teamMember": "Team member", "paymentDate": "Payment date", "paymentMethod": "Payment method", "amountPaid": "Amount paid", "totalAmountPaid": "Total amount paid for the Order", "paymentPending": "Pending payment", "comments": "Comments"}}}, "downloadEvidence": "Download evidence", "downloadAllEvidence": "Download all photos, videos, and files (PDF, XLSX, DOC)", "downloadEvidenceError": "An error occurred while downloading evidence", "attachFile": "Attach file", "showEstimateToCustomer": "Show [Lower:Estimate] to customer", "scheduleAppointment": "Schedule appointment for declined items", "spreadSheetView": "Spreadsheet view", "normalView": "Normal view", "printEstimate": "Print PDF of the [Lower:Estimate]", "printEstimateTooltip": "The [Title:Estimate] PDF includes only the \"[Title:Estimate]\" section. It does not include the \"General information\", \"Evidence\", \"Jobs\" or \"Digital signatures\" sections.", "downloadPhotos": "Download photos", "attachFileErrorTitle": "Error attaching file", "attachFileErrorBody": "Supported extensions are: .doc, .docx, .xlsx, .xls, .text, .txt, .pdf", "attachFileSuccessTitle": "File attached successfully", "attachFileSuccessBody": "You have successfully attached the file", "downloadEstimatesExcel": "Export estimate in an Excel file (.xlsx)"}, "estimateNotes": {"notesForCustomer": "Notes visible to the customer", "notesForInternal": "Internal notes", "addNote": "Add note", "noteActivityLog": "Activity log", "addedBy": "Added by", "on": "on", "modifiedBy": "Modified by", "at": "at"}, "activityLog": {"activityLog": "Activity log", "clientCannotSee": "Follow-up activity is only accessible from your private dashboard. The customer will never be able to see it.", "phoneCall": "Phone call", "estimateAdded": "[Title:Estimate] added", "insertDateTime": "Insert date / time", "addNewComment": "Add a new event", "commentPlaceholder": "Write a new event here", "addComment": "Add event", "phoneCallComment": "Phone call to consumer made on {{- date}} at {{- time}}.", "estimateProvidedComment": "[Title:Estimate] provided to consumer on {{- date}} at {{- time}}.\r\n\t[Title:Labor]:\r\n\t[Title:Parts]:\r\n\tMisc:\r\n\tTotal:", "dateTimeComment": "{{- date}} {{- time}}", "orderCreation": "Order #{{orderNumber}}, created by {{userName}} on {{- date}} at {{- time}} from the ClearMechanic web dashboard. Order not synchronized with the integrated software."}, "totalEstimates": {"discount": "Discounts:", "subtotal": "Subtotal:", "taxes": "Taxes:", "total": "TOTAL:", "approved": "Approved:", "declined": "Declined:", "pending": "Pending:"}, "tower": "Tower", "phase": "Phase: ", "noPhase": "No phase", "closedOrder": "Closed Order", "enterTower": "Enter tower", "customerSignature": "Customer signature", "customerSignatureAtReception": "Customer's signature at Reception", "customerSignatureAtDelivery": "Customer's signature at Delivery", "customerSignatureAdhesion": "Signature of the adhesion contract", "customerSignaturePrivacyNotice": "Signature of the privacy notice", "customerNotifications": {"appointmentsNotificationsTitle": "Appointments notifications", "appointmentsNotificationsDescription": "The customer will receive automatic WhatsApp notifications such as appts, reminders, etc.", "massSendingsTitle": "Mass sendings", "massSendingsDescription": "The customer will receive mass sendings by WhatsApp."}, "teamMemberSignature": "Team member signature", "serviceAdvisorDeliverySignature": "Service Advisor's signature at Delivery", "serviceAdvisorReceptionSignature": "Service Advisor's signature at Reception", "signatureNameAndJobTitle": "Name and position of the signature", "brandPlaceholder": "Select a brand", "modelPlaceholder": "Select a model", "yearPlaceholder": "Select a year", "reasonsForAppointment": "[Sentence:ReasonsForVisit]", "addedByCustomer": "Added by customer", "addedByTeamMember": "Added by team member", "packagePopup": {"title": "Select packages", "subtitle": "Select a package related to this item:", "package": "Package", "withoutPackages": "Continue without adding packages", "itemDisabledHint": "This item is already in the Order. Select other packages to add new inspection items.", "packageAdded": {"title": "Package added", "text": "The package has been added to the estimate successfully."}}, "closedOrderWarning": {"body1": "The Order has been closed by {{userDisplayName}} on {{- formattedDateTime}}.", "body2": "The “Re-open Order” button is only enabled for team members with the “Re-open Orders” permission activated.", "title": "Closed Order", "button": "Ok"}, "tooltipCopy": "Copy", "tooltipTextCopied": "The text was copied", "tooltipComments": "Inventory comments", "thirdParty": {"updateOrderInfo": {"success": {"title": "Order updated", "description": " The general information of the Order was successfully updated in {{accountIntegratedName}}."}, "error": {"title": "Unable to update the Order in {{accountIntegratedName}}", "description": "Error message: {{errorMessage}}."}}}}, "phases": {"noPhase": "No phase", "closedOrder": "Closed Order", "allPhases": "All phases"}, "settings": {"accessDeniedText": "I'm sorry, you don't have permission to access settings.", "location": {"name": "Location name", "namePlaceholder": "Enter the location name", "legalName": "Legal name", "legalNamePlaceholder": "Enter the legal name of the location", "address": "Address", "addressPlaceholder": "Enter the location address", "phoneNumber": "Phone number", "phoneNumberPlaceholder": "Enter the location phone number", "emailAddress": "Email address", "emailAddressPlaceholder": "Enter the location email address", "website": "Website", "websitePlaceholder": "Enter the location website", "taxIdentification": "[Sentence:TaxIdentification]", "taxIdentificationPlaceholder": "Enter the location [Lower:TaxIdentification]", "mondayToFridayHours": "Monday to Friday", "mondayToFridayHoursPlaceholder": "Enter the [Lower:ServiceHours] for Monday to Friday", "saturdayHours": "Saturday", "saturdayHoursPlaceholder": "Enter the [Lower:ServiceHours] for Saturday", "header": "Customize the image in Orders", "dimensionsNote": "<strong>Note:</strong> The image must have the following dimensions: 220x126", "orderLogoRemoved": "Logo for the Order removed successfully", "orderLogoUpdated": "Order logo updated successfully", "invalidLogo": "Logo for the Order is not valid", "generalInformation": "General information", "serviceHours": "[Sentence:ServiceHours]", "showBrandLogo": "Show vehicle brand logo"}, "cm": {"general": "General", "signatures": "Signatures", "surveys": {"header": "Surveys", "enable": "Activate sending of surveys", "days": "# of days after assigning the Order in the phase that the survey will be sent", "personalizeQuestions": "Personalize questions", "personalizeQuestionsTooltip": "Additional questions will only be sent if the customer answers the previous question.", "phase": "Phase at which it will send the survey", "personalizedQuestions": {"selectAnswerOption": "Select answer option", "textBoxHeaderPrimary": "Hi [<1 className={{blueText}}>Customer name</1>]! Thank you for visiting [<1 className={{blueText}}>Business name</1>]. Please help us by answering the following survey.", "textBoxHeaderSecondary": "Thank you for your response! Please answer the following question.", "question": "Question", "1to5Option": {"label": "From 1 - 5", "placeholder": "What grade would you give to the attention given by our team?", "textBoxFooterPrimary": "Please answer with a number from 1 to 5.", "textBoxFooterSecondary": "Considering that 5 means an \"excellent\" grade."}, "1to10Option": {"label": "From 1 - 10", "placeholder": "What grade would you give to the attention given by our team?", "textBoxFooterPrimary": "Please answer with a number from 1 to 10.", "textBoxFooterSecondary": "Considering that 10 means an \"excellent\" grade."}, "yesNoOption": {"label": "Yes / No", "placeholder": "Do you think the attention provided by our team was excellent?", "textBoxFooterPrimary": "Please answer number 1 for “Yes” or number 2 for “No”."}, "lineBrakesWarn": "Line breaks are not allowed.", "questionsSavedTitle": "Question saved", "questionsSavedText": "Question saved successfully", "addQuestion": "Add question"}}, "campaigns": {"tab": "Campaigns", "title": "General", "activateSwitch": {"label": "Activate campaigns", "subText": "Display vehicle campaigns in the mobile app upon entering the VIN."}, "columns": {"allBrands": "All", "allModels": "All", "allYears": "All", "allVins": "All"}, "noCampaigns": {"title": "No campaigns registered!", "description": "Please start importing campaigns."}, "seekerPlaceholder": "Search campaign", "priorityLevel": {"text": "Default priority level for campaign inspection items", "level": {"urgent": "<PERSON><PERSON>", "suggested": "Suggested", "ok": "Ok"}}, "deleteButton": {"title": "Delete campaign", "description": "Are you sure you want to delete this campaign?", "cancel": "Don't delete", "delete": "Yes, delete", "success": "Campaign successfully deleted"}, "tableHeaders": {"campaignCode": "CAMPAIGN CODE", "campaignName": "CAMPAIGN NAME", "vins": "VIN", "brand": "BRAND", "model": "MODEL", "year": "YEAR", "startDate": "START DATE", "endDate": "END DATE"}, "importModal": {"openButton": "Import campaigns", "title": "Import campaigns", "cancelButton": {"cancel": "Yes, cancel", "back": "Go back"}, "errors": {"missingMandatory": {"title": "File not uploaded", "description": "It has mandatory columns without information."}, "invalid": {"title": "Document not uploaded", "description": "The list of campaigns was not uploaded; please review the data and try again."}}}}, "followUp": {"header": "Surveys", "surveys": {"header": "Surveys", "enable": "Activate sending of surveys", "days": "Send the survey after [] days", "email": "Email to receive notifications", "googlePlaceId": "Place ID in Google Maps", "defaultDate": {"label": "Default date to send the survey", "closed": "Order was closed", "firstTime": "Order was sent for the first time", "uploaded": "Order was uploaded"}}}, "mobileApp": {"header": "Mobile app", "frequentNotesLabel": "Frequent notes", "frequentNotesForCustomerLabel": "Customer visible frequent notes", "frequentNotesForInternalLabel": "Internal frequent notes", "frequentNotesForCustomerHint": "Notes that can be seen by both team members and the customer.", "frequentNotesForInternalHint": "Notes that can only be seen by team members.", "frequentNotesNote1": "Add your note and hit “Enter” to save", "frequentNotesNote2": "Note (s) of type “Capture in Empty Space”", "sharedOrdersRestrictionLabel": "Restriction on editing shared Orders", "sharedOrdersRestrictionOption1": "No restrictions on editing", "sharedOrdersRestrictionOption2": "Restricted editing", "sharedOrdersRestrictionDescription1": "User can change shared Orders in any way", "sharedOrdersRestrictionDescription2": "User can only add to shared Orders", "showCustomerInfoLabel": "Show customer information to [Title:Technicians]", "generateOrderNumberAutomaticallyLabel": "Generate Order number automatically", "generateOrderNumberAutomaticallyOption1": "Disabled", "generateOrderNumberAutomaticallyOption2": "Enabled", "generateOrderNumberAutomaticallyDescription1": "User manually types in Order number", "generateOrderNumberAutomaticallyDescription2": "Order number is automatically created", "startingOrderNumberLabel": "Starting Order number", "hideAddInspectionItemFeatureLabel": "Hide “Add inspection item” feature", "addCharactersBeforeEachOrderNumber": "Add characters before each Order number", "mandatoryFieldsReception": "Mandatory fields to generate Order (Order Information)", "receptionSignature": "Reception signature", "deliverySignature": "Delivery signature", "mandatoryFieldsCustomer": "Mandatory fields to generate Order (Customer Information)", "name": "Name", "lastName": "Last name", "email": "Email", "mobile": "Mobile", "signatureInPrivacyNotice": "Signature in privacy notice", "mandatoryFieldsVehicle": "Mandatory fields to generate Order (Vehicle Information)", "plates": "Plates", "brand": "Brand", "model": "Model", "year": "Year", "mileage": "[Title:Mileage]", "VIN": "VIN", "tower": "Tower", "orderType": "Order type", "deliveryDay": "Delivery day", "deliveryHour": "Delivery hour", "tmSignatures": {"1": "Team member signature 1", "2": "Team member signature 2", "3": "Team member signature 3", "4": "Team member signature 4"}, "signatureDisabledHint": "Before activating, make sure to enable the team member's signature in the “Team member signature” section.", "phaseToSign": "Phase to sign", "mandatoryFieldsReceptionHint": "The team member's signature will be mandatory depending on the selected phase.", "allowUploadingMediaFilesFromTheMobileGallery": "Allow uploading media files from the mobile gallery", "allowUploadingMediaFilesFromTheMobileGalleryTooltip": "Team members will be able to add images and videos to inspection items from their mobile gallery."}, "inCharge": {"header": "In charge", "automaticallyInCharge": "[Title:ServiceAdvisor] is automatically “In Charge“ of Orders"}, "assignedTo": {"header": "Assigned to", "automaticallyAssignedTo": "[Title:Technician] is automatically “Assigned to” of Orders"}, "estimates": {"header": "[Title:Estimates]", "enableSpreadsheetViewByDefault": "Automatically show “Spreadsheet View“", "enableEstimateReview": "Do not show [Lower:Estimate] until team member review"}, "dmsIntegration": {"header": "DMS integration", "showAllRosFromDms": "Show all Orders from DMS"}, "teamMemberSignature": {"mobilAppSignaturesHeader": "Team member's signature of the Order in the mobile app", "handwrittenSignaturesHeader": "Team member's handwritten signatures", "activateDigitalSignatureInMobileApplication1": "Activate digital signature in the mobile application for team member 1", "activateDigitalSignatureInMobileApplication2": "Activate digital signature in the mobile application for team member 2", "activateDigitalSignatureInMobileApplication3": "Activate digital signature in the mobile application for team member 3", "activateDigitalSignatureInMobileApplication4": "Activate digital signature in the mobile application for team member 4", "signatureHeaderText": "Signature header text", "signatureHeaderText1": "Digital signature header for team member 1", "signatureHeaderText2": "Digital signature header for team member 2", "signatureHeaderText3": "Digital signature header for team member 3", "signatureHeaderText4": "Digital signature header for team member 4", "signatureHeaderTextPlaceholder1": "Team member signature 1", "signatureHeaderTextPlaceholder2": "Team member signature 2", "signatureHeaderTextPlaceholder3": "Team member signature 3", "signatureHeaderTextPlaceholder4": "Team member signature 4", "signatureHeaderTextPlaceholder": "Team member signature", "customerFormPdfSignature": "Activate team member's handwritten signature in the PDF version of the Order, normal view", "estimatesPdfSignature": "Activate team member's handwritten signature in the PDF version of the Order, spreadsheet view"}, "serviceAdvisorSignature": {"header": "[Title:ServiceAdvisor] signature", "enableServiceAdvisorSignature": "Enable [Title:ServiceAdvisor]'s signature"}, "technicianSignature": {"header": "[Title:Technician] signature", "enableTechnicianSignature": "Enable [Title:Technician]'s signature"}, "customerSignature": {"header": "Customer signature", "enableElectronicSignature": "Enable digital reception signature in mobile application", "electronicSignatureHeader": "Reception signature header", "electronicSignatureHeaderPlaceholder": "Enter reception signature header text", "electronicSignatureText": "Reception signature text", "electronicSignatureTextPlaceholder": "Enter reception signature text here", "electronicSignatureCheckbox": "Reception signature checkbox text", "electronicSignatureCheckboxPlaceholder": "Enter reception signature checkbox text here", "enableAdhesionContractSignature": "Enable adhesion contract signature in the mobile app", "adhesionContractSignatureHeader": "Adhesion contract signature header", "adhesionContractSignatureHeaderPlaceholder": "Enter adhesion contract signature header", "adhesionContractSignatureText": "Adhesion contract signature text", "adhesionContractSignatureTextPlaceholder": "Enter adhesion contract signature text here", "adhesionContractSignatureCheckbox": "Adhesion contract signature checkbox text", "adhesionContractSignatureCheckboxPlaceholder": "Enter adhesion contract signature checkbox text here", "enableNoticePrivacySignature": "Enable privacy notice signature in the mobile app", "noticePrivacySignatureHeader": "Notice of privacy signature header", "noticePrivacySignatureHeaderPlaceholder": "Enter notice of privacy signature header here", "noticePrivacySignatureText": "Notice of privacy signature text", "noticePrivacySignatureTextPlaceholder": "Enter notice of privacy signature text here", "privacyNoticeSignatureCheckbox": "Privacy notice signature checkbox text", "privacyNoticeSignatureCheckboxPlaceholder": "Enter privacy notice signature checkbox text here", "enableElectronicDeliverySignature": "Enable digital delivery signature in mobile application", "electronicDeliverySignatureHeader": "Delivery signature header text", "electronicDeliverySignatureHeaderPlaceholder": "Enter delivery signature header text", "electronicDeliverySignatureText": "Delivery signature text", "electronicDeliverySignatureTextPlaceholder": "Enter delivery signature text here", "electronicDeliverySignatureCheckbox": "Delivery signature checkbox text", "electronicDeliverySignatureCheckboxPlaceholder": "Enter delivery signature checkbox text here", "enableHandwrittenSignature": "Activate customer's handwritten signature in the PDF version of the Order, normal view", "handwrittenSignatureHeader": "Header text in PDF", "handwrittenSignatureHeaderPlaceholder": "Enter header text in PDF", "handwrittenSignatureText": "PDF signature text", "handwrittenSignatureTextPlaceholder": "Enter PDF signature text here", "enableHandwrittenSignatureForEstimateApproval": "Activate handwritten signature for estimate approval", "handwrittenSignatureForEstimateApprovalHeader": "Signature header", "handwrittenSignatureForEstimateApprovalHeaderPlaceholder": "Enter signature header"}, "legalRepresentativeSignature": {"header": "Legal representative signature", "dimensionsNote": "The signature must have dimensions of 50 x 55", "enableSignatureInPDF": "Enable legal representative signature in PDF", "signatureLabel": "Legal representative signature label", "signaturePlaceholder": "Signature of the legal representative"}, "activityReports": {"title": "Activity reports", "enableDaily": "Enable daily activity report", "enableWeekly": "Enable weekly activity report", "enableMonthly": "Enable monthly activity report", "successBody": "The activity report has been enabled successfully."}, "others": {"title": "Others", "pdf": "Include the option to \"Print Order in PDF\" in upload notifications", "customerContact": "Enable \"Add customer contact\" feature in mobile app", "successBody": "The option of the “Others” section has been updated successfully."}, "updatedConfiguration": "Updated configuration", "defaultSms": {"title": "Default SMS", "smsText": "SMS text", "restore": "Rest<PERSON>", "save": "Save", "saveSuccessBody": "The default SMS has been updated successfully.", "restoreSuccessBody": "The configuration has been changed successfully."}}, "general": {"locationInfo": "Location info", "teamMembers": "Team members", "orderPdf": "Order PDF", "orders": "Orders", "inspectionForms": "Inspection forms", "appointments": "Appointments", "siteForAppointments": "Online appointments", "appointmentReasons": "[Sentence:ReasonsForVisit]", "orderTypes": "Order types", "phases": "Phases", "terminologies": "Customizable terms", "packages": "Packages"}, "aftersalesCrm": {"general": "General", "classificationOfActivities": {"title": "Activity classification", "addStatus": "Add new customer status", "addStatusPlaceholder": "Enter new customer status", "addActivity": "Add activity", "activity": "Activity", "description": "Description", "activityModal": {"newActivity": "New activity", "editActivity": "Edit activity", "nameTitle": "Activity name", "namePlaceholder": "Enter the name", "statusTitle": "Customer status", "statusPlaceholder": "Select the customer status", "descriptionTitle": "Activity description", "descriptionPlaceholder": "Briefly describe the activity classification", "newActivityButton": "Create new activity", "editActivityButton": "Save changes"}, "deleteStatusModal": {"title": "Delete this customer status?", "text": "All activities linked to this status will also be deleted."}, "deleteActivityModal": {"title": "Delete this activity?", "text": "Only this activity will be deleted."}, "notifications": {"statusCreated": "Customer status created", "activityCreated": "Activity created", "statusDeleted": "Customer status deleted", "activityDeleted": "Activity deleted", "statusExists": "Customer status already exists", "activityExists": "Activity already exists"}, "delete": "Delete", "cancel": "Cancel", "emptyPageTitle": "Create a customer status!", "emptyPageBody": "It's required to create activity classifications."}, "prospectionPriorities": "Prospection priorities", "classificationOfAppointments": "Classification of appointments", "importVehicles": {"title": "Import vehicles", "successTitle": "Document uploaded", "successBody": "The vehicles list has been uploaded successfully", "partialImportCompleted": "Partial import completed", "partialImport": {"text": "{{successful}} of {{total}} records were uploaded.", "link": "View records that were not uploaded"}, "incorrectFormatTitle": "Import failed", "incorrectFormatBody": "Invalid file format. Please make sure to upload a file with a .xlsx or .csv extension.", "invalidVehicleListTitle": "Import failed", "invalidVehicleListBody": "Invalid vehicle list format. Please ensure that each vehicle includes a VIN.", "fileSizeLimitExceededTitle": "Import failed", "fileSizeLimitExceededBody": "The uploaded file exceeds the 5MB limit. Please upload a smaller file.", "uploadFile": "Upload file", "notes": "Notes", "acceptedFormat": "Accepted format .xlsx and .csv", "maximumAllowedSize": "Maximum allowed size of the document 5 MB", "downloadTemplate": "Download template", "pendingImports": {"inProgress": "Import in progress", "inProgressText": "You can continue using other screens and features while the import is completed. ", "completed": "Import completed successfully", "completedText": "All records were successfully imported.", "failedImportInternalErrorText": "Import completely failed due to an internal error, please try again later or contact support if it keeps happening", "failedImportInternalError": "Import failed (Internal error)", "partialFailInternalErrorText": {"line1": "{{successful}} of {{total}} records were uploaded.", "line2": "Some errors occured", "line3": "Re-upload the file or contact support."}, "partialFailInternalError": "Partial import completed"}}}, "notifications": {"header": "Notifications", "realTime": {"headerOld": "Real time alerts", "header": "Real-Time Notifications in Mobile App and Web Dashboard", "headerTooltip": " The notifications activated in this section will be displayed by clicking on the \"Bell\" icon in the upper right corner of the Web Dashboard and Mobile App. In the case of the Mobile App, notifications will also be displayed on the mobile device's home screen, even if the device is asleep.", "restore": "Rest<PERSON>", "alertBody": "Notification text", "alertTitle": "Notification title", "maxChar": "{{count}} characters as maximum", "minute": "{{count}} minutes", "hour_other": "{{count}} hours", "hour_one": "{{count}} hour", "hours": "Hours", "minutes": "Minutes", "customTime": "Custom time", "recipients": "Select recipients", "enabledSuccessfully": "The alert has been enabled successfully", "disabledSuccessfully": "The alert has been disabled successfully", "triggers": {"SelectPriorityColor": "Select priority color", "SelectPercentage": "Select percentage", "SelectMileage": "[Title:Mileage]", "SelectCount": "Select quantity", "SelectTime": "Select time", "SelectCondition": "Select condition", "SelectItem": "Select item", "SurveyWithNegativeRating": {"question": "Question", "selectQuestionNumber": "Select question number", "selectNegativeRating": "Select negative rating", "addQuestion": "Add question"}, "NewRedItem": {"Red": "red", "Yellow": "yellow", "RedYellow": "red/yellow"}, "PhaseSetback": {"originPhase": "Origin phase", "selectOriginPhase": " Select origin phase", "destinationPhase": "Destination phase", "selectDestinationPhase": "Select destination phase", "addPhaseSetbackSettingTooltip": "Add another phase setback setting.", "removePhaseSetbackSettingTooltip": "Remove this setback setting."}}, "restorePopupTitle": "Restore default text", "restorePopupText": "Are you sure you want to reset to the default text?\nThis will override any previous customizations made.", "appointmentAssignedTooltip": "The notification will be shown to selected recipients when an appointment is assigned to a team member.", "newOrderTooltip": "The notification will be displayed to the selected recipients when a team member creates a new Order.", "phaseModifiedTooltip": "The notification will be displayed to the selected recipients when a team member changes the phase of an Order.", "orderAssignedTooltip": "The notification will be displayed to the selected recipients who are assigned an Order.", "inspectionItemApprovedTooltip": "The notification will be displayed to selected recipients when a customer or team member approves an inspection item or job estimated.", "inspectionItemDeclinedTooltip": "The notification will be displayed to selected recipients when an inspection item or job estimated is declined by a customer or team member.", "surveyWithNegativeRatingTooltip": "The notification will be displayed to the selected recipients when a survey has a low rating based on the settings made.", "newRedItemTooltip": "The notification will be displayed to selected recipients when a new red and/or yellow inspection item or job is uploaded, according to the configuration.", "phaseSetbackTooltip": "The notification will be shown to the selected recipients when a team member moves the Order from the current phase to a previous phase.", "paymentReceivedTooltip": "The payment notification will be sent to the selected recipients when the customer makes a partial or full payment of the Order.", "jobAssignedTooltip": "The notification will be displayed to the selected recipients who are assigned a job."}, "defaultEmail": {"header": "Default e-mail", "preview": "Preview", "previewHeader": "Preview", "subject": "Subject", "text": "E-mail text", "restore": {"restore": "Rest<PERSON>", "title": "Restore default text", "text": "Are you sure you want to reset to the default text?\nThis will override any previous customizations made."}}, "smsShortcuts": {"header": "SMS shortcuts", "shortcut": "Shortcut", "enterShortcutName": "Enter the name of the shortcut", "enterShortcut": "Enter the shortcut here", "addShortcut": "Add shortcut", "savedSuccessfully": "The SMS shortcuts have been saved successfully.", "deletedSuccessfully": "The SMS shortcuts have been deleted successfully."}, "defaultEstimateNotes": {"header": "Default notes for customer about the [Lower:Estimate]", "note": "Note", "addNote": "Add note", "savedSuccessfully": "The default notes for customer about the [Lower:Estimate] have been saved successfully.", "deletedSuccessfully": "The default notes for customer about the [Lower:Estimate] have been deleted successfully."}, "restoreDefaultText": {"restore": "Rest<PERSON>", "title": "Restore default text", "text": "Are you sure you want to reset to the default text?\nThis will override any previous customizations made."}, "recipients": {"header": "Recipients", "emailSms": "Send e-mail and SMS", "emailSmsTooltip": "These e-mail addresses are notified every time an e-mail or SMS text is sent through ClearMechanic One Solution.", "upload": "Upload notifications", "uploadTooltip": "These e-mail addresses are notified whenever a photo or video is uploaded to the Order through the ClearMechanic mobile applications.", "support": "Support contact", "supportTooltip": "These e-mail addresses are notified whenever a technical issue occurs with the ClearMechanic mobile applications, such as an incomplete photo or video upload.", "activity": "Activity reports", "activityTooltip": "These e-mail addresses receive daily weekly, and monthly activity reports summarizing all activity through ClearMechanic One Solution, which contain information such as the number of messages sent, how many photos and videos uploaded, and other metrics.", "followup": "Follow-up letters", "followupTooltip": "These e-mail addresses are copied in all follow-up letters.", "approval": "Receiving approvals", "approvalTooltip": "When your customers approve your recommendations, a notification will be sent to this e-mail address.", "emailPlaceholder": "<EMAIL>"}, "fromEmail": {"header": "\"From\" e-mail", "enableFromEmail": "Enable \"From\" e-mail features", "emailAddressLabel": "E-mail address", "emailAddressPlaceholder": "<EMAIL>", "emailAddressHint": "This is the \"from\" email address seen by customers", "repairShopNameLabel": "Repair shop name", "repairShopNamePlaceholder": "The Repair Shop", "repairShopNameHint": "This is the \"from\" name seen by customers", "connectionTypeLabel": "Connection type", "connectionType": {"plain": "Plain", "ssl": "SSL", "tls": "TLS"}, "portLabel": "Port", "serverNameLabel": "SMTP server name", "serverNamePlaceholder": "smtp-mail.domain.com", "userNameLabel": "SMTP user name", "userNamePlaceholder": "<EMAIL>", "passwordLabel": "SMTP password", "passwordPlaceholder": "Password", "verifyButton": "Verify", "verifySuccess": "Your custom \"from\" e-mail address has been successfully set up!", "verifyFailedTitle": "Verification Problem", "verifyFailedBody": "We were unable to set up your custom \"from\" e-mail address. Please confirm your SMTP user name and password are correctly typed in. If you still experience problems, please contact us at"}, "orderx": {"title": "WhatsApps sent to the customer during the [Upper:RO]", "hints": {"reception": "WhatsApp sent automatically to the customer when the [Upper:RO] has been created.", "inspection": "WhatsApp sent automatically to the customer when the first inspection item or job has been added to the [Upper:RO].", "estimateReady": "WhatsApp sent automatically to the customer when the field “Show [Lower:Estimate] to consumer” has been selected in the “Order detail” screen.", "estimateApproval": "WhatsApp sent automatically when the customer approves or declines an inspection item or job."}, "tmpl": {"reception": "Dear [First name and last name of final customer],\nThis is a message from [Name of location customer]. The status of your vehicle is “*Reception in process*”, which means we are creating the necessary documentation for your service.\n\nFor reference, your Order number is [Insert [Upper:RO] number]. We will continue to send updates during this process.", "inspection": "Dear [First name and last name of final customer],\nThis is a message from [Name of location customer]. The status of your vehicle is “*Inspection in process*”, which means we are inspecting your vehicle.\n\nFor reference, your Order number is [Insert [Upper:RO] number]. We will continue to send updates during this process.", "estimateReady": "Dear [First name and last name of final customer],\nThis is a message from [Name of location customer]. The status of your vehicle is “*[Title:Estimate] ready to be approved*”, which means you can review the [Lower:Estimate] prepared by our team.\n\nPlease reply with one of the following options (1, 2, 3 or 4):\n1. To receive a link to your Order.\n2. To receive a PDF document of your Order.\n3. To receive a link and PDF document.\n4. To talk with a [Title:ServiceAdvisor].", "estimateApproval": "Dear [First name and last name of final customer],\nThis is a message from [Name of location customer]. We received your response to the [Lower:Estimate] prepared by our team.\n\nFor reference, your Order number is [Insert [Upper:RO] number]. We will continue to send updates during this process."}, "reception": "Reception in process", "inspection": "Inspection in process", "estimateReady": "[Title:Estimate] ready to be approved", "estimateApproval": "[Title:Estimate] has been approved/declined"}, "phase": {"title": "Modified phase", "phaseSelectPlaceholder": "Select the phase", "tooltip": "The customer will be automatically notified by WhatsApp when the Order is in this phase.", "placeholder": "Enter message here", "addPhase": "Add another phase WhatsApp", "updateToastTitle": "Updated template", "updateToastBody": "Phase notification template saved successfully.", "createToastTitle": "Created template", "createToastBody": "Phase notification template created successfully.", "errorToastBody": "Line breaks are not allowed.", "restore": "Rest<PERSON>", "save": "Save", "template": {"phaseStatus": "Dear [First name and last name of customer],\nThis is a message from [Name of the location]. The status of your vehicle is “[*Phase*]”.", "orderNumber": "For reference, your Order number is [*Number Order*]. We will continue to send updates during this process."}, "whatsappNotificationDelay": "Waiting period before sending WhatsApp about a modified phase", "whatsappNotificationDelayTooltip": "This setting establishes a waiting period before sending WhatsApp notifications when an Order changes phase. This allows team members to correct any accidental change in phase before the notification is sent to the customer.", "seconds": "{{sec}} sec", "sec": "sec"}}, "terminologies": {"header": "Customizable terms", "placeholder": "Enter a term", "updateToastTitle": "Updated term", "updateToastBody": "Term saved successfully.", "invalidLengthTitle": "Unedited term", "invalidLengthBody": "The character limit was exceeded."}, "appointment": {"general": "General", "defaultAppointmentDuration": "Default appointment duration", "activateAutomaticAppointmentNumber": "Activate automatic appointment number", "initialAppointmentNumber": "Initial appointment number", "changeAppointmentStatusAfter": "Change appt. to \"Customer did not arrive\" after:", "valetServiceTitle": "Appointment with valet service", "valetServiceDescription": "A setting will be displayed on the \"New appointment\" screen to indicate whether the appointment requires valet service.", "appointmentOrigin": "Appointment origin", "openingAndClosingSchedule": "Opening and closing schedule", "weekdays": "Weekdays", "openingTime": "Opening time", "closingTime": "Closing time", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "appointmentOriginNotCreated": "Appointment origin not created", "appointmentOriginNotEdited": "Appointment origin not edited", "appointmentOriginIsDuplicated": "Appointment origin is duplicated.", "appointmentOriginCreated": "Appointment origin created", "appointmentOriginSavedSuccessfully": "Appointment origin saved successfully.", "appointmentOriginEdited": "Appointment origin edited", "appointmentOriginDeleted": "Appointment origin deleted", "appointmentOriginRemovedSuccessfully": "Appointment origin removed successfully.", "createNewOrigin": "Create new origin", "defaultAppointmentDurationUpdated": "Default appointment duration updated", "defaultAppointmentDurationSavedSuccessfully": "Default appointment duration saved successfully.", "changeAppointmentStatusAfterUpdated": "Setting updated", "changeAppointmentStatusAfterSavedSuccessfully": "Setting saved successfully.", "initialAppointmentNumberNotSaved": "Initial appointment number not saved", "theInitialAppointmentNumberFieldMustBeMore": "The initial appointment number field must be greater than the last appointment number.", "theInitialAppointmentNumberFieldDoesNotAcceptAnEmptyValue": "The initial appointment number field does not accept an empty value.", "theInitialAppointmentNumberFieldDoesNotAcceptDuplicatedValues": "The initial appointment number field does not accept duplicated values.", "enterAppointmentOrigin": "Enter appointment origin", "siteForAppointments": "Online appointments", "activateSiteForApptsTitle": "Activate online appointments", "activateSiteForApptsDescription": "Your customers can schedule appointments online for reception their vehicles.", "copySiteForApptsLink": "Copy online appointments link", "linkCopiedSuccessfully": "Link copied"}, "siteForAppointments": {"general": "Online appointments", "preview": "Preview", "fields": {"vehicle_brand": "Brand", "vehicle_model": "Model", "vehicle_year": "Year", "vehicle_vin": "VIN", "vehicle_mileage": "[Title:Mileage]", "vehicle_plates": "Plates", "vehicle_color": "Color", "customer_name": "Name(s)", "customer_last_name": "Last name(s)", "customer_lastname": "Last name(s)", "customer_mobile": "Mobile/WhatsApp", "customer_email": "Email", "customer_tax_id": "[Title:TaxIdentification]", "customer_business_name": "Business name", "customer_notes": "Notes"}, "notifications": {"settingsUpdated": "Settings updated", "somethingWentWrong": "Something went wrong", "settingsUpdatedSuccessfully": "Settings updated successfully", "invalidValue": "Invalid value"}}, "orderTypes": {"createNewOrderType": "Create new Order type", "newOrderType": "New Order type", "orderTypeNotCreated": "Order type not created", "orderTypeNotEdited": "Order type not edited", "theOrderTypeIsDuplicated": "The Order type is duplicated.", "orderTypeCreated": "Order type created", "orderTypeEdited": "Order type edited", "orderTypeSavedSuccessfully": "Order type saved successfully.", "enterOrderType": "Enter Order type", "orderType": "ORDER TYPE", "orderTypeCaption": "Order type", "externalOrderTypeCaption": "Order type external ID", "externalOrderTypeCaptionPlaceholder": "Enter the ID assigned in the external system", "lastUpdate": "LAST UPDATE", "editOrderType": "Edit Order type", "saveData": "Save data", "saveOrderType": "Save Order type", "deleteOrderType?": "Delete Order type?", "byDeletingThisOrderTypeItWillBeRemovedFromTheListOfOrderTypes": "By deleting this Order type, <1>it will be removed from the list of Order types.</1>", "deleteOrderType": "Delete Order Type", "orderTypeDeleted": "Order type deleted", "orderTypeDeletedSuccessfully": "Order type deleted successfully.", "color": "COLOR", "orderTypeColor": "Order type color", "orderTypeColorPlaceholder": "Select Order type color", "updatedDateFormat": "MM/DD/YY", "colorInUse": "Color in use"}, "orders": {"autoCloseOrders": {"header": "Automatically close Orders", "hint": "Allow closing Orders with no activity during the configured time and in the selected phases.", "inactivityTime": "Select inactivity time", "selectPhase": "Select the phase", "selectPhasePlaceholder": "Phases", "cancelPopupTitle": "Cancel automatic close Order configuration?", "disablePopupTitle": "Are you sure you want to disable automatic Order closure?"}, "design": {"header": "Orders design", "formType": {"header": "Default view", "cost": "By cost", "priority": "By priority", "system": "By system"}, "accountName": "Account name displayed", "includeEstimate": "Include detailed estimate in the Order sent to the customer", "accountNamePlaceholder": "Enter account name displayed", "accountNameSaved": "Account name displayed saved successfully", "showNA": "Show \"N/A\" items", "showDetails": "Show \"Details\" at inspection items seen by the customer", "approveOnlyEstimated": "Inspection items can only be approved if they have an [Lower:Estimate]", "showCutOrderNum": "Show only last 8 digits for Order#", "taxPercentage": "Calculate taxes when adding [Lower:Estimate]", "taxesSaved": "Percentage saved successfully", "addHourlyCost": "Add standard hourly [Lower:Labor] cost", "costSaved": "<PERSON>st saved successfully", "showServiceAdvisorInfo": "Show [Title:ServiceAdvisor] information", "hideMakeLogos": "Don't show brand logos", "discount": {"header": "Type of discount to apply", "currency": "<PERSON><PERSON><PERSON><PERSON>", "percentage": "Percentage"}}, "photos": {"header": "Photos in Orders", "showOriginal": "Show original photo without arrows", "activateFacebook": "Activate the share option on \"Facebook\""}, "image": {"header": "Customize the image in orders", "dimensionsNote": "The image must have the following dimensions: 220x126", "orderLogoRemoved": "Logo for the Order removed successfully", "orderLogoUpdated": "Order logo updated successfully", "invalidLogo": "Logo for the Order is not valid", "showBrandLogo": "Show vehicle brand logo"}, "orderPriorityText": {"header": "Instructional text for digital Order view by <PERSON>st", "prioritySectionHeader": "Text by priority:", "redPriorityLabel": "“Urgent” priority (Red)", "redPriorityDefaultText": "Recommendations to act on urgently.", "yellowPriorityLabel": "“Suggested” priority (Yellow)", "yellowPriorityDefaultText": "Recommendations to prevent future problems.", "greenPriorityLabel": "“OK” priority (Green)", "greenPriorityDefaultText": "In good condition."}, "timePickerInterval": {"header": "Time selectors", "label": "Time interval between scheduling options", "tooltip": "Define the time interval for displaying available time options. E.g., every 5, 10, 15 minutes"}}, "prospections": {"tabs": {"general": "General", "maintenanceProspection": "Maintenance prospection", "prospectionException": "Prospection exceptions", "importCustomers": "Import customers", "callResults": "Call results", "prospectionSettings": "Prospection Settings"}, "general": {"sendingProspectionMessages": "Sending prospection messages", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "startTime": "Start time", "endTime": "End time", "weSuggestThatMessageSendingSchedule": "We suggest that the message sending schedule is within a regular schedule.", "exampleMondayToFridayFromTo": "<strong>Example:</strong> Monday to Friday from 9:00 am to 6:00 pm.", "prospectionOfUnapprovedInspectionItems": "Prospection of unapproved inspection items", "enableFollowUps": "Activate the prospection of unapproved inspection items", "daysToSendWhatsAppFollowUp": "Days to send WhatsApp follow-up", "days": "days", "day": "day", "deselectedDaysOfTheWeek": "Deselected days of the week", "notAllDaysOfTheWeekCanBeDeselectedPleaseSelectAtLeastOneDay": "Not all days of the week can be deselected, please select at least one day.", "theEndTimeIsEqualToTheStartTime": "The end time is equal to the start time", "theEndTimeCannotBeTheSameAsTheStartTimePleaseSelectAnotherEndTime": "The end time cannot be the same or less than the start time, please select another end time.", "updatedConfiguration": "Updated configuration", "theConfigurationWasSuccessfullyUpdated": "The configuration was successfully updated."}, "maintenance": {"search": "Search", "mileageUnit": "MI", "allBrands": "All brands", "createNewMaintenanceProspectionRule": "Create new maintenance prospection rule", "mileage": "[Upper:Mileage]", "monthsForNextService": "MONTHS FOR NEXT SERVICE", "brand": "BRAND", "model": "MODEL", "year": "YEAR", "month": "month", "months": "months", "form": {"createMaintenanceProspectionRule": "Create maintenance prospection rule", "editMaintenanceProspectionRule": "Edit maintenance prospection rule", "cancel": "Cancel", "createNewRule": "Create new rule", "saveData": "Save data", "mileageForTheNextService": "[Title:Mileage] until next service", "enterTheMileageForProspection": "Enter the [Lower:Mileage] for prospection", "monthsForTheNextService": "Months until next service", "enterTheMonthsForProspection": "Enter the months for prospection", "brand": "Brand", "selectAll": "Select all", "all": "All", "selectABrand": "Select a brand(s)", "model": "Model", "selectAModel": "Select a model(s)", "year": "Year", "selectAYear": "Select a year(s)", "maintenanceProspectionRuleCreated": "Maintenance prospection rule created", "maintenanceProspectionRuleCreatedSuccessfully": "Maintenance prospection rule created successfully.", "maintenanceProspectionRuleNotCreated": "Maintenance prospection rule not created", "theMaintenanceProspectionRuleIsDuplicated": "The maintenance prospection rule is duplicated.", "maintenanceProspectionRuleEdited": "Maintenance prospection rule edited", "maintenanceProspectionRuleSavedSuccessfully": "Maintenance prospection rule saved successfully.", "maintenanceProspectionRuleNotEdited": "Maintenance prospection rule not edited", "allBrands": "All brands", "allModels": "All models", "allYears": "All years", "selectSpecificBrands": "Select specific brands", "selectSpecificModels": "Select specific models", "selectSpecificYears": "Select specific years"}, "deleteMaintenanceProspectionRule": "Delete maintenance prospection rule?", "byDeletingThisMaintenanceRuleItWillRemovedFromTheListOfMaintenanceProspectionRule": "By deleting this maintenance prospection rule, <strong>it will be removed from the list of maintenance prospection rules.</strong>", "deleteProspectionRule": "Delete prospection rule", "maintenanceProspectionRuleDeleted": "Maintenance prospection rule deleted", "maintenanceProspectionRuleDeletedSuccessfully": "Maintenance prospection rule deleted successfully."}, "importCustomers": {"uploadFile": "Upload file", "downloadTemplate": "Download template", "notes": "Notes", "acceptedFormat": "Accepted format .xslx", "maximumAllowedSize": "Maximum allowed size of the document 2 MB", "incorrectFormatTitle": "Document not uploaded", "incorrectFormatBody": "The format of customer's list is not correct, please use the indicated format.", "invalidRowsTitle": "There are records that were not uploaded", "invalidRowsBody": "The register for row {{rowN<PERSON>ber}} was not uploaded, please check that the data is correct.", "manyInvalidRowsTitle": "There are records that were not uploaded", "manyInvalidRowsBody": "Approximately {{rowsCount}} records were not uploaded; please verify that the data is correct.", "successTitle": "Document uploaded", "successBody": "The customers list has been uploaded successfully.", "templateFileName": "List of customers.xlsx"}}, "workshopPlannerSettings": {"title": "Workshop Planner settings", "tabs": {"general": "Generals", "plannings": "Plannings"}, "general": {"UTsEqualTo": "1 hour of time is equal to:", "UTsSaved": "UT´s saved correctly", "maximumNumberOfDigitsForTowerNumber": "Maximum number of digits to display for tower number", "maximumNumberOfDigitsForTowerNumberSaved": "Maximum number of digits for tower number saved correctly", "maximumNumberOfDigitsForTowerNumberHint": "The quantity selected will determine the digits of the Order or tower number that will be displayed within the tower oval.", "maximumNumberOfDigitsForOrderAndAppointment": "Maximum number of digits to display for Order/Appointment number", "maximumNumberOfDigitsForOrderAndAppointmentSaved": "Maximum number of digits for Order and Appointment number saved correctly", "maximumNumberOfDigitsForOrderAndAppointmentHint": "The quantity selected will determine the final digits of the Order and Appointment number that will be displayed in the block.", "airportScreenView": "Airport screen view", "airportScreenViewOptions": {"verticalOption": "Vertical", "horizontalOption": "Horizontal", "verticalOptionHint": "You can see the team members at the top of the screen and the work schedule on the left side.", "horizontalOptionHint": "You can see the team members on the left side of the screen and the work schedule at the top."}, "showTeamMemberSpecialty": "Show team member [Lower:<PERSON>ty]", "defaultScheduledDuration": "Default duration for the \"Scheduled duration\" field", "defaultScheduledDurationHint": "The default duration will appear in the \"Scheduled duration\" field when adding a job to the Order and the appointment.", "showTechnicianCapacity": "Show \"Actual Technical Capacity\" metric", "maximumTechnicianCapacity": "Maximum Actual Technical Capacity", "allowSchedulingWithMaximum": "Allow scheduling job upon reaching the Maximum Actual Technical Capacity", "showTechnicianCapacityTooltip": {"mainText": "Manage the Actual Technical Capacity in the Workshop Planner to ensure availability for urgent jobs and those without an appointment, and optimize the workload.", "technicalCapacity": "Actual Technical Capacity", "totalTechnicians": "Total Technicians", "availableHours": "Available hours", "workDays": "Work days", "technicalProductivity": "Technical Productivity", "productiveHours": "Productive hours", "workedHours": "Worked hours"}, "showWorkshopUtilizationMetric": "Show \"Workshop Utilization\" metric", "showWorkshopUtilizationMetricTooltip": "Metric that shows the calculation of the workshop's utilization and its potential capacity.", "productiveWorkspace": "Productive workspaces", "completedJobsTypeSetting": "Calculate metric with jobs completed according to:", "completedJobsTypeSettingHint": "Defines how completed jobs are considered for the calculation of the \"Technical Productivity\" and \"Actual Technical Capacity\" metrics in Workshop Planner.", "completedJobsTypeSettingOptions": {"scheduledTime": "Scheduled time", "scheduledTimeHint": "A job is considered completed when its scheduled end time has passed and it has not been deleted.", "actualTimeSpent": "Actual time spent", "actualTimeSpentHint": "The job is only considered completed if the technician used the \"Start\" and \"Finish\" buttons in the web app or mobile app."}, "showTowerColor": {"title": "Show tower color", "byServiceAdvisor": "By Service Advisor", "byOrderType": "By Order type", "byReasonForAppointment": "By [Lower:ReasonForVisit]", "infoTooltipText": "Select how to display the tower color based on the colors assigned to the Service Advisor, Order Type, and [Lower:ReasonForVisit].", "reasonForAppointmentInactive": "Activate customizable [Lower:ReasonsForVisit] in \"Settings / General / [Sentence:ReasonForVisit]\" to configure the tower color and job blocks based on the [Lower:ReasonForVisit]."}, "showJobBlockColor": {"title": "Show job block color", "byServiceAdvisor": "By Service Advisor", "byOrderType": "By Order type", "byReasonForAppointment": "By [Lower:ReasonForVisit]", "noColor": "No color", "infoTooltipText": "Select how to display the color of job blocks for Orders and appointments in planning, based on the colors assigned to the Service Advisor, Order type, and [Lower:ReasonForVisit].", "reasonForAppointmentInactive": "Activate customizable [Lower:ReasonsForVisit] in \"Settings / General / [Sentence:ReasonForVisit]\" to configure the tower color and job blocks based on the [Lower:ReasonForVisit]."}, "notifications": {"settingsUpdatedSuccessfully": "Settings updated successfully"}, "incompleteColorConfigurationModal": {"title": "Incomplete color configuration", "body": "Cannot apply \"{{configuration}} {{selectedOption}}\" option. Complete the color setup before retrying.", "goToSettings": "Go to settings", "cancel": "Cancel", "ShowTowerColorType": "Show tower color", "ShowJobBlockColorType": "Show job block color", "ByOrderType": "by Order type", "ByReasonForAppointment": "by [Lower:ReasonForVisit]"}, "standardOperations": {"title": "Allow adding \"Operation code\" and \"Standard time\" in the job", "tooltip": "The fields \"Operation code\" and \"Standard time\" will be added to the \"Add job\" and \"Edit job\" pop-ups in the web app, exclusively for the selected Order types. Both fields will be mandatory to create or edit a job for those Order types."}, "technicianSignatureForJob": {"title": "Add signature of the Technician at job", "tooltip": "The Technician's signature will be added to the “Job detail” screen in the mobile app for the selected Order types. The Technician's signature will be mandatory when stopping the job timer."}, "urgentFlashingIndication": {"title": "Order flashes yellow for items marked “Urgent”", "inspectionFormToConsider": "Inspection form to consider:", "infoTooltipText": "Orders flash yellow when the items of the selected inspection form are marked with “Urgent” priority (Red).", "selectInspectionForm": "Select inspection form", "explanatoryTextLabel": "Explanatory text to display in Workshop Planner", "explanatoryTextTooltip": "Text that explains the meaning of the yellow flashing in the Workshop Planner Order blocks."}}, "pausedOrders": {"title": "Paused Orders", "highlightOrdersBy": "Highlight Orders by:", "deliveryPromiseDate": "Delivery promise date", "deliveryPromiseDateTooltip": "Paused Orders will blink in red when the delivery promise date has passed.", "daysPaused": "Days paused", "daysPausedTooltip": "Paused Orders will blink in yellow as a preventive warning and in orange as an urgent alert when the configured number of days paused is reached or exceeded. The urgent alert should be higher than the preventive warning.", "daysUntilPauseWarning": "Days until pause warning:", "preventive": "Preventive", "urgent": "<PERSON><PERSON>", "days": "days", "day": "day", "urgentShouldBeHigherWarningTitle": "Urgent alert should be higher", "urgentShouldBeHigherWarningText": "Than the preventive warning"}, "planning": {"Advisors": "Advisors", "Technicians": "[Title:Technicians]", "addPlanning": "Add planning", "notifications": {"planningUpdatedSuccessfullyTitle": "Updated planning", "planningUpdatedSuccessfullyBody": "Planning saved successfully.", "planningDeletedSuccessfullyTitle": "Updated planning", "planningDeletedSuccessfullyBody": "Planning deleted successfully.", "planningAlreadyExistsTitle": "Duplicate planning", "planningAlreadyExistsBody": "The planning already exists.", "planningCreatedTitle": "Planning created", "planningCreatedBody": "Planning saved successfully.", "planningEditedTitle": "Planning edited", "planningEditedBody": "Planning saved successfully."}, "createModal": {"title": "New planning", "nameHeader": "Planning name", "namePlaceholder": "Enter the name", "selectTeamMembersHeader": "Select team members", "selectTeamMembersPlaceholder": "Select team members", "allTeamMembers": "All team members", "cancelButton": "Cancel", "createNewButton": "Create new planning"}, "editModal": {"title": "Edit planning", "nameHeader": "Planning name", "namePlaceholder": "Enter the name", "selectTeamMembersHeader": "Select team members", "selectTeamMembersPlaceholder": "Select team members", "allTeamMembers": "All team members", "cancelButton": "Cancel", "saveChanges": "Save changes"}, "popup": {"deletePlanningTitle": "Delete planning", "deletePlanningBody": "Do you want to delete this planning?", "doNotDeletePlanning": "Do not delete", "deletePlanning": "Delete planning"}}}, "customizableFields": {"header": "Customizable fields", "addField": "New field", "addColumn": "New column", "sections": {"WebLocationGeneralInfo": "Web dashboard / Location info", "WebLocationGeneralInfo-header": "General information", "WebLocationServiceHoursInfo": "Web dashboard / [Sentence:ServiceHours]", "WebLocationServiceHoursInfo-header": "[Sentence:ServiceHours]", "WebCustomerInfo": "Web dashboard / Customer information", "WebCustomerInfo-header": "Customer information", "WebVehicleInfo": "Web dashboard / Vehicle information", "WebVehicleInfo-header": "Vehicle information", "WebOrderInfo": "Web dashboard / Order information", "WebOrderInfo-header": "Order information", "WebJobInfo": "Workshop Planner / \"More job information\" pop-up", "WebJobInfo-header": "More job information", "WebParts": "Web dashboard / [Title:Parts]", "WebParts-header": "[Title:Parts]", "WebLabor": "Web dashboard / [Title:Labor]", "WebLabor-header": "[Title:Labor]", "MobileOrder": "Mobile app / Order", "MobileOrder-header": "Order", "MobileCustomer": "Mobile app / Customer", "MobileCustomer-header": "Customer", "MobileVehicle": "Mobile app / Vehicle", "MobileVehicle-header": "Vehicle", "MobileJobInfo": "Mobile app / \"More job info\" screen in Order detail", "MobileJobInfo-header": "More job information"}, "predefined": {"location_address": "Address", "location_phoneNumber": "Phone number", "location_name": "Location name", "location_emailAddress": "Email address", "location_website": "Website", "location_taxIdentification": "[Title:TaxIdentification]", "location_legalName": "Legal name", "location_mondayToFridayHours": "Monday to Friday", "location_saturdayHours": "Saturday", "customer_name": "Name", "customer_email": "Email", "customer_landline": "Landline", "customer_mobile": "Mobile", "customer_id_document": "Identification document", "customer_business_name": "Business name", "customer_payment_method": "Payment method", "vehicle_brand": "Brand", "vehicle_model": "Model", "vehicle_year": "Year", "vehicle_vin": "VIN", "vehicle_mileage": "[Title:Mileage]", "vehicle_plates": "Plates", "vehicle_color": "Color", "order_phase": "Phase", "order_tower": "Tower", "order_type": "Order type", "order_in_charge": "Service Advisor", "order_assigned_to": "Assigned to", "order_note": "Note", "order_technician": "Technician", "order_additionalPositionOne": "Additional position 1", "order_additionalPositionTwo": "Additional position 2", "order_additionalPositionThree": "Additional position 3", "order_additionalPositionFour": "Additional position 4", "order_delivery_date": "[Sentence:PromisedDeliveryDateAndTime]", "job_team_member": "Team member", "job_employee_id": "Employee ID", "job_planning": "Planning", "job_start_date": "Start date", "job_start_time": "Start time", "job_scheduled_duration": "Scheduled duration", "job_time_units_sold": "Time units sold", "job_job_description": "Job description", "parts_number": "[Upper:PartNumber]", "parts_qty": "QTY.", "parts_avail": "AVAIL.", "parts_cost_unit": "COST U.", "parts_price_unit": "PRICE U.", "labor_hrs": "HRS.", "labor_price_hr": "PRICE HR.", "mob_order": "Order", "mob_order_tower": "Tower", "mob_order_type": "Order type", "mob_order_delivery_day": "Delivery day", "mob_order_delivery_hour": "Delivery hour", "mob_order_record_notes": "Record notes", "mob_order_enter_notes": "Enter notes", "mob_customer_first_name": "First name", "mob_customer_last_name": "Last name", "mob_customer_mobile": "Mobile", "mob_customer_email": "E-mail", "mob_customer_landline": "Landline", "mob_vehicle_brand": "Brand", "mob_vehicle_model": "Model", "mob_vehicle_year": "Year", "mob_vehicle_mileage": "[Title:Mileage]", "mob_vehicle_scan_plates": "Scan plates", "mob_vehicle_enter_plates": "Enter plates", "mob_vehicle_scan_vin": "Scan VIN", "mob_vehicle_enter_vin": "Enter VIN", "mob_job_team_member": "Team member", "mob_job_planning": "Planning", "mob_job_job_description": "Job description"}, "createNewModal": {"header": "New field", "headerColumn": "New column", "addField": "Add field", "addColumn": "Add column", "name": "Field name", "namePlaceholder": "Enter the field name", "nameColumn": "Column name", "namePlaceholderColumn": "Enter the column name", "type": "Field type", "typePlaceholder": "Select field type", "typeColumn": "Column type", "typeColumnPlaceholder": "Select column type", "additionalSection": "Add customizable field to another section", "additionalSectionSubtitle": "The field will be added to the “{{main}}” and “{{additional}}” sections.", "hasExtraText": "Add free text field at the end", "hasExtraTextSubtitle": "This field will have an additional free text field.", "requiredMobile": "Required in the mobile app", "requiredMobileSubtitle": "This field must be completed in the mobile app.", "mandatoryField": "Mandatory field", "mandatoryFieldSubtitle": "This field will be required when stopping the job.", "jobColor": "Job color", "jobColorSubTitle": "Assign job color according to this field.", "nameConflict": {"title": "Duplicate field name", "text": "The field name already exists in a different field."}, "successNotif": {"title": "Field created", "text": "<PERSON> saved successfully."}, "successNotifColumn": {"title": "Column created", "text": "<PERSON>umn saved successfully."}}, "editModal": {"header": "Edit field", "headerColumn": "Edit column", "save": "Save changes", "successNotif": {"title": "<PERSON> edited", "text": "<PERSON> saved successfully."}, "successNotifColumn": {"title": "<PERSON><PERSON><PERSON> edited", "text": "<PERSON>umn saved successfully."}}, "deleteModal": {"fieldTitle": "Delete field", "columnTitle": "Delete column", "fieldBody": "Do you want to delete the field <name>“{{name}}”</name>?", "columnBody": "Do you want to delete the column <name>“{{name}}”</name>?", "columnNotif": {"title": "Column deleted", "text": "Column deleted successfully."}, "fieldNotif": {"title": "Field deleted", "text": "Field deleted successfully."}}, "multipleChoiceSection": {"selectionType": "Selection Type", "selectionTypeInfo": "Select the type of selection for the dropdown list", "multipleChoiceSingleOption": "Allow selection of a single option", "multipleChoiceMultipleOption": "Allow selection of multiple options", "enterTheOptions": "Enter the options", "jobColor": "Job color", "defaultOption": "Option", "addExternalId": "Add external ID", "addExternalIdTooltip": "Enter a unique identifier for this option. The ID can be up to 36 characters long and is used for reference.", "addAnotherOption": "Add another option", "jobColorPlaceholder": "Select job type color"}, "orderTypesSelector": {"allOrderTypes": "All Order types", "label": "Type(s) of Order(s) where the field will appear"}}, "customAppointmentReasons": {"importAppointmentReasonDetail": {"importAppointmentReasonDetails": "Import [Lower:ReasonForVisit] details", "goBack": "Go back", "uploadFile": "Upload file", "downloadTemplate": "Download template", "notes": "Notes", "acceptedFormat": "Accepted format .xslx", "maximumAllowedSize": "Maximum allowed size of the document 10 MB", "documentUploadedTitle": "Document uploaded", "documentUploadedText": "The list of [Lower:ReasonForVisit] details has been uploaded successfully.", "documentNotUploadedTitle": "Document not uploaded", "documentNotUploadedText": "The [Lower:ReasonForVisit] details list was not loaded; please review the data and try again.", "cancelXlsxFileUpload": "Cancel .xlsx file upload?", "yesCancel": "Yes, cancel", "dragOrClickHereToUploadYourExcelFile": "Drag or click here to upload your Excel file"}}, "packages": {"activate": {"title": "Activate packages", "description": "It will allow the creation and use of customizable packages."}, "search": "Search package", "import": "Import package", "create": "Create package", "instruction": {"mainText": "No packages registered!", "secondaryText": "Please start creating packages."}, "table": {"name": "Package name", "brand": "Brand", "model": "Model", "year": "Year", "all": "All"}, "editOrCreate": {"createPackage": "Create package", "editPackage": "Edit package", "packageName": "Package name", "packageNamePlaceholder": "Enter package name", "inspectionItemLabel": "Link inspection item or Job", "inspectionItemPlaceholder": "Type or select inspection item or job", "partsNumber": "[Title:Parts] - Part #", "partsNumberPlaceholder": "Enter part #", "quantity": "[Title:Parts] - <PERSON><PERSON>", "quantityPlaceholder": "Enter quantity", "price": "[Title:Parts] - <PERSON><PERSON>", "pricePlaceholder": "Enter unit price", "hours": "[Title:Labor] - Hours", "hoursPlaceholder": "Enter hours", "hourPrice": "[Title:Labor] - Price Hr.", "hourPricePlaceholder": "Enter price hr.", "description": "Description", "addPackageItem": "Link another inspection item or job", "createdNotif": "Package created successfully", "updatedNotif": "Package edited successfully", "subItemDescriptionPlaceholder": "Enter subitem description", "cancelPackageCreation": "Cancel package creation?", "cancelPackageEditing": "Cancel package editing?"}, "deletePopup": {"title": "Delete package?", "body": "Deleting this package <1>will remove it from the list of packages.</1>", "deleteButton": "Delete Package", "deletedSuccessfully": "Package deleted successfully."}, "importModal": {"uploadFile": "Upload file", "notes": "Accepted format .xslx\nMaximum allowed size of the document 10 MB", "downloadTemplate": "Download template", "success": {"title": "Document uploaded", "text": "The list of package has been successfully uploaded."}, "error": {"title": "Document not uploaded", "text": "The list of package was not uploaded; please review the data and try again."}, "cancel": "Cancel .xlsx file upload?", "conflict": {"text": "There is a conflict in the \"{{columns}}\" column. Remove the incorrect data from the corresponding cell.", "title": "The \"{{packageName}}\" package could not be imported", "descriptionColumns": "Description", "itemName": "Link item or job"}, "mandatoryColMissing": {"text": "It has mandatory columns without information.", "title": "File not uploaded"}}}}, "customizableFields": {"types": {"predefined-Dropdown": "Dropdown", "predefined-Scan": "<PERSON><PERSON>", "predefined-RecordAudio": "Audio recording", "ShortText": "Short text field", "LongText": "Long text field", "Numeric": "Numeric", "Date": "Date", "Time": "Time", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "MultipleChoice": "Multiple choice", "MultiSelect": "Multiple choice"}, "placeholders": {"select": "Select"}}, "toasters": {"oneMoment": "One moment, please", "errorOccurred": "Error occurred", "errorOccurredWhenLoading": "Error occurred when loading data", "errorOccurredWhenSaving": "Error occurred when saving data", "unexpectedError": "Unexpected error occurred", "settingUpdated": "Setting updated", "settingSuccessfullyUpdated": "Setting successfully updated", "networkErrorTitle": "Failed action", "networkErrorBody": "There has been a connection problem and the action could not be executed correctly.<br><br>Check the internet connection before doing the action again", "settingUpdateFailure": "Setting cannot be updated", "fileNotSupported": "File not supported", "updatedConfiguration": "Updated configuration", "configurationChangedSuccessfully": "The configuration has been changed successfully", "noResultsFoundFor": "No results found for", "insufficientPermissionsTitle": "Insufficient permissions", "insufficientPermissionsBody": "You don't have permissions to see this page", "disabledFeatureTitle": "Feature is disabled", "disabledFeatureBody": "This feature is disabled in your account", "documentPartiallyUploadedTitle": "Document partially uploaded", "documentPartiallyUploadedText": "{{importedRows}} of {{totalRows}} records were uploaded </br> <a href=\"{{failedRowsFileUrl}}\">View the records that were not uploaded</a>"}, "communication": {"sendEmail": "Send E-mail", "sendEmailWithNote": "Send E-mail with note", "sendEmailWithNoteDesc": "Popup description", "sendSms": "Send SMS", "sendSmsWithNote": "Send SMS with note", "sendSmsWithNoteDesc": "Popup description", "sendWa": "Send WhatsApp", "sendWaSmsEmail": "Send WA, SMS and E-mail", "sendWaSms": "Send WA and SMS", "sendWaEmail": "Send WA and E-mail", "sendSmsEmail": "Send SMS and E-mail", "sendMessage": "Send message", "sendOrder": "Send Order", "callCustomer": "Call customer", "callLandline": "Call landline", "callMobile": "Call mobile", "viewCallsLog": "View calls log", "messageSent": "Your message has been sent successfully!"}, "sendEmail": {"sendEmail": "Send E-mail with personalized note", "writeTheMessage": "Write the message with the personalized note you want to send.", "email": "E-mail", "emailPlaceholder": "<EMAIL>", "message": "Message", "attachFile": "Attach file", "shortcuts": "Shortcuts", "insertLink": "Insert Link", "writeComment": "Write a comment here.", "sendFailedTitle": "Send failed", "sendFailedBody": "E-mail sending failed, please try again."}, "sendSms": {"sendFailedTitle": "Send failed", "sendFailedBody": "Message sending failed, please try again.", "shortcuts": "Shortcuts", "sendSms": "Send SMS with personalized note", "writeTheMessage": "Write the message with the personalized note you want to send.", "number": "Number", "numberPlaceholder": "Enter customer's mobile", "message": "Message", "insertLink": "Insert Link", "writeComment": "Write a comment here."}, "errorMessages": {"invalidMailTitle": "Invalid Mail", "invalidMailBody": "Please enter a valid mail.", "wrongPasswordTitle": "Incorrect username or password", "wrongPasswordBody": "Please try again.", "passwordsDontMatchTitle": "Passwords don't match", "passwordsDontMatchBody": "Make sure that confirm password is the same than password.", "recaptchaErrorTitle": "ReCAPTCHA", "recaptchaErrorBody": "Your actions indicate that you might be a bot, we're unable to process your request", "userDoesntExistTitle": "Username does not exist", "userDoesntExistBody": "Make sure you have entered the username correctly", "requestThrottledTitle": "Limit reached", "requestThrottledBody": "You have reached the limit of password resets for this email. Try again in 24 hours.", "userNotFound": "User not found"}, "notFound": {"haveProblem": "HOUSTON, WE HAVE A PROBLEM!", "weCanNotFind": "We can't find the page you requested.", "weCanNotFindOrderDetail": "We could not find the Order {{orderNumber}}, please, try again later", "goBack": "Go back"}, "passwordValidation": {"notification": {"title": "Error resetting password", "lowercase": "Password must contain at least 1 lowercase letter.", "uppercase": "Password must contain at least 1 uppercase letter.", "empty": "Password field cannot be empty.", "length": "Password must be at least 8 characters long.", "digits": "The password must have at least one numeric digit.", "special": "Password must contain at least 1 special character. (!,@,#,$,%,^,&,*,?, etc.)", "equal": "The password and confirmation password do not match."}, "label": {"lowercase": "You must include at least one lowercase letter", "uppercase": "You must include at least one uppercase letter", "digits": "You must include at least one number", "special": "You must include at least one special character (!,@,#,$,%,^,&,*,?, etc.)", "length": "Your password must have at least 8 characters"}, "secure": "Safe password"}, "accountDeactivated": {"somethingDisconnected": "Oh oh! It seems that something has disconnected", "accountIsNotActivated": "Your account is not activated.", "executiveCanHelp": "But don't worry, your Account Executive can help you.", "contact": "Contact Account Executive", "supportForm": {"header": "Do you need help?", "email": "Email", "emailPlaceholder": "Enter your contact email here.", "message": "Detail of the problem", "messagePlaceholder": "Please describe the details of your problem here.", "send": "Send", "enterValidUserName": "Please enter a valid username.", "invalidUserName": "Invalid username"}}, "helpEmailSent": {"great": "Great!", "notified": "our account executive has already been notified, he will contact you soon."}, "timePassed": {"secondAgo": "1 second ago", "xSecondsAgo": "{{seconds}} seconds ago", "minuteAgo": "1 minute ago", "xMinutesAgo": "{{minutes}} minutes ago", "hourAgo": "1 hour ago", "xHoursAgo": "{{hours}} hours ago", "dayAgo": "1 day ago", "xDaysAgo": "{{days}} days ago"}, "newVehicle": {"platesPlaceholder": "Enter plates", "brandPlaceholder": "Slide or type the brand", "modelPlaceholder": "Slide or type the model", "year": "Year", "yearPlaceholder": "Slide or type the year", "colorPlaceholder": "Enter color", "color": "Color", "areYouSure": {"yes": "Yes, cancel", "subtitle": "The changes made will not be saved.", "title": "Are you sure you want to cancel vehicle creation?"}, "title": "New vehicle", "create": "Create vehicle", "vehicleCreated": "Vehicle created", "vehicleCreatedSuccessfully": "The vehicle has been created successfully", "vehicleNotCreated": "Vehicle not created", "vehicleDuplicated": "The customer's vehicle was previously registered, the customer cannot have duplicate vehicles.", "vinDuplicated": "This VIN is already registered to another vehicle.", "platesDuplicated": "This license plate is already registered to another vehicle.", "vinAndPlatesDuplicated": "This plates and VIN are already registered to another vehicle.", "brandModelYearDuplicated": "A vehicle with the same brand, model, and year already exists for this customer. Add VIN or plates to differentiate it."}, "vehicleAutocomplete": {"selectAVehicle": "Select a vehicle", "missingVehicleNotice": "This vehicle no longer belongs to selected customer"}, "newCustomer": {"vehicleNotice": "To create a new vehicle, please provide at least a license plate, VIN, or brand + model + year.", "title": "New customer and vehicle", "requiredFields": "Required fields", "goBack": "Go back", "create": "Create customer and vehicle", "name": "Name", "namePlaceholder": "Enter name", "lastName": "Last names", "lastNamePlaceholder": "Enter last names", "email": "E-mail", "emailPlaceholder": "Enter e-mail", "mobile": "Mobile", "mobilePlaceholder": "Enter mobile", "taxIdent": "[Sentence:TaxIdentification]", "taxIdentPlaceholder": "Enter [Lower:TaxIdentification]", "platesPlaceholder": "Enter plates", "brandPlaceholder": "Slide or type the brand", "modelPlaceholder": "Slide or type the model", "year": "Year", "yearPlaceholder": "Slide or type the year", "colorPlaceholder": "Enter color", "color": "Color", "businessName": "Business name", "businessNamePlaceholder": "Enter business name", "areYouSure": {"yes": "Yes, cancel", "subtitle": "The changes made will not be saved.", "title": "Are you sure you want to cancel the customer creation?"}, "created": "Customer and vehicle created", "createdBody": "The customer and vehicle have been created successfully.", "notCreated": "Customer not created", "notCreatedBody": "This mobile number is already associated with another customer.", "changeCustomer": "(Change customer)", "noCustomersText": "No customers found. Please verify the information, try another search, or fill in the fields to create a new one."}, "callLog": "Call log", "locations": {"allLocations": "All locations", "selectLocation": "Select a location", "locationsWithCount_one": "{{count}} location", "locationsWithCount_other": "{{count}} locations", "location": "Location", "locationSwitchWarning": {"title": "Note about changing the “Location” field", "text": "If the “Location” field is changed to a different location, the “Customer” field is also reset. The reason is that the “Customer” information is linked to a specific location.", "yes": "Yes, change the location"}}, "appointments": {"accessDeniedText": "I'm sorry, you don't have permission to create appointments.", "accessDeniedHint": "You can update your permissions from the \"Settings\" screen.", "gettingAppts3rdParty": "We are getting appointments from {{integratedAccountName}}.", "get3rdParty": {"success": {"title": "Appointments successfully synchronized", "description": "Appointments have been successfully synchronized from {{integratedAccountName}}."}, "error": "An error occurred while retrieving appointments from {{integratedAccountName}}."}, "convertToOrder": "Convert to Order", "allAppointments": "All Appointments", "allAdvisors": "All Advisors", "filters": {"filters": "Filters", "selectFilters": "Select filters", "apply": "Apply", "view": "View", "status": "Status", "advisors": "Advisors"}, "newAppointment": "New appointment", "editAppointment": "Edit appointment", "requiredFields": "Required fields", "createAppointment": "Create Appointment", "saveChanges": "Save changes", "cancel": "Cancel", "pleaseSelectLocationFirst": "Please select the location first", "shopTzNotice": "Consider that the hours shown correspond to the time zone of the location", "repairOrderExists": "Repair order with this number already exists", "step1": {"step1": "STEP 1", "selectACustomer": "Select a customer", "searchAndSelectCustomerByNameMobileOrPlates": "Search and select customer by name, mobile or plates", "customerName": "Customer name", "name": "Name", "lastNames": "Last names", "mobile": "Mobile", "email": "E-mail", "taxIdentification": "[Sentence:TaxIdentification]", "plates": "Plates", "noPlate": "No plate", "noPlates": "No plates", "noVin": "No VIN", "brand": "Brand", "noBrand": "No brand", "model": "Model", "year": "Year", "color": "Color", "createNewCustomer": "Create new customer", "createNewVehicle": "Create new vehicle", "frequentCustomer": "Frequent customers", "vehicle": "Vehicle"}, "step2": {"step2": "STEP 2", "selectTheReasonForTheAppointment": "Select the [Lower:ReasonForVisit]", "reasonForTheAppointment": "[Sentence:ReasonForVisit]", "workshopReasonForAppointment": "[Sentence:WorkshopReasonForVisit]", "customerReasonForAppointment": "[Sentence:CustomerReasonForVisit]", "selectOrAddTheReasonForTheAppointment": "Select or add the [Lower:ReasonForVisit]", "addReasonForAppointment": "Add another [Lower:ReasonForVisit]", "reasonDetailsForTheAppointment": "[Sentence:ReasonForVisit] detail", "selectTheReasonDetailsForTheAppointment": "Select the [Lower:ReasonForVisit] detail", "seeAll": "See all", "save": "Save", "reasonsForTheAppointment": "[Sentence:ReasonsForVisit]", "listOfAllExistingReasonsForTheAppointment": "List of all existing [Sentence:ReasonForVisit]", "search": "Search", "frequentReasons": "Frequent reasons", "appointmentReasonDuplicated": "The [Lower:ReasonForVisit] is duplicated"}, "step3": {"step3": "STEP 3", "selectTheTimeAndDateOfTheAppointment": "Select the time and date of the appointment", "selectTheDate": "Select the date", "selectTheServiceAdvisorInCharge": "Select the [Title:ServiceAdvisor] in charge", "anyServiceAdvisor": "Any [Title:ServiceAdvisor]", "selectTime": "Select time", "thereIsNoAvailability": "There is no availability to schedule appointments for this day, please select another day.", "theClientCouldNotBeEdited": "The client could not be edited", "theNameCannotBeEmpty": "The Name cannot be empty", "theMobileCannotBeEmpty": "The Mobile cannot be empty", "theMobileIsRegistered": "The Mobile is already registered"}, "personWhoSchedules": "Person who schedules", "appointmentNumber": "Appointment number", "appointmentNumberIsDuplicated": "Appointment number is duplicated", "invalidMobileNumberError": "Mobile phone in wrong format", "appointmentNumberAlreadyTaken": "Appointment number already taken", "weSuggestUsingAnotherNumber": "We suggest using another number", "status": {"appointmentStatus": "Appointment status", "unconfirmed": "Unconfirmed", "confirmed": "Confirmed", "confirmedDisabled": "This status can only be selected once the customer has confirmed their attendance in the appointment reminder message.", "customerArrived": "Customer arrived", "customerDidNotArrive": "Customer did not arrive", "orderCreated": "Order created", "orderCreatedDisabled": "This status can only be selected once the appointment has been converted into an Order.“", "onTime": "On time", "delayed": "Delayed"}, "appointmentFiles": {"deleteFile": "Delete file", "deleteFileQuestion": "Delete file?", "attachedFiles": "Attached files", "attachFile": "Attach file", "noAttachedFiles": "No attached files", "downloadFile": "Download file", "areYouSureDelete": "Are you sure you want to delete this file?", "fileDeleted": "File deleted", "noDelete": "Don't delete", "fileSizeExceeded": "File size exceeded", "fileSizeExceeded10Mb": "File size exceeds 10 MB. Please reduce the size."}, "appointmentOrigin": "Appointment origin", "valetService": "Valet service", "selectAnOrigin": "Select an origin", "date": "Promised date", "selectADate": "Select a date", "time": "Promised time", "selectAHour": "Select a hour", "observations": "Observations", "enterTheObservations": "Enter the observations", "enterTheAppointmentNumber": "Enter unique appointment number", "itIsAssignedAutomatically": "It is assigned automatically", "deleteTitle": "Delete appointment?", "delete": "Delete appointment", "orderNumber": "Order number", "location": "Location", "customerName": "Customer name", "e-mail": "E-mail", "mobile": "Mobile", "vehicle": "Vehicle", "duration": "Duration", "serviceAdvisor": "[Sentence:ServiceAdvisor]", "reasonForTheAppointment": "[Sentence:ReasonForVisit]", "customerArrived": "Customer arrived", "customerDidNotArrive": "Customer did not arrive", "cancelNewAppointmentRegister": "Cancel new appointment register?", "appointmentDeleted": "Appointment deleted", "theAppointmentHasBeenDeletedSuccessfully": "The appointment has been deleted successfully.", "cancelAppointmentEditing": "Cancel appointment editing?", "deleteAppointmentTooltip": "Delete appointment", "appointmentUpdated": "Appointment updated", "confirm": "Confirm", "goBack": "Go back", "confirmCustomerDidNotArrive": "Confirm customer did not arrive", "confirmCustomerArrived": "Confirm customer arrived", "didNotArriveForHisAppointment": "did not arrive for his appointment", "arrivedForHisAppointment": "arrived for his appointment", "from": "from", "appointmentCreated": "Appointment created", "scheduleNewAppointmentOrAbsence": "Click to schedule appointment or absence", "scheduleNewAppointment": "Schedule new appointment", "editAbsence": "Edit absence", "scheduleAbsence": "Schedule absence", "backToToday": "Today", "updateCustomer": "Update customer", "confirmCustomerUpdateText": "The existing customer and/or vehicle information has been modified. Are you sure you want to save the changes?", "week": "Week", "day": "Day", "notes": "Notes", "notesVisibleForCustomer": "Notes visible to the customer", "internalNotes": "Internal notes", "noNotesForCustomer": "No notes visible to the customer.", "noInternalNotes": "No internal notes.", "enterNotes": "Enter notes", "editNote": "Edit", "deleteNote": "Delete", "noteActivityLog": "Activity log", "noteCannotBeEdited": "This text cannot be edited.", "appointmentCreatedInYourIntegratedSoftwareFirst": "Appointment created successfully in {{integratedAccountName}}.", "appointmentCreatedInYourIntegratedSoftware": "Appointment created in your integrated software.", "appointmentUpdatedInYourIntegratedSoftware": "Appointment updated in your integrated software.", "appointmentDeletedInYourIntegratedSoftware": "Appointment deleted in your integrated software.", "appointmentMustBeCreatedUsing3rdPartySoftware": "The appointment must be created using your third-party software.", "appointmentMustBeEditedUsing3rdPartySoftware": "The appointment must be edited using your third-party software.", "omnichannelModal": {"appointmentNotCreatedInYourIntegratedSoftware": "Appointment not created in your integrated software.", "appointmentNotUpdatedInYourIntegratedSoftware": "Appointment not updated in your integrated software.", "appointmentNotDeletedInYourIntegratedSoftware": "Appointment not deleted in your integrated software.", "oneMomentPlease": "One moment, please.", "weAreCreatingTheAppointmentInYourIntegratedSOftware": "We are creating the appointment in your integrated software.", "weAreUpdatingTheAppointmentInYourIntegratedSoftware": "We are updating the appointment in your integrated software.", "reasonForErrorInYourIntegratedSoftware": "Reason for error in your integrated software", "doYouWantToDeleteTheAppointmentOnlyInClearMechanic": "Do you want to delete the appointment only in ClearMechanic?", "cancel": "Cancel", "retry": "Retry", "yesDeleteTheAppointment": "Yes, delete the appointment", "noKeepTheAppointment": "No, keep the appointment", "retryApptCreation": "Retry appt creation", "createApptInClearmechanic": "Create appt in ClearMechanic", "retryApptUpdate": "Retry appt update", "updateApptInClearmechanic": "Update appt in ClearMechanic", "synchronizeWithYourIntegratedSoftware": "Synchronize with your integrated software", "appointmentNotCreatedInYourIntegratedSoftwareFirstTitle": "Unable to create cita on {{integratedAccountName}}", "appointmentNotCreatedInYourIntegratedSoftwareFirstMessage": "Error message: {{errorMessage}}.", "appointmentThirdPartyModalTitle": "Create appointment in ClearMechanic only?", "appointmentThirdPartyModalBodyMessage": "The appointment will be created in the web Private Dashboard and will not sync with your integrated software.", "appointmentThirdPartyModalBodyQuestion": "Do you want to retry creating the appointments in {{integratedAccountName}} or continue and create the appointment in ClearMechanic only?", "appointmentThirdPartyRetry": "Retry", "appointmentThirdPartyCancel": "No, cancel", "appointmentThirdPartyConfirm": "Yes, confirm"}, "appointmentNotes": {"addNote": "Add note", "enterNote": "Enter note", "addedBy": "Added by", "on": "on", "modifiedBy": "Modified by", "at": "at", "customer": "customer"}, "appointmentScheduledFromSiteForAppointments": "Appointment scheduled online.", "theServiceAdvisorAlreadyHasAnAppointment": "The Service Advisor already has an appointment at this time. Please reschedule the appointment or assign a different Service Advisor.", "conflictModal": {"title": "Appointment conflict detected", "theServiceAdvisor": "The Service Advisor", "alreadyHasAnAppointmentAtSameTime": "already has an appointment at the same time. Would you like to modify the current appointment or keep both at the same time?", "modifySchedule": "Modify schedule", "keepAppointment": "Keep Appointment"}, "customerHistory": {"showCustomerHistory": "Show customer history", "goToOrderDetail": "Go to Order detail", "noPreviousOrders": "No previous Orders for the selected customer and vehicle.", "previousOrders": "Previous Orders", "nextMaintenance": "Next Maintenance", "lastOrder": "Last Order", "itemsNotApproved": "Items not approved in the latest Order", "order": "Order", "seeMore": "See more", "seeLess": "See less", "nextMaintenanceDate": "Next maintenance date", "mileage": "[Title:Mileage]", "serviceAdvisor": "[Title:ServiceAdvisor]", "date": "Date", "orderType": "Order type", "reasonForTheAppointment": "Reason for the appointment", "notes": "Notes", "urgent": "<PERSON><PERSON>", "suggested": "Suggested"}}, "orderStatus": {"status": {"authPending": "Authorization pending", "readyForDelivery": "Ready for delivery", "prepForDelivery": "Preparing delivery", "qualityControl": "Quality control", "onProcess": "In process", "onQueue": "On queue", "inWash": "In wash", "onHold": "{{reason}} pending"}, "detail": {"orderNumber": "[Upper:RO] No.", "promiseDate": "Delivery [Lower:Estimate] date", "tower": "Tower", "vehicleReady": "Your vehicle is ready!", "holdingForDelivery": "We will wait for you to deliver", "estimateQuestions": "If you have questions about your [Lower:Estimate], please, contact your [Title:ServiceAdvisor]", "delivered": "Delivered", "answerPoll": "Answer poll", "authPending": "Authorization pending"}}, "status": {"appointments": {"goBackTooltip": "Go back", "selectColumnsTooltip": "Select columns", "columns": "Columns", "showAdvisorName": "Show Advisor name", "headers": {"appointmentTime": "Appointment time", "serviceAdvisor": "[Title:ServiceAdvisor]", "customer": "Customer", "model": "Model", "plates": "Plates", "status": "Status", "fullName": "Full name", "initials": "Initials"}, "status": {"order#": "Order #", "orderCreated": "Order created", "customerDidNotArrive": "Customer did not arrive", "confirmed": "Appointment confirmed", "unconfirmed": "Unconfirmed"}}, "orders": {"goBackTooltip": "Go back", "selectColumnsTooltip": "Select columns", "headers": {"deliveryTime": "Delivery time", "tower": "Tower", "#order": "# Order", "customer": "Customer", "model": "Model", "plates": "Plates", "phase": "Phase"}, "reasonsForPause": {"assignmentofAnotherVehicle": "Assignment of another vehicle", "lunch": "Lunch", "waitingForCustomerAuthorization": "Waiting for customer authorization", "waitingForServiceBay": "Waiting for service bay", "waitingForTools": "Waiting for tools", "waitingForTechnicianReassignment": "Waiting for [Title:Technician] reassignment", "waitingForParts": "Waiting for [Lower:Parts]", "inWarrantyProcess": "In warranty process", "tot": "TOT", "other": "Other"}, "noPhase": "No phase"}}, "conversations": {"whatsAppTemplates": "WhatsApp templates", "sendWhatsAppTemplate": "Send WhatsApp template", "closedWindowTemplates": {"followUpAfterEstimate": "Follow-up after giving an estimate", "followUpAfterAppointmentNoShow": "Follow-up after appointment no show", "followUpNotAnsweringQuestion": "Follow-up after not answering a customer's question"}, "dateFormat": "MM/DD/YYYY", "voiceMessage": "Voice message", "filter": {"tooltip": "Show filters", "searchPlaceholder": "Find by name or phone", "bothInboxes": "Both inboxes", "advisorInbox": "Advisor inbox", "chatbotInbox": "Chatbot inbox", "active": "Active", "closed": "Closed", "hideFilters": "Hide filters"}, "mutePreferences": {"tooltip": "Manage notifications for specific messages in all conversations", "popup": {"title": "Mute notifications", "cancel": "Cancel silence setting?", "messageType": "Message type", "exceptions": "Exceptions", "always": "Always", "custom": "Custom", "silenceDuration": "Silence duration", "silenceDurationTooltip": "Define the time span to deactivate message notifications. This setting will impact messages sent by customers.", "templateCategoriesTooltip": "Disable notifications for sent messages. This applies to customer messages sent after receiving these notifications.", "exceptionsLabel": "Select the messages from customers you want to enable", "businessCustomers": "Messages from business customers", "businessCustomersTooltip": "Customer messages identified by \"Business name\" in the Order detail will not be muted.", "ordersWithoutActivity": "Messages from customers with Orders without activity", "ordersWithoutActivityTooltip": "Messages from customers with Orders without activity, based on the specified \"Time without activity\" will not be muted.", "timeWithoutActivity": "Time without activity", "keywords": "Messages with keywords", "keywordsTooltip": "Messages containing keywords entered in the box will not be muted.", "keywordsNote": "Start typing to add keywords. Press the “Enter” key to confirm a new word.", "keywordsLimitWarning": "Maximum of 20 keywords allowed", "invalidSilenceDuration": "Silence duration must be greater than 0"}, "templateCategories": {"label": "Select the messages you want to mute.", "placeholder": "Select messages", "All": "All messages", "AppointmentConfirmation": "Appointment confirmation messages", "AppointmentReminder": "Appointment reminder messages", "SentDuringOrder": "Messages sent to the customer during the Order", "Survey": "Survey messages", "NextServiceReminder": "Next service reminder messages", "FollowUpMessages": "Follow-up messages", "MassSendingMessages": "Mass sending messages"}}, "muteState": {"dateFmt": "LLL dd, yyyy", "until": "Until {{value}}", "template": "Muted conversation: {{when}}", "always": "Always", "errorTooltip": "Failed to fetch conversation muted state, click here to refetch"}, "showCustomerDetail": "Show customer detail", "hideCustomerDetail": "Hide customer detail", "useTemplate": "Use template", "textAreaPlaceholder": "Write a message...", "fastMessages": {"welcome": "You're welcome!", "service": "We're at your service.", "pleasure": "It's a pleasure."}, "send": "Send", "templates": {"cm_consumer_numbers": "Order information"}, "selectTemplate": "Select a template", "noContextWarning": "It is not possible to send messages to conversations that are not linked to an order.", "chatBotModeWarning": "It is not possible to send messages while the client does not require the attention of the advisor.", "closedWindowTimeWarning": "According to Meta's policies, it is only possible to send approved templates if there has been no response from the customer in the last 24 hours.", "notCurrentControlFlowWarning": "It is not possible to send messages while another [Lower:ServiceAdvisor] has current control of the conversation.", "orderNumber": "Order #{{number}}", "sentBy": "<bold>Sent by</bold>: {{name}}", "instruction": "Please select a conversation to view more details.", "errorMessages": {"failedToCall": "Failed to call the customer", "phoneNumberIsMissing": "No phone number is configured for team member"}}, "chatNotifications": {"conversationsNumber": "Conversations ({{count}})", "readAll": "Mark all as read", "unreadMessages": "Unread messages", "conversationWith": "Conversation with:", "conversationsAvailable": "available WA conversations", "marketingConversations": "WA marketing conversations used:", "utilityServiceConversations": "WA utility/service conversations used:"}, "notifications": {"header": "Notifications"}, "reports": {"reportsPageTitle": "Reports", "forGeneralManager": "For general manager", "forAftersalesManager": "For aftersales manager", "forAll": "For all", "Question": "Question", "Answer": "Answer question", "ChecklistNumber": "Checklist #", "ItemsInChecklistNumber": "Items in Checklist #", "cr_OrderNumber": "# Order", "cr_OrderNumberByOpeningDate": "# Order by opening date", "cr_OrderNumberByClosingDate": "# Order by closing date", "cr_Location": "Location", "cr_Currency": "<PERSON><PERSON><PERSON><PERSON>", "cr_GeneralInformation": "General information", "cr_AppointmentNumber": "# Appointment", "cr_ServiceAdvisor": "[Title:ServiceAdvisor]", "cr_Technician": "[Title:Technician]", "cr_CustomerName": "Name", "cr_CustomerEmail": "Email", "cr_CustomerMobile": "Mobile", "cr_CustomerLandline": "Landline", "cr_Customers": "Customers", "cr_Appointments": "Appointments", "cr_Appointment": "# Appointment", "cr_AppointmentStatus": "Appointment status", "cr_AppointmentReason": "[Sentence:ReasonForVisit]", "cr_AppointmentDate": "Date of the appointment", "cr_AppointmentTime": "Time of the appointment", "cr_AppointmentOrigin": "Appointment origin", "cr_AppointmentUserWhoSchedule": "Person who schedules", "cr_Estimates": "[Title:Estimates]", "cr_EstimatesParts": "[Title:Estimate] - [Title:Parts]", "cr_EstimatesLabor": "[Title:Estimate] - [Title:Labor]", "cr_EstimatesSubTotal": "[Title:Estimate] - Subtotal", "cr_EstimatesTax": "Taxes", "cr_EstimatesTotal": "[Title:Estimate] - Total", "cr_EstimatesTotalApproved": "Approved amount", "cr_EstimatesTotalRejected": "Rejected amount", "cr_EstimatesPercentApproved": "% Approved", "cr_EstimatesApproveStatus": "Approved or Rejected?", "cr_Surveys": "Surveys", "cr_SurveysDeliveryDate": "Delivery date", "cr_SurveysAnswerDate": "Answer date", "cr_Question1": "Question 1", "cr_AnswerQuestion1": "Answer question 1", "cr_Question2": "Question 2", "cr_AnswerQuestion2": "Answer question 2", "cr_Orders": "Orders", "cr_OrdersNumber": "# Order", "cr_OrdersTower": "Tower", "cr_OrdersType": "Order type", "cr_OrdersSecureLink": "Digital Order", "cr_OrdersRedItems": "Red items", "cr_OrdersYellowItems": "Yellow items", "cr_OrderTechnician": "Technician", "cr_WorkshopPlannerTechnician": "Technician", "cr_OrdersGreenItems": "Green items", "cr_OrdersNaItems": "N/A items", "cr_OrdersInspectionItems": "Inspection items", "cr_OrdersSuccessfulCalls": "Successful calls", "cr_OrdersPriority": "Priority", "cr_OrdersFailedCalls": "Failed calls", "cr_OrdersCustomerViews": "Customer views", "cr_OrdersInternalViews": "Internal views", "cr_OrdersPhotos": "Photos", "cr_OrdersVideos": "Videos", "cr_OrdersServiceAdvisor": "Service Advisor", "cr_OrdersUploadedBy": "Uploaded by", "cr_OrdersInCharge": "In charge", "cr_OrdersAssignedTo": "Assigned to", "cr_OrdersDateOfUpload": "Date of Order opening", "cr_OrdersTimeOfUpload": "Time of Order opening", "cr_OrdersDateOfLastUpdate": "Date of last update", "cr_OrdersTimeOfLastUpdate": "Time of last update", "cr_OrdersFirstDateMessage": "Date of first sending of Order", "cr_OrdersFirstTimeMessage": "Time of first sending of Order", "cr_OrdersTimeUntilFirstSending": "Time until first sending", "cr_OrdersLastCommunication": "Last communication", "cr_OrdersDateOfClosing": "Date of Order closing", "cr_OrdersTimeOfClosing": "Time of Order closing", "cr_OrdersTimeFromUploadedToClosed": "Time in shop", "cr_OrdersPhaseSetback": "Phase setback", "cr_OrdersOriginPhase": "Origin phase", "cr_OrdersDestinationPhase": "Destination phase", "cr_OrdersPhaseSetbackReason": "Reason for the setback", "cr_WorkshopPlanner": "Workshop Planner", "cr_WorkshopPlannerPhase": "Current phase", "cr_WorkshopPlannerDeliveryDate": "Promised delivery date", "cr_WorkshopPlannerDeliveryTime": "Promised delivery time", "cr_WorkshopPlannerDuration": "Assigned time", "cr_WorkshopPlannerPauseReason": "Reason for pause", "cr_WorkshopPlannerTimeUnitsSold": "Time units sold", "cr_WorkshopPlannerTimeOnPause": "Time on pause", "cr_WorkshopPlannerAvailableHours": "Available hours", "cr_WorkshopPlannerHoursPresent": "Hours present", "cr_WorkshopPlannerProductiveHours": "Productive hours", "cr_WorkshopPlannerUnproductiveHours": "Unproductive hours", "cr_WorkshopPlannerPercentProductivity": "% productivity", "cr_WorkshopPlannerNumberOfJobs": "Number of jobs", "cr_WorkshopPlannerJobDescription": "Job description", "cr_WorkshopPlannerJobEndDateAndTime": "Job end date and time", "cr_WorkshopPlannerJobNumber": "Job number", "cr_WorkshopPlannerJobRealDuration": "Job real duration", "cr_WorkshopPlannerJobStartDateAndTime": "Job start date and time", "cr_Vehicle": "Vehicle", "cr_VehicleBrand": "Brand", "cr_VehicleModel": "Model", "cr_VehicleYear": "Year", "cr_VehicleVin": "VIN", "cr_VehicleMileage": "[Title:Mileage]", "cr_VehiclePlates": "Plates", "cr_OrdersRepairsIsSubItem": "Item or sub-item?", "timeInPhase": "Time in phase \"{{phaseName}}\"", "noPhase": "No phase", "OrdersTimeFromUploadedToClosedPropertyTooltip": "Time the vehicle stayed at the location.", "WorkshopPlannerDurationTooltip": "Sum of times assigned to job in the Order.", "WorkshopPlannerTimeOnPauseTooltip": "Sum of time the Order has been on pause.", "WorkshopPlannerCurrentPhaseTooltip": "Current phase that the Order has selected.", "OrderByOpeningDateTooltip": "The Orders included in the report will be those created within the selected time range.", "OrderByClosingDateTooltip": "The Orders included in the report will be those closed within the selected time range.", "WorkshopPlannerJobNumberTooltip": "A number is assigned to each job based on its scheduled time. The first scheduled job is number 1, the second is number 2, and so on.", "WorkshopPlannerJobRealDurationTooltip": "It is the duration, in hours and minutes, between the start time and the end time of the job. It does not include the periods when it was paused.", "executiveIntelligenceReportOption": "Executive intelligence report", "auditedProcessesOption": "Activity report: Audited processes", "inspectionPatternsReportOption": "Inspection patterns report", "financialKpisReportOption": "Financial KPI´s report", "operationalKpisReportOption": "Operational KPI´s report (dashboard)", "activityReportInspectionFormsOption": "Activity report: Inspection forms", "activityReportFollowUpLettersOption": "Activity report: Follow-up letters", "activityReportDmsOption": "Activity report: DMS", "activityReportPartsOption": "Activity report: [Title:Parts]", "activityReportEstimatesOption": "Activity report: [Title:Estimates]", "activityReportSurveysOption": "Activity report: Surveys", "activityReportWorkshopPlanner": "Activity report: Workshop Planner", "uploadDate": "Orders by upload date", "updateDate": "Orders by last action", "approvalDate": "Approval date", "createReport": "Create report", "downloadReport": "Download report", "reportType": "Report type", "start": "Start", "end": "End", "CustomReportFileName": "Custom report", "RowLimitExceeded": "Row limit exceeded", "MoreOneMillionRowsError": "We have generated your report, but it exceeds Excel's limit of 1,000,000 rows, so the rows corresponding to the dates {{formattedDateFromString}} - {{formattedLastIncludedDateString}} were not included", "DownloadCancel": "Download canceled", "DownloadSuccess": "Downloaded report", "DownloadingInProgress": "Downloading in progress!", "YouCanContinueUsingOtherScreens": "You can continue using other screens and functions while you wait for the download to complete.", "columnNotFound": "Column not found", "tryAgainWithADifferentColumn": "Try again with a different column", "ifYouCancel": "If you cancel, you will lose the latest changes you made.", "ifYouCancelYouWillLoseTheLatestChangesYouMade": "If you cancel, the report will not be created.", "cancelCustomReportEdition": "Cancel custom report edition?", "cancelCustomReportCreation": "Cancel custom report creation?", "cancelEdition": "Cancel edition", "cancelCreation": "Cancel creation", "savedReport": "Saved report", "updatedReport": "Updated report", "unsavedChangesWillBeLost": "Unsaved changes will be lost", "cancelCreationText": "If you cancel, the report will not be created.", "cancelCustomReportEditing": "Cancel custom report edition?", "reportTheSameName": "There is already a report with the same name", "nameAlreadyExisting": "Name already existing", "saveChanges": "Save changes", "newReport": "New report", "areYouSure": "Are You Sure?", "saveCustomReport": "Save custom report", "cancelTemp": "Cancel Temp", "saveReport": "Save report", "name": "Name", "cancel": "Cancel", "tryAgain": "Try again", "downloadError": "Download error", "errorOccurred": "An error occurred while downloading the file.", "downloadingAgain": "Would you like to try downloading the report again?", "enterTheNameOfTheCustomReport": "Enter the name of the custom report", "thereIsAlreadyAReportWithTheSameNamePleaseWriteAnotherName": "There is already a report with the same name. Please write another name.", "thisReportWillBeSavedForEasyFutureReference": "This report will be saved for easy future reference", "yesCancel": "Yes, <PERSON>cel", "goBack": "Go Back", "results": "results", "result": "result", "createReportImage": "Create a report!", "pleaseSelectColumnsImage": "Please select columns", "selectedcolumnstitle": "Selected columns", "selectedcolumnstitletooltip": "These columns will appear in the personalized report you create.", "searchcolumn": "Search column", "primarycolumn": "Primary column", "primarycolumntooltip": "Select a primary column.", "primary": "Primary", "reportDeleted": "Report deleted", "confirmreportdelete": "Delete report", "canceldelete": "Go back", "nosavedreports": "No saved reports", "selectcustomizablereport": "Select customizable report", "deletecustomreporttitle": "Delete custom report?", "deletecustomreport": "This report will be permanently deleted.", "columnstitle": "COLUMNS", "informativebox": "This is a preview with limited rows. To view the full report, click on <strong>“Download report”</strong>.", "uploadTooltipText": "Generate reports for Orders based on the date the Order was first uploaded.", "updateTooltipText": "Generate reports for Orders based on the date the Order was last updated.", "approvalTooltipText": "Generate reports based on the approved date.", "predefinedReports": "Predefined reports", "customizableReports": "Customizable reports", "primaryColumnChangedPopupTitle": "Primary column changed", "primaryColumnChangedPopupBody": "You changed the primary column from “{{prevColumnName}}” to “{{nextColumnName}}”. To preserve your selected data, the previous primary column ({{prevColumnName}}) has been moved to the first secondary column.", "invalidDates": {"title": "The start date and the end date must be of the same year", "body1": "To download this report, it is required that the “start date” and the “end date” be from the same year.", "body2": "For example: The start date can be “{{from}}” and the end date “{{to}}”.", "body3": "Please select a valid start date and end date.", "close": "Close", "dateFormat": "MMMM D, YYYY"}, "emailSentTitle": "Successful report submission", "emailSentBody": "Check your email in approximately 10 minutes", "largeReport": {"title": "Receive a report by email", "text": "This report contains a significant amount of data and will take time to generate.\nPlease enter your e-mail address and we will send the report by e-mail in approximately 10 minutes.", "emailCaption": "Email", "placeholder": "<EMAIL>", "submit": "Submit", "invalidEmailTitle": "Invalid email", "invalidEmailBody": "Please enter a valid email"}}, "indicators": {"start": "Start", "end": "End", "dateRangeErrorTitle": "Date range error", "dateRangeErrorText": "Select a different date.", "apply": "Apply", "filterTitle": "Select Indicators", "days": "days", "day": "day", "notAvailable": "N/A", "titles": {"AverageTicketResult": "Average ticket result", "TotalEstimated": "Total estimated", "TotalApproved": "Total approved", "PercentApprovedOfEstimated": "% Approved of estimated", "TotalEstimatedVsTotalApproved": "Total estimated vs Total approved", "RedOrYellowItemsAdded": "Red or yellow items added", "RedOrYellowItemsEstimated": "Red or yellow items estimated", "RedOrYellowItemsWithoutEstimate": "Red or yellow items without estimate", "RedItemsAdded": "Red items added", "RedItemsEstimated": "Red items estimated", "RedItemsWithoutEstimate": "Red items without estimate", "YellowItemsAdded": "Yellow items added", "YellowItemsEstimated": "Yellow items estimated", "YellowItemsWithoutEstimate": "Yellow items without estimate", "EstimatedItems": "Estimated items", "Productivity": "Productivity", "OrdersCreated": "Orders created", "OpenOrders": "Open Orders", "ClosedOrders": "Closed Orders", "OrdersWithCompleteChecklist": "Orders with complete checklist", "AverageDwellTime": "Average dwell time", "OrdersActivity": "Orders Activity"}, "tooltips": {"AverageTicketResult": "Result obtained by dividing the total approved by the customer by the number of Orders created within the specified date range.", "TotalEstimated": "It is the total estimated in the Orders within the selected date range.", "TotalApproved": "It is the total approved by a team member or the customer for the Order estimates within the selected date range.", "PercentApprovedOfEstimated": "Percentage calculated by dividing the total estimated amount by the total approved amount for the Orders within the selected date range.", "RedOrYellowItemsAdded": "Number of red or yellow items added to Orders within the selected date range.", "RedOrYellowItemsEstimated": "Number of red or yellow items that were estimated in Orders within the selected date range.", "RedOrYellowItemsWithoutEstimate": "Number of red or yellow items that were added to the Orders but were not estimated.", "RedItemsAdded": "Number of red items added to Orders within the selected date range.", "RedItemsEstimated": "Number of red items that were estimated in Orders within the selected date range.", "RedItemsWithoutEstimate": "Number of red items that were added to the Orders but were not estimated.", "YellowItemsAdded": "Number of yellow items added to Orders within the selected date range.", "YellowItemsEstimated": "Number of yellow items that were estimated in Orders within the selected date range.", "YellowItemsWithoutEstimate": "Number of yellow items that were added to the Orders but were not estimated.", "EstimatedItems": "Breakdown of approved and estimated totals by inspection item priority and team members.", "OrdersCreated": "Number of Orders created in the selected date range.", "OpenOrders": "Number of Orders created in the selected date range that are still open.", "ClosedOrders": "Number of Orders that were closed in the selected date range.", "OrdersWithCompleteChecklist": "Number of Orders that have all inspection items with a selected priority.", "AverageDwellTime": "Average time the vehicle stayed at the location."}, "columns": {"TotalEstimatedVsTotalApproved": {"Location": "Location", "TeamMember": "Team member", "TotalEstimated": "Total estimated", "TotalApproved": "Total approved", "PercentApprovedEstimated": "% Approved of estimated"}, "EstimatedItems": {"Location": "Location", "TeamMember": "Team member", "RedItemsEstimated": "Red items estimated", "RedItemsApproved": "Red items approved", "PercentRedItemsApproved": "% Red items approved", "YellowItemsEstimated": "Yellow items estimated", "YellowItemsApproved": "Yellow items approved", "PercentYellowItemsApproved": "% Yellow items approved"}, "Productivity": {"Location": "Location", "TeamMember": "Team member", "AvailableHours": "Available hours", "PresenceHours": "Presence hours", "ProductiveHours": "Productive hours", "UnproductiveHours": "Unproductive hours", "PercentProductivity": "% productivity"}, "OrdersActivity": {"Location": "Location", "TeamMember": "Team member", "OpenOrders": "Open Orders", "OrdersSentToCustomer": "Orders sent to the customer", "OrdersNotSentToCustomer": "Orders not sent to the customer", "OrdersWithCompleteChecklist": "Orders with complete checklist", "OrdersWithoutCompleteChecklist": "Orders without complete checklist"}, "tooltips": {"Productivity": {"AvailableHours": "Available hours is the total number of hours that the technician is in the locations, according to his/her work schedule. Considering the recorded absences.", "PresenceHours": "Presence hours is the total number of hours that the technician is in the locations, according to his/her work schedule. Omitting registered absences.", "ProductiveHours": "Productive hours is the sum of hours in which the technician was working on an Order.", "UnproductiveHours": "Unproductive hours is the sum of hours in which the technician has no assigned jobs.", "PercentProductivity": "The productivity percentage is obtained by dividing “Productive hours” by “Presence hours” and multiplying by 100."}}}}, "menuPricing": {"header": "Menu pricing", "search": "Search", "addManualEstimate": "Add manual [Lower:Estimate]", "vehicleNotFound": "Vehicle not found", "results": "{{count}} results", "noResults": "No results found for \"{{searchText}}\"", "estimateUpdatedTitle": "[Title:Estimate] updated", "estimateUpdatedBody": "The [Lower:Estimate] has been updated with “Menu pricing”.", "performSearch": "Perform your search to display results", "zeroResults": "0 results", "jobs": "Jobs", "parts": "[Title:Parts]", "labor": "[Title:Labor]", "selectedCount": "{{selectedCount}} selected", "cancelEstimateModal": {"title": "Are you sure you want to cancel the selection?", "cancel": "Cancel", "confirm": "Yes"}}, "cancelModal": {"doYouWantToCancel": "Do you want to cancel?", "goBack": "Go back", "yesCancel": "Yes, cancel"}, "massiveSending": {"massiveSendingsLists": "Mass sendings lists", "newMassiveSending": "New mass sending", "table": {"sendingName": "SENDING NAME", "sendDate": "SEND DATE", "endingDate": "ENDING DATE", "impactedCustomers": "IMPACTED CUSTOMERS", "status": "STATUS", "filterByStatus": "Filter by status", "dateFormat": "MM/DD/YYYY", "timeFormat": "HH:mm", "neverEnds": "Never ends", "customersWhomMessageSent": "Customers to whom the WhatsApp message was sent", "customersRespondedMassiveSending": "Customers responded to the mass sending", "appointmentScheduled72HoursAfterMassiveSending": "Appointments scheduled 72 hours after mass sending", "active": "Active", "finalized": "Finalized"}, "sendingType": {"_": "Sending type", "placeholders": {"WarrantyCampaign": "One-time sending according to the VIN numbers loaded from an Excel file.", "Personalized": "One-time sending according to personalized filters."}, "WarrantyCampaign": "Warranty campaign (VIN)", "Personalized": "Personalized"}, "modal": {"sendingRules": "Sending rules", "whatsAppTemplate": "WhatsApp template", "preview": "Preview", "newMassiveSending": "New mass sending", "editMassiveSending": "Edit mass sending", "goBack": "Go back", "cancel": "Cancel", "continue": "Continue", "uploadAndContinue": "Upload and continue", "saveChanges": "Save changes", "confirmSending": "Confirm sending", "tabs": {"sendingRules": "Sending rules", "whatsAppTemplate": "WhatsApp template", "preview": "Preview"}, "sendingName": "Sending name", "enterTheNameOfYouNewMassiveSending": "Enter the name of you new mass sending", "allCustomers": "All customers", "dateOfSending": "Sending date", "timeOfSending": "Sending hour", "sendRightNow": "Send right now", "addImage": "Add image", "recommendedSize": "Recommended size", "text": "Text", "imageAndText": "Image and text", "templateType": "Template type", "templateImage": "Template image", "notes": "Notes", "thisImageWillBeSentToTheCustomerByWhatsAppMessageAllowedFormatsForTheImage": "This image will be sent to the customer by WhatsApp message. Allowed formats for the image: ", "pngOrJpg": ".png or .jpg", "templateText": "Template text", "hello": "Hello, [Customer name],", "weWriteFrom": {"Personalized": "we write from [WorkShop name]", "WarrantyCampaign": "we are writing from [Workshop name] to notify you of a warranty issue."}, "template": {"Personalized": "We have a 15% discount promotion for your maintenance service. Mention this message when scheduling your appointment to get your discount!, You can schedule your appointment by calling (XXX-XXX-XXXX).", "WarrantyCampaign": "<b>Service details</b>: Airbag review at no cost, for models 2018 to 2020.", "startOfMessage": "We want to express our appreciation for the general repair of your [Brand] [Model] [Year].", "endOfMessage": "We hope to see you soon! Choose an option.", "mainText": "We have a 15% discount promotion for your next maintenance service. Mention this message when scheduling your appointment to receive your discount!", "automaticReply": "We will contact you shortly"}, "paragraphs": {"startOfMessageLabel": "<1>Start of the message</1><2>*</2> <3>(Write an initial greeting)</3>", "startOfMessagePlaceholder": "Write the text with which you want to start your message.", "mainTextLabel": "<1>Main text</1><2>*</2> <3>(Write the main message)</3>", "mainTextPlaceholder": "Write the main message you want to communicate.", "endOfMessageLabel": "<1>End of the message</1><2>*</2> <3>(Write the text with which you want to end your message)</3>", "endOfMessagePlaceholder": "Write the closing of your message.", "buttonsLabel": "<1>🔽 Press the \"I WANT TO BE CONTACTED\" button, and we will get in touch with you.<br>🔽 Press the \"STOP PROMOTIONS\" button to stop receiving these messages.</1>", "buttons": {"iWantToBeConducted": "I WANT TO BE CONTACTED", "stopPromotions": "STOP PROMOTIONS"}, "automaticReplyLabel": "<1>Automatic reply after pressing \"I WANT TO BE CONTACTED\"</1><2>*</2> <3>(Write the automatic reply text)</3>", "automaticReplyPlaceholder": "Write the automatic reply your customer will receive after pressing the \"I WANT TO BE CONTACTED\" button."}, "paragraphsOptions": {"1paragraph": "1 customizable paragraph", "3paragraphsAndButton": "3 customizable paragraphs and a button"}, "insertVariablesButton": {"buttonTitle": "Insert variable", "vehicleInformation": "Vehicle information", "brand": "Brand", "model": "Model", "year": "Year", "plates": "Plates", "customerInformation": "Customer information", "name": "Name", "lastName": "Last name"}, "verifyThatAllTheDataOfYourMassiveSendingAreCorrect": "Verify that all the data of your mass sending are correct", "nameOfTheSending": "Name of the sending", "singleSending": "Single sending", "cancelMassiveSending?": "Cancel mass sending?", "cancelMassiveSendingEdition?": "Cancel mass sending edition?", "weLookForwardToHearingFromYou!": "We look forward to hearing from you!", "theSendingWillBeMadeAtTheEndOfTheMassiveSendingCreation": "The sending will be made at the end of the mass sending creation", "massiveSendingSuccessfullyCreated": "Mass sending successfully created", "massiveSendingModified": "Mass sending modified", "typeOfCustomersToSend": "Type of customers to send", "downloadImpactedCustomersReport": "Download impacted customer report", "duplicate": "Duplicate and create new sending", "sendingDetails": "Mass sending detail", "lastVisit": {"months_other": "Last {{count}} months", "months_one": "Last month", "allMonths": "All months", "label": "Last visit", "placeholder": "Select the last visit"}, "vinUpload": {"uploadedTitle": "File uploaded", "uploadedText": "The file has been uploaded successfully", "invalidFile": "Format not allowed", "fileLimitExceeded": "You have exceeded the file weight limit", "vinsLimitExceeded": "You have exceeded the maximum number of VINS", "totalMessage": "Total messages to send", "tmplUrl": "https://clearmechanic.s3.ca-central-1.amazonaws.com/Content/MassSendings/ImpactedCustomers/VINs Registration.xlsx"}, "imageSizeError": {"title": "Image exceeds maximum size", "text": "Maximum size allowed 800kb"}, "nlWarn": "Line breaks are not allowed."}, "delete": {"yesDelete": "Yes, delete", "title": "Do you want to delete mass sending?", "body": "If you delete mass sending it will be cancelled."}}, "components": {"dragAndDropFileInput": {"title": "Drag or click here to upload your file", "noFile": "No file", "size": "Size"}}, "workshopPlanner": {"advisors": "Advisors", "youMustHaveAtLeastOneServiceAdvisorSelected": "You must have at least one [Title:ServiceAdvisor] selected.", "youMustHaveAtLeastOneTechnicianSelected": "You must have at least one [Title:Technician] selected.", "appointmentConfirmedOrOrderCreated": "Appt. confirmed or Order created", "customerDidNotArrive": "Customer did not arrive", "ORDER": "ORDER", "DID_NOT_ARRIVE": "DID NOT ARRIVE", "APPOINTMENT_CONFIRMED": "APPT. CONFIRMED", "APPOINTMENT_UNCONFIRMED": "UNCONFIRMED", "appointment": "Appointment", "utLabel": "Time units sold", "redHighlightAnnotation": "Order not started according to the scheduled time or with expired delivery promise date", "schedulePopup": {"preview": "Preview", "changeAppointment": "(change appointment)", "changeOrder": "(change Order)", "jobDescription": "[Sentence:JobDescription]:", "customerName": "Customer", "subtitle": "Job start time: {{- date}} at {{time}} hrs", "orderTitle": "Add job to an Order for {{userPosition}} {{userName}}", "appointmentTitle": "Add job to an appointment for {{userPosition}} {{userName}}", "selectAppointment": "Select appointment", "selectAppointmentPlaceholder": "Search for appointment number", "selectOrder": "Select Order", "selectOrderPlaceholder": "Search for Order number", "duration": "Scheduled duration", "operationCode": "Operation code", "operationCodePlaceholder": "Enter operation code", "standardTime": "Standard time", "standardTimePlaceholder": "Enter standard time", "operationCodeDescription": "Operation code description", "operationCodeDescriptionPlaceholder": "Enter operation code description", "appointmentDateAndTime": "Appointment date and time", "orderDateAndTime": "[Sentence:PromisedDeliveryDateAndTime]", "orderDateAndTimeAt": "[Sentence:PromisedDeliveryDateAndTime]: {{- date}} at {{time}} hrs", "exceedsDuration": "The entered duration exceeds the team member's working hours. Adjust the duration or choose another time.", "appointmentCreated": {"title": "Job added to appointment", "text": "Job has been added to appointment #{{appointmentNumber}}"}, "orderCreated": {"title": "Job added to the Order", "text": "Job has been added to the Order #{{orderNumber}}"}, "teamMember": "Team member", "teamMemberPlaceholder": "Select team member", "planning": "Planning", "planningPlaceholder": "Select planning", "appointmentAltTitle": "Add job for appointment {{appointment}}", "appointmentSubtitle": "Appointment date and time: {{- date}} at {{time}} hrs", "orderAltTitle": "Add job for Order {{order}}", "unassignedHint": "After saving, the job will be assigned to <bold>{{user}}</bold>.", "moreInfo": "More information", "sroValidation": {"success": {"title": "Operation code validation", "description": "Operation code validation successfully in {{dmsName}}"}, "error": {"title": "Unable to validate operation code on {{dmsName}}", "description": "Error message: {{errorMessage}}"}}}, "unifiedSearch": {"searchPlaceholder": "Search: #Appt, # Order, VIN, plates", "noResults": "No matches found for your search", "results": "Results", "orderNumber": "Order #", "appointmentNumber": "Appointment #", "customer": "Customer: ", "vehicle": "Vehicle: ", "vin": "VIN: ", "plates": "Plates: ", "phase": "Phase: "}, "3dot": {"seeOrEditJob": "See or edit job", "deleteJob": "Delete job", "reassignJob": "Reassign job", "seeOrEditOrder": "See or edit Order", "startJob": "Start Job", "pauseJob": "Pause Job", "stopJob": "Stop Job", "resumeJob": "Resume Job", "duplicateJob": "Duplicate job"}, "orderPopup": {"noJobsInTimeline": "There are no added jobs at the moment.", "quickOrderInfo": "Quick Order info", "timeline": "Timeline", "orderDetail": "See Order detail", "timelineTemplate": "{{user}} — {{planning}} Planning", "title": "Order #{{orderNumber}}", "appointment": "Appointment", "reasonForAppointment": "[Sentence:ReasonForVisit]", "customerName": "Name", "tower": "Tower", "orderType": "Order type", "mobile": "Mobile", "plates": "Plates", "vehicle": "Vehicle", "deliveryDate": "[Sentence:PromisedDeliveryDateAndTime]", "date": "Work date", "time": "Work hour", "timePlaceholder": "Select an hour", "datePlaceholder": "Select a date", "duration": "Duration", "scheduleWorkIsOutsideScheduleError": "Check the entered data.", "technicianNotAssignedError": "[Title:Technician] is not assigned to the order", "technicianCanNotBeReassigned": "[Title:Technician] can not be re-assigned", "canNotResumeNow": "Order can not be resumed, because [Lower:Technician] is working on another order currently", "startPointIsOutOfSchedule": "Order can not be resumed out of schedule", "orderScheduleOccupiedError": "This Order is worked by {{displayName}}.", "durationMustBeGreater": "The value entered must be greater.", "technicianOccupied": {"title": "[Title:Technician] is working on another Order", "text": "A [Title:Technician] cannot be assigned to 2 Orders at the same time."}, "phase": "Current phase", "orderTimeline": "History of phases", "selectPhasePlaceholder": "Select a Phase", "seeMore": "See more", "seeLess": "See less", "pauseOrder": "Pause Order", "orderMustBeAssigned": "Order must be assigned and taken to work to set a pause", "addJob": "Add job to Order", "orderDetailPopup": {"title": "You will be redirected to the “Order detail” screen", "body": "The changes you have made in this pop-up will be saved and it will close when you click the “Save changes and continue” button.", "saveChanges": "Save changes and continue"}, "jobCreatedIntegrationSuccess": {"text": "Job created successfully in {{integrationAccountName}}.", "title": "Job created"}, "jobCreatedIntegrationError": {"text": "Error message: {{errorMessage}}.", "title": "Unable to create the job in {{integrationAccountName}}"}, "setActionIntegrationSuccess": {"text": "Job updated successfully in {{integrationAccountName}}.", "title": "Job updated"}, "setActionIntegrationError": {"text": "Error message: {{errorMessage}}", "title": "Unable to update the job in {{integrationAccountName}}"}}, "planOrderPopup": {"title": "Appointment #{{appointmentNumber}}", "appointment": "Appointment", "reasonForAppointment": "[Sentence:ReasonForVisit]", "customerName": "Name", "firstNamePlaceholder": "Name", "lastNamePlaceholder": "Last name", "mobilePlaceholder": "Enter mobile", "platesPlaceholder": "Enter plates", "mobile": "Mobile", "plates": "Plates", "vehicle": "Vehicle", "date": "Work date", "time": "Work hour", "timePlaceholder": "Select an hour", "datePlaceholder": "Select a date", "duration": "Duration", "scheduleWorkIsOutsideScheduleError": "Check the entered data.", "outsideDaySchedule": {"title": "Check the entered data.", "text": "Order planning should fit 1 working day"}, "technicianOccupied": {"title": "[Title:Technician] is working on another Order", "text": "A [Title:Technician] cannot be assigned to 2 Orders at the same time."}}, "moreJobInfoPopup": {"title": "More job information", "order": "Order #{{orderNumber}}", "teamMember": "Team member", "employeeId": "Employee ID", "planning": "Planning", "startDate": "Start date", "startHour": "Start hour", "scheduledDuration": "Scheduled duration", "timeUnitsSold": "Time units sold", "jobDescription": "Job description:", "timeline": "Job timeline", "realDuration": "Real duration:", "technicianSignature": "Technician's signature", "technicianSignaturePlaceholder": "Add signature from mobile app"}, "phases": {"phasesTitle": "Phases", "promiseLabel": "Promise: ", "noPhase": "No phase", "noPhaseDescription": "Orders created without stage assigned", "closedOrder": "Closed Order", "closedOrderDescription": "Closed Orders of the current day", "settings": {"addPhase": "Add phase", "enterPhase": "Enter phase", "notifications": {"updatedPhaseTitle": "Updated phase", "updatedPhaseBody": "Phase saved successfully.", "phaseCreatedTitle": "Phase created", "phaseCreatedBody": "Phase saved successfully.", "duplicatePhaseTitle": "Duplicate phase", "duplicatePhaseBody": "The phase already exists.", "tooLongNamePhaseTitle": "Phase name is too long", "tooLongNamePhaseBody": "The phase name must be less than 50 characters.", "updatedPhase": "Updated phase", "phaseDeletedSuccessfully": "Phase deleted successfully."}, "deletePopup": {"deletePhaseTitle": "Delete phase", "doYouWantToDeleteThisPhase?": "Do you want to delete this phase?", "changesAffectSettings": "The changes made will affect the settings for the “Phase setback” notification.", "deletePhase": "Delete phase"}, "reorderPopup": {"title": "Are you sure you want to modify the sequence of the phases?", "body": "The changes made will affect the settings for the “Phase setback” notification.", "confirm": "Yes, modify"}}}, "pausedOrders": {"noPausedOrders": "There are no orders on pause.", "paused": "Paused", "order": "Order", "orders": "Orders", "filters": {"filterByPhase": "Filter by phase", "filterByLatestTechnician": "Filter by latest Technician", "filterByReasonForPause": "Filter by reason for pause"}, "tableHeaders": {"tower": "Tower", "order": "Order", "phase": "Phase", "latestTechnician": "Latest [Title:Technician]", "reasonForPause": "Reason for pause", "promiseDate": "Promise date", "daysOnPause": "Days paused"}, "days": "days", "day": "day", "reasonsForPause": {"pauseFor": "Pause for", "assignmentofAnotherVehicle": "Assignment of another vehicle", "lunch": "Lunch", "waitingForCustomerAuthorization": "Waiting for customer authorization", "waitingForServiceBay": "Waiting for service bay", "waitingForTools": "Waiting for tools", "waitingForTechnicianReassignment": "Waiting for [Title:Technician] reassignment", "waitingForParts": "Waiting for [Lower:Parts]", "inWarrantyProcess": "In warranty process", "tot": "TOT", "other": "Other"}, "lightsLegends": {"expiredPausedDays": "Order paused for {{days}} days or more", "expiredDeliveryDay": "Order with expired delivery promise date"}}, "convertOrder": {"appointment": "Appointment", "serviceAdvisor": "[Title:ServiceAdvisor]", "reasonForAppointment": "[Sentence:ReasonForVisit]", "notes": "Notes", "enterNotes": "Enter notes", "vehicle": "Vehicle", "convertToOrder": "Convert to Order", "planOrder": "Plan appointment", "editPlanning": "Edit planning", "editOrder": "Edit Order", "addJob": "Add job to appointment", "appointmentDetailTab": "Appointment detail", "timelineTab": "Timeline"}, "technicians": {"title": "[Title:Technicians]", "absent": "Absent", "noActivity": "No activity", "legend": {"ByOrderType": "Order type", "ByReasonForAppointment": "[Sentence:ReasonForVisit]", "ByServiceAdvisor": "Service Advisor"}}, "add": {"appointment": "Add job to appointment", "order": "Add job to Order", "absence": "Add absence"}, "deleteBlock": {"yes": "Yes, delete", "text": "Are you sure you want to delete this job?", "successNotif": {"text": "Job deleted successfully.", "title": "Job deleted"}, "successIntegrationNotif": {"text": "Job deleted successfully in {{integrationAccountName}}.", "title": "Job eliminated"}, "errorIntegrationNotif": {"text": "Error message: {{errorMessage}}.", "title": "Unable to delete the job in {{integrationAccountName}}"}}, "editBlock": {"startDate": "Start date", "startHour": "Start hour", "successNotif": {"text": "Job modified successfully.", "title": "Job modified"}, "outsideOfSchedule": {"text": "Job can't be assigned outside of the team member schedule.", "title": "The team member doesn't work at this time."}, "tooLarge": {"text": "Job is too large.", "title": "The job is too large and can't be scheduled."}, "duplicateOperationCode": {"text": "Operation code is duplicated.", "title": "Please, change operation code."}}, "reassignBlock": {"reassignTo": "Reassign to", "successNotif": {"text": "Job has been reassigned to {{user}}", "title": "Reassigned job"}, "teamMemberBusyNotif": {"text": "Two jobs can't be assigned at the same time.", "title": "The team member has another job assigned"}}, "phaseChangingBlock": {"text": "Do you want to change the phase?", "onStart": {"positiveItemText": "Yes, start job and change the phase to \"{{phase}}\"", "negativeItemText": "No, start job and maintain the \"{{phase}}\" phase."}, "onStop": {"positiveItemText": "Yes,  stop the job and change the phase to \"{{phase}}\"", "negativeItemText": "No, stop the job and keep the \"{{phase}}\" phase."}}, "stopJobValidationPopup": {"incompleteInformationTitle": "Incomplete Information", "pendingSignatureTitle": "Pending signature", "body": "This job cannot be stopped until", "mandatoryFieldNotSpecified": "The fields in the “More job information” pop-up are completed.", "noStandardOperationSpecified": "The operation codes and standard times are completed from “Edit job” pop-up.", "pendingSignature": "The Technician's signature is added from mobile app.", "completeInformation": "Complete information", "ok": "OK"}, "pauseJobBlock": {"selectPauseReason": "Please, select the pause reason"}, "requestPasswordBlock": {"assignToMessage": "The job is assigned to {{name}}", "requestPasswordMessage": "If you are {{name}}, please enter your password and click on the “{{buttonTitle}}” button.", "pleaseTryAgain": "Please try again.", "incorrectPassword": "Incorrect password", "startJob": "Start job", "pauseJob": "Pause job", "stopJob": "Stop job", "resumeJob": "Resume job"}, "detailsTooltip": {"orderType": "Order type", "promiseOfDelivery": "Promise of delivery", "phase": "Phase", "duration": "Duration", "serviceAdvisor": "[Title:ServiceAdvisor]", "appointmentTitle": "Appt.", "appointmentReason": "[Sentence:ReasonForVisit]", "internalNotes": "Internal notes", "visibleToCustomerNotes": "Notes visible to the customer", "status": "Status", "appointmentTime": "Appointment time", "jobDescription": "Job description"}, "didNotArrive": {"title": "Did not arrive"}, "indicators": {"capacity": "Capacity", "utilization": "Utilization"}, "technicianCapacityTooltip": {"inputData": "Input data", "period": "Period", "availableActualTechnicalCapacityTitle": "Available Actual Technical Capacity: {{hours}} hours", "availableActualTechnicalCapacity": "Available Actual Technical Capacity", "actualTechnicalCapacityUsed": "Actual Technical Capacity used", "actualTechnicalCapacityTitle": "Actual Technical Capacity: {{hours}} hours", "actualTechnicalCapacity": "Actual Technical Capacity", "technicians": "[Title:Technicians]", "availableHours": "Available hours", "assignedHours": "Assigned hours for the period", "averageAvailableHours": "Average available hours per [Title:Technician]", "averageAvailableHoursWithNewLine": "Average available hours \nper [Title:Technician]", "workDays": "Work days", "technicalProductivity": "Technical Productivity", "prevWeekTechnicalProductivity": "Technical Productivity of the previous week", "prevWeekTechnicalProductivityWithNewLine": "Technical Productivity \nof the previous week", "productiveHours": "Productive hours", "workedHours": "Worked hours", "technicianProductivity": "Technical Productivity: {{percentage}} %", "completedJobs": "Completed Jobs"}, "technicianCapacityAlert": {"title": "Actual Technical Capacity achieved", "body": "<1><2>The Actual Technical Capacity has reached {{capacity}}%. It is recommended to reserve the remaining {{remain}}% for urgent or unscheduled jobs.</2><2>Do you wish to continue adding job?</2></1>", "cancel": "No, cancel", "confirm": "Yes, continue"}, "workshopUtilizationTooltip": {"inputData": "Input data", "period": "Period", "actualTechnicalCapacity": "Actual Technical Capacity", "averageAvailableHours": "Average available hours per [Title:Technician]", "workDays": "Work days", "potentialCapacity": "Potential Capacity", "potentialCapacityTitle": "Potential Capacity (Installed Capacity): {{hours}} hours", "productiveWorkspaces": "Productive workspaces", "workshopUtilization": "Workshop Utilization", "hours": "hours"}, "invalidSchedule": {"title": "Invalid working hours", "text": "The registered schedule for this day is invalid because the closing time is earlier than the opening time.", "settings": "Click <1>here</1> to review the settings.", "bugReport": "If you believe this is an error, please contact us."}, "changeJobStatusPopup": {"title": "Do you want to change the phase?", "body": "The job has been {{action}} and the Order is currently in the <bold>\"{{currentPhase}}\"</bold> phase.", "cancelButton": "No, keep current phase", "continueButton": "Yes, change phase", "actionStarted": "started", "actionPaused": "paused", "actionResumed": "resumed", "actionStopped": "finished"}, "changeJobPhasePopup": {"title": "Please select the phase:", "dropdownLabel": "Select a phase", "cancelButton": "Back", "continueButton": "Confirm"}, "closedOrderWarning": {"body1": "The Order has been closed by {{userDisplayName}} on {{- formattedDateTime}}.", "body2": "To make changes, the Order must be reopened.", "title": "Closed Order", "button": "Ok"}}, "towerAlert": {"towerInUse": "Tower in use", "theTowerNumberIsBeingUsedByOrder": "The tower number is being used by Order"}, "syncEstimate": {"syncEstimate": "Sync [Lower:Estimate]", "getEstimateFromYourIntegratedSoftware": "Get [Lower:Estimate] from your integrated software", "uploadEstimateToYourIntegratedSoftware": "Upload [Lower:Estimate] to your integrated software", "estimateSuccessfullySynchronized": "[Title:Estimate] successfully synchronized.", "theEstimateWasNotSuccessfullySynchronized": "The [Lower:Estimate] was not successfully synchronized", "obtainEstimateFrom": "Get estimate from {{integratedAccountName}}", "uploadEstimateTo": "Upload estimate to {{integratedAccountName}}", "obtainEstimateFrom3rdPartySoftware": "Get estimate from third party software", "uploadEstimateTo3rdPartySoftware": "Upload estimate to third party software", "estimateSynchronized": {"success": {"title": "[Title:Estimate] successfully synchronized.", "description": "[Title:Estimate] successfully synchronized with {{integratedAccountName}}"}, "error": {"title": "The [Lower:Estimate] was not successfully synchronized with {{integratedAccountName}}.", "description": "Error message from {{integratedAccountName}}"}}}, "enterprise": {"appointments": {"new": {"outsideLocationSchedule": {"title": "The appointment can't be scheduled", "text": "The selected time is outside the location's working hours"}}}}, "viewByCost": {"tabs": {"estimate": "[Title:ApproveEstimate]", "mobileEstimate": "[Title:Estimate]", "phase": "[Title:VehiclePhase]", "mobilePhase": "Phase", "generalInfo": "General information", "mobileGeneralInfo": "General Inf."}, "common": {"documentTitle": "Order #", "greeting": "Hi! ", "urgent": "<PERSON><PERSON>", "medium": "Suggested", "ok": "Ok", "options": "Options", "es-MX": "Español", "en-US": "English", "viewPdfOfTheOrder": "View PDF of the order"}, "estimate": {"notes": "Notes about [Lower:Estimate]", "approve": "Approve", "approved": "Approved", "text1": "Select jobs and items to approve the [Lower:Estimate], and", "text2": "expedite the delivery of your vehicle.", "approveTitle": "Total [Lower:Estimate]", "approveTitle2": "Confirm your approval", "discount": "Discounts:", "subtotal": "Subtotal:", "taxes": "Taxes:", "total": "Total:", "cancel": "Cancel", "confirm": "Confirm", "pay": "Pay", "approvedMessage": "Thank you, we have reported your approval.", "approvedMessageDescription": "For your convenience, making the payment now will speed up the process and help you avoid waiting at the establishment.", "approvedMessageQuestion": "Would you like to proceed with the payment at this time?", "paymentProcessedTitle": "Thank you, your payment request is being processed.", "paymentProcessedDescription": "We recommend saving the receipt or taking a screenshot of this page as additional backup.", "paymentWhatsappTitle": "Thank you, your approval has been recorded.", "paymentWhatsappDescription": "You will receive a WhatsApp confirmation once the payment is completed. We recommend that you save the receipt or take a screenshot as an additional backup.", "nowButton": "Pay now", "laterButton": "Pay later", "closeButton": "Close", "available": "Available", "yes": "Yes", "no": "No", "inventoryComments": "Inventory comments", "PDF": "View Order in PDF version", "closedOrderWarning": {"title": "Closed Order", "body1": "The Order is closed. You can review all related information, but it is not possible to approve estimate items.", "body2": "We suggest you contact your Service Advisor to resolve any questions.", "button": "Ok"}}, "tableHeaders": {"all": "All", "jobsAndInspectionItems": "Jobs and inspection items", "estimate": "[Title:Estimate]"}, "modalEvidences": {"of": "of", "evidences": "evidences", "videoEvidence": "Video evidence"}, "modalComments": {"notes": "Notes"}, "generalInfo": {"orderInformation": "Order Information", "generalLocationInfo": "Location Information", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "name": "Name", "email": "Email", "mobile": "Mobile", "landline": "Landline", "vehicle": "Vehicle", "mileage": "[Title:Mileage]", "vin": "VIN", "plates": "Plates", "businessInformation": "Business Information", "address": "Address", "rfc": "RFC", "phone": "Landline", "teamMemberInCharge": "[Title:ServiceAdvisor]", "serviceHours": "[Sentence:ServiceHours]", "mondayFriday": "Monday - Friday", "saturday": "Saturday"}, "phase": {"deliveryDate": "Estimated delivery date:", "hrs": "hrs."}}, "absences": {"block": {"allUsers": "All", "allDay": "All day"}, "createPopup": {"title": "Absence", "personWhoSchedules": "Person who schedules absence: {{user}}", "teamMemberLabel": "Team member", "reasonLabel": "Absence reason", "reasonPlaceholder": "Enter absence reason", "allDayLabel": "All day", "startDateLabel": "Start date", "endDateLabel": "End date", "dateLabel": "Date", "startTimeLabel": "Start time", "endTimeLabel": "End time", "notesLabel": "Notes", "notesPlaceholder": "Enter notes", "cancelButton": "Cancel", "createButton": "Create absence", "createdAbsencesTitle": "Scheduled absence", "createdAbsencesBody": "Scheduled absence for {{reason}}", "selectTeamMember": "Select team member"}, "editPopup": {"title": "Absence detail", "personWhoSchedules": "Person who scheduled absence: {{user}}", "teamMemberLabel": "Team member", "reasonLabel": "Absence reason", "reasonPlaceholder": "Enter absence reason", "allDayLabel": "All day", "startDateLabel": "Start date", "endDateLabel": "End date", "dateLabel": "Date", "startTimeLabel": "Start time", "endTimeLabel": "End time", "notesLabel": "Notes", "notesPlaceholder": "Enter notes", "cancelButton": "Cancel", "saveButton": "Save changes", "updatedAbsencesTitle": "Absence updated", "updatedAbsencesBody": "The absence has been successfully updated.", "selectTeamMember": "Select team member"}, "deleteModal": {"title": "Delete absence", "body": "Do you want to delete this absence?", "cancelButton": "Do not delete", "confirmButton": "Delete absence", "notificationTitle": "Absence deleted", "notificationBody": "The absence has been successfully deleted."}, "continueWithSchedule": {"title": "Continue with the scheduled absence?", "body": "There are <1>{{count}}</1> scheduled appointments or Orders that this team member would not be able to attend if you schedule the absence.<2></2><2></2>We suggest moving them to another time or team member, then try scheduling the absence again.", "cancelButton": "Cancel", "yesCancel": "Yes, cancel", "goBack": "Go back", "cancelText": "Do you want to cancel the absence registration?", "cancelEditText": "Do you want to cancel the absence edition?", "saveButton": "Save changes"}, "blockOverlapAbsenceErrorTitle": "Scheduled absence", "blockOverlapAbsenceErrorBody": "Team member not available during these hours."}, "customAppointmentReasonsForms": {"activate": {"title": "Activate customizable [Lower:ReasonsForVisit]", "description": "Customize the [Lower:ReasonsForVisit] for your business."}, "form": {"newTitle": "New [Lower:ReasonForVisit]", "editTitle": "Edit [Lower:ReasonForVisit]", "newButton": "Create [Lower:ReasonForVisit]", "editButton": "Save changes", "nameCaption": "[Sentence:ReasonForVisit] name", "namePlaceholder": "Enter [Lower:ReasonForVisit] name", "colorCaption": "[Sentence:ReasonForVisit] color", "colorPlaceholder": "Select [Lower:ReasonForVisit] color", "cancelNewTitle": "Cancel creating [Lower:ReasonForVisit]?", "cancelEditTitle": "Cancel editing  [Lower:ReasonForVisit]?"}, "appointmentReasons": {"addButton": "Add new [Lower:ReasonForVisit]", "notifications": {"createSuccessTitle": "[Sentence:ReasonForVisit] created", "updateSuccessTitle": "[Sentence:ReasonForVisit] updated", "errorTitle": "[Sentence:ReasonForVisit] already exists", "settingsSuccessTitle": "Updated settings"}, "delete": {"title": "Delete [Lower:ReasonForVisit] and its details?", "description": "This action is irreversible.", "cancel": "Go back", "confirm": "Yes, delete"}}, "details": {"addButton": "Create [Lower:ReasonForVisit] detail", "importButton": "Import [Lower:ReasonForVisit] detail", "form": {"title": "New [Lower:ReasonForVisit] detail", "newReason": "[Sentence:ReasonForVisit] detail name", "newReasonPlaceholder": "Select or add [Lower:ReasonForVisit] detail", "brand": "Brand", "brandPlaceholder": "Select brand", "model": "Model", "modelPlaceholder": "Select model", "year": "Year", "yearPlaceholder": "Select year", "selectAll": "Select all", "selectAllBrands": "Select all", "allBrandsSelected": "All brands", "allModelsSelected": "All models", "allYearsSelected": "All years", "allSelected": "All selected", "missingBrand": "Select at least one brand to see models", "modalTitle": "[Sentence:ReasonForVisit] detail", "modalDescription": "List of all existing [Lower:ReasonForVisit] detail", "editTitle": "Edit [Lower:ReasonForVisit] detail", "editButton": "Save changes", "cancelTitle": "Cancel creating [Lower:ReasonForVisit] detail?", "cancelConfirm": "Yes, cancel", "goBack": "Go back"}, "notifications": {"createSuccessTitle": "[Sentence:ReasonForVisit] detail created", "missingName": "[Sentence:ReasonForVisit] detail name is required", "errorTitle": "[Sentence:ReasonForVisit] detail already exists", "updateSuccessTitle": "[Sentence:ReasonForVisit] updated", "updateErrorTitle": "Error updating [Lower:ReasonForVisit] detail"}, "name": "[Sentence:ReasonForVisit] detail", "brand": "Brand", "model": "Model", "year": "Year", "allBrands": "All", "allDefault": "All", "deleteTitle": "Delete [Lower:ReasonForVisit] detail?"}}, "siteForAppointments": {"common": {"mandatoryField": "Mandatory field"}, "step1": {"title": "STEP 1", "description": "Vehicle and [Sentence:ReasonForVisit]", "info": "Fields that appear in STEP 1 when scheduling an online appointment.", "brand": "Brand", "selectBrandsTitle": "Select the brands your customer can use to schedule appointments"}, "step2": {"title": "STEP 2", "description": "Schedule and Customer", "info": "Fields that appear in STEP 2 when scheduling an online appointment.", "schedulesInfo": "The customization of schedules can be done from the Sidebar menu -> “Settings” -> “General” -> “Team members” -> “Work schedule”. You can customize the appointment reception schedule by enabling the “Activate appointment reception schedule” function."}, "step3": {"title": "STEP 3", "description": "Confirmation", "info": "Choose which information appears in STEP 3 when scheduling an online appointment.", "showCancellationPolicy": "Show cancellation policies", "showPrivacyNotice": "Show privacy notice", "privacyNoticeText": "The customization of the privacy notice can be done from the Sidebar Menu -> Settings -> “ClearMechanic” -> “General”. You can customize the header of the privacy notice as well as the text of the privacy notice.", "clear": {"clear": "Clear", "title": "Clear cancellation policies", "text": "Are you sure you want to clear the cancellation policies?\nThis will override any previous customizations made."}}, "types": {"Dropdown": "Dropdown", "FreeText": "Free text", "PhoneNumber": "Phone number", "Email": "Email"}, "general": {"title": "General", "minimumTime": "Minimum time to schedule an appointment", "selectLeadTime": "Select the lead time", "hour": "Hour", "hours": "Hours", "day": "Day", "days": "Days", "week": "Week", "weeks": "Weeks"}}, "phaseSetbackPopup": {"title": "Write the reason for the phase setback", "placeholder": "Write the reason for the setback here"}, "jobsInProgressPopup": {"title": "Jobs in progress", "bodyChangePhase": "Order #{{orderNumber}} has jobs in progress. You may change the phase of the Order, but first we recommend finishing these jobs.", "bodyClose": "We detected jobs in progress on Order #{{orderNumber}}. To close the Order, it is necessary to finish the jobs.", "scheduledJob": "Job {{index}}<br/>Scheduled start time: {{time}}", "dateFormat": "MMM DD - HH:mm", "noEditJobsPermission": "You don't have permissions to edit jobs", "finishAndChangePhase": "Finish and change phase", "finishAndClose": "Finish and close", "continueAnyway": "Continue anyway", "mandatoryFieldNotSpecified": "The required fields in the “More job information” pop-up are not completed.", "noStandardOperationSpecified": "The operation codes and standard times are not completed from “Edit job” pop-up.", "pendingSignature": "The Technician's signature is not added from mobile app."}, "orderTypePicker": {"itWillAppearForOrders": "It will appear for Orders:", "allOrderTypes": "All Order types"}, "afterSalesCrm": {"vehicleDetails": {"contactDetails": {"placeholder": {"name": "Enter name", "lastName": "Enter last name", "phone": "Enter phone", "mobile": "Enter mobile", "email": "Enter email", "businessName": "Enter business name", "taxId": "Enter [Lower:TaxIdentification]", "taxEmail": "Enter billing email", "street": "Enter street and interior number", "neighborhood": "Enter neighborhood", "municipality": "Enter municipality or borough", "zipCode": "Enter ZIP code", "city": "Enter city", "state": "Enter state", "selectYesOrNo": "Select yes or no"}, "generalInfo": "General information", "name": "Name", "phone": "Phone", "lastName": "Last name", "mobile": "Mobile", "email": "Email", "associateContactWith": "Associate contact with", "vehicle": "Vehicle", "businessName": "Business name", "taxId": "[Title:TaxIdentification]", "street": "Street and interior number", "neighborhood": "Neighborhood", "municipality": "Municipality", "zipCode": "ZIP code", "city": "City", "state": "State", "taxEmail": "Billing email", "addContact": "Add contact", "contactAlreadyAssociated": "Contact already associated with the vehicle.", "noContactsFound": "No contacts found", "tryAgainOrFillFields": "Try again, or fill in the fields to create a new one.", "searchContactPlaceholder": "Search by name, mobile, or plate of another vehicle", "successTitle": "Contact added", "successMessage": "Contact added successfully", "errorTitle": "Error", "errorMessage": "Error. Please try again later.", "errorTitleAlreadyExist": "Mobile already registered", "errorMessageAlreadyExist": "Registered to: {{name}}", "errorTitleLocalAlreadyExist": "Contact already associated", "errorMessageLocalAlreadyExist": "This mobile number is already linked to this vehicle.", "firstOwner": "First owner", "contactAssociateWith": "Contact associate  with", "editContact": "Edit contact", "saveChanges": "Save changes", "updateSuccessTitle": "Contact edited", "updateSuccessBody": "Contact edited successfully", "clear": "Change", "update": "Update", "establishAsOwner": "Establish as owner of the vehicle", "willBeAssociatedAsOwnerOf": "will be associated as owner of the vehicle", "removeSuccessTitle": "Association removed", "removeSuccessMessage": "Unlinked contact from the vehicle", "remove": "Remove association", "confirmationPlaceholder": "Indicate the reason for disassociating this contact. It must be at least 5 characters long and cannot contain only symbols or spaces.", "willBeUnlinkedFrom": "will no longer be a contact associated with the", "removeAssociationTitle": "Remove association with this vehicle", "removeLabel": "Reason for disassociation:", "reasonForDisassociation": "Reason for disassociation:", "ownerSuccessTitle": "Save changes", "ownerSuccessMessage": "Contact associated as owner"}, "notFound": "Vehicle not found", "notFoundDescription": "Vehicle you requested does not exist in your repair shop. If you believe this is an error, please contact support.", "title": "Vehicle detail", "tabs": {"activities": "Activities", "whatsapp": "WhatsApp", "orders": "Orders", "activityLog": "Activity log"}, "contacts": "Contacts", "owner": "Owner", "contact": "Contact", "callContact": "Call this contact", "copyEmail": "Copy email", "emailCopied": "Email copied!", "firstOwner": "First owner", "billingInformation": "Billing information", "noAssociatedContacts": "This vehicle has no associated contacts.", "createOrAssociateContact": "Create or associate a contact!", "removeAssociation": "Remove association with this vehicle", "setAsOwner": "Establish as owner of the vehicle", "fallbackFromCacheWarning": "We couldn't retrieve the latest information. Showing previously saved data.", "sections": {"generalInfo": "General info", "vehicleInfo": "Vehicle info", "salesAndServiceInfo": "Sales and service info"}, "vinStatus": {"Inactive": "Inactive", "Active": "Active", "ActivatedByAnother": "Activated by another", "Recent": "Recent"}, "fields": {"assignedBdcAdvisor": "Assigned BDC Advisor", "assignedBdcAdvisorPlaceholder": "Select BDC Advisor", "customerStatus": "Customer status", "customerStatusPlaceholder": "No customer status selected", "vinStatus": "VIN status", "customerType": "Customer type", "prospectionPriority": "Prospection priority", "retentionBand": "Retention band", "pendingCampaigns": "Pending campaigns (Recalls)", "lastActivity": "Last activity", "lastActivityPlaceholder": "No last activity performed", "lastActivityDate": "Last activity date", "monthsDaysSinceLastActivity": "Months/days since last activity", "nextActivityDate": "Next activity date", "brand": "Brand", "model": "Model", "year": "Year", "plates": "Plates", "platesPlaceholder": "Enter plates", "platesAlreadyRegistered": {"title": "Plates already registered", "text": "These plates belong to another vehicle. Please verify the information."}, "vin": "VIN", "vinPlaceholder": "Enter VIN", "vinAlreadyRegistered": {"text": "This VIN belongs to another vehicle. Please verify the information.", "title": "VIN already registered"}, "mileage": "[Title:Mileage]", "saleDate": "Sale date", "monthsFromDateOfSale": "Months from the date of sale", "lastServiceDate": "Last service date", "monthsSinceLastService": "Months since last service", "lastServiceDateWithMe": "Last service date with me", "monthsSinceLastServiceWithMe": "Months since last service with me", "lastServiceDateWithAnother": "Last service date with another", "monthsSinceLastServiceWithAnother": "Months since last service with another", "usageType": "Usage type", "usageTypePlaceholder": "Select a usage type", "recommendedServiceFrequency": "Recommended service frequency", "recommendedServiceFrequencyPlaceholder": "Select a recommended service frequency", "soldByDealer": "Sold by dealer", "soldByDealerPlaceholder": "Select an option", "bacThatSold": "BAC that sold (branch #)", "inPma": "In PMA (within geographical area)"}, "customerType": {"Fleet": "Fleet", "Public": "Public"}, "usageType": {"Personal": "Personal", "Commercial": "Commercial", "Rideshare": "Rideshare"}, "readonlyField": {"header": "This field is not editable", "customerStatus": "It is updated automatically based on the last activity or appointment.", "vinStatus": "It is calculated by the CRM based on VIN, sale date, and service history.", "customerType": "It is retrieved from the DMS.", "prospectionPriority": "It is calculated by the CRM based on the sale date and service history.", "retentionBand": "It is calculated by the CRM based on time since the vehicle's sale.", "pendingCampaigns": "It is retrieved from the DMS.", "lastActivity": "It is updated automatically from the “Activities” tab.", "lastActivityDate": "It is auto-filled based on the date of the last recorded activity.", "monthsDaysSinceLastActivity": "It is calculated based on the last activity date.", "nextActivityDate": "It is auto-filled based on the next scheduled task or appointment.", "monthsFromDateOfSale": "It is calculated based on the sale date.", "lastServiceDate": "It shows the most recent date between “service with me” and “with another”.", "monthsSinceLastService": "It is calculated based on the last service date.", "monthsSinceLastServiceWithMe": "It is calculated based on the last service at your dealership.", "monthsSinceLastServiceWithAnother": "It is calculated based on the last service at another dealership.", "bacThatSold": "It is retrieved from the DMS or import file.", "inPma": "It is retrieved from the DMS or import file."}}, "columns": {"vin": "VIN", "plates": "Plates", "brand": "Brand", "model": "Model", "year": "Year", "actions": "Actions", "vehicle": "Vehicle", "contacts": "Contact", "activity": "Activity", "nextActivity": "Next activity", "priority": "Priority", "service": "Service", "bdcAdvisor": "BDC Advisor", "soldBy": "Sold by", "lastServiceDateWithOther": "Last service date with other", "phone": "Mobile", "email": "Email", "owner": "Owner", "contact": "Contact", "nextService": "Next service", "lastAtOtherDealership": "Last at other dealership", "lastAtMyDealership": "Last at my dealership", "monthsSinceServiced": "Last serviced {{months}} months ago", "monthsSinceSold": "Sold {{months}} months ago", "recommendedService": "Last at my dealership", "call": "Call", "whatsapp": "WhatsApp", "whatsappTooltip": "Send WhatsApp", "note": "Note", "noteTooltip": "Create note", "task": "Task", "taskTooltip": "Create task", "appt": "Appt", "mileage": "[Title:Mileage]", "lastActivity": "Last activity", "lastActivityDate": "Last activity date", "monthsDaysSinceLastActivity": "Months/Days since last activity", "dateOfNextActivity": "Date of next activity", "recommendedServiceFrequency": "Recommended service frequency", "assignedBdcAdvisor": "Assigned BDC advisor", "pendingCampaigns": "Pending campaigns (Recalls)", "neighborhood": "Neighborhood", "districtOrMunicipality": "District or municipality", "zipCode": "Zip code", "city": "City", "state": "State", "vinStatus": "VIN status", "prospectionPriority": "Prospection priority", "retentionBand": "Retention band", "soldByDealer": "Sold by dealer", "bacThatSold": "BAC that sold (Branch #)", "inPma": "In PMA", "saleDate": "Sale date", "monthsFromSaleDate": "Months from sale date", "lastServiceDate": "Last service date", "monthsSinceLastService": "Months since last service", "lastServiceDateWithMe": "Last service date with me", "monthsSinceLastServiceWithMe": "Months since last service with me", "lastServiceDateWithAnother": "Last service date with another", "monthsSinceLastServiceWithAnother": "Months since last service with another", "customerType": "Customer type", "usageType": "Usage type", "customerStatus": "Customer status", "isFirstOwner": "First owner"}, "values": {"noPriority": "No priority", "Inactive": "Inactive", "Active": "Active", "ActivatedByAnother": "Activated by another", "Recent": "Recent", "EmptyVin": "Empty VIN", "Fleet": "Fleet", "Public": "Public", "Platform": "Platform", "Personal": "Personal", "Commercial": "Commercial", "Rideshare": "Rideshare", "SixThousand": "Six thousand", "TwelveThousand": "Twelve thousand", "WithoutContact": "Without contact", "Unreachable": "Unreachable", "Contacted": "Contacted", "ScheduledAppointment": "Scheduled appointment", "CompletedAppointment": "Completed appointment", "NoShow": "No show", "CanceledAppointment": "Canceled appointment", "NoAnswer": "No answer", "NoPhoneWhatsAppOrEmail": "No phone, WhatsApp, or email", "IncorrectNumberDifferentPerson": "Incorrect number, different person", "CustomerDeceased": "Customer deceased", "CustomerNoLongerHasVehicle": "Customer no longer has vehicle", "VehicleTotalLoss": "Vehicle total loss", "CourtesyCall": "Courtesy call", "InformationalContact": "Informational contact", "CompletedService": "Completed service", "ComplaintOrBadExperience": "Complaint or bad experience", "NotDueForServiceYet": "Not due for service yet", "NotInterestedDueToPrice": "Not interested due to price", "NotInterestedOtherReason": "Not interested (other reason)", "NotInterestedDueToTime": "Not interested due to time", "NotWithinPMA": "Not within PMA", "InterestedInFollowUp": "Interested in follow-up", "WillCallBack": "Will call back", "DoesNotWishToBeContacted": "Does not wish to be contacted", "Other": "Other", "ProactiveAppointmentScheduled": "Proactive appointment scheduled", "ReactiveAppointmentScheduled": "Reactive appointment scheduled", "AppointmentConfirmation": "Appointment confirmation", "RescheduledAppointment": "Rescheduled appointment", "VehicleReadyNotification": "Vehicle ready notification", "ThirdDayFollowUpCall": "Third-day follow-up call", "NoShowReschedulingNoContact": "No-show rescheduling, no contact", "RequestsToCancelAppointment": "Requests to cancel appointment", "Yes": "Yes", "No": "No", "recommendedServiceFrequency": "Every {{km}}km or {{months}} months"}, "searchBar": {"placeholder": "Search: VIN, plates", "placeholderVehicles": "Search: VIN, plates, customer, mobile", "vehicle": "Vehicle", "plate": "Plate", "vin": "VIN", "label": "Vehicles", "noResults": "No matches found for the search performed.", "contact": "Contact", "owner": "Owner"}, "filterMenu": {"all": "All", "clear": "Clear", "Retention": "Retention", "FollowUp": "Follow-up", "Vehicle": "Vehicle", "GeographicLocation": "Geographic location", "allColumns": "All columns", "columnsTitle": "Columns"}, "filters": {"filters": "Filters", "filtersByVehicle": "Filters by vehicle", "selectFilters": "Select filters", "Platform": "Platform", "Personal": "Personal", "Commercial": "Commercial", "allVinStatus": "All statuses", "allProspectionPriority": "All priorities", "allUsageType": "All usage types", "allCustomerStatus": "All customer statuses", "allBrand": "All brands", "allModel": "All models", "allYear": "All years", "allAssignedBdcAdvisor": "All BDC Advisors", "allLastActivity": "All activities", "all": "All", "noBdcAdvisorsFound": "No matches found. Try another team member or check the spelling.", "noYearsFound": "No matches found. Try another year or check the spelling.", "noModelsFound": "No matches found. Try another model or check the spelling.", "noBrandsFound": "No matches found. Try another brand or check the spelling."}, "totalVehiclesResult": "Results: ", "totalVehicles": " vehicles", "newVehicle": "New vehicle", "noVehiclesFoundTitle": "There are no vehicles registered in this account yet", "noVehiclesFoundText": "Create a new vehicle to start managing your database.", "noMatchesFound": "No matches found", "callButton": {"hint": "Make phone call", "call": "Call {{number}}"}, "appointmentButton": {"hint": "Create appointment"}, "viewDetailsButton": {"label": "View details", "tooltip": "View vehicle details"}, "vehicleDetailsCell": {"mileage": "Mileage", "usageType": "Usage type", "lastOrder": "Last Order"}}}