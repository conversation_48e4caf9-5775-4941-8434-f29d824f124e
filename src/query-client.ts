import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { QueryCache, QueryClient } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import BUILD_INFO from './build-id';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: false,
            // set cache time to 1s to make sure that query data
            // will not be discarded immediately after query
            // client is restored from cache
            cacheTime: 1000,
            // after data is retrieved it is marked as "stale" after this many milliseconds
            // if data is NOT "stale" useQuery DOES NOT make a DB call and just uses data from the cache
            staleTime: 0,
        },
    },
    queryCache: new QueryCache(),
});

const localStoragePersister = createSyncStoragePersister({ storage: window.localStorage });

// persist data to localStorage every time something changes
// NOTE this may (or may not, I'm not sure) affect performance if we have a ton of stuff in cache (most likely not an issue
// because we're not gonna have a ton of stuff in cache)
persistQueryClient({
    queryClient,
    maxAge: 7 * 24 * 60 * 60 * 1000,
    persister: localStoragePersister,
    dehydrateOptions: {
        shouldDehydrateQuery: (query) => {
            const result =
                (query.state.status === 'success' || query.state.status === 'loading') &&
                (!query.meta || !query.meta.noPersist);
            return result;
        },
        dehydrateQueries: true,
    },
    // NOTE: whenever buster changes the caches is completely invalidated
    buster: BUILD_INFO.BUILD_ID,
});

export default queryClient;
