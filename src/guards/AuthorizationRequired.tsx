import { ENTERPRISE_ROUTES, ROUTES } from 'common/constants';
import useIsEnterpriseRoute from 'common/hooks/useIsEnterpriseRoute';
import { Navigate, useLocation } from 'react-router-dom';
import { useCurrentUserOptional } from 'store/slices/user';

export default function AuthorizationRequired({ children }: React.PropsWithChildren<{}>) {
    const user = useCurrentUserOptional();
    const loginRoute = useLoginUrl();

    if (!user) {
        return <Navigate replace to={loginRoute} />;
    }

    return <>{children}</>;
}

function useLoginUrl() {
    const location = useLocation();
    const isEnterprise = useIsEnterpriseRoute();

    const query =
        '?next=' +
        encodeURIComponent(location.pathname + (location.search ? `${location.search}` : ''));

    if (isEnterprise) {
        return ENTERPRISE_ROUTES.LOGIN + query;
    } else {
        return ROUTES.LOGIN + query;
    }
}
