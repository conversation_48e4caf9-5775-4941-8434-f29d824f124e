/* disable dark scrollbar on browsers that support it, because we do not have dark mode */
@supports (color-scheme: light) {
    html {
        color-scheme: light;
    }
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
        'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#root {
    height: 100%;

    /* to prevent margin collaps */
    display: flex;
    flex-direction: column;
    align-items: stretch;
    min-height: 100vh;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

a.a-inherit,
a.a-inherit:hover,
a.a-inherit:focus,
a.a-inherit:active {
    text-decoration: inherit;
    color: inherit;
    font: inherit;
}

.scrollbar-gutter-fix {
    padding-left: calc(100vw - 100%);
}
