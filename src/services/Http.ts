import axios, { AxiosError, AxiosHeaders, AxiosInstance, AxiosRequestConfig } from 'axios';
import { createEvent } from 'utils/event';
import { HttpException } from '../datacontracts/HttpException';

/**
 * Do not add references to the http library (Axios, Fetch, etc) used in the application outside of this class,
 * Try to keep all http client handling within this class.
 */
export class Http {
    private static readonly LOG_TAG = '[HTTP]';
    private static readonly LOCAL = window.location.host.includes('localhost');
    private static _accessTokenAccessor: () => string | undefined = () => undefined;
    public static axiosError = createEvent<AxiosError>();

    static get HOST(): string {
        return Http.LOCAL ? import.meta.env.VITE_LOCAL_API_URL ?? '' : '';
    }

    static getConfig(authRequired = true) {
        const configs: AxiosRequestConfig = {};

        const headers = new AxiosHeaders({
            'Content-type': 'application/json',
        });
        configs.headers = headers;

        return configs;
    }

    private static apiURL(action: string) {
        return `${Http.HOST}/api/dashboard/${action}`;
    }

    public static setAccessTokenAccessor(accessTokenAccessor: () => string | undefined) {
        this._accessTokenAccessor = accessTokenAccessor;
    }

    static getHttpClient(authRequired = true): AxiosInstance {
        const client = axios.create(Http.getConfig(authRequired));

        client.interceptors.request.use((req) => {
            req.headers = req.headers ?? new AxiosHeaders();
            const accessToken = Http._accessTokenAccessor();
            if (accessToken) {
                req.headers.setAuthorization(`Bearer ${accessToken}`);
            }

            return req;
        });

        return client;
    }
    /*
  Convert Https.Get in Promise format
  */
    public static async Get(
        action: string,
        json = true,
        authRequired = true,
        withCredentials = true,
        config?: AxiosRequestConfig
    ) {
        const URL = Http.apiURL(action);
        if (Http.LOCAL) console.debug(Http.LOG_TAG, '[GET]', URL);

        return Http.pipeJsonResponse(
            Http.getHttpClient(authRequired).get(URL, { ...config, withCredentials }),
            URL,
            'GET'
        );
    }

    /*
  Convert Https.Post in Promise format
  */
    public static async Post(
        action: string,
        body: any,
        json = true,
        authRequired = true,
        withCredentials = true,
        customConfigs?: AxiosRequestConfig
    ) {
        const URL = Http.apiURL(action);
        console.debug(Http.LOG_TAG, '[POST]<Trying>', URL, body);

        return Http.pipeJsonResponse(
            Http.getHttpClient(authRequired).post(URL, body, {
                withCredentials,
                ...(customConfigs ?? {}),
            }),
            URL,
            'POST'
        );
    }
    public static async Delete(
        action: string,
        body?: any,
        json = true,
        authRequired = true,
        withCredentials = true
    ) {
        const URL = Http.apiURL(action);
        const data = body ? { ...body } : undefined;
        console.debug(Http.LOG_TAG, '[DELETE]<Trying>', URL, body);

        return Http.pipeJsonResponse(
            Http.getHttpClient(authRequired).delete(URL, { data, withCredentials }),
            URL,
            'DELETE'
        );
    }

    public static async Patch(
        action: string,
        body?: any,
        json = true,
        authRequired = true,
        withCredentials = true,
        customConfigs?: AxiosRequestConfig
    ) {
        const URL = Http.apiURL(action);
        console.debug(Http.LOG_TAG, '[PATCH]<Trying>', URL, body);

        return Http.pipeJsonResponse(
            Http.getHttpClient(authRequired).patch(URL, body, {
                withCredentials,
                ...(customConfigs ?? {}),
            }),
            URL,
            'PATCH'
        );
    }

    public static async download(action: string, outname = 'download', authRequired = true) {
        const URL = Http.apiURL(action);
        const result = await Http.pipeBlobResponse(
            Http.getHttpClient(authRequired).get(URL, { responseType: 'blob' })
        );
        Http.convertBlobToLocalURL(result.data, outname, result.headers['content-disposition']);
    }

    public static async Upload(action: string, formData: FormData) {
        const URL = Http.apiURL(action);

        const config: AxiosRequestConfig = {
            headers: {
                'Content-type': 'multipart/form-data',
                authorization: 'Bearer ' + Http._accessTokenAccessor(),
            },
        };

        return Http.pipeJsonResponse(axios.create(config).post(URL, formData), URL, 'POST');
    }

    // [CMOS-2869 Vsevolod I. 20.06.2024] Need only to debug/profile production data locally here: CMOS.Frontend\src\api\Reports.ts getCustomReportTestPreview
    public static async PostFullUrl(
        url: string,
        body: any,
        authRequired = true,
        withCredentials = true,
        customConfigs?: AxiosRequestConfig
    ) {
        console.debug(Http.LOG_TAG, '[POST]<Trying>', url, body);

        return Http.pipeJsonResponse(
            Http.getHttpClient(authRequired).post(url, body, {
                withCredentials,
                ...(customConfigs ?? {}),
            }),
            url,
            'POST'
        );
    }

    private static convertBlobToLocalURL(blob: any, outname: string, disposition: string) {
        const filename = Http.extractFileNameFromContentDisposition(disposition) ?? outname;
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', filename);
        link.setAttribute('target', '_blank');
        document.body.appendChild(link);
        link.click();
    }

    private static extractFileNameFromContentDisposition(disposition: string): string | null {
        if (disposition && disposition.indexOf('attachment') !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
                return Http.b64DecodeUnicode(matches[1].replace(/['"]/g, ''));
            }
        }
        return null;
    }

    private static b64DecodeUnicode(str: string) {
        // Copypasted from there https://stackoverflow.com/questions/30106476/using-javascripts-atob-to-decode-base64-doesnt-properly-decode-utf-8-strings
        return decodeURIComponent(
            window
                .atob(str)
                .split('')
                .map(function (c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2); //Some formatting magic
                })
                .join('')
        );
    }

    private static async pipeBlobResponse(promise: Promise<any>) {
        return new Promise<any>(async (resolve, reject) => {
            try {
                const result = await promise;
                if (result.status === 200) {
                    return resolve(result);
                } else {
                    return reject(result);
                }
            } catch (error) {
                Http.handleError(error, '');
                return reject(error);
            }
        });
    }

    private static async pipeJsonResponse(promise: Promise<any>, url: string, method: string) {
        return new Promise<any>(async (resolve, reject) => {
            try {
                const { status, data } = await promise;
                if (Http.LOCAL)
                    console.debug(Http.LOG_TAG, `[${method}][${status}]<Response>`, url, data);

                if (status >= 200 && status <= 299) {
                    return resolve(data);
                } else {
                    console.error(Http.LOG_TAG, `[${method}][${status}]<Error>`, data);
                    const errorData: HttpException = { data, status };
                    return reject(errorData);
                }
            } catch (error) {
                Http.handleError(error, method);
                return reject(error);
            }
        });
    }

    private static handleError(error: unknown, method: string) {
        if (axios.isAxiosError(error)) {
            Http.axiosError.publish(error);
        }
    }
}
