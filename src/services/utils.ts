namespace HttpUtils {
    export async function uploadFileViaPut(fullUrl: string, file: File): Promise<void> {
        await fetch(fullUrl, {
            method: 'PUT',
            body: file,
            headers: {
                'Cache-Control': 'no-cache',
            },
        });
    }

    export function downloadViaAnchor(url: string, fileName?: string) {
        const anchor = document.createElement('a');
        anchor.href = url;
        if (fileName) anchor.download = fileName;
        anchor.click();
    }

    export function downloadFile(url: string, fileName?: string) {
        fetch(url, {
            method: 'GET',
            cache: 'no-cache',
        })
            .then((r) => r.blob())
            .then((b) => {
                const url = URL.createObjectURL(b);
                downloadViaAnchor(url, fileName);
            });
    }

    export function openLinkViaAnchor(url: string) {
        const anchor = document.createElement('a');
        anchor.href = url;
        anchor.target = '_blank';
        anchor.click();
    }
}

export { HttpUtils };
