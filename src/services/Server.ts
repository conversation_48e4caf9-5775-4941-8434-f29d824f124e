import { ApiError, wrapAxiosError } from 'api/error';
import axios, {
    AxiosHeaders,
    AxiosInstance,
    AxiosRequestConfig,
    AxiosResponse,
    isAxiosError,
} from 'axios';
import qs from 'qs';
import { createEvent } from 'utils/event';

export type ErrorResponseBase = {
    kind: 'persistent' | 'internal' | 'transient';
    message: string;
};

type TypedErrorResponse<Code extends string, Meta> = {
    code: Code;
    meta: Meta;
} & ErrorResponseBase;

export type ErrorResponse = TypedErrorResponse<string, unknown>;

export function getErrorResponse(v: unknown): ErrorResponse | null {
    if (isErrorResponse(v)) {
        return v;
    }

    if (isAxiosError(v) && v.response && isErrorResponse(v.response.data)) {
        return v.response.data;
    }

    return null;
}

export type CustomErrorCodesWithMeta = {
    // you're trying to schedule too large work
    ['General.WP.Schedule.TooLarge']: null;
    // you're trying to schedule work outside of user's schedule
    ['General.WP.OutsideOfUserSchedule']: {
        duration: string;
        startsAt: string;
    };
    // can't schedule 1 piece of work on few days
    ['General.WP.OutsideOfDayUserSchedule']: {
        duration: string;
    };
    // someone else if working on that order
    ['General.WP.OrderScheduleOccupied']: {
        takenBy: {
            userId: string;
            displayName: string;
        }[];
    };
    // technician is working on other order currently and cannot work on 2 at the same time OR you're trying to schedule work outside of work schedule for that user
    ['General.WP.UserScheduleOccupied']: {
        takenBy: {
            entryId: string;
            orderId: string | null;
            orderNumber: string | null;
            appointmentId: string | null;
            appointmentNumber: string | null;
        }[];
    };
    ['General.WP.Schedule.Overlap']: null;
    ['General.WP.TechnicianNotAssigned']: null;
    ['General.WP.TechnicianCanNotBeReassigned']: null;
    ['General.WP.TowerNumberConflict']: {
        takenBy: {
            number: string;
        };
    };
    ['General.WP.IncompleteColorConfiguration']: {
        configuration: string;
        selectedOption: string;
    };

    ['General.WP.BlockOverlapAbsence']: null;

    ['MassiveSending.InvalidVinFile-FileNotFound']: null;
    ['MassiveSending.InvalidVinFile-InvalidFileType']: null;
    ['MassiveSending.InvalidVinFile-FileTooBig']: null;
    ['MassiveSending.InvalidVinFile-TooManyVins']: null;
    ['MassiveSending.InvalidVinFile-InvalidFileStructure']: null;

    ['Appointments.InvalidAppointmentNumber']: null;
    ['ValidationError']: { payloadErrors: [{ errorCode: string }] };

    ['General.Packages.RequiredColumnMissing']: null;
    ['General.Packages.UnknownMasterItem']: { missingItems: string[] };
    ['General.Packages.EmptyFile']: null;
    ['General.DuplicatedInspectionItem']: null;

    ['General.Users.UserNotFound']: null;
    ['General.Users.PhoneNumberIsMissing']: null;
};

/**
 * Checks if the input is an ErrorResponse object.
 *
 * @param {any} o - the input object to be checked
 * @return {boolean} true if the input is an ErrorResponse object, false otherwise
 */
export function isErrorResponse(o: any): o is ErrorResponse {
    if (o === null || typeof o !== 'object') return false;
    const er = o as ErrorResponse;
    return (
        typeof er.code === 'string' && typeof er.message === 'string' && typeof er.kind === 'string'
    );
}

/**
 * Checks if the given error is a network error.
 *
 * @param {unknown} e - the error to check
 * @return {boolean} true if the error is a network error, false otherwise
 */
export function isNetworkError(e: unknown): boolean {
    return axios.isAxiosError(e) && !e.response && e.message === 'Network Error';
}

/**
 * Checks if the response has the given error code.
 *
 * @param {ErrorResponse} response - the response to check
 * @param {K} key - the error code to check for
 * @return {boolean} true if the response has the given error code, false otherwise
 */
export function hasCode<K extends keyof CustomErrorCodesWithMeta>(
    response: ErrorResponse,
    key: K
): response is TypedErrorResponse<K, CustomErrorCodesWithMeta[K]> {
    return response.code === key;
}

export function hasSubCode<K extends keyof CustomErrorCodesWithMeta>(
    response: ErrorResponse,
    key: K,
    errorCode?: string
): response is TypedErrorResponse<K, CustomErrorCodesWithMeta[K]> {
    if (
        response.code === key &&
        errorCode !== undefined &&
        typeof response.meta === 'object' &&
        response.meta !== null
    ) {
        const payloadErrors = (response.meta as { payloadErrors: unknown }).payloadErrors;
        if (payloadErrors instanceof Array) {
            return payloadErrors.some(
                (e: unknown) =>
                    typeof e === 'object' &&
                    e !== null &&
                    (e as { errorCode: unknown }).errorCode === errorCode
            );
        }
    }

    return false;
}

module Server {
    export const apiError = createEvent<ApiError>();
    export const unknownError = createEvent<unknown>();

    let httpClient: AxiosInstance | undefined = undefined;
    let accessTokenAccessor: () => string | undefined = () => undefined;

    export async function get<T>(action: string, config?: AxiosRequestConfig): Promise<T> {
        return Server.getResponse<T>(action, config).then(handleJsonResponse);
    }

    export async function getResponse<T>(
        action: string,
        config?: AxiosRequestConfig
    ): Promise<AxiosResponse<T>> {
        const url = `${trimSlash(getBaseUrl(), true)}/${trimSlash(action, false)}`;
        const client = getHttpClient();
        const response = await client.get<T>(url, config);
        return response;
    }

    export async function patch<T>(
        action: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data: any,
        config?: AxiosRequestConfig
    ): Promise<T> {
        const url = `${trimSlash(getBaseUrl(), true)}/${trimSlash(action, false)}`;
        const client = getHttpClient();
        const resultPromise = client.patch<T>(url, data, config).then(handleJsonResponse);
        return resultPromise;
    }

    export async function post<T>(
        action: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<T> {
        const url = `${trimSlash(getBaseUrl(), true)}/${trimSlash(action, false)}`;
        const client = getHttpClient();
        const resultPromise = client.post<T>(url, data, config).then(handleJsonResponse);
        return resultPromise;
    }

    export async function put<T>(
        action: string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<T> {
        const url = `${trimSlash(getBaseUrl(), true)}/${trimSlash(action, false)}`;
        const client = getHttpClient();
        const resultPromise = client.put<T>(url, data, config).then(handleJsonResponse);
        return resultPromise;
    }

    export async function delete_<T>(action: string, config?: AxiosRequestConfig): Promise<T> {
        const url = `${trimSlash(getBaseUrl(), true)}/${trimSlash(action, false)}`;
        const client = getHttpClient();
        const resultPromise = client.delete<T>(url, config).then(handleJsonResponse);
        return resultPromise;
    }

    export async function download(
        action: string,
        config?: AxiosRequestConfig,
        outName: string = 'download'
    ) {
        const url = `${trimSlash(getBaseUrl(), true)}/${trimSlash(action, false)}`;
        const client = getHttpClient();
        const result = await client.get(url, {
            ...config,
            responseType: 'blob',
        });

        if (!(result.data instanceof Blob))
            throw new Error('unexpected response type, expected Blob, got ' + typeof result.data);

        convertBlobToLocalURL(result.data, outName, result.headers['content-disposition']);
    }

    export function setAccessTokenAccessor(value: () => string | undefined) {
        accessTokenAccessor = value;
    }

    function handleJsonResponse<T>(response: AxiosResponse<T>): T {
        return response.data;
    }

    export function getHttpClient(): AxiosInstance {
        if (!httpClient) {
            const timeout = import.meta.env.NODE_ENV === 'development' ? 1000 * 3600 : 30000;

            const client = axios.create({
                headers: new AxiosHeaders({
                    'Content-type': 'application/json',
                }),
                timeout,
                paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
            });

            client.interceptors.request.use((request) => {
                if (!request.headers) request.headers = new AxiosHeaders();

                const accessToken = accessTokenAccessor();
                if (accessToken) {
                    const authorizationHeader = `Bearer ${accessToken}`;
                    request.headers.setAuthorization(authorizationHeader);
                }
                return request;
            });

            client.interceptors.response.use(undefined, (error) => {
                if (axios.isAxiosError(error)) {
                    const cmosError = wrapAxiosError(error);
                    apiError.publish(cmosError);
                    return Promise.reject(cmosError);
                } else {
                    unknownError.publish(error);
                }
                return Promise.reject(error);
            });

            httpClient = client;
        }

        return httpClient;
    }

    function convertBlobToLocalURL(blob: BlobPart, outName: string, disposition: string) {
        const filename = extractFileNameFromContentDisposition(disposition) ?? outName;
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', filename);
        link.setAttribute('target', '_blank');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    function extractFileNameFromContentDisposition(disposition: string): string | null {
        if (disposition && disposition.indexOf('attachment') !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) {
                const name = matches[1].replace(/['"]/g, '');
                try {
                    // who knows why we need this but legacy backend uses b64 for some reason
                    return b64DecodeUnicode(name);
                } catch {
                    return name;
                }
            }
        }
        return null;
    }
}

export { Server };
export default Server;

function b64DecodeUnicode(str: string) {
    return decodeURIComponent(
        window
            .atob(str)
            .split('')
            .map(function (c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2); //Some formatting magic
            })
            .join('')
    );
}

function trimSlash(v: string, end: boolean) {
    if (end) {
        if (v.endsWith('/')) {
            return v.substring(0, v.length - 1);
        }
    } else {
        if (v.startsWith('/')) {
            return v.substring(1);
        }
    }
    return v;
}

function getBaseUrl(): string {
    return import.meta.env.VITE_GENERAL_API_BASE_URL ?? '';
}
