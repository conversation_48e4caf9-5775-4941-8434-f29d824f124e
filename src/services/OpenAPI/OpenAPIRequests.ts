import axios, { AxiosInstance, AxiosResponse, Method } from 'axios';
import { ErrorResponse } from './models/ErrorResponse';
import { HttpClient } from './models/HttpClient';
import { HttpParameters } from './models/HttpParameters';

export default class OpenAPIRequest implements HttpClient {
    public http: AxiosInstance;
    constructor(httpParameters: HttpParameters) {
        this.http = axios.create(httpParameters);
    }

    public async createRequest(
        method: Method,
        url: string,
        params?: object,
        data?: object
    ): Promise<AxiosResponse | ErrorResponse> {
        try {
            const res: AxiosResponse = await this.http({
                method,
                url,
                params,
                data,
            });
            return res;
        } catch (e) {
            const error: any = e;
            return error;
        }
    }
}
