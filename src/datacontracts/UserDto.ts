import { UserPermissionDto } from 'api/account';
import { JobTitle } from '../common/constants/JobTitle';
import { UserBaseDto } from './UserBaseDto';
import { UserSchedule } from './Users';

export type UserDto = UserBaseDto & {
    jobTitle: JobTitle | '';
    jobTitleOther: string;
    dmsIDNumber: string;
    landlinePhoneNumber: string;
    landlinePhoneExtension: string;
    mobilePhoneNumber: string;
    isActive: boolean;
    color: string;
    permission: UserPermissionDto;
    userSchedule: UserSchedule[];
    receptionScheduleEnabled: boolean;
    appointmentsPerDay: number;
    specialtyId: number | null;
    dmsUserName: string;
};
