import { JobTitle } from 'common/constants';

export type AdditionalNoteType = 'ForCustomer' | 'ForInternal';

export type AdditionalNoteSection = 'Appointments' | 'OrderDetails' | 'WorkshopPlanner';

export type AdditionalNoteDto = {
    additionalNoteId: number;
    orderId: number;
    text: string;
    isFromCustomer: boolean;
    type: AdditionalNoteType;
    section: AdditionalNoteSection;
    user: AdditionalNoteUserDto | null;
    historyNotes: AdditionalNoteHistoryDto[] | null;
};

export type AdditionalNoteHistoryDto = {
    id: number;
    text: string;
    user: AdditionalNoteUserDto | null;
    createdAt: Date;
};

export type AdditionalNoteUserDto = {
    id: string;
    name: string;
    job: JobTitle | null;
};

export type AdditionalNoteInfoDto = {
    text: string;
    type: AdditionalNoteType;
};
