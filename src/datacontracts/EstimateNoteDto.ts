import { JobTitle } from 'common/constants';

export type EstimateNoteType = 'ForCustomer' | 'ForInternal';

export type EstimateNoteDto = {
    estimateNoteId: number;
    repairOrderId: number;
    user: EstimateNoteUserDto | null;
    text: string;
    type: EstimateNoteType;
    historyNotes: EstimateNoteHistoryDto[] | null;
};

export type EstimateNoteHistoryDto = {
    id: number;
    text: string;
    user: EstimateNoteUserDto;
    createdAt: Date;
};

export type EstimateNoteUserDto = {
    id: string;
    name: string;
    job: JobTitle | null;
};
