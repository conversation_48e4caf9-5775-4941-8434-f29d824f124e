import { PaymentMethodType } from 'common/constants';
import { EstimatePriority } from '../../common/constants/EstimatePriority';
import { OrderStatus } from '../../common/constants/OrderStatus';

export interface IPreviewSubItem {
    name: string | null;
    totalCost: number | null;
}

export interface IPreviewItem {
    subitems: IPreviewSubItem[];
    repairName: string;
    totalCost: number | null;
    priority: EstimatePriority;
}
export interface IRepairOrderPreviewResponse {
    repairOrderId: number;
    repairOrderNumber: string;
    repairShopId: number;
    vehicleId: string | null;
    make: string | null;
    model: string | null;
    year: number | null;
    mileage: number | null;
    vin: string | null;
    plates: string | null;
    customerId: string | null;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    mobilePhone: string | null;
    landlinePhone: string | null;
    inspectionFormStatus: OrderStatus;
    consumerViews: number;
    phoneCallsCount: number;
    photoCount: number;
    videoCount: number;
    redItems: IPreviewItem[];
    yellowItems: IPreviewItem[];
    lastCommunicationTime: Date;
    inspectionLink: string;
    uploadTime: Date;
    businessName: string | null;
    nextServiceDate?: Date;
    hasFollowUp?: boolean;
    calculatedMileage?: number;
    serviceAdvisor: null | {
        key: string;
        name: string;
    };
    appointmentReasonColor: string | null;
    towerNumber: string;
    phaseName: string;
    paymentMethod: PaymentMethodType;
    orderType: {
        id: string;
        name: string;
        color: string | null;
    } | null;
}
