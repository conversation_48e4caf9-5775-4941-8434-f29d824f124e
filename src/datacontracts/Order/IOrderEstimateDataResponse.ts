import { DiscountType } from 'api/settings/OrdersSettings';
import { ApproveStatus, EstimatePriority } from '../../common/constants';

export interface IGetEstimateDataResponse {
    items: IEstimateItem[];
    totalCost: number;
    totalCostWithTaxesAndDiscount: number;
    discount: number;
    discountType: DiscountType;
}

export interface IEstimateItem {
    repairName: string;
    totalCostWithDiscount?: number;
    priority: EstimatePriority;
    approveStatus?: ApproveStatus;
}
