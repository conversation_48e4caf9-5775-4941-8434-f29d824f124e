import { OrderByType, TeamMemberType } from '../../common/constants';

export interface IRepairOrderListRequest {
    From?: string; //Date in format DDmmyyy
    To?: string; //Date in format DDmmyyy
    Vin?: string;
    Plts?: string;
    AStatus?: string;
    OTypes?: string[];
    RShops: number[];
    TmIds?: string; //Numbers separated with "," example: 1,23,45
    Ron?: string; //RepairOrder Number
    CName?: string;
    TmTyp?: TeamMemberType;
    OrdBy?: OrderByType;
    PIdx: number;
    MRows?: number;
    AAlerts?: boolean;
    RAlerts?: boolean;
    Est?: boolean;
    Phas?: string; //Numbers separated with "," example: 1,23,45
    Id?: string;
}

export const RepairOrderListRequestToParams = (filters: IRepairOrderListRequest): string[] => {
    const params = [];

    if (filters.From) params.push(`from=${filters.From}`);
    if (filters.To) params.push(`to=${filters.To}`);
    if (filters.Ron) params.push(`ron=${filters.Ron}`);
    if (filters.Vin) params.push(`vin=${filters.Vin}`);
    if (filters.Plts) params.push(`plts=${filters.Plts}`);
    if (filters.AStatus) params.push(`aStatus=${filters.AStatus}`);

    if (filters.OTypes?.length) {
        const types = filters.OTypes.map((t) => `'${t}'`).join(',');
        params.push(`oTypes=${encodeURIComponent(types)}`);
    }
    if (filters.RShops?.length) {
        const types = filters.RShops.map((t) => t.toString()).join(',');
        params.push(`rShops=${encodeURIComponent(types)}`);
    }

    if (filters.CName) params.push(`cName=${encodeURIComponent(filters.CName)}`);
    if (filters.TmIds && filters.TmIds?.length > 0 && filters.TmTyp) {
        params.push(`tmIds=${filters.TmIds}`);
        params.push(`tmTyp=${filters.TmTyp}`);
    }
    if (filters.PIdx) params.push(`pIdx=${filters.PIdx}`);
    if (filters.MRows) params.push(`mRows=${filters.MRows}`);
    if (filters.OrdBy) params.push(`ordBy=${filters.OrdBy}`);

    if (filters.AAlerts !== undefined) params.push(`aAlerts=${filters.AAlerts}`);
    if (filters.RAlerts !== undefined) params.push(`rAlerts=${filters.RAlerts}`);
    if (filters.Est !== undefined) params.push(`est=${filters.Est}`);

    if (filters.Phas && filters.Phas?.length > 0) {
        params.push(`phas=${filters.Phas}`);
    }

    if (filters.Id) params.push(`id=${filters.Id}`);

    return params;
};
