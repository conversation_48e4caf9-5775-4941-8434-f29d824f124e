import { DiscountType } from 'api/settings/OrdersSettings';
import { PaymentMethodType } from 'common/constants';
import { OrderStatus } from '../../common/constants/OrderStatus';
import { IdentificationDocumentDto } from './IdentificationDocumentDto';

export interface IOrderDetailsResponse {
    uploadTime: string;
    lastUpdateTime: string;
    assignedToInitials: string | null;
    assignedToDisplayName: string | null;
    inChargeUser: null | {
        id: number;
        key: string;
        name: string;
        initials: string;
    };
    inspectionLink: string;
    pdfLink: string;
    spreadsheetPdfLink: string;
    jobsPdfLink: string;
    customPdfLink: string;
    additionalStatus: string | null;
    orderType: {
        id: string;
        name: string;
        color: string | null;
    } | null;
    key: string;
    repairOrderId: number;
    repairOrderNumber: string;

    vehicleId: string | null;
    make: string | null;
    model: string | null;
    year: number | null;
    mileage: number | null;
    vin: string | null;
    plates: string | null;

    customerId: string | null;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    mobilePhone: string | null;
    landlinePhone: string | null;
    inspectionFormStatus: OrderStatus;
    consumerViews: number;
    internalViews: number;
    phoneCallsCount: number;
    photoCount: number;
    videoCount: number;
    lastCommunicationTime: string;
    assignedToUserId: number | null;
    showEstimates: boolean;
    /**
     * @deprecated you should use shopId (Guid)
     */
    repairShopId: number;
    shopId: string;
    businessName: string;
    identificationDocument: IdentificationDocumentDto;
    appointmentNumber?: number;
    discount?: number;
    towerNumber?: string;
    phaseId: number | null;
    paymentMethodType: PaymentMethodType | null;
    deliveryDate: string | null;
    discountType: DiscountType;
    appointmentsNotificationsEnabled: boolean;
    massSendingEnabled: boolean;
    appointmentReasonColor: string | null;
}
