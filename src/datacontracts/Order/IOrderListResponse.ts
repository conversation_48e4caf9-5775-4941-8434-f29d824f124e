import { PaymentMethodType } from 'common/constants';
import { OrderStatus } from '../../common/constants/OrderStatus';
import { PagedGrid } from '../PagedGrid';
export interface IRepairOrderListResponse {
    ordersGrid: PagedGrid<IRepairOrder>;
}
export interface IRepairOrder {
    repairOrderId: number;
    repairOrderKey: string;
    repairOrderNumber: string;
    consumerFirstName: string | null;
    consumerLastName: string | null;
    consumerMobilePhoneNumber: string | null;
    consumerLandlinePhoneNumber: string | null;
    consumerEmail: string | null;
    businessName: string;
    idDocTypeName: string | null;
    idDocNumber: string | null;
    paymentMethod: PaymentMethodType;
    vin: string | null;
    mileage: number;
    make: string | null;
    model: string | null;
    year: number | null;
    inspectionFormStatus: OrderStatus;
    additionalStatus: string;
    repairShopName: string;
    photoCount: number;
    videoCount: number;
    green: number;
    yellow: number;
    red: number;
    assignedToInitials: string | null;
    assignedToDisplayName: string | null;
    inChargeUser: null | {
        id: number;
        key: string;
        color: string | null;
        name: string;
        initials: string;
    };
    uploadTime: Date;
    lastUpdateTime: Date;
    lastCommunicationTime: Date;
    yellowRedTotalCostWithTaxes: number;
    activeAlerts: number;
    resolvedAlerts: number;
    consumerViews: number;
    phoneCallsCount: number;
    plates: string;
    orderType: {
        id: string;
        name: string;
    } | null;
    phase: {
        id: number;
        name: string;
    };
    tower: string;
    appointmentReasonColor: string | null;
    jobsCount: number;
}
