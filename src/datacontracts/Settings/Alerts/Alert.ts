import AlertRecipient from './AlertRecipient';
import AlertTrigger from './AlertTrigger';
import AlertType from './AlertType';

export interface AlertBaseDto {
    enabled: boolean;
    body: string;
    id: number;
    title: string;
    type: AlertType;
}

export interface ToggleAlertRequest {
    id: number;
    enabled: boolean;
}

export interface AlertDto extends AlertBaseDto {
    recipients: AlertRecipient[];
    triggers: AlertTrigger[];
}
