export interface GeneralSettings {
    sharedInspectionsAddOnlyMode: boolean;
    showConsumerInformationToTechnicians: boolean;
    hideAddInspectionItemFeature: boolean;
    automaticallyInCharge: boolean;
    automaticallyAssignedTo: boolean;
    enableSpreadsheetViewByDefault: boolean;
    enableEstimateReview: boolean;
    showAllRosFromDms: boolean;
    enableServiceAdvisorSignature: boolean;
    enableTechnicianSignature: boolean;
    enableElectronicSignature: boolean;
    electronicSignatureHeader: string;
    electronicSignatureText: string;
    electronicSignatureCheckbox: string;
    enableAdhesionContractSignature: boolean;
    adhesionContractSignatureHeader: string;
    adhesionContractSignatureText: string;
    enableNoticePrivacySignature: boolean;
    noticePrivacySignatureHeader: string;
    noticePrivacySignatureText: string;
    enableElectronicDeliverySignature: boolean;
    electronicDeliverySignatureHeader: string;
    electronicDeliverySignatureText: string;
    electronicDeliverySignatureCheckbox: string;
    enableHandwrittenSignature: boolean;
    handwrittenSignatureHeader: string;
    handwrittenSignatureText: string;
    isReceptonSignatureRequired: boolean;
    isDeliverySignatureRequired: boolean;
    isNameRequired: boolean;
    isLastNameRequired: boolean;
    isEmailRequired: boolean;
    isMobileRequired: boolean;
    isSignatureInPrivacyNoticeRequired: boolean;
    isPlatesRequired: boolean;
    isBrandRequired: boolean;
    isModelRequired: boolean;
    isYearRequired: boolean;
    isMileageRequired: boolean;
    isVINRequired: boolean;
    isTowerRequired: boolean;
    isOrderTypeRequired: boolean;
    isDeliveryDayRequired: boolean;
    isDeliveryHourRequired: boolean;
}

export function getDefaultGeneralSettings(): GeneralSettings {
    return {
        sharedInspectionsAddOnlyMode: false,
        showConsumerInformationToTechnicians: false,
        hideAddInspectionItemFeature: false,
        automaticallyInCharge: false,
        automaticallyAssignedTo: false,
        enableSpreadsheetViewByDefault: false,
        enableEstimateReview: false,
        showAllRosFromDms: false,
        enableServiceAdvisorSignature: false,
        enableTechnicianSignature: false,
        enableElectronicSignature: false,
        electronicSignatureHeader: '',
        electronicSignatureText: '',
        electronicSignatureCheckbox: '',
        enableAdhesionContractSignature: false,
        adhesionContractSignatureHeader: '',
        adhesionContractSignatureText: '',
        enableNoticePrivacySignature: false,
        noticePrivacySignatureHeader: '',
        noticePrivacySignatureText: '',
        enableElectronicDeliverySignature: false,
        electronicDeliverySignatureHeader: '',
        electronicDeliverySignatureText: '',
        electronicDeliverySignatureCheckbox: '',
        enableHandwrittenSignature: false,
        handwrittenSignatureHeader: '',
        handwrittenSignatureText: '',
        isReceptonSignatureRequired: false,
        isDeliverySignatureRequired: false,
        isNameRequired: false,
        isLastNameRequired: false,
        isEmailRequired: false,
        isMobileRequired: false,
        isSignatureInPrivacyNoticeRequired: true,
        isPlatesRequired: false,
        isBrandRequired: false,
        isModelRequired: false,
        isYearRequired: false,
        isMileageRequired: false,
        isVINRequired: false,
        isTowerRequired: false,
        isOrderTypeRequired: false,
        isDeliveryDayRequired: false,
        isDeliveryHourRequired: false,
    };
}
