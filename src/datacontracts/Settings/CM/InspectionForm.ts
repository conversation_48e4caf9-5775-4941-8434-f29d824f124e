import { ConsumerFormType } from 'api/settings/OrdersSettings';

export interface InspectionForm {
    accountName: string;
    consumerLogoPath: string | null;
    liveGalleryImagePath: string;
    showDefaultItems: boolean;
    defaultFormType: ConsumerFormType;
    showDetails: boolean;
    approveOnlyEstimated: boolean;
    showServiceAdvisorInfo: boolean;
    showCutRepairOrderNumber: boolean;
    hideMakeLogos: boolean;
    enableHoursOfUse: boolean;
    taxPercentage: number | null;
    costPerHour: number | null;
    showOriginalPhotos: boolean;
    enableFacebookButton: boolean;
    showVehicleMakeLogo: boolean;
}

export function getDefaultInspectionForm(): InspectionForm {
    return {
        accountName: '',
        consumerLogoPath: '',
        liveGalleryImagePath: '',
        showDefaultItems: false,
        defaultFormType: 'BySystem',
        showDetails: false,
        approveOnlyEstimated: false,
        showServiceAdvisorInfo: false,
        showCutRepairOrderNumber: false,
        hideMakeLogos: false,
        enableHoursOfUse: false,
        taxPercentage: null,
        costPerHour: null,
        showOriginalPhotos: false,
        enableFacebookButton: false,
        showVehicleMakeLogo: false,
    };
}
