export interface LegalRepresentativeSignature {
    enableLegalRepresentativeSignature: boolean;
    legalRepresentativeSignaturePath: string | null;
    legalRepresentativeSignatureLabel: string;
}

export function getDefaultLegalRepresentativeSignature(): LegalRepresentativeSignature {
    return {
        enableLegalRepresentativeSignature: false,
        legalRepresentativeSignaturePath: '',
        legalRepresentativeSignatureLabel: '',
    };
}
