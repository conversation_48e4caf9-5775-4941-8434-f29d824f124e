import { InternationalizationDto } from 'datacontracts/Interationalization/InternationalizationDto';
import { CustomizableBannerDto } from './CustomizableBannerDto';
import EnterpriseSettingsDto from './EnterpriseSettingsDto';
import { FeatureFlagsDto } from './FeatureFlagsDto';
import RepairShopSettingsDto from './RepairShopSettingsDto';

export type AppModeEnum = 'RepairShop' | 'Enterprise';

export default interface GlobalSettingsDto {
    appMode: AppModeEnum;
    name: string;
    //organization id (RepairShopId or EnterpriseId)
    id: number;
    uid: string; // guid id
    enableNewDesign: boolean;
    enableCustomReport: boolean;
    isInactive: boolean;
    repairShopSettings: RepairShopSettingsDto | null;
    enterpriseSettings: EnterpriseSettingsDto | null;
    internationalization: InternationalizationDto;
    customizableBanner: CustomizableBannerDto | null;
    enableStandardOperations: boolean;
    standardOperationOrderTypes: string;
    addTechnicianSignatureAtJob: boolean;
    technicianSignatureAtJobOrderTypes: string;
    featureFlags: FeatureFlagsDto;
}
