import { AirportScreenViewType } from 'api/workshopPlanner/Settings/General/GeneralSettings';

export interface GallerySettings {
    enabled: boolean;
    url: string;
}

/**
 * For every field this type has you can add enable at the beginning.
 * Unles the name of the field alread has a verb like "hide" or "show".
 */
export interface FeatureFlags {
    dmsIntegration: boolean;
    orderType: boolean;
    orderEditing: boolean;
    estimateReview: boolean;
    showCutOrderNumber: boolean;
    paymentMethod: boolean;
    spreadsheetViewByDefault: boolean;
    itemJobs: boolean;
    itemJobsFromOpenApi: boolean;
    whatsAppChat: boolean;
    phoneCalls: boolean;
    customSms: boolean;
    sms: boolean;
    partsSupplier: boolean;
    autoInCharge: boolean;
    autoOrderNumber: boolean;
    autoAssignedTo: boolean;
    liveStream: boolean;
    estimateIntegration: boolean;
    accountOverdueMessage: boolean;
    hideAddInspectionItem: boolean;
    sendWhatsappToConsumer: boolean;
    showConsumerInfoToTechnicians: boolean;
    sendSurveysToConsumer: boolean;
    enableEstimateIntegration: boolean;
    invoiceIntegration: boolean;
    syncAppts: boolean;
    enableRemoveDecimals: boolean;
    showAccountOverdueMessage: boolean;
    enableWp: boolean;
    enableAutoProspection: boolean;
    enableAftersalesCrm: boolean;
    isOmnichannelAppointmentsEnabled: boolean;
    isAppointmentSiteEnabled: boolean;
    uTsEqualTo: number;
    maximumNumberOfDigitsToDisplayForTowerNumber: number;
    maximumNumberOfDigitsToDisplayForOrderAndAppointment: number;
    airportScreenView: AirportScreenViewType;
    showTechnicianCapacity: boolean;
    maximumTechnicianCapacity: number;
    allowSchedulingWithMaximumCapacity: boolean;
    packages: boolean;
    isCustomAppointmentReasonEnabled: boolean;
    vehicleReceptionIntegrationEnabled: boolean;
    quotesIntegration: boolean;
    enableStandardOperations: boolean;
    onlinePaymentsEnabled: boolean;
    closeOrderEnabled: boolean;
    enableOnlinePayments: boolean;
    sroValidationIntegration: boolean;
    enableManualPaymentRegistration: boolean;
    syncAppointmentsThirdPartyEnabled: boolean;
    enableWorkshopJobIntegration: boolean;
    updateOrderInfo3rdParty: boolean;
    campaigns: RepairShopCampaignConfiguration;
}

export interface RepairShopCampaignConfiguration {
    integration: boolean;
    enableVehicleCampaign: boolean;
    priorityLevel: 'Urgent' | 'Suggested' | 'Ok';
}

export default interface RepairShopSettingsDto {
    taxPercentage: number | null;
    gallery: GallerySettings;
    features: FeatureFlags;
    accountName: string;
    integrationAccountName: string;
    integrationType: IntegrationType;
    quotesIntegrationType: QuotesIntegrationType;
    consumerLogoPath: string | null;
    showTowerColor: 'ByServiceAdvisor' | 'ByOrderType' | 'ByReasonForAppointment';
    showJobBlockColor: 'NoColor' | 'ByServiceAdvisor' | 'ByOrderType' | 'ByReasonForAppointment';
}

export const IntegrationTypes = {
    BariguiFomulaParque: 'BariguiFomulaParque',
    InchcapeTumbaMuerto: 'InchcapeTumbaMuerto',
    InchcapeCalle50: 'InchcapeCalle50',
    InchcapeUruca: 'InchcapeUruca',
    InchcapeZapote: 'InchcapeZapote',
    JaguarLandRover: 'JaguarLandRover',
    OpenAPI: 'OpenAPI',
    JaguarLandRoverV2: 'JaguarLandRoverV2',
} as const;

export type IntegrationType = (typeof IntegrationTypes)[keyof typeof IntegrationTypes];

export enum QuotesIntegrationType {
    INDIVIDUAL_ITEMS = 'IndividualItems',
    GROUP_ITEMS = 'GroupItems',
}
