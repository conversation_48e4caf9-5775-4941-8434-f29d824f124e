export default interface IOrderDetailsApiResponse {
    orderId: string;
    number: string;
    uts: number;
    duration: number;
    effectiveUts: number;
    postSaleUts: number;
    postSaleRequested: boolean;
    workDate: string;
    status: string;
    gralStatus: string;
    reWashes: number;
    enterToWash: string | null;
    exitFromWash: string | null;
    updated: string;
    created: string;
    onHoldDate: string | null;
    promiseDate: string;
    scheduled: boolean;
    origin: string;
    prepickingPrepared: boolean;
    prepickingDelivered: boolean;
    driveTestDate: string | null;
    qualityControlDate: string | null;
    forWashingDate: string | null;
    prepDeliveryDate: string | null;
    finishedToDeliverDate: string | null;
    deliveredDate: string | null;
    vehicleStr: string;
    isExpress: boolean;
    licensePlate: string;
    fir: boolean;
    id: string;
    towerId: string;
    technicianId: string;
    organizationId: string;
    orderTypeId: string;
    additionalDescription: string;
    onHoldReasonId?: string;
    onHoldReasonKey?: string;
    vehicleModel: string;
    towerNumber: number;
    towerColor: string;
    technicianFirstName: string;
    technicianLastName: string;
    orderTypeName: string;
    dashboardStatus: string;
}
