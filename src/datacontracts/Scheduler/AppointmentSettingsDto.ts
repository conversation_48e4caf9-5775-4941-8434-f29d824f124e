export type AppointmentWorkingDayDto = {
    id?: string | null;
    active: boolean;
    dayNumber: number;
    opening: string;
    closing: string;
};

export type AppointmentSettingsDto = {
    // id: string; // we don't care about id because it's just a random useless GUID
    repairShopKey: string; // but for some reason we have a separate repairShopKey, okay, that's not weird at all
    duration: number;
    activateAutomaticAppointmentNumber: boolean;
    initialAppointmentNumber?: number;
    activateChangeAppointmentStatusAfter: boolean;
    changeAppointmentStatusAfterTime: number;
    valetServiceEnabled: boolean;
};

export type AppointmentSettingWorkingDaysDto = {
    repairShopKey: string;
    duration: number;
    activateAutomaticAppointmentNumber: boolean;
    initialAppointmentNumber?: number;
    lastAppointmentNumber?: number;
    workingDays: AppointmentWorkingDayDto[];
    synchronizeAppointmentsEnabled: boolean;
    omnichannelEnabled: boolean;
    activateChangeAppointmentStatusAfter: boolean;
    changeAppointmentStatusAfterTime: number;
    valetServiceEnabled: boolean;
};
