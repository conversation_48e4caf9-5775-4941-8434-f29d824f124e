import { LegacyAppointmentReasonDto } from 'api/appointments';
import { AppointmentStatusEnum } from './AppointmentStatusEnum';

export interface WorkshopPlannerAppointmentDto {
    id: string;
    number: string;
    userServiceAdvisorId: string;
    userServiceDisplayName: string;
    status: AppointmentStatusEnum;
    startDate: string;
    endDate: string;
    vehicleVin?: string;

    // WARNING none of these fields are actually optional they are all required
    // but I don't have time to deal with them
    // god this is a mess
    // TODO fix this!
    orderNumber?: string;
    customerFirstName?: string;
    customerLastName?: string;
    customerMobile?: string;
    vehicleMake?: string;
    vehicleModel?: string;
    vehicleYear?: string;
    vehiclePlates?: string;
    observations?: string;

    reasons: LegacyAppointmentReasonDto[];
    isPlanned: boolean;
    orderId: string | null;
}

export interface AppointmentReasonBaseDto {
    name: string;
}
