import { AppointmentStatusEnum } from './AppointmentStatusEnum';

/**
 * TOD<PERSON> re-make this DTO. It's breaking SRP by being used in 2 different places - during creation of new appointment and
 *      when returning a list of appointments. "id" field of this DTO can be undefined in some of those cases and that
 *      is confusing.
 * @deprecated this dto should be re-made (see TODO)
 */
export interface LegacyAppointmentDto {
    number: string;
    orderNumber?: string;
    repairShopId: string;
    status: AppointmentStatusEnum;
    customerId?: string;
    customerFirstName?: string;
    customerLastName?: string;
    customerEmail?: string;
    customerMobile?: string;
    userServiceDisplayName: string;
    userServiceAdvisorColor: string;
    userServiceAdvisorId: string;
    duration?: number;
    vehiclePlates?: string;
    vehicleVIN?: string;
    vehicleMake?: string;
    vehicleModel?: string;
    vehicleYear?: string;
    vehicleColor?: string;
    jobType: string;
    /**
     * Id of the appointment.
     * This is undefined for new appointment that we send to the backend and not undefined for existing ones.
     */
    id?: string;
    endDate: string;
    startDate: string;
    isThirdPartyCreated?: boolean;
    isThirdPartyUpdated?: boolean;
    isThirdPartyDeleted?: boolean;
    isFromAppointmentSite?: boolean;
    hasConflict: boolean | null;
    withValetService: boolean;
}
