import { InventoryCustomizableFieldDto } from 'api/OpenAPIItemJobs';

export interface EstimateDto {
    estimateId: number;
    repairId: number;
    name: string;
    isSubItem: boolean;
    itemJobId: number | null;
    itemJobDescription: string | null;
    partsNumber: string | null;
    quantity: number | null;
    availability: number | null;
    partUnitCost: number | null;
    partUnitPrice: number | null;
    hours: number | null;
    hourUnitPrice: number | null;
    discount: number | null;
    total: number;
    jobId: string | null;
    estimateUpdateTime: string | null;
    customizableFields?: InventoryCustomizableFieldDto[];
}
