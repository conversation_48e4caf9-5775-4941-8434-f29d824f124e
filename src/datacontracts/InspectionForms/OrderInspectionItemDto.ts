import { PriorityLevel } from '../../common/constants/PriorityLevel';
import { EstimateDto } from './EstimateDto';

export interface OrderInspectionItemDto {
    repairId: number;
    masterItemId: number;
    name: string;
    campaignId?: string;
    sequenceNumber: number;
    priority: PriorityLevel;
    hasMedia: boolean;
    comments: string;
    isHidden: boolean;
    isDeclined: boolean;
    isApproved: boolean;
    estimates: EstimateDto[];
}
