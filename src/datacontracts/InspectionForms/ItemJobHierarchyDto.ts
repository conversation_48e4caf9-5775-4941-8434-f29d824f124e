interface ItemJobPartDto {
    price: number;
    quantity: number;
    number: string;
    description: string;
}

interface ItemJobLabourDto {
    description: string;
    partNumber: string;
    hours: string;
}

interface ItemJobDto {
    id: string;
    description: string;
    parts: ItemJobPartDto[];
    labours: ItemJobLabourDto[];
}

interface ItemJobVariantDto {
    id: string;
    description: string;
    jobs: ItemJobDto[];
}

interface ItemJobOperationDto {
    id: string;
    description: string;
    jobVariants: ItemJobVariantDto[];
}

interface ItemJobGroupDto {
    id: string;
    description: string;
    operations: ItemJobOperationDto[];
}

export default interface ItemJobHierarchyDto {
    groups: ItemJobGroupDto[];
}
