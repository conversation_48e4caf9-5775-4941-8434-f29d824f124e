export interface ItemJobHierarchyDto {
    groups: ItemJobGroupDto[];
}

export interface ItemJobGroupDto {
    id: string;
    description: string;
    operations: ItemJobOperationDto[];
}

export interface ItemJobOperationDto {
    id: string;
    description: string;
    jobVariants: ItemJobVariantDto[];
}

export interface ItemJobVariantDto {
    id: string;
    description: string;
    jobs: ItemJobDto[];
}

export interface ItemJobDto {
    id: string;
    description: string;
}
