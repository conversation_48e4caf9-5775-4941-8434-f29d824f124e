export type EnterpriseConversationDto = {
    conversationId: number;
    customerPhoneNumber: string;
    customerFirstName: string;
    customerLastName: string;
    lastInboundMessage: string;
    lastSentMessage: string;
    chatBotMode: boolean;
    currentControlFlow: boolean;
    unreadMessages: number;
    lastMessageContent: string;
    shopName: string;

    //guids
    customerId: string | null;
    repairOrderId: string | null;
    appointmentId: string | null;
    vehicleId: string | null;
    massSendingId: string | null;
    shopId: string;
};

//TODO: (AG) CMOS-1173 move to one file with request
//https://bitbucket.org/clearmechanic/clearmechanic.frontend/pull-requests/633#comment-thread-416221635
