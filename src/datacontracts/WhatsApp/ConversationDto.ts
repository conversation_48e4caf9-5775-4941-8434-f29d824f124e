export interface ConversationDto {
    conversationId: number;
    customerPhoneNumber: string;
    customerFirstName: string;
    customerLastName: string;
    lastInboundMessage: string;
    lastSentMessage: string;
    chatBotMode: boolean;
    currentControlFlow: boolean;
    unreadMessages: number;
    lastMessageContent: string;
    //guids
    customerId: string | null;
    repairOrderId: string | null;
    appointmentId: string | null;
    vehicleId: string | null;
    massSendingId: string | null;
    inChargeUserId: string | null;
}
