import { initI18n } from 'common/i18n';
import initSentry from 'common/sentry';
import 'overlayscrollbars/overlayscrollbars.css';
import { Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import 'react-loading-skeleton/dist/skeleton.css';
import { Http } from 'services/Http';
import { Server } from 'services/Server';
import store from 'store';
import { selectAccessToken } from 'store/slices/user';
import ProductLogoLoader from 'views/Components/ProductLogoLoader';
import App from './app';
import './index.css';
import './os-style.css';
import './polyfills';

initSentry();

// initialize translation with default language
initI18n(window.navigator.language);

const getAccessToken = () => selectAccessToken(store.getState()) || undefined;

Server.setAccessTokenAccessor(getAccessToken);
Http.setAccessTokenAccessor(getAccessToken);

const containerElement = document.getElementById('root');
const root = createRoot(containerElement!);

root.render(
    // <StrictMode>
    <Suspense fallback={<ProductLogoLoader />}>
        <App />
    </Suspense>
    // </StrictMode>
);
