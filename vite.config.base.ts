/// <reference types="vitest" />
// import react from '@vitejs/plugin-react';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react-swc';
import { execSync } from 'node:child_process';
import { version } from 'node:os';
import { ConfigEnv, Plugin, PluginOption, defineConfig } from 'vite';
import checker from 'vite-plugin-checker';
import { createHtmlPlugin } from 'vite-plugin-html';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';
import packageJson from './package.json';

interface VersionPluginOptions {
    version: string;
    filename?: string; // defaults to version.txt
}

function versionPlugin(options: VersionPluginOptions): Plugin {
    const filename = options.filename ?? 'version.txt';

    return {
        name: 'vite-plugin-version-txt',
        apply: 'build',

        async generateBundle(_, _bundle) {
            console.log('VERSION', version);

            this.emitFile({
                type: 'asset',
                fileName: filename,
                source: options.version,
            });
        },
    };
}

// https://github.com/vitejs/vite/issues/15012#issuecomment-1825035992
const muteWarningsPlugin = (warningsToIgnore: [string, string][]): Plugin => {
    const mutedMessages = new Set();
    return {
        name: 'mute-warnings',
        enforce: 'pre',
        config: (userConfig) => ({
            build: {
                rollupOptions: {
                    onwarn(warning, defaultHandler) {
                        if (warning.code) {
                            const muted = warningsToIgnore.find(
                                ([code, message]) =>
                                    code === warning.code && warning.message.includes(message)
                            );

                            if (muted) {
                                mutedMessages.add(muted.join());
                                return;
                            }
                        }

                        if (userConfig.build?.rollupOptions?.onwarn) {
                            userConfig.build.rollupOptions.onwarn(warning, defaultHandler);
                        } else {
                            defaultHandler(warning);
                        }
                    },
                },
            },
        }),
        closeBundle() {
            const diff = warningsToIgnore.filter((x) => !mutedMessages.has(x.join()));
            if (diff.length > 0) {
                this.warn('Some of your muted warnings never appeared during the build process:');
                diff.forEach((m) => this.warn(`- ${m.join(': ')}`));
            }
        },
    };
};

const plugins: PluginOption[] = [
    checker({
        typescript: true,
    }),
    react(),
    tsconfigPaths(),
    svgr({
        svgrOptions: {
            ref: true,
        },
    }),
    createHtmlPlugin({
        minify: true,
    }),

    // Rollup, be a dear, and shut up! ...please
    // mutes a warning that occurs when sourcemaps are enabled
    muteWarningsPlugin([['SOURCEMAP_ERROR', "Can't resolve original location of error"]]),

    sentryVitePlugin({
        authToken: process.env.SENTRY_AUTH_TOKEN,
        org: 'clearmechanic',
        project: 'cmos-frontend',
    }),
];

const LOCALHOST = process.env.VITE_DEV_LOCALHOST || 'localhost';
const MONOLITH = `http://${LOCALHOST}:54812`;
const GENERAL_API = `http://${LOCALHOST}:5154`;

function getGitCommitValue(): string {
    try {
        return execSync('git rev-parse HEAD').toString().trim();
    } catch (e) {
        console.error('failed to get current git commit ', e);
        return '<unknown>';
    }
}

function getBuildInfo(env: ConfigEnv) {
    const CODEBUILD_ID = process.env.CODEBUILD_BUILD_ID ?? '';

    const buildId = CODEBUILD_ID || `local${Math.floor(Date.now() / 1000)}`;
    const fullBuildId = `${env.mode}:${packageJson.version}:${buildId}`;

    return {
        ID: fullBuildId,
        TS: Date.now(),
        APP_VERSION: packageJson.version,
        GIT_COMMIT: getGitCommitValue(),
    };
}

const timestamp = Date.now().toString(16);

const changeOrigin = process.env.VITE_DEV_PROXY_CHANGE_ORIGIN
    ? process.env.VITE_DEV_PROXY_CHANGE_ORIGIN === 'true'
    : true;

// https://vitejs.dev/config/
export default defineConfig((env) => {
    const buildInfo = getBuildInfo(env);

    return {
        base: '/dashboard',
        plugins: [
            ...plugins,
            versionPlugin({
                version: JSON.stringify(buildInfo),
            }),
        ],

        test: {
            environment: 'jsdom',
        },

        define: {
            // define a global variable that we can use later
            // in dev it will just add window.__BUILD_INFO__ = ...
            // in PROD it will literally replace __BUILD_INFO__ with this value during compilation
            __BUILD_INFO__: JSON.stringify(buildInfo),
        },

        // this option is needed only for case, when vite use it's own web-server (i.e. dev only)
        server: {
            // we add proxy settings to base to allow vite preview (aka npm run serve) to work properly
            proxy: {
                '/api': {
                    target: MONOLITH,
                    changeOrigin,
                },
                '/general-api': {
                    target: GENERAL_API,
                    changeOrigin,
                },
            },
        },

        build: {
            rollupOptions: {
                output: {
                    manualChunks: {
                        // dependencies that will not change often
                        // splitting all those libs into multiple chunks about the same size
                        // should (in theory) improve loading speed thanks to HTTP 2 multiplexing
                        // in practice? who know we'll see
                        'vendor-react': [
                            'react',
                            'react-dom',
                            'react-router',
                            'react-router-dom',
                            'i18next',
                            'react-i18next',
                            '@emotion/react',
                            '@emotion/styled',
                            'redux',
                            'react-redux',
                            '@reduxjs/toolkit',
                        ],
                        'vendor-mui': ['@mui/styles', '@mui/material', '@mui/x-date-pickers'],
                        'vendor-dt': ['moment', 'luxon', 'date-fns'], // the holy trinity of date-time libraries
                        'vendor-misc': ['framer-motion', '@tanstack/react-query', 'axios'],
                    },

                    // NOTE (MB) IMPORTANT these MUST start with assets, otherwise nginx will not serve them correctly
                    chunkFileNames: `assets/[name]-[hash]-${timestamp}-chunk.js`,
                    assetFileNames: `assets/[name]-[hash]-${timestamp}[extname]`,
                    entryFileNames: `assets/[name]-[hash]-${timestamp}-entrypoint.js`,
                },
            },
        },
    };
});
